take the form

$$
z_j = h\left(\sum_i w_{ji} x_i + w_{j0}\right) \tag{5.113}
$$

while the activations of the output units are given by

$$
y_k = \sum_j w_{kj} z_j + w_{k0}.
$$
 (5.114)

Suppose we perform a linear transformation of the input data of the form

$$
x_i \to \widetilde{x}_i = ax_i + b. \tag{5.115}
$$

Then we can arrange for the mapping performed by the network to be unchanged by making a corresponding linear transformation of the weights and biases from the *Exercise 5.24* inputs to the units in the hidden layer of the form

$$
w_{ji} \to \widetilde{w}_{ji} = \frac{1}{a} w_{ji} \tag{5.116}
$$

$$
w_{j0} \to \tilde{w}_{j0} = w_{j0} - \frac{b}{a} \sum_{i} w_{ji}.
$$
 (5.117)

Similarly, a linear transformation of the output variables of the network of the form

$$
y_k \to \widetilde{y}_k = cy_k + d \tag{5.118}
$$

can be achieved by making a transformation of the second-layer weights and biases using

$$
w_{kj} \to \widetilde{w}_{kj} = c w_{kj} \tag{5.119}
$$

$$
w_{k0} \rightarrow \widetilde{w}_{k0} = cw_{k0} + d. \tag{5.120}
$$

If we train one network using the original data and one network using data for which the input and/or target variables are transformed by one of the above linear transformations, then consistency requires that we should obtain equivalent networks that differ only by the linear transformation of the weights as given. Any regularizer should be consistent with this property, otherwise it arbitrarily favours one solution over another, equivalent one. Clearly, simple weight decay (5.112), that treats all weights and biases on an equal footing, does not satisfy this property.

We therefore look for a regularizer which is invariant under the linear transformations (5.116), (5.117), (5.119) and (5.120). These require that the regularizer should be invariant to re-scaling of the weights and to shifts of the biases. Such a regularizer is given by

$$
\frac{\lambda_1}{2} \sum_{w \in \mathcal{W}_1} w^2 + \frac{\lambda_2}{2} \sum_{w \in \mathcal{W}_2} w^2
$$
 (5.121)

where  $W_1$  denotes the set of weights in the first layer,  $W_2$  denotes the set of weights in the second layer, and biases are excluded from the summations. This regularizer

# **5.5. Regularization in Neural Networks 259**

will remain unchanged under the weight transformations provided the regularization parameters are re-scaled using  $\lambda_1 \rightarrow a^{1/2}\lambda_1$  and  $\lambda_2 \rightarrow c^{-1/2}\lambda_2$ .

The regularizer (5.121) corresponds to a prior of the form

$$
p(\mathbf{w}|\alpha_1, \alpha_2) \propto \exp\left(-\frac{\alpha_1}{2} \sum_{w \in \mathcal{W}_1} w^2 - \frac{\alpha_2}{2} \sum_{w \in \mathcal{W}_2} w^2\right).
$$
 (5.122)

Note that priors of this form are *improper* (they cannot be normalized) because the bias parameters are unconstrained. The use of improper priors can lead to difficulties in selecting regularization coefficients and in model comparison within the Bayesian framework, because the corresponding evidence is zero. It is therefore common to include separate priors for the biases (which then break shift invariance) having their own hyperparameters. We can illustrate the effect of the resulting four hyperparameters by drawing samples from the prior and plotting the corresponding network functions, as shown in Figure 5.11.

More generally, we can consider priors in which the weights are divided into any number of groups  $W_k$  so that

$$
p(\mathbf{w}) \propto \exp\left(-\frac{1}{2} \sum_{k} \alpha_k \|\mathbf{w}\|_{k}^{2}\right)
$$
 (5.123)

where

$$
\|\mathbf{w}\|_{k}^{2} = \sum_{j \in \mathcal{W}_{k}} w_{j}^{2}.
$$
 (5.124)

As a special case of this prior, if we choose the groups to correspond to the sets of weights associated with each of the input units, and we optimize the marginal likelihood with respect to the corresponding parameters  $\alpha_k$ , we obtain *automatic relevance determination* as discussed in Section 7.2.2.

## **5.5.2 Early stopping**

An alternative to regularization as a way of controlling the effective complexity of a network is the procedure of *early stopping*. The training of nonlinear network models corresponds to an iterative reduction of the error function defined with respect to a set of training data. For many of the optimization algorithms used for network training, such as conjugate gradients, the error is a nonincreasing function of the iteration index. However, the error measured with respect to independent data, generally called a validation set, often shows a decrease at first, followed by an increase as the network starts to over-fit. Training can therefore be stopped at the point of smallest error with respect to the validation data set, as indicated in Figure 5.12, in order to obtain a network having good generalization performance.

The behaviour of the network in this case is sometimes explained qualitatively in terms of the effective number of degrees of freedom in the network, in which this number starts out small and then to grows during the training process, corresponding to a steady increase in the effective complexity of the model. Halting training before

Image /page/2/Figure/1 description: This figure contains four plots, each illustrating the effect of hyperparameters on different curves. The top-left plot has the title "αw1 = 1, αb1 = 1, αw2 = 1, αb2 = 1" and shows five curves ranging from approximately -2 to 4 on the y-axis, with the x-axis ranging from -1 to 1. The top-right plot, titled "αw1 = 1, αb1 = 1, αw2 = 10, αb2 = 1", displays five curves with y-axis values ranging from -60 to 40 and an x-axis from -1 to 1. The bottom-left plot, labeled "αw1 = 1000, αb1 = 100, αw2 = 1, αb2 = 1", features five step-like curves with y-axis values between -10 and 5, and an x-axis from -1 to 1. The bottom-right plot, titled "αw1 = 1000, αb1 = 1000, αw2 = 1, αb2 = 1", also shows five step-like curves with y-axis values between -10 and 5, and an x-axis from -1 to 1. The figure is captioned "Figure 5.11 Illustration of the effect of the hyperparameters governing the prior distribution over weights and..."

**Figure 5.11** Illustration of the effect of the hyperparameters governing the prior distribution over weights and biases in a two-layer network having a single input, a single linear output, and 12 hidden units having 'tanh' activation functions. The priors are governed by four hyperparameters  $\alpha_1^b$ ,  $\alpha_1^w$ ,  $\alpha_2^b$ , and  $\alpha_2^w$ , which represent the precisions of the Gaussian distributions of the first-layer biases, first-layer weights, second-layer biases, and second-layer weights, respectively. We see that the parameter  $\alpha_2^w$  governs the vertical scale of functions (note the different vertical axis ranges on the top two diagrams),  $\alpha_1^w$  governs the horizontal scale of variations in the function values, and  $\alpha_1^b$  governs the horizontal range over which variations occur. The parameter  $\alpha_2^b$ , whose effect is not illustrated here, governs the range of vertical offsets of the functions.

a minimum of the training error has been reached then represents a way of limiting the effective network complexity.

In the case of a quadratic error function, we can verify this insight, and show that early stopping should exhibit similar behaviour to regularization using a simple weight-decay term. This can be understood from Figure 5.13, in which the axes in weight space have been rotated to be parallel to the eigenvectors of the Hessian matrix. If, in the absence of weight decay, the weight vector starts at the origin and proceeds during training along a path that follows the local negative gradient vector, then the weight vector will move initially parallel to the  $w_2$  axis through a point corresponding roughly to  $\tilde{w}$  and then move towards the minimum of the error function  $w_{\text{ML}}$ . This follows from the shape of the error surface and the widely differing eigenvalues of the Hessian. Stopping at a point near  $\tilde{w}$  is therefore similar to weight decay. The relationship between early stopping and weight decay can be made quan-*Exercise 5.25* titative, thereby showing that the quantity  $\tau \eta$  (where  $\tau$  is the iteration index, and  $\eta$ is the learning rate parameter) plays the role of the reciprocal of the regularization

*Exercise 5.25*

Image /page/3/Figure/1 description: The image contains two plots. The left plot shows a red line with circular markers, with the y-axis ranging from 0.15 to 0.25 and the x-axis ranging from 0 to 50. The line starts at approximately 0.28, decreases to around 0.18 by x=25, and then levels off. A dashed vertical line is present at x=25. The right plot shows a blue line with circular markers, with the y-axis ranging from 0.35 to 0.45 and the x-axis ranging from 0 to 50. The line starts at approximately 0.45, decreases to around 0.41 by x=10, then continues to decrease to a minimum of approximately 0.405 at x=25, and then levels off. A dashed vertical line is present at x=25.

**Figure 5.12** An illustration of the behaviour of training set error (left) and validation set error (right) during a typical training session, as a function of the iteration step, for the sinusoidal data set. The goal of achieving the best generalization performance suggests that training should be stopped at the point shown by the vertical dashed lines, corresponding to the minimum of the validation set error.

parameter  $\lambda$ . The effective number of parameters in the network therefore grows during the course of training.

## **5.5.3 Invariances**

In many applications of pattern recognition, it is known that predictions should be unchanged, or *invariant*, under one or more transformations of the input variables. For example, in the classification of objects in two-dimensional images, such as handwritten digits, a particular object should be assigned the same classification irrespective of its position within the image (*translation invariance*) or of its size (*scale invariance*). Such transformations produce significant changes in the raw data, expressed in terms of the intensities at each of the pixels in the image, and yet should give rise to the same output from the classification system. Similarly in speech recognition, small levels of nonlinear warping along the time axis, which preserve temporal ordering, should not change the interpretation of the signal.

If sufficiently large numbers of training patterns are available, then an adaptive model such as a neural network can learn the invariance, at least approximately. This involves including within the training set a sufficiently large number of examples of the effects of the various transformations. Thus, for translation invariance in an image, the training set should include examples of objects at many different positions.

This approach may be impractical, however, if the number of training examples is limited, or if there are several invariants (because the number of combinations of transformations grows exponentially with the number of such transformations). We therefore seek alternative approaches for encouraging an adaptive model to exhibit the required invariances. These can broadly be divided into four categories:

1. The training set is augmented using replicas of the training patterns, transformed according to the desired invariances. For instance, in our digit recognition example, we could make multiple copies of each example in which the

**Figure 5.13** A schematic illustration of why early stopping can give similar results to weight decay in the case of a quadratic error function. The ellipse shows a contour of constant error, and  $w_{ML}$ denotes the minimum of the error function. If the weight vector starts at the origin and moves according to the local negative gradient direction, then it will follow the path shown by the curve. By stopping training early, a weight vector  $\widetilde{\bf w}$  is found that is qualitatively similar to that obtained with a simple weight-decay regularizer and training to the minimum of the regularized error, as can be seen by comparing with Figure 3.15.

Image /page/4/Figure/2 description: The image displays a 2D plot with the x-axis labeled "w1" and the y-axis labeled "w2". A blue curve starts from the origin and curves upwards and to the right. A blue dot labeled "w~" is located on this curve. A red ellipse is centered in the upper right quadrant of the plot. A blue arrow originates from the blue dot "w~" and points to a red dot labeled "wML", which is located inside the red ellipse. The arrow is horizontal and points to the right.

digit is shifted to a different position in each image.

- 2. A regularization term is added to the error function that penalizes changes in the model output when the input is transformed. This leads to the technique of *tangent propagation*, discussed in Section 5.5.4.
- 3. Invariance is built into the pre-processing by extracting features that are invariant under the required transformations. Any subsequent regression or classification system that uses such features as inputs will necessarily also respect these invariances.
- 4. The final option is to build the invariance properties into the structure of a neural network (or into the definition of a kernel function in the case of techniques such as the relevance vector machine). One way to achieve this is through the use of local receptive fields and shared weights, as discussed in the context of convolutional neural networks in Section 5.5.6.

Approach 1 is often relatively easy to implement and can be used to encourage complex invariances such as those illustrated in Figure 5.14. For sequential training algorithms, this can be done by transforming each input pattern before it is presented to the model so that, if the patterns are being recycled, a different transformation (drawn from an appropriate distribution) is added each time. For batch methods, a similar effect can be achieved by replicating each data point a number of times and transforming each copy independently. The use of such augmented data can lead to significant improvements in generalization (Simard *et al.*, 2003), although it can also be computationally costly.

Approach 2 leaves the data set unchanged but modifies the error function through the addition of a regularizer. In Section 5.5.5, we shall show that this approach is closely related to approach 2.

Image /page/5/Figure/1 description: The image displays a grid of plots related to a handwritten digit. The leftmost plot is a pixelated representation of the digit '6'. To its right, there are three smaller plots, each showing a progressively clearer version of the digit '6'. Below these three plots are three corresponding plots displaying vector fields. The first vector field plot shows random, short vectors scattered across the grid. The second and third vector field plots show more organized, swirling patterns of longer vectors, with the third plot exhibiting a more concentrated and intense flow in the upper left quadrant.

**Figure 5.14** Illustration of the synthetic warping of a handwritten digit. The original image is shown on the left. On the right, the top row shows three examples of warped digits, with the corresponding displacement fields shown on the bottom row. These displacement fields are generated by sampling random displacements  $\Delta x, \Delta y \in (0, 1)$  at each pixel and then smoothing by convolution with Gaussians of width 0.01, 30 and 60 respectively.

One advantage of approach 3 is that it can correctly extrapolate well beyond the range of transformations included in the training set. However, it can be difficult to find hand-crafted features with the required invariances that do not also discard information that can be useful for discrimination.

## **5.5.4 Tangent propagation**

We can use regularization to encourage models to be invariant to transformations of the input through the technique of *tangent propagation* (Simard *et al.*, 1992). Consider the effect of a transformation on a particular input vector  $\mathbf{x}_n$ . Provided the transformation is continuous (such as translation or rotation, but not mirror reflection for instance), then the transformed pattern will sweep out a manifold  $\mathcal M$  within the D-dimensional input space. This is illustrated in Figure 5.15, for the case of  $D =$ 2 for simplicity. Suppose the transformation is governed by a single parameter  $\xi$ (which might be rotation angle for instance). Then the subspace  $\mathcal M$  swept out by  $\mathbf x_n$ 

**Figure 5.15** Illustration of a two-dimensional input space  $x_2$ showing the effect of a continuous transformation on a particular input vector  $x_n$ . A onedimensional transformation, parameterized by the continuous variable  $\xi$ , applied to  $x_n$  causes it to sweep out a one-dimensional manifold  $M$ . Locally, the effect of the transformation can be approximated by the tangent vector  $\tau_n$ .

Image /page/5/Figure/7 description: The image displays a 2D coordinate system with axes labeled x1 and x2. A blue curve, labeled M, represents a manifold. A point on the curve is marked with a red dot and labeled xn. A purple vector, labeled τn, is tangent to the curve at xn. An arrow labeled ξ points along the curve M in the direction of increasing x1.

will be one-dimensional, and will be parameterized by  $\xi$ . Let the vector that results from acting on  $\mathbf{x}_n$  by this transformation be denoted by  $\mathbf{s}(\mathbf{x}_n, \xi)$ , which is defined so that  $s(\mathbf{x},0) = \mathbf{x}$ . Then the tangent to the curve M is given by the directional derivative  $\tau = \partial s / \partial \xi$ , and the tangent vector at the point  $x_n$  is given by

$$
\boldsymbol{\tau}_n = \left. \frac{\partial \mathbf{s}(\mathbf{x}_n, \xi)}{\partial \xi} \right|_{\xi=0}.
$$
\n(5.125)

Under a transformation of the input vector, the network output vector will, in general, change. The derivative of output k with respect to  $\xi$  is given by

$$
\left. \frac{\partial y_k}{\partial \xi} \right|_{\xi=0} = \sum_{i=1}^D \left. \frac{\partial y_k}{\partial x_i} \frac{\partial x_i}{\partial \xi} \right|_{\xi=0} = \sum_{i=1}^D J_{ki} \tau_i \tag{5.126}
$$

where  $J_{ki}$  is the  $(k, i)$  element of the Jacobian matrix **J**, as discussed in Section 5.3.4. The result (5.126) can be used to modify the standard error function, so as to encourage local invariance in the neighbourhood of the data points, by the addition to the original error function E of a regularization function  $\Omega$  to give a total error function of the form

$$
E = E + \lambda \Omega \tag{5.127}
$$

where  $\lambda$  is a regularization coefficient and

$$
\Omega = \frac{1}{2} \sum_{n} \sum_{k} \left( \frac{\partial y_{nk}}{\partial \xi} \bigg|_{\xi=0} \right)^2 = \frac{1}{2} \sum_{n} \sum_{k} \left( \sum_{i=1}^{D} J_{nki} \tau_{ni} \right)^2
$$
 \cdot (5.128)

The regularization function will be zero when the network mapping function is invariant under the transformation in the neighbourhood of each pattern vector, and the value of the parameter  $\lambda$  determines the balance between fitting the training data and learning the invariance property.

In a practical implementation, the tangent vector  $\tau_n$  can be approximated using finite differences, by subtracting the original vector  $x_n$  from the corresponding vector after transformation using a small value of  $\xi$ , and then dividing by  $\xi$ . This is illustrated in Figure 5.16.

The regularization function depends on the network weights through the Jacobian **J**. A backpropagation formalism for computing the derivatives of the regu-*Exercise 5.26* larizer with respect to the network weights is easily obtained by extension of the techniques introduced in Section 5.3.

> If the transformation is governed by L parameters (e.g.,  $L = 3$  for the case of translations combined with in-plane rotations in a two-dimensional image), then the manifold  $M$  will have dimensionality  $L$ , and the corresponding regularizer is given by the sum of terms of the form (5.128), one for each transformation. If several transformations are considered at the same time, and the network mapping is made invariant to each separately, then it will be (locally) invariant to combinations of the transformations (Simard *et al.*, 1992).

**Figure 5.16** Illustration showing (a) the original image **x** of a handwritten digit, (b) the tangent vector *τ* corresponding to an infinitesimal clockwise rotation, (c) the result of adding a small contribution from the tangent vector to the original image giving  $x + \epsilon \tau$  with  $\epsilon = 15$  degrees, and (d) the true image rotated for comparison.

Image /page/7/Figure/2 description: The image displays four plots arranged in a 2x2 grid. Each plot contains a pixelated representation of the digit '6'. Plot (a) shows a clear, dark blue '6'. Plot (b) shows a '6' with areas of yellow and dark blue, suggesting a difference or error map. Plot (c) shows a '6' that is slightly more spread out and lighter in color than in plot (a). Plot (d) shows a '6' that is similar in clarity to plot (a) but appears slightly thicker and darker in some areas.

A related technique, called *tangent distance*, can be used to build invariance properties into distance-based methods such as nearest-neighbour classifiers (Simard *et al.*, 1993).

## **5.5.5 Training with transformed data**

We have seen that one way to encourage invariance of a model to a set of transformations is to expand the training set using transformed versions of the original input patterns. Here we show that this approach is closely related to the technique of tangent propagation (Bishop, 1995b; Leen, 1995).

As in Section 5.5.4, we shall consider a transformation governed by a single parameter  $\xi$  and described by the function  $\mathbf{s}(\mathbf{x}, \xi)$ , with  $\mathbf{s}(\mathbf{x}, 0) = \mathbf{x}$ . We shall also consider a sum-of-squares error function. The error function for untransformed inputs can be written (in the infinite data set limit) in the form

$$
E = \frac{1}{2} \iint \{y(\mathbf{x}) - t\}^2 p(t|\mathbf{x}) p(\mathbf{x}) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t \tag{5.129}
$$

as discussed in Section 1.5.5. Here we have considered a network having a single output, in order to keep the notation uncluttered. If we now consider an infinite number of copies of each data point, each of which is perturbed by the transformation

in which the parameter  $\xi$  is drawn from a distribution  $p(\xi)$ , then the error function defined over this expanded data set can be written as

$$
\widetilde{E} = \frac{1}{2} \iiint \{y(\mathbf{s}(\mathbf{x}, \xi)) - t\}^2 p(t|\mathbf{x}) p(\mathbf{x}) p(\xi) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t \, \mathrm{d}\xi. \tag{5.130}
$$

We now assume that the distribution  $p(\xi)$  has zero mean with small variance, so that we are only considering small transformations of the original input vectors. We can then expand the transformation function as a Taylor series in powers of  $\xi$  to give

$$
\mathbf{s}(\mathbf{x}, \xi) = \mathbf{s}(\mathbf{x}, 0) + \xi \left. \frac{\partial}{\partial \xi} \mathbf{s}(\mathbf{x}, \xi) \right|_{\xi=0} + \frac{\xi^2}{2} \left. \frac{\partial^2}{\partial \xi^2} \mathbf{s}(\mathbf{x}, \xi) \right|_{\xi=0} + O(\xi^3)
$$
$$
= \mathbf{x} + \xi \boldsymbol{\tau} + \frac{1}{2} \xi^2 \boldsymbol{\tau}' + O(\xi^3)
$$

where  $\tau'$  denotes the second derivative of  $\mathbf{s}(\mathbf{x}, \xi)$  with respect to  $\xi$  evaluated at  $\xi = 0$ . This allows us to expand the model function to give

$$
y(\mathbf{s}(\mathbf{x},\xi)) = y(\mathbf{x}) + \xi \boldsymbol{\tau}^{\mathrm{T}} \nabla y(\mathbf{x}) + \frac{\xi^2}{2} \left[ (\boldsymbol{\tau}')^{\mathrm{T}} \nabla y(\mathbf{x}) + \boldsymbol{\tau}^{\mathrm{T}} \nabla \nabla y(\mathbf{x}) \boldsymbol{\tau} \right] + O(\xi^3).
$$

Substituting into the mean error function (5.130) and expanding, we then have

$$
\widetilde{E} = \frac{1}{2} \iint \{y(\mathbf{x}) - t\}^2 p(t|\mathbf{x}) p(\mathbf{x}) \, d\mathbf{x} \, dt + \mathbb{E}[\xi] \iint \{y(\mathbf{x}) - t\} \boldsymbol{\tau}^{\mathrm{T}} \nabla y(\mathbf{x}) p(t|\mathbf{x}) p(\mathbf{x}) \, d\mathbf{x} \, dt + \mathbb{E}[\xi^2] \iint \left[ \{y(\mathbf{x}) - t\} \frac{1}{2} \left\{ (\boldsymbol{\tau}')^{\mathrm{T}} \nabla y(\mathbf{x}) + \boldsymbol{\tau}^{\mathrm{T}} \nabla \nabla y(\mathbf{x}) \boldsymbol{\tau} \right\} + \left. (\boldsymbol{\tau}^{\mathrm{T}} \nabla y(\mathbf{x}))^2 \right] p(t|\mathbf{x}) p(\mathbf{x}) \, d\mathbf{x} \, dt + O(\xi^3).
$$

Because the distribution of transformations has zero mean we have  $\mathbb{E}[\xi]=0$ . Also, we shall denote  $\mathbb{E}[\xi^2]$  by  $\lambda$ . Omitting terms of  $O(\xi^3)$ , the average error function then becomes

$$
E = E + \lambda \Omega \tag{5.131}
$$

where E is the original sum-of-squares error, and the regularization term  $\Omega$  takes the form

$$
\Omega = \int \left[ \{ y(\mathbf{x}) - \mathbb{E}[t|\mathbf{x}] \} \frac{1}{2} \left\{ (\boldsymbol{\tau}')^{\mathrm{T}} \nabla y(\mathbf{x}) + \boldsymbol{\tau}^{\mathrm{T}} \nabla \nabla y(\mathbf{x}) \boldsymbol{\tau} \right\} + \left( \boldsymbol{\tau}^{\mathrm{T}} \nabla y(\mathbf{x}) \right)^{2} \right] p(\mathbf{x}) d\mathbf{x}
$$
(5.132)

in which we have performed the integration over  $t$ .

# **5.5. Regularization in Neural Networks 267**

We can further simplify this regularization term as follows. In Section 1.5.5 we saw that the function that minimizes the sum-of-squares error is given by the conditional average  $\mathbb{E}[t|\mathbf{x}]$  of the target values t. From (5.131) we see that the regularized error will equal the unregularized sum-of-squares plus terms which are  $O(\xi)$ , and so the network function that minimizes the total error will have the form

$$
y(\mathbf{x}) = \mathbb{E}[t|\mathbf{x}] + O(\xi).
$$
 (5.133)

Thus, to leading order in  $\xi$ , the first term in the regularizer vanishes and we are left with

$$
\Omega = \frac{1}{2} \int (\boldsymbol{\tau}^T \nabla y(\mathbf{x}))^2 p(\mathbf{x}) \, \mathrm{d}\mathbf{x}
$$
 (5.134)

which is equivalent to the tangent propagation regularizer  $(5.128)$ .

If we consider the special case in which the transformation of the inputs simply consists of the addition of random noise, so that  $\mathbf{x} \to \mathbf{x} + \boldsymbol{\xi}$ , then the regularizer *Exercise 5.27* takes the form

$$
\Omega = \frac{1}{2} \int \left\| \nabla y(\mathbf{x}) \right\|^2 p(\mathbf{x}) \, \mathrm{d}\mathbf{x} \tag{5.135}
$$

which is known as *Tikhonov* regularization (Tikhonov and Arsenin, 1977; Bishop, 1995b). Derivatives of this regularizer with respect to the network weights can be found using an extended backpropagation algorithm (Bishop, 1993). We see that, for small noise amplitudes, Tikhonov regularization is related to the addition of random noise to the inputs, which has been shown to improve generalization in appropriate circumstances (Sietsma and Dow, 1991).

## **5.5.6 Convolutional networks**

Another approach to creating models that are invariant to certain transformation of the inputs is to build the invariance properties into the structure of a neural network. This is the basis for the *convolutional neural network* (Le Cun *et al.*, 1989; LeCun *et al.*, 1998), which has been widely applied to image data.

Consider the specific task of recognizing handwritten digits. Each input image comprises a set of pixel intensity values, and the desired output is a posterior probability distribution over the ten digit classes. We know that the identity of the digit is invariant under translations and scaling as well as (small) rotations. Furthermore, the network must also exhibit invariance to more subtle transformations such as elastic deformations of the kind illustrated in Figure 5.14. One simple approach would be to treat the image as the input to a fully connected network, such as the kind shown in Figure 5.1. Given a sufficiently large training set, such a network could in principle yield a good solution to this problem and would learn the appropriate invariances by example.

However, this approach ignores a key property of images, which is that nearby pixels are more strongly correlated than more distant pixels. Many of the modern approaches to computer vision exploit this property by extracting *local* features that depend only on small subregions of the image. Information from such features can then be merged in later stages of processing in order to detect higher-order features

*Exercise 5.27*

Image /page/10/Figure/1 description: This is a diagram illustrating part of a convolutional neural network. It shows three layers: an input image, a convolutional layer, and a sub-sampling layer. Lines connect features from the input image to the convolutional layer, and then from the convolutional layer to the sub-sampling layer, demonstrating the flow of information through these layers.

**Figure 5.17** Diagram illustrating part of a convolutional neural network, showing a layer of convolutional units followed by a layer of subsampling units. Several successive pairs of such layers may be used.

> and ultimately to yield information about the image as whole. Also, local features that are useful in one region of the image are likely to be useful in other regions of the image, for instance if the object of interest is translated.

> These notions are incorporated into convolutional neural networks through three mechanisms: (i) local receptive fields, (ii) weight sharing, and (iii) subsampling. The structure of a convolutional network is illustrated in Figure 5.17. In the convolutional layer the units are organized into planes, each of which is called a *feature map*. Units in a feature map each take inputs only from a small subregion of the image, and all of the units in a feature map are constrained to share the same weight values. For instance, a feature map might consist of 100 units arranged in a  $10 \times 10$  grid, with each unit taking inputs from a  $5 \times 5$  pixel patch of the image. The whole feature map therefore has 25 adjustable weight parameters plus one adjustable bias parameter. Input values from a patch are linearly combined using the weights and the bias, and the result transformed by a sigmoidal nonlinearity using (5.1). If we think of the units as feature detectors, then all of the units in a feature map detect the same pattern but at different locations in the input image. Due to the weight sharing, the evaluation of the activations of these units is equivalent to a convolution of the image pixel intensities with a 'kernel' comprising the weight parameters. If the input image is shifted, the activations of the feature map will be shifted by the same amount but will otherwise be unchanged. This provides the basis for the (approximate) invariance of

the network outputs to translations and distortions of the input image. Because we will typically need to detect multiple features in order to build an effective model, there will generally be multiple feature maps in the convolutional layer, each having its own set of weight and bias parameters.

The outputs of the convolutional units form the inputs to the subsampling layer of the network. For each feature map in the convolutional layer, there is a plane of units in the subsampling layer and each unit takes inputs from a small receptive field in the corresponding feature map of the convolutional layer. These units perform subsampling. For instance, each subsampling unit might take inputs from a  $2 \times 2$ unit region in the corresponding feature map and would compute the average of those inputs, multiplied by an adaptive weight with the addition of an adaptive bias parameter, and then transformed using a sigmoidal nonlinear activation function. The receptive fields are chosen to be contiguous and nonoverlapping so that there are half the number of rows and columns in the subsampling layer compared with the convolutional layer. In this way, the response of a unit in the subsampling layer will be relatively insensitive to small shifts of the image in the corresponding regions of the input space.

In a practical architecture, there may be several pairs of convolutional and subsampling layers. At each stage there is a larger degree of invariance to input transformations compared to the previous layer. There may be several feature maps in a given convolutional layer for each plane of units in the previous subsampling layer, so that the gradual reduction in spatial resolution is then compensated by an increasing number of features. The final layer of the network would typically be a fully connected, fully adaptive layer, with a softmax output nonlinearity in the case of multiclass classification.

The whole network can be trained by error minimization using backpropagation to evaluate the gradient of the error function. This involves a slight modification of the usual backpropagation algorithm to ensure that the shared-weight constraints *Exercise 5.28* are satisfied. Due to the use of local receptive fields, the number of weights in the network is smaller than if the network were fully connected. Furthermore, the number of independent parameters to be learned from the data is much smaller still, due to the substantial numbers of constraints on the weights.

## **5.5.7 Soft weight sharing**

One way to reduce the effective complexity of a network with a large number of weights is to constrain weights within certain groups to be equal. This is the technique of weight sharing that was discussed in Section 5.5.6 as a way of building translation invariance into networks used for image interpretation. It is only applicable, however, to particular problems in which the form of the constraints can be specified in advance. Here we consider a form of *soft weight sharing* (Nowlan and Hinton, 1992) in which the hard constraint of equal weights is replaced by a form of regularization in which groups of weights are encouraged to have similar values. Furthermore, the division of weights into groups, the mean weight value for each group, and the spread of values within the groups are all determined as part of the learning process.

*Exercise 5.28*

Recall that the simple weight decay regularizer, given in (5.112), can be viewed as the negative log of a Gaussian prior distribution over the weights. We can encourage the weight values to form several groups, rather than just one group, by consid-*Section 2.3.9* ering instead a probability distribution that is a *mixture* of Gaussians. The centres and variances of the Gaussian components, as well as the mixing coefficients, will be considered as adjustable parameters to be determined as part of the learning process. Thus, we have a probability density of the form

$$
p(\mathbf{w}) = \prod_{i} p(w_i)
$$
 (5.136)

where

$$
p(w_i) = \sum_{j=1}^{M} \pi_j \mathcal{N}(w_i | \mu_j, \sigma_j^2)
$$
 (5.137)

and  $\pi_j$  are the mixing coefficients. Taking the negative logarithm then leads to a regularization function of the form

$$
\Omega(\mathbf{w}) = -\sum_{i} \ln \left( \sum_{j=1}^{M} \pi_j \mathcal{N}(w_i | \mu_j, \sigma_j^2) \right).
$$
 (5.138)

The total error function is then given by

$$
\widetilde{E}(\mathbf{w}) = E(\mathbf{w}) + \lambda \Omega(\mathbf{w}) \tag{5.139}
$$

where  $\lambda$  is the regularization coefficient. This error is minimized both with respect to the weights  $w_i$  and with respect to the parameters  $\{\pi_j, \mu_j, \sigma_j\}$  of the mixture model. If the weights were constant, then the parameters of the mixture model could be determined by using the EM algorithm discussed in Chapter 9. However, the distribution of weights is itself evolving during the learning process, and so to avoid numerical instability, a joint optimization is performed simultaneously over the weights and the mixture-model parameters. This can be done using a standard optimization algorithm such as conjugate gradients or quasi-Newton methods.

In order to minimize the total error function, it is necessary to be able to evaluate its derivatives with respect to the various adjustable parameters. To do this it is convenient to regard the  $\{\pi_i\}$  as *prior* probabilities and to introduce the corresponding posterior probabilities which, following (2.192), are given by Bayes' theorem in the form

$$
\gamma_j(w) = \frac{\pi_j \mathcal{N}(w|\mu_j, \sigma_j^2)}{\sum_k \pi_k \mathcal{N}(w|\mu_k, \sigma_k^2)}.
$$
\n(5.140)

The derivatives of the total error function with respect to the weights are then given

$$
\frac{\partial \widetilde{E}}{\partial w_i} = \frac{\partial E}{\partial w_i} + \lambda \sum_j \gamma_j(w_i) \frac{(w_i - \mu_j)}{\sigma_j^2}.
$$
\n(5.141)

*Exercise 5.29* by

### **5.5. Regularization in Neural Networks 271**

The effect of the regularization term is therefore to pull each weight towards the centre of the  $j<sup>th</sup>$  Gaussian, with a force proportional to the posterior probability of that Gaussian for the given weight. This is precisely the kind of effect that we are seeking.

Derivatives of the error with respect to the centres of the Gaussians are also *Exercise* 5.30 easily computed to give

$$
\frac{\partial E}{\partial \mu_j} = \lambda \sum_i \gamma_j(w_i) \frac{(\mu_i - w_j)}{\sigma_j^2}
$$
\n(5.142)

which has a simple intuitive interpretation, because it pushes  $\mu_i$  towards an average of the weight values, weighted by the posterior probabilities that the respective weight parameters were generated by component  $j$ . Similarly, the derivatives with *Exercise* 5.31 respect to the variances are given by

$$
\frac{\partial \widetilde{E}}{\partial \sigma_j} = \lambda \sum_i \gamma_j(w_i) \left( \frac{1}{\sigma_j} - \frac{(w_i - \mu_j)^2}{\sigma_j^3} \right)
$$
(5.143)

which drives  $\sigma_i$  towards the weighted average of the squared deviations of the weights around the corresponding centre  $\mu_j$ , where the weighting coefficients are again given by the posterior probability that each weight is generated by component  $j$ . Note that in a practical implementation, new variables  $\eta_j$  defined by

$$
\sigma_j^2 = \exp(\eta_j) \tag{5.144}
$$

are introduced, and the minimization is performed with respect to the  $\eta_i$ . This ensures that the parameters  $\sigma_j$  remain positive. It also has the effect of discouraging pathological solutions in which one or more of the  $\sigma_j$  goes to zero, corresponding to a Gaussian component collapsing onto one of the weight parameter values. Such solutions are discussed in more detail in the context of Gaussian mixture models in Section 9.2.1.

For the derivatives with respect to the mixing coefficients  $\pi_i$ , we need to take account of the constraints

$$
\sum_{j} \pi_{j} = 1, \qquad 0 \leq \pi_{i} \leq 1 \tag{5.145}
$$

which follow from the interpretation of the  $\pi_j$  as prior probabilities. This can be done by expressing the mixing coefficients in terms of a set of auxiliary variables  ${\eta_i}$  using the *softmax* function given by

$$
\pi_j = \frac{\exp(\eta_j)}{\sum_{k=1}^M \exp(\eta_k)}.
$$
\n(5.146)

The derivatives of the regularized error function with respect to the  $\{\eta_j\}$  then take the form

*Exercise 5.31*

*Exercise* 5.32

**Figure 5.18** The left figure shows a two-link robot arm, in which the Cartesian coordinates  $(x_1, x_2)$  of the end effector are determined uniquely by the two joint angles  $\theta_1$ and  $\theta_2$  and the (fixed) lengths  $L_1$  and  $L_2$  of the arms. This is know as the forward kinematics of the arm. In practice, we have to find the joint angles that will give rise to a desired end effector position and, as shown in the right figure, this inverse kinematics has two solutions corresponding to 'elbow up' and 'elbow down'.

Image /page/14/Figure/2 description: The image displays a diagram illustrating a two-link robotic arm. The left side shows the arm in an 'elbow up' configuration, with links labeled L1 and L2, and joint angles labeled \u03b81 and \u03b82. The end effector is at coordinates (x1, x2). The right side of the diagram shows the same robotic arm in an 'elbow down' configuration, with the second link depicted as a dashed red line, indicating a different possible position for the end effector at (x1, x2).

$$
\frac{\partial \widetilde{E}}{\partial \eta_j} = \sum_i \{ \pi_j - \gamma_j(w_i) \}.
$$
\n(5.147)

We see that  $\pi_j$  is therefore driven towards the average posterior probability for component j.

# **5.6. Mixture Density Networks**

The goal of supervised learning is to model a conditional distribution  $p(t|x)$ , which for many simple regression problems is chosen to be Gaussian. However, practical machine learning problems can often have significantly non-Gaussian distributions. These can arise, for example, with *inverse problems* in which the distribution can be multimodal, in which case the Gaussian assumption can lead to very poor predictions.

*Exercise 5.33*

As a simple example of an inverse problem, consider the kinematics of a robot *Exercise 5.33* arm, as illustrated in Figure 5.18. The *forward problem* involves finding the end effector position given the joint angles and has a unique solution. However, in practice we wish to move the end effector of the robot to a specific position, and to do this we must set appropriate joint angles. We therefore need to solve the inverse problem, which has two solutions as seen in Figure 5.18.

> Forward problems often corresponds to causality in a physical system and generally have a unique solution. For instance, a specific pattern of symptoms in the human body may be caused by the presence of a particular disease. In pattern recognition, however, we typically have to solve an inverse problem, such as trying to predict the presence of a disease given a set of symptoms. If the forward problem involves a many-to-one mapping, then the inverse problem will have multiple solutions. For instance, several different diseases may result in the same symptoms.

> In the robotics example, the kinematics is defined by geometrical equations, and the multimodality is readily apparent. However, in many machine learning problems the presence of multimodality, particularly in problems involving spaces of high dimensionality, can be less obvious. For tutorial purposes, however, we shall consider a simple toy problem for which we can easily visualize the multimodality. Data for this problem is generated by sampling a variable x uniformly over the interval  $(0, 1)$ , to give a set of values  $\{x_n\}$ , and the corresponding target values  $t_n$  are obtained

**Figure 5.19** On the left is the data set for a simple 'forward problem' in which the red curve shows the result of fitting a two-layer neural network by minimizing the sum-of-squares error function. The corresponding inverse problem, shown on the right, is obtained by exchanging the roles of  $x$  and  $t$ . Here the same network trained again by minimizing the sum-of-squares error function gives a very poor fit to the data due to the

Image /page/15/Figure/2 description: Figure 5.19 shows two plots side-by-side. The left plot displays a dataset for a simple forward problem, with green circles representing data points and a red curve showing the result of fitting a two-layer neural network by minimizing the sum-of-squares error function. The right plot illustrates the corresponding inverse problem, obtained by exchanging the roles of x and t. This plot also shows green circles for data points and a red curve representing the same network trained again. However, the red curve in the right plot shows a very poor fit to the data due to the multimodality of the dataset. Both plots have x-axis labels ranging from 0 to 1 and y-axis labels ranging from 0 to 1.

by computing the function  $x_n + 0.3 \sin(2\pi x_n)$  and then adding uniform noise over the interval  $(-0.1, 0.1)$ . The inverse problem is then obtained by keeping the same data points but exchanging the roles of  $x$  and  $t$ . Figure 5.19 shows the data sets for the forward and inverse problems, along with the results of fitting two-layer neural networks having 6 hidden units and a single linear output unit by minimizing a sumof-squares error function. Least squares corresponds to maximum likelihood under a Gaussian assumption. We see that this leads to a very poor model for the highly non-Gaussian inverse problem.

We therefore seek a general framework for modelling conditional probability distributions. This can be achieved by using a mixture model for  $p(t|x)$  in which both the mixing coefficients as well as the component densities are flexible functions of the input vector **x**, giving rise to the *mixture density network*. For any given value of **x**, the mixture model provides a general formalism for modelling an arbitrary conditional density function  $p(t|x)$ . Provided we consider a sufficiently flexible network, we then have a framework for approximating arbitrary conditional distributions.

Here we shall develop the model explicitly for Gaussian components, so that

$$
p(\mathbf{t}|\mathbf{x}) = \sum_{k=1}^{K} \pi_k(\mathbf{x}) \mathcal{N}\left(\mathbf{t}|\boldsymbol{\mu}_k(\mathbf{x}), \sigma_k^2(\mathbf{x})\right).
$$
 (5.148)

This is an example of a *heteroscedastic* model since the noise variance on the data is a function of the input vector **x**. Instead of Gaussians, we can use other distributions for the components, such as Bernoulli distributions if the target variables are binary rather than continuous. We have also specialized to the case of isotropic covariances for the components, although the mixture density network can readily be extended to allow for general covariance matrices by representing the covariances using a Cholesky factorization (Williams, 1996). Even with isotropic components, the conditional distribution  $p(\mathbf{t}|\mathbf{x})$  does not assume factorization with respect to the components of **t** (in contrast to the standard sum-of-squares regression model) as a consequence of the mixture distribution.

We now take the various parameters of the mixture model, namely the mixing coefficients  $\pi_k(\mathbf{x})$ , the means  $\mu_k(\mathbf{x})$ , and the variances  $\sigma_k^2(\mathbf{x})$ , to be governed by

Image /page/16/Figure/1 description: This image depicts a neural network architecture on the left, with input nodes labeled x1 to xD and output nodes labeled θ1 to θM. The network is connected by lines and dotted lines, suggesting layers and connections. An arrow labeled θ points from the neural network to a graph on the right. The graph shows a probability distribution p(t|x) on the y-axis plotted against a variable t on the x-axis. The distribution is represented by a red curve, which appears to be a mixture of several Gaussian distributions, with individual Gaussian components shown in blue.

**Figure 5.20** The mixture density network can represent general conditional probability densities  $p(t|x)$ by considering a parametric mixture model for the distribution of **t** whose parameters are determined by the outputs of a neural network that takes **x** as its input vector.

the outputs of a conventional neural network that takes **x** as its input. The structure of this mixture density network is illustrated in Figure 5.20. The mixture density network is closely related to the mixture of experts discussed in Section 14.5.3. The principle difference is that in the mixture density network the same function is used to predict the parameters of all of the component densities as well as the mixing coefficients, and so the nonlinear hidden units are shared amongst the input-dependent functions.

The neural network in Figure 5.20 can, for example, be a two-layer network having sigmoidal ('tanh') hidden units. If there are  $L$  components in the mixture model (5.148), and if **t** has <sup>K</sup> components, then the network will have <sup>L</sup> output unit activations denoted by  $a_k^{\pi}$  that determine the mixing coefficients  $\pi_k(\mathbf{x})$ , K outputs denoted by  $a_k^{\sigma}$  that determine the kernel widths  $\sigma_k(\mathbf{x})$  and  $L \times K$  outputs denoted denoted by  $a_k^{\sigma}$  that determine the kernel widths  $\sigma_k(\mathbf{x})$ , and  $L \times K$  outputs denoted<br>by  $a_{kj}^{\mu}$  that determine the components  $\mu_{kj}(\mathbf{x})$  of the kernel centres  $\mu_k(\mathbf{x})$ . The total<br>number of network outputs is number of network outputs is given by  $(K + 2)L$ , as compared with the usual K outputs for a network, which simply predicts the conditional means of the target variables.

The mixing coefficients must satisfy the constraints

$$
\sum_{k=1}^{K} \pi_k(\mathbf{x}) = 1, \qquad 0 \leq \pi_k(\mathbf{x}) \leq 1 \tag{5.149}
$$

which can be achieved using a set of softmax outputs

$$
\pi_k(\mathbf{x}) = \frac{\exp(a_k^{\pi})}{\sum_{l=1}^K \exp(a_l^{\pi})}.
$$
\n(5.150)

Similarly, the variances must satisfy  $\sigma_k^2(\mathbf{x}) \geq 0$  and so can be represented in terms of the exponentials of the corresponding network activations using of the exponentials of the corresponding network activations using

$$
\sigma_k(\mathbf{x}) = \exp(a_k^{\sigma}).\tag{5.151}
$$

Finally, because the means  $\mu_k(\mathbf{x})$  have real components, they can be represented

## **5.6. Mixture Density Networks 275**

directly by the network output activations

$$
\mu_{kj}(\mathbf{x}) = a_{kj}^{\mu}.\tag{5.152}
$$

The adaptive parameters of the mixture density network comprise the vector **w** of weights and biases in the neural network, that can be set by maximum likelihood, or equivalently by minimizing an error function defined to be the negative logarithm of the likelihood. For independent data, this error function takes the form

$$
E(\mathbf{w}) = -\sum_{n=1}^{N} \ln \left\{ \sum_{k=1}^{k} \pi_k(\mathbf{x}_n, \mathbf{w}) \mathcal{N}\left(\mathbf{t}_n | \boldsymbol{\mu}_k(\mathbf{x}_n, \mathbf{w}), \sigma_k^2(\mathbf{x}_n, \mathbf{w})\right) \right\}
$$
(5.153)

where we have made the dependencies on **w** explicit.

In order to minimize the error function, we need to calculate the derivatives of the error  $E(\mathbf{w})$  with respect to the components of **w**. These can be evaluated by using the standard backpropagation procedure, provided we obtain suitable expressions for the derivatives of the error with respect to the output-unit activations. These represent error signals  $\delta$  for each pattern and for each output unit, and can be backpropagated to the hidden units and the error function derivatives evaluated in the usual way. Because the error function (5.153) is composed of a sum of terms, one for each training data point, we can consider the derivatives for a particular pattern  $n$  and then find the derivatives of  $E$  by summing over all patterns.

Because we are dealing with mixture distributions, it is convenient to view the mixing coefficients  $\pi_k(\mathbf{x})$  as x-dependent prior probabilities and to introduce the corresponding posterior probabilities given by

$$
\gamma_k(\mathbf{t}|\mathbf{x}) = \frac{\pi_k \mathcal{N}_{nk}}{\sum_{l=1}^K \pi_l \mathcal{N}_{nl}}
$$
(5.154)

where  $\mathcal{N}_{nk}$  denotes  $\mathcal{N}(\mathbf{t}_n|\boldsymbol{\mu}_k(\mathbf{x}_n), \sigma_k^2(\mathbf{x}_n))$ .<br>The derivatives with respect to the network

The derivatives with respect to the network output activations governing the mix-*Exercise* 5.34 ing coefficients are given by

$$
\frac{\partial E_n}{\partial a_k^{\pi}} = \pi_k - \gamma_k. \tag{5.155}
$$

Similarly, the derivatives with respect to the output activations controlling the com-*Exercise* 5.35 ponent means are given by

$$
\frac{\partial E_n}{\partial a_{kl}^{\mu}} = \gamma_k \left\{ \frac{\mu_{kl} - t_l}{\sigma_k^2} \right\}.
$$
\n(5.156)

Finally, the derivatives with respect to the output activations controlling the compo-*Exercise* 5.36 nent variances are given by

$$
\frac{\partial E_n}{\partial a_k^{\sigma}} = -\gamma_k \left\{ \frac{\Vert \mathbf{t} - \boldsymbol{\mu}_k \Vert^2}{\sigma_k^3} - \frac{1}{\sigma_k} \right\}.
$$
\n(5.157)

*Exercise 5.34*

**Figure 5.21** (a) Plot of the mixing coefficients  $\pi_k(x)$  as a function of  $x$  for the three kernel functions in a mixture density network trained on the data shown in Figure 5.19. The model has three Gaussian components, and uses a two-layer multilayer perceptron with five 'tanh' sigmoidal units in the hidden layer, and nine outputs (corresponding to the 3 means and 3 variances of the Gaussian components and the 3 mixing coefficients). At both small and large values of  $x$ , where the conditional probability density of the target data is unimodal, only one of the kernels has a high value for its prior probability, while at intermediate values of  $x$ , where the conditional density is trimodal, the three mixing coefficients have comparable values. (b) Plots of the means  $\mu_k(x)$  using the same colour coding as for the mixing coefficients. (c) Plot of the contours of the corresponding conditional probability density of the target data for the same mixture density network. (d) Plot of the approximate conditional mode, shown by the red points, of the conditional density.

Image /page/18/Figure/2 description: The image displays four plots labeled (a), (b), (c), and (d). Plots (a) and (b) are line graphs with x-axis ranging from 0 to 1 and y-axis ranging from 0 to 1. Each plot shows three colored lines: a blue line, a green line, and a red line. In plot (a), the blue line starts at 1 and decreases to 0, the green line starts at 0, peaks around 0.5, and decreases to 0, and the red line starts at 0 and increases to 1. In plot (b), the blue line starts at 0 and increases to 1, the green line starts at 1 and decreases to 0, and the red line starts at 0 and increases to 1. Plots (c) and (d) are also graphs with x-axis ranging from 0 to 1 and y-axis ranging from 0 to 1. Plot (c) is a contour plot showing a winding S-shaped distribution of colored contours, ranging from blue to yellow to red, indicating density. Plot (d) shows a scatter plot of green circular data points along a red line that follows an S-shaped curve. The red line appears to be a fitted curve to the scattered data points.

We illustrate the use of a mixture density network by returning to the toy example of an inverse problem shown in Figure 5.19. Plots of the mixing coefficients  $\pi_k(x)$ , the means  $\mu_k(x)$ , and the conditional density contours corresponding to  $p(t|x)$ , are shown in Figure 5.21. The outputs of the neural network, and hence the parameters in the mixture model, are necessarily continuous single-valued functions of the input variables. However, we see from Figure 5.21(c) that the model is able to produce a conditional density that is unimodal for some values of  $x$  and trimodal for other values by modulating the amplitudes of the mixing components  $\pi_k(\mathbf{x})$ .

Once a mixture density network has been trained, it can predict the conditional density function of the target data for any given value of the input vector. This conditional density represents a complete description of the generator of the data, so far as the problem of predicting the value of the output vector is concerned. From this density function we can calculate more specific quantities that may be of interest in different applications. One of the simplest of these is the mean, corresponding to the conditional average of the target data, and is given by

$$
\mathbb{E}[\mathbf{t}|\mathbf{x}] = \int \mathbf{t}p(\mathbf{t}|\mathbf{x}) \, \mathrm{d}\mathbf{t} = \sum_{k=1}^{K} \pi_k(\mathbf{x}) \mu_k(\mathbf{x}) \tag{5.158}
$$

where we have used (5.148). Because a standard network trained by least squares is approximating the conditional mean, we see that a mixture density network can reproduce the conventional least-squares result as a special case. Of course, as we have already noted, for a multimodal distribution the conditional mean is of limited value.

We can similarly evaluate the variance of the density function about the condi-*Exercise* 5.37 tional average, to give

$$
s^{2}(\mathbf{x}) = \mathbb{E}\left[\|\mathbf{t} - \mathbb{E}[\mathbf{t}|\mathbf{x}]\|^{2} | \mathbf{x}\right]
$$
(5.159)

$$
= \sum_{k=1}^{K} \pi_k(\mathbf{x}) \left\{ \sigma_k^2(\mathbf{x}) + \left\| \boldsymbol{\mu}_k(\mathbf{x}) - \sum_{l=1}^{K} \pi_l(\mathbf{x}) \boldsymbol{\mu}_l(\mathbf{x}) \right\|^2 \right\}
$$
  $(5.160)$ 

where we have used  $(5.148)$  and  $(5.158)$ . This is more general than the corresponding least-squares result because the variance is a function of **x**.

We have seen that for multimodal distributions, the conditional mean can give a poor representation of the data. For instance, in controlling the simple robot arm shown in Figure 5.18, we need to pick one of the two possible joint angle settings in order to achieve the desired end-effector location, whereas the average of the two solutions is not itself a solution. In such cases, the conditional mode may be of more value. Because the conditional mode for the mixture density network does not have a simple analytical solution, this would require numerical iteration. A simple alternative is to take the mean of the most probable component (i.e., the one with the largest mixing coefficient) at each value of **x**. This is shown for the toy data set in Figure 5.21(d).

# **5.7. Bayesian Neural Networks**

So far, our discussion of neural networks has focussed on the use of maximum likelihood to determine the network parameters (weights and biases). Regularized maximum likelihood can be interpreted as a MAP (maximum posterior) approach in which the regularizer can be viewed as the logarithm of a prior parameter distribution. However, in a Bayesian treatment we need to marginalize over the distribution of parameters in order to make predictions.

In Section 3.3, we developed a Bayesian solution for a simple linear regression model under the assumption of Gaussian noise. We saw that the posterior distribution, which is Gaussian, could be evaluated exactly and that the predictive distribution could also be found in closed form. In the case of a multilayered network, the highly nonlinear dependence of the network function on the parameter values means that an exact Bayesian treatment can no longer be found. In fact, the log of the posterior distribution will be nonconvex, corresponding to the multiple local minima in the error function.

The technique of variational inference, to be discussed in Chapter 10, has been applied to Bayesian neural networks using a factorized Gaussian approximation

to the posterior distribution (Hinton and van Camp, 1993) and also using a fullcovariance Gaussian (Barber and Bishop, 1998a; Barber and Bishop, 1998b). The most complete treatment, however, has been based on the Laplace approximation (MacKay, 1992c; MacKay, 1992b) and forms the basis for the discussion given here. We will approximate the posterior distribution by a Gaussian, centred at a mode of the true posterior. Furthermore, we shall assume that the covariance of this Gaussian is small so that the network function is approximately linear with respect to the parameters over the region of parameter space for which the posterior probability is significantly nonzero. With these two approximations, we will obtain models that are analogous to the linear regression and classification models discussed in earlier chapters and so we can exploit the results obtained there. We can then make use of the evidence framework to provide point estimates for the hyperparameters and to compare alternative models (for example, networks having different numbers of hidden units). To start with, we shall discuss the regression case and then later consider the modifications needed for solving classification tasks.

## **5.7.1 Posterior parameter distribution**

Consider the problem of predicting a single continuous target variable  $t$  from a vector **x** of inputs (the extension to multiple targets is straightforward). We shall suppose that the conditional distribution  $p(t|\mathbf{x})$  is Gaussian, with an **x**-dependent mean given by the output of a neural network model  $y(x, w)$ , and with precision (inverse variance)  $\beta$ 

$$
p(t|\mathbf{x}, \mathbf{w}, \beta) = \mathcal{N}(t|y(\mathbf{x}, \mathbf{w}), \beta^{-1}).
$$
\n(5.161)

Similarly, we shall choose a prior distribution over the weights **w** that is Gaussian of the form

$$
p(\mathbf{w}|\alpha) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \alpha^{-1}\mathbf{I}).
$$
 (5.162)

For an i.i.d. data set of N observations  $\mathbf{x}_1, \dots, \mathbf{x}_N$ , with a corresponding set of target values  $\mathcal{D} = \{t_1, \dots, t_N\}$  the likelihood function is given by values  $\mathcal{D} = \{t_1, \ldots, t_N\}$ , the likelihood function is given by

$$
p(\mathcal{D}|\mathbf{w}, \beta) = \prod_{n=1}^{N} \mathcal{N}(t_n | y(\mathbf{x}_n, \mathbf{w}), \beta^{-1})
$$
\n(5.163)

and so the resulting posterior distribution is then

$$
p(\mathbf{w}|\mathcal{D}, \alpha, \beta) \propto p(\mathbf{w}|\alpha)p(\mathcal{D}|\mathbf{w}, \beta).
$$
 (5.164)

which, as a consequence of the nonlinear dependence of  $y(\mathbf{x}, \mathbf{w})$  on **w**, will be non-Gaussian.

We can find a Gaussian approximation to the posterior distribution by using the Laplace approximation. To do this, we must first find a (local) maximum of the posterior, and this must be done using iterative numerical optimization. As usual, it is convenient to maximize the logarithm of the posterior, which can be written in the

form

$$
\ln p(\mathbf{w}|\mathcal{D}) = -\frac{\alpha}{2}\mathbf{w}^{\mathrm{T}}\mathbf{w} - \frac{\beta}{2}\sum_{n=1}^{N} \left\{y(\mathbf{x}_n, \mathbf{w}) - t_n\right\}^2 + \text{const}
$$
 (5.165)

which corresponds to a regularized sum-of-squares error function. Assuming for the moment that  $\alpha$  and  $\beta$  are fixed, we can find a maximum of the posterior, which we denote  $w_{MAP}$ , by standard nonlinear optimization algorithms such as conjugate gradients, using error backpropagation to evaluate the required derivatives.

Having found a mode  $w_{MAP}$ , we can then build a local Gaussian approximation by evaluating the matrix of second derivatives of the negative log posterior distribution. From (5.165), this is given by

$$
\mathbf{A} = -\nabla\nabla \ln p(\mathbf{w}|\mathcal{D}, \alpha, \beta) = \alpha \mathbf{I} + \beta \mathbf{H}
$$
 (5.166)

where **H** is the Hessian matrix comprising the second derivatives of the sum-ofsquares error function with respect to the components of **w**. Algorithms for computing and approximating the Hessian were discussed in Section 5.4. The corresponding Gaussian approximation to the posterior is then given from (4.134) by

$$
q(\mathbf{w}|\mathcal{D}) = \mathcal{N}(\mathbf{w}|\mathbf{w}_{\text{MAP}}, \mathbf{A}^{-1}).
$$
\n(5.167)

Similarly, the predictive distribution is obtained by marginalizing with respect to this posterior distribution

$$
p(t|\mathbf{x}, \mathcal{D}) = \int p(t|\mathbf{x}, \mathbf{w}) q(\mathbf{w}|\mathcal{D}) \, \mathrm{d}\mathbf{w}.
$$
 (5.168)

However, even with the Gaussian approximation to the posterior, this integration is still analytically intractable due to the nonlinearity of the network function  $y(\mathbf{x}, \mathbf{w})$ as a function of **w**. To make progress, we now assume that the posterior distribution has small variance compared with the characteristic scales of **w** over which  $y(\mathbf{x}, \mathbf{w})$ is varying. This allows us to make a Taylor series expansion of the network function around  $w<sub>MAP</sub>$  and retain only the linear terms

$$
y(\mathbf{x}, \mathbf{w}) \simeq y(\mathbf{x}, \mathbf{w}_{\text{MAP}}) + \mathbf{g}^{\text{T}}(\mathbf{w} - \mathbf{w}_{\text{MAP}})
$$
(5.169)

where we have defined

$$
\mathbf{g} = \nabla_{\mathbf{w}} y(\mathbf{x}, \mathbf{w})|_{\mathbf{w} = \mathbf{w}_{\text{MAP}}}.
$$
 (5.170)

With this approximation, we now have a linear-Gaussian model with a Gaussian distribution for  $p(\mathbf{w})$  and a Gaussian for  $p(t|\mathbf{w})$  whose mean is a linear function of **w** of the form

$$
p(t|\mathbf{x}, \mathbf{w}, \beta) \simeq \mathcal{N}\left(t|y(\mathbf{x}, \mathbf{w}_{\text{MAP}}) + \mathbf{g}^{\text{T}}(\mathbf{w} - \mathbf{w}_{\text{MAP}}), \beta^{-1}\right).
$$
 (5.171)

*Exercise 5.38*

*Exercise 5.38* We can therefore make use of the general result (2.115) for the marginal  $p(t)$  to give

$$
p(t|\mathbf{x}, \mathcal{D}, \alpha, \beta) = \mathcal{N}\left(t|y(\mathbf{x}, \mathbf{w}_{MAP}), \sigma^2(\mathbf{x})\right)
$$
(5.172)

where the input-dependent variance is given by

$$
\sigma^2(\mathbf{x}) = \beta^{-1} + \mathbf{g}^{\mathrm{T}} \mathbf{A}^{-1} \mathbf{g}.
$$
 (5.173)

We see that the predictive distribution  $p(t|\mathbf{x}, \mathcal{D})$  is a Gaussian whose mean is given by the network function  $y(x, w_{MAP})$  with the parameter set to their MAP value. The variance has two terms, the first of which arises from the intrinsic noise on the target variable, whereas the second is an **x**-dependent term that expresses the uncertainty in the interpolant due to the uncertainty in the model parameters **w**. This should be compared with the corresponding predictive distribution for the linear regression model, given by (3.58) and (3.59).

### **5.7.2 Hyperparameter optimization**

So far, we have assumed that the hyperparameters  $\alpha$  and  $\beta$  are fixed and known. We can make use of the evidence framework, discussed in Section 3.5, together with the Gaussian approximation to the posterior obtained using the Laplace approximation, to obtain a practical procedure for choosing the values of such hyperparameters.

The marginal likelihood, or evidence, for the hyperparameters is obtained by integrating over the network weights

$$
p(\mathcal{D}|\alpha,\beta) = \int p(\mathcal{D}|\mathbf{w},\beta)p(\mathbf{w}|\alpha) \,d\mathbf{w}.
$$
 (5.174)

*Exercise 5.39* This is easily evaluated by making use of the Laplace approximation result (4.135). Taking logarithms then gives

$$
\ln p(\mathcal{D}|\alpha,\beta) \simeq -E(\mathbf{w}_{\text{MAP}}) - \frac{1}{2}\ln|\mathbf{A}| + \frac{W}{2}\ln\alpha + \frac{N}{2}\ln\beta - \frac{N}{2}\ln(2\pi) \tag{5.175}
$$

where  $W$  is the total number of parameters in  $w$ , and the regularized error function is defined by

$$
E(\mathbf{w}_{\text{MAP}}) = \frac{\beta}{2} \sum_{n=1}^{N} \{y(\mathbf{x}_n, \mathbf{w}_{\text{MAP}}) - t_n\}^2 + \frac{\alpha}{2} \mathbf{w}_{\text{MAP}}^{\text{T}} \mathbf{w}_{\text{MAP}}.
$$
 (5.176)

We see that this takes the same form as the corresponding result (3.86) for the linear regression model.

In the evidence framework, we make point estimates for  $\alpha$  and  $\beta$  by maximizing ln  $p(\mathcal{D}|\alpha, \beta)$ . Consider first the maximization with respect to  $\alpha$ , which can be done by analogy with the linear regression case discussed in Section 3.5.2. We first define the eigenvalue equation

$$
\beta \mathbf{H} \mathbf{u}_i = \lambda_i \mathbf{u}_i \tag{5.177}
$$

where **H** is the Hessian matrix comprising the second derivatives of the sum-ofsquares error function, evaluated at  $w = w_{MAP}$ . By analogy with (3.92), we obtain

$$
\alpha = \frac{\gamma}{\mathbf{w}_{\text{MAP}}^{\text{T}} \mathbf{w}_{\text{MAP}}}
$$
 (5.178)

*Section 3.5.3* where  $\gamma$  represents the effective number of parameters and is defined by

$$
\gamma = \sum_{i=1}^{W} \frac{\lambda_i}{\alpha + \lambda_i}.
$$
\n(5.179)

Note that this result was exact for the linear regression case. For the nonlinear neural network, however, it ignores the fact that changes in  $\alpha$  will cause changes in the Hessian **H**, which in turn will change the eigenvalues. We have therefore implicitly ignored terms involving the derivatives of  $\lambda_i$  with respect to  $\alpha$ .

Similarly, from (3.95) we see that maximizing the evidence with respect to  $\beta$ gives the re-estimation formula

$$
\frac{1}{\beta} = \frac{1}{N - \gamma} \sum_{n=1}^{N} \{y(\mathbf{x}_n, \mathbf{w}_{MAP}) - t_n\}^2.
$$
 (5.180)

As with the linear model, we need to alternate between re-estimation of the hyperparameters  $\alpha$  and  $\beta$  and updating of the posterior distribution. The situation with a neural network model is more complex, however, due to the multimodality of the posterior distribution. As a consequence, the solution for  $w_{MAP}$  found by maximizing the log posterior will depend on the initialization of **w**. Solutions that differ only *Section 5.1.1* as a consequence of the interchange and sign reversal symmetries in the hidden units are identical so far as predictions are concerned, and it is irrelevant which of the equivalent solutions is found. However, there may be inequivalent solutions as well, and these will generally yield different values for the optimized hyperparameters.

> In order to compare different models, for example neural networks having different numbers of hidden units, we need to evaluate the model evidence  $p(\mathcal{D})$ . This can be approximated by taking (5.175) and substituting the values of  $\alpha$  and  $\beta$  obtained from the iterative optimization of these hyperparameters. A more careful evaluation is obtained by marginalizing over  $\alpha$  and  $\beta$ , again by making a Gaussian approximation (MacKay, 1992c; Bishop, 1995a). In either case, it is necessary to evaluate the determinant <sup>|</sup>**A**<sup>|</sup> of the Hessian matrix. This can be problematic in practice because the determinant, unlike the trace, is sensitive to the small eigenvalues that are often difficult to determine accurately.

> The Laplace approximation is based on a local quadratic expansion around a mode of the posterior distribution over weights. We have seen in Section 5.1.1 that any given mode in a two-layer network is a member of a set of  $M!2^M$  equivalent modes that differ by interchange and sign-change symmetries, where  $M$  is the number of hidden units. When comparing networks having different numbers of hidden units, this can be taken into account by multiplying the evidence by a factor of  $M!2^M$ .

## **5.7.3 Bayesian neural networks for classification**

So far, we have used the Laplace approximation to develop a Bayesian treatment of neural network regression models. We now discuss the modifications to

this framework that arise when it is applied to classification. Here we shall consider a network having a single logistic sigmoid output corresponding to a two-class classification problem. The extension to networks with multiclass softmax outputs *Exercise 5.40* is straightforward. We shall build extensively on the analogous results for linear classification models discussed in Section 4.5, and so we encourage the reader to familiarize themselves with that material before studying this section.

The log likelihood function for this model is given by

$$
\ln p(\mathcal{D}|\mathbf{w}) = \sum_{n=1}^{N} \{t_n \ln y_n + (1 - t_n) \ln(1 - y_n)\}
$$
\n(5.181)

where  $t_n \in \{0, 1\}$  are the target values, and  $y_n \equiv y(\mathbf{x}_n, \mathbf{w})$ . Note that there is no hyperparameter  $\beta$ , because the data points are assumed to be correctly labelled. As before, the prior is taken to be an isotropic Gaussian of the form (5.162).

The first stage in applying the Laplace framework to this model is to initialize the hyperparameter  $\alpha$ , and then to determine the parameter vector **w** by maximizing the log posterior distribution. This is equivalent to minimizing the regularized error function

$$
E(\mathbf{w}) = -\ln p(\mathcal{D}|\mathbf{w}) + \frac{\alpha}{2}\mathbf{w}^{\mathrm{T}}\mathbf{w}
$$
 (5.182)

and can be achieved using error backpropagation combined with standard optimization algorithms, as discussed in Section 5.3.

Having found a solution  $w_{MAP}$  for the weight vector, the next step is to evaluate the Hessian matrix **H** comprising the second derivatives of the negative log likelihood function. This can be done, for instance, using the exact method of Section 5.4.5, or using the outer product approximation given by (5.85). The second derivatives of the negative log posterior can again be written in the form (5.166), and the Gaussian approximation to the posterior is then given by (5.167).

To optimize the hyperparameter  $\alpha$ , we again maximize the marginal likelihood, *Exercise 5.41* which is easily shown to take the form

$$
\ln p(\mathcal{D}|\alpha) \simeq -E(\mathbf{w}_{\text{MAP}}) - \frac{1}{2}\ln|\mathbf{A}| + \frac{W}{2}\ln \alpha + \text{const}
$$
 (5.183)

where the regularized error function is defined by

$$
E(\mathbf{w}_{\text{MAP}}) = -\sum_{n=1}^{N} \{t_n \ln y_n + (1 - t_n) \ln(1 - y_n)\} + \frac{\alpha}{2} \mathbf{w}_{\text{MAP}}^{\text{T}} \mathbf{w}_{\text{MAP}}
$$
 (5.184)

in which  $y_n \equiv y(\mathbf{x}_n, \mathbf{w}_{\text{MAP}})$ . Maximizing this evidence function with respect to  $\alpha$ again leads to the re-estimation equation given by (5.178).

The use of the evidence procedure to determine  $\alpha$  is illustrated in Figure 5.22 for the synthetic two-dimensional data discussed in Appendix A.

Finally, we need the predictive distribution, which is defined by (5.168). Again, this integration is intractable due to the nonlinearity of the network function. The

*Exercise 5.41*

**Figure 5.22** Illustration of the evidence framework applied to a synthetic two-class data set. The green curve shows the optimal decision boundary, the black curve shows the result of fitting a two-layer network with 8 hidden units by maximum likelihood, and the red curve shows the result of including a regularizer in which  $\alpha$  is optimized using the evidence procedure, starting from the initial value  $\alpha = 0$ . Note that the evidence procedure greatly reduces the over-fitting of the network.

Image /page/25/Figure/2 description: A scatter plot shows two classes of data points, one represented by blue circles and the other by red crosses. The plot has a grid with x-axis values ranging from -2 to 2 and y-axis values ranging from -2 to 3. Three decision boundaries are plotted: a black curve, a red curve, and a green curve, separating the data points into different regions.

simplest approximation is to assume that the posterior distribution is very narrow and hence make the approximation

$$
p(t|\mathbf{x}, \mathcal{D}) \simeq p(t|\mathbf{x}, \mathbf{w}_{MAP}).
$$
 (5.185)

We can improve on this, however, by taking account of the variance of the posterior distribution. In this case, a linear approximation for the network outputs, as was used in the case of regression, would be inappropriate due to the logistic sigmoid outputunit activation function that constrains the output to lie in the range  $(0, 1)$ . Instead, we make a linear approximation for the output unit activation in the form

$$
a(\mathbf{x}, \mathbf{w}) \simeq a_{\text{MAP}}(\mathbf{x}) + \mathbf{b}^{\text{T}}(\mathbf{w} - \mathbf{w}_{\text{MAP}})
$$
 (5.186)

where  $a_{\text{MAP}}(\mathbf{x}) = a(\mathbf{x}, \mathbf{w}_{\text{MAP}})$ , and the vector  $\mathbf{b} \equiv \nabla a(\mathbf{x}, \mathbf{w}_{\text{MAP}})$  can be found by backpropagation.

Because we now have a Gaussian approximation for the posterior distribution over **w**, and a model for <sup>a</sup> that is a linear function of **w**, we can now appeal to the results of Section 4.5.2. The distribution of output unit activation values, induced by the distribution over network weights, is given by

$$
p(a|\mathbf{x}, \mathcal{D}) = \int \delta\left(a - a_{\text{MAP}}(\mathbf{x}) - \mathbf{b}^{\text{T}}(\mathbf{x})(\mathbf{w} - \mathbf{w}_{\text{MAP}})\right) q(\mathbf{w}|\mathcal{D}) \, \mathrm{d}\mathbf{w} \qquad (5.187)
$$

where  $q(\mathbf{w}|\mathcal{D})$  is the Gaussian approximation to the posterior distribution given by (5.167). From Section 4.5.2, we see that this distribution is Gaussian with mean  $a_{\text{MAP}} \equiv a(\mathbf{x}, \mathbf{w}_{\text{MAP}})$ , and variance

$$
\sigma_a^2(\mathbf{x}) = \mathbf{b}^{\mathrm{T}}(\mathbf{x}) \mathbf{A}^{-1} \mathbf{b}(\mathbf{x}). \tag{5.188}
$$

Finally, to obtain the predictive distribution, we must marginalize over  $\alpha$  using

$$
p(t = 1|\mathbf{x}, \mathcal{D}) = \int \sigma(a) p(a|\mathbf{x}, \mathcal{D}) \, da.
$$
 (5.189)

Image /page/26/Figure/1 description: Two plots are shown side-by-side. Both plots display a scatter plot of data points, with blue circles and red crosses, overlaid with contour lines. The x-axis ranges from -2 to 2, and the y-axis ranges from -2 to 3. The blue circles are concentrated in the lower-left quadrant, while the red crosses are more spread out, particularly in the upper-right quadrant. The contour lines, in shades of brown, orange, green, and blue, suggest decision boundaries or probability distributions. The left plot shows a more complex contour pattern compared to the right plot, which has smoother, more rounded contours. Both plots appear to be illustrating a classification or regression problem with two distinct classes represented by the blue circles and red crosses.

**Figure 5.23** An illustration of the Laplace approximation for a Bayesian neural network having 8 hidden units with 'tanh' activation functions and a single logistic-sigmoid output unit. The weight parameters were found using scaled conjugate gradients, and the hyperparameter  $\alpha$  was optimized using the evidence framework. On the left is the result of using the simple approximation  $(5.185)$  based on a point estimate  $w<sub>MAP</sub>$  of the parameters, in which the green curve shows the  $y = 0.5$  decision boundary, and the other contours correspond to output probabilities of  $y = 0.1, 0.3, 0.7,$  and 0.9. On the right is the corresponding result obtained using (5.190). Note that the effect of marginalization is to spread out the contours and to make the predictions less confident, so that at each input point x, the posterior probabilities are shifted towards 0.5, while the  $y = 0.5$  contour itself is unaffected.

The convolution of a Gaussian with a logistic sigmoid is intractable. We therefore apply the approximation (4.153) to (5.189) giving

$$
p(t = 1|\mathbf{x}, \mathcal{D}) = \sigma\left(\kappa(\sigma_a^2)\mathbf{b}^{\mathrm{T}}\mathbf{w}_{\mathrm{MAP}}\right)
$$
 (5.190)

where  $\kappa(\cdot)$  is defined by (4.154). Recall that both  $\sigma_a^2$  and b are functions of **x**.<br>Figure 5.23 shows an example of this framework applied to the synthetic of

Figure 5.23 shows an example of this framework applied to the synthetic classification data set described in Appendix A.

# **Exercises**

**5.1**  $(\star \star)$  Consider a two-layer network function of the form (5.7) in which the hiddenunit nonlinear activation functions  $g(\cdot)$  are given by logistic sigmoid functions of the form

$$
\sigma(a) = \{1 + \exp(-a)\}^{-1}.
$$
\n(5.191)

Show that there exists an equivalent network, which computes exactly the same function, but with hidden unit activation functions given by  $tanh(a)$  where the tanh function is defined by (5.59). Hint: first find the relation between  $\sigma(a)$  and  $\tanh(a)$ , and then show that the parameters of the two networks differ by linear transformations.

**5.2**  $(\star)$  **www** Show that maximizing the likelihood function under the conditional distribution (5.16) for a multioutput neural network is equivalent to minimizing the sum-of-squares error function  $(5.11)$ .

**5.3**  $(\star \star)$  Consider a regression problem involving multiple target variables in which it is assumed that the distribution of the targets, conditioned on the input vector **x**, is a Gaussian of the form

$$
p(\mathbf{t}|\mathbf{x}, \mathbf{w}) = \mathcal{N}(\mathbf{t}|\mathbf{y}(\mathbf{x}, \mathbf{w}), \Sigma)
$$
 (5.192)

where  $\mathbf{v}(\mathbf{x}, \mathbf{w})$  is the output of a neural network with input vector **x** and weight vector **w**, and  $\Sigma$  is the covariance of the assumed Gaussian noise on the targets. Given a set of independent observations of **x** and **t**, write down the error function that must be minimized in order to find the maximum likelihood solution for **w**, if we assume that  $\Sigma$  is fixed and known. Now assume that  $\Sigma$  is also to be determined from the data, and write down an expression for the maximum likelihood solution for **Σ**. Note that the optimizations of **w** and **Σ** are now coupled, in contrast to the case of independent target variables discussed in Section 5.2.

- **5.4**  $(\star \star)$  Consider a binary classification problem in which the target values are  $t \in \mathbb{R}$  $\{0, 1\}$ , with a network output  $y(\mathbf{x}, \mathbf{w})$  that represents  $p(t = 1|\mathbf{x})$ , and suppose that there is a probability  $\epsilon$  that the class label on a training data point has been incorrectly set. Assuming independent and identically distributed data, write down the error function corresponding to the negative log likelihood. Verify that the error function (5.21) is obtained when  $\epsilon = 0$ . Note that this error function makes the model robust to incorrectly labelled data, in contrast to the usual error function.
- **5.5** ( $\star$ ) **www** Show that maximizing likelihood for a multiclass neural network model in which the network outputs have the interpretation  $y_k(\mathbf{x}, \mathbf{w}) = p(t_k = 1|\mathbf{x})$  is equivalent to the minimization of the cross-entropy error function (5.24).
- **5.6** ( $\star$ ) **www** Show the derivative of the error function (5.21) with respect to the activation  $a_k$  for an output unit having a logistic sigmoid activation function satisfies (5.18).
- **5.7** ( $\star$ ) Show the derivative of the error function (5.24) with respect to the activation  $a_k$ for output units having a softmax activation function satisfies (5.18).
- **5.8** ( $\star$ ) We saw in (4.88) that the derivative of the logistic sigmoid activation function can be expressed in terms of the function value itself. Derive the corresponding result for the 'tanh' activation function defined by (5.59).
- **5.9** ( $\star$ ) **www** The error function (5.21) for binary classification problems was derived for a network having a logistic-sigmoid output activation function, so that  $0 \leq y(\mathbf{x}, \mathbf{w}) \leq 1$ , and data having target values  $t \in \{0, 1\}$ . Derive the corresponding error function if we consider a network having an output  $-1 \leq y(\mathbf{x}, \mathbf{w}) \leq 1$ and target values  $t = 1$  for class  $C_1$  and  $t = -1$  for class  $C_2$ . What would be the appropriate choice of output unit activation function?
- **5.10** ( $\star$ ) **www** Consider a Hessian matrix **H** with eigenvector equation (5.33). By setting the vector **v** in (5.39) equal to each of the eigenvectors  $\mathbf{u}_i$  in turn, show that **H** is positive definite if, and only if, all of its eigenvalues are positive.

- **5.11**  $(\star \star)$  **www** Consider a quadratic error function defined by (5.32), in which the Hessian matrix **H** has an eigenvalue equation given by (5.33). Show that the contours of constant error are ellipses whose axes are aligned with the eigenvectors  $\mathbf{u}_i$ , with lengths that are inversely proportional to the square root of the corresponding eigenvalues  $\lambda_i$ .
- **5.12**  $(\star \star)$  **www** By considering the local Taylor expansion (5.32) of an error function about a stationary point  $w^*$ , show that the necessary and sufficient condition for the stationary point to be a local minimum of the error function is that the Hessian matrix **H**, defined by (5.30) with  $\hat{\mathbf{w}} = \mathbf{w}^*$ , be positive definite.
- **5.13**  $(\star)$  Show that as a consequence of the symmetry of the Hessian matrix **H**, the number of independent elements in the quadratic error function (5.28) is given by  $W(W + 3)/2$ .
- **5.14** ( $\star$ ) By making a Taylor expansion, verify that the terms that are  $O(\epsilon)$  cancel on the right-hand side of (5.69).
- **5.15**  $(\star \star)$  In Section 5.3.4, we derived a procedure for evaluating the Jacobian matrix of a neural network using a backpropagation procedure. Derive an alternative formalism for finding the Jacobian based on *forward propagation* equations.
- **5.16**  $(\star)$  The outer product approximation to the Hessian matrix for a neural network using a sum-of-squares error function is given by (5.84). Extend this result to the case of multiple outputs.
- **5.17** ( $\star$ ) Consider a squared loss function of the form

$$
E = \frac{1}{2} \iint \left\{ y(\mathbf{x}, \mathbf{w}) - t \right\}^2 p(\mathbf{x}, t) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t \tag{5.193}
$$

where  $y(x, w)$  is a parametric function such as a neural network. The result (1.89) shows that the function  $y(x, w)$  that minimizes this error is given by the conditional expectation of  $t$  given  $x$ . Use this result to show that the second derivative of  $E$  with respect to two elements  $w_r$  and  $w_s$  of the vector **w**, is given by

$$
\frac{\partial^2 E}{\partial w_r \partial w_s} = \int \frac{\partial y}{\partial w_r} \frac{\partial y}{\partial w_s} p(\mathbf{x}) \, \mathrm{d}\mathbf{x}.\tag{5.194}
$$

Note that, for a finite sample from  $p(x)$ , we obtain (5.84).

- **5.18** ( $\star$ ) Consider a two-layer network of the form shown in Figure 5.1 with the addition of extra parameters corresponding to skip-layer connections that go directly from the inputs to the outputs. By extending the discussion of Section 5.3.2, write down the equations for the derivatives of the error function with respect to these additional parameters.
- **5.19** ( $\star$ ) **WWW** Derive the expression (5.85) for the outer product approximation to the Hessian matrix for a network having a single output with a logistic sigmoid output-unit activation function and a cross-entropy error function, corresponding to the result (5.84) for the sum-of-squares error function.

- **5.20**  $(\star)$  Derive an expression for the outer product approximation to the Hessian matrix for a network having  $K$  outputs with a softmax output-unit activation function and a cross-entropy error function, corresponding to the result (5.84) for the sum-ofsquares error function.
- **5.21**  $(\star \star \star)$  Extend the expression (5.86) for the outer product approximation of the Hessian matrix to the case of  $K > 1$  output units. Hence, derive a recursive expression analogous to  $(5.87)$  for incrementing the number N of patterns and a similar expression for incrementing the number  $K$  of outputs. Use these results, together with the identity (5.88), to find sequential update expressions analogous to (5.89) for finding the inverse of the Hessian by incrementally including both extra patterns and extra outputs.
- **5.22**  $(\star \star)$  Derive the results (5.93), (5.94), and (5.95) for the elements of the Hessian matrix of a two-layer feed-forward network by application of the chain rule of calculus.
- **5.23**  $(\star \star)$  Extend the results of Section 5.4.5 for the exact Hessian of a two-layer network to include skip-layer connections that go directly from inputs to outputs.
- **5.24** ( $\star$ ) Verify that the network function defined by (5.113) and (5.114) is invariant under the transformation (5.115) applied to the inputs, provided the weights and biases are simultaneously transformed using (5.116) and (5.117). Similarly, show that the network outputs can be transformed according (5.118) by applying the transformation (5.119) and (5.120) to the second-layer weights and biases.
- **5.25**  $(\star \star \star)$  **www** Consider a quadratic error function of the form

$$
E = E_0 + \frac{1}{2} (\mathbf{w} - \mathbf{w}^*)^{\mathrm{T}} \mathbf{H} (\mathbf{w} - \mathbf{w}^*)
$$
 (5.195)

where  $w^*$  represents the minimum, and the Hessian matrix  $H$  is positive definite and constant. Suppose the initial weight vector  $w^{(0)}$  is chosen to be at the origin and is updated using simple gradient descent

$$
\mathbf{w}^{(\tau)} = \mathbf{w}^{(\tau - 1)} - \rho \nabla E \tag{5.196}
$$

where  $\tau$  denotes the step number, and  $\rho$  is the learning rate (which is assumed to be small). Show that, after  $\tau$  steps, the components of the weight vector parallel to the eigenvectors of **H** can be written

$$
w_j^{(\tau)} = \{1 - (1 - \rho \eta_j)^\tau\} w_j^\star \tag{5.197}
$$

where  $w_j = \mathbf{w}^T \mathbf{u}_j$ , and  $\mathbf{u}_j$  and  $\eta_j$  are the eigenvectors and eigenvalues, respectively, of **H** so that

$$
\mathbf{H}\mathbf{u}_j = \eta_j \mathbf{u}_j. \tag{5.198}
$$

Show that as  $\tau \to \infty$ , this gives  $\mathbf{w}^{(\tau)} \to \mathbf{w}^*$  as expected, provided  $|1 - \rho \eta_i| < 1$ . Now suppose that training is halted after a finite number  $\tau$  of steps. Show that the

components of the weight vector parallel to the eigenvectors of the Hessian satisfy

$$
w_j^{(\tau)} \simeq w_j^{\star} \quad \text{when} \quad \eta_j \gg (\rho \tau)^{-1} \tag{5.199}
$$

$$
|w_j^{(\tau)}| \ll |w_j^{\star}| \quad \text{when} \quad \eta_j \ll (\rho \tau)^{-1}.\tag{5.200}
$$

Compare this result with the discussion in Section 3.5.3 of regularization with simple weight decay, and hence show that  $(\rho \tau)^{-1}$  is analogous to the regularization parameter  $\lambda$ . The above results also show that the effective number of parameters in the network, as defined by (3.91), grows as the training progresses.

**5.26**  $(\star \star)$  Consider a multilayer perceptron with arbitrary feed-forward topology, which is to be trained by minimizing the *tangent propagation* error function (5.127) in which the regularizing function is given by  $(5.128)$ . Show that the regularization term  $\Omega$  can be written as a sum over patterns of terms of the form

$$
\Omega_n = \frac{1}{2} \sum_k \left( \mathcal{G} y_k \right)^2 \tag{5.201}
$$

where  $\mathcal G$  is a differential operator defined by

$$
\mathcal{G} \equiv \sum_{i} \tau_{i} \frac{\partial}{\partial x_{i}}.
$$
\n(5.202)

By acting on the forward propagation equations

$$
z_j = h(a_j), \qquad a_j = \sum_i w_{ji} z_i \tag{5.203}
$$

with the operator  $G$ , show that  $\Omega_n$  can be evaluated by forward propagation using the following equations:

$$
\alpha_j = h'(a_j)\beta_j, \qquad \beta_j = \sum_i w_{ji}\alpha_i. \qquad (5.204)
$$

where we have defined the new variables

$$
\alpha_j \equiv \mathcal{G}z_j, \qquad \beta_j \equiv \mathcal{G}a_j. \tag{5.205}
$$

Now show that the derivatives of  $\Omega_n$  with respect to a weight  $w_{rs}$  in the network can be written in the form

$$
\frac{\partial \Omega_n}{\partial w_{rs}} = \sum_k \alpha_k \left\{ \phi_{kr} z_s + \delta_{kr} \alpha_s \right\} \tag{5.206}
$$

where we have defined

$$
\delta_{kr} \equiv \frac{\partial y_k}{\partial a_r}, \qquad \phi_{kr} \equiv \mathcal{G} \delta_{kr}.
$$
 (5.207)

Write down the backpropagation equations for  $\delta_{kr}$ , and hence derive a set of backpropagation equations for the evaluation of the  $\phi_{kr}$ .

- **5.27**  $(\star \star)$  **www** Consider the framework for training with transformed data in the special case in which the transformation consists simply of the addition of random noise  $\mathbf{x} \to \mathbf{x} + \xi$  where  $\xi$  has a Gaussian distribution with zero mean and unit covariance. By following an argument analogous to that of Section 5.5.5, show that the resulting regularizer reduces to the Tikhonov form (5.135).
- **5.28**  $(\star)$  **www** Consider a neural network, such as the convolutional network discussed in Section 5.5.6, in which multiple weights are constrained to have the same value. Discuss how the standard backpropagation algorithm must be modified in order to ensure that such constraints are satisfied when evaluating the derivatives of an error function with respect to the adjustable parameters in the network.
- **5.29**  $(\star)$  **www** Verify the result (5.141).
- **5.30**  $(\star)$  Verify the result  $(5.142)$ .
- **5.31**  $\left(\star\right)$  Verify the result (5.143).
- **5.32** ( $\star\star$ ) Show that the derivatives of the mixing coefficients { $\pi_k$ }, defined by (5.146), with respect to the auxiliary parameters  $\{\eta_i\}$  are given by

$$
\frac{\partial \pi_k}{\partial \eta_j} = \delta_{jk}\pi_j - \pi_j \pi_k.
$$
\n(5.208)

Hence, by making use of the constraint  $\sum_{k} \pi_k = 1$ , derive the result (5.147).

- **5.33** ( $\star$ ) Write down a pair of equations that express the Cartesian coordinates  $(x_1, x_2)$ for the robot arm shown in Figure 5.18 in terms of the joint angles  $\theta_1$  and  $\theta_2$  and the lengths  $L_1$  and  $L_2$  of the links. Assume the origin of the coordinate system is given by the attachment point of the lower arm. These equations define the 'forward kinematics' of the robot arm.
- **5.34** ( $\star$ ) **www** Derive the result (5.155) for the derivative of the error function with respect to the network output activations controlling the mixing coefficients in the mixture density network.
- **5.35** ( $\star$ ) Derive the result (5.156) for the derivative of the error function with respect to the network output activations controlling the component means in the mixture density network.
- **5.36** ( $\star$ ) Derive the result (5.157) for the derivative of the error function with respect to the network output activations controlling the component variances in the mixture density network.
- **5.37** ( $\star$ ) Verify the results (5.158) and (5.160) for the conditional mean and variance of the mixture density network model.
- **5.38** ( $\star$ ) Using the general result (2.115), derive the predictive distribution (5.172) for the Laplace approximation to the Bayesian neural network model.

- **5.39** ( $\star$ ) **WWW** Make use of the Laplace approximation result (4.135) to show that the evidence function for the hyperparameters  $\alpha$  and  $\beta$  in the Bayesian neural network model can be approximated by (5.175).
- **5.40**  $(\star)$  **www** Outline the modifications needed to the framework for Bayesian neural networks, discussed in Section 5.7.3, to handle multiclass problems using networks having softmax output-unit activation functions.
- **5.41**  $(\star \star)$  By following analogous steps to those given in Section 5.7.1 for regression networks, derive the result (5.183) for the marginal likelihood in the case of a network having a cross-entropy error function and logistic-sigmoid output-unit activation function.