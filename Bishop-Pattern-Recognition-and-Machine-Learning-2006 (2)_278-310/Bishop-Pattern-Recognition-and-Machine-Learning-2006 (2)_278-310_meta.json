{"table_of_contents": [{"title": "258 5. NEURAL NETWORKS", "heading_level": null, "page_id": 0, "polygon": [[29.5927734375, 40.5], [201.75, 40.5], [201.75, 51.5841064453125], [29.5927734375, 51.5841064453125]]}, {"title": "5.5. Regularization in Neural Networks 259", "heading_level": null, "page_id": 1, "polygon": [[245.25, 40.5], [473.25, 40.5], [473.25, 51.4215087890625], [245.25, 51.4215087890625]]}, {"title": "5.5.2 Early stopping", "heading_level": null, "page_id": 1, "polygon": [[138.0, 432.0], [255.9375, 432.0], [255.9375, 443.2412109375], [138.0, 443.2412109375]]}, {"title": "260 5. NEURAL NETWORKS", "heading_level": null, "page_id": 2, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.2589111328125], [29.25, 51.2589111328125]]}, {"title": "5.5.3 Invariances", "heading_level": null, "page_id": 3, "polygon": [[137.56640625, 315.0], [239.25, 315.0], [239.25, 326.8212890625], [137.56640625, 326.8212890625]]}, {"title": "262 5. NEURAL NETWORKS", "heading_level": null, "page_id": 4, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 52.0718994140625], [30.0, 52.0718994140625]]}, {"title": "5.5.4 Tangent propagation", "heading_level": null, "page_id": 5, "polygon": [[138.75, 366.0], [290.25, 366.0], [290.25, 377.5517578125], [138.75, 377.5517578125]]}, {"title": "264 5. NEURAL NETWORKS", "heading_level": null, "page_id": 6, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "", "heading_level": null, "page_id": 6, "polygon": [[31.177001953125, 511.857421875], [91.546875, 511.857421875], [91.546875, 521.61328125], [31.177001953125, 521.61328125]]}, {"title": "5.5.5 Training with transformed data", "heading_level": null, "page_id": 7, "polygon": [[138.0, 421.5], [344.25, 421.5], [344.25, 433.4853515625], [138.0, 433.4853515625]]}, {"title": "266 5. NEURAL NETWORKS", "heading_level": null, "page_id": 8, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.462158203125], [29.25, 51.462158203125]]}, {"title": "5.5. Regularization in Neural Networks 267", "heading_level": null, "page_id": 9, "polygon": [[245.109375, 40.5], [473.25, 40.5], [473.25, 51.3402099609375], [245.109375, 51.3402099609375]]}, {"title": "5.5.6 Convolutional networks", "heading_level": null, "page_id": 9, "polygon": [[138.75, 371.25], [305.25, 371.25], [305.25, 382.4296875], [138.75, 382.4296875]]}, {"title": "5.5.7 Soft weight sharing", "heading_level": null, "page_id": 11, "polygon": [[138.55078125, 463.5], [284.25, 463.5], [284.25, 475.1103515625], [138.55078125, 475.1103515625]]}, {"title": "270 5. NEURAL NETWORKS", "heading_level": null, "page_id": 12, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.949951171875], [29.25, 51.949951171875]]}, {"title": "5.5. Regularization in Neural Networks 271", "heading_level": null, "page_id": 13, "polygon": [[245.232421875, 41.25], [472.5, 41.25], [472.5, 51.462158203125], [245.232421875, 51.462158203125]]}, {"title": "272 5. NEURAL NETWORKS", "heading_level": null, "page_id": 14, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.54345703125], [29.25, 51.54345703125]]}, {"title": "5.6. Mixture Density Networks", "heading_level": null, "page_id": 14, "polygon": [[87.9169921875, 276.75], [279.0, 276.75], [279.0, 289.91162109375], [87.9169921875, 289.91162109375]]}, {"title": "5.6. Mixture Density Networks 275", "heading_level": null, "page_id": 17, "polygon": [[283.5, 41.25], [473.25, 41.25], [473.25, 51.380859375], [283.5, 51.380859375]]}, {"title": "276 5. NEURAL NETWORKS", "heading_level": null, "page_id": 18, "polygon": [[29.25, 40.5], [201.0, 40.5], [201.0, 51.54345703125], [29.25, 51.54345703125]]}, {"title": "5.7. Bayesian Neural Networks", "heading_level": null, "page_id": 19, "polygon": [[89.25, 401.25], [284.25, 401.25], [284.25, 413.9736328125], [89.25, 413.9736328125]]}, {"title": "5.7.1 Posterior parameter distribution", "heading_level": null, "page_id": 20, "polygon": [[137.25, 260.25], [351.0, 260.25], [351.0, 271.5380859375], [137.25, 271.5380859375]]}, {"title": "5.7.2 Hyperparameter optimization", "heading_level": null, "page_id": 22, "polygon": [[137.07421875, 207.0], [334.6875, 207.0], [334.6875, 217.880859375], [137.07421875, 217.880859375]]}, {"title": "5.7.3 Bayesian neural networks for classification", "heading_level": null, "page_id": 23, "polygon": [[138.75, 568.5], [411.75, 568.5], [411.75, 580.798828125], [138.75, 580.798828125]]}, {"title": "282 5. NEURAL NETWORKS", "heading_level": null, "page_id": 24, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.7467041015625], [30.0, 51.7467041015625]]}, {"title": "Exercises", "heading_level": null, "page_id": 26, "polygon": [[30.0, 459.0], [91.5, 459.0], [91.5, 472.18359375], [30.0, 472.18359375]]}, {"title": "286 5. NEURAL NETWORKS", "heading_level": null, "page_id": 28, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.787353515625], [30.0, 51.787353515625]]}, {"title": "288 5. NEURAL NETWORKS", "heading_level": null, "page_id": 30, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.624755859375], [30.0, 51.624755859375]]}, {"title": "290 5. NEURAL NETWORKS", "heading_level": null, "page_id": 32, "polygon": [[29.25, 39.75], [203.25, 39.75], [203.25, 51.9093017578125], [29.25, 51.9093017578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 54], ["Text", 9], ["Equation", 9], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 60], ["Text", 7], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 52], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 39], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 50], ["ListItem", 4], ["Text", 4], ["SectionHeader", 1], ["Figure", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 645, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1245, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 90], ["TextInlineMath", 4], ["Equation", 4], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4572, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 32], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 608], ["Line", 88], ["Equation", 6], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 51], ["Text", 7], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 26], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 599, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 45], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 65], ["Text", 6], ["Equation", 6], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 60], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 53], ["Text", 6], ["SectionHeader", 2], ["TextInlineMath", 1], ["Figure", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1252, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 56], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 699, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 43], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 660, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 69], ["Text", 7], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 68], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 68], ["Text", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1019, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 40], ["Text", 5], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 420], ["Line", 48], ["Text", 9], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 57], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 49], ["Text", 6], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 53], ["Text", 9], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 52], ["Equation", 5], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 626, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 48], ["Text", 4], ["Equation", 2], ["TextInlineMath", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 728, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["Line", 40], ["ListItem", 8], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 50], ["ListItem", 9], ["Equation", 2], ["ListGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 45], ["ListItem", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 56], ["Text", 9], ["Equation", 9], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 38], ["ListItem", 12], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 11], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_278-310"}