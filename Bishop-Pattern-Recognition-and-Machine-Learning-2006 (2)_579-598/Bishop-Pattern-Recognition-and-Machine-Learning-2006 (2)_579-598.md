Image /page/0/Picture/0 description: The image is a black and white illustration of rippling water. Overlaid on the water are the words "12 Continuous Latent Variables" in large, bold, black font. The number 12 is at the top, followed by "Continuous" below it, and then "Latent Variables" below that.

In Chapter 9, we discussed probabilistic models having discrete latent variables, such as the mixture of Gaussians. We now explore models in which some, or all, of the latent variables are continuous. An important motivation for such models is that many data sets have the property that the data points all lie close to a manifold of much lower dimensionality than that of the original data space. To see why this might arise, consider an artificial data set constructed by taking one of the off-line digits, represented by a  $64 \times 64$  pixel grey-level image, and embedding it in a larger image of size  $100 \times 100$  by padding with pixels having the value zero (corresponding to white pixels) in which the location and orientation of the digit is varied at random, as illustrated in Figure 12.1. Each of the resulting images is represented by a point in the  $100 \times 100 = 10,000$ -dimensional data space. However, across a data set of such images, there are only three *degrees of freedom* of variability, corresponding to the vertical and horizontal translations and the rotations. The data points will therefore live on a subspace of the data space whose *intrinsic dimensionality* is three. Note

*Appendix A*

Image /page/1/Picture/1 description: The image displays five black and white pixelated images of the number 3. Each image is contained within a white square with a thin black border. The numbers are slightly varied in their pixelation and slight distortions, suggesting they might be examples from a dataset or generated by an algorithm.

Figure 12.1 A synthetic data set obtained by taking one of the off-line digit images and creating multiple copies in each of which the digit has undergone a random displacement and rotation within some larger image field. The resulting images each have  $100 \times 100 = 10,000$ pixels.

> that the manifold will be nonlinear because. for instance. if we translate the digit past a particular pixel, that pixel value will go from zero (white) 10 one (black) and back to zero again. which is clearly a nonlinear function of the digit position. In this example, the translation and rotation parameters are latent variables because we observe only the image vectors and are not told which values of the translation or rotation variables were used to create them.

> For real digit image data, there will be a further degree of freedom arising from scaling. Moreover there will be multiple additional degrees of freedom associated with more complex deformations due to the variability in an individual's writing as well as the differences in writing styles between individuals. Nevertheless, the number of such degrees of freedom will be small compared to the dimensionality of the data set.

*Appendix A* Another example is provided by the oil flow data set, in which (for a given geometrical configuration of the gas, water, and oil phases) there are only two degrees of freedom of variability corresponding to the fraction of oil in the pipe and the fraction of water (the fraction of gas then being determined). Although the data space comprises 12 measurements, a data set of points will lie close to a two-dimensional manifold embedded within this space. In this case, the manifold comprises scveral distinct segments corresponding to different flow regimes. each such segment being a (noisy) continuous two-dimensional manifold. If our goal is data compression. or density modelling, then there can be benefits in exploiting this manifold structure.

> In practice, the data points will not be confined precisely to a smooth lowdimensional manifold, and we can interpret the departures of data points from the manifold as 'noise'. This leads naturally to a generative view of such models in which we first select a point within the manifold according to some latent variable distribution and then generate an observed data point by adding noise, drawn from some conditional distribution of the data variables given the latent variables.

Thc simplest continuous latent variable model assumes Gaussian distributions for both the latent and observed variables and makes use of a linear-Gaussian de-*Section* 8.1.4 pendence of the observed variables on the state of the latent variables. This leads to a probabilislic fonnulation of the well-known technique of principal component analysis (PCA), as well as to a related model called factor analysis.

*Section* 12.1 In this chapter w will begin with a standard, nonprobabilistic treatment of PCA, and then we show how PCA arises naturally as the maximum likelihood solution to

*Appendix A*

*Section 8.1.4*

Section 12.1

# 12.1. Principal Component Analysis

Figure 12.2 Principal component analysis seeks a space of lower dimensionality, known as the princi $x_2$ pal subspace and denoted by the magenta line, such that the orthogonal projection of the data points (red dots) onto this subspace maximizes the variance of the projected points (green dots). An alternative definition of PCA is based on minimizing the sum-of-squares of the projection errors, indicated by the blue lines.

Image /page/2/Figure/2 description: A scatter plot shows data points in a two-dimensional space with axes labeled x1 and x2. A line, representing a principal component, is drawn through the data, indicated by the vector u1. Several red data points, labeled xn, are shown. From each xn, a blue line extends to a green point on the principal component line, labeled x~n. This illustrates the projection of the data points onto the principal component.

Section 12.2 a particular form of linear-Gaussian latent variable model. This probabilistic reformulation brings many advantages, such as the use of EM for parameter estimation, principled extensions to mixtures of PCA models, and Bayesian formulations that allow the number of principal components to be determined automatically from the data. Finally, we discuss briefly several generalizations of the latent variable concept that go beyond the linear-Gaussian assumption including non-Gaussian latent variables, which leads to the framework of independent component analysis, as well as models having a nonlinear relationship between latent and observed variables. Section 12.4

## 12.1.2 **Minimum-error formulation**

Principal component analysis, or PCA, is a technique that is widely used for applications such as dimensionality reduction, lossy data compression, feature extraction, and data visualization (Jolliffe, 2002). It is also known as the Karhunen-Loève transform.

There are two commonly used definitions of PCA that give rise to the same algorithm. PCA can be defined as the orthogonal projection of the data onto a lower dimensional linear space, known as the *principal subspace*, such that the variance of the projected data is maximized (Hotelling, 1933). Equivalently, it can be defined as the linear projection that minimizes the average projection cost, defined as the mean squared distance between the data points and their projections (Pearson, 1901). The process of orthogonal projection is illustrated in Figure 12.2. We consider each of these definitions in turn.

## **Maximum variance formulation** $12.1.1$

Consider a data set of observations  $\{x_n\}$  where  $n = 1, ..., N$ , and  $x_n$  is a Euclidean variable with dimensionality  $D$ . Our goal is to project the data onto a space having dimensionality  $M < D$  while maximizing the variance of the projected data. For the moment, we shall assume that the value of  $M$  is given. Later in this

561

chapter, we shall consider techniques to determine an appropriate value of  $M$  from the data.

To begin with, consider the projection onto a one-dimensional space  $(M = 1)$ . We can define the direction of this space using a D-dimensional vector  $\mathbf{u}_1$ , which for convenience (and without loss of generality) we shall choose to be a unit vector so that  $\mathbf{u}_1^T \mathbf{u}_1 = 1$  (note that we are only interested in the direction defined by  $\mathbf{u}_1$ , not in the magnitude of  $\mathbf{u}_1$  itself). Each data point  $\mathbf{x}_n$  is then projected onto a scalar value  $\mathbf{u}_1^T \mathbf{x}_n$ . The mean of the projected data is  $\mathbf{u}_1^T \overline{\mathbf{x}}$  where  $\overline{\mathbf{x}}$  is the sample set mean given by

$$
\overline{\mathbf{x}} = \frac{1}{N} \sum_{n=1}^{N} \mathbf{x}_n
$$
 (12.1)

and the variance of the projected data is given by

$$
\frac{1}{N} \sum_{n=1}^{N} \left\{ \mathbf{u}_{1}^{T} \mathbf{x}_{n} - \mathbf{u}_{1}^{T} \overline{\mathbf{x}} \right\}^{2} = \mathbf{u}_{1}^{T} \mathbf{S} \mathbf{u}_{1}
$$
\n(12.2)

where S is the data covariance matrix defined by

$$
\mathbf{S} = \frac{1}{N} \sum_{n=1}^{N} (\mathbf{x}_n - \overline{\mathbf{x}}) (\mathbf{x}_n - \overline{\mathbf{x}})^{\mathrm{T}}.
$$
 (12.3)

We now maximize the projected variance  $\mathbf{u}_1^T \mathbf{S} \mathbf{u}_1$  with respect to  $\mathbf{u}_1$ . Clearly, this has to be a constrained maximization to prevent  $\|\mathbf{u}_1\| \to \infty$ . The appropriate constraint comes from the normalization condition  $\mathbf{u}_1^{\mathrm{T}} \mathbf{u}_1 = 1$ . To enforce this constraint, we introduce a Lagrange multiplier that we shall denote by  $\lambda_1$ , and then make an unconstrained maximization of

$$
\mathbf{u}_1^{\mathrm{T}}\mathbf{S}\mathbf{u}_1 + \lambda_1 \left(1 - \mathbf{u}_1^{\mathrm{T}}\mathbf{u}_1\right). \tag{12.4}
$$

By setting the derivative with respect to  $\mathbf{u}_1$  equal to zero, we see that this quantity will have a stationary point when

$$
\mathbf{S}\mathbf{u}_1 = \lambda_1 \mathbf{u}_1 \tag{12.5}
$$

which says that  $\mathbf{u}_1$  must be an eigenvector of S. If we left-multiply by  $\mathbf{u}_1^{\mathrm{T}}$  and make use of  $\mathbf{u}_1^T \mathbf{u}_1 = 1$ , we see that the variance is given by

$$
\mathbf{u}_1^{\mathrm{T}} \mathbf{S} \mathbf{u}_1 = \lambda_1 \tag{12.6}
$$

and so the variance will be a maximum when we set  $\mathbf{u}_1$  equal to the eigenvector having the largest eigenvalue  $\lambda_1$ . This eigenvector is known as the first principal component.

We can define additional principal components in an incremental fashion by choosing each new direction to be that which maximizes the projected variance

*Appendix E*

amongst all possible directions orthogonal to those already considered. If we consider the general case of an  $M$ -dimensional projection space, the optimal linear projection for which the variance of the projected data is maximized is now defined by the M eigenvectors  $\mathbf{u}_1, \dots, \mathbf{u}_M$  of the data covariance matrix S corresponding to the M largest eigenvalues  $\lambda_1, \ldots, \lambda_M$ . This is easily shown using proof by induction.

To summarize, principal component analysis involves evaluating the mean  $\bar{x}$ and the covariance matrix  $S$  of the data set and then finding the  $M$  eigenvectors of  $S$ corresponding to the M largest eigenvalues. Algorithms for finding eigenvectors and eigenvalues, as well as additional theorems related to eigenvector decomposition, can be found in Golub and Van Loan (1996). Note that the computational cost of computing the full eigenvector decomposition for a matrix of size  $D \times D$  is  $O(D^3)$ . If we plan to project our data onto the first  $M$  principal components, then we only need to find the first  $M$  eigenvalues and eigenvectors. This can be done with more efficient techniques, such as the *power method* (Golub and Van Loan, 1996), that scale like  $O(MD^2)$ , or alternatively we can make use of the EM algorithm.

### 12.1.2 **Minimum-error formulation**

We now discuss an alternative formulation of PCA based on projection error minimization. To do this, we introduce a complete orthonormal set of  $D$ -dimensional basis vectors  $\{u_i\}$  where  $i = 1, \ldots, D$  that satisfy

$$
\mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j = \delta_{ij}.\tag{12.7}
$$

Because this basis is complete, each data point can be represented exactly by a linear combination of the basis vectors

$$
\mathbf{x}_n = \sum_{i=1}^D \alpha_{ni} \mathbf{u}_i
$$
 (12.8)

where the coefficients  $\alpha_{ni}$  will be different for different data points. This simply corresponds to a rotation of the coordinate system to a new system defined by the  ${\bf u}_i$ , and the original *D* components  ${x_{n1},...,x_{nD}}$  are replaced by an equivalent set  $\{\alpha_{n1}, \dots, \alpha_{nD}\}\$ . Taking the inner product with  $\mathbf{u}_j$ , and making use of the orthonormality property, we obtain  $\alpha_{ni} = \mathbf{x}_n^T \mathbf{u}_i$ , and so without loss of generality we can write

$$
\mathbf{x}_n = \sum_{i=1}^D (\mathbf{x}_n^{\mathrm{T}} \mathbf{u}_i) \mathbf{u}_i.
$$
 (12.9)

Our goal, however, is to approximate this data point using a representation involving a restricted number  $M < D$  of variables corresponding to a projection onto a lower-dimensional subspace. The M -dimensional linear subspace can be represented, without loss of generality, by the first  $M$  of the basis vectors, and so we approximate each data point  $x_n$  by

$$
\widetilde{\mathbf{x}}_n = \sum_{i=1}^M z_{ni} \mathbf{u}_i + \sum_{i=M+1}^D b_i \mathbf{u}_i
$$
\n(12.10)

*Section 12.2.2*

*Exercise 12.1*

*Appendix* C

where the  $\{z_{ni}\}$  depend on the particular data point, whereas the  $\{b_i\}$  are constants that are the same for all data points. We are free to choose the  $\{u_i\}$ , the  $\{z_{ni}\}$ , and the  $\{b_i\}$  so as to minimize the distortion introduced by the reduction in dimensionality. As our distortion measure, we shall use the squared distance between the original data point  $x_n$  and its approximation  $\tilde{x}_n$ , averaged over the data set, so that our goal is to minimize

$$
J = \frac{1}{N} \sum_{n=1}^{N} ||\mathbf{x}_n - \widetilde{\mathbf{x}}_n||^2.
$$
 (12.11)

Consider first of all the minimization with respect to the quantities  $\{z_{ni}\}\$ . Substituting for  $\widetilde{\mathbf{x}}_n$ , setting the derivative with respect to  $z_{nj}$  to zero, and making use of the orthonormality conditions, we obtain

$$
z_{nj} = \mathbf{x}_n^{\mathrm{T}} \mathbf{u}_j \tag{12.12}
$$

where  $j = 1, \ldots, M$ . Similarly, setting the derivative of J with respect to  $b_i$  to zero, and again making use of the orthonormality relations, gives

$$
b_j = \overline{\mathbf{x}}^{\mathrm{T}} \mathbf{u}_j \tag{12.13}
$$

where  $j = M+1, \ldots, D$ . If we substitute for  $z_{ni}$  and  $b_i$ , and make use of the general expansion (12.9), we obtain

$$
\mathbf{x}_n - \widetilde{\mathbf{x}}_n = \sum_{i=M+1}^D \left\{ (\mathbf{x}_n - \overline{\mathbf{x}})^{\mathrm{T}} \mathbf{u}_i \right\} \mathbf{u}_i \tag{12.14}
$$

from which we see that the displacement vector from  $x_n$  to  $\tilde{x}_n$  lies in the space orthogonal to the principal subspace, because it is a linear combination of  $\{u_i\}$  for  $i = M + 1, \ldots, D$ , as illustrated in Figure 12.2. This is to be expected because the projected points  $\widetilde{\mathbf{x}}_n$  must lie within the principal subspace, but we can move them freely within that subspace, and so the minimum error is given by the orthogonal projection.

We therefore obtain an expression for the distortion measure  $J$  as a function purely of the  $\{u_i\}$  in the form

$$
J = \frac{1}{N} \sum_{n=1}^{N} \sum_{i=M+1}^{D} \left( \mathbf{x}_n^{\mathrm{T}} \mathbf{u}_i - \overline{\mathbf{x}}^{\mathrm{T}} \mathbf{u}_i \right)^2 = \sum_{i=M+1}^{D} \mathbf{u}_i^{\mathrm{T}} \mathbf{S} \mathbf{u}_i.
$$
 (12.15)

There remains the task of minimizing J with respect to the  $\{u_i\}$ , which must be a constrained minimization otherwise we will obtain the vacuous result  $u_i = 0$ . The constraints arise from the orthonormality conditions and, as we shall see, the solution will be expressed in terms of the eigenvector expansion of the covariance matrix. Before considering a formal solution, let us try to obtain some intuition about the result by considering the case of a two-dimensional data space  $D = 2$  and a onedimensional principal subspace  $M = 1$ . We have to choose a direction  $\mathbf{u}_2$  so as to

minimize  $J = \mathbf{u}_2^T \mathbf{S} \mathbf{u}_2$ , subject to the normalization constraint  $\mathbf{u}_2^T \mathbf{u}_2 = 1$ . Using a Lagrange multiplier  $\lambda_2$  to enforce the constraint, we consider the minimization of

$$
\widetilde{J} = \mathbf{u}_2^{\mathrm{T}} \mathbf{S} \mathbf{u}_2 + \lambda_2 \left( 1 - \mathbf{u}_2^{\mathrm{T}} \mathbf{u}_2 \right). \tag{12.16}
$$

Setting the derivative with respect to  $u_2$  to zero, we obtain  $Su_2 = \lambda_2 u_2$  so that  $u_2$ is an eigenvector of S with eigenvalue  $\lambda_2$ . Thus any eigenvector will define a stationary point of the distortion measure. To find the value of  $J$  at the minimum, we back-substitute the solution for  $u_2$  into the distortion measure to give  $J = \lambda_2$ . We therefore obtain the minimum value of  $J$  by choosing  $\mathbf{u}_2$  to be the eigenvector corresponding to the smaller of the two eigenvalues. Thus we should choose the principal subspace to be aligned with the eigenvector having the *larger* eigenvalue. This result accords with our intuition that, in order to minimize the average squared projection distance, we should choose the principal component subspace to pass through the mean of the data points and to be aligned with the directions of maximum variance. For the case when the eigenvalues are equal, any choice of principal direction will give rise to the same value of J.

The general solution to the minimization of J for arbitrary  $D$  and arbitrary  $M <$ *D* is obtained by choosing the  $\{u_i\}$  to be eigenvectors of the covariance matrix given by

$$
\mathbf{S}\mathbf{u}_i = \lambda_i \mathbf{u}_i \tag{12.17}
$$

where  $i = 1, \ldots, D$ , and as usual the eigenvectors  $\{u_i\}$  are chosen to be orthonormal. The corresponding value of the distortion measure is then given by

$$
J = \sum_{i=M+1}^{D} \lambda_i
$$
 (12.18)

which is simply the sum of the eigenvalues of those eigenvectors that are orthogonal to the principal subspace. We therefore obtain the minimum value of  $J$  by selecting these eigenvectors to be those having the  $D - M$  smallest eigenvalues, and hence the eigenvectors defining the principal subspace are those corresponding to the M largest eigenvalues.

Although we have considered  $M < D$ , the PCA analysis still holds if  $M =$  $D$ , in which case there is no dimensionality reduction but simply a rotation of the coordinate axes to align with principal components.

Finally, it is worth noting that there exists a closely related linear dimensionality reduction technique called *canonical correlation analysis,* or *CCA* (Hotelling, 1936; Bach and Jordan, 2002). Whereas PCA works with a single random variable, CCA considers two (or more) variables and tries to find a corresponding pair of linear subspaces that have high cross-correlation, so that each component within one of the subspaces is correlated with a single component from the other subspace. Its solution can be expressed in terms of a generalized eigenvector problem.

## **12.1.3 Applications of peA**

We can illustrate the use of PCA for data compression by considering the offline digits data set. Because each eigenvector of the covariance matrix is a vector

*Exercise 12.2*

*Appendix A*

Image /page/7/Figure/1 description: The image displays a series of five images, each depicting the digit '3'. The first image, labeled 'Mean', shows a blurred representation of the digit. The subsequent four images are labeled with eigenvalues: \"lambda\_1 = 3.4 \* 10^5\", \"lambda\_2 = 2.8 \* 10^5\", \"lambda\_3 = 2.4 \* 10^5\", and \"lambda\_4 = 1.6 \* 10^5\". These images show variations or components of the digit '3', with colorations of purple, yellow, and white, suggesting different features or modes of variation.

**Figure 12.3** The mean vector  $\bar{x}$  along with the first four PCA eigenvectors  $u_1, \ldots, u_4$  for the off-line digits data set, together with the corresponding eigenvalues.

in the original  $D$ -dimensional space, we can represent the eigenvectors as images of the same size as the data points. The first five eigenvectors, along with the corresponding eigenvalues, are shown in Figure 12.3. A plot of the complete spectrum of eigenvalues, sorted into decreasing order, is shown in Figure 12.4(a). The distortion measure  $J$  associated with choosing a particular value of  $M$  is given by the sum of the eigenvalues from  $M + 1$  up to D and is plotted for different values of M in Figure 12.4(b).

If we substitute  $(12.12)$  and  $(12.13)$  into  $(12.10)$ , we can write the PCA approximation to a data vector  $x_n$  in the form

$$
\widetilde{\mathbf{x}}_n = \sum_{i=1}^M (\mathbf{x}_n^{\mathrm{T}} \mathbf{u}_i) \mathbf{u}_i + \sum_{i=M+1}^D (\overline{\mathbf{x}}^{\mathrm{T}} \mathbf{u}_i) \mathbf{u}_i
$$
\n(12.19)

$$
= \overline{\mathbf{x}} + \sum_{i=1}^{M} \left( \mathbf{x}_n^{\mathrm{T}} \mathbf{u}_i - \overline{\mathbf{x}}^{\mathrm{T}} \mathbf{u}_i \right) \mathbf{u}_i
$$
 (12.20)

Image /page/7/Figure/7 description: The image contains two plots side-by-side. Plot (a) shows a curve labeled \"λᵢ\" on the y-axis and \"i\" on the x-axis. The y-axis is scaled by 10⁵, with values ranging from 0 to 3. The curve starts at approximately 3.5 x 10⁵ and rapidly decreases, approaching the x-axis. Plot (b) shows a curve labeled \"J\" on the y-axis and \"M\" on the x-axis. The y-axis is scaled by 10⁶, with values ranging from 0 to 3. The curve starts at approximately 2.8 x 10⁶ and also rapidly decreases, approaching the x-axis. Both plots show a similar decaying trend.

Figure 12.4 (a) Plot of the eigenvalue spectrum for the off-line digits data set. (b) Plot of the sum of the discarded eigenvalues, which represents the sum-of-squares distortion J introduced by projecting the data onto a principal component subspace of dimensionality  $M$ .

### 12.1. Principal Component Analysis 567

Image /page/8/Figure/1 description: The image displays a series of five images of the digit '3'. The first image is labeled 'Original' and shows a clear, dark digit '3'. The subsequent four images are labeled 'M = 1', 'M = 10', 'M = 50', and 'M = 250', respectively. These images show progressively clearer reconstructions of the digit '3' as the value of M increases, starting from a very blurry representation at M=1 and becoming sharper and more detailed with higher M values.

Figure 12.5 An original example from the off-line digits data set together with its PCA reconstructions obtained by retaining  $M$  principal components for various values of  $M$ . As  $M$  increases the reconstruction becomes more accurate and would become perfect when  $M = D =$  $28 \times 28 = 784.$ 

where we have made use of the relation

$$
\overline{\mathbf{x}} = \sum_{i=1}^{D} (\overline{\mathbf{x}}^{\mathrm{T}} \mathbf{u}_i) \mathbf{u}_i
$$
 (12.21)

which follows from the completeness of the  $\{u_i\}$ . This represents a compression of the data set, because for each data point we have replaced the  $D$ -dimensional vector  $x_n$  with an M-dimensional vector having components  $(x_n^T u_i - \overline{x}^T u_i)$ . The smaller the value of  $M$ , the greater the degree of compression. Examples of PCA reconstructions of data points for the digits data set are shown in Figure 12.5.

Another application of principal component analysis is to data pre-processing. In this case, the goal is not dimensionality reduction but rather the transformation of a data set in order to standardize certain of its properties. This can be important in allowing subsequent pattern recognition algorithms to be applied successfully to the data set. Typically, it is done when the original variables are measured in various different units or have significantly different variability. For instance in the Old Faithful data set, the time between eruptions is typically an order of magnitude greater than the duration of an eruption. When we applied the  $K$ -means algorithm to this data set, we first made a separate linear re-scaling of the individual variables such that each variable had zero mean and unit variance. This is known as *standardizing* the data, and the covariance matrix for the standardized data has components

$$
\rho_{ij} = \frac{1}{N} \sum_{n=1}^{N} \frac{(x_{ni} - \overline{x}_i)}{\sigma_i} \frac{(x_{nj} - \overline{x}_j)}{\sigma_j}
$$
(12.22)

where  $\sigma_i$  is the variance of  $x_i$ . This is known as the *correlation* matrix of the original data and has the property that if two components  $x_i$  and  $x_j$  of the data are perfectly correlated, then  $\rho_{ij} = 1$ , and if they are uncorrelated, then  $\rho_{ij} = 0$ .

However, using PCA we can make a more substantial normalization of the data to give it zero mean and unit covariance, so that different variables become decorrelated. To do this, we first write the eigenvector equation (12.17) in the form

$$
SU = UL \tag{12.23}
$$

Appendix A

Section 9.1

Image /page/9/Figure/1 description: The image displays three scatter plots side-by-side. The first plot has an x-axis ranging from 2 to 6 and a y-axis ranging from 40 to 100, with data points clustered in two main groups. The second plot has an x-axis ranging from -2 to 2 and a y-axis ranging from -2 to 2, showing a similar pattern to the first plot but with a line segment drawn through the center of the data. The third plot also has an x-axis ranging from -2 to 2 and a y-axis ranging from -2 to 2, displaying a more dispersed set of data points with a general upward trend.

**Figure 12.6** Illustration of the effects of linear pre-processing applied to the Old Faithful data set. The plot on the left shows the original data. The centre plot shows the result of standardizing the individual variables to zero mean and unit variance. Also shown are the principal axes of this normalized data set, plotted over the range  $\pm\lambda^{1/2}_s$ . The plot on the right shows the result of whitening of the data to give it zero mean and unit covariance.

where **L** is a  $D \times D$  diagonal matrix with elements  $\lambda_i$ , and **U** is a  $D \times D$  orthogonal matrix with columns given by  $\mathbf{u}_i$ . Then we define, for each data point  $\mathbf{x}_n$ , a transformed value given by

$$
\mathbf{y}_n = \mathbf{L}^{-1/2} \mathbf{U}^{\mathrm{T}} (\mathbf{x}_n - \overline{\mathbf{x}})
$$
 (12.24)

where  $\bar{x}$  is the sample mean defined by (12.1). Clearly, the set  $\{y_n\}$  has zero mean, and its covariance is given by the identity matrix because

$$
\frac{1}{N} \sum_{n=1}^{N} \mathbf{y}_n \mathbf{y}_n^{\mathrm{T}} = \frac{1}{N} \sum_{n=1}^{N} \mathbf{L}^{-1/2} \mathbf{U}^{\mathrm{T}} (\mathbf{x}_n - \overline{\mathbf{x}}) (\mathbf{x}_n - \overline{\mathbf{x}})^{\mathrm{T}} \mathbf{U} \mathbf{L}^{-1/2}
$$

$$
= \mathbf{L}^{-1/2} \mathbf{U}^{\mathrm{T}} \mathbf{S} \mathbf{U} \mathbf{L}^{-1/2} = \mathbf{L}^{-1/2} \mathbf{L} \mathbf{L}^{-1/2} = \mathbf{I}. \qquad (12.25)
$$

This operation is known as *whitening* or *sphereing* the data and is illustrated for the Old Faithful data set in Figure 12.6.

It is interesting to compare PCA with the Fisher linear discriminant which was discussed in Section 4.1.4. Both methods can be viewed as techniques for linear dimensionality reduction. However, PCA is unsupervised and depends only on the values  $x_n$  whereas Fisher linear discriminant also uses class-label information. This difference is highlighted by the example in Figure 12.7.

Another common application of principal component analysis is to data visualization. Here each data point is projected onto a two-dimensional  $(M = 2)$  principal subspace, so that a data point  $x_n$  is plotted at Cartesian coordinates given by  $x_n^T u_1$ and  $x_n^T u_2$ , where  $u_1$  and  $u_2$  are the eigenvectors corresponding to the largest and second largest eigenvalues. An example of such a plot, for the oil flow data set, is shown in Figure 12.8.

*Appendix* A

*Appendix A*

Figure 12.7 A comparison of principal component analysis with Fisher's linear discriminant for linear dimensionality reduction. Here the data in two dimensions, belonging to two classes shown in red and blue, is to be projected onto a single dimension. PCA chooses the direction of maximum variance, shown by the magenta curve, which leads to strong class overlap, whereas the Fisher linear discriminant takes account of the class labels and leads to a projection onto the green curve giving much better class separation.

Image /page/10/Figure/2 description: A scatter plot shows two distinct groups of data points, one colored blue and the other red. The blue points are clustered in the upper region of the plot, roughly between y-values of 0.5 and 1.5, and extending horizontally from approximately x=-5 to x=5. The red points are concentrated in the lower region, primarily between y-values of -0.5 and -1.5, and also spanning horizontally from around x=-5 to x=5. A vertical green line is drawn at x=0, separating the plot into left and right halves. A diagonal red line, representing a decision boundary, starts near the top left of the plot (around x=-5, y=0.3) and slopes downwards to the right, crossing the vertical green line at approximately y=0 and continuing to the bottom right (around x=5, y=-0.3). The plot has x-axis labels from -5 to 5 and y-axis labels from -2 to 1.5.

Visualization of the oil flow data set obtained Figure 12.8 by projecting the data onto the first two principal components. The red, blue, and green points correspond to the 'laminar', 'homogeneous', and 'annular' flow configurations respectively.

Image /page/10/Figure/4 description: A scatter plot displays three distinct groups of data points. The largest group, colored green and marked with circles, is clustered horizontally across the center of the plot. Below this group, a second cluster of blue plus signs extends horizontally, largely overlapping with the green circles. A third group of red crosses is scattered below the main clusters, with a denser concentration in the lower left quadrant and a sparser distribution to the right.

### PCA for high-dimensional data 12.1.4

In some applications of principal component analysis, the number of data points is smaller than the dimensionality of the data space. For example, we might want to apply PCA to a data set of a few hundred images, each of which corresponds to a vector in a space of potentially several million dimensions (corresponding to three colour values for each of the pixels in the image). Note that in a D-dimensional space a set of N points, where  $N < D$ , defines a linear subspace whose dimensionality is at most  $N-1$ , and so there is little point in applying PCA for values of M that are greater than  $N - 1$ . Indeed, if we perform PCA we will find that at least  $D-N+1$  of the eigenvalues are zero, corresponding to eigenvectors along whose directions the data set has zero variance. Furthermore, typical algorithms for finding the eigenvectors of a  $D \times D$  matrix have a computational cost that scales like  $O(D^3)$ , and so for applications such as the image example, a direct application of PCA will be computationally infeasible.

We can resolve this problem as follows. First, let us define **X** to be the  $(N \times D)$ -

dimensional centred data matrix, whose  $n^{\text{th}}$  row is given by  $(\mathbf{x}_n - \overline{\mathbf{x}})^{\text{T}}$ . The covariance matrix (12.3) can then be written as  $S = N^{-1}X^{T}X$ , and the corresponding eigenvector equation becomes

$$
\frac{1}{N} \mathbf{X}^{\mathrm{T}} \mathbf{X} \mathbf{u}_{i} = \lambda_{i} \mathbf{u}_{i}.
$$
 (12.26)

Now pre-multiply both sides by  $X$  to give

$$
\frac{1}{N} \mathbf{X} \mathbf{X}^{\mathrm{T}} (\mathbf{X} \mathbf{u}_{i}) = \lambda_{i} (\mathbf{X} \mathbf{u}_{i}).
$$
\n(12.27)

If we now define  $v_i = Xu_i$ , we obtain

$$
\frac{1}{N} \mathbf{X} \mathbf{X}^{\mathrm{T}} \mathbf{v}_i = \lambda_i \mathbf{v}_i
$$
 (12.28)

which is an eigenvector equation for the  $N \times N$  matrix  $N^{-1} \mathbf{X} \mathbf{X}^{T}$ . We see that this has the same  $N-1$  eigenvalues as the original covariance matrix (which itself has an additional  $D - N + 1$  eigenvalues of value zero). Thus we can solve the eigenvector problem in spaces of lower dimensionality with computational cost  $O(N^3)$  instead of  $O(D^3)$ . In order to determine the eigenvectors, we multiply both sides of (12.28) by  $X<sup>T</sup>$  to give

$$
\left(\frac{1}{N}\mathbf{X}^{\mathrm{T}}\mathbf{X}\right)(\mathbf{X}^{\mathrm{T}}\mathbf{v}_{i}) = \lambda_{i}(\mathbf{X}^{\mathrm{T}}\mathbf{v}_{i})
$$
\n(12.29)

from which we see that  $(X^T v_i)$  is an eigenvector of S with eigenvalue  $\lambda_i$ . Note, however, that these eigenvectors need not be normalized. To determine the appropriate normalization, we re-scale  $\mathbf{u}_i \propto \mathbf{X}^T \mathbf{v}_i$  by a constant such that  $\|\mathbf{u}_i\| = 1$ , which, assuming  $v_i$  has been normalized to unit length, gives

$$
\mathbf{u}_{i} = \frac{1}{(N\lambda_{i})^{1/2}} \mathbf{X}^{\mathrm{T}} \mathbf{v}_{i}.
$$
 (12.30)

In summary, to apply this approach we first evaluate  $\mathbf{X} \mathbf{X}^{\mathrm{T}}$  and then find its eigenvectors and eigenvalues and then compute the eigenvectors in the original data space using (12.30).

## **12.2. Probabilistic peA**

The formulation of PCA discussed in the previous section was based on a linear projection of the data onto a subspace of lower dimensionality than the original data space. We now show that PCA can also be expressed as the maximum likelihood solution of a probabilistic latent variable model. This reformulation of PCA, known as *probabilistic PCA*, brings several advantages compared with conventional PCA:

• Probabilistic PCA represents a constrained form of the Gaussian distribution in which the number of free parameters can be restricted while still allowing the model to capture the dominant correlations in a data set.

| Section 12.2.2 | • We can derive an EM algorithm for PCA that is computationally efficient in<br>situations where only a few leading eigenvectors are required and that avoids<br>having to evaluate the data covariance matrix as an intermediate step.                                                                                                                                                                     |
|----------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                | • The combination of a probabilistic model and EM allows us to deal with miss-<br>ing values in the data set.                                                                                                                                                                                                                                                                                               |
|                | • Mixtures of probabilistic PCA models can be formulated in a principled way<br>and trained using the EM algorithm.                                                                                                                                                                                                                                                                                         |
| Section 12.2.3 | • Probabilistic PCA forms the basis for a Bayesian treatment of PCA in which<br>the dimensionality of the principal subspace can be found automatically from<br>the data.                                                                                                                                                                                                                                   |
|                | • The existence of a likelihood function allows direct comparison with other<br>probabilistic density models. By contrast, conventional PCA will assign a low<br>reconstruction cost to data points that are close to the principal subspace even<br>if they lie arbitrarily far from the training data.                                                                                                    |
|                | • Probabilistic PCA can be used to model class-conditional densities and hence<br>be applied to classification problems.                                                                                                                                                                                                                                                                                    |
|                | • The probabilistic PCA model can be run generatively to provide samples from<br>the distribution.                                                                                                                                                                                                                                                                                                          |
| Section 8.1.4  | This formulation of PCA as a probabilistic model was proposed independently by<br>Tipping and Bishop (1997, 1999b) and by Roweis (1998). As we shall see later, it is<br>closely related to <i>factor analysis</i> (Basilevsky, 1994).<br>Probabilistic PCA is a simple example of the linear-Gaussian framework, in<br>which all of the marginal and conditional distributions are Gaussian. We can formu- |
|                | late probabilistic PCA by first introducing an explicit latent variable z corresponding<br>to the principal-component subspace. Next we define a Gaussian prior distribution<br>$p(\mathbf{z})$ over the latent variable, together with a Gaussian conditional distribution $p(\mathbf{x} \mathbf{z})$                                                                                                      |

 $p(\mathbf{z}) = \mathcal{N}(\mathbf{z}|\mathbf{0}, \mathbf{I}).$  (12.31)

Similarly, the conditional distribution of the observed variable  $x$ , conditioned on the value of the latent variable z, is again Gaussian, of the form

for the observed variable x conditioned on the value of the latent variable. Specifically, the prior distribution over z is given by a zero-mean unit-covariance Gaussian

$$
p(\mathbf{x}|\mathbf{z}) = \mathcal{N}(\mathbf{x}|\mathbf{W}\mathbf{z} + \boldsymbol{\mu}, \sigma^2 \mathbf{I})
$$
 (12.32)

in which the mean of **x** is a general linear function of **z** governed by the  $D \times M$ matrix W and the D-dimensional vector  $\mu$ . Note that this factorizes with respect to the elements of x, in other words this is an example of the naive Bayes model. As we shall see shortly, the columns of  $W$  span a linear subspace within the data space that corresponds to the principal subspace. The other parameter in this model is the scalar  $\sigma^2$  governing the variance of the conditional distribution. Note that there is no

*Section 8.2.2*

Image /page/13/Figure/1 description: The image displays three plots illustrating a probabilistic model. The leftmost plot shows a univariate probability distribution, labeled p(z), with a peak at z=0 and a point marked at z-hat. A dashed curve connects this point to the middle plot. The middle plot is a 2D scatter plot with axes labeled x1 and x2. A blue line represents a linear relationship, with a point labeled mu on it. Another point, z-hat|w, is shown on the line, with concentric red circles indicating a probability distribution p(x|z-hat). A vector w is also depicted along the blue line. The rightmost plot shows a 2D probability distribution p(x) as a set of green elliptical contours in the x1-x2 plane, with a point labeled mu at the center.

Figure 12.9 An illustration of the generative view of the probabilistic PCA model for a two-dimensional data space and a one-dimensional latent space. An observed data point x is generated by first drawing a value  $\widehat{z}$ for the latent variable from its prior distribution  $p(z)$  and then drawing a value for x from an isotropic Gaussian distribution (illustrated by the red circles) having mean  $w\hat{z} + \mu$  and covariance  $\sigma^2 I$ . The green ellipses show the density contours for the marginal distribution  $p(x)$ .

loss of generality in assuming a zero mean, unit covariance Gaussian for the latent distribution  $p(z)$  because a more general Gaussian distribution would give rise to an equivalent probabilistic model.

We can view the probabilistic PCA model from a generative viewpoint in which a sampled value of the observed variable is obtained by first choosing a value for the latent variable and then sampling the observed variable conditioned on this latent value. Specifically, the  $D$ -dimensional observed variable  $x$  is defined by a linear transformation of the  $M$ -dimensional latent variable  $z$  plus additive Gaussian 'noise', so that

$$
\mathbf{x} = \mathbf{W}\mathbf{z} + \boldsymbol{\mu} + \boldsymbol{\epsilon} \tag{12.33}
$$

where z is an M-dimensional Gaussian latent variable, and  $\epsilon$  is a D-dimensional zero-mean Gaussian-distributed noise variable with covariance  $\sigma^2 I$ . This generative process is illustrated in Figure 12.9. Note that this framework is based on a mapping from latent space to data space, in contrast to the more conventional view of PCA discussed above. The reverse mapping, from data space to the latent space, will be obtained shortly using Bayes' theorem.

Suppose we wish to determine the values of the parameters W,  $\mu$  and  $\sigma^2$  using maximum likelihood. To write down the likelihood function, we need an expression for the marginal distribution  $p(x)$  of the observed variable. This is expressed, from the sum and product rules of probability, in the form

$$
p(\mathbf{x}) = \int p(\mathbf{x}|\mathbf{z})p(\mathbf{z}) \, \mathrm{d}\mathbf{z}.\tag{12.34}
$$

Because this corresponds to a linear-Gaussian model, this marginal distribution is again Gaussian, and is given by

$$
p(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \mathbf{C}) \tag{12.35}
$$

Exercise 12.4

Exercise 12.7

where the  $D \times D$  covariance matrix C is defined by

$$
\mathbf{C} = \mathbf{W}\mathbf{W}^{\mathrm{T}} + \sigma^2 \mathbf{I}.
$$
 (12.36)

This result can also be derived more directly by noting that the predictive distribution will be Gaussian and then evaluating its mean and covariance using  $(12.33)$ . This gives

$$
\mathbb{E}[\mathbf{x}] = \mathbb{E}[\mathbf{W}\mathbf{z} + \boldsymbol{\mu} + \boldsymbol{\epsilon}] = \boldsymbol{\mu}
$$
\n
$$
\text{cov}[\mathbf{x}] = \mathbb{E}[(\mathbf{W}\mathbf{z} + \boldsymbol{\epsilon})(\mathbf{W}\mathbf{z} + \boldsymbol{\epsilon})^{\text{T}}]
$$
\n(12.37)

$$
= \mathbb{E} \left[ \mathbf{W} \mathbf{z} \mathbf{z}^{\mathrm{T}} \mathbf{W}^{\mathrm{T}} \right] + \mathbb{E} \left[ \boldsymbol{\epsilon} \boldsymbol{\epsilon}^{\mathrm{T}} \right] = \mathbf{W} \mathbf{W}^{\mathrm{T}} + \sigma^2 \mathbf{I} \qquad (12.38)
$$

where we have used the fact that z and  $\epsilon$  are independent random variables and hence are uncorrelated.

Intuitively, we can think of the distribution  $p(x)$  as being defined by taking an isotropic Gaussian 'spray can' and moving it across the principal subspace spraying Gaussian ink with density determined by  $\sigma^2$  and weighted by the prior distribution. The accumulated ink density gives rise to a 'pancake' shaped distribution representing the marginal density  $p(x)$ .

The predictive distribution  $p(x)$  is governed by the parameters  $\mu$ , **W**, and  $\sigma^2$ . However, there is redundancy in this parameterization corresponding to rotations of the latent space coordinates. To see this, consider a matrix  $\widetilde{W} = WR$  where R is an orthogonal matrix. Using the orthogonality property  $\mathbf{R}\mathbf{R}^{\mathrm{T}} = \mathbf{I}$ , we see that the quantity  $\widetilde{\mathbf{W}}\widetilde{\mathbf{W}}^{\mathrm{T}}$  that appears in the covariance matrix C takes the form

$$
\mathbf{W}\mathbf{W}^{\mathrm{T}} = \mathbf{W}\mathbf{R}\mathbf{R}^{\mathrm{T}}\mathbf{W}^{\mathrm{T}} = \mathbf{W}\mathbf{W}^{\mathrm{T}} \tag{12.39}
$$

and hence is independent of  $\bf R$ . Thus there is a whole family of matrices  $\bf W$  all of which give rise to the same predictive distribution. This invariance can be understood in terms of rotations within the latent space. We shall return to a discussion of the number of independent parameters in this model later.

When we evaluate the predictive distribution, we require  $C^{-1}$ , which involves the inversion of a  $D \times D$  matrix. The computation required to do this can be reduced by making use of the matrix inversion identity (C.7) to give

$$
\mathbf{C}^{-1} = \sigma^{-1}\mathbf{I} - \sigma^{-2}\mathbf{W}\mathbf{M}^{-1}\mathbf{W}^{\mathrm{T}}
$$
 (12.40)

where the  $M \times M$  matrix M is defined by

 $\sim$   $\sim$ 

$$
\mathbf{M} = \mathbf{W}^{\mathrm{T}} \mathbf{W} + \sigma^2 \mathbf{I}.
$$
 (12.41)

Because we invert M rather than inverting C directly, the cost of evaluating  $C^{-1}$  is reduced from  $O(D^3)$  to  $O(M^3)$ .

As well as the predictive distribution  $p(x)$ , we will also require the posterior distribution  $p(z|x)$ , which can again be written down directly using the result (2.116) for linear-Gaussian models to give

$$
p(\mathbf{z}|\mathbf{x}) = \mathcal{N}\left(\mathbf{z}|\mathbf{M}^{-1}\mathbf{W}^{\mathrm{T}}(\mathbf{x} - \boldsymbol{\mu}), \sigma^{-2}\mathbf{M}\right).
$$
 (12.42)

Note that the posterior mean depends on  $x$ , whereas the posterior covariance is independent of x.

*Exercise 12.8*

**Figure 12.10** The probabilistic PCA model for a data set of  $N$  observations of x can be expressed as a directed graph in which each observation  $x_n$  is associated with a value  $z_n$  of the latent variable.

Image /page/15/Figure/2 description: This is a graphical model diagram. It shows a plate labeled 'N' which contains two nodes, z\_n and x\_n. The node z\_n is represented by an unfilled circle, and the node x\_n is represented by a filled circle. There is an arrow pointing from z\_n to x\_n. There are also external variables mu and sigma^2, and a variable W. Mu has an arrow pointing to x\_n. Sigma^2 has an arrow pointing to x\_n. W has arrows pointing to and from x\_n.

### 12.2.1 **Maximum likelihood peA**

We next consider the determination of the model parameters using maximum likelihood. Given a data set  $X = \{x_n\}$  of observed data points, the probabilistic peA model can be expressed as a directed graph, as shown in Figure 12.10. The corresponding log likelihood function is given, from (12.35), by

$$
\ln p(\mathbf{X}|\boldsymbol{\mu}, \mathbf{W}, \sigma^2) = \sum_{n=1}^{N} \ln p(\mathbf{x}_n | \mathbf{W}, \boldsymbol{\mu}, \sigma^2)
$$
$$
= -\frac{ND}{2} \ln(2\pi) - \frac{N}{2} \ln |\mathbf{C}| - \frac{1}{2} \sum_{n=1}^{N} (\mathbf{x}_n - \boldsymbol{\mu})^{\mathrm{T}} \mathbf{C}^{-1} (\mathbf{x}_n - \boldsymbol{\mu}). \quad (12.43)
$$

Setting the derivative of the log likelihood with respect to  $\mu$  equal to zero gives the expected result  $\mu = \bar{x}$  where  $\bar{x}$  is the data mean defined by (12.1). Back-substituting we can then write the log likelihood function in the form

$$
\ln p(\mathbf{X}|\mathbf{W}, \boldsymbol{\mu}, \sigma^2) = -\frac{N}{2} \left\{ D \ln(2\pi) + \ln |\mathbf{C}| + \text{Tr}(\mathbf{C}^{-1}\mathbf{S}) \right\}
$$
(12.44)

where S is the data covariance matrix defined by (12.3). Because the log likelihood is a quadratic function of  $\mu$ , this solution represents the unique maximum, as can be confirmed by computing second derivatives.

Maximization with respect to W and  $\sigma^2$  is more complex but nonetheless has an exact closed-form solution. It was shown by Tipping and Bishop (1999b) that all of the stationary points of the log likelihood function can be written as

$$
\mathbf{W}_{\mathrm{ML}} = \mathbf{U}_M (\mathbf{L}_M - \sigma^2 \mathbf{I})^{1/2} \mathbf{R}
$$
 (12.45)

where  $U_M$  is a  $D \times M$  matrix whose columns are given by any subset (of size M) of the eigenvectors of the data covariance matrix S, the  $M \times M$  diagonal matrix  ${\bf L}_M$  has elements given by the corresponding eigenvalues  $\lambda_i$ , and R is an arbitrary  $M \times M$  orthogonal matrix.

Furthermore, Tipping and Bishop (1999b) showed that the *maximum* of the likelihood function is obtained when the  $M$  eigenvectors are chosen to be those whose eigenvalues are the *M* largest (all other solutions being saddle points). A similar result was conjectured independently by Roweis (1998), although no proof was given.

Again, we shall assume that the eigenvectors have been arranged in order of decreasing values of the corresponding eigenvalues, so that the  $M$  principal eigenvectors are  $u_1, \ldots, u_M$ . In this case, the columns of W define the principal subspace of standard PCA. The corresponding maximum likelihood solution for  $\sigma^2$  is then given by

$$
\sigma_{\rm ML}^2 = \frac{1}{D - M} \sum_{i=M+1}^{D} \lambda_i
$$
 (12.46)

so that  $\sigma_{ML}^2$  is the average variance associated with the discarded dimensions.

Because R is orthogonal, it can be interpreted as a rotation matrix in the  $M \times M$ latent space. If we substitute the solution for  $W$  into the expression for  $C$ , and make use of the orthogonality property  $\mathbf{R}\mathbf{R}^{\mathrm{T}} = \mathbf{I}$ , we see that C is independent of R. This simply says that the predictive density is unchanged by rotations in the latent space as discussed earlier. For the particular case of  $\mathbf{R} = \mathbf{I}$ , we see that the columns of W are the principal component eigenvectors scaled by the variance parameters  $\lambda_i - \sigma^2$ . The interpretation of these scaling factors is clear once we recognize that for a convolution of independent Gaussian distributions (in this case the latent space distribution and the noise model) the variances are additive. Thus the variance  $\lambda_i$ in the direction of an eigenvector  $\mathbf{u}_i$  is composed of the sum of a contribution  $\lambda_i$  - $\sigma^2$  from the projection of the unit-variance latent space distribution into data space through the corresponding column of  $W$ , plus an isotropic contribution of variance  $\sigma^2$  which is added in all directions by the noise model.

It is worth taking a moment to study the form of the covariance matrix given by (12.36). Consider the variance of the predictive distribution along some direction specified by the unit vector **v**, where  $\mathbf{v}^T \mathbf{v} = 1$ , which is given by  $\mathbf{v}^T \mathbf{C} \mathbf{v}$ . First suppose that v is orthogonal to the principal subspace, in other words it is given by some linear combination of the discarded eigenvectors. Then  $v<sup>T</sup>U = 0$  and hence  $v<sup>T</sup>Cv = \sigma<sup>2</sup>$ . Thus the model predicts a noise variance orthogonal to the principal subspace, which, from (12.46), is just the average of the discarded eigenvalues. Now suppose that  $\mathbf{v} = \mathbf{u}_i$  where  $\mathbf{u}_i$  is one of the retained eigenvectors defining the principal subspace. Then  $\mathbf{v}^T \mathbf{C} \mathbf{v} = (\lambda_i - \sigma^2) + \sigma^2 = \lambda_i$ . In other words, this model correctly captures the variance of the data along the principal axes, and approximates the variance in all remaining directions with a single average value  $\sigma^2$ .

One way to construct the maximum likelihood density model would simply be to find the eigenvectors and eigenvalues of the data covariance matrix and then to evaluate W and  $\sigma^2$  using the results given above. In this case, we would choose  $R = I$  for convenience. However, if the maximum likelihood solution is found by numerical optimization of the likelihood function, for instance using an algorithm such as conjugate gradients (Fletcher, 1987; Nocedal and Wright, 1999; Bishop and Nabney, 2008) or through the EM algorithm, then the resulting value of  **is es**sentially arbitrary. This implies that the columns of W need not be orthogonal. If an orthogonal basis is required, the matrix  $W$  can be post-processed appropriately (Golub and Van Loan, 1996). Alternatively, the EM algorithm can be modified in such a way as to yield orthonormal principal directions, sorted in descending order of the corresponding eigenvalues, directly (Ahn and Oh, 2003).

*Section 12.2.2*

*Section 2.3*

The rotational invariance in latent space represents a form of statistical nonidentifiability, analogous to that encountered for mixture models in the case of discrete latent variables. Here there is a continuum of parameters all of which lead to the same predictive density, in contrast to the discrete nonidentifiability associated with component re-labelling in the mixture setting.

If we consider the case of  $M = D$ , so that there is no reduction of dimensionality, then  $U_M = U$  and  $L_M = L$ . Making use of the orthogonality properties  $UU<sup>T</sup> = I$  and  $RR<sup>T</sup> = I$ , we see that the covariance C of the marginal distribution for x becomes

$$
\mathbf{C} = \mathbf{U}(\mathbf{L} - \sigma^2 \mathbf{I})^{1/2} \mathbf{R} \mathbf{R}^{\mathrm{T}} (\mathbf{L} - \sigma^2 \mathbf{I})^{1/2} \mathbf{U}^{\mathrm{T}} + \sigma^2 \mathbf{I} = \mathbf{U} \mathbf{L} \mathbf{U}^{\mathrm{T}} = \mathbf{S}
$$
 (12.47)

and so we obtain the standard maximum likelihood solution for an unconstrained Gaussian distribution in which the covariance matrix is given by the sample covariance.

Conventional PCA is generally formulated as a projection of points from the *D*dimensional data space onto an M -dimensional linear subspace. Probabilistic PCA, however, is most naturally expressed as a mapping from the latent space into the data space via (12.33). For applications such as visualization and data compression, we can reverse this mapping using Bayes' theorem. Any point x in data space can then be summarized by its posterior mean and covariance in latent space. From (12.42) the mean is given by

$$
\mathbb{E}[\mathbf{z}|\mathbf{x}] = \mathbf{M}^{-1}\mathbf{W}_{\mathrm{ML}}^{\mathrm{T}}(\mathbf{x} - \overline{\mathbf{x}})
$$
 (12.48)

where  $M$  is given by (12.41). This projects to a point in data space given by

$$
\mathbf{W}\mathbb{E}[\mathbf{z}|\mathbf{x}] + \boldsymbol{\mu}.\tag{12.49}
$$

*Section 3.3.1* Note that this takes the same form as the equations for regularized linear regression and is a consequence of maximizing the likelihood function for a linear Gaussian model. Similarly, the posterior covariance is given from (12.42) by  $\sigma^2 M^{-1}$  and is independent of x.

If we take the limit  $\sigma^2 \rightarrow 0$ , then the posterior mean reduces to

$$
(\mathbf{W}_{\mathrm{ML}}^{\mathrm{T}} \mathbf{W}_{\mathrm{ML}})^{-1} \mathbf{W}_{\mathrm{ML}}^{\mathrm{T}} (\mathbf{x} - \overline{\mathbf{x}})
$$
 (12.50)

*Exercise 12.11 Exercise 12.12* which represents an orthogonal projection of the data point onto the latent space, and so we recover the standard PCA model. The posterior covariance in this limit is zero, however, and the density becomes singular. For  $\sigma^2 > 0$ , the latent projection is shifted towards the origin, relative to the orthogonal projection.

Finally, we note that an important role for the probabilistic PCA model is in defining a multivariate Gaussian distribution in which the number of degrees of freedom, in other words the number of independent parameters, can be controlled whilst still allowing the model to capture the dominant correlations in the data. Recall that a general Gaussian distribution has  $D(D + 1)/2$  independent parameters in its covariance matrix (plus another *D* parameters in its mean). Thus the number of parameters scales quadratically with *D* and can become excessive in spaces of high

dimensionality. If we restrict the covariance matrix to be diagonal, then it has only  $D$ independent parameters, and so the number of parameters now grows linearly with dimensionality. However, it now treats the variables as if they were independent and hence can no longer express any correlations between them. Probabilistic PCA provides an elegant compromise in which the *M* most significant correlations can be captured while still ensuring that the total number of parameters grows only linearly with  $D$ . We can see this by evaluating the number of degrees of freedom in the PPCA model as follows. The covariance matrix  $C$  depends on the parameters  $W$ , which has size  $D \times M$ , and  $\sigma^2$ , giving a total parameter count of  $DM + 1$ . However, we have seen that there is some redundancy in this parameterization associated with rotations of the coordinate system in the latent space. The orthogonal matrix  **that** expresses these rotations has size  $M \times M$ . In the first column of this matrix there are  $M - 1$  independent parameters, because the column vector must be normalized to unit length. In the second column there are  $M - 2$  independent parameters, because the column must be normalized and also must be orthogonal to the previous column, and so on. Summing this arithmetic series, we see that **R** has a total of  $M(M-1)/2$ independent parameters. Thus the number of degrees of freedom in the covariance matrix C is given by

$$
DM + 1 - M(M - 1)/2.
$$
 (12.51)

The number of independent parameters in this model therefore only grows linearly with D, for fixed M. If we take  $M = D - 1$ , then we recover the standard result for a full covariance Gaussian. In this case, the variance along  $D - 1$  linearly independent directions is controlled by the columns of W, and the variance along the remaining direction is given by  $\sigma^2$ . If  $M = 0$ , the model is equivalent to the isotropic covariance case.

### **12.2.2 EM algorithm for peA**

As we have seen, the probabilistic PCA model can be expressed in terms of a marginalization over a continuous latent space  $z$  in which for each data point  $x_n$ , there is a corresponding latent variable  $z_n$ . We can therefore make use of the EM algorithm to find maximum likelihood estimates of the model parameters. This may seem rather pointless because we have already obtained an exact closed-form solution for the maximum likelihood parameter values. However, in spaces of high dimensionality, there may be computational advantages in using an iterative EM procedure rather than working directly with the sample covariance matrix. This EM procedure can also be extended to the factor analysis model, for which there is no closed-form solution. Finally, it allows missing data to be handled in a principled way.

*Section 9.4* We can derive the EM algorithm for probabilistic PCA by following the general framework for EM. Thus we write down the complete-data log likelihood and take its expectation with respect to the posterior distribution of the latent distribution evaluated using 'old' parameter values. Maximization of this expected completedata log likelihood then yields the 'new' parameter values. Because the data points

*Exercise 12.14*

*Section 12.2.4*

are assumed independent, the complete-data log likelihood function takes the form

$$
\ln p\left(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \mathbf{W}, \sigma^2\right) = \sum_{n=1}^N \left\{ \ln p(\mathbf{x}_n | \mathbf{z}_n) + \ln p(\mathbf{z}_n) \right\}
$$
(12.52)

where the  $n<sup>th</sup>$  row of the matrix **Z** is given by  $z_n$ . We already know that the exact maximum likelihood solution for  $\mu$  is given by the sample mean  $\bar{x}$  defined by (12.1), and it is convenient to substitute for  $\mu$  at this stage. Making use of the expressions (12.31) and (12.32) for the latent and conditional distributions, respectively, and taking the expectation with respect to the posterior distribution over the latent variables, we obtain

$$
\mathbb{E}[\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \mathbf{W}, \sigma^2)] = -\sum_{n=1}^N \left\{ \frac{D}{2} \ln(2\pi\sigma^2) + \frac{1}{2} \text{Tr}(\mathbb{E}[\mathbf{z}_n \mathbf{z}_n^{\mathrm{T}}]) + \frac{1}{2\sigma^2} ||\mathbf{x}_n - \boldsymbol{\mu}||^2 - \frac{1}{\sigma^2} \mathbb{E}[\mathbf{z}_n]^{\mathrm{T}} \mathbf{W}^{\mathrm{T}} (\mathbf{x}_n - \boldsymbol{\mu}) + \frac{1}{2\sigma^2} \text{Tr}(\mathbb{E}[\mathbf{z}_n \mathbf{z}_n^{\mathrm{T}}] \mathbf{W}^{\mathrm{T}} \mathbf{W}) \right\}. (12.53)
$$

Note that this depends on the posterior distribution only through the sufficient statistics of the Gaussian. Thus in the E step, we use the old parameter values to evaluate

$$
\mathbb{E}[\mathbf{z}_n] = \mathbf{M}^{-1} \mathbf{W}^{\mathrm{T}} (\mathbf{x}_n - \overline{\mathbf{x}})
$$
 (12.54)

$$
\mathbb{E}[\mathbf{z}_n \mathbf{z}_n^{\mathrm{T}}] = \sigma^2 \mathbf{M}^{-1} + \mathbb{E}[\mathbf{z}_n] \mathbb{E}[\mathbf{z}_n]^{\mathrm{T}}
$$
(12.55)

which follow directly from the posterior distribution (12.42) together with the standard result  $\mathbb{E}[z_n z_n^{\mathrm{T}}] = \text{cov}[z_n] + \mathbb{E}[z_n] \mathbb{E}[z_n]^{\mathrm{T}}$ . Here M is defined by (12.41).

In the M step, we maximize with respect to W and  $\sigma^2$ , keeping the posterior statistics fixed. Maximization with respect to  $\sigma^2$  is straightforward. For the maximization with respect to W we make use of  $(C.24)$ , and obtain the M-step equations

$$
\mathbf{W}_{\text{new}} = \left[ \sum_{n=1}^{N} (\mathbf{x}_{n} - \overline{\mathbf{x}}) \mathbb{E}[\mathbf{z}_{n}]^{\text{T}} \right] \left[ \sum_{n=1}^{N} \mathbb{E}[\mathbf{z}_{n} \mathbf{z}_{n}^{\text{T}}] \right]^{-1} \tag{12.56}
$$

$$
\sigma_{\text{new}}^2 = \frac{1}{ND} \sum_{n=1}^N \left\{ ||\mathbf{x}_n - \overline{\mathbf{x}}||^2 - 2\mathbb{E}[\mathbf{z}_n]^T \mathbf{W}_{\text{new}}^T (\mathbf{x}_n - \overline{\mathbf{x}}) \right. \\ \left. + \text{Tr} \left( \mathbb{E}[\mathbf{z}_n \mathbf{z}_n^T] \mathbf{W}_{\text{new}}^T \mathbf{W}_{\text{new}} \right) \right\} . \tag{12.57}
$$

The EM algorithm for probabilistic PCA proceeds by initializing the parameters and then alternately computing the sufficient statistics of the latent space posterior distribution using (12.54) and (12.55) in the E step and revising the parameter values using (12.56) and (12.57) in the M step.

One of the benefits of the EM algorithm for PCA is computational efficiency for large-scale applications (Roweis, 1998). Unlike conventional PCA based on an

*Exercise 12.15*