{"table_of_contents": [{"title": "27 Latent variable models for discrete data", "heading_level": null, "page_id": 0, "polygon": [[54.0, 93.814453125], [423.0, 93.814453125], [421.5, 154.5], [54.0, 144.9140625]]}, {"title": "27.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.0, 207.0], [195.75, 207.0], [195.75, 217.845703125], [96.0, 217.845703125]]}, {"title": "27.2 Distributed state LVMs for discrete data", "heading_level": null, "page_id": 1, "polygon": [[93.0234375, 60.75], [334.5, 60.75], [334.5, 71.9033203125], [93.0234375, 71.9033203125]]}, {"title": "27.2.1 Mixture models", "heading_level": null, "page_id": 1, "polygon": [[89.25, 134.25], [204.0, 134.25], [204.0, 144.28125], [89.25, 144.28125]]}, {"title": "27.2.2 Exponential family PCA", "heading_level": null, "page_id": 2, "polygon": [[87.75, 60.75], [242.25, 60.75], [242.25, 71.86376953125], [87.75, 71.86376953125]]}, {"title": "27.2.3 LDA and mPCA", "heading_level": null, "page_id": 3, "polygon": [[86.765625, 278.25], [202.921875, 278.25], [202.921875, 288.404296875], [86.765625, 288.404296875]]}, {"title": "27.2.4 GaP model and non-negative matrix factorization", "heading_level": null, "page_id": 4, "polygon": [[87.75, 240.0], [364.21875, 240.0], [364.21875, 250.91015625], [87.75, 250.91015625]]}, {"title": "27.3 Latent Dirichlet allocation (LDA)", "heading_level": null, "page_id": 5, "polygon": [[93.0, 303.0], [297.84375, 303.0], [297.84375, 314.033203125], [93.0, 314.033203125]]}, {"title": "27.3.1 Basics", "heading_level": null, "page_id": 5, "polygon": [[89.25, 363.75], [159.75, 363.75], [159.75, 373.67578125], [89.25, 373.67578125]]}, {"title": "Document #29795", "heading_level": null, "page_id": 7, "polygon": [[165.9375, 317.25], [219.75, 317.25], [219.75, 324.94921875], [165.9375, 324.94921875]]}, {"title": "Document #1883", "heading_level": null, "page_id": 7, "polygon": [[163.265625, 381.75], [216.0, 381.75], [216.0, 389.49609375], [163.265625, 389.49609375]]}, {"title": "Document #21359", "heading_level": null, "page_id": 7, "polygon": [[165.75, 453.41015625], [219.75, 453.41015625], [219.75, 460.5], [165.75, 460.5]]}, {"title": "27.3.2 Unsupervised discovery of topics", "heading_level": null, "page_id": 8, "polygon": [[87.75, 135.75], [286.734375, 135.75], [286.734375, 146.1796875], [87.75, 146.1796875]]}, {"title": "27.3.3 Quantitatively evaluating LDA as a language model", "heading_level": null, "page_id": 8, "polygon": [[87.2578125, 240.0], [371.25, 240.0], [371.25, 250.91015625], [87.2578125, 250.91015625]]}, {"title": "27.3.3.1 Perplexity", "heading_level": null, "page_id": 8, "polygon": [[84.0, 344.25], [175.21875, 344.25], [175.21875, 354.69140625], [84.0, 354.69140625]]}, {"title": "27.3.3.2 Perplexity of LDA", "heading_level": null, "page_id": 9, "polygon": [[82.5, 411.75], [206.25, 411.75], [206.25, 422.0859375], [82.5, 422.0859375]]}, {"title": "27.3.4 Fitting using (collapsed) Gibbs sampling", "heading_level": null, "page_id": 10, "polygon": [[87.0, 465.75], [321.0, 465.75], [321.0, 476.82421875], [87.0, 476.82421875]]}, {"title": "27.3.5 Example", "heading_level": null, "page_id": 11, "polygon": [[87.328125, 559.5], [171.984375, 559.5], [171.984375, 569.53125], [87.328125, 569.53125]]}, {"title": "27.3.6 Fitting using batch variational inference", "heading_level": null, "page_id": 12, "polygon": [[86.6953125, 394.5], [321.0, 394.5], [321.0, 404.68359375], [86.6953125, 404.68359375]]}, {"title": "27.3.6.1 Sequence version", "heading_level": null, "page_id": 12, "polygon": [[83.953125, 450.0], [207.421875, 450.0], [207.421875, 459.421875], [83.953125, 459.421875]]}, {"title": "27.3.6.2 Count version", "heading_level": null, "page_id": 13, "polygon": [[81.75, 186.75], [191.25, 186.75], [191.25, 196.962890625], [81.75, 196.962890625]]}, {"title": "27.3.6.3 VB version", "heading_level": null, "page_id": 13, "polygon": [[81.75, 436.5], [177.046875, 436.5], [177.046875, 446.44921875], [81.75, 446.44921875]]}, {"title": "27.3.7 Fitting using online variational inference", "heading_level": null, "page_id": 14, "polygon": [[87.75, 495.75], [324.0, 495.75], [324.0, 506.25], [87.75, 506.25]]}, {"title": "27.3.8 Determining the number of topics", "heading_level": null, "page_id": 15, "polygon": [[87.328125, 507.75], [291.75, 507.75], [291.75, 518.2734375], [87.328125, 518.2734375]]}, {"title": "27.4 Extensions of LDA", "heading_level": null, "page_id": 16, "polygon": [[93.75, 102.75], [225.0, 102.75], [225.0, 113.748046875], [93.75, 113.748046875]]}, {"title": "27.4.1 Correlated topic model", "heading_level": null, "page_id": 16, "polygon": [[89.25, 163.5], [240.0, 163.5], [240.0, 173.865234375], [89.25, 173.865234375]]}, {"title": "27.4.2 Dynamic topic model", "heading_level": null, "page_id": 17, "polygon": [[87.75, 404.25], [231.75, 404.25], [231.75, 414.4921875], [87.75, 414.4921875]]}, {"title": "27.4.3 LDA-HMM", "heading_level": null, "page_id": 18, "polygon": [[87.75, 509.25], [178.59375, 509.25], [178.59375, 519.22265625], [87.75, 519.22265625]]}, {"title": "27.4.4 Supervised LDA", "heading_level": null, "page_id": 22, "polygon": [[86.765625, 294.0], [205.171875, 294.0], [205.171875, 305.015625], [86.765625, 305.015625]]}, {"title": "27.4.4.1 Generative supervised LDA", "heading_level": null, "page_id": 22, "polygon": [[84.0, 351.75], [246.375, 351.75], [246.375, 361.3359375], [84.0, 361.3359375]]}, {"title": "27.4.4.2 Discriminative supervised LDA", "heading_level": null, "page_id": 23, "polygon": [[82.5, 324.75], [262.5, 324.75], [262.5, 334.44140625], [82.5, 334.44140625]]}, {"title": "27.4.4.3 Discriminative categorical PCA", "heading_level": null, "page_id": 24, "polygon": [[82.5, 471.75], [263.25, 471.75], [263.25, 480.62109375], [82.5, 480.62109375]]}, {"title": "27.5 LVMs for graph-structured data", "heading_level": null, "page_id": 25, "polygon": [[93.75, 462.0], [291.9375, 462.0], [291.9375, 473.66015625], [93.75, 473.66015625]]}, {"title": "27.5.1 Stochastic block model", "heading_level": null, "page_id": 26, "polygon": [[88.6640625, 531.75], [238.78125, 531.75], [238.78125, 542.3203125], [88.6640625, 542.3203125]]}, {"title": "27.5.2 Mixed membership stochastic block model", "heading_level": null, "page_id": 28, "polygon": [[87.1171875, 393.0], [332.25, 393.0], [332.25, 403.1015625], [87.1171875, 403.1015625]]}, {"title": "27.5.3 Relational topic model", "heading_level": null, "page_id": 29, "polygon": [[87.2578125, 522.75], [237.75, 523.5], [237.75, 534.0], [87.2578125, 533.14453125]]}, {"title": "27.6 LVMs for relational data", "heading_level": null, "page_id": 30, "polygon": [[93.0, 492.75], [253.5, 492.75], [253.5, 503.71875], [93.0, 503.71875]]}, {"title": "27.6.1 Infinite relational model", "heading_level": null, "page_id": 31, "polygon": [[89.25, 473.25], [246.0, 472.5], [246.0, 483.46875], [89.25, 483.46875]]}, {"title": "******** Learning ontologies", "heading_level": null, "page_id": 32, "polygon": [[85.4296875, 379.5], [216.75, 379.5], [216.75, 389.8125], [85.4296875, 389.8125]]}, {"title": "******** Clustering based on relations and features", "heading_level": null, "page_id": 32, "polygon": [[82.265625, 556.5], [314.25, 556.5], [314.25, 566.05078125], [82.265625, 566.05078125]]}, {"title": "27.6.2 Probabilistic matrix factorization for collaborative filtering", "heading_level": null, "page_id": 34, "polygon": [[86.484375, 61.5], [407.25, 61.5], [407.25, 71.78466796875], [86.484375, 71.78466796875]]}, {"title": "27.7 Restricted Boltzmann machines (RBMs)", "heading_level": null, "page_id": 38, "polygon": [[94.5, 380.25], [332.25, 380.25], [332.25, 391.39453125], [94.5, 391.39453125]]}, {"title": "27.7.1 Varieties of RBMs", "heading_level": null, "page_id": 40, "polygon": [[88.3125, 210.75], [214.59375, 210.75], [214.59375, 221.326171875], [88.3125, 221.326171875]]}, {"title": "27.7.1.1 Binary RBMs", "heading_level": null, "page_id": 40, "polygon": [[85.1484375, 279.75], [186.1875, 279.75], [186.1875, 289.828125], [85.1484375, 289.828125]]}, {"title": "27.7.1.2 Categorical RBM", "heading_level": null, "page_id": 41, "polygon": [[83.390625, 215.25], [202.078125, 215.25], [202.078125, 224.173828125], [83.390625, 224.173828125]]}, {"title": "27.7.1.3 Gaussian RBM", "heading_level": null, "page_id": 41, "polygon": [[83.953125, 396.75], [192.0, 396.75], [192.0, 406.8984375], [83.953125, 406.8984375]]}, {"title": "27.7.1.4 RBMs with Gaussian hidden units", "heading_level": null, "page_id": 42, "polygon": [[84.0, 194.25], [276.0, 194.25], [276.0, 204.3984375], [84.0, 204.3984375]]}, {"title": "27.7.2 Learning RBMs", "heading_level": null, "page_id": 42, "polygon": [[87.2578125, 311.25], [202.21875, 311.25], [202.21875, 321.310546875], [87.2578125, 321.310546875]]}, {"title": "27.7.2.1 Deriving the gradient using p(h, v | \\theta)", "heading_level": null, "page_id": 42, "polygon": [[83.7421875, 403.5], [294.1875, 403.5], [294.1875, 412.91015625], [83.7421875, 412.91015625]]}, {"title": "27.7.2.2 Deriving the gradient using p(v|\\theta)", "heading_level": null, "page_id": 43, "polygon": [[81.75, 123.0], [283.5, 123.0], [283.5, 133.365234375], [81.75, 133.365234375]]}, {"title": "27.7.2.3 Approximating the expectations", "heading_level": null, "page_id": 44, "polygon": [[82.5, 222.75], [268.5, 222.75], [268.5, 232.083984375], [82.5, 232.083984375]]}, {"title": "27.7.2.4 Contrastive divergence", "heading_level": null, "page_id": 44, "polygon": [[82.96875, 421.5], [230.0625, 421.5], [230.0625, 431.578125], [82.96875, 431.578125]]}, {"title": "27.7.2.5 Persistent CD", "heading_level": null, "page_id": 45, "polygon": [[82.5, 558.75], [188.25, 558.75], [188.25, 567.94921875], [82.5, 567.94921875]]}, {"title": "27.7.3 Applications of RBMs", "heading_level": null, "page_id": 46, "polygon": [[87.75, 380.25], [231.0, 380.25], [231.0, 390.4453125], [87.75, 390.4453125]]}, {"title": "27.7.3.1 Language modeling and document retrieval", "heading_level": null, "page_id": 46, "polygon": [[84.0, 461.25], [318.75, 461.25], [318.75, 470.8125], [84.0, 470.8125]]}, {"title": "27.7.3.2 RBMs for collaborative filtering", "heading_level": null, "page_id": 48, "polygon": [[82.1953125, 60.75], [265.5, 60.75], [265.5, 71.78466796875], [82.1953125, 71.78466796875]]}, {"title": "Exercises", "heading_level": null, "page_id": 48, "polygon": [[129.0, 143.25], [179.25, 143.25], [179.25, 154.72265625], [129.0, 154.72265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 33], ["TextInlineMath", 4], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9792, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 47], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 50], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 42], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 53], ["Text", 9], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 41], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 45], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["TableCell", 228], ["Line", 40], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9908, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 45], ["Text", 7], ["SectionHeader", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 64], ["Text", 7], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 57], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1803, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 73], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1978, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 72], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 49], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 38], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 38], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 603], ["Line", 202], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1225, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 47], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["Line", 179], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1213, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 29], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 917, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 540], ["Span", 151], ["Line", 53], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7783, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 47], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 51], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 47], ["Text", 7], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 73], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 815, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 45], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 776, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 67], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 865, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 39], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 826, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 65], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 993, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 113], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1282, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 416], ["Line", 51], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 83], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 86], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2145, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 59], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 49], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 62], ["TableCell", 56], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1225, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 81], ["Equation", 7], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 53], ["Text", 8], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 107], ["Equation", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 42], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 785, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 49], ["TableCell", 32], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Table", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 54], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["TableCell", 69], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Text", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6094, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 55], ["Line", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-31"}