{"table_of_contents": [{"title": "28 Deep learning", "heading_level": null, "page_id": 0, "polygon": [[51.2578125, 94.60546875], [237.0, 94.60546875], [234.75, 147.0], [51.0, 143.6484375]]}, {"title": "28.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[95.25, 207.0], [195.75, 207.0], [195.75, 218.00390625], [95.25, 218.00390625]]}, {"title": "28.2 Deep generative models", "heading_level": null, "page_id": 0, "polygon": [[93.0, 425.25], [255.09375, 425.25], [255.09375, 436.32421875], [93.0, 436.32421875]]}, {"title": "28.2.1 Deep directed networks", "heading_level": null, "page_id": 1, "polygon": [[87.46875, 216.0], [242.25, 216.0], [242.25, 225.755859375], [87.46875, 225.755859375]]}, {"title": "28.2.2 Deep Boltzmann machines", "heading_level": null, "page_id": 1, "polygon": [[86.2734375, 494.25], [255.75, 494.25], [255.75, 504.3515625], [86.2734375, 504.3515625]]}, {"title": "28.2.3 Deep belief networks", "heading_level": null, "page_id": 2, "polygon": [[86.25, 160.5], [230.25, 160.5], [230.25, 170.701171875], [86.25, 170.701171875]]}, {"title": "28.2.4 Greedy layer-wise learning of DBNs", "heading_level": null, "page_id": 3, "polygon": [[86.5546875, 253.5], [297.75, 253.5], [297.75, 263.724609375], [86.5546875, 263.724609375]]}, {"title": "28.3 Deep neural networks", "heading_level": null, "page_id": 4, "polygon": [[93.0, 60.75], [245.25, 60.75], [245.25, 71.78466796875], [93.0, 71.78466796875]]}, {"title": "28.3.1 Deep multi-layer perceptrons", "heading_level": null, "page_id": 4, "polygon": [[88.1015625, 301.5], [268.5, 301.5], [268.5, 311.9765625], [88.1015625, 311.9765625]]}, {"title": "28.3.2 Deep auto-encoders", "heading_level": null, "page_id": 5, "polygon": [[86.9765625, 347.25], [224.25, 347.25], [224.25, 357.5390625], [86.9765625, 357.5390625]]}, {"title": "28.3.3 Stacked denoising auto-encoders", "heading_level": null, "page_id": 6, "polygon": [[87.0, 320.25], [284.25, 320.25], [284.25, 330.328125], [87.0, 330.328125]]}, {"title": "28.4 Applications of deep networks", "heading_level": null, "page_id": 6, "polygon": [[92.4609375, 498.65625], [288.0, 498.65625], [288.0, 509.4140625], [92.4609375, 509.4140625]]}, {"title": "28.4.1 Handwritten digit classification using DBNs", "heading_level": null, "page_id": 6, "polygon": [[88.59375, 547.5], [336.0, 546.75], [336.0, 557.82421875], [88.59375, 557.82421875]]}, {"title": "28.4.2 Data visualization and feature discovery using deep auto-encoders", "heading_level": null, "page_id": 7, "polygon": [[86.625, 483.75], [444.75, 483.75], [444.75, 493.59375], [86.625, 493.59375]]}, {"title": "28.4.3 Information retrieval using deep auto-encoders (semantic hashing)", "heading_level": null, "page_id": 8, "polygon": [[87.046875, 351.0], [445.5, 351.0], [445.5, 361.01953125], [87.046875, 361.01953125]]}, {"title": "28.4.4 Learning audio features using 1d convolutional DBNs", "heading_level": null, "page_id": 9, "polygon": [[87.75, 386.25], [381.0, 386.25], [381.0, 395.82421875], [87.75, 395.82421875]]}, {"title": "28.4.5 Learning image features using 2d convolutional DBNs", "heading_level": null, "page_id": 10, "polygon": [[87.75, 394.5], [384.0, 394.5], [384.0, 404.68359375], [87.75, 404.68359375]]}, {"title": "28.5 Discussion", "heading_level": null, "page_id": 10, "polygon": [[93.0, 525.75], [186.75, 525.75], [186.75, 535.9921875], [93.0, 535.9921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 30], ["Text", 5], ["SectionHeader", 3], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 52], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1751, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 50], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1162, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 34], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 39], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 75], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 818, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Line", 119], ["Span", 86], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 808, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 723, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 50], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 43], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 42], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2], ["Footnote", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 30], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1280, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 53], ["Line", 21], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-32"}