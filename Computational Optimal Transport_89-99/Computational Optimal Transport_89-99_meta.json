{"table_of_contents": [{"title": "5", "heading_level": null, "page_id": 0, "polygon": [[303.1236230110159, 135.760009765625], [320.8671875, 135.760009765625], [320.8671875, 161.4443359375], [303.1236230110159, 161.4443359375]]}, {"title": "Semidiscrete Optimal Transport", "heading_level": null, "page_id": 0, "polygon": [[201.08200734394123, 180.69289099526065], [420.92166462668297, 180.69289099526065], [420.92166462668297, 195.9154052734375], [201.08200734394123, 195.9154052734375]]}, {"title": "5.1 c-Transform and c¯-Transform", "heading_level": null, "page_id": 0, "polygon": [[104.29253365973072, 620.0540284360189], [296.3708690330477, 620.0540284360189], [296.3708690330477, 631.48681640625], [104.29253365973072, 631.48681640625]]}, {"title": "5.2 Semidiscrete Formulation", "heading_level": null, "page_id": 2, "polygon": [[104.29253365973072, 184.44170616113743], [274.6119951040392, 184.44170616113743], [274.6119951040392, 195.7222900390625], [104.29253365973072, 195.7222900390625]]}, {"title": "5.3 Entropic Semidiscrete Formulation", "heading_level": null, "page_id": 4, "polygon": [[104.29253365973072, 466.95263671875], [324.88249694002445, 466.95263671875], [324.88249694002445, 478.53955078125], [104.29253365973072, 478.53955078125]]}, {"title": "5.4 Stochastic Optimization Methods", "heading_level": null, "page_id": 7, "polygon": [[104.162109375, 476.84928909952606], [321.1309669522644, 476.84928909952606], [321.1309669522644, 488.1953125], [104.162109375, 488.1953125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 32], ["SectionHeader", 3], ["Text", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1950, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 70], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 532], ["Line", 62], ["TextInlineMath", 6], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 57], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 80], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 75], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 71], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_89-99"}