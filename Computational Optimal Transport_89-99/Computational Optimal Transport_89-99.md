# **5**

## **Semidiscrete Optimal Transport**

This chapter studies methods to tackle the optimal transport problem when one of the two input measures is discrete (a sum of Dirac masses) and the other one is arbitrary, including notably the case where it has a density with respect to the Lebesgue measure. When the ambient space has low dimension, this problem has a strong geometrical flavor because one can show that the optimal transport from a continuous density toward a discrete one is a piecewise constant map, where the preimage of each point in the support of the discrete measure is a union of disjoint cells. When the cost is the squared Euclidean distance, these cells correspond to an important concept from computational geometry, the so-called <PERSON><PERSON><PERSON> cells, which are Voronoi cells offset by a constant. This connection allows us to borrow tools from computational geometry to obtain fast computational schemes. In high dimensions, the semidescrete formulation can also be interpreted as a stochastic programming problem, which can also benefit from a bit of regularization, extending therefore the scope of applications of the entropic regularization scheme presented in Chapter 4. All these constructions rely heavily on the notion of the *c*-transform, this time for general cost functions and not only matrices as in §3.2. The *c*-transform is a generalization of the Legendre transform from convex analysis and plays a pivotal role in the theory and algorithms for OT.

## **5.1** *c***-Transform and** *c***-Transform**

Recall that the dual OT problem (2.24) reads

$$
\sup_{(f,g)} \mathcal{E}(f,g) \stackrel{\text{def.}}{=} \int_{\mathcal{X}} f(x) \mathrm{d}\alpha(x) + \int_{\mathcal{Y}} g(y) \mathrm{d}\beta(y) + \iota_{\mathcal{R}(c)}(f,g),
$$

where we used the useful indicator function notation  $(4.50)$ . Keeping either dual potential *f* or *g* fixed and optimizing w.r.t. *g* or *f*, respectively, leads to closed form solutions that provide the definition of the *c*-transform:

$$
\forall y \in \mathcal{Y}, \quad f^c(y) \stackrel{\text{def.}}{=} \inf_{x \in \mathcal{X}} c(x, y) - f(x), \tag{5.1}
$$

$$
\forall x \in \mathcal{X}, \quad g^{\bar{c}}(x) \stackrel{\text{def.}}{=} \inf_{y \in \mathcal{Y}} c(x, y) - g(y), \tag{5.2}
$$

where we denoted  $\bar{c}(y, x) \stackrel{\text{def.}}{=} c(x, y)$ . Indeed, one can check that

$$
f^c \in \underset{g}{\text{argmax}} \ \mathcal{E}(f,g) \quad \text{and} \quad g^{\bar{c}} \in \underset{f}{\text{argmax}} \ \mathcal{E}(f,g). \tag{5.3}
$$

Note that these partial minimizations define maximizers on the support of respectively *α* and *β*, while the definitions (5.1) actually define functions on the whole spaces  $X$ and  $\mathcal Y$ . This is thus a way to extend in a canonical way solutions of  $(2.24)$  on the whole spaces. When  $\mathcal{X} = \mathbb{R}^d$  and  $c(x, y) = ||x - y||_2^p = \left(\sum_{i=1}^d |x_i - y_i|\right)^{p/2}$ , then the *c*transform (5.1)  $f^c$  is the so-called inf-convolution between  $-f$  and  $\|\cdot\|^p$ . The definition of *f c* is also often referred to as a "Hopf–Lax formula."

The map  $(f, g) \in C(\mathcal{X}) \times C(\mathcal{Y}) \mapsto (g^{\bar{c}}, f^c) \in C(\mathcal{X}) \times C(\mathcal{Y})$  replaces dual potentials by "better" ones (improving the dual objective  $\mathcal{E}$ ). Functions that can be written in the form  $f^c$  and  $g^{\bar{c}}$  are called *c*-concave and  $\bar{c}$ -concave functions. In the special case  $c(x, y) = \langle x, y \rangle$  in  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^d$ , this definition coincides with the usual notion of concave functions. Extending naturally Proposition 3.1 to a continuous case, one has the property that

$$
f^{c\bar{c}c} = f^c \quad \text{and} \quad g^{\bar{c}c\bar{c}} = g^{\bar{c}},
$$

where we denoted  $f^{c\bar{c}} = (f^c)^{\bar{c}}$ . This invariance property shows that one can "improve" only once the dual potential this way. Alternatively, this means that alternate maximization does not converge (it immediately enters a cycle), which is classical for functionals involving a nonsmooth (a constraint) coupling of the optimized variables. This is in sharp contrast with entropic regularization of OT as shown in Chapter 4. In this case, because of the regularization, the dual objective (4.30) is smooth, and alternate maximization corresponds to Sinkhorn iterations (4.43) and (4.44). These iterates, written over the dual variables, define entropically smoothed versions of the *c*-transform, where min operations are replaced by a "soft-min."

Using (5.3), one can reformulate (2.24) as an unconstrained convex program over a single potential,

$$
\mathcal{L}_c(\alpha, \beta) = \sup_{f \in \mathcal{C}(\mathcal{X})} \int_{\mathcal{X}} f(x) \mathrm{d}\alpha(x) + \int_{\mathcal{Y}} f^c(y) \mathrm{d}\beta(y) \tag{5.4}
$$

$$
= \sup_{g \in \mathcal{C}(\mathcal{Y})} \int_{\mathcal{X}} g^{\bar{c}}(x) d\alpha(x) + \int_{\mathcal{Y}} g(y) d\beta(y). \tag{5.5}
$$

Since one can iterate the map  $(f, g) \mapsto (g^{\bar{c}}, f^c)$ , it is possible to add the constraint that f is  $\bar{c}$ -concave and  $q$  is  $c$ -concave, which is important to ensure enough regularity on these potentials and show, for instance, existence of solutions to (2.24).

## **5.2 Semidiscrete Formulation**

A case of particular interest is when  $\beta = \sum_j \mathbf{b}_j \delta_{y_j}$  is discrete (of course the same construction applies if  $\alpha$  is discrete by exchanging the role of  $\alpha$ ,  $\beta$ ). One can adapt the definition of the  $\bar{c}$  transform (5.1) to this setting by restricting the minimization to the support  $(y_i)_i$  of  $\beta$ ,

$$
\forall \mathbf{g} \in \mathbb{R}^m, \ \forall \, x \in \mathcal{X}, \quad \mathbf{g}^{\bar{c}}(x) \stackrel{\text{def.}}{=} \min_{j \in [m]} c(x, y_j) - \mathbf{g}_j. \tag{5.6}
$$

This transform maps a vector **g** to a continuous function  $\mathbf{g}^{\bar{c}} \in C(\mathcal{X})$ . Note that this definition coincides with  $(5.1)$  when imposing that the space X is equal to the support of *β*. Figure 5.1 shows some examples of such discrete *c*¯-transforms in one and two dimensions.

Crucially, using the discrete  $\bar{c}$ -transform in the semidiscrete problem (5.4) yields a finite-dimensional optimization,

$$
\mathcal{L}_c(\alpha, \beta) = \max_{\mathbf{g} \in \mathbb{R}^m} \mathcal{E}(\mathbf{g}) \stackrel{\text{def.}}{=} \int_{\mathcal{X}} \mathbf{g}^{\bar{c}}(x) d\alpha(x) + \sum \mathbf{g}_y \mathbf{b}_j.
$$
 (5.7)

The Laguerre cells associated to the dual weights **g**

$$
\mathbb{L}_j(\mathbf{g}) \stackrel{\text{def.}}{=} \left\{ x \in \mathcal{X} \; : \; \forall j' \neq j, c(x, y_j) - \mathbf{g}_j \leq c(x, y_{j'}) - \mathbf{g}_{j'} \right\}
$$

induce a disjoint decomposition of  $\mathcal{X} = \bigcup_j \mathbb{L}_j(g)$ . When **g** is constant, the Laguerre cells decomposition corresponds to the Voronoi diagram partition of the space. Figure 5.1, bottom row, shows examples of Laguerre cells segmentations in two dimensions.

This allows one to conveniently rewrite the minimized energy as

$$
\mathcal{E}(\mathbf{g}) = \sum_{j=1}^{m} \int_{\mathbb{L}_j(\mathbf{g})} \left( c(x, y_j) - \mathbf{g}_j \right) d\alpha(x) + \langle \mathbf{g}, \mathbf{b} \rangle.
$$
 (5.8)

The gradient of this function can be computed as follows:

$$
\forall j \in [\![m]\!], \quad \nabla \mathcal{E}(\mathbf{g})_j = -\int_{\mathbb{L}_j(\mathbf{g})} d\alpha(x) + \mathbf{b}_j.
$$

Figure 5.2 displays iterations of a gradient descent to minimize  $\mathcal{E}$ . Once the optimal **g** is computed, then the optimal transport map *T* from  $\alpha$  to  $\beta$  is mapping any  $x \in L_i(\mathbf{g})$ toward  $y_j$ , so it is piecewise constant.

In the special case  $c(x, y) = ||x - y||^2$ , the decomposition in Laguerre cells is also known as a "power diagram." The cells are polyhedral and can be computed efficiently

Image /page/3/Figure/1 description: The image displays a figure with two rows of plots. The top row shows a line graph with multiple blue and green lines, representing a function over a horizontal axis labeled from 0 to 1. Several red dots are plotted on the graph. To the right of the line graph, a color bar indicates a scale from 0.5 to 2, with colors transitioning from blue to green. The bottom row contains four smaller plots, each labeled with a value of 'p' (p = 1/2, p = 1, p = 3/2, and p = 2). These plots show contour lines and black boundaries, with red dots at the center of some regions. The overall figure appears to illustrate a mathematical concept or simulation.

**Figure 5.1:** Top: examples of semidiscrete  $\bar{c}$ -transforms  $\mathbf{g}^{\bar{c}}$  in one dimension, for ground cost  $c(x, y)$  $|x - y|^p$  for varying *p* (see colorbar). The red points are at locations  $(y_j, -g_j)_j$ . Bottom: examples of semidiscrete  $\bar{c}$ -transforms  $\mathbf{g}^{\bar{c}}$  in two dimensions, for ground cost  $c(x, y) = ||x - y||_2^p = \left(\sum_{i=1}^d |x_i - y_i|\right)^{p/2}$ for varying p. The red points are at locations  $y_j \in \mathbb{R}^2$ , and their size is proportional to  $\mathbf{g}_j$ . The regions delimited by bold black curves are the Laguerre cells  $(\mathbb{L}_j(\mathbf{g}))_j$  associated to these points  $(y_j)_j$ .

using computational geometry algorithms; see [Aurenhammer, 1987]. The most widely used algorithm relies on the fact that the power diagram of points in  $\mathbb{R}^d$  is equal to the projection on  $\mathbb{R}^d$  of the convex hull of the set of points  $((y_j, \|y_j\|^2 - \mathbf{g}_j))_{j=1}^m \subset \mathbb{R}^{d+1}$ . There are numerous algorithms to compute convex hulls; for instance, that of Chan [1996] in two and three dimensions has complexity  $O(m \log(Q))$ , where Q is the number of vertices of the convex hull.

The initial idea of a semidiscrete solver for Monge–Ampère equations was proposed by Oliker and Prussner [1989], and its relation to the dual variational problem was shown by Aurenhammer et al. [1998]. A theoretical analysis and its application to the reflector problem in optics is detailed in [Caffarelli et al., 1999]. The semidiscrete formulation was used in [Carlier et al., 2010] in conjunction with a continuation approach based on Knothe's transport. The recent revival of this methods in various fields is due to Mérigot [2011], who proposed a quasi-Newton solver and clarified the link with concepts from computational geometry. We refer to [Lévy and Schwindt, 2018] for a recent

Image /page/4/Figure/1 description: The image displays a series of five plots arranged horizontally. The first plot on the left shows a grayscale gradient, darkest at the center and fading outwards, with several small colored dots scattered around it. The remaining four plots are colored Voronoi diagrams, each containing multiple colored dots. These plots are labeled from left to right as 'α and β', 'ℓ = 1', 'ℓ = 20', 'ℓ = 40', and 'ℓ = 100'. The Voronoi diagrams show a partitioning of space into regions, with a colored dot at the center of each region. As the value of ℓ increases from 1 to 100, the Voronoi cells appear to become smaller and more numerous, particularly in the lower left cluster of dots. Some of the colored dots in the Voronoi diagrams are larger than others, suggesting a possible variation in their significance or size.

**Figure 5.2:** Iterations of the semidiscrete OT algorithm minimizing (5.8) (here a simple gradient descent is used). The support  $(y_i)_i$  of the discrete measure  $\beta$  is indicated by the colored points, while the continuous measure  $\alpha$  is the uniform measure on a square. The colored cells display the Laguerre partition  $(\mathbb{L}_j(\mathbf{g}^{(\ell)}))_j$  where  $\mathbf{g}^{(\ell)}$  is the discrete dual potential computed at iteration  $\ell$ .

overview. The use of a Newton solver which is applied to sampling in computer graphics is proposed in [De Goes et al., 2012]; see also [Lévy, 2015] for applications to 3-D volume and surface processing. An important area of application of the semidiscrete method is for the resolution of the incompressible fluid dynamic (Euler's equations) using Lagrangian methods [de Goes et al., 2015, Gallouët and Mérigot, 2017]. The semidiscrete OT solver enforces incompressibility at each iteration by imposing that the (possibly weighted) points cloud approximates a uniform distribution inside the domain. The convergence (with linear rate) of damped Newton iterations is proved in [Mirebeau, 2015] for the Monge–Ampère equation and is refined in [Kitagawa et al., 2016] for optimal transport. Semidiscrete OT finds important applications to illumination design, notably reflectors; see [Meyron et al., 2018].

## **5.3 Entropic Semidiscrete Formulation**

The dual of the entropic regularized problem between arbitrary measures (4.9) is a smooth unconstrained optimization problem:

$$
\mathcal{L}_{c}^{\varepsilon}(\alpha,\beta) = \sup_{(f,g)\in\mathcal{C}(\mathcal{X})\times\mathcal{C}(\mathcal{Y})} \int_{\mathcal{X}} f \,d\alpha + \int_{\mathcal{Y}} g \,d\beta - \varepsilon \int_{\mathcal{X}\times\mathcal{Y}} e^{\frac{-c+f\oplus g}{\varepsilon}} d\alpha d\beta, \tag{5.9}
$$

where we denoted  $(f \oplus g)(x, y) \stackrel{\text{def.}}{=} f(x) + g(y)$ .

Similarly to the unregularized problem (5.1), one can minimize explicitly with respect to either  $f$  or  $g$  in (5.9), which yields a smoothed  $c$ -transform

$$
\forall y \in \mathcal{Y}, \quad f^{c,\varepsilon}(y) \stackrel{\text{def.}}{=} -\varepsilon \log \left( \int_{\mathcal{X}} e^{\frac{-c(x,y) + f(x)}{\varepsilon}} d\alpha(x) \right),
$$
  
$$
\forall x \in \mathcal{X}, \quad g^{\bar{c},\varepsilon}(x) \stackrel{\text{def.}}{=} -\varepsilon \log \left( \int_{\mathcal{Y}} e^{\frac{-c(x,y) + g(y)}{\varepsilon}} d\beta(y) \right).
$$

In the case of a discrete measure  $\beta = \sum_{j=1}^{m} \mathbf{b}_j \delta_{y_j}$ , the problem simplifies as with (5.7) to a finite-dimensional problem expressed as a function of the discrete dual potential 90 Semidiscrete Optimal Transport

 $\mathbf{g} \in \mathbb{R}^m$ ,

$$
\forall x \in \mathcal{X}, \quad \mathbf{g}^{\bar{c},\varepsilon}(x) \stackrel{\text{def.}}{=} -\varepsilon \log \left( \sum_{j=1}^{m} e^{\frac{-c(x,y_j) + \mathbf{g}_j}{\varepsilon}} \mathbf{b}_j \right). \tag{5.10}
$$

One defines similarly  $f^{\bar{c}, \varepsilon}$  in the case of a discrete measure  $\alpha$ . Note that the rewriting (4.40) and (4.41) of Sinkhorn using the soft-min operator min*<sup>ε</sup>* corresponds to the alternate computation of entropic smoothed *c*-transforms,

$$
\mathbf{f}_{i}^{(\ell+1)} = \mathbf{g}^{\bar{c},\varepsilon}(x_i) \quad \text{and} \quad \mathbf{g}_{j}^{(\ell+1)} = \mathbf{f}^{c,\varepsilon}(y_j). \tag{5.11}
$$

Instead of maximizing (5.9), one can thus solve the following finite-dimensional optimization problem:

$$
\max_{\mathbf{g}\in\mathbb{R}^n} \mathcal{E}^{\varepsilon}(\mathbf{g}) \stackrel{\text{def.}}{=} \int_{\mathcal{X}} \mathbf{g}^{\bar{c},\varepsilon}(x) d\alpha(x) + \langle \mathbf{g}, \mathbf{b} \rangle.
$$
 (5.12)

Note that this optimization problem is still valid even in the unregularized case  $\varepsilon = 0$ and in this case  $g^{\bar{c},\varepsilon=0} = g^{\bar{c}}$  is the  $\bar{c}$ -transform defined in (5.6) so that (5.12) is in fact (5.8). The gradient of this functional reads

$$
\forall j \in [\![m]\!], \quad \nabla \mathcal{E}^{\varepsilon}(\mathbf{g})_j = -\int_{\mathcal{X}} \chi_j^{\varepsilon}(x) \mathrm{d}\alpha(x) + \mathbf{b}_j,\tag{5.13}
$$

where  $\chi_j^{\varepsilon}$  is a smoothed version of the indicator  $\chi_j^0$  of the Laguerre cell  $\mathbb{L}_j(\mathbf{g})$ ,

$$
\chi_j^{\varepsilon}(x) = \frac{e^{\frac{-c(x,y_j)+\mathbf{g}_j}{\varepsilon}}}{\sum_{\ell} e^{\frac{-c(x,y_{\ell})+\mathbf{g}_{\ell}}{\varepsilon}}}.
$$

Note once again that this formula (5.13) is still valid for  $\varepsilon = 0$ . Note also that the family of functions  $(\chi_j^{\varepsilon})_j$  is a partition of unity, *i.e.*  $\sum_j \chi_j^{\varepsilon} = 1$  and  $\chi_j^{\varepsilon} \geq 0$ . Figure 5.3, bottom row, illustrates this.

**Remark 5.1** (Second order methods and connection with logistic regression)**.** A crucial aspect of the smoothed semidiscrete formulation (5.12) is that it corresponds to the minimization of a smooth function. Indeed, as shown in [Genevay et al., 2016], the Hessian of  $\mathcal{E}^{\varepsilon}$  is upper bounded by  $1/\varepsilon$ , so that  $\nabla \mathcal{E}^{\varepsilon}$  is  $\frac{1}{\varepsilon}$ -Lipschitz continuous. In fact, that problem is very closely related to a multiclass logistic regression problem (see Figure 5.3 for a display of the resulting fuzzy classification boundary) and enjoys the same favorable properties (see [Hosmer Jr et al., 2013]), which are generalizations of self-concordance; see [Bach, 2010]. In particular, the Newton method converges quadratically, and one can use in practice quasi-Newton techniques, such as L-BFGS, as advocated in [Cuturi and Peyré, 2016]. Note that [Cuturi and Peyré, 2016] studies the more general barycenter problem detailed in §9.2, but it is equivalent to this semidiscrete setting when considering only a pair of input measures. The use of second

90

order schemes (Newton or L-BFGS) is also advocated in the unregularized case  $\varepsilon = 0$ by [Mérigot, 2011, De Goes et al., 2012, Lévy, 2015]. In [Kitagawa et al., 2016, Theo. 5.1, the Hessian of  $\mathcal{E}^0(\mathbf{g})$  is shown to be uniformly bounded as long as the volume of the Laguerre cells is bounded by below and  $\alpha$  has a continuous density. Kitagawa et al. proceed by showing the linear convergence of a damped Newton algorithm with a backtracking to ensure that the Laguerre cells never vanish between two iterations. This result justifies the use of second order methods even in the unregularized case. The intuition is that, while the conditioning of the entropic regularized problem scales like  $1/\varepsilon$ , when  $\varepsilon = 0$ , this conditioning is rather driven by m, the number of samples of the discrete distribution (which controls the size of the Laguerre cells). Other methods exploiting second order schemes were also recently studied by [Knight and Ruiz, 2013, Sugiyama et al., 2017, Cohen et al., 2017, Allen-Zhu et al., 2017].

Image /page/6/Figure/2 description: The image displays two plots. The top plot is a line graph with multiple colored lines, ranging from blue to yellow, representing different values of epsilon. The y-axis ranges from -0.2 to 0.2, and the x-axis ranges from 0 to 1. Red dots are scattered across the graph. A color bar on the right indicates values from 0 to 0.3, corresponding to the colors of the lines. The bottom of the image contains four smaller plots, each labeled with a value of epsilon: ε = 0, ε = 0.01, ε = 0.1, and ε = 0.3. These plots show colored regions (blue, cyan, yellow, red, green) with concentric circular contour lines and a red dot at the center of each. The colors and contour patterns change with increasing epsilon values.

**Figure 5.3:** Top: examples of entropic semidiscrete  $\bar{c}$ -transforms  $\mathbf{g}^{\bar{c},\varepsilon}$  in one dimension, for ground cost  $c(x, y) = |x - y|$  for varying  $\varepsilon$  (see colorbar). The red points are at locations  $(y_j, -\mathbf{g}_j)_j$ . Bottom:  $\alpha$  examples of entropic semidiscrete  $\bar{c}$ -transforms  $\mathbf{g}^{\bar{c},\varepsilon}$  in two dimensions, for ground cost  $c(x,y) = ||x-y||_2$ for varying  $\varepsilon$ . The black curves are the level sets of the function  $g^{\bar{c},\varepsilon}$ , while the colors indicate the smoothed indicator function of the Laguerre cells  $\chi_j^{\varepsilon}$ . The red points are at locations  $y_j \in \mathbb{R}^2$ , and their size is proportional to  $\mathbf{g}_j$ .

**Remark 5.2** (Legendre transforms of OT cost functions)**.** As stated in Proposition 4.6,  $L_{\mathbf{C}}^{\varepsilon}(\mathbf{a},\mathbf{b})$  is a convex function of  $(\mathbf{a},\mathbf{b})$  (which is also true in the unregularized case  $\varepsilon = 0$ ). It is thus possible to compute its Legendre–Fenchel transform, which is defined in (4.54). Denoting  $F_a(b) = L_C^{\varepsilon}(a, b)$ , one has, for a fixed **a**, following Cuturi and Peyré [2016]:

$$
F_{\mathbf{a}}^*(\mathbf{g}) = -\varepsilon H(\mathbf{a}) + \sum_i \mathbf{a}_i \mathbf{g}^{\bar{c},\varepsilon}(x_i).
$$

Here  $g^{\bar{c},\varepsilon}$  is the entropic-smoothed *c*-transform introduced in (5.10). In the unregularized case  $\varepsilon = 0$ , and for generic measures, Carlier et al. [2015] show, denoting  $\mathcal{F}_{\alpha}(\beta) \stackrel{\scriptscriptstyle\rm def.}{=} \mathcal{L}_c(\alpha,\beta),$ 

$$
\forall g \in \mathcal{C}(\mathcal{Y}), \quad \mathcal{F}_{\alpha}^*(g) = \int_{\mathcal{X}} g^{\bar{c}}(x) d\alpha(x),
$$

where the  $\bar{c}$ -transform  $g^{\bar{c}} \in C(\mathcal{X})$  of *g* is defined in §5.1. Note that here, since  $\mathcal{M}(\mathcal{X})$ is in duality with  $C(\mathcal{X})$ , the Legendre transform is a function of continuous functions. Denoting now  $G(\mathbf{a}, \mathbf{b}) \stackrel{\text{def.}}{=} L_C^{\varepsilon}(\mathbf{a}, \mathbf{b})$ , one can derive as in [Cuturi and Peyré, 2016, 2018] the Legendre transform for both arguments,

$$
\forall (\mathbf{f}, \mathbf{g}) \in \mathbb{R}^n \times \mathbb{R}^m, \quad G^*(\mathbf{f}, \mathbf{g}) = -\varepsilon \log \sum_{i,j} e^{\frac{-\mathbf{C}_{i,j} + \mathbf{f}_i + \mathbf{g}_j}{\varepsilon}},
$$

which can be seen as a smoothed version of the Legendre transform of  $\mathcal{G}(\alpha,\beta) \stackrel{\text{def.}}{=}$  $\mathcal{L}_c(\alpha,\beta),$ 

$$
\forall (f,g) \in \mathcal{C}(\mathcal{X}) \times \mathcal{C}(\mathcal{Y}), \quad \mathcal{G}^*(f,g) = \inf_{(x,y) \in \mathcal{X} \times \mathcal{Y}} c(x,y) - f(x) - g(y).
$$

## **5.4 Stochastic Optimization Methods**

The semidiscrete formulation (5.8) and its smoothed version (5.12) are appealing because the energies to be minimized are written as an expectation with respect to the probability distribution *α*,

$$
\mathcal{E}^{\varepsilon}(\mathbf{g}) = \int_{\mathcal{X}} E^{\varepsilon}(\mathbf{g}, x) d\alpha(x) = \mathbb{E}_{X}(E^{\varepsilon}(\mathbf{g}, X))
$$
  
where  $E^{\varepsilon}(\mathbf{g}, x) \stackrel{\text{def.}}{=} \mathbf{g}^{\bar{c}, \varepsilon}(x) - \langle \mathbf{g}, \mathbf{b} \rangle$ 

and *X* denotes a random vector distributed on  $\mathcal X$  according to  $\alpha$ . Note that the gradient of each of the involved functional reads

$$
\nabla_{\mathbf{g}} E^{\varepsilon}(x, \mathbf{g}) = (\chi_j^{\varepsilon}(x) - \mathbf{b}_j)_{j=1}^m \in \mathbb{R}^m.
$$

One can thus use stochastic optimization methods to perform the maximization, as proposed in Genevay et al. [2016]. This allows us to obtain provably convergent algorithms

without the need to resort to an arbitrary discretization of  $\alpha$  (either approximating  $\alpha$ ) using sums of Diracs or using quadrature formula for the integrals). The measure  $\alpha$  is used as a black box from which one can draw independent samples, which is a natural computational setup for many high-dimensional applications in statistics and machine learning. This class of methods has been generalized to the computation of Wasserstein barycenters (as described in §9.2) in [Staib et al., 2017b].

**Stochastic gradient descent.** Initializing  $\mathbf{g}^{(0)} = \mathbb{0}_P$ , the stochastic gradient descent algorithm (SGD; used here as a maximization method) draws at step  $\ell$  a point  $x_{\ell} \in \mathcal{X}$ according to distribution  $\alpha$  (independently from all past and future samples  $(x_\ell)_\ell$ ) to form the update

$$
\mathbf{g}^{(\ell+1)} \stackrel{\text{def}}{=} \mathbf{g}^{(\ell)} + \tau_{\ell} \nabla_{\mathbf{g}} E^{\varepsilon}(\mathbf{g}^{(\ell)}, x_{\ell}). \tag{5.14}
$$

The step size  $\tau_\ell$  should decay fast enough to zero in order to ensure that the "noise" created by using  $\nabla_{\mathbf{g}} E^{\varepsilon}(x_{\ell}, \mathbf{g})$  as a proxy for the true gradient  $\nabla \mathcal{E}^{\varepsilon}(\mathbf{g})$  is canceled in the limit. A typical choice of schedule is

$$
\tau_{\ell} \stackrel{\text{def.}}{=} \frac{\tau_0}{1 + \ell/\ell_0},\tag{5.15}
$$

where  $\ell_0$  indicates roughly the number of iterations serving as a warmup phase. One can prove the convergence result

$$
\mathcal{E}^{\varepsilon}(\mathbf{g}^*) - \mathbb{E}(\mathcal{E}^{\varepsilon}(\mathbf{g}^{(\ell)})) = O\left(\frac{1}{\sqrt{\ell}}\right),\,
$$

where  $g^*$  is a solution of (5.12) and where  $E$  indicates an expectation with respect to the i.i.d. sampling of  $(x_\ell)_\ell$  performed at each iteration. Figure 5.4 shows the evolution of the algorithm on a simple 2-D example, where  $\alpha$  is the uniform distribution on  $[0,1]^2$ .

**Stochastic gradient descent with averaging.** SGD is slow because of the fast decay of the stepsize  $\tau_\ell$  toward zero. To improve the convergence speed, it is possible to average the past iterates, which is equivalent to running a "classical" SGD on auxiliary variables  $(\tilde{\mathbf{g}}^{(\ell)})_{\ell}$ 

$$
\tilde{\mathbf{g}}^{(\ell+1)} \stackrel{\text{\tiny def.}}{=} \tilde{\mathbf{g}}^{(\ell)} + \tau_{\ell} \nabla_{\mathbf{g}} E^{\varepsilon}(\tilde{\mathbf{g}}^{(\ell)}, x_{\ell}),
$$

where  $x_{\ell}$  is drawn according to  $\alpha$  (and all the  $(x_{\ell})_{\ell}$  are independent) and output as estimated weight vector the average

$$
\mathbf{g}^{(\ell)}\stackrel{\scriptscriptstyle\rm def.}{=}\frac{1}{\ell}\sum_{k=1}^{\ell}\tilde{\mathbf{g}}^{(k)}.
$$

This defines the stochastic gradient descent with averaging (SGA) algorithm. Note that it is possible to avoid explicitly storing all the iterates by simply updating a running

Image /page/9/Figure/1 description: The image displays a line graph showing the evolution of a logarithmic value over iterations. The y-axis is labeled with values ranging from -8 to 0, and the x-axis is labeled with iteration numbers from 0 to 10000. Several colored lines, representing different runs, decrease sharply at the beginning and then fluctuate around a lower value. Four smaller inset images, arranged below the main graph, show Voronoi diagrams with red dots representing points within different colored regions. Arrows connect specific points on the lines in the main graph to these inset Voronoi diagrams, illustrating the state of the system at different stages of the iteration process.

**Figure 5.4:** Evolution of the energy  $\mathcal{E}^{\varepsilon}(\mathbf{g}^{(\ell)})$ , for  $\varepsilon = 0$  (no regularization) during the SGD iterations (5.14). Each colored curve shows a different randomized run. The images display the evolution of the Laguerre cells  $(\mathbb{L}_j(\mathbf{g}^{(\ell)}))_j$  through the iterations.

average as follows:

$$
\mathbf{g}^{(\ell+1)} = \frac{1}{\ell+1}\tilde{\mathbf{g}}^{(\ell+1)} + \frac{\ell}{\ell+1}\mathbf{g}^{(\ell)}.
$$

In this case, a typical choice of decay is rather of the form

$$
\tau_{\ell} \stackrel{\text{\tiny def.}}{=} \frac{\tau_0}{1 + \sqrt{\ell/\ell_0}}.
$$

Notice that the step size now goes much slower to 0 than for (5.15), at rate  $\ell^{-1/2}$ . Bach [2014] proves that SGA leads to a faster convergence (the constants involved are smaller) than SGD, since in contrast to SGD, SGA is adaptive to the local strong convexity (or concavity for maximization problems) of the functional.

**Remark 5.3** (Continuous-continuous problems). When neither  $\alpha$  nor  $\beta$  is a discrete measure, one cannot resort to semidiscrete strategies involving finite-dimensional dual variables, such as that given in Problem (5.7). The only option is to use stochastic optimization methods on the dual problem (4.45), as proposed in [Genevay et al., 2016]. A suitable regularization of that problem is crucial, for instance by setting an entropic regularization strength  $\varepsilon > 0$ , to obtain an unconstrained problem that can be solved by stochastic descent schemes. A possible approach to revisit Problem (4.45) is to restrict that infinite-dimensional optimization problem over a space of continuous functions to a

much smaller subset, such as that spanned by multilayer neural networks [Seguy et al., 2018]. This approach leads to nonconvex finite-dimensional optimization problems with no approximation guarantees, but this can provide an effective way to compute a proxy for the Wasserstein distance in high-dimensional scenarios. Another solution is to use nonparametric families, which is equivalent to considering some sort of progressive refinement, as that proposed by Genevay et al. [2016] using reproducing kernel Hilbert spaces, whose dimension is proportional to the number of iterations of the SGD algorithm.