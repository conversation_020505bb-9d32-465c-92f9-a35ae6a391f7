In the previous chapter I introduced the concept of weak curvaturedimension bound, which extends the classical notion of curvaturedimension bound from the world of smooth Riemannian manifolds to the world of metric-measure geodesic spaces; then I proved that such bounds are stable under measured Gromov–<PERSON>f convergence.

Still, this notion would be of limited value if it could not be used to establish nontrivial conclusions. Fortunately, weak curvature-dimension bounds do have interesting geometric and analytic consequences. This might not be a surprise to the reader who has already browsed Part II of these notes, since there many geometric and analytic statements of Riemannian geometry were derived from optimal transport theory.

In this last chapter, I shall present the state of the art concerning properties of weak  $CD(K, N)$  spaces. This direction of research is growing relatively fast, so the present list might soon become outdated.

**Convention:** Throughout the sequel, a "weak  $CD(K, N)$  space" is a locally compact, complete separable geodesic space  $(\mathcal{X}, d)$  equipped with a locally finite Borel measure  $\nu$ , satisfying a weak  $CD(K, N)$  condition as in Definition 29.8.

## Elementary properties

The next proposition gathers some almost immediate consequences of the definition of weak  $CD(K, N)$  spaces. I shall say that a subset  $\mathcal{X}'$  of a geodesic space  $(\mathcal{X}, d)$  is *totally convex* if any geodesic whose endpoints belong to  $\mathcal{X}'$  is entirely contained in  $\mathcal{X}'$ .

Proposition 30.1 (Elementary consequences of weak  $CD(K, N)$ **bounds**). Let  $(X, d, \nu)$  be a weak  $CD(K, N)$  space. Then:

(i) If  $\mathcal{X}'$  is a totally convex closed subset of  $\mathcal{X}$ , then  $\mathcal{X}'$  inherits from  $(\mathcal{X}, d, \nu)$  a natural structure of metric-measure geodesic space, and  $\mathcal{X}'$ is also a weak  $CD(K, N)$  space;

- (ii) For any  $\alpha > 0$ ,  $(\mathcal{X}, d, \alpha \nu)$  is a weak CD(K, N) space;
- (iii) For any  $\lambda > 0$ ,  $(\mathcal{X}, \lambda d, \nu)$  is a weak  $CD(\lambda^{-2}K, N)$  space.

*Proof of Proposition 30.1.* Let  $\mathcal{X}'$  be a totally convex subset of  $\mathcal{X},$ equipped with the restriction of the distance d and the measure  $\nu$ . Let  $\mu_0$ ,  $\mu_1$  be two probability measures in  $P_2(\mathcal{X}')$ . The notion of optimal coupling is the same whether one considers them as measures on  $\mathcal{X}'$  or on  $\mathcal{X}$ . Also, since  $\mathcal{X}'$  is totally convex, a path  $[0,1] \to \mathcal{X}$  with endpoints in  $\mathcal{X}'$  is a geodesic in  $\mathcal{X}'$  if and only if it is a geodesic in  $\mathcal{X}$ . So  $\mathcal{X}'$  is a geodesic space, and the representation theorem for Wasserstein geodesics (Theorem 7.22) ensures that a path  $(\mu_t)_{0 \leq t \leq 1}$  valued in  $P_2(\mathcal{X}')$  is a geodesic in  $P_2(\mathcal{X}')$  if and only if it is a geodesic in  $P_2(\mathcal{X})$ . Property (i) follows immediately.

To prove (ii), note that the replacement of  $\nu$  by  $\alpha\nu$  induces a multiplication of the density  $\rho$  by  $\alpha^{-1}$ ; so

$$
U_{\alpha\nu}(\mu) = (U_{\alpha})_{\nu}(\mu), \qquad U_{\pi,\alpha\nu}^{\beta}(\mu) = (U_{\alpha})_{\pi,\nu}^{\beta}(\mu),
$$

where  $U_{\alpha}(r) = \alpha U(\alpha^{-1}r)$ . But the transform  $U \to U_{\alpha}$  leaves the class  $DC_N$  invariant. So the inequalities defining the  $CD(K, N)$  condition will hold just the same in  $(\mathcal{X}, d, \alpha\nu)$  or in  $(\mathcal{X}, d, \nu)$ .

As for (iii), recall the definition of  $\beta^{(K,N)}$ :

$$
\beta_t^{(K,N)}(x,y) = \left(\frac{\sin t\alpha(N,K,d(x,y))}{t\sin\alpha(N,K,d(x,y))}\right)^{N-1},
$$

where

$$
\alpha(N, K, d) = \sqrt{\frac{K}{N - 1}} d(x, y).
$$

Then  $\alpha(N,K,d) = \alpha(N,\lambda^{-2}K,\lambda d)$ , and Property (iii) follows. □

The next theorem shows that the property of being a  $CD(K, N)$ space does not involve the whole space  $\mathcal{X}$ , but only the support of  $\nu$ .

Theorem 30.2 (Restriction of the  $CD(K, N)$  property to the support). A metric-measure space  $(\mathcal{X}, d, \nu)$  is a weak  $CD(K, N)$  space if and only if  $(\text{Spt }\nu, d, \nu)$  is itself a weak  $CD(K, N)$  space.

Remark 30.3. Theorem 30.2 allows us to systematically reduce to the case when  $Spt \nu = \mathcal{X}$  in the study of properties of weak  $CD(K,N)$ spaces. Then why not impose this in the definition of these spaces? The answer is that on some occasions it is useful to allow  $\mathcal X$  to be larger than  $Spt \nu$ , in particular for *convergence* issues. Indeed, it may very well happen that a sequence of weak  $CD(K, N)$  spaces  $(\mathcal{X}_k, d_k, \nu_k)_{k \in \mathbb{N}}$ with  $\text{Spt } \nu_k = \mathcal{X}_k$  converges in the measured Gromov–Hausdorff sense to a metric-measure space  $(\mathcal{X}, d, \nu)$  such that Spt  $\nu$  is strictly smaller than  $X$ . This phenomenon of "reduction of support" is impossible if  $N < \infty$ , as shown by Theorem 29.32, but can occur in the case  $N = \infty$ . As a simple example, consider  $\mathcal{X}_k = (\mathbb{R}^n, |\cdot|)$  (Euclidean space) equipped with the sharply peaked Gaussian probability measure  $e^{-k|x|^2}dx/Z_k$ ,  $Z_k$  being a normalizing constant. As  $k \to \infty$ ,  $\mathcal{X}_k$  converges in measured Gromov–Hausdorff sense to  $\mathcal{X} = (\mathbb{R}^n, |\cdot|, \delta_0)$ . Each of the spaces  $\mathcal{X}_k$  is a weak  $CD(0,\infty)$  space and satisfies  $Spt \nu_k = \mathcal{X}_k$ , however, the limit measure is supported in just a point. To summarize: For weak  $CD(K, N)$  spaces  $(X, d, \nu)$  with  $N < \infty$ , one probably does not lose anything by assuming  $\text{Spt } v = \mathcal{X}$ ; but in the class of weak  $CD(K,\infty)$  spaces, the stability theorem would not be true if one would not allow the support of  $\nu$  to be strictly smaller than the whole space.

*Proof of Theorem 30.2.* First assume that  $(\text{Spt }\nu,d,\nu)$  has the weak  $CD(K, N)$  property. Replacing  $Spt \nu$  by X does not enlarge the class of probability measures that can be chosen for  $\mu_0$  and  $\mu_1$  in Definition 29.8, and does not change the functionals  $U_{\nu}$  or  $U_{\pi,\nu}^{\beta_t^{(K,N)}}$  either. Because  $Spt\nu$  is (by assumption) a length space, geodesics in  $Spt\nu$ are also geodesics in X. So geodesics in  $P_2(\text{Spt }\nu)$  are also geodesics in  $P_2(\mathcal{X})$  (it is the converse that might be false), and then the property of  $\mathcal{X}'$  being a weak  $CD(K, N)$  space implies that  $\mathcal X$  is also a weak  $CD(K,N)$  space.

The converse implication is more subtle. Assume that  $(\mathcal{X}, d, \nu)$  is a weak  $CD(K, N)$  space. Let  $\mu_0$ ,  $\mu_1$  be two compactly supported probability measures on X with  $\text{Spt }\mu_0 \subset \text{Spt }\nu$ ,  $\text{Spt }\mu_1 \subset \text{Spt }\nu$ . For any  $t_0 \in \{0, 1\}$ , choose a sequence of probability measures  $(\mu_{k,t_0})_{k \in \mathbb{N}}$  converging to  $\mu_{t_0}$ , satisfying the conclusion of Theorem 29.20(iii), such that the supports of all measures  $\mu_{k,t_0}$  are included in a common compact set. By definition of the weak  $CD(K, N)$  property, for each  $k \in \mathbb{N}$ there is a Wasserstein geodesic  $(\mu_{k,t})_{0 \leq t \leq 1}$  and an associated coupling  $\pi_k \in \Pi(\mu_k, \nu_k)$  such that for all  $t \in [0, 1]$  and  $U \in \mathcal{DC}_N$ ,

$$
U_{\nu}(\mu_{k,t}) \le (1-t) U_{\pi_k,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_{k,0}) + t U_{\check{\pi}_k,\nu}^{\beta_t^{(K,N)}}(\mu_{k,1}). \tag{30.1}
$$

Choosing  $H(r) = r \log r$ , and using the monotonicity of the CD(K, N) condition with respect to  $N$ , we deduce

$$
H_{\nu}(\mu_{k,t}) \le (1-t) H_{\pi_{k},\nu}^{\beta_{1-t}^{(K,\infty)}}(\mu_{k,0}) + t H_{\check{\pi}_k,\nu}^{\beta_t^{(K,\infty)}}(\mu_{k,1}).
$$
 (30.2)

By an explicit calculation (as in the proof of (30.6) later in this chapter) the right-hand side is equal to

$$
(1-t) H_{\nu}(\mu_{k,0}) + t H_{\nu}(\mu_{k,1}) - K \frac{t(1-t)}{2} \int d(x_0,x_1)^2 \pi_k(dx_0 dx_1),
$$

and this quantity is finite since  $\mu_{k,0}$ ,  $\mu_{k,1}$  are compactly supported. Then by (30.2),  $H_{\nu}(\mu_{k,t}) < +\infty$  for all  $t \in [0,1]$  and for all  $k \in \mathbb{N}$ . Since  $H'(\infty) = \infty$ , this implies that  $\mu_{k,t}$  is absolutely continuous with respect to  $\nu$ , and in particular it is supported in Spt  $\nu$ .

Next, by Ascoli's theorem, some subsequence of the family  $(\mu_{k,t})$ converges uniformly in  $C([0, 1], P_2(\mathcal{X}))$  to some Wasserstein geodesic  $(\mu_t)_{0 \leq t \leq 1}$ . Since Spt  $\nu$  is closed,  $\mu_t$  is also supported in Spt  $\nu$ , for each  $t \in [0, 1].$ 

Let  $(\gamma_t)_{0 \leq t \leq 1}$  be a random geodesic such that  $\mu_t = \text{law}(\gamma_t)$ . By the preceding argument,  $\mathbb{P}[\gamma_t \notin Spt \nu] = 0$  for any  $t \in [0, 1]$ . If  $(t_i)_{i \in \mathbb{N}}$ is a dense sequence of times in [0, 1], then  $\mathbb{P}[\exists j; \gamma_{t_j} \notin Spt \nu] = 0.$ But since  $\gamma$  is continuous and Spt  $\nu$  closed, this can be reinforced into  $\mathbb{P}[\exists t; \gamma_t \notin Spt \nu] = 0.$  So  $\gamma$  lies entirely in Spt  $\nu$ , with probability 1. The path  $(\mu_t)_{0 \leq t \leq 1}$  is valued in  $P_2(\text{Spt }\nu)$ , and it is a geodesic in the larger space  $P_2(\mathcal{X})$ ; so it is also a geodesic in  $P_2(\text{Spt }\nu)$ .

Admit for the moment that  $Spt \nu$  is a geodesic space. By Proposition 29.12, to show that  $\text{Spt } \nu$  is a weak  $\text{CD}(K,N)$  space it is sufficient to establish the displacement convexity property when  $U \in \mathcal{DC}_N$  is Lipschitz (for  $N < \infty$ ) or behaves like r log r at infinity (for  $N = \infty$ ). For such a nonlinearity  $U$ , we can pass to the limit in  $(30.1)$ , invoking Theorem  $29.20(i)$  and (iv):

$$
U_{\nu}(\mu_{t}) \leq \liminf_{k \to \infty} U_{\nu}(\mu_{k,t})
$$
  

$$
\leq \limsup_{k \to \infty} \left[ (1-t) U_{\pi_{k},\nu}^{\beta_{1-t}^{(K,N)}}(\mu_{k,0}) + t U_{\check{\pi}_{k},\nu}^{\beta_{t}^{(K,N)}}(\mu_{k,1}) \right]
$$
  

$$
\leq (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_{0}) + t U_{\check{\pi},\nu}^{\beta_{t}^{(K,N)}}(\mu_{1}), \qquad (30.3)
$$

where  $\mu_0 = \rho_0 \nu$ ,  $\mu_1 = \rho_1 \nu$ , and  $\pi$  is an optimal coupling between  $\mu_0$ and  $\mu_1$ .

It only remains to check that  $Spt \nu$  is indeed geodesic; this is not a priori obvious since it needs not be totally convex. Let  $x_0, x_1$  be any two points in Spt  $\nu$ ; then for any  $r > 0$ , we have  $\nu[B_r(x_0)] > 0$ ,  $\nu[B_r(x_1)] > 0$ ; so it makes sense to define  $\mu_0 = 1_{B_r(x_0)}/\nu[B_r(x_0)]$ , and  $\mu_1 = 1_{B_r(x_1)}/\nu[B_r(x_1)]$ . The preceding reasoning shows that there is a random geodesic  $\gamma^{(r)}$  which lies entirely in Spt  $\nu$ , and whose endpoints belong to  $B_r(x_0)$  and  $B_r(x_1)$ . By Ascoli's theorem, there is a subsequence  $r_j \to 0$  such that  $\gamma^{(r_j)}$  converges uniformly to some random geodesic  $\gamma$ , which necessarily satisfies  $\gamma_0 = x_0, \gamma_1 = x_1$ , and lies entirely in Spt  $\nu$ . So Spt  $\nu$  satisfies the axioms of a geodesic space.  $\Box$ 

## Displacement convexity

The definition of weak  $CD(K, N)$  spaces is based upon displacement convexity inequalities, but these inequalities are only required to hold under the assumption that  $\mu_0$  and  $\mu_1$  are compactly supported. To exploit the full strength of displacement convexity inequalities, it is important to get rid of this restriction.

The next theorem shows that the functionals appearing in Definition 29.1 can be extended to measures  $\mu$  that are not compactly supported, provided that the nonlinearity U belongs to some  $\mathcal{DC}_N$  class, and the measure  $\mu$  admits a moment of order p, where N and p are related through the behavior of  $\nu$  at infinity. Recall Conventions 17.10 and 17.30 from Part II.

Theorem 30.4 (Domain of definition of  $U_{\nu}$  and  $U_{\pi,\nu}^{\beta}$  on noncompact spaces). Let  $(X, d)$  be a boundedly compact Polish space, equipped with a locally finite measure  $\nu$ , and let z be any point in X. Let  $K \in \mathbb{R}, N \in [1,\infty],$  and  $U \in \mathcal{DC}_N$ . For any measure  $\mu$  on X, introduce its Lebesgue decomposition with respect to  $\nu$ :

$$
\mu = \rho \nu + \mu_s.
$$

Let  $\pi(dy|x)$  be a family of conditional probability measures on X, indexed by  $x \in \mathcal{X}$ , and let  $\pi(dx\,dy) = \mu(dx)\,\pi(dy|x)$  be the associated coupling with first marginal  $\mu$ . Assume that

$$
\int_{\mathcal{X}\times\mathcal{X}} d(x,y)^2 \,\pi(dx\,dy) < +\infty; \qquad \int_{\mathcal{X}} d(z,x)^p \,\mu(dx) < +\infty,
$$

where  $p \geq 2$  is such that

$$
\begin{cases}\n\int_{\mathcal{X}} \frac{d\nu(x)}{[1+d(z,x)]^{p(N-1)}} < +\infty & (N < \infty) \\
\exists c > 0; \qquad \int_{\mathcal{X}} e^{-c d(z,x)^p} d\nu(x) < +\infty & (N = \infty).\n\end{cases}
$$
\n(30.4)

Then for any  $t \in [0,1]$  the following expressions make sense in  $\mathbb{R} \cup$  $\{\pm\infty\}$  and can be taken as generalized definitions of the functionals appearing in Definition 29.1:

$$
U_{\nu}(\mu) := \int_{\mathcal{X}} U(\rho(x)) \nu(dx) + U'(\infty) \mu_s[\mathcal{X}];
$$
  
$$
U_{\pi,\nu}^{\beta_t^{(K,N)}}(\mu) := \int_{\mathcal{X}\times\mathcal{X}} U\left(\frac{\rho(x)}{\beta_t^{(K,N)}(x,y)}\right) \beta_t^{(K,N)}(x,y) \pi(dy|x) \nu(dx) + U'(\infty) \mu_s[\mathcal{X}],
$$

where the right-hand side really should be understood as

$$
\lim_{N'\downarrow N}\int_{\mathcal{X}\times\mathcal{X}}U\left(\frac{\rho(x)}{\beta_t^{(K,N')}(x,y)}\right)\beta_t^{(K,N')}(x,y)\,\pi(dy|x)\,\nu(dx) + U'(\infty)\,\mu_s[\mathcal{X}].
$$

Even if there is no such p,  $U^{\beta}_{\pi,\nu}(\mu)$  still makes sense for any  $\mu \in P_c(\mathcal{X})$ .

Proof of Theorem 30.4. The proof is the same as for Theorem 17.28 and Application 17.29, with only two minor differences: (a)  $\rho$  is not necessarily a probability density, but still its integral is bounded above by 1; (b) there is an additional term  $U'(\infty) \mu_s[\mathcal{X}] \in \mathbb{R} \cup \{+\infty\}.$  □

The next theorem is the final goal of this section: It extends the displacement convexity inequalities of Definition 29.8 to noncompact situations.

Theorem 30.5 (Displacement convexity inequalities in weak CD(K, N) spaces). Let  $N \in [1,\infty]$ , let  $(\mathcal{X},d,\nu)$  be a weak CD(K, N) space, and let  $p \in [2, +\infty) \cup \{c\}$  satisfy condition (30.4). Let  $\mu_0$  and  $\mu_1$  be two probability measures in  $P_p(\mathcal{X})$ , whose supports are included in Spt  $\nu$ . Then there exists a Wasserstein geodesic  $(\mu_t)_{0 \le t \le 1}$ , and an associated optimal coupling  $\pi$  of  $(\mu_0, \mu_1)$  such that, for all  $U \in \mathcal{DC}_N$ and for all  $t \in [0,1]$ ,

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) + t U_{\check{\pi},\nu}^{\beta_t^{(K,N)}}(\mu_1). \tag{30.5}
$$

Furthermore, if  $N = \infty$ , one also has

$$
U_{\nu}(\mu_t) \le (1-t) U_{\nu}(\mu_0) + t U_{\nu}(\mu_1) - \frac{\lambda(K, U) t (1-t)}{2} W_2(\mu_0, \mu_1)^2,
$$
\n(30.6)

where

$$
\lambda(K, U) = \inf_{r>0} \frac{Kp(r)}{r} = \begin{cases} K p'(0) & \text{if } K > 0 \\ 0 & \text{if } K = 0 \\ K p'(\infty) & \text{if } K < 0. \end{cases}
$$
 (30.7)

These inequalities are the starting point for all subsequent inequalities considered in the present chapter.

The proof of Theorem 30.5 will use two auxiliary results, which generalize Theorems 29.20(i) and (iii) to noncompact situations. These are definitely not the most general results of their kind, but they will be enough to derive displacement convexity inequalities with a lot of generality. As usual, I shall denote by  $M_+(\mathcal{X})$  the set of finite (nonnegative) Borel measures on  $\mathcal{X}$ , and by  $L^1_+(\mathcal{X})$  the set of nonnegative  $\nu$ -integrable measurable functions on X. Recall from Definition 6.8 the notion of (weak) convergence in  $P_2(\mathcal{X})$ .

Theorem 30.6 (Lower semicontinuity of  $U_{\nu}$  again). Let  $(\mathcal{X}, d)$  be a boundedly compact Polish space, equipped with a locally finite measure v such that  $\text{Spt } \nu = \mathcal{X}$ . Let  $U : \mathbb{R}_+ \to \mathbb{R}$  be a continuous convex function, with  $U(0) = 0$ ,  $U(r) \ge -c r$  for some  $c \in \mathbb{R}$ . Then

(i) For any  $\mu \in M_+(\mathcal{X})$  and any sequence  $(\mu_k)_{k \in \mathbb{N}}$  converging weakly to  $\mu$  in  $M_+(\mathcal{X})$ ,

$$
U_{\nu}(\mu) \le \liminf_{k \to \infty} U_{\nu}(\mu_k).
$$

(ii) Assume further that  $\mu \in P_2(\mathcal{X})$ , and let  $\beta(x, y)$  be a positive measurable function on  $\mathcal{X} \times \mathcal{X}$ , with  $|\log \beta(x, y)| = O(d(x, y)^2)$ . Then there is a sequence  $(\mu_k)_{k\in\mathbb{N}}$  of compactly supported probability measures on  $\mathcal X$  such that

$$
\mu_k \xrightarrow[k \to \infty]{} \mu \qquad in \ P_2(\mathcal{X}),
$$

and for any sequence of probability measures  $(\pi_k)_{k\in\mathbb{N}}$  such that the first marginal of  $\pi_k$  is  $\mu_k$ , and the second one is  $\mu_{k,1}$ , the limits

$$
\begin{cases} \pi_k \xrightarrow[k \to \infty]{} \pi \\ \int d(x, y)^2 \pi_k(dx \, dy) \xrightarrow[k \to \infty]{} \int d(x, y)^2 \pi(dx \, dy) \\ \mu_{k,1} \xrightarrow[k \to \infty]{} \mu_1 \quad \text{in } P_2(\mathcal{X}) \end{cases}
$$

imply

$$
\lim_{k \to \infty} U_{\pi_k, \nu}^{\beta}(\mu_k) = U_{\pi, \nu}^{\beta}(\mu).
$$

Proof of Theorem 30.6. First of all, we may reduce to the case when U is valued in  $\mathbb{R}_+$ , just replacing U by  $r \to U(r) + c r$ . So in the sequel U will be nonnegative.

Let us start with (i). Let  $z$  be an arbitrary base point, and let  $(\chi_R)_{R>0}$  be a z-cutoff as in the Appendix (that is, a family of cutoff continuous functions that are identically equal to 1 on a ball  $B_R(z)$  and identically equal to 0 outside  $B_{R+1}(z)$ ). For any  $R > 0$ , write

$$
U_{\nu}(\chi_R \,\mu) = \int U(\chi_R \,\rho) \,d\nu + U'(\infty) \int \chi_R \,d\mu_s.
$$

Since U is convex nonnegative with  $U(0) = 0$ , it is nondecreasing; by the monotone convergence theorem,

$$
U_{\nu}(\chi_R \mu) \xrightarrow[R \to \infty]{} U_{\nu}(\mu).
$$

In particular,

$$
U_{\nu}(\mu) = \sup_{R>0} U_{\nu}(\chi_R \mu).
$$
 (30.8)

.

On the other hand, for each fixed  $R$ , we have

$$
U_{\nu}(\chi_R \,\mu) = U_{\chi_{R+1}\nu}(\chi_R \,\mu),
$$

and then we can apply Proposition 29.19(i) with the compact space  $(B_{R+1]}(z), \nu$ , to get

$$
U_{\nu}(\chi_R \mu) = \sup \left\{ \int_{\mathcal{X}} \varphi \chi_R d\mu - \int_{\mathcal{X}} U^*(\varphi) \chi_{R+1} d\nu; \varphi \in C_b(B_{R+1]}(z)), \varphi \le U'(\infty) \right\}
$$

The function  $\varphi \chi_R$ , extended by 0 outside of  $B_{R+1}$ , defines a bounded continuous function on the whole of  $\mathcal{X}$ , so  $\mu \mapsto \int \varphi \chi_R d\mu$  is continuous with respect to the weak topology of convergence against bounded continuous functions. Thus  $U_{\nu}(\chi_R\mu)$  is a lower semicontinuous function of  $\mu$ . This combined with (30.8) shows that  $U_{\nu}$  is a lower semicontinuous function of  $\mu$ , which establishes (i).

Let us now turn to the proof of (ii), which is more tricky. As before I shall assume that U is nonnegative. Let  $B_R = B_{R}(z)$ . Again, let  $(\chi_R)_{R>0}$  be a z-cutoff as in the Appendix. For k large enough,  $\int \chi_R d\mu \geq 1/2$ . Define

$$
\mu_k = \frac{\chi_k \,\mu}{\int \chi_k \, d\mu}.
$$

We'll see that  $\mu_k$  does the job. The proof will use truncation and regularization into continuous functions.

So let  $(\pi_k)$  be as in the theorem. Define

$$
\mu^{(R)} = \frac{\chi_R \mu}{\int \chi_R d\mu}; \qquad Z^{(R)} = \int \chi_R d\mu.
$$

Let  $\rho^{(R)}$  be the density of the absolutely continuous part of  $\mu^{(R)}$ , and  $\mu_s^{(R)}$  be the singular part. It is obvious that  $\rho^{(R)}$  converges to  $\rho$  in  $L^1(\nu)$ and  $\mu_s^{(R)}[\mathcal{X}] \to \mu_s[\mathcal{X}]$  as  $R \to \infty$ .

Next, define

$$
\pi^{(R)}(dy|x) = \chi_R(y) \pi(dy|x) + \left( \int \left[1 - \chi_R(y')\right] \pi(dy'|x) \right) \delta_z;
$$

$$
\pi^{(R)}(dx dy) = \mu^{(R)}(dx) \pi^{(R)}(dy|x).
$$

Note that  $\pi^{(R)}$  is a probability measure supported in  $B_R \times B_R$ .

Further, define  $\mu_k^{(R)}$  $k^{(R)},\ Z_k^{(R)}$  $\frac{\rho(R)}{k}, \ \rho_k^{(R)}$  $\binom{R}{k},\ \mu_{k,s}^{(R)},\ \pi_{k}^{(R)}$  $_{k}^{(R)}(dy|x), \; \pi_{k}^{(R)}$  $\int_k^{(h)} (dx\,dy)$  in a similar way, just replacing  $\mu$  by  $\mu_k$ . The explicit formula

$$
\int \Psi \, d\pi_k^{(R)} = \int \Psi(x, y) \, \chi_R(y) \, \pi_k(dx \, dy) + \int \Psi(x, z) \left[ 1 - \chi_R(y) \right] \pi_k(dx \, dy)
$$

shows that  $\pi_k^{(R)}$  $k_k^{(R)}$  converges to  $\pi^{(R)}$  as  $k \to \infty$ , for any fixed R.

The plan is to first replace the original expressions by the expressions with the superscript  $(R)$ , and then to pass to the limit as  $k \to \infty$  for fixed R. For that I will distinguish two cases.

## Case 1: $U$ is Lipschitz. Then

Case 1: \$U\$ is Lipschitz. Then

$$
\left| U_{\pi^{(R)},\nu}^{\beta}(\mu^{(R)}) - U_{\pi,\nu}^{\beta}(\mu) \right|
$$

$$
\leq \int U\left(\frac{\rho^{(R)}(x)}{\beta(x,y)}\right) \beta(x,y) \pi^{(R)}(dy|x) \nu(dx) - \int U\left(\frac{\rho(x)}{\beta(x,y)}\right) \beta(x,y) \pi(dy|x) \nu(dx)
$$

$$
+ U'(\infty) \left|\mu_s^{(R)}[\mathcal{X}] - \mu_s[\mathcal{X}]\right|
$$

$$
\leq \int \left| U\left(\frac{\rho^{(R)}(x)}{\beta(x,y)}\right) - U\left(\frac{\rho(x)}{\beta(x,y)}\right) \right| \beta(x,y) \pi^{(R)}(dy|x) \nu(dx) + \int U\left(\frac{\rho(x)}{\beta(x,y)}\right) \beta(x,y) \left[1 - \chi_R(y)\right] \pi(dy|x) \nu(dx) + \int U\left(\frac{\rho(x)}{\beta(x,y)}\right) \beta(x,y) \left(\int \left[1 - \chi_R(y')\right] \pi(dy'|x)\right) \delta_{y=z} \nu(dx) + ||U||_{\text{Lip}} \left|\mu_s^{(R)}[\mathcal{X}] - \mu_s[\mathcal{X}]\right|
$$

$$
\leq ||U||_{\text{Lip}} \left(\int |\rho^{(R)}(x) - \rho(x)| \pi^{(R)}(dy|x) \nu(dx) + \int \rho(x) \left[1 - \chi_R(y)\right] \pi(dy|x) \nu(dx) + \int \rho(x) \left[1 - \chi_R(y')\right] \pi(dy'|x) \nu(dx) + |\mu_s^{(R)}[\mathcal{X}] - \mu_s[\mathcal{X}]| \right)
$$

$$
\leq ||U||_{\text{Lip}} \left[\int |\rho^{(R)} - \rho| \, d\nu + 2 \int [1 - \chi_R(y)] \pi(dx \, dy) + |\mu_s^{(R)}[\mathcal{X}] - \mu_s[\mathcal{X}]| \right].
$$
In the last expression, the second term inside brackets is bounded by

In the last expression, the second term inside brackets is bounded by

.

$$
2\int_{d(z,y)\geq R} \pi(dx\,dy) = 2\int_{d(z,y)\geq R} \mu_1(dy) \leq \frac{2}{R^2} \int d(z,y)^2 \,\mu_1(dy).
$$

Conclusion: There is a constant  $C$  such that

$$
\left| U_{\pi^{(R)},\nu}^{\beta}(\mu^{(R)}) - U_{\pi,\nu}^{\beta}(\mu) \right| \le C \left( \|\rho^{(R)} - \rho\|_{L^{1}(\nu)} + |\mu_{s}^{(R)}[\mathcal{X}] - \mu_{s}[\mathcal{X}] | \right) + \frac{1}{R^{2}} \int d(z,y)^{2} \mu_{1}(dy) \right). \tag{30.9}
$$

Similarly,<sup>1</sup>

$$
\left| U^{\beta}_{\pi^{(R)}_k, \nu}(\mu^{(R)}_k) - U^{\beta}_{\pi_k, \nu}(\mu_k) \right| \le C \left( \| \rho^{(R)}_k - \rho_k \|_{L^1(\nu)} + |\mu^{(R)}_{k,s}[X] - \mu_{k,s}[X]| \right) + \frac{1}{R^2} \int d(z, y)^2 \mu_{k,1}(dy) \quad (30.10)
$$

Note that for  $k \ge R$ ,  $\rho_k^{(R)} = \rho^{(R)}$ , and  $\mu_{k,s}^{(R)} = \mu_s^{(R)}$ . Then in view of the definition of  $\mu_k$  and the fact that  $\int d(z, y)^2 \mu_{k,1}(dy)$  is bounded, we easily deduce from (30.9) and (30.10) that

$$
\begin{cases} \lim_{R \to \infty} \left| U^{\beta}_{\pi^{(R)},\nu}(\mu^{(R)}) - U^{\beta}_{\pi,\nu}(\mu) \right| = 0; \\ \lim_{R \to \infty} \limsup_{k \to \infty} \left| U^{\beta}_{\pi^{(R)}_k,\nu}(\mu^{(R)}_k) - U^{\beta}_{\pi_k,\nu}(\mu_k) \right| = 0. \end{cases}
$$

So to prove the result, it is sufficient to establish that for fixed  $R$ ,

$$
\lim_{k \to \infty} U_{\pi_k^{(R)},\nu}^{\beta}(\mu_k^{(R)}) = U_{\pi^{(R)},\nu}^{\beta}(\mu^{(R)}).
$$
\n(30.11)

The interest of this reduction is that all probability measures  $\mu_k^{(R)}$  $\binom{n}{k}$  (resp.  $\pi_k^{(R)}$  $\binom{n}{k}$  are now supported in a common compact set, namely the closed ball  $B_{2R}$  (resp.  $B_{2R} \times B_{2R}$ ). Note that  $\mu_k^{(R)}$  $\mu^{(R)}$  converges to  $\mu^{(R)}$ .

If k is large enough,  $\mu_k^{(R)} = \mu^{(R)}$ , so (30.11) becomes

$$
U_{\pi_k^{(R)},\nu}^{\beta}(\mu^{(R)}) \xrightarrow[k \to \infty]{} U_{\pi^{(R)},\nu}^{\beta}(\mu^{(R)}).
$$

In the sequel, I shall drop the superscript  $(R)$ , so the goal will be

$$
U^{\beta}_{\pi_k,\nu}(\mu) \xrightarrow[k \to \infty]{} U^{\beta}_{\pi,\nu}(\mu).
$$

The argument now is similar to the one used in the proof of Theorem 29.20(iii). Define

$$
g(x,y) = U\left(\frac{\rho(x)}{\beta(x,y)}\right) \frac{\beta(x,y)}{\rho(x)},
$$

with the convention that  $g(x, y) = U'(\infty)$  when  $x \in \text{Spt}\,\mu_s$ , and  $g(x, y) = U'(0)$  when  $\rho(x) = 0$  and  $x \notin \text{Spt } \mu_s$ . Then

<sup>&</sup>lt;sup>1</sup> Here again the notation might be confusing:  $\mu_{k,s}$  stands for the singular part of  $\mu_k$ , while  $\mu_{k,1}$  is the second marginal of  $\pi_k$ .

$$
U_{\pi_k,\nu}^{\beta}(\mu) = \int g(x,y) \,\mu(dx) \,\pi_k(dy|x) = \int g(x,y) \,\pi_k(dx\,dy);
$$
$$
U_{\pi,\nu}(\mu) = \int g(x,y) \,\pi(dx\,dy).
$$

Since  $g \in L^1((B_{2R}, \mu); C(B_{2R}))$ , by Lemma 29.36 there is a sequence  $(\Psi_j)_{j\in\mathbb{N}}$  in  $C(B_{2R}\times B_{2R})$  such that  $\|\Psi_j - g\|_{L^1((B_{2R},\mu);C(B_{2R}))} \xrightarrow[j\to\infty]{} 0.$ Then

$$
\sup_{k \in \mathbb{N}} \left| U_{\pi_k, \nu}^{\beta}(\mu) - \int \Psi_j d\pi_k \right| \le \int \left| g(x, y) - \Psi_j(x, y) \right| \pi_k(dx \, dy)
$$
  
$$
\le \int \sup_y \left| g(x, y) - \Psi_j(x, y) \right| \mu(dx) \xrightarrow[j \to \infty]{} 0;
$$
 $(30.12)$ 

$$
\left| U_{\pi,\nu}^{\beta}(\mu) - \int \Psi_j d\pi \right| \xrightarrow[j \to \infty]{} 0; \tag{30.13}
$$

and for each fixed  $k$ ,

$$
\int \Psi_j \, d\pi_k \longrightarrow \int \Psi_j \, d\pi. \tag{30.14}
$$

The combination of  $(30.12)$ ,  $(30.13)$  and  $(30.14)$  closes the case.

Case 2: U is not Lipschitz. If  $U_{\pi,\nu}^{\beta}(\mu) < +\infty$  then necessarily  $\mu_s[\mathcal{X}] = 0$ . Moreover, there exist positive constants a, c such that  $U(r) \leq a r \log(2+r)$  and  $|U'(r)| \leq c \log(2+r)$ ; in particular, there is  ${\cal C}>0$  such that

$$
\forall x, y \ge 0,
$$
  $|U(x) - U(y)| \le C |x - y| \left( \log(2 + x) + \log(2 + y) \right).$ 

Possibly increasing the value of  $C$ , we deduce that

Possibly increasing the value of C, we deduce that

$$
\left|U_{\pi^{(R)},\nu}^{\beta}(\mu^{(R)}) - U_{\pi,\nu}^{\beta}(\mu)\right|
$$

$$
\leq C \int \left|\frac{\rho^{(R)}(x)}{\beta(x,y)} - \frac{\rho(x)}{\beta(x,y)}\right| \left(\log(2+\rho(x)) + \log\left(2+\frac{1}{\beta(x,y)}\right) + \log(2+\rho^{(R)}(x))\right) \beta(x,y) \pi^{(R)}(dy|x) \nu(dx)
$$

$$
+ C \int \frac{\rho(x)}{\beta(x,y)} \left[\log(2+\rho(x)) + \log\left(2+\frac{1}{\beta(x,y)}\right)\right] \beta(x,y) \left[1 - \chi_R(y)\right] \pi(dy|x) \nu(dx)
$$

$$
+ C \int \frac{\rho(x)}{\beta(x,y)} \left[\log(2+\rho(x)) + \log\left(2+\frac{1}{\beta(x,y)}\right)\right] \beta(x,y) \left(\int \left[1 - \chi_R(y')\right] \pi(dy'|x)\right) \delta_{y=z} \nu(dx).
$$

Using  $\rho^{(R)} \leq 2\rho$ ,  $\log(1/\beta) \leq C d(x,y)^2$  and reasoning as in the first case, we can bound the above expression by

Using \$\rho \leq 2\rho\$, \$\log(1/\rho) \leq C \alpha(x,y)\$ and reasoning as in the first case, we can bound the above expression by

$$
C \int |\rho^{(R)}(x) - \rho(x)| \log(2 + \rho(x)) \nu(dx)
$$

$$
+ C \int |\rho^{(R)}(x) - \rho(x)| (1 + d(x, y))^2 \pi^{(R)}(dy|x) \nu(dx)
$$

$$
+ C \int \rho(x) \log(2 + \rho(x)) [1 - \chi_R(y)] \pi(dy|x) \nu(dx)
$$

$$
+ C \int \rho(x) (1 + d(x, z)^2) [1 - \chi_R(y)] \pi(dy|x) \nu(dx)
$$

$$
\leq C \int |\rho^{(R)}(x) - \rho(x)| \log(2 + \rho(x)) \nu(dx)
$$

$$
+ C (1 + D^2) \int |\rho^{(R)}(x) - \rho(x)| \nu(dx)
$$

$$
+ C \int_{d(x,y) \geq D} |\rho^{(R)}(x) - \rho(x)| (1 + d(x, y)^2) (\pi(dy|x) + \delta_z) \nu(dx)
$$

$$
+ C \log(2 + M) \int \rho(x) [1 - \chi_R(y)] \pi(dy|x) \nu(dx)
$$

$$
+ C \int_{\rho(x) \geq M} \rho(x) \log(2 + \rho(x)) \pi(dy|x) \nu(dx)
$$

$$
+ C (1 + D^2) \int \rho(x) [1 - \chi_R(y)] \pi(dy|x) \nu(dx)
$$

$$
+ C \int_{d(x,z) \geq D} \rho(x) (1 + d(x,z)^2) \pi(dy|x) \nu(dx)
$$

$$
\leq C (1 + D^2) \int |\rho^{(R)}(x) - \rho(x)| \log(2 + \rho(x)) \nu(dx)
$$

$$
+ C \int_{d(x,y) \geq D} [1 + d(x,y)^2] \pi(dx \, dy)
$$

$$
+ C \int_{d(x,z) \geq D} [1 + d(x,z)^2] \pi(dx \, dy)
$$

$$
+ C (\log(2 + M) + (1 + D^2)) \int [1 - \chi_R(y)] \pi(dx \, dy)
$$

$$
+ C \int_{\rho \geq M} \rho \log(2 + \rho) \, dv.
$$

Since  $d(x,y)^2 1_{d(x,y)\geq D} \leq d(x,z)^2 1_{d(x,z)\geq D/2} + d(y,z)^2 1_{d(y,z)\geq D/2}$ , the above expression can in turn be bounded by

$$
C (1+D^2) \int |\rho^{(R)} - \rho| \log(2+\rho) d\nu + C \int_{d(z,x)\geq D/2} [1+d(x,z)^2] \mu(dx)
$$
  
+ 
$$
C \int_{d(z,y)\geq D/2} [1+d(y,z)^2] \mu_1(dy) + C \int_{\rho \geq M} \rho \log(2+\rho) d\nu
$$
  
+ 
$$
C \frac{(\log(2+M)+D^2)}{R^2} \int_{d(z,y)\geq D/2} d(z,y)^2 \mu_1(dy).
$$

Of course this bound converges to 0 if  $R \to \infty$ , then  $M, D \to \infty$ . Similarly,  $\left|U^{\beta}_{\pi}\right|$  $\frac{1}{\pi_k^{(R)},\nu}(\mu_k^{(R)})$  ${k \choose k} - U_{\pi_k,\nu}^{\beta}(\mu_k)$  is bounded by

$$
C (1 + D^2) \int |\rho^{(R)} - \rho| \log(2 + \rho) d\nu + C \int_{d(z,x) \ge D/2} [1 + d(x,z)^2] \mu(dx) + C \int_{d(z,y) \ge D/2} [1 + d(y,z)^2] \mu_{k,1}(dy) + C \int_{\rho \ge M} \rho \log(2 + \rho) d\nu + C \frac{(\log(2 + M) + D^2)}{R^2} \int_{d(z,y) \ge D/2} d(z,y)^2 \mu_{k,1}(dy).
$$

By letting  $k \to \infty$ , then  $R \to \infty$ , then  $M, D \to \infty$  and using the definition of convergence in  $P_2(\mathcal{X})$ , we conclude that

$$
\limsup_{k \to \infty} \left| U_{\pi_k^{(R)},\nu}^{\beta}(\mu_k^{(R)}) - U_{\pi_k,\nu}^{\beta}(\mu_k) \right| \xrightarrow[R \to \infty]{} 0.
$$

From that point on, the proof is similar to the one in the first case. (To prove that  $g \in L^1(B_{2R}; C(B_{2R}))$  one can use the fact that  $\beta$  is bounded from above and below by positive constants on  $B_{2R} \times B_{2R}$ , and apply the same estimates as in the proof of Theorem 29.20(ii).)  $\Box$ 

Proof of Theorem 30.5. By an approximation theorem as in the proof of Proposition 29.12, we may restrict to the case when  $U$  is nonnegative; we may also assume that U is Lipschitz (in case  $N < \infty$ ) or that it behaves at infinity like a r log r+b r (in case  $N = \infty$ ). By approximating N by  $N' > N$ , we may also assume that the distortion coefficients  $\beta_t^{(K,N)}$  $t_k^{(K,N)}(x,y)$  are locally bounded and  $|\log \beta_t^{(K,N)}|$  $|f_t^{(K,N)}(x,y)| = O(d(x,y)^2).$ 

Let  $(\mu_{k,0})_{k\in\mathbb{N}}$  (resp.  $(\mu_{k,1})_{k\in\mathbb{N}}$ ) be a sequence converging to  $\mu_0$  (resp. to  $\mu_1$ ) and satisfying the conclusions of Theorem 30.6(ii). For each k there is a Wasserstein geodesic  $(\mu_{k,t})_{0 \leq t \leq 1}$  and an associated coupling  $\pi_k$  of  $(\mu_{k,0}, \mu_{k,1})$  such that

$$
U_{\nu}(\mu_{k,t}) \le (1-t) U_{\pi_k, \nu}^{\beta_{1-t}^{(K,N)}}(\mu_{k,0}) + t U_{\check{\pi}_k, \nu}^{\beta_t^{(K,N)}}(\mu_{k,1}). \tag{30.15}
$$

Further, let  $\Pi_k$  be a dynamical optimal transference plan such that  $(e_t)_\# \Pi_k = \mu_{k,t}$  and  $(e_0,e_1)_\# \Pi_k = \pi_k$ . Since the sequence  $\mu_{k,0}$  converges weakly to  $\mu_0$ , its elements belong to a compact subset of  $P(\mathcal{X})$ ; the same is true of the measures  $\mu_{k,1}$ . By Theorem 7.21 the families  $(\mu_{k,t})_{0 \leq t \leq 1}$  belong to a compact subset of  $C([0,1]; P(\mathcal{X}))$ ; and also the dynamical optimal transference plans  $\Pi_k$  belong to a compact subset of  $P(C([0, 1]; \mathcal{X}))$ . So up to extraction of a subsequence we may assume that  $\Pi_k$  converges to some  $\Pi$ ,  $(\mu_{k,t})_{0 \leq t \leq 1}$  converges to some path  $(\mu_t)_{0 \leq t \leq 1}$  (uniformly in t), and  $\pi_k$  converges to some  $\pi$ . Since the evaluation map is continuous, it is immediate that  $\pi = (e_0, e_1)_\# \Pi$  and  $\mu_t = (e_t)_\# \Pi.$ 

By Theorem 30.6(i),  $U_{\nu}(\mu_t) \leq \liminf_{k \to \infty} U_{\nu}(\mu_{k,t})$ . Then, by construction (and Theorem  $30.6(ii)$ ),

$$
\begin{cases} \limsup_{k \to \infty} U_{\pi_k, \nu}^{\beta_{1-t}^{(K,N)}}(\mu_{k,0}) \leq U_{\pi, \nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) \\ \limsup_{k \to \infty} U_{\tilde{\pi}_k, \nu}^{\beta_{t}^{(K,N)}}(\mu_{k,1}) \leq U_{\tilde{\pi}, \nu}^{\beta_{t}^{(K,N)}}(\mu_1). \end{cases}
$$

The desired inequality (30.5) follows by plugging the above into (30.15).

To deduce (30.6) from (30.5), I shall use a reasoning similar to the one in the proof of Theorem 20.10. Since  $N = \infty$ , Proposition 17.7(ii) implies  $U'(\infty) = +\infty$  (save for the trivial case where U is linear), so we may assume that  $\mu_0$  and  $\mu_1$  are absolutely continuous with respect to  $\nu$ , with respective densities  $\rho_0$  and  $\rho_1$ . The convexity of  $u : \delta \longmapsto U(e^{-\delta})e^{\delta}$ implies

$$
U\left(\frac{\rho_{1}(x_{1})}{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}\right) \frac{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}{\rho_{1}(x_{1})}
$$
  

$$
\leq U(\rho_{1}(x_{1}))\frac{1}{\rho_{1}(x_{1})}
$$
  

$$
+ \frac{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}{\rho_{1}(x_{1})} p\left(\frac{\rho_{1}(x_{1})}{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}\right) \left(\log \frac{1}{\rho_{1}(x_{1})}-\log \frac{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}{\rho_{1}(x_{1})}\right)
$$
  

$$
= \frac{U(\rho_{1}(x_{1}))}{\rho_{1}(x_{1})}-\frac{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}{\rho_{1}(x_{1})} p\left(\frac{\rho_{1}(x_{1})}{\beta_{t}^{(K,\infty)}(x_{0},x_{1})}\right) \frac{K(1-t^{2})}{6} d(x_{0},x_{1})^{2}
$$
  

$$
\leq \frac{U(\rho_{1}(x_{1}))}{\rho_{1}(x_{1})}-\lambda(K,U)\left(\frac{1-t^{2}}{6}\right) d(x_{0},x_{1})^{2};
$$