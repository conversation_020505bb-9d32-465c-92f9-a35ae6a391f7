{"table_of_contents": [{"title": "Entropy, Relative Entropy \nand Mutual Information", "heading_level": null, "page_id": 0, "polygon": [[69.75, 105.6796875], [364.7109375, 105.6796875], [364.7109375, 157.88671875], [69.75, 157.88671875]]}, {"title": "2.1 ENTROPY", "heading_level": null, "page_id": 0, "polygon": [[70.5, 549.28125], [146.25, 549.28125], [146.25, 561.9375], [70.5, 561.9375]]}, {"title": "2.2 JOINT ENTROPY AND CONDITIONAL ENTROPY", "heading_level": null, "page_id": 3, "polygon": [[62.25, 408.75], [335.689453125, 408.75], [335.689453125, 419.5546875], [62.25, 419.5546875]]}, {"title": "Corollary:", "heading_level": null, "page_id": 5, "polygon": [[63.75, 58.4560546875], [119.25, 58.4560546875], [119.25, 68.4228515625], [63.75, 68.4228515625]]}, {"title": "2.3 RELATIVE ENTROPY AND MUTUAL INFORMATION", "heading_level": null, "page_id": 6, "polygon": [[66.0, 57.0], [355.5, 57.0], [355.5, 67.6318359375], [66.0, 67.6318359375]]}, {"title": "2.4 RELATIONSHIP BET<PERSON>EEN ENTROPY AND MUTUAL \nINFORMATION", "heading_level": null, "page_id": 7, "polygon": [[64.5, 399.75], [356.25, 399.75], [356.25, 422.71875], [64.5, 422.71875]]}, {"title": "2.5 CHAIN RULES FOR ENTROPY, RELATIVE ENTROPY AND \nMUTUAL INFORMATION", "heading_level": null, "page_id": 9, "polygon": [[64.44140625, 178.5], [380.25, 178.5], [380.25, 201.392578125], [64.44140625, 201.392578125]]}, {"title": "2.6 JENSEN'S INEQUALITY AND ITS CONSEQUENCES", "heading_level": null, "page_id": 11, "polygon": [[63.52734375, 393.0], [345.75, 393.0], [345.75, 403.41796875], [63.52734375, 403.41796875]]}, {"title": "Corollary:", "heading_level": null, "page_id": 15, "polygon": [[63.75, 180.0], [118.5, 180.0], [118.5, 191.583984375], [63.75, 191.583984375]]}, {"title": "Corollary:", "heading_level": null, "page_id": 15, "polygon": [[63.75, 246.005859375], [119.25, 246.005859375], [119.25, 257.080078125], [63.75, 257.080078125]]}, {"title": "2.7 THE LOG SUM INEQUALITY AND ITS APPLICATIONS", "heading_level": null, "page_id": 17, "polygon": [[61.5, 60.75], [362.8828125, 60.75], [362.8828125, 70.99365234375], [61.5, 70.99365234375]]}, {"title": "Proof:", "heading_level": null, "page_id": 18, "polygon": [[76.5, 302.25], [111.0, 302.25], [111.0, 312.767578125], [76.5, 312.767578125]]}, {"title": "2.8 DATA PROCESSING INEQUALITY", "heading_level": null, "page_id": 20, "polygon": [[66.0, 58.5], [262.5, 58.5], [262.5, 69.01611328125], [66.0, 69.01611328125]]}, {"title": "2.9 THE SECOND LAW OF THERMODYNAMICS", "heading_level": null, "page_id": 21, "polygon": [[64.5, 402.75], [312.0, 402.75], [312.0, 413.859375], [64.5, 413.859375]]}, {"title": "2.10 SUFFICIENT STATISTICS", "heading_level": null, "page_id": 24, "polygon": [[63.75, 439.5], [219.75, 440.25], [219.75, 449.9296875], [63.75, 449.9296875]]}, {"title": "2.11 FANO'S INEQUALITY", "heading_level": null, "page_id": 26, "polygon": [[64.5, 392.25], [203.37890625, 392.25], [203.37890625, 402.78515625], [64.5, 402.78515625]]}, {"title": "SUMMARY OF CHAPTER 2", "heading_level": null, "page_id": 28, "polygon": [[165.75, 318.62109375], [297.984375, 318.62109375], [297.984375, 328.11328125], [165.75, 328.11328125]]}, {"title": "Alternative expressions: 1", "heading_level": null, "page_id": 29, "polygon": [[66.0, 162.0], [183.7265625, 162.0], [183.7265625, 172.125], [66.0, 172.125]]}, {"title": "Properties of D and I:", "heading_level": null, "page_id": 29, "polygon": [[66.75, 306.75], [174.0, 306.75], [174.0, 316.72265625], [66.75, 316.72265625]]}, {"title": "Chain rules", "heading_level": null, "page_id": 29, "polygon": [[68.25, 420.75], [126.9404296875, 420.75], [126.9404296875, 430.3125], [68.25, 430.3125]]}, {"title": "PROBLEMS FOR CHAPTER 2", "heading_level": null, "page_id": 30, "polygon": [[64.5, 309.0], [209.25, 309.0], [209.25, 318.62109375], [64.5, 318.62109375]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 37, "polygon": [[63.75, 310.5], [170.47265625, 310.5], [170.47265625, 320.51953125], [63.75, 320.51953125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 34], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4949, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 38], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 32], ["Text", 6], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 685, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 39], ["Equation", 10], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 30], ["TableCell", 30], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2492, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 40], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 28], ["Equation", 11], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 33], ["Equation", 8], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 26], ["Equation", 8], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1011, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 33], ["Equation", 10], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1034, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 30], ["Equation", 7], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 30], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 33], ["Equation", 7], ["TextInlineMath", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 33], ["Equation", 10], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 28], ["Text", 7], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 26], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 31], ["TextInlineMath", 8], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 30], ["Text", 6], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3062, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 32], ["TextInlineMath", 6], ["Equation", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 32], ["Text", 6], ["Equation", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 34], ["TextInlineMath", 7], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 35], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 37], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 29], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1012, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 33], ["Text", 7], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 36], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 33], ["Equation", 8], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1035, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 31], ["Text", 7], ["ListItem", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 35], ["Equation", 8], ["ListItem", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 39], ["ListItem", 11], ["TextInlineMath", 4], ["ListGroup", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 40], ["Text", 8], ["ListItem", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 45], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 40], ["ListItem", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1256, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 37], ["ListItem", 17], ["Equation", 4], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 47], ["ListItem", 15], ["Equation", 3], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 36], ["ListItem", 11], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 41], ["Text", 8], ["ListItem", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_34-71"}