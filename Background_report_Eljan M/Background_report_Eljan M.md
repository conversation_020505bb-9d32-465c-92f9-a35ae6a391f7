# Project: Generative Multimodal Dataset Distillation (GMDD) via Latent Concept Distillation Proposal

Supervisor: <PERSON><PERSON> Student: <PERSON><PERSON>/CID: 06021369 Imperial College London

Date: 15.07.2025

## 1 Introduction and Problem Statement

### 1.1 Background

### 1.2 Problem Statement

Current dataset distillation methods encounter several fundamental challenges in multimodal contexts:

Architecture Overfitting: Although dataset distillation can dramatically shrink training corpora, the distilled sets themselves tend to overfit the backbone they were created on; achieving truly architecture-agnostic distillation therefore remains an outstanding challenge (<PERSON>, 2025).

Discrete Textual Data Handling: The discrete nature of textual data makes direct gradientbased optimization challenging, leading to synthetic embeddings that lack universal transferability across different models and tokenizers (<PERSON><PERSON><PERSON>, 2023).

Multimodal Complexity: Existing frameworks struggle with fine-grained cross-modal alignment and rich semantic relationships required for complex multimodal tasks (<PERSON>, 2025).

Computational Inefficiency: Traditional bi-level optimization approaches require extensive computational resources and time for distillation, limiting scalability to large multimodal datasets (<PERSON>, 2025).

## 2 Literature Review

### 2.1 Dataset Distillation Foundations

Dataset distillation has evolved from early gradient matching approaches to sophisticated distribution matching techniques. <PERSON> et al. introduced the foundational bi-level optimization framework, while <PERSON> et al. advanced the field with trajectory matching. Recent works by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. and <PERSON> et al. have introduced generative approaches that leverage pre-trained models for enhanced efficiency and quality (Liu Du, 2025).

### 2.2 Multimodal Dataset Distillation

The extension of dataset distillation to multimodal scenarios represents a nascent but rapidly growing field. Wu et al. pioneered Vision-Language Dataset Distillation, while Tian et al. explored Audio-Visual Dataset Distillation. These works highlight the unique challenges and opportunities in multimodal condensation (Liu Du, 2025).

#### 2.3 Generative Models for Multimodal Synthesis

Recent advances in generative models, particularly diffusion-based architectures like Stable Diffusion, SDXL, and Multimodal Diffusion Transformers, have demonstrated remarkable capabilities in generating high-quality multimodal content (SDXL: Improving Latent Diffusion Models for High-Resolution Image Synthesis, n.d.). These models serve as the foundation for our proposed approach.

## 3 Proposed Method: GMDD Framework

#### 3.1 Core Innovation

By focusing on latent concept distillation and the control space of pre-trained Multimodal Generative Models (MGMs), GMDD aims to produce dynamic, diverse, and universally compatible synthetic data. This proposal suggests a shift from optimizing fixed synthetic data points to learning compact Distilled Multimodal Concepts (DMCs) that control a generative model. Traditionally, DD methods focus on generating fixed synthetic samples, often by optimizing them directly in pixel space or through short-term gradient trajectory matching. Although several recent works have moved towards distilling information into generative models or their latent spaces. However, this work is a more direct and intentional interpretation of what is being distilled, potentially leading to more interpretable and controllable synthetic data generation. Thus, the key innovation lies in distilling knowledge into the control space of generative models rather than directly into pixel or embedding spaces. This kind of approach may enable universal compatibility across different architectures while maintaining the flexibility to generate arbitrary amounts of synthetic data

#### 3.2 Framework Overview

The GMDD framework operates through three distinct phases:

Phase 1: Foundation Pre-training - Establishes the foundational MGM and expert models using large-scale multimodal datasets with curriculum learning and contrastive alignment.

Phase 2: Multimodal Knowledge Condensation (MKC) - Iteratively optimizes DMCs through advanced loss functions that capture cross-modal semantics, diversity, and robustness.

Phase 3: Dynamic Deployment - Generates synthetic multimodal data on-demand using optimized DMCs, supporting flexible batch sizes and continuous adaptation.

#### 3.3 Technical Architecture

The GMDD framework consists of four primary components, leveraging recent advancements in multimodal generative modeling and distillation:

Multimodal Generative Model (MGM): A diffusion-based generative model, built upon SDXL or Stable Diffusion 3.5 architectures with Multimodal Diffusion Transformers (MMDiT), capable of generating coherent image-text pairs at resolutions up to  $1024\times1024$ . The MGM incorporates enhanced cross-attention mechanisms between visual and textual modalities to ensure semantic alignment and prevent modality collapse.

Distilled Multimodal Concepts (DMCs): Learnable parameter sets representing condensed knowledge from the original dataset, optimized via low-rank similarity mining and factorized prototypes. Each DMC consists of visual and textual components that guide the MGM's generation process, supporting exponential informativeness through representation blending.

Multimodal Expert Model: A fine-tuned vision-language model (e.g., CLIP ViT-G/14) that extracts joint embeddings and generates rich soft labels for both real and synthetic data. The expert model ensures semantic consistency during condensation via contrastive alignment and multi-head classification.

Optimization Framework: An efficient training system that alternates between synthetic data generation and DMC updates, incorporating diversity regularization, feature matching objectives, and distributional robustness via DRO.

## 4 Mathematical Formulation

The mathematical framework of GMDD is designed to distill large multimodal datasets into compact, learnable DMCs that serve as latent controls for the MGM.

#### 4.1 Dataset and Concept Definitions

We define the original multimodal dataset as  $\mathcal{T} = \{(x_{i}^{V}, x_{i}^{L}, y_{i})\}_{i=1}^{|\mathcal{T}|}$ , where each sample consists of three components. The visual component  $x_i^V \in \mathbb{R}^{H \times W \times C}$  represents an RGB image with height H, width W, and C channels. The linguistic component  $x_i^L \in \mathcal{V}^S$  denotes a text sequence of S tokens from vocabulary V. The ground-truth label  $y_i \in \Delta^{M-1}$  is represented as a soft probability distribution over M classes to capture nuanced semantic relationships.

The objective is to learn K Distilled Multimodal Concepts (DMCs), denoted as  $\mathcal{C} = \{(c_k^V, c_k^L, p_k)\}_{k=1}^K$ , where  $K \ll |\mathcal{T}|$ . Each DMC consists of a visual prototype  $c_k^V \in \mathbb{R}^{D_V}$  that captures multi-scale visual features, a textual prototype  $c_k^L \in \mathbb{R}^{D_L}$  enriched with semantic information, and privileged information  $p_k \in \Delta^{Q-1}$  that incorporates auxiliary attributes like attention labels or uncertainty estimates.

This formulation helps us to capture the rich semantic relationships needed for multimodal understanding. The soft labels and privileged information help preserve nuanced semantics. That is a primary reason we use soft labels; otherwise, it would be lost in traditional hard-label approaches. The compact DMC representation allows efficient storage and transfer It also maintains the ability to generate diverse synthetic samples.

#### 4.2 Core Optimization Objective

The fundamental optimization problem focuses on learning optimal DMCs while keeping the generative model frozen (or fine-tuning only LoRA adapters):

$$
\mathcal{L}_{total}(\mathcal{C}) = OT_{\epsilon}(\mu_{\mathcal{T}}, \mu_{\mathcal{C}}) + \alpha \cdot \mathcal{L}_{MI}(\mathcal{C}) + \beta \cdot \mathcal{L}_{div}(\mathcal{C})
$$

Here,  $\text{OT}_{\epsilon}$  represents the entropic optimal transport distance between the real data distribution  $\mu_{\mathcal{T}}$  and the synthetic distribution  $\mu_{\mathcal{C}}$  induced by the DMCs.  $\mathcal{L}_{\text{MI}}$  enforces mutual information between visual and textual components, and  $\mathcal{L}_{div}$  promotes diversity among DMCs. The generative model  $G_{\phi}$  parameters remain frozen during optimization.

We freeze the generative model and optimizing only the DMCs. With this approach, we should significantly reduce computational requirements while leveraging the pre-trained model's capabilities. Our unified loss with three complementary terms captures all essential aspects of multimodal distillation, which are distribution matching through optimal transport, cross-modal alignment through mutual information, and concept diversity through repulsion.

##### 4.3 Entropic Optimal Transport

The optimal transport term is computed using the Sinkhorn divergence for efficient differentiable optimization (Cuturi, 2013)(Luise et al., 2019):

$$
\mathrm{OT}_\epsilon(\mu_\mathcal{T},\mu_\mathcal{C})=\min_{\Pi\in\mathcal{U}}\langle\Pi,D\rangle-\epsilon H(\Pi)
$$

where the constraint set is:

$$
\mathcal{U} = \{\Pi \ge 0 : \Pi \mathbf{1}_K = \frac{1}{N} \mathbf{1}_N, \mathbf{1}_N^T \Pi = \frac{1}{K} \mathbf{1}_K \}
$$

and the entropy is  $H(\Pi) = -\sum_{ij} \Pi_{ij} \log \Pi_{ij}$ . The cost matrix  $D_{ij} = d(x_i, \tilde{x}_j)$  measures the distance between real sample  $i$  and synthetic sample  $j$ .

The Sinkhorn algorithm provides a fully differentiable solution to the optimal transport problem by enabling end-to-end optimization. The entropic regularization ensures numerical stability and promotes soft assignments between real and synthetic samples. We think that this formulation is computationally efficient and scales well with batch sizes.

##### 4.4 Sample-to-Prototype Distance

The distance function between real and synthetic samples combines cosine similarity in the expert embedding space with cross-entropy for label alignment:

$$
d(x, \tilde{x}) = [1 - \cos(E(x), E(\tilde{x}))] + \text{CE}(y, \hat{y})
$$

where  $\cos(u, v) = \frac{u^T v}{||u|| ||_2}$  $\frac{u^T v}{||u|| ||v||}$  is the cosine similarity,  $E(\cdot)$  is the expert model embedding, and CE is the cross-entropy that handles both hard and soft labels.

The cosine term is bounded in  $[0, 2]$  and captures semantic similarity in a scale-invariant manner. Cross-entropy naturally handles both hard and soft labels without modification. This simple formulation requires only one hyperparameter for relative scaling and provides stable gradients for optimization.

##### 4.5 Mutual Information Loss

The mutual information term uses InfoNCE loss over the  $K$  prototypes to ensure cross-modal alignment:

$$
\mathcal{L}_{\text{MI}}(\mathcal{C}) = -\frac{1}{K} \sum_{k=1}^{K} \log \frac{\exp(\text{sim}(c_k^V, c_k^L)/\tau)}{\sum_{j=1}^{K} \exp(\text{sim}(c_k^V, c_j^L)/\tau)}
$$

where  $\text{sim}(u, v) = \frac{u^T v}{\|u\| \|u\|}$  $\frac{u^T v}{||u|| ||v||}$  is the normalized dot product and  $\tau$  is the temperature parameter.

InfoNCE provides a lower bound on the mutual information between visual and textual components. This ensures that paired visual-textual prototypes remain aligned while being distinguishable from other prototypes. The temperature parameter controls the sharpness of the distribution, allowing fine-tuning of the alignment strength.

##### 4.6 Diversity Regularization

To prevent DMC collapse, we employ a repulsion kernel that encourages uniform spacing:

$$
\mathcal{L}_{div}(\mathcal{C}) = \sum_{1 \le k < j \le K} \exp\left(-\frac{\|c_k - c_j\|^2}{\sigma^2}\right)
$$

where  $c_k = [c_k^V, c_k^L]$  is the concatenated prototype and  $\sigma$  controls the repulsion range.

The exponential kernel creates smooth repulsion forces that decay with distance. This approach prevents both collapse and over-separation. The single parameter  $\sigma$  is interpretable as the characteristic spacing between concepts. The  $O(K^2)$  complexity is tractable for moderate K values typical in distillation scenarios.

###### 4.7 Feature Pyramid Matching

Applications that require multi-scale feature preservation, we can augment the distance function with pyramid matching (Lin et al. 2017 for FPN):

$$
d_{\text{pyr}}(x,\tilde{x}) = \sum_{l \in \{\text{early}, \text{mid}, \text{late}\}} \mu_l \|F_l(x) - F_l(\tilde{x})\|_2^2
$$

where  $F_l$  extracts features at level l and  $\sum_l \mu_l = 1$ . This is incorporated into the Sinkhorn cost (Genevay et al., 2018):  $D_{ij} \leftarrow D_{ij} + d_{\text{pyr}}(x_i, \tilde{x}_j)$ .

Multi-scale matching facilitates preserving both low-level details and high-level semantics. The simple weighted  $L_2$  formulation integrates with the optimal transport framework without introducing additional hyperparameters beyond the level weights $\mu_l$ .

## 5 Technical Implementation

#### 5.1 Algorithm Design and Rationale

The GMDD framework is implemented through four key algorithms, each designed to address specific challenges in multimodal dataset distillation:

##### Algorithm 1: MGM Architecture Enhancement

This algorithm allows the base diffusion model accept DMC conditioning and maintain the pretrained knowledge. We employ LoRA (Low-Rank Adaptation) for efficient fine-tuning where pretrained weights are not destroyed. AdaIN (Adaptive Instance Normalization) provides effective feature modulation for style control (Hu et al. 2021). The role of multi-scale injection is to ensure that DMCs will be influenced to generate at multiple resolutions. This leads to better coherence between global and local features.

##### Algorithm 2: Foundation Pre-training

Foundation pre-training establishes strong multimodal representations before distillation. Curriculum learning starts with simple examples and gradually increases complexity. It leads to more stable training. The diffusion loss ensures the MGM can generate high-quality images, while the contrastive loss aligns visual and textual representations. This dual training approach creates a strong foundation for subsequent distillation phases.

##### Algorithm 3: Multimodal Knowledge Condensation (Revised)

This algorithm focuses optimization on the DMCs and keeps the generative model frozen (except for optional LoRA adapters). K-means initialization provides a data-driven starting point.

Algorithm 1 MGM Architecture Enhancement Require: Base SDXL model, DMC dimensions Ensure: Enhanced MGM with DMC conditioning Load pretrained SDXL UNet and text encoder Add DMC projection layers:  $dmc\_proj = Linear(dmc\_dim, hidden\_dim)$  with LoRA for efficiency Insert cross-attention modules at UNet bottleneck and decoder layers Implement feature modulation: output =  $AdaIN(features, dmc\_proj(dmc))$ Add multi-scale DMC injection points with curriculum-based evolution return Enhanced MGM

Algorithm 2 Foundation Pre-training

| <b>Require:</b> Large multimodal dataset $\mathcal{T}$                                       |
|----------------------------------------------------------------------------------------------|
| <b>Ensure:</b> Pretrained MGM $(G_{\phi})$ and Expert $(E_{\theta})$                         |
| Initialize SDXL-based MGM and CLIP-based Expert                                              |
| for epoch in range (pretrain_epochs) do                                                      |
| for batch in DataLoader( $\mathcal T$ , curriculum=True) do                                  |
| Train MGM with standard diffusion loss                                                       |
| $noise = 1$ torch.randn_like(batch.images)                                                   |
| $t = \text{torch.random}(0, \text{num\_steps}, (\text{batch_size}))$                         |
| $loss\_diffusion = mse_loss(model(noise, t, batch.text), noise)$                             |
| Train Expert with contrastive loss                                                           |
| $img\_features = expert.encode\_image(batch.images)$                                         |
| $text_fext_{\text{features}} = \text{expert.encode\_text(batch.text)}$                       |
| $loss_{\text{contrastive}} = \text{inforce_loss}(\text{img_features}, \text{text_features})$ |
| Update parameters                                                                            |
| end for                                                                                      |
| end for                                                                                      |
| return $G_{\phi}$ , $E_{\theta}$                                                             |

### Algorithm 3 Multimodal Knowledge Condensation

**Require:** Pretrained  $G_{\phi}$ ,  $E_{\theta}$ , Dataset  $\mathcal{T}$ , Number of DMCs K **Ensure:** Optimized DMCs  $\mathcal{C}$ Initialize C by K-means on  $\{E(x_i)\}\$ for  $t = 1$  to  $T_{\text{distill}}$  do Sample real batch  $R$  of size  $B$ Sample  $B$  prototypes from  $C$ Generate synthetic batch  $\tilde{x}_j = G_{\phi'}(z_j, c_{k_j})$ Build cost matrix  $D_{ij} = [1 - \cos(E(x_i), E(\tilde{x}_j))] + \text{CE}(y_i, \hat{y}_j)$  (+  $d_{\text{pyr}}$  if used) Solve Sinkhorn for  $\Pi^*$  on D (batch marginals =  $1/B$ ) Compute  $\mathcal{L}_{OT} = \langle \Pi^*, D \rangle$ ,  $\mathcal{L}_{MI}$  over the B prototypes,  $\mathcal{L}_{div}$  over  $\mathcal{C}_{UT}$  $\mathcal{L}_{\text{total}} = \mathcal{L}_{\text{OT}} + \alpha \mathcal{L}_{\text{MI}} + \beta \mathcal{L}_{\text{div}}$ Backprop  $\rightarrow$  update  $C$  (+ LoRA) end for return Optimized C

The Sinkhorn algorithm should efficiently solve the optimal transport problem with differentiable operations. The unified loss balances distribution matching, cross-modal alignment, and diversity. This approach significantly reduces computational requirements compared to joint optimization.

Algorithm 4: Dynamic Synthetic Data Generation

| <b>Algorithm 4 Dynamic Synthetic Data Generation</b>                                                                                        |
|---------------------------------------------------------------------------------------------------------------------------------------------|
| Require: Optimized DMCs $C$ , Trained MGM $G_{\phi}$ , Target dataset size $N$                                                              |
| Ensure: Synthetic multimodal dataset $S_{\text{gen}}$                                                                                       |
| Initialize $S_{\text{gen}} = []$                                                                                                            |
| for $i = 0$ to $N$ step $batch\_size$ do                                                                                                    |
| $batch\_N = \min(\text{batch}\_\text{size}, N - i)$                                                                                         |
| $dmc\_indices = \text{adaptive\_sample\_indices}(\mathcal{C}, \text{batch\_N}, \text{el2n\_scores}=\text{True})$                            |
| $sampled\_dmcs = [\text{interpolate\_dmcs}(\mathcal{C}[idx], \alpha=\text{random.uniform}(0,1)) \text{ for } idx \text{ in } dmc\_indices]$ |
| $noise = \text{entropy\_guided\_noise}(\text{batch\_N}, *\text{noise\_shape})$                                                              |
| $synthetic\_batch = G_{\phi}(\text{noise}, sampled\_dmcs)$                                                                                  |
| $synthetic\_batch = \text{apply\_online\_augmentation}(\text{synthetic\_batch})$                                                            |
| $S_{\text{gen}}.extend(\text{validate\_coherence}(\text{synthetic\_batch}))$                                                                |
| end for                                                                                                                                     |
| return $S_{\text{gen}}$                                                                                                                     |

This algorithm generates synthetic data on-demand with maximum diversity and quality. Adaptive sampling ensures balanced representation across different concepts. DMC interpolation creates hybrid concepts for enhanced diversity. Entropy-guided noise provides structured randomness that works well with the trained MGM. Online augmentation and coherence validation ensure highquality outputs. This flexible generation approach supports various dataset sizes and requirements.

#### 5.2 System Architecture

The GMDD system is implemented using PyTorch with Hugging Face Diffusers for the generative components. The architecture supports distributed training on multiple GPUs with mixed-precision optimization for efficiency. Key implementation details include:

Hardware Requirements: The system requires NVIDIA A100 or H100 GPUs with at least 80GB VRAM for optimal performance. Multi-GPU setups are supported through PyTorch Distributed Data Parallel (DDP).

**Software Dependencies:** The implementation builds on PyTorch  $2.0+$ , Transformers  $4.30+$ , Diffusers  $0.21+$ , and custom optimization libraries for efficient DMC updates.

Scalability Features: The system includes gradient checkpointing, mixed-precision training, and efficient memory management to handle large-scale datasets and models.

## 6 Experimental Design and Evaluation

### 6.1 Performance Metrics

The evaluation of GMDD encompasses multiple dimensions to ensure comprehensive assessment of the framework's effectiveness:

Downstream Task Performance: We evaluate the quality of distilled datasets through their performance on downstream tasks including image-text retrieval (measured by Recall@K), multimodal classification (Top-1 accuracy), image captioning (BLEU, CIDEr, ROUGE scores), and Visual Question Answering (VQA accuracy).

Efficiency Metrics: Storage reduction ratio, training time comparison, memory usage during distillation, and generation speed for synthetic data are measured to assess practical applicability.

**Quality Metrics:** Fréchet Inception Distance (FID) for image quality, Inception Score (IS) for diversity, LPIPS for perceptual similarity, CLIP alignment score for cross-modal consistency, and semantic preservation metrics ensure high-quality synthetic data.

#### 6.2 Datasets and Benchmarks

The experimental evaluation will be conducted on standard multimodal benchmarks including Flickr30K, MS COCO, Conceptual Captions, and VQA 2.0. These datasets provide diverse scenarios for testing the framework's generalization capabilities across different domains and tasks.

#### 6.3 Baseline Comparisons

GMDD is compared against several categories of baseline methods:

Coreset Selection Methods: Traditional data selection approaches including random sampling, K-Center selection, herding-based selection, and forgetting-based selection serve as efficiency baselines.

Recent Multimodal Distillation: Direct comparison with existing multimodal distillation methods

The comprehensive evaluation framework will ensure that GMDD's performance validated across multiple dimensions and use cases, providing strong evidence for its effectiveness in practical applications.

## 7 Expected Contributions and Impact

#### 7.1 Technical Contributions

Methodological Innovations: GMDD introduces the novel concept of latent concept distillation for multimodal data, which enables dynamic generation of synthetic samples rather than fixed synthetic datasets. This paradigm technique addresses fundamental limitations in existing approaches.

Algorithmic Advances: We assume that the framework contributes efficient MKC algorithms, feature enrichment techniques, and adaptive sampling strategies that significantly improve the quality and diversity of distilled multimodal concepts.

Architectural Innovations: The integration of advanced generative models with multimodal distillation creates new possibilities for scalable and efficient multimodal learning.

# References

Liu, P. and Du, J. (2025). The evolution of dataset distillation: toward scalable and generalizable solutions. *arXiv preprint arXiv:2502.05673*.

Kaddour, J. and Liu, Q. (2023). Synthetic data generation in Low-Resource settings via Fine-Tuning of large language models. arXiv preprint arXiv:2310.01119.

SDXL: Improving Latent Diffusion Models for High-Resolution Image Synthesis. (2023). arXiv preprint arXiv:2307.01952.

Genevay, A., Chizat, L., Bach, F., Cuturi, M., and Peyré, G. (2018). Sample Complexity of Sinkhorn divergences. arXiv preprint arXiv:1810.02733.

Luise, G., Salzo, S., Pontil, M., and Ciliberto, C. (2019). Sinkhorn Barycenters with Free Support via Frank-Wolfe Algorithm. Proceedings of Neural Information Processing Systems.

Cuturi, M. (2013). Sinkhorn Distances: Lightspeed computation of optimal transportation distances. arXiv preprint arXiv:1306.0895.

Lin, T.-Y., Dollár, P., Girshick, R., He, K., Hariharan, B., and Belongie, S. (2017). Feature Pyramid Networks for Object Detection. 2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR).