{"table_of_contents": [{"title": "Project: Generative Multimodal Dataset Distillation (GMDD) via\nLatent Concept Distillation Proposal", "heading_level": null, "page_id": 0, "polygon": [[76.5, 113.115234375], [536.09765625, 113.115234375], [536.09765625, 150.8203125], [76.5, 150.8203125]]}, {"title": "1 Introduction and Problem Statement", "heading_level": null, "page_id": 0, "polygon": [[70.5, 265.869140625], [360.984375, 265.869140625], [360.984375, 280.177734375], [70.5, 280.177734375]]}, {"title": "1.1 Background", "heading_level": null, "page_id": 0, "polygon": [[70.5, 292.939453125], [175.7109375, 292.939453125], [175.7109375, 304.927734375], [70.5, 304.927734375]]}, {"title": "1.2 Problem Statement", "heading_level": null, "page_id": 0, "polygon": [[70.5, 313.435546875], [218.25, 313.435546875], [218.25, 325.423828125], [70.5, 325.423828125]]}, {"title": "2 Literature Review", "heading_level": null, "page_id": 0, "polygon": [[70.5, 529.41796875], [225.017578125, 529.41796875], [225.017578125, 543.33984375], [70.5, 543.33984375]]}, {"title": "2.1 Dataset Distillation Foundations", "heading_level": null, "page_id": 0, "polygon": [[70.5, 555.75], [294.345703125, 555.75], [294.345703125, 567.703125], [70.5, 567.703125]]}, {"title": "2.2 Multimodal Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[70.5, 660.0], [291.75, 660.0], [291.75, 672.1171875], [70.5, 672.1171875]]}, {"title": "2.3 Generative Models for Multimodal Synthesis", "heading_level": null, "page_id": 1, "polygon": [[70.5, 117.0], [367.55859375, 117.0], [367.55859375, 128.00390625], [70.5, 128.00390625]]}, {"title": "3 Proposed Method: GMDD Framework", "heading_level": null, "page_id": 1, "polygon": [[70.5, 210.568359375], [371.25, 210.568359375], [371.25, 224.490234375], [70.5, 224.490234375]]}, {"title": "3.1 Core Innovation", "heading_level": null, "page_id": 1, "polygon": [[70.5, 237.0], [197.6748046875, 237.0], [197.6748046875, 248.466796875], [70.5, 248.466796875]]}, {"title": "3.2 Framework Overview", "heading_level": null, "page_id": 1, "polygon": [[70.5, 435.75], [228.005859375, 435.75], [228.005859375, 446.66015625], [70.5, 446.66015625]]}, {"title": "3.3 Technical Architecture", "heading_level": null, "page_id": 1, "polygon": [[70.5, 566.9296875], [236.373046875, 566.9296875], [236.373046875, 577.7578125], [70.5, 577.7578125]]}, {"title": "4 Mathematical Formulation", "heading_level": null, "page_id": 2, "polygon": [[70.5, 214.5], [285.0, 214.5], [285.0, 227.583984375], [70.5, 227.583984375]]}, {"title": "4.1 Dataset and Concept Definitions", "heading_level": null, "page_id": 2, "polygon": [[70.5, 282.75], [295.5, 282.75], [295.5, 293.712890625], [70.5, 293.712890625]]}, {"title": "4.2 Core Optimization Objective", "heading_level": null, "page_id": 2, "polygon": [[70.5, 523.5], [273.75, 523.5], [273.75, 534.83203125], [70.5, 534.83203125]]}, {"title": "4.3 Entropic Optimal Transport", "heading_level": null, "page_id": 3, "polygon": [[70.5, 74.25], [269.25, 74.25], [269.25, 85.658203125], [70.5, 85.658203125]]}, {"title": "4.4 Sample-to-Prototype Distance", "heading_level": null, "page_id": 3, "polygon": [[70.5, 308.25], [281.25, 308.25], [281.25, 319.236328125], [70.5, 319.236328125]]}, {"title": "4.5 Mutual Information Loss", "heading_level": null, "page_id": 3, "polygon": [[70.5, 487.5], [249.75, 487.5], [249.75, 498.8671875], [70.5, 498.8671875]]}, {"title": "4.6 Diversity Regularization", "heading_level": null, "page_id": 4, "polygon": [[70.5, 74.25], [246.0, 74.25], [246.0, 85.46484375], [70.5, 85.46484375]]}, {"title": "4.7 Feature Pyramid Matching", "heading_level": null, "page_id": 4, "polygon": [[70.5, 241.5], [263.1181640625, 241.5], [263.1181640625, 253.30078125], [70.5, 253.30078125]]}, {"title": "5 Technical Implementation", "heading_level": null, "page_id": 4, "polygon": [[70.5, 423.0], [279.10546875, 423.0], [279.10546875, 435.05859375], [70.5, 435.05859375]]}, {"title": "5.1 Algorithm Design and Rationale", "heading_level": null, "page_id": 4, "polygon": [[70.5, 449.25], [294.75, 449.25], [294.75, 460.1953125], [70.5, 460.1953125]]}, {"title": "Algorithm 1: MGM Architecture Enhancement", "heading_level": null, "page_id": 4, "polygon": [[88.5, 497.25], [351.75, 497.25], [351.75, 507.76171875], [88.5, 507.76171875]]}, {"title": "Algorithm 2: Foundation Pre-training", "heading_level": null, "page_id": 4, "polygon": [[88.5, 592.5], [298.5, 592.5], [298.5, 602.89453125], [88.5, 602.89453125]]}, {"title": "Algorithm 3: Multimodal Knowledge Condensation (Revised)", "heading_level": null, "page_id": 4, "polygon": [[88.5, 673.5], [428.818359375, 673.5], [428.818359375, 684.10546875], [88.5, 684.10546875]]}, {"title": "Algorithm 3 Multimodal Knowledge Condensation", "heading_level": null, "page_id": 5, "polygon": [[71.25, 507.75], [323.25, 507.75], [323.25, 520.13671875], [71.25, 520.13671875]]}, {"title": "5.2 System Architecture", "heading_level": null, "page_id": 6, "polygon": [[70.5, 430.5], [224.25, 430.5], [224.25, 441.6328125], [70.5, 441.6328125]]}, {"title": "6 Experimental Design and Evaluation", "heading_level": null, "page_id": 6, "polygon": [[70.5, 605.98828125], [356.25, 605.98828125], [356.25, 619.13671875], [70.5, 619.13671875]]}, {"title": "6.1 Performance Metrics", "heading_level": null, "page_id": 6, "polygon": [[70.5, 631.5], [225.75, 631.5], [225.75, 643.5], [70.5, 643.5]]}, {"title": "6.2 Datasets and Benchmarks", "heading_level": null, "page_id": 7, "polygon": [[70.5, 185.25], [256.5, 185.25], [256.5, 195.873046875], [70.5, 195.873046875]]}, {"title": "6.3 Baseline Comparisons", "heading_level": null, "page_id": 7, "polygon": [[70.5, 261.0], [233.5341796875, 261.0], [233.5341796875, 271.86328125], [70.5, 271.86328125]]}, {"title": "7 Expected Contributions and Impact", "heading_level": null, "page_id": 7, "polygon": [[70.5, 423.0], [351.0, 423.0], [351.0, 435.83203125], [70.5, 435.83203125]]}, {"title": "7.1 Technical Contributions", "heading_level": null, "page_id": 7, "polygon": [[71.25, 448.98046875], [244.5908203125, 448.98046875], [244.5908203125, 459.80859375], [71.25, 459.80859375]]}, {"title": "References", "heading_level": null, "page_id": 7, "polygon": [[70.5, 597.75], [148.0693359375, 597.75], [148.0693359375, 610.62890625], [70.5, 610.62890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 33], ["Text", 9], ["SectionHeader", 7], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6953, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 40], ["Text", 10], ["SectionHeader", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 345], ["Line", 42], ["Text", 8], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 62], ["Text", 7], ["Equation", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 49], ["SectionHeader", 7], ["Text", 7], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 43], ["TableCell", 17], ["Text", 2], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1562, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 41], ["TableCell", 28], ["Text", 9], ["SectionHeader", 3], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2006, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 39], ["Text", 14], ["SectionHeader", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 32], ["Line", 10], ["Text", 4], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Background_report_<PERSON>jan <PERSON>"}