{"table_of_contents": [{"title": "2 Probability", "heading_level": null, "page_id": 0, "polygon": [[84.515625, 95.712890625], [216.0, 95.712890625], [213.0, 150.75], [82.5, 142.857421875]]}, {"title": "2.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 206.25], [195.75, 207.0], [195.75, 218.162109375], [100.5, 218.162109375]]}, {"title": "2.2 A brief review of probability theory", "heading_level": null, "page_id": 1, "polygon": [[99.0, 282.75], [315.75, 282.75], [315.75, 295.048828125], [99.0, 295.048828125]]}, {"title": "2.2.1 Discrete random variables", "heading_level": null, "page_id": 1, "polygon": [[93.75, 356.25], [255.0, 356.25], [255.0, 367.34765625], [93.75, 367.34765625]]}, {"title": "2.2.2 Fundamental rules", "heading_level": null, "page_id": 1, "polygon": [[92.109375, 558.0], [218.671875, 558.0], [218.671875, 568.265625], [92.109375, 568.265625]]}, {"title": "2.2.2.1 Probability of a union of two events", "heading_level": null, "page_id": 2, "polygon": [[87.3984375, 60.75], [288.0, 60.75], [288.0, 71.46826171875], [87.3984375, 71.46826171875]]}, {"title": "2.2.2.2 Joint probabilities", "heading_level": null, "page_id": 2, "polygon": [[86.90625, 141.0], [208.5, 141.0], [208.5, 151.**********], [86.90625, 151.**********]]}, {"title": "2.2.2.3 Conditional probability", "heading_level": null, "page_id": 2, "polygon": [[87.609375, 351.0], [231.75, 351.0], [231.75, 361.3359375], [87.609375, 361.3359375]]}, {"title": "2.2.3 <PERSON><PERSON> rule", "heading_level": null, "page_id": 2, "polygon": [[92.0390625, 426.75], [180.75, 426.75], [180.75, 437.58984375], [92.0390625, 437.58984375]]}, {"title": "2.2.3.1 Example: medical diagnosis", "heading_level": null, "page_id": 2, "polygon": [[88.5, 514.5], [252.0, 514.5], [252.0, 524.6015625], [88.5, 524.6015625]]}, {"title": "2.2.3.2 Example: Generative classifiers", "heading_level": null, "page_id": 3, "polygon": [[86.9765625, 354.75], [266.25, 354.75], [266.25, 364.5], [86.9765625, 364.5]]}, {"title": "2.2.4 Independence and conditional independence", "heading_level": null, "page_id": 3, "polygon": [[93.0, 509.25], [342.75, 509.25], [342.75, 519.22265625], [93.0, 519.22265625]]}, {"title": "2.2.5 Continuous random variables", "heading_level": null, "page_id": 5, "polygon": [[93.0, 136.5], [270.75, 136.5], [270.75, 146.49609375], [93.0, 146.49609375]]}, {"title": "2.2.6 Quantiles", "heading_level": null, "page_id": 6, "polygon": [[91.125, 272.25], [176.25, 272.25], [176.25, 282.234375], [91.125, 282.234375]]}, {"title": "2.2.7 Mean and variance", "heading_level": null, "page_id": 6, "polygon": [[92.953125, 477.0], [221.25, 477.0], [221.25, 486.6328125], [92.953125, 486.6328125]]}, {"title": "2.3 Some common discrete distributions", "heading_level": null, "page_id": 7, "polygon": [[99.0, 228.75], [321.0, 228.75], [321.0, 240.46875], [99.0, 240.46875]]}, {"title": "2.3.1 The binomial and <PERSON><PERSON><PERSON> distributions", "heading_level": null, "page_id": 7, "polygon": [[94.5, 290.25], [323.25, 290.25], [323.25, 300.90234375], [94.5, 300.90234375]]}, {"title": "2.3.2 The multinomial and multinoulli distributions", "heading_level": null, "page_id": 8, "polygon": [[93.0, 242.25], [347.25, 242.25], [347.25, 253.283203125], [93.0, 253.283203125]]}, {"title": "2.3.2.1 Application: DNA sequence motifs", "heading_level": null, "page_id": 9, "polygon": [[88.2421875, 393.0], [277.5, 393.0], [277.5, 402.78515625], [88.2421875, 402.78515625]]}, {"title": "2.3.3 The Poisson distribution", "heading_level": null, "page_id": 10, "polygon": [[92.953125, 251.25], [246.0, 251.25], [246.0, 261.3515625], [92.953125, 261.3515625]]}, {"title": "2.3.4 The empirical distribution", "heading_level": null, "page_id": 10, "polygon": [[92.8125, 376.5], [254.25, 376.5], [254.25, 386.6484375], [92.8125, 386.6484375]]}, {"title": "2.4 Some common continuous distributions", "heading_level": null, "page_id": 11, "polygon": [[99.75, 60.75], [337.5, 60.75], [337.5, 71.7451171875], [99.75, 71.7451171875]]}, {"title": "2.4.1 Gaussian (normal) distribution", "heading_level": null, "page_id": 11, "polygon": [[94.5, 122.25], [275.25, 122.25], [275.25, 132.2578125], [94.5, 132.2578125]]}, {"title": "2.4.2 Degenerate pdf", "heading_level": null, "page_id": 12, "polygon": [[93.0, 135.75], [203.25, 135.75], [203.25, 146.654296875], [93.0, 146.654296875]]}, {"title": "2.4.3 The Laplace distribution", "heading_level": null, "page_id": 14, "polygon": [[93.0, 297.75], [246.0, 297.75], [246.0, 308.1796875], [93.0, 308.1796875]]}, {"title": "2.4.4 The gamma distribution", "heading_level": null, "page_id": 14, "polygon": [[93.0, 471.75], [245.25, 471.75], [245.25, 481.5703125], [93.0, 481.5703125]]}, {"title": "2.4.5 The beta distribution", "heading_level": null, "page_id": 15, "polygon": [[92.953125, 463.5], [229.5, 463.5], [229.5, 473.34375], [92.953125, 473.34375]]}, {"title": "2.4.6 Pareto distribution", "heading_level": null, "page_id": 16, "polygon": [[93.0, 329.25], [219.75, 329.25], [219.75, 339.8203125], [93.0, 339.8203125]]}, {"title": "2.5 Joint probability distributions", "heading_level": null, "page_id": 17, "polygon": [[99.0, 277.5], [284.25, 277.5], [284.25, 289.669921875], [99.0, 289.669921875]]}, {"title": "2.5.1 Covariance and correlation", "heading_level": null, "page_id": 17, "polygon": [[94.5, 459.75], [259.5, 459.75], [259.5, 469.86328125], [94.5, 469.86328125]]}, {"title": "2.5.2 The multivariate Gaussian", "heading_level": null, "page_id": 19, "polygon": [[92.3203125, 232.5], [252.75, 232.5], [252.75, 242.68359375], [92.3203125, 242.68359375]]}, {"title": "2.5.3 Multivariate Student t distribution", "heading_level": null, "page_id": 19, "polygon": [[92.953125, 449.25], [292.5, 449.25], [292.5, 459.10546875], [92.953125, 459.10546875]]}, {"title": "2.5.4 Dirichlet distribution", "heading_level": null, "page_id": 20, "polygon": [[92.8828125, 432.0], [231.75, 432.0], [231.75, 443.28515625], [92.8828125, 443.28515625]]}, {"title": "2.6 Transformations of random variables", "heading_level": null, "page_id": 22, "polygon": [[99.0, 303.75], [323.25, 303.75], [323.25, 314.666015625], [99.0, 314.666015625]]}, {"title": "2.6.1 Linear transformations", "heading_level": null, "page_id": 22, "polygon": [[93.0, 364.5], [240.0, 364.5], [240.0, 374.30859375], [93.0, 374.30859375]]}, {"title": "2.6.2 General transformations", "heading_level": null, "page_id": 23, "polygon": [[91.5, 123.75], [246.0, 123.75], [246.0, 133.7607421875], [91.5, 133.7607421875]]}, {"title": "2.6.2.1 Multivariate change of variables *", "heading_level": null, "page_id": 23, "polygon": [[87.75, 509.25], [276.0, 509.25], [276.0, 518.90625], [87.75, 518.90625]]}, {"title": "2.6.3 Central limit theorem", "heading_level": null, "page_id": 24, "polygon": [[91.5, 381.0], [234.0, 381.0], [234.0, 392.02734375], [91.5, 392.02734375]]}, {"title": "2.7 Monte Carlo approximation", "heading_level": null, "page_id": 25, "polygon": [[99.75, 444.75], [273.0, 444.75], [273.0, 455.94140625], [99.75, 455.94140625]]}, {"title": "2.7.1 Example: change of variables, the MC way", "heading_level": null, "page_id": 26, "polygon": [[94.5, 511.5], [331.5, 511.5], [331.5, 522.0703125], [94.5, 522.0703125]]}, {"title": "2.7.2 Example: estimating π by Monte <PERSON> integration", "heading_level": null, "page_id": 27, "polygon": [[92.4609375, 264.75], [370.5, 264.75], [370.5, 275.2734375], [92.4609375, 275.2734375]]}, {"title": "2.7.3 Accuracy of Monte Carlo approximation", "heading_level": null, "page_id": 27, "polygon": [[93.0, 532.5], [319.5, 532.5], [319.5, 542.63671875], [93.0, 542.63671875]]}, {"title": "2.8 Information theory", "heading_level": null, "page_id": 29, "polygon": [[99.0, 144.75], [230.34375, 144.75], [230.34375, 155.9091796875], [99.0, 155.9091796875]]}, {"title": "2.8.1 Entropy", "heading_level": null, "page_id": 29, "polygon": [[93.75, 361.5], [167.25, 361.5], [167.25, 371.77734375], [93.75, 371.77734375]]}, {"title": "2.8.2 KL divergence", "heading_level": null, "page_id": 30, "polygon": [[91.5, 365.25], [198.0, 365.25], [198.0, 375.2578125], [91.5, 375.2578125]]}, {"title": "Theorem 2.8.1. (Information inequality) \\mathbb{KL}(p||q) \\ge 0 with equality iff p = q.", "heading_level": null, "page_id": 31, "polygon": [[129.75, 153.0], [453.0, 153.0], [453.0, 162.94921875], [129.75, 162.94921875]]}, {"title": "2.8.3 Mutual information", "heading_level": null, "page_id": 32, "polygon": [[92.25, 60.75], [222.75, 60.75], [222.75, 71.5869140625], [92.25, 71.5869140625]]}, {"title": "2.8.3.1 Mutual information for continuous random variables *", "heading_level": null, "page_id": 32, "polygon": [[87.75, 499.5], [366.75, 499.5], [366.75, 509.4140625], [87.75, 509.4140625]]}, {"title": "Exercises", "heading_level": null, "page_id": 34, "polygon": [[129.75, 231.0], [179.15625, 231.0], [179.15625, 241.41796875], [129.75, 241.41796875]]}, {"title": "Exercise 2.2 Legal reasoning", "heading_level": null, "page_id": 34, "polygon": [[129.75, 371.25], [235.6875, 371.25], [235.6875, 380.63671875], [129.75, 380.63671875]]}, {"title": "Exercise 2.3 Variance of a sum", "heading_level": null, "page_id": 34, "polygon": [[129.75, 501.0], [245.25, 501.0], [245.25, 510.6796875], [129.75, 510.6796875]]}, {"title": "Exercise 2.4 <PERSON><PERSON> rule for medical diagnosis", "heading_level": null, "page_id": 34, "polygon": [[129.75, 540.0], [296.15625, 540.0], [296.15625, 549.59765625], [129.75, 549.59765625]]}, {"title": "Exercise 2.6 Conditional independence", "heading_level": null, "page_id": 35, "polygon": [[129.75, 225.0], [274.5, 225.0], [274.5, 233.666015625], [129.75, 233.666015625]]}, {"title": "Exercise 2.12 Expressing mutual information in terms of entropies", "heading_level": null, "page_id": 37, "polygon": [[129.75, 62.25], [372.65625, 62.25], [372.65625, 72.10107421875], [129.75, 72.10107421875]]}, {"title": "Exercise 2.13 Mutual information for correlated normals", "heading_level": null, "page_id": 37, "polygon": [[129.515625, 114.75], [335.25, 114.75], [335.25, 124.03125], [129.515625, 124.03125]]}, {"title": "Exercise 2.14 A measure of correlation (normalized mutual information)", "heading_level": null, "page_id": 37, "polygon": [[129.75, 296.25], [391.5, 296.25], [391.5, 305.96484375], [129.75, 305.96484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 32], ["Text", 5], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 38], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 718, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 40], ["Text", 8], ["Equation", 7], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 37], ["Equation", 7], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 33], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 35], ["Equation", 6], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["Line", 38], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 62], ["Text", 9], ["Equation", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 64], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 831, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 67], ["TableCell", 32], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3271, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 54], ["Equation", 4], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 52], ["Text", 5], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 50], ["Text", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 66], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1765, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 57], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 886, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 50], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 45], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 865, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 927, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 75], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 584], ["Line", 63], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 72], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 881, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1780, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 553], ["Line", 46], ["TextInlineMath", 7], ["Equation", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 668], ["Line", 65], ["Text", 6], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 570], ["Line", 67], ["Text", 9], ["Equation", 9], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 169], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 47], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 42], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 693, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 73], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 43], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 39], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 581], ["Line", 61], ["TextInlineMath", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 48], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 77], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 40], ["Text", 6], ["ListItem", 6], ["SectionHeader", 4], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 40], ["Text", 15], ["ListItem", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 598], ["Line", 62], ["TextInlineMath", 9], ["Text", 7], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 48], ["Text", 6], ["Equation", 6], ["TextInlineMath", 6], ["SectionHeader", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 971, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-3"}