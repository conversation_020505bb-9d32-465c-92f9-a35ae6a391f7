Image /page/0/Picture/0 description: The image is a title page with the number 13 in large red font at the top. Below the number 13, the words "Sequential Data" are written in large black font. The background of the image is a blurry, abstract pattern of golden brown and white, resembling rippling water or a distorted reflection.

So far in this book, we have focussed primarily on sets of data points that were assumed to be independent and identically distributed (i.i.d.). This assumption allowed us to express the likelihood function as the product over all data points of the probability distribution evaluated at each data point. For many applications, however, the i.i.d. assumption will be a poor one. Here we consider a particularly important class of such data sets, namely those that describe sequential data. These often arise through measurement of time series, for example the rainfall measurements on successive days at a particular location, or the daily values of a currency exchange rate, or the acoustic features at successive time frames used for speech recognition. An example involving speech data is shown in Figure 13.1. Sequential data can also arise in contexts other than time series, for example the sequence of nucleotide base pairs along a strand of DNA or the sequence of characters in an English sentence. For convenience, we shall sometimes refer to 'past' and 'future' observations in a sequence. However, the models explored in this chapter are equally applicable to all

**Figure 13.1** Example of a spectrogram of the spoken words "<PERSON><PERSON>' theorem" showing a plot of the intensity of the spectral coefficients versus time index.

Image /page/1/Figure/2 description: The image displays a spectrogram and a waveform of a spoken utterance, along with its phonetic and word transcriptions. The top panel is a spectrogram showing frequency on the y-axis (from 0 to 10000 Hz) and time on the x-axis (from 0 to 1 second). The spectrogram uses a color scale to represent intensity, with blue indicating low intensity and red indicating high intensity. The middle panel is a waveform plot, with amplitude on the y-axis (ranging from -0.3 to 0.3) and time on the x-axis (from 0 to 1 second). The bottom panel provides a phonetic transcription of the utterance as "b | ey | z | th | ih | er | em" and a word transcription as "Bayes' | Theorem". The phonetic and word transcriptions are aligned with the corresponding segments of the audio signal shown in the spectrogram and waveform.

forms of sequential data, not just temporal sequences.

It is useful to distinguish between stationary and nonstationary sequential distributions. In the stationary case, the data evolves in time, but the distribution from which it is generated remains the same. For the more complex nonstationary situation, the generative distribution itself is evolving with time. Here we shall focus on the stationary case.

For many applications, such as financial forecasting, we wish to be able to predict the next value in a time series given observations of the previous values. Intuitively, we expect that recent observations are likely to be more informative than more historical observations in predicting future values. The example in Figure 13.1 shows that successive observations of the speech spectrum are indeed highly correlated. Furthermore, it would be impractical to consider a general dependence of future observations on all previous observations because the complexity of such a model would grow without limit as the number of observations increases. This leads us to consider *Markov models* in which we assume that future predictions are inde-

**Figure 13.2** The simplest approach to . . . . . . modelling a sequence of ob- $\mathbf{x}_1 \bullet \mathbf{x}_2 \bullet \mathbf{x}_3 \bullet \mathbf{x}_4$ servations is to treat them as independent, corresponding to a graph without links.

pendent of all but the most recent observations.

Although such models are tractable, they are also severely limited. We can obtain a more general framework, while still retaining tractability, by the introduction of latent variables, leading to *state space models*. As in Chapters 9 and 12, we shall see that complex models can thereby be constructed from simpler components (in particular, from distributions belonging to the exponential family) and can be readily characterized using the framework of probabilistic graphical models. Here we focus on the two most important examples of state space models, namely the *hidden Markov model*, in which the latent variables are discrete, and *linear dynamical systems*, in which the latent variables are Gaussian. Both models are described by directed graphs having a tree structure (no loops) for which inference can be performed efficiently using the sum-product algorithm.

# Markov Models

The easiest way to treat sequential data would be simply to ignore the sequential aspects and treat the observations as i.i.d., corresponding to the graph in Figure 13.2. Such an approach, however, would fail to exploit the sequential patterns in the data, such as correlations between observations that are close in the sequence. Suppose, for instance, that we observe a binary variable denoting whether on a particular day it rained or not. Given a time series of recent observations of this variable, we wish to predict whether it will rain on the next day. If we treat the data as i.i.d., then the only information we can glean from the data is the relative frequency of rainy days. However, we know in practice that the weather often exhibits trends that may last for several days. Observing whether or not it rains today is therefore of significant help in predicting if it will rain tomorrow.

To express such effects in a probabilistic model, we need to relax the i.i.d. assumption, and one of the simplest ways to do this is to consider a *Markov model*. First of all we note that, without loss of generality, we can use the product rule to express the joint distribution for a sequence of observations in the form

$$
p(\mathbf{x}_1,\ldots,\mathbf{x}_N)=\prod_{n=1}^N p(\mathbf{x}_n|\mathbf{x}_1,\ldots,\mathbf{x}_{n-1}).
$$
 (13.1)

If we now assume that each of the conditional distributions on the right-hand side is independent of all previous observations except the most recent, we obtain the *first-order Markov chain*, which is depicted as a graphical model in Figure 13.3. The

**Figure 13.3** A first-order Markov chain of observations  $\{x_n\}$  in which the distribution  $p(\mathbf{x}_n|\mathbf{x}_{n-1})$  of a particular observation  $x_n$  is conditioned on the value of the previous observation **x**<sub>n−1</sub>.

Image /page/3/Figure/2 description: A diagram shows four blue circles with red outlines arranged horizontally from left to right. Each circle is labeled with an 'x' followed by a subscript number: x1, x2, x3, and x4. Red arrows connect the circles sequentially, indicating a directional flow from x1 to x2, x2 to x3, and x3 to x4. An arrow continues to the right from x4, suggesting the sequence continues beyond the visible elements.

joint distribution for a sequence of  $N$  observations under this model is given by

$$
p(\mathbf{x}_1,\ldots,\mathbf{x}_N)=p(\mathbf{x}_1)\prod_{n=2}^N p(\mathbf{x}_n|\mathbf{x}_{n-1}).
$$
 (13.2)

Section 8.2 From the d-separation property, we see that the conditional distribution for observation  $x_n$ , given all of the observations up to time n, is given by

$$
p(\mathbf{x}_n|\mathbf{x}_1,\ldots,\mathbf{x}_{n-1})=p(\mathbf{x}_n|\mathbf{x}_{n-1})
$$
\n(13.3)

*Exercise 13.1*

which is easily verified by direct evaluation starting from  $(13.2)$  and using the prod-*Exercise 13.1* uct rule of probability. Thus if we use such a model to predict the next observation in a sequence, the distribution of predictions will depend only on the value of the immediately preceding observation and will be independent of all earlier observations.

In most applications of such models, the conditional distributions  $p(\mathbf{x}_n|\mathbf{x}_{n-1})$ that define the model will be constrained to be equal, corresponding to the assumption of a stationary time series. The model is then known as a *homogeneous* Markov chain. For instance, if the conditional distributions depend on adjustable parameters (whose values might be inferred from a set of training data), then all of the conditional distributions in the chain will share the same values of those parameters.

Although this is more general than the independence model, it is still very restrictive. For many sequential observations, we anticipate that the trends in the data over several successive observations will provide important information in predicting the next value. One way to allow earlier observations to have an influence is to move to higher-order Markov chains. If we allow the predictions to depend also on the previous-but-one value, we obtain a second-order Markov chain, represented by the graph in Figure 13.4. The joint distribution is now given by

$$
p(\mathbf{x}_1,\ldots,\mathbf{x}_N)=p(\mathbf{x}_1)p(\mathbf{x}_2|\mathbf{x}_1)\prod_{n=3}^N p(\mathbf{x}_n|\mathbf{x}_{n-1},\mathbf{x}_{n-2}).
$$
 (13.4)

Again, using d-separation or by direct evaluation, we see that the conditional distribution of  $\mathbf{x}_n$  given  $\mathbf{x}_{n-1}$  and  $\mathbf{x}_{n-2}$  is independent of all observations  $\mathbf{x}_1, \ldots, \mathbf{x}_{n-3}$ .

**Figure 13.4** A second-order Markov chain, in which the conditional distribution of a particular observation **x**<sup>n</sup> depends on the values of the two previous observations **x**<sup>n</sup>−<sup>1</sup> and **x**<sup>n</sup>−<sup>2</sup>.

Image /page/3/Figure/14 description: A diagram shows four blue circles labeled x1, x2, x3, and x4 from left to right. Red arrows connect the circles, indicating a directed graph. There are horizontal arrows pointing from x1 to x2, x2 to x3, and x3 to x4. Additionally, there are curved red arrows that go from x1 to x3, x2 to x4, and x3 to a point beyond x4, suggesting a temporal or sequential relationship with some form of skip connection or feedback.

**13.1. Markov Models 609**

**Figure 13.5** We can represent sequential data using a Markov chain of latent variables, with each observation conditioned on the state of the corresponding latent variable. This important graphical structure forms the foundation both for the hidden Markov model and for linear dynamical systems.

Image /page/4/Figure/2 description: This is a diagram illustrating a hidden Markov model. It shows a sequence of hidden states, denoted by z1, z2, ..., zn-1, zn, zn+1, represented by white circles. Each hidden state z\_i is connected to the next hidden state z\_{i+1} by a directed arrow, indicating the temporal dependency. Below each hidden state, there is an observed state, denoted by x1, x2, ..., xn-1, xn, xn+1, represented by blue circles. A directed arrow connects each hidden state z\_i to its corresponding observed state x\_i, indicating that the observed state is dependent on the hidden state. The diagram visually represents the probabilistic relationships between the hidden states and the observed states over time.

Each observation is now influenced by two previous observations. We can similarly consider extensions to an  $M<sup>th</sup>$  order Markov chain in which the conditional distribution for a particular variable depends on the previous  $M$  variables. However, we have paid a price for this increased flexibility because the number of parameters in the model is now much larger. Suppose the observations are discrete variables having K states. Then the conditional distribution  $p(\mathbf{x}_n|\mathbf{x}_{n-1})$  in a first-order Markov chain will be specified by a set of  $K - 1$  parameters for each of the K states of  $\mathbf{x}_{n-1}$ giving a total of  $K(K - 1)$  parameters. Now suppose we extend the model to an  $M<sup>th</sup>$  order Markov chain, so that the joint distribution is built up from conditionals  $p(\mathbf{x}_n|\mathbf{x}_{n-M},\ldots,\mathbf{x}_{n-1})$ . If the variables are discrete, and if the conditional distributions are represented by general conditional probability tables, then the number of parameters in such a model will have  $K^{M-1}(K-1)$  parameters. Because this grows exponentially with  $M$ , it will often render this approach impractical for larger values of M.

For continuous variables, we can use linear-Gaussian conditional distributions in which each node has a Gaussian distribution whose mean is a linear function of its parents. This is known as an *autoregressive* or *AR* model (Box *et al.*, 1994; Thiesson *et al.*, 2004). An alternative approach is to use a parametric model for  $p(\mathbf{x}_n|\mathbf{x}_{n-M},\ldots,\mathbf{x}_{n-1})$  such as a neural network. This technique is sometimes called a *tapped delay line* because it corresponds to storing (delaying) the previous M values of the observed variable in order to predict the next value. The number of parameters can then be much smaller than in a completely general model (for example it may grow linearly with  $M$ ), although this is achieved at the expense of a restricted family of conditional distributions.

Suppose we wish to build a model for sequences that is not limited by the Markov assumption to any order and yet that can be specified using a limited number of free parameters. We can achieve this by introducing additional latent variables to permit a rich class of models to be constructed out of simple components, as we did with mixture distributions in Chapter 9 and with continuous latent variable models in Chapter 12. For each observation  $x_n$ , we introduce a corresponding latent variable **<sup>z</sup>**<sup>n</sup> (which may be of different type or dimensionality to the observed variable). We now assume that it is the latent variables that form a Markov chain, giving rise to the graphical structure known as a *state space model*, which is shown in Figure 13.5. It satisfies the key conditional independence property that  $z_{n-1}$  and  $z_{n+1}$  are independent given  $z_n$ , so that

$$
\mathbf{z}_{n+1} \perp \!\!\!\perp \mathbf{z}_{n-1} \mid \mathbf{z}_n. \tag{13.5}
$$

The joint distribution for this model is given by

$$
p(\mathbf{x}_1,\ldots,\mathbf{x}_N,\mathbf{z}_1,\ldots,\mathbf{z}_N)=p(\mathbf{z}_1)\left[\prod_{n=2}^N p(\mathbf{z}_n|\mathbf{z}_{n-1})\right]\prod_{n=1}^N p(\mathbf{x}_n|\mathbf{z}_n).
$$
 (13.6)

Using the d-separation criterion, we see that there is always a path connecting any two observed variables  $x_n$  and  $x_m$  via the latent variables, and that this path is never blocked. Thus the predictive distribution  $p(\mathbf{x}_{n+1}|\mathbf{x}_1,\ldots,\mathbf{x}_n)$  for observation  $\mathbf{x}_{n+1}$ given all previous observations does not exhibit any conditional independence properties, and so our predictions for  $x_{n+1}$  depends on all previous observations. The observed variables, however, do not satisfy the Markov property at any order. We shall discuss how to evaluate the predictive distribution in later sections of this chapter.

There are two important models for sequential data that are described by this graph. If the latent variables are discrete, then we obtain the *hidden Markov model*, *Section 13.2* or *HMM* (Elliott *et al.*, 1995). Note that the observed variables in an HMM may be discrete or continuous, and a variety of different conditional distributions can be used to model them. If both the latent and the observed variables are Gaussian (with a linear-Gaussian dependence of the conditional distributions on their parents), then *Section 13.3* we obtain the *linear dynamical system*.

# Hidden Markov Models

The hidden Markov model can be viewed as a specific instance of the state space model of Figure 13.5 in which the latent variables are discrete. However, if we examine a single time slice of the model, we see that it corresponds to a mixture distribution, with component densities given by  $p(\mathbf{x}|\mathbf{z})$ . It can therefore also be interpreted as an extension of a mixture model in which the choice of mixture component for each observation is not selected independently but depends on the choice of component for the previous observation. The HMM is widely used in speech recognition (Jelinek, 1997; Rabiner and Juang, 1993), natural language modelling (Manning and Schütze, 1999), on-line handwriting recognition (Nag  $et al., 1986$ ), and for the analysis of biological sequences such as proteins and DNA (Krogh *et al.*, 1994; Durbin *et al.*, 1998; Baldi and Brunak, 2001).

As in the case of a standard mixture model, the latent variables are the discrete multinomial variables  $z_n$  describing which component of the mixture is responsible for generating the corresponding observation  $x_n$ . Again, it is convenient to use a 1-of-K coding scheme, as used for mixture models in Chapter 9. We now allow the probability distribution of  $z_n$  to depend on the state of the previous latent variable  $z_{n-1}$  through a conditional distribution  $p(z_n|z_{n-1})$ . Because the latent variables are K-dimensional binary variables, this conditional distribution corresponds to a table of numbers that we denote by **A**, the elements of which are known as *transition probabilities*. They are given by  $A_{ik} \equiv p(z_{nk} = 1 | z_{n-1,j} = 1)$ , and because they are probabilities, they satisfy  $0 \leq A_{jk} \leq 1$  with  $\sum_{k} A_{jk} = 1$ , so that the matrix **A** 

*Section 13.2*

*Section 13.3*

**Figure 13.6** Transition diagram showing a model whose latent variables have three possible states corresponding to the three boxes. The black lines denote the elements of the transition matrix  $A_{jk}$ .

Image /page/6/Picture/2 description: The image displays a directed graph with three nodes, each represented by a square and colored red, green, and blue. The red node is labeled "k = 1", the green node is labeled "k = 2", and the blue node is labeled "k = 3". Arrows indicate the direction of transitions between these nodes. Specifically, there are self-loops on each node, labeled A11, A22, and A33 for the red, green, and blue nodes, respectively. There are also directed edges between the nodes: A12 from red to green, A21 from green to red, A13 from red to blue, A31 from blue to red, A23 from green to blue, and A32 from blue to green.

has  $K(K-1)$  independent parameters. We can then write the conditional distribution explicitly in the form

$$
p(\mathbf{z}_n|\mathbf{z}_{n-1}, \mathbf{A}) = \prod_{k=1}^K \prod_{j=1}^K A_{jk}^{z_{n-1,j} z_{nk}}.
$$
 (13.7)

The initial latent node  $z_1$  is special in that it does not have a parent node, and so it has a marginal distribution  $p(\mathbf{z}_1)$  represented by a vector of probabilities  $\pi$  with elements  $\pi_k \equiv p(z_{1k} = 1)$ , so that

$$
p(\mathbf{z}_1|\boldsymbol{\pi}) = \prod_{k=1}^K \pi_k^{z_{1k}}
$$
\n(13.8)

where  $\sum_{k} \pi_k = 1$ .

The transition matrix is sometimes illustrated diagrammatically by drawing the states as nodes in a state transition diagram as shown in Figure 13.6 for the case of  $K = 3$ . Note that this does not represent a probabilistic graphical model, because the nodes are not separate variables but rather states of a single variable, and so we have shown the states as boxes rather than circles.

It is sometimes useful to take a state transition diagram, of the kind shown in Figure 13.6, and unfold it over time. This gives an alternative representation of the *Section 8.4.5* transitions between latent states, known as a *lattice* or *trellis* diagram, and which is shown for the case of the hidden Markov model in Figure 13.7.

> The specification of the probabilistic model is completed by defining the conditional distributions of the observed variables  $p(\mathbf{x}_n|\mathbf{z}_n, \phi)$ , where  $\phi$  is a set of parameters governing the distribution. These are known as *emission probabilities*, and might for example be given by Gaussians of the form (9.11) if the elements of **x** are continuous variables, or by conditional probability tables if **x** is discrete. Because  $\mathbf{x}_n$  is observed, the distribution  $p(\mathbf{x}_n|\mathbf{z}_n, \phi)$  consists, for a given value of  $\phi$ , of a vector of K numbers corresponding to the K possible states of the binary vector  $z_n$ .

*Section 8.4.5*

**Figure 13.7** If we unfold the state transition diagram of Figure 13.6 over time, we obtain a lattice, or trellis, representation of the latent states. Each column of this diagram corresponds to one of the latent variables **z**n.

Image /page/7/Figure/2 description: The image displays a layered network diagram with three distinct layers labeled k=1, k=2, and k=3. Each layer consists of a series of nodes represented by squares with thick colored borders: red for k=1, green for k=2, and blue for k=3. The nodes within each layer are connected horizontally by lines labeled A11 for the top layer and A33 for the bottom layer. The middle layer (k=2) has no horizontal labels. The diagram shows connections between nodes in adjacent layers, forming a bipartite graph structure. Specifically, nodes in layer k=1 are connected to nodes in layer k=2, and nodes in layer k=2 are connected to nodes in layer k=3. The connections between layers are represented by diagonal lines. The horizontal axis is indexed by n-2, n-1, n, and n+1, indicating a progression along the network. An arrow at the top of the image points to the right, suggesting a direction or flow through the network.

We can represent the emission probabilities in the form

$$
p(\mathbf{x}_n|\mathbf{z}_n,\boldsymbol{\phi}) = \prod_{k=1}^K p(\mathbf{x}_n|\boldsymbol{\phi}_k)^{z_{nk}}.
$$
 (13.9)

We shall focuss attention on *homogeneous* models for which all of the conditional distributions governing the latent variables share the same parameters **A**, and similarly all of the emission distributions share the same parameters  $\phi$  (the extension to more general cases is straightforward). Note that a mixture model for an i.i.d. data set corresponds to the special case in which the parameters  $A_{ik}$  are the same for all values of j, so that the conditional distribution  $p(\mathbf{z}_n|\mathbf{z}_{n-1})$  is independent of  $\mathbf{z}_{n-1}$ . This corresponds to deleting the horizontal links in the graphical model shown in Figure 13.5.

The joint probability distribution over both latent and observed variables is then given by

$$
p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\theta}) = p(\mathbf{z}_1 | \boldsymbol{\pi}) \left[ \prod_{n=2}^{N} p(\mathbf{z}_n | \mathbf{z}_{n-1}, \mathbf{A}) \right] \prod_{m=1}^{N} p(\mathbf{x}_m | \mathbf{z}_m, \boldsymbol{\phi}) \tag{13.10}
$$

where  $X = \{x_1, \ldots, x_N\}$ ,  $Z = \{z_1, \ldots, z_N\}$ , and  $\theta = \{\pi, A, \phi\}$  denotes the set of parameters governing the model. Most of our discussion of the hidden Markov model will be independent of the particular choice of the emission probabilities. Indeed, the model is tractable for a wide range of emission distributions including discrete tables, Gaussians, and mixtures of Gaussians. It is also possible to exploit *Exercise 13.4* discriminative models such as neural networks. These can be used to model the emission density  $p(x|z)$  directly, or to provide a representation for  $p(z|x)$  that can be converted into the required emission density  $p(\mathbf{x}|\mathbf{z})$  using Bayes' theorem (Bishop *et al.*, 2004).

> We can gain a better understanding of the hidden Markov model by considering it from a generative point of view. Recall that to generate samples from a mixture of

*Exercise 13.4*

Image /page/8/Figure/1 description: The image displays two plots side-by-side. The left plot shows three sets of concentric ellipses, colored red, blue, and green, labeled k=1, k=3, and k=2 respectively. These ellipses are centered around different points in a 2D space, with axes ranging from 0 to 1. The right plot shows a scatter plot of points, also colored red, blue, and green, connected by black lines, forming a network or trajectory. The axes in this plot also range from 0 to 1. The red points are clustered in the upper left, the blue points are clustered in the middle right, and the green points are clustered in the lower left.

**Figure 13.8** Illustration of sampling from a hidden Markov model having a 3-state latent variable **z** and a Gaussian emission model p(**x**|**z**) where **x** is 2-dimensional. (a) Contours of constant probability density for the emission distributions corresponding to each of the three states of the latent variable. (b) A sample of 50 points drawn from the hidden Markov model, colour coded according to the component that generated them and with lines connecting the successive observations. Here the transition matrix was fixed so that in any state there is a 5% probability of making a transition to each of the other states, and consequently a 90% probability of remaining in the same state.

Gaussians, we first chose one of the components at random with probability given by the mixing coefficients  $\pi_k$  and then generate a sample vector **x** from the corresponding Gaussian component. This process is repeated  $N$  times to generate a data set of  $N$  independent samples. In the case of the hidden Markov model, this procedure is modified as follows. We first choose the initial latent variable  $z_1$  with probabilities governed by the parameters  $\pi_k$  and then sample the corresponding observation  $\mathbf{x}_1$ . Now we choose the state of the variable  $z_2$  according to the transition probabilities  $p(\mathbf{z}_2|\mathbf{z}_1)$  using the already instantiated value of  $\mathbf{z}_1$ . Thus suppose that the sample for  $z_1$  corresponds to state j. Then we choose the state k of  $z_2$  with probabilities  $A_{ik}$ for  $k = 1, \ldots, K$ . Once we know  $\mathbf{z}_2$  we can draw a sample for  $\mathbf{x}_2$  and also sample the next latent variable  $z_3$  and so on. This is an example of ancestral sampling for *Section 8.1.2* a directed graphical model. If, for instance, we have a model in which the diagonal transition elements  $A_{kk}$  are much larger than the off-diagonal elements, then a typical data sequence will have long runs of points generated from a single component, with infrequent transitions from one component to another. The generation of samples from a hidden Markov model is illustrated in Figure 13.8.

> There are many variants of the standard HMM model, obtained for instance by imposing constraints on the form of the transition matrix **A** (Rabiner, 1989). Here we mention one of particular practical importance called the *left-to-right* HMM, which is obtained by setting the elements  $A_{ik}$  of **A** to zero if  $k < j$ , as illustrated in the

*Section 8.1.2*

**Figure 13.9** Example of the state transition diagram for a 3-state left-to-right hidden Markov model. Note that once a state has been vacated, it cannot later be re-entered.

Image /page/9/Figure/2 description: A diagram shows three states labeled k=1, k=2, and k=3. State k=1 is represented by a red square with a loop labeled A11. There is an arrow from state k=1 to state k=2 labeled A12, and an arrow from state k=1 to state k=3 labeled A13. State k=2 is represented by a green square with a loop labeled A22. There is an arrow from state k=2 to state k=3 labeled A23. State k=3 is represented by a blue square with a loop labeled A33.

state transition diagram for a 3-state HMM in Figure 13.9. Typically for such models the initial state probabilities for  $p(\mathbf{z}_1)$  are modified so that  $p(z_{11})=1$  and  $p(z_{1i})=0$ for  $j \neq 1$ , in other words every sequence is constrained to start in state  $j = 1$ . The transition matrix may be further constrained to ensure that large changes in the state index do not occur, so that  $A_{ik} = 0$  if  $k > j + \Delta$ . This type of model is illustrated using a lattice diagram in Figure 13.10.

Many applications of hidden Markov models, for example speech recognition, or on-line character recognition, make use of left-to-right architectures. As an illustration of the left-to-right hidden Markov model, we consider an example involving handwritten digits. This uses on-line data, meaning that each digit is represented by the trajectory of the pen as a function of time in the form of a sequence of pen coordinates, in contrast to the off-line digits data, discussed in Appendix A, which comprises static two-dimensional pixellated images of the ink. Examples of the online digits are shown in Figure 13.11. Here we train a hidden Markov model on a subset of data comprising 45 examples of the digit '2'. There are  $K = 16$  states, each of which can generate a line segment of fixed length having one of 16 possible angles, and so the emission distribution is simply a  $16 \times 16$  table of probabilities associated with the allowed angle values for each state index value. Transition probabilities are all set to zero except for those that keep the state index  $k$  the same or that increment it by 1, and the model parameters are optimized using 25 iterations of EM. We can gain some insight into the resulting model by running it generatively, as shown in Figure 13.11.

Image /page/9/Figure/5 description: Figure 13.10 is a lattice diagram for a 3-state left-to-right HMM. The diagram illustrates that the state index k is allowed to increase by at most 1 at each transition. The state index k is shown to be 1.

Image /page/9/Figure/6 description: The image displays a diagram illustrating a system with three rows labeled k=1, k=2, and k=3. Each row contains a series of squares, with the squares in the first row colored red, the second row green, and the third row blue. Horizontal lines connect adjacent squares within each row, and these connections are labeled A11 for the first row and A33 for the third row. Diagonal lines connect squares between adjacent rows, forming a staggered grid pattern. An arrow at the top indicates a direction of progression, and the horizontal axis is labeled with indices n-2, n-1, n, and n+1, suggesting a temporal or spatial sequence.

**Figure 13.11** Top row: examples of on-line handwritten digits. Bottom row: synthetic digits sampled generatively from a left-to-right hidden Markov model that has been trained on a data set of 45 handwritten digits.

Image /page/10/Picture/2 description: The image displays six squares arranged in two rows and three columns. Each square contains a red drawing of the number 2. The top row shows three variations of the number 2, with the first two being more standard and the third being more stylized. The bottom row also shows three variations of the number 2, with these drawings appearing more abstract and less clearly formed than those in the top row.

One of the most powerful properties of hidden Markov models is their ability to exhibit some degree of invariance to local warping (compression and stretching) of the time axis. To understand this, consider the way in which the digit '2' is written in the on-line handwritten digits example. A typical digit comprises two distinct sections joined at a cusp. The first part of the digit, which starts at the top left, has a sweeping arc down to the cusp or loop at the bottom left, followed by a second moreor-less straight sweep ending at the bottom right. Natural variations in writing style will cause the relative sizes of the two sections to vary, and hence the location of the cusp or loop within the temporal sequence will vary. From a generative perspective such variations can be accommodated by the hidden Markov model through changes in the number of transitions to the same state versus the number of transitions to the successive state. Note, however, that if a digit '2' is written in the reverse order, that is, starting at the bottom right and ending at the top left, then even though the pen tip coordinates may be identical to an example from the training set, the probability of the observations under the model will be extremely small. In the speech recognition context, warping of the time axis is associated with natural variations in the speed of speech, and again the hidden Markov model can accommodate such a distortion and not penalize it too heavily.

# Maximum likelihood for the HMM

If we have observed a data set  $X = \{x_1, \ldots, x_N\}$ , we can determine the parameters of an HMM using maximum likelihood. The likelihood function is obtained from the joint distribution (13.10) by marginalizing over the latent variables

$$
p(\mathbf{X}|\boldsymbol{\theta}) = \sum_{\mathbf{Z}} p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta}).
$$
 (13.11)

Because the joint distribution  $p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$  does not factorize over n (in contrast to the mixture distribution considered in Chapter 9), we cannot simply treat each of the summations over  $z_n$  independently. Nor can we perform the summations explicitly because there are  $N$  variables to be summed over, each of which has  $K$  states, resulting in a total of  $K^N$  terms. Thus the number of terms in the summation grows exponentially with the length of the chain. In fact, the summation in (13.11) corresponds to summing over exponentially many paths through the lattice diagram in Figure 13.7.

We have already encountered a similar difficulty when we considered the inference problem for the simple chain of variables in Figure 8.32. There we were able to make use of the conditional independence properties of the graph to re-order the summations in order to obtain an algorithm whose cost scales linearly, instead of exponentially, with the length of the chain. We shall apply a similar technique to the hidden Markov model.

A further difficulty with the expression (13.11) for the likelihood function is that, because it corresponds to a generalization of a mixture distribution, it represents a summation over the emission models for different settings of the latent variables. Direct maximization of the likelihood function will therefore lead to complex ex-*Section 9.2* pressions with no closed-form solutions, as was the case for simple mixture models (recall that a mixture model for i.i.d. data is a special case of the HMM).

> We therefore turn to the expectation maximization algorithm to find an efficient framework for maximizing the likelihood function in hidden Markov models. The EM algorithm starts with some initial selection for the model parameters, which we denote by  $\theta^{old}$ . In the E step, we take these parameter values and find the posterior distribution of the latent variables  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$ . We then use this posterior distribution to evaluate the expectation of the logarithm of the complete-data likelihood function, as a function of the parameters  $\theta$ , to give the function  $Q(\theta, \theta^{\text{old}})$  defined by

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{\text{old}}) = \sum_{\mathbf{Z}} p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}}) \ln p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta}).
$$
 (13.12)

At this point, it is convenient to introduce some notation. We shall use  $\gamma(\mathbf{z}_n)$  to denote the marginal posterior distribution of a latent variable  $z_n$ , and  $\xi(z_{n-1}, z_n)$  to denote the joint posterior distribution of two successive latent variables, so that

$$
\gamma(\mathbf{z}_n) = p(\mathbf{z}_n | \mathbf{X}, \boldsymbol{\theta}^{\text{old}})
$$
\n(13.13)

$$
\xi(\mathbf{z}_{n-1}, \mathbf{z}_n) = p(\mathbf{z}_{n-1}, \mathbf{z}_n | \mathbf{X}, \boldsymbol{\theta}^{\text{old}}). \tag{13.14}
$$

For each value of n, we can store  $\gamma(\mathbf{z}_n)$  using a set of K nonnegative numbers that sum to unity, and similarly we can store  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$  using a  $K \times K$  matrix of nonnegative numbers that again sum to unity. We shall also use  $\gamma(z_{nk})$  to denote the conditional probability of  $z_{nk} = 1$ , with a similar use of notation for  $\xi(z_{n-1,j}, z_{nk})$ and for other probabilistic variables introduced later. Because the expectation of a binary random variable is just the probability that it takes the value 1, we have

$$
\gamma(z_{nk}) = \mathbb{E}[z_{nk}] = \sum_{\mathbf{z}} \gamma(\mathbf{z}) z_{nk}
$$
\n(13.15)

$$
\xi(z_{n-1,j}, z_{nk}) = \mathbb{E}[z_{n-1,j} z_{nk}] = \sum_{\mathbf{z}} \gamma(\mathbf{z}) z_{n-1,j} z_{nk}. \quad (13.16)
$$

If we substitute the joint distribution  $p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$  given by (13.10) into (13.12),

*Section 9.2*

#### Hidden Markov Models

and make use of the definitions of  $\gamma$  and  $\xi$ , we obtain

$$
Q(\theta, \theta^{\text{old}}) = \sum_{k=1}^{K} \gamma(z_{1k}) \ln \pi_k + \sum_{n=2}^{N} \sum_{j=1}^{K} \sum_{k=1}^{K} \xi(z_{n-1,j}, z_{nk}) \ln A_{jk} + \sum_{n=1}^{N} \sum_{k=1}^{K} \gamma(z_{nk}) \ln p(\mathbf{x}_n | \phi_k). \quad (13.17)
$$

The goal of the E step will be to evaluate the quantities  $\gamma(\mathbf{z}_n)$  and  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$  efficiently, and we shall discuss this in detail shortly.

In the M step, we maximize  $Q(\theta, \theta^{\text{old}})$  with respect to the parameters  $\theta =$  ${\pi, \mathbf{A}, \phi}$  in which we treat  $\gamma(\mathbf{z}_n)$  and  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$  as constant. Maximization with respect to  $\pi$  and **A** is easily achieved using appropriate Lagrange multipliers with the results

$$
\pi_k = \frac{\gamma(z_{1k})}{\sum_{j=1}^K \gamma(z_{1j})}
$$
(13.18)  
$$
A_{jk} = \frac{\sum_{n=2}^N \xi(z_{n-1,j}, z_{nk})}{\sum_{l=1}^K \sum_{n=2}^N \xi(z_{n-1,j}, z_{nl})}.
$$
(13.19)

The EM algorithm must be initialized by choosing starting values for  $\pi$  and **A**, which should of course respect the summation constraints associated with their probabilistic interpretation. Note that any elements of  $\pi$  or **A** that are set to zero initially will *Exercise 13.6* remain zero in subsequent EM updates. A typical initialization procedure would involve selecting random starting values for these parameters subject to the summation and non-negativity constraints. Note that no particular modification to the EM results are required for the case of left-to-right models beyond choosing initial values for the elements  $A_{ik}$  in which the appropriate elements are set to zero, because these will remain zero throughout.

To maximize  $Q(\theta, \theta^{\text{old}})$  with respect to  $\phi_k$ , we notice that only the final term in (13.17) depends on  $\phi_k$ , and furthermore this term has exactly the same form as the data-dependent term in the corresponding function for a standard mixture distribution for i.i.d. data, as can be seen by comparison with (9.40) for the case of a Gaussian mixture. Here the quantities  $\gamma(z_{nk})$  are playing the role of the responsibilities. If the parameters  $\phi_k$  are independent for the different components, then this term decouples into a sum of terms one for each value of  $k$ , each of which can be maximized independently. We are then simply maximizing the weighted log likelihood function for the emission density  $p(\mathbf{x}|\phi_k)$  with weights  $\gamma(z_{nk})$ . Here we shall suppose that this maximization can be done efficiently. For instance, in the case of

*Exercise* 13.5

Gaussian emission densities we have  $p(\mathbf{x}|\phi_k) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)$ , and maximization of the function  $Q(\theta, \theta^{\text{old}})$  then gives

$$
\mu_k = \frac{\sum_{n=1}^{N} \gamma(z_{nk}) \mathbf{x}_n}{\sum_{n=1}^{N} \gamma(z_{nk})}
$$
(13.20)  
$$
\Sigma_k = \frac{\sum_{n=1}^{N} \gamma(z_{nk}) (\mathbf{x}_n - \mu_k) (\mathbf{x}_n - \mu_k)^{\mathrm{T}}}{\sum_{n=1}^{N} \gamma(z_{nk})}.
$$
(13.21)

For the case of discrete multinomial observed variables, the conditional distribution of the observations takes the form

$$
p(\mathbf{x}|\mathbf{z}) = \prod_{i=1}^{D} \prod_{k=1}^{K} \mu_{ik}^{x_i z_k}
$$
 (13.22)

*Exercise 13.8* and the corresponding M-step equations are given by

$$
\mu_{ik} = \frac{\sum_{n=1}^{N} \gamma(z_{nk}) x_{ni}}{\sum_{n=1}^{N} \gamma(z_{nk})}.
$$
(13.23)

An analogous result holds for Bernoulli observed variables.

The EM algorithm requires initial values for the parameters of the emission distribution. One way to set these is first to treat the data initially as i.i.d. and fit the emission density by maximum likelihood, and then use the resulting values to initialize the parameters for EM.

### The forward-backward algorithm

Next we seek an efficient procedure for evaluating the quantities  $\gamma(z_{nk})$  and  $\xi(z_{n-1,j}, z_{nk})$ , corresponding to the E step of the EM algorithm. The graph for the hidden Markov model, shown in Figure 13.5, is a tree, and so we know that the posterior distribution of the latent variables can be obtained efficiently using a two-*Section 8.4* stage message passing algorithm. In the particular context of the hidden Markov model, this is known as the *forward-backward* algorithm (Rabiner, 1989), or the *Baum-Welch* algorithm (Baum, 1972). There are in fact several variants of the basic algorithm, all of which lead to the exact marginals, according to the precise form of

*Section 8.4*

the messages that are propagated along the chain (Jordan, 2007). We shall focus on the most widely used of these, known as the alpha-beta algorithm.

As well as being of great practical importance in its own right, the forwardbackward algorithm provides us with a nice illustration of many of the concepts introduced in earlier chapters. We shall therefore begin in this section with a 'conventional' derivation of the forward-backward equations, making use of the sum and product rules of probability, and exploiting conditional independence properties which we shall obtain from the corresponding graphical model using d-separation. Then in Section 13.2.3, we shall see how the forward-backward algorithm can be obtained very simply as a specific example of the sum-product algorithm introduced in Section 8.4.4.

It is worth emphasizing that evaluation of the posterior distributions of the latent variables is independent of the form of the emission density  $p(x|z)$  or indeed of whether the observed variables are continuous or discrete. All we require is the values of the quantities  $p(\mathbf{x}_n|\mathbf{z}_n)$  for each value of  $\mathbf{z}_n$  for every n. Also, in this section and the next we shall omit the explicit dependence on the model parameters  $\theta$ <sup>old</sup> because these fixed throughout.

We therefore begin by writing down the following conditional independence properties (Jordan, 2007)

$$
p(\mathbf{X}|\mathbf{z}_n) = p(\mathbf{x}_1, \dots, \mathbf{x}_n | \mathbf{z}_n)
$$
 (12.24)

$$
p(\mathbf{x}_{n+1},...,\mathbf{x}_N|\mathbf{z}_n)
$$
 (13.24)

$$
p(\mathbf{x}_1, \dots, \mathbf{x}_{n-1} | \mathbf{x}_n, \mathbf{z}_n) = p(\mathbf{x}_1, \dots, \mathbf{x}_{n-1} | \mathbf{z}_n)
$$
(13.25)  
$$
p(\mathbf{x}_1, \dots, \mathbf{x}_{n-1} | \mathbf{z}_n) = p(\mathbf{x}_1, \dots, \mathbf{x}_{n-1} | \mathbf{z}_n)
$$
(13.26)

$$
p(\mathbf{x}_1, ..., \mathbf{x}_{n-1} | \mathbf{z}_{n-1}, \mathbf{z}_n) = p(\mathbf{x}_1, ..., \mathbf{x}_{n-1} | \mathbf{z}_{n-1})
$$
(13.26)  
$$
p(\mathbf{x}_{n+1}, ..., \mathbf{x}_N | \mathbf{z}_n, \mathbf{z}_{n+1}) = p(\mathbf{x}_{n+1}, ..., \mathbf{x}_N | \mathbf{z}_{n+1})
$$
(13.27)

$$
p(\mathbf{x}_{n+2},\ldots,\mathbf{x}_N|\mathbf{z}_{n+1},\mathbf{x}_{n+1}) = p(\mathbf{x}_{n+2},\ldots,\mathbf{x}_N|\mathbf{z}_{n+1})
$$
(13.28)

$$
p(\mathbf{X}|\mathbf{z}_{n-1}, \mathbf{z}_n) = p(\mathbf{x}_1, \dots, \mathbf{x}_{n-1}|\mathbf{z}_{n-1})
$$
  
$$
p(\mathbf{x}_n|\mathbf{z}_n)p(\mathbf{x}_{n+1}, \dots, \mathbf{x}_N|\mathbf{z}_n)
$$
 (13.29)

$$
p(\mathbf{x}_{N+1}|\mathbf{X}, \mathbf{z}_{N+1}) = p(\mathbf{x}_{N+1}|\mathbf{z}_{N+1}) \tag{13.30}
$$

$$
p(\mathbf{z}_{N+1}|\mathbf{z}_N, \mathbf{X}) = p(\mathbf{z}_{N+1}|\mathbf{z}_N)
$$
\n(13.31)

where  $X = \{x_1, \ldots, x_N\}$ . These relations are most easily proved using d-separation. For instance in the first of these results, we note that every path from any one of the nodes  $\mathbf{x}_1, \ldots, \mathbf{x}_{n-1}$  to the node  $\mathbf{x}_n$  passes through the node  $\mathbf{z}_n$ , which is observed. Because all such paths are head-to-tail, it follows that the conditional independence property must hold. The reader should take a few moments to verify each of these properties in turn, as an exercise in the application of d-separation. These relations can also be proved directly, though with significantly greater effort, from the joint distribution for the hidden Markov model using the sum and product rules of proba-

Let us begin by evaluating  $\gamma(z_{nk})$ . Recall that for a discrete multinomial random variable the expected value of one of its components is just the probability of that component having the value 1. Thus we are interested in finding the posterior distribution  $p(\mathbf{z}_n|\mathbf{x}_1,\dots,\mathbf{x}_N)$  of  $\mathbf{z}_n$  given the observed data set  $\mathbf{x}_1,\dots,\mathbf{x}_N$ . This

*Exercise 13.10* bility.

represents a vector of length  $K$  whose entries correspond to the expected values of  $z_{nk}$ . Using Bayes' theorem, we have

$$
\gamma(\mathbf{z}_n) = p(\mathbf{z}_n | \mathbf{X}) = \frac{p(\mathbf{X} | \mathbf{z}_n) p(\mathbf{z}_n)}{p(\mathbf{X})}.
$$
\n(13.32)

Note that the denominator  $p(X)$  is implicitly conditioned on the parameters  $\theta^{\text{old}}$ of the HMM and hence represents the likelihood function. Using the conditional independence property (13.24), together with the product rule of probability, we obtain

$$
\gamma(\mathbf{z}_n) = \frac{p(\mathbf{x}_1, \dots, \mathbf{x}_n, \mathbf{z}_n) p(\mathbf{x}_{n+1}, \dots, \mathbf{x}_N | \mathbf{z}_n)}{p(\mathbf{X})} = \frac{\alpha(\mathbf{z}_n) \beta(\mathbf{z}_n)}{p(\mathbf{X})}
$$
(13.33)

where we have defined

$$
\alpha(\mathbf{z}_n) \equiv p(\mathbf{x}_1, \dots, \mathbf{x}_n, \mathbf{z}_n) \tag{13.34}
$$

$$
\beta(\mathbf{z}_n) \equiv p(\mathbf{x}_{n+1}, \dots, \mathbf{x}_N | \mathbf{z}_n). \tag{13.35}
$$

The quantity  $\alpha(\mathbf{z}_n)$  represents the joint probability of observing all of the given data up to time *n* and the value of  $z_n$ , whereas  $\beta(z_n)$  represents the conditional probability of all future data from time  $n + 1$  up to N given the value of  $z_n$ . Again,  $\alpha(\mathbf{z}_n)$  and  $\beta(\mathbf{z}_n)$  each represent set of K numbers, one for each of the possible settings of the 1-of-K coded binary vector  $z_n$ . We shall use the notation  $\alpha(z_{nk})$  to denote the value of  $\alpha(\mathbf{z}_n)$  when  $z_{nk} = 1$ , with an analogous interpretation of  $\beta(z_{nk})$ .

We now derive recursion relations that allow  $\alpha(\mathbf{z}_n)$  and  $\beta(\mathbf{z}_n)$  to be evaluated efficiently. Again, we shall make use of conditional independence properties, in particular (13.25) and (13.26), together with the sum and product rules, allowing us to express  $\alpha(\mathbf{z}_n)$  in terms of  $\alpha(\mathbf{z}_{n-1})$  as follows

$$
\alpha(\mathbf{z}_n) = p(\mathbf{x}_1, ..., \mathbf{x}_n, \mathbf{z}_n)  
= p(\mathbf{x}_1, ..., \mathbf{x}_n | \mathbf{z}_n) p(\mathbf{z}_n)  
= p(\mathbf{x}_n | \mathbf{z}_n) p(\mathbf{x}_1, ..., \mathbf{x}_{n-1} | \mathbf{z}_n) p(\mathbf{z}_n)  
= p(\mathbf{x}_n | \mathbf{z}_n) p(\mathbf{x}_1, ..., \mathbf{x}_{n-1}, \mathbf{z}_n)  
= p(\mathbf{x}_n | \mathbf{z}_n) \sum_{\mathbf{z}_{n-1}} p(\mathbf{x}_1, ..., \mathbf{x}_{n-1}, \mathbf{z}_{n-1}, \mathbf{z}_n)  
= p(\mathbf{x}_n | \mathbf{z}_n) \sum_{\mathbf{z}_{n-1}} p(\mathbf{x}_1, ..., \mathbf{x}_{n-1}, \mathbf{z}_n | \mathbf{z}_{n-1}) p(\mathbf{z}_{n-1})  
= p(\mathbf{x}_n | \mathbf{z}_n) \sum_{\mathbf{z}_{n-1}} p(\mathbf{x}_1, ..., \mathbf{x}_{n-1} | \mathbf{z}_{n-1}) p(\mathbf{z}_n | \mathbf{z}_{n-1}) p(\mathbf{z}_{n-1})  
= p(\mathbf{x}_n | \mathbf{z}_n) \sum_{\mathbf{z}_{n-1}} p(\mathbf{x}_1, ..., \mathbf{x}_{n-1}, \mathbf{z}_{n-1}) p(\mathbf{z}_n | \mathbf{z}_{n-1})
$$

Making use of the definition (13.34) for  $\alpha(\mathbf{z}_n)$ , we then obtain

$$
\alpha(\mathbf{z}_n) = p(\mathbf{x}_n | \mathbf{z}_n) \sum_{\mathbf{z}_{n-1}} \alpha(\mathbf{z}_{n-1}) p(\mathbf{z}_n | \mathbf{z}_{n-1}).
$$
\n(13.36)

#### Hidden Markov Models

**Figure 13.12** Illustration of the forward recursion (13.36) for evaluation of the  $\alpha$  variables. In this fragment of the lattice, we see that the quantity  $\alpha(z_{n1})$ is obtained by taking the elements  $\alpha(z_{n-1,j})$  of  $\alpha(\mathbf{z}_{n-1})$  at step  $n-1$  and summing them up with weights given by  $A_{i1}$ , corresponding to the values of  $p(\mathbf{z}_n|\mathbf{z}_{n-1})$ , and then multiplying by the data contribution  $p(\mathbf{x}_n|z_{n1})$ .

It is worth taking a moment to study this recursion relation in some detail. Note that there are  $K$  terms in the summation, and the right-hand side has to be evaluated for each of the K values of  $z_n$  so each step of the  $\alpha$  recursion has computational cost that scaled like  $O(K^2)$ . The forward recursion equation for  $\alpha(\mathbf{z}_n)$  is illustrated using a lattice diagram in Figure 13.12.

In order to start this recursion, we need an initial condition that is given by

$$
\alpha(\mathbf{z}_1) = p(\mathbf{x}_1, \mathbf{z}_1) = p(\mathbf{z}_1) p(\mathbf{x}_1 | \mathbf{z}_1) = \prod_{k=1}^K \left\{ \pi_k p(\mathbf{x}_1 | \boldsymbol{\phi}_k) \right\}^{z_{1k}}
$$
(13.37)

which tells us that  $\alpha(z_{1k})$ , for  $k = 1, ..., K$ , takes the value  $\pi_k p(\mathbf{x}_1 | \phi_k)$ . Starting at the first node of the chain, we can then work along the chain and evaluate  $\alpha(\mathbf{z}_n)$ for every latent node. Because each step of the recursion involves multiplying by a  $K \times K$  matrix, the overall cost of evaluating these quantities for the whole chain is of  $O(K^2N)$ .

We can similarly find a recursion relation for the quantities  $\beta(\mathbf{z}_n)$  by making use of the conditional independence properties (13.27) and (13.28) giving

$$
\beta(\mathbf{z}_n) = p(\mathbf{x}_{n+1}, ..., \mathbf{x}_N | \mathbf{z}_n)
$$
  
$$
= \sum_{\mathbf{z}_{n+1}} p(\mathbf{x}_{n+1}, ..., \mathbf{x}_N, \mathbf{z}_{n+1} | \mathbf{z}_n)
$$
  
$$
= \sum_{\mathbf{z}_{n+1}} p(\mathbf{x}_{n+1}, ..., \mathbf{x}_N | \mathbf{z}_n, \mathbf{z}_{n+1}) p(\mathbf{z}_{n+1} | \mathbf{z}_n)
$$
  
$$
= \sum_{\mathbf{z}_{n+1}} p(\mathbf{x}_{n+1}, ..., \mathbf{x}_N | \mathbf{z}_{n+1}) p(\mathbf{z}_{n+1} | \mathbf{z}_n)
$$
  
$$
= \sum_{\mathbf{z}_{n+1}} p(\mathbf{x}_{n+2}, ..., \mathbf{x}_N | \mathbf{z}_{n+1}) p(\mathbf{x}_{n+1} | \mathbf{z}_{n+1}) p(\mathbf{z}_{n+1} | \mathbf{z}_n).
$$

Image /page/16/Figure/9 description: The image displays a diagram illustrating a probabilistic model, likely for sequence analysis or time series. It shows nodes representing states at time steps n-1 and n. At time step n-1, there are three states labeled k=1, k=2, and k=3, each associated with an alpha value: \alpha(z\_{n-1,1}), \alpha(z\_{n-1,2}), and \alpha(z\_{n-1,3}). These states are depicted as colored squares: red for k=1, green for k=2, and blue for k=3. At time step n, there is a single state labeled \alpha(z\_{n,1}), also represented by a red square. Arrows and labels indicate transitions and dependencies. An arrow labeled A11 connects the k=1 state at n-1 to the state at n. Arrows labeled A21 and A31 originate from the k=2 and k=3 states at n-1, respectively, and both point to the state at n. An additional label, p(x\_n|z\_{n,1}), points to the state at time step n, suggesting this state influences an observation x\_n. The diagram visually represents how states at one time step influence states and observations at the next time step in a probabilistic framework.

**Figure 13.13** Illustration of the backward recursion (13.38) for evaluation of the  $\beta$  variables. In this fragment of the lattice, we see that the quantity  $\beta(z_{n1})$  is obtained by taking the components  $\beta(z_{n+1,k})$  of  $\beta(z_{n+1})$  at step  $n + 1$  and summing them up with weights given by the products of  $A_{1k}$ , corresponding to the values of  $p(\mathbf{z}_{n+1}|\mathbf{z}_n)$  and the corresponding values of the emission density  $p(\mathbf{x}_n|z_{n+1,k}).$ 

Image /page/17/Figure/2 description: This is a diagram illustrating a probabilistic model. It shows nodes representing states at time steps 'n' and 'n+1'. For each time step, there are three states, labeled k=1, k=2, and k=3. The states at time step 'n' are represented by single squares, while the states at time step 'n+1' are represented by squares with associated probability distributions p(xn|zn+1,k). Arrows indicate transitions between states, labeled with transition probabilities like A11, A12, and A13. The states are also associated with beta functions, such as β(zn,1) and β(zn+1,k).

Making use of the definition (13.35) for  $\beta(\mathbf{z}_n)$ , we then obtain

$$
\beta(\mathbf{z}_n) = \sum_{\mathbf{z}_{n+1}} \beta(\mathbf{z}_{n+1}) p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1}) p(\mathbf{z}_{n+1}|\mathbf{z}_n).
$$
 (13.38)

Note that in this case we have a backward message passing algorithm that evaluates  $\beta(\mathbf{z}_n)$  in terms of  $\beta(\mathbf{z}_{n+1})$ . At each step, we absorb the effect of observation  $\mathbf{x}_{n+1}$ through the emission probability  $p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1})$ , multiply by the transition matrix  $p(\mathbf{z}_{n+1}|\mathbf{z}_n)$ , and then marginalize out  $\mathbf{z}_{n+1}$ . This is illustrated in Figure 13.13.

Again we need a starting condition for the recursion, namely a value for  $\beta(\mathbf{z}_N)$ . This can be obtained by setting  $n = N$  in (13.33) and replacing  $\alpha(\mathbf{z}_N)$  with its definition (13.34) to give

$$
p(\mathbf{z}_N|\mathbf{X}) = \frac{p(\mathbf{X}, \mathbf{z}_N)\beta(\mathbf{z}_N)}{p(\mathbf{X})}
$$
(13.39)

which we see will be correct provided we take  $\beta(\mathbf{z}_N) = 1$  for all settings of  $\mathbf{z}_N$ .

In the M step equations, the quantity  $p(X)$  will cancel out, as can be seen, for instance, in the M-step equation for  $\mu_k$  given by (13.20), which takes the form

$$
\mu_k = \frac{\sum_{n=1}^n \gamma(z_{nk}) \mathbf{x}_n}{\sum_{n=1}^n \gamma(z_{nk})} = \frac{\sum_{n=1}^n \alpha(z_{nk}) \beta(z_{nk}) \mathbf{x}_n}{\sum_{n=1}^n \alpha(z_{nk}) \beta(z_{nk})}. (13.40)
$$

However, the quantity  $p(X)$  represents the likelihood function whose value we typically wish to monitor during the EM optimization, and so it is useful to be able to evaluate it. If we sum both sides of (13.33) over  $z_n$ , and use the fact that the left-hand side is a normalized distribution, we obtain

$$
p(\mathbf{X}) = \sum_{\mathbf{z}_n} \alpha(\mathbf{z}_n) \beta(\mathbf{z}_n).
$$
 (13.41)

#### Hidden Markov Models

Thus we can evaluate the likelihood function by computing this sum, for any convenient choice of  $n$ . For instance, if we only want to evaluate the likelihood function, then we can do this by running the  $\alpha$  recursion from the start to the end of the chain, and then use this result for  $n = N$ , making use of the fact that  $\beta(\mathbf{z}_N)$  is a vector of 1s. In this case no  $\beta$  recursion is required, and we simply have

$$
p(\mathbf{X}) = \sum_{\mathbf{z}_N} \alpha(\mathbf{z}_N).
$$
 (13.42)

Let us take a moment to interpret this result for  $p(X)$ . Recall that to compute the likelihood we should take the joint distribution  $p(\mathbf{X}, \mathbf{Z})$  and sum over all possible values of **Z**. Each such value represents a particular choice of hidden state for every time step, in other words every term in the summation is a path through the lattice diagram, and recall that there are exponentially many such paths. By expressing the likelihood function in the form (13.42), we have reduced the computational cost from being exponential in the length of the chain to being linear by swapping the order of the summation and multiplications, so that at each time step  $n$  we sum the contributions from all paths passing through each of the states  $z_{nk}$  to give the intermediate quantities  $\alpha(\mathbf{z}_n)$ .

Next we consider the evaluation of the quantities  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$ , which correspond to the values of the conditional probabilities  $p(\mathbf{z}_{n-1}, \mathbf{z}_n|\mathbf{X})$  for each of the  $K \times K$ settings for  $(\mathbf{z}_{n-1}, \mathbf{z}_n)$ . Using the definition of  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$ , and applying Bayes' theorem, we have

$$
\xi(\mathbf{z}_{n-1}, \mathbf{z}_n) = p(\mathbf{z}_{n-1}, \mathbf{z}_n | \mathbf{X})
$$

$$
= \frac{p(\mathbf{X}|\mathbf{z}_{n-1}, \mathbf{z}_n)p(\mathbf{z}_{n-1}, \mathbf{z}_n)}{p(\mathbf{X})}
$$

$$
= \frac{p(\mathbf{x}_1, \dots, \mathbf{x}_{n-1}|\mathbf{z}_{n-1})p(\mathbf{x}_n|\mathbf{z}_n)p(\mathbf{x}_{n+1}, \dots, \mathbf{x}_N|\mathbf{z}_n)p(\mathbf{z}_n|\mathbf{z}_{n-1})p(\mathbf{z}_{n-1})}{p(\mathbf{X})}
$$

$$
= \frac{\alpha(\mathbf{z}_{n-1})p(\mathbf{x}_n|\mathbf{z}_n)p(\mathbf{z}_n|\mathbf{z}_{n-1})\beta(\mathbf{z}_n)}{p(\mathbf{X})}
$$
 $(13.43)$ 

where we have made use of the conditional independence property (13.29) together with the definitions of  $\alpha(\mathbf{z}_n)$  and  $\beta(\mathbf{z}_n)$  given by (13.34) and (13.35). Thus we can calculate the  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$  directly by using the results of the  $\alpha$  and  $\beta$  recursions.

Let us summarize the steps required to train a hidden Markov model using the EM algorithm. We first make an initial selection of the parameters  $\theta$ <sup>old</sup> where  $\theta \equiv (\pi, \mathbf{A}, \phi)$ . The **A** and  $\pi$  parameters are often initialized either uniformly or randomly from a uniform distribution (respecting their non-negativity and summation constraints). Initialization of the parameters  $\phi$  will depend on the form of the distribution. For instance in the case of Gaussians, the parameters  $\mu_k$  might be initialized by applying the K-means algorithm to the data, and  $\Sigma_k$  might be initialized to the covariance matrix of the corresponding  $K$  means cluster. Then we run both the forward  $\alpha$  recursion and the backward  $\beta$  recursion and use the results to evaluate  $\gamma(\mathbf{z}_n)$  and  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$ . At this stage, we can also evaluate the likelihood function.

This completes the E step, and we use the results to find a revised set of parameters *θ*new using the M-step equations from Section 13.2.1. We then continue to alternate between E and M steps until some convergence criterion is satisfied, for instance when the change in the likelihood function is below some threshold.

Note that in these recursion relations the observations enter through conditional distributions of the form  $p(\mathbf{x}_n|\mathbf{z}_n)$ . The recursions are therefore independent of the type or dimensionality of the observed variables or the form of this conditional distribution, so long as its value can be computed for each of the  $K$  possible states of  $z_n$ . Since the observed variables  $\{x_n\}$  are fixed, the quantities  $p(x_n|z_n)$  can be pre-computed as functions of  $z_n$  at the start of the EM algorithm, and remain fixed throughout.

We have seen in earlier chapters that the maximum likelihood approach is most effective when the number of data points is large in relation to the number of parameters. Here we note that a hidden Markov model can be trained effectively, using maximum likelihood, provided the training sequence is sufficiently long. Alternatively, we can make use of multiple shorter sequences, which requires a straightforward *Exercise 13.12* modification of the hidden Markov model EM algorithm. In the case of left-to-right models, this is particularly important because, in a given observation sequence, a given state transition corresponding to a nondiagonal element of **A** will seen at most once.

> Another quantity of interest is the predictive distribution, in which the observed data is  $X = \{x_1, \ldots, x_N\}$  and we wish to predict  $x_{N+1}$ , which would be important for real-time applications such as financial forecasting. Again we make use of the sum and product rules together with the conditional independence properties (13.29) and (13.31) giving

$$
p(\mathbf{x}_{N+1}|\mathbf{X}) = \sum_{\mathbf{z}_{N+1}} p(\mathbf{x}_{N+1}, \mathbf{z}_{N+1}|\mathbf{X})
$$
  
$$
= \sum_{\mathbf{z}_{N+1}} p(\mathbf{x}_{N+1}|\mathbf{z}_{N+1}) p(\mathbf{z}_{N+1}|\mathbf{X})
$$
  
$$
= \sum_{\mathbf{z}_{N+1}} p(\mathbf{x}_{N+1}|\mathbf{z}_{N+1}) \sum_{\mathbf{z}_{N}} p(\mathbf{z}_{N+1}, \mathbf{z}_{N}|\mathbf{X})
$$
  
$$
= \sum_{\mathbf{z}_{N+1}} p(\mathbf{x}_{N+1}|\mathbf{z}_{N+1}) \sum_{\mathbf{z}_{N}} p(\mathbf{z}_{N+1}|\mathbf{z}_{N}) p(\mathbf{z}_{N}|\mathbf{X})
$$
  
$$
= \sum_{\mathbf{z}_{N+1}} p(\mathbf{x}_{N+1}|\mathbf{z}_{N+1}) \sum_{\mathbf{z}_{N}} p(\mathbf{z}_{N+1}|\mathbf{z}_{N}) \frac{p(\mathbf{z}_{N}, \mathbf{X})}{p(\mathbf{X})}
$$
  
$$
= \frac{1}{p(\mathbf{X})} \sum_{\mathbf{z}_{N+1}} p(\mathbf{x}_{N+1}|\mathbf{z}_{N+1}) \sum_{\mathbf{z}_{N}} p(\mathbf{z}_{N+1}|\mathbf{z}_{N}) \alpha(\mathbf{z}_{N}) \quad (13.44)
$$
which can be evaluated by first running a forward-recursion and then computing

which can be evaluated by first running a forward  $\alpha$  recursion and then computing the final summations over  $z_N$  and  $z_{N+1}$ . The result of the first summation over  $z_N$ can be stored and used once the value of  $\mathbf{x}_{N+1}$  is observed in order to run the  $\alpha$ recursion forward to the next step in order to predict the subsequent value  $\mathbf{x}_{N+2}$ .

*Exercise 13.12*

Image /page/20/Figure/1 description: The image displays a factor graph representation of a hidden Markov model. The graph consists of several nodes connected by edges. There are square nodes labeled with Greek letters and lowercase letters, and circular nodes labeled with lowercase letters. Specifically, there is a square node labeled 'χ', followed by a chain of circular nodes labeled 'z1', 'zn-1', and 'zn', with dotted lines indicating continuation. Between 'zn-1' and 'zn', there is a square node labeled 'ψn'. Below the chain of 'z' nodes, there are square nodes labeled 'g1', 'gn-1', and 'gn', each connected to a corresponding circular node above it. Below these 'g' nodes are circular nodes labeled 'x1', 'xn-1', and 'xn', each connected to the 'g' node directly above it. The circular nodes 'x1', 'xn-1', and 'xn' are filled with blue color.

Note that in (13.44), the influence of all data from  $x_1$  to  $x_N$  is summarized in the K values of  $\alpha(\mathbf{z}_N)$ . Thus the predictive distribution can be carried forward indefinitely using a fixed amount of storage, as may be required for real-time applications.

Here we have discussed the estimation of the parameters of an HMM using maximum likelihood. This framework is easily extended to regularized maximum likelihood by introducing priors over the model parameters  $\pi$ , **A** and  $\phi$  whose values are then estimated by maximizing their posterior probability. This can again be done using the EM algorithm in which the E step is the same as discussed above, and the M step involves adding the log of the prior distribution  $p(\theta)$  to the function  $Q(\theta, \theta^{\text{old}})$ before maximization and represents a straightforward application of the techniques developed at various points in this book. Furthermore, we can use variational meth-*Section 10.1* ods to give a fully Bayesian treatment of the HMM in which we marginalize over the parameter distributions (MacKay, 1997). As with maximum likelihood, this leads to a two-pass forward-backward recursion to compute posterior probabilities.

### The sum-product algorithm for the HMM

The directed graph that represents the hidden Markov model, shown in Figure 13.5, is a tree and so we can solve the problem of finding local marginals for the *Section 8.4.4* hidden variables using the sum-product algorithm. Not surprisingly, this turns out to be equivalent to the forward-backward algorithm considered in the previous section, and so the sum-product algorithm therefore provides us with a simple way to derive the alpha-beta recursion formulae.

> We begin by transforming the directed graph of Figure 13.5 into a factor graph, of which a representative fragment is shown in Figure 13.14. This form of the factor graph shows all variables, both latent and observed, explicitly. However, for the purpose of solving the inference problem, we shall always be conditioning on the variables  $x_1, \ldots, x_N$ , and so we can simplify the factor graph by absorbing the emission probabilities into the transition probability factors. This leads to the simplified factor graph representation in Figure 13.15, in which the factors are given by

$$
h(\mathbf{z}_1) = p(\mathbf{z}_1)p(\mathbf{x}_1|\mathbf{z}_1)
$$
\n(13.45)

$$
f_n(\mathbf{z}_{n-1}, \mathbf{z}_n) = p(\mathbf{z}_n | \mathbf{z}_{n-1}) p(\mathbf{x}_n | \mathbf{z}_n).
$$
 (13.46)

*Section 10.1*

*Section 8.4.4*

**Figure 13.15** A simplified form of factor graph to describe the hidden Markov model. h contract  $\bigcap$  fn  $z_1$   $z_{n-1}$   $z_n$ 

> To derive the alpha-beta algorithm, we denote the final hidden variable  $z_N$  as the root node, and first pass messages from the leaf node  $h$  to the root. From the general results (8.66) and (8.69) for message propagation, we see that the messages which are propagated in the hidden Markov model take the form

$$
\mu_{\mathbf{z}_{n-1}\to f_n}(\mathbf{z}_{n-1}) = \mu_{f_{n-1}\to\mathbf{z}_{n-1}}(\mathbf{z}_{n-1}) \tag{13.47}
$$

$$
\mu_{f_n \to \mathbf{z}_n}(\mathbf{z}_n) = \sum_{\mathbf{z}_{n-1}} f_n(\mathbf{z}_{n-1}, \mathbf{z}_n) \mu_{\mathbf{z}_{n-1} \to f_n}(\mathbf{z}_{n-1}) \qquad (13.48)
$$

These equations represent the propagation of messages forward along the chain and are equivalent to the alpha recursions derived in the previous section, as we shall now show. Note that because the variable nodes  $z_n$  have only two neighbours, they perform no computation.

We can eliminate  $\mu_{\mathbf{z}_{n-1}\to f_n}(\mathbf{z}_{n-1})$  from (13.48) using (13.47) to give a recursion for the  $f \rightarrow z$  messages of the form

$$
\mu_{f_n \to \mathbf{z}_n}(\mathbf{z}_n) = \sum_{\mathbf{z}_{n-1}} f_n(\mathbf{z}_{n-1}, \mathbf{z}_n) \mu_{f_{n-1} \to \mathbf{z}_{n-1}}(\mathbf{z}_{n-1}). \tag{13.49}
$$

If we now recall the definition (13.46), and if we define

$$
\alpha(\mathbf{z}_n) = \mu_{f_n \to \mathbf{z}_n}(\mathbf{z}_n)
$$
\n(13.50)

then we obtain the alpha recursion given by (13.36). We also need to verify that the quantities  $\alpha(\mathbf{z}_n)$  are themselves equivalent to those defined previously. This is easily done by using the initial condition (8.71) and noting that  $\alpha(\mathbf{z}_1)$  is given by  $h(\mathbf{z}_1) = p(\mathbf{z}_1)p(\mathbf{x}_1|\mathbf{z}_1)$  which is identical to (13.37). Because the initial  $\alpha$  is the same, and because they are iteratively computed using the same equation, all subsequent  $\alpha$  quantities must be the same.

Next we consider the messages that are propagated from the root node back to the leaf node. These take the form

$$
\mu_{f_{n+1}\to f_n}(\mathbf{z}_n) = \sum_{\mathbf{z}_{n+1}} f_{n+1}(\mathbf{z}_n, \mathbf{z}_{n+1}) \mu_{f_{n+2}\to f_{n+1}}(\mathbf{z}_{n+1})
$$
(13.51)

where, as before, we have eliminated the messages of the type  $z \rightarrow f$  since the variable nodes perform no computation. Using the definition (13.46) to substitute for  $f_{n+1}(\mathbf{z}_n, \mathbf{z}_{n+1})$ , and defining

$$
\beta(\mathbf{z}_n) = \mu_{f_{n+1} \to \mathbf{z}_n}(\mathbf{z}_n)
$$
\n(13.52)

we obtain the beta recursion given by (13.38). Again, we can verify that the beta variables themselves are equivalent by noting that (8.70) implies that the initial message send by the root variable node is  $\mu_{\mathbf{z}_N \to f_N}(\mathbf{z}_N) = 1$ , which is identical to the initialization of  $\beta(\mathbf{z}_N)$  given in Section 13.2.2.

The sum-product algorithm also specifies how to evaluate the marginals once all the messages have been evaluated. In particular, the result (8.63) shows that the local marginal at the node  $z_n$  is given by the product of the incoming messages. Because we have conditioned on the variables  $X = \{x_1, \ldots, x_N\}$ , we are computing the joint distribution

$$
p(\mathbf{z}_n, \mathbf{X}) = \mu_{f_n \to \mathbf{z}_n}(\mathbf{z}_n) \mu_{f_{n+1} \to \mathbf{z}_n}(\mathbf{z}_n) = \alpha(\mathbf{z}_n) \beta(\mathbf{z}_n).
$$
 (13.53)

Dividing both sides by  $p(X)$ , we then obtain

$$
\gamma(\mathbf{z}_n) = \frac{p(\mathbf{z}_n, \mathbf{X})}{p(\mathbf{X})} = \frac{\alpha(\mathbf{z}_n)\beta(\mathbf{z}_n)}{p(\mathbf{X})}
$$
(13.54)

*Exercise* 13.11 in agreement with (13.33). The result (13.43) can similarly be derived from (8.72).

# Scaling factors

There is an important issue that must be addressed before we can make use of the forward backward algorithm in practice. From the recursion relation (13.36), we note that at each step the new value  $\alpha(\mathbf{z}_n)$  is obtained from the previous value  $\alpha(\mathbf{z}_{n-1})$ by multiplying by quantities  $p(\mathbf{z}_n|\mathbf{z}_{n-1})$  and  $p(\mathbf{x}_n|\mathbf{z}_n)$ . Because these probabilities are often significantly less than unity, as we work our way forward along the chain, the values of  $\alpha(\mathbf{z}_n)$  can go to zero exponentially quickly. For moderate lengths of chain (say 100 or so), the calculation of the  $\alpha(\mathbf{z}_n)$  will soon exceed the dynamic range of the computer, even if double precision floating point is used.

In the case of i.i.d. data, we implicitly circumvented this problem with the evaluation of likelihood functions by taking logarithms. Unfortunately, this will not help here because we are forming sums of products of small numbers (we are in fact implicitly summing over all possible paths through the lattice diagram of Figure 13.7). We therefore work with re-scaled versions of  $\alpha(\mathbf{z}_n)$  and  $\beta(\mathbf{z}_n)$  whose values remain of order unity. As we shall see, the corresponding scaling factors cancel out when we use these re-scaled quantities in the EM algorithm.

In (13.34), we defined  $\alpha(\mathbf{z}_n) = p(\mathbf{x}_1,\ldots,\mathbf{x}_n,\mathbf{z}_n)$  representing the joint distribution of all the observations up to  $\mathbf{x}_n$  and the latent variable  $\mathbf{z}_n$ . Now we define a normalized version of  $\alpha$  given by

$$
\widehat{\alpha}(\mathbf{z}_n) = p(\mathbf{z}_n | \mathbf{x}_1, \dots, \mathbf{x}_n) = \frac{\alpha(\mathbf{z}_n)}{p(\mathbf{x}_1, \dots, \mathbf{x}_n)}
$$
(13.55)

which we expect to be well behaved numerically because it is a probability distribution over K variables for any value of n. In order to relate the scaled and original alpha variables, we introduce scaling factors defined by conditional distributions over the observed variables

$$
c_n = p(\mathbf{x}_n | \mathbf{x}_1, \dots, \mathbf{x}_{n-1}).
$$
\n(13.56)