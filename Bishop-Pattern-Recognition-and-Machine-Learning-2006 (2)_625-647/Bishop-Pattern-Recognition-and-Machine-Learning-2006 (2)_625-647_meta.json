{"table_of_contents": [{"title": "606 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 1, "polygon": [[29.25, 40.5], [196.5, 40.5], [196.5, 51.7060546875], [29.25, 51.7060546875]]}, {"title": "13.1. <PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[83.25, 311.25], [215.25, 310.5], [215.25, 323.73193359375], [83.25, 323.73193359375]]}, {"title": "608 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 3, "polygon": [[29.25, 41.25], [196.5, 41.25], [196.5, 51.949951171875], [29.25, 51.949951171875]]}, {"title": "610 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 5, "polygon": [[29.25, 41.25], [197.25, 41.25], [197.25, 51.8280029296875], [29.25, 51.8280029296875]]}, {"title": "13.2. <PERSON>", "heading_level": null, "page_id": 5, "polygon": [[81.75, 339.0], [262.5, 339.0], [262.5, 352.8369140625], [81.75, 352.8369140625]]}, {"title": "612 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 7, "polygon": [[29.25, 40.5], [197.25, 40.5], [197.25, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "614 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 9, "polygon": [[29.25, 40.5], [197.25, 40.5], [197.25, 52.112548828125], [29.25, 52.112548828125]]}, {"title": "13.2.1 Maximum likelihood for the HMM", "heading_level": null, "page_id": 10, "polygon": [[138.75, 449.25], [361.51171875, 449.25], [361.51171875, 460.8017578125], [138.75, 460.8017578125]]}, {"title": "13.2. <PERSON> 617", "heading_level": null, "page_id": 12, "polygon": [[289.5, 40.5], [473.25, 40.5], [473.25, 51.0150146484375], [289.5, 51.0150146484375]]}, {"title": "618 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 13, "polygon": [[29.25, 40.5], [197.25, 40.5], [197.25, 51.7060546875], [29.25, 51.7060546875]]}, {"title": "13.2.2 The forward-backward algorithm", "heading_level": null, "page_id": 13, "polygon": [[135.84375, 505.5], [359.25, 505.5], [359.25, 516.41015625], [135.84375, 516.41015625]]}, {"title": "620 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 15, "polygon": [[29.25, 40.5], [197.25, 40.5], [197.25, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "13.2. <PERSON> 621", "heading_level": null, "page_id": 16, "polygon": [[286.9453125, 40.5], [472.5, 40.5], [472.5, 51.0150146484375], [286.9453125, 51.0150146484375]]}, {"title": "622 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 17, "polygon": [[29.25, 40.5], [196.5, 40.5], [196.5, 52.03125], [29.25, 52.03125]]}, {"title": "13.2. <PERSON> 623", "heading_level": null, "page_id": 18, "polygon": [[289.5, 40.5], [473.25, 40.5], [473.25, 51.21826171875], [289.5, 51.21826171875]]}, {"title": "13.2.3 The sum-product algorithm for the HMM", "heading_level": null, "page_id": 20, "polygon": [[137.8125, 387.75], [401.25, 387.75], [401.25, 398.689453125], [137.8125, 398.689453125]]}, {"title": "626 13. SEQUENTIAL DATA", "heading_level": null, "page_id": 21, "polygon": [[29.25, 40.5], [196.5, 40.5], [196.5, 51.6654052734375], [29.25, 51.6654052734375]]}, {"title": "13.2.4 Scaling factors", "heading_level": null, "page_id": 22, "polygon": [[138.75, 285.0], [265.5, 285.0], [265.5, 296.09033203125], [138.75, 296.09033203125]]}, {"title": "", "heading_level": null, "page_id": 22, "polygon": [[31.74609375, 264.54638671875], [100.65234375, 264.54638671875], [100.65234375, 273.32666015625], [31.74609375, 273.32666015625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 32], ["Line", 18], ["Picture", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4813, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Line", 46], ["Span", 41], ["Text", 4], ["SectionHeader", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 48], ["Text", 8], ["Equation", 3], ["Figure", 2], ["SectionHeader", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1842, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 49], ["TextInlineMath", 3], ["Text", 2], ["Figure", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 708, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 49], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1249, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 52], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Picture", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1359, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 49], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1361, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1346, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 38], ["Figure", 3], ["SectionHeader", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2017, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 36], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Picture", 1], ["Text", 1], ["SectionHeader", 1], ["Equation", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 603, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 45], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 67], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4703, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 59], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 43], ["Equation", 8], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 704], ["Line", 50], ["Equation", 6], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1762, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["Line", 52], ["Text", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2328, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 66], ["Equation", 4], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1785, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 43], ["TextInlineMath", 5], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1421, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 64], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8530, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 39], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["Line", 40], ["Equation", 6], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 426], ["Line", 42], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_625-647"}