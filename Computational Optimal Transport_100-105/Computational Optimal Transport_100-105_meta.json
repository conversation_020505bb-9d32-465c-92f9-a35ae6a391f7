{"table_of_contents": [{"title": "6", "heading_level": null, "page_id": 0, "polygon": [[303.1236230110159, 135.0841064453125], [321.76513671875, 135.0841064453125], [321.76513671875, 162.1202392578125], [303.1236230110159, 162.1202392578125]]}, {"title": "W1 Optimal Transport", "heading_level": null, "page_id": 0, "polygon": [[229.725341796875, 180.69289099526065], [390.30859375, 180.69289099526065], [390.30859375, 196.1085205078125], [229.725341796875, 196.1085205078125]]}, {"title": "6.1 W1 on Metric Spaces", "heading_level": null, "page_id": 1, "polygon": [[104.29253365973072, 125.21042654028436], [255.85434516523867, 125.21042654028436], [255.85434516523867, 136.45687203791468], [104.29253365973072, 136.45687203791468]]}, {"title": "6.2 W1 on Euclidean Spaces", "heading_level": null, "page_id": 2, "polygon": [[103.5422276621787, 503.0909952606635], [271.779296875, 503.0909952606635], [271.779296875, 515.2314453125], [103.5422276621787, 515.2314453125]]}, {"title": "6.3 W_1 on a Graph", "heading_level": null, "page_id": 3, "polygon": [[104.29253365973072, 403.3725118483412], [222.09057527539778, 403.3725118483412], [222.09057527539778, 414.8115234375], [104.29253365973072, 414.8115234375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 26], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 839], ["Line", 83], ["TextInlineMath", 9], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2519, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["Line", 66], ["TextInlineMath", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 398], ["Line", 43], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 43], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 675, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_100-105"}