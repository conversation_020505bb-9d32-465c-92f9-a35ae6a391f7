{"table_of_contents": [{"title": "Curriculum Learning: A Survey", "heading_level": null, "page_id": 0, "polygon": [[40.53400503778337, 137.010009765625], [291.9949622166247, 137.010009765625], [291.9949622166247, 153.660888671875], [40.53400503778337, 153.660888671875]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[39.578125, 598.3223508459483], [86.06787109375, 598.3223508459483], [86.06787109375, 606.83203125], [39.578125, 606.83203125]]}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[40.53400503778337, 632.8121104185218], [93.82871536523929, 632.8121104185218], [93.82871536523929, 642.189453125], [40.53400503778337, 642.189453125]]}, {"title": "P<PERSON>", "heading_level": null, "page_id": 0, "polygon": [[40.53400503778337, 687.5458593054318], [72.81108312342569, 687.5458593054318], [72.81108312342569, 697.28125], [40.53400503778337, 697.28125]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[40.53400503778337, 722.0356188780053], [72.81108312342569, 722.0356188780053], [72.81108312342569, 732.*********], [40.53400503778337, 732.*********]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[297.24937027707807, 483.60641139804096], [370.0604534005038, 483.60641139804096], [370.0604534005038, 494.*********], [297.24937027707807, 494.*********]]}, {"title": "2 Curriculum Learning", "heading_level": null, "page_id": 2, "polygon": [[40.53400503778337, 146.95636687444346], [157.632241813602, 146.95636687444346], [157.632241813602, 157.4*********], [40.53400503778337, 157.4*********]]}, {"title": "3 Taxonomy of Curriculum Learning Methods", "heading_level": null, "page_id": 4, "polygon": [[296.49874055415614, 91.47284060552093], [529.9445843828715, 91.47284060552093], [529.9445843828715, 101.858154296875], [296.49874055415614, 101.858154296875]]}, {"title": "4 Applications of Curriculum Learning", "heading_level": null, "page_id": 6, "polygon": [[297.24937027707807, 519.5957257346394], [493.1637279596977, 519.5957257346394], [493.1637279596977, 529.5390625], [297.24937027707807, 529.5390625]]}, {"title": "4.1 Multi-domain approaches", "heading_level": null, "page_id": 6, "polygon": [[296.49874055415614, 708.5396260017809], [425.6070528967254, 708.5396260017809], [425.6070528967254, 717.*********], [296.49874055415614, 717.*********]]}, {"title": "4.2 Computer vision", "heading_level": null, "page_id": 14, "polygon": [[40.53400503778337, 92.22261798753338], [131.360201511335, 92.22261798753338], [131.360201511335, 102.1151123046875], [40.53400503778337, 102.1151123046875]]}, {"title": "4.3 Natural language processing", "heading_level": null, "page_id": 22, "polygon": [[40.53400503778337, 92.22261798753338], [182.40302267002517, 92.22261798753338], [182.40302267002517, 102.16650390625], [40.53400503778337, 102.16650390625]]}, {"title": "4.4 Speech processing", "heading_level": null, "page_id": 25, "polygon": [[296.49874055415614, 203.18967052537843], [391.8287153652393, 203.18967052537843], [391.8287153652393, 214.**********], [296.49874055415614, 214.**********]]}, {"title": "4.5 Medical imaging", "heading_level": null, "page_id": 26, "polygon": [[296.49874055415614, 646.3081032947462], [385.823677581864, 646.3081032947462], [385.823677581864, 656.**********], [296.49874055415614, 656.**********]]}, {"title": "4.6 Reinforcement learning", "heading_level": null, "page_id": 28, "polygon": [[40.53400503778337, 174.6981300089047], [159.**********, 174.6981300089047], [159.**********, 185.318115234375], [40.53400503778337, 185.318115234375]]}, {"title": "4.7 Other domains", "heading_level": null, "page_id": 30, "polygon": [[296.49874055415614, 545.8379341050756], [379.06801007556675, 545.8379341050756], [379.06801007556675, 555.**********], [296.49874055415614, 555.**********]]}, {"title": "5 Hierarchical Clustering of Curriculum\nLearning Methods", "heading_level": null, "page_id": 31, "polygon": [[296.49874055415614, 91.47284060552093], [498.4181360201511, 91.47284060552093], [498.4181360201511, 114.50048828125], [296.49874055415614, 114.50048828125]]}, {"title": "6 Closing Remarks and Future Directions", "heading_level": null, "page_id": 33, "polygon": [[296.49874055415614, 211.43722172751558], [506.67506297229215, 211.43722172751558], [506.67506297229215, 221.80615234375], [296.49874055415614, 221.80615234375]]}, {"title": "6.1 Generic directions", "heading_level": null, "page_id": 33, "polygon": [[296.49874055415614, 236.17987533392696], [393.3299748110831, 236.17987533392696], [393.3299748110831, 245.857421875], [296.49874055415614, 245.857421875]]}, {"title": "6.2 Domain-specific directions", "heading_level": null, "page_id": 34, "polygon": [[296.49874055415614, 509.0988423864648], [428.666015625, 509.0988423864648], [428.666015625, 519.2607421875], [296.49874055415614, 519.2607421875]]}, {"title": "Conflict of interest", "heading_level": null, "page_id": 35, "polygon": [[297.24937027707807, 137.7294921875], [392.87109375, 137.7294921875], [392.87109375, 147.39111328125], [297.24937027707807, 147.39111328125]]}, {"title": "References", "heading_level": null, "page_id": 35, "polygon": [[296.49874055415614, 210.6874443455031], [353.29296875, 210.6874443455031], [353.29296875, 220.16162109375], [296.49874055415614, 220.16162109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 77], ["Text", 13], ["SectionHeader", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4668, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 107], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 105], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 80], ["Text", 4], ["Caption", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 103], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 107], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 100], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 595], ["Span", 496], ["Line", 112], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5752, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["TableCell", 210], ["Line", 97], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4300, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["TableCell", 238], ["Line", 107], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 6404, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["TableCell", 231], ["Line", 120], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7882, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 755], ["Span", 601], ["Line", 113], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 21930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["TableCell", 243], ["Line", 111], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11436, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 106], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 105], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 107], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 107], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 106], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 106], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 106], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 106], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 103], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 106], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["Line", 106], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["Line", 107], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 104], ["Text", 13], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 104], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 107], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 103], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["Line", 107], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 104], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 101], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Line", 59], ["Span", 21], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 676, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 104], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 105], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["Line", 101], ["ListItem", 10], ["Reference", 10], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 106], ["ListItem", 28], ["Reference", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 105], ["ListItem", 28], ["Reference", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 106], ["ListItem", 31], ["Reference", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 195], ["Line", 107], ["ListItem", 31], ["Reference", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 106], ["ListItem", 29], ["Reference", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 106], ["ListItem", 29], ["Reference", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 80], ["ListItem", 22], ["Reference", 22], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Curriculum Learning- A Survey"}