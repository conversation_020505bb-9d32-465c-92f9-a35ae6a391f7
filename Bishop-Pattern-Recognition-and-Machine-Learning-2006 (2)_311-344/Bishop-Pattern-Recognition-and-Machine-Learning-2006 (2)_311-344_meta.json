{"table_of_contents": [{"title": "", "heading_level": null, "page_id": 1, "polygon": [[29.977294921875, 546.0029296875], [76.2890625, 544.7021484375], [76.2890625, 553.8076171875], [29.977294921875, 555.1083984375]]}, {"title": "6.1. Dual Representations", "heading_level": null, "page_id": 2, "polygon": [[89.25, 79.5], [255.75, 79.5], [255.75, 92.761962890625], [89.25, 92.761962890625]]}, {"title": "294 6. <PERSON>ER<PERSON><PERSON> METHODS", "heading_level": null, "page_id": 3, "polygon": [[29.77734375, 40.5], [193.5, 40.5], [193.5, 51.5028076171875], [29.77734375, 51.5028076171875]]}, {"title": "6.2. <PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 3, "polygon": [[88.5, 405.75], [252.75, 405.75], [252.75, 419.1767578125], [88.5, 419.1767578125]]}, {"title": "Techniques for Constructing New Kernels.", "heading_level": null, "page_id": 5, "polygon": [[134.859375, 72.75], [333.0, 72.75], [333.0, 83.006103515625], [134.859375, 83.006103515625]]}, {"title": "", "heading_level": null, "page_id": 6, "polygon": [[31.623046875, 376.9013671875], [91.669921875, 376.9013671875], [91.669921875, 388.6083984375], [31.623046875, 388.6083984375]]}, {"title": "298 6. <PERSON>ER<PERSON><PERSON> METHODS", "heading_level": null, "page_id": 7, "polygon": [[30.0, 40.5], [193.5, 40.5], [193.5, 51.8280029296875], [30.0, 51.8280029296875]]}, {"title": "6.3. Radial Basis Function Networks", "heading_level": null, "page_id": 8, "polygon": [[90.0, 289.0986328125], [318.75, 289.0986328125], [318.75, 301.78125], [90.0, 301.78125]]}, {"title": "302 6. KERNEL METHODS", "heading_level": null, "page_id": 11, "polygon": [[29.25, 39.75], [194.25, 39.75], [194.25, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "6.4. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 12, "polygon": [[90.0, 567.0], [249.0, 567.0], [249.0, 580.4736328125], [90.0, 580.4736328125]]}, {"title": "6.4.1 Linear regression revisited", "heading_level": null, "page_id": 13, "polygon": [[137.25, 380.8037109375], [322.62890625, 380.8037109375], [322.62890625, 391.2099609375], [137.25, 391.2099609375]]}, {"title": "306 6. KERNEL METHODS", "heading_level": null, "page_id": 15, "polygon": [[30.0, 40.5], [193.5, 40.5], [193.5, 51.787353515625], [30.0, 51.787353515625]]}, {"title": "6.4.2 Gaussian processes for regression", "heading_level": null, "page_id": 15, "polygon": [[137.07421875, 213.75], [367.91015625, 213.75], [367.91015625, 224.7099609375], [137.07421875, 224.7099609375]]}, {"title": "308 6. KERNEL METHODS", "heading_level": null, "page_id": 17, "polygon": [[29.25, 40.5], [193.5, 40.5], [193.5, 51.7467041015625], [29.25, 51.7467041015625]]}, {"title": "310 6. KERNEL METHODS", "heading_level": null, "page_id": 19, "polygon": [[29.25, 40.5], [193.5, 40.5], [193.5, 51.787353515625], [29.25, 51.787353515625]]}, {"title": "", "heading_level": null, "page_id": 19, "polygon": [[31.16162109375, 424.3798828125], [89.0859375, 424.3798828125], [89.0859375, 434.1357421875], [31.16162109375, 434.1357421875]]}, {"title": "6.4.3 Learning the hyperparameters", "heading_level": null, "page_id": 20, "polygon": [[138.75, 117.0], [341.25, 117.0], [341.25, 128.696044921875], [138.75, 128.696044921875]]}, {"title": "312 6. KERNEL METHODS", "heading_level": null, "page_id": 21, "polygon": [[29.25, 40.5], [193.5, 40.5], [193.5, 51.9093017578125], [29.25, 51.9093017578125]]}, {"title": "6.4.4 Automatic relevance determination", "heading_level": null, "page_id": 21, "polygon": [[137.25, 287.25], [366.0, 287.25], [366.0, 299.1796875], [137.25, 299.1796875]]}, {"title": "6.4.5 Gaussian processes for classification", "heading_level": null, "page_id": 22, "polygon": [[138.0, 457.5], [381.75, 457.5], [381.75, 467.9560546875], [138.0, 467.9560546875]]}, {"title": "6.4.6 <PERSON><PERSON> approximation", "heading_level": null, "page_id": 24, "polygon": [[138.0, 439.9892578125], [301.7109375, 439.9892578125], [301.7109375, 451.0458984375], [138.0, 451.0458984375]]}, {"title": "316 6. KERNEL METHODS", "heading_level": null, "page_id": 25, "polygon": [[29.25, 40.5], [193.5, 40.5], [193.5, 51.299560546875], [29.25, 51.299560546875]]}, {"title": "", "heading_level": null, "page_id": 25, "polygon": [[29.977294921875, 578.197265625], [89.7626953125, 578.197265625], [89.7626953125, 587.302734375], [29.977294921875, 587.302734375]]}, {"title": "318 6. <PERSON>ER<PERSON><PERSON> METHODS", "heading_level": null, "page_id": 27, "polygon": [[29.25, 40.5], [193.5, 40.5], [193.5, 51.3402099609375], [29.25, 51.3402099609375]]}, {"title": "6.4.7 Connection to neural networks", "heading_level": null, "page_id": 28, "polygon": [[138.75, 291.75], [345.0, 291.75], [345.0, 303.732421875], [138.75, 303.732421875]]}, {"title": "320 6. KERNEL METHODS", "heading_level": null, "page_id": 29, "polygon": [[30.0, 40.5], [193.5, 40.5], [193.5, 51.86865234375], [30.0, 51.86865234375]]}, {"title": "Exercises", "heading_level": null, "page_id": 29, "polygon": [[30.0, 181.5], [91.5, 181.5], [91.5, 194.95458984375], [30.0, 194.95458984375]]}, {"title": "322 6. KERNEL METHODS", "heading_level": null, "page_id": 31, "polygon": [[30.0, 40.5], [193.5, 40.5], [193.5, 51.9093017578125], [30.0, 51.9093017578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 18], ["Text", 4], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6841, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 47], ["Text", 7], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1155, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 75], ["Equation", 7], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 49], ["Text", 7], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1203, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 71], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8459, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 673], ["Line", 66], ["Equation", 11], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 66], ["Text", 9], ["Equation", 6], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 426], ["Line", 59], ["Text", 9], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1834, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 47], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 52], ["Text", 7], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 42], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1399, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 54], ["Equation", 6], ["Text", 5], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 54], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 42], ["Text", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 54], ["Equation", 6], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 44], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 728, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 47], ["TextInlineMath", 6], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 63], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1177, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 45], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1235, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 52], ["Text", 4], ["SectionHeader", 2], ["Figure", 2], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 68], ["Text", 8], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 62], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 57], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 41], ["Equation", 3], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 48], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2573, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 57], ["TextInlineMath", 6], ["Equation", 5], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1194, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["Line", 50], ["Equation", 8], ["Text", 7], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 610], ["Line", 94], ["TextInlineMath", 4], ["Text", 4], ["Equation", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8694, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 45], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 685, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 39], ["ListItem", 10], ["SectionHeader", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 37], ["ListItem", 7], ["Equation", 4], ["Text", 4], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 533], ["Line", 46], ["ListItem", 7], ["ListGroup", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 10], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_311-344"}