{"table_of_contents": [{"title": "86 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 0, "polygon": [[29.25, 40.5], [246.0, 40.5], [246.0, 51.5028076171875], [29.25, 51.5028076171875]]}, {"title": "2.3. The Gaussian Distribution 87", "heading_level": null, "page_id": 1, "polygon": [[288.75, 40.5], [473.25, 40.5], [473.25, 50.9337158203125], [288.75, 50.9337158203125]]}, {"title": "2.3.2 Marginal Gaussian distributions", "heading_level": null, "page_id": 2, "polygon": [[137.25, 71.25], [351.0, 71.25], [351.0, 83.1280517578125], [137.25, 83.1280517578125]]}, {"title": "2.3. The Gaussian Distribution 89", "heading_level": null, "page_id": 3, "polygon": [[288.75, 40.5], [474.0, 40.5], [474.0, 50.811767578125], [288.75, 50.811767578125]]}, {"title": "Partitioned Gaussians", "heading_level": null, "page_id": 3, "polygon": [[138.3046875, 554.25], [242.25, 554.25], [242.25, 564.8642578125], [138.3046875, 564.8642578125]]}, {"title": "90 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 4, "polygon": [[29.25, 40.5], [245.25, 40.5], [245.25, 51.299560546875], [29.25, 51.299560546875]]}, {"title": "2.3.3 <PERSON><PERSON>' theorem for Gaussian variables", "heading_level": null, "page_id": 4, "polygon": [[136.08984375, 517.5], [386.25, 517.5], [386.25, 529.41796875], [136.08984375, 529.41796875]]}, {"title": "92 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 6, "polygon": [[29.25, 40.5], [246.0, 40.5], [246.0, 51.4215087890625], [29.25, 51.4215087890625]]}, {"title": "Marginal and Conditional Gaussians", "heading_level": null, "page_id": 7, "polygon": [[137.07421875, 71.25], [308.25, 71.25], [308.25, 83.25], [137.07421875, 83.25]]}, {"title": "2.3.4 Maximum likelihood for the Gaussian", "heading_level": null, "page_id": 7, "polygon": [[137.443359375, 284.25], [380.4609375, 284.25], [380.4609375, 295.6025390625], [137.443359375, 295.6025390625]]}, {"title": "94 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 8, "polygon": [[29.25, 40.5], [245.25, 40.5], [245.25, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "2.3.5 Sequential estimation", "heading_level": null, "page_id": 8, "polygon": [[137.25, 438.75], [294.8203125, 438.75], [294.8203125, 449.419921875], [137.25, 449.419921875]]}, {"title": "96 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 10, "polygon": [[29.25, 40.5], [246.0, 40.5], [246.0, 51.2589111328125], [29.25, 51.2589111328125]]}, {"title": "2.3.6 Bayesian inference for the Gaussian", "heading_level": null, "page_id": 11, "polygon": [[138.75, 421.5], [375.0, 421.5], [375.0, 432.509765625], [138.75, 432.509765625]]}, {"title": "98 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 12, "polygon": [[29.25, 40.5], [245.25, 40.5], [245.25, 51.6654052734375], [29.25, 51.6654052734375]]}, {"title": "102 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 16, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.9906005859375], [30.0, 51.9906005859375]]}, {"title": "2.3.7 Student's t-distribution", "heading_level": null, "page_id": 16, "polygon": [[137.25, 524.25], [301.7109375, 524.25], [301.7109375, 536.572265625], [137.25, 536.572265625]]}, {"title": "2.3. The Gaussian Distribution 103", "heading_level": null, "page_id": 17, "polygon": [[282.26953125, 40.5], [474.0, 40.5], [474.0, 50.73046875], [282.26953125, 50.73046875]]}, {"title": "", "heading_level": null, "page_id": 17, "polygon": [[28.6083984375, 608.765625], [91.8544921875, 607.46484375], [91.8544921875, 617.220703125], [28.6083984375, 618.521484375]]}, {"title": "104 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 18, "polygon": [[30.0, 40.5], [251.384765625, 40.5], [251.384765625, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "2.3. The Gaussian Distribution 105", "heading_level": null, "page_id": 19, "polygon": [[283.5, 40.5], [473.25, 40.5], [473.25, 50.974365234375], [283.5, 50.974365234375]]}, {"title": "2.3.8 Periodic variables", "heading_level": null, "page_id": 19, "polygon": [[138.55078125, 268.5], [274.640625, 268.5], [274.640625, 280.318359375], [138.55078125, 280.318359375]]}, {"title": "106 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 20, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.624755859375], [30.0, 51.624755859375]]}, {"title": "2.3. The Gaussian Distribution 107", "heading_level": null, "page_id": 21, "polygon": [[281.28515625, 40.5], [473.25, 40.5], [473.25, 50.73046875], [281.28515625, 50.73046875]]}, {"title": "108 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 22, "polygon": [[30.0, 40.5], [251.25, 40.5], [251.25, 51.299560546875], [30.0, 51.299560546875]]}, {"title": "110 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 24, "polygon": [[30.0, 40.5], [251.25, 40.5], [251.25, 51.5841064453125], [30.0, 51.5841064453125]]}, {"title": "2.3.9 Mixtures of Gaussians", "heading_level": null, "page_id": 24, "polygon": [[137.25, 540.75], [298.5, 540.75], [298.5, 552.5068359375], [137.25, 552.5068359375]]}, {"title": "112 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 26, "polygon": [[29.9619140625, 40.5], [251.25, 40.5], [251.25, 51.136962890625], [29.9619140625, 51.136962890625]]}, {"title": "2.4. The Exponential Family", "heading_level": null, "page_id": 27, "polygon": [[89.25, 192.75], [266.25, 192.75], [266.25, 206.33642578125], [89.25, 206.33642578125]]}, {"title": "114 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 28, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.5028076171875], [30.0, 51.5028076171875]]}, {"title": "116 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 30, "polygon": [[30.0, 40.5], [251.25, 39.0], [252.0, 51.624755859375], [30.0, 51.624755859375]]}, {"title": "2.4.1 Maximum likelihood and sufficient statistics", "heading_level": null, "page_id": 30, "polygon": [[135.720703125, 214.5], [416.25, 214.5], [416.25, 226.49853515625], [135.720703125, 226.49853515625]]}, {"title": "2.4.2 Conjugate priors", "heading_level": null, "page_id": 31, "polygon": [[138.0, 248.25], [268.5, 248.25], [268.5, 259.505859375], [138.0, 259.505859375]]}, {"title": "2.4.3 Noninformative priors", "heading_level": null, "page_id": 31, "polygon": [[138.75, 549.75], [296.296875, 549.75], [296.296875, 560.9619140625], [138.75, 560.9619140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 72], ["TextInlineMath", 5], ["Equation", 4], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8428, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 57], ["Equation", 9], ["TextInlineMath", 6], ["Text", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 56], ["Equation", 4], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 66], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1439, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 45], ["Equation", 4], ["Text", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 78], ["Equation", 7], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5223, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 54], ["Equation", 7], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 641, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 59], ["Equation", 9], ["Text", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 618, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 44], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 61], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 71], ["Equation", 8], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 54], ["TextInlineMath", 5], ["Figure", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1415, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 60], ["Equation", 6], ["Text", 5], ["SectionHeader", 1], ["ListItem", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 69], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1322, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 70], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 808, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 570], ["Line", 85], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 9188, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 47], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 2], ["Text", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1649, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 462], ["Line", 70], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2879, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 47], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 732, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 53], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 54], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 707, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 455], ["Line", 53], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1163, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 39], ["Equation", 5], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1397, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 69], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1824, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 57], ["Text", 5], ["SectionHeader", 2], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 41], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 648, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 52], ["Text", 3], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6872, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 48], ["Text", 6], ["Equation", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 973, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 50], ["Equation", 12], ["TextInlineMath", 6], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 98], ["Equation", 9], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2167, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 73], ["Equation", 9], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2030, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 50], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_106-137"}