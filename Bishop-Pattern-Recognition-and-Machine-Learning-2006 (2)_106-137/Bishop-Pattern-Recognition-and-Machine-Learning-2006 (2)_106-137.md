evaluated from the joint distribution  $p(\mathbf{x}) = p(\mathbf{x}_a, \mathbf{x}_b)$  simply by fixing  $\mathbf{x}_b$  to the observed value and normalizing the resulting expression to obtain a valid probability distribution over  $x_a$ . Instead of performing this normalization explicitly, we can obtain the solution more efficiently by considering the quadratic form in the exponent of the Gaussian distribution given by (2.44) and then reinstating the normalization coefficient at the end of the calculation. If we make use of the partitioning (2.65), (2.66), and (2.69), we obtain

$$
-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu}) =
$$
  
$$
-\frac{1}{2}(\mathbf{x}_a - \boldsymbol{\mu}_a)^{\mathrm{T}} \boldsymbol{\Lambda}_{aa}(\mathbf{x}_a - \boldsymbol{\mu}_a) - \frac{1}{2}(\mathbf{x}_a - \boldsymbol{\mu}_a)^{\mathrm{T}} \boldsymbol{\Lambda}_{ab}(\mathbf{x}_b - \boldsymbol{\mu}_b)
$$
  
$$
-\frac{1}{2}(\mathbf{x}_b - \boldsymbol{\mu}_b)^{\mathrm{T}} \boldsymbol{\Lambda}_{ba}(\mathbf{x}_a - \boldsymbol{\mu}_a) - \frac{1}{2}(\mathbf{x}_b - \boldsymbol{\mu}_b)^{\mathrm{T}} \boldsymbol{\Lambda}_{bb}(\mathbf{x}_b - \boldsymbol{\mu}_b).
$$
 (2.70)

We see that as a function of  $x_a$ , this is again a quadratic form, and hence the corresponding conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  will be Gaussian. Because this distribution is completely characterized by its mean and its covariance, our goal will be to identify expressions for the mean and covariance of  $p(\mathbf{x}_a|\mathbf{x}_b)$  by inspection of  $(2.70).$ 

This is an example of a rather common operation associated with Gaussian distributions, sometimes called 'completing the square', in which we are given a quadratic form defining the exponent terms in a Gaussian distribution, and we need to determine the corresponding mean and covariance. Such problems can be solved straightforwardly by noting that the exponent in a general Gaussian distribution  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  can be written

$$
-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu}) = -\frac{1}{2} \mathbf{x}^{\mathrm{T}} \boldsymbol{\Sigma}^{-1} \mathbf{x} + \mathbf{x}^{\mathrm{T}} \boldsymbol{\Sigma}^{-1} \boldsymbol{\mu} + \text{const}
$$
 (2.71)

where 'const' denotes terms which are independent of **x**, and we have made use of the symmetry of  $\Sigma$ . Thus if we take our general quadratic form and express it in the form given by the right-hand side of  $(2.71)$ , then we can immediately equate the matrix of coefficients entering the second order term in **x** to the inverse covariance<br>matrix  $\Sigma^{-1}$  and the coefficient of the linear term in **x** to  $\Sigma^{-1}$  (*t* from which we can matrix  $\Sigma^{-1}$  and the coefficient of the linear term in **x** to  $\Sigma^{-1}\mu$ , from which we can obtain  $\mu$ obtain *µ*.

Now let us apply this procedure to the conditional Gaussian distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$ for which the quadratic form in the exponent is given by (2.70). We will denote the mean and covariance of this distribution by  $\mu_{a|b}$  and  $\Sigma_{a|b}$ , respectively. Consider the functional dependence of (2.70) on  $x_a$  in which  $x_b$  is regarded as a constant. If we pick out all terms that are second order in  $x_a$ , we have

$$
-\frac{1}{2}\mathbf{x}_{a}^{\mathrm{T}}\boldsymbol{\Lambda}_{aa}\mathbf{x}_{a}
$$
 (2.72)

from which we can immediately conclude that the covariance (inverse precision) of  $p(\mathbf{x}_a|\mathbf{x}_b)$  is given by

$$
\Sigma_{a|b} = \Lambda_{aa}^{-1}.
$$
\n(2.73)

## **2.3. The Gaussian Distribution 87**

Now consider all of the terms in  $(2.70)$  that are linear in  $x_a$ 

$$
\mathbf{x}_a^{\mathrm{T}} \left\{ \mathbf{\Lambda}_{aa} \boldsymbol{\mu}_a - \mathbf{\Lambda}_{ab} (\mathbf{x}_b - \boldsymbol{\mu}_b) \right\} \tag{2.74}
$$

where we have used  $\Lambda_{ba}^{\text{T}} = \Lambda_{ab}$ . From our discussion of the general form (2.71), the coefficient of x in this expression must equal  $\Sigma^{-1}u$  is and hence the coefficient of  $\mathbf{x}_a$  in this expression must equal  $\sum_{a|b}^{-1} \mu_{a|b}$  and hence

$$
\mu_{a|b} = \Sigma_{a|b} \{ \Lambda_{aa} \mu_a - \Lambda_{ab} (\mathbf{x}_b - \mu_b) \}
$$
  
=  $\mu_a - \Lambda_{aa}^{-1} \Lambda_{ab} (\mathbf{x}_b - \mu_b)$  (2.75)

where we have made use of (2.73).

The results (2.73) and (2.75) are expressed in terms of the partitioned precision matrix of the original joint distribution  $p(\mathbf{x}_a, \mathbf{x}_b)$ . We can also express these results in terms of the corresponding partitioned covariance matrix. To do this, we make use *Exercise* 2.24 of the following identity for the inverse of a partitioned matrix

$$
\begin{pmatrix} A & B \ C & D \end{pmatrix}^{-1} = \begin{pmatrix} M & -MBD^{-1} \\ -D^{-1}CM & D^{-1} + D^{-1}CMBD^{-1} \end{pmatrix}
$$
 (2.76)

where we have defined

$$
M = (A - BD^{-1}C)^{-1}.
$$
 (2.77)

The quantity **M**<sup>−</sup><sup>1</sup> is known as the *Schur complement* of the matrix on the left-hand side of (2.76) with respect to the submatrix **D**. Using the definition

$$
\begin{pmatrix} \Sigma_{aa} & \Sigma_{ab} \\ \Sigma_{ba} & \Sigma_{bb} \end{pmatrix}^{-1} = \begin{pmatrix} \Lambda_{aa} & \Lambda_{ab} \\ \Lambda_{ba} & \Lambda_{bb} \end{pmatrix}
$$
 (2.78)

and making use of (2.76), we have

$$
\Lambda_{aa} = (\Sigma_{aa} - \Sigma_{ab} \Sigma_{bb}^{-1} \Sigma_{ba})^{-1}
$$
\n
$$
\Lambda_{aa} = (\Sigma_{aa} - \Sigma_{ab} \Sigma_{bb}^{-1} \Sigma_{ba})^{-1}
$$
\n
$$
(2.79)
$$

$$
\Lambda_{ab} = -(\Sigma_{aa} - \Sigma_{ab}\Sigma_{bb}^{-1}\Sigma_{ba})^{-1}\Sigma_{ab}\Sigma_{bb}^{-1}.
$$
 (2.80)

From these we obtain the following expressions for the mean and covariance of the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$ 

$$
\boldsymbol{\mu}_{a|b} = \boldsymbol{\mu}_a + \boldsymbol{\Sigma}_{ab} \boldsymbol{\Sigma}_{bb}^{-1} (\mathbf{x}_b - \boldsymbol{\mu}_b) \tag{2.81}
$$

$$
\Sigma_{a|b} = \Sigma_{aa} - \Sigma_{ab} \Sigma_{bb}^{-1} \Sigma_{ba}.
$$
 (2.82)

Comparing (2.73) and (2.82), we see that the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  takes a simpler form when expressed in terms of the partitioned precision matrix than when it is expressed in terms of the partitioned covariance matrix. Note that the mean of the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$ , given by (2.81), is a linear function of  $x_b$  and that the covariance, given by (2.82), is independent of  $x_a$ . This represents an *Section 8.1.4* example of a *linear-Gaussian* model.

*Section 8.1.4*

## **2.3.2 Marginal Gaussian distributions**

We have seen that if a joint distribution  $p(\mathbf{x}_a, \mathbf{x}_b)$  is Gaussian, then the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  will again be Gaussian. Now we turn to a discussion of the marginal distribution given by

$$
p(\mathbf{x}_a) = \int p(\mathbf{x}_a, \mathbf{x}_b) \, \mathrm{d}\mathbf{x}_b \tag{2.83}
$$

which, as we shall see, is also Gaussian. Once again, our strategy for evaluating this distribution efficiently will be to focus on the quadratic form in the exponent of the joint distribution and thereby to identify the mean and covariance of the marginal distribution  $p(\mathbf{x}_a)$ .

The quadratic form for the joint distribution can be expressed, using the partitioned precision matrix, in the form (2.70). Because our goal is to integrate out  $x_b$ , this is most easily achieved by first considering the terms involving  $x_b$  and then completing the square in order to facilitate integration. Picking out just those terms that involve  $x_b$ , we have

$$
-\frac{1}{2}\mathbf{x}_{b}^{\mathrm{T}}\mathbf{\Lambda}_{bb}\mathbf{x}_{b}+\mathbf{x}_{b}^{\mathrm{T}}\mathbf{m}=-\frac{1}{2}(\mathbf{x}_{b}-\mathbf{\Lambda}_{bb}^{-1}\mathbf{m})^{\mathrm{T}}\mathbf{\Lambda}_{bb}(\mathbf{x}_{b}-\mathbf{\Lambda}_{bb}^{-1}\mathbf{m})+\frac{1}{2}\mathbf{m}^{\mathrm{T}}\mathbf{\Lambda}_{bb}^{-1}\mathbf{m}
$$
 (2.84)

where we have defined

$$
\mathbf{m} = \mathbf{\Lambda}_{bb} \boldsymbol{\mu}_b - \mathbf{\Lambda}_{ba} (\mathbf{x}_a - \boldsymbol{\mu}_a). \tag{2.85}
$$

We see that the dependence on  $x_b$  has been cast into the standard quadratic form of a Gaussian distribution corresponding to the first term on the right-hand side of (2.84), plus a term that does not depend on  $x_b$  (but that does depend on  $x_a$ ). Thus, when we take the exponential of this quadratic form, we see that the integration over  $\mathbf{x}_b$ required by (2.83) will take the form

$$
\int \exp\left\{-\frac{1}{2}(\mathbf{x}_b - \mathbf{\Lambda}_{bb}^{-1}\mathbf{m})^{\mathrm{T}}\mathbf{\Lambda}_{bb}(\mathbf{x}_b - \mathbf{\Lambda}_{bb}^{-1}\mathbf{m})\right\} d\mathbf{x}_b.
$$
 (2.86)

This integration is easily performed by noting that it is the integral over an unnormalized Gaussian, and so the result will be the reciprocal of the normalization coefficient. We know from the form of the normalized Gaussian given by (2.43), that this coefficient is independent of the mean and depends only on the determinant of the covariance matrix. Thus, by completing the square with respect to  $x_b$ , we can integrate out  $\mathbf{x}_b$  and the only term remaining from the contributions on the left-hand side of  $(2.84)$  that depends on  $x_a$  is the last term on the right-hand side of  $(2.84)$  in which **m** is given by (2.85). Combining this term with the remaining terms from

## **2.3. The Gaussian Distribution 89**

(2.70) that depend on  $x_a$ , we obtain

$$
1/2 [ \Lambda_{bb} \mu_b - \Lambda_{ba} (x_a - \mu_a) ]^T \Lambda_{bb}^{-1} [ \Lambda_{bb} \mu_b - \Lambda_{ba} (x_a - \mu_a) ]
$$

$$
- 1/2 x_a^T \Lambda_{aa} x_a + x_a^T (\Lambda_{aa} \mu_a + \Lambda_{ab} \mu_b) + const
$$

= 
$$
- 1/2 x_a^T (\Lambda_{aa} - \Lambda_{ab} \Lambda_{bb}^{-1} \Lambda_{ba}) x_a
$$

$$
+ x_a^T (\Lambda_{aa} - \Lambda_{ab} \Lambda_{bb}^{-1} \Lambda_{ba})^{-1} \mu_a + const
$$
(2.87)

where 'const' denotes quantities independent of  $x_a$ . Again, by comparison with (2.71), we see that the covariance of the marginal distribution of  $p(\mathbf{x}_a)$  is given by

$$
\Sigma_a = (\Lambda_{aa} - \Lambda_{ab}\Lambda_{bb}^{-1}\Lambda_{ba})^{-1}.
$$
 (2.88)

Similarly, the mean is given by

$$
\Sigma_a (\Lambda_{aa} - \Lambda_{ab} \Lambda_{bb}^{-1} \Lambda_{ba}) \mu_a = \mu_a \tag{2.89}
$$

where we have used  $(2.88)$ . The covariance in  $(2.88)$  is expressed in terms of the partitioned precision matrix given by (2.69). We can rewrite this in terms of the corresponding partitioning of the covariance matrix given by (2.67), as we did for the conditional distribution. These partitioned matrices are related by

$$
\begin{pmatrix}\n\Lambda_{aa} & \Lambda_{ab} \\
\Lambda_{ba} & \Lambda_{bb}\n\end{pmatrix}^{-1} = \begin{pmatrix}\n\Sigma_{aa} & \Sigma_{ab} \\
\Sigma_{ba} & \Sigma_{bb}\n\end{pmatrix}
$$
\n(2.90)

Making use of (2.76), we then have

$$
\left(\Lambda_{aa} - \Lambda_{ab}\Lambda_{bb}^{-1}\Lambda_{ba}\right)^{-1} = \Sigma_{aa}.
$$
\n(2.91)

Thus we obtain the intuitively satisfying result that the marginal distribution  $p(\mathbf{x}_a)$ has mean and covariance given by

$$
\mathbb{E}[\mathbf{x}_a] = \boldsymbol{\mu}_a \tag{2.92}
$$

$$
cov[\mathbf{x}_a] = \Sigma_{aa}.
$$
 (2.93)

We see that for a marginal distribution, the mean and covariance are most simply expressed in terms of the partitioned covariance matrix, in contrast to the conditional distribution for which the partitioned precision matrix gives rise to simpler expressions.

Our results for the marginal and conditional distributions of a partitioned Gaussian are summarized below.

## Partitioned Gaussians

Given a joint Gaussian distribution  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  with  $\boldsymbol{\Lambda} \equiv \boldsymbol{\Sigma}^{-1}$  and

$$
\mathbf{x} = \begin{pmatrix} \mathbf{x}_a \\ \mathbf{x}_b \end{pmatrix}, \quad \boldsymbol{\mu} = \begin{pmatrix} \boldsymbol{\mu}_a \\ \boldsymbol{\mu}_b \end{pmatrix}
$$
 (2.94)

Image /page/4/Figure/1 description: The image displays two plots. The left plot is a contour plot showing the joint probability distribution p(xa, xb) with green contour lines. A horizontal red line is drawn at xb = 0.7, indicating a specific value for xb. The x-axis is labeled 'xa' and ranges from 0 to 1, and the y-axis is labeled 'xb' and ranges from 0 to 1. The right plot shows two probability density functions, p(xa) and p(xa|xb = 0.7), plotted against 'xa' on the x-axis, which ranges from 0 to 1. The p(xa) distribution is shown in blue, and the p(xa|xb = 0.7) distribution is shown in red. The y-axis of the right plot is labeled with values from 0 to 10, representing the probability density.

**Figure 2.9** The plot on the left shows the contours of a Gaussian distribution  $p(x_a, x_b)$  over two variables, and the plot on the right shows the marginal distribution  $p(x_a)$  (blue curve) and the conditional distribution  $p(x_a|x_b)$ for  $x_b = 0.7$  (red curve).

$$
\Sigma = \begin{pmatrix} \Sigma_{aa} & \Sigma_{ab} \\ \Sigma_{ba} & \Sigma_{bb} \end{pmatrix}, \quad \Lambda = \begin{pmatrix} \Lambda_{aa} & \Lambda_{ab} \\ \Lambda_{ba} & \Lambda_{bb} \end{pmatrix}.
$$
 (2.95)

Conditional distribution:

$$
p(\mathbf{x}_a|\mathbf{x}_b) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_{a|b}, \boldsymbol{\Lambda}_{aa}^{-1})
$$
 (2.96)

$$
\boldsymbol{\mu}_{a|b} = \boldsymbol{\mu}_a - \boldsymbol{\Lambda}_{aa}^{-1} \boldsymbol{\Lambda}_{ab} (\mathbf{x}_b - \boldsymbol{\mu}_b). \tag{2.97}
$$

Marginal distribution:

$$
p(\mathbf{x}_a) = \mathcal{N}(\mathbf{x}_a | \boldsymbol{\mu}_a, \boldsymbol{\Sigma}_{aa}).
$$
\n(2.98)

We illustrate the idea of conditional and marginal distributions associated with a multivariate Gaussian using an example involving two variables in Figure 2.9.

## **2.3.3 Bayes' theorem for Gaussian variables**

In Sections 2.3.1 and 2.3.2, we considered a Gaussian  $p(x)$  in which we partitioned the vector **x** into two subvectors  $\mathbf{x} = (\mathbf{x}_a, \mathbf{x}_b)$  and then found expressions for the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  and the marginal distribution  $p(\mathbf{x}_a)$ . We noted that the mean of the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  was a linear function of  $\mathbf{x}_b$ . Here we shall suppose that we are given a Gaussian marginal distribution  $p(x)$  and a Gaussian conditional distribution  $p(y|x)$  in which  $p(y|x)$  has a mean that is a linear function of **x**, and a covariance which is independent of **x**. This is an example of a *linear Gaussian model* (Roweis and Ghahramani, 1999), which we shall study in greater generality in Section 8.1.4. We wish to find the marginal distribution  $p(y)$ and the conditional distribution  $p(x|y)$ . This is a problem that will arise frequently in subsequent chapters, and it will prove convenient to derive the general results here.

We shall take the marginal and conditional distributions to be

$$
p(\mathbf{x}) = \mathcal{N}\left(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda}^{-1}\right) \tag{2.99}
$$

$$
p(\mathbf{y}|\mathbf{x}) = \mathcal{N}\left(\mathbf{y}|\mathbf{A}\mathbf{x} + \mathbf{b}, \mathbf{L}^{-1}\right)
$$
 (2.100)

where  $\mu$ , **A**, and **b** are parameters governing the means, and **Λ** and **L** are precision matrices. If **x** has dimensionality <sup>M</sup> and **y** has dimensionality <sup>D</sup>, then the matrix **A** has size  $D \times M$ .

First we find an expression for the joint distribution over **x** and **y**. To do this, we define

$$
z = \begin{pmatrix} x \\ y \end{pmatrix} \tag{2.101}
$$

and then consider the log of the joint distribution

$$
\ln p(\mathbf{z}) = \ln p(\mathbf{x}) + \ln p(\mathbf{y}|\mathbf{x})
$$

$$
= -\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \mathbf{\Lambda}(\mathbf{x} - \boldsymbol{\mu})
$$

$$
= -\frac{1}{2}(\mathbf{y} - \mathbf{A}\mathbf{x} - \mathbf{b})^{\mathrm{T}} \mathbf{L}(\mathbf{y} - \mathbf{A}\mathbf{x} - \mathbf{b}) + \text{const}
$$
 (2.102)

where 'const' denotes terms independent of **x** and **y**. As before, we see that this is a quadratic function of the components of **z**, and hence  $p(z)$  is Gaussian distribution. To find the precision of this Gaussian, we consider the second order terms in (2.102), which can be written as

$$
-\frac{1}{2}\mathbf{x}^{\mathrm{T}}(\mathbf{\Lambda} + \mathbf{A}^{\mathrm{T}}\mathbf{L}\mathbf{A})\mathbf{x} - \frac{1}{2}\mathbf{y}^{\mathrm{T}}\mathbf{L}\mathbf{y} + \frac{1}{2}\mathbf{y}^{\mathrm{T}}\mathbf{L}\mathbf{A}\mathbf{x} + \frac{1}{2}\mathbf{x}^{\mathrm{T}}\mathbf{A}^{\mathrm{T}}\mathbf{L}\mathbf{y}
$$
$$
= -\frac{1}{2}\begin{pmatrix} \mathbf{x} \ \mathbf{y} \end{pmatrix}^{\mathrm{T}} \begin{pmatrix} \mathbf{\Lambda} + \mathbf{A}^{\mathrm{T}}\mathbf{L}\mathbf{A} & -\mathbf{A}^{\mathrm{T}}\mathbf{L} \\ -\mathbf{L}\mathbf{A} & \mathbf{L} \end{pmatrix} \begin{pmatrix} \mathbf{x} \ \mathbf{y} \end{pmatrix} = -\frac{1}{2}\mathbf{z}^{\mathrm{T}}\mathbf{R}\mathbf{z} \quad (2.103)
$$

and so the Gaussian distribution over **z** has precision (inverse covariance) matrix given by

$$
\mathbf{R} = \begin{pmatrix} \mathbf{\Lambda} + \mathbf{A}^{\mathrm{T}} \mathbf{L} \mathbf{A} & -\mathbf{A}^{\mathrm{T}} \mathbf{L} \\ -\mathbf{L} \mathbf{A} & \mathbf{L} \end{pmatrix}.
$$
 (2.104)

The covariance matrix is found by taking the inverse of the precision, which can be *Exercise* 2.29 done using the matrix inversion formula (2.76) to give

$$
cov[\mathbf{z}] = \mathbf{R}^{-1} = \begin{pmatrix} \mathbf{\Lambda}^{-1} & \mathbf{\Lambda}^{-1} \mathbf{A}^{T} \\ \mathbf{A} \mathbf{\Lambda}^{-1} & \mathbf{L}^{-1} + \mathbf{A} \mathbf{\Lambda}^{-1} \mathbf{A}^{T} \end{pmatrix}.
$$
 (2.105)

*Exercise 2.29*

Similarly, we can find the mean of the Gaussian distribution over **z** by identifying the linear terms in (2.102), which are given by

$$
\mathbf{x}^{\mathrm{T}} \mathbf{\Lambda} \boldsymbol{\mu} - \mathbf{x}^{\mathrm{T}} \mathbf{A}^{\mathrm{T}} \mathbf{L} \mathbf{b} + \mathbf{y}^{\mathrm{T}} \mathbf{L} \mathbf{b} = \begin{pmatrix} \mathbf{x} \\ \mathbf{y} \end{pmatrix}^{\mathrm{T}} \begin{pmatrix} \mathbf{\Lambda} \boldsymbol{\mu} - \mathbf{A}^{\mathrm{T}} \mathbf{L} \mathbf{b} \\ \mathbf{L} \mathbf{b} \end{pmatrix}.
$$
 (2.106)

Using our earlier result (2.71) obtained by completing the square over the quadratic form of a multivariate Gaussian, we find that the mean of **z** is given by

$$
\mathbb{E}[\mathbf{z}] = \mathbf{R}^{-1} \begin{pmatrix} \mathbf{\Lambda} \boldsymbol{\mu} - \mathbf{A}^{\mathrm{T}} \mathbf{L} \mathbf{b} \\ \mathbf{L} \mathbf{b} \end{pmatrix} . \tag{2.107}
$$

*Exercise* 2.30 Making use of (2.105), we then obtain

$$
\mathbb{E}[\mathbf{z}] = \begin{pmatrix} \mu \\ \mathbf{A}\mu + \mathbf{b} \end{pmatrix}.
$$
 (2.108)

Next we find an expression for the marginal distribution  $p(y)$  in which we have marginalized over **x**. Recall that the marginal distribution over a subset of the components of a Gaussian random vector takes a particularly simple form when ex-*Section 2.3* pressed in terms of the partitioned covariance matrix. Specifically, its mean and covariance are given by (2.92) and (2.93), respectively. Making use of (2.105) and (2.108) we see that the mean and covariance of the marginal distribution  $p(\mathbf{y})$  are given by

$$
\mathbb{E}[\mathbf{y}] = \mathbf{A}\boldsymbol{\mu} + \mathbf{b} \tag{2.109}
$$

$$
cov[\mathbf{y}] = \mathbf{L}^{-1} + \mathbf{A}\mathbf{\Lambda}^{-1}\mathbf{A}^{\mathrm{T}}.
$$
 (2.110)

A special case of this result is when  $A = I$ , in which case it reduces to the convolution of two Gaussians, for which we see that the mean of the convolution is the sum of the mean of the two Gaussians, and the covariance of the convolution is the sum of their covariances.

Finally, we seek an expression for the conditional  $p(x|y)$ . Recall that the results for the conditional distribution are most easily expressed in terms of the partitioned *Section 2.3* precision matrix, using (2.73) and (2.75). Applying these results to (2.105) and (2.108) we see that the conditional distribution  $p(\mathbf{x}|\mathbf{y})$  has mean and covariance given by

$$
\mathbb{E}[\mathbf{x}|\mathbf{y}] = (\mathbf{\Lambda} + \mathbf{A}^{\mathrm{T}} \mathbf{L} \mathbf{A})^{-1} \{ \mathbf{A}^{\mathrm{T}} \mathbf{L} (\mathbf{y} - \mathbf{b}) + \mathbf{\Lambda} \boldsymbol{\mu} \}
$$
(2.111)

$$
cov[\mathbf{x}|\mathbf{y}] = (\mathbf{\Lambda} + \mathbf{A}^{\mathrm{T}} \mathbf{L} \mathbf{A})^{-1}.
$$
 (2.112)

The evaluation of this conditional can be seen as an example of Bayes' theorem. We can interpret the distribution  $p(x)$  as a prior distribution over x. If the variable **y** is observed, then the conditional distribution  $p(\mathbf{x}|\mathbf{y})$  represents the corresponding posterior distribution over **x**. Having found the marginal and conditional distributions, we effectively expressed the joint distribution  $p(\mathbf{z}) = p(\mathbf{x})p(\mathbf{y}|\mathbf{x})$  in the form  $p(\mathbf{x}|\mathbf{y})p(\mathbf{y})$ . These results are summarized below.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment Name         | Alpha                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

This experiment was a significant step forward in our research.

## Marginal and Conditional Gaussians

Given a marginal Gaussian distribution for **x** and a conditional Gaussian distribution for **y** given **x** in the form

$$
p(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda}^{-1})
$$
 (2.113)

$$
p(\mathbf{y}|\mathbf{x}) = \mathcal{N}(\mathbf{y}|\mathbf{A}\mathbf{x} + \mathbf{b}, \mathbf{L}^{-1})
$$
 (2.114)

the marginal distribution of **y** and the conditional distribution of **x** given **y** are given by

$$
p(\mathbf{y}) = \mathcal{N}(\mathbf{y}|\mathbf{A}\boldsymbol{\mu} + \mathbf{b}, \mathbf{L}^{-1} + \mathbf{A}\boldsymbol{\Lambda}^{-1}\mathbf{A}^{\mathrm{T}})
$$
(2.115)

$$
p(\mathbf{x}|\mathbf{y}) = \mathcal{N}(\mathbf{x}|\mathbf{\Sigma}\{\mathbf{A}^{T}\mathbf{L}(\mathbf{y}-\mathbf{b})+\mathbf{\Lambda}\boldsymbol{\mu}\}, \mathbf{\Sigma})
$$
 (2.116)

where

$$
\Sigma = (\Lambda + A^{T}LA)^{-1}.
$$
 (2.117)

## **2.3.4 Maximum likelihood for the Gaussian**

Given a data set  $\mathbf{X} = (\mathbf{x}_1, \dots, \mathbf{x}_N)^T$  in which the observations  $\{\mathbf{x}_n\}$  are assumed to be drawn independently from a multivariate Gaussian distribution, we can estimate the parameters of the distribution by maximum likelihood. The log likelihood function is given by

$$
\ln p(\mathbf{X}|\boldsymbol{\mu}, \boldsymbol{\Sigma}) = -\frac{ND}{2} \ln(2\pi) - \frac{N}{2} \ln |\boldsymbol{\Sigma}| - \frac{1}{2} \sum_{n=1}^{N} (\mathbf{x}_n - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1} (\mathbf{x}_n - \boldsymbol{\mu}). \tag{2.118}
$$

By simple rearrangement, we see that the likelihood function depends on the data set only through the two quantities

$$
\sum_{n=1}^{N} \mathbf{x}_n, \qquad \sum_{n=1}^{N} \mathbf{x}_n \mathbf{x}_n^{\mathrm{T}}.
$$
 (2.119)

These are known as the *sufficient statistics* for the Gaussian distribution. Using *Appendix C* (C.19), the derivative of the log likelihood with respect to  $\mu$  is given by

 $\overline{N}$ 

$$
\frac{\partial}{\partial \mu} \ln p(\mathbf{X}|\boldsymbol{\mu}, \boldsymbol{\Sigma}) = \sum_{n=1}^{N} \boldsymbol{\Sigma}^{-1}(\mathbf{x}_n - \boldsymbol{\mu})
$$
\n(2.120)

and setting this derivative to zero, we obtain the solution for the maximum likelihood estimate of the mean given by

$$
\mu_{\rm ML} = \frac{1}{N} \sum_{n=1}^{N} \mathbf{x}_n
$$
\n(2.121)

*Appendix C*

which is the mean of the observed set of data points. The maximization of  $(2.118)$ with respect to  $\Sigma$  is rather more involved. The simplest approach is to ignore the *Exercise 2.34* symmetry constraint and show that the resulting solution is symmetric as required. Alternative derivations of this result, which impose the symmetry and positive definiteness constraints explicitly, can be found in Magnus and Neudecker (1999). The result is as expected and takes the form

$$
\Sigma_{\mathrm{ML}} = \frac{1}{N} \sum_{n=1}^{N} (\mathbf{x}_n - \boldsymbol{\mu}_{\mathrm{ML}}) (\mathbf{x}_n - \boldsymbol{\mu}_{\mathrm{ML}})^{\mathrm{T}}
$$
(2.122)

which involves  $\mu_{\text{ML}}$  because this is the result of a joint maximization with respect to  $\mu$  and  $\Sigma$ . Note that the solution (2.121) for  $\mu_{ML}$  does not depend on  $\Sigma_{ML}$ , and so we can first evaluate  $\mu_{ML}$  and then use this to evaluate  $\Sigma_{ML}$ .

If we evaluate the expectations of the maximum likelihood solutions under the *Exercise* 2.35 true distribution, we obtain the following results

$$
\mathbb{E}[\mu_{\mathrm{ML}}] = \mu \tag{2.123}
$$

$$
\mathbb{E}[\Sigma_{\text{ML}}] = \frac{N-1}{N} \Sigma.
$$
 (2.124)

We see that the expectation of the maximum likelihood estimate for the mean is equal to the true mean. However, the maximum likelihood estimate for the covariance has an expectation that is less than the true value, and hence it is biased. We can correct this bias by defining a different estimator  $\Sigma$  given by

$$
\widetilde{\Sigma} = \frac{1}{N-1} \sum_{n=1}^{N} (\mathbf{x}_n - \boldsymbol{\mu}_{\mathrm{ML}}) (\mathbf{x}_n - \boldsymbol{\mu}_{\mathrm{ML}})^{\mathrm{T}}.
$$
\n(2.125)

Clearly from (2.122) and (2.124), the expectation of  $\widetilde{\Sigma}$  is equal to  $\Sigma$ .

## **2.3.5 Sequential estimation**

Our discussion of the maximum likelihood solution for the parameters of a Gaussian distribution provides a convenient opportunity to give a more general discussion of the topic of sequential estimation for maximum likelihood. Sequential methods allow data points to be processed one at a time and then discarded and are important for on-line applications, and also where large data sets are involved so that batch processing of all data points at once is infeasible.

Consider the result (2.121) for the maximum likelihood estimator of the mean  $\mu_{ML}$ , which we will denote by  $\mu_{ML}^{(N)}$  when it is based on N observations. If we

*Exercise 2.35*

**Figure 2.10** A schematic illustration of two correlated random variables  $z$  and  $\theta$ , together with the regression function  $f(\theta)$  given by the conditional expectation  $\mathbb{E}[z|\theta]$ . The Robbins-Monro algorithm provides a general sequential procedure for finding the root  $\theta^*$  of such functions. functions.  $\theta$ 

Image /page/9/Figure/2 description: A graph shows the function f(theta) plotted against theta. The z-axis is the vertical axis, and the theta-axis is the horizontal axis. The function starts from the bottom left, crosses the origin, and goes up to the top right. An arrow points to the origin, labeling it as theta\*. A blue dot is placed at the end of the curve in the upper right quadrant.

dissect out the contribution from the final data point  $\mathbf{x}_N$ , we obtain

$$
\mu_{\text{ML}}^{(N)} = \frac{1}{N} \sum_{n=1}^{N} \mathbf{x}_n
$$

$$
= \frac{1}{N} \mathbf{x}_N + \frac{1}{N} \sum_{n=1}^{N-1} \mathbf{x}_n
$$

$$
= \frac{1}{N} \mathbf{x}_N + \frac{N-1}{N} \mu_{\text{ML}}^{(N-1)}
$$

$$
= \mu_{\text{ML}}^{(N-1)} + \frac{1}{N} (\mathbf{x}_N - \mu_{\text{ML}}^{(N-1)}).
$$
(2.126)

This result has a nice interpretation, as follows. After observing  $N - 1$  data points we have estimated  $\mu$  by  $\mu_{ML}^{(N-1)}$ . We now observe data point  $\mathbf{x}_N$ , and we obtain our revised estimate  $\mu_{ML}^{(N)}$  by moving the old estimate a small amount, proportional to 1/N, in the direction of the 'error signal'  $(\mathbf{x}_N - \boldsymbol{\mu}_{ML}^{(N-1)})$ . Note that, as N increases, so the contribution from successive data points gets smaller so the contribution from successive data points gets smaller.

The result (2.126) will clearly give the same answer as the batch result (2.121) because the two formulae are equivalent. However, we will not always be able to derive a sequential algorithm by this route, and so we seek a more general formulation of sequential learning, which leads us to the *Robbins-Monro* algorithm. Consider a pair of random variables  $\theta$  and z governed by a joint distribution  $p(z, \theta)$ . The conditional expectation of z given  $\theta$  defines a deterministic function  $f(\theta)$  that is given by

$$
f(\theta) \equiv \mathbb{E}[z|\theta] = \int zp(z|\theta) dz
$$
 (2.127)

and is illustrated schematically in Figure 2.10. Functions defined in this way are called *regression functions*.

Our goal is to find the root  $\theta^*$  at which  $f(\theta^*)=0$ . If we had a large data set of observations of z and  $\theta$ , then we could model the regression function directly and then obtain an estimate of its root. Suppose, however, that we observe values of  $z$  one at a time and we wish to find a corresponding sequential estimation scheme for  $\theta^*$ . The following general procedure for solving such problems was given by

Robbins and Monro (1951). We shall assume that the conditional variance of z is finite so that

$$
\mathbb{E}\left[ (z-f)^2 \mid \theta \right] < \infty \tag{2.128}
$$

and we shall also, without loss of generality, consider the case where  $f(\theta) > 0$  for  $\theta > \theta^*$  and  $f(\theta) < 0$  for  $\theta < \theta^*$ , as is the case in Figure 2.10. The Robbins-Monro procedure then defines a sequence of successive estimates of the root  $\theta^*$  given by

$$
\theta^{(N)} = \theta^{(N-1)} + a_{N-1} z(\theta^{(N-1)})
$$
\n(2.129)

where  $z(\theta^{(N)})$  is an observed value of z when  $\theta$  takes the value  $\theta^{(N)}$ . The coefficients  ${a_N}$  represent a sequence of positive numbers that satisfy the conditions

$$
\lim_{N \to \infty} a_N = 0 \tag{2.130}
$$

$$
\sum_{N=1}^{\infty} a_N = \infty \tag{2.131}
$$

$$
\sum_{N=1}^{\infty} a_N^2 < \infty. \tag{2.132}
$$

It can then be shown (Robbins and Monro, 1951; Fukunaga, 1990) that the sequence of estimates given by (2.129) does indeed converge to the root with probability one. Note that the first condition (2.130) ensures that the successive corrections decrease in magnitude so that the process can converge to a limiting value. The second condition (2.131) is required to ensure that the algorithm does not converge short of the root, and the third condition (2.132) is needed to ensure that the accumulated noise has finite variance and hence does not spoil convergence.

Now let us consider how a general maximum likelihood problem can be solved sequentially using the Robbins-Monro algorithm. By definition, the maximum likelihood solution  $\theta_{ML}$  is a stationary point of the log likelihood function and hence satisfies

$$
\frac{\partial}{\partial \theta} \left\{ \frac{1}{N} \sum_{n=1}^{N} \ln p(\mathbf{x}_n | \theta) \right\} \bigg|_{\theta_{\text{ML}}} = 0.
$$
 (2.133)

Exchanging the derivative and the summation, and taking the limit  $N \to \infty$  we have

$$
\lim_{N \to \infty} \frac{1}{N} \sum_{n=1}^{N} \frac{\partial}{\partial \theta} \ln p(x_n|\theta) = \mathbb{E}_x \left[ \frac{\partial}{\partial \theta} \ln p(x|\theta) \right]
$$
(2.134)

and so we see that finding the maximum likelihood solution corresponds to finding the root of a regression function. We can therefore apply the Robbins-Monro procedure, which now takes the form

$$
\theta^{(N)} = \theta^{(N-1)} + a_{N-1} \frac{\partial}{\partial \theta^{(N-1)}} \ln p(x_N | \theta^{(N-1)}).
$$
 (2.135)

Image /page/11/Figure/1 description: Figure 2.11 describes the regression function for a Gaussian distribution. It states that when theta corresponds to the mean mu, the regression function, as illustrated in Figure 2.10 and shown in red, is a straight line. In this scenario, the random variable z is the derivative of the log likelihood function, expressed as (x - mu\_ML)/sigma^2. The expectation that defines the regression function is a straight line given by (mu - mu\_ML)/sigma^2. The root of the regression function is identified as the maximum likelihood estimator mu\_ML.

Image /page/11/Figure/2 description: The image displays a graph with the horizontal axis labeled 'µ' and the vertical axis labeled with a single tick mark. Three vertical green lines are present on the graph. Two blue curves, representing probability density functions labeled 'p(z|µ)', are shown, each centered around one of the outer green lines. A red line, representing a relationship between 'z' and 'µ', slopes upwards from left to right, intersecting the horizontal axis at the center green line. An arrow points to this intersection, labeled 'µML'.

As a specific example, we consider once again the sequential estimation of the mean of a Gaussian distribution, in which case the parameter  $\theta^{(N)}$  is the estimate  $\mu_{\text{ML}}^{(N)}$  of the mean of the Gaussian, and the random variable z is given by

$$
z = \frac{\partial}{\partial \mu_{\rm ML}} \ln p(x | \mu_{\rm ML}, \sigma^2) = \frac{1}{\sigma^2} (x - \mu_{\rm ML}).
$$
 (2.136)

Thus the distribution of z is Gaussian with mean  $\mu - \mu_{ML}$ , as illustrated in Figure 2.11. Substituting  $(2.136)$  into  $(2.135)$ , we obtain the univariate form of  $(2.126)$ , provided we choose the coefficients  $a_N$  to have the form  $a_N = \sigma^2/N$ . Note that although we have focussed on the case of a single variable, the same technique, together with the same restrictions (2.130)–(2.132) on the coefficients  $a_N$ , apply equally to the multivariate case (Blum, 1965).

## **2.3.6 Bayesian inference for the Gaussian**

The maximum likelihood framework gave point estimates for the parameters  $\mu$ and  $\Sigma$ . Now we develop a Bayesian treatment by introducing prior distributions over these parameters. Let us begin with a simple example in which we consider a single Gaussian random variable x. We shall suppose that the variance  $\sigma^2$  is known, and we consider the task of inferring the mean  $\mu$  given a set of N observations  $\mathbf{X} = \{x_1, \ldots, x_N\}$ . The likelihood function, that is the probability of the observed data given  $\mu$ , viewed as a function of  $\mu$ , is given by

$$
p(\mathbf{X}|\mu) = \prod_{n=1}^{N} p(x_n|\mu) = \frac{1}{(2\pi\sigma^2)^{N/2}} \exp\left\{-\frac{1}{2\sigma^2} \sum_{n=1}^{N} (x_n - \mu)^2\right\}.
$$
 (2.137)

Again we emphasize that the likelihood function  $p(\mathbf{X}|\mu)$  is not a probability distribution over  $\mu$  and is not normalized.

We see that the likelihood function takes the form of the exponential of a quadratic form in  $\mu$ . Thus if we choose a prior  $p(\mu)$  given by a Gaussian, it will be a

conjugate distribution for this likelihood function because the corresponding posterior will be a product of two exponentials of quadratic functions of  $\mu$  and hence will also be Gaussian. We therefore take our prior distribution to be

$$
p(\mu) = \mathcal{N}\left(\mu|\mu_0, \sigma_0^2\right) \tag{2.138}
$$

and the posterior distribution is given by

$$
p(\mu|\mathbf{X}) \propto p(\mathbf{X}|\mu)p(\mu).
$$
 (2.139)

*Exercise 2.38* Simple manipulation involving completing the square in the exponent shows that the posterior distribution is given by

$$
p(\mu|\mathbf{X}) = \mathcal{N}\left(\mu|\mu_N, \sigma_N^2\right) \tag{2.140}
$$

where

$$
\mu_N = \frac{\sigma^2}{N\sigma_0^2 + \sigma^2} \mu_0 + \frac{N\sigma_0^2}{N\sigma_0^2 + \sigma^2} \mu_{ML}
$$
\n(2.141)

$$
\frac{1}{\sigma_N^2} = \frac{1}{\sigma_0^2} + \frac{N}{\sigma^2} \tag{2.142}
$$

in which  $\mu_{ML}$  is the maximum likelihood solution for  $\mu$  given by the sample mean

$$
\mu_{\rm ML} = \frac{1}{N} \sum_{n=1}^{N} x_n.
$$
\n(2.143)

It is worth spending a moment studying the form of the posterior mean and variance. First of all, we note that the mean of the posterior distribution given by (2.141) is a compromise between the prior mean  $\mu_0$  and the maximum likelihood solution  $\mu_{ML}$ . If the number of observed data points  $N = 0$ , then (2.141) reduces to the prior mean as expected. For  $N \to \infty$ , the posterior mean is given by the maximum likelihood solution. Similarly, consider the result (2.142) for the variance of the posterior distribution. We see that this is most naturally expressed in terms of the inverse variance, which is called the precision. Furthermore, the precisions are additive, so that the precision of the posterior is given by the precision of the prior plus one contribution of the data precision from each of the observed data points. As we increase the number of observed data points, the precision steadily increases, corresponding to a posterior distribution with steadily decreasing variance. With no observed data points, we have the prior variance, whereas if the number of data points  $N \to \infty$ , the variance  $\sigma_N^2$  goes to zero and the posterior distribution becomes infinitely peaked around the maximum likelihood solution. We therefore see that the maximum likelihood result of a point estimate for  $\mu$  given by (2.143) is recovered precisely from the Bayesian formalism in the limit of an infinite number of observations. Note also that for finite N, if we take the limit  $\sigma_0^2 \to \infty$  in which the prior has infinite variance then the posterior mean (2.141) reduces to the maximum likelihood result, while from (2.142) the posterior variance is given by  $\sigma_N^2 = \sigma^2/N$ .

**Figure 2.12** Illustration of Bayesian inference for the mean  $\mu$  of a Gaussian distribution, in which the variance is assumed to be known. The curves show the prior distribution over  $\mu$ (the curve labelled  $N = 0$ ), which in this case is itself Gaussian, along with the posterior distribution given by (2.140) for increasing numbers  $N$ of data points. The data points are generated from a Gaussian of mean 0.8 and variance 0.1, and the prior is chosen to have mean 0. In both the prior and the likelihood function, the variance is set to the true value.

Image /page/13/Figure/2 description: The image displays a graph with four probability density functions, labeled N=0, N=1, N=2, and N=10. The x-axis ranges from -1 to 1, with tick marks at -1, 0, and 1. The y-axis ranges from 0 to 5. The function labeled N=0 is a broad, bell-shaped curve centered around 0. The function labeled N=1 is a narrower, bell-shaped curve shifted to the right, peaking around 0.2. The function labeled N=2 is even narrower and shifted further to the right, peaking around 0.4. The function labeled N=10 is the narrowest and most sharply peaked, located furthest to the right, with its peak around 0.8. All curves start at 0 on the left side of the graph and approach 0 on the right side.

We illustrate our analysis of Bayesian inference for the mean of a Gaussian distribution in Figure 2.12. The generalization of this result to the case of a Ddimensional Gaussian random variable **x** with known covariance and unknown mean *Exercise 2.40* is straightforward.

*Exercise 2.70*

We have already seen how the maximum likelihood expression for the mean of *Section 2.3.5* a Gaussian can be re-cast as a sequential update formula in which the mean after observing N data points was expressed in terms of the mean after observing  $N - 1$ data points together with the contribution from data point  $\mathbf{x}_N$ . In fact, the Bayesian paradigm leads very naturally to a sequential view of the inference problem. To see this in the context of the inference of the mean of a Gaussian, we write the posterior distribution with the contribution from the final data point  $\mathbf{x}_N$  separated out so that

$$
p(\boldsymbol{\mu}|D) \propto \left[p(\boldsymbol{\mu})\prod_{n=1}^{N-1}p(\mathbf{x}_n|\boldsymbol{\mu})\right]p(\mathbf{x}_N|\boldsymbol{\mu}).
$$
\n(2.144)

The term in square brackets is (up to a normalization coefficient) just the posterior distribution after observing  $N - 1$  data points. We see that this can be viewed as a prior distribution, which is combined using Bayes' theorem with the likelihood function associated with data point  $\mathbf{x}_N$  to arrive at the posterior distribution after observing  $N$  data points. This sequential view of Bayesian inference is very general and applies to any problem in which the observed data are assumed to be independent and identically distributed.

So far, we have assumed that the variance of the Gaussian distribution over the data is known and our goal is to infer the mean. Now let us suppose that the mean is known and we wish to infer the variance. Again, our calculations will be greatly simplified if we choose a conjugate form for the prior distribution. It turns out to be most convenient to work with the precision  $\lambda \equiv 1/\sigma^2$ . The likelihood function for  $\lambda$ takes the form

$$
p(\mathbf{X}|\lambda) = \prod_{n=1}^{N} \mathcal{N}(x_n|\mu, \lambda^{-1}) \propto \lambda^{N/2} \exp\left\{-\frac{\lambda}{2} \sum_{n=1}^{N} (x_n - \mu)^2\right\}.
$$
 (2.145)

Image /page/14/Figure/1 description: The image displays three plots side-by-side, each showing a red curve on a white background with black axes and labels. The x-axis for all plots is labeled "λ" and ranges from 0 to 2. The y-axis for all plots ranges from 0 to 2. The first plot has text indicating "a = 0.1" and "b = 0.1". The curve starts at a high value near λ=0 and rapidly decreases, approaching the x-axis as λ increases. The second plot has text indicating "a = 1" and "b = 1". The curve starts at a value of 1 at λ=0 and decreases more gradually than the first plot, approaching the x-axis. The third plot has text indicating "a = 4" and "b = 6". The curve starts at 0 at λ=0, rises to a peak around λ=0.5, and then decreases, approaching the x-axis as λ increases.

**Figure 2.13** Plot of the gamma distribution  $Gam(\lambda|a, b)$  defined by (2.146) for various values of the parameters  $a$  and  $b$ .

The corresponding conjugate prior should therefore be proportional to the product of a power of  $\lambda$  and the exponential of a linear function of  $\lambda$ . This corresponds to the *gamma* distribution which is defined by

$$
Gam(\lambda|a, b) = \frac{1}{\Gamma(a)} b^a \lambda^{a-1} \exp(-b\lambda).
$$
 (2.146)

Here  $\Gamma(a)$  is the gamma function that is defined by (1.141) and that ensures that *Exercise 2.41* (2.146) is correctly normalized. The gamma distribution has a finite integral if  $a > 0$ , and the distribution itself is finite if  $a \ge 1$ . It is plotted, for various values of a and *Exercise 2.42* b, in Figure 2.13. The mean and variance of the gamma distribution are given by

> $\mathbb{E}[\lambda] = \frac{a}{b}$  $(2.147)$

$$
\text{var}[\lambda] = \frac{a}{b^2}.\tag{2.148}
$$

Consider a prior distribution  $Gam(\lambda|a_0, b_0)$ . If we multiply by the likelihood function (2.145), then we obtain a posterior distribution

$$
p(\lambda|\mathbf{X}) \propto \lambda^{a_0 - 1} \lambda^{N/2} \exp\left\{-b_0 \lambda - \frac{\lambda}{2} \sum_{n=1}^N (x_n - \mu)^2\right\}
$$
 (2.149)

which we recognize as a gamma distribution of the form  $Gam(\lambda|a_N, b_N)$  where

 $\ddot{\phantom{a}}$ 

$$
a_N = a_0 + \frac{N}{2} \tag{2.150}
$$

$$
b_N = b_0 + \frac{1}{2} \sum_{n=1}^{N} (x_n - \mu)^2 = b_0 + \frac{N}{2} \sigma_{ML}^2 \qquad (2.151)
$$

where  $\sigma_{ML}^2$  is the maximum likelihood estimator of the variance. Note that in (2.149) there is no need to keep track of the normalization constants in the prior and the likelihood function because, if required, the correct coefficient can be found at the end using the normalized form (2.146) for the gamma distribution.

From  $(2.150)$ , we see that the effect of observing N data points is to increase the value of the coefficient a by  $N/2$ . Thus we can interpret the parameter  $a_0$  in the prior in terms of  $2a_0$  'effective' prior observations. Similarly, from (2.151) we see that the N data points contribute  $N \sigma_{ML}^2/2$  to the parameter b, where  $\sigma_{ML}^2$  is the variance, and so we can interpret the parameter  $b<sub>0</sub>$  in the prior as arising from the  $2a_0$  'effective' prior observations having variance  $2b_0/(2a_0) = b_0/a_0$ . Recall *Section 2.2* that we made an analogous interpretation for the Dirichlet prior. These distributions are examples of the exponential family, and we shall see that the interpretation of a conjugate prior in terms of effective fictitious data points is a general one for the exponential family of distributions.

> Instead of working with the precision, we can consider the variance itself. The conjugate prior in this case is called the *inverse gamma* distribution, although we shall not discuss this further because we will find it more convenient to work with the precision.

> Now suppose that both the mean and the precision are unknown. To find a conjugate prior, we consider the dependence of the likelihood function on  $\mu$  and  $\lambda$

$$
p(\mathbf{X}|\mu,\lambda) = \prod_{n=1}^{N} \left(\frac{\lambda}{2\pi}\right)^{1/2} \exp\left\{-\frac{\lambda}{2}(x_n - \mu)^2\right\}
$$
$$
\propto \left[\lambda^{1/2} \exp\left(-\frac{\lambda\mu^2}{2}\right)\right]^N \exp\left\{\lambda\mu \sum_{n=1}^{N} x_n - \frac{\lambda}{2} \sum_{n=1}^{N} x_n^2\right\}
$$
 (2.152)

We now wish to identify a prior distribution  $p(\mu, \lambda)$  that has the same functional dependence on  $\mu$  and  $\lambda$  as the likelihood function and that should therefore take the form

$$
p(\mu,\lambda) \propto \left[\lambda^{1/2} \exp\left(-\frac{\lambda \mu^2}{2}\right)\right]^\beta \exp\left\{c\lambda\mu - d\lambda\right\}
$$
  
= 
$$
\exp\left\{-\frac{\beta\lambda}{2}(\mu - c/\beta)^2\right\}\lambda^{\beta/2} \exp\left\{-\left(d - \frac{c^2}{2\beta}\right)\lambda\right\}
$$
 (2.153)

where c, d, and  $\beta$  are constants. Since we can always write  $p(\mu, \lambda) = p(\mu|\lambda)p(\lambda)$ , we can find  $p(\mu|\lambda)$  and  $p(\lambda)$  by inspection. In particular, we see that  $p(\mu|\lambda)$  is a Gaussian whose precision is a linear function of  $\lambda$  and that  $p(\lambda)$  is a gamma distribution, so that the normalized prior takes the form

$$
p(\mu, \lambda) = \mathcal{N}(\mu | \mu_0, (\beta \lambda)^{-1}) \text{Gam}(\lambda | a, b)
$$
 (2.154)

where we have defined new constants given by  $\mu_0 = c/\beta$ ,  $a = 1 + \beta/2$ ,  $b =$ <sup>d</sup>−c<sup>2</sup>/2β. The distribution (2.154) is called the *normal-gamma* or *Gaussian-gamma* distribution and is plotted in Figure 2.14. Note that this is not simply the product of an independent Gaussian prior over  $\mu$  and a gamma prior over  $\lambda$ , because the precision of  $\mu$  is a linear function of  $\lambda$ . Even if we chose a prior in which  $\mu$  and  $\lambda$ were independent, the posterior distribution would exhibit a coupling between the precision of  $\mu$  and the value of  $\lambda$ .

Section 2.2

**Figure 2.14** Contour plot of the normal-gamma distribution (2.154) for parameter values  $\mu_0 = 0$ ,  $\beta = 2$ ,  $a = 5$  and  $b=6.$ 

Image /page/16/Figure/2 description: This is a contour plot with the x-axis labeled as \"μ\" and the y-axis labeled as \"λ\". The x-axis ranges from -2 to 2, with tick marks at -2, 0, and 2. The y-axis ranges from 0 to 2, with tick marks at 0, 1, and 2. The plot displays a series of nested, closed curves that are roughly triangular or almond-shaped, with the innermost curve having a small, somewhat hexagonal opening at its center. The curves are colored red.

In the case of the multivariate Gaussian distribution  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda}^{-1})$  for a D-<br>projonal variable x the conjugate prior distribution for the mean  $\boldsymbol{\mu}$  assuming dimensional variable **x**, the conjugate prior distribution for the mean  $\mu$ , assuming the precision is known, is again a Gaussian. For known mean and unknown precision *Exercise* 2.45 matrix  $\Lambda$ , the conjugate prior is the *Wishart* distribution given by

$$
W(\mathbf{\Lambda}|\mathbf{W},\nu) = B|\mathbf{\Lambda}|^{(\nu-D-1)/2} \exp\left(-\frac{1}{2}\text{Tr}(\mathbf{W}^{-1}\mathbf{\Lambda})\right)
$$
(2.155)

where  $\nu$  is called the number of *degrees of freedom* of the distribution, **W** is a  $D \times D$ scale matrix, and  $Tr(\cdot)$  denotes the trace. The normalization constant B is given by

$$
B(\mathbf{W}, \nu) = |\mathbf{W}|^{-\nu/2} \left( 2^{\nu D/2} \pi^{D(D-1)/4} \prod_{i=1}^{D} \Gamma\left(\frac{\nu+1-i}{2}\right) \right)^{-1}. (2.156)
$$

Again, it is also possible to define a conjugate prior over the covariance matrix itself, rather than over the precision matrix, which leads to the *inverse Wishart* distribution, although we shall not discuss this further. If both the mean and the precision are unknown, then, following a similar line of reasoning to the univariate case, the conjugate prior is given by

$$
p(\boldsymbol{\mu}, \boldsymbol{\Lambda} | \boldsymbol{\mu}_0, \boldsymbol{\beta}, \mathbf{W}, \nu) = \mathcal{N}(\boldsymbol{\mu} | \boldsymbol{\mu}_0, (\boldsymbol{\beta} \boldsymbol{\Lambda})^{-1}) \mathcal{W}(\boldsymbol{\Lambda} | \mathbf{W}, \nu)
$$
(2.157)

which is known as the *normal-Wishart* or *Gaussian-Wishart* distribution.

## **2.3.7 Student's t-distribution**

We have seen that the conjugate prior for the precision of a Gaussian is given *Section 2.3.6* by a gamma distribution. If we have a univariate Gaussian  $\mathcal{N}(x|\mu, \tau^{-1})$  together with a Gamma prior  $Gam(\tau | a, b)$  and we integrate out the precision, we obtain the *Exercise* 2.46 marginal distribution of x in the form

## **2.3. The Gaussian Distribution 103**

**Figure 2.15** Plot of Student's t-distribution (2.159) for  $\mu = 0$  and  $\lambda = 1$  for various values of  $\nu$ . The limit  $\nu \to \infty$  corresponds to a Gaussian distribution with mean 0.4  $\mu$  and precision  $\lambda$ .

Image /page/17/Figure/2 description: This is a line graph showing three curves representing different values of nu. The x-axis ranges from -5 to 5, and the y-axis ranges from 0 to 0.5. The green line, labeled "nu -> infinity", peaks at approximately 0.4 at x=0 and decreases symmetrically on both sides. The blue line, labeled "nu = 1.0", peaks at approximately 0.32 at x=0 and also decreases symmetrically. The red line, labeled "nu = 0.1", peaks at approximately 0.13 at x=0 and decreases more gradually than the other two lines, with a wider base. All three curves are bell-shaped and centered around x=0.

$$
p(x|\mu, a, b) = \int_0^\infty \mathcal{N}(x|\mu, \tau^{-1}) \text{Gam}(\tau|a, b) d\tau \quad (2.158)
$$

$$
= \int_0^\infty \frac{b^a e^{(-b\tau)} \tau^{a-1}}{\Gamma(a)} \left(\frac{\tau}{2\pi}\right)^{1/2} \exp\left\{-\frac{\tau}{2}(x-\mu)^2\right\} d\tau
$$

$$
= \frac{b^a}{\Gamma(a)} \left(\frac{1}{2\pi}\right)^{1/2} \left[b + \frac{(x-\mu)^2}{2}\right]^{-a-1/2} \Gamma(a+1/2)
$$

where we have made the change of variable  $z = \tau [b + (x - \mu)^2/2]$ . By convention we define new parameters given by  $\nu = 2a$  and  $\lambda = a/b$ , in terms of which the distribution  $p(x|\mu, a, b)$  takes the form

St
$$
(x|\mu, \lambda, \nu) = \frac{\Gamma(\nu/2 + 1/2)}{\Gamma(\nu/2)} \left(\frac{\lambda}{\pi \nu}\right)^{1/2} \left[1 + \frac{\lambda(x - \mu)^2}{\nu}\right]^{-\nu/2 - 1/2}
$$
 (2.159)

which is known as *Student's t-distribution*. The parameter  $\lambda$  is sometimes called the *precision* of the t-distribution, even though it is not in general equal to the inverse of the variance. The parameter  $\nu$  is called the *degrees of freedom*, and its effect is illustrated in Figure 2.15. For the particular case of  $\nu = 1$ , the t-distribution reduces to the *Cauchy* distribution, while in the limit  $\nu \to \infty$  the t-distribution  $St(x|\mu, \lambda, \nu)$ *Exercise* 2.47 becomes a Gaussian  $\mathcal{N}(x|\mu, \lambda^{-1})$  with mean  $\mu$  and precision  $\lambda$ .

From (2.158), we see that Student's t-distribution is obtained by adding up an infinite number of Gaussian distributions having the same mean but different precisions. This can be interpreted as an infinite mixture of Gaussians (Gaussian mixtures will be discussed in detail in Section 2.3.9. The result is a distribution that in general has longer 'tails' than a Gaussian, as was seen in Figure 2.15. This gives the tdistribution an important property called *robustness*, which means that it is much less sensitive than the Gaussian to the presence of a few data points which are *outliers*. The robustness of the t-distribution is illustrated in Figure 2.16, which compares the maximum likelihood solutions for a Gaussian and a t-distribution. Note that the maximum likelihood solution for the t-distribution can be found using the expectation-*Exercise 12.24* maximization (EM) algorithm. Here we see that the effect of a small number of

Image /page/18/Figure/1 description: The image displays two histograms, labeled (a) and (b). Both histograms have a y-axis ranging from 0 to 0.5 and an x-axis ranging from -5 to 10. Histogram (a) shows a bell-shaped distribution with bars concentrated around 0, overlaid with a red curve that closely follows the distribution. Histogram (b) also shows a similar bell-shaped distribution around 0, with a red curve. However, histogram (b) also includes a green curve that is wider and flatter than the red curve, and there are additional bars on the far right of the x-axis, around 9, indicating a secondary distribution.

**Figure 2.16** Illustration of the robustness of Student's t-distribution compared to a Gaussian. (a) Histogram distribution of 30 data points drawn from a Gaussian distribution, together with the maximum likelihood fit obtained from a t-distribution (red curve) and a Gaussian (green curve, largely hidden by the red curve). Because the t-distribution contains the Gaussian as a special case it gives almost the same solution as the Gaussian. (b) The same data set but with three additional outlying data points showing how the Gaussian (green curve) is strongly distorted by the outliers, whereas the t-distribution (red curve) is relatively unaffected.

outliers is much less significant for the t-distribution than for the Gaussian. Outliers can arise in practical applications either because the process that generates the data corresponds to a distribution having a heavy tail or simply through mislabelled data. Robustness is also an important property for regression problems. Unsurprisingly, the least squares approach to regression does not exhibit robustness, because it corresponds to maximum likelihood under a (conditional) Gaussian distribution. By basing a regression model on a heavy-tailed distribution such as a t-distribution, we obtain a more robust model.

If we go back to (2.158) and substitute the alternative parameters  $\nu = 2a$ ,  $\lambda =$  $a/b$ , and  $\eta = \tau b/a$ , we see that the t-distribution can be written in the form

$$
St(x|\mu,\lambda,\nu) = \int_0^\infty \mathcal{N}\left(x|\mu,(\eta\lambda)^{-1}\right) Gam(\eta|\nu/2,\nu/2) d\eta.
$$
 (2.160)

We can then generalize this to a multivariate Gaussian  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda})$  to obtain the corresponding multivariate Student's t-distribution in the form

$$
St(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda}, \nu) = \int_0^\infty \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, (\eta \boldsymbol{\Lambda})^{-1}) Gam(\eta | \nu/2, \nu/2) d\eta.
$$
 (2.161)

Using the same technique as for the univariate case, we can evaluate this integral to

*Exercise 2.48* give

## **2.3. The Gaussian Distribution 105**

St(**x**|
$$
\mu
$$
,  $\Lambda$ ,  $\nu$ ) =  $\frac{\Gamma(D/2 + \nu/2)}{\Gamma(\nu/2)} \frac{|\Lambda|^{1/2}}{(\pi \nu)^{D/2}} \left[1 + \frac{\Delta^2}{\nu}\right]^{-D/2 - \nu/2}$  (2.162)

where D is the dimensionality of **x**, and  $\Delta^2$  is the squared Mahalanobis distance defined by

$$
\Delta^2 = (\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \Lambda (\mathbf{x} - \boldsymbol{\mu}). \tag{2.163}
$$

This is the multivariate form of Student's t-distribution and satisfies the following

$$
\mathbb{E}[\mathbf{x}] = \boldsymbol{\mu}, \qquad \text{if} \quad \nu > 1 \tag{2.164}
$$

cov[**x**] = 
$$
\frac{\nu}{(\nu - 2)} \mathbf{\Lambda}^{-1}
$$
, if  $\nu > 2$  (2.165)

$$
mode[x] = \mu \tag{2.166}
$$

with corresponding results for the univariate case.

## **2.3.8 Periodic variables**

Although Gaussian distributions are of great practical significance, both in their own right and as building blocks for more complex probabilistic models, there are situations in which they are inappropriate as density models for continuous variables. One important case, which arises in practical applications, is that of periodic variables.

An example of a periodic variable would be the wind direction at a particular geographical location. We might, for instance, measure values of wind direction on a number of days and wish to summarize this using a parametric distribution. Another example is calendar time, where we may be interested in modelling quantities that are believed to be periodic over 24 hours or over an annual cycle. Such quantities can conveniently be represented using an angular (polar) coordinate  $0 \le \theta < 2\pi$ .

We might be tempted to treat periodic variables by choosing some direction as the origin and then applying a conventional distribution such as the Gaussian. Such an approach, however, would give results that were strongly dependent on the arbitrary choice of origin. Suppose, for instance, that we have two observations at  $\theta_1 = 1^\circ$  and  $\theta_2 = 359^\circ$ , and we model them using a standard univariate Gaussian distribution. If we choose the origin at  $0^\circ$ , then the sample mean of this data set will be  $180°$  with standard deviation  $179°$ , whereas if we choose the origin at  $180°$ , then the mean will be  $0°$  and the standard deviation will be  $1°$ . We clearly need to develop a special approach for the treatment of periodic variables.

Let us consider the problem of evaluating the mean of a set of observations  $\mathcal{D} = \{\theta_1, \dots, \theta_N\}$  of a periodic variable. From now on, we shall assume that  $\theta$  is measured in radians. We have already seen that the simple average  $(\theta_1+\cdots+\theta_N)/N$ will be strongly coordinate dependent. To find an invariant measure of the mean, we note that the observations can be viewed as points on the unit circle and can therefore be described instead by two-dimensional unit vectors  $\mathbf{x}_1, \dots, \mathbf{x}_N$  where  $\|\mathbf{x}_n\| = 1$ for  $n = 1, \ldots, N$ , as illustrated in Figure 2.17. We can average the vectors  $\{x_n\}$ 

*Exercise* 2.49 properties

**Figure 2.17** Illustration of the representation of values  $\theta_n$  of a periodic variable as twodimensional vectors  $x_n$  living on the unit circle. Also shown is the average  $\bar{x}$  of those vectors.

Image /page/20/Figure/2 description: The image displays a 2D Cartesian coordinate system with the x1-axis pointing right and the x2-axis pointing up. A red circle is centered at the origin, representing a unit circle. Four points, labeled x1, x2, x3, and x4, are marked on the circumference of the circle at approximately (1,0), (0,1), (-1,0), and (0,-1) respectively. Inside the circle, a blue point labeled x-bar is shown in the first quadrant. A blue line segment connects the origin to x-bar, labeled r-bar, representing the radial distance. A cyan sector is formed between the positive x1-axis and the line segment to x-bar, with the angle labeled theta-bar.

instead to give

$$
\overline{\mathbf{x}} = \frac{1}{N} \sum_{n=1}^{N} \mathbf{x}_n
$$
\n(2.167)

and then find the corresponding angle  $\bar{\theta}$  of this average. Clearly, this definition will ensure that the location of the mean is independent of the origin of the angular coordinate. Note that  $\bar{x}$  will typically lie inside the unit circle. The Cartesian coordinates of the observations are given by  $x_n = (\cos \theta_n, \sin \theta_n)$ , and we can write the Cartesian coordinates of the sample mean in the form  $\bar{\mathbf{x}} = (\bar{r} \cos \bar{\theta}, \bar{r} \sin \bar{\theta})$ . Substituting into (2.167) and equating the  $x_1$  and  $x_2$  components then gives

$$
\overline{r}\cos\overline{\theta} = \frac{1}{N} \sum_{n=1}^{N} \cos\theta_n, \qquad \qquad \overline{r}\sin\overline{\theta} = \frac{1}{N} \sum_{n=1}^{N} \sin\theta_n.
$$
 (2.168)

Taking the ratio, and using the identity  $\tan \theta = \sin \theta / \cos \theta$ , we can solve for  $\overline{\theta}$  to give

$$
\overline{\theta} = \tan^{-1} \left\{ \frac{\sum_{n} \sin \theta_{n}}{\sum_{n} \cos \theta_{n}} \right\}.
$$
 (2.169)

Shortly, we shall see how this result arises naturally as the maximum likelihood estimator for an appropriately defined distribution over a periodic variable.

We now consider a periodic generalization of the Gaussian called the *von Mises* distribution. Here we shall limit our attention to univariate distributions, although periodic distributions can also be found over hyperspheres of arbitrary dimension. For an extensive discussion of periodic distributions, see Mardia and Jupp (2000).

By convention, we will consider distributions  $p(\theta)$  that have period  $2\pi$ . Any probability density  $p(\theta)$  defined over  $\theta$  must not only be nonnegative and integrate

## **2.3. The Gaussian Distribution 107**

**Figure 2.18** The von Mises distribution can be derived by considering a two-dimensional Gaussian of the form (2.173), whose density contours are shown in blue and conditioning on the unit circle shown in red.

 $\overline{x_1}$  $x<sub>2</sub>$  $p(\mathbf{x})$  $r=1$ 

to one, but it must also be periodic. Thus  $p(\theta)$  must satisfy the three conditions

$$
p(\theta) \geqslant 0 \tag{2.170}
$$

$$
\int_0^{2\pi} p(\theta) \, \mathrm{d}\theta = 1 \tag{2.171}
$$

$$
p(\theta + 2\pi) = p(\theta). \tag{2.172}
$$

From (2.172), it follows that  $p(\theta + M2\pi) = p(\theta)$  for any integer M.

 $2n -$ 

We can easily obtain a Gaussian-like distribution that satisfies these three properties as follows. Consider a Gaussian distribution over two variables  $\mathbf{x} = (x_1, x_2)$ having mean  $\mu = (\mu_1, \mu_2)$  and a covariance matrix  $\Sigma = \sigma^2 \mathbf{I}$  where **I** is the  $2 \times 2$ identity matrix, so that

$$
p(x_1, x_2) = \frac{1}{2\pi\sigma^2} \exp\left\{-\frac{(x_1 - \mu_1)^2 + (x_2 - \mu_2)^2}{2\sigma^2}\right\}.
$$
 (2.173)

The contours of constant  $p(x)$  are circles, as illustrated in Figure 2.18. Now suppose we consider the value of this distribution along a circle of fixed radius. Then by construction this distribution will be periodic, although it will not be normalized. We can determine the form of this distribution by transforming from Cartesian coordinates  $(x_1, x_2)$  to polar coordinates  $(r, \theta)$  so that

$$
x_1 = r \cos \theta, \qquad x_2 = r \sin \theta. \tag{2.174}
$$

We also map the mean  $\mu$  into polar coordinates by writing

$$
\mu_1 = r_0 \cos \theta_0, \qquad \mu_2 = r_0 \sin \theta_0. \tag{2.175}
$$

Next we substitute these transformations into the two-dimensional Gaussian distribution (2.173), and then condition on the unit circle  $r = 1$ , noting that we are interested only in the dependence on  $\theta$ . Focussing on the exponent in the Gaussian distribution we have

$$
-\frac{1}{2\sigma^2}\left\{(r\cos\theta - r_0\cos\theta_0)^2 + (r\sin\theta - r_0\sin\theta_0)^2\right\}
$$
  
= 
$$
-\frac{1}{2\sigma^2}\left\{1 + r_0^2 - 2r_0\cos\theta\cos\theta_0 - 2r_0\sin\theta\sin\theta_0\right\}
$$
  
= 
$$
\frac{r_0}{\sigma^2}\cos(\theta - \theta_0) + \text{const}
$$
 (2.176)

Image /page/22/Figure/1 description: The image displays two plots side-by-side. The left plot is a 2D line graph with a red line and a blue line. The red line represents a distribution with parameters m=5 and θ0=π/4, peaking sharply. The blue line represents a distribution with parameters m=1 and θ0=3π/4, with a broader, lower peak. The right plot is a polar coordinate graph showing two directional distributions. A red, elongated oval shape is centered around the angle π/4, indicating a distribution with m=5 and θ0=π/4. A blue circle is centered around the angle 3π/4, indicating a distribution with m=1 and θ0=3π/4. The polar plot includes radial lines labeled 0, π/4, 3π/4, and 2π, representing angles.

**Figure 2.19** The von Mises distribution plotted for two different parameter values, shown as a Cartesian plot on the left and as the corresponding polar plot on the right.

*Exercise 2.51*

where 'const' denotes terms independent of  $\theta$ , and we have made use of the following *Exercise* 2.51 trigonometrical identities

$$
\cos^2 A + \sin^2 A = 1 \tag{2.177}
$$

$$
\cos A \cos B + \sin A \sin B = \cos(A - B). \tag{2.178}
$$

If we now define  $m = r_0/\sigma^2$ , we obtain our final expression for the distribution of  $p(\theta)$  along the unit circle  $r = 1$  in the form

$$
p(\theta|\theta_0, m) = \frac{1}{2\pi I_0(m)} \exp\{m \cos(\theta - \theta_0)\}
$$
 (2.179)

which is called the *von Mises* distribution, or the *circular normal*. Here the parameter  $\theta_0$  corresponds to the mean of the distribution, while m, which is known as the *concentration* parameter, is analogous to the inverse variance (precision) for the Gaussian. The normalization coefficient in (2.179) is expressed in terms of  $I_0(m)$ , which is the zeroth-order Bessel function of the first kind (Abramowitz and Stegun, 1965) and is defined by

$$
I_0(m) = \frac{1}{2\pi} \int_0^{2\pi} \exp\{m\cos\theta\} d\theta.
$$
 (2.180)

*Exercise 2.52* For large m, the distribution becomes approximately Gaussian. The von Mises distribution is plotted in Figure 2.19, and the function  $I_0(m)$  is plotted in Figure 2.20.

> Now consider the maximum likelihood estimators for the parameters  $\theta_0$  and m for the von Mises distribution. The log likelihood function is given by

$$
\ln p(\mathcal{D}|\theta_0, m) = -N \ln(2\pi) - N \ln I_0(m) + m \sum_{n=1}^{N} \cos(\theta_n - \theta_0).
$$
 (2.181)

Image /page/23/Figure/1 description: The image contains two plots. The left plot shows a function I0(m) on the y-axis and m on the x-axis. The x-axis ranges from 0 to 10, with tick marks at 0, 5, and 10. The y-axis ranges from 0 to 3000, with tick marks at 0, 1000, 2000, and 3000. The plot shows a curve that starts at approximately (0,0) and increases exponentially, reaching approximately (10, 3000). The right plot shows a function A(m) on the y-axis and m on the x-axis. The x-axis ranges from 0 to 10, with tick marks at 0, 5, and 10. The y-axis ranges from 0 to 1, with tick marks at 0, 0.5, and 1. The plot shows a curve that starts at (0,0) and increases rapidly at first, then levels off, approaching 1 as m increases. The curve reaches approximately (10, 0.95).

**Figure 2.20** Plot of the Bessel function  $I_0(m)$  defined by (2.180), together with the function  $A(m)$  defined by (2.186).

Setting the derivative with respect to  $\theta_0$  equal to zero gives

$$
\sum_{n=1}^{N} \sin(\theta_n - \theta_0) = 0.
$$
 (2.182)

To solve for  $\theta_0$ , we make use of the trigonometric identity

$$
\sin(A - B) = \cos B \sin A - \cos A \sin B \tag{2.183}
$$

*Exercise* 2.53 from which we obtain

$$
\theta_0^{\text{ML}} = \tan^{-1} \left\{ \frac{\sum_n \sin \theta_n}{\sum_n \cos \theta_n} \right\}
$$
 (2.184)

which we recognize as the result  $(2.169)$  obtained earlier for the mean of the observations viewed in a two-dimensional Cartesian space.

Similarly, maximizing (2.181) with respect to m, and making use of  $I'_0(m) =$  $I_1(m)$  (Abramowitz and Stegun, 1965), we have

$$
A(m) = \frac{1}{N} \sum_{n=1}^{N} \cos(\theta_n - \theta_0^{\text{ML}})
$$
 (2.185)

where we have substituted for the maximum likelihood solution for  $\theta_0^{\text{ML}}$  (recalling that we are performing a joint optimization over  $\theta$  and m), and we have defined

$$
A(m) = \frac{I_1(m)}{I_0(m)}.\t(2.186)
$$

The function  $A(m)$  is plotted in Figure 2.20. Making use of the trigonometric identity  $(2.178)$ , we can write  $(2.185)$  in the form

$$
A(m_{\rm ML}) = \left(\frac{1}{N} \sum_{n=1}^{N} \cos \theta_n\right) \cos \theta_0^{\rm ML} - \left(\frac{1}{N} \sum_{n=1}^{N} \sin \theta_n\right) \sin \theta_0^{\rm ML}.\tag{2.187}
$$

**Figure 2.21** Plots of the 'old faithful' data in which the blue curves show contours of constant probability density. On the left is a single Gaussian distribution which has been fitted to the data using maximum likelihood. Note that this distribution fails to capture the two clumps in the data and indeed places much of its probability mass in the central region between the clumps where the data are relatively sparse. On the right the distribution is given by a linear combination of two Gaussians which has been fitted to the data by maximum likelihood using techniques discussed Chapter 9, and which gives a better representation of the data.

Image /page/24/Figure/2 description: The image displays two scatter plots side-by-side, each with overlaid contour lines representing probability distributions. Both plots have x-axes labeled from 1 to 6 and y-axes labeled from 40 to 100. The left plot shows a single cluster of green data points that are generally increasing from left to right, with three concentric blue ellipses indicating a probability distribution that is elongated along a positive slope. The right plot shows two distinct clusters of green data points. The upper cluster is similar in shape and orientation to the cluster in the left plot. The lower cluster is more circular and located to the left and below the upper cluster. This plot also has three concentric blue ellipses for each cluster, indicating two separate probability distributions.

The right-hand side of (2.187) is easily evaluated, and the function  $A(m)$  can be inverted numerically.

For completeness, we mention briefly some alternative techniques for the construction of periodic distributions. The simplest approach is to use a histogram of observations in which the angular coordinate is divided into fixed bins. This has the virtue of simplicity and flexibility but also suffers from significant limitations, as we shall see when we discuss histogram methods in more detail in Section 2.5. Another approach starts, like the von Mises distribution, from a Gaussian distribution over a Euclidean space but now marginalizes onto the unit circle rather than conditioning (Mardia and Jupp, 2000). However, this leads to more complex forms of distribution and will not be discussed further. Finally, any valid distribution over the real axis (such as a Gaussian) can be turned into a periodic distribution by mapping successive intervals of width  $2\pi$  onto the periodic variable  $(0, 2\pi)$ , which corresponds to 'wrapping' the real axis around unit circle. Again, the resulting distribution is more complex to handle than the von Mises distribution.

One limitation of the von Mises distribution is that it is unimodal. By forming *mixtures* of von Mises distributions, we obtain a flexible framework for modelling periodic variables that can handle multimodality. For an example of a machine learning application that makes use of von Mises distributions, see Lawrence *et al.* (2002), and for extensions to modelling conditional densities for regression problems, see Bishop and Nabney (1996).

## **2.3.9 Mixtures of Gaussians**

While the Gaussian distribution has some important analytical properties, it suffers from significant limitations when it comes to modelling real data sets. Consider the example shown in Figure 2.21. This is known as the 'Old Faithful' data set, and comprises 272 measurements of the eruption of the Old Faithful geyser at Yel-*Appendix A* lowstone National Park in the USA. Each measurement comprises the duration of

**Figure 2.22** Example of a Gaussian mixture distribution  $p(x)$ in one dimension showing three Gaussians (each scaled by a coefficient) in blue and their sum in red.

Image /page/25/Figure/2 description: The image displays a graph with an x-axis labeled 'x' and a y-axis labeled 'f(x)'. The graph shows three blue curves, each resembling a bell curve, and a red curve that is the sum of these three blue curves. The blue curves are positioned at different points along the x-axis, with one peaking at a lower value, another at a medium value, and the third at a higher value. The red curve is a composite curve that follows the general shape of the blue curves, peaking higher than any individual blue curve and exhibiting a more complex, multi-modal shape.

the eruption in minutes (horizontal axis) and the time in minutes to the next eruption (vertical axis). We see that the data set forms two dominant clumps, and that a simple Gaussian distribution is unable to capture this structure, whereas a linear superposition of two Gaussians gives a better characterization of the data set.

Such superpositions, formed by taking linear combinations of more basic distributions such as Gaussians, can be formulated as probabilistic models known as *mixture distributions* (McLachlan and Basford, 1988; McLachlan and Peel, 2000). In Figure 2.22 we see that a linear combination of Gaussians can give rise to very complex densities. By using a sufficient number of Gaussians, and by adjusting their means and covariances as well as the coefficients in the linear combination, almost any continuous density can be approximated to arbitrary accuracy.

We therefore consider a superposition of  $K$  Gaussian densities of the form

$$
p(\mathbf{x}) = \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x} | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)
$$
 (2.188)

which is called a *mixture of Gaussians*. Each Gaussian density  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)$  is called a *component* of the mixture and has its own mean  $\mu_k$  and covariance  $\Sigma_k$ . Contour and surface plots for a Gaussian mixture having 3 components are shown in Figure 2.23.

In this section we shall consider Gaussian components to illustrate the framework of mixture models. More generally, mixture models can comprise linear combinations of other distributions. For instance, in Section 9.3.3 we shall consider mixtures of Bernoulli distributions as an example of a mixture model for discrete

The parameters  $\pi_k$  in (2.188) are called *mixing coefficients*. If we integrate both sides of (2.188) with respect to **x**, and note that both  $p(x)$  and the individual Gaussian components are normalized, we obtain

$$
\sum_{k=1}^{K} \pi_k = 1.
$$
\n(2.189)

Also, the requirement that  $p(\mathbf{x}) \ge 0$ , together with  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \ge 0$ , implies  $\pi_k > 0$  for all k. Combining this with the condition (2.189) we obtain  $\pi_k \geq 0$  for all k. Combining this with the condition (2.189) we obtain

$$
0 \leqslant \pi_k \leqslant 1. \tag{2.190}
$$

*Section 9.3.3* variables.

Image /page/26/Figure/1 description: The image displays three plots. Plot (a) shows three sets of concentric ellipses in red, green, and blue, representing probability density functions. The red ellipses are centered around (0.25, 0.25), the green ellipses are centered around (0.4, 0.4), and the blue ellipses are centered around (0.6, 0.6). Numerical labels 0.5, 0.3, and 0.2 are associated with the red, green, and blue ellipses, respectively. The x and y axes of plot (a) range from 0 to 1. Plot (b) shows a set of magenta contour lines forming a complex, multi-peaked shape, also with axes ranging from 0 to 1. Plot (c) is a 3D surface plot, rendered in shades of brown, depicting a landscape with multiple peaks and valleys, corresponding to the probability distribution shown in plot (b).

**Figure 2.23** Illustration of a mixture of 3 Gaussians in a two-dimensional space. (a) Contours of constant density for each of the mixture components, in which the 3 components are denoted red, blue and green, and the values of the mixing coefficients are shown below each component. (b) Contours of the marginal probability density  $p(x)$  of the mixture distribution. (c) A surface plot of the distribution  $p(x)$ .

We therefore see that the mixing coefficients satisfy the requirements to be probabilities.

From the sum and product rules, the marginal density is given by

$$
p(\mathbf{x}) = \sum_{k=1}^{K} p(k)p(\mathbf{x}|k)
$$
\n(2.191)

which is equivalent to (2.188) in which we can view  $\pi_k = p(k)$  as the prior probability of picking the  $k^{\text{th}}$  component, and the density  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) = p(\mathbf{x}|k)$  as the probability of  $x$  conditioned on  $k$ . As we shall see in later chapters, an important role is played by the posterior probabilities  $p(k|\mathbf{x})$ , which are also known as *responsibilities*. From Bayes' theorem these are given by

$$
\gamma_k(\mathbf{x}) = p(k|\mathbf{x})
$$

$$
= \frac{p(k)p(\mathbf{x}|k)}{\sum_l p(l)p(\mathbf{x}|l)}
$$

$$
= \frac{\pi_k \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_l \pi_l \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_l, \boldsymbol{\Sigma}_l)}.
$$
 (2.192)

We shall discuss the probabilistic interpretation of the mixture distribution in greater detail in Chapter 9.

The form of the Gaussian mixture distribution is governed by the parameters  $\pi$ ,  $\mu$  and  $\Sigma$ , where we have used the notation  $\pi \equiv {\pi_1, \ldots, \pi_K}, \mu \equiv {\mu_1, \ldots, \mu_K}$ and  $\Sigma = {\Sigma_1, \dots, \Sigma_K}$ . One way to set the values of these parameters is to use maximum likelihood. From (2.188) the log of the likelihood function is given by

$$
\ln p(\mathbf{X}|\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Sigma}) = \sum_{n=1}^{N} \ln \left\{ \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right\}
$$
(2.193)

where  $X = \{x_1, \ldots, x_N\}$ . We immediately see that the situation is now much more complex than with a single Gaussian, due to the presence of the summation over  $k$  inside the logarithm. As a result, the maximum likelihood solution for the parameters no longer has a closed-form analytical solution. One approach to maximizing the likelihood function is to use iterative numerical optimization techniques (Fletcher, 1987; Nocedal and Wright, 1999; Bishop and Nabney, 2008). Alternatively we can employ a powerful framework called *expectation maximization*, which will be discussed at length in Chapter 9.

## **2.4. The Exponential Family**

The probability distributions that we have studied so far in this chapter (with the exception of the Gaussian mixture) are specific examples of a broad class of distributions called the *exponential family* (Duda and Hart, 1973; Bernardo and Smith, 1994). Members of the exponential family have many important properties in common, and it is illuminating to discuss these properties in some generality.

The exponential family of distributions over **x**, given parameters *<sup>η</sup>*, is defined to be the set of distributions of the form

$$
p(\mathbf{x}|\boldsymbol{\eta}) = h(\mathbf{x})g(\boldsymbol{\eta}) \exp\left\{\boldsymbol{\eta}^{\mathrm{T}} \mathbf{u}(\mathbf{x})\right\}
$$
 (2.194)

where **x** may be scalar or vector, and may be discrete or continuous. Here *<sup>η</sup>* are called the *natural parameters* of the distribution, and  $u(x)$  is some function of **x**. The function  $q(\eta)$  can be interpreted as the coefficient that ensures that the distribution is normalized and therefore satisfies

$$
g(\eta) \int h(\mathbf{x}) \exp \{ \eta^{\mathrm{T}} \mathbf{u}(\mathbf{x}) \} d\mathbf{x} = 1
$$
 (2.195)

where the integration is replaced by summation if **x** is a discrete variable.

We begin by taking some examples of the distributions introduced earlier in the chapter and showing that they are indeed members of the exponential family. Consider first the Bernoulli distribution

$$
p(x|\mu) = \text{Bern}(x|\mu) = \mu^x (1 - \mu)^{1 - x}.
$$
 (2.196)

Expressing the right-hand side as the exponential of the logarithm, we have

$$
p(x|\mu) = \exp\{x \ln \mu + (1 - x) \ln(1 - \mu)\}
$$
  
$$
= (1 - \mu) \exp\{\ln\left(\frac{\mu}{1 - \mu}\right) x\}. (2.197)
$$

Comparison with (2.194) allows us to identify

$$
\eta = \ln\left(\frac{\mu}{1-\mu}\right) \tag{2.198}
$$

which we can solve for  $\mu$  to give  $\mu = \sigma(\eta)$ , where

$$
\sigma(\eta) = \frac{1}{1 + \exp(-\eta)}
$$
\n(2.199)

is called the *logistic sigmoid* function. Thus we can write the Bernoulli distribution using the standard representation (2.194) in the form

$$
p(x|\eta) = \sigma(-\eta) \exp(\eta x)
$$
 (2.200)

where we have used  $1 - \sigma(\eta) = \sigma(-\eta)$ , which is easily proved from (2.199). Comparison with (2.194) shows that

$$
u(x) = x \tag{2.201}
$$

$$
h(x) = 1 \tag{2.202}
$$

$$
g(\eta) = \sigma(-\eta). \tag{2.203}
$$

Next consider the multinomial distribution that, for a single observation **x**, takes the form

$$
p(\mathbf{x}|\boldsymbol{\mu}) = \prod_{k=1}^{M} \mu_k^{x_k} = \exp\left\{\sum_{k=1}^{M} x_k \ln \mu_k\right\}
$$
 (2.204)

where  $\mathbf{x} = (x_1, \dots, x_N)^\text{T}$ . Again, we can write this in the standard representation (2.194) so that

$$
p(\mathbf{x}|\boldsymbol{\eta}) = \exp(\boldsymbol{\eta}^{\mathrm{T}}\mathbf{x})
$$
\n(2.205)

where  $\eta_k = \ln \mu_k$ , and we have defined  $\boldsymbol{\eta} = (\eta_1, \dots, \eta_M)^T$ . Again, comparing with (2.194) we have

$$
\mathbf{u}(\mathbf{x}) = \mathbf{x} \tag{2.206}
$$

$$
h(\mathbf{x}) = 1 \tag{2.207}
$$

$$
g(\eta) = 1. \tag{2.208}
$$

Note that the parameters  $\eta_k$  are not independent because the parameters  $\mu_k$  are subject to the constraint

$$
\sum_{k=1}^{M} \mu_k = 1
$$
\n(2.209)

so that, given any  $M - 1$  of the parameters  $\mu_k$ , the value of the remaining parameter is fixed. In some circumstances, it will be convenient to remove this constraint by expressing the distribution in terms of only  $M - 1$  parameters. This can be achieved by using the relationship (2.209) to eliminate  $\mu_M$  by expressing it in terms of the remaining  $\{\mu_k\}$  where  $k = 1, \ldots, M - 1$ , thereby leaving  $M - 1$  parameters. Note that these remaining parameters are still subject to the constraints

$$
0 \le \mu_k \le 1,
$$
  $\sum_{k=1}^{M-1} \mu_k \le 1.$  (2.210)

Making use of the constraint (2.209), the multinomial distribution in this representation then becomes

$$
\exp\left\{\sum_{k=1}^{M} x_k \ln \mu_k\right\}
$$

$$
= \exp\left\{\sum_{k=1}^{M-1} x_k \ln \mu_k + \left(1 - \sum_{k=1}^{M-1} x_k\right) \ln \left(1 - \sum_{k=1}^{M-1} \mu_k\right)\right\}
$$

$$
= \exp\left\{\sum_{k=1}^{M-1} x_k \ln \left(\frac{\mu_k}{1 - \sum_{j=1}^{M-1} \mu_j}\right) + \ln \left(1 - \sum_{k=1}^{M-1} \mu_k\right)\right\}. (2.211)
$$

We now identify

$$
\ln\left(\frac{\mu_k}{1-\sum_j \mu_j}\right) = \eta_k \tag{2.212}
$$

which we can solve for  $\mu_k$  by first summing both sides over k and then rearranging and back-substituting to give

$$
\mu_k = \frac{\exp(\eta_k)}{1 + \sum_j \exp(\eta_j)}.
$$
\n(2.213)

This is called the *softmax* function, or the *normalized exponential*. In this representation, the multinomial distribution therefore takes the form

$$
p(\mathbf{x}|\boldsymbol{\eta}) = \left(1 + \sum_{k=1}^{M-1} \exp(\eta_k)\right)^{-1} \exp(\boldsymbol{\eta}^{\mathrm{T}} \mathbf{x})\right.
$$
 (2.214)

This is the standard form of the exponential family, with parameter vector  $\eta$  =  $(\eta_1,\ldots,\eta_{M-1})^T$  in which

$$
\mathbf{u}(\mathbf{x}) = \mathbf{x} \tag{2.215}
$$

$$
h(\mathbf{x}) = 1 \tag{2.216}
$$
\n
$$
\left(\begin{array}{cc} M-1 & \mathbf{1} \end{array}\right)^{-1}
$$

$$
g(\eta) = \left(1 + \sum_{k=1}^{M-1} \exp(\eta_k)\right)^{-1}.
$$
 (2.217)

Finally, let us consider the Gaussian distribution. For the univariate Gaussian, we have

$$
p(x|\mu, \sigma^2) = \frac{1}{(2\pi\sigma^2)^{1/2}} \exp\left\{-\frac{1}{2\sigma^2}(x-\mu)^2\right\}
$$
 (2.218)

$$
= \frac{1}{(2\pi\sigma^2)^{1/2}} \exp\left\{-\frac{1}{2\sigma^2}x^2 + \frac{\mu}{\sigma^2}x - \frac{1}{2\sigma^2}\mu^2\right\} \quad (2.219)
$$

which, after some simple rearrangement, can be cast in the standard exponential *Exercise 2.57* family form (2.194) with

$$
\eta = \begin{pmatrix} \mu/\sigma^2 \\ -1/2\sigma^2 \end{pmatrix} \tag{2.220}
$$

$$
\mathbf{u}(x) = \begin{pmatrix} x \\ x^2 \end{pmatrix} \tag{2.221}
$$

$$
h(\mathbf{x}) = (2\pi)^{-1/2} \tag{2.222}
$$

$$
g(\eta) = (-2\eta_2)^{1/2} \exp\left(\frac{\eta_1^2}{4\eta_2}\right).
$$
 (2.223)

## **2.4.1 Maximum likelihood and sufficient statistics**

Let us now consider the problem of estimating the parameter vector  $\eta$  in the general exponential family distribution (2.194) using the technique of maximum likelihood. Taking the gradient of both sides of  $(2.195)$  with respect to  $\eta$ , we have

$$
\nabla g(\eta) \int h(\mathbf{x}) \exp \{ \boldsymbol{\eta}^{\mathrm{T}} \mathbf{u}(\mathbf{x}) \} d\mathbf{x} + g(\eta) \int h(\mathbf{x}) \exp \{ \boldsymbol{\eta}^{\mathrm{T}} \mathbf{u}(\mathbf{x}) \} \mathbf{u}(\mathbf{x}) d\mathbf{x} = 0.
$$
 (2.224)

Rearranging, and making use again of (2.195) then gives

$$
-\frac{1}{g(\eta)}\nabla g(\eta) = g(\eta) \int h(\mathbf{x}) \exp \{ \eta^{\mathrm{T}} \mathbf{u}(\mathbf{x}) \} \mathbf{u}(\mathbf{x}) d\mathbf{x} = \mathbb{E}[\mathbf{u}(\mathbf{x})] \qquad (2.225)
$$

where we have used (2.194). We therefore obtain the result

$$
-\nabla \ln g(\boldsymbol{\eta}) = \mathbb{E}[\mathbf{u}(\mathbf{x})]. \tag{2.226}
$$

Note that the covariance of  $\mathbf{u}(\mathbf{x})$  can be expressed in terms of the second derivatives *Exercise 2.58* of  $g(\eta)$ , and similarly for higher order moments. Thus, provided we can normalize a distribution from the exponential family, we can always find its moments by simple differentiation.

Now consider a set of independent identically distributed data denoted by  $X =$  $\{x_1, \ldots, x_n\}$ , for which the likelihood function is given by

$$
p(\mathbf{X}|\boldsymbol{\eta}) = \left(\prod_{n=1}^{N} h(\mathbf{x}_n)\right) g(\boldsymbol{\eta})^{N} \exp\left\{\boldsymbol{\eta}^{\mathrm{T}} \sum_{n=1}^{N} \mathbf{u}(\mathbf{x}_n)\right\}.
$$
 (2.227)

Setting the gradient of  $\ln p(\mathbf{X}|\boldsymbol{\eta})$  with respect to  $\boldsymbol{\eta}$  to zero, we get the following condition to be satisfied by the maximum likelihood estimator  $\eta_{ML}$ 

$$
-\nabla \ln g(\boldsymbol{\eta}_{\mathrm{ML}}) = \frac{1}{N} \sum_{n=1}^{N} \mathbf{u}(\mathbf{x}_n)
$$
 (2.228)

which can in principle be solved to obtain  $\eta_{ML}$ . We see that the solution for the maximum likelihood estimator depends on the data only through  $\sum_n u(x_n)$ , which is therefore called the *sufficient statistic* of the distribution (2.194). We do not need is therefore called the *sufficient statistic* of the distribution (2.194). We do not need to store the entire data set itself but only the value of the sufficient statistic. For the Bernoulli distribution, for example, the function  $\mathbf{u}(x)$  is given just by x and so we need only keep the sum of the data points  $\{x_n\}$ , whereas for the Gaussian  $u(x) = (x, x^2)^T$ , and so we should keep both the sum of  $\{x_n\}$  and the sum of  $\{x_n^2\}$ .<br>If we consider the limit  $N \to \infty$ , then the right-hand side of (2.228) becomes

If we consider the limit  $N \to \infty$ , then the right-hand side of (2.228) becomes  $\mathbb{E}[\mathbf{u}(\mathbf{x})]$ , and so by comparing with (2.226) we see that in this limit  $\eta_{ML}$  will equal the true value *η*.

In fact, this sufficiency property holds also for Bayesian inference, although we shall defer discussion of this until Chapter 8 when we have equipped ourselves with the tools of graphical models and can thereby gain a deeper insight into these important concepts.

## **2.4.2 Conjugate priors**

We have already encountered the concept of a conjugate prior several times, for example in the context of the Bernoulli distribution (for which the conjugate prior is the beta distribution) or the Gaussian (where the conjugate prior for the mean is a Gaussian, and the conjugate prior for the precision is the Wishart distribution). In general, for a given probability distribution  $p(x|\eta)$ , we can seek a prior  $p(\eta)$  that is conjugate to the likelihood function, so that the posterior distribution has the same functional form as the prior. For any member of the exponential family (2.194), there exists a conjugate prior that can be written in the form

$$
p(\boldsymbol{\eta}|\boldsymbol{\chi},\nu) = f(\boldsymbol{\chi},\nu)g(\boldsymbol{\eta})^{\nu}\exp\left\{\nu\boldsymbol{\eta}^{\mathrm{T}}\boldsymbol{\chi}\right\}
$$
 (2.229)

where  $f(\chi, \nu)$  is a normalization coefficient, and  $g(\eta)$  is the same function as appears in (2.194). To see that this is indeed conjugate, let us multiply the prior (2.229) by the likelihood function (2.227) to obtain the posterior distribution, up to a normalization coefficient, in the form

$$
p(\boldsymbol{\eta}|\mathbf{X},\boldsymbol{\chi},\nu) \propto g(\boldsymbol{\eta})^{\nu+N} \exp\left\{\boldsymbol{\eta}^{\mathrm{T}}\left(\sum_{n=1}^{N}\mathbf{u}(\mathbf{x}_{n})+\nu\boldsymbol{\chi}\right)\right\}.
$$
 (2.230)

This again takes the same functional form as the prior (2.229), confirming conjugacy. Furthermore, we see that the parameter  $\nu$  can be interpreted as a effective number of pseudo-observations in the prior, each of which has a value for the sufficient statistic  $\mathbf{u}(\mathbf{x})$  given by  $\mathbf{\chi}$ .

## **2.4.3 Noninformative priors**

In some applications of probabilistic inference, we may have prior knowledge that can be conveniently expressed through the prior distribution. For example, if the prior assigns zero probability to some value of variable, then the posterior distribution will necessarily also assign zero probability to that value, irrespective of