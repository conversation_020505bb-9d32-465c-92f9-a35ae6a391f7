Image /page/0/Picture/0 description: A square button with rounded corners, colored in a light gray gradient. At the top of the button, there is a circular icon with a ribbon or bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a darker gray color.

# UrFound: Towards Universal Retinal Foundation Models via Knowledge-Guided Masked Modeling

Kai <PERSON><sup>1</sup>, <PERSON><sup>1( $\boxtimes$ )</sup>, <PERSON><sup>1</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON>  $Goh<sup>1</sup>$ , <PERSON><PERSON><PERSON><sup>2,3</sup>, and <PERSON><sup>1</sup>

<sup>1</sup> Institute of High Performance Computing (IHPC), Agency for Science, Technology and Research (A\*STAR), 1 Fusionopolis Way, #16–16 Connexis, Singapore 138632,

Singapore

zhou\<EMAIL>

<sup>2</sup> Singapore Eye Research Institute, Singapore National Eye Centre, Singapore, Singapore

<sup>3</sup> Centre for Innovation and Precision Eye Health, National University of Singapore, Singapore, Singapore

Abstract. Retinal foundation models aim to learn generalizable representations from diverse retinal images, facilitating label-efficient model adaptation across various ophthalmic tasks. Despite their success, current retinal foundation models are generally restricted to a single imaging modality, such as Color Fundus Photography (CFP) or Optical Coherence Tomography (OCT), limiting their versatility. Moreover, these models may struggle to fully leverage expert annotations and overlook the valuable domain knowledge essential for domain-specific representation learning. To overcome these limitations, we introduce UrFound, a retinal foundation model designed to learn universal representations from both multimodal retinal images and domain knowledge. UrFound is equipped with a modality-agnostic image encoder and accepts either CFP or OCT images as inputs. To integrate domain knowledge into representation learning, we encode expert annotation in text supervision and propose a knowledge-guided masked modeling strategy for model pre-training. It involves reconstructing randomly masked patches of retinal images while predicting masked text tokens conditioned on the corresponding image. This approach aligns multimodal images and textual expert annotations within a unified latent space, facilitating generalizable and domainspecific representation learning. Experimental results demonstrate that UrFound exhibits strong generalization ability and data efficiency when adapting to various tasks in retinal image analysis. By training on ∼180k retinal images, UrFound significantly outperforms the state-of-the-art retinal foundation model trained on up to 1.6 million unlabelled images across 8 public retinal datasets. Our code and data are available at [https://github.com/yukkai/UrFound.](https://github.com/yukkai/UrFound)

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_70.](https://doi.org/10.1007/978-3-031-72390-2_70)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 753–762, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_70)\_70

Keywords: Domain expert knowledge · Masked modeling · Retinal image understanding · Multimodal foundation model

# 1 Introduction

Foundation models (FMs) are large, powerful artificial intelligence (AI) models pre-trained on vast amounts of unlabeled data. By learning fundamental patterns and relationships within diverse data, FMs gain the ability to adapt to diverse downstream tasks with minimal additional training [\[20](#page-9-0)]. Notable examples of FMs, such as CLIP [\[14](#page-9-1)], SAM [\[8](#page-9-2)], and GPT4 [\[12\]](#page-9-3), have demonstrated impressive generalization capabilities in various real-world scenarios.

Medical FMs are a specialized type of FM designed for the medical domain [\[11,](#page-9-4)[19\]](#page-9-5), representing one of the most notable advancements in medical AI. Among these, Medical Vision-Language pre-training stands out as a specific solution that improves medical image analysis by learning domain-specific features from medical images paired with corresponding clinical descriptions or reports [\[10](#page-9-6),[18\]](#page-9-7). Recent medical FMs have focused heavily on radiology, particularly chest X-rays [\[16](#page-9-8)[,17](#page-9-9)]. For retinal FMs, RETFound [\[20\]](#page-9-0) has been proposed, which is pre-trained on 1.6 million retinal images using Masked Autoencoders (MAE). Another notable example is FLAIR [\[15](#page-9-10)], a vision-language model that leverages the CLIP architecture to enhance performance in retinal image analysis, supporting zero-shot and few-shot inference through text supervision. Unlike task-specific models that may yield sub-optimal results in the presence of domain shifts, retinal FMs demonstrate robust generalization capabilities across different retinal datasets and tasks. This presents an attractive solution to enhance model efficacy and reduce the annotation burden on experts, thereby enabling widespread clinical AI applications in retinal imaging.

Albeit impressive, existing retinal FMs are restricted to processing a single imaging modality, such as Colour Fundus Photography (CFP) and Optical Coherence Tomography (OCT). In clinical ophthalmology, diagnosis often involves multiple modalities, including CFP, OCT, and Fundus Fluorescence Angiography (FFA) images. This requires training separate FMs for each modality, resulting in higher maintenance costs and hindering the acquisition of complementary information across modalities. The question arises: Can a retinal FM be developed to process multiple modalities? Moreover, expert domain knowledge, often in the form of labels or medical reports, is crucial for effective retinal image analysis. It guides models in capturing clinically relevant information, ensuring clinical significance in real-world healthcare scenarios. However, current retinal FMs struggle to fully leverage expert annotations, potentially hindering specialized representation learning. Another question arises: Can domain knowledge be incorporated into a retinal FM for better generalization ability?

To address the research problems mentioned above, we introduce UrFound, a universal retinal FM designed to learn versatile representations from both multimodal retinal images and domain knowledge. UrFound employs a modalityagnostic image encoder for processing CFP or OCT images and integrates

domain knowledge from categorical labels and clinical descriptions through text supervision. To achieve this, we convert expert annotations into detailed clinical descriptions and propose a knowledge-guided masked modeling strategy for UrFound pre-training. This strategy includes a masked image modeling branch to reconstruct randomly masked patches of retinal images, and a conditional masked language modeling branch to predict masked word tokens based on the corresponding retinal image. This approach aligns multimodal images and textual expert annotations within a unified latent space, facilitating domain-specific representation learning.

Empirically, we find that incorporating domain knowledge into the retinal FM through text supervision enhances generalization ability. Furthermore, UrFound captures information from CFP and OCT images and performs well with both modalities. Despite being pre-trained on a relatively small dataset of 180k retinal images with expert annotations, UrFound significantly outperforms state-of-theart (SOTA) retinal FMs trained on up to 1.6 million unlabeled images across eight public retinal datasets. This demonstrates the effectiveness of multimodal images and domain knowledge in training powerful retinal FMs.

Our contribution is threefold: 1. We propose UrFound, a universal retinal foundation model capable of processing CFP and OCT images while incorporating domain knowledge from expert annotations. 2. We introduce a knowledgeguided masked modeling strategy that unifies the pre-training from multimodal images and clinical descriptions, effectively integrating domain knowledge. 3. We provide comprehensive evaluations, comparing UrFound with SOTA retinal FMs across eight public retinal datasets.

Image /page/2/Figure/4 description: This diagram illustrates a multimodal learning framework for retinal image analysis. The top section shows a language branch where 104K CFP and 83K OCT images are processed with expert annotations and clinical descriptions. These descriptions are then randomly masked and fed into a language decoder, which aims to reconstruct the masked tokens, indicated by the LM loss. The bottom section depicts an image branch where the same images are randomly masked and fed into an image encoder. The encoded representations are then passed to an image decoder, which reconstructs the masked image patches, indicated by the MIM loss. Both branches interact through shared representations, suggesting a joint learning approach.

<span id="page-2-0"></span>Fig. 1. Knowledge-guided masked modeling framework for UrFound pre-training. Solid arrows represent data flow, while dashed arrows indicate gradient flow.

# 2 The UrFound Model

In this section, we propose UrFound, a retinal FM designed for CFP and OCT images, as the initial step toward developing universal retinal FMs. UrFound is trained with guidance from expert annotations, which can take the form of categorical labels, clinical descriptions, or any other formats that can be encoded in text supervision. UrFound aims to learn domain-specific representations by reconstructing masked patches of a retinal image while predicting masked word tokens of textual domain knowledge conditioned on the unmasked image patches.

Figure [1](#page-2-0) provides an overview of the UrFound model. UrFound has an image encoder that learns the latent representation of retinal images as well as two decoders that reconstruct the original retinal image and predict the word tokens of the associated clinical descriptions from the latent representation, respectively. The input of the image encoder can be either a CFP or OCT image. During pretraining, we apply masked image modeling to randomly mask certain patches of the input image. Then, the rest unmasked image patches are fed into the image encoder to obtain their embeddings. These embeddings are then forwarded to the image decoder to reconstruct the masked image patches, aiding the model in capturing versatile and informative visual features.

Similar to masked image modeling, we apply conditional masked language modeling to replace certain portions of word tokens of the clinical descriptions with the mask token. The language decoder is then trained to predict the original identity of the masked tokens based on both the unmasked words and the latent image representation from the image encoder. This approach encourages the model to recognize and comprehend the relationships between the retinal image and fine-grained medical knowledge. It serves to bridge the gap between visual features and textual information, integrating domain knowledge from the descriptions into the latent image representation.

## 2.1 Knowledge-Guided Masked Modeling

Formally, given a retinal image  $X$ , it is first reshaped into n patches with the patch size s (e.g.,  $16 \times 16$  in ViT [\[5](#page-9-11)]). A random mask  $M \in \{0, 1\}^n$  is generated with the mask ratio  $\rho$  where 1 indicates a masked patch and 0 indicates an unmasked patch. The masked image X is obtained as:  $\hat{X}_i = X_i \cdot (1 - M_i) + X_0$ .  $M_i, \forall i \in \{1, \dots, n\}$ , where  $X_0$  represents the image [MASK] token. Let  $f(\cdot)$  be the image encoder that maps each image patch to a latent representation  $z_i =$  $f(X_i)$ , and  $g^v(\cdot)$  be the image decoder that reconstructs the original image patch  $X_i$  from the latent representation. Then the mask imaging modeling (MIM) can be achieved by minimizing the following mean square error (MSE) loss:

$$
\mathcal{L}_{MIM} = \sum_{i=1}^{n} M_i \cdot ||X_i - g^v(\mathbf{z}_i)||_2^2, \tag{1}
$$

which measures the differences between the reconstructed and original image patches. We adopt the high-resolution trick in [\[19\]](#page-9-5) to let the model reconstruct high-resolution patches at  $2\times$  the input resolution, which allows the model to learn more detailed local features.

For conditional masked language modeling (MLM), the input text is transformed into a sequence of tokens  $W = [w_1, \dots, w_L]$ , where L is the sequence length. Then, a certain percentage of tokens in the sequence are randomly replaced with a special [MASK] token, leading to a masked set  $W_{\mathcal{M}}$  and an unmasked set  $W_N$ . Let **z** be the average pooling of the unmasked image patch representations, and  $h(\cdot)$  be the text decoder to restore the masked text tokens. The objective of MLM is to minimize the negative log-likelihood function as follows:

$$
\mathcal{L}_{MLM} = -\sum_{w_i \in \mathcal{W}_{\mathcal{M}}} \log P(h(w_i) | \{ h(w_j), w_j \in \mathcal{W}_{\mathcal{N}} \}, \mathbf{z}), \tag{2}
$$

which predicts the original identities of those masked tokens based on the surrounding context and the latent image representation. The total pre-training objective function of the UrFound model is  $\mathcal{L} = \mathcal{L}_{MIM} + \mathcal{L}_{MLM}$ . After pretraining, the decoders are discarded and the encoder  $f(\cdot)$  can be fine-tuned with a small number of data for specific downstream tasks for retinal image analysis.

## 2.2 Text Preparation

For retinal images, the majority of publicly available expert annotations come in the form of categorical labels rather than text. To maximize the utilization of domain knowledge for pre-training, we follow FLAIR [\[15\]](#page-9-10) to enhance categorical image labels by augmenting relevant medical findings sourced from established knowledge bases and clinical literature. For instance, the label "drusens" might be described as "yellow deposits under the retina" or "numerous uniform round yellow-white lesions". Each label may have a varying number of descriptions. During pre-training, we randomly select one of these descriptions for samples in each batch, enhancing the diversity and robustness of the text supervision.

## 2.3 Multimodal Image Processing

UrFound directly learns representations for CFP and OCT images using a modality-agnostic encoder. We have observed that this straightforward implementation performs well and achieves superior generalization, particularly when training is guided by domain knowledge through masked modeling. We also explore variants that use separate patch embedding layers, encoders, and decoders for CFP and OCT imaging, respectively, while such modifications do not lead to better results in our experiments.

# 3 Experiments

In this section, we assess the performance of UrFound compared to the SOTA retinal FMs and conduct comprehensive experiments to address the following key questions: Q1. Can the imaging modalities of CFP and OCT be encoded in a universal FM? Q2. Does domain knowledge improve the generalization ability of FMs? Q3. Do CFP and OCT images contain supplementary information that helps representation learning? **Q4.** How well do retinal FMs perform in terms of data efficiency? Q5. How effective are retinal FMs in adapting to downstream tasks compared with task-specific models?

## 3.1 Experimental Setup

We assess the capabilities of UrFound in adapting to diagnostic classification tasks with minimal additional training. In line with common practice, we add a linear classifier head on top of the learned image encoder and then fine-tune both the encoder and classifier with task-specific labels. We compare the proposed UrFound against the MAE model pre-trained on natural images as well as SOTA retinal FMs including RETFound [\[20\]](#page-9-0) and FLAIR [\[15](#page-9-10)]. For these compared models, we use official checkpoints for fine-tuning. We report the area under the receiver operating curve (ROC) and the area under the precisionrecall curve (PRC) as evaluation metrics. We choose the best checkpoints with the highest ROC scores on the validation set for final evaluation.

Datasets. For pre-training, we construct a training set by collecting 25 CFP datasets and one large OCT dataset, which include 103,786 CFP images and 83,484 OCT images with expert annotations, covering a wide range of ophthalmic diseases. More details can be found in the supplementary material. We follow [\[15](#page-9-10)] to augment domain knowledge and transform categorical labels into textual inputs. For evaluation of fine-tuning performance, we test 8 publicly available datasets across three diagnostic classification tasks including diabetic retinopathy grading (IDRID [\[13\]](#page-9-12), MESSIDOR [\[3\]](#page-9-13), APTOS [\[7\]](#page-9-14)), glaucoma detection (PAPILA [\[9\]](#page-9-15), GF [\[1](#page-9-16)]), and multi-disease diagnosis (JSIEC [\[2\]](#page-9-17), Retina, OCTID  $[6]$ ).

<span id="page-5-0"></span>

| Dataset       | MAE   |       | FLAIR |       | RETFd-CFP |              | RETFd-OCT |       | UrFound      |       |
|---------------|-------|-------|-------|-------|-----------|--------------|-----------|-------|--------------|-------|
|               | ROC   | PRC   | ROC   | PRC   | ROC       | PRC          | ROC       | PRC   | ROC          | PRC   |
| <b>APTOS</b>  | 94.06 | 67.59 | 92.68 | 62.20 | 94.26     | <b>71.87</b> | 87.56     | 53.76 | <b>94.86</b> | 71.64 |
| <b>IDRID</b>  | 79.24 | 43.65 | 80.88 | 49.32 | 83.33     | 51.13        | 59.29     | 28.66 | 85.22        | 57.73 |
| Messidor      | 84.21 | 48.76 | 81.88 | 48.32 | 86.40     | 58.59        | 65.89     | 28.59 | 88.22        | 60.78 |
| <b>PAPILA</b> | 62.85 | 47.48 | 74.80 | 59.30 | 74.36     | 57.27        | 51.67     | 35.03 | 78.32        | 62.54 |
| GF            | 93.09 | 83.17 | 78.87 | 59.60 | 95.68     | <b>88.18</b> | 87.61     | 70.85 | 95.75        | 88.01 |
| <b>JSIEC</b>  | 98.46 | 81.78 | 93.53 | 52.65 | 99.39     | 86.95        | 88.44     | 41.09 | 99.51        | 92.34 |
| Retina        | 74.22 | 53.70 | 77.75 | 55.33 | 86.22     | 71.59        | 75.43     | 53.76 | 90.09        | 79.30 |
| <b>OCTID</b>  | 98.67 | 95.35 | 84.52 | 60.20 | 93.85     | 82.09        | 99.39     | 97.58 | 99.55        | 97.97 |

Table 1. Performance of retinal FMs on different datasets (best, second best).

Implementation Details. We implement UrFound by using PyTorch on a single NVIDIA A100 GPU. We employ a Vision Transformer (ViT-base) with 12 Transformer blocks and a patch embedding layer as the retinal image encoder. We utilize 8 and 6 Transformer blocks as the image and text decoders, respectively. In the pre-training stage, we initialize UrFound with the MAE model and use the tokenizer of BERT-Base [\[4](#page-9-19)] to convert clinical descriptions into word

tokens. We use a mask ratio of 0.75 for image modeling and 0.5 for language modeling. We resize the input image to  $224 \times 224$  both in the pre-training stage and fine-tuning stage. Random horizontal flip and random crop are implemented in the pre-training stage. And random horizontal flip, and color jitter for data augmentation in the fine-tuning stage, each with a probability of 0.5. The total training epoch is set to 200 with a warm-up period of 40 epochs. The learning rate is set to 1.5e-4, and the batch size is set to 128. In the fine-tuning stage, the learning rate is adjusted to 1e-4, the batch size is reduced to 16, and the training epoch is set to 50 with a warm-up period of 10 epochs.

## 3.2 Main Results

Table [1](#page-5-0) shows the classification results of the compared retinal FMs fine-tuned for various retinal disease diagnosis tasks, and statistical significance analysis is available in the supplementary material. It can be observed that retinal FMs such as RETFound-CFP and UrFound achieve better results than MAE in all the cases, which demonstrates the effectiveness of retinal FMs in learning generalizable representations for retinal imaging analysis. UrFound consistently outperforms the second-best method, RETFound. This superiority can be attributed to the integrated domain knowledge in UrFound through text supervision. In contrast, although FLAIR also leverages domain knowledge, it does not perform well and lags behind MAE in some cases. This is possibly because FLAIR focuses on image-text alignment rather than capturing the visual features of retinal images. It results in a sub-optimal image encoder for image understanding in the pretrain-finetune setting.

RETFound-CFP and FLAIR are designed specifically for CFP images, exhibiting subpar performance when applied to OCT images. Similarly, RETFound-OCT yields the poorest results on CFP datasets. In contrast, UrFound showcases its superiority in processing both CFP and OCT modalities. It achieves this by learning universal and comprehensive representations that span across modalities, demonstrating its capability to effectively handle diverse imaging types.

Impact of Multimodal Imaging and Domain Knowledge. To investigate how multimodal data and domain knowledge affect the performance of UrFound, we compared UrFound against its single-modality variants, either with or without domain knowledge. As shown in Table [2,](#page-7-0) without text supervision, UrFound trained from CFP+OCT images achieves reasonably good results on both CFP and OCT datasets. This indicates that it is promising to learn universal FMs for multiple retinal imaging modalities (Q1). Furthermore, the inclusion of text supervision significantly enhances the performance of UrFound, which demonstrates the effectiveness of domain knowledge in learning domain-specific and generalizable representations  $(Q2)$ . With text supervision, UrFound trained from CFP+OCT images outperforms its single-modality counterparts, which suggests that CFP and OCT images contain supplementary information beneficial for improved representation learning (Q3).

| Dataset       | W/O Text Supervision |       |       |       |              |       | W/ Text Supervision |              |       |       |              |              |
|---------------|----------------------|-------|-------|-------|--------------|-------|---------------------|--------------|-------|-------|--------------|--------------|
|               | CFP                  |       | OCT   |       | CFP+OCT      |       | CFP                 |              | OCT   |       | CFP+OCT      |              |
|               | ROC                  | PRC   | ROC   | PRC   | ROC          | PRC   | ROC                 | PRC          | ROC   | PRC   | ROC          | PRC          |
| <b>APTOS</b>  | 93.81                | 65.93 | 89.92 | 56.11 | 94.01        | 67.58 | 94.36               | 68.68        | 90.40 | 56.38 | <b>94.86</b> | <b>71.64</b> |
| <b>IDRID</b>  | 79.47                | 45.44 | 69.10 | 35.32 | 79.51        | 44.59 | 84.64               | 55.46        | 66.28 | 31.09 | <b>85.22</b> | <b>57.73</b> |
| Messidor      | 84.76                | 52.86 | 69.10 | 30.73 | 84.28        | 50.47 | 86.24               | 58.28        | 71.21 | 32.34 | <b>88.22</b> | <b>60.78</b> |
| <b>PAPILA</b> | 69.13                | 52.36 | 47.21 | 35.47 | 69.65        | 53.68 | 73.45               | 54.92        | 56.59 | 38.19 | <b>78.32</b> | <b>62.54</b> |
| GF            | 93.84                | 84.50 | 89.01 | 73.30 | 93.48        | 83.70 | 95.16               | 87.17        | 89.61 | 73.62 | <b>95.75</b> | <b>88.01</b> |
| <b>JSIEC</b>  | 98.72                | 88.72 | 91.71 | 50.60 | 99.08        | 85.44 | 99.48               | <b>92.84</b> | 92.35 | 51.33 | <b>99.51</b> | 92.34        |
| Rtina         | 88.17                | 76.01 | 70.29 | 48.78 | 87.08        | 74.34 | 88.50               | 75.77        | 81.40 | 61.81 | <b>90.09</b> | <b>79.30</b> |
| <b>OCTID</b>  | 98.40                | 95.60 | 99.37 | 97.35 | <b>99.59</b> | 97.88 | 98.05               | 94.56        | 99.28 | 95.33 | 99.55        | <b>97.97</b> |

<span id="page-7-0"></span>Table 2. Performance of UrFound and its variants (best, second best).

Data Efficiency. Figure [2a](#page-7-1) shows the classification results of the compared FMs at different percentages of training data on the APTOS, GF, JSIEC, and OCTID datasets. UrFound outperforms other retinal FMs in most settings and demonstrates a more significant advantage when fewer data are used for training (Q4). It is noteworthy that UrFound is pre-trained on ∼180k retinal images, a significantly smaller dataset compared to existing retinal FMs such as RETFound-CFP, which is trained with over 900k CFP images. These demonstrate the superior data efficiency of UrFound, making it well-suited for retinal imaging analysis with limited annotations.

Image /page/7/Figure/4 description: The image contains two figures, labeled (a) and (b). Figure (a) consists of four line graphs, each plotting a performance metric against a value on the x-axis ranging from 0.2 to 1.0. The y-axis for the top two graphs ranges from 75 to 95, and the y-axis for the bottom two graphs ranges from 80 to 100. The top left graph is labeled APTOS, the top right is GF, the bottom left is JSIEC, and the bottom right is OCTID. Each graph shows four lines representing different methods: MAE (orange circles), FLAIR (yellow stars), RETFound-CFP (blue triangles), RETFound-OCT (red triangles), and UrFound (green circles). Figure (b) is a bar chart showing AUROC values for different datasets: APTOS, GF, JSIEC, and OCTID. The y-axis ranges from 92 to 100. The bars represent three methods: TSM-CFP (yellow with diagonal lines), TSM-OCT (green with diagonal lines), and UrFound (blue with diagonal lines). The specific AUROC values shown are: APTOS (TSM-CFP: 95.17, TSM-OCT: 94.86), GF (TSM-CFP: 94.46, TSM-OCT: 95.75), JSIEC (TSM-CFP: 98.08, TSM-OCT: 99.51), and OCTID (TSM-OCT: 96.86, UrFound: 99.55).

<span id="page-7-1"></span>Fig. 2. (a) Data efficiency of retinal FMs, Axes X and Y are the percentage of training data used and the corresponding AUROC, respectively. (b) Comparison of UrFound with tasks-specific models in AUROC with different datasets.

Comparison with Task-specific Models. To verify the advantages of transforming expert annotations into text for pre-training, we compare UrFound with task-specific models (TSMs) that are first trained with the class labels of specific classification tasks (as a supervised way of pre-training) and then adapted to test datasets for evaluation. Specifically, we test two TSMs: one for diabetic retinopathy grading (TSM-CFP) and the other for OCT disease classification (TSM-OCT). TSM-CFP is trained on all the CFP pre-training datasets for diabetic retinopathy grading, comprising 51,556 CFP images and labels of five classes. TSM-OCT is trained on all the OCT pre-training datasets, which include 83,484 OCT images and labels of four classes. In total, the data used for training TSMs account for 72% of those used for pre-training UrFound.

Figure [2b](#page-7-1) presents the ROC scores of TSMs and UrFound on the APTOS, GF, JSIEC, and OCTID datasets, where each dataset corresponds to different downstream tasks. UrFound and TSM-CFP obtain similar results on the ATPOS dataset. This is expected because the task of APTOS aligns with the training of TSM-CPT. UrFound consistently outperforms TSMs on other datasets. This suggests that TSMs lack the flexibility to learn generalizable representations for various tasks. In contrast, UrFound benefits from expert annotations via text supervision, offering a more effective approach to integrating valuable domain knowledge in representation learning (Q5).

# 4 Conclusion

We proposed UrFound, a Universal retinal Foundation model, which features a modality-agnostic image encoder and utilizes knowledge-guided mask modeling as a pre-training objective, allowing it to learn generalizable representations from both multimodal images and expert annotations. Through comprehensive experiments on 8 public retinal datasets, we demonstrated its strong generalization ability and data efficiency in adapting to various downstream tasks. Nevertheless, UrFound has two limitations: 1. UrFound is designed to process CFP and OCT images while there exist other retinal imaging modalities such as FFA. 2. UrFound is pre-trained on a relatively small dataset with disease labels as expert annotations. In practice, many unlabeled data are available for pre-training.

Acknowledgments. This work was supported in part by the National Research Foundation of Singapore under its AI Singapore Programme (AISG) under Award AISG2-TC-2021-003, and in part by the Agency for Science, Technology and Research (A\*STAR) through its AME Programmatic Funding Scheme under Project A20H4b0141, the RIE2020 Health and Biomedical Sciences (HBMS) Industry Alignment Fund Pre-Positioning (IAF-PP) (grant no. H20C6a0032), the 2022 Horizontal Technology Coordinating Office Seed Fund (Biomedical Engineering Programme - BEP RUN 3, grant no. C221318005) and partially supported by A\*STAR Central Research Fund "A Secure and Privacy-Preserving AI Platform for Digital Health".

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-9-16"></span>1. Ahn, J.M., et al.: A deep learning model for the detection of both advanced and early glaucoma using fundus photography. PloS one 13(11), e0207982 (2018)
- <span id="page-9-17"></span>2. Cen, L.P., et al.: Automatic detection of 39 fundus diseases and conditions in retinal photographs using deep neural networks. Nat. Commun. 12(1), 4828 (2021)
- <span id="page-9-13"></span>3. Decencière, E., et al.: Feedback on a publicly distributed image database: the messidor database. Image Anal. Stereol. 33(3), 231–234 (2014)
- <span id="page-9-19"></span>4. Devlin, J., Chang, M.W., Lee, K., Toutanova, K.: BERT: pre-training of deep bidirectional transformers for language understanding. In: Proceedings of NAACL, pp. 4171–4186 (2019)
- <span id="page-9-11"></span>5. Dosovitskiy, A., et al.: An image is worth 16x16 words: transformers for image recognition at scale. In: Proceedings of ICLR (2021)
- <span id="page-9-18"></span>6. Gholami, P., Roy, P., Parthasarathy, M.K., Lakshminarayanan, V.: OCTID: optical coherence tomography image database. Comput. Electr. Eng. 81, 106532 (2020)
- <span id="page-9-14"></span>7. Karthik, Maggie, S.D.: Aptos 2019 blindness detection (2019). [https://kaggle.com/](https://kaggle.com/competitions/aptos2019-blindness-detection) [competitions/aptos2019-blindness-detection](https://kaggle.com/competitions/aptos2019-blindness-detection)
- <span id="page-9-2"></span>8. Kirillov, A., et al.: Segment anything. In: Proceedings of ICCV, pp. 4015–4026 (2023)
- <span id="page-9-15"></span>9. Kovalyk, O., et al.: PAPILA: dataset with fundus images and clinical data of both eyes of the same patient for glaucoma assessment. Sci. Data 9(1), 291 (2022)
- <span id="page-9-6"></span>10. Li, C., et al.: Llava-med: training a large language-and-vision assistant for biomedicine in one day. In: Advances in Neural Information Processing Systems, vol. 36 (2024)
- <span id="page-9-4"></span>11. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nature Communications 15(1), 654 (2024)
- <span id="page-9-3"></span>12. OpenAI: Chatgpt-4. <https://openai.com/chatgpt> (2023). Accessed 01 Feb 2024
- <span id="page-9-12"></span>13. Porwal, P., et al.: Indian diabetic retinopathy image dataset (IDRiD): a database for diabetic retinopathy screening research. Data 3(3), 25 (2018)
- <span id="page-9-1"></span>14. Radford, A., et al.: Learning transferable visual models from natural language supervision. In: Proceddings of ICML, pp. 8748–8763 (2021)
- <span id="page-9-10"></span>15. Silva-Rodriguez, J., Chakor, H., Kobbi, R., Dolz, J., Ayed, I.B.: A foundation language-image model of the retina (FLAIR): encoding expert knowledge in text supervision. arXiv preprint [arXiv:2308.07898](http://arxiv.org/abs/2308.07898) (2023)
- <span id="page-9-8"></span>16. You, K., et al.: CXR-CLIP: toward large scale chest x-ray language-image pretraining. In: Greenspan, H., et al. (ed.) Medical Image Computing and Computer Assisted Intervention - MICCAI 2023, MICCAI 2023, LNCS, vol. 14221, pp. 101– 111. Springer, Cham (2023). [https://doi.org/10.1007/978-3-031-43895-0\\_10](https://doi.org/10.1007/978-3-031-43895-0_10)
- <span id="page-9-9"></span>17. Zhang, X., Wu, C., Zhang, Y., Xie, W., Wang, Y.: Knowledge-enhanced visuallanguage pre-training on chest radiology images. Nature Communications  $14(1)$ , 4542 (2023)
- <span id="page-9-7"></span>18. Zhang, Y., Jiang, H., Miura, Y., Manning, C.D., Langlotz, C.P.: Contrastive learning of medical visual representations from paired images and text. In: Machine Learning for Healthcare Conference, pp. 2–25. PMLR (2022)
- <span id="page-9-5"></span>19. Zhou, H.Y., Lian, C., Wang, L., Yu, Y.: Advancing radiograph representation learning with masked record modeling. In: Proceedings of ICLR, pp. 1–16 (2023)
- <span id="page-9-0"></span>20. Zhou, Y., Chia, M.A., Wagner, S.K., Ayhan, M.S., Williamson, D.J., Struyven, R.R., Liu, T., Xu, M., Lozano, M.G., Woodward-Court, P., et al.: A foundation model for generalizable disease detection from retinal images. Nature 622(7981), 156–163 (2023)

Image /page/10/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a gray circle with a gray bookmark shape inside. The bookmark is tilted to the left and appears to be partially unfurled. The text is in a light gray sans-serif font.

# **VertFound: Synergizing Semantic and Spatial Understanding for Fine-Grained Vertebrae Classification via Foundation Models**

Yinhao Wu<sup>1</sup>, Jinzhou Tang<sup>1</sup>, Zequan Yao<sup>1</sup>, Mingjie Li<sup>2</sup>, Yuan Hong<sup>3</sup>, Dongdong Yu<sup>4</sup>, Zhifan Gao<sup>5</sup>, Bin Chen<sup>4</sup>, and Shen Zhao<sup>1( $\boxtimes$ )</sup>

<sup>1</sup> School of Intelligent Systems Engineering, Sun Yat-sen University, Shenzhen, China

<EMAIL>, <EMAIL> <sup>2</sup> Radiation Oncology, Stanford University, Stanford, USA

<sup>3</sup> Zhejiang Normal University, Hangzhou, China

<sup>4</sup> The First Affiliated Hospital, Zhejiang University School of Medicine, Hangzhou, China

<sup>5</sup> School of Biomedical Engineering, Sun Yat-sen University, Shenzhen, China

**Abstract.** Achieving automated vertebrae classification in spine images is a crucial yet challenging task due to the repetitive nature of adjacent vertebrae and limited fields of view (FoV). Different from previous methods that leverage the serial information of vertebrae to optimize classification results, we propose VertFound, a framework that harnesses the inherent adaptability and versatility of foundation models for finegrained vertebrae classification. Specifically, VertFound designs a vertebral positioning with cross-model synergy (VPS) module that efficiently merges semantic information from CLIP and spatial features from SAM, leading to richer feature representations that capture vertebral spatial relationships. Moreover, a novel Wasserstein loss is designed to minimize disparities between image and text feature distributions by continuously optimizing the transport distance between the two distributions, resulting in a more discriminative alignment capability of CLIP for vertebral classification. Extensive evaluations on our vertebral MRI dataset show VertFound exhibits significant improvements in both identification rate (IDR) and identification accuracy (IRA), which underscores its efficacy and further shows the remarkable potential of foundation models for finegrained recognition tasks in the medical domain. Our code is available at [https://github.com/inhaowu/VertFound.](https://github.com/inhaowu/VertFound)

**Keywords:** Foundation Models · Merging Models · Fine-grained Classification

Y. Wu and J. Tang—These authors contributed equally to this work. This work is supported by the National Natural Science Foundation of China under Grants 62101607.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 763–772, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_71)\_71

# 1 Introduction

The automated recognition of vertebrae in spinal images is crucial for a wide range of medical applications, including the diagnosis of spinal disorders, surgical planning, and postoperative assessment  $[1,2]$  $[1,2]$  $[1,2]$ . This process entails not only the precise localization of individual vertebrae but also their subsequent fine-grained classification. Among these tasks, achieving accurate classification poses a great challenge [\[3\]](#page-18-2), particularly due to the subtle morphological differences among adjacent vertebrae and the constraints arising from arbitrary fields of view (FoV).

Existing methods mainly utilize the spatial relationships of vertebrae to enhance classification results that are aligned with the inherent sequential characteristics of vertebrae. For example, Yang *et al.* [\[4\]](#page-18-3) enhanced vertebrae classification performance by proposing a message passing scheme to depict the spatial relationship of vertebrae. Wang *et al.* [\[5\]](#page-18-4) combined key point localization with anatomically-constrained knowledge to facilitate vertebrae classification in CT images. Cui *et al.* [\[6\]](#page-18-5) introduced a bidirectional relation module to capture the vertebral relationships among vertebrae with a self-attention mechanism. Wu *et al.* [\[7\]](#page-18-6) designed a sequence loss based on dynamic programming to better preserve the sequential structure of vertebrae. However, the reliance on empirical designs and the scarcity of datasets may limit their robustness to data variability and transferability across different domains.

Recently, large-scale pre-trained foundation models, such as CLIP [\[8\]](#page-18-7) and SAM [\[9\]](#page-18-8), have shown remarkable potential with exceptional adaptability and flexibility in the medical domain. For example, Liu *et al.* [\[10\]](#page-19-0) achieved universal segmentation on partially labeled datasets by leveraging CLIP text embeddings to comprehend the anatomical relationships of different organs and tumors. Lao *et al.* and Qin *et al.* [\[11](#page-19-1)[,12](#page-19-2)] introduced different approaches to effectively fuse the different text prompts to enhance the abilities of GLIP [\[13](#page-19-3)] for zero-shot lesion detection. Cheng *et al.* and Wang *et al.* [\[14](#page-19-4)[,15](#page-19-5)] showed the improved performance of SAM in medical images by introducing additional adapter layers.

However, the use of foundation models in fine-grained vertebrae classification faces considerable challenges due to their deficiencies in effectively exploiting vertebral spatial relationships and their limited discrimination against sim-

<span id="page-11-0"></span>Image /page/11/Figure/6 description: This figure illustrates three different methods for segmenting a spine MRI. Method (a) uses CLIP with a text prompt 'A photo of a spine' to generate a heatmap overlay on the MRI. Method (b) uses SAM with box prompts [b1, b2, ..., bn] to generate a similar heatmap. Method (c) combines CLIP and SAM, also using the text prompt 'A photo of a spine' and box prompts, to produce a heatmap.

**Fig. 1.** Illustrations of the individual and combined capabilities of CLIP and SAM for feature representation in spine images. (a) CLIP excels in semantic understanding across the entire image. (b) SAM excels in spatial understanding for precise localization. (c) The merged model obtains features with enhanced local and global information.

ilar vertebral morphologies. On the one hand, different pre-training objectives endow foundation models with different capabilities in feature representation. As illustrated in Fig.  $1(a)$  $1(a)$  and (b), contrastive learning-based models such as CLIP excel at capturing high-level semantic information [\[16\]](#page-19-6), while segmentation models such as SAM excel at capturing low-level spatial details [\[17](#page-19-7)]. This disparity indicates that employing a single foundation model might not adequately capture the positional information of vertebrae. On the other hand, foundation models struggle to handle fine-grained classification tasks [\[18](#page-19-8)], often lacking the necessary discriminative capacity for the intricate vertebral textures.

To address these challenges, we propose **VertFound**, a framework designed to efficiently synergize the strengths of CLIP and SAM into a unified foundation model for fine-grained vertebrae classification. Firstly, feature extractors from CLIP and SAM are employed to obtain corresponding image-level and region-level features. The proposed vertebral positioning with cross-model synergy (VPS) module then enriches the feature representation by adopting the dot product attention mechanisms. Specifically, VPS utilizes two different attention modules (i.e., CLIP2SAM and SAM2CLIP) to facilitate effective information integration. The CLIP2SAM attention employs image-level features as the queries, while the region-level features serve as both keys and values, which enables local features with global dependencies. Conversely, SAM2CLIP attention employs region-level features as queries and image-level features as keys and values, thereby enriching semantic features with spatial context. This bidirectional attention mechanism amalgamates the strengths of CLIP and SAM, yielding enriched feature representations that encapsulate vertebral spatial relationships. Subsequently, we incorporate textual information as an additional modality input to leverage the image-text alignment capabilities of CLIP for achieving fine-grained classification within vertebral regions. Considering the significant similarities in feature distributions within vertebral images and textual descriptions, we further introduce a Wasserstein loss that transfers contrastive learning into an optimal transport problem by continually optimizing the transport cost between the two distributions. This enables the model to minimize the disparities between the image and text feature distributions and enhance the discriminative alignment capability of CLIP for vertebral classification. Empirical validation underscores the efficacy of VertFound while demonstrating the huge potential of foundation models for fine-grained classification tasks in the medical domain.

# 2 Methodology

As depicted in Fig. [2,](#page-13-0) VertFound has two main stages. First, CLIP and SAM image encoders are employed to extract image-level and region-level features. The VPS module then combines these features to enhance vertebral position information. In the second stage, fine-grained vertebrae classification is achieved through image-text alignment within CLIP, with an additional Wasserstein loss to improve alignment score discrimination by optimizing the transport distance between image and text feature distributions.

Image /page/13/Figure/1 description: This is a flowchart illustrating a two-stage process for fine-grained vertebral classification. Stage 1, 'Vertebral Position Enhancement,' takes an MRI image and bbox prompts as input. It uses a CLIP Image Encoder and a SAM module (with Image Encoder, Mask Decoder, and Prompt Encoder) to generate SAM Positioning Features. These features, along with the CLIP Image Encoder output, are fed into a VPS Module. Stage 2, 'Fine-grained Vertebral Classification,' receives the output from the VPS Module (Position-Enhanced Regions) and a Category List processed by a CLIP Text Encoder. The Position-Enhanced Regions are then aligned with text features, creating an 'Image-text Alignment' matrix. This alignment is updated based on Wasserstein distance to produce a 'Refined Image-text Alignment,' which is then used for classification.

<span id="page-13-0"></span>**Fig. 2.** The two-stage framework of the proposed VertFound. The first stage obtains comprehensive feature representations by employing the VPS module to effectively synergize semantic and spatial features for capturing vertebral position information. The second stage achieves fine-grained vertebrae classification by leveraging image-text alignment within CLIP, while a Wasserstein loss is proposed to enhance the discriminative alignment capability.

## 2.1 Vertebral Position Enhancement

**Image- and Region-Level Feature Extraction.** Capitalizing on the robust generalization capabilities of foundation models, we directly employ the frozen visual encoders of CLIP and SAM as feature extractors to obtain the corresponding image features at the image-level and region-level. Specifically, we input images I with a shape of  $H \times W$  into the vision transformer (ViT) model of CLIP to yield multi-stage features  $C_l \in \mathbb{R}^{h,d_c}$  with image-level semantic information, where l represents the l-th stage, h and  $d_c$  denote the channels and feature dimensionality, respectively. On the other hand, images combined with their associated annotated box prompts are input into the image encoder and prompt encoders of SAM to produce relevant image and prompt embeddings. The two embeddings are then merged to obtain region-level embeddings  $R \in \mathbb{R}^{n,d_s}$  with spatial information, where *n* represents the number of bounding boxes,  $d_s$  denotes feature dimensionality.

**Vertebral Positioning with Cross-Model Synergy Module.** To fully leverage the semantic and spatial knowledge from CLIP and SAM, we propose the **V**ertebral **P**ositioning with Cross-Model **S**ynergy (**VPS**) module that enhances feature representations with more vertebral spatial details. Specifically, as shown in Fig. [3,](#page-14-0) VPS adopts the dot product attention mechanisms [\[19](#page-19-9)] (i.e., SAM2CLIP and CLIP2SAM) to achieve the interactions between global and spatial features. First, CLIP2SAM receives image-level features  $C_l$  (e.g.,  ${C_{16}, C_{20}, C_{24}}$  as queries and region-level features R as both keys and values to enrich semantic features with nuanced spatial details:

<span id="page-13-1"></span>
$$
C'_{l} = \text{softmax}\left(\frac{Q_{l}K^{T}}{\sqrt{d}}\right)V\tag{1}
$$

$$
Q_l = C_l W_q, K = R W_k, V = R W_v \tag{2}
$$

<span id="page-14-0"></span>Image /page/14/Figure/1 description: This is a diagram illustrating the Vertebral Positioning with Cross-Model Synergy (VPS) Module. The module takes CLIP Image Embeddings (C1, C2, C3) and SAM Positioning Embeddings as input. These inputs are processed through CLIP2SAM blocks and a Feature Pyramid Network (FPN). The output of the FPN is concatenated and fed into a SAM2CLIP block, producing Position-Enhanced Features. The diagram also shows a CLIP2SAM & SAM2CLIP section, which takes Image Embeddings processed by Linear layers. These are used in a Multi-Head Attention mechanism, with a query (Q) derived from a Multi-Head Self-Attention block. The output of the Multi-Head Attention is normalized and combined with the output of the Multi-Head Self-Attention block.

**Fig. 3.** The paradigm of the VPS module. It employs bidirectional attention mechanisms (i.e., CLIP2SAM and SAM2CLIP) to inherit the advantages of SAM and CLIP and obtain vertebral position-aware features.

where  $W_a \in \mathbb{R}^{d_c,d}$ ,  $W_k \in \mathbb{R}^{d_s,d}$  and  $W_v \in \mathbb{R}^{d_s,d}$  are learnable weights, in which d is the dimensionality of the feature space for the queries, keys, and values. Subsequently, a Feature Pyramid Network (FPN) [\[20\]](#page-19-10) is employed to process  $C_l'$ derived from Eq. [1](#page-13-1) and obtain image-level features  $\hat{C} \in \mathbb{R}^{h,d_c}$  with multi-scale information. The refined features  $\hat{C}$  are then utilized as keys and values in the SAM2CLIP mechanism, with region-level features  $R$  serving as queries, which greatly embeds the spatial understanding with global features. Through the integration of SAM and CLIP, we anticipate that the resultant model will assimilate the representation-level strengths of each, thereby enhancing its understanding of vertebral spatial relationships.

## 2.2 Fine-Grained Vertebral Classification

**Region-Text Alignment.** Beyond learning richer visual representations, we also integrate textual information to harness CLIP's capabilities in image-text alignment for improved vertebral classification. Similar to CLIP, we compute the alignment matrix  $S \in \mathbb{R}^{n,m}$  between the refined region features  $V \in \mathbb{R}^{n,d_c}$ , generated by the VPS, and the text features  $T \in \mathbb{R}^{m,d_c}$ , extracted from a list of m vertebral categories (e.g., S, L5, L4,  $\dots$ , C1) by using the text encoder of CLIP. This computation is formulated as follows:

<span id="page-14-1"></span>
$$
S_{ij} = \frac{\phi_v(V)\phi_t(T)^T}{\tau\sqrt{\sum_k \phi_v(V_{ik})^2}\sqrt{\sum_k \phi_t(T_{jk})^2}}
$$
(3)

Here,  $\phi_v$  and  $\phi_t$  represent different learnable feature projections on image and text features, while  $\tau$  is a temperature parameter. Based on this, we calculate the cross-entropy loss (CEL) to explicitly push away representations from different image-text pairs while pulling together those that share the same semantics:

$$
\mathcal{L}_{CEL} = -\sum_{i=1}^{n} \sum_{j=1}^{m} Y_{ij} \log S_{ij}
$$
\n(4)

where  $Y \in \{0,1\}^{n,m}$  represents the one-hot vertebral category of the image regions. This approach facilitates precise vertebrae classification by harnessing CLIP's alignment capabilities at the region level.

**Wasserstein Loss.** However, considering the significant similarities between different vertebrae and their corresponding textual descriptions, the image-text alignment score  $S$  obtained in Eq. [3](#page-14-1) might lack sufficient discrimination for finegrained vertebral classification. Inspired by the work [\[21](#page-19-11)], we propose a Wasserstein loss (WSL) that casts contrastive learning into an *optimal transport problem*. Specifically, we optimize the Wasserstein distance  $d_M^{\lambda}(V,T)$  to reduce the transport cost between two distributions, which results in more discriminative alignment scores for vertebral classification. The calculation of  $d_M^{\lambda}(V,T)$  is formulated as follows:

<span id="page-15-0"></span>
$$
d_M^{\lambda}(V,T) = \min_{P \in U(V,T)} \sum_{i=1}^{n} \sum_{j=1}^{m} P_{ij} M_{ij} + \frac{1}{\lambda} \left( -\sum_{i=1}^{n} \sum_{j=1}^{m} P_{ij} \log P_{ij} \right) \tag{5}
$$

where  $U(V,T) = \{P \in \mathbb{R}^{n,m}_+ \mid P\mathbf{1}_n = V, P^T\mathbf{1}_m = T\}$  represents all possible transport matrices;  $\mathbf{1}_n$  and  $\mathbf{1}_m$  denote the vectors of ones in dimension n and  $m; \lambda$  is the penalty term associated with the distribution P.  $M_{ij}$  quantifies the difference between the  $i^{th}$  image and the  $j^{th}$  text and is defined as:

$$
M_{ij} = -\frac{\exp(S_{ij})}{\sum_{j} \exp(S_{ij})}
$$
\n(6)

The Sinkhorn-Knopp algorithm [\[22](#page-19-12)] is used to iteratively solve for the optimal solution. As suggested in Eq. [5,](#page-15-0) a smaller  $d_M^{\lambda}(V,T)$  signifies greater similarity among matched image-text pairs while a larger distance indicates a weaker correlation. Therefore, we directly adopt the Wasserstein distance  $d_M^{\lambda}(V,T)$  as the WSL to achieve the fine-grained alignment between image and text:

$$
\mathcal{L}_{WSL} = d_M^{\lambda}(V, T) \tag{7}
$$

## 2.3 Model Optimization

Finally, VertFound employs a dual-loss optimization strategy to enhance the overall vertebrae classification accuracy:

$$
\mathcal{L}_{total} = \mathcal{L}_{CEL} + \mathcal{L}_{WSL} \tag{8}
$$

# 3 Experiment Results

## 3.1 Datasets and Evaluation Metrics

We evaluate the proposed VertFound on an in-house dataset that contains 1233 2D MRI images from 266 patients with a variety of vertebral appearances and

FOVs, and a five-fold cross-validation approach is used for a thorough evaluation. Furthermore, we use identification rate (IDR) and image identification accuracy (IRA) as evaluation metrics. IDR calculates the ratio of vertebrae that are successfully detected, whereas IRA calculates the ratio of images that have all of their vertebrae correctly identified.

## 3.2 Implementation Details

All input images are resized to  $224 \times 224$  and  $1024 \times 1024$  and sent to CLIP and SAM image encoders via frozen ViT-L/14 and ViT-B/16, respectively. During training, the annotated bounding boxes are input to SAM, while in the testing phase the bounding boxes are predicted by a pre-trained detector (YOLOv8). The frozen text encoder of CLIP is employed to extract text features from a total of 25 category names of vertebrae (e.g., S, L5, L4, ... C1). The AdamW optimizer is employed with an initial learning rate of  $2.5 \times 10^{-5}$ , following a warm-up multi-step schedule with weight decay of 0.0001, Adam momentum of 0.9, and batch size of 40. Our methods are implemented in Python using the PyTorch framework and trained on an NVIDIA GTX 1080 Ti GPU. The CEL and WSL are all used for classification optimization, and the loss weights are all empirically set to 1.

Image /page/16/Figure/4 description: The image displays five radar charts, labeled (a) wo. CLIP, (b) wo. SAM2CLIP, (c) wo. CLIP2SAM, (d) wo. WSL, and (e) Full model. Each chart has a circular grid with radial lines extending from the center. The radial lines are marked with numbers from 0 to 24, representing hours. Multiple colored lines emanate from the center of each chart, extending outwards along these radial lines, indicating data points at different hours. The lengths of these lines vary, suggesting different magnitudes or frequencies associated with each hour. The charts are arranged horizontally, with the labels positioned below each respective chart.

<span id="page-16-0"></span>**Fig. 4.** Confusion stars show the classification errors of different ablation studies. Specifically, the circle is divided into 25 regions, each representing a distinct class. Within each region, there are 24 subsections corresponding to the other classes, indicating the number of classification errors for each particular class.

## 3.3 Results

**Comparison with Competing Methods.** As shown in Table [1,](#page-17-0) in-depth comparisons with current methods are carried out to evaluate the effectiveness of VertFound. All experiments adopt identical experimental settings to ensure fairness and reliability in the comparative evaluation. Our baselines include classical object detection algorithms (i.e., DETR [\[23](#page-19-13)], YOLOv5 [\[24\]](#page-19-14), and YOLOv8 [\[25\]](#page-19-15)) and recent foundation models (i.e., GLIPv1 [\[13](#page-19-3)] and OWL-ViT [\[26](#page-19-16)]). VertFound demonstrates superior performance in both IDR and IRA, which underscores its improved capability for fine-grained vertebrae classification.

| Methods          | $IDR(\%)$        | $IRA(\%)$        |
|------------------|------------------|------------------|
| DETR $[23]$      | $75.58 \pm 5.39$ | $46.60 \pm 4.26$ |
| YOLOv $5$ [24]   | $76.46 \pm 2.29$ | $54.30 \pm 3.27$ |
| YOLOv $8$ [25]   | $77.21 + 1.49$   | $55.43 \pm 2.46$ |
| $GLIPv1$ [13]    | $74.31 + 2.63$   | $47.87 \pm 2.19$ |
| OWL-ViT [26]     | $76.68 + 3.15$   | $54.81 \pm 3.56$ |
| Ours (VertFound) | $89.05 \pm 5.82$ | $83.73 \pm 4.46$ |

<span id="page-17-0"></span>**Table 1.** Comparison with Competing Methods.

#### <span id="page-17-1"></span>**Table 2.** Ablation study Results.

<span id="page-17-2"></span>

| Methods                                            | $IDR(\%)$                          | $IRA(\%)$ |
|----------------------------------------------------|------------------------------------|-----------|
| wo.CLIP                                            | $76.31 \pm 7.08$ 64.86 $\pm$ 3.62  |           |
| wo. CLIP2SAM                                       | $84.15 \pm 2.46$ $ 80.18 \pm 3.69$ |           |
| wo. SAM2CLIP                                       | $61.64 \pm 2.12$ 65.26 $\pm$ 4.89  |           |
| wo. WSL                                            | $82.50 \pm 4.51$ 75.97 $\pm 4.38$  |           |
| Ours (VertFound) 89.05 $\pm$ 5.82 83.73 $\pm$ 4.46 |                                    |           |

Image /page/17/Figure/5 description: The image displays three heatmaps in the top row and three corresponding heatmaps in the bottom row, labeled (a) Easy case, (b) Medium case, and (c) Hard case. Each heatmap represents 'Vertebrae Regions' on the y-axis and 'Vertebrae Categories' on the x-axis. The top row heatmaps show distinct patterns of colored squares. The first heatmap (left) has yellow squares at approximately (10,10), (11,11), (12,12), (13,13), (14,14), and a single yellow square at (24,24). The second heatmap (middle) shows a similar pattern of yellow squares from (5,5) to (9,9). The third heatmap (right) displays a diagonal pattern of colored squares ranging from yellow to green and blue, indicating varying values. The bottom row heatmaps show the results for the different cases. The 'Easy case' heatmap is entirely yellow. The 'Medium case' heatmap is predominantly yellow with a diagonal line of lighter yellow squares from approximately (7,7) to (10,10). The 'Hard case' heatmap is mostly green, with a single yellow square at approximately (6,6). A color bar on the right indicates values ranging from 0.0 to 0.6.

**Fig. 5.** Different examples show the effectiveness of WSL in improving the discriminative capability of image-text alignment.

**Ablation Studies.** We conducted thorough ablation studies to verify the efficacy of the crucial components in VertFound. As presented in Table [2,](#page-17-1) removing CLIP (relying solely on SAM for classification) results in a noticeable decrease in classification performance. Meanwhile, removing either the CLIP2SAM or SAM2CLIP component in the VPS module also shows a noticeable decrease in both IDR and IRA, which highlights the significance of the aggregation between global and local features for precise vertebral positioning. Besides, the confusion star [\[27\]](#page-19-17) in Fig. [4](#page-16-0) vividly illustrates the classification errors arising from different ablation methods. Furthermore, the removal of WSL also leads to a decline in overall performance. Figure [5](#page-17-2) further elucidates that the incorporation of WSL optimizes the image-text alignment scores with varying degrees, thereby enhancing the discrimination against fine-grained vertebral features.

# 4 Conclusion

In this paper, we propose VertFound, which combines semantic and spatial understanding to achieve fine-grained classification of vertebrae. We introduce a novel VPS module that leverages the complementary strengths of CLIP and SAM for enriching vertebral feature representations. A WSL is introduced to enhance the discriminative alignment capability of CLIP for fine-grained vertebrae classification. Empirical validation shows the efficacy of VertFound while demonstrating the remarkable potential of foundation models for fine-grained recognition tasks. In the future, we will extend our experiments to include additional datasets from different sources and modalities to test the generalization capabilities of our method, including 3D MRI and CT scans.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# **References**

- <span id="page-18-0"></span>1. Liao, H., Mesfin, A., Luo, J.: Joint vertebrae identification and localization in spinal CT images by combining short-and long-range contextual information. IEEE Trans. Med. Imaging **37**(5), 1266–1275 (2018)
- <span id="page-18-1"></span>2. Shen Zhao, Bin Chen, Heyou Chang, Bo Chen, and Shuo Li. Reasoning discriminative dictionary-embedded network for fully automatic vertebrae tumor diagnosis. Med. Image Anal. **79**, 102456 (2022)
- <span id="page-18-2"></span>3. Windsor, R., Jamaludin, A., Kadir, T., Zisserman, A.: A convolutional approach to vertebrae detection and labelling in whole spine MRI. In: Martel, A.L., et al. (ed.) Medical Image Computing and Computer Assisted Intervention - MICCAI 2020, MICCAI 2020, Part VI, LNCS, vol. 12266, pp. 712–722. Springer, Cham (2020). [https://doi.org/10.1007/978-3-030-59725-2](https://doi.org/10.1007/978-3-030-59725-2_69) 69
- <span id="page-18-3"></span>4. Yang, D., et al.: Automatic vertebra labeling in large-scale 3D CT using deep image-to-image network with message passing and sparsity regularization. In: Niethammer, M., et al. Information Processing in Medical Imaging, IPMI 2017, LNCS, vol. 10265, pp. 633–644. Springer, Cham (2017). [https://doi.org/10.1007/](https://doi.org/10.1007/978-3-319-59050-9_50) [978-3-319-59050-9](https://doi.org/10.1007/978-3-319-59050-9_50) 50
- <span id="page-18-4"></span>5. Wang, F., Zheng, K., Lu, L., Xiao, J., Wu, M., Miao, S.: Automatic vertebra localization and identification in CT by spine rectification and anatomically-constrained optimization. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 5280–5288 (2021)
- <span id="page-18-5"></span>6. Cui, Z., et al.: VertNet: accurate vertebra localization and identification network from CT images. In: de Bruijne, M., et al. Medical Image Computing and Computer Assisted Intervention - MICCAI 2021, MICCAI 2021, Part V, LNCS, vol. 12905, pp. 281–290. Springer, Cham (2021). [https://doi.org/10.1007/978-3-030-](https://doi.org/10.1007/978-3-030-87240-3_27) [87240-3](https://doi.org/10.1007/978-3-030-87240-3_27) 27
- <span id="page-18-6"></span>7. Wu, H., et al.: Multi-view vertebra localization and identification from CT images. In: Greenspan, H., et al. (ed.) Medical Image Computing and Computer Assisted Intervention - MICCAI 2023, MICCAI 2023, LNCS, vol. 14224, pp. 136–145. Springer, Cham (2023). [https://doi.org/10.1007/978-3-031-43904-9](https://doi.org/10.1007/978-3-031-43904-9_14.) 14.
- <span id="page-18-7"></span>8. Radford, A., et al.: Learning transferable visual models from natural language supervision. In: International Conference on Machine Learning, pp. 8748–8763. PMLR (2021)
- <span id="page-18-8"></span>9. Kirillov, A., et al.: Segment anything. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)

- <span id="page-19-0"></span>10. Liu, J., et al.: Clip-driven universal model for organ segmentation and tumor detection. In: Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 21152–21164 (2023)
- <span id="page-19-1"></span>11. Guo, M., Yi, H., Qin, Z., Wang, H., Men, A., Lao, Q.: multiple prompt fusion for zero-shot lesion detection using vision-language models. In: Greenspan, H., et al. Medical Image Computing and Computer Assisted Intervention - MICCAI 2023, MICCAI 2023, LNCS, vol. 14224, pp. 283–292. Springer, Cham (2023). [https://](https://doi.org/10.1007/978-3-031-43904-9_28) [doi.org/10.1007/978-3-031-43904-9](https://doi.org/10.1007/978-3-031-43904-9_28) 28
- <span id="page-19-2"></span>12. Qin, Z., Yi, H., Lao, Q., Li, K.: Medical image understanding with pretrained vision language models: a comprehensive study. [arXiv:2209.15517](http://arxiv.org/abs/2209.15517) (2022)
- <span id="page-19-3"></span>13. Li, L.H., et al.: Grounded language-image pre-training. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 10965– 10975 (2022)
- <span id="page-19-4"></span>14. Cheng, J., et al.: Sam-med2d. arXiv preprint [arXiv:2308.16184](http://arxiv.org/abs/2308.16184) (2023)
- <span id="page-19-5"></span>15. Wang, H., et al.: Sam-med3d. arXiv preprint [arXiv:2310.15161](http://arxiv.org/abs/2310.15161) (2023)
- <span id="page-19-6"></span>16. Park, N., Kim, W., Heo, B., Kim, T., Yun, S.: What do self-supervised vision transformers learn? arXiv preprint [arXiv:2305.00729](http://arxiv.org/abs/2305.00729) (2023)
- <span id="page-19-7"></span>17. Wang, H., et al.: Sam-clip: merging vision foundation models towards semantic and spatial understanding. arXiv preprint [arXiv:2310.15308](http://arxiv.org/abs/2310.15308) (2023)
- <span id="page-19-8"></span>18. Coquenet, D., Rambour, C., Dalsasso, E., Thome, N.: Leveraging visionlanguage foundation models for fine-grained downstream tasks. arXiv preprint [arXiv:2307.06795](http://arxiv.org/abs/2307.06795) (2023)
- <span id="page-19-9"></span>19. Vaswani, A., et al.: Attention is all you need. In: Advances in Neural Information Processing Systems, vol. 30 (2017)
- <span id="page-19-10"></span>20. Lin, T.Y., Doll´ar P., Girshick, R., He, K., Hariharan, B., Belongie, S.: Feature pyramid networks for object detection. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (2017)
- <span id="page-19-11"></span>21. Asano, Y.M., Rupprecht, C., Vedaldi, A.: Self-labelling via simultaneous clustering and representation learning. arXiv preprint [arXiv:1911.05371](http://arxiv.org/abs/1911.05371) (2019)
- <span id="page-19-12"></span>22. Knight, P.A.: The sinkhorn–knopp algorithm: convergence and applications. SIAM J. Matrix Anal. Appl. **30**(1), 261–275 (2008)
- <span id="page-19-13"></span>23. Carion, N., Massa, F., Synnaeve, G., Usunier, N., Kirillov, A., Zagoruyko, S.: End-to-end object detection with transformers. In: Vedaldi, A., Bischof, H., Brox, T., Frahm, JM. (eds.) Computer Vision - ECCV 2020, ECCV 2020, LNCS, vol. 12346, pp. 213–229. Springer, Cham (2020). [https://doi.org/10.1007/978-3-030-](https://doi.org/10.1007/978-3-030-58452-8_13) [58452-8](https://doi.org/10.1007/978-3-030-58452-8_13) 13
- <span id="page-19-14"></span>24. Jocher, G., et al.: ultralytics/yolov5: v7.0 - YOLOv5 SOTA realtime instance segmentation, November 2022.
- <span id="page-19-15"></span>25. Jocher, G., Chaurasia, A., Qiu, J.: Ultralytics YOLO, January 2023
- <span id="page-19-16"></span>26. Minderer, M., et al.: Simple Open-Vocabulary Object Detection. In: Avidan, S., Brostow, G., Cissé, M., Farinella, G.M., Hassner, T. (eds.) Computer Vision -ECCV 2022, ECCV 2022, LNCS, vol. 13670, pp. 728–755. Springer, Cham (2022). [https://doi.org/10.1007/978-3-031-20080-9](https://doi.org/10.1007/978-3-031-20080-9_42) 42
- <span id="page-19-17"></span>27. Luque, A., Mazzoleni, M., Carrasco, A., Ferramosca, A.: Visualizing classification results: confusion star and confusion gear. IEEE Access **10**, 1659–1677 (2021)

Image /page/20/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a ribbon or flag shape inside, pointing to the right. The text below the icon reads "Check for updates" in a light gray sans-serif font. The button itself is a light gray color with a subtle gradient, giving it a slightly raised appearance.

# XCoOp: Explainable Prompt Learning for Computer-Aided Diagnosis via Concept-Guided Context Optimization

Yequan Bie<sup>1</sup>, Luyang Luo<sup>1</sup>, Zhixuan Chen<sup>1</sup>, and Hao Chen<sup>1,2( $\boxtimes$ )</sup>

<sup>1</sup> The Hong Kong University of Science and Technology, Hong Kong, China {ybie,zchenhi}@connect.ust.hk, <EMAIL> <sup>2</sup> HKUST Shenzhen-Hong Kong Collaborative Innovation Research Institute, Shenzhen, China

<EMAIL>

Abstract. Utilizing potent representations of the large vision-language models (VLMs) to accomplish various downstream tasks has attracted increasing attention. Within this research field, soft prompt learning has become a representative approach for efficiently adapting VLMs such as CLIP, to tasks like image classification. However, most existing prompt learning methods learn text tokens that are unexplainable, which cannot satisfy the stringent interpretability requirements of Explainable Artificial Intelligence (XAI) in high-stakes scenarios like healthcare. To address this issue, we propose a novel explainable prompt learning framework that leverages medical knowledge by aligning the semantics of images, learnable prompts, and clinical concept-driven prompts at multiple granularities. Moreover, our framework addresses the lack of valuable concept annotations by eliciting knowledge from large language models and offers both visual and textual explanations for the prompts. Extensive experiments and explainability analyses conducted on various datasets, with and without concept labels, demonstrate that our method simultaneously achieves superior diagnostic performance, flexibility, and interpretability, shedding light on the effectiveness of foundation models in facilitating XAI. The code is available at [https://github.com/Tommy-Bie/XCoOp.](https://github.com/Tommy-Bie/XCoOp)

Keywords: Prompt Learning · XAI · Multi-modal ML · LLM · VLM

# 1 Introduction

In the era of foundation models (FMs), large-scale vision-language pre-trained models (VLMs) such as CLIP [\[24](#page-29-0)], BLIP [\[18](#page-29-1)], Flamingo [\[2\]](#page-28-0), ALIGN [\[13](#page-29-2)], CoCa [\[29](#page-30-0)] have underscored their potential in representation learning, excelling at vision and language understanding. However, the massive sizes and expensive

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_72.](https://doi.org/10.1007/978-3-031-72390-2_72)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 773–783, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_72)\_72

training costs have prompted studies to explore ways to efficiently adapt the knowledge of pre-trained VLMs to downstream tasks. Recently, prompt learning from the field of natural language processing has been introduced to the vision domain [\[30,](#page-30-1)[31](#page-30-2)], achieving great success in adapting large-scale VLMs to downstream tasks like image classification and segmentation [\[19](#page-29-3)[,21](#page-29-4)]. These methods fix the parameters of the models and train the learnable tokens that serve as the input of the text encoder (i.e., context optimization), significantly reducing the cost of utilizing foundation models. Nevertheless, existing prompt learning methods result in unexplainable learned tokens. This lack of interpretability prevents further application of prompt learning from being applied to highstakes domains with rigorous demands of trustworthiness, such as healthcare [\[6](#page-28-1),[20,](#page-29-5)[22,](#page-29-6)[27](#page-30-3)]. Specifically, the models applied to the healthcare domain should not only perform well but also need to be understandable and trustworthy to practitioners, necessitating research into Explainable Artificial Intelligence (XAI). Several prior studies have introduced knowledge to prompt learning [\[4,](#page-28-2)[28\]](#page-30-4). For instance, Yao et al. [\[28](#page-30-4)] adopt human knowledge (*a photo of a* [*class name*]) as hard prompts to guide the learning of soft prompts at the global level. However, the insufficient knowledge and inadequate guidance still lead to non-interpretable prompt learning. To address the explainability challenge of current methods, we propose XCoOp, a novel eXplainable prompt learning framework for medical image analysis via concept-guided Context Optimization, which leverages medical knowledge by aligning the semantics of the images, learnable prompts, and clinical concept-driven prompts at multiple granularities, making each token of soft prompts more informative and explainable guided by clinical concepts of corresponding diseases. Furthermore, our framework addresses the lack of valuable concept annotations by eliciting knowledge from large language models and offers both visual and textual explanations for learned prompts.

We summarize our main contributions as follows: (i) We propose XCoOp, a novel explainable prompt learning framework that leverages concept-based medical knowledge at multiple granularities to make the prompts more explainable. To the best of our knowledge, this is the first work to explore addressing the lack of interpretability of prompt learning methods in healthcare. (ii) We demonstrate that our method can be flexibly applied to various datasets with or without concept annotations, alleviating the requirement of human labor by eliciting medical knowledge from LLMs. (iii) Extensive experiments and explainability analyses show that our method simultaneously achieves promising performance and interpretability, highlighting the effectiveness of foundation model-enhanced XAI.

# 2 Method

Figure [1](#page-22-0) presents the overall architecture of our explainable prompt learning framework for computer-aided diagnosis. Specifically, we initialize the soft prompts with "*a photo of a* [*disease name*]", and the clinical prompts are created based on the medical concepts (Sect. [2.1\)](#page-22-1). The text features of the prompts are extracted using a pre-trained text encoder, and a multi-granularity prompt

<span id="page-22-0"></span>Image /page/22/Figure/1 description: The image illustrates a two-part diagram detailing a method for LLM-based clinical prompt design and multi-granularity soft-hard prompt alignment. The top section, 'LLM-based Clinical Prompt Design,' shows a question about distinguishing melanoma in an image, feeding into an LLM which generates concepts like irregular dots and globules, irregular pigmentation, and irregular vascular structures. These concepts are then tokenized into a clinical prompt. The bottom section, 'Multi-Granularity Soft-Hard Prompt Alignment & Global-Local Image-Prompt Alignment,' depicts a clinical prompt and a soft prompt being processed by text encoders. An input image is processed by an image encoder. These outputs are then aligned at the token, prompt, and image-prompt levels. The final part of the diagram shows a fusion of these aligned representations, leading to a classification output labeled 'Melanoma,' with accompanying bar charts indicating confidence levels. Icons indicate that the text encoder and image encoder are frozen, while the alignment and fusion processes are trainable.

Fig. 1. The overall pipeline of XCoOp. The key insight of XCoOp is enhancing the informativeness and explainability of the soft prompts under the guidance of conceptbased medical knowledge at multiple granularities, achieving FM-enhanced XAI.

alignment module is proposed to align the semantics of the soft prompts and the clinical prompts (Sect. [2.2\)](#page-22-2). The final disease diagnosis is performed by measuring the similarity between the text features of soft prompts and the image features at both global and local levels (Sect. [2.3\)](#page-23-0).

<span id="page-22-1"></span>

## 2.1 Clinical Concept-Driven Prompt Design

To introduce medical knowledge into the prompt learning process, we first design disease-specific prompts using clinical concepts. Figure [1](#page-22-0) shows the steps of creating clinical prompts in our framework. Specifically, for medical datasets with concept annotations (e.g., Derm7pt [\[15](#page-29-7)], SkinCon [\[7](#page-28-3)]), we can easily create clinical prompts based on the labels annotated by medical experts. An example clinical prompt for melanoma in a dermoscopic image is *a photo of melanoma, with irregular pigment network, dots and globules, blue-whitish veil, and vascular structures*. For the datasets lacking explicit concept annotations, we elicit medical knowledge from a large language model such as GPT4 [\[1](#page-28-4)] and create the corresponding clinical prompts. A sample query used to prompt the LLM is *"What are the most useful visual concepts to distinguish* [*disease name*] *in a* {*dermoscopic image, chest X-ray, etc.*}*?"*.

<span id="page-22-2"></span>

## 2.2 Soft-Hard Prompt Alignment

To enhance the informativeness and explainability of the soft prompts by incorporating clinical semantics, we introduce a soft-hard prompt alignment module that aligns prompts at both the prompt level and token level. Prompt-level alignment facilitates the model to learn correspondences between soft prompts and clinical (hard) prompts from a global disease perspective, exploiting the intrinsic information captured by the pre-trained text encoder. The token-level alignment focuses on a more fine-grained local level. Since each token embedding of the clinical prompts is obtained by tokenizing the original concept-based prompts, the alignment enforces the soft prompts to be close to the clinical prompts in the embedding space, aiming to make each token of soft prompts more informative and explainable guiding by clinical concepts of corresponding diseases, hence enhancing the effectiveness and interpretability of the prompt learning framework.

Token-Level Alignment. Given the token embeddings of soft prompts  $V \in$  $\mathbb{R}^{D\times C\times dim}$  and clinical prompts  $Q \in \mathbb{R}^{D\times C\times dim}$  for different classes, we first align their embeddings at the token level via contrastive learning, where D, C, dim represent the number of classes, the context length, and dimension of embedding, respectively. A probability distribution over the class labels is given by:

$$
P(y_d|V_d) = \frac{\exp(\cos(Q_d, V_d)/\tau)}{\sum_{k=1}^D \exp(\cos(Q_k, V_d)/\tau)},
$$
\n(1)

where  $y_d$  is the binary label of class d,  $cos(\cdot, \cdot)$  is the cosine similarity, and  $\tau$  is a temperature parameter. The token-level alignment objective  $\mathcal{L}_T$  is optimized by minimizing the cross-entropy loss:

$$
\mathcal{L}_T = -\sum_{k=1}^D y_k \log P(y_k | V_d). \tag{2}
$$

**Prompt-Level Alignment.** Given the pre-trained text encoder  $q(\cdot)$ , we align the text features of soft prompts and clinical prompts at the global prompt level by minimizing the following objective function:

$$
\mathcal{L}_P = \sum_{d=1}^D CE\left(\frac{\exp(\cos(g(Q_d), g(V_d)))}{\sum_{k=1}^D \exp(\cos(g(Q_k), g(V_d))/\tau)}, y_d\right),\tag{3}
$$

where  $\mathcal{L}_P$  represents the prompt-level alignment loss,  $CE(\cdot)$  denotes the crossentropy loss,  $q(V)$  and  $q(Q) \in \mathbb{R}^{D \times dim}$  denote the output text features of soft prompts  $V$  and clinical prompts  $Q$ , respectively. The overall objective of the soft-hard prompt alignment module  $\mathcal{L}_{PPA}$  is the average of  $\mathcal{L}_T$  and  $\mathcal{L}_P$ .

<span id="page-23-0"></span>

## 2.3 Global-Local Image-Prompt Alignment

Medical diagnosis typically hinges on various clinical symptoms observable within specific, localized regions in an image. Given that different clinical concepts may correspond to distinct sub-regions of a medical image, we employ a global-local image-prompt alignment module to align the medical images and clinical concept-driven prompts at multiple levels. Specifically, as illustrated in Fig. [1,](#page-22-0) given an image x and the pre-trained image encoder of CLIP  $[24]$  $[24]$ , we obtain the global visual feature p and a set of local features  $F = \{f_1, f_2, ..., f_M\},\$ where  $M$  is the number of local (patch) features. The final prediction probability is computed by the matching scores of both global and local features, and the alignment can be optimized using cross-entropy loss which estimates the discrepancy between the predicted diagnosis results and the ground truth:

$$
\mathcal{L}_{\text{IPA}} = CE[\cos(p, g(V_d)) + \lambda \frac{1}{M} (\sum_{m=1}^{M} \cos(f_m, g(V_d))), y_d], \tag{4}
$$

where  $\mathcal{L}_{IPA}$  represents the image-prompt alignment loss,  $\lambda$  is the weight of the prediction of local features. The overall training objective is represented as  $\mathcal{L} =$  $\mathcal{L}_{\text{PPA}}+\lambda'\mathcal{L}_{\text{IPA}},$  where  $\lambda'$  is a loss-balancing factor. The global-local image-prompt<br>alignment module encourages the model to mimic the process wherein medical alignment module encourages the model to mimic the process wherein medical experts utilize both global and local information to diagnose disease.

# 3 Experiments

## 3.1 Experimental Setup

**Datasets:** *Derm7pt* [\[15](#page-29-7)] is a dermoscopic image dataset contains 1011 images with clinical concepts for melanoma skin lesions in dermatology. *SkinCon* [\[7\]](#page-28-3) is a skin disease dataset densely annotated by experts for fine-grained model debugging and analysis. The concepts of *Derm7pt* and the *F17k* part of *SkinCon* are used to design clinical prompts for these two datasets. *Pneumonia* [\[16](#page-29-8)] is a public dataset for classifying pneumonia cases from normal ones, with 1583 normal and 4273 pneumonia images. *IU X-Ray* [\[8](#page-28-5)] is a chest X-ray dataset with 3,955 radiology reports, corresponding to 7,470 frontal and lateral images. We filter out the lateral x-ray, leaving only frontal images.

Implementation Details: Our framework adopted the pre-trained visual (ViT- $B/16$ ) and text encoder of CLIP [\[24\]](#page-29-0). We adopted SGD [\[26\]](#page-29-9) optimizer with learning rate of 0.032. We used warm-up and cosine anneal as training strategies. All methods implemented in this paper adopted random crop and random flip for data augmentation. Grid search was used to select hyperparameters, we set  $\tau = 0.9, \lambda = 0.1$ . All experiments were conducted on an RTX 3090 GPU.

## 3.2 Experimental Results

In order to comprehensively demonstrate the competitive performance of our method in disease diagnosis, we commence by comparing with other state-ofthe-art prompt learning methods on four datasets. Subsequently, we undertake intensive ablation experiments to assess the effectiveness of our method. Finally, we evaluate the explainability of our framework using multiple XAI metrics.

| <b>METHOD</b>                                                                                                                                                                             |            | Derm7pt | $\operatorname{SkinCon}$ |           | Pneumonia  |           | IU X-Ray                                                                                                                                                                                                                        |                                                                                                                                              |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|---------|--------------------------|-----------|------------|-----------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                                                                           | <b>AUC</b> | ACC     | <b>AUC</b>               | $\bf ACC$ | <b>AUC</b> | $\bf ACC$ | <b>AUC</b>                                                                                                                                                                                                                      | ACC                                                                                                                                          |
| CLIP $[24]$                                                                                                                                                                               | 50.00      | 69.11   | 39.68                    | 70.29     | 50.00      | 62.52     | 47.90                                                                                                                                                                                                                           | 13.21                                                                                                                                        |
| $CoOp$ [31]                                                                                                                                                                               |            |         |                          |           |            |           | $\left[71.76_{0.1} \right]$ $\left[75.19_{0.4} \right]$ $\left[77.52_{0.4} \right]$ $\left[75.91_{0.7} \right]$ $\left[84.08_{0.6} \right]$ $\left[85.88_{0.6} \right]$ $\left[78.45_{1.2} \right]$ $\left[71.93_{0.7} \right]$ |                                                                                                                                              |
| CoCoOp [30] 70.40 <sub>0.4</sub> 77.04 <sub>0.7</sub>   78.02 <sub>0.5</sub> 76.19 <sub>0.8</sub>   85.96 <sub>0.4</sub> 86.06 <sub>0.8</sub>   76.00 <sub>1.6</sub> 70.63 <sub>0.5</sub> |            |         |                          |           |            |           |                                                                                                                                                                                                                                 |                                                                                                                                              |
| KgCoOp [28] 69.67 <sub>2.7</sub> 73.84 <sub>1.4</sub> 75.33 <sub>0.3</sub> 76.95 <sub>0.5</sub> 80.95 <sub>0.3</sub> 82.64 <sub>0.3</sub> 75.61 <sub>1.2</sub> 70.74 <sub>1.2</sub>       |            |         |                          |           |            |           |                                                                                                                                                                                                                                 |                                                                                                                                              |
| LASP $ 4 $                                                                                                                                                                                |            |         |                          |           |            |           | $\left[75.08_{0.6}$ 76.20 <sub>1.6</sub> $\left[78.31_{0.3}$ 77.33 <sub>0.8</sub> $\left[91.31_{0.1}$ $92.41_{0.1}\right]83.69_{0.3}$ 76.46 <sub>0.7</sub>                                                                      |                                                                                                                                              |
| XCoOp                                                                                                                                                                                     |            |         |                          |           |            |           |                                                                                                                                                                                                                                 | $\{78.43_{ 0.6} \, 78.82_{ 1.0} \vert 81.12_{ 0.3} \, 78.57_{ 0.6} \vert 92.85_{ 0.3} \, 93.80_{ 0.3} \vert 84.91_{ 0.6} \, 78.44_{ 0.9} \}$ |

<span id="page-25-0"></span>Table 1. Quantitative comparison on disease diagnosis with the state-of-the-art prompt learning methods. The performance is reported as mean<sub>std</sub> of three random runs. Our method is highlighted in light cyan, and the best results are shown in bold.

Diagnosis Results. In Table [1,](#page-25-0) we report the disease diagnosis comparison results of our method using AUROC and Accuracy on four medical image datasets. We include the CLIP baseline [\[24](#page-29-0)] without any tuning (the first row), two CoOp-based methods (CoOp [\[31\]](#page-30-2) and CoCoOp [\[30](#page-30-1)]), and two knowledgeguided prompt learning methods (KgCoOp [\[28\]](#page-30-4) and LASP [\[4](#page-28-2)]). Our method outperforms other state-of-the-art prompt learning methods with a significant margin, especially achieving 1.2%–3.4% AUC and 1.2%–2.0% accuracy improvement compared to the second-best results on all considered datasets, which demonstrates that the full utilization of medical knowledge and the global-local correlations between images and prompts effectively encourages the soft prompts to learn clinical semantics, thus benefiting the performance of our model.

Ablation Study. We conduct various ablation studies on *SkinCon* to investigate the influence of different modules and settings. In Table [2,](#page-25-1) we assess the

<span id="page-25-1"></span>Table 2. Ablation study of XCoOp on disease diagnosis (AUC [%]). CCP, IPA, and PPA represent the clinical conceptdriven prompts, image-prompt alignment, and soft-hard prompt alignment modules, respectively.

| Method              | AUC                             |
|---------------------|---------------------------------|
| Baseline (LASP [4]) | $78.31_{0.3}$                   |
| CCP                 | $79.93_{0.4}$                   |
| CCP + IPA           | $80.46_{0.7}$                   |
| CCP + IPA + PPA     | <b><math>81.12_{0.3}</math></b> |

Image /page/25/Figure/7 description: The image is a line graph showing the Area Under the Curve (AUC) in percentage on the y-axis against the number of Epochs on the x-axis. Four lines represent different models: ViT-B/16 (blue), ViT-B/32 (orange), ResNet-101 (green), and ResNet-50 (red). The graph shows the performance of these models over 50 epochs. At 1 epoch, ViT-B/16 has an AUC of approximately 39%, ViT-B/32 has an AUC of approximately 45%, ResNet-101 has an AUC of approximately 53%, and ResNet-50 has an AUC of approximately 54%. By 10 epochs, ViT-B/16 reaches about 77%, ViT-B/32 reaches about 75%, ResNet-101 reaches about 64%, and ResNet-50 reaches about 67%. At 50 epochs, ViT-B/16 and ViT-B/32 both reach an AUC of approximately 81%, ResNet-101 reaches about 69%, and ResNet-50 reaches about 70%.

Fig. 2. Ablation study on the number of training epochs of XCoOp with different vision backbones.

<span id="page-26-0"></span>Image /page/26/Figure/1 description: The image displays two figures. Figure (a) illustrates prompt examples for knowledge intervention, showing a progression from general knowledge about melanoma to clinical-concept-based knowledge with specific features like pigment network, irregular dots, and globules. An intervention step modifies the prompt to include 'typical pigment network, regular dots and globules'. Figure (b) is a bar chart titled 'Knowledge intervention' that plots AUC (%) on the y-axis against different knowledge interventions on the x-axis. The x-axis categories are 'W/O Kg', 'Random Kg', 'General Kg', 'Intervened Kg', and 'Concept Kg'. The corresponding AUC values are approximately 74.2%, 74.7%, 77.2%, 74.7%, and 79.3%.

Fig. 3. Illustration of our model's faithfulness using knowledge intervention. (a) Clarification of prompt examples based on different knowledge and intervention. (b) The results of concept-based knowledge (Kg) intervention on Derm7pt, where x-axis represents different kinds of prompts and y-axis represents the AUC [%], respectively.

effectiveness of each module in our proposed framework. Specifically, we show that our method can benefit from all the components, including the clinical concept-driven prompts, the soft-hard prompt alignment, and the global-local image-prompt alignment. The last configuration of Table [2](#page-25-1) demonstrates that our method achieves the best overall performance with all designed components. To explore the influence of different numbers of training epochs and vision backbones, we conduct an ablation study and report the AUC in Fig. [2.](#page-25-1) The results show that our method can converge quickly with different vision backbones (e.g., ViT [\[9](#page-29-10)], ResNet [\[11](#page-29-11)]), which demonstrates the high efficiency of our method.

## 3.3 Analysis of Explainability

In order to evaluate the explainability of our proposed method, we analyze our framework using multiple crucial XAI metrics in this section. Specifically, inspired by previous works  $[3,10,12,14,25]$  $[3,10,12,14,25]$  $[3,10,12,14,25]$  $[3,10,12,14,25]$  $[3,10,12,14,25]$  $[3,10,12,14,25]$ , we evaluate our framework from the perspectives of *faithfulness*, *understandability* and *plausibility*.

Faithfulness. *Faithfulness* is defined as the degree to which the explanation reflects the model decision process and requires the explanation to be faithful to the designed model mechanism [\[10](#page-29-12),[17,](#page-29-16)[25](#page-29-15)]. In this paper, we evaluate *faithfulness* by intervening the input clinical concept-driven prompts. As shown in Fig. [3,](#page-26-0) we use five kinds of prompt settings, including without knowledge, with random knowledge (i.e., random words as clinical prompts), with general knowledge (i.e., knowledge without specific clinical concepts), with clinical-concept-based knowledge and the intervened knowledge. Specifically, we adopt *Derm7pt* dataset as an example, as shown in Fig. [3a](#page-26-0), where intervention means modifying some of the concepts in the original clinical prompts and obtaining a new prompt. Figure [3b](#page-26-0) shows that using only random knowledge, general knowledge, or knowledge after intervention as prompts may lead to performance degradation, which demonstrates that the clinical knowledge faithfully explains the model's decisions.

<span id="page-27-0"></span>Table 3. Quantitative comparison on prompt interpretation by measuring distances between the soft prompts and the hand-crafted clinical prompts (i.e., textual explanations). The results are reported as the average distances of different categories. Our method is highlighted in light cyan, and the best results are shown in **bold**.

| <b>Method</b> | Derm7pt      | SkinCon      | Pneumonia    | IU X-ray     | Average ↓    |
|---------------|--------------|--------------|--------------|--------------|--------------|
| KgCoOp [28]   | 2.293        | 1.475        | 1.727        | 2.433        | 1.982        |
| LASP [4]      | 2.936        | 3.867        | 2.270        | 2.972        | 3.011        |
| <b>XCoOp</b>  | <b>1.161</b> | <b>1.139</b> | <b>0.987</b> | <b>1.127</b> | <b>1.104</b> |

<span id="page-27-1"></span>Image /page/27/Picture/3 description: The image displays two rows of images and two scatter plots. The top row shows five images: two close-ups of skin lesions, one of an eye with a ruler, and a chest X-ray. The bottom row shows corresponding heatmaps overlaid on similar images, highlighting areas of interest. To the right, two scatter plots labeled (b) t-SNE are presented. The top scatter plot contains several colored dots (blue, green, orange, purple), and the bottom scatter plot also contains colored dots (yellow, pink, purple, red). The entire figure is captioned "(a) Image-prompt similarity visualization." and "(b) t-SNE."

Fig. 4. Visual explanations. (a) Visualization examples of the similarity between the images and soft prompts. (b) The t-SNE visualization of tokens of different soft prompts of SkinCon (top) and IU X-ray (bottom) datasets. Different colors represent different categories of prompts, and the number of context tokens is 4.

Understandability and Plausibility. *Understandability* requires explanations to be easily understandable by users without requiring technical knowledge [\[14](#page-29-14)] while *plausibility* refers to given domain knowledge, how believable or likely the explanation seems [\[5,](#page-28-7)[10\]](#page-29-12). Our framework achieves *understandability* and *plausibility* by offering both textual and visual explanations. Specifically, we interpret the learnable prompts by measuring the distance between the soft prompts and the hand-crafted clinical prompts. As shown in Table [3,](#page-27-0) we compare the average distances with two knowledge-guided prompt learning methods [\[4,](#page-28-2)[28\]](#page-30-4). Our method outperforms the other methods and achieves the least distance between the learnable prompts and the clinical prompts. For visual explanation, our framework provides the similarity visualization between the medical images and the learnable prompts, as shown in Fig. [4a](#page-27-1), where we can observe that the model focuses more on discriminative concept regions within images guided by our learned prompts. Figure [4b](#page-27-1) presents the t-SNE visualization [\[23\]](#page-29-17) of tokens of different soft prompts and shows that the token embeddings cluster well, demonstrating that tokens in each prompt meticulously learn the discriminative clinical semantics of the corresponding disease category. The explanations offered by our framework enhance human comprehension of the model's decision-making process by elucidating the utilized knowledge and the specific regions of focus, potentially aiding medical experts in utilizing AI models for disease diagnosis.

# 4 Conclusion

In this paper, we propose XCoOp, an explainable prompt learning framework for computer-aided diagnosis, which utilizes medical knowledge by aligning the semantics of images, learnable prompts, and clinical concept-driven prompts at multiple granularities. By adopting the concept-based knowledge eliciting from foundation models to guide the soft prompt at both the token embedding level and prompt level, our method outperforms other prompt learning methods while preserving inherent interpretability with both visual and textual explanations. Extensive experiments and explainability analyses conducted on various datasets demonstrate that our method simultaneously achieves promising performance and interpretability, highlighting the effectiveness of FM-enhanced XAI.

Acknowledgments. This work was supported by the HKUST (Project No. FS111) and Project of Hetao Shenzhen-Hong Kong Science and Technology Innovation Cooperation Zone (HZQB-KCZYB-2020083).

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-28-4"></span>1. Achiam, J., et al.: GPT-4 technical report. arXiv preprint [arXiv:2303.08774](http://arxiv.org/abs/2303.08774) (2023)
- <span id="page-28-0"></span>2. Alayrac, J.B., Donahue, J., Luc, P., Miech, A., Barr, I., Hasson, Y., Lenc, K., Mensch, A., Millican, K., Reynolds, M., et al.: Flamingo: a visual language model for few-shot learning. Advances in Neural Information Processing Systems 35, 23716– 23736 (2022)
- <span id="page-28-6"></span>3. Bie, Y., Luo, L., Chen, H.: Mica: towards explainable skin lesion diagnosis via multi-level image-concept alignment. arXiv preprint [arXiv:2401.08527](http://arxiv.org/abs/2401.08527) (2024)
- <span id="page-28-2"></span>4. Bulat, A., Tzimiropoulos, G.: Lasp: Text-to-text optimization for language-aware soft prompting of vision  $\&$  language models. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 23232–23241 (2023)
- <span id="page-28-7"></span>5. Carvalho, D.V., Pereira, E.M., Cardoso, J.S.: Machine learning interpretability: a survey on methods and metrics. Electronics  $8(8)$ , 832 (2019)
- <span id="page-28-1"></span>6. Chen, Z., Luo, L., Bie, Y., Chen, H.: Dia-llama: Towards large language modeldriven CT report generation. arXiv preprint [arXiv:2403.16386](http://arxiv.org/abs/2403.16386) (2024)
- <span id="page-28-3"></span>7. Daneshjou, R., Yuksekgonul, M., Cai, Z.R., Novoa, R., Zou, J.Y.: Skincon: A skin disease dataset densely annotated by domain experts for fine-grained debugging and analysis. Adv. Neural Inf. Process. Syst. 35, 18157–18167 (2022)
- <span id="page-28-5"></span>8. Demner-Fushman, D., Kohli, M.D., Rosenman, M.B., Shooshan, S.E., Rodriguez, L., Antani, S., Thoma, G.R., McDonald, C.J.: Preparing a collection of radiology examinations for distribution and retrieval. Journal of the American Medical Informatics Association 23(2), 304–310 (2016)

- <span id="page-29-10"></span>9. Dosovitskiy, A., et al.: An image is worth  $16 \times 16$  words: transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-29-12"></span>10. Guidotti, R., Monreale, A., Ruggieri, S., Turini, F., Giannotti, F., Pedreschi, D.: A survey of methods for explaining black box models. ACM computing surveys (CSUR) 51(5), 1–42 (2018)
- <span id="page-29-11"></span>11. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pp. 770–778 (2016)
- <span id="page-29-13"></span>12. Hsiao, J.H.W., Ngai, H.H.T., Qiu, L., Yang, Y., Cao, C.C.: Roadmap of designing cognitive metrics for explainable artificial intelligence (XAI). arXiv preprint [arXiv:2108.01737](http://arxiv.org/abs/2108.01737) (2021)
- <span id="page-29-2"></span>13. Jia, C., et al.: Scaling up visual and vision-language representation learning with noisy text supervision. In: International Conference on Machine Learning, pp. 4904–4916. PMLR (2021)
- <span id="page-29-14"></span>14. Jin, W., Li, X., Fatehi, M., Hamarneh, G.: Guidelines and evaluation of clinical explainable AI in medical image analysis. Med. Image Anal. 84, 102684 (2023)
- <span id="page-29-7"></span>15. Kawahara, J., Daneshvar, S., Argenziano, G., Hamarneh, G.: Seven-point checklist and skin lesion classification using multitask multimodal neural nets. IEEE journal of biomedical and health informatics  $23(2)$ , 538–546 (2018)
- <span id="page-29-8"></span>16. Kermany, D.S., et al.: Identifying medical diagnoses and treatable diseases by image-based deep learning. Cell 172(5), 1122–1131 (2018)
- <span id="page-29-16"></span>17. Lakkaraju, H., Kamar, E., Caruana, R., Leskovec, J.: Faithful and customizable explanations of black box models. In: Proceedings of the 2019 AAAI/ACM Conference on AI, Ethics, and Society, pp. 131–138 (2019)
- <span id="page-29-1"></span>18. Li, J., Li, D., Xiong, C., Hoi, S.: Blip: Bootstrapping language-image pre-training for unified vision-language understanding and generation. In: International Conference on Machine Learning, pp. 12888–12900. PMLR (2022)
- <span id="page-29-3"></span>19. Lin, Y., Nie, D., Liu, Y., Yang, M., Zhang, D., Wen, X.: Multi-target domain adaptation with prompt learning for medical image segmentation. In: Greenspan, H., et al. Medical Image Computing and Computer Assisted Intervention - MIC-CAI 2023, MICCAI 2023, LNCS, vol. 14220, pp. 717–727. Springer, Cham (2023). [https://doi.org/10.1007/978-3-031-43907-0\\_68](https://doi.org/10.1007/978-3-031-43907-0_68)
- <span id="page-29-5"></span>20. Lipton, Z.C.: The doctor just won't accept that! arXiv preprint [arXiv:1711.08037](http://arxiv.org/abs/1711.08037) (2017)
- <span id="page-29-4"></span>21. Lüddecke, T., Ecker, A.: Image segmentation using text and image prompts. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 7086–7096 (2022)
- <span id="page-29-6"></span>22. Luo, L., Huang, X., Wang, M., Wan, Z., Chen, H.: Medical image debiasing by learning adaptive agreement from a biased council. arXiv preprint [arXiv:2401.11713](http://arxiv.org/abs/2401.11713) (2024)
- <span id="page-29-17"></span>23. Van der Maaten, L., Hinton, G.: Visualizing data using t-sne. Journal of machine learning research 9(11), 2579–2605 (2008)
- <span id="page-29-0"></span>24. Radford, A., et al.: Learning transferable visual models from natural language supervision. In: International Conference on Machine Learning, pp. 8748–8763. PMLR (2021)
- <span id="page-29-15"></span>25. Rigotti, M., Miksovic, C., Giurgiu, I., Gschwind, T., Scotton, P.: Attention-based interpretability with concept transformers. In: International Conference on Learning Representations (2021)
- <span id="page-29-9"></span>26. Robbins, H., Monro, S.: A stochastic approximation method. The annals of mathematical statistics 22, 400–407 (1951)

- <span id="page-30-3"></span>27. Rudin, C.: Stop explaining black box machine learning models for high stakes decisions and use interpretable models instead. Nature machine intelligence  $\mathbf{1}(5)$ , 206–215 (2019)
- <span id="page-30-4"></span>28. Yao, H., Zhang, R., Xu, C.: Visual-language prompt tuning with knowledge-guided context optimization. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 6757–6767 (2023)
- <span id="page-30-0"></span>29. Yu, J., Wang, Z., Vasudevan, V., Yeung, L., Seyedhosseini, M., Wu, Y.: Coca: contrastive captioners are image-text foundation models. arxiv 2022. arXiv preprint [arXiv:2205.01917](http://arxiv.org/abs/2205.01917)
- <span id="page-30-1"></span>30. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Conditional prompt learning for visionlanguage models. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 16816–16825 (2022)
- <span id="page-30-2"></span>31. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Learning to prompt for vision-language models. International Journal of Computer Vision 130(9), 2337–2348 (2022)