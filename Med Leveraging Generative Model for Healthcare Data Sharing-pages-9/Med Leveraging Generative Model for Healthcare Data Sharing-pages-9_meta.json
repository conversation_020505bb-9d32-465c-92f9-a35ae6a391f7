{"table_of_contents": [{"title": "UrFound: Towards Universal Retinal\nFoundation Models via Knowledge-Guided\nMasked Modeling", "heading_level": null, "page_id": 0, "polygon": [[77.236328125, 51.5394287109375], [378.125, 51.5394287109375], [378.125, 102.916015625], [77.236328125, 102.916015625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[39.04436860068259, 98.03076171875], [134.40273037542661, 98.03076171875], [134.40273037542661, 109.************], [39.04436860068259, 109.************]]}, {"title": "2 The UrFound Model", "heading_level": null, "page_id": 2, "polygon": [[52.55972696245733, 558.54736328125], [196.044921875, 558.54736328125], [196.044921875, 570.27197265625], [52.55972696245733, 570.27197265625]]}, {"title": "2.1 Knowledge-Guided Masked Modeling", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 359.88037109375], [254.53924914675767, 359.88037109375], [254.53924914675767, 370.30224609375], [38.29351535836177, 370.30224609375]]}, {"title": "2.2 Text Preparation", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 227.97851562500003], [167.1484375, 227.97851562500003], [167.1484375, 239.05175781250003], [52.55972696245733, 239.05175781250003]]}, {"title": "2.3 Multimodal Image Processing", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 369.65087890625], [231.171875, 369.65087890625], [231.171875, 381.37548828125], [52.55972696245733, 381.37548828125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 484.94287109375006], [148.66894197952217, 484.94287109375006], [148.66894197952217, 497.97021484375006], [52.55972696245733, 497.97021484375006]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 54.26702880859375], [166.93359375, 54.26702880859375], [166.93359375, 66.31732177734375], [38.29351535836177, 66.31732177734375]]}, {"title": "3.2 Main Results", "heading_level": null, "page_id": 6, "polygon": [[52.55972696245733, 180.75439453125], [146.845703125, 180.75439453125], [146.845703125, 191.17626953125], [52.55972696245733, 191.17626953125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 313.63330078125], [138.90784982935153, 313.63330078125], [138.90784982935153, 326.00927734375], [52.55972696245733, 326.00927734375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[39.04436860068259, 53.37139892578125], [103.447265625, 53.37139892578125], [103.447265625, 66.56158447265625], [39.04436860068259, 66.56158447265625]]}, {"title": "VertFound: Synergizing Semantic\nand Spatial Understanding\nfor Fine-Grained Vertebrae Classification\nvia Foundation Models", "heading_level": null, "page_id": 10, "polygon": [[79.59044368600682, 50.969482421875], [375.546875, 50.969482421875], [375.546875, 120.17724609375001], [79.59044368600682, 120.17724609375001]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 11, "polygon": [[39.04436860068259, 54.77052868391451], [134.40273037542661, 54.77052868391451], [134.40273037542661, 66.31732177734375], [39.04436860068259, 66.31732177734375]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 12, "polygon": [[51.80887372013652, 499.27294921875006], [150.92150170648463, 499.27294921875006], [150.92150170648463, 510.99755859375006], [51.80887372013652, 510.99755859375006]]}, {"title": "2.1 Vertebral Position Enhancement", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 272.271484375], [229.0102389078498, 272.271484375], [229.0102389078498, 282.693359375], [38.29351535836177, 282.693359375]]}, {"title": "2.2 Fine-Grained Vertebral Classification", "heading_level": null, "page_id": 14, "polygon": [[52.55972696245733, 374.3903262092239], [268.0546075085324, 374.3903262092239], [268.0546075085324, 384.9580078125], [52.55972696245733, 384.9580078125]]}, {"title": "2.3 Model Optimization", "heading_level": null, "page_id": 15, "polygon": [[38.29351535836177, 457.67154105736785], [167.44027303754265, 457.67154105736785], [167.44027303754265, 468.33300781250006], [38.29351535836177, 468.33300781250006]]}, {"title": "3 Experiment Results", "heading_level": null, "page_id": 15, "polygon": [[38.29351535836177, 538.35498046875], [177.67578125, 538.35498046875], [177.67578125, 550.07958984375], [38.29351535836177, 550.07958984375]]}, {"title": "3.1 Datasets and Evaluation Metrics", "heading_level": null, "page_id": 15, "polygon": [[38.9404296875, 562.1298828125], [230.52734375, 562.1298828125], [230.52734375, 572.5517578125], [38.9404296875, 572.5517578125]]}, {"title": "3.2 Implementation Details", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 132.7974853515625], [198.97610921501706, 132.7974853515625], [198.97610921501706, 143.8707275390625], [52.55972696245733, 143.8707275390625]]}, {"title": "3.3 Results", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 485.26855468750006], [117.13310580204778, 485.26855468750006], [117.13310580204778, 496.99316406250006], [52.55972696245733, 496.99316406250006]]}, {"title": "Table 2. Ablation study Results.", "heading_level": null, "page_id": 17, "polygon": [[234.26621160409556, 60.33288574218751], [370.9215017064846, 60.33288574218751], [370.9215017064846, 70.2662353515625], [234.26621160409556, 70.2662353515625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 17, "polygon": [[39.04436860068259, 556.59326171875], [124.64163822525596, 556.59326171875], [124.64163822525596, 568.96923828125], [39.04436860068259, 568.96923828125]]}, {"title": "References", "heading_level": null, "page_id": 18, "polygon": [[51.80887372013652, 205.83203125], [117.13310580204778, 205.83203125], [117.13310580204778, 218.859375], [51.80887372013652, 218.859375]]}, {"title": "XCoOp: Explainable Prompt Learning\nfor Computer-Aided Diagnosis\nvia Concept-Guided Context Optimization", "heading_level": null, "page_id": 20, "polygon": [[78.08873720136518, 50.8880615234375], [378.125, 50.8880615234375], [378.125, 102.1832275390625], [78.08873720136518, 102.1832275390625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 20, "polygon": [[52.55972696245733, 492.43359375000006], [149.208984375, 492.43359375000006], [149.208984375, 506.11230468750006], [52.55972696245733, 506.11230468750006]]}, {"title": "2 Method", "heading_level": null, "page_id": 21, "polygon": [[38.29351535836177, 516.20849609375], [105.8703071672355, 516.20849609375], [105.8703071672355, 528.58447265625], [38.29351535836177, 528.58447265625]]}, {"title": "2.1 Clinical Concept-Driven Prompt Design", "heading_level": null, "page_id": 22, "polygon": [[51.80887372013652, 380.************], [282.************, 380.************], [282.************, 391.4716796875], [51.80887372013652, 391.4716796875]]}, {"title": "2.2 Soft-Hard Prompt Alignment", "heading_level": null, "page_id": 22, "polygon": [[52.55972696245733, 562.1298828125], [228.59375, 562.1298828125], [228.59375, 572.5517578125], [52.55972696245733, 572.5517578125]]}, {"title": "2.3 Global-Local Image-Prompt Alignment", "heading_level": null, "page_id": 23, "polygon": [[38.29351535836177, 537.3779296875], [263.3984375, 537.3779296875], [263.3984375, 548.451171875], [38.29351535836177, 548.451171875]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 24, "polygon": [[52.55972696245733, 267.223388671875], [148.66894197952217, 267.223388671875], [148.66894197952217, 278.947998046875], [52.55972696245733, 278.947998046875]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 24, "polygon": [[52.55972696245733, 292.626708984375], [180.25390625, 292.626708984375], [180.25390625, 303.048583984375], [52.55972696245733, 303.048583984375]]}, {"title": "3.2 Experimental Results", "heading_level": null, "page_id": 24, "polygon": [[52.55972696245733, 516.1934758155231], [188.525390625, 516.1934758155231], [188.525390625, 526.63037109375], [52.55972696245733, 526.63037109375]]}, {"title": "3.3 Analysis of Explainability", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 379.095703125], [208.7372013651877, 379.095703125], [208.7372013651877, 389.517578125], [52.55972696245733, 389.517578125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 28, "polygon": [[52.55972696245733, 97.53655793025872], [138.90784982935153, 97.53655793025872], [138.90784982935153, 109.6739501953125], [52.55972696245733, 109.6739501953125]]}, {"title": "References", "heading_level": null, "page_id": 28, "polygon": [[51.80887372013652, 341.9677734375], [117.13310580204778, 341.9677734375], [117.13310580204778, 354.34375], [51.80887372013652, 354.34375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 44], ["Text", 7], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7514, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Line", 62], ["Span", 56], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 650, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 46], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 42], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["TableCell", 209], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["TableCell", 259], ["Line", 90], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Text", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3687, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 50], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 41], ["Text", 8], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 587, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 52], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 616, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 45], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 64], ["TextInlineMath", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 60], ["TextInlineMath", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 49], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 37], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 659, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["TableCell", 39], ["Line", 38], ["Reference", 3], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 2939, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 46], ["ListItem", 9], ["Reference", 9], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 46], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 38], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 593, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 44], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 70], ["Reference", 3], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 51], ["Text", 3], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 42], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["TableCell", 88], ["Line", 46], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Text", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 13639, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 53], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["TableCell", 48], ["Line", 33], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Picture", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 41], ["ListItem", 8], ["Reference", 8], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 51], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 37], ["Line", 15], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-9"}