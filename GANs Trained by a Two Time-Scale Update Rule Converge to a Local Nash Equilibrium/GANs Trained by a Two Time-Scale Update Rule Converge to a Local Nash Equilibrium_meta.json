{"table_of_contents": [{"title": "GANs Trained by a Two Time-Scale Update Rule\nConverge to a Local Nash Equilibrium", "heading_level": null, "page_id": 0, "polygon": [[123.75, 99.75], [488.8828125, 99.75], [488.8828125, 136.125], [123.75, 136.125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 303.75], [329.25, 303.75], [329.25, 314.982421875], [282.75, 314.982421875]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.5, 539.859375], [186.0, 539.859375], [186.0, 553.0078125], [106.5, 553.0078125]]}, {"title": "Two Time-Scale Update Rule for GANs", "heading_level": null, "page_id": 2, "polygon": [[106.5, 71.25], [350.25, 71.25], [350.25, 83.77294921875], [106.5, 83.77294921875]]}, {"title": "<PERSON> Follows an HBF ODE and Ensures TTUR Convergence", "heading_level": null, "page_id": 3, "polygon": [[107.25, 282.75], [488.25, 282.75], [488.25, 295.83984375], [107.25, 295.83984375]]}, {"title": "Experiments", "heading_level": null, "page_id": 4, "polygon": [[106.5, 639.0], [186.75, 639.0], [186.75, 652.39453125], [106.5, 652.39453125]]}, {"title": "Conclusion", "heading_level": null, "page_id": 9, "polygon": [[107.25, 70.5], [177.75, 70.5], [177.75, 84.3046875], [107.25, 84.3046875]]}, {"title": "Acknowledgment", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 252.333984375], [216.0, 252.333984375], [216.0, 265.869140625], [107.1298828125, 265.869140625]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[106.5, 357.521484375], [175.8603515625, 357.521484375], [175.8603515625, 372.216796875], [106.5, 372.216796875]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 9, "polygon": [[106.5, 429.0], [168.837890625, 429.0], [168.837890625, 444.33984375], [106.5, 444.33984375]]}, {"title": "Contents", "heading_level": null, "page_id": 9, "polygon": [[107.25, 462.0], [155.25, 462.0], [155.25, 474.50390625], [107.25, 474.50390625]]}, {"title": "A1 Fréchet Inception Distance (FID)", "heading_level": null, "page_id": 10, "polygon": [[106.5, 72.75], [303.908203125, 72.75], [303.908203125, 83.96630859375], [106.5, 83.96630859375]]}, {"title": "A2 Two Time-Scale Stochastic Approximation Algorithms", "heading_level": null, "page_id": 15, "polygon": [[106.5, 72.0], [414.0, 72.0], [414.0, 83.38623046875], [106.5, 83.38623046875]]}, {"title": "A2.1 Convergence of Two Time-Scale Stochastic Approximation Algorithms", "heading_level": null, "page_id": 15, "polygon": [[106.5, 453.0], [438.75, 453.0], [438.75, 463.2890625], [106.5, 463.2890625]]}, {"title": "A2.1.1 Additive Noise", "heading_level": null, "page_id": 15, "polygon": [[106.5, 477.0], [208.5, 477.0], [208.5, 486.87890625], [106.5, 486.87890625]]}, {"title": "Convergence Theorem The next theorem is from <PERSON><PERSON><PERSON> 1997 [9].", "heading_level": null, "page_id": 16, "polygon": [[107.25, 382.5], [384.0, 382.5], [384.0, 392.90625], [107.25, 392.90625]]}, {"title": "Comments", "heading_level": null, "page_id": 16, "polygon": [[107.25, 438.75], [154.9423828125, 438.75], [154.9423828125, 450.140625], [107.25, 450.140625]]}, {"title": "A2.1.2 Linear Update, Additive Noise, and Markov Chain", "heading_level": null, "page_id": 17, "polygon": [[106.5, 287.25], [361.5, 287.25], [361.5, 297.580078125], [106.5, 297.580078125]]}, {"title": "Comments.", "heading_level": null, "page_id": 19, "polygon": [[106.8310546875, 183.0], [157.5, 183.0], [157.5, 193.0693359375], [106.8310546875, 193.0693359375]]}, {"title": "A2.1.3 Additive Noise and Controlled Markov Processes", "heading_level": null, "page_id": 19, "polygon": [[107.25, 335.25], [353.25, 335.25], [353.25, 345.33984375], [107.25, 345.33984375]]}, {"title": "Convergence Theorem. The following theorem is from Karmakar & Bhatnagar [28]:", "heading_level": null, "page_id": 22, "polygon": [[107.25, 73.5], [460.1953125, 73.5], [460.1953125, 84.35302734375], [107.25, 84.35302734375]]}, {"title": "Comments.", "heading_level": null, "page_id": 22, "polygon": [[106.5, 208.5], [156.75, 208.5], [156.75, 218.689453125], [106.5, 218.689453125]]}, {"title": "A2.2 Rate of Convergence of Two Time-Scale Stochastic Approximation Algorithms", "heading_level": null, "page_id": 22, "polygon": [[106.5, 306.75], [471.75, 306.75], [471.75, 317.689453125], [106.5, 317.689453125]]}, {"title": "A2.2.1 Linear Update Rules", "heading_level": null, "page_id": 22, "polygon": [[106.5, 330.75], [234.75, 330.75], [234.75, 341.0859375], [106.5, 341.0859375]]}, {"title": "(A4) Convergence rate remains simple:", "heading_level": null, "page_id": 23, "polygon": [[118.5, 73.5], [279.75, 73.5], [279.75, 83.724609375], [118.5, 83.724609375]]}, {"title": "Comments.", "heading_level": null, "page_id": 24, "polygon": [[107.25, 153.75], [156.75, 153.75], [156.75, 164.6455078125], [107.25, 164.6455078125]]}, {"title": "A2.2.2 Nonlinear Update Rules", "heading_level": null, "page_id": 24, "polygon": [[106.5, 323.25], [248.25, 323.25], [248.25, 333.158203125], [106.5, 333.158203125]]}, {"title": "Assumptions. We make the following assumptions:", "heading_level": null, "page_id": 24, "polygon": [[106.5, 517.5], [322.734375, 517.5], [322.734375, 528.0], [106.5, 528.0]]}, {"title": "(A2) Linear approximation and <PERSON><PERSON>witz:", "heading_level": null, "page_id": 24, "polygon": [[119.25, 597.0], [286.5, 597.0], [286.5, 606.76171875], [119.25, 606.76171875]]}, {"title": "Comments.", "heading_level": null, "page_id": 26, "polygon": [[106.90576171875, 230.25], [156.75, 230.25], [156.75, 241.3125], [106.90576171875, 241.3125]]}, {"title": "A2.3 Equal Time-Scale Stochastic Approximation Algorithms", "heading_level": null, "page_id": 26, "polygon": [[106.5, 303.0], [377.419921875, 303.0], [377.419921875, 313.048828125], [106.5, 313.048828125]]}, {"title": "A2.3.1 Equal Time-Scale for Saddle Point Iterates", "heading_level": null, "page_id": 26, "polygon": [[106.5, 356.25], [327.75, 356.25], [327.75, 366.609375], [106.5, 366.609375]]}, {"title": "Assumptions. We make the following assumptions:", "heading_level": null, "page_id": 26, "polygon": [[107.25, 528.0], [322.5, 528.0], [322.5, 538.3125], [107.25, 538.3125]]}, {"title": "Comments.", "heading_level": null, "page_id": 27, "polygon": [[107.25, 285.75], [157.5, 285.75], [157.5, 296.806640625], [107.25, 296.806640625]]}, {"title": "A2.3.2 Equal Time Step for Actor-Critic Method", "heading_level": null, "page_id": 27, "polygon": [[106.5, 396.0], [322.5, 396.0], [322.5, 406.0546875], [106.5, 406.0546875]]}, {"title": "Comments.", "heading_level": null, "page_id": 29, "polygon": [[107.25, 389.25], [157.5, 389.25], [157.5, 399.48046875], [107.25, 399.48046875]]}, {"title": "A3 ADAM Optimization as Stochastic Heavy Ball with Friction", "heading_level": null, "page_id": 29, "polygon": [[106.5, 492.75], [441.0, 492.75], [441.0, 504.28125], [106.5, 504.28125]]}, {"title": "A4 Experiments: Additional Information", "heading_level": null, "page_id": 31, "polygon": [[106.5, 491.25], [327.75, 491.25], [327.75, 501.9609375], [106.5, 501.9609375]]}, {"title": "A4.2 WGAN-GP on the One Billion Word Benchmark.", "heading_level": null, "page_id": 32, "polygon": [[106.5, 73.5], [348.75, 73.5], [348.75, 83.8212890625], [106.5, 83.8212890625]]}, {"title": "A4.3 BEGAN", "heading_level": null, "page_id": 32, "polygon": [[107.25, 462.0], [174.0673828125, 462.0], [174.0673828125, 471.796875], [107.25, 471.796875]]}, {"title": "A5 Discriminator vs. Generator Learning Rate", "heading_level": null, "page_id": 33, "polygon": [[106.5, 72.0], [358.5, 72.0], [358.5, 83.96630859375], [106.5, 83.96630859375]]}, {"title": "A6 Used Software, Datasets, Pretrained Models, and Implementations", "heading_level": null, "page_id": 33, "polygon": [[107.25, 583.5], [475.13671875, 583.5], [475.13671875, 594.7734375], [107.25, 594.7734375]]}, {"title": "References", "heading_level": null, "page_id": 34, "polygon": [[107.25, 246.0], [165.0, 246.0], [165.0, 256.201171875], [107.25, 256.201171875]]}, {"title": "List of Figures", "heading_level": null, "page_id": 37, "polygon": [[106.30810546875, 285.0], [183.0, 285.0], [183.0, 297.0], [106.30810546875, 297.0]]}, {"title": "List of Tables", "heading_level": null, "page_id": 37, "polygon": [[106.5, 483.0], [177.75, 483.0], [177.75, 494.61328125], [106.5, 494.61328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 42], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 11202, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 627], ["Line", 93], ["ListItem", 5], ["Text", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1238, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1047], ["Line", 83], ["TextInlineMath", 4], ["ListItem", 4], ["Equation", 2], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 72], ["TextInlineMath", 5], ["SectionHeader", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8052, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 848], ["Line", 76], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 102], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1119, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 82], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 87], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2195, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 237], ["Span", 188], ["Line", 56], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Text", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["TableCell", 84], ["Line", 43], ["SectionHeader", 5], ["Text", 3], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 802], ["Line", 64], ["ListItem", 6], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 64], ["Equation", 5], ["Text", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 114], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["Line", 5], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1232, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 4], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1190, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 89], ["ListItem", 10], ["Equation", 7], ["Reference", 7], ["Text", 4], ["SectionHeader", 3], ["ListGroup", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 660], ["Line", 76], ["Equation", 9], ["ListItem", 7], ["Text", 7], ["TextInlineMath", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 94], ["Text", 8], ["Equation", 7], ["ListItem", 5], ["Reference", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 913], ["Line", 146], ["Equation", 17], ["ListItem", 8], ["Text", 8], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 910], ["Line", 72], ["TextInlineMath", 6], ["ListItem", 6], ["Equation", 5], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 1281], ["Line", 157], ["Text", 10], ["Equation", 8], ["TextInlineMath", 5], ["ListItem", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 859], ["Line", 86], ["Equation", 12], ["TextInlineMath", 6], ["Text", 6], ["ListItem", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 695], ["Line", 89], ["Equation", 11], ["Text", 7], ["SectionHeader", 4], ["ListItem", 4], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 847], ["Line", 167], ["Equation", 19], ["Text", 11], ["ListItem", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 669], ["Line", 127], ["Text", 10], ["Equation", 9], ["SectionHeader", 4], ["ListItem", 4], ["TextInlineMath", 2], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 847], ["Line", 170], ["Equation", 16], ["Text", 7], ["ListItem", 6], ["TextInlineMath", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 621], ["Line", 116], ["Text", 10], ["Equation", 9], ["SectionHeader", 4], ["ListItem", 3], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 595], ["Line", 76], ["Text", 12], ["Equation", 8], ["ListItem", 4], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 891], ["Line", 87], ["Text", 15], ["Equation", 14], ["TextInlineMath", 4], ["ListItem", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 77], ["Text", 7], ["Equation", 6], ["ListItem", 5], ["TextInlineMath", 5], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 876], ["Line", 183], ["Equation", 8], ["TextInlineMath", 5], ["Text", 5], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 539], ["Line", 76], ["TableCell", 47], ["Text", 3], ["Reference", 3], ["Equation", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2466, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["TableCell", 113], ["Line", 71], ["Reference", 5], ["Text", 3], ["SectionHeader", 2], ["Table", 2], ["Caption", 2], ["Equation", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 47], ["Text", 6], ["Reference", 3], ["SectionHeader", 2], ["Figure", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 51], ["ListItem", 17], ["Reference", 13], ["Text", 2], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 52], ["ListItem", 23], ["Reference", 23], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 52], ["ListItem", 20], ["Reference", 20], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["TableCell", 61], ["Line", 36], ["Reference", 7], ["ListItem", 6], ["SectionHeader", 2], ["TableOfContents", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2793, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/GANs Trained by a Two Time-Scale Update Rule Converge to a Local Nash Equilibrium"}