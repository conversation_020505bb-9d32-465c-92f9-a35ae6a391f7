Image /page/0/Picture/0 description: A square button with rounded corners has a circular icon at the top and text at the bottom. The icon is a circle with a curved line segment on the left side and a flag shape on the right side. The text at the bottom of the button reads "Check for updates".

# PromptSmooth: Certifying Robustness of Medical Vision-Language Models via Prompt Learning

Noor <PERSON><sup>( $\boxtimes$ )</sup>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> University of Artificial Intelligence, Abu Dhabi, UAE {noor.hussein,fahad.shamshad,muzammal.naseer, karthik.nandakumar}@mbzuai.ac.ae

Abstract. Medical vision-language models (Med-VLMs) trained on large datasets of medical image-text pairs and later fine-tuned for specific tasks have emerged as a mainstream paradigm in medical image analysis. However, recent studies have highlighted the susceptibility of these Med-VLMs to adversarial attacks, raising concerns about their safety and robustness. Randomized smoothing is a well-known technique for turning any classifier into a model that is certifiably robust to adversarial perturbations. However, this approach requires retraining the Med-VLM-based classifier so that it classifies well under Gaussian noise, which is often infeasible in practice. In this paper, we propose a novel framework called PromptSmooth to achieve efficient certified robustness of Med-VLMs by leveraging the concept of prompt learning. Given any pre-trained Med-VLM, PromptSmooth adapts it to handle Gaussian noise by learning textual prompts in a zero-shot or few-shot manner, achieving a delicate balance between accuracy and robustness, while minimizing the computational overhead. Moreover, PromptSmooth requires only a single model to handle multiple noise levels, which substantially reduces the computational cost compared to traditional methods that rely on training a separate model for each noise level. Comprehensive experiments based on three Med-VLMs and across six downstream datasets of various imaging modalities demonstrate the efficacy of PromptSmooth. Our code and models are available at [https://github.com/nhussein/PromptSmooth.](https://github.com/nhussein/PromptSmooth)

**Keywords:** Certified Robustness · Medical Vision-Language Models · Prompt tuning · Randomized smoothing

# 1 Introduction

Medical Vision-Language Models (Med-VLMs) have significantly advanced the state-of-the-art across a broad spectrum of medical imaging tasks such as classification, segmentation, and detection [\[21,](#page-9-0)[29\]](#page-10-0). During pre-training, these models

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_65.](https://doi.org/10.1007/978-3-031-72390-2_65)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 698–708, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_65)\_65

| Methods                         | Data<br>Efficient                   | Computational<br>Cost                      | Noise-Agnostic<br>Training          | Tailored<br>to VLM                  | Accuracy vs.<br>Robustness Trade-off       |
|---------------------------------|-------------------------------------|--------------------------------------------|-------------------------------------|-------------------------------------|--------------------------------------------|
| Noise-augmented Re-training [4] | <span style="color:red;">✕</span>   | <span style="color:red;">High</span>       | <span style="color:red;">✕</span>   | <span style="color:red;">✕</span>   | <span style="color:red;">High</span>       |
| Denoised Smoothing [19]         | <span style="color:red;">✕</span>   | <span style="color:red;">High</span>       | <span style="color:red;">✕</span>   | <span style="color:red;">✕</span>   | <span style="color:black;">Moderate</span> |
| Diffusion Smoothing [3]         | <span style="color:red;">✕</span>   | <span style="color:black;">Moderate</span> | <span style="color:green;">✓</span> | <span style="color:red;">✕</span>   | <span style="color:green;">Low</span>      |
| <b>PromptSmooth (Ours)</b>      | <span style="color:green;">✓</span> | <span style="color:black;">Low</span>      | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">Low</span>      |

<span id="page-1-0"></span>Table 1. Comparison of different randomized smoothing implementations.

learn generic representations from large volumes of medical image-text pairs and subsequently transfer this knowledge to downstream medical tasks, which often suffer from limited data availability [\[27\]](#page-10-1). However, recent advances in adversarial machine learning have exposed the vulnerability of VLMs to adversarial attacks [\[28\]](#page-10-2), which introduce small, imperceptible perturbations to the image that drastically change the resulting predictions. Med-VLMs are also prone to these attacks  $[6,8]$  $[6,8]$  $[6,8]$ , which poses a significant risk to the integrity of medical diagnostics, underscoring the need for defense mechanisms to safeguard against such threats.

Though many empirical approaches have been proposed to defend medical models against adversarial attacks [\[5\]](#page-8-2), these defenses have consistently shown vulnerabilities to newer and more powerful adversarial attacks [\[1](#page-8-3)]. Consequently, *certifiable defenses* [\[16](#page-9-3)] with provable adversarial robustness guarantees have attracted considerable attention, particularly in the safety-critical medical domain [\[14](#page-9-4)]. Specifically, these *certifiable defenses* guarantee that the model's predictions will remain unchanged for adversarial perturbations bounded by a *certified radius* around an input sample. However, most of these *certified defenses* are either not scalable to large models or have been evaluated on low-dimensional datasets (*e.g.*,  $32 \times 32$ ) [\[13\]](#page-9-5), significantly hindering their applicability to Med-VLMs and/or high-dimensional datasets encountered in medical imaging [\[2](#page-8-4)].

A well-known approach for addressing the scalability issue is randomized smoothing (RS) [\[15](#page-9-6)], which constructs a new *smoothed classifier* by averaging the output of a *base classifier* under random Gaussian perturbations of the input. The addition of Gaussian noise to the input image creates a trade-off between accuracy and robustness [\[16](#page-9-3)], which depends on how well the base classifier performs on noisy images. As the noise variance increases, robustness improves at the cost of lower clean accuracy. To improve the trade-off between accuracy and robustness, three broad strategies have been proposed. The *first* approach involves training a classifier from scratch on a Gaussian noise-augmented dataset [\[4](#page-8-5)[,17](#page-9-7)]. The *second* strategy prepends a custom-trained denoiser before the pre-trained classifier to remove Gaussian noise from the image prior before RS [\[19\]](#page-9-1). The *third* approach utilizes pre-trained off-the-shelf diffusion models (trained on large-scale image datasets) as denoisers [\[3,](#page-8-0)[14](#page-9-4)]. Extending these methods to Med-VLMs presents unique challenges (see Tab. [1\)](#page-1-0). *Noise-augmented retraining* of Med-VLMs would require substantial computational resources and access to large (often privacy-sensitive) medical datasets. *Denoiser prepending* requires a large dataset of paired clean-noisy images as well as time-consuming

denoiser training for each noise level. *Diffusion-based denoisers* require extensive datasets to accurately model complex medical images and training of such diffusion models is expensive.

To overcome the above limitations, we propose PromptSmooth to efficiently achieve certified robustness in pre-trained Med-VLMs without hampering clean accuracy. Instead of re-training the VLM from scratch or utilizing denoisers, we inject a small number of learnable prompts (tokens) into the VLM input space and optimize them, while keeping the entire backbone frozen. Our contributions are two-fold: (i) To the best of our knowledge, this is the first work where prompt learning is exploited for efficient robustness certification of Med-VLMs in classification, and (ii) We propose algorithms for effective prompt learning under both zero-shot (Zero-Shot PromptSmooth) and few-shot (Few-Shot PromptSmooth) settings.

# 2 Related Work and Background

Medical VLMs: Medical VLMs based on Contrastive Language Image Pretraining (CLIP) [\[18](#page-9-8)] have gained considerable attention in medical imaging [\[29\]](#page-10-0). This pre-training method aims to maximize the cosine similarity between the embeddings of matched image-text pairs, while minimizing it among unmatched pairs. Despite the introduction of numerous Med-VLMs for many imaging modalities, including histopathology [\[9](#page-9-9)[,10\]](#page-9-10), X-ray [\[26](#page-10-3)], and retinal [\[23\]](#page-10-4) images, a critical evaluation of their robustness remains largely unexplored.

**Certified Robustness:** Let  $f : \mathcal{X} \to \mathcal{Y}$  be a base classifier that maps an input  $\mathbf{x} \in \mathcal{X} \subseteq \mathbb{R}^D$  into a class label  $y \in \mathcal{Y} = \{1, 2, \cdots, K\}$ , where X and Y are the input and label spaces,  $D$  is the input dimensionality, and  $K$  is the number of classes. Randomized smoothing  $(RS)$  [\[4](#page-8-5)] transforms the base classifier f into a smoothed classifier g as follows:  $g(\mathbf{x}) = \arg \max_{y \in \mathcal{Y}} \mathbb{P}[f(\mathbf{x} + \delta) = y]$ , where  $\delta \sim \mathcal{N}(0, \sigma^2 I)$  and **P** denotes a probability measure. For an input **x**, g predicts the class most likely under the base classifier  $f$  when  $x$  is perturbed with isotropic Gaussian noise  $\delta$ . RS provides the following robustness guarantee for g: if  $y_A \in \mathcal{Y}$ and  $p_A, \overline{p_B} \in [0, 1]$  satisfy  $\mathbb{P}[f(\mathbf{x} + \delta) = y_A] \geq p_A \geq \overline{p_B} \geq \max_{y \neq y_A} \mathbb{P}[f(\mathbf{x} + \delta) = y_A]$ y], then  $g(\mathbf{x} + \mathbf{r}) = y_A$  for all  $||\mathbf{r}||_2 < R$ , where the certified radius R around an input **x** is given by  $R = \frac{\sigma}{2} \left( \Phi^{-1}(\underline{p_A}) - \Phi^{-1}(\overline{p_B}) \right)$ . This guarantee ensures that for any  $\ell_2$  adversarial perturbation **r** with magnitude less than R, the output of the smoothed classifier g remains unchanged. Here,  $p_A$  and  $\overline{p_B}$  are the lower-bound and upper-bound of probabilities of the most-likely  $(y_A)$  and second-most-likely  $(y_B)$  classes, respectively, predicted by f under noise, and  $\Phi^{-1}$  is the inverse of the standard Gaussian cdf. For practical applications, RS uses Monte Carlo sampling to estimate  $p_A$  and  $\overline{p_B}$ , thereby facilitating the computation of a certified radius. Increasing noise variance  $\sigma$  leads to better robustness (higher R), but at the cost of accuracy (because predictions of  $f$  under noise become less reliable). This trade-off can be mitigated by improving the accuracy of  $f$  under noise.

**Prompt Learning:** Prompt learning (PL) is a technique that fine-tunes VLMs for specific tasks by adding learnable prompt tokens to the model's input, thereby avoiding changes to existing parameters. The effectiveness of PL in few-shot

Image /page/3/Figure/1 description: This diagram illustrates a framework for certifiably robust models. The process begins with an input image, It, which is then subjected to noise, resulting in multiple noisy versions (It + δ), where δ is drawn from a Gaussian distribution. These noisy images are fed into an image encoder, Eimage, which produces embeddings. Simultaneously, zero-shot and few-shot prompts are processed by a text encoder, Etext, also yielding embeddings. The similarity between image and text embeddings is computed. The framework incorporates a certification process that involves calculating entropy (H) and a minimum value related to the entropy of the distribution of predicted labels. This process leads to a decision to either predict a label or abstain, ultimately resulting in a certifiably robust model. The diagram also indicates learnable and frozen components using fire and snowflake icons, respectively, and shows a backpropagation path for learning.

<span id="page-3-0"></span>Fig. 1. Overview of PromptSmooth for certified robustness. Prompts can be learned offline or at test-time. Gaussian noise is added at test-time to T copies of the input  $\mathbf{I}_t$ and prompts are learned by minimizing the entropy loss (dashed orange line). Using zero-shot and/or few-shot prompts, inference is repeated for M noisy instances for certification (solid black line). Model predicts (and gives a certified radius) or abstains.

scenarios [\[30](#page-10-5)[,31](#page-10-6)] makes it especially useful for data-limited medical imaging tasks. Recently, attempts have also been made to learn prompts in a zero-shot manner by enforcing consistency regularization between multiple augmentations of a test sample at test time [\[22](#page-9-11)]. While PL is typically used to improve performance on downstream tasks, this work investigates how to leverage PL for efficient robustness certification of Med-VLMs in both few-shot and zero-shot settings.

# 3 Methodology

Our *goal* is to efficiently adapt zero-shot classifiers based on Med-VLMs in datalimited scenarios to predict well under Gaussian noise, thereby ensuring that they maintain high accuracy on clean images while also achieving strong certified robustness. We first outline how Med-VLMs can be used for zero-shot inference on downstream tasks and introduce PromptSmooth for their efficient adaptation in few/zero-shot settings.

<span id="page-3-1"></span>

### 3.1 Zero-Shot Inference Based on Med-VLMs

Med-VLMs learn an alignment between image and text input spaces (denoted as  $I$  and  $I$ , respectively) and typically consist of two encoders: an image encoder  $\mathbf{E}_{\text{image}} : \mathcal{I} \to \mathbb{R}^d$  and a text encoder  $\mathbf{E}_{\text{text}} : \mathcal{I} \to \mathbb{R}^d$ . The image encoder maps a given image  $\mathbf{I} \in \mathcal{I} \subseteq \mathbb{R}^{H \times W \times C}$  into a d-dimensional image feature vector  $\mathbf{v} \in \mathbb{R}^d$ . Similarly, the text encoder maps the given text  $\mathbf{T} \in \mathcal{T}$  into a text feature vector  $\mathbf{u} \in \mathbb{R}^d$ . These models utilize a contrastive loss during pre-training to enhance the similarity between text and image feature vectors, ensuring their alignment within the feature space. After pre-training, Med-VLMs can be used in the zeroshot manner for various downstream tasks like image classification. For zeroshot application, consider a test image  $\mathbf{I}_t \in \mathcal{I}$  from class  $y_t \in \mathcal{Y}$ . All the class labels  $y_i \in \mathcal{Y}$   $(i \in [1, K])$  are converted into text prompts using a hand-crafted template such as  $\mathbf{t}(y_i)$  = "A X-ray image of [CLASS  $y_i$ ] patient". These text prompts are processed by the text encoder to obtain  $\{\mathbf{u}_1, \mathbf{u}_2, \cdots, \mathbf{u}_K\}$ , where  $\mathbf{u}_i = \mathbf{E}_{\text{text}}(\mathbf{t}(y_i))$ . Let  $\mathbf{v}_t = \mathbf{E}_{\text{image}}(\mathbf{I}_t)$  be the image feature vector for the test image. A cosine similarity score  $s_i = \text{sim}(\mathbf{u}_i, \mathbf{v}_t)$  is computed for  $i \in [1, K]$  and

the prediction probabilities for  $\mathbf{I}_t$  are obtained as  $\mathbb{P}(y_i|\mathbf{I}_t) = \frac{\exp(\tau s_i)}{\sum_{j=1}^K \exp(\tau s_j)},$  where  $\tau$  is the softmax temperature parameter. Thus, a zero-shot classifier f based on the Med-VLM ( $\mathbf{E}_{image}, \mathbf{E}_{text}$ ) outputs a predicted label  $\hat{y}_t$ , where  $\hat{y}_t = f(\mathbf{I}_t)$  $\arg \max_{y \in \mathcal{Y}} \mathbb{P}(y|\mathbf{I}_t)$ . Despite their impressive zero-shot capabilities, Med-VLMs cannot be directly subjected to RS as they are pre-trained on clean datasets and their accuracy drops drastically when input images are perturbed with Gaussian noise. A naive solution is to pre-train the Med-VLMs from scratch with noisy data augmentation as in [\[4](#page-8-5)], but this is often practically infeasible.

### 3.2 PromptSmooth

We now present our PromptSmooth approach, as shown in Fig. [1,](#page-3-0) to efficiently adapt a zero-shot classifier  $f$  based on Med-VLMs such that high certified adversarial robustness can be achieved without severely degrading clean accuracy. The key idea is to inject small number of learnable prompts as inputs to the text encoder of the Med-VLM and learn these prompts to improve prediction accuracy on noisy images, while keeping the backbone fixed. Note that when a text prompt  $\mathbf{t}(y_i) \in \mathcal{T}$  is presented as input to the text encoder, it is broken down into a sequence of word tokens, with their embeddings processed by the encoder. In other words,  $\mathbf{t}(y_i)$  can be represented as a sequence  $[\mathbf{w}]_1 [\mathbf{w}]_2 \cdots [\text{CLASS } y_i],$ where [∗] represents the embedding of a single word in the text prompt. In prompt learning (PL), the fixed word embeddings (except for the class name) are replaced with M learnable embeddings, *i.e.*,  $\mathbf{t}(y_i)$  can now be represented as a sequence  $\mathbf{p}_{i1}$   $\mathbf{p}_{i2}$   $\cdots$   $\mathbf{p}_{iM}$  [CLASS  $y_i$ ], where the dimensionality of **p** is the same as  $[\mathbf{w}]$ . Let  $\mathcal{P} = {\mathbf{p}_{im}}$ ,  $i \in [1, K]$ ,  $m \in [1, M]$ , denote the collection of all learnable prompts. Also, let  $\mathbf{u}_i(\mathcal{P})$  be the text feature vector output by the text encoder for class  $y_i$  after introduction of the learnable prompts  $\mathcal P$  and  $f_{\mathcal P}$  be the modified zero-shot classifier based on these new text features. Next, we address the question of how to learn these prompts  $P$  efficiently.

Few-Shot PromptSmooth: In Few-Shot PromptSmooth, we consider a scenario where only a few samples from the downstream medical task are available. Given a zero-shot classifier f based on a pre-trained Med-VLM  $(\mathbf{E}_{image}, \mathbf{E}_{text})$ as well as a few labeled samples  $\{(\mathbf{I}_n, y_n)\}_{n=1}^N$  from a downstream dataset  $\mathcal{D}$ ,<br>where  $\mathbf{I} \in \mathcal{T}$  and  $y \in \mathcal{Y}$ . Fey-Shot, Propert Speech learns the properts  $\mathcal{D}$  as where  $I_n \in \mathcal{I}$  and  $y_n \in \mathcal{Y}$ , Few-Shot PromptSmooth learns the prompts  $\mathcal{P}$  as follows:

<span id="page-4-0"></span>
$$
\mathcal{P}^* = \underset{\mathcal{P}}{\arg\min} \mathbb{E}_{\delta \sim \mathcal{N}(0,\sigma^2\mathbf{I})} \frac{1}{N} \sum_{n=1}^N \mathcal{L}(f_{\mathcal{P}}(\mathbf{I}_n + \delta), y_n), \tag{1}
$$

where  $\mathcal L$  denotes the loss function between the classifier prediction and the ground-truth label. Similar to [\[31](#page-10-6)], fine-tuning is performed to minimize the standard classification loss based on cross-entropy, and the gradients are backpropagated through the frozen text encoder **E**text to iteratively update the prompts P. Note that these prompts are external to the pre-trained Med-VLM

and they adjust the input context of the model without distorting its pre-trained features. Thus, this approach preserves the rich knowledge encoded in the frozen Med-VLMs to maintain high clean accuracy. At the same time, updating the prompts based on a few noisy samples from the downstream data set enhances certified robustness.

Zero-Shot PromptSmooth: In Zero-Shot PromptSmooth, the challenge is to learn the prompts  $\mathcal{P}$  at inference time given only a single test sample  $\mathbf{I}_t$  without any label. Given the lack of labels, the prompts cannot be optimized using the cross-entropy loss as in the few shot case. Therefore, we need a carefully designed unsupervised loss function for  $\mathcal{L}$ . Inspired by [\[22\]](#page-9-11), we optimize the prompts using a single step gradient descent based on the following entropy minimization loss.

<span id="page-5-0"></span>
$$
\mathcal{P}^* = \underset{\mathcal{P}}{\arg\min} \mathcal{H}\left(\mathbb{E}_{\delta \sim \mathcal{N}(0,\sigma^2\mathbf{I})} \mathbb{Q}_{\mathcal{P}}(\mathbf{y} | (\mathbf{I}_t + \delta))\right),\tag{2}
$$

where H denotes the entropy of a discrete probability distribution  $\mathbb{Q}, \mathbb{Q}_p(\mathbf{y}|\mathbf{I}_t + \mathbf{I}_p)$  $(\delta)$ ) =  $[\mathbb{P}_\mathcal{P}(y_1 | (\mathbf{I}_t + \delta)), \mathbb{P}_\mathcal{P}(y_2 | (\mathbf{I}_t + \delta)), \cdots, \mathbb{P}_\mathcal{P}(y_K | (\mathbf{I}_t + \delta))],$  and  $\mathbb{P}_\mathcal{P}(y_i | (\mathbf{I}_t + \delta))$ is the softmax output of the classifier  $f_{\mathcal{P}}$  for class  $y_i, i \in [1, K]$  based on the noisy input  $(\mathbf{I}_t + \delta)$ . Note that  $\sum_{i=1}^K \mathbb{P}_p(y_i | (\mathbf{I}_t + \delta)) = 1$ . The above entropy min-<br>imization loss forces the classifier  $f_{\mathcal{D}}$  to produce highly-confident (low entropy) imization loss forces the classifier  $f_{\mathcal{P}}$  to produce highly-confident (low entropy) yet consistent predictions for different noisy perturbations of  $I_t$ .

In practice, the expectation in both Eqs.  $(1)$  and  $(2)$  can be replaced by a sample average over T Monte Carlo samples of  $\delta$  drawn from Gaussian distributions with different values of  $\sigma$  chosen from a desired range. This greatly reduces the computational cost of our approach because it avoids the need to learn the prompts  $P$  that are specific to a given  $\sigma$ . Finally, it is also possible to apply Zero-Shot PromptSmooth on top of Few-Shot PromptSmooth (i.e., combine both methods), which we simply refer to as PromptSmooth.

# 4 Experiments

Models and Datasets: We evaluate our approach using three publicly available pre-trained Med-VLMs on six downstream datasets. The evaluated VLMs are PLIP [\[9](#page-9-9)], Quilt [\[10\]](#page-9-10), and MedCLIP [\[26\]](#page-10-3), with PLIP and Quilt trained on histopathology datasets and MedCLIP on X-ray images. Specifically, for PLIP, we show results on four pathology datasets: KatherColon [\[11\]](#page-9-12) (nine classes), Pan-Nuke [\[7](#page-9-13)] (binary), SkinCancer [\[12](#page-9-14)] (sixteen classes) and SICAPv2 [\[24](#page-10-7)] (three classes). Quilt is evaluated on SkinCancer and SICAPv2, while MedCLIP is evaluated on the binary COVID [\[25\]](#page-10-8) and RSNA Pneumonia datasets [\[20](#page-9-15)] (threeclasses). For all of our experiments, we utilize the official train and test splits of the datasets unless otherwise mentioned.

Implementation Details: Our method is implemented in PyTorch on an NVIDIA A100 GPU with 40GB of memory. We report results with images normalized to  $[0, 1]^{224 \times 224 \times 3}$ , aligning with prior studies. Labels for downstream dataset fine-tuning are converted into sentences, e.g., the label "Tumor" becomes

| Method                                                                                                                                                                                  |     |      |     | Certified Accuracy at $\ell_2$ radius $(\%)$ |     |                                                                                                                        |     |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----|------|-----|----------------------------------------------|-----|------------------------------------------------------------------------------------------------------------------------|-----|
|                                                                                                                                                                                         | 0.1 | 0.25 | 0.5 | 0.75                                         | 1.0 | 1.25                                                                                                                   | 1.5 |
| Zero-shot PLIP (No PL)                                                                                                                                                                  |     |      |     |                                              |     | $^{(56.6)}49.4$ $^{(56.6)}38.2$ $^{(28.9)}20.8$ $^{(28.9)}17.6$ $^{(11.0)}11.0$ $^{(11.0)}11.0$ $^{(11.0)}11.0$        |     |
| Naive PL $(CoOp)$ [31]                                                                                                                                                                  |     |      |     |                                              |     | $^{(71.6)}66.7$ $^{(71.6)}56.0$ $^{(22.0)}16.4$ $^{(22.0)}14.2$ $^{(11.0)}11.0$ $^{(11.0)}11.0$ $^{(11.0)}11.0$        |     |
| Denoised Smoothing  19                                                                                                                                                                  |     |      |     |                                              |     | $^{(55.0)}48.2$ $^{(55.0)}39.2$ $^{(45.2)}31.0$ $^{(45.2)}25.6$ $^{(26.2)}17.4$ $^{(26.2)}16.2$ $^{(26.2)}14.6$        |     |
| Diffusion Smoothing [3]                                                                                                                                                                 |     |      |     |                                              |     | $^{(58.0)}$ 57.0 $^{(53.0)}$ 49.0 $^{(53.0)}$ 41.0 $^{(53.0)}$ 34.0 $^{(53.0)}$ 26.0 $^{(53.0)}$ 22.0 $^{(53.0)}$ 16.0 |     |
| Zero-shot PromptSmooth <sup>(57.6)</sup> 53.4 <sup>(57.6)</sup> 49.0 <sup>(30.2)</sup> 29.0 <sup>(30.2)</sup> 29.0 <sup>(30.2)</sup> 28.6 <sup>(30.2)</sup> 28.4 <sup>(30.2)</sup> 27.4 |     |      |     |                                              |     |                                                                                                                        |     |
| Few-Shot PromptSmooth                                                                                                                                                                   |     |      |     |                                              |     | $^{(81.2)}78.2$ $^{(81.2)}67.6$ $^{(75.6)}52.2$ $^{(75.6)}35.6$ $^{(50.4)}26.4$ $^{(50.4)}22.2$ $^{(50.4)}17.6$        |     |
| PromptSmooth                                                                                                                                                                            |     |      |     |                                              |     | $^{(82.0)}81.8^{(82.0)}81.0^{(76.6)}74.8^{(76.6)}73.2^{(54.0)}48.4^{(54.0)}47.2^{(54.0)}45.6$                          |     |

<span id="page-6-0"></span>Table 2. Certification results for PLIP on KatherColon dataset, where the numbers indicate certified accuracy (%). Corresponding clean accuracy (%) is in parentheses.

<span id="page-6-1"></span>Table 3. Certified accuracy (%) for MedCLIP on COVID and RSNA Pneumonia datasets, with the corresponding clean accuracy (%) in parentheses.

| Method                                                                                                                                                                                                         |                                                                                                                                         |      | <b>COVID</b> |      |     |      | <b>RSNA</b> Pneumonia                                                                                                                   |      |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------|------|--------------|------|-----|------|-----------------------------------------------------------------------------------------------------------------------------------------|------|
|                                                                                                                                                                                                                | 0.1                                                                                                                                     | 0.25 | 0.5          | 0.75 | 0.1 | 0.25 | 0.5                                                                                                                                     | 0.75 |
| Denoised Smoothing [19]                                                                                                                                                                                        |                                                                                                                                         |      |              |      |     |      | $^{(66.4)}$ 54.6 $^{(50.2)}$ 48.4 $^{(50.2)}$ 45.8 $^{(50.2)}$ 36.0 $^{(37.6)}$ 27.6 $^{(31.6)}$ 21.0 $^{(31.6)}$ 9.40 $^{(31.6)}$ 1.79 |      |
| Diffusion Smoothing [3]                                                                                                                                                                                        |                                                                                                                                         |      |              |      |     |      | $^{(56.0)}37.0$ $^{(44.0)}22.0$ $^{(44.0)}6.00$ $^{(44.0)}1.00$ $^{(44.0)}40.0$ $^{(44.0)}28.0$ $^{(44.0)}12.0$ $^{(44.0)}1.00$         |      |
| Zero-shot PromptSmooth <sup>(62.4)</sup> 62.0 <sup>(62.4)</sup> 60.6 <sup>(50.2)</sup> 50.0 <sup>(50.2)</sup> 49.8 <sup>(37.0)</sup> 35.4 <sup>(37.0)</sup> 33.4 <sup>(33.4)</sup> 33.4 <sup>(33.4)</sup> 33.2 |                                                                                                                                         |      |              |      |     |      |                                                                                                                                         |      |
| Few-shot PromptSmooth                                                                                                                                                                                          |                                                                                                                                         |      |              |      |     |      | $^{(66.8)}$ 58.0 $^{(52.0)}$ 48.8 $^{(52.0)}$ 47.6 $^{(52)}$ 42.8 $^{(41.4)}$ 34.4 $^{(34.0)}$ 31.2 $^{(34.0)}$ 27.0 $^{(34.0)}$ 23.6   |      |
| PromptSmooth                                                                                                                                                                                                   | $^{(69.4)}$ 69.0 $^{(69.4)}$ 68.4 $^{(53.0)}$ 52.8 $^{(53.0)}$ 52.6 $^{(42.4)}$ 40.8 $^{(42.4)}$ 35.8 $^{(34.6)}$ 33.4 $^{(34.6)}$ 32.0 |      |              |      |     |      |                                                                                                                                         |      |

'An H&E image patch of {Tumor}'. For Few-Shot PromptSmooth, we fine-tune using a 16-shot setting for 50 epochs. To update the prompts, we use SGD optimizer with a learning rate of 0.002 and a batch size of 16 and initialize prompts with 5 randomly initialized context tokens [\[31](#page-10-6)]. For Zero-Shot PromptSmooth, we augment with  $T = 100$  noisy samples and update the prompt with a single gradient descent step. For RS, we use  $M = 10,000$  Monte Carlo samples with  $\alpha = 0.001$  (see [\[4\]](#page-8-5)).

Baselines: We conduct a comparative analysis of PromptSmooth against two representative RS techniques: *Denoised Smoothing* [\[19\]](#page-9-1) and *Diffusion Smoothing* [\[3\]](#page-8-0), with the latter being the current state-of-the-art. Additionally, we also compare with zero-shot certification (no PL) and naive PL baselines. In the former scenario (see Sect. [3.1\)](#page-3-1), certification results are obtained using hand-crafted prompts without PL, while naive PL [\[31](#page-10-6)] (CoOp) updates prompts using only clean samples from the target dataset.

Evaluation: We use both clean and certified accuracy as the evaluation metrics. Certified accuracy is calculated as the proportion of the test set that CERTIFY  $[4]$  $[4]$ correctly identifies for radius  $R$  without abstention. Following prior works, we employ RS across four noise levels,  $\sigma \in \{0.1, 0.25, 0.5, 1.0\}$ , selecting the optimal results for each R from 500 samples randomly chosen from the official test sets.

Image /page/7/Figure/1 description: The image displays three line graphs side-by-side, each plotting certified accuracy against the l2 radius. The first graph, labeled (a) Shots, shows certified accuracy for different numbers of shots (2, 4, 8, 16, 28). The second graph, labeled (b) Context Tokens, illustrates certified accuracy for varying numbers of context tokens (2, 4, 8, 32). The third graph, labeled (c) Optimizer Steps, presents certified accuracy for different optimizer steps (1, 2, 4, 8, 16). All graphs have the l2 radius on the x-axis ranging from 0.0 to 1.2 and certified accuracy on the y-axis ranging from 0.0 to 1.0.

Fig. 2. Impact of changing the number of (a) shots and (b) context tokens in Few-Shot PromptSmooth and (c) varying the optimizer steps in Zero-Shot PromptSmooth.

<span id="page-7-2"></span><span id="page-7-1"></span><span id="page-7-0"></span>Table 4. Zero-shot PromptSmooth con-Table 5. Training time and certification text initialization time per sample

| Prompt                        | $\ell_2$ radius |             |             |             | Method                  | Time     |               |            |
|-------------------------------|-----------------|-------------|-------------|-------------|-------------------------|----------|---------------|------------|
|                               | 0               | 0.1         | 0.25        | 0.5         |                         | Training | Certification | Total      |
| "An $H&E$ image patch of"     | 38.0            | 35.0        | 32.0        | 31.0        | Denoised Smoothing [19] | 8h 47m   | 17s           | 8h 47m 17s |
| "An H&E noisy image patch of" | <b>40.0</b>     | <b>38.0</b> | <b>38.0</b> | <b>35.0</b> | Diffusion Smoothing [3] | -        | 4m 40s        | 4m 40s     |
|                               |                 |             |             |             | PromptSmooth            | 40s      | 1.9s          | 41.9s      |

### 4.1 Results and Discussion

Table [2](#page-6-0) compares PromptSmooth against baseline methods on the KatherColon dataset using the PLIP model. PromptSmooth consistently surpasses all baselines across each radius for both standard and certified accuracy. Notably, at a high radius of 1.5, it achieves an absolute gain of 29.6% in certified accuracy over the recent *Diffusion Smoothing* method. Zero-Shot PromptSmooth outperforms baselines at higher radii by adapting to certifying input noise levels, while Few-Shot PromptSmooth achieves high certified accuracy at lower radii through alignment with noisy sample distributions via few-shot prompt learning. Combining zero-shot's adaptability with few-shot's noisy distribution alignment, PromptSmooth ensures high certified accuracy across all radii and maintains clean accuracy. Similar performance trends are observed in Table [3](#page-6-1) for MedCLIP on the COVID and RSNA Pneumonia datasets. Certification results for Quilt and other datasets are provided in Appendix, and they show a similar trend.

### 4.2 Ablations

All ablations are performed on the samples from the official test set of Kather-Colon dataset with PLIP model.

Ablations for Few-Shot PromptSmooth: Increasing the number of samples per class in the few-shot case improves certified accuracy as depicted in Fig. [2a](#page-7-0), at the cost of a slight increase in fine-tuning time. Additionally, Fig. [2b](#page-7-0) demonstrates that optimal certified accuracy is reached with 8 context tokens during PL, beyond which there is a degradation.

Ablations for Zero-Shot PromptSmooth: Figure [2c](#page-7-0) illustrates that certified accuracy increases with the number of gradient descent steps (up to 8), after which it plateaus. Additionally, initializing with a noisy context, as demonstrated in the Table [4,](#page-7-1) enhances certified accuracy compared to standard prompts.

Computational Time: As illustrated in Table [5,](#page-7-2) due to its lightweight nature, PromptSmooth is an order of magnitude faster than the Denoised Smoothing [\[19](#page-9-1)] and Diffusion Smoothing [\[3](#page-8-0)]. Denoised Smoothing requires extensive training for custom denoisers, and Diffusion Smoothing which, despite utilizing pre-trained model, incurs longer certification time.

# 5 Conclusion

In this paper, we introduced a novel approach for efficiently adapting a zero-shot classifier based on a Medical Vision-Language Model (Med-VLM) for adversarial robustness certification through prompt learning. We also developed two variants of our approach, specifically tailored for zero-shot and few-shot scenarios, which are particularly useful in the context of data-scarce medical applications. Extensive experiments conducted on three publicly available Med-VLMs and six downstream datasets demonstrate that our proposed approach achieves stateof-the-art performance. Moreover, it is computationally efficient and does not require large medical datasets, which enhances its practicality.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-8-3"></span>1. Athalye, A., Carlini, N., Wagner, D.: Obfuscated gradients give a false sense of security: Circumventing defenses to adversarial examples. In: International conference on machine learning. pp. 274–283. PMLR (2018)
- <span id="page-8-4"></span>2. Azad, B., Azad, R., Eskandari, S., Bozorgpour, A., Kazerouni, A., Rekik, I., Merhof, D.: Foundational models in medical imaging: A comprehensive survey and future vision. arXiv preprint [arXiv:2310.18689](http://arxiv.org/abs/2310.18689) (2023)
- <span id="page-8-0"></span>3. Carlini, N., Tramer, F., Dvijotham, K.D., Rice, L., Sun, M., Kolter, J.Z.: (certified!!) adversarial robustness for free! arXiv preprint [arXiv:2206.10550](http://arxiv.org/abs/2206.10550) (2022)
- <span id="page-8-5"></span>4. Cohen, J., Rosenfeld, E., Kolter, Z.: Certified adversarial robustness via randomized smoothing. In: international conference on machine learning. pp. 1310–1320. PMLR (2019)
- <span id="page-8-2"></span>5. Dong, J., Chen, J., Xie, X., Lai, J., Chen, H.: Adversarial attack and defense for medical image analysis: Methods and applications. arXiv preprint [arXiv:2303.14133](http://arxiv.org/abs/2303.14133) (2023)
- <span id="page-8-1"></span>6. Finlayson, S.G., Bowers, J.D., Ito, J., Zittrain, J.L., Beam, A.L., Kohane, I.S.: Adversarial attacks on medical machine learning. Science 363(6433), 1287–1289 (2019)

- <span id="page-9-13"></span>7. Gamper, J., Alemi Koohbanani, N., Benet, K., Khuram, A., Rajpoot, N.: Pannuke: an open pan-cancer histology dataset for nuclei instance segmentation and classification. In: Digital Pathology: 15th European Congress, ECDP 2019, Warwick, UK, April 10–13, 2019, Proceedings 15. pp. 11–19. Springer (2019)
- <span id="page-9-2"></span>8. Han, T., Nebelung, S., Khader, F., Wang, T., Mueller-Franzes, C., Försch, S., Kleesiek, C., Bressem, K.K., et al.: Medical foundation models are susceptible to targeted misinformation attacks. arXiv preprint [arXiv:2309.17007](http://arxiv.org/abs/2309.17007) (2023)
- <span id="page-9-9"></span>9. Huang, Z., Bianchi, F., Yuksekgonul, M., Montine, T.J., Zou, J.: A visual–language foundation model for pathology image analysis using medical twitter. Nature medicine 29(9), 2307–2316 (2023)
- <span id="page-9-10"></span>10. Ikezogwo, W., Seyfioglu, S., Ghezloo, F., Geva, D., Sheikh Mohammed, F., Anand, P.K., Krishna, R., Shapiro, L.: Quilt-1m: One million image-text pairs for histopathology. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-9-12"></span>11. Kather, J.N., Krisam, J., Charoentong, P., Luedde, T., Herpel, E., Weis, C.A., Gaiser, T., Marx, A., Valous, N.A., Ferber, D., et al.: Predicting survival from colorectal cancer histology slides using deep learning: A retrospective multicenter study. PLoS medicine 16(1), e1002730 (2019)
- <span id="page-9-14"></span>12. Kriegsmann, K., Lobers, F., Zgorzelski, C., Kriegsmann, J., Janssen, C., Meliss, R.R., Muley, T., Sack, U., Steinbuss, G., Kriegsmann, M.: Deep learning for the detection of anatomical tissue structures and neoplasms of the skin on scanned histopathological tissue sections. Frontiers in Oncology 12, 1022967 (2022)
- <span id="page-9-5"></span>13. Kumari, A., Bhardwaj, D., Jindal, S., Gupta, S.: Trust, but verify: A survey of randomized smoothing techniques. arXiv preprint [arXiv:2312.12608](http://arxiv.org/abs/2312.12608) (2023)
- <span id="page-9-4"></span>14. Laousy, O., Araujo, A., Chassagnon, G., Paragios, N., Revel, M.P., Vakalopoulou, M.: Certification of deep learning models for medical image segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 611–621. Springer (2023)
- <span id="page-9-6"></span>15. Lecuyer, M., Atlidakis, V., Geambasu, R., Hsu, D., Jana, S.: Certified robustness to adversarial examples with differential privacy. In: 2019 IEEE symposium on security and privacy (SP). pp. 656–672. IEEE (2019)
- <span id="page-9-3"></span>16. Li, L., Xie, T., Li, B.: Sok: Certified robustness for deep neural networks. In: 2023 IEEE symposium on security and privacy (SP). pp. 1289–1310. IEEE (2023)
- <span id="page-9-7"></span>17. Qiu, K., Zhang, H., Wu, Z., Lin, S.: Exploring transferability for randomized smoothing. arXiv preprint [arXiv:2312.09020](http://arxiv.org/abs/2312.09020) (2023)
- <span id="page-9-8"></span>18. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-9-1"></span>19. Salman, H., Sun, M., Yang, G., Kapoor, A., Kolter, J.Z.: Denoised smoothing: A provable defense for pretrained classifiers. Advances in Neural Information Processing Systems 33, 21945–21957 (2020)
- <span id="page-9-15"></span>20. Shih, G., Wu, C.C., Halabi, S.S., Kohli, M.D., Prevedello, L.M., Cook, T.S., Sharma, A., Amorosa, J.K., Arteaga, V., Galperin-Aizenberg, M., et al.: Augmenting the national institutes of health chest radiograph dataset with expert annotations of possible pneumonia. Radiology: Artificial Intelligence 1(1), e180041 (2019)
- <span id="page-9-0"></span>21. Shrestha, P., Amgain, S., Khanal, B., Linte, C.A., Bhattarai, B.: Medical vision language pretraining: A survey. arXiv preprint [arXiv:2312.06224](http://arxiv.org/abs/2312.06224) (2023)
- <span id="page-9-11"></span>22. Shu, M., Nie, W., Huang, D.A., Yu, Z., Goldstein, T., Anandkumar, A., Xiao, C.: Test-time prompt tuning for zero-shot generalization in vision-language models. Advances in Neural Information Processing Systems 35, 14274–14289 (2022)

- <span id="page-10-4"></span>23. Silva-Rodriguez, J., Chakor, H., Kobbi, R., Dolz, J., Ayed, I.B.: A foundation language-image model of the retina (flair): Encoding expert knowledge in text supervision. arXiv preprint [arXiv:2308.07898](http://arxiv.org/abs/2308.07898) (2023)
- <span id="page-10-7"></span>24. Silva-Rodríguez, J., Colomer, A., Sales, M.A., Molina, R., Naranjo, V.: Going deeper through the gleason scoring scale: An automatic end-to-end system for histology prostate grading and cribriform pattern detection. Computer methods and programs in biomedicine 195, 105637 (2020)
- <span id="page-10-8"></span>25. Tawsifur, R., Amith, K., Yazan, Q., Anas, T., Serkan, K., Abul, K.S.B., Tariqul, I.M., Somaya, A.M.: Zughaier susu m, khan muhammad salman, et al. Exploring the effect of image enhancement techniques on covid-19 detection using chest x-ray images. Computers in biology and medicine 132, 104319 (2021)
- <span id="page-10-3"></span>26. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. arXiv preprint [arXiv:2210.10163](http://arxiv.org/abs/2210.10163) (2022)
- <span id="page-10-1"></span>27. Zhang, J., Kapse, S., Ma, K., Prasanna, P., Saltz, J., Vakalopoulou, M., Samaras, D.: Prompt-mil: Boosting multi-instance learning schemes via task-specific prompt tuning. arXiv preprint [arXiv:2303.12214](http://arxiv.org/abs/2303.12214) (2023)
- <span id="page-10-2"></span>28. Zhao, Y., Pang, T., Du, C., Yang, X., Li, C., Cheung, N.M.M., Lin, M.: On evaluating adversarial robustness of large vision-language models. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-10-0"></span>29. Zhao, Z., Liu, Y., Wu, H., Li, Y., Wang, S., Teng, L., Liu, D., Li, X., Cui, Z., Wang, Q., et al.: Clip in medical imaging: A comprehensive survey. arXiv preprint [arXiv:2312.07353](http://arxiv.org/abs/2312.07353) (2023)
- <span id="page-10-5"></span>30. Zhong, Y., Xu, M., Liang, K., Chen, K., Wu, M.: Ariadne's thread: Using text prompts to improve segmentation of infected areas from chest x-ray images. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 724–733. Springer (2023)
- <span id="page-10-6"></span>31. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Learning to prompt for vision-language models. International Journal of Computer Vision 130(9), 2337–2348 (2022)

Image /page/11/Picture/0 description: A square button with rounded corners contains a circular icon and the text "Check for updates". The icon is a circle with a bookmark shape inside it. The circle and bookmark are gray, and the background of the button is a lighter shade of gray with a subtle gradient. The text "Check for updates" is in a sans-serif font below the icon.

# RET-CLIP: A Retinal Image Foundation Model Pre-trained with Clinical Diagnostic Reports

Jiawei Du<sup>1</sup>, Jia Guo<sup>1</sup>, Weihang Zhang<sup>1</sup>, Shengzhu Yang<sup>1</sup>, Hanruo Liu<sup>1,2</sup>, Huiqi  $\text{Li}^{1(\boxtimes)}$ , and Ningli Wang<sup>2</sup>

 $<sup>1</sup>$  Beijing Institute of Technology, Beijing, China</sup> {jiaweidu,huiqili}@bit.edu.cn <sup>2</sup> Beijing Institute of Ophthalmology, Beijing Tongren Hospital, Capital Medical University, Beijing, China

Abstract. The Vision-Language Foundation model is increasingly investigated in the fields of computer vision and natural language processing, yet its exploration in ophthalmology and broader medical applications remains limited. The challenge is the lack of labeled data for the training of foundation model. To handle this issue, a CLIP-style retinal image foundation model is developed in this paper. Our foundation model, RET-CLIP, is specifically trained on a dataset of 193,865 patients to extract general features of color fundus photographs (CFPs), employing a tripartite optimization strategy to focus on left eye, right eye, and patient level to reflect real-world clinical scenarios. Extensive experiments demonstrate that RET-CLIP outperforms existing benchmarks across eight diverse datasets spanning four critical diagnostic categories: diabetic retinopathy, glaucoma, multiple disease diagnosis, and multilabel classification of multiple diseases, which demonstrate the performance and generality of our foundation model. The sourse code and pretrained model are available at [https://github.com/sStonemason/RET-](https://github.com/sStonemason/RET-CLIP.)[CLIP.](https://github.com/sStonemason/RET-CLIP.)

Keywords: Vision-Language Pre-training · Foundation Model · Retinal Fundus Image

# 1 Introduction

Foundation models trained on large-scale, multi-task datasets are now becoming increasingly popular and have achieved success in the fields of computer vision and natural language processing. Foundation models excel in generalization in feature extraction, offering significant potential for addressing the

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_66.](https://doi.org/10.1007/978-3-031-72390-2_66)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 709–719, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_66)\_66

complex challenges of clinical applications. However, the development of medical foundation models is still in its nascent phase, primarily hindered by the lack of high-quality data and concerns around patient privacy. Although initial efforts have been made  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$  $[6,9,11,12,19,23,24]$ , the effectiveness of these models, particularly in analyzing retina fundus images, has yet to meet expectations, underscoring the urgent need for focused advancements in this area.

In the clinical diagnosis and treatment of ocular diseases, medical imaging, such as color fundus photography (CFP), and the detailed image interpretations and diagnostic reports written by professional ophthalmologists are indispensable. This makes the clinics of ophthalmology inherently rich in imagetext multi-modality data, which holds significant potential for enhancing clinical applications. RETFound [\[25](#page-21-3)] is a foundation model for retinal images based on self-supervised learning. However, it solely utilizes image data and overlooks the equally vast amount of clinical diagnostic text. To address this limitation, CLIP [\[17](#page-21-4)], a powerful vision-language self-supervised paradigm, is widely explored in foundation models. By aligning the information of image and text in a shared representation space using a large corpus of image-text pairs, CLIP-style models can understand and associate visual content with natural language information. This results in feature representations with stronger generalization capabilities. Many studies focus on training vision-text models in the medical field [\[2](#page-20-4),[7,](#page-20-5)[9,](#page-20-1)[18](#page-21-5)– [20](#page-21-6)[,22](#page-21-7),[23\]](#page-21-1). PMC-CLIP [\[9](#page-20-1)] collects image-description pairs from large amount of scientific documents and trains a CLIP-style model based on them. FLAIR [\[18](#page-21-5)] is a pre-trained vision-language model designed to understand retinal fundus images. The textual data utilized in such research often comes from captions in medical papers or through the manual annotation of simple labels. However, clinical diagnostic reports, rich in valuable textual information, remain underutilized in this context.

Moreover, the conventional approaches often involve treating CFPs of individual eyes as separate entities during model training. This necessitates the extraction of information corresponding to each eye from the original clinical diagnostic reports, which may not always clearly differentiate between left and right eyes. The manual processing involved in this procedure requires specialized knowledge and could introduce errors and increase costs significantly due to the potential for human-induced noise. Conversely, considering both eyes of a patient together provides a more holistic and clinically meaningful approach in clinical scenarios.

To alleviate the above issues, we have the following contributions in this paper: Firstly, we propose a vision-language foundation model for CFPs, named RET-CLIP, which we believe is the first attempt to leverage clinical diagnostic reports to build a retinal foundation model, enriching the model's visual encoding capabilities with practicality and authenticity. The diagnostic reports in Chinese are included, extending the linguistic versatility of the research domain beyond English. Secondly, a novel strategy is proposed to decouple the information of left and right eyes in diagnostic reports, which is a simple yet effective paradigm for building a retinal foundation model. In practical scenarios, diagnostic reports

are usually patient-level, mixing information from both eyes, which brings a big challenge for directly using CLIP to build foundation models. The proposed monocular and patient-level contrastive learning approach can handle this challenge in the ophthalmology domain. Lastly, our model achieves state-of-the-art performance across diverse tasks and datasets, confirming the effectiveness of the proposed training strategy.

# 2 Method

## 2.1 Data Collection and Preprocessing

Dataset acquisition. We collected a dataset of retina fundus binocular imagestext triplets (RET-Clinical) at the patient level for RET-CLIP. The dataset includes a total of 193,865 samples from Beijing Tongren Hospital, Beijing, China. Each patient's triplet includes two CFPs for left and right eyes, alongside a clinical diagnostic report.

Data Preprocessing and Augmentation. For the CFPs, all of them are resized to  $512 \times 512$ . The augmentation includes random crop followed by resizing to  $224 \times 224$ , random horizontal flipping, color jitter, and image normalization. For diagnostic reports, we focus on correcting typos and consecutive punctuation errors caused by human input, restoring abbreviations to their full expressions, unifying mixed Chinese and English expressions into Chinese to align with our text encoder's language capabilities, and ensuring the text is coherent and grammatically correct by manual scrutiny. It's important to highlight that the preprocessing of text data only involves basic text standardization mentioned above, avoiding the need for advanced clinical knowledge or modifications that may alter the original content or meaning.

## 2.2 Model Architecture

As shown in Fig. [1,](#page-14-0) we trained a Visual-Language model called RET-CLIP under the CLIP paradigm using our constructed binocular images-text triplets. RET-CLIP consists of a visual encoder and a text encoder, which extract image features from CFPs and text features from clinical diagnostic reports, respectively. During pre-training, image-text contrastive learning is performed at the monocular and patient level jointly. Patient level examines data features from a holistic patient perspective, effectively leveraging the information in raw data while minimizing the interference of manual preprocessing in the pre-training phase. Concurrently, the binocular level guides the model towards acquiring finer-grained features than the patient level. Combined together, these methodologies can improve RET-CLIP's performance.

Given a mini-batch containing  $N$  binocular images-text triplets (i.e.,  $N$ patients),  $\mathcal{D} = \{(\mathcal{I}_1^l, \mathcal{I}_1^r, \mathcal{I}_1), \cdots, (\mathcal{I}_N^l, \mathcal{I}_N^r, \mathcal{I}_N)\},\$  where  $\mathcal{I}_i^l, \mathcal{I}_i^r$  and  $\mathcal{I}_i$  represents the CFP of left eye, the CFP of right eye and the diagnostic report of the ith

Image /page/14/Figure/1 description: This is a diagram illustrating a multimodal deep learning model for medical image analysis. The model consists of three main components: a Visual Encoder, a Similarity Calculation module, and a Text Encoder. The Visual Encoder processes images from the left eye (Imgs\_L) and right eye (Imgs\_R) using an 'Image' module, with 'Weight Sharing' indicated between the processing of left and right eye images. The outputs from the Visual Encoder are fed into the Similarity Calculation module, which computes similarity at three levels: Left Eye Level (L), Patient Level (P), and Right Eye Level (R). Each level involves a 'Feature Representation' matrix and an MLP. The Similarity Calculation module's outputs are then processed by the Text Encoder, which takes 'Text' as input and outputs a representation of patient-level information. The diagram also includes a 'Concatenation Operator' (C) and MLPs, with a legend explaining 'Feature Representation', 'MLP', 'Concatenation Operator', and the different levels (Left Eye Level, Right Eye Level, Patient Level).

<span id="page-14-0"></span>Fig. 1. Overview of the RET-CLIP foundation model.

patient, respectively. The visual encoder takes  $\mathcal{I}_i^l$  and  $\mathcal{I}_i^r$  as input, while the text encoder is fed with  $\mathcal{T}_i$ .

**Visual Encoder.** The left and right  $(\mathcal{I}^l, \mathcal{I}^r)$  CFPs for a patient are encoded to the embedding dimension of d using a ViT-based  $[5]$  encoder  $\Phi_v(\cdot)$  respectively:

$$
\boldsymbol{V}^{l} = \boldsymbol{\Phi}_{v}(\mathcal{I}^{l}) \in \mathbb{R}^{d}, \boldsymbol{V}^{r} = \boldsymbol{\Phi}_{v}(\mathcal{I}^{r}) \in \mathbb{R}^{d}.
$$
\n(1)

where  $V^l$  and  $V^r$  represent the image features of the left and right eye, respectively. Next, concatenation and a simple Multilayer Perceptron (MLP)  $F_v(\cdot)$  are employed to merge the image features of left and right eyes to derive comprehensive patient-level image features:

$$
\mathbf{V}^p = F_v(\mathbf{V}^l \oplus \mathbf{V}^r) \in \mathbb{R}^d, \tag{2}
$$

where ⊕ denotes concatenation.

**Text Encoder.** For a given patient's diagnostic report  $\mathcal{T}$ , a BERT-based [\[4](#page-20-7)] encoder  $\Phi_t(\cdot)$  is implemented to encode the clinical descriptions with a text token of length l:

$$
T = \Phi_t(T) \in \mathbb{R}^{l \times d}, \ T_0 \in \mathbb{R}^d,
$$
 (3)

where  $T$  denotes the sentence embedding,  $T_0$  denotes the embedding for [CLS] token. We then implement three stacked two-layer nonlinear MLPs  $F_l(\cdot), F_r(\cdot),$  $F_p(\cdot)$  to decouple  $T_0$  into textual features representing the left eye, right eye, and patient level, termed as  $T^l$ ,  $T^r$ , and  $T^p$ , respectively:

$$
\boldsymbol{T}^l = F_l(\boldsymbol{T}_0), \ \boldsymbol{T}^r = F_r(\boldsymbol{T}_0), \ \boldsymbol{T}^p = F_p(\boldsymbol{T}_0), \ \boldsymbol{T}^l, \boldsymbol{T}^r, \boldsymbol{T}^p \in \mathbb{R}^d. \tag{4}
$$

### 2.3 Training Objective

For the provided mini-batch, termed as  $D$ , the extracted feature set F, which is  $\{(\bm{V}_1^l, \bm{V}_1^r, \bm{V}_1^p, \bm{T}_1^l, \bm{T}_1^r, \bm{T}_1^p), \cdots, (\bm{V}_N^l, \bm{V}_N^r, \bm{V}_N^p, \bm{T}_N^l, \bm{T}_N^r, \bm{T}_N^p)\},$  is then divided into three subsets:  $\mathcal{F}^l = \{(\mathbf{V}^l_1, \mathbf{T}^l_1), \cdots, (\mathbf{V}^l_N, \mathbf{T}^l_N)\}, \mathcal{F}^r =$  $\{(V_1^r, T_1^r), \cdots, (V_N^r, T_N^r)\}\$ , and  $\mathcal{F}^p = \{(V_1^p, T_1^p), \cdots, (V_N^p, T_N^p)\}\$ , corresponding to left eye, right eye, and patient level, respectively. The image and text features of the same patient in each subset are positive samples of each other, while the rest are negative samples. The cosine similarity matrix is calculated on each subset.

For the subset of left eye features, we obtain the image feature matrix  $V^l$  =  $(\mathbf{V}_1^l, \dots, \mathbf{V}_N^l) \in \mathbb{R}^{N \times d}$  and the text feature matrix  $\mathbf{T}^l = (\mathbf{T}_1^l, \dots, \mathbf{T}_N^l) \in \mathbb{R}^{N \times d}$ . We measure the inter-sample similarity, termed as  $\mathbf{P}^{v2t}$  and  $\mathbf{P}^{t2v}$ , using the cosine distance  $S(\cdot)$ :

$$
\mathbf{P}^{v2t} = S(\mathbf{V}^l, \mathbf{T}^l) \in \mathbb{R}^{N \times N}, \ \mathbf{P}^{t2v} = S(\mathbf{T}^l, \mathbf{V}^l) \in \mathbb{R}^{N \times N}.
$$
 (5)

Then we calculate the contrastive loss of the left eye:

$$
\mathcal{L}_l = \frac{1}{2} (\text{CE}(\mathbf{P}^{v2t}, \mathbf{Y}^{v2t}) + \text{CE}(\mathbf{P}^{t2v}, \mathbf{Y}^{t2v}),
$$
\n(6)

where  $\mathbf{Y}^{v2t}$  and  $\mathbf{Y}^{t2v}$  represent the one-hot labels, CE refers to InfoNCE loss [\[13](#page-20-8)].

Then we calculate  $\mathcal{L}_r$  and  $\mathcal{L}_p$  for right eye and patient level based on  $\mathcal{F}^r$  and  $\mathcal{F}^p$  in the same way. The final loss is the sum of the above three:

$$
\mathcal{L} = \mathcal{L}_l + \mathcal{L}_r + \mathcal{L}_p. \tag{7}
$$

### 2.4 Implementation

The vision encoder utilizes the base-sized version of the vision transformer (ViTbase) [\[5](#page-20-6)], while the text encoder employs the base-sized version of RoBERTa (RoBERTa-base) [\[10\]](#page-20-9), both are initialized with the Chinese-CLIP weights [\[21\]](#page-21-8). AdamW is used as the optimizer. The batch size is 256, and training is performed using NVIDIA GeForce RTX 4090. The training process consists of 10 epochs, with the first 50 steps dedicated to warming up the model (from 0 to a learning rate of  $3 \times 10^{-5}$ ).

# 3 Experiments

### 3.1 Tasks and Datasets

We focus on designing downstream evaluation experiments primarily for visual tasks. These tasks contain four main categories: diagnosis of diabetic retinopathy, glaucoma, multiple diseases, and multi-label classification of multiple diseases.

For diabetic retinopathy diagnosis, IDRID [\[16\]](#page-21-9) and APTOS-2019 [\(https://](https://www.kaggle.com/competitions/aptos2019-blindness-detection/data) [www.kaggle.com/competitions/aptos2019-blindness-detection/data\)](https://www.kaggle.com/competitions/aptos2019-blindness-detection/data) are used. The labels for diabetic retinopathy are no, mild, moderate, severe, and proliferative retinopathy. The IDRID dataset comprises 516 images, while the APTOS dataset contains 3662 images.

For glaucoma diagnosis, PAPILA [\[8\]](#page-20-10) (488 images in total) and Glaucoma Fundus [\[1](#page-20-11)] (1544 images in total) are used. They both have three categorical labels, non-glaucoma, suspected glaucoma (early glaucoma), and glaucoma (advanced glaucoma).

For multiple disease diagnosis, JSIEC [\[3](#page-20-12)] (1000 in total) and Retina [\(https://](https://www.kaggle.com/datasets/jr2ngb/cataractdataset) [www.kaggle.com/datasets/jr2ngb/cataractdataset\)](https://www.kaggle.com/datasets/jr2ngb/cataractdataset) (601 in total) are tested. JSIEC contains 39 categories of common referable fundus diseases and conditions. Retina includes labels for normal, glaucoma, cataract, and other retinal diseases.

For multi-label classification of multiple diseases, RFMID [\[15](#page-21-10)] and ODIR [\(https://odir2019.grand-challenge.org/\)](https://odir2019.grand-challenge.org/) are tested. RFMID includes 3200 images with 28 categories of common referable fundus diseases and conditions. ODIR includes 10000 images (5000 patients' paired left and right eyes) with labels of normal, diabetic retinopathy, glaucoma, cataract,age-related macular degeneration (AMD), hypertension, myopia, and other diseases.

For the IDRIR, the entire dataset is officially divided into a test set comprising 20% of the data, with the remaining 80% designated as the training set. In our experiments, we further split the training set into a training set and a validation set using a 4:1 ratio. Similarly, for the PAPLA, we follow the official partitioning method, which aligns with the approach described above. Regarding the RFMID, the official division includes distinct sets for training, validation, and testing; we adhere to this official partitioning. For all other datasets, we divide them into training, validation, and test sets using a 0.56:0.14:0.3 ratio, following RETFound's [\[25](#page-21-3)] partitioning method. For all datasets, samples within each category are initially distributed based on the specified proportions before being combined to ensure consistent category distribution across the training, validation, and test sets.

When adapting to downstream tasks, the input image is mapped to a highlevel feature representation by the visual encoder. A simple linear prediction head is then applied, followed by a Sigmoid or Softmax layer to achieve classification.

For each task, two adaptation methods are implemented: linear probing, training the classifier only with the encoder frozen, and fine-tuning, where both the encoder and classifier are trained. Each evaluation process consists of 50 epochs with a batch size of 16. The model weights with the best performance on the validation set are saved for testing.

## 3.2 Comparision Methods and Evaluation Metrics

To demonstrate the superiority of our method, we compare two broad categories of models: foundation models trained on non-CFP datasets (Chinese-CLIP [\[21\]](#page-21-8), PMC-CLIP [\[9\]](#page-20-1), DINOv2 [\[14\]](#page-21-11)) and models designed for CFP vision tasks (RET-Found [\[25\]](#page-21-3), FLAIR [\[18\]](#page-21-5)).

We use the area under the receiver operating curve (AUROC) and area under the precision-recall curve (AUPR) as the evaluation metrics. We evaluate five iterations with different random seeds for each model on each downstream dataset to calculate the mean values. We also conduct the t-test for each downstream task to determine the significance level at which the top-performing method surpasses the others (see Supplementary Materials).

## 3.3 Result

RET-CLIP outperforms five comparison models across eight datasets (four categories) as introduced before, demonstrating strong generalization capabilities.

For linear probing, the results are shown in Table [1](#page-17-0) and Table [2.](#page-18-0) RET-CLIP demonstrates superior performance on almost all datasets, which indicates that RET-CLIP has learned a rich feature representation during the pre-training phase, demonstrating the capability to capture high-quality features.

<span id="page-17-0"></span>Table 1. Diabetic retinopathy and glaucoma diagnosis results for linear probing. The best results on each metric are highlighted in bold.

| Models        | IDRID        |              | APTOS2019    |              | PAPILA       |              | Glaucoma Fundus |              |
|---------------|--------------|--------------|--------------|--------------|--------------|--------------|-----------------|--------------|
|               | AUROC        | AUPR         | AUROC        | AUPR         | AUROC        | AUPR         | AUROC           | AUPR         |
| CN-CLIP [21]  | 0.633        | 0.336        | 0.806        | 0.429        | 0.658        | 0.473        | 0.863           | 0.716        |
| PMC-CLIP [9]  | 0.585        | 0.303        | 0.756        | 0.368        | 0.773        | 0.603        | 0.899           | 0.780        |
| DinoV2 [14]   | 0.748        | 0.463        | 0.783        | 0.432        | 0.740        | 0.556        | 0.891           | 0.746        |
| RETFound [25] | 0.665        | 0.368        | 0.745        | 0.370        | 0.620        | 0.511        | 0.899           | 0.773        |
| FLAIR [18]    | 0.700        | 0.475        | 0.849        | 0.515        | 0.746        | 0.595        | 0.872           | 0.672        |
| <b>OURS</b>   | <b>0.856</b> | <b>0.616</b> | <b>0.923</b> | <b>0.656</b> | <b>0.775</b> | <b>0.667</b> | <b>0.893</b>    | <b>0.789</b> |

For fine-tuning, as shown in Table [3](#page-18-1) and Table [4,](#page-19-0) RET-CLIP demonstrates superior performance across nearly all tasks. This outcome substantiates RET-CLIP's robust feature extraction and generalization capabilities. Furthermore, it suggests that RET-CLIP not only captures high-quality features but also exhibits strong adaptability, enabling effective customization for specific tasks.

It's noteworthy that the previous foundation models designed for CFPs do not exhibit an advantage over models trained on non-CFP datasets. RETFound's [\[25](#page-21-3)] image reconstruction-focused paradigm may prioritize features related to the rebuilding of CFP, which lack the granularity and quality needed for specific downstream tasks, hindering its broader applicability. FLAIR [\[18](#page-21-5)], while is a CLIP-style model, does not suit ophthalmic tasks as it uses the text provision

| Models        | JSIEC        |              | Retina       |              | RFMID        |              | ODIR         |              |
|---------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|               | AUROC        | AUPR         | AUROC        | AUPR         | AUROC        | AUPR         | AUROC        | AUPR         |
| CN-CLIP [21]  | 0.783        | 0.239        | 0.738        | 0.514        | 0.819        | 0.293        | 0.801        | 0.483        |
| PMC-CLIP [9]  | 0.947        | 0.654        | 0.778        | 0.597        | 0.854        | 0.372        | 0.800        | 0.506        |
| DinoV2 [14]   | 0.873        | 0.446        | 0.813        | 0.635        | 0.860        | 0.430        | 0.825        | 0.550        |
| RETFound [25] | 0.704        | 0.167        | 0.630        | 0.434        | 0.842        | 0.409        | 0.738        | 0.401        |
| FLAIR [18]    | 0.843        | 0.304        | 0.773        | 0.557        | 0.773        | 0.254        | 0.858        | 0.531        |
| <b>OURS</b>   | <b>0.982</b> | <b>0.855</b> | <b>0.935</b> | <b>0.864</b> | <b>0.925</b> | <b>0.552</b> | <b>0.902</b> | <b>0.682</b> |

<span id="page-18-0"></span>Table 2. Multiple disease diagnosis and multi-label classification of multiple diseases results for linear probing.

<span id="page-18-1"></span>Table 3. Diabetic retinopathy and glaucoma diagnosis results for fine-tuning.

| Models        | IDRID        |              | APTOS2019    |              | PAPILA       |              | Glaucoma Fundus |              |
|---------------|--------------|--------------|--------------|--------------|--------------|--------------|-----------------|--------------|
|               | AUROC        | AUPR         | AUROC        | AUPR         | AUROC        | AUPR         | AUROC           | AUPR         |
| CN-CLIP [21]  | 0.778        | 0.506        | 0.881        | 0.619        | 0.804        | 0.690        | 0.951           | 0.876        |
| PMC-CLIP [9]  | 0.785        | 0.511        | 0.776        | 0.386        | 0.798        | 0.659        | 0.925           | 0.827        |
| DinoV2 [14]   | 0.791        | 0.533        | 0.920        | 0.675        | 0.797        | 0.681        | 0.955           | 0.884        |
| RETFound [25] | 0.822        | 0.496        | 0.943        | 0.726        | <b>0.855</b> | 0.748        | 0.943           | 0.863        |
| FLAIR [18]    | 0.795        | 0.529        | 0.932        | 0.686        | 0.752        | 0.610        | 0.905           | 0.792        |
| <b>OURS</b>   | <b>0.863</b> | <b>0.630</b> | <b>0.951</b> | <b>0.748</b> | <b>0.853</b> | <b>0.754</b> | <b>0.958</b>    | <b>0.889</b> |

method employed by the original CLIP [\[17\]](#page-21-4), which is designed for natural contexts, offering limited textual insights from single labels. Moreover, its dependence on public datasets for training constrains its performance due to their limited scale and quality. In contrast, RET-CLIP leverages rich textual information from clinical reports to extract detailed features for ophthalmic tasks better, showcasing the benefits of integrating diagnostic reports into the training of medical CLIP-style models.

## 3.4 Ablation Study

The results, as shown in Table [5,](#page-19-1) confirm the effectiveness of optimizing objectives at both monocular and patient levels. As previously discussed, the combination of the global information provided at the patient level with the finer-grained features contributed at the monocular level is essential to achieve optimal performance.

| Models        | JSIEC        |              | Retina       |              | RFMID        |              | ODIR         |              |
|---------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|               | AUROC        | AUPR         | AUROC        | AUPR         | AUROC        | AUPR         | AUROC        | AUPR         |
| CN-CLIP [21]  | 0.992        | 0.882        | 0.839        | 0.691        | 0.901        | 0.480        | 0.859        | 0.598        |
| PMC-CLIP [9]  | 0.964        | 0.738        | 0.875        | 0.742        | 0.894        | 0.456        | 0.819        | 0.542        |
| DinoV2 [14]   | 0.996        | 0.918        | 0.893        | 0.771        | 0.914        | 0.547        | 0.867        | 0.621        |
| RETFound [25] | 0.990        | 0.884        | 0.847        | 0.697        | 0.889        | 0.489        | 0.850        | 0.620        |
| FLAIR [18]    | 0.917        | 0.704        | 0.863        | 0.679        | 0.870        | 0.397        | 0.860        | 0.601        |
| <b>OURS</b>   | <b>0.999</b> | <b>0.972</b> | <b>0.942</b> | <b>0.871</b> | <b>0.946</b> | <b>0.581</b> | <b>0.917</b> | <b>0.715</b> |

<span id="page-19-0"></span>Table 4. Multiple disease diagnosis and multi-label classification of multiple diseases results for fine-tuning.

<span id="page-19-1"></span>**Table 5.** Results of ablation studies. Monocular-level loss refers to  $\mathcal{L}_l$  plus  $\mathcal{L}_r$ .

|                      |              | <b>AUROC</b> |       |              | <b>AUPR</b> |       |
|----------------------|--------------|--------------|-------|--------------|-------------|-------|
| Monocular-level Loss | $\checkmark$ |              | √     | ✓            |             | ✓     |
| Patient-level Loss   | ✓            | ✓            |       | $\checkmark$ | ✓           |       |
| <b>IDRID</b>         | 0.863        | 0.860        | 0.847 | 0.63         | 0.623       | 0.619 |
| $APTOS-2019$         | 0.951        | 0.945        | 0.941 | 0.748        | 0.737       | 0.759 |
| <b>PAPILA</b>        | 0.853        | 0.864        | 0.846 | 0.754        | 0.745       | 0.739 |
| Glaucoma Fundus      | 0.958        | 0.948        | 0.957 | 0.889        | 0.869       | 0.888 |
| JSIEC                | 0.999        | 0.997        | 0.997 | 0.972        | 0.949       | 0.962 |
| Retina               | 0.942        | 0.939        | 0.935 | 0.871        | 0.869       | 0.876 |
| <b>RFMID</b>         | 0.946        | 0.924        | 0.940 | 0.581        | 0.573       | 0.578 |
| ODIR.                | 0.917        | 0.909        | 0.905 | 0.715        | 0.692       | 0.696 |

# 4 Conclusion

In this study, we compile a binocular images-text dataset, RET-Clinical, derived from 193,865 clinical patients, with which, we jointly optimize and pre-train a CLIP-style model, RET-CLIP, cooperating with the information of left eye, right eye, and patient level. RET-CLIP achieves state-of-the-art results across eight downstream tasks spanning four critical diagnostic categories. Our research narrows the existing void in ophthalmic vision-language models by integrating textual data from clinical diagnostic reports, thereby offering insights into the applicability of raw clinical texts in the wider medical domain.

Acknowledgments. This research work is supported by Beijing Natural Science Foundation (Grant No. IS23112), National Natural Science Foundation of China (NSFC) (Grant No. 82072007), and Beijing Institute of Technology Research Fund Program for Young Scholars.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-20-11"></span>1. Ahn, J.M., Kim, S., Ahn, K.S., Cho, S.H., Lee, K.B., Kim, U.S.: A deep learning model for the detection of both advanced and early glaucoma using fundus photography. PloS one 13(11), e0207982 (2018)
- <span id="page-20-4"></span>2. Baliah, S., Maani, F.A., Sanjeev, S., Khan, M.H.: Exploring the transfer learning capabilities of clip in domain generalization for diabetic retinopathy. In: International Workshop on Machine Learning in Medical Imaging. pp. 444–453. Springer (2023)
- <span id="page-20-12"></span>3. Cen, L.P., Ji, J., Lin, J.W., Ju, S.T., Lin, H.J., Li, T.P., Wang, Y., Yang, J.F., Liu, Y.F., Tan, S., et al.: Automatic detection of 39 fundus diseases and conditions in retinal photographs using deep neural networks. Nature communications  $12(1)$ , 4828 (2021)
- <span id="page-20-7"></span>4. Devlin, J., Chang, M.W., Lee, K., Toutanova, K.: Bert: Pre-training of deep bidirectional transformers for language understanding. arXiv preprint [arXiv:1810.04805](http://arxiv.org/abs/1810.04805) (2018)
- <span id="page-20-6"></span>5. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth  $16x16$  words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-20-0"></span>6. Eslami, S., Meinel, C., De Melo, G.: Pubmedclip: How much does clip benefit visual question answering in the medical domain? In: Findings of the Association for Computational Linguistics: EACL 2023. pp. 1151–1163 (2023)
- <span id="page-20-5"></span>7. Huang, S.C., Shen, L., Lungren, M.P., Yeung, S.: Gloria: A multimodal global-local representation learning framework for label-efficient medical image recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 3942–3951 (2021)
- <span id="page-20-10"></span>8. Kovalyk, O., Morales-Sánchez, J., Verdú-Monedero, R., Sellés-Navarro, I., Palazón-Cabanes, A., Sancho-Gómez, J.L.: Papila: Dataset with fundus images and clinical data of both eyes of the same patient for glaucoma assessment. Scientific Data 9(1), 291 (2022)
- <span id="page-20-1"></span>9. Lin, W., Zhao, Z., Zhang, X., Wu, C., Zhang, Y., Wang, Y., Xie, W.: Pmc-clip: Contrastive language-image pre-training using biomedical documents. arXiv preprint [arXiv:2303.07240](http://arxiv.org/abs/2303.07240) (2023)
- <span id="page-20-9"></span>10. Liu, Y., Ott, M., Goyal, N., Du, J., Joshi, M., Chen, D., Levy, O., Lewis, M., Zettlemoyer, L., Stoyanov, V.: Roberta: A robustly optimized bert pretraining approach. arXiv preprint [arXiv:1907.11692](http://arxiv.org/abs/1907.11692) (2019)
- <span id="page-20-2"></span>11. Moor, M., Banerjee, O., Abad, Z.S.H., Krumholz, H.M., Leskovec, J., Topol, E.J., Rajpurkar, P.: Foundation models for generalist medical artificial intelligence. Nature 616(7956), 259–265 (2023)
- <span id="page-20-3"></span>12. Moor, M., Huang, Q., Wu, S., Yasunaga, M., Dalmia, Y., Leskovec, J., Zakka, C., Reis, E.P., Rajpurkar, P.: Med-flamingo: a multimodal medical few-shot learner. In: Machine Learning for Health (ML4H). pp. 353–367. PMLR (2023)
- <span id="page-20-8"></span>13. Oord, A.v.d., Li, Y., Vinyals, O.: Representation learning with contrastive predictive coding. arXiv preprint [arXiv:1807.03748](http://arxiv.org/abs/1807.03748) (2018)

- <span id="page-21-11"></span>14. Oquab, M., Darcet, T., Moutakanni, T., Vo, H., Szafraniec, M., Khalidov, V., Fernandez, P., Haziza, D., Massa, F., El-Nouby, A., et al.: Dinov2: Learning robust visual features without supervision. arXiv preprint [arXiv:2304.07193](http://arxiv.org/abs/2304.07193) (2023)
- <span id="page-21-10"></span>15. Pachade, S., Porwal, P., Thulkar, D., Kokare, M., Deshmukh, G., Sahasrabuddhe, V., Giancardo, L., Quellec, G., Mériaudeau, F.: Retinal fundus multi-disease image dataset (rfmid): A dataset for multi-disease detection research. Data  $6(2)$ , 14 (2021)
- <span id="page-21-9"></span>16. Porwal, P., Pachade, S., Kokare, M., Deshmukh, G., Son, J., Bae, W., Liu, L., Wang, J., Liu, X., Gao, L., et al.: Idrid: Diabetic retinopathy–segmentation and grading challenge. Medical image analysis 59, 101561 (2020)
- <span id="page-21-4"></span>17. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-21-5"></span>18. Silva-Rodriguez, J., Chakor, H., Kobbi, R., Dolz, J., Ayed, I.B.: A foundation language-image model of the retina (flair): Encoding expert knowledge in text supervision. arXiv preprint [arXiv:2308.07898](http://arxiv.org/abs/2308.07898) (2023)
- <span id="page-21-0"></span>19. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. arXiv preprint [arXiv:2210.10163](http://arxiv.org/abs/2210.10163) (2022)
- <span id="page-21-6"></span>20. Wu, C., Zhang, X., Zhang, Y., Wang, Y., Xie, W.: Medklip: Medical knowledge enhanced language-image pre-training. medRxiv pp. 2023–01 (2023)
- <span id="page-21-8"></span>21. Yang, A., Pan, J., Lin, J., Men, R., Zhang, Y., Zhou, J., Zhou, C.: Chinese clip: Contrastive vision-language pretraining in chinese. arXiv preprint [arXiv:2211.01335](http://arxiv.org/abs/2211.01335) (2022)
- <span id="page-21-7"></span>22. Zhang, X., Wu, C., Zhao, Z., Lin, W., Zhang, Y., Wang, Y., Xie, W.: Pmc-vqa: Visual instruction tuning for medical visual question answering. arXiv preprint [arXiv:2305.10415](http://arxiv.org/abs/2305.10415) (2023)
- <span id="page-21-1"></span>23. Zhang, Y., Jiang, H., Miura, Y., Manning, C.D., Langlotz, C.P.: Contrastive Learning of Medical Visual Representations from Paired Images and Text (Sep 2022)
- <span id="page-21-2"></span>24. Zhong, T., Zhao, W., Zhang, Y., Pan, Y., Dong, P., Jiang, Z., Kui, X., Shang, Y., Yang, L., Wei, Y., et al.: Chatradio-valuer: A chat large language model for generalizable radiology report generation based on multi-institution and multisystem data. arXiv preprint [arXiv:2310.05242](http://arxiv.org/abs/2310.05242) (2023)
- <span id="page-21-3"></span>25. Zhou, Y., Chia, M.A., Wagner, S.K., Ayhan, M.S., Williamson, D.J., Struyven, R.R., Liu, T., Xu, M., Lozano, M.G., Woodward-Court, P., et al.: A foundation model for generalizable disease detection from retinal images. Nature 622(7981), 156–163 (2023)

Image /page/22/Picture/0 description: A square button with a light gray background. In the center of the button, there is a circular icon with a bookmark symbol inside. Below the icon, the text "Check for updates" is displayed in a gray sans-serif font.

# **S-SAM: SVD-Based Fine-Tuning of Segment Anything Model for Medical Image Segmentation**

Jay N. Paranjape<sup>1( $\boxtimes$ )</sup>, Shameema Sikder<sup>2,3</sup>, S. Swaroop Vedula<sup>3</sup>, and Vishal M. Patel<sup>1</sup>

<sup>1</sup> Department of Electrical and Computer Engineering, The Johns Hopkins University, Baltimore, USA

<EMAIL>

<sup>2</sup> Wilmer Eye Institute, The Johns Hopkins University, Baltimore, USA

<sup>3</sup> Malone Center for Engineering in Healthcare, The Johns Hopkins University, Baltimore, USA

**Abstract.** Medical image segmentation has been traditionally approached by training or fine-tuning the entire model to cater to any new modality or dataset. However, this approach often requires tuning a large number of parameters during training. With the introduction of the Segment Anything Model (SAM) for prompted segmentation of natural images, many efforts have been made towards adapting it efficiently for medical imaging, thus reducing the training time and resources. However, these methods still require expert annotations for every image in the form of point prompts or bounding box prompts during training and inference, making it tedious to employ them in practice. In this paper, we propose an adaptation technique, called S-SAM, that only trains parameters equal to 0.4% of SAM's parameters and at the same time uses simply the label names as prompts for producing precise masks. This not only makes tuning SAM more efficient than the existing adaptation methods but also removes the burden of providing expert prompts.We call this modified version S-SAM and evaluate it on five different modalities including endoscopic images, x-ray, ultrasound, CT, and histology images. Our experiments show that S-SAM outperforms state-of-the-art methods as well as existing SAM adaptation methods while tuning a significantly less number of parameters. We release the code for S-SAM at [https://github.com/JayParanjape/SVDSAM.](https://github.com/JayParanjape/SVDSAM)

**Keywords:** Blackbox Adaptation · Prompted Segmentation

# 1 Introduction

Image segmentation is a fundamental task in medical image analysis. Various deep learning (DL) based algorithms have been proposed in the literature that

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_67) 67.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 720–730, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_67)\_67

are able to perform remarkably well on a wide variety of modalities including CT, MRI, X-ray, ultrasound and endoscopy for segmenting organs, tumors, and tissues [\[2,](#page-30-0)[32\]](#page-32-0). However, most of the well-performing models inherently have a large number of parameters, thus increasing the resources needed to train them every time for a new dataset or a new modality. This problem has been tackled for natural images to some extent by foundational models which are trained on billions of data points, exhibit an innate understanding of a given task, and are able to generalize well, given suitable user prompts. Notable examples include CLIP [\[22](#page-31-0)] and ALIGN [\[11](#page-31-1)] for image classification and open set image-text understanding, and the recently proposed Segment Anything Model (SAM) [\[13\]](#page-31-2) for segmentation. As such, it was hoped that medical image segmentation could also be addressed using SAM. However, SAM is not trained on medical images and various studies show its inability to generalize well on medical data [\[9](#page-31-3),[21\]](#page-31-4). In order to utilize the power of SAM on medical images, there have been various efforts to adapt it efficiently [\[18](#page-31-5)[,19](#page-31-6)[,24](#page-32-1),[33\]](#page-32-2). However, it is not feasible to provide expert-level prompts for every image during training or inference, as is required by these adaptation methods since this process is time-consuming for experts. In our work, we propose S-SAM - an adaptation of SAM that only expects the name of the class of interest as a prompt, thus eliminating the need for expert-level prompts. This property makes it more suitable for usage in medical systems while also retaining the promptable nature of SAM. Furthermore, we show that our approach is significantly more efficient and requires training far fewer parameters than existing methods. In the proposed approach, we tune the singular values of the weight matrices in SAM's image encoder. Singular values define how important each visual feature in the image is to the activation of a given layer of the model [\[29\]](#page-32-3) and hence, modifying these during training allows S-SAM to correctly adapt SAM for a given task.

In summary, the main contributions of our paper are as follows. (1) We propose a novel adaptation of SAM, called S-SAM, that can perform text-prompted segmentation of medical images. S-SAM expects prompts as simple as the name of the class of interest to produce precise masks, thus removing the requirement of expert-level prompts. (2) We develop a technique for training S-SAM that makes it far more efficient than existing adaptation methods. In comparison with the original SAM, the number of trainable parameters for S-SAM are only 0.4%. (3) Extensive experiments are conducted on five publicly available datasets of different modalities where we obtain the SOTA performance.

# 2 Related Work

While SAM is a powerful tool for natural image segmentation, it needs to be adapted to perform well in the medical domain. Hence, much research has been conducted to harness the power of SAM's encoders and decoders for medical images. One of the first efforts in this direction was MedSAM [\[19](#page-31-6)], where the authors finetune SAM for a huge corpus of medical images and provide support for point and bounding box-based prompts. While this approach finetunes all the parameters of SAM, the Medical SAM Adapter [\[33](#page-32-2)] introduces learnable adapter layers for all the encoder and decoder blocks. These are low-rank approximations that allow learning the domain shift while freezing all other parameters of SAM. While this method significantly brings down the GPU requirement from 1024 (for SAM) to 4, it can still be considered compute-intensive. SonoSAM [\[24\]](#page-32-1) tunes the mask decoder and prompt encoder of SAM for training on sonography images. SAMUS [\[18](#page-31-5)], on the other hand, adapts SAM for ultrasound images by training an additional CNN-based image encoder and fusing it with SAM's encoder. We refer readers to a comprehensive survey of SAM-adaptation methods [\[15\]](#page-31-7) for more details. However, these approaches still require experts to manually provide precise point prompts or bounding box prompts for every image during training and testing, which is tedious. Hence, an adaptation of SAM that minimizes expert involvement would be highly beneficial for medical applications. In light of this, SAMed [\[35\]](#page-32-4) was introduced, which added trainable low-rank adaptation layers to SAM's image encoder while keeping the rest of the encoder weights frozen. In addition, the user-defined prompts were replaced with a default prompt to eliminate expert involvement. Similarly, AutoSAM [\[26\]](#page-32-5) replaces the user-defined prompt with the image itself, and trains the prompt encoder to produce good prompt embeddings with the image input. However, these techniques remove the promptable nature of SAM. To tackle this, AdaptiveSAM [\[21](#page-31-4)] introduces text-prompted segmentation which allows the use of label names as the prompt.

To perform this, they tune the biases of the encoder network and keep the decoder fully trainable while also adding a text affine layer, producing good results for surgical scene segmentation. Using text prompts can be considered as requiring no expert involvement since simply the label names can serve as a valid text prompt. Hence, in S-SAM, we also perform text-prompted segmentation. However, our

<span id="page-24-0"></span>**Table 1.** Comparison of different adaptation methods of SAM present in the literature. Here, expert intervention means providing expert prompts for every image, including points, bounding boxes or masks. We exclude text prompts with label names from this category since experts are not required to provide the label name prompts for every image, but only once for the entire dataset.

| Method                   | Expert Intervention Not Required | Promptable | Training Decoder Not Required |
|--------------------------|----------------------------------|------------|-------------------------------|
| MedSAM [19]              | X                                | ✓          | X                             |
| Medical SAM Adapter [33] | X                                | ✓          | X                             |
| SonoSAM [24]             | X                                | ✓          | X                             |
| SAMUS [18]               | X                                | ✓          | ✓                             |
| SAMed [35]               | ✓                                | X          | X                             |
| AutoSAM [26]             | ✓                                | X          | ✓                             |
| AdaptiveSAM [21]         | ✓                                | ✓          | X                             |
| S-SAM (Ours)             | ✓                                | ✓          | ✓                             |

method does not require the mask decoder of SAM to be tuned. Hence, it is more efficient than AdaptiveSAM. Furthermore, by tuning the singular values instead of biases, our model does a better job of learning the domain shift. In summary, an appropriate adaptation method for SAM should minimize expert involvement. It should allow for some form of prompting to facilitate interactivity as needed. Finally, it should be as efficient as possible. Our method satisfies all of these requirements, as shown in Table [1.](#page-24-0)

# 3 Proposed Method

**SVD-Based Tuning:** The image encoder in SAM is a chain of N blocks, each of which has a multi-head attention module followed by a Multi Layer Perceptron (MLP), which perform the following computation

$$
qkv = (W_{qkv}^n)x + b_{qkv}^n, \ \ o_n = (W_M^n)x + b_M^n,
$$
\n(1)

where  $q, k$  and v denote the query, key and value associated with the multi-head attention and M denotes the MLP layer. o denotes the output of the block and x denotes the input to a module. Here,  $n$  is used to index the number of blocks (N). Note that  $W \in \mathbb{R}^{D \times K}$  and  $b \in \mathbb{R}^{D \times 1}$  denote the weights and biases of the respective modules. Here, D represents the dimension of the input to the module (multi-head attention or MLP) and  $K$  represents the output dimension of the respective module. These weights are primarily responsible for learning how to encode natural images after being extensively trained on them. To adapt them to the medical domain, we propose tuning W as follows:

$$
W \leftarrow UReLU(A \odot \Sigma + B)V^{T}, \text{ where } W = U\Sigma V^{T}, \tag{2}
$$

where  $W = U\Sigma V^T$  denotes the Singular Value Decomposition (SVD) of the weight matrix  $W$  and  $\odot$  represents the element-wise multiplication. A and B are matrices with the same shape as  $\Sigma$  with non-diagonal entries as 0.

In other words, we compute the singular values of any weight matrix in the image encoder and learn a non-linear transformation over them. Since A is multiplied elementwise, it represents scaling of the singular values while B is added and thus represents shifting of the singular values. Finally, to maintain the positive semidefinite nature of the  $\Sigma$  matrix, we apply a ReLU operation. This operation is illustrated in Fig. [2](#page-26-0) (c).

**S-SAM - Architecture:** An overview of S-SAM's architecture can be found in Fig. [1.](#page-25-0) S-SAM consists of an image encoder, prompt encoder and the mask decoder. An additional module called the Text Affine Layer (TAL) is also added before the

Image /page/25/Figure/9 description: This is a diagram illustrating a deep learning model for medical image segmentation. The model consists of a prompt encoder, an image encoder, and a mask decoder. The prompt encoder takes a text embedding of 'Liver' and processes it through a Text Affine Layer and a SAM Prompt Encoder, which is frozen. The image encoder takes a medical image of a liver, processes it through normalization, multi-head attention, and an MLP, with a component for singular value tuning (W = U x Sigma x V^T). The image encoder's output is combined with the prompt encoder's output and fed into a SAM Decoder, which is also frozen, to produce a segmentation mask of the liver. A legend indicates that the Text Affine Layer and the MLP are trainable, while the SAM Prompt Encoder and SAM Decoder are frozen. The diagram also shows summation operations and normalization layers within the model architecture.

<span id="page-25-0"></span>**Fig. 1.** S-SAM Architecture. The image encoder weights are modified by performing a transformation over their singular values. Other trainable parameters include the layernorms and positional embeddings in the encoder and the Text Affine Layer (TAL). Everything else is frozen and initialized with SAM's pre-trained checkpoint.

prompt encoder. The inputs to S-SAM include an image and a text prompt (which can be the label name itself) and the output is a mask over the region described by the text. The image encoder of S-SAM is SAM's original image encoder, albeit with all the weights modified as described in the previous subsection. The image is fed into this modified encoder which outputs the image embeddings. Similarly, for the text prompt, we first obtain the CLIP embedding. However, both CLIP and SAM were trained on natural images and hence might not capture the medical labels correctly. Therefore, we pass the CLIP embedding through TAL (a learnable one-layer MLP), before passing it to the prompt encoder, which outputs the prompt embeddings. Finally, we use the mask decoder to fuse the image and prompt embeddings to generate the mask of interest.

In this setup, all the weights of the image encoder, prompt encoder and mask decoder are initialized with SAM's pre-trained checkpoint. The non-zero elements of A corresponding to every weight matrix are initialized with one and B is initialized with zeros. During training, CLIP, the mask decoder and the prompt encoder are completely frozen. All the weights of the image encoder are frozen and only the transform parameters  $A$  and  $B$  are learnable. In addition, positional embeddings are trainable to allow training with smaller resolutions and layernorm layers are trainable to better adapt to the new domain. The positional embeddings in SAM's checkpoint expect the input image size to be  $1024 \times 1024$ . Hence, many adaptation techniques upsample the images to this scale. However, this significantly adds on to the memory requirements of these approaches. We replace SAM's positional embeddings with learnable embeddings that can facilitate training with lower image sizes. To retain the information contained in SAM, we initialize these by performing an AveragePool operation over the embedding weights present in SAM's checkpoint to bring them to the required size. Finally, the TAL is also trainable.

### Comparison with LoRA:

Low Rank Adaptation (LoRA) is a highly effective technique for finetuning large language models for various applications [\[5](#page-31-8),[10,](#page-31-9)[34\]](#page-32-6). This concept involves adding a product of two low-rank matrices  $X \in \mathbb{R}^{D \times r}$ and  $Y \in \mathbb{R}^{r \times K}$  to W as follows:  $W \leftarrow W +$ XY. However, this may lead to underfitting if the learnt subspace is smaller than required [\[35](#page-32-4)]. Thus, the effectiveness of this

Image /page/26/Figure/5 description: The image displays three methods for parameter-efficient fine-tuning of a weight matrix W of size D x K. Method (a), General Finetuning, shows W as a single trainable parameter block with DK trainable parameters. Method (b), LoRA, decomposes W into two low-rank matrices X (D x r) and Y (r x K), where r is the rank. The output is WX + Y, with r(D+K) trainable parameters. Method (c), S-SAM, uses Singular Value Decomposition (SVD) to represent W as U \* Sigma \* V^T, where U is D x D, Sigma is D x D with diagonal singular values, and V^T is K x K. S-SAM updates only the singular values and two low-rank matrices, resulting in 2\*min(D,K) trainable parameters. The image uses fire emojis to indicate trainable parameters and snowflake emojis to indicate frozen parameters.

<span id="page-26-0"></span>**Fig. 2.** Comparison of different fine-tuning methods. (a) Naive fine-tuning (b) LoRA (c) Our approach only tunes the singular values and is even more efficient than LoRA.

method largely depends on the rank of the approximated matrices. In S-SAM, all the singular values are tuned resulting in a full-rank computation, thus avoiding this problem. Furthermore, LoRA involves fine-tuning all the parameters of the low-rank matrices while S-SAM only tunes the singular values. This makes our approach more efficient than LoRA. Assuming W is  $D \times K$ , the rank of W is

given by min( $D, K$ ). In LoRA, the learnt matrices  $X \in \mathbb{R}^{DXr}$  and  $Y \in \mathbb{R}^{r \times K}$ have rank  $r \ll \min(D, K)$ . This makes the number of trainable parameters equal to  $r(D + K)$  as shown in Fig. [2.](#page-26-0) On the other hand, S-SAM only tunes the singular values of  $W$  by learning the scale  $A$  and shift  $B$  parameters. Hence, the number of trainable parameters amounts to  $2 \times \min(D, K)$ , which is lesser than LoRA.

# 4 Experimental Results

**Datasets:** We evaluate S-SAM on the following five medical imaging datasets corresponding to different modalities. (i) CholecSeg8k [\[8\]](#page-31-10) consists of endoscopic surgery images with 12 classes of interest including surgical instruments, organs and tissues. (ii) The abdominal ultrasound dataset [\[31\]](#page-32-7) consists of simulated ultrasound images and has 8 classes of interest denoting different organs and bones. The testing data consists of a mix of real and synthetic images. (iii) ChestXDet [\[17](#page-31-11)] has x-ray images with 13 classes of interest representing malignancies in the chest region. (iv) The LiTS Dataset [\[20](#page-31-12)] consists of Computed Tomography (CT) images with the classes of interest being the liver and the tumor region. S-SAM takes in a 2D image as an input. Hence, we use its derived dataset having 2D slices found at [\[20\]](#page-31-12). (v) The GLAS challenge dataset [\[28\]](#page-32-8) comprises of histology images. There is only one class of interest, namely the glands, which needs to be segmented. The rest of the experimental setup is outlined in the supplementary.

**Results:** In clinical practice, a blank mask, corresponds to the label of interest that is not present in the image. Note that a blank mask is a valid prediction. For example, if a CT scan of a normal liver is queried with the text prompt "Tumor", it should output an empty mask. In the classical definition of DICE Score (DSC), such cases have undefined score and hence, ignored. Hence, for each of the datasets, we evaluate S-SAM using the Dice Score as defined by Rahman et al. [\[23\]](#page-32-9) who give a DSC of 1 to cases

Image /page/27/Figure/5 description: This image displays a grid of medical images and their corresponding segmentation masks generated by different models. The rows are labeled with anatomical structures or conditions: Gastrointestinal Tract, Liver, Consolidation, Liver, and Glands. The columns represent different stages or methods: Inputs, Ground Truth, S-SAM (Ours), Adaptive SAM, LoRA, and SAM w/ point prompt. The first column shows the original input images, which include laparoscopic views, ultrasound, X-ray, CT scan, and histology slides. The subsequent columns show segmentation masks, with the 'Ground Truth' column displaying the manually annotated masks. The remaining columns show the segmentation results from various models, highlighting their performance in segmenting the specified regions. Some columns, particularly the last one, also include visual representations of prompts or confidence scores.

<span id="page-27-0"></span>**Fig. 3.** A qualitative comparison among different methods. From the top, the rows represent CholecSeg8k, Ultrasound, ChestXDet, LiTS, and GLAS, respectively. The green dot in the last column denotes the point prompt used to query SAM.

<span id="page-28-0"></span>**Table 2.** Results on ChoecSeg8k. Some methods group certain labels into one category and report the groupwise results, denoted by multi-columns numbers.

| GB-Gall Bladder, AW-Abdominal Wall, GT-GI Tract, CD-Cystic |
|------------------------------------------------------------|
| Duct, LHEC-L Hook Electrocautery, HV-Hepatic Vein,         |
| CT-Connective Tissue, LL-Liver Ligament.                   |

| Method                          | Object wise DSC |       |      |      |      |         |      |       |      |      |      |      |      |
|---------------------------------|-----------------|-------|------|------|------|---------|------|-------|------|------|------|------|------|
|                                 | Fat             | Liver | GB   | AW   | GT   | Grasper | LHEC | Blood | HV   | CT   | LL   | CD   | Avg  |
| <b>Traditional DL Methods</b>   |                 |       |      |      |      |         |      |       |      |      |      |      |      |
| U-Net [25]                      | 0.87            | 0.52  | 0.40 | 0.73 | 0.26 | 0.52    |      | 0.08  |      |      |      |      | 0.48 |
| UNetR [7]                       | 0.88            | 0.74  | 0.42 | 0.76 | 0.35 | 0.71    |      | 0     |      |      |      |      | 0.55 |
| TransUNet [3]                   | 0.83            | 0.43  | 0.77 | 0.35 | 0.43 | 0.70    | 0.55 | 0.61  | 0.82 | 0.57 | 0.72 | 0.64 | 0.62 |
| MedT [30]                       | 0.81            | 0.39  | 0.56 | 0.34 | 0.25 | 0.48    | 0.71 | 1     | 0.70 | 0.69 | 0    | 0.89 | 0.57 |
| <b>SAM based methods</b>        |                 |       |      |      |      |         |      |       |      |      |      |      |      |
| SAM w/ text prompt [13]         | 0.05            | 0     | 0.02 | 0    | 0    | 0.01    | 0.04 | 0.01  | 0.14 | 0.01 | 0.14 | 0.01 | 0.04 |
| SAM w/ point prompt [13]        | 0.17            | 0.23  | 0.07 | 0.30 | 0.10 | 0.22    | 0.63 | 1     | 0.70 | 0.43 | 1    | 1    | 0.49 |
| SAM with full finetuning [13]   | 0               | 0     | 0.02 | 0    | 0.09 | 0.13    | 0.38 | 0.91  | 0.7  | 0.38 | 1    | 0.91 | 0.38 |
| MedSAM [19]                     | 0               | 0     | 0.02 | 0    | 0.08 | 0.15    | 0.46 | 1     | 0.69 | 0.38 | 1    | 1    | 0.40 |
| SAMed [35]                      | 0               | 0     | 0.03 | 0    | 0.13 | 0.19    | 0.46 | 1     | 0.69 | 0.38 | 1    | 1    | 0.41 |
| AdaptiveSAM [21]                | 0.85            | 0.71  | 0.37 | 0.80 | 0.10 | 0.20    | 0.70 | 1     | 0.70 | 0.38 | 1    | 1    | 0.64 |
| Low Rank Adaptation of SAM [10] | 0.87            | 0.72  | 0.45 | 0.76 | 0.42 | 0.20    | 0.48 | 0.97  | 0.70 | 0.70 | 0.6  | 0.97 | 0.65 |
| S-SAM (Ours)                    | 0.89            | 0.71  | 0.51 | 0.73 | 0.43 | 0.30    | 0.63 | 1     | 0.71 | 0.56 | 1    | 1    | 0.71 |

where the predicted mask

and the label are both completely blank.

We present quantitative results for CholecSeg8k, LiTS, and GLAS in Tables [2,](#page-28-0) [4,](#page-29-0) and [5](#page-29-1) respectively. Due to space constraints, results on Abdominal Ultrasound and ChestXDet are in supplementary Tables 2 and 3 respectively. On average, we achieve a significant improvement of 6–7% on CholecSeg8k, Ultrasound, and GLAS over the existing state-of-the-art (SOTA), and 1% improvement on the other two datasets. However, note that our method achieves on par performance with a significantly lesser number of parameters. From the tables, we see that adapting foundation models like SAM improves performance over existing DLbased methods. Furthermore, we compare S-SAM with zero-shot SAM in the first two rows in SAM-based methods in the tables. SAM is not trained on medical images and hence performs poorly when prompted with the label name as text prompt without any finetuning. However, with a point prompt, it can still segment to some extent. Similarly, MedSAM also performs well on certain objects, but not as well as adaptation methods, showing the requirement of tuning on new datasets. We also observe that full finetuning of SAM overfits to the data, resulting in a lower test performance. Finally, we show improvements over various SAM-based adaptation methods. A comparison based on the number of parameters is provided in Fig. [4,](#page-30-2) which shows that S-SAM requires a significantly lower number of parameters than these adaptation methods, while also outperforming them on all five datasets. In comparison to SAM, there is a 99.6% reduction in the number of trainable parameters while in comparison to AdaptiveSAM, there is 90% reduction. Similarly, S-SAM trains 50% lesser parameters

than LoRA. All results with S-SAM have a p-value of at most 10−<sup>8</sup> wrt other methods, which shows statistical significance.

**Qualitative Results:** In Fig. [3,](#page-27-0) we present sample results of S-SAM and other methods. S-SAM is able to segment smaller objects like the GI Tract that is missed by Adaptive SAM or SAM as seen in row 1 of the Figure. In addition, S-SAM is also able to pro-

<span id="page-29-2"></span>

| <b>Table 3.</b> Ablation analysis on the compo- |  |  |
|-------------------------------------------------|--|--|
| nents of S-SAM.                                 |  |  |

| Tuning Pos Embeds | Tuning Layernorm | TAL | Scaling | Shifting | Avg DSC     |
|-------------------|------------------|-----|---------|----------|-------------|
|                   |                  |     |         |          | 0.04        |
|                   |                  |     |         |          | 0.04        |
| ✓                 |                  |     |         |          | 0.50        |
| ✓                 | ✓                |     |         |          | 0.52        |
| ✓                 | ✓                | ✓   |         |          | 0.54        |
| ✓                 | ✓                | ✓   | ✓       |          | 0.64        |
| ✓                 | ✓                | ✓   | ✓       | ✓        | <b>0.71</b> |

duce blank masks when a queried object of interest is not present, as seen in row 3. The effectiveness of adaptation methods can be visually represented through all the cases, where zero-shot SAM produces gibberish results, unlike the adaptation methods.

**Component-Wise Ablation:** S-SAM has three major modified components over SAM, namely the Text Affine Layer (TAL), scaling matrix A, and the shifting matrix  $B$ , as well as other modifications like training the positional embeddings and layernorms of the model. To assess the importance of each of these, we start with SAM and note the rise in performance on CholecSeg8k when each component is added. Results for this study are tabulated in Table [3.](#page-29-2)

The first row in the table shows SAM's zero-shot performance without modifications. Tuning only the positional embeddings of the encoder doesn't improve performance. However, tuning layernorm layers significantly boosts DSC by around 46%, consistent with domain adaptation research where norm layers contribute to domain bias [\[16\]](#page-31-13). Adding TAL further improves performance. Shifting and scaling, added one by one, both enhance performance, with shifting

| Method                          | Objectwise DSC |       |             |
|---------------------------------|----------------|-------|-------------|
|                                 | Liver          | Tumor | Avg.        |
| <b>Traditional DL Methods</b>   |                |       |             |
| UNet [25]                       | 0.77           | 0.65  | 0.71        |
| SegNet [1]                      | 0.76           | 0.64  | 0.70        |
| KiuNet [12]                     | 0.80           | 0.71  | 0.76        |
| DeepLab v3+ [4]                 | 0.85           | 0.68  | 0.77        |
| <b>SAM based Methods</b>        |                |       |             |
| SAM w/ text prompt [13]         | 0.04           | 0     | 0.02        |
| SAM w/ point prompt [13]        | 0.05           | 0     | 0.03        |
| SAM with full finetuning [13]   | 0.05           | 0.86  | 0.5         |
| MedSAM [19]                     | 0.06           | 0.01  | 0.04        |
| SAMed [35]                      | 0.61           | 0.91  | 0.76        |
| AdaptiveSAM [21]                | 0.80           | 0.86  | 0.83        |
| Low Rank Adaptation of SAM [10] | 0.82           | 0.84  | 0.83        |
| S-SAM (Ours)                    | 0.85           | 0.83  | <b>0.84</b> |

<span id="page-29-0"></span>**Table 4.** Results on LiTS

<span id="page-29-1"></span>

| <b>Table 5.</b> Results on GLAS. |
|----------------------------------|
|                                  |

| Method                          | Objectwise DSC |
|---------------------------------|----------------|
|                                 | Glands         |
| <b>Traditional DL Methods</b>   |                |
| UNet [25]                       | 0.52           |
| SegNet [1]                      | 0.84           |
| clDice [27]                     | 0.85           |
| PointRend [14]                  | 0.88           |
| TI-Loss [6]                     | 0.88           |
| <b>SAM Based Methods</b>        |                |
| SAM w/ text prompt [13]         | 0.01           |
| SAM w/ point prompt [13]        | 0.19           |
| SAM with full finetuning [13]   | 0.85           |
| MedSAM [19]                     | 0.21           |
| SAMed [35]                      | 0.65           |
| AdaptiveSAM [21]                | 0.66           |
| Low Rank Adaptation of SAM [10] | 0.83           |
| S-SAM (Ours)                    | 0.90           |

Image /page/30/Figure/1 description: This is a horizontal bar chart comparing the number of trainable parameters for different methods. The y-axis lists the methods: UNet (1.56E+6), UNet++ (7.45E+7), TransUNet (1.05E+8), MedT (1.56E+6), SAM (base) (9.37E+7), MedSAM (9.37E+7), SAMed (1.88E+7), AdaptiveSAM (img size 1024) (7.33E+6), AdaptiveSAM (img size 256) (4.51E+6), LoRA Adaptation of SAM (img size 1024) (3.79E+6), LoRA Adaptation of SAM (img size 256) (8.42E+5), S-SAM (img size 1024) (3.39E+6), and S-SAM (img size 256) (4.45E+5). The x-axis represents the number of trainable parameters, ranging from 0.00E+0 to 1.00E+8.

<span id="page-30-2"></span>**Fig. 4.** A comparison among different methods based on the number of parameters trained. The red bars indicate traditional DL-based segmentation methods. Blue bars indicate SAM-based methods and green bars indicate our method. The numbers to the right of each bar denote the number of trainable parameters. (Color figure online)

appearing more important for modeling domain shift, resulting in a 12% increase. Finally, with all components, we achieve S-SAM with the best performance.

# 5 Conclusion

In this paper, we presented S-SAM - an efficient adaptation of SAM for medical images, which is realized by tuning the singular values of the weight matrix. We show that S-SAM performs on par or outperforms existing methods on five publicly available medical image segmentation datasets with significantly lower amount of trainable parameters and allows the use of label names as prompts. We find that S-SAM could be affected by class-size disparities, as seen from its performance on specific classes. A potential future direction towards improving S-SAM could be using additional loss functions or weighted loss functions to reduce the effect of the disparities.

**Disclosure of Interests.** This research was supported by a grant from the National Institutes of Health, USA; R01EY033065. The content is solely the responsibility of the authors and does not necessarily represent the official views of the National Institutes of Health. The authors have no competing interests in the paper.

# References

- <span id="page-30-3"></span>1. Badrinarayanan, V., Kendall, A., Cipolla, R.: Segnet: A deep convolutional encoder-decoder architecture for image segmentation (2016)
- <span id="page-30-0"></span>2. Basak, H., Yin, Z.: Pseudo-label guided contrastive learning for semi-supervised medical image segmentation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 19786–19797 (June 2023)
- <span id="page-30-1"></span>3. Chen, J., Lu, Y., Yu, Q., Luo, X., Adeli, E., Wang, Y., Lu, L., Yuille, A.L., Zhou, Y.: Transunet: Transformers make strong encoders for medical image segmentation. arXiv preprint [arXiv:2102.04306](http://arxiv.org/abs/2102.04306) (2021)

- <span id="page-31-15"></span>4. Chen, L.C., Zhu, Y., Papandreou, G., Schroff, F., Adam, H.: Encoder-decoder with atrous separable convolution for semantic image segmentation (2018)
- <span id="page-31-8"></span>5. Ding, N., Qin, Y., Yang, G., Wei, F., Yang, Z., Su, Y., Hu, S., Chen, Y., Chan, C.M., Chen, W., Yi, J., Zhao, W., Wang, X., Liu, Z., Zheng, H.T., Chen, J., Liu, Y., Tang, J., Li, J., Sun, M.: Parameter-efficient fine-tuning of large-scale pre-trained language models. Nature Machine Intelligence **5** (2023)
- 6. Gupta, S., Hu, X., Kaan, J., Jin, M., Mpoy, M., Chung, K., Singh, G., Saltz, M., Kurc, T., Saltz, J., Tassiopoulos, A., Prasanna, P., Chen, C.: Learning topological interactions for multi-class medical image segmentation (2022)
- 7. Hatamizadeh, A., Tang, Y., Nath, V., Yang, D., Myronenko, A., Landman, B., Roth, H.R., Xu, D.: Unetr: Transformers for 3d medical image segmentation. In: 2022 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV). pp. 1748–1758 (2022)
- <span id="page-31-10"></span>8. Hong, W.Y., Kao, C.L., Kuo, Y.H., Wang, J.R., Chang, W.L., Shih, C.S.: Cholecseg8k: A semantic segmentation dataset for laparoscopic cholecystectomy based on cholec80 (2020)
- <span id="page-31-3"></span>9. Hu, C., Xia, T., Ju, S., Li, X.: When sam meets medical images: An investigation of segment anything model (sam) on multi-phase liver tumor segmentation (2023)
- <span id="page-31-9"></span>10. Hu, E.J., Shen, Y., Wallis, P., Allen-Zhu, Z., Li, Y., Wang, S., Wang, L., Chen, W.: Lora: Low-rank adaptation of large language models (2021)
- <span id="page-31-1"></span>11. Jia, C., Yang, Y., Xia, Y., Chen, Y.T., Parekh, Z., Pham, H., Le, Q.V., Sung, Y., Li, Z., Duerig, T.: Scaling up visual and vision-language representation learning with noisy text supervision (2021)
- <span id="page-31-14"></span>12. Jose, J.M., Sindagi, V., Hacihaliloglu, I., Patel, V.M.: Kiu-net: Towards accurate segmentation of biomedical images using over-complete representations (2020)
- <span id="page-31-2"></span>13. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., Dollár, P., Girshick, R.: Segment anything. [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-31-16"></span>14. Kirillov, A., Wu, Y., He, K., Girshick, R.: Pointrend: Image segmentation as rendering (2020)
- <span id="page-31-7"></span>15. Lee, H.H., Gu, Y., Zhao, T., Xu, Y., Yang, J., Usuyama, N., Wong, C., Wei, M., Landman, B.A., Huo, Y., Santamaria-Pang, A., Poon, H.: Foundation models for biomedical image segmentation: A survey (2024)
- <span id="page-31-13"></span>16. Li, Y., Wang, N., Shi, J., Liu, J., Hou, X.: Revisiting batch normalization for practical domain adaptation (2016)
- <span id="page-31-11"></span>17. Lian, J., Liu, J., Zhang, S., Gao, K., Liu, X., Zhang, D., Yu, Y.: A structure-aware relation network for thoracic diseases detection and segmentation (2021)
- <span id="page-31-5"></span>18. Lin, X., Xiang, Y., Zhang, L., Yang, X., Yan, Z., Yu, L.: Samus: Adapting segment anything model for clinically-friendly and generalizable ultrasound image segmentation (2023)
- <span id="page-31-6"></span>19. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images (2023)
- <span id="page-31-12"></span>20. Maranh˜ao, A.: <https://www.kaggle.com/datasets/andrewmvd/lits-png,> [https://](https://www.kaggle.com/datasets/andrewmvd/lits-png) [www.kaggle.com/datasets/andrewmvd/lits-png](https://www.kaggle.com/datasets/andrewmvd/lits-png)
- <span id="page-31-4"></span>21. Paranjape, J.N., Nair, N.G., Sikder, S., Vedula, S.S., Patel, V.M.: Adaptivesam: Towards efficient tuning of sam for surgical scene segmentation (2023)
- <span id="page-31-0"></span>22. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., Krueger, G., Sutskever, I.: Learning transferable visual models from natural language supervision (2021)

- <span id="page-32-9"></span>23. Rahman, A., Valanarasu, J., Hacihaliloglu, I., Patel, V.M.: Ambiguous medical image segmentation using diffusion models. In: 2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 11536–11546. IEEE Computer Society, Los Alamitos, CA, USA (jun 2023), [https://doi.ieeecomputersociety.](https://doi.ieeecomputersociety.org/10.1109/CVPR52729.2023.01110) [org/10.1109/CVPR52729.2023.01110](https://doi.ieeecomputersociety.org/10.1109/CVPR52729.2023.01110)
- <span id="page-32-1"></span>24. Ravishankar, H., Patil, R., Melapudi, V., Bhatia, P., Taha, K.H., Annangi, P.: Sonosam – segment anything on ultrasound images (2023)
- <span id="page-32-10"></span>25. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation. vol. 9351 (2015)
- <span id="page-32-5"></span>26. Shaharabany, T., Dahan, A., Giryes, R., Wolf, L.: Autosam: Adapting sam to medical images by overloading the prompt encoder (2023)
- <span id="page-32-12"></span>27. Shit, S., Paetzold, J.C., Sekuboyina, A., Ezhov, I., Unger, A., Zhylka, A., Pluim, J.P.W., Bauer, U., Menze, B.H.: clDice - a novel topology-preserving loss function for tubular structure segmentation. In: 2021 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). IEEE (jun 2021), [https://doi.org/10.](https://doi.org/10.1109%2Fcvpr46437.2021.0162) [1109%2Fcvpr46437.2021.0162](https://doi.org/10.1109%2Fcvpr46437.2021.0162)
- <span id="page-32-8"></span>28. Sirinukunwattana, K., Pluim, J.P.W., Chen, H., Qi, X., Heng, P.A., Guo, Y.B., Wang, L.Y., Matuszewski, B.J., Bruni, E., Sanchez, U., Böhm, A., Ronneberger, O., Cheikh, B.B., Racoceanu, D., Kainz, P., Pfeiffer, M., Urschler, M., Snead, D.R.J., Rajpoot, N.M.: Gland segmentation in colon histology images: The glas challenge contest (2016)
- <span id="page-32-3"></span>29. Turk, M., Pentland, A.: Face recognition using eigenfaces. In: Proceedings. 1991 IEEE Computer Society Conference on Computer Vision and Pattern Recognition. pp. 586–591 (1991)
- <span id="page-32-11"></span>30. Valanarasu, J.M.J., Oza, P., Hacihaliloglu, I., Patel, V.M.: Medical transformer: Gated axial-attention for medical image segmentation. In: Medical Image Computing and Computer Assisted Intervention – MICCAI 2021. pp. 36–46. Springer International Publishing, Cham (2021)
- <span id="page-32-7"></span>31. Vitale, S., Orlando, J., Iarussi, E., Larrabide, I.: Improving realism in patientspecific abdominal ultrasound simulation using cyclegans. International Journal of Computer Assisted Radiology and Surgery (07 2019)
- <span id="page-32-0"></span>32. Wang, R., Lei, T., Cui, R., Zhang, B., Meng, H., Nandi, A.K.: Medical image segmentation using deep learning: A survey. IET Image Processing **16**(5), 1243–1267 (2022), <https://ietresearch.onlinelibrary.wiley.com/doi/abs/10.1049/ipr2.12419>
- <span id="page-32-2"></span>33. Wu, J., Zhang, Y., Fu, R., Fang, H., Liu, Y., Wang, Z., Xu, Y., Jin, Y.: Medical sam adapter: Adapting segment anything model for medical image segmentation (2023)
- <span id="page-32-6"></span>34. Yang, J., Jin, H., Tang, R., Han, X., Feng, Q., Jiang, H., Yin, B., Hu, X.: Harnessing the power of llms in practice: A survey on chatgpt and beyond (2023)
- <span id="page-32-4"></span>35. Zhang, K., Liu, D.: Customized segment anything model for medical image segmentation (2023)

Image /page/33/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a darker gray curved line segment on the left side, and a gray flag shape pointing to the right in the center. The text below the icon reads "Check for updates" in gray capital letters.

# ShapeMamba-EM: Fine-Tuning Foundation Model with Local Shape Descriptors and Mamba Blocks for 3D EM Image Segmentation

Ruohua Shi<sup>1,2,3</sup>, Qiufan Pang<sup>1</sup>, Lei Ma<sup>1,2,3</sup>, Lingyu Duan<sup>1,2,4</sup>, Tiejun Huang<sup>1,2,3</sup>, and Tingting Jiang<sup>1,2,3( $\boxtimes$ )</sup>

<sup>1</sup> National Engineering Research Center of Visual Technology, School of Computer Science, Peking University, Beijing, China

{shiruohua,lei.ma,lingyu,tjhuang,ttjiang}@pku.edu.cn

<EMAIL>

<sup>2</sup> National Key Laboratory for Multimedia Information Processing, School of Computer Science, Peking University, Beijing, China

<sup>3</sup> National Biomedical Imaging Center, Peking University, Beijing, China <sup>4</sup> Peng Cheng Laboratory, Shenzhen, China

Abstract. Electron microscopy (EM) imaging offers unparalleled resolution for analyzing neural tissues, crucial for uncovering the intricacies of synaptic connections and neural processes fundamental to understanding behavioral mechanisms. Recently, the foundation models have demonstrated impressive performance across numerous natural and medical image segmentation tasks. However, applying these foundation models to EM segmentation faces significant challenges due to domain disparities. This paper presents *ShapeMamba-EM*, a specialized fine-tuning method for 3D EM segmentation, which employs adapters for long-range dependency modeling and an encoder for local shape description within the original foundation model. This approach effectively addresses the unique volumetric and morphological complexities of EM data. Tested over a wide range of EM images, covering five segmentation tasks and 10 datasets, ShapeMamba-EM outperforms existing methods, establishing a new standard in EM image segmentation and enhancing the understanding of neural tissue architecture.

**Keywords:** 3D EM image segmentation  $\cdot$  State space model  $\cdot$  Local shape descriptor

# 1 Introduction

Electron microscopy (EM) allows the imaging of neural tissue at a resolution sufficient to resolve individual synapses and fine neural processes. Therefore,

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_68.](https://doi.org/10.1007/978-3-031-72390-2_68)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 731–741, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_68)\_68

Image /page/34/Figure/1 description: The image displays a multi-panel figure illustrating a process related to brain imaging and analysis. Panel (a) shows a 3D rendering of a brain structure on the left, with a blue plane cutting through it, followed by an axial MRI slice of the brain on the right, indicating a scale of 60mm. Panel (b) presents a 3D volume rendering of a microscopic tissue sample, transitioning to a detailed 2D electron micrograph of neuronal structures, with a scale bar of 0.5µm. Panel (c) shows a wireframe cube containing traced neuronal pathways in blue and green, with a black line indicating a reference axis, and a final panel displays a black background with several colorful, segmented shapes representing identified structures.

<span id="page-34-0"></span>Fig. 1. Illustration of the medical data and EM data. (a) MRI edema image from the BraTS2021 dataset. (b) EM mitochondria data from MitoEM-R dataset. (c) 3D and 2D segmentation results of (b). The boundaries of instances share a similar local shape, and the scope of the instance spans the entire volume.

the segmentation of EM images plays a pivotal role in the realm of biological research, offering profound insights into the inner mechanisms underlying behavior and helping drive future theoretical experiments [\[11,](#page-42-0)[20,](#page-43-0)[22](#page-43-1)]. Many excellent algorithms and datasets have emerged [\[2,](#page-41-0)[12](#page-42-1)[,24](#page-43-2)[–26](#page-43-3)[,29](#page-43-4)].

Recent advancements in computer vision have spurred breakthroughs by the foundation models, such as the Segment anything model (SAM), which has achieved promising zero-shot segmentation performance on a variety of natural image datasets [\[14\]](#page-42-2). However, their performance significantly declines when applied to some downstream tasks, like biological and medical images, primarily due to the substantial disparity between the two image domains.

An effective method to enhance the generalizability of the foundation models to downstream tasks lies in fine-tuning. Recently, numerous methods applied to medical datasets have demonstrated impressive efficacy [\[4,](#page-42-3)[28](#page-43-5)[,31,](#page-43-6)[32\]](#page-43-7). For instance, Med-SA [\[30](#page-43-8)] maintains the pre-trained SAM parameters frozen while integrating LoRA modules to the designated positions. MedSAM [\[18\]](#page-42-4) has achieved this by fine-tuning the decoder with 1.1 million masks, enabling SAM's application in medical imaging. In addition to these methods trained on 2D medical images, some methods propose solutions for 3D images. 3DSAM-adapter [\[6](#page-42-5)] proposes a holistically designed scheme for transferring SAM from 2D to 3D for promptable medical image segmentation. SAM-Med3D [\[28](#page-43-5)] reformulates SAM to a thorough 3D architecture trained on a comprehensively processed large-scale volumetric medical dataset.

However, the medical foundation models above cannot be directly applied to EM data. Taking Fig. [1](#page-34-0) as an example, although both medical and EM images are all volumetric grayscale images, EM images have significantly higher resolution (about  $10<sup>5</sup>$  times), which results in more noise. Besides, the objects to be segmented in EM images have relatively consistent local features and are widely distributed across the spatial domain. This distribution, combined with the inherent anisotropy, intensifies the challenges of EM segmentation. Consequently, EM images require customized methods to address these challenges. On the other hand, medical images have grayscale and volumetric data, which are closer to EM images than to natural images, so the fine-tuned medical SAM models are more suitable for EM data than SAM. Therefore, this paper proposes a fine-tuning method specifically designed for EM image segmentation, based on a 3D medical foundation model, named ShapeMamba-EM. ShapeMamba-EM

Image /page/35/Figure/1 description: This is a diagram illustrating a 3D neural network architecture for image segmentation. The architecture consists of a 3D Image Encoder, a 3D Mask Decoder, and a 3D LSDs Encoder. The 3D Image Encoder takes a 3D image as input and processes it through components like 3D Patch Embedding, 3D Abs PE, and multiple Mamba-Blocks. The output of the encoder is an image embedding. The 3D Mask Decoder receives the image embedding and LSD embedding to produce a segmented output image. The 3D LSDs Encoder utilizes a 3D U-Net to process input LSDs and generate predicted LSDs, with a Mean Squared Error (MSE) loss calculated against ground truth LSDs. The diagram also details a 3D Mamba Adapter, which includes Layer Norm 3D, Mamba Blocks, and MLP layers, with some components marked for fine-tuning (fire icon) and freezing (snowflake icon).

<span id="page-35-0"></span>Fig. 2. The overall architecture of ShapeMamba-EM. The image encoder is updated with FacT. The volumetric or temporal information is effectively incorporated via a set of 3D Mamba adapters. The mask decoder is fully fine-tuned and modified to recover the prediction resolution. The LSDs are trained by the 3D U-Net network.

first modifies the original foundation model to enhance the tuning efficiency, and then adds two novel modules targeting the local morphological features and long-range dependencies in EM data as shown in Fig. [1](#page-34-0) (b,c).

Specifically, for the selection of the foundation model, we opted for the currently largest model trained on 3D medical images, SAM-Med3D [\[28](#page-43-5)], which consists of three parts: image encoder, mask decoder, and prompt encoder. Inspired by existing work on medical data, we leverage FacT [\[13](#page-42-6)] to tune the image encoder module for retaining most pre-trained weights while only updating lightweight weight increments. Furthermore, the prompt encoder is removed because crafting appropriate prompts is a challenging task for EM data and automatic segmentation has shown promise.

Besides modifying the original model, we tackle the challenges of *local morphological features* and *long-range dependencies* for EM data with two novel modules. Firstly, we find pixel-wise prediction alone insufficient. To accurately segment objects with similar *local morphological features* and imperfect edges, we implement a 3D U-Net architecture [\[5](#page-42-7)] to predict the Local Shape Descriptors (LSDs) [\[23\]](#page-43-9) to enhance the boundary prediction. Secondly, to address the challenge of *long-range dependency* inherent in EM object analysis, we draw inspiration from the recent innovation in Mamba [\[7\]](#page-42-8) which utilizes state space sequential models (SSMs) [\[8](#page-42-9)[,19\]](#page-43-10). These models excel at extracting long-dependency information with reduced computational burden and lower memory consumption. Consequently, we propose the integration of *3D Mamba Adapters* into the image encoder. Through extensive experimentation, we demonstrate the superior performance of the ShapeMamba-EM framework across a broad spectrum of EM images, spanning five segmentation tasks and 10 datasets.

# 2 Method

## 2.1 Overview

The overall framework of our proposed ShapeMamba-EM is illustrated in Fig. [2.](#page-35-0) We introduce an innovative model that enhances the SAM-Med3D core framework for 3D EM segmentation tasks. Specifically, we have augmented the SAM-Med3D architecture by incorporating the FacT approach into the 3D Multi-head self-attention model. This integration enables a more effective and efficient finetuning process. Furthermore, we introduce *3D Mamba Adapters* designed to tackle the challenges of long-range dependency of segmentation objects. Besides, a 3D U-Net network is incorporated into the *3D Mask Decoder* to capture the Local Shape Descriptors of the segmentation objects. These two designs address the inherent limitations of SAM-Med3D in EM segmentation, thereby increasing segmentation accuracy and efficiency.

## 2.2 SAM-Med3D

In this paper, we adopt SAM-Med3D [\[28\]](#page-43-5) as our foundation model, due to its excellent ability to extract volumetric data features by training on a large amount of 3D medical data. It is designed based on the segment anything model (SAM) [\[14](#page-42-2)] for volumetric medical imaging by incorporating a 3D structure for direct spatial information capture. SAM-Med3D employs a 3D convolution for patch embedding, uses a 3D positional encoding extended from SAM's 2D version, and inputs these to 3D attention blocks. These blocks, enhanced with 3D relative positional encodings, are part of the Multi-Head Self-Attention module, facilitating direct spatial detail capture. The prompt encoder uses 3D position encodings for sparse prompts and 3D convolutions for dense prompts, while the 3D Mask Decoder integrates 3D upscaling with 3D transposed convolution.

## 2.3 Parameter-Efficient Fine-Tuning of 3D Image Encoder

In order to effectively extract image features, Med-SAM3D's 3D Image Encoder comprises a substantial portion of network parameters. Fine-tuning all these weights is computationally intensive. Previous research has shown that PETL techniques can achieve adaptation performance similar to full fine-tuning but with significantly fewer network parameters updated  $[13,21,27]$  $[13,21,27]$  $[13,21,27]$  $[13,21,27]$ . In this regard, we adopt FacT [\[13](#page-42-6)], a SOTA parameter efficient transfer learning (PETL) technique, that can obtain comparable or superior performance compared to other PETL methods while introducing a smaller number of trainable parameters.

## 2.4 3D Mamba Adapter

In 3D EM images, the objects are widely distributed yet dense. To address this long-range dependencies challenge in EM images, we concentrate on enhancing the representational capacity of the encoder in neural network architectures

Image /page/37/Figure/1 description: This figure displays a grid of images and their corresponding labels and visualizations. The rows are labeled 'Axon', 'Cell', 'Mitochondria', 'Nuclei', and 'Synapse'. Each row contains an original image, a segmented label image with different colors representing different structures, and four columns labeled 'LSD[0:3]', 'LSD[3:6]', 'LSD[6:9]', and 'LSD[9:10]', which appear to be different visualizations or processing steps applied to the original images. The visualizations in the LSD columns vary in color and intensity, highlighting different aspects of the structures in the original images.

<span id="page-37-0"></span>Fig. 3. Visualizations of LSDs for different segmentation tasks. From left to right: the EM image, segmentation labels, and components of the LSDs.

through the incorporation of the Mamba layer, which is designed to capture longrange dependencies within the input data. Specifically, we design the *3D Mamba Adapter* as illustrated in Fig. [2](#page-35-0) (right), which substitutes the self-attention module in the transformer architecture with the more efficient Mamba layer. Each 3D Mamba Adapter consists of a *3D Layer Norm* operation with a *Mamba Block* [\[7\]](#page-42-8). This enables both multi-scale and global feature modeling while maintaining a high efficiency during training and inference.

## 2.5 3D Local Shape Descriptor Encoder

Although recent deep learning networks have made good progress in EM image segmentation, a neural network might focus only on a few center voxels to detect objects and achieve high accuracy during training, especially if trained using a voxel-wise loss. However, this strategy might fail in rare cases where boundary evidence is ambiguous. The objects to be segmented in EM images, such as axons, cells, and mitochondria, have regular local shapes. Inspired by [\[23\]](#page-43-9), to enhance the model's learning of the local shapes objects in EM images, We first define the Local Shape Descriptors (LSDs) of a segment  $i \in \{1, \ldots, l\}$  under a given voxel v. we intersect the segment  $y(v)$  underlying a voxel  $v \in \Omega$  with a 3D ball of radius  $\sigma$  centered at v to obtain a subset of voxels  $S_v \subset \Omega$ , formally

| Task                                                                                       | Axon |     | Cell                                                                                                                            |          |             |                |      |       | Synapse |       |                                 |                |  |
|--------------------------------------------------------------------------------------------|------|-----|---------------------------------------------------------------------------------------------------------------------------------|----------|-------------|----------------|------|-------|---------|-------|---------------------------------|----------------|--|
| Dataset                                                                                    | Gauy |     |                                                                                                                                 | ISBI2012 |             | <b>SNEMI3D</b> |      | CREMI |         | CREMI |                                 | $EM-R50$       |  |
| <b>Metrics</b>                                                                             | Dice | mAP | Dice                                                                                                                            | mAP      | $\sum$ Dice | mAP            | Dice | mAP   | Dice    | mAP   | Dice                            | mAP            |  |
| U3D-BCD 0.790  0.801  0.932  0.930  0.964  0.961  0.952  0.960  0.814  0.823  0.764  0.775 |      |     |                                                                                                                                 |          |             |                |      |       |         |       |                                 |                |  |
| SwinU                                                                                      |      |     | $[0.722 \; 0.746 \; 0.932 \; 0.935 \; 0.962 \; 0.958 \; 0.943 \; 0.950 \; 0.821 \; 0.829 \; 0.733 \; 0.749$                     |          |             |                |      |       |         |       |                                 |                |  |
| nnU-Net                                                                                    |      |     | $[0.788 \; 0.789 \; 0.971 \; 0.966 \; 0.965 \; 0.962 \; 0.947 \; 0.951 \; 0.828 \; 0.814 \; 0.760 \; 0.758$                     |          |             |                |      |       |         |       |                                 |                |  |
| MA-SAM 0.539 0.621 0.763 0.742 0.874 0.878 0.836 0.845 0.475 0.464 0.527 0.539             |      |     |                                                                                                                                 |          |             |                |      |       |         |       |                                 |                |  |
| 3DSAMA 0.474 0.599 0.518 0.569 0.371 0.428 0.531 0.594 0.01                                |      |     |                                                                                                                                 |          |             |                |      |       |         | 0.01  | 0.01                            | $ 0.01\rangle$ |  |
| MSAM3D 0.716 0.743 0.858 0.862 0.931 0.927 0.943 0.948 0.611 0.591                         |      |     |                                                                                                                                 |          |             |                |      |       |         |       | $\vert 0.711 \vert 0.718 \vert$ |                |  |
| $w/\mathrm{o}$ M                                                                           |      |     | $[0.735 \; 0.750 \; 0.947 \; 0.951 \; 0.962 \; 0.965 \; 0.954 \; 0.966 \; 0.815 \; 0.823 \; 0.779 \; 0.780$                     |          |             |                |      |       |         |       |                                 |                |  |
| $w/\mathrm{o}$ L                                                                           |      |     | $\vert 0.791 \vert 0.796 \vert 0.939 \vert 0.942 \vert 0.959 \vert 0.958 \vert 0.942 \vert 0.951 \vert 0.799 \vert 0.810 \vert$ |          |             |                |      |       |         |       | $\vert 0.780 \vert 0.801$       |                |  |
| Ours                                                                                       |      |     | $[0.8090.8270.9580.9510.9740.9720.9650.9730.8340.8650.7920.817$                                                                 |          |             |                |      |       |         |       |                                 |                |  |

<span id="page-38-0"></span>Table 1. Quantitative outcomes of methods applied to segmentation tasks for axons, synapses, and cells. Bold and underlined numbers denote the 1st and 2nd scores.

given as:

$$
S_v = \left\{ v' \in \Omega \, |y(v) = y(v'), \left| v - v' \right|_2^2 \le \sigma \right\}.
$$
 (1)

The LSD lsd<sup>y</sup> :  $\Omega \mapsto \mathbb{R}^{10}$  for a voxel v is a concatenation of the size, center offset and coordinate covariance, that is:

$$
lsdy(v) = (s(Sv), m(Sv) - v, c(Sv)v)
$$
 (2)  
size center offset covariance

where  $s(S_v) = |S_v|$  is the size of  $S_v$ ,  $m(S_v)$  is the covariance of its coordinates and  $c(S_v)$  is the mean coordinates:

$$
m(S_v) = \frac{1}{s(S_v)} \sum_{v \in S_v} v, \quad c(S_v) = \frac{1}{s(S_v)} \sum_{v \in S_v} (v - m(S_v)) (v - m(S_v))^T.
$$
 (3)

Therefore, for a 3D image, LSDs are represented as a ten-dimensional embedding. This encapsulation includes: LSD[0:3] for the mean offset; LSD[3:6] and LSD[6:9] delineating the covariance of coordinates, with LSD[3:6] capturing the diagonal entries and LSD[6:9] the off-diagonals; and finally, LSD[9:10] reflecting the size, quantified as the number of voxels within the intersected Gaussian. Some visualization examples of LSDs are shown in Fig. [3.](#page-37-0) We use  $\text{lsd}^y(v)$  to formulate an auxiliary learning task that complements the prediction of affinities. For that, we use the 3D U-Net network to learn the  $\mathrm{lsd}^{\mathrm{x}}:\varOmega\mapsto\mathbb{R}^{10}$  directly from raw data x, and take it as the embedding to the 3D Emage Decoder network.

# 3 Experiments and Results

We extensively evaluate our method on five EM image segmentation tasks, covering 10 datasets, i.e., axon segmentation, cell segmentation, mitochondria segmentation, synapse segmentation, and nuclei segmentation. We first conduct

| Task    | Mitochondria |            |          |          |          |         | Nuclei |       |       |       |       |       |
|---------|--------------|------------|----------|----------|----------|---------|--------|-------|-------|-------|-------|-------|
|         | Gauy         | Kasthuri++ | Lucchi++ | MitoEM-H | MitoEM-R | NucMM-Z | Dice   | mAP   | Dice  | mAP   | Dice  | mAP   |
| Dataset | Dice         | mAP        | Dice     | mAP      | Dice     | mAP     | Dice   | mAP   | Dice  | mAP   | Dice  | mAP   |
| U3D-BCD | 0.564        | 0.529      | 0.889    | 0.831    | 0.880    | 0.753   | 0.746  | 0.773 | 0.775 | 0.844 | 0.879 | 0.894 |
| SwinU   | 0.472        | 0.443      | 0.904    | 0.861    | 0.869    | 0.874   | 0.779  | 0.822 | 0.803 | 0.867 | 0.866 | 0.837 |
| nnU-Net | 0.528        | 0.501      | 0.859    | 0.872    | 0.856    | 0.829   | 0.807  | 0.830 | 0.825 | 0.864 | 0.907 | 0.894 |
| MA-SAM  | 0.349        | 0.315      | 0.769    | 0.773    | 0.754    | 0.740   | 0.692  | 0.701 | 0.723 | 0.744 | 0.839 | 0.858 |
| 3DSAMA  | 0.145        | 0.188      | 0.582    | 0.668    | 0.645    | 0.682   | 0.671  | 0.710 | 0.786 | 0.802 | 0.863 | 0.897 |
| MSAM3D  | 0.537        | 0.521      | 0.902    | 0.878    | 0.715    | 0.728   | 0.711  | 0.714 | 0.822 | 0.835 | 0.878 | 0.895 |
| w/o M   | 0.572        | 0.598      | 0.951    | 0.920    | 0.906    | 0.910   | 0.817  | 0.825 | 0.846 | 0.852 | 0.910 | 0.903 |
| w/o L   | 0.586        | 0.591      | 0.942    | 0.922    | 0.915    | 0.908   | 0.820  | 0.812 | 0.839 | 0.844 | 0.904 | 0.899 |
| Ours    | 0.612        | 0.603      | 0.968    | 0.936    | 0.940    | 0.954   | 0.847  | 0.877 | 0.852 | 0.930 | 0.915 | 0.907 |

<span id="page-39-0"></span>Table 2. Quantitative outcomes of methods applied to segmentation tasks for mitochondrion and nucleus. Bold and underlined numbers denote the 1st and 2nd scores.

comparisons with state-of-the-art EM image segmentation methods and SAM fine-tuning methods, and then provide ablation studies to analyze our method.

### 3.1 Datasets and Experimental Settings

We conduct a series of extensive experiments on 5 segmentation tasks with 10 datasets to evaluate the performance of our method. Here we briefly introduce them, and more details are shown in the supplementary materials. Specifically, the *Axon Segmentation Task* uses the Gauy dataset [\[9\]](#page-42-10), the *Cell Segmentation Task* uses three datasets: ISBI2012 [\[2](#page-41-0)], SNEMI3D [\[15](#page-42-11)] CREMI [\[1](#page-41-1)], the *Mitochondira Segmentation Task* uses five datasets: MitoEM-R [\[29](#page-43-4)], MitoEM-H [\[29\]](#page-43-4), Kasthuri++ [\[3\]](#page-42-12), Lucchi++ [\[3](#page-42-12)], and Gauy [\[9](#page-42-10)], the *Nuclei Segmentation Task* uses NucMM-Z [\[17](#page-42-13)] dataset, ans the *Synapse Segmentation Task* uses two datasets: CREMI [\[1](#page-41-1)] and EM-R50 [\[16\]](#page-42-14). For the evaluation, we evaluate the methods using mean 3D Average Precision (mAP) [\[29\]](#page-43-4) and Dice scores at the instance level.

We comprehensively compare our proposed method against a suite of cuttingedge algorithms. These include recent successful approaches for biomedical data segmentation utilizing CNN architectures, such as U3D-BCD [\[29\]](#page-43-4) and nnU-Net [\[12\]](#page-42-1), alongside Transformer-based architecture, SwinUNETR [\[10](#page-42-15)] (SwinU). Additionally, we examine fine-tuning methods based on SAM, specifically MA-SAM [\[4](#page-42-3)] and 3DSAM-Adapter [\[6\]](#page-42-5) (3DSAMA). Our evaluation also extends to direct fine-tuning using Med-SAM3D (MSAM3D), supplemented by an ablation study to assess the impact of our novel 3D Mamba Adapter and 3D LSD Encoder Module. During the training of fine-tuning methods, we independently trained the model for each task and employed Binary Cross-Entropy (BCE) loss during training. The experiments are conducted training on 8 NVIDIA A800 GPUs.

Image /page/40/Picture/1 description: This image displays a comparison of segmentation results for different biological structures, including axons, cells, mitochondria, synapses, and nuclei. The first column shows the original electron microscopy images. Subsequent columns present segmentation masks generated by various methods (U3D-BCD, SwinU, nnU-Net, MA-SAM, 3DSAMA, MSAM3D) and the proposed method ('Ours'). Each row focuses on a specific biological structure, with different colors used to distinguish individual segmented components. Dashed white boxes highlight specific regions of interest within the segmentation masks for closer examination.

Fig. 4. The visualization of segmentation results.

<span id="page-40-0"></span>More details of the experiments including the splits of the training and testing data,  $\sigma$  in LSD generation are shown in the supplementary materials.

## 3.2 Quantitative and Qualitative Segmentation Results

Qualitative Results. The quantitative results in Tab. [1](#page-38-0) and Tab. [2](#page-39-0) underscore the superior performance of our proposed method in precise EM segmentation across all five tasks. Compared to both CNN-based, Transformer-based, and fine-tuning based methods, the proposed ShapeMamba-EM demonstrates competitive even higher performance. We showcase several predictions in Fig. [4.](#page-40-0) More comparisons of other SOTA methods for each dataset are provided in the supplementary material.

Comparisions of Fine-Tuning Methods. In the comparative analysis of fine-tuning algorithms, it indicates that those based on Med-SAM3D reflects significant improvement over those based solely on SAM (such as MA-SAM and 3DSAM-Adapter). Such progress underscores the effectiveness of leveraging medical data in refining the model's ability to generalize to EM data. In Fig. [4,](#page-40-0) we observe that SAM-based methods demonstrate poor performance in segmenting small objects and dense cells, which strongly requires volumetric spatial information. Particularly for the 3D SAM-Adapter method, it seldom predicts the synapse. This limitation might stem from its reliance on the quality of prompts during training. Considering that the number of positive prompts in synapse datasets is substantially lower than that of negative prompts, the model faces challenges in learning useful information effectively. Furthermore, its performance in cell segmentation is hindered by the static number of prompts.

Ablation Study for 3D Mamba Adapter and 3D LSD Encoder. We further evaluate the effectiveness of the 3D Mamba Adapter  $(w/\sigma M)$  and the 3D LSD Encoder  $(w/\sigma L)$ . Results indicate an average increase of 5% with the use of the 3D Mamba Adapter and 8% with the 3D LSD Encoder. Additionally, we observe that the impact of the 3D Mamba Adapter on the nuclei segmentation task is relatively minor, potentially attributable to the smaller size of the images and objects to be segmented. Moreover, compared to methods that utilize multihead self-attention modules, such as MSAM3D and 3DSAMA, the experiments confirm Mamba's superior performance. The segmentation results of the models with and without 3D Mamba Adapter are shown in the supplementary material.

Limitations. This paper focuses on fine-tuning foundation models for EM images. Due to space constraints, we did not compare with other Mamba-based methods as they are 2D models and do not support 3D segmentation. Additionally, we used the 3D U-Net for LSD estimation because the original method uses it. We plan to explore more advanced models in future work.

# 4 Discusion and Conclusion

In summary, ShapeMamba-EM offers an innovative fine-tuning method for EM segmentation, leveraging a 3D medical foundation model to address unique challenges of EM data such as high resolution and complex tissues. It surpasses traditional models by integrating 3D Mamba Adapters and Local Shape Descriptors Encoder, improving accuracy and efficiency. Extensive tests on diverse EM datasets highlight its effectiveness in high-resolution image segmentation, setting new benchmarks. This work aims to enhance EM segmentation and showcase the adaptability of medical foundation models for in-depth biological studies.

Acknowledgments. This work was supported by the Natural Science Foundation of China under contract 62088102, the Beijing Natural Science Foundation (Grant No. JQ24023), and the Beijing Municipal Science & Technology Commission Project (No. Z231100006623010). We also acknowledge the Biomedical Computing Platform of National Biomedical Imaging Center and High-Performance Computing Platform of Peking University for providing computational resources.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-41-1"></span>1. CREMI Challenge, <https://cremi.org/>
- <span id="page-41-0"></span>2. Arganda-Carreras, I., Turaga, S.C., Berger, D.R., Cireşan, D., Giusti, A., Gambardella, L.M., Schmidhuber, J., Laptev, D., Dwivedi, S., Buhmann, J.M., Liu, T., Seyedhosseini, M., Tasdizen, T., Kamentsky, L., Burget, R., Uher, V., Tan, X., Sun, C., Pham, T.D., Bas, E., Uzunbas, M.G., Cardona, A., Schindelin, J., Seung, H.S.: Crowdsourcing the creation of image segmentation algorithms for connectomics. Frontiers in Neuroanatomy 9, 142 (2015)

- <span id="page-42-12"></span>3. Casser, V., Kang, K., Pfister, H., Haehn, D.: Fast mitochondria detection for connectomics. In: Proceedings of the Third Conference on Medical Imaging with Deep Learning. vol. 121, pp. 111–120 (06–08 Jul 2020)
- <span id="page-42-3"></span>4. Chen, C., Miao, J., Wu, D., Yan, Z., Kim, S., Hu, J., Zhong, A., Liu, Z., Sun, L., Li, X., et al.: MA-SAM: Modality-agnostic sam adaptation for 3D medical image segmentation. arXiv preprint [arXiv:2309.08842](http://arxiv.org/abs/2309.08842) (2023)
- <span id="page-42-7"></span>5. Çiçek, Ö., Abdulkadir, A., Lienkamp, S.S., Brox, T., Ronneberger, O.: 3D U-Net: learning dense volumetric segmentation from sparse annotation. In: Medical Image Computing and Computer-Assisted Interventino, 21, 2016, Proceedings, Part II 19. pp. 424–432. Springer (2016)
- <span id="page-42-5"></span>6. Gong, S., Zhong, Y., Ma, W., Li, J., Wang, Z., Zhang, J., Heng, P.A., Dou, Q.: 3DSAM-adapter: Holistic adaptation of sam from 2D to 3D for promptable medical image segmentation. arXiv preprint [arXiv:2306.13465](http://arxiv.org/abs/2306.13465) (2023)
- <span id="page-42-8"></span>7. Gu, A., Dao, T.: Mamba: Linear-time sequence modeling with selective state spaces. arXiv preprint [arXiv:2312.00752](http://arxiv.org/abs/2312.00752) (2023)
- <span id="page-42-9"></span>8. Gu, A., Goel, K., Ré, C.: Efficiently modeling long sequences with structured state spaces. arXiv preprint [arXiv:2111.00396](http://arxiv.org/abs/2111.00396) (2021)
- <span id="page-42-10"></span>9. Guay, M.D., Emam, Z.A., Anderson, A.B., Aronova, M.A., Pokrovskaya, I.D., Storrie, B., Leapman, R.D.: Dense cellular segmentation for EM using 2D–3D neural network ensembles. Scientific reports 11(1), 2561 (2021)
- <span id="page-42-15"></span>10. Hatamizadeh, A., Nath, V., Tang, Y., Yang, D., Roth, H., Xu, D.: Swin UNETR: Swin transformers for semantic segmentation of brain tumors in MRI images. arXiv preprint [arXiv:2201.01266](http://arxiv.org/abs/2201.01266) (2022)
- <span id="page-42-0"></span>11. Hulse, B.K., Haberkern, H., Franconville, R., Turner-Evans, D., Takemura, S.y., Wolff, T., Noorman, M., Dreher, M., Dan, C., Parekh, R., et al.: A connectome of the drosophila central complex reveals network motifs suitable for flexible navigation and context-dependent action selection. Elife 10 (2021)
- <span id="page-42-1"></span>12. Isensee, F., Jaeger, P.F., Kohl, S.A., Petersen, J., Maier-Hein, K.H.: nnU-Net: a self-configuring method for deep learning-based biomedical image segmentation. Nature methods 18(2), 203–211 (2021)
- <span id="page-42-6"></span>13. Jie, S., Deng, Z.H.: Fact: Factor-tuning for lightweight adaptation on vision transformer. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 37, pp. 1060–1068 (2023)
- <span id="page-42-2"></span>14. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., et al.: Segment anything. arXiv preprint [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-42-11"></span>15. Lee, K., Zung, J., Li, P., Jain, V., Seung, H.S.: Superhuman accuracy on the SNEMI3D connectomics challenge. arXiv preprint [arXiv:1706.00120](http://arxiv.org/abs/1706.00120) (2017)
- <span id="page-42-14"></span>16. Lin, Z., Wei, D., Jang, W.D., Zhou, S., Chen, X., Wang, X., Schalek, R., Berger, D., Matejek, B., Kamentsky, L., et al.: Two stream active query suggestion for active learning in connectomics. In: Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part XVIII 16. pp. 103–120. Springer (2020)
- <span id="page-42-13"></span>17. Lin, Z., Wei, D., Petkova, M.D., Wu, Y., Ahmed, Z., Zou, S., Wendt, N., Boulanger-Weill, J., Wang, X., Dhanyasi, N., et al.: NucMM dataset: 3D neuronal nuclei instance segmentation at sub-cubic millimeter scale. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 164–174. Springer (2021)
- <span id="page-42-4"></span>18. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nature Communications 15(1), 654 (2024)

- <span id="page-43-10"></span>19. Ma, J., Li, F., Wang, B.: U-mamba: Enhancing long-range dependency for biomedical image segmentation. arXiv preprint [arXiv:2401.04722](http://arxiv.org/abs/2401.04722) (2024)
- <span id="page-43-0"></span>20. Motta, A., Berning, M., Boergens, K.M., Staffler, B., Beining, M., Loomba, S., Hennig, P., Wissler, H., Helmstaedter, M.: Dense connectomic reconstruction in layer 4 of the somatosensory cortex. Science 366(6469), eaay3134 (2019)
- <span id="page-43-11"></span>21. Qiao, Y., Yu, Z., Wu, Q.: VLN-PETL: Parameter-efficient transfer learning for vision-and-language navigation. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 15443–15452 (2023)
- <span id="page-43-1"></span>22. Schneider-Mizell, C.M., Gerhard, S., Longair, M., Kazimiers, T., Li, F., Zwart, M.F., Champion, A., Midgley, F.M., Fetter, R.D., Saalfeld, S., et al.: Quantitative neuroanatomy for connectomics in drosophila. Elife 5, e12059 (2016)
- <span id="page-43-9"></span>23. Sheridan, A., Nguyen, T.M., Deb, D., Lee, W.C.A., Saalfeld, S., Turaga, S.C., Manor, U., Funke, J.: Local shape descriptors for neuron segmentation. Nature Methods 20(2), 295–303 (2023)
- <span id="page-43-2"></span>24. Shi, R., Bi, K., Du, K., Ma, L., Fang, F., Duan, L., Jiang, T., Huang, T.: Ps-net: human perception-guided segmentation network for em cell membrane. Bioinformatics 39(8), btad464 (2023)
- 25. Shi, R., Duan, L., Huang, T., Jiang, T.: Evidential uncertainty-guided mitochondria segmentation for 3d em images. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 38, pp. 4847–4855 (2024)
- <span id="page-43-3"></span>26. Shi, R., Wang, W., Li, Z., He, L., Sheng, K., Ma, L., Du, K., Jiang, T., Huang, T.: U-risc: an annotated ultra-high-resolution electron microscopy dataset challenging the existing deep learning algorithms. Frontiers in Computational Neuroscience 16, 842760 (2022)
- <span id="page-43-12"></span>27. Tu, C.H., Mai, Z., Chao, W.L.: Visual query tuning: Towards effective usage of intermediate representations for parameter and memory efficient transfer learning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7725–7735 (2023)
- <span id="page-43-5"></span>28. Wang, H., Guo, S., Ye, J., Deng, Z., Cheng, J., Li, T., Chen, J., Su, Y., Huang, Z., Shen, Y., et al.: SAM-Med3D. arXiv preprint [arXiv:2310.15161](http://arxiv.org/abs/2310.15161) (2023)
- <span id="page-43-4"></span>29. Wei, D., Lin, Z., Franco-Barranco, D., Wendt, N., Liu, X., Yin, W., Huang, X., Gupta, A., Jang, W.D., Wang, X., et al.: MitoEM dataset: Large-scale 3D mitochondria instance segmentation from EM images. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 66–76 (2020)
- <span id="page-43-8"></span>30. Wu, J., Fu, R., Fang, H., Liu, Y., Wang, Z., Xu, Y., Jin, Y., Arbel, T.: Medical sam adapter: Adapting segment anything model for medical image segmentation. arXiv preprint [arXiv:2304.12620](http://arxiv.org/abs/2304.12620) (2023)
- <span id="page-43-6"></span>31. Zhang, Y., Hu, S., Jiang, C., Cheng, Y., Qi, Y.: Segment anything model with uncertainty rectification for auto-prompting medical image segmentation. arXiv preprint [arXiv:2311.10529](http://arxiv.org/abs/2311.10529) (2023)
- <span id="page-43-7"></span>32. Zhang, Y., Shen, Z., Jiao, R.: Segment anything model for medical image segmentation: Current applications and future directions. arXiv preprint [arXiv:2401.03495](http://arxiv.org/abs/2401.03495) (2024)

Image /page/44/Picture/0 description: A square button with rounded corners contains a circular icon and text. The icon is a gray circle with a darker gray ribbon or bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# Symmetry Awareness Encoded Deep Learning Framework for Brain Imaging Analysis

Yang Ma<sup>1,2</sup>, Dongang Wang<sup>2,5</sup>, Peilin Liu<sup>2,3</sup>, Lynette Masters<sup>4</sup>, Michael Barnett<sup>2,5</sup>, Weidong Cai<sup>1</sup>, and Chenyu Wang<sup>2,5( $\boxtimes$ )</sup>

<sup>1</sup> School of Computer Science, University of Sydney, Darlington, NSW 2008, Australia

<sup>2</sup> Brain and Mind Centre, University of Sydney, Darlington, NSW 2050, Australia

<sup>3</sup> School of Mathematics and Statistics, University of Sydney, Darlington, NSW 2050, Australia

<sup>4</sup> I-MED Radiology Network, Sydney, NSW 2050, Australia

<sup>5</sup> Sydney Neuroimaging Analysis Centre, Darlington, NSW 2050, Australia <EMAIL>

Abstract. The heterogeneity of neurological conditions, ranging from structural anomalies to functional impairments, presents a significant challenge in medical imaging analysis tasks. Moreover, the limited availability of well-annotated datasets constrains the development of robust analysis models. Against this backdrop, this study introduces a novel approach leveraging the inherent anatomical symmetrical features of the human brain to enhance the subsequent detection and segmentation analysis for brain diseases. A novel Symmetry-Aware Cross-Attention (SACA) module is proposed to encode symmetrical features of left and right hemispheres, and a proxy task to detect symmetrical features as the Symmetry-Aware Head (SAH) is proposed, which guides the pretraining of the whole network on a vast 3D brain imaging dataset comprising both healthy and diseased brain images across various MRI and CT. Through meticulous experimentation on downstream tasks, including both classification and segmentation for brain diseases, our model demonstrates superior performance over state-of-the-art methodologies, particularly highlighting the significance of symmetry-aware learning. Our findings advocate for the effectiveness of incorporating symmetry awareness into pretraining and set a new benchmark for medical imaging analysis, promising significant strides toward accurate and efficient diagnostic processes. Code is available at [https://github.com/bitMyron/sa-swin.](https://github.com/bitMyron/sa-swin)

Keywords: Symmetry-Aware Cross-Attention (SACA) · Self-Supervised Learning · Neuroimaging Diagnosis

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_69.](https://doi.org/10.1007/978-3-031-72390-2_69)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 742–752, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_69)\_69

# 1 Introduction

In many neurological and psychiatric conditions, the symmetry nature of the left and right brain hemispheres plays a pivotal role in brain disease diagnosis and monitoring, often acting as a precursor to various disorders. The measurement of pathological structural and functional asymmetries could also be used in managing disease progression and the effectiveness of treatment [\[18\]](#page-54-0). The significance of these asymmetrical alterations is especially marked in diseases like Alzheimer's disease (AD) [\[14\]](#page-53-0) and schizophrenia [\[22\]](#page-54-1), where hemispheric symmetry is also considered as one of the most indicative biomarkers by radiologists. Figure [1](#page-45-0) showcases instances of pathologies affecting the brain's symmetry.

Image /page/45/Figure/3 description: The image displays four MRI scans of the human brain. Image (a) shows a healthy brain scan. Images (b), (c), and (d) show brain scans with abnormalities, indicated by red dashed circles. Image (b) highlights a large tumor in the frontal lobe. Image (c) shows an abnormality in the temporal lobe. Image (d) points to a lesion in the parietal lobe.

<span id="page-45-0"></span>Fig. 1. Brain T1 images: (a) represents a healthy case, displaying symmetrical shape, structure, and image intensity.  $(b)$ – $(d)$  depict patients with a brain tumor, Alzheimer's Disease, and focal epilepsy, respectively, and the noticeable structural asymmetry parts are circled out.

Leveraging the clinical experience and identifying such asymmetrical features in neuroimaging analysis tasks can further enhance the efficacy of deep learning tools used in triage, annotation, and diagnosis. Certain approaches attempt to identify asymmetries by subtracting the  $L/R$  flipped image from the original and using the resulting subtraction as input for deep neural networks [\[12\]](#page-53-1). Other strategies involve segmenting the regions of interest on the original and flipped images separately, employing the pair of segmentation masks to compute an additional loss to improve model performance [\[13\]](#page-53-2). However, hemispheric symmetrical features were only used for pre-processing or data augmentation rather than being encoded within the network structure, which limits the generalization ability to extend to the general neuroimaging analysis process.

To better leverage symmetrical features, we introduce an innovative Symmetry-Aware Cross-Attention (SACA) module, advancing the attention mechanism's application in neuroimaging analysis. Our module enhances the transformer blocks' capabilities by implementing cross-attention mechanisms between the original input and its symmetrical counterpart, compelling the network to focus on symmetrical comparisons and semantic feature encoding. This approach mimics the expert diagnostic strategy, leveraging self-supervised and supervised training processes to improve the model's understanding of brain anatomy and its inherent symmetries.

The SACA module can be pretrained with a substantial amount of data based on a self-supervised learning framework to be further used for downstream tasks, including disease diagnosis, brain structure, and lesion segmentation. The advancement of self-supervised learning has been illustrated in previous work [\[1\]](#page-52-0), which involve generating pre-text tasks, including inpainting  $[10]$ , rotation  $[9]$  $[9]$  and contrastive coding [\[2\]](#page-53-5), to embed intrinsic features. Besides these tasks widely used in self-supervised learning methods, we introduce a Symmetry-Aware Head (SAH) to enhance the pretraining process by focusing on the contrastive symmetrical features of the left and right hemispheres. Since they are entangled with the patient status (i.e., healthy or disease), the hemispheric symmetrical features are utilized in computing the symmetry-aware loss.

To fulfill the pretraining, we have compiled an extensive dataset comprising brain MRI and CT images from both healthy subjects (assumed to be symmetrical) and diseased subjects (assumed with asymmetrical features). Empirical experiments of our approach, through extensive pretraining on brain CT and MRI datasets and subsequent evaluations on various classification and segmentation tasks, underscore a notable improvement over existing baseline models.

In general, our primary contributions are outlined as follows:

- We introduce a novel Symmetry-Aware Cross-Attention (SACA) module, which leverages a cross-attention mechanism to analyze the relationship between an image and its symmetrical counterpart, facilitating a deeper understanding of brain anatomy and its inherent symmetries.
- The proposed network is pretrained on a symmetry-aware self-supervised process and can be further applied in real-world clinical datasets and analysis tasks, including classification and segmentation for multiple modalities.
- A vast dataset spanning MRI and CT modalities has been curated, and our approach has undergone extensive testing and demonstrated state-of-the-art performance on diagnosis and segmentation tasks.

# 2 Methodology

In this study, we leverage a substantial corpus of 3D brain imaging data, denoted as  $\mathcal{I}^0 = {\mathbf{I}_n}$ . Each image is associated with a binary label  $\mathcal{Y}^0 = \{0, 1\}$  indicating the health status of the subject (healthy or diseased), where  $y_n^0 \in \mathcal{Y}^0$ . Our objective is to train a model to encapsulate structural insights into the human brain. The comprehensive pipeline is depicted in Fig. [2.](#page-48-0)

## 2.1 Preprocssing

A midway registration pipeline was introduced to standardize the input image to position the brain within a standardized 3D coordinate framework, ensuring that flipping the image volume along its sagittal axis could generally map the left and right cerebral hemispheres correctly. Given an original input image **I**, we first generate its mirror image  $\mathbf{I}'$  by flipping **I** across the vertical plane of the image.

We then obtain the affine transformation  $\mathcal{T} : \mathbf{I} \to \mathbf{I}'$  with the widely used linear registration tool FMRIB's Linear Image Registration Tool (FLIRT) [\[17\]](#page-53-6). Based on the generated transformation matrix, the midway points of all transformation operations can be further calculated and formed the midway transformation  $T_{1/2}$ . The midway transformation is applied to  $I$ , resulting in  $I$ , whose sagittal plane is perfectly aligned with respect to the vertical plane of the image volume. By the midway registration, the simple flip of the input image ensures the mirroring of the left and right hemispheres, assisting the model in learning and utilizing the inherent hemispheric symmetry of the human brain.

After the midway registration, random sub-volumes, denoted as  $\mathcal{X} = {\mathbf{x}_n}$ , are extracted as inputs to networks from the collection of all aligned images  $\mathcal{I} = {\mathbf{I}_n}$ . Since the input images are standardized by the midway registration, for each patch  $\mathbf{x} \in \mathbb{R}^{H \times W \times D}$ , the mirrored counterpart  $\mathbf{x}'$  with respect to the sagittal plane  $\Phi_s$  can be identified and can be flipped to obtain  $\tilde{\mathbf{x}}$ . Note that the generated  $\tilde{\mathbf{x}}$  is expected to closely resemble the original patch  $\mathbf{x}$ , barring minor structural discrepancies inherent to the intrinsic hemispheric symmetry of the human brain. Both original patch **x**'s and their flipped symmetrical counterparts  $\tilde{\mathbf{x}}$ 's are used as the input to the encoder network as sub-volumes.

As illustrated in Fig. [2,](#page-48-0) two distinct rotations  $R = \{r_1, r_2\}$  are conducted on both sub-volumes and two random inner cutouts  $C = \{c_1, c_2\}$  are conducted on original sub-volume **x**, resulting in augmented sub-volumes  $\{x_1, x_2, \tilde{x}_1, \tilde{x}_2\}$ . The augmented sub-volumes are segmented into patches of the size  $(h, w, d)$ , ending up with a sequence of the length  $\frac{H}{h} \times \frac{W}{w} \times \frac{D}{d}$ . These patches are processed through an embedding network and subsequently passed into a 3D Sliding-window (Swin) Transformer architecture [\[20](#page-54-2)], which consists of four stages and eight transformer layers. This results in a reduced sequence of the length  $\frac{H}{16h} \times \frac{W}{16w} \times \frac{D}{16d}$  with the embedding feature dimension 768.

### 2.2 Symmetry-Aware Cross-Attention (SACA) Module

Following the Swin Transformer Encoder [\[11](#page-53-7)], we introduce a Symmetry-Aware Cross-Attention (SACA) module to foster deep symmetrical feature alignment and referencing. The SACA module processes input embedding sequences **f**, applying self-attention mechanisms to **f** and cross-attention to the flipped symmetrical counterpart **f**. This dual attention strategy, as depicted in Fig. [3,](#page-49-0) is intended to exchange information between the raw sub-volume and the flipped symmetrical counterpart.

The cross-attention mechanism within this module is represented as follows:

SACA(f, \tilde{f}) = \text{Softmax}\left(\frac{Q\tilde{K}^T}{\sqrt{D}}\right)\tilde{V}, \text{where}

$$
\begin{cases} Q = \mathbf{f}W_Q\\ \tilde{K} = \tilde{\mathbf{f}}W_K\\ \tilde{V} = \tilde{\mathbf{f}}W_V \end{cases}.
$$
 (1)

Q represents the queries generated from the features of the original patch **f**, and  $K$  and  $V$  are keys and values generated from the counterpart patch  $\mathbf{f}$ , with D denoting the dimensionality of the query and key vectors. The matrices W

Image /page/48/Figure/1 description: The image displays a diagram illustrating a medical image processing pipeline. Part (a) shows a process of midway registration and flipping of a medical image, labeled as I, I', and I-bar. Part (b) details a network architecture that takes multiple views of a medical image (x, x1, x2, x-tilde, x1-tilde, x2-tilde) as input. These inputs are processed through patch partitioning and Swin encoders, followed by SACA encoders. The outputs are then fed into proxy heads, including CH, IH, RH, and SAH modules, which generate features (f1, f1-tilde, f2, f2-tilde) used for various loss functions: L\_Contrastive, L\_Inpainting, L\_Rotation, and L\_Symmetry. The diagram highlights the flow of data and the components of the proposed model.

<span id="page-48-0"></span>Fig. 2. (a) The process of midway registration to align input images; (b) The framework applied to input sub-volumes and their sagittal symmetrical counterparts. This framework is augmented by the Symmetry-Aware Cross-Attention (SACA) module and is optimized through a composite loss function that integrates inpainting, rotation, contrastive, and symmetry losses.

are learnable weights for the query, key, and value transformations within the SACA module. With our novel SACA module, the contrastive information from the original patch and its flipped symmetrical counterpart could be utilized, and the level of symmetry of the patch could be learned along the process, enabling the network to recognize symmetrical features of input brain images.

### 2.3 Symmetry-Aware Pretraining Proxies

Upon obtaining the encoded features from the SACA-based encoder, they are directed to four distinct proxy heads, each responsible for a specific pretraining task. The proxies include one symmetry-aware head that identifies the symmetrical features of the input sub-volumes and three heads that deal with intrinsic features of the input sub-volume (i.e., inpainting, rotation, and contrastive coding) as shown in Fig. [2.](#page-48-0)

The Symmetry-Aware Head (SAH) consists of a max pooling layer followed by a multi-layer perceptron (MLP), which distills the features for an original input patch and the symmetrical counterpart as  $f^s$  and  $\tilde{f}^s$ . These features serve as the foundation of computing the conditional symmetry loss, expressed as:

$$
\mathcal{L}_{Symmetry} = -\log \frac{\sum \exp(\text{sim}(\mathbf{f}^s, \tilde{\mathbf{f}}^s)/\tau) \cdot y^0}{\sum \exp(\text{sim}(\mathbf{f}^s, \tilde{\mathbf{f}}^s)/\tau)},
$$
\n(2)

where  $y^0$  indicates the subject's health status, with 1 for healthy control and 0 for patients. The parameter  $\tau$  is a temperature factor adjusting the logits'

Image /page/49/Figure/1 description: The image displays a two-part diagram illustrating a neural network architecture. Part (a) shows a cube-shaped representation of brain scans, labeled 'x', 'x', and 'x''. Arrows indicate operations: a blue curved arrow labeled 'Self-Attention' points from one 'x' to another 'x', an orange arrow labeled 'SACA' points from one 'x' to 'x', and a green double-headed arrow labeled 'Flip' points between 'x' and 'x''. A star symbol is placed on one of the 'x' cubes. Part (b) details a cross-attention mechanism. It shows input features 'f' and 'f' being transformed by weight matrices WQ, WK, and WV to produce Q, K, and V. These are then used in a cross-attention calculation, resulting in a 'CA Score' and output features. The diagram highlights the flow of information through these components.

<span id="page-49-0"></span>Fig. 3. Symmetry-Aware Cross-Attention (SACA) Module: (a) The computation of attention for each token within the SACA module; (b) Cross-attention calculation of SACA module structure.

scaling. The cosine similarity  $\sin(\mathbf{a}, \mathbf{b})$  is used to measure the similarity between embedded features. The core premise of this formulation is that for subjects with healthy brains, a given patch and its symmetrical counterpart should exhibit similar visual and structural characteristics, whereas deviations and asymmetries are expected in the presence of pathological conditions, reflecting the brain's asymmetrical response to disease.

The heads for intrinsic feature awareness follow [\[27](#page-54-3)], including the Inpainting Head (IH), defined as a decoder to reconstruct the occluded voxels; the Rotation Head (RH), defined by an MLP classifier to identify the rotation angles; and the Contrastive Coding Head (CH), defined by two MLP layers to distinguish the present sub-volumes from other sub-volumes. The losses are defined as:

$$
\mathcal{L}_{Inpainting} = \|\mathbf{f}^{p} - \mathbf{x}\|_{1}
$$
\n
$$
\mathcal{L}_{Rotation} = -r \log(\text{Softmax}(\mathbf{f}^{r})),
$$
\n
$$
\mathcal{L}_{Contrastive} = -\log \frac{\exp(\text{sim}(\mathbf{f}_{i}^{c}, \mathbf{f}_{j}^{c})/\tau)}{\sum \mathbb{1}_{k \neq i} \exp(\text{sim}(\mathbf{f}_{i}^{c}, \mathbf{f}_{k}^{c})/\tau)},
$$
\n(3)

where  $f^p$ ,  $f^r$ ,  $f^c$  represent the concatenated output from each head, and **x** is the input sub-volume. Through comprehensive grid search experimentation, the four losses are configured to be evenly summed up for pretraining, balancing the contribution of each task effectively. The pretrained symmetry-aware network can be further applied for downstream tasks, as shown in Sect. [3.](#page-49-1)

<span id="page-49-1"></span>

# 3 Experiments and Results

We evaluated our proposed approach by pretraining on large neuroimaging datasets and downstream classification and segmentation tasks.

### 3.1 Datasets and Downstream Tasks

We curated two large datasets for brain image pretraining using head CT and brain MRI images separately. The MRI dataset comprises 3D T1 images of 3,509 healthy adults and 2,917 patients sourced from a variety of publicly available datasets  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  $[3,4,16,16,19,23,24,29]$  (more details in supplementary). The CT dataset is derived from our proprietary in-house collection, including 2025 images from healthy individuals and 4693 images from patients afflicted with a range of brain diseases, with images from three brain diseases (i.e., hemorrhage, fracture, midline shift) reserved for downstream tasks.

For MRI-based evaluations, we assess the zero-shot and few-shot classification capabilities of our model across several disease identification tasks, including Focal Epilepsy [\[25](#page-54-8)], ADHD [\[5\]](#page-53-11), and Schizophrenia [\[26\]](#page-54-9). Furthermore, we explore the model's transfer learning proficiency in segmentation tasks using images with different modalities from the MRI pretraining. This includes the BraTS2021 dataset [\[3](#page-53-8)] and the MSSEG2016 dataset [\[8](#page-53-12)].

For CT imaging, we conducted few-shot training using reserved in-house data of three brain diseases, including intracranial hemorrhage, bone fracture, and midline shift, and evaluated the performance on the public dataset CQ500 [\[7\]](#page-53-13) to demonstrate the model's versatility across varied clinical conditions.

### 3.2 Implementation Details

The proposed model undergoes pretraining with distinct settings for image classification and segmentation tasks. Specifically, the whole image classification task pretraining is conducted with a batch size of 2 and images resized to  $160\times160\times160$ , and a batch size of 4 and sub-volume dimensions of  $96\times96\times96$ was used for segmentation-related pretraining. The pretraining phase is executed with a learning rate of  $6\times10^{-6}$  over 300 epochs. For downstream tasks, the learning rate is set to  $1 \times 10^{-5}$ , spanning 100 epochs. All models are trained utilizing NVIDIA Tesla V100 GPUs.

The pretrained models are further experimented with downstream tasks under a 5-fold cross-validation approach across all experiments, with the exception of the multiple sclerosis lesion segmentation on MSSEG, which has a predefined validation set [\[21\]](#page-54-10). The average performance scores, including Area under the ROC Curve (AUC), F1-measure, and Dice Similarity Coefficient (DSC), are reported.

### 3.3 Results

The classification outcomes are delineated in Tables [1](#page-51-0) and [2.](#page-51-1) In the supervised fine-tuning scenario (checked under "+SFT"), our model consistently surpassed the performance of the Swin MLP [\[27](#page-54-3)] across all evaluated downstream tasks, especially in some diseases that display significant asymmetrical features, such as ADHD and Midline Shift. Besides, our method has achieved comparable performance in CT disease diagnosis as that of [\[6](#page-53-14)], but only hundreds of labeled cases were used for fine-tuning, much less than in [\[6\]](#page-53-14) where 313,318 scans were collected and manually labeled. This underscores the significant advantage of incorporating symmetrical awareness during the pretraining phase.

| Experiments   | +Pretrain | +SACA | +SFT | Focal Epilepsy |              | Schizophrenia |              | ADHD         |              |
|---------------|-----------|-------|------|----------------|--------------|---------------|--------------|--------------|--------------|
|               |           |       |      | AUC            | F1           | AUC           | F1           | AUC          | F1           |
| Swin MLP [27] | ✗         | ✗     | ✓    | 0.678          | 0.683        | 0.656         | 0.810        | 0.685        | 0.625        |
|               | ✓         | ✗     | ✗    | 0.522          | 0.651        | 0.594         | 0.749        | 0.514        | 0.609        |
|               | ✓         | ✗     | ✓    | 0.713          | 0.695        | 0.627         | 0.803        | 0.642        | 0.645        |
| Ours          | ✓         | ✓     | ✗    | 0.631          | 0.683        | 0.663         | 0.760        | 0.515        | 0.625        |
|               | ✓         | ✓     | ✓    | <b>0.778</b>   | <b>0.788</b> | <b>0.719</b>  | <b>0.836</b> | <b>0.792</b> | <b>0.727</b> |

<span id="page-51-0"></span>Table 1. Benchmark methods on the MRI T1-only classification tasks.

Furthermore, we assessed the impact of symmetrical awareness in a zero-shot learning context. Remarkably, our pretrained model exhibited superior zero-shot capabilities in almost all tasks. This indicates that our pretraining strategy effectively captures critical pathological features based on symmetry characteristics, facilitating generalization to novel tasks without requiring task-specific data.

<span id="page-51-1"></span>Table 2. Benchmark methods on the CT classification tasks evaluated on the CQ500 dataset [\[6](#page-53-14)].

| Experiments   | +Pretrain | +SACA | +SFT | Hemorrhage   |              | Fracture     |              | Midline Shift |              |
|---------------|-----------|-------|------|--------------|--------------|--------------|--------------|---------------|--------------|
|               |           |       |      | AUC          | F1           | AUC          | F1           | AUC           | F1           |
| Qure.ai [6]   | X         | X     | ✓    | 0.942        | 0.761        | 0.962        | 0.508        | 0.970         | 0.704        |
| Swin MLP [27] | X         | X     | ✓    | 0.872        | 0.776        | 0.918        | 0.460        | 0.926         | 0.640        |
|               | X         | X     | ✓    | 0.751        | 0.685        | 0.781        | 0.353        | 0.788         | 0.413        |
|               | X         | X     | ✓    | 0.911        | 0.748        | 0.952        | 0.469        | 0.942         | 0.620        |
| Ours          | ✓         | X     | ✓    | 0.793        | 0.722        | 0.803        | 0.267        | 0.866         | 0.508        |
|               | ✓         | ✓     | ✓    | <b>0.958</b> | <b>0.781</b> | <b>0.969</b> | <b>0.513</b> | <b>0.986</b>  | <b>0.728</b> |

The assessment of models' proficiency in adapting to segmentation tasks is summarized in Table [3,](#page-52-1) which manifests the superior performance of our method. Additionally, we conducted an ablation study to discern the relative contribution of our proposed SACA module and SAH proxy. Both the SAH only pretraining process and the SACA module could improve the performance of segmentation steadily, and the combination of all pretraining proxies and the novel SACA module could further yield the most substantial benefits, underscoring the synergetic effect of combining these diverse learning strategies. This comprehensive evaluation demonstrates our method's superior capability in leveraging pretraining proxies to enhance model performance on segmentation tasks, highlighting its potential for advancing medical image analysis.

<span id="page-52-1"></span>Table 3. Benchmark methods and ablation study of our method on the MRI segmentation tasks. "IRC" represents commonly used pretrain heads for inpainting (I), rotation (R), and contrastive (C).

| Experiments    | +IRC | +SAH | +SAC A BraTS [3] |              |              |              | MSSEG[8]     |              |
|----------------|------|------|------------------|--------------|--------------|--------------|--------------|--------------|
|                |      |      | DiceET           | DiceTC       | DiceWT       | DiceAvg      |              |              |
| nnU-Net [15]   | x    | x    | x                | 0.883        | 0.927        | 0.913        | 0.908        | -            |
| TransBTS [28]  | x    | x    | x                | 0.868        | 0.911        | 0.898        | 0.891        | -            |
| SwinUNETR [11] | x    | x    | x                | 0.891        | 0.933        | 0.917        | 0.913        | 0.594        |
| Ours           | x    | ✔    | x                | 0.897        | 0.935        | 0.920        | 0.917        | 0.615        |
|                | x    | x    | ✔                | 0.901        | 0.937        | 0.928        | 0.922        | 0.640        |
|                | x    | ✔    | ✔                | 0.909        | 0.940        | 0.935        | 0.928        | 0.654        |
|                | ✔    | ✔    | ✔                | <b>0.912</b> | <b>0.946</b> | <b>0.940</b> | <b>0.932</b> | <b>0.680</b> |

# 4 Conclusion

Our study introduces an innovative framework utilizing 3D brain imaging data of various modalities, significantly enhancing neuroimage analysis by encoding symmetry-aware features in deep neural networks. The integration of the Symmetry-Aware Cross-Attention (SACA) module and the symmetry-aware pretraining proxy has shown remarkable improvements in classification and segmentation tasks, setting new benchmarks over existing models. This work highlights the critical role of anatomical symmetry in neuroimaging analysis and suggests a promising direction for developing more accurate, robust, and generalized AI models in healthcare leveraging intrinsic anatomical features.

Acknowledgments. The authors would like to express their gratitude to the BCR Scholarship for Computational Neuroscience and the Faculty of Engineering Research Stipend Scholarship at the University of Sydney for their financial support, which enabled this research and its contributions to medical imaging and healthcare.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

<span id="page-52-0"></span>1. Atito, S., Awais, M., Kittler, J.: Sit: self-supervised vision transformer. arXiv preprint [arXiv:2104.03602](http://arxiv.org/abs/2104.03602) (2021)

- <span id="page-53-5"></span>2. Azizi, S., et al.: Big self-supervised models advance medical image classification. In: Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 3478–3488 (2021)
- <span id="page-53-8"></span>3. Baid, U., Ghodasara, S., Mohan, S., Bilello, M., Calabrese, E., et al.: The rsnaasnr-miccai brats 2021 benchmark on brain tumor segmentation and radiogenomic classification. arXiv preprint [arXiv:2107.02314](http://arxiv.org/abs/2107.02314) (2021)
- <span id="page-53-9"></span>4. Biswal, B.B., Mennes, M., Zuo, X.N., Gohel, S., Kelly, C., et al.: Toward discovery science of human brain function. Proceedings of the National Academy of Sciences 107(10), 4734–4739 (2010)
- <span id="page-53-11"></span>5. Booth, J.R., Cooke, G., et al.: Working memory and reward in children with and without attention deficit hyperactivity disorder (adhd) (2021). [https://doi.org/10.](https://doi.org/10.18112/openneuro.ds002424.v1.2.0) [18112/openneuro.ds002424.v1.2.0](https://doi.org/10.18112/openneuro.ds002424.v1.2.0)
- <span id="page-53-14"></span>6. Chilamkurthy, S., et al.: Development and validation of deep learning algorithms for detection of critical findings in head ct scans. arXiv preprint [arXiv:1803.05854](http://arxiv.org/abs/1803.05854) (2018)
- <span id="page-53-13"></span>7. Chilamkurthy, S., Ghosh, R., Tanamala, S., Biviji, M., et al.: Deep learning algorithms for detection of critical findings in head ct scans: a retrospective study. The Lancet 392(10162), 2388–2396 (2018)
- <span id="page-53-12"></span>8. Commowick, O., Kain, M., Casey, R., Ameli, R., Ferré, J.C., Kerbrat, A., Tourdias, T., Cervenansky, F., Camarasu-Pop, S., Glatard, T., et al.: Multiple sclerosis lesions segmentation from multiple experts: The miccai 2016 challenge dataset. Neuroimage 244, 118589 (2021)
- <span id="page-53-4"></span>9. Gidaris, S., Singh, P., Komodakis, N.: Unsupervised representation learning by predicting image rotations. arXiv preprint [arXiv:1803.07728](http://arxiv.org/abs/1803.07728) (2018)
- <span id="page-53-3"></span>10. Haghighi, F., Taher, M.R.H., Zhou, Z., Gotway, M.B., Liang, J.: Transferable visual words: Exploiting the semantics of anatomical patterns for self-supervised learning. IEEE transactions on medical imaging  $40(10)$ , 2857–2868 (2021)
- <span id="page-53-7"></span>11. Hatamizadeh, A., Nath, V., Tang, Y., Yang, D., Roth, H.R., Xu, D.: Swin unetr: Swin transformers for semantic segmentation of brain tumors in mri images. In: International MICCAI Brainlesion Workshop, pp. 272–284. Springer (2021)
- <span id="page-53-1"></span>12. Herzog, N.J., Magoulas, G.D.: Deep learning of brain asymmetry images and transfer learning for early diagnosis of dementia. In: International Conference on Engineering Applications of Neural Networks, pp. 57–70. Springer (2021)
- <span id="page-53-2"></span>13. Hua, Y., Yan, Z., Kuang, Z., Zhang, H., Deng, X., Yu, L.: Symmetry-aware deep learning for cerebral ventricle segmentation with intra-ventricular hemorrhage. IEEE Journal of Biomedical and Health Informatics 26(10), 5165–5176 (2022)
- <span id="page-53-0"></span>14. Illán, I.A., Górriz, J.M., Ramírez, J., et al.: Bilateral symmetry aspects in computer-aided alzheimer's disease diagnosis by single-photon emission-computed tomography imaging. Artificial intelligence in medicine 56(3), 191–198 (2012)
- <span id="page-53-15"></span>15. Isensee, F., Jäger, P.F., Full, P.M., Vollmuth, P., Maier-Hein, K.H.: nnu-net for brain tumor segmentation. In: Brainlesion: Glioma, Multiple Sclerosis, Stroke and Traumatic Brain Injuries: 6th International Workshop, BrainLes 2020, Held in Conjunction with MICCAI 2020, Lima, Peru, October 4, 2020, Revised Selected Papers, Part II 6, pp. 118–132. Springer (2021)
- <span id="page-53-10"></span>16. Jack Jr, C.R., et al.: The alzheimer's disease neuroimaging initiative (adni): Mri methods. J. Magnetic Resonance Imaging Official J. Int. Soc. Magnetic Resonance Med. 27(4), 685–691 (2008)
- <span id="page-53-6"></span>17. Jenkinson, M., Bannister, P., Brady, M., Smith, S.: Improved optimization for the robust and accurate linear registration and motion correction of brain images. Neuroimage 17(2), 825–841 (2002)

- <span id="page-54-0"></span>18. Kuo, F., Massoud, T.F.: Structural asymmetries in normal brain anatomy: A brief overview. Annals of Anatomy-Anatomischer Anzeiger 241, 151894 (2022)
- <span id="page-54-4"></span>19. LaMontagne, P.J., et al.: Oasis-3: longitudinal neuroimaging, clinical, and cognitive dataset for normal aging and alzheimer disease. MedRxiv, pp. 2019–12 (2019)
- <span id="page-54-2"></span>20. Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., et al.: Swin transformer: hierarchical vision transformer using shifted windows. In: Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 10012–10022 (2021)
- <span id="page-54-10"></span>21. Ma, Y., Zhang, C., Cabezas, M., Song, Y., Tang, Z., Liu, D., Cai, W., Barnett, M., Wang, C.: Multiple sclerosis lesion analysis in brain magnetic resonance images: techniques and clinical applications. IEEE Journal of Biomedical and Health Informatics 26(6), 2680–2692 (2022)
- <span id="page-54-1"></span>22. Narr, K.L., Bilder, R.M., Luders, E., Thompson, P.M., Woods, R.P., Robinson, D., Szeszko, P.R., et al.: Asymmetries of cortical shape: effects of handedness, sex and schizophrenia. Neuroimage  $34(3)$ , 939–948 (2007)
- <span id="page-54-5"></span>23. Nooner, K.B., Colcombe, S.J., Tobe, R.H., Mennes, M., Benedict, M.M., Moreno, A.L., Panek, L.J., Brown, S., Zavitz, S.T., Li, Q., et al.: The nki-rockland sample: a model for accelerating the pace of discovery science in psychiatry. Frontiers in neuroscience 6, 152 (2012)
- <span id="page-54-6"></span>24. Rowland, A., Burns, M., Hartkens, T., Hajnal, J., Rueckert, D., Hill, D.L.: Information extraction from images (ixi): Image processing workflows using a grid enabled image database. Proceedings of DiDaMIC 4, 55–64 (2004)
- <span id="page-54-8"></span>25. Schuch, F., Walger, L., Schmitz, M., et al.: An open presurgery mri dataset of people with epilepsy and focal cortical dysplasia type ii  $(2023)$ . [https://doi.org/](https://doi.org/10.18112/openneuro.ds004199.v1.0.5) [10.18112/openneuro.ds004199.v1.0.5](https://doi.org/10.18112/openneuro.ds004199.v1.0.5)
- <span id="page-54-9"></span>26. Soler-Vidal, J., Fuentes-Claramonte, P., Salgado-Pineda, P., Ramiro, N., García-León, M.Á., Torres, M.L., Arévalo, A., Guerrero-Pedraza, A., Munuera, J., Sarró, S., et al.: Brain correlates of speech perception in schizophrenia patients with and without auditory hallucinations. PloS one  $17(12)$ , e0276975 (2022)
- <span id="page-54-3"></span>27. Tang, Y., et al.: Self-supervised pre-training of swin transformers for 3d medical image analysis. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 20730–20740 (2022)
- <span id="page-54-11"></span>28. Wang, W., Chen, C., et al.: Transbts: Multimodal brain tumor segmentation using transformer. In: Medical Image Computing and Computer Assisted Intervention– MICCAI 2021: 24th International Conference, Strasbourg, France, September 27– October 1, 2021, Proceedings, Part I 24, pp. 109–119. Springer (2021)
- <span id="page-54-7"></span>29. Zuo, X.N., Anderson, J.S., Bellec, et al.: An open science resource for establishing reliability and reproducibility in functional connectomics. Sci. Data  $1(1)$ , 1–13 (2014)