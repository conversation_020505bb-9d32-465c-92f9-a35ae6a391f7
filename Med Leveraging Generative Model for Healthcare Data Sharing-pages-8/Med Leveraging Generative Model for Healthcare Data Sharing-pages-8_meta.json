{"table_of_contents": [{"title": "PromptSmooth: Certifying Robustness\nof Medical Vision-Language Models\nvia Prompt Learning", "heading_level": null, "page_id": 0, "polygon": [[76.58703071672355, 51.**********], [350.1953125, 51.**********], [350.1953125, 102.6717529296875], [76.58703071672355, 102.6717529296875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[38.96728515625, 508.39208984375006], [135.029296875, 508.39208984375006], [135.029296875, 522.07080078125], [38.96728515625, 522.07080078125]]}, {"title": "2 Related Work and Background", "heading_level": null, "page_id": 2, "polygon": [[38.29351535836177, 216.57958984375], [243.27645051194537, 216.57958984375], [243.27645051194537, 229.93261718750003], [38.29351535836177, 229.93261718750003]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 3, "polygon": [[52.55972696245733, 305.979736328125], [150.92150170648463, 305.979736328125], [150.92150170648463, 319.332763671875], [52.55972696245733, 319.332763671875]]}, {"title": "3.1 Zero-Shot Inference Based on Med-VLMs", "heading_level": null, "page_id": 3, "polygon": [[52.55972696245733, 418.8291015625], [290.580204778157, 418.8291015625], [290.580204778157, 430.5537109375], [52.55972696245733, 430.5537109375]]}, {"title": "3.2 PromptSmooth", "heading_level": null, "page_id": 4, "polygon": [[38.29351535836177, 170.98388671875], [143.73046875, 170.98388671875], [143.73046875, 182.05712890625], [38.29351535836177, 182.05712890625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[52.55972696245733, 416.875], [148.45703125, 416.875], [148.45703125, 429.90234375], [52.55972696245733, 429.9111361079865]]}, {"title": "4.1 Results and Discussion", "heading_level": null, "page_id": 7, "polygon": [[52.55972696245733, 292.4638671875], [196.15234375, 292.4638671875], [196.15234375, 303.86279296875], [52.55972696245733, 303.86279296875]]}, {"title": "4.2 Ablations", "heading_level": null, "page_id": 7, "polygon": [[52.55972696245733, 486.24560546875006], [128.90625, 486.24560546875006], [128.90625, 497.31884765625006], [52.55972696245733, 497.31884765625006]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[38.96728515625, 185.96533203125], [125.25390624999999, 185.96533203125], [125.25390624999999, 200.29541015625], [38.96728515625, 200.29541015625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[38.29351535836177, 374.5361328125], [104.0380859375, 374.5361328125], [104.0380859375, 388.21484375], [38.29351535836177, 388.21484375]]}, {"title": "RET-CLIP: A Retinal Image Foundation\nModel Pre-trained with Clinical\nDiagnostic Reports", "heading_level": null, "page_id": 11, "polygon": [[82.71484375, 51.41729736328125], [371.03515625, 51.41729736328125], [371.03515625, 101.61328125], [82.71484375, 101.61328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 11, "polygon": [[52.55972696245733, 484.29150390625006], [148.66894197952217, 484.29150390625006], [148.66894197952217, 497.31884765625006], [52.55972696245733, 497.31884765625006]]}, {"title": "2 Method", "heading_level": null, "page_id": 13, "polygon": [[52.55972696245733, 144.3592529296875], [120.95703125, 144.3592529296875], [120.95703125, 158.2008056640625], [52.55972696245733, 158.2008056640625]]}, {"title": "2.1 Data Collection and Preprocessing", "heading_level": null, "page_id": 13, "polygon": [[52.55972696245733, 169.518310546875], [255.66406249999997, 169.518310546875], [255.66406249999997, 181.568603515625], [52.55972696245733, 181.568603515625]]}, {"title": "2.2 Model Architecture", "heading_level": null, "page_id": 13, "polygon": [[52.55972696245733, 405.150390625], [179.716796875, 405.150390625], [179.716796875, 417.5263671875], [52.55972696245733, 417.5263671875]]}, {"title": "2.3 Training Objective", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 54.9591064453125], [174.19795221843003, 54.9591064453125], [174.19795221843003, 65.8695068359375], [52.55972696245733, 65.8695068359375]]}, {"title": "2.4 Implementation", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 391.4716796875], [159.93174061433447, 391.4716796875], [159.93174061433447, 401.8935546875], [52.55972696245733, 401.8935546875]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 513.9287109375], [148.779296875, 513.9287109375], [148.779296875, 525.6533203125], [52.55972696245733, 525.6533203125]]}, {"title": "3.1 Tasks and Datasets", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 539.4521934758155], [177.67578125, 539.4521934758155], [177.67578125, 550.07958984375], [52.55972696245733, 550.07958984375]]}, {"title": "3.2 Comparision Methods and Evaluation Metrics", "heading_level": null, "page_id": 16, "polygon": [[38.29351535836177, 563.10693359375], [298.839590443686, 563.10693359375], [298.839590443686, 574.83154296875], [38.29351535836177, 574.83154296875]]}, {"title": "3.3 Result", "heading_level": null, "page_id": 17, "polygon": [[52.55972696245733, 168.541259765625], [113.115234375, 168.541259765625], [113.115234375, 180.265869140625], [52.55972696245733, 180.265869140625]]}, {"title": "3.4 Ablation Study", "heading_level": null, "page_id": 18, "polygon": [[38.29351535836177, 464.09912109375006], [143.41296928327645, 464.09912109375006], [143.41296928327645, 477.12646484375006], [38.29351535836177, 477.12646484375006]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 19, "polygon": [[51.80887372013652, 427.62255859375], [139.65870307167233, 427.62255859375], [139.65870307167233, 441.95263671875], [51.80887372013652, 441.95263671875]]}, {"title": "References", "heading_level": null, "page_id": 20, "polygon": [[39.04436860068259, 96.890869140625], [103.984375, 96.890869140625], [103.984375, 109.918212890625], [39.04436860068259, 109.918212890625]]}, {"title": "S-SAM: SVD-Based Fine-Tuning\nof Segment Anything Model for Medical\nImage Segmentation", "heading_level": null, "page_id": 22, "polygon": [[67.57679180887372, 51.49871826171875], [358.7890625, 51.49871826171875], [358.7890625, 102.427490234375], [67.57679180887372, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 22, "polygon": [[38.779296875, 519.46533203125], [134.40273037542661, 519.46533203125], [134.40273037542661, 532.49267578125], [38.779296875, 532.49267578125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 23, "polygon": [[52.55972696245733, 505.13525390625006], [156.17747440273035, 505.13525390625006], [156.17747440273035, 516.85986328125], [52.55972696245733, 516.85986328125]]}, {"title": "3 Proposed Method", "heading_level": null, "page_id": 25, "polygon": [[52.55972696245733, 54.4705810546875], [180.20477815699658, 54.4705810546875], [180.20477815699658, 66.11376953125], [52.55972696245733, 66.11376953125]]}, {"title": "Comparison with LoRA:", "heading_level": null, "page_id": 26, "polygon": [[38.994140625, 336.7568359375], [159.93174061433447, 336.7568359375], [159.93174061433447, 347.1787109375], [38.994140625, 347.1787109375]]}, {"title": "4 Experimental Results", "heading_level": null, "page_id": 27, "polygon": [[51.80887372013652, 146.23193359375], [201.30859375, 146.23193359375], [201.30859375, 157.95654296875], [51.80887372013652, 157.95654296875]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 30, "polygon": [[38.29351535836177, 269.99169921875], [124.71679687499999, 269.99169921875], [124.71679687499999, 283.3447265625], [38.29351535836177, 283.3447265625]]}, {"title": "References", "heading_level": null, "page_id": 30, "polygon": [[38.29351535836177, 469.96142578125006], [102.91015625, 469.96142578125006], [102.91015625, 482.98876953125006], [38.29351535836177, 482.98876953125006]]}, {"title": "ShapeMamba-EM: Fine-Tuning\nFoundation Model with Local Shape\nDescriptors and Mamba Blocks for 3D EM\nImage Segmentation", "heading_level": null, "page_id": 33, "polygon": [[75.947265625, 50.48095703125], [379.84375, 50.48095703125], [379.84375, 120.50292968750001], [75.947265625, 120.50292968750001]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 33, "polygon": [[52.9052734375, 518.48828125], [149.74609375, 518.48828125], [149.74609375, 532.818359375], [52.9052734375, 532.818359375]]}, {"title": "2 Method", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 52.72003173828125], [106.9384765625, 52.72003173828125], [106.9384765625, 67.05010986328125], [38.29351535836177, 67.05010986328125]]}, {"title": "2.1 Overview", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 77.30914306640625], [112.900390625, 77.30914306640625], [112.900390625, 89.56298828125], [38.29351535836177, 89.56298828125]]}, {"title": "2.2 SAM-Med3D", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 245.89111328125003], [132.55859375, 245.89111328125003], [132.55859375, 258.26708984375], [38.29351535836177, 258.26708984375]]}, {"title": "2.3 Parameter-Efficient Fine-Tuning of 3D Image Encoder", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 415.89794921875], [340.09765625, 415.89794921875], [340.09765625, 428.27392578125], [38.29351535836177, 428.27392578125]]}, {"title": "2.4 3D Mamba Adapter", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 548.451171875], [166.93359375, 548.451171875], [166.93359375, 560.8271484375], [38.29351535836177, 560.8271484375]]}, {"title": "2.5 3D Local Shape Descriptor Encoder", "heading_level": null, "page_id": 37, "polygon": [[52.55972696245733, 464.75048828125006], [261.29692832764505, 464.75048828125006], [261.29692832764505, 476.47509765625006], [52.55972696245733, 476.47509765625006]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 38, "polygon": [[38.29351535836177, 547.1484375], [207.98634812286687, 547.1484375], [207.98634812286687, 559.5244140625], [38.29351535836177, 559.5244140625]]}, {"title": "3.1 Datasets and Experimental Settings", "heading_level": null, "page_id": 39, "polygon": [[52.55972696245733, 323.40380859375], [261.29692832764505, 323.40380859375], [261.29692832764505, 334.47705078125], [52.55972696245733, 334.47705078125]]}, {"title": "3.2 Quantitative and Qualitative Segmentation Results", "heading_level": null, "page_id": 40, "polygon": [[38.29351535836177, 338.38525390625], [323.61774744027304, 338.38525390625], [323.61774744027304, 350.76123046875], [38.29351535836177, 350.76123046875]]}, {"title": "4 Discusion and Conclusion", "heading_level": null, "page_id": 41, "polygon": [[52.55972696245733, 254.68457031250003], [225.5859375, 254.68457031250003], [225.5859375, 269.34033203125], [52.55972696245733, 269.34033203125]]}, {"title": "References", "heading_level": null, "page_id": 41, "polygon": [[52.55972696245733, 502.20410156250006], [117.13310580204778, 502.20410156250006], [117.13310580204778, 516.5341796875], [52.55972696245733, 516.5341796875]]}, {"title": "Symmetry Awareness Encoded Deep\nLearning Framework for Brain Imaging\nAnalysis", "heading_level": null, "page_id": 44, "polygon": [[74.765625, 52.0279541015625], [351.26953125, 52.0279541015625], [351.26953125, 102.427490234375], [74.765625, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 45, "polygon": [[52.55972696245733, 54.77052868391451], [148.349609375, 54.77052868391451], [148.349609375, 66.439453125], [52.55972696245733, 66.439453125]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 46, "polygon": [[38.29351535836177, 415.89794921875], [137.4061433447099, 415.89794921875], [137.4061433447099, 429.57666015625], [38.29351535836177, 429.57666015625]]}, {"title": "2.1 Preprocssing", "heading_level": null, "page_id": 46, "polygon": [[38.29351535836177, 519.46533203125], [130.64846416382252, 519.46533203125], [130.64846416382252, 531.18994140625], [38.29351535836177, 531.18994140625]]}, {"title": "2.2 Symmetry-Aware Cross-Attention (SACA) Module", "heading_level": null, "page_id": 47, "polygon": [[52.55972696245733, 395.3798828125], [337.3046875, 395.3798828125], [337.3046875, 405.8017578125], [52.55972696245733, 405.8017578125]]}, {"title": "2.3 Symmetry-Aware Pretraining Proxies", "heading_level": null, "page_id": 48, "polygon": [[38.29351535836177, 401.56787109375], [255.29010238907847, 401.56787109375], [255.29010238907847, 411.98974609375], [38.29351535836177, 411.98974609375]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 49, "polygon": [[52.55972696245733, 527.607421875], [222.25255972696243, 527.607421875], [222.25255972696243, 539.33203125], [52.55972696245733, 539.33203125]]}, {"title": "3.1 Datasets and Downstream Tasks", "heading_level": null, "page_id": 50, "polygon": [[38.29351535836177, 55.16265869140625], [229.76109215017064, 55.16265869140625], [229.76109215017064, 66.07305908203125], [38.29351535836177, 66.07305908203125]]}, {"title": "3.2 Implementation Details", "heading_level": null, "page_id": 50, "polygon": [[38.29351535836177, 307.933837890625], [184.12109375, 307.933837890625], [184.12109375, 318.681396484375], [38.29351535836177, 318.681396484375]]}, {"title": "3.3 Results", "heading_level": null, "page_id": 50, "polygon": [[38.29351535836177, 513.60302734375], [102.8668941979522, 513.60302734375], [102.8668941979522, 524.67626953125], [38.29351535836177, 524.67626953125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 52, "polygon": [[38.29351535836177, 311.67919921875], [125.89843749999999, 311.67919921875], [125.89843749999999, 326.3349609375], [38.29351535836177, 326.3349609375]]}, {"title": "References", "heading_level": null, "page_id": 52, "polygon": [[38.29351535836177, 558.2216796875], [103.5546875, 558.2216796875], [103.5546875, 572.5517578125], [38.29351535836177, 572.5517578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11331, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 53], ["TableCell", 45], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4702, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 64], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 697, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 48], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 45], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 676], ["TableCell", 135], ["Line", 41], ["TextInlineMath", 3], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Text", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 15691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 90], ["TableCell", 59], ["Text", 3], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8920, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 40], ["ListItem", 6], ["Reference", 6], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 52], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 29], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 38], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 40], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 65], ["TextInlineMath", 4], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 806, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 584], ["Line", 43], ["SectionHeader", 4], ["TextInlineMath", 4], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 44], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["TableCell", 139], ["Line", 37], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4775, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 276], ["Span", 199], ["Line", 33], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10374, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["TableCell", 216], ["Line", 42], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 8025, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 47], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 38], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 41], ["Text", 8], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 570, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 45], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["TableCell", 66], ["Line", 62], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1402, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 67], ["Text", 4], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 702, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 72], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 67], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["TableCell", 368], ["Line", 46], ["Text", 4], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 11713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["TableCell", 272], ["Line", 71], ["Text", 5], ["Table", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 2, "llm_tokens_used": 16515, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 54], ["Reference", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 50], ["ListItem", 19], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 42], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 40], ["Text", 8], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 582, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 43], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 675, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 50], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 39], ["SectionHeader", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 32], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["TableCell", 145], ["Line", 58], ["Equation", 3], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["TableCell", 285], ["Line", 39], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3278, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 71], ["Line", 37], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 51], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 44], ["ListItem", 14], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 41], ["Text", 8], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 37], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 41], ["Text", 4], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 49], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 704, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 64], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 714, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 42], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["TableCell", 276], ["Line", 35], ["Text", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15280, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["TableCell", 150], ["Line", 35], ["Text", 5], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["ListItem", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11378, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 51], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 39], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-8"}