# Entropy Rates of a Stochastic Process

The asymptotic equipartition property in Chapter 3 establishes that  $nH(X)$  bits suffice on the average to describe n independent and identically distributed random variables. But what if the random variables are dependent? In particular, what if the random variables form a stationary process? We will show, just as in the i.i.d. case, that the entropy  $H(X_1, X_2, \ldots, X_n)$  grows (asymptotically) linearly with n at a rate  $H(\mathcal{X})$ , which we will call the entropy rate of the process. The interpretation of  $H(\mathcal{X})$  as the best achievable data compression will await the analysis in Chapter 5.

## 4.1 MARKOV CHAINS

A stochastic process is an indexed sequence of random variables. In general, there can be an arbitrary dependence among the random variables. The process is characterized by the joint probability mass functions  $Pr\{(X_1, X_2, \ldots, X_n) = (x_1, x_2, \ldots, x_n)\} = p(x_1, x_2, \ldots, x_n)$  $(x_1, x_2, \ldots, x_n) \in \mathcal{X}^n$  for n

Definition: A stochastic process is said to be stationary if the joint distribution of any subset of the sequence of random variables is invariant with respect to shifts in the time index, i.e.,

$$
\Pr{X_1 = x_1, X_2 = x_2, \dots, X_n = x_n}
$$
  
= 
$$
\Pr{X_{1+l} = x_1, X_{2+l} = x_2, \dots, X_{n+l} = x_n}
$$
 (4.1)

for every shift *l* and for all  $x_1, x_2, ..., x_n \in \mathcal{X}$ .

A simple example of a stochastic process with dependence is one in which each random variable depends on the one preceding it and is conditionally independent of all the other preceding random variables. Such a process is said to be Markov.

**Definition:** A discrete stochastic process  $X_1, X_2, \ldots$  is said to be a Markov chain or a Markov process if, for  $n = 1, 2, \ldots$ ,

$$
\Pr(X_{n+1} = x_{n+1} | X_n = x_n, X_{n-1} = x_{n-1}, \dots, X_1 = x_1)
$$
  
= 
$$
\Pr(X_{n+1} = x_{n+1} | X_n = x_n)
$$
 (4.2)

for all  $x_1, x_2, \ldots, x_n, x_{n+1} \in \mathcal{X}$ .

In this case, the joint probability mass function of the random variables can be written as

$$
p(x_1, x_2, \dots, x_n) = p(x_1)p(x_2|x_1)p(x_3|x_2)\cdots p(x_n|x_{n-1}).
$$
 (4.3)

**Definition:** The Markov chain is said to be *time invariant* if the conditional probability  $p(x_{n+1} | x_n)$  does not depend on n, i.e., for  $n =$  $1,2,\ldots$ 

$$
\Pr\{X_{n+1} = b | X_n = a\} = \Pr\{X_2 = b | X_1 = a\}, \text{ for all } a, b \in \mathcal{X}. \quad (4.4)
$$

We will assume that the Markov chain is time invariant unless otherwise stated.

If  $\{X_i\}$  is a Markov chain, then  $X_n$  is called the state at time n. A time invariant Markov chain is characterized by its initial state and a probability transition matrix  $P = [P_{ij}], i, j \in \{1, 2, ..., m\}$ , where  $P_{ij} =$  $Pr{X_{n+1} = j | X_n = i}.$ 

If it is possible to go with positive probability from any state of the Markov chain to any other state in a finite number of steps, then the Markov chain is said to be irreducible.

If the probability mass function of the random variable at time  $n$  is  $p(x_n)$ , then the probability mass function at time  $n + 1$  is

$$
p(x_{n+1}) = \sum_{x_n} p(x_n) P_{x_n x_{n+1}}.
$$
 (4.5)

A distribution on the states such that the distribution at time  $n + 1$  is the same as the distribution at time  $n$  is called a *stationary distribution*. The stationary distribution is so called because if the initial state of a Markov chain is drawn according to a stationary distribution, then the Markov chain forms a stationary process.

If the finite state Markov chain is irreducible and aperiodic, then the stationary distribution is unique, and from any starting distribution, the distribution of  $X_n$  tends to the stationary distribution as  $n \to \infty$ .

Example 4.1.1: Consider a two-state Markov chain with a probability transition matrix

$$
P = \begin{bmatrix} 1 - \alpha & \alpha \\ \beta & 1 - \beta \end{bmatrix}
$$
 (4.6)

as shown in Figure 4.1.

Let the stationary distribution be represented by a vector  $\mu$  whose components are the stationary probabilities of state 1 and state 2, respectively. Then the stationary probability can be found by solving the equation  $\mu P = \mu$  or, more simply, by balancing probabilities. For the stationary distribution, the net probability flow across any cut-set in the state transition graph is 0. Applying this to Figure 4.1, we obtain

$$
\mu_1 \alpha = \mu_2 \beta \ . \tag{4.7}
$$

Since  $\mu_1 + \mu_2 = 1$ , the stationary distribution is

$$
\mu_1 = \frac{\beta}{\alpha + \beta} \,, \qquad \mu_2 = \frac{\alpha}{\alpha + \beta} \,. \tag{4.8}
$$

If the Markov chain has an initial state drawn according to the stationary distribution, the resulting process will be stationary. The entropy of the state  $X_n$  at time *n* is

$$
H(X_n) = H\left(\frac{\beta}{\alpha + \beta}, \frac{\alpha}{\alpha + \beta}\right). \tag{4.9}
$$

However, this is not the rate at which entropy grows for  $H(X_1, X_2, X_3)$  $\ldots$ ,  $X_n$ ). The dependence among the  $X_i$ 's will take a steady toll.

Image /page/2/Figure/12 description: This is a state transition diagram with two states labeled 1 and 2. From state 1, there is a self-loop with probability 1-alpha, and a transition to state 2 with probability alpha. There is also an outgoing edge from state 1 labeled with mu\_1 = beta / (alpha + beta). From state 2, there is a self-loop with probability 1-beta, and a transition to state 1 with probability beta. There is also an outgoing edge from state 2 labeled with mu\_2 = alpha / (alpha + beta).

Figure 4.1. Two-state Markov chain.

## 4.2 ENTROPY RATE

If we have a sequence of  $n$  random variables, a natural question to ask is "how does the entropy of the sequence grow with n." We define the entropy rate as this rate of growth as follows.

**Definition:** The entropy rate of a stochastic process  $\{X_i\}$  is defined by

$$
H(\mathscr{X}) = \lim_{n \to \infty} \frac{1}{n} H(X_1, X_2, \dots, X_n)
$$
 (4.10)

when the limit exists.

We now consider some simple examples of stochastic processes and their corresponding entropy rates.

- 1. Typewriter. Consider the case of a typewriter that has m equall likely output letters. The typewriter can produce  $m<sup>n</sup>$  sequences of length n, all of them equally likely. Hence  $H(X_1, X_2, \ldots, X_n)$  = log m<sup>n</sup> and the entropy rate is  $H(\mathscr{X}) = \log m$  bits per symbol.
- 2.  $X_1, X_2, \ldots$  are i.i.d. random variables. Then

$$
H(\mathscr{X}) = \lim \frac{H(X_1, X_2, \dots, X_n)}{n} = \lim \frac{nH(X_1)}{n} = H(X_1), \quad (4.11)
$$

which is what one would expect for the entropy rate per symbol.

3. Sequence of independent, but not identically distributed random variables. In this case,

$$
H(X_1, X_2, \ldots, X_n) = \sum_{i=1}^{n} H(X_i)
$$
 (4.12)

but the  $H(X_i)$ 's are all not equal. We can choose a sequence of distributions on  $X_1, X_2, \ldots$  such that the limit of  $\frac{1}{n} \Sigma H(X_i)$  does not exist. An example of such a sequence is a random binary sequence where  $p_i = P(X_i = 1)$  is not constant, but a function of i, chosen carefully so that the limit in (4.10) does not exist. For example, let

$$
p_i = \begin{cases} 0.5 & \text{if } 2k < \log \log i \le 2k + 1, \\ 0 & \text{if } 2k + 1 < \log \log i \le 2k + 2 \end{cases}
$$
(4.13)

for  $k = 0, 1, 2, \ldots$ . Then there are arbitrarily long stretches where  $H(X_i) = 1$ , followed by exponentially longer segments where  $H(X_i) = 0$ . Hence the running average of the  $H(X_i)$  will oscillate between 0 and 1 and will not have a limit. Thus  $H(\mathcal{X})$  is not defined for this process.

We can also define a related quantity for entropy rate:

$$
H'(\mathscr{X}) = \lim_{n \to \infty} H(X_n | X_{n-1}, X_{n-2}, \dots, X_1), \tag{4.14}
$$

when the limit exists.

The two quantities  $H(x)$  and  $H'(x)$  correspond to two different notions of entropy rate. The first is the per symbol entropy of the  $n$ random variables, and the second is the conditional entropy of the last random variable given the past. We will now prove the important result that for stationary processes both the limits exist and are equal.

**Theorem 4.2.1:** For a stationary stochastic process, the limits in  $(4.10)$ and (4.14) exist and are equal, i.e.,

$$
H(\mathscr{X}) = H'(\mathscr{X})\,. \tag{4.15}
$$

We will first prove that  $\lim H(X_n|X_{n-1},\ldots,X_1)$  exists.

**Theorem 4.2.2:** For a stationary stochastic process,  $H(X_n | X_{n-1}, \ldots, X_1)$ is decreasing in n and has a limit  $H'(\mathscr{X})$ .

Proof:

$$
H(X_{n+1}|X_1, X_2, \dots, X_n) \le H(X_{n+1}|X_n, \dots, X_2)
$$
\n(4.16)

$$
=H(X_n|X_{n-1},\ldots,X_1)\,,\qquad (4.17)
$$

where the inequality follows from the fact that conditioning reduces entropy and the equality follows from the stationarity of the process. Since  $H(X_n | X_{n-1}, \ldots, X_1)$  is a decreasing sequence of non-negative numbers, it has a limit,  $H'(\mathscr{X})$ .  $\square$ 

We now use the following simple result from analysis.

**Theorem 4.2.3** (Cesáro mean): If  $a_n \to a$  and  $b_n = \frac{1}{n} \sum_{i=1}^n a_i$ , then  $b_n \rightarrow a$ .

**Proof** (Informal outline): Since most of the terms in the sequence  ${a_k}$  are eventually close to a, then  $b_n$ , which is the average of the first n terms, is also eventually close to  $a$ .

**Formal proof:** Since  $a_n \to a$ , there exists a number  $N(\epsilon)$  such that  $|a_n - a| \leq \epsilon$  for all  $n \geq N(\epsilon)$ . Hence

$$
|b_n - a| = \left| \frac{1}{n} \sum_{i=1}^{n} (a_i - a) \right| \tag{4.18}
$$

$$
\leq \frac{1}{n} \sum_{i=1}^{n} |(a_i - a)| \tag{4.19}
$$

$$
\leq \frac{1}{n} \sum_{i=1}^{N(\epsilon)} |a_i - a| + \frac{n - N(\epsilon)}{n} \epsilon \qquad (4.20)
$$

$$
\leq \frac{1}{n} \sum_{i=1}^{N(\epsilon)} |a_i - a| + \epsilon , \qquad (4.21)
$$

for all  $n \ge N(\epsilon)$ . Since the first term goes to 0 as  $n \to \infty$ , we can make  $|b_n - a| \leq 2\epsilon$  by taking *n* large enough. Hence  $b_n \to a$  as  $n \to \infty$ .

Proof of Theorem 4.2.1: By the chain rule,

$$
\frac{H(X_1, X_2, \dots, X_n)}{n} = \frac{1}{n} \sum_{i=1}^n H(X_i | X_{i-1}, \dots, X_1), \qquad (4.22)
$$

i.e., the entropy rate is the time average of the conditional entropies. But we know that the conditional entropies tend to a limit  $H'(\mathcal{X})$ . Hence, by Theorem 4.2.3, their running average has a limit, which is equal to the limit  $H'(\mathscr{X})$  of the terms.

Thus, by Theorem 4.2.2.,

$$
H(\mathscr{X}) = \lim_{n} \frac{H(X_1, X_2, \dots, X_n)}{n} = \lim_{n} H(X_n | X_{n-1}, \dots, X_1) = H'(\mathscr{X}) \ . \quad \Box
$$
\n(4.23)

The significance of the entropy rate of a stochastic process arises from the AEP for a stationary ergodic process. We will prove the general AEP in Section 15.7, where we will show that for any stationary ergodic process,

$$
-\frac{1}{n}\log p(X_1, X_2, \dots, X_n) \to H(\mathscr{X}), \qquad (4.24)
$$

with probability 1. Using this, the theorems of Chapter 3 can be easily extended to a general stationary ergodic process. We can define a typical set in the same way as we did for the i.i.d. case in Chapter 3. By the same arguments, we can show that the typical set has a probability close to 1, and that there are about  $2^{nH(x)}$  typical sequences of length n, each with probability about  $2^{-nH(\mathscr{X})}$ . We can therefore represent the typical sequences of length n using approximately  $nH(\mathscr{X})$  bits. This shows the significance of the entropy rate as the average description length for a stationary ergodic process.

The entropy rate is well defined for all stationary processes. The entropy rate is particularly easy to calculate for Markov chains.

Markov Chains: For a stationary Markov chain, the entropy rate is given by

$$
H(\mathscr{X}) = H'(\mathscr{X}) = \lim H(X_n | X_{n-1}, \dots, X_1) = \lim H(X_n | X_{n-1}) = H(X_2 | X_1),
$$
\n(4.25)

where the conditional entropy is calculated using the given stationary distribution. We express this result explicitly in the following theorem:

**Theorem 4.2.4:** Let  $\{X_i\}$  be a stationary Markov chain with stationary distribution  $\mu$  and transition matrix P. Then the entropy rate is

$$
H(\mathscr{X}) = -\sum_{ij} \mu_i P_{ij} \log P_{ij} \tag{4.26}
$$

**Proof:**  $H(\mathscr{X}) = H(X_2|X_1) = \sum_i \mu_i(\sum_i - P_{ij} \log P_{ij}).$ 

**Example 4.2.1** (Two-state Markov chain): The entropy rate of the two-state Markov chain in Figure 4.1 is

$$
H(\mathscr{X}) = H(X_2|X_1) = \frac{\beta}{\alpha + \beta} H(\alpha) + \frac{\alpha}{\alpha + \beta} H(\beta).
$$
 (4.27)

Remark: If the Markov chain is irreducible and aperiodic, then it has a unique stationary distribution on the states, and any initial distribution tends to the stationary distribution as  $n \rightarrow \infty$ . In this case, even though the initial distribution is not the stationary distribution, the entropy rate, which is defined in terms of long term behavior, is  $H(\mathscr{X})$  as defined in (4.25) and (4.26).

## 4.3 EXAMPLE: ENTROPY RATE OF A RANDOM WALK ON A WEIGHTED GRAPH

As an example of a stochastic process, let us consider a random walk on a connected graph (Figure 4.2). Consider a graph with  $m$  nodes labeled  $\{1,2,\ldots,m\}$ , with weight  $W_{ii} \ge 0$  on the edge joining node i to node j.

Image /page/6/Figure/11 description: A graph with 5 nodes labeled 1 through 5. Node 1 is connected to node 2 and node 5. Node 2 is connected to node 1, node 3, and node 5. Node 3 is connected to node 2, node 4, and node 5. Node 4 is connected to node 3 and node 5. Node 5 is connected to node 1, node 2, node 3, and node 4.

Figure 4.2. Random walk on a graph.

(The graph is assumed to be undirected, so that  $W_{ij} = W_{ji}$ . We set  $W_{ij} = 0$ if the pair of nodes  $i$  and  $j$  are not connected.)

A particle randomly walks from node to node in this graph. The random walk  $\{X_n\}, X_n \in \{1, 2, ..., m\}$  is a sequence of vertices of the graph. Given  $X<sub>n</sub> = i$ , the next vertex j is chosen from among the nodes connected to node  $i$  with a probability proportional to the weight of the edge connecting *i* to *j*. Thus  $P_{ij} = W_{ij}/\Sigma_k W_{ik}$ .

In this case, the stationary distribution has a surprisingly simple form which we will guess and verify. The stationary distribution for this Markov chain assigns probability to node i proportional to the total weight of the edges emanating from node i. Let

$$
W_i = \sum_j W_{ij} \tag{4.28}
$$

be the total weight of edges emanating from node i and let

$$
W = \sum_{i, j: j > i} W_{ij} \tag{4.29}
$$

be the sum of the weights of all the edges. Then  $\Sigma_i W_i = 2W$ . We now guess that the stationary distribution is

$$
\mu_i = \frac{W_i}{2W} \,. \tag{4.30}
$$

We verify that this is the stationary distribution by checking that  $\mu P = \mu$ . Here

$$
\sum_{i} \mu_i P_{ij} = \sum_{i} \frac{W_i}{2W} \frac{W_{ij}}{W_i}
$$
 (4.31)

$$
=\sum_{i}\frac{1}{2W}W_{ij}\tag{4.32}
$$

$$
=\frac{W_j}{2W}\tag{4.33}
$$

$$
=\mu_j\,.
$$
\n(4.34)

Thus the stationary probability of state  $i$  is proportional to the weight of edges emanating from node i. This stationary distribution has an interesting property of locality: it depends only on the total weight and the weight of edges connected to the node and hence does not change if the weights in some other part of the graph are changed while keeping the total weight constant.

We can now calculate the entropy rate as

$$
H(\mathscr{X}) = H(X_2 | X_1) \tag{4.35}
$$

$$
= -\sum_{i} \mu_i \sum_{j} P_{ij} \log P_{ij} \tag{4.36}
$$

$$
= -\sum_{i} \frac{\mathbf{W}_{i}}{2W} \sum_{j} \frac{\mathbf{W}_{ij}}{\mathbf{W}_{i}} \log \frac{\mathbf{W}_{ij}}{\mathbf{W}_{i}}
$$
(4.37)

$$
= -\sum_{i} \sum_{j} \frac{W_{ij}}{2W} \log \frac{W_{ij}}{W_i}
$$
 (4.38)

$$
= -\sum_{i}\sum_{j}\frac{W_{ij}}{2W}\log\frac{W_{ij}}{2W} + \sum_{i}\sum_{j}\frac{W_{ij}}{2W}\log\frac{W_{i}}{2W}
$$
 (4.39)

$$
= H\left(\ldots, \frac{W_{ij}}{2W}, \ldots\right) - H\left(\ldots, \frac{W_i}{2W}, \ldots\right). \tag{4.40}
$$

If all the edges have equal weight, the stationary distribution puts weight  $E_i/2E$  on node i, where  $E_i$  is the number of edges emanating from node i and  $E$  is the total number of edges in the graph. In this case, the entropy rate of the random walk is

$$
H(\mathscr{X}) = \log(2E) - H\left(\frac{E_1}{2E}, \frac{E_2}{2E}, \dots, \frac{E_m}{2E}\right)
$$
 (4.41)

This answer for the entropy  $X_i$  is so simple that it is almost misleading. Apparently, the entro $_{\mu\nu}$  rate, which is the average transition entropy, depends only on the entropy of the stationary distribution and the total number of edges.

**Example 4.3.1** (Random walk on a chessboard): Let a king move at random on an  $8 \times 8$  chessboard. The king has 8 moves in the interior, 5 moves at the edges and 3 moves at the corners. Using this and the preceding results, the stationary probabilities are respectively  $\frac{8}{420}$ ,  $\frac{5}{420}$ and  $\frac{3}{420}$ , and the entropy rate is 0.92 log 8. The factor of 0.92 is due to edge effects; we would have an entropy rate of log8 on an infinite chessboard.

Similarly, we can find the entropy rate of rooks (log 14 bits, since the rook always has 14 possible moves), bishops and queens. The queen combines the moves of a rook and a bishop. Does the queen have more or less freedom than the pair?

Remark: It is easy to see that a stationary random walk on a graph is time-reversible, that is, the probability of any sequence of states is the same forward or backward:

$$
Pr(X_1 = x_1, X_2 = x_2, \dots, X_n = x_n) = Pr(X_n = x_1, X_{n-1} = x_2, \dots, X_1 = x_n).
$$
\n(4.42)

Rather surprisingly, the converse is also true, that is, any time-reversible Markov chain can be represented as a random walk on an undirected weighted graph.

## 4.4 HIDDEN MARKOV MODELS

Here is an example that can be very difficult if done the wrong way. It illustrates the power of the techniques developed so far. Let  $X_1$ ,  $X_2, \ldots, X_n, \ldots$  be a stationary Markov chain, and let  $Y_i = \phi(X_i)$  be a process, each term of which is a function of the corresponding state in the Markov chain. Such functions of Markov chains occur often in practice. In many situations, one has only partial information about the state of the system. It would simplify matters greatly if  $Y_1, Y_2, \ldots, Y_n$ also formed a Markov chain, but in many cases this is not true. However, since the Markov chain is stationary, so is  $Y_1, Y_2, \ldots, Y_n$ , and the entropy rate is well defined. However, if we wish to compute  $H(\mathcal{Y})$ , we might compute  $H(Y_n | Y_{n-1}, \ldots, Y_1)$  for each n and find the limit. Since the convergence can be arbitrarily slow, we will never know how close we are to the limit; we will not know when to stop. (We can't look at the change between the values at n and  $n + 1$ , since this difference may be small even when we are far away from the limit-consider, for example,  $\Sigma \frac{1}{n}$ .)

It would be useful computationally to have upper and lower bounds converging to the limit from above and below. We can halt the computation when the difference between the upper bound and the lower bound is small, and we will then have a good estimate of the limit.

We already know that  $H(Y_n|Y_{n-1}, \ldots, Y_1)$  converges monotonically to  $H(\mathcal{Y})$  from above. For a lower bound, we will use  $H(Y_n | Y_{n-1}, \ldots, Y_2, X_1)$ . This is a neat trick based on the idea that  $X_1$  contains as much information about  $Y_n$  as  $Y_1, Y_0, Y_{-1}, \ldots$ .

## Lemma 4.4.1:

$$
H(Y_n | Y_{n-1}, \dots, Y_2, X_1) \le H(\mathcal{Y})
$$
\n(4.43)

**Proof:** We have, for  $k = 1, 2, \ldots$ ,

$$
H(Y_n|Y_{n-1},...,Y_2,X_1)
$$

<sup>(a)</sup>
$$
=H(Y_n|Y_{n-1},...,Y_2,Y_1,X_1)
$$
 (4.44)

$$
\stackrel{(b)}{=} H(Y_n|Y_{n-1},\ldots,Y_1,X_1,X_0,X_{-1},\ldots,X_{-k})
$$
\n(4.45)

$$
\stackrel{\scriptscriptstyle (c)}{=} H(Y_n|Y_{n-1},\ldots,Y_1,X_1,X_0,X_{-1},\ldots,X_{-k},Y_0,\ldots,Y_{-k}) \tag{4.46}
$$

$$
\leq H(Y_n|Y_{n-1},\ldots,Y_1,Y_0,\ldots,Y_{-k})
$$
\n(4.47)

$$
\stackrel{(e)}{=} H(Y_{n+k+1}|Y_{n+k},\ldots,Y_1), \qquad (4.48)
$$

where (a) follows from that fact that  $Y_1$  is a function of  $X_1$ , and (b) follows from the Markovity of X, (c) from the fact that  $Y_i$  is a function of  $X_i$ , (d) from the fact that conditioning reduces entropy, and (e) by stationarity. Since the inequality is true for all  $k$ , it is true in the limit. Thus

$$
H(Y_n|Y_{n-1},\ldots,Y_1,X_1) \leq \lim_{k} H(Y_{n+k+1}|Y_{n+k},\ldots,Y_1) \qquad (4.49)
$$

$$
=H(\mathcal{Y})\qquad \qquad (4.50)
$$

The next lemma shows that the interval between the upper and the lower bounds decreases in length.

## Lemma 4.4.2:

$$
H(Y_n|Y_{n-1},\ldots,Y_1) - H(Y_n|Y_{n-1},\ldots,Y_1,X_1) \to 0. \tag{4.51}
$$

Proof: The interval length can be rewritten as

$$
H(Y_n|Y_{n-1},\ldots,Y_1) - H(Y_n|Y_{n-1},\ldots,Y_1,X_1) = I(X_1;Y_n|Y_{n-1},\ldots,Y_1).
$$
\n(4.52)

By the properties of mutual information,

$$
I(X_1; Y_1, Y_2, \dots, Y_n) \le H(X_1), \tag{4.53}
$$

and hence

$$
\lim_{n \to \infty} I(X_1; Y_1, Y_2, \dots, Y_n) \le H(X_1).
$$
 (4.54)

By the chain rule,

$$
\lim_{n \to \infty} I(X_1; Y_1, Y_2, \dots, Y_n) = \lim_{n \to \infty} \sum_{i=1}^n I(X_1; Y_i | Y_{i-1}, \dots, Y_1) \quad (4.55)
$$

$$
= \sum_{i=1}^{8} I(X_1; Y_i | Y_{i-1}, \dots, Y_1).
$$
 (4.56)

Since this infinite sum is finite and the terms are non-negative, the terms must tend to 0, i.e.,

$$
\lim I(X_1; Y_n | Y_{n-1}, \dots, Y_1) = 0, \tag{4.57}
$$

which proves the lemma.  $\square$ 

Combining the previous two lemmas, we have the following theorem:

**Theorem 4.4.1:** If  $X_1, X_2, \ldots, X_n$  form a stationary Markov chain, and  $Y_i = \phi(X_i)$ , then

$$
H(Y_n|Y_{n-1},\ldots,Y_1,X_1) \le H(\mathcal{Y}) \le H(Y_n|Y_{n-1},\ldots,Y_1) \tag{4.58}
$$

and

$$
\lim H(Y_n | Y_{n-1}, \dots, Y_1, X_1) = H(\mathcal{Y}) = \lim H(Y_n | Y_{n-1}, \dots, Y_1). \quad (4.59)
$$

## SUMMARY OF CHAPTER 4

Entropy rate: Two definitions of entropy rate for a stochastic process are

$$
H(\mathscr{X}) = \lim_{n \to \infty} \frac{1}{n} H(X_1, X_2, \dots, X_n),
$$
 (4.60)

$$
H'(\mathscr{X}) = \lim_{n \to \infty} H(X_n | X_{n-1}, X_{n-2}, \dots, X_1).
$$
 (4.61)

For a stationary stochastic process,

$$
H(\mathscr{X}) = H'(\mathscr{X})\,. \tag{4.62}
$$

Entropy rate of a stationary Markov chain:

$$
H(\mathscr{X}) = -\sum_{ij} \mu_i P_{ij} \log P_{ij} . \qquad (4.63)
$$

**Functions of a Markov chain:** If  $X_1, X_2, \ldots, X_n$  form a Markov chain and  $Y_i = \phi(X_i)$ , then

$$
H(Y_n|Y_{n-1},\ldots,Y_1,X_1) \le H(\mathcal{Y}) \le H(Y_n|Y_{n-1},\ldots,Y_1) \tag{4.64}
$$

and

$$
\lim_{n \to \infty} H(Y_n | Y_{n-1}, \dots, Y_1, X_1) = H(\mathcal{Y}) = \lim_{n \to \infty} H(Y_n | Y_{n-1}, \dots, Y_1).
$$
 (4.65)

## PROBLEMS FOR CHAPTER 4

1. Doubly stochastic matrices. An  $n \times n$  matrix  $P = [P_{ij}]$  is said to be doubly stochastic if  $P_{ij} \ge 0$  and  $\sum_j P_{ij} = 1$  for all i and  $\sum_i P_{ij} = 1$  for all j. An  $n \times n$  matrix P is said to be a *permutation* matrix if it is doubly stochastic and there is precisely one  $P_{ij} = 1$  in each row and each column.

It can be shown that every doubly stochastic matrix can be written as the convex combination of permutation matrices.

- (a) Let  $\mathbf{a}^t = (a_1, a_2, \dots, a_n), a_i \ge 0, \sum a_i = 1$ , be a probability vector. Let  $\mathbf{b} = \mathbf{a}P$ , where P is doubly stochastic. Show that **b** is a probability vector and that  $H(b_1, b_2, \ldots, b_n) \ge H(a_1, a_2, \ldots, a_n)$ . Thus stochastic mixing increases entropy.
- (b) Show that a stationary distribution  $\mu$  for a doubly stochastic matrix  $P$  is the uniform distribution.
- (c) Conversely, prove that if the uniform distribution is a stationary distribution for a Markov transition matrix  $P$ , then  $P$  is doubly stochastic.
- 2. Time's arrow. Let  ${X_i}_{i=-\infty}^{\infty}$  be a stationary stochastic process. Prove that

$$
H(X_0|X_{-1}, X_{-2}, \ldots, X_{-n}) = H(X_0|X_1, X_2, \ldots, X_n).
$$

In other words, the present has a conditional entropy given the past equal to the conditional entropy given the future.

This is true even though it is quite easy to concoct stationary random processes for which the flow into the future looks quite different from the flow into the past. That is to say, one can determine the direction of time by looking at a sample function of the process. Nonetheless, given the present state, the conditional uncertainty of the next symbol in the future is equal to the conditional uncertainty of the previous symbol in the past.

3. Entropy of a random tree. Consider the following method of generating a random tree with  $n$  nodes. First expand the root node:

Image /page/12/Figure/12 description: A simple black and white drawing of an inverted V shape, resembling a roof or an upward-pointing arrow.

Then expand one of the two terminal nodes at random:

Image /page/12/Figure/14 description: Two identical abstract figures are shown side-by-side against a white background. Each figure consists of three black lines. The leftmost figure has a central vertical line with two diagonal lines branching upwards from its top, forming an inverted V shape. A third diagonal line branches downwards from the right side of the central vertical line. The rightmost figure is a mirror image of the leftmost figure.

At time k, choose one of the  $k-1$  terminal nodes according to a uniform distribution and expand it. Continue until  $n$  terminal nodes have been generated. Thus a sequence leading to a five node tree might look like this:

Image /page/13/Figure/1 description: The image displays a sequence of four branching structures, resembling simplified trees or phylogenetic diagrams. Each structure starts with a single upward-sloping line, which then splits into two downward-sloping lines. The first structure has only this initial branching. The second structure has the initial branch, and then one of the downward branches further splits into two. The third structure shows the initial branch, with both downward branches splitting into two, creating a total of four terminal branches. The fourth structure is the most complex, with the initial branch splitting, and then each of those branches splitting again, and one of those branches splitting a third time, resulting in a total of seven terminal branches. The structures are arranged diagonally from the top left to the bottom right of the image, with each subsequent structure being larger and more complex than the preceding one.

Surprisingly, the following method of generating random trees yields the same probability distribution on trees with  $n$  terminal nodes. First choose an integer  $N_1$  uniformly distributed on  $\{1, 2, \ldots, n-1\}$ . We then have the picture.

Image /page/13/Figure/3 description: A diagram shows two branches originating from a single point at the top, splitting into two distinct labels at the bottom. The left label reads "N1", and the right label reads "n - N1".

Then choose an integer  $N_2$  uniformly distributed over  $\{1, 2, \ldots, \}$  $N_1 - 1$ , and independently choose another integer  $N_3$  uniformly over  ${1, 2, \ldots, (n - N_1) - 1}.$  The picture is now:

Image /page/13/Figure/5 description: A binary tree diagram is shown. The root node is not labeled. The left child of the root node has two children labeled N2 and N1 - N2. The right child of the root node has two children labeled N3 and n - N1 - N3.

Continue the process until no further subdivision can be made. (The equivalence of these two tree generation schemes follows, for example, from Polya's urn model.)

Now let  $T_n$  denote a random *n*-node tree generated as described. The probability distribution on such trees seems difficult to describe, but we can find the entropy of this distribution in recursive form.

First some examples. For  $n = 2$ , we have only one tree. Thus  $H(T_n) = 0$ . For  $n = 3$ , we have two equally probable trees:

Image /page/13/Figure/9 description: Two identical abstract line drawings are shown side-by-side against a white background. Each drawing consists of several black lines forming a branching pattern. The left drawing has a central vertical line from which two lines branch upwards and outwards, forming an inverted V shape. From the junction of these two lines, a single line extends downwards and to the right, and then splits into two lines that extend downwards and outwards. The right drawing is a mirror image of the left drawing, with the branching pattern oriented in the opposite direction.

Thus  $H(T_3) = \log 2$ . For  $n = 4$ , we have five possible trees, with probabilities l/3, l/6, l/6, l/6, l/6.

Now for the recurrence relation. Let  $N_1(T_n)$  denote the number of terminal nodes of  $T_n$  in the right half of the tree. Justify each of the steps in the following:

$$
H(T_n) \stackrel{(a)}{=} H(N_1, T_n) \tag{4.66}
$$

$$
H(N_1) + H(T_n|N_1) \tag{4.67}
$$

$$
\stackrel{(c)}{=} \log(n-1) + H(T_n|N_1) \tag{4.68}
$$

$$
\stackrel{(d)}{=} \log(n-1) + \frac{1}{n-1} \sum_{k=1}^{n-1} [H(T_k) + H(T_{n-k})] \tag{4.69}
$$

$$
\stackrel{(e)}{=} \log(n-1) + \frac{2}{n-1} \sum_{k=1}^{n-1} H(T_k).
$$
 (4.70)

$$
= \log(n-1) + \frac{2}{n-1} \sum_{k=1}^{n-1} H_k \,. \tag{4.71}
$$

(f) Use this to show that

$$
(n-1)H_n = nH_{n-1} + (n-1)\log(n-1) - (n-2)\log(n-2),
$$
\n(4.72)

or

$$
\frac{H_n}{n} = \frac{H_{n-1}}{n-1} + c_n \,,\tag{4.73}
$$

for appropriately defined  $c_n$ . Since  $\sum c_n = c < \infty$ , you have proved that  $\frac{1}{n}H(T_n)$  converges to a constant. Thus the expected number of bits necessary to describe the random tree  $T_n$  grows linearly with n.

4. Monotonicity of entropy per element. For a stationary stochastic process  $X_1, X_2, \ldots, X_n$ , show that

(a) 
$$
\frac{H(X_1, X_2, \dots, X_n)}{n} \le \frac{H(X_1, X_2, \dots, X_{n-1})}{n-1}.
$$
 (4.74)

(b) 
$$
\frac{H(X_1, X_2, \dots, X_n)}{n} \ge H(X_n | X_{n-1}, \dots, X_1).
$$
 (4.75)

- 5. Entropy rates of Markov chains.
  - (a) Find the entropy rate of the two-state Markov chain with transition matrix

$$
P = \begin{bmatrix} 1 - p_{01} & p_{01} \\ p_{10} & 1 - p_{10} \end{bmatrix}.
$$

- (b) What values of  $p_{01},p_{10}$  maximize the rate of part (a)?
- (c) Find the entropy rate of the two-state Markov chain with transition matrix

$$
P=\left[\begin{array}{cc}1-p&p\\1&0\end{array}\right].
$$

- (d) Find the maximum value of the entropy rate of the Markov chain of part (c). We expect that the maximizing value of  $p$  should be less than l/2, since the 0 state permits more information to be generated than the 1 state.
- (e) Let  $N(t)$  be the number of allowable state sequences of length t for the Markov chain of part (c). Find  $N(t)$  and calculate

$$
H_0 = \lim_{t \to \infty} \frac{1}{t} \log N(t) \, .
$$

Hint: Find a linear recurrence that expresses  $N(t)$  in terms of  $N(t - 1)$  and  $N(t - 2)$ . Why is  $H<sub>0</sub>$  an upper bound on the entropy rate of the Markov chain? Compare  $H_0$  with the maximum entropy found in part (d).

- 6. Maximum entropy process. A discrete memoryless source has alphabet { 1,2} where the symbol 1 has duration 1 and the symbol 2 has duration 2. The probabilities of 1 and 2 are  $p_1$  and  $p_2$ , respectively. Find the value of  $p_i$ , that maximizes the source entropy per unit time  $H(X)/El<sub>X</sub>$ . What is the maximum value H?
- 7. Initial conditions. Show, for a Markov chain, that

$$
H(X_0|X_n) \ge H(X_0|X_{n-1})
$$

Thus initial conditions  $X_0$  become more difficult to recover as the future  $X<sub>n</sub>$ , unfolds.

- 8. Pairwise independence. Let  $X_1, X_2, \ldots, X_{n-1}$  be i.i.d. random variables taking values in  $\{0, 1\}$ , with  $Pr(X_i = 1) = \frac{1}{2}$ . Let  $X_n = 1$  if  $\sum_{i=1}^{n-1} X_i$  is odd and  $X_n = 0$  otherwise. Let  $n \geq 3$ .
  - (a) Show that  $X_i$  and  $X_j$  are independent, for  $i \neq j$ ,  $i, j \in \{1, 2, ..., n\}$ .
  - (b) Find  $H(X_i, X_j)$ , for  $i \neq j$ .
  - (c) Find  $H(X_1, X_2, \ldots, X_n)$ . Is this equal to  $n(X_1)$ ?
- **9.** Stationary processes. Let  $\dots$ ,  $X_{-1}$ ,  $X_0$ ,  $X_1$ ,  $\dots$  be a stationary (not necessarily Markov) stochastic process. Which of the following statements are true? State true or false. Then either prove or provide a counterexample. Warning: At least one answer is false.
  - (a)  $H(X_n | X_0) = H(X_{-n} | X_0)$ .
  - (b)  $H(X_n|X_0) \ge H(X_{n-1}|X_0)$ .
  - (c)  $H(X_n | X_1^{n-1}, X_{n+1})$  is nonincreasing in n.
- 10. The entropy rate of a dog looking for a bone. A dog walks on the integers, possibly reversing direction at each step with probability  $p=.1$ . Let  $X_0=0$ . The first step is equally likely to be positive or negative. A typical walk might look like this:

$$
(X_0, X_1, \ldots) = (0,-1,-2,-3,-4,-3,-2,-1,0,1,\ldots).
$$

- (a) Find  $H(X_1, X_2, \ldots, X_n)$ .
- (b) Find the entropy rate of this browsing dog.
- (cl What is the expected number of steps the dog takes before reversing direction?
- 11. Random walk on chessboard. Find the entropy rate of the Markov chain associated with a random walk of a king on the  $3 \times 3$  chessboard

| 1 | 2 | 3 |
|---|---|---|
| 4 | 5 | 6 |
| 7 | 8 | 9 |

What about the entropy rate of rooks, bishops and queens? There are two types of bishops.

12. Entropy rate. Let  $\{X_i\}$  be a discrete stationary stochastic process with entropy rate  $H(\mathscr{X})$ . Show

$$
\frac{1}{n}H(X_n,\ldots,X_1|X_0,X_{-1},\ldots,X_{-k})\to H(\mathscr{X}),\tag{4.76}
$$

for  $k = 1, 2, ...$ 

**13.** Entropy rate of constrained sequences. In magnetic recording, the mech anism of recording and reading the bits imposes constraints on the sequences of bits that can be recorded. For example, to ensure proper synchronization, it is often necessary to limit the length of runs of O's between two 1's. Also to reduce intersymbol interference, it may be necessary to require at least one 0 between any two 1's. We will consider a simple example of such a constraint.

Suppose that we are required to have at least one 0 and at most two O's between any pair of l's in a sequences. Thus, sequences like 101001 and 0101001 are valid sequences, but 0110010 and 0000101 are not. We wish to calculate the number of valid sequences of length n.

(a) Show that the set of constrained sequences is the same as the set of allowed paths on the following state diagram:

Image /page/16/Figure/9 description: A directed graph with three nodes labeled 1, 2, and 3. There is a directed edge from node 1 to node 2, and another directed edge from node 2 to node 3. There is also a directed edge from node 1 to node 3, and a self-loop on node 2.

(b) Let  $X_i(n)$  be the number of valid paths of length n ending at state *i*. Argue that  $\mathbf{X}(n) = [X_1(n) \ X_2(n) \ X_3(n)]^T$  satisfies the following recursion:

$$
\begin{bmatrix} X_1(n) \\ X_2(n) \\ X_3(n) \end{bmatrix} = \begin{bmatrix} 0 & 1 & 1 \\ 1 & 0 & 0 \\ 0 & 1 & 0 \end{bmatrix} \begin{bmatrix} X_1(n-1) \\ X_2(n-1) \\ X_3(n-1) \end{bmatrix} = A\mathbf{X}(n-1) \qquad (4.77)
$$

with initial conditions  $\mathbf{X}(1) = \begin{bmatrix} 1 & 1 & 0 \end{bmatrix}^T$ .

(c) Then we have by induction

$$
\mathbf{X}(n) = A\mathbf{X}(n-1) = A^2\mathbf{X}(n-2) = \cdots = A^{n-1}\mathbf{X}(1). \quad (4.78)
$$

Using the eigenvalue decomposition of A for the case of distinct eigenvalues, we can write  $A = U \cap \Lambda U$ , where  $\Lambda$  is the diagonal matrix of eigenvalues. Then  $A^{\prime\prime} = U^{\prime} \wedge^{\prime\prime} U$ . Show that we can write

$$
\mathbf{X}(n) = \lambda_1^{n-1} \mathbf{Y}_1 + \lambda_2^{n-1} \mathbf{Y}_2 + \lambda_3^{n-1} \mathbf{Y}_3, \qquad (4.79)
$$

where  $Y_1$ ,  $Y_2$ ,  $Y_3$  do not depend on *n*. For large *n*, this sum is dominated by the largest term. Therefore argue that for  $i = 1, 2, 3$ , we have

$$
\frac{1}{n}\log X_i(n) \to \log \lambda , \qquad (4.80)
$$

where  $\lambda$  is the largest (positive) eigenvalue. Thus the number of sequences of length n grows as  $\lambda^n$  for large n. Calculate  $\lambda$  for the matrix A above. (The case when the eigenvalues are not distinct can be handled in a similar manner.)

(d) We will now take a different approach. Consider a Markov chain whose state diagram is the one given in part (a), but with arbitrary transition probabilities. Therefore the probability transition matrix of this Markov chain is

$$
P = \begin{bmatrix} 0 & \alpha & 1 \\ 1 & 0 & 0 \\ 0 & 1 - \alpha & 0 \end{bmatrix}.
$$
 (4.81)

Show that the stationary distribution of this Markov chain is

$$
\mu = \left[\frac{1}{3-\alpha}, \frac{1}{3-\alpha}, \frac{1-\alpha}{3-\alpha}\right]^T. \tag{4.82}
$$

- (e) Maximize the entropy rate of the Markov chain over choices of  $\alpha$ . What is the maximum entropy rate of the chain?
- (f) Compare the maximum entropy rate in part (e) with  $log \lambda$  in part (c). Why are the two answers the same?
- **14.** Waiting times are insensitive to distributions. Let  $X_0, X_1, X_2, \ldots$  be drawn i.i.d.  $\sim p(x)$ ,  $x \in \mathcal{X} = \{1, 2, ..., m\}$  and let N be the waiting time to the next occurrence of  $X_0$ , where  $N = \min_{n} \{X_n = X_0\}.$ 
  - (a) Show that  $EN = m$ .
  - (b) Show that E log  $N \leq H(X)$ .
  - (c) (Optional) Prove part (a) for  $\{X_i\}$  stationary and ergodic.

## HISTORICAL NOTES

The entropy rate of a stochastic process was introduced by Shannon [238], who also explored some of the connections between the entropy rate of the process and the number of possible sequences generated by the process. Since Shannon, there have been a number of results extending the basic theorems of information theory to general stochastic processes.

# Data Compression

We now put content in the definition of entropy by establishing the fundamental limit for the compression of information. Data compression can be achieved by assigning short descriptions to the most frequent outcomes of the data source and necessarily longer descriptions to the less frequent outcomes. For example, in Morse code, the most frequent symbol is represented by a single dot. In this chapter we find the shortest average description length of a random variable.

We first define the notion of an instantaneous code and then prove the important Kraft inequality, which asserts that the exponentiated codeword length assignments must look like a probability mass function. Simple calculus then shows that the expected description length must be greater than or equal to the entropy, the first main result. Then Shannon's simple construction shows that the expected description length can achieve this bound asymptotically for repeated descriptions. This establishes the entropy as a natural measure of efficient description length. The famous Huffman coding procedure for finding minimum expected description length assignments is provided. Finally, we show that Huffman codes are competitively optimal and that it requires roughly  $H$  fair coin flips to generate a sample of a random variable having entropy H.

Thus the entropy is the data compression limit as well as the number of bits needed in random number generation. And codes achieving  $H$  turn out to be optimal from many points of view.

## 5.1 EXAMPLES OF CODES

**Definition:** A source code C for a random variable X is a mapping from  $\mathscr X$ , the range of X, to  $\mathscr D^*$ , the set of finite length strings of symbols from a D-ary alphabet. Let  $C(x)$  denote the codeword corresponding to x and let  $l(x)$  denote the length of  $C(x)$ .

For example,  $C(\text{Red}) = 00$ ,  $C(\text{Blue}) = 11$  is a source code for  $\mathcal{X} = \{\text{Red},\}$ Blue} with alphabet  $\mathcal{D} = \{0, 1\}$ .

**Definition:** The expected length  $L(C)$  of a source code  $C(x)$  for a random variable X with probability mass function  $p(x)$  is given by

$$
L(C) = \sum_{x \in \mathscr{X}} p(x)l(x), \qquad (5.1)
$$

where  $l(x)$  is the length of the codeword associated with x.

Without loss of generality, we can assume that the  $D$ -ary alphabet is  $\mathcal{D} = \{0, 1, \ldots, D - 1\}.$ 

Some examples of codes follow.

**Example 5.1.1:** Let X be a random variable with the following distribution and codeword assignment:

Pr(X = 1) = 1/2, codeword C(1) = 0

Pr(X = 2) = 1/4, codeword C(2) = 10

Pr(X = 3) = 1/8, codeword C(3) = 110

Pr(X = 4) = 1/8, codeword C(4) = 111.

(5.2)

The entropy  $H(X)$  of X is 1.75 bits, and the expected length  $L(C) = El(X)$ of this code is also 1.75 bits. Here we have a code that has the same average length as the entropy. We note that any sequence of bits can be uniquely decoded into a sequence of symbols of  $X$ . For example, the bit string 0110111100110 is decoded as 134213.

Example 6.1.2: Consider another simple example of a code for a random variable:

$$
Pr(X = 1) = 1/3, codeword C(1) = 0
$$
  
Pr(X = 2) = 1/3, codeword C(2) = 10 (5.3)  
Pr(X = 3) = 1/3, codeword C(3) = 11.

Just as in the previous case, the code is uniquely decodable. However, in this case the entropy is  $log 3 = 1.58$  bits, while the average length of the encoding is 1.66 bits. Here  $El(X) > H(X)$ .

**Example 5.1.3** (Morse code): The Morse code is a reasonably efficient code for the English alphabet using an alphabet of four symbols: a dot, a dash, a letter space and a word space. Short sequences represent frequent letters (e.g., a single dot represents E) and long sequences represent infrequent letters (e.g., Q is represented by "dash, dash, dot, dash"). This is not the optimal representation for the alphabet in four symbols-in fact, many possible codewords are not utilized because the codewords for letters do not contain spaces except for a letter space at the end of every codeword and no space can follow another space. It is an interesting problem to calculate the number of sequences that can be constructed under these constraints. The problem was solved by Shannon in his original 1948 paper. The problem is also related to coding for magnetic recording, where long strings of O's are prohibited [2], [184].

We now define increasingly more stringent conditions on codes. Let  $x^n$ denote  $(x_1, x_2, \ldots, x_n)$ .

**Definition:** A code is said to be non-singular if every element of the range of X maps into a different string in  $\mathcal{D}^*$ , i.e.,

$$
x_i \neq x_j \Rightarrow C(x_i) \neq C(x_j) \,. \tag{5.4}
$$

Non-singularity suffices for an unambiguous description of a single value of  $X$ . But we usually wish to send a sequence of values of  $X$ . In such cases, we can ensure decodability by adding a special symbol (a "comma") between any two codewords. But this is an inefficient use of the special symbol; we can do better by developing the idea of selfpunctuating or instantaneous codes. Motivated by the necessity to send sequences of symbols X, we define the extension of a code as follows:

**Definition:** The extension  $C^*$  of a code C is the mapping from finite length strings of  $\mathscr X$  to finite length strings of  $\mathscr D$ , defined by

$$
C(x_1x_2\cdots x_n) = C(x_1)C(x_2)\cdots C(x_n), \qquad (5.5)
$$

where  $C(x_1)C(x_2) \cdots C(x_n)$  indicates concatenation of the corresponding codewords.

**Example 5.1.4:** If  $C(x_1) = 00$  and  $C(x_2) = 11$ , then  $C(x_1, x_2) = 0011$ .

**Definition:** A code is called uniquely decodable if its extension is non-singular.

In other words, any encoded string in a uniquely decodable code has only one possible source string producing it. However, one may have to look at the entire string to determine even the first symbol in the corresponding source string.

**Definition:** A code is called a *prefix code* or an *instantaneous code* if no codeword is a prefix of any other codeword.

An instantaneous code can be decoded without reference to the future codewords since the end of a codeword is immediately recognizable. Hence, for an instantaneous code, the symbol  $x_i$  can be decoded as soon as we come to the end of the codeword corresponding to it. We need not wait to see the codewords that come later. An instantaneous code is a "self-punctuating" code; we can look down the sequence of code symbols and add the commas to separate the codewords without looking at later symbols. For example, the binary string 01011111010 produced by the code of Example  $5.1.1$  is parsed as  $0, 10, 111, 110, 10$ .

The nesting of these definitions is shown in Figure 5.1. To illustrate the differences between the various kinds of codes, consider the following examples of codeword assignments  $C(x)$  to  $x \in \mathcal{X}$  in Table 5.1.

For the non-singular code, the code string 010 has three possible source sequences: 2 or 14 or 31, and hence the code is not uniquely decodable.

The uniquely decodable code is not prefix free and is hence not instantaneous. To see that it is uniquely decodable, take any code string and start from the beginning. If the first two bits are 00 or 10, they can be decoded immediately. If the first two bits are 11, then we must look at the following bits. If the next bit is a 1, then the first source symbol is a 3. If the length of the string of O's immediately following the 11 is odd, then the first codeword must be 110 and the first source symbol must be

Image /page/21/Figure/7 description: This is a Venn diagram illustrating the relationship between different types of codes. The outermost circle represents "All codes." Inside this, a larger circle labeled "Non-singular codes" encloses a smaller circle labeled "Uniquely decodable codes." The innermost and smallest circle is labeled "Instantaneous codes." The diagram shows that instantaneous codes are a subset of uniquely decodable codes, which are a subset of non-singular codes, which in turn are a subset of all codes.

Figure 5.1. Classes of codes.

| X | Singular | Non-singular, but not uniquely decodable | Uniquely decodable, but not instantaneous | Instantaneous |
|---|----------|------------------------------------------|-------------------------------------------|---------------|
| 1 | 0        | 0                                        | 10                                        | 0             |
| 2 | 0        | 010                                      | 00                                        | 10            |
| 3 | 0        | 01                                       | 11                                        | 110           |
| 4 | 0        | 10                                       | 110                                       | 111           |

TABLE 5.1. Classes of Codes

4; if the length of the string of O's is even, then the first source symbol is a 3. By repeating this argument, we can see that this code is uniquely decodable. Sardinas and Patterson have devised a finite test for unique decodability, which involves forming sets of possible suffixes to the codewords and systematically eliminating them. The test is described more fully in Problem 24 at the end of the chapter.

The fact that the last code in Table 5.1 is instantaneous is obvious since no codeword is a prefix of any other.

## 5.2 KRAFT INEQUALITY

We wish to construct instantaneous codes of minimum expected length to describe a given source. It is clear that we cannot assign short codewords to all source symbols and still be prefix free. The set of codeword lengths possible for instantaneous codes is limited by the following inequality:

**Theorem 5.2.1** (Kraft inequality): For any instantaneous code (prefix) code) over an alphabet of size D, the codeword lengths  $l_1, l_2, \ldots, l_m$  must satisfy the inequality

$$
\sum_{i} D^{-l_i} \le 1 \,. \tag{5.6}
$$

Conversely, given a set of codeword lengths that satisfy this inequality, there exists an instantaneous code with these word lengths.

Proof: Consider a D-ary tree in which each node has D children. Let the branches of the tree represent the symbols of the codeword. For example, the  $D$  branches arising from the root node represent the  $D$ possible values of the first symbol of the codeword. Then each codeword is represented by a leaf on the tree. The path from the root traces out the symbols of the codeword. A binary example of such a tree is shown in Figure 5.2.

Image /page/23/Figure/1 description: A binary tree diagram starts with a node labeled "Root". From the root, two branches extend, one labeled "0" and the other labeled "1". The branch labeled "0" leads to another node from which two dashed branches extend, each labeled "0". The branch labeled "1" leads to a node from which two solid branches extend. The upper branch is labeled "0" and leads to a node from which two dashed branches extend, one labeled "10". The lower branch is labeled "1" and leads to a node from which two solid branches extend, labeled "0" and "1" respectively. The branch labeled "0" terminates with the label "110", and the branch labeled "1" terminates with the label "111".

Figure 5.2. Code tree for the Krafi inequality.

The prefix condition on the codewords implies that no codeword is an ancestor of any other codeword on the tree. Hence, each codeword eliminates its descendants as possible codewords.

Let  $l_{\text{max}}$  be the length of the longest codeword of the set of codewords. Consider all nodes of the tree at level  $l_{\text{max}}$ . Some of them are codewords, some are descendants of codewords, and some are neither. A codeword at level  $l_i$  has  $D^{\text{max} \to i}$  descendants at level  $l_{\text{max}}$ . Each of these descendant sets must be disjoint. Also, the total number of nodes in these sets must be less than or equal to  $D^{l_{\max}}$ . Hence, summing over all the codewords, we have

$$
\sum D^{l_{\max}-l_i} \le D^{l_{\max}} \tag{5.7}
$$

or

$$
\sum D^{-l_i} \le 1\,,\tag{5.8}
$$

which is the Kraft inequality.

Conversely, given any set of codeword lengths  $l_1, l_2, \ldots, l_m$  which satisfy the Kraft inequality, we can always construct a tree like the one in Figure 5.2. Label the first node (lexicographically) of depth  $l_1$  as codeword 1, and remove its descendants from the tree. Then label the first remaining node of depth  $l_2$  as codeword 2, etc. Proceeding this way, we construct a prefix code with the specified  $l_1, l_2, \ldots, l_m$ .

We now show that an infinite prefix code also satisfies the Kraft inequality.

**Theorem 5.2.2** (*Extended Kraft Inequality*): For any countably infinite set of codewords that form a prefix code, the codeword lengths satisfy the extended Kraft inequality,

$$
\sum_{i=1}^{\infty} D^{-l_i} \le 1 \tag{5.9}
$$

Conversely, given any  $l_1, l_2, \ldots$  satisfying the extended Kraft inequality, we can construct a prefix code with these codeword lengths.

**Proof:** Let the D-ary alphabet be  $\{0, 1, \ldots, D-1\}$ . Consider the *i*th codeword  $y_1y_2 \ldots y_{l_i}$ . Let  $0.y_1y_2 \cdots y_{l_i}$  be the real number given by the D-ary expansion

$$
0.y_1y_2\cdots y_{l_i} = \sum_{j=1}^{l_i} y_j D^{-j}.
$$
 (5.10)

This codeword corresponds to the interval

$$
\left(0.\mathbf{y}_1\mathbf{y}_2\cdots\mathbf{y}_{l_i},0.\mathbf{y}_1\mathbf{y}_2\cdots\mathbf{y}_{l_i}+\frac{1}{D^{l_i}}\right),\tag{5.11}
$$

the set of all real numbers whose D-ary expansion begins with  $0.y_1y_2 \cdots y_l$ . This is a subinterval of the unit interval [0, 1]. By the prefix condition, these intervals are disjoint. Hence the sum of their lengths has to be less than or equal to 1.

This proves that

$$
\sum_{i=1}^{\infty} D^{-l_i} \le 1 \tag{5.12}
$$

Just as in the finite case, we can reverse the proof to construct the code for a given  $l_1, l_2, \ldots$  that satisfies the Kraft inequality. First reorder the indexing so that  $l_1 \geq l_2 \geq \ldots$ . Then simply assign the intervals in order from the low end of the unit interval.  $\square$ 

In Section 5.5, we will show that the lengths of codewords for a uniquely decodable code also satisfy the Kraft inequality. Before we do that, we consider the problem of finding the shortest instantaneous code.

## 5.3 OPTIMAL CODES

In the previous section, we proved that any codeword set that satisfies the prefix condition has to satisfy the Kraft inequality and that the Kraft inequality is a sufficient condition for the existence of a codeword set with the specified set of codeword lengths. We now consider the problem of finding the prefix code with the minimum expected length. From the results of the previous section, this is equivalent to finding the set of lengths  $l_1, l_2, \ldots, l_m$  satisfying the Kraft inequality and whose expected length  $L = \sum p_i l_i$  is less than the expected length of any other prefix code. This is a standard optimization problem: Minimize

$$
L = \sum p_i l_i \tag{5.13}
$$

over all integers  $l_1, l_2, \ldots, l_m$  satisfying

$$
\sum D^{-l_i} \le 1 \,. \tag{5.14}
$$

A simple analysis by calculus suggests the form of the minimizing  $l^*$ . We neglect the integer constraint on  $l_i$  and assume equality in the constraint. Hence, we can write the constrained minimization using Lagrange multipliers as the minimization of

$$
J = \sum p_i l_i + \lambda \left( \sum D^{-l_i} \right). \tag{5.15}
$$

Differentiating with respect to  $l_i$ , we obtain

$$
\frac{\partial J}{\partial l_i} = p_i - \lambda D^{-l_i} \log_e D \,. \tag{5.16}
$$

Setting the derivative to 0, we obtain

$$
D^{-l_i} = \frac{p_i}{\lambda \log_e D} \,. \tag{5.17}
$$

Substituting this in the constraint to find  $\lambda$ , we find  $\lambda = 1/\log_e D$  and hence

$$
p_i = D^{-l_i},\tag{5.18}
$$

yielding optimal codelengths

$$
l_i^* = -\log_D p_i \,. \tag{5.19}
$$

This non-integer choice of codeword lengths yields expected codeword length

$$
L^* = \sum p_i l_i^* = -\sum p_i \log_D p_i = H_D(X) \,. \tag{5.20}
$$

But since the  $l_i$  must be integers, we will not always be able to set the codeword lengths as in (5.19). Instead, we should choose a set of codeword lengths  $l_i$  "close" to the optimal set. Rather than demonstrate by calculus that  $l_i^* = -\log_p p_i$  is a global minimum, we will verify optimality directly in the proof of the following theorem.

Theorem 5.3.1: The expected length L of any instantaneous D-ary code for a random variable X is greater than or equal to the entropy  $H_n(X)$ , i.e.,

$$
L \ge H_p(X) \tag{5.21}
$$

with equality iff  $D^{-l_i} = p_i$ .

Proof: We can write the difference between the expected length and the entropy as

$$
L - H_D(X) = \sum p_i l_i - \sum p_i \log_D \frac{1}{p_i}
$$
 (5.22)

$$
= -\sum p_i \log_D D^{-l_i} + \sum p_i \log_D p_i. \tag{5.23}
$$

Letting  $r_i = D^{-l_i}/\Sigma_i D^{-l_j}$  and  $c = \Sigma D^{-l_i}$ , we obtain

$$
L - H = \sum p_i \log_D \frac{p_i}{r_i} - \log_D c \tag{5.24}
$$

$$
=D(\mathbf{p}||\mathbf{r}) + \log_D \frac{1}{c}
$$
 (5.25)

$$
\geq 0 \tag{5.26}
$$

by the non-negativity of relative entropy and the fact (Kraft inequality) that  $c \le 1$ . Hence  $L \ge H$  with equality iff  $p_i = D^{-l_i}$ , i.e., iff  $-\log_p p_i$  is an integer for all  $i. \Box$ 

**Definition:** A probability distribution is called  $D$ -adic with respect to  $D$ if each of the probabilities is equal to  $D^{-n}$  for some n.

Thus we have equality in the theorem if and only if the distribution of  $X$  is  $D$ -adic.

The preceding proof also indicates a procedure for finding an optimal code: find the D-adic distribution that is closest (in the relative entropy sense) to the distribution of  $X$ . This distribution provides the set of codeword lengths. Construct the code by choosing the first available node as in the proof of the Kraft inequality. We then have an optimal code for X.

However, this procedure is not easy, since the search for the closest D-adic distribution is not obvious. In the next section, we give a good suboptimal procedure (Shannon-Fano coding). In Section 5.6, we describe a simple procedure (Huffman coding) for actually finding the optimal code.

## 5.4 BOUNDS ON THE OPTIMAL CODELENGTH

We now demonstrate a code that achieves an expected description length  $L$  within 1 bit of the lower bound, that is,

$$
H(X) \le L < H(X) + 1. \tag{5.27}
$$

Recall the setup of the last section: we wish to minimize  $L = \sum p_i l_i$ subject to the constraint that  $l_1, l_2, \ldots, l_m$  are integers and  $\Sigma D^{-l_i} \leq 1$ . We proved that the optimal codeword lengths can be found by finding the D-adic probability distribution closest to the distribution of  $X$  in relative entropy i.e., finding the D-adic r  $(r_i = D^{-l_i}/\Sigma, D^{-l_j})$  minimizing

$$
L - H_D = D(\mathbf{p}||\mathbf{r}) - \log \left(\sum D^{-l_i}\right) \ge 0.
$$
 (5.28)

The choice of word lengths  $l_i = \log_{D} \frac{1}{p_i}$  yields  $L = H$ . Since  $\log_{D} \frac{1}{p_i}$  may not equal an integer, we round it up to give integer word length assignments,

$$
l_i = \left\lceil \log_D\left(\frac{1}{p_i}\right) \right\rceil,\tag{5.29}
$$

where  $[x]$  is the smallest integer  $\geq x$ . These lengths satisfy the Kraft inequality since

$$
\sum D^{-\left\lceil \log \frac{1}{p_i} \right\rceil} \leq \sum D^{-\log \frac{1}{p_i}} = \sum p_i = 1.
$$
 (5.30)

This choice of codeword lengths satisfies

$$
\log_D \frac{1}{p_i} \le l_i < \log_D \frac{1}{p_i} + 1 \,. \tag{5.31}
$$

Multiplying by  $p_i$  and summing over i, we obtain

$$
H_p(X) \le L < H_p(X) + 1. \tag{5.32}
$$

Since the optimal code can only be better than this code, we have the since the optima

**Theorem 5.4.1:** Let  $l_1^*, l_2^*, \ldots, l_m^*$  be the optimal codeword lengths for a source distribution  $p$  and a D-ary alphabet, and let  $L^*$  be the associated expected length of the optimal code  $(L^* = \Sigma p_i l_i^*)$ . Then

$$
H_D(X) \le L^* < H_D(X) + 1 \tag{5.33}
$$

**Proof:** Let  $l_i = \lceil \log_b \frac{1}{p_i} \rceil$ . Then  $l_i$  satisfies the Kraft inequality and from (5.32) we have

$$
H_D(X) \le L = \sum p_i l_i < H_D(X) + 1 \,. \tag{5.34}
$$

But since  $L^*$ , the expected length of the optimal code, is less than  $L = \sum p_i l_i$ , and since  $L^* \ge H_n$  from Theorem 5.3.1, we have the theorem.  $\Box$ 

In the preceding theorem, there is an overhead which is at most 1 bit, due to the fact that  $\log \frac{1}{p}$  is not always an integer. We can reduce the overhead per symbol by spreading it out over many symbols. With this in mind, let us consider a system in which we send a sequence of  $n$ symbols from  $X$ . The symbols are assumed to be drawn i.i.d. according to  $p(x)$ . We can consider these *n* symbols to be a supersymbol from the alphabet  $\mathscr{X}^n$ .

Define  $L_n$  to be the expected codeword length per input symbol, i.e., if  $l(x_1, x_2, \ldots, x_n)$  is the length of the codeword associated with  $(x_1, x_2, \ldots, x_n)$ , then

$$
L_n = \frac{1}{n} \sum p(x_1, x_2, \dots, x_n) l(x_1, x_2, \dots, x_n) = \frac{1}{n} El(X_1, X_2, \dots, X_n) .
$$
\n(5.35)

We can now apply the bounds derived above to the code:

$$
H(X_1, X_2, \ldots, X_n) \leq El(X_1, X_2, \ldots, X_n) < H(X_1, X_2, \ldots, X_n) + 1 \tag{5.36}
$$

Since  $X_1, X_2, \ldots, X_n$  are i.i.d.,  $H(X_1, X_2, \ldots, X_n) = \sum H(X_i) = nH(X)$ . Dividing  $(5.36)$  by n, we obtain

$$
H(X) \le L_n < H(X) + \frac{1}{n} \tag{5.37}
$$

Hence by using large block lengths we can achieve an expected codelength per symbol arbitrarily close to the entropy.

We can also use the same argument for a sequence of symbols from a stochastic process that is not necessarily i.i.d. In this case, we still have the bound

$$
H(X_1, X_2, \ldots, X_n) \leq El(X_1, X_2, \ldots, X_n) < H(X_1, X_2, \ldots, X_n) + 1 \,.
$$
\n(5.38)

Dividing by *n* again and defining  $L_n$  to be the expected description length per symbol, we obtain

$$
\frac{H(X_1, X_2, \dots, X_n)}{n} \le L_n < \frac{H(X_1, X_2, \dots, X_n)}{n} + \frac{1}{n} \,. \tag{5.39}
$$

If the stochastic process is stationary, then  $H(X_1, X_2, \ldots, X_n)/n \to H(\mathcal{X}),$ and the expected description length tends to the entropy rate as  $n \to \infty$ . Thus we have the following theorem:

Theorem 5.4.2: The minimum expected codeword length per symbol satisfies

$$
\frac{H(X_1, X_2, \dots, X_n)}{n} \le L_n^* < \frac{H(X_1, X_2, \dots, X_n)}{n} + \frac{1}{n} \,. \tag{5.40}
$$

Moreover, if  $X_1, X_2, \ldots, X_n$  is a stationary stochastic process,

$$
L_n^* \to H(\mathscr{X})\,,\tag{5.41}
$$

where  $H(\mathcal{X})$  is the entropy rate of the process.

This theorem provides another justification for the definition of entropy rate—it is the expected number of bits per symbol required to describe the process.

Finally, we ask what happens to the expected description length if the code is designed for the wrong distribution. For example, the wrong distribution may be the best estimate that we can make of the unknown true distribution.

We consider the Shannon code assignment  $l(x) = \int \log \frac{1}{q(x)}$  designed for the probability mass function  $q(x)$ . Suppose the true probability mass function is  $p(x)$ . Thus we will not achieve expected length  $L \approx H(p)$  =  $-\sum p(x)$  log p(x). We now show that the increase in expected description length due to the incorrect distribution is the relative entropy  $D(p||q)$ . Thus  $D(p||q)$  has a concrete interpretation as the increase in descriptive complexity due to incorrect information.

**Theorem 5.4.3:** The expected length under  $p(x)$  of the code assignment  $l(x) = \lceil \log \frac{1}{g(x)} \rceil$  satisfies

$$
H(p) + D(p||q) \le E_p l(X) < H(p) + D(p||q) + 1. \tag{5.42}
$$

Proof: The expected codelength is

$$
El(X) = \sum_{x} p(x) \left[ \log \frac{1}{q(x)} \right]
$$
 (5.43)

$$
\langle \sum_{x} p(x) \left( \log \frac{1}{q(x)} + 1 \right) \tag{5.44}
$$

$$
= \sum_{x} p(x) \log \frac{p(x)}{q(x)} \frac{1}{p(x)} + 1
$$
 (5.45)

$$
= \sum_{x} p(x) \log \frac{p(x)}{q(x)} + \sum_{x} p(x) \log \frac{1}{p(x)} + 1
$$
 (5.46)

$$
= D(p||q) + H(p) + 1.
$$
 (5.47)

The lower bound can be derived similarly.  $\Box$ 

Thus using the wrong distribution incurs a penalty of  $D(p||q)$  in the average description length.

## 5.5 KRAFT INEQUALITY FOR UNIQUELY DECODABLE CODES

We have proved that any instantaneous code must satisfy the Kraft inequality. The class of uniquely decodable codes is larger than the class of instantaneous codes, so one expects to achieve a lower expected codeword length if L is minimized over all uniquely decodable codes. In this section, we prove that the class of uniquely decodable codes does not offer any further possibilities for the set of codeword lengths than do instantaneous codes. We now give Karush's elegant proof of the following theorem.

**Theorem 5.5.1** (McMillan): The codeword lengths of any uniquely decodable code must satisfy the Kraft inequality

$$
\sum D^{-l_i} \le 1 \,. \tag{5.48}
$$

Conversely, given a set of codeword lengths that satisfy this inequality, it is possible to construct a uniquely decodable code with these codeword lengths.

**Proof:** Consider  $C^k$ , the kth extension of the code, i.e., the code formed by the concatenation of  $k$  repetitions of the given uniquely decodable code C. By the definition of unique decodability, the kth extension of the code is non-singular. Since there are only  $D<sup>n</sup>$  different D-ary strings of length  $n$ , unique decodability implies that the number

of code sequences of length  $n$  in the kth extension of the code must be no greater than  $D<sup>n</sup>$ . We now use this observation to prove the Kraft inequality.

Let the codeword lengths of the symbols  $x \in \mathcal{X}$  be denoted by  $l(x)$ . For the extension code, the length of the code-sequence is

$$
l(x_1, x_2, \dots, x_k) = \sum_{i=1}^k l(x_i).
$$
 (5.49)

The inequality that we wish to prove is

$$
\sum_{x \in \mathscr{X}} D^{-l(x)} \le 1 \,. \tag{5.50}
$$

The trick is to consider the  $k$ <sup>th</sup> power of this quantity. Thus

$$
\left(\sum_{x\in\mathscr{X}}D^{-l(x)}\right)^k = \sum_{x_1\in\mathscr{X}}\sum_{x_2\in\mathscr{X}}\cdots\sum_{x_k\in\mathscr{X}}D^{-l(x_1)}D^{-l(x_2)}\cdots D^{-l(x_k)}\qquad(5.51)
$$

$$
= \sum_{x_1, x_2, \dots, x_k \in \mathcal{X}^k} D^{-l(x_1)} D^{-l(x_2)} \cdot \cdot \cdot D^{-l(x_k)} \qquad (5.52)
$$

$$
=\sum_{x^k\in\mathscr{X}^k}D^{-l(x^k)},\tag{5.53}
$$

by (5.49). We now gather the terms by word lengths to obtain

$$
\sum_{x^k \in \mathcal{X}^k} D^{-l(x^k)} = \sum_{m=1}^{kl_{\text{max}}} a(m) D^{-m}, \qquad (5.54)
$$

where  $l_{\text{max}}$  is the maximum codeword length and  $a(m)$  is the number of source sequences  $x^k$  mapping into codewords of length m. But the code is uniquely decodable, so there is at most one sequence mapping into each code *m*-sequence and there are at most  $D^m$  code *m*-sequences. Thus  $a(m) \leq D^m$ , and we have

$$
\left(\sum_{x\in\mathscr{X}}D^{-l(x)}\right)^k = \sum_{m=1}^{kl_{\text{max}}}a(m)D^{-m} \tag{5.55}
$$

$$
\leq \sum_{m=1}^{kl_{\max}} D^m D^{-m} \tag{5.56}
$$

$$
=kl_{\text{max}}\tag{5.57}
$$

and hence

$$
\sum_{j} D^{-l_j} \leq \left(k l_{\text{max}}\right)^{1/k}.
$$
\n(5.58)

Since this inequality is true for all k, it is true in the limit as  $k \rightarrow \infty$ . Since  $(kl_{\max})^{1/k} \rightarrow 1$ , we have

$$
\sum_{j} D^{-l_j} \le 1 , \qquad (5.59)
$$

which is the Kraft inequality.

Conversely, given any set of  $l_1, l_2, \ldots, l_m$  satisfying the Kraft inequality, we can construct an instantaneous code as proved in Section 5.2. Since every instantaneous code is uniquely decodable, we have also constructed a uniquely decodable code.  $\Box$ 

Corollary: A uniquely decodable code for an infinite source alphabet  $\mathscr X$  also satisfies the Kraft inequality.

Proof: The point at which the preceding proof breaks down for infinite  $|\mathcal{X}|$  is at (5.58), since for an infinite code  $l_{\text{max}}$  is infinite. But there is a simple fix to the proof. Any subset of a uniquely decodable code is also uniquely decodable; hence, any finite subset of the infinite set of codewords satisfies the Kraft inequality. Hence,

$$
\sum_{i=1}^{\infty} D^{-l_i} = \lim_{N \to \infty} \sum_{i=1}^{N} D^{-l_i} \le 1.
$$
 (5.60)

Given a set of word lengths  $l_1, l_2, \ldots$  that satisfy the Kraft inequality, we can construct an instantaneous code as in the last section. Since instantaneous codes are uniquely decodable, we have constructed a uniquely decodable code with an infinite number of codewords. So the McMillan theorem also applies to infinite alphabets.  $\Box$ 

The theorem implies a rather surprising result—that the class of uniquely decodable codes does not offer any further choices for the set of codeword lengths than the class of prefix codes. The set of achievable codeword lengths is the same for uniquely decodable and instantaneous codes. Hence the bounds derived on the optimal codeword lengths continue to hold even when we expand the class of allowed codes to the class of all uniquely decodable codes.

## 5.6 HUFFMAN CODES

An optimal (shortest expected length) prefix code for a given distribution can be constructed by a simple algorithm discovered by Huffman [1381. 'We will prove that any other code for the same alphabet cannot have a lower expected length than the code constructed by the

algorithm. Before we give any formal proofs, let us introduce Huffman codes with some examples:

**Example 5.6.1:** Consider a random variable X taking values in the set  $\mathscr{X} = \{1, 2, 3, 4, 5\}$  with probabilities 0.25, 0.25, 0.2, 0.15, 0.15, respectively. We expect the optimal binary code for  $X$  to have the longest codewords assigned to the symbols 4 and 5. Both these lengths must be equal, since otherwise we can delete a bit from the longer codeword and still have a prefix code, but with a shorter expected length. In general, we can construct a code in which the two longest codewords differ only in the last bit. For this code, we can combine the symbols 4 and 5 together into a single source symbol, with a probability assignment 0.30. Proceeding this way, combining the two least likely symbols into one symbol, until we are finally left with only one symbol, and then assigning codewords to the symbols, we obtain the following table:

| Codeword<br>length | Codeword | X | Probability |      |      |      |   |
|--------------------|----------|---|-------------|------|------|------|---|
| 2                  | 01       | 1 | 0.25        | 0.3  | 0.45 | 0.55 | 1 |
| 2                  | 10       | 2 | 0.25        | 0.25 | 0.25 | 0.3  |   |
| 2                  | 11       | 3 | 0.2         | 0.25 | 0.25 | 0.25 |   |
| 3                  | 000      | 4 | 0.15        | 0.2  | 0.45 |      |   |
| 3                  | 001      | 5 | 0.15        |      | 0.45 |      |   |

This code has average length 2.3 bits.

**Example 5.6.2:** Consider a ternary code for the same random variable. Now we combine the three least likely symbols into one supersymbol and obtain the following table:

Image /page/33/Figure/6 description: The image is a table with three columns: Codeword, X, and Probability. The Codeword column lists '1', '2', '00', '01', and '02'. The X column lists '1', '2', '3', '4', and '5' corresponding to the codewords. The Probability column shows probabilities associated with the X values. Specifically, '1' has a probability of '0.25', '2' has a probability of '0.25', '3' has a probability of '0.2', '4' has a probability of '0.15', and '5' has a probability of '0.15'. Lines connect the probabilities to the values in the X column, and further lines connect some of these probabilities to the value '1' in a separate column labeled '1' which appears to represent a sum or a final outcome.

This code has an average length of 1.5 ternary digits.

**Example 5.6.3:** If  $D \geq 3$ , we may not have a sufficient number of symbols so that we can combine them  $D$  at a time. In such a case, we add dummy symbols to the end of the set of symbols. The dummy symbols have probability 0 and are inserted to fill the tree. Since at each stage of the reduction, the number of symbols is reduced by  $D-1$ , we want the total number of symbols to be  $1 + k(D-1)$ , where k is the number of levels in the tree. Hence, we add enough dummy symbols so that the total number of symbols is of this form. For example:

Image /page/34/Figure/2 description: The image is a table with three columns: Codeword, X, and Probability. The table lists seven codewords: 1, 2, 01, 02, 000, 001, and 002. The 'X' column lists corresponding values: 1, 2, 3, 4, 5, 6, and 'Dummy'. The 'Probability' column shows a breakdown of probabilities associated with these values. For X=1, the probability is 0.25, which contributes to a cumulative probability of 0.5 and then 1.0. For X=2, the probability is 0.25, contributing to the cumulative probability of 0.5 and then 1.0. For X=3, the probability is 0.2, contributing to the cumulative probability of 0.2. For X=4, the probability is 0.1, contributing to the cumulative probability of 0.2. For X=5, the probability is 0.1, contributing to the cumulative probability of 0.1. For X=6, the probability is 0.1, contributing to the cumulative probability of 0.1. The 'Dummy' value has a probability of 0.0.

This code has an average length of 1.7 ternary digits.

A proof of the optimality of Huffman coding will be given in Section 5.8.

## 5.7 SOME COMMENTS ON HUFFMAN CODES

1. Equivalence of source coding and 20 questions. We now digress to show the equivalence of coding and the game of 20 questions.

Supposing we wish to find the most efficient series of yes-no questions to determine an object from a class of objects. Assuming we know the probability distribution on the objects, can we find the most efficient sequence of questions?

We first show that a sequence of questions is equivalent to a code for the object. Any question depends only on the answers to the questions before it. Since the sequence of answers uniquely determines the object, each object has a different sequence of answers, and if we represent the yes-no answers by O's and l's, we have a binary code for the set of objects. The average length of this code is the average number of questions for the questioning scheme.

Also, from a binary code for the set of objects, we can find a sequence of questions that correspond to the code, with the average number of questions equal to the expected codeword length of the code. The first question in this scheme becomes "Is the first bit equal to 1 in the object's codeword?"

Since the Huffman code is the best source code for a random variable, the optimal series of questions is that determined by the Huffman code. In Example 5.6.1, the optimal first question is "Is  $X$  equal to 2 or 3?" The answer to this determines the first bit of the Huffman code. Assuming the answer to the first question is 'Yes," the next question should be "Is  $X$  equal to 3?" which determines the second bit. However, we need not wait for the answer to the first question to ask the second. We can ask as our second question "Is  $X$  equal to 1 or 3?" determining the second bit of the Huffman code independently of the first.

The expected number of questions EQ in this optimal scheme satisfies

$$
H(X) \le EQ < H(X) + 1. \tag{5.61}
$$

2. Huffman coding for weighted codewords. Huffman's algorithm for minimizing  $\Sigma$  p<sub>i</sub>l, can be applied to any set of numbers  $p_i \geq 0$ , regardless of  $\Sigma$   $p_i$ . In this case, the Huffman code minimizes the sum of weighted codelengths  $\sum w_i l_i$  rather than the average codelength.

**Example 5.7.1:** We perform the weighted minimization using the same algorithm.

Image /page/35/Figure/6 description: A table with three columns labeled X, Codeword, and Weights. The X column lists numbers 1 through 4. The Codeword column lists binary codes 00, 01, 10, and 11 corresponding to the numbers in the X column. The Weights column shows a diagram with lines connecting numbers. The number 5 is connected to 8 and 10. The number 5 is also connected to 5 and 8. The number 4 is connected to 5 and 5. The number 4 is also connected to 10 and 18.

In this case the code minimizes the weighted sum of the codeword lengths, and the minimum weighted sum is 36.

3. **Huffman coding and "slice" questions.** We have described the equivalence of source coding with the game of 20 questions. The optimal sequence of questions corresponds to an optimal source code for the random variable. However, Huffman codes ask arbitrary questions of the form "Is  $X \in A$ ?" for any set  $A \subset$  $\{1, 2, \ldots, m\}.$ 

Now we consider the game of 20 questions with a restricted set of questions. Specifically, we assume that the elements of  $\mathcal{X} =$  $\{1, 2, \ldots, m\}$  are ordered so that  $p_1 \geq p_2 \geq \cdots \geq p_m$  and that the only questions allowed are of the form "Is  $X > a$ ?" for some a.

The Huffman code constructed by the Huffman algorithm may not correspond to "slices" (sets of the form  $\{x : x \le a\}$ ). If we take the codeword lengths  $(l_1 \le l_2 \le \cdots \le l_m)$ , by Lemma 5.8.1) derived from the Huffman code and use them to assign the symbols to the

code tree by taking the first available node at the corresponding level, we will construct another optimal code. However, unlike the Huffman code itself, this code is a "slice" code, since each question (each bit of the code) splits the tree into sets of the form  $\{x : x > a\}$ and  $\{x : x < a\}.$ 

We illustrate this with an example.

Example 6.7.2: Consider the first example of Section 5.6. The code that was constructed by the Huffman coding procedure is not a "slice" code. But using the codeword lengths from the Huffman procedure, namely,  $\{2, 2, 2, 3, 3\}$ , and assigning the symbols to the first available node on the tree, we obtain the following code for this random variable:

 $1\rightarrow 00$ ,  $2\rightarrow 01$ ,  $3\rightarrow 10$ ,  $4\rightarrow 110$ ,  $5\rightarrow 111$ 

It can be verified that this code is a "slice" code. These "slice" codes are known as alphabetic codes because the codewords are alphabetically ordered.

4. Huffman codes and Shannon codes. Using codeword lengths of  $\lceil \log \frac{1}{p} \rceil$  (which is called Shannon coding) may be much worse than the optimal code for some particular symbol. For example, consider two symbols, one of which occurs with probability 0.9999 and the other with probability 0.0001. Then using codeword lengths of  $\lceil \log \frac{1}{P_i} \rceil$  implies using codeword lengths of 1 bit and 14 bits respectively. The optimal codeword length is obviously 1 bit for both symbols. Hence, the code for the infrequent symbol is much longer in the Shannon code than in the optimal code.

Is it true that the codeword lengths for an optimal code are always less than  $\lceil \log \frac{1}{p} \rceil$ ? The following example illustrates that this is not always true.

**Example 5.7.3:** Consider a random variable  $X$  with a distribution  $(\frac{1}{3}, \frac{1}{3}, \frac{1}{4}, \frac{1}{12})$ . The Huffman coding procedure results in codeword lengths of  $(2,2,2,2)$  or  $(1,2,3,3)$  (depending on where one puts the merged probabilities, as the reader can verify). Both these codes achieve the same expected codeword length. In the second code, the third symbol has length 3, which is greater than  $\lceil \log \frac{1}{p_3} \rceil$ . Thus the codeword length for a Shannon code could be less than the codeword length of the corresponding symbol of an optimal (Huffman) code.

This example also illustrates the fact that the set of codeword lengths for an optimal code is not unique (there may be more than one set of lengths with the same expected value).

Although either the Shannon code or the Huffman code can be shorter for individual symbols, the Huffman code is shorter on the average. Also, the Shannon code and the Huffman code differ by less than one bit in expected codelength (since both lie between  $H$ and  $H + 1$ .)

5. Fano codes. Fano proposed a suboptimal procedure for constructing a source code, which is similar to the idea of slice codes. In his method, we first order the probabilities in decreasing order. Then we choose k such that  $|\sum_{i=1}^{k} p_i - \sum_{i=k+1}^{m} p_i|$  is minimized. This point divides the source symbols into two sets of almost equal probability. Assign 0 for the first bit of the upper set and 1 for the lower set. Repeat this process for each subset. By this recursive procedure, 'we obtain a code for each source symbol. This scheme, though not optimal in general, achieves  $L(C) \le H(X) + 2$ . (See [137].)

## 5.8 OPTIMALITY OF HUFFMAN CODES

We prove by induction that the binary Huffman code is optimal. It is important to remember that there are many optimal codes: inverting all the bits or exchanging two codewords of the same length will give another optimal code. The Huffman procedure constructs one such optimal code. To prove the optimality of Huffman codes, we first prove some properties of a particular optimal code.

Without loss of generality, we will assume that the probability masses are ordered, so that  $p_1 \geq p_2 \geq \cdots \geq p_m$ . Recall that a code is optimal if  $\Sigma$   $p_i l_i$  is minimal.

Lemma 5.8.1: For any distribution, there exists an optimal instantaneous code (with minimum expected length) that satisfies the following properties:

- 1. If  $p_j > p_k$ , then  $l_j \leq l_k$ .
- 2. The two longest codewords have the same length.
- 3. The two longest codewords differ only in the last bit and correspond to the two least likely symbols.

Proof: The proof amounts to swapping, trimming and rearranging, as shown in Figure 5.3. Consider an optimal code  $C_m$ :

If  $p_j > p_k$ , then  $l_j \leq l_k$ . Here we swap codewords.

Consider  $C_m$ , with the codewords j and k of  $C_m$  interchange Then

Image /page/38/Figure/1 description: The image displays four binary trees labeled (a), (b), (c), and (d). Each tree has nodes representing states, and edges are labeled with either '0' or '1'. Leaf nodes are labeled with symbols p1 through p5. Tree (a) shows a root with branches labeled '0' and '1'. The '0' branch leads to another node with '0' and '1' branches. The '0' branch from this node leads to p5, and the '1' branch leads to p1. The '1' branch from the root leads to a node with a '1' branch leading to p2. Tree (b) is identical to tree (a). Tree (c) shows a root with branches labeled '0' and '1'. The '0' branch leads to p2. The '1' branch leads to a node with '0' and '1' branches. The '0' branch from this node leads to p5, and the '1' branch leads to p1. Below this, there is another node with '0' and '1' branches, leading to p3 and p4 respectively. Tree (d) shows a root with branches labeled '0' and '1'. The '0' branch leads to p1. The '1' branch leads to a node with '0' and '1' branches. The '0' branch from this node leads to p2 and p3 respectively. The '1' branch leads to a node with '0' and '1' branches, leading to p4 and p5 respectively.

Figure 5.3. Properties of optimal codes. We will assume that  $p_1 \geq p_2 \geq \cdots \geq p_m$ . A possible instantaneous code is given in  $(a)$ . By trimming branches without siblings, we improve the code to  $(b)$ . We now rearrange the tree as shown in  $(c)$  so that the word lengths are ordered by increasing length from top to bottom. Finally, we swap probability assignments to improve the expected depth of the tree as shown in  $(d)$ . Thus every optimal code can be rearranged and swapped into the canonical form  $(d)$ . Note that  $l_1 \leq l_2 \leq \cdots \leq l_m$ , that  $l_{m-1} = l_m$ , and the last two codewords differ only in the last bit.

$$
L(C'_m) - L(C_m) = \sum p_i l'_i - \sum p_i l_i
$$
 (5.62)

$$
= p_j l_k + p_k l_j - p_j l_j - p_k l_k \tag{5.63}
$$

$$
= (p_j - p_k)(l_k - l_j) \,. \tag{5.64}
$$

But  $p_i - p_k > 0$ , and since  $C_m$  is optimal,  $L(C_m) - L(C_m) \ge 0$ . Hence we must have  $l_k \geq l_i$ . Thus  $C_m$  itself satisfies property 1.

 $\cdot$  The two longest codewords are of the same length. Here we trim the codewords.

If the two longest codewords are not of the same length, then one can delete the last bit of the longer one, preserving the prefix property and achieving lower expected codeword length. Hence the two longest codewords must have the same length. By property 1, the longest codewords must belong to the least probable source symbols.

• The two longest codewords differ only in the last bit and correspond to the two least likely symbols. Not all optimal codes satisfy this property, but by rearranging, we can find a code that does.

If there is a maximal length codeword without a sibling, then we can delete the last bit of the codeword and still satisfy the prefix property. This reduces the average codeword length and contradicts the optimality of the code. Hence every maximal length codeword in any optimal code has a sibling.

Now we can exchange the longest length codewords so the two lowest probability source symbols are associated with two siblings on the tree. This does not change the expected length  $\sum p_i l_i$ . Thus the codewords for the two lowest probability source symbols have maximal length and agree in all but the last bit.

Summarizing, we have shown that if  $p_1 \geq p_2 \geq \cdots \geq p_m$ , then there exists an optimal code with  $l_1 \leq l_2 \leq \cdots \leq l_{m-1} = l_m$ , and codewords  $C(x_{m-1})$  and  $C(x_m)$  that differ only in the last bit.  $\Box$ 

Thus we have shown that there exists an optimal code satisfying the properties of the lemma. We can now restrict our search to codes that satisfy these properties.

For a code  $C_m$  satisfying the properties of the lemma, we now define a "merged" code  $C_{m-1}$  for  $m-1$  symbols as follows: take the common prefix of the two longest codewords (corresponding to the two least likel symbols), and allot it to a symbol with probability  $p_{m-1} + p_m$ . All the other codewords remain the same. The correspondence is shown in the following:

 $C_{m-1}$   $C_m$ 

 $p_1$   $w'_1$   $l'_1$   $w_1 = w'_1$   $l_1 = l'_1$ 

 $p_2$   $w'_2$   $l'_2$   $w_2 = w'_2$   $l_2 = l'_2$ 

 $ext{ extvisiblespace}$   $ext{ extvisiblespace}$   $ext{ extvisiblespace}$   $ext{ extvisiblespace}$   $ext{ extvisiblespace}$ 

 $p_{m-2}$   $w'_{m-2}$   $l'_{m-2}$   $w_{m-2} = w'_{m-2}$   $l_{m-2} = l'_{m-2}$ 

 $p_{m-1} + p_m$   $w'_{m-1}$   $l'_{m-1}$   $w_{m-1} = w'_{m-1} 0$   $l_{m-1} = l'_{m-1} + 1$   $(5.65)$ 

 $w_m = w'_{m-1} 1$   $l_m = l'_{m-1} + 1$ 

where  $w$  denotes a binary codeword and  $l$  denotes its length. The expected length of the code  $C_m$  is

$$
L(C_m) = \sum_{i=1}^{m} p_i l_i
$$
 (5.66)

$$
= \sum_{i=1}^{m-2} p_i l'_i + p_{m-1}(l'_{m-1} + 1) + p_m(l'_{m-1} + 1)
$$
 (5.67)

$$
= \sum_{i=1}^{m-1} p_i l'_i + p_{m-1} + p_m \tag{5.68}
$$

$$
= L(C_{m-1}) + p_{m-1} + p_m. \tag{5.69}
$$

Thus the expected length of the code  $C_m$  differs from the expected length of  $C_{m-1}$  by a fixed amount independent of  $C_{m-1}$ . Thus minimizing the expected length  $L(C_m)$  is equivalent to minimizing  $L(C_{m-1})$ . Thus we have reduced the problem to one with  $m-1$  symbols and probability masses ( $p_1, p_2, \ldots, p_{m-2}, p_{m-1} + p_m$ ). This step is illustrated in Figur  $5.4.$  We again look for a code which satisfies the properties of Lemm 5.8.1 for these  $m-1$  symbols and then reduce the problem to finding the optimal code for  $m - 2$  symbols with the appropriate probability masses obtained by merging the two lowest probabilities on the previous merged list. Proceeding this way, we finally reduce the problem to two symbols, for which the solution is obvious, i.e., allot 0 for one of the symbols and 1 for the other. Since we have maintained optimality at

Image /page/40/Figure/5 description: The image displays three Huffman trees labeled (a), (b), and (c). Tree (a) shows a binary tree with nodes labeled p1 through p5. Edges are labeled with 0s and 1s. Tree (b) is similar to (a) but combines p4 and p5 into a single node labeled "p4 + p5". Tree (c) shows a further reduction step, combining p4 and p5, and then reconfiguring the tree structure. The text below the trees indicates "reduction step for Huffman coding. Intermediate stage. A".

Figure 5.4. Induction step for Huffman coding. Let  $p_1 \geq p_2 \geq \cdots \geq p_5$ . A canonical optimal code is illustrated in  $(a)$ . Combining the two lowest probabilities, we obtain the code in  $(b)$ . Rearranging the probabilities in decreasing order, we obtain the canonical code in (c) for  $m - 1$  symbols.

every stage in the reduction, the code constructed for  $m$  symbols is optimal. Thus we have proved the following theorem for binary alphabets.

**Theorem 5.8.1:** Huffman coding is optimal, i.e., if  $C^*$  is the Huffman code and C' is any other code, then  $L(C^*) \leq L(C')$ .

Although we have proved the theorem for a binary alphabet, the proof can be extended to establishing optimality of the Huffman coding algorithm for a D-ary alphabet as well. Incidentally, we should remark that Huffman coding is a "greedy" algorithm in that it coalesces the two least likely symbols at each stage. The above proof shows that this local optimality ensures a global optimality of the final code.

## 5.9 SHANNON-FANO-ELIAS CODING

In Section 5.4, we showed that the set of lengths  $l(x) = \lceil \log \frac{1}{p(x)} \rceil$  satisfies the Kraft inequality and can therefore be used to construct a uniquely decodable code for the source. In this section, we describe a simple constructive procedure which uses the cumulative distribution function to allot codewords.

Without loss of generality we can take  $\mathcal{X} = \{1, 2, ..., m\}$ . Assume  $p(x) > 0$  for all x. The cumulative distribution function  $F(x)$  is defined as

$$
F(x) = \sum_{\alpha \leq x} p(\alpha) \,. \tag{5.70}
$$

This function is illustrated in Figure 5.5. Consider the modified cumulative distribution function

Image /page/41/Figure/9 description: A cumulative distribution function (CDF) plot is shown. The x-axis is labeled 'x' and ranges from 0 to 4. The y-axis is labeled 'F(x)' and ranges from 0 to 1. The plot is a step function, starting at F(x)=0 for x<1. At x=1, the function jumps to 0.2. At x=2, it jumps to 0.4. At x=3, it jumps to 0.6. At x=4, it jumps to 0.8. Between x=4 and x=5, it jumps to 1. A bracket labeled 'p(x)' indicates the height of the step at x=3, which is 0.2.

Figure 5.5. Cumulative distribution function and Shannon-Fano-Elias coding.

$$
\bar{F}(x) = \sum_{a \le x} p(a) + \frac{1}{2} p(x), \qquad (5.71)
$$

where  $\overline{F}(x)$  denotes the sum of the probabilities of all symbols less than x plus half the probability of the symbol x. Since the random variable is discrete, the cumulative distribution function consists of steps of size  $p(x)$ . The value of the function  $F(x)$  is the midpoint of the step corresponding to  $x$ .

Since all the probabilities are positive,  $F(a) \neq F(b)$  if  $a \neq b$ , and hence we can determine x if we know  $F(x)$ . Merely look at the graph of the cumulative distribution function and find the corresponding  $x$ . Thus the value of  $F(x)$  can be used as a code for x.

But in general  $F(x)$  is a real number expressible only by an infinite number of bits. So it is not efficient to use the exact value of  $F(x)$  as a code for  $x$ . If we use an approximate value, what is the required accuracy?

Assume that we round off  $\bar{F}(x)$  to  $l(x)$  bits (denoted by  $\left[\bar{F}(x)\right]_{l(x)}$ ). Thus we use the first  $l(x)$  bits of  $\bar{F}(x)$  as a code for x. By definition of rounding off, we have

$$
\bar{F}(x) - \left[\bar{F}(x)\right]_{l(x)} < \frac{1}{2^{l(x)}}\,. \tag{5.72}
$$

If  $l(x) = \lceil \log \frac{1}{p(x)} \rceil + 1$ , then

$$
\frac{1}{2^{l(x)}} < \frac{p(x)}{2} = \bar{F}(x) - F(x-1) \,, \tag{5.73}
$$

and therefore  $\left[\bar{F}(x)\right]_{l(x)}$  lies within the step corresponding to x. Thus  $l(x)$ bits suffice to describe  $x$ .

In addition to requiring that the codeword identify the corresponding symbol, we also require the set of codewords to be prefix-free. To check whether the code is prefix-free, we consider each codeword  $z_1z_2 \ldots z_l$  to represent not a point but the interval  $[0. z_1 z_2 ... z_l, 0. z_1 z_2 ... z_l + \frac{1}{2l}].$ The code is prefix-free if and only if the intervals corresponding to codewords are disjoint.

We now verify that the code above is prefix-free. The interval corresponding to any codeword has length  $2^{-\bar{l}(x)}$ , which is less than half the height of the step corresponding to x by  $(5.73)$ . The lower end of the interval is in the lower half of the step. Thus the upper end of the interval lies below the top of the step, and the interval corresponding to any codeword lies entirely within the step corresponding to that symbol in the cumulative distribution function. Therefore the intervals corresponding to different codewords are disjoint and the code is prefix-free.

Note that this procedure does not require the symbols to be ordered

in terms of probability. Another procedure that uses the ordered probabilities is described in Problem 25 at the end of the chapter.

Since we use  $l(x) = \lceil \log \frac{1}{p(x)} \rceil + 1$  bits to represent x, the expected length of this code is

$$
L = \sum_{x} p(x)l(x) = \sum_{x} p(x) \left( \left[ \log \frac{1}{p(x)} \right] + 1 \right) < H(X) + 2. \tag{5.74}
$$

Thus this coding scheme achieves an average codeword length that is within two bits of the entropy.

**Example 5.9.1:** We first consider an example where all the probabilities are dyadic. We construct the code in the following table:

| $\boldsymbol{x}$ | p(x)  | F(x)  | $\bar{F}(x)$ | F(x) in binary | $l(x) = \lfloor \log \frac{1}{p(x)} \rfloor + 1$ | Codeword |
|------------------|-------|-------|--------------|----------------|--------------------------------------------------|----------|
| 1                | 0.25  | 0.25  | 0.125        | 0.001          | 3                                                | 001      |
| 2                | 0.5   | 0.75  | 0.5          | 0.10           | 2                                                | 10       |
| 3                | 0.125 | 0.875 | 0.8125       | 0.1101         | 4                                                | 1101     |
| 4                | 0.125 | 1.0   | 0.9375       | 0.1111         | 4                                                | 1111     |

In this case, the average codeword length is 2.75 bits while the entropy is 1.75 bits. The Huffman code for this case achieves the entropy bound. Looking at the codewords, it is obvious that there is some inefficiency-for example, the last bit of the last two codewords can be omitted. But if we remove the last bit from all the codewords, the code is no longer prefix free.

**Example 5.9.2:** We now give another example for the construction for the Shannon-Fano-Elias code. In this case, since the distribution is not dyadic, the representation of  $F(x)$  in binary may have an infinite number of bits. We denote  $0.01010101...$  by  $0.01$ .

We construct the code in the following table:

| x | p(x) | F(x) | $\bar{F}(x)$ | F(x) in binary          | $l(x) = \left[ \log \frac{1}{p(x)} \right] + 1$ | Codeword |
|---|------|------|--------------|-------------------------|-------------------------------------------------|----------|
| 1 | 0.25 | 0.25 | 0.125        | 0.001                   | 3                                               | 001      |
| 2 | 0.25 | 0.5  | 0.375        | $0.0\overline{11}$      | 3                                               | 011      |
| 3 | 0.2  | 0.7  | 0.6          | $0.10\overline{011}$    | 4                                               | 1001     |
| 4 | 0.15 | 0.85 | 0.775        | $0.1100\overline{0011}$ | 4                                               | 1100     |
| 5 | 0.15 | 1.0  | 0.925        | $0.111\overline{110}$   | 4                                               | 1110     |

The above code is 1.2 bits longer on the average than the Huffman code for this source (Example 5.6.1).

In the next section, we extend the concept of Shannon-Fano-Elias coding and describe a computationally efficient algorithm for encoding and decoding called arithmetic coding.