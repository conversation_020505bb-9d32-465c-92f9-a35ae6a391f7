{"table_of_contents": [{"title": "16 Adaptive basis function models", "heading_level": null, "page_id": 0, "polygon": [[62.71875, 92.232421875], [361.5, 92.232421875], [361.5, 144.59765625], [62.25, 144.59765625]]}, {"title": "16.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 218.00390625], [96.75, 218.00390625]]}, {"title": "16.2 Classification and regression trees (CART)", "heading_level": null, "page_id": 1, "polygon": [[94.5, 162.75], [346.5, 162.75], [346.5, 173.232421875], [94.5, 173.232421875]]}, {"title": "16.2.1 Basics", "heading_level": null, "page_id": 1, "polygon": [[89.25, 246.75], [159.75, 246.75], [159.75, 256.763671875], [89.25, 256.763671875]]}, {"title": "16.2.2 Growing a tree", "heading_level": null, "page_id": 2, "polygon": [[87.75, 475.5], [201.0, 475.5], [201.0, 486.31640625], [87.75, 486.31640625]]}, {"title": "16.2.2.1 Regression cost", "heading_level": null, "page_id": 3, "polygon": [[84.0, 535.5], [198.0, 535.5], [198.0, 545.80078125], [84.0, 545.80078125]]}, {"title": "16.2.2.2 Classification cost", "heading_level": null, "page_id": 4, "polygon": [[83.25, 112.5], [209.25, 112.5], [209.25, 123.1611328125], [83.25, 123.1611328125]]}, {"title": "• Gini index", "heading_level": null, "page_id": 5, "polygon": [[129.75, 275.25], [188.25, 275.25], [188.25, 285.08203125], [129.75, 285.08203125]]}, {"title": "16.2.2.3 Example", "heading_level": null, "page_id": 5, "polygon": [[83.25, 483.0], [168.75, 483.0], [168.75, 492.64453125], [83.25, 492.64453125]]}, {"title": "16.2.3 Pruning a tree", "heading_level": null, "page_id": 6, "polygon": [[87.46875, 446.25], [199.5, 446.25], [199.5, 457.20703125], [87.46875, 457.20703125]]}, {"title": "16.2.4 Pros and cons of trees", "heading_level": null, "page_id": 7, "polygon": [[87.046875, 267.0], [237.515625, 267.0], [237.515625, 277.8046875], [87.046875, 277.8046875]]}, {"title": "16.2.5 Random forests", "heading_level": null, "page_id": 7, "polygon": [[87.75, 444.75], [205.875, 444.75], [205.875, 454.9921875], [87.75, 454.9921875]]}, {"title": "16.2.6 CART compared to hierarchical mixture of experts *", "heading_level": null, "page_id": 8, "polygon": [[87.75, 374.25], [376.5, 374.25], [376.5, 384.43359375], [87.75, 384.43359375]]}, {"title": "16.3 Generalized additive models", "heading_level": null, "page_id": 9, "polygon": [[95.25, 101.25], [276.75, 101.25], [276.75, 112.0869140625], [95.25, 112.0869140625]]}, {"title": "16.3.1 Backfitting", "heading_level": null, "page_id": 9, "polygon": [[89.25, 332.25], [181.6875, 332.25], [181.6875, 342.3515625], [89.25, 342.3515625]]}, {"title": "16.3.2 Computational efficiency", "heading_level": null, "page_id": 10, "polygon": [[87.75, 61.5], [249.0, 61.5], [249.0, 71.54736328125], [87.75, 71.54736328125]]}, {"title": "16.3.3 Multivariate adaptive regression splines (MARS)", "heading_level": null, "page_id": 10, "polygon": [[88.5, 154.5], [354.0, 154.5], [354.0, 164.53125], [88.5, 164.53125]]}, {"title": "16.4 Boosting", "heading_level": null, "page_id": 11, "polygon": [[95.25, 378.0], [175.5, 378.0], [175.5, 389.49609375], [95.25, 389.49609375]]}, {"title": "16.4.1 Forward stagewise additive modeling", "heading_level": null, "page_id": 12, "polygon": [[90.0, 467.25], [306.0, 467.25], [306.0, 477.45703125], [90.0, 477.45703125]]}, {"title": "16.4.2 L2boosting", "heading_level": null, "page_id": 14, "polygon": [[87.9609375, 552.75], [183.0, 552.75], [183.0, 562.88671875], [87.9609375, 562.88671875]]}, {"title": "16.4.3 AdaBoost", "heading_level": null, "page_id": 15, "polygon": [[88.5, 345.75], [175.5, 345.75], [175.5, 356.58984375], [88.5, 356.58984375]]}, {"title": "Algorithm 16.2: Adaboost.M1, for binary classification with exponential loss", "heading_level": null, "page_id": 16, "polygon": [[132.328125, 326.21484375], [439.875, 326.21484375], [439.875, 335.25], [132.328125, 335.25]]}, {"title": "16.4.4 LogitBoost", "heading_level": null, "page_id": 16, "polygon": [[88.453125, 477.75], [180.75, 477.75], [180.75, 487.58203125], [88.453125, 488.84765625]]}, {"title": "16.4.5 Boosting as functional gradient descent", "heading_level": null, "page_id": 17, "polygon": [[88.5, 408.75], [318.75, 408.75], [318.75, 418.921875], [88.5, 418.921875]]}, {"title": "Algorithm 16.4: Gradient boosting", "heading_level": null, "page_id": 18, "polygon": [[134.15625, 242.25], [274.78125, 242.25], [274.78125, 252.4921875], [134.15625, 252.4921875]]}, {"title": "16.4.6 Sparse boosting", "heading_level": null, "page_id": 18, "polygon": [[87.75, 463.5], [205.3125, 463.5], [205.3125, 473.66015625], [87.75, 473.66015625]]}, {"title": "16.4.7 Multivariate adaptive regression trees (MART)", "heading_level": null, "page_id": 19, "polygon": [[89.25, 295.5], [345.0, 295.5], [345.0, 305.6484375], [89.25, 305.6484375]]}, {"title": "16.4.8 Why does boosting work so well?", "heading_level": null, "page_id": 19, "polygon": [[87.46875, 559.5], [287.25, 559.5], [287.25, 569.21484375], [87.46875, 569.21484375]]}, {"title": "16.4.9 A Bayesian view", "heading_level": null, "page_id": 20, "polygon": [[87.75, 207.75], [207.5625, 207.75], [207.5625, 217.845703125], [87.75, 217.845703125]]}, {"title": "16.5 Feedforward neural networks (multilayer perceptrons)", "heading_level": null, "page_id": 20, "polygon": [[95.25, 459.75], [410.25, 459.75], [410.25, 470.8125], [95.25, 470.8125]]}, {"title": "16.5.1 Convolutional neural networks", "heading_level": null, "page_id": 21, "polygon": [[90.0, 486.75], [276.75, 486.75], [276.75, 498.0234375], [90.0, 498.0234375]]}, {"title": "16.5.2 Other kinds of neural networks", "heading_level": null, "page_id": 25, "polygon": [[87.75, 357.75], [279.75, 357.75], [279.75, 368.9296875], [87.75, 368.9296875]]}, {"title": "16.5.3 A brief history of the field", "heading_level": null, "page_id": 25, "polygon": [[88.5, 523.5], [255.0, 523.5], [255.0, 533.4609375], [88.5, 533.4609375]]}, {"title": "16.5.4 The backpropagation algorithm", "heading_level": null, "page_id": 26, "polygon": [[88.5, 495.0], [276.75, 495.0], [276.75, 505.30078125], [88.5, 505.30078125]]}, {"title": "16.5.5 Identifiability", "heading_level": null, "page_id": 29, "polygon": [[87.75, 203.25], [194.625, 203.25], [194.625, 213.75], [87.75, 213.75]]}, {"title": "16.5.6 Regularization", "heading_level": null, "page_id": 29, "polygon": [[87.75, 392.25], [199.5, 392.25], [199.5, 402.46875], [87.75, 402.46875]]}, {"title": "16.5.6.1 Consistent Gaussian priors *", "heading_level": null, "page_id": 30, "polygon": [[84.0, 209.25], [253.5, 209.25], [253.5, 219.26953125], [84.0, 219.26953125]]}, {"title": "16.5.6.2 Weight pruning", "heading_level": null, "page_id": 30, "polygon": [[82.5, 494.25], [198.0, 494.25], [198.0, 504.3515625], [82.5, 504.3515625]]}, {"title": "16.5.6.3 Soft weight sharing*", "heading_level": null, "page_id": 32, "polygon": [[83.25, 351.75], [218.25, 351.75], [218.25, 361.3359375], [83.25, 361.3359375]]}, {"title": "16.5.6.4 Semi-supervised embedding *", "heading_level": null, "page_id": 32, "polygon": [[83.25, 466.5], [259.5, 466.5], [259.5, 476.25], [83.25, 476.25]]}, {"title": "16.5.7 Bayesian inference *", "heading_level": null, "page_id": 33, "polygon": [[89.25, 429.0], [226.5, 429.0], [226.5, 439.171875], [89.25, 439.171875]]}, {"title": "16.5.7.1 Parameter posterior for regression", "heading_level": null, "page_id": 34, "polygon": [[84.75, 157.5], [280.5, 157.5], [280.5, 167.25], [84.75, 167.25]]}, {"title": "16.5.7.2 Parameter posterior for classification", "heading_level": null, "page_id": 35, "polygon": [[83.109375, 61.5], [292.5, 61.5], [292.5, 71.349609375], [83.109375, 71.349609375]]}, {"title": "16.5.7.3 Predictive posterior for regression", "heading_level": null, "page_id": 35, "polygon": [[83.25, 170.25], [279.75, 170.25], [279.75, 179.71875], [83.25, 179.71875]]}, {"title": "16.5.7.4 Predictive posterior for classification", "heading_level": null, "page_id": 35, "polygon": [[84.0, 453.75], [291.0, 453.75], [291.0, 463.8515625], [84.0, 463.8515625]]}, {"title": "16.5.7.5 ARD for neural networks", "heading_level": null, "page_id": 36, "polygon": [[83.8828125, 462.0], [238.5, 462.0], [238.5, 472.078125], [83.8828125, 472.078125]]}, {"title": "16.6 Ensemble learning", "heading_level": null, "page_id": 37, "polygon": [[94.5, 213.0], [227.25, 213.0], [227.25, 223.224609375], [94.5, 223.224609375]]}, {"title": "16.6.1 <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 37, "polygon": [[88.59375, 383.25], [170.25, 383.25], [170.25, 393.609375], [88.59375, 393.609375]]}, {"title": "16.6.2 Error-correcting output codes", "heading_level": null, "page_id": 38, "polygon": [[87.75, 182.25], [270.75, 182.25], [270.75, 192.216796875], [87.75, 192.216796875]]}, {"title": "16.6.3 Ensemble learning is not equivalent to Bayes model averaging", "heading_level": null, "page_id": 38, "polygon": [[87.75, 403.41796875], [424.6875, 403.41796875], [424.6875, 412.91015625], [87.75, 412.91015625]]}, {"title": "16.7 Experimental comparison", "heading_level": null, "page_id": 39, "polygon": [[95.25, 356.25], [263.25, 356.25], [263.25, 367.6640625], [95.25, 367.6640625]]}, {"title": "16.7.1 Low-dimensional features", "heading_level": null, "page_id": 39, "polygon": [[90.0, 489.75], [252.0, 489.75], [252.0, 499.921875], [90.0, 499.921875]]}, {"title": "16.7.2 High-dimensional features", "heading_level": null, "page_id": 40, "polygon": [[88.3828125, 475.5], [255.75, 475.5], [255.75, 485.68359375], [88.3828125, 485.68359375]]}, {"title": "16.8 Interpreting black-box models", "heading_level": null, "page_id": 42, "polygon": [[94.5, 375.0], [286.5, 375.0], [286.5, 386.96484375], [94.5, 386.96484375]]}, {"title": "Exercises", "heading_level": null, "page_id": 44, "polygon": [[129.1640625, 60.75], [178.5, 60.75], [178.5, 72.2197265625], [129.1640625, 72.2197265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 42], ["TextInlineMath", 3], ["Equation", 3], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8792, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 44], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1499, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 44], ["TableCell", 5], ["Text", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Table", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 910, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 52], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 102], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1967, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 57], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 835, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 43], ["Text", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 60], ["Text", 5], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1588, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["Line", 48], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2473, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 57], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 883, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 47], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["TableCell", 50], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Equation", 2], ["TextInlineMath", 2], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4197, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 54], ["Text", 9], ["Equation", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 364], ["Line", 46], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["Line", 38], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 42], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 50], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 44], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["Line", 42], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 57], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 683, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 46], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1468, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 32], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1435, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 43], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 49], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 80], ["Equation", 10], ["Text", 9], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 52], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 52], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 68], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1253, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 54], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 42], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 43], ["Equation", 7], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 37], ["Equation", 8], ["Text", 6], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 65], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 53], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 44], ["TableCell", 30], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4335, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["TableCell", 242], ["Line", 50], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2641, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 42], ["ListItem", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["TableCell", 162], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3517, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 78], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1067, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 64], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 999, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 33], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-20"}