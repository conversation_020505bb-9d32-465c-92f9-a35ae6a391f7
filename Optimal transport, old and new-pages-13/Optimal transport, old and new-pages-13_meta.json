{"table_of_contents": [{"title": "Applications to the Monge problem", "heading_level": null, "page_id": 0, "polygon": [[133.5, 302.994140625], [345.75, 302.994140625], [345.75, 314.595703125], [133.5, 314.595703125]]}, {"title": "Removing the conditions at infinity", "heading_level": null, "page_id": 4, "polygon": [[133.5, 190.5], [344.25, 190.5], [344.25, 201.673828125], [133.5, 201.673828125]]}, {"title": "Removing the assumption of finite cost", "heading_level": null, "page_id": 7, "polygon": [[133.5, 216.75], [366.0, 216.75], [366.0, 227.197265625], [133.5, 227.197265625]]}, {"title": "264 10 Solution of the Monge problem II: Local approach", "heading_level": null, "page_id": 9, "polygon": [[133.5, 26.25], [384.0, 26.25], [384.0, 35.72314453125], [133.5, 35.72314453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 33], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1362, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["Line", 37], ["TextInlineMath", 5], ["Text", 4], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 542], ["Line", 41], ["TextInlineMath", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 707], ["Line", 53], ["TextInlineMath", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 33], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 652], ["Line", 42], ["TextInlineMath", 8], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 51], ["Text", 6], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 34], ["TextInlineMath", 6], ["Text", 6], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 36], ["Text", 5], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 676], ["Line", 43], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 816], ["Line", 61], ["TextInlineMath", 6], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 703], ["Line", 71], ["Text", 8], ["Equation", 8], ["TextInlineMath", 5]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4615, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 765], ["Line", 41], ["Equation", 4], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 39], ["TextInlineMath", 4], ["Equation", 4], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-13"}