{"table_of_contents": [{"title": "10\nBoosting and Additive Trees", "heading_level": null, "page_id": 0, "polygon": [[132.0, 109.5], [371.25, 109.5], [371.25, 163.0986328125], [132.0, 163.0986328125]]}, {"title": "10.1 Boosting Methods", "heading_level": null, "page_id": 0, "polygon": [[133.5, 355.201171875], [285.978515625, 355.201171875], [285.978515625, 367.962890625], [133.5, 367.962890625]]}, {"title": "10.1.1 Outline of This Chapter", "heading_level": null, "page_id": 3, "polygon": [[133.5, 588.0], [300.75, 588.0], [300.75, 600.1875], [133.5, 600.1875]]}, {"title": "10.2 Boosting Fits an Additive Model", "heading_level": null, "page_id": 4, "polygon": [[133.5, 345.75], [377.419921875, 345.75], [377.419921875, 358.875], [133.5, 358.875]]}, {"title": "10.3 Forward Stagewise Additive Modeling", "heading_level": null, "page_id": 5, "polygon": [[132.75, 506.25], [407.25, 506.25], [407.25, 518.9765625], [132.75, 518.9765625]]}, {"title": "10.4 Exponential Loss and AdaBoost", "heading_level": null, "page_id": 6, "polygon": [[133.20263671875, 287.25], [371.25, 287.25], [371.25, 300.673828125], [133.20263671875, 300.673828125]]}, {"title": "344 10. Bo<PERSON>ing and Additive Trees", "heading_level": null, "page_id": 7, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "10.5 Why Exponential Loss?", "heading_level": null, "page_id": 8, "polygon": [[132.75, 442.5], [318.849609375, 442.5], [318.849609375, 457.48828125], [132.75, 457.48828125]]}, {"title": "10.6 Loss Functions and Robustness", "heading_level": null, "page_id": 9, "polygon": [[132.75, 464.25], [366.064453125, 464.25], [366.064453125, 477.2109375], [132.75, 477.2109375]]}, {"title": "Robust Loss Functions for Classification", "heading_level": null, "page_id": 9, "polygon": [[133.5, 537.75], [309.75, 537.75], [309.75, 547.98046875], [133.5, 547.98046875]]}, {"title": "348 10. <PERSON><PERSON>ing and Additive Trees", "heading_level": null, "page_id": 11, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.806640625], [132.0, 98.806640625]]}, {"title": "Robust Loss Functions for Regression", "heading_level": null, "page_id": 12, "polygon": [[133.5, 293.25], [298.23046875, 293.25], [298.23046875, 302.80078125], [133.5, 302.80078125]]}, {"title": "10.7 \"Off-the-Shelf\" Procedures for Data Mining", "heading_level": null, "page_id": 13, "polygon": [[132.75, 468.0], [443.4609375, 468.0], [443.4609375, 483.01171875], [132.75, 483.01171875]]}, {"title": "352 10. Boosting and Additive Trees", "heading_level": null, "page_id": 15, "polygon": [[132.0, 88.5], [302.712890625, 88.5], [302.712890625, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "10.8 Example: Spam Data", "heading_level": null, "page_id": 15, "polygon": [[132.75, 541.5], [305.701171875, 541.5], [305.701171875, 554.5546875], [132.75, 554.5546875]]}, {"title": "10.9 Boosting Trees", "heading_level": null, "page_id": 16, "polygon": [[133.5, 555.0], [264.0, 555.0], [264.0, 568.08984375], [133.5, 568.08984375]]}, {"title": "356 10. Boosting and Additive Trees", "heading_level": null, "page_id": 19, "polygon": [[132.0, 89.25], [302.5634765625, 89.25], [302.5634765625, 98.56494140625], [132.0, 98.56494140625]]}, {"title": "358 10. Bo<PERSON>ing and Additive Trees", "heading_level": null, "page_id": 21, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "10.10 Numerical Optimization via Gradient\nBoosting", "heading_level": null, "page_id": 21, "polygon": [[133.27734375, 208.44140625], [410.888671875, 208.44140625], [410.888671875, 237.83203125], [133.27734375, 237.83203125]]}, {"title": "10.10.1 Steepest Descent", "heading_level": null, "page_id": 21, "polygon": [[133.5, 565.5], [267.75, 565.5], [267.75, 576.984375], [133.5, 576.984375]]}, {"title": "10.10.2 <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 22, "polygon": [[133.5, 260.25], [276.75, 260.25], [276.75, 271.283203125], [133.5, 271.283203125]]}, {"title": "10.10.3 Implementations of Gradient Boosting", "heading_level": null, "page_id": 23, "polygon": [[133.5, 549.0], [378.75, 549.0], [378.75, 559.96875], [133.5, 559.96875]]}, {"title": "Algorithm 10.3 Gradient Tree Boosting Algorithm.", "heading_level": null, "page_id": 24, "polygon": [[133.5, 113.5986328125], [351.0, 113.5986328125], [351.0, 123.0], [133.5, 123.0]]}, {"title": "10.11 Right-Sized <PERSON> for Boosting", "heading_level": null, "page_id": 24, "polygon": [[133.27734375, 601.5], [369.0, 601.5], [369.0, 615.26953125], [133.27734375, 615.26953125]]}, {"title": "362 10. <PERSON><PERSON>ing and Additive Trees", "heading_level": null, "page_id": 25, "polygon": [[132.0, 89.25], [302.4140625, 89.25], [302.4140625, 98.56494140625], [132.0, 98.56494140625]]}, {"title": "10.12 Regularization", "heading_level": null, "page_id": 27, "polygon": [[133.5, 298.5], [269.25, 298.5], [269.25, 312.08203125], [133.5, 312.08203125]]}, {"title": "10.12.1 <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 27, "polygon": [[133.5, 462.0], [232.5, 462.0], [232.5, 474.1171875], [133.5, 474.1171875]]}, {"title": "10.12.2 Subsampling", "heading_level": null, "page_id": 28, "polygon": [[133.5, 441.75], [246.75, 441.75], [246.75, 452.84765625], [133.5, 452.84765625]]}, {"title": "10.13 Interpretation", "heading_level": null, "page_id": 30, "polygon": [[132.6796875, 471.75], [266.5546875, 471.75], [266.5546875, 484.9453125], [132.6796875, 484.9453125]]}, {"title": "10.13.1 Relative Importance of Predictor Variables", "heading_level": null, "page_id": 30, "polygon": [[133.5, 571.5], [406.40625, 571.5], [406.40625, 585.10546875], [133.5, 585.10546875]]}, {"title": "368 10. <PERSON><PERSON>ing and Additive Trees", "heading_level": null, "page_id": 31, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.5166015625], [132.0, 98.5166015625]]}, {"title": "10.13.2 Partial Dependence Plots", "heading_level": null, "page_id": 32, "polygon": [[133.5, 156.0], [312.0, 156.0], [312.0, 167.255859375], [133.5, 167.255859375]]}, {"title": "370 10. Boosting and Additive Trees", "heading_level": null, "page_id": 33, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "10.14 Illustrations", "heading_level": null, "page_id": 34, "polygon": [[133.5, 111.0], [255.0498046875, 111.0], [255.0498046875, 123.36328125], [133.5, 123.36328125]]}, {"title": "10.14.1 California Housing", "heading_level": null, "page_id": 34, "polygon": [[133.4267578125, 186.0], [281.25, 186.0], [281.25, 197.806640625], [133.4267578125, 197.806640625]]}, {"title": "10.14.2 New Zealand Fish", "heading_level": null, "page_id": 38, "polygon": [[132.0, 546.0], [275.25, 546.0], [275.25, 559.96875], [132.0, 559.96875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 31], ["TextInlineMath", 3], ["Text", 2], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6532, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 33], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 50], ["ListItem", 6], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 32], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 39], ["ListItem", 9], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 58], ["Equation", 5], ["ListItem", 4], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 55], ["Equation", 5], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 98], ["Text", 7], ["Equation", 7], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 52], ["Text", 5], ["Equation", 4], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 41], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 735, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 51], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 55], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 32], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["TableCell", 126], ["Line", 45], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 42], ["Text", 6], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 41], ["Text", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1042, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 43], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 58], ["Text", 5], ["Equation", 5], ["TextInlineMath", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 67], ["Text", 6], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 51], ["Equation", 5], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 46], ["TextInlineMath", 6], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 41], ["TableCell", 15], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 43], ["ListItem", 7], ["Text", 3], ["ListGroup", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 50], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 33], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 838, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 34], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 42], ["Text", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 62], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 48], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1178, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 78], ["Text", 5], ["Equation", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 44], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 44], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 39], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 40], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 35], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 64], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 28], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 718, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_356-394"}