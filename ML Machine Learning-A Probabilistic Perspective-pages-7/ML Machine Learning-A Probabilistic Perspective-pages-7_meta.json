{"table_of_contents": [{"title": "5.5 Hierarchical Bayes", "heading_level": null, "page_id": 0, "polygon": [[99.75, 90.0], [226.546875, 90.0], [226.546875, 100.93359375], [99.75, 100.93359375]]}, {"title": "5.5.1 Example: modeling related cancer rates", "heading_level": null, "page_id": 0, "polygon": [[94.5, 237.75], [318.75, 237.75], [318.75, 247.74609375], [94.5, 247.74609375]]}, {"title": "5.6 Empirical Bayes", "heading_level": null, "page_id": 1, "polygon": [[99.0, 427.5], [213.1875, 427.5], [213.1875, 438.5390625], [99.0, 438.5390625]]}, {"title": "5.6.1 Example: beta-binomial model", "heading_level": null, "page_id": 2, "polygon": [[94.5, 311.25], [276.75, 311.25], [276.75, 321.626953125], [94.5, 321.626953125]]}, {"title": "5.6.2 Example: Gaussian-Gaussian model", "heading_level": null, "page_id": 2, "polygon": [[91.8984375, 500.25], [299.25, 500.25], [299.25, 510.6796875], [91.8984375, 510.6796875]]}, {"title": "5.6.2.1 Example: predicting baseball scores", "heading_level": null, "page_id": 3, "polygon": [[88.5, 483.75], [286.5, 483.75], [286.5, 493.59375], [88.5, 493.59375]]}, {"title": "5.6.2.2 Estimating the hyper-parameters", "heading_level": null, "page_id": 4, "polygon": [[87.0, 480.75], [271.96875, 480.75], [271.96875, 490.74609375], [87.0, 490.74609375]]}, {"title": "5.7 Bayesian decision theory", "heading_level": null, "page_id": 5, "polygon": [[100.5, 432.75], [261.0, 432.75], [261.0, 442.65234375], [100.5, 442.65234375]]}, {"title": "5.7.1 <PERSON><PERSON> estimators for common loss functions", "heading_level": null, "page_id": 6, "polygon": [[95.25, 399.75], [339.75, 399.75], [339.75, 409.74609375], [95.25, 409.74609375]]}, {"title": "5.7.1.1 MAP estimate minimizes 0-1 loss", "heading_level": null, "page_id": 6, "polygon": [[90.75, 456.0], [273.0, 456.0], [273.0, 465.43359375], [90.75, 465.43359375]]}, {"title": "5.7.1.2 Reject option", "heading_level": null, "page_id": 7, "polygon": [[88.6640625, 378.75], [188.4375, 378.75], [188.4375, 388.86328125], [88.6640625, 388.86328125]]}, {"title": "5.7.1.3 Posterior mean minimizes \\ell_2 (quadratic) loss", "heading_level": null, "page_id": 8, "polygon": [[88.2421875, 205.5], [326.8125, 205.5], [326.8125, 214.83984375], [88.2421875, 214.83984375]]}, {"title": "5.7.1.4 Posterior median minimizes \\ell_1 (absolute) loss", "heading_level": null, "page_id": 8, "polygon": [[88.875, 479.25], [329.25, 479.25], [329.25, 489.75], [88.875, 489.75]]}, {"title": "5.7.1.5 Supervised learning", "heading_level": null, "page_id": 8, "polygon": [[89.25, 560.25], [217.5, 560.25], [217.5, 569.84765625], [89.25, 569.84765625]]}, {"title": "5.7.2 The false positive vs false negative tradeoff", "heading_level": null, "page_id": 9, "polygon": [[92.953125, 213.75], [336.0, 213.75], [336.0, 223.541015625], [92.953125, 223.541015625]]}, {"title": "5.7.2.1 ROC curves and all that", "heading_level": null, "page_id": 9, "polygon": [[88.6640625, 558.0], [234.0, 558.0], [234.0, 567.94921875], [88.6640625, 567.94921875]]}, {"title": "5.7.2.2 Precision recall curves", "heading_level": null, "page_id": 11, "polygon": [[87.6796875, 331.5], [228.75, 331.5], [228.75, 341.71875], [87.6796875, 341.71875]]}, {"title": "5.7.2.3 F-scores *", "heading_level": null, "page_id": 12, "polygon": [[86.8359375, 249.0], [174.375, 249.0], [174.375, 259.13671875], [86.8359375, 259.13671875]]}, {"title": "5.7.2.4 False discovery rates *", "heading_level": null, "page_id": 13, "polygon": [[87.75, 62.25], [227.25, 62.25], [227.25, 71.31005859375], [87.75, 71.31005859375]]}, {"title": "5.7.3 Other topics *", "heading_level": null, "page_id": 13, "polygon": [[93.0, 372.0], [196.5, 372.0], [196.5, 382.21875], [93.0, 382.21875]]}, {"title": "5.7.3.1 Contextual bandits", "heading_level": null, "page_id": 13, "polygon": [[89.25, 429.0], [212.765625, 429.0], [212.765625, 438.5390625], [89.25, 438.5390625]]}, {"title": "5.7. Bayesian decision theory 185", "heading_level": null, "page_id": 14, "polygon": [[129.75, 30.0], [513.75, 30.0], [513.75, 39.175048828125], [129.75, 39.175048828125]]}, {"title": "5.7.3.2 Utility theory", "heading_level": null, "page_id": 14, "polygon": [[85.921875, 336.75], [189.0, 336.75], [189.0, 346.46484375], [85.921875, 346.46484375]]}, {"title": "5.7.3.3 Sequential decision theory", "heading_level": null, "page_id": 15, "polygon": [[87.75, 62.25], [246.0, 62.25], [246.0, 71.70556640625], [87.75, 71.70556640625]]}, {"title": "Exercises", "heading_level": null, "page_id": 15, "polygon": [[129.75, 156.75], [178.734375, 156.75], [178.734375, 167.0625], [129.75, 167.0625]]}, {"title": "Exercise 5.4 More reject options", "heading_level": null, "page_id": 16, "polygon": [[129.75, 203.25], [249.75, 203.25], [249.75, 212.783203125], [129.75, 212.783203125]]}, {"title": "Exercise 5.5 Newsvendor problem", "heading_level": null, "page_id": 16, "polygon": [[129.65625, 332.25], [255.75, 332.25], [255.75, 341.40234375], [129.65625, 341.40234375]]}, {"title": "Exercise 5.6 Bayes factors and ROC curves", "heading_level": null, "page_id": 16, "polygon": [[129.75, 510.0], [286.5, 510.0], [286.5, 518.90625], [129.75, 518.90625]]}, {"title": "Exercise 5.7 Bayes model averaging helps predictive accuracy", "heading_level": null, "page_id": 16, "polygon": [[129.75, 560.25], [354.0, 560.25], [354.0, 568.58203125], [129.75, 568.58203125]]}, {"title": "(Source: Jaak<PERSON><PERSON>.)", "heading_level": null, "page_id": 17, "polygon": [[129.75, 267.0], [195.75, 267.0], [195.75, 276.064453125], [129.75, 276.064453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 44], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5477, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 60], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1028, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 43], ["TableCell", 26], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4444, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 633], ["Line", 48], ["TextInlineMath", 7], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 590], ["Line", 58], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 48], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 40], ["TableCell", 21], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1019, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 37], ["Text", 3], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 718, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 45], ["Text", 7], ["Equation", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 735, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 42], ["Equation", 7], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["TableCell", 49], ["Line", 41], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7193, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 56], ["TableCell", 21], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TextInlineMath", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4220, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["Line", 53], ["TableCell", 45], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 45], ["TextInlineMath", 4], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 47], ["TableCell", 24], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2], ["Table", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 47], ["TableCell", 24], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListItem", 2], ["Table", 1], ["Equation", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 47], ["TableCell", 27], ["ListItem", 5], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["ListGroup", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 532], ["Line", 44], ["Text", 8], ["Equation", 7], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 11], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-7"}