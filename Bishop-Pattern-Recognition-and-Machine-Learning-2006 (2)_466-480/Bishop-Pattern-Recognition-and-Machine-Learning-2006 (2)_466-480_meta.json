{"table_of_contents": [{"title": "446 9. <PERSON><PERSON><PERSON><PERSON> MODELS AND EM", "heading_level": null, "page_id": 0, "polygon": [[29.25, 40.5], [237.0, 40.5], [237.0, 51.3402099609375], [29.25, 51.3402099609375]]}, {"title": "9.3. An Alternative View of EM 447", "heading_level": null, "page_id": 1, "polygon": [[279.75, 40.5], [473.25, 40.5], [473.25, 51.0150146484375], [279.75, 51.0150146484375]]}, {"title": "9.3.4 EM for Bayesian linear regression", "heading_level": null, "page_id": 2, "polygon": [[137.25, 421.5], [360.75, 420.75], [360.75, 433.4853515625], [137.25, 433.4853515625]]}, {"title": "450 9. MIXTURE MODELS AND EM", "heading_level": null, "page_id": 4, "polygon": [[29.25, 40.5], [237.0, 40.5], [237.0, 51.462158203125], [29.25, 51.462158203125]]}, {"title": "9.4. The EM Algorithm in General", "heading_level": null, "page_id": 4, "polygon": [[86.4404296875, 177.75], [299.49609375, 177.75], [299.49609375, 190.8896484375], [86.4404296875, 190.8896484375]]}, {"title": "9.4. The EM Algorithm in General 451", "heading_level": null, "page_id": 5, "polygon": [[263.3203125, 41.25], [472.5, 41.25], [472.5, 50.8524169921875], [263.3203125, 50.8524169921875]]}, {"title": "452 9. <PERSON><PERSON><PERSON><PERSON> MODELS AND EM", "heading_level": null, "page_id": 6, "polygon": [[29.25, 40.5], [237.0, 40.5], [237.0, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "9.4. The EM Algorithm in General 453", "heading_level": null, "page_id": 7, "polygon": [[264.05859375, 40.5], [473.25, 40.5], [473.25, 50.8524169921875], [264.05859375, 50.8524169921875]]}, {"title": "454 9. <PERSON><PERSON><PERSON><PERSON> MODELS AND EM", "heading_level": null, "page_id": 8, "polygon": [[29.25, 40.5], [237.0, 40.5], [237.0, 51.380859375], [29.25, 51.380859375]]}, {"title": "Exercises", "heading_level": null, "page_id": 9, "polygon": [[31.5, 474.75], [93.0, 474.75], [93.0, 488.1181640625], [31.5, 488.1181640625]]}, {"title": "456 9. <PERSON><PERSON><PERSON><PERSON> MODELS AND EM", "heading_level": null, "page_id": 10, "polygon": [[30.0, 40.5], [237.0, 40.5], [237.0, 51.5028076171875], [30.0, 51.5028076171875]]}, {"title": "458 9. <PERSON><PERSON><PERSON><PERSON> MODELS AND EM", "heading_level": null, "page_id": 12, "polygon": [[30.0, 40.5], [237.0, 40.5], [237.0, 51.5028076171875], [30.0, 51.5028076171875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 68], ["TextInlineMath", 5], ["Equation", 5], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 10201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 46], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 29], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 589, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 68], ["Text", 9], ["Equation", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 52], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 941, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 41], ["TextInlineMath", 6], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1351, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 41], ["Figure", 2], ["TextInlineMath", 2], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["Equation", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4048, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 53], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 51], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 40], ["ListItem", 8], ["Equation", 2], ["ListGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 44], ["ListItem", 7], ["Equation", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 45], ["ListItem", 7], ["Equation", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 11], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_466-480"}