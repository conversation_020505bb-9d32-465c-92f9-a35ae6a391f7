{"table_of_contents": [{"title": "4\nLinear Methods for Classification", "heading_level": null, "page_id": 0, "polygon": [[131.70849609375, 111.0], [408.0, 111.0], [408.0, 161.6484375], [131.70849609375, 161.6484375]]}, {"title": "4.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[132.0, 354.75], [243.0, 354.75], [243.0, 367.576171875], [132.0, 367.576171875]]}, {"title": "102 4. Linear Methods for Classification", "heading_level": null, "page_id": 1, "polygon": [[132.75, 88.5], [316.7578125, 88.5], [316.7578125, 98.75830078125], [132.75, 98.75830078125]]}, {"title": "4.2 Linear Regression of an Indicator Matrix", "heading_level": null, "page_id": 2, "polygon": [[132.0, 389.232421875], [417.75, 389.232421875], [417.75, 401.80078125], [132.0, 401.80078125]]}, {"title": "104 4. Linear Methods for Classification", "heading_level": null, "page_id": 3, "polygon": [[132.0, 88.5], [316.7578125, 88.5], [316.7578125, 98.806640625], [132.0, 98.806640625]]}, {"title": "106 4. Linear Methods for Classification", "heading_level": null, "page_id": 5, "polygon": [[132.0, 88.5], [317.953125, 88.5], [317.953125, 98.37158203125], [132.0, 98.37158203125]]}, {"title": "4.3 Linear Discriminant Analysis", "heading_level": null, "page_id": 5, "polygon": [[132.0, 577.5], [345.146484375, 577.5], [345.146484375, 590.51953125], [132.0, 590.51953125]]}, {"title": "110 4. Linear Methods for Classification", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [316.7578125, 88.5], [316.7578125, 98.806640625], [132.0, 98.806640625]]}, {"title": "112 4. Linear Methods for Classification", "heading_level": null, "page_id": 11, "polygon": [[132.0, 88.5], [317.953125, 88.5], [317.953125, 98.37158203125], [132.0, 98.37158203125]]}, {"title": "4.3.1 Regularized Discriminant Analysis", "heading_level": null, "page_id": 11, "polygon": [[132.75, 338.25], [347.8359375, 338.25], [347.8359375, 349.98046875], [132.75, 349.98046875]]}, {"title": "4.3.2 Computations for LDA", "heading_level": null, "page_id": 12, "polygon": [[132.0, 219.0], [288.8173828125, 219.0], [288.8173828125, 230.291015625], [132.0, 230.291015625]]}, {"title": "4.3.3 Reduced-Rank Linear Discriminant Analysis", "heading_level": null, "page_id": 12, "polygon": [[132.0, 501.0], [398.25, 501.0], [398.25, 512.40234375], [132.0, 512.40234375]]}, {"title": "114 4. Linear Methods for Classification", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [316.5, 89.25], [316.5, 98.806640625], [132.0, 98.806640625]]}, {"title": "LDA and Dimension Reduction on the Vowel Data", "heading_level": null, "page_id": 16, "polygon": [[227.25, 108.75], [387.75, 108.75], [387.75, 118.2392578125], [227.25, 118.2392578125]]}, {"title": "Classification in Reduced Subspace", "heading_level": null, "page_id": 17, "polygon": [[226.810546875, 186.75], [385.5, 186.75], [385.5, 198.0], [226.810546875, 198.0]]}, {"title": "Canonical Coordinate 1", "heading_level": null, "page_id": 17, "polygon": [[267.0, 501.75], [346.5, 501.75], [346.5, 512.40234375], [267.0, 512.40234375]]}, {"title": "4.4 Logistic Regression", "heading_level": null, "page_id": 18, "polygon": [[132.0, 213.75], [283.5, 213.75], [283.5, 227.197265625], [132.0, 227.197265625]]}, {"title": "120 4. Linear Methods for Classification", "heading_level": null, "page_id": 19, "polygon": [[132.0, 88.5], [316.5, 88.5], [316.5, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "4.4.1 Fitting Logistic Regression Models", "heading_level": null, "page_id": 19, "polygon": [[132.0, 112.5], [346.341796875, 112.5], [346.341796875, 123.4599609375], [132.0, 123.4599609375]]}, {"title": "4.4.2 Example: South African Heart Disease", "heading_level": null, "page_id": 21, "polygon": [[132.0, 345.0], [367.55859375, 345.0], [367.55859375, 356.748046875], [132.0, 356.748046875]]}, {"title": "124 4. Linear Methods for Classification", "heading_level": null, "page_id": 23, "polygon": [[132.0, 88.5], [316.5, 88.5], [316.5, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "4.4.3 Quadratic Approximations and Inference", "heading_level": null, "page_id": 23, "polygon": [[132.0, 566.25], [380.70703125, 566.25], [380.70703125, 578.14453125], [132.0, 578.14453125]]}, {"title": "4.4.4 L1 Regularized Logistic Regression", "heading_level": null, "page_id": 24, "polygon": [[132.0, 522.0], [345.75, 522.0], [345.75, 534.05859375], [132.0, 534.05859375]]}, {"title": "126 4. Linear Methods for Classification", "heading_level": null, "page_id": 25, "polygon": [[132.0, 88.5], [316.7578125, 88.5], [316.7578125, 98.61328125], [132.0, 98.61328125]]}, {"title": "4.4.5 Logistic Regression or LDA?", "heading_level": null, "page_id": 26, "polygon": [[132.0, 190.5], [318.75, 190.5], [318.75, 202.25390625], [132.0, 202.25390625]]}, {"title": "128 4. Linear Methods for Classification", "heading_level": null, "page_id": 27, "polygon": [[132.0, 88.5], [316.5, 88.5], [316.5, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "4.5 Separating Hyperplanes", "heading_level": null, "page_id": 28, "polygon": [[132.0, 361.5], [312.275390625, 361.5], [312.275390625, 375.1171875], [132.0, 375.1171875]]}, {"title": "4.5.1 Rosenblatt's Perceptron Learning Algorithm", "heading_level": null, "page_id": 29, "polygon": [[132.0, 607.5], [394.5, 607.5], [394.5, 620.68359375], [132.0, 620.68359375]]}, {"title": "132 4. Linear Methods for Classification", "heading_level": null, "page_id": 31, "polygon": [[132.0, 88.5], [317.35546875, 88.5], [317.35546875, 98.56494140625], [132.0, 98.56494140625]]}, {"title": "4.5.2 Optimal Separating Hyperplanes", "heading_level": null, "page_id": 31, "polygon": [[132.0, 240.75], [335.28515625, 240.75], [335.28515625, 253.107421875], [132.0, 253.107421875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 34, "polygon": [[132.0, 173.7333984375], [255.3486328125, 173.7333984375], [255.3486328125, 187.0751953125], [132.0, 187.0751953125]]}, {"title": "Exercises", "heading_level": null, "page_id": 34, "polygon": [[132.0, 296.25], [190.353515625, 296.25], [190.353515625, 309.181640625], [132.0, 309.181640625]]}, {"title": "136 4. Linear Methods for Classification", "heading_level": null, "page_id": 35, "polygon": [[133.5, 89.25], [317.056640625, 89.25], [317.056640625, 98.7099609375], [133.5, 98.7099609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 26], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6556, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["Line", 51], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1030, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 2114], ["Line", 870], ["TextInlineMath", 3], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2515, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 53], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1534], ["Line", 552], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2376, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1052], ["Line", 370], ["TextInlineMath", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1897, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 917], ["Line", 389], ["TableCell", 33], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 65], ["Text", 4], ["ListItem", 4], ["Equation", 3], ["TextInlineMath", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1171, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 111], ["Text", 3], ["TextInlineMath", 2], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 52], ["TextInlineMath", 5], ["Text", 2], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1970], ["Line", 878], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2500, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 63], ["Text", 5], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 933, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 37], ["ListItem", 4], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 43], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 3194], ["Line", 1431], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3762, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 43], ["Text", 3], ["ListItem", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 55], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 818, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 1033], ["Line", 447], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1610, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 59], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2331, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 533], ["Line", 80], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 60], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["TableCell", 72], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3865, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 14476], ["Line", 5631], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 13822, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["TableCell", 48], ["Line", 42], ["Text", 5], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3279, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 64], ["Text", 5], ["ListItem", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 83], ["Text", 5], ["SectionHeader", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1098, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 65], ["Text", 6], ["Equation", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6648, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 40], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 29], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 588, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 32], ["Text", 3], ["ListItem", 3], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1788, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 65], ["Equation", 4], ["Text", 4], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 51], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 564, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 69], ["Text", 6], ["Equation", 6], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1793, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 61], ["Line", 29], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 49], ["Text", 8], ["Equation", 4], ["TextInlineMath", 3], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 45], ["ListItem", 4], ["Text", 3], ["TextInlineMath", 3], ["ListGroup", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 13], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_120-157"}