{"table_of_contents": [{"title": "546 11. SAMPLING METHODS", "heading_level": null, "page_id": 2, "polygon": [[30.0, 40.5], [210.75, 40.5], [210.75, 51.8280029296875], [30.0, 51.8280029296875]]}, {"title": "11.4. <PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[81.75, 459.0], [213.36328125, 459.0], [213.36328125, 472.833984375], [81.75, 472.833984375]]}, {"title": "11.5. The Hybrid Monte Carlo Algorithm", "heading_level": null, "page_id": 4, "polygon": [[79.48828125, 171.3779296875], [331.5, 171.3779296875], [331.5, 184.7109375], [79.48828125, 184.7109375]]}, {"title": "11.5.1 Dynamical systems", "heading_level": null, "page_id": 4, "polygon": [[138.0, 363.75], [288.75, 363.75], [288.75, 375.275390625], [138.0, 375.275390625]]}, {"title": "<PERSON>\n1805–1865", "heading_level": null, "page_id": 5, "polygon": [[105.328125, 467.25], [204.75, 467.25], [204.75, 491.6953125], [105.328125, 491.6953125]]}, {"title": "550 11. SAMPLING METHODS", "heading_level": null, "page_id": 6, "polygon": [[30.0, 40.5], [210.75, 40.5], [210.75, 51.5841064453125], [30.0, 51.5841064453125]]}, {"title": "11.5.2 Hybrid Monte Carlo", "heading_level": null, "page_id": 8, "polygon": [[138.0, 71.25], [288.75, 71.25], [288.75, 83.2093505859375], [138.0, 83.2093505859375]]}, {"title": "11.6. Estimating the Partition Function", "heading_level": null, "page_id": 10, "polygon": [[81.75, 252.75], [324.84375, 252.75], [324.84375, 266.009765625], [81.75, 266.009765625]]}, {"title": "Exercises<br>11.1 (*) www", "heading_level": null, "page_id": 12, "polygon": [[30.0, 82.5], [168.75, 82.5], [168.75, 104.25], [30.0, 104.25]]}, {"title": "558 11. SAMPLING METHODS", "heading_level": null, "page_id": 14, "polygon": [[28.5, 38.25], [212.25, 38.25], [212.25, 52.275146484375], [28.5, 52.275146484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 51], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2890, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 46], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 40], ["Text", 6], ["SectionHeader", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 650, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 48], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 41], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 60], ["Text", 7], ["Equation", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 88], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2339, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 48], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 47], ["Text", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 53], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1223, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 52], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1560, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 52], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 40], ["ListItem", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 30], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Figure", 1], ["Picture", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1154, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 3], ["SectionHeader", 1], ["ListItem", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_564-578"}