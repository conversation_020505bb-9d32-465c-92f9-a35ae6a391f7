To show that this procedure samples from the required distribution, we first of all note that the distribution  $p(\mathbf{z})$  is an invariant of each of the Gibbs sampling steps individually and hence of the whole Markov chain. This follows from the fact that when we sample from  $p(z_i|\{z_{\setminus i}\})$ , the marginal distribution  $p(z_{\setminus i})$  is clearly invariant because the value of  $\mathbf{z}_{\setminus i}$  is unchanged. Also, each step by definition samples from the correct conditional distribution  $p(z_i|\mathbf{z}_{\setminus i})$ . Because these conditional and marginal distributions together specify the joint distribution, we see that the joint distribution is itself invariant.

The second requirement to be satisfied in order that the <PERSON> sampling procedure samples from the correct distribution is that it be ergodic. A sufficient condition for ergodicity is that none of the conditional distributions be anywhere zero. If this is the case, then any point in  $z$  space can be reached from any other point in a finite number of steps involving one update of each of the component variables. If this requirement is not satisfied, so that some of the conditional distributions have zeros, then ergodicity, if it applies, must be proven explicitly.

The distribution of initial states must also be specified in order to complete the algorithm, although samples drawn after many iterations will effectively become independent of this distribution. Of course, successive samples from the Markov chain will be highly correlated, and so to obtain samples that are nearly independent it will be necessary to subsample the sequence.

We can obtain the Gibbs sampling procedure as a particular instance of the Metropolis-Hastings algorithm as follows. Consider a Metropolis-Hastings sampling step involving the variable  $z_k$  in which the remaining variables  $\mathbf{z}_{\setminus k}$  remain fixed, and for which the transition probability from **z** to  $z^*$  is given by  $q_k(z^*|z) = p(z_k^*|z_k)$ .<br>We note that  $z^* = z_k$ , because these components are unchanged by the sampling We note that  $\mathbf{z}_{\setminus k}^* = \mathbf{z}_{\setminus k}$  because these components are unchanged by the sampling step. Also,  $p(\mathbf{z}) = p(z_k|\mathbf{z}_{\setminus k})p(\mathbf{z}_{\setminus k})$ . Thus the factor that determines the acceptance probability in the Metropolis-Hastings (11.44) is given by

$$
A(\mathbf{z}^{\star}, \mathbf{z}) = \frac{p(\mathbf{z}^{\star})q_k(\mathbf{z}|\mathbf{z}^{\star})}{p(\mathbf{z})q_k(\mathbf{z}^{\star}|\mathbf{z})} = \frac{p(z_k^{\star}|\mathbf{z}_{\backslash k}^{\star})p(\mathbf{z}_{\backslash k}^{\star})p(z_k|\mathbf{z}_{\backslash k}^{\star})}{p(z_k|\mathbf{z}_{\backslash k})p(\mathbf{z}_{\backslash k})p(z_k^{\star}|\mathbf{z}_{\backslash k})} = 1
$$
(11.49)

where we have used  $\mathbf{z}_{\setminus k}^* = \mathbf{z}_{\setminus k}$ . Thus the Metropolis-Hastings steps are always accepted.

As with the Metropolis algorithm, we can gain some insight into the behaviour of Gibbs sampling by investigating its application to a Gaussian distribution. Consider a correlated Gaussian in two variables, as illustrated in Figure 11.11, having conditional distributions of width  $l$  and marginal distributions of width  $L$ . The typical step size is governed by the conditional distributions and will be of order  $l$ . Because the state evolves according to a random walk, the number of steps needed to obtain independent samples from the distribution will be of order  $(L/l)^2$ . Of course if the Gaussian distribution were uncorrelated, then the Gibbs sampling procedure would be optimally efficient. For this simple problem, we could rotate the coordinate system in order to decorrelate the variables. However, in practical applications it will generally be infeasible to find such transformations.

One approach to reducing random walk behaviour in Gibbs sampling is called *over-relaxation* (Adler, 1981). In its original form, this applies to problems for which

**Figure 11.11** Illustration of Gibbs sampling by alternate updates of two variables whose distribution is a correlated Gaussian. The step size is governed by the standard deviation of the conditional distribution (green curve), and is  $O(l)$ , leading to slow progress in the direction of elongation of the joint distribution (red ellipse). The number of steps needed to obtain an independent sample from the distribution is  $O((L/l)^2)$ .

Image /page/1/Figure/2 description: The image displays a 2D plot with axes labeled z1 and z2. An ellipse, colored red, is centered in the plot and oriented diagonally. A blue line traces a path within the ellipse, resembling a random walk. Horizontal dashed green lines extend from the top of the ellipse to the y-axis, and a vertical dashed green line extends from the right side of the ellipse to the x-axis. A horizontal arrow labeled 'L' spans the width of the ellipse. A smaller horizontal arrow labeled 'l' is positioned below the ellipse, indicating a segment on the z1 axis. A green bell curve, representing a probability distribution, is plotted along the z1 axis, centered beneath the vertical dashed green line.

the conditional distributions are Gaussian, which represents a more general class of distributions than the multivariate Gaussian because, for example, the non-Gaussian distribution  $p(z, y) \propto \exp(-z^2 y^2)$  has Gaussian conditional distributions. At each step of the Gibbs sampling algorithm, the conditional distribution for a particular component  $z_i$  has some mean  $\mu_i$  and some variance  $\sigma_i^2$ . In the over-relaxation framework, the value of  $z_i$  is replaced with

$$
z'_{i} = \mu_{i} + \alpha(z_{i} - \mu_{i}) + \sigma_{i}(1 - \alpha_{i}^{2})^{1/2}\nu
$$
 (11.50)

where  $\nu$  is a Gaussian random variable with zero mean and unit variance, and  $\alpha$ is a parameter such that  $-1 < \alpha < 1$ . For  $\alpha = 0$ , the method is equivalent to standard Gibbs sampling, and for  $\alpha < 0$  the step is biased to the opposite side of the mean. This step leaves the desired distribution invariant because if  $z_i$  has mean  $\mu_i$ and variance  $\sigma_i^2$ , then so too does  $z_i'$ . The effect of over-relaxation is to encourage directed motion through state space when the variables are highly correlated. The framework of *ordered over-relaxation* (Neal, 1999) generalizes this approach to non-Gaussian distributions.

The practical applicability of Gibbs sampling depends on the ease with which samples can be drawn from the conditional distributions  $p(z_k|\mathbf{z}\rangle_k)$ . In the case of probability distributions specified using graphical models, the conditional distributions for individual nodes depend only on the variables in the corresponding Markov blankets, as illustrated in Figure 11.12. For directed graphs, a wide choice of conditional distributions for the individual nodes conditioned on their parents will lead to conditional distributions for Gibbs sampling that are log concave. The adaptive rejection sampling methods discussed in Section 11.1.3 therefore provide a framework for Monte Carlo sampling from directed graphs with broad applicability.

If the graph is constructed using distributions from the exponential family, and if the parent-child relationships preserve conjugacy, then the full conditional distributions arising in Gibbs sampling will have the same functional form as the orig-

# **546 11. SAMPLING METHODS**

**Figure 11.12** The Gibbs sampling method requires samples to be drawn from the conditional distribution of a variable conditioned on the remaining variables. For graphical models, this conditional distribution is a function only of the states of the nodes in the Markov blanket. For an undirected graph this comprises the set of neighbours, as shown on the left, while for a directed graph the Markov blanket comprises the parents, the children, and the co-parents, as shown on the right.

Image /page/2/Picture/2 description: The image displays two diagrams composed of circles and lines, representing nodes and edges in a graph. Both diagrams feature a central white circle surrounded by blue circles. The diagram on the left shows a central white circle connected to four blue circles arranged in a cross shape. The diagram on the right depicts a central white circle with arrows pointing from two upper blue circles to it, and arrows pointing from the white circle to two lower blue circles. Additionally, there is a blue circle to the right of the central white circle with an arrow pointing from it to a blue circle below it. The lines and arrows are red, and the circles have red outlines.

inal conditional distributions (conditioned on the parents) defining each node, and so standard sampling techniques can be employed. In general, the full conditional distributions will be of a complex form that does not permit the use of standard sampling algorithms. However, if these conditionals are log concave, then sampling can be done efficiently using adaptive rejection sampling (assuming the corresponding variable is a scalar).

If, at each stage of the Gibbs sampling algorithm, instead of drawing a sample from the corresponding conditional distribution, we make a point estimate of the variable given by the maximum of the conditional distribution, then we obtain the iterated conditional modes (ICM) algorithm discussed in Section 8.3.3. Thus ICM can be seen as a greedy approximation to Gibbs sampling.

Because the basic Gibbs sampling technique considers one variable at a time, there are strong dependencies between successive samples. At the opposite extreme, if we could draw samples directly from the joint distribution (an operation that we are supposing is intractable), then successive samples would be independent. We can hope to improve on the simple Gibbs sampler by adopting an intermediate strategy in which we sample successively from groups of variables rather than individual variables. This is achieved in the *blocking Gibbs* sampling algorithm by choosing blocks of variables, not necessarily disjoint, and then sampling jointly from the variables in each block in turn, conditioned on the remaining variables (Jensen *et al.*, 1995).

## **11.4. Slice Sampling**

We have seen that one of the difficulties with the Metropolis algorithm is the sensitivity to step size. If this is too small, the result is slow decorrelation due to random walk behaviour, whereas if it is too large the result is inefficiency due to a high rejection rate. The technique of *slice sampling* (Neal, 2003) provides an adaptive step size that is automatically adjusted to match the characteristics of the distribution. Again it requires that we are able to evaluate the unnormalized distribution  $\tilde{p}(\mathbf{z})$ .

Consider first the univariate case. Slice sampling involves augmenting  $z$  with an additional variable u and then drawing samples from the joint  $(z, u)$  space. We shall see another example of this approach when we discuss hybrid Monte Carlo in Section 11.5. The goal is to sample uniformly from the area under the distribution

Image /page/3/Figure/1 description: The image displays two plots, labeled (a) and (b), illustrating a function p~(z) against z. Both plots show a red curve representing the function, which has a bimodal shape with two peaks. In plot (a), a vertical black line is drawn at z(τ), and a horizontal dashed blue line labeled 'u' intersects the first peak of the curve. In plot (b), a similar vertical black line is at z(τ), and a horizontal blue line labeled 'u' is shown. This blue line is bracketed by 'zmin' on the left and 'zmax' on the right, indicating a range around z(τ) where the function's value is at least 'u'.

**Figure 11.13** Illustration of slice sampling. (a) For a given value  $z^{(\tau)}$ , a value of u is chosen uniformly in the region  $0 \leq u \leq \widetilde{p}(z^{(\tau)})$ , which then defines a 'slice' through the distribution, shown by the solid horizontal lines. (b) Because it is infeasible to sample directly from a slice, a new sample of z is drawn from a region  $z_{\rm min} \leqslant z \leqslant z_{\rm max}$ , which contains the previous value  $z^{(\tau)}$ .

given by

$$
\widehat{p}(z,u) = \begin{cases} 1/Z_p & \text{if } 0 \leq u \leq \widetilde{p}(z) \\ 0 & \text{otherwise} \end{cases}
$$
\n(11.51)

where  $Z_p = \int \widetilde{p}(z) dz$ . The marginal distribution over z is given by

$$
\int \widehat{p}(z, u) du = \int_0^{\widetilde{p}(z)} \frac{1}{Z_p} du = \frac{\widetilde{p}(z)}{Z_p} = p(z)
$$
\n(11.52)

and so we can sample from  $p(z)$  by sampling from  $\hat{p}(z, u)$  and then ignoring the u values. This can be achieved by alternately sampling  $z$  and  $u$ . Given the value of  $z$ we evaluate  $\tilde{p}(z)$  and then sample u uniformly in the range  $0 \leq u \leq \tilde{p}(z)$ , which is straightforward. Then we fix  $u$  and sample  $z$  uniformly from the 'slice' through the distribution defined by  $\{z : \tilde{p}(z) > u\}$ . This is illustrated in Figure 11.13(a).

In practice, it can be difficult to sample directly from a slice through the distribution and so instead we define a sampling scheme that leaves the uniform distribution under  $\hat{p}(z, u)$  invariant, which can be achieved by ensuring that detailed balance is satisfied. Suppose the current value of z is denoted  $z^{(\tau)}$  and that we have obtained a corresponding sample  $u$ . The next value of  $z$  is obtained by considering a region  $z_{\text{min}} \leq z \leq z_{\text{max}}$  that contains  $z^{(\tau)}$ . It is in the choice of this region that the adaptation to the characteristic length scales of the distribution takes place. We want the region to encompass as much of the slice as possible so as to allow large moves in  $z$ space while having as little as possible of this region lying outside the slice, because this makes the sampling less efficient.

One approach to the choice of region involves starting with a region containing  $z^{(\tau)}$  having some width w and then testing each of the end points to see if they lie within the slice. If either end point does not, then the region is extended in that direction by increments of value  $w$  until the end point lies outside the region. A candidate value  $z'$  is then chosen uniformly from this region, and if it lies within the slice, then it forms  $z^{(\tau+1)}$ . If it lies outside the slice, then the region is shrunk such that z' forms an end point and such that the region still contains  $z^{(\tau)}$ . Then another

candidate point is drawn uniformly from this reduced region and so on, until a value of z is found that lies within the slice.

Slice sampling can be applied to multivariate distributions by repeatedly sampling each variable in turn, in the manner of Gibbs sampling. This requires that we are able to compute, for each component  $z_i$ , a function that is proportional to  $p(z_i|\mathbf{z}_{\setminus i}).$ 

## **11.5. The Hybrid Monte Carlo Algorithm**

As we have already noted, one of the major limitations of the Metropolis algorithm is that it can exhibit random walk behaviour whereby the distance traversed through the state space grows only as the square root of the number of steps. The problem cannot be resolved simply by taking bigger steps as this leads to a high rejection rate.

In this section, we introduce a more sophisticated class of transitions based on an analogy with physical systems and that has the property of being able to make large changes to the system state while keeping the rejection probability small. It is applicable to distributions over continuous variables for which we can readily evaluate the gradient of the log probability with respect to the state variables. We will discuss the dynamical systems framework in Section 11.5.1, and then in Section 11.5.2 we explain how this may be combined with the Metropolis algorithm to yield the powerful hybrid Monte Carlo algorithm. A background in physics is not required as this section is self-contained and the key results are all derived from first principles.

### **11.5.1 Dynamical systems**

The dynamical approach to stochastic sampling has its origins in algorithms for simulating the behaviour of physical systems evolving under Hamiltonian dynamics. In a Markov chain Monte Carlo simulation, the goal is to sample from a given probability distribution  $p(z)$ . The framework of *Hamiltonian dynamics* is exploited by casting the probabilistic simulation in the form of a Hamiltonian system. In order to remain in keeping with the literature in this area, we make use of the relevant dynamical systems terminology where appropriate, which will be defined as we go along.

The dynamics that we consider corresponds to the evolution of the state variable  $z = \{z_i\}$  under continuous time, which we denote by  $\tau$ . Classical dynamics is described by Newton's second law of motion in which the acceleration of an object is proportional to the applied force, corresponding to a second-order differential equation over time. We can decompose a second-order equation into two coupled firstorder equations by introducing intermediate *momentum* variables **r**, corresponding to the rate of change of the state variables **z**, having components

$$
r_i = \frac{\mathrm{d}z_i}{\mathrm{d}\tau} \tag{11.53}
$$

where the  $z_i$  can be regarded as *position* variables in this dynamics perspective. Thus

for each position variable there is a corresponding momentum variable, and the joint space of position and momentum variables is called *phase space*.

Without loss of generality, we can write the probability distribution  $p(\mathbf{z})$  in the form

$$
p(\mathbf{z}) = \frac{1}{Z_p} \exp(-E(\mathbf{z}))
$$
\n(11.54)

where <sup>E</sup>(**z**) is interpreted as the *potential energy* of the system when in state **z**. The system acceleration is the rate of change of momentum and is given by the applied *force*, which itself is the negative gradient of the potential energy

$$
\frac{\mathrm{d}r_i}{\mathrm{d}\tau} = -\frac{\partial E(\mathbf{z})}{\partial z_i}.\tag{11.55}
$$

It is convenient to reformulate this dynamical system using the Hamiltonian framework. To do this, we first define the *kinetic energy* by

$$
K(\mathbf{r}) = \frac{1}{2} ||\mathbf{r}||^2 = \frac{1}{2} \sum_{i} r_i^2.
$$
 (11.56)

The total energy of the system is then the sum of its potential and kinetic energies

$$
H(\mathbf{z}, \mathbf{r}) = E(\mathbf{z}) + K(\mathbf{r})
$$
\n(11.57)

where H is the *Hamiltonian* function. Using (11.53), (11.55), (11.56), and (11.57), we can now express the dynamics of the system in terms of the Hamiltonian equa-*Exercise 11.15* tions given by

$$
\frac{\mathrm{d}z_i}{\mathrm{d}\tau} = \frac{\partial H}{\partial r_i} \tag{11.58}
$$

$$
\frac{\mathrm{d}r_i}{\mathrm{d}\tau} = -\frac{\partial H}{\partial z_i}.\tag{11.59}
$$

Image /page/5/Picture/13 description: A black and white portrait of an older man with a receding hairline and a high collar with a cravat. He is looking slightly to the left of the camera. The image is framed by a light purple border.

### William Hamilton 1805 –1865

William Rowan Hamilton was an Irish mathematician and physicist, and child prodigy, who was appointed Professor of Astronomy at Trinity College, Dublin, in 1827, before he had even graduated. One

of Hamilton's most important contributions was a new formulation of dynamics, which played a significant role in the later development of quantum mechanics.

His other great achievement was the development of quaternions, which generalize the concept of complex numbers by introducing three distinct square roots of minus one, which satisfy  $i^2 = j^2 = k^2 = ijk = -1$ . It is said that these equations occurred to him while walking along the Royal Canal in Dublin with his wife, on 16 October 1843, and he promptly carved the equations into the side of Broome bridge. Although there is no longer any evidence of the carving, there is now a stone plaque on the bridge commemorating the discovery and displaying the quaternion equations.

# **550 11. SAMPLING METHODS**

During the evolution of this dynamical system, the value of the Hamiltonian  $H$  is constant, as is easily seen by differentiation

$$
\frac{dH}{d\tau} = \sum_{i} \left\{ \frac{\partial H}{\partial z_i} \frac{dz_i}{d\tau} + \frac{\partial H}{\partial r_i} \frac{dr_i}{d\tau} \right\}
$$

$$
= \sum_{i} \left\{ \frac{\partial H}{\partial z_i} \frac{\partial H}{\partial r_i} - \frac{\partial H}{\partial r_i} \frac{\partial H}{\partial z_i} \right\} = 0.
$$
(11.60)

A second important property of Hamiltonian dynamical systems, known as *Liouville's Theorem*, is that they preserve volume in phase space. In other words, if we consider a region within the space of variables  $(z, r)$ , then as this region evolves under the equations of Hamiltonian dynamics, its shape may change but its volume will not. This can be seen by noting that the flow field (rate of change of location in phase space) is given by

$$
\mathbf{V} = \left(\frac{\mathrm{d}\mathbf{z}}{\mathrm{d}\tau}, \frac{\mathrm{d}\mathbf{r}}{\mathrm{d}\tau}\right) \tag{11.61}
$$

and that the divergence of this field vanishes

$$
\operatorname{div} \mathbf{V} = \sum_{i} \left\{ \frac{\partial}{\partial z_{i}} \frac{\mathrm{d}z_{i}}{\mathrm{d}\tau} + \frac{\partial}{\partial r_{i}} \frac{\mathrm{d}r_{i}}{\mathrm{d}\tau} \right\}
$$

$$
= \sum_{i} \left\{ -\frac{\partial}{\partial z_{i}} \frac{\partial H}{\partial r_{i}} + \frac{\partial}{\partial r_{i}} \frac{\partial H}{\partial z_{i}} \right\} = 0. \quad (11.62)
$$

Now consider the joint distribution over phase space whose total energy is the Hamiltonian, i.e., the distribution given by

$$
p(\mathbf{z}, \mathbf{r}) = \frac{1}{Z_H} \exp(-H(\mathbf{z}, \mathbf{r})).
$$
 (11.63)

Using the two results of conservation of volume and conservation of  $H$ , it follows that the Hamiltonian dynamics will leave  $p(z, r)$  invariant. This can be seen by considering a small region of phase space over which  $H$  is approximately constant. If we follow the evolution of the Hamiltonian equations for a finite time, then the volume of this region will remain unchanged as will the value of  $H$  in this region, and hence the probability density, which is a function only of  $H$ , will also be unchanged.

Although  $H$  is invariant, the values of  $z$  and  $r$  will vary, and so by integrating the Hamiltonian dynamics over a finite time duration it becomes possible to make large changes to **z** in a systematic way that avoids random walk behaviour.

Evolution under the Hamiltonian dynamics will not, however, sample ergodically from  $p(\mathbf{z}, \mathbf{r})$  because the value of H is constant. In order to arrive at an ergodic sampling scheme, we can introduce additional moves in phase space that change the value of H while also leaving the distribution  $p(\mathbf{z}, \mathbf{r})$  invariant. The simplest way to achieve this is to replace the value of **r** with one drawn from its distribution conditioned on **z**. This can be regarded as a Gibbs sampling step, and hence from

Section 11.3 we see that this also leaves the desired distribution invariant. Noting that **z** and **r** are independent in the distribution  $p(\mathbf{z}, \mathbf{r})$ , we see that the conditional *Exercise 11.16* distribution  $p(\mathbf{r}|\mathbf{z})$  is a Gaussian from which it is straightforward to sample.

> In a practical application of this approach, we have to address the problem of performing a numerical integration of the Hamiltonian equations. This will necessarily introduce numerical errors and so we should devise a scheme that minimizes the impact of such errors. In fact, it turns out that integration schemes can be devised for which Liouville's theorem still holds exactly. This property will be important in the hybrid Monte Carlo algorithm, which is discussed in Section 11.5.2. One scheme for achieving this is called the *leapfrog* discretization and involves alternately updating discrete-time approximations  $\hat{z}$  and  $\hat{r}$  to the position and momentum variables using

$$
\widehat{r}_i(\tau + \epsilon/2) = \widehat{r}_i(\tau) - \frac{\epsilon}{2} \frac{\partial E}{\partial z_i}(\widehat{\mathbf{z}}(\tau)) \tag{11.64}
$$

$$
\widehat{z}_i(\tau + \epsilon) = \widehat{z}_i(\tau) + \epsilon \widehat{r}_i(\tau + \epsilon/2)
$$
\n(11.65)

$$
\widehat{r}_i(\tau + \epsilon) = \widehat{r}_i(\tau + \epsilon/2) - \frac{\epsilon}{2} \frac{\partial E}{\partial z_i} (\widehat{\mathbf{z}}(\tau + \epsilon)). \tag{11.66}
$$

We see that this takes the form of a half-step update of the momentum variables with step size  $\epsilon/2$ , followed by a full-step update of the position variables with step size  $\epsilon$ , followed by a second half-step update of the momentum variables. If several leapfrog steps are applied in succession, it can be seen that half-step updates to the momentum variables can be combined into full-step updates with step size  $\epsilon$ . The successive updates to position and momentum variables then leapfrog over each other. In order to advance the dynamics by a time interval  $\tau$ , we need to take  $\tau/\epsilon$  steps. The error involved in the discretized approximation to the continuous time dynamics will go to zero, assuming a smooth function  $E(z)$ , in the limit  $\epsilon \to 0$ . However, for a nonzero  $\epsilon$  as used in practice, some residual error will remain. We shall see in Section 11.5.2 how the effects of such errors can be eliminated in the hybrid Monte Carlo algorithm.

In summary then, the Hamiltonian dynamical approach involves alternating between a series of leapfrog updates and a resampling of the momentum variables from their marginal distribution.

Note that the Hamiltonian dynamics method, unlike the basic Metropolis algorithm, is able to make use of information about the gradient of the log probability distribution as well as about the distribution itself. An analogous situation is familiar from the domain of function optimization. In most cases where gradient information is available, it is highly advantageous to make use of it. Informally, this follows from the fact that in a space of dimension  $D$ , the additional computational cost of evaluating a gradient compared with evaluating the function itself will typically be a fixed factor independent of  $D$ , whereas the  $D$ -dimensional gradient vector conveys D pieces of information compared with the one piece of information given by the function itself.

*Exercise 11.16*

### **11.5.2 Hybrid Monte Carlo**

As we discussed in the previous section, for a nonzero step size  $\epsilon$ , the discretization of the leapfrog algorithm will introduce errors into the integration of the Hamiltonian dynamical equations. *Hybrid Monte Carlo* (Duane *et al.*, 1987; Neal, 1996) combines Hamiltonian dynamics with the Metropolis algorithm and thereby removes any bias associated with the discretization.

Specifically, the algorithm uses a Markov chain consisting of alternate stochastic updates of the momentum variable **r** and Hamiltonian dynamical updates using the leapfrog algorithm. After each application of the leapfrog algorithm, the resulting candidate state is accepted or rejected according to the Metropolis criterion based on the value of the Hamiltonian H. Thus if  $(z, r)$  is the initial state and  $(z^*, r^*)$ is the state after the leapfrog integration, then this candidate state is accepted with probability

$$
\min\left(1, \exp\{H(\mathbf{z}, \mathbf{r}) - H(\mathbf{z}^*, \mathbf{r}^*)\}\right). \tag{11.67}
$$

If the leapfrog integration were to simulate the Hamiltonian dynamics perfectly, then every such candidate step would automatically be accepted because the value of  $H$  would be unchanged. Due to numerical errors, the value of  $H$  may sometimes decrease, and we would like the Metropolis criterion to remove any bias due to this effect and ensure that the resulting samples are indeed drawn from the required distribution. In order for this to be the case, we need to ensure that the update equations corresponding to the leapfrog integration satisfy detailed balance (11.40). This is easily achieved by modifying the leapfrog scheme as follows.

Before the start of each leapfrog integration sequence, we choose at random, with equal probability, whether to integrate forwards in time (using step size  $\epsilon$ ) or backwards in time (using step size  $-\epsilon$ ). We first note that the leapfrog integration scheme  $(11.64)$ ,  $(11.65)$ , and  $(11.66)$  is time-reversible, so that integration for L steps using step size  $-\epsilon$  will exactly undo the effect of integration for L steps using step size  $\epsilon$ . Next we show that the leapfrog integration preserves phase-space volume exactly. This follows from the fact that each step in the leapfrog scheme updates either a  $z_i$  variable or an  $r_i$  variable by an amount that is a function only of the other variable. As shown in Figure 11.14, this has the effect of shearing a region of phase space while not altering its volume.

Finally, we use these results to show that detailed balance holds. Consider a small region  $\mathcal R$  of phase space that, under a sequence of  $L$  leapfrog iterations of step size  $\epsilon$ , maps to a region  $\mathcal{R}'$ . Using conservation of volume under the leapfrog iteration, we see that if R has volume  $\delta V$  then so too will R'. If we choose an initial point from the distribution (11.63) and then update it using  $L$  leapfrog interactions, the probability of the transition going from  $\mathcal R$  to  $\mathcal R'$  is given by

$$
\frac{1}{Z_H} \exp(-H(\mathcal{R})) \delta V_{\frac{1}{2}} \min\left\{1, \exp(-H(\mathcal{R}) + H(\mathcal{R}'))\right\}.
$$
 (11.68)

where the factor of  $1/2$  arises from the probability of choosing to integrate with a positive step size rather than a negative one. Similarly, the probability of starting in

Image /page/9/Figure/1 description: The image displays two graphs side-by-side. The left graph is a rectangle with a blue shaded vertical bar in the center. The x-axis is labeled "zi" and the y-axis is labeled "ri". The right graph is similar, but the top and bottom edges of the rectangle are curved, creating a wavy effect. The x-axis is labeled "zi'" and the y-axis is labeled "ri'". Both graphs depict a region bounded by red lines, with a blue shaded area within that region.

**Figure 11.14** Each step of the leapfrog algorithm (11.64)–(11.66) modifies either a position variable  $z_i$  or a momentum variable  $r_i$ . Because the change to one variable is a function only of the other, any region in phase space will be sheared without change of volume.

region  $\mathcal{R}'$  and integrating backwards in time to end up in region  $\mathcal{R}$  is given by

$$
\frac{1}{Z_H} \exp(-H(\mathcal{R}')) \delta V_{\frac{1}{2}} \min\left\{1, \exp(-H(\mathcal{R}') + H(\mathcal{R}))\right\}.
$$
 (11.69)

It is easily seen that the two probabilities (11.68) and (11.69) are equal, and hence *Exercise 11.17* detailed balance holds. Note that this proof ignores any overlap between the regions  $\mathcal R$  and  $\mathcal R'$  but is easily generalized to allow for such overlap.

> It is not difficult to construct examples for which the leapfrog algorithm returns to its starting position after a finite number of iterations. In such cases, the random replacement of the momentum values before each leapfrog integration will not be sufficient to ensure ergodicity because the position variables will never be updated. Such phenomena are easily avoided by choosing the magnitude of the step size at random from some small interval, before each leapfrog integration.

> We can gain some insight into the behaviour of the hybrid Monte Carlo algorithm by considering its application to a multivariate Gaussian. For convenience, consider a Gaussian distribution  $p(z)$  with independent components, for which the Hamiltonian is given by

$$
H(\mathbf{z}, \mathbf{r}) = \frac{1}{2} \sum_{i} \frac{1}{\sigma_i^2} z_i^2 + \frac{1}{2} \sum_{i} r_i^2.
$$
 (11.70)

Our conclusions will be equally valid for a Gaussian distribution having correlated components because the hybrid Monte Carlo algorithm exhibits rotational isotropy. During the leapfrog integration, each pair of phase-space variables  $z_i$ ,  $r_i$  evolves independently. However, the acceptance or rejection of the candidate point is based on the value of  $H$ , which depends on the values of all of the variables. Thus, a significant integration error in any one of the variables could lead to a high probability of rejection. In order that the discrete leapfrog integration be a reasonably

*Exercise 11.17*

good approximation to the true continuous-time dynamics, it is necessary for the leapfrog integration scale  $\epsilon$  to be smaller than the shortest length-scale over which the potential is varying significantly. This is governed by the smallest value of  $\sigma_i$ , which we denote by  $\sigma_{\min}$ . Recall that the goal of the leapfrog integration in hybrid Monte Carlo is to move a substantial distance through phase space to a new state that is relatively independent of the initial state and still achieve a high probability of acceptance. In order to achieve this, the leapfrog integration must be continued for a number of iterations of order  $\sigma_{\text{max}}/\sigma_{\text{min}}$ .

By contrast, consider the behaviour of a simple Metropolis algorithm with an isotropic Gaussian proposal distribution of variance  $s<sup>2</sup>$ , considered earlier. In order to avoid high rejection rates, the value of s must be of order  $\sigma_{\min}$ . The exploration of state space then proceeds by a random walk and takes of order  $(\sigma_{\text{max}}/\sigma_{\text{min}})^2$  steps to arrive at a roughly independent state.

## **11.6. Estimating the Partition Function**

As we have seen, most of the sampling algorithms considered in this chapter require only the functional form of the probability distribution up to a multiplicative constant. Thus if we write

$$
p_E(\mathbf{z}) = \frac{1}{Z_E} \exp(-E(\mathbf{z}))
$$
\n(11.71)

then the value of the normalization constant  $Z_E$ , also known as the partition function, is not needed in order to draw samples from  $p(z)$ . However, knowledge of the value of  $Z_E$  can be useful for Bayesian model comparison since it represents the model evidence (i.e., the probability of the observed data given the model), and so it is of interest to consider how its value might be obtained. We assume that direct evaluation by summing, or integrating, the function  $\exp(-E(\mathbf{z}))$  over the state space of **z** is intractable.

For model comparison, it is actually the ratio of the partition functions for two models that is required. Multiplication of this ratio by the ratio of prior probabilities gives the ratio of posterior probabilities, which can then be used for model selection or model averaging.

One way to estimate a ratio of partition functions is to use importance sampling from a distribution with energy function  $G(\mathbf{z})$ 

$$
\frac{Z_E}{Z_G} = \frac{\sum_{\mathbf{z}} \exp(-E(\mathbf{z}))}{\sum_{\mathbf{z}} \exp(-G(\mathbf{z}))}
$$

$$
= \frac{\sum_{\mathbf{z}} \exp(-E(\mathbf{z}) + G(\mathbf{z})) \exp(-G(\mathbf{z}))}{\sum_{\mathbf{z}} \exp(-G(\mathbf{z}))}
$$

$$
= \mathbb{E}_{G(\mathbf{z})}[\exp(-E + G)]
$$

$$
\approx \sum_{l} \exp(-E(\mathbf{z}^{(l)}) + G(\mathbf{z}^{(l)})) \qquad (11.72)
$$

where  $\{\mathbf{z}^{(l)}\}$  are samples drawn from the distribution defined by  $p_G(\mathbf{z})$ . If the distribution  $p_G$  is one for which the partition function can be evaluated analytically for tribution  $p<sub>G</sub>$  is one for which the partition function can be evaluated analytically, for example a Gaussian, then the absolute value of  $Z_E$  can be obtained.

This approach will only yield accurate results if the importance sampling distribution  $p_G$  is closely matched to the distribution  $p_E$ , so that the ratio  $p_E/p_G$  does not have wide variations. In practice, suitable analytically specified importance sampling distributions cannot readily be found for the kinds of complex models considered in this book.

An alternative approach is therefore to use the samples obtained from a Markov chain to define the importance-sampling distribution. If the transition probability for the Markov chain is given by  $T(\mathbf{z}, \mathbf{z}')$ , and the sample set is given by  $\mathbf{z}^{(1)}, \ldots, \mathbf{z}^{(L)}$ , then the sampling distribution can be written as then the sampling distribution can be written as

$$
\frac{1}{Z_G} \exp(-G(\mathbf{z})) = \sum_{l=1}^{L} T(\mathbf{z}^{(l)}, \mathbf{z})
$$
\n(11.73)

which can be used directly in  $(11.72)$ .

Methods for estimating the ratio of two partition functions require for their success that the two corresponding distributions be reasonably closely matched. This is especially problematic if we wish to find the absolute value of the partition function for a complex distribution because it is only for relatively simple distributions that the partition function can be evaluated directly, and so attempting to estimate the ratio of partition functions directly is unlikely to be successful. This problem can be tackled using a technique known as *chaining* (Neal, 1993; Barber and Bishop, 1997), which involves introducing a succession of intermediate distributions  $p_2, \ldots, p_{M-1}$ that interpolate between a simple distribution  $p_1(z)$  for which we can evaluate the normalization coefficient  $Z_1$  and the desired complex distribution  $p_M(z)$ . We then have

$$
\frac{Z_M}{Z_1} = \frac{Z_2}{Z_1} \frac{Z_3}{Z_2} \cdots \frac{Z_M}{Z_{M-1}}
$$
\n(11.74)

in which the intermediate ratios can be determined using Monte Carlo methods as discussed above. One way to construct such a sequence of intermediate systems is to use an energy function containing a continuous parameter  $0 \le \alpha \le 1$  that interpolates between the two distributions

$$
E_{\alpha}(\mathbf{z}) = (1 - \alpha)E_1(\mathbf{z}) + \alpha E_M(\mathbf{z}).
$$
\n(11.75)

If the intermediate ratios in (11.74) are to be found using Monte Carlo, it may be more efficient to use a single Markov chain run than to restart the Markov chain for each ratio. In this case, the Markov chain is run initially for the system  $p_1$  and then after some suitable number of steps moves on to the next distribution in the sequence. Note, however, that the system must remain close to the equilibrium distribution at each stage.

# Exercises 11.1 (\*) www

- **11.1** ( $\star$ ) **www** Show that the finite sample estimator f defined by (11.2) has mean equal to  $\mathbb{E}[f]$  and variance given by (11.3).
- **11.2** ( $\star$ ) Suppose that z is a random variable with uniform distribution over  $(0, 1)$  and that we transform z using  $y = h^{-1}(z)$  where  $h(y)$  is given by (11.6). Show that y has the distribution  $p(y)$ .
- **11.3** ( $\star$ ) Given a random variable z that is uniformly distributed over  $(0, 1)$ , find a transformation  $y = f(z)$  such that y has a Cauchy distribution given by (11.8).
- **11.4**  $(\star \star)$  Suppose that  $z_1$  and  $z_2$  are uniformly distributed over the unit circle, as shown in Figure 11.3, and that we make the change of variables given by (11.10) and (11.11). Show that  $(y_1, y_2)$  will be distributed according to (11.12).
- **11.5**  $(\star)$  **www** Let **z** be a D-dimensional random variable having a Gaussian distribution with zero mean and unit covariance matrix, and suppose that the positive definite symmetric matrix  $\Sigma$  has the Cholesky decomposition  $\Sigma = LL^T$  where L is a lowertriangular matrix (i.e., one with zeros above the leading diagonal). Show that the variable **y** <sup>=</sup> *<sup>µ</sup>* <sup>+</sup> **Lz** has a Gaussian distribution with mean *<sup>µ</sup>* and covariance **Σ**. This provides a technique for generating samples from a general multivariate Gaussian using samples from a univariate Gaussian having zero mean and unit variance.
- **11.6**  $(\star \star)$  **www** In this exercise, we show more carefully that rejection sampling does indeed draw samples from the desired distribution  $p(z)$ . Suppose the proposal distribution is  $q(\mathbf{z})$  and show that the probability of a sample value **z** being accepted is given by  $\widetilde{p}(\mathbf{z})/kq(\mathbf{z})$  where  $\widetilde{p}$  is any unnormalized distribution that is proportional to  $p(\mathbf{z})$ , and the constant k is set to the smallest value that ensures  $kq(\mathbf{z}) \geq \tilde{p}(\mathbf{z})$  for all values of  $\mathbf{z}$ . Note that the probability of drawing a value  $\mathbf{z}$  is given by the probability values of **z**. Note that the probability of drawing a value **z** is given by the probability of drawing that value from  $q(z)$  times the probability of accepting that value given that it has been drawn. Make use of this, along with the sum and product rules of probability, to write down the normalized form for the distribution over **z**, and show that it equals  $p(\mathbf{z})$ .
- **11.7**  $(\star)$  Suppose that z has a uniform distribution over the interval [0, 1]. Show that the variable  $y = b \tan z + c$  has a Cauchy distribution given by (11.16).
- **11.8**  $(\star \star)$  Determine expressions for the coefficients  $k_i$  in the envelope distribution (11.17) for adaptive rejection sampling using the requirements of continuity and normalization.
- **11.9**  $(\star \star)$  By making use of the technique discussed in Section 11.1.1 for sampling from a single exponential distribution, devise an algorithm for sampling from the piecewise exponential distribution defined by (11.17).
- **11.10**  $(\star)$  Show that the simple random walk over the integers defined by (11.34), (11.35), and (11.36) has the property that  $\mathbb{E}[(z^{(\tau)})^2] = \mathbb{E}[(z^{(\tau-1)})^2] + 1/2$  and hence by induction that  $\mathbb{E}[(z^{(\tau)})^2] = \tau/2$ .

**Figure 11.15** A probability distribution over two variables  $z_1$ and  $z<sub>2</sub>$  that is uniform over the shaded regions and that is zero everywhere else.  $z_2$ 

Image /page/13/Figure/2 description: A 2D plot with a vertical axis labeled with an upward-pointing arrow and a horizontal axis labeled "z1". There are two pink, amorphous blobs plotted on the graph. One blob is in the lower-left quadrant, and the other is in the upper-right quadrant.

- **11.11**  $(\star \star)$  **www** Show that the Gibbs sampling algorithm, discussed in Section 11.3, satisfies detailed balance as defined by (11.40).
- **11.12**  $(\star)$  Consider the distribution shown in Figure 11.15. Discuss whether the standard Gibbs sampling procedure for this distribution is ergodic, and therefore whether it would sample correctly from this distribution
- **11.13**  $(\star \star)$  Consider the simple 3-node graph shown in Figure 11.16 in which the observed node x is given by a Gaussian distribution  $\mathcal{N}(x|\mu, \tau^{-1})$  with mean  $\mu$  and precision  $\tau$ . Suppose that the marginal distributions over the mean and precision are given by  $\mathcal{N}(\mu|\mu_0, s_0)$  and  $\text{Gam}(\tau|a, b)$ , where  $\text{Gam}(\cdot|\cdot, \cdot)$  denotes a gamma distribution. Write down expressions for the conditional distributions  $p(\mu|x, \tau)$  and  $p(\tau|x, \mu)$  that would be required in order to apply Gibbs sampling to the posterior distribution  $p(\mu, \tau | x).$
- **11.14** ( $\star$ ) Verify that the over-relaxation update (11.50), in which  $z_i$  has mean  $\mu_i$  and variance  $\sigma_i$ , and where  $\nu$  has zero mean and unit variance, gives a value  $z_i$  with mean  $\mu_i$  and variance  $\sigma_i^2$ .
- **11.15**  $(\star)$  **www** Using (11.56) and (11.57), show that the Hamiltonian equation (11.58) is equivalent to  $(11.53)$ . Similarly, using  $(11.57)$  show that  $(11.59)$  is equivalent to  $(11.55).$
- **11.16**  $(\star)$  By making use of (11.56), (11.57), and (11.63), show that the conditional distribution  $p(\mathbf{r}|\mathbf{z})$  is a Gaussian.
- **Figure 11.16** A graph involving an observed Gaussian variable  $x$  with prior distributions over its mean  $\mu$  and precision  $\tau$ .

Image /page/13/Picture/10 description: A directed graphical model shows two nodes, mu and tau, with arrows pointing to a third node, x. The nodes mu and tau are represented by red circles, and the node x is represented by a blue circle.

# **558 11. SAMPLING METHODS**

**11.17**  $(\star)$  **www** Verify that the two probabilities (11.68) and (11.69) are equal, and hence that detailed balance holds for the hybrid Monte Carlo algorithm.