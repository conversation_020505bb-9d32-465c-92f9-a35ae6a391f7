# Otto calculus

Let M be a Riemannian manifold, and let  $P_2(M)$  be the associated Wasserstein space of order 2. Recall from Chapter 7 that  $P_2(M)$  is a length space and that there is a nice representation formula for the Wasserstein distance  $W_2$ :

$$
W_2(\mu_0, \mu_1)^2 = \inf \int_0^1 \|\dot{\mu}_t\|_{\mu_t}^2 dt,
$$
 (15.1)

where  $\|\mu\|_{\mu}$  is the norm of the infinitesimal variation  $\mu$  of the measure  $\mu$ , defined by

$$
\|\dot{\mu}\|_{\mu} = \inf \left\{ \int |v|^2 d\mu; \qquad \dot{\mu} + \nabla \cdot (v\mu) = 0 \right\}.
$$

One of the reasons for the popularity of Riemannian geometry (as opposed to the study of more general metric structures) is that it allows for rather explicit computations. At the end of the nineties, <PERSON> realized that some precious inspiration could be gained by performing computations of a Riemannian nature in the Wasserstein space. His motivations will be described later on; to make a long story short, he needed a good formalism to study certain diffusive partial differential equations which he knew could be considered as gradient flows in the Wasserstein space.

In this chapter, as in <PERSON>'s original papers, this problem will be considered from a purely formal point of view, and there will be no attempt at rigorous justification. So the problem is to set up rules for formally differentiating functions (i.e. functionals) on  $P_2(M)$ . To fix the ideas, and because this is an important example arising in many different contexts, I shall discuss only a certain class of functionals,

that involve (i) a function  $V : M \to \mathbb{R}$ , used to distort the reference volume measure; and (ii) a function  $U : \mathbb{R}_+ \to \mathbb{R}$ , twice differentiable (at least on  $(0, +\infty)$ ), which will relate the values of the density of our probability measure and the value of the functional. So let

$$
\left\{ \begin{array}{l} \nu(dx) := e^{-V(x)} \operatorname{vol}(dx) \\ U_{\nu}(\mu) := \int_M U(\rho(x)) d\nu(x), \quad \mu = \rho \nu. \end{array} \right. \quad (15.2)
$$

So far the functional  $U_{\nu}$  is only defined on the set of probability measures that are absolutely continuous with respect to  $\nu$ , or equivalently with respect to the volume measure, and I shall not go beyond that setting before Part III of these notes. If  $\rho^0$  stands for the density of  $\mu$ with respect to the plain volume, then obviously  $\rho^0 = e^{-V} \rho$ , so there is the alternative expression

$$
U_{\nu}(\mu) = \int_M U(e^V \rho^0) e^{-V} dvol, \qquad \mu = \rho^0 \text{ vol}.
$$

One can think of  $U$  as a constitutive law for the internal energy of a fluid: this is jargon to say that the energy "contained" in a fluid of density  $\rho$  is given by the formula  $\int U(\rho)$ . The function U should be a property of the fluid itself, and might reflect some microscopic interaction between the particles which constitute it; it is natural to assume  $U(0) = 0$ .

In the same thermodynamical analogy, one can also introduce the pressure law:

$$
p(\rho) = \rho U'(\rho) - U(\rho).
$$
 (15.3)

The physical interpretation is as follows: if the fluid is enclosed in a domain  $\Omega$ , then the pressure felt by the boundary  $\partial\Omega$  at a point x is normal and proportional to  $p(\rho)$  at that point. (Recall that the pressure is defined, up to a sign, as the partial derivative of the internal energy with respect to the volume of the fluid.) So if you consider a homogeneous fluid of total mass 1, in a volume  $V$ , then its density is  $\rho = 1/V$ , the total energy is  $V U(1/V)$ , and the pressure should be  $(-d/dV)[V U(1/V)] = p(1/V);$  this justifies formula (15.3).

To the pressure p is associated a total pressure  $\int p(\rho) d\nu$ , and one can again consider the influence of small variations of volume on this functional; this leads to the definition of the iterated pressure:

15 Otto calculus 437

$$
p_2(\rho) = \rho \, p'(\rho) - p(\rho). \tag{15.4}
$$

Both the pressure and the iterated pressure will appear naturally when one differentiates the energy functional: the pressure for first-order derivatives, and the iterated pressure for second-order derivatives.

**Example 15.1.** Let  $m \neq 1$ , and

$$
U(\rho) = U^{(m)}(\rho) = \frac{\rho^m - \rho}{m - 1};
$$

then

$$
p(\rho) = \rho^m
$$
,  $p_2(\rho) = (m-1)\rho^m$ .

There is an important limit case as  $m \to 1$ :

$$
U^{(1)}(\rho) = \rho \log \rho;
$$

then

$$
p(\rho) = \rho, \qquad p_2(\rho) = 0.
$$

By the way, the linear part  $-\rho/(m-1)$  in  $U^{(m)}$  does not contribute to the pressure, but has the merit of displaying the link between  $U^{(m)}$ and  $U^{(1)}$ .

Differential operators will also be useful. Let  $\Delta$  be the Laplace operator on  $M$ , then the distortion of the volume element by the function  $V$ leads to a natural second-order operator:

$$
L = \Delta - \nabla V \cdot \nabla. \tag{15.5}
$$

Recall from Chapter 14 the expression of the *carré du champ itéré* associated with L:

$$
\Gamma_2(\psi) = L\left(\frac{|\nabla \psi|^2}{2}\right) - \nabla \psi \cdot \nabla (L\psi)
$$
\n(15.6)

$$
= \|\nabla^2 \psi\|_{\text{HS}}^2 + \left(\text{Ric} + \nabla^2 V\right)(\nabla \psi);\tag{15.7}
$$

the second equality is a consequence of Bochner's formula (14.28), as we shall briefly check. With respect to (14.28), there is an additional term in the left-hand side:

$$
-\nabla V \cdot \nabla \frac{|\nabla \psi|^2}{2} + \nabla \psi \cdot \nabla (\nabla V \cdot \nabla \psi)
$$
  
\=\ 
$$
-\langle \nabla^2 \psi \cdot \nabla V, \nabla \psi \rangle + \langle \nabla^2 V \cdot \nabla \psi, \nabla \psi \rangle + \langle \nabla^2 \psi \cdot \nabla V, \nabla \psi \rangle
$$
  
\=\ 
$$
\langle \nabla^2 V \cdot \nabla \psi, \nabla \psi \rangle,
$$

which is precisely the additional term in the right-hand side.

The next formula is the first important result in this chapter: it gives an "explicit" expression for the gradient of the functional  $U_{\nu}$ . For a given measure  $\mu$ , the gradient of  $U_{\nu}$  at  $\mu$  is a "tangent vector" at  $\mu$  in the Wasserstein space, so this should be an infinitesimal variation of  $\mu$ .

Formula 15.2 (Gradient formula in Wasserstein space). Let  $\mu$ be absolutely continuous with respect to  $\nu$ . Then, with the above notation,

$$
\text{grad}_{\mu} U_{\nu} = -\nabla \cdot \left(\mu \, \nabla U'(\rho)\right) \tag{15.8}
$$

$$
= -\nabla \cdot \left( e^{-V} \nabla p(\rho) \right) \text{vol} \tag{15.9}
$$

$$
= -\left(L p(\rho)\right)\nu. \tag{15.10}
$$

Remark 15.3. The expression in the right-hand side of (15.8) is the divergence of a vector-valued measure; recall that  $\nabla \cdot m$  is defined in the weak sense by its action on compactly supported smooth functions:

$$
\int \phi \, d(\nabla \cdot m) = - \int \nabla \phi \cdot (dm).
$$

On the other hand, the divergence in (15.9) is the divergence of a vector field. Note that  $\nabla \cdot (\xi \text{ vol}) = (\nabla \cdot \xi)$  vol, so in (15.9) one could put the volume "inside the divergence". All three expressions in Formula (15.2) are interesting, the first one because it writes the "tangent vector"  $\text{grad}_{\mu}U_{\nu}$  in the normalized form  $-\nabla \cdot (\mu \nabla \psi)$ , with  $\psi = U'(\rho)$ ; the second one because it gives the result as the divergence of a vector field; the third one because it is stated in terms of the infinitesimal variation of density  $\rho = d\mu/d\nu$ .

Below are some important examples of application of Formula 15.2.

Example 15.4. Define the H-functional of Boltzmann (opposite of the entropy) by

$$
H(\mu) = \int_M \rho \log \rho \, d\text{vol}.
$$

Then the second expression in equation (15.8) yields

$$
\text{grad}_{\mu}H = -\Delta\mu,
$$

which can be identified with the function  $-\Delta \rho$ . Thus the gradient of Boltzmann's entropy is the Laplace operator. This short statement is one of the first striking conclusions of Otto's formalism.

**Example 15.5.** Now consider a general  $\nu = e^{-V}$  vol, write  $\mu = \rho \nu =$  $\rho^0$  vol, and define

$$
H_{\nu}(\mu) = \int_M \rho \log \rho \, d\nu = \int_M (\log \rho^0 + V) \, d\mu
$$

(this is the H-functional relative to the reference measure  $\nu$ ). Then

$$
\text{grad}_{\mu}H_{\nu} = -(\Delta \rho - \nabla V \cdot \nabla \rho)\,\nu = -(L\rho)\,\nu.
$$

In short, the gradient of the relative entropy is the distorted Laplace operator.

Example 15.6. To generalize Example 15.4 in another direction, consider

$$
H^{(m)}(\mu) = \int \frac{\rho^m - \rho}{m - 1} \, d\text{vol};
$$

then

$$
\text{grad}_{\mu} H^{(m)} = -\Delta(\rho^m).
$$

More generally, if  $\rho$  is the density with respect to  $\nu = e^{-V}$  vol, and

$$
H_{\nu}^{(m)}(\mu) = \int \frac{\rho^m - \rho}{m - 1} d\nu,
$$

then

$$
\operatorname{grad}_{\mu} U_{\nu} = -e^{V} \nabla \cdot \left( e^{-V} \nabla \rho^{m} \right) \nu \tag{15.11}
$$

$$
= -\left(L\rho^m\right)\nu.\tag{15.12}
$$

The next formula is about second-order derivatives, or Hessians. Since the Hessian of  $U_{\nu}$  at  $\mu$  is a quadratic form on the tangent space  $T_{\mu}P_{2}$ , I shall write down its expression when evaluated on a tangent vector of the form  $-\nabla \cdot (\mu \nabla \psi)$ .

Formula 15.7 (Hessian formula in Wasserstein space). Let  $\mu$  be absolutely continuous with respect to  $\nu$ , and let  $\mu = -\nabla \cdot (\mu \nabla \psi)$  be a tangent vector at  $\mu$ . Then, with the above notation,

$$
\operatorname{Hess}_{\mu} U_{\nu}(\dot{\mu}) = \int_{M} \Gamma_{2}(\psi) p(\rho) d\nu + \int_{M} (L\psi)^{2} p_{2}(\rho) d\nu \qquad (15.13)
$$
$$
= \int_{M} \left[ \|\nabla^{2}\psi\|_{\text{HS}}^{2} + \left(\operatorname{Ric} + \nabla^{2}V\right)(\nabla\psi) \right] p(\rho) d\nu + \int_{M} \left( -\Delta\psi + \nabla V \cdot \nabla\psi \right)^{2} p_{2}(\rho) d\nu. \quad (15.14)
$$

**Remark 15.8.** As expected, this is a quadratic expression in  $\nabla \psi$  and its derivatives; and this expression does depend on the measure  $\mu$ .

**Example 15.9.** Applying the formula with  $U(\rho) = (\rho^m - \rho)/(m - 1)$ , recalling that  $\mu = \rho \nu$ , one obtains

$$
\operatorname{Hess}_{{\mu}} H_{{\nu}}^{(m)}(\dot{\mu}) =
$$
  
$$
\int_{M} \left( \|\nabla^{2} \psi\|_{\text{HS}}^{2} + (\operatorname{Ric} + \nabla^{2} V)(\nabla \psi) + (m - 1)(\Delta \psi - \nabla V \cdot \nabla \psi)^{2} \right) \rho^{m-1} d\mu.
$$

In the limit case  $m = 1$ , which is  $U(\rho) = \rho \log \rho$ , this expression simplifies into

$$
\operatorname{Hess}_{\mu} H_{\nu}(\dot{\mu}) = \int_{M} \left( \|\nabla^{2} \psi\|_{\text{HS}}^{2} + (\operatorname{Ric} + \nabla^{2} V)(\nabla \psi) \right) d\mu;
$$

or equivalently, with the notation of Chapter 14,

$$
\operatorname{Hess}_{\mu} H_{\nu}(\dot{\mu}) = \int_{M} \left( \|\nabla^{2} \psi\|_{\operatorname{HS}}^{2} + \operatorname{Ric}_{\infty,\nu}(\nabla \psi) \right) d\mu.
$$

Formulas 15.2 and 15.7 will be justified only at a heuristic level. A rigorous proof would require many more definitions and much more apparatus, as well as regularity and decay assumptions on the measures and the functionals. So here I shall disregard all issues about integrability and regularity, which will be a huge simplification. Still, the proofs will not be completely trivial.

"Proof" of Formula 15.2. When the integration measure is not specified, it will be the volume rather than  $\nu$ . To understand the proof, it is important to make the distinction between a gradient and a differential.

Let  $\zeta$  be such that the tangent vector  $\text{grad}_{\mu}U_{\nu}$  can be represented as  $-\nabla \cdot (\mu \nabla \zeta)$ , and let  $\partial_t \mu = -\nabla \cdot (\mu \nabla \psi)$  be an arbitrary "tangent" vector". The infinitesimal variation of the density  $\rho = d\mu/d\nu$  is given by

$$
\partial_t \rho = -e^V \nabla \cdot (\rho e^{-V} \nabla \psi).
$$

By direct computation and integration by parts, the infinitesimal variation of  $U_{\nu}$  along that variation is equal to

$$
\int U'(\rho) \, \partial_t \rho \, d\nu = - \int U'(\rho) \, \nabla \cdot (\rho \, e^{-V} \nabla \psi)
$$
$$
= \int \nabla U'(\rho) \cdot \nabla \psi \, \rho \, e^{-V}
$$
$$
= \int \nabla U'(\rho) \cdot \nabla \psi \, d\mu.
$$

By definition of the gradient operator, this should coincide with

$$
\left\langle\mathrm{grad}_{\mu}U_{\nu},\,\partial_{t}\mu\right\rangle=\int\nabla\zeta\cdot\nabla\psi\,d\mu.
$$

If this should hold true for all  $\psi$ , the only possible choice is that  $\nabla U'(\rho) = \nabla \zeta(\rho)$ , at least  $\mu$ -almost everywhere. In any case  $\zeta := U'(\rho)$ provides an admissible representation of  $\text{grad}_{\mu}U_{\nu}$ . This proves formula (15.8). The other two formulas are obtained by noting that  $p'(\rho) = \rho U''(\rho)$ , and so

$$
\nabla U'(\rho)\rho = \rho U''(\rho)\nabla \rho = p'(\rho)\nabla \rho = \nabla p(\rho);
$$

therefore

$$
\nabla \cdot (\mu \nabla U'(\rho)) = \nabla \cdot (e^{-V} \rho \nabla U'(\rho)) = e^{-V} L p(\rho).
$$

For the second order (formula (15.7)), things are more intricate. The following identity will be helpful: If  $\xi$  is a tangent vector at x on a Riemannian manifold  $M$ , and F is a function on  $M$ , then

$$
\text{Hess}_x F(\xi) = \frac{d^2}{dt^2} \bigg|_{t=0} F(\gamma(t)), \tag{15.15}
$$

where  $\gamma(t)$  is a geodesic starting from  $\gamma(0) = x$  with velocity  $\dot{\gamma}(0) = \xi$ . To prove (15.15), it suffices to note that the first derivative of  $F(\gamma(t))$ is  $\dot{\gamma}(t) \cdot \nabla F(\gamma(t))$ ; so the second derivative is  $(d/dt)(\dot{\gamma}(t)) \cdot \nabla F(\gamma(t))$  +  $\langle \nabla^2 F(\gamma(t)) \cdot \dot{\gamma}(t), \dot{\gamma}(t) \rangle$ , and the first term vanishes because a geodesic has zero acceleration.

"Proof" of Formula 15.7. The problem consists in differentiating  $U_{\nu}(\mu_t)$ twice along a geodesic path of the form

$$
\begin{cases} \partial_t \mu + \nabla \cdot (\mu \nabla \psi) = 0 \\ \partial_t \psi + \frac{|\nabla \psi|^2}{2} = 0. \end{cases}
$$

The following integration by parts formula will be useful:

$$
\int \nabla f \cdot \nabla g \, d\nu = -\int (Lf)g \, d\nu. \tag{15.16}
$$

From the proof of the gradient formula, one has, with the notation  $\mu_t = \rho_t \nu,$ 

$$
\frac{dU_{\nu}(\mu_t)}{dt} = \int_M \nabla \psi_t \cdot \nabla U'(\rho_t) \rho_t \, d\nu
$$
$$
= \int_M \nabla \psi_t \cdot \nabla p(\rho_t) \, d\nu
$$
$$
= - \int_M (L\psi_t) p(\rho_t) \, d\nu.
$$

It remains to differentiate again. To alleviate notation, I shall not write the time variable explicitly. So

$$
\frac{d^2U_{\nu}(\mu)}{dt^2} = -\int (L\partial_t \psi) p(\rho) d\nu - \int (L\psi) p'(\rho) \partial_t \rho d\nu \qquad (15.17)
$$

$$
= \int L\left(\frac{|\nabla \psi|^2}{2}\right) p(\rho) d\nu - \int (L\psi) p'(\rho) \partial_t \mu. \tag{15.18}
$$

The last term in (15.18) can be rewritten as

The second term in (15.19) needs a bit of reworking; it can be recast

$$
\int (L\psi) p'(\rho) \nabla \cdot (\mu \nabla \psi)
$$

$$
= -\int \nabla ((L\psi) p'(\rho)) \cdot \nabla \psi d\mu
$$

$$
= -\int \nabla ((L\psi) p'(\rho)) \cdot \nabla \psi \rho d\nu
$$

$$
= -\int \nabla (L\psi) \cdot \nabla \psi p'(\rho) \rho d\nu - \int (L\psi) p''(\rho) \rho \nabla \rho \cdot \nabla \psi d\nu
$$

$$
= -\int \nabla (L\psi) \cdot \nabla \psi \rho p'(\rho) d\nu - \int (L\psi) \nabla p_2(\rho) \cdot \nabla \psi d\nu. (15.19)
$$

The second term in (15.19) needs a bit of reworking: it can be recast as

$$
- \int \nabla (L\psi p_2(\rho)) \cdot \nabla \psi \, d\nu - \int (\nabla L\psi) p_2(\rho) \cdot \nabla \psi \, d\nu
$$
$$
= \int (L\psi)^2 p_2(\rho) \, d\nu - \int (\nabla L\psi) \cdot \nabla \psi p_2(\rho) \, d\nu,
$$

where  $(15.16)$  has been used once more.

By collecting all these calculations,

$$
\frac{d^2U_{\nu}(\mu)}{dt^2} = \int L\left(\frac{|\nabla\psi|^2}{2}\right)p(\rho)\,d\nu + \int (L\psi)^2p_2(\rho)\,d\nu + \int (\nabla\psi\cdot\nabla L\psi)\left(p_2(\rho) - \rho\,p'(\rho)\right)d\nu.
$$

Since  $p_2(\rho) - \rho p'(\rho) = -p(\rho)$ , this transforms into

$$
\int \left( L\left(\frac{|\nabla \psi|^2}{2}\right) - \nabla \psi \cdot \nabla L\psi \right) p(\rho) d\nu + \int (L\psi)^2 p_2(\rho). \qquad (15.20)
$$

In view of  $(15.6)–(15.7)$ , this establishes formula  $(15.13)$ . □

**Exercise 15.10.** "Prove" that the gradient of an arbitrary functional  $\mathcal{F}$ on  $P_2(M)$  can be written

$$
\operatorname{grad}_{\mu} \mathcal{F} = -\nabla \cdot (\mu \nabla \phi), \qquad \phi = \frac{\delta \mathcal{F}}{\delta \mu},
$$

where  $\delta \mathcal{F}/\delta \mu$  is a function defined by

$$
\frac{d}{dt}\mathcal{F}(\mu_t) = \int \left(\frac{\delta \mathcal{F}}{\delta \mu}\right) \partial_t \mu_t.
$$

Check that in the particular case

$$
\mathcal{F}(\mu) = \int_M F\Big(x, \rho(x), \nabla \rho(x)\Big) d\nu(x),\tag{15.21}
$$

where  $F = F(x, \rho, p)$  is a smooth function of  $\rho \in \mathbb{R}_+$ ,  $(x, p) \in TM$ , one has

$$
\left(\frac{\partial \mathcal{F}}{\partial \mu}\right)(x) = (\partial_{\rho} F)(x, \rho(x), \nabla \rho(x))
$$
$$
- (\nabla_x - \nabla V(x)) \cdot (\nabla_p F)(x, \rho(x), \nabla \rho(x))
$$

The following two open problems (loosely formulated) are natural and interesting, and I don't know how difficult they are:

Open Problem 15.11. Find a nice formula for the Hessian of the functional  $\mathcal F$  appearing in (15.21).

Open Problem 15.12. Find a nice formalism playing the role of the Otto calculus in the space  $P_p(M)$ , for  $p \neq 2$ . More generally, are there nice formal rules for taking derivatives along displacement interpolation, for general Lagrangian cost functions?

To conclude this chapter, I shall come back to the subject of rigorous justification of Otto's formalism. At the time of writing, several theories have been developed, at least in the Euclidean setting (see the bibliographical notes); but they are rather heavy and not yet completely convincing.<sup>1</sup> From the technical point of view, they are based on the natural strategy which consists in truncating and regularizing, then applying the arguments presented in this chapter, then passing to the limit.

A quite different strategy, which I personally recommend, consists in translating all the Eulerian statements in the language of Lagrangian formalism. This is less appealing for intuition and calculations, but somehow easier to justify in the case of optimal transport. For instance, instead of the Hessian operator, one will only speak of the second derivative along geodesics in the Wasserstein space. This point of view will be developed in the next two chapters, and then a rigorous treatment will not be that painful.

Still, in many situations the Eulerian point of view is better for intuition and for understanding, in particular in certain problems involving functional inequalities. The above discussion might be summarized by the slogan "Think Eulerian, prove Lagrangian". This is a rather exceptional situation from the point of view of fluid dynamics, where the standard would rather be "Think Lagrangian, prove Eulerian" (for instance, shocks are delicate to treat in a Lagrangian formalism). Once again, the point is that "there are no shocks" in optimal transport: as discussed in Chapter 8, trajectories do not meet until maybe at final time.

## Bibliographical notes

Otto's seminal paper [669] studied the formal Riemannian structure of the Wasserstein space, and gave applications to the study of the porous medium equation; I shall come back to this topic later. With all the preparations of Part I, the computations performed in this chapter may look rather natural, but they were a little conceptual tour de force at the time of Otto's contribution, and had a strong impact on the research community. This work was partly inspired by the desire to

<sup>&</sup>lt;sup>1</sup> I can afford this negative comment since myself I participated in the story.

understand in depth a previous contribution by Jordan, Kinderlehrer and Otto [493].

Otto's computations were concerned with the case  $U(\rho) = \rho^m$  in  $\mathbb{R}^n$ . Then Otto and I considered  $U(\rho) = \rho \log \rho$  on a manifold [671, Section 3]; we computed the Hessian by differentiating twice along geodesics in the Wasserstein space. (To my knowledge, this was the first published work where Ricci curvature appeared in relation to optimal transport.) Functionals of the form  $E(\mu) = \int W(x - y) \mu(dx) \mu(dy)$ in  $\mathbb{R}^n$  were later studied by Carrillo, McCann and myself [213]. More recently, Lott and I [577, Appendix E] considered the functionals  $U_{\nu}$ presented in this chapter (on a manifold and with a reference measure  $e^{-V}$ vol).

In my previous book [814, Section 9.1], I already gave formulas for the gradient and Hessian of three basic types of functionals on  $P_2(\mathbb{R}^n)$ that I called internal energy, potential energy and interaction energy, and which can be written respectively (with obvious notation) as

$$
\int U(\rho(x)) dx; \qquad \int V d\mu; \qquad \frac{1}{2} \int W(x - y) d\mu(x) d\mu(y). \quad (15.22)
$$

A short presentation of the differential calculus in the Wasserstein space can be found in [814, Chapter 8]; other sources dealing with this subject, with some variations in the presentation, are [30, 203, 214, 671, 673].

Apart from computations of gradients and Hessians, little is known about Riemannian calculus in  $P_2(M)$ . The following issues are natural (I am not sure how important they are, but at least they are natural):

- Is there a Jacobi equation in  $P_2(M)$ , describing small variations of geodesic fields?
- Can one define Christoffel symbols, at least formally?
- Can one define a Laplace operator?
- Can one define a volume element? A divergence operator?

Recently, Lott [575] partly answered some of these questions by establishing formulas for the Riemannian connection and Riemannian curvature in the subset  $P^{\infty}(M)$  of smooth positive densities, viewed as a subset of  $P_2(M)$ , when M is compact. In a different direction, Gigli [415] gave a rigorous construction of a parallel transport along a curve in  $P_2(\mathbb{R}^n)$  for which  $\int_0^1 \|v_t\|_{\text{Lip}} dt < +\infty$ . (See [29] for improved results.)

The problem whether there exists a natural probability measure ("volume", or "Boltzmann–Gibbs measure") on  $P_2(M)$  is, I think, very relevant for applications in geometry or theoretical statistics. Von Renesse and Sturm [827] have managed to construct natural probability measures on  $P_2(S^1)$ ; these measures depend on a parameter  $\beta$  ("inverse temperature") and may be written heuristically as

$$
\mathbb{P}_{\beta}(d\mu) = \frac{e^{-H_{\nu}(\mu)} d\text{vol}(\mu)}{Z_{\beta}},
$$
\n(15.23)

where  $\nu$  is the reference measure on  $S^1$ , that is, the Lebesgue measure. Their construction strongly uses the one-dimensional assumption, and makes the link with the theory of "Poisson measures" used in nonparametric statistics. A particle approximation was studied in [38].

The point of view that was first advocated by Otto himself, and which I shall adopt in this course, is that the "Otto calculus" should primarily be considered a heuristic tool, and conclusions drawn by its use should then be checked by "direct" means. This might lack elegance, but it is much safer from the point of view of mathematical rigor. Some papers in which this strategy has been used with success are [577, 669, 671, 673, 761]. Recently, Calvez [197] used the Otto formalism to derive complex identities for chemotaxis models of Keller–Segel type, which would have been very difficult to guess otherwise.

In most of these works, rigorous justifications are done in Lagrangian formalism, or by methods which do not use transport at all. The work by Otto and Westdickenberg [673] is an interesting exception: there everything is attacked from an Eulerian perspective (using such tools as regularization of currents on manifolds); see [271] for an elaboration of these ideas, which applies even without smoothness.

All the references quoted above mainly deal with calculus in  $P_p(M)$ for  $p = 2$ . The case  $p \neq 2$  is much less well understood; as noticed in [30, p. 10],  $P_p(M)$  can be seen as a kind of Finsler structure, and there are also rules to compute derivatives in that space, at least to first order. The most general results to this date are in [30].

A better understood generalization treats the case when geodesics in  $P_2(M)$  are replaced by action-minimizing curves, for some Lagrangian action like those considered in Chapter 7; the adaptation of Otto calculus to this situation was worked out by Lott [576], with applications to the study of the Ricci flow.

Let me conclude with some remarks about the functionals considered in this chapter.

Functionals of the form (15.22) appear everywhere in mathematical physics to model all kinds of energies. It would be foolish to try to make a list.

The interpretation of  $p(\rho) = \rho U'(\rho) - U(\rho)$  as a pressure associated to the constitutive law  $U$  is well-known in thermodynamics, and was explained to me by McCann; the discussion in the present chapter is slightly expanded in [814, Remarks 5.18].

The functional  $H_{\nu}(\mu) = \int \rho \log \rho d\nu \, (\mu = \rho \nu)$  is well-known is statistical physics, where it was introduced by Boltzmann [141]. In Boltzmann's theory of gases,  $H_{\nu}$  is identified with the negative of the entropy. It would take a whole book to review the meaning of entropy in thermodynamics and statistical mechanics (see, e.g., [812] for its use in kinetic theory). I should also mention that the  $H$  functional coincides with the Kullback information in statistics, and it appears in Shannon's theory of information as an optimal compression rate [747], and in Sanov's theorem as the rate function for large deviations of the empirical measure of independent samples [302, Chapter 3] [296, Theorem 6.2.10].

An interesting example of a functional of the form (15.21) that was considered in relation with optimal transport is the so-called Fisher information,

$$
I(\mu) = \int \frac{|\nabla \rho|^2}{\rho};
$$

see [30, Example 11.1.10] and references there provided. We shall encounter this functional again later.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.