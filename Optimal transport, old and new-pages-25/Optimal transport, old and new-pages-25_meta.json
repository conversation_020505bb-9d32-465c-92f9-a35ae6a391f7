{"table_of_contents": [{"title": "<PERSON> calculus", "heading_level": null, "page_id": 0, "polygon": [[133.5, 98.25], [229.5, 98.25], [229.5, 111.2783203125], [133.5, 111.2783203125]]}, {"title": "436 15 <PERSON>", "heading_level": null, "page_id": 1, "polygon": [[133.5, 26.103515625], [235.9248046875, 26.103515625], [235.9248046875, 35.2880859375], [133.5, 35.2880859375]]}, {"title": "438 15 <PERSON>", "heading_level": null, "page_id": 3, "polygon": [[133.5, 26.25], [234.87890625, 26.25], [234.87890625, 35.4814453125], [133.5, 35.4814453125]]}, {"title": "442 15 <PERSON>", "heading_level": null, "page_id": 7, "polygon": [[132.8291015625, 25.5], [235.5, 25.5], [235.5, 35.8681640625], [132.8291015625, 35.8681640625]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 9, "polygon": [[232.787109375, 442.40625], [359.490234375, 442.40625], [359.490234375, 454.0078125], [232.787109375, 454.0078125]]}, {"title": "446 15 <PERSON>", "heading_level": null, "page_id": 11, "polygon": [[133.5, 26.25], [236.07421875, 26.25], [236.07421875, 35.6748046875], [133.5, 35.6748046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 35], ["Text", 3], ["Equation", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1591, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 55], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1046, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 69], ["Equation", 9], ["Text", 8], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1141, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 48], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 80], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1171, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 77], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2052, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 67], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 950, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 102], ["Equation", 5], ["Text", 5], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5893, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 80], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1461, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 43], ["Text", 4], ["ListItem", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 40], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 30], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-25"}