{"table_of_contents": [{"title": "Semiconvexity and semiconcavity", "heading_level": null, "page_id": 0, "polygon": [[133.5, 380.25], [331.5, 380.25], [331.5, 391.74609375], [133.5, 391.74609375]]}, {"title": "Assumptions on the cost function", "heading_level": null, "page_id": 6, "polygon": [[133.5, 48.0], [335.8828125, 48.0], [335.8828125, 59.11962890625], [133.5, 59.11962890625]]}, {"title": "Proposition 10.15 (Properties of Lagrangian cost functions).", "heading_level": null, "page_id": 7, "polygon": [[133.5, 253.30078125], [465.75, 253.30078125], [465.75, 263.7421875], [133.5, 263.7421875]]}, {"title": "Differentiability of c-convex functions", "heading_level": null, "page_id": 10, "polygon": [[133.5, 536.25], [357.0, 536.25], [357.0, 547.59375], [133.5, 547.59375]]}, {"title": "Theorem 10.24 (c-subdifferentiability of c-convex functions).", "heading_level": null, "page_id": 11, "polygon": [[133.4267578125, 192.75], [465.0, 192.75], [465.0, 204.1875], [133.4267578125, 204.1875]]}, {"title": "Theorem 10.25 (Subdifferentiability of c-convex functions).", "heading_level": null, "page_id": 11, "polygon": [[133.5, 284.625], [463.5, 284.625], [463.5, 295.06640625], [133.5, 295.06640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 535], ["Line", 34], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1929, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 39], ["Text", 7], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 730], ["Line", 58], ["TextInlineMath", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 924], ["Line", 52], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 667], ["Line", 98], ["Equation", 8], ["TextInlineMath", 6], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 700], ["Line", 45], ["TextInlineMath", 6], ["Equation", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 419], ["Line", 42], ["ListItem", 8], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 41], ["TextInlineMath", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 614], ["Line", 65], ["TextInlineMath", 6], ["Equation", 3], ["Text", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1372, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 39], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 44], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 36], ["TextInlineMath", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 646], ["Line", 54], ["TextInlineMath", 7], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 43], ["TextInlineMath", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 998, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 598], ["Line", 74], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1445, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-12"}