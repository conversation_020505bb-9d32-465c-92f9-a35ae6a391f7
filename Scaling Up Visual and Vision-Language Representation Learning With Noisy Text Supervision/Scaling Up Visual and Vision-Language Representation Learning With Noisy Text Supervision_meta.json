{"table_of_contents": [{"title": "Scaling Up Visual and Vision-Language Representation Learning\nWith Noisy Text Supervision", "heading_level": null, "page_id": 0, "polygon": [[96.75, 89.25], [499.04296875, 89.25], [499.04296875, 120.7529296875], [96.75, 120.7529296875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 206.25], [195.75, 206.25], [195.75, 216.94921875], [148.5, 216.94921875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[306.0, 206.25], [384.75, 206.25], [384.75, 216.755859375], [306.0, 216.755859375]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[306.0, 338.185546875], [391.5, 338.185546875], [391.5, 349.787109375], [306.0, 349.787109375]]}, {"title": "3. A Large-Scale Noisy Image-Text Dataset", "heading_level": null, "page_id": 2, "polygon": [[54.0, 310.728515625], [276.0, 310.728515625], [276.0, 323.490234375], [54.0, 323.490234375]]}, {"title": "4. Pre-training and Task Transfer", "heading_level": null, "page_id": 2, "polygon": [[306.0, 282.3046875], [479.25, 282.3046875], [479.25, 294.6796875], [306.0, 294.6796875]]}, {"title": "4.1. Pre-training on Noisy Image-Text Pairs", "heading_level": null, "page_id": 2, "polygon": [[305.25, 303.57421875], [493.5, 303.57421875], [493.5, 314.40234375], [305.25, 314.40234375]]}, {"title": "4.2. Transferring to Image-Text Matching & Retrieval", "heading_level": null, "page_id": 3, "polygon": [[54.0, 123.0732421875], [285.75, 123.0732421875], [285.75, 133.9013671875], [54.0, 133.9013671875]]}, {"title": "4.3. Transferring to Visual Classification", "heading_level": null, "page_id": 3, "polygon": [[54.0, 341.25], [227.5576171875, 341.25], [227.5576171875, 352.107421875], [54.0, 352.107421875]]}, {"title": "5. Experiments and Results", "heading_level": null, "page_id": 3, "polygon": [[306.0, 68.0625], [448.5, 68.0625], [448.5, 79.5673828125], [306.0, 79.5673828125]]}, {"title": "5.1. Image-Text Matching & Retrieval", "heading_level": null, "page_id": 3, "polygon": [[306.0, 346.5], [469.5, 346.5], [469.5, 356.748046875], [306.0, 356.748046875]]}, {"title": "5.2. Zero-shot Visual Classification", "heading_level": null, "page_id": 4, "polygon": [[305.701171875, 454.0078125], [456.0, 454.0078125], [456.0, 463.2890625], [305.701171875, 463.2890625]]}, {"title": "5.3. Visual Classification w/ Image Encoder Only", "heading_level": null, "page_id": 5, "polygon": [[54.0, 202.5], [262.5, 202.5], [262.5, 212.501953125], [54.0, 212.501953125]]}, {"title": "6. Ablation Study", "heading_level": null, "page_id": 5, "polygon": [[306.0, 593.2265625], [397.5, 593.2265625], [397.5, 604.0546875], [306.0, 604.0546875]]}, {"title": "6.1. Model Architectures", "heading_level": null, "page_id": 6, "polygon": [[54.0, 129.75], [160.5, 129.75], [160.5, 140.0888671875], [54.0, 140.0888671875]]}, {"title": "6.2. Pre-training Datasets", "heading_level": null, "page_id": 6, "polygon": [[305.25, 234.75], [416.267578125, 234.75], [416.267578125, 244.212890625], [305.25, 244.212890625]]}, {"title": "7. Analysis of Learned Embeddings", "heading_level": null, "page_id": 7, "polygon": [[54.0, 278.25], [237.0, 278.25], [237.0, 290.232421875], [54.0, 290.232421875]]}, {"title": "8. Multilingual ALIGN Model", "heading_level": null, "page_id": 8, "polygon": [[54.0, 216.0], [210.0, 216.0], [210.0, 227.390625], [54.0, 227.390625]]}, {"title": "9. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[54.0, 633.75], [125.25, 633.75], [125.25, 645.43359375], [54.0, 645.43359375]]}, {"title": "10. Social Impacts and Future Work", "heading_level": null, "page_id": 8, "polygon": [[306.59765625, 303.75], [492.75, 303.75], [492.75, 314.40234375], [306.59765625, 314.40234375]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[306.0, 567.703125], [406.705078125, 567.703125], [406.705078125, 578.53125], [306.0, 578.53125]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 79.51904296875], [54.0, 79.51904296875]]}, {"title": "<PERSON><PERSON> Near-Duplicate Test Images from\nTraining Data", "heading_level": null, "page_id": 13, "polygon": [[54.0, 67.5], [283.587890625, 69.0], [283.587890625, 92.66748046875], [54.0, 92.66748046875]]}, {"title": "B. Evaluation on SimLex-999", "heading_level": null, "page_id": 13, "polygon": [[54.0, 285.0], [205.5, 285.0], [205.5, 295.83984375], [54.0, 295.83984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 86], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5225, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 92], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1020, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 117], ["Text", 10], ["Caption", 7], ["SectionHeader", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["TextInlineMath", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 660, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 100], ["Text", 8], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 592], ["Span", 510], ["Line", 78], ["Caption", 4], ["Table", 4], ["Text", 4], ["TableGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 28076, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["TableCell", 127], ["Line", 92], ["Text", 8], ["Table", 3], ["SectionHeader", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8043, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["TableCell", 162], ["Line", 104], ["Text", 8], ["SectionHeader", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3287, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 79], ["TableCell", 49], ["Text", 4], ["Caption", 3], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Picture", 1], ["Footnote", 1], ["TableGroup", 1], ["FigureGroup", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3834, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 99], ["TableCell", 88], ["Text", 9], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7000, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 93], ["ListItem", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 95], ["ListItem", 23], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 95], ["ListItem", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 41], ["ListItem", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["TableCell", 80], ["Line", 59], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["Footnote", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4258, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Scaling Up Visual and Vision-Language Representation Learning With Noisy Text Supervision"}