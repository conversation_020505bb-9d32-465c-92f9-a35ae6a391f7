{"table_of_contents": [{"title": "3 Generative models for discrete data", "heading_level": null, "page_id": 0, "polygon": [[83.8125, 95.3173828125], [388.5, 95.3173828125], [388.5, 142.6201171875], [83.8125, 142.6201171875]]}, {"title": "3.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 207.0], [195.75, 207.0], [195.75, 217.6875], [101.25, 217.6875]]}, {"title": "3.2 Bayesian concept learning", "heading_level": null, "page_id": 0, "polygon": [[99.0, 339.75], [266.25, 339.75], [266.25, 350.89453125], [99.0, 350.89453125]]}, {"title": "3.2.1 Likelihood", "heading_level": null, "page_id": 2, "polygon": [[94.5, 172.5], [180.0, 172.5], [180.0, 182.56640625], [94.5, 182.56640625]]}, {"title": "3.2.2 Prior", "heading_level": null, "page_id": 2, "polygon": [[92.7421875, 474.0], [155.25, 474.0], [155.25, 484.41796875], [92.7421875, 484.41796875]]}, {"title": "3.2.3 Posterior", "heading_level": null, "page_id": 3, "polygon": [[92.53125, 279.0], [173.25, 279.0], [173.25, 289.828125], [92.53125, 289.828125]]}, {"title": "3.2.4 Posterior predictive distribution", "heading_level": null, "page_id": 6, "polygon": [[93.0, 394.5], [282.0, 394.5], [282.0, 404.3671875], [93.0, 404.3671875]]}, {"title": "3.2.5 A more complex prior", "heading_level": null, "page_id": 7, "polygon": [[93.0, 327.0], [235.546875, 327.0], [235.546875, 337.60546875], [93.0, 337.60546875]]}, {"title": "3.3 The beta-binomial model", "heading_level": null, "page_id": 7, "polygon": [[99.75, 531.0], [260.25, 531.0], [260.25, 541.6875], [99.75, 541.6875]]}, {"title": "3.3.1 Likelihood", "heading_level": null, "page_id": 8, "polygon": [[94.5, 504.0], [180.75, 504.0], [180.75, 514.4765625], [94.5, 514.4765625]]}, {"title": "3.3.2 Prior", "heading_level": null, "page_id": 9, "polygon": [[92.390625, 272.25], [155.25, 272.25], [155.25, 282.392578125], [92.390625, 282.392578125]]}, {"title": "3.3.3 Posterior", "heading_level": null, "page_id": 10, "polygon": [[92.671875, 263.25], [173.953125, 263.25], [173.953125, 273.375], [92.671875, 273.375]]}, {"title": "3.3.3.1 Posterior mean and mode", "heading_level": null, "page_id": 11, "polygon": [[87.609375, 61.5], [243.0, 61.5], [243.0, 71.46826171875], [87.609375, 71.46826171875]]}, {"title": "3.3.3.2 Posterior variance", "heading_level": null, "page_id": 11, "polygon": [[87.0, 408.0], [209.953125, 408.0], [209.953125, 417.97265625], [87.0, 417.97265625]]}, {"title": "3.3.4 Posterior predictive distribution", "heading_level": null, "page_id": 12, "polygon": [[92.109375, 113.25], [282.75, 113.25], [282.75, 123.1611328125], [92.109375, 123.1611328125]]}, {"title": "******* Overfitting and the black swan paradox", "heading_level": null, "page_id": 12, "polygon": [[88.6640625, 276.75], [301.5, 276.75], [301.5, 286.98046875], [88.6640625, 286.98046875]]}, {"title": "******* Predicting the outcome of multiple future trials", "heading_level": null, "page_id": 13, "polygon": [[86.90625, 99.75], [336.75, 99.75], [336.75, 109.8720703125], [86.90625, 109.8720703125]]}, {"title": "3.4 The Dirichlet-multinomial model", "heading_level": null, "page_id": 13, "polygon": [[99.75, 485.25], [299.25, 485.25], [299.25, 496.125], [99.75, 496.125]]}, {"title": "3.4.1 Likelihood", "heading_level": null, "page_id": 14, "polygon": [[94.21875, 242.25], [180.75, 242.25], [180.75, 253.283203125], [94.21875, 253.283203125]]}, {"title": "3.4.2 Prior", "heading_level": null, "page_id": 14, "polygon": [[92.7421875, 383.25], [156.0, 383.25], [156.0, 394.2421875], [92.7421875, 394.2421875]]}, {"title": "3.4.3 Posterior", "heading_level": null, "page_id": 14, "polygon": [[92.4609375, 490.5], [173.8125, 490.5], [173.8125, 501.1875], [92.4609375, 501.1875]]}, {"title": "3.4.4 Posterior predictive", "heading_level": null, "page_id": 16, "polygon": [[92.25, 61.5], [223.59375, 60.75], [223.59375, 71.62646484375], [92.25, 71.62646484375]]}, {"title": "3.4.4.1 Worked example: language models using bag of words", "heading_level": null, "page_id": 16, "polygon": [[88.1015625, 270.75], [366.0, 270.75], [366.0, 280.65234375], [88.1015625, 280.65234375]]}, {"title": "3.5 Na<PERSON> Bayes classifiers", "heading_level": null, "page_id": 17, "polygon": [[99.75, 270.75], [246.0, 270.75], [246.0, 280.810546875], [99.75, 280.810546875]]}, {"title": "3.5.1 Model fitting", "heading_level": null, "page_id": 18, "polygon": [[93.796875, 147.0], [191.25, 147.0], [191.25, 157.412109375], [93.796875, 157.412109375]]}, {"title": "3.5.1.1 MLE for NBC", "heading_level": null, "page_id": 18, "polygon": [[89.71875, 215.25], [188.015625, 215.25], [188.015625, 224.173828125], [89.71875, 224.173828125]]}, {"title": "Algorithm 3.1: Fitting a naive <PERSON><PERSON> classifier to binary features", "heading_level": null, "page_id": 19, "polygon": [[132.1875, 64.5], [391.5, 64.5], [391.5, 74.25], [132.1875, 74.25]]}, {"title": "3.5.1.2 Bayesian naive Bayes", "heading_level": null, "page_id": 19, "polygon": [[87.890625, 425.25], [222.609375, 425.25], [222.609375, 435.05859375], [87.890625, 435.05859375]]}, {"title": "3.5.2 Using the model for prediction", "heading_level": null, "page_id": 20, "polygon": [[92.8828125, 213.0], [276.75, 213.0], [276.75, 223.69921875], [92.8828125, 223.69921875]]}, {"title": "3.5.3 The log-sum-exp trick", "heading_level": null, "page_id": 21, "polygon": [[92.6015625, 112.5], [235.125, 112.5], [235.125, 122.6865234375], [92.6015625, 122.6865234375]]}, {"title": "3.5.4 Feature selection using mutual information", "heading_level": null, "page_id": 21, "polygon": [[93.0, 523.5], [336.75, 523.5], [336.75, 533.4609375], [93.0, 533.4609375]]}, {"title": "3.5.5 Classifying documents using bag of words", "heading_level": null, "page_id": 22, "polygon": [[93.0, 498.65625], [330.0, 498.65625], [330.0, 508.1484375], [93.0, 508.1484375]]}, {"title": "Exercises", "heading_level": null, "page_id": 24, "polygon": [[129.75, 288.75], [178.5, 288.75], [178.5, 298.529296875], [129.75, 298.529296875]]}, {"title": "Exercise 3.3 Posterior predictive for Beta-Binomial model", "heading_level": null, "page_id": 25, "polygon": [[129.75, 62.25], [339.75, 62.25], [339.75, 72.0615234375], [129.75, 72.0615234375]]}, {"title": "Exercise 3.4 Beta updating from censored likelihood", "heading_level": null, "page_id": 25, "polygon": [[129.75, 270.75], [321.1875, 270.75], [321.1875, 279.*********], [129.75, 279.*********]]}, {"title": "Exercise 3.8 MLE for the uniform distribution", "heading_level": null, "page_id": 25, "polygon": [[129.75, 537.75], [298.5, 537.75], [298.5, 547.06640625], [129.75, 547.06640625]]}, {"title": "Exercise 3.9 Bayesian analysis of the uniform distribution", "heading_level": null, "page_id": 26, "polygon": [[129.515625, 114.75], [341.25, 114.75], [341.25, 123.7939453125], [129.515625, 123.7939453125]]}, {"title": "Exercise 3.10 Taxicab (tramcar) problem", "heading_level": null, "page_id": 26, "polygon": [[129.75, 321.75], [276.890625, 321.75], [276.890625, 330.01171875], [129.75, 330.01171875]]}, {"title": "Exercise 3.15 Setting the beta hyper-parameters", "heading_level": null, "page_id": 28, "polygon": [[129.65625, 305.25], [303.75, 305.25], [303.75, 314.*********], [129.65625, 314.*********]]}, {"title": "Exercise 3.20 Class conditional densities for binary data", "heading_level": null, "page_id": 29, "polygon": [[129.75, 500.25], [336.375, 500.25], [336.375, 509.4140625], [129.75, 509.4140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 30], ["SectionHeader", 3], ["TextInlineMath", 3], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8748, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 437], ["Line", 42], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 47], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 52], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 72], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 66], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1066, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 34], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 900, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1112, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["Line", 50], ["TextInlineMath", 5], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 55], ["Equation", 5], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 943, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 46], ["Text", 9], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 938, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 42], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 53], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 58], ["Equation", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 928, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 56], ["Equation", 8], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 46], ["Text", 7], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["TableCell", 54], ["Line", 45], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Table", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1062, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 58], ["Text", 8], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 56], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 824, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 67], ["Equation", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 63], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 55], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["TableCell", 72], ["Line", 47], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 543], ["Line", 55], ["Text", 9], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 60], ["Text", 9], ["Equation", 7], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 47], ["ListItem", 8], ["Equation", 5], ["TextInlineMath", 4], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["Line", 44], ["ListItem", 9], ["Text", 5], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 650], ["Line", 46], ["Text", 8], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 537], ["Line", 56], ["Equation", 7], ["Text", 5], ["ListItem", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 25], ["ListItem", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-4"}