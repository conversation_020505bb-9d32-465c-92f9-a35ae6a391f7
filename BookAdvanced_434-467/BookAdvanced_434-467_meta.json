{"table_of_contents": [{"title": "Bibliographic Notes", "heading_level": null, "page_id": 0, "polygon": [[132.0, 110.8916015625], [255.0498046875, 110.8916015625], [255.0498046875, 124.0400390625], [132.0, 124.0400390625]]}, {"title": "Exercises", "heading_level": null, "page_id": 0, "polygon": [[132.75, 369.0], [190.2041015625, 369.0], [190.2041015625, 381.69140625], [132.75, 381.69140625]]}, {"title": "Ex. 11.5", "heading_level": null, "page_id": 0, "polygon": [[132.2314453125, 607.5], [170.1826171875, 607.5], [170.1826171875, 616.81640625], [132.2314453125, 616.81640625]]}, {"title": "416 Neural Networks", "heading_level": null, "page_id": 1, "polygon": [[132.0, 88.5], [234.0, 88.5], [234.0, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "12", "heading_level": null, "page_id": 2, "polygon": [[132.0, 108.75], [201.41015625, 108.75], [201.41015625, 144.052734375], [132.0, 144.052734375]]}, {"title": "Support Vector Machines and\nFlexible Discriminants", "heading_level": null, "page_id": 2, "polygon": [[132.0, 141.0], [381.603515625, 141.0], [381.603515625, 181.6611328125], [132.0, 181.6611328125]]}, {"title": "12.1 Introduction", "heading_level": null, "page_id": 2, "polygon": [[132.75, 354.75], [250.41796875, 354.75], [250.41796875, 367.576171875], [132.75, 367.576171875]]}, {"title": "12.2 The Support Vector Classifier", "heading_level": null, "page_id": 2, "polygon": [[132.75, 577.37109375], [356.25, 577.37109375], [356.25, 590.51953125], [132.75, 590.51953125]]}, {"title": "420 12. Flexible Discriminants", "heading_level": null, "page_id": 5, "polygon": [[132.0, 88.5], [277.5, 88.5], [277.5, 99.2900390625], [132.0, 99.2900390625]]}, {"title": "12.2.1 Computing the Support Vector Classifier", "heading_level": null, "page_id": 5, "polygon": [[133.5, 111.0], [385.5, 111.0], [385.5, 123.0732421875], [133.5, 123.0732421875]]}, {"title": "12.2.2 Mixture Example (Continued)", "heading_level": null, "page_id": 6, "polygon": [[133.5, 405.75], [330.50390625, 405.75], [330.50390625, 416.8828125], [133.5, 416.8828125]]}, {"title": "12.3 Support Vector Machines and Kernels", "heading_level": null, "page_id": 8, "polygon": [[132.0, 111.0], [407.6015625, 111.0], [407.6015625, 123.6533203125], [132.0, 123.6533203125]]}, {"title": "12.3.1 Computing the SVM for Classification", "heading_level": null, "page_id": 8, "polygon": [[133.5, 414.0], [374.1328125, 414.0], [374.1328125, 425.00390625], [133.5, 425.00390625]]}, {"title": "424 12. Flexible Discriminants", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [277.5, 88.5], [277.5, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "12.3.2 The SVM as a Penalization Method", "heading_level": null, "page_id": 11, "polygon": [[133.5, 453.75], [362.1796875, 453.75], [362.1796875, 465.609375], [133.5, 465.609375]]}, {"title": "428 12. Flexible Discriminants", "heading_level": null, "page_id": 13, "polygon": [[132.0, 88.5], [277.611328125, 88.5], [277.611328125, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "12.3.3 Function Estimation and Reproducing Kernels", "heading_level": null, "page_id": 13, "polygon": [[133.5, 204.75], [417.76171875, 204.75], [417.76171875, 217.142578125], [133.5, 217.142578125]]}, {"title": "430\\,\n12. Flexible Discriminants", "heading_level": null, "page_id": 15, "polygon": [[132.0, 87.75], [278.25, 87.75], [278.25, 99.0], [132.0, 99.0]]}, {"title": "12.3.4 SVMs and the Curse of Dimensionality", "heading_level": null, "page_id": 16, "polygon": [[133.5, 297.0], [378.75, 297.0], [378.75, 309.181640625], [133.5, 309.181640625]]}, {"title": "Test Error <PERSON> − SVM with <PERSON><PERSON>", "heading_level": null, "page_id": 17, "polygon": [[232.787109375, 117.75], [377.25, 117.75], [377.25, 126.0], [232.787109375, 126.0]]}, {"title": "12.3.5 A Path Algorithm for the SVM Classifier", "heading_level": null, "page_id": 17, "polygon": [[133.5, 560.25], [389.25, 560.25], [389.25, 573.1171875], [133.5, 573.1171875]]}, {"title": "434 12. Flexible Discriminants", "heading_level": null, "page_id": 19, "polygon": [[132.0, 89.25], [277.5, 89.25], [277.5, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "12.3.6 Support Vector Machines for Regression", "heading_level": null, "page_id": 19, "polygon": [[133.5, 509.25], [382.5, 509.25], [382.5, 521.296875], [133.5, 521.296875]]}, {"title": "436 12. Flexible Discriminants", "heading_level": null, "page_id": 21, "polygon": [[132.0, 89.25], [277.5, 89.25], [277.5, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "12.3.7 Regression and Kernels", "heading_level": null, "page_id": 21, "polygon": [[133.4267578125, 440.25], [296.25, 440.25], [296.25, 451.30078125], [133.4267578125, 451.30078125]]}, {"title": "438 12. Flexible Discriminants", "heading_level": null, "page_id": 23, "polygon": [[132.75, 89.25], [277.5, 89.25], [277.5, 99.0], [132.75, 99.0]]}, {"title": "12.3.8 Discussion", "heading_level": null, "page_id": 23, "polygon": [[133.4267578125, 112.5], [233.5341796875, 112.5], [233.5341796875, 122.9765625], [133.4267578125, 122.9765625]]}, {"title": "12.4 Generalizing Linear Discriminant Analysis", "heading_level": null, "page_id": 23, "polygon": [[133.27734375, 524.390625], [433.5, 524.390625], [433.5, 537.5390625], [133.27734375, 537.5390625]]}, {"title": "440 12. Flexible Discriminants", "heading_level": null, "page_id": 25, "polygon": [[132.0, 89.25], [277.5, 89.25], [277.5, 98.806640625], [132.0, 98.806640625]]}, {"title": "12.5 Flexible Discriminant Analysis", "heading_level": null, "page_id": 25, "polygon": [[132.75, 352.5], [362.478515625, 352.5], [362.478515625, 365.44921875], [132.75, 365.44921875]]}, {"title": "444 12. Flexible Discriminants", "heading_level": null, "page_id": 29, "polygon": [[132.0, 88.5], [277.5, 88.5], [277.5, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "12.5.1 Computing the FDA Estimates", "heading_level": null, "page_id": 29, "polygon": [[133.5, 477.75], [337.078125, 477.75], [337.078125, 488.8125], [133.5, 488.8125]]}, {"title": "12.6 Penalized Discriminant Analysis", "heading_level": null, "page_id": 31, "polygon": [[132.75, 111.0], [375.92578125, 111.0], [375.92578125, 124.0400390625], [132.75, 124.0400390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 37], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 25], ["Text", 4], ["ListItem", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 60], ["Line", 24], ["SectionHeader", 4], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 588], ["Line", 104], ["Equation", 4], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 859, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 52], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 96], ["Equation", 9], ["TextInlineMath", 5], ["SectionHeader", 2], ["Text", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 562, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 46], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Line", 35], ["Span", 19], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1241, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 345], ["Line", 57], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 72], ["Equation", 4], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3322, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Line", 20], ["Span", 17], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1319, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 46], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 49], ["TableCell", 30], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5116, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 68], ["Text", 8], ["Equation", 4], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 48], ["Text", 3], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1020, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Line", 31], ["Span", 11], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1363, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["TableCell", 61], ["Line", 42], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5517, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 40], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1343, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 61], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 828, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 49], ["Text", 4], ["ListItem", 3], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 63], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 508], ["Line", 81], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3110, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 45], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 934, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 45], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 39], ["ListItem", 7], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 62], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 64], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 123], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 822, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 1416], ["Line", 601], ["TableCell", 28], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["TableCell", 148], ["Line", 38], ["SectionHeader", 2], ["TextInlineMath", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7215, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["TableCell", 96], ["Line", 48], ["ListItem", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8531, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 51], ["Text", 7], ["ListItem", 5], ["Equation", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["TextInlineMath", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 740, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 3444], ["Line", 1613], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4215, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_434-467"}