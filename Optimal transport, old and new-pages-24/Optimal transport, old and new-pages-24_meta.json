{"table_of_contents": [{"title": "First Appendix: Second differentiability of convex\nfunctions", "heading_level": null, "page_id": 0, "polygon": [[133.5, 48.0], [429.75, 48.0], [429.75, 72.703125], [133.5, 72.703125]]}, {"title": "420 14 Ricci curvature", "heading_level": null, "page_id": 4, "polygon": [[133.5, 26.176025390625], [243.9931640625, 26.176025390625], [243.9931640625, 35.698974609375], [133.5, 35.698974609375]]}, {"title": "Second Appendix: Very elementary comparison\narguments", "heading_level": null, "page_id": 7, "polygon": [[133.5, 336.75], [415.5, 336.75], [415.5, 361.388671875], [133.5, 361.388671875]]}, {"title": "Third Appendix: <PERSON><PERSON> fields forever", "heading_level": null, "page_id": 10, "polygon": [[133.5, 47.8564453125], [356.25, 47.8564453125], [356.25, 59.361328125], [133.5, 59.361328125]]}, {"title": "428 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 12, "polygon": [[133.4267578125, 25.95849609375], [243.0, 25.95849609375], [243.0, 35.52978515625], [133.4267578125, 35.52978515625]]}, {"title": "430 14 Ricci curvature", "heading_level": null, "page_id": 14, "polygon": [[133.5, 26.0068359375], [243.0, 26.0068359375], [243.0, 35.14306640625], [133.5, 35.14306640625]]}, {"title": "432 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 16, "polygon": [[133.5, 26.0068359375], [243.6943359375, 26.0068359375], [243.6943359375, 35.384765625], [133.5, 35.384765625]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 16, "polygon": [[233.25, 265.869140625], [358.892578125, 265.869140625], [358.892578125, 276.697265625], [233.25, 276.697265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 624], ["Line", 61], ["TextInlineMath", 5], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9874, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 768], ["Line", 40], ["TextInlineMath", 7], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1002, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["Line", 49], ["Equation", 7], ["TextInlineMath", 7], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 38], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 792], ["Line", 46], ["TextInlineMath", 7], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 578], ["Line", 75], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2749, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 825], ["Line", 161], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1254, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 40], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 659], ["Line", 77], ["TextInlineMath", 6], ["Equation", 4], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 952], ["Line", 84], ["TextInlineMath", 8], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["Line", 49], ["TextInlineMath", 6], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 629], ["Line", 59], ["TextInlineMath", 9], ["Equation", 4], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 925, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 54], ["Equation", 5], ["Text", 5], ["TextInlineMath", 3], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 682], ["Line", 41], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 79], ["Equation", 6], ["TextInlineMath", 5], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2017, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 767], ["Line", 130], ["TextInlineMath", 7], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 36], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 39], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 34], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-24"}