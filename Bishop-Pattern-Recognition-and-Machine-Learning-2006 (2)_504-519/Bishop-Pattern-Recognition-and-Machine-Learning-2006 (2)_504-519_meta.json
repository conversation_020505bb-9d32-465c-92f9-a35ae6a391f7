{"table_of_contents": [{"title": "484 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 0, "polygon": [[29.25, 40.5], [240.0, 40.5], [240.0, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "10.2.5 Induced factorizations", "heading_level": null, "page_id": 1, "polygon": [[136.458984375, 152.25], [305.25, 152.25], [305.25, 163.5732421875], [136.458984375, 163.5732421875]]}, {"title": "10.3. Variational Linear Regression", "heading_level": null, "page_id": 2, "polygon": [[81.2724609375, 204.75], [304.171875, 204.75], [304.171875, 217.39306640625], [81.2724609375, 217.39306640625]]}, {"title": "10.3.1 Variational distribution", "heading_level": null, "page_id": 2, "polygon": [[137.07421875, 577.5], [306.87890625, 577.5], [306.87890625, 589.25390625], [137.07421875, 589.25390625]]}, {"title": "10.3. Variational Linear Regression 487", "heading_level": null, "page_id": 3, "polygon": [[260.3671875, 40.5], [473.25, 40.5], [473.25, 51.0], [260.3671875, 51.0]]}, {"title": "488 10. APPROX<PERSON><PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 4, "polygon": [[30.0, 40.5], [240.0, 40.5], [240.0, 51.462158203125], [30.0, 51.462158203125]]}, {"title": "10.3.2 Predictive distribution", "heading_level": null, "page_id": 4, "polygon": [[138.55078125, 471.0], [303.0, 471.0], [303.0, 482.2646484375], [138.55078125, 482.2646484375]]}, {"title": "10.3.3 Lower bound", "heading_level": null, "page_id": 5, "polygon": [[138.05859375, 167.25], [256.5, 167.25], [256.5, 178.04443359375], [138.05859375, 178.04443359375]]}, {"title": "490 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 6, "polygon": [[29.25, 41.25], [240.0, 41.25], [240.0, 51.6654052734375], [29.25, 51.6654052734375]]}, {"title": "10.4. Exponential Family Distributions", "heading_level": null, "page_id": 6, "polygon": [[81.75, 246.0], [322.5, 246.0], [322.5, 259.1806640625], [81.75, 259.1806640625]]}, {"title": "10.4. Exponential Family Distributions 491", "heading_level": null, "page_id": 7, "polygon": [[247.5, 40.5], [472.5, 40.5], [472.5, 51.0963134765625], [247.5, 51.0963134765625]]}, {"title": "10.4.1 Variational message passing", "heading_level": null, "page_id": 7, "polygon": [[137.8125, 578.25], [339.75, 578.25], [339.75, 589.25390625], [137.8125, 589.25390625]]}, {"title": "492 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 8, "polygon": [[29.25, 40.5], [240.0, 40.5], [240.0, 51.4215087890625], [29.25, 51.4215087890625]]}, {"title": "10.5. Local Variational Methods", "heading_level": null, "page_id": 9, "polygon": [[81.333984375, 79.5], [282.0234375, 78.75], [282.0234375, 92.924560546875], [81.333984375, 92.924560546875]]}, {"title": "494 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 10, "polygon": [[29.25, 40.5], [241.41796875, 40.5], [241.41796875, 51.299560546875], [29.25, 51.299560546875]]}, {"title": "496 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 12, "polygon": [[29.25, 40.5], [241.91015625, 40.5], [241.91015625, 51.2589111328125], [29.25, 51.2589111328125]]}, {"title": "498 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 14, "polygon": [[30.0, 40.5], [240.0, 40.5], [240.0, 51.3402099609375], [30.0, 51.3402099609375]]}, {"title": "10.6. Variational Logistic Regression", "heading_level": null, "page_id": 14, "polygon": [[81.75, 145.5], [313.5234375, 145.5], [313.5234375, 158.85791015625], [81.75, 158.85791015625]]}, {"title": "10.6.1 Variational posterior distribution", "heading_level": null, "page_id": 14, "polygon": [[137.56640625, 301.5], [359.25, 301.5], [359.25, 312.5126953125], [137.56640625, 312.5126953125]]}, {"title": "10.6. Variational Logistic Regression 499", "heading_level": null, "page_id": 15, "polygon": [[257.25, 40.5], [473.25, 40.5], [473.25, 50.7711181640625], [257.25, 50.7711181640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 48], ["Text", 6], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 6273, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 40], ["Text", 5], ["Equation", 4], ["TextInlineMath", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 56], ["Equation", 9], ["Text", 6], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3489, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 50], ["Equation", 6], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1263, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 64], ["Equation", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 52], ["Text", 4], ["SectionHeader", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 51], ["Equation", 7], ["Text", 7], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 48], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 53], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Equation", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1423, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 40], ["Equation", 4], ["TextInlineMath", 4], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 38], ["Equation", 7], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 62], ["Text", 5], ["Equation", 5], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 44], ["Text", 7], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 52], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1049, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 537], ["Line", 72], ["Equation", 7], ["Text", 6], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3301, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_504-519"}