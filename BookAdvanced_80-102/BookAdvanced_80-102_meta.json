{"table_of_contents": [{"title": "3.3.4 Prostate Cancer Data Example (Continued)", "heading_level": null, "page_id": 0, "polygon": [[132.75, 112.5], [395.25, 112.5], [395.25, 123.4599609375], [132.75, 123.4599609375]]}, {"title": "3.4 Shrinkage Methods", "heading_level": null, "page_id": 0, "polygon": [[132.0, 479.91796875], [283.5, 479.91796875], [283.5, 493.06640625], [132.0, 493.06640625]]}, {"title": "3.4.1 Ridge Regression", "heading_level": null, "page_id": 0, "polygon": [[132.0, 609.0], [261.0, 609.0], [261.0, 620.296875], [132.0, 620.296875]]}, {"title": "64 3. Linear Methods for Regression", "heading_level": null, "page_id": 3, "polygon": [[132.0, 89.25], [301.5, 89.25], [301.5, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "66 3. Linear Methods for Regression", "heading_level": null, "page_id": 5, "polygon": [[132.0, 89.25], [301.5, 89.25], [301.5, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "68 3. Linear Methods for Regression", "heading_level": null, "page_id": 7, "polygon": [[132.0, 89.25], [301.5, 89.25], [301.5, 98.61328125], [132.0, 98.61328125]]}, {"title": "3.4.2 The Lasso", "heading_level": null, "page_id": 7, "polygon": [[132.75, 366.75], [224.25, 366.75], [224.25, 378.017578125], [132.75, 378.017578125]]}, {"title": "3.4.3 Discussion: Subset Selection, Ridge Regression and the\nLasso", "heading_level": null, "page_id": 8, "polygon": [[133.5, 438.75], [452.25, 438.75], [452.25, 464.44921875], [133.5, 464.44921875]]}, {"title": "72 3. Linear Methods for Regression", "heading_level": null, "page_id": 11, "polygon": [[132.75, 88.5], [301.81640625, 88.5], [301.81640625, 98.46826171875], [132.75, 98.46826171875]]}, {"title": "3.4.4 Least Angle Regression", "heading_level": null, "page_id": 12, "polygon": [[132.0, 417.0], [288.0703125, 417.0], [288.0703125, 428.87109375], [132.0, 428.87109375]]}, {"title": "74 3. Linear Methods for Regression", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [301.517578125, 89.25], [301.517578125, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "Algorithm 3.2 Least Angle Regression.", "heading_level": null, "page_id": 13, "polygon": [[132.8291015625, 184.5], [309.75, 184.5], [309.75, 194.51953125], [132.8291015625, 194.51953125]]}, {"title": "76 3. Linear Methods for Regression", "heading_level": null, "page_id": 15, "polygon": [[132.0, 89.25], [301.5, 89.25], [301.5, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "Algorithm 3.2a Least Angle Regression: Lasso Modification.", "heading_level": null, "page_id": 15, "polygon": [[133.5, 246.0], [402.22265625, 246.0], [402.22265625, 255.427734375], [133.5, 255.427734375]]}, {"title": "Degrees-of-Freedom Formula for LAR and Lasso", "heading_level": null, "page_id": 16, "polygon": [[132.75, 341.25], [345.0, 341.25], [345.0, 351.0], [132.75, 351.0]]}, {"title": "3.5 Methods Using Derived Input Directions", "heading_level": null, "page_id": 18, "polygon": [[132.0, 221.25], [417.1640625, 221.25], [417.1640625, 234.544921875], [132.0, 234.544921875]]}, {"title": "3.5.1 Principal Components Regression", "heading_level": null, "page_id": 18, "polygon": [[132.0, 325.5], [342.45703125, 325.5], [342.45703125, 336.638671875], [132.0, 336.638671875]]}, {"title": "3.5.2 Partial Least Squares", "heading_level": null, "page_id": 19, "polygon": [[132.45556640625, 391.5], [279.75, 391.5], [279.75, 403.34765625], [132.45556640625, 403.34765625]]}, {"title": "82 3. Linear Methods for Regression", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [301.517578125, 88.5], [301.517578125, 98.5166015625], [132.0, 98.5166015625]]}, {"title": "3.6 Discussion: A Comparison of the Selection and\nShrinkage Methods", "heading_level": null, "page_id": 21, "polygon": [[131.9326171875, 193.5], [453.75, 194.25], [453.75, 222.75], [131.9326171875, 222.75]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 40], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4552, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 99], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["TableCell", 168], ["Line", 69], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 45], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 143], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1075, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 567], ["Line", 57], ["TextInlineMath", 6], ["Equation", 4], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1145, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 494], ["Line", 218], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1128, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 74], ["Text", 3], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2077, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 43], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 22], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 805, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 26], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 844, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 54], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 43], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 43], ["ListItem", 5], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 2], ["Equation", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 39], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 48], ["Text", 7], ["Equation", 3], ["SectionHeader", 2], ["ListItem", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 49], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 38], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 906, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 44], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 731, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 532], ["Line", 36], ["ListItem", 7], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 37], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 37], ["Figure", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2916, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_80-102"}