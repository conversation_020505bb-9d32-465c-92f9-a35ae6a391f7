{"table_of_contents": [{"title": "404 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 0, "polygon": [[29.25, 40.5], [205.5, 40.5], [205.5, 51.9906005859375], [29.25, 51.9906005859375]]}, {"title": "8.4. Inference in Graphical Models 405", "heading_level": null, "page_id": 1, "polygon": [[263.07421875, 41.25], [473.25, 41.25], [473.25, 51.0150146484375], [263.07421875, 51.0150146484375]]}, {"title": "406 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 2, "polygon": [[29.25, 40.5], [204.75, 40.5], [204.75, 51.949951171875], [29.25, 51.949951171875]]}, {"title": "408 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 4, "polygon": [[29.25, 40.5], [205.5, 40.5], [205.5, 51.787353515625], [29.25, 51.787353515625]]}, {"title": "8.4. Inference in Graphical Models 409", "heading_level": null, "page_id": 5, "polygon": [[264.0, 39.75], [474.0, 39.75], [474.0, 51.0], [264.0, 51.0]]}, {"title": "8.4.5 The max-sum algorithm", "heading_level": null, "page_id": 7, "polygon": [[138.75, 177.75], [306.0, 177.75], [306.0, 188.61328125], [138.75, 188.61328125]]}, {"title": "412 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 8, "polygon": [[29.25, 40.5], [205.5, 40.5], [205.5, 51.5841064453125], [29.25, 51.5841064453125]]}, {"title": "8.4. Inference in Graphical Models 413", "heading_level": null, "page_id": 9, "polygon": [[264.75, 41.25], [474.0, 41.25], [474.0, 51.624755859375], [264.75, 51.624755859375]]}, {"title": "414 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 10, "polygon": [[29.25, 40.5], [205.5, 40.5], [205.5, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "8.4.6 Exact inference in general graphs", "heading_level": null, "page_id": 12, "polygon": [[137.25, 71.25], [360.28125, 71.25], [360.28125, 83.656494140625], [137.25, 83.656494140625]]}, {"title": "8.4.7 Loopy belief propagation", "heading_level": null, "page_id": 13, "polygon": [[138.0, 177.0], [313.5, 177.0], [313.5, 188.2880859375], [138.0, 188.2880859375]]}, {"title": "418 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 14, "polygon": [[29.25, 40.5], [204.75, 40.5], [204.75, 51.9093017578125], [29.25, 51.9093017578125]]}, {"title": "8.4.8 Learning the graph structure", "heading_level": null, "page_id": 14, "polygon": [[136.458984375, 271.5], [333.0, 271.5], [333.0, 283.08251953125], [136.458984375, 283.08251953125]]}, {"title": "Exercises", "heading_level": null, "page_id": 14, "polygon": [[30.0, 528.75], [91.5, 528.75], [91.5, 542.42578125], [30.0, 542.42578125]]}, {"title": "420 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 16, "polygon": [[29.25, 40.5], [205.5, 40.5], [205.5, 52.2344970703125], [29.25, 52.2344970703125]]}, {"title": "422 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 18, "polygon": [[29.25, 39.8770751953125], [205.5, 39.8770751953125], [205.5, 51.9093017578125], [29.25, 51.9093017578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 54], ["Equation", 4], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 6352, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 39], ["TextInlineMath", 4], ["Figure", 2], ["Equation", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1277, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 49], ["Text", 5], ["Equation", 3], ["Figure", 2], ["SectionHeader", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2718, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 46], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 45], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1215, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 39], ["Equation", 13], ["Text", 3], ["Figure", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1277, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 56], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5061, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 47], ["TableCell", 18], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 55], ["Text", 6], ["Equation", 4], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1112, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 54], ["Text", 5], ["Equation", 5], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6076, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 53], ["Equation", 4], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 649, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 47], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 45], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 42], ["Text", 5], ["SectionHeader", 3], ["ListItem", 2], ["TextInlineMath", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["TableCell", 50], ["Line", 42], ["ListItem", 7], ["Table", 2], ["ListGroup", 2], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2369, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 35], ["ListItem", 6], ["Text", 4], ["Equation", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 41], ["ListItem", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 23], ["ListItem", 5], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_424-442"}