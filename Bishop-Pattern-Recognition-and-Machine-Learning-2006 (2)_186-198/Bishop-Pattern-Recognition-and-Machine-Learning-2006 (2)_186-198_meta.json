{"table_of_contents": [{"title": "166 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 0, "polygon": [[30.0, 40.5], [279.80859375, 40.5], [279.80859375, 51.5028076171875], [30.0, 51.5028076171875]]}, {"title": "3.5.1 Evaluation of the evidence function", "heading_level": null, "page_id": 0, "polygon": [[137.56640625, 412.5], [368.40234375, 414.0], [368.40234375, 425.0302734375], [137.56640625, 425.0302734375]]}, {"title": "3.5. The Evidence Approximation 167", "heading_level": null, "page_id": 1, "polygon": [[270.0, 41.25], [473.25, 41.25], [473.25, 51.0556640625], [270.0, 51.0556640625]]}, {"title": "168 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 2, "polygon": [[30.0, 40.5], [278.0859375, 40.5], [278.0859375, 51.787353515625], [30.0, 51.787353515625]]}, {"title": "3.5.2 Maximizing the evidence function", "heading_level": null, "page_id": 2, "polygon": [[137.07421875, 443.25], [359.25, 442.5], [359.25, 454.623046875], [137.07421875, 454.623046875]]}, {"title": "3.5. The Evidence Approximation 169", "heading_level": null, "page_id": 3, "polygon": [[270.0, 41.25], [473.25, 41.25], [473.25, 51.0], [270.0, 51.0]]}, {"title": "170 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 4, "polygon": [[30.0, 41.25], [277.5, 41.25], [277.5, 51.462158203125], [30.0, 51.462158203125]]}, {"title": "3.5.3 Effective number of parameters", "heading_level": null, "page_id": 4, "polygon": [[137.25, 261.0], [348.0, 261.0], [348.0, 272.02587890625], [137.25, 272.02587890625]]}, {"title": "172 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 6, "polygon": [[30.0, 40.5], [278.33203125, 40.5], [278.33203125, 51.3402099609375], [30.0, 51.3402099609375]]}, {"title": "3.6. Limitations of Fixed Basis Functions", "heading_level": null, "page_id": 6, "polygon": [[88.5, 528.75], [347.25, 528.0], [347.25, 542.7509765625], [88.5, 542.7509765625]]}, {"title": "Exercises", "heading_level": null, "page_id": 7, "polygon": [[30.75, 411.0], [92.25, 411.0], [92.25, 423.0791015625], [30.75, 423.0791015625]]}, {"title": "174 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 8, "polygon": [[30.0, 40.5], [278.0859375, 40.5], [278.0859375, 51.462158203125], [30.0, 51.462158203125]]}, {"title": "176 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 10, "polygon": [[30.0, 40.5], [278.0859375, 40.5], [278.0859375, 51.624755859375], [30.0, 51.624755859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 44], ["Text", 7], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3500, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 65], ["Text", 10], ["Equation", 8], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2245, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 63], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 648, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 75], ["Text", 7], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 47], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 53], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 42], ["Text", 3], ["SectionHeader", 2], ["Figure", 2], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1430, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 51], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 51], ["Text", 7], ["Equation", 6], ["ListItem", 5], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 54], ["ListItem", 6], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 39], ["ListItem", 7], ["Text", 5], ["Equation", 4], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 41], ["ListItem", 5], ["Text", 4], ["Equation", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_186-198"}