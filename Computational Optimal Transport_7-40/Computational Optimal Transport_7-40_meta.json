{"table_of_contents": [{"title": "1", "heading_level": null, "page_id": 0, "polygon": [[303.8739290085679, 135.953125], [322.36376953125, 135.953125], [322.36376953125, 164.5341796875], [303.8739290085679, 164.5341796875]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[268.609547123623, 179.9431279620853], [353.7919921875, 179.9431279620853], [353.7919921875, 196.205078125], [268.609547123623, 196.205078125]]}, {"title": "Notation", "heading_level": null, "page_id": 2, "polygon": [[105.04283965728274, 155.95071090047392], [154.56303549571604, 155.95071090047392], [154.56303549571604, 167.23779296875], [105.04283965728274, 167.23779296875]]}, {"title": "2", "heading_level": null, "page_id": 4, "polygon": [[303.1236230110159, 135.8565673828125], [322.36376953125, 135.8565673828125], [322.36376953125, 160.9615478515625], [303.1236230110159, 160.9615478515625]]}, {"title": "Theoretical Foundations", "heading_level": null, "page_id": 4, "polygon": [[226.432861328125, 180.69289099526065], [394.6609547123623, 180.69289099526065], [394.6609547123623, 195.625732421875], [226.432861328125, 195.625732421875]]}, {"title": "2.1 Histograms and Measures", "heading_level": null, "page_id": 4, "polygon": [[103.5422276621787, 529.908203125], [278.36352509179926, 529.908203125], [278.36352509179926, 540.72265625], [103.5422276621787, 540.72265625]]}, {"title": "2.2 Assignment and Monge Problem", "heading_level": null, "page_id": 6, "polygon": [[103.5422276621787, 316.4], [315.1285189718482, 316.4], [315.1285189718482, 328.68212890625], [103.5422276621787, 328.68212890625]]}, {"title": "2.2. Assignment and Monge Problem 11 and 2.2. Assignment and Monge Problem 11", "heading_level": null, "page_id": 8, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 104.089111328125], [105.04283965728274, 104.089111328125]]}, {"title": "2.3 <PERSON><PERSON><PERSON>ich Relaxation", "heading_level": null, "page_id": 10, "polygon": [[104.29253365973072, 382.37914691943126], [263.3574051407589, 382.37914691943126], [263.3574051407589, 394.534423828125], [104.29253365973072, 394.534423828125]]}, {"title": "2.3. <PERSON><PERSON><PERSON><PERSON> Relaxation 15", "heading_level": null, "page_id": 12, "polygon": [[104.29253365973072, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.702880859375], [104.29253365973072, 103.702880859375]]}, {"title": "2.4 Metric Properties of Optimal Transport", "heading_level": null, "page_id": 16, "polygon": [[104.29253365973072, 274.41327014218007], [353.3941248470012, 274.41327014218007], [353.3941248470012, 285.617431640625], [104.29253365973072, 285.617431640625]]}, {"title": "2.5 Dual Problem", "heading_level": null, "page_id": 20, "polygon": [[104.29253365973072, 275.16303317535545], [212.664306640625, 275.16303317535545], [212.664306640625, 287.162353515625], [104.29253365973072, 287.162353515625]]}, {"title": "2.5. Dual Problem 25", "heading_level": null, "page_id": 22, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.89599609375], [105.04283965728274, 103.89599609375]]}, {"title": "Proposition 2.5. One has", "heading_level": null, "page_id": 23, "polygon": [[115.5361328125, 418.36777251184833], [240.84822521419827, 418.36777251184833], [240.84822521419827, 429.10205078125], [115.5361328125, 429.10205078125]]}, {"title": "2.5. Dual Problem 27", "heading_level": null, "page_id": 24, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 104.089111328125], [105.04283965728274, 104.089111328125]]}, {"title": "2.5. Dual Problem 29", "heading_level": null, "page_id": 26, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.9925537109375], [105.04283965728274, 103.9925537109375]]}, {"title": "2.6 Special Cases", "heading_level": null, "page_id": 27, "polygon": [[104.29253365973072, 124.460663507109], [210.41943359375, 124.460663507109], [210.41943359375, 136.2427978515625], [104.29253365973072, 136.2427978515625]]}, {"title": "2.6. Special Cases 33", "heading_level": null, "page_id": 30, "polygon": [[104.29253365973072, 92.22085308056872], [518.4614443084455, 92.22085308056872], [518.4614443084455, 103.509765625], [104.29253365973072, 103.509765625]]}, {"title": "2.6. Special Cases 35", "heading_level": null, "page_id": 32, "polygon": [[104.29253365973072, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 103.6063232421875], [104.29253365973072, 103.6063232421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 47], ["Line", 26], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4456, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 29], ["ListItem", 20], ["Text", 1], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 16], ["ListItem", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 29], ["SectionHeader", 3], ["Text", 3], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["Line", 47], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 40], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 52], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 42], ["TextInlineMath", 5], ["Equation", 3], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 50], ["TextInlineMath", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["Line", 67], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 52], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 41], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 750, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 44], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1560, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 556], ["Line", 42], ["TextInlineMath", 5], ["Equation", 3], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["Line", 143], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3257, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 35], ["Text", 3], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["Line", 51], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 56], ["Text", 7], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 42], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 66], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3187, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 41], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 46], ["TextInlineMath", 7], ["Equation", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 58], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 35], ["TextInlineMath", 3], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 42], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 36], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 65], ["Equation", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 92], ["Text", 3], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2172, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 56], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 40], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 14], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1286, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_7-40"}