{"table_of_contents": [{"title": "Meta-Learning in Neural Networks: A Survey", "heading_level": null, "page_id": 0, "polygon": [[68.1328125, 54.96240234375], [544.5, 54.96240234375], [544.5, 79.61572265625], [68.1328125, 79.61572265625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 285.0], [140.25, 285.0], [140.25, 296.2265625], [48.0, 296.2265625]]}, {"title": "2 BACKGROUND", "heading_level": null, "page_id": 1, "polygon": [[47.25, 211.5], [136.564453125, 211.5], [136.564453125, 221.58984375], [47.25, 221.58984375]]}, {"title": "2.1 Formalizing Meta-Learning", "heading_level": null, "page_id": 1, "polygon": [[45.75, 609.75], [192.0, 611.25], [192.0, 621.0703125], [45.75, 621.0703125]]}, {"title": "2.2 Historical Context of Meta-Learning", "heading_level": null, "page_id": 2, "polygon": [[310.5, 176.25], [495.75, 176.25], [495.75, 185.625], [310.5, 185.625]]}, {"title": "2.3 Related Fields", "heading_level": null, "page_id": 2, "polygon": [[311.25, 438.75], [399.75, 438.75], [399.75, 448.20703125], [311.25, 448.20703125]]}, {"title": "3 TAXONOMY", "heading_level": null, "page_id": 3, "polygon": [[311.25, 278.25], [386.68359375, 278.25], [386.68359375, 289.265625], [311.25, 289.265625]]}, {"title": "3.1 Previous Taxonomies", "heading_level": null, "page_id": 3, "polygon": [[309.5859375, 297.0], [432.75, 297.0], [432.75, 307.44140625], [309.5859375, 307.44140625]]}, {"title": "3.2 Proposed Taxonomy", "heading_level": null, "page_id": 4, "polygon": [[45.75, 366.75], [164.25, 366.75], [164.25, 376.*********], [45.75, 376.*********]]}, {"title": "4 SURVEY: METHODOLOGIES", "heading_level": null, "page_id": 4, "polygon": [[47.21484375, 708.75], [200.25, 708.75], [200.25, 719.296875], [47.21484375, 719.296875]]}, {"title": "4.1 Meta-Representation", "heading_level": null, "page_id": 4, "polygon": [[310.5, 43.5], [429.75, 43.5], [429.75, 53.*********], [310.5, 53.*********]]}, {"title": "4.2 Meta-Optimizer", "heading_level": null, "page_id": 7, "polygon": [[46.5, 416.49609375], [140.25, 416.49609375], [140.25, 426.55078125], [46.5, 426.55078125]]}, {"title": "4.3 Meta-Objective and Episode Design", "heading_level": null, "page_id": 7, "polygon": [[311.25, 425.25], [496.5, 425.25], [496.5, 435.83203125], [311.25, 435.83203125]]}, {"title": "5 APPLICATIONS", "heading_level": null, "page_id": 8, "polygon": [[47.25, 691.453125], [140.0009765625, 691.453125], [140.0009765625, 702.28125], [47.25, 702.28125]]}, {"title": "5.1 Computer Vision and Graphics", "heading_level": null, "page_id": 8, "polygon": [[309.5859375, 298.5], [474.75, 298.5], [474.75, 308.21484375], [309.5859375, 308.21484375]]}, {"title": "5.1.1 Few-Shot Learning Methods", "heading_level": null, "page_id": 8, "polygon": [[311.25, 368.9296875], [462.75, 368.9296875], [462.75, 378.59765625], [311.25, 378.59765625]]}, {"title": "5.1.2 Few-Shot Learning Benchmarks", "heading_level": null, "page_id": 9, "polygon": [[46.5, 346.5], [215.25, 346.5], [215.25, 356.748046875], [46.5, 356.748046875]]}, {"title": "5.2 Meta Reinforcement Learning and Robotics", "heading_level": null, "page_id": 9, "polygon": [[310.5, 138.0], [531.75, 138.0], [531.75, 148.4033203125], [310.5, 148.4033203125]]}, {"title": "5.2.1 Methods", "heading_level": null, "page_id": 9, "polygon": [[311.25, 382.46484375], [380.25, 382.46484375], [380.25, 392.90625], [311.25, 392.90625]]}, {"title": "5.2.2 Benchmarks", "heading_level": null, "page_id": 10, "polygon": [[46.5, 627.2578125], [132.75, 627.2578125], [132.75, 637.3125], [46.5, 637.3125]]}, {"title": "5.3 Environment Learning and Sim2Real", "heading_level": null, "page_id": 10, "polygon": [[310.5, 652.0078125], [501.134765625, 652.0078125], [501.134765625, 662.0625], [310.5, 662.0625]]}, {"title": "5.4 Neural Architecture Search (NAS)", "heading_level": null, "page_id": 11, "polygon": [[45.75, 193.5], [222.75, 193.5], [222.75, 204.1875], [45.75, 204.1875]]}, {"title": "5.5 Bayesian Meta-learning", "heading_level": null, "page_id": 11, "polygon": [[46.5, 664.5], [176.30859375, 664.5], [176.30859375, 674.05078125], [46.5, 674.05078125]]}, {"title": "5.6 Unsupervised Meta-Learning", "heading_level": null, "page_id": 11, "polygon": [[310.5, 381.69140625], [466.171875, 381.69140625], [466.171875, 391.359375], [310.5, 391.359375]]}, {"title": "5.7 Continual, Online and Adaptive Learning", "heading_level": null, "page_id": 12, "polygon": [[45.75, 104.02734375], [255.0, 104.02734375], [255.0, 114.85546875], [45.75, 114.85546875]]}, {"title": "5.8 Domain Adaptation and Domain Generalization", "heading_level": null, "page_id": 12, "polygon": [[45.75, 602.5078125], [284.25, 602.5078125], [284.25, 613.3359375], [45.75, 613.3359375]]}, {"title": "5.9 Hyper-parameter Optimization", "heading_level": null, "page_id": 12, "polygon": [[310.5, 183.69140625], [471.75, 183.69140625], [471.75, 194.90625], [310.5, 194.90625]]}, {"title": "5.10 Novel and Biologically Plausible Learners", "heading_level": null, "page_id": 12, "polygon": [[310.5, 427.32421875], [529.5, 427.32421875], [529.5, 438.15234375], [310.5, 438.15234375]]}, {"title": "5.11 Language and Speech", "heading_level": null, "page_id": 12, "polygon": [[310.5, 542.953125], [441.0, 542.953125], [441.0, 553.78125], [310.5, 553.78125]]}, {"title": "5.12 Meta-learning for Social Good", "heading_level": null, "page_id": 13, "polygon": [[45.75, 43.5], [212.25, 43.5], [212.25, 53.80224609375], [45.75, 53.80224609375]]}, {"title": "5.13 Abstract Reasoning", "heading_level": null, "page_id": 13, "polygon": [[46.5, 234.75], [165.75, 234.75], [165.75, 244.599609375], [46.5, 244.599609375]]}, {"title": "5.14 Systems", "heading_level": null, "page_id": 13, "polygon": [[45.75, 390.0], [116.25, 390.0], [116.25, 400.25390625], [45.75, 400.25390625]]}, {"title": "6 CHALLENGES AND OPEN QUESTIONS", "heading_level": null, "page_id": 13, "polygon": [[311.25, 391.5], [514.880859375, 391.5], [514.880859375, 402.57421875], [311.25, 402.57421875]]}, {"title": "7 CONCLUSION", "heading_level": null, "page_id": 14, "polygon": [[311.25, 96.0], [397.5, 96.0], [397.5, 107.314453125], [311.25, 107.314453125]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 14, "polygon": [[311.25, 268.5], [417.75, 268.5], [417.75, 278.630859375], [311.25, 278.630859375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 14, "polygon": [[311.25, 360.0], [379.5, 360.0], [379.5, 370.283203125], [311.25, 370.283203125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 94], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7482, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 695], ["Line", 128], ["Text", 8], ["TextInlineMath", 6], ["Reference", 5], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 684], ["Line", 143], ["Text", 8], ["TextInlineMath", 5], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 117], ["Text", 9], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 117], ["Text", 13], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 129], ["Text", 6], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 924, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 120], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 119], ["Text", 11], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["TableCell", 126], ["Line", 92], ["Text", 11], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8912, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 117], ["Text", 12], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 119], ["Text", 10], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 118], ["Text", 10], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 115], ["Text", 12], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 116], ["Text", 12], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 125], ["ListItem", 17], ["Reference", 15], ["Text", 8], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 616], ["Line", 154], ["ListItem", 66], ["Reference", 65], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 153], ["ListItem", 61], ["Reference", 61], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["Line", 154], ["ListItem", 60], ["Reference", 60], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 609], ["Line", 153], ["ListItem", 59], ["Reference", 56], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 398], ["Line", 113], ["ListItem", 36], ["Reference", 36], ["Picture", 4], ["Text", 4], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2312, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Meta-Learning in Neural Networks- A Survey"}