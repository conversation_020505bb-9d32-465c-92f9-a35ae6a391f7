Image /page/0/Picture/0 description: The image is a title slide with the number 10 in large red font at the top. Below the number 10, the words "Approximate Inference" are written in large black font. The background of the slide is a blurry, abstract pattern that resembles rippling water or molten metal, with shades of gold, brown, and white.

# **10.1. Variational Inference**

analytical solutions, while the dimensionality of the space and the complexity of the integrand may prohibit numerical integration. For discrete variables, the marginalizations involve summing over all possible configurations of the hidden variables, and though this is always possible in principle, we often find in practice that there may be exponentially many hidden states so that exact calculation is prohibitively expensive.

In such situations, we need to resort to approximation schemes, and these fall broadly into two classes, according to whether they rely on stochastic or deterministic approximations. Stochastic techniques such as Markov chain Monte Carlo, described in Chapter 11, have enabled the widespread use of Bayesian methods across many domains. They generally have the property that given infinite computational resource, they can generate exact results, and the approximation arises from the use of a finite amount of processor time. In practice, sampling methods can be computationally demanding, often limiting their use to small-scale problems. Also, it can be difficult to know whether a sampling scheme is generating independent samples from the required distribution.

In this chapter, we introduce a range of deterministic approximation schemes, some of which scale well to large applications. These are based on analytical approximations to the posterior distribution, for example by assuming that it factorizes in a particular way or that it has a specific parametric form such as a Gaussian. As such, they can never generate exact results, and so their strengths and weaknesses are complementary to those of sampling methods.

In Section 4.4, we discussed the Laplace approximation, which is based on a local Gaussian approximation to a mode (i.e., a maximum) of the distribution. Here we turn to a family of approximation techniques called *variational inference* or *variational Bayes*, which use more global criteria and which have been widely applied. We conclude with a brief introduction to an alternative variational framework known as *expectation propagation*.

# **10.1. Variational Inference**

Variational methods have their origins in the  $18<sup>th</sup>$  century with the work of Euler, Lagrange, and others on the *calculus of variations*. Standard calculus is concerned with finding derivatives of functions. We can think of a function as a mapping that takes the value of a variable as the input and returns the value of the function as the output. The derivative of the function then describes how the output value varies as we make infinitesimal changes to the input value. Similarly, we can define a *functional* as a mapping that takes a function as the input and that returns the value of the functional as the output. An example would be the entropy  $H[p]$ , which takes a probability distribution  $p(x)$  as the input and returns the quantity

$$
H[p] = \int p(x) \ln p(x) dx
$$
 (10.1)

as the output. We can the introduce the concept of a *functional derivative*, which expresses how the value of the functional changes in response to infinitesimal changes to the input function (Feynman *et al.*, 1964). The rules for the calculus of variations mirror those of standard calculus and are discussed in Appendix D. Many problems can be expressed in terms of an optimization problem in which the quantity being optimized is a functional. The solution is obtained by exploring all possible input functions to find the one that maximizes, or minimizes, the functional. Variational methods have broad applicability and include such areas as finite element methods (Kapur, 1989) and maximum entropy (Schwarz, 1988).

Although there is nothing intrinsically approximate about variational methods, they do naturally lend themselves to finding approximate solutions. This is done by restricting the range of functions over which the optimization is performed, for instance by considering only quadratic functions or by considering functions composed of a linear combination of fixed basis functions in which only the coefficients of the linear combination can vary. In the case of applications to probabilistic inference, the restriction may for example take the form of factorization assumptions (Jordan *et al.*, 1999; Jaakkola, 2001).

Now let us consider in more detail how the concept of variational optimization can be applied to the inference problem. Suppose we have a fully Bayesian model in which all parameters are given prior distributions. The model may also have latent variables as well as parameters, and we shall denote the set of all latent variables and parameters by **Z**. Similarly, we denote the set of all observed variables by **X**. For example, we might have a set of  $N$  independent, identically distributed data, for which  $X = \{x_1, \ldots, x_N\}$  and  $Z = \{z_1, \ldots, z_N\}$ . Our probabilistic model specifies the joint distribution  $p(X, Z)$ , and our goal is to find an approximation for the posterior distribution  $p(Z|X)$  as well as for the model evidence  $p(X)$ . As in our discussion of EM, we can decompose the log marginal probability using

$$
\ln p(\mathbf{X}) = \mathcal{L}(q) + \mathrm{KL}(q||p) \tag{10.2}
$$

where we have defined

$$
\mathcal{L}(q) = \int q(\mathbf{Z}) \ln \left\{ \frac{p(\mathbf{X}, \mathbf{Z})}{q(\mathbf{Z})} \right\} d\mathbf{Z}
$$
 (10.3)

$$
KL(q||p) = -\int q(\mathbf{Z}) \ln \left\{ \frac{p(\mathbf{Z}|\mathbf{X})}{q(\mathbf{Z})} \right\} d\mathbf{Z}.
$$
 (10.4)

This differs from our discussion of EM only in that the parameter vector *θ* no longer appears, because the parameters are now stochastic variables and are absorbed into **Z**. Since in this chapter we will mainly be interested in continuous variables we have used integrations rather than summations in formulating this decomposition. However, the analysis goes through unchanged if some or all of the variables are discrete simply by replacing the integrations with summations as required. As before, we can maximize the lower bound  $\mathcal{L}(q)$  by optimization with respect to the distribution  $q(\mathbf{Z})$ , which is equivalent to minimizing the KL divergence. If we allow any possible choice for  $q(\mathbf{Z})$ , then the maximum of the lower bound occurs when the KL divergence vanishes, which occurs when  $q(\mathbf{Z})$  equals the posterior distribution  $p(\mathbf{Z}|\mathbf{X})$ .

Image /page/3/Figure/1 description: The image contains two plots. The left plot shows three curves: a red curve peaking at approximately 0.8 at x=0, a green curve peaking at approximately 0.9 at x=1, and a yellow shaded area under the red curve. The x-axis ranges from -2 to 4, and the y-axis ranges from 0 to 1. The right plot shows three curves: a red curve, a green curve, and a yellow curve. The x-axis ranges from -2 to 4, and the y-axis ranges from 0 to 40. All three curves are U-shaped, with the red curve being the lowest, the green curve in the middle, and the yellow curve being the highest at the edges of the plot.

**Figure 10.1** Illustration of the variational approximation for the example considered earlier in Figure 4.14. The left-hand plot shows the original distribution (yellow) along with the Laplace (red) and variational (green) approximations, and the right-hand plot shows the negative logarithms of the corresponding curves.

However, we shall suppose the model is such that working with the true posterior distribution is intractable.

We therefore consider instead a restricted family of distributions  $q(\mathbf{Z})$  and then seek the member of this family for which the KL divergence is minimized. Our goal is to restrict the family sufficiently that they comprise only tractable distributions, while at the same time allowing the family to be sufficiently rich and flexible that it can provide a good approximation to the true posterior distribution. It is important to emphasize that the restriction is imposed purely to achieve tractability, and that subject to this requirement we should use as rich a family of approximating distributions as possible. In particular, there is no 'over-fitting' associated with highly flexible distributions. Using more flexible approximations simply allows us to approach the true posterior distribution more closely.

One way to restrict the family of approximating distributions is to use a parametric distribution  $q(\mathbf{Z}|\boldsymbol{\omega})$  governed by a set of parameters  $\boldsymbol{\omega}$ . The lower bound  $\mathcal{L}(q)$ then becomes a function of  $\omega$ , and we can exploit standard nonlinear optimization techniques to determine the optimal values for the parameters. An example of this approach, in which the variational distribution is a Gaussian and we have optimized with respect to its mean and variance, is shown in Figure 10.1.

## **10.1.1 Factorized distributions**

Here we consider an alternative way in which to restrict the family of distributions  $q(\mathbf{Z})$ . Suppose we partition the elements of  $\mathbf{Z}$  into disjoint groups that we denote by  $\mathbf{Z}_i$  where  $i = 1, \ldots, M$ . We then assume that the q distribution factorizes with respect to these groups, so that

$$
q(\mathbf{Z}) = \prod_{i=1}^{M} q_i(\mathbf{Z}_i).
$$
 (10.5)

## **10.1. Variational Inference 465**

It should be emphasized that we are making no further assumptions about the distribution. In particular, we place no restriction on the functional forms of the individual factors  $q_i(\mathbf{Z}_i)$ . This factorized form of variational inference corresponds to an approximation framework developed in physics called *mean field theory* (Parisi, 1988).

Amongst all distributions  $q(\mathbf{Z})$  having the form (10.5), we now seek that distribution for which the lower bound  $\mathcal{L}(q)$  is largest. We therefore wish to make a free form (variational) optimization of  $\mathcal{L}(q)$  with respect to all of the distributions  $q_i(\mathbf{Z}_i)$ , which we do by optimizing with respect to each of the factors in turn. To achieve this, we first substitute (10.5) into (10.3) and then dissect out the dependence on one of the factors  $q_i(\mathbf{Z}_i)$ . Denoting  $q_i(\mathbf{Z}_i)$  by simply  $q_i$  to keep the notation uncluttered, we then obtain

$$
\mathcal{L}(q) = \int \prod_{i} q_{i} \left\{ \ln p(\mathbf{X}, \mathbf{Z}) - \sum_{i} \ln q_{i} \right\} d\mathbf{Z}
$$
  
$$
= \int q_{j} \left\{ \int \ln p(\mathbf{X}, \mathbf{Z}) \prod_{i \neq j} q_{i} d\mathbf{Z}_{i} \right\} d\mathbf{Z}_{j} - \int q_{j} \ln q_{j} d\mathbf{Z}_{j} + \text{const}
$$
  
$$
= \int q_{j} \ln \widetilde{p}(\mathbf{X}, \mathbf{Z}_{j}) d\mathbf{Z}_{j} - \int q_{j} \ln q_{j} d\mathbf{Z}_{j} + \text{const}
$$
(10.6)

where we have defined a new distribution  $\widetilde{p}(\mathbf{X}, \mathbf{Z}_i)$  by the relation

$$
\ln \widetilde{p}(\mathbf{X}, \mathbf{Z}_j) = \mathbb{E}_{i \neq j}[\ln p(\mathbf{X}, \mathbf{Z})] + \text{const.}
$$
 (10.7)

Here the notation  $\mathbb{E}_{i\neq j}[\cdots]$  denotes an expectation with respect to the q distributions over all variables  $z_i$  for  $i \neq j$ , so that

$$
\mathbb{E}_{i \neq j}[\ln p(\mathbf{X}, \mathbf{Z})] = \int \ln p(\mathbf{X}, \mathbf{Z}) \prod_{i \neq j} q_i d\mathbf{Z}_i.
$$
 (10.8)

Now suppose we keep the  $\{q_{i\neq j}\}\$  fixed and maximize  $\mathcal{L}(q)$  in (10.6) with respect to all possible forms for the distribution  $q_i(\mathbf{Z}_i)$ . This is easily done by recognizing that (10.6) is a negative Kullback-Leibler divergence between  $q_i(\mathbf{Z}_i)$  and  $\widetilde{p}(\mathbf{X}, \mathbf{Z}_i)$ . Thus maximizing (10.6) is equivalent to minimizing the Kullback-Leibler

Image /page/4/Picture/9 description: A portrait of a man with fair skin and light brown hair, wearing a white cap and a blue and black striped robe. He is looking to the left with a slight smile. The background is dark and appears to be a wall.

Leonhard Euler 1707–1783

Euler was a Swiss mathematician and physicist who worked in St. Petersburg and Berlin and who is widely considered to be one of the greatest mathematicians of all time. He is certainly the most prolific, and

contributions, he formulated the modern theory of the function, he developed (together with Lagrange) the calculus of variations, and he discovered the formula  $e^{i\pi} = -1$ , which relates four of the most important numbers in mathematics. During the last 17 years of his life, he was almost totally blind, and yet he produced nearly half of his results during this period.

his collected works fill 75 volumes. Amongst his many

divergence, and the minimum occurs when  $q_i(\mathbf{Z}_i) = \widetilde{p}(\mathbf{X}, \mathbf{Z}_i)$ . Thus we obtain a general expression for the optimal solution  $q_j^*(\mathbf{Z}_j)$  given by

$$
\ln q_j^{\star}(\mathbf{Z}_j) = \mathbb{E}_{i \neq j}[\ln p(\mathbf{X}, \mathbf{Z})] + \text{const.}
$$
 (10.9)

It is worth taking a few moments to study the form of this solution as it provides the basis for applications of variational methods. It says that the log of the optimal solution for factor  $q_j$  is obtained simply by considering the log of the joint distribution over all hidden and visible variables and then taking the expectation with respect to all of the other factors  $\{q_i\}$  for  $i \neq j$ .

The additive constant in (10.9) is set by normalizing the distribution  $q_j^{\star}(\mathbf{Z}_j)$ .<br>s if we take the exponential of both sides and normalize we have Thus if we take the exponential of both sides and normalize, we have

$$
q_j^{\star}(\mathbf{Z}_j) = \frac{\exp(\mathbb{E}_{i 
eq j}[\ln p(\mathbf{X}, \mathbf{Z})])}{\int \exp(\mathbb{E}_{i 
eq j}[\ln p(\mathbf{X}, \mathbf{Z})]) d\mathbf{Z}_j}.
$$

In practice, we shall find it more convenient to work with the form (10.9) and then reinstate the normalization constant (where required) by inspection. This will become clear from subsequent examples.

The set of equations given by (10.9) for  $j = 1, \ldots, M$  represent a set of consistency conditions for the maximum of the lower bound subject to the factorization constraint. However, they do not represent an explicit solution because the expression on the right-hand side of (10.9) for the optimum  $q_j^*(\mathbf{Z}_j)$  depends on expectations<br>computed with respect to the other factors  $q_j(\mathbf{Z}_j)$  for  $j \neq j$ . We will therefore seek computed with respect to the other factors  $q_i(\mathbf{Z}_i)$  for  $i \neq j$ . We will therefore seek a consistent solution by first initializing all of the factors  $q_i(\mathbf{Z}_i)$  appropriately and then cycling through the factors and replacing each in turn with a revised estimate given by the right-hand side of (10.9) evaluated using the current estimates for all of the other factors. Convergence is guaranteed because bound is convex with respect to each of the factors  $q_i(\mathbf{Z}_i)$  (Boyd and Vandenberghe, 2004).

### **10.1.2 Properties of factorized approximations**

Our approach to variational inference is based on a factorized approximation to the true posterior distribution. Let us consider for a moment the problem of approximating a general distribution by a factorized distribution. To begin with, we discuss the problem of approximating a Gaussian distribution using a factorized Gaussian, which will provide useful insight into the types of inaccuracy introduced in using factorized approximations. Consider a Gaussian distribution  $p(z) = \mathcal{N}(z|\mu, \Lambda^{-1})$ <br>over two correlated variables  $z = (z_1, z_2)$  in which the mean and precision have over two correlated variables  $z = (z_1, z_2)$  in which the mean and precision have elements

$$
\boldsymbol{\mu} = \begin{pmatrix} \mu_1 \\ \mu_2 \end{pmatrix}, \qquad \boldsymbol{\Lambda} = \begin{pmatrix} \Lambda_{11} & \Lambda_{12} \\ \Lambda_{21} & \Lambda_{22} \end{pmatrix}
$$
 (10.10)

and  $\Lambda_{21} = \Lambda_{12}$  due to the symmetry of the precision matrix. Now suppose we wish to approximate this distribution using a factorized Gaussian of the form  $q(\mathbf{z}) =$  $q_1(z_1)q_2(z_2)$ . We first apply the general result (10.9) to find an expression for the

optimal factor  $q_1^*(z_1)$ . In doing so it is useful to note that on the right-hand side we only need to retain those terms that have some functional dependence on  $z_1$  because all other terms can be absorbed into the normalization constant. Thus we have

$$
\ln q_1^*(z_1) = \mathbb{E}_{z_2}[\ln p(\mathbf{z})] + \text{const}
$$
  
\n
$$
= \mathbb{E}_{z_2} \left[ -\frac{1}{2}(z_1 - \mu_1)^2 \Lambda_{11} - (z_1 - \mu_1) \Lambda_{12}(z_2 - \mu_2) \right] + \text{const}
$$
  
\n
$$
= -\frac{1}{2} z_1^2 \Lambda_{11} + z_1 \mu_1 \Lambda_{11} - z_1 \Lambda_{12} (\mathbb{E}[z_2] - \mu_2) + \text{const.} \quad (10.11)
$$

Next we observe that the right-hand side of this expression is a quadratic function of  $z_1$ , and so we can identify  $q^*(z_1)$  as a Gaussian distribution. It is worth emphasizing that we did not assume that  $q(z_i)$  is Gaussian, but rather we derived this result by variational optimization of the KL divergence over all possible distributions  $q(z<sub>i</sub>)$ . Note also that we do not need to consider the additive constant in (10.9) explicitly because it represents the normalization constant that can be found at the end by *Section 2.3.1* inspection if required. Using the technique of completing the square, we can identify the mean and precision of this Gaussian, giving

$$
q^*(z_1) = \mathcal{N}(z_1|m_1, \Lambda_{11}^{-1})
$$
 (10.12)

where

$$
m_1 = \mu_1 - \Lambda_{11}^{-1} \Lambda_{12} (\mathbb{E}[z_2] - \mu_2).
$$
 (10.13)

By symmetry,  $q_2^{\star}(z_2)$  is also Gaussian and can be written as

$$
q_2^{\star}(z_2) = \mathcal{N}(z_2|m_2, \Lambda_{22}^{-1})
$$
\n(10.14)

in which

$$
m_2 = \mu_2 - \Lambda_{22}^{-1} \Lambda_{21} (\mathbb{E}[z_1] - \mu_1).
$$
 (10.15)

Note that these solutions are coupled, so that  $q^*(z_1)$  depends on expectations computed with respect to  $q^*(z_2)$  and vice versa. In general, we address this by treating the variational solutions as re-estimation equations and cycling through the variables in turn updating them until some convergence criterion is satisfied. We shall see an example of this shortly. Here, however, we note that the problem is sufficiently simple that a closed form solution can be found. In particular, because  $\mathbb{E}[z_1] = m_1$ and  $\mathbb{E}[z_2] = m_2$ , we see that the two equations are satisfied if we take  $\mathbb{E}[z_1] = \mu_1$ and  $\mathbb{E}[z_2] = \mu_2$ , and it is easily shown that this is the only solution provided the dis-*Exercise 10.2* tribution is nonsingular. This result is illustrated in Figure 10.2(a). We see that the mean is correctly captured but that the variance of  $q(\mathbf{z})$  is controlled by the direction of smallest variance of  $p(\mathbf{z})$ , and that the variance along the orthogonal direction is significantly under-estimated. It is a general result that a factorized variational approximation tends to give approximations to the posterior distribution that are too compact.

> By way of comparison, suppose instead that we had been minimizing the reverse Kullback-Leibler divergence  $KL(p||q)$ . As we shall see, this form of KL divergence

Exercise 10.2

**Figure 10.2** Comparison of the two alternative forms for the Kullback-Leibler divergence. The green contours corresponding to 1, 2, and 3 standard deviations for a correlated Gaussian distribution  $p(z)$  over two variables  $z_1$  and  $z_2$ , and the red contours represent the corresponding levels for an approximating distribution  $a(z)$ approximating distribution over the same variables given by the product of two independent univariate Gaussian distributions whose parameters are obtained by minimization of (a) the Kullback-Leibler divergence  $KL(q||p)$ , and (b) the reverse Kullback-Leibler divergence  $KL(p||q)$ .

Image /page/7/Figure/2 description: The image displays two plots, labeled (a) and (b), each showing contour lines in a 2D space with axes labeled z1 and z2. Both plots have axes ranging from 0 to 1. Plot (a) features green elliptical contour lines that are elongated along the diagonal direction, with three nested ellipses. Superimposed on these are three concentric red circles, with the smallest circle at the center of the ellipses. Plot (b) also shows green elliptical contour lines, but these are more circular and less elongated than in plot (a). Three nested green ellipses are visible, and three concentric red circles are also present, with the smallest circle at the center of the ellipses. The red circles in plot (b) are larger than those in plot (a), indicating a wider spread.

is used in an alternative approximate inference framework called *expectation prop-Section 10.7 agation.* We therefore consider the general problem of minimizing  $KL(p||q)$  when  $q(\mathbf{Z})$  is a factorized approximation of the form (10.5). The KL divergence can then be written in the form

$$
KL(p||q) = -\int p(\mathbf{Z}) \left[ \sum_{i=1}^{M} \ln q_i(\mathbf{Z}_i) \right] d\mathbf{Z} + \text{const}
$$
 (10.16)

where the constant term is simply the entropy of  $p(\mathbf{Z})$  and so does not depend on  $q(\mathbf{Z})$ . We can now optimize with respect to each of the factors  $q_j(\mathbf{Z}_j)$ , which is *Exercise 10.3* easily done using a Lagrange multiplier to give

$$
q_j^{\star}(\mathbf{Z}_j) = \int p(\mathbf{Z}) \prod_{i \neq j} d\mathbf{Z}_i = p(\mathbf{Z}_j).
$$
 (10.17)

In this case, we find that the optimal solution for  $q_i(\mathbf{Z}_i)$  is just given by the corresponding marginal distribution of  $p(\mathbf{Z})$ . Note that this is a closed-form solution and so does not require iteration.

To apply this result to the illustrative example of a Gaussian distribution  $p(z)$ over a vector **z** we can use (2.98), which gives the result shown in Figure 10.2(b). We see that once again the mean of the approximation is correct, but that it places significant probability mass in regions of variable space that have very low probability.

The difference between these two results can be understood by noting that there is a large positive contribution to the Kullback-Leibler divergence

$$
KL(q||p) = -\int q(\mathbf{Z}) \ln \left\{ \frac{p(\mathbf{Z})}{q(\mathbf{Z})} \right\} d\mathbf{Z}
$$
 (10.18)

Image /page/8/Figure/1 description: The image displays three contour plots labeled (a), (b), and (c). Plot (a) shows overlapping elliptical contours in both red and blue, suggesting two distinct but interacting distributions. Plot (b) features two separate sets of elliptical contours, one in red and one in blue, with the red contours nested within the blue ones, indicating a clear separation and hierarchy. Plot (c) also shows two distinct sets of elliptical contours, similar to plot (b), with red contours inside blue contours, but the separation between the two sets appears slightly larger than in plot (b).

**Figure 10.3** Another comparison of the two alternative forms for the Kullback-Leibler divergence. (a) The blue contours show a bimodal distribution p(**Z**) given by a mixture of two Gaussians, and the red contours correspond to the single Gaussian distribution  $q(\mathbf{Z})$  that best approximates  $p(\mathbf{Z})$  in the sense of minimizing the Kullback-Leibler divergence  $KL(p||q)$ . (b) As in (a) but now the red contours correspond to a Gaussian distribution  $q(\mathbf{Z})$ found by numerical minimization of the Kullback-Leibler divergence  $KL(q||p)$ . (c) As in (b) but showing a different local minimum of the Kullback-Leibler divergence.

from regions of **Z** space in which  $p(\mathbf{Z})$  is near zero unless  $q(\mathbf{Z})$  is also close to zero. Thus minimizing this form of KL divergence leads to distributions  $q(\mathbf{Z})$  that avoid regions in which  $p(\mathbf{Z})$  is small. Conversely, the Kullback-Leibler divergence  $KL(p||q)$  is minimized by distributions  $q(\mathbf{Z})$  that are nonzero in regions where  $p(\mathbf{Z})$ is nonzero.

We can gain further insight into the different behaviour of the two KL divergences if we consider approximating a multimodal distribution by a unimodal one, as illustrated in Figure 10.3. In practical applications, the true posterior distribution will often be multimodal, with most of the posterior mass concentrated in some number of relatively small regions of parameter space. These multiple modes may arise through nonidentifiability in the latent space or through complex nonlinear dependence on the parameters. Both types of multimodality were encountered in Chapter 9 in the context of Gaussian mixtures, where they manifested themselves as multiple maxima in the likelihood function, and a variational treatment based on the minimization of  $KL(q||p)$  will tend to find one of these modes. By contrast, if we were to minimize  $KL(p||q)$ , the resulting approximations would average across all of the modes and, in the context of the mixture model, would lead to poor predictive distributions (because the average of two good parameter values is typically itself not a good parameter value). It is possible to make use of  $KL(p||q)$  to define a useful inference procedure, but this requires a rather different approach to the one discussed *Section 10.7* here, and will be considered in detail when we discuss expectation propagation.

*Section 10.7*

The two forms of Kullback-Leibler divergence are members of the *alpha family*

of divergences (Ali and Silvey, 1966; Amari, 1985; Minka, 2005) defined by

$$
D_{\alpha}(p||q) = \frac{4}{1 - \alpha^2} \left( 1 - \int p(x)^{(1 + \alpha)/2} q(x)^{(1 - \alpha)/2} dx \right)
$$
(10.19)

where  $-\infty < \alpha < \infty$  is a continuous parameter. The Kullback-Leibler divergence  $KL(p||q)$  corresponds to the limit  $\alpha \to 1$ , whereas  $KL(q||p)$  corresponds to the limit *Exercise 10.6*  $\alpha \to -1$ . For all values of  $\alpha$  we have  $D_{\alpha}(p||q) \ge 0$ , with equality if, and only if,  $p(x) = q(x)$ . Suppose  $p(x)$  is a fixed distribution, and we minimize  $D_{\alpha}(p||q)$  with respect to some set of distributions  $q(x)$ . Then for  $\alpha \leq -1$  the divergence is *zero forcing*, so that any values of x for which  $p(x)=0$  will have  $q(x)=0$ , and typically  $q(x)$  will under-estimate the support of  $p(x)$  and will tend to seek the mode with the largest mass. Conversely for  $\alpha \geq 1$  the divergence is *zero-avoiding*, so that values of x for which  $p(x) > 0$  will have  $q(x) > 0$ , and typically  $q(x)$  will stretch to cover all of  $p(x)$ , and will over-estimate the support of  $p(x)$ . When  $\alpha = 0$  we obtain a symmetric divergence that is linearly related to the *Hellinger distance* given by

$$
D_H(p||q) = \int (p(x)^{1/2} - q(x)^{1/2}) dx.
$$
 (10.20)

The square root of the Hellinger distance is a valid distance metric.

### **10.1.3 Example: The univariate Gaussian**

We now illustrate the factorized variational approximation using a Gaussian distribution over a single variable x (MacKay, 2003). Our goal is to infer the posterior distribution for the mean  $\mu$  and precision  $\tau$ , given a data set  $\mathcal{D} = \{x_1, \dots, x_N\}$  of observed values of  $x$  which are assumed to be drawn independently from the Gaussian. The likelihood function is given by

$$
p(\mathcal{D}|\mu,\tau) = \left(\frac{\tau}{2\pi}\right)^{N/2} \exp\left\{-\frac{\tau}{2} \sum_{n=1}^{N} (x_n - \mu)^2\right\}.
$$
 (10.21)

We now introduce conjugate prior distributions for  $\mu$  and  $\tau$  given by

$$
p(\mu|\tau) = \mathcal{N}\left(\mu|\mu_0, (\lambda_0 \tau)^{-1}\right) \tag{10.22}
$$

$$
p(\tau) = \text{Gam}(\tau | a_0, b_0) \tag{10.23}
$$

*Section 2.3.6*

where  $Gam(\tau | a_0, b_0)$  is the gamma distribution defined by (2.146). Together these *Section 2.3.6* distributions constitute a Gaussian-Gamma conjugate prior distribution.

*Exercise 2.44*

For this simple problem the posterior distribution can be found exactly, and again *Exercise 2.44* takes the form of a Gaussian-gamma distribution. However, for tutorial purposes we will consider a factorized variational approximation to the posterior distribution given by

$$
q(\mu, \tau) = q_{\mu}(\mu)q_{\tau}(\tau).
$$
 (10.24)

Note that the true posterior distribution does not factorize in this way. The optimum factors  $q_{\mu}(\mu)$  and  $q_{\tau}(\tau)$  can be obtained from the general result (10.9) as follows. For  $q_{\mu}(\mu)$  we have

$$
\ln q^{\star}_{\mu}(\mu) = \mathbb{E}_{\tau} [\ln p(\mathcal{D}|\mu, \tau) + \ln p(\mu|\tau)] + \text{const}
$$
$$
= -\frac{\mathbb{E}[\tau]}{2} \left\{ \lambda_0 (\mu - \mu_0)^2 + \sum_{n=1}^{N} (x_n - \mu)^2 \right\} + \text{const.} \quad (10.25)
$$

Completing the square over  $\mu$  we see that  $q_{\mu}(\mu)$  is a Gaussian  $\mathcal{N}(\mu|\mu_N, \lambda_N^{-1})$  with *Exercise 10.7* mean and precision given by

$$
\mu_N = \frac{\lambda_0 \mu_0 + N\overline{x}}{\lambda_0 + N} \tag{10.26}
$$

$$
\lambda_N = (\lambda_0 + N) \mathbb{E}[\tau]. \tag{10.27}
$$

Note that for  $N \to \infty$  this gives the maximum likelihood result in which  $\mu_N = \overline{x}$ and the precision is infinite.

Similarly, the optimal solution for the factor  $q_\tau(\tau)$  is given by

$$
\ln q_{\tau}^{\star}(\tau) = \mathbb{E}_{\mu} [\ln p(\mathcal{D}|\mu, \tau) + \ln p(\mu|\tau)] + \ln p(\tau) + \text{const}
$$
  
\\
$$
= (a_0 - 1) \ln \tau - b_0 \tau + \frac{N}{2} \ln \tau
$$
  

$$
-\frac{\tau}{2} \mathbb{E}_{\mu} \left[ \sum_{n=1}^{N} (x_n - \mu)^2 + \lambda_0 (\mu - \mu_0)^2 \right] + \text{const} (10.28)
$$

and hence  $q_{\tau}(\tau)$  is a gamma distribution  $Gam(\tau | a_N, b_N)$  with parameters

$$
a_N = a_0 + \frac{N}{2} \tag{10.29}
$$

$$
b_N = b_0 + \frac{1}{2} \mathbb{E}_{\mu} \left[ \sum_{n=1}^N (x_n - \mu)^2 + \lambda_0 (\mu - \mu_0)^2 \right].
$$
 (10.30)

*Exercise 10.8*

*Exercise 10.8* Again this exhibits the expected behaviour when  $N \to \infty$ .

It should be emphasized that we did not assume these specific functional forms for the optimal distributions  $q_{\mu}(\mu)$  and  $q_{\tau}(\tau)$ . They arose naturally from the structure *Section 10.4.1* of the likelihood function and the corresponding conjugate priors.

> Thus we have expressions for the optimal distributions  $q_{\mu}(\mu)$  and  $q_{\tau}(\tau)$  each of which depends on moments evaluated with respect to the other distribution. One approach to finding a solution is therefore to make an initial guess for, say, the moment  $\mathbb{E}[\tau]$  and use this to re-compute the distribution  $q_{\mu}(\mu)$ . Given this revised distribution we can then extract the required moments  $\mathbb{E}[\mu]$  and  $\mathbb{E}[\mu^2]$ , and use these to recompute the distribution  $q_{\tau}(\tau)$ , and so on. Since the space of hidden variables for this example is only two dimensional, we can illustrate the variational approximation to the posterior distribution by plotting contours of both the true posterior and the factorized approximation, as illustrated in Figure 10.4.

*Exercise 10.7*

Image /page/11/Figure/1 description: This image displays four subplots, labeled (a), (b), (c), and (d), arranged in a 2x2 grid. Each subplot shows contour plots on a 2D plane with the horizontal axis labeled 'µ' and the vertical axis labeled 'τ'. The axes range from -1 to 1 for µ and 0 to 2 for τ. Subplots (a), (b), and (c) feature green contour lines forming a triangular shape, with blue contour lines overlaid. In (a), the blue contours are clustered in the upper right quadrant. In (b), the blue contours are centered around µ=0 and τ=1.5. In (c), the blue contours are more tightly clustered around µ=0 and τ=1, with the green contours also more concentrated around this central region. Subplot (d) shows the green contour lines forming a similar triangular shape, but with red contour lines overlaid, which are more tightly packed and centered around µ=0 and τ=1, suggesting a different distribution or approximation compared to the blue contours in the other plots.

**Figure 10.4** Illustration of variational inference for the mean  $\mu$  and precision  $\tau$  of a univariate Gaussian distribution. Contours of the true posterior distribution  $p(\mu, \tau | D)$  are shown in green. (a) Contours of the initial factorized approximation  $q_\mu(\mu)q_\tau(\tau)$  are shown in blue. (b) After re-estimating the factor  $q_\mu(\mu)$ . (c) After re-estimating the factor  $q_{\tau}(\tau)$ . (d) Contours of the optimal factorized approximation, to which the iterative scheme converges, are shown in red.

In general, we will need to use an iterative approach such as this in order to solve for the optimal factorized posterior distribution. For the very simple example we are considering here, however, we can find an explicit solution by solving the simultaneous equations for the optimal factors  $q_{\mu}(\mu)$  and  $q_{\tau}(\tau)$ . Before doing this, we can simplify these expressions by considering broad, noninformative priors in which  $\mu_0 = a_0 = b_0 = \lambda_0 = 0$ . Although these parameter settings correspond to improper priors, we see that the posterior distribution is still well defined. Using the *Appendix B* standard result  $\mathbb{E}[\tau] = a_N / b_N$  for the mean of a gamma distribution, together with (10.29) and (10.30), we have

> 1  $\frac{1}{\mathbb{E}[\tau]}=\mathbb{E}$  $\lceil 1 \rceil$ N  $\sum_{i=1}^{N}$  $n=1$  $(x_n - \mu)^2$  $=\overline{x^2} - 2\overline{x}\mathbb{E}[\mu] + \mathbb{E}[\mu^2].$  (10.31)

Then, using (10.26) and (10.27), we obtain the first and second order moments of

*Appendix B*

# **10.1. Variational Inference 473**

 $q_\mu(\mu)$  in the form

$$
\mathbb{E}[\mu] = \overline{x}, \qquad \mathbb{E}[\mu^2] = \overline{x}^2 + \frac{1}{N \mathbb{E}[\tau]}.
$$
 (10.32)

*Exercise 10.9* We can now substitute these moments into (10.31) and then solve for  $\mathbb{E}[\tau]$  to give

$$
\frac{1}{\mathbb{E}[\tau]} = \frac{1}{N-1} (\overline{x^2} - \overline{x}^2)
$$
$$
= \frac{1}{N-1} \sum_{n=1}^{N} (x_n - \overline{x})^2 \quad (10.33)
$$

We recognize the right-hand side as the familiar unbiased estimator for the variance of a univariate Gaussian distribution, and so we see that the use of a Bayesian ap-*Section 1.2.4* proach has avoided the bias of the maximum likelihood solution.

### **10.1.4 Model comparison**

As well as performing inference over the hidden variables **Z**, we may also wish to compare a set of candidate models, labelled by the index  $m$ , and having prior probabilities  $p(m)$ . Our goal is then to approximate the posterior probabilities  $p(m|\mathbf{X})$ , where **X** is the observed data. This is a slightly more complex situation than that considered so far because different models may have different structure and indeed different dimensionality for the hidden variables **Z**. We cannot therefore simply consider a factorized approximation  $q(\mathbf{Z})q(m)$ , but must instead recognize that the posterior over **Z** must be conditioned on <sup>m</sup>, and so we must consider  $q(\mathbf{Z}, m) = q(\mathbf{Z}|m)q(m)$ . We can readily verify the following decomposition based *Exercise 10.10* on this variational distribution

$$
\ln p(\mathbf{X}) = \mathcal{L}_m - \sum_{m} \sum_{\mathbf{Z}} q(\mathbf{Z}|m) q(m) \ln \left\{ \frac{p(\mathbf{Z}, m | \mathbf{X})}{q(\mathbf{Z}|m)q(m)} \right\}
$$
(10.34)

where the 
$$
\mathcal{L}_m
$$
 is a lower bound on  $\ln p(\mathbf{X})$  and is given by

$$
\mathcal{L}_m = \sum_m \sum_{\mathbf{Z}} q(\mathbf{Z}|m) q(m) \ln \left\{ \frac{p(\mathbf{Z}, \mathbf{X}, m)}{q(\mathbf{Z}|m)q(m)} \right\}.
$$
 (10.35)

Here we are assuming discrete **Z**, but the same analysis applies to continuous latent variables provided the summations are replaced with integrations. We can maximize *Exercise 10.11*  $\mathcal{L}_m$  with respect to the distribution  $q(m)$  using a Lagrange multiplier, with the result

$$
q(m) \propto p(m) \exp{\{\mathcal{L}_m\}}.\tag{10.36}
$$

However, if we maximize  $\mathcal{L}_m$  with respect to the  $q(\mathbf{Z}|m)$ , we find that the solutions for different  $m$  are coupled, as we expect because they are conditioned on  $m$ . We proceed instead by first optimizing each of the  $q(\mathbf{Z}|m)$  individually by optimization

*Exercise 10.10*

of (10.35), and then subsequently determining the  $q(m)$  using (10.36). After normalization the resulting values for  $q(m)$  can be used for model selection or model averaging in the usual way.

# **10.2. Illustration: Variational Mixture of Gaussians**

We now return to our discussion of the Gaussian mixture model and apply the variational inference machinery developed in the previous section. This will provide a good illustration of the application of variational methods and will also demonstrate how a Bayesian treatment elegantly resolves many of the difficulties associated with the maximum likelihood approach (Attias, 1999b). The reader is encouraged to work through this example in detail as it provides many insights into the practical application of variational methods. Many Bayesian models, corresponding to much more sophisticated distributions, can be solved by straightforward extensions and generalizations of this analysis.

Our starting point is the likelihood function for the Gaussian mixture model, illustrated by the graphical model in Figure 9.6. For each observation  $x_n$  we have a corresponding latent variable  $z_n$  comprising a 1-of-K binary vector with elements  $z_{nk}$  for  $k = 1, ..., K$ . As before we denote the observed data set by **X** =  $\{x_1, \ldots, x_N\}$ , and similarly we denote the latent variables by  $\mathbf{Z} = \{z_1, \ldots, z_N\}$ . From (9.10) we can write down the conditional distribution of **Z**, given the mixing coefficients  $\pi$ , in the form

$$
p(\mathbf{Z}|\boldsymbol{\pi}) = \prod_{n=1}^{N} \prod_{k=1}^{K} \pi_k^{z_{nk}}.
$$
 (10.37)

Similarly, from (9.11), we can write down the conditional distribution of the observed data vectors, given the latent variables and the component parameters

$$
p(\mathbf{X}|\mathbf{Z}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) = \prod_{n=1}^{N} \prod_{k=1}^{K} \mathcal{N}\left(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Lambda}_k^{-1}\right)^{z_{nk}}
$$
(10.38)

where  $\mu = {\mu_k}$  and  $\Lambda = {\Lambda_k}$ . Note that we are working in terms of precision matrices rather than covariance matrices as this somewhat simplifies the mathematics.

Next we introduce priors over the parameters  $\mu$ ,  $\Lambda$  and  $\pi$ . The analysis is con-*Section 10.4.1* siderably simplified if we use conjugate prior distributions. We therefore choose a Dirichlet distribution over the mixing coefficients *π*

$$
p(\boldsymbol{\pi}) = \text{Dir}(\boldsymbol{\pi}|\boldsymbol{\alpha}_0) = C(\boldsymbol{\alpha}_0) \prod_{k=1}^K \pi_k^{\alpha_0 - 1}
$$
 (10.39)

where by symmetry we have chosen the same parameter  $\alpha_0$  for each of the components, and  $C(\mathbf{\alpha}_0)$  is the normalization constant for the Dirichlet distribution defined

*Section 10.4.1*

## **10.2. Illustration: Variational Mixture of Gaussians 475**

**Figure 10.5** Directed acyclic graph representing the Bayesian mixture of Gaussians model, in which the box (plate) denotes a set of N i.i.d. observations. Here  $\mu$  denotes  $\{\boldsymbol{\mu}_k\}$  and  $\boldsymbol{\Lambda}$  denotes  $\{\boldsymbol{\Lambda}_k\}$ .

Image /page/14/Figure/2 description: This is a graphical representation of a probabilistic model. It uses circles to represent random variables and arrows to indicate dependencies. The variables are labeled as pi, zn, xn, Lambda, and mu. The variable xn is enclosed in a blue box with the label N, indicating that it is part of a collection of N observations. There are arrows showing that pi influences zn, and both zn and Lambda influence xn. Lambda also influences mu, and both Lambda and mu influence xn. This diagram likely illustrates a Bayesian model, possibly for mixture models or latent variable models.

# *Section 2.2.1* by (B.23). As we have seen, the parameter $\alpha_0$ can be interpreted as the effective prior number of observations associated with each component of the mixture. If the value of $\alpha_0$ is small, then the posterior distribution will be influenced primarily by the data rather than by the prior.

Similarly, we introduce an independent Gaussian-Wishart prior governing the mean and precision of each Gaussian component, given by

$$
p(\boldsymbol{\mu}, \boldsymbol{\Lambda}) = p(\boldsymbol{\mu}|\boldsymbol{\Lambda})p(\boldsymbol{\Lambda})
$$
  
= 
$$
\prod_{k=1}^{K} \mathcal{N}(\boldsymbol{\mu}_k | \mathbf{m}_0, (\beta_0 \boldsymbol{\Lambda}_k)^{-1}) \mathcal{W}(\boldsymbol{\Lambda}_k | \mathbf{W}_0, \nu_0)
$$
 (10.40)

because this represents the conjugate prior distribution when both the mean and pre-*Section 2.3.6* cision are unknown. Typically we would choose  $\mathbf{m}_0 = \mathbf{0}$  by symmetry.

> The resulting model can be represented as a directed graph as shown in Figure 10.5. Note that there is a link from  $\Lambda$  to  $\mu$  since the variance of the distribution over  $\mu$  in (10.40) is a function of  $Λ$ .

> This example provides a nice illustration of the distinction between latent variables and parameters. Variables such as  $z_n$  that appear inside the plate are regarded as latent variables because the number of such variables grows with the size of the data set. By contrast, variables such as  $\mu$  that are outside the plate are fixed in number independently of the size of the data set, and so are regarded as parameters. From the perspective of graphical models, however, there is really no fundamental difference between them.

## **10.2.1 Variational distribution**

In order to formulate a variational treatment of this model, we next write down the joint distribution of all of the random variables, which is given by

$$
p(\mathbf{X}, \mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) = p(\mathbf{X}|\mathbf{Z}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) p(\mathbf{Z}|\boldsymbol{\pi}) p(\boldsymbol{\pi}) p(\boldsymbol{\mu}|\boldsymbol{\Lambda}) p(\boldsymbol{\Lambda})
$$
(10.41)

in which the various factors are defined above. The reader should take a moment to verify that this decomposition does indeed correspond to the probabilistic graphical model shown in Figure 10.5. Note that only the variables  $X = \{x_1, \ldots, x_N\}$  are observed.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

We now consider a variational distribution which factorizes between the latent variables and the parameters so that

$$
q(\mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) = q(\mathbf{Z})q(\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}).
$$
\n(10.42)

It is remarkable that this is the *only* assumption that we need to make in order to obtain a tractable practical solution to our Bayesian mixture model. In particular, the functional form of the factors  $q(\mathbf{Z})$  and  $q(\pi, \mu, \Lambda)$  will be determined automatically by optimization of the variational distribution. Note that we are omitting the subscripts on the q distributions, much as we do with the p distributions in (10.41), and are relying on the arguments to distinguish the different distributions.

The corresponding sequential update equations for these factors can be easily derived by making use of the general result (10.9). Let us consider the derivation of the update equation for the factor  $q(\mathbf{Z})$ . The log of the optimized factor is given by

$$
\ln q^{\star}(\mathbf{Z}) = \mathbb{E}_{\pi,\mu,\Lambda}[\ln p(\mathbf{X}, \mathbf{Z}, \pi, \mu, \Lambda)] + \text{const.}
$$
 (10.43)

We now make use of the decomposition (10.41). Note that we are only interested in the functional dependence of the right-hand side on the variable **Z**. Thus any terms that do not depend on **Z** can be absorbed into the additive normalization constant, giving

$$
\ln q^{\star}(\mathbf{Z}) = \mathbb{E}_{\pi}[\ln p(\mathbf{Z}|\pi)] + \mathbb{E}_{\mu,\Lambda}[\ln p(\mathbf{X}|\mathbf{Z},\mu,\Lambda)] + \text{const.}
$$
 (10.44)

Substituting for the two conditional distributions on the right-hand side, and again absorbing any terms that are independent of **Z** into the additive constant, we have

$$
\ln q^{\star}(\mathbf{Z}) = \sum_{n=1}^{N} \sum_{k=1}^{K} z_{nk} \ln \rho_{nk} + \text{const}
$$
 (10.45)

where we have defined

$$
\ln \rho_{nk} = \mathbb{E}[\ln \pi_k] + \frac{1}{2} \mathbb{E} [\ln |\mathbf{\Lambda}_k|] - \frac{D}{2} \ln(2\pi)
$$
$$
-\frac{1}{2} \mathbb{E}_{{\mu_k}, \mathbf{\Lambda}_k} [(\mathbf{x}_n - \boldsymbol{\mu}_k)^{\mathrm{T}} \mathbf{\Lambda}_k (\mathbf{x}_n - \boldsymbol{\mu}_k)] \tag{10.46}
$$

where <sup>D</sup> is the dimensionality of the data variable **x**. Taking the exponential of both sides of (10.45) we obtain

$$
q^{\star}(\mathbf{Z}) \propto \prod_{n=1}^{N} \prod_{k=1}^{K} \rho_{nk}^{z_{nk}}.
$$
 (10.47)

Requiring that this distribution be normalized, and noting that for each value of  $n$ *Exercise 10.12* the quantities  $z_{nk}$  are binary and sum to 1 over all values of k, we obtain

$$
q^{\star}(\mathbf{Z}) = \prod_{n=1}^{N} \prod_{k=1}^{K} r_{nk}^{z_{nk}}
$$
 (10.48)

## **10.2. Illustration: Variational Mixture of Gaussians 477**

where

$$
r_{nk} = \frac{\rho_{nk}}{\sum_{j=1}^{K} \rho_{nj}}.
$$
(10.49)

We see that the optimal solution for the factor  $q(\mathbf{Z})$  takes the same functional form as the prior  $p(\mathbf{Z}|\boldsymbol{\pi})$ . Note that because  $\rho_{nk}$  is given by the exponential of a real quantity, the quantities  $r_{nk}$  will be nonnegative and will sum to one, as required.

For the discrete distribution  $q^{\star}(\mathbf{Z})$  we have the standard result

$$
\mathbb{E}[z_{nk}] = r_{nk} \tag{10.50}
$$

from which we see that the quantities  $r_{nk}$  are playing the role of responsibilities. Note that the optimal solution for  $q^{\star}(\mathbf{Z})$  depends on moments evaluated with respect to the distributions of other variables, and so again the variational update equations are coupled and must be solved iteratively.

At this point, we shall find it convenient to define three statistics of the observed data set evaluated with respect to the responsibilities, given by

$$
N_k = \sum_{n=1}^{N} r_{nk} \tag{10.51}
$$

$$
\overline{\mathbf{x}}_k = \frac{1}{N_k} \sum_{n=1}^N r_{nk} \mathbf{x}_n
$$
 (10.52)

$$
\mathbf{S}_k = \frac{1}{N_k} \sum_{n=1}^N r_{nk} (\mathbf{x}_n - \overline{\mathbf{x}}_k) (\mathbf{x}_n - \overline{\mathbf{x}}_k)^{\mathrm{T}}.
$$
 (10.53)

Note that these are analogous to quantities evaluated in the maximum likelihood EM algorithm for the Gaussian mixture model.

N

Now let us consider the factor  $q(\pi, \mu, \Lambda)$  in the variational posterior distribution. Again using the general result (10.9) we have

$$
\ln q^{\star}(\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) = \ln p(\boldsymbol{\pi}) + \sum_{k=1}^{K} \ln p(\boldsymbol{\mu}_{k}, \boldsymbol{\Lambda}_{k}) + \mathbb{E}_{\mathbf{Z}} [\ln p(\mathbf{Z}|\boldsymbol{\pi})]
$$
$$
+ \sum_{k=1}^{K} \sum_{n=1}^{N} \mathbb{E}[z_{nk}] \ln \mathcal{N}(\mathbf{x}_{n}|\boldsymbol{\mu}_{k}, \boldsymbol{\Lambda}_{k}^{-1}) + \text{const.} \qquad (10.54)
$$

We observe that the right-hand side of this expression decomposes into a sum of terms involving only  $\pi$  together with terms only involving  $\mu$  and  $\Lambda$ , which implies that the variational posterior  $q(\pi, \mu, \Lambda)$  factorizes to give  $q(\pi)q(\mu, \Lambda)$ . Furthermore, the terms involving  $\mu$  and  $\Lambda$  themselves comprise a sum over k of terms involving  $\mu_k$  and  $\Lambda_k$  leading to the further factorization

$$
q(\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) = q(\boldsymbol{\pi}) \prod_{k=1}^{K} q(\boldsymbol{\mu}_k, \boldsymbol{\Lambda}_k).
$$
 (10.55)

Identifying the terms on the right-hand side of (10.54) that depend on  $\pi$ , we have

$$
\ln q^{\star}(\boldsymbol{\pi}) = (\alpha_0 - 1) \sum_{k=1}^{K} \ln \pi_k + \sum_{k=1}^{K} \sum_{n=1}^{N} r_{nk} \ln \pi_k + \text{const}
$$
 (10.56)

where we have used (10.50). Taking the exponential of both sides, we recognize  $q^{\star}(\boldsymbol{\pi})$  as a Dirichlet distribution

$$
q^{\star}(\boldsymbol{\pi}) = \text{Dir}(\boldsymbol{\pi}|\boldsymbol{\alpha}) \tag{10.57}
$$

where  $\alpha$  has components  $\alpha_k$  given by

$$
\alpha_k = \alpha_0 + N_k. \tag{10.58}
$$

Finally, the variational posterior distribution  $q^{\star}(\mu_k, \Lambda_k)$  does not factorize into the product of the marginals, but we can always use the product rule to write it in the form  $q^{\star}(\mu_k, \Lambda_k) = q^{\star}(\mu_k|\Lambda_k)q^{\star}(\Lambda_k)$ . The two factors can be found by inspecting (10.54) and reading off those terms that involve  $\mu_k$  and  $\Lambda_k$ . The result, as expected, *Exercise 10.13* is a Gaussian-Wishart distribution and is given by

$$
q^{\star}(\boldsymbol{\mu}_k, \boldsymbol{\Lambda}_k) = \mathcal{N}\left(\boldsymbol{\mu}_k | \mathbf{m}_k, (\beta_k \boldsymbol{\Lambda}_k)^{-1}\right) \mathcal{W}(\boldsymbol{\Lambda}_k | \mathbf{W}_k, \nu_k)
$$
(10.59)

where we have defined

$$
\beta_k = \beta_0 + N_k \tag{10.60}
$$

$$
\mathbf{m}_k = \frac{1}{\beta_k} \left( \beta_0 \mathbf{m}_0 + N_k \overline{\mathbf{x}}_k \right) \tag{10.61}
$$

$$
\mathbf{W}_k^{-1} = \mathbf{W}_0^{-1} + N_k \mathbf{S}_k + \frac{\beta_0 N_k}{\beta_0 + N_k} (\overline{\mathbf{x}}_k - \mathbf{m}_0) (\overline{\mathbf{x}}_k - \mathbf{m}_0)^{\mathrm{T}} \quad (10.62)
$$

$$
\nu_k = \nu_0 + N_k. \tag{10.63}
$$

These update equations are analogous to the M-step equations of the EM algorithm for the maximum likelihood solution of the mixture of Gaussians. We see that the computations that must be performed in order to update the variational posterior distribution over the model parameters involve evaluation of the same sums over the data set, as arose in the maximum likelihood treatment.

In order to perform this variational M step, we need the expectations  $\mathbb{E}[z_{nk}] =$  $r_{nk}$  representing the responsibilities. These are obtained by normalizing the  $\rho_{nk}$  that are given by (10.46). We see that this expression involves expectations with respect to the variational distributions of the parameters, and these are easily evaluated to *Exercise 10.14* give

$$
\mathbb{E}_{\mu_k, \Lambda_k} \left[ (\mathbf{x}_n - \boldsymbol{\mu}_k)^{\mathrm{T}} \boldsymbol{\Lambda}_k (\mathbf{x}_n - \boldsymbol{\mu}_k) \right] \n= D \beta_k^{-1} + \nu_k (\mathbf{x}_n - \mathbf{m}_k)^{\mathrm{T}} \mathbf{W}_k (\mathbf{x}_n - \mathbf{m}_k)
$$
\n(10.64)

$$
\ln \widetilde{\Lambda}_k \equiv \mathbb{E}\left[\ln |\mathbf{\Lambda}_k|\right] = \sum_{i=1}^D \psi\left(\frac{\nu_k + 1 - i}{2}\right) + D\ln 2 + \ln|\mathbf{W}_k| \tag{10.65}
$$

$$
\ln \widetilde{\pi}_k \equiv \mathbb{E} \left[ \ln \pi_k \right] = \psi(\alpha_k) - \psi(\widehat{\alpha}) \tag{10.66}
$$

*Exercise 10.13*

where we have introduced definitions of  $\tilde{\Lambda}_k$  and  $\tilde{\pi}_k$ , and  $\psi(\cdot)$  is the digamma function defined by (B.25), with  $\hat{\alpha} = \sum_{k} \alpha_k$ . The results (10.65) and (10.66) follow from *Appendix B* the standard properties of the Wishart and Dirichlet distributions.

> If we substitute  $(10.64)$ ,  $(10.65)$ , and  $(10.66)$  into  $(10.46)$  and make use of (10.49), we obtain the following result for the responsibilities

$$
r_{nk} \propto \widetilde{\pi}_k \widetilde{\Lambda}_k^{1/2} \exp\left\{-\frac{D}{2\beta_k} - \frac{\nu_k}{2} (\mathbf{x}_n - \mathbf{m}_k)^{\mathrm{T}} \mathbf{W}_k (\mathbf{x}_n - \mathbf{m}_k)\right\}.
$$
 (10.67)

Notice the similarity to the corresponding result for the responsibilities in maximum likelihood EM, which from (9.13) can be written in the form

$$
r_{nk} \propto \pi_k |\mathbf{\Lambda}_k|^{1/2} \exp\left\{-\frac{1}{2}(\mathbf{x}_n - \boldsymbol{\mu}_k)^{\mathrm{T}} \mathbf{\Lambda}_k(\mathbf{x}_n - \boldsymbol{\mu}_k)\right\}
$$
(10.68)

where we have used the precision in place of the covariance to highlight the similarity to (10.67).

Thus the optimization of the variational posterior distribution involves cycling between two stages analogous to the E and M steps of the maximum likelihood EM algorithm. In the variational equivalent of the E step, we use the current distributions over the model parameters to evaluate the moments in (10.64), (10.65), and (10.66) and hence evaluate  $\mathbb{E}[z_{nk}] = r_{nk}$ . Then in the subsequent variational equivalent of the M step, we keep these responsibilities fixed and use them to re-compute the variational distribution over the parameters using (10.57) and (10.59). In each case, we see that the variational posterior distribution has the same functional form as the corresponding factor in the joint distribution (10.41). This is a general result and is *Section 10.4.1* a consequence of the choice of conjugate distributions.

Figure 10.6 shows the results of applying this approach to the rescaled Old Faithful data set for a Gaussian mixture model having  $K = 6$  components. We see that after convergence, there are only two components for which the expected values of the mixing coefficients are numerically distinguishable from their prior values. This effect can be understood qualitatively in terms of the automatic trade-off in a *Section 3.4* Bayesian model between fitting the data and the complexity of the model, in which the complexity penalty arises from components whose parameters are pushed away from their prior values. Components that take essentially no responsibility for explaining the data points have  $r_{nk} \simeq 0$  and hence  $N_k \simeq 0$ . From (10.58), we see that  $\alpha_k \simeq \alpha_0$  and from (10.60)–(10.63) we see that the other parameters revert to their prior values. In principle such components are fitted slightly to the data points, but for broad priors this effect is too small to be seen numerically. For the variational Gaussian mixture model the expected values of the mixing coefficients in the *Exercise 10.15* posterior distribution are given by

$$
\mathbb{E}[\pi_k] = \frac{\alpha_k + N_k}{K\alpha_0 + N}.\tag{10.69}
$$

Consider a component for which  $N_k \simeq 0$  and  $\alpha_k \simeq \alpha_0$ . If the prior is broad so that  $\alpha_0 \to 0$ , then  $\mathbb{E}[\pi_k] \to 0$  and the component plays no role in the model, whereas if

*Appendix B*

*Exercise 10.15*

**Figure 10.6** Variational Bayesian mixture of  $K = 6$  Gaussians applied to the Old Faithful data set, in which the ellipses denote the one standard-deviation density contours for each of the components, and the density of red ink inside each ellipse corresponds to the mean value of the mixing coefficient for each component. The number in the top left of each diagram shows the number of iterations of variational inference. Components whose expected mixing coefficient are numerically indistinguishable from zero are not plotted.

Image /page/19/Figure/2 description: This image displays four scatter plots arranged in a 2x2 grid. Each plot shows green dots representing data points, with red ellipses indicating clusters or distributions. The top-left plot is labeled '0' and shows several overlapping red ellipses with many green dots clustered within them. The top-right plot is labeled '15' and shows two distinct clusters of green dots, each enclosed by red ellipses. The bottom-left plot is labeled '60' and also shows two distinct clusters, similar to the '15' plot but with slightly more spread in the data points. The bottom-right plot is labeled '120' and presents two well-defined clusters, each with green dots tightly distributed within red ellipses. The overall trend across the plots suggests a change in data distribution or clustering over time, indicated by the numerical labels.

the prior tightly constrains the mixing coefficients so that  $\alpha_0 \to \infty$ , then  $\mathbb{E}[\pi_k] \to$  $1/K$ .

In Figure 10.6, the prior over the mixing coefficients is a Dirichlet of the form (10.39). Recall from Figure 2.5 that for  $\alpha_0 < 1$  the prior favours solutions in which some of the mixing coefficients are zero. Figure 10.6 was obtained using  $\alpha_0 = 10^{-3}$ , and resulted in two components having nonzero mixing coefficients. If instead we choose  $\alpha_0 = 1$  we obtain three components with nonzero mixing coefficients, and for  $\alpha = 10$  all six components have nonzero mixing coefficients.

As we have seen there is a close similarity between the variational solution for the Bayesian mixture of Gaussians and the EM algorithm for maximum likelihood. In fact if we consider the limit  $N \to \infty$  then the Bayesian treatment converges to the maximum likelihood EM algorithm. For anything other than very small data sets, the dominant computational cost of the variational algorithm for Gaussian mixtures arises from the evaluation of the responsibilities, together with the evaluation and inversion of the weighted data covariance matrices. These computations mirror precisely those that arise in the maximum likelihood EM algorithm, and so there is little computational overhead in using this Bayesian approach as compared to the traditional maximum likelihood one. There are, however, some substantial advantages. First of all, the singularities that arise in maximum likelihood when a Gaussian component 'collapses' onto a specific data point are absent in the Bayesian treatment.

Indeed, these singularities are removed if we simply introduce a prior and then use a MAP estimate instead of maximum likelihood. Furthermore, there is no over-fitting if we choose a large number  $K$  of components in the mixture, as we saw in Figure 10.6. Finally, the variational treatment opens up the possibility of determining the optimal number of components in the mixture without resorting to techniques *Section 10.2.4* such as cross validation.

## **10.2.2 Variational lower bound**

We can also straightforwardly evaluate the lower bound  $(10.3)$  for this model. In practice, it is useful to be able to monitor the bound during the re-estimation in order to test for convergence. It can also provide a valuable check on both the mathematical expressions for the solutions and their software implementation, because at each step of the iterative re-estimation procedure the value of this bound should not decrease. We can take this a stage further to provide a deeper test of the correctness of both the mathematical derivation of the update equations and of their software implementation by using finite differences to check that each update does indeed give a (constrained) maximum of the bound (Svensén and Bishop, 2004).

For the variational mixture of Gaussians, the lower bound (10.3) is given by

$$
\mathcal{L} = \sum_{\mathbf{Z}} \int \int \int q(\mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) \ln \left\{ \frac{p(\mathbf{X}, \mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda})}{q(\mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda})} \right\} d\boldsymbol{\pi} d\boldsymbol{\mu} d\boldsymbol{\Lambda}
$$
  
=  $\mathbb{E}[\ln p(\mathbf{X}, \mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda})] - \mathbb{E}[\ln q(\mathbf{Z}, \boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda})]$   
=  $\mathbb{E}[\ln p(\mathbf{X}|\mathbf{Z}, \boldsymbol{\mu}, \boldsymbol{\Lambda})] + \mathbb{E}[\ln p(\mathbf{Z}|\boldsymbol{\pi})] + \mathbb{E}[\ln p(\boldsymbol{\pi})] + \mathbb{E}[\ln p(\boldsymbol{\mu}, \boldsymbol{\Lambda})]$   
-  $\mathbb{E}[\ln q(\mathbf{Z})] - \mathbb{E}[\ln q(\boldsymbol{\pi})] - \mathbb{E}[\ln q(\boldsymbol{\mu}, \boldsymbol{\Lambda})]$  (10.70)

where, to keep the notation uncluttered, we have omitted the  $\star$  superscript on the q distributions, along with the subscripts on the expectation operators because each expectation is taken with respect to all of the random variables in its argument. The *Exercise 10.16* various terms in the bound are easily evaluated to give the following results

$$
\mathbb{E}[\ln p(\mathbf{X}|\mathbf{Z}, \boldsymbol{\mu}, \boldsymbol{\Lambda})] = \frac{1}{2} \sum_{k=1}^{K} N_k \left\{ \ln \widetilde{\Lambda}_k - D\beta_k^{-1} - \nu_k \text{Tr}(\mathbf{S}_k \mathbf{W}_k) -\nu_k (\overline{\mathbf{x}}_k - \mathbf{m}_k)^{\text{T}} \mathbf{W}_k (\overline{\mathbf{x}}_k - \mathbf{m}_k) - D \ln(2\pi) \right\} (10.71)
$$

$$
\mathbb{E}[\ln p(\mathbf{Z}|\boldsymbol{\pi})] = \sum_{n=1}^{N} \sum_{k=1}^{K} r_{nk} \ln \widetilde{\pi}_k
$$
(10.72)

$$
\mathbb{E}[\ln p(\boldsymbol{\pi})] = \ln C(\boldsymbol{\alpha}_0) + (\alpha_0 - 1) \sum_{k=1}^{K} \ln \widetilde{\pi}_k \qquad (10.73)
$$

$$
\mathbb{E}[\ln p(\boldsymbol{\mu}, \boldsymbol{\Lambda})] = \frac{1}{2} \sum_{k=1}^{K} \left\{ D \ln(\beta_0 / 2\pi) + \ln \widetilde{\Lambda}_k - \frac{D\beta_0}{\beta_k} \right\} \\ -\beta_0 \nu_k (\mathbf{m}_k - \mathbf{m}_0)^{\mathrm{T}} \mathbf{W}_k (\mathbf{m}_k - \mathbf{m}_0) + K \ln B(\mathbf{W}_0, \nu_0) \\ + \frac{(\nu_0 - D - 1)}{2} \sum_{k=1}^{K} \ln \widetilde{\Lambda}_k - \frac{1}{2} \sum_{k=1}^{K} \nu_k \text{Tr}(\mathbf{W}_0^{-1} \mathbf{W}_k) \tag{10.74}
$$

$$
\mathbb{E}[\ln q(\mathbf{Z})] = \sum_{n=1}^{N} \sum_{k=1}^{K} r_{nk} \ln r_{nk}
$$
\n(10.75)

$$
\mathbb{E}[\ln q(\boldsymbol{\pi})] = \sum_{k=1}^{K} (\alpha_k - 1) \ln \widetilde{\pi}_k + \ln C(\boldsymbol{\alpha}) \qquad (10.76)
$$

$$
\mathbb{E}[\ln q(\boldsymbol{\mu}, \boldsymbol{\Lambda})] = \sum_{k=1}^{K} \left\{ \frac{1}{2} \ln \widetilde{\Lambda}_k + \frac{D}{2} \ln \left( \frac{\beta_k}{2\pi} \right) - \frac{D}{2} - \mathrm{H} \left[ q(\boldsymbol{\Lambda}_k) \right] \right\} (10.77)
$$

where D is the dimensionality of **x**, H[ $q(\Lambda_k)$ ] is the entropy of the Wishart distribution given by (B.82), and the coefficients  $C(\alpha)$  and  $B(\mathbf{W}, \nu)$  are defined by (B.23) and (B.79), respectively. Note that the terms involving expectations of the logs of the q distributions simply represent the negative entropies of those distributions. Some simplifications and combination of terms can be performed when these expressions are summed to give the lower bound. However, we have kept the expressions separate for ease of understanding.

Finally, it is worth noting that the lower bound provides an alternative approach for deriving the variational re-estimation equations obtained in Section 10.2.1. To do this we use the fact that, since the model has conjugate priors, the functional form of the factors in the variational posterior distribution is known, namely discrete for **Z**, Dirichlet for  $\pi$ , and Gaussian-Wishart for  $(\mu_k, \Lambda_k)$ . By taking general parametric forms for these distributions we can derive the form of the lower bound as a function of the parameters of the distributions. Maximizing the bound with respect to these *Exercise 10.18* parameters then gives the required re-estimation equations.

*Exercise 10.18*

## **10.2.3 Predictive density**

In applications of the Bayesian mixture of Gaussians model we will often be interested in the predictive density for a new value  $\hat{x}$  of the observed variable. Associated with this observation will be a corresponding latent variable  $\hat{z}$ , and the predictive density is then given by

$$
p(\widehat{\mathbf{x}}|\mathbf{X}) = \sum_{\widehat{\mathbf{z}}} \int \int \int p(\widehat{\mathbf{x}}|\widehat{\mathbf{z}}, \boldsymbol{\mu}, \boldsymbol{\Lambda}) p(\widehat{\mathbf{z}}|\boldsymbol{\pi}) p(\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}|\mathbf{X}) \, d\boldsymbol{\pi} \, d\boldsymbol{\mu} \, d\boldsymbol{\Lambda} \qquad (10.78)
$$

where  $p(\pi, \mu, \Lambda | X)$  is the (unknown) true posterior distribution of the parameters. Using (10.37) and (10.38) we can first perform the summation over  $\hat{z}$  to give

$$
p(\widehat{\mathbf{x}}|\mathbf{X}) = \sum_{k=1}^{K} \int \int \int \pi_k \mathcal{N}\left(\widehat{\mathbf{x}}|\boldsymbol{\mu}_k, \boldsymbol{\Lambda}_k^{-1}\right) p(\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Lambda}|\mathbf{X}) \, d\boldsymbol{\pi} \, d\boldsymbol{\mu} \, d\boldsymbol{\Lambda}.
$$
 (10.79)

Because the remaining integrations are intractable, we approximate the predictive density by replacing the true posterior distribution  $p(\pi, \mu, \Lambda | X)$  with its variational approximation  $q(\pi)q(\mu,\Lambda)$  to give

$$
p(\widehat{\mathbf{x}}|\mathbf{X}) = \sum_{k=1}^{K} \int \int \int \pi_k \mathcal{N}\left(\widehat{\mathbf{x}}|\boldsymbol{\mu}_k, \boldsymbol{\Lambda}_k^{-1}\right) q(\boldsymbol{\pi}) q(\boldsymbol{\mu}_k, \boldsymbol{\Lambda}_k) \, d\boldsymbol{\pi} \, d\boldsymbol{\mu}_k \, d\boldsymbol{\Lambda}_k \qquad (10.80)
$$

where we have made use of the factorization (10.55) and in each term we have implicitly integrated out all variables  $\{\mu_i, \Lambda_j\}$  for  $j \neq k$  The remaining integrations *Exercise 10.19* can now be evaluated analytically giving a mixture of Student's t-distributions

$$
p(\widehat{\mathbf{x}}|\mathbf{X}) = \frac{1}{\widehat{\alpha}} \sum_{k=1}^{K} \alpha_k \text{St}(\widehat{\mathbf{x}}|\mathbf{m}_k, \mathbf{L}_k, \nu_k + 1 - D)
$$
(10.81)

in which the  $k^{\text{th}}$  component has mean  $\mathbf{m}_k$ , and the precision is given by

$$
\mathbf{L}_k = \frac{(\nu_k + 1 - D)\beta_k}{(1 + \beta_k)} \mathbf{W}_k
$$
\n(10.82)

in which  $\nu_k$  is given by (10.63). When the size N of the data set is large the predictive *Exercise 10.20* distribution (10.81) reduces to a mixture of Gaussians.

### **10.2.4 Determining the number of components**

We have seen that the variational lower bound can be used to determine a pos-*Section 10.1.4* terior distribution over the number K of components in the mixture model. There is, however, one subtlety that needs to be addressed. For any given setting of the parameters in a Gaussian mixture model (except for specific degenerate settings), there will exist other parameter settings for which the density over the observed variables will be identical. These parameter values differ only through a re-labelling of the components. For instance, consider a mixture of two Gaussians and a single observed variable x, in which the parameters have the values  $\pi_1 = a, \pi_2 = b, \mu_1 = c$ ,  $\mu_2 = d$ ,  $\sigma_1 = e$ ,  $\sigma_2 = f$ . Then the parameter values  $\pi_1 = b$ ,  $\pi_2 = a$ ,  $\mu_1 = d$ ,  $\mu_2 = c$ ,  $\sigma_1 = f$ ,  $\sigma_2 = e$ , in which the two components have been exchanged, will by symmetry give rise to the same value of  $p(x)$ . If we have a mixture model comprising K components, then each parameter setting will be a member of a family of *Exercise 10.21* K! equivalent settings.

> In the context of maximum likelihood, this redundancy is irrelevant because the parameter optimization algorithm (for example EM) will, depending on the initialization of the parameters, find one specific solution, and the other equivalent solutions play no role. In a Bayesian setting, however, we marginalize over all possible

*Exercise 10.19*