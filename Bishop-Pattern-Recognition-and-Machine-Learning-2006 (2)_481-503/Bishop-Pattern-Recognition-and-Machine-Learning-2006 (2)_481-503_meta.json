{"table_of_contents": [{"title": "10.1. Variational Inference", "heading_level": null, "page_id": 1, "polygon": [[81.75, 432.0], [247.5, 432.0], [247.5, 444.8671875], [81.75, 444.8671875]]}, {"title": "10.1.1 Factorized distributions", "heading_level": null, "page_id": 3, "polygon": [[137.8125, 495.75], [312.0, 495.75], [312.0, 507.3046875], [137.8125, 507.3046875]]}, {"title": "10.1. Variational Inference 465", "heading_level": null, "page_id": 4, "polygon": [[301.5, 40.5], [473.25, 40.5], [473.25, 51.3402099609375], [301.5, 51.3402099609375]]}, {"title": "466 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 5, "polygon": [[30.0, 40.5], [240.0, 40.5], [240.0, 51.21826171875], [30.0, 51.21826171875]]}, {"title": "10.1.2 Properties of factorized approximations", "heading_level": null, "page_id": 5, "polygon": [[137.3203125, 435.75], [398.671875, 435.75], [398.671875, 446.818359375], [137.3203125, 446.818359375]]}, {"title": "468 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 7, "polygon": [[29.25, 40.5], [240.1875, 40.5], [240.1875, 51.299560546875], [29.25, 51.299560546875]]}, {"title": "", "heading_level": null, "page_id": 7, "polygon": [[30.30029296875, 284.5458984375], [82.072265625, 284.5458984375], [82.072265625, 293.326171875], [30.30029296875, 293.326171875]]}, {"title": "", "heading_level": null, "page_id": 7, "polygon": [[31.376953125, 394.13671875], [92.2236328125, 392.8359375], [92.2236328125, 401.94140625], [31.376953125, 403.2421875]]}, {"title": "470 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 9, "polygon": [[29.25, 40.5], [240.064453125, 40.5], [240.064453125, 51.3402099609375], [29.25, 51.3402099609375]]}, {"title": "10.1.3 Example: The univariate Gaussian", "heading_level": null, "page_id": 9, "polygon": [[138.427734375, 325.5], [369.75, 325.5], [369.75, 336.5771484375], [138.427734375, 336.5771484375]]}, {"title": "472 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 11, "polygon": [[29.25, 40.5], [241.171875, 40.5], [241.171875, 51.1776123046875], [29.25, 51.1776123046875]]}, {"title": "10.1. Variational Inference 473", "heading_level": null, "page_id": 12, "polygon": [[301.5, 39.75], [473.25, 39.75], [473.25, 50.2020263671875], [301.5, 51.5028076171875]]}, {"title": "10.1.4 Model comparison", "heading_level": null, "page_id": 12, "polygon": [[138.75, 266.25], [285.0, 266.25], [285.0, 277.22900390625], [138.75, 277.22900390625]]}, {"title": "10.2. Illustration: Variational Mixture of Gaussians", "heading_level": null, "page_id": 13, "polygon": [[81.75, 132.75], [396.75, 132.0], [396.75, 145.85009765625], [81.75, 145.85009765625]]}, {"title": "10.2. Illustration: Variational Mixture of Gaussians 475", "heading_level": null, "page_id": 14, "polygon": [[190.96875, 40.5], [473.25, 40.5], [473.25, 50.974365234375], [190.96875, 50.974365234375]]}, {"title": "Section 2.2.1 by (B.23). As we have seen, the parameter \\alpha_0 can be interpreted as the effective\nprior number of observations associated with each component of the mixture. If the\nvalue of \\alpha_0 is small, then the posterior distribution will be influenced primarily by\nthe data rather than by the prior.", "heading_level": null, "page_id": 14, "polygon": [[29.0390625, 187.5], [473.25, 187.5], [473.25, 234.0], [29.0390625, 234.0]]}, {"title": "10.2.1 Variational distribution", "heading_level": null, "page_id": 14, "polygon": [[138.75, 480.0], [307.5, 480.0], [307.5, 491.6953125], [138.75, 491.6953125]]}, {"title": "476 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 15, "polygon": [[29.25, 40.5], [240.75, 40.5], [240.75, 51.380859375], [29.25, 51.380859375]]}, {"title": "10.2. Illustration: Variational Mixture of Gaussians 477", "heading_level": null, "page_id": 16, "polygon": [[191.25, 40.5], [473.25, 40.5], [473.25, 51.136962890625], [191.25, 51.136962890625]]}, {"title": "478 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 17, "polygon": [[29.25, 40.5], [240.0, 40.5], [240.0, 51.3402099609375], [29.25, 51.3402099609375]]}, {"title": "480 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 19, "polygon": [[30.0, 40.5], [240.0, 40.5], [240.0, 51.299560546875], [30.0, 51.299560546875]]}, {"title": "10.2.2 Variational lower bound", "heading_level": null, "page_id": 20, "polygon": [[138.673828125, 152.25], [312.75, 152.25], [312.75, 164.2236328125], [138.673828125, 164.2236328125]]}, {"title": "482 10. APPROX<PERSON><PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 21, "polygon": [[29.25, 40.5], [240.0, 40.5], [240.0, 51.3402099609375], [29.25, 51.3402099609375]]}, {"title": "10.2.3 Predictive density", "heading_level": null, "page_id": 21, "polygon": [[137.8125, 499.5], [280.5, 500.25], [280.5, 511.5322265625], [137.8125, 511.5322265625]]}, {"title": "10.2.4 Determining the number of components", "heading_level": null, "page_id": 22, "polygon": [[138.181640625, 399.0], [401.25, 399.0], [401.25, 410.396484375], [138.181640625, 410.396484375]]}, {"title": "", "heading_level": null, "page_id": 22, "polygon": [[31.77685546875, 560.3115234375], [97.330078125, 560.3115234375], [97.330078125, 569.4169921875], [31.77685546875, 569.4169921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 56], ["Line", 18], ["Picture", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6308, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 41], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 51], ["Text", 3], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 44], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 67], ["Text", 6], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1831, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 52], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 968, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 54], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1792, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 71], ["TextInlineMath", 5], ["SectionHeader", 3], ["Equation", 3], ["Figure", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 30], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 566], ["Line", 58], ["Text", 7], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1212, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 589], ["Line", 73], ["Equation", 6], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 52], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1407, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 56], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 55], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 46], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2372, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 61], ["Equation", 7], ["Text", 6], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1090, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["Line", 66], ["Equation", 7], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 533], ["Line", 65], ["Equation", 11], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 57], ["TextInlineMath", 6], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1155, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 39], ["Text", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 705, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 56], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3970, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 74], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5161, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 54], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_481-503"}