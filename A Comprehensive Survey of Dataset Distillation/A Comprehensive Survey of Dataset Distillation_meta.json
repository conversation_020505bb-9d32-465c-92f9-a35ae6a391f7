{"table_of_contents": [{"title": "A Comprehensive Survey of Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[52.5, 54.09228515625], [558.0, 54.09228515625], [558.0, 79.22900390625], [52.5, 79.22900390625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 288.0], [140.2998046875, 288.0], [140.2998046875, 298.16015625], [48.0, 298.16015625]]}, {"title": "2 BACKGROUND", "heading_level": null, "page_id": 1, "polygon": [[311.25, 323.103515625], [400.5, 323.103515625], [400.5, 333.544921875], [311.25, 333.544921875]]}, {"title": "2.1 Formalizing Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[45.75, 339.75], [212.25, 339.75], [212.25, 349.787109375], [45.75, 349.787109375]]}, {"title": "3 META-LEARNING FRAMEWORK", "heading_level": null, "page_id": 2, "polygon": [[47.25, 579.75], [218.25, 579.75], [218.25, 590.90625], [47.25, 590.90625]]}, {"title": "3.1 Backpropagation Through Time Approach", "heading_level": null, "page_id": 2, "polygon": [[310.5, 393.75], [525.0, 393.75], [525.0, 403.34765625], [310.5, 403.34765625]]}, {"title": "3.2 Kernel Ridge Regression Approach", "heading_level": null, "page_id": 3, "polygon": [[46.5, 565.5], [231.0, 565.5], [231.0, 575.05078125], [46.5, 575.05078125]]}, {"title": "3.3 Discussion", "heading_level": null, "page_id": 4, "polygon": [[45.75, 384.75], [121.5, 384.75], [121.5, 394.453125], [45.75, 394.453125]]}, {"title": "4 DATA MATCHING FRAMEWORK", "heading_level": null, "page_id": 4, "polygon": [[47.25, 662.25], [216.0, 662.25], [216.0, 672.1171875], [47.25, 672.1171875]]}, {"title": "4.1 Gradient Matching Approach", "heading_level": null, "page_id": 4, "polygon": [[310.18359375, 301.5], [465.0, 301.5], [465.0, 311.501953125], [310.18359375, 311.501953125]]}, {"title": "4.2 Trajectory Matching Approach", "heading_level": null, "page_id": 5, "polygon": [[310.5, 493.5], [471.75, 493.5], [471.75, 503.12109375], [310.5, 503.12109375]]}, {"title": "4.3 Distribution Matching Approach", "heading_level": null, "page_id": 6, "polygon": [[46.5, 502.5], [214.5, 502.5], [214.5, 512.015625], [46.5, 512.015625]]}, {"title": "4.4 Discussion", "heading_level": null, "page_id": 6, "polygon": [[310.5, 570.75], [385.5, 570.75], [385.5, 581.23828125], [310.5, 581.23828125]]}, {"title": "5 FACTORIZED DATASET DISTILLATION", "heading_level": null, "page_id": 7, "polygon": [[47.14013671875, 317.25], [247.130859375, 317.25], [247.130859375, 327.9375], [47.14013671875, 327.9375]]}, {"title": "6 PERFORMANCE COMPARISON", "heading_level": null, "page_id": 8, "polygon": [[46.5, 208.5], [212.25, 208.5], [212.25, 219.462890625], [46.5, 219.462890625]]}, {"title": "6.1 Standard Benchmark", "heading_level": null, "page_id": 8, "polygon": [[45.75, 617.25], [167.25, 617.25], [167.25, 627.2578125], [45.75, 627.2578125]]}, {"title": "6.2 Cross-Architecture Transferability", "heading_level": null, "page_id": 8, "polygon": [[311.25, 389.25], [488.25, 387.75], [488.25, 399.09375], [311.25, 399.09375]]}, {"title": "TABLE 1", "heading_level": null, "page_id": 9, "polygon": [[285.6796875, 44.25], [323.630859375, 44.25], [323.630859375, 52.3037109375], [285.6796875, 53.8505859375]]}, {"title": "7 DATA MODALITIES", "heading_level": null, "page_id": 9, "polygon": [[46.5, 585.0], [156.0, 585.0], [156.0, 596.3203125], [46.5, 596.3203125]]}, {"title": "7.1 Graph Data", "heading_level": null, "page_id": 9, "polygon": [[45.75, 652.0078125], [122.6689453125, 652.0078125], [122.6689453125, 662.8359375], [45.75, 662.8359375]]}, {"title": "7.2 Text Data", "heading_level": null, "page_id": 10, "polygon": [[45.75, 304.5], [114.0, 305.25], [114.0, 315.369140625], [45.75, 315.369140625]]}, {"title": "8 APPLICATION", "heading_level": null, "page_id": 10, "polygon": [[47.25, 450.0], [132.8291015625, 450.0], [132.8291015625, 460.96875], [47.25, 460.96875]]}, {"title": "8.1 Continual Learning", "heading_level": null, "page_id": 10, "polygon": [[45.75, 583.5], [157.5, 583.5], [157.5, 593.2265625], [45.75, 593.2265625]]}, {"title": "8.2 Neural Architecture Search", "heading_level": null, "page_id": 10, "polygon": [[310.5, 411.75], [457.5, 411.75], [457.5, 421.5234375], [310.5, 421.5234375]]}, {"title": "8.3 Federated Learning", "heading_level": null, "page_id": 10, "polygon": [[311.25, 686.25], [423.75, 686.25], [423.75, 696.48046875], [311.25, 696.48046875]]}, {"title": "8.4 Other Applications", "heading_level": null, "page_id": 11, "polygon": [[45.75, 430.5], [156.0, 430.5], [156.0, 440.859375], [45.75, 440.859375]]}, {"title": "9 CHALLENGES AND FUTURE DIRECTIONS", "heading_level": null, "page_id": 11, "polygon": [[310.78125, 93.0], [528.75, 93.0], [528.75, 104.607421875], [310.78125, 104.607421875]]}, {"title": "9.1 Challenges", "heading_level": null, "page_id": 11, "polygon": [[310.5, 191.25], [386.25, 191.25], [386.25, 201.48046875], [310.5, 201.48046875]]}, {"title": "9.2 Future Directions", "heading_level": null, "page_id": 12, "polygon": [[45.75, 108.5712890625], [149.25, 108.5712890625], [149.25, 118.2392578125], [45.75, 118.2392578125]]}, {"title": "ACKNOWLEDGE", "heading_level": null, "page_id": 13, "polygon": [[47.21484375, 155.25], [126.0, 155.25], [126.0, 165.90234375], [47.21484375, 165.90234375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 13, "polygon": [[46.6171875, 235.5], [116.25, 235.5], [116.25, 245.953125], [46.6171875, 245.953125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 83], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7381, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 675], ["Line", 126], ["TableCell", 76], ["Text", 6], ["TextInlineMath", 4], ["Equation", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 815], ["Line", 136], ["Text", 7], ["Equation", 7], ["TextInlineMath", 6], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 748], ["Line", 115], ["Equation", 5], ["TextInlineMath", 5], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1025], ["Line", 206], ["Text", 9], ["Equation", 9], ["TextInlineMath", 8], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 698], ["Line", 147], ["TextInlineMath", 7], ["Equation", 6], ["Reference", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 780, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 778], ["Line", 148], ["TextInlineMath", 6], ["Equation", 5], ["Text", 5], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 674, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 532], ["Line", 119], ["Text", 5], ["TextInlineMath", 3], ["Reference", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 500], ["Line", 117], ["Text", 8], ["ListItem", 8], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 3510], ["Line", 703], ["TableCell", 563], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["Table", 2], ["Equation", 2], ["Reference", 2], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2297, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1263], ["Line", 314], ["TableCell", 175], ["Text", 7], ["SectionHeader", 5], ["TextInlineMath", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 5355, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 118], ["Text", 7], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 121], ["Text", 11], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 119], ["ListItem", 24], ["Reference", 24], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 123], ["ListItem", 31], ["Reference", 31], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 123], ["ListItem", 28], ["Reference", 28], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 101], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/A Comprehensive Survey of Dataset Distillation"}