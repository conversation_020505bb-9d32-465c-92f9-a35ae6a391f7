Image /page/0/Picture/0 description: A square button with a rounded border contains a circular icon with a bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a light gray color.

# **Black-Box Adaptation for Medical Image Segmentation**

Jay <PERSON><sup>1( $\boxtimes$ )</sup>, <PERSON><PERSON><PERSON><sup>2,3</sup>, <PERSON><PERSON> Vedula<sup>3</sup>, and <PERSON><PERSON><PERSON><sup>1</sup>

 $^{\rm 1}$  Department of Electrical and Computer Engineering, The Johns Hopkins University, Baltimore, USA

<EMAIL>

<sup>2</sup> Wilmer Eye Institute, The Johns Hopkins University, Baltimore, USA <sup>3</sup> Malone Center for Engineering in Healthcare, The Johns Hopkins University, Baltimore, USA

**Abstract.** In recent years, various large foundation models have been proposed for image segmentation. These models are often trained on large amounts of data corresponding to general computer vision tasks. Hence, these models do not perform well on medical data. There have been some attempts in the literature to perform parameter-efficient finetuning of such foundation models for medical image segmentation. However, these approaches assume that all the parameters of the model are available for adaptation. But, in many cases, these models are released as APIs or Black-Boxes, with no or limited access to the model parameters and data. In addition, finetuning methods also require a significant amount of compute, which may not be available for the downstream task. At the same time, medical data can't be shared with third-party agents for finetuning due to privacy reasons. To tackle these challenges, we pioneer a Black-Box adaptation technique for prompted medical image segmentation, called BAPS. BAPS has two components - (i) An Image-Prompt decoder (IP decoder) module that generates visual prompts given an image and a prompt, and (ii) A Zero Order Optimization (ZOO) Method, called SPSA-GC that is used to update the IP decoder without the need for backpropagating through the foundation model. Thus, our method does not require any knowledge about the foundation model's weights or gradients. We test BAPS on four different modalities and show that our method can improve the original model's performance by around 4%. The code is available at [https://github.com/JayParanjape/Blackbox.](https://github.com/JayParanjape/Blackbox)

**Keywords:** Black-Box Adaptation · Prompted Segmentation

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_43) 43.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 454–464, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_43)\_43

# **1 Introduction**

Image segmentation is a fundamental problem in medical image analysis tasks. Many deep learning-based approaches have been proposed in the literature that segment out various regions of interest across different medical modalities [\[24](#page-10-0)]. In recent years, many foundation models have been proposed, which are large models with billions of parameters that show excellent performance on downstream tasks like classification  $[18]$  $[18]$  and segmentation  $[13,16,28]$  $[13,16,28]$  $[13,16,28]$  $[13,16,28]$ . Following the success of these foundation models, various adaptation approaches have been proposed that transfer the knowledge learnt by these large-scale models to medical segmentation [\[14\]](#page-9-2). These adaptation methods are commonly termed as Parameter Efficient Finetuning (PEFT) methods and they aim to utilize a given foundation model while tuning only a fraction of its parameters. However, all PEFT methods make two over-optimistic assumptions. First, they assume that all parameters of the foundation model are available during training. However, it often occurs that companies release their AI models as APIs or Black-Boxes instead of releasing the entire parameter set, training dataset, or codebase due to proprietary concerns. In such conditions, all the PEFT methods would fail since they require gradient computation to work. Secondly, many PEFT methods assume the availability of high compute resources  $[25,27]$  $[25,27]$  $[25,27]$ , which is not realistic. At the same time, the transfer of medical data to a third-party system with more resources would result in privacy concerns. In this work, we tackle these concerns by pioneering a Black-Box adaptation method for medical image segmentation called Black-Box Adapter for Prompted Segmentation (BAPS). BAPS uses a frozen image encoder and a trainable Image-Prompt Decoder (IP Decoder), to produce an input-dependent per-pixel prompt. This is added to the original image and provided to the foundation model Black-Box. We train the IP Decoder using a recently proposed ZOO method called Simultaneous Perturbation Stochastic Approximation with Gradient Correction (SPSA-GC) [\[17\]](#page-9-3) which does not require computation of any gradient, thus, not needing any parameter information about the foundation model. The IP Decoder is a lightweight module and hence, our method requires minimal compute resources. In summary, our contributions can be listed as follows:

**1)** To the best of our knowledge, this is the first paper to explore Black-Box adaptation for medical image segmentation. For this, we propose BAPS which has a frozen pre-trained encoder and a lightweight decoder module that can be trained using derivative-free optimization methods. A visual comparison of our method with existing approaches can be seen in Fig. [1.](#page-2-0)

**2)** We show that BAPS can improve the original foundation model's performance on four widely used public datasets of different modalities, including endoscopic images, dermoscopic images, gastrointestinal polyp images, and retinal images. For this purpose, we conduct experiments using two recently proposed popular image segmentation foundation models - SAM [\[13](#page-9-0)] and MedSAM [\[16](#page-9-1)].

Image /page/2/Figure/1 description: This figure illustrates three different approaches to fine-tuning foundation models for image segmentation tasks. Diagram (a) shows a 'Whitebox FM' where both the Image Encoder and Prompt Encoder are trainable (indicated by fire icons), feeding into a trainable Decoder. Diagram (b) also depicts a 'Whitebox FM', but here the Image Encoder is frozen (indicated by a snowflake icon), while the Prompt Encoder and Decoder are trainable. Adapter layers (Adt.) are introduced between the encoders and the decoder. Diagram (c) presents a 'Blackbox FM' approach, where the foundation model is not directly accessible for training. Instead, a 'BAPS' module is used, which takes the image and a prompt as input. The output of the BAPS module is combined with noisy latent representations (indicated by a static-like image) and fed into the Blackbox FM. A 'Zero Order Optimization' process is shown, with a red arrow indicating that backpropagation is not possible for the Blackbox FM. A legend clarifies the icons: snowflakes represent frozen components, fire icons represent trainable components, a circle with a plus sign represents summation, 'Adt. Adapter Layer' denotes adapter layers, and 'FM Foundation Model' refers to the foundation model. The overall goal is to segment an object, represented by a black and white image at the top of each diagram.

<span id="page-2-0"></span>**Fig. 1.** Comparison of our method (c) against full finetuning of a Foundation Model (FM), (a) and common adaptation techniques (b).

# **2 Related Work**

The motivation for using Black-Box adaptation arose from the Natural Language Processing (NLP) field. Many custom AI models available in the NLP industry are deployed as a proprietary service or API [\[26\]](#page-10-5). Hence, several works attempt to adapt them for customized datasets using ZOO algorithms as the model weights and gradients are not available  $[4,21,22]$  $[4,21,22]$  $[4,21,22]$  $[4,21,22]$ . BBT  $[22]$  and BBTv2  $[21]$  use a ZOO method called Covariance Matrix Adaptation Evolution Strategy (CMA-ES) [\[7](#page-9-5), [8](#page-9-6)], while RLPrompt [\[4](#page-9-4)] uses a reinforcement learning gradient-free based method to tune prompts, which are then added to the input to the foundation model. However, reinforcement learning and evolutionary strategy-based methods tend to show high variance and are unstable [\[15\]](#page-9-7). Further, they do not work as well for vision tasks [\[17](#page-9-3)]. There are very few methods that perform Black-Box adaptation for vision tasks [\[17](#page-9-3),[23\]](#page-10-8). BAR [\[23\]](#page-10-8) uses a one-sided approximation for gradient estimation. However, this was found to be inaccurate empirically by a succeeding approach called BlackVIP [\[17](#page-9-3)]. BlackVIP uses a two-sided approximation for gradient estimation and proposes a different ZOO method for tuning weights. However, both BAR and BlackVIP are proposed only for the classification task with CLIP [\[18\]](#page-10-1) as the Black-Box foundation model. In this work, we pioneer Black-Box adaptation for the recently proposed task of prompted segmentation.

# **3 Proposed Method**

**Model Architecture:** Foundation models for segmentation usually perform the task of prompted segmentation. Given an input image and a prompt, the foundation model produces a segmentation mask that corresponds to the given prompt. Hence, for BAPS, we consider an image and a point prompt as the inputs. The overview of BAPS is shown in Fig. [2.](#page-3-0) The input image is passed through a pre-trained image encoder that produces image embeddings. We use

the Vision Transformer (ViT) encoder with Masked Autoencoder (MAE) pretraining [\[9](#page-9-8)] as our image encoder because of its strong innate understanding of images. Hence, it generates highly representative features. On the other hand, the point prompt is converted into positional embeddings based on its relative position in the image. This uses a sinusoid function for generating the embeddings, similar to ViT [\[5\]](#page-9-9).

The image and prompt embeddings are then concatenated and passed to a module called the Image-Prompt Decoder (IP-Decoder). This module is a deconvolution network that generates a visual prompt as its output. This is then added to the original image and passed to the Black-Box foundation model along with the original point prompt. Thus, the job of the IP-Decoder is to learn a residual visual prompt that when added to the original image, will make it easier for the foundation model to segment out the correct shape. Please note that the IP-Decoder is the only trainable module in BAPS.

Once the Black-Box model generates the prediction, it is compared with the label using a sum of the Binary Cross Entropy (BCE) loss and dice loss [\[20\]](#page-10-9). However, note that the gradients cannot be backpropagated since the weights in the Black-Box model are not available. Hence, we use a ZOO method called SPSA-GC [\[17](#page-9-3)], which we describe next.

Image /page/3/Figure/4 description: This is a diagram illustrating a method called BAPS. The diagram shows an image encoder and a positional embedding module that process an input image and a positional embedding respectively. These processed inputs are then concatenated and fed into an IP decoder. The output of the IP decoder is combined with a noisy image through an addition operation, and this combined output is then fed into a Blackbox FM module. The Blackbox FM module also receives input from a zero-order optimization process, indicated by a dashed red line. The output of the Blackbox FM module is a predicted segmentation mask, denoted as \hat{y}, which is compared to the ground truth segmentation mask, denoted as y, using a loss function \mathcal{L}(y, \hat{y}).

<span id="page-3-0"></span>**Fig. 2.** Overview of BAPS.

**SPSA-GC:** Simultaneous Perturbation Stochastic Approximation (SPSA) [\[19\]](#page-10-10) is a Zero Order Optimization (ZOO) technique that estimates gradients effectively using a two-sided approximation of the derivative. Given a set of parameters  $\phi$  and a loss function L, the estimated gradients  $\hat{q}$  are given as follows:

$$
\hat{g}_i = \frac{\mathbb{L}(\phi_i + c\Delta_i) - \mathbb{L}(\phi_i - c\Delta_i)}{2c} \Delta_i^{-1},\tag{1}
$$

where  $c \in [0, 1]$  is a hyperparameter and  $\Delta$  represents a random perturbation vector of the same shape as  $\phi$ . Each element of  $\Delta$  is sampled uniformly from  $[-1, -0.5] \cup [0.5, 1]$ . Here, i represents the iteration number. This estimate of the gradient can be used to update the weights of the model as  $\phi_{i+1} \leftarrow \phi_i - \alpha \hat{g}_i$ , where  $\alpha$  is the learning rate. While SPSA is a good approximator, it has been shown that it can lead to slower convergence [\[17\]](#page-9-3). Hence, in SPSA-GC a momentum term  $m$  is added while updating the weights as follows:

$$
m_{i+1} = \beta m_i - \alpha \hat{g}_i(\phi_i + \beta m_i), \quad \phi_{i+1} = \phi_i + m_{i+1}, \tag{2}
$$

where  $\beta$  denotes the weight of the momentum. This allows the IP Decoder to train quicker and more stably. However, we found that given a point prompt, the foundation model produces a reasonable mask without any sort of optimization (zero-shot performance). Thus, the approximated gradients are small in magnitude and the system gets stuck at local minimas more often. To alleviate this, as an implementation detail, we add a mechanism to detect the local minima and increase the learning rate for some iterations during training to help the model get out of the local minima. This is detailed in the supplementary document.

# **4 Experiments and Results**

We choose the recently proposed Segment Anything Model (SAM) [\[13\]](#page-9-0) and Med-SAM [\[16](#page-9-1)] as the Black-Box foundation models for our experiments. SAM and MedSAM are foundation models for the task of promptable segmentation. Given an image and a prompt in the form of point, text, mask or bounding box, they can segment out the object of interest corresponding to the prompt. SAM is trained on a large corpus of natural images while MedSAM is tuned on medical data including 3D and 2D images. Majority of the training data of MedSAM includes CT and MRI images. We use the point-based prompt for our experiments. Thus, for a given image and a point prompt, we pass it through BAPS and generate a visual prompt. This is added to the original image and sent to the Black-Box foundation model along with the point prompt. We evaluate our method on four different datasets and calculate the Dice Score (DSC) and Hausdorff Distance at the 95th percentile (HD95). For our baselines, we consider the zero-shot performance of the foundation model without any adaptation, and Visual Prompting (VP) [\[12\]](#page-9-10) with SPSA-GC. We measure the upper bound of the segmentation performance using a white box adaptation of SAM and MedSAM using LoRA [\[10\]](#page-9-11), similar to various SAM-based adaptation methods [\[25,](#page-10-3)[27\]](#page-10-4).

**Datasets.** We use four widely used publicly available datasets for our experiments. Kvasir-Seg [\[11](#page-9-12)] consists of gastrointestinal polyp images, divided into 600 training, 100 validation, and 300 test images. ISIC2018 [\[3](#page-9-13)] contains dermoscopic images for skin lesion segmentation. It is divided into 2594 images for training, 100 for validation, and 100 for testing. The third dataset is REFUGE [\[6](#page-9-14)], which has retinal images for optic disk and optic cup segmentation. It is divided into 800 training, 800 validation, and 800 testing images. The fourth dataset is Endovis 17 [\[1\]](#page-8-0), which has images of endoscopic surgery. It is further divided into 2878 testing and 3231 training images, out of which we use 366 for validation. The dataloaders will be made available along with the code after the review process.

**Experimental Setup.** For all the datasets, we use the same set of data augmentations. These include random rotation up to 10  $\degree$ C, random brightness changes with scale 2, and random saturation changes with scale 2. The images are scaled to the resolution  $512 \times 512$ . Based on the validation set performance of these datasets, we set hyperparameters of the model as:  $c = 0.01$ ,  $\alpha = 0.005$ for Endovis17 and  $\alpha = 0.01$  for other datasets,  $\beta = 0.9$ . For all datasets, we use a batch size of 32. Training is done on a single Nvidia RTX A5000 GPU and uses only 6GB of memory. Note that in our case, we also run the Black-Box on the same GPU. However, in practice, training would be cheaper since the forward propagation in the Black-Box will occur at the server. The Black-Boxes SAM and MedSAM both are initialized using their ViT-base checkpoints.

<span id="page-5-0"></span>**Table 1.** Our approach improves over the zero-shot performance of the Black-Box model SAM (row 1). Having the IP Decoder improves performance over direct visual prompt tuning (row 2). The performance of our method is upper bounded by the white box adaptation methods like LoRA (row 4).

| Foundation Model - SAM     |             |              |             |             |             |               |             |              |
|----------------------------|-------------|--------------|-------------|-------------|-------------|---------------|-------------|--------------|
| Method                     | Kvasir-Seg  |              | ISIC2018    |             | REFUGE      |               | Endovis 17  |              |
|                            | DSC (†)     | HD95 (↓)     | DSC (†)     | HD95 (↓)    | DSC (†)     | HD95 (↓)      | DSC (†)     | HD95 (↓)     |
| SAM (ZS) [13]              | 0.68        | 99.3         | 0.66        | 85.98       | 0.38        | 214.98        | 0.60        | 89.46        |
| VP [12]                    | 0.69        | 99.3         | 0.70        | 70.96       | 0.39        | 201.67        | 0.63        | 83.77        |
| <b>BAPS (Our Approach)</b> | <b>0.72</b> | <b>83.55</b> | <b>0.74</b> | <b>70.3</b> | <b>0.44</b> | <b>176.67</b> | <b>0.65</b> | <b>81.56</b> |
| Whitebox LoRA [10]         | 0.88        | 22.23        | 0.85        | 30.95       | 0.85        | 19.22         | 0.68        | 45.39        |

**Results.** We tabulate the quantitative results on each of the datasets in Table [1](#page-5-0) for SAM and in Table [2](#page-6-0) for MedSAM. For all four datasets, we see an average of 5% improvement in the Dice Score with our method over the foundation model SAM and an average of 7% over MedSAM. For all the results, evaluation is done five times with randomly selected point prompts and the mean value is listed in the table. The standard deviation in each case is less than 0.01. The increase in performance can be attributed to the strong pre-trained encoder and IP Decoder of BAPS, which generates a visual prompt as a function of the input image and the point prompt. Some samples of the modified images after adding the visual prompt are shown in Fig. [4.](#page-8-1) In row 2, we compare our method with simply adding the same visual prompt for each image-point pair (no encoder or I-P Decoder). We see significant improvement, showing the effectiveness of the encoder-decoder structure. This can also be seen in supplementary Fig. 1, where we plot the training progress of BAPS in comparison to VP, with MedSAM as the Black-Box for ISIC2018 and REFUGE. For both these cases, we see that the average error for BAPS decreases consistently with the number of iterations. All results with BAPS have a p-value of at most  $10^{-8}$ . Qualitative results are shown in Fig. [3.](#page-6-1)

<span id="page-6-0"></span>**Table 2.** Our approach improves over the zero-shot performance of the Black-Box model MedSAM (row 1). Having the IP Decoder improves performance over direct visual prompt tuning (row 2). The performance of our method is upper bounded by the white box adaptation methods like LoRA (row 4).

| Foundation Model - MedSAM  |             |              |             |             |             |               |             |             |
|----------------------------|-------------|--------------|-------------|-------------|-------------|---------------|-------------|-------------|
| Method                     | Kvasir-Seg  |              | ISIC2018    |             | REFUGE      |               | Endovis 17  |             |
|                            | DSC (↑)     | HD95 (↓)     | DSC (↑)     | HD95 (↓)    | DSC (↑)     | HD95 (↓)      | DSC (↑)     | HD95 (↓)    |
| MedSAM (ZS) [16]           | 0.68        | 98.4         | 0.70        | 87.23       | 0.36        | 214.29        | 0.63        | 87.52       |
| VP [12]                    | 0.68        | 90.36        | 0.71        | 99.2        | 0.36        | 214.48        | 0.63        | 85.58       |
| <b>BAPS (Our Approach)</b> | <b>0.72</b> | <b>80.01</b> | <b>0.79</b> | <b>66.6</b> | <b>0.44</b> | <b>168.07</b> | <b>0.65</b> | <b>82.4</b> |
| Whitebox LoRA [10]         | 0.90        | 20.05        | 0.86        | 30.1        | 0.85        | 19.15         | 0.68        | 42.92       |

Image /page/6/Figure/3 description: This image displays a grid of medical images and their corresponding segmentation masks. The grid is organized into four rows, each representing a different dataset: ISIC 2018, KVASIR-Seg, REFUGES, and Endovis 17. Each row contains an original image on the left, followed by five segmentation masks. The segmentation masks are labeled at the bottom as GT (Ground Truth), BAPS (Ours), VP, and SAM (Zero Shot). The original images show various medical conditions, including a skin lesion, endoscopic views of the digestive tract, an eye, and surgical tissue. The segmentation masks visually represent the identified regions of interest within each original image, with varying degrees of accuracy and detail across the different datasets and segmentation methods.

<span id="page-6-1"></span>**Fig. 3.** Qualitative Results on all the datasets. GT - ground truth, VP - visual prompting [\[12](#page-9-10)]. The green dot in the image denotes the point prompt given to the Black-Box foundation model

# **5 Ablation Analysis**

**Ablation over Pretrained Image Encoder.** We use the MAE pre-trained ViT image encoder from Meta (ViT-MAE) [\[9](#page-9-8)] since it gives a stronger featurization of images over other encoders. To verify this claim, we make an ablation analysis by changing the pre-trained encoder and measuring the performance on the ISIC 2018 dataset. The results are shown in Table [3,](#page-7-0) where we compare the results of ViT-MAE with three popular image encoders. We find that ViT-MAE outperforms CLIP  $[18]$  $[18]$  and DINO-Resnet50  $[2]$  $[2]$  significantly. ViT  $[5]$  $[5]$  is a strong encoder. However, the MAE pretraining further improves the downstream performance by 2%

| Image encoder              | DSC         |
|----------------------------|-------------|
| CLIP [18]                  | 0.65        |
| DINO-Resnet50 [2]          | 0.69        |
| ViT [5]                    | 0.72        |
| ViT-MAE [9] (used in BAPS) | <b>0.74</b> |

<span id="page-7-0"></span>**Table 3.** Ablation analysis of different pre-trained image encoders on ISIC 2018 dataset

<span id="page-7-1"></span>**Table 4.** Ablation analysis of component-wise importance of the zero-order optimization method.

| Optimization method    | DSC         |
|------------------------|-------------|
| SAM (ZS) [13] (No ZOO) | 0.66        |
| SPSA [19]              | 0.68        |
| SPSA-GC (Ours)         | <b>0.74</b> |

**Ablation Over SPSA.** We empirically test the effectiveness of SPSA-GC as a ZOO method by removing each of the components of the algorithm and measuring performance on the ISIC 2018 dataset. As seen in Table [4,](#page-7-1) we start with the zero-shot performance of SAM [\[13](#page-9-0)], which has no required optimization. Using just SPSA [\[19\]](#page-10-10) with zero momentum increases this performance by only 1%. This is further improved by adding a momentum of 0.9 as suggested by SPSA-GC [\[17](#page-9-3)], finally giving a DSC of 0.74.

**Visualizing the Modified Images:** To test the effectiveness of BAPS, we compare the visual results of the Black-Box foundation model MedSAM with and without adding the visual prompt generated by BAPS. These results are shown in Fig. [4.](#page-8-1) Here, the zero-shot prediction of MedSAM generates inaccurate masks as seen in column 3. However, after adding the learnt visual prompt to the original image, MedSAM can correctly generate the masks. The modified image can be seen in column 4 of the figure.

**Memory Analysis.** We perform an analysis of the number of parameters required to be trained by the client and the memory requirement of the generated checkpoint. Here, the client refers to the machine used at the downstream application level. Note that for our method, SAM is a Black-Box and does not have to be run on the client machine, resulting in significantly lower memory consumption. This is one of the advantages of the proposed Black-Box paradigm. BAPS requires just one hundred thousand parameters to be tuned, thus making the memory required to store the model checkpoint only 0.4 MB as compared to hundreds to thousands of MBs required by whitebox adaptation methods, which have at least ten times as many parameters.

Image /page/8/Picture/1 description: This image displays a grid of five columns and three rows, showcasing results related to image segmentation of skin lesions. The columns are labeled 'Original Image', 'GT', 'Zero-Shot Prediction', 'Modified Image by BAPS', and 'Prediction on Modified Image'. Each row presents a different case. The first row shows an original image of a mole, its ground truth segmentation, a zero-shot prediction, a modified version of the original image, and the prediction on the modified image. The second row follows the same pattern with a different mole, but the zero-shot prediction appears to be incorrect, covering a large white area instead of the lesion. The third row shows another original image of a mole, its ground truth, a zero-shot prediction that closely matches the ground truth, a modified image, and the prediction on the modified image. The overall presentation compares the effectiveness of different segmentation methods on original and modified images.

**Fig. 4.** Visualizing the effect of adding the visual prompt on ISIC 2018 dataset. The Black-Box used for these results is MedSAM.

<span id="page-8-1"></span>

# **6 Conclusion**

In this work, we proposed one of the first Black-Box adaptation methods, called BAPS, for the adaptation of foundation models for prompted segmentation. BAPS consists of a pretrained image encoder and a trainable IP decoder, that generates a visual prompt as a function of the input image and given prompt. This visual prompt is added to the original image and given to the foundation model. The IP decoder is trained using a novel Zero Order Optimization (ZOO) method called SPSA-GC. We test BAPS on four different public datasets in medical segmentation and verify its effectiveness for the recently proposed and popular foundation models SAM and MedSAM. Finally, we carry out an ablation study to gauge the effectiveness of different design decisions of BAPS. Thus, our proposed method can efficiently perform Black-Box adaptation of SAM without the requirement of gradients.

**Disclosure of Interest.** This research was supported by a grant from the National Institutes of Health, USA; R01EY033065. The content is solely the responsibility of the authors and does not necessarily represent the official views of the National Institutes of Health. The authors have no competing interests in the paper.

# **References**

- <span id="page-8-0"></span>1. Allan, M., Shvets, A., Kurmann, T., Zhang, Z., Duggal, R., Su, Y.H., Rieke, N., Laina, I., Kalavakonda, N., Bodenstedt, S., Herrera, L., Li, W., Iglovikov, V., Luo, H., Yang, J., Stoyanov, D., Maier-Hein, L., Speidel, S., Azizian, M.: 2017 robotic instrument segmentation challenge (2019)
- <span id="page-8-2"></span>2. Caron, M., Touvron, H., Misra, I., Jégou, H., Mairal, J., Bojanowski, P., Joulin, A.: Emerging properties in self-supervised vision transformers. CoRR **abs/2104.14294** (2021), <https://arxiv.org/abs/2104.14294>

- <span id="page-9-13"></span>3. Codella, N., Rotemberg, V., Tschandl, P., Celebi, M.E., Dusza, S., Gutman, D., Helba, B., Kalloo, A., Liopyris, K., Marchetti, M., Kittler, H., Halpern, A.: Skin lesion analysis toward melanoma detection 2018: A challenge hosted by the international skin imaging collaboration (isic) (2019)
- <span id="page-9-4"></span>4. Deng, M., Wang, J., Hsieh, C.P., Wang, Y., Guo, H., Shu, T., Song, M., Xing, E.P., Hu, Z.: Rlprompt: Optimizing discrete text prompts with reinforcement learning (2022)
- <span id="page-9-9"></span>5. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., Uszkoreit, J., Houlsby, N.: An image is worth 16x16 words: Transformers for image recognition at scale. In: International Conference on Learning Representations (2021), [https://openreview.](https://openreview.net/forum?id=YicbFdNTTy) [net/forum?id=YicbFdNTTy](https://openreview.net/forum?id=YicbFdNTTy)
- <span id="page-9-14"></span>6. Fu, H., Li, F., Orlando, J.I., Bogunović, H., Sun, X., Liao, J., Xu, Y., Zhang, S., Zhang, X.: Refuge: Retinal fundus glaucoma challenge (2019), [https://dx.doi.org/](https://dx.doi.org/10.21227/tz6e-r977) [10.21227/tz6e-r977](https://dx.doi.org/10.21227/tz6e-r977)
- <span id="page-9-5"></span>7. Hansen, N., M¨uller, S.D., Koumoutsakos, P.: Reducing the time complexity of the derandomized evolution strategy with covariance matrix adaptation (cma-es). Evol. Comput. **11**(1), 1–18 (2003)
- <span id="page-9-6"></span>8. Hansen, N., Ostermeier, A.: Completely derandomized self-adaptation in evolution strategies. Evol. Comput. **9**(2), 159–195 (2001). [https://doi.org/10.1162/](https://doi.org/10.1162/106365601750190398) [106365601750190398](https://doi.org/10.1162/106365601750190398)
- <span id="page-9-8"></span>9. He, K., Chen, X., Xie, S., Li, Y., Dollár, P., Girshick, R.B.: Masked autoencoders are scalable vision learners. CoRR **abs/2111.06377** (2021), [https://arxiv.org/](https://arxiv.org/abs/2111.06377) [abs/2111.06377](https://arxiv.org/abs/2111.06377)
- <span id="page-9-11"></span>10. Hu, E.J., yelong shen, Wallis, P., Allen-Zhu, Z., Li, Y., Wang, S., Wang, L., Chen, W.: LoRA: Low-rank adaptation of large language models. In: International Conference on Learning Representations (2022), [https://openreview.net/forum?](https://openreview.net/forum?id=nZeVKeeFYf9) [id=nZeVKeeFYf9](https://openreview.net/forum?id=nZeVKeeFYf9)
- <span id="page-9-12"></span>11. Jha, D., Smedsrud, P.H., Riegler, M.A., Halvorsen, P., de Lange, T., Johansen, D., Johansen, H.D.: Kvasir-seg: A segmented polyp dataset. In: International Conference on Multimedia Modeling. pp. 451–462. Springer (2020)
- <span id="page-9-10"></span>12. Jia, M., Tang, L., Chen, B.C., Cardie, C., Belongie, S., Hariharan, B., Lim, S.N.: Visual prompt tuning. In: Computer Vision - ECCV 2022: 17th European Conference, Tel Aviv, Israel, October 23-27, 2022, Proceedings, Part XXXIII. p. 709- 727. Springer-Verlag, Berlin, Heidelberg (2022), [https://doi.org/10.1007/978-3-](https://doi.org/10.1007/978-3-031-19827-4_41) [031-19827-4](https://doi.org/10.1007/978-3-031-19827-4_41) 41
- <span id="page-9-0"></span>13. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., Dollár, P., Girshick, R.: Segment anything (2023)
- <span id="page-9-2"></span>14. Lee, H.H., Gu, Y., Zhao, T., Xu, Y., Yang, J., Usuyama, N., Wong, C., Wei, M., Landman, B.A., Huo, Y., Santamaria-Pang, A., Poon, H.: Foundation models for biomedical image segmentation: A survey (2024)
- <span id="page-9-7"></span>15. Liu, S., Chen, P.Y., Kailkhura, B., Zhang, G., Hero, A.O., III., Varshney, P.K.: A primer on zeroth-order optimization in signal processing and machine learning: Principals, recent advances, and applications. IEEE Signal Process. Mag. **37**(5), 43–54 (2020)
- <span id="page-9-1"></span>16. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nat. Commun. **15**, 654 (2024)
- <span id="page-9-3"></span>17. Oh, C., Hwang, H., Lee, H.y., Lim, Y., Jung, G., Jung, J., Choi, H., Song, K.: Blackvip: Black-box visual prompting for robust transfer learning. In: Proceed-

ings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 24224–24235 (June 2023)

- <span id="page-10-1"></span>18. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., Krueger, G., Sutskever, I.: Learning transferable visual models from natural language supervision (2021)
- <span id="page-10-10"></span>19. Spall, J.: Multivariate stochastic approximation using a simultaneous perturbation gradient approximation. IEEE Trans. Autom. Control **37**(3), 332–341 (1992)
- <span id="page-10-9"></span>20. Sudre, C.H., Li, W., Vercauteren, T.K.M., Ourselin, S., Cardoso, M.J.: Generalised dice overlap as a deep learning loss function for highly unbalanced segmentations. Deep learning in medical image analysis and multimodal learning for clinical decision support : Third International Workshop, DLMIA 2017, and 7th International Workshop, ML-CDS 2017, held in conjunction with MICCAI 2017 Quebec City, QC,... **2017**, 240–248 (2017), <https://api.semanticscholar.org/CorpusID:21957663>
- <span id="page-10-6"></span>21. Sun, T., He, Z., Qian, H., Zhou, Y., Huang, X., Qiu, X.: BBTv2: Towards a gradient-free future with large language models. In: Goldberg, Y., Kozareva, Z., Zhang, Y. (eds.) Proceedings of the 2022 Conference on Empirical Methods in Natural Language Processing. pp. 3916–3930. Association for Computational Linguistics, Abu Dhabi, United Arab Emirates (Dec 2022), [https://aclanthology.org/](https://aclanthology.org/2022.emnlp-main.259) [2022.emnlp-main.259](https://aclanthology.org/2022.emnlp-main.259)
- <span id="page-10-7"></span>22. Sun, T., Shao, Y., Qian, H., Huang, X., Qiu, X.: Black-box tuning for languagemodel-as-a-service. In: Chaudhuri, K., Jegelka, S., Song, L., Szepesvari, C., Niu, G., Sabato, S. (eds.) Proceedings of the 39th International Conference on Machine Learning. Proceedings of Machine Learning Research, vol. 162, pp. 20841–20855. PMLR (17–23 Jul 2022), <https://proceedings.mlr.press/v162/sun22e.html>
- <span id="page-10-8"></span>23. Tsai, Y.Y., Chen, P.Y., Ho, T.Y.: Transfer learning without knowing: Reprogramming black-box machine learning models with scarce data and limited resources. In: III, H.D., Singh, A. (eds.) Proceedings of the 37th International Conference on Machine Learning. Proceedings of Machine Learning Research, vol. 119, pp. 9614–9624. PMLR (13–18 Jul 2020), [https://proceedings.mlr.press/v119/tsai20a.](https://proceedings.mlr.press/v119/tsai20a.html) [html](https://proceedings.mlr.press/v119/tsai20a.html)
- <span id="page-10-0"></span>24. Wang, R., Lei, T., Cui, R., Zhang, B., Meng, H., Nandi, A.K.: Medical image segmentation using deep learning: A survey. IET Image Processing **16**(5), 1243–1267 (2022), <https://ietresearch.onlinelibrary.wiley.com/doi/abs/10.1049/ipr2.12419>
- <span id="page-10-3"></span>25. Wu, J., Ji, W., Liu, Y., Fu, H., Xu, M., Xu, Y., Jin, Y.: Medical sam adapter: Adapting segment anything model for medical image segmentation (2023)
- <span id="page-10-5"></span>26. Ye, J., Chen, X., Xu, N., Zu, C., Shao, Z., Liu, S., Cui, Y., Zhou, Z., Gong, C., Shen, Y., Zhou, J., Chen, S., Gui, T., Zhang, Q., Huang, X.: A comprehensive capability analysis of gpt-3 and gpt-3.5 series models (2023)
- <span id="page-10-4"></span>27. Zhang, K., Liu, D.: Customized segment anything model for medical image segmentation (2023)
- <span id="page-10-2"></span>28. Zou, X., Yang, J., Zhang, H., Li, F., Li, L., Wang, J., Wang, L., Gao, J., Lee, Y.J.: Segment everything everywhere all at once (2023)

Image /page/11/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line on the left side and a flag shape on the right side. The button is light gray with a subtle gradient, and the text is dark gray.

# CT2Rep: Automated Radiology Report Generation for 3D Medical Imaging

Yuexi  $Du^1$ , Brian Chang<sup>1</sup>, and Nicha C. Dvornek<sup>1,2( $\boxtimes$ )</sup>

<sup>1</sup> Department of Biomedical Engineering, Yale University, New Haven, CT, USA

<sup>2</sup> Department of Radiology and Biomedical Imaging, Yale University, New Haven,

CT, USA

{yuexi.du,brian.chang,nicha.dvornek}@yale.edu

Abstract. Recent advancements in Contrastive Language-Image Pretraining (CLIP) [\[21\]](#page-21-0) have demonstrated notable success in self-supervised representation learning across various tasks. However, the existing CLIPlike approaches often demand extensive GPU resources and prolonged training times due to the considerable size of the model and dataset, making them poor for medical applications, in which large datasets are not always common. Meanwhile, the language model prompts are mainly manually derived from labels tied to images, potentially overlooking the richness of information within training samples. We introduce a novel language-image Contrastive Learning method with an Efficient large language model and prompt Fine-Tuning (CLEFT) that harnesses the strengths of the extensive pre-trained language and visual models. Furthermore, we present an efficient strategy for learning context-based prompts that mitigates the gap between informative clinical diagnostic data and simple class labels. Our method demonstrates state-of-the-art performance on multiple chest X-ray and mammography datasets compared with various baselines. The proposed parameter efficient framework can reduce the total trainable model size by 39% and reduce the trainable language model to only 4% compared with the current BERT encoder. (The official implementation is available at [https://github.com/](https://github.com/XYPB/CLEFT) [XYPB/CLEFT.](https://github.com/XYPB/CLEFT))

**Keywords:** Deep Learning · Multi-Modal · Contrastive Learning · Chest X-ray · Mammography

# 1 Introduction

Contrastive learning [\[9\]](#page-20-0) has emerged as a pivotal paradigm in deep learning due to its ability to construct a robust feature space and generalize well to downstream

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_44.](https://doi.org/10.1007/978-3-031-72390-2_44)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 465–475, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_44)\_44

Image /page/12/Figure/1 description: This is a bubble chart comparing different models based on their CheXpert-5x200 Zero-Shot Accuracy (%) on the y-axis and model names on the x-axis. The size of the bubbles represents the Full Model Size (M), and the color of the bubbles represents the Trainable Text-Encoder Parameters. The models shown are CLIP-ViT (20.16 accuracy, 90M size, low parameters), ConViRT-R50 (41.88 accuracy, 120M size, low parameters), GLORiA-R50 (43.28 accuracy, 120M size, low parameters), MGCA-ViT (51.35 accuracy, 150M size, medium parameters), MedCLIP-Swin (59.42 accuracy, 150M size, medium parameters), Ours-Prefix (60.55 accuracy, 150M size, medium parameters), Ours-IA3 (65.61 accuracy, 150M size, high parameters), and Ours-LoRA (66.74 accuracy, 150M size, high parameters). A dashed line indicates that Ours-LoRA is 3.31x better than CLIP-ViT and 39% smaller than MGCA-ViT.

<span id="page-12-0"></span>Fig. 1. Zero-shot Performance on CheXpert-5x200. We compare the performance of our method with multiple baselines on zero-shot CheXpert-5x200 classification. For each model, the diameter denotes the total number of trainable parameters, the color shows the number of trainable text encoder parameters, and the number reports accuracy. Our method outperforms all baselines with better parameter efficiency.

tasks. Traditional contrastive learning methods  $[5,6,9,20]$  $[5,6,9,20]$  $[5,6,9,20]$  $[5,6,9,20]$  $[5,6,9,20]$  $[5,6,9,20]$  focus on building positive pairs from different views of the same input while distinguishing it from other data in the feature space. Such a contrastive paradigm allows the model to learn a robust representation when an exhaustive amount of training data is provided. Recently, Contrastive Language-Image Pre-training (CLIP) [\[21](#page-21-0)], which utilizes both visual and textual data insights, has extended contrastive learning to multimodality data and reaped immense benefits from advancements in language models [\[3](#page-19-0)[,22](#page-21-1)[,25](#page-21-2)]. CLIP supervises the image encoder with a language encoder trained simultaneously. By contrasting the features learned from both the image and language encoder, the CLIP model learns to bridge the textual and visual data in the high-dimensional embedding space, allowing it to use knowledge learned from the language model to guide visual encoder training. A properly pre-trained contrastive visual encoder can be adapted to multiple downstream tasks with a minimum amount of labeled data required.

However, difficulties arise when adapting CLIP from the natural image-text pair to the medical domain, where access to data can be severely restricted by various factors, including security and privacy concerns, difficulty in obtaining expert annotations, and expensive imaging. The limited image-text pairs in the medical domain constrain the potential of CLIP models trained from scratch. While existing medical CLIP methods [\[12](#page-20-4)[,26](#page-21-3)[–28\]](#page-21-4) use a pre-trained BERT language model [\[1\]](#page-19-1), its limited model size constrained the expression ability in the embedding space and further constrained the pre-training capability. Also, the common approach of handcrafting textual prompts for CLIP [\[12](#page-20-4), [21,](#page-21-0) [27\]](#page-21-5) leads to a lack of diversity in text training prompts, which can result in the catastrophic forgetting phenomenon [\[19](#page-20-5)] in the text encoder and limit the model's performance.

In light of these challenges, we introduce a novel language-image Contrastive Learning method with Efficient LLM and prompt context Fine-Tuning (CLEFT) to boost overall performance. We leverage the strengths of vast pre-trained large language models (LLMs) and visual models, adapting them to the medical domain to counterbalance the scarcity of medical data and address the constraints due to

Image /page/13/Figure/1 description: The image displays two distinct diagrams, labeled (a) and (b), illustrating different approaches to model pretraining and prompt context learning. Diagram (a), titled "PEFT CLIP Model Pretraining," depicts a GPT-2 block structure with multiple layers including Causal Self-Attention, Layer Norm, Feed-Forward, and a PEFT Module. This block is repeated 32 times. It shows inputs passing through these layers to produce outputs. A "Fixed Prompts" box containing text about a chest X-ray with potential findings like "Edema" and "Lung Lesion" is connected to a "BioMedLM-3B" model. Input images are processed by a "DINOv2-ViT" model, and the outputs from both models are combined in a matrix format with tokens (T1 to TN) and image features (I1 to IN). Backpropagation is indicated from the combined output back to the "BioMedLM-3B" model. Diagram (b), titled "Prompt Context Learning," shows a "BioMedLM-3B" model that is "Frozen" (indicated by a lock icon). It receives input from "Trainable Context Prompting Token" which consists of L trainable tokens [v1] to [vL] and a CLSi label. Input images are processed by a "DINOv2-ViT" model, also marked as "Frozen." The outputs from the "DINOv2-ViT" are combined with tokens (T1 to TCLS) to form a sequence. Backpropagation is shown from this sequence back to the "BioMedLM-3B" model. A legend at the top right indicates "Partially Trainable" (dashed outline), "Fully Trainable" (fire icon), and "Frozen" (lock icon) states for different model components.

<span id="page-13-0"></span>Fig. 2. Proposed Method Framework. (a) Language-image contrastive learning with an LLM by utilizing PEFT. Fixed handcrafted prompts are used in this stage. (b) Prompt context learning with the pre-trained image and text encoder via classification.

language model size. Our work is the first in the realm of medical imaging to scale up the language model in the language-image pre-training to the billion parameter level, leading to greatly improved model generalization ability. Further, our approach is parameter efficient, solely focusing on optimizing smaller adaptation layers [\[11,](#page-20-6)[15](#page-20-7)] in the LLM, thus conserving GPU resources without compromising knowledge acquired from natural language. Our model reduces the total number of trainable parameters by 39% with only 4% of the trainable language model parameters compared to the vanilla CLIP [\[22](#page-21-1)] (Fig. [1\)](#page-12-0). In addition, our method learns a context-based prompt with prompt fine-tuning [\[30\]](#page-21-6) to mitigate the bias introduced by the undiversified hand-crafted prompt and generalize to different unseen data. The proposed method shows state-of-the-art performance on two public chest Xray datasets [\[13](#page-20-8)[,24](#page-21-7)] and a public mammography dataset [\[14\]](#page-20-9)

## 2 Methods

The proposed CLEFT framework is in Fig. [2.](#page-13-0) We first efficiently incorporate an LLM into the CLIP [\[21\]](#page-21-0) framework. We then train the learnable prompt context with frozen pre-trained text and visual encoders to further improve generalization.

### 2.1 Boosting CLIP with an LLM

Contrastive Language-Image Pre-training. The conventional CLIP [\[21\]](#page-21-0) framework includes a vision encoder and a text encoder with the corresponding projection head that encodes the image-text pair  $(x_I, x_{\overline{I}_i})$  sampled from the training data to feature  $(I_i, T_i)$  (Fig. [2\(](#page-13-0)a)). The projection head maps the embedding from two different modalities into the same feature space and therefore allows the model to bridge two modalities. The multi-modal contrastive

learning optimizes both image-to-text and text-to-image InfoNCE [\[9](#page-20-0)] loss symmetrically:

<span id="page-14-0"></span>
$$
\mathcal{L} = -\frac{1}{2N} \left[ \sum_{i=0}^{N} \log \frac{\exp(I_i \cdot T_i/\tau)}{\sum_{j \neq i}^{N} \exp(I_i \cdot T_j/\tau)} \right]_{\mathcal{L}_{I2T}} + \left[ \sum_{i=0}^{N} \log \frac{\exp(T_i \cdot I_i/\tau)}{\sum_{j \neq i}^{N} \exp(T_i \cdot I_j/\tau)} \right]_{\mathcal{L}_{T2I}} \right], \quad (1)
$$

where N is the number of samples within a batch, and  $\tau$  is the learnable softmax temperature. Optimizing the loss will reduce the cosine distance between paired image and text features while repelling unpaired samples. However, proper contrastive learning requires a large amount of training data as well as a large negative sample size (*i*.*e*., batch size), and it is hard to gather large datasets in the medical domain and GPU resources are limited. Thus, we propose to replace the vanilla encoders trained from scratch with fully pre-trained models to alleviate this issue. Initializing the text encoder with a pre-trained model provides higher-quality supervision since the pre-trained model already provides a robust feature space [\[4](#page-19-2)], which simplifies the contrastive learning procedure.

PEFT LLM as Text Encoder. To further explore the potential of the CLIP framework, we take advantage of a medical LLM that was pre-trained with a large amount of text data. We use a GPT-2-based causal language model as our text encoder rather than a BERT-based language model, which was widely used in previous medical CLIP models [\[12](#page-20-4)[,26](#page-21-3)[–28](#page-21-4)], since the causal LLM has shown a better capability as it scales up to over a billion parameters [\[3\]](#page-19-0). A stronger text encoder allows the model to embed the input into a more robust feature space with less training. However, LLMs are more likely to overfit on undiversified text data given their strong expression ability. To avoid this issue and maintain the robust pre-trained knowledge within the LLM efficiently, we introduce the parameter-efficient fine-tuning (PEFT) module to the frozen LLM (Fig.  $2(a)$  $2(a)$ ), where a small set of trainable parameters are injected into each transformer block, adjusting the original output of the attention layers slightly. This reduces the number of trainable parameters during training to no more than 1% of the full large language model size. Common PEFT methods like LoRA [\[11\]](#page-20-6) and IA3 [\[16](#page-20-10)] either adjust the attention output with low-rank bottleneck matrices or scale the key, query, and value outputs. Also, prefix fine-tuning [\[15\]](#page-20-7) introduces extra trainable prefix tokens to the language model to influence the behavior of the attention layers. The nature of these PEFT methods ensures the finetuned output will not deviate too much from the original model and helps avoid the catastrophic forgetting phenomenon [\[19](#page-20-5)]. To further merge the domain gap between pre-training text data and CLIP prompt, we unlock the LLM's embedding layer and update the corresponding token embedding during pre-training.

CLIP as Knowledge Distillation. We further argue that contrastive language-image learning with a pre-trained text encoder can be viewed as a knowledge distillation process, where the numerator in Eq. [\(1\)](#page-14-0) minimizes the

distance between  $I_i$  and  $T_i$ . Given that the language encoder is largely frozen, the language encoder serves more as a teacher model since it already has a wellestablished embedding space that can distinguish between different samples, and its output only changes slightly during training. The fully optimized visual encoder then focuses on aligning its output to the language model's embedding space, which serves as the student model. This allows us to distill the knowledge within the well-pre-trained language model into a generally much smaller vision model and, therefore, further improve its performance. Additionally, since the "teacher model" is still optimized during pre-training, the negative pairs are still necessary.

Model Architecture. We choose GPT-2 [\[22\]](#page-21-1) with 32 causal transformer blocks as the text encoder and the ViT-Base [\[8\]](#page-20-11) with a patch size of 14 as the visual encoder (Fig. [2\(](#page-13-0)a)). Similar to the original CLIP [\[21\]](#page-21-0), we use a randomly initialized linear projection layer to map the embeddings from each encoder to a unified embedding space with the same size. For the text encoder, we use the output embedding of the first [EOS] token since the model is causal and this token encodes all the information from the input. For the PEFT module, we experiment with LoRA  $[11]$  $[11]$ , IA3  $[16]$  $[16]$ , and prefix fine-tuning  $[15]$  $[15]$ . For the visual encoder, we use the averaged embedding of all visual tokens from ViT [\[8\]](#page-20-11) as the visual embedding. We remove the last layer norm for better training stability.

### 2.2 Learning the Context-Based Prompt

To further address the issue of the lack of diversity in the hand-crafted prompts, we introduce a second stage of training that only optimizes a learnable contextbased token of length  $L$  (Fig. [2\(](#page-13-0)b)). After pre-training, we freeze both encoders and replace the original hand-crafted prompt with a series of trainable tokens that then feed into the language model. The same context-based prompt tokens are used from all classes, which ensures the generalization ability of these tokens. Different from the pre-training stage, we optimize the trainable context tokens with a zero-shot classification cross-entropy loss. This allows the prompt tokens to adapt to different classes evenly and avoid the potential shortcut issue. We further initialize these tokens with the embedding of the original hand-crafted caption. If  $L$  is longer than the original caption, we instead initialize the first few tokens according to the random uniform distribution.

## 3 Experiments

### 3.1 Datasets

We evaluate our CLEFT model on two major applications in medical imaging, chest X-ray and mammography. We use the  $CheXpert-1.0$  [\[13](#page-20-8)] for pretraining following GLoRIA [\[12\]](#page-20-4). The dataset has 223,415 images from 65,240 patients with corresponding class labels for 14 different classes. We only use frontal chest radiographs for consistency. We leave out 5% of data for validation. For evaluation, we use the in-domain  $\mathbf{CheXpert-5x200}$  [\[12](#page-20-4)] and out-of-domain RSNA [\[24](#page-21-7)] datasets. The CheXpert-5x200 [\[12](#page-20-4)] is a multi-class classification subset of CheXpert-1.0 with 5 different classes and 200 images in each class. These images are removed from the training set. The RSNA [\[24](#page-21-7)] provides a collection of pneumonia and non-pneumonia images for binary classification. Following Med-CLIP [\[27](#page-21-5)], we sample a 1:1 subset with 8,486 training and 3,538 testing images for this dataset. For the mammography data, we use the **EMBED** [\[14\]](#page-20-9) dataset which contains 364,515 2D mammograms with both BI-RADS [\[23\]](#page-21-8) and breast density labels. We split the data into 70%/10% for training/validation. We evaluate our model on both BI-RADS and density prediction tasks with two balanced subsets sampled from the 20% remaining data. The BI-RADS classification test set contains 7 classes with 200 samples per class. The density classification test set contains 4 classes each with 500 samples.

### 3.2 Baselines and Evaluation Metrics

We compare multiple state-of-the-art baselines. To demonstrate the effectiveness of the CLIP pre-training, we compare with the same  $ViT$  [\[20\]](#page-20-3) model with random initialization and Image-Net [\[7\]](#page-20-12) pre-training. We further compare our model with conventional CLIP [\[21\]](#page-21-0), ResNet50 [\[10](#page-20-13)] based medical CLIP method ConVIRT [\[28](#page-21-4)] and GLoRIA [\[12\]](#page-20-4). We also compare with recent medical CLIP baselines including MGCA  $[26]$ , MRM  $[29]$  $[29]$ , and MedCLIP  $[27]$  with Swin-Transformer [\[17\]](#page-20-14). We choose these baselines as they provide either their pretrained model or full training code. However, the ConVIRT [\[28](#page-21-4)] model does not provide a pre-trained model, so we report results directly drawn from Wang *et al*. [\[27\]](#page-21-5).

We evaluate all models under zero-shot, linear-probing, and full fine-tuning settings. We report accuracy for zero-shot classification and both accuracy and area under the receiver operating characteristic curve (AUC) for the two finetuning settings. We further evaluate the data efficiency during full fine-tuning of our model and compare the model size.

### 3.3 Implementation Details

We choose BioMedLM-3B [\[2](#page-19-3)] and DiNOv2 [\[20\]](#page-20-3) to initialize our encoders. During the pre-training, we use a batch size of 72 and learning rate of  $4 \times 10^{-5}$  for 40,000 steps. We use the cosine annealing scheduler with a 4,000-step linear warmup and AdamW [\[18](#page-20-15)] optimizer with weight decay of 0.2. We select the model with the smallest validation loss as the final model. During prompt context learning, we use a batch size of 36 and a learning rate of  $1 \times 10^{-3}$  for 4,000 steps. Prompt context length is set to  $L = 30$ . We use the same scheduler with a 1,000-step warm-up and SGD optimizer. For the PEFT strategy, we experiment with LoRA  $[11]$  $[11]$ , IA3  $[16]$  $[16]$  and Prefix-tuning  $[15]$  $[15]$ . For linear probing and full finetuning, we optimize cross-entropy loss using a batch size of 36 and learning rate

<span id="page-17-0"></span>Table 1. Main Evaluation Results. We evaluate the proposed method with multiple baselines on the CheXpert-5*×*200 [\[13](#page-20-8)] and RSNA [\[24](#page-21-7)] datasets. We evaluate zero-shot (ZS) classification, linear-probing (LP), and full-finetuning (FT) tasks. All values are percentages. We highlight the top result in bold and the second-best with an underline.

| Method             | CheXpert $5 	imes 200$ [13] |        |        |        |        | RSNA [24] |        |        |        |        |
|--------------------|-----------------------------|--------|--------|--------|--------|-----------|--------|--------|--------|--------|
|                    | ZS-Acc                      | LP-Acc | LP-AUC | FT-Acc | FT-AUC | ZS-Acc    | LP-Acc | LP-AUC | FT-Acc | FT-AUC |
| Random-ViT [20]    | -                           | 20.62  | 57.85  | 20.32  | 63.68  | -         | 65.15  | 72.22  | 72.70  | 79.89  |
| ImageNet-ViT [20]  | -                           | 38.54  | 75.44  | 56.46  | 85.71  | -         | 74.96  | 83.72  | 77.44  | 85.24  |
| CLIP-ViT [21]      | 20.16                       | 35.34  | 65.14  | 44.84  | 77.59  | 49.89     | 69.47  | 75.90  | 77.08  | 83.53  |
| ConVIRT-R50 [28]   | 41.88*                      | 47.70* | -      | -      | -      | 47.31*    | 78.46* | -      | -      | -      |
| GLoRIA-R50 [12]    | 43.28*                      | 51.65  | 83.15  | 57.56  | 87.11  | 33.06*    | 76.77  | 86.53  | 78.55  | 87.15  |
| MGCA-ViT† [26]     | 51.35                       | 26.63  | 63.94  | 56.96  | 86.33  | 69.90     | 75.35  | 85.63  | 79.79  | 88.11  |
| MRM-ViT [29]       | -                           | 58.26  | 84.48  | 56.56  | 87.41  | -         | 76.43  | 85.48  | 78.77  | 86.63  |
| MedCLIP-Swin† [27] | 59.42*                      | 54.55  | 85.75  | 57.46  | 87.85  | 74.47*    | 79.26  | 88.26  | 78.80  | 87.36  |
| Ours-Prefix        | 60.55                       | 61.56  | 87.20  | 61.16  | 87.73  | 64.07     | 78.75  | 88.30  | 79.34  | 88.52  |
| Ours-IA3           | 65.61                       | 60.16  | 86.48  | 61.06  | 86.81  | 64.08     | 78.80  | 87.74  | 79.99  | 88.59  |
| Ours-LoRA          | 66.74                       | 63.46  | 87.76  | 63.96  | 88.22  | 64.93     | 79.40  | 88.34  | 80.36  | 88.72  |

<sup>∗</sup> Result directly drawn from Wang *et al*. [\[27](#page-21-5)]

† Method pre-trained with a different dataset with 2*×* greater size

of  $5 \times 10^{-4}$  for 8,000 steps with weight decay of  $1 \times 10^{-3}$ . All models are trained with BFloat-16-mix precision with 2 NVIDIA A5000 GPUs using PyTorch.

### 3.4 Main Results

Zero-Shot Classification. As shown in Table [1,](#page-17-0) our model with LoRA [\[11\]](#page-20-6) outperforms other baselines with 7% improvement on the CheXpert-5x200 [\[13\]](#page-20-8) dataset and with the smallest number of trainable language parameters and overall small trainable model size (Fig. [1,](#page-12-0) Table S3). Note that while our model falls behind MGCA [\[26](#page-21-3)] and MedCLIP [\[27](#page-21-5)] on the RNSA [\[24](#page-21-7)] evaluation, these two baselines were pre-trained with a different, larger dataset with twice the size of CheXpert-1.0 [\[13](#page-20-8)]. Compared with the other baselines that were pre-trained with the same data, our method performs best on the out-of-domain RSNA data.

Linear Probing. Under the linear probing condition, our model with LoRA [\[11\]](#page-20-6) achieves the best performance on both datasets (Tab. [1\)](#page-17-0). We highlight the 5% gap in the CheXpert-5x200 [\[13](#page-20-8)] experiment. This indicates our model has a more robust embedding space that can distinguish input data even without taskspecific fine-tuning. We further note that with linear probing, our method now surpasses MGCA [\[26\]](#page-21-3) and MedCLIP [\[27\]](#page-21-5) on RSNA, even with less pre-training data.

Full Fine-Tuning. Our model outperforms all other baselines when the model is fully fine-tuned (Tab. [1\)](#page-17-0). Our model shows impressive improvement on the CheXpert [\[13](#page-20-8)] dataset and also beats other baselines on the RSNA [\[24](#page-21-7)] dataset.

<span id="page-18-0"></span>

#### Table 2. EMBED Evaluation. All values are percentages.

Image /page/18/Figure/2 description: The image is a table comparing the performance of different methods on BI-RADS and Density datasets. The methods compared are Random [20], ImageNet [20], CLIP [21], and Ours-LoRA. The performance metrics are ZS-Acc, LP-Acc, and FT-Acc for both datasets. For BI-RADS, Random and ImageNet have '-' for ZS-Acc, while CLIP has 20.93 and Ours-LoRA has 33.29. LP-Acc values are 17.86 for Random, 18.29 for ImageNet, 16.57 for CLIP, and 30.86 for Ours-LoRA. FT-Acc values are 17.43 for Random, 20.21 for ImageNet, 21.21 for CLIP, and 23.79 for Ours-LoRA. For Density, Random has '-' for ZS-Acc, ImageNet has '-' for ZS-Acc, CLIP has 70.45, and Ours-LoRA has 74.40. LP-Acc values are 34.65 for Random, 56.95 for ImageNet, 36.70 for CLIP, and 74.80 for Ours-LoRA. FT-Acc values are 54.45 for Random, 67.45 for ImageNet, 50.50 for CLIP, and 74.95 for Ours-LoRA. The Ours-LoRA method shows the highest performance across most metrics, with bolded values indicating the best results.

<span id="page-18-1"></span>

#### Table 3. Ablation Evaluation. All values are percentages.

| Method        | LM #Param. | Zero-shot Acc.           |                          |
|---------------|------------|--------------------------|--------------------------|
|               |            | CheXpert 5 × 200 [13]    | RSNA [24]                |
| Ours-LoRA     | 2.6M       | 66.74 ( $±0.00$ )        | 64.63 ( $±0.00$ )        |
| - Prompt FT.  | 2.6M       | 63.46 ( $-3.28$ )        | 57.15 ( $-7.48$ )        |
| - Freeze LM   | 0.0M       | 64.44 ( $-2.23$ )        | 65.51 ( $+0.88$ )        |
| + Full FT. LM | 2.7B       | <b>67.03</b> ( $+0.29$ ) | <b>67.58</b> ( $+2.95$ ) |

Image /page/18/Figure/5 description: The line graph is titled "Zero-shot Acc. with different learnable prompt lengths". The x-axis is labeled "Learnable Prompt Length" and shows values of 10, 20, 30, and 40. The y-axis is labeled "CheXpert Zero-shot Acc. (%)" and ranges from 64.5 to 67.0. The line shows the following data points: at a learnable prompt length of 10, the accuracy is 64.69%; at 20, it is 64.98%; at 30, it is 66.74%; and at 40, it is 65.57%.

<span id="page-18-2"></span>Fig. 3. Zero-shot Accuracy vs. Number of Trainable Prompt Tokens.

We suggest it is the proposed PEFT language model that provides a better quality of supervision. Meanwhile, the vastly pre-trained encoders allow the model to properly adapt to out-of-domain tasks. We further evaluate each model's data efficiency with different ratios of training data in Table S1 and Table S2. A more robust pre-trained model should be able to generalize easily to the target task even with a small amount of training data. Notably, our model also outperforms the baselines even with much less training data. We highlight that for RSNA, our accuracy drops by  $\langle 3\% \rangle$  when using 1% compared to 100% of the training data.

Mammography Evaluation. We report the performance of our model with LoRA [\[11\]](#page-20-6) and baselines on the EMBED mammography data in Tab. [2.](#page-18-0) Our model clearly surpasses the compared baselines with a considerable gap. These initial results on the mammography dataset suggest the proposed model has the potential to be applied to other medical domains.

### 3.5 Ablation Experiments

Results of ablation experiments with our method are presented in Tab. [3.](#page-18-1) Without the second stage prompt fine-tuning, the accuracy drops by  $\sim$ 3% for both datasets. Using a fully frozen language model harms performance on in-domain data but improves performance with out-of-domain data. The fully fine-tuned model improves the performance even with a much smaller batch size; however, this improvement comes with the cost of ∼4 times more GPU memory cost and only 1/20 batch size with 2 times longer training. We also evaluate the influence of using different lengths for the prompt context (Fig. [3\)](#page-18-2), and we choose  $L = 30$ as our best model in the other evaluations. Increasing the prompt length does not always improve the performance according to this experiment.

## 4 Discussion and Conclusion

We propose a novel contrastive language-image pre-training framework for medical images, called CLEFT, which incorporates a pre-trained medical LLM, parameter-efficient fine-tuning for the LLM, and prompt context learning. The proposed method successfully outperforms multiple baselines in the experiments conducted on three public datasets. Furthermore, the proposed method greatly reduces the training cost of incorporating an LLM while maintaining robust performance. We provide a new view that treats the existing CLIP task as knowledge distillation from the LLM. The proposed framework can be easily merged with other related CLIP methods like MedCLIP [\[27](#page-21-5)] to further improve the performance.

While the proposed method demonstrated enhanced performance, additional methodological improvements should be explored, *e*.*g*., different initialization methods, more robust prompt finetuning methods that can adapt to the medical image domain, and generating more diverse and detailed prompting during training. Finally, more experiments including text-image retrieval and benchmarking of different visual encoders and LLM models should be conducted to further evaluate the methods.

Acknowledgments. This work was supported by NIH grant R21EB032950.

Disclosure of Interests. The authors have no competing interests in this work and other related research.

## References

- <span id="page-19-1"></span>1. Alsentzer, E., Murphy, J., Boag, W., Weng, W.H., Jin, D., Naumann, T., McDermott, M.: Publicly available clinical BERT embeddings. In: Proceedings of the 2nd Clinical Natural Language Processing Workshop. pp. 72–78. Association for Computational Linguistics, Minneapolis, Minnesota, USA (Jun 2019). [https://doi.org/](https://doi.org/10.18653/v1/W19-1909) [10.18653/v1/W19-1909,](https://doi.org/10.18653/v1/W19-1909) <https://www.aclweb.org/anthology/W19-1909>
- <span id="page-19-3"></span>2. Bolton, E., Hall, D., Yasunaga, M., Lee, T., Manning, C., Liang, P.: Biomedlm, [https://huggingface.co/stanford-crfm/BioMedLM,](https://huggingface.co/stanford-crfm/BioMedLM) accessed: 2023-03-02
- <span id="page-19-0"></span>3. Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J.D., Dhariwal, P., Neelakantan, A., Shyam, P., Sastry, G., Askell, A., et al.: Language models are few-shot learners. Advances in neural information processing systems 33, 1877–1901 (2020)
- <span id="page-19-2"></span>4. Chen, C., Zhong, A., Wu, D., Luo, J., Li, Q.: Contrastive masked image-text modeling for medical visual representation learning. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 493–503. Springer (2023)

- <span id="page-20-1"></span>5. Chen, T., Kornblith, S., Norouzi, M., Hinton, G.: A simple framework for contrastive learning of visual representations. In: International conference on machine learning. pp. 1597–1607. PMLR (2020)
- <span id="page-20-2"></span>6. Chen, X., Fan, H., Girshick, R., He, K.: Improved baselines with momentum contrastive learning. arXiv preprint [arXiv:2003.04297](http://arxiv.org/abs/2003.04297) (2020)
- <span id="page-20-12"></span>7. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A largescale hierarchical image database. In: 2009 IEEE conference on computer vision and pattern recognition. pp. 248–255. Ieee (2009)
- <span id="page-20-11"></span>8. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth  $16x16$  words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-20-0"></span>9. He, K., Fan, H., Wu, Y., Xie, S., Girshick, R.: Momentum contrast for unsupervised visual representation learning. arXiv preprint [arXiv:1911.05722](http://arxiv.org/abs/1911.05722) (2019)
- <span id="page-20-13"></span>10. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-20-6"></span>11. Hu, E.J., Shen, Y., Wallis, P., Allen-Zhu, Z., Li, Y., Wang, S., Wang, L., Chen, W.: Lora: Low-rank adaptation of large language models. arXiv preprint [arXiv:2106.09685](http://arxiv.org/abs/2106.09685) (2021)
- <span id="page-20-4"></span>12. Huang, S.C., Shen, L., Lungren, M.P., Yeung, S.: Gloria: A multimodal global-local representation learning framework for label-efficient medical image recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 3942–3951 (2021)
- <span id="page-20-8"></span>13. Irvin, J., Rajpurkar, P., Ko, M., Yu, Y., Ciurea-Ilcus, S., Chute, C., Marklund, H., Haghgoo, B., Ball, R., Shpanskaya, K., et al.: Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison. In: Proceedings of the AAAI conference on artificial intelligence. vol. 33-01, pp. 590–597 (2019)
- <span id="page-20-9"></span>14. Jeong, J.J., Vey, B.L., Bhimireddy, A., Kim, T., Santos, T., Correa, R., Dutt, R., Mosunjac, M., Oprea-Ilies, G., Smith, G., et al.: The emory breast imaging dataset (embed): A racially diverse, granular dataset of 3.4 million screening and diagnostic mammographic images. Radiology: Artificial Intelligence 5(1), e220047 (2023)
- <span id="page-20-7"></span>15. Li, X.L., Liang, P.: Prefix-tuning: Optimizing continuous prompts for generation. arXiv preprint [arXiv:2101.00190](http://arxiv.org/abs/2101.00190) (2021)
- <span id="page-20-10"></span>16. Liu, H., Tam, D., Muqeeth, M., Mohta, J., Huang, T., Bansal, M., Raffel, C.: Fewshot parameter-efficient fine-tuning is better and cheaper than in-context learning. arXiv preprint [arXiv:2205.05638](http://arxiv.org/abs/2205.05638) (2022)
- <span id="page-20-14"></span>17. Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., Zhang, Z., Lin, S., Guo, B.: Swin transformer: Hierarchical vision transformer using shifted windows. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 10012–10022 (2021)
- <span id="page-20-15"></span>18. Loshchilov, I., Hutter, F.: Decoupled weight decay regularization. arXiv preprint [arXiv:1711.05101](http://arxiv.org/abs/1711.05101) (2017)
- <span id="page-20-5"></span>19. Luo, Y., Yang, Z., Meng, F., Li, Y., Zhou, J., Zhang, Y.: An empirical study of catastrophic forgetting in large language models during continual fine-tuning. arXiv preprint [arXiv:2308.08747](http://arxiv.org/abs/2308.08747) (2023)
- <span id="page-20-3"></span>20. Oquab, M., Darcet, T., Moutakanni, T., Vo, H.V., Szafraniec, M., Khalidov, V., Fernandez, P., Haziza, D., Massa, F., El-Nouby, A., Howes, R., Huang, P.Y., Xu, H., Sharma, V., Li, S.W., Galuba, W., Rabbat, M., Assran, M., Ballas, N., Synnaeve, G., Misra, I., Jegou, H., Mairal, J., Labatut, P., Joulin, A., Bojanowski, P.: Dinov2: Learning robust visual features without supervision (2023)

- <span id="page-21-0"></span>21. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-21-1"></span>22. Radford, A., Wu, J., Child, R., Luan, D., Amodei, D., Sutskever, I.: Language models are unsupervised multitask learners. OpenAI blog (2019)
- <span id="page-21-8"></span>23. Sickles, E.A., D'Orsi, C.J., Bassett, L.W., et al.: ACR BI-RADS mammography. In: ACR BI-RADS Atlas, Breast Imaging Reporting and Data System. American College of Radiology, Reston, VA, 5th edn. (2013)
- <span id="page-21-7"></span>24. Stein, A., et.al., T.X.: Rsna pneumonia detection challenge (2018), [https://kaggle.](https://kaggle.com/competitions/rsna-pneumonia-detection-challenge) [com/competitions/rsna-pneumonia-detection-challenge](https://kaggle.com/competitions/rsna-pneumonia-detection-challenge)
- <span id="page-21-2"></span>25. Sun, Q., Fang, Y., Wu, L., Wang, X., Cao, Y.: Eva-clip: Improved training techniques for clip at scale. arXiv preprint [arXiv:2303.15389](http://arxiv.org/abs/2303.15389) (2023)
- <span id="page-21-3"></span>26. Wang, F., Zhou, Y., Wang, S., Vardhanabhuti, V., Yu, L.: Multi-granularity crossmodal alignment for generalized medical visual representation learning. Advances in Neural Information Processing Systems 35, 33536–33549 (2022)
- <span id="page-21-5"></span>27. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. arXiv preprint [arXiv:2210.10163](http://arxiv.org/abs/2210.10163) (2022)
- <span id="page-21-4"></span>28. Zhang, Y., Jiang, H., Miura, Y., Manning, C.D., Langlotz, C.P.: Contrastive learning of medical visual representations from paired images and text. In: Machine Learning for Healthcare Conference. pp. 2–25. PMLR (2022)
- <span id="page-21-9"></span>29. Zhou, H.Y., Lian, C., Wang, L., Yu, Y.: Advancing radiograph representation learning with masked record modeling. arXiv preprint [arXiv:2301.13155](http://arxiv.org/abs/2301.13155) (2023)
- <span id="page-21-6"></span>30. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Learning to prompt for vision-language models. International Journal of Computer Vision 130(9), 2337–2348 (2022)

Image /page/22/Picture/0 description: A square button with rounded corners contains a circular icon and text. The icon is a circle with a ribbon or bookmark shape inside it. The text below the icon reads "Check for updates".

# CT2Rep: Automated Radiology Report Generation for 3D Medical Imaging

Ibrahim Ethem Hamamci<sup>1( $\boxtimes$ )</sup>, Sezgin Er<sup>2</sup>, and Bjoern Menze<sup>1</sup>

<sup>1</sup> University of Zurich, Zurich, Switzerland <EMAIL> <sup>2</sup> Istanbul Medipol University, Istanbul, Turkey

Abstract. Medical imaging plays a crucial role in diagnosis, with radiology reports serving as vital documentation. Automating report generation has emerged as a critical need to alleviate the workload of radiologists. While machine learning has facilitated report generation for 2D medical imaging, extending this to 3D has been unexplored due to computational complexity and data scarcity. We introduce the first method to generate radiology reports for 3D medical imaging, specifically targeting chest CT volumes. Given the absence of comparable methods, we establish a baseline using an advanced 3D vision encoder in medical imaging to demonstrate our method's effectiveness, which leverages a novel autoregressive causal transformer. Furthermore, recognizing the benefits of leveraging information from previous visits, we augment CT2Rep with a cross-attention-based multi-modal fusion module and hierarchical memory, enabling the incorporation of longitudinal multimodal data. Access our code at [https://github.com/ibrahimethemhamamci/CT2Rep.](https://github.com/ibrahimethemhamamci/CT2Rep)

Keywords: 3D Medical Imaging · Chest CT Volume · Radiology  $\begin{minipage}{.4\linewidth} \textbf{Report} \cdot \textbf{CT-RATE D} \textbf{ataset} \cdot \textbf{Report Generation} \cdot \textbf{Longitudinal} \cdot \textbf{Transforms} \end{minipage}$ 

## 1 Introduction

The integration of machine learning into radiology, driven by numerous public datasets [\[12](#page-31-0)[,14](#page-31-1),[28\]](#page-32-0), has significantly enhanced disease classification and segmentation [\[6,](#page-30-0)[10](#page-31-2)[,25,](#page-32-1)[30\]](#page-32-2). Furthermore, recent advancements have enabled the development of many methods for generating radiology reports for 2D medical imaging [\[4](#page-30-1),[15,](#page-31-3)[20,](#page-31-4)[26](#page-32-3)[,27](#page-32-4)] utilizing public datasets [\[16,](#page-31-5)[23\]](#page-32-5). However, this progress in report generation has not yet extended to 3D medical imaging, due to computational complexities [\[8\]](#page-31-6) and the lack of datasets paired with radiology reports [\[3\]](#page-30-2).

3D medical imaging, such as computed tomography (CT) and magnetic resonance imaging, provides a more detailed perspective on the patient's condition compared to 2D imaging [\[22](#page-31-7)]. Consequently, manual report generation, essential for conveying diagnostic findings, becomes more time-consuming and errorprone, highlighting the need for automation. One of the challenges in developing such a framework lies in the scarcity of 3D medical imaging datasets paired with reports [\[19\]](#page-31-8). Moreover, the nature of 3D images involves volumetric data, which necessitates more sophisticated algorithms for interpreting the additional dimension. This complexity presents unique obstacles to generating descriptive and clinically relevant reports that effectively capture the details of 3D images.

Recognizing this gap, our work introduces CT2Rep, the first approach to automated radiology report generation for 3D medical imaging, specifically targeting chest CT volumes. CT2Rep leverages a novel 3D auto-regressive causal vision feature extractor, optimized for processing volumetric data. We also incorporate relational memory to utilize information from previous report generations, employing memory-driven conditional layer normalization to integrate this data into our framework. To train our framework, we utilize the CT-RATE dataset [\[9](#page-31-9)], which consists of 25,692 non-contrast chest CT volumes, expanded to 50,188 through various reconstructions, from 21,304 unique patients, along with corresponding radiology reports. CT2Rep's uniqueness, the first of its kind in 3D medical imaging, means that no directly comparable methods exist. Nonetheless, to demonstrate the effectiveness of our framework, we reasonably designed a baseline using a state-of-the-art vision encoder used for 3D chest CT volume interpretation, CT-Net [\[6\]](#page-30-0), for report generation. CT2Rep outperforms this welldesigned baseline method, showcasing the efficacy of our novel approach.

Radiologists typically assess a 3D chest CT volume alongside previous volumes and reports for the same patient, as multiple visits are common in clinical practice. Longitudinal volumes and their reports contain valuable information, and leveraging this multimodal data can potentially enhance report generation. Hence, we extended CT2Rep by incorporating a cross-attention-based multimodal fusion module coupled with a hierarchical memory-driven decoder. This extension not only addresses computational challenges associated with 3D image analysis but also facilitates the inclusion of longitudinal multimodal patient data, enriching the context and accuracy of generated reports. We evaluated this extended version, named CT2RepLong, through a comprehensive ablation study to underscore the importance of historical imaging and reports in informing current diagnostic interpretations. Our contributions can be summarized as:

- We propose CT2Rep, the first radiology report generation framework for 3D medical imaging, employing a novel auto-regressive causal transformer.
- As CT2Rep is the first of its kind and no comparable methods exist, we have designed a baseline employing the cutting-edge 3D vision encoder used in chest CT classification to benchmark our method and prove its effectiveness.
- We augment CT2Rep with a cross-attention-based multi-modal fusion module and a hierarchical memory-driven decoder to leverage commonly available longitudinal data, backed by a comprehensive ablation study showcasing the efficacy of incorporating longitudinal data for report generation.
- We make our trained models and source codes publicly available to facilitate out-of-the-box report generation for 3D chest CT volumes.

## 2 Methods

Although 3D medical imaging, such as 3D chest CT volumes, offers more comprehensive information than its 2D counterparts like chest X-rays, there are currently no solutions for generating radiology reports for 3D imaging due to data scarcity and computational complexity. To address this gap, we developed a 3D sequence-to-sequence generation model, detailed in Sect. [2.1,](#page-24-0) utilizing the data outlined in Sect. [2.3.](#page-27-0) Additionally, we enhanced our method to incorporate longitudinal multimodal data from previous visits, as described in Sect. [2.2.](#page-26-0)

<span id="page-24-0"></span>

### 2.1 The Proposed Method

The model ( $\Phi_{\text{CT2Ren}}$ ) accepts input 3D volumes  $x \in \mathbb{R}^{(240) \times 480 \times 480}$  as a sequence of CT patches  $x = \{x_1, x_2, x_3, ..., x_N\}, x_n \in \mathbb{R}^{(12)\times 24\times 24}$  to predict a target sequence  $r_{out} = \{r_1^{\text{out}}, r_2^{\text{out}}, ..., r_T^{\text{out}}\}, r_t^{\text{out}} \in \mathbb{V}$ . Here, N represents CT feature count,  $T$  the token count, and  $V$  the possible token vocabulary. CT2Rep, depicted in Fig. [1,](#page-24-1) consists of three key components, each elaborated below.

Image /page/24/Figure/5 description: This is a diagram illustrating a deep learning model for processing medical imaging data. The model takes an input CT volume (x) with dimensions 480 px by 480 px and 240 px depth, represented as X229:240 and X1:12 slices. This volume is processed by a 3D Vision Feature Extractor, which includes Spatial Transformers and Causal Transformers, resulting in CT Tokens (z20) and CT Tokens (z1). These tokens are then embedded as Embedded CT Tokens (z). Separately, a Radiology Report (r) containing 'Findings' is processed by a Transformer Decoder. The decoder consists of a Softmax layer, a Linear Layer, multiple Memory-driven Conditional LN (MCLN) layers, a Feed Forward layer, Multi-Head Attention, and Masked Attention. The output of the decoder generates Hidden States (hx). Both the Embedded CT Tokens (z) and Hidden States (hx) are fed into a Transformer Encoder (Φtransformer\_enc). The output of the encoder is then processed by a Relational Memory (RM) module, which interacts with a Volume Memory. Finally, the model produces an Output Embedding.

<span id="page-24-1"></span>Fig. 1. CT2Rep features a novel auto-regressive causal transformer for 3D vision feature extraction, complemented by RM and MCLN-enhanced transformer-based encoder and decoder network for clinically accurate report generation.

3D vision feature extractor. A key component and main contribution of our framework, this network  $(\varPhi_{\text{enc}}^{\text{visual}})$  facilitates the extraction of embedded CT tokens from 3D chest CT volumes by segmenting the data into distinct patches and transforming them into a lower-dimensional latent space, inspired by [\[1\]](#page-30-3). These tokens capture essential information, facilitating subsequent analysis.

The network takes a 3D CT volume  $(x)$  and produces embedded CT tokens  $z_x \in \mathbb{R}^{20 \times 20 \times 20 \times 512}$ , by initially extracting  $(12) \times 24 \times 24$  non-overlapping patches from x. Each patch is then mapped to a  $D$ -dimensional space, with  $D$  set to 512.

The patches are then reshaped and transformed linearly to  $B \times T \times \frac{H}{p_1} \times \frac{W}{p_2} \times D$ , following a previous work  $[11]$  $[11]$ . Here,  $p_t$  denotes the temporal patch size, T represents the number of temporal patches,  $B$  is the batch size,  $H$  and  $W$  are the height and width of the slices, respectively, and  $p_1$  and  $p_2$  represent the spatial patch sizes. After patch embedding, the resulting tensor size is  $B \times$  $(T) \times \frac{H}{p_1} \times \frac{W}{p_2} \times D$ . This tensor is then processed by two transformer networks consecutively. First, the spatial transformer operates on a reshaped tensor of size  $(B\cdot(T))\times(\frac{H}{p_1}\cdot\frac{W}{p_2})\times D$ , yielding a tensor of the same dimensions. Subsequently, the causal transformer processes this output reshaped to  $(\frac{H}{p_1} \cdot \frac{W}{p_2}) \times (B \cdot (T)) \times D$ , and produces an output maintaining these dimensions. This method ensures that both spatial and latent dimensions are preserved after each layer, thereby retaining 3D volumetric information throughout the network's processing stages. The overall 3D chest CT volume feature extraction process, formally defined as  $z_x = \Phi_{\text{enc}}^{\text{visual}}(x)$ , ensures 3D volumetric information is preserved, facilitating the effective construction of sequence-to-sequence models for report generation.

**Transformer Encoder.** We employ a conventional transformer  $(\varPhi_{\text{enc}}^{\text{transfer}})$  to encode CT features extracted by  $\Phi_{\rm enc}^{\rm visual}$ . This network processes these features to produce encoded hidden states via an attention mechanism, crucial for capturing feature interdependencies. The encoded hidden states are represented as:

$$
h_x = \{h_1, h_2, \dots, h_N\} = \Phi_{\text{enc}}^{\text{transformer}}(z_x) = \Phi_{\text{enc}}^{\text{transformer}}(z_1, z_2, \dots, z_N),
$$

where each  $h_n \in \mathbb{R}^{512}$  represents the encoded state of a patch, with N being the total patch count. The attention mechanism in the transformer is defined as Attention $(Q, K, V)$  = softmax  $\left(\frac{QK^T}{\sqrt{d_k}}\right)V$ , where  $Q, K$ , and V stand for the query, key, and value matrices, respectively, and  $d_k$  is the key's dimensionality. Transformer Decoder. We adapt a traditional transformer network as a decoder ( $\Phi_{\rm dec}^{\rm transformer}$ ), with two notable enhancements. First, we integrate relational memory (RM) [\[4\]](#page-30-1), entailing the utilization of a matrix to encapsulate and propagate pattern information across generation steps. Each row within this matrix stores specific pattern details, which are iteratively refined through updates incorporating outputs from preceding steps. The updating mechanism involves employing the matrix from the previous step as a query and concatenating it with the prior output to serve as the key and value for the transformer's multi-head attention module. Mathematically, this process is achieved through multi-head attention, where  $Q = M_{t-1} \cdot W_q$ ,  $K = [M_{t-1}; y_{t-1}] \cdot W_k$ , and  $V = [M_{t-1}; y_{t-1}] \cdot W_v$ . Here,  $y_{t-1}$  denotes the embedding of the previous step's output, while  $W_q$ ,  $W_k$ , and  $W_v$  represent the trainable weights for query, key, and value transformations, respectively. Thus, the model effectively learns conserved report patterns, such as *"Trachea, both main bronchi are open"*, within similar CT volumes. Second, we employ a memory-driven conditional layer nor-malization (MCLN) [\[17](#page-31-11)], integrating RM directly into the decoder's scaling  $(\gamma)$ and shifting  $(\beta)$  parameters. This makes the model more contextually aware and adept at generating accurate text outputs. The decoding process is defined as:

$$
r_T^{\text{out}} = \Phi_{\text{dec}}^{\text{transformer}}(h_1, h_2, \dots, h_N, MCLN(RM(r_1^{\text{out}}, r_2^{\text{out}}, \dots, r_{T-2}^{\text{out}}, r_{T-1}^{\text{out}}))).
$$

**Inference.** After training, CT2Rep ( $\Phi$ <sub>CT2Rep</sub>) is able to generate a radiology report  $(r_{\text{out}})$  for a given 3D chest CT volume  $(x)$ , formally defined as follows:

Image /page/26/Figure/2 description: This is a diagram illustrating a deep learning model for analyzing medical imaging data, specifically CT scans and radiology reports. The model processes an input CT volume (480 px by 480 px by 240 px) through a 3D Vision Feature Extractor, which generates embedded CT tokens. These tokens are then processed by a Transformer Encoder, resulting in hidden states. A Transformer Decoder takes a radiology report (findings) and processes it through layers including Softmax, a Linear Layer, Memory-driven Conditional LN (MCLN) blocks, Multi-Head Attention, and Masked Attention. The decoder interacts with a Volume Memory and a Longitudinal Memory, both represented as grids of colored circles, and produces an output embedding. The model also incorporates longitudinal data, processing a historical CT volume (480 px by 480 px by 240 px) and a historical radiology report through a similar pipeline involving a 3D Vision Feature Extractor and a Transformer Encoder. The hidden states from both the current and historical data are then combined through Cross Attention, and further processed by another Transformer Encoder. The overall architecture suggests a system for understanding and potentially predicting medical conditions based on sequential imaging and textual data.

$$
r_{\rm out} = \varPhi_{\rm CT2Rep}(x) = \varPhi_{\rm dec}^{\rm transformer}(\varPhi_{\rm enc}^{\rm transformer}(\varPhi_{\rm enc}^{\rm visual}(x))).
$$

<span id="page-26-1"></span>Fig. 2. CT2RepLong enhances CT2Rep with a cross-attention multi-modal fusion module and longitudinal memory for effective historical data integration.

<span id="page-26-0"></span>

### 2.2 Longitudinal Data Utilization

To utilize multimodal data from previous visits, we augmented CT2Rep with a cross-attention-based fusion module [\[31](#page-32-6)] that allows to predict an outcome sequence  $r_{out}^{new} = \{r_1, r_2, ..., r_T\}, r_t \in V$ , for a given new 3D chest CT volume  $(x^{new})$  by integrating representations from the previous CT volume  $(x^{old})$  and its corresponding previous report  $(r_{in}^{old})$ . The fusion process is facilitated by computing cross-attention between previous volume and report representations by  $R^* = \text{softmax}\left(\frac{q(H_{RP})k(H_{IP})^\top}{\sqrt{d_k}}\right)$  $\int$  and  $I^* = \text{softmax}\left(\frac{q(H_{IP})k(H_{RP})}{\sqrt{d_k}}\right)$ ), where  $H_{IF}$  $\mu$  = solumes  $\left(\frac{\sqrt{d_k}}{\sqrt{d_k}}\right)$  and  $\mu$  = solumes  $\left(\frac{\sqrt{d_k}}{\sqrt{d_k}}\right)$ , where  $\mu_{IP}$  and  $H_{RP}$  are the attended features for longitudinal volumes and reports, respectively. These features are concatenated to create a comprehensive multimodal longitudinal representation H*L*. This integrated approach significantly enhances the performance of the longitudinal framework,  $\Phi_{\text{CT2RepLong}}$ , by leveraging both spatial and semantic information from previous visits, as detailed in Fig. [2.](#page-26-1)

Multimodal Transformer Decoder. The decoder of  $\Phi$ <sub>CT2RepLong</sub> closely follows that of  $\Phi_{\text{CT2Rep}}$ . However,  $\Phi_{\text{CT2RepLong}}$  employs two more cross-attention mechanisms, together defined as  $\Phi_{\text{long}}^{\text{attn}}$ , to analyze the relationships between previous reports  $(r^{\text{old}})$  and volumes  $(x^{\text{old}})$ , and vice versa. The outputs are concatenated, and then RM is applied to the new report as per the process described in Sect. [2.1.](#page-24-0) Subsequently, another cross-attention,  $(\varPhi_{\text{mem}}^{\text{attn}})$ , is used between the RM and the cross-attention outputs from previous volumes and reports. The resulting cross-attention outputs are then utilized in MCLN, formalized as:

$$
r_T^{\text{out}} = \Phi_{\text{dec}}^{\text{transformer}}(h_1, ..., h_N, MCLN(\Phi_{\text{mem}}^{\text{attn}}(RM(r_1^{\text{out}}, ..., r_{T-1}^{\text{out}}), \Phi_{\text{long}}^{\text{attn}}(r^{\text{old}}, x^{\text{old}}))).
$$

**Inference.** After training,  $\Phi_{\text{CT2RepLong}}$  can generate a report  $(r_{\text{out}}^{\text{new}})$  for a given new volume  $(x^{new})$ , alongside a previous volume and its corresponding report:

$$
r_{\text{out}}^{\text{new}} = \Phi_{\text{CT2RepLong}}(x^{\text{new}}, x^{\text{old}}, r^{\text{old}})
$$
  
=  $\Phi_{\text{dec}}^{\text{transformer}}(\Phi_{\text{enc}}^{\text{transformer}}(\Phi_{\text{enc}}^{\text{visual}}(x^{\text{new}})), \Phi_{\text{long}}^{\text{attn}}(r^{\text{old}}, x^{\text{old}})).$ 

<span id="page-27-0"></span>

### 2.3 Dataset Preparation

We utilize 3D chest CT volumes along with corresponding radiology reports from the publicly available CT-RATE dataset [\[9](#page-31-9)]. For the development of CT2Rep, we employ all volumes and reports from the initial release of CT-RATE. Our dataset comprises 25,701 non-contrast 3D chest CT volumes from 21,314 unique patients, which expands to 49,138 volumes after applying multiple reconstructions tailored to different window settings [\[29\]](#page-32-7). Each volume features a resolution of  $512 \times 512$  pixels in the axial plane, with slice counts ranging from 100 to 600. The radiology reports associated with each volume are segmented into four sections: clinical information, technique, findings, and impression; however, only the findings section is utilized for report generation training. The same radiology report is used for each reconstructed volume of a single CT volume. The dataset is divided into a training set of 20,000 patients and a validation set of 1,314 patients, ensuring no overlap. CT volumes were converted to Hounsfield Units (HU) using slope and intercept values from the metadata and clipped to [−<sup>1000</sup> HU, +200 HU] to represent the practical diagnostic limits of the HU scale [\[5\]](#page-30-4). Each volume was subsequently resized to achieve uniform spacing of 0.75 mm on the x and y axes and 1.5 mm on the z-axis. The volumes were either center-cropped or padded to achieve a consistent resolution of  $(240) \times 480 \times 480$ . Creating the Longitudinal Dataset. We targeted patients with more than two visits, yielding 6,766 and 429 3D chest CT volumes from 2,638 and 169 unique patients for the training and validation sets, respectively. After applying various reconstructions, these volumes increased to 13,354 for training and 849 for validation. We chronologically ordered the volumes for each patient using the *StudyTime* metadata attribute and paired every two possible longitudinal volumes for a patient, resulting in 28,441 training and 1,689 validation pairs.

## 3 Experiments and Results

To evaluate model efficacy in generating radiology reports, we employed natural language generation (NLG) and clinical efficacy (CE) metrics. NLG metrics include BLEU (BL) [\[24](#page-32-8)], METEOR  $(M)$  [\[18](#page-31-12)], and ROUGE-L  $(R_L)$  [\[21\]](#page-31-13), assessing word overlap, synonym use and word order, and sequence matching, respectively. For CE metrics, we fine-tuned the CXR-Bert model [\[2\]](#page-30-5) for multi-label classification of reports on 18 abnormalities, as detailed in the supplementary material. We then predicted the abnormality labels of both ground-truth data and generated reports and computed classification scores, including precision (P), recall (R), and F1 score, to measure the clinical accuracy of the generated reports.

| Method                                         | NLG Metrics |       |       |       | CE Metrics |       |       |       |       |
|------------------------------------------------|-------------|-------|-------|-------|------------|-------|-------|-------|-------|
|                                                | BL-1        | BL-2  | BL-3  | BL-4  | M          | RL    | P     | R     | F1    |
| Base w/ CT-Net                                 | 0.443       | 0.399 | 0.375 | 0.354 | 0.286      | 0.442 | 0.513 | 0.531 | 0.456 |
| CT2Rep (Ours)                                  | 0.460       | 0.415 | 0.390 | 0.369 | 0.295      | 0.459 | 0.749 | 0.548 | 0.534 |
| <i>methods below utilize longitudinal data</i> |             |       |       |       |            |       |       |       |       |
| Baseline                                       | 0.372       | 0.317 | 0.282 | 0.251 | 0.238      | 0.353 | 0.666 | 0.465 | 0.525 |
| + report                                       | 0.330       | 0.284 | 0.260 | 0.241 | 0.213      | 0.313 | 0.623 | 0.410 | 0.524 |
| + volume                                       | 0.305       | 0.261 | 0.238 | 0.220 | 0.204      | 0.291 | 0.662 | 0.434 | 0.530 |
| + report + volume                              | 0.365       | 0.319 | 0.292 | 0.271 | 0.239      | 0.351 | 0.658 | 0.410 | 0.533 |
| CT2RepLong (Ours)                              | 0.374       | 0.327 | 0.304 | 0.401 | 0.285      | 0.263 | 0.727 | 0.511 | 0.536 |

<span id="page-28-0"></span>Table 1. Quantitative evaluation showcases the effectiveness of our CT2Rep model in comparison to a well-designed baseline and highlights how our enhanced method, CT2RepLong, leverages longitudinal data to improve performance.

<span id="page-28-1"></span>

### 3.1 Comparison with the Baseline Method

Given the absence of directly comparable methods, further highlighting our method's novelty, we established a benchmark for radiology report generation by implementing a state-of-the-art vision encoder, CT-Net [\[7\]](#page-30-6), used in 3D medical imaging. CT-Net is the first and only model developed for classifying 3D chest CT volumes. Its architecture comprises a ResNet-18 feature extractor [\[13\]](#page-31-14), augmented by 3D convolutional blocks designed to streamline ResNet features, followed by final classification layers. In our approach, we harness the feature extraction capabilities of CT-Net, using these features as inputs for our 3D volume transformer, establishing it as the baseline for our study. Table [1](#page-28-0) demonstrates that our CT2Rep significantly outperforms this baseline, thanks to our novel auto-regressive causal transformer used as the 3D vision feature extractor. Case Study. We assessed our model's performance through a qualitative analysis on a randomly chosen case from our test set, comparing generated reports to the ground truth. Figure [3](#page-29-0) illustrates that CT2Rep accurately generates reports with content flow and medical terminology that closely resemble those written by radiologists, markedly surpassing the baseline established with CT-Net.

<span id="page-28-2"></span>

### 3.2 Ablation Study on Longitudinal Data Utilization

We evaluated CT2RepLong's performance and the impact of incorporating prior data through an ablation study. Initially, we established a baseline by training CT2Rep solely on the longitudinal dataset without using any prior data. We then augmented this baseline with three strategies: utilizing embeddings from previous reports, previous volume embeddings, and their combination via simple fusion (excluding our longitudinal cross-attention mechanism). Table [1](#page-28-0) demonstrates the advantages of prior multimodal data and our unique crossattention mechanism. The exception with  $R<sub>L</sub>$  can be attributed to its emphasis

Image /page/29/Picture/1 description: The image displays a comparison of radiology reports for a chest CT scan. On the left are two CT scan images, labeled "3D Chest CT Volume" and "Previous CT Volume." To the right of the CT scans are three text blocks representing different radiology reports: "Current (Ground-truth)," "Previous," and "Generated." The "Current (Ground-truth)" report notes "No occlusive pathology," increased heart size, "diffuse calcified atherosclerotic plaques," and "emphysematous changes." The "Previous" report mentions open bronchi, "calcific atheroma plaques," increased heart size, and "emphysematous changes." The "Generated" report, labeled "Baseline," describes open bronchi, linear atelectasis, and no mass or infiltration. The "CT2RepLong" generated report also notes open bronchi, "calcific atheroma plaques," minimal "cardiomegaly," and "emphysematous changes."

Fig. 3. Comparison of ground-truth with reports generated by a CT-Net-based baseline and CT2Rep, highlighting CT2Rep's medical precision with color codes.

<span id="page-29-0"></span>on sequence length over the enriched content and diversity from longitudinal data integration. Besides, despite the limited size of longitudinal data-only 13% of patients (see Sect. [2.3\)](#page-27-0)-CT2RepLong's performance was comparable with the original CT2Rep, illustrating its effectiveness, even with a constrained dataset. Case Study. A qualitative analysis of a random test case (Fig. [4\)](#page-29-1) reveals that CT2RepLong significantly benefits from integrating longitudinal data. Key terms like "cardiomegaly" and "calcified atherosclerotic plaques" appeared in both current and previous reports, enriching the accuracy of the generated reports. Notably, terms missed by the baseline, such as "cardiomegaly" were included by CT2RepLong, aligning with the ground truth and demonstrating the enhanced reliability of report generation with our extension for utilizing longitudinal data.

Image /page/29/Figure/4 description: The image displays a comparison of radiology reports for a chest CT scan. On the left are two CT scan images, labeled "3D Chest CT Volume" and "Previous CT Volume". To the right of the scans are three text sections: "Current (Ground-truth)", "Previous", and "Generated". The "Current (Ground-truth)" report states "No occlusive pathology was detected in the trachea and left main bronchus lumen. Heart size has increased, cardiomegaly. Diffuse calcified atherosclerotic plaques were observed on the thoracic aorta and coronary artery walls. Emphysematous changes were observed in both lungs." The "Previous" report notes "Trachea, both main bronchi are open. There are calcific atheroma plaques in the thoracic aorta and at the level of the coronary arteries. The heart size has increased. Thoracic aorta diameter is normal. When both lung parenchyma windows are evaluated, emphysematous changes were observed in both lungs." The "Generated" section includes a "Baseline" report stating "Trachea and both main bronchi are open. There are linear atelectasis in the lower lobes of both lungs. There was no evidence of mass or pneumonic infiltration in both lungs. No pleural or pericardial effusion was detected." Below this is a "CT2RepLong" report which reads "Trachea, both main bronchi are open. Calcific atheroma plaques are seen in the aorta and coronary arteries. Minimal cardiomegaly are observed. Diffuse centrilobular emphysematous changes are observed in both lungs." Various phrases within the reports are highlighted with different colors: yellow for open airways, red for cardiomegaly, blue for calcific atherosclerotic plaques, and purple for emphysematous changes.

<span id="page-29-1"></span>Fig. 4. CT2RepLong surpasses the baseline, leveraging longitudinal data for enhanced medical detail accuracy, with related terms color-coded for clarity.

### 3.3 Implementation Details

CT2Rep and the baseline method (see Sect. [3.1\)](#page-28-1) were trained on 49,138 3D CT volumes and their corresponding reports (Sect. [2.3\)](#page-27-0). We used the Adam optimizer with  $\beta_1$  and  $\beta_2$  hyperparameters set to 0.9 and 0.99, respectively. The learning rate was established at 0.00005 for the visual extractor and 0.0001 for the other parameters. A StepLR scheduler with a gamma of 0.1, a batch size of 1, and a

maximum token count of 300 for the scheduler were employed. CT2RepLong and the ablation methods (Sect. [3.2\)](#page-28-2) were trained on 28,441 pairs (Sect. [2.3\)](#page-27-0), utilizing the same hyperparameters as CT2Rep. The training duration for all models was one week on a single NVIDIA A100 GPU, achieving 20 epochs. Inference takes approximately 35 s for CT2Rep and 50 s for CT2RepLong.

# 4 Discussion and Conclusion

In conclusion, we introduce CT2Rep, the first framework for automating 3D medical imaging report generation, with a focus on chest CT volumes. Leveraging an innovative auto-regressive causal transformer architecture and integrating relational memory, CT2Rep enhances accuracy in report generation. As the first of its kind, we establish a benchmark using the state-of-the-art vision encoder in 3D chest CT volume interpretation to showcase CT2Rep's effectiveness. Additionally, we extend its capabilities with longitudinal data integration, resulting in CT2RepLong, further enhancing context and accuracy. We make our trained models and code fully open-source to lay a solid foundation for further research.

Acknowledgments. We extend our gratitude to the Helmut Horten Foundation for their invaluable support of our research. Additionally, we would like to express our sincere appreciation to Istanbul Medipol University for providing the CT-RATE dataset.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-30-3"></span>1. Arnab, A., Dehghani, M., Heigold, G., Sun, C., Lučić, M., Schmid, C.: Vivit: A video vision transformer. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 6836–6846 (2021)
- <span id="page-30-5"></span>2. Boecking, B., Usuyama, N., Bannur, S., Castro, D.C., Schwaighofer, A., Hyland, S., Wetscherek, M., Naumann, T., Nori, A., Alvarez-Valle, J., et al.: Making the most of text semantics to improve biomedical vision–language processing. In: European conference on computer vision. pp. 1–21. Springer (2022)
- <span id="page-30-2"></span>3. Chen, X., Wang, X., Zhang, K., Fung, K.M., Thai, T.C., Moore, K., Mannel, R.S., Liu, H., Zheng, B., Qiu, Y.: Recent advances and clinical applications of deep learning in medical image analysis. Medical Image Analysis 79, 102444 (2022)
- <span id="page-30-1"></span>4. Chen, Z., Song, Y., Chang, T.H., Wan, X.: Generating radiology reports via memory-driven transformer. arXiv preprint [arXiv:2010.16056](http://arxiv.org/abs/2010.16056) (2020)
- <span id="page-30-4"></span>5. DenOtter, T.D., Schubert, J.: Hounsfield unit (2019)
- <span id="page-30-0"></span>6. Draelos, R.L., Dov, D., Mazurowski, M.A., Lo, J.Y., Henao, R., Rubin, G.D., Carin, L.: Machine-learning-based multiple abnormality prediction with large-scale chest computed tomography volumes. Medical image analysis 67, 101857 (2021)
- <span id="page-30-6"></span>7. Draelos, R.L., Dov, D., Mazurowski, M.A., Lo, J.Y., Henao, R., Rubin, G.D., Carin, L.: Machine-learning-based multiple abnormality prediction with large-scale chest computed tomography volumes. Medical image analysis 67, 101857 (2021)

- <span id="page-31-6"></span>8. Gao, J., Shen, T., Wang, Z., Chen, W., Yin, K., Li, D., Litany, O., Gojcic, Z., Fidler, S.: Get3d: A generative model of high quality 3d textured shapes learned from images. Advances In Neural Information Processing Systems 35, 31841–31854 (2022)
- <span id="page-31-9"></span>9. Hamamci, I.E., Er, S., Almas, F., Simsek, A.G., Esirgun, S.N., Dogan, I., Dasdelen, M.F., Wittmann, B., Simsar, E., Simsar, M., et al.: A foundation model utilizing chest ct volumes and radiology reports for supervised-level zero-shot detection of abnormalities. arXiv preprint [arXiv:2403.17834](http://arxiv.org/abs/2403.17834) (2024)
- <span id="page-31-2"></span>10. Hamamci, I.E., Er, S., Simsar, E., Sekuboyina, A., Gundogar, M., Stadlinger, B., Mehl, A., Menze, B.: Diffusion-based hierarchical multi-label object detection to analyze panoramic dental x-rays. In: Greenspan, H., Madabhushi, A., Mousavi, P., Salcudean, S., Duncan, J., Syeda-Mahmood, T., Taylor, R. (eds.) Medical Image Computing and Computer Assisted Intervention – MICCAI 2023. pp. 389–399. Springer Nature Switzerland, Cham (2023)
- <span id="page-31-10"></span>11. Hamamci, I.E., Er, S., Simsar, E., Tezcan, A., Simsek, A.G., Almas, F., Esirgun, S.N., Reynaud, H., Pati, S., Bluethgen, C., et al.: Generatect: Text-guided 3d chest ct generation. arXiv preprint [arXiv:2305.16037](http://arxiv.org/abs/2305.16037) (2023)
- <span id="page-31-0"></span>12. Hamamci, I.E., Er, S., Simsar, E., Yuksel, A.E., Gultekin, S., Ozdemir, S.D., Yang, K., Li, H.B., Pati, S., Stadlinger, B., et al.: Dentex: An abnormal tooth detection with dental enumeration and diagnosis benchmark for panoramic x-rays. arXiv preprint [arXiv:2305.19112](http://arxiv.org/abs/2305.19112) (2023)
- <span id="page-31-14"></span>13. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-31-1"></span>14. Irvin, J., Rajpurkar, P., Ko, M., Yu, Y., Ciurea-Ilcus, S., Chute, C., Marklund, H., Haghgoo, B., Ball, R., Shpanskaya, K., et al.: Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison. In: Proceedings of the AAAI conference on artificial intelligence. vol. 33, pp. 590–597 (2019)
- <span id="page-31-3"></span>15. Jing, B., Xie, P., Xing, E.: On the automatic generation of medical imaging reports. arXiv preprint [arXiv:1711.08195](http://arxiv.org/abs/1711.08195) (2017)
- <span id="page-31-5"></span>16. Johnson, A.E., Pollard, T.J., Berkowitz, S.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Mark, R.G., Horng, S.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Scientific data  $6(1)$ , 317 (2019)
- <span id="page-31-11"></span>17. Lample, G., Sablayrolles, A., Ranzato, M., Denoyer, L., Jégou, H.: Large memory layers with product keys. Advances in Neural Information Processing Systems 32 (2019)
- <span id="page-31-12"></span>18. Lavie, A., Denkowski, M.J.: The meteor metric for automatic evaluation of machine translation. Machine translation 23, 105–115 (2009)
- <span id="page-31-8"></span>19. Li, J., Zhu, G., Hua, C., Feng, M., Bennamoun, B., Li, P., Lu, X., Song, J., Shen, P., Xu, X., et al.: A systematic collection of medical image datasets for deep learning. ACM Computing Surveys 56(5), 1–51 (2023)
- <span id="page-31-4"></span>20. Li, M., Lin, B., Chen, Z., Lin, H., Liang, X., Chang, X.: Dynamic graph enhanced contrastive learning for chest x-ray report generation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3334– 3343 (2023)
- <span id="page-31-13"></span>21. Lin, C.Y.: Rouge: A package for automatic evaluation of summaries. In: Text summarization branches out. pp. 74–81 (2004)
- <span id="page-31-7"></span>22. Müller, N.: Computed tomography and magnetic resonance imaging: past, present and future. European Respiratory Journal 19(35 suppl), 3s–12s (2002)

- <span id="page-32-5"></span>23. Nguyen, H.Q., Lam, K., Le, L.T., Pham, H.H., Tran, D.Q., Nguyen, D.B., Le, D.D., Pham, C.M., Tong, H.T., Dinh, D.H., et al.: Vindr-cxr: An open dataset of chest x-rays with radiologist's annotations. Scientific Data  $9(1)$ , 429 (2022)
- <span id="page-32-8"></span>24. Papineni, K., Roukos, S., Ward, T., Zhu, W.J.: Bleu: a method for automatic evaluation of machine translation. In: Proceedings of the 40th annual meeting of the Association for Computational Linguistics. pp. 311–318 (2002)
- <span id="page-32-1"></span>25. Pati, S., Thakur, S.P., Hamamcı, İ.E., Baid, U., Baheti, B., Bhalerao, M., Güley, O., Mouchtaris, S., Lang, D., Thermos, S., et al.: Gandlf: the generally nuanced deep learning framework for scalable end-to-end clinical workflows. Communications Engineering 2(1), 23 (2023)
- <span id="page-32-3"></span>26. Thirunavukarasu, A.J., Ting, D.S.J., Elangovan, K., Gutierrez, L., Tan, T.F., Ting, D.S.W.: Large language models in medicine. Nature medicine 29(8), 1930–1940 (2023)
- <span id="page-32-4"></span>27. Wang, J., Bhalerao, A., He, Y.: Cross-modal prototype driven network for radiology report generation. In: European Conference on Computer Vision. pp. 563–579. Springer (2022)
- <span id="page-32-0"></span>28. Wang, X., Peng, Y., Lu, L., Lu, Z., Bagheri, M., Summers, R.M.: Chestx-ray8: Hospital-scale chest x-ray database and benchmarks on weakly-supervised classification and localization of common thorax diseases. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 2097–2106 (2017)
- <span id="page-32-7"></span>29. Willemink, M.J., Noël, P.B.: The evolution of image reconstruction for ct-from filtered back projection to artificial intelligence. European radiology 29, 2185–2195 (2019)
- <span id="page-32-2"></span>30. Yüksel, A.E., Gültekin, S., Simsar, E., Özdemir, S.D., Gündogar, M., Tokgöz, S.B., Hamamci, I.E.: Dental enumeration and multiple treatment detection on panoramic X-rays using deep learning. Scientific Reports (2021). [https://doi.org/](https://doi.org/10.1038/s41598-021-90386-1) [10.1038/s41598-021-90386-1](https://doi.org/10.1038/s41598-021-90386-1)
- <span id="page-32-6"></span>31. Zhu, Q., Mathai, T.S., Mukherjee, P., Peng, Y., Summers, R.M., Lu, Z.: Utilizing longitudinal chest x-rays and reports to pre-fill radiology reports. arXiv preprint [arXiv:2306.08749](http://arxiv.org/abs/2306.08749) (2023)

Image /page/33/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below. The icon is a gray circle with a darker gray ribbon or bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# **Curriculum Prompting Foundation Models for Medical Image Segmentation**

Xiuqi Zheng [,](http://orcid.org/0009-0002-2814-5167) Yuhang Zhang, Haoran Zhang, Hongrui Liang, Xueqi Bao, Zhuqing Jiang<sup>( $\boxtimes$ [\)](http://orcid.org/0000-0002-6032-8548)</sup>, and Qicheng Lao<sup>( $\boxtimes$ )</sup>

Beijing University of Posts and Telecommunications, Beijing, China <EMAIL>

**Abstract.** Adapting large pre-trained foundation models, e.g., SAM, for medical image segmentation remains a significant challenge. A crucial step involves the formulation of a series of specialized prompts that incorporate specific clinical instructions. Past works have been heavily reliant on a singular type of prompt for each instance, necessitating manual input of an ideally correct prompt, which is less efficient. To tackle this issue, we propose to utilize prompts of different granularity, which are sourced from original images to provide a broader scope of clinical insights. However, combining prompts of varying types can pose a challenge due to potential conflicts. In response, we have designed a coarse-to-fine mechanism, referred to as curriculum prompting, that progressively integrates prompts of different types. Through extensive experiments on three public medical datasets across various modalities, we demonstrate the effectiveness of our proposed approach, which not only automates the prompt generation process but also yields superior performance compared to other SAMbased medical image segmentation methods. Code will be available at: [https://github.com/AnnaZzz-zxq/Curriculum-Prompting.](https://github.com/AnnaZzz-zxq/Curriculum-Prompting)

**Keywords:** Medical image segmentation · SAM · Prompt engineering · Curriculum learning

# **1 Introduction**

Medical image segmentation is a critical area of research within medical image analysis. It plays a vital role in identifying and delineating various tissues or lesions, thereby significantly enhancing the efficiency and accuracy of medical diagnosis [\[4\]](#page-41-0). Recently, with the advent of large-scale foundation models for segmentation such as SAM  $[16]$  $[16]$ , the field of medical image segmentation has seen rapid development. SAM enables the generation of masks for regions of interest through interactive prompting, making it well-suited for universal medical image segmentation tasks. Several studies  $[11, 12, 24]$  $[11, 12, 24]$  $[11, 12, 24]$  $[11, 12, 24]$  have already explored the application of SAM in medical image segmentation. However, due to the substantial differences between natural and medical images, SAM struggles to achieve optimal segmentation performance across medical image datasets. One strategy to enhance SAM's performance in medical image segmentation involves integrating medical knowledge through specialized prompts. However, the manual generation of such prompts incurs high labor costs and yields diverse prompt quality.

To address the aforementioned challenges, this paper introduces an automated approach for identifying an optimal prompt for SAM-based medical image segmentation. Unlike conventional methods that rely on a single prompt and necessitate manual intervention, our proposed methodology leverages multiple prompt types to integrate a diverse range of image-specific details and clinical knowledge into the network. However, combining diverse knowledge domains presents a non-trivial challenge. Inspired by curriculum learning [\[1\]](#page-41-1), which is motivated by the cognitive learning strategies of humans gradually acquiring knowledge from simple to complex tasks, we propose *curriculum prompting*, which employs prompts that have progressively increasing granularity to systematically address segmentation challenges of varying difficulty levels, starting from coarse to fine-grained levels, to mitigate conflicts across different prompt domains. Specifically, we use mask prompts as an intermediary to gradually combine box and point prompts, refining the initial coarse mask prompt into a fine-tuned version. Unlike conventional SAM-based medical image segmentation methods that depend solely on a single prompt and necessitate the manual provision of an absolutely correct prompt, our approach significantly reduces the need for manual intervention, enabling the automatic generation of optimal prompts for SAM-based medical image segmentation based only on input medical images. In summary, our paper makes three significant contributions:

- Automated Prompt Generation: We propose a novel approach to automatically generate optimal prompts for SAM-based medical image segmentation, eliminating the need for manual intervention and providing more imagespecific details and clinically specific knowledge to the network.
- Curriculum Prompting Method: Our method integrates prompts of varying domains in a progressive manner, starting from coarse to fine-grained levels, which helps mitigate conflicts when simultaneously using multiple prompts from different domains.
- Improved Segmentation Results: The combined effect of automated prompt generation and curriculum prompting leads to significantly improved segmentation results on three public medical datasets across various modalities, outperforming existing SAM-based methods qualitatively and quantitatively.

# **2 Methodology**

### **2.1 Overview**

Given an image  $I \in R^{H \times W \times 3}$  with spatial resolution  $H \times W$ , large foundation models for segmentation, e.g., SAM, typically adopt an image encoder for extracting the image embedding **e** from the image *I*, transform the prompt input

Image /page/35/Figure/1 description: This is a diagram illustrating the "Curriculum Prompting Segment Anything Model." The model has two main stages: (a) Intermediate Prompt Generation and (b) Curriculum Prompting Segment Anything Model. In stage (a), it shows two parallel pathways for generating prompts: Keypoint Detection Networks (Fpoint) using HRNet and ViTPose, and Object Detection Networks (Fbox) using GLIP and Grounding DINO. These pathways generate edge points and bounding boxes, respectively. Stage (b) details the segmentation process. It takes an input image and an image embedding (e) generated by an Image Encoder. It uses a Box Prompt (Pbox) with a bounding box to generate a coarse mask (Scoarse) via a Box Prompt Encoder (Encb) and a Mask Decoder. The coarse mask is then processed with a convolution and combined with a Point Prompt Encoder (Encp) output, which uses edge points as input. Finally, another Mask Decoder produces the final fine mask (Sfine).

Fig. 1. Overview of Curriculum Prompting: (a) Intermediate Prompt Generation, which prepares prompts for SAM; (b) Curriculum Prompting SAM, first utilizing selfgenerated box prompts to obtain coarse masks, and then acquire refined masks with self-generated point prompts and coarse masks (as mask prompts).

*P* through a prompt encoder *Enc*, and finally generate a segmentation mask *S* through a mask decoder *Dec*, formulated as:

$$
S = Dec(\mathbf{e}, Enc(P)),
$$
\n(1)

where *P* can be in the form of various types, such as point prompt  $P_{point}$  =  $[x, y]$ , where *x*, *y* denotes the coordinates of the point, box prompt  $P_{box}$  $[x_1, y_1, x_2, y_2]$ , composed of coordinates of the top-left and bottom-right corners of the bounding box, and mask prompt  $P_{mask} \in R^{H \times W}$ .

Prompts play a crucial role during the segmenting process, where high-quality prompts enable SAM to produce accurate segmentation masks [\[3](#page-41-2)[,5](#page-41-3),[13\]](#page-42-3). However, existing methods only utilize a single type of prompt, which contains limited information and often requires manual interventions.

Our proposed curriculum prompting adheres to a straightforward idea, which aims to progressively combine different types of prompts in a coarse-to-fine way. We begin with the initial prompt  $P_1$  to assist SAM in segmentation tasks. Subsequently, the intermediate prediction generated by  $P_1$  is fed back together with an auxiliary prompt as supplementary into SAM, initiating a recursive process. This cycle continues *n* steps until a satisfactory segmentation result is achieved, and our empirical observations indicate that a notably improved result can be obtained when  $n = 2$ . This process can be described as:

$$
P_2 = Dec(e, Enc(P_1)),
$$

$$
P_3 = Dec(e, Enc(P_2, P'_2)),
$$

$$
...,
$$

$$
S = Dec(e, Enc(P_n, P'_n))
$$

$$
(2)
$$

where  $P'_n$  denotes an auxiliary prompt apart from  $P_n$  as a supplementary.

In summary, we design a curriculum prompting mechanism to first address intermediate easy segmentation tasks and acquire initial coarse masks with selfgenerated prompts, and then add more refined prompts to tackle harder segmentation tasks and obtain the ultimate mask, to improve the overall performance.

<span id="page-36-0"></span>

### **2.2 Coarse Prompting**

During the coarse prompting phase, we aim to segment most of the foreground pixels which is an easier task compared to the fine-grained segmentation with a single step. We utilize prompts that are relatively coarse but contain sufficient information to obtain an initial coarse mask. Since empirical observations suggest that two different types of prompts, e.g., box prompt and point prompt, may conflict with each other  $[3,13]$  $[3,13]$  $[3,13]$ , in this work, we choose to employ a single type of prompt as our coarse prompt. Compared to point prompts, box prompts encompass more significant information, indicating the precise location of the object and the potential intensity features within a specified limited area. Thus, we consider self-generated box prompts as coarse prompts for initial masks.

To break through the limitation of SAM requiring manual prompts, we intend to directly and automatically derive prompts from the original image. We generate box prompts with large pre-trained object detection models, e.g. Grounding DINO [\[20](#page-42-4)] or GLIP [\[17](#page-42-5)]. We fine-tune the pre-trained model with the given medical data and obtain the self-generated box prompts  $P'_{box}$  as follows,

$$
P'_{box} = F_{box}(I, T),\tag{3}
$$

where  $F_{box}$  denotes the chosen object detection model, *I* denotes the input image and *T* denotes the text prompt if required for the model.

Following the acquisition of box prompts, a series of post-processing steps (e.g. NMS ) are undertaken. We fine-tune SAM's prompt encoder with groundtruth bounding boxes, employing a combination of Dice Loss and BCE Loss as our loss function. Then, we acquire coarse masks *Scoarse* utilizing these selfgenerated box prompts and the input image embedding **e**,

$$
S_{coarse} = Dec(\mathbf{e}, Enc_B(P'_{box})),\tag{4}
$$

where  $Enc_B$  denotes the prompt encoder fine-tuned with bounding boxes.

### **2.3 Fine-Grained Prompting**

Having acquired the coarse masks, we further aim to employ more refined prompts to tackle a harder fine-grained segmentation task and guide SAM in

generating the final mask. As indicated in [\[15](#page-42-6)], SAM struggles with precise edge segmentation, making the enhancement of edge delineation a more complex task compared to segmenting most of the foreground pixels.

Thus, we adopt edge points as additional prompts to unleash SAM's full ability for segmentation. Similar to the process of box prompt generation, we employ a keypoint detection network (e.g. HRNet [\[26\]](#page-43-1) or ViTPose [\[27](#page-43-2)]) to generate point prompts. We obtain the self-generated point prompts  $P'_{point}$  as follows:

<span id="page-37-0"></span>
$$
P'_{point} = F_{point}(I),\tag{5}
$$

where  $F_{point}$  denotes the keypoint detection network.

However, utilizing multiple types of prompts synergistically requires careful design. As numerous studies have indicated [\[13](#page-42-3),[23,](#page-43-3)[28\]](#page-43-4), the simultaneous use of point and box prompts can paradoxically lead to a decrease in performance. One speculation about the cause of this contradiction is due to the structure of SAM's prompt encoder. In SAM's prompt encoder *Enc*, point prompts *Ppoint* and box prompts *Pbox* are processed through a series of steps and then concatenated into a sparse embedding, which is fed into the mask decoder *Dec*. During this process, different types of prompts may influence each other.

The question then arises: how can we effectively incorporate the guidance of point prompts while leveraging the information from box prompts? The answer lies in employing an additional type of prompt - the mask prompt, as a bridge to combine both box prompts and point prompts. This is where we take advantage of the coarse masks *Scoarse* obtained in Sect. [2.2.](#page-36-0)

While point embeddings and box embeddings influence each other, the mask prompts *Pmask* will only be transformed into a dense embedding through convolutions and summed with the image embedding **e** without interacting with the sparse embedding. Thus, we employ self-generated point prompts  $P'_{point}$  on the basis of coarse masks *Scoarse* as mask prompts to achieve refined segmentation.

Similar to the process described in Sect. [2.2,](#page-36-0) the SAM model we use has undergone fine-tuning with medical images, and edge points and coarse masks served as prompts. Then final masks  $S_{fine}$  are acquired as follows:

$$
S_{fine} = Dec(\mathbf{e}, Enc_P(S_{coarse}, P'_{point})),
$$
\n(6)

where *Enc<sub>P</sub>* denotes the prompt encoder that is fine-tuned with edge point prompts and mask prompts, and  $P'_{point}$  is obtained by Eq.([5\)](#page-37-0).

### **3 Experiments and Results**

### **3.1 Dataset**

We evaluate our proposed method on three public medical image datasets across various modalities, including thyroid nodule segmentation dataset TN3K [\[10\]](#page-42-7), polyp segmentation dataset Kvasir [\[14](#page-42-8)], and pulmonary lesion segmentation dataset QaTa-COV19 [\[7](#page-42-9)]. The TN3K dataset includes 3493 ultrasound images

| Method                    | Kvasir (Endoscopy) |               | TN3K (Ultrasound) |               | QaTa-COV19 (X-ray) |               |
|---------------------------|--------------------|---------------|-------------------|---------------|--------------------|---------------|
|                           | mDice(%)           | mIoU(%)       | mDice(%)          | mIoU(%)       | mDice(%)           | mIoU(%)       |
| CaraNet [21]              | 92.050             | 86.890        | 72.647            | 62.746        | 73.887             | 63.517        |
| TRFE+ [10]                | 42.819             | 29.517        | 83.300*           | 71.380*       | 45.719             | 32.835        |
| LViT-T[19]                | 77.899             | 67.519        | 76.871            | 66.573        | 77.207             | 67.178        |
| fine-tuned SAM [16]       | 81.848             | 74.191        | 50.791            | 39.771        | 48.794             | 61.504        |
| nnSAM [18]                | 91.176             | 85.946        | 82.797            | 74.027        | 78.943             | 69.452        |
| SAM-Med2D (9 Points) [5]- | -                  | -             | 64.740            | 55.760        | 76.431             | 66.083        |
| MedSAM (Box) [22]         | 86.473             | 78.046        | 81.126            | 69.464        |                    |               |
| Grounded SAM [25]         | 93.340             | 89.029        | 81.600            | 73.986        | 78.625             | 68.616        |
| <b>Ours</b>               | <b>93.670</b>      | <b>89.442</b> | <b>84.430</b>     | <b>76.367</b> | <b>79.826</b>      | <b>70.265</b> |

<span id="page-38-0"></span>**Table 1.** Comparisons with traditional task-specific and SAM-based medical image segmentation methods. "\*" denotes results reported by the referenced paper. "-" means results are unavailable caused by dataset being used during training.

with pixel-wise thyroid nodule annotations; The Kvasir dataset contains 1000 endoscopic images and their corresponding polyp ground-truth masks; The QaTa-COV19 dataset consists of 9258 chest X-ray radiographs with pneumonia segmentation masks. We follow the same dataset split as  $[9,10,19]$  $[9,10,19]$  $[9,10,19]$  $[9,10,19]$ , respectively.

### **3.2 Experiment Settings and Metrics**

Our method finetunes four distinct models, ensuring each model builds upon previous outputs. We fine-tune the object detection and keypoint detection network through the MMDetection  $[2]$  and MMPose  $[6]$  framework. Specifically, we select Grounding DINO [\[20\]](#page-42-4) and HRNet [\[26](#page-43-1)] for box and point prompt generation, respectively. Specifically, we use 8 edge points as point prompts. In terms of fine-tuning SAM, we initialize the model with the pre-trained weight of SAM's ViT-H version [\[8\]](#page-42-15). We employ an AdamW optimizer with a learning rate of 0.0001 and a batch size of 4. Our model is implemented using PyTorch and trained and evaluated on an Nvidia RTX4090 24 GB GPU. We adopt two commonly used metrics to quantitatively evaluate our proposed method, Dice (dice coefficient) and IoU (Intersection over Union).

### **3.3 Results**

**Our Proposed Approach Outperforms the Baselines on All Three Datasets.** We compare our method with SOTA task-specific methods and SAMbased foundation models. CaraNet  $[21]$  $[21]$ , TRFE+  $[10]$  $[10]$  and LViT-T  $[19]$  are three SOTA methods on the Kvasir, TN3K and QaTa-COV19 datasets, respectively. Additionally, five SOTA foundation models are chosen for comparison, including the vanilla SAM [\[16](#page-42-0)], nnSAM [\[18](#page-42-12)], SAM-Med2D [\[5](#page-41-3)], MedSAM [\[22\]](#page-42-13) and Grounded SAM [\[25](#page-43-5)]. Note that we standardize the text prompt to the name or a simple

Image /page/39/Picture/1 description: This image displays a grid of ultrasound images and their corresponding segmentation masks generated by different algorithms. The grid has three rows and eight columns. The first column shows the original ultrasound images, labeled 'Images'. The subsequent columns show segmentation results from various methods: TRFE+, nnSAM, SAM-Med2D, MedSAM, Grounded SAM, Ours, and GT (Ground Truth). Each row presents a different ultrasound scan and the segmentation masks produced by each algorithm for that scan. The segmentation masks are binary images, with white representing the segmented region and black representing the background.

**Fig. 2.** Qualitative comparisons between our curriculum prompting SAM and other segmentation methods on the TN3K dataset, including SOTA task-specific method TRFE+, and other SAM-based segmentation models.

<span id="page-39-0"></span>Image /page/39/Figure/3 description: This figure illustrates a process for segmenting pulmonary infections in chest X-rays. It shows two rows, each representing a different case. Each row displays the original X-ray image, the image with bounding box prompts indicating suspected infection areas, a coarse mask generated from the bounding boxes, a point prompt refinement stage with green boxes and red lines connecting yellow points, the final segmented mask, and the ground truth (GT) mask. The top row shows segmentation of bilateral pulmonary infections, and the bottom row also shows segmentation of bilateral pulmonary infections.

<span id="page-39-1"></span>**Fig. 3.** The process of mask generation through our proposed curriculum prompting.

description of the target lesion, such as polyp, thyroid nodule, or bilateral pul-monary infection, for fine-tuning LViT-T [\[19](#page-42-11)] and Grounded SAM [\[25](#page-43-5)].

Table [1](#page-38-0) summarizes the quantitative results. Notably, our method consistently achieves the best performance on all three tasks with average IoU scores of 89.442%, 76.367%, and 70.265%. Compared to SAM-Med2D and MedSAM which require extra point prompts or box prompts derived from labels, our method outperforms them by a large margin (e.g., mean IoU  $> 6.9\%$ ) without human intervention. This validates the effectiveness of our proposed method by integrating multiple prompts in a coarse-to-fine manner.

We present qualitative results in Fig. [2,](#page-39-0) where the segmentation masks of the thyroid nodules from different methods are shown. As seen in the figure, our method can precisely locate the target lesion and yields more accurate and smooth edge delineation, compared to other baselines.

**Visualization of Curriculum Prompting Process.** As shown in Fig. [3,](#page-39-1) during the first coarse phase when only the box prompt is used, SAM is capable of segmenting the majority of the foreground pixels. Through curriculum prompting, with the addition of edge points guidance on this basis, SAM can discern where the edges of the target are, as well as accurately distinguish between two target areas when they are nearby, instead of merging the masks into one large area. Moreover, it can be observed that the edges of the final mask have become smoother, with fewer isolated dots that are not connected to the larger area, which is very common in masks generated by SAM.

| Label       | Metric    | Result |
|-------------|-----------|--------|
| negative(0) | mDice (%) | 84.430 |
|             | mIoU (%)  | 76.367 |
| positive(1) | mDice (%) | 84.259 |
|             | mIoU (%)  | 76.192 |

<span id="page-40-0"></span>**Table 2.** Negative or positive prompts.

<span id="page-40-1"></span>

| Point        | BBox         | Mask         | mDice (%) | mIoU (%) |
|--------------|--------------|--------------|-----------|----------|
| $\checkmark$ |              |              | 70.300    | 61.127   |
|              | $\checkmark$ |              | 81.600    | 73.986   |
|              | $\checkmark$ | $\checkmark$ | 81.660    | 74.099   |
| $\checkmark$ | $\checkmark$ |              | 79.466    | 71.454   |
| $\checkmark$ | $\checkmark$ | $\checkmark$ | 84.430    | 76.367   |

**Edge Points Served as Negative Prompts Can Better Improve SAM's Performance.** As SAM struggles with precise edge segmentation, we introduce point prompts to provide extra details, especially focusing on the lesion edges. These points can act as either positive or negative prompts. Table [2](#page-40-0) demonstrates that labeling edge points as negative (label  $= 0$ ) can better enhance the segmentation result. We theorize that negative prompts give more detailed guidance, clearly marking non-foreground areas. In contrast, positive prompts may not add valuable information, as the model might already identify these areas as foreground, diminishing their impact on edge definition. Thus, we label the point prompts as negative in all our experiments.

**Ablation Study.** There are three different types of prompts used in our study yielding seven unique combinations. We perform ablation studies on five scenarios on the TN3K dataset, detailed in Table [3.](#page-40-1) Given that mask prompts result from SAM's inference using box prompts, we exclude unavailable scenarios including solely utilizing mask prompts and utilizing both point and mask prompts due to their dependency on box prompts for mask generation. When segmenting solely with 8 edge points, SAM fails to achieve a satisfactory result, whereas, when using self-generated boxes, SAM is already capable of achieving relatively good segmentation. We can observe a decline when simultaneously using point and box prompts, compared to using box prompts alone. The results show that when utilizing three prompt types in the proposed curriculum manner, SAM gives the best segmentation performance, demonstrating each prompt type is necessary and curriculum combining them is effective.

**Training Time.** The time consumption primarily occurs during the finetuning process. Our model requires 9.5 h, 2.7 h, 21.1 h training on TN3K, Kvasir, and QaTa-COV19. For comparison, the nnSAM model takes 15.2 h, 12.5 h, and 20.8 h. In most cases, our training time is shorter than nnSAM but outperforms nnSAM on all three datasets, demonstrating that though our training process is somewhat complicated, the training time is acceptable.

### **4 Conclusion**

In this paper, we present curriculum prompting for medical image segmentation using large foundation models, an efficient method to combine multiple prompts for better segmentation performance. We employ self-generated prompts that have progressively increasing granularity to systematically address segmentation challenges of varying difficulty levels. Compared to utilizing a singular type of prompt, our method introduces more prompt information while avoiding possible conflicts between different prompt types, and achieves state-of-the-art performance on three public medical datasets with different modalities and target lesions. We hope our study provides some inspiration about prompting vision foundation models for medical image segmentation.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-41-1"></span>1. Bengio, Y., Louradour, J., Collobert, R., Weston, J.: Curriculum learning. In: Proceedings of the 26th Annual International Conference on Machine Learning. p. 41-48. ICML '09, Association for Computing Machinery, New York, NY, USA (2009). <https://doi.org/10.1145/1553374.1553380>
- <span id="page-41-4"></span>2. Chen, K., Wang, J., Pang, J., Cao, Y., Xiong, Y., Li, X., Sun, S., Feng, W., Liu, Z., Xu, J., Zhang, Z., Cheng, D., Zhu, C., Cheng, T., Zhao, Q., Li, B., Lu, X., Zhu, R., Wu, Y., Dai, J., Wang, J., Shi, J., Ouyang, W., Loy, C.C., Lin, D.: MMDetection: Open mmlab detection toolbox and benchmark. arXiv preprint [arXiv:1906.07155](http://arxiv.org/abs/1906.07155) (2019)
- <span id="page-41-2"></span>3. Cheng, D., Qin, Z., Jiang, Z., Zhang, S., Lao, Q., Li, K.: Sam on medical images: A comprehensive study on three prompt modes. arXiv preprint [arXiv:2305.00035](http://arxiv.org/abs/2305.00035) (2023)
- <span id="page-41-0"></span>4. Cheng, J., Tian, S., Yu, L., Gao, C., Kang, X., Ma, X., Wu, W., Liu, S., Lu, H.: Resganet: Residual group attention network for medical image classification and segmentation. Medical Image Analysis **76**, 102313 (2022)
- <span id="page-41-3"></span>5. Cheng, J., Ye, J., Deng, Z., Chen, J., Li, T., Wang, H., Su, Y., Huang, Z., Chen, J., Jiang, L., et al.: Sam-med2d. arXiv preprint [arXiv:2308.16184](http://arxiv.org/abs/2308.16184) (2023)
- <span id="page-41-5"></span>6. Contributors, M.: Openmmlab pose estimation toolbox and benchmark. [https://](https://github.com/open-mmlab/mmpose) [github.com/open-mmlab/mmpose](https://github.com/open-mmlab/mmpose) (2020)

- <span id="page-42-9"></span>7. Degerli, A., Kiranyaz, S., Chowdhury, M.E., Gabbouj, M.: Osegnet: Operational segmentation network for covid-19 detection using chest x-ray images. In: 2022 IEEE International Conference on Image Processing (ICIP). pp. 2306–2310. IEEE (2022)
- <span id="page-42-15"></span>8. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-42-14"></span>9. Fan, D.P., Ji, G.P., Zhou, T., Chen, G., Fu, H., Shen, J., Shao, L.: Pranet: Parallel reverse attention network for polyp segmentation. In: International conference on medical image computing and computer-assisted intervention. pp. 263–273. Springer (2020)
- <span id="page-42-7"></span>10. Gong, H., Chen, J., Chen, G., Li, H., Li, G., Chen, F.: Thyroid region prior guided attention for ultrasound segmentation of thyroid nodules. Computers in Biology and Medicine **155**, 106389 (2023)
- <span id="page-42-1"></span>11. He, S., Bao, R., Li, J., Grant, P.E., Ou, Y.: Accuracy of segment-anything model (sam) in medical image segmentation tasks. arXiv preprint [arXiv:2304.09324](http://arxiv.org/abs/2304.09324) (2023)
- <span id="page-42-2"></span>12. Huang, Y., Yang, X., Liu, L., Zhou, H., Chang, A., Zhou, X., Chen, R., Yu, J., Chen, J., Chen, C., et al.: Segment anything model for medical images? Medical Image Analysis **92**, 103061 (2024)
- <span id="page-42-3"></span>13. Huang, Y., Yang, X., Liu, L., Zhou, H., Chang, A., Zhou, X., Chen, R., Yu, J., Chen, J., Chen, C., et al.: Segment anything model for medical images? Medical Image Analysis **92**, 103061 (2024)
- <span id="page-42-8"></span>14. Jha, D., Smedsrud, P.H., Riegler, M.A., Halvorsen, P., de Lange, T., Johansen, D., Johansen, H.D.: Kvasir-seg: A segmented polyp dataset. In: MultiMedia Modeling: 26th International Conference, MMM 2020, Daejeon, South Korea, January 5–8, 2020, Proceedings, Part II 26. pp. 451–462. Springer (2020)
- <span id="page-42-6"></span>15. Ke, L., Ye, M., Danelljan, M., Liu, Y., Tai, Y.W., Tang, C.K., Yu, F.: Segment anything in high quality. arXiv preprint [arXiv:2306.01567](http://arxiv.org/abs/2306.01567) (2023)
- <span id="page-42-0"></span>16. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., et al.: Segment anything. arXiv preprint [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-42-5"></span>17. Li, L.H., Zhang, P., Zhang, H., Yang, J., Li, C., Zhong, Y., Wang, L., Yuan, L., Zhang, L., Hwang, J.N., et al.: Grounded language-image pre-training. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 10965–10975 (2022)
- <span id="page-42-12"></span>18. Li, Y., Jing, B., Feng, X., Li, Z., He, Y., Wang, J., Zhang, Y.: nnsam: Plugand-play segment anything model improves nnunet performance. arXiv preprint [arXiv:2309.16967](http://arxiv.org/abs/2309.16967) (2023)
- <span id="page-42-11"></span>19. Li, Z., Li, Y., Li, Q., Wang, P., Guo, D., Lu, L., Jin, D., Zhang, Y., Hong, Q.: Lvit: language meets vision transformer in medical image segmentation. IEEE transactions on medical imaging (2023)
- <span id="page-42-4"></span>20. Liu, S., Zeng, Z., Ren, T., Li, F., Zhang, H., Yang, J., Li, C., Yang, J., Su, H., Zhu, J., et al.: Grounding dino: Marrying dino with grounded pre-training for open-set object detection. arXiv preprint [arXiv:2303.05499](http://arxiv.org/abs/2303.05499) (2023)
- <span id="page-42-10"></span>21. Lou, A., Guan, S., Ko, H., Loew, M.H.: Caranet: Context axial reverse attention network for segmentation of small medical objects. In: Medical Imaging 2022: Image Processing. vol. 12032, pp. 81–92. SPIE (2022)
- <span id="page-42-13"></span>22. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nature Communications **15**, 1–9 (2024)

- <span id="page-43-3"></span>23. Mattjie, C., de Moura, L.V., Ravazio, R.C., Kupssinskü, L.S., Parraga, O., Delucis, M.M., Barros, R.C.: Zero-shot performance of the segment anything model (sam) in 2d medical imaging: A comprehensive evaluation and practical guidelines. arXiv preprint [arXiv:2305.00109](http://arxiv.org/abs/2305.00109) (2023)
- <span id="page-43-0"></span>24. Putz, F., Grigo, J., Weissmann, T., Schubert, P., Hoefler, D., Gomaa, A., Tkhayat, H.B., Hagag, A., Lettmaier, S., Frey, B., et al.: The segment anything foundation model achieves favorable brain tumor autosegmentation accuracy on mri to support radiotherapy treatment planning. arXiv preprint [arXiv:2304.07875](http://arxiv.org/abs/2304.07875) (2023)
- <span id="page-43-5"></span>25. Ren, T., Liu, S., Zeng, A., Lin, J., Li, K., Cao, H., Chen, J., Huang, X., Chen, Y., Yan, F., Zeng, Z., Zhang, H., Li, F., Yang, J., Li, H., Jiang, Q., Zhang, L.: Grounded sam: Assembling open-world models for diverse visual tasks (2024)
- <span id="page-43-1"></span>26. Sun, K., Xiao, B., Liu, D., Wang, J.: Deep high-resolution representation learning for human pose estimation. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 5693–5703 (2019)
- <span id="page-43-2"></span>27. Xu, Y., Zhang, J., Zhang, Q., Tao, D.: Vitpose: Simple vision transformer baselines for human pose estimation. Advances in Neural Information Processing Systems **35**, 38571–38584 (2022)
- <span id="page-43-4"></span>28. Zhang, C., Puspitasari, F.D., Zheng, S., Li, C., Qiao, Y., Kang, T., Shan, X., Zhang, C., Qin, C., Rameau, F., et al.: A survey on segment anything model (sam): Vision foundation model meets prompt engineering. arXiv preprint [arXiv:2306.06211](http://arxiv.org/abs/2306.06211) (2023)

Image /page/44/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a curved line segment on the left side and a bookmark-like shape in the center. The text below the icon reads "Check for updates".

# **DB-SAM: Delving into High Quality Universal Medical Image Segmentation**

Chao Qin $^{1(\boxtimes)}$ , Jiale Cao<sup>2</sup>, Huazhu Fu<sup>3</sup>, Fahad Shahbaz Khan $^{1,4},$ and Rao Muhammad Anwer<sup>1</sup>

 $^{\rm 1}$  Mohamed bin Zayed University of Artificial Intelligence, Abu Dhabi, UAE <EMAIL> <sup>2</sup> Tianjin University, Tianjin, China

<sup>3</sup> Institute of High Performance Computing, Agency for Science, Technology and Research, Singapore, Singapore

 $4$  Linköping University, Linköping, Sweden

**Abstract.** Recently, the Segment Anything Model (SAM) has demonstrated promising segmentation capabilities in a variety of downstream segmentation tasks. However in the context of universal medical image segmentation there exists a notable performance discrepancy when directly applying SAM due to the domain gap between natural and 2D/3D medical data. In this work, we propose a dual-branch adapted SAM framework, named DB-SAM, that strives to effectively bridge this domain gap. Our dual-branch adapted SAM contains two branches in parallel: a ViT branch and a convolution branch. The ViT branch incorporates a learnable channel attention block after each frozen attention block, which captures domain-specific local features. On the other hand, the convolution branch employs a light-weight convolutional block to extract domain-specific shallow features from the input medical image. To perform cross-branch feature fusion, we design a bilateral crossattention block and a ViT convolution fusion block, which dynamically combine diverse information of two branches for mask decoder. Extensive experiments on large-scale medical image dataset with various 3D and 2D medical segmentation tasks reveal the merits of our proposed contributions. On 21 3D medical image segmentation tasks, our proposed DB-SAM achieves an absolute gain of 8.8%, compared to a recent medical SAM adapter in the literature. The code and model are available at <https://github.com/AlfredQin/DB-SAM.>

**Keywords:** Medical Image Segmentation · Segmentation Foundation Model · Multi-modality

## 1 Introduction

Medical image segmentation plays a pivotal role in various clinical applications, including disease diagnosis and surgical planning. In past years, deep learningbased methods have achieved significant progress on medical image segmentation tasks. Most existing methods [\[8](#page-52-0)[,9](#page-52-1)[,32](#page-54-0)] are typically task-specific in that

they usually focus on a specific segmentation task, for which the dataset is captured through a specific type of medical device. As a result, these methods have limited generalization ability to different medical segmentation tasks, especially when the medical device belongs to different types. Recently, a class-agnostic segmentation model, named SAM [\[18](#page-53-0)], has been introduced to perform universal segmentation in natural images. SAM is trained on large-scale data and exhibits impressive generalization ability on various down-stream segmentation tasks. However, its segmentation quality deteriorates when directly adapting to 2D and 3D medical image segmentation. This is due to a large domain gap between natural images and medical images. A straightforward way to adapt SAM for universal medical image segmentation is re-training the entire SAM model on medical image datasets. However, this is challenging since it requires significant computational resources along with the availability of a very large-scale medical image dataset. Compared to natural images, medical images are relatively scarce and expensive. To address this issue, the recently introduced MedSAM [\[22](#page-53-1)] focuses on only fine-tuning the mask decoder of SAM, achieving better universal medical image segmentation performance compared to the vanilla SAM. Nonetheless, this fine-tuning strategy does not fully harness domain-specific (medical) knowledge and likely results in sub-optimal segmentation performance, especially on organs with intricate outlines. In contrast, the concept of adapter that is prevalent in natural language processing (NLP) provides a potential avenue. The adapter module is usually parameter-efficient and can be easily integrated into a pre-trained model. During training, only the adapter module is trained and the pre-trained model parameters are frozen. In this work, we propose a dualbranch framework, named DB-SAM, that adapts SAM for high-quality universal medical image segmentation. We introduce a novel dual-branch encoder comprising **(i)** a ViT branch with inserted channel attention blocks for domain-specific locality inductive bias, and **(ii)** a convolution branch equipped with lightweight convolutional blocks for extracting shallow features. A bilateral cross-attention block is designed for effective cross-layer feature fusion between ViT branch and convolution branch. Finally, the output features of two branches are fused through an automatic selective mechanism. Our proposed DB-SAM is evaluated on a comprehensive dataset drawn from 30 public medical datasets covering both 3D and 2D images of different modalities. Our experimental results demonstrate the effectiveness of the proposed DB-SAM, leading to consistent improvement in performance on a diverse set of 2D and 3D medical segmentation tasks. In case of 3D medical image segmentation, the proposed DB-SAM achieves an absolute gain of 6% and 8*.*9% in terms of DSC and NSD, respectively, compared to the baseline MedSAM [\[22\]](#page-53-1).

## 2 Method

Figure [1\(](#page-46-0)a) shows overall architecture of our method. Given an input image *I*, we first resize the input image into two images of different resolutions that are represented as  $I_{vit} \in \mathbb{R}^{1024 \times 1024}$  and  $I_{conv} \in \mathbb{R}^{256 \times 256}$ . These two images are fed

Image /page/46/Figure/1 description: The image displays the overall architecture of a model called DB-SAM. It includes four subfigures: (a) Overall architecture of our DB-SAM, (b) Channel attention block, (c) Bilateral cross attention block, and (d) ViT-Conv fusion block. Subfigure (a) shows the input image "I\_vit" (1024 x 1024) being processed through a patch embedding, followed by several stages of self-attention and channel attention blocks, and bilateral cross attention. Another input "I\_conv" (256 x 256) goes through a lightweight convolution block. The outputs of these paths are fused in a final fusion block, then processed by a mask decoder, which takes input from a prompt encoder. The prompt encoder is shown as either learnable or frozen, with a bounding box input. Subfigure (b) details the channel attention block with PW Conv, SE Attention, DW Conv, and Layer Norm. Subfigure (c) illustrates the bilateral cross attention block with Feed Forward, Layer Norm, and Deformable Attention. Subfigure (d) shows the ViT-Conv fusion block with FC layers, GELU activation, sigmoid, and element-wise multiplication.

<span id="page-46-0"></span>**Fig. 1. (a)** Overall architecture of our DB-SAM. Our DB-SAM contains two branches: one ViT branch and one convolution branch. The ViT branch incorporates channel attention block **(b)** to capture domain-specific high-level features, while the convolution branch adopts light-weight convolution blocks to extract shallow features. For crossbranch fusion, we introduce a bilateral cross-attention operation **(c)** and ViT-Conv fusion module **(d)** to adaptively combine the features. Finally, the fused features and prompt embeddings are fed to mask decoder.

to two different branches of our dual-branch image encoder, where the image  $I_{vit}$  goes through the ViT branch and the image  $I_{conv}$  goes through the convolution branch. The ViT branch comprises a patch embedding block, multiple attention blocks, and multiple channel attention blocks. The convolution branch contains multiple light-weight convolutional blocks. To fuse the features between two branches, the bilateral cross-attention block is designed to perform crosslayer feature fusion. The output features of two branches are represented as  $F_d^o$  and  $F_s^o$ , which are fused as final image embeddings for mask decoder. In addition, we convert the bounding-box into prompt embeddings using a prompt encoder. Based on the image embeddings and prompt embeddings, we employ mask decoder to predict corresponding segmentation map.

### 2.1 ViT Branch

The SAM adopts the transformer ViT as image encoder, and trains it on largescale natural image dataset. To keep the strong feature representation ability of pre-trained ViT, we do not fine-tune the weights of pre-trained ViT during training, and introduce a local adapter module to introduce a locality inductive bias. Inspired by the advances in EfficientNetV2 [\[31\]](#page-54-1), our local adapter module contains a channel attention block attached after the attention block of ViT. Different to attention block, the channel attention block is learnable during training and aims to extract high-level domain-specific features from the different levels of ViT encoder. As a result, our ViT branch adapts the original ViT to medical image segmentation without losing the strong feature representation ability.

Figure [1\(](#page-46-0)b) shows the structure of channel attention block, which is written as

$$
F_{\text{out}} = F_{\text{vit}} + \text{Conv}_{1 \times 1} \left( \text{SE} \left( \text{DWConv}_{3 \times 3} \left( \text{LN}(F_{\text{vit}}) \right) \right) \right) \tag{1}
$$

where  $F_{\text{vit}}$  represents input embeddings from ViT attention block, LN represents layer normalization, DWConv<sub>3×3</sub> denotes depth-wise  $3\times3$  convolution, SE refers to the squeeze and excitation block [\[15](#page-53-2)], and  $Conv_{1\times 1}$  is the point-wise convolution. The channel attention block is a simple and efficient operation, which does not introduce much computational cost for ViT encoder.

### 2.2 Convolution Branch

The original ViT employs the patch embeddings to downsample the input image 16 times and then extracts deep features. We argue that the downsampling is not good to keep locality inductive bias. To incorporate more local details for accurate segmentation, we introduce a convolution branch that employs lightweight convolution blocks to directly extract shallow features from resized image *Iconv*. In addition, we introduce a bilateral cross attention block to fuse deep features from ViT branch and shallow features from convolution branch,

As in Fig. [1,](#page-46-0) we first feed the resized image *Iconv* to the light-weight convolution block to extract shallow features  $F_s$ . Afterwards, we employ the bilateral cross attention block to perform feature fusion. The generated features for two branches are represented as shallow features  $F_s^1$  and deep features  $F_d^1$ . Then we feed the features  $F_d^1$  to next self-attention blocks in the ViT branch. Similarly, we perform feature fusion of output features of each block. The final features for two branches are represented as  $F_s^o$  and  $F_d^o$ . We introduce the light-weight convolution block and bilateral cross attention block in details as follow.

**Light-Weight Convolution Block:** Our light-weight convolution block consists of two  $3 \times 3$  and three  $1 \times 1$  convolutional layers. We add a batch normalization layer and a ReLU layer after convolutional layer at the first four layers.

**Bilateral Cross Attention Block:** Figure [1\(](#page-46-0)c) shows the structure of bilateral cross attention block that aims to perform cross-branch feature fusion. Assuming that the deep feature is  $F_d$  and the shallow feature is  $F_s$ , we employ deformable attention  $[33]$  to fuse them as in  $[10]$  $[10]$ . For the ViT branch, we treat deep feature  $F_d$  as query, and treat shallow feature  $F_s$  as key and value. For the convolution branch, we treat shallow feature  $F_s$  as query, and treat deep feature  $F_d$  as

| Segmentation Task       |          | Modality | DSC (%)  |             |               | NSD (%)  |             |               |
|-------------------------|----------|----------|----------|-------------|---------------|----------|-------------|---------------|
|                         |          |          | SAM [18] | MedSAM [22] | DB-SAM (Ours) | SAM [18] | MedSAM [22] | DB-SAM (Ours) |
| <b>Brain Ventricles</b> | MR-T1    |          | 41.96    | 74.82       | 78.95         | 31.26    | 78.17       | 82.08         |
| <b>Brain Ventricles</b> | MR-T2    |          | 39.56    | 72.87       | 77.02         | 31.39    | 75.01       | 81.23         |
| Brain Tumor             | MR-FLAIR |          | 74       | 89.15       | 92.49         | 38.27    | 76.13       | 85.77         |
| Cerebellum              | MR-T1    |          | 83.25    | 93.31       | 94.47         | 44.55    | 82.39       | 88.32         |
| Cerebellum              | MR-T2    |          | 81.88    | 90.78       | 92.69         | 38.84    | 70.01       | 76.77         |
| Gallbladder             | MR       |          | 61.97    | 77.78       | 87.43         | 36.34    | 76.36       | 84.57         |
| Left Ventricle          | MR       |          | 68.44    | 88.91       | 91.15         | 55.73    | 91.05       | 94.10         |
| Right Ventricle         | MR       |          | 72.11    | 85.92       | 90.51         | 68.98    | 88.85       | 94.97         |
| Liver                   | MR       |          | 80.38    | 93.9        | 96.17         | 33       | 80.13       | 89.87         |
| Pancreas                | MR       |          | 51.11    | 80.07       | 85.16         | 32.71    | 79.42       | 88.26         |
| Prostate                | MR-ADC   |          | 79.61    | 92.25       | 93.25         | 60.12    | 92.72       | 94.13         |
| Prostate                | MR-T2    |          | 79.39    | 92.18       | 93.45         | 57.6     | 92          | 94.32         |
| Abdomen Tumor           | CT       |          | 42.86    | 65.54       | 78.31         | 34.49    | 64.99       | 79.62         |
| Gallbladder             | CT       |          | 47.28    | 84.36       | 90.12         | 30.48    | 87.07       | 94.92         |
| Head-Neck Tumor         | CT       |          | 23.87    | 68.29       | 76.14         | 23.88    | 47.36       | 56.27         |
| Liver                   | CT       |          | 74.21    | 91.42       | 96.21         | 26.08    | 76.3        | 91.35         |
| Lung Infections         | CT       |          | 32.54    | 60.01       | 78.51         | 25.84    | 58.89       | 78.98         |
| Pancreas                | CT       |          | 43.53    | 76.76       | 83.8          | 32.38    | 81.09       | 89.45         |
| Pleural Effusion        | CT       |          | 9.52     | 59.46       | 75.52         | 11.19    | 75.77       | 90.96         |
| Stomach                 | CT       |          | 68.95    | 82.66       | 92.54         | 35.49    | 70.93       | 87.74         |
| Head-Neck Tumor         | PET      |          | 72.45    | 81.17       | 84.12         | 38.57    | 62.76       | 67.78         |
| Average                 |          |          | 58.52    | 81.02       | 87.05         | 37.49    | 76.54       | 85.31         |

<span id="page-48-0"></span>**Table 1.** Performance comparison between our method, MedSAM, and SAM on 21 3D medical image segmentation tasks evaluated by DSC and NSD. Our model achieves significant and consistent improvements across all the tasks. Best results are in bold.

key and value. The features generated by deformable attention are respectively represented as  $F_d^c$  and  $F_s^c$ , which are fed to a layer normalization layer and a feed-forward layer to generate the output features  $F_d^1$  and  $F_s^1$ . The feed-forward layer contains two MLP layers and a GeLU layer. In addition, we add the residual connection in both deformable attention and feed forward layers.

### 2.3 ViT-Conv Fusion Block

We design a fusion module with an automatic selective mechanism to integrate the diverse information from ViT branch and convolution branch. Figure  $1(d)$  $1(d)$ gives the architecture. The feature from a given branch, denoted as  $F_d$  or  $F_s$ , is initially processed through a channel attention layer. This layer comprises a fully connected squeeze layer, a GELU activation layer, and a fully connected restore layer. This process yields the logits  $\Lambda_d$  or  $\Lambda_s$ . The output logits from each branch's channel attention layer are then combined, leading to the creation of an element-wise selective mask. This mask is derived by applying the sigmoid to the summed features as  $\mathcal{M} = \text{Sigmoid}(\Lambda_d + \Lambda_s)$ . The final fusion output, representing an amalgamation of both branches, is computed as follows:

$$
F_{\text{output}} = F_d^o \otimes \mathcal{M} + F_s^o \otimes (1 - \mathcal{M}), \tag{2}
$$

<span id="page-49-0"></span>**Table 2.** Performance comparison between our method and MedSAM as well as SAM on 9 2D medical image segmentation tasks evaluated by Normalized Surface Distance. Our model achieves significant and consistent improvements across all the tasks. Best results are in bold.

| Segmentation Task Modality |                                 | $DSC(\%)$ |       |       | $NSD(\%)$ |       |                                                                                |  |
|----------------------------|---------------------------------|-----------|-------|-------|-----------|-------|--------------------------------------------------------------------------------|--|
|                            |                                 |           |       |       |           |       | $\text{SAM}$ [18] MedSAM [22] DB-SAM (Ours) SAM [18] MedSAM [22] DB-SAM (Ours) |  |
| Breast Tumor               | Ultrasound                      | 78.01     | 85.42 | 87.43 | 82.48     | 89.02 | 92.30                                                                          |  |
| Liver                      | Ultrasound                      | 67.81     | 74.36 | 81.10 | 72.07     | 79.07 | 92.67                                                                          |  |
| Vessel                     | Ultrasound                      | 57.6      | 70.88 | 72.15 | 65.1      | 78.41 | 86.45                                                                          |  |
| Heart                      | X-Ray                           | 79.28     | 91.19 | 95.35 | 83.85     | 94.1  | 95.65                                                                          |  |
| Lungs                      | X-Ray                           | 72.24     | 96.57 | 97.60 | 75.45     | 98.56 | 99.30                                                                          |  |
| Polyp                      | Endoscope                       | 81.6      | 86.9  | 92.13 | 85.93     | 90.91 | 96.28                                                                          |  |
| Instrument                 | Endoscope                       | 76.61     | 86.37 | 91.89 | 82.36     | 90.93 | 97.33                                                                          |  |
| Retinal Vessel             | Retinal Image <sup>[0.75]</sup> |           | 66.1  | 67.53 | 3.45      | 84.4  | 91.36                                                                          |  |
| Gland                      | Pathology                       | 22.63     | 37.23 | 52.79 | 27.75     | 43.09 | 74.95                                                                          |  |
| Average                    |                                 | 59.62     | 77.22 | 82.00 | 64.27     | 83.17 | 91.81                                                                          |  |

where  $F_d^o$  and  $F_s^o$  represent the features from the ViT and convolution branches, respectively, while ⊗ signifies element-wise multiplication. Each token in the final output feature map learns both the global context information from the ViT branch and the local spatial information from the convolution branch. This is achieved by adaptive fusion between the two branches, ensuring a comprehensive representation of the features.

## 3 Experiments

### 3.1 Implementation and Evaluation Metrics

**Dataset.** Our proposed method utilizes the extensive and heterogeneous largescale dataset collected by MedSAM [\[22](#page-53-1)], adhering to their established split criteria for training and testing. The large-scale dataset encompasses 30 distinct segmentation tasks collected from public datasets across various medical devices. Specifically, the segmentation tasks of brain ventricle, brain tumor, cerebellum, gallbladder, and the left and right ventricles of the heart are generated from various MR sequences such as T1, T2, ADC, and FLAIR [\[4](#page-52-3),[5,](#page-52-4)[7](#page-52-5)[,12](#page-53-3)[,16,](#page-53-4)[17](#page-53-5)[,21](#page-53-6)[,27](#page-53-7),[28\]](#page-54-3). The segmentation tasks of abdominal tumors, COVID-19 related infections, gallbladder, head and neck tumors, liver, pancreas, pleural effusion, and stomach are generated from CT scans [\[3](#page-52-6),[6,](#page-52-7)[11,](#page-53-8)[14](#page-53-9)[,19,](#page-53-10)[20](#page-53-11)[,23](#page-53-12)[–25](#page-53-13)[,28](#page-54-3)]. The segmentation tasks of heart and lungs are from X-Ray images [\[26](#page-53-14)]. The segmentation tasks of polyps and instruments are from endoscopic images [\[2](#page-52-8),[13\]](#page-53-15). The segmentation task of vessels is from retinal images [\[1](#page-52-9)]. The segmentation task of colon glands is from pathology images [\[29,](#page-54-4)[30\]](#page-54-5). The images of all segmentation tasks are normalized to the range of [0, 255] and resized to the fixed resolution of  $256 \times 256 \times 3$ . For 3D images, such as those from CT and MR scans, the images are split into 2D slices along axial plane. Finally, the training set contains 161,857 images, while the test set comprises of 52,506 images.

Image /page/50/Figure/1 description: This image displays a comparison of segmentation results from different models, including GT (Ground Truth), SAM, MedSAM, and Ours. The comparison is presented across multiple medical imaging modalities, likely CT and MRI scans, as well as histological and endoscopic views. Each row represents a different model's output, and each column shows a different view or slice of the medical data. The segmented regions, highlighted in yellow, appear to represent tumors or lesions. The blue bounding boxes indicate the areas of interest for segmentation. The image effectively visualizes the performance and accuracy of the 'Ours' model against established methods like SAM and MedSAM, with GT serving as the benchmark.

<span id="page-50-0"></span>**Fig. 2.** Visualization examples of the pre-trained SAM, MedSAM, our model and GT on different 3D and 2D tasks. Our DB-SAM model achieves more accurate segmentation than the SAM and MedSAM, especially in scenarios involving small organs and organs with complex shapes. Best viewed zoomed in. Additional results are presented in the supplementary material.

**Training Settings.** We leverage a pre-trained SAM with ViT-B and integrate our proposed adapter modules. Both the ViT encoder and the prompt encoder are kept frozen during training. Similar to MedSAM [\[22\]](#page-53-1), the mask decoder is trainable. During training, to closely replicate a real-world clinical setting where a doctor manually marks a target area, we generated bounding box prompts from ground-truth masks with a random perturbation ranging from 0 to 20 pixels. This approach is designed to reflect the variability and imprecision inherent in human-drawn bounding boxes in medical imaging. We train our method using 4 Nvidia A100 GPUs, and adopt the AdamW optimizer with a polynomial learning rate. There are totally 12 epochs with initial learning rate of 1e-4. The adapter module is regulated by dropout and drop path with the rates of 0.4. We employ the sum of cross-entropy loss and dice loss to supervise mask learning.

**Evaluation Metrics.** Similar to MedSAM, the performance is evaluated using the standard Dice Similarity Coefficient (DSC) and Normalized Surface Distance (NSD) with 1 mm tolerance, a widely recognized and reliable metrics for segmentation efficacy.

### 3.2 Comparison with SAM and MedSAM

Here we compare our DB-SAM with SAM [\[18](#page-53-0)] and MedSAM [\[22](#page-53-1)] on both 3D and 2D tasks in Table [1](#page-48-0) and Table [2.](#page-49-0) Our DB-SAM significantly outperforms both the original SAM and MedSAM across all 30 segmentation tasks. Compared to

| Channel attention | Bilateral cross attention | Final fusion | 3D Average   |              | 2D Average   |              |
|-------------------|---------------------------|--------------|--------------|--------------|--------------|--------------|
|                   |                           |              | Dice         | NSD          | Dice         | NSD          |
|                   |                           |              | 81.02        | 76.54        | 77.22        | 83.17        |
| ✓                 |                           |              | 85.25        | 82.25        | 78.26        | 89.02        |
| ✓                 | ✓                         |              | 86.60        | 85.01        | 79.55        | 90.06        |
| ✓                 |                           | ✓            | <b>87.05</b> | <b>85.31</b> | <b>82.00</b> | <b>91.81</b> |

<span id="page-51-0"></span>**Table 3.** Ablation study of integrating different modules, including channel attention block, bilateral cross attention block, and ViT-Conv fusion block.

SAM on different 3D medical segmentation tasks, our DB-SAM achieves 28.53% improvement in terms of average DSC and 47.82% in terms of average NSD. Compared to MedSAM, our DB-SAM obtains 6.03% improvement in terms of average DSC and 8.77% in terms of average NSD. Further, we observe that our DB-SAM particularly excels in complex 3D tasks involving abdomen tumors, liver, lungs, pleural effusion, and stomach. For instance, on abdomen tumor segmentation, our DB-SAM obtains a DSC of 78.31%, whereas MedSAM provides a DSC of 65.54%. This leads to an absolute improvement of 12.77%, likely due to the ability of DB-SAM to capture rich local information and adapt to varying scales through its convolution branch and deformable attention operation. As a result, DB-SAM effectively addresses challenges posed by small, discrete, and irregularly shaped objects. Table [2](#page-49-0) shows results on different 2D tasks. In terms of DSC, SAM obtains 59.62%, MedSAM achieves 77.22%, whereas our DB-SAM obtains significantly improved performance of 82.00%. Similarly, our Db-SAM outperforms SAM and MedSAM by 27.54% and 8.64% in terms of NSD.

Figure [2](#page-50-0) presents a qualitative comparison of pre-trained SAM, MedSAM, and our DB-SAM with respect to ground-truth (GT). Compared to SAM and MedSAM, our proposed method can generate high-quality results. For instance, the first-row example highlights our proposed method has a superior performance in segmenting small objects with complicated shapes. Specifically, the pre-trained SAM could identify a small portion of the object, MedSAM is able to recognize some more parts. In contrast, our proposed method can accurately predict the masks of the small object. The third-row example shows that our proposed method is able to segment very small object accurately.

### 3.3 Ablation Study

**Efficacy of Proposed Modules** . We conduct an ablation study on impact of three different experiments in Table [3.](#page-51-0) The baseline method is MedSAM, which only fine-tunes mask decoder during training. MedSAM has an averaged DSC score of 81.02% and an averaged NSD score of 76.54% on 3D tasks. When we only integrate the channel attention mechanism into the ViT branch, it has an averaged DSC score of 85.25% and an averaged NSD score of 82.25% on 3D tasks. It outperforms the baseline by 4.21% in terms of DSC and 5.71% in terms of

NSD. When further integrating convolution branch, it has an averaged DSC score of 86.60% and an averaged NSD score of 85.01% on 3D tasks. It outperforms the baseline by 5.58% in terms of DSC and 8.47% in terms of NSD. Finally, when fusing the output features of two branches, it has an averaged DSC score of 87.05% and an averaged NSD score of 85.31% on 3D tasks, which outperforms the baseline by 6.01% and 8.77%. Similarly, we observe that integrating different modules can improve the performance on 2D tasks.

# 4 Conclusion

We introduce a dual-branch method, DB-SAM, to adapt SAM for universal medical image segmentation tasks. Our proposed DB-SAM replaces the original image encoder of SAM with a dual-branch image encoder. The dual-branch image encoder comprises a ViT branch and a convolution branch, which are respectively used to extract high-and low-level domain-specific features. Experiments on large-scale medical image segmentation dataset demonstrate the effectiveness of proposed method, leading to superior segmentation performance.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-52-9"></span>1. Drive: Digital retinal images for vessel extraction (2023), [https://drive.grand](https://drive.grand-challenge.org)[challenge.org](https://drive.grand-challenge.org)
- <span id="page-52-8"></span>2. Ali, S., Jha, D., Ghatwary, N., et al.: A multi-centre polyp detection and segmentation dataset for generalisability assessment. Scientific Data **10**(1), 75 (2023)
- <span id="page-52-6"></span>3. Andrearczyk, V., Oreiller, V., Jreige, M., et al.: Overview of the hecktor challenge at miccai 2020: Automatic head and neck tumor segmentation in pet/ct. In: Head and Neck Tumor Segmentation. pp. 1–21 (2021)
- <span id="page-52-3"></span>4. Bakas, S., Reyes, M., Jakab, A., et al.: Identifying the best machine learning algorithms for brain tumor segmentation, progression assessment, and overall survival prediction in the brats challenge. [arXiv:1811.02629](http://arxiv.org/abs/1811.02629) (2018)
- <span id="page-52-4"></span>5. Bernard, O., Lalande, A., Zotti, C., et al.: Deep learning techniques for automatic mri cardiac multi-structures segmentation and diagnosis: is the problem solved? IEEE Transactions on Medical Imaging **37**(11), 2514–2525 (2018)
- <span id="page-52-7"></span>6. Bilic, P., Christ, P., Li, H.B., et al.: The liver tumor segmentation benchmark (lits). Medical Image Analysis **84**, 102680 (2023)
- <span id="page-52-5"></span>7. Bloch, N., Madabhushi, A., Huisman, H., et al.: Nci-isbi 2013 challenge: automated segmentation of prostate structures (2015), <https://wiki.cancerimagingarchive.net>
- <span id="page-52-0"></span>8. Cao, H., Wang, Y., Chen, J., et al.: Swin-unet: Unet-like pure transformer for medical image segmentation. In: ECCVW (2022)
- <span id="page-52-1"></span>9. Chen, J., Lu, Y., Yu, Q., et al.: Transunet: Transformers make strong encoders for medical image segmentation. [arXiv:2102.04306](http://arxiv.org/abs/2102.04306) (2021)
- <span id="page-52-2"></span>10. Chen, Z., Duan, Y., Wang, W., et al.: Vision transformer adapter for dense predictions. [arXiv:2205.08534](http://arxiv.org/abs/2205.08534) (2022)

- <span id="page-53-8"></span>11. Clark, K., Vendt, B., Smith, K., et al.: The cancer imaging archive (tcia): maintaining and operating a public information repository. Journal of Digital Imaging **26**(6), 1045–1057 (2013)
- <span id="page-53-3"></span>12. Full, P.M., Isensee, F., Jäger, P.F., Maier-Hein, K.: Studying robustness of semantic segmentation under domain shift in cardiac mri. In: Statistical Atlases and Computational Models of the Heart. M&Ms and EMIDEC Challenges (2021)
- <span id="page-53-15"></span>13. Garcia-Peraza-Herrera, L.C., Fidon, L., D'Ettorre, C., et al.: Image compositing for segmentation of surgical tools without manual annotations. IEEE Transactions on Medical Imaging **40**(5), 1450–1460 (2021)
- <span id="page-53-9"></span>14. Heller, N., Isensee, F., Maier-Hein, K.H., et al.: The state of the art in kidney and kidney tumor segmentation in contrast-enhanced ct imaging: Results of the kits19 challenge. Medical Image Analysis **67**, 101821 (2021)
- <span id="page-53-2"></span>15. Hu, J., Shen, L., Sun, G.: Squeeze-and-excitation networks. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 7132–7141 (2018)
- <span id="page-53-4"></span>16. Ji, Y., Bai, H., Yang, J., et al.: Amos: A large-scale abdominal multi-organ benchmark for versatile medical image segmentation. [arXiv:2206.08023](http://arxiv.org/abs/2206.08023) (2022)
- <span id="page-53-5"></span>17. Jia, H., Cai, W., Huang, H., Xia, Y.: H2nf-net for brain tumor segmentation using multimodal mr imaging: 2nd place solution to brats challenge 2020 segmentation task. In: Brainlesion: Glioma, Multiple Sclerosis, Stroke and Traumatic Brain Injuries. pp. 58–68. Springer International Publishing, Cham (2021)
- <span id="page-53-0"></span>18. Kirillov, A., Mintun, E., Ravi, N., et al.: Segment anything. [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-53-10"></span>19. Kiser, K.J., Barman, A., Stieb, S., et al.: Novel autosegmentation spatial similarity metrics capture the time required to correct segmentations better than traditional metrics in a thoracic cavity segmentation workflow. Journal of Digital Imaging **34**, 541–553 (2021)
- <span id="page-53-11"></span>20. Kiser, K., Ahmed, S., Stieb, S., et al.: Data from the thoracic volume and pleural effusion segmentations in diseased lungs for benchmarking chest ct processing pipelines. The Cancer Imaging Archive (2020)
- <span id="page-53-6"></span>21. Litjens, G., Toth, R., van de Ven, W., et al.: Evaluation of prostate segmentation algorithms for mri: the promise12 challenge. Medical Image Analysis **18**(2), 359– 373 (2014)
- <span id="page-53-1"></span>22. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nature Communications **15**(1), 654 (2024)
- <span id="page-53-12"></span>23. Ma, J., Wang, Y., An, X., et al.: Towards data-efficient learning: A benchmark for covid-19 ct lung and infection segmentation. Medical Physics **48**(3), 1197–1210 (2021)
- 24. Ma, J., Zhang, Y., Gu, S., et al.: Abdomenct-1k: Is abdominal organ segmentation a solved problem? IEEE Transactions on Pattern Analysis and Machine Intelligence **44**(10), 6695–6714 (2022)
- <span id="page-53-13"></span>25. Ma, J., Zhang, Y., Gu, S., et al.: Fast and low-gpu-memory abdomen ct organ segmentation: The flare challenge. Medical Image Analysis **82**, 102616 (2022)
- <span id="page-53-14"></span>26. Shiraishi, J., Katsuragawa, S., Ikezoe, J., et al.: Development of a digital image database for chest radiographs with and without a lung nodule: receiver operating characteristic analysis of radiologists' detection of pulmonary nodules. American Journal of Roentgenology **174**(1), 71–74 (2000)
- <span id="page-53-7"></span>27. Shusharina, N., Bortfeld, T., Cardenas, C., Yang, J.: Anatomical brain barriers to cancer spread: Segmentation from ct and mr images (2020), [https://doi.org/10.](https://doi.org/10.5281/zenodo.3746561) [5281/zenodo.3746561](https://doi.org/10.5281/zenodo.3746561)

- <span id="page-54-3"></span>28. Simpson, A.L., Antonelli, M., Bakas, S., et al.: A large annotated medical image dataset for the development and evaluation of segmentation algorithms. [arXiv:1902.09063](http://arxiv.org/abs/1902.09063) (2019)
- <span id="page-54-4"></span>29. Sirinukunwattana, K., Pluim, J.P., Chen, H., et al.: Gland segmentation in colon histology images: The glas challenge contest. Medical Image Analysis **35**, 489–502 (2017)
- <span id="page-54-5"></span>30. Sirinukunwattana, K., Snead, D.R., Rajpoot, N.M.: A stochastic polygons model for glandular structures in colon histology images. IEEE Transactions on Medical Imaging **34**(11), 2366–2378 (2015)
- <span id="page-54-1"></span>31. Tan, M., Le, Q.: Efficientnetv2: Smaller models and faster training. In: International conference on machine learning. pp. 10096–10106 (2021)
- <span id="page-54-0"></span>32. Zhou, Z., Rahman Siddiquee, M.M., et al.: Unet++: A nested u-net architecture for medical image segmentation. [arXiv:1807.10165](http://arxiv.org/abs/1807.10165) (2018)
- <span id="page-54-2"></span>33. Zhu, X., Su, W., Lu, L., et al.: Deformable detr: Deformable transformers for endto-end object detection. [arXiv:2010.04159](http://arxiv.org/abs/2010.04159) (2020)

Image /page/55/Picture/0 description: A square button with a rounded border contains a circular icon with a bookmark symbol inside. Below the icon, the text "Check for updates" is displayed in a light gray font.

# **DeSAM: Decoupled Segment Anything Model for Generalizable Medical Image Segmentation**

Yifan Gao<sup>1,2</sup>, Wei Xia<sup>2</sup>, Dingdu Hu<sup>1,2</sup>, Wenkui Wang<sup>3</sup>, and Xin Gao<sup>2( $\boxtimes$ )</sup>

<sup>1</sup> School of Biomedical Engineering (Suzhou), Division of Life Science and Medicine, University of Science and Technology of China, Hefei, China

{yifangao,hudingdu}@mail.ustc.edu.cn

*{*yifangao,hudingdu*}*@mail.ustc.edu.cn <sup>2</sup> Suzhou Institute of Biomedical Engineering and Technology, Chinese Academy of Sciences, Suzhou, China

<EMAIL>, <EMAIL>

<sup>3</sup> State Key Laboratory of Ultra-precision Machining Technology, Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University,

Kowloon, Hong Kong SAR, People's Republic of China

<EMAIL>

**Abstract.** Deep learning-based medical image segmentation models often suffer from domain shift, where the models trained on a source domain do not generalize well to other unseen domains. As a promptdriven foundation model with powerful generalization capabilities, the Segment Anything Model (SAM) shows potential for improving the crossdomain robustness of medical image segmentation. However, SAM performs significantly worse in automatic segmentation scenarios than when manually prompted, hindering its direct application to domain generalization. Upon further investigation, we discovered that the degradation in performance was related to the coupling effect of inevitable poor prompts and mask generation. To address the coupling effect, we propose the Decoupled SAM (DeSAM). DeSAM modifies SAM's mask decoder by introducing two new modules: a prompt-relevant IoU module (PRIM) and a prompt-decoupled mask module (PDMM). PRIM predicts the IoU score and generates mask embeddings, while PDMM extracts multi-scale features from the intermediate layers of the image encoder and fuses them with the mask embeddings from PRIM to generate the final segmentation mask. This decoupled design allows DeSAM to leverage the pretrained weights while minimizing the performance degradation caused by poor prompts. We conducted experiments on publicly available crosssite prostate and cross-modality abdominal image segmentation datasets. The results show that our DeSAM leads to a substantial performance improvement over previous state-of-the-art domain generalization methods. The code is publicly available at [https://github.com/yifangao112/](https://github.com/yifangao112/DeSAM) [DeSAM.](https://github.com/yifangao112/DeSAM)

**Keywords:** Segment Anything Model *·* Medical Image Segmentation *·* Single-source Domain Generalization

## 1 Introduction

Deep learning models achieve remarkable performance in medical image segmentation when trained and evaluated on data from the same domain [\[1\]](#page-63-0). However, the generalizability of deep models may be poor to unseen out-of-domain data, which prevents the use of models in clinical settings. To mitigate the performance degradation caused by domain shifting, previous attempts focus on unsupervised domain adaptation [\[2\]](#page-63-1) and multi-source domain generalization [\[3\]](#page-63-2). However, unsupervised domain adaptation and multi-source domain generalization rely on training data from the target domain or from multiple source domains. Such requirements may not hold due to cost and privacy issues, making it difficult for real-world clinical applications.

A more practical but challenging method is single-source domain generalization: using training data from only one source domain to train deep learning models robust to unseen data. The main solutions include input space-based and feature-based data augmentation [\[4\]](#page-63-3) Yet there are some limitations with solutions: input space-based augmentation requires expertise to design the augmentation function, and feature-based augmentation usually requires complex adversarial training [\[5](#page-63-4)].

Compared to the above approaches, directly migrating models based on large datasets to medical image segmentation to improve generalization is an attractive approach. Some early work utilized pre-trained models on natural or medical images and achieved good performance  $[6,7]$  $[6,7]$ . However, due to the small capacity of the pre-trained models, the cross-domain generalizability of the deep models was not effectively improved.

Recently, vision foundation models have made great progress in image segmentation [\[8](#page-64-0),[9\]](#page-64-1). Segment Anything Model (SAM) [\[8\]](#page-64-0), trained on more than 1 billion masks, has achieved unprecedented generalization capabilities on a variety of natural images. Some work shows that adapting SAM to medical image segmentation also shows satisfactory results [\[10](#page-64-2)[–13](#page-64-3)]. These advances demonstrate the promise of training a powerful segmentation model with generalizability using pre-trained foundation models.

However, the prompt-driven SAM struggles with automatic segmentation without specific prompts, which hinders its application to domain generalization. Workarounds like using grid points or full-image bounding boxes as prompts can enable automatic segmentation but with significantly reduced performance compared to providing explicit prompts. We argue that the poor performance of fully automated SAM in medical image segmentation can be attributed to a mechanism, namely the coupling effect: image embeddings and prompt tokens interacted in the cross-attention transformer layer of the SAM mask decoder, which makes the final output mask highly dependent on the prompt. Therefore, even after finetuning, the model still tends to be more sensitive to wrong prompts (i.e., points not in the mask or the boxes significantly larger than the mask).

To address this issue, we propose Decoupled Segment Anything Model (DeSAM) in this work, a novel architecture for fully automated medical image segmentation based on SAM. We decouple the mask decoder of SAM into two

subtasks: 1) prompt-relevant IoU regression, 2) prompt-decoupled mask learning. Specifically, we design two new modules and add to the fully automated SAM. The first one is the prompt-relevant IoU module (PRIM), which predicts IoU scores based on given prompt and generates mask embeddings. The second one is the prompt-decoupled mask module (PDMM), which fuses the image embeddings from the image encoder with the mask embeddings from PRIM to generate the mask. DeSAM minimizes the performance degradation caused by wrong prompts in the automatic mode. Extensive experiments on two public datasets show that the DeSAM improves the robustness of fully automated prostate segmentation against distribution variations across different sites.

### 2 Related Work

**Single-Source Domain Generalization.** Given training data from only one domain and generalizing to an unseen domain, single-source domain generalization is more challenging since there is less diversity in training domains. Chen et al. [\[14\]](#page-64-4) augment data with the random bias field, the common image artifact in clinical MR images. RandConv [\[15\]](#page-64-5) uses random convolutions for data augmentation. MixStyle adopts a combination of the style information of randomlyselected instances of different domains. Maxstyle [\[5](#page-63-4)] expands the domain space with additional noise and the worst-case composition. Ouyang et al. [\[16](#page-64-6)] propose a simple causality-inspired data augmentation method that greatly improves the cross-domain robustness of deep models. Unlike previous methods, DeSAM enhances the generalization ability of deep models without a complex pipeline for data augmentation, making it more competitive in practical applications.

**Segment Anything Model.** The remarkable extension ability of Transformer makes it possible to construct large-scale models with billions of parameters. SAM was the first proposed foundation model for image segmentation and has been applied to various computer vision applications [\[17](#page-64-7),[18\]](#page-64-8). Intrigued by its unprecedented performance in natural image segmentation, considerable efforts have been made on its extended applications in medical image [\[19](#page-64-9)]. In particular, some work attempts to adapt SAM to medical image segmentation, including fine-tuning mask decoder and image encoder  $[10,12,13]$  $[10,12,13]$  $[10,12,13]$  $[10,12,13]$ . However, these approaches mainly focus on adaptating SAM for specific medical tasks, and may not fully address the challenges in automatic segmentation scenarios. Our proposed DeSAM tackles the performance degradation in automatic segmentation through a novel decoupled architecture. Unlike previous methods, DeSAM introduces prompt-relevant IoU module (PRIM) and prompt-decoupled mask module (PDMM) to leverage generalizable knowledge while minimizing sensitivity to poor prompts. Furthermore, DeSAM's multi-scale feature fusion enhances context capture, which is crucial for medical image segmentation.

Image /page/58/Figure/1 description: This is a diagram illustrating a segmentation model. The model takes a medical image and a prompt input. The image is processed by a SAM Image Encoder, which outputs features at different resolutions: (256, 64, 64), (64, 256, 256), and (128, 128, 128). These features are fed into a Prompt-Decoupled Mask Module (PDMM). The prompt input is processed by a SAM Prompt Encoder. The PDMM combines features from the image encoder and the prompt encoder through a series of operations, including element-wise addition (indicated by 'c'), and uses SRB (presumably a residual block) and SRB + Upsampling blocks to generate a segmentation mask. A separate module, the Prompt-Relevant IoU Module (PRIM), takes the output of the Cross Attention Transformer and the Prediction Head to produce an IoU Output. The diagram also shows the dimensions of the feature maps at various stages, such as (256, 64, 64), (64, 256, 256), and (128, 128, 128).

<span id="page-58-0"></span>**Fig. 1.** Overview of the proposed DeSAM. The DeSAM consists of the image and prompt encoders of SAM, a prompt-decoupled mask module (PDMM), and a promptrelevant IoU module (PRIM). The image encoder are used to compute the image embeddings before training. The prompt encoder is frozen during training. The PRIM consists of a cross-attention transformer and an IoU prediction head, and it utilizes the image and prompt embeddings to generate mask embeddings and IoU score. The PDMM contains multiple channel attention-based residual blocks (SRB) and upsampling operations, and it integrates the mask embeddings and image embeddings to generate the mask.

# 3 Decoupled Segment Anything Model

### 3.1 Architecture

The overview of DeSAM is illustrated in Fig. [1.](#page-58-0) In addition to the encoder inherited from SAM, DeSAM contains two main components, the prompt-relevant IoU module (PRIM) and the prompt-decoupled mask module (PDMM). These components are described in detail in this section.

**Prompt-Relevant IoU Module (PRIM).** The PRIM has a similar structure to the mask decoder of SAM, which includes a cross-attention transformer layer and an IoU prediction head. However, to decouple the prompt and the output mask, we only discard the mask prediction head and extract mask embeddings from the cross-attention transformer layer.

**Prompt-Decoupled Mask Module (PDMM).** PDMM is a crucial component of the proposed DeSAM architecture, designed to generate the final segmentation mask by fusing multi-scale image embeddings with prompt-relevant mask embeddings. Inspired by the success of U-Net [\[20\]](#page-64-11) and UNETR [\[21\]](#page-64-12) in medical image segmentation, we adopt a similar encoder-decoder structure for PDMM.

The first step in PDMM is to extract multi-scale image embeddings from the SAM ViT-H image encoder. Specifically, we select the output features from the global attention layers  $i = (8, 16, 24)$ , which have a spatial resolution of 1280*×*64*×*64. These intermediate features capture hierarchical representations of the input image at different scales, providing rich contextual information for the subsequent mask-generation process.

To efficiently process and refine the extracted image embeddings, we employ a series of squeeze-and-excitation (SE) residual blocks [\[22](#page-64-13)], followed by upsampling operations. The number of SE residual blocks applied to each image embedding varies depending on its spatial resolution. The refined image embeddings are then upsampled to match the resolution of the final segmentation mask. To further improve the information flow and gradient propagation within PDMM, we introduce skip connections that fuse the upsampled image embeddings from different scales. This fusion strategy allows the network to combine low-level spatial details with high-level semantic information, enabling more precise and detailed segmentation masks.

Finally, we merge the mask embeddings generated by the PRIM with the bottleneck embeddings of PDMM. This fusion serves two essential purposes. First, it allows PDMM to leverage the pre-trained weights of SAM's mask decoder, which encodes valuable knowledge about object shapes and boundaries. Second, it ensures a smooth gradient flow between PRIM and PDMM during training, facilitating effective end-to-end optimization of the entire DeSAM architecture.

By decoupling the mask generation process from the prompt embeddings and introducing a dedicated module for multi-scale image feature fusion, PDMM significantly enhances the ability of DeSAM to generate accurate and robust segmentation masks in a domain-agnostic manner.

### 3.2 Training Strategies

During training, we load the pre-trained weights of SAM ViT-H, freeze the image and prompt encoders, and fine-tune the layers within PDMM and PRIM. Since the automatic segmentation includes the grid points mode and the whole box mode, we adopt two different strategies to train the proposed model. In the grid points mode, we randomly generate points within and outside the ground truth mask in a 1:1 ratio. Mask generation is supervised by dice loss L*dice* and crossentropy loss L*ce*. IoU is supervised by mean square error loss L*mse*. The total loss is:

$$
L_{points} = \lambda_1 L_{dice} + \lambda_2 L_{ce} + \lambda_3 L_{mse}
$$
\n<sup>(1)</sup>

The loss weight  $\lambda_1$ ,  $\lambda_2$ ,  $\lambda_3$  are 1, 1, and 10. In the whole box mode, since the ground truth mask must be inside the box, we only supervise the mask generation:

$$
L_{box} = L_{dice} + L_{ce}
$$
\n<sup>(2)</sup>

Image /page/60/Figure/1 description: The image displays a series of four diagrams, labeled (a), (b), (c), and (d), illustrating a progression of a neural network architecture. Diagram (a) shows a single yellow block connected to a dashed arrow labeled 'mask'. Diagram (b) depicts the output of (a) feeding into a yellow block and a 'Cross Attention Transformer' module, with the yellow block's output connected to a circle labeled 'c' and then to a dashed arrow labeled 'mask'. The 'Cross Attention Transformer' module also receives an input and its output is connected to the 'c' module. Diagram (c) is similar to (b), but it adds an 'IoU Prediction Head' module that receives input from the 'Cross Attention Transformer' and outputs 'IoU'. Diagram (d) shows a further iteration, with the 'Cross Attention Transformer' and 'IoU Prediction Head' modules receiving inputs, and the 'Cross Attention Transformer' output feeding into the 'c' module, which then outputs 'mask'. The 'IoU Prediction Head' also receives an input and outputs 'IoU'.

<span id="page-60-0"></span>**Fig. 2.** Design choices of the decoder. (a) Generating a mask by directly using the image embedding from the encoder (PDMM only). (b) PDMM and PRIM without IoU prediction head. (c) PDMM and PRIM without mask embedding fusion. (d) Our proposed DeSAM.

<span id="page-60-1"></span>**Table 1.** Quantitative results of our ablation experiments. The best performance is indicated by bolded fonts. IPH: IoU prediction head. MEF: mask embedding fusion.

| Methods      |        |        |        |        |              | Dice (%)     |              |              |              |              |              |
|--------------|--------|--------|--------|--------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
| Settings     | PDMM   | PRIM   | IPH    | MEF    | A            | B            | C            | D            | E            | F            | Overall      |
| 1            | $	ick$ |        |        |        | 79.09        | 74.96        | 54.19        | 80.11        | 77.22        | 77.53        | 73.85        |
| 2            | $	ick$ | $	ick$ |        | $	ick$ | 76.74        | 76.61        | 58.94        | 80.45        | 78.39        | 79.56        | 75.12        |
| 3            | $	ick$ | $	ick$ | $	ick$ |        | 79.52        | 78.37        | 59.59        | 82.65        | 80.16        | 74.58        | 75.81        |
| 4 (proposed) | $	ick$ | $	ick$ | $	ick$ | $	ick$ | <b>82.80</b> | <b>80.61</b> | <b>64.77</b> | <b>83.41</b> | <b>80.36</b> | <b>82.17</b> | <b>79.02</b> |

## 4 Results and Discussion

### 4.1 Dataset and Implementation Details

To evaluate the performance of the proposed DeSAM, we conducted assessments in two cross-domain settings: 1) cross-modality abdominal multi-organ segmentation and 2) cross-site prostate segmentation. For cross-modality abdominal segmentation, we used two publicly available datasets  $[23, 24]$  $[23, 24]$ . For the multi-site prostate segmentation dataset, we collected three publicly available datasets from six different clinical sites, including NCI-ISBI-2013 [\[25\]](#page-65-2), I2CVB [\[26\]](#page-65-3), and PROMISE12 [\[27\]](#page-65-4). We adopted the same preprocessing method as MaxStyle [\[5\]](#page-63-4). In each experiment, we split the data from one domain into a training dataset and an in-domain validation set in a 9:1 ratio. Then, we tested the robustness of the other domains using the best model on the in-domain validation set. We use the dice score as the evaluation metric to measure the quality of the predicted masks.

The image embeddings were precomputed before training using the ViT-H model as image encoder. We set the number of points for the grid points mode to  $9 \times 9$ . For network optimization, we used a learning rate of 1e-4 with a batch size of 8 and applied learning rate decay. The network was trained for 50 epochs for the prostate dataset to ensure convergence. We performed the experiments on a single RTX 3060 12GB. During training, the video memory usage was approximately 7.8 GB. We conducted the system-level comparison of our method with the upper bound (fully supervised on the seen domain), baseline

| Method           | Abdominal    |              | Prostate     |              |              |              |              |              |              |  |
|------------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--|
|                  | CT           | MRI          | A            | B            | C            | D            | E            | F            | Overall      |  |
| Upper bound [28] | 91.89        | 88.78        | 85.38        | 83.68        | 82.15        | 85.21        | 87.04        | 84.29        | 84.63        |  |
| Baseline [28]    | 70.13        | 66.36        | 63.73        | 61.21        | 27.41        | 34.36        | 44.10        | 61.70        | 48.75        |  |
| AdvBias [14]     | 75.04        | 74.20        | 77.45        | 62.12        | 51.09        | 70.20        | 51.12        | 50.69        | 60.45        |  |
| RandConv [15]    | 78.92        | 73.41        | 75.52        | 57.23        | 44.21        | 61.27        | 49.98        | 54.21        | 57.07        |  |
| MaxStyle [5]     | 82.92        | 76.93        | 81.25        | 70.27        | 62.09        | 58.18        | 70.04        | 67.77        | 68.27        |  |
| CSDG [16]        | 83.57        | 77.54        | 80.72        | 68.00        | 59.78        | 72.40        | 68.67        | 70.78        | 70.06        |  |
| MedSAM [10]      | 80.64        | 72.10        | 72.32        | 73.31        | 61.53        | 64.46        | 68.89        | 61.39        | 66.98        |  |
| SAMed [12]       | 77.21        | 70.35        | 73.61        | 75.89        | 58.61        | 73.91        | 66.52        | 72.85        | 70.23        |  |
| DeSAM-B          | 84.87        | 79.57        | 82.30        | 78.06        | 66.65        | 82.87        | 77.58        | 79.05        | 77.75        |  |
| DeSAM-P          | <b>86.68</b> | <b>80.05</b> | <b>82.80</b> | <b>80.61</b> | <b>64.77</b> | <b>83.41</b> | <b>80.36</b> | <b>82.17</b> | <b>79.02</b> |  |

<span id="page-61-1"></span>**Table 2.** Quantitative comparison of our DeSAM and state-of-the-art single-source domain generalization methods. The best performance is indicated by bolded fonts, and the second-best results is underlined.

(no domain generalization), MedSAM [\[10\]](#page-64-2), SAMed [\[12\]](#page-64-10) and other state-of-theart single-source domain generalization methods, including 1) adversarial bias field (AdvBias)  $[14]$  $[14]$ ; 2) RandConv  $[15]$ ; 3) MaxStyle  $[5]$ ; and 4) causality-inspired domain generalization (CSDG) [\[16](#page-64-6)]. Upper bound and baseline experiments were implemented using nnU-Net [\[28](#page-65-5)], and all other comparison methods used their recommended settings. We use two variants of DeSAM: DeSAM-B, which uses a whole image bounding box as the prompt, and DeSAM-P, which employs a grid of points as the prompt.

Image /page/61/Figure/4 description: The image displays six line graphs, each representing the Dice score (%) on the y-axis against different centers labeled A through F on the x-axis. The x-axis for each graph is marked with numbers 1, 5, 9, 13, 15, and 19. For Center A, the Dice score starts at approximately 81.9% at x=1, increases to 82.5% at x=5, then to 82.8% at x=9, and remains constant at 82.9% for x=13, 15, and 19. For Center B, the score begins at about 76.4% at x=1, rises to 77.8% at x=5, then sharply increases to 80.3% at x=9, and stays at 80.4% for x=13, 15, and 19. Center C shows a score of around 62.3% at x=1, climbing to 64.5% at x=5, then to 64.8% at x=9, and leveling off at 64.9% for x=13, 15, and 19. Center D has a score of approximately 83.4% at x=1, which remains constant at 83.5% for x=5, 9, 13, 15, and 19. Center E shows a score of about 80.4% at x=1, staying constant at 80.5% for x=5, 9, 13, 15, and 19. Finally, Center F starts at roughly 79.3% at x=1, increases to 80.5% at x=5, then to 82.1% at x=9, and remains at 82.3% for x=13, 15, and 19.

<span id="page-61-0"></span>**Fig. 3.** Quantitative results of different number of points.

### 4.2 Ablation Studies

We performed a series of ablation experiments to validate the key design choices in our DeSAM on the prostate segmentation dataset. If not specified, the grid points mode with 9*×*9 points are used for prediction by default setting. For the ablation study, we compared our DeSAM with three variants: (a) Generating a mask by directly using the image embedding from the encoder (PDMM only). (b) PDMM and PRIM without IoU prediction head. (c) PDMM and PRIM without mask embedding fusion. Figure [2](#page-60-0) shows the difference between the three variants and DeSAM. The results in Table [1](#page-60-1) show that the performance of our model is improved by gradually adding these components.

To investigate the robustness of our method in the grid points mode, we experimented with different numbers of grid points. Figure [3](#page-61-0) reports the quantitative results. The results show that there is no degradation in the performance of the model as the number of grid points increases, indicating the effectiveness of our method in avoiding false positive masks affected by poor prompts in the grid points mode.

Image /page/62/Picture/3 description: This image displays a grid of medical scans, likely MRIs and CT scans, with different segmentation methods applied. The grid is organized into four rows labeled a), b), c), and d), and multiple columns representing different segmentation techniques: AdvBias, RandConv, MaxStyle, CSDG, MedSAM, SAMed, DeSAM-B, DeSAM-P, and GT (Ground Truth). Rows a) and b) show axial views of the prostate, with yellow segmentation highlighting a region of interest. Row c) shows axial views of the liver and surrounding organs, with the liver segmented in blue and another organ in yellow. Row d) shows axial views of the abdomen, with kidneys segmented in different colors: red, blue, green, and yellow, along with other abdominal organs.

**Fig. 4.** Visual comparison of different methods for cross-site prostate segmentation and cross-modality abdominal multi-organ segmentation. GT represents the ground truth.

<span id="page-62-0"></span>

### 4.3 Comparison with State-of-the-Art Methods

The experimental results on both abdominal and prostate datasets are presented in Table [2.](#page-61-1) DeSAM-P achieves the best overall performance with a Dice score of 79.02%, outperforming all other methods by a significant margin. Notably, DeSAM-P surpasses the previous state-of-the-art method, CSDG, by 8.96% on the overall Dice score. These results highlight the effectiveness of our decoupling strategy in improving the generalization ability of SAM-based models for medical image segmentation. On the abdominal dataset, DeSAM-P and DeSAM-B consistently outperform other methods on both CT and MRI modalities. DeSAM-P achieves Dice scores of 86.68% and 80.05% on CT and MRI, respectively, setting new state-of-the-art performance levels. Figure [4](#page-62-0) shows a visual comparison between the different methods of prostate segmentation. In contrast, our DeSAM

achieves better results than the others, with no false positives in the background and segmentation boundaries very close to the ground truth.

## 5 Conclusion

We introduce DeSAM, a powerful network architecture for single-source domain generalization in medical image segmention. It decouples mask generation from prompt and takes advantage of the pre-trained weights of SAM. DeSAM motivates the decoder to learn prompt-invariant features from robust image embeddings. Moreover, DeSAM has strong ability to resist unseen distribution changes by fusing image embeddings at multiple scales. We validated the performance of DeSAM on two public datasets, demonstrating that the proposed method outperforms other state-of-the-art methods.

**Acknowledgments.** This work was supported in part by National Science Foundation of China under Grant 82372052, in part by Taishan Industrial Experts Program under Grant tscx202312131, in part by Key Research and Development Program of Shandong under Grant 2021SFGC0104, in part by Science Foundation of Shandong under Grant ZR2022QF071 and ZR2022QF099, in part by Huami Innovation and Venture Fund, and in part by Key Research and Development Program of Jiangsu under Grant BE2021663 and BE2023714.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-63-0"></span>1. Yifan Gao, Yin Dai, Fayu Liu, Weibing Chen, and Lifu Shi. An anatomy-aware framework for automatic segmentation of parotid tumor from multimodal mri. *Computers in Biology and Medicine*, page 107000, 2023.
- <span id="page-63-1"></span>2. Yaroslav Ganin and Victor Lempitsky. Unsupervised domain adaptation by backpropagation. In *International conference on machine learning*, pages 1180–1189. PMLR, 2015.
- <span id="page-63-2"></span>3. Krikamol Muandet, David Balduzzi, and Bernhard Schölkopf. Domain generalization via invariant feature representation. In *International conference on machine learning*, pages 10–18. PMLR, 2013.
- <span id="page-63-3"></span>4. Hao Guan and Mingxia Liu. Domain adaptation for medical image analysis: a survey. *IEEE Transactions on Biomedical Engineering*, 69(3):1173–1185, 2021.
- <span id="page-63-4"></span>5. Chen Chen, Zeju Li, Cheng Ouyang, Matthew Sinclair, Wenjia Bai, and Daniel Rueckert. Maxstyle: Adversarial style composition for robust medical image segmentation. In *International Conference on Medical Image Computing and Computer-Assisted Intervention*, pages 151–161. Springer, 2022.
- <span id="page-63-5"></span>6. Sihong Chen, Kai Ma, and Yefeng Zheng. Med3d: Transfer learning for 3d medical image analysis. arXiv preprint [arXiv:1904.00625,](http://arxiv.org/abs/1904.00625) 2019.
- <span id="page-63-6"></span>7. Zongwei Zhou, Vatsal Sodha, Jiaxuan Pang, Michael B Gotway, and Jianming Liang. Models genesis. *Medical image analysis*, 67:101840, 2021.

- <span id="page-64-0"></span>8. Alexander Kirillov, Eric Mintun, Nikhila Ravi, Hanzi Mao, Chloe Rolland, Laura Gustafson, Tete Xiao, Spencer Whitehead, Alexander C Berg, Wan-Yen Lo, et al. Segment anything. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 4015–4026, 2023.
- <span id="page-64-1"></span>9. Xueyan Zou, Jianwei Yang, Hao Zhang, Feng Li, Linjie Li, Jianfeng Wang, Lijuan Wang, Jianfeng Gao, and Yong Jae Lee. Segment everything everywhere all at once. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-64-2"></span>10. Jun Ma, Yuting He, Feifei Li, Lin Han, Chenyu You, and Bo Wang. Segment anything in medical images. *Nature Communications*, 15(1):654, 2024.
- 11. Junde Wu, Rao Fu, Huihui Fang, Yuanpei Liu, Zhaowei Wang, Yanwu Xu, Yueming Jin, and Tal Arbel. Medical sam adapter: Adapting segment anything model for medical image segmentation. arXiv preprint [arXiv:2304.12620,](http://arxiv.org/abs/2304.12620) 2023.
- <span id="page-64-10"></span>12. Kaidong Zhang and Dong Liu. Customized segment anything model for medical image segmentation. arXiv preprint [arXiv:2304.13785,](http://arxiv.org/abs/2304.13785) 2023.
- <span id="page-64-3"></span>13. Yuheng Li, Mingzhe Hu, and Xiaofeng Yang. Polyp-sam: Transfer sam for polyp segmentation. arXiv preprint [arXiv:2305.00293,](http://arxiv.org/abs/2305.00293) 2023.
- <span id="page-64-4"></span>14. Chen Chen, Chen Qin, Huaqi Qiu, Cheng Ouyang, Shuo Wang, Liang Chen, Giacomo Tarroni, Wenjia Bai, and Daniel Rueckert. Realistic adversarial data augmentation for mr image segmentation. In *International Conference on Medical Image Computing and Computer-Assisted Intervention*, pages 667–677. Springer, 2020.
- <span id="page-64-5"></span>15. Zhenlin Xu, Deyi Liu, Junlin Yang, Colin Raffel, and Marc Niethammer. Robust and generalizable visual representation learning via random convolutions. In *International Conference on Learning Representations*, 2021.
- <span id="page-64-6"></span>16. Cheng Ouyang, Chen Chen, Surui Li, Zeju Li, Chen Qin, Wenjia Bai, and Daniel Rueckert. Causality-inspired single-source domain generalization for medical image segmentation. *IEEE Transactions on Medical Imaging*, 2022.
- <span id="page-64-7"></span>17. Jiazhong Cen, Zanwei Zhou, Jiemin Fang, Wei Shen, Lingxi Xie, Dongsheng Jiang, Xiaopeng Zhang, Qi Tian, et al. Segment anything in 3d with nerfs. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-64-8"></span>18. Simiao Ren, Francesco Luzi, Saad Lahrichi, Kaleb Kassaw, Leslie M Collins, Kyle Bradbury, and Jordan M Malof. Segment anything, from space? In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 8355–8365, 2024.
- <span id="page-64-9"></span>19. Saikat Roy, Tassilo Wald, Gregor Koehler, Maximilian R Rokuss, Nico Disch, Julius Holzschuh, David Zimmerer, and Klaus H Maier-Hein. Sam. md: Zero-shot medical image segmentation capabilities of the segment anything model. arXiv preprint [arXiv:2304.05396,](http://arxiv.org/abs/2304.05396) 2023.
- <span id="page-64-11"></span>20. Olaf Ronneberger, Philipp Fischer, and Thomas Brox. U-net: Convolutional networks for biomedical image segmentation. In *Medical Image Computing and Computer-Assisted Intervention–MICCAI 2015: 18th International Conference, Munich, Germany, October 5-9, 2015, Proceedings, Part III 18*, pages 234–241. Springer, 2015.
- <span id="page-64-12"></span>21. Ali Hatamizadeh, Yucheng Tang, Vishwesh Nath, Dong Yang, Andriy Myronenko, Bennett Landman, Holger R Roth, and Daguang Xu. Unetr: Transformers for 3d medical image segmentation. In *Proceedings of the IEEE/CVF winter conference on applications of computer vision*, pages 574–584, 2022.
- <span id="page-64-13"></span>22. Jie Hu, Li Shen, and Gang Sun. Squeeze-and-excitation networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 7132–7141, 2018.

- <span id="page-65-0"></span>23. Bennett Landman, Zhoubing Xu, J Igelsias, Martin Styner, T Langerak, and Arno Klein. Miccai multi-atlas labeling beyond the cranial vault–workshop and challenge. In *Proc. MICCAI Multi-Atlas Labeling Beyond Cranial Vault-Workshop Challenge*, volume 5, page 12, 2015.
- <span id="page-65-1"></span>24. A Emre Kavur, N Sinem Gezer, Mustafa Barı¸s, Sinem Aslan, Pierre-Henri Conze, Vladimir Groza, Duc Duy Pham, Soumick Chatterjee, Philipp Ernst, Savaş Özkan, et al. Chaos challenge-combined (ct-mr) healthy abdominal organ segmentation. *Medical Image Analysis*, 69:101950, 2021.
- <span id="page-65-2"></span>25. Bloch N., Madabhushi A., Huisman H., Freymann J., et al. NCI-ISBI 2013 challenge: Automated segmentation of prostate structures, 2015. [https://www.](https://www.cancerimagingarchive.net) [cancerimagingarchive.net](https://www.cancerimagingarchive.net)
- <span id="page-65-3"></span>26. Guillaume Lemaˆıtre, Robert Mart´ı, Jordi Freixenet, Joan C Vilanova, Paul M Walker, and Fabrice Meriaudeau. Computer-aided detection and diagnosis for prostate cancer based on mono and multi-parametric mri: a review. *Computers in biology and medicine*, 60:8–31, 2015.
- <span id="page-65-4"></span>27. Geert Litjens, Robert Toth, Wendy van de Ven, Caroline Hoeks, Sjoerd Kerkstra, Bram van Ginneken, Graham Vincent, Gwenael Guillard, Neil Birbeck, Jindang Zhang, et al. Evaluation of prostate segmentation algorithms for mri: the promise12 challenge. *Medical image analysis*, 18(2):359–373, 2014.
- <span id="page-65-5"></span>28. Fabian Isensee, Paul F Jaeger, Simon AA Kohl, Jens Petersen, and Klaus H Maier-Hein. nnu-net: a self-configuring method for deep learning-based biomedical image segmentation. *Nature methods*, 18(2):203–211, 2021.

Image /page/66/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a curved line segment on the left and a flag shape on the right. The text below the icon reads "Check for updates".

# **DinoBloom: A Foundation Model for Generalizable Cell Embeddings in Hematology**

Valentin Koch<sup>1,2</sup>, Sophia J. Wagner<sup>1,2</sup>, Salome Kazeminia<sup>1,2</sup>, Ece Sancar<sup>1,2</sup>, Matthias Hehr<sup>2,3</sup>, Julia A. Schnabel<sup>1,2,4</sup>, Tingying Peng<sup>1,2</sup>, and Carsten Marr<sup>2( $\boxtimes$ )</sup>

<sup>1</sup> School of Computation, Information and Technology, Technical University of Munich, Munich, Germany

 $^{\rm 2}$  Institute of AI for Health, Computational Health Center, Helmholtz Munich, Munich, Germany

<EMAIL>

<sup>3</sup> Dr. von Haunersches Kinderspital, Ludwig-Maximilians-University Munich, Munich, Germany

Munich, Germany

<sup>4</sup> School of Biomedical Engineering and Imaging Sciences, King's College London, London, UK

**Abstract.** In hematology, computational models offer significant potential to improve diagnostic accuracy, streamline workflows, and reduce the tedious work of analyzing single cells in peripheral blood or bone marrow smears. However, clinical adoption of computational models has been hampered by the lack of generalization due to large batch effects, small dataset sizes, and poor performance in transfer learning from natural images. To address these challenges, we introduce DinoBloom, the first foundation model for single cell images in hematology, utilizing a tailored DINOv2 pipeline. Our model is built upon an extensive collection of 13 diverse, publicly available datasets of peripheral blood and bone marrow smears, the most substantial open-source cohort in hematology so far, comprising over 380,000 white blood cell images. To assess its generalization capability, we evaluate it on an external dataset with a challenging domain shift. We show that our model outperforms existing medical and non-medical vision models in (i) linear probing and *k*-nearest neighbor evaluations for cell-type classification on blood and bone marrow smears and (ii) weakly supervised multiple instance learning for acute myeloid leukemia subtyping by a large margin. A family of four DinoBloom models (small, base, large, and giant) can be adapted for a wide range of downstream applications, be a strong baseline for classification problems, and facilitate the assessment of batch effects in new datasets. All models are available at [github.com/marrlab/DinoBloom.](https://github.com/marrlab/DinoBloom)

**Keywords:** Self-supervised learning *·* Foundation model *·* Hematology

V. Koch and S. J. Wagner—Contributed equally.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 520–530, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_49)\_49

## 1 Introduction

Hematology, the study of blood and blood-related diseases, relies heavily on the microscopic examination of peripheral blood and bone marrow smears. This practice is integral to diagnosing hematological diseases, such as acute myeloid leukemia (AML) [\[3,](#page-74-0)[15](#page-75-0)]. Currently, differential blood counts still rely on manual cytomorphological analysis of at least 200 individual white blood cells (WBC) per patient, where exact evaluation is crucial for early and precise diagnosis [\[13\]](#page-75-1). This labor-intensive process has resisted automation, remaining a domain for trained experts. However, it suffers from significant intra- and inter-expert variability, complicating diagnosis in environments that lack trained personnel [\[9\]](#page-75-2).

Recent advances in deep learning propose solutions to the challenges in hematology, such as classifying leukemia subtypes from microscopic images [\[18](#page-75-3),[20\]](#page-75-4). However, the transition from manual to automated analysis requires robust models that can deal with limited data, strong batch effects, and largely varying cell phenotypes. In particular, for weakly-supervised settings, such as multiple instance learning (MIL) for patient-level disease prediction, a strong feature extractor for single blood cells is necessary as supervised learning is not possible.

Still, most approaches so far rely on supervised training sets with corresponding datasets. Rastogi et al. [\[24](#page-76-0)] train a convolutional neural network (CNN) as a feature extractor on the AML Matek dataset [\[20\]](#page-75-4) on 18,365 single-cell images from peripheral blood smears. Hehr et al. [\[12\]](#page-75-5) train a feature extractor fully supervised on a cell classification task on additional data from the same domain and subsequently train the MIL aggregation model.

Large-scale self-supervised training on diverse datasets has transformed the domain of computer vision on natural images  $[5,10]$  $[5,10]$  $[5,10]$ . In the medical imaging domain, especially, in histopatholoy, domain specific self-supervised representation learning on large sets of unlabeled images [\[30](#page-76-1)[,31](#page-76-2)] has shown to improve downstream tasks in MIL settings [\[29\]](#page-76-3). To this end, DINO [\[5\]](#page-75-6) and its successor DINOv2 [\[23\]](#page-76-4) have emerged as a pipeline well-suited to train these feature extractors  $[6,8,28]$  $[6,8,28]$  $[6,8,28]$  $[6,8,28]$ . However, to date, there is no comparable effort tackling the challenges in the domain of hematology.

We propose DinoBloom (Dino Blood Model), a model family based on vision transformers trained with a customized DINOv2 [\[23\]](#page-76-4) pipeline to provide rich visual features for single-cell image analysis in hematology. These models are able to extract predictive features even on unseen datasets that can be used for few-shot classification, multiple instance learning, or cell embeddings potentially characterizing disease profiles while offering explainable features for enhanced interpretability. The main contributions of our work are:

- We introduce DinoBloom, the first large-scale self-supervised trained models designed explicitly for single-cell hematology image analysis.
- We assemble the largest cohort in hematology comprising 13 datasets of peripheral blood and bone marrow smears.
- We show that DinoBloom models are effective in capturing diverse visual features of single cells across tasks on both in-domain and out-of-domain datasets for cell-type classification and leukemia subtyping.

Image /page/68/Picture/1 description: This figure displays a grid of microscopic images of blood cells, categorized by different datasets and analyses. Panel (a) shows rows of cell images under column headers representing datasets like BMC, AML Hehr, INT, AML Matek, Acevedo, Raabin, NuClick, Warty pig, LISC, Chula, SSLSeg, BCCD, and Aslan. Below each column are numerical counts, such as 171,373 for BMC and 100 for Aslan. Panel (b) illustrates a machine learning process with 'DinoBloom student' and 'DinoBloom teacher' models, showing augmentations of cell images. Panels (c), (d), and (e) present simplified diagrams of classification tasks for 'WBC blood' (10 cell types), 'AML subtype' (5 subtypes), and 'WBC bone marrow' (21 cell types), respectively, each featuring a 'DinoBloom' model and a classifier with corresponding bar charts indicating the number of categories.

**Fig. 1.** Data and model overview of our pipeline. (a) All 13 datasets used in this study: dashed lines indicate datasets split into training data for DinoBloom and test data for downstream evaluations, continuous line indicates the dataset was completely held out for testing purposes. (b) Modified DINOv2 pipeline without local crops for model training. We evaluate the performance on three downstream tasks: (c) WBC type classification on the external dataset Acevedo, (d) AML subtyping via multiple instance learning, and (e) bone marrow WBC type classification.

<span id="page-68-0"></span>– We provide open access to all DinoBloom models as well as the source code and parameters used for training, encouraging the research community to collaboratively build upon our work.

### 2 Datasets

To the best of our knowledge, we collected the largest hematology cohort with 13 publicly available datasets of in total over 380,000 blood cell images, fusing the domains of peripheral blood and bone marrow smears (Fig. [1a](#page-68-0)). It consists of the following datasets: The *Bone Marrow Cytomorphology* (BMC) dataset contains 171,373 de-identified, expert-annotated cells from bone marrow smears of 945 patients [\[18](#page-75-3)]. In contrast to all other datasets included in this study, the bone marrow smears were stained using the May-Grünwald-Giemsa/Pappenheim stain. The *AML Hehr* dataset includes 81,214 single-cell images from 189 patients, covering four genetic AML subtypes and a healthy control group, sourced from annotated patient-level blood smears. The *INT* dataset is an internal dataset that consists of 41,906 images of peripheral blood smears. The dataset will be made publicly available through a different publication. The *AML Matek* dataset consists of 18,365 expert-labeled single-cell images with 15 heavily imbalanced classes taken from peripheral blood smears of 100 patients

<span id="page-69-0"></span>

| Model       | Batch size | Train time | Feature dim | #params |
|-------------|------------|------------|-------------|---------|
| DinoBloom-S | 1216       | 1:30 h     | 384         | 22 M    |
| DinoBloom-B | 960        | 0:45 h     | 768         | 86 M    |
| DinoBloom-L | 448        | 1:00 h     | 1024        | 304 M   |
| DinoBloom-G | 208        | 4:00 h     | 1536        | 1136 M  |

**Table 1.** Training configuration of the DinoBloom model family.

diagnosed with AML, as well as 100 healthy patients [\[19](#page-75-10),[20\]](#page-75-4). *Acevedo* encompasses 17,092 images of WBCs labeled into 11 classes [\[1](#page-74-1)]. The *Raabin* dataset features 10,175 cropped WBCs, which are labeled by experts into five classes [\[17\]](#page-75-11). The *NuClick* dataset was created from 11,000 WBC images of four classes to generate 2,689 images of artificially overlapping nuclei [\[16\]](#page-75-12). The *Warty Pig* dataset contains 1,408 cropped and classified, plus 1,463 augmented WBC images of juvenile Visayan warty pigs [\[2\]](#page-74-2). The public part of the *LISC* dataset consists of 157 WBC images as well as several augmented versions of them, totaling 2*,* 263 images [\[25\]](#page-76-6). *Chula* [\[22](#page-76-7)] is a red blood cell segmentation dataset and holds 706 single images. The *SSLSeg* datasets contain 300 images of WBCs, stemming from two different sources (200/100 images) [\[32\]](#page-76-8). The *Blood Cell Count and Detection* (BCCD) dataset was created to detect blood cells and includes 364 images with bounding box labels [\[21\]](#page-76-9). The *Aslan* blood cell detection dataset offers 100 images of white and red blood cells taken from a light microscope [\[4\]](#page-75-13).

## 3 Methods

**DINOv2 Finetuning.** We train our models that are based on vision trans-formers (ViT) [\[7\]](#page-75-14) in different sizes using the DINOv2 framework. Following [\[26\]](#page-76-10), we use the pretrained checkpoints to efficiently finetune the vision transformer on our multi-cohort dataset. The self-supervised learning framework DINOv2 employs a teacher-student architecture with an image-based loss on the class token of the DINO head and a patch-based loss on the class token of the masked patches from the iBot head (Fig. [1b](#page-68-0)). We remove the global-local crop loss as we found it hampers performance when learning representations on the singlecell images in blood and bone marrow datasets. Images are resized to 224*×*224 pixels. The models are trained on 8 NVIDIA A100-SXM4-40GB GPUs with an AMD EPYC 7742 64-Core CPU. All models show similar convergence patterns and reached their peak performance between 4,000 and 8,000 iterations, after which downstream task performance drops slightly. Depending on the model and corresponding batch size, 4,000 iterations cover the training set between 1*.*7 (batch size 208, DinoBloom-G) up to 10 times (batch size 1,216, DinoBloom-S). Used batch size, feature dimension, and training time, as well as the number of parameters, can be inferred from Table [1.](#page-69-0)

<span id="page-70-0"></span>**Table 2.** Evaluation on peripheral blood: Image-level WBC classification on Acevedo dataset and patient-level AML subtyping on AML Hehr dataset. Best results are marked in bold, second-best results are underlined. Standard deviation in the ABMIL setting was obtained by 5-fold cross-validation. Performance is measured in weighted F1-score (wF1) and balanced Accuracy (bAcc).

|                                                                             |                                                               | Acevedo |         | AML Hehr |              |     |                                                             |
|-----------------------------------------------------------------------------|---------------------------------------------------------------|---------|---------|----------|--------------|-----|-------------------------------------------------------------|
|                                                                             | $1-NN$                                                        |         | $20-NN$ |          | Linear probe |     | <b>ABMIL</b>                                                |
|                                                                             | wF1 bAcc wF1 bAcc wF1                                         |         |         |          | bAcc         | wF1 | bAcc                                                        |
| ResNet 50                                                                   | 58.8 52.6 65.6 58.7 81.3                                      |         |         |          | 75.4         |     | $81.9 + 9.7$ $81.5 + 9.6$                                   |
| ResNet 50 trunc                                                             | 68.5 62.4 74.0 67.8 87.5                                      |         |         |          | 81.6         |     | $41.5 + 11.7 + 45.9 + 10.0$                                 |
| DINO <sub>v</sub> 2 ViT-S                                                   | 72.9 65.6 78.5 70.8 87.7                                      |         |         |          | 82.0         |     | $52.5 + 11.8 \cdot 54.5 + 10.0$                             |
| DINO <sub>v2</sub> ViT-B                                                    | 71.9 64.8 77.3 69.9 87.8                                      |         |         |          | 81.8         |     | $49.6 \scriptstyle{\pm 17.3}$ $52.2 \scriptstyle{\pm 14.3}$ |
| DINO <sub>v2</sub> ViT-L                                                    | 72.2 64.9 77.8 72.4 89.1                                      |         |         |          | 83.5         |     | $51.5 + \frac{16.6}{53.6} + \frac{13.5}{54}$                |
| DINO <sub>v2</sub> ViT-G                                                    | 77.8 70.4 81.9 74.2 90.1                                      |         |         |          | 84.5         |     | $21.1 + 13.5$ $28.6 + 10.2$                                 |
| <b>CTransPath</b>                                                           | $80.8$ 73.9 $83.1$ 76.8 $88.0$                                |         |         |          | 82.5         |     | $60.2$ +12.6 60.9+11.8                                      |
| Phikon ViT-B                                                                | 83.3   76.5   85.1   78.7   88.2                              |         |         |          | 82.7         |     | $81.8 + s.3$ $81.5 + s.5$                                   |
| $DinoBloom-S$ (ours)                                                        | $86.4 \,   \, 80.5 \,   \, 90.0 \,   \, 84.5 \,   \, 90.1 \,$ |         |         |          | 84.5         |     | $93.0_{\pm 3.0}$   $92.3_{\pm 3.4}$                         |
| DinoBloom-B (ours) $ 87.4 81.9 90.5 85.4 90.7$                              |                                                               |         |         |          | 85.5         |     | $92.7_{+2.9}$ $91.9_{+3.1}$                                 |
| DinoBloom-L (ours) $8.9$ $8.2$ $91.3$ $86.1$ $91.2$                         |                                                               |         |         |          | 86.0         |     | $91.7_{\pm 2.4}$ $91.0_{\pm 2.7}$                           |
| DinoBloom-G (ours) $\mid 89.1 \mid 83.5 \mid 91.4 \mid 86.4 \mid 91.8 \mid$ |                                                               |         |         |          | 86.6         |     | $93.1_{+2.5}$ $92.4_{+2.8}$                                 |

**Train and Test Data.** We train the DinoBloom models on all datasets except the Acevedo dataset, which is kept as external test set. The other two datasets used for evaluation were split into train/test (80*/*20). Only training data was used to train our DinoBloom models. We evaluate the downstream task performance using the same train/test split. The following datasets and settings are used:

- Acevedo is the smallest of the peripheral blood datasets with complete imagelevel classification exhibiting a strong batch effect [\[27](#page-76-11)],(Fig. [1a](#page-68-0)) hence serving as a good measure of the generalization capabilities of our model.
- The AML Hehr dataset is divided into train/test  $(80/20)$  on patient level. It includes 101,949 WBCs from 242 patients across four AML subtypes: CBFB::MYH11, NPM1, PML::RARA, and RUNX1::RUNX1T1 and a healthy control class.
- The BMC dataset is split into train/test (80*/*20) as it is the only bone marrow dataset. It contains 21 heavily imbalanced classes (Fig. [1e](#page-68-0)).

**Downstream Evaluations.** In all downstream experiments, we compare the following feature extractors: (i) the non-medical-domain models ImageNetpretrained ResNet50 [\[11](#page-75-15)] (full and truncated) and (ii) the pretrained DINOv2 checkpoints, trained on LVD-142M [\[23](#page-76-4)]; (iii) the medical-image domain feature extractors CTransPath [\[31](#page-76-2)], trained on 14M patches from TCGA and PAIP, (iv)

<span id="page-71-0"></span>

|                    | 1-NN |      |      | 20-NN |      |      | Linear probe |      |      |
|--------------------|------|------|------|-------|------|------|--------------|------|------|
|                    | wF1  | Acc  | bAcc | wF1   | Acc  | bAcc | wF1          | Acc  | bAcc |
| ResNet 50          | 37.6 | 37.3 | 21.1 | 47.4  | 50.0 | 23.0 | 64.1         | 65.2 | 39.6 |
| ResNet 50 trunc    | 46.7 | 46.4 | 31.2 | 57.5  | 59.8 | 33.0 | 74.5         | 75.0 | 49.8 |
| DINOv2 ViT-S       | 43.2 | 43.2 | 25.0 | 52.4  | 55.6 | 26.5 | 68.1         | 69.0 | 44.9 |
| DINOv2 ViT-B       | 39.8 | 39.6 | 23.9 | 49.3  | 55.6 | 24.1 | 70.8         | 71.5 | 48.5 |
| DINOv2 ViT-L       | 39.4 | 39.2 | 24.5 | 48.9  | 52.2 | 24.0 | 71.0         | 71.6 | 47.8 |
| DINOv2 ViT-G       | 41.0 | 41.0 | 22.6 | 50.4  | 53.7 | 24.4 | 73.5         | 74.0 | 52.1 |
| CTransPath         | 49.1 | 48.7 | 42.0 | 58.5  | 60.3 | 36.1 | 74.1         | 74.9 | 52.2 |
| Phikon ViT-B       | 47.5 | 47.2 | 40.8 | 57.1  | 59.0 | 35.5 | 73.2         | 73.8 | 54.4 |
| DinoBloom-S (ours) | 78.4 | 78.3 | 62.0 | 84.2  | 84.8 | 55.6 | 85.7         | 85.9 | 71.4 |
| DinoBloom-B (ours) | 79.6 | 79.5 | 65.8 | 83.7  | 84.1 | 57.1 | 85.5         | 85.6 | 70.7 |
| DinoBloom-L (ours) | 78.8 | 78.8 | 57.7 | 83.6  | 84.0 | 56.3 | 84.9         | 85.0 | 64.4 |
| DinoBloom-G (ours) | 80.0 | 79.9 | 59.4 | 83.8  | 84.2 | 56.2 | 84.9         | 85.0 | 69.3 |

**Table 3.** Evaluation on bone marrow: WBC classification on the dataset BMC with 21 highly imbalanced classes.

the Phikon ViT-B model [\[8\]](#page-75-9), trained on PanCancer40M from TCGA; and (v) our models DinoBloom-S, -B, -L, and -G.

We evaluate the performance of all supervised classifier models by linear probe and *k*-nearest neighbors (*k*-NN) for cell-type classification and in a weaklysupervised multiple instance learning (MIL) setting for AML subtyping. For linear probe, the sklearn LogisticRegression class is used with l2-regularization coefficient of  $c \times n100$  where *n* is the number of training samples and *c* is the number of classes. For the MIL evaluation, similar to the Hehr et al. [\[12\]](#page-75-5) framework, we deploy a dedicated classifier head, structured as a two linear layer architecture with an intermediary ReLU activation, tailored to map the aggregated latent vectors of a patient to a class prediction.

### 4 Results

**Peripheral Blood.** DinoBloom models outperform existing models of same size on single WBC classification on the external dataset Acevedo. There is a strong performance gain over the original DINOv2 models, e.g., ViT-S with 71*.*9 weighted F1-score on 1-NN vs. DinoBloom-S 86*.*4, ViT-G 77*.*8 vs. DinoBloom-G 89*.*1 (Table [2\)](#page-70-0). The histopathology domain-specific feature extractors CTransPath and the recently released Phikon (Vit-B) model perform better than models with non-medical pretraining, both models perform roughly equally in linear probing, while Phikon has a slightly better performance in k-NN evaluations. However, DinoBloom models do not only perform better than their corresponding baseline from DINOv2, but even our smallest model performs better than all other tested models, irrespective of their size. One can also

Image /page/72/Figure/1 description: This is a UMAP plot showing different cell populations, likely from a blood sample. The plot is divided into several clusters, each labeled with a cell type or condition. Key labels include 'Healthy', 'Neutrophil granulocytes', 'Monocytic cells', 'Myeloblasts', 'Lymphocytes', 'Normocytes', 'Broken cells', 'Doublets', and 'Staining artifacts'. There are also specific genetic alterations highlighted, such as 'CBFB::MYH11', 'RUNX1::RUNX1T1', 'NPM1', and 'PML::RARA', each associated with distinct clusters of cells colored in shades of orange, purple, green, and red, respectively. A legend indicates that colored dots represent 'All patients of subtype' and darker dots represent 'Random patient of subtype', with different colors corresponding to different subtypes.

<span id="page-72-0"></span>**Fig. 2.** Low dimensional representation (UMAP) of DinoBloom-B features fitted on over 80,000 single cells from the AML Hehr training dataset. Center: UMAP with original images. Five arcs: UMAP of patients from the training set (grey) and from the test set for healthy patients (blue), patients with CBFB::MYH11 (orange), NPM1 (green), PML::RARA (red), and RUNX1::RUNX1T1 (purple). In each arc, all test set patients are shown in bright dots, with one random patient highlighted in dark dots. (Color figure online)

observe that the larger variants of DinoBloom models perform better compared to smaller versions, e.g., DinoBloom-G vs. DinoBloom-B vs. DinoBloom-S with the weighted F1-score on 1-NN (89.1 vs. 87.4 vs. 86.4).

DinoBloom effectively serves as a feature extractor for training a weaklysupervised AML subtype classifier with ABMIL [\[14](#page-75-16)] aggregation. In our experiments, DinoBloom models achieve a weighted F1-score between 91*.*7 (DinoBloom-L) and 93*.*1 (DinoBloom-G), while the second-best models are ResNet50 and Phikon Vit-B with 81.9 and 81*.*8, respectively.

**Bonemarrow Cytology.** In line with the results on the Acevedo dataset, DinoBloom models outperform both non-medical and medical models on bone marrow WBC classification by even larger margins. The classification task on BMC is heavily imbalanced with 21 classes and over 170*,* 000 samples in total, where some classes have a very low sample count, such as abnormal eosinophils (8) or smudge cells (42, Fig. [1e](#page-68-0)). Despite the challenging task, DinoBloom-B reaches a balanced accuracy of 65*.*8 and an accuracy of 79*.*5 in 1-NN evaluation compared to 42*.*0 and 48*.*7 of the next best model (Fig. [3\)](#page-71-0). Similar large gaps in performance are also observed in linear probing, where our best model, DinoBLoom-S, achieves a balanced accuracy of 71*.*4 compared to 52*.*2 of CTransPath. Notably, the performance in 1-NN weighted-F1 score is doubled

Image /page/73/Picture/1 description: This image displays a grid of microscopic cell images, categorized by cell type and source. The rows are labeled 'Peripheral blood Metamyelocytes', 'Peripheral blood Promyelocyte', 'Bone marrow Promyelocytes', and 'Bone marrow Basophils'. The columns are labeled 'Input Images', 'DinoBloom-B', 'Phikon (ViT-B)', and 'DINOv2 ViT-B'. The 'Input Images' column shows original microscopic views of the cells. The other columns show segmented or processed versions of these cells, with different colors representing different cellular components or segmentation masks. For example, the 'Peripheral blood Metamyelocytes' row shows two input images and four processed images, where the cells are highlighted with blue and purple colors against a green background.

**Fig. 3.** PCA visualization of the patch tokens on the test data of Acevedo (external) and BMC. Comparison between DinoBloom-B, the second-best model Phikon (ViT-B), and the pretrained DINOv2 ViT-B. Colors represent the values of the first three PCA components. DinoBloom-B can differentiate between nuclei, cytoplasm, surrounding red blood cells, and background. (Color figure online)

<span id="page-73-0"></span>compared to the DINOv2 baseline that can be observed for the ViT-B variant. Compared to the peripheral blood tasks, the performance of the larger variants of DinoBloom is not consistently better than that of the smaller variants.

**Patient Embeddings.** We show a potential clinical application of our model: The low-dimensional embedding of AML and healthy patients from the training set of the AML Hehr dataset shows that related cell types cluster well (Fig. [2\)](#page-72-0). Based on this fit, new patients (from the test set) are embedded into the same lower dimensional feature representation. The distribution of cell types is clearly distinct between healthy patients and all AML subtypes: as expected there are almost no cells in the Myeloblast related cluster in the healthy group. More subtle differences can be seen between clustering profiles of distinct AML subtypes, e.g., CBFB::MYH11 and RUNX1::RUNX1T1. The cell embeddings of our model could give clinicians an easy-to-grasp visualization of the cell distribution of a patient and help to verify the manual quantification of different cell types. For instance, experts could identify the presence of myeloblasts within an entire smear, and gates could be applied to the embedding to facilitate morphologybased cell counting, similar to FACS analysis.

**Interpretability.** In Fig. [3,](#page-73-0) we show that our model learns robust features that detect semantic concepts across domains and compare it to its baseline, the pretrained DINOV2, and the second-best performing model Phikon. We compute the principal components for the encoded patches of four images per dataset and visualize the first three components of each patch of size 14*×*14 (DinoBloom-B, DINOv2 Vit-B) and 16*×*16 pixels (Phikon), respectively, in RGB colors. One can clearly observe that DinoBloom captures shapes of nuclei, cell body, and the surrounding cells. While the other two models also capture shape and roughly outline cells, they do not catch fine grained details, as can be especially seen in the case of the nuclei, that only DinoBloom is able to differentiate from the whole cell.

# 5 Conclusion

With DinoBloom, we introduce a publicly available family of foundation models for single cell images in hematology, trained on the largest multi-cohort dataset with over 380,000 WBC images of 13 datasets. We show its strong generalization capabilities to external datasets despite strong batch effects. Our experiments demonstrate that DinoBloom extracts rich features from hematology images, with its effectiveness demonstrated for cell-type classification and AML subtyping compared to non-medical and medical-imaging specific models. We also support this claim through visualizations showing that our model detects important hematological concepts, such as nuclei, cytoplasm, and red blood cells, which could be further leveraged for zero-shot segmentation. We believe that the generalizable cell embedding capabilities of our DinoBloom models offer great potential in assisting clinicians in their tedious manual work of cell detection and classification.

**Acknowledgments.** VK, SJW and SK are supported by the Helmholtz Association under the joint research school "Munich School for Data Science - MUDS". SJW is supported by the Add-on Fellowship of the Joachim Herz Foundation. This work was also supported by the BMBF-funded de.NBI Cloud within the German Network for Bioinformatics Infrastructure (de.NBI) (031A532B, 031A533A, 031A533B, 031A534A, 031A535A, 031A537A, 031A537B, 031A537C, 031A537D, 031A538A). CM has received funding from the European Research Council under the European Union's Horizon 2020 research and innovation program (grant agreement number 866411).

# References

- <span id="page-74-1"></span>1. Acevedo, A., Merino, A., Alférez, S., Molina, Á., Boldú, L., Rodellar, J.: A dataset of microscopic peripheral blood cell images for development of automatic recognition systems. Data in brief **30** (2020)
- <span id="page-74-2"></span>2. Alipo-on, J.R., Escobar, F.I., Novia, J.L., Atienza, M.M., Mana-ay, S., Tan, M.J., AlDahoul, N., Yu, E.: Dataset for machine learning-based classification of white blood cells of the juvenile visayan warty pig (2022). 10.21227/3qsb-d447
- <span id="page-74-0"></span>3. Arber, D.A., Orazi, A., Hasserjian, R.P., Borowitz, M.J., Calvo, K.R., Kvasnicka, H.M., Wang, S.A., Bagg, A., Barbui, T., Branford, S., et al.: International consensus classification of myeloid neoplasms and acute leukemias: integrating morphologic, clinical, and genomic data. Blood, The Journal of the American Society of Hematology **140**(11), 1200–1228 (2022)

- <span id="page-75-13"></span>4. Aslan, A.: Blood cell detection dataset (2020), accessed: 2024-03-05
- <span id="page-75-6"></span>5. Caron, M., Touvron, H., Misra, I., Jégou, H., Mairal, J., Bojanowski, P., Joulin, A.: Emerging properties in self-supervised vision transformers (2021)
- <span id="page-75-8"></span>6. Chen, R.J., Ding, T., Lu, M.Y., Williamson, D.F.K., Jaume, G., Chen, B., Zhang, A., Shao, D., Song, A.H., Shaban, M., Williams, M., Vaidya, A., Sahai, S., Oldenburg, L., Weishaupt, L.L., Wang, J.J., Williams, W., Le, L.P., Gerber, G., Mahmood, F.: A general-purpose self-supervised model for computational pathology (2023)
- <span id="page-75-14"></span>7. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-75-9"></span>8. Filiot, A., Ghermi, R., Olivier, A., Jacob, P., Fidon, L., Kain, A.M., Saillard, C., Schiratti, J.B.: Scaling self-supervised learning for histopathology with masked image modeling. medRxiv (2023)
- <span id="page-75-2"></span>9. Font, P., Loscertales, J., Soto, C., Ricard, P., Novas, C.M., Martín-Clavero, E., L´opez-Rubio, M., Garcia-Alonso, L., Callejas, M., Bermejo, A., et al.: Interobserver variance in myelodysplastic syndromes with less than 5% bone marrow blasts: unilineage vs. multilineage dysplasia and reproducibility of the threshold of 2% blasts. Annals of hematology **94**, 565–573 (2015)
- <span id="page-75-7"></span>10. He, K., Fan, H., Wu, Y., Xie, S., Girshick, R.: Momentum contrast for unsupervised visual representation learning (2020)
- <span id="page-75-15"></span>11. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proc. of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-75-5"></span>12. Hehr, M., Sadafi, A., Matek, C., Lienemann, P., Pohlkamp, C., Haferlach, T., Spiekermann, K., Marr, C.: Explainable ai identifies diagnostic cells of genetic aml subtypes. PLOS Digital Health **2**(3), e0000187 (2023)
- <span id="page-75-1"></span>13. Houwen, B.: The differential cell count. Laboratory Hematology **7**, 89–100 (2001)
- <span id="page-75-16"></span>14. Ilse, M., Tomczak, J., Welling, M.: Attention-based deep multiple instance learning. In: International conference on machine learning. pp. 2127–2136. PMLR (2018)
- <span id="page-75-0"></span>15. Khoury, J.D., Solary, E., Abla, O., Akkari, Y., Alaggio, R., Apperley, J.F., Bejar, R., Berti, E., Busque, L., Chan, J.K., et al.: The 5th edition of the world health organization classification of haematolymphoid tumours: myeloid and histiocytic/dendritic neoplasms. Leukemia **36**(7), 1703–1719 (2022)
- <span id="page-75-12"></span>16. Koohbanani, N.A., Jahanifar, M., Tajadin, N.Z., Rajpoot, N.: Nuclick: a deep learning framework for interactive segmentation of microscopic images. Medical Image Analysis **65**, 101771 (2020)
- <span id="page-75-11"></span>17. Kouzehkanan, Z., Saghari, S., Tavakoli, E., Rostami, P., Abaszadeh, M., Mirzadeh, F., Satlsar, E., Gheidishahran, M., Gorgi, F., Mohammadi, S., et al.: Raabin-wbc: a large free access dataset of white blood cells from normal peripheral blood. (2021)
- <span id="page-75-3"></span>18. Matek, C., Krappe, S., Münzenmayer, C., Haferlach, T., Marr, C.: Highly accurate differentiation of bone marrow cell morphologies using deep neural networks on a large image data set. Blood, The Journal of the American Society of Hematology **138**(20), 1917–1927 (2021)
- <span id="page-75-10"></span>19. Matek, C., Schwarz, S., Marr, C., Spiekermann, K.: A single-cell morphological dataset of leukocytes from aml patients and non-malignant controls (amlcytomorphology lmu). The Cancer Imaging Archive (TCIA) (2019)
- <span id="page-75-4"></span>20. Matek, C., Schwarz, S., Spiekermann, K., Marr, C.: Human-level recognition of blast cells in acute myeloid leukaemia with convolutional neural networks. Nature Machine Intelligence **1**(11), 538–544 (2019)

- <span id="page-76-9"></span>21. Mohamed, M., Far, B., Guaily, A.: An efficient technique for white blood cells nuclei automatic segmentation. In: 2012 IEEE International Conference on Systems, Man, and Cybernetics (SMC). pp. 220–225. IEEE (2012)
- <span id="page-76-7"></span>22. Naruenatthanaset, K., Chalidabhongse, T.H., Palasuwan, D., Anantrasirichai, N., Palasuwan, A.: Red blood cell segmentation with overlapping cell separation and classification on imbalanced dataset (2023)
- <span id="page-76-4"></span>23. Oquab, M., Darcet, T., Moutakanni, T., Vo, H., Szafraniec, M., Khalidov, V., Fernandez, P., Haziza, D., Massa, F., El-Nouby, A., et al.: Dinov2: Learning robust visual features without supervision. arXiv preprint [arXiv:2304.07193](http://arxiv.org/abs/2304.07193) (2023)
- <span id="page-76-0"></span>24. Rastogi, P., Khanna, K., Singh, V.: Leufeatx: Deep learning-based feature extractor for the diagnosis of acute leukemia from microscopic images of peripheral blood smear. Computers in Biology and Medicine **142**, 105236 (2022)
- <span id="page-76-6"></span>25. Rezatofighi, S.H., Soltanian-Zadeh, H.: Automatic recognition of five types of white blood cells in peripheral blood. Computerized Medical Imaging and Graphics **35**(4), 333–343 (2011)
- <span id="page-76-10"></span>26. Roth, B., Koch, V., Wagner, S.J., Schnabel, J.A., Marr, C., Peng, T.: Low-resource finetuning of foundation models beats state-of-the-art in histopathology. arXiv preprint [arXiv:2401.04720](http://arxiv.org/abs/2401.04720) (2024)
- <span id="page-76-11"></span>27. Sadafi, A., Salehi, R., Gruber, A., Boushehri, S.S., Giehr, P., Navab, N., Marr, C.: A continual learning approach for cross-domain white blood cell classification. In: Koch, L., Cardoso, M.J., Ferrante, E., Kamnitsas, K., Islam, M., Jiang, M., Rieke, N., Tsaftaris, S.A., Yang, D. (eds.) Domain Adaptation and Representation Transfer, pp. 136–146. Springer Nature Switzerland, Cham (2024)
- <span id="page-76-5"></span>28. Vorontsov, E., Bozkurt, A., Casson, A., Shaikovski, G., Zelechowski, M., Liu, S., Severson, K., Zimmermann, E., Hall, J., Tenenholtz, N., Fusi, N., Mathieu, P., van Eck, A., Lee, D., Viret, J., Robert, E., Wang, Y.K., Kunz, J.D., Lee, M.C.H., Bernhard, J., Godrich, R.A., Oakley, G., Millar, E., Hanna, M., Retamero, J., Moye, W.A., Yousfi, R., Kanan, C., Klimstra, D., Rothrock, B., Fuchs, T.J.: Virchow: A million-slide digital pathology foundation model (2024)
- <span id="page-76-3"></span>29. Wagner, S.J., Reisenbüchler, D., West, N.P., Niehues, J.M., Zhu, J., Foersch, S., Veldhuizen, G.P., Quirke, P., Grabsch, H.I., van den Brandt, P.A., et al.: Transformer-based biomarker prediction from colorectal cancer histology: A largescale multicentric study. Cancer Cell (2023)
- <span id="page-76-1"></span>30. Wang, X., Du, Y., Yang, S., Zhang, J., Wang, M., Zhang, J., Yang, W., Huang, J., Han, X.: Retccl: Clustering-guided contrastive learning for whole-slide image retrieval. Medical Image Analysis **83**, 102645 (2023)
- <span id="page-76-2"></span>31. Wang, X., Yang, S., Zhang, J., Wang, M., Zhang, J., Yang, W., Huang, J., Han, X.: Transformer-based unsupervised contrastive learning for histopathological image classification. Medical image analysis **81**, 102559 (2022)
- <span id="page-76-8"></span>32. Zheng, X., Wang, Y., Wang, G., Liu, J.: Fast and robust segmentation of white blood cell images by self-supervised learning. Micron **107**, 55–71 (2018)

Image /page/77/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a segment missing, and a bookmark-like shape is inside the circle. The text below the icon reads "Check for updates" in a light gray sans-serif font.

# **FACMIC: Federated Adaptative CLIP Model for Medical Image Classification**

Yihang Wu<sup>1</sup>, Christian Desrosiers<sup>2</sup>, and Ahmad Chaddad<sup>1,2( $\boxtimes$ )</sup>

<sup>1</sup> Laboratory for Artificial Intelligence for Personalised Medicine, School of Artificial Intelligence, Guilin University of Electronic Technology, Guilin, China <EMAIL>

 $^2$  Laboratory for Imagery Vision and Artificial Intelligence,  $\rm \acute{E}TS$  Montreal, Montreal, Canada

**Abstract.** Federated learning (FL) has emerged as a promising approach to medical image analysis that allows deep model training using decentralized data while ensuring data privacy. However, in the field of FL, communication cost plays a critical role in evaluating the performance of the model. Thus, transferring vision foundation models can be particularly challenging due to the significant resource costs involved. In this paper, we introduce a federated adaptive Contrastive Language Image Pretraining (CLIP) model designed for classification tasks. We employ a light-weight and efficient feature attention module for CLIP that selects suitable features for each client's data. Additionally, we propose a domain adaptation technique to reduce differences in data distribution between clients. Experimental results on four publicly available datasets demonstrate the superior performance of FACMIC in dealing with real-world and multisource medical imaging data. Our codes are available at [https://github.com/AIPMLab/FACMIC.](https://github.com/AIPMLab/FACMIC)

**Keywords:** Federated learning · Domain adaptation · Medical imaging

# 1 Introduction

The success of deep learning (DL) highly depends on the availability of large amounts of data for training. However, there has been a growing focus on data privacy and security in recent years, with some organizations implementing regulations and laws such as the EU General Data Protection Regulation (GDPR) [\[1](#page-86-0)]. Collecting raw data is often impractical in this case, which poses a challenge to the feasibility of centralized DL approaches. Federated learning (FL) has emerged as a new distributed learning method to deal with this challenge and has been widely adopted in various applications [\[2\]](#page-86-1). FL enables model aggregation without directly accessing the raw user data from different clients. As pioneering work in FL, FedAVG efficiently combines distributed information through a

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_50) 50.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 531–541, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_50)\_50

simple but effective averaging algorithm [\[3\]](#page-86-2). This method ensures that raw data remain on the local client, preserving data privacy and security.

The performance of FL models is typically influenced by two main factors: data distribution shifts and communication costs [\[4](#page-86-3)]. Shifts in data distribution can negatively impact model prediction accuracy, particularly in the medical field, where variations in imaging equipment can introduce discrepancies. In most FL solutions, communication costs are proportional to the number of model parameters to transmit. This impedes the use of large Vision Language Models (VLMs) such as CLIP that contain more than  $10^8$  parameters (ViT-B/32) [\[5\]](#page-86-4).

Work on FL with foundation models has started recently. In [\[6\]](#page-86-5), the authors proposed sharing the prompts instead of models to reduce communication costs. An important limitation of this approach is that it does not address the issue of heterogeneity in the data distribution. In [\[7\]](#page-86-6), a specialized model compression technique is introduced to reduce transmission costs. However, this technique still has substantial computational costs. Furthermore, the results in [\[8\]](#page-86-7) show that foundation models like CLIP or BLIP fail for medical image classification tasks (e.g., 52.7% classification recall for CLIP using the ISIC2019 dataset). This leads us to consider how federated foundation models can be enhanced in terms of effectiveness and efficiency. In this paper, we propose the Federated adaptive CLIP model for the medical image classification (FACMIC) task. FACMIC adds a light-weight feature-attention module on top of CLIP to help the model focus on useful features in the data for each client. It also introduces a domain adaptation (DA) strategy to minimize data discrepancies between clients. Our model can quickly converge and achieve improved performance through adaptation, which greatly reduces training time. The contributions of our work are summarized as follows.

- We propose a novel federated adaptive CLIP model that combines feature attention with an adaptation technique to address both problems of communication costs and distribution discrepancy. Although past studies have applied FL in medical imaging, to our knowledge, we are the first to explore FL on VLMs in this context.
- We test the performance of our method in both real-world and simulated multi-source cases, and show its superior performance compared to state-ofthe-art approaches for brain tumor and skin cancer classification tasks.

# 2 Material and Methods

### 2.1 Problem Formulation

In our federated learning setting, we have a set of N clients  $\{C_1, C_2, \cdots, C_N\}$ , each client  $C_i$  having a private data set  $\mathcal{D}_i = \{(\mathbf{x}_{i,j}, y_{i,j})\}_{j=1}^{n_i}$ . As in similar studies [\[9](#page-86-8)], we assume that the data of separate clients have the same input and output space, but follow different distributions, i.e.,  $P(\mathcal{D}_{i'}) \neq P(\mathcal{D}_i)$ ,  $\forall i' \neq i$ . Each dataset  $\mathcal{D}_i$  consists of three non-overlapping parts, namely a training set  $\mathcal{D}_i^{train}$ , a validation set  $\mathcal{D}_i^{val}$  and a test set  $\mathcal{D}_i^{test}$ . Our goal is to train a robust global model  $f_{\theta}(\cdot)$  while preserving data privacy and security. This model should provide good testing performance on the test data of every client, i.e.,

Image /page/79/Figure/1 description: This diagram illustrates a federated learning framework for multimodal medical image analysis. It shows multiple clients (Client 1 to Client N) and a global server. Each client processes medical images and text prompts, such as "A picture of a {\"glioma tumor}\". Both text and image encoders are frozen. A local attention module in each client is fine-tuned, indicated by a flame icon. The output from the image encoder (I) is processed through the local attention module, resulting in a modified representation (I-tilde). A contrastive loss (Lcontr) is calculated. The clients upload their attention weights (a1 to aN) to the global server. The global server aggregates these weights and uses a global attention module to refine them. An adaptation loss (LDA) is calculated between the client outputs and the global server's output. The diagram includes a legend explaining symbols for broadcast, upload, aggregate, element-wise multiplication (torch.mul), frozen, and fine-tuned.

<span id="page-79-0"></span>**Fig. 1.** The proposed FACMIC framework. Each client trains its model separately, optimizing only the parameters of its local attention module  $(a_i)$  using contrastive and domain adaptation losses. After receiving the local client parameters, the server aggregates them into a global attention module (a*global* ) whose parameters are broadcasted back to clients.

$$
\min_{f} \frac{1}{N} \sum_{i=1}^{N} \frac{1}{n_i^{test}} \sum_{j=1}^{n_i^{test}} \ell(f_{\theta}(\mathbf{x}_{i,j}^{test}), y_{i,j}^{test}), \tag{1}
$$

based on a given loss function  $\ell$ . For generalization, we assume that there exist  $Q$ different clients  $\{M_1, M_2, \cdots, M_Q\}$  with data  $\mathcal{D}_i^M = \{(\mathbf{x}_{i,j}, y_{i,j})\}_{j=1}^{n_i}, n_i$  being the number of samples in each client. Our goal is for the model to achieve a good performance on clients that *were excluded* from the local training stage, i.e.,

$$
\min_{f} \frac{1}{M} \sum_{i=1}^{M} \frac{1}{m_i} \sum_{j=1}^{m_i} \ell(f_{\theta}(\mathbf{x}_{i,j}), y_{i,j}).
$$
\n(2)

### 2.2 Our Federated Adaptive CLIP framework

Our federated learning framework for CLIP-based medical image classification is illustrated in Fig. [1.](#page-79-0) This framework comprises three key components: a feature attention module for efficient FL, a feature adaptation strategy that addresses the problem of data distribution shifts across different clients, and a global aggregation strategy to combine the learned information from multiple clients. We present these components in what follows.

**Training the Attention Module.** We use a pretrained CLIP model comprising an image encoder  $e_I(\cdot)$  and a text encoder  $e_T(\cdot)$ , to extract features from the data for each client  $C_i$ . For a training example  $\mathbf{x}_j \in \mathcal{D}_i^{train}$ , we denote as  $\mathbf{I}_i = e_I(\mathbf{x}_i) \in \mathbb{R}^D$  the D-dimensional vector of image features. For text features,

we use the standard prompt "a picture of a  ${class}$ " as input to the text encoder to obtain features  $\mathbf{T}_i = e_T(\mathbf{x}_i) \in \mathbb{R}^D$ .

Pretrained foundation models hold the ability to extract a rich set of features, however, not all of those are suitable for learning a specific task. This is particularly true for detecting and classifying abnormal regions such as lesions in medical images, as these regions are absent in normal images and typically represent a small part of the image. To identify the regions of focus for locallytrained models, we introduce a client feature attention module, denoted as  $a_i(\cdot)$ . This attention module takes as input image features **I** and returns an attention mask  $a_i(I) \in [0,1]^D$ . This mask is then used to generate masked images features  $\widetilde{\mathbf{I}} = a_i(\mathbf{I}) \otimes \mathbf{I}$ , where  $\otimes$  is the Hadamard (element-wise) product.

We measure the probability that an example  $x_j$  belongs to a class c using the cosine similarity between the image features of  $\mathbf{x}_i$  and the text features  $\mathbf{T}_c$ corresponding to the prompt of  $c$ :

<span id="page-80-0"></span>
$$
p(\mathbf{Y}=c \mid \mathbf{x}_j) = \frac{\exp(s_{j,c}/\tau)}{\sum_{c'=1}^{K} \exp(s_{j,c'}/\tau)}, \text{ with } s_{j,c} = \frac{\langle \widetilde{\mathbf{I}}_j, \mathbf{T}_c \rangle}{\|\widetilde{\mathbf{I}}_j\| \cdot \|\mathbf{T}_c\|}
$$
(3)

where  $\tau$  is the softmax temperature parameter.

Keeping the image and text encoders frozen, we train the local adapter modules by minimizing a contrastive loss  $\mathcal{L}_{contr}$  that pushes together the image and text features from the same training example and pulls apart non-matching ones. Following [\[10](#page-86-9)], we compute the contrastive loss over batches of size B. Let **S** be the  $B \times B$  matrix where  $s_{j,j'}$  is the cosine similarity between image features  $\mathbf{I}_j$  and  $\mathbf{T}_{j'}$ , as measured in Eq. [\(3\)](#page-80-0). We compute an image probability matrix  $\mathbf{P} = \text{softmax}(\mathbf{S}/\tau) \in [0,1]^{B \times B}$  and a text probability matrix  $\mathbf{Q} = \text{softmax}(\mathbf{S}^\top / \tau) \in [0, 1]^{B \times B}$ . The contrastive loss is then formulated as follows:

$$
\mathcal{L}_{contr} = -\frac{1}{B} \sum_{j=1}^{B} \frac{1}{2} \Big( \log p_{j,j} + \log q_{j,j} \Big). \tag{4}
$$

**Local Feature Adaptation.** Discrepencies in the local data distribution of clients may affect the training of the global model via federated learning. To address this problem, we propose a client-specific feature adaptation strategy based on the Local Maximum Mean Discrepancy (LMMD) method [\[11](#page-86-10)]. This domain adaptation method aligns the class-wise distribution statistics of the data from a source domain  $\mathcal{D}_s$  and a target domain  $\mathcal{D}_t$ , by minimizing the following loss:

<span id="page-80-1"></span>
$$
\mathcal{L}_{DA} = \frac{1}{K} \sum_{c=1}^{K} \left\| \sum_{\mathbf{x}_i^s \in \mathcal{D}_s} \omega_{i,c}^s \phi(\mathbf{x}_i^s) - \sum_{\mathbf{x}_j^t \in \mathcal{D}_t} \omega_{j,c}^t \phi(\mathbf{x}_j^t) \right\|_{\mathcal{H}}^2, \quad \omega_{i,c}^{s|t} = \frac{\mathbb{1}(y_i = c)}{\sum_{y_j \in \mathcal{D}^{s|t}} \mathbb{1}(y_j = c)}.
$$
\n(5)

Here,  $\phi(\cdot)$  is a mapping function to a Hilbert space  $\mathcal{H}$ , while  $\omega_{i,c}^s$ ,  $\omega_{j,c}^t$  are weights that measure the membership of example  $\mathbf{x}_i^s$  and  $\mathbf{x}_j^t$  in class c.

In our setting, we suppose that the shift occurs only on the image features, thus  $\mathbf{x} = \mathbf{I}$  in Eq. [\(5\)](#page-80-1). Furthermore, since we cannot compute  $\phi(\cdot)$  directly (as it maps features to a high-dimensional space), we use the kernel trick and reformulate the loss as

$$
\mathcal{L}_{DA} = \frac{1}{K} \sum_{c=1}^{K} \left[ \sum_{i=1}^{n_s} \sum_{j=1}^{n_s} \omega_{i,c}^s \omega_{j,c}^s k(\mathbf{I}_i^s, \mathbf{I}_j^s) + \sum_{i=1}^{n_t} \sum_{j=1}^{n_t} \omega_{i,c}^t \omega_{j,c}^t k(\mathbf{I}_i^t, \mathbf{I}_j^t) - 2 \sum_{i=1}^{n_s} \sum_{j=1}^{n_t} \omega_{i,c}^s \omega_{j,c}^t k(\mathbf{I}_i^s, \mathbf{I}_j^t) \right] \quad (6)
$$
[-8pt]

[-5pt]

where  $n_s$ ,  $n_t$  are the number of samples in the source domain and target domain, respectively, and  $k(\cdot, \cdot) = \langle \phi(\cdot), \phi(\cdot) \rangle$  is the kernel function.

Although we defined our domain adaptation loss, we still need to specify the source and target domains used in this formulation. When performing a local adaption for client  $C_i$ , we set the source domain to be the data of this client, i.e.,  $\mathcal{D}_s = \mathcal{D}_i$ . However, to preserve privacy, we cannot exchange data directly between clients, therefore we cannot use the data of other clients for the target domain. Instead, we use a global set of unlabeled images as the target domain data. This constraint can be easily satisfied since there are many publicly available datasets in medical imaging and no labels are needed for this reference data.

To compute the weights  $\omega_{i,c}^{s|t}$  in Eq. [\(5\)](#page-80-1), we employ a pseudo-label strategy to estimate the class labels. Specifically, we obtain the class probabilities of a target image  $\mathbf{I}^t_j$  using Eq. [\(3\)](#page-80-0), and assign this image to the class with the highest probability. Our final loss to update the feature attention parameters of each client is the combination of the contrastive loss and domain adaptation loss,

$$
\mathcal{L} = \mathcal{L}_{contr} + \lambda \mathcal{L}_{DA},\tag{7}
$$

where  $\lambda$  is a hyper-parameter controlling the trade-off between these two loss terms.

**Global Aggregation.** The last part of our proposed FACMIC framework is the aggregation strategy to combine the parameters of different clients into a single global model. This strategy works as follows. In each round, each client C*<sup>i</sup>* uploads its attention module parameters  $\theta_i^a$  to the server. Thereafter, the server combines these parameters into a single vector  $\theta^a_{global}$  using a weighted average

<span id="page-81-0"></span>
$$
\theta_{global}^{a} = \sum_{i=1}^{N} \omega_i \cdot \theta_i^{a}, \quad \omega_i = \frac{n_i^{train}}{\sum_{i'=1}^{N} n_i^{train}}.
$$
\n
$$
(8)
$$

Next, the server broadcasts the global attention module parameters back to each client. Since the attention module has only a small amount of parameters compared to the CLIP encoders, this strategy has very low communication and computational costs.

| Clients | $\alpha = 0.3$ |      | $\alpha = 0.6$ |      | $\alpha = 0.9$ |      |
|---------|----------------|------|----------------|------|----------------|------|
|         | BT             | SC   | BT             | SC   | BT             | SC   |
| Client1 | 2075           | 218  | 1355           | 387  | 1480           | 1156 |
| Client2 | 389            | 1374 | 908            | 487  | 423            | 335  |
| Client3 | 406            | 647  | 607            | 1365 | 967            | 748  |
| Global  | 394            | 118  | 394            | 118  | 394            | 118  |

<span id="page-82-0"></span>**Table 1.** Samples of each client under non-iid conditions for BT and SC datasets.

## 3 Experiments

### 3.1 Datasets

**Brain Tumor.** We conduct experiments on a public MRI brain tumor classification dataset, denoted as BT [\[12](#page-86-11)]. BT has four different classes, namely glioma tumor, meningioma tumor, no tumor, and pituitary tumor. The training set has 2,870 samples, while the testing set has 394 samples.

**Skin Cancer.** We also use a public skin cancer (denoted as SC) dataset, obtained from The International Skin Imaging Collaboration (ISIC) [\[13](#page-86-12)]. The data set contains the following diseases: actinic keratosis (AK), basal cell carcinoma (BCC), dermatofibroma (DF), melanoma (MEL), nevus (NV), pigmented benign keratosis (PBK), seborrheic keratosis (SK), squamous cell carcinoma (SCC) and vascular lesion (VL). The training set contains 2,239 samples, and the testing set 118 samples. For both the BT and SC dataset, we divide the training set into three clients, following both iid and non-iid conditions.

**Real Multi-source Skin Cancer.** We build this dataset (denoted as Real) from three sources, SC, HAM10000, and ISIC2019 [\[14](#page-86-13)[–16](#page-87-0)], where each source is treated as an individual client. Since ISIC2019 lacks a test set, we divided it into two parts, one for training and the other for testing, with a ratio of 8:2. We selected the common classes for this dataset: AK, BCC, DF, MEL, NV, PBK, and VL. The global testing set contains 6,233 samples, while Client 1 (SC) has 1,971 samples, Client 2 (ISIC2019) holds 19,766 samples and Client 3 (HAM10000) possesses 8,512 samples. For the BT, SC and Real datasets, each client's data was then divided into three subsets: a training set, a validation set, and a testing set  $(8:1:1)$ . Finally, we evaluated the global model using the global testing set.

**Data Under iid Condition.** For BT and SC, we randomly allocate the training set to each client with an equal number of samples. After this split, in BT, every client receives 956 samples, while in SC, every client holds 746 samples.

<span id="page-83-0"></span>**Table 2.** Global testing accuracy (ACC%) and balanced accuracy (BACC%) (highest) for BT, SC and Real dataset. The BACC metric is only adopted for Real dataset while the other results indicate ACC. The best results are marked in **bold**. Average indicates the mean value of ACC on all clients. Note: ✘ indicates no partition on data.

| Method      | α = 0.3      |              | α = 0.6      |              | α = 0.9      |              | iid          |              | Average      |              | Real         |              |
|-------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|             | <b>BT</b>    | SC           | <b>BT</b>    | SC           | <b>BT</b>    | SC           | <b>BT</b>    | SC           | <b>BT</b>    | SC           | ACC          | <b>BACC</b>  |
| Centralized | <b>X</b>     | <b>X</b>     | <b>X</b>     | <b>X</b>     | <b>X</b>     | <b>X</b>     | <b>X</b>     | <b>X</b>     | 70.30        | <b>61.01</b> | 60.81        | 47.80        |
| FedAVG      | 66.24        | 48.30        | 66.49        | 49.15        | 64.21        | 54.23        | 68.52        | 49.15        | 66.36        | 50.21        | 60.24        | 50.96        |
| FedProx     | 66.24        | 48.30        | 63.95        | 51.69        | 65.23        | 49.15        | 70.05        | 50.84        | 66.37        | 49.99        | 59.52        | 48.89        |
| <b>MOON</b> | 67.25        | 50.84        | 63.96        | 50.00        | 65.48        | 53.38        | 69.29        | 47.45        | 66.49        | 50.42        | 59.95        | 50.90        |
| FedFocal    | 67.00        | 49.15        | 65.48        | 49.15        | 67.26        | 54.23        | 64.21        | 51.69        | 65.99        | 51.05        | 59.97        | 49.80        |
| FedCLIP     | 68.78        | 53.38        | 66.24        | 55.93        | 67.26        | 54.23        | 66.50        | 50.0         | 67.19        | 53.38        | 58.43        | 51.17        |
| <b>Ours</b> | <b>82.74</b> | <b>56.78</b> | <b>82.23</b> | <b>58.47</b> | <b>81.73</b> | <b>56.78</b> | <b>82.23</b> | <b>57.62</b> | <b>82.23</b> | <b>57.41</b> | <b>72.37</b> | <b>63.61</b> |

**Non-iid Data.** We employ a Dirichlet distribution with concentration parameters  $[\alpha, \alpha, \alpha]$ ,  $\alpha \in \{0.3, 0.6, 0.9\}$ , as a conjugate prior to generate non-iid data for the clients. For each class, we sample from this distribution and use the sampled values (one for each client) to divide the class examples among the clients. Table [1](#page-82-0) shows the results after division for the BT and SC datasets.

### 3.2 Implementation Details

The ViT-B/16 pre-trained model is adopted as the CLIP backbone in our framework. Our light-weight attention module is composed of five layers: a first linear layer, a batch normalization layer, a LeakyReLU layer, a second linear layer, and a softmax activation function. During training, we keep the CLIP encoders frozen and optimize only the attention module's parameters using Adam with beta parameters set to 0.9 and 0.98, a weight decay of 0.02, a fixed learning rate of  $5\times10^{-5}$ , and a batch size of 32.

For FL, we set the number of global training rounds to 100 for BT, 50 for SC and 50 for Real. For each round, we perform a single epoch of local training and aggregate the parameters of all clients. As image preprocessing, we resized the images to  $224 \times 224$ , and normalized their intensity using z-score normalization for both the training and testing phases [\[17\]](#page-87-1). For the LMMD loss  $\mathcal{L}_{DA}$ , we adopt a Gaussian kernel with a bandwidth set to the median pairwise squared distances in the training data following [\[11\]](#page-86-10), and use a weight of  $\lambda = 1$  for this loss term. The environment used for experiments is based on the Windows 11 operating system, and features an Intel 13900KF CPU with 128 GB of RAM and an RTX 4090 GPU.

### 3.3 Comparison with State-of-the-Art Methods

To have a comprehensive comparison, we include several related approaches in our experiments: FedAVG [\[3](#page-86-2)], MOON [\[18\]](#page-87-2), FedProx [\[19](#page-87-3)], FedFocal [\[20](#page-87-4)], Fed-CLIP [\[9](#page-86-8)] and a centralized method. FedAVG fine-tunes and aggregates all the

Image /page/84/Figure/1 description: This image contains four line graphs, each plotting "Global ACC(%)" on the y-axis against "Epoch" on the x-axis, ranging from 0 to 100. Each graph displays multiple lines representing different methods: "Ours", "Ours-w/o DA", "FedAVG", "FedProx", "MOON", "FedFocal", "FedClip", and "Centralized". The four graphs correspond to different values of alpha: "Epoch(α=0.3)", "Epoch(α=0.6)", "Epoch(α=0.9)", and "Epoch(iid)". In all graphs, the "Ours" line consistently shows the highest accuracy, reaching around 80% or more, while the "Centralized" line is the second highest. The other methods generally perform at lower accuracy levels, mostly between 50% and 70%, with some fluctuations.

<span id="page-84-1"></span><span id="page-84-0"></span>**Fig. 2.** Global testing accuracy (%) for each round on the BT dataset.

**Table 3.** Impact of the domain adaptation loss (ACC%).

<span id="page-84-2"></span>

| Table 4. Impact of batch size (ACC%). |  |
|---------------------------------------|--|
|---------------------------------------|--|

| $\mathcal{L}_{DA}$ | $\alpha=0.3$ | $\alpha=0.6$ | $\alpha=0.9$ | <i>iid</i> | Batch size | 4     | 8     | 16    | 32    | Avg   |
|--------------------|--------------|--------------|--------------|------------|------------|-------|-------|-------|-------|-------|
| $\checkmark$       | 82.48        | 82.23        | 81.73        | 82.23      | BT (iid)   | 79.69 | 80.20 | 81.22 | 82.23 | 80.84 |
| $\boldsymbol{X}$   | 75.63        | 75.12        | 74.62        | 74.62      | Real       | 63.39 | 67.32 | 69.68 | 72.37 | 68.19 |

parameters of the CLIP image and text encoders. MOON extends FedAVG with a contrastive loss between the previous model and the current model. The Fed-Prox approach adds a proximal term to FedAVG that allows having slight differences between clients and the server. FedFocal replaces the standard CE loss with a focal loss for FedAVG. FedCLIP adds an adapter to the CLIP model and only considers the parameters of this adapter during the fine-tuning and aggregation steps. Finally, the centralized method is designed with only one client holding all the training data. For a fair comparison, the same experimental setting described above is used for all tested methods. We evaluate performance using classification accuracy (ACC) as the primary metric. Additionally, due to the class imbalance in the Real dataset, we also consider balanced accuracy (BACC) for a more comprehensive assessment [\[21\]](#page-87-5). *Additional implementation details and visualization results can be found in the Supplemental materials*.

Table [2](#page-83-0) reports the global classification ACC and BACC for the BT, SC and Real datasets. As can be seen, our FACMIC approach yields a better performance than other FL methods across all datasets, outperforming the second-best federated approach by 15.40%, 4.03% and 12.13% in ACC on BT, SC and Real, respectively. FACMIC also achieves the highest BACC in the Real dataset, outperforming the second-best FL approach by 12.44%. Figure [2](#page-84-0) shows the global test accuracy measured in each communication round. Our method demonstrates a notable increase in accuracy with a minimal number of epochs, highlighting its effectiveness. Specifically, when aggregating parameters in Eq. [8,](#page-81-0) it gives a larger weight to clients with more training samples. In the non-iid setting, this enables the global model to learn from the most knowledgeable clients, preventing performance degradation. The lower performance of other methods in the iid setting could be due to the need of fine-tuning the entire image encoder or to the shallower adaptation module architecture (FedCLIP). Our findings suggest that

| FedAVG | FedProx | MOON  | FedFocal | Centralized | FedCLIP | Ours         |
|--------|---------|-------|----------|-------------|---------|--------------|
| 84.18  | 84.72   | 82.32 | 79.99    | 86.12       | 91.7    | <b>94.42</b> |

<span id="page-85-0"></span>**Table 5.** Global testing accuracy (%) using BT2 dataset.

the CLIP model can achieve comparable performance in some medical image classification tasks.

### 3.4 Ablation Study

To validate the effectiveness and generalizability of our model, a series of ablation studies were conducted following the same experimental setting. As reported in Table [3,](#page-84-1) adding the domain adaptation loss  $\mathcal{L}_{DA}$  leads to a better classification accuracy in the BT dataset, in all situations. These results demonstrate the need to address the problem of shifting data distribution among clients.

We also studied the performance of our method for different batch sizes (from 4 to 32), as using large batches is not practical for less capable devices. For sizes of 4 and 8, the adaptation loss is rescaled by a factor of 1/10 since it is too large compared with the contrastive loss in those cases. Table [4](#page-84-2) shows the global testing ACC using BT (iid) and Real dataset. As one can see, our model is robust to batch size on the BT data, however, using a too small batch size for the large-scale SC data can result in negative adaptation.

To assess our method's generalization ability, we use the fine-tuned global model trained on the BT dataset under iid condition to perform classification directly on another brain tumor dataset (BT2) without any fine-tuning. The BT2 dataset comprises 7023 samples obtained from figshare, SARTAJ, and Br35H [\[22](#page-87-6)[–25](#page-87-7)], and has the same classes as the BT dataset. We tested these methods using the whole dataset. As reported in Table [5,](#page-85-0) our method achieves the highest generalization accuracy of 94.42% on BT2.

### 4 Conclusion

We explored the usefulness of VLMs for medical imaging in FL and presented a novel FACMIC framework that combines a feature attention module to reduce communication costs and a domain adaptation strategy to minimize data distribution differences between each client. Experimental results in brain tumor and skin cancer classification tasks demonstrate the superior performance of FACMIC compared to state-of-the-art FL approaches.

**Acknowledgments.** This research was funded by the National NSFC (82260360), the Guilin (20222C264164), and the Guangxi talent (2022AC18004, 2022AC21040).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-86-0"></span>1. Paul Voigt and Axel Von dem Bussche. The eu general data protection regulation (gdpr). *A Practical Guide, 1st Ed., Cham: Springer International Publishing*, 10(3152676):10–5555, 2017.
- <span id="page-86-1"></span>2. Ahmad Chaddad, Yihang Wu, and Christian Desrosiers. Federated learning for healthcare applications. *IEEE Internet of Things Journal*, 11(5):7339–7358, 2024.
- <span id="page-86-2"></span>3. Xiang Li, Kaixuan Huang, Wenhao Yang, Shusen Wang, and Zhihua Zhang. On the convergence of fedavg on non-iid data. arXiv preprint [arXiv:1907.02189,](http://arxiv.org/abs/1907.02189) 2019.
- <span id="page-86-3"></span>4. Felix Sattler, Simon Wiedemann, Klaus-Robert Müller, and Wojciech Samek. Robust and communication-efficient federated learning from non-iid data. *IEEE transactions on neural networks and learning systems*, 31(9):3400–3413, 2019.
- <span id="page-86-4"></span>5. Rishi Bommasani, Drew A Hudson, Ehsan Adeli, Russ Altman, Simran Arora, Sydney von Arx, Michael S Bernstein, Jeannette Bohg, Antoine Bosselut, Emma Brunskill, et al. On the opportunities and risks of foundation models. arXiv preprint [arXiv:2108.07258,](http://arxiv.org/abs/2108.07258) 2021.
- <span id="page-86-5"></span>6. Tao Guo, Song Guo, Junxiao Wang, Xueyang Tang, and Wenchao Xu. Promptfl: Let federated participants cooperatively learn prompts instead of models-federated learning in age of foundation model. *IEEE Transactions on Mobile Computing*, 2023.
- <span id="page-86-6"></span>7. Sixing Yu, J Pablo Muñoz, and Ali Jannesari. Bridging the gap between foundation models and heterogeneous federated learning. arXiv preprint [arXiv:2310.00247,](http://arxiv.org/abs/2310.00247) 2023.
- <span id="page-86-7"></span>8. Joana Palés Huix, Adithya Raju Ganeshan, Johan Fredin Haslum, Magnus Söderberg, Christos Matsoukas, and Kevin Smith. Are natural domain foundation models useful for medical image classification? In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, pages 7634– 7643, January 2024.
- <span id="page-86-8"></span>9. Wang Lu, Xixu Hu, Jindong Wang, and Xing Xie. Fedclip: Fast generalization and personalization for clip in federated learning. arXiv preprint [arXiv:2302.13485,](http://arxiv.org/abs/2302.13485) 2023.
- <span id="page-86-9"></span>10. Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *International conference on machine learning*, pages 8748–8763. PMLR, 2021.
- <span id="page-86-10"></span>11. Yongchun Zhu, Fuzhen Zhuang, Jindong Wang, Guolin Ke, Jingwu Chen, Jiang Bian, Hui Xiong, and Qing He. Deep subdomain adaptation network for image classification. *IEEE transactions on neural networks and learning systems*, 32(4):1713– 1722, 2020.
- <span id="page-86-11"></span>12. Sartaj Bhuvaji, Ankita Kadam, Prajakta Bhumkar, Sameer Dedge, and Swati Kanchan. Brain tumor classification (mri). [https://www.kaggle.com/datasets/](https://www.kaggle.com/datasets/sartajbhuvaji/brain-tumor-classification-mri) [sartajbhuvaji/brain-tumor-classification-mri,](https://www.kaggle.com/datasets/sartajbhuvaji/brain-tumor-classification-mri) 2020.
- <span id="page-86-12"></span>13. Andrey Katanskiy. Skin cancer dataset. [https://www.kaggle.com/datasets/](https://www.kaggle.com/datasets/nodoubttome/skin-cancer9-classesisic/data) [nodoubttome/skin-cancer9-classesisic/data,](https://www.kaggle.com/datasets/nodoubttome/skin-cancer9-classesisic/data) 2019.
- <span id="page-86-13"></span>14. Philipp Tschandl, Cliff Rosendahl, and Harald Kittler. The ham10000 dataset, a large collection of multi-source dermatoscopic images of common pigmented skin lesions. *Scientific data*, 5(1):1–9, 2018.
- 15. Noel CF Codella, David Gutman, M Emre Celebi, Brian Helba, Michael A Marchetti, Stephen W Dusza, Aadi Kalloo, Konstantinos Liopyris, Nabin Mishra, Harald Kittler, et al. Skin lesion analysis toward melanoma detection: A challenge

at the 2017 international symposium on biomedical imaging (isbi), hosted by the international skin imaging collaboration (isic). In *2018 IEEE 15th international symposium on biomedical imaging (ISBI 2018)*, pages 168–172. IEEE, 2018.

- <span id="page-87-0"></span>16. Marc Combalia, Noel CF Codella, Veronica Rotemberg, Brian Helba, Veronica Vilaplana, Ofer Reiter, Cristina Carrera, Alicia Barreiro, Allan C Halpern, Susana Puig, et al. Bcn20000: Dermoscopic lesions in the wild. arXiv preprint [arXiv:1908.02288,](http://arxiv.org/abs/1908.02288) 2019.
- <span id="page-87-1"></span>17. Nanyi Fei, Yizhao Gao, Zhiwu Lu, and Tao Xiang. Z-score normalization, hubness, and few-shot learning. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 142–151, 2021.
- <span id="page-87-2"></span>18. Qinbin Li, Bingsheng He, and Dawn Song. Model-contrastive federated learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10713–10722, 2021.
- <span id="page-87-3"></span>19. Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. Federated optimization in heterogeneous networks. *Proceedings of Machine learning and systems*, 2:429–450, 2020.
- <span id="page-87-4"></span>20. Sarkar Dipankar, Narang Ankur, and Rai Sumit. Fed-focal loss for imbalanced data classification in federated learning. In *IJCAI*, 2020.
- <span id="page-87-5"></span>21. Kay Henning Brodersen, Cheng Soon Ong, Klaas Enno Stephan, and Joachim M Buhmann. The balanced accuracy and its posterior distribution. In *2010 20th international conference on pattern recognition*, pages 3121–3124. IEEE, 2010.
- <span id="page-87-6"></span>22. Msoud Nickparvar. Brain tumor mri dataset. [https://www.kaggle.com/dsv/](https://www.kaggle.com/dsv/2645886) [2645886,](https://www.kaggle.com/dsv/2645886) 2021.
- 23. Jun Cheng. brain tumor dataset. [https://figshare.com/articles/dataset/brain](https://figshare.com/articles/dataset/brain_tumor_dataset/1512427) tumor [dataset/1512427,](https://figshare.com/articles/dataset/brain_tumor_dataset/1512427) 4 2017.
- 24. Sartaj Bhuvaji, Ankita Kadam, Prajakta Bhumkar, Sameer Dedge, and Swati Kanchan. Brain tumor classification (mri). [https://www.kaggle.com/dsv/1183165,](https://www.kaggle.com/dsv/1183165) 2020.
- <span id="page-87-7"></span>25. Ahmed Hamada. Brain tumor mri dataset. [https://www.kaggle.com/datasets/](https://www.kaggle.com/datasets/ahmedhamada0/brain-tumor-detection?select=no) [ahmedhamada0/brain-tumor-detection?select=no,](https://www.kaggle.com/datasets/ahmedhamada0/brain-tumor-detection?select=no) 2020.

Image /page/88/Picture/0 description: A square button with a light gray background has a circular progress indicator in the upper half. Inside the circle, a gray flag icon points to the right. Below the circle, the text "Check for updates" is displayed in a dark gray sans-serif font.

# **FastSAM3D: An Efficient Segment Anything Model for 3D Volumetric Medical Images**

Yiging Shen<sup>1</sup>, Jingxing Li<sup>1</sup>, Xinyuan Shao<sup>1</sup>, Blanca Inigo Romillo<sup>1</sup>, Ankush Jindal<sup>1</sup>, David Dreizin<sup>2( $\boxtimes$ )</sup>, and Mathias Unberath<sup>1( $\boxtimes$ )</sup>

<sup>1</sup> Johns Hopkins University, Baltimore, MD 21218, USA {yshen92,unberath}@jhu.edu <sup>2</sup> University of Maryland School of Medicine and R Adams Cowley Shock Trauma

Center, Baltimore, MD 21201, USA

<EMAIL>

**Abstract.** Segment anything models (SAMs) are gaining attention for their zero-shot generalization capability in segmenting objects of unseen classes and in unseen domains when properly prompted. Interactivity is a key strength of SAMs, allowing users to iteratively provide prompts that specify objects of interest to refine outputs. However, to realize the interactive use of SAMs for 3D medical imaging tasks, rapid inference times are necessary. High memory requirements and long processing delays remain constraints that hinder the adoption of SAMs for this purpose. Specifically, while 2D SAMs applied to 3D volumes contend with repetitive computation to process all slices independently, 3D SAMs suffer from an exponential increase in model parameters and FLOPS. To address these challenges, we present FastSAM3D which accelerates SAM inference to 8 milliseconds per  $128 \times 128 \times 128$  3D volumetric image on an NVIDIA A100 GPU. This speedup is accomplished through 1) a novel layer-wise progressive distillation scheme that enables knowledge transfer from a complex 12-layer ViT-B to a lightweight 6-layer ViT-Tiny variant encoder without training from scratch; and 2) a novel 3D sparse flash attention to replace vanilla attention operators, substantially reducing memory needs and improving parallelization. Experiments on three diverse datasets reveal that FastSAM3D achieves a remarkable speedup of 527*.*38× compared to 2D SAMs and 8*.*75× compared to 3D SAMs on the same volumes without significant performance decline. Thus, FastSAM3D opens the door for low-cost truly interactive SAM-based 3D medical imaging segmentation with commonly used GPU hardware. Code is available at [https://github.com/arcadelab/FastSAM3D.](https://github.com/arcadelab/FastSAM3D)

**Keywords:** Foundation Model  $\cdot$  Segment Anything Model (SAM)  $\cdot$  Interactive Segmentation  $\cdot$  Model Acceleration

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_51) 51.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 542–552, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_51)\_51

## 1 Introduction

In medical image analysis, object segmentation is a key aspect of diagnosis- and prognosis-related tasks including lesion localization, tissue characterization, and volume estimation, among others [\[8](#page-96-0)[,14](#page-97-0),[21,](#page-97-1)[29\]](#page-97-2). Traditionally, deep learning models like U-Net [\[22](#page-97-3)] and variants [\[11](#page-97-4)[,15\]](#page-97-5) have excelled in specific tasks and datasets with clear and confined scope, but often demonstrate limited generalization. While some work considered interactive segmentation approaches as a means to overcome the limitations of narrowly scoped, task-specific models [\[1,](#page-96-1)[26\]](#page-97-6), the introduction of Segment Anything Model (SAM) [\[16](#page-97-7)] initiated a paradigm shift to prompt-based interactive segmentation that now provides competitive performance due to the inherent generalizability of foundation models. SAM is comprised of a pre-trained Vision Transformer (ViT) encoder [\[9\]](#page-96-2), a prompt encoder, and a lightweight decoder that facilitates multi-mask prediction via IoU-based ranking. Trained on over 1 billion masks and 11 million images, SAM adapts to new tasks without training [\[16\]](#page-97-7). Despite successes on natural images [\[10](#page-97-8)[,17](#page-97-9)], direct application of SAM to medical segmentation reveals performance gaps compared to task-specific U-Nets [\[33](#page-98-0)].

<span id="page-89-0"></span>**Table 1.** Comparison of SAM approaches regarding applicability for medical imaging, suitability for 3D volumetric data, and computational efficiency of the core components: image encoder, prompt encoder, and mask decoder. The vanilla SAM [\[16\]](#page-97-7) lacks in all criteria. MobileSAM [\[32\]](#page-98-1) improves encoder efficiency, while TinySAM [\[24](#page-97-10)] accelerates all components, but neither addresses 3D medical imaging data. MedSAM [\[18\]](#page-97-11) and **SAM-Med2D** [\[4\]](#page-96-3) are tailored for medical 2D data yet do not improve efficiency. SAM-Med3D [\[27\]](#page-97-12) handles 3D medical data but inference times for these limit or altogether preclude real-time interactive use with standard GPU hardware. Our proposed FastSAM3D meets all criteria, providing a comprehensive solution for efficient interactive medical image segmentation in volumetric 3D data.

| Method           | Medical | Volumetric3D<br>Data | Efficient<br>Image<br>Encoder | Efficient<br>Prompt<br>Encoder | Efficient Mask<br>Decoder |
|------------------|---------|----------------------|-------------------------------|--------------------------------|---------------------------|
| SAM [16]         | x       | x                    | x                             | x                              | x                         |
| MobileSAM [32]   | x       | x                    | √                             | x                              | x                         |
| TinySAM [24]     | x       | x                    | √                             | √                              | √                         |
| MedSAM [18]      | √       | x                    | x                             | x                              | x                         |
| SAM-Med2D [4]    | √       | x                    | x                             | x                              | x                         |
| SAM-Med3D [27]   | √       | √                    | x                             | x                              | x                         |
| FastSAM3D (ours) | √       | √                    | √                             | √                              | √                         |

To address this, MedSAM [\[18](#page-97-11)] and SAM-Med2D [\[4](#page-96-3)] were tailored for 2D medical data via model fine-tuning. When applied to 3D volumetric data, these approaches under-perform due to slice-wise processing [\[3](#page-96-4)[,19](#page-97-13)]. They also suffer from an increased computational cost that is proportional to the number of slices in the volume, as well as the higher input resolution. Addressing this gap,

SAM-Med3D [\[27\]](#page-97-12) introduced 3D counterparts of SAM's components and end-toend 3D training.

Existing medical SAMs also face limitations of long inference times and high computational costs stemming from the Transformer architecture [\[9](#page-96-2)]. Prior efforts accelerated 2D SAMs for natural images via approaches such as FastSAM, which employs a YOLOv8 as the image encoder  $[2,35]$  $[2,35]$  $[2,35]$ . However, this CNNbased approach exhibits limitations with small object segmentation and deviates from SAM's interactive prompting design [\[32,](#page-98-1)[35](#page-98-2)]. Attempts, more aligned with the SAM value proposition, such as MobileSAM, retain the Transformer encoder while employing distillation to transition from a larger SAM encoder to a more lightweight ViT encoder [\[32\]](#page-98-1). Following a similar approach, other works have explored several ViT variants as alternative encoders to balance efficiency and effectiveness [\[25,](#page-97-14)[34\]](#page-98-3). TinySAM further reduces computational load via postdistillation quantization [\[24](#page-97-10)]. However, these advancements remain confined to 2D natural images without delving into efficient volumetric medical segmentation. Table [1](#page-89-0) offers a systematic comparison of existing SAMs, highlighting the capabilities of our proposed method in addressing the demands of 3D medical image segmentation with enhanced efficiency across all components.

The contributions of this work are two-fold, summarized as follows: Firstly, we introduce FastSAM3D, a markedly more efficient 3D SAM for interactive volumetric medical image segmentation. Rather than costly training from scratch which also leads to the difficulty in convergence [\[12](#page-97-15)], we propose a layer-wise progressive distillation approach to transfer representational knowledge from a complex 12-layer ViT-B architecture to an efficient customized 6-layer ViT-Tiny encoder. This retains segmentation performance while significantly enhancing computational efficiency. Secondly, we propose a novel 3D sparse flash attention that replaces the standard self-attention operator in all SAM components, dramatically reducing memory footprint, and enabling parallel processing. Together, these innovations address the efficiency limitations that hinder the implementation of medical SAMs for real-time prompt-based interactive 3D segmentation.

# 2 Methods

### 2.1 Architecture Overview of FastSAM3D

We introduce FastSAM3D, a computationally efficient adaptation of SAM-Med3D [\[27](#page-97-12)], also designed specifically for efficient interactive 3D medical image segmentation. Adhering to the standard SAM paradigm [\[16](#page-97-7)], FastSAM3D is comprised of three key modules (Fig. [1\)](#page-91-0): (i) a ViT-based image encoder [\[9\]](#page-96-2) to obtain volumetric embeddings; (ii) a prompt encoder; and (iii) a mask decoder to project representations back to the segmentation mask. To achieve faster inference, FastSAM3D distills knowledge from a high-powered 12-layer ViT-B encoder to a streamlined 6-layer ViT-Tiny variant, substantially reducing computational complexity during encoding. Specifically, aside from having fewer layers, each Transformer block contains only 6 attention heads, in contrast to 12 heads per block in SAM-Med3D's ViT architecture. Moreover, we retain the feed-forward

Image /page/91/Figure/1 description: This diagram illustrates a knowledge distillation framework for medical image segmentation. The framework utilizes a Teacher model (ViT-B) and a Student model (ViT-Tiny Variant). The Teacher model, scaled by X6, consists of two Transformer blocks, each with 12 heads, incorporating MHSA and FFN layers. The Student model, scaled by X2 and X4, also uses Transformer blocks with 6 heads, featuring 3D Sparse Attention and FFN layers. A key component is the 3D Sparse Flash Attention mechanism, which takes Query, Key, and Value linear projections. The framework employs Layer-Wise Progressive Distillation, connecting the Teacher and Student models through bottlenecks. A loss function L is defined as the expectation of the sum of L2 norms between the teacher and student feature maps across k layers. A Prompt Encoder, which is frozen, and a learnable Mask Decoder are also shown. The input is volumetric data of size 128x128x128, which is embedded into patches of size 768x8x8x8. The output is a segmented medical image of size 128x128x128. The diagram also includes a legend indicating Layer Normalization, Frozen, Learnable, and Addition operations.

<span id="page-91-0"></span>**Fig. 1.** The overall framework of FastSAM3D, comprising a 6-layer ViT-Tiny variant image encoder distilled from a capable 12-layer ViT-B teacher encoder, a lightweight prompt encoder, and a mask decoder. All the self attention operators are replaced by the proposed 3D sparse attention for better efficiency.

network (FFN) within the first two transformer blocks and omit attention operations [\[9](#page-96-2)], incurring minimal impact on performance while amplifying speed [\[31\]](#page-98-4). This design choice further contributes to shorter training by requiring fewer layers to align with the teacher during our progressive distillation process.

### 2.2 Layer-Wise Progressive Distillation for the Image Encoder

As the image encoder accounts for a major portion of SAM's computational load, our first focus is transferring knowledge from the heavy ViT-B architecture to a lightweight ViT-Tiny model for efficiency gains. To avoid costly training from scratch, we follow the teacher-student distillation paradigm [\[12\]](#page-97-15) by designating the 12-layer ViT-B as the teacher model,  $f_{\text{teacher}}$ , and the 6-layer ViT-Tiny variant as the student model,  $f_{student}$ . Unlike traditional logit-level distillation [\[12\]](#page-97-15) with which all our experiments failed to converge, we propose a novel layer-wise progressive distillation method. This approach allows for a more granular and effective knowledge transfer between the student and teachers by matching the intermediate representation progressively across layers, thus making it easier for optimization. Formally, let  $f_{\text{teacher}}^{(i)}(\mathbf{x})$  and  $f_{\text{student}}^{(j)}(\mathbf{x})$  denote layer  $i = 1, \dots, 12$ and  $j = 1, \dots, 6$  outputs for an input  $\mathbf{x} \in \mathbb{R}^{128 \times 128 \times 128}$  from the 12-layer teacher and 6-layer student respectively. The objective of our layer-wise progressive distillation becomes:

<span id="page-91-1"></span>
$$
\mathcal{L} = \mathbb{E}_{\mathbf{x}} \Big( \frac{1}{k} \sum_{i=1}^{k} \| f_{\text{teacher}}^{(2i)}(\mathbf{x}) - f_{\text{student}}^{(i)}(\mathbf{x}) \| \Big), \tag{1}
$$

where  $\|\cdot\|$  denotes the L2-norm,  $k$  varies from 1 to 6 based on current and total training iterations:

<span id="page-92-0"></span>
$$
k = \lceil \frac{\#(\text{Current Iteration}) \times 6}{\#(\text{Total Iterations})} \rceil,
$$
\n(2)

where  $\lceil \cdot \rceil$  is the upper rounding operator. This enables progressive alignment of student and teacher intermediate representations. After finishing layer-wise distillation, we perform logit-level distillation to fit predictions further. In Eq. [1,](#page-91-1)  $\mathbb{E}(\cdot)$  represents the expectation over all possible images.

### 2.3 3D Sparse Flash Attention

As we observe that the attention operators take up the largest proportion of computation, we introduce a 3D sparse flash attention operator to further enhance efficiency. Specifically, our 3D sparse flash attention scheme supplants the traditional self-attention operation in both the encoder and decoder, integrating extended receptive fields inspired by dilated convolutions [\[30](#page-98-5)] with the computational agility achieved by flash attention [\[5,](#page-96-6)[6\]](#page-96-7).

**3D Sparse Attention.** The 3D sparse attention mechanism aims to expand the receptive field across volumetric data while effectively managing the computational load. Traditional attention mechanisms tend to escalate in computational demand proportional to the increase in data volume, particularly challenging for 3D volumetric data due to its large number of tokens [\[9](#page-96-2),[23\]](#page-97-16). To address this, our approach segments the input token sequence into equally sized partitions of *w* and applies a strategic sparsification across these segments [\[7\]](#page-96-8). This involves selectively sampling data points at the determined intervals with length *r*, thereby diminishing the overall number of tokens subjected to the attention process. This allows for more efficient computation by focusing attention on fewer yet representative tokens. Formally, the 3D sparse attention mechanism can be formulated as computing the attention over each segment as follows:

$$
\hat{S}_i = [S_i, S_{i+r}, \dots, S_{i+(w-1)r}], \tag{3}
$$

where  $S_i$  represents the selectively sampled segment, ensuring that the model's attention is distributed across a sparse set of points, thereby reducing computational demands without sacrificing the depth of contextual analysis.

**Enhancing Efficiency Through Parallel Processing with Flash Attention.** We enhance efficiency by processing each segment in 3D sparse attention independently, enabling parallel operations that significantly boost computational throughput. By incorporating flash attention  $[5,6]$  $[5,6]$  $[5,6]$ , our model optimizes the functionality of parallel attention heads, substantially reducing the time and memory overhead associated with simultaneous processing activities.

**Overall Processing Procedure.** The 3D sparse flash attention operator, integral to both the image encoder and mask decoder, operates through a sequence of orchestrated steps as follows. The process starts with the sparsification step, wherein the input sequence undergoes partitioning into sparse segments. Subsequently, the attention operation ensues, wherein the previous segments are subjected to the flash attention for parallelization  $[5,6]$  $[5,6]$  $[5,6]$ . The focus of this stage is on harnessing the reduced sequential computation and memory optimization capabilities of flash attention. The final phase is recomposition [\[7](#page-96-8)], where the discrete outputs procured from the flash attention are reassembled to form the final encoded representation. This stage ensures that the final encoded representation has an identical dimension to its input.

<span id="page-93-0"></span>**Table 2.** Performance comparison of 2D and 3D SAM approaches in terms of Dice score. We measure the performance at 1, 3, 5, and 10 point prompts (pt). SAM-Med3D and our FastSAM3D are evaluated in a 3D context, whereas SAM, MobileSAM, TinySAM, MedSAM and SAM-Med2D are applied independently to all 2D slices of the entire 3D volume. Notably, FastSAM3D demonstrates competitive performance with SAM-Med3D and shows enhanced Dice scores relative to all its 2D counterparts, highlighting the effectiveness of our approach. The best performance is shown in **red** and boldface, while the second best is in blue.

| Dim | Method         | AMOS [13] |       |       |       | TotalSegmentator [28] |       |       |       | BraTS [20] |       |       |       |
|-----|----------------|-----------|-------|-------|-------|-----------------------|-------|-------|-------|------------|-------|-------|-------|
|     |                | 1pt       | 3pt   | 5pt   | 10pt  | 1pt                   | 3pt   | 5pt   | 10pt  | 1pt        | 3pt   | 5pt   | 10pt  |
| 2D  | SAM [16]       | 0.049     | 0.093 | 0.114 | 0.145 | 0.202                 | 0.279 | 0.311 | 0.348 | 0.108      | 0.192 | 0.217 | 0.237 |
|     | MobileSAM [32] | 0.041     | 0.056 | 0.063 | 0.070 | 0.149                 | 0.170 | 0.182 | 0.212 | 0.079      | 0.132 | 0.156 | 0.186 |
|     | TinySAM [24]   | 0.049     | 0.077 | 0.089 | 0.101 | 0.171                 | 0.225 | 0.243 | 0.262 | 0.103      | 0.165 | 0.187 | 0.211 |
|     | MedSAM [18]    | 0.004     | 0.051 | 0.060 | 0.074 | 0.006                 | 0.069 | 0.090 | 0.111 | 0.008      | 0.059 | 0.064 | 0.071 |
|     | SAM-Med2D [4]  | 0.097     | 0.127 | 0.129 | 0.132 | 0.008                 | 0.081 | 0.100 | 0.128 | 0.013      | 0.076 | 0.082 | 0.084 |
| 3D  | SAM-Med3D [27] | 0.289     | 0.386 | 0.418 | 0.448 | 0.252                 | 0.400 | 0.463 | 0.522 | 0.328      | 0.395 | 0.418 | 0.446 |
|     | FastSAM3D      | 0.273     | 0.368 | 0.402 | 0.437 | 0.250                 | 0.378 | 0.445 | 0.519 | 0.333      | 0.401 | 0.421 | 0.445 |

## 3 Experiments

**Implementation Details.** Our method as well as all baseline methods are implemented in Python 3.9 and PyTorch 2.1.0. The computational environment for our experiments is standardized across all methods, utilizing an NVIDIA A100 GPU with 40 Gb of memory. For the layer-wise progressive distillation, we set the total training iteration number in Eq. [\(2\)](#page-92-0) to 36. Training is facilitated by the Adam optimizer, with a learning rate of  $5 \times 10^{-3}$  and a batch size of 16. For evaluation metrics, we use the Dice score to measure segmentation performance. We also report inference time, floating point operations (FLOPs), and memory cost to quantify the computational complexity.

<span id="page-94-0"></span>**Table 3.** Comparison of the computational efficiency with respect to the encoder and decoder. We report the time (ms), FLOPs (G), and memory (Gb), alongside acceleration factors relative to 2D SAM  $[16]$  and 3D SAM-Med3D  $[27]$  $[27]$ . For 2D SAMs, we compute the time to process all the slices within volumetric data. The best results are highlighted in **bold** if statistically different from the second best result ( $p < 0.01$ ).

| Dim | Method         | Resolution      | Encoder   |             |             | Decoder   |             |             | Acceleration   |              |
|-----|----------------|-----------------|-----------|-------------|-------------|-----------|-------------|-------------|----------------|--------------|
|     |                |                 | Time (ms) | FLOPs (G)   | Memory (Gb) | Time (ms) | FLOPs (G)   | Memory (Gb) | To 2D ↑        | To 3D ↑      |
| 2D  | SAM [16]       | 1024 × 1024     | 3980      | 369.0       | 7.87        | 239       | 3.0         | 5.57        | 1.00×          | /            |
|     | MobileSAM [32] | 1024 × 1024     | 584       | 36.7        | 5.48        | 233       | 3.0         | 5.27        | 5.16×          | /            |
|     | TinySAM [24]   | 1024 × 1024     | 609       | 36.7        | 5.48        | 246       | 3.0         | 5.27        | 4.93×          | /            |
|     | MedSAM [18]    | 1024 × 1024     | 3983      | 369.0       | 7.87        | 241       | 2.9         | 5.57        | 1.00×          | /            |
|     | SAM-Med2D [4]  | 256 × 256       | 1063      | 32.0        | 6.32        | 216       | <b>0.21</b> | 5.55        | 3.30×          | /            |
| 3D  | SAM-Med3D [27] | 128 × 128 × 128 | 70        | 89.5        | 6.58        | 20        | 2.8         | 5.53        | 60.27×         | 1.00×        |
|     | FastSAM3D      | 128 × 128 × 128 | 3         | <b>21.9</b> | <b>0.78</b> | <b>5</b>  | 2.8         | <b>0.71</b> | <b>527.38×</b> | <b>8.75×</b> |

**Datasets.** Our evaluation incorporates three diverse datasets that span two modalities, namely computed tomography (CT) and magnetic resonance imaging (MRI), where we follow the dataset splits of previous work [\[27\]](#page-97-12). (1) The *AMOS* dataset [\[13\]](#page-97-17) is a substantial and varied clinical collection designed for abdominal organ segmentation with 500 CT and 100 MRI scans. (2) The *TotalSegmentator* dataset [\[28](#page-97-18)] consists of 1228 CT studies each with 117 anatomical structures acquired from different pathologies, scanners, series, and institutions. (3) The *BraTS 2021* dataset [\[20](#page-97-19)] assembles a total number of 1251 multi-institutional MRI scans.

**Performance Comparison.** Table [2](#page-93-0) compares segmentation performance for FastSAM3D with various 2D and 3D SAM approaches. Figure [2](#page-95-0) provides an illustrative visualization for samples segmented by different methods. FastSAM3D not only demonstrates competitive segmentation performance in comparison to its teacher model, SAM-Med3D, but also surpasses all 2D efficient SAM models, especially when the number of point prompts is increased. For example, FastSAM3D achieves a Dice score of 0*.*437 on the *AMOS* dataset with 10 point prompts, which is a significant improvement over the 0.306 score from the best-performing 2D model (*p <* <sup>0</sup>*.*01). This trend is consistent across the *TotalSegmentator*

<span id="page-94-1"></span>**Table 4.** Ablation study for the contribution of 3D sparse attention ('Sparse Attn.') and flash attention ('Flash Attn.') to the performance and efficiency of FastSAM3D. Best scores are highlighted in **bold**, if statistically different from the second best result (*p <* 0*.*01). 3D sparse attention and flash attention contribute to substantial improvements in time and memory requirements without statistically significant performance decline.

| Sparse<br>Attn. | Flash Attn. | AMOS <sup>[13]</sup> |     |                                                                                                                                           | TotalSegmentator [28] |     |     | $BraTS$ [20] |      |     | Encoder |     |      |  |      |                       |
|-----------------|-------------|----------------------|-----|-------------------------------------------------------------------------------------------------------------------------------------------|-----------------------|-----|-----|--------------|------|-----|---------|-----|------|--|------|-----------------------|
|                 |             | 1 <sub>pt</sub>      | 3pt | 5pt                                                                                                                                       | 10 <sub>pt</sub>      | 1pt | 3pt | 5pt          | 10pt | 1pt | 3pt     | 5pt | 10pt |  |      | $ Time FLOPs $ Memory |
| x               |             |                      |     | $(0.282 \mid 0.375 \mid 0.403 \mid 0.436 \mid 0.243 \mid 0.371 \mid 0.442 \mid 0.516 \mid 0.335 \mid 0.404 \mid 0.422 \mid 0.444 \mid 10$ |                       |     |     |              |      |     |         |     |      |  | 23.1 | 1.16                  |
| x               |             |                      |     | $0.276$ $0.366$ $0.398$ $0.432$ $0.247$ $0.374$ $0.438$ $0.516$ $0.331$ $0.402$ $0.421$ $0.445$ $6$                                       |                       |     |     |              |      |     |         |     |      |  | 21.9 | 1.15                  |
| ✓               |             |                      |     | $0.277$ $0.370$ $0.402$ $0.433$ $0.255$ $0.381$ $0.450$ $0.520$ $0.328$ $0.403$ $0.422$ $0.445$ $9$                                       |                       |     |     |              |      |     |         |     |      |  | 23.1 | 0.79                  |
|                 |             |                      |     | $0.273 \mid 0.368 \mid 0.402 \mid 0.437 \mid 0.250 \mid 0.378 \mid 0.445 \mid 0.519 \mid 0.333 \mid 0.401 \mid 0.421 \mid 0.445 \mid 3$   |                       |     |     |              |      |     |         |     |      |  | 21.9 | 0.78                  |

and *BraTS* datasets, underscoring the robustness of FastSAM3D across different datasets, modalities, and organs. Additionally, 2D SAM methods require intensive per-slice prompting as opposed to FastSAM3D which only involves volumelevel interactions.

**Computational Efficiency Comparison.** Regarding the computational efficiency, Table [3](#page-94-0) reveals that FastSAM3D reduces the inference time for the encoder to 3 milliseconds and decoder to 5 milliseconds for 3D volumetric images, a substantial improvement from the 3980 milliseconds required by the vanilla SAM employed in a slice-by-slice manner. Moreover, FastMed3D requires fewer FLOPs and less memory than all counterparts, achieving a 527*.*38× acceleration compared to the vanilla SAM and 8*.*75<sup>×</sup> acceleration compared to SAM-Med3D.

**Ablation Study.** Table [4](#page-94-1) illustrates the effectiveness of both sparse and flash attention in optimizing computational efficiency. Specifically, when neither 3D sparse nor flash attention mechanisms were applied, the model achieved a Dice score of 0*.*436 on the AMOS dataset with 10 prompts. The introduction of 3D sparse attention marginally reduces the Dice score to 0*.*433 from 0*.*436 on *AMOS* but substantially reduces memory consumption from 1*.*06 Gb to 0*.*79 Gb. Flash attention alone improves inference time from 10 ms to 6 ms, underscoring its impact on computational efficiency. Moreover, the concurrent implementation of both sparse and flash attention yields the most substantial improvements. For instance, the Dice score on the AMOS dataset with 10 prompts increases to 0*.*437, and the encoder time is reduced to 3 ms, Memory requirements are also minimized to 0*.*78 Gb, suggesting an optimized model footprint.

# 4 Conclusion

We present FastSAM3D, an innovative adaptation of SAM for efficient segmentation of volumetric medical imaging data. This model addresses the critical

<span id="page-95-0"></span>Image /page/95/Picture/6 description: This image displays a grid of medical scans, comparing the segmentation results of different models against the ground truth. The grid is organized into three rows labeled 'AMOS', 'Brats', and 'Total Segmentator', and eight columns representing different segmentation models: 'Ground Truth', 'SAM', 'MobileSAM', 'TinySAM', 'MedSAM', 'SAM-Med2D', 'SAM-Med3D', and 'FastSAM3D'. Each cell in the grid shows a medical scan with a highlighted region, presumably a tumor or anatomical structure, in blue. The 'AMOS' row shows scans of the pelvic region, the 'Brats' row shows brain scans with tumors, and the 'Total Segmentator' row shows scans of the torso. The segmentation quality varies across the models and datasets, with some models showing more accurate and complete segmentation than others.

**Fig. 2.** Representative segmentation results from all methods across three datasets. FastSAM3D demonstrates accurate contour delineation comparable to SAM-Med3D.

challenges of high inference time and the substantial computational cost associated with previous 3D SAM methods. Through a novel layer-wise progressive distillation and 3D sparse flash attention integration, we significantly reduce computational demands while maintaining high segmentation performance. Our experiments across different modalities and organs demonstrate that FastSAM3D not only accelerates inference by factors of 527*.*38<sup>×</sup> compared to 2D SAMs and <sup>8</sup>*.*75<sup>×</sup> to 3D SAMs but also retains the flexibility of SAM's interactivity, making it a promising and powerful tool for clinical deployment. FastSAM3D opens up the possibility of real-time human-machine interaction by facilitating rapid prompting volumetric segmentation, thereby potentially maximizing user agency and trust while minimizing effort, workload, and wait time-related frustration. With the speed and efficiency of Fast SAM3D, another possible direction includes the development of mixed reality (MR) applications for surgical planning and guidance.

**Acknowledgments.** This work was supported in part by grants from the National Institutes of Health (NIH R01 GM148987-01).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-96-1"></span>1. Amrehn, M., Gaube, S., Unberath, M., Schebesch, F., Horz, T., Strumia, M., Steidl, S., Kowarschik, M., Maier, A.: Ui-net: Interactive artificial neural networks for iterative image segmentation based on a user model. arXiv preprint [arXiv:1709.03450](http://arxiv.org/abs/1709.03450) (2017)
- <span id="page-96-5"></span>2. Bolya, D., Zhou, C., Xiao, F., et al.: Yolact: Real-time instance segmentation. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 9157–9166 (2019)
- <span id="page-96-4"></span>3. Bui, N.T., Hoang, D.H., Tran, M.T., et al.: Sam3d: Segment anything model in volumetric medical images. arXiv preprint [arXiv:2309.03493](http://arxiv.org/abs/2309.03493) (2023)
- <span id="page-96-3"></span>4. Cheng, J., Ye, J., Deng, Z., et al.: Sam-med2d. arXiv preprint [arXiv:2308.16184](http://arxiv.org/abs/2308.16184) (2023)
- <span id="page-96-6"></span>5. Dao, T.: Flashattention-2: Faster attention with better parallelism and work partitioning. arXiv preprint [arXiv:2307.08691](http://arxiv.org/abs/2307.08691) (2023)
- <span id="page-96-7"></span>6. Dao, T., Fu, D., Ermon, S., et al.: Flashattention: Fast and memory-efficient exact attention with io-awareness. Advances in Neural Information Processing Systems **35**, 16344–16359 (2022)
- <span id="page-96-8"></span>7. Ding, J., Ma, S., Dong, L., et al.: Longnet: Scaling transformers to 1,000,000,000 tokens. arXiv preprint [arXiv:2307.02486](http://arxiv.org/abs/2307.02486) (2023)
- <span id="page-96-0"></span>8. Dora, L., Agrawal, S., Panda, R., et al.: State-of-the-art methods for brain tissue segmentation: A review. IEEE reviews in biomedical engineering **10**, 235–249 (2017)
- <span id="page-96-2"></span>9. Dosovitskiy, A., Beyer, L., Kolesnikov, A., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)

- <span id="page-97-8"></span>10. Gao, H., Li, Y., Long, K., et al.: A survey for foundation models in autonomous driving. arXiv preprint [arXiv:2402.01105](http://arxiv.org/abs/2402.01105) (2024)
- <span id="page-97-4"></span>11. He, Z., Unberath, M., Ke, J., et al.: Transnuseg: A lightweight multi-task transformer for nuclei segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 206–215. Springer (2023)
- <span id="page-97-15"></span>12. Hinton, G., Vinyals, O., Dean, J.: Distilling the knowledge in a neural network. arXiv preprint [arXiv:1503.02531](http://arxiv.org/abs/1503.02531) (2015)
- <span id="page-97-17"></span>13. Ji, Y., Bai, H., Ge, C., et al.: Amos: A large-scale abdominal multi-organ benchmark for versatile medical image segmentation. Advances in Neural Information Processing Systems **35**, 36722–36732 (2022)
- <span id="page-97-0"></span>14. Jiang, H., Diao, Z., Yao, Y.D.: Deep learning techniques for tumor segmentation: a review. The Journal of Supercomputing **78**(2), 1807–1851 (2022)
- <span id="page-97-5"></span>15. Ke, J., Lu, Y., Shen, Y., et al.: Clusterseg: A crowd cluster pinpointed nucleus segmentation framework with cross-modality datasets. Medical Image Analysis **85**, 102758 (2023)
- <span id="page-97-7"></span>16. Kirillov, A., Mintun, E., Ravi, N., et al.: Segment anything. arXiv preprint [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-97-9"></span>17. Liu, X., Zhou, T., Wang, Y., et al.: Towards the unification of generative and discriminative visual foundation model: A survey. arXiv preprint [arXiv:2312.10163](http://arxiv.org/abs/2312.10163) (2023)
- <span id="page-97-11"></span>18. Ma, J., He, Y., Li, F., et al.: Segment anything in medical images. Nature Communications **15**(1), 654 (2024)
- <span id="page-97-13"></span>19. Mazurowski, M.A., Dong, H., Gu, H., et al.: Segment anything model for medical image analysis: an experimental study. Medical Image Analysis **89**, 102918 (2023)
- <span id="page-97-19"></span>20. Menze, B.H., Jakab, A., Bauer, S., et al.: The multimodal brain tumor image segmentation benchmark (brats). IEEE transactions on medical imaging **34**(10), 1993–2024 (2014)
- <span id="page-97-1"></span>21. Mirikharaji, Z., Abhishek, K., Bissoto, A., et al.: A survey on deep learning for skin lesion segmentation. Medical Image Analysis p. 102863 (2023)
- <span id="page-97-3"></span>22. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation pp. 234–241 (2015)
- <span id="page-97-16"></span>23. Shen, Y., Guo, P., Wu, J.a.: Movit: Memorizing vision transformers for medical image analysis. In: International Workshop on Machine Learning in Medical Imaging. pp. 205–213. Springer (2023)
- <span id="page-97-10"></span>24. Shu, H., Li, W., Tang, Y., Zhang, Y., Chen, Y., Li, H., Wang, Y., Chen, X.: Tinysam: Pushing the envelope for efficient segment anything model. arXiv preprint [arXiv:2312.13789](http://arxiv.org/abs/2312.13789) (2023)
- <span id="page-97-14"></span>25. Wang, A., Chen, H., Lin, Z., et al.: Repvit-sam: Towards real-time segmenting anything. arXiv preprint [arXiv:2312.05760](http://arxiv.org/abs/2312.05760) (2023)
- <span id="page-97-6"></span>26. Wang, G., Li, W., Zuluaga, M.A., Pratt, R., Patel, P.A., Aertsen, M., Doel, T., David, A.L., Deprest, J., Ourselin, S., et al.: Interactive medical image segmentation using deep learning with image-specific fine tuning. IEEE transactions on medical imaging **37**(7), 1562–1573 (2018)
- <span id="page-97-12"></span>27. Wang, H., Guo, S., Ye, J., et al.: Sam-med3d. arXiv preprint [arXiv:2310.15161](http://arxiv.org/abs/2310.15161) (2023)
- <span id="page-97-18"></span>28. Wasserthal, J., Breit, H.C., Meyer, M.T., et al.: Totalsegmentator: Robust segmentation of 104 anatomic structures in ct images. Radiology: Artificial Intelligence **5**(5) (2023)
- <span id="page-97-2"></span>29. Wu, J., Xu, Q., et al.: Swin transformer improves the idh mutation status prediction of gliomas free of mri-based tumor segmentation. Journal of Clinical Medicine **11**(15), 4625 (2022)

- <span id="page-98-5"></span>30. Yu, F., Koltun, V.: Multi-scale context aggregation by dilated convolutions. arXiv preprint [arXiv:1511.07122](http://arxiv.org/abs/1511.07122) (2015)
- <span id="page-98-4"></span>31. Yu, W., Luo, M., Zhou, P., Si, C., Zhou, Y., Wang, X., Feng, J., Yan, S.: Metaformer is actually what you need for vision. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 10819–10829 (2022)
- <span id="page-98-1"></span>32. Zhang, C., Han, D., Qiao, Y., et al.: Faster segment anything: Towards lightweight sam for mobile applications. arXiv preprint [arXiv:2306.14289](http://arxiv.org/abs/2306.14289) (2023)
- <span id="page-98-0"></span>33. Zhang, Y., Shen, Z., Jiao, R.: Segment anything model for medical image segmentation: Current applications and future directions. arXiv preprint [arXiv:2401.03495](http://arxiv.org/abs/2401.03495) (2024)
- <span id="page-98-3"></span>34. Zhang, Z., Cai, H., Han, S.: Efficientvit-sam: Accelerated segment anything model without performance loss. arXiv preprint [arXiv:2402.05008](http://arxiv.org/abs/2402.05008) (2024)
- <span id="page-98-2"></span>35. Zhao, X., Ding, W., An, Y., et al.: Fast segment anything. arXiv preprint [arXiv:2306.12156](http://arxiv.org/abs/2306.12156) (2023)

Image /page/99/Picture/0 description: A square button with a light gray background and a subtle gradient. In the center of the button, there is a circular icon with a bookmark-like shape inside. Below the icon, the text "Check for updates" is displayed in a dark gray sans-serif font.

# **Few-Shot Adaptation of Medical Vision-Language Models**

Fereshteh Shakeri<sup>1[,](http://orcid.org/0000-0002-9726-9393)2( $\boxtimes$ )</sup> , Yunshi Huang<sup>1,2</sup> , Julio Silva-Rodríguez<sup>1</sup> , Houda Bahig<sup>2</sup>[,](http://orcid.org/0000-0002-2436-7750) An Tang<sup>[2](http://orcid.org/0000-0002-9668-8027)</sup> $\bullet$ , Jose Dolz<sup>1,2</sup> $\bullet$ , and Ismail Ben Ayed<sup>1,2</sup> $\bullet$ 

<sup>1</sup> ETS Montreal, Montreal, Canada <EMAIL>  $^{\rm 2}$  Centre de Recherche du Centre Hospitalier de l'Université de Montréal (CRCHUM), Montreal, Canada

**Abstract.** Integrating image and text data through multi-modal learning has emerged as a new approach in medical imaging research, following its successful deployment in computer vision. While considerable efforts have been dedicated to establishing medical foundation models and their zero-shot transfer to downstream tasks, the popular few-shot setting remains relatively unexplored. Following on from the currently strong emergence of this setting in computer vision, we introduce the first structured benchmark for adapting medical vision-language models (VLMs) in a strict few-shot regime and investigate various adaptation strategies commonly used in the context of natural images. Furthermore, we evaluate a simple generalization of the linear-probe adaptation baseline, which seeks an optimal blending of the visual prototypes and text embeddings via learnable class-wise multipliers. Surprisingly, such a textinformed linear probe yields competitive performances in comparison to convoluted prompt-learning and adapter-based strategies, while running considerably faster and accommodating the black-box setting. Our extensive experiments span three different medical modalities and specialized foundation models, nine downstream tasks, and several state-of-the-art few-shot adaptation methods. We made our benchmark and code publicly available to trigger further developments in this emergent subject: [https://github.com/FereshteShakeri/few-shot-MedVLMs.](https://github.com/FereshteShakeri/few-shot-MedVLMs)

**Keywords:** Medical VLMs · Few-shot Learning · Efficient Adaptation

## 1 Introduction

Deep neural networks have attracted paramount attention in the last decade in the medical image analysis community [\[20](#page-108-0)]. Their breakthrough developments in natural image recognition tasks have been successfully applied to a breadth of medical tasks, such as radiology image classification [\[12](#page-108-1)], tumor grading in gigapixel stained histology images [\[28\]](#page-109-0), or diabetic retinopathy grading

F. Shakeri and Y. Huang—Equal contributions.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 553–563, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_52)\_52

[\[5](#page-107-0)], among others. However, the limitations of such models have restricted their widespread adoption in real clinical settings. In particular, they require large labeled datasets for training reliable task-specific solutions, a burden for medical domains  $[4]$  $[4]$ , in which annotated data is usually scarce. In addition, the large domain drifts existing in medical image analysis from inter-scanner, inter-stain, or inter-population variability require continuous adaptation, ideally done in a data-efficient way, *i.e.* using small numbers of labeled samples, a.k.a *few-shot* adaptation. A potential alternative for such adaptation is transfer learning of large pre-trained models that extract robust features. Although popular in computer vision, transferring such models from natural to medical images did not achieve the expected gains [\[24](#page-108-2)], due to the fine-grained nature of medical images.

A paradigm shift in transfer learning is currently underway, focused on largescale pre-training on heterogeneous datasets, which have shown improved transferability, the so-called *foundation models*. In particular, vision-language models, such as CLIP [\[23](#page-108-3)] and ALIGN [\[13\]](#page-108-4), exhibit remarkable adaptability to various downstream tasks. These models can integrate large-scale sources with text supervision (*e.g.* 400M image-text pairs for CLIP), and train joint embedding representations of such modalities by contrastive learning, which have shown astonishing robustness to domain drifts [\[23](#page-108-3)]. In addition, such pre-trained knowledge can be efficiently transferred to downstream tasks, in low-shot regimes. Although those are conditions largely desired in the medical-imaging community [\[21](#page-108-5)], the direct application of CLIP models has been limited in this domain, since they lack fine-grained expert's medical knowledge.

To alleviate this issue, a myriad of recent works have gathered large openaccess medical datasets to build specialized medical vision-language models for radiology [\[31,](#page-109-1)[32](#page-109-2)[,35](#page-109-3)], histology [\[10](#page-108-6),[11\]](#page-108-7), or ophthalmology [\[27](#page-108-8)]. With the current endeavors towards developing and adapting such models to downstream tasks, nevertheless, there are important specific challenges inherent to clinical domains, which are largely being overlooked. First, current studies on medical VLMs predominantly revolve around fine-tuning models with a reduced percentage of the available datasets (e.g.,  $1\%$  or  $10\%$  in [\[11](#page-108-7)] or [\[35](#page-109-3)]), which still amount to hundreds or thousands of annotated samples. This assumes large labeled datasets for adaptation, which might be inconvenient in clinical applications, particularly when dealing with rare, low-prevalence diseases. Second, pre-training medical foundation models will potentially involve the use of private sources of clinical records, both images and text reports. While recent studies have warned about the potential leaking of the source data from solely using the pre-trained weights [\[29](#page-109-4)], fine-tuning the entire encoders during adaptation is still a dominant choice in the literature [\[32](#page-109-2)]. Moreover, foundation models tend to improve performances by increasing substantially the number of trainable parameters, thereby requiring substantial hardware requirements for full fine-tuning, which may be unpractical in clinical institutions, with limited computational sources.

Linear probing (LP) is a standard adaptation method, which was also evaluated in the seminal CLIP paper [\[23\]](#page-108-3). It is a computationally efficient fine-tuning baseline, which operates in black-box settings, *i.e.* it does not require access to the inner representations of the pre-training models. It consists of updating the weights of a linear classifier on top of the frozen vision encoder, by

optimizing the cross-entropy loss built with a few labeled samples in the target task. Unfortunately, LP has often been reported as a very weak baseline in the recent literature on few-shot VLMs [\[23,](#page-108-3)[34](#page-109-5)[,37\]](#page-109-6), as it completely omits the text encoder's knowledge, potentially over-fitting the few labeled images. This has motivated intensive recent research efforts in computer vision, targeted at building convoluted *prompt learning* [\[3](#page-107-2),[33,](#page-109-7)[36](#page-109-8)[,37](#page-109-6)] or *feature-adaptation* [\[7,](#page-108-9)[34\]](#page-109-5) strategies, which account for such information. In particular, prompt learning is gaining wide popularity in the field. This parameter-efficient family of methods improves adaptation by optimizing the best text input for a target task, via learnable continuous prompts. We demonstrate that such prompt-tuning approaches offer limited performance gains in few-shot medical-image classification, at the cost of imposing an overlooked extensive computational and memory overhead, requiring gradient back-propagation throughout the entire text encoder. Moreover, the assumption of accessing the learned parameters of the text encoder may hinder their deployment in low-resource and privacy-preserving black-box scenarios, which are crucial considerations in medical domains. To address these issues, a few, very recent studies in computer vision have incorporated knowledge from the text encoder to enhance the linear-probe baseline [\[9,](#page-108-10)[19](#page-108-11)].

Given the continuous emergence of foundation models in medical imaging, along with the potential deployment of these popular adaptation methods, we aim at paving the way towards more realistic adaptation of medical VLMs, taking into account transferability scenarios with access to limited labeled examples per task, *i.e.* up to 16 shots. Our main contributions could be summarized as follows:

- We introduce the first structured benchmark for adapting medical visionlanguage models (VLMs) in a strict few-shot regime.
- We evaluate a simple generalization of the LP baseline, which seeks an optimal blending of the visual prototypes and text embeddings via learnable class-wise multipliers. Surprisingly, such a text-informed LP yields competitive performances in comparison to convoluted prompt-learning and adapterbased strategies, while running considerably faster and accommodating the black-box setting (as it requires access to the output embeddings only).
- We report extensive evaluations and comparisons over three different medical modalities and specialized foundation models, nine downstream tasks and several state-of-the-art few-shot adaptation strategies.

### 2 Related Work

*Prompt Learning.* One of the foremost categories of approaches in the fewshot adaptation of vision-language models is prompt learning, motivated by the observation that the choice of input prompt may affect the performance of zeroshot prediction. Following the burgeoning interest in prompt learning within the NLP community [\[8](#page-108-12)[,14](#page-108-13),[26\]](#page-108-14), notable work by [\[37](#page-109-6)] introduced context optimization (CoOp) for vision-language models. In CoOp, text is represented as learnable continuous vectors, which are trained as task-specific prompts through few-shot training examples and a standard supervised classification loss. The innovative idea of CoOp has spurred an extensive body of literature on prompt learning for few-shot vision-language models, yielding numerous sophisticated extensions [\[3](#page-107-2),[33,](#page-109-7)[36,](#page-109-8)[38](#page-109-9)]. For example, CoCoOp [\[36](#page-109-8)] additionally learns instance-conditional contexts conditioned on the inputs to improve the generalization of CoOp to unseen classes. PLOT [\[3\]](#page-107-2) learns multiple prompts to describe each class's characteristics through the minimization of an optimal-transport distance. KgCoOp [\[33](#page-109-7)] enhances CoOp's performance on unseen classes by minimizing the discrepancy between the text embeddings generated by the learned prompts and hand-crafted ones. ProGrad [\[38\]](#page-109-9) aligns few-shot downstream knowledge with large-scale general knowledge, thereby mitigating overfitting the few-shot samples. Given the popularity of prompt-learning methods in vision and NLP, there is currently an emergent interest in their application within the medical field. This includes, for instance, parameter-efficient medical image segmentation [\[6](#page-107-3)] and prompt learning on large clinical language models [\[30](#page-109-10)], the latter being more closely related to our setting.

*Black-Box Adapters.* Adapters represent another category of approaches within the realm of few-shot adaptation for VLMs. These methods focus on non-linear transformations applied to the pre-trained vision and text features [\[7](#page-108-9),[34\]](#page-109-5). They are multi-layer modules added to the encoder's bottleneck, and whose parameters are fine-tuned over a few-shot task by optimizing the crossentropy loss. For instance, CLIP-Adapter [\[7](#page-108-9)] incorporated a multi-layer perceptron to learn new features, which are blended with the original pre-trained features through residual connections. Tip-Adapter [\[34\]](#page-109-5) integrated a non-linear, quadratic-complexity module to assess pairwise similarities between the features of the labeled samples, and blended the resulting class scores with the textual features. This category of approaches effectively alleviates the limitation of prompt-learning methods in terms of computational complexity, by eliminating the need for back-propagation over the text encoder. However, their performance relies heavily on key hyper-parameters, particularly those governing the blending between vision and textual features, which require computationally intensive grid searches.

<span id="page-102-0"></span>

# 3 Methods

*The Few-Shot Image Classification Setting.* Following on from the popular few-shot setting in computer vision [\[34](#page-109-5)[,37\]](#page-109-6), our approach involves a foundation model pre-trained on a large dataset composed of image-text pairs. The objective is to predict the labels of samples from previously unseen target datasets, via fine-tuning on a limited number of labeled samples, a.k.a *the support set*. For each support image  $x_i$ , one may compute its vision embedding  $f_i = \theta_v(x_i)$ , with  $\theta_v$ denoting the frozen pre-trained visual encoder. Also, for each given target class  $k \in 1, \ldots, K$ , one may use a textual description of the class (or a prompt),  $z_k$ , e.g., "an image of a  $[class_k]$ ", where  $[class_k]$  is the class name/description. Let  $t_k = \theta_t(z_k)$  denotes the corresponding text embedding, and  $\theta_t$  the text encoder.

*The Standard Linear-Probe (LP) baseline.* The standard linear probe (LP), initially evaluated as a few-shot adaptation baseline in the CLIP paper [\[23](#page-108-3)], is a linear classifier that exclusively utilizes the frozen vision features. It optimizes the following cross-entropy loss w.r.t. the last-layer weights of the vision encoder (i.e., the class prototypes),  $\mathbf{w} = (\mathbf{w}_k)_{1 \leq k \leq K}$ :

$$
L_{\text{CE}}(\mathbf{w}) = -\frac{1}{N} \sum_{i=1}^{N} \sum_{k=1}^{K} y_{ik} \ln p_{ik}(\mathbf{w}); \quad p_{ik}(\mathbf{w}) = \frac{\exp\left(\boldsymbol{f}_i^t \boldsymbol{w}_k\right)}{\sum_{j=1}^{K} \exp\left(\boldsymbol{f}_i^t \boldsymbol{w}_j\right)} \tag{1}
$$

where  $y_{ik}$  denotes one-hot encoded label of support image  $x_i$ , i.e.,  $y_{ik} = 1$  if *x<sup>i</sup>* belongs to class k and 0 otherwise. Unlike prompt learning methods and Adapters, which integrate text knowledge, a limitation of this standard LP baseline is that it omits completely information from the text encoder, *i.e.*  $\mathbf{t} = (t_k)_{1 \leq k \leq K}$ , yielding significantly lower performances than zero-shot predictions [\[23](#page-108-3)].

*Text-Driven Linear Probe (LP+text).* We evaluate a simple generalization of the LP baseline, which we introduced recently in the context of natural-image few-shot tasks [\[9\]](#page-108-10). Our method integrates text knowledge while accommodating the black-box setting. It seeks an optimal blending of the visual prototypes and text embeddings via learnable class-wise multipliers,  $\alpha = (\alpha_k)_{1 \leq k \leq K}$ , by optimizing the following loss function:

<span id="page-103-0"></span>
$$
L_{\text{CE}}(\mathbf{w}, \boldsymbol{\alpha}) = -\frac{1}{N} \sum_{i=1}^{N} \sum_{k=1}^{K} y_{ik} \ln p_{ik}(\mathbf{w}, \boldsymbol{\alpha}); \quad p_{ik}(\mathbf{w}, \boldsymbol{\alpha}) = \frac{\exp \left( \boldsymbol{f}_i^t(\boldsymbol{w}_k + \alpha_k \boldsymbol{t}_k) \right)}{\sum_{j=1}^{K} \exp \left( \boldsymbol{f}_i^t(\boldsymbol{w}_j + \alpha_j \boldsymbol{t}_j) \right)}
$$
(2)

During few-shot adaptation, visual class prototypes  $\mathbf{w} = (\mathbf{w}_k)_{1 \leq k \leq K}$  and classwise blending parameters  $\alpha = (\alpha_k)_{1 \leq k \leq K}$  are updated via full-batch gradient descent, while text embeddings  $\mathbf{t} = (t_k)_{1 \leq k \leq K}$  are kept fixed. To minimize [\(2\)](#page-103-0), we follow the computationally efficient, full-batch optimizer in [\[9\]](#page-108-10), in which step sizes are implicit (derived from the Lipschitz-gradient properties of the objective function  $[9]$  $[9]$ ). This relaxes intensive validation searches for the optimization hyper-parameters, unlike standard gradient descent practices where learning rates are intensively searched over validation sets. Therefore, it runs significantly faster than state-of-the-art few-shot adaptation methods for VLMs.

### 4 Experiments

*Medical Vision-Language Models (VLMs).* A comprehensive assessment of the potential of medical VLM adaptation is carried out across three different popular medical domains: histology, radiology, and ophthalmology. In each domain, we utilize an open-access specialized foundation VLM. **Histology**: we employed Quilt-1M [\[11\]](#page-108-7), with ViT-B/32 vision and GPT2 text encoder. **Ophtalmology**: we utilized FLAIR [\[27](#page-108-8)], a foundation model focused on color fundus image understanding. **Radiology**: we focused on chest X-ray (CXR) scans, which have attracted the attention of a large body of literature  $[31,32,35]$  $[31,32,35]$  $[31,32,35]$  $[31,32,35]$ . Concretely, we used MedCLIP [\[31\]](#page-109-1) pre-trained on CheXpert [\[12\]](#page-108-1) and MIMIC-CXR [\[16](#page-108-15)] datasets. Since these datasets are also further used for evaluation, we pretrained this model to control test partition better and avoid test-data leakage. We followed [\[31](#page-109-1)] implementation details. Note that FLAIR and MedCLIP present a similar dual-encoder architecture: ResNet-50 as vision encoder, and BioClinical-BERT [\[2](#page-107-4)] text encoder. It is worth mentioning that those models cover a wide range of architectures, both convolutional and ViTs.

*Adaptation Tasks.* Our benchmark encompasses a wide number of downstream tasks for the adaptation of medical VLMs. To ensure a logical transfer of the pretrained features, each specialized foundation model is used uniquely for datasets from their respective domain. In addition, such open-access datasets are carefully selected to avoid test data leaking, *i.e.* evaluating with data used for pretraining. **Histology**: involve three different organs and cancer types. Concretely, colorectal adenocarcinoma samples in NCT-CRC [\[17](#page-108-16)], prostate cancer grading in SICAPv2 [\[28](#page-109-0)], and SkinCancer [\[18\]](#page-108-17). **Ophtalmology**: we consider MESSI-DOR [\[5](#page-107-0)] focused on diabetic retinopathy (DR) grading, and FIVES [\[15\]](#page-108-18) and ODIR200x3 [\[1](#page-107-5)], for inter-diseases discrimination. **Radiology**: following the same evaluation benchmark as in [\[31\]](#page-109-1), we employed CheXpert<sub>5×200</sub> [\[12](#page-108-1)], MIMIC<sub>5×200</sub> [\[16](#page-108-15)], and RSNA [\[25](#page-108-19)]. These datasets include a heterogeneous variety of finegrained findings, such as pneumonia, atelectasis, edema, or pleural effusion.

*Few-Shot Adaptation Protocol and Evaluation.* Transfer learning from the large-scale pre-trained models is performed in a challenging, but realistic medical setting, in which only a few samples, *i.e.* shots, are available. Following relevant literature in natural image [\[7](#page-108-9)[,23](#page-108-3),[37\]](#page-109-6), the training subset consists of  $S = \{1, 2, 4, 8, 16\}$  images per class randomly sampled for each dataset in all scenarios. To guarantee fair comparisons among different approaches, we deploy a few-shot validation set with the same number of samples for hyper-parameters tuning. We employed the test splits from the original datasets, if available, or performed a 20% hold-out partition otherwise. The evaluation metric is a balanced average accuracy (ACA), widely employed in CXR [\[31\]](#page-109-1) and Ophthalmology [\[27](#page-108-8)] benchmarks. The evaluation is carried out through 5 random seeds to account for the variability in the few shots selected.

*Implementation Details and Baselines.* We conduct a comprehensive comparison of several state-of-the-art methods in the few-shot efficient transfer learning of CLIP-based models. Our benchmarks include Zero-shot prediction (*i.e.* no adaptation), Prompt Learning, and black-box Adapter methods. **Zero-shot**: following CLIP [\[23\]](#page-108-3), these predictions are obtained by computing the softmax cosine similarity between image and text embeddings. Text embeddings for each category are obtained following the specific prompts used in each original VLM's publication. This is, prompt ensembles for MedCLIP [\[31](#page-109-1)], and domain-expert descriptions for FLAIR  $[27]$  $[27]$  and Quilt-1M  $[11]$  $[11]$ . It is worth mentioning that the same text-driven prompts are used when required in other Adapters. **Prompt Learning**: we resort to the popular CoOp [\[37\]](#page-109-6) and KgCoOp [\[33](#page-109-7)]. **Black-box Adapters**: The firstly proposed linear probing in CLIP paper, LP, is considered as a baseline. Concretely, logistic regression is trained with the L-BFGS

Image /page/105/Figure/1 description: The image displays a grid of nine line graphs, each representing the performance of different few-shot adaptation methods across various datasets. The datasets are NCT-CRC, SICAPv2, SkinCancer, ODIR200x3, FIVESS, MESSIDOR, CheXpert, MIMIC, and RSNA. Each graph plots 'Test ACA (%)' on the y-axis against 'Support samples per class' on the x-axis, with values ranging from 0 to 16. Several methods are represented by different colored lines and markers: CoOp (blue), CooCoOp (gray), KgCoOo (brown), Clip-Ad (red), Tip-Ad-F (orange), LP (purple), and LP+text (green). The graphs generally show an increasing trend in Test ACA (%) as the number of support samples per class increases, with variations in performance across different datasets and methods.

<span id="page-105-0"></span>**Fig. 1.** Comparison of different adaptation methods of Medical VLMs evaluated on 9 benchmarks, averaged over 5 tasks.

[\[22](#page-108-20)] optimizer. Also, more recent adaptation techniques such as CLIP-adapter [\[7](#page-108-9)] and TIP-adapter [\[34](#page-109-5)] are included. For TIP-Adapter, we employed its finetuned version, TIP-Adapter-F, and set  $\alpha$  and  $\beta$  to 1 initially. Later, we find best values of  $\alpha$  and  $\beta$  using the validation set. Finally, we include the efficient proposed LP+text in the benchmark, following its description in Sect. [3.](#page-102-0)

*Results.* Figure [1](#page-105-0) shows a quantitative comparison of all studied few-shot adaptation methods on the 9 benchmarks. As demonstrated by the figure LP+text performs relatively well in most cases, outperforming prompt learning methods by a large margin and performing on par with Adapters. In Table [1](#page-106-0) we present specific numerical results for each method, averaged per modality. Specific numeric results per dataset are provided in Supp. Materials. It is worth mentioning from the results that Prompt Learning methods rarely outperform black-box Adapters. For instance, the most recent method of such a family, KgCoOp [\[33](#page-109-7)] ranges performance drops  $(e.g. [1.3, 3.4]\%$  for  $S = 16)$  compared with the proposed LP+text. In addition, the significant standard derivation of prompt learning is relatively large, especially in low-shot settings, which motivates the use of Adapters as a more appealing alternative. Comparing the proposed LP+text with other Adapters, our method shows consistent performance gains to the popular TIP-Adapter [\[34\]](#page-109-5), and performs at par with CLIP-Adapter,

| (a) Histology                                           | $S=1$                              | $S=2$                             | $S=4$                                                                                               | $S=8$                              | $S=16$                            |
|---------------------------------------------------------|------------------------------------|-----------------------------------|-----------------------------------------------------------------------------------------------------|------------------------------------|-----------------------------------|
| Zero-shot $_{\text{ICML'}21}$ [23]                      | 48.33                              |                                   |                                                                                                     |                                    |                                   |
| $CoOp_{IJCV'22}$ [37]                                   | $46.05 \pm 9.79$                   | $54.55 \pm 8.53$                  | $66.04 \pm 5.15$                                                                                    | $71.45 \pm 5.53$ 77.69 $\pm$ 1.32  |                                   |
| $CoCoOp$ $CVPR22$ [3]                                   | $46.63 \pm 7.71$                   | $55.98 \pm 5.65$                  | $65.39 \pm 3.04$                                                                                    | $70.04 \pm 3.05$ 73.73 $\pm$ 2.83  |                                   |
| $KgCoOp$ CVPR'23 [33]                                   | $53.96 \pm 5.95$                   | $62.44 \pm 3.49$                  | $69.37 \pm 3.33$                                                                                    | $76.01 \pm 2.46$  79.91 $\pm$ 1.07 |                                   |
| $CLIP-Adapter_{IJCV'23}$ [7]                            | $52.50 \pm 8.31$                   | $62.58 + 4.07$                    | $69.21 + 4.44$                                                                                      | $75.92 + 2.48$ $ 80.47 + 1.31 $    |                                   |
| Tip-Adapter-F $_{\text{ECCV'22}}$ [34] 53.97 $\pm$ 6.11 |                                    | $63.54 \pm 3.41$                  | $69.11 \pm 4.24$                                                                                    | $77.01 \pm 2.5280.69 \pm 1.42$     |                                   |
| Linear probe $(LP)$                                     | $52.05 \pm 4.66$                   | $63.33 \pm 3.24$                  | $69.22 \pm 4.02$                                                                                    | $76.64 \pm 1.66$ 80.47 $\pm$ 1.61  |                                   |
| $LP + text$ [9]                                         |                                    |                                   | 55.60 ± 6.26 64.69 ± 3.65 70.56 ± 3.94 76.52 ± 2.44 81.26 ± 1.76                                    |                                    |                                   |
| (b) Ophtalmology                                        | $S=1$                              | $S=2$                             | $S=4$                                                                                               | $S=8$                              | $S=16$                            |
| Zero-shot $ICML21$ [23]                                 | 65.74                              |                                   |                                                                                                     |                                    |                                   |
| $CoOp_{IJCV'22}$ [37]                                   |                                    |                                   | $45.98 \pm 12.26\vert 50.11 \pm 12.29\vert 58.48 \pm 11.12\vert 62.00 \pm 6.96\vert 72.45 \pm 2.04$ |                                    |                                   |
| $CoCoOp$ CVPR'22 [3]                                    | $47.87 \pm 12.07$ 59.19 $\pm 7.97$ |                                   | $69.16 \pm 5.79$                                                                                    | $71.94 \pm 4.43$ 77.16 $\pm 3.01$  |                                   |
| $KgCoOp_{CVPR'23}$ [33]                                 | $46.23 \pm 10.26$ 55.03 $\pm$ 8.01 |                                   | $62.98 \pm 4.49$                                                                                    | $64.31 \pm 4.92$ 71.67 $\pm 4.98$  |                                   |
| $CLIP-Adapter_{IJCV'23}$ [7]                            | $66.18 \pm 4.54$                   | $68.00 \pm 4.29$                  | $70.38 \pm 5.90$                                                                                    | $74.27 \pm 3.99$ 77.65 $\pm 2.72$  |                                   |
| Tip-Adapter-F $_{\text{ECCV'22}}$ [34] 66.95 $\pm$ 4.03 |                                    | $71.57 \pm 3.78$ 72.16 $\pm 3.92$ |                                                                                                     | $75.42 \pm 4.12$ $75.30 \pm 3.38$  |                                   |
| Linear probe $(LP)$                                     | $64.39 \pm 5.57$                   | $69.18 \pm 5.28$                  | $73.13 \pm 4.38$                                                                                    |                                    | $75.09 \pm 4.24$ 79.83 $\pm 2.34$ |
| $LP + text$ [9]                                         | 69.56 $\pm$ 6.22 71.15 $\pm$ 4.95  |                                   | $74.72 \pm 3.80$ 75.66 $\pm 3.42$ 77.42 $\pm 2.07$                                                  |                                    |                                   |
| (c) Radiology                                           | $S=1$                              | $S=2$                             | $S=4$                                                                                               | $S=8$                              | $S=16$                            |
| Zero-shot $ICML21$ [23]                                 | 60.37                              |                                   |                                                                                                     |                                    |                                   |
| $CoOp_{IJCV'22}$ [37]                                   | $37.64 \pm 6.82$                   | $40.82 \pm 6.76$                  | $49.95 \pm 6.15$                                                                                    | $57.21 \pm 3.97$ 62.21 $\pm 4.00$  |                                   |
| $CoCoOp$ CVPR'22 [3]                                    | $34.52 \pm 6.50$                   | $40.35 \pm 5.63$                  | $46.93 \pm 6.60$                                                                                    | $49.19 \pm 4.55$ 52.73 $\pm 3.46$  |                                   |
| $KgCoOp$ CVPR'23 [33]                                   | $38.57 \pm 7.47$                   | $46.70 \pm 7.11$                  | $50.57 \pm 5.72$                                                                                    | $55.39 \pm 3.47$ 60.73 $\pm 3.51$  |                                   |
| CLIP-Adapter $_{\text{IJCV23}}$ [7]                     |                                    |                                   | $61.13 \pm 2.43$ $62.10 \pm 2.66$ $63.17 \pm 2.93$ $64.06 \pm 2.48$ $64.15 \pm 2.27$                |                                    |                                   |
| Tip-Adapter-F $_{\text{ECCV'22}}$ [34] 59.88 $\pm$ 2.80 |                                    | $60.52 \pm 1.68$                  | $62.64 \pm 4.55$                                                                                    | $60.03 \pm 3.29$ $62.59 \pm 2.47$  |                                   |
| Linear probe $(LP)$                                     | $45.98 \pm 4.87$                   | $49.63 \pm 4.50$                  | $53.28 \pm 4.80$                                                                                    | $57.97 \pm 3.12$ 60.50 $\pm$ 4.76  |                                   |
| $LP + text$ [9]                                         | $58.39 \pm 5.03$                   | 62.10 $\pm$ 3.80 62.79 $\pm$ 3.19 |                                                                                                     |                                    | 64.80 ± 2.79 64.15 ± 3.20         |

<span id="page-106-0"></span>**Table 1. Comparison of state-of-the-art methods.** Average ACA (%) on 3 benchmarks for each modality. Best values are highlighted in **bold**.

albeit being much more computationally efficient, as we later discuss. Finally, while the basic LP suffers a consistent performance drop in the extreme-low data regime (*i.e.*  $S = 1$ ), introducing text information in LP+text prevents it.

*Assessing Computational Workload.* Here we evaluate the efficiency of the methods considered by presenting their computational overhead. We also indicate whether these methods enable black-box adaptation, which is a crucial consideration for addressing practical, real-world demands. Furthermore, we outline the number of parameters to be learned during training as an indicator of model complexity. This comparison Table [2](#page-107-6) shows that, beyond outperforming state-ofthe-art methods as shown in previous sections, LP+text stands out as the most efficient method. Complementary, it is worth noting that LP+text uses around 800MB of peak GPU memory, whereas CoCoOP requires up to 28GB (based

| Methods            | Category            | Training Time | Black-box    | #Parameters                  |
|--------------------|---------------------|---------------|--------------|------------------------------|
| Zero-shot [23]     |                     | n/a           | $\checkmark$ | n/a                          |
| CoOp [37]          | Prompt-Learning     | 3 min         | $\times$     | $K \times n_{ctx1} \times D$ |
| CoCoOp [36]        |                     | 12 min        | $\times$     | $n_{ctx2} \times D + C$      |
| KgCoOp [33]        |                     | 3 min         | $\times$     | $K \times n_{ctx1} \times D$ |
| Clip-Adapter [7]   | CLIP-based Adapters | 2 min         | $\checkmark$ | $2(D_1 \times D_2)$          |
| Tip-adapter-F [34] |                     | 2 min         | $\checkmark$ | $K \times S \times D$        |
| LP                 | Linear probe        | 43 s          | $\checkmark$ | $K \times D$                 |
| LP+text [9]        |                     | 4 s           | $\checkmark$ | $K(D+1)$                     |

<span id="page-107-6"></span>**Table 2. Computational Efficiency.** Experiments on a single NVIDIA RTX A6000 GPU on NCT-CRC.  $D_1 = 256$ , and  $D_2 = D = 512$ . Number of context tokens for CoOp and KgCoOp:  $n_{ctx1} = 16$ ; for CoCoOp:  $n_{ctx2} = 4$ .

on NCT-CRC experiments). This makes prompt learning methods inefficient for institutions with limited access to high-resource GPUs.

*Conclusions.* Inspired by the computer vision field, we established a new fewshot adaption setting for medical VLMs. We also introduced a generalization of LP baseline, integrating image and text embeddings through learnable classwise multipliers. Evaluations across various benchmarks show that the proposed method stands out for its performance in different scenarios, its simplicity, computational efficiency, and its potential applicability in black-box scenarios.

**Acknowledgement.** This work was funded by the Natural Sciences and Engineering Research Council of Canada (NSERC) and Montreal University Hospital Research Center (CRCHUM). We also thank Calcul Quebec and Compute Canada.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article

## References

- <span id="page-107-5"></span>1. Ocular disease intelligent recognition (odir) (2019), [https://odir2019.grand](https://odir2019.grand-challenge.org/)[challenge.org/](https://odir2019.grand-challenge.org/)
- <span id="page-107-4"></span>2. Alsentzer, E., et al.: Publicly available clinical BERT embeddings. In: Clinical Natural Language Processing Workshop (2019)
- <span id="page-107-2"></span>3. Chen, G., et al.: Prompt learning with optimal transport for vision-language models. In: International Conference on Learning Representations (2023)
- <span id="page-107-1"></span>4. Chen, X., et al.: Recent advances and clinical applications of deep learning in medical image analysis. Medical Image Analysis **79** (2022)
- <span id="page-107-0"></span>5. Decencière, E., et al.: Feedback on a publicly distributed image database: The messidor database. Image Analysis & Stereology **33**, 231–234 (07 2014)
- <span id="page-107-3"></span>6. Fischer, M., Bartler, A., Yang, B.: Prompt tuning for parameter-efficient medical image segmentation. Medical Image Analysis **91**, 103024 (2024)

- <span id="page-108-9"></span>7. Gao, P., et al.: Clip-adapter: Better vision-language models with feature adapters. International Journal of Computer Vision **132**, 581–595 (2023)
- <span id="page-108-12"></span>8. hong, Z., Friedman, D., Chen, D.: Factual probing is [mask]: Learning vs. learning to recall. In: Conference of the North American Chapter of the Association for Computational Linguistics (2021)
- <span id="page-108-10"></span>9. Huang, Y., Shakeri, F., Dolz, J., Boudiaf, M., Bahig, H., Ayed, I.B.: Lp++: A surprisingly strong linear probe for few-shot clip. In: IEEE Conference on Computer Vision and Pattern Recognition (2024)
- <span id="page-108-6"></span>10. Huang, Z., Bianchi, F., Yuksekgonul, M., Montine, T., Zou, J.: A visual–language foundation model for pathology image analysis using medical twitter. Nature Medicine **29**, 1–10 (2023)
- <span id="page-108-7"></span>11. Ikezogwo, W.O., et al.: Quilt-1m: One million image-text pairs for histopathology. In: Neural Information Processing Systems (2023)
- <span id="page-108-1"></span>12. Irvin, J., et al.: Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison. In: AAAI (2019)
- <span id="page-108-4"></span>13. Jia, C., et al.: Scaling up visual and vision-language representation learning with noisy text supervision. In: International Conference on Machine Learning (2021)
- <span id="page-108-13"></span>14. Jiang, Z., Xu, F., Araki, J., Neubig, G.: How can we know what language models know. In: Association for Computational Linguistics (2020)
- <span id="page-108-18"></span>15. Jin, K., et al.: Fives: A fundus image dataset for artificial intelligence based vessel segmentation. Scientific Data **9** (2022)
- <span id="page-108-15"></span>16. Johnson, A.E., et al.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Scientific data **6** (2019)
- <span id="page-108-16"></span>17. Kather, J.N., Halama, N., Marx, A.: 100,000 histological images of human colorectal cancer and healthy tissue. Zenodo **5281** (2018)
- <span id="page-108-17"></span>18. Kriegsmann, K., et al.: Deep learning for the detection of anatomical tissue structures and neoplasms of the skin on scanned histopathological tissue sections. Frontiers in Oncology **12** (2022)
- <span id="page-108-11"></span>19. Lin, Z., Yu, S., Kuang, Z., Pathak, D., Ramanan, D.: Multimodality helps unimodality: Cross-modal few-shot learning with multimodal models. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 19325–19337 (2023)
- <span id="page-108-0"></span>20. Litjens, G., et al.: A survey on deep learning in medical image analysis. Medical Image Analysis **42** (2017)
- <span id="page-108-5"></span>21. Moor, M., et al.: Foundation models for generalist medical artificial intelligence. Nature **616**, 259–265 (4 2023)
- <span id="page-108-20"></span>22. Nocedal, J.: Updating quasi-newton matrices with limited storage. Mathematics of Computation **35**(151), 773–782 (1980)
- <span id="page-108-3"></span>23. Radford, A., et al.: Learning transferable visual models from natural language supervision. In: International Conference on Machine Learning (2021)
- <span id="page-108-2"></span>24. Raghu, M., Zhang, C., Kleinberg, J., Bengio, S.: Transfusion: Understanding transfer learning for medical imaging. In: Advances in neural information processing systems (2019)
- <span id="page-108-19"></span>25. Shih, G., et al.: Augmenting the national institutes of health chest radiograph dataset with expert annotations of possible pneumonia. Radiology: Artificial Intelligence **1** (2019)
- <span id="page-108-14"></span>26. Shin, T., et al.: Autoprompt: Eliciting knowledge from language models with automatically generated prompts. In: CoRR (2020)
- <span id="page-108-8"></span>27. Silva-Rodriguez, J., Chakor, H., Kobbi, R., Dolz, J., Ayed, I.B.: A foundation language-image model of the retina (flair): Encoding expert knowledge in text supervision. ArXiv Preprint (2023)

- <span id="page-109-0"></span>28. Silva-Rodríguez, J., Colomer, A., Sales, M.A., Molina, R., Naranjo, V.: Going deeper through the gleason scoring scale: An automatic end-to-end system for histology prostate grading and cribriform pattern detection. Computer methods and programs in biomedicine **195** (2020)
- <span id="page-109-4"></span>29. Song, C., Ristenpart, T., Shmatikov, V.: Machine learning models that remember too much. In: Conference on Computer and Communications Security (2017)
- <span id="page-109-10"></span>30. Taylor, N., et al.: Clinical prompt learning with frozen language models. IEEE Transactions on Neural Networks and Learning Systems (2023)
- <span id="page-109-1"></span>31. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. In: Empirical Methods in Natural Language Processing (2022)
- <span id="page-109-2"></span>32. Wu, C., Zhang, X., Zhang, Y., Wang, Y., Xie, W.: Medklip: Medical knowledge enhanced language-image pre-training for x-ray diagnosis. In: International Conference on Computer Vision (2023)
- <span id="page-109-7"></span>33. Yao, H., Zhang, R., Xu, C.: Visual-language prompt tuning with knowledgeguided context optimization. In: IEEE Conference on Computer Vision and Pattern Recognition (2023)
- <span id="page-109-5"></span>34. Zhang, R., et al.: Tip-adapter: Training-free adaption of clip for few-shot classification. In: European Conference on Computer Vision (2022)
- <span id="page-109-3"></span>35. Zhang, Y., Jiang, H., Miura, Y., Manning, C.D., Langlotz, C.P.: Contrastive learning of medical visual representations from paired images and text. In: MHLC (2022)
- <span id="page-109-8"></span>36. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Conditional prompt learning for visionlanguage models. In: IEEE Conference on Computer Vision and Pattern Recognition (2022)
- <span id="page-109-6"></span>37. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Learning to prompt for vision-language models. International Journal of Computer Vision **130**, 2337–2348 (2022)
- <span id="page-109-9"></span>38. Zhu, B., Niu, Y., Han, Y., Wu, Y., Zhang, H.: Prompt-aligned gradient for prompt tuning. In: International Conference on Computer Vision (2023)