{"table_of_contents": [{"title": "Black-Box Adaptation for Medical Image\nSegmentation", "heading_level": null, "page_id": 0, "polygon": [[64.57337883959045, 52.1907958984375], [360.5078125, 52.1907958984375], [360.5078125, 84.514892578125], [64.57337883959045, 84.514892578125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[52.55972696245733, 54.77052868391451], [149.1015625, 54.77052868391451], [149.1015625, 66.5208740234375], [52.55972696245733, 66.5208740234375]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[38.80615234375, 242.95996093750003], [142.333984375, 242.95996093750003], [142.333984375, 255.33593750000003], [38.80615234375, 255.33593750000003]]}, {"title": "3 Proposed Method", "heading_level": null, "page_id": 2, "polygon": [[38.29351535836177, 502.20410156250006], [165.93856655290102, 502.20410156250006], [165.93856655290102, 514.580078125], [38.29351535836177, 514.6929133858268]]}, {"title": "4 Experiments and Results", "heading_level": null, "page_id": 4, "polygon": [[38.29351535836177, 232.37524414062503], [208.18359375, 232.37524414062503], [208.18359375, 245.40258789062503], [38.29351535836177, 245.40258789062503]]}, {"title": "5 Ablation Analysis", "heading_level": null, "page_id": 6, "polygon": [[38.29351535836177, 449.76904296875], [165.537109375, 449.76904296875], [165.537109375, 464.75048828125006], [38.29351535836177, 464.75048828125006]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[38.29351535836177, 261.361083984375], [125.03906249999999, 261.361083984375], [125.03906249999999, 275.039794921875], [38.29351535836177, 275.039794921875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[39.04436860068259, 503.18115234375006], [103.33984375, 503.18115234375006], [103.33984375, 517.51123046875], [39.04436860068259, 517.51123046875]]}, {"title": "CLEFT: Language-Image Contrastive\nLearning with Efficient Large Language\nModel and Prompt Fine-Tuning", "heading_level": null, "page_id": 11, "polygon": [[87.763671875, 51.49871826171875], [367.8125, 51.49871826171875], [367.8125, 102.427490234375], [87.763671875, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 11, "polygon": [[52.55972696245733, 516.5341796875], [149.74609375, 516.5341796875], [149.74609375, 530.212890625], [52.55972696245733, 530.212890625]]}, {"title": "2 Methods", "heading_level": null, "page_id": 13, "polygon": [[52.55972696245733, 420.783203125], [125.68359374999999, 420.783203125], [125.68359374999999, 432.5078125], [52.55972696245733, 432.5078125]]}, {"title": "2.1 Boosting CLIP with an LLM", "heading_level": null, "page_id": 13, "polygon": [[51.80887372013652, 512.6259765625], [225.5859375, 512.6259765625], [225.5859375, 523.0478515625], [51.80887372013652, 523.0478515625]]}, {"title": "2.2 Learning the Context-Based Prompt", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 330.56884765625], [264.3003412969283, 330.56884765625], [264.3003412969283, 340.99072265625], [52.55972696245733, 340.99072265625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 15, "polygon": [[51.80887372013652, 512.6259765625], [148.779296875, 512.6259765625], [148.779296875, 524.3505859375], [51.80887372013652, 524.3505859375]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 537.05224609375], [123.96484375, 537.05224609375], [123.96484375, 548.12548828125], [52.55972696245733, 548.12548828125]]}, {"title": "3.2 Baselines and Evaluation Metrics", "heading_level": null, "page_id": 16, "polygon": [[38.29351535836177, 240.02880859375003], [233.51535836177473, 240.02880859375003], [233.51535836177473, 251.10205078125003], [38.29351535836177, 251.10205078125003]]}, {"title": "3.3 Implementation Details", "heading_level": null, "page_id": 16, "polygon": [[38.29351535836177, 456.93408203125006], [184.12109375, 456.93408203125006], [184.12109375, 468.65869140625006], [38.29351535836177, 468.65869140625006]]}, {"title": "3.4 Main Results", "heading_level": null, "page_id": 17, "polygon": [[52.55972696245733, 335.77978515625], [147.705078125, 335.77978515625], [147.705078125, 346.85302734375], [52.55972696245733, 346.85302734375]]}, {"title": "Table 2. EMBED Evaluation. All val-\nues are percentages.", "heading_level": null, "page_id": 18, "polygon": [[38.29351535836177, 55.73260498046875], [208.291015625, 55.73260498046875], [208.291015625, 76.25067138671875], [38.29351535836177, 76.25067138671875]]}, {"title": "Table 3. Ablation Evaluation. All val-\nues are percentages.", "heading_level": null, "page_id": 18, "polygon": [[217.63671875, 51.13232421875], [386.07421875, 51.13232421875], [386.07421875, 72.3017578125], [217.63671875, 72.3017578125]]}, {"title": "3.5 Ablation Experiments", "heading_level": null, "page_id": 18, "polygon": [[38.29351535836177, 494.71337890625006], [177.03125, 494.71337890625006], [177.03125, 506.43798828125006], [38.29351535836177, 506.43798828125006]]}, {"title": "4 Discussion and Conclusion", "heading_level": null, "page_id": 19, "polygon": [[52.55972696245733, 108.8597412109375], [230.7421875, 108.8597412109375], [230.7421875, 121.72424316406251], [52.55972696245733, 121.72424316406251]]}, {"title": "References", "heading_level": null, "page_id": 19, "polygon": [[52.55972696245733, 415.24658203125], [117.3046875, 415.24658203125], [117.3046875, 428.27392578125], [52.55972696245733, 428.27392578125]]}, {"title": "CT2Rep: Automated Radiology Report\nGeneration for 3D Medical Imaging", "heading_level": null, "page_id": 22, "polygon": [[74.765625, 52.1907958984375], [349.98046875, 52.1907958984375], [349.98046875, 85.00341796875], [74.765625, 85.00341796875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 22, "polygon": [[39.04436860068259, 436.416015625], [134.40273037542661, 436.416015625], [134.40273037542661, 448.140625], [39.04436860068259, 448.140625]]}, {"title": "2 Methods", "heading_level": null, "page_id": 24, "polygon": [[38.29351535836177, 53.37139892578125], [111.87713310580205, 53.37139892578125], [111.87713310580205, 66.56158447265625], [38.29351535836177, 66.56158447265625]]}, {"title": "2.1 The Proposed Method", "heading_level": null, "page_id": 24, "polygon": [[38.29351535836177, 180.10302734375], [179.45392491467575, 180.10302734375], [179.45392491467575, 191.501953125], [38.29351535836177, 191.501953125]]}, {"title": "2.2 Longitudinal Data Utilization", "heading_level": null, "page_id": 26, "polygon": [[38.29351535836177, 292.626708984375], [215.49488054607508, 292.626708984375], [215.49488054607508, 304.025634765625], [38.29351535836177, 304.025634765625]]}, {"title": "2.3 Dataset Preparation", "heading_level": null, "page_id": 27, "polygon": [[52.55972696245733, 135.321533203125], [182.45733788395904, 135.321533203125], [182.45733788395904, 145.417724609375], [52.55972696245733, 145.417724609375]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 27, "polygon": [[52.55972696245733, 473.21826171875006], [221.71875, 473.21826171875006], [221.71875, 484.29150390625006], [52.55972696245733, 484.29150390625006]]}, {"title": "3.1 Comparison with the Baseline Method", "heading_level": null, "page_id": 28, "polygon": [[38.88671875, 257.452880859375], [261.29692832764505, 257.452880859375], [261.29692832764505, 269.177490234375], [38.88671875, 269.177490234375]]}, {"title": "3.2 Ablation Study on Longitudinal Data Utilization", "heading_level": null, "page_id": 28, "polygon": [[38.29351535836177, 487.22265625000006], [311.60409556313994, 487.22265625000006], [311.60409556313994, 498.94726562500006], [38.29351535836177, 498.94726562500006]]}, {"title": "3.3 Implementation Details", "heading_level": null, "page_id": 29, "polygon": [[52.55972696245733, 525.6533203125], [198.22525597269623, 525.6533203125], [198.22525597269623, 536.7265625], [52.55972696245733, 536.7265625]]}, {"title": "4 Discussion and Conclusion", "heading_level": null, "page_id": 30, "polygon": [[38.29351535836177, 132.**********], [216.9965870307167, 132.**********], [216.9965870307167, 146.069091796875], [38.29351535836177, 146.069091796875]]}, {"title": "References", "heading_level": null, "page_id": 30, "polygon": [[39.04436860068259, 363.78857421875], [103.232421875, 363.78857421875], [103.232421875, 377.46728515625], [39.04436860068259, 377.46728515625]]}, {"title": "Curriculum Prompting Foundation\nModels for Medical Image Segmentation", "heading_level": null, "page_id": 33, "polygon": [[81.318359375, 51.295166015625], [373.3984375, 51.295166015625], [373.3984375, 84.189208984375], [81.318359375, 84.189208984375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 33, "polygon": [[52.55972696245733, 448.46630859375], [149.1015625, 448.46630859375], [149.1015625, 462.14501953125006], [52.55972696245733, 462.14501953125006]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 34, "polygon": [[38.29351535836177, 511.97460937500006], [137.4061433447099, 511.97460937500006], [137.4061433447099, 526.3046875], [38.29351535836177, 526.3046875]]}, {"title": "2.1 Overview", "heading_level": null, "page_id": 34, "polygon": [[38.29351535836177, 536.7265625], [113.22265625, 536.7265625], [113.22265625, 549.**********], [38.29351535836177, 549.**********]]}, {"title": "2.2 Coarse Prompting", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 195.8233970753656], [158.232421875, 195.8233970753656], [158.232421875, 206.15771484375], [38.29351535836177, 206.15771484375]]}, {"title": "2.3 Fine-<PERSON>rained Prompting", "heading_level": null, "page_id": 36, "polygon": [[39.04436860068259, 562.710911136108], [189.96587030716722, 562.710911136108], [189.96587030716722, 573.203125], [39.04436860068259, 573.203125]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 37, "polygon": [[52.55972696245733, 507.9403824521935], [222.79296875, 507.9403824521935], [222.79296875, 518.81396484375], [52.55972696245733, 518.81396484375]]}, {"title": "3.1 Dataset", "heading_level": null, "page_id": 37, "polygon": [[52.55972696245733, 532.6996625421823], [118.63481228668941, 532.6996625421823], [118.63481228668941, 542.26318359375], [52.55972696245733, 542.26318359375]]}, {"title": "3.2 Experiment Settings and Metrics", "heading_level": null, "page_id": 38, "polygon": [[38.29351535836177, 331.22021484375], [233.75, 331.22021484375], [233.75, 342.94482421875], [38.29351535836177, 342.94482421875]]}, {"title": "3.3 Results", "heading_level": null, "page_id": 38, "polygon": [[39.04436860068259, 500.57568359375006], [104.0380859375, 500.57568359375006], [104.0380859375, 512.95166015625], [39.04436860068259, 512.95166015625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 41, "polygon": [[51.80887372013652, 145.417724609375], [138.90784982935153, 145.417724609375], [138.90784982935153, 158.**********], [51.80887372013652, 158.**********]]}, {"title": "References", "heading_level": null, "page_id": 41, "polygon": [[52.55972696245733, 345.224609375], [117.13310580204778, 345.224609375], [117.13310580204778, 358.251953125], [52.55972696245733, 358.251953125]]}, {"title": "DB-SAM: Delving into High Quality\nUniversal Medical Image Segmentation", "heading_level": null, "page_id": 44, "polygon": [[72.83276450511946, 51.7022705078125], [352.34375, 51.7022705078125], [352.34375, 85.00341796875], [72.83276450511946, 85.00341796875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 44, "polygon": [[38.994140625, 534.2002249718786], [134.70703125, 534.2002249718786], [134.70703125, 546.8**********], [38.994140625, 546.8**********]]}, {"title": "2 Method", "heading_level": null, "page_id": 45, "polygon": [[52.55972696245733, 541.61181640625], [120.13651877133105, 541.61181640625], [120.13651877133105, 553.33642578125], [52.55972696245733, 553.33642578125]]}, {"title": "2.1 ViT Branch", "heading_level": null, "page_id": 46, "polygon": [[38.29351535836177, 543.56591796875], [125.46874999999999, 543.56591796875], [125.46874999999999, 555.29052734375], [38.29351535836177, 555.29052734375]]}, {"title": "2.2 Convolution Branch", "heading_level": null, "page_id": 47, "polygon": [[52.55972696245733, 261.035400390625], [180.95563139931738, 261.035400390625], [180.95563139931738, 271.457275390625], [52.55972696245733, 271.457275390625]]}, {"title": "2.3 ViT-Conv Fusion Block", "heading_level": null, "page_id": 48, "polygon": [[38.29351535836177, 433.810546875], [184.7098976109215, 433.810546875], [184.7098976109215, 444.8837890625], [38.29351535836177, 444.8837890625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 49, "polygon": [[51.80887372013652, 337.408203125], [148.45703125, 337.408203125], [148.45703125, 349.1328125], [51.80887372013652, 349.1328125]]}, {"title": "3.1 Implementation and Evaluation Metrics", "heading_level": null, "page_id": 49, "polygon": [[52.55972696245733, 362.48583984375], [280.8191126279863, 362.48583984375], [280.8191126279863, 372.90771484375], [52.55972696245733, 372.90771484375]]}, {"title": "3.2 Comparison with SAM and MedSAM", "heading_level": null, "page_id": 50, "polygon": [[38.88671875, 549.75390625], [257.16796875, 549.75390625], [257.16796875, 561.478515625], [38.88671875, 561.478515625]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 51, "polygon": [[52.55972696245733, 500.43757030371205], [156.92832764505118, 500.43757030371205], [156.92832764505118, 510.3**********006], [52.55972696245733, 510.3**********006]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 52, "polygon": [[38.29351535836177, 156.65380859375], [124.82421874999999, 156.65380859375], [124.82421874999999, 170.33251953125], [38.29351535836177, 170.33251953125]]}, {"title": "References", "heading_level": null, "page_id": 52, "polygon": [[38.29351535836177, 320.47265625], [103.447265625, 320.47265625], [103.447265625, 334.**********], [38.29351535836177, 334.**********]]}, {"title": "DeSAM: Decoupled Segment Anything\nModel for Generalizable Medical Image\nSegmentation", "heading_level": null, "page_id": 55, "polygon": [[85.59726962457337, 51.8651123046875], [370.8203125, 51.8651123046875], [370.8203125, 102.59033203125], [85.59726962457337, 102.59033203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 56, "polygon": [[39.04436860068259, 54.77052868391451], [134.40273037542661, 54.77052868391451], [134.40273037542661, 66.39874267578125], [39.04436860068259, 66.39874267578125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 57, "polygon": [[51.80887372013652, 194.107421875], [156.17747440273035, 194.107421875], [156.17747440273035, 205.50634765625], [51.80887372013652, 205.50634765625]]}, {"title": "3 Decoupled Segment Anything Model", "heading_level": null, "page_id": 58, "polygon": [[38.29351535836177, 331.**********], [277.81569965870307, 331.**********], [277.81569965870307, 345.**********], [38.29351535836177, 345.**********]]}, {"title": "3.1 Architecture", "heading_level": null, "page_id": 58, "polygon": [[38.29351535836177, 357.27490234375], [129.765625, 357.27490234375], [129.765625, 369.65087890625], [38.29351535836177, 369.65087890625]]}, {"title": "3.2 Training Strategies", "heading_level": null, "page_id": 59, "polygon": [[52.55972696245733, 382.64341957255346], [175.69965870307166, 382.64341957255346], [175.69965870307166, 392.7744140625], [52.55972696245733, 392.7744140625]]}, {"title": "4 Results and Discussion", "heading_level": null, "page_id": 60, "polygon": [[38.29351535836177, 313.144775390625], [195.22184300341297, 313.144775390625], [195.22184300341297, 325.195068359375], [38.29351535836177, 325.195068359375]]}, {"title": "4.1 Dataset and Implementation Details", "heading_level": null, "page_id": 60, "polygon": [[38.29351535836177, 338.37682789651296], [249.28327645051192, 338.37682789651296], [249.28327645051192, 349.1328125], [38.29351535836177, 349.1328125]]}, {"title": "4.2 Ablation Studies", "heading_level": null, "page_id": 61, "polygon": [[52.55972696245733, 512.95166015625], [164.43686006825936, 512.95166015625], [164.43686006825936, 523.37353515625], [52.55972696245733, 523.37353515625]]}, {"title": "4.3 Comparison with State-of-the-Art Methods", "heading_level": null, "page_id": 62, "polygon": [[38.29351535836177, 450.42041015625], [284.8828125, 450.42041015625], [284.8828125, 462.14501953125006], [38.29351535836177, 462.14501953125006]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 63, "polygon": [[52.55972696245733, 96.72802734375], [139.21875, 96.72802734375], [139.21875, 109.918212890625], [52.55972696245733, 109.918212890625]]}, {"title": "References", "heading_level": null, "page_id": 63, "polygon": [[52.55972696245733, 360.857421875], [117.13310580204778, 360.857421875], [117.13310580204778, 373.884765625], [52.55972696245733, 373.884765625]]}, {"title": "DinoBloom: A Foundation Model\nfor Generalizable Cell Embeddings\nin Hematology", "heading_level": null, "page_id": 66, "polygon": [[87.548828125, 51.620849609375], [338.59375, 51.620849609375], [338.59375, 102.5089111328125], [87.548828125, 102.5089111328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 67, "polygon": [[52.55972696245733, 53.61566162109375], [148.88671875, 53.61566162109375], [148.88671875, 66.31732177734375], [52.55972696245733, 66.31732177734375]]}, {"title": "2 Datasets", "heading_level": null, "page_id": 68, "polygon": [[39.04436860068259, 411.98974609375], [110.859375, 411.98974609375], [110.859375, 424.36572265625], [39.04436860068259, 424.36572265625]]}, {"title": "3 Methods", "heading_level": null, "page_id": 69, "polygon": [[52.55972696245733, 355.97216796875], [125.68359374999999, 355.97216796875], [125.68359374999999, 368.99951171875], [52.55972696245733, 368.99951171875]]}, {"title": "4 Results", "heading_level": null, "page_id": 71, "polygon": [[52.55972696245733, 448.140625], [117.13310580204778, 448.140625], [117.13310580204778, 460.51660156250006], [52.55972696245733, 460.51660156250006]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 74, "polygon": [[38.29351535836177, 156.0838623046875], [124.64163822525596, 156.0838623046875], [124.64163822525596, 170.658203125], [38.29351535836177, 170.658203125]]}, {"title": "References", "heading_level": null, "page_id": 74, "polygon": [[38.29351535836177, 454.97998046875006], [102.8668941979522, 454.97998046875006], [102.8668941979522, 469.96142578125006], [38.29351535836177, 469.96142578125006]]}, {"title": "FACMIC: Federated Adaptative CLIP\nModel for Medical Image Classification", "heading_level": null, "page_id": 77, "polygon": [[87.09897610921502, 50.969482421875], [368.02734375, 50.969482421875], [368.02734375, 84.35205078125], [87.09897610921502, 84.35205078125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 77, "polygon": [[52.55972696245733, 420.**********], [150.283203125, 420.**********], [150.283203125, 434.**********], [52.55972696245733, 434.**********]]}, {"title": "2 Material and Methods", "heading_level": null, "page_id": 78, "polygon": [[38.29351535836177, 476.14941406250006], [192.21843003412968, 476.14941406250006], [192.21843003412968, 489.82812500000006], [38.29351535836177, 489.82812500000006]]}, {"title": "2.1 Problem Formulation", "heading_level": null, "page_id": 78, "polygon": [[38.29351535836177, 501.55273437500006], [173.37890625, 501.55273437500006], [173.37890625, 513.27734375], [38.29351535836177, 513.27734375]]}, {"title": "2.2 Our Federated Adaptive CLIP framework", "heading_level": null, "page_id": 79, "polygon": [[52.55972696245733, 442.60400390625], [291.3310580204778, 442.60400390625], [291.3310580204778, 452.37451171875], [52.55972696245733, 452.37451171875]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 82, "polygon": [[38.29351535836177, 181.40576171875], [135.78125, 181.40576171875], [135.78125, 194.7587890625], [38.29351535836177, 194.7587890625]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 82, "polygon": [[38.29351535836177, 206.971923828125], [109.78515625, 206.971923828125], [109.78515625, 218.370849609375], [38.29351535836177, 218.370849609375]]}, {"title": "3.2 Implementation Details", "heading_level": null, "page_id": 83, "polygon": [[52.55972696245733, 312.98193359375], [198.515625, 312.98193359375], [198.515625, 323.40380859375], [52.55972696245733, 323.40380859375]]}, {"title": "3.3 Comparison with State-of-the-Art Methods", "heading_level": null, "page_id": 83, "polygon": [[52.55972696245733, 550.73095703125], [299.0625, 550.73095703125], [299.0625, 561.15283203125], [52.55972696245733, 561.15283203125]]}, {"title": "3.4 Ablation Study", "heading_level": null, "page_id": 85, "polygon": [[52.55972696245733, 172.56467941507313], [156.943359375, 171.81439820022499], [156.943359375, 182.545654296875], [52.55972696245733, 182.545654296875]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 85, "polygon": [[52.55972696245733, 438.1642294713161], [138.90784982935153, 438.1642294713161], [138.90784982935153, 449.443359375], [52.55972696245733, 449.443359375]]}, {"title": "References", "heading_level": null, "page_id": 86, "polygon": [[39.04436860068259, 52.597900390625], [103.76953125, 52.597900390625], [103.76953125, 66.927978515625], [39.04436860068259, 66.927978515625]]}, {"title": "FastSAM3D: An Efficient Segment\nAnything Model for 3D Volumetric\nMedical Images", "heading_level": null, "page_id": 88, "polygon": [[85.5078125, 51.8651123046875], [340.95703125, 51.8651123046875], [340.95703125, 102.59033203125], [85.5078125, 102.59033203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 89, "polygon": [[52.55972696245733, 53.3306884765625], [149.208984375, 53.3306884765625], [149.208984375, 66.439453125], [52.55972696245733, 66.439453125]]}, {"title": "2 Methods", "heading_level": null, "page_id": 90, "polygon": [[38.29351535836177, 429.**********], [111.2890625, 429.**********], [111.2890625, 442.9296875], [38.29351535836177, 442.9296875]]}, {"title": "2.1 Architecture Overview of FastSAM3D", "heading_level": null, "page_id": 90, "polygon": [[38.29351535836177, 453.35156250000006], [245.13671875, 453.35156250000006], [245.13671875, 465.07617187500006], [38.29351535836177, 465.07617187500006]]}, {"title": "2.2 Layer-Wise Progressive Distillation for the Image Encoder", "heading_level": null, "page_id": 91, "polygon": [[52.55972696245733, 360.2060546875], [375.546875, 360.2060546875], [375.546875, 371.279296875], [52.55972696245733, 371.279296875]]}, {"title": "2.3 3D Sparse Flash Attention", "heading_level": null, "page_id": 92, "polygon": [[38.29351535836177, 173.58935546875], [199.8046875, 173.58935546875], [199.8046875, 184.66259765625], [38.29351535836177, 184.66259765625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 93, "polygon": [[52.55972696245733, 440.32421875], [148.779296875, 440.32421875], [148.779296875, 453.35156250000006], [52.55972696245733, 453.35156250000006]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 95, "polygon": [[52.55972696245733, 375.5**********], [139.21875, 375.5**********], [139.21875, 389.84326171875], [52.55972696245733, 389.84326171875]]}, {"title": "References", "heading_level": null, "page_id": 96, "polygon": [[39.04436860068259, 308.910888671875], [102.8668941979522, 308.910888671875], [102.8668941979522, 322.915283203125], [39.04436860068259, 322.915283203125]]}, {"title": "Few-Shot Adaptation of Medical\nVision-Language Models", "heading_level": null, "page_id": 99, "polygon": [[110.3754266211604, 50.969482421875], [344.1796875, 50.969482421875], [344.1796875, 84.35205078125], [110.3754266211604, 84.35205078125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 99, "polygon": [[53.03955078125, 500.90136718750006], [149.423828125, 500.90136718750006], [149.423828125, 513.**********], [53.03955078125, 513.**********]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 101, "polygon": [[52.55972696245733, 486.57128906250006], [156.17747440273035, 486.57128906250006], [156.17747440273035, 498.94726562500006], [52.55972696245733, 498.94726562500006]]}, {"title": "3 Methods", "heading_level": null, "page_id": 102, "polygon": [[39.04436860068259, 431.**********], [111.396484375, 431.**********], [111.396484375, 445.53515625], [39.04436860068259, 445.53515625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 103, "polygon": [[52.55972696245733, 472.6771653543307], [148.02734375, 472.6771653543307], [148.02734375, 483.96582031250006], [52.55972696245733, 483.96582031250006]]}, {"title": "References", "heading_level": null, "page_id": 107, "polygon": [[52.55972696245733, 445.53515625], [117.13310580204778, 445.53515625], [117.13310580204778, 458.56250000000006], [52.55972696245733, 458.56250000000006]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 40], ["Text", 6], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 20194, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 43], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 39], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 192], ["Line", 43], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 674, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["TableCell", 102], ["Line", 41], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6097, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["TableCell", 103], ["Line", 34], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["Text", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6025, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 40], ["TableCell", 36], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3222, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 36], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 708, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 51], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 43], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 38], ["Text", 8], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 72], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 812, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 87], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1075, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 56], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1119, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 40], ["TextInlineMath", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 42], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["TableCell", 273], ["Line", 41], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2942, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 58], ["TableCell", 44], ["SectionHeader", 3], ["Text", 3], ["Reference", 3], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3312, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 40], ["Text", 5], ["ListItem", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 52], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 26], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 44], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 85], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 998, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 52], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 93], ["Equation", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1020, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 46], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["TableCell", 168], ["Line", 40], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2398, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 73], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 41], ["ListItem", 7], ["Reference", 7], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 51], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 31], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 41], ["ListItem", 3], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 54], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 44], ["Text", 6], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1022, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 42], ["TextInlineMath", 5], ["Text", 4], ["Equation", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["TableCell", 149], ["Line", 39], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 34], ["Text", 4], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1267, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["TableCell", 89], ["Line", 53], ["Text", 3], ["Table", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 40], ["ListItem", 6], ["Reference", 6], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 52], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 54], ["Line", 22], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 43], ["Text", 6], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 44], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Line", 83], ["Span", 83], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 778, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 40], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["TableCell", 400], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["TableCell", 93], ["Line", 43], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 69], ["Line", 30], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["TableCell", 68], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 42], ["ListItem", 10], ["Reference", 10], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 50], ["ListItem", 17], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 16], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 43], ["Text", 10], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1148, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 46], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 40], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 53], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 924, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 41], ["Text", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["TableCell", 126], ["Line", 73], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["TableCell", 232], ["Line", 55], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 71], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 683, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 41], ["ListItem", 7], ["Reference", 7], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 50], ["ListItem", 15], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 23], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 42], ["Text", 9], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1145, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 46], ["TextInlineMath", 3], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 57], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["ListItem", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 923, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["TableCell", 45], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1263, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["TableCell", 117], ["Line", 40], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3413, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["TableCell", 231], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 776, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 42], ["Text", 3], ["ListItem", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 52], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 42], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 41], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 44], ["Text", 2], ["TextInlineMath", 2], ["ListItem", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 103], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["Line", 84], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1114, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 86], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4912, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["TableCell", 72], ["Line", 36], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["TableCell", 227], ["Line", 41], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5477, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 103], ["TableCell", 67], ["Table", 3], ["Reference", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 1, "llm_tokens_used": 7669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 37], ["TableCell", 26], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2150, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 50], ["ListItem", 15], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 31], ["ListItem", 10], ["Reference", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 40], ["Text", 7], ["Picture", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["TableCell", 96], ["Line", 50], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1788, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 44], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 75], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 975, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 39], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["TableCell", 235], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2618, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 750], ["TableCell", 292], ["Line", 54], ["TextInlineMath", 3], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 20842, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 44], ["ListItem", 9], ["Reference", 9], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 52], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 44], ["Line", 15], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 40], ["Text", 4], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 577, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 48], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 45], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 44], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 64], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 45], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Line", 144], ["Span", 106], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 1263], ["TableCell", 162], ["Line", 42], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["TableCell", 89], ["Line", 44], ["Reference", 7], ["ListItem", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 52], ["ListItem", 21], ["Reference", 21], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 59], ["Line", 29], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-5"}