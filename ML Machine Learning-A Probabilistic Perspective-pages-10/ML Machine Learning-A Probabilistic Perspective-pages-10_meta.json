{"table_of_contents": [{"title": "8 Logistic regression", "heading_level": null, "page_id": 0, "polygon": [[84.375, 96.4248046875], [265.5, 96.4248046875], [265.5, 142.7783203125], [84.375, 142.7783203125]]}, {"title": "8.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 207.0], [195.75, 207.0], [195.75, 217.845703125], [101.25, 217.845703125]]}, {"title": "8.2 Model specification", "heading_level": null, "page_id": 0, "polygon": [[99.0, 340.5], [230.25, 340.5], [230.25, 352.4765625], [99.0, 352.4765625]]}, {"title": "8.3 Model fitting", "heading_level": null, "page_id": 0, "polygon": [[99.0, 480.0], [197.25, 480.0], [197.25, 491.37890625], [99.0, 491.37890625]]}, {"title": "8.3.1 MLE", "heading_level": null, "page_id": 1, "polygon": [[93.75, 346.5], [151.5, 346.5], [151.5, 357.22265625], [93.75, 357.22265625]]}, {"title": "8.3.2 Steepest descent", "heading_level": null, "page_id": 2, "polygon": [[92.1796875, 405.75], [209.25, 405.75], [209.25, 416.07421875], [92.1796875, 416.07421875]]}, {"title": "8.3.3 <PERSON>'s method", "heading_level": null, "page_id": 4, "polygon": [[92.53125, 148.5], [211.5, 148.5], [211.5, 159.0732421875], [92.53125, 159.0732421875]]}, {"title": "8.3.4 Iteratively reweighted least squares (IRLS)", "heading_level": null, "page_id": 5, "polygon": [[92.53125, 441.75], [326.8125, 441.75], [326.8125, 451.51171875], [92.53125, 451.51171875]]}, {"title": "8.3.5 Q<PERSON>si<PERSON>Newton (variable metric) methods", "heading_level": null, "page_id": 6, "polygon": [[92.25, 397.5], [321.0, 397.5], [321.0, 407.53125], [92.25, 407.53125]]}, {"title": "8.3.6 \\ell_2 regularization", "heading_level": null, "page_id": 7, "polygon": [[91.5, 235.5], [211.5, 235.5], [211.5, 245.84765625], [91.5, 245.84765625]]}, {"title": "8.3.7 Multi-class logistic regression", "heading_level": null, "page_id": 7, "polygon": [[92.8125, 446.25], [270.0, 446.25], [270.0, 456.2578125], [92.8125, 456.2578125]]}, {"title": "8.4 Bayesian logistic regression", "heading_level": null, "page_id": 9, "polygon": [[99.0, 471.75], [273.0, 471.75], [273.0, 482.51953125], [99.0, 482.51953125]]}, {"title": "8.4.1 Laplace approximation", "heading_level": null, "page_id": 10, "polygon": [[94.5, 60.75], [239.25, 60.75], [239.25, 71.70556640625], [94.5, 71.70556640625]]}, {"title": "8.4.2 Derivation of the BIC", "heading_level": null, "page_id": 10, "polygon": [[92.8125, 476.25], [231.75, 476.25], [231.75, 487.265625], [92.8125, 487.265625]]}, {"title": "8.4.3 Gaussian approximation for logistic regression", "heading_level": null, "page_id": 11, "polygon": [[93.0, 189.0], [351.75, 189.0], [351.75, 199.3359375], [93.0, 199.3359375]]}, {"title": "8.4.4 Approximating the posterior predictive", "heading_level": null, "page_id": 11, "polygon": [[91.96875, 521.25], [315.0, 521.25], [315.0, 531.5625], [91.96875, 531.5625]]}, {"title": "8.4.4.1 Monte Carlo approximation", "heading_level": null, "page_id": 13, "polygon": [[88.9453125, 463.5], [249.75, 463.5], [249.75, 473.34375], [88.9453125, 473.34375]]}, {"title": "8.4.4.2 Probit approximation (moderated output) *", "heading_level": null, "page_id": 14, "polygon": [[86.90625, 420.0], [315.0, 420.0], [315.0, 430.3125], [86.90625, 430.3125]]}, {"title": "8.4.5 Residual analysis (outlier detection) *", "heading_level": null, "page_id": 15, "polygon": [[93.0, 507.75], [306.75, 507.75], [306.75, 517.95703125], [93.0, 517.95703125]]}, {"title": "8.5 Online learning and stochastic optimization", "heading_level": null, "page_id": 16, "polygon": [[99.0, 228.75], [358.5, 228.75], [358.5, 239.8359375], [99.0, 239.8359375]]}, {"title": "8.5.1 Online learning and regret minimization", "heading_level": null, "page_id": 17, "polygon": [[94.078125, 61.5], [323.25, 61.5], [323.25, 71.54736328125], [94.078125, 71.54736328125]]}, {"title": "8.5.2 Stochastic optimization and risk minimization", "heading_level": null, "page_id": 17, "polygon": [[92.8828125, 360.0], [349.5, 360.0], [349.5, 369.87890625], [92.8828125, 369.87890625]]}, {"title": "8.5.2.1 Setting the step size", "heading_level": null, "page_id": 18, "polygon": [[88.2421875, 135.75], [218.25, 135.75], [218.25, 145.388671875], [88.2421875, 145.388671875]]}, {"title": "8.5.2.2 Per-parameter step sizes", "heading_level": null, "page_id": 18, "polygon": [[86.5546875, 381.75], [236.25, 380.25], [236.25, 391.7109375], [86.5546875, 391.7109375]]}, {"title": "8.5.2.3 SGD compared to batch learning", "heading_level": null, "page_id": 19, "polygon": [[87.0, 61.5], [271.265625, 61.5], [271.265625, 71.982421875], [87.0, 71.982421875]]}, {"title": "Algorithm 8.3: Stochastic gradient descent", "heading_level": null, "page_id": 19, "polygon": [[132.3984375, 132.0], [309.375, 132.0], [309.375, 140.958984375], [132.3984375, 140.958984375]]}, {"title": "8.5.3 The LMS algorithm", "heading_level": null, "page_id": 19, "polygon": [[92.25, 520.5], [220.21875, 520.5], [220.21875, 530.9296875], [92.25, 530.9296875]]}, {"title": "8.5.4 The perceptron algorithm", "heading_level": null, "page_id": 20, "polygon": [[93.0, 501.75], [252.0, 501.75], [252.0, 511.9453125], [93.0, 511.9453125]]}, {"title": "8.5.5 A Bayesian view", "heading_level": null, "page_id": 21, "polygon": [[92.25, 495.0], [207.0, 495.0], [207.0, 504.984375], [92.25, 504.984375]]}, {"title": "8.6 Generative vs discriminative classifiers", "heading_level": null, "page_id": 22, "polygon": [[99.0, 432.75], [331.5, 432.75], [331.5, 443.6015625], [99.0, 443.6015625]]}, {"title": "8.6.1 Pros and cons of each approach", "heading_level": null, "page_id": 23, "polygon": [[93.75, 172.5], [282.09375, 172.5], [282.09375, 182.8828125], [93.75, 182.8828125]]}, {"title": "8.6.2 Dealing with missing data", "heading_level": null, "page_id": 24, "polygon": [[91.5, 495.75], [253.5, 495.75], [253.5, 506.25], [91.5, 506.25]]}, {"title": "8.6.2.1 Missing data at test time", "heading_level": null, "page_id": 26, "polygon": [[87.46875, 61.5], [237.0, 61.5], [237.0, 71.5078125], [87.46875, 71.5078125]]}, {"title": "8.6.2.2 Missing data at training time", "heading_level": null, "page_id": 26, "polygon": [[86.25, 300.0], [255.75, 300.0], [255.75, 309.4453125], [86.25, 309.4453125]]}, {"title": "8.6.3 Fisher's linear discriminant analysis (FLDA) *", "heading_level": null, "page_id": 26, "polygon": [[91.5, 380.25], [342.0, 380.25], [342.0, 390.12890625], [91.5, 390.12890625]]}, {"title": "******* Derivation of the optimal 1d projection", "heading_level": null, "page_id": 28, "polygon": [[87.75, 99.75], [300.0, 99.75], [300.0, 109.8720703125], [87.75, 109.8720703125]]}, {"title": "******* Extension to higher dimensions and multiple classes", "heading_level": null, "page_id": 29, "polygon": [[86.25, 529.5], [358.5, 529.5], [358.5, 539.47265625], [86.25, 539.47265625]]}, {"title": "******* Probabilistic interpretation of FLDA *", "heading_level": null, "page_id": 30, "polygon": [[86.25, 334.5], [290.25, 334.5], [290.25, 343.6171875], [86.25, 343.6171875]]}, {"title": "Exercises", "heading_level": null, "page_id": 31, "polygon": [[129.515625, 174.75], [178.5, 174.75], [178.5, 184.939453125], [129.515625, 184.939453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 21], ["SectionHeader", 4], ["TextInlineMath", 2], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 102], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1291, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 55], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 36], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["Line", 48], ["TableCell", 40], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6920, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["Line", 49], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 825, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 47], ["TableCell", 22], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Table", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5014, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 66], ["Equation", 5], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 603], ["Line", 82], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3116, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 528], ["Line", 79], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 459], ["Line", 58], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 40], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 60], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 893, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 80], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 842, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 71], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 815, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 47], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 47], ["Equation", 4], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 56], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 46], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 40], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 50], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 765, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 38], ["Equation", 5], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 40], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 43], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 52], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["TableCell", 95], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Table", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2462, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 51], ["Equation", 4], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 64], ["Text", 9], ["Equation", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2564, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 47], ["Text", 7], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 508], ["Line", 59], ["Equation", 9], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["Line", 42], ["TableCell", 24], ["Text", 11], ["ListItem", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1088, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 540], ["Line", 52], ["Text", 8], ["ListItem", 7], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["Line", 51], ["Text", 10], ["ListItem", 8], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-10"}