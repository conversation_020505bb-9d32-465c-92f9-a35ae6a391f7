{"table_of_contents": [{"title": "20 Exact inference for graphical models", "heading_level": null, "page_id": 0, "polygon": [[50.484375, 92.548828125], [399.75, 92.548828125], [399.75, 144.439453125], [50.484375, 144.439453125]]}, {"title": "20.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[95.25, 207.0], [195.75, 207.0], [195.75, 217.6875], [95.25, 217.6875]]}, {"title": "20.2 Belief propagation for trees", "heading_level": null, "page_id": 0, "polygon": [[93.0, 353.25], [273.0, 353.25], [273.0, 364.18359375], [93.0, 364.18359375]]}, {"title": "20.2.1 Serial protocol", "heading_level": null, "page_id": 0, "polygon": [[88.453125, 426.0], [200.390625, 426.0], [200.390625, 437.2734375], [88.453125, 437.2734375]]}, {"title": "20.2.2 Parallel protocol", "heading_level": null, "page_id": 2, "polygon": [[86.25, 523.5], [209.25, 523.5], [209.25, 533.77734375], [86.25, 533.77734375]]}, {"title": "20.2.3 Gaussian BP *", "heading_level": null, "page_id": 3, "polygon": [[86.1328125, 446.25], [198.0, 446.25], [198.0, 455.625], [86.1328125, 455.625]]}, {"title": "20.2.4 Other BP variants *", "heading_level": null, "page_id": 5, "polygon": [[86.2734375, 555.0], [222.75, 555.0], [222.75, 565.734375], [86.2734375, 565.734375]]}, {"title": "20.2.4.1 Max-product algorithm", "heading_level": null, "page_id": 6, "polygon": [[83.25, 275.25], [231.75, 275.25], [231.75, 285.240234375], [83.25, 285.240234375]]}, {"title": "20.2.4.2 Sampling from a tree", "heading_level": null, "page_id": 6, "polygon": [[81.75, 379.5], [222.75, 379.5], [222.75, 389.8125], [81.75, 389.8125]]}, {"title": "20.2.4.3 Computing posteriors on sets of variables", "heading_level": null, "page_id": 6, "polygon": [[81.4921875, 436.5], [311.25, 436.5], [311.25, 445.81640625], [81.4921875, 445.81640625]]}, {"title": "20.3 The variable elimination algorithm", "heading_level": null, "page_id": 7, "polygon": [[92.4609375, 274.5], [311.25, 274.5], [311.25, 285.556640625], [92.4609375, 285.556640625]]}, {"title": "20.3.1 The generalized distributive law *", "heading_level": null, "page_id": 10, "polygon": [[88.5, 136.5], [289.5, 136.5], [289.5, 146.654296875], [88.5, 146.654296875]]}, {"title": "20.3.2 Computational complexity of VE", "heading_level": null, "page_id": 10, "polygon": [[85.9921875, 547.5], [283.5, 547.5], [283.5, 557.5078125], [85.9921875, 557.5078125]]}, {"title": "20.3.3 A weakness of VE", "heading_level": null, "page_id": 13, "polygon": [[85.640625, 220.5], [214.734375, 220.5], [214.734375, 230.34375], [85.640625, 230.34375]]}, {"title": "20.4 The junction tree algorithm *", "heading_level": null, "page_id": 13, "polygon": [[92.8125, 470.25], [282.0, 470.25], [282.0, 480.62109375], [92.8125, 480.62109375]]}, {"title": "20.4.1 Creating a junction tree", "heading_level": null, "page_id": 13, "polygon": [[89.25, 531.75], [243.75, 531.75], [243.75, 541.6875], [89.25, 541.6875]]}, {"title": "20.4.2 Message passing on a junction tree", "heading_level": null, "page_id": 15, "polygon": [[86.625, 311.25], [296.15625, 311.25], [296.15625, 321.943359375], [86.625, 321.943359375]]}, {"title": "20.4.2.1 Example: jtree algorithm on a chain", "heading_level": null, "page_id": 17, "polygon": [[83.953125, 524.25], [288.0, 524.25], [288.0, 533.77734375], [83.953125, 533.77734375]]}, {"title": "20.4.3 Computational complexity of JTA", "heading_level": null, "page_id": 18, "polygon": [[86.2734375, 445.5], [286.5, 445.5], [286.5, 455.625], [86.2734375, 455.625]]}, {"title": "20.4.4 JTA generalizations *", "heading_level": null, "page_id": 19, "polygon": [[86.9765625, 261.0], [228.9375, 261.0], [228.9375, 271.634765625], [86.9765625, 271.634765625]]}, {"title": "20.5 Computational intractability of exact inference in the worst case", "heading_level": null, "page_id": 19, "polygon": [[93.0, 489.75], [465.0, 489.75], [465.0, 500.87109375], [93.0, 500.87109375]]}, {"title": "20.5.1 Approximate inference", "heading_level": null, "page_id": 20, "polygon": [[88.5234375, 415.5], [238.5, 415.5], [238.5, 425.8828125], [88.5234375, 425.8828125]]}, {"title": "Exercises", "heading_level": null, "page_id": 21, "polygon": [[129.234375, 114.75], [178.5, 114.75], [178.5, 124.6640625], [129.234375, 124.6640625]]}, {"title": "Exercise 20.3 Message passing on a tree", "heading_level": null, "page_id": 21, "polygon": [[129.75, 409.5], [279.75, 409.5], [279.75, 418.2890625], [129.75, 418.2890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 32], ["SectionHeader", 4], ["Text", 4], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5316, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 462], ["Line", 60], ["TextInlineMath", 3], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 762, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 61], ["Text", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 52], ["Text", 8], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3250, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 62], ["Equation", 10], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 938, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 70], ["Equation", 12], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 29], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 667, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 46], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 705, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 74], ["Equation", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7570, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 131], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2052, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 42], ["Text", 10], ["Equation", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 109], ["TableCell", 20], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 12370, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 61], ["TextInlineMath", 4], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 967, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 39], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 66], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1543, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 49], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Footnote", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 46], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 808, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 52], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 37], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 767, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 35], ["ListItem", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["TableCell", 54], ["Line", 42], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Footnote", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2000, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 69], ["Text", 9], ["Equation", 7], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 25], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 649, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-24"}