So if we choose  $\delta$  small enough, then in turn  $\eta$  small enough, we can make sure that the right-hand side of (29.51) is as small as desired. This concludes the proof. □

## Bibliographical notes

Here are some (probably too lengthy) comments about the genesis of Definition 29.8. It comes after a series of particular cases and/or variants studied by <PERSON><PERSON> and myself [577, 578] on the one hand, <PERSON><PERSON><PERSON> [762, 763] on the other. To summarize: In a first step, <PERSON><PERSON> and <PERSON> [577] treated  $CD(K, \infty)$  and  $CD(0, N)$ , while <PERSON><PERSON><PERSON> [762] independently treated  $CD(K, \infty)$ . These cases can be handled with just displacement convexity. Then it took some time before <PERSON><PERSON><PERSON> [763] came up with the brilliant idea to use distorted displacement as the basis of the definition of  $CD(K, N)$  for  $N < \infty$  and  $K \neq 0$ .

There are slight variations in the definitions appearing in all these works; and they are not exactly the ones appearing in this course either. I shall describe the differences in some detail below.

In the case  $K = 0$ , for compact spaces, Definition 29.8 is exactly the definition that was used in [577]. In the case  $N = \infty$ , the definition in [577] was about the same as Definition 29.8, but it was based on inequality (29.2) (which is very simple in the case  $K = \infty$ ) instead of (29.3). Sturm [762] also used a similar definition, but preferred to impose the weak displacement convexity inequality only for the Boltzmann H functional, i.e. for  $U(r) = r \log r$ , not for the whole class  $\mathcal{DC}_{\infty}$ . It is interesting to note that precisely for the H functional and  $N = \infty$ , inequalities (29.2) and (29.3) are the same, while in general the former is a priori weaker. So the definition which I have adopted here is a priori stronger than both definitions in [577] and [762].

Now for the general  $CD(K, N)$  criterion. Sturm's original definition [763] is quite close to Definition 29.8, with three differences. First, he does not impose the basic inequality to hold true for all members of the class  $\overline{\mathcal{DC}}_N$ , but only for functions of the form  $-r^{1-1/N'}$  with  $N' \geq N$ . Secondly, he does not require the displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  and the coupling  $\pi$  to be related via some dynamical optimal transference plan. Thirdly, he imposes  $\mu_0$ ,  $\mu_1$  to be absolutely continuous with respect to  $\nu$ , rather than just to have their support included in Spt  $\nu$ . After becoming aware of Sturm's work, Lott and I [578] modified his definition, imposing the inequality to hold true for all  $U \in \mathcal{DC}_N$ , imposing a relation between  $(\mu_t)$  and  $\pi$ , and allowing in addition  $\mu_0$ and  $\mu_1$  to be singular. In the present course, I decided to extend the new definition to the case  $N = \infty$ .

Sturm [763] proved the stability of his definition under a variant of measured Gromov–Hausdorff convergence, provided that one stays away from the limit Bonnet–Myers diameter. Then Lott and I [578] briefly sketched a proof of stability for our modified definition. Details appear here for the first time, in particular the painful<sup>2</sup> proof of upper semicontinuity of  $U^{\beta}_{\pi,\nu}(\mu)$  under regularization (Theorem 29.20(iii)). It should be noted that Sturm manages to prove the stability of his definition without using this upper semicontinuity explicitly; but this might be due to the particular form of the functions  $U$  that he is considering, and the assumption of absolute continuity.

The treatment of noncompact spaces here is not exactly the same as in [577] or [763]. In the present set of notes I adopted a rather weak point of view in which every "noncompact" statement reduces to the compact case; in particular in Definition 29.8 I only consider compactly supported probability densities. This leads to simpler proofs, but the treatment in [577, Appendix E] is more precise in that it passes to the limit directly in the inequalities for probability measures that are not compactly supported.

Other tentative definitions have been rejected for various reasons. Let me mention four of them:

(i) Imposing the displacement convexity inequality along all displacement interpolations in Definition 29.8, rather than along some displacement interpolation. This concept is not stable under measured Gromov–Hausdorff convergence. (See the last remark in the concluding chapter.)

(ii) Replace the integrated displacement convexity inequalities by pointwise inequalities, in the style of those appearing in Chapter 14. For instance, with the same notation as in Definition 29.8, one may define

$$
\mathcal{J}_t(\gamma_0) := \frac{\rho_0(x)}{\mathbb{E}\left[\rho_t(\gamma_t)|\gamma_0\right]},
$$

<sup>2</sup> As a matter of fact, I was working on precisely this problem when my left lung collapsed, earning me a one-week holiday in hospital with unlimited amounts of pain-killers.

where  $\gamma$  is a random geodesic with law  $(\gamma_t) = \mu_t$ , and  $\rho_t$  is the absolutely continuous part of  $\mu_t$  with respect to  $\nu$ . Then  $\mathcal J$  is a continuous function of  $t$ , and it makes sense to require that inequality  $(29.1)$  be satisfied  $\nu(dx)$ -almost everywhere (as a function of t, in the sense of distributions). This notion of a weak  $CD(K, N)$  space makes perfect sense, and is a priori stronger than the notion discussed in this chapter. But there is no evidence that it should be stable under measured Gromov– Hausdorff convergence. Integrated convexity inequalities enjoy better stability properties. (One might hope that integrated inequalities lead to pointwise inequalities by a localization argument, as in Chapter 19; but this is not obvious at all, due to the a priori nonuniqueness of displacement interpolation in a nonsmooth context.)

(iii) Choose inequality (29.2) as the basis for the definition, instead of  $(29.3)$ . In the case  $K < 0$ , this inequality is stable, due to the convexity of  $-r^{1-1/N}$ , and the a priori regularity of the speed field provided by Theorem 28.5. (This was actually my original motivation for Theorem 28.5.) In the case  $K > 0$  there is no reason to expect that the inequality is stable, but then one can weaken even more the formulation of  $CD(K, N)$  and replace it by

$$
U_{\nu}(\mu_t) \le (1 - t) U_{\nu}(\mu_0) + t U_{\nu}(\mu_1)
$$
  
 
$$
- \frac{K_{N,U}}{2} \left[ \max \left( \sup \rho_0, \sup \rho_1 \right) \right]^{-1/N} W_2(\mu_0, \mu_1)^2, \quad (29.52)
$$

which in turn is stable, and still equivalent to the usual  $CD(K, N)$  when applied to smooth manifolds. For the purpose of the present chapter, this approach would have worked fine; as far as I know, Theorem 29.28 was first proved for general  $K, N$  by this approach (in an unpublished letter of mine from September 2004). But basing the definition of the general  $CD(K, N)$  criterion on (29.52) has a major drawback: It seems very difficult, if not impossible, to derive any sharp geometric theorem such as Bishop–Gromov or Bonnet–Myers from this inequality. We shall see in the next chapter that these sharp inequalities do follow from Definition 29.8.

(iv) Base the definition of  $CD(K, N)$  on other inequalities, involving the volume growth. The main instance is the so-called measure contraction property (MCP). This property involves a conditional probability  $P^{(t)}(x, y; dz)$  on the set  $[x, y]_t$  of t-barycenters of x, y (think of  $P^{(t)}$  as a measurable rule to choose barycenters of x and y). By definition, a metric-measure space  $(\mathcal{X}, d)$  satisfies MCP $(K, N)$  if for any 858 29 Weak Ricci curvature bounds I: Definition and Stability

Borel set  $A \subset \mathcal{X}$ ,

$$
\int \beta_t^{(K,N)}(x,y) P^{(t)}(x,y;A) \nu(dy) \le \frac{\nu[A]}{t^N};
$$

and symetrically

$$
\int \beta_{1-t}^{(K,N)}(x,y) P^{(t)}(x,y;A) \nu(dx) \le \frac{\nu[A]}{(1-t)^N}.
$$

In the case  $K = 0$ , these inequalities basically reduce to  $\nu[[x, B]_t] \ge$  $t^N \nu[B]$ . (Recall Theorem 19.6.) This approach has two drawbacks: First, it does not extend to the case  $N = \infty$ ; secondly, if M is a Riemannian manifold, then  $MCP(K,N)$  does not imply  $CD(K,N)$ , unless N coincides with the true dimension of the manifold. On the other hand,  $CD(K, N)$  implies  $MCP(K, N)$ , at least in a nonbranching space. All this is discussed in independent works by Sturm [763, Section 6] and Ohta [654]; see also [573, Remark 4.9]. Unlike the weak  $CD(K, N)$ property, the MCP property is known to be true for finite-dimensional Alexandrov spaces with curvature bounded below [654, Section 2]. An interesting field of application of the MCP property is the analysis on the Heisenberg group, which does not satisfy any  $CD(K, N)$  bound but still satisfies  $\text{MCP}(0, 5)$  [496].

Bonciocat and Sturm [143] modified the definition of weak  $CD(K, \infty)$ spaces to allow for a fixed "resolution error" in the measurement of distances (implying of course a modification of the transport cost). This approach defines spaces which are " $\delta$ -approximately  $CD(K,\infty)$ "; it gives the possibility to study Ricci curvature bounds outside the scope of length spaces, including even discrete spaces in the analysis. For instance, any weak  $CD(K, \infty)$  space is a limit of  $\delta$ -approximate CD(K,  $\infty$ ) discrete spaces, with  $\delta \to 0$ . The procedure is a bit similar in spirit to the construction of  $CAT_{\delta}(K)$  spaces [439], modulo of course the introduction of the formalism of entropies and Wasserstein geodesics. (So this theory provides one answer to the very last question raised by Gromov in [439].) Bonciocat and Sturm apply it to classes of planar homogeneous graphs.

Cordero-Erausquin suggested the use of the Prékopa–Leindler inequality as a possible alternative basis for a synthetic  $CD(K, N)$  criterion. This is motivated by the fact that the Prékopa–Leindler inequality has many geometric consequences, including Sobolev or Talagrand inequalities (see the bibliographical notes for Chapters 21 and 22). So far nobody has undertaken this program seriously and it is not known whether it includes some serious analytical difficulties.

Lott [576] noticed that (for a Riemannian manifold) at least  $CD(0, N)$ bounds can be formulated in terms of displacement convexity of certain functionals explicitly involving the time variable. For instance, CD(0, N) is equivalent to the convexity of  $t \to t U_{\nu}(\mu_t) + N t \log t$ on [0, 1], along displacement interpolation, for all  $U \in \mathcal{DC}_{\infty}$ ; rather than convexity of  $U_{\nu}(\mu_t)$  for all  $U \in \mathcal{DC}_N$ . (Note carefully: in one formulation the dimension is taken care of by the time-dependence of the functional, while in the other one it is taken care of by the class of nonlinearities.) More general curvature-dimension bounds can probably be encoded by refined convexity estimates: for instance,  $CD(K, N)$  seems to be equivalent to the (displacement) convexity of  $tH_{\nu}(\mu_t) + N t \log t - K(t^3/6) W_2(\mu_0, \mu_1)^2.$ 

It seems likely that this observation can be developed into a complete theory. For geometric applications, this point of view is probably less sharp than the one based on distortion coefficients, but it may be technically simpler.

A completely different approach to Ricci bounds in metric-measure spaces has been under consideration in a work by Kontsevich and Soibelman [528], in relation to Quantum Field Theory, mirror symmetry and heat kernels; see also [756]. Kontsevich pointed out to me that the class of spaces covered by this approach is probably strictly smaller than the class defined here, since it does not seem to include the normed spaces considered in Example 29.16; he also suggested that this point of view is related to the one of Ollivier, described below.

To close this list, I shall evoke the recent independent contributions by Joulin [494, 495] and Ollivier [662] who suggested defining the infimum of the Ricci curvature as the best constant  $K$  in the contraction inequality

$$
W_1(P_t \delta_x, P_t \delta_y) \le e^{-Kt} d(x, y),
$$

where  $P_t$  is the heat semigroup (defined on probability measures); or equivalently, as the best constant  $K$  in the inequality

$$
||P_t^* f||_{\text{Lip}} \le e^{-Kt} ||f||_{\text{Lip}},
$$

where  $P_t^*$  is the adjoint of  $P_t$  (that is, the heat semigroup on functions). Similar inequalities have been used before in concentration theory [305, 595], and in the study of spectral gaps [231, 232] or large-time convergence [310, 458, 679, 729]. In a Riemannian setting, this definition is justified by Sturm and von Renesse [764], who showed that one recovers in this way the usual Ricci curvature bound, the key observation being that the parallel transport is close to be optimal in some sense. (The distance  $W_1$  may be replaced by any  $W_r$ ,  $1 \leq r < \infty$ .) This point of view is natural when the problem includes a distinguished Markov kernel; in particular, it leads to the possibility of treating discrete spaces equipped with a random walk. Ollivier [662] has demonstrated the geometric interest of this notion on an impressive list of examples and applications, most of them in discrete spaces; and he also derived geometric consequences such as concentration of measure. The dimension can probably be taken into account by using refined estimates on the rates of convergence. On the other hand, the stability of this notion requires a stronger topology than just measured Gromov– Hausdorff convergence: also the Markov process should converge. (The topology explored in [97] might be useful.) To summarize:

- there are two main classes of synthetic definitions of  $CD(K, N)$ bounds: those based on displacement convexity of integral functionals (as in Chapter 17) and those based on contraction properties of diffusion equations in Wasserstein distances (as in [764]), the Jordan–Kinderlehrer–Otto theorem [493] ensuring the formal equivalence of these points of view;
- the first definition was discretized by Bonciocat and Sturm [143], while the second one was discretized by Joulin [495] and Ollivier [662].

Remark 29.30 essentially means that manifolds with (strongly) negative Ricci curvature bounds are dense in Gromov–Hausdorff topology. Statements of this type are due in particular to Lokhamp; see Berger [99, Section 12.3.5] and the references there provided.

Next, here are some further comments about the ingredients in the proof of Theorem 29.24.

The definition and properties of the functional  $U_{\nu}$  acting on singular measures (Definition 29.1, Proposition 29.19, Theorem  $29.20(i)–(ii)$ ) were worked out in detail in [577]. At least some of these properties belong to folklore, but it is not so easy to find precise references. For the particular case  $U(r) = r \log r$ , there is a detailed alternative proof of Theorem  $29.20(i)$ –(ii) in [30, Lemmas 9.4.3 to 9.4.5, Corollary 9.4.6] when  $X$  is a separable Hilbert space, possibly infinite-dimensional; the proof of the contraction property in that reference does not rely on the Legendre representation. There is also a proof of the lower semicontinuity and the contraction property, for general functions  $U$ , in [556, Chapter 1]; the arguments there do not rely on the Legendre representation either. I personally advocate the use of the Legendre representation, as an efficient and versatile tool.

In [577], we also discussed the extension of these properties to spaces that are not necessarily compact, but only locally compact, and reference measures that are not necessarily finite, but only locally finite. Integrability conditions at infinity should be imposed on  $\mu$ , as in Theorem 17.8. The discussion on the Legendre representation in this generalized setting is a bit subtle, for instance it is in general impossible to impose at the same time  $\varphi \in C_c(\mathcal{X})$  and  $U^*(\varphi) \in C_c(\mathcal{X})$ . Here I preferred to limit the use of the Legendre representation (and the lower semicontinuity) to the compact case; but another approximation argument will be used in the next chapter to extend the displacement convexity inequalities to probability measures that are not compactly supported.

The density of  $C(\mathcal{X})$  in  $L^1(\mathcal{X}, \mu)$ , where  $\mathcal X$  is a locally compact space and  $\nu$  is a finite Borel measure, is a classical result that can be found e.g. in [714, Theorem 3.14]. It is also true that nonnegative continuous functions are dense in  $L^1_+(\mathcal{X}, \mu)$ , or that continuous probability densities are dense in the space of probability densities, equipped with the  $L<sup>1</sup>$  norm. All these results can be derived from Lusin's approximation theorem [714, Theorem 2.24].

The Tietze–Urysohn extension theorem states the following: If  $(\mathcal{X}, d)$  is a metric space, C is a closed subset of X, and  $f : C \to \mathbb{R}$ is uniformly continuous on  $C$ , then it is possible to extend  $f$  into a continuous function on the whole of  $X$ , with preservation of the supremum norm of  $f$ ; see [318, Theorem 2.6.4].

The crucial approximation scheme based on regularization kernels was used by Lott and myself in [577]. In Appendix C of that reference, we worked out in detail the properties stated after Definition 29.34. We used this tool extensively, and also discussed regularization in noncompact spaces. Even in the framework of absolutely continuous measures, the approach based on the regularizing kernel has many advantages over Lusin's approximation theorem (it is explicit, linear, preserves convexity inequalities, etc.).

The existence of continuous partitions of unity was used in the First Appendix to construct the regularizing kernels, and in the Second Ap-

pendix about the separability of  $L^1(\mathcal{X}; C(\mathcal{Y}))$ ; the proof of this classical result can be found e.g. in [714, Theorem 2.13].

Apart from Theorem 29.28, other "external" consequences of the theory of weak  $CD(K, N)$  spaces are discussed in [577, Section 7.2], in the cases  $K = 0$  and  $N = \infty$ .

Lemma 29.7 is taken from a recent work of mine with Figalli [372]. It will be used later in the proof of Theorems 30.37 and 30.42.

I shall conclude with some remarks about the examples considered in this chapter.

The following generalization of Example 29.13 is proven in [30, Theorems 9.4.10 and 9.4.11. If  $\nu$  is a finite measure on  $\mathbb{R}^n$  such that  $H_{\nu}$  is displacement convex, then  $\nu$  takes the form  $e^{-V}\mathcal{H}^k$ , where V is lower semicontinuous and  $\mathcal{H}^k$  is the k-dimensional Hausdorff measure,  $k = \dim(\text{Spt }\nu)$ . The same reference extends to infinite-dimensional separable Hilbert spaces the result according to which  $H_{\nu}$  is displacement convex if and only if  $\nu$  is log-concave.

Example 29.15 was treated in [577]: We show that the quotient of a  $CD(K, N)$  Riemannian manifold by a compact Lie group action is still a weak  $CD(K, N)$  space, if  $K = 0$  or  $N = \infty$ . (The definition of  $CD(K,\infty)$  space used in [577] is not exactly the same, but Theorem 30.32 in the next chapter shows that both definitions coincide in nonbranching spaces.) The same theorem is also certainly true for all values of  $K$  and  $N$ , although this was never written down explicitly. More problematic is the extension to noncompact spaces  $\mathcal X$  or noncompact Lie groups  $G$ . The proof uses indeed an isomorphism between  $P_2(\mathcal{X}/G)$  and  $P_2(\mathcal{X})^G$ , the set of probability measures on X which are invariant under the action of  $G$ ; but this isomorphism might not exist in noncompact situations. Take for instance  $\mathcal{X} = \mathbb{R}$ ,  $G = \mathbb{Z}$ , then  $P_2(\mathcal{X})^G$ is the set of probability measures invariant by integer translation, which is empty.

Elementary background on Lie group actions, and possibly singular spaces obtained by this procedure, can be found in Burago, Burago and Ivanov [174, Chapter 3]. This topic is also treated in dozens of books on Riemannian geometry.

Example 29.16 will be considered in more detail in the final chapter. It can be considered as the precursor of a large work by Ohta [657] investigating curvature-dimension conditions in Finsler spaces.

Example 29.31 was explained to me by Lott; it is studied in [574]. This example shows that a lower bound on the Ricci curvature is not enough to ensure the continuity of the Hausdorff measure under measured Gromov–Hausdorff convergence. Such a phenomenon is necessarily linked with collapsing (loss of dimension), as shown by the results of continuity of the Hausdorff measure for  $n$ -dimensional Alexandrov spaces  $[751]$  or for limits of *n*-dimensional Riemannian manifolds with Ricci curvature bounded below [228, Theorem 5.9].

Example 29.17 was also suggested by Lott.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.