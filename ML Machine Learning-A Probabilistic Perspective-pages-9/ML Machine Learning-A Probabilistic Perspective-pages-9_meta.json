{"table_of_contents": [{"title": "7 Linear regression", "heading_level": null, "page_id": 0, "polygon": [[85.5, 99.2724609375], [259.5, 99.2724609375], [257.25, 149.25], [83.25, 143.0947265625]]}, {"title": "7.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 207.0], [195.75, 207.0], [195.75, 217.845703125], [101.25, 217.845703125]]}, {"title": "7.2 Model specification", "heading_level": null, "page_id": 0, "polygon": [[99.0, 303.75], [230.25, 303.75], [230.25, 316.248046875], [99.0, 316.248046875]]}, {"title": "7.3 Maximum likelihood estimation (least squares)", "heading_level": null, "page_id": 0, "polygon": [[99.75, 536.25], [372.75, 536.25], [372.75, 547.3828125], [99.75, 547.3828125]]}, {"title": "7.3.1 Derivation of the MLE", "heading_level": null, "page_id": 2, "polygon": [[94.5, 534.75], [234.84375, 534.75], [234.84375, 545.16796875], [94.5, 545.16796875]]}, {"title": "7.3.2 Geometric interpretation", "heading_level": null, "page_id": 3, "polygon": [[93.0, 363.75], [247.5, 363.75], [247.5, 373.67578125], [93.0, 373.67578125]]}, {"title": "7.3.3 Convexity", "heading_level": null, "page_id": 4, "polygon": [[93.0, 501.0], [177.75, 501.0], [177.75, 511.62890625], [93.0, 511.62890625]]}, {"title": "7.4 Robust linear regression *", "heading_level": null, "page_id": 6, "polygon": [[99.75, 342.75], [264.75, 342.75], [264.75, 353.7421875], [99.75, 353.7421875]]}, {"title": "7.5 Ridge regression", "heading_level": null, "page_id": 8, "polygon": [[99.75, 260.25], [216.75, 260.25], [216.75, 271.318359375], [99.75, 271.318359375]]}, {"title": "7.5.1 Basic idea", "heading_level": null, "page_id": 8, "polygon": [[94.5, 332.25], [178.5, 332.25], [178.5, 342.984375], [94.5, 342.984375]]}, {"title": "7.5.2 Numerically stable computation *", "heading_level": null, "page_id": 10, "polygon": [[93.0, 189.75], [288.0, 189.75], [288.0, 200.91796875], [93.0, 200.91796875]]}, {"title": "7.5.3 Connection with PCA *", "heading_level": null, "page_id": 11, "polygon": [[93.0, 426.75], [238.5, 426.75], [238.5, 437.58984375], [93.0, 437.58984375]]}, {"title": "7.5.4 Regularization effects of big data", "heading_level": null, "page_id": 13, "polygon": [[93.0, 208.5], [287.71875, 208.5], [287.71875, 218.63671875], [93.0, 218.63671875]]}, {"title": "7.6 Bayesian linear regression", "heading_level": null, "page_id": 14, "polygon": [[99.0, 537.75], [267.75, 537.75], [267.75, 548.33203125], [99.0, 549.59765625]]}, {"title": "7.6.1 Computing the posterior", "heading_level": null, "page_id": 15, "polygon": [[94.5, 112.5], [247.5, 112.5], [247.5, 122.44921875], [94.5, 122.44921875]]}, {"title": "7.6.2 Computing the posterior predictive", "heading_level": null, "page_id": 16, "polygon": [[93.0, 560.25], [297.0, 560.25], [297.0, 571.4296875], [93.0, 571.4296875]]}, {"title": "7.6.3 Bayesian inference when \\sigma^2 is unknown *", "heading_level": null, "page_id": 17, "polygon": [[92.109375, 311.25], [330.1875, 311.25], [330.1875, 324.31640625], [92.109375, 324.31640625]]}, {"title": "7.6.3.1 Conjugate prior", "heading_level": null, "page_id": 17, "polygon": [[88.59375, 394.5], [199.5, 394.5], [199.5, 404.3671875], [88.59375, 404.3671875]]}, {"title": "7.6.3.2 Uninformative prior", "heading_level": null, "page_id": 19, "polygon": [[87.0, 386.25], [217.828125, 386.25], [217.828125, 396.140625], [87.0, 396.140625]]}, {"title": "7.6.3.3 An example where Bayesian and frequentist inference coincide *", "heading_level": null, "page_id": 20, "polygon": [[86.765625, 338.25], [409.5, 338.25], [409.5, 348.6796875], [86.765625, 348.6796875]]}, {"title": "7.6.4 EB for linear regression (evidence procedure)", "heading_level": null, "page_id": 21, "polygon": [[92.8125, 267.75], [345.0, 267.75], [345.0, 277.646484375], [92.8125, 277.646484375]]}, {"title": "Exercises", "heading_level": null, "page_id": 22, "polygon": [[129.75, 342.0], [178.5, 342.0], [178.5, 352.79296875], [129.75, 352.79296875]]}, {"title": "Exercise 7.3 Centering and ridge regression", "heading_level": null, "page_id": 23, "polygon": [[129.75, 231.75], [290.25, 231.75], [290.25, 241.259765625], [129.75, 241.259765625]]}, {"title": "Exercise 7.6 MLE for simple linear regression", "heading_level": null, "page_id": 24, "polygon": [[129.75, 62.25], [296.71875, 62.25], [296.71875, 71.46826171875], [129.75, 71.46826171875]]}, {"title": "Exercise 7.9 Generative model for linear regression", "heading_level": null, "page_id": 25, "polygon": [[129.75, 480.0], [317.8125, 480.0], [317.8125, 488.84765625], [129.75, 488.84765625]]}, {"title": "Exercise 7.10 Bayesian linear regression using the g-prior", "heading_level": null, "page_id": 26, "polygon": [[129.0, 165.0], [339.75, 165.0], [339.75, 174.33984375], [129.0, 174.33984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 27], ["Text", 6], ["SectionHeader", 4], ["Equation", 4], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 81], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 793, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 60], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 66], ["Equation", 8], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6270, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 41], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 30], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1268, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 63], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 832, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 491], ["Line", 53], ["TableCell", 48], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1780, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 55], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 54], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 802, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 86], ["Equation", 7], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["Line", 44], ["Equation", 7], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 48], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 86], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1231, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 580], ["Line", 53], ["Equation", 8], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 82], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 937, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 48], ["Equation", 9], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 74], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 943, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 654], ["Line", 49], ["Equation", 9], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["TableCell", 120], ["Line", 39], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7812, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 44], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 51], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 892, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 67], ["TableCell", 30], ["Text", 9], ["Equation", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 596], ["Line", 86], ["Equation", 8], ["Text", 8], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 53], ["ListItem", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 25], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListItem", 2], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-9"}