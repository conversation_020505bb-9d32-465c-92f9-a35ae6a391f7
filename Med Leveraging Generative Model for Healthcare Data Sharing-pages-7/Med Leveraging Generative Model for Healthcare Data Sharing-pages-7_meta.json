{"table_of_contents": [{"title": "MedCLIP-SAM: Bridging Text\nand Image Towards Universal Medical\nImage Segmentation", "heading_level": null, "page_id": 0, "polygon": [[88.73046875, 51.7022705078125], [366.73828125, 51.7022705078125], [366.73828125, 102.427490234375], [88.73046875, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[39.04436860068259, 54.77052868391451], [134.40273037542661, 54.77052868391451], [134.40273037542661, 66.276611328125], [39.04436860068259, 66.276611328125]]}, {"title": "2 Methods and Materials", "heading_level": null, "page_id": 1, "polygon": [[38.**********, 558.873046875], [197.4744027303754, 558.873046875], [197.4744027303754, 570.59765625], [38.**********, 570.59765625]]}, {"title": "2.1 Efficient BiomedCLIP Fine-Tuning with the DHN-NCE Loss", "heading_level": null, "page_id": 2, "polygon": [[52.55972696245733, 292.6096737907762], [386.2890625, 292.6096737907762], [386.2890625, 302.234375], [52.55972696245733, 302.234375]]}, {"title": "2.2 Zero-Shot and <PERSON><PERSON><PERSON> Supervised Medical Image Segmentation", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 380.392575928009], [386.6894197952218, 380.392575928009], [386.6894197952218, 390.49462890625], [38.29351535836177, 390.49462890625]]}, {"title": "2.3 Datasets, Experimental Setup, and Validation Metrics", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 55.7733154296875], [353.65187713310576, 55.7733154296875], [353.65187713310576, 65.7066650390625], [52.55972696245733, 65.7066650390625]]}, {"title": "3 Results", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 53.28997802734375], [103.76953125, 53.28997802734375], [103.76953125, 66.23590087890625], [38.29351535836177, 66.23590087890625]]}, {"title": "3.1 Cross-Modal Retrieval Accuracy and gScoreCAM Vs.\nGradCAM", "heading_level": null, "page_id": 5, "polygon": [[39.04436860068259, 77.96051025390625], [341.6015625, 77.96051025390625], [341.6015625, 101.2061767578125], [39.04436860068259, 101.2061767578125]]}, {"title": "3.2 Zero-Shot and <PERSON>akly Supervised Segmentation", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 429.2509765625], [309.35153583617745, 429.2509765625], [309.35153583617745, 440.32421875], [38.29351535836177, 440.32421875]]}, {"title": "4 Discussion", "heading_level": null, "page_id": 6, "polygon": [[51.80887372013652, 545.1943359375], [135.90443686006824, 545.1943359375], [135.90443686006824, 556.9189453125], [51.80887372013652, 556.9189453125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 120.34008789062501], [139.111328125, 120.34008789062501], [139.111328125, 133.693115234375], [52.55972696245733, 133.693115234375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[51.80887372013652, 305.16552734375], [117.13310580204778, 305.16552734375], [117.13310580204778, 318.**********], [51.80887372013652, 318.**********]]}, {"title": "MedSynth: Leveraging Generative Model\nfor Healthcare Data Sharing", "heading_level": null, "page_id": 11, "polygon": [[68.32764505119454, 51.66156005859375], [356.6552901023891, 51.66156005859375], [356.6552901023891, 84.514892578125], [68.32764505119454, 84.514892578125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 12, "polygon": [[52.55972696245733, 54.77052868391451], [148.779296875, 54.77052868391451], [148.779296875, 66.602294921875], [52.55972696245733, 66.602294921875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 13, "polygon": [[39.04436860068259, 96.72802734375], [141.9112627986348, 96.72802734375], [141.9112627986348, 108.289794921875], [39.04436860068259, 108.289794921875]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 558.2092238470192], [137.4061433447099, 558.2092238470192], [137.4061433447099, 569.294921875], [38.29351535836177, 569.294921875]]}, {"title": "3.1 Phase I Pre-train the Generator", "heading_level": null, "page_id": 14, "polygon": [[52.55972696245733, 314.28466796875], [242.55859375, 314.28466796875], [242.55859375, 323.7294921875], [52.55972696245733, 323.7294921875]]}, {"title": "3.2 Phase II Fine-Tune the Generator Using ViT", "heading_level": null, "page_id": 15, "polygon": [[38.88671875, 179.1259765625], [294.3344709897611, 179.1259765625], [294.3344709897611, 188.57080078125], [38.88671875, 188.57080078125]]}, {"title": "4 Experimental Analysis", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 132.04949381327336], [207.5390625, 132.04949381327336], [207.5390625, 143.5450439453125], [52.55972696245733, 143.5450439453125]]}, {"title": "4.1 Datasets", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 157.142333984375], [123.89078498293514, 157.142333984375], [123.89078498293514, 167.07568359375], [52.55972696245733, 167.07568359375]]}, {"title": "4.2 Implementation Details", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 338.37682789651296], [198.22525597269623, 338.37682789651296], [198.22525597269623, 348.80712890625], [52.55972696245733, 348.80712890625]]}, {"title": "4.3 Comparisons with State-of-the-Art", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 561.80419921875], [255.66406249999997, 561.80419921875], [255.66406249999997, 571.57470703125], [52.55972696245733, 571.57470703125]]}, {"title": "4.4 Generalization Ability Comparison", "heading_level": null, "page_id": 17, "polygon": [[38.29351535836177, 443.90673828125], [241.77474402730374, 443.90673828125], [241.77474402730374, 454.97998046875006], [38.29351535836177, 454.97998046875006]]}, {"title": "4.5 Membership Inference Attack Analysis", "heading_level": null, "page_id": 18, "polygon": [[51.80887372013652, 448.7919921875], [276.71875, 448.7919921875], [276.71875, 459.86523437500006], [51.80887372013652, 459.86523437500006]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 156.328125], [124.64163822525596, 156.328125], [124.64163822525596, 170.33251953125], [38.29351535836177, 170.33251953125]]}, {"title": "References", "heading_level": null, "page_id": 19, "polygon": [[39.04436860068259, 446.51220703125], [102.91015625, 446.51220703125], [102.91015625, 460.84228515625006], [39.04436860068259, 460.84228515625006]]}, {"title": "One Registration is Worth Two\nSegmentations", "heading_level": null, "page_id": 22, "polygon": [[116.66015625, 51.58013916015625], [337.51953125, 51.58013916015625], [337.51953125, 96.71868896484375], [116.66015625, 96.71868896484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 22, "polygon": [[52.9052734375, 546.17138671875], [148.88671875, 546.17138671875], [148.88671875, 558.54736328125], [52.9052734375, 558.54736328125]]}, {"title": "2 ROI-Based Correspondence Representation", "heading_level": null, "page_id": 24, "polygon": [[52.55972696245733, 220.58267716535434], [330.3754266211604, 220.58267716535434], [330.3754266211604, 231.56103515625003], [52.55972696245733, 231.56103515625003]]}, {"title": "3 One Registration Versus Two Segmentations", "heading_level": null, "page_id": 25, "polygon": [[38.29351535836177, 207.948974609375], [322.8668941979522, 207.948974609375], [322.8668941979522, 220.650634765625], [38.29351535836177, 220.650634765625]]}, {"title": "4 Segment Everything and Match: A Training-Free\nRegistration Algorithm", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 54.71484375], [367.91808873720134, 54.71484375], [367.91808873720134, 80.76953125], [52.55972696245733, 80.76953125]]}, {"title": "4.1 Preliminary: SAM Architecture", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 93.03487064116986], [240.2730375426621, 93.03487064116986], [240.2730375426621, 103.6488037109375], [52.55972696245733, 103.6488037109375]]}, {"title": "4.2 The SAMReg Algorithm", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 358.63442069741285], [204.9829351535836, 358.63442069741285], [204.9829351535836, 369.3251953125], [52.55972696245733, 369.3251953125]]}, {"title": "5 Experiments and Results", "heading_level": null, "page_id": 28, "polygon": [[52.55972696245733, 514.580078125], [222.578125, 514.580078125], [222.578125, 526.9560546875], [52.55972696245733, 526.9560546875]]}, {"title": "Ablative Study:", "heading_level": null, "page_id": 29, "polygon": [[39.04436860068259, 559.5244140625], [119.23828125, 559.5244140625], [119.23828125, 570.59765625], [39.04436860068259, 570.59765625]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 30, "polygon": [[51.80887372013652, 225.04736328125], [139.00390625, 225.04736328125], [139.00390625, 238.72607421875003], [51.80887372013652, 238.72607421875003]]}, {"title": "References", "heading_level": null, "page_id": 30, "polygon": [[52.55972696245733, 426.31982421875], [117.13310580204778, 426.31982421875], [117.13310580204778, 439.99853515625], [52.55972696245733, 439.99853515625]]}, {"title": "PEPSI: Pathology-Enhanced\nPulse-Sequence-Invariant Representations\nfor Brain MRI", "heading_level": null, "page_id": 33, "polygon": [[66.07508532423208, 51.01019287109375], [359.6484375, 51.01019287109375], [359.6484375, 102.59033203125], [66.07508532423208, 102.59033203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 33, "polygon": [[38.**********, 520.11669921875], [134.4921875, 520.11669921875], [134.4921875, 533.79541015625], [38.**********, 533.79541015625]]}, {"title": "2 Approach", "heading_level": null, "page_id": 35, "polygon": [[38.29351535836177, 314.447509765625], [118.37890625, 314.447509765625], [118.37890625, 327.800537109375], [38.29351535836177, 327.800537109375]]}, {"title": "2.1 Generating Pathology-Encoded Training Data", "heading_level": null, "page_id": 35, "polygon": [[38.29351535836177, 450.74609375], [297.33788395904435, 450.74609375], [297.33788395904435, 462.47070312500006], [38.29351535836177, 462.47070312500006]]}, {"title": "2.2 Representing Across Contrasts, Pathologies, Datasets", "heading_level": null, "page_id": 36, "polygon": [[52.55972696245733, 528.91015625], [350.625, 528.91015625], [350.625, 538.6806640625], [52.55972696245733, 538.6806640625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 39, "polygon": [[38.29351535836177, 366.068359375], [134.921875, 366.068359375], [134.921875, 379.7470703125], [38.29351535836177, 379.7470703125]]}, {"title": "3.1 Anatomy and Pathology Image Synthesis", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 327.96337890625], [287.890625, 327.96337890625], [287.890625, 338.38525390625], [52.55972696245733, 338.38525390625]]}, {"title": "3.2 Pathology Segmentation", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 552.206974128234], [202.73037542662115, 552.206974128234], [202.73037542662115, 562.78125], [52.55972696245733, 562.78125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 41, "polygon": [[38.29351535836177, 289.044189453125], [124.64163822525596, 289.044189453125], [124.64163822525596, 301.7***********], [38.29351535836177, 301.7***********]]}, {"title": "References", "heading_level": null, "page_id": 41, "polygon": [[38.29351535836177, 543.240234375], [103.232421875, 543.240234375], [103.232421875, 556.267578125], [38.29351535836177, 556.267578125]]}, {"title": "Prompting Vision-Language Models\nfor Dental Notation Aware Abnormality\nDetection", "heading_level": null, "page_id": 44, "polygon": [[86.15234375, 51.0509033203125], [369.74609375, 51.0509033203125], [369.74609375, 103.404541015625], [86.15234375, 103.404541015625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 45, "polygon": [[39.04436860068259, 97.705078125], [134.40273037542661, 97.705078125], [134.40273037542661, 110.0810546875], [39.04436860068259, 110.0810546875]]}, {"title": "2 Method", "heading_level": null, "page_id": 46, "polygon": [[52.55972696245733, 496.68616422947133], [120.3125, 496.68616422947133], [120.3125, 508.71777343750006], [52.55972696245733, 508.71777343750006]]}, {"title": "2.1 Task Formulation", "heading_level": null, "page_id": 47, "polygon": [[38.29351535836177, 106.0914306640625], [154.04296875, 106.0914306640625], [154.04296875, 116.67614746093751], [38.29351535836177, 116.67614746093751]]}, {"title": "2.2 Dental Notation Aware Tooth Identification", "heading_level": null, "page_id": 47, "polygon": [[38.88671875, 402.***********], [286.8259385665529, 402.***********], [286.8259385665529, 413.***********], [38.88671875, 413.***********]]}, {"title": "2.3 Multi-level Dental Abnormality Detection", "heading_level": null, "page_id": 48, "polygon": [[52.55972696245733, 153.0573678290214], [292.0819112627986, 153.0573678290214], [292.0819112627986, 163.***********], [52.55972696245733, 163.***********]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 49, "polygon": [[38.29351535836177, 53.12713623046875], [207.98634812286687, 53.12713623046875], [207.98634812286687, 66.64300537109375], [38.29351535836177, 66.64300537109375]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 49, "polygon": [[38.29351535836177, 78.12335205078125], [167.578125, 78.12335205078125], [167.578125, 89.9700927734375], [38.29351535836177, 89.9700927734375]]}, {"title": "3.2 Comparison with State-of-the-Art Methods", "heading_level": null, "page_id": 49, "polygon": [[38.29351535836177, 337.***********], [284.66796875, 337.***********], [284.66796875, 349.***********], [38.29351535836177, 349.***********]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 52, "polygon": [[52.55972696245733, 54.226318359375], [158.232421875, 54.226318359375], [158.232421875, 66.602294921875], [52.55972696245733, 66.602294921875]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 52, "polygon": [[52.55972696245733, 361.5087890625], [139.00390625, 361.5087890625], [139.00390625, 375.8388671875], [52.55972696245733, 375.8388671875]]}, {"title": "References", "heading_level": null, "page_id": 53, "polygon": [[39.04436860068259, 53.37139892578125], [103.447265625, 53.37139892578125], [103.447265625, 66.80584716796875], [39.04436860068259, 66.80584716796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 39], ["Text", 7], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 44], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 82], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 57], ["Equation", 4], ["Text", 3], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 41], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["TableCell", 103], ["Line", 40], ["SectionHeader", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7163, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 573], ["TableCell", 199], ["Line", 41], ["Text", 3], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 42], ["ListItem", 8], ["Reference", 8], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 51], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 51], ["Line", 21], ["ListItem", 7], ["Reference", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 41], ["Text", 4], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 560, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 45], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 42], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 39], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 43], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 36], ["Text", 6], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["TableCell", 193], ["Line", 61], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Text", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3522, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 40], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 51], ["ListItem", 21], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 19], ["ListItem", 8], ["Reference", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 548, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 44], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 54], ["TextInlineMath", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 705, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 711], ["Line", 40], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 41], ["TextInlineMath", 4], ["SectionHeader", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 51], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 652, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 425], ["TableCell", 172], ["Line", 36], ["Reference", 4], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 48], ["TableCell", 32], ["Text", 4], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 41], ["Text", 6], ["ListItem", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 51], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 26], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 42], ["Text", 7], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 45], ["TextInlineMath", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 57], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 50], ["Text", 4], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 51], ["TextInlineMath", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["Equation", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 632], ["TableCell", 430], ["Line", 62], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14182, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 59], ["Text", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 893, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["TableCell", 82], ["Line", 74], ["Text", 5], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 40], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListItem", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 51], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 60], ["Line", 27], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 43], ["Text", 11], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Line", 80], ["Span", 63], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 755, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 41], ["TextInlineMath", 3], ["Equation", 3], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["Line", 48], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["TableCell", 133], ["Line", 37], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5708, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["TableCell", 256], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11487, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["TableCell", 134], ["Line", 43], ["Reference", 4], ["Caption", 3], ["Table", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["PictureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 7946, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 36], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 50], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 36], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-7"}