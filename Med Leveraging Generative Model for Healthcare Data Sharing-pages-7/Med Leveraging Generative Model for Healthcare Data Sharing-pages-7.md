Image /page/0/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line segment on the left and a flag shape on the right. The button is light gray with a subtle gradient, and the text is dark gray.

# **MedCLIP-SAM: Bridging Text and Image Towards Universal Medical Image Segmentation**

<PERSON><PERSON><sup>1( $\boxtimes$ )</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><sup>1</sup>, and Yi<PERSON><sup>2</sup>

 $1$  Department of Electrical and Computer Engineering, Concordia University, Montreal, Canada

*{*taha.koleilat,hojat.asgarian<PERSON>hkordi,hassan.rivaz*}*@concordia.ca <sup>2</sup> Department of Computer Science and Software Engineering, Concordia University,

Montreal, Canada

<EMAIL>

**Abstract.** Medical image segmentation of anatomical structures and pathology is crucial in modern clinical diagnosis, disease study, and treatment planning. To date, great progress has been made in deep learningbased segmentation techniques, but most methods still lack data efficiency, generalizability, and interactability. Consequently, the development of new, precise segmentation methods that demand fewer labeled datasets is of utmost importance in medical image analysis. Recently, the emergence of foundation models, such as CLIP and Segment-Anything-Model (SAM), with comprehensive cross-domain representation opened the door for interactive and universal image segmentation. However, exploration of these models for data-efficient medical image segmentation is still limited but is highly necessary. In this paper, we propose a novel framework, called MedCLIP-SAM that combines CLIP and SAM models to generate segmentation of clinical scans using text prompts in both zero-shot and weakly supervised settings. To achieve this, we employed a new Decoupled Hard Negative Noise Contrastive Estimation (DHN-NCE) loss to fine-tune the BiomedCLIP model and the recent gScoreCAM to generate prompts to obtain segmentation masks from SAM in a zero-shot setting. Additionally, we explored the use of zeroshot segmentation labels in a weakly supervised paradigm to improve the segmentation quality further. By extensively testing three diverse segmentation tasks and medical image modalities (breast tumor ultrasound, brain tumor MRI, and lung X-ray), our proposed framework has demonstrated excellent accuracy. Code is available at [https://github.](https://github.com/HealthX-Lab/MedCLIP-SAM) [com/HealthX-Lab/MedCLIP-SAM.](https://github.com/HealthX-Lab/MedCLIP-SAM)

**Keywords:** Image segmentation · Foundation models · Zero-shot learning · Weakly Supervised Semantic Segmentation

## 1 Introduction

With the increasing availability of radiological technologies, there is a pressing need for accurate and efficient medical image segmentation to aid the study, diagnosis, and treatment of various medical conditions [\[29\]](#page-10-0). Deep learning (DL) techniques have been established as state-of-the-art in the domain, but current methods often face three major limitations, hindering their widespread clinical adoption. First, the lack of large well-annotated data sets is a major bottleneck for DL model development. Second, the lack of interactivity and interpretability limits the credence of the methods. Lastly, most trained models are taskand contrast/modality-specific with low flexibility. While many self- and weakly supervised methods  $[4,8,30]$  $[4,8,30]$  $[4,8,30]$  $[4,8,30]$  $[4,8,30]$  have been proposed to tackle training data efficiency and explainable AI (XAI) methods (e.g., uncertainty estimation [\[19](#page-9-0)[,21](#page-9-1)] and saliency map [\[2,](#page-8-2)[3](#page-8-3)]) are being actively investigated, cross-domain generalization has been a challenge. Recently, the introduction of foundation models, such as the CLIP (Contrastive Language-Image Pre-Training) [\[26\]](#page-9-2) and SAM (Segment Anything Model) [\[14\]](#page-9-3) opened the door for interactive and universal medical image segmentation. To date, several groups have endeavored to adapt CLIP and SAM for radiological tasks from natural images, notably the development of BiomedCLIP [\[33](#page-10-2)] and MedSAM [\[22](#page-9-4)], which were pre-trained on millions of biomedical data. However, more efficient parameter fine-tuning methods can be beneficial to further boost the performance of these foundation models in radiological applications. On the other hand, with a strong interest in SAM, which requires interactive prompts to guide segmentation, a few techniques were proposed to fine-tune SAM without prompts [\[9,](#page-9-5)[12](#page-9-6)], generate prompts through Class Activation Map (CAM) from classification tasks  $[16,17,20]$  $[16,17,20]$  $[16,17,20]$  $[16,17,20]$  $[16,17,20]$ , and to refine its output based on weak supervision [\[7,](#page-8-4)[13](#page-9-10)[,31](#page-10-3)]. Still at the nascent phase, using foundation models for interactive and universal medical image segmentation necessitates additional investigation and is of significant interest.

To address the aforementioned needs, we present MedCLIP-SAM, a novel framework that leverages BiomedCLIP [\[33\]](#page-10-2) and SAM [\[14](#page-9-3)] for text-prompt-based interactive and universal medical image segmentation in both zero-shot and weakly supervision settings. The contributions of this work are threefold: **First**, we proposed a novel CLIP training/fine-tuning method, called the Decoupled Hard Negative Noise Contrastive Estimation (DHN-NCE). **Second**, we proposed a zero-shot medical segmentation method by combining CLIP and SAM in radiological tasks for the first time. **Lastly**, a weakly-supervised strategy was explored with the attempt to further refine zero-shot segmentation results, and the full proposed technique was extensively validated on three different segmentation tasks and modalities (breast tumor segmentation in ultrasound, brain tumor segmentation in MRI, and lung segmentation in chest X-ray).

## 2 Methods and Materials

An overview of the proposed MedCLIP-SAM framework is presented in Fig. [1,](#page-2-0) organized into three distinct stages: BiomedCLIP fine-tuning employing our new DHN-NCE loss, zero-shot segmentation guided by text-prompts, and weakly supervised segmentation for potential label refinement.

Image /page/2/Figure/2 description: This figure illustrates a three-stage framework for medical image segmentation. The first stage, 'DHN-NCE Fine-tuning,' involves a text encoder processing the description 'Loss of gray white matter differentiation in the right parietal lobe' and an image encoder processing a CT scan. These are combined with positive and hard negative labels in a matrix. The second stage, 'Zero-shot Segmentation,' uses a Segment Anything Model (SAM) with an image encoder and prompt encoder, taking 'Medical Images' and 'Text Prompts' (specifically 'benign breast tumour') as input. This stage also incorporates a fine-tuned BiomedCLIP model and gScoreCAM to generate saliency maps and box prompts after CRF post-processing. The third stage, 'Weakly Supervised Segmentation,' utilizes pseudo-masks generated from the previous stage, a DiceCE loss function, and training to produce a segmentation network. The figure also includes a legend indicating 'Frozen' and 'Trainable' components.

<span id="page-2-0"></span>**Fig. 1.** An overview of the proposed MedCLIP-SAM framework.

### 2.1 Efficient BiomedCLIP Fine-Tuning with the DHN-NCE Loss

**Decoupled Hard Negative Noise Contrastive Estimation Loss.** A CLIP model is trained in large datasets of images and the corresponding texts. Specifically, an image encoder and a text encoder are used to extract features of images and texts and project them into vectors of the same dimension,  $\mathbf{I}_{p,i}$  and  $\mathbf{T}_{p,i}$ , respectively. Then, through contrastive learning, an embedding space shared by the image and text vectors is learned so that similar pairs (an image and its description) are closer together and dissimilar ones are farther apart. While BiomedCLIP [\[33\]](#page-10-2) was trained on medical charts/images and clinical texts, further fine-tuning can effectively benefit medical image-specific tasks. In CLIP training with the conventional InfoNCE loss [\[23](#page-9-11)], the *negative-positive-coupling (NPC)* effect [\[32\]](#page-10-4) can lead to sub-optimal learning efficiency, particularly in small batch sizes while for medical images, more nuanced discrimination between cases within the same imaging categories can be difficult. To solve these, we propose the Decoupled Hard Negative Noise Contrastive Estimation (DHN-NCE) loss, which 1) combines the InfoNCE loss [\[23](#page-9-11)] with hard negative sampling [\[28](#page-10-5)] to focus on "close samples" and 2) adds decoupling contrastive learning [\[32\]](#page-10-4) by removing the positive term in the denominator to allow smaller batch sizes. Specifically, the loss function  $\mathcal{L}_{DHN-NCE}$  uses weighting functions  $(\mathcal{W}_{\mathbf{I}_{p,i}}^{\nu\rightarrow t},\mathcal{W}_{\mathbf{T}_{p,i}\mathbf{I}_{p,j}}^{\nu\rightarrow v})$  to increase the penalty for negatives that happen to be very close to the anchor through image-to-text and text-to-image hardness parameters  $\beta_1, \beta_2 \geq 0$ . Here,  $t \to v$  means text-to-image, and  $v \to t$  denotes image-to-text.

$$
\mathcal{L}^{v \to t} = -\sum_{i=1}^{B} \frac{\mathbf{I}_{p,i} \mathbf{T}_{p,i}^{\top}}{\tau} + \sum_{i=1}^{B} \log \left( \sum_{j \neq i} e^{\mathbf{I}_{p,i} \mathbf{T}_{p,j}^{\top} / \tau} \mathcal{W}_{\mathbf{I}_{p,i} \mathbf{T}_{p,j}}^{v \to t} \right)
$$
(1)

646 T. Koleilat et al.

$$
\mathcal{L}^{t \to v} = -\sum_{i=1}^{B} \frac{\mathbf{T}_{p,i} \mathbf{I}_{p,i}^{\top}}{\tau} + \sum_{i=1}^{B} \log \left( \sum_{j 
eq i} e^{\mathbf{T}_{p,i} \mathbf{I}_{p,j}^{\top} / \tau} \mathcal{W}_{\mathbf{T}_{p,i} \mathbf{I}_{p,j}}^{t o v} \right) \quad (2)
$$

$$
\mathcal{L}_{DHN-NCE} = \mathcal{L}^{v \to t} + \mathcal{L}^{t \to v} \tag{3}
$$

where B is the batch size,  $\tau$  is the temperature parameter, and the hardness weighting formulas are as follows:

$$
\mathcal{W}_{\mathbf{I}_{p,i}\mathbf{T}_{p,j}}^{\nu \to t} = (B-1) \times \frac{e^{\beta_1 \mathbf{I}_{p,i}\mathbf{T}_{p,j}/\tau}}{\sum_{k \neq i} e^{\beta_1 \mathbf{I}_{p,i}\mathbf{T}_{p,k}/\tau}}
$$
(4)

$$
\mathcal{W}_{\mathbf{T}_p,i\mathbf{I}_{p,j}}^{t \to v} = (B-1) \times \frac{e^{\beta_2 \mathbf{T}_p,i\mathbf{I}_{p,j}/\tau}}{\sum_{k \neq i} e^{\beta_2 \mathbf{T}_p,i\mathbf{I}_{p,k}/\tau}}
$$
(5)

**BiomedCLIP Fine-Tuning.** We utilized the public MedPix dataset with different radiological modalities to fine-tune the BiomedCLIP model [\[33](#page-10-2)] with DHN-NCE loss. Here, we used the base Vision Transformer and PubMedBERT [\[33](#page-10-2)] as the image and text encoders. We cleaned the MedPix dataset by stripping off any special characters, leading and trailing white spaces, and deleting samples with captions of less than 20 characters. All images were resized to 224  $\times$ 224 pixels and normalized by the RGB channel means and standard deviations used in the original CLIP model [\[26](#page-9-2)]. After performing an 85%:15% split, we ended up with 20,292 training images and 3,515 images for validation. Here, we chose a low learning rate of 1E-6 with a decay rate of 50%, and fine-tuning was done on batches of 64 samples.

### 2.2 Zero-Shot and Weakly Supervised Medical Image Segmentation

With a fine-tuned BiomedCLIP model, we proposed a zero-shot universal medical image segmentation strategy, which leverages the recent XAI technique, gScoreCAM [\[6\]](#page-8-5) that provides visual saliency maps of text prompts in corresponding images for CLIP models. While gScoreCAM was shown to outperform gradCAM in natural images in accuracy and specificity, we adopted it in radiological tasks for the first time. Here, for an input image and a text prompt for the target anatomy/pathology, we first obtained an initial, coarse segmentation by post-processing the gScoreCAM map with a conditional random field (CRF) filter [\[15\]](#page-9-12) in order to produce discrete pixel-wise labels as initial segmentation masks. These generated initial segmentation labels are used to calculate 4 box coordinates (bounding boxes) that enclose each connected contour in the segmentation mask. Finally, we supply these bounding boxes as prompt inputs to SAM in order to produce a pseudo-mask as zero-shot segmentation. In the attempt to further enhance the accuracy of zero-shot segmentation, we used the resulting pseudo-masks as the sole supervision signal to train a Residual UNet [\[34](#page-10-6)] in a weakly supervised setting.

### 2.3 Datasets, Experimental Setup, and Validation Metrics

**BiomedCLIP Fine-Tuning Performance.** We validated the quality of BiomedCLIP fine-tuning by the accuracy of top 1 and top 2 matching retrievals for both image-to-text and text-to-image directions in the ROCO (Radiology Objects in COntext) dataset [\[24](#page-9-13)] which contains  $\approx 7.042$  multi-modal medical images spanning a myriad of clinical cases. We executed the experiments for 5 runs with a batch size of 50 with shuffling to ensure random bagging of different texts and images within a batch (thus, we get 70,420 shuffled examples). We compared different loss functions for fine-tuning, including the InfoNCE loss [\[23](#page-9-11)], DCL [\[32\]](#page-10-4), HN-NCE [\[25](#page-9-14)], and our DHN-NCE loss. For a fair comparison, we trained all the strategies using the same hyperparameters ( $\tau = 0.6$ , learning rate = 1E-6). For HN-NCE and DHN-NCE, we use the same hardness  $\beta_1 = \beta_2$  $= 0.15$ . As baselines, we also included the results of pre-trained BiomedCLIP [\[33](#page-10-2)], PMC-CLIP [\[18](#page-9-15)], and CLIP [\[26\]](#page-9-2).

**Image Segmentation Accuracy.** To validate the zero-shot and weakly supervised segmentation results, as well as different design components of the MedCLIP-SAM framework, we used three public datasets (three different modalities) with segmentation ground truths (segmentation of breast tumor, brain tumor, and lung), which were split for training, validation, and testing. These datasets with their divisions include:

- **Breast Tumor Ultrasound**: Breast Ultrasound Images dataset (BUSI) [\[1\]](#page-8-6) with 600 benign and malignant tumors images for training only; 65 and 98 images from the UDIAT [\[5\]](#page-8-7) dataset for validation and testing, respectively.
- **Brain Tumor MRI**: Brain Tumor dataset from [\[10\]](#page-9-16) consisting of 1,462, 400, and 400 T1-weighted MRIs for training, validation and testing respectively.
- **Lung Chest X-ray**: COVID-19 Radiography Database (COVID-QU-Ex) [\[11](#page-9-17)[,27\]](#page-9-18) with 16,280, 1,372, and 957 Chest X-ray scans (normal, lung opacity, viral pneumonia, and COVID-19 cases) for training, validation, and testing.

With these datasets, we conducted a detailed comparison of the segmentation quality for the initial labels based on CRF-processed gScoreCAM results, zeroshot pseudo-masks, and weakly supervised results on the aforementioned testing sets. As ablation studies for zero-shot segmentation, we investigated **1)** the impacts of BiomedCLIP fine-tuning and **2)** the choice of gScoreCAM vs. grad-CAM. The ablation studies were performed on the test set of each of the three aforementioned datasets. For a fair comparison, we utilized the same SAM model, target layer, text prompts, and CAM settings of the top 60 channels for all data across different variations. In all experiments, Intersection over Union (IoU), Dice Similarity Coefficient (DSC), and area under the ROC curve (AUC) were used, and paired-sample t-tests were performed to confirm the observations and trends. Here, a p-value < 0.05 indicates a statistically significant difference.

## 3 Results

### 3.1 Cross-Modal Retrieval Accuracy and gScoreCAM Vs. GradCAM

The accuracy of cross-modal retrieval (text-to-image and image-to-text) for the ROCO dataset [\[24\]](#page-9-13) is shown in Table [1](#page-5-0) across different losses for fine-tuning BiomedCLIP, with three pre-trained CLIP models as baselines. Paired McNemar statistical tests show that our DHN-NCE significantly outperformed other existing loss functions and pre-trained baseline models  $(p \le 0.01)$ . In Table [2,](#page-6-0) we present the accuracy evaluation for our MedCLIP-SAM zero-shot segmentation with different setups (Pre-trained BiomedCLIP vs. fine-tuned BiomedCLIP and gScoreCAM vs. GradCAM). The comparison demonstrated the great advantages of using gScoreCAM over GradCAM to generate bounding-box prompts for SAM  $(p \lt 1E-4)$ . Additionally, the benefit of fine-tuning BiomedCLIP with our DHN-NCE loss is further validated with improved segmentation quality across different tasks and image modalities  $(p < 0.05)$ .

<span id="page-5-0"></span>**Table 1.** Top-K cross-modal retrieval accuracy (mean*±*std) for CLIP models.

| Model           | Version               | image → text (%)                  |                                   | text → image (%)                  |                                   |
|-----------------|-----------------------|-----------------------------------|-----------------------------------|-----------------------------------|-----------------------------------|
|                 |                       | Top-1                             | Top-2                             | Top-1                             | Top-2                             |
| BiomedCLIP [33] | Pre-trained           | $81.83 		 0.20$                   | $92.79 		 0.13$                   | $81.36 		 0.48$                   | $92.27 		 0.14$                   |
|                 | InfoNCE [23]          | $84.21 		 0.35$                   | $94.47 		 0.19$                   | $85.73 		 0.19$                   | $94.99 		 0.16$                   |
|                 | <b>DCL</b> [32]       | $84.44 		 0.37$                   | $94.68 		 0.19$                   | $85.89 		 0.16$                   | $95.09 		 0.19$                   |
|                 | HN-NCE [25]           | $84.33 		 0.35$                   | $94.60 		 0.19$                   | $85.80 		 0.17$                   | $95.10 		 0.19$                   |
|                 | <b>DHN-NCE (Ours)</b> | <b><math>84.70 		 0.33</math></b> | <b><math>94.73 		 0.16</math></b> | <b><math>85.99 		 0.19</math></b> | <b><math>95.17 		 0.19</math></b> |
| CLIP [26]       | Pre-trained           | $26.68 		 0.30$                   | $41.80 		 0.19$                   | $26.17 		 0.20$                   | $41.13 		 0.20$                   |
| PMC-CLIP [18]   | Pre-trained           | $75.47 		 0.37$                   | $87.46 		 0.11$                   | $76.78 		 0.11$                   | $88.35 		 0.19$                   |

### 3.2 Zero-Shot and Weakly Supervised Segmentation

In Table [3,](#page-6-1) we present segmentation accuracy for our proposed method in zeroshot and weakly supervised settings, with fully supervised segmentation as a reference. Note that for zero-shot results, we include a comparison between initial labels generated by gScoreCAM-based saliency maps ("Saliency Maps") and pseudo-masks from SAM ("Saliency Maps + SAM"). Combining BiomedCLIP and SAM demonstrates clear advantages, notably improving segmentation quality for all metrics  $(p < 0.05)$ . Comparing zero-shot results to weakly supervised segmentation, we observe general improvements for X-ray-based lung segmentation. However, the impact on tumor segmentation in breast ultrasound and brain MRI remains unclear, with an AUC boost of  $\sim$ 2% only for breast ultrasound. While fully supervised DL models currently provide state-of-the-art accuracy for medical image segmentation, our MedCLIP-SAM zero-shot segmentation outperformed ResUNet-based full supervision for breast ultrasound and brain

MRI segmentation. Lung X-ray segmentation, however, showed superior accuracy with the fully supervised method across all metrics. Finally, to provide a qualitative assessment, exemplary segmentation results for zero-shot and weakly supervised settings are shown in Fig. [2](#page-7-0) against the original image and ground truths (GTs) across all segmentation tasks.

<span id="page-6-0"></span>**Table 2.** Comparison of zero-shot segmentation accuracy (mean*±*std) with SAM based on the pre-trained and fine-tuned BiomedCLIP models using gScoreCAM vs. Grad-CAM techniques for bounding-box generation.

| Modality                                 | Model               | CAM     | IoU(%)                                                                | DSC (%)          | $\rm AUC$ $(\%)$ |
|------------------------------------------|---------------------|---------|-----------------------------------------------------------------------|------------------|------------------|
| Breast Ultrasound Biomed CLIP gScore CAM |                     |         | $56.24 \pm 9.25$                                                      | $66.03 \pm 8.77$ | $78.59 \pm 6.38$ |
|                                          |                     | GradCAM | $18.16 \pm 9.67$                                                      | $23.99 \pm 8.24$ | $60.12 \pm 6.36$ |
|                                          | Ours                |         | $g\text{ScoreCAM}$ 57.97 $\pm$ 8.59 67.82 $\pm$ 8.26 79.31 $\pm$ 6.84 |                  |                  |
|                                          |                     | GradCAM | $20.79 \pm 9.32$                                                      | $25.65 \pm 7.81$ | $62.54 \pm 5.22$ |
| Brain MRI                                | BiomedCLIPgScoreCAM |         | $48.87 \pm 6.71$ 65.13 $\pm$ 5.98                                     |                  | $79.69 \pm 6.12$ |
|                                          |                     | GradCAM | $26.69 \pm 7.45$ $32.03 \pm 5.23$                                     |                  | $76.04 \pm 7.86$ |
|                                          | Ours                |         | $\rm gScoreCAM 50.30\pm5.94 66.72\pm5.27 81.35\pm6.33$                |                  |                  |
|                                          |                     | GradCAM | $ 27.07 \pm 7.29 33.10 \pm 6.91 78.72 \pm 7.16$                       |                  |                  |
| Lung $X$ -ray                            | BiomedCLIPgScoreCAM |         | $47.95 \pm 10.37$ 63.21 $\pm$ 11.70 77.53 $\pm$ 5.49                  |                  |                  |
|                                          |                     | GradCAM | $ 22.79 \pm 7.35 \,  35.21 \pm 10.75 \,  60.19 \pm 4.73 \,  $         |                  |                  |
|                                          | $_{\rm Ours}$       |         | $g\text{ScoreCAM}$ 49.06 $\pm$ 9.22 64.49 $\pm$ 9.09 78.54 $\pm$ 5.64 |                  |                  |
|                                          |                     | GradCAM | $26.45 \pm 8.39$ 39.75 $\pm$ 8.44 62.95 $\pm$ 5.71                    |                  |                  |

<span id="page-6-1"></span>**Table 3.** Segmentation accuracy (mean*±*std) for zero-shot and weakly supervised methods against a fully supervised baseline.

| Modality          | Model                         | IoU (%)                      | DSC (%)                     | AUC (%)                     |
|-------------------|-------------------------------|------------------------------|-----------------------------|-----------------------------|
| Breast Ultrasound | Saliency Maps                 | $40.43 	ext{ 	extpm } 8.34$  | $51.82 	ext{ 	extpm } 9.60$ | $73.77 	ext{ 	extpm } 7.54$ |
|                   | Saliency Maps + SAM           | $57.97 	ext{ 	extpm } 8.59$  | $67.82 	ext{ 	extpm } 8.26$ | $79.31 	ext{ 	extpm } 6.84$ |
|                   | Weak supervision-ResUNet [34] | $41.68 	ext{ 	extpm } 5.63$  | $58.62 	ext{ 	extpm } 5.66$ | $81.44 	ext{ 	extpm } 4.22$ |
|                   | Full supervision-ResUNet [34] | $53.15 	ext{ 	extpm } 8.36$  | $67.29 	ext{ 	extpm } 7.84$ | $84.74 	ext{ 	extpm } 5.09$ |
| Brain MRI         | Saliency Maps                 | $39.12 	ext{ 	extpm } 6.11$  | $53.06 	ext{ 	extpm } 6.34$ | $75.89 	ext{ 	extpm } 6.92$ |
|                   | Saliency Maps + SAM           | $50.30 	ext{ 	extpm } 5.94$  | $66.72 	ext{ 	extpm } 5.27$ | $81.35 	ext{ 	extpm } 6.33$ |
|                   | Weak supervision-ResUNet [34] | $42.17 	ext{ 	extpm } 8.67$  | $58.80 	ext{ 	extpm } 8.63$ | $78.25 	ext{ 	extpm } 5.32$ |
|                   | Full supervision-ResUNet [34] | $45.93 	ext{ 	extpm } 7.68$  | $62.57 	ext{ 	extpm } 7.20$ | $79.85 	ext{ 	extpm } 4.87$ |
| Lung X-ray        | Saliency Maps                 | $35.04 	ext{ 	extpm } 8.40$  | $49.54 	ext{ 	extpm } 9.18$ | $71.94 	ext{ 	extpm } 6.21$ |
|                   | Saliency Maps + SAM           | $49.06 	ext{ 	extpm } 9.22$  | $64.49 	ext{ 	extpm } 9.09$ | $78.54 	ext{ 	extpm } 5.64$ |
|                   | Weak supervision-ResUNet [34] | $76.46 	ext{ 	extpm } 12.03$ | $86.07 	ext{ 	extpm } 8.61$ | $90.76 	ext{ 	extpm } 4.39$ |
|                   | Full supervision-ResUNet [34] | $95.26 	ext{ 	extpm } 4.82$  | $97.50 	ext{ 	extpm } 2.84$ | $98.38 	ext{ 	extpm } 2.01$ |

## 4 Discussion

To the best of our knowledge, our proposed MedCLIP-SAM presents the first framework that integrates CLIP and SAM models toward universal radiological segmentation. By leveraging the latest CAM technique, gScoreCAM, which

Image /page/7/Figure/1 description: This image displays a grid of medical images, with five columns labeled (a) Image, (b) CLIP-CRF, (c) Zero-Shot, (d) WSS, and (e) GT. Each column contains three rows of images. The first row shows an ultrasound image with a circular anomaly. The second row presents an MRI scan of a brain with a tumor. The third row displays a chest X-ray with segmented lungs. The images in columns (b) through (e) appear to be segmentation masks or results corresponding to the original images in column (a).

<span id="page-7-0"></span>**Fig. 2.** Qualitative comparison of segmentation results. CLIP-CRF=CRF processed BiomedCLIP saliency map and WSS=weakly supervised segmentation.

is used in medical imaging for the first time, our method offers a unique solution that allows text-prompt-based interaction, easy adaptation to new data domains/tasks, and data-efficient model (pre-)training. One major contribution of this work lies in the newly devised DHN-NCE loss, which benefits from the synergy of DCL and HN-NCE and has been demonstrated to outperform the state-of-the-art loss functions (see Table [1\)](#page-5-0) to efficiently fine-tune the Biomed-CLIP model with a small batch size. Although we only demonstrated its application in unsupervised CLIP model fine-tuning, we will test its application in full model training in the near future. When using BiomedCLIP and gScoreCAM to obtain saliency maps, we used more simplistic keywords for segmentation tasks, such as "brain tumor". However, we also noticed that the quality of these maps could benefit from more sophisticated text prompt engineering, including detailed descriptions (e.g., shape and location of the target anatomy/pathology). This leaves an interesting application of our MedCLIP-SAM framework for interactive radiological education. From the ablation studies, both gScoreCAM and fine-tuned BiomedCLIP positively contributed to the success of our method. Our weakly supervised segmentation only improved the accuracy in X-ray-based lung segmentation. This could be explained by the complex contrast of ultrasound and the 3D nature of the brain MRI, which may be more suitable for 3D segmentation. Notably, the latest MedSAM [\[22](#page-9-4)] has demonstrated superior performance for medical applications. However, as it was fine-tuned on large amounts of public medical datasets, which include our test databases, adopting it for our framework will invalidate the "zero-shot" setting. With encouraging results from SAM in our framework, we aim to further explore the incorporation of MedSAM into MedCLIP-SAM to verify the potential performance enhancement. Finally, we only tested three segmentation tasks and image modalities in this study, and will expand our validation to a broader range of applications and image types.

## 5 Conclusion

We proposed MedCLIP-SAM, a novel framework that combines CLIP and SAM foundation models to obtain text-prompt-based universal medical image segmentation. The interactive nature of the method provides a unique venue to allow human interaction. In addition, our newly proposed DHN-NCE loss could potentially benefit broader applications. Our comprehensive experiments demonstrated excellent performance of the proposed framework, which possesses great potential for clinical adoption upon future improvements.

**Acknowledgments.** We acknowledge the support of the Natural Sciences and Engineering Research Council of Canada (NSERC).

**Disclosure of Interests.** The authors have no competing interests.

### **References**

- <span id="page-8-6"></span>1. Al-Dhabyani, W., Gomaa, M., Khaled, H., Fahmy, A.: Dataset of breast ultrasound images. Data in Brief **28**, 104863 (2020). [https://doi.org/10.1016/j.dib.2019.](https://doi.org/10.1016/j.dib.2019.104863) [104863](https://doi.org/10.1016/j.dib.2019.104863)
- <span id="page-8-2"></span>2. Arun, N., Gaw, N., Singh, P., Chang, K., Aggarwal, M., Chen, B., Hoebel, K., Gupta, S., Patel, J., Gidwani, M., Adebayo, J., Li, M.D., Kalpathy-Cramer, J.: Assessing the (un)trustworthiness of saliency maps for localizing abnormalities in medical imaging (2021)
- <span id="page-8-3"></span>3. Bae, W., Noh, J., Kim, G.: Rethinking class activation mapping for weakly supervised object localization. In: Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part XV 16. pp. 618–634. Springer (2020)
- <span id="page-8-0"></span>4. Baevski, A., Babu, A., Hsu, W.N., Auli, M.: Efficient self-supervised learning with contextualized target representations for vision, speech and language. In: International Conference on Machine Learning. pp. 1416–1429. PMLR (2023)
- <span id="page-8-7"></span>5. Byra, M., Jarosik, P., Szubert, A., Galperin, M., Ojeda-Fournier, H., Olson, L., O'Boyle, M., Comstock, C., Andre, M.: Breast mass segmentation in ultrasound with selective kernel U-Net convolutional neural network. Biomed Signal Process Control **61** (Jun 2020)
- <span id="page-8-5"></span>6. Chen, P., Li, Q., Biaz, S., Bui, T., Nguyen, A.: gscorecam: What is clip looking at? In: Proceedings of the Asian Conference on Computer Vision (ACCV) (2022)
- <span id="page-8-4"></span>7. Chen, T., Mai, Z., Li, R., lun Chao, W.: Segment anything model (sam) enhanced pseudo labels for weakly supervised semantic segmentation (2023)
- <span id="page-8-1"></span>8. Chen, T., Kornblith, S., Swersky, K., Norouzi, M., Hinton, G.E.: Big self-supervised models are strong semi-supervised learners. Advances in neural information processing systems **33**, 22243–22255 (2020)

- <span id="page-9-5"></span>9. Chen, Z., Xu, Q., Liu, X., Yuan, Y.: Un-sam: Universal prompt-free segmentation for generalized nuclei images (2024)
- <span id="page-9-16"></span>10. Cheng, J.: brain tumor dataset (4 2017). [https://doi.org/10.6084/m9.figshare.](https://doi.org/10.6084/m9.figshare.1512427.v5) [1512427.v5](https://doi.org/10.6084/m9.figshare.1512427.v5)
- <span id="page-9-17"></span>11. Chowdhury, M.E.H., Rahman, T., Khandakar, A., Mazhar, R., Kadir, M.A., Mahbub, Z.B., Islam, K.R., Khan, M.S., Iqbal, A., Emadi, N.A., Reaz, M.B.I., Islam, M.T.: Can ai help in screening viral and covid-19 pneumonia? IEEE Access **8**, 132665–132676 (2020). <https://doi.org/10.1109/ACCESS.2020.3010287>
- <span id="page-9-6"></span>12. Hu, X., Xu, X., Shi, Y.: How to efficiently adapt large segmentation model(sam) to medical images (2023)
- <span id="page-9-10"></span>13. Huang, Z., Liu, H., Zhang, H., Li, X., Liu, H., Xing, F., Laine, A., Angelini, E., Hendon, C., Gan, Y.: Push the boundary of sam: A pseudo-label correction framework for medical segmentation (2023)
- <span id="page-9-3"></span>14. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., Dollár, P., Girshick, R.: Segment anything (2023)
- <span id="page-9-12"></span>15. Kraehenbuehl, P., Koltun, V.: Parameter learning and convergent inference for dense random fields. In: Dasgupta, S., McAllester, D. (eds.) Proceedings of the 30th International Conference on Machine Learning. Proceedings of Machine Learning Research, vol. 28, pp. 513–521. PMLR, Atlanta, Georgia, USA (17–19 Jun 2013)
- <span id="page-9-7"></span>16. Li, S., Cao, J., Ye, P., Ding, Y., Tu, C., Chen, T.: Clipsam: Clip and sam collaboration for zero-shot anomaly segmentation (2024)
- <span id="page-9-8"></span>17. Li, Y., Wang, H., Duan, Y., Li, X.: Clip surgery for better explainability with enhancement in open-vocabulary tasks (2023)
- <span id="page-9-15"></span>18. Lin, W., Zhao, Z., Zhang, X., Wu, C., Zhang, Y., Wang, Y., Xie, W.: Pmc-clip: Contrastive language-image pre-training using biomedical documents (2023)
- <span id="page-9-0"></span>19. Liu, J., Lin, Z., Padhy, S., Tran, D., Bedrax Weiss, T., Lakshminarayanan, B.: Simple and principled uncertainty estimation with deterministic deep learning via distance awareness. Advances in Neural Information Processing Systems **33**, 7498– 7512 (2020)
- <span id="page-9-9"></span>20. Liu, X., Huang, X.: Weakly supervised salient object detection via bounding-box annotation and sam model. Electronic Research Archive **32**(3), 1624–1645 (2024)
- <span id="page-9-1"></span>21. Loquercio, A., Segu, M., Scaramuzza, D.: A general framework for uncertainty estimation in deep learning. IEEE Robotics and Automation Letters **5**(2), 3153– 3160 (2020)
- <span id="page-9-4"></span>22. Ma, J., Wang, B.: Segment anything in medical images. ArXiv **abs/2304.12306** (2023)
- <span id="page-9-11"></span>23. Oord, A.v.d., Li, Y., Vinyals, O.: Representation learning with contrastive predictive coding. arXiv preprint [arXiv:1807.03748](http://arxiv.org/abs/1807.03748) (2018)
- <span id="page-9-13"></span>24. Pelka, O., Koitka, S., R¨uckert, J., Nensa, F., Friedrich, C.: Radiology objects in context (roco): A multimodal image dataset. In: CVII-STENT/LABELS@MICCAI (2018)
- <span id="page-9-14"></span>25. Radenovic, F., Dubey, A., Kadian, A., Mihaylov, T., Vandenhende, S., Patel, Y., Wen, Y., Ramanathan, V., Mahajan, D.: Filtering, distillation, and hard negatives for vision-language pre-training. [arXiv:2301.02280](http://arxiv.org/abs/2301.02280) (2023)
- <span id="page-9-2"></span>26. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., Krueger, G., Sutskever, I.: Learning transferable visual models from natural language supervision (2021)
- <span id="page-9-18"></span>27. Rahman, T., Khandakar, A., Qiblawey, Y., Tahir, A., Kiranyaz, S., Abul Kashem, S.B., Islam, M.T., Al Maadeed, S., Zughaier, S.M., Khan, M.S., Chowdhury,

M.E.: Exploring the effect of image enhancement techniques on covid-19 detection using chest x-ray images. Computers in Biology and Medicine **132**, 104319 (2021). <https://doi.org/10.1016/j.compbiomed.2021.104319>

- <span id="page-10-5"></span>28. Robinson, J., Chuang, C.Y., Sra, S., Jegelka, S.: Contrastive learning with hard negative samples (2021)
- <span id="page-10-0"></span>29. Siuly, S., Zhang, Y.: Medical big data: neurological diseases diagnosis through medical data analysis. Data Science and Engineering **1**, 54–64 (2016)
- <span id="page-10-1"></span>30. Taleb, A., Lippert, C., Klein, T., Nabi, M.: Multimodal self-supervised learning for medical image analysis. In: International conference on information processing in medical imaging. pp. 661–673. Springer (2021)
- <span id="page-10-3"></span>31. Yang, X., Gong, X.: Foundation model assisted weakly supervised semantic segmentation (2023)
- <span id="page-10-4"></span>32. Yeh, C.H., Hong, C.Y., Hsu, Y.C., Liu, T.L., Chen, Y., LeCun, Y.: Decoupled contrastive learning (2022)
- <span id="page-10-2"></span>33. Zhang, S., Xu, Y., Usuyama, N., Bagga, J., Tinn, R., Preston, S., Rao, R., Wei, M., Valluri, N., Wong, C., Lungren, M.P., Naumann, T., Poon, H.: Large-scale domain-specific pretraining for biomedical vision-language processing (2023)
- <span id="page-10-6"></span>34. Zhang, Z., Liu, Q., Wang, Y.: Road extraction by deep residual u-net. IEEE Geoscience and Remote Sensing Letters **15**(5), 749-753 (May 2018). [https://doi.org/](https://doi.org/10.1109/lgrs.2018.2802944) [10.1109/lgrs.2018.2802944](https://doi.org/10.1109/lgrs.2018.2802944)

Image /page/11/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a ribbon or bookmark shape inside. The text below the icon reads "Check for updates".

## MedSynth: Leveraging Generative Model for Healthcare Data Sharing

Renuga Kanagavelu<sup>1( $\otimes$ [\)](http://orcid.org/0000-0002-2808-3093)</sup> [,](http://orcid.org/0000-0002-9702-5524) Madhav Walia<sup>2</sup> , Yuan Wang<sup>1</sup> , Huazhu Fu<sup>1</sup> , Qingsong Wei<sup>[1](http://orcid.org/0000-0001-9116-1595)</sup>  $\bullet$ [,](http://orcid.org/0000-0002-1590-2029) Yong Liu<sup>1</sup>  $\bullet$ , and Rick Siow Mong Goh<sup>1</sup>  $\bullet$ 

<sup>1</sup> Institute of High Performance Computing (IHPC), Agency for Science, Technology and Research (A\*STAR), Singapore, Singapore {renuga\_k,wang\_yuan,wei\_qingsong,liuyong,gohsm}@ihpc.a-star.edu.sg, <EMAIL> <sup>2</sup> SRM University, Amaravati, AP, India madhav\<EMAIL>

Abstract. Sharing medical datasets among healthcare organizations is essential for advancing AI-assisted disease diagnostics and enhancing patient care. Employing techniques like data de-identification and data synthesis in medical data sharing, however, comes with inherent drawbacks that may lead to privacy leakage. Therefore, there is a pressing need for mechanisms that can effectively conceal sensitive information, ensuring a secure environment for data sharing. Dataset Condensation (DC) emerges as a solution, creating a reduced-scale synthetic dataset from a larger original dataset while maintaining comparable training outcomes. This approach offers advantages in terms of privacy and communication efficiency in the context of medical data sharing. Despite these benefits, traditional condensation methods encounter challenges, particularly with high-resolution medical datasets. To address these challenges, we present *MedSynth*, a novel dataset condensation scheme designed to efficiently condense the knowledge within extensive medical datasets into a generative model. This facilitates the sharing of the generative model across hospitals without the need to disclose raw data. By combining an attention-based generator with a vision transformer (ViT), *MedSynth* creates a generative model capable of producing a concise set of representative synthetic medical images, encapsulating the features of the original dataset. This generative model can then be shared with hospitals to optimize various downstream model training tasks. Extensive experimental results across medical datasets demonstrate that *MedSynth* outperforms state-of-theart methods. Moreover, *MedSynth* successfully defends against state-ofthe-art Membership Inference Attacks (MIA), highlighting its significant potential in preserving the privacy of medical data.

Keywords: Data sharing · Privacy · Dataset Condensation · Generative Model · Membership Inference Attack · Vision Transformer

## 1 Introduction

Incorporating Artificial Intelligence (AI) into medical practice leads to advancement and breakthroughs in areas like radiology, pathology, and the overall healthcare ecosystem  $[1,2]$  $[1,2]$  $[1,2]$ . However, the data-intensive nature of AI in medicine emphasizes the need for substantial and high-quality data, highlighting the significance of collaborative medical data sharing among hospitals [\[3](#page-19-2)]. Nevertheless, efficient data sharing faces challenges due to privacy concerns. Using methods such as data synthesis [\[4](#page-19-3)] and data de-identification [\[5\]](#page-19-4) in medical data sharing, however, poses inherent challenges that could potentially result in privacy leakage. Data de-identification techniques always carry a risk of re-identification [\[6\]](#page-20-0), and there is evidence that the models used in data synthesis may leak information related to the training samples, making them prone to Membership Inference Attacks (MIA) [\[7\]](#page-20-1). Hence, it is essential to implement secure and dependable solutions for sharing healthcare data.

Dataset Condensation (DC) [\[8](#page-20-2)] involves creating a reduced-scale synthetic dataset derived from a large original dataset while still achieving comparable training outcomes. This method provides advantages in terms of privacy [\[9](#page-20-3)[,10](#page-20-4)] and data storage efficiency [\[11\]](#page-20-5). Therefore, it is beneficial to explore medical dataset condensation to address current challenges associated with medical dataset sharing. Nevertheless, when dealing with high-resolution medical datasets, traditional condensation methods [\[11](#page-20-5)[–15](#page-20-6)] encounter difficulties as they directly extract information from the original dataset into pixel space, and the feature distribution of condensed samples frequently lacks diversity.

To address the challenges, we present a novel dataset condensation (DC) approach named *MedSynth* aiming to condense the knowledge of a large medical dataset into a single generative model. With this generative model, one can generate a concise set of representative synthetic images, encapsulating the features of the original dataset. This is achieved by integrating an attention-based generator with vision transformer (ViT)-based feature refinement. By sharing only the generative model, a hospital or other medical facility can securely contribute its local data knowledge to another hospital. This facilitates enhancing data resources for downstream tasks such as training and fine-tuning, while effectively mitigating potential privacy risks.

We evaluate the effectiveness of *MedSynth* using ISIC 2019 and Alzheimer's MRI datasets and compare its efficiency with state-of-the-art methods. The key contributions of our work can be outlined as follows:

- 1. We propose *MedSynth*, a novel dataset condensation scheme that efficiently condenses the knowledge within extensive medical datasets into a generative model for secure sharing across hospitals without disclosing raw data and ensuring privacy protection.
- 2. We enhance the extraction of fine-grained information from medical images by combining an attention-based generator with a ViT for feature matching.
- 3. Extensive experimentation on medical datasets demonstrates that *MedSynth* outperforms state-of-the-art methods.

4. We conduct a membership inference attack on *MedSynth*'s generative model to confirm its resilience and successful protection of medical data privacy.

## 2 Related Work

Deep learning models need to be trained on extensive datasets for accurate disease diagnosis, emphasizing the significance of healthcare data sharing. Deidentification [\[5\]](#page-19-4) is a data-sharing technique that focuses on removing or making anonymous any personally identifiable information from medical records. However, there is consistently a potential risk of re-identification in this process [\[6](#page-20-0)]. An alternative solution to address data sharing limitations involves using Generative Adversarial Networks (GANs) [\[4](#page-19-3)]. It facilitates the generation of anonymous and potentially limitless synthetic datasets. Nonetheless, there is evidence [\[7\]](#page-20-1) that the models used in data synthesis may leak information related to the training samples. Federated learning offers an alternative that trains a joint model across many hospitals without sharing raw data [\[16,](#page-20-7)[17\]](#page-20-8). Instead, each hospital trains the model locally and shares only its updates, keeping data private. Nonetheless, it introduces a vulnerability, as model updates sent to the central server are susceptible to gradient leakage attacks [\[18\]](#page-20-9), potentially allowing the reconstruction of original data.

A recent strategy called dataset condensation has emerged to address these limitations. It can be achieved in two ways: either condensing the knowledge of an entire original dataset into a few synthetic images or into a generative model. several techniques follow the first method  $[11-15]$  $[11-15]$ . These methods begin with a small number of learnable image tensors, which are then updated by comparing the training trajectories  $[15]$  $[15]$ , embedding distributions  $[13,14]$  $[13,14]$  $[13,14]$ , or training gradients [\[11](#page-20-5)] with the original images. The second approach is utilized by [\[19,](#page-20-12)[20\]](#page-20-13). IT-GAN [\[19](#page-20-12)] aims to explore if a fixed GAN can generate informative images without changing the dataset size or reducing training costs. DiM [\[20\]](#page-20-13), on the other hand, condenses the information of the original dataset into a generator, although it has limitations when dealing with high-resolution medical images. Current medical dataset distillation methods [\[21](#page-20-14)[,22](#page-20-15)] consider learning soft labels in the synthetic set, meaning that the label is trainable for improved information compression. The researchers [\[23\]](#page-20-16) also proposed a distillation-based approach, aiming to match the parameters of the teacher networks trained on the original dataset with the student networks built on the distilled dataset. In [\[24](#page-20-17)], a generalizable dataset distillation-based federated learning (GDD-FL) framework is proposed to achieve communication-efficient federated skin lesion classification. Our method is a generative-based method that condenses the knowledge within extensive medical datasets into a generative model.

## 3 Methodology

The *MedSynth* works in two phases as shown in Fig. [1.](#page-14-0) In the first phase, an attention-based generator is trained to capture the features of the original medical dataset. In the second phase, the generator is fine-tuned using a ViT to condense the important features into the synthetic dataset. Fine-tuning is accomplished by employing logit matching to compare the features of the synthetic dataset with those of the original dataset.

Image /page/14/Figure/2 description: This diagram illustrates a two-phase process for training and fine-tuning a generative model for medical imaging. Phase I, conducted at Hospital A, involves pre-training an attention-based generator using a noise vector and labels to produce generated images and an attention mask. These generated images are then compared against synthetic images and original images by a discriminator, with a loss function guiding the training. The pre-trained generator is then shared. Phase II, also at Hospital A, fine-tunes the pre-trained generator using a Vision Transformer (ViT) with LoRA. This fine-tuning process uses synthetic images and original images, with backpropagation updating the generator based on a Logits Matching Loss. The fine-tuned generator is then shared with downstream hospitals, such as Hospital B, which can then generate synthetic data for their specific tasks.

<span id="page-14-0"></span>Fig. 1. Workflow of our framework: 1) Phase I- Pre-train the attention-based generator to create meaningful correspondences with original images. 2) Phase II- Fine-tuning the generator by applying logit matching on image embeddings of original and synthetic datasets produced by a fine-tuned ViT.

### 3.1 Phase I Pre-train the Generator

The attention-based generator used in the framework comprises an encoder, a transition layer, and a decoder. The encoder reduces the image's feature maps to a quarter of their original size. Subsequently, in the transition layer, we replaced the inner convolutional layers with six residual attention blocks (RABs). This modification effectively captures long-range dependencies and highlights latent features within the medical images. Finally, the decoder performs up-sampling to generate two outputs: a generated image, denoted as g, and an attention mask, denoted as a. The synthetic image, denoted as s, is defined as the weighted combination of the generated image  $g$  and input  $x$  by  $a$ .

$$
s = a \times x + (1 - a) \times g \tag{1}
$$

The synthetic image <sup>s</sup> is then fed to the discriminator <sup>F</sup>*a*, which comprises of 9 convolution layers and 2 fully connected layers. As described in [\[25](#page-20-18)], Wasserstein distance with gradient penalty is utilized for calculating the GAN loss, defined as:

$$
L_G = \mathbb{E}_x[F_a(s)] - \mathbb{E}_x[F_a(x)]
$$
  
+  $\lambda_{GP} (\|\nabla_z F_a(z)\|^2 - 1)^2$  (2)

$$
z = \epsilon \cdot x + (\epsilon - 1) \cdot s, \quad \epsilon \sim \mathcal{U}[0, 1] \tag{3}
$$

where  $E_x[\cdot]$  is the expected value of a function the input data and  $\epsilon$  is the same size as the input  $x$ , and has random values from 0 to 1. The Wasserstein distance is evaluated by the first two terms in  $L_G$ , while the last term accounts for the gradient penalty [\[26](#page-20-19)]. Additionally, we replaced standard batch normalization with spectral normalization in the generator, as recommended by [\[27](#page-21-0)], for improved stability during training with large batches and highly textured data like medical images. The training phase is crucial, as our experimental findings indicate a significant 25% degradation in classification performance without this training step.

### 3.2 Phase II Fine-Tune the Generator Using ViT

In the second phase, the pre-trained generator from the first phase is fine-tuned using a ViT to extract the important features into the synthetic dataset. While previous works [\[11](#page-20-5)[,13](#page-20-10),[20,](#page-20-13)[28\]](#page-21-1), have utilized ConvNet models for feature vector extraction, our study opts for the ViT architecture [\[29](#page-21-2)]. Unlike ConvNet, ViTs utilize self-attention mechanisms, allowing them to directly compare and relate features across the entire image and capture the fine-grained details in medical scans. Their inherent inductive bias towards global features enables them to learn efficiently from limited data, potentially overcoming the data bottlenecks that hinder CNN-based approaches in this domain.

ViT for feature extraction - In our approach, we initially fine-tuned the ViT on the original dataset to facilitate the extraction of feature vectors. Subsequently, the feature vectors of both the original and synthetic images, extracted from the ViT, are compared using logit matching. This process aims to condense the essential attributes of the original images into the generator, significantly enhancing the quality of synthetic images produced and serving as a key process in the dataset condensation.

However, due to data scarcity and class imbalance in medical datasets, finetuning the ViT becomes a challenge as the model starts to overfit. Our work mitigates this problem by applying Low-Rank Adaptation (LoRA) [\[30\]](#page-21-3) to perform parameter-efficient fine-tuning. LoRA significantly diminishes the number of trainable parameters, reducing the risk of overfitting when fine-tuning a large transformer-based model with limited data points. This enables the utilization of ViT with medical datasets, which are typically limited in size.

Fine-tune generator via logit matching - For a batch consisting of authentic images x and generated images  $S$ , we utilize a network  $n$  to predict the classification logits for these images. To formalize, the alignment of logits can be articulated as follows:

$$
L_n = MSE(n(S); n(x))
$$
\n(4)

Here MSE is the Mean Squared Error loss over the two feature vectors produced by the model. The Logit Matching loss  $L_n$  seeks to minimize the prediction logits, which directly impact the outcomes of subsequent classification tasks. Finally, the total can be written as:

$$
L_{total} = L_n + \lambda \cdot L_g \tag{5}
$$

This generative model can be shared securely, allowing hospitals to enhance their resources without compromising privacy.

## 4 Experimental Analysis

### 4.1 Datasets

Our evaluations were performed using the Alzheimer's Disease (AD) [\[31](#page-21-4)] and the International Skin Imaging Collaboration (ISIC) 2019 dataset [\[32](#page-21-5)]. The Alzheimer's Disease dataset is a collection of MRI images. The images are classified into four distinct categories: Mild Demented, Moderate Demented, Non-Demented, and Very Mild Demented. It has 5121 train images and 1279 test images.

The ISIC 2019 dataset comprises 20,264 train images and 5,067 test images available for the classification of dermoscopic images among nine different diagnostic categories: Melanoma, Melanocytic nevus, Basal cell carcinoma, Actinic keratosis Benign keratosis (solar lentigo/seborrheic keratosis/lichen planus-like keratosis), Dermatofibroma, Vascular lesion, Squamous cell carcinoma and none of the above.

### 4.2 Implementation Details

Experimental Setup. The training for the generator occurs in two phases (refer to Fig. [1\)](#page-14-0). In the first phase, we train the attention-based generator. The default epoch size is set to 150. The momentum for the ADAM optimizer is set to 0.9. The learning rate for *MedSynth* generator is set to 0.0001 while for the ViT in the second phase is set to 0.01. The batch size  $B$  is kept at 32 to avoid memory overflow. The implementation utilizes the PyTorch framework and is run on 4 Nvidia V100 GPUs.

Performance Assessment and Metrics. To assess the effectiveness of the *MedSynth*, we select a pre-trained ViT as the backbone for the downstream classification task. We first fine-tune this ViT using LoRa on the condensed datasets obtained with MedSynth and the baseline methods, utilizing a learning rate of 0.01, and a batch size of 32. Subsequently, we evaluate and report the resulting performance in terms of the area under the Receiver Operating Characteristic curve (AUC) and accuracy.

### 4.3 Comparisons with State-of-the-Art

In Table [1,](#page-18-0) we present a comparison of our method results with other state-of-theart distribution-based and GAN-based condensation methods. We compare our results with DiM [\[20](#page-20-13)] that uses vanilla conditional GAN. For the other methods, a ResNet18 architecture is used, whereas our work utilizes ViT-base16 with LoRA as described in [\[29,](#page-21-2)[30](#page-21-3)]. Both the architectures are pre-trained on ImageNet. The experimental findings across the ISIC 2019 and Alzheimer datasets demonstrate that *MedSynth* outperforms baseline methods, reducing the dataset size by 95% while maintaining a close approximation of 96% of the original classification performance in terms of AUC on ISIC 2019 and Alzheimer datasets.

We believe two major factors contribute to this result: 1) The presence of residual attention blocks in the generator, aiding in capturing long-range dependencies, thus facilitating effective dataset condensation into the generator. 2) ViT's self-attention mechanism enables the model to capture global interactions among all components of the input image, coupled with stronger generalization ability through pre-trained weights, further improves the quality of the generated condensed datasets.

We compare our method with non-condensation methods, specifically using deep convolutional GAN (DCGAN) [\[33](#page-21-6)]. We achieved a performance improvement of about 7% on the Alzheimer dataset and about 3% on the ISIC 2019 dataset compared with DCGAN.

We consider the condensation ratio which is the ratio of the condensed dataset size to the whole original training dataset size as a metric to measure the size of the synthetic dataset required to achieve a similar performance on the whole original dataset. In comparison to training on the entire dataset, using only 50 condensed images per class of Alzheimer's and ISIC 2019 synthetic data resulted in approximately 96% of the original classification performance. Furthermore, a dataset comprising 25,331 skin lesion (ISIC) images typically demands around 10 GB of storage. In contrast, the generative model derived from it only requires about 524 MB, reducing the communication cost as well as the storage cost by a factor of 20. This illustrates how a generative model sharing can improve efficiency in healthcare settings, enhancing data portability, sharing, and distribution without excessively burdening the backbone network and ensuring privacy protection.

### 4.4 Generalization Ability Comparison

We also assess the generalization capability of our approach across various deep networks, including ConvNet, ResNet18, and DenseNet, and compare the performance with the DiM method. The findings are shown in Fig. [2.](#page-18-1) AUC scores for various models trained on the condensed Alzheimer's dataset with images per class = 50 are compared. Our method demonstrates outstanding generalization performance across various architectures. This highlights that the condensed images produced by *MedSynth* can be utilized to train different networks, making it easier for users to select a model as required by their application.

| Method           | IPC | Dataset     |              |              | ISIC 2019 |              |              |
|------------------|-----|-------------|--------------|--------------|-----------|--------------|--------------|
|                  |     | Alzheimer's | Cond Ratio%  | AUC Score    | Accuracy  | Cond Ratio%  | AUC Score    |
| DM [14]          | 50  | 0.98        | 65.90        | 79.34        | 0.27      | 77.90        | 41.24        |
|                  | 100 | 1.95        | 71.73        | 74.07        | 0.49      | 80.65        | 44.46        |
| IT-GAN           | 50  | 0.98        | 78.84        | 79.54        | 0.27      | 77.24        | 77.62        |
|                  | 100 | 1.95        | 82.11        | 83.48        | 0.49      | 80.53        | 81.03        |
| DiM              | 50  | 0.98        | 89.35        | 89.46        | 0.27      | 81.87        | 82.33        |
|                  | 100 | 1.95        | 90.28        | 91.13        | 0.49      | 82.94        | 83.25        |
| MedSynth         | 50  | 0.98        | <b>94.90</b> | <b>95.58</b> | 0.27      | <b>83.12</b> | <b>85.54</b> |
|                  | 100 | 1.95        | <b>96.27</b> | <b>97.11</b> | 0.49      | <b>84.93</b> | <b>87.22</b> |
| DCGAN            | -   |             | 90.07        | 91.18        | -         | 82.57        | 84.92        |
| Original Dataset | -   |             | 97.19        | 98.83        | -         | 86.19        | 88.95        |

<span id="page-18-0"></span>Table 1. Performance Evaluation with varying images per class (IPC) on ISIC 2019 and Alzheimer datasets.

Image /page/18/Figure/3 description: A bar chart displays the AUC score for three different models: Densenet, Resnet18, and Convnet. The chart compares two methods, DiM (represented by red bars) and MedSynth (represented by blue bars). For Densenet, DiM has an AUC score of approximately 78, while MedSynth has an AUC score of approximately 85. For Resnet18, DiM has an AUC score of approximately 79.5, and MedSynth has an AUC score of approximately 83. For Convnet, DiM has an AUC score of approximately 82, and MedSynth has an AUC score of approximately 83. The y-axis is labeled "AUC\_Score" and ranges from 50 to 85, while the x-axis is labeled "Model".

<span id="page-18-1"></span>Fig. 2. Generalization ability comparison to DiM.

Image /page/18/Figure/5 description: A bar chart compares the accuracy of three methods (Black-Box, White-Box, and Baseline) across two datasets (ISIC2019 and Alzheimer's). For the ISIC2019 dataset, the Black-Box method achieves an accuracy of approximately 0.142, the White-Box method achieves approximately 0.157, and the Baseline method achieves approximately 0.165. For the Alzheimer's dataset, the Black-Box method achieves an accuracy of approximately 0.117, the White-Box method achieves approximately 0.125, and the Baseline method achieves approximately 0.137. The y-axis represents accuracy, ranging from 0.00 to 0.16.

<span id="page-18-2"></span>Fig. 3. Membership Inference Attack - Accuracy on the Generator

### 4.5 Membership Inference Attack Analysis

We evaluate MedSynth's resiliency against the MIA attack. We generate the white-box and black-box attacks based on the threat model proposed in [\[34\]](#page-21-7). For both, the attacker training set consists of a random 10% of the original dataset (ISIC/Alzheimer) with synthetic fake samples as non-members. In the white box attack, knowing the target GAN architecture, the attacker inputs the training set to target GAN's discriminator, extracts and sorts the prediction probabilities and uses the highest probabilities to predict the training set members. In the blackbox attack, without knowing the target GAN architecture, the attacker first trains a local GAN using the target GAN samples and carry out the steps typical of a white-box attack. The attack's accuracy is determined by the percentage of correctly identified images from the training set. Lower attack accuracy, below random guessing, indicates increased model security against MIA attacks. We compare the accuracy of both scenarios to a baseline corresponding to random guesses made by a third party on the membership of samples in the dataset. As shown in Fig. [3,](#page-18-2) we see the accuracy for both scenarios lies under the baseline, proving the generator is safe against MIA. This happens because the generator is distilled with knowledge about a condensed version of the dataset, which makes it difficult to reverse-engineer individual samples from the original dataset.

## 5 Conclusion

In this work, we proposed a novel generative model-based condensation technique to improve the high-resolution medical dataset's condensation process using an attention-based generator combined with ViT-based feature refinement. Incorporating DC with specialized GAN architectures for medical images and attentionbased foundation models is more effective in condensing medical images. We evaluated the effectiveness of our proposed approach on several healthcare datasets and achieved an AUC score similar to the original dataset. We also looked at how these generative models are safe from membership inference attacks, making them safe to use in the medical domain. By sharing the generative models instead of the entire raw images, it lowers the amount of storage and bandwidth needed for data storage and transfer. Our evaluation shows that the condensed medical datasets/generative models obtained with our method could be more securely and efficiently shared among healthcare facilities.

Acknowledgments. This Research is supported by the RIE2025 Industry Alignment Fund - Industry Collaboration Project (IAF-ICP) (Award No: I2301E0020), administered by A\*STAR. This work is supported by A\*STAR Central Research Fund "A Secure and Privacy Preserving AI Platform for Digital Health".

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

### References

- <span id="page-19-0"></span>1. Y. Chen et al. "Generative Adversarial Networks in Medical Image augmentation: A review". In: *Comput Biol Med* 144 (2022), p. 105382.
- <span id="page-19-1"></span>2. Jieneng Chen et al. *3D TransUNet: Advancing Medical Image Segmentation through Vision Transformers*. 2023. arXiv: 2310.07781 [cs.CV].
- <span id="page-19-2"></span>3. Mana Azarm-Daigle, Craig Kuziemsky, and Liam Peyton. "A Review of Cross Organizational Healthcare Data Sharing". In: *Procedia Computer Science* 63 (Dec. 2015), pp. 425-432.
- <span id="page-19-3"></span>4. Aldren Gonzales, Guruprabha Guruswamy, and Scott Smith. "Synthetic data in health care: A narrative review". In: *PLOS Digital Health* 2 (Jan. 2023), e0000082.
- <span id="page-19-4"></span>5. Khaled El Emam, Sam Rodgers, and Bradley Malin. "Anonymising and sharing individual patient data". In: *BMJ* 350 (2015).

- <span id="page-20-0"></span>6. Kerstin N. Vokinger, Daniel J. Stekhoven, and Michael Krauthammer. "Lost in Anonymization-A Data Anonymization Reference Classification Merging Legal and Technical Considerations". In: *The Journal of Law, Medicine & Ethics* 48.1 (2020), pp. 228-231.
- <span id="page-20-1"></span>7. Z. Zhang, C. Yan, and B. A. Malin. "Membership inference attacks against synthetic health data". In: *J Biomed Inform* 125 (2022), p. 103977.
- <span id="page-20-2"></span>8. Tongzhou Wang et al. "Dataset Distillation". In: *CoRR* abs/1811.10959 (2018). arXiv: 1811.10959. URL:  $\frac{\text{http://arxiv.org/abs/1811.10959.}}{\text{http://arxiv.org/abs/1811.10959.}}$  $\frac{\text{http://arxiv.org/abs/1811.10959.}}{\text{http://arxiv.org/abs/1811.10959.}}$  $\frac{\text{http://arxiv.org/abs/1811.10959.}}{\text{http://arxiv.org/abs/1811.10959.}}$
- <span id="page-20-3"></span>9. Tian Dong, Bo Zhao, and Lingjuan Lyu. *Privacy for Free: How does Dataset Condensation Help Privacy?* 2022. arXiv: 2206.00240 [cs.CR].
- <span id="page-20-4"></span>10. Dingfan Chen, Raouf Kerkouche, and Mario Fritz. *Private Set Generation with Discriminative Information*. 2022. arXiv: 2211.04446 [cs.CR].
- <span id="page-20-5"></span>11. Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. *Dataset Condensation with Gradient Matching*. 2021. arXiv: 2006.05929 [cs.CV].
- 12. Bo Zhao and Hakan Bilen. "Dataset condensation with differentiable siamese augmentation". In: *International Conference on Machine Learning*. PMLR. 2021, pp. 12674-12685.
- <span id="page-20-10"></span>13. Kai Wang et al. CAFE: *Learning to Condense Dataset by Aligning Features*. 2022. arXiv: 2203.01531 [cs.CV].
- <span id="page-20-11"></span>14. Bo Zhao and Hakan Bilen. *Dataset Condensation with Distribution Matching*. 2022. arXiv: 2110.04181 [cs.LG].
- <span id="page-20-6"></span>15. George Cazenavette et al. *Dataset Distillation by Matching Training Trajectories*. 2022. arXiv: 2203.11932 [cs.CV].
- <span id="page-20-7"></span>16. H. Brendan McMahan et al. *Communication-Efficient Learning of Deep Networks from Decentralized Data*. 2023. arXiv: 1602.05629 [cs.LG].
- <span id="page-20-8"></span>17. Yuan Wang et al. "An Aggregation-Free Federated Learning for Tackling Data Heterogeneity". In: *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*. 2024, pp. 26233- 26242.
- <span id="page-20-9"></span>18. Yangsibo Huang et al. *Evaluating Gradient Inversion Attacks and Defenses in Federated Learning*. 2021. arXiv: 2112.00059 [cs.CR].
- <span id="page-20-12"></span>19. Bo Zhao and Hakan Bilen. *Synthesizing Informative Training Samples with GAN*. 2022. arXiv: 2204.07513 [cs.LG].
- <span id="page-20-13"></span>20. Kai Wang et al. *DiM: Distilling Dataset into Generative Model*. 2023. arXiv: 2303.04707 [cs.CV].
- <span id="page-20-14"></span>21. Guang Li et al. "Soft-Label Anonymous Gastric X-Ray Image Distillation". In: 2020 IEEE International Conference on Image Processing (ICIP) (2020), pp. 305-309.
- <span id="page-20-15"></span>22. Guang Li et al. "Compressed gastric image generation based on soft-label dataset distillation for medical data sharing". In: *Computer Methods and Programs in Biomedicine* 227 (2022), p. 107189. issn: 0169-2607.
- <span id="page-20-16"></span>23. Guang Li et al. "Dataset Distillation for Medical Dataset Sharing". In: *AAAI-23 Workshop on Representation Learning for Responsible Human- Centric AI* (2023).
- <span id="page-20-17"></span>24. Yuchen Tian et al. "Communication-Efficient Federated Skin Lesion Classification with Generalizable Dataset Distillation". In: *MICCAI 2023* Workshops. Vancouver, BC, Canada: Springer-Verlag, 2023. isbn: 978-3-031-47400-2.
- <span id="page-20-18"></span>25. Euijin Jung, Miguel Luna, and Sang Hyun Park. "Conditional generative adversarial network for predicting 3d medical images affected by alzheimer's diseases". In: *Predictive Intelligence in Medicine: Third International Workshop, PRIME 2020, Held in Conjunction with MICCAI 2020*. Springer. 2020, pp. 79-90.
- <span id="page-20-19"></span>26. Ishaan Gulrajani et al. *Improved Training of Wasserstein GANs*. 2017. arXiv: 1704.00028 [cs.LG].

- <span id="page-21-0"></span>27. Takeru Miyato et al. Spectral *Normalization for Generative Adversarial Networks*. 2018. arXiv: 1802.05957 [cs.LG].
- <span id="page-21-1"></span>28. Bo Zhao and Hakan Bilen. *Dataset Condensation with Differentiable Siamese Augmentation*. 2021. arXiv: 2102.08259 [cs.LG].
- <span id="page-21-2"></span>29. Alexey Dosovitskiy et al. *An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale*. 2021. arXiv: 2010.11929 [cs.CV].
- <span id="page-21-3"></span>30. Edward J. Hu et al. *LoRA: Low-Rank Adaptation of Large Language Models*. 2021. arXiv: 2106.09685 [cs.CL].
- <span id="page-21-4"></span>31. Sarvesh Dubey. *Alzheimer's Dataset (4 class of Images)*. 2020. url: [https://www.](https://www.kaggle.com/datasets/tourist55/alzheimers-dataset-4-class-of-images) [kaggle.com/datasets/tourist55/alzheimers-dataset-4-class-of-images.](https://www.kaggle.com/datasets/tourist55/alzheimers-dataset-4-class-of-images)
- <span id="page-21-5"></span>32. *Skin Lesion Images for Melanoma Classification*. [https://www.kaggle.com/](https://www.kaggle.com/datasets/andrewmvd/isic-2019) [datasets/andrewmvd/isic-2019.](https://www.kaggle.com/datasets/andrewmvd/isic-2019)
- <span id="page-21-6"></span>33. Alec Radford, Luke Metz, and Soumith Chintala. *Unsupervised Representation Learning with Deep Convolutional Generative Adversarial Networks*. 2016. arXiv: 1511.06434 [cs.LG].
- <span id="page-21-7"></span>34. Jamie Hayes et al. "LOGAN: Membership Inference Attacks Against Generative Models". In: *Proceedings on Privacy Enhancing Technologies* 2019 (2017), pp. 133 -152.

Image /page/22/Picture/0 description: A square button with rounded corners contains a circular icon with a ribbon shape inside and the text "Check for updates" below it.

# One Registration is Worth Two Segmentations

Shiqi Huang $^{1,2(\boxtimes)},$ Tingfa Xu<sup>2</sup>, Ziyi Shen $^1,$  Shaheer Ullah Saeed $^1,$  Wen Yan $^1,$ Dean Barratt<sup>1</sup>, and Yipeng  $Hu<sup>1</sup>$ 

> <sup>1</sup> University College London, London, UK <EMAIL>, <EMAIL> <sup>2</sup> Beijing Institute of Technology, Beijing, China ciom\<EMAIL>

Abstract. The goal of image registration is to establish spatial correspondence between two or more images, traditionally through dense displacement fields (DDFs) or parametric transformations (e.g., rigid, affine, and splines). Rethinking the existing paradigms of achieving alignment via spatial transformations, we uncover an alternative but more intuitive correspondence representation: a set of corresponding regions-of-interest (ROI) pairs, which we demonstrate to have sufficient representational capability as other correspondence representation methods. Further, it is neither necessary nor sufficient for these ROIs to hold specific anatomical or semantic significance. In turn, we formulate image registration as searching for the same set of corresponding ROIs from both moving and fixed images - in other words, two multi-class segmentation tasks on a pair of images. For a general-purpose and practical implementation, we integrate the segment anything model (SAM) into our proposed algorithms, resulting in a SAM-enabled registration (SAMReg) that does not require any training data, gradient-based fine-tuning or engineered prompts. We experimentally show that the proposed SAMReg is capable of segmenting and matching multiple ROI pairs, which establish sufficiently accurate correspondences, in three clinical applications of registering prostate MR, cardiac MR and abdominal CT images. Based on metrics including Dice and target registration errors on anatomical structures, the proposed registration outperforms both intensity-based iterative algorithms and DDF-predicting learning-based networks, even yielding competitive performance with weakly-supervised registration which requires fully-segmented training data.

**Keywords:** Image Registration  $\cdot$  Correspondence Representation  $\cdot$  Segment Anything Model (SAM)

## 1 Introduction

Registering a pair of moving and fixed images is one of the basic tasks in many medical image computing applications  $[8,10,22]$  $[8,10,22]$  $[8,10,22]$  $[8,10,22]$ , among which the registrationestimated spatial transformation can warp the moving image to be *aligned* with the fixed image. Common transformation functions may be parameterized by translation, rotation, scaling, spline coefficients, spatially-sampled control point coordinates, and, most generally, displacement vectors at all voxel locations a dense displacement field (DDF). Like their *classical* interactive counterparts, recent learning-based registration algorithms have also been formulated to predict these transformation parameters [\[9](#page-31-2)].

Warping images is only one of many possible use cases of the output transformation that represents spatial correspondence. For example, registration-enabled atlas-based label fusion [\[30\]](#page-32-1), morphological analysis on predefined anatomical structures [\[5\]](#page-31-3), surgical navigation [\[17](#page-31-4)] and target tracking [\[25](#page-32-2)], require accurate correspondence to be estimated on only one or a small number of regions-ofinterest (ROIs). In fact, few clinical applications require ubiquitous correspondence and, arguably, the registration becomes an *ill-posed* problem when estimating  $N \times 3$  displacements at all voxel locations, given  $N \times 2$  intensity values from two images, N being the average number of voxels in one image.

Representing correspondence with the above-discussed transformation functions is sufficient for most downstream tasks, but their necessity in representing correspondence (therefore efficiency) is application-dependent. This motivated this study to propose a different type of correspondence representation, using a set of corresponding ROIs. This is considered flexible to allow correspondence to be represented with only a few paired ROI masks, yet without losing generality (e.g. more and smaller ROI pairs) if denser local correspondence is required.

For designing a learning-based registration algorithm to estimate corresponding ROI pairs, it is intuitive to use predefined ROI types and their segmented labels for training, akin to a weakly-supervised algorithm [\[13](#page-31-5)], with or without intensity-based unsupervised loss [\[2](#page-30-0)]. However, requiring ROI-labelled training data limits the general applicability of the algorithm.

In this work, we propose a practical and general-purpose registration algorithm to overcome the limitation due to training data. Specifically, the registration process is streamlined using Segment Anything Model (SAM) [\[16](#page-31-6)] to automatically generate prototypes from the multiple segmented ROIs on both images, and match them in pairs based on similarity. The presented experiments show that neither the definition nor the quantity of these SAM-segmented ROIs needs to be prescribed (or kept the same), for estimating sufficiently useful correspondences, in three different clinical image registration tasks.

In summary, our main contributions are: 1)We pioneer a new correspondence representation for image registration using intuitive multiple paired-ROIs. 2) We introduce the general-purpose SAMReg by segmenting corresponding ROIs from both images, without any labels or fine-tuning. To our knowledge, it is the first application trial of SAM in image registration. 3) Extensive experiments show that our method outperforms the common-used registration tool and unsupervised registration, and is competitive with weakly-supervised registration that requires additional labeled training images, across three clinical applications.

Image /page/24/Figure/1 description: The image displays three methods for establishing correspondence between two spaces, labeled Space X and Space Y. Method (a), Rigid Transformation-based Correspondence, shows a matrix A, a translation vector t, and the resulting transformation f(x) in Space Y. Method (b), DDF-based Correspondence, illustrates the process using an interpolation function (I) and a warping function (W) to generate a Dense Displacement Field (DDF) that maps points from Space X to Space Y. Method (c), ROI-based Correspondence, demonstrates establishing correspondences based on Regions of Interest (ROIs) within Space X and mapping them to Space Y using rotation matrices R1, R2, and R3. A legend at the bottom clarifies the symbols used: a circle with a cross for matrix multiplication, a circle with an 'I' for interpolation function, and a circle with a 'W' for warping function.

<span id="page-24-0"></span>Fig. 1. The correspondence representation diagram.

<span id="page-24-2"></span>

## 2 ROI-Based Correspondence Representation

This section considers spatial correspondence to be a mapping  $f : X \to Y$  from spatial locations  $X$  in a moving image coordinate system to locations  $Y$  in the fixed image coordinate system. For example in 3D,  $X \in \mathbb{R}^3$  and  $Y \in \mathbb{R}^3$  are random vectors containing Euclidean coordinates, in x-, y- and z-directions.

Existing methods for representing spatial correspondence,  $f$ , fall into two main types: transformation functions of spatial locations or paired samples. The first type includes parametric functions like rigid, affine, and spline-based trans-formations (e.g. thin-plate-splines [\[23\]](#page-32-3)) that map input vectors  $x$  to coordinate vectors y through  $y = f(x) : X \to Y$ . An example (shown in Fig. [1a](#page-24-0)) is the rigid transformation  $f^{rigid}(\mathbf{x}) = A\mathbf{x} + \mathbf{t}$ , where A is a rotation matrix and  $\mathbf{t}$ is a translation vector. The second type represents correspondence through  $N$ pairs of corresponding locations  $(\mathbf{x}_n, \mathbf{y}_n)$ , where  $n = 1, ..., N$ . For instance, DDF (shown in Fig. [1b](#page-24-0)) is represented by  $\{(\mathbf{x}_n, \mathbf{x}_n + \mathbf{d}_n)\}\$  where  $\mathbf{d}_n$  is a set of vectors defined on all voxel locations  $x_n$ . Interpolation is applied to estimate y for x at locations beyond the defined voxels  $x_n$ . In this context, we formulate a more intuitive ROI-based correspondence representation as an alternative:

<span id="page-24-1"></span>Theorem 1. *Denoting x and y as spatial locations at respective moving- and fixed image spaces (i.e. coordinate systems), it is sufficient for* K *pairs of regions* $of\text{-}interest (ROIs), denoted as \{ (R_k^x, R_s^y) \}_{k=1}^K$ , to indicate any spatial correspon*dence between the two image spaces, if* K *is sufficiently large, where*  $R_k^x = {\mathbf{x}_l}_{l=1}^{L_k^x}$ and  $R_k^y = \{y_i\}_{i=1}^{L_k^y}$  are two sets of  $L_k^x$  and  $L_k^y$  spatial locations that represent the<br>same (corresponding) ROIs in the moving, and fired image spaces, respectively *same (corresponding) ROIs, in the moving- and fixed image spaces, respectively.*

This is intuitive in an extreme case, in which a large number  $(K = N)$  of single-voxel (i.e.  $L_k^x = L_k^y = 1$ ) ROIs are used, the ROI pairs can be sampled at N voxels to an equivalent DDF representation  $f(R^x - x, R^y - x)$ N voxels to an equivalent DDF representation  $\{(R_k^x = \mathbf{x}_k, R_k^y = \mathbf{y}_k)\}_{k=1}^N$ .<br>Therefore, an informal proof for Theorem 1 can be understood by

Therefore, an informal proof for Theorem [1](#page-24-1) can be understood by iterating the following process until sufficiency is reached: When a point-to-point correspondence  $y^* = f(x^*)$  that has NOT been represented by current ROI pairs  $\{(R_k^x, R_k^y)\}_{k=1}^K$ , one can always sample an additional ROI pair  $\{(R_{K+1}^x = \mathbf{x}^*, R_{K+1}^y = \mathbf{y}^*)\}$ , such that the resulting  $(K+1)$  ROI pairs  $\{(R_k^x, R_k^y)\}_{k=1}^{K+1} =$ <br> $\{(R_k^x, R_k^y)\}_{k=1}^K$  $\{(R_k^x, R_k^y)\}_{k=1}^K \cup \{(R_{K+1}^x, R_{K+1}^y)\}\$  suffice.<br>Further properties of the proposed B

Further, properties of the proposed ROI-based correspondence are summarized: 1) individual ROIs can be practically depicted as binary pair masks; 2) paired ROIs can represent both local and global correspondence; 3) Overlapping ROIs facilitate one-to-many correspondence; 4) if dense-level correspondence is required, the correspondence at any spatial location can be inferred. We discuss specific algorithms in Sect. [4.2;](#page-26-0) 5) if dense-level correspondence is not required, omitting smoothness regularization (e.g., rigidity or bending energy) during estimation enhances registration efficiency with ROI-based representation.

<span id="page-25-0"></span>

## 3 One Registration Versus Two Segmentations

Given a single-image  $I^x$ , a multi-class segmentation is to estimate the joint probability of a random vector  $\mathbf{C}^x = [C_1^x, ..., C_{K^x}^x]^\top \in \mathbb{R}^{K^x}$ , representing  $K^x$ -class probabilities over N voxel locations,  $\prod_{n=1}^{N} p_n(\mathbf{C}^x | I^x)$ . The segmented ROI set  $\{\hat{R}_k^x\}_{k=1}^{K^x}$  can be computed by binarising the estimated class probability vectors  ${\{\hat{\mathbf{c}}_n^x\}}_{n=1}^N$ , where  $\hat{\mathbf{c}}_n^x \sim \mathbf{C}^x$ . For a second image  $I^y$ , ROIs  $\hat{R}_k^y$  are segmented by estimating  $K^y$ -class probabilities  $\hat{\mathbf{c}}^y$  where  $\hat{\mathbf{c}}^y$  is the class probabilities for  $K^y$ estimating  $K^y$ -class probabilities  $\hat{\mathbf{c}}_n^y$ , where  $\hat{\mathbf{c}}_n^y$  is the class probabilities for  $K^y$ <br>classes. In this section, we consider the following two cases for registering  $I^x$  and classes. In this section, we consider the following two cases for registering  $I^x$  and  $I<sup>y</sup>$ , by estimating ROI-based correspondence described in Sect. [2.](#page-24-2)

Segmenting the Same ROIs from Two Images. In this case, the segmentation algorithm (e.g. one or two neural networks) is capable of segmenting the same classes of ROIs, i.e.  $\mathbf{C} = \mathbf{C}^x = \mathbf{C}^y$  and  $K = K^x = K^y$ , from both images. Therefore, the obtained ROIs pairs can directly represent the correspondence, described in Sect. [2,](#page-24-2)  $\{(R_k^x = \hat{R}_k^x, R_k^y = \hat{R}_k^y)\}_{k=1}^K$ , that is to estimate the mono-<br>tonic joint probability given two images  $n$  (C| $I^x$   $I^y$ )  $\propto n$  (C| $I^x$ ),  $n$  (C| $I^y$ ) tonic joint probability given two images  $p_n(\mathbf{C}|I^x,I^y) \propto p_n(\mathbf{C}|I^x) \cdot p_n(\mathbf{C}|I^y)$ , subject to normalization constants. This approach usually requires predefined ROI types and segmentation networks trained using these ROI-labelled data. Interestingly, these segmented training data are exactly the same data required for training weakly-supervised registration algorithms [\[12](#page-31-7)[,13](#page-31-5)].

Matching the Segmented ROI Candidates. Assume an alternative "unsupervised" segmentation can be used to obtain two sets of candidate ROIs,  $\{\hat{R}_k^x\}_{k=1}^{K^x}$  and  $\{\hat{R}_k^y\}_{k=1}^{K^y}$ , from the fixed and moving- images, respectively. Search for a subset of common ROI classes,  $\mathbf{C} = [C_1, ..., C_K]^\top$ , where  $\{C_k\}_{k=1}^K$ <br> $\{C_x\}^K$   $\cap$   $\{C_y\}^K$  Thus the corresponding ROI pairs  $\{R_x, R_y\}^K$ for a subset of common ROI classes,  $C = [C_1, ..., C_K]$ , where  $\{C_k^k\}_{k=1}^K -$ <br> $\{C_k^x\}_{k=1}^{K^x} \cap \{C_k^y\}_{k=1}^{K^y}$ . Thus, the corresponding ROI pairs  $\{(R_k^x, R_k^y)\}_{k=1}^K$  derived<br>from the estimated probability vectors  $\hat$ from the estimated probability vectors  $\hat{\mathbf{c}}_n \sim \mathbf{C}$ , that is the joint probability between the two conditionally independent "candidate probabilities",  $p_n(\mathbf{C}|I^x,I^y) = p_n(\mathbf{C}^x,\mathbf{C}^y|I^x,I^y) = p_n(\mathbf{C}^x|I^x,I^y) \cdot p_n(\mathbf{C}^y|I^x,I^y) \propto p_n(\mathbf{C}^x|I^x)$  $p_n(\mathbf{C}^y|I^y)$ . In the following Sect. [4,](#page-26-1) we propose a specific algorithm using this strategy.

<span id="page-26-1"></span>

## 4 Segment Everything and Match: A Training-Free Registration Algorithm

### 4.1 Preliminary: SAM Architecture

SAM [\[16\]](#page-31-6) consists of an image encoder, a prompt encoder and a mask decoder, is adept at image segmentation without task-specific training, and has recently been adapted for medical imaging. The adaptation includes training on large medical datasets [\[6](#page-31-8),[18\]](#page-31-9), thus establishing a foundation model, and fine-tuning for targeted medical datasets using, for example, additional adaptation layers [\[11,](#page-31-10) [28,](#page-32-4)[29](#page-32-5)]. Despite the advancements, as highlighted by these authors, SAM may yields limited sensitivity to medical images compared to natural images [\[14,](#page-31-11) [19\]](#page-31-12), resulting in mixed results in direct applications for specific anatomy and pathology segmentation without further training.

It is interesting to argue that requiring known anatomical significance in segmentation may not limit medical image registration. Although correspondence between medical images may be application-dependent to some extent [\[7](#page-31-13)], the main objective is to find *similar* regions across images, a task that suits SAM's strong feature and pixel classification capabilities. For example, SAM can effectively segment ROIs with clear edges such as the iliac internal, which are not typical targets but useful for establishing local correspondence. By deploying SAM to segment extensive foreground areas, we aim to discover corresponding ROIs that capture diverse anatomical structures and areas that may lack precise anatomical definitions (*e.g.*, common cavities between structures).

<span id="page-26-0"></span>

### 4.2 The SAMReg Algorithm

Since the multiple ROIs generated by SAM segmentation lack explicit one-to-one correspondence, our strategy involves embedding these ROIs before matching them in feature space, with the proposed pipeline illustrated in Fig. [2.](#page-27-0)

ROI Embedding: Leveraging the SAM, we independently encode the input images  $I^x$  and  $I^y$  into features,  $F^x$  and  $F^y$  respectively. By setting the SAM decoder to the *everything* mode with a simple outlier-removing filter (further details in Sect. [5\)](#page-28-0), we exploit its generality to produce a comprehensive array of segmentation masks for each image, obtaining mask sets  $\{M_k^x\}_{k=1}^{K^x}$  and  $\{M_k^y\}_{k=1}^{K^y}$ , representing ROIs  ${R_k^x}_{k=1}^K$  and  ${R_k^y}_{k=1}^K$ , respectively.

For each binary mask, we derive moving prototypes  $\{p_k^x\}_{k=1}^{K^x}$  by elementwise multiplying the mask with their corresponding feature map, element being indexed by  $n: p_k^x =$  $\frac{\sum_n M_k^{x, res}(n) \cdot F^x(n)}{\sum_n M_k^{x, res}(n)}$ , where res indicates resizing  $M_k^x$  to the size of  $F^x$  as  $M_k^{x,res}$ . This also applies on the fixed prototypes  $\{p_k^y\}_{k=1}^{K^y}$ .

**ROI Matching:** Assume a similarity matrix  $S \in \mathbb{R}^{K^x \times K^y}$  to measure the cosine similarity between the moving- and fixed prototypes:  $S(i, j) = ||\frac{p_i^x \cdot p_j^y}{||p_i^x|| \cdot ||p_j^y||}||$ , where  $\|\cdot\|$  normalises each element  $S(i, j) \in [0, 1], i \in [1, K^x]$  and  $j \in [1, K^y]$ .

Image /page/27/Figure/1 description: This is a diagram illustrating a medical image registration process. The process begins with a 'Fixed Image' and a 'Moving Image', both processed by an 'Encoder (SAM)' and 'Decoder (SAM)' to extract features. These features are then used to generate 'Fixed Segmented Masks' and 'Moving Segmented Masks' with corresponding candidate ROIs. These masks and ROIs are further processed to create 'Prototypes'. The prototypes are then used to compute a 'Similarity Matrix'. Finally, the process results in 'Fixed Paired ROIs' and 'Moving Paired ROIs', indicating the successful registration of corresponding regions between the two images.

<span id="page-27-0"></span>Fig. 2. The pipeline of the proposed SAM-Reg method.

A set of index pairs P then can be identified:  $P = \arg \max_{i,j} (S(i,j)),$  $i,j$ 

*s.t.*  $S(i, j) > \epsilon \ \forall \ (i, j)$ , corresponding to pairings with maximum similarities. A further constraint is added to ensure each i i is chosen at most once if overlanfurther constraint is added to ensure each  $i, j$  is chosen at most once, if overlapping ROIs segmented from SAM are filtered.  $\epsilon$  is the similarity threshold and the set length  $K = |P| \leq min(K^x, K^y)$ . Using the selected index pairs P, we construct two new sets of masks,  $\{M_k^{x,cor}\}_{k=1}^K$  and  $\{M_k^{y,cor}\}_{k=1}^K$ :

$$
(M_k^x, M_k^y)^{cor} = \{ (M_i^x, M_j^y) | (i, j) \in P \}, k = 1, 2, ..., K.
$$
 (1)

This leads to the final paired ROIs indicating the correspondence between  $I^x$  and  $I<sup>y</sup>$ . This matching mechanism can be applied on either 2D images or 3D images, in the latter of which, SAM-segmented 2D slices can be stacked to provide the candidate masks,  $\{M_k^x\}_{k=1}^{K^x}$  and  $\{M_k^y\}_{k=1}^{K^y}$  in 3D, before computing prototypes.

Optional ROI-to-Dense Correspondence: Further, as discussed in Sect. [3,](#page-25-0) we explore the capability of converting estimated ROI-based correspondence to its dense counterpart DDF, useful for applications such as full-image alignment. In this section, we describe a general method that iteratively refines the DDF, to optimize an objective function  $\mathcal L$  that combines a region-specific alignment measure  $\mathcal{L}_{roi}$  with a regularization term  $\mathcal{L}_{ddf}$  to ensure smooth interpolation:

$$
\mathcal{L}_{\Theta}(\{(M_k^x, M_k^y)\}) = \sum_{k=1}^K \mathcal{L}_{roi}(M_k^x, \mathcal{T}(M_k^y, \Theta)) + \lambda \mathcal{L}_{ddf}(\Theta)
$$
(2)

where  $\Theta$  represents parameters of the transformation function  $\mathcal T$  and  $\lambda$  is a regularization parameter that balances the alignment accuracy with the deformation smoothness. This may itself be considered a multi-ROI alignment algorithm but with known ROI correspondence. In this work, we use an equally-weighted MSE and Dice as  $\mathcal{L}_{roi}$  and a L<sup>2</sup>-norm of DDF gradient as  $\mathcal{L}_{ddf}$ .

| Method          | MR-Prostate [1]  |                 | MR-Cardiac [3]   |                 | CT-Abdomen [15]  |                 |
|-----------------|------------------|-----------------|------------------|-----------------|------------------|-----------------|
|                 | Dice             | TRE             | Dice             | TRE             | Dice             | TRE             |
| NiftyReg [21]   | $7.68 
es 3.98$  | $4.67 
es 3.48$ | $9.93 
es 2.21$  | $3.29 
es 2.89$ | $6.81 
es 3.02$  | $5.86 
es 3.64$ |
| VoxelMorph* [2] | $56.84 
es 3.41$ | $3.68 
es 1.92$ | $60.10 
es 3.95$ | $3.03 
es 2.41$ | $58.34 
es 3.72$ | $4.13 
es 2.23$ |
| LabelReg* [13]  | $77.32 
es 3.56$ | $2.72 
es 1.23$ | $78.97 
es 2.42$ | $1.73 
es 1.34$ | $74.53 
es 3.43$ | $2.58 
es 1.18$ |
| SAMReg(Ours)    | $75.67 
es 3.81$ | $2.09 
es 1.35$ | $80.28 
es 3.67$ | $1.43 
es 1.13$ | $71.74 
es 3.72$ | $2.48 
es 1.01$ |

<span id="page-28-1"></span>Table 1. Comparison with the-state-of-the-art medical image registration methods, where the ∗ indicates methods specifically trained on the dataset.

<span id="page-28-2"></span>Table 2. Ablation study of segmentation performance: SAM versus leading datasetspecific segmentation models. *Lab-ROI* represents the dataset's original labeled ROIs, while *Pse-ROI* refers to pseudo random ROIs identified by SAM.

| Dataset      | Seg.Model    | Lab-ROI            |                   | Pse-ROI            |                   |
|--------------|--------------|--------------------|-------------------|--------------------|-------------------|
|              |              | Dice               | TRE               | Dice               | TRE               |
| Prostate [1] | SAM [16]     | 75.67 \$\pm\$ 3.81 | 2.72 \$\pm\$ 1.23 | 97.73 \$\pm\$ 3.15 | 0.82 \$\pm\$ 0.23 |
|              | Vnet [20]    | 80.35 \$\pm\$ 3.14 | 2.13 \$\pm\$ 1.24 | 43.56 \$\pm\$ 3.54 | 5.32 \$\pm\$ 2.51 |
| Cardiac [3]  | SAM [16]     | 80.28 \$\pm\$ 3.67 | 1.73 \$\pm\$ 1.34 | 98.87 \$\pm\$ 3.61 | 1.62 \$\pm\$ 0.67 |
|              | FCT [27]     | 83.54 \$\pm\$ 3.46 | 1.31 \$\pm\$ 1.25 | 31.35 \$\pm\$ 3.74 | 7.24 \$\pm\$ 2.52 |
| Abdomen [15] | SAM [16]     | 71.74 \$\pm\$ 3.72 | 2.58 \$\pm\$ 1.18 | 98.24 \$\pm\$ 3.17 | 1.45 \$\pm\$ 0.75 |
|              | MedNeXt [24] | 78.26 \$\pm\$ 3.34 | 1.84 \$\pm\$ 1.54 | 45.73 \$\pm\$ 3.73 | 5.37 \$\pm\$ 2.12 |

Image /page/28/Figure/5 description: This image displays a comparison of segmentation results from two different methods, SAM and a trained SegModel, across two registration types: inter-subject and intra-subject. For each method and registration type, there are two main columns. The first column shows the 'Moving paired ROI' and 'Fixed paired ROI' which are MRI images with segmented regions highlighted in different colors. The second column displays the segmentation masks for 'Moving lab.', 'Moved lab.', and 'Fixed lab.', presented as binary images. The inter-subject registration section shows prostate MRI scans with segmented regions like the prostate, seminal vesicles, and rectum. The intra-subject registration section shows cardiac MRI scans with segmented regions of the heart chambers and major vessels. The trained SegModel appears to produce more accurate and consistent segmentations compared to SAM, especially in the inter-subject registration of the prostate.

<span id="page-28-3"></span>Fig. 3. The qualitative comparisons of SAM and fully-supervised segmentation results, including ROI-label(top) and pseudo-label(bottom)wrapping outcomes.

<span id="page-28-0"></span>

## 5 Experiments and Results

Datasets and Implementation Details: Our method is evaluated on MR-Prostate [\[1](#page-30-1)], MR-Cardiac [\[3\]](#page-30-2), and CT-Abdomen [\[15\]](#page-31-14) datasets, highlighting diverse anatomical areas and modalities. We apply inter-subject registration for Prostate and Abdomen datasets to align images to a standard reference, and intra-subject registration for Cardiac images, aligning time-variant image pairs.

| PairNum   Dice           |                                    | TRE |
|--------------------------|------------------------------------|-----|
|                          | $69.59 \pm 3.51 \pm 3.65 \pm 1.53$ |     |
| $\overline{2}$           | $74.19 \pm 3.41 \pm 2.63 \pm 1.76$ |     |
| 3                        | $76.00 \pm 3.23 \pm 2.02 \pm 1.35$ |     |
| $\overline{\mathcal{A}}$ | $75.83 \pm 3.43 \pm 2.42 \pm 1.65$ |     |
| AH                       | $74.67 \pm 3.31 \pm 2.72 \pm 1.53$ |     |

<span id="page-29-0"></span>Table 3. Ablation of paired ROI quantity on MR-prostate dataset.

| Threshold $\epsilon$ | Dice             | TRE             |
|----------------------|------------------|-----------------|
| 0.2                  | 72.76 $\pm$ 3.63 | 3.65 $\pm$ 1.38 |

0.4  $\sqrt{74.43 \pm 3.402.46 \pm 1.56}$ 0.6  $75.78 \pm 3.24$   $2.20 \pm 1.43$ 0.8  $\boxed{70.43 + 3.183.13 + 1.53}$ 

<span id="page-29-1"></span>Table 4. Ablation of minimum similarity

Performance is measured using the Dice score for overlap and target registration error (TRE) for landmark alignment, where TRE is defined as the root-meansquare  $(RMS)$  distance. For non-rigid registration  $[10,26]$  $[10,26]$  $[10,26]$ , we assess accuracy by the mean transformation accuracy of critical anatomical structures [\[13](#page-31-5)], with TRE calculated from the centroids of the relevant ROIs. In configuring the SAM parameters, beyond the standard settings, we adjusted the pred-iou-thresh and stability-score-thresh to 0.90. Spatial size of input images are normalized to  $256 \times 256$  and the ROI filtering strategy for output masks is based on area and overlapping ratio, setting a minimum of 200 and a maximum of 7000 for the ROI sizes and a maximum ratio of 0.8 empirically. Spatial size of  $M_k^{x, res}$  is  $32 \times 32$ ,<br>and the similarity threshold  $\epsilon$  is set to 0.8. Our network is implemented with and the similarity threshold  $\epsilon$  is set to 0.8. Our network is implemented with pytorch and MONAI [\[4\]](#page-30-3). Code demo has been released at: [https://github.com/](https://github.com/sqhuang0103/SAMReg.git) [sqhuang0103/SAMReg.git.](https://github.com/sqhuang0103/SAMReg.git)

Comparing SAMReg with SOTA Methods: In our evaluation, we fairly compare SAMReg with NiftyReg [\[21](#page-31-15)], a non-learning tool, and also contrast it, less equitably, with tailored methods like VoxelMorph [\[2\]](#page-30-0) and LabelReg [\[13\]](#page-31-5). VoxelMorph and LabelReg are fine-tuned on the research datasets, employing unsupervised and weakly-supervised learning respectively, with LabelReg also depending on anatomical annotations. Despite this, SAMReg demonstrates outstanding performance without dataset-specific training, particularly in the intrasubject cardiac dataset due to consistent patterns, as illustrated in Table [1.](#page-28-1) For the inter-subject datasets of MR-Prostate and MR-Cardiac, SAMReg slightly lags behind in Dice scores but showcases competitive TRE performance, highlighting its interesting local alignment ability over shape conformity, perhaps due to the involvement of multiple ROIs. Furthermore, the ability to achieve such results without dataset-specific training underscores the robust generalization capacity of SAMReg, suggesting its wide applicability in medical tasks, and reducing the time and resources needed for clinical deployment.

### Ablative Study:

*ROI Segmentation Comparison:* Table [2](#page-28-2) and Fig. [3](#page-28-3) present ROI segmentation and the registration alignment performance comparisons between SAM and top segmentation models. Despite an unfair advantage for benchmark models designed and trained for groundtruth annotations (Label-ROI), SAMReg demonstrates robust performance and effectiveness, without ground-truth reliance, even outperforming these models with SAM-generated pseudo ROIs (Pse-ROI).

*Optimal Number* K *of ROI Pairs:* Table [3](#page-29-0) shows that using up to three ROI pairs optimizes registration, improving Dice and TRE scores. This is an interestingly small number, suggesting future work for even furthering registration performance, by more effectively utilizing smaller paired ROIs.

**Threshold**  $\epsilon$  for **ROI** Correspondence: As illustrated in Table [4,](#page-29-1) a 0.6 similarity threshold enhances Dice scores and reduces TRE, while a higher threshold (0.8) reduces ROI selection and performance, underscoring the importance of a balanced threshold for optimal registration results.

## 6 Conclusion

In this study, We introduce a novel ROI-based correspondence representation. With this representation, the image registration is reformalized as two multi-class segmentation tasks, with a proposed, general-purpose and practical implementation, SAMReg. The comprehensive experiments show competitive registration performance that promises a new research direction in image registration.

Acknowledgments. This work was supported by the International Alliance for Cancer Early Detection, a partnership between Cancer Research UK [C28070/A30912; C73666/A31378], Canary Center at Stanford University, the University of Cambridge, OHSU Knight Cancer Institute, University College London and the University of Manchester.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

### References

- <span id="page-30-1"></span>1. Ahmed, H.U., Bosaily, A.E.S., Brown, L.C., Gabe, R., Kaplan, R., Parmar, M.K., Collaco-Moraes, Y., Ward, K., Hindley, R.G., Freeman, A., et al.: Diagnostic accuracy of multi-parametric mri and trus biopsy in prostate cancer (promis): a paired validating confirmatory study. The Lancet 389(10071), 815–822 (2017)
- <span id="page-30-0"></span>2. Balakrishnan, G., Zhao, A., Sabuncu, M.R., Guttag, J., Dalca, A.V.: Voxelmorph: a learning framework for deformable medical image registration. IEEE transactions on medical imaging 38(8), 1788–1800 (2019)
- <span id="page-30-2"></span>3. Bernard, O., Lalande, A., Zotti, C., Cervenansky, F., Yang, X., Heng, P.A., Cetin, I., Lekadir, K., Camara, O., Ballester, M.A.G., et al.: Deep learning techniques for automatic mri cardiac multi-structures segmentation and diagnosis: is the problem solved? IEEE transactions on medical imaging 37(11), 2514–2525 (2018)
- <span id="page-30-3"></span>4. Cardoso, M.J., Li, W., Brown, R., Ma, N., Kerfoot, E., Wang, Y., Murrey, B., Myronenko, A., Zhao, C., Yang, D., et al.: Monai: An open-source framework for deep learning in healthcare. [arXiv:2211.02701](http://arxiv.org/abs/2211.02701) (2022)

- <span id="page-31-3"></span>5. Cerveri, P., Belfatto, A., Manzotti, A.: Pair-wise vs group-wise registration in statistical shape model construction: representation of physiological and pathological variability of bony surface morphology. Computer Methods in Biomechanics and Biomedical Engineering 22(7), 772–787 (2019)
- <span id="page-31-8"></span>6. Cheng, J., Ye, J., Deng, Z., Chen, J., Li, T., Wang, H., Su, Y., Huang, Z., Chen, J., Jiang, L., et al.: Sam-med2d. [arXiv:2308.16184](http://arxiv.org/abs/2308.16184) (2023)
- <span id="page-31-13"></span>7. Crum, W.R., Griffin, L.D., Hill, D.L., Hawkes, D.J.: Zen and the art of medical image registration: correspondence, homology, and quality. NeuroImage  $20(3)$ , 1425–1437 (2003)
- <span id="page-31-0"></span>8. De Vos, B.D., Berendsen, F.F., Viergever, M.A., Sokooti, H., Staring, M., Išgum, I.: A deep learning framework for unsupervised affine and deformable image registration. Medical image analysis 52, 128–143 (2019)
- <span id="page-31-2"></span>9. DeTone, D., Malisiewicz, T., Rabinovich, A.: Deep image homography estimation. [arXiv:1606.03798](http://arxiv.org/abs/1606.03798) (2016)
- <span id="page-31-1"></span>10. Eppenhof, K.A., Lafarge, M.W., Moeskops, P., Veta, M., Pluim, J.P.: Deformable image registration using convolutional neural networks. In: Medical Imaging 2018: Image Processing. vol. 10574, pp. 192–197. SPIE (2018)
- <span id="page-31-10"></span>11. Gong, S., Zhong, Y., Ma, W., Li, J., Wang, Z., Zhang, J., Heng, P.A., Dou, Q.: 3dsam-adapter: Holistic adaptation of sam from 2d to 3d for promptable medical image segmentation. [arXiv:2306.13465](http://arxiv.org/abs/2306.13465) (2023)
- <span id="page-31-7"></span>12. Hu, Y., Gibson, E., Barratt, D.C., Emberton, M., Noble, J.A., Vercauteren, T.: Conditional segmentation in lieu of image registration. In: MICCAI 2019. pp. 401– 409. Springer (2019)
- <span id="page-31-5"></span>13. Hu, Y., Modat, M., Gibson, E., Li, W., Ghavami, N., Bonmati, E., Wang, G., Bandula, S., Moore, C.M., Emberton, M., et al.: Weakly-supervised convolutional neural networks for multimodal image registration. Medical image analysis 49, 1–13 (2018)
- <span id="page-31-11"></span>14. Huang, Y., Yang, X., Liu, L., Zhou, H., Chang, A., Zhou, X., Chen, R., Yu, J., Chen, J., Chen, C., et al.: Segment anything model for medical images? Medical Image Analysis 92, 103061 (2024)
- <span id="page-31-14"></span>15. Ji, Y., Bai, H., Ge, C., Yang, J., Zhu, Y., Zhang, R., Li, Z., Zhanng, L., Ma, W., Wan, X., et al.: Amos: A large-scale abdominal multi-organ benchmark for versatile medical image segmentation. In: NeurIPS 2022. pp. 36722–36732 (2022)
- <span id="page-31-6"></span>16. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., et al.: Segment anything. [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-31-4"></span>17. Luebbers, H.T., Messmer, P., Obwegeser, J.A., Zwahlen, R.A., Kikinis, R., Graetz, K.W., Matthews, F.: Comparison of different registration methods for surgical navigation in cranio-maxillofacial surgery. Journal of Cranio-Maxillofacial Surgery 36(2), 109–116 (2008)
- <span id="page-31-9"></span>18. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nature Communications 15(1), 654 (2024)
- <span id="page-31-12"></span>19. Mazurowski, M.A., Dong, H., Gu, H., Yang, J., Konz, N., Zhang, Y.: Segment anything model for medical image analysis: an experimental study. Medical Image Analysis 89, 102918 (2023)
- <span id="page-31-16"></span>20. Milletari, F., Navab, N., Ahmadi, S.A.: V-net: Fully convolutional neural networks for volumetric medical image segmentation. In: 3DV 2016. pp. 565–571. Ieee (2016)
- <span id="page-31-15"></span>21. Modat, M., Cash, D.M., Daga, P., Winston, G.P., Duncan, J.S., Ourselin, S.: Global image registration using a symmetric block-matching approach. Journal of medical imaging 1(2), 024003–024003 (2014)

- <span id="page-32-0"></span>22. Rohé, M.M., Datar, M., Heimann, T., Sermesant, M., Pennec, X.: Svf-net: learning deformable image registration using shape matching. In: MICCAI 2017. pp. 266– 274. Springer (2017)
- <span id="page-32-3"></span>23. Rohr, K., Stiehl, H.S., Sprengel, R., Buzug, T.M., Weese, J., Kuhn, M.: Landmarkbased elastic registration using approximating thin-plate splines. IEEE Transactions on medical imaging  $20(6)$ , 526–534 (2001)
- <span id="page-32-7"></span>24. Roy, S., Koehler, G., Ulrich, C., Baumgartner, M., Petersen, J., Isensee, F., Jaeger, P.F., Maier-Hein, K.H.: Mednext: transformer-driven scaling of convnets for medical image segmentation. In: MICCAI 2023. pp. 405–415. Springer (2023)
- <span id="page-32-2"></span>25. Seregni, M., Paganelli, C., Summers, P., Bellomi, M., Baroni, G., Riboldi, M.: A hybrid image registration and matching framework for real-time motion tracking in mri-guided radiotherapy. IEEE Transactions on Biomedical Engineering 65(1), 131–139 (2017)
- <span id="page-32-8"></span>26. Sotiras, A., Davatzikos, C., Paragios, N.: Deformable medical image registration: A survey. IEEE transactions on medical imaging 32(7), 1153–1190 (2013)
- <span id="page-32-6"></span>27. Tragakis, A., Kaul, C., Murray-Smith, R., Husmeier, D.: The fully convolutional transformer for medical image segmentation. In: WCACV 2023. pp. 3660–3669 (2023)
- <span id="page-32-4"></span>28. Wang, H., Guo, S., Ye, J., Deng, Z., Cheng, J., Li, T., Chen, J., Su, Y., Huang, Z., Shen, Y., Fu, B., Zhang, S., He, J., Qiao, Y.: Sam-med3d. [arXiv:2310.15161](http://arxiv.org/abs/2310.15161) (2023)
- <span id="page-32-5"></span>29. Wu, J., Fu, R., Fang, H., Liu, Y., Wang, Z., Xu, Y., Jin, Y., Arbel, T.: Medical sam adapter: Adapting segment anything model for medical image segmentation. [arXiv:2304.12620](http://arxiv.org/abs/2304.12620) (2023)
- <span id="page-32-1"></span>30. Yang, H., Sun, J., Li, H., Wang, L., Xu, Z.: Neural multi-atlas label fusion: Application to cardiac mr images. Medical image analysis 49, 60–75 (2018)

Image /page/33/Picture/0 description: A square button with rounded corners contains a circular icon and text. The icon is a circle with a curved line segment and a flag-like shape inside. The text below the icon reads "Check for updates".

# PEPSI: Pathology-Enhanced Pulse-Sequence-Invariant Representations for Brain MRI

Peirong Liu<sup>1,2( $\boxtimes$ )</sup>, Oula Puonti<sup>1,2</sup>, Annabel Sorby-Adams<sup>1,2</sup>, W. Taylor Kimberly<sup>1,2</sup>, and Juan E. Iglesias<sup>1,2,3,4</sup>

> <sup>1</sup> Harvard Medical School, Boston, USA <EMAIL>

<sup>2</sup> Massachusetts General Hospital, Boston, USA

<sup>3</sup> University College London, London, UK

<sup>4</sup> Massachusetts Institute of Technology, Cambridge, USA

Abstract. Remarkable progress has been made by data-driven machinelearning methods in the analysis of MRI scans. However, most existing MRI analysis approaches are crafted for specific MR pulse sequences (MR contrasts) and usually require nearly isotropic acquisitions. This limits their applicability to the diverse, real-world clinical data, where scans commonly exhibit variations in appearances due to being obtained with varying sequence parameters, resolutions, and orientations – especially in the presence of pathology. In this paper, we propose PEPSI, the first pathology-enhanced, and pulse-sequence-invariant feature representation learning model for brain MRI. PEPSI is trained entirely on synthetic images with a novel pathology encoding strategy, and enables cotraining across datasets with diverse pathologies and missing modalities. Despite variations in pathology appearances across different MR pulse sequences or the quality of acquired images (e.g., resolution, orientation, artifacts, etc.), PEPSI produces a high-resolution image of reference contrast (MP-RAGE) that captures anatomy, along with an image specifically highlighting the pathology. Our experiments demonstrate PEPSI's remarkable capability for image synthesis compared with the state-ofthe-art, contrast-agnostic synthesis models, as it accurately reconstructs anatomical structures while differentiating between pathology and normal tissue. We further illustrate the efficiency and effectiveness of PEPSI features for downstream pathology segmentation on five public datasets covering white matter hyperintensities and stroke lesions. Code is available at [https://github.com/peirong26/PEPSI.](https://github.com/peirong26/PEPSI)

## 1 Introduction

Recent learning-based methods have enabled considerably more rapid and accurate image analysis of brain magnetic resonance imaging (MRI) [\[15\]](#page-42-0), which pro-

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_63.](https://doi.org/10.1007/978-3-031-72390-2_63)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 676–686, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_63)\_63

vides precise and adjustable soft-tissue contrast via noninvasive, in vivo imaging of the human brain [\[4\]](#page-42-1). Nevertheless, the majority of current MRI analysis approaches are tailored to particular MR pulse sequences (MR contrasts), and often rely on nearly isotropic acquisitions. Consequently, sharp declines in performance frequently occur when voxel size and anisotropy increase, or when applied to a contrast that is different from the one used during training [\[28\]](#page-43-0). This compromises model generalizability and leads to extra data collection and training efforts when dealing with new datasets. Leveraging synthetic data, recent contrast-agnostic models [\[2](#page-42-2)[,13](#page-42-3)[,15](#page-42-0),[16,](#page-42-4)[18,](#page-42-5)[20](#page-43-1)[,22\]](#page-43-2) demonstrate remarkable performance and largely broaden the scope of model applicability to the diverse clinical acquisition protocols. However, these models are confined to the specific tasks they were trained for and cannot be readily adapted to other tasks.

Meanwhile, task-agnostic foundation models  $(1,3)$  in general computer vision and natural language processing have experienced notable success, driven by the fast growth of large-scale datasets [\[5](#page-42-7),[8,](#page-42-8)[17\]](#page-42-9). Nonetheless, the development of foundation models in medical imaging has been hindered by the lack of large-scale datasets (in many domains), variations in acquisition protocols and processing pipelines, and privacy constraints. MONAI [\[7\]](#page-42-10) provides pre-trained models for diverse tasks, but they generally are highly task-oriented and contrast-sensitive. Zhou et al. [\[30\]](#page-43-3) proposed a medical foundation model, which is specifically designed for the detection of eye and systemic health conditions from retinal scans, yet this model is limited to the modalities of color fundus photography and optical coherence tomography. AI generalist systems [\[24](#page-43-4)[,26](#page-43-5),[27\]](#page-43-6) have shown superiority in biomedical tasks (e.g., visual question answering, image classification, radiology report generation and summarizing), but mostly within the vision-language context. CIFL [\[9](#page-42-11)] was designed for task-agnostic feature representations, yet it has only been demonstrated in 2D, and exclusively relies on contrastive learning, insufficient in surpassing task-specific models in downstream applications [\[21](#page-43-7)]. Recently, Liu et al. [\[21\]](#page-43-7) proposed Brain-ID, which extracts contrast-agnostic features for brain MRI, and achieves state-of-the-art performance in various fundamental medical imaging tasks including reconstruction, segmentation, and super-resolution. However, Brain-ID exclusively focuses on healthy-appearing anatomy and lacks the capacity to model pathologies (Fig. [4\)](#page-39-0).

In this paper, we introduce PEPSI, the first pulse-sequence-invariant feature representation learning approach specifically designed to emphasize pathology. PEPSI is trained on synthetic data encoded with pathology, and can be directly applied to real images featuring various types of pathology.

- 1) We introduce a data generator that synthesizes images incorporating augmented pathologies across *any* combination of deformation, pulse sequence, resolution, orientation, artifacts, etc., thus circumventing the limitations of real data, which are often confined to the acquired pulse sequence (Fig. [1\)](#page-35-0).
- 2) We design a feature learning framework guided by MP-RAGE and FLAIR scans, which balances anatomy and pathology. Furthermore, PEPSI bridges the gaps of pathologies across datasets via our proposed implicit pathology

Image /page/35/Figure/1 description: This figure illustrates a process involving medical image analysis. On the left, an image labeled 'L' shows a brain scan with different regions color-coded. Below it, an image labeled 'P (Eq. (1))' displays an anomaly probability map, with a color bar indicating probabilities from 0 to 1. An arrow labeled 'Deformation' points to a grid representing a deformation field 'φ'. This deformation is applied to both the brain scan 'L' and the anomaly map 'P', resulting in 'φ ∘ L' and 'φ ∘ P' respectively. Following this, a section labeled 'Pathology-enhanced Sampling (Eq. (2))' shows three probability distribution curves (blue, green, and red) with arrows pointing down. The process then leads to a dashed box labeled 'w/ random Corruption + Resampling', which contains a series of brain scan images labeled S1, S2, ..., SN-1, SN. These images are categorized into two groups: 'T1w-resembled' (S1, S2) and 'T2w/FLAIR-resembled' (SN-1, SN), suggesting the generation of augmented or corrupted image samples.

<span id="page-35-0"></span>Fig. 1. PEPSI's on-the-fly generator uses 3D anatomy labels (*L*) and anomaly probabilities (*P*) to generate training data with diverse deformations, contrasts, and corruptions – enhanced by varying intensity profiles in pathological regions (Sect. [2.1\)](#page-35-1).

supervision, and enables co-training across datasets with different pathology types and potentially missing modalities (Sect. [2.2\)](#page-36-0).

3) We conduct comprehensive evaluations on image synthesis and pathology segmentation. PEPSI exhibits *(i)* a remarkable capability to synthesize images with missing modalities while simultaneously capturing various pathologies (Fig. [4\)](#page-39-0); *(ii)* superior efficiency and effectiveness on downstream pathology segmentation across five public datasets, covering modalities of T1w and FLAIR, with white matter hyperintensity (WMH) and stroke lesions (Table [2\)](#page-40-0).

## 2 Approach

Sourcing large-scale datasets with high-quality and diverse contrasts for brain MRI is challenging. Recent works [\[2](#page-42-2),[14,](#page-42-12)[15,](#page-42-0)[21](#page-43-7)] proposed to utilize anatomy labels to simulate data, yet their data generators are *solely* based on anatomy and lack prior information on potential pathologies. Instead, we synthesize data that *emphasizes* pathologies (Sect. [2.1\)](#page-35-1), to encourage the model to *distinguish* between normal and abnormal regions in the features (Sect. [2.2\)](#page-36-0), thereby facilitating the transmission of valuable information for downstream pathology segmentations.

<span id="page-35-1"></span>

### 2.1 Generating Pathology-Encoded Training Data

PEPSI leverages neuroanatomical labels and pathology segmentation to generate contrast-diverse data while *simultaneously* emphasizing pathology.

Anomaly Probabilities: We construct a proxy for anomaly maps (P) using *a priori* knowledge of an image's expected appearance conditioned on its MR contrast, where pathology is typically darker in T1w and brighter in T2w/FLAIR:

$$
P(x) = \begin{cases} 0, & x \notin \Omega_P \\ 1 - \frac{(I(x) - I_{\min})}{(I_{\max} - I_{\min})}, & x \in \Omega_P, \ I \in \{\text{T1w}\} \\ \frac{(I(x) - I_{\min})}{(I_{\max} - I_{\min})}, & x \in \Omega_P, \ I \in \{\text{T2w}, \text{FLAIR}\} \end{cases} (1)
$$

where  $\Omega_P$  refers to the pathological region,  $I_{\text{max}}(I_{\text{min}})$  is the regional maximum (minimum) image intensities:  $I_{\text{max}} = \max_{x \in \Omega_P} I(x)$ ,  $I_{\text{min}} = \min_{x \in \Omega_P} I(x)$ .

Image /page/36/Figure/1 description: This figure illustrates a deep learning framework for medical image reconstruction. On the left, a series of input MRI scans, denoted as S\_N down to S\_1, are fed into a 'backbone' network (F). This backbone processes the input data, generating feature maps. The middle section shows these feature maps, labeled F\_N down to F\_1, where each F\_i consists of two images: a grayscale MRI scan and a corresponding binary mask. A legend indicates that a checkmark signifies modality availability and an 'x' signifies unavailability. The right side of the figure shows the output of the framework. For each input feature F\_i, there are three outputs: a reconstructed T1-weighted image (S~\_i^T1), a reconstructed T1-weighted image (I~\_i^T1), and a reconstructed T2/FLAIR image (I~\_i^T2/FLAIR). These reconstructed images are then compared to ground truth images (S^T1, I^T1, I^T2/FLAIR) through specific equations (Eq. (4) and Eq. (3)) and processing modules (P\_T1 and P\_T2/FLAIR), with the results indicated by checkmarks or crosses, signifying successful or unsuccessful reconstruction.

<span id="page-36-1"></span>Fig. 2. PEPSI's pathology-enhanced, contrast-agnostic training overview (Sect. [2.2\)](#page-36-0).

Pathology-Encoded Contrast: To generate images with complex brain structures, we leverage anatomy labels following [\[21](#page-43-7)]. As shown in Fig. [1,](#page-35-0) a random deformation field  $(\phi)$  is first generated, comprising linear and non-linear trans-formations [\[16,](#page-42-4)[21\]](#page-43-7). After the anatomy labels  $(L)$  and anomaly probabilities  $(P)$ are deformed by  $\phi$ , we generate the pathology-encoded images via two steps:

*(i)* "Anomaly-free" image  $(S_0)$ : We begin with randomly sampling intensities on the brain anatomy labels, where the regional intensities are generated by independently sampling a Gaussian distribution for each labeled region [\[21\]](#page-43-7).

*(ii) Pathology enhancement*: We incorporate the anomaly probabilities into the "anomaly-free" image  $(S_0)$  to produce a pathology-encoded image  $(S)$  – again, using *a priori* knowledge of the modality. This is conditioned on the direction of intensities from white to gray matter in  $S_0$ :  $S(x) = S_0(x) + \Delta S(x) * p(x)$ ,

s.t. 
$$
\Delta S(x) \sim \begin{cases} \{0\}, & x \notin \Omega_{\phi \circ P} \\ \mathcal{N}(-\mu_{\rm w}/2, \mu_{\rm w}/2), & x \in \Omega_{\phi \circ P}, \mu_{\rm w} > \mu_{\rm g} \\ \mathcal{N}(\mu_{\rm w}/2, \mu_{\rm w}/2), & x \in \Omega_{\phi \circ P}, \mu_{\rm w} \leq \mu_{\rm g} \end{cases}
$$
 (2)

 $\mu_{\rm w}$  ( $\mu_{\rm g}$ ) is the mean value of white (gray) matter intensities in  $S_0$ . A higher  $\mu_{\rm w}$ resembles T1w, where pathologies appear darker; A lower  $\mu_{\rm w}$  resembles T2w or FLAIR, where pathologies are typically brighter. (See the dashed box in Fig. [1.](#page-35-0))

The pathology-encoded images  $(S)$  further undergo a corruption pipeline [\[15\]](#page-42-0) (Fig. [1\)](#page-35-0), which includes a model of partial voluming [\[2\]](#page-42-2), and introduces various resolutions, noises and scanning artifacts commonly found in clinical protocols.

<span id="page-36-0"></span>

### 2.2 Representing Across Contrasts, Pathologies, Datasets

Here we present PEPSI's training framework, which learns to emphasize anomalies and facilitates co-training across datasets with different types of pathology.

Input: We adopt the "mild-to-severe" intra-subject sampling strategy in [\[21\]](#page-43-7), which maximizes intra-subject variance to enhance feature robustness. Samples generated within a mini-batch are from the same subject, yet exhibit

Image /page/37/Picture/1 description: This image shows a diagram illustrating the input and output of a medical imaging analysis process. On the left, text reads "ISLES [12] FLAIR" followed by a gray arrow pointing to an MRI scan of a human brain. The brain scan is axial and shows multiple areas highlighted with red circles, indicating regions of interest. On the right, a gray arrow points from the brain scan to a black image with white segmented areas and red "X" marks within red circles. Text next to this image reads "Gold-standard pathology map (Stroke annotations only)". This suggests the process involves analyzing FLAIR MRI scans to identify stroke annotations, with the right image representing the ground truth or output map.

Fig. 3. Left: an axial slice of a FLAIR from ISLES [\[12](#page-42-13)], WMH marked in red. Right: its gold-standard lesion segmentation, which only includes stroke lesions (no WMH).

<span id="page-37-0"></span>varying contrasts, corruptions, and pathology intensities, enriching the learning space (Fig. [2\)](#page-36-1).

Dual Guidance Balancing Anatomy and Pathology: We aim to obtain robust, contrast-agnostic feature representations that capture the distinctive anatomy of each subject while effectively distinguishing between pathology and normal tissue. MP-RAGE is the standard T1w MR contrast to delineate anatomical structures in research, but it is insufficient to differentiate many types of anomalies from normal tissue. FLAIR MRI, on the other hand, highlights areas of T2 prolongation as bright while suppressing cerebrospinal fluid (CSF), providing clear visibility of lesions in proximity to CSF  $[11]$  – but provides worse contrast than MP-RAGE in normal anatomy. PEPSI resorts to both MP-RAGE and FLAIR as learning targets, to concurrently capture normal anatomy and pathology. (Fig. [4](#page-39-0) compares the performance of dual-guidance and single-guidance.)

As shown in Fig. [2,](#page-36-1) the input mini-batch of intra-subject pathology-encoded samples,  $\{S_1, \ldots, S_N\}$ , are mapped to their corresponding feature space by a backbone  $(\mathcal{F}), \{\mathbf{F}_1, \ldots, \mathbf{F}_N\}$ . Two linear activation layers are followed to synthesize the anatomy and pathology images. The synthesis loss is obtained by collecting the reconstruction errors of all samples in the current mini-batch:

<span id="page-37-1"></span>
$$
\mathcal{L}_{\text{synth}} = \alpha \mathcal{L}_{I_{\text{T1}}}} + \beta \mathcal{L}_{I_{\text{T2}/\text{FLAIR}}}} \qquad (\alpha, \beta \in \{0, 1\}, \lambda \in R^{+}) \qquad (3)
$$
$$
= \alpha \sum_{i}^{N} |\widetilde{I_{i}^{\text{T1}}}} - I^{\text{T1}}| + \lambda |\nabla \widetilde{I_{i}^{\text{T1}}}} - \nabla I^{\text{T1}}|
$$
$$
+ \beta \sum_{i}^{N} |I_{i}^{\text{T2}/\text{FLAIR}}} - I^{\text{T2}/\text{FLAIR}}| + \lambda |\nabla \widetilde{I_{i}^{\text{T2}/\text{FLAIR}}}} - \nabla I^{\text{T2}/\text{FLAIR}}|,
$$

where I  $(\tilde{I})$  is the ground truth (predicted) image,  $\alpha$  ( $\beta$ ) denote the availability of ground truth  $I^{T1}$  ( $I^{T2/FLAIR}$ ),  $\lambda$  is the weight of reconstruction gradient loss [\[21](#page-43-7)] (Fig. [3\)](#page-37-0).

Implicit Pathology Supervision for Multi-pathology/Dataset Training: Co-training across datasets broadens the model's exposure to various types of pathology, but also presents inherent challenges – notably, difficulty to accurately synthesize *abnormal* regions in the *missing* modality, particularly for smaller datasets (e.g., "PEPSI (No-Seg)" in Fig. [4\)](#page-39-0). Direct supervision on pathology segmentations forces the model to pay more attention to anomalies, but could potentially result in conflicts during co-training due to the *non-exhaustive* pathology annotations across datasets (e.g., "PEPSI (Dir-Seg)" in Fig.  $4$ ) – The above figure shows a FLAIR image from ISLES [\[12](#page-42-13)] stroke dataset, despite

<span id="page-38-1"></span>Table 1. Quantitative comparisons in anatomy and pathology image synthesis among PEPSI, its variants, and the state-of-the-art contrast-agnostic synthesis models. The proposed PEPSI *(i)* outperforms all the other models, especially on single-modality datasets; and *(ii)* preserves its high performance even for cross-modality synthesis.

| Dataset<br>(# of train/test)  | MR Contrast |        | Metric         | SynthSR[15] | Brain-ID [21] | PEPSI(SG-T1w) | PEPSI(SG-FLAIR) | PEPSI(No-Seg) | PEPSI(Dir-Seg) | PEPSI(Proposed) |
|-------------------------------|-------------|--------|----------------|-------------|---------------|---------------|-----------------|---------------|----------------|-----------------|
|                               | Input       | Output |                |             |               |               |                 |               |                |                 |
| ATLAS [19]<br>(590/65)        | T1w         | T1w    | L1 (↓)         | 0.067       | 0.65          | 0.69          | -               | 0.052         | 0.074          | <b>0.036</b>    |
|                               |             |        | PSNR (†) 16.90 |             | 17.91         | 16.54         | -               | 18.46         | 16.01          | <b>21.69</b>    |
|                               |             |        | SSIM (↑) 0.804 |             | 0.833         | 0.845         | -               | 0.861         | 0.831          | <b>0.897</b>    |
| ISLES [12]<br>(137/15)        | FLAIR       | FLAIR  | L1 (↓)         | -           | -             | -             | 0.022           | 0.018         | 0.021          | <b>0.016</b>    |
|                               |             |        | PSNR (†) -     |             |               |               | 23.87           | 25.34         | 24.02          | <b>26.03</b>    |
|                               |             |        | SSIM (↑) -     |             |               |               | 0.962           | 0.942         | 0.926          | <b>0.969</b>    |
| <b>ADNI3</b> [29]<br>(298/33) | T1w         | T1w    | L1 (↓)         | 0.023       | 0.021         | 0.025         | -               | 0.022         | 0.022          | <b>0.020</b>    |
|                               |             |        | PSNR (†) 23.51 |             | 24.42         | 24.44         | -               | 24.01         | 23.37          | <b>26.67</b>    |
|                               |             |        | SSIM (↑) 0.901 |             | 0.899         | 0.930         | -               | 0.932         | 0.931          | <b>0.935</b>    |
|                               | FLAIR       |        | L1 (↓)         | -           | -             | -             | 0.043           | 0.392         | 0.396          | <b>0.036</b>    |
|                               |             |        | PSNR (†) -     |             |               |               | 18.87           | 19.64         | 19.58          | <b>21.40</b>    |
|                               |             |        | SSIM (↑) -     |             |               |               | 0.900           | 0.901         | 0.894          | <b>0.911</b>    |
|                               | FLAIR       | T1w    | L1 (↓)         | 0.027       | 0.026         | 0.027         | -               | 0.027         | 0.029          | <b>0.023</b>    |
|                               |             |        | PSNR (†) 23.25 |             | 23.74         | 23.96         | -               | 23.50         | 23.61          | <b>25.62</b>    |
|                               |             |        | SSIM (↑) 0.906 |             | 0.879         | 0.916         | -               | 0.919         | 0.915          | <b>0.929</b>    |
|                               | FLAIR       |        | L1 (↓)         | -           | -             | -             | 0.044           | 0.0396        | 0.041          | <b>0.034</b>    |
|                               |             |        | PSNR (†) -     |             |               |               | 18.65           | 19.66         | 19.31          | <b>21.77</b>    |
|                               |             |        | SSIM (↑) -     |             |               |               | 0.911           | 0.910         | 0.904          | <b>0.914</b>    |

the acquired FLAIR image clearly indicating WMH (circled in red), their goldstandard pathology segmentation *only* provides/annotates areas of stroke lesions.

Here we propose an indirect pathology supervision approach. Specifically, for each output modality (i.e., MP-RAGE and FLAIR), we employ a "third-party", real-image-supervised pathology segmentation model as a reference, to encourage the pathology estimated from the predicted and ground truth images to align, without imposing strict supervision from the gold-standard pathology maps. As depicted in Fig. [2,](#page-36-1) we pass all intra-subject training samples through the frozen, reference pathology segmentation models  $(\mathcal{P}_{T1}, \mathcal{P}_{T2/FLAIR})$ . The implicit pathology loss is computed based on the segmentation errors between the estimated pathology maps from the synthesized and ground truth images:

$$
\mathcal{L}_{{\text{pathol}}} = \alpha \mathcal{L}_{S_{{\text{T1}}}} + \beta \mathcal{L}_{S_{{\text{T2/FLAIR}}}} \qquad (\alpha, \beta \in \{0, 1\}) \qquad (4)
$$

$$
= \alpha \sum_{i}^{N} \mathcal{L}_{{\text{seg}}}(\widetilde{S_{i}^{{\text{T1}}}}, S^{{\text{T1}}}) + \beta \sum_{i}^{N} \mathcal{L}_{{\text{seg}}}(\widetilde{S_{i}^{{\text{T2/FLAIR}}}}, S^{{\text{T2/FLAIR}}}}).
$$

 $\mathcal{L}_{\text{seg}}$  is the segmentation loss consisting of soft dice and cross-entropy loss [\[2\]](#page-42-2).  $S(\tilde{S})$  denotes the third-party-referenced (predicted) pathology<sup>[1](#page-38-0)</sup>. Therefore, the overall training object writes  $\mathcal{L} = \mathcal{L}_{T1} + \omega \mathcal{L}_{T2/FLAIR}$ ,  $\omega \in \mathcal{R}^+$ .

<span id="page-38-0"></span><sup>1</sup> We train a segmentation model using data with *minimal corruption*, since it shall work well *only if* the inputs are of high quality—It would be uninformative if the segmentation network provides accurate labels for images of any quality.

Image /page/39/Figure/1 description: This figure displays a comparison of different MRI image reconstruction methods. The figure is organized into three rows, labeled (a), (b), and (c), each representing a different dataset: ATLAS (Stroke), ISLES (Stroke), and ADNI3 (WMH), respectively. Each row corresponds to a specific input MRI sequence: T1w for rows (a) and (c), and FLAIR for row (b). Within each row, there are multiple columns representing different reconstruction methods: SynthSR [15], Brain-ID [21], PEPSI (SG-T1w), PEPSI (SG-FLAIR), PEPSI (No-Seg), PEPSI (Dir-Seg), PEPSI (Proposed), and Ground Truth (T1w|FLAIR). Below each set of reconstructed images, there are corresponding 'X' marks or 'No FLAIR Available'/'No T1w Available' labels indicating the availability or success of the reconstruction for that specific input and method. Yellow arrows are used to highlight specific regions of interest, likely lesions or abnormalities, in the reconstructed images.

<span id="page-39-0"></span>Fig. 4. Qualitative comparisons on T1w and FLAIR synthesis ( $\leftrightarrow$  highlights the ground truth regions of pathology). Rows (columns) refer to the datasets (compared methods).

<span id="page-39-1"></span>

## 3 Experiments

We demonstrate the effectiveness of PEPSI from two perspectives: *(i) Image synthesis*—estimating both anatomy and pathology images, with potentially missing modalities (Sect. [3.1\)](#page-40-1); *(ii) Pathology segmentation*—fine-tuning PEPSI on individual datasets for segmenting a specific type of pathology (Sect. [3.2\)](#page-40-2).

Datasets: To cover a broader range of anatomies and pathologies, we train PEPSI on 1025 subjects from (# of train/test cases): *(i)* ADNI3 [\[29](#page-43-8)] (298/33), with 1 mm isotropic T1w and FLAIR pairs with WMH; *(ii)* ATLAS [\[19](#page-42-15)] (590/65), with *only* T1w and manually segmented stroke lesion for subacute/chronic stroke patients; *(iii)* ISLES [\[12](#page-42-13)] (137/15), with *only* FLAIR and stroke lesion segmentation for acute/subacute stroke patients. For pathology segmentation, we also test on ISBI2015 [\[6](#page-42-16)] and MSSEG2016 [\[10\]](#page-42-17), comprising 21 and 15 WMH patients.

Metrics: For image synthesis, we use L1 distance, PSNR, and SSIM (structural similarity) [\[23](#page-43-9)]. For pathology segmentation, we use Dice scores [\[2](#page-42-2)].

Models: We compare PEPSI with the state-of-the-art contrast-agnostic synthesis methods, SynthSR [\[15](#page-42-0)] and Brain-ID [\[21\]](#page-43-7). We also evaluate PEPSI's variants: *(i-* <span id="page-40-0"></span>**Table 2.** Average Dice scores ( $\uparrow$ ) for pathology segmentation, w/o or w/ PEPSI pretrained features. (Numbers in the parentheses denote the convergence/testing epochs  $($  $|$ ); We directly test on ISBI2015 and MSSEG2016 using models trained from ADNI3.

| Model     | ATLAS<br>(Stroke)            | ISLES<br>(Stroke)           | ADNI3 (WMH)                 |                             | ISBI2015 (WMH)              |                             | MSSEG2016 (WMH)             |                             |
|-----------|------------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|
|           |                              |                             | T1w                         | FLAIR                       | T1w                         | FLAIR                       | T1w                         | FLAIR                       |
| w/o PEPSI | 0.49 ± 0.14<br>(2500)        | 0.35 ± 0.13<br>(2000)       | 0.50 ± 0.15<br>(1600)       | 0.67 ± 0.13<br>(1500)       | 0.21 ± 0.05<br>(1600)       | 0.39 ± 0.15<br>(1500)       | 0.24 ± 0.09<br>(1600)       | 0.31 ± 0.10<br>(1500)       |
| w/ PEPSI  | <b>0.71 ± 0.22</b><br>(1000) | <b>0.62 ± 0.27</b><br>(500) | <b>0.69 ± 0.12</b><br>(800) | <b>0.75 ± 0.10</b><br>(500) | <b>0.34 ± 0.06</b><br>(800) | <b>0.57 ± 0.15</b><br>(500) | <b>0.38 ± 0.10</b><br>(800) | <b>0.45 ± 0.11</b><br>(500) |

*ii)* SG-T1w/FLAIR: single-guidance from MR-RAGE/FLAIR; *(iii-iv)* No/Dir-Seg: No/direct supervision from gold-standard pathology segmentations.

Implementation Details: As a general feature representation model, PEPSI can use any backbone to extract features. For fairer comparison, we adopt the same five-level 3D UNet [\[25\]](#page-43-10) as utilized in state-of-the-art models [\[15,](#page-42-0)[21\]](#page-43-7) we compare with. Two linear layers are followed for anatomy and pathology image synthesis (Sect. [2.2\)](#page-36-0). The synthetic pathology-encoded data is of size 128<sup>3</sup> (Sect. [2.1\)](#page-35-1), with batch size as 4. We use AdamW optimizer, with a learning rate of  $10^{-4}$  for the first 160,000 iterations and  $10^{-5}$  until 240,000 iterations. We set  $\lambda = 1$  in Eq. [\(3\)](#page-37-1), and  $\omega = 0.1$  in Sect. [2.2](#page-36-0) for 100,000 iterations, and 1 afterward. The training took  $\approx$  5 days on one NVIDIA RTX8000 GPU.

<span id="page-40-1"></span>

### 3.1 Anatomy and Pathology Image Synthesis

As shown in Table [1,](#page-38-1) PEPSI achieves the best performance in synthesizing T1w and FLAIR, across all datasets and pathologies. Notably, PEPSI exhibits superiority on single-modality datasets (ATLAS [\[19\]](#page-42-15), ISLES [\[12\]](#page-42-13)), and further demonstrates strong *robustness* against contrasts. For example, it maintains consistent scores for T1w synthesis on ADNI3 [\[29\]](#page-43-8), regardless of the input modality.

Thanks to the co-training and pathology-enhanced, contrast-agnostic learning, PEPSI can synthesize images that are *not* present in the original datasets. Fig.  $4-(a)$  $4-(a)$ : PEPSI successfully synthesizes T1w and pathology-enhanced images based on T1w from ATLAS [\[19](#page-42-15)], for which ground truth FLAIR is not available. Remarkably, other models either *cannot* estimate pathology-enhanced images, or struggle to *accurately* capture and highlight (brighten) the areas of pathology. Fig. [4-](#page-39-0)(b): ISLES [\[12\]](#page-42-13) only provides FLAIR and annotations for stroke lesions, yet PEPSI: *(i)* accurately synthesizes T1w images with appropriately *darkened* pathology regions inferred from the FLAIR input, and *(ii)* is not constrained to the stroke lesions manually annotated by ISLES, but instead, captures (*brightens*) all pathological regions including *both* stroke lesions and WMH.

<span id="page-40-2"></span>

### 3.2 Pathology Segmentation

In Sect. [3.1,](#page-40-1) we validate PEPSI's superiority in synthesizing pathology-enhanced images under various contrasts, providing voxel-level information that is *not* confined to particular pathology types, but contains comprehensive information on anomalies. We further illustrate the *efficiency* and *effectiveness* of PEPSI features for downstream pathology segmentations that target a specific pathology.

To this end, we compare the following two models trained on each dataset and contrast, *(i)* starting from random weights (w/o PEPSI), and *(ii)* fine-tuned from PEPSI pre-trained weights  $(w / PEPST)$ . For ATLAS [\[19](#page-42-15)], ISLES [\[12\]](#page-42-13), and ADNI3 [\[29](#page-43-8)], both models are trained and tested on their respective training and testing sets. Since ISBI2015 [\[6](#page-42-16)] and MSSEG2016 [\[10](#page-42-17)] datasets contain only 21 and 15 WMH cases, respectively, we directly evaluate the trained models from ADNI3 (WMH) [\[29](#page-43-8)] on all available cases in these datasets. Note that although PEPSI has undergone pre-training on synthetic data using anatomy labels and pathology probability maps from the training sets of ATLAS [\[19](#page-42-15)] and ISLES [\[12](#page-42-13)] (Sect. [3\)](#page-39-1), it has *not* been exposed to any real image during the pre-training stage.

As shown in Table [2,](#page-40-0) utilizing PEPSI's pre-trained features largely reduces the convergence time (by  $\approx 60\%$  on average) – and, more importantly, it yields higher Dice scores compared with training from scratch (i.e.,  $w/o$  PEPSI) on all testing pathologies, contrasts and datasets. Furthermore, when directly tested on the two small datasets (ISBI2015 and MSSEG2016), PEPSI exhibits superior generalizability compared to models trained without PEPSI pre-trained features.

## 4 Conclusion

We introduced PEPSI, the first pathology-enhanced, contrast-agnostic feature representation learning approach for brain MRI. Trained on synthetic data with diverse contrasts and anomalies, PEPSI exhibits remarkable robustness and accuracy beyond manual annotations of a specific pathology, regardless of MR contrasts. We demonstrated PEPSI's performance on image synthesis, covering T1w and FLAIR with stroke lesions and WMH, and further showcased its efficiency and effectiveness for downstream pathology segmentation on five public datasets. We believe PEPSI will pave the way for the exciting future of contrast-agnostic pathology representations for heterogeneous, real-world brain MRI – enabling studies of diverse brain diseases with large clinical MRI datasets.

Acknowledgments. Primarily supported by NIH 1RF1AG080371. Additional support from NIH 1RF1AG080371, 1R01EB031114, 1R01AG070988, 1RF1MH123195. ASA is funded by the American Heart Association Postdoctoral Fellowship and the Fulbright Commission. OP is supported by Lundbeckfonden R360-2021-395.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

### References

<span id="page-41-0"></span>1. Awais, M., Naseer, M., Khan, S.S., Anwer, R.M., Cholakkal, H., Shah, M., et al.: Foundational models defining a new era in vision: A survey and outlook. arXiv abs/2307.13721 (2023)

- <span id="page-42-2"></span>2. Billot, B., Greve, D.N., Puonti, O., Thielscher, A., Leemput, K.V., Fischl, B.R., et al.: SynthSeg: Segmentation of brain MRI scans of any contrast and resolution without retraining. Medical Image Analysis (2021)
- <span id="page-42-6"></span>3. Bommasani, R., Hudson, D.A., Adeli, E., Altman, R., Arora, S., von Arx, S., et al.: On the opportunities and risks of foundation models. arXiv abs/2108.07258 (2021)
- <span id="page-42-1"></span>4. Brant-Zawadzki, M.N., Gillan, G.D., Nitz, W.R.: MP RAGE: A three-dimensional, T1-weighted, gradient-echo sequence–initial experience in the brain. Radiology (1992)
- <span id="page-42-7"></span>5. Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J.D., Dhariwal, P., et al.: Language models are few-shot learners. In: NeurIPS (2020)
- <span id="page-42-16"></span>6. Carass, A., Roy, S., Jog, A., Cuzzocreo, J.L., Magrath, E., Gherman, A., et al.: Longitudinal multiple sclerosis lesion segmentation: resource and challenge. NeuroImage (2017)
- <span id="page-42-10"></span>7. Cardoso, M.J., Li, W., Brown, R., Ma, N., Kerfoot, E., Wang, Y., et al.: MONAI: An open-source framework for deep learning in healthcare. arXiv  $abs/2211.02701$ (2022)
- <span id="page-42-8"></span>8. Chowdhery, A., Narang, S., Devlin, J., Bosma, M., Mishra, G., Roberts, A., et al.: PaLM: Scaling language modeling with pathways. JMLR (2022)
- <span id="page-42-11"></span>9. Chua, Y.Z.R., Dalca, A.V.: Contrast invariant feature representations for segmentation and registration of medical images. In: MIDL (2023)
- <span id="page-42-17"></span>10. Commowick, O., Kain, M., Casey, R., Ameli, R., Ferré, J.C., Kerbrat, A., et al.: Multiple sclerosis lesions segmentation from multiple experts: The MICCAI 2016 challenge dataset. Neuroimage (2021)
- <span id="page-42-14"></span>11. Hajnal, J.V., Bryant, D.J., Kasuboski, L., Pattany, P.M., De Coene, B., Lewis, P.D., et al.: Use of fluid attenuated inversion recovery (FLAIR) pulse sequences in MRI of the brain. Journal of computer assisted tomography (1992)
- <span id="page-42-13"></span>12. Hernandez Petzsche, M.R., de la Rosa, E., Hanning, U., Wiest, R., Valenzuela, W., Reyes, M., et al.: ISLES 2022: A multi-center magnetic resonance imaging stroke lesion segmentation dataset. Scientific data (2022)
- <span id="page-42-3"></span>13. Hoffmann, M., Billot, B., Greve, D.N., Iglesias, J.E., Fischl, B.R., Dalca, A.V.: SynthMorph: Learning contrast-invariant registration without acquired images. IEEE Transactions on Medical Imaging (2020)
- <span id="page-42-12"></span>14. Hoopes, A., Mora, J.S., Dalca, A.V., Fischl, B.R., Hoffmann, M.: SynthStrip: skullstripping for any brain image. NeuroImage (2022)
- <span id="page-42-0"></span>15. Iglesias, J.E., Billot, B., Balbastre, Y., Magdamo, C.G., Arnold, S., Das, S., et al.: SynthSR: A public AI tool to turn heterogeneous clinical brain scans into highresolution T1-weighted images for 3D morphometry. Science Advances (2023)
- <span id="page-42-4"></span>16. Iglesias, J.E., Billot, B., Balbastre, Y., Tabari, A., Conklin, J., Alexander, D.C., et al.: Joint super-resolution and synthesis of 1 mm isotropic MP-RAGE volumes from clinical MRI exams with scans of different orientation, resolution and contrast. NeuroImage (2020)
- <span id="page-42-9"></span>17. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., et al.: Segment anything. arXiv abs/2304.02643 (2023)
- <span id="page-42-5"></span>18. Laso, P., Cerri, S., Sorby-Adams, A., Guo, J., Mateen, F., Goebl, P., et al.: Quantifying white matter hyperintensity and brain volumes in heterogeneous clinical and low-field portable MRI. arXiv abs/2312.05119 (2023)
- <span id="page-42-15"></span>19. Liew, S.L., Anglin, J.M., Banks, N.W., Sondag, M., Ito, K.L., Kim, H., et al.: A large, open source dataset of stroke anatomical brain images and manual lesion segmentations. Scientific data (2018)

- <span id="page-43-1"></span>20. Liu, P., Lee, Y., Aylward, S., Niethammer, M.: Deep decomposition for stochastic normal-abnormal transport. In: CVPR (2022)
- <span id="page-43-7"></span>21. Liu, P., Puonti, O., Hu, X., Alexander, D.C., Iglesias, J.E.: Brain-ID: Learning robust feature representations for brain imaging. In: ECCV (2024)
- <span id="page-43-2"></span>22. Liu, P., Tian, L., Zhang, Y., Aylward, S., Lee, Y., Niethammer, M.: Discovering hidden physics behind transport dynamics. In: CVPR (2021)
- <span id="page-43-9"></span>23. Liu, P., Wang, R., Cao, X., Zhou, Y., Shah, A., Lim, S.N.: Differential motion evolution for fine-grained motion deformation in unsupervised image animation. arXiv abs/2110.04658 (2021)
- <span id="page-43-4"></span>24. Moor, M., Banerjee, O., Abad, Z.F.H., Krumholz, H.M., Leskovec, J., Topol, E.J., Rajpurkar, P.: Foundation models for generalist medical artificial intelligence. Nature (2023)
- <span id="page-43-10"></span>25. Ronneberger, O., Fischer, P., Brox, T.: U-Net: Convolutional networks for biomedical image segmentation. In: MICCAI (2015)
- <span id="page-43-5"></span>26. Singhal, K., Azizi, S., Tu, T., Mahdavi, S., Wei, J., Chung, H.W., et al.: Large language models encode clinical knowledge. Nature (2022)
- <span id="page-43-6"></span>27. Tu, T., Azizi, S., Driess, D., Schaekermann, M., Amin, M., Chang, P.C., et al.: Towards generalist biomedical AI. arXiv abs/2307.14334 (2023)
- <span id="page-43-0"></span>28. Wang, M., Deng, W.: Deep visual domain adaptation: A survey. Neurocomputing (2018)
- <span id="page-43-8"></span>29. Weiner, M.W., Veitch, D.P., Aisen, P.S., Beckett, L.A., Cairns, N.J., Green, R.C., et al.: The Alzheimer's disease neuroimaging initiative 3: Continued innovation for clinical trial improvement. Alzheimer's & Dementia (2017)
- <span id="page-43-3"></span>30. Zhou, Y., Chia, M.A., Wagner, S.K., Ayhan, M.S., Williamson, D.J., Struyven, R.R., et al.: A foundation model for generalizable disease detection from retinal images. Nature (2023)

Image /page/44/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line segment on the left and a flag shape on the right. The button is light gray with a subtle gradient, and the text is dark gray.

# Prompting Vision-Language Models for Dental Notation Aware Abnormality **Detection**

Chenlin Du<sup>1</sup> $\bullet$ [,](http://orcid.org/0000-0002-2500-1814) Xiaoxuan Chen<sup>2</sup>, Jingyi Wang<sup>2</sup>, Juniie Wang<sup>3</sup>, Zhongsen Li<sup>1</sup> $\bullet$ . Zongjiu Zhang<sup>1,4( $\boxtimes$ [\)](http://orcid.org/0000-0002-6032-8548)</sup>, and Qicheng Lao<sup>3,5( $\boxtimes$ )</sup>

<sup>1</sup> School of Biomedical Engineering, Tsinghua University, Beijing, China <EMAIL>

<sup>2</sup> State Key Laboratory of Oral Diseases and National Center for Stomatology and National Clinical Research Center for Oral Diseases, West China Hospital of

Stomatology, Sichuan University, Chengdu, China

<sup>3</sup> School of Artificial Intelligence, Beijing University of Posts and

Telecommunications (BUPT), Beijing, China

<EMAIL>

<sup>4</sup> Institute for Hospital Management, Tsinghua University, Beijing, China

<sup>5</sup> Shanghai Artificial Intelligence Laboratory, Shanghai, China

Abstract. The large pretrained vision-language models (VLMs) have demonstrated remarkable data efficiency when transferred to the medical domain. However, the successful transfer hinges on the development of effective prompting strategies. Despite progress in this area, the application of VLMs to dentistry, a field characterized by complex, multi-level dental abnormalities and subtle features associated with minor dental issues, remains uncharted territory. To address this, we propose a novel approach for detecting dental abnormalities by prompting VLMs, leveraging the symmetrical structure of the oral cavity and guided by the dental notation system. Our framework consists of two main components: dental notation-aware tooth identification and multilevel dental abnormality detection. Initially, we prompt VLMs with tooth notations for enumerating each tooth to aid subsequent detection. We then initiate a multi-level detection of dental abnormalities with quadrant and tooth codes, prompting global abnormalities across the entire image and local abnormalities on the matched teeth. Our method harmonizes subtle features with global information for local-level abnormality detection. Extensive experiments on the re-annotated DET-NEX dataset demonstrate that our proposed framework significantly improves performance by at least 4.3% mAP and 10.8% AP50 compared to state-of-the-art methods. Code and annotations will be released on [https://github.com/CDchenlin/DentalVLM.](https://github.com/CDchenlin/DentalVLM)

C. Du and X. Chen—Equal contribution.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_64.](https://doi.org/10.1007/978-3-031-72390-2_64)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 687–697, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_64)\_64

**Keywords:** Vision-language models  $\cdot$  Dental abnormality detection  $\cdot$  Prompting  $\cdot$  Dental notation system  $\cdot$  Dental panoramic X-ray image

## 1 Introduction

Recently, the accomplishments of large-scale pretrained vision-language models (VLMs) such as CLIP [\[18\]](#page-54-0), GLIP [\[11\]](#page-53-0), and Grounding DINO [\[25](#page-54-1)] have garnered attention. These models undergo initial pretraining to learn universal representations through extensive unlabelled data and have demonstrated data efficiency when transferred to the medical domain  $[5,13,17,22]$  $[5,13,17,22]$  $[5,13,17,22]$  $[5,13,17,22]$  $[5,13,17,22]$  $[5,13,17,22]$ . However, the performance of VLMs can be significantly influenced by the prompts used for textual and visual alignment [\[23\]](#page-54-4), and therefore developing appropriate prompting approaches for medical domain is the fundamental key to the success of transferring [\[5](#page-53-1)[,17,](#page-54-2)[22\]](#page-54-3).

Despite these advancements, the generalization of VLMs to the field of dentistry remains largely unexplored. In the dental clinical practice, dental panoramic X-ray are universally recognized as fundamental radiography for oral health information [\[19](#page-54-5)]. They provide a thorough visualization of all teeth and adjacent structures within a single image, facilitating a preliminary assessment of dental abnormalities, such as dental caries, impacted tooth, and periodontal bone loss [\[16](#page-54-6)]. Consequently, an exhaustive analysis of dental panoramic X-ray images is indispensable for screening purposes or therapeutic decision-making. Nonetheless, it has been extensively documented that their interpretation is extremely time-consuming [\[1](#page-53-3),[19\]](#page-54-5) and often sensitive to radiologists' experience [\[10\]](#page-53-4).

The majority of current machine learning methods developed to assist in interpreting dental panoramic X-ray images are either task-specific [\[2,](#page-53-5)[9](#page-53-6)[,15,](#page-53-7)[21\]](#page-54-7), or exhibit limitations in accurately localizing fine-grained dental abnormalities [\[6](#page-53-8)[,12](#page-53-9)[,24](#page-54-8)]. Notably, none of these methods have harnessed the benefits offered by the dental notation system, e.g., the Fédération Dentaire Internationale (FDI) notation system [\[20\]](#page-54-9). The dental notation system can serve as a crucial tool for facilitating precise dental abnormality detection through the identification and record-keeping of individual teeth [\[4\]](#page-53-10). Furthermore, it inherently captures the symmetrical structure of the oral cavity, a significant yet under-exploited aspect in dental radiology, which could potentially augment multi-level abnormality detection of dental panoramic X-ray images. Dental abnormalities typically manifest at multiple levels, where some are detectable at a global level across the entire image, such as impacted tooth and residual root, and others are local-level abnormalities, such as dental caries, tooth defect, characterized by the relatively subtle details compared to large dental panoramic X-ray images [\[15](#page-53-7)]. These distinctive properties within dentistry could be potentially harnessed to effectively prompt VLMs, thereby enhancing the overall detection of dental abnormalities.

In this paper, we present a dental notation-aware abnormality detection framework by leveraging the dental notation system and incorporating multilevel abnormality prompting. Our framework comprises two main components. Initially, we prompt fine-tuned vision-language models for dental notation-aware

tooth detection, which enumerates each tooth to facilitate subsequent dental abnormality detection. Next, our approach embarks on multi-level detection of all fine-grained dental abnormalities. To accomplish this, we first retrieve all the corresponding quadrant codes and then align the tooth codes within the FDI notation system. This strategy is inspired by the clinical practice of dentists where analogous teeth in other quadrants are often referred to due to dental symmetry, achieving a balance between the relatively subtle feature and the inclusion of global information and symmetry, particularly in the context of local-level abnormality detection. We then prompt VLMs with global abnormalities across the entire X-ray image and local abnormalities on the matched teeth, respectively, to achieve multi-level dental abnormality detection. Our proposed framework is validated on the re-annotated DENTEX dataset [\[7](#page-53-11)], and experimental results demonstrate its superior performance in fine-grained dental abnormality detection, where the overall detection performance is significantly improved by at least 4.3% mAP and 10.8% AP50 compared to state-of-the-art methods.

Image /page/46/Figure/2 description: This is a flowchart illustrating a dental notation-aware tooth identification and multi-level dental abnormality detection system. The system begins with a dental panoramic X-ray image. It uses a 'Tooth notation prompting' module with a text encoder and image encoder, feeding into a Finetuned VLM, to identify teeth based on FDI notation (quadrant code cq and tooth code ct). The system then proceeds to 'Multi-level Dental Abnormality Detection'. This involves 'Local abnormality prompting' where specific dental issues like tooth defect, dental caries, and periapical periodontitis are prompted, and 'Global abnormality prompting' for conditions like impacted tooth, periodontitis, and residual root. Both prompting methods utilize text and image encoders, feeding into another Finetuned VLM. The output of the local abnormality detection is shown as bounding boxes on tooth diagrams, indicating different abnormalities such as impacted tooth, periodontitis, dental caries, tooth defect, periapical periodontitis, and residual root. Finally, a 'Map back' step integrates these detected abnormalities onto the original dental panoramic X-ray image, showing localized bounding boxes for each identified issue.

<span id="page-46-0"></span>Fig. 1. Illustration of the proposed framework.

## 2 Method

To address the challenge of detecting small dental abnormalities that often present as relatively subtle features within large dental panoramic X-ray images, we propose a robust multi-level detection framework by leveraging a dental notation system, as depicted in Fig. [1](#page-46-0) Our framework primarily comprises two components: dental notation aware tooth identification, which accurately enumerates each tooth for subsequent detection (Sect. [2.2\)](#page-47-0); and multi-level dental abnormality detection, which balances between the relatively small feature map of dental abnormalities and the global information and symmetry available in the image (Sect. [2.3\)](#page-48-0). Together, these components form a comprehensive approach to detecting dental abnormalities within dental panoramic X-ray images.

### 2.1 Task Formulation

In this paper, we aim to precisely detect all fine-grained dental abnormalities, including impacted teeth, periodontitis, residual roots, tooth defects, dental caries, and periapical periodontitis from dental panoramic X-ray images of those aged 12 years or above. Considering the data scarcity, we employ large pretrained vision-language models (VLMs), specifically, Grounding DINO [\[14\]](#page-53-12) in our framework. Given an input dental panoramic X-ray images  $X$ , we design text prompt  $P_A$  with the class names of the M candidate dental abnormalities, i.e.,  $P_A = [abnormality_1, abnormality_2, \cdots, abnormality_M]$ . Therefore, we can predict bounding boxes of candidate abnormal regions  $Z_A = [cls, x, y, w, h]$  with  $VLM(\cdot,*)$  denoting the prediction process:

$$
Z_A = VLM(X, P_A),\tag{1}
$$

where cls denotes the class name of the tooth abnormality, while  $(x, y)$  is the coordinates of the top-left corner of the box, and  $(w, h)$  is the box size. With the following aligning/grounding process, we can fine-tune the pre-trained VLM model with  $Enc<sub>I</sub>$  and  $Enc<sub>T</sub>$  representing distinct encoders for image and text prompt, respectively:

$$
V = Enc_I(X), U = Enc_T(P_A), S_{ground} = VU^\top, L_{cls} = Loss(S_{ground}, G), (2)
$$

where  $V \in \mathbb{R}^{N \times d}$ ,  $U \in \mathbb{R}^{M \times d}$  denote the image and text features respectively for N candidate region proposals and M target tooth abnormalities,  $S_{ground} \in$  $\mathbb{R}^{N \times M}$  represents the alignment scores, and  $G \in \{0,1\}^{N \times M}$  is the target matrix.

<span id="page-47-0"></span>

### 2.2 Dental Notation Aware Tooth Identification

We first argue that conventional dental detection models trained with one-hot labels ignore the symmetry of teeth during tooth enumeration. For instance, despite their anatomical symmetry, central incisors at the upper/lower right and left are usually encoded as  $[1, 0, 0, 0, \cdots, 0], [0, 1, 0, 0, \cdots, 0], [0, 0, 1, 0, \cdots, 0],$  $[0, 0, 0, 1, \cdots, 0]$ , which ignore the semantic and symmetric relationship between teeth. To maximally utilize the symmetry property of teeth for subsequent abnormality detection, we adopt the Fédération Dentaire Internationale (FDI) notation system [\[20](#page-54-9)] to locate the tooth  $T_i$  by code  $C^i = [c_q^i, c_t^i]$  in the dental panoramic X-ray image, where  $c_q \in \{1, 2, 3, 4\}$  is the quadrant code, and  $c_t \in \{1, 2, 3, 4, 5, 6, 7, 8\}$  is the tooth code. Note that during our preliminary experiments, we found that dental implants might prohibit the accurate matching of tooth notation, thereby deteriorating the subsequent abnormality detection. Thus, we consider detecting dental implants together with tooth identification. The text prompt  $P_T$  utilized in this stage can be concluded as follows:

$$
P_T = [\text{tooth } C^1, \text{tooth } C^2, \cdots, \text{tooth } C^{32}, \text{Implant}]. \tag{3}
$$

We then use a fine-tuned VLM to identify and notate all the teeth, while detecting all dental implants in the panoramic image X with prompt  $P_T$ :

$$
[T, Z_D] = VLM(X, P_T), \qquad (4)
$$

where  $T_i = [C^i, x, y, w, h]$  are the tooth identified at the location  $C^i$  in the FDI notation system.  $Z_D$  are all detected dental implants, then disconnected.

<span id="page-48-0"></span>

### 2.3 Multi-level Dental Abnormality Detection

To balance the detailed feature map of dental abnormalities with the broader context, we emulate the clinical practice of dentists. Dentists often refer to analogous teeth in other quadrants due to dental symmetry. We achieve this by first retrieving all the corresponding quadrant codes and then matching their tooth codes within the FDI notation system. More specifically, after identifying and numbering all the teeth, each tooth is cropped to form a set of proposed tooth regions, denoted as  $\{\tau_{c_q^i, c_t^i}\}$  from the input radiography X. Here  $\tau_{c_q^i, c_t^i}$  represents the proposed box region of detected tooth  $T_i$  in the image  $X$  at the location  $[c_q^i, c_t^i]$  in the FDI notation system. For a given set of proposals  $\{\tau_{c_q^i, c_t^i}\}$ , we retrieve all the  $c_q$  and match their  $c_t$ .

All the retrieved and matched tooth regions  $\{\tau_{1,c_i^i}, \tau_{2,c_i^i}, \tau_{3,c_i^i}, \tau_{4,c_i^i}\}\)$  are then positioned in a new tooth image  $\Omega_{c_i^i}$ , according to their  $c_q$ . Specifically, regions with  $c_q$  from 1 to 4 are placed in the top left, top right, bottom right, and bottom left quadrants, respectively. We conclude this process as the following equation:

$$
\Omega_{c_t^i} = \left[ \frac{\tau_{1,c_t^i} | \tau_{2,c_t^i}}{\tau_{4,c_t^i} | \tau_{3,c_t^i}} \right].
$$
\n(5)

It is worth noting that we allow absent teeth in the tooth image  $\Omega_{c_t^i}$  where a single or multiple  $\tau_{c_q^i, c_t^i}$  can be missing.

Subsequently, given all matched tooth images  $\{\Omega_{c_i^i}\}\$ , we can then effectively detect all local-level dental abnormalities by designing prompt with their class names, i.e.,  $P_{\text{local}} = [\text{tooth defect}, \text{dental carries}, \text{peri apical period}$ We define local-level dental abnormalities as those sensitive to local features, that are more perceivable in the tooth image  $\Omega$  compared to the original panoramic image X. Predicted results of local abnormalities  $Z_{local}$  are then mapped back to the original position in the panoramic image. For global-level abnormalities such as impacted tooth, we directly locate them as  $Z_{\text{global}}$  from the panoramic image with text prompt  $P_{\text{global}} =$  [impacted tooth, periodontitis, residual root, $\cdots$ ]. Consequently, the multi-level detection of dental abnormalities can be described as follows:

$$
Z_A = Z_{\text{local}} \bigcup Z_{\text{global}} = Map(VLM\left(\Omega, P_{\text{local}}\right)) \bigcup VLM(X, P_{\text{global}}),\tag{6}
$$

where  $Map(\cdot)$  denotes the mapping operation of local abnormalities from the tooth image to the original panoramic image.

## 3 Experiments and Results

### 3.1 Experimental Setup

Dataset. In this study, we employ the quadrant-enumerated subset of the DEN-TEX dataset [\[7](#page-53-11)], encompassing 645 panoramic X-ray images. These X-rays are meticulously annotated with the assistance of dental professionals. It is noteworthy that our annotation process also encompasses dental implants and involves the refinement of bounding boxes for each tooth present in every X-ray image. The dataset is randomly partitioned into training, validation, and test sets, containing 405, 102, and 127 panoramic X-ray images. For quantification of the detection performance, we report the average precision (AP) and AP50.

Implementation Details. We adopt the Grounding-DINO-T variant [\[14\]](#page-53-12) as our pre-trained vision-language model. Our models are trained using the AdamW optimizer, with a base learning rate and weight decay both set at  $1 \times 10^{-4}$ . The model with the best performance on the validation set is subsequently utilized for further evaluation. To compensate for potential errors in tooth enumeration, we crop each tooth by an additional 20, 40, 10, and 10 pixels on the crown, root, left, and right sides, respectively. During the inference, all prompts used in our framework were fixed. All baseline models were evaluated with fine tuning using the same data split as our proposed approach.

### 3.2 Comparison with State-of-the-Art Methods

To thoroughly evaluate our proposed method, we compare the performance of our framework in tooth identification, dental implant, and abnormality detection against state-of-the-art detection models that can be adapted for dental object detection. These include Dyhead [\[3](#page-53-13)], DINO [\[25\]](#page-54-1), and two VLMs, specifically

| Method              | Tooth |      | Implant |      | Abnormality |      |
|---------------------|-------|------|---------|------|-------------|------|
|                     | mAP   | AP50 | AP      | AP50 | mAP         | AP50 |
| Dyead [3]           | 64.5  | 97.1 | 49.6    | 81.6 | 18.1        | 36.7 |
| DINO [25]           | 63.4  | 94.3 | 26.5    | 48.0 | 12.6        | 23.5 |
| SegAndDet [8]       | 64.3  | 96.3 | 54.7    | 83.2 | 20.4        | 37.7 |
| PDCNN [9]           | 60.1  | 91.6 | 57.2    | 93.2 | 1.2         | 4.0  |
| HierarchicalDet [6] | 59.8  | 91.6 | 54.0    | 85.6 | 23.1        | 46.4 |
| GLIP [11]           | 66.7  | 96.7 | 71.9    | 99.6 | 30.3        | 52.9 |
| G-DINO [14]         | 67.4  | 97.1 | 75.2    | 100  | 32.7        | 55.5 |
| Ours                | 67.4  | 97.1 | 75.2    | 100  | 37.0        | 66.3 |

<span id="page-49-0"></span>Table 1. Overview of quantitative comparison with state-of-the-art detection methods for tooth identification, dental implant, and abnormality detection  $(\%)$ .

| Method              | Local-level Abnormality |      |        |      |            |      | Global-level Abnormality |      |             |      |          |      |
|---------------------|-------------------------|------|--------|------|------------|------|--------------------------|------|-------------|------|----------|------|
|                     | Defect                  |      | Caries |      | Periapical |      | Impacted                 |      | Periodontal |      | Residual |      |
|                     | AP                      | AP50 | AP     | AP50 | AP         | AP50 | AP                       | AP50 | AP          | AP50 | AP       | AP50 |
| Dyead [3]           | 5.9                     | 15.1 | 1.8    | 6.6  | 7.4        | 23.4 | 53.1                     | 83.6 | 35.6        | 73.4 | 5.0      | 18.2 |
| DINO [25]           | 3.4                     | 8.6  | 0.6    | 2.5  | 1.3        | 5.1  | 46.5                     | 73.4 | 23.1        | 48.9 | 1.0      | 2.8  |
| SegAndDet [8]       | 4.8                     | 11.0 | 2.7    | 11.1 | 8.0        | 24.6 | 56.2                     | 76.8 | 34.6        | 68.9 | 16.1     | 34.1 |
| PDCNN [9]           | 0.0                     | 0.0  | 0.0    | 0.0  | 0.0        | 0.0  | 2.6                      | 8.8  | 4.7         | 15.1 | 0.0      | 0.0  |
| HierarchicalDet [6] | 8.6                     | 14.8 | 5.2    | 14.8 | 10.3       | 30.9 | 60.5                     | 84.6 | 32.2        | 68.0 | 21.9     | 65.3 |
| GLIP [11]           | 9.1                     | 19.3 | 6.3    | 17.4 | 12.2       | 36.2 | 70.5                     | 91.4 | 39.5        | 77.8 | 44.4     | 75.0 |
| G-DINO [14]         | 10.5                    | 21.5 | 9.8    | 30.2 | 16.0       | 41.7 | 71.7                     | 91.4 | 44.9        | 82.3 | 43.2     | 65.9 |
| Ours                | 21.2                    | 34.0 | 17.3   | 48.9 | 21.6       | 61.8 | 71.2                     | 89.8 | 44.6        | 82.4 | 45.9     | 80.6 |

<span id="page-50-0"></span>Table 2. Quantitative comparison with state-of-the-art detection methods for multilevel dental abnormality detection (%).

GLIP [\[11](#page-53-0)] and Grounding DINO [\[14\]](#page-53-12). Furthermore, our framework has been benchmarked against models specifically designed for dental applications, such as SegAndDet [\[8\]](#page-53-14), the winner of the DENTEX [\[7](#page-53-11)] (MICCAI Challenge 2023), as well as PDCNN [\[9](#page-53-6)], and HierarchicalDet [\[6\]](#page-53-8) to ensure a comprehensive evaluation.

The Proposed Approach Achieves the Best Performance in Dental Abnormality Detection Compared to State-of-the-Arts. Table [1](#page-49-0) indicates that our framework outperforms all state-of-the-art detection models in dental abnormality detection, as well as tooth and implant identification. Notably, our proposed approach by leveraging the tooth notation prompting exhibits superior performance in dental abnormality detection, obtaining 36.7% mAP and 66.3% AP50 when compared to other baselines where the second highest is Grounding DINO  $[14]$  $[14]$  with 32.7% mAP and 55.5% AP50, an increase of  $4\%$ in mAP and 10.8% in AP50, respectively. Our results also demonstrate superior accuracy in tooth enumeration and implant detection, especially compared with conventional one-hot encoded models such as Dyhead [\[3](#page-53-13)] and DINO [\[25\]](#page-54-1). This highlights the effectiveness of our proposed tooth enumeration approach, which leverages the FDI notation system in facilitating subsequent dental abnormality detection.

The Proposed Approach Significantly Improves the Local-Level Abnormality Detection. As further elaborated in Table [2,](#page-50-0) our framework significantly outperforms other methods in detecting local-level dental abnormalities including tooth defect, dental caries, and periapical periodontitis. We observe a significant improvement in both mAP and AP50 scores, with an average increase of 7.9% and 17.2% respectively. The significant improvement

Image /page/51/Picture/1 description: This image is a comparative analysis of different object detection models on dental X-rays. It displays rows for 'Defect', 'Caries', and 'Periapical' conditions, and columns for 'Ground Truth', 'Dyhead', 'DINO', 'SegAndDet', 'PDCNN', 'HierarchicalDet', 'GLIP', 'G-DINO', and 'Ours'. Each cell shows a dental X-ray with bounding boxes indicating detected anomalies, along with confidence scores. The 'Ground Truth' column shows the correct annotations. The other columns show the results from various models, highlighting their performance in detecting defects, caries, and periapical issues. The 'Ours' column shows the results from the proposed model, with bounding boxes and confidence scores for 'Defect' (29.1%), 'Caries' (17.6%, 79.8%), and 'Periapical' (11.0%).

Fig. 2. Visualization of detection results for local-level dental abnormality detection.

<span id="page-51-1"></span><span id="page-51-0"></span>Table 3. Ablation on key components for local-level detection (%).

| Tooth | Notation-aware Implant |                                                                           | $\rm Defect$ |                                                                                                 | Caries |  | Periapical Loval-level |           |  |
|-------|------------------------|---------------------------------------------------------------------------|--------------|-------------------------------------------------------------------------------------------------|--------|--|------------------------|-----------|--|
|       | Cropping Matching      | $Disconnection$ AP50 AP $AP$ $AP$ $AP$ $AP$ $AP$ $AP$ $AP$ $AP$ $AP$ $AP$ |              |                                                                                                 |        |  |                        |           |  |
|       |                        |                                                                           |              | $ 10.5 21.5 $ 9.8 $ 30.2 16.0 41.7 12.1 31.1$                                                   |        |  |                        |           |  |
|       |                        |                                                                           | 9.2          | $\begin{array}{ c c c c c c c c c c c c c c c c c c c$                                          |        |  |                        | 10.2 28.0 |  |
|       |                        |                                                                           |              | <b>21.836.8</b> 17.1 47.8 18.7 55.6 19.2 46.7                                                   |        |  |                        |           |  |
|       |                        |                                                                           |              | $\left 21.2\right 34.0$ $\left 17.3\right 48.9$ $\left 21.6\right 61.8$ $\left 20.0\right 48.2$ |        |  |                        |           |  |

<span id="page-51-2"></span>Table 4. Ablation study on designing dental notation prompts (%).

<span id="page-51-3"></span>Table 5. Ablation study on multi-level prompting (%).

| Prompt                        | Tooth       |             | Implant     |            |
|-------------------------------|-------------|-------------|-------------|------------|
|                               | mAP         | AP50        | AP          | AP50       |
| One-hot                       | 63.4        | 94.3        | 26.5        | 48.0       |
| tooth $c_t$ at quadrant $c_q$ | 67.2        | 96.9        | 72.7        | 97.8       |
| tooth $c_q c_t$               | <b>67.4</b> | <b>97.1</b> | <b>75.2</b> | <b>100</b> |

| Detection Level   | Abnormality |             |
|-------------------|-------------|-------------|
|                   | mAP         | AP50        |
| Global-level Only | 32.7        | 55.5        |
| Local-level Only  | 25.8        | 49.7        |
| Multi-level       | <b>37.0</b> | <b>66.3</b> |

underscores the ability of our framework to effectively amalgamate the detailed features of dental abnormalities with the inherent global and symmetrical information present in dental panoramic X-ray images, facilitated by the dental notation system.

Visualizations. Figure [2](#page-51-0) demonstrates our framework's ability to accurately detect three local-level dental abnormalities. It shows fewer errors in locating tooth defect and superior sensitivity in identifying subtle abnormalities such as dental caries and periapical periodontitis compared to other methods. Full-size dental panoramic X-ray images are available in the appendix.

### 3.3 Ablation Study

We perform ablation studies to evaluate the effectiveness of the key components in our proposed framework for dental abnormality detection. We first investigate key modules for local-level detection in our dental abnormality detection framework, including tooth cropping, dental notation-aware tooth matching, and implant disconnection. Additionally, we assess the impact of different dental notation prompts on tooth enumeration and implant identification, as well as the influence of multi-level prompting on abnormality detection.

Ablation on Key Components for Local-Level Detection. Table [3](#page-51-1) reveals the indispensability of all three key components proposed for local-level detection. While tooth cropping improves local feature discernment, it compromises the detection of tooth defect and periapical periodontitis due to the loss of global and symmetrical information. This is counterbalanced by the tooth matching, with average performance further boosted by implant disconnection.

Ablation on Dental Notation Prompts. Two FDI notation prompts and one-hot encoding are evaluated for tooth enumeration and implant identification (Table [4\)](#page-51-2). Both FDI notation prompts outperformed one-hot encoding, with the 'tooth c*q*c*t*' prompt yielding the best results, more suitable for our framework.

Ablation on Multi-level Prompting. Table [5](#page-51-3) indicates that multi-level prompting, combining global and local prompts, proved effective in addressing the multi-level manifestation of dental abnormalities, compared with global or local only.

## 4 Conclusion

In this study, we propose a robust, multi-level, dental notation-aware abnormality detection framework that leverages vision-language models to identify finegrained dental abnormalities. Our methodology, substantiated by comprehensive experimental results, exhibits significant efficacy. Unlike conventional approaches that either scan the entire X-ray image or solely concentrate on isolated tooth images, our framework harnesses the benefits offered by the dental notation system. Our framework adeptly balances the relatively diminutive feature map of dental abnormalities with the globally available information and symmetry. This balance enables it to effectively address the multi-level manifestation of dental abnormalities. A notable constraint of our method is the necessity for multiple fine-tuning stages for each component, which we leave for future work.

Disclosure of Interests. No conflicts of interests to be declared.

### References

- <span id="page-53-3"></span>1. Bruno, M.A., Walker, E.A., Abujudeh, H.H.: Understanding and confronting our mistakes: the epidemiology of error in radiology and strategies for error reduction. Radiographics 35(6), 1668–1676 (2015)
- <span id="page-53-5"></span>2. Chang, J., Chang, M.F., Angelov, N., Hsu, C.Y., Meng, H.W., Sheng, S., Glick, A., Chang, K., He, Y.R., Lin, Y.B., et al.: Application of deep machine learning for the radiographic diagnosis of periodontitis. Clinical Oral Investigations  $26(11)$ , 6629–6637 (2022)
- <span id="page-53-13"></span>3. Dai, X., Chen, Y., Xiao, B., Chen, D., Liu, M., Yuan, L., Zhang, L.: Dynamic head: Unifying object detection heads with attentions. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 7373–7382 (June 2021)
- <span id="page-53-10"></span>4. Erfan, O., Qasemian, E., Khan, M., et al.: Introduction of new tooth notation systems in comparison with currently in-use systems. European Journal of Dental and Oral Health 3(2), 35–48 (2022)
- <span id="page-53-1"></span>5. Guo, M., Yi, H., Qin, Z., Wang, H., Men, A., Lao, Q.: Multiple prompt fusion for zero-shot lesion detection using vision-language models. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 283–292. Springer (2023)
- <span id="page-53-8"></span>6. Hamamci, I.E., Er, S., Simsar, E., Sekuboyina, A., Gundogar, M., Stadlinger, B., Mehl, A., Menze, B.: Diffusion-based hierarchical multi-label object detection to analyze panoramic dental x-rays. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 389–399. Springer (2023)
- <span id="page-53-11"></span>7. Hamamci, I.E., Er, S., Simsar, E., Yuksel, A.E., Gultekin, S., Ozdemir, S.D., Yang, K., Li, H.B., Pati, S., Stadlinger, B., et al.: Dentex: An abnormal tooth detection with dental enumeration and diagnosis benchmark for panoramic x-rays. arXiv preprint [arXiv:2305.19112](http://arxiv.org/abs/2305.19112) (2023)
- <span id="page-53-14"></span>8. He, L., Liu, Y., Wang, L.: Intergrated segmentation and detection models for dentex challenge 2023. arXiv preprint  $arXiv:2308.14161$  (2023)
- <span id="page-53-6"></span>9. Kong, Z., Ouyang, H., Cao, Y., Huang, T., Ahn, E., Zhang, M., Liu, H.: Automated periodontitis bone loss diagnosis in panoramic radiographs using a bespoke twostage detector. Computers in Biology and Medicine 152, 106374 (2023)
- <span id="page-53-4"></span>10. Kumar, A., Bhadauria, H.S., Singh, A.: Descriptive analysis of dental x-ray images using various practical methods: A review. PeerJ Computer Science 7, e620 (2021)
- <span id="page-53-0"></span>11. Li, L.H., Zhang, P., Zhang, H., Yang, J., Li, C., Zhong, Y., Wang, L., Yuan, L., Zhang, L., Hwang, J.N., et al.: Grounded language-image pre-training. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 10965–10975 (2022)
- <span id="page-53-9"></span>12. Lin, S.Y., Chang, H.Y.: Tooth numbering and condition recognition on dental panoramic radiograph images using cnns. IEEE Access 9, 166008–166026 (2021)
- <span id="page-53-2"></span>13. Liu, J., Zhang, Y., Chen, J.N., Xiao, J., Lu, Y., A Landman, B., Yuan, Y., Yuille, A., Tang, Y., Zhou, Z.: Clip-driven universal model for organ segmentation and tumor detection. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV). pp. 21152–21164 (October 2023)
- <span id="page-53-12"></span>14. Liu, S., Zeng, Z., Ren, T., Li, F., Zhang, H., Yang, J., Li, C., Yang, J., Su, H., Zhu, J., et al.: Grounding dino: Marrying dino with grounded pre-training for open-set object detection. arXiv preprint [arXiv:2303.05499](http://arxiv.org/abs/2303.05499) (2023)
- <span id="page-53-7"></span>15. Mei, L., Fang, Y., Cui, Z., Deng, K., Wang, N., He, X., Zhan, Y., Zhou, X., Tonetti, M., Shen, D.: Hc-net: Hybrid classification network for automatic periodontal dis-

ease diagnosis. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 54–63. Springer (2023)

- <span id="page-54-6"></span>16. de Oliveira Capote, T.S., de Almeida Gonçalves, M., Gonçalves, A., Gonçalves, M.: Panoramic radiography-diagnosis of relevant structures that might compromise oral and general health of the patient. In: Emerging Trends in Oral Health Sciences and Dentistry. IntechOpen (2015)
- <span id="page-54-2"></span>17. Qin, Z., Yi, H.H., Lao, Q., Li, K.: Medical image understanding with pretrained vision language models: A comprehensive study. In: The Eleventh International Conference on Learning Representations (2022)
- <span id="page-54-0"></span>18. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-54-5"></span>19. Turosz, N., Chęcińska, K., Chęciński, M., Brzozowska, A., Nowak, Z., Sikora, M.: Applications of artificial intelligence in the analysis of dental panoramic radiographs: An overview of systematic reviews. Dentomaxillofacial Radiology  $52(7)$ , 20230284 (2023)
- <span id="page-54-9"></span>20. Van Wijk, A.J., Tan, S.P.: A numeric code for identifying patterns of human tooth agenesis: a new approach. European journal of oral sciences 114(2), 97–101 (2006)
- <span id="page-54-7"></span>21. Wang, X., Guo, J., Zhang, P., Chen, Q., Zhang, Z., Cao, Y., Fu, X., Liu, B.: A deep learning framework with pruning roi proposal for dental caries detection in panoramic x-ray images. In: International Conference on Neural Information Processing. pp. 524–536. Springer (2023)
- <span id="page-54-3"></span>22. Wu, Y., Zhou, Y., Saiyin, J., Wei, B., Lai, M., Shou, J., Fan, Y., Xu, Y.: Zero-shot nuclei detection via visual-language pre-trained models. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 693– 703. Springer (2023)
- <span id="page-54-4"></span>23. Yamada, Y., Tang, Y., Yildirim, I.: When are lemons purple? the concept association bias of clip. arXiv preprint [arXiv:2212.12043](http://arxiv.org/abs/2212.12043) (2022)
- <span id="page-54-8"></span>24. Yüksel, A.E., Gültekin, S., Simsar, E., Özdemir, Ş.D., Gündoğar, M., Tokgöz, S.B., Hamamcı, İ.E.: Dental enumeration and multiple treatment detection on panoramic x-rays using deep learning. Scientific reports 11(1), 12342 (2021)
- <span id="page-54-1"></span>25. Zhang, H., Li, F., Liu, S., Zhang, L., Su, H., Zhu, J., Ni, L., Shum, H.Y.: Dino: Detr with improved denoising anchor boxes for end-to-end object detection. In: The Eleventh International Conference on Learning Representations (2022)