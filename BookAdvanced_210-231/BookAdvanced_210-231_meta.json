{"table_of_contents": [{"title": "6\nKernel Smoothing Methods", "heading_level": null, "page_id": 0, "polygon": [[132.0, 108.75], [362.25, 108.75], [362.25, 162.615234375], [132.0, 162.615234375]]}, {"title": "6.1 One-Dimensional Kernel Smoothers", "heading_level": null, "page_id": 1, "polygon": [[132.0, 378.0], [385.5, 378.0], [385.5, 391.166015625], [132.0, 391.166015625]]}, {"title": "6.1.1 Local Linear Regression", "heading_level": null, "page_id": 3, "polygon": [[133.5, 585.0], [292.5, 585.0], [292.5, 596.70703125], [133.5, 596.70703125]]}, {"title": "6.1.2 Local Polynomial Regression", "heading_level": null, "page_id": 6, "polygon": [[133.5, 383.25], [316.5, 383.25], [316.5, 395.419921875], [133.5, 395.419921875]]}, {"title": "6.2 Selecting the Width of the Kernel", "heading_level": null, "page_id": 7, "polygon": [[132.0, 527.25], [375.626953125, 527.25], [375.626953125, 541.79296875], [132.0, 541.79296875]]}, {"title": "200 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 9, "polygon": [[132.75, 88.5], [294.0, 88.5], [294.0, 98.75830078125], [132.75, 98.75830078125]]}, {"title": "6.3 Local Regression in \\mathbb{R}^p", "heading_level": null, "page_id": 9, "polygon": [[132.0, 111.0], [309.287109375, 111.0], [309.287109375, 124.6201171875], [132.0, 124.6201171875]]}, {"title": "6.4 Structured Local Regression Models in \\mathbb{R}^p", "heading_level": null, "page_id": 10, "polygon": [[132.0, 565.5], [426.75, 565.5], [426.75, 578.91796875], [132.0, 578.91796875]]}, {"title": "6.4.1 Structured Kernels", "heading_level": null, "page_id": 12, "polygon": [[133.5, 112.5], [267.75, 112.5], [267.75, 123.4599609375], [133.5, 123.4599609375]]}, {"title": "6.4.2 Structured Regression Functions", "heading_level": null, "page_id": 12, "polygon": [[133.5, 396.0], [335.25, 396.0], [335.25, 407.21484375], [133.5, 407.21484375]]}, {"title": "6.5 Local Likelihood and Other Models", "heading_level": null, "page_id": 14, "polygon": [[132.0, 420.75], [384.75, 420.75], [384.75, 434.671875], [132.0, 434.671875]]}, {"title": "206 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 15, "polygon": [[132.0, 88.5], [294.0, 88.5], [294.0, 98.37158203125], [132.0, 98.37158203125]]}, {"title": "6.6 Kernel Density Estimation and Classification", "heading_level": null, "page_id": 17, "polygon": [[132.0, 415.5], [444.05859375, 415.5], [444.05859375, 429.64453125], [132.0, 429.64453125]]}, {"title": "6.6.1 Kernel Density Estimation", "heading_level": null, "page_id": 17, "polygon": [[133.5, 496.5], [309.0, 496.5], [309.0, 508.1484375], [133.5, 508.1484375]]}, {"title": "210 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 19, "polygon": [[131.783203125, 88.5], [294.0, 88.5], [294.0, 98.66162109375], [131.783203125, 98.66162109375]]}, {"title": "6.6.2 Kernel Density Classification", "heading_level": null, "page_id": 19, "polygon": [[133.5, 243.75], [321.0, 243.75], [321.0, 255.427734375], [133.5, 255.427734375]]}, {"title": "6.6.3 The Naive Bayes Classifier", "heading_level": null, "page_id": 19, "polygon": [[133.5, 609.0], [310.5, 609.0], [310.5, 619.5234375], [133.5, 619.5234375]]}, {"title": "212 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [293.748046875, 88.5], [293.748046875, 99.0], [132.0, 99.0]]}, {"title": "6.7 Radial Basis Functions and Kernels", "heading_level": null, "page_id": 21, "polygon": [[132.0, 111.0], [385.5, 111.0], [385.5, 123.943359375], [132.0, 123.943359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 26], ["Text", 3], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4286, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 555], ["Line", 135], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1054, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 58], ["Text", 5], ["Equation", 4], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 44], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 148], ["Text", 3], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 588], ["Line", 184], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2321, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["Line", 131], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1012, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 41], ["ListItem", 6], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 99], ["Text", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 816, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 51], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 34], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 648, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 74], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 864, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 50], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 75], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 869, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 41], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["ListItem", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 822, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 69], ["ListItem", 5], ["Equation", 4], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 52], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 36], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 61], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1748, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 40], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 52], ["Text", 4], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6210, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 70], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2024, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_210-231"}