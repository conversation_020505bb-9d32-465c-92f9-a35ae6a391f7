{"table_of_contents": [{"title": "Learning Generative Models with Sinkhorn Divergences", "heading_level": null, "page_id": 0, "polygon": [[123.0, 92.25], [513.38671875, 92.25], [513.38671875, 107.0244140625], [123.0, 107.0244140625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[166.5, 216.75], [218.5927734375, 216.75], [218.5927734375, 227.583984375], [166.5, 227.583984375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[326.25, 267.0], [422.25, 267.0], [422.25, 278.4375], [326.25, 278.4375]]}, {"title": "2 Minimum Kantorovich Estimation", "heading_level": null, "page_id": 2, "polygon": [[74.25, 72.75], [298.5, 72.75], [298.5, 83.91796875], [74.25, 83.91796875]]}, {"title": "3 Sinkhorn AutoDiff Algorithm", "heading_level": null, "page_id": 3, "polygon": [[326.021484375, 232.5], [522.0, 232.5], [522.0, 243.24609375], [326.021484375, 243.24609375]]}, {"title": "Algorithm 1 SGD with Auto-diff", "heading_level": null, "page_id": 5, "polygon": [[74.25, 251.25], [226.5, 251.25], [226.5, 262.58203125], [74.25, 262.58203125]]}, {"title": "4 Applications", "heading_level": null, "page_id": 5, "polygon": [[326.25, 73.5], [422.25, 73.5], [422.25, 84.44970703125], [326.25, 84.44970703125]]}, {"title": "4.1 Data Fitting with Ellipses.", "heading_level": null, "page_id": 5, "polygon": [[326.25, 285.75], [487.5, 285.75], [487.5, 296.033203125], [326.25, 296.033203125]]}, {"title": "4.2 Tuning a Generative Neural Network", "heading_level": null, "page_id": 6, "polygon": [[74.25, 671.25], [289.5, 671.25], [289.5, 681.78515625], [74.25, 681.78515625]]}, {"title": "Conclusion", "heading_level": null, "page_id": 7, "polygon": [[327.0, 692.25], [391.5, 692.25], [391.5, 703.0546875], [327.0, 703.0546875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[74.25, 198.75], [138.75, 198.75], [138.75, 210.375], [74.25, 210.375]]}, {"title": "A Numerical Exploration of the\nSinkhorn Divergence", "heading_level": null, "page_id": 9, "polygon": [[326.25, 495.0], [525.33984375, 495.0], [525.33984375, 518.9765625], [326.25, 518.9765625]]}, {"title": "A.1 Sample Complexity", "heading_level": null, "page_id": 9, "polygon": [[326.25, 534.0], [455.25, 534.0], [455.25, 544.5], [326.25, 544.5]]}, {"title": "A.2 Positivity", "heading_level": null, "page_id": 10, "polygon": [[326.25, 74.25], [405.0, 74.25], [405.0, 84.35302734375], [326.25, 84.35302734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 88], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3436, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 680], ["Line", 110], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 835, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1051], ["Line", 126], ["Equation", 7], ["Text", 7], ["TextInlineMath", 6], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 948], ["Line", 127], ["TextInlineMath", 7], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 890], ["Line", 129], ["TextInlineMath", 10], ["Equation", 6], ["Text", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 996], ["Line", 94], ["TextInlineMath", 6], ["Text", 3], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 602], ["Line", 90], ["TableCell", 40], ["TextInlineMath", 4], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3223, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 104], ["TableCell", 8], ["Text", 5], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1384, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 96], ["ListItem", 18], ["Reference", 18], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 109], ["ListItem", 18], ["Reference", 18], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 936], ["Line", 93], ["TextInlineMath", 6], ["Text", 6], ["ListItem", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 96], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2509, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Learning Generative Models with Sinkhorn Divergences"}