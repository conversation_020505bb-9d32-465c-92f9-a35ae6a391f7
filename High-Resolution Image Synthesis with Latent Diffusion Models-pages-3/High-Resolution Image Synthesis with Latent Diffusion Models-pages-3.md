# Appendix

<span id="page-0-0"></span>Image /page/0/Picture/1 description: The image displays three distinct landscape scenes. The top panel features a misty, mountainous terrain with dark evergreen trees in the foreground and a sharp, snow-capped peak in the distance. The middle panel shows a serene beach at sunset, with calm water reflecting the warm hues of the sky. The bottom panel presents a tranquil lake bordered by lush green grass and dense forests, with a large, cloud-shrouded mountain dominating the background.

Figure 12. Convolutional samples from the semantic landscapes model as in Sec. 4.3.2, finetuned on  $512^2$  images.

<span id="page-1-0"></span>Image /page/1/Picture/0 description: The image is a painting in the Cubist style, depicting the Last Supper. The figures are rendered with sharp angles and geometric shapes, using a palette of bold colors including red, blue, yellow, green, and black, outlined with thick black lines. The composition shows a long table with figures seated around it, their faces and bodies fragmented and reassembled in a characteristic Cubist manner. The overall impression is a dynamic and abstract interpretation of the biblical scene.

*'An oil painting of a latent space.'*

*'An epic painting of <PERSON><PERSON><PERSON> the Black summoning thunder and lightning in the mountains.'*

Image /page/1/Picture/3 description: The image displays two distinct scenes. The left side depicts a cosmic landscape with two glowing yellow celestial bodies against a backdrop of stars and swirling blue and red nebulae. A metallic, complex structure, possibly a spacecraft, is visible in the foreground, partially obscured by the nebulae. The right side of the image presents a dramatic scene of a lone figure standing on a rocky, barren landscape. The figure is cloaked and appears to be holding a staff or pole. Above, a powerful lightning storm illuminates the sky with bright, jagged bolts of electricity, casting an eerie glow on the surroundings.

*'A sunset over a mountain range, vector image.'*

Image /page/1/Picture/5 description: A vector image depicts a mountain range at sunset. The sky transitions from a deep purple at the top to a vibrant orange and yellow near the horizon. Two suns are visible: a large, bright sun on the left, partially obscured by a mountain peak, and a smaller, setting sun on the right, peeking through a gap between mountains. Silhouetted mountains in shades of dark purple and blue fill the foreground and midground, with layers of hazy, lighter purple mountains in the distance. Wispy clouds are scattered across the sky.

Figure 13. Combining classifier free diffusion guidance with the convolutional sampling strategy from Sec. 4.3.2, our 1.45B parameter text-to-image model can be used for rendering images larger than the native  $256<sup>2</sup>$  resolution the model was trained on.

## A. Changelog

Here we list changes between this version (<https://arxiv.org/abs/2112.10752v2>) of the paper and the previous version, *i.e*. <https://arxiv.org/abs/2112.10752v1>.

- We updated the results on text-to-image synthesis in Sec. 4.3 which were obtained by training a new, larger model (1.45B parameters). This also includes a new comparison to very recent competing methods on this task that were published on arXiv at the same time as  $(59, 109)$  or after  $(26)$  the publication of our work.
- We updated results on class-conditional synthesis on ImageNet in Sec. 4.1, Tab. 3 (see also Sec. [D.4\)](#page-6-0) obtained by retraining the model with a larger batch size. The corresponding qualitative results in Fig. 26 and Fig. 27 were also updated. Both the updated text-to-image and the class-conditional model now use classifier-free guidance  $[32]$  as a measure to increase visual fidelity.
- We conducted a user study (following the scheme suggested by Saharia et al [72]) which provides additional evaluation for our inpainting (Sec. 4.5) and superresolution models (Sec. 4.4).
- Added Fig. 5 to the main paper, moved Fig. [18](#page-9-0) to the appendix, added Fig. [13](#page-1-0) to the appendix.

## B. Detailed Information on Denoising Diffusion Models

Diffusion models can be specified in terms of a signal-to-noise ratio SNR(t) =  $\frac{\alpha_t^2}{\sigma_t^2}$  consisting of sequences  $(\alpha_t)_{t=1}^T$  and  $(\sigma_t)_{t=1}^T$  which, starting from a data sample  $x_0$ , define a forward diffusion process q as

$$
q(x_t|x_0) = \mathcal{N}(x_t|\alpha_t x_0, \sigma_t^2 \mathbb{I})
$$
\n(4)

with the Markov structure for  $s < t$ :

$$
q(x_t|x_s) = \mathcal{N}(x_t|\alpha_{t|s}x_s, \sigma_{t|s}^2 \mathbb{I})
$$
\n(5)

$$
\alpha_{t|s} = \frac{\alpha_t}{\alpha_s} \tag{6}
$$

$$
\sigma_{t|s}^2 = \sigma_t^2 - \alpha_{t|s}^2 \sigma_s^2 \tag{7}
$$

Denoising diffusion models are generative models  $p(x_0)$  which revert this process with a similar Markov structure running backward in time, *i.e*. they are specified as

$$
p(x_0) = \int_z p(x_T) \prod_{t=1}^T p(x_{t-1}|x_t)
$$
\n(8)

The evidence lower bound (ELBO) associated with this model then decomposes over the discrete time steps as

$$
-\log p(x_0) \leq \mathbb{KL}(q(x_T|x_0)|p(x_T)) + \sum_{t=1}^T \mathbb{E}_{q(x_t|x_0)}\mathbb{KL}(q(x_{t-1}|x_t,x_0)|p(x_{t-1}|x_t))
$$
\n(9)

The prior  $p(x_T)$  is typically choosen as a standard normal distribution and the first term of the ELBO then depends only on the final signal-to-noise ratio SNR(T). To minimize the remaining terms, a common choice to parameterize  $p(x_{t-1}|x_t)$  is to specify it in terms of the true posterior  $q(x_{t-1}|x_t, x_0)$  but with the unknown  $x_0$  replaced by an estimate  $x_{\theta}(x_t, t)$  based on the current step  $x_t$ . This gives [45]

$$
p(x_{t-1}|x_t) := q(x_{t-1}|x_t, x_\theta(x_t, t))\tag{10}
$$

$$
= \mathcal{N}(x_{t-1}|\mu_{\theta}(x_t, t), \sigma_{t|t-1}^2 \frac{\sigma_{t-1}^2}{\sigma_t^2} \mathbb{I}), \tag{11}
$$

where the mean can be expressed as

$$
\mu_{\theta}(x_t, t) = \frac{\alpha_{t|t-1}\sigma_{t-1}^2}{\sigma_t^2}x_t + \frac{\alpha_{t-1}\sigma_{t|t-1}^2}{\sigma_t^2}x_{\theta}(x_t, t).
$$
\n(12)

In this case, the sum of the ELBO simplify to

$$
\sum_{t=1}^{T} \mathbb{E}_{q(x_t|x_0)} \mathbb{KL}(q(x_{t-1}|x_t, x_0) | p(x_{t-1}) = \sum_{t=1}^{T} \mathbb{E}_{\mathcal{N}(\epsilon|0, \mathbb{I})} \frac{1}{2} (\text{SNR}(t-1) - \text{SNR}(t)) \| x_0 - x_\theta(\alpha_t x_0 + \sigma_t \epsilon, t) \|^2 \tag{13}
$$

Following [30], we use the reparameterization

$$
\epsilon_{\theta}(x_t, t) = (x_t - \alpha_t x_{\theta}(x_t, t))/\sigma_t \tag{14}
$$

to express the reconstruction term as a denoising objective,

$$
||x_0 - x_\theta(\alpha_t x_0 + \sigma_t \epsilon, t)||^2 = \frac{\sigma_t^2}{\alpha_t^2} ||\epsilon - \epsilon_\theta(\alpha_t x_0 + \sigma_t \epsilon, t)||^2
$$
\n(15)

and the reweighting, which assigns each of the terms the same weight and results in Eq. (1).

<span id="page-4-1"></span>

## C. Image Guiding Mechanisms

<span id="page-4-0"></span>Image /page/4/Picture/1 description: The image displays a grid of landscape photographs, organized into three columns labeled 'Samples 256²', 'Guided Convolutional Samples 512²', and 'Convolutional Samples 512²'. The first row shows a mountain range with a grassy field in the foreground under a dark sky, a similar scene with the mountains silhouetted against a bright sky and a grassy field, and a textured, abstract pattern. The second row features a snowy mountain landscape with clouds, a misty mountain valley under a clear blue sky, and a reflection of mountains and trees in a calm lake. The third row presents a green hillside sloping down to a body of water under a blue sky, and a dense, green hillside with vegetation. The text below the images states that 'On landscapes, convolutional sampling with unconditional models can lead to homogeneous and incoherent global'.

Figure 14. On landscapes, convolutional sampling with unconditional models can lead to homogeneous and incoherent global structures (see column 2).  $L_2$ -guiding with a low resolution image can help to reestablish coherent global structures.

An intriguing feature of diffusion models is that unconditional models can be conditioned at test-time [15, 82, 85]. In particular, [15] presented an algorithm to guide both unconditional and conditional models trained on the ImageNet dataset with a classifier  $\log p_{\Phi}(y|x_t)$ , trained on each  $x_t$  of the diffusion process. We directly build on this formulation and introduce post-hoc *image-guiding*:

For an epsilon-parameterized model with fixed variance, the guiding algorithm as introduced in [15] reads:

$$
\hat{\epsilon} \leftarrow \epsilon_{\theta}(z_t, t) + \sqrt{1 - \alpha_t^2} \nabla_{z_t} \log p_{\Phi}(y|z_t).
$$
\n(16)

This can be interpreted as an update correcting the "score"  $\epsilon_{\theta}$  with a conditional distribution log  $p_{\Phi}(y|z_t)$ .

So far, this scenario has only been applied to single-class classification models. We re-interpret the guiding distribution  $p_{\Phi}(y|T(\mathcal{D}(z_0(z_t))))$  as a general purpose image-to-image translation task given a target image y, where T can be any differentiable transformation adopted to the image-to-image translation task at hand, such as the identity, a downsampling operation or similar.

As an example, we can assume a Gaussian guider with fixed variance  $\sigma^2 = 1$ , such that

$$
\log p_{\Phi}(y|z_t) = -\frac{1}{2} ||y - T(\mathcal{D}(z_0(z_t)))||_2^2
$$
\n(17)

becomes a  $L_2$  regression objective.

Fig. [14](#page-4-0) demonstrates how this formulation can serve as an upsampling mechanism of an unconditional model trained on 256<sup>2</sup> images, where unconditional samples of size 256<sup>2</sup> guide the convolutional synthesis of 512<sup>2</sup> images and T is a 2× bicubic downsampling. Following this motivation, we also experiment with a perceptual similarity guiding and replace the  $L_2$  objective with the LPIPS  $[106]$  metric, see Sec. 4.4.

## D. Additional Results

<span id="page-6-1"></span>

### D.1. Choosing the Signal-to-Noise Ratio for High-Resolution Synthesis

<span id="page-6-2"></span>Image /page/6/Picture/2 description: This image is a grid of nine landscape photographs, arranged in three columns and three rows. The columns are labeled: "KL-reg, w/o rescaling", "KL-reg, w/ rescaling", and "VQ-reg, w/o rescaling". The top row shows three different lake scenes with mountains in the background under clear blue skies. The middle row displays three dramatic sunset or sunrise scenes over bodies of water, with vibrant orange and red skies. The bottom row features three aerial views of mountainous terrain partially covered in clouds, with bodies of water visible below.

Figure 15. Illustrating the effect of latent space rescaling on convolutional sampling, here for semantic image synthesis on landscapes. See Sec. 4.3.2 and Sec. [D.1.](#page-6-1)

As discussed in Sec. 4.3.2, the signal-to-noise ratio induced by the variance of the latent space (*i.e.* Var( $z/\sigma_t^2$ ) significantly affects the results for convolutional sampling. For example, when training a LDM directly in the latent space of a KLregularized model (see Tab. [8\)](#page-7-0), this ratio is very high, such that the model allocates a lot of semantic detail early on in the reverse denoising process. In contrast, when rescaling the latent space by the component-wise standard deviation of the latents as described in Sec. [G,](#page-15-0) the SNR is descreased. We illustrate the effect on convolutional sampling for semantic image synthesis in Fig. [15.](#page-6-2) Note that the VQ-regularized space has a variance close to 1, such that it does not have to be rescaled.

### D.2. Full List of all First Stage Models

We provide a complete list of various autoenconding models trained on the OpenImages dataset in Tab. [8.](#page-7-0)

### D.3. Layout-to-Image Synthesis

Here we provide the quantitative evaluation and additional samples for our layout-to-image models from Sec. 4.3.1. We train a model on the COCO [4] and one on the OpenImages [49] dataset, which we subsequently additionally finetune on COCO. Tab [9](#page-8-0) shows the result. Our COCO model reaches the performance of recent state-of-the art models in layout-toimage synthesis, when following their training and evaluation protocol [89]. When finetuning from the OpenImages model, we surpass these works. Our OpenImages model surpasses the results of Jahn et al [37] by a margin of nearly 11 in terms of FID. In Fig. [16](#page-7-1) we show additional samples of the model finetuned on COCO.

<span id="page-6-0"></span>

### D.4. Class-Conditional Image Synthesis on ImageNet

Tab. [10](#page-8-1) contains the results for our class-conditional LDM measured in FID and Inception score (IS). LDM-8 requires significantly fewer parameters and compute requirements (see Tab. [18\)](#page-14-0) to achieve very competitive performance. Similar to previous work, we can further boost the performance by training a classifier on each noise scale and guiding with it,

<span id="page-7-0"></span>

| f             | $ \mathcal{Z} $ | c   | R-FID $\downarrow$ | <b>R-IS</b> $\uparrow$ | <b>PSNR</b> $\uparrow$ | <b>PSIM</b> $\downarrow$ | SSIM $\uparrow$ |
|---------------|-----------------|-----|--------------------|------------------------|------------------------|--------------------------|-----------------|
| 16 VQGAN [23] | 16384           | 256 | 4.98               |                        | $19.9 \pm 3.4$         | $1.83 \pm 0.42$          | $0.51 \pm 0.18$ |
| 16 VQGAN [23] | 1024            | 256 | 7.94               |                        | $19.4 \pm 3.3$         | $1.98 \pm 0.43$          | $0.50 \pm 0.18$ |
| 8 DALL-E [66] | 8192            |     | 32.01              |                        | $22.8 \pm 2.1$         | $1.95 \pm 0.51$          | $0.73 \pm 0.13$ |
| 32            | 16384           | 16  | 31.83              | $40.40 \pm 1.07$       | $17.45 \pm 2.90$       | $2.58 \pm 0.48$          | $0.41 \pm 0.18$ |
| 16            | 16384           | 8   | 5.15               | $144.55 \pm 3.74$      | $20.83 \pm 3.61$       | $1.73 \pm 0.43$          | $0.54 \pm 0.18$ |
| 8             | 16384           | $4$ | 1.14               | $201.92 \pm 3.97$      | $23.07 \pm 3.99$       | $1.17 \pm 0.36$          | $0.65 \pm 0.16$ |
| 8             | 256             | $4$ | 1.49               | $194.20 \pm 3.87$      | $22.35 \pm 3.81$       | $1.26 \pm 0.37$          | $0.62 \pm 0.16$ |
| $4$           | 8192            | 3   | 0.58               | $224.78 \pm 5.35$      | $27.43 \pm 4.26$       | $0.53 \pm 0.21$          | $0.82 \pm 0.10$ |
| 4†            | 8192            | 3   | 1.06               | $221.94 \pm 4.58$      | $25.21 \pm 4.17$       | $0.72 \pm 0.26$          | $0.76 \pm 0.12$ |
| 4             | 256             | 3   | 0.47               | $223.81 \pm 4.58$      | $26.43 \pm 4.22$       | $0.62 \pm 0.24$          | $0.80 \pm 0.11$ |
| $2$           | 2048            | $c$ | 0.16               | $232.75 \pm 5.09$      | $30.85 \pm 4.12$       | $0.27 \pm 0.12$          | $0.91 \pm 0.05$ |
| $2$           | 64              | $2$ | 0.40               | $226.62 \pm 4.83$      | $29.13 \pm 3.46$       | $0.38 \pm 0.13$          | $0.90 \pm 0.05$ |
| 32            | KL              | 64  | 2.04               | $189.53 \pm 3.68$      | $22.27 \pm 3.93$       | $1.41 \pm 0.40$          | $0.61 \pm 0.17$ |
| 32            | KL              | 16  | 7.3                | $132.75 \pm 2.71$      | $20.38 \pm 3.56$       | $1.88 \pm 0.45$          | $0.53 \pm 0.18$ |
| 16            | KL              | 16  | 0.87               | $210.31 \pm 3.97$      | $24.08 \pm 4.22$       | $1.07 \pm 0.36$          | $0.68 \pm 0.15$ |
| 16            | KL              | 8   | 2.63               | $178.68 \pm 4.08$      | $21.94 \pm 3.92$       | $1.49 \pm 0.42$          | $0.59 \pm 0.17$ |
| 8             | KL              | $4$ | 0.90               | $209.90 \pm 4.92$      | $24.19 \pm 4.19$       | $1.02 \pm 0.35$          | $0.69 \pm 0.15$ |
| $4$           | KL              | 3   | 0.27               | $227.57 \pm 4.89$      | $27.53 \pm 4.54$       | $0.55 \pm 0.24$          | $0.82 \pm 0.11$ |
| $2$           | KL              | $2$ | 0.086              | $232.66 \pm 5.16$      | $32.47 \pm 4.19$       | $0.20 \pm 0.09$          | $0.93 \pm 0.04$ |

Table 8. Complete autoencoder zoo trained on OpenImages, evaluated on ImageNet-Val. † denotes an attention-free autoencoder.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: This is a grid of images demonstrating layout-to-image synthesis on the COCO dataset. The top row shows bounding box layouts with labels for objects like pizza, traffic light, wall, cabinet, shelf, floor, horse, fence, grass, and sink. The subsequent rows display corresponding synthesized images for each layout. The images feature various scenes including pizzas, city streets with traffic lights, modern kitchens, people riding horses, giraffes in a natural habitat, elephants, and people interacting with their environment.

Figure 16. More samples from our best model for layout-to-image synthesis, *LDM-4*, which was trained on the OpenImages dataset and finetuned on the COCO dataset. Samples generated with 100 DDIM steps and  $\eta = 0$ . Layouts are from the COCO validation set.

see Sec. [C.](#page-4-1) Unlike the pixel-based methods, this classifier is trained very cheaply in latent space. For additional qualitative results, see Fig. 26 and Fig. 27.

<span id="page-8-0"></span>

| Method                                             | COCO256 × 256    | OpenImages 256 × 256 | OpenImages 512 × 512 |
|----------------------------------------------------|------------------|----------------------|----------------------|
|                                                    | FID↓             | FID↓                 | FID↓                 |
| LostGAN-V2 [87]                                    | 42.55            | -                    | -                    |
| OC-GAN [89]                                        | 41.65            | -                    | -                    |
| SPADE [62]                                         | 41.11            | -                    | -                    |
| VQGAN+T [37]                                       | 56.58            | 45.33                | 48.11                |
| LDM-8 (100 steps, ours)<br>LDM-4 (200 steps, ours) | 42.06†<br>40.91* | -<br>32.02           | -<br>35.80           |

Table 9. Quantitative comparison of our layout-to-image models on the COCO [4] and OpenImages [49] datasets. <sup>†</sup>: Training from scratch on COCO; \*: Finetuning from OpenImages.

<span id="page-8-1"></span>

| Method            | FID↓  | IS↑         | Precision↑  | Recall↑     | Nparams |                                                                                           |
|-------------------|-------|-------------|-------------|-------------|---------|-------------------------------------------------------------------------------------------|
| SR3 [72]          | 11.30 | -           | -           | -           | 625M    | -                                                                                         |
| ImageBART [21]    | 21.19 | -           | -           | -           | 3.5B    | -                                                                                         |
| ImageBART [21]    | 7.44  | -           | -           | -           | 3.5B    | 0.05 acc. rate*                                                                           |
| VQGAN+T [23]      | 17.04 | 70.6±1.8    | -           | -           | 1.3B    | -                                                                                         |
| VQGAN+T [23]      | 5.88  | 304.8±3.6   | -           | -           | 1.3B    | 0.05 acc. rate*                                                                           |
| BigGan-deep [3]   | 6.95  | 203.6±2.6   | 0.87        | 0.28        | 340M    | -                                                                                         |
| ADM [15]          | 10.94 | 100.98      | 0.69        | 0.63        | 554M    | 250 DDIM steps                                                                            |
| ADM-G [15]        | 4.59  | 186.7       | 0.82        | 0.52        | 608M    | 250 DDIM steps                                                                            |
| ADM-G, ADM-U [15] | 3.85  | 221.72      | 0.84        | 0.53        | n/a     | 2 × 250 DDIM steps                                                                        |
| CDM [31]          | 4.88  | 158.71±2.26 | -           | -           | n/a     | 2 × 100 DDIM steps                                                                        |
| LDM-8 (ours)      | 17.41 | 72.92±2.6   | 0.65        | 0.62        | 395M    | 200 DDIM steps, 2.9M train steps, batch size 64                                           |
| LDM-8-G (ours)    | 8.11  | 190.43±2.60 | 0.83        | 0.36        | 506M    | 200 DDIM steps, classifier scale 10, 2.9M train steps, batch size 64                      |
| LDM-8 (ours)      | 15.51 | 79.03±1.03  | 0.65        | <b>0.63</b> | 395M    | 200 DDIM steps, 4.8M train steps, batch size 64                                           |
| LDM-8-G (ours)    | 7.76  | 209.52±4.24 | 0.84        | 0.35        | 506M    | 200 DDIM steps, classifier scale 10, 4.8M train steps, batch size 64                      |
| LDM-4 (ours)      | 10.56 | 103.49±1.24 | 0.71        | 0.62        | 400M    | 250 DDIM steps, 178K train steps, batch size 1200                                         |
| LDM-4-G (ours)    | 3.95  | 178.22±2.43 | 0.81        | 0.55        | 400M    | 250 DDIM steps, unconditional guidance [32] scale 1.25, 178K train steps, batch size 1200 |
| LDM-4-G (ours)    | 3.60  | 247.67±5.59 | <b>0.87</b> | 0.48        | 400M    | 250 DDIM steps, unconditional guidance [32] scale 1.5, 178K train steps, batch size 1200  |

Table 10. Comparison of a class-conditional ImageNet *LDM* with recent state-of-the-art methods for class-conditional image generation on the ImageNet [12] dataset.<sup>∗</sup>: Classifier rejection sampling with the given rejection rate as proposed in [67].

### D.5. Sample Quality vs. V100 Days (Continued from Sec. 4.1)

<span id="page-8-2"></span>Image /page/8/Figure/5 description: The image contains two line graphs side-by-side. The left graph is titled "FID vs. V100 days" and plots FID on the y-axis against V100 days on the x-axis. The right graph is titled "Inception Score vs. V100 days" and plots Inception Score on the y-axis against V100 days on the x-axis. Both graphs show data for six different models labeled LDM-1, LDM-2, LDM-4, LDM-8, LDM-16, and LDM-32, distinguished by different colored lines. In the left graph, FID generally decreases as V100 days increase, with LDM-16 showing the lowest FID values. In the right graph, Inception Score generally increases as V100 days increase, with LDM-4 and LDM-16 showing the highest Inception Scores.

Figure 17. For completeness we also report the training progress of class-conditional *LDMs* on the ImageNet dataset for a fixed number of 35 V100 days. Results obtained with 100 DDIM steps [84] and  $\kappa = 0$ . FIDs computed on 5000 samples for efficiency reasons.

For the assessment of sample quality over the training progress in Sec. 4.1, we reported FID and IS scores as a function of train steps. Another possibility is to report these metrics over the used resources in V100 days. Such an analysis is additionally provided in Fig. [17,](#page-8-2) showing qualitatively similar results.

<span id="page-9-2"></span>

| Method                                  | FID <span style="vertical-align:sub">↓</span>                                                    | IS <span style="vertical-align:sub">↑</span>          | PSNR <span style="vertical-align:sub">↑</span>     | SSIM <span style="vertical-align:sub">↑</span>      |
|-----------------------------------------|--------------------------------------------------------------------------------------------------|-------------------------------------------------------|----------------------------------------------------|-----------------------------------------------------|
| Image Regression [72]                   | 15.2                                                                                             | 121.1                                                 | <b>27.9</b>                                        | <b>0.801</b>                                        |
| SR3 [72]                                | 5.2                                                                                              | <b>180.1</b>                                          | 26.4                                               | 0.762                                               |
| <i>LDM-4</i> (ours, 100 steps)          | <b>2.8<span style="vertical-align:sub">†</span>/4.8<span style="vertical-align:sub">†</span></b> | 166.3                                                 | 24.4 <span style="vertical-align:sub">±</span> 3.8 | 0.69 <span style="vertical-align:sub">±</span> 0.14 |
| <i>LDM-4</i> (ours, 50 steps, guiding)  | <b>4.4<span style="vertical-align:sub">†</span>/6.4<span style="vertical-align:sub">†</span></b> | 153.7                                                 | 25.8 <span style="vertical-align:sub">±</span> 3.7 | 0.74 <span style="vertical-align:sub">±</span> 0.12 |
| <i>LDM-4</i> (ours, 100 steps, guiding) | <b>4.4<span style="vertical-align:sub">†</span>/6.4<span style="vertical-align:sub">‡</span></b> | 154.1                                                 | 25.7 <span style="vertical-align:sub">±</span> 3.7 | 0.73 <span style="vertical-align:sub">±</span> 0.12 |
| <i>LDM-4</i> (ours, 100 steps, +15 ep.) | <b>2.6<span style="vertical-align:sub">†</span>/4.6<span style="vertical-align:sub">‡</span></b> | 169.76 <span style="vertical-align:sub">±</span> 5.03 | 24.4 <span style="vertical-align:sub">±</span> 3.8 | 0.69 <span style="vertical-align:sub">±</span> 0.14 |
| Pixel-DM (100 steps, +15 ep.)           | <b>5.1<span style="vertical-align:sub">†</span>/7.1<span style="vertical-align:sub">‡</span></b> | 163.06 <span style="vertical-align:sub">±</span> 4.67 | 24.1 <span style="vertical-align:sub">±</span> 3.3 | 0.59 <span style="vertical-align:sub">±</span> 0.12 |

Table 11.  $\times$ 4 upscaling results on ImageNet-Val. (256<sup>2</sup>); <sup>†</sup>: FID features computed on validation split, <sup>‡</sup>: FID features computed on train split. We also include a pixel-space baseline that receives the same amount of compute as *LDM-4*. The last two rows received 15 epochs of additional training compared to the former results.

#### D.6. Super-Resolution

For better comparability between LDMs and diffusion models in pixel space, we extend our analysis from Tab. 5 by comparing a diffusion model trained for the same number of steps and with a comparable number  $\frac{1}{1}$  $\frac{1}{1}$  $\frac{1}{1}$  of parameters to our LDM. The results of this comparison are shown in the last two rows of Tab. [11](#page-9-2) and demonstrate that LDM achieves better performance while allowing for significantly faster sampling. A qualitative comparison is given in Fig. 20 which shows random samples from both LDM and the diffusion model in pixel space.

##### D.6.1 LDM-BSR: General Purpose SR Model via Diverse Image Degradation

<span id="page-9-0"></span>Image /page/9/Figure/5 description: This image displays a comparison of image super-resolution techniques. The top row shows close-ups of a dog's face, with the first image labeled "bicubic", the second "LDM-SR", and the third "LDM-BSR". The bottom row shows close-ups of a fox's face, with the first image labeled "bicubic", the second "LDM-SR", and the third "LDM-BSR". The images demonstrate the visual differences in detail and clarity achieved by each method.

Figure 18. *LDM-BSR* generalizes to arbitrary inputs and can be used as a general-purpose upsampler, upscaling samples from a classconditional *LDM* (image *cf*. Fig. 4) to 1024<sup>2</sup> resolution. In contrast, using a fixed degradation process (see Sec. 4.4) hinders generalization.

To evaluate generalization of our LDM-SR, we apply it both on synthetic LDM samples from a class-conditional ImageNet model (Sec. 4.1) and images crawled from the internet. Interestingly, we observe that LDM-SR, trained only with a bicubicly downsampled conditioning as in [72], does not generalize well to images which do not follow this pre-processing. Hence, to obtain a superresolution model for a wide range of real world images, which can contain complex superpositions of camera noise, compression artifacts, blurr and interpolations, we replace the bicubic downsampling operation in LDM-SR with the degration pipeline from [105]. The BSR-degradation process is a degradation pipline which applies JPEG compressions noise, camera sensor noise, different image interpolations for downsampling, Gaussian blur kernels and Gaussian noise in a random order to an image. We found that using the bsr-degredation process with the original parameters as in  $[105]$  leads to a very strong degradation process. Since a more moderate degradation process seemed apppropiate for our application, we adapted the parameters of the bsr-degradation (our adapted degradation process can be found in our code base at [https:](https://github.com/CompVis/latent-diffusion) [//github.com/CompVis/latent-diffusion](https://github.com/CompVis/latent-diffusion)). Fig. [18](#page-9-0) illustrates the effectiveness of this approach by directly comparing *LDM-SR* with *LDM-BSR*. The latter produces images much sharper than the models confined to a fixed preprocessing, making it suitable for real-world applications. Further results of *LDM-BSR* are shown on LSUN-cows in Fig. 19.

<span id="page-9-1"></span><sup>&</sup>lt;sup>1</sup>It is not possible to exactly match both architectures since the diffusion model operates in the pixel space

## E. Implementation Details and Hyperparameters

### E.1. Hyperparameters

<span id="page-10-0"></span>We provide an overview of the hyperparameters of all trained *LDM* models in Tab. [12,](#page-10-0) Tab. [13,](#page-10-1) Tab. [14](#page-11-0) and Tab. [15.](#page-11-1)

|                       | CelebA-HQ $256 	imes 256$ | FFHQ 256 $\times$ 256 | LSUN-Churches $256 	imes 256$ | LSUN-Bedrooms $256 	imes 256$ |
|-----------------------|---------------------------|-----------------------|-------------------------------|-------------------------------|
| $f$                   | 4                         | 4                     | 8                             | 4                             |
| $z$ -shape            | $64 	imes 64 	imes 3$     | $64 	imes 64 	imes 3$ | -                             | $64 	imes 64 	imes 3$         |
| $ \mathcal{Z} $       | 8192                      | 8192                  | -                             | 8192                          |
| Diffusion steps       | 1000                      | 1000                  | 1000                          | 1000                          |
| Noise Schedule        | linear                    | linear                | linear                        | linear                        |
| $N_{\text{params}}$   | 274M                      | 274M                  | 294M                          | 274M                          |
| Channels              | 224                       | 224                   | 192                           | 224                           |
| Depth                 | 2                         | 2                     | 2                             | 2                             |
| Channel Multiplier    | 1,2,3,4                   | 1,2,3,4               | 1,2,2,4,4                     | 1,2,3,4                       |
| Attention resolutions | 32, 16, 8                 | 32, 16, 8             | 32, 16, 8, 4                  | 32, 16, 8                     |
| Head Channels         | 32                        | 32                    | 24                            | 32                            |
| <b>Batch Size</b>     | 48                        | 42                    | 96                            | 48                            |
| Iterations*           | 410k                      | 635k                  | 500k                          | 1.9M                          |
| Learning Rate         | $9.6e-5$                  | $8.4e-5$              | $5.e-5$                       | $9.6e-5$                      |

<span id="page-10-1"></span>Table 12. Hyperparameters for the unconditional *LDMs* producing the numbers shown in Tab. 1. All models trained on a single NVIDIA A100.

|                     | LDM-1                   | LDM-2                   | LDM-4                 | LDM-8                 | LDM-16                | LDM-32                |
|---------------------|-------------------------|-------------------------|-----------------------|-----------------------|-----------------------|-----------------------|
| z-shape             | $256 	imes 256 	imes 3$ | $128 	imes 128 	imes 2$ | $64 	imes 64 	imes 3$ | $32 	imes 32 	imes 4$ | $16 	imes 16 	imes 8$ | $88 	imes 8 	imes 32$ |
| $  Z $              | -                       | 2048                    | 8192                  | 16384                 | 16384                 | 16384                 |
| Diffusion steps     | 1000                    | 1000                    | 1000                  | 1000                  | 1000                  | 1000                  |
| Noise Schedule      | linear                  | linear                  | linear                | linear                | linear                | linear                |
| Model Size          | 396M                    | 391M                    | 391M                  | 395M                  | 395M                  | 395M                  |
| Channels            | 192                     | 192                     | 192                   | 256                   | 256                   | 256                   |
| Depth               | 2                       | 2                       | 2                     | 2                     | 2                     | 2                     |
| Channel Multiplier  | 1,1,2,2,4,4             | 1,2,2,4,4               | 1,2,3,5               | 1,2,4                 | 1,2,4                 | 1,2,4                 |
| Number of Heads     | 1                       | 1                       | 1                     | 1                     | 1                     | 1                     |
| Batch Size          | 7                       | 9                       | 40                    | 64                    | 112                   | 112                   |
| Iterations          | 2M                      | 2M                      | 2M                    | 2M                    | 2M                    | 2M                    |
| Learning Rate       | $4.9e-5$                | $6.3e-5$                | $8e-5$                | $6.4e-5$              | $4.5e-5$              | $4.5e-5$              |
| Conditioning        | CA                      | CA                      | CA                    | CA                    | CA                    | CA                    |
| CA-resolutions      | 32, 16, 8               | 32, 16, 8               | 32, 16, 8             | 32, 16, 8             | 16, 8, 4              | 8, 4, 2               |
| Embedding Dimension | 512                     | 512                     | 512                   | 512                   | 512                   | 512                   |
| Transformers Depth  | 1                       | 1                       | 1                     | 1                     | 1                     | 1                     |

Table 13. Hyperparameters for the conditional *LDMs* trained on the ImageNet dataset for the analysis in Sec. 4.1. All models trained on a single NVIDIA A100.

### E.2. Implementation Details

<span id="page-10-3"></span>

#### E.2.1 Implementations of $\tau_\theta$ for conditional *LDMs*

For the experiments on text-to-image and layout-to-image (Sec. 4.3.1) synthesis, we implement the conditioner  $\tau_{\theta}$  as an unmasked transformer which processes a tokenized version of the input y and produces an output  $\zeta := \tau_{\theta}(y)$ , where  $\zeta \in$  $\mathbb{R}^{M \times d_{\tau}}$ . More specifically, the transformer is implemented from N transformer blocks consisting of global self-attention layers, layer-normalization and position-wise MLPs as follows<sup>[2](#page-10-2)</sup>:

<span id="page-10-2"></span><sup>2</sup> adapted from <https://github.com/lucidrains/x-transformers>

<span id="page-11-0"></span>

|                       | LDM-1                     | LDM-2                     | LDM-4                   | LDM-8                   | LDM-16                  | LDM-32                  |
|-----------------------|---------------------------|---------------------------|-------------------------|-------------------------|-------------------------|-------------------------|
| z-shape               | $256 \times 256 \times 3$ | $128 \times 128 \times 2$ | $64 \times 64 \times 3$ | $32 \times 32 \times 4$ | $16 \times 16 \times 8$ | $88 \times 8 \times 32$ |
| $ Z $                 | -                         | $2048$                    | $8192$                  | $16384$                 | $16384$                 | $16384$                 |
| Diffusion steps       | $1000$                    | $1000$                    | $1000$                  | $1000$                  | $1000$                  | $1000$                  |
| Noise Schedule        | linear                    | linear                    | linear                  | linear                  | linear                  | linear                  |
| Model Size            | $270M$                    | $265M$                    | $274M$                  | $258M$                  | $260M$                  | $258M$                  |
| <b>Channels</b>       | $192$                     | $192$                     | $224$                   | $256$                   | $256$                   | $256$                   |
| Depth                 | 2                         | 2                         | 2                       | 2                       | 2                       | 2                       |
| Channel Multiplier    | $1, 1, 2, 2, 4, 4$        | $1,2,2,4,4$               | $1,2,3,4$               | $1,2,4$                 | $1,2,4$                 | $1,2,4$                 |
| Attention resolutions | $32, 16, 8$               | $32, 16, 8$               | $32, 16, 8$             | $32, 16, 8$             | $16, 8, 4$              | $8, 4, 2$               |
| <b>Head Channels</b>  | $32$                      | $32$                      | $32$                    | $32$                    | $32$                    | $32$                    |
| <b>Batch Size</b>     | $9$                       | $11$                      | $48$                    | $96$                    | $128$                   | $128$                   |
| Iterations*           | $500k$                    | $500k$                    | $500k$                  | $500k$                  | $500k$                  | $500k$                  |
| Learning Rate         | $9e-5$                    | $1.1e-4$                  | $9.6e-5$                | $9.6e-5$                | $1.3e-4$                | $1.3e-4$                |

Table 14. Hyperparameters for the unconditional *LDMs* trained on the CelebA dataset for the analysis in Fig. 7. All models trained on a single NVIDIA A100. \*: All models are trained for 500k iterations. If converging earlier, we used the best checkpoint for assessing the provided FID scores.

<span id="page-11-1"></span>

| Task                       | Text-to-Image           | Layout-to-Image         |                         | Class-Label-to-Image    | Super Resolution        | Inpainting              | Semantic-Map-to-Image   |
|----------------------------|-------------------------|-------------------------|-------------------------|-------------------------|-------------------------|-------------------------|-------------------------|
| <b>Dataset</b>             | <b>LAION</b>            | <b>OpenImages</b>       | <b>COCO</b>             | <b>ImageNet</b>         | <b>ImageNet</b>         | <b>Places</b>           | <b>Landscapes</b>       |
| $f$                        | 8                       | 4                       | 8                       | 4                       | 4                       | 4                       | 8                       |
| $z$ -shape                 | $32 \times 32 \times 4$ | $64 \times 64 \times 3$ | $32 \times 32 \times 4$ | $64 \times 64 \times 3$ | $64 \times 64 \times 3$ | $64 \times 64 \times 3$ | $32 \times 32 \times 4$ |
| $ \mathcal{Z} $            | -                       | 8192                    | 16384                   | 8192                    | 8192                    | 8192                    | 16384                   |
| Diffusion steps            | 1000                    | 1000                    | 1000                    | 1000                    | 1000                    | 1000                    | 1000                    |
| Noise Schedule             | linear                  | linear                  | linear                  | linear                  | linear                  | linear                  | linear                  |
| Model Size                 | 1.45B                   | 306M                    | 345M                    | 395M                    | 169M                    | 215M                    | 215M                    |
| Channels                   | 320                     | 128                     | 192                     | 192                     | 160                     | 128                     | 128                     |
| Depth                      | 2                       | 2                       | 2                       | 2                       | 2                       | 2                       | 2                       |
| <b>Channel Multiplier</b>  | 1,2,4,4                 | 1,2,3,4                 | 1,2,4                   | 1,2,3,5                 | 1,2,2,4                 | 1,4,8                   | 1,4,8                   |
| Number of Heads            | 8                       | 1                       | 1                       | 1                       | 1                       | 1                       | 1                       |
| Dropout                    | -                       | -                       | 0.1                     | -                       | -                       | -                       | -                       |
| <b>Batch Size</b>          | 680                     | 24                      | 48                      | 1200                    | 64                      | 128                     | 48                      |
| <b>Iterations</b>          | 390K                    | 4.4M                    | 170K                    | 178K                    | 860K                    | 360K                    | 360K                    |
| Learning Rate              | $1.0e-4$                | $4.8e-5$                | $4.8e-5$                | $1.0e-4$                | $6.4e-5$                | $1.0e-6$                | $4.8e-5$                |
| Conditioning               | <b>CA</b>               | <b>CA</b>               | <b>CA</b>               | <b>CA</b>               | concat                  | concat                  | concat                  |
| $(C)$ A-resolutions        | 32, 16, 8               | 32, 16, 8               | 32, 16, 8               | 32, 16, 8               | -                       | -                       | -                       |
| <b>Embedding Dimension</b> | 1280                    | 512                     | 512                     | 512                     | -                       | -                       | -                       |
| <b>Transformer Depth</b>   | 1                       | 3                       | 2                       | 1                       | -                       | -                       | -                       |

Table 15. Hyperparameters for the conditional *LDMs* from Sec. 4. All models trained on a single NVIDIA A100 except for the inpainting model which was trained on eight V100.

$$
\zeta \leftarrow \text{TokEmb}(y) + \text{PosEmb}(y) \tag{18}
$$

for 
$$
i = 1, ..., N
$$
:  
\n $\zeta_1 \leftarrow \text{LayerNorm}(\zeta)$  (19)

$$
\zeta_2 \leftarrow \text{MultiHeadSelfAttention}(\zeta_1) + \zeta \tag{20}
$$

 $\zeta_3 \leftarrow \text{LayerNorm}(\zeta_2)$  (21)

$$
\zeta \leftarrow \text{MLP}(\zeta_3) + \zeta_2 \tag{22}
$$

$$
\zeta \leftarrow \text{LayerNorm}(\zeta) \tag{23}
$$

(24)

With  $\zeta$  available, the conditioning is mapped into the UNet via the cross-attention mechanism as depicted in Fig. 3. We modify the "ablated UNet" [15] architecture and replace the self-attention layer with a shallow (unmasked) transformer consisting of T blocks with alternating layers of (i) self-attention, (ii) a position-wise MLP and (iii) a cross-attention layer; see Tab. [16.](#page-12-0) Note that without (ii) and (iii), this architecture is equivalent to the "ablated UNet".

While it would be possible to increase the representational power of  $\tau_\theta$  by additionally conditioning on the time step t, we do not pursue this choice as it reduces the speed of inference. We leave a more detailed analysis of this modification to future work.

For the text-to-image model, we rely on a publicly available<sup>[3](#page-12-1)</sup> tokenizer [99]. The layout-to-image model discretizes the spatial locations of the bounding boxes and encodes each box as a  $(l, b, c)$ -tuple, where l denotes the (discrete) top-left and b the bottom-right position. Class information is contained in  $c$ .

See Tab. [17](#page-12-2) for the hyperparameters of  $\tau_{\theta}$  and Tab. [13](#page-10-1) for those of the UNet for both of the above tasks.

<span id="page-12-0"></span>Note that the class-conditional model as described in Sec. 4.1 is also implemented via cross-attention, where  $\tau_{\theta}$  is a single learnable embedding layer with a dimensionality of 512, mapping classes y to  $\zeta \in \mathbb{R}^{1 \times 512}$ .

| input          | $\mathbb{R}^{h \times w \times c}$           |
|----------------|----------------------------------------------|
| LayerNorm      | $\mathbb{R}^{h \times w \times c}$           |
| Conv1x1        | $\mathbb{R}^{h \times w \times d \cdot n_h}$ |
| Reshape        | $\mathbb{R}^{h \cdot w \times d \cdot n_h}$  |
| SelfAttention  | $\mathbb{R}^{h \cdot w \times d \cdot n_h}$  |
| $\times T$     | $\mathbb{R}^{h \cdot w \times d \cdot n_h}$  |
| MLP            | $\mathbb{R}^{h \cdot w \times d \cdot n_h}$  |
| CrossAttention | $\mathbb{R}^{h \cdot w \times d \cdot n_h}$  |
| Reshape        | $\mathbb{R}^{h \times w \times d \cdot n_h}$ |
| Conv1x1        | $\mathbb{R}^{h \times w \times c}$           |

<span id="page-12-2"></span>Table 16. Architecture of a transformer block as described in Sec. [E.2.1,](#page-10-3) replacing the self-attention layer of the standard "ablated UNet" architecture [15]. Here,  $n_h$  denotes the number of attention heads and d the dimensionality per head.

|                      | Text-to-Image | Layout-to-Image |
|----------------------|---------------|-----------------|
| seq-length           | 77            | 92              |
| depth <span>N</span> | 32            | 16              |
| dim                  | 1280          | 512             |

Table 17. Hyperparameters for the experiments with transformer encoders in Sec. 4.3.

##### E.2.2 Inpainting

For our experiments on image-inpainting in Sec. 4.5, we used the code of [88] to generate synthetic masks. We use a fixed set of 2k validation and 30k testing samples from Places [108]. During training, we use random crops of size  $256 \times 256$ and evaluate on crops of size  $512 \times 512$ . This follows the training and testing protocol in [88] and reproduces their reported metrics (see † in Tab. 7). We include additional qualitative results of *LDM-4, w/ attn* in Fig. 21 and of *LDM-4, w/o attn, big, w/ ft* in Fig. 22.

### E.3. Evaluation Details

This section provides additional details on evaluation for the experiments shown in Sec. 4.

#### E.3.1 Quantitative Results in Unconditional and Class-Conditional Image Synthesis

We follow common practice and estimate the statistics for calculating the FID-, Precision- and Recall-scores [29,50] shown in Tab. 1 and [10](#page-8-1) based on 50k samples from our models and the entire training set of each of the shown datasets. For calculating FID scores we use the torch-fidelity package [60]. However, since different data processing pipelines might lead to different results [64], we also evaluate our models with the script provided by Dhariwal and Nichol [15]. We find that results

<span id="page-12-1"></span><sup>3</sup>[https://huggingface.co/transformers/model\\_doc/bert.html#berttokenizerfast](https://huggingface.co/transformers/model_doc/bert.html#berttokenizerfast)

mainly coincide, except for the ImageNet and LSUN-Bedrooms datasets, where we notice slightly varying scores of 7.76 (torch-fidelity) vs. 7.77 (Nichol and Dhariwal) and 2.95 vs 3.0. For the future we emphasize the importance of a unified procedure for sample quality assessment. Precision and Recall are also computed by using the script provided by Nichol and Dhariwal.

##### E.3.2 Text-to-Image Synthesis

Following the evaluation protocol of [66] we compute FID and Inception Score for the Text-to-Image models from Tab. 2 by comparing generated samples with 30000 samples from the validation set of the MS-COCO dataset [51]. FID and Inception Scores are computed with torch-fidelity.

##### E.3.3 Layout-to-Image Synthesis

For assessing the sample quality of our Layout-to-Image models from Tab. [9](#page-8-0) on the COCO dataset, we follow common practice [37, 87, 89] and compute FID scores the 2048 unaugmented examples of the COCO Segmentation Challenge split. To obtain better comparability, we use the exact same samples as in [37]. For the OpenImages dataset we similarly follow their protocol and use 2048 center-cropped test images from the validation set.

##### E.3.4 Super Resolution

We evaluate the super-resolution models on ImageNet following the pipeline suggested in [72], *i.e*. images with a shorter size less than 256 px are removed (both for training and evaluation). On ImageNet, the low-resolution images are produced using bicubic interpolation with anti-aliasing. FIDs are evaluated using torch-fidelity [60], and we produce samples on the validation split. For FID scores, we additionally compare to reference features computed on the train split, see Tab. 5 and Tab. [11.](#page-9-2)

##### E.3.5 Efficiency Analysis

For efficiency reasons we compute the sample quality metrics plotted in Fig. 6, [17](#page-8-2) and 7 based on 5k samples. Therefore, the results might vary from those shown in Tab. 1 and [10.](#page-8-1) All models have a comparable number of parameters as provided in Tab. [13](#page-10-1) and [14.](#page-11-0) We maximize the learning rates of the individual models such that they still train stably. Therefore, the learning rates slightly vary between different runs *cf* . Tab. [13](#page-10-1) and [14.](#page-11-0)

### E.3.6 User Study

For the results of the user study presented in Tab. 4 we followed the protocoll of [72] and and use the 2-alternative force-choice paradigm to assess human preference scores for two distinct tasks. In Task-1 subjects were shown a low resolution/masked image between the corresponding ground truth high resolution/unmasked version and a synthesized image, which was generated by using the middle image as conditioning. For SuperResolution subjects were asked: *'Which of the two images is a better high quality version of the low resolution image in the middle?'*. For Inpainting we asked *'Which of the two images contains more realistic inpainted regions of the image in the middle?'*. In Task-2, humans were similarly shown the lowres/masked version and asked for preference between two corresponding images generated by the two competing methods. As in [72] humans viewed the images for 3 seconds before responding.

## F. Computational Requirements

<span id="page-14-0"></span>

| Method                                                                                                        | Generator          | Classifier | Overall           | Inference   |                     | $FID \downarrow$                        | IS <sup>↑</sup>             | Precision <sup>+</sup> | <b>Recall</b> <sup>+</sup> |
|---------------------------------------------------------------------------------------------------------------|--------------------|------------|-------------------|-------------|---------------------|-----------------------------------------|-----------------------------|------------------------|----------------------------|
|                                                                                                               | Compute            | Compute    | Compute           | Throughput* | $N_{\text{params}}$ |                                         |                             |                        |                            |
|                                                                                                               |                    |            |                   |             |                     |                                         |                             |                        |                            |
| LSUN Churches $2562$                                                                                          |                    |            |                   |             |                     |                                         |                             |                        |                            |
| StyleGAN2 $[42]$ <sup>†</sup>                                                                                 | 64                 |            | 64                | $\sim$      | 59M                 | 3.86                                    |                             |                        |                            |
| $LDM-8$ (ours, 100 steps, 410K)                                                                               | 18                 |            | 18                | 6.80        | 256M                | 4.02                                    |                             | 0.64                   | 0.52                       |
| LSUN Bedrooms $2562$                                                                                          |                    |            |                   |             |                     |                                         |                             |                        |                            |
| ADM $[15]$ <sup>†</sup> (1000 steps)                                                                          | 232                |            | 232               | 0.03        | 552M                | 1.9                                     | ٠                           | 0.66                   | 0.51                       |
| $LDM-4$ (ours, 200 steps, 1.9M)                                                                               | 60                 |            | 55                | 1.07        | 274M                | 2.95                                    | ٠                           | 0.66                   | 0.48                       |
| CelebA-HO $2562$                                                                                              |                    |            |                   |             |                     |                                         |                             |                        |                            |
| $LDM-4$ (ours, 500 steps, 410K)                                                                               | 14.4               |            | 14.4              | 0.43        | 274M                | 5.11                                    | ä,                          | 0.72                   | 0.49                       |
| FFHQ $256^2$                                                                                                  |                    |            |                   |             |                     |                                         |                             |                        |                            |
| StyleGAN2 [42]                                                                                                | $32.13^{\ddagger}$ |            | $32.13^{\dagger}$ | $\sim$      | 59M                 | 3.8                                     |                             |                        |                            |
| $LDM-4$ (ours, 200 steps, 635K)                                                                               | 26                 |            | 26                | 1.07        | 274M                | 4.98                                    | ٠                           | 0.73                   | 0.50                       |
| ImageNet $2562$                                                                                               |                    |            |                   |             |                     |                                         |                             |                        |                            |
| VQGAN-f-4 (ours, first stage)                                                                                 | 29                 |            | 29                |             | 55M                 | $0.58$ <sup><math>\dagger</math>†</sup> |                             |                        |                            |
| VOGAN-f-8 (ours, first stage)                                                                                 | 66                 |            | 66                |             | 68M                 | $1.14^{\dagger\dagger}$                 |                             |                        |                            |
| BigGAN-deep $[3]^\dagger$                                                                                     | 128-256            |            | 128-256           | $\sim$      | 340M                | 6.95                                    | $203.6{\scriptstyle \pm26}$ | 0.87                   | 0.28                       |
| ADM [15] (250 steps) <sup>†</sup>                                                                             | 916                | ٠          | 916               | 0.12        | 554M                | 10.94                                   | 100.98                      | 0.69                   | 0.63                       |
| ADM-G [15] (25 steps) <sup>†</sup>                                                                            | 916                | 46         | 962               | 0.7         | 608M                | 5.58                                    | $\overline{\phantom{a}}$    | 0.81                   | 0.49                       |
| ADM-G [15] (250 steps) <sup>†</sup>                                                                           | 916                | 46         | 962               | 0.07        | 608M                | 4.59                                    | 186.7                       | 0.82                   | 0.52                       |
| ADM-G, ADM-U [15] (250 steps) <sup>†</sup>                                                                    | 329                | 30         | 349               | n/a         | n/a                 | 3.85                                    | 221.72                      | 0.84                   | 0.53                       |
| $LDM-8-G$ (ours, 100, 2.9M)                                                                                   | 79                 | 12         | 91                | 1.93        | 506M                | 8.11                                    | $190.4{\scriptstyle \pm26}$ | 0.83                   | 0.36                       |
| LDM-8 (ours, 200 ddim steps 2.9M, batch size 64)                                                              | 79                 |            | 79                | 1.9         | 395M                | 17.41                                   | 72.92                       | 0.65                   | 0.62                       |
| LDM-4 (ours, 250 ddim steps 178K, batch size 1200)                                                            | 271                |            | 271               | 0.7         | 400M                | 10.56                                   | $103.49 + 1.24$             | 0.71                   | 0.62                       |
| LDM-4-G (ours, 250 ddim steps 178K, batch size 1200, classifier-free guidance [32] scale 1.25)                | 271                |            | 271               | 0.4         | 400M                | 3.95                                    | $178.22_{\pm 243}$          | 0.81                   | 0.55                       |
| $LDM-4-G$ (ours, 250 ddim steps 178K, batch size 1200, classifier-free guidance $\lceil 32 \rceil$ scale 1.5) | 271                |            | 271               | 0.4         | 400M                | 3.60                                    | $247.67 + s$                | 0.87                   | 0.48                       |

Table 18. Comparing compute requirements during training and inference throughput with state-of-the-art generative models. Compute during training in V100-days, numbers of competing methods taken from [15] unless stated differently;\*: Throughput measured in samples/sec on a single NVIDIA A100;<sup>†</sup>: Numbers taken from [15] ;<sup>‡</sup>: Assumed to be trained on 25M train examples; <sup>††</sup>: R-FID vs. ImageNet validation set

In Tab [18](#page-14-0) we provide a more detailed analysis on our used compute ressources and compare our best performing models on the CelebA-HQ, FFHQ, LSUN and ImageNet datasets with the recent state of the art models by using their provided numbers, *cf*. [15]. As they report their used compute in V100 days and we train all our models on a single NVIDIA A100 GPU, we convert the A100 days to V100 days by assuming a  $\times 2.2$  speedup of A100 vs V100 [7[4](#page-14-1)]<sup>4</sup>. To assess sample quality, we additionally report FID scores on the reported datasets. We closely reach the performance of state of the art methods as StyleGAN2 [42] and ADM [15] while significantly reducing the required compute resources.

<span id="page-14-1"></span><sup>&</sup>lt;sup>4</sup>This factor corresponds to the speedup of the A100 over the V100 for a U-Net, as defined in Fig. 1 in  $[74]$ 

<span id="page-15-0"></span>

## G. Details on Autoencoder Models

We train all our autoencoder models in an adversarial manner following [23], such that a patch-based discriminator  $D_{\psi}$ is optimized to differentiate original images from reconstructions  $\mathcal{D}(\mathcal{E}(x))$ . To avoid arbitrarily scaled latent spaces, we regularize the latent z to be zero centered and obtain small variance by introducing an regularizing loss term  $L_{rea}$ .

We investigate two different regularization methods: (i) a low-weighted Kullback-Leibler-term between  $q_{\mathcal{E}}(z|x)$  =  $\mathcal{N}(z;\mathcal{E}_{\mu},\mathcal{E}_{\sigma^2})$  and a standard normal distribution  $\mathcal{N}(z;0,1)$  as in a standard variational autoencoder [46, 69], and, (ii) regularizing the latent space with a vector quantization layer by learning a codebook of  $|\mathcal{Z}|$  different exemplars [96].

To obtain high-fidelity reconstructions we only use a very small regularization for both scenarios, *i.e*. we either weight the KL term by a factor  $\sim 10^{-6}$  or choose a high codebook dimensionality  $|\mathcal{Z}|$ .

The full objective to train the autoencoding model  $(\mathcal{E}, \mathcal{D})$  reads:

$$
L_{\text{Autoencoder}} = \min_{\mathcal{E}, \mathcal{D}} \max_{\psi} \left( L_{rec}(x, \mathcal{D}(\mathcal{E}(x))) - L_{adv}(\mathcal{D}(\mathcal{E}(x))) + \log D_{\psi}(x) + L_{reg}(x; \mathcal{E}, \mathcal{D}) \right) \tag{25}
$$

DM Training in Latent Space Note that for training diffusion models on the learned latent space, we again distinguish two cases when learning  $p(z)$  or  $p(z|y)$  (Sec. 4.3): (i) For a KL-regularized latent space, we sample  $z = \mathcal{E}_{\mu}(x) + \mathcal{E}_{\sigma}(x) \cdot \varepsilon =: \mathcal{E}(x)$ , where  $\varepsilon \sim \mathcal{N}(0, 1)$ . When rescaling the latent, we estimate the component-wise variance

$$
\hat{\sigma}^2 = \frac{1}{bchw} \sum_{b,c,h,w} (z^{b,c,h,w} - \hat{\mu})^2
$$

from the first batch in the data, where  $\hat{\mu} = \frac{1}{bchw} \sum_{b,c,h,w} z^{b,c,h,w}$ . The output of  $\mathcal E$  is scaled such that the rescaled latent has unit standard deviation, *i.e.*  $z \leftarrow \frac{z}{\hat{\sigma}} = \frac{\mathcal{E}(x)}{\hat{\sigma}}$  $\frac{f(x)}{\hat{\sigma}}$ . (ii) For a VQ-regularized latent space, we extract *z before* the quantization layer and absorb the quantization operation into the decoder, *i.e*. it can be interpreted as the first layer of D.

## H. Additional Qualitative Results

Finally, we provide additional qualitative results for our landscapes model (Fig. [12,](#page-0-0) 23, 24 and 25), our class-conditional ImageNet model (Fig. 26 - 27) and our unconditional models for the CelebA-HQ, FFHQ and LSUN datasets (Fig. 28 - 31). Similar as for the inpainting model in Sec. 4.5 we also fine-tuned the semantic landscapes model from Sec. 4.3.2 directly on  $512^2$  $512^2$  $512^2$  images and depict qualitative results in Fig. 12 and Fig. 23. For our those models trained on comparably small datasets, we additionally show nearest neighbors in VGG [79] feature space for samples from our models in Fig. 32 - 34.