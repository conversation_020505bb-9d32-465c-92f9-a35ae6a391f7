# 7

## Dynamic Formulations

This chapter presents the geodesic (also called dynamic) point of view of optimal transport when the cost is a squared geodesic distance. This describes the optimal transport between two measures as a curve in the space of measures minimizing a total length. The dynamic point of view offers an alternative and intuitive interpretation of optimal transport, which not only allows us to draw links with fluid dynamics but also results in an efficient numerical tool to compute OT in small dimensions when interpolating between two densities. The drawback of that approach is that it cannot scale to largescale sparse measures and works only in low dimensions on regular domains (because one needs to grid the space) with a squared geodesic cost.

In this chapter, we use the notation  $(\alpha_0, \alpha_1)$  in place of  $(\alpha, \beta)$  in agreement with the idea that we start at time  $t = 0$  from one measure to reach another one at time  $t=1$ .

## 7.1 Continuous Formulation

In the case  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^d$ , and  $c(x, y) = ||x - y||^2$ , the optimal transport distance  $W_2^2(\alpha, \beta) = \mathcal{L}_c(\alpha, \beta)$  as defined in (2.15) can be computed by looking for a minimal length path  $(\alpha_t)_{t=0}^1$  between these two measures. This path is described by advecting the measure using a vector field  $v_t$  defined at each instant. The vector field  $v_t$  and the path  $\alpha_t$  must satisfy the conservation of mass formula, resulting in

$$
\frac{\partial \alpha_t}{\partial t} + \text{div}(\alpha_t v_t) = 0 \quad \text{and} \quad \alpha_{t=0} = \alpha_0, \alpha_{t=1} = \alpha_1,\tag{7.1}
$$

## 7.1. Continuous Formulation 103

where the equation above should be understood in the sense of distributions on  $\mathbb{R}^d$ . The infinitesimal length of such a vector field is measured using the *L* <sup>2</sup> norm associated to the measure  $\alpha_t$ , that is defined as

$$
||v_t||_{L^2(\alpha_t)} = \left(\int_{\mathbb{R}^d} ||v_t(x)||^2 d\alpha_t(x)\right)^{1/2}.
$$

This definition leads to the following minimal-path reformulation of  $\mathcal{W}_2$ , originally introduced by Benamou and Brenier [2000]:

$$
\mathcal{W}_2^2(\alpha_0, \alpha_1) = \min_{(\alpha_t, v_t)_t \text{ sat. (7.1)}} \int_0^1 \int_{\mathbb{R}^d} ||v_t(x)||^2 \, d\alpha_t(x) dt,
$$
\n(7.2)

where  $\alpha_t$  is a scalar-valued measure and  $v_t$  a vector-valued measure. Figure 7.1 shows two examples of such paths of measures.

Image /page/1/Figure/6 description: The image displays a series of five pairs of shapes arranged horizontally. The top row shows smooth, blurred shapes, while the bottom row shows corresponding shapes composed of numerous small dots. From left to right, the shapes transition in color from red to shades of purple and finally to blue. The first pair is a solid red circle. The second pair consists of two red semi-circles separated by a white line. The third pair shows two purple semi-circles. The fourth pair displays two smaller, more amorphous purple shapes. The fifth pair features two blue circles. Below each pair of shapes, labels indicate progression: "α0", "α1/4", "α1/2", "α3/4", and "α1".

**Figure 7.1:** Displacement interpolation  $\alpha_t$  satisfying (7.2). Top: for two measures  $(\alpha_0, \alpha_1)$  with densities with respect to the Lebesgue measure. Bottom: for two discrete empirical measures with the same number of points (bottom).

The formulation (7.2) is a nonconvex formulation in the variables  $(\alpha_t, v_t)_t$  because of the constraint (7.1) involving the product  $\alpha_t v_t$ . Introducing a vector-valued measure (often called the "momentum")

$$
J_t \stackrel{\text{\tiny def.}}{=} \alpha_t v_t,
$$

Benamou and Brenier showed in their landmark paper [2000] that it is instead convex in the variable  $(\alpha_t, J_t)_t$  when writing

$$
\mathcal{W}_2^2(\alpha_0, \alpha_1) = \min_{(\alpha_t, J_t)_t \in \mathcal{C}(\alpha_0, \alpha_1)} \int_0^1 \int_{\mathbb{R}^d} \theta(\alpha_t(x), J_t(x)) \, \mathrm{d}x \, \mathrm{d}t,\tag{7.3}
$$

where we define the set of constraints as

$$
\mathcal{C}(\alpha_0, \alpha_1) \stackrel{\text{def.}}{=} \left\{ (\alpha_t, J_t) \; : \; \frac{\partial \alpha_t}{\partial t} + \text{div}(J_t) = 0, \alpha_{t=0} = \alpha_0, \alpha_{t=1} = \alpha_1 \right\},\tag{7.4}
$$

and where  $\theta : \to \mathbb{R}^+ \cup \{+\infty\}$  is the following lower semicontinuous convex function

$$
\forall (a, b) \in \mathbb{R}_+ \times \mathbb{R}^d, \quad \theta(a, b) = \begin{cases} \frac{\|b\|^2}{a} & \text{if } a > 0, \\ 0 & \text{if } (a, b) = 0, \\ +\infty & \text{otherwise.} \end{cases}
$$
(7.5)

This definition might seem complicated, but it is crucial to impose that the momentum  $J_t(x)$  should vanish when  $\alpha_t(x) = 0$ . Note also that (7.3) is written in an informal way as if the measures  $(\alpha_t, J_t)$  were density functions, but this is acceptable because  $\theta$  is a 1-homogeneous function (and hence defined even if the measures do not have a density with respect to Lebesgue measure) and can thus be extended in an unambiguous way from density to functions.

**Remark 7.1** (Links with McCann's interpolation)**.** In the case (see Equation (2.28)) where there exists an optimal Monge map  $T : \mathbb{R}^d \to \mathbb{R}^d$  with  $T_\sharp \alpha_0 = \alpha_1$ , then  $\alpha_t$  is equal to McCann's interpolation

$$
\alpha_t = ((1 - t)\text{Id} + t) \sharp \alpha_0. \tag{7.6}
$$

In the 1-D case, using Remark 2.30, this interpolation can be computed thanks to the relation

$$
\mathcal{C}_{\alpha_t}^{-1} = (1 - t)\mathcal{C}_{\alpha_0}^{-1} + t\mathcal{C}_{\alpha_1}^{-1};\tag{7.7}
$$

see Figure 2.11. We refer to Gangbo and McCann [1996] for a detailed review on the Riemannian geometry of the Wasserstein space. In the case that there is "only" an optimal coupling  $\pi$  that is not necessarily supported on a Monge map, one can compute this interpolant as

$$
\alpha_t = P_{t\sharp}\pi \quad \text{where} \quad P_t: (x, y) \in \mathbb{R}^d \times \mathbb{R}^d \mapsto (1 - t)x + ty. \tag{7.8}
$$

For instance, in the discrete setup  $(2.3)$ , denoting **P** a solution to  $(2.11)$ , an interpolation is defined as

$$
\alpha_t = \sum_{i,j} \mathbf{P}_{i,j} \delta_{(1-t)x_i + ty_j}.
$$
\n(7.9)

Such an interpolation is typically supported on  $n + m - 1$  points, which is the maximum number of nonzero elements of **P**. Figure 7.2 shows two examples of such displacement interpolation of discrete measures. This construction can be generalized to geodesic spaces  $\mathcal X$  by replacing  $P_t$  by the interpolation along geodesic

104

## 7.2. Discretization on Uniform Staggered Grids 105

paths. McCann's interpolation finds many applications, for instance, color, shape, and illumination interpolations in computer graphics [Bonneel et al., 2011].

Image /page/3/Figure/2 description: The image displays a scatter plot with two rows and six columns of data points. Each column is labeled with a Greek letter followed by a fraction, ranging from \alpha\_0 to \alpha\_1. The data points are represented as circles of varying sizes and colors, transitioning from blue on the left to red on the right. The top row shows clusters of points that generally increase in size and shift in color from blue to purple to red as you move from left to right. The bottom row mirrors this pattern, with clusters of points also varying in size and color from blue to purple to red across the columns. The labels below the columns are \alpha\_0, \alpha\_1/5, \alpha\_2/5, \alpha\_3/5, \alpha\_4/5, and \alpha\_1.

**Figure 7.2:** Comparison of displacement interpolation (7.8) of discrete measures. Top: point clouds (empirical measures  $(\alpha_0, \alpha_1)$  with the same number of points). Bottom: same but with varying weights. For  $0 < t < 1$ , the top example corresponds to an empirical measure interpolation  $\alpha_t$  with N points, while the bottom one defines a measure supported on  $2N - 1$  points.

## 7.2. Discretization on Uniform Staggered Grids

For simplicity, we describe the numerical scheme in dimension  $d = 2$ ; the extension to higher dimensions is straightforward. We follow the discretization method introduced by Papadakis et al. [2014], which is inspired by staggered grid techniques which are commonly used in fluid dynamics. We discretize time as  $t_k = k/T \in [0, 1]$  and assume the space is uniformly discretized at points  $x_i = (i_1/n_1, i_2/n_2) \in X = [0, 1]^2$ . We use a staggered grid representation, so that  $\alpha_t$  is represented using  $\mathbf{a} \in \mathbb{R}^{(T+1)\times n_1 \times n_2}$ associated to half grid points in time, whereas *J* is represented using  $J = (J_1, J_2)$ , where  $\mathbf{J}_1 \in \mathbb{R}^{T \times (n_1+1) \times n_2}$  and  $\mathbf{J}_1 \in \mathbb{R}^{T \times n_1 \times (n_2+1)}$  are stored at half grid points in each space direction. Using this representation, for  $(k, i_1, i_2) \in [1, T] \times [1, n_1] \times [1, n_2]$ , the time derivative is computed as

$$
(\partial_t\mathbf{a})_{k,i} \stackrel{\scriptscriptstyle\rm def.}{=} \mathbf{a}_{k+1,i} - \mathbf{a}_{k,i}
$$

and spatial divergence as

$$
\operatorname{div}(\mathbf{J})_{k,i} \stackrel{\text{def}}{=} \mathbf{J}_{k,i_1+1,i_2}^1 - \mathbf{J}_{k,i_1,i_2}^1 + \mathbf{J}_{k,i_1,i_2+1}^2 - \mathbf{J}_{k,i_1,i_2}^2,\tag{7.10}
$$

which are both defined at grid points, thus forming arrays of  $\mathbb{R}^{T \times n_1 \times n_2}$ .

In order to evaluate the functional to be optimized, one needs interpolation operators from midgrid points to grid points, for all  $(k, i_1, i_2) \in [1, T] \times [1, n_1] \times [1, n_2]$ ,

$$
\mathcal{I}_{a}(\mathbf{a})_{k,i} \stackrel{\scriptscriptstyle\rm def.}{=} \mathcal{I}(\mathbf{a}_{k+1,i},\mathbf{a}_{k,i}),
$$

$$
\mathcal{I}_{J}(\mathbf{J})_{k,i} \stackrel{\text{def.}}{=} (\mathcal{I}(\mathbf{J}_{k,i_1+1,i_2}^1, \mathbf{J}_{k,i_1,i_2}^1), \mathcal{I}(\mathbf{J}_{k,i_1,i_2+1}^2, \mathbf{J}_{k,i_1,i_2}^2)).
$$

The simplest choice is to use a linear operator  $\mathcal{I}(r,s) = \frac{r+s}{2}$ , which is the one we consider next. The discrete counterpart to (7.3) reads

$$
\min_{(\mathbf{a},\mathbf{J}) \in \mathbf{C}(\mathbf{a}_0,\mathbf{a}_1)} \Theta(\mathcal{I}_a(\mathbf{a}), \mathcal{I}_J(\mathbf{J})) \hfill (7.11)
$$

$$
\text{where} \quad \Theta(\tilde{\mathbf{a}}, \tilde{\mathbf{J}}) \stackrel{\text{def}}{=} \sum_{k=1}^T \sum_{i_1=1}^{n_1} \sum_{i_2=1}^{n_2} \theta(\tilde{\mathbf{a}}_{k,i}, \tilde{\mathbf{J}}_{k,i})
$$

and where the constraint now reads

$$
\mathbf{C}(\mathbf{a}_0,\mathbf{a}_1) \stackrel{\text{\tiny def.}}{=} \{(\mathbf{a},\mathbf{J}) \; : \; \partial_t \mathbf{a} + \text{div}(\mathbf{J}) = 0, (\mathbf{a}_{0,\cdot},\mathbf{a}_{T,\cdot}) = (\mathbf{a}_0,\mathbf{a}_1)\},
$$

where  $\mathbf{a} \in \mathbb{R}^{(T+1)\times n_1 \times n_2}$ ,  $\mathbf{J} = (\mathbf{J}_1, \mathbf{J}_2)$  with  $\mathbf{J}_1 \in \mathbb{R}^{T \times (n_1+1)\times n_2}$ ,  $\mathbf{J}_2 \in \mathbb{R}^{T \times n_1 \times (n_2+1)}$ . Figure 7.3 shows an example of evolution  $(\alpha_t)_t$  approximated using this discretization scheme.

**Remark 7.2** (Dynamic formulation on graphs). In the case where  $\mathcal{X}$  is a graph and  $c(x, y) = d<sub>X</sub>(x, y)<sup>2</sup>$  is the squared geodesic distance, it is possible to derive faithful discretization methods that use a discrete divergence associated to the graph structure in place of the uniform grid discretization (7.10). In order to ensure that the heat equation has a gradient flow structure (see §9.3 for more details about gradient flows) for the corresponding dynamic Wasserstein distance, Maas [2011] and later Mielke [2013] proposed to use a logarithmic mean  $\mathcal{I}(r, s)$  (see also [Solomon et al., 2016b, Chow et al., 2012, 2017b,a]).

## 7.3. Proximal Solvers

The discretized dynamic OT problem (7.11) is challenging to solve because it requires us to minimize a nonsmooth optimization problem under affine constraints. Indeed, the function  $\theta$  is convex but nonsmooth for measures with vanishing mass  $\mathbf{a}_{k,i}$ . When interpolating between two compactly supported input measures  $(a_0, a_1)$ , one typically expects the mass of the interpolated measures  $(\mathbf{a}_k)_{k=1}^T$  to vanish as well, and the difficult part of the optimization process is indeed to track this evolution of the support. In particular, it is not possible to use standard smooth optimization techniques.

There are several ways to recast (7.11) into a quadratic-cone program, either by considering the dual problem or simply by replacing the functional  $\theta(\mathbf{a}_{k,i}, \mathbf{J}_{k,i})$  by a

106

## 7.3. Proximal Solvers 107

linear function under constraints,

$$
\Theta(\tilde{\mathbf{a}},\tilde{\mathbf{J}})=\min_{\tilde{\mathbf{z}}}\;\left\{\sum_{k,i}\tilde{\mathbf{z}}_{k,i}\;:\;\forall\,(k,i),(\mathbf{z}_{k,i},\tilde{\mathbf{a}}_{k,i},\tilde{\mathbf{J}}_{i,j})\in\mathcal{L}\right\},
$$

which thus requires the introduction of an extra variable  $\tilde{z}$ . Here  $\mathcal{L} \stackrel{\text{def.}}{=} \{(z, a, J) \in$  $\mathbb{R} \times \mathbb{R}^+ \times \mathbb{R}^d$  :  $||J||^2 \leq za$  is a rotated Lorentz quadratic-cone. With this extra variable, it is thus possible to solve the discretized problem using standard interior point solvers for quadratic-cone programs [Nesterov and Nemirovskii, 1994]. These solvers have fast convergence rates and are thus capable of computing a solution with high precision. Unfortunately, each iteration is costly and requires the resolution of a linear system of dimension that scales with the number of discretization points. They are thus not applicable for large-scale multidimensional problems encountered in imaging applications.

An alternative to these high-precision solvers are low-precision first order methods, which are well suited for nonsmooth but highly structured problems such as  $(7.11)$ . While this class of solvers is not new, it has recently been revitalized in the fields of imaging and machine learning because they are the perfect fit for these applications, where numerical precision is not the driving goal. We refer, for instance, to the monograph [Bauschke and Combettes, 2011] for a detailed account on these solvers and their use for large-scale applications. We here concentrate on a specific solver, but of course many more can be used, and we refer to [Papadakis et al., 2014] for a study of several such approaches for dynamical OT. Note that the idea of using a first order scheme for dynamical OT was initially proposed by Benamou and Brenier [2000].

The DR algorithm [Lions and Mercier, 1979] is specifically tailored to solve nonsmooth structured problems of the form

$$
\min_{x \in \mathcal{H}} F(x) + G(x),\tag{7.12}
$$

where H is some Euclidean space, and where  $F, G : \mathcal{H} \to \mathbb{R} \cup \{+\infty\}$  are two closed convex functions, for which one can "easily " (*e.g.* in closed form or using a rapidly converging scheme) compute the so-called proximal operator

$$
\forall x \in \mathcal{H}, \quad \text{Prox}_{\tau F}(x) \stackrel{\text{def.}}{=} \underset{x' \in \mathcal{H}}{\text{argmin}} \frac{1}{2} \|x - x'\|^2 + \tau F(x) \tag{7.13}
$$

for a parameter  $\tau > 0$ . Note that this corresponds to the proximal map for the Euclidean metric and that this definition can be extended to more general Bregman divergence in place of  $||x - x'||^2$ ; see (4.52) for an example using the **KL** divergence. The iterations of the DR algorithm define a sequence  $(x^{(\ell)}, w^{(\ell)}) \in \mathcal{H}^2$  using an initialization  $(x^{(0)}, w^{(0)}) \in$  $\mathcal{H}^2$  and

$$
w^{(\ell+1)} \stackrel{\text{def.}}{=} w^{(\ell)} + \alpha (\text{Prox}_{\gamma F}(2x^{(\ell)} - w^{(\ell)}) - x^{(\ell)}),
$$
  
$$
x^{(\ell+1)} \stackrel{\text{def.}}{=} \text{Prox}_{\gamma G}(w^{(\ell+1)}).
$$
 (7.14)

If  $0 < \alpha < 2$  and  $\gamma > 0$ , one can show that  $x^{(\ell)} \to z^*$ , where  $z^*$  is a solution of (7.12); see [Combettes and Pesquet, 2007] for more details. This algorithm is closely related to another popular method, the alternating direction method of multipliers [Gabay and Mercier, 1976, Glowinski and Marroco, 1975 (see also [Boyd et al., 2011] for a review), which can be recovered by applying DR on a dual problem; see [Papadakis et al., 2014] for more details on the equivalence between the two, first shown by [Eckstein and Bertsekas, 1992].

There are many ways to recast Problem (7.11) in the form (7.12), and we refer to [Papadakis et al., 2014] for a detailed account of these approaches. A simple way to achieve this is by setting  $x = (\mathbf{a}, \mathbf{J}, \tilde{\mathbf{a}}, \mathbf{J})$  and letting

$$
F(x) \stackrel{\text{def.}}{=} \Theta(\tilde{\mathbf{a}}, \tilde{\mathbf{J}}) + \iota_{\mathbf{C}(\mathbf{a}_0, \mathbf{a}_1)}(\mathbf{a}, \mathbf{J}) \quad \text{and} \quad G(x) = \iota_{\mathcal{D}}(\mathbf{a}, \mathbf{J}, \tilde{\mathbf{a}}, \tilde{\mathbf{J}}),
$$
  
where  $\mathcal{D} \stackrel{\text{def.}}{=} \{ (\mathbf{a}, \mathbf{J}, \tilde{\mathbf{a}}, \tilde{\mathbf{J}}) : \tilde{\mathbf{a}} = \mathcal{I}_a(\mathbf{a}), \tilde{\mathbf{J}} = \mathcal{I}_J(\mathbf{J}) \}.$ 

The proximal operator of these two functions can be computed efficiently. Indeed, one has

 $\text{Prox}_{\tau F}(x) = (\text{Prox}_{\tau \Theta}(\tilde{\mathbf{a}}, \tilde{\mathbf{J}}), \text{Proj}_{\mathbf{C}(\mathbf{a}_0, \mathbf{a}_1)}(\mathbf{a}, \mathbf{J}))$ .

The proximal operator  $Prox_{\tau\Theta}$  is computed by solving a cubic polynomial equation at each grid position. The orthogonal projection on the affine constraint  $\mathbf{C}(\mathbf{a}_0, \mathbf{a}_1)$  involves the resolution of a Poisson equation, which can be achieved in  $O(N \log(N))$  operations using the fast Fourier transform, where  $N = T n_1 n_2$  is the number of grid points. Lastly, the proximal operator  $Prox_{\tau G}$  is a linear projector, which requires the inversion of a small linear system. We refer to Papadakis et al. [2014] for more details on these computations. Figure 7.3 shows an example in which that method is used to compute a dynamical interpolation inside a complicated planar domain. This class of proximal methods for dynamical OT has also been used to solve related problems such as mean field games [Benamou and Carlier, 2015].

## 7.4. Dynamical Unbalanced OT

In order to be able to match input measures with different mass  $\alpha_0(\mathcal{X}) \neq \alpha_1(\mathcal{X})$  (the so-called "unbalanced" settings, the terminology introduced by Benamou [2003]), and also to cope with local mass variation, several normalizations or relaxations have been proposed, in particular by relaxing the fixed marginal constraint; see §10.2. A general methodology consists in introducing a source term  $s_t(x)$  in the continuity equation (7.4). We thus consider

$$
\bar{\mathcal{C}}(\alpha_0,\alpha_1) \stackrel{\text{def.}}{=} \left\{ (\alpha_t,J_t,s_t) \; : \; \frac{\partial \alpha_t}{\partial t} + \text{div}(J_t) = s_t, \alpha_{t=0} = \alpha_0, \alpha_{t=1} = \alpha_1 \right\}.
$$

The crucial question is how to measure the cost associated to this source term and introduce it in the original dynamic formulation (7.3). Several proposals appear in the

Image /page/7/Figure/1 description: The image displays a series of ten snapshots of a maze, each labeled with a time value from t=0 to t=1. The maze is depicted as a black grid with white pathways. In each snapshot, a colored blob, representing a probability distribution, is shown within the maze. At t=0, the blob is in the top-left corner. As time progresses, the blob moves through the maze, changing shape and orientation. By t=1, the blob has returned to the top-left corner, appearing as a circular distribution with concentric color rings.

**Figure 7.3:** Solution  $\alpha_t$  of dynamic OT computed with a proximal splitting scheme.

literature, for instance, using an *L* 2 cost Piccoli and Rossi [2014]. In order to avoid having to "teleport" mass (mass which travels at infinite speed and suddenly grows in a region where there was no mass before), the associated cost should be infinite. It turns out that this can be achieved in a simple convex way, by also allowing  $s_t$  to be an arbitrary measure (*e.g.* using a 1-homogeneous cost) by penalizing  $s_t$  in the same way as the momentum  $J_t$ ,

$$
\text{WFR}^2(\alpha_0, \alpha_1) = \min_{(\alpha_t, J_t, s_t)_t \in \overline{\mathcal{C}}(\alpha_0, \alpha_1)} \Theta(\alpha, J, s), \tag{7.15}
$$

where 
$$
\Theta(\alpha, J, s) \stackrel{\text{def.}}{=} \int_0^1 \int_{\mathbb{R}^d} (\theta(\alpha_t(x), J_t(x)) + \tau \theta(\alpha_t(x), s_t(x))) \, dx \, dt
$$
,

where  $\theta$  is the convex 1-homogeneous function introduced in (7.5), and  $\tau$  is a weight controlling the trade-off between mass transportation and mass creation/destruction. This formulation was proposed independently by several authors [Liero et al., 2016, Chizat et al., 2018c, Kondratyev et al., 2016]. This "dynamic" formulation has a "static" counterpart; see Remark 10.5. The convex optimization problem (7.15) can be solved using methods similar to those detailed in §7.3. Figure 7.4 displays a comparison of several unbalanced OT dynamic interpolations. This dynamic formulation resembles "metamorphosis" models for shape registration [Trouvé and Younes, 2005], and a more precise connection is detailed in [Maas et al., 2015, 2016].

As  $\tau \to 0$ , and if  $\alpha_0(\mathcal{X}) = \alpha_1(\mathcal{X})$ , then one retrieves the classical OT problem,  $WFR(\alpha_0, \alpha_1) \rightarrow W(\alpha_0, \alpha_1)$ . In contrast, as  $\tau \rightarrow +\infty$ , this distance approaches the Hellinger metric over densities

$$
\frac{1}{\tau} \text{WFR}(\alpha_0, \alpha_1)^2 \xrightarrow{\tau \to \infty} \int_X |\sqrt{\rho_{\alpha_0}(x)} - \sqrt{\rho_{\alpha_1}(x)}|^2 dx
$$

$$
= \int_X |1 - \sqrt{\frac{d\alpha_1}{d\alpha_0}(x)}|^2 d\alpha_0(x).
$$

**Figure 7.4:** Comparison of Hellinger (first row), Wasserstein (row 2), partial optimal transport (row 3), and Wasserstein–Fisher–Rao (row 4) dynamic interpolations.

## 7.5. More General Mobility Functionals

It is possible to generalize the dynamic formulation (7.3) by considering other "mobility functions"  $\theta$  in place of the one defined in (7.5). A possible choice for this mobility functional is proposed in Dolbeault et al. [2009],

$$
\forall (a, b) \in \mathbb{R}_+ \times \mathbb{R}^d, \quad \theta(a, b) = a^{s-p} ||b||^p,
$$
\n(7.16)

where the parameter should satisfy  $p \geq 1$  and  $s \in [1, p]$  in order for  $\theta$  to be convex. Note that this definition should be handled with care in the case  $1 < s \leq p$  because  $\theta$ does not have a linear growth at infinity, so that solutions to (7.3) must be constrained to have a density with respect to the Lebesgue measure.

The case  $s = 1$  corresponds to the classical OT problem and the optimal value of (7.3) defines  $W_p(\alpha, \beta)$ . In this case,  $\theta$  is 1-homogeneous, so that solutions to (7.3) can be arbitrary measures. The case  $(s = 1, p = 2)$  is the initial setup considered in (7.3) to define  $\mathcal{W}_2$ .

## 7.6. Dynamic Formulation over the Paths Space 111

The limiting case  $s = p$  is also interesting, because it corresponds to a dual Sobolev norm  $W^{-1,p}$  and the value of (7.3) is then equal to

$$
\|\alpha - \beta\|_{W^{-1,p}(\mathbb{R}^d)}^p = \min_f \left\{ \int_{\mathbb{R}^d} f \mathbf{d}(\alpha - \beta) \ : \ \int_{\mathbb{R}^d} \|\nabla f(x)\|^q \, \mathrm{d}x \le 1 \right\}
$$

for  $1/q + 1/p = 1$ . In the limit  $(p = s, q) \rightarrow (1, \infty)$ , one recovers the  $W_1$  norm. The case  $s = p = 2$  corresponds to the Sobolev  $H^{-1}(\mathbb{R}^d)$  Hilbert norm defined in (8.15).

## 7.6. Dynamic Formulation over the Paths Space

There is a natural dynamical formulation of both classical and entropic regularized (see §4) formulations of OT, which is based on studying abstract optimization problems on the space  $\overline{X}$  of all possible paths  $\gamma : [0,1] \to \mathcal{X}$  (*i.e.* curves) on the space X. For simplicity, we assume  $\mathcal{X} = \mathbb{R}^d$ , but this extends to more general spaces such as geodesic spaces and graphs. Informally, the dynamic of "particles" between two input measures  $\alpha_0, \alpha_1$  at times  $t = 0, 1$  is described by a probability distribution  $\bar{\pi} \in \mathcal{M}_+^1(\bar{\mathcal{X}})$ . Such a distribution should satisfy that the distributions of starting and end points must match  $(\alpha_0, \alpha_1)$ , which is formally written using push-forward as

$$
\bar{\mathcal{U}}(\alpha_0,\alpha_1) \stackrel{\text{def.}}{=} \left\{ \bar{\pi} \in \mathcal{M}_+^1(\bar{\mathcal{X}}) \; : \; \bar{P}_{0\sharp}\bar{\pi} = \alpha_0, \bar{P}_{1\sharp}\bar{\pi} = \alpha_1 \right\},\,
$$

where, for any path  $\gamma \in \overline{\mathcal{X}}$ ,  $P_0(\gamma) = \gamma(0)$ ,  $P_1(\gamma) = \gamma(1)$ .

**OT over the space of paths.** The dynamical version of classical OT (2.15), formulated over the space of paths, then reads

$$
\mathcal{W}_2(\alpha_0, \alpha_1)^2 = \min_{\bar{\pi} \in \bar{\mathcal{U}}(\alpha_0, \alpha_1)} \int_{\bar{\mathcal{X}}} \mathcal{L}(\gamma)^2 d\bar{\pi}(\gamma), \tag{7.17}
$$

where  $\mathcal{L}(\gamma) = \int_0^1 |\gamma'(s)|^2 ds$  is the kinetic energy of a path  $s \in [0,1] \mapsto \gamma(s) \in \mathcal{X}$ . The connection between optimal couplings  $\pi^*$  and  $\bar{\pi}^*$  solving respectively (7.17) and (2.15) is that  $\bar{\pi}^*$  only gives mass to geodesics joining pairs of points in proportion prescribed by  $\pi^*$ . In the particular case of discrete measures, this means that

$$
\pi^* = \sum_{i,j} \mathbf{P}_{i,j} \delta_{(x_i, y_j)} \quad \text{and} \quad \bar{\pi}^* = \sum_{i,j} \mathbf{P}_{i,j} \delta_{\gamma_{x_i, y_j}},
$$

where  $\gamma_{x_i, y_j}$  is the geodesic between  $x_i$  and  $y_j$ . Furthermore, the measures defined by the distribution of the curve points  $\gamma(t)$  at time *t*, where  $\gamma$  is drawn following  $\bar{\pi}^*$ , *i.e.* 

$$
t \in [0, 1] \mapsto \alpha_t \stackrel{\text{def.}}{=} P_{t\sharp} \bar{\pi}^{\star} \quad \text{where} \quad P_t(\gamma) = \gamma(t) \in \mathcal{X}, \tag{7.18}
$$

is a solution to the dynamical formulation (7.3), *i.e.* it is the displacement interpolation. In the discrete case, one recovers (7.9).

**Entropic OT over the space of paths.** We now turn to the re-interpretation of entropic OT, defined in Chapter 4, using the space of paths. Similarly to (4.11), this is defined using a Kullback–Leibler projection, but this time of a reference measure over the space of paths  $\bar{\mathcal{K}}$  which is the distribution of a reversible Brownian motion (Wiener process), which has a uniform distribution at the initial and final times

$$
\min_{\bar{\pi} \in \bar{\mathcal{U}}(\alpha_0, \alpha_1)} \mathrm{KL}(\bar{\pi}|\bar{\mathcal{K}}). \tag{7.19}
$$

We refer to the review paper by Léonard [2014] for an overview of this problem and an historical account of the work of Schrödinger [1931]. One can show that the (unique) solution  $\bar{\pi}^*_{\varepsilon}$  to (7.19) converges to a solution of (7.17) as  $\varepsilon \to 0$ . Furthermore, this solution is linked to the solution of the static entropic OT problem (4.9) using Brownian bridge  $\bar{\gamma}_{x,y}^{\varepsilon} \in \bar{\mathcal{X}}$  (which are similar to fuzzy geodesic and converge to  $\delta_{\gamma_{x,y}}$  as  $\varepsilon \to 0$ ). In the discrete setting, this means that

$$
\pi_{\varepsilon}^{\star} = \sum_{i,j} \mathbf{P}_{\varepsilon,i,j}^{\star} \delta_{(x_i, y_j)} \quad \text{and} \quad \bar{\pi}_{\varepsilon}^{\star} = \sum_{i,j} \mathbf{P}_{\varepsilon,i,j}^{\star} \bar{\gamma}_{x_i, y_j}^{\varepsilon}, \tag{7.20}
$$

where  $\mathbf{P}_{\varepsilon,i,j}^{\star}$  can be computed using Sinkhorn's algorithm. Similarly to (7.18), one then can define an entropic interpolation as

$$
\alpha_{\varepsilon,t}\stackrel{\scriptscriptstyle\rm def.}{=} {\bf P}_{t\sharp}\bar\pi^\star_\varepsilon.
$$

Since the law  $P_{t\sharp} \bar{\gamma}_{x,y}^{\varepsilon}$  of the position at time *t* along a Brownian bridge is a Gaussian  $\mathcal{G}_{t(1-t)\varepsilon^2}(\cdot - \gamma_{x,y}(t))$  of variance  $t(1-t)\varepsilon^2$  centered at  $\gamma_{x,y}(t)$ , one can deduce that  $\alpha_{\varepsilon,t}$ is a Gaussian blurring of a set of traveling Diracs

$$
\alpha_{\varepsilon,t} = \sum_{i,j} \mathbf{P}^{\star}_{\varepsilon,i,j} \mathcal{G}_{t(1-t)\varepsilon^2}(\cdot - \gamma_{x_i,y_j}(t)).
$$

The resulting mixture of Brownian bridges is displayed on Figure 7.5.

Image /page/10/Figure/10 description: The image displays four panels, each illustrating a network of points and connections, labeled with increasing values of epsilon: \u03b5 = 0, \u03b5 = .05, \u03b5 = 0.2, and \u03b5 = 1. In the first panel (\u03b5 = 0), there are several disconnected pairs of red and blue dots, each connected by a single straight line. As epsilon increases, the connections become more numerous and complex. In the panels for \u03b5 = .05 and \u03b5 = 0.2, the lines begin to form more intricate paths and loops, with a gradient of red and blue colors indicating the flow or nature of the connections. By the last panel (\u03b5 = 1), the network is dense and chaotic, with many overlapping red and blue lines creating a complex, almost web-like structure connecting the points.

**Figure 7.5:** Samples from Brownian bridge paths associated to the Schrödinger entropic interpolation (7.20) over path space. Blue corresponds to  $t = 0$  and red to  $t = 1$ .

## 7.6. Dynamic Formulation over the Paths Space 113

Another way to describe this entropic interpolation  $(\alpha_t)_t$  is using a regularization of the Benamou–Brenier dynamic formulation (7.2), namely

$$
\min_{(\alpha_t, v_t)_t \text{ sat. (7.1)}} \int_0^1 \int_{\mathbb{R}^d} \left( \|v_t(x)\|^2 + \frac{\varepsilon}{4} \|\nabla \log(\alpha_t)(x)\|^2 \right) d\alpha_t(x) dt; \tag{7.21}
$$

see [Gentil et al., 2015, Chen et al., 2016a].