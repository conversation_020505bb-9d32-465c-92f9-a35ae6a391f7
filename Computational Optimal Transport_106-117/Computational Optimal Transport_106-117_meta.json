{"table_of_contents": [{"title": "7", "heading_level": null, "page_id": 0, "polygon": [[303.80615234375, 136.4359130859375], [322.36376953125, 136.4359130859375], [322.36376953125, 162.1202392578125], [303.80615234375, 162.1202392578125]]}, {"title": "Dynamic Formulations", "heading_level": null, "page_id": 0, "polygon": [[231.97021484375, 180.69289099526065], [388.65850673194615, 180.69289099526065], [388.65850673194615, 195.625732421875], [231.97021484375, 195.625732421875]]}, {"title": "7.1 Continuous Formulation", "heading_level": null, "page_id": 0, "polygon": [[103.5422276621787, 559.3232227488152], [267.10893512851897, 559.3232227488152], [267.10893512851897, 570.46240234375], [103.5422276621787, 570.46240234375]]}, {"title": "7.1. Continuous Formulation 103", "heading_level": null, "page_id": 1, "polygon": [[104.611083984375, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 103.7994384765625], [104.611083984375, 103.7994384765625]]}, {"title": "7.2. Discretization on Uniform Staggered Grids 105", "heading_level": null, "page_id": 3, "polygon": [[104.8355712890625, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.702880859375], [104.8355712890625, 103.702880859375]]}, {"title": "7.2 Discretization on Uniform Staggered Grids", "heading_level": null, "page_id": 3, "polygon": [[104.29253365973072, 436.36208530805686], [369.15055079559363, 436.36208530805686], [369.15055079559363, 448.41357421875], [104.29253365973072, 448.41357421875]]}, {"title": "7.3 Proximal Solvers", "heading_level": null, "page_id": 4, "polygon": [[103.5422276621787, 540.5791469194313], [226.5924112607099, 540.5791469194313], [226.5924112607099, 552.69580078125], [103.5422276621787, 552.69580078125]]}, {"title": "7.3. <PERSON><PERSON> <PERSON><PERSON> 107", "heading_level": null, "page_id": 5, "polygon": [[104.9852294921875, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 103.89599609375], [104.9852294921875, 103.89599609375]]}, {"title": "7.4 Dynamical Unbalanced OT", "heading_level": null, "page_id": 6, "polygon": [[103.5422276621787, 523.3345971563981], [283.6156670746634, 523.3345971563981], [283.6156670746634, 534.15673828125], [103.5422276621787, 534.15673828125]]}, {"title": "7.5 More General Mobility Functionals", "heading_level": null, "page_id": 8, "polygon": [[103.5422276621787, 473.85023696682464], [325.63280293757646, 473.85023696682464], [325.63280293757646, 485.8779296875], [103.5422276621787, 485.8779296875]]}, {"title": "7.6. Dynamic Formulation over the Paths Space 111", "heading_level": null, "page_id": 9, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.9925537109375], [105.04283965728274, 103.9925537109375]]}, {"title": "7.6 Dynamic Formulation over the Paths Space", "heading_level": null, "page_id": 9, "polygon": [[103.5422276621787, 237.67488151658767], [375.1529987760098, 237.67488151658767], [375.1529987760098, 248.539306640625], [103.5422276621787, 248.539306640625]]}, {"title": "7.6. Dynamic Formulation over the Paths Space 113", "heading_level": null, "page_id": 11, "polygon": [[104.29253365973072, 91.47109004739336], [519.2117503059975, 91.47109004739336], [519.2117503059975, 104.2170616113744], [104.29253365973072, 104.2170616113744]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 23], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4157, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 46], ["Equation", 4], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 64], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 34], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 59], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3502, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 403], ["Line", 70], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 52], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 637, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 45], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 1], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1320, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 648], ["Line", 67], ["TextInlineMath", 6], ["Equation", 5], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 55], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 751, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 18], ["Text", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_106-117"}