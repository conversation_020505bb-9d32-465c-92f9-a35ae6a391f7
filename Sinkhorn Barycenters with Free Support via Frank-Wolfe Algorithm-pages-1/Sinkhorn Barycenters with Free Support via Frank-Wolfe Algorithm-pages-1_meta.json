{"table_of_contents": [{"title": "Sinkhorn Barycenters with Free Support via\nFrank-Wolfe Algorithm", "heading_level": null, "page_id": 0, "polygon": [[117.0, 138.3486328125], [496.5, 138.3486328125], [496.5, 175.8603515625], [117.0, 175.8603515625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[285.0, 309.75], [330.0, 309.75], [330.0, 320.009765625], [285.0, 320.009765625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[67.647216796875, 444.75], [182.25, 444.75], [182.25, 458.26171875], [67.647216796875, 458.26171875]]}, {"title": "2 Background", "heading_level": null, "page_id": 1, "polygon": [[68.20751953125, 568.5], [177.75, 568.5], [177.75, 581.625], [68.20751953125, 581.625]]}, {"title": "3 Sinkhorn barycenters with <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 3, "polygon": [[67.90869140625, 212.25], [372.0, 212.25], [372.0, 224.876953125], [67.90869140625, 224.876953125]]}, {"title": "Algorithm 1 Frank<PERSON>Wolfe in Dual Banach Spaces", "heading_level": null, "page_id": 4, "polygon": [[68.25, 98.25], [347.25, 98.25], [347.25, 109.7314453125], [68.25, 109.7314453125]]}, {"title": "4 Algorithm: practical Sinkhorn barycenters", "heading_level": null, "page_id": 4, "polygon": [[68.25, 573.0], [395.05078125, 573.0], [395.05078125, 585.87890625], [68.25, 585.87890625]]}, {"title": "5 Convergence analysis", "heading_level": null, "page_id": 6, "polygon": [[68.25, 605.25], [243.6943359375, 605.25], [243.6943359375, 617.9765625], [68.25, 617.9765625]]}, {"title": "6 Experiments", "heading_level": null, "page_id": 8, "polygon": [[68.095458984375, 353.84765625], [182.8828125, 353.84765625], [182.8828125, 367.76953125], [68.095458984375, 367.76953125]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 10, "polygon": [[68.170166015625, 266.642578125], [171.75, 266.642578125], [171.75, 280.951171875], [68.170166015625, 280.951171875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[68.1328125, 477.59765625], [144.931640625, 477.59765625], [144.931640625, 491.51953125], [68.1328125, 491.51953125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 42], ["Text", 7], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2810, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 345], ["Line", 46], ["Text", 4], ["TextInlineMath", 2], ["Reference", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 927], ["Line", 58], ["TextInlineMath", 9], ["Equation", 6], ["Reference", 6], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 716], ["Line", 49], ["TextInlineMath", 6], ["Reference", 4], ["Equation", 3], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 831], ["Line", 65], ["TextInlineMath", 9], ["Reference", 4], ["SectionHeader", 2], ["Equation", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1047], ["Line", 58], ["TextInlineMath", 6], ["Equation", 3], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 638], ["Line", 46], ["TextInlineMath", 4], ["Reference", 4], ["Text", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 818], ["Line", 55], ["TextInlineMath", 7], ["Reference", 3], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 30], ["TextInlineMath", 4], ["Reference", 3], ["Caption", 2], ["Figure", 1], ["SectionHeader", 1], ["Text", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 30], ["TextInlineMath", 3], ["Caption", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 31], ["Reference", 7], ["ListItem", 6], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 37], ["ListItem", 15], ["Reference", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 38], ["ListItem", 16], ["Reference", 10], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 38], ["ListItem", 15], ["Reference", 8], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 26], ["Line", 6], ["ListItem", 3], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Sinkhorn Barycenters with Free Support via Frank-Wolfe Algorithm-pages-1"}