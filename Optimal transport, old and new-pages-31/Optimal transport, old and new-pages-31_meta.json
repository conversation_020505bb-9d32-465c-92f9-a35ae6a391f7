{"table_of_contents": [{"title": "Density control and local regularity", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.5], [386.25, 97.5], [386.25, 112.0517578125], [133.5, 112.0517578125]]}, {"title": "Pointwise estimates on the interpolant density", "heading_level": null, "page_id": 2, "polygon": [[133.5, 185.9150390625], [408.75, 185.9150390625], [408.75, 196.3564453125], [133.5, 196.3564453125]]}, {"title": "Democratic condition", "heading_level": null, "page_id": 9, "polygon": [[133.5, 438.75], [263.267578125, 438.75], [263.267578125, 451.6875], [133.5, 451.6875]]}, {"title": "Local Poincaré inequality", "heading_level": null, "page_id": 11, "polygon": [[133.27734375, 498.48046875], [284.25, 498.48046875], [284.25, 509.30859375], [133.27734375, 509.30859375]]}, {"title": "Back to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>–Le<PERSON>ler\ninequalities", "heading_level": null, "page_id": 14, "polygon": [[133.5, 48.0], [426.75, 48.0], [426.75, 73.86328125], [133.5, 73.86328125]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 17, "polygon": [[233.2353515625, 48.0], [358.5, 48.0], [358.5, 59.40966796875], [233.2353515625, 59.40966796875]]}, {"title": "540 19 Density control and local regularity", "heading_level": null, "page_id": 19, "polygon": [[133.5, 26.25], [325.125, 26.25], [325.125, 35.602294921875], [133.5, 35.602294921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 37], ["TextInlineMath", 3], ["ListItem", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 38], ["Text", 6], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 518], ["Line", 60], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2265, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 39], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 691], ["Line", 109], ["Equation", 8], ["TextInlineMath", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2501, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 659], ["Line", 135], ["Equation", 6], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4152, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 75], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1952, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 598], ["Line", 64], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1314, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 55], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1157, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 555], ["Line", 87], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3841, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 46], ["TextInlineMath", 8], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["Line", 63], ["Text", 4], ["Equation", 4], ["TextInlineMath", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 610], ["Line", 54], ["TextInlineMath", 6], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 72], ["Text", 6], ["Equation", 6], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2089, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 548], ["Line", 99], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3018, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 59], ["TextInlineMath", 6], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 623], ["Line", 137], ["Equation", 5], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2951, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 41], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 40], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 51], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1145, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-31"}