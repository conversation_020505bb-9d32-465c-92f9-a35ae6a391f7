{"table_of_contents": [{"title": "Interpolating between Optimal Transport and MMD\nusing Sinkhorn Divergences", "heading_level": null, "page_id": 0, "polygon": [[134.25, 81.75], [503.2265625, 81.75], [503.2265625, 110.6015625], [134.25, 110.6015625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[166.5, 245.56640625], [218.7421875, 245.56640625], [218.7421875, 256.78125], [166.5, 256.78125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[74.25, 586.265625], [169.5, 586.265625], [169.5, 597.09375], [74.25, 597.09375]]}, {"title": "1.1 Previous works", "heading_level": null, "page_id": 0, "polygon": [[326.25, 635.765625], [430.91015625, 635.765625], [430.91015625, 645.8203125], [326.25, 645.8203125]]}, {"title": "1.2 Interpolating between OT and MMD\nusing Sinkhorn divergences", "heading_level": null, "page_id": 1, "polygon": [[326.25, 230.25], [540.87890625, 230.25], [540.87890625, 253.107421875], [326.25, 253.107421875]]}, {"title": "1.3 Contributions", "heading_level": null, "page_id": 2, "polygon": [[74.25, 320.25], [171.75, 320.25], [171.75, 330.2578125], [74.25, 330.2578125]]}, {"title": "2 Proof of Theorem 1", "heading_level": null, "page_id": 2, "polygon": [[327.0, 150.0], [465.275390625, 150.0], [465.275390625, 160.875], [327.0, 160.875]]}, {"title": "2.1 Properties of the \\text{OT}_{\\varepsilon} loss", "heading_level": null, "page_id": 2, "polygon": [[326.25, 285.0], [486.0, 285.0], [486.0, 295.259765625], [326.25, 295.259765625]]}, {"title": "2.2 Sinkhorn and Haussdorf divergences", "heading_level": null, "page_id": 3, "polygon": [[74.25, 600.75], [285.0, 600.75], [285.0, 611.015625], [74.25, 611.015625]]}, {"title": "2.3 Proof of the Theorem", "heading_level": null, "page_id": 3, "polygon": [[326.25, 541.5], [463.5, 541.5], [463.5, 551.4609375], [326.25, 551.4609375]]}, {"title": "3 Computational scheme", "heading_level": null, "page_id": 4, "polygon": [[74.25, 156.0], [230.25, 156.0], [230.25, 167.0625], [74.25, 167.0625]]}, {"title": "3.1 The Sinkhorn algorithm(s)", "heading_level": null, "page_id": 4, "polygon": [[74.25, 438.75], [235.5, 438.75], [235.5, 448.59375], [74.25, 448.59375]]}, {"title": "3.2 Computing the Sinkhorn divergence and\nits gradients", "heading_level": null, "page_id": 5, "polygon": [[74.25, 123.0], [306.0, 123.0], [306.0, 145.01953125], [74.25, 145.01953125]]}, {"title": "github.com/jeanfeydy/global-divergences,", "heading_level": null, "page_id": 5, "polygon": [[339.75, 207.75], [548.25, 207.75], [548.25, 218.49609375], [339.75, 218.49609375]]}, {"title": "3.3 Scaling up to large datasets", "heading_level": null, "page_id": 5, "polygon": [[326.25, 349.5], [493.5, 349.5], [493.5, 359.841796875], [326.25, 359.841796875]]}, {"title": "4 Numerical illustration", "heading_level": null, "page_id": 6, "polygon": [[74.25, 460.5], [225.0, 460.5], [225.0, 471.41015625], [74.25, 471.41015625]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 7, "polygon": [[326.25, 543.0], [413.279296875, 543.0], [413.279296875, 554.16796875], [326.25, 554.16796875]]}, {"title": "Bibliography", "heading_level": null, "page_id": 8, "polygon": [[74.25, 74.25], [140.25, 74.25], [140.25, 84.111328125], [74.25, 84.111328125]]}, {"title": "A Standard results", "heading_level": null, "page_id": 10, "polygon": [[74.25, 72.75], [196.5, 72.75], [196.5, 83.724609375], [74.25, 83.724609375]]}, {"title": "A.1 The Kullback-Leibler divergence", "heading_level": null, "page_id": 10, "polygon": [[74.25, 148.5], [267.75, 148.5], [267.75, 158.361328125], [74.25, 158.361328125]]}, {"title": "A.2 SoftMin Operator", "heading_level": null, "page_id": 11, "polygon": [[74.25, 261.75], [195.75, 261.75], [195.75, 271.4765625], [74.25, 271.4765625]]}, {"title": "B Proofs", "heading_level": null, "page_id": 11, "polygon": [[74.25, 624.0], [138.2080078125, 624.0], [138.2080078125, 635.37890625], [74.25, 635.37890625]]}, {"title": "B.1 Dual Potentials", "heading_level": null, "page_id": 11, "polygon": [[74.25, 648.75], [182.25, 648.75], [182.25, 658.96875], [74.25, 658.96875]]}, {"title": "B.2 Proof of Proposition 2", "heading_level": null, "page_id": 12, "polygon": [[74.25, 591.6796875], [216.7998046875, 591.6796875], [216.7998046875, 600.9609375], [74.25, 600.9609375]]}, {"title": "B.3 Proof of Proposition 3", "heading_level": null, "page_id": 13, "polygon": [[74.25, 73.5], [216.75, 73.5], [216.75, 83.724609375], [74.25, 83.724609375]]}, {"title": "B.4 Proof of Proposition 4", "heading_level": null, "page_id": 14, "polygon": [[74.25, 406.5], [216.75, 406.5], [216.75, 416.8828125], [74.25, 416.8828125]]}, {"title": "B.5 Proof of the Metrization of the\nConvergence in Law", "heading_level": null, "page_id": 14, "polygon": [[74.25, 684.75], [261.0, 684.75], [261.0, 707.30859375], [74.25, 707.30859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 84], ["Text", 9], ["SectionHeader", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 960], ["Line", 137], ["TextInlineMath", 7], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 865], ["Line", 102], ["TextInlineMath", 6], ["Equation", 6], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 616, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1227], ["Line", 132], ["TextInlineMath", 13], ["Equation", 10], ["Text", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1057], ["Line", 148], ["Text", 9], ["Equation", 9], ["TextInlineMath", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 767], ["Line", 124], ["Text", 8], ["TextInlineMath", 7], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 684], ["Line", 113], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2908, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 64], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 103], ["ListItem", 27], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 30], ["Line", 13], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1168], ["Line", 120], ["TextInlineMath", 16], ["Equation", 13], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 967, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 1036], ["Line", 129], ["TextInlineMath", 15], ["Equation", 12], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1352], ["Line", 123], ["Equation", 12], ["Text", 10], ["TextInlineMath", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1133], ["Line", 161], ["Equation", 12], ["TextInlineMath", 10], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3445, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 759], ["Line", 69], ["TextInlineMath", 7], ["Text", 5], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1152, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Interpolating between Optimal Transport and MMD using Sinkhorn Divergences"}