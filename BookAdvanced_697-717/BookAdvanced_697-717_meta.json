{"table_of_contents": [{"title": "18.6.1 Connection to Latent-Variable Modeling", "heading_level": null, "page_id": 0, "polygon": [[133.5, 597.0], [382.5, 597.0], [382.5, 608.30859375], [133.5, 608.30859375]]}, {"title": "18.6.2 Relationship with Partial Least Squares", "heading_level": null, "page_id": 2, "polygon": [[133.5, 111.75], [378.75, 111.75], [378.75, 123.6533203125], [133.5, 123.6533203125]]}, {"title": "18.6.3 Pre-Conditioning for Feature Selection", "heading_level": null, "page_id": 3, "polygon": [[133.5, 583.5], [375.92578125, 583.5], [375.92578125, 596.3203125], [133.5, 596.3203125]]}, {"title": "18.7 Feature Assessment and the Multiple-Testing\nProblem", "heading_level": null, "page_id": 5, "polygon": [[132.75, 585.75], [452.25, 585.75], [452.25, 614.109375], [132.75, 614.109375]]}, {"title": "18.7.1 The False Discovery Rate", "heading_level": null, "page_id": 9, "polygon": [[133.5, 167.25], [308.25, 167.25], [308.25, 178.7607421875], [133.5, 178.7607421875]]}, {"title": "18.7.2 Asymmetric Cutpoints and the SAM Procedure", "heading_level": null, "page_id": 12, "polygon": [[133.5, 290.25], [417.1640625, 290.25], [417.1640625, 301.25390625], [133.5, 301.25390625]]}, {"title": "692 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 14, "polygon": [[132.0, 88.5], [332.89453125, 88.5], [332.89453125, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "18.7.3 A Bayesian Interpretation of the FDR", "heading_level": null, "page_id": 14, "polygon": [[133.5, 306.75], [375.328125, 306.75], [375.328125, 318.65625], [133.5, 318.65625]]}, {"title": "18.8 Bibliographic Notes", "heading_level": null, "page_id": 15, "polygon": [[132.75, 399.0], [294.0, 399.0], [294.0, 411.46875], [132.75, 411.46875]]}, {"title": "694 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 16, "polygon": [[132.0, 89.25], [332.89453125, 89.25], [332.89453125, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "Exercises", "heading_level": null, "page_id": 16, "polygon": [[132.75, 212.25], [191.1005859375, 212.25], [191.1005859375, 224.876953125], [132.75, 224.876953125]]}, {"title": "Ex. 18.17 Equivalence between <PERSON><PERSON> and plug-in methods.", "heading_level": null, "page_id": 19, "polygon": [[132.75, 349.5], [451.529296875, 349.5], [451.529296875, 359.6484375], [132.75, 359.6484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 39], ["Text", 6], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3240, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 39], ["Text", 5], ["TextInlineMath", 3], ["ListItem", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 48], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 569, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 43], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5149, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 850, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 43], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["TableCell", 114], ["Line", 67], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6194, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 54], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 62], ["TextInlineMath", 5], ["Text", 4], ["Equation", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 40], ["TableCell", 36], ["Text", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1149, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 47], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 793, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 53], ["Text", 6], ["ListItem", 3], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 43], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 69], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 45], ["TextInlineMath", 7], ["Equation", 5], ["SectionHeader", 2], ["Picture", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 40], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 67], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 40], ["ListItem", 6], ["TextInlineMath", 3], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 525], ["Line", 40], ["TextInlineMath", 6], ["ListItem", 5], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["Line", 37], ["ListItem", 6], ["Text", 3], ["ListGroup", 3], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 26], ["ListItem", 3], ["Text", 2], ["Equation", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_697-717"}