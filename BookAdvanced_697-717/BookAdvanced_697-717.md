Algorithm 18.1 Supervised Principal Components.

- 1. Compute the standardized univariate regression coefficients for the outcome as a function of each feature separately.
- 2. For each value of the threshold  $\theta$  from the list  $0 \leq \theta_1 < \theta_2 < \cdots < \theta_K$ :
  - (a) Form a reduced data matrix consisting of only those features whose univariate coefficient exceeds  $\theta$  in absolute value, and compute the first m principal components of this matrix.
  - (b) Use these principal components in a regression model to predict the outcome.
- 3. Pick  $\theta$  (and m) by cross-validation.

step (2b). For survival problems, <PERSON>'s proportional hazards regression model is widely used; hence we use the score test from this model in step (1) and the multivariate Cox model in step (2b). The details are not essential for understanding the basic method; they may be found in <PERSON> et al. (2006).

Figure 18.14 shows the results of supervised principal components in this example. We used a Cox-score cutoff of 3.53, yielding 27 genes, where the value 3.53 was found through 10-fold cross-validation. We then computed the first principal component  $(m = 1)$  using just this subset of the data, as well as its value for each of the test observations. We included this as a quantitative predictor in a Cox regression model, and its likelihood-ratio significance was  $p = 0.005$ . When dichotomized (using the mean score on the training data as a threshold), it clearly separates the patients in the test set into low and high risk groups (middle-right panel of Figure 18.14,  $p = 0.006$ .

The top-right panel of Figure 18.14 uses the top scoring gene (dichotomized) alone as a predictor of survival. It is not significant on the test set. Likewise, the lower-right panel shows the dichotomized principal component using all the training data, which is also not significant.

Our procedure allows  $m > 1$  principal components in step (2a). However, the supervision in step (1) encourages the principal components to align with the outcome, and thus in most cases only the first or first few components tend to be useful for prediction. In the mathematical development below, we consider only the first component, but extensions to more than one component can be derived in a similar way.

## 18.6.1 Connection to Latent-Variable Modeling

A formal connection between supervised principal components and the underlying cell type model (Figure 18.13) can be seen through a latent variable model for the data. Suppose we have a response variable  $Y$  which is related to an underlying latent variable U by a linear model

$$
Y = \beta_0 + \beta_1 U + \varepsilon. \tag{18.32}
$$

In addition, we have measurements on a set of features  $X_j$  indexed by  $j \in \mathcal{P}$ (for pathway), for which

$$
X_j = \alpha_{0j} + \alpha_{1j}U + \epsilon_j, \quad j \in \mathcal{P}.
$$
 (18.33)

The errors  $\varepsilon$  and  $\epsilon_j$  are assumed to have mean zero and are independent of all other random variables in their respective models.

We also have many additional features  $X_k, k \notin \mathcal{P}$  which are independent of U. We would like to identify  $P$ , estimate U, and hence fit the prediction model (18.32). This is a special case of a latent-structure model, or single-component factor-analysis model (Mardia et al., 1979, see also Section 14.7). The latent factor U is a continuous version of the cell type conceptualized in Figure 18.13.

The supervised principal component algorithm can be seen as a method for fitting this model:

- The screening step (1) estimates the set  $P$ .
- Given  $\hat{\mathcal{P}}$ , the largest principal component in step (2a) estimates the latent factor U.
- Finally, the regression fit in step (2b) estimates the coefficient in model (18.32).

Step (1) is natural, since on average the regression coefficient is nonzero only if  $\alpha_{1j}$  is non-zero. Hence this step should select the features  $j \in \mathcal{P}$ . Step (2a) is natural if we assume that the errors  $\epsilon_j$  have a Gaussian distribution, with the same variance. In this case the principal component is the maximum likelihood estimate for the single factor model (Mardia et al., 1979). The regression in (2b) is an obvious final step.

Suppose there are a total of  $p$  features, with  $p_1$  features in the relevant set P. Then if p and  $p_1$  grow but  $p_1$  is small relative to p, one can show (under reasonable conditions) that the leading supervised principal component is consistent for the underlying latent factor. The usual leading principal component may not be consistent, since it can be contaminated by the presence of a large number of "noise" features.

Finally, suppose that the threshold used in step (1) of the supervised principal component procedure yields a large number of features for computation of the principal component. Then for interpretational purposes, as well as for practical uses, we would like some way of finding a reduced a set of features that approximates the model. Pre-conditioning (Section 18.6.3) is one way of doing this.

## 18.6.2 Relationship with Partial Least Squares

Image /page/2/Picture/2 description: A yellow, cartoonish face with wide, staring eyes and an open mouth is depicted. The face is screaming or in shock, with its hands pressed against its ears. The background is white, and the figure is outlined in black.

Supervised principal components is closely related to partial least squares regression (Section 3.5.2). Bair et al. (2006) found that the key to the good performance of supervised principal components was the filtering out of noisy features in step (2a). Partial least squares (Section 3.5.2) downweights noisy features, but does not throw them away; as a result a large number of noisy features can contaminate the predictions. However, a modification of the partial least squares procedure has been proposed that has a similar flavor to supervised principal components [Brown et al.  $(1991)$ , Nadler and Coifman (2005), for example]. We select the features as in steps (1) and (2a) of supervised principal components, but then apply PLS (rather than principal components) to these features. For our current discussion, we call this "thresholded PLS."

Thresholded PLS can be viewed as a noisy version of supervised principal components, and hence we might not expect it to work as well in practice. Assume the variables are all standardized. The first PLS variate has the form

$$
\mathbf{z} = \sum_{j \in \mathcal{P}} \langle \mathbf{y}, \mathbf{x}_j \rangle \mathbf{x}_j, \tag{18.34}
$$

and can be thought of as an estimate of the latent factor  $U$  in model (18.33). In contrast, the supervised principal components direction  $\hat{u}$  satisfies

$$
\hat{\mathbf{u}} = \frac{1}{d^2} \sum_{j \in \mathcal{P}} \langle \hat{\mathbf{u}}, \mathbf{x}_j \rangle \mathbf{x}_j,
$$
(18.35)

where d is the leading singular value of  $\mathbf{X}_{\mathcal{P}}$ . This follows from the definition of the leading principal component. Hence thresholded PLS uses weights which are the inner product of y with each of the features, while supervised principal components uses the features to derive a "self-consistent" estimate  $\hat{u}$ . Since many features contribute to the estimate  $\hat{u}$ , rather than just the single outcome  $y$ , we can expect  $\hat{u}$  to be less noisy than  $z$ . In fact, if there are  $p_1$  features in the set  $P$ , and  $N$ ,  $p$  and  $p_1$  go to infinity with  $p_1/N \to 0$ , then it can be shown using the techniques in Bair et al. (2006) that

$$
\mathbf{z} = \mathbf{u} + O_p(1)
$$
  
\n
$$
\hat{\mathbf{u}} = \mathbf{u} + O_p(\sqrt{p_1/N}),
$$
\n(18.36)

where  $\bf{u}$  is the true (unobservable) latent variable in the model (18.32), (18.33).

We now present a simulation example to compare the methods numerically. There are  $N = 100$  samples and  $p = 5000$  genes. We generated the data as follows:

Image /page/3/Figure/1 description: A heatmap displays gene expression data. On the left, a color bar indicates the range of expression levels, with blue representing low expression and yellow representing high expression. The main part of the heatmap shows a matrix of colored cells. There are distinct blocks of yellow and blue cells, suggesting clusters of genes with similar expression patterns. Specifically, there are two horizontal bands of yellow cells separated by a band of blue cells, and below that, another band of blue cells. The right side of the heatmap shows a more scattered pattern of yellow and blue cells, indicating less clear clustering.

FIGURE 18.15. Heatmap of the outcome (left column) and first 500 genes from a realization from model (18.37). The genes are in the columns, and the samples are in the rows.

$$
x_{ij} = \begin{cases} 3 + \epsilon_{ij} & \text{if } i \le 50, \\ 4 + \epsilon_{ij} & \text{if } i > 50 \end{cases} \qquad j = 1, ..., 50
$$
  

$$
x_{ij} = \begin{cases} 1.5 + \epsilon_{ij} & \text{if } 1 \le i \le 25 \text{ or } 51 \le i \le 75 \\ 5.5 + \epsilon_{ij} & \text{if } 26 \le i \le 50 \text{ or } 76 \le i \le 100 \end{cases} \qquad j = 51, ..., 250
$$
  

$$
x_{ij} = \epsilon_{ij} \qquad j = 251, ..., 5000
$$
  

$$
y_i = 2 \cdot \frac{1}{50} \sum_{j=1}^{50} x_{ij} + \epsilon_i
$$

(18.37)

where  $\epsilon_{ij}$  and  $\epsilon_i$  are independent normal random variables with mean 0 and standard deviations 1 and 1.5, respectively. Thus in the first 50 genes, there is an average difference of 1 unit between samples 1–50 and 51–100, and this difference correlates with the outcome  $y$ . The next 200 genes have a large average difference of 4 units between samples  $(1–25, 51–75)$  and  $(26–50,$ 76–100), but this difference is uncorrelated with the outcome. The rest of the genes are noise. Figure 18.15 shows a heatmap of a typical realization, with the outcome at the left, and the first 500 genes to the right.

We generated 100 simulations from this model, and summarize the test error results in Figure 18.16. The test errors of principal components and partial least squares are shown at the right of the plot; both are badly affected by the noisy features in the data. Supervised principal components and thresholded PLS work best over a wide range of the number of selected features, with the former showing consistently lower test errors.

While this example seems "tailor-made" for supervised principal components, its good performance seems to hold in other simulated and real datasets (Bair et al., 2006).

## 18.6.3 Pre-Conditioning for Feature Selection

Supervised principal components can yield lower test errors than competing methods, as shown in Figure 18.16. However, it does not always produce a sparse model involving only a small number of features (genes). Even if the thresholding in Step (1) of the algorithm yields a relatively small number

Image /page/4/Figure/1 description: This is a line graph showing the relative root mean square test error as a function of the number of features. The x-axis is labeled "Number of Features" and ranges from 0 to 5000, with tick marks at 0, 50, 100, 150, 200, 250, 300, and 5000. The y-axis is labeled "Relative Root Mean Square Test Error" and ranges from 1.00 to 1.25, with tick marks at 1.00, 1.05, 1.10, 1.15, 1.20, and 1.25. There are two lines plotted on the graph: "Thresholded PLS" (orange) and "Supervised Principal Components" (blue). Both lines show an initial decrease in error as the number of features increases, followed by an increase. The "Thresholded PLS" line generally shows a higher error than the "Supervised Principal Components" line, especially for a larger number of features. Error bars are shown for each data point.

FIGURE 18.16. Root mean squared test error  $(\pm$  one standard error), for supervised principal components and thresholded PLS on 100 realizations from model (18.37). All methods use one component, and the errors are relative to the noise standard deviation (the Bayes error is 1.0). For both methods, different values for the filtering threshold were tried and the number of features retained is shown on the horizontal axis. The extreme right points correspond to regular principal components and partial least squares, using all the genes.

of features, it may be that some of the omitted features have sizable inner products with the supervised principal component (and could act as a good surrogate). In addition, highly correlated features will tend to be chosen together, and there may be great deal of redundancy in the set of selected features.

The lasso (Sections 18.4 and 3.4.2), on the other hand, produces a sparse model from the data. How do the test errors of the two methods compare on the simulated example of the last section? Figure 18.17 shows the test errors for one realization from model (18.37) for the lasso, supervised principal components, and the pre-conditioned lasso (described below).

We see that supervised principal components (orange curve) reaches its lowest error when about 50 features are included in the model, which is the correct number for the simulation. Although a linear model in the first 50 features is optimal, the lasso (green) is adversely affected by the large number of noisy features, and starts overfitting when far fewer are in the model.

Can we get the low test error of supervised principal components along with the sparsity of the lasso? This is the goal of *pre-conditioning* (Paul et al., 2008). In this approach, one first computes the supervised principal component predictor  $\hat{y}_i$  for each observation in the training set (with the

Image /page/5/Figure/1 description: This is a line graph showing the relationship between the number of features in a model and the mean test error for three different methods: Lasso, Supervised Principal Components, and Preconditioned Lasso. The x-axis represents the number of features in the model, ranging from 0 to 250. The y-axis represents the mean test error, ranging from 3.0 to 4.4. The Lasso method (green line) shows a relatively stable mean test error around 3.8 to 4.0 as the number of features increases. The Supervised Principal Components method (orange line) initially shows a decrease in mean test error as the number of features increases, reaching a minimum around 3.0 at approximately 40 features, and then gradually increases to around 3.25 at 250 features. The Preconditioned Lasso method (purple line) shows a sharp decrease in mean test error as the number of features increases from 0 to about 30, reaching a minimum of approximately 3.0, and then the line ends. The legend indicates the colors corresponding to each method.

FIGURE 18.17. Test errors for the lasso, supervised principal components, and pre-conditioned lasso, for one realization from model (18.37). Each model is indexed by the number of non-zero features. The supervised principal component path is truncated at 250 features. The lasso self-truncates at 100, the sample size (see Section 18.4). In this case, the pre-conditioned lasso achieves the lowest error with about 25 features.

threshold selected by cross-validation). Then we apply the lasso with  $\hat{y}_i$  as the outcome variable, in place of the usual outcome  $y_i$ . All features are used in the lasso fit, not just those that were retained in the thresholding step in supervised principal components. The idea is that by first denoising the outcome variable, the lasso should not be as adversely affected by the large number of noise features. Figure 18.17 shows that pre-conditioning (purple curve) has been successful here, yielding much lower test error than the usual lasso, and as low (in this case) as for supervised principal components. It also can achieve this using less features. The usual lasso, applied to the raw outcome, starts to overfit more quickly than the pre-conditioned version. Overfitting is not a problem, since the outcome variable has been denoised. We usually select the tuning parameter for the pre-conditioned lasso on more subjective grounds, like parsimony.

Pre-conditioning can be applied in a variety of settings, using initial estimates other than supervised principal components and post-processors other than the lasso. More details may be found in Paul et al. (2008).

### 18.7 Feature Assessment and the Multiple-Testing Problem

In the first part of this chapter we discuss prediction models in the  $p \gg N$ setting. Here we consider the more basic problem of assessing the significance of each of the p features. Consider the protein mass spectrometry example of Section 18.4.1. In that problem, the scientist might not be interested in predicting whether a given patient has prostate cancer. Rather the goal might be to identify proteins whose abundance differs between normal and cancer samples, in order to enhance understanding of the disease and suggest targets for drug development. Thus our goal is to assess the significance of individual features. This assessment is usually done without the use of a multivariate predictive model like those in the first part of this chapter. The feature assessment problem moves our focus from prediction to the traditional statistical topic of multiple hypothesis testing. For the remainder of this chapter we will use  $M$  instead of  $p$  to denote the number of features, since we will frequently be referring to p-values.

TABLE 18.4. Subset of the 12, 625 genes from microarray study of radiation sensitivity. There are a total of 44 samples in the normal group and 14 in the radiation sensitive group; we only show three samples from each group.

|             | Normal   |       |          |     | Radiation Sensitive |          |           |     |
|-------------|----------|-------|----------|-----|---------------------|----------|-----------|-----|
| Gene 1      | 7.85     | 29.74 | 29.50    | ... | 17.20               | $-50.75$ | $-18.89$  | ... |
| Gene 2      | 15.44    | 2.70  | 19.37    | ... | 6.57                | $-7.41$  | 79.18     | ... |
| Gene 3      | $-1.79$  | 15.52 | $-3.13$  | ... | $-8.32$             | 12.64    | 4.75      | ... |
| Gene 4      | $-11.74$ | 22.35 | $-36.11$ | ... | $-52.17$            | 7.24     | $-2.32$   | ... |
| ...         | ...      | ...   | ...      | ... | ...                 | ...      | ...       | ... |
| Gene 12,625 | $-14.09$ | 32.77 | 57.78    | ... | $-32.84$            | 24.09    | $-101.44$ | ... |

Consider, for example, the microarray data in Table 18.4, taken from a study on the sensitivity of cancer patients to ionizing radiation treatment (Rieger et al., 2004). Each row consists of the expression of genes in 58 patient samples: 44 samples were from patients with a normal reaction, and 14 from patients who had a severe reaction to radiation. The measurements were made on oligo-nucleotide microarrays. The object of the experiment was to find genes whose expression was different in the radiation sensitive group of patients. There are  $M = 12,625$  genes altogether; the table shows the data for some of the genes and samples for illustration.

To identify informative genes, we construct a two-sample t-statistic for each gene.

$$
t_j = \frac{\bar{x}_{2j} - \bar{x}_{1j}}{\text{se}_j},\tag{18.38}
$$

where  $\bar{x}_{kj} = \sum_{i \in C_{\ell}} x_{ij}/N_{\ell}$ . Here  $C_{\ell}$  are the indices of the  $N_{\ell}$  samples in group  $\ell$ , where  $\ell = 1$  is the normal group and  $\ell = 2$  is the sensitive group. The quantity  $se_i$  is the pooled within-group standard error for gene j:

Image /page/7/Figure/1 description: A histogram shows the distribution of t-statistics. The x-axis is labeled 't-statistics' and ranges from -4 to 4. The y-axis represents frequency, with tick marks at 0, 200, 400, 600, and 800. Orange bars represent the histogram, and a blue line outlines the shape of the distribution, which appears to be a normal distribution centered around 0. The distribution is bell-shaped, with the highest frequency around t=0 and frequencies decreasing as the t-statistic moves away from 0 in either direction.

FIGURE 18.18. Radiation sensitivity microarray example. A histogram of the 12, 625 t-statistics comparing the radiation-sensitive versus insensitive groups. Overlaid in blue is the histogram of the t-statistics from 1000 permutations of the sample labels.

$$
se_j = \hat{\sigma}_j \sqrt{\frac{1}{N_1} + \frac{1}{N_2}}; \quad \hat{\sigma}_j^2 = \frac{1}{N_1 + N_2 - 2} \left( \sum_{i \in C_1} (x_{ij} - \bar{x}_{1j})^2 + \sum_{i \in C_2} (x_{ij} - \bar{x}_{2j})^2 \right).
$$
\n(18.39)

A histogram of the 12,625 t-statistics is shown in orange in Figure 18.18, ranging in value from  $-4.7$  to 5.0. If the  $t_j$  values were normally distributed we could consider any value greater than two in absolute value to be significantly large. This would correspond to a significance level of about 5%. Here there are 1189 genes with  $|t_i| \geq 2$ . However with 12,625 genes we would expect many large values to occur by chance, even if the grouping is unrelated to any gene. For example, if the genes were independent (which they are surely not), the number of falsely significant genes would have a binomial distribution with mean  $12,625 \cdot 0.05 = 631.3$  and standard deviation 24.5; the actual 1189 is way out of range.

How do we assess the results for all 12,625 genes? This is called the multiple testing problem. We can start as above by computing a p-value for each gene. This can be done using the theoretical  $t$ -distribution probabilities, which assumes the features are normally distributed. An attractive alternative approach is to use the permutation distribution, since it avoids assumptions about the distribution of the data. We compute (in principle) all  $K = \binom{58}{14}$  permutations of the sample labels, and for each permutation k compute the t-statistics  $t_j^k$ . Then the p-value for gene j is

686 18. High-Dimensional Problems:  $p \gg N$ 

$$
p_j = \frac{1}{K} \sum_{k=1}^{K} I(|t_j^k| > |t_j|). \tag{18.40}
$$

Of course,  $\binom{58}{14}$  is a large number (around  $10^{13}$ ) and so we can't enumerate all of the possible permutations. Instead we take a random sample of the possible permutations; here we took a random sample of  $K = 1000$ permutations.

To exploit the fact that the genes are similar (e.g., measured on the same scale), we can instead pool the results for all genes in computing the p-values.

$$
p_j = \frac{1}{MK} \sum_{j'=1}^{M} \sum_{k=1}^{K} I(|t_{j'}^k| > |t_j|). \tag{18.41}
$$

This also gives more granular  $p$ -values than does  $(18.40)$ , since there many more values in the pooled null distribution than there are in each individual null distribution.

Using this set of  $p$ -values, we would like to test the hypotheses:

$$
H_{0j} = \text{treatment has no effect on gene } j
$$
\n
$$
versus \tag{18.42}
$$

$$
H_{1j} = \text{treatment has an effect on gene } j
$$

for all  $j = 1, 2, ..., M$ . We reject  $H_{0j}$  at level  $\alpha$  if  $p_j < \alpha$ . This test has type-I error equal to  $\alpha$ ; that is, the probability of falsely rejecting  $H_{0j}$  is  $\alpha$ .

Now with many tests to consider, it is not clear what we should use as an overall measure of error. Let  $A_j$  be the event that  $H_{0j}$  is falsely rejected; by definition  $Pr(A_i) = \alpha$ . The *family-wise error rate* (FWER) is the probability of at least one false rejection, and is a commonly used overall measure of error. In detail, if  $A = \bigcup_{j=1}^{M} A_j$  is the event of at least one false rejection, then the FWER is  $Pr(A)$ . Generally  $Pr(A) \gg \alpha$  for large M, and depends on the correlation between the tests. If the tests are independent each with type-I error rate  $\alpha$ , then the family-wise error rate of the collection of tests is  $(1 - (1 - \alpha)^M)$ . On the other hand, if the tests have positive dependence, that is  $Pr(A_i|A_k) > Pr(A_i)$ , then the FWER will be less than  $(1 - (1 - \alpha)^M)$ . Positive dependence between tests often occurs in practice, in particular in genomic studies.

One of the simplest approaches to multiple testing is the Bonferroni method. It makes each individual test more stringent, in order to make the FWER equal to at most  $\alpha$ : we reject  $H_{0j}$  if  $p_j < \alpha/M$ . It is easy to show that the resulting FWER is  $\leq \alpha$  (Exercise 18.16). The Bonferroni method can be useful if  $M$  is relatively small, but for large  $M$  it is too conservative, that is, it calls too few genes significant.

In our example, if we test at level say  $\alpha = 0.05$ , then we must use the threshold  $0.05/12, 625 = 3.9 \times 10^{-6}$ . None of the 12,625 genes had a *p*-value this small.

There are variations to this approach that adjust the individual  $p$ -values to achieve an FWER of at most  $\alpha$ , with some approaches avoiding the assumption of independence; see, e.g., Dudoit et al. (2002b).

#### 18.7.1 The False Discovery Rate

A different approach to multiple testing does not try to control the FWER, but focuses instead on the proportion of falsely significant genes. As we will see, this approach has a strong practical appeal.

Table 18.5 summarizes the theoretical outcomes of M hypothesis tests. Note that the family-wise error rate is  $Pr(V \geq 1)$ . Here we instead focus

**TABLE 18.5.** Possible outcomes from M hypothesis tests. Note that  $V$  is the number of false-positive tests; the type-I error rate is  $E(V)/M_0$ . The type-II error rate is  $E(T)/M_1$ , and the power is  $1 - E(T)/M_1$ .

|             | Called Not Significant | Called Significant | Total   |
|-------------|------------------------|--------------------|---------|
| $H_0$ True  | U                      | V                  | $M_{0}$ |
| $H_0$ False | T                      | S                  | $M_{1}$ |
| Total       | $M - R$                | R                  | M       |

on the false discovery rate

$$
FDR = E(V/R). \tag{18.43}
$$

In the microarray setting, this is the expected proportion of genes that are incorrectly called significant, among the  $R$  genes that are called significant. The expectation is taken over the population from which the data are generated. Benjamini and Hochberg (1995) first proposed the notion of false discovery rate, and gave a testing procedure (Algorithm 18.2) whose FDR is bounded by a user-defined level  $\alpha$ . The Benjamini–Hochberg (BH) procedure is based on p-values; these can be obtained from an asymptotic approximation to the test statistic (e.g., Gaussian), or a permutation distribution, as is done here.

If the hypotheses are independent, Benjamini and Hochberg (1995) show that regardless of how many null hypotheses are true and regardless of the distribution of the p-values when the null hypothesis is false, this procedure has the property

$$
\text{FDR} \le \frac{M_0}{M} \alpha \le \alpha. \tag{18.45}
$$

For illustration we chose  $\alpha = 0.15$ . Figure 18.19 shows a plot of the ordered *p*-values  $p_{(j)}$ , and the line with slope 0.15/12625.

Algorithm 18.2 Benjamini–Hochberg (BH) Method.

- 1. Fix the false discovery rate  $\alpha$  and let  $p_{(1)} \leq p_{(2)} \leq \cdots \leq p_{(M)}$  denote the ordered p-values
- 2. Define

$$
L = \max\left\{j : p_{(j)} < \alpha \cdot \frac{j}{M}\right\}.\tag{18.44}
$$

3. Reject all hypotheses  $H_{0j}$  for which  $p_j \leq p_{(L)}$ , the BH rejection threshold.

Image /page/10/Figure/6 description: This is a quantile-quantile plot showing p-values for genes ordered by their p-value. The x-axis represents the genes ordered by p-value, ranging from 1 to 100. The y-axis represents the p-value on a logarithmic scale, ranging from 5\*10^-6 to 5\*10^-3. There are two sets of points plotted: red points on the left and blue points on the right. A dashed vertical line is drawn at x=10. A solid black line is drawn as a reference. The red points generally fall below the reference line, indicating smaller p-values than expected under the null hypothesis. The blue points generally fall above the reference line, indicating larger p-values than expected under the null hypothesis.

FIGURE 18.19. Microarray example continued. Shown is a plot of the ordered p-values  $p_{(j)}$  and the line  $0.15 \cdot (j/12, 625)$ , for the Benjamini–Hochberg method. The largest j for which the p-value  $p_{(j)}$  falls below the line, gives the BH threshold. Here this occurs at  $j = 11$ , indicated by the vertical line. Thus the BH method calls significant the 11 genes (in red) with smallest p-values.

Algorithm 18.3 The Plug-in Estimate of the False Discovery Rate.

- 1. Create K permutations of the data, producing t-statistics  $t_j^k$  for features  $j = 1, 2, \ldots, M$  and permutations  $k = 1, 2, \ldots, K$ .
- 2. For a range of values of the cut-point  $C$ , let

$$
R_{\text{obs}} = \sum_{j=1}^{M} I(|t_j| > C), \quad \widehat{E(V)} = \frac{1}{K} \sum_{j=1}^{M} \sum_{k=1}^{K} I(|t_j^k| > C). \quad (18.46)
$$

3. Estimate the FDR by  $\widehat{\text{FDR}} = \widehat{\text{E}(V)}/R_{\text{obs}}$ .

Starting at the left and moving right, the BH method finds the last time that the p-values fall below the line. This occurs at  $j = 11$ , so we reject the 11 genes with smallest  $p$ -values. Note that the cutoff occurs at the 11th smallest p-value, 0.00012, and the 11th largest of the values  $|t_i|$  is 4.101 Thus we reject the 11 genes with  $|t_i| \geq 4.101$ .

From our brief description, it is not clear how the BH procedure works; that is, why the corresponding FDR is at most 0.15, the value used for  $\alpha$ . Indeed, the proof of this fact is quite complicated (Benjamini and Hochberg, 1995).

A more direct way to proceed is a plug-in approach. Rather than starting with a value for  $\alpha$ , we fix a cut-point for our t-statistics, say the value 4.101 that appeared above. The number of observed values  $|t_i|$  equal or greater than 4.101 is 11. The total number of permutation values  $|t_j^k|$  equal or greater than 4.101 is 1518, for an average of  $1518/1000 = 1.518$  per permutation. Thus a direct estimate of the false discovery rate is  $\widehat{F}D\widehat{R} =$  $1.518/11 \approx 14\%$ . Note that  $14\%$  is approximately equal to the value of  $\alpha = 0.15$  used above (the difference is due to discreteness). This procedure is summarized in Algorithm 18.3. To recap:

The plug-in estimate of FDR of Algorithm 18.3 is equivalent to the BH procedure of Algorithm 18.2, using the permutation p-values (18.40).

This correspondence between the BH method and the plug-in estimate is not a coincidence. Exercise 18.17 shows that they are equivalent in general. Note that this procedure makes no reference to  $p$ -values at all, but rather works directly with the test statistics.

The plug-in estimate is based on the approximation

$$
E(V/R) \approx \frac{E(V)}{E(R)},
$$
\n(18.47)

and in general  $\widehat{FDR}$  is a consistent estimate of  $FDR$  (Storey, 2002; Storey et al., 2004). Note that the numerator  $\widetilde{E(V)}$  actually estimates  $(M/M_0)E(V)$ , since the permutation distribution uses M rather  $M_0$  null hypotheses. Hence if an estimate of  $M_0$  is available, a better estimate of FDR can be obtained from  $(\hat{M}_0/M) \cdot \widehat{FDR}$ . Exercise 18.19 shows a way to estimate  $M_0$ . The most conservative (upwardly biased) estimate of FDR uses  $M_0 = M$ . Equivalently, an estimate of  $M_0$  can be used to improve the BH method, through relation (18.45).

The reader might be surprised that we chose a value as large as 0.15 for  $\alpha$ , the FDR bound. We must remember that the FDR is not the same as type-I error, for which 0.05 is the customary choice. For the scientist, the false discovery rate is the expected proportion of false positive genes among the list of genes that the statistician tells him are significant. Microarray experiments with FDRs as high as 0.15 might still be useful, especially if they are exploratory in nature.

#### 18.7.2 Asymmetric Cutpoints and the SAM Procedure

In the testing methods described above, we used the absolute value of the test statistic  $t_i$ , and hence applied the same cut-points to both positive and negative values of the statistic. In some experiments, it might happen that most or all of the differentially expressed genes change in the positive direction (or all in the negative direction). For this situation it is advantageous to derive separate cut-points for the two cases.

The significance analysis of microarrays (SAM) approach offers a way of doing this. The basis of the SAM method is shown in Figure 18.20. On the vertical axis we have plotted the ordered test statistics  $t_{(1)} \leq t_{(2)} \leq \cdots \leq t_{(n)}$  $t_{(M)}$ , while the horizontal axis shows the expected order statistics from the permutations of the data:  $\tilde{t}_{(j)} = (1/K) \sum_{k=1}^{K} t^{k}_{(j)}$ , where  $t^{k}_{(1)} \leq t^{k}_{(2)} \leq \cdots \leq$  $t^{k}_{(M)}$  are the ordered test statistics from permutation k.

Two lines are drawn, parallel to the  $45^{\circ}$  line,  $\Delta$  units away. Starting at the origin and moving to the right, we find the first place that the genes leave the band. This defines the upper cutpoint  $C_{hi}$  and all genes beyond that point are called significant (marked red). Similarly we find the lower cutpoint  $C_{low}$  for genes in the bottom left corner. Thus each value of the tuning parameter  $\Delta$  defines upper and lower cutpoints, and the plug-in estimate  $\widehat{FDR}$  for each of these cutpoints is estimated as before. Typically a range of values of  $\Delta$  and associated FDR values are computed, from which a particular pair are chosen on subjective grounds.

The advantage of the SAM approach lies in the possible asymmetry of the cutpoints. In the example of Figure 18.20, with  $\Delta = 0.71$  we obtain 11 significant genes; they are all in the upper right. The data points in the bottom left never leave the band, and hence  $C_{low} = -\infty$ . Hence for this value of  $\Delta$ , no genes are called significant on the left (negative) side. We do not impose symmetry on the cutpoints, as was done in Section 18.7.1, as there is no reason to assume similar behavior at the two ends.

Image /page/13/Figure/1 description: This is a quantile-quantile plot, also known as a Q-Q plot. The x-axis is labeled "Expected Order Statistics" and ranges from -4 to 4. The y-axis is labeled "t-statistic" and also ranges from -4 to 4. A blue line of points follows a diagonal pattern, indicating a potential relationship between the expected order statistics and the t-statistic. Several dashed lines are present: a horizontal dashed line at y=4 labeled "Chi", and two diagonal dashed lines that create a band around the main blue line. A small triangle labeled "Δ" is shown above the blue line near the top right of the plot, indicating a deviation or difference. A few red points are scattered above the blue line in the upper right quadrant, suggesting potential outliers or deviations from the expected distribution.

FIGURE 18.20. SAM plot for the radiation sensitivity microarray data. On the vertical axis we have plotted the ordered test statistics, while the horizontal axis shows the expected order statistics of the test statistics from permutations of the data. Two lines are drawn, parallel to the  $45^{\circ}$  line,  $\Delta$  units away from it. Starting at the origin and moving to the right, we find the first place that the genes leave the band. This defines the upper cut-point  $C_{hi}$  and all genes beyond that point are called significant (marked in red). Similarly we define a lower cutpoint  $C_{1ow}$ . For the particular value of  $\Delta = 0.71$  in the plot, no genes are called significant in the bottom left.

# 18. High-Dimensional Problems: $p \gg N$

There is some similarity between this approach and the asymmetry possible with likelihood-ratio tests. Suppose we have a log-likelihood  $\ell_0(t_i)$  under the null-hypothesis of no effect, and a log-likelihood  $\ell(t_i)$  under the alternative. Then a likelihood ratio test amounts to rejecting the null-hypothesis if

$$
\ell(t_j) - \ell_0(t_j) > \Delta,\tag{18.48}
$$

for some  $\Delta$ . Depending on the likelihoods, and particularly their relative values, this can result in a different threshold for  $t_i$  than for  $-t_i$ . The SAM procedure rejects the null-hypothesis if

$$
|t_{(j)} - \tilde{t}_{(j)}| > \Delta \tag{18.49}
$$

Again, the threshold for each  $t_{(i)}$  depends on the corresponding value of the null value  $\tilde{t}_{(j)}$ .

#### 18.7.3 A Bayesian Interpretation of the FDR

Image /page/14/Picture/7 description: A yellow, cartoonish depiction of Edvard Munch's 'The Scream' is shown against a white background. The figure has wide, circular eyes, an open mouth, and is holding its hands to its head. The figure is emerging from a black, rectangular shape.

There is an interesting Bayesian view of the FDR, developed in Storey (2002) and Efron and Tibshirani (2002). First we need to define the positive false discovery rate (pFDR) as

$$
pFDR = E\left[\frac{V}{R}\middle|R > 0\right].\tag{18.50}
$$

The additional term *positive* refers to the fact that we are only interested in estimating an error rate where positive findings have occurred. It is this slightly modified version of the FDR that has a clean Bayesian interpretation. Note that the usual FDR [expression (18.43)] is not defined if  $Pr(R = 0) > 0.$ 

Let  $\Gamma$  be a rejection region for a single test; in the example above we used  $\Gamma = (-\infty, -4.10) \cup (4.10, \infty)$ . Suppose that M identical simple hypothesis tests are performed with the i.i.d. statistics  $t_1, \ldots, t_M$  and rejection region Γ. We define a random variable  $Z_j$  which equals 0 if the jth null hypothesis is true, and 1 otherwise. We assume that each pair  $(t_j, Z_j)$  are i.i.d random variables with

$$
t_j | Z_j \sim (1 - Z_j) \cdot F_0 + Z_j \cdot F_1 \tag{18.51}
$$

for some distributions  $F_0$  and  $F_1$ . This says that each test statistic  $t_j$  comes from one of two distributions:  $F_0$  if the null hypothesis is true, and  $F_1$ otherwise. Letting  $Pr(Z_j = 0) = \pi_0$ , marginally we have:

$$
t_j \sim \pi_0 \cdot F_0 + (1 - \pi_0) \cdot F_1. \tag{18.52}
$$

Then it can be shown (Efron et al., 2001; Storey, 2002) that

18.8 Bibliographic Notes 693

$$
pFDR(\Gamma) = Pr(Z_j = 0 | t_j \in \Gamma).
$$
\n(18.53)

Hence under the mixture model (18.51), the pFDR is the posterior probability that the null hypothesis it true, given that test statistic falls in the rejection region for the test; that is, given that we reject the null hypothesis (Exercise 18.20).

The false discovery rate provides a measure of accuracy for tests based on an entire rejection region, such as  $|t_j| \geq 2$ . But if the FDR of such a test is say 10%, then a gene with say  $t_i = 5$  will be more significant than a gene with  $t_j = 2$ . Thus it is of interest to derive a local (gene-specific) version of the FDR. The q-value (Storey, 2003) of a test statistic  $t_i$  is defined to be the smallest FDR over all rejection regions that reject  $t_j$ . That is, for symmetric rejection regions, the q-value for  $t<sub>j</sub> = 2$  is defined to be the FDR for the rejection region  $\Gamma = \{-(\infty, -2) \cup (2, \infty)\}\.$  Thus the q-value for  $t_j = 5$  will be smaller than that for  $t_j = 2$ , reflecting the fact that  $t_j = 5$ is more significant than  $t_j = 2$ . The *local false discovery rate* (Efron and Tibshirani, 2002) at  $t = t_0$  is defined to be

$$
Pr(Z_j = 0 | t_j = t_0).
$$
\n(18.54)

This is the (positive) FDR for an infinitesimal rejection region surrounding the value  $t_i = t_0$ .

## 18.8 Bibliographic Notes

Many references were given at specific points in this chapter; we give some additional ones here. Dudoit et al. (2002a) give an overview and comparison of discrimination methods for gene expression data. Levina (2002) does some mathematical analysis comparing diagonal LDA to full LDA, as  $p, N \to \infty$  with  $p > N$ . She shows that with reasonable assumptions diagonal LDA has a lower asymptotic error rate than full LDA. Tibshirani et al. (2001a) and Tibshirani et al. (2003) proposed the nearest shrunken-centroid classifier. Zhu and Hastie (2004) study regularized logistic regression. Highdimensional regression and the lasso are very active areas of research, and many references are given in Section 3.8.5. The fused lasso was proposed by Tibshirani et al. (2005), while Zou and Hastie (2005) introduced the elastic net. Supervised principal components is discussed in Bair and Tibshirani (2004) and Bair et al. (2006). For an introduction to the analysis of censored survival data, see Kalbfleisch and Prentice (1980).

Microarray technology has led to a flurry of statistical research: see for example the books by Speed (2003), Parmigiani et al. (2003), Simon et al. (2004), and Lee (2004).

The false discovery rate was proposed by Benjamini and Hochberg (1995), and studied and generalized in subsequent papers by these authors and

# 18. High-Dimensional Problems: $p \gg N$

many others. A partial list of papers on FDR may be found on Yoav Benjamini's homepage. Some more recent papers include Efron and Tibshirani (2002), Storey (2002), Genovese and Wasserman (2004), Storey and Tibshirani (2003) and Benjamini and Yekutieli (2005). Dudoit et al. (2002b) review methods for identifying differentially expressed genes in microarray studies.

# Exercises

Ex. 18.1 For a coefficient estimate  $\hat{\beta}_j$ , let  $\hat{\beta}_j/||\hat{\beta}_j||_2$  be the normalized version. Show that as  $\lambda \to \infty$ , the normalized ridge-regression estimates converge to the renormalized partial-least-squares one-component estimates.

Ex. 18.2 Nearest shrunken centroids and the lasso. Consider a (naive Bayes) Gaussian model for classification in which the features  $j = 1, 2, \ldots, p$  are assumed to be independent within each class  $k = 1, 2, \ldots, K$ . With observations  $i = 1, 2, ..., N$  and  $C_k$  equal to the set of indices of the  $N_k$ observations in class k, we observe  $x_{ij} \sim N(\mu_j + \mu_{jk}, \sigma_j^2)$  for  $i \in C_k$  with  $\sum_{k=1}^{K} \mu_{jk} = 0$ . Set  $\hat{\sigma}_j^2 = s_j^2$ , the pooled within-class variance for feature j, and consider the lasso-style minimization problem

$$
\min_{\{\mu_j, \mu_{jk}\}} \left\{ \frac{1}{2} \sum_{j=1}^p \sum_{k=1}^K \sum_{i \in C_k} \frac{(x_{ij} - \mu_j - \mu_{jk})^2}{s_j^2} + \lambda \sqrt{N_k} \sum_{j=1}^p \sum_{k=1}^K \frac{|\mu_{jk}|}{s_j} \right\} (18.55)
$$

Show that the solution is equivalent to the nearest shrunken centroid estimator (18.5), with  $s_0$  set to zero, and  $M_k$  equal to  $1/N_k$  instead of  $1/N_k - 1/N$  as before.

Ex. 18.3 Show that the fitted coefficients for the regularized multiclass logistic regression problem (18.10) satisfy  $\sum_{k=1}^{K} \hat{\beta}_{kj} = 0, j = 1, ..., p$ . What about the  $\hat{\beta}_{k0}$ ? Discuss issues with these constant parameters, and how they can be resolved.

Ex. 18.4 Derive the computational formula (18.15) for ridge regression. [Hint: Use the first derivative of the penalized sum-of-squares criterion to show that if  $\lambda > 0$ , then  $\hat{\beta} = \mathbf{X}^T s$  for some  $s \in \mathbb{R}^N$ .

Ex. 18.5 Prove the theorem  $(18.16)$ – $(18.17)$  in Section 18.3.5, by decomposing  $\beta$  and the rows of **X** into their projections into the column space of **V** and its complement in  $\mathbb{R}^p$ .

Ex. 18.6 Show how the theorem in Section 18.3.5 can be applied to regularized discriminant analysis [Section 4.14 and Equation (18.9)].

Ex. 18.7 Consider a linear regression problem where  $p \gg N$ , and assume the rank of **X** is N. Let the SVD of  $X = UDV^T = RV^T$ , where **R** is  $N \times N$  nonsingular, and **V** is  $p \times N$  with orthonormal columns.

- (a) Show that there are infinitely many least-squares solutions all with zero residuals.
- (b) Show that the ridge-regression estimate for  $\beta$  can be written

$$
\hat{\beta}_{\lambda} = \mathbf{V}(\mathbf{R}^T \mathbf{R} + \lambda \mathbf{I})^{-1} \mathbf{R}^T \mathbf{y}
$$
 (18.56)

(c) Show that when  $\lambda = 0$ , the solution  $\hat{\beta}_0 = \mathbf{V} \mathbf{D}^{-1} \mathbf{U}^T \mathbf{y}$  has residuals all equal to zero, and is unique in that it has the smallest Euclidean norm amongst all zero-residual solutions.

Ex. 18.8 Data Piling. Exercise 4.2 shows that the two-class LDA solution can be obtained by a linear regression of a binary response vector y consisting of  $-1$ s and  $+1$ s. The prediction  $\hat{\beta}^T x$  for any x is (up to a scale and shift) the LDA score  $\delta(x)$ . Suppose now that  $p \gg N$ .

- (a) Consider the linear regression model  $f(x) = \alpha + \beta^{T} x$  fit to a binary response  $Y \in \{-1, +1\}$ . Using Exercise 18.7, show that there are infinitely many directions defined by  $\hat{\beta}$  in  $\mathbb{R}^p$  onto which the data project to exactly two points, one for each class. These are known as data piling directions (Ahn and Marron, 2005).
- (b) Show that the distance between the projected points is  $2/||\hat{\beta}||$ , and hence these directions define separating hyperplanes with that margin.
- (c) Argue that there is a single maximal data piling direction for which this distance is largest, and is defined by  $\hat{\beta}_0 = \mathbf{V} \mathbf{D}^{-1} \mathbf{U}^T \mathbf{y} = \mathbf{X}^- \mathbf{y}$ , where  $\mathbf{X} = \mathbf{U} \mathbf{D} \mathbf{V}^T$  is the SVD of **X**.

Ex. 18.9 Compare the data piling direction of Exercise 18.8 to the direction of the optimal separating hyperplane (Section 4.5.2) qualitatively. Which makes the widest margin, and why? Use a small simulation to demonstrate the difference.

Ex. 18.10 When  $p \gg N$ , linear discriminant analysis (see Section 4.3) is degenerate because the within-class covariance matrix W is singular. One version of regularized discriminant analysis (4.14) replaces W by a ridged version  $W + \lambda I$ , leading to a regularized discriminant function  $\delta_{\lambda}(x)$  $x^T(\mathbf{W} + \lambda \mathbf{I})^{-1}(\bar{x}_1 - \bar{x}_{-1})$ . Show that  $\delta_0(x) = \lim_{\lambda \downarrow 0} \delta_\lambda(x)$  corresponds to the maximal data piling direction defined in Exercise 18.8.

Ex. 18.11 Suppose you have a sample of N pairs  $(x_i, y_i)$ , with  $y_i$  binary and  $x_i \in \mathbb{R}^1$ . Suppose also that the two classes are separable; e.g., for each

pair  $i, i'$  with  $y_i = 0$  and  $y_{i'} = 1$ ,  $x_{i'} - x_i \ge C$  for some  $C > 0$ . You wish to fit a linear logistic regression model logitPr( $Y = 1|X$ ) =  $\alpha + \beta X$  by maximum-likelihood. Show that  $\hat{\beta}$  is undefined.

Ex. 18.12 Suppose we wish to select the ridge parameter  $\lambda$  by 10-fold crossvalidation in a  $p \gg N$  situation (for any linear model). We wish to use the computational shortcuts described in Section 18.3.5. Show that we need only to reduce the  $N \times p$  matrix **X** to the  $N \times N$  matrix **R** once, and can use it in all the cross-validation runs.

Ex. 18.13 Suppose our  $p > N$  predictors are presented as an  $N \times N$  innerproduct matrix  $\mathbf{K} = \mathbf{X}\mathbf{X}^T$ , and we wish to fit the equivalent of a linear logistic regression model in the original features with quadratic regularization. Our predictions are also to be made using inner products; a new  $x_0$ is presented as  $k_0 = \mathbf{X}x_0$ . Let  $\mathbf{K} = \mathbf{U}\mathbf{D}^2\mathbf{U}^T$  be the eigen-decomposition of **K**. Show that the predictions are given by  $\hat{f}_0 = k_0^T \hat{\alpha}$ , where

- (a)  $\hat{\alpha} = \mathbf{U} \mathbf{D}^{-1} \hat{\beta}$ , and
- (b)  $\hat{\beta}$  is the ridged logistic regression estimate with input matrix  $\mathbf{R} =$ UD.

Argue that the same approach can be used for any appropriate kernel matrix K.

Ex. 18.14 Distance weighted 1-NN classification. Consider the 1-nearestneighbor method (Section 13.3) in a two-class classification problem. Let  $d_{+}(x_0)$  be the shortest distance to a training observation in class  $+1$ , and likewise  $d_-(x_0)$  the shortest distance for class  $-1$ . Let  $N_-\,$  be the number of samples in class  $-1$ ,  $N_+$  the number in class  $+1$ , and  $N = N_- + N_+$ .

(a) Show that

$$
\delta(x_0) = \log \frac{d_{-}(x_0)}{d_{+}(x_0)}\tag{18.57}
$$

can be viewed as a nonparametric discriminant function corresponding to 1-NN classification. [*Hint*: Show that  $\hat{f}_+(x_0) = \frac{1}{N+d+(x_0)}$  can be viewed as a nonparametric estimate of the density in class +1 at  $x_0$ .

- (b) How would you modify this function to introduce class prior probabilities  $\pi_+$  and  $\pi_-$  different from the sample-priors  $N_+/N$  and  $N_-/N$ ?
- (c) How would you generalize this approach for K-NN classification?

Ex. 18.15 Kernel PCA. In Section 18.5.2 we show how to compute the principal component variables Z from an uncentered inner-product matrix **K**. We compute the eigen-decomposition  $(I - M)K(I - M) = UD^2U^T$ , with  $M = 11<sup>T</sup>/N$ , and then  $Z = UD$ . Suppose we have the inner-product vector  $\mathbf{k}_0$ , containing the N inner-products between a new point  $x_0$  and each of the  $x_i$  in our training set. Show that the (centered) projections of  $x_0$  onto the principal-component directions are given by

$$
\mathbf{z}_0 = \mathbf{D}^{-1} \mathbf{U}^T (\mathbf{I} - \mathbf{M}) \left[ \mathbf{k}_0 - \mathbf{K} \mathbf{1}/N \right]. \tag{18.58}
$$

Ex. 18.16 Bonferroni method for multiple comparisons. Suppose we are in a multiple-testing scenario with null hypotheses  $H_{0j}$ ,  $j = 1, 2, ..., M$ , and corresponding p-values  $p_j$ ,  $i = 1, 2, ..., M$ . Let A be the event that at least one null hypothesis is falsely rejected, and let  $A_i$  be the event that the jth null hypothesis is falsely rejected. Suppose that we use the Bonferroni method, rejecting the j<sup>th</sup> null hypothesis if  $p_j < \alpha/M$ .

- (a) Show that  $Pr(A) \leq \alpha$ . [Hint:  $Pr(A_j \cup A_{j'}) = Pr(A_j) + Pr(A_{j'}) \Pr(A_j \cap A_{j'})]$
- (b) If the hypotheses  $H_{0j}$ ,  $j = 1, 2, ..., M$ , are independent, then  $Pr(A)$  $1 - \Pr(A^C) = 1 - \prod_{j=1}^{M} \Pr(A_j^C) = 1 - (1 - \alpha/M)^M$ . Use this to show that  $Pr(A) \approx \alpha$  in this case.

### Ex. 18.17 Equivalence between Benjamini –Hochberg and plug-in methods.

- (a) In the notation of Algorithm 18.2, show that for rejection threshold  $p_0 = p_{(L)}$ , a proportion of at most  $p_0$  of the permuted values  $t_j^k$ exceed  $|T|_{(L)}$  where  $|T|_{(L)}$  is the Lth largest value among the  $|t_j|$ . Hence show that the plug-in FDR estimate  $\widehat{FDR}$  is less than or equal to  $p_0 \cdot M/L = \alpha$ .
- (b) Show that the cut-point  $|T|_{(L+1)}$  produces a test with estimated FDR greater than  $\alpha$ .

Ex. 18.18 Use result (18.53) to show that

$$
pFDR = \frac{\pi_0 \cdot \{Type I error of \Gamma\}}{\pi_0 \cdot \{Type I error of \Gamma\} + \pi_1 \{Power of \Gamma\}}
$$
(18.59)

(Storey, 2003).

Ex. 18.19 Consider the data in Table 18.4 of Section (18.7), available from the book website.

- (a) Using a symmetric two-sided rejection region based on the t-statistic, compute the plug-in estimate of the FDR for various values of the cut-point.
- (b) Carry out the BH procedure for various FDR levels  $\alpha$  and show the equivalence of your results, with those from part (a).

- 698 18. High-Dimensional Problems:  $p \gg N$
- (c) Let  $(q_{.25}, q_{.75})$  be the quartiles of the t-statistics from the permuted datasets. Let  $\hat{\pi}_0 = {\{\#t_j \in (q_{.25}, q_{.75})\}}/(.5M)$ , and set  $\hat{\pi}_0 = \min(\hat{\pi}_0, 1)$ . Multiply the FDR estimates from (a) by  $\hat{\pi}_0$  and examine the results.
- (d) Give a motivation for the estimate in part (c).

(Storey, 2003)

Ex. 18.20 Proof of result (18.53). Write

$$
pFDR = E\left(\frac{V}{R}|R>0\right) \tag{18.60}
$$

$$
= \sum_{k=1}^{M} \mathcal{E}\left[\frac{V}{R}|R=k\right] \Pr(R=k|R>0)
$$
 (18.61)

Use the fact that given  $R = k$ , V is a binomial random variable, with k trials and probability of success  $Pr(H = 0|T \in \Gamma)$ , to complete the proof.