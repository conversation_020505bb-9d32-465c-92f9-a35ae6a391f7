{"table_of_contents": [{"title": "3\nLinear Methods for Regression", "heading_level": null, "page_id": 0, "polygon": [[131.3349609375, 108.75], [387.0, 108.75], [387.0, 162.03515625], [131.3349609375, 162.03515625]]}, {"title": "3.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[132.0, 354.0], [243.24609375, 354.0], [243.24609375, 367.76953125], [132.0, 367.76953125]]}, {"title": "44 3. Linear Methods for Regression", "heading_level": null, "page_id": 1, "polygon": [[132.0, 89.25], [301.81640625, 89.25], [301.81640625, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "3.2 Linear Regression Models and Least Squares", "heading_level": null, "page_id": 1, "polygon": [[132.75, 109.5], [440.771484375, 109.5], [440.771484375, 123.8466796875], [132.75, 123.8466796875]]}, {"title": "3.2.1 Example: Prostate Cancer", "heading_level": null, "page_id": 6, "polygon": [[133.5, 500.25], [305.25, 500.25], [305.25, 512.40234375], [133.5, 512.40234375]]}, {"title": "50 3. Linear Methods for Regression", "heading_level": null, "page_id": 7, "polygon": [[132.0, 88.5], [302.25, 88.5], [302.25, 98.25], [132.0, 98.25]]}, {"title": "3.2.2 The Gauss–<PERSON><PERSON> Theorem", "heading_level": null, "page_id": 8, "polygon": [[133.5, 265.5], [318.75, 265.5], [318.75, 277.083984375], [133.5, 277.083984375]]}, {"title": "52 3. Linear Methods for Regression", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [301.5, 88.5], [301.5, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "3.2.3 Multiple Regression from Simple Univariate Regression", "heading_level": null, "page_id": 9, "polygon": [[132.75, 406.5], [453.0, 406.5], [453.0, 417.75], [132.75, 417.75]]}, {"title": "56 3. Linear Methods for Regression", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "3.2.4 Multiple Outputs", "heading_level": null, "page_id": 13, "polygon": [[132.75, 112.5], [258.1875, 112.5], [258.1875, 123.169921875], [132.75, 123.169921875]]}, {"title": "3.3 Subset Selection", "heading_level": null, "page_id": 14, "polygon": [[132.0, 110.25], [267.0029296875, 110.25], [267.0029296875, 123.6533203125], [132.0, 123.6533203125]]}, {"title": "3.3.1 Best-Subset Selection", "heading_level": null, "page_id": 14, "polygon": [[133.5, 429.75], [279.75, 429.75], [279.75, 440.47265625], [133.5, 440.47265625]]}, {"title": "3.3.2 Forward- and Backward-Stepwise Selection", "heading_level": null, "page_id": 15, "polygon": [[132.45556640625, 477.0], [390.75, 477.0], [390.75, 488.8125], [132.45556640625, 488.8125]]}, {"title": "60 3. Linear Methods for Regression", "heading_level": null, "page_id": 17, "polygon": [[132.0, 89.25], [301.517578125, 89.25], [301.517578125, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "3.3.3 Forward-Stagewise Regression", "heading_level": null, "page_id": 17, "polygon": [[132.75, 362.25], [324.0, 362.25], [324.0, 373.376953125], [132.75, 373.376953125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 22], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 50], ["ListItem", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5386, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 77], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1752, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 35], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1273, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 57], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 42], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 748, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 59], ["TextInlineMath", 4], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["TableCell", 216], ["Line", 40], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7854, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 55], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 56], ["Text", 6], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 949, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 45], ["TextInlineMath", 5], ["Text", 2], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 412], ["Line", 34], ["ListItem", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 43], ["TextInlineMath", 6], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 50], ["Equation", 7], ["TextInlineMath", 5], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 94], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1473, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 42], ["ListItem", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 42], ["Text", 7], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_62-79"}