{"table_of_contents": [{"title": "Curvature-dimension bounds", "heading_level": null, "page_id": 0, "polygon": [[133.5, 280.5], [306.59765625, 280.5], [306.59765625, 292.166015625], [133.5, 292.166015625]]}, {"title": "402 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 3, "polygon": [[133.4267578125, 26.25], [243.0, 26.25], [243.0, 35.360595703125], [133.4267578125, 35.360595703125]]}, {"title": "From differential to integral curvature-dimension bounds", "heading_level": null, "page_id": 3, "polygon": [[133.5, 486.0], [469.5, 486.0], [469.5, 497.3203125], [133.5, 497.3203125]]}, {"title": "404 14 Ricci curvature", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.25], [243.24609375, 26.25], [243.24609375, 35.52978515625], [133.5, 35.52978515625]]}, {"title": "406 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 7, "polygon": [[133.5, 25.934326171875], [243.544921875, 25.934326171875], [243.544921875, 35.360595703125], [133.5, 35.360595703125]]}, {"title": "Distortion coefficients", "heading_level": null, "page_id": 8, "polygon": [[133.5, 342.75], [264.0, 342.75], [264.0, 353.84765625], [133.5, 353.84765625]]}, {"title": "414 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 15, "polygon": [[132.978515625, 26.103515625], [243.544921875, 26.103515625], [243.544921875, 35.52978515625], [132.978515625, 35.52978515625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 79], ["Equation", 3], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 60], ["ListItem", 6], ["TextInlineMath", 2], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1162, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 59], ["TextInlineMath", 8], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 51], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 67], ["Equation", 7], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1014, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 462], ["Line", 47], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1280, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 525], ["Line", 89], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9429, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 52], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1006, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 40], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 56], ["Equation", 3], ["Text", 3], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 31], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 606, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 61], ["Text", 6], ["Equation", 4], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7391, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["Line", 63], ["Equation", 4], ["Text", 4], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3426, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 45], ["TextInlineMath", 6], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1957, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 635], ["Line", 85], ["Equation", 7], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 713], ["Line", 100], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1399, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 74], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1250, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-23"}