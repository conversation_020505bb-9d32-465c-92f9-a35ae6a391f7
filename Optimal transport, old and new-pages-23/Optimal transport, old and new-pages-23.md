$$
\Delta_{\perp} f = \Delta f - \left\langle \nabla^2 f \cdot \widehat{\nabla \psi}, \widehat{\nabla \psi} \right\rangle,
$$

and next,

$$
L_{\perp}f = \Delta_{\perp}f - \nabla V \cdot \nabla f,
$$

$$
\Gamma_{2,\perp}(\psi) = L_{\perp} \frac{|\nabla \psi|^2}{2} - \nabla \psi \cdot \nabla (L_{\perp} \psi) - 2|(\nabla^2 \psi) \cdot \widehat{\nabla \psi}|^2 - 2|(\nabla^2 \psi) \cdot \widehat{\nabla \psi}|^2.
$$

Then

$$
\Gamma_{2,\perp}(\psi) = \frac{(L_{\perp}\psi)^2}{N-1} + \text{Ric}_{N,\nu}(\nabla\psi) + \left\|\nabla^2_{\perp}\psi - \left(\frac{\Delta_{\perp}\psi}{n-1}\right)I_{n-1}\right\|^2 + \frac{n-1}{(N-1)(N-n)}\left[\left(\frac{N-n}{n-1}\right)\Delta_{\perp}\psi + \nabla V \cdot \nabla\psi\right]^2 + \sum_{j=2}^n (\partial_{1j}\psi)^2.
$$

# Curvature-dimension bounds

It is convenient to declare that a Riemannian manifold  $M$ , equipped with its volume measure, satisfies the curvature-dimension estimate  $CD(K, N)$  if its Ricci curvature is bounded below by K and its dimension is bounded above by N: Ric  $\geq K$ ,  $n \leq N$ . (As usual, Ric  $\geq K$  is a shorthand for " $\forall x$ , Ric<sub>x</sub> ≥ Kg<sub>x</sub>.") The number K might be positive or negative. If the reference measure is not the volume, but  $\nu = e^{-V}$  vol, then the correct definition is  $Ric_{N,\nu} \geq K$ .

Most of the previous discussion is summarized by Theorem 14.8 below, which is all the reader needs to know about Ricci curvature to understand the rest of the proofs in this course. For convenience I shall briefly recall the notation:

- *measures:* vol is the volume on  $M, \nu = e^{-V}$  vol is the reference measure;
- operators:  $\Delta$  is the Laplace(–Beltrami) operator on  $M$ ,  $\nabla^2$  is the Hessian operator,  $L = \Delta - \nabla V \cdot \nabla$  is the modified Laplace operator, and  $\Gamma_2(\psi) = L(|\nabla \psi|^2/2) - \nabla \psi \cdot \nabla (L\psi);$
- tensors: Ric is the Ricci curvature bilinear form, and  $Ric_{N,\nu}$  is the modified Ricci tensor:  $Ric_{N,\nu} = Ric + \nabla^2 V - (\nabla V \otimes \nabla V)/(N - n),$ where  $\nabla^2 V(x)$  is the Hessian of V at x, identified to a bilinear form;

- functions:  $\psi$  is an arbitrary function; in formulas involving the  $\Gamma_2$ formalism it will be assumed to be of class  $C^3$ , while in formulas involving Jacobian determinants it will only be assumed to be semiconvex;
- geodesic paths: If  $\psi$  is a given function on M,  $\gamma(t,x) = T_t(x) =$  $\exp_x((t-t_0)\nabla\psi(x))$  is the geodesic starting from x with velocity  $\dot{\gamma}(t_0,x) = \nabla \psi(x)$ , evaluated at time  $t \in [0,1]$ ; it is assumed that  $\mathcal{J}(t,x)$  does not vanish for  $t \in (0,1)$ ; the starting time  $t_0$  may be the origin  $t_0 = 0$ , or any other time in [0, 1];
- Jacobian determinants:  $\mathcal{J}(t,x)$  is the Jacobian determinant of  $T_t(x)$ (with respect to the reference measure  $\nu$ , not with respect to the standard volume),  $\ell = -\log \mathcal{J}$ , and  $\mathcal{D} = \mathcal{J}^{1/N}$  is the mean distortion associated with  $(T_t)$ ;
- the dot means differentiation with respect to time;
- finally, the subscript  $\perp$  in  $\mathcal{J}_\perp$ ,  $\mathcal{D}_\perp$ ,  $\Gamma_{2,\perp}$  means that the direction of motion  $\dot{\gamma} = \nabla \psi$  has been taken out (see above for precise definitions).

Theorem 14.8 (CD $(K, N)$  curvature-dimension bound). Let M be a Riemannian manifold of dimension n, and let  $K \in \mathbb{R}, N \in [n,\infty]$ . Then, the conditions below are all equivalent if they are required to hold true for arbitrary data; when they are fulfilled, M is said to satisfy the  $CD(K, N)$  curvature-dimension bound:

(i) Ric<sub>N, $\nu \geq K$ ;</sub> (ii)  $\Gamma_2(\psi) \geq \frac{(L\psi)^2}{N}$  $\frac{X\psi_j}{N} + K|\nabla\psi|^2;$ (iii)  $\ddot{\ell} \geq \frac{(\dot{\ell})^2}{N}$  $\frac{\varepsilon}{N} + K|\dot{\gamma}|^2.$ 

If  $N < \infty$ , this is also equivalent to

$$
(iv) \ \ddot{\mathcal{D}} + \left(\frac{K|\dot{\gamma}|^2}{N}\right)\mathcal{D} \leq 0.
$$

Moreover, these inequalities are also equivalent to

$$
(ii') \Gamma_{2,\perp}(\psi) \ge \frac{(L_{\perp}\psi)^2}{N-1} + K|\nabla\psi|^2;
$$
$$
(iii') \ddot{\ell}_{\perp} \ge \frac{(\dot{\ell}_{\perp})^2}{N-1} + K|\dot{\gamma}|^2;
$$
and, in the case 

 $N < \infty$ ,
$$
(iv') \mathcal{D}_{\perp} + \left(\frac{K|\dot{\gamma}|^2}{N-1}\right) \mathcal{D}_{\perp} \le 0.
$$

**Remark 14.9.** Note carefully that the inequalities  $(i)$ – $(iv')$  are required to be true *always*: For instance (ii) should be true for all  $\psi$ , all x and all  $t \in (0,1)$ . The equivalence is that  $[(i)$  true for all x is equivalent to  $[(ii)$  true for all  $\psi$ , all x and all t, etc.

Examples 14.10 (One-dimensional  $CD(K, N)$  model spaces). (a) Let  $K > 0$  and  $1 < N < \infty$ , consider

$$
M = \left(-\sqrt{\frac{N-1}{K}}\frac{\pi}{2}, \sqrt{\frac{N-1}{K}}\frac{\pi}{2}\right) \subset \mathbb{R},
$$

equipped with the usual distance on R, and the reference measure

$$
\nu(dx) = \cos^{N-1}\left(\sqrt{\frac{K}{N-1}}x\right) dx;
$$

then M satisfies  $CD(K, N)$ , although the Hausdorff dimension of M is of course 1. Note that  $M$  is not complete, but this is not a serious problem since  $CD(K, N)$  is a local property. (We can also replace M by its closure, but then it is a manifold with boundary.)

(b) For  $K < 0$ ,  $1 \leq N < \infty$ , the same conclusion holds true if one considers  $M=\mathbb{R}$  and

$$
\nu(dx) = \cosh^{N-1}\left(\sqrt{\frac{|K|}{N-1}}x\right) dx.
$$

(c) For any  $N \in [1,\infty)$ , an example of one-dimensional space satisfying  $CD(0, N)$  is provided by  $M = (0, +\infty)$  with the reference measure  $\nu(dx) = x^{N-1} dx;$ 

(d) For any  $K \in \mathbb{R}$ , take  $M = \mathbb{R}$  and equip it with the reference measure

$$
\nu(dx) = e^{-\frac{Kx^2}{2}} dx;
$$

then M satisfies  $CD(K, \infty)$ .

Sketch of proof of Theorem 14.8. It is clear from our discussion in this chapter that (i) implies (ii) and (iii); and (iii) is equivalent to (iv) by elementary manipulations about derivatives. (Moreover, (ii) and (iii) are equivalent modulo smoothness issues, by Eulerian/Lagrangian duality.)

It is less clear why, say, (ii) would imply (i). This comes from formulas (14.37) and (14.50). Indeed, assume (ii) and choose an arbitrary

 $x_0 \in M$ , and  $v_0 \in T_{x_0}M$ . Assume, to fix ideas, that  $N > n$ . Construct a  $C^3$  function  $\psi$  such that

$$
\begin{cases}\n\nabla \psi(x_0) = v_0 \\
\nabla^2 \psi(x_0) = \lambda_0 I_n \\
\Delta \psi(x_0) (= n\lambda_0) = -\frac{n}{N - n} (\nabla V(x_0) \cdot v_0).\n\end{cases}
$$

(This is fairly easy by using local coordinates, or distance and exponential functions.) Then all the remainder terms in (14.50) will vanish at  $x_0$ , so that

$$
K|v_0|^2 = K|\nabla \psi(x_0)|^2 \le \left(\Gamma_2(\psi) - \frac{(L\psi)^2}{N}\right)(x_0) = \text{Ric}_{N,\nu}(\nabla \psi(x_0)) = \text{Ric}_{N,\nu}(v_0).
$$

So indeed  $\text{Ric}_{N,\nu} \geq K$ .

The proof goes in the same way for the equivalence between (i) and (ii'), (iii'), (iv'): again the problem is to understand why (ii') implies (i), and the reasoning is almost the same as before; the key point being that the extra error terms in  $\partial_{1j}\psi$ ,  $j \neq 2$ , all vanish at  $x_0$ . □

Many interesting inequalities can be derived from  $CD(K, N)$ . It was successfully advocated by Bakry and other authors during the past two decades that  $CD(K, N)$  should be considered as a property of the generalized Laplace operator  $L$ . Instead, it will be advocated in this course that  $CD(K, N)$  is a property of the solution of the optimal transport problem, when the cost function is the square of the geodesic distance. Of course, both points of view have their advantages and their drawbacks.

# From differential to integral curvature-dimension bounds

There are two ways to characterize the concavity of a function  $f(t)$ on a time-interval, say [0, 1]: the differential inequality  $f \leq 0$ , or the integral bound  $f((1 - \lambda) t_0 + \lambda t_1) \ge (1 - \lambda) f(t_0) + \lambda f(t_1)$ . If the latter is required to hold true for all  $t_0, t_1 \in [0, 1]$  and  $\lambda \in [0, 1]$ , then the two formulations are equivalent.

There are two classical generalizations. The first one states that the differential inequality  $\ddot{f} + K \leq 0$  is equivalent to the integral inequality

$$
f((1 - \lambda) t_0 + \lambda t_1) \ge (1 - \lambda) f(t_0) + \lambda f(t_1) + \frac{Kt(1 - t)}{2} (t_0 - t_1)^2.
$$

Another one is as follows: The differential inequality

$$
\ddot{f}(t) + Af(t) \le 0 \tag{14.52}
$$

is equivalent to the integral bound

$$
f\left((1-\lambda) t_0 + \lambda t_1\right) \ge \tau^{(1-\lambda)}(|t_0 - t_1|) f(t_0) + \tau^{(\lambda)}(|t_0 - t_1|) f(t_1), (14.53)
$$

where

$$
\tau^{(\lambda)}(\theta) = \begin{cases} \frac{\sin(\lambda \theta \sqrt{\Lambda})}{\sin(\theta \sqrt{\Lambda})} & \text{if } \Lambda > 0 \\ \lambda & \text{if } \Lambda = 0 \\ \frac{\sinh(\lambda \theta \sqrt{-\Lambda})}{\sinh(\theta \sqrt{-\Lambda})} & \text{if } \Lambda < 0. \end{cases}
$$

A more precise statement, together with a proof, are provided in the Second Appendix of this chapter.

This leads to the following integral characterization of  $CD(K, N)$ :

Theorem 14.11 (Integral reformulation of curvature-dimension bounds). Let M be a Riemannian manifold, equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , and let d be the geodesic distance on M. Let  $K \in \mathbb{R}$  and  $N \in [1,\infty]$ . Then, with the same notation as in Theorem 14.8, M satisfies  $CD(K, N)$  if and only if the following inequality is always true (for any semiconvex  $\psi$ , and almost any x, as soon as  $\mathcal{J}(t,x)$  does not vanish for  $t \in (0,1)$ :

$$
\mathcal{D}(t,x) \ge \tau_{K,N}^{(1-t)} \mathcal{D}(0,x) + \tau_{K,N}^{(t)} \mathcal{D}(1,x) \tag{1.54}
$$

$$
(14.54)
$$

$$
\ell(t, x) \le (1 - t)\,\ell(0, x) + t\,\ell(1, x) - \frac{Kt(1 - t)}{2}\,d(x, y)^2 \qquad (N = \infty),
$$
\n(14.55)

where  $y = \exp_x(\nabla \psi(x))$  and, in case  $N < \infty$ ,

$$
\tau_{K,N}^{(t)} = \begin{cases} \frac{\sin(t\alpha)}{\sin\alpha} & \text{if } K > 0 \\ t & \text{if } K = 0 \\ \frac{\sinh(t\alpha)}{\sinh\alpha} & \text{if } K < 0 \end{cases}
$$

where

$$
\alpha = \sqrt{\frac{|K|}{N}} d(x, y) \qquad (\alpha \in [0, \pi] \text{ if } K > 0).
$$

*Proof of Theorem 14.11.* If  $N < \infty$ , inequality (14.54) is obtained by transforming the differential bound of (iii) in Theorem 14.8 into an integral bound, after noticing that  $|\dot{\gamma}|$  is a constant all along the geodesic  $\gamma$ , and equals  $d(\gamma_0, \gamma_1)$ . Conversely, to go from (14.54) to Theorem 14.8(iii), we select a geodesic  $\gamma$ , then reparametrize the geodesic  $(\gamma_t)_{t_0 \leq t \leq t_1}$  into a geodesic  $[0, 1] \to M$ , apply  $(14.54)$  to the reparametrized path and discover that

$$
\mathcal{D}(t,x) \geq \tau_{K,N}^{(1-\lambda)} \mathcal{D}(t_0,x) + \tau_{K,N}^{(\lambda)} \mathcal{D}(t_1,x) \quad t = (1-\lambda)t_0 + \lambda t_1;
$$

where now  $\alpha = \sqrt{|K|/N} d(\gamma(t_0), \gamma(t_1))$ . It follows that  $\mathcal{D}(t,x)$  satisfies (14.53) for any choice of  $t_0, t_1$ ; and this is equivalent to (14.52).

The reasoning is the same for the case  $N = \infty$ , starting from in-<br>rality (ii) in Theorem 14.8. equality (ii) in Theorem  $14.8$ .

The next result states that the the coefficients  $\tau_{K,N}^{(t)}$  obtained in Theorem 14.11 can be *automatically improved* if N is finite and  $K \neq 0$ , by taking out the direction of motion:

Theorem 14.12 (Curvature-dimension bounds with direction of motion taken out). Let  $M$  be a Riemannian manifold, equipped with a reference measure  $\nu = e^{-V}$  vol, and let d be the geodesic distance on M. Let  $K \in \mathbb{R}$  and  $N \in [1,\infty)$ . Then, with the same notation as in Theorem 14.8, M satisfies  $CD(K, N)$  if and only if the following inequality is always true (for any semiconvex  $\psi$ , and almost any x, as soon as  $\mathcal{J}(t,x)$  does not vanish for  $t \in (0,1)$ :

$$
\mathcal{D}(t,x) \ge \tau_{K,N}^{(1-t)} \mathcal{D}(0,x) + \tau_{K,N}^{(t)} \mathcal{D}(1,x) \tag{14.56}
$$

where now

$$
\tau_{K,N}^{(t)} = \begin{cases} t^{\frac{1}{N}} \left( \frac{\sin(t\alpha)}{\sin \alpha} \right)^{1-\frac{1}{N}} & \text{if } K > 0 \\ t & \text{if } K = 0 \\ t^{\frac{1}{N}} \left( \frac{\sinh(t\alpha)}{\sinh \alpha} \right)^{1-\frac{1}{N}} & \text{if } K < 0 \end{cases}
$$

and

$$
\alpha = \sqrt{\frac{|K|}{N-1}} d(x, y) \qquad (\alpha \in [0, \pi] \text{ if } K > 0).
$$

**Remark 14.13.** When  $N < \infty$  and  $K > 0$  Theorem 14.12 contains the Bonnet–Myers theorem according to which  $d(x, y) \leq \pi \sqrt{(N-1)/K}$ . With Theorem 14.11 the bound was only  $\pi \sqrt{N/K}$ .

*Proof of Theorem 14.12.* The proof that  $(14.56)$  implies  $CD(K, N)$  is done in the same way as for  $(14.54)$ . (In fact  $(14.56)$ ) is stronger than (14.54).)

As for the other implication: Start from (14.22), and transform it into an integral bound:

$$
\mathcal{D}_{\perp}(t,x) \ge \sigma_{K,N}^{(1-t)} \mathcal{D}_{\perp}(0,x) + \sigma_{K,N}^{(t)} \mathcal{D}_{\perp}(1,x),
$$

where  $\sigma_{K,N}^{(t)} = \sin(t\alpha)/\sin\alpha$  if  $K > 0$ ; t if  $K = 0$ ;  $\sinh(t\alpha)/\sinh\alpha$  if  $K < 0$ . Next transform  $(14.19)$  into the integral bound

$$
\mathcal{D}_{//}(t,x) \ge (1-t)\,\mathcal{D}_{//}(0,x) + t\,\mathcal{D}_{//}(1,x).
$$

Both estimates can be combined thanks to Hölder's inequality:

$$
\mathcal{D}(t,x) = \mathcal{D}_{\perp}(t,x)^{1-\frac{1}{N}} \mathcal{D}_{//}(t,x)^{\frac{1}{N}}
$$
$$
\geq \left(\sigma_{K,N}^{(1-t)} \mathcal{D}(0,x) + \sigma_{K,N}^{(t)} \mathcal{D}(1,x)\right)^{1-\frac{1}{N}} \left((1-t)\mathcal{D}_{//}(0,x) + t\mathcal{D}_{//}(1,x)\right)^{\frac{1}{N}}
$$
$$
\geq (\sigma_{K,N}^{(1-t)})^{1-\frac{1}{N}}(1-t)^{\frac{1}{N}} \mathcal{D}(0,x) + (\sigma_{K,N}^{(t)})^{\frac{1}{N}} t^{\frac{1}{N}} \mathcal{D}_{//}(1,x).
$$
This implies inequality (14.56)

This implies inequality  $(14.56)$ . □

Estimate (14.56) is sharp in general. The following reformulation yields an appealing interpretation of  $CD(K, N)$  in terms of comparison spaces. In the sequel, I will write  $Jac_x$  for the (unoriented) Jacobian determinant evaluated at point  $x$ , computed with respect to a given reference measure.

Corollary 14.14 (Curvature-dimension bounds by comparison). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$  vol,  $V \in C^2(M)$ . Define the J-function of M on  $[0, 1] \times \mathbb{R}_+ \times \mathbb{R}_+$  by the formula

$$
\mathcal{J}_{M,\nu}(t,\delta,J) := \inf \Big\{ \operatorname{Jac}_x(\exp(t\xi)); \quad |\xi(x)| = \delta; \quad \operatorname{Jac}_x(\exp(\xi)) = J \Big\},\tag{14.57}
$$

where the infimum is over all vector fields  $\xi$  defined around x, such that  $\nabla \xi(x)$  is symmetric, and  $\text{Jac}_x(\exp s\xi) \neq 0$  for  $0 \leq s < 1$ . Then, for any  $K \in \mathbb{R}$ ,  $N \in [1,\infty]$   $(K \leq 0 \text{ if } N = 1)$ ,

$$
(M,\nu)
$$
 satisfies  $CD(K,N) \iff \mathcal{J}_{M,\nu} \geq \mathcal{J}^{(K,N)},$ 

where  $\mathcal{J}^{(K,N)}$  is the J-function of the model  $CD(K,N)$  space considered in Examples 14.10.

If N is an integer,  $\mathcal{J}^{(K,N)}$  is also the J-function of the N-dimensional model space

$$
S^{(K,N)} = \begin{cases} S^N\left(\sqrt{\frac{N-1}{K}}\right) & \text{if } K > 0 \\ \mathbb{R}^N & \text{if } K = 0 \\ \mathbb{H}^N\left(\sqrt{\frac{N-1}{|K|}}\right) & \text{if } K < 0, \end{cases}
$$

equipped with its volume measure.

Corollary 14.14 results from Theorem 14.12 by a direct computation of the J-function of the model spaces. In the case of  $S^{(K,N)}$ , one can also make a direct computation, or note that all the inequalities which were used to obtain (14.56) turn into equalities for suitable choices of parameters.

Remark 14.15. There is a quite similar (and more well-known) formulation of lower *sectional* curvature bounds which goes as follows. Define the  $\mathcal{L}\text{-function}$  of a manifold M by the formula

$$
\mathcal{L}_M(t,\delta,L) = \inf \Big\{ d(\exp_x(tv), \exp_x(tw)); \quad |v| = |w| = \delta; d(\exp_x v, \exp_x w) = L \Big\},
$$

where the infimum is over tangent vectors  $v, w \in T_xM$ . Then M has sectional curvature larger than  $\kappa$  if and only if  $\mathcal{L}_M \geq \mathcal{L}^{(\kappa)}$ , where  $\mathcal{L}^{(\kappa)}$  is the L-function of the reference space  $S^{(\kappa)}$ , which is  $S^2(1/\sqrt{\kappa})$  if  $\kappa > 0$ ,  $\mathbb{R}^2$  if  $\kappa = 0$ , and  $\mathbb{H}^2(1/\sqrt{|\kappa|})$  if  $\kappa < 0$ . By changing the infimum into a supremum and reversing the inequalities, one can also obtain a characterization of upper sectional curvature bounds (under a topological assumption of simple connectivity). The comparison with (14.14) conveys the idea that sectional curvature bounds measure the rate of separation of geodesics in terms of distances, while Ricci curvature bounds do it in terms of Jacobian determinants.

## Distortion coefficients

Apart from Definition 14.19, the material in this section is not necessary to the understanding of the rest of this course. Still, it is interesting because it will give a new interpretation of Ricci curvature bounds, and motivate the introduction of distortion coefficients, which will play a crucial role in the sequel.

Definition 14.16 (Barycenters). If A and B are two measurable sets in a Riemannian manifold, and  $t \in [0, 1]$ , a t-barycenter of A and B is a point which can be written  $\gamma_t$ , where  $\gamma$  is a (minimizing, constantspeed) geodesic with  $\gamma_0 \in A$  and  $\gamma_1 \in B$ . The set of all t-barycenters between A and B is denoted by  $[A, B]_t$ .

Definition 14.17 (Distortion coefficients). Let M be a Riemannian manifold, equipped with a reference measure  $e^{-V}$ vol,  $V \in C(M)$ , and let x and y be any two points in  $M$ . Then the distortion coefficient  $\beta_t(x, y)$  between x and y at time  $t \in (0, 1)$  is defined as follows:

• If x and y are joined by a unique geodesic  $\gamma$ , then

$$
\overline{\beta}_t(x,y) = \lim_{r \downarrow 0} \frac{\nu[[x, B_r(y)]_t]}{\nu[B_{tr}(y)]} = \lim_{r \downarrow 0} \frac{\nu[[x, B_r(y)]_t]}{t^n \nu[B_r(y)]};\tag{14.58}
$$

• If x and y are joined by several minimizing geodesics, then

$$
\overline{\beta}_t(x,y) = \inf_{\gamma} \limsup_{s \to 1^-} \overline{\beta}_t(x,\gamma_s), \tag{14.59}
$$

where the infimum is over all minimizing geodesics joining  $\gamma(0) = x$ to  $\gamma(1) = y$ .

Finally, the values of  $\beta_t(x, y)$  for  $t = 0$  and  $t = 1$  are defined by

$$
\overline{\beta}_1(x,y) \equiv 1; \qquad \overline{\beta}_0(x,y) := \liminf_{t \to 0^+} \overline{\beta}_t(x,y).
$$

The heuristic meaning of distortion coefficients is as follows (see Figure 14.4). Assume you are standing at point  $x$  and observing some device located at  $y$ . You are trying to estimate the volume of this device, but your appreciation is altered because light rays travel along curved lines (geodesics). If  $x$  and  $y$  are joined by a unique geodesic, then the coefficient  $\beta_0(x, y)$  tells by how much you are overestimating; so it is less than 1 in negative curvature, and greater than 1 in positive curvature. If  $x$  and  $y$  are joined by several geodesics, this is just the same, except that you choose to look in the direction where the device looks smallest.

Image /page/9/Figure/9 description: The image is a diagram illustrating how gravity distorts light. An observer is located at the pointed end of a conical shape. The diagram shows that geodesics (paths of light) are distorted by curvature effects. A light source is depicted as a hatched oval shape, and an arrow indicates that this is how the observer perceives the light source to look due to the distortion.

Fig. 14.4. The meaning of distortion coefficients: Because of positive curvature effects, the observer overestimates the surface of the light source; in a negatively curved world this would be the contrary.

More generally,  $\beta_t(x, y)$  compares the volume occupied by the light rays emanating from the light source, when they arrive close to  $\gamma(t)$ , to the volume that they would occupy in a flat space (see Figure 14.5).

Image /page/10/Figure/2 description: The image shows a cone with its apex labeled 'x' on the left and its base labeled 'y' on the right. The cone is depicted with solid lines for its boundary and dashed lines extending from the apex to form the outer edges of the cone. Inside the cone, there is a cross-section shown as an oval shape with diagonal hatching, indicating a slice through the cone.

Fig. 14.5. The distortion coefficient is approximately equal to the ratio of the volume filled with lines, to the volume whose contour is in dashed line. Here the space is negatively curved and the distortion coefficient is less than 1.

Now let us express distortion coefficients in differential terms, and more precisely *Jacobi fields*. A key concept in doing so will be the notion of focalization, which was already discussed in Chapter 8: A point y is said to be focal to another point x if there exists  $v \in T_xM$  such that  $y = \exp_v x$  and the differential  $d_v \exp_x : T_xM \to T_yM$  is not invertible. It is equivalent to say that there is a geodesic  $\gamma$  which visits both x and y, and a Jacobi field J along  $\gamma$  such that  $J(x) = 0$ ,  $J(y) = 0$ . This concept is obviously symmetric in  $x$  and  $y$ , and then  $x, y$  are said to be conjugate points (along  $\gamma$ ).

If x and y are joined by a unique geodesic  $\gamma$  and are not conjugate, then by the local inversion theorem, for  $r$  small enough, there is a unique velocity  $\xi(z)$  at  $z \in B_r(y)$  such that  $\exp_z \xi(z) = x$ . Then the distortion coefficients can be interpreted as the Jacobian determinant of  $\exp \xi$  at time t, renormalized by  $(1-t)^n$ , which would be the value in Euclidean space. The difference with the computations at the beginning of this chapter is that now the Jacobi field is not defined by its initial value and initial derivative, but rather by its initial value and its final value:  $\exp_z \xi(z) = x$  independently of z, so the Jacobi field vanishes after a time 1. It will be convenient to reverse time so that  $t = 0$  corresponds to x and  $t = 1$  to y; thus the conditions are  $J(0) = 0$ ,  $J(1) = I_n$ . After these preparations it is easy to derive the following:

Proposition 14.18 (Computation of distortion coefficients). Let  $M$  be a Riemannian manifold, let  $x$  and  $y$  be two points in  $M$ . Then

$$
\overline{\beta}_t(x,y) = \inf_{\gamma} \overline{\beta}_t^{[\gamma]}(x,y),
$$

where the infimum is over all minimizing geodesics  $\gamma$  joining  $\gamma(0) = x$ to  $\gamma(1) = y$ , and  $\overline{\beta}_t^{[\gamma]}$  $t^{(1)}(x, y)$  is defined as follows:

• If x, y are not conjugate along  $\gamma$ , let **E** be an orthonormal basis of  $T_{y}M$  and define

$$
\overline{\beta}_{t}^{[\gamma]}(x,y) = \begin{cases} \frac{\det \mathbf{J}^{0,1}(t)}{t^{n}} & \text{if } 0 < t \le 1\\ \lim_{s \to 0} \frac{\det \mathbf{J}^{0,1}(s)}{s^{n}} & \text{if } t = 0, \end{cases}
$$
(14.60)

where  $\mathbf{J}^{0,1}$  is the unique matrix of Jacobi fields along  $\gamma$  satisfying

$$
\mathbf{J}^{0,1}(0) = 0; \qquad \mathbf{J}^{0,1}(1) = \mathbf{E};
$$

• If x, y are conjugate along  $\gamma$ , define

$$
\overline{\beta}_t^{[\gamma]}(x,y) = \begin{cases} 1 & \text{if } t = 1 \\ +\infty & \text{if } 0 \le t < 1. \end{cases}
$$

Distortion coefficients can be explicitly computed for the model  $CD(K, N)$  spaces and depend only on the distance between the two points  $x$  and  $y$ . These particular coefficients (or rather their expression as a function of the distance) will play a key role in the sequel of these notes.

Definition 14.19 (Reference distortion coefficients). Given  $K \in \mathbb{R}, N \in [1,\infty]$  and  $t \in [0,1]$ , and two points x, y in some metric space  $(\mathcal{X}, d)$ , define  $\beta_t^{(K,N)}$  $t^{(K,N)}(x,y)$  as follows:

• If  $0 < t \leq 1$  and  $1 < N < \infty$  then

$$
\beta_t^{(K,N)}(x,y) = \begin{cases} +\infty & \text{if } K > 0 \text{ and } \alpha > \pi, \\ \left(\frac{\sin(t\alpha)}{t \sin \alpha}\right)^{N-1} & \text{if } K > 0 \text{ and } \alpha \in [0, \pi], \\ 1 & \text{if } K = 0, \\ \left(\frac{\sinh(t\alpha)}{t \sinh \alpha}\right)^{N-1} & \text{if } K < 0 \end{cases} \quad (14.61)
$$

where

$$
\alpha = \sqrt{\frac{|K|}{N-1}} d(x, y).
$$
 (14.62)

• In the two limit cases  $N \to 1$  and  $N \to \infty$ , modify the above expressions as follows:

$$
\beta_t^{(K,1)}(x,y) = \begin{cases}\n+\infty & \text{if } K > 0, \\
1 & \text{if } K \le 0,\n\end{cases}
$$
\n(14.63)

$$
\beta_t^{(K,\infty)}(x,y) = e^{\frac{K}{6}(1-t^2)d(x,y)^2}.
$$
\n(14.64)

• For  $t = 0$  define  $\beta_0^{(K,N)}$  $y_0^{(K,N)}(x,y)=1.$ 

If  $X$  is the model space for  $CD(K, N)$ , as in Examples 14.10, then  $\beta^{(K,N)}$  is just the distortion coefficient on X.

If K is positive, then for fixed  $t, \beta_t^{(K,N)}$  $t_t^{(K,N)}$  is an increasing function of  $\alpha$ (going to  $+\infty$  at  $\alpha = \pi$ ), while for fixed  $\alpha$ , it is a decreasing function of t on [0, 1]. All this is reversed for negative K. On the whole,  $\beta_t^{(K,N)}$ t is nondecreasing in  $K$  and nonincreasing in  $N$ . (See Figure 14.6.)

The next two theorems relate distortion coefficients with Ricci curvature lower bounds; they show that (a) distortion coefficients can be interpreted as the "best possible" coefficients in concavity estimates for the Jacobian determinant; and (b) the curvature-dimension bound  $CD(K, N)$  is a particular case of a family of more general estimates characterized by a lower bound on the distortion coefficients.

Image /page/13/Figure/1 description: The image displays three graphs side-by-side, each illustrating a different scenario based on the value of K. The first graph, labeled 'K > 0', shows a curve that starts at a moderate positive value on the y-axis and increases sharply as it approaches a vertical dashed line at pi on the x-axis. The second graph, labeled 'K = 0', shows a horizontal line indicating a constant value on the y-axis across the x-axis. The third graph, labeled 'K < 0', shows a curve that starts at a moderate positive value on the y-axis, decreases to a minimum, and then approaches zero on the y-axis as it moves along the x-axis.

Fig. 14.6. The shape of the curves  $\beta_t^{(K,N)}(x,y)$ , for fixed  $t \in (0,1)$ , as a function of  $\alpha = \sqrt{\frac{K}{N-1}} d(x,y)$ .

Theorem 14.20 (Distortion coefficients and concavity of Jacobian determinant). Let M be a Riemannian manifold of dimension n, and let x, y be any two points in M. Then if  $(\beta_t(x,y))_{0 \le t \le 1}$  and  $(\beta_t(y,x))_{0 \leq t \leq 1}$  are two families of nonnegative coefficients, the following statements are equivalent:

(a)  $\forall t \in [0,1], \quad \beta_t(x,y) \leq \beta_t(x,y); \quad \beta_t(y,x) \leq \beta_t(y,x);$ 

(b) For any  $N \geq n$ , for any geodesic  $\gamma$  joining x to y, for any  $t_0 \in [0, 1]$ , and for any initial vector field  $\xi$  around  $x_0 = \gamma(t_0)$ ,  $\nabla \xi(x_0)$ symmetric, let  $\mathcal{J}(s)$  stand for the Jacobian determinant of  $\exp((s-t_0)\xi)$ at  $x_0$ ; if  $\mathcal{J}(s)$  does not vanish for  $0 < s < 1$ , then for all  $t \in [0,1]$ ,

$$
\begin{cases}
\mathcal{J}(t)^{\frac{1}{N}} \ge (1-t)\,\beta_{1-t}(y,x)^{\frac{1}{N}}\mathcal{J}(0)^{\frac{1}{N}} + t\,\beta_{t}(x,y)^{\frac{1}{N}}\mathcal{J}(1)^{\frac{1}{N}} & (N < \infty) \\
\log \mathcal{J}(0,x) \ge (1-t)\,\log \mathcal{J}(0) + t\,\log \mathcal{J}(1) \\
&+[(1-t)\,\log \beta_{1-t}(y,x) + t\,\log \beta_{t}(x,y)] & (N = \infty);
\end{cases}
$$
(14.65)

(c) Property (b) holds true for  $N = n$ .

Theorem 14.21 (Ricci curvature bounds in terms of distortion coefficients). Let  $M$  be a Riemannian manifold of dimension  $n$ , equipped with its volume measure. Then the following two statements are equivalent:

(a) Ric 
$$
\geq K
$$
;  
(b)  $\overline{\beta} \geq \beta^{(K,n)}$ .

Sketch of proof of Theorem 14.20. To prove the implication  $(a) \Rightarrow (b)$ , it suffices to establish (14.65) for  $\beta = \overline{\beta}$ . The case  $N = \infty$  is obtained from

the case  $N < \infty$  by passing to the limit, since  $\lim_{N\to 0} [N(a^{1/N} - 1)] =$ log a. So all we have to show is that if  $n \leq N < \infty$ , then

$$
\mathcal{J}(t)^{\frac{1}{N}} \ge (1-t)\overline{\beta}_{1-t}(y,x)^{\frac{1}{N}}\mathcal{J}(0)^{\frac{1}{N}} + t\overline{\beta}_{t}(x,y)^{\frac{1}{N}}\mathcal{J}(1)^{\frac{1}{N}}.
$$

The case when  $x, y$  are conjugate can be treated by a limiting argument. (In fact the conclusion is that both  $\mathcal{J}(0)$  and  $\mathcal{J}(1)$  have to vanish if x and y are conjugate.) So we may assume that x and y are not conjugate, introduce a moving orthonormal basis  $E(t)$ , along  $\gamma$ , and define the Jacobi matrices  $J^{1,0}(t)$  and  $J^{0,1}(t)$  by the requirement

$$
J^{1,0}(0) = I_n, \quad J^{1,0}(1) = 0; \quad J^{0,1}(0) = 0, \quad J^{0,1}(1) = I_n.
$$

(Here  $\mathbf{J}^{1,0}, \mathbf{J}^{0,1}$  are identified with their expressions  $J^{1,0}, J^{0,1}$  in the moving basis E.)

As noted after (14.7), the Jacobi equation is invariant under the change  $t \to 1-t$ ,  $\mathbf{E} \to -\mathbf{E}$ , so  $J^{1,0}$  becomes  $J^{0,1}$  when one exchanges the roles of x and y, and replaces t by  $1 - t$ . In particular, we have the formula

$$
\frac{\det J^{1,0}(t)}{(1-t)^n} = \beta_{1-t}(y,x). \tag{14.66}
$$

As in the beginning of this chapter, the issue is to compute the determinant at time t of a Jacobi field  $J(t)$ . Since the Jacobi fields are solutions of a linear differential equation of the form  $\ddot{J} + R J = 0$ , they form a vector space of dimension  $2n$ , and they are invariant under right-multiplication by a constant matrix. This implies

$$
J(t) = J^{1,0}(t) J(0) + J^{0,1}(t) J(1).
$$
 (14.67)

The determinant in dimension  $n$  satisfies the following inequality: If X and Y are two  $n \times n$  nonnegative symmetric matrices, then

$$
\det(X+Y)^{\frac{1}{n}} \ge (\det X)^{\frac{1}{n}} + (\det Y)^{\frac{1}{n}}.
$$
 (14.68)

By combining this with Hölder's inequality, in the form

$$
(a^{\frac{1}{n}} + b^{\frac{1}{n}})^{\frac{n}{N}} \ge (1-t)^{\frac{N-n}{N}} a^{\frac{1}{N}} + t^{\frac{N-n}{N}} b^{\frac{1}{N}},
$$

we obtain a generalization of (14.68):

$$
\det(X+Y)^{\frac{1}{N}} \ge (1-t)^{\frac{N-n}{N}} (\det X)^{\frac{1}{N}} + t^{\frac{N-n}{N}} (\det Y)^{\frac{1}{N}}.
$$
 (14.69)

If one combines (14.67) and (14.69) one obtains

$$
(\det J(t))^{\frac{1}{N}} \ge (1-t)^{\frac{N-n}{N}} (\det J^{1,0}(t))^{\frac{1}{N}} (\det J(0))^{\frac{1}{N}} + t^{\frac{N-n}{N}} (\det J^{0,1}(t))^{\frac{1}{N}} (\det J(1))^{\frac{1}{N}}
$$

$$
= (1-t) \left[ \frac{\det J^{1,0}(t)}{(1-t)^n} \right]^{\frac{1}{N}} \mathcal{J}(0)^{\frac{1}{N}} + t \left[ \frac{\det J^{0,1}(t)}{t^n} \right]^{\frac{1}{N}} \mathcal{J}(1)^{\frac{1}{N}}
$$

$$
= (1-t) \overline{\beta}_{1-t}(y,x)^{\frac{1}{N}} \mathcal{J}(0)^{\frac{1}{N}} + t \overline{\beta}_{t}(x,y)^{\frac{1}{N}} \mathcal{J}(1)^{\frac{1}{N}},
$$

where the final equality follows from  $(14.60)$  and  $(14.66)$ .

In this way we have shown that (a) implies (b), but at the price of a gross cheating, since in general the matrices appearing in (14.67) are not symmetric! It turns out however that they can be *positively* cosymmetrized: there is a kernel  $K(t)$  such that  $\det K(t) > 0$ , and  $K(t) J^{1,0}(t)$  and  $K(t) J^{0,1}(t) J(1)$  are both positive symmetric, at least for  $t \in (0, 1)$ . This remarkable property is a consequence of the structure of Jacobi fields; see Propositions 14.30 and 14.31 in the Third Appendix of this chapter.

Once the cosymmetrization property is known, it is obvious how to fix the proof: just write

$$
\left(\det(K(t) J(t))\right)^{\frac{1}{n}} \geq \left(\det(K(t) J^{1,0}(t))\right)^{\frac{1}{n}} + \left(\det(K(t) J^{0,1}(t) J(1))\right)^{\frac{1}{n}},
$$

and then factor out the positive quantity  $(\det K(t))^{1/n}$  to get

$$
\left(\det J(t)\right)^{\frac{1}{n}} \ge \left(\det(J^{1,0}(t))\right)^{\frac{1}{n}} + \left(\det(J^{0,1}(t) J(1))\right)^{\frac{1}{n}}.
$$

The end of the argument is as before.

Next, it is obvious that (b) implies (c). To conclude the proof, it suffices to show that (c)  $\Rightarrow$  (a). By symmetry and definition of  $\beta_t$ , it is sufficient to show that  $\beta_t(x, y) \leq \overline{\beta}_t^{[\gamma]}$  $t^{(1)}(x, y)$  for any geodesic  $\gamma$ . If x and y are conjugate along  $\gamma$  then there is nothing to prove. Otherwise, we can introduce  $\xi(z)$  in the ball  $B_r(y)$  such that for any  $z \in B_r(y)$ ,  $\exp_z \xi(z) = x$ , and  $\exp_z(t\xi(z))$  is the only geodesic joining z to x. Let  $\mu_0$ be the uniform probability distribution on  $B_r(y)$ , and  $\mu_1$  be the Dirac mass at x; then  $\exp \xi$  is the unique map T such that  $T_{\#}\mu_0 = \mu_1$ , so it is the optimal transport map, and therefore can be written as  $\exp(\nabla \psi)$ for some  $d^2/2$ -convex  $\psi$ ; in particular,  $\xi = \nabla \psi$ . (Here I have chosen  $t_0 = 1$ , say.) So we can apply (b) with  $N = n$ ,  $\mathcal{D}(1) = 0$ ,  $\mathcal{D}(0) = 1$ ,  $\mathcal{D}(t,x) = \det J^{0,1}(t)$ , and obtain

$$
\det J^{0,1}(t)^{\frac{1}{n}} \ge t \beta_t(x,y)^{\frac{1}{n}}.
$$

It follows that  $\beta_t(x, y) \leq (\det J^{0,1}(t))/t^n = \overline{\beta}_t^{[\gamma]}$  $t^{\text{H}}(x,y)$ , as desired. □

Sketch of proof of Theorem 14.21. To prove (a)  $\Rightarrow$  (b), we may apply inequality (14.56) with  $n = N$ , to conclude that Property (c) in Theorem 14.20 is satisfied with  $\beta = \beta^{(K,n)}$ ; thus  $\overline{\beta} \geq \beta^{(K,n)}$ . Conversely, if  $\overline{\beta} \ge \beta^{(K,n)}$ , Theorem 14.20 implies inequality (14.56), which in turn implies  $CD(K, n)$ , or equivalently Ric ≥ K. □

**Remark 14.22.** If  $(M, \nu)$  satisfies  $CD(K, N)$  then (14.65) still holds true with  $\beta = \beta^{(K,N)}$  (provided of course that one takes the measure  $\nu$  into account when computing Jacobian determinants): this is just a rewriting of Theorem 14.12. However, Theorem 14.20 does not apply in this case, since  $N$  is in general larger than the "true dimension"  $n$ .

Remark 14.23. Theorems 14.20 and 14.21 suggest a generalization of the  $CD(K, N)$  criterion: Given an effective dimension N, define the generalized distortion coefficients  $\overline{\beta}_{N,\nu}$  as the best coefficients in (14.65) (the first inequality if  $N < \infty$ , the second one if  $N = \infty$ ). In this way the condition  $CD(K, N)$  might be a particular case of a more general condition CD( $\beta$ , N), which would be defined by the inequality  $\overline{\beta}_{N,\nu} \geq$ β, where  $β(x, y)$  would be, say, a given function of the distance between  $x$  and  $y$ . I shall not develop this idea, because (i) it is not clear at present that it would really add something interesting to the  $CD(K, N)$  theory; (ii) the condition  $CD(\beta, N)$  would in general be nonlocal.

**Remark 14.24.** It is not a priori clear what kind of functions  $\beta$  can occur as distortion coefficients. It is striking to note that, in view of Theorems 14.11 and 14.12, for any given manifold  $M$  of dimension  $n$ the following two conditions are equivalent, say for  $K > 0$ :

(i) 
$$
\forall x, y \in M, \ \forall t \in [0, 1], \quad \overline{\beta}_t(x, y) \ge \left( \frac{\sin \left( t \sqrt{\frac{K}{n}} d(x, y) \right)}{t \sin \left( \sqrt{\frac{K}{n}} d(x, y) \right)} \right)^n;
$$
  
(ii) 
$$
\forall x, y \in M, \ \forall t \in [0, 1], \quad \overline{\beta}_t(x, y) \ge \left( \frac{\sin \left( t \sqrt{\frac{K}{n-1}} d(x, y) \right)}{t \sin \left( \sqrt{\frac{K}{n-1}} d(x, y) \right)} \right)^{n-1}.
$$

This self-improvement property implies restrictions on the possible behavior of  $\beta$ .