Proof of Theorem 30.26. <PERSON><PERSON><PERSON> changes of notation, the proof is the same as the proof of Theorem 19.13, once Theorem 30.17 guarantees the almost sure uniqueness of geodesics. ⊓⊔

## Talagrand inequalities

With logarithmic Sobolev inequalities come a rich functional apparatus for treating concentration of measure. One may also get concentration from curvature bounds  $CD(K,\infty)$  via Talagrand inequalities. As for the links between logarithmic Sobolev and Talagrand inequalities, they also remain true, at least under mild stringent regularity assumptions on  $\mathcal{X}$ :

Theorem 30.28 (Talagrand inequalities and weak curvature **bounds).** (i) Let  $(X, d, \nu)$  be a weak CD(K,  $\infty$ ) space with  $K > 0$ . Then  $\nu$  lies in  $P_2(\mathcal{X})$  and satisfies the Talagrand inequality  $T_2(K)$ .

(ii) Let  $(\mathcal{X}, d, \nu)$  be a locally compact Polish geodesic space equipped with a locally doubling measure  $\nu$ , satisfying a local Poincaré inequality. If v satisfies a logarithmic So<PERSON><PERSON> inequality for some constant  $K > 0$ , then  $\nu$  lies in  $P_2(\mathcal{X})$  and satisfies the Talagrand inequality  $T_2(K)$ .

(iii) Let  $(\mathcal{X}, d, \nu)$  be a locally compact Polish geodesic space. If  $\nu$ satisfies a Talagrand inequality  $T_2(K)$  for some  $K > 0$ , then it also satisfies a global Poincaré inequality with constant  $K$ .

(iv) Let  $(X, d, \nu)$  be a locally compact Polish geodesic space equipped with a locally doubling measure  $\nu$ , satisfying a local Poincaré inequality. If  $\nu$  satisfies a global Poincaré inequality, then it also satisfies a modified logarithmic Sobolev inequality and a quadratic-linear transportation inequality as in Theorem 22.25.

Remark 30.29. In view of Corollary 30.14 and Theorem 30.26, the regularity assumptions required in (ii) are satisfied if  $(\mathcal{X}, d, \nu)$  is a nonbranching weak  $CD(K', N')$  space for some  $K' \in \mathbb{R}$ ,  $N' < \infty$ ; note that the values of  $K'$  and  $N'$  do not play any role in the conclusion.

Proof of Theorem 30.28. Part (i) is an immediate consequence of (30.25) and (30.29) with  $\mu_0 = \nu$ .

The proof of (ii) and (iii) is the same as the proof of Theorem 22.17, once one has an analog of Proposition 22.16. It turns out that properties (i)–(vi) of Proposition 22.16 and Theorem 22.46 are still satisfied when the Riemannian manifold M is replaced by any metric space  $\mathcal{X},$ but property (vii) might fail in general. It is still true that this property holds true for *v*-almost all x, under the assumption that  $\nu$  is locally doubling and satisfies a local Poincaré inequality. See Theorem  $30.30$ below for a precise statement (and the bibliographical notes for references). This is enough for the proof of Theorem 22.17 to go through. ⊓⊔

The next theorem was used in the proof of Theorem 30.28:

Theorem 30.30 (Hamilton–Jacobi semigroup in metric spaces). Let  $L : \mathbb{R}_+ \to \mathbb{R}_+$  be a strictly increasing, locally semiconcave, convex continuous function such that  $L(0) = 0$ . Let  $(\mathcal{X}, d)$  be a locally compact geodesic Polish space equipped with a reference measure  $\nu$ , locally doubling and satisfying a local Poincaré inequality. For any  $f \in C_b(\mathcal{X})$ , define the evolution  $(H_t f)_{t>0}$  by

 $H_0 f = f$ 

$$
(H_t f)(x) = \underset{y o \mathcal{X}}{\inf} \left[ f(y) + t L\left(\frac{d(x, y)}{t}\right) \right] \quad (t > 0, \ x \in \mathcal{X}).
$$

(30.34)

Then Properties  $(i)$ – $(vi)$  of Theorem 22.46 remain true, up to the replacement of M by  $\mathcal X$ . Moreover, the following weakened version of (vii) holds true:

(vii') For v-almost any  $x \in \mathcal{X}$  and any  $t > 0$ ,

$$
\lim_{s \downarrow 0} \frac{(H_{t+s}f)(x) - (H_t f)(x)}{s} = -L^*(|\nabla^{\dagger} H_t f|);
$$

this conclusion extends to  $t = 0$  if  $||f||_{\text{Lip}} \le L'(\infty)$  and f is locally Lipschitz.

Remark 30.31. There are also dimensional versions of Talagrand inequalities available, for instance the analog of Theorem 22.37 holds true in weak  $CD(K, N)$  spaces with  $K > 0$  and  $N < \infty$ .

### Equivalence of definitions in nonbranching spaces

In the definition of weak  $CD(K, N)$  spaces we chose to impose the displacement convexity inequality for all  $U \in \mathcal{DC}_N$ , but only along some displacement interpolation. We could have chosen otherwise, for instance impose the inequality for just some particular functions  $U$ , or along all displacement interpolations. In the end our choice was dictated partly by the will to get a stable definition, partly by convenience. It turns out that in nonbranching metric-measure spaces, the choice really does not matter. It is equivalent:

- to require the displacement convexity inequality to hold true for any  $U \in \mathcal{DC}_N$ ; or just for  $U = U_N$ , where as usual  $U_N(r) = -Nr^{1-1/N}$ if  $1 < N < \infty$ , and  $U_{\infty}(r) = r \log r$ ;
- to require the inequality to hold true for compactly supported, absolutely continuous probability measures  $\mu_0$ ,  $\mu_1$ ; or for any two probability measures with suitable moment conditions;
- to require the inequality to hold true along some displacement interpolation, or along any displacement interpolation.

The next statement makes this claim precise. Note that I leave aside the case  $N = 1$ , which is special (for instance  $U_1$  is not defined). I shall write  $(U_N)_{\nu} = H_{N,\nu}$ , and  $(U_N)_{\pi,\nu}^{\beta} = H_{N,\pi,\nu}^{\beta}$ . Recall Convention 17.10.

Theorem 30.32 (Equivalent definitions of  $CD(K, N)$  in non**branching spaces).** Let  $(\mathcal{X}, d, \nu)$  be a nonbranching locally compact Polish geodesic space equipped with a locally finite measure  $\nu$ . Let  $K \in \mathbb{R}, N \in (1,\infty]$ , and let  $p \in [2,+\infty) \cup \{c\}$  satisfy the assumptions of Theorem 30.4. Then the following three properties are equivalent:

(i)  $(X, d, \nu)$  is a weak  $CD(K, N)$  space, in the sense of Definition 29.8;

(ii) For any two compactly supported continuous probability densities  $\rho_0$  and  $\rho_1$ , there is a displacement interpolation  $(\mu_t)_{0 \le t \le 1}$  joining  $\mu_0 =$  $\rho_0 \nu$  to  $\mu_1 = \rho_1 \nu$ , and an associated optimal plan  $\pi$ , such that for all  $t \in [0, 1],$ 

$$
H_{N,\nu}(\mu_t) \le (1-t) H_{N,\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) + t H_{N,\check{\pi},\nu}^{\beta_t^{(K,N)}}(\mu_1). \tag{30.35}
$$

(iii) For any displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  with  $\mu_0, \mu_1 \in$  $P_p(\mathcal{X})$ , for any associated transport plan  $\pi$ , for any  $U \in \mathcal{DC}_N$  and for any  $t \in [0,1]$ ,

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) + t U_{\tilde{\pi},\nu}^{\beta_t^{(K,N)}}(\mu_1). \tag{30.36}
$$

**Remark 30.33.** In the case  $N = 1$ , (30.35) does not make any sense, but the equivalence (i)  $\Rightarrow$  (iii) still holds. This can be seen by working in dimension  $N' > 1$  and letting  $N' \downarrow 1$ , as in the proof of Theorem 17.41.

Theorem 30.32 is interesting even for smooth Riemannian manifolds, since it covers singular measures, for which there is a priori no uniqueness of displacement interpolant. Its proof is based on the idea, already used in Theorem 19.4, that we may condition the optimal transport to lie in a very small ball at time  $t$ , and, by passing to the limit, retrieve a pointwise control of the density  $\rho_t$ . This will work because the nonbranching property implies the uniqueness of the displacement interpolation between intermediate times, and forbids the crossing of geodesics used in the optimal transport, as in Theorem 7.30. Apart from this simple idea, the proof is quite technical and can be skipped at first reading.

*Proof of Theorem 30.32.* Let us first consider the case  $N < \infty$ .

Clearly, (iii)  $\Rightarrow$  (i)  $\Rightarrow$  (ii). So it is sufficient to show (ii)  $\Rightarrow$  (iii). In the sequel, I shall assume that Property (ii) is satisfied. By the same arguments as in the proof of Proposition 29.12, it is sufficient to establish  $(30.36)$  when U is nonnegative and Lipschitz continuous, and  $u(r) := U(r)/r$  (extended at 0 by  $u(0) = U'(0)$ ) is a continuous function of r. I shall fix  $t \in (0,1)$  and establish Property (iii) for that t. For simplicity I shall abbreviate  $\beta_t^{(K,N)}$  $t^{(K,N)}$  into just  $\beta_t$ .

First of all, let us establish that Property (ii) also applies if  $\mu_0$ and  $\mu_1$  are not absolutely continuous. The scheme of reasoning is the same as we already used several times. Let  $\mu_0$  and  $\mu_1$  be any two compactly supported measures. As in the proof of Theorem 29.24 we can construct probability measures  $\mu_{k,0}$  and  $\mu_{k,1}$ , absolutely continuous with continuous densities, supported in a common compact set, such that  $\mu_{k,0}$  converges to  $\mu_0$ ,  $\mu_{k,1}$  converges to  $\mu_1$ , in such a way that

$$
\limsup_{k \to \infty} U_{\pi_k, \nu}^{\beta_{1-t}}(\mu_{k,0}) \le U_{\pi, \nu}^{\beta_{1-t}}(\mu_0); \qquad \limsup_{k \to \infty} U_{\tilde{\pi}_k, \nu}^{\beta_t}(\mu_{k,1}) \le U_{\tilde{\pi}, \nu}^{\beta_t}(\mu_1),
$$
\n(30.37)

where  $\pi_k$  is any optimal transference plan between  $\mu_{k,0}$  and  $\mu_{k,1}$  such that  $\pi_k \to \pi$ . Since  $\mu_{k,0}$  and  $\mu_{k,1}$  are absolutely continuous with continuous densities, for each  $k$  we may choose an optimal tranference plan  $\pi_k$  and an associated displacement interpolation  $(\mu_{k,t})_{0 \leq t \leq 1}$  such that

$$
H_{N,\nu}(\mu_{k,t}) \le (1-t) H_{N,\pi_k,\nu}^{\beta_{1-t}}(\mu_{k,0}) + t H_{N,\tilde{\pi}_k,\nu}^{\beta_t}(\mu_{k,1}). \tag{30.38}
$$

Since all the measures  $\mu_{k,0}$  and  $\mu_{k,1}$  are supported in a uniform compact set, Corollary 7.22 guarantees that the sequence  $(\Pi_k)_{k\in\mathbb{N}}$  converges, up to extraction, to some dynamical optimal transference plan  $\Pi$  with  $(e_0)_\# \Pi = \mu_0$  and  $(e_1)_\# \Pi = \mu_1$ . Then  $\mu_{k,t}$  converges weakly to  $\mu_t =$  $(e_t)_\# \Pi$ , and  $\pi_k := (e_0, e_1)_\# \Pi_k$  converges weakly to  $\pi = (e_0, e_1)_\# \Pi$ . It remains to pass to the limit as  $k \to \infty$  in the inequality (30.38); this is easy in view of (30.37) and Theorem 29.20(i), which imply

$$
H_{N,\nu}(\mu_t) \le \liminf_{k \to \infty} H_{N,\nu}(\mu_{k,t}). \tag{30.39}
$$

Next, the proofs of Theorem 30.11 and Corollary 30.14 go through, since they only use the convex function  $U = U_N$ ; in particular the measure  $\nu$  is *locally doubling* on its support.

Also the proof of Theorem 30.19(ii)-(iii) can be easily adapted in the present setting, as soon as  $\mu_0$  and  $\mu_1$  are compactly supported.

Now we can start the core of the argument. It will be decomposed into four steps.

**Step 1:** Assume that  $\mu_0$  and  $\mu_1$  are compactly supported,  $\mu_t$  is absolutely continuous and there exists a dynamical optimal transference plan  $\Pi$  joining  $\mu_0$  to  $\mu_1$ , such that for any subplan  $\Pi' = \Pi / \Pi[\Gamma],$  $0 \leq \Pi \leq \Pi$ , it happens that  $\Pi'$  is the unique dynamical optimal transference plan between  $\mu'_0 = (e_0)_{\#} \Pi'$  and  $\mu'_1 = (e_1)_{\#} \Pi'.$ 

In particular,  $\Pi$  is the unique dynamical optimal transference plan between  $\mu_0$  and  $\mu_1$ , and, by Corollary 7.23,  $\mu_t = (e_t)_\# \Pi$  defines the unique displacement interpolation between  $\mu_0$  and  $\mu_1$ . In the sequel, I shall denote by  $\rho_t$  the density of  $\mu_t$ , and by  $\rho_0$ ,  $\rho_1$  the densities of the absolutely continuous parts of  $\mu_0$ ,  $\mu_1$  respectively. I shall also fix Borel sets  $S_0$ ,  $S_1$  such that  $\nu[S_0] = \nu[S_1] = 0$ ,  $\mu_{0,s}$  is concentrated on  $S_0$  and  $\mu_{1,s}$  is concentrated on  $S_1$ . By convention  $\rho_0$  is defined to be  $+\infty$  on S<sub>0</sub>; similarly  $\rho_1$  is defined to be  $+\infty$  on  $S_1$ .

Then let  $y \in \text{Spt } \mu_t$ , and let  $\delta > 0$ . Define

$$
\mathcal{Z} = \Big\{ \gamma \in \Gamma; \ \gamma_t \in B_{\delta}(y) \Big\},\
$$

and let  $\Pi' = (1_Z \Pi)/\Pi[\mathcal{Z}]$ . (If  $\gamma$  is a random variable distributed according to  $\Pi$ , then  $\Pi'$  is the law of  $\gamma$  conditioned by the event

" $\gamma_t \in B_\delta(y)$ ".) Let  $\mu'_t = (e_t)_\# \Pi'$ , let  $\rho'_t$  be the density of the absolutely continuous part of  $\mu'_t$ , and let  $\pi' := (e_0, e_1)_\# \Pi'$ . Since  $\Pi'$  is the unique dynamical optimal transference plan between  $\mu'_0$  and  $\mu'_1$ , we can write the displacement convexity inequality

$$
H_{N,\nu}(\mu'_t) \le (1-t) H_{N,\pi,\nu}^{\beta_{1-t}}(\mu'_0) + t H_{N,\check{\pi},\nu}^{\beta_t}(\mu'_1).
$$

In other words,

$$
\int_{\mathcal{X}} (\rho'_t)^{1-\frac{1}{N}} d\nu \ge (1-t) \int_{\mathcal{X} \times \mathcal{X}} (\rho'_0(x_0))^{-\frac{1}{N}} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} \pi'(dx_0 dx_1) + t \int_{\mathcal{X} \times \mathcal{X}} (\rho'_1(x_1))^{-\frac{1}{N}} \beta_t(x_0, x_1)^{\frac{1}{N}} \pi'(dx_0 dx_1)
$$
 $(30.40)$ 

with the understanding that  $\rho'_0(x_0) = +\infty$  when  $x_0 \in S_0$ , and similarly  $\rho'_1(x_1) = +\infty$  when  $x_1 \in S_1$ .

By reasoning as in the proof of Theorem 19.4, we obtain

$$
\frac{\nu[B_{\delta}(y)]^{\frac{1}{N}}}{\mu_t[B_{\delta}(y)]^{\frac{1}{N}}} \geq \mathbb{E}_{\Pi}\Big[(1-t)\left(\frac{\beta_{1-t}(\gamma_0,\gamma_1)}{\rho_0(\gamma_0)}\right)^{\frac{1}{N}} + t\left(\frac{\beta_t(\gamma_0,\gamma_1)}{\rho_1(\gamma_1)}\right)^{\frac{1}{N}} \mid \gamma_t \in B_{\delta}(y)\Big].
$$

If we define

$$
f(\gamma) := (1-t) \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_0(\gamma_0)} \right)^{\frac{1}{N}} + t \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_1(\gamma_1)} \right)^{\frac{1}{N}},
$$

then the conclusion can be rewritten

$$
\frac{\nu[B_{\delta}(y)]^{\frac{1}{N}}}{\mu_t[B_{\delta}(y)]^{\frac{1}{N}}} \geq \mathbb{E}_{\Pi}\Big[f(\gamma)|\gamma_t \in B_{\delta}(y)\Big] = \frac{\mathbb{E}\,f(\gamma)\mathbf{1}_{[\gamma_t \in B_{\delta}(y)]}}{\mu_t[B_{\delta}(y)]}.\tag{30.41}
$$

In view of the nonbranching property,  $\Pi$  only sees geodesics which do not cross each other; recall Theorem 7.30(iv)-(v). Let  $F_t$  be the map appearing in that theorem, defined by  $F_t(\gamma_t) = \gamma$ . Then (30.41) becomes

$$
\frac{\nu[B_{\delta}(y)]^{\frac{1}{N}}}{\mu_t[B_{\delta}(y)]^{\frac{1}{N}}} \ge \frac{\mathbb{E}\left[f(F_t(\gamma_t))1_{\left[\gamma_t \in B_{\delta}(y)\right]}
ight]}{\mu_t[B_{\delta}(y)]}
$$

$$
= \frac{\int_{B_{\delta}(y)} f(F_t(x)) d\mu_t(x)}{\mu_t[B_{\delta}(y)]}. \tag{30.42}
$$

Since the measure  $\nu$  is locally doubling, we can apply Lebesgue's density theorem: There is a set Z of zero  $\nu$ -measure such that if  $y \notin Z$ , then

$$
\frac{\nu[B_{\delta}(y)]^{\frac{1}{N}}}{\mu_t[B_{\delta}(y)]^{\frac{1}{N}}} \xrightarrow[\delta \to 0]{} \frac{1}{\rho_t(y)^{\frac{1}{N}}}.
$$

Similarly, outside of a set of zero measure,

$$
\frac{\int_{B_{\delta}(y)} f(F_t(x)) d\mu_t(x)}{\mu_t[B_{\delta}(y)]} = \frac{\int_{B_{\delta}(y)} f(F_t(x)) \rho_t(x) d\nu(x)}{\nu[B_{\delta}(y)]} \frac{\nu[B_{\delta}(y)]}{\mu_t[B_{\delta}(y)]}
$$

$$
\xrightarrow{\delta \to 0} \frac{f(F_t(y)) \rho_t(y)}{\rho_t(y)},
$$

and this coincides with  $f(F_t(y))$  if  $\rho_t(y) \neq 0$ . All in all,  $\mu_t(dy)$ -almost surely,

$$
\frac{1}{\rho_t(y)^{\frac{1}{N}}} \ge f(F_t(y)).
$$

Equivalently,  $\Pi(d\gamma)$ -almost surely,

$$
\frac{1}{\rho_t(\gamma_t)^{\frac{1}{N}}} \ge f(F_t(\gamma_t)) = f(\gamma).
$$

Let us recapitulate: We have shown that  $\Pi(d\gamma)$ -almost surely,

$$
\frac{1}{\rho_t(\gamma_t)^{\frac{1}{N}}} \ge (1-t) \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_0(\gamma_0)} \right)^{\frac{1}{N}} + t \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_1(\gamma_1)} \right)^{\frac{1}{N}}.
$$
 (30.43)

**Step 2:** Now we shall prove inequality (30.36) when  $\mu_0$  and  $\mu_1$  are compactly supported, and  $\mu_t$  is absolutely continuous. So let  $(\mu_s)_{0 \le s \le 1}$ be a displacement interpolation joining  $\mu_0$  to  $\mu_1$ , and let  $\Pi$  be a dynamical optimal transport plan with  $\mu_s = (e_s)_{\#} \Pi$ . Let  $\varepsilon \in (0, 1 - t)$ be given. By the nonbranching property and Theorem 7.30(iii), the restricted plan  $\Pi^{0,1-\varepsilon}$  obtained as the push-forward of  $\Pi$  by the restriction map from  $C([0,1];\mathcal{X})$  to  $C([0,1-\varepsilon];\mathcal{X})$  is the only dynamical optimal transport plan between  $\mu_0$  and  $\mu_{1-\varepsilon}$ ; and more generally, if  $0 \leq \overline{H} \leq \overline{H}^{0,1-\varepsilon}$  with  $\overline{H}[F] > 0$ , then  $\overline{H}' := \overline{H}/\overline{H}[F]$  is the only dynamical optimal transport plan between its endpoints measures. In other words,  $\tilde{\mu}_0 = \mu_0$  and  $\tilde{\mu}_1 = \mu_{1-\varepsilon}$  satisfy the assumptions used in Step 1. The only displacement interpolation between  $\tilde{\mu}_0$  and  $\tilde{\mu}_1$  is

 $\widetilde{\mu}_t = \mu_{(1-\varepsilon)t}$ , so we can apply formula (30.43) to that path, after timereparametrization. Writing

$$
t = \left(\frac{1-t-\varepsilon}{1-\varepsilon}\right) \times 0 + \left(\frac{t}{1-\varepsilon}\right) \times (1-\varepsilon),
$$

we see that,  $\Pi(d\gamma)$ -almost surely,

$$
\frac{1}{\rho_t(\gamma_t)^{\frac{1}{N}}} \ge \left(\frac{1-t-\varepsilon}{1-\varepsilon}\right) \left(\frac{\beta_{\frac{1-t-\varepsilon}{1-\varepsilon}}(\gamma_0, \gamma_{1-\varepsilon})}{\rho_0(\gamma_0)}\right)^{\frac{1}{N}} + \left(\frac{t}{1-\varepsilon}\right) \left(\frac{\beta_{\frac{t}{1-\varepsilon}}(\gamma_0, \gamma_{1-\varepsilon})}{\rho_{1-\varepsilon}(\gamma_{1-\varepsilon})}\right)^{\frac{1}{N}}.
$$
 (30.44)

Next, let us apply the same reasoning on the time-interval  $[t, 1]$ rather than  $[0, 1 - \varepsilon]$ . Write  $1 - \varepsilon$  as an intermediate point between t and 1:

$$
1 - \varepsilon = \left(\frac{\varepsilon}{1 - t}\right) \times t + \left(\frac{1 - t - \varepsilon}{1 - t}\right) \times 1.
$$

Since  $\mu_t$  is absolutely continuous and  $\mu_{1-\varepsilon}$  belongs to the unique displacement interpolation between  $\mu_t$  and  $\mu_1$ , it follows from Theorem 30.19(ii) that  $\mu_{1-\varepsilon}$  is absolutely continuous too. Then (30.43) becomes, after time-reparametrization,

$$
\frac{1}{\rho_{1-\varepsilon}(\gamma_{1-\varepsilon})^{\frac{1}{N}}} \geq \left(\frac{\varepsilon}{1-t}\right) \left(\frac{\beta_{\frac{\varepsilon}{1-t}}(\gamma_t, \gamma_1)}{\rho_t(\gamma_t)}\right)^{\frac{1}{N}} + \left(\frac{1-t-\varepsilon}{1-t}\right) \left(\frac{\beta_{\frac{1-t-\varepsilon}{1-t}}(\gamma_t, \gamma_1)}{\rho_1(\gamma_1)}\right)^{\frac{1}{N}} . (30.45)
$$

The combination of (30.44) and (30.45) yields

$$
\left(1-\left(\frac{t}{1-\varepsilon}\right)\left(\frac{\varepsilon}{1-t}\right)\beta_{\frac{t}{1-\varepsilon}}(\gamma_0,\gamma_{1-\varepsilon})^{\frac{1}{N}}\beta_{\frac{\varepsilon}{1-t}}(\gamma_t,\gamma_1)^{\frac{1}{N}}\right)\frac{1}{\rho_t(\gamma_t)^{\frac{1}{N}}} \\ \geq \left(\frac{1-t-\varepsilon}{1-\varepsilon}\right)\left(\frac{\beta_{\frac{1-t-\varepsilon}{1-\varepsilon}}(\gamma_0,\gamma_{1-\varepsilon})}{\rho_0(\gamma_0)}\right)^{\frac{1}{N}} \\ +\left(\frac{1-t-\varepsilon}{1-t}\right)\left(\frac{t}{1-\varepsilon}\right)\left(\frac{\beta_{\frac{1-t-\varepsilon}{1-t}}(\gamma_t,\gamma_1)\beta_{\frac{t}{1-\varepsilon}}(\gamma_0,\gamma_{1-\varepsilon})}{\rho_1(\gamma_1)}\right)^{\frac{1}{N}}. \end{split}
$$

Then we can pass to the limit as  $\varepsilon \to 0$  thanks to the continuity of  $\gamma$ and  $\beta$ ; since  $\beta_1(x,y) = 1$  for all x, y, we conclude that inequality (30.43) holds true almost surely.

Now let  $w(\delta) = u(\delta^{-N}) = \delta^{N} U(\delta^{-N}),$  with the convention  $w(0) =$  $U'(\infty)$ . By assumption w is a convex nonincreasing function of  $\delta$ . So (30.43) implies

$$
$\mathbb{E}$  u(\rho_t(\gamma_t)) =  $\mathbb{E}$  w\left( $\frac{1}{\rho_t(\gamma_t)^{\frac{1}{N}}}$ \right) \le (1-t)  $\mathbb{E}$  w\left(\left( $\frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_0(\gamma_0)}$ \right)^{\frac{1}{N}}\right) + t  $\mathbb{E}$  w\left(\left( $\frac{\beta_t(\gamma_0, \gamma_1)}{\rho_1(\gamma_1)}$ \right)^{\frac{1}{N}}\right). \quad (30.46)
$$

The left-hand side is just  $\int U(\rho_t(x))/\rho_t(x) d\mu_t(x) = \int U(\rho_t(x)) d\nu(x) =$  $U_{\nu}(\mu_t)$ . The first term in the right-hand side is  $(1-t) U_{\pi,\nu}^{\beta_{1-t}}(\mu_0)$ , since we chose to define  $\rho_0(x_0) = +\infty$  when  $x_0$  belongs to the singular set  $S_0$ . Similarly, the second term is  $t U_{\tilde{\pi},\nu}^{\beta_t}(\mu_1)$ . So (30.46) reads

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}}(\mu_0) + t U_{\check{\pi},\nu}^{\beta_t}(\mu_1),
$$

as desired.

Step 3: Now we wish to establish inequality (30.36) in the case when  $\mu_t$  is absolutely continuous, that is, we just want to drop the assumption of compact support.

It follows from Step 2 that  $(\mathcal{X}, d, \nu)$  is a weak  $CD(K, N)$  space, so we now have access to Theorem 30.19 even if  $\mu_0$  and  $\mu_1$  are not compactly supported; and also we can appeal to Theorem 30.5 to guarantee that Property (ii) is verified for probability measures that are not necessarily compactly supported. Then we can repeat Steps 1 and 2 without the assumption of compact support, and in the end establish inequality (30.36) for measures that are not compactly supported.

**Step 4:** Now we shall consider the case when  $\mu_t$  is not absolutely continuous. (This is the part of the proof which has interest even in a smooth setting.) Let  $(\mu_t)_s$  stand for the singular part of  $\mu_t$ , and  $m := (\mu_t)_s[\mathcal{X}] > 0.$ 

Let  $E^{(a)}$  and  $E^{(s)}$  be two disjoint Borel sets in X such that the absolutely continuous part of  $\mu_t$  is concentrated on  $E^{(a)}$ , and the singular part of  $\mu_t$  is concentrated on  $E^{(s)}$ . Obviously,  $\Pi[\gamma_t \in E^{(s)}] =$  $(\mu_t)_s[\mathcal{X}] = m$ , and  $\Pi[\gamma_t \in E^{(a)}] = 1 - m$ . Let us decompose  $\Pi$  into  $\Pi = (1 - m) \Pi^{(a)} + m \Pi^{(s)}$ , where

$$
\Pi^{(a)}(d\gamma) = \frac{1_{[\gamma_t \in E^{(a)}]} \Pi(d\gamma)}{\Pi[\gamma_t \in E^{(a)}]}, \qquad \Pi^{(s)}(d\gamma) = \frac{1_{[\gamma_t \in E^{(s)}]} \Pi(d\gamma)}{\Pi[\gamma_t \in E^{(s)}]}.
$$

Further, for any  $s \in [0, 1]$ , let

$$
\mu_s^{(a)} = (e_s)_\# \Pi^{(a)}, \qquad \mu_s^{(s)} = (e_s)_\# \Pi^{(s)},
$$
  
\n $\pi^{(a)} = (e_0, e_1)_\# \Pi^{(a)}, \qquad \pi^{(s)} = (e_0, e_1)_\# \Pi^{(s)}.$ 

Since it has been obtained by conditioning of a dynamical optimal transference plan,  $\Pi^{(a)}$  is itself a dynamical optimal transference plan (Theorem 7.30(ii)), and by construction  $\mu_t^{(a)}$  $t_t^{(u)}$  is the absolutely continuous part of  $\mu_t$ , while  $\mu_t^{(s)}$  $t_t^{(s)}$  is its singular part. So the result of Step 2 applies to the path  $(\mu_s^{(a)})_{0 \le s \le 1}$ :

$$
U_{\nu}(\mu_t^{(a)}) \le (1-t) U_{\pi^{(a)},\nu}^{\beta_{1-t}}(\mu_0^{(a)}) + t U_{\pi^{(a)},\nu}^{\beta_t}(\mu_1^{(a)}).
$$

Actually, we shall not apply this inequality with the nonlinearity  $U$ , but rather with  $U_m(r) = U((1 - m)r)$ , which lies in  $\mathcal{DC}_N$  if U does. So

$$
(U_m)_{\nu}(\mu_t^{(a)}) \le (1-t) (U_m)_{\pi^{(a)},\nu}^{\beta_{1-t}}(\mu_0^{(a)}) + t (U_m)_{\pi^{(a)},\nu}^{\beta_t}(\mu_1^{(a)})
$$
 (30.47)

Since  $\mu_t^{(s)}$  $t_t^{(s)}$  is purely singular and  $\mu_t = (1 - m) \mu_t^{(a)} + m \mu_t^{(s)}$  $t^{(s)}$ , the definition of  $U_{\nu}$  implies

$$
U_{\nu}(\mu_t) = (U_m)_{\nu}(\mu_t^{(a)}) + m U'(\infty). \tag{30.48}
$$

By Theorem 30.19(iii),  $\mu_0^{(s)}$  $\int_{0}^{(s)}$  is purely singular. In view of the identity  $\mu_0 = (1 - m) \mu_0^{(a)} + m \mu_0^{(s)}$  $\binom{s}{0}$ 

$$
U_{\pi,\nu}^{\beta_{1-t}}(\mu_0) = (U_m)_{\pi^{(a)},\nu}^{\beta_{1-t}}(\mu_0^{(a)}) + m U'(\infty).
$$
 (30.49)

Similarly,

$$
U^{\beta_t}_{\check{\pi},\nu}(\mu_1) = (U_m)^{\beta_t}_{\check{\pi}^{(a)},\nu}(\mu_1^{(a)}) + m U'(\infty). \tag{30.50}
$$

The combination of (30.47), (30.48), (30.49) and (30.50) implies

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}}(\mu_0) + t U_{\tilde{\pi},\nu}^{\beta_t}(\mu_1).
$$

This concludes the proof in the case  $N < \infty$ .

When  $N = \infty$ , at first sight things look pretty much the same; formula (30.43) should be replaced by

$$
\log \frac{1}{\rho_t(\gamma_t)} \ge (1-t) \log \frac{1}{\rho_0(\gamma_0)} + t \log \frac{1}{\rho_1(\gamma_1)} + \frac{Kt(1-t)}{2} d(\gamma_0, \gamma_1)^2.
$$
\n(30.51)

At a technical level, there is a small simplification since it is not necessary to treat singular measures (if  $\mu$  is singular and U is not linear, then according to Proposition 17.7(ii)  $U'(\infty) = +\infty$ , so  $U_{\nu}(\mu) = +\infty$ ). On the other hand, there is a serious complication: The proof of Step 1 breaks down since the measure  $\nu$  is not a priori locally doubling, and Lebesgue's density theorem does not apply!

It seems a bit of a miracle that the method of proof can still be saved, as I shall now explain. First assume that  $\rho_0$  and  $\rho_1$  satisfy the same assumptions as in Step 1 above, but that in addition they are upper semicontinuous. As in Step 1, define

$$
f(\gamma) = (1 - t) \log \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_0(\gamma_0)} \right) + t \log \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_1(\gamma_1)} \right)
$$

$$
= (1 - t) \log \frac{1}{\rho_0(\gamma_0)} + t \log \frac{1}{\rho_1(\gamma_1)} + \frac{Kt(1 - t)}{2} d(\gamma_0, \gamma_1)^2.
$$

The argument of Step 1 shows that

$$
\log\left(\frac{\nu[B_{\delta}(y)]}{\mu_t[B_{\delta}(y)]}\right) \ge \frac{\int_{B_{\delta}(y)} f(F_t(x)) \mu_t(dx)}{\mu_t[B_{\delta}(y)]};
$$

in particular  $\mu_t[B_\delta(y)] \le \exp \left(-\inf_{x \in B_\delta(y)}$  $f(F_t(x))$   $\nu[B_\delta(y)]$ . Similarly, for any  $z \in B_{\delta/2}(y)$  and  $r \leq \delta/2$ ,

$$
\mu_t[B_r(z)] \le \exp\left(-\inf_{x \in B_\delta(y)} f(F_t(x))\right) \nu[B_r(z)]. \tag{30.52}
$$

The family of balls  $\{B_r(z); z \in B_{\delta/2}(y); r \leq \delta/2\}$  generates the Borel  $\sigma$ -algebra of  $B_{\delta/2}(y)$ , so (30.52) holds true for any measurable set  $S \subset$  $B_{\delta/2}(y)$  instead of  $B_r(z)$ . Then we can pass to densities:

$$
\rho_t(z) \le \exp\left(-\inf_{x \in B_\delta(y)} f(F_t(x))\right) \qquad \text{almost surely in } B_{\delta/2}(y).
$$

In particular, for almost any z,

$$
\rho_t(z) \le \sup_{x \in B_{2\delta}(z)} e^{-f(F_t(x))}.
$$
\n(30.53)

Now note that the map  $F_t$  from Theorem 7.30(v) is *continuous* on Spt  $\Pi$ . Indeed, if a sequence  $(\gamma_k)_{k\in\mathbb{N}}$  of geodesics is given in Spt  $\Pi$ , in such a way that  $\gamma_k(t) \to \gamma(t)$ , by compactness there is a subsequence, still denoted  $\gamma_k$ , which converges uniformly to some geodesic  $\tilde{\gamma} \in \text{Spt } \Pi$ and satisfying  $\tilde{\gamma}(t) = \gamma(t)$ ; which implies that  $\tilde{\gamma} = \gamma$ . (In fact I used the same argument to prove the measurability of  $F_t$  in the case when Spt  $\Pi$ is not necessarily compact.) Since  $\rho_0$  and  $\rho_1$  are upper semicontinuous, f is lower semicontinuous; so  $e^{-f(F_t)}$  is upper semicontinuous, and

$$
\lim_{\delta \downarrow 0} \sup_{x \in B_{\delta}(z)} e^{-f(F_t(x))} \le e^{-f(F_t(z))}.
$$

So we may pass to the limit as  $\delta \to 0$  in (30.53) and recover  $\rho_t \leq e^{-f \circ F_t}$ , or in other words

$$
\log \frac{1}{\rho_t(\gamma_t)} \ge (1-t) \log \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_0(\gamma_0)} \right) + t \log \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_1(\gamma_1)} \right). (30.54)
$$

This is the desired estimate, but under the additional assumption of upper semicontinuity of  $\rho_0$  and  $\rho_1$ . In the general case, we still have

$$
\log \frac{1}{\rho_t(\gamma_t)} \ge (1-t) \log \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\overline{\rho}_0(\gamma_0)} \right) + t \log \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\overline{\rho}_1(\gamma_1)} \right), (30.55)
$$

where  $\overline{\rho}_0$  and  $\overline{\rho}_1$  are upper semicontinuous and  $\rho_0 \le \overline{\rho}_0$ ,  $\rho_1 \le \overline{\rho}_1$ .

Next recall that if  $q$  is any nonnegative measurable function, there is a sequence  $(g_k)_{k\in\mathbb{N}}$  of nonnegative upper semicontinuous functions such that  $0 \leq g_k \leq g$  out of a set of zero measure, and  $g_k \uparrow g$  almost surely as  $k \to \infty$ . Indeed, one can write g as a nondecreasing limit of simple functions  $h_j = \sum_{\ell} \lambda_j^{\ell} 1_{B_j^{\ell}}$ , where  $(B_j^{\ell})_{1 \leq \ell \leq L_j}$  is a finite family of Borel sets. For each  $B_j^{\ell}$ , the regularity of the measure allows one to find a nondecreasing sequence of compact sets  $(K^{\ell}_{j,m})_{m\in\mathbb{N}}$  included in  $B^{\ell}_{j}$ , such that  $\nu[K_{j,m}^{\ell}] \longrightarrow \nu[B_j^{\ell}].$  So  $h_{j,m} = \sum_{j} \lambda_j^{\ell} 1_{K_{j,m}^{\ell}}$  converges monotonically to  $h_j$  as  $m \to \infty$ , up to a set of zero  $\nu$ -measure. Each  $h_{j,m}$  is obviously upper semicontinuous. Then choose  $g_k = \max\{h_{j,i}; j \leq k\}$ : this is still upper semicontinuous (the maximum is over a finite set of upper semicontinuous functions). For any  $\ell$  and any  $k \geq \ell$  we have  $g_k \geq h_{\ell,k}$ , which converges to  $g_{\ell}$  as  $k \to \infty$ ; so liminf  $g_k \geq g$ , almost surely.

Coming back to the proof of Theorem 30.32, I shall now proceed to approximate  $\Pi$ . Let  $(g_k)_{k\in\mathbb{N}}$  be a sequence of upper semicontinuous functions such that  $0 \leq g_k \leq \rho_0$  and  $g_k \uparrow \rho_0$  up to a *v*-negligible set. Let

.

$$
Z_k = \int g_k \, d\nu, \qquad \rho_{k,0} = \frac{g_k}{Z_k}
$$

Next disintegrate  $\Pi$  with respect to its marginal  $(e_0)_\# \Pi$ :

$$
\Pi(d\gamma) = \rho(\gamma_0) \nu(d\gamma_0) \Pi(d\gamma|\gamma_0),
$$

and define

$$
\Pi'_k(d\gamma) = g_k(\gamma_0) \nu(d\gamma_0) \Pi(d\gamma|\gamma_0); \qquad \Pi_k = \frac{\Pi'_k}{Z_k}.
$$

Then  $\Pi_k$  is a probability measure on geodesics, and since it has been obtained from  $\Pi$  by restriction, it is actually the unique dynamical optimal transference plan between the two probability measures  $\mu_{k,0} =$  $(e_0)_\# \Pi_k$  and  $\mu_{k,1} = (e_1)_\# \Pi_k$ . From the construction of  $\Pi_k$ ,

$$
\mu_{k,0} = \rho_{k,0} \nu;
$$
\n $\mu_{k,1} = \rho_{k,1} \nu;$ \n $\rho_{k,1} \le \frac{\rho_1}{Z_k}.$ 

Next we repeat the process at the other end: Let  $(h_{k,\ell})_{\ell \in \mathbb{N}}$  be a nonincreasing sequence of upper semicontinuous functions such that  $0 \leq h_{k,\ell} \leq \rho_{k,1}$  and  $h_{k,\ell} \uparrow \rho_{k,1}$  (up to a set of zero measure). Define

$$
Z_{k,\ell} = \int h_{k,\ell} d\nu, \qquad \rho_{k,\ell,1} = \frac{h_{k,\ell}}{Z_{k,\ell}};
$$
$$
\Pi' _{k,\ell}(d\gamma) = \Pi_k(d\gamma|\gamma_1) h_{k,\ell}(\gamma_1) \nu(d\gamma_1); \qquad \Pi_{k,\ell}(d\gamma) = \frac{\Pi'_{k,\ell}(d\gamma)}{Z_{k,\ell}}.
$$

Then again  $\Pi_{k,\ell}$  is the unique dynamical optimal transference plan between its marginals  $\mu_{k,\ell,0} = (e_0)_\# \Pi_{k,\ell}$  and  $\mu_{k,\ell,1} = \rho_{k,\ell,1} \nu$ .

If t is any time in [0, 1] and  $\rho_{k,t}$  (resp.  $\rho_{k,\ell,t}$ ) stands for the density of  $(e_t)_\# \Pi_k$  (resp.  $(e_t)_\# \Pi_{k,\ell}$ ) with respect to  $\nu$ , then

$$
Z_k \rho_{k,t} \uparrow \rho_t \text{ as } k \to \infty;
$$
  
$$
Z_{k,\ell} \rho_{k,\ell,t} \uparrow \rho_{k,t} \text{ as } \ell \to \infty.
$$

Moreover,  $\rho_{k,0}$  and  $\rho_{k,\ell,1}$  are upper semicontinuous; in particular  $\rho_{k,\ell,0} \leq$  $\rho_{k,0}/Z_{k,\ell}$ , which is upper semicontinuous. Then we can apply (30.55) with the dynamical plan  $\Pi_{k,\ell}$  and get

$$
\log \frac{1}{\rho_{k,\ell,t}(\gamma_t)} \ge (1-t) \log \left( \frac{Z_{k,\ell} \beta_{1-t}(\gamma_0, \gamma_1)}{\rho_{0,k}(\gamma_0)} \right) + t \log \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_{k,\ell,1}(\gamma_1)} \right).
$$

By letting  $\ell \to \infty$  and then  $k \to \infty$ , we conclude that (30.54) is true, but this time without any upper semicontinuity assumption.

This concludes the proof of Step 1 in the case  $N = \infty$ . Then Steps 2 d 3 are done as before. and 3 are done as before.

## Locality

Locality is one of the most fundamental properties that one may expect from any notion of curvature. In the setting of weak  $CD(K, N)$  spaces, the locality problem may be loosely formulated as follows: If  $(\mathcal{X}, d, \nu)$  is weakly  $CD(K, N)$  in the neighborhood of any of its points, then  $(X, d, \nu)$ should be a weakly  $CD(K, N)$  space.

So far it is not known whether this "local-to-global" property holds in general. However, it is true at least in a *nonbranching space*, if  $K = 0$ and  $N < \infty$ . The validity of a more general statement may depend on the following:

Conjecture 30.34 (Local-to-global  $CD(K, N)$  property along a **path).** Let  $\theta \in (0,1)$  and  $\alpha \in [0,\pi]$ . Let  $f:[0,1] \to \mathbb{R}_+$  be a measurable function such that for all  $\lambda \in [0,1]$ ,  $t, t' \in [0,1]$ , the inequality

$$
f((1 - \lambda)t + \lambda t') \ge (1 - \lambda) \left( \frac{\sin((1 - \lambda)\alpha|t - t'|)}{(1 - \lambda)\sin(\alpha|t - t'|)} \right)^{\theta} f(t) + \lambda \left( \frac{\sin(\lambda\alpha|t - t'|)}{\lambda\sin(\alpha|t - t'|)} \right)^{\theta} f(t') \quad (30.56)
$$

holds true as soon as  $|t-t^{\prime}|$  is small enough. Then  $(30.56)$  automatically holds true for all  $t, t' \in [0, 1]$ .

The same if sin is replaced by sinh and  $\alpha$  is allowed to vary in  $\mathbb{R}_+$ .

I really don't have much to support this conjecture, except that it would imply a really nice (to my taste) result. It might be trivially false or trivially true, but I was unable to prove or disprove it. (If it would hold true only under additional regularity assumptions such as local integrability or continuity of  $f$ , this might be fine.)

To understand the relation of (30.56) to optimal transport, take  $\theta = 1 - 1/N$ ,  $\alpha = \sqrt{|K|/(N-1)} d(\gamma_0, \gamma_1)$ ,  $f(t) = \rho_t(\gamma_t)^{-1/N}$ , and write  $I_t(\gamma_0, \gamma_t, \gamma_1)$  for the inequality appearing in (30.43). Then Conjecture 30.34, if true, means that this inequality is local, in the sense that if  $I_t(\gamma_{t_0}, \gamma_{(1-t)t_0+t t_1}, \gamma_{t_1})$  holds true for  $|t_0-t_1|$  small enough, then it holds true for all  $t_0, t_1$ , and in particular  $t_0 = 0, t_1 = 1$ .

There are at least two limit cases in which Conjecture 30.34 becomes true. The first one is for  $\alpha = 0$  and  $\theta$  fixed (this corresponds to CD(0, N),  $N = 1/(1 - \theta)$ ; the second one is the limit when  $\theta \to 1$ ,

 $\alpha \to 0$  in such a way that  $\alpha^2/(1-\theta)$  converges to a finite limit (this corresponds to  $CD(K, \infty)$ , and the limit of  $\alpha^2/(1-\theta)$  would be  $K d(\gamma_0, \gamma_1)^2$ ). In the first case, Conjecture 30.56 reduces to the locality of the property of concavity:

$$
f((1 - \lambda) t + \lambda t') \ge (1 - \lambda) f(t) + \lambda f(t');
$$

while in the second case, it reduces to the locality of the more general property of  $\kappa$ -concavity  $(\kappa \in \mathbb{R})$ :

$$
f((1 - \lambda)t + \lambda t') \ge (1 - \lambda) f(t) + \lambda f(t') + \frac{\kappa \lambda (1 - \lambda)}{2} |t - t'|^2. (30.57)
$$

These properties do satisfy a local-to-global principle, for instance because they are equivalent to the differential inequality  $f'' \n\leq 0$ , or  $f'' \leq -\kappa$ , to be understood in the distributional sense.

To summarize: If  $K = 0$  (resp.  $N = \infty$ ), inequality (30.43) (resp. (30.51)) satisfies a local-to-global principle; in the other cases I don't know.

Next, I shall give a precise definition of what it means to satisfy  $CD(K, N)$  locally:

Definition 30.35 (Local CD(K, N) space). Let  $K \in \mathbb{R}$  and  $N \in$  $[1,\infty]$ . A locally compact Polish geodesic space  $(\mathcal{X},d)$  equipped with a locally finite measure  $\nu$  is said to be a local weak  $CD(K, N)$  space if for any  $x_0 \in \mathcal{X}$  there is  $r > 0$  such that whenever  $\mu_0$ ,  $\mu_1$  are two probability measures supported in  $B_r(x_0) \cap \text{Spt } \nu$ , there is a displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  joining  $\mu_0$  to  $\mu_1$ , and an associated optimal coupling  $\pi$ , such that for all  $t \in [0,1]$  and for all  $U \in \mathcal{DC}_N$ ,

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) + t U_{\check{\pi},\nu}^{\beta_t^{(K,N)}}(\mu_1). \tag{30.58}
$$

Remark 30.36. In the previous definition, one could also have imposed that the whole path  $(\mu_t)_{0 \leq t \leq 1}$  is supported in  $B_r(x_0)$ . Both formulations are equivalent: Indeed, if  $\mu_0$  and  $\mu_1$  are supported in  $B_{r/3}(x_0)$ then all measures  $\mu_t$  are supported in  $B_r(x_0)$ .

Now comes the main result in this section:

Theorem 30.37 (From local to global  $CD(K, N)$ ). Let  $K \in \mathbb{R}$ ,  $N \in [1,\infty)$ , and let  $(\mathcal{X},d,\nu)$  be a nonbranching local weak  $CD(K,N)$ space with  $\text{Spt } \nu = \mathcal{X}$ . If  $K = 0$ , then X is also a weak  $CD(K, N)$ space. The same is true for all values of  $K$  if Conjecture 30.34 has an affirmative answer.

**Remark 30.38.** If the assumption  $Spt \nu = \mathcal{X}$  is dropped then the result becomes trivially false. As a counterexample, take  $\mathcal{X} = \mathbb{R}^3$ , equipped with the Euclidean distance, and let  $\nu$  be the 2-dimensional Lebesgue measure on each horizontal plane of integer altitude. (So the measure is concentrated on well-separated parallel planes.) This is a local weak  $CD(0, 2)$  space but not a weak  $CD(0, 2)$  space.

Remark 30.39. I don't know if the nonbranching condition can be removed in Theorem 30.37.

As in the proof of Theorem 30.32, one of the main ideas in the proof of Theorem 30.37 consists in using the nonbranching condition to translate integral conditions into pointwise density bounds along geodesic paths. Another idea consists in "cutting" dynamical optimal transference plans into small pieces, each of which is "small enough" that the local displacement convexity can be applied. The fact that we work along geodesic paths parametrized by [0, 1] explains that the whole locality problem is reduced to the one-dimensional "local-toglobal" problem exposed in Conjecture 30.34.

*Proof of Theorem 30.37.* If we can treat the case  $N > 1$ , then the case  $N = 1$  will follow by letting N go to 1 (as in the proof of Theorem 29.24). So let us assume  $1 < N < \infty$ . In the sequel, I shall use the shorthand  $\beta_t = \beta_t^{(K,N)}$  $t^{(N,IV)}.$ 

Let  $(\mathcal{X}, d, \nu)$  be a nonbranching local weak  $CD(K, N)$  space. By repeating the proof of Theorem 30.32, we can show that for any  $x_0 \in \mathcal{X}$ there is  $r = r(x_0) > 0$  such that (30.58) holds true along any displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  which is supported in  $B(x_0, r)$ . Moreover, if  $\Pi$  is a dynamical optimal transference plan such that  $(e_t)_{\#}\Pi = \mu_t$ , and each measure  $\mu_t$  is absolutely continuous with density  $\rho_t$ , then  $\Pi(d\gamma)$ -almost all geodesics will satisfy inequality (30.43), which I recast below:

$$
\frac{1}{\rho_t(\gamma_t)^{\frac{1}{N}}} \ge (1-t) \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_0(\gamma_0)} \right)^{\frac{1}{N}} + t \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_1(\gamma_1)} \right)^{\frac{1}{N}}.
$$
 (30.59)

Let  $\mu_0$ ,  $\mu_1$  be any two compactly supported probability measures on X, and let  $B = B(z, R)$  be a large ball such that any geodesic going from  $\text{Spt}\,\mu_0$  to  $\text{Spt}\,\mu_1$  lies within B. Let  $\Pi$  be a dynamical optimal transference plan between  $\mu_0$  and  $\mu_1$ . The goal is to prove that for all  $U \in \mathcal{DC}_N$ ,

Locality 909

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}}(\mu_0) + t U_{\check{\pi},\nu}^{\beta_t}(\mu_1). \tag{30.60}
$$

The plan is to cut  $\Pi$  into very small pieces, each of which will be included in a sufficiently small ball that the local weak  $CD(K, N)$ criterion can be used. I shall first proceed to construct these small pieces.

Cover the closed ball  $B[z, R]$  by a finite number of balls  $B(x_i, r_i/3)$ with  $r_j = r(x_j)$ , and let  $\overline{r} := \inf(r_j/3)$ . For any  $y \in B[z, R]$ , the ball  $B(y, \overline{r})$  lies inside some  $B(x_j, r_j)$ ; so if  $(\overline{\mu}_t)_{0 \le t \le 1}$  is any displacement interpolation supported in some ball  $B(y, \overline{r})$ ,  $\overline{\Pi}$  is an associated dynamical optimal transference plan, and  $\overline{\mu}_0$ ,  $\overline{\mu}_1$  are absolutely continuous, then the density  $\overline{\rho}_t$  of  $\overline{\mu}_t$  will satisfy the inequality

$$
\frac{1}{\overline{\rho}_t(\gamma_t)^{\frac{1}{N}}} \ge (1-t) \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\overline{\rho}_0(\gamma_0)} \right)^{\frac{1}{N}} + t \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\overline{\rho}_1(\gamma_1)} \right)^{\frac{1}{N}}, \quad (30.61)
$$

 $\overline{\Pi}(d\gamma)$ -almost surely. The problem now is to cut  $\Pi$  into many small subplans  $\overline{\Pi}$  and to apply (30.61) to all these subplans.

Let  $\delta \in 1/\mathbb{N}$  be small enough that  $4R \delta \leq \overline{r}/3$ , and let  $B(y_\ell, \delta)_{1 \leq \ell \leq L}$ be a finite covering of  $B[z, R]$  by balls of radius  $\delta$ . Define  $A_1 = B(y_1, \delta)$ ,  $A_2 = B(y_2, \delta) \setminus A_1, A_3 = B(y_3, \delta) \setminus (A_1 \cup A_2),$  etc. This provides a covering of  $B(z, R)$  by *disjoint* sets  $(A_{\ell})_{1 \leq \ell \leq L}$ , each of which is included in a ball of radius  $\delta$ . (Without loss of generality, we can assume that they are all nonempty.)

Let  $m = 1/\delta \in \mathbb{N}$ . We divide the set  $\Gamma$  of all geodesics going from Spt  $\mu_0$  to Spt  $\mu_1$  into pieces, as follows. For any finite sequence  $\underline{\ell}$  =  $(\ell_0, \ell_1, \ldots, \ell_m)$ , let

$$
\Gamma_{\underline{\ell}} = \Big\{ \gamma \in \Gamma; \ \gamma_0 \in A_{\ell_0}, \ \gamma_\delta \in A_{\ell_1}, \ \gamma_{2\delta} \in A_{\ell_2}, \ldots, \ \gamma_{m\delta} = \gamma_1 \in A_{\ell_m} \Big\}.
$$

The sets  $\Gamma_{\underline{\ell}}$  are disjoint. We discard the sequences  $\underline{\ell}$  such that  $\Pi[\Gamma_{\underline{\ell}}] = 0$ . Then let  $Z_{\ell} = \Pi[\Gamma_{\ell}],$  and let

$$
\varPi_{\underline{\ell}} = \frac{\mathbb{1}_{\varGamma_{\underline{\ell}}}\varPi_{\underline{\ell}}}{Z_{\underline{\ell}}}
$$

be the law of  $\gamma$  conditioned by the event  $\{\gamma \in \Gamma_{\underline{\ell}}\}$ . Further, let  $\mu_{\underline{\ell},t} =$  $(e_t)_{\#}\Pi_{\underline{\ell}}$ , and  $\pi_{\underline{\ell}} = (e_0, e_1)_{\#}\Pi_{\underline{\ell}}$ .

For each  $\underline{\ell}$  and  $k \in \{0, \ldots, m-2\}$ , we define  $\Pi_{\underline{\ell}}^k$  to be the image of  $\Pi_{\underline{\ell}}$ by the restriction map  $[0, 1] \rightarrow [k\delta, (k+2)\delta]$ . Up to affine reparametrization of time,  $\Pi_{\underline{\ell}}^k$  is a dynamical optimal transference plan between the measures  $\mu_{\ell,k\delta}$  and  $\mu_{\ell,(k+2)\delta}$  (Theorem 7.30(i)–(ii)).

Let  $\gamma$  be a random geodesic distributed according to the law  $\Pi_{\underline{\ell}}^k$ . Almost surely,  $\gamma(k\delta)$  belongs to  $A_{\ell_k}$ , which has a diameter at most  $\overline{r}/3$ . Moreover, the speed of  $\gamma$  is bounded above by diam  $(B[z, R]) \leq 2R$ , so on the time-interval  $[k\delta, (k+2)\delta], \gamma$  moves at most by a distance  $(2\delta)(2R) \leq \overline{r}/3$ . Thus  $\gamma$  is entirely contained in a set of diameter  $2\overline{r}/3$ . In particular,  $(\mu_{\ell,t}^k)_{k\delta \le t \le (k+2)\delta}$  is entirely supported in a set of diameter  $\overline{r}$ , and satisfies the displacement convexity inequalities which are typical of the curvature-dimension bound  $CD(K, N)$ .

By Theorem 7.30(iii),  $\mu_{\underline{\ell},t}^k$  is (up to time-reparametrization) the unique optimal dynamical transference plan between  $\mu_{\underline{\ell},k\delta}$  and  $\mu_{\underline{\ell},(k+2)\delta}$ . So by Theorem 30.19(ii), the absolute continuity of  $\mu_{\ell,k\delta}$  implies the absolute continuity of  $\mu_{\ell,t}$  for all  $t \in [k\delta, (k+2)\delta)$ . Since  $\mu_{\ell,0}$  is absolutely continuous, an immediate induction shows that  $\mu_{\ell,t}$  is absolutely continuous for all times. Then we can apply (30.61) to each path  $(\mu_{\ell,t})_{k\delta \leq t \leq (k+2)\delta}$ ; after time reparametrization, this becomes:

$$
\forall k \in \{0, \dots, m-2\},
$$
  

$$
\Pi_{\underline{\ell}}(d\gamma)\text{-almost surely}, \forall t \in [0,1], \forall (t_0, t_1) \in [k\delta, (k+2)\delta],
$$
  

$$
\frac{1}{\rho_{\underline{\ell},(1-t)t_0+tt_1}(\gamma_{(1-t)t_0+tt_1})^{\frac{1}{N}}} \ge (1-t)\left(\frac{\beta_{1-t}(\gamma_{t_0}, \gamma_{t_1})}{\rho_{\underline{\ell},t_0}(\gamma_{t_0})}\right)^{\frac{1}{N}}+t\left(\frac{\beta_t(\gamma_{t_0}, \gamma_{t_1})}{\rho_{\underline{\ell},t_1}(\gamma_{t_1})}\right)^{\frac{1}{N}}
$$
 $(30.62)$ 

.

So inequality (30.62) is satisfied whenever  $|t_0 - t_1| \leq \delta$ . Then our assumptions, and the discussion following Conjecture 30.34, imply that the same inequality is satisfied for all values of  $t_0$  and  $t_1$  in [0, 1]. In particular,  $\Pi_{\ell}$ -almost surely,

$$
\frac{1}{\rho_{\underline{\ell},t}(\gamma_t)^{\frac{1}{N}}} \ge (1-t) \left( \frac{\beta_{1-t}(\gamma_0, \gamma_1)}{\rho_{\underline{\ell},0}(\gamma_0)} \right)^{\frac{1}{N}} + t \left( \frac{\beta_t(\gamma_0, \gamma_1)}{\rho_{\underline{\ell},1}(\gamma_1)} \right)^{\frac{1}{N}}.
$$
 (30.63)

By reasoning as in the proof of Theorem 30.32 (end of Step 2), we deduce the inequality

$$
U_{\nu}(\mu_{\underline{\ell},t}) \le (1-t) U_{\pi_{\underline{\ell}},\nu}^{\beta_{1-t}}(\mu_{\underline{\ell},0}) + t U_{\check{\pi}_{\underline{\ell}},\nu}^{\beta_t}(\mu_{\underline{\ell},1}). \tag{30.64}
$$

Recall that  $\mu_t = \sum Z_{\ell} \mu_{\ell,t}$ ; so the issue is now to add up the various contributions coming from different values of  $\ell$ .

For each  $\underline{\ell}$ , we apply (30.64) with U replaced by  $U_{\underline{\ell}} = U(Z_{\underline{\ell}} \cdot)/Z_{\underline{\ell}}$ . Then, with the shorthand  $U_{\ell,\nu} = (U_{\ell})_{\nu}$  and  $U_{\ell,\pi_{\ell},\nu}^{\beta} = (U_{\ell})_{\pi_{\ell},\nu}^{\beta}$ , we obtain

Locality 911

$$
U_{\underline{\ell},\nu}(\mu_{\underline{\ell},t}) \le (1-t) U_{\underline{\ell},\pi_{\underline{\ell}},\nu}^{\beta_{1-t}}(\mu_{\underline{\ell},0}) + t U_{\underline{\ell},\check{\pi}_{\underline{\ell}},\nu}^{\beta_t}(\mu_{\underline{\ell},1}). \tag{30.65}
$$

For any  $t \in (0,1)$ , the map  $\gamma_t \to \gamma$  is injective, as a consequence of Theorem 7.30(iv)–(v), and in particular the measures  $\mu_{\ell,t}$  are mutually singular as  $\underline{\ell}$  varies. Then it follows from Lemma 29.7 that

$$
U_{\nu}(\mu_t) = \sum_{\underline{\ell}} Z_{\underline{\ell}} U_{\ell,\nu}(\mu_{\underline{\ell},t}). \tag{30.66}
$$

Since  $\pi = \sum_{\ell} Z_{\ell} \pi_{\ell}$ , Lemma 29.7 also implies

$$
\sum_{\underline{\ell}} Z_{\underline{\ell}} U_{\underline{\ell}, \pi_{\underline{\ell}}, \nu}^{\beta_{1-t}}(\mu_{\underline{\ell}, 0}) \leq U_{\pi, \nu}^{\beta_{1-t}}(\mu_0);
$$
$$
\sum_{\underline{\ell}} Z_{\underline{\ell}} U_{\underline{\ell}, \check{\pi}_{\underline{\ell}}, \nu}^{\beta_t}(\mu_{\underline{\ell}, 1}) \leq U_{\check{\pi}, \nu}^{\beta_t}(\mu_1).
$$
 $(30.67)$ 

The combination of (30.65), (30.66) and (30.67) implies the desired conclusion  $(30.60)$ . □

In the case  $N = \infty$ , Conjecture 30.34 is satisfied; however, I don't know if Theorem 30.37 can be extended to that case without additional assumptions; the problem is that  $H_{\nu}(\mu)$  might be + $\infty$ . More precisely, if either  $H_{\nu}(\mu_{k\delta})$  or  $H_{\nu}(\mu_{(k+2)\delta})$  is  $+\infty$ , then we cannot derive (30.51) between times  $k\delta$  and  $(k+2)\delta$ , so the proof breaks down.

To get around this problem, I shall impose further assumptions ensuring that the space is "almost everywhere" finite-dimensional. Let us agree that a point x in a metric-measure space  $(\mathcal{X}, d, \nu)$  is finitedimensional if there is a small ball  $B_r(x)$  in which the criterion for  $CD(K', N')$  is satisfied, where  $K' \in \mathbb{R}$  and  $N' < \infty$ . More explicitly, it is required that for any two probability measures  $\mu_0$ ,  $\mu_1$  supported in  $B_r(x) \cap \text{Spt } \nu$ , there is a displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  and an associated coupling  $\pi$  such that for all  $U \in \mathcal{DC}_N$ ,

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) + t U_{\tilde{\pi},\nu}^{\beta_t^{(K,N)}}(\mu_1).
$$

A point will be called infinite-dimensional if it is not finite-dimensional.

**Example 30.40.** Let  $\varphi : \mathbb{R} \to \mathbb{R} \cup \{+\infty\}$  be a convex function with domain  $(a, b)$ , where a, b are two real numbers. (So  $\varphi$  takes the value  $+\infty$  outside of  $(a,b)$ .) Equip R with the usual distance and the measure  $\nu(dx) = e^{-\varphi(x)} dx$ ; this gives a weak CD(0,  $\infty$ ) space. Then the support  $[a, b]$  of  $\nu$  consists in finite-dimensional points, which fill up the open interval  $(a, b)$ ; and the two infinite-dimensional points a and b.

**Example 30.41.** The space  $\mathcal{X}$  in Example 29.17 is "genuinely infinitedimensional" in the sense that none of its points is finite-dimensional (such a point would have a neighborhood of finite Hausdorff dimension by Corollary 30.13).

Theorem 30.42 (From local to global  $CD(K,\infty)$ ). Let  $K \in \mathbb{R}$  and let  $(\mathcal{X}, d, \nu)$  be a local weak  $CD(K, \infty)$  space with  $Spt \nu = \mathcal{X}$ . Assume that  $X$  is nonbranching and that there is a totally convex measurable subset  $Y$  of  $X$  such that all points in  $Y$  are finite-dimensional and  $\nu[\mathcal{X} \setminus \mathcal{Y}] = 0$ . Then  $(\mathcal{X}, d, \nu)$  is a weak  $CD(K, \infty)$  space.

**Remark 30.43.** I don't know if the assumption of the existence of  $\mathcal{Y}$ can be removed from the above theorem.

*Proof of Theorem 30.42.* Let  $\Gamma$  be the set of all geodesics in  $\mathcal{X}$ . Let  $K$ be a compact subset of  $\mathcal{Y}$ , and let  $\mu_0, \mu_1$  be two probability measures supported in K. Let  $(\mu_t)_{0 \leq t \leq 1}$  be a displacement interpolation. The set  $\Gamma_K$  of geodesics  $(\gamma_t)_{0 \leq t \leq 1}$  such that  $\gamma_0, \gamma_1 \in K$  is a compact subset of  $\Gamma(\mathcal{X})$  (the set of all geodesics in  $\mathcal{X}$ ). So

$$
\mathcal{X}_K:=\Big\{\gamma_t; \ 0\leq t\leq 1; \ \gamma_0\in K, \ \gamma_1\in K\Big\}
$$

is a compact set too, as the image of  $\Gamma_K \times [0, 1]$  by the continuous map  $(\gamma, t) \rightarrow \gamma_t.$ 

For each  $x \in \mathcal{X}_K$ , we may find a small ball  $B_r(x)$  such that the displacement convexity inequality defining  $CD(K, \infty)$  is satisfied for all displacement interpolations supported in  $B_r(x)$ ; but also the displacement convexity inequality defining  $CD(K', N')$ , for some  $K' \in \mathbb{R}$ ,  $N' < \infty$ . (Both K' and N' will depend on x.) In particular, if  $(\mu_t')_{t_1 \leq t \leq t_2}$ is a displacement interpolation supported in  $B_r(x)$ , with  $\mu'_{t_1}$  absolutely continuous, then also  $\mu'_t$  is absolutely continuous for all  $t \in (t_1, t_2)$  (the proof is the same as for Theorem 30.19(ii)). By reasoning as in the proof of Theorem 30.37, one deduces that  $\mu_t$  is absolutely continuous for all  $t \in [0, 1]$  if  $\mu_0$  and  $\mu_1$  are absolutely continuous.

Of course this is not yet sufficient to imply the finiteness of  $H_{\nu}(\mu_t)$ , but now we shall be able to reduce to this case by approximation. More precisely, we shall construct a sequence  $(\Pi^{(k)})_{k \in \mathbb{N}}$  of dynamical optimal transference plans, such that

$$
\Pi^{(k)} = \frac{\widehat{\Pi}^{(k)}}{Z^{(k)}}, \qquad 0 \le \widehat{\Pi}^{(k)} \le \Pi; \qquad Z^{(k)} \uparrow 1; \qquad Z^{(k)} \, \Pi^{(k)} \uparrow \Pi,
$$
\n(30.68)

Locality 913

and

$$
\forall k \in \mathbb{N}, \quad \forall j \in \mathbb{N} \ (j \le 1/\delta), \qquad \sup \rho_{j\delta}^{(k)} < +\infty,
$$
 (30.69)

where the supremum really is an essential supremum, and  $\rho_t^{(k)}$  $t^{(\kappa)}$  is the density of  $\mu_t^{(k)} = (e_t)_\# \Pi^{(k)}$  with respect to  $\nu$ .

If we can do this, then by repeating the proof of Theorem 30.37 we shall obtain

$$
H_{\nu}(\mu_t^{(k)}) \le (1-t) H_{\pi_k, \nu}^{\beta_{1-t}^{(K,\infty)}}(\mu_0^{(k)}) + t H_{\check{\pi}_k, \nu}^{\beta_t^{(K,\infty)}}(\mu_1^{(k)}).
$$

Then by monotonicity we may pass to the limit as  $k \to \infty$  (as in the proof of Theorem 17.37, say) and deduce

$$
H_{\nu}(\mu_t) \le (1-t) H_{\pi,\nu}^{\beta_{1-t}^{(K,\infty)}}(\mu_0) + t H_{\tilde{\pi},\nu}^{\beta_t^{(K,\infty)}}(\mu_1). \tag{30.70}
$$

Here  $\mu_0$  and  $\mu_1$  are assumed to be supported in a compact subset K of  $\mathcal Y$ . But then, by regularity of  $\nu$ , we may introduce an increasing sequence of compact sets  $(K_m)_{m\in\mathbb{N}}$  such that  $\cup K_m = \mathcal{Y}$ , up to a *ν*-negligible set. Observe that  $\overline{y} = \mathcal{X}$  (otherwise Spt *v* would be included in  $\overline{y}$  and strictly smaller than X; that  $\mathcal{X} \setminus \mathcal{Y}$  has zero measure; that any  $\mu$  such that  $H_{\nu}(\mu) < +\infty$  satisfies  $\mu_s = 0$ , so  $H_{\nu}(\mu) =$  $\int_{\mathcal{X}} \rho \log \rho d\nu = \int_{\mathcal{Y}} \rho \log \rho d\nu$ . This makes it possible to run again a classical scheme to approximate any  $\mu \in P_c(\mathcal{X})$  with  $H_{\nu}(\mu) < +\infty$  by a sequence  $(\mu_m)_{m \in \mathbb{N}}$ , such that  $\mu_m$  is supported in  $K_m$ ,  $\mu_m$  converges weakly to  $\mu$  and  $H_{\pi_m,\nu}^{\beta_{1-t}^{(K,\infty)}}$  converges to  $H_{\pi,\nu}^{\beta_{1-t}^{(K,\infty)}}$  if  $\pi_m \to \pi$ . (Choose for instance  $\mu_m = \chi_m \mu / (\int \chi_m d\mu)$ , where  $\chi_m$  is a cutoff function satisfying  $0 \leq \chi_m \leq 1$ ,  $\chi_m = 0$  outside  $K_{m+1}$ ,  $\chi_m = 1$  on  $K_m$ , and argue as in the proof of Theorem 30.5.) A limit argument will then establish (30.70) for any two compactly supported probability measures  $\mu_0$ ,  $\mu_1$ .

So it all boils down to providing an approximation sequence  $\Pi^{(k)}$ satisfying  $(30.68)$ ,  $(30.69)$ . This is done in m (simple) steps as follows.

First approximate  $\rho_{\delta}$  by a nondecreasing sequence of bounded densities:  $0 \leq h_{\delta}^{k_1} \leq \rho_{\delta}, k_1 \in \mathbb{N}$ , where each  $h_{\delta}^{k_1}$  is bounded and  $h_{\delta}^{k_1} \uparrow \rho_{\delta}$  as  $k_1 \rightarrow \infty$ . Define

$$
\widehat{\Pi}^{k_1}(d\gamma) = (h_\delta^{k_1} \nu)(d\gamma_\delta) \, \Pi(d\gamma|\gamma_\delta),
$$

where  $\Pi(d\gamma|\gamma_t)$  stands for the conditional probability of  $\gamma$ , distributed according to  $\Pi$  and conditioned by its value at time t. Then let

$$
Z^{k_1} = \widehat{\Pi}^{k_1}[\Gamma]; \qquad \Pi^{k_1} = \frac{\widehat{\Pi}^{k_1}}{Z^{k_1}}
$$

.

As  $k_1$  goes to infinity, it is clear that  $Z^{k_1} \uparrow 1$  (in particular, we may assume without loss of generality that  $Z^{k_1} > 0$  and  $Z^{k_1} \prod_{k=1}^k \uparrow H$ . Moreover, if  $\rho_t^{k_1}$  stands for the density of  $(e_t)_\# \Pi^{k_1}$ , then  $\rho_\delta^{k_1} = (Z^{k_1})^{-1} h_\delta^{k_1}$ is bounded.

Now comes the second step: For each  $k_1$ , let  $(h_{2\delta}^{k_1,k_2})$  $\binom{k_1,k_2}{2\delta}$ <sub>k<sub>2</sub>∈N be a non-</sub> decreasing sequence of bounded functions converging almost surely to  $\rho_{2\delta}^{k_1}$  as  $k_2 \to \infty$ . Let

$$
\widehat{\Pi}^{k_1,k_2}(d\gamma) = (h_{2\delta}^{k_1,k_2} \nu)(d\gamma_{2\delta}) \Pi^{k_1}(d\gamma|\gamma_{2\delta}),
$$
  
$$
Z^{k_1,k_2} = \widehat{\Pi}^{k_1,k_2}[\Gamma], \qquad \Pi^{k_1,k_2} = \frac{\widehat{\Pi}^{k_1,k_2}}{Z^{k_1,k_2}},
$$

and let  $\rho_t^{k_1,k_2}$  stand for the density of  $(e_t)_\# \Pi^{k_1,k_2}$ . Then  $\rho_\delta^{k_1,k_2} \leq$  $(Z^{k_1,k_2})^{-1}\rho_{\delta}^{k_1} = (Z^{k_1,k_2}Z^{k_1})^{-1}h_{\delta}^{k_1}$  and  $\rho_{2\delta}^{k_1,k_2} = (Z^{k_1,k_2})^{-1}h_{2\delta}^{k_1,k_2}$  $\frac{\kappa_1,\kappa_2}{2\delta}$  are both bounded.

Then repeat the process: If  $\Pi^{k_1,\ldots,k_j}$  has been constructed for any  $k_1, \ldots, k_j$  in N, introduce a nonincreasing sequence  $(h_{(i+1)\delta}^{k_1,\ldots,k_{j+1}})$  $\binom{\kappa_1,\ldots,\kappa_{j+1}}{(j+1)\delta} k_{j+1} \in \mathbb{N}$ converging almost surely to  $\rho_{(i+1)\delta}^{k_1,...,k_j}$  $\chi_{(j+1)\delta}^{\kappa_1,\ldots,\kappa_j}$  as  $k_{j+1} \to \infty$ ; define

$$
\widehat{\Pi}^{k_1,\ldots,k_{j+1}}(d\gamma) = (h_{(j+1)\delta}^{k_1,\ldots,k_{j+1}} \nu)(d\gamma_{(j+1)\delta}) \Pi^{k_1,\ldots,k_{j+1}}(d\gamma|\gamma_{(j+1)\delta}),
$$

$$
Z^{k_1,\ldots,k_{j+1}} = \widehat{\Pi}^{k_1,\ldots,k_{j+1}}[\Gamma],
$$

$$
\Pi^{k_1,\ldots,k_{j+1}} = \widehat{\Pi}^{k_1,\ldots,k_{j+1}}/Z^{k_1,\ldots,k_{j+1}},
$$

$$
\mu_t^{k_1,\ldots,k_{j+1}} = (e_t)_\# \Pi^{k_1,\ldots,k_{j+1}}.
$$

Then for any  $t \in \{\delta, 2\delta, \ldots, (j+1)\delta\}$ , the density  $\rho_t^{k_1, \ldots, k_{j+1}}$  $_{t}^{k_{1},...,k_{j+1}}$  of  $\mu_{t}^{k_{1},...,k_{j+1}}$ t is bounded.

After m operations this process has constructed  $\Pi^{k_1,\ldots,k_m}$  such that all densities  $\rho_{j\delta}^{k_1,...,k_m}$  are bounded. The proof is completed by choosing  $\Pi^{(k)} = \Pi^{k,...,k}, Z^{(k)} = Z^k \cdot Z^{k,k} \cdot \ldots \cdot Z^{k,...,k}$ .