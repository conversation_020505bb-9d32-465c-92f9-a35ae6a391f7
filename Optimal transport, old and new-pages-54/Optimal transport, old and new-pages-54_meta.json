{"table_of_contents": [{"title": "Talagrand inequalities", "heading_level": null, "page_id": 0, "polygon": [[133.5, 141.0], [265.658203125, 141.0], [265.658203125, 152.75390625], [133.5, 152.75390625]]}, {"title": "Equivalence of definitions in nonbranching spaces", "heading_level": null, "page_id": 2, "polygon": [[133.5, 48.0], [426.75, 48.0], [426.75, 58.8779296875], [133.5, 58.8779296875]]}, {"title": "898 30 Weak Ricci curvature bounds II: Geometric and analytic properties", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.25], [453.75, 26.25], [453.75, 35.62646484375], [133.5, 35.62646484375]]}, {"title": "904 30 Weak Ricci curvature bounds II: Geometric and analytic properties", "heading_level": null, "page_id": 11, "polygon": [[133.5, 26.25], [453.0234375, 26.25], [453.0234375, 35.505615234375], [133.5, 35.505615234375]]}, {"title": "Locality", "heading_level": null, "page_id": 13, "polygon": [[133.27734375, 47.953125], [182.583984375, 47.953125], [182.583984375, 59.0712890625], [133.27734375, 59.0712890625]]}, {"title": "910 30 Weak Ricci curvature bounds II: Geometric and analytic properties", "heading_level": null, "page_id": 17, "polygon": [[133.5, 26.25], [453.322265625, 26.25], [453.322265625, 35.72314453125], [133.5, 35.72314453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 32], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 48], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1046, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 44], ["TextInlineMath", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 59], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 680], ["Line", 43], ["TextInlineMath", 6], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 741], ["Line", 123], ["Equation", 6], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2292, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 639], ["Line", 75], ["Equation", 5], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1151, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 656], ["Line", 140], ["Equation", 5], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 607], ["Line", 56], ["TextInlineMath", 6], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1165, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 750], ["Line", 122], ["Equation", 8], ["Text", 5], ["TextInlineMath", 5]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 598], ["Line", 77], ["Equation", 6], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 776], ["Line", 63], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 622], ["Line", 65], ["Equation", 7], ["TextInlineMath", 7], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1077, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 61], ["TextInlineMath", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1096, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["Line", 61], ["TextInlineMath", 6], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 53], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 769], ["Line", 66], ["TextInlineMath", 7], ["Equation", 4], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 672], ["Line", 74], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1401, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 58], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 585], ["Line", 48], ["TextInlineMath", 5], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 770], ["Line", 64], ["Text", 5], ["Equation", 4], ["TextInlineMath", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 715], ["Line", 59], ["TextInlineMath", 6], ["Equation", 3], ["Text", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2372, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-54"}