Image /page/0/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below. The icon is a circle with a curved line segment on the left and a bookmark shape on the right. The text below the icon reads "Check for updates".

# Feature Extraction for Generative Medical Imaging Evaluation: New Evidence Against an Evolving Trend

M<PERSON>Kell Woodland<sup>1[,](http://orcid.org/0009-0000-9298-6644)2( $\boxtimes$ )</sup> $\textcircled{b}$ , Austin Castelo<sup>1</sup> $\textcircled{b}$ , <PERSON><PERSON><sup>1</sup> $\textcircled{b}$ , <PERSON><sup>1</sup> $\bullet$ [,](http://orcid.org/0009-0001-9264-9725) <PERSON><sup>1</sup> $\bullet$ , <PERSON><sup>1</sup> $\bullet$ . <PERSON><sup>1</sup> $\odot$ [,](http://orcid.org/0000-0003-3752-5997) <PERSON><PERSON><PERSON><PERSON><sup>1</sup> $\odot$ , <PERSON><sup>1</sup> $\odot$ , <PERSON><PERSON><PERSON><PERSON><sup>2[,](http://orcid.org/0000-0001-9678-496X)3</sup> $\bullet$ , and Kristy K. Brock<sup>[1](http://orcid.org/0000-0001-9364-5040)</sup> $\bullet$ 

<sup>1</sup> The University of Texas MD Anderson Cancer Center, Houston, TX 77030, USA {mewoodland,ahcastelo,mmal1,jalbuquerque,meltaher,adshieh, skundu2, jyung, kkbrock}@mdanderson.org <sup>2</sup> Rice University, Houston, TX 77005, USA

<EMAIL>

<sup>3</sup> Baylor College of Medicine, Houston, TX 77030, USA

Abstract. Fréchet Inception Distance (FID) is a widely used metric for assessing synthetic image quality. It relies on an ImageNet-based feature extractor, making its applicability to medical imaging unclear. A recent trend is to adapt FID to medical imaging through feature extractors trained on medical images. Our study challenges this practice by demonstrating that ImageNet-based extractors are more consistent and aligned with human judgment than their RadImageNet counterparts. We evaluated sixteen StyleGAN2 networks across four medical imaging modalities and four data augmentation techniques with Fréchet distances (FDs) computed using eleven ImageNet or RadImageNet-trained feature extractors. Comparison with human judgment via visual Turing tests revealed that ImageNet-based extractors produced rankings consistent with human judgment, with the FD derived from the ImageNet-trained SwAV extractor significantly correlating with expert evaluations. In contrast, RadImageNet-based rankings were volatile and inconsistent with human judgment. Our findings challenge prevailing assumptions, providing novel evidence that medical image-trained feature extractors do not inherently improve FDs and can even compromise their reliability. Our code is available at [https://github.com/mckellwoodland/fid-med-eval.](https://github.com/mckellwoodland/fid-med-eval)

Keywords: Generative Medical Imaging · Fréchet Inception Distance

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_9.](https://doi.org/10.1007/978-3-031-72390-2_9)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 87–97, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_9)\_9

# 1 Introduction

Fréchet Inception Distance (FID) is a commonly used metric for evaluating synthetic image quality [\[1](#page-9-0)]. It quantifies the Fréchet distance (FD) between two Gaussian distribution curves fitted to embeddings of real and generated images. These embeddings are typically extracted from the penultimate layer of an InceptionV3 network trained on ImageNet. FID's utility has been demonstrated through its correlation with human judgment [\[2\]](#page-9-1), sensitivity to distortions [\[1\]](#page-9-0), capability to detect overfitting [\[3\]](#page-9-2), and relative sample efficiency [\[3](#page-9-2)]. Nonetheless, the metric has faced criticism, including that the InceptionV3 network may only embed information relevant to ImageNet class discrimination [\[4,](#page-9-3)[5](#page-9-4)].

Three approaches exist for adapting FID to medical imaging. The first involves using an InceptionV3 extractor trained on a large, publicly available medical dataset, such as RadImageNet, a database containing 1.35 million annotated computed tomography (CT), magnetic resonance imaging (MRI), and ultrasonography exams [\[6](#page-9-5)[,7](#page-9-6)]. While a RadImageNet-based FD considers medically relevant features, its efficacy remains largely unexplored. One potential bias is that networks trained for disease detection may focus too heavily on small, localized regions [\[8\]](#page-9-7) to evaluate an entire image's quality effectively. Additionally, RadImageNet-based FDs may not generalize to new medical modalities [\[7](#page-9-6)] or patient populations. Our novel comparison of RadImageNet-based FDs to human judgment revealed discrepancies, even on in-domain abdominal CT data.

The second approach utilizes self-supervised networks for feature extraction [\[9](#page-9-8)]. These networks are encouraging as they create transferable and robust representations [\[10\]](#page-9-9), including on medical images [\[4\]](#page-9-3). Despite their promise, the lack of publicly available, self-supervised models trained on extensive medical imaging datasets has hindered their application. Our study is the first to employ self-supervised extractors for synthetic medical image evaluation. We find a significant correlation between an FD derived from an ImageNet-trained SwAV network (FSD) and medical experts' appraisal of image realism, highlighting the potential of self-supervision for advancing generative medical imaging evaluation.

The third approach employs a feature extractor trained on the dataset used to train the generative imaging model  $[11-13]$  $[11-13]$ . While advantageous for domain coherence, the algorithm designer creates the metric used to evaluate their algorithm, potentially resulting in unquantified bias. Moreover, the private and varied nature of these feature extractors poses challenges for reproducibility and benchmarking. Given these limitations, our study focuses on publicly available feature extractors.

Our study offers a novel comparison of generative model rankings created by ImageNet- and RadImageNet-trained feature extractors with expert judgment. Our main contributions are:

1. Demonstrating that ImageNet-based feature extractors consistently produce more realistic model rankings than their RadImageNet-based counterparts. This finding raises concerns about the prevalent practice of using medical image-trained feature extractors for generative model ranking without evaluating the efficacy of the proposed metric.

- 2. Identifying a significant correlation between an FD calculated with an ImageNet-trained SwAV network and expert assessments of image realism, demonstrating that FSD is a viable alternative to FID on medical images.
- 3. Benchmarking multiple data augmentation techniques designed to enhance generative performance within limited data domains on medical imaging datasets.
- 4. Introducing a novel method for evaluating visual Turing Tests (VTTs) via hypothesis testing, providing an unbiased measure of participant perception of synthetic image realism.

# 2 Methods

## 2.1 Generative Modeling

Four medical imaging datasets were used for generative modeling: the Segmentation of the Liver Competition 2007 (SLIVER07) dataset with 20 liver CT studies  $[14]^1$  $[14]^1$  $[14]^1$ , the ChestX-ray14 dataset with 11[2](#page-2-1),100 chest X-rays  $[15]^2$  $[15]^2$ , the brain tumor dataset from the Medical Segmentation Decathlon (MSD) with 750 brain MRI studies  $[16, 17]^3$  $[16, 17]^3$  $[16, 17]^3$  $[16, 17]^3$  $[16, 17]^3$ , and the Automated Cardiac Diagnosis Challenge (ACDC) dataset with 150 cardiac cine-MRIs  $[18]^4$  $[18]^4$  $[18]^4$ . Multi-dimensional images were converted to two dimensions by extracting axial slices and excluding the slices with less than 15% nonzero pixels.

Four StyleGAN2 [\[19](#page-10-3)] models were trained per dataset, using either adaptive discriminator augmentation (ADA) [\[20](#page-10-4)], differentiable augmentation (DiffAugment) [\[21\]](#page-10-5), adaptive pseudo augmentation (APA) [\[22](#page-10-6)], or no augmentation, to enable a comparison of synthetic quality. StyleGAN2 was chosen for its ability to produce high-fidelity medical images [\[2\]](#page-9-1) and its readily available data augmentation implementations. While all of the data augmentation techniques were created to improve the performance of generative models on limited data domains, such as medical imaging, we are the first to benchmark the techniques on medical images. Each model was evaluated using the weights obtained at the end of 25,000 kimg (a kimg represents a thousand real images being shown to the discriminator), except for the MSD experiments, which were limited to 5,000 kimg due to training instability. Our code and trained model weights are available at [https://github.com/mckellwoodland/fid-med-eval.](https://github.com/mckellwoodland/fid-med-eval)

## 2.2 Human Evaluation

Human perception of model quality was assessed with one VTT per model. Each test comprised 20 randomly selected images with an equal number of real and generated images. Participants were asked to identify whether each image was

<span id="page-2-0"></span><sup>1</sup> [https://sliver07.grand-challenge.org/.](https://sliver07.grand-challenge.org/)

<span id="page-2-1"></span> $^2$ [https://nihcc.app.box.com/v/ChestXray-NIHCC.](https://nihcc.app.box.com/v/ChestXray-NIHCC)

<span id="page-2-2"></span><sup>3</sup> [http://medicaldecathlon.com/,](http://medicaldecathlon.com/) CC-BY-SA 4.0 license.

<span id="page-2-3"></span><sup>4</sup> [https://www.creatis.insa-lyon.fr/Challenge/acdc/databases.html.](https://www.creatis.insa-lyon.fr/Challenge/acdc/databases.html)

real or generated and rate its realism on a Likert scale from 1 to 3 (1: "Not at all realistic," 2: "Somewhat realistic," and 3: "Very realistic"). The tests were administered to five specialists with medical degrees. In addition to the VTTs, three radiologists were shown 35 synthetic radiographs per ChestX-ray14 model and were asked to rank and provide a qualitative assessment of the models.

False positive rate (FPR) and false negative rate (FNR) were used to evaluate the VTTs. The FPRs represent the proportion of generated images that participants considered to be real. FPRs near 50% indicate random guessing. One-sided paired t tests were performed on the FPRs with  $\alpha = .05$  to benchmark the data augmentation techniques. For each VTT, the average Likert ratings of real and generated images were computed per participant. The difference between these average ratings (Diff) was then computed to compare the perceived realism of real and generated images. Two-sample Kolmogorov-Smirnov (KS) tests were conducted on the Likert ratings of the real and generated images with significance level  $\alpha = .10$  to determine whether the ratings came from the same distribution, indicating that the participants viewed the realism of the generated images to be equivalent to that of the real images. We are the first to use the difference in average Likert ratings and the KS test for generative modeling evaluation.

When taking a VTT, participants may be more likely to select either "real" or "generated" when uncertain. This bias causes the average FPR to not fully encapsulate whether participants can differentiate between real and generated images. We propose a novel method for evaluating VTTs via hypothesis testing to address this challenge. The method aims to demonstrate that the likelihood of a participant selecting "real" is the same for both real and generated images. We define the null hypothesis  $\mathbb{P}(p \text{ guesses real} | G) = \mathbb{P}(p \text{ guesses real} | R)$ where G represents the event that the image is generated and R represents the event that the image is real for each participant  $p$ . We evaluate this hypothesis using a two-sample t test with significance level  $\alpha = .10$ , where the first sample is the participant's binary predictions for generated images, and the second is their predictions for real images. We define the null hypothesis P(random  $p \in P$  guesses real  $| G \rangle = P$ (random  $p \in P$  guesses real  $| R \rangle$  to evaluate VTTs for multiple participants P. We evaluate this hypothesis via a two-sample t test with significance level  $\alpha = .10$ , where the first sample is the FPR and the second is the true positive rate of each participant.

## 2.3 Frret Distances

Quantitative evaluation of synthetic image quality was performed by calculating the FD  $d(\Sigma_1, \Sigma_2, \mu_1, \mu_2)^2 = |\mu_1 - \mu_2|^2 + \text{tr}(\Sigma_1 + \Sigma_2 - 2(\Sigma_1 \Sigma_2)^{\frac{1}{2}})$  [\[23\]](#page-10-7) between two multivariate Gaussians  $(\Sigma_R, \mu_R)$  and  $(\Sigma_G, \mu_G)$  fitted to real and generated features extracted from the penultimate layer of eleven backbone networks: InceptionV3 [\[24](#page-10-8)], ResNet50 [\[25](#page-10-9)], InceptionResNetV2 [\[26](#page-10-10)], and DenseNet121 [\[27](#page-10-11)] each trained separately on both ImageNet [\[28](#page-10-12)] and RadImageNet [\[6](#page-9-5)], along with SwAV [\[29](#page-10-13)], DINO [\[30\]](#page-10-14), and a Swin Transformer [\[31\]](#page-10-15) trained on ImageNet. The first four networks were included to compare all publicly available RadImageNet

models to their ImageNet equivalents. SwAV and DINO were included to evaluate the impact of self-supervision, as self-supervised representations have demonstrated superior transferability to new domains [\[10\]](#page-9-9) and richer embeddings on medical images [\[4\]](#page-9-3). Finally, a Swin Transformer [\[31\]](#page-10-15) was included as transformers have been shown to create transferable and robust representations [\[32\]](#page-10-16). We are the first to use self-supervised and transformer architectures with FD for generative medical imaging evaluation. FDs were calculated between the entire real dataset and 50,000 generated images. ImageNet-based FDs were calculated with the StudioGAN repository [\[33](#page-10-17)]. Further implementation details are available on our GitHub.

As the scale of FDs varies substantially by feature extractor, relative FDs  $(rFDs)$   $\frac{d(\Sigma_R, \Sigma_G, \mu_R, \mu_G)^2}{d(\Sigma_{R_1}, \Sigma_{R_2}, \mu_{R_1}, \mu_{R_2})^2}$  were computed with a random split of the real features into two Gaussian distributions  $(\Sigma_{R_1}, \mu_{R_1})$  and  $(\Sigma_{R_2}, \mu_{R_2})$ . Paired t tests with significance level  $\alpha = 0.05$  were conducted on the FDs to benchmark the data augmentation techniques. The Pearson correlation coefficient  $(\rho)$  with significance level  $\alpha = 0.05$  was used to quantify the correspondence between FDs and VTT metrics and the correspondence between individual FDs. We are the first to consider whether medical image-based FDs are correlated with human judgment.

# 3 Results

Table [1](#page-5-0) summarizes the overall results of the VTTs, with detailed individual participant outcomes available on our GitHub. The rFDs based on ImageNet and RadImageNet are outlined in Tables [2](#page-6-0) and [3,](#page-7-0) while the FDs can be found in Tables S1 and S2 in the supplementary material. Model rankings based on individual metrics are illustrated in Fig. [1.](#page-8-0) Our analysis revealed consistent rankings among all ImageNet-based FDs, aligning closely with human judgment. In contrast, RadImageNet-based FDs exhibited volatility and diverged from human assessment. DiffAugment was the best-performing form of augmentation, generating hyper-realistic images on two datasets.

ImageNet Extractors Aligned with Human Judgment. ImageNet-based FDs were consistent with one another in ranking generative models, except for on the MSD dataset, where human rankings were also inconsistent (Fig. [1\)](#page-8-0). This consistency was reinforced by strong correlations between the FD derived from InceptionV3 and all other ImageNet-based FDs across all sixteen models (.84 <  $\rho$  < .99,  $p$  < .001). Furthermore, the ImageNet-based FD rankings aligned with expert judgment (Fig. [1\)](#page-8-0). On the ChestX-ray14 dataset, ImageNetbased FDs ranked generative models in the same order as the radiologists: DiffAugment, ADA, no augmentation, and APA. Particularly promising was the SwAV-based FD, which significantly correlated with human perception across all sixteen models ( $\rho = .475$  with Diff,  $p = .064$ ).

<span id="page-5-0"></span>Table 1. VTT results. Columns 1 and 2 split the models by dataset and augmentation technique (Aug): no augmentation (None), ADA, APA, and DiffAugment (DiffAug). Columns 3 and 4 show average FPRs and FNRs, with FPRs near 50% implying random guessing. Column 5 provides *t* test *p*-values, which tested if participants selected "real" for real and generated images equally. Column 6 displays the average difference between mean Likert ratings for real and generated images (Diff), with negative values indicating that generated images were perceived as more realistic than real images. Column 7 presents KS test *p*-values, which tested if Likert ratings for real and generated images came from the same distribution.  $\uparrow$  and  $\downarrow$  denote preferable higher or lower values. Underlined boldface type represents the best performance per dataset. Gray boxes indicate failure to reject the null hypothesis, suggesting that participants viewed real and generated images as equivalent. †indicates decreased performance compared to no augmentation.

| Dataset      | Aug     | FPR [%] ↑ | FNR [%] ↑ | t Test   | Diff ↓       | KS Test  |
|--------------|---------|-----------|-----------|----------|--------------|----------|
| ChestXray-14 | None    | <b>48</b> | <b>58</b> | p = .497 | 0.12         | p = .869 |
|              | ADA     | 32        | 47        | p = .340 | 0.28         | p = .549 |
|              | APA     | 34        | 56        | p = .082 | 0.24         | p = .717 |
|              | DiffAug | <b>48</b> | <b>58</b> | p = .616 | <b>-0.16</b> | p = .967 |
| SLIVER07     | None    | 20        | <b>34</b> | p = .424 | 0.68         | p < .001 |
|              | ADA     | 24        | 30        | p = .748 | 0.52         | p = .001 |
|              | APA     | 10        | 28        | p = .232 | 0.82         | p < .001 |
|              | DiffAug | <b>34</b> | 30        | p = .825 | <b>0.22</b>  | p = .717 |
| MSD          | None    | 58        | 48        | p = .543 | -0.08        | p > .999 |
|              | ADA     | <b>66</b> | 48        | p = .217 | -0.04        | p > .999 |
|              | APA     | 46        | 38        | p = .587 | 0.04         | p > .999 |
|              | DiffAug | 50        | <b>54</b> | p = .812 | <b>-0.08</b> | p > .999 |
| ACDC         | None    | 34        | 22        | p = .470 | 0.52         | p = .022 |
|              | ADA     | 38        | <b>30</b> | p = .653 | 0.38         | p = .112 |
|              | APA     | 28        | 22        | p = .707 | 0.46         | p = .003 |
|              | DiffAug | <b>44</b> | 16        | p = .015 | 0.28         | p = .112 |

RadImageNet Extractors were Volatile. RadImageNet-based FDs produced inconsistent rankings that were contrary to expert judgment. Notably, on the SLIVER07 dataset, RadImageNet-based FDs ranked DiffAugment as one of the poorest-performing models. However, all measures of human judgment identified DiffAugment as the best-performing model (see Fig. [1\)](#page-8-0). This discrepancy is especially concerning considering RadImageNet's inclusion of approximately 300,000 CT scans. On the ChestX-ray14 dataset, the FD derived from a RadImageNet-trained InceptionV3 network ranked the model without augmentation as the best performing. In contrast, a thoracic radiologist observed that both the APA and no augmentation models generated multiple radiographs with obviously distorted anatomy. Conversely, the weaknesses of the DiffAug-

<span id="page-6-0"></span>Table 2. ImageNet-based rFDs. Columns 1 and 2 split the models by dataset and augmentation technique (Aug): no augmentation (None), ADA, APA, and DiffAugment (DiffAug). Columns 3–9 show rFDs computed using seven ImageNet-based extractors: InceptionV3 (Incept), ResNet50 (Res), InceptionResNetV2 (IRV2), DenseNet121 (Dense), SwAV, DINO, and Swin Transformer (Swin). ↓ denotes lower values are preferable. Underlined boldface type indicates the best performance per dataset. †denotes decreased performance compared to no augmentation.

| Dataset      | Aug     | Relative Fréchet Distances (ImageNet) $\downarrow$ |                 |                |               |              |              |              |
|--------------|---------|----------------------------------------------------|-----------------|----------------|---------------|--------------|--------------|--------------|
|              |         | Incept                                             | Res             | IRV2           | Dense         | SwAV         | DINO         | Swin         |
| ChestXray-14 | None    | 12.53                                              | 279.00          | 701.00         | 20.80         | 53.50        | 60.43        | 34.00        |
|              | ADA     | 8.90                                               | 237.00          | 576.00         | 15.55         | 33.00        | 37.81        | 26.36        |
|              | APA     | 17.58†                                             | 334.00†         | 1004.50†       | 39.85†        | 66.00†       | 82.23†       | 54.21†       |
|              | DiffAug | <b>7.68</b>                                        | <b>146.00</b>   | <b>441.00</b>  | <b>13.25</b>  | <b>25.00</b> | <b>34.51</b> | <b>22.79</b> |
| SLIVER07     | None    | 1.48                                               | 7.90            | 12.98          | 2.59          | 8.28         | 6.12         | 6.07         |
|              | ADA     | 1.24                                               | 7.35            | 11.71          | 1.95          | 6.86         | 4.57         | 6.22†        |
|              | APA     | 1.37                                               | 7.33            | 11.96          | 2.36          | 7.79         | 5.59         | 5.43         |
|              | DiffAug | <b>0.78</b>                                        | <b>3.25</b>     | <b>5.99</b>    | <b>1.24</b>   | <b>5.26</b>  | <b>3.07</b>  | <b>4.77</b>  |
| MSD          | None    | 37.32                                              | 63.13           | 61.18          | 170.38        | 142.50       | 108.39       | 504.47       |
|              | ADA     | <b>36.84</b>                                       | <b>62.50</b>    | <b>58.88</b>   | <b>141.63</b> | 305.00†      | 121.90†      | 308.59       |
|              | APA     | 43.63†                                             | 70.00†          | 81.76†         | 145.13        | 122.50       | 126.47†      | 196.65       |
|              | DiffAug | <b>46.32</b> †                                     | <b>125.50</b> † | <b>79.88</b> † | 170.38        | 825.00†      | 138.11†      | 175.12       |
| ACDC         | None    | 49.67                                              | 86.48           | 121.14         | 87.46         | 118.00       | 140.15       | 111.07       |
|              | ADA     | 20.99                                              | 31.66           | 49.94          | 35.95         | 76.40        | 65.52        | 61.49        |
|              | APA     | 31.15                                              | 54.35           | 76.47          | 56.68         | 90.60        | 87.69        | 72.10        |
|              | DiffAug | <b>15.87</b>                                       | <b>23.58</b>    | <b>40.60</b>   | <b>27.20</b>  | <b>71.00</b> | <b>50.47</b> | <b>47.23</b> |

ment and ADA models were more subtle, with mistakes in support devices and central lines.

APA and ADA Demonstrated Varied Performance. Although APA was designed to enhance image quality in limited data domains such as medical imaging, it unexpectedly reduced the perceptual quality of the generated images (t test on FPRs,  $p = .012$ ), leading to an 18% reduction in the FPR on average. While ADA outperformed APA (t test on FDs,  $p = .050$ ), it did not significantly affect participants' ability to differentiate real from generated images (t test on FPRs,  $p > .999$ ). Despite both techniques underperforming in the VTTs, they improved the rFDs for the SLIVER07 (t tests,  $p = .025$  ADA,  $p = .016$  APA) and ACDC (t tests,  $p = .003$  ADA,  $p = .004$  APA) datasets.

DiffAugment Created Hyper-Realistic Images. DiffAugment outperformed the other augmentation techniques across all FDs ( $t$  tests,  $p = .092$  ADA,

<span id="page-7-0"></span>Table 3. RadImageNet-based rFDs. Columns 1 and 2 split the models by dataset and augmentation technique (Aug): no augmentation (None), ADA, APA, and DiffAugment (DiffAug). Columns 3–6 display rFDs computed using four RadImageNetbased extractors: InceptionV3 (Incept), ResNet50 (Res), InceptionResNetV2 (IRV2), and DenseNet121 (Dense). ↓ denotes lower values are preferable. Underlined boldface type indicates the best performance per dataset. †denotes decreased performance compared to no augmentation.

| Dataset      | Aug     | Relative Fréchet Distances (RadImageNet) ↓ |              |              |              |
|--------------|---------|--------------------------------------------|--------------|--------------|--------------|
|              |         | Incept                                     | Res          | IRV2         | Dense        |
| ChestXray-14 | None    | <b>140.00</b>                              | 75.00        | <b>80.00</b> | 40.00        |
|              | ADA     | 660.00†                                    | 135.00†      | 190.00†      | 80.00†       |
|              | APA     | 280.00†                                    | 65.00        | <b>80.00</b> | 80.00        |
|              | DiffAug | 280.00†                                    | <b>50.00</b> | 90.00†       | <b>30.00</b> |
| SLIVER07     | None    | 3.67                                       | 3.14         | 6.00         | 4.33         |
|              | ADA     | <b>1.89</b>                                | <b>1.86</b>  | 3.75         | <b>2.33</b>  |
|              | APA     | 2.22                                       | <b>1.86</b>  | <b>3.00</b>  | 2.67         |
|              | DiffAug | 4.67†                                      | 3.29†        | 5.50         | 4.67†        |
| MSD          | None    | 53.00                                      | 32.50        | <b>32.50</b> | <b>40.00</b> |
|              | ADA     | <b>36.00</b>                               | <b>27.5</b>  | 37.50†       | 60.00†       |
|              | APA     | 54.00†                                     | 32.50        | 40.00†       | <b>40.00</b> |
|              | DiffAug | 1551.00†                                   | 1105.00†     | 350.00†      | 615.00†      |
| ACDC         | None    | 26.64                                      | 19.00        | 20.33        | 32.50        |
|              | ADA     | <b>10.18</b>                               | <b>9.25</b>  | <b>9.67</b>  | 13.00        |
|              | APA     | 14.09                                      | <b>8.75</b>  | 11.67        | 17.50        |
|              | DiffAug | 12.09                                      | 15.25        | <b>9.67</b>  | <b>10.50</b> |

 $p = .059$  APA). DiffAugment was the only form of augmentation to significantly enhance perceptual quality (t test on Diff,  $p = .001$ ), resulting in an 81% reduction in the average difference between mean Likert ratings. Participants rated images from DiffAugment-based models as more realistic than those from both the ChestX-ray14 and MSD datasets. Additionally, Likert ratings for real and generated images from all DiffAugment-based models did not differ significantly (KS test,  $p = .793$ ), suggesting that participants perceived them as equivalent.

# 4 Discussion

RadImageNet-based FDs may have underperformed due to several factors. First, networks trained for disease detection place a greater emphasis on local regions than their ImageNet counterparts [\[8](#page-9-7)], likely affecting their ability to evaluate the quality of an entire image. Second, medical images are highly heterogeneous, including differences across modalities, acquisition protocols, patient populations, and image processing techniques. RadImageNet does not contain chest

Image /page/8/Figure/1 description: A heatmap displays the performance of different models across various datasets and metrics. The rows are categorized by datasets: ChestX-ray14, SLIVER07, MSD, and ACDC, each with sub-categories numbered 1 through 4. The columns are grouped into three main sections: RadImageNet FDs, ImageNet FDs, and VTT Metrics. Within RadImageNet FDs, there are columns for Incept, Res, IRV2, and Dense. ImageNet FDs includes Incept, Res, IRV2, Dense, SwAV, DINO, and Swin. VTT Metrics has columns for KS, Diff, and FPR. A legend at the bottom indicates that the color intensity represents different augmentation models: No Augmentation (lightest blue), ADA (medium light blue), APA (medium dark blue), and DiffAugment (darkest blue). The heatmap visually compares the effectiveness of these augmentation strategies across different models and datasets.

<span id="page-8-0"></span>Fig. 1. Model rankings. Columns represent evaluation metrics. FDs are split by feature extraction dataset and architecture: InceptionV3 (Incept), ResNet50 (Res), Inception-ResNetV2 (IRV2), DenseNet121 (Dense), SwAV, DINO, and Swin Transformer (Swin). Metrics evaluating human perception via VTTs are KS test *p*-values (KS), average difference in mean Likert scores (Diff), and FPRs. Rows represent models trained with different augmentation techniques on the same dataset. Models are ranked 1–4 in descending order of performance and are differentiated by color. Vertical bars denote a shared rank.

X-rays nor cine MRIs. Furthermore, it was collected from a single radiology facility [\[6](#page-9-5)], making it likely that the protocols, machinery, and patients populations differed from those of the SLIVER07 and MSD datasets.

# 5 Conclusion

Our study challenges prevailing assumptions by providing novel evidence that medical image-trained feature extractors do not inherently improve FDs for synthetic medical imaging evaluation; instead, they may compromise metric consistency and alignment with human judgment, even on in-domain data.

Acknowledgments. Research reported in this publication was supported in part by resources of the Image Guided Cancer Therapy Research Program (IGCT) at The University of Texas MD Anderson Cancer Center, a generous gift from the Apache Corporation, the National Institutes of Health/NCI under award number P30CA016672, and the Tumor Measurement Initiative through the MD Anderson Strategic Initiative Development Program (STRIDE). We thank the NIH Clinical Center for the ChestXray14 dataset, Dr. Rishi Agrawal and Dr. Carol Wu for their generative modeling feedback, Dr. Vikram Haheshri and Dr. Oleg Igoshin for the discussion that led to the hypothesis testing contribution, and Erica Goodoff - Senior Scientific Editor in the Research Medical Library at MD Anderson for editing this article. GPT4 was used in the proofreading stage of this manuscript.

Disclosure of Interests. The authors have no competing interests to declare.

# References

- <span id="page-9-0"></span>1. Heusel, M., Ramsauer, H., Unterthiner, T., Nessler, B., Hochreiter, S.: Gans trained by a two time-scale update rule converge to a local nash equilibrium. In: Guyon, I., et al. (eds.) NIPS. vol. 30. Curran Associates, Inc. (2017)
- <span id="page-9-1"></span>2. Woodland, M., et al.: Evaluating the performance of stylegan2-ada on medical images. In: Zhao, C., et al. (eds.) SASHIMI. pp. 142–153. Springer (2022). [https://](https://doi.org/10.1007/978-3-031-16980-9_14) [doi.org/10.1007/978-3-031-16980-9\\_14](https://doi.org/10.1007/978-3-031-16980-9_14)
- <span id="page-9-2"></span>3. Borji, A.: Pros and cons of gan evaluation measures. Comput. Vis. Image Underst. 179, 41–65 (2019). <https://doi.org/10.1016/j.cviu.2018.10.009>
- <span id="page-9-3"></span>4. Truong, T., Mohammadi, S., Lenga, M.: How transferable are self-supervised features in medical image classification tasks? In: Jung, K., et al. (eds.) MLHC. vol. 158, pp. 54–74. PMLR (2021)
- <span id="page-9-4"></span>5. Kynkäänniemi, T., Karras, T., Aittala, M., Aila, T., Lehtinen, J.: The role of imagenet classes in fréchet inception distance. [arXiv:2203.06026](http://arxiv.org/abs/2203.06026) (2023)
- <span id="page-9-5"></span>6. Mei, X., et al.: Radimagenet: An open radiologic deep learning research dataset for effective transfer learning. Radiol.: Artif. Intell. 4(5) (2022). [https://doi.org/](https://doi.org/10.1148/ryai.210315) [10.1148/ryai.210315](https://doi.org/10.1148/ryai.210315)
- <span id="page-9-6"></span>7. Osuala, R., et al.: medigan: a Python library of pretrained generative models for medical image synthesis. J. Med. Imaging  $10(6)$ , 061403 (2023). [https://doi.org/](https://doi.org/10.1117/1.JMI.10.6.061403) [10.1117/1.JMI.10.6.061403](https://doi.org/10.1117/1.JMI.10.6.061403)
- <span id="page-9-7"></span>8. Anton, J., et al.: How well do self-supervised models transfer to medical imaging? J. Imaging 8(12), 320 (2022). <https://doi.org/10.3390/jimaging8120320>
- <span id="page-9-8"></span>9. Morozov, S., Voynov, A., Babenko, A.: On self-supervised image representations for gan evaluation. In: ICLR (2020)
- <span id="page-9-9"></span>10. He, K., Fan, H., Wu, Y., Xie, S., Girshick, R.: Momentum contrast for unsupervised visual representation learning. In: Zabih, R., et al. (eds.) CVPR. IEEE (2020)
- <span id="page-9-10"></span>11. Chen, J., Wei, J., Li, R.: Targan: target-aware generative adversarial networks for multi-modality medical image translation. In: de Bruijne, M., et al. (eds.) MICCAI. pp. 24–33. Springer (2021). [https://doi.org/10.1007/978-3-030-87231-1\\_3](https://doi.org/10.1007/978-3-030-87231-1_3)
- 12. Jung, E., Luna, M., Park, S.H.: Conditional gan with an attention-based generator and a 3d discriminator for 3d medical image generation. In: de Bruijne, M., et al. (eds.) MICCAI. pp. 318–328. Springer (2021). [https://doi.org/10.1007/978-3-030-](https://doi.org/10.1007/978-3-030-87231-1_31) [87231-1\\_31](https://doi.org/10.1007/978-3-030-87231-1_31)
- <span id="page-9-11"></span>13. Tronchin, L., Sicilia, R., Cordelli, E., Ramella, S., Soda, P.: Evaluating gans in medical imaging. In: Engelhardt, S., et al. (eds.) DGM4MICCAI, DALI. pp. 112– 121. Springer (2021). [https://doi.org/10.1007/978-3-030-88210-5\\_10](https://doi.org/10.1007/978-3-030-88210-5_10)
- <span id="page-9-12"></span>14. Heimann, T., et al.: Comparison and evaluation of methods for liver segmentation from ct datasets. IEEE Trans. Med. Imaging  $28(8)$ , 1251–1265 (2009). [https://doi.](https://doi.org/10.1109/TMI.2009.2013851) [org/10.1109/TMI.2009.2013851](https://doi.org/10.1109/TMI.2009.2013851)
- <span id="page-9-13"></span>15. Wang, X., et al.: Chestx-ray8: Hospital-scale chest x-ray database and benchmarks on weakly-supervised classification and localization of common thorax diseases. In: Chellappa, R., et al. (eds.) CVPR. IEEE (2017)

- <span id="page-10-0"></span>16. Antonelli, M., et al.: The medical segmentation decathlon. Nat. Commun. 13(1), 4128 (2022). <https://doi.org/10.1038/s41467-022-30695-9>
- <span id="page-10-1"></span>17. Simpson, A.L., et al.: A large annotated medical image dataset for the development and evaluation of segmentation algorithms. [arXiv:1902.09063](http://arxiv.org/abs/1902.09063) (2019)
- <span id="page-10-2"></span>18. Bernard, O., et al.: Deep learning techniques for automatic mri cardiac multistructures segmentation and diagnosis: Is the problem solved? IEEE Trans. Med. Imaging 37(11), 2514–2525 (2018). <https://doi.org/10.1109/TMI.2018.2837502>
- <span id="page-10-3"></span>19. Karras, T., et al.: Analyzing and improving the image quality of stylegan. In: Zabih, R., et al. (eds.) CVPR. IEEE (2020)
- <span id="page-10-4"></span>20. Karras, T., et al.: Training generative adversarial networks with limited data. In: Larochelle, H., et al. (eds.) NeurIPS. vol. 33, pp. 12104–12114. Curran Associates, Inc. (2020)
- <span id="page-10-5"></span>21. Zhao, S., Liu, Z., Lin, J., Zhu, J.Y., Han, S.: Differentiable augmentation for dataefficient gan training. In: Larochelle, H., et al. (eds.) NeurIPS. vol. 33, pp. 7559– 7570. Curran Associates, Inc. (2020)
- <span id="page-10-6"></span>22. Jiang, L., Dai, B., Wu, W., Loy, C.C.: Deceive d: Adaptive pseudo augmentation for gan training with limited data. In: Ranzato, M. (ed.) NeurIPS. vol. 34, pp. 21655–21667. Curran Associates, Inc. (2021)
- <span id="page-10-7"></span>23. Dowson, D., Landau, B.: The fréchet distance between multivariate normal distributions. J. Multivar. Anal. 12(3), 450–455 (1982). [https://doi.org/10.1016/0047-](https://doi.org/10.1016/0047-259X(82)90077-X) [259X\(82\)90077-X](https://doi.org/10.1016/0047-259X(82)90077-X)
- <span id="page-10-8"></span>24. Szegedy, C., et al.: Going deeper with convolutions. In: Bischof, H., et al. (eds.) CVPR. IEEE (2015)
- <span id="page-10-9"></span>25. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Zabih, R., et al. (eds.) CVPR. IEEE (2016)
- <span id="page-10-10"></span>26. Szegedy, C., Ioffe, S., Vanhoucke, V., Alemi, A.: Inception-v4, inception-resnet and the impact of residual connections on learning. vol. 31 (2017). [https://doi.org/10.](https://doi.org/10.1609/aaai.v31i1.11231) [1609/aaai.v31i1.11231](https://doi.org/10.1609/aaai.v31i1.11231)
- <span id="page-10-11"></span>27. Huang, G., Liu, Z., van der Maaten, L., Weinberger, K.Q.: Densely connected convolutional networks. In: CVPR. IEEE (2017)
- <span id="page-10-12"></span>28. Deng, J., et al.: Imagenet: A large-scale hierarchical image database. In: CVPR. pp. 248–255. IEEE (2009). <https://doi.org/10.1109/CVPR.2009.5206848>
- <span id="page-10-13"></span>29. Caron, M., et al.: Unsupervised learning of visual features by contrasting cluster assignments. In: NeurIPS. vol. 33, pp. 9912–9924. Curran Associates, Inc. (2020)
- <span id="page-10-14"></span>30. Caron, M., et al.: Emerging properties in self-supervised vision transformers. In: Berg, T., et al. (eds.) ICCV. pp. 9650–9660. IEEE (2021)
- <span id="page-10-15"></span>31. Li, Z., Wang, Y., Yu, J.: Swin transformer: Hierarchical vision transformer using shifted windows. In: Berg, T., et al. (eds.) ICCV. pp. 10012–10022. IEEE (2021)
- <span id="page-10-16"></span>32. Zhou, H.Y., Lu, C., Yang, S., Yu, Y.: Convnets vs. transformers: Whose visual representations are more transferable? In: Vandenhende, S., et al. (eds.) ICCV Workshops. pp. 2230–2238. IEEE (2021)
- <span id="page-10-17"></span>33. Kang, M., Shim, W., Cho, M., Park, J.: Studiogan: A taxonomy and benchmark of gans for image synthesis. Trans. Pattern Anal. Mach. Intell. 45(12), 15725–15742 (2023). <https://doi.org/10.1109/TPAMI.2023.3306436>

Image /page/11/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a gray bookmark shape inside. The bookmark is a rectangle with a triangular cutout on the right side. The text below the icon reads "Check for updates" in gray, sans-serif font.

# Few-Shot Domain Adaptive Object Detection for Microscopic Images

 $\operatorname{Sumayya}$ Inayat<br/>( $\boxtimes$ ), Nimra Dilawar, Waqas Sultani, and Mohsen Ali

Intelligent Machines Lab, Department of Artificial Intelligence, Information Technology University, Lahore, Pakistan

{sumayya.inayat,nimra.dilawar,waqas.sultani,mohsen.ali}@itu.edu.pk

Abstract. Currently, unsupervised domain adaptive strategies proposed to overcome domain shift, are handicapped by the requirement of large amount of target data. On the other hand medical imaging problems and datasets are often characterized not only by scarcity of labeled and unlabeled data but also class imbalance. Few-shot domain adaptive object detection (FSDAOD) addresses the challenge of adapting object detectors to target domains with limited labeled data. However, existing FSDAOD works struggle with randomly selected target domain images which might not represent the target distribution, resulting in overfitting and poor generalization. We propose a novel FSDAOD strategy for microscopic imaging to tackle high-class imbalance and localization errors due to foreground-background similarity. Our contributions include: a domain adaptive class balancing strategy for few shot scenario and label dependent cross domain feature alignment. Specifically, multi-layer instance-level inter and intra-domain feature alignment is performed by enhancing similarity between the instances of classes regardless of the domain and increasing dissimilarity between instances of different classes. In order to retain the features necessary for localizing and detecting minute texture variations in microscopic objects across the domain, the classification loss was applied at feature-map before the detection head. Extensive experimental results with competitive baselines indicate the effectiveness of our proposed approach, achieving state-of-the-art results on two public microscopic datasets, M5 [\[12](#page-20-0)] and Raabin-WBC [\[10](#page-20-1)]. Our method outperformed both datasets, increasing average mAP@50 by 8.3 points and 14.6 points, respectively. The project page is available here [\(https://im.itu.edu.pk/few-shot-DAODMI/\)](https://im.itu.edu.pk/few-shot-DAODMI/).

Keywords: Few shot domain adaptive Object Detection · Second keyword · class-balancing-cut-paste

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_10.](https://doi.org/10.1007/978-3-031-72390-2_10)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 98–108, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_10)\_10

# 1 Introduction

Deep-learning methods have shown substantial success for microscopic analysis  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$  $[3,9,14,16,18,19,21]$ , however, they require a lot of expert-annotated data, which is hard to obtain due to limited expert time and availability. This pose a significant limitation when required to train on one domain (source) and tested on other (target), due to domain shift. Current unsupervised domain adaptation methods, have unrealistic assumption of access to large unlabeled target dataset. A more realistic scenario is having few labeled image samples from target domain, a challenge known as Few-Shot Domain Adaptive Object Detection (FSDAOD) [\[4](#page-19-1)[,5](#page-19-2),[8,](#page-20-7)[20\]](#page-21-1).

The discrepancy between the source (abundant train data) and target (fewshot samples) domains arises due to differences in data acquisition protocols [\[15](#page-20-8)[,20](#page-21-1)], such as microscopic quality, lighting conditions, microscopic-lens resolution, and camera-lens quality. This challenge is further compounded by extreme data imbalance in microscopic cells  $[6,12,13]$  $[6,12,13]$  $[6,12,13]$  $[6,12,13]$ , visual similarity between the background and foreground, and intra-foreground visual similarity, leading to higher false-positive and false-negative rates. Recently, unsupervised and fewshot domain adaptive approaches have been proposed  $[4,5,20]$  $[4,5,20]$  $[4,5,20]$  $[4,5,20]$  to tackle this limitation. However, as our experimental results show (Tables [1,](#page-16-0) [2](#page-16-1) and [3\)](#page-16-2), these methods suffer from overfitting, leading to less generalization.

Since in few-shot domain adaptive object detection, only a few samples of any class are available from the target domain, a strategy for the feature alignment could be to enforce that, the representation of the same class across the domain is same. However, this results in over-adaptation to the smaller few-shot set. To address this challenge, we propose *intra and inter-Domain Feature Alignment* technique; I2DA, that addresses (a) the cross-domain shift between similar class cells by aligning the inter-domain feature level representations of cells coming from the same classes, and (b) Intra-Domain Feature Consistency at the cell level to learn distinguishable features for each class because the foreground cells in microscopic datasets possess high visual similarity with the background cells. This is especially challenging in the case of malarial-affected cells where the foreground classes are very similar to the background platelets, resulting in a higher rate of false positives (Supp. Fig. 1). Secondly, we propose a *Domain-Generalized Class Balancing Cut-Paste strategy*; CBCP to tackle the extreme class imbalance when dealing with microscopic datasets. CBCP balances the overall count of the rare and abundant classes in the data. We propose generating samples of rare classes to match the abundant class. New samples are created by replicating existing ones with target domain-inspired visual augmentations to avoid over fitting. Simply stating, we crop the existing cells, apply visual augmentations, and paste them in carefully selected locations in the images where no other cells are present. The extensive experimentation on two public microscopic datasets M5- Malaria [\[15\]](#page-20-8) and Raabin-WBC [\[11](#page-20-11)] demonstrate the effectiveness of our method by outperforming with an increase in average mAP@50 by 8.3 point and 14.6 points (respectively) as compared to other competitive baselines.

Image /page/13/Figure/1 description: The image illustrates the Class Balancing Cut Paste (CBCP) Augmentation process. It is divided into two main sections: the original m5 dataset and the output. The original m5 dataset section shows a five-step process. Step 1 involves a source and target dataset of microscopic images. Step 2, Metadata Calculation, displays bar charts for original cell count (gametocyte, schizont, trophozoite, ring) and split images (Ig, If). Step 3, Algorithm 1, leads to getting the count to be incremented. Step 4, Augmentation, details the process with an input image (x1) and its mask, followed by augmentation techniques like Gaussian blur and random color intensity variation, resulting in augmented images (xz, Pc). Step 5, Resultant Cell Count, shows a bar chart reflecting the cell counts after augmentation. The output section presents two bar charts comparing cell counts before and after augmentation, and two sets of source and augmented source images. The augmented source images show bounding boxes indicating pre-existing cells (black), source cells (green), and target cells (red) that have been pasted onto the source images.

<span id="page-13-0"></span>Fig. 1. Class Balancing Cut Paste strategy (CBCP): We first compute the metadata and increment-stats from the abundant source dataset and few target images and then increment cells to the images with less pre-existing cells.

# 2 Methodology

**Preliminaries:** Let  $\{(x_i^s, y_i^s)\}_{i=1}^{N_s} \in \mathcal{D}_s$  be dataset of images from source domain and  $\{(x_i^t, \mathbf{y}_i^t)\}_{i=1}^{N_t} \in \mathcal{D}_t$  be few-shot dataset from the target domain. Where,  $(x, \mathbf{y})$  is impose and ground truth pair for object detection tools. Note that  $(x_i, y_i)$  is image and ground-truth pair for object detection task. Note that,  $|\mathcal{D}_s| \gg |\mathcal{D}_t|$ , and label space in both the domains is same. The N be the total number of classes, and both datasets suffer from class imbalance. Objective is to train the object detector  $\mathcal{F}$ , using the source dataset  $\mathcal{D}_s$  and overcome domain shift challenges by using few shot target dataset  $\mathcal{D}_t$ .

## 2.1 Domain Generalized Class Balancing Cut-Paste

To address the limitations of existing image resampling techniques [\[6\]](#page-20-9) we propose to increase the count of rare class instances to match the maximum instances class count. Instead of matching the number of instances per class exactly, our goal is to achieve a more realistic and balanced distribution. In the text following we will use the terminology of class-size to indicate number of instances per class Class Balancing Cut Paste Strategy (CBCP): Given,  $\mathcal{D}_s$  and  $\mathcal{D}_t$ , we construct a new dataset  $\mathcal{D}_{aug}$  from their combination, such that  $|\mathcal{D}_{aug}| = |\mathcal{D}_s|$  but has balanced class-sizes (Fig. [1\)](#page-13-0). From  $\mathcal{D}_s$  we collect following data statistics: (1) *O<sub>c</sub>*: number of instance per class *c*, (2)  $\mathcal{I}_f = \{(x_i^s, \mathbf{y}_i^s) \mid s.t. |\mathbf{y}_i^s| < r \& (x_i^s, \mathbf{y}_i^s) \in \mathcal{I}_i\}$  (3)  $\mathcal{I}_f = \{(x_i^s, \mathbf{y}_i^s) \in f | \mathbf{y}_i^s| > r \& (x_i^s, \mathbf{y}_i^s) \in \mathcal{I}_i\}$  (A)  $\mathcal{P}^c$  images wi  $\mathcal{D}_s$  { 3)  $\mathcal{I}_g = \{(x_i^s, \mathbf{y}_i^s) \text{ s.t. } |\mathbf{y}_i^s| \ge r \& (x_i^s, \mathbf{y}_i^s) \in \mathcal{D}_s$  { 4)  $\mathcal{P}_s^c$  images with  $c^{th}$  class present,  $\mathcal{P}_s^c \subset \mathcal{D}_s$ . We need to determine how many times each instance has to be replicated,  $S^c = (max({\{O_c\}}_{c=1}^N) - O_c)/O_c$ , to balance the distribution (see supplementary Algorithm 1). However, these replicated instances are only added in the images  $x_i \in \mathcal{I}_f$  thus trying to keep distribution of instances in each image

about same. Specifically, for each instance in  $\mathcal{P}_s^c$ , we create its multiple copies as per  $\mathcal{S}^c$ , by applying random visual augmentations. Thus creating the list  $P_l$  of candidates to be used for balancing. Next, for each  $x_i \in \mathcal{I}_f$  using  $\mathbf{y}_i^s$  we identify regions where no object is present. Objects from P*<sup>l</sup>* are randomly selected and placed at non-overlapping locations, taking in consideration that distribution of classes is balanced.

To achieve a domain generalized we extract a random object from a random image  $x_j \in \mathcal{D}_t$  and paste it in  $x_z \in \mathcal{D}_{aug}$  and update the corresponding annotations. Note that for random visual augmentation, we choose random color intensity variation and random Gaussian blurring (see supplementary). We only increment cells to  $\mathcal{I}_f$  to ensure more realistic real world simulation. The overall flow of the process is shown in Fig. [1.](#page-13-0)

Image /page/14/Figure/3 description: The image displays a proposed framework for a deep learning model. The framework starts with 'Source' and 'Few Target' data inputs, which are processed through 'CBCP Augmentation'. The augmented source data feeds into a 'Backbone' and 'Neck' component, followed by an 'upsampling' module. This upsampled data then enters a 'Cell wise Feature Extractor' which produces 'Feature Volumes' and 'Feature Vectors Label' after pooling. The framework also includes a 'Detection head' that receives input from different levels (L3, L2, L1) of the backbone. The extracted features are then processed through multiple stages, indicated by stacked rectangles and arrows, leading to three distinct loss functions: 'Min Intra class distance' (Similarity Loss), 'Max Inter class mean distance' (Dissimilarity Loss), and 'Min Identification error' (Classification Loss). These three losses are combined into an 'I2DA Loss'. The 'Min Intra class distance' section visualizes cells of the c-th class with arrows indicating repulsion and attraction. The 'Max Inter class mean distance' section shows means of c classes with arrows indicating repulsion. The 'Min Identification error' section shows a 'Leaky Relu' activation function connected to purple circles representing feature vectors.

<span id="page-14-0"></span>Fig. 2. Proposed approach: We first build our class-wise balanced dataset through our proposed Class Balancing Cut Paste (CBCP) augmentation strategy (Fig. [1\)](#page-13-0), then train the model with our proposed *inter-intra-domain alignment* (I2DA); inter-domain instance feature-level alignment and intra-domain instance feature-level consistency. We extract multi-layer neck features and upsample them to a common size, followed by the extraction of pooled object-level features, which are then passed to the similaritydissimilarity and classification module.

## 2.2 Inter-domain Alignment and Intra-domain Class Consistency

Generally feature level adversarial loss is used to align the features across domain. However, such strategies gets overwhelmed by large background information and fail to overcome domain shift at object level. Applying object level contrastive loss [\[2](#page-19-3)] for domain adaptive object detection, results in high false positive when there is high visual similarity between foreground and background. This is especially true for malarial peripheral blood smear slides.

To enforce feature alignment across domain, we propose maximizing the dissimilarity between instances of different classes while minimizing the similarity between instances of the same class. Specifically to achieve we formulate a novel solution, (see Fig. [2\)](#page-14-0), aiming to learn more robust instance features. The similarity and dissimilarity must be performed within the domain as well as across domain to learn the diversity of intra-class features and capture inter-class variations.

We extract multi-layer instance-level features from the neck of the detector  $F$ . The reason behind choosing multi-layer neck features is to take the most representative small, medium, and large-size object-level features. We up-sample these features to  $(S \times S)$  and extract all the cell features corresponding to its ground truth, followed by average pooling.

Let,  $n_c$  be the total instances of objects belonging to the class  $c$  in the batch. Let  $\mathbf{v}_{ik}$  and  $\mathbf{v}_{il}$  denote the feature vector of object k and l of  $c^{th}$  class. We compute pairwise cosine similarity, denote by  $\text{sim}(\mathbf{v}_{ik}, \mathbf{v}_{il})$ , between features of all the objects of same class. We compute the similarity loss for each level of features. We sum up all the losses to compute the overall similarity loss L*sim*.

<span id="page-15-0"></span>
$$
L_{\text{sim}} = \sum_{c} \frac{1}{\binom{n_c}{2}} \sum_{k=1}^{n_c - 1} \sum_{l=k+1}^{n_c} \text{sim}(\mathbf{v}_{ck}, \mathbf{v}_{cl}).
$$
 (1)

To improve the classification between classes during the domain shift, we maximize dissimilarity loss. Instead of increasing dissimilarity between instances, we push for increasing dissimilarity between the mean representation of each class. Let  $\bar{\mathbf{v}}_{\mathbf{c}_i}$  be the mean of the  $n_{c_i}$  instances of  $c_i$ <sup>th</sup> class.

We compute pair-wise similarity between mean-feature representations of classes. Specifically, similarity  $d$  is computed using cosine similarity function between pairs of mean-feature representations of two classes. If  $s_d < m$ , set  $d = 0$ . Finally, the total dissimilarity loss  $L_{dis}$  can be obtained by summing up the resulting d values for all pairwise combinations of mean feature vectors:

<span id="page-15-1"></span>
$$
L_{\text{dis}} = \sum_{c_i=1}^{C-1} \sum_{c_j=c_i+1}^{C} \max\left(0, \left(\frac{\bar{\mathbf{v}}_{c_i} \cdot \bar{\mathbf{v}}_{c_j}}{\|\bar{\mathbf{v}}_{c_i}\| \|\bar{\mathbf{v}}_{c_j}\|}\right) - m\right).
$$
(2)

We boost the learning with a classifier to learn more robust features for each class. We compute the  $C$  class-wise classification losses, let  $l_c^k$  denote the class loss of instance  $k$  in  $c^{th}$  class, Let  $n_c$  be the number of instances in class  $c$ , then the instance level classification loss  $L_{cls}$  is given by:

<span id="page-15-2"></span>
$$
L_{\rm cls} = \sum_{c=1}^{C} \left( \frac{1}{n_c} \sum_{k=1}^{n_c} l_c^k \right)
$$
 (3)

|                         | Malaria-HCM-1000 $x \rightarrow$ Malaria-LCM-1000 $x$ |                            |      |                |         |             |                |      |      |                |      |      |                |      |      |
|-------------------------|-------------------------------------------------------|----------------------------|------|----------------|---------|-------------|----------------|------|------|----------------|------|------|----------------|------|------|
| Method                  |                                                       | $mAP@50(\%)$<br>Gametocyte |      | Schizont       |         | Trophozoit  |                |      | Ring |                |      |      |                |      |      |
| Source                  | 19.9                                                  | 3.9                        |      |                | 0.5     |             |                | 55.9 |      |                | 19.3 |      |                |      |      |
| Oracle                  | 43.7                                                  |                            |      | 33.3           |         |             | 4.3            |      |      | 81.6           |      |      | 55.7           |      |      |
| <b>Shots</b>            | $\overline{2}$                                        | 3                          | 5    | $\overline{2}$ | 3       | 5           | $\overline{2}$ | 3    | 5    | $\overline{2}$ | 3    | 5    | $\overline{2}$ | 3    | 5    |
| F <sub>s</sub> Det [17] | 11.2                                                  | 11.5                       | 12.9 | 11.8           | 11.2    | 12.3        | 0.0            | 0.0  | 0.0  | 27.7           | 28.3 | 30.7 | 5.4            | 6.5  | 8.6  |
| <b>VFA</b> [7]          | 8.9                                                   | 6.5                        | 8.1  | 9.1            | $3.2\,$ | 24.5        | 0.1            | 3.3  | 0.2  | 17.8           | 19.4 | 6.5  | 12.8           | 0.2  | 1.3  |
| FDP <sub>[1]</sub>      | 14.6                                                  | 14.7                       | 20.5 | 16.4           | 8.1     | 33.7        | 1.20           | 10.1 | 1.3  | 27.4           | 24.4 | 33.3 | 13.5           | 16.4 | 14.0 |
| AsvFOD [4]              | 26.0                                                  | 29.1                       | 33.5 | 14.9           | 23.3    | 36.8        | 1.20           | 7.0  | 2.80 | 59.4           | 60.7 | 64.7 | 28.7           | 31.8 | 30.9 |
| $AcroFOD$ [5]           | 32.9                                                  | 42.5                       | 39.1 | 27.6           | 50.9    | 62.9        | 17.6           | 22.1 | 5.40 | 58.7           | 62.7 | 61.3 | 27.8           | 34.4 | 27.0 |
| Ours                    | 44.7                                                  | 45.9                       | 48.9 | 71.4           |         | 66.0   68.2 | 11.4           | 18.1 | 30.4 | 66.9           | 66.7 | 65.6 | 29.3           | 32.6 | 31.5 |

<span id="page-16-0"></span>**Table 1.** Results in mAP $@50\%$  on Malaria [\[15\]](#page-20-8) test set.

<span id="page-16-1"></span>**Table 2.** Results in mAP $@50\%$  on Raabin-WBC [\[11\]](#page-20-11) test set.

| Raabin-WBC-HCM → Raabin-WBC-LCM |          |             |      |      |            |      |      |             |      |      |          |      |      |      |      |
|---------------------------------|----------|-------------|------|------|------------|------|------|-------------|------|------|----------|------|------|------|------|
| Method                          | mAP50(%) | Large Lymph |      |      | Neutrophil |      |      | Small Lymph |      |      | Monocyte |      |      |      |      |
|                                 |          | 2           | 3    | 5    | 2          | 3    | 5    | 2           | 3    | 5    | 2        | 3    | 5    |      |      |
| Source                          | 27.2     | 25.1        |      |      | 59.6       |      |      | 22.9        |      |      | 1.0      |      |      |      |      |
| Oracle                          | 75.0     | 90.9        |      |      | 98.1       |      |      | 83.2        |      |      | 27.7     |      |      |      |      |
| Shots                           | 2        | 3           | 5    | 2    | 3          | 5    | 2    | 3           | 5    | 2    | 3        | 5    |      |      |      |
| FsDet [17]                      | 26.5     | 28.5        | 30.1 | 41.5 | 24.3       | 38.1 | 17.5 | 31.8        | 44.1 | 23.7 | 34.4     | 29.6 | 23.1 | 23.7 | 8.7  |
| VFA [7]                         | 30.3     | 33.2        | 45.2 | 44.6 | 28.6       | 59.8 | 25.7 | 59.8        | 66.0 | 48.7 | 27.7     | 42.6 | 2.40 | 16.7 | 12.5 |
| FDP [1]                         | 35.9     | 32.4        | 44.3 | 28.2 | 36.2       | 59.1 | 60.7 | 39.8        | 66.8 | 46.2 | 34.0     | 35.2 | 8.60 | 19.9 | 15.9 |
| AsyFOD[4]                       | 35.6     | 38.1        | 26.3 | 37.2 | 42.2       | 39.3 | 58.3 | 48.7        | 43.1 | 46.5 | 51.1     | 22.4 | 0.3  | 10.4 | 0.3  |
| AcroFOD[5]                      | 44.9     | 47.2        | 61.2 | 50.5 | 64.1       | 82.1 | 88.1 | 89.1        | 95.9 | 37.6 | 30.7     | 59.6 | 3.5  | 5.1  | 7.3  |
| Ours                            | 64.2     | 62.6        | 70.8 | 74.1 | 75.2       | 87.2 | 86.0 | 94.3        | 54.7 | 46.8 | 42.5     | 40.6 | 41.6 | 71.3 |      |

<span id="page-16-2"></span>**Table 3.** mAP@50(%) on [\[15](#page-20-8)] & [\[11](#page-20-11)] test sets on 8 random few-target images.

| Data    | Malaria     |             |            |             |      | Raabin-WBC  |             |             |             |             |
|---------|-------------|-------------|------------|-------------|------|-------------|-------------|-------------|-------------|-------------|
|         | mAP@50      | Gamet.      | Schizo.    | Troph.      | Ring | mAP@50      | L-Lymp.     | Neutro.     | S-Lymp.     | Mono.       |
| AsyFOD  | 30.2        | 23.8        | 1.3        | 61.8        | 33.9 | 33.7        | 28.4        | 48.6        | <b>56.2</b> | 1.4         |
| AcroFOD | <b>33.1</b> | <b>46.8</b> | <b>3.8</b> | 56.9        | 24.9 | 48.9        | 69.1        | <b>90.7</b> | 27.7        | 7.9         |
| Ours    | <b>40.3</b> | <b>62.3</b> | 2.0        | <b>64.4</b> | 32.6 | <b>55.7</b> | <b>71.3</b> | 80.6        | 49.6        | <b>23.9</b> |

Finally, we compute the mean similarity (Eq. [1\)](#page-15-0), dissimilarity (Eq. [2\)](#page-15-1), and classification (Eq. [3\)](#page-15-2) losses for the three levels, followed by multiplication with weights  $\lambda_1$ ,  $\lambda_2$  and  $\lambda_3$  with the similarity, dissimilarity, and class mean losses respectively. We add up the losses as our final I2DA loss, L*<sup>I</sup>*2*DA* is represented as:

$$
L_{I2DA} = \lambda_1 L_{sim} + \lambda_2 L_{dis} + \lambda_3 L_{cls}
$$
\n<sup>(4)</sup>

# 3 Experiments and Results

Datasets: M5 [\[15\]](#page-20-8) is a large-scale malarial domain adaptive cell detection dataset captured from two different microscopes, one high cost, and one low cost, and the corresponding images captured from three different resolution levels. We utilized their standard train val test splits for training whereas for few-shot settings, we randomly sampled a set of 8 images as per [\[4,](#page-19-1)[5\]](#page-19-2) while also selected images as per our strategy (described in section below) for 2-shot, 3-shot, and 5-shot. We consider the shots as the number of images per a specific category.

Raabin-WBC. (R-WBC) is a white blood cell dataset comprising images captured by a mobile phone camera. 17819 images belong to the high-cost microscope and 3167 images belong to the low-cost microscope. The authors did not provide any standard train, val, or test splits for the detection task, hence we first extracted center cropped images and as per 'Label2', for the four following classes, Large Lymph, Neutrophill, Small Lymph, and Monocyte. We then made random splits of the train (2052 images), val (150 images), and test (450 images) for both the microscope data and chose the few-shot samples similar to M5.

<span id="page-17-0"></span>

| <b>CBCP</b> | $L_{\mathrm{sim}}$ | $L_{\rm dis}$ | $L_{\rm cls}$ | M5          | R-WBC       |
|-------------|--------------------|---------------|---------------|-------------|-------------|
| ✓           | x                  | x             | x             | 42.1        | 69.2        |
| x           | ✓                  | x             | x             | 40.3        | 63.8        |
| x           | x                  | ✓             | x             | 41.9        | 65.2        |
| x           | x                  | x             | ✓             | 41.9        | 59.3        |
| x           | ✓                  | ✓             | x             | 42.4        | 68.5        |
| x           | ✓                  | x             | ✓             | 44.3        | 67.6        |
| x           | ✓                  | ✓             | ✓             | 43.9        | 65.4        |
| x           | ✓                  | ✓             | ✓             | 45.4        | 66.5        |
| ✓           | ✓                  | ✓             | ✓             | <b>48.9</b> | <b>70.8</b> |

Table 4. Ablation of each component of our method on a 5-shot set

Implementation Details: Our proposed approach is object detector agnostic, however, for a fair comparison with  $[4,5]$  $[4,5]$  $[4,5]$ , we have used  $[10]$  $[10]$  as a base model. For our experiments (conducted on GTX1080 GPU), batch-size was set to 4. We develop customized batches for each epoch such that each batch of the extracted features contains  $n \geq 1$  object from the few-shot target set. For each batch, we select 1 image from the few-shot set, 2 images from the real source, and 1 image from the augmented source. However, for training with larger batch sizes, we suggest allocating 2% of the batch size to the real target, 68% to the real source, and 30% to the augmented source dataset. The  $\lambda_1$   $\lambda_2$  and  $\lambda_3$  values are set to 0.005, 0.005, and 0.001 respectively. We chose low weights to scale the complete I2DA loss with the YOLOv5 detection loss.

## 3.1 Results

We perform two sets of experiments, one with  $8$  random images as per [\[4,](#page-19-1)[5](#page-19-2)] that may have any number of images per class or even miss a rare class. The other set of experiments is performed on our variation of k-shot settings. We define our shots as k images per class. Malaria results of the baselines  $[1,4,5,7,17]$  $[1,4,5,7,17]$  $[1,4,5,7,17]$  $[1,4,5,7,17]$  $[1,4,5,7,17]$  $[1,4,5,7,17]$ and our work on 2-shot, 3-shot, and 5-shot images are shown in Table [1.](#page-16-0) We evaluated our models based on mAP@50 because all the given baselines [\[1,](#page-19-4)[4,](#page-19-1) [5,](#page-19-2)[7](#page-20-13)[,17](#page-20-12)] yielded results in mAP@50. But for [\[4](#page-19-1)[,5](#page-19-2)] we additionally evaluated the models on mAP@50:95 and average precision and average recall. Note that we performed 100 epochs of adaptation and reported results over the test dataset for the model obtained after 100 epochs. The same experimental settings are used for all the comparative methods. As shown in Table [1,](#page-16-0) our work outperforms the existing competitive baseline by a good margin in each shot setting. Table [2](#page-16-1) shows the results of the Raabin-WBC test set and proves that our method works well for large-size objects as well. Table [3](#page-16-2) shows our results obtained on 8 random target domain images and as visible our method outperforms in these settings as well. Please refer to the supplementary for detailed results and comparisons. Figure [3](#page-18-0) shows the qualitative results of AcroFOD [\[5\]](#page-19-2) and our methods. AcroFOD has given some wrong predictions and was comparatively less confident in the correct predictions. In contrast, ours is more confident in the correct predictions and the false-positive rate is comparatively less.

## 3.2 Ablation

Table [4](#page-17-0) shows the ablation studies of each of the I2DA loss components and the CBCP module on 5-shot sets of M5 and Raabin-WBC, respectively. The results validate the effectiveness of each component of the I2DA loss and the CBCP module. I2DA and CBCP complement each other hence obtaining the best results when applied together. We also performed ablation of our CBCP augmentation

Image /page/18/Figure/5 description: This image is a comparative analysis of different object detection models for malaria parasite detection in blood samples. It displays a grid of images, with the top row showing ground truth and the bottom row showing results from three different models: AcroFOD, I2DA (ours), and I2DA + CBCP (ours). Each image within the grid is a microscopic view of red blood cells, with bounding boxes indicating detected parasites. The bounding box colors correspond to different parasite stages: teal for gametocyte, yellow for schizont, blue for trophozoite, and purple for ring. A red bounding box signifies a wrong detection. The top row shows two blue bounding boxes in the ground truth, indicating trophozoites. The bottom row shows the detection results for each model. AcroFOD has a red bounding box (wrong detection) and a purple bounding box (ring). I2DA has two blue bounding boxes (trophozoites). I2DA + CBCP has one purple bounding box (ring) and one blue bounding box (trophozoite).

<span id="page-18-0"></span>Fig. 3. Qualitative results of LCM Malarial defected regions after adaptation.

with AcroFOD alignment algorithm. AcroFOD alignment results w/o augmentation (M5:37.8|RWBC:58.7) are much less than our alignment-only I2DA results (M5:44.2|RWBC:66.5). AcroFOD alignment prioritizes target-similar examples, but we believe that small few-shot sets can't fully represent the whole target population. Our approach extracts moderate knowledge from the target set, ensuring generalizability to larger test sets while optimizing performance across the majority of few-shot scenarios.

# 4 Conclusion

We have put forwarded a novel solution to tackle **FSDAOD** in few shot settings in microscopic imaging. The intra-class feature space variation is minimized and inter-class variation is maximized irrespective of domains to boost the performance with a specialized feature-level instance classifier. To handle the extreme class imbalance in microscopic datasets, especially in domain adaptive few-shot settings, we devise a novel strategy to balance the skewed data distribution with our cut-paste augmentation strategy. Extensive experimentation validate the effectiveness of our method as compared to the existing competitive baselines. Our method outperform the competitive baselines on average 8.3 points on M5 dataset and 14.7 points on Raabin-WBC demonstrating it capability to handle variable cell sizes.

Acknowledgments. This research was partially funded by the Google Research award.

Disclosure of Interests. The authors have no competing interests.

# References

- <span id="page-19-4"></span>1. Wang, Z., Yang, B., Yue, H., Ma, Z.: Fine-grained prototypes distillation for fewshot object detection. In: Proceedings of the AAAI Conference on Artificial Intelligence, vol. 38, no. 6, pp. 5859–5866 (2024)
- <span id="page-19-3"></span>2. Cao, S., Joshi, D., Gui, L.Y., Wang, Y.X.: Contrastive mean teacher for domain adaptive object detectors. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 23839–23848 (2023)
- <span id="page-19-0"></span>3. Fujita, S., Han, X.H.: Cell detection and segmentation in microscopy images with improved mask r-cnn. In: Proceedings of the Asian conference on computer vision (2020)
- <span id="page-19-1"></span>4. Gao, Y., Lin, K.Y., Yan, J., Wang, Y., Zheng, W.S.: Asyfod: An asymmetric adaptation paradigm for few-shot domain adaptive object detection. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3261–3271 (2023)
- <span id="page-19-2"></span>5. Gao, Y., Yang, L., Huang, Y., Xie, S., Li, S., Zheng, W.S.: Acrofod: An adaptive method for cross-domain few-shot object detection. In: European Conference on Computer Vision. pp. 673–690. Springer (2022)

- <span id="page-20-9"></span>6. Gosain, A., Sardana, S.: Handling class imbalance problem using oversampling techniques: A review. In: 2017 international conference on advances in computing, communications and informatics (ICACCI). pp. 79–85. IEEE (2017)
- <span id="page-20-13"></span>7. Han, J., Ren, Y., Ding, J., Yan, K., Xia, G.S.: Few-shot object detection via variational feature aggregation. arXiv preprint [arXiv:2301.13411](http://arxiv.org/abs/2301.13411) (2023)
- <span id="page-20-7"></span>8. Han, L., Zhai, J., Yu, Z., Zheng, B.: See you somewhere in the ocean: few-shot domain adaptive underwater object detection. Frontiers in Marine Science 10, 1151112 (2023)
- <span id="page-20-2"></span>9. Huang, J., Shen, Y., Shen, D., Ke, J.: Ca 2.5-net nuclei segmentation framework with a microscopy cell benchmark collection. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2021: 24th International Conference, Strasbourg, France, September 27–October 1, 2021, Proceedings, Part VIII 24. pp. 445–454. Springer (2021)
- <span id="page-20-1"></span>10. Jocher, G., Stoken, A., Borovec, J., NanoCode012, ChristopherSTAN, Changyu, L., Laughing, tkianai, Hogan, A., lorenzomammana, yxNONG, AlexWang1900, Diaconu, L., Marc, wanghaoyang0106, ml5ah, Doug, Ingham, F., Frederik, Guilhen, Hatovix, Poznanski, J., Fang, J., , L.Y., changyu98, Wang, M., Gupta, N., Akhtar, O., PetrDvoracek, Rai, P.: ultralytics/yolov5: v3.1 - Bug Fixes and Performance Improvements (Oct 2020). <https://doi.org/10.5281/zenodo.4154370>
- <span id="page-20-11"></span>11. Kouzehkanan, Z.M., Saghari, S., Tavakoli, S., Rostami, P., Abaszadeh, M., Mirzadeh, F., Satlsar, E.S., Gheidishahran, M., Gorgi, F., Mohammadi, S., et al.: A large dataset of white blood cells containing cell locations and types, along with segmented nuclei and cytoplasm. Scientific reports 12(1), 1123 (2022)
- <span id="page-20-0"></span>12. Lin, T.Y., Goyal, P., Girshick, R., He, K., Dollár, P.: Focal loss for dense object detection. In: Proceedings of the IEEE international conference on computer vision. pp. 2980–2988 (2017)
- <span id="page-20-10"></span>13. Liu, M., Li, X., Gao, X., Chen, J., Shen, L., Wu, H.: Sample hardness based gradient loss for long-tailed cervical cell detection. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 109–119. Springer (2022)
- <span id="page-20-3"></span>14. Sudre, C.H., Van Wijnen, K., Dubost, F., Adams, H., Atkinson, D., Barkhof, F., Birhanu, M.A., Bron, E.E., Camarasa, R., Chaturvedi, N., et al.: Where is valdo? vascular lesions detection and segmentation challenge at miccai 2021. Medical Image Analysis 91, 103029 (2024)
- <span id="page-20-8"></span>15. Sultani, W., Nawaz, W., Javed, S., Danish, M.S., Saadia, A., Ali, M.: Towards lowcost and efficient malaria detection. In: 2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 20655–20664. IEEE (2022)
- <span id="page-20-4"></span>16. Thomas, R.M., John, J.: A review on cell detection and segmentation in microscopic images. In: 2017 International Conference on Circuit, Power and Computing Technologies (ICCPCT). pp. 1–5. IEEE (2017)
- <span id="page-20-12"></span>17. Wang, X., Huang, T.E., Darrell, T., Gonzalez, J.E., Yu, F.: Frustratingly simple few-shot object detection. arXiv preprint [arXiv:2003.06957](http://arxiv.org/abs/2003.06957) (2020)
- <span id="page-20-5"></span>18. Wang, X., Zhang, J., Yang, S., Xiang, J., Luo, F., Wang, M., Zhang, J., Yang, W., Huang, J., Han, X.: A generalizable and robust deep learning algorithm for mitosis detection in multicenter breast histopathological images. Medical Image Analysis 84, 102703 (2023)
- <span id="page-20-6"></span>19. Yang, S., Fang, B., Tang, W., Wu, X., Qian, J., Yang, W.: Faster r-cnn based microscopic cell detection. In: 2017 international conference on security, pattern analysis, and cybernetics (SPAC). pp. 345–350. IEEE (2017)

- <span id="page-21-1"></span>20. Zhang, J., Chao, H., Dhurandhar, A., Chen, P.Y., Tajer, A., Xu, Y., Yan, P.: Spectral adversarial mixup for few-shot unsupervised domain adaptation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 728–738. Springer (2023)
- <span id="page-21-0"></span>21. Zhao, Z., Pang, F., Liu, Z., Ye, C.: Positive-unlabeled learning for cell detection in histopathology images with incomplete annotations. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2021: 24th International Conference, Strasbourg, France, September 27–October 1, 2021, Proceedings, Part VIII 24. pp. 509–518. Springer (2021)

Image /page/22/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line on the left side and a flag shape on the right side. The button is light gray with a subtle gradient, and the text is dark gray.

# Few-Shot Lymph Node Metastasis Classification Meets High Performance on Whole Slide Images via the Informative Non-parametric Classifier

Yi Li<sup>1</sup> $\odot$ [,](http://orcid.org/0000-0002-7697-0842) Oixiang Zhang<sup>1</sup> $\odot$ , Tiangi Xiang<sup>1</sup> $\odot$ , Yiqun Lin<sup>1</sup> $\odot$ , Qingling Zhang<sup>3</sup> [,](http://orcid.org/0000-0002-5287-264X) and Xiaomeng Li<sup>1,2( $\boxtimes$ [\)](http://orcid.org/0000-0003-1105-8083)</sup>

<sup>1</sup> Department of Electronic and Computer Engineering, The Hong Kong University of Science and Technology, Hong Kong, China

<EMAIL>

<sup>2</sup> HKUST Shenzhen-Hong Kong Collaborative Innovation Research Institute, Futian, Shenzhen, China

<sup>3</sup> Department of Pathology, Guangdong Provincial People's Hospital, Guangdong Academy of Medical Sciences, Guangzhou, China

**Abstract.** Lymph node metastasis (LNM) classification is crucial for breast cancer staging. However, the process of identifying tiny metastatic cancer cells within gigapixel whole slide image (WSI) is tedious, timeconsuming, and expensive. To address this challenge, computational pathology methods have emerged, particularly multiple instance learning (MIL) based on deep learning. But these methods require massive amounts of data, while existing few-shot methods severely compromise accuracy for data saving. To simultaneously achieve few-shot and high performance LNM classification, we propose the informative non-parametric classifier (INC). It maintains informative local patch features divided by mask label, then innovatively utilizes non-parametric similarity to classify LNM, avoiding overfitting on a few WSI examples. Experimental results demonstrate that the proposed INC outperforms existing SoTA methods across various settings, with less data and labeling cost. For the same setting, we achieve remarkable AUC improvements over 36.76% on CAME-LYON16. Additionally, our approach demonstrates excellent generalizability across multiple medical centers and corrupted WSIs, even surpassing many-shot SoTA methods over 7.55% on CAMELYON16-C. Code is available at [https://github.com/xmed-lab/INC.](https://github.com/xmed-lab/INC)

**Keywords:** Few-Shot · Non-Parametric · WSI · Lymph Node Metastasis

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_11).11.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 109–119, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_11)\_11

# 1 Introduction

Pathological Whole Slide Image (WSI) serves as the gold standard for cancer diagnosis. For breast cancer, the lymph node metastasis (LNM) classification is an essential diagnostic staging task, according to the tumor, nodes, metastasis staging system. In clinical practice, it is tedious, time-consuming, and expensive to search tiny metastatic cancer cells from multiple lymph nodes within gigapixel WSI. Therefore, deep learning based computational pathology schemes [\[1](#page-31-0)[,17](#page-32-0)] emerged to this end.

Image /page/23/Figure/3 description: The image displays two figures, (a) and (b). Figure (a) is a scatter plot titled "AUC vs WSI number (Bubble Size: Labeling Cost)". The y-axis represents AUC (%) and ranges from 50 to 100. The x-axis represents WSI number and ranges from 0 to 250. The plot shows three data series: CLAM (many-shot SoTA) as red circles, SimpleShot (few-shot SoTA) as pink circles, and INC (ours) as purple circles. The size of the bubbles indicates the labeling cost. Arrows and text annotations highlight trends such as "remarkable improvements", "trade-off between data number and performance", and "few-shot meets high performance via our INC". Figure (b) illustrates a comparison between a parametric classifier of MIL methods and a non-parametric classifier from averaged features (our INC). The left side shows a parametric classifier with "learnable parameters" represented by a flame icon, which is multiplied with "features of query WSI" (Nq). The right side shows a non-parametric classifier with "stastic features" represented by a snowflake icon, which is multiplied with "features of query WSI" (Ng). An arrow from the parametric classifier side points to "no fine-tuning, less over-fitting", while an arrow from the non-parametric classifier side points to "rich information, dynamic features".

<span id="page-23-0"></span>**Fig. 1.** (a) Few-shot LNM classification meets high performance via the proposed INC method on CAMELYON16 dataset [\[1\]](#page-31-0). (b) INC is a non-parametric classifier, maintaining rich information without fine-tuning. The notion of a parametric classifier can be regarded as an FC layer with fixed weights, while a non-parametric classifier (e.g., KNN) uses the globally averaged feature. In comparison, our INC method keeps all local features and matches them dynamically according to individual test examples.

Currently, the predominant WSI classification technique is multiple instance learning (MIL) [\[11](#page-31-1),[12,](#page-31-2)[19](#page-32-1)[,22](#page-32-2)], including techniques like feature fusion [\[14\]](#page-31-3), selfattention [\[12](#page-31-2)[,22\]](#page-32-2), pretraining [\[4,](#page-31-4)[13](#page-31-5)] etc. [\[15](#page-32-3)[,18](#page-32-4)]. Notably, conventional MIL methods require massive data, *e.g.*, Campanella et al. [\[2](#page-31-6)] used thousands of training WSIs for LNM classification. However, acquiring a large training set can be laborious, costly, and demands significant training computational resources. To address these issues, few-shot methods have been proposed for WSI classification tasks, which only use several training examples at hundreds of times less data, *e.g.*, text prompt learning [\[20\]](#page-32-5), data-efficient MIL [\[19](#page-32-1)], prototype [\[5\]](#page-31-7) with general few-shot learning [\[24](#page-32-6)]. Besides, some pathological few-shot techniques [\[3](#page-31-8)[,7,](#page-31-9)[9](#page-31-10)[,26](#page-32-7)] are specially designed for patch classification, which are not directly applicable to WSI classification without a crucial patch aggregation process.

Despite the achievement in reducing data cost, performances of existing methods are obviously sacrificed using fewer WSIs. For example, the AUC of CLAM  $[19]$  $[19]$  dramatically drops to 57.65% from 97.45% as Fig. [1](#page-23-0) (a). In other words, it is a trade-off between few data and high performance. While extra information can compensate for the performance loss owing to the data scarcity. For

instance, leveraging weak labels [\[16](#page-32-8)] significantly enhances the model's capability beyond that of the unsupervised method [\[27\]](#page-32-9) for gland segmentation. Motivated by this, we aim to use fewer WSIs with mask labels to save data and achieve high performance at the same time. Importantly, many WSI-level labels cost more labeling efforts than a few patch-level masks (when mask labels  $\lesssim \frac{1}{30}$  WSI labels), indicating that our scheme saves both data and annotation costs.

Another motivation is that, parametric classifier (*e.g.*, FC layer) in MIL is prone to overfit on a few WSI examples or specific medical centers (see MIL methods in Table [3\)](#page-28-0). Thus, we expect a non-parametric classifier without finetuning on a few WSIs. For existing non-parametric methods, like KNN evaluation protocol for pretrained models [\[4](#page-31-4)], or Prototype [\[5\]](#page-31-7) and SimpleShot [\[24\]](#page-32-6) based on mask label, they use the global static feature as Fig. [1](#page-23-0) (b). Unfortunately, these methods discard rich information of local patches to obtain the global feature, resulting in suboptimal performance. Besides, some KNN based MIL methods for text [\[10](#page-31-11)], CT [\[25\]](#page-32-10) etc. are not designed for gigabyte WSI.

Based on the above motivations, we proposed the informative non-parametric classifier (INC) to simultaneously achieve few-shot and high performance LNM classification. Specifically, it maintains all deep features of local patches divided by mask label, then utilizes non-parametric similarity between the informative gallery WSI bags and the query bag to classify LNM. Experimentally, our method surpasses existing SoTA MIL methods across various settings. Under the same setting on CAMELYON16 [\[1\]](#page-31-0) dataset, the AUC improvements are nontrivial over 36.76%. Importantly, our method saves the labeling cost when applying much less data, with better performances. Furthermore, we also achieve outstanding performance when generalizing to multiple medical centers on CAMELYON17 [\[17](#page-32-0)], and the AUC on corrupted WSIs of CAMELYON16-C [\[28](#page-32-11)] even surpasses many-shot SoTA over 7.55%.

# 2 Methodology

The primary benefit of the INC lies in its ability to preserve rich local features, thereby avoiding information loss in previous parametric classifier methods such as MIL, or non-parametric methods that rely on mean feature values (*e.g.*, KNN and Prototype [\[5\]](#page-31-7)). In contrast to static classifiers with fixed parameters or global features, INC functions as a dynamic classifier by incorporating bags of local features. This unique approach guarantees the retention of the most comprehensive information when compared to other classifiers, enabling it to dynamically adapt to different medical centers or scanners without the risk of overfitting. Here, we firstly elaborate how we generate the classification logit based on similarity from bags of local features. Then we describe a further retrieval aggregation step, which considers multiple related logits for slide-level prediction.

## 2.1 Informative Similarity Logit for Non-parametric Classifier

The core step of the proposed INC is to produce a classification logit from bags of local features. This is accomplished by computing the similarity between informative gallery feature bags and query bags, taking into account both the cancer region and the normal region. In light of the fact that the similarity stems from informative local features, it is referred to as the informative similarity logit.

To be specific, we firstly extract the patch features via a self-supervised pretrained encoder  $f(\cdot)$ , which returns L2 normalized features. Then, we get the bag of local gallery features  $G \in \mathbb{R}^{N_g \times C}$  (support WSIs) and query features  $Q \in \mathbb{R}^{N_q \times C}$  (a test WSI) as:

<span id="page-25-1"></span>
$$
\mathbf{G} = f(\mathbf{X}_g), \quad \mathbf{Q} = f(\mathbf{X}_q), \tag{1}
$$

where  $\mathbf{X}_q$ ,  $\mathbf{X}_q$  indicate gallery and query patch instances, at instances quantity of  $N_g$  and  $N_q$ , respectively. Then the target similarity matrix  $S \in \mathbb{R}^{N_g \times N_q}$ between gallery bag and query bag are calculated by matrix multiplication as:

$$
S = G Q^{\top}.
$$
 (2)

The above cosine similarity matrix contains rich information between gallery feature bag and query bag. To get the prediction score, we need logits for each query via reducing  $S$  along the gallery dimension  $(N_q)$ . In the step, we firstly divide *S* into a set of positive similarity matrix  $S^+ \in \mathbb{R}^{N_p \times N_q}$  and negative similarity matrix  $S^- \in \mathbb{R}^{N_n \times N_q}$  as:

$$
S = \{S^+, S^-\}, \quad s.t. \quad S^+ = S_{idx(m=1)}, \quad S^- = S_{idx(m=0)}, \tag{3}
$$

where the mask label  $\mathbf{m} \in \mathbb{R}^{N_g}$  is used to divide positive gallery related similarities  $S^+ = S_{idx(m=1)}$  and negative similarities  $S^- = S_{idx(m=0)}$  via indexing on the gallery dimension. Note,  $N_p$ ,  $N_n$  indicates the number of cancer patches and normal patches, respectively.

To dynamically utilize suitable similarities for each query, we take the top-k  $S^{\dagger}$  similarities of  $S^{\dagger}, S^{\dagger} \in \mathbb{R}^{N_k \times N_q}, \hat{S}^{\dagger} \in \mathbb{R}^{N_k \times N_q}$  at top-k size  $N_k$ . Then we generate the informative similarity logit  $\mathbf{s} \in \mathbb{R}^{N_q}$  for each query as:

<span id="page-25-0"></span>
$$
\mathbf{s} = \frac{\sum \hat{\boldsymbol{S}}^{+} - \sum \hat{\boldsymbol{S}}^{-}}{N_k},\tag{4}
$$

where top related cancer gallery patches and normal patches are dynamically used as the non-parametric classifier (Fig. [2\)](#page-26-0).

## 2.2 Retrieval Aggregation of Informative Non-parametric Classifier

To get the slide-level prediction from the patch logit in Eq. [4,](#page-25-0) we need a further step to aggregate the patch logits. Our solution is to select the highest logit in Eq. [4,](#page-25-0) then conduct retrieval within the bag of query features to aggregate discriminative and related logits. Let  $Q_1$  to be the query feature at the highest similarity logit, namely  $idx(max(\mathbf{s}))$  using max and indexing operation, respectively. Then, we can retrieve the index array **r** of related patch queries as:

$$
\mathbf{r} = i dx (\mathbf{Q}_1 \mathbf{Q}^\top > t),\tag{5}
$$

Image /page/26/Figure/1 description: This diagram illustrates a method for Whole Slide Image (WSI) prediction. It begins with a few gallery WSIs and a query WSI, which are processed by a self-supervised pre-trained encoder to extract local features. These features are then used to compute similarity logits, top positive similarities, and top negative similarities. The process involves calculating informative similarity and using mask labels. The query feature at the highest logit is identified, and related queries are indexed via these logits. Finally, related logits and aggregation weights are used to produce the WSI prediction.

<span id="page-26-0"></span>Fig. 2. Data flow of the proposed INC. Note,  $\times$  indicates matrix multiplication, and each symbol can be found from Eq. [1](#page-25-1) to Eq. [7.](#page-26-1)

where cosine similarities  $Q_1 Q^\top$  higher than a threshold t are indexed into **r**.

To aggregate the related logits according to their importance, we need an aggregation weight **w** for each logit according to the similarity  $\hat{\mathbf{s}}$  from **r**. Then we multiply it with a softmax temperature  $\tau$  before the softmax operation  $\sigma(\cdot)$ . As above descriptions, we have the formula of aggregation weights:

$$
\mathbf{w} = \sigma(\hat{\mathbf{s}} \cdot \tau) \quad s.t. \quad i dx(\hat{\mathbf{s}}) = \mathbf{r}.
$$
 (6)

In the last, we multiply the related logits  $\hat{\mathbf{s}}$  with the transposed weight  $\mathbf{w}^{\top}$ to realize the weighted sum. Until here, we get the slide-level prediction  $p$  from the aggregation of related query logits as:

<span id="page-26-1"></span>
$$
p = \hat{\mathbf{s}} \mathbf{w}^{\top}.\tag{7}
$$

# 3 Experiments

**Dataset.** The main dataset is CAMELYON16 [\[1\]](#page-31-0), consisting of 399 H&E stained WSIs for breast cancer LNM classification. The train-val set has 270 slides, and there are 129 test slides. To test the generalization, we introduced CAME-LYON17 [\[17\]](#page-32-0) and CAMELYON16-C [\[28\]](#page-32-11). Specifically, CAMELYON17 contains 5 medical centers, where 500 labeled WSIs (1 invalid) are used to test. Besides, CAMELYON16-C is an augmented dataset simulating WSI scanning corruption from CAMELYON16. Following Li et al. [\[15](#page-32-3)], 243 WSIs (9:1) of CAMELYON16 are used to train many-shot baselines. Besides, CAMELYON17 (499 WSIs) and CAMELYON16-C (129 test WSIs) are used to test only, using models trained from CAMELYON16 to evaluate generalization as Li et al. [\[15\]](#page-32-3).

**Implementation and Evaluation.** We use a self-supervised pretrained model [\[13](#page-31-5)] based on backbone ViT-S/8 [\[8\]](#page-31-12) as the image encoder. The patch size is 256 at

### 114 Y. Li et al.

<span id="page-27-0"></span>**Table 1.** Few-shot results on CAMELYON16, CAMELYON17 [\[17](#page-32-0)] and CAMEL YON16-C [\[28\]](#page-32-11), using 8 WSIs in total. Note, all the methods are implemented in the same settings for fair comparison, except results with *†* based on CLIP [\[21\]](#page-32-12). Besides, <sup>∗</sup> indicates the usage of mask labels as ours, where SimpleShot [\[24](#page-32-6)] is a general few-shot method, combined with Prototype [\[5](#page-31-7)].

| Method                                                                                                                                 | CAMELYON16             | CAMELYON17                                   |                                                                                                                                                                                   | CAMELYON16-C           |  |  |
|----------------------------------------------------------------------------------------------------------------------------------------|------------------------|----------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|--|--|
|                                                                                                                                        | AUC $(\%)$ Acc. $(\%)$ | $\vert \text{AUC}(\%) \vert \text{Acc.}(\%)$ |                                                                                                                                                                                   | AUC $(\%)$ Acc. $(\%)$ |  |  |
| $\uparrow$ TOP $\left[20\right]$                                                                                                       | 75.41                  |                                              |                                                                                                                                                                                   |                        |  |  |
| KNN-mean                                                                                                                               |                        |                                              | $58.12_{\pm 8.02}$ $41.09_{\pm 2.45}$ $45.80_{\pm 2.75}$ $37.72_{\pm 2.20}$ $57.16_{\pm 7.94}$ $40.62_{\pm 3.42}$                                                                 |                        |  |  |
| KNN-max                                                                                                                                |                        |                                              | $\left[59.45_{\pm 8.01}\right]\left[40.93_{\pm 1.92}\right]\left[50.91_{\pm 7.23}\right]\left[41.84_{\pm 10.74}\right]\left[59.77_{\pm 7.96}\right]\left[43.26_{\pm 7.09}\right]$ |                        |  |  |
| $CLAM-MB$ [19]                                                                                                                         |                        |                                              | $57.65_{\pm 6.79}$ 48.68 $_{\pm 6.79}$ $52.49_{\pm 6.42}$ 49.26 $_{\pm 9.87}$ 57.12 $_{\pm 3.58}$ 48.53 $_{\pm 7.86}$                                                             |                        |  |  |
| $*$ Prototype [5]                                                                                                                      |                        |                                              | $ 48.83_{\pm 2.88} 49.77_{\pm 2.57} 57.50_{\pm 1.52} 64.13_{\pm 0.71} 48.69_{\pm 2.88} 59.53_{\pm 2.10} $                                                                         |                        |  |  |
| *SimpleShot [24] 61.95 <sub>±3.89</sub> $59.22_{\pm 2.38}$ $71.58_{\pm 2.50}$ $69.98_{\pm 1.69}$ $62.40_{\pm 2.75}$ $66.67_{\pm 2.59}$ |                        |                                              |                                                                                                                                                                                   |                        |  |  |
| INC (ours)                                                                                                                             |                        |                                              | $\ket{98.71_{\pm 0.22} 95.35_{\pm 0.49} 89.42_{\pm 0.81} 85.37_{\pm 1.76} 95.27_{\pm 0.76} 85.89_{\pm 2.61}}$                                                                     |                        |  |  |

<span id="page-27-1"></span>**Table 2.** Few-shot results on CAMELYON16 [\[1\]](#page-31-0) (8 WSIs in Table [1\)](#page-27-0). Note, 2 WSIs is also 1-shot (1 WSI per class) for TOP [\[20](#page-32-5)], KNN, CLAM-MB [\[19](#page-32-1)], while others use 2 positive WSIs. All the baselines use the same self-supervised encoder [\[13](#page-31-5)], but *†* indicates the CLIP [\[21\]](#page-32-12) encoder, and <sup>∗</sup> indicates the usage of mask labels as ours.

| Method                                                                                                                                                           | 1 WSI  |                                                                                                                                              | 2 WSIs                         |          | 4 WSIs                                                                     |          |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------|----------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|----------|----------------------------------------------------------------------------|----------|
|                                                                                                                                                                  | AUC(%) | Acc. (%)                                                                                                                                     | $\vert$ AUC $\left(\% \right)$ | Acc. (%) | $\vert$ AUC $\left( \% \right)$                                            | Acc. (%) |
| $\uparrow$ TOP $[20]$                                                                                                                                            |        |                                                                                                                                              | 67.14                          |          | 69.67                                                                      |          |
| KNN-mean                                                                                                                                                         |        |                                                                                                                                              |                                |          | $ 50.49_{\pm 10.86} 39.22_{\pm 3.52} 53.71_{\pm 13.17} 43.10_{\pm 3.05} $  |          |
| KNN-max                                                                                                                                                          |        |                                                                                                                                              |                                |          | $ 45.03_{\pm 11.75} 39.69_{\pm 6.19} 55.01_{\pm 15.94} 46.82_{\pm 12.20} $ |          |
| CLAM-MB $[19]$ -                                                                                                                                                 |        |                                                                                                                                              |                                |          | $ 50.19_{\pm 12.75} 43.88_{\pm 7.99} 52.89_{\pm 9.33} 45.74_{\pm 8.26} $   |          |
| *Prototype [5] [52.61 <sub>±7.77</sub> [49.30 <sub>±7.94</sub> [44.15 <sub>±4.33</sub> [42.95 <sub>±6.37</sub> [43.43 <sub>±8.80</sub> [40.16 <sub>±7.38</sub> ] |        |                                                                                                                                              |                                |          |                                                                            |          |
| *SimpleShot $[24]$ $\frac{59.82}_{+9.96}$ $\frac{51.16}{+10.53}$ $57.23_{+8.56}$ $\frac{51.32}{+7.28}$ $55.74_{+13.36}$ $\frac{49.77}{+10.12}$                   |        |                                                                                                                                              |                                |          |                                                                            |          |
| <b>INC</b>                                                                                                                                                       |        | $\ket{87.33_{\pm 8.72} \vert 82.48_{\pm 10.20} \vert 93.24_{\pm 7.01} \vert 88.68_{\pm 4.00} \vert 94.15_{\pm 1.18} \vert 88.84_{\pm 1.81}}$ |                                |          |                                                                            |          |

 $\times$ 20 magnification. To fit the input size of ViT-S/8 [\[8](#page-31-12)], we apply five-crop at side 224 without other augmentation to extract features and use the mean feature as the local feature. *For a fair comparison, we apply this backbone, weights, and crop strategy to all implemented baselines*. The few-shot WSI numbers are 1, 2, 4, 8 (default shot). All the hyperparameters are gird searched on the validation set with detailed experiments in Supplementary B. For baselines, we studied the implementations in Supplementary A for fair and strong baselines. For stable results, we randomly split the dataset 5 times (fixed random seed at 1024 for good reproducibility), and report the average Area Under the Curve (AUC) and Accuracy (ACC) with corresponding standard deviation  $(\pm)$ .

**Remarkable Improvements in Few-Shot LNM Classification.** We compare our results with previous SoTA methods as Table [1](#page-27-0) in fair implementation, where the encoder, crop strategy, and data split are the same. The results suggest our INC has surpassed SoTA few-shot baselines at large margins. Specifically, on CAMELYON16 [\[1](#page-31-0)], our AUC is  $98.71_{+0.22}$  which is higher than the baseline in the same setting (SimpleShot  $[24]$ ) by 36.76%, and the gain of accuracy is 36.13%. For the results of TOP [\[20\]](#page-32-5) (only single data split), the improvement is also remarkable at 23.30%. When generalized to CAMELYON17 [\[17\]](#page-32-0) and CAMELYON16-C [\[28\]](#page-32-11), the proposed INC still works best, where the AUC gain is 17.84% and 32.87% on these datasets, respectively.

Besides the results of 8 WSIs in Table [1,](#page-27-0) we list more results using fewer WSIs in Table [2](#page-27-1) on CAMELYON16 [\[1\]](#page-31-0). From these tables, we find INC is significantly higher than existing methods. Specifically, it surpasses the second by 27.51%, 26.10%, 24.48%, 23.30% on AUC for 1, 2, 4, 8 WSIs, respectively, and the corresponding accuracy improvements are 31.32%, 37.36%, 39.07%, 19.22%, respectively. These remarkable results can be attributed to our effective utilization of crucial local data (patch) and task-specific designs, which suit WSI much better than existing methods borrowed from the general CV. It suggests that maintaining rich local information is necessary, instead of generating global information like Prototype [\[5](#page-31-7)] and SimpleShot [\[24\]](#page-32-6). Also, it's more suitable to used non-parametric classifier instead of parametric classifier (CLAM-MB [\[19\]](#page-32-1)) in few-shot setting.

<span id="page-28-0"></span>**Table 3.** Our few-shot INC with 8 WSIs meets high performance on CAMELYON16, CAMELYON17 [\[17\]](#page-32-0) and CAMELYON16-C [\[28\]](#page-32-11), even beyond SoTA many-shot methods using 243 WSIs at fair implementation. Notably, our overall labeling cost is comparable or less than these many-shot methods. Note, signs *‡* and *†* indicate referenced results with partial results, and *†* means single test without repeats.

| Method                            | CAMELYON16             |                        | CAMELYON17             |                        | CAMELYON16-C           |                         |
|-----------------------------------|------------------------|------------------------|------------------------|------------------------|------------------------|-------------------------|
|                                   | AUC (%)                | Acc. (%)               | AUC (%)                | Acc. (%)               | AUC (%)                | Acc. (%)                |
| many-shot                         |                        |                        |                        |                        |                        |                         |
| $	ext{	extdagger}d$ MIL-Trans [6] | 91.69                  | 81.40                  |                        |                        |                        |                         |
| $	ext{	extuparrow}UNI$ [5]        | 97.5                   |                        |                        |                        |                        |                         |
| $	ext{	extddagger}MHIM-MIL$ [23]  | 96.49 $_{	ext{±}0.65}$ | 92.48 $_{	ext{±}0.35}$ |                        |                        |                        |                         |
| $	ext{	extddagger}Trans.+FT$ [15] | 96.7 $_{	ext{±}0.3}$   |                        | 71.7                   |                        | 85.7                   |                         |
| MIL-mean                          | 59.30 $_{	ext{±}3.35}$ | 64.81 $_{	ext{±}3.27}$ | 66.63 $_{	ext{±}1.84}$ | 66.21 $_{	ext{±}2.81}$ | 57.87 $_{	ext{±}6.17}$ | 55.50 $_{	ext{±}3.66}$  |
| MIL-max                           | 89.36 $_{	ext{±}0.44}$ | 86.51 $_{	ext{±}0.46}$ | 88.07 $_{	ext{±}0.40}$ | 77.24 $_{	ext{±}1.85}$ | 73.31 $_{	ext{±}2.37}$ | 64.65 $_{	ext{±}1.46}$  |
| MIL-top5 [19]                     | 96.16 $_{	ext{±}2.37}$ | 91.16 $_{	ext{±}1.44}$ | 60.04 $_{	ext{±}3.52}$ | 57.07 $_{	ext{±}3.39}$ | 85.02 $_{	ext{±}3.16}$ | 61.50 $_{	ext{±}10.04}$ |
| ABMIL [12]                        | 92.02 $_{	ext{±}0.41}$ | 90.17 $_{	ext{±}0.22}$ | 83.34 $_{	ext{±}1.24}$ | 79.93 $_{	ext{±}1.83}$ | 84.59 $_{	ext{±}2.40}$ | 82.08 $_{	ext{±}2.20}$  |
| CLAM-SB [19]                      | 97.25 $_{	ext{±}0.16}$ | 92.56 $_{	ext{±}0.62}$ | 87.03 $_{	ext{±}2.36}$ | 79.48 $_{	ext{±}3.99}$ | 86.42 $_{	ext{±}2.46}$ | 74.81 $_{	ext{±}1.61}$  |
| CLAM-MB [19]                      | 97.45 $_{	ext{±}1.81}$ | 94.57 $_{	ext{±}2.21}$ | 85.00 $_{	ext{±}1.23}$ | 76.07 $_{	ext{±}2.37}$ | 87.72 $_{	ext{±}2.02}$ | 75.35 $_{	ext{±}4.23}$  |
| TransMIL [22]                     | 95.77 $_{	ext{±}2.24}$ | 90.39 $_{	ext{±}2.11}$ | 88.02 $_{	ext{±}1.63}$ | 81.35 $_{	ext{±}2.74}$ | 87.31 $_{	ext{±}2.83}$ | 72.75 $_{	ext{±}2.56}$  |
| few-shot                          |                        |                        |                        |                        |                        |                         |
| INC (ours)                        | 98.71 $_{	ext{±}0.22}$ | 95.35 $_{	ext{±}0.49}$ | 89.42 $_{	ext{±}0.81}$ | 85.37 $_{	ext{±}1.76}$ | 95.27 $_{	ext{±}0.76}$ | 85.89 $_{	ext{±}2.61}$  |

**Few-Shot INC Meets High Performance Beyond Many-Shot Methods.** Besides great successes in few-shot setting, our INC even shows higher results  $(97.5\%$  vs. our  $98.7\%)$  than many-shot methods  $(243 \text{ WSIs} \text{ vs. our } 8 \text{ WSIs})$  as Table [3.](#page-28-0) Notably, the labeling cost is also reduced when applying 8 or less masks (30 times labeling cost for quantity  $\leq \frac{8}{243}$ ). These results indicate our method takes advantages in both performance and labeling and data cost.

<span id="page-29-0"></span>**Table 4.** Ablation study on the CAMELYON16 [\[1\]](#page-31-0) using 8 WSIs showed that the mask label (SimpleShot) only slightly improves the baseline (KNN). However, the core improvements come from informative local features for similarity logit, and the retrieval aggregation further enhances the performance.

| Setting         | Mask Label Informative Similarity Logit Retrieval Aggregation $AUC$ (%) |                      |
|-----------------|-------------------------------------------------------------------------|----------------------|
| KNN-mean        |                                                                         | $ 58.12_{\pm 8.02} $ |
| SimpleShot      |                                                                         | $ 61.95_{\pm 3.89} $ |
| Ours Eqs. $1-4$ |                                                                         | $ 96.43_{\pm 0.49} $ |
| Ours Eqs. $1-7$ |                                                                         | $ 98.71_{0.22} $     |

In real practice, the WSI scanners are different across hospitals with variations. Thus, the model train from one hospital often meets obvious performance drops when applied to other medical centers. For example, the parametric MIL method, CLAM-MB [\[19\]](#page-32-1) produces good results on CAMELYON16 at AUC 97.45%, but it turns to 85% on CAMELYON17 and 87.72% on CAMELYON16- C, where the decreases are 12.45% and 9.73%, respectively. In comparison, our INC shows stronger generalization ability at AUC 89.42% and 95.27%, which greatly exceeds previous methods. Specifically, INC surpasses the best manyshot method by 7.55% on CAMELYON16-C. Notably, non-parametric classifiers (few-shot baselines) present similar results for different datasets, which support our motivation of non-parametric classifiers fine.

**Ablation Study.** In Table [4,](#page-29-0) we find that the usage of mask label in SimpleShot can only improve the KNN baseline by 3.83% in AUC only. While our informative similarity logit improves the baseline by 40.59%. It suggests that keeping rich local information is very crucial to make good use of mask label in few-shot setting. Besides, the retrieval aggregation can further enhance the results at a high level, which demonstrates the significant effectiveness of the proposed INC.

**Visualization Results.** We further depict the visualizations in Fig. [3](#page-30-0) via the query logits ˆ**s** in Eq. [4.](#page-25-0) Since KNN methods use global features without local information, we compare our method with Prototype and SimpleShot. We find our INC obviously shows better results than other baselines. Specifically, baselines predict more false negatives. Besides, we draw retrieval processes in Supplementary C to show how INC works.

Image /page/30/Picture/1 description: This image displays a comparison of different methods for analyzing biological samples, likely cells, based on their visual characteristics. The image is organized into rows and columns. The first column, labeled 'GT', shows the ground truth images, which are stained biological samples. The subsequent columns, labeled 'ProtoType', 'SimpleShot', and 'INC (ours)', show heatmaps or saliency maps generated by different analytical methods. These heatmaps highlight areas of interest within the samples, with colors ranging from blue (low activation) to red (high activation). The rows are categorized as 'pos' (positive) and 'neg' (negative), presumably indicating the presence or absence of certain features or conditions. The 'pos' rows show samples with distinct features, and the heatmaps in the corresponding columns highlight these features. The 'neg' rows show samples that lack these features, and the heatmaps generally show less concentrated or absent areas of high activation. White bounding boxes are used in some of the 'ProtoType' and 'SimpleShot' columns to indicate detected regions of interest.

**Fig. 3.** Visualizations on CAMELYON16 datasets. Ground-truth tumors are circled in yellow, predicted LNM regions are red, and false predictions are noted by white boxes. (Color figure online)

<span id="page-30-0"></span>

# 4 Conclusion

In summary, this study presents a novel, data-efficient, and effective informative non-parametric (INC) classifier for the classification of lymph node metastasis (LNM). In contrast to existing many-shot methods that heavily depend on large datasets, or few-shot methods that seriously compromise performance, INC successes to make few-shot LNM classification meet high performance, even beyond existing SoTA many-shot methods. Furthermore, the INC does not require fine-tuning and avoids overfitting on specific medical centers or scanners, with less labeling cost using only a few masks. Comprehensive experimental results demonstrate that the INC attains the new SoTA in LNM classification, meanwhile offering several notable advantages.

**Acknowledgement.** This work was supported in part by grants from the National Natural Science Foundation of China under Grant No. 62306254, grants from Hong Kong Innovation and Technology Fund under Projects ITS/030/21, grants from the Foshan HKUST Projects under Grants FSUST21-HKUST10E and FSUST21- HKUST11E and Project of Hetao Shenzhen-Hong Kong Science and Technology Innovation Cooperation Zone (HZQB-KCZYB-2020083).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-31-0"></span>1. Bejnordi, B.E., Veta, M., Van Diest, P.J., Van Ginneken, B., Karssemeijer, N., Litjens, G., Van Der Laak, J.A., Hermsen, M., Manson, Q.F., Balkenhol, M., et al.: Diagnostic assessment of deep learning algorithms for detection of lymph node metastases in women with breast cancer. Jama **318**(22), 2199–2210 (2017)
- <span id="page-31-6"></span>2. Campanella, G., Hanna, M.G., Geneslaw, L., Miraflor, A., Werneck Krauss Silva, V., Busam, K.J., Brogi, E., Reuter, V.E., Klimstra, D.S., Fuchs, T.J.: Clinicalgrade computational pathology using weakly supervised deep learning on whole slide images. Nature medicine **25**(8), 1301–1309 (2019)
- <span id="page-31-8"></span>3. Chen, J., Jiao, J., He, S., Han, G., Qin, J.: Few-shot breast cancer metastases classification via unsupervised cell ranking. IEEE/ACM transactions on computational biology and bioinformatics **18**(5), 1914–1923 (2019)
- <span id="page-31-4"></span>4. Chen, R.J., Chen, C., Li, Y., Chen, T.Y., Trister, A.D., Krishnan, R.G., Mahmood, F.: Scaling vision transformers to gigapixel images via hierarchical self-supervised learning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 16144–16155 (2022)
- <span id="page-31-7"></span>5. Chen, R.J., Ding, T., Lu, M.Y., Williamson, D.F., Jaume, G., Chen, B., Zhang, A., Shao, D., Song, A.H., Shaban, M., et al.: A general-purpose self-supervised model for computational pathology. arXiv preprint [arXiv:2308.15474](http://arxiv.org/abs/2308.15474) (2023)
- <span id="page-31-13"></span>6. Chen, Y., Shao, Z., Bian, H., Fang, Z., Wang, Y., Cai, Y., Wang, H., Liu, G., Li, X., Zhang, Y.: dmil-transformer: Multiple instance learning via integrating morphological and spatial information for lymph node metastasis classification. IEEE Journal of Biomedical and Health Informatics (2023)
- <span id="page-31-9"></span>7. Deuschel, J., Firmbach, D., Geppert, C.I., Eckstein, M., Hartmann, A., Bruns, V., Kuritcyn, P., Dexl, J., Hartmann, D., Perrin, D., et al.: Multi-prototype fewshot learning in histopathology. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 620–628 (2021)
- <span id="page-31-12"></span>8. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-31-10"></span>9. He, Y., Li, X.: Whole-slide-imaging cancer metastases detection and localization with limited tumorous data. arXiv preprint  $\frac{\text{arXiv:}2303.10342}{2023}$
- <span id="page-31-11"></span>10. Hellman, S., Murray, W.R., Wiemerslage, A., Rosenstein, M., Foltz, P., Becker, L., Derr, M.: Multiple instance learning for content feedback localization without annotation. In: Proceedings of the Fifteenth Workshop on Innovative Use of NLP for Building Educational Applications. pp. 30–40 (2020)
- <span id="page-31-1"></span>11. Huang, S.C., Chen, C.C., Lan, J., Hsieh, T.Y., Chuang, H.C., Chien, M.Y., Ou, T.S., Chen, K.H., Wu, R.C., Liu, Y.J., et al.: Deep neural network trained on gigapixel images improves lymph node metastasis detection in clinical settings. Nature Communications **13**(1), 3347 (2022)
- <span id="page-31-2"></span>12. Ilse, M., Tomczak, J., Welling, M.: Attention-based deep multiple instance learning. In: International conference on machine learning. pp. 2127–2136. PMLR (2018)
- <span id="page-31-5"></span>13. Kang, M., Song, H., Park, S., Yoo, D., Pereira, S.: Benchmarking self-supervised learning on diverse pathology datasets. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3344–3354 (2023)
- <span id="page-31-3"></span>14. Li, B., Li, Y., Eliceiri, K.W.: Dual-stream multiple instance learning network for whole slide image classification with self-supervised contrastive learning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 14318–14328 (June 2021)

- <span id="page-32-3"></span>15. Li, H., Zhu, C., Zhang, Y., Sun, Y., Shui, Z., Kuang, W., Zheng, S., Yang, L.: Taskspecific fine-tuning via variational information bottleneck for weakly-supervised pathology whole slide image classification. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7454–7463 (2023)
- <span id="page-32-8"></span>16. Li, Y., Yu, Y., Zou, Y., Xiang, T., Li, X.: Online easy example mining for weaklysupervised gland segmentation from histology images. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 578–587. Springer (2022)
- <span id="page-32-0"></span>17. Litjens, G., Bandi, P., Ehteshami Bejnordi, B., Geessink, O., Balkenhol, M., Bult, P., Halilovic, A., Hermsen, M., van de Loo, R., Vogels, R., et al.: 1399 h&e-stained sentinel lymph node sections of breast cancer patients: the camelyon dataset. Giga-Science **7**(6), giy065 (2018)
- <span id="page-32-4"></span>18. Lu, M.Y., Chen, B., Zhang, A., Williamson, D.F., Chen, R.J., Ding, T., Le, L.P., Chuang, Y.S., Mahmood, F.: Visual language pretrained multiple instance zeroshot transfer for histopathology images. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 19764–19775 (2023)
- <span id="page-32-1"></span>19. Lu, M.Y., Williamson, D.F., Chen, T.Y., Chen, R.J., Barbieri, M., Mahmood, F.: Data-efficient and weakly supervised computational pathology on whole-slide images. Nature biomedical engineering **5**(6), 555–570 (2021)
- <span id="page-32-5"></span>20. Qu, L., Fu, K., Wang, M., Song, Z., et al.: The rise of ai language pathologists: Exploring two-level prompt learning for few-shot weakly-supervised whole slide image classification. Advances in Neural Information Processing Systems **36** (2024)
- <span id="page-32-12"></span>21. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-32-2"></span>22. Shao, Z., Bian, H., Chen, Y., Wang, Y., Zhang, J., Ji, X., et al.: Transmil: Transformer based correlated multiple instance learning for whole slide image classification. Advances in neural information processing systems **34**, 2136–2147 (2021)
- <span id="page-32-13"></span>23. Tang, W., Huang, S., Zhang, X., Zhou, F., Zhang, Y., Liu, B.: Multiple instance learning framework with masked hard instance mining for whole slide image classification. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 4078–4087 (2023)
- <span id="page-32-6"></span>24. Wang, Y., Chao, W.L., Weinberger, K.Q., Van Der Maaten, L.: Simpleshot: Revisiting nearest-neighbor classification for few-shot learning. arXiv preprint [arXiv:1911.04623](http://arxiv.org/abs/1911.04623) (2019)
- <span id="page-32-10"></span>25. Xu, C., Qi, S., Feng, J., Xia, S., Kang, Y., Yao, Y., Qian, W.: Dct-mil: deep cnn transferred multiple instance learning for copd identification using ct images. Physics in Medicine & Biology **65**(14), 145011 (2020)
- <span id="page-32-7"></span>26. Yang, J., Chen, H., Yan, J., Chen, X., Yao, J.: Towards better understanding and better generalization of few-shot classification in histology images with contrastive learning. arXiv preprint [arXiv:2202.09059](http://arxiv.org/abs/2202.09059) (2022)
- <span id="page-32-9"></span>27. Zhang, Q., Li, Y., Xue, C., Li, X.: Morphology-inspired unsupervised gland segmentation via selective semantic grouping. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 281–291. Springer (2023)
- <span id="page-32-11"></span>28. Zhang, Y., Sun, Y., Li, H., Zheng, S., Zhu, C., Yang, L.: Benchmarking the robustness of deep neural networks to common corruptions in digital pathology. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 242–252. Springer (2022)

Image /page/33/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a gray ribbon or bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# Fine-Grained Prompt Tuning: A Parameter and Memory Efficient Transfer Learning Method for High-Resolution Medical Image Classification

Yijin Huang<sup>1,2</sup>, Pujin Cheng<sup>1,3,4</sup>, Roger Tam<sup>2( $\boxtimes$ )</sup>, and Xiaoying Tang<sup>1,4( $\boxtimes$ )</sup>

<sup>1</sup> Department of Electronic and Electrical Engineering, Southern University of Science and Technology, Shenzhen, China

<EMAIL>

<sup>2</sup> School of Biomedical Engineering, The University of British Columbia,

Vancouver, Canada

<EMAIL>

<sup>3</sup> Department of Electrical and Electronic Engineering, The University of Hong Kong, Hong Kong, China

<sup>4</sup> Southern University of Science and Technology, Jiaxing Research Institute, Jiaxing, China

**Abstract.** Parameter-efficient transfer learning (PETL) is proposed as a cost-effective way to transfer pre-trained models to downstream tasks, avoiding the high cost of updating entire large-scale pre-trained models (LPMs). In this work, we present Fine-grained Prompt Tuning (FPT), a novel PETL method for medical image classification. FPT significantly reduces memory consumption compared to other PETL methods, especially in high-resolution input contexts. To achieve this, we first freeze the weights of the LPM and construct a learnable lightweight side network. The frozen LPM takes high-resolution images as input to extract fine-grained features, while the side network is fed low-resolution images to reduce memory usage. To allow the side network to access pre-trained knowledge, we introduce fine-grained prompts that summarize information from the LPM through a fusion module. Important tokens selection and preloading techniques are employed to further reduce training cost and memory requirements. We evaluate FPT on four medical datasets with varying sizes, modalities, and complexities. Experimental results demonstrate that FPT achieves comparable performance to fine-tuning the entire LPM while using only 1.8% of the learnable parameters and 13% of the memory costs of an encoder ViT-B model with a 512 *×* 512 input resolution.

**Keywords:** Parameter-efficient transfer learning · Memory-efficient transfer learning · High-resolution medical image classification

# 1 Introduction

By utilizing the technique of fine-tuning [\[26\]](#page-43-0), pre-trained models can be effectively adapted to specific downstream tasks by initializing the task-specific model with the weights from the pre-trained model. Recently, the remarkable achievements of large-scale pre-trained models (LPMs) [\[5](#page-41-0)[,14,](#page-42-0)[19](#page-42-1)[,20](#page-42-2)] further underscore the importance of this technique. However, as the model size grows rapidly, fine-tuning the parameters of an entire LPM has become very costly. To address this challenge, the concept of parameter-efficient transfer learning (PETL) [\[9](#page-42-3),[10,](#page-42-4)[13,](#page-42-5)[23](#page-42-6)[,27,](#page-43-1)[29\]](#page-43-2) has emerged, offering a strategic approach for transferring pre-trained models. PETL involves selectively updating a small subset of pre-trained parameters or introducing a modest number of additional parameters specific to new tasks, while keeping the majority of pre-trained parameters frozen.

PETL has successfully established its effectiveness in computer vision, but the field of medical image has not fully benefited from such advances yet [\[7\]](#page-41-1), mainly because of the domain gap between them. In natural images, the objects of interest typically occupy a large portion of the image and exhibit distinct characteristics. In contrast, diagnostic cues in medical images often occupy a small portion and are distributed throughout the entire image [\[12\]](#page-42-7). Providing such fine-grained information often requires the use of high-resolution input images [\[2](#page-41-2)[,11](#page-42-8),[30\]](#page-43-3). However, as shown in Fig. [1,](#page-34-0) this preference for high-resolution images comes at the cost of increased GPU memory consumption and training expenses.

In this work, we propose a novel parameter and memory efficient transfer learning method, namely Fine-grained Prompt Tuning (FPT). FPT aims to enhance the effectiveness of PETL specifically for medical images in highresolution input contexts by addressing two main challenges:

*1) How to efficiently extract fine-grained information from highresolution images?* Existing PETL methods involve training a subset of parameters within the large-scale pre-trained model (LPM) [\[9,](#page-42-3)[10](#page-42-4)]. In contrast, FPT utilizes a lightweight additive network inspired by the concept of a side network [\[23,](#page-42-6)[29](#page-43-2)]. This learnable side network is introduced outside the LPM, eliminating the need for back-propagation through the LPM. However, train-

Image /page/34/Figure/5 description: The image contains two plots. The left plot is a line graph showing GPU memory usage (MB) on the y-axis and input resolution on the x-axis. The x-axis has labels 224, 256, 384, and 512. There are five lines representing different methods: Baseline (orange squares), LoRA (green triangles), BitFit (purple inverted triangles), Adapter (blue diamonds), and FPT (ours) (blue circles). The FPT (ours) line shows consistently low memory usage across all resolutions, while the other methods show increasing memory usage with higher resolutions, with Baseline using the most memory. The right plot is a scatter plot showing Average AUC on the y-axis and # Learnable Parameters (%) on the x-axis. The x-axis is on a logarithmic scale with labels 1e-003, 1e-002, 1e-001, 1, 10, and 100. The points represent different methods: Linear (blue circle, low parameters, low AUC), Prompt tuning (blue circle, low parameters, moderate AUC), Adapter (blue circle, moderate parameters, moderate AUC), Attention tuning (blue circle, higher parameters, moderate AUC), BitFit (blue circle, higher parameters, higher AUC), and LoRA (blue circle, higher parameters, higher AUC). FPT (ours) is represented by a pink circle, positioned at the highest AUC and relatively low parameters. The size of the blue circles appears to vary, possibly indicating something not explicitly labeled.

sumption.

<span id="page-34-1"></span><span id="page-34-0"></span>**Fig. 1.** High-resolution comes at the **Fig. 2.** Our proposed FPT shows the best cost of heightened GPU memory con-trade-off between performance and efficiency. The size of the dots represents memory usage.

ing the side network can still be computationally expensive with high-resolution input images due to the long input sequence. FPT addresses this concern by strategically introducing asymmetric input resolution and employing important token selection to significantly reduce the length of the input sequence for the learnable network.

*2) How to effectively adapt pre-trained knowledge from LPMs?* LPMs are primarily pre-trained on natural image datasets like ImageNet [\[21\]](#page-42-9). To effectively adapt pre-trained knowledge from LPMs of domains outside of medical images, FPT introduces the concept of fine-grained prompts and a Finegrained Fusion Module (FFM) as bridging components. Fine-grained prompts are a small set of learnable embeddings that summarize pre-trained knowledge from the LPM through the FFM. These prompts are then prepended to the intermediate layers of the side network to convey fine-grained information by integrating them into the forward propagation.

Our main contributions are summarized as follows:

- 1. We present a novel PETL method, namely Fine-grained Prompt Tuning (FPT), for medical image classification in high-resolution contexts. Asymmetric input and important token selection are proposed to improve memory efficiency. Fine-grained prompts and fine-grained fusion module are introduced to adapt pre-trained knowledge effectively and efficiently. Our code is available [online.](https://github.com/YijinHuang/FPT)
- 2. To the best of our knowledge, this is the first work to enhance the efficiency of PETL in high-resolution input settings, which is particularly significant in the field of medical image analysis.
- 3. We introduce a new metric to evaluate the trade-off between performance and memory efficiency for transfer learning methods.
- 4. We conduct extensive experiments on four medical image datasets with different modalities. As shown in Fig. [2,](#page-34-1) FPT achieves the best trade-off between performance and parameter/memory efficiency.

# 2 Method

# 2.1 Side Tuning

As illustrated in part (a) of Fig. [3,](#page-36-0) the FPT framework consists of two networks: a frozen LPM M and a learnable side network S. Unlike other PETL methods that introduce additional learnable parameters within the LPM, in our approach, the entire LPM remains frozen while the side network is kept learnable and separate. We adopt a lightweight architecture design for the side network, which is a scaleddown variant of the LPM. The hidden dimensions of the side network are  $1/k$ times that of the LPM, where  $k$  represents a reduction factor. To leverage the pre-trained knowledge from the LPM, the side network reuses the intermediate features at each layer of the LPM. Specifically, given two models  $M$  and  $S$  with L layers, parameterized by  $\theta_M$  and  $\theta_S$  respectively, the intermediate activation

Image /page/36/Figure/1 description: The image illustrates a deep learning model architecture for fine-grained prompt tuning and a fine-grained fusion module. Part (a) shows a pre-trained model with frozen Transformer blocks processing high-resolution input. Fine-grained prompts are introduced and fused with features from multiple layers of the pre-trained model using Fusion Modules. These fused features are then fed into learnable Transformer blocks to produce an output. Part (b) details the Fine-grained Fusion Module, which takes fine-grained features (V and K) and fine-grained prompts (Q) as input. It computes an attention map using Softmax, scales it by 1/k, and combines it with the scaled fine-grained prompts (x k) to generate an output. The diagram also includes a legend indicating that blue boxes represent frozen components, green boxes represent learnable components, and orange boxes represent learnable Transformer blocks.

<span id="page-36-0"></span>**Fig. 3.** The proposed FPT framework.

 $z_{S,I}^l$  of layer l is obtained as follows:

$$
z_M^l = \theta_M^l(z_M^{l-1}),\tag{1}
$$

$$
z_S^l = \theta_S^l(\mathcal{F}(z_S^{l-1}, z_M^l)),\tag{2}
$$

where  $\mathcal F$  denotes the module that fuses features of M and S, and  $z_S^L$  is considered the final output of the framework. The use of the side network not only reduces the number of trainable parameters due to its lightweight design but also helps mitigate memory expenses during the training phase. As shown in part (a) of Fig. [4,](#page-37-0) the side network eliminates the need for resource-intensive backpropagation from the pre-trained model by excluding any learnable parameters in the forward pass of the heavy LPM model.

## 2.2 Asymmetric Input

Fine-grained information holds significant importance in the context of medical image analysis and is typically acquired by using high input resolutions. Although training with high-resolution inputs may be infeasible due to high memory consumption, using a LPM solely for inference with high-resolution images remains practical. Therefore, we propose an asymmetric input strategy within the FPT framework. Specifically, given a high-resolution image  $I$ , we simply resize it to obtain a low-resolution image I . Then, the frozen pre-trained model M is provided with the image I, while the learnable side network  $S$  is fed low-resolution image I . Thus, we have the intermediate activation of the side network  $z_{S,I'}^l = \theta_S^l(\mathcal{F}(z_{S,I'}^{l-1}, z_{M,I}^l)).$ 

## 2.3 Fine-Grained Prompts and Fusion Module

In this section, we introduce our proposed fusion module  $\mathcal{F}$ . As shown in the part (b) of Fig. [3,](#page-36-0) we utilize the cross-attention mechanism [\[1\]](#page-41-3) inside the FFM to fuse

Image /page/37/Figure/1 description: This image displays three diagrams illustrating different aspects of a neural network architecture. Diagram (a), titled "Gradient Flow," shows a network with multiple Transformer Blocks and Fusion Modules, indicating forward and gradient paths. Diagram (b), labeled "Important Token Selection," details a self-attention mechanism within a Transformer Block, highlighting the selection of top K tokens for a Fusion Module. Diagram (c), "Fine-grained Features Preloading," contrasts two scenarios: a "Frozen Pre-trained Model" processing "High Resolution w/o Data Augmentation" and a subsequent stage where "Important Tokens" are preloaded into a "Side Network" for processing "Low Resolution" data. A legend at the bottom clarifies that solid arrows represent forward passes and dashed arrows represent gradients.

<span id="page-37-0"></span>Fig. 4. Components of FPT: (a) The gradient flow of FPT. (b) Important tokens selection mechanism. (c) Fine-grained features preloading.

features from the LPM to the side network. In the context of cross-attention, we reuse the key and value from the self-attention layer of the pre-trained model M. Regarding the query, one approach is to directly reuse the query from the side network S. However, the cross-attention map can be large if the input sequence is long, leading to increased memory consumption. Therefore, we introduce a small set of learnable embeddings  $z_p$ , namely fine-grained prompts, into each layer of the fusion modules as the query. Unlike prompt tuning [\[13,](#page-42-5)[16\]](#page-42-10), which directly uses prompts as part of the input sequence, fine-grained prompts serve as a bridge linking the frozen LPM and the side network. These prompts are concatenated with the intermediate sequence of the side network to join the forward propagation after fusing pre-trained features from the LPM. They are then removed after the layer's forward processing. Specifically, the fusion module is processed as follows:

$$
\mathcal{F}(z_S, z_M) = [z_S, f_{out}(\text{CA}(z_p, z_M)) + z_p], \tag{3}
$$

$$
CA(z_p, z_M) = Attn(Q, K, V) = Attn(f_{in}(z_p), K_M(z_M), V_M(z_M)),
$$
 (4)

where the notations for the input source and the index of the layer are omitted in the formula for simplification. Here,  $CA(\cdot)$  and  $Attn(\cdot)$  denote the cross-attention module and attention function  $[25]$  $[25]$  respectively,  $[\cdot, \cdot]$  denotes the concatenation operation, f*in* and f*out* denote linear layers that align the hidden dimension between the features. The terms  $K_{\text{M}}$  and  $V_{\text{M}}$  refer to the key and value mapping matrices within the corresponding self-attention layer of M respectively.

## 2.4 Important Token Selection

Building on knowledge in the medical image domain, where images of the same modality often display similar anatomical structures and the objects of interest typically occupy a small proportion of the entire image, we propose a method to further reduce memory consumption, as shown in part (b) of Fig. [4.](#page-37-0) Specifically, we introduce important token selection, which selects the top  $m\%$  tokens with the highest average scores on the self-attention map, considering them as important

tokens. Only the features associated with these important tokens are passed to the FFM. This approach significantly reduces the overhead introduced by high-resolution inputs while preserving essential fine-grained information.

## 2.5 Fine-Grained Features Preloading

To further accelerate training procedure, we opt not to use any data augmentation on the input of the frozen LPM. This choice ensures that the intermediate features from the LPM associated with an image remain consistent throughout training. This approach allows us to pre-store these features before training, leading to significant reductions in training costs. Note that while the preloaded features remain fixed, the data augmentation applied to the input of the side network maintains the diversity of training samples.

# 3 Experiments

## 3.1 Datasets

We evaluate FPT on four medical datasets with different modalities, including fundus images (messidor-2 [\[4\]](#page-41-4)), dermoscopic images (ISIC 2018 [\[3](#page-41-5)]), mammography (DDSM [\[15](#page-42-12)]), and chest X-ray (COVID [\[22](#page-42-13)]). The dataset sizes range from 1,748 to 11,527 samples, with classification categories varying from 3 to 7. We use official dataset splits when available. Otherwise, we employ a random partition of  $70\%/10\%/20\%$  for training, validation, and testing, respectively.

## 3.2 Training and Evaluation Setup

**Experiment Setup.** In this study, we utilize a popular variant of Vision Transformers (ViT) [\[6\]](#page-41-6), specifically ViT-B(ase), which is pre-trained on ImageNet-21K [\[21](#page-42-9)]. All methods are fine-tuned for 20 epochs with a mini-batch size of 16. We use the AdamW optimizer [\[18](#page-42-14)] with cross-entropy as the loss function for all datasets. To ensure fair comparisons, we conduct a grid search of hyper-parameters for all methods. All methods are run at a resolution of  $512 \times 512$ . In the context of FPT, the high input resolution for the LPM remains  $512 \times 512$ , while the low input resolution for the side network is set to  $224 \times 224$ . All methods were trained employing the same data augmentations as those for low-resolution inputs in FPT. We set the reduction factor  $k$  to 8 and use 16 fine-grained prompts with the same hidden dimension as S. For important token selection, we retain the top 20% of important tokens.

**Evaluation Metric.** We use the Area Under the Receiver Operating Characteristic Curve (AUC) to evaluate the classification performance of each dataset. The performance-efficiency (PE) metric  $[8,17]$  $[8,17]$  $[8,17]$  is a metric to assess the performance and efficiency trade-off. However, PE only considers the impact of the number of learnable parameters. The memory requirement is another crucial factor that significantly influences training expenses. Therefore, we extend the <span id="page-39-0"></span>**Table 1.** Comparison results with state-of-the-art PETL methods across four evaluation datasets. 'Params.' denotes the ratio of learnable parameters to the total number of parameters. 'Mem.' denotes the memory usage (MB). The best and second-best results are bold and underlined, respectively.

| Method                | Computing cost |        | Fundus<br>(Messidor2) | Dermoscopy<br>(ISIC2018) | Mammography<br>(DDSM) | Chest X-ray<br>(COVID) | Performance |       |       |
|-----------------------|----------------|--------|-----------------------|--------------------------|-----------------------|------------------------|-------------|-------|-------|
|                       | Params.        | Mem.   |                       |                          |                       |                        | Avg. AUC    | PPE   | PME   |
| Full fine-tuning      | 100            | 24,116 | $86.87 \pm 0.53$      | $96.65 \pm 0.29$         | $92.49 \pm 0.34$      | $99.85 \pm 0.05$       | 93.96       | 69.54 | 69.54 |
| Linear probing        | 0.01           | 4,364  | $79.73 \pm 0.84$      | $93.37 \pm 0.31$         | $80.89 \pm 0.39$      | $99.21 \pm 0.07$       | 88.30       | 88.30 | 82.15 |
| Prompt tuning [13]    | 0.17           | 21,530 | $80.02 \pm 2.34$      | $94.20 \pm 0.20$         | $82.67 \pm 0.34$      | $99.27 \pm 0.03$       | 89.04       | 88.97 | 67.49 |
| Attention tuning [24] | 33.04          | 21,740 | $81.87 \pm 1.42$      | $94.40 \pm 0.40$         | $80.67 \pm 2.33$      | $99.58 \pm 0.19$       | 89.13       | 78.74 | 67.42 |
| Adapter [9]           | 2.05           | 20,308 | $80.77 \pm 1.48$      | $95.96 \pm 0.13$         | $80.76 \pm 3.38$      | $99.17 \pm 0.48$       | 89.16       | 88.38 | 68.39 |
| BitFit [28]           | 0.12           | 21,330 | $83.81 \pm 1.11$      | $94.84 \pm 0.15$         | $84.77 \pm 0.54$      | $99.81 \pm 0.03$       | 90.81       | 90.76 | 68.97 |
| LoRA [10]             | 0.69           | 21,944 | $86.08 \pm 0.95$      | $95.02 \pm 0.22$         | $82.26 \pm 4.04$      | $99.69 \pm 0.05$       | 91.01       | 90.74 | 68.72 |
| FPT (Ours)            | 1.81           | 3,182  | $84.95 \pm 2.01$      | $93.88 \pm 0.60$         | $90.52 \pm 0.59$      | $99.70 \pm 0.30$       | 92.26       | 91.54 | 87.42 |

PE metric to include performance-parameter-efficiency (PPE) and performancememory-efficiency (PME). The PPE formula remains the same as PE, defined as  $PPE = score*exp(-log_{10}(r+1))$ , where the score represents the average performance across all datasets, and  $r$  is the ratio of learnable parameters to all parameters. Similar to PPE, PME is defined as PME = score  $* \exp(-\log_{10}(m + 1)),$ where  $m$  is the ratio of the GPU memory requirement of the method to that of fine-tuning the entire LPM.

## 3.3 Comparisons with State-of-the-Art

We compare FPT against full fine-tuning, linear probing, and state-of-the-art (SOTA) PETL approaches. In full fine-tuning, all parameters of the LPM are made learnable during training on the downstream tasks. Linear probing involves solely training the new task-specific head on top of the LPM. Generally, full finetuning often represents the upper performance bound for transfer learning, while linear probing represents the lower bound. We also compare FPT against five other popular SOTA PETL methods.

The performance and efficiency of PETL methods are tabulated in Table [1.](#page-39-0) It can be observed that fine-tuning the entire LPM (full fine-tuning), as the upper bound, achieves the best average AUC of 93.96%, requiring 24,116 MB of training GPU memory with a batch size of 16. Although other PETL methods improve transfer learning efficiency by reducing the number of learnable parameters, they are unable to reduce the overhead brought by the high input resolution; all compared PETL methods require at least 20,000 MB of memory, and even linear probing requires 4,364 MB of memory. In contrast, FPT achieves the second-best AUC of 92.26%, while significantly reducing the memory requirement to only 3,182 MB (13% of full fine-tuning). Moreover, with a separate network, FPT utilizes only 1.81% learnable parameters compared to full fine-tuning. Therefore, in terms of efficiency, FPT achieves the best PPE and PME, presenting the best trade-off between performance and efficiency. This parameter and memory

| Components            | Avg. AUC   | Mem.         | PME        |
|-----------------------|------------|--------------|------------|
| Sole side network     | 80.12      | 17,218       | 62.95      |
| + LPM w/ FFM          | 90.82      | 21,070       | 69.14      |
|                       | $(+10.70)$ | $(+3,852)$   | $(+6.19)$  |
| + Asymmetric input    | 92.14      | 8,796        | 80.50      |
|                       | $(+1.32)$  | $(-12, 274)$ | $(+11.36)$ |
| + Token selection     | 92.26      | 4,880        | 84.82      |
|                       | $(+0.12)$  | $(-3,916)$   | $(+4.32)$  |
| + Features preloading | 92.26      | 3,182        | 87.42      |
|                       | $(+0.00)$  | $(-1,698)$   | $(+2.60)$  |

of FPT with different components.

<span id="page-40-0"></span>Table 2. The performance and efficiency Table 3. The performance and efficiency of FPT with different ratios for important token selection.

<span id="page-40-1"></span>

| Ratio | Avg. AUC     | Mem.         | PME          |
|-------|--------------|--------------|--------------|
| 10%   | 91.11        | <b>2,760</b> | 86.92        |
| 20%   | <b>92.26</b> | 3,182        | <b>87.42</b> |
| 30%   | 92.20        | 3,606        | 86.79        |
| 40%   | 91.99        | 4,020        | 86.03        |
| 50%   | 92.21        | 4,424        | 85.71        |
| 100%  | 92.14        | 8,796        | 80.50        |

efficiency positions FPT as a practical and feasible choice for leveraging LPMs in high-resolution contexts.

## 3.4 Impact of Components

To assess the impact of the proposed components in FPT, we evaluate the performance and efficiency of our framework by incrementally incorporating components. As shown in Table [2,](#page-40-0) starting with a sole side network, although the side network is lightweight, the long input sequence still consumes a large amount of memory at 17,218 MB for training. Employing fusion modules with fine-grained prompts to extract pre-trained knowledge from a frozen LPM notably enhances performance but further increases the memory burden. Then, the introduction of asymmetric input significantly lowers memory usage by 58% through decreasing the resolution of the input for the side network. It is worth noting that decreasing the input resolution for the side network enhances performance because the fine-grained information provided by the fine-grained prompts is sufficient, and smaller inputs for the side network reduce redundancy in features. Finally, by applying important token selection and preloading techniques, FPT further lowers the memory requirement by 64% without any loss of performance.

## 3.5 Impact of Important Token Selection Ratio

We evaluate the impact of different ratios of important tokens on FPT. As shown in Table [3,](#page-40-1) we observe that the classification performance remains similar when the ratio is between 20% and 50%. We then notice that 20% of the content of an image is sufficient for diagnosis in the tasks and modalities considered. Regarding efficiency, memory requirements increase as the ratio increases, leading to lower PME. Therefore, the ratio for important token selection is set to 20%.

# 4 Conclusion

In this paper, we introduce a novel PETL method, namely Fine-grained Prompt Tuning (FPT), for medical image classification. FPT significantly reduces memory requirements, particularly in the high-resolution context that commonly used in medical image analysis. To address the challenge of high memory requirement, we first adopt the design of side tuning and enhance it with an asymmetric input strategy. We then introduce fine-grained prompts and the fine-grained fusion module to allow effective adaptation of pre-trained knowledge from an out-of-domain LPM that takes images of a different scale as input. To further reduce memory requirement, important token selection and the preloading of pre-trained features are applied. By integrating these components, our PETL method achieves superior performance across four medical datasets while maintaining the best parameter and memory efficiency.

**Acknowledgments.** This study was supported by the National Key Research and Development Program of China (2023YFC2415400); the National Natural Science Foundation of China (62071210); the Shenzhen Science and Technology Program (RCYX202 10609103056042); the Shenzhen Science and Technology Innovation Committee (KXFZ20C20122117340001); the Guangdong Basic and Applied Basic Research (2021A1515220131).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-41-3"></span>1. Chen, C.F.R., Fan, Q., Panda, R.: Crossvit: Cross-attention multi-scale vision transformer for image classification. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 357–366 (2021)
- <span id="page-41-2"></span>2. Chen, Z., Guo, X., Woo, P.Y., Yuan, Y.: Super-resolution enhanced medical image diagnosis with sample affinity interaction. IEEE Transactions on Medical Imaging **40**(5), 1377–1389 (2021)
- <span id="page-41-5"></span>3. Codella, N., Rotemberg, V., Tschandl, P., Celebi, M.E., Dusza, S., Gutman, D., Helba, B., Kalloo, A., Liopyris, K., Marchetti, M., et al.: Skin lesion analysis toward melanoma detection 2018: A challenge hosted by the international skin imaging collaboration (isic). arXiv preprint [arXiv:1902.03368](http://arxiv.org/abs/1902.03368) (2019)
- <span id="page-41-4"></span>4. Decencière, E., Zhang, X., Cazuguel, G., Lay, B., Cochener, B., Trone, C., Gain, P., Ordonez, R., Massin, P., Erginay, A., et al.: Feedback on a publicly distributed image database: the messidor database. Image Analysis & Stereology **33**(3), 231– 234 (2014)
- <span id="page-41-0"></span>5. Devlin, J., Chang, M.W., Lee, K., Toutanova, K.: Bert: Pre-training of deep bidirectional transformers for language understanding. arXiv preprint [arXiv:1810.04805](http://arxiv.org/abs/1810.04805) (2018)
- <span id="page-41-6"></span>6. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth  $16x16$  words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-41-1"></span>7. Dutt, R., Ericsson, L., Sanchez, P., Tsaftaris, S.A., Hospedales, T.: Parameterefficient fine-tuning for medical image analysis: The missed opportunity. arXiv preprint [arXiv:2305.08252](http://arxiv.org/abs/2305.08252) (2023)

- <span id="page-42-15"></span>8. He, X., Li, C., Zhang, P., Yang, J., Wang, X.E.: Parameter-efficient model adaptation for vision transformers. arXiv preprint [arXiv:2203.16329](http://arxiv.org/abs/2203.16329) (2022)
- <span id="page-42-3"></span>9. Houlsby, N., Giurgiu, A., Jastrzebski, S., Morrone, B., De Laroussilhe, Q., Gesmundo, A., Attariyan, M., Gelly, S.: Parameter-efficient transfer learning for nlp. In: International Conference on Machine Learning. pp. 2790–2799. PMLR (2019)
- <span id="page-42-4"></span>10. Hu, E.J., Shen, Y., Wallis, P., Allen-Zhu, Z., Li, Y., Wang, S., Wang, L., Chen, W.: Lora: Low-rank adaptation of large language models. arXiv preprint [arXiv:2106.09685](http://arxiv.org/abs/2106.09685) (2021)
- <span id="page-42-8"></span>11. Huang, Y., Lin, L., Cheng, P., Lyu, J., Tam, R., Tang, X.: Identifying the key components in resnet-50 for diabetic retinopathy grading from fundus images: a systematic investigation. Diagnostics **13**(10), 1664 (2023)
- <span id="page-42-7"></span>12. Huang, Y., Lyu, J., Cheng, P., Tam, R., Tang, X.: Ssit: Saliency-guided selfsupervised image transformer for diabetic retinopathy grading. IEEE Journal of Biomedical and Health Informatics (2024)
- <span id="page-42-5"></span>13. Jia, M., Tang, L., Chen, B.C., Cardie, C., Belongie, S., Hariharan, B., Lim, S.N.: Visual prompt tuning. In: European Conference on Computer Vision. pp. 709–727. Springer (2022)
- <span id="page-42-0"></span>14. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., et al.: Segment anything. arXiv preprint [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-42-12"></span>15. Lee, R.S., Gimenez, F., Hoogi, A., Miyake, K.K., Gorovoy, M., Rubin, D.L.: A curated mammography data set for use in computer-aided detection and diagnosis research. Scientific data **4**(1), 1–9 (2017)
- <span id="page-42-10"></span>16. Lester, B., Al-Rfou, R., Constant, N.: The power of scale for parameter-efficient prompt tuning. arXiv preprint [arXiv:2104.08691](http://arxiv.org/abs/2104.08691) (2021)
- <span id="page-42-16"></span>17. Li, C., Liu, H., Li, L., Zhang, P., Aneja, J., Yang, J., Jin, P., Hu, H., Liu, Z., Lee, Y.J., et al.: Elevater: A benchmark and toolkit for evaluating language-augmented visual models. Advances in Neural Information Processing Systems **35**, 9287–9301 (2022)
- <span id="page-42-14"></span>18. Loshchilov, I., Hutter, F.: Decoupled weight decay regularization. arXiv preprint [arXiv:1711.05101](http://arxiv.org/abs/1711.05101) (2017)
- <span id="page-42-1"></span>19. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-42-2"></span>20. Radford, A., Wu, J., Child, R., Luan, D., Amodei, D., Sutskever, I., et al.: Language models are unsupervised multitask learners. OpenAI blog **1**(8), 9 (2019)
- <span id="page-42-9"></span>21. Ridnik, T., Ben-Baruch, E., Noy, A., Zelnik-Manor, L.: Imagenet-21k pretraining for the masses. arXiv preprint [arXiv:2104.10972](http://arxiv.org/abs/2104.10972) (2021)
- <span id="page-42-13"></span>22. Siddhartha, M.: Covid cxr image dataset (research) (2021), [https://www.kaggle.](https://www.kaggle.com/datasets/sid321axn/covid-cxr-image-dataset-research) [com/datasets/sid321axn/covid-cxr-image-dataset-research](https://www.kaggle.com/datasets/sid321axn/covid-cxr-image-dataset-research)
- <span id="page-42-6"></span>23. Sung, Y.L., Cho, J., Bansal, M.: Lst: Ladder side-tuning for parameter and memory efficient transfer learning. Advances in Neural Information Processing Systems **35**, 12991–13005 (2022)
- <span id="page-42-17"></span>24. Touvron, H., Cord, M., El-Nouby, A., Verbeek, J., Jégou, H.: Three things everyone should know about vision transformers. In: European Conference on Computer Vision. pp. 497–515. Springer (2022)
- <span id="page-42-11"></span>25. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. Advances in neural information pro cessing systems **30** (2017)

- <span id="page-43-0"></span>26. Weiss, K., Khoshgoftaar, T.M., Wang, D.: A survey of transfer learning. Journal of Big data **3**(1), 1–40 (2016)
- <span id="page-43-1"></span>27. Wu, J., Fu, R., Fang, H., Liu, Y., Wang, Z., Xu, Y., Jin, Y., Arbel, T.: Medical sam adapter: Adapting segment anything model for medical image segmentation. arXiv preprint [arXiv:2304.12620](http://arxiv.org/abs/2304.12620) (2023)
- <span id="page-43-4"></span>28. Zaken, E.B., Ravfogel, S., Goldberg, Y.: Bitfit: Simple parameter-efficient fine-tuning for transformer-based masked language-models. arXiv preprint [arXiv:2106.10199](http://arxiv.org/abs/2106.10199) (2021)
- <span id="page-43-2"></span>29. Zhang, J.O., Sax, A., Zamir, A., Guibas, L., Malik, J.: Side-tuning: a baseline for network adaptation via additive side networks. In: Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part III 16. pp. 698–714. Springer (2020)
- <span id="page-43-3"></span>30. Zhang, J., Kapse, S., Ma, K., Prasanna, P., Saltz, J., Vakalopoulou, M., Samaras, D.: Prompt-mil: Boosting multi-instance learning schemes via task-specific prompt tuning. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 624–634. Springer (2023)

Image /page/44/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below. The icon is a gray circle with a gray bookmark shape inside. The bookmark is a rectangle with a triangular cutout at the top right corner, and it is positioned vertically in the center of the circle. The text below the icon reads "Check for updates" in gray capital letters.

# FissionFusion: Fast Geometric Generation and Hierarchical Souping for Medical Image Analysis

Santosh Sanjeev **D**[,](http://orcid.org/0009-0003-6571-8902) Nuren Zhaksylyk **D**, Ibrahim Almakky<sup>( $\boxtimes$ [\)](http://orcid.org/0009-0008-8802-7107)</sup> **D**, Anees Ur Rehman Hashmi $\mathbf{D}$ [,](http://orcid.org/0000-0002-1458-7565) Mohammad Areeb Qazi $\mathbf{D}$ , and Mohammad Yaqu[b](http://orcid.org/0000-0001-6896-1105)

Mohamed bin Zayed University of Artificial Intelligence, Abu Dhabi, UAE {santosh.sanjeev,nuren.zhaksylyk,ibrahim.almakky, anees.hashmi,mohammad.qazi,mohammad.yaqub}@mbzuai.ac.ae

Abstract. The scarcity of well-annotated medical datasets requires leveraging transfer learning from broader datasets like ImageNet or pretrained models like CLIP. Model soups averages multiple fine-tuned models aiming to improve performance on In-Domain (ID) tasks and enhance robustness on Out-of-Distribution (OOD) datasets. However, applying these methods to the medical imaging domain faces challenges and results in suboptimal performance. This is primarily due to differences in error surface characteristics that stem from data complexities such as heterogeneity, domain shift, class imbalance, and distributional shifts between training and testing phases. To address this issue, we propose a hierarchical merging approach that involves local and global aggregation of models at various levels based on models' hyperparameter configurations. Furthermore, to alleviate the need for training a large number of models in the hyperparameter search, we introduce a computationally efficient method using a cyclical learning rate scheduler to produce multiple models for aggregation in the weight space. Our method demonstrates significant improvements over the model souping approach across multiple datasets (around 6% gain in HAM10000 and CheXpert datasets) while maintaining low computational costs for model generation and selection. Moreover, we achieve better results on OOD datasets compared to model soups. Code is available at [https://github.](https://github.com/BioMedIA-MBZUAI/FissionFusion) [com/BioMedIA-MBZUAI/FissionFusion.](https://github.com/BioMedIA-MBZUAI/FissionFusion)

**Keywords:** Model Soups · Medical Image Analysis · Model Merging · Transfer Learning

# 1 Introduction

Deep learning (DL) has emerged as the de facto standard for various computer vision tasks, consistently achieving state-of-the-art performance. A pivotal con-

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_13.](https://doi.org/10.1007/978-3-031-72390-2_13)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 131–141, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_13)\_13

tributor to this success lies in the availability of pre-trained models. In recent years, a well-established paradigm has emerged: pre-training models on largescale data, such as ImageNet [\[5\]](#page-52-0) followed by fine-tuning on target tasks with limited training data [\[21](#page-53-0)[,22](#page-53-1)]. This strategy has proven particularly effective in domains with constrained data availability, such as medical imaging. The scarcity of annotated medical datasets, coupled with the challenges of data acquisition and ethical considerations, highlights the importance of transfer learning from large-scale datasets. Although public data in medical imaging is increasing, it is usually smaller in size compared to natural image datasets, which has led to the widespread adoption of transfer learning from ImageNet [\[5\]](#page-52-0), to improve performance on medical tasks [\[20,](#page-53-2)[23,](#page-53-3)[29](#page-54-0)].

Image /page/45/Figure/2 description: The image displays two rows of contour plots, each representing error landscapes. The top row, labeled "Error landscapes of Natural Domain Datasets," contains three plots (a), (b), and (c). The bottom row, labeled "Error landscapes of Medical Domain Datasets," contains three plots (d), (e), and (f). Each plot shows a color-coded landscape with contour lines, indicating varying error levels. Specific points are marked on each plot with a black circle representing the "Best model in validation (origin)", a black square representing the "2nd best model in validation", and a black diamond representing the "3rd best model in validation". The color bars next to each plot indicate the range of error values, with different color gradients representing different error magnitudes. For example, plot (a) shows error values ranging from 0.66 to over 3.7, plot (b) from 0.66 to over 3.7, and plot (c) from 27 to over 31. Plots (d), (e), and (f) show error values for medical datasets, with ranges like 3.7 to over 24 for (d), 0.0 to over 16 for (e), and 7.7 to over 13 for (f).

<span id="page-45-0"></span>Fig. 1. The validation error on a two-dimensional slice of the error landscapes for various natural and medical domain datasets following [\[7\]](#page-53-4). (a) CIFAR-10 [\[14\]](#page-53-5) (b) CIFAR-100 [\[14](#page-53-5)] (c) FGVC-Aircrafts [\[17\]](#page-53-6) (d) RSNA Pneumonia [\[28](#page-54-1)] (e) APTOS [\[13\]](#page-53-7) (f) HAM10000 [\[32\]](#page-54-2). We employ the 3 best-performing models from the validation set, with the best model serving as the reference (origin).  $(a-c)$  are characterized by smoothness and convexity, whereas (d–f) exhibit pronounced roughness. The roughness and presence of multiple local minima in loss surfaces of medical datasets are attributed to various intricacies inherent in medical data, including data heterogeneity, domain shift from pre-trained (ImageNet) networks, class imbalance, and distribution shift between the training and testing phases.

The common practice in transfer learning is to adapt a pre-trained model to a downstream task by fine-tuning. This involves conducting a grid search to explore various hyperparameter combinations and selecting the model that performs best on the validation set. Another approach is employing ensemble techniques [\[6](#page-53-8)], where multiple models are utilized simultaneously, albeit at the expense of increased computational and memory requirements, especially at inference time.

Recent research by [\[25\]](#page-53-9) has noted that fine-tuned models optimized independently from the same pre-trained initialization often converge to similar error basins. A previous work [\[10](#page-53-10)] has demonstrated that averaging weights along a single training trajectory can enhance model performance in non-transfer settings. Motivated by these observations, [\[34](#page-54-3)] proposed model averaging - Model Soups (MS) as an alternative approach to ensembles, aiming to derive a single model that achieves a good performance in terms of accuracy and inference time on In-Domain (ID) as well as Out-of-Distribution (OOD) datasets. They show that averaging several fine-tuned models trained with different hyperparameter settings results in a better model. This method is particularly effective in scenarios with natural imaging datasets or those without significant domain shifts. Several other works have improved upon the model merging concept by adopting Fisher-based weight averaging [\[19\]](#page-53-11), Task Arithmetic [\[8\]](#page-53-12), Pruning [\[37\]](#page-54-4), Gradientbased Matching [\[3\]](#page-52-1), Tie-Merging [\[35](#page-54-5)], and FedSoups [\[1](#page-52-2)] for Federated Learning setting. Unlike ensembles, [\[8,](#page-53-12)[34](#page-54-3)[,37](#page-54-4)] as well as our work, require only one model for inference. This is especially important in hospital settings, where compute resources are often limited, such as portable ultrasound devices.

However, complexities arise when dealing with medical datasets. As illustrated in Fig. [1,](#page-45-0) a significant contrast arises between the error surfaces of natural imaging datasets  $(a-c)$  and those of medical datasets  $(d-f)$ . The presence of multiple local minima in (d–f) result in challenging optimization landscapes, where models are prone to getting stuck in local minima. Consequently, the effectiveness of model averaging is compromised, often leading to subpar performance outcomes. Few works have adopted model souping for medical datasets [\[16](#page-53-13)[,18](#page-53-14),[30,](#page-54-6)[36](#page-54-7)]. Most of these studies have applied uniform or greedy souping to few models, and the majority have not explored or analyzed model souping from the perspective of error surfaces. Additionally, the process of model averaging typically involves training multiple models with different hyperparameter settings, which can be computationally intensive. In contrast to previous research, our work focuses on computationally efficient model generation and averaging in a transfer learning context, especially addressing domains that experience significant shifts.

In Model Soups [\[34\]](#page-54-3), the process of fine-tuning consists of two main steps: (1) Model generation - fine-tuning models with various hyperparameter configurations, and (2) Model selection - selecting the model with the highest accuracy on the held-out validation set and then performing either uniform or greedy souping. Our study explores the challenges associated with both steps in the medical image analysis domain. Drawing from insights in [\[7,](#page-53-4)[25](#page-53-9)], we propose a Fast Geometric Generation (FGG) approach to generate models with minimal computational overhead. Additionally, we address the model selection process by introducing Hierarchical Souping (HS), which is better suited for medical data because of the aforementioned complexities. Our key contributions are as follows:

– We propose **Fast Geometric Generation (FGG)** approach, which uses cyclical learning rate scheduler in the weight space for efficient model generation. This approach achieves superior results compared to model soups at a lower computational cost.

- We introduce a novel selection mechanism Hierarchical Souping (HS), tailored specifically for the medical image analysis domain, which performs model averaging at different levels.
- We comprehensively analyze model souping across various datasets in both natural and medical domains. We demonstrate that the combination of FGG and HS for selection significantly enhances results, improves robustness, and increases generalization to out-of-distribution datasets.

Image /page/47/Figure/4 description: The image displays a three-part figure illustrating optimization processes. Part (a) shows a 3D landscape with a pink sphere labeled \u03b8
eighborsubscript{0} at the top, connected by dashed arrows to several yellow spheres labeled \u03b8
eighborsubscript{1}, \u03b8
eighborsubscript{2}, \u03b8
eighborsubscript{3}, and \u03b8
eighborsubscript{n} at various points on the landscape, indicating a path or exploration. Part (b) is a zoomed-in view of a section of the landscape from part (a), showing a white sphere and several green and yellow spheres, also connected by dashed arrows, suggesting a local search or refinement. Part (c) presents a hierarchical diagram labeled FGG and HS. The FGG section shows \u03b8
eighborsubscript{0} at the top, branching to \u03b8
eighborsubscript{1}, \u03b8
eighborsubscript{2}, ..., \u03b8
eighborsubscript{n}. Each of these branches further splits into multiple nodes labeled \u03b8
eighborsubscript{1}
eighborsubscript{1}, \u03b8
eighborsubscript{2}
eighborsubscript{1}, ..., \u03b8
eighborsubscript{m}
eighborsubscript{n}. Below these are nodes labeled \u03b8
eighborsubscript{sp1}, \u03b8
eighborsubscript{sp2}, ..., \u03b8
eighborsubscript{spn}. The HS section contains a single blue sphere labeled \u03b8
eighborsubscript{sp} below the \u03b8
eighborsubscript{sp} nodes.

<span id="page-47-0"></span>Fig. 2. An illustration of (a) Loss landscape of fine-tuned models (b) Fast Geometric Generation(FGG) approach using cyclical learning rate scheduler (c) FGG and the Hierarchical Souping (HS) approach.

# 2 Methodology

Let  $\mathcal{D}_{\text{train}} = \{(x_i, y_i)\}_{i=1}^N$  denote the training dataset where  $x_i \in \mathbb{R}^d$  represents the input data and  $y_i \in \{1, 2, ..., C\}$  denotes the corresponding label from a set of *C* classes. Similarly, let  $\mathcal{D}_{val} = \{(x_j, y_j)\}_{j=1}^M$  be the validation dataset. We adapt a pre-trained model to our task by performing linear probing for a few epochs as a warm-up step, where we only update the weights of the last layer while freezing the rest of the model. Let  $\theta_0$  denote the parameters of the linear probed model. As mentioned above, the souping procedure involves two main steps (1) Generation of the models by fine-tuning (2) Selection of models that perform best on the  $\mathcal{D}_{val}$ . The illustration of our proposed approaches (FGG and HS) can be seen in Fig. [2.](#page-47-0)

Fast Geometric Generation (FGG). We design FGG to generate a range of diverse neural networks while navigating through the weight space taking small steps. The primary objective is to explore variations in network weights without straying into regions likely to lead to low test performance. Instead of employing the conventional hyperparameter grid search, we opt for a more focused strategy. In FGG, we only iterate through a single hyperparameter i.e. (learning rate), and the rest of the hyperparameters are kept constant. This helps to reduce the hyperparameter search space and the computational cost.

In FGG, we start with  $\theta_0$  as an initialization and fully train the model using n different learning rates, resulting in a set of parameters  $\Theta = {\theta_1, \ldots, \theta_n}$ . After obtaining  $\Theta$ , we implement the **fission** process. We initialize the weights  $\theta_t \in \Theta$ , and carry out a second training process for a set of iterations  $I = \{1, \ldots, k\}$  to generate a set of parameters  $\hat{\Theta} = {\hat{\theta}^1, \ldots, \hat{\theta}^m}$ . During this, we employ a learning rate scheduler to cyclically change the learning rate every cycle  $c$ , where  $c$  is defined as a set of number of iterations and is an even number. This encourages  $\theta_t$  to diverge from its initialization, while maintaining validation accuracy. We use a function  $\alpha(i)$  to control the learning rate at iteration  $i \in I$  as follows:

$$
\alpha(i) = \begin{cases} \alpha_2.(2t(i)) + \alpha_1(1 - 2t(i)) & \text{if } 0 < t(i) \le 0.5, \\ \alpha_1.(2t(i) - 1) + \alpha_2(2 - 2t(i)) & \text{if } 0.5 < t(i) \le 1, \end{cases}
$$

where  $t(i) = \frac{1}{c} (\text{mod}(i-1, c) + 1), \alpha_2$  and  $\alpha_1 (\alpha_2 \le \alpha_1)$  are hyperparameters used to control the minimum and maximum learning rates, respectively. Using  $\alpha(i)$ , we train the network  $\theta_t$  to form the new set of parameters  $\hat{\Theta}$ , where each  $\hat{\theta} \in \hat{\Theta}$  is the model parameters when the learning rate reaches its minimum value,  $\alpha(i) = \alpha_2$ . This occurs at the point where  $mod(i - 1, c) + 1 = \frac{c}{2}$  and  $t(i) = \frac{1}{2}$ . We follow this training process for every  $\theta_t \in \Theta$ , which results in a  $\{\hat{\Theta}_1,\ldots,\hat{\Theta}_n\}$ .

During high learning rate intervals (close to  $\alpha_1$ ), the weight  $\theta_t$  traverses the weight space with larger strides, potentially leading to higher test error. Conversely, in low learning rate episodes,  $\theta_t$  transitions to smaller steps, reducing test error. This mechanism facilitates incremental movements in weight space, aiding models in evading local minima. Additionally, it gathers diverse models for averaging, reducing the necessity for an extensive grid search. To summarise, fission process starts from the base models (each trained with a different learning rate). Then, we cyclically vary the LR, generating multiple models for each base model. This process helps models escape several local minima in rough loss surfaces and generate more generalizable models, facilitating easier model averaging (Fusion using HS).

Hierarchical Souping (HS). In [\[34\]](#page-54-3), the greedy souping approach outperforms uniform averaging by sequentially adding models to the soup if they improve accuracy on  $\mathcal{D}_{val}$ . This approach can yield suboptimal results when the best model is stuck in a local optimum because of the uneven error surface caused due to domain shift. To solve this, we propose Hierarchical Souping, where models are merged at different levels. Starting from the parameter sets  $\Theta$  and  $\Theta$  acquired during the FGG phase, we adopt a local souping approach where the generated models  $\{\hat{\Theta}_1,\ldots,\hat{\Theta}_n\}$  are averaged along with the corresponding initialization  $\theta_t$ (greedy or uniform i.e.  $\hat{\theta}_{sp_t} = \frac{1}{m+1} [\sum_{i=1}^m \hat{\theta}_t^i + \theta_t]$ ) at different levels resulting in  ${\{\hat{\theta}_{sp_1}, \hat{\theta}_{sp_2}, \ldots, \hat{\theta}_{sp_n}\}}$  and a greedy averaging technique is used at the top level giving  $\theta_{\rm sp}$  (GoG refers to Greedy at all levels whereas GoU refers to Greedy at the top level and Uniform at the lower levels). This approach also known as fusion enables networks to escape local minima at the lower levels by local aggregation, giving a good subset of generalizable models that can be averaged in a greedy manner at the top level.

# 3 Experiments

Implementation Details. Our research investigates two model architectures: DeiT-B (Transformer) and ResNet50 (CNN), both pre-trained on ImageNet. We conduct a 10-epoch warmup with the entire network except the last layer frozen  $(\theta_0)$ . After unfreezing the entire model, we perform full fine-tuning using the LP-FT approach [\[15](#page-53-15)]. We use the AdamW optimizer with a cosine annealing scheduler for 50 epochs, a batch size of 128, and an image resolution of  $224 \times 224$ .

For model soups experiments, we conduct a grid search over learning rates, seeds, and augmentations similar to [\[34\]](#page-54-3). The learning rates (LR) are (1e–3, 5e−4, 1e−4, 5e−5, 1e−5, 5e−6, 1e−6, 1e−7), and augmentations include minimal (random crop covering 90–100% of image), medium (default timm library settings [\[33\]](#page-54-8)), and heavy (RandAugment with  $N = 2, M = 15$ ) [\[2\]](#page-52-3). Each hyperparameter configuration is run with two seeds, resulting in 48 base models [\[34\]](#page-54-3).

For our method, we vary only the learning rate, fine-tuning eight models initially with heavy augmentation and a fixed seed. In the Supplementary Material, we analyze the learning rate as a top-level hyperparameter using Linear Mode Connectivity (LMC) in Fig. [1.](#page-45-0) LMC ( $\theta = \lambda \cdot \theta_A + (1-\lambda) \cdot \theta_B$ ) between two models  $\theta_A$  and  $\theta_B$  generated during GS is analyzed by varying  $\lambda$  and calculating performance at  $\theta$  for each model pair. Ideally, LMC should be an inverted parabola or a straight curve if models are in the same basin. We observe that changing seed and augmentation yield smooth curves (models with large differences cause drops in F1), while LR variations result in erratic patterns and significant F1 drops, indicating models lie in different basins. This suggests LR is crucial in guiding models to specific basins, while other hyperparameters aid in converging to global optima. [\[24](#page-53-16),[34\]](#page-54-3) also support LR as a critical hyperparameter.

All experiments use the AdamW optimizer with a cyclical learning rate scheduler, where the learning rate ranges between  $\alpha_1 = 1e - 5$  and  $\alpha_2 = 1e - 8$ . From each base model, we generate five models using FGG, resulting in a total of 48 models. We train the eight initial models for 50 epochs, then perform the second stage training process (FGG) for 17 epochs (4 epochs per cycle) with a cyclical learning rate, collecting a total of five models per base model.

Datasets. For the primary experiments, we consider two natural imaging domain datasets and five medical domain datasets. We use CIFAR10 [\[14\]](#page-53-5) and CIFAR100 [\[14\]](#page-53-5), partitioning the training dataset into train/validation sets in a 90%:10% ratio. We utilize the official test split provided. For the CheXpert [\[9](#page-53-17)] and HAM10000 [\[32](#page-54-2)] datasets, we adhere to the official train/validation/test splits. For APTOS [\[13](#page-53-7)], EyePACs [\[12\]](#page-53-18), and RSNA-Pneumonia [\[27](#page-54-9),[28\]](#page-54-1) datasets, we split the data into train/validation/test sets in an  $80\%:10\%:10\%$  ratio, given that only the training dataset was publicly available. Notably, all datasets are multiclass, except CheXpert, which is a multilabel dataset. For the OOD experiments, we consider the CIFAR10.1 [\[26](#page-54-10)[,31](#page-54-11)] having 2000 test samples from the natural imaging domain. For the medical imaging domain, we use the Messidor and Messidor-2 [\[4](#page-52-4)] datasets, sampling 10% of the data for the test set. We also use the MIMIC-CXR [\[11\]](#page-53-19) dataset and follow its official test split.

# 4 Results and Discussion

Table [1](#page-50-0) shows that our method achieves better results in both natural and medical imaging domains. Different classification metrics were chosen for different datasets, as they are the metrics of interest used for those datasets [\[21\]](#page-53-0) [?]. In CIFAR datasets, GoU and GoG achieve better results than model soups, though it is insignificant due to the smooth convex error surface. However, in medical datasets, we observe around  $6\%$  improvement in Recall (GoG) for the HAM10000 dataset (ResNet50) and around 6% gain (GoG) in AUC (DeiT-B) for the CheXpert dataset. Both GoU and GoG approaches achieve similar results

<span id="page-50-0"></span>

| <b>Table 1.</b> Performance comparison of different methods. (GS (best) - best model on |
|-----------------------------------------------------------------------------------------|
| the validation set from Grid Search, FGG(best) - best model on the validation set from  |
| Fast Geometric Generation, GoU - Greedy of Uniform, GoG - Greedy of Greedy), Bold       |
| numbers mean best and underlined are the second best                                    |

| Model    | Method          | CIFAR10<br>(Acc.) | CIFAR100<br>(Acc.)                                             | APTOS<br>(F1)                                           | HAM10000<br>(Recall)                                    | RSNA<br>(F1)                                            | CheXpert<br>(AUC)                                       | EyePACs<br>(F1)                                         |
|----------|-----------------|-------------------|----------------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|
| ResNet50 | GS(best)        | 0.9769            | 0.8380                                                         | 0.7086                                                  | 0.6074                                                  | 0.9444                                                  | 0.8444                                                  | 0.4750                                                  |
|          | Uniform Soup    | 0.8703            | 0.7652                                                         | 0.5509                                                  | 0.5698                                                  | 0.9171                                                  | 0.5752                                                  | 0.1738                                                  |
|          | Greedy Soup     | 0.9769            | 0.8401                                                         | <b>0.7247</b>                                           | 0.6074                                                  | 0.9444                                                  | 0.8444                                                  | 0.4750                                                  |
|          | FGG(best)(Ours) | 0.9783            | <span style="text-decoration: underline;">0.8464</span>        | 0.7172                                                  | 0.6614                                                  | 0.9518                                                  | 0.8434                                                  | 0.4874                                                  |
|          | GoU (Ours)      | <b>0.9785</b>     | 0.8457                                                         | 0.6909                                                  | <b>0.6818</b>                                           | <b>0.9545</b>                                           | 0.8351                                                  | <b>0.4905</b>                                           |
|          | GoG (Ours)      | <b>0.9785</b>     | <span style="text-decoration: underline;"><b>0.8477</b></span> | <span style="text-decoration: underline;">0.7172</span> | 0.6614                                                  | <span style="text-decoration: underline;">0.9518</span> | <b>0.8488</b>                                           | <span style="text-decoration: underline;">0.4900</span> |
| DeiT-B   | GS(best)        | 0.9871            | 0.8919                                                         | 0.6903                                                  | 0.6487                                                  | 0.9503                                                  | 0.8143                                                  | 0.4807                                                  |
|          | Uniform Soup    | 0.9386            | 0.8551                                                         | 0.1637                                                  | <span style="text-decoration: underline;">0.1429</span> | 0.4147                                                  | 0.7177                                                  | 0.1697                                                  |
|          | Greedy Soup     | 0.9892            | 0.8968                                                         | 0.6785                                                  | <span style="text-decoration: underline;">0.6487</span> | 0.9503                                                  | <span style="text-decoration: underline;">0.8068</span> | 0.4865                                                  |
|          | FGG(best)(Ours) | 0.9876            | 0.8963                                                         | <b>0.7011</b>                                           | 0.6393                                                  | 0.9529                                                  | <b>0.8619</b>                                           | <b>0.5029</b>                                           |
|          | GoU (Ours)      | 0.9899            | <span style="text-decoration: underline;">0.8968</span>        | 0.6976                                                  | <b>0.6495</b>                                           | <b>0.9579</b>                                           | 0.7609                                                  | 0.4903                                                  |
|          | GoG (Ours)      | <b>0.9901</b>     | <b>0.8987</b>                                                  | <span style="text-decoration: underline;">0.7003</span> | 0.6393                                                  | 0.9529                                                  | <b>0.8644</b>                                           | <span style="text-decoration: underline;">0.4940</span> |

except for CheXpert (DeiT-B), where GoU lags behind GoG. GoG consistently outperforms Greedy Soup in almost all cases and at a significantly lower computational cost, as we do not perform a full grid search. Model soups GS requires 2400 epochs to generate 48 models (50 epochs each), whereas our FGG requires 536 epochs  $(8 \times 50 + 8 \times 17)$ , four times less than GS. The time per epoch is the same in both settings. Unlike ensembles, we hierarchically average model weights to get one model without incurring additional inference or memory costs.

Greedy soups do not perform as expected when fine-tuning on medical datasets due to uneven error surfaces. For example, for DeiT-B on the CheXpert dataset, the best model has a high validation score but a low test score, indicating poor generalization. The FGG process overcomes this issue by escaping numerous local minima, while acquired models cluster around the same area in the error surface, facilitating smoother averaging. Local averaging in the HS approach allows smoother averaging at local surfaces and greater diversity at the top level. We conduct an ablation study in Table 1 in the Supplementary Material exploring greedy souping on FGG models and HS on grid-search generated models.

OOD Analysis. We conduct an analysis on OOD scenarios for both natural and medical domain datasets. Performance comparison of souped models on OOD data is important as averaged models should demonstrate robustness to distribution shifts [\[34](#page-54-3)]. In Fig. [3,](#page-51-0) we compare the performance of various approaches in

Image /page/51/Figure/4 description: This image contains six scatter plots arranged in a 2x3 grid. The top row plots CIFAR10 (in-domain) Accuracy against CIFAR10.1 (OOD) Accuracy (a), ChExPert (in-domain) AUC against MIMIC (OOD) AUC (b), and EyePACS (in-domain) F1 against Avg (APTOS, Messidor-2) (OOD) F1 (c). The bottom row plots the same metrics but with CIFAR10 (in-domain) Accuracy on the x-axis and CIFAR10.1 (OOD) Accuracy on the y-axis (d), ChExPert (in-domain) AUC on the x-axis and MIMIC (OOD) AUC on the y-axis (e), and EyePACS (in-domain) F1 on the x-axis and Avg (APTOS, Messidor-2) (OOD) F1 on the y-axis (f). Each plot uses different markers to represent different model types: triangles for Top 5 MS models, stars for Top 5 FGG models, circles for Greedy Soup, and diamonds for GoG. Plot (a) shows CIFAR10 (in-domain) Accuracy ranging from 0.970 to 0.978 and CIFAR10.1 (OOD) Accuracy ranging from 0.9175 to 0.9350. Plot (b) shows ChExPert (in-domain) AUC ranging from 0.820 to 0.850 and MIMIC (OOD) AUC ranging from 0.765 to 0.790. Plot (c) shows EyePACS (in-domain) F1 ranging from 0.465 to 0.490 and Avg (APTOS, Messidor-2) (OOD) F1 ranging from 0.41 to 0.49. Plot (d) shows CIFAR10 (in-domain) Accuracy ranging from 0.9860 to 0.9900 and CIFAR10.1 (OOD) Accuracy ranging from 0.956 to 0.968. Plot (e) shows ChExPert (in-domain) AUC ranging from 0.76 to 0.86 and MIMIC (OOD) AUC ranging from 0.75 to 0.79. Plot (f) shows EyePACS (in-domain) F1 ranging from 0.470 to 0.500 and Avg (APTOS, Messidor-2) (OOD) F1 ranging from 0.41 to 0.48.

<span id="page-51-0"></span>**Fig. 3.** OOD analysis for different architectures on various datasets (a) CIFAR10  $v/s$ CIFAR10.1 - ResNet50 (b) CheXpert v/s MIMIC - ResNet50 (c) APTOS v/s (EyePacs, Messidor, Messidorv2) - ResNet50 (d) CIFAR10 v/s CIFAR10.1 - DeiT-B (e) CheXpert  $v/s$  MIMIC - DeiT-B (f) APTOS  $v/s$  (EyePacs, Messidor, Messidorv2) - DeiT-B. We do not plot the results of Uniform Soups as it performs poorly.

both ID and OOD datasets, where the top five models are selected based on validation set results. In almost all cases, GoU or GoG yields similar or better results than greedy soups in ID and OOD tasks at a much lower computational cost. Our approach results in higher ID and OOD performance gains, particularly for medical imaging datasets, attributed to the FGG approach aiding models in escaping local minima. HS also contributes by facilitating easier averaging between models in the error surface.

# 5 Conclusion

This work investigates challenges associated with model averaging in transfer learning settings, particularly in medical imaging domain where significant domain shifts can occur. To address this, we introduce Fast Geometric Generation (FGG) leveraging hyperparameter significance and a cyclical learning rate scheduler. Moreover, we propose a Hierarchical Souping mechanism, which involves averaging models at different levels based on the smoothness of the error surface and hyperparameter significance. The proposed generation and selection methodologies yield notable performance enhancements compared to the traditional model souping approach. While our work achieves improved results, we observe instances where models from grid search occasionally outperform averaged models due to very rough error landscapes. This suggests a potential improvement for enhancing generalizability by smoothing the error surface, an aspect we plan to focus on in future endeavors.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-52-2"></span>1. Chen, M., Jiang, M., Dou, et al.: Fedsoup: Improving generalization and personalization in federated learning via selective model interpolation. In: International Conference on MICCAI. Springer (2023)
- <span id="page-52-3"></span>2. Cubuk, E.D., Zoph, B., Shlens, J., Le, Q.: Randaugment: Practical automated data augmentation with a reduced search space. In: Advances in NeurIPS. vol. 33, pp. 18613–18624. Curran Associates, Inc. (2020), [https://proceedings.neurips.cc/](https://proceedings.neurips.cc/paper_files/paper/2020/file/d85b63ef0ccb114d0a3bb7b7d808028f-Paper.pdf) [paper\\_files/paper/2020/file/d85b63ef0ccb114d0a3bb7b7d808028f-Paper.pdf](https://proceedings.neurips.cc/paper_files/paper/2020/file/d85b63ef0ccb114d0a3bb7b7d808028f-Paper.pdf)
- <span id="page-52-1"></span>3. Daheim, N., Möllenhoff, T., Ponti, E., Gurevych, I., Khan, M.E.: Model merging by uncertainty-based gradient matching. In: The Twelfth ICLR (2024), [https://](https://openreview.net/forum?id=D7KJmfEDQP) [openreview.net/forum?id=D7KJmfEDQP](https://openreview.net/forum?id=D7KJmfEDQP)
- <span id="page-52-4"></span>4. Decencière, E., Zhang, et al.: Feedback on a publicly distributed image database: The messidor database. Image Analysis and Stereology 0 (07 2014). [https://doi.](https://doi.org/10.5566/ias.1155) [org/10.5566/ias.1155](https://doi.org/10.5566/ias.1155)
- <span id="page-52-0"></span>5. Deng, J., Dong, W., Socher, et al.: Imagenet: A large-scale hierarchical image database. In: 2009 IEEE Conference on Computer Vision and Pattern Recognition (2009)

- <span id="page-53-8"></span>6. Dietterich, T.G.: Ensemble methods in machine learning. In: Multiple Classifier Systems. pp. 1–15. Springer Berlin Heidelberg, Berlin, Heidelberg (2000)
- <span id="page-53-4"></span>7. Garipov, T., Izmailov, et al.: Loss surfaces, mode connectivity, and fast ensembling of dnns. In: Advances in Neural Information Processing Systems (2018)
- <span id="page-53-12"></span>8. Ilharco, G., Ribeiro, M.T., Wortsman, M., Schmidt, L., Hajishirzi, H., Farhadi, A.: Editing models with task arithmetic. In: The Eleventh ICLR (2023), [https://](https://openreview.net/forum?id=6t0Kwf8-jrj) [openreview.net/forum?id=6t0Kwf8-jrj](https://openreview.net/forum?id=6t0Kwf8-jrj)
- <span id="page-53-17"></span>9. Irvin, J., Rajpurkar, P., Ko, et al.: Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison. In: Proceedings of the AAAI conference on artificial intelligence (2019)
- <span id="page-53-10"></span>10. Izmailov, P., Podoprikhin, D., Garipov, T., Vetrov, D., Wilson, A.G.: Averaging weights leads to wider optima and better generalization. arXiv preprint [arXiv:1803.05407](http://arxiv.org/abs/1803.05407) (2018)
- <span id="page-53-19"></span>11. Johnson, A.E., Pollard, T.J., Berkowitz, S.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Mark, R.G., Horng, S.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Scientific data  $6(1)$ , 317 (2019)
- <span id="page-53-18"></span>12. Kaggle: Diabetic Retinopathy Detection. [https://www.kaggle.com/competitions/](https://www.kaggle.com/competitions/diabetic-retinopathy-detection/data) [diabetic-retinopathy-detection/data](https://www.kaggle.com/competitions/diabetic-retinopathy-detection/data) (2015)
- <span id="page-53-7"></span>13. Karthik, Maggie, S.D.: Aptos 2019 blindness detection (2019), [https://kaggle.com/](https://kaggle.com/competitions/aptos2019-blindness-detection) [competitions/aptos2019-blindness-detection](https://kaggle.com/competitions/aptos2019-blindness-detection)
- <span id="page-53-5"></span>14. Krizhevsky, A., Hinton, G.: Learning multiple layers of features from tiny images. Tech. Rep. 0, University of Toronto, Toronto, Ontario (2009), [https://www.cs.](https://www.cs.toronto.edu/~kriz/learning-features-2009-TR.pdf) [toronto.edu/~kriz/learning-features-2009-TR.pdf](https://www.cs.toronto.edu/~kriz/learning-features-2009-TR.pdf)
- <span id="page-53-15"></span>15. Kumar, A., Raghunathan, A., et al.: Fine-tuning can distort pretrained features and underperform out-of-distribution. In: ICLR (2022)
- <span id="page-53-13"></span>16. Kvak, D., Chromcová, A., Biroš, et al.: Chest x-ray abnormality detection by using artificial intelligence: A single-site retrospective study of deep learning model performance. BioMedInformatics (2023)
- <span id="page-53-6"></span>17. Maji, S., Kannala, J., Rahtu, E., Blaschko, M., Vedaldi, A.: Fine-grained visual classification of aircraft. Tech. rep. (2013)
- <span id="page-53-14"></span>18. Maron, R.C., Hekler, A., Haggenmüller, et al.: Model soups improve performance of dermoscopic skin cancer classifiers. European Journal of Cancer (2022)
- <span id="page-53-11"></span>19. Matena, M.S., Raffel, C.A.: Merging models with fisher-weighted averaging. Advances in Neural Information Processing Systems (2022)
- <span id="page-53-2"></span>20. Matsoukas, C., Haslum, J.F., Söderberg, M., Smith, K.: Is it time to replace cnns with transformers for medical images? arXiv preprint [arXiv:2108.09038](http://arxiv.org/abs/2108.09038) (2021)
- <span id="page-53-0"></span>21. Matsoukas, C., Haslum, J.F., Sorkhei, M., Söderberg, M., Smith, K.: What makes transfer learning work for medical images: Feature reuse & other factors. In: Proceedings of the IEEE/CVF Conference on CVPR (2022)
- <span id="page-53-1"></span>22. Morid, M.A., Borjali, et al.: A scoping review of transfer learning research on medical image analysis using imagenet. Computers in biology and medicine (2021)
- <span id="page-53-3"></span>23. Morid, M.A., Borjali, A., Del Fiol, G.: A scoping review of transfer learning research on medical image analysis using imagenet. Computers in Biology and Medicine 128 (2021)[.https://doi.org/10.1016/j.compbiomed.2020.104115](https://doi.org/10.1016/j.compbiomed.2020.104115)
- <span id="page-53-16"></span>24. Moussa, C., van Rijn, et al.: Hyperparameter importance of quantum neural networks across small datasets. In: International Conference on Discovery Science. Springer (2022)
- <span id="page-53-9"></span>25. Neyshabur, B., Sedghi, et al.: What is being transferred in transfer learning? Advances in neural information processing systems (2020)

- <span id="page-54-10"></span>26. Recht, B., Roelofs, R., Schmidt, L., Shankar, V.: Do cifar-10 classifiers generalize to cifar-10? arXiv preprint [arXiv:1806.00451](http://arxiv.org/abs/1806.00451) (2018)
- <span id="page-54-9"></span>27. Shih, G., Wu, C.C., Halabi, et al.: Augmenting the national institutes of health chest radiograph dataset with expert annotations of possible pneumonia. Radiology: Artificial Intelligence  $1(1)$ , e180041 (2019)
- <span id="page-54-1"></span>28. Stein, A., Wu, C., Carr, C., et al.: Rsna pneumonia detection challenge (2018), <https://kaggle.com/competitions/rsna-pneumonia-detection-challenge>
- <span id="page-54-0"></span>29. Tajbakhsh, N., Shin, J.Y., Gurudu, et al.: Convolutional neural networks for medical image analysis: Full training or fine tuning? IEEE TMI (2016)
- <span id="page-54-6"></span>30. Tenescu, A., Bercean, B.A., et al.: Averaging model weights boosts automated lung nodule detection on computed tomography. In: Proceedings of the 2023 13th International Conference on Bioscience, Biochemistry and Bioinformatics (2023)
- <span id="page-54-11"></span>31. Torralba, A., Fergus, R., Freeman, W.T.: 80 million tiny images: A large data set for nonparametric object and scene recognition. IEEE TPAMI (2008)
- <span id="page-54-2"></span>32. Tschandl, P., Rosendahl, C., Kittler, H.: The ham10000 dataset, a large collection of multi-source dermatoscopic images of common pigmented skin lesions. Scientific data  $5(1)$ , 1–9 (2018)
- <span id="page-54-8"></span>33. Wightman, R.: Pytorch image models. [https://github.com/rwightman/pytorch](https://github.com/rwightman/pytorch-image-models)[image-models](https://github.com/rwightman/pytorch-image-models) (2019)
- <span id="page-54-3"></span>34. Wortsman, M., Ilharco, et al.: Model soups: averaging weights of multiple finetuned models improves accuracy without increasing inference time. In: Proceedings of the 39th ICML. PMLR (2022), [https://proceedings.mlr.press/v162/](https://proceedings.mlr.press/v162/wortsman22a.html) [wortsman22a.html](https://proceedings.mlr.press/v162/wortsman22a.html)
- <span id="page-54-5"></span>35. Yadav, P., Tam, et al.: Resolving interference when merging models. arXiv preprint [arXiv:2306.01708](http://arxiv.org/abs/2306.01708) (2023)
- <span id="page-54-7"></span>36. Zhang, G., Lai, Z.F., et al.: A histopathological image classification method based on model fusion in the weight space. Applied Sciences (2023)
- <span id="page-54-4"></span>37. Zimmer, M., Spiegel, C., et al.: Sparse model soups: A recipe for improved pruning via model averaging. arXiv preprint [arXiv:2306.16788](http://arxiv.org/abs/2306.16788) (2023)

Image /page/55/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a gray bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# GBT: Geometric-Oriented Brain Transformer for Autism Diagnosis

Zhihao Peng<sup>1</sup> $\bullet$ [,](http://orcid.org/0000-0003-0997-9887) Zhibin He<sup>2</sup> $\bullet$ , Yu Jiang<sup>1</sup> $\bullet$ , Pengyu Wang<sup>1</sup> $\bullet$ , and Yixuan Yuan<sup>1( $\boxtimes$ [\)](http://orcid.org/0000-0002-0853-6948)</sup>

<sup>1</sup> The Chinese University of Hong Kong, Sha Tin, <NAME_EMAIL>

<sup>2</sup> Northwestern Polytechnical University, Xi'an 710072, China

**Abstract.** Human brains are typically modeled as networks of Regions of Interest (ROI) to comprehend brain functional Magnetic Resonance Imaging (fMRI) connectome for Autism diagnosis. Recently, various deep neural network-based models have been developed to learn the representation of ROIs, achieving impressive performance improvements. However, they (*i*) heavily rely on increasingly complex network architecture with an obscure learning mechanism, or *(ii)* solely utilize the crossentropy loss to supervise the training process, leading to sub-optimal performance. To this end, we propose a simple and effective Geometricoriented Brain Transformer (GBT) with the Attention Weight Matrix Approximation (AWMA)-based transformer module and the geometricoriented representation learning module for brain fMRI connectome analysis. Specifically, the AWMA-based transformer module selectively removes the components of the attention weight matrix with smaller singular values, aiming to learn the most relevant and representative graph representation. The geometric-oriented representation learning module imposes low-rank intra-class compactness and high-rank inter-class diversity constraints on learned representations to promote that to be discriminative. Experimental results on the ABIDE dataset validate that our method GBT consistently outperforms state-of-the-art approaches. The code is available at [https://github.com/CUHK-AIM-Group/GBT.](https://github.com/CUHK-AIM-Group/GBT)

**Keywords:** Autism diagnosis · Regions of Interest · Representation learning · Brain transformer network

# 1 Introduction

Autism is an ineradicable neurodevelopmental disability characterized by challenges with social skills and communication [\[7,](#page-63-0)[10](#page-63-1)[,18](#page-64-0)[,20](#page-64-1),[30\]](#page-64-2), where functional Magnetic Resonance Imaging (fMRI) is a powerful neuroimaging tool that depicts human brains as networks of Regions of Interest (ROIs) to enhance the interpretation and assessment of Autism diagnosis. In brain fMRI connectome analysis, some ROIs can co-activate or co-deactivate simultaneously when performing cognitive-related tasks such as action, language, and vision. Based on this pattern, brain ROIs can be classified into diverse functional modules to analyze diseases towards their diagnosis, progress understanding, and treatment. In recent decades, due to the powerful representation capability of deep learning, a series of brain-aware specialized deep neural networks have been designed for brain fMRI connectome analysis. Early works designed Convolutional Neural Network (CNN)-based brain networks with specific convolutional filters to consider the locality structure of the brain [\[14](#page-64-3)[,16](#page-64-4),[26\]](#page-64-5). Recently, Graph Neural Network (GNN)-based brain networks [\[11](#page-63-2)[,17](#page-64-6),[22](#page-64-7)[–24\]](#page-64-8) and Transformer (TF)-based brain networks [\[12](#page-63-3)[,13,](#page-64-9)[32\]](#page-65-0) have attracted widespread attention due to their powerful graph representation learning capability, which is well fit the non-euclidean properties of brain fMRI data.

Although numerous deep neural networks are deliberately well-designed to conduct effective representation learning for Autism diagnosis and have achieved competitive classification performance  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$  $[1-3, 6, 21, 25, 28, 29]$ , there remain several unsolved challenges with two main limitations. Firstly, existing works heavily rely on increasingly complex network architecture with an obscure learning mechanism to implicitly learn representations of ROIs, which may stick into overfitting problems due to the introduced inductive biases resulting from sophisticated structures. Thus, it is expected that the network could emphatically focus on the principal component information of brain fMRI data, allowing us to study the most relevant and representative graph representation to reveal the interactions and functional organization between different brain regions. Secondly, they solely utilize the cross-entropy loss to conduct supervised learning of the encoder and the classifier, ignoring the presumable blurred boundaries issue within cross-boundary samples, leading to sub-optimal performance. Such a restriction inspires us to identify and compare brain function differences between different individuals to achieve distinguishable guidance toward embedding representation learning, promoting the learned representation to be discriminative.

To this end, we propose a novel Geometric-oriented Brain Transformer (GBT) with the Attention Weight Matrix Approximation (AWMA)-based transformer module and the geometric-oriented representation learning module for brain fMRI connectome analysis. Specifically, the AWMA-based transformer module focuses on learning the most relevant and representative graph representation of brain ROIs by selectively removing the components of the attention weight matrix with smaller singular values. Moreover, the geometric-oriented representation learning module imposes low-rank intra-class compactness and high-rank inter-class diversity constraints on the learned embedding representation to make it discriminative. Notably, such a discriminative representation is capable of fitting the natural geometric properties of brain data that subjects having the same disorder share similar brain network patterns, which means disorder-specific representations across instances are preferable. Experimental results on the ABIDE dataset demonstrate that the proposed method GBT consistently outperforms state-of-the-art (SOTA) approaches, e.g., GBT outperforms the second-best comparisons with a 6.00% improvement on ACC. In

Image /page/57/Figure/1 description: This figure illustrates a deep learning framework for autism diagnosis using fMRI data. The left panel shows the input processing, starting with fMRI scans, extracting signal series from five ROIs, and then computing a Pearson Correlation Coefficient Matrix. The central panel details the model architecture, which includes an AWMA-based Transformer Module, Multi-Head Self Attention, Concatenation, and a Linear layer, feeding into a Geometric-oriented Representation Learning Module. The right panel elaborates on the Geometric-oriented Representation Learning Module, highlighting 'Low-Rank Compactness' and 'High-Rank Diversity' concepts, which ultimately lead to autism diagnosis represented by an illustration of a doctor and a child. The bottom right section provides a detailed view of the AWMA-based Transformer Module, showing the computation of Q, K, and V matrices, and the calculation of attention weights (w\_i^A and w\_i^LR) using linear transformations and matrix operations, culminating in the output Z\_i.

<span id="page-57-0"></span>**Fig. 1.** Illustration of our method GBT, which mainly includes the AWMA-based transformer module and the geometric-oriented representation learning module.

addition, we conduct a series of ablation studies on each component of GBT to verify its effectiveness. In summary, our contributions are as follows:

- We propose a novel AWMA-based transformer module to learn the most relevant and representative representation for brain fMRI connectome analysis.
- We propose a novel geometric-oriented representation learning module that combines low-rank intra-class compactness and high-rank inter-class diversity to enhance the discriminative capability of the representation.
- Both proposed modules are plug-and-play for the existing brain transformer network architecture.
- Experimental results on ABIDE validate the effectiveness of our method GBT in Autism diagnosis, e.g., GBT achieves a 6.00% ACC improvement than SOTA approaches.

# 2 Proposed Method

In this section, we introduce our network in detail, where the overall network architecture is shown in Fig. [1.](#page-57-0) Specifically, our network consists of the following parts: an AWMA-based transformer module to conduct the matrix approximation toward the attention weight matrix of the transformer encoder and a geometric-oriented representation learning module with low-rank intraclass compactness and high-rank inter-class diversity constraints to improve the embedding learning capability of our network.

## 2.1 Problem Definition

In brain fMRI connectome analysis, human brains are popularly depicted as networks of ROIs given an atlas, where the set of neural connections is defined as the Pearson correlation coefficient matrix  $\mathbf{X} \in \mathbb{R}^{v \times v}$  of pairwise ROIs between the blood-oxygen-level-dependent (BOLD) signal series. *v* is the number of ROIs. To fit the natural topological properties of brain data, one of the most commonly used paradigms is to employ a vanilla transformer to obtain the graph representation  $\mathbf{Z}_l$  with *l* being the number of layers. For  $\mathbf{Z}_i$  on the *i*-th layer, three full-connected layers, parametrized by layer-specific weight matrices  $\mathbf{W}^Q \in \mathbb{R}^{v \times d^Q}, \mathbf{W}^K \in \mathbb{R}^{v \times d^K},$  and  $\mathbf{W}^V \in \mathbb{R}^{v \times d^V},$  are introduced to obtain the query matrix  $Q$ , the key matrix  $K$ , and the value matrix  $V$ , respectively. After that, the LeakyReLU activation function  $\mathcal{F}^{leak}$  [\[19](#page-64-14)] is applied on the multiplication between  $\mathbf{Z}_{i-1}$  and the corresponding weight matrices. The softmax function  $\mathcal{F}^{soft}$  is then utilized to obtain the attention weight matrix  $\mathbf{W}^{\mathbf{A}}_i$  of the transformer encoder. Afterward, the output is normalized by a multi-head self-attention module and a concatenation operation, denoted as <sup>F</sup>*mcat*.

The corresponding expressions are formulated as

<span id="page-58-0"></span>
$$
\mathbf{Z}_{i} = \mathcal{F}^{meat} \left( \mathbf{W}_{i}^{A} \mathbf{V} \right) \mathbf{W}_{i}, \text{ s.t. } \mathbf{W}_{i}^{A} = \mathcal{F}^{\text{soft}} \left( \frac{\mathbf{Q} \mathbf{K}^{T}}{\sqrt{d^{K}}} \right), \mathbf{Z}_{0} = \mathbf{X}, \quad (1)
$$

$$
\mathbf{Q} = \mathcal{F}^{leak} \left( \mathbf{Z}_{i-1} \mathbf{W}^{Q} \right), \mathbf{K} = \mathcal{F}^{leak} \left( \mathbf{Z}_{i-1} \mathbf{W}^{K} \right), \mathbf{V} = \mathcal{F}^{leak} \left( \mathbf{Z}_{i-1} \mathbf{W}^{V} \right).
$$

## 2.2 AWMA-Based Transformer Module

To avoid the over-fitting problem, we propose an AWMA-based transformer module for developing a robust brain network to learn the most relevant and representative graph representation, as shown in Fig.  $1(a)$  $1(a)$ . Specifically, we replace the attention weight matrix  $\mathbf{W}_i^{\mathbf{A}}$  of the transformer encoder at the *i*-th layer with its rank *k* approximation  $\mathbf{W}_i^{\text{LR}}$  being

<span id="page-58-1"></span>
$$
\mathbf{W}_i^{\mathbf{LR}} = \mathbf{L}_k \mathbf{\Sigma}_k \mathbf{R}_k^{\mathsf{T}}, \quad \text{s.t.} \quad \mathbf{W}_i^{\mathbf{A}} = \mathbf{L} \mathbf{\Sigma} \mathbf{R}^{\mathsf{T}}, \tag{2}
$$

where  $\mathbf{L}, \mathbf{L}_k, \boldsymbol{\Sigma}, \boldsymbol{\Sigma}_k, \mathbf{R}^T$ , and  $\mathbf{R}_k$  is the left-singular matrix, the matrix formed by the first *k* columns of **L**, a diagonal matrix containing the singular values, the *k*-th principal sub-matrix of  $\Sigma$ , the conjugate transpose of the right-singular matrix, and the matrix formed by the first *k* columns of **R**, respectively. It is well-known that Eckart-Young-Mirsky theorem [\[8\]](#page-63-7) has proven singular value decomposition is the optimal solution of the matrix approximation [\[31](#page-65-1)] by removing the components with smaller singular values. By combining the Eqs. [\(1\)](#page-58-0) and [\(2\)](#page-58-1), the learned representation can be formulated as

$$
\mathbf{Z}_{i} = \mathcal{F}^{meat} \left( \mathbf{W}_{i}^{\mathrm{LR}} \mathbf{V} \right) \mathbf{W}_{i}.
$$
 (3)

Image /page/59/Figure/1 description: The image displays two 3D scatter plots, labeled (a) and (b). Plot (a) shows a cluster of points spread across a wide range of values on the x, y, and z axes, with colors transitioning from purple to blue. The axes are labeled from -6 to 2 on the x-axis, -4 to 2 on the y-axis, and -10 to 20 on the z-axis. Plot (b) presents two distinct clusters of points. One cluster is a curved band of blue points, and the other is a more vertical, planar cluster of purple points. The axes for plot (b) are labeled from -0.4 to 0.2 on the x-axis, -0.4 to 0.2 on the y-axis, and -2 to 2 on the z-axis.

<span id="page-59-1"></span>**Fig. 2.** Visualization of the skewed synthetic data with two classes. (a) Synthetic data involving cross-boundary samples. (b) Geometric-oriented representation with low-rank intra-class compactness and high-rank inter-class diversity. To avoid overfitting, we used the rank k approximation with SVD, the dropout regularization technique with dropping-out neurons, and the pooling strategy with aggregating local information.

## 2.3 Geometric-Oriented Representation Learning Module

To enforce the learned representation to be discriminative, we design a geometricoriented representation learning module with low-rank intra-class compactness and high-rank inter-class diversity constraints, named the geometric-oriented loss function. Specifically, we impose the low-rank constraint on the learned embedding representation within the same class, making the intra-class samples compact. Moreover, we encourage the whole features with inter-class diversity based on a high-rank constraint. Specifically, let  $\mathbf{Z}^c$  denote the subspace matrix formed by the columns of  $\mathbf{Z}_l$  that lies in the *c*-th class, the geometric-oriented loss function is as follows,

$$
\min_{\mathbf{Z}_l} \sum_{i=1}^c rank\left(\mathbf{Z}_i\right) - rank\left(\mathbf{Z}_l\right),\tag{4}
$$

where the rank function is inherently non-convex, making it computationally complex to optimize directly. Thus, we relax the rank function to its convex formulation, also known as (a.k.a.) the nuclear norm, i.e.,

<span id="page-59-0"></span>
$$
\min_{\mathbf{Z}_l} \sum_{i=1}^c |\mathbf{Z}_i|_* - |\mathbf{Z}_l|_*\,,\tag{5}
$$

where the nuclear norm of a matrix is defined as the sum of its singular values. More details of the relationship between the minimum-rank solution and the nuclear norm minimization can be found in the published theoretical justifications  $[9,27]$  $[9,27]$  $[9,27]$ . By minimizing Eq.  $(5)$ , the learned representation can simultaneously achieve intra-class compactness and inter-class diversity, explicitly satisfying the brain fMRI data property that these subjects with the same disorder share similar brain network patterns. Figure [2](#page-59-1) shows via two illustrative examples based on a synthetic dataset the visualization of using the geometric-oriented loss function as the objective function to qualitatively verify the methodology of the proposed geometric-oriented representation learning module. Finally, for the classification task, we first employ the MLP classifier  $\mathcal{F}_{\Theta}$  to conduct supervised learning via the cross-entropy loss  $\mathcal{F}^{ce}(\mathbf{Y}, \mathcal{F}_{\Theta}(\mathbf{Z}_l))$  with **Y** being the ground truth label, where the cross-entropy loss is the most widely used loss function for supervised learning of the transformer encoder and the classifier  $[1,12]$  $[1,12]$  $[1,12]$ . Furthermore, we additionally exploit the proposed geometric-oriented loss Eq. [\(5\)](#page-59-0) to supervise the whole training process, which can be written as

$$
\min_{\mathbf{Z}_l} \sum_{i=1}^c |\mathbf{Z}_i|_* - |\mathbf{Z}_l|_* + \mathcal{F}^{ce}(\mathbf{Y}, \mathcal{F}_{\Theta}(\mathbf{Z}_l)). \tag{6}
$$

# 3 Experiments

## 3.1 Datasets

Previous works [\[1,](#page-63-4)[12](#page-63-3)] commonly conduct the study of Autism on the open-source dataset Autism Brain Imaging Data Exchange (ABIDE), which is a collaborative initiative involving 17 international imaging sites [\[4\]](#page-63-9). Thus, we conduct experiments on ABIDE to understand the neural bases of Autism. Specifically, it aggregates and openly shares brain fMRI data of 1009 subjects where 516 individuals were diagnosed with Autism. The region definition is based on Craddock 200 atlas [\[5\]](#page-63-10). This dataset comprises structural and resting-state fMRI data and extensive phenotypic information, which are anonymous adhering to HIPAA guidelines and the 1000 Functional Connectomes Project/INDI protocols.

## 3.2 Compared Methods

We evaluate the effectiveness of our method by comparing it with one CNNbased brain network (BrainNetCNN [\[14](#page-64-3)]), three GNN-based brain networks (BrainGNN [\[17\]](#page-64-6), FBNETGEN [\[11\]](#page-63-2), BrainGB [\[6\]](#page-63-6)), and three TF-based brain networks (Graphormer [\[32\]](#page-65-0), BrainNetTF [\[12](#page-63-3)], and Com-BrainTF [\[1](#page-63-4)]).

## 3.3 Evaluation Metrics

We evaluate all the models on four commonly used evaluation metrics, i.e., the area under the receiver operating characteristic curve (AUC), accuracy (ACC), sensitivity (SEN), and specificity (SPE), where a higher value indicates a superior classification performance.

## 3.4 Training Procedure

For fair comparisons, we adopt [\[12](#page-63-3)] as our backbone network to conduct graph representation learning, where we employ a two-layer multi-head self-attention module with the number of attention heads being 4, the batch size being 64, and the epoch number being 200. We use Adam [\[15\]](#page-64-16) with an initial learning rate of 1*e* − 4 and a weight decay of 1*e* − 4. For the datasets, we randomly split 70% for training, 10% for validation, and 20% for testing, which is the same as the splits/evaluation strategy as the SOTA ones  $[1,12]$  $[1,12]$  $[1,12]$ . We perform the experiments five times and report the average results with their standard deviations (i.e., mean  $\pm$  std). For compared approaches, we directly report the performances provided in the original papers  $[1,12]$  $[1,12]$  $[1,12]$ . The model is implemented with PyTorch on NVIDIA GeForce RTX 4090.

## 3.5 Compared Results

Table [1](#page-61-0) shows the comparisons between the proposed method and seven compared approaches with four metrics on the ABIDE dataset, where we have the following observations: (*i*) Our method GBT achieves almost the best performance on all metrics and outperforms the SOTA approaches. For example, GBT achieves a 6.00% improvement on ACC compared with the second-best method. (*ii*) The TF-based brain networks perform significantly better than the CNNbased and GNN-based brain networks on almost all the metrics. For instance, on SEN, the critical metric for diagnostic tests referring to true positive rate, our TF-based method outperforms the best GNN-based network FBNETGEN [\[11](#page-63-2)] and CNN-based network BrainNetCNN [\[14\]](#page-64-3) by 15.51\% and 16.41\%, respectively. (*iii*) Compared with FBNETGEN, which uses group losses to extract GNN features, our main innovation is utilizing global rank-aware constraints to impose intra-class and inter-class rank constraints into the brain-aware transformer, effectively exploring the natural geometric properties of brain data and making a 14.52% SPE improvement. (*iiii*) Our method GBT achieves better performance than the baseline BrainNetTF [\[12](#page-63-3)], demonstrating the effectiveness of the AWMA-based transformer module and the proposed geometric-oriented representation learning module. Concretely, GBT improves 4.54% on AUC, 7.50% on ACC, 7.71% on SEN, and 7.62% on SPE.

<span id="page-61-0"></span>

| <b>Table 1.</b> Performance comparisons, where the <b>bolded</b> and underlined values indicate |  |  |
|-------------------------------------------------------------------------------------------------|--|--|
| the best and the second-best results, respectively.                                             |  |  |

| Type       | CNN-based                             | GNN-based                           |                      |                     |                            | TF-based                   |                                  |                   |  |
|------------|---------------------------------------|-------------------------------------|----------------------|---------------------|----------------------------|----------------------------|----------------------------------|-------------------|--|
|            | Method BrainNetCNN<br>[Neuroimage'17] | BrainGNN<br>$\left  \right $ MIA'21 | FBNETGEN<br>[MDL'22] | BrainGB<br>[TMI'22] | Graphormer<br>[NeurIPS'21] | BrainNetTF<br>[NeurIPS'22] | $Com-BrainTF$ Our<br>[MICCAI'23] |                   |  |
| AUC        | $74.90 \pm 02.40$                     | $62.40 \pm 03.50$                   | $75.60 \pm 01.20$    | $69.70 \pm 03.30$   | $63.50 \pm 03.70$          | $80.20 \pm 01.00$          | $79.60 \pm 03.80$                | $84.74 + 04.57$   |  |
| ACC        | $67.80 \pm 02.70$                     | $59.40 \pm 02.30$                   | $68.00 + 01.40$      | $63.60 + 01.90$     | $60.80 \pm 02.70$          | $71.00 \pm 01.20$          | $72.50 \pm 04.40$                | $78.50 \pm 06.50$ |  |
| <b>SEN</b> | $63.80 \pm 09.70$                     | $36.70 + 24.00$                     | $64.70 + 08.70$      | $63.70 + 08.30$     | $78.70 \pm 22.30$          | $72.50 \pm 05.20$          | $80.10 \pm 05.80$                | $80.21 + 09.38$   |  |
| <b>SPE</b> | $71.00 \pm 10.20$                     | $[70.70 + 19.30]$                   | $62.40 \pm 09.20$    | $60.40 \pm 10.10$   | $36.70 \pm 23.50$          | $69.30 \pm 06.50$          | $65.70 \pm 06.40$                | $76.92 + 03.85$   |  |

## 3.6 Ablation Study

We conduct comprehensive ablation studies, where the experimental results are listed in Table [2.](#page-62-0) Specifically, the first row (**I**) denotes the model [\[12](#page-63-3)], i.e., our

backbone network, with the cross-entropy loss. The second row (**II**) denotes our backbone network with the AWMA-based transformer module and the crossentropy loss. The third row (**III**) denotes a variant of our network that conducts supervised learning with the second-term high-rank loss and the cross-entropy loss, i.e.,  $\min_{\mathbf{Z}_l} (-|\mathbf{Z}_l|_*) + \mathcal{F}^{ce} (\mathbf{Y}, \mathcal{F}_{\Theta} (\mathbf{Z}_l))$ . The fourth row  $(\mathbf{IV})$  denotes a variant of our network that utilizes the first-term low-rank loss and the cross-entropy loss to supervise the training process, i.e.,  $\min_{\mathbf{Z}_l} \sum_{i=1}^c |\mathbf{Z}_i|_* + \mathcal{F}^{ce}(\mathbf{Y}, \mathcal{F}_{\Theta}(\mathbf{Z}_l))$ . The fifth row (**Our**) is our full method. From Table [2,](#page-62-0) we have the following observations: (*i*) The comparisons between (**I**) and (**II**) illustrate the effectiveness of the proposed AWMA-based transformer module among all metrics. For example, it achieves a 5.29% improvement on the SEN metric. (*ii*) By comparing the results in (**II**) and (**III**), we can observe that the utilization of the highrank constraint has performance degradation. The possible reason is that the samples lack intra-class compactness, resulting in reduced discriminative power between different nodes. (*iii*) The comparisons of **Our** method with (**III**) and (**IV**) demonstrate that simultaneously considering the low-rank intra-class compactness and high-rank inter-class diversity could promote learning a discriminative representation, improving the classification performance. For example, **Our** method obtains 10.10% performance improvement over **III** on SPE.

<span id="page-62-0"></span>**Table 2.** Ablation studies of the proposed geometric-oriented representation learning module (i.e., low-rank and high-rank constraints) and the AWMA-based transformer module, where  $\chi$  and  $\chi$  in each row indicate the non-use and use of the corresponding component, respectively. The best results are highlighted with **bold**.

|       | Low-rank               | High-rank              | AWMA                   | AUC                  | ACC                  | SEN                  | SPE                  |
|-------|------------------------|------------------------|------------------------|----------------------|----------------------|----------------------|----------------------|
| (I)   | Image: Red X           | Image: Red X           | Image: Red X           | 80.20 ± 01.00        | 71.00 ± 01.20        | 72.50 ± 05.20        | 69.30 ± 06.50        |
| (II)  | Image: Red X           | Image: Red X           | Image: Green Checkmark | 81.19 ± 02.33        | 76.20 ± 00.98        | 77.79 ± 11.29        | 72.40 ± 09.17        |
| (III) | Image: Red X           | Image: Green Checkmark | Image: Green Checkmark | 79.79 ± 02.63        | 71.60 ± 03.20        | 76.91 ± 10.48        | 66.82 ± 14.89        |
| (IV)  | Image: Green Checkmark | Image: Red X           | Image: Green Checkmark | 80.30 ± 03.13        | 76.25 ± 02.17        | 78.75 ± 12.95        | 71.17 ± 13.54        |
| Our   | Image: Green Checkmark | Image: Green Checkmark | Image: Green Checkmark | <b>84.74 ± 04.57</b> | <b>78.50 ± 06.50</b> | <b>80.21 ± 09.38</b> | <b>76.92 ± 03.85</b> |

# 4 Conclusion

This paper proposes a novel transformer-based brain network, GBT, to learn discriminative graph representations across brain ROIs. Specifically, GBT combines an AWMA-based transformer module to conduct the matrix approximation toward the attention weight matrix of the transformer encoder and a geometricoriented representation learning module to consider the intra-class compactness and inter-class diversity, aiming to understand brain fMRI connectome for Autism diagnosis. Experimental results on ABIDE demonstrate its superiority over SOTA methods, validating the effectiveness of the proposed AWMA-based transformer module and geometric-oriented representation learning module. In the future, we will exploit the Neurosynth platform to conduct neuroimaging meta-analysis to promote biomarker discovery.

**Acknowledgments.** This work was supported by Hong Kong Research Grants Council (RGC) General Research Fund 14204321.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-63-4"></span>1. Bannadabhavi, A., Lee, S., Deng, W., Ying, R., Li, X.: Community-aware transformer for autism prediction in fmri connectome. In: Medical Image Computing and Computer-Assisted Intervention. pp. 287–297. Springer (2023)
- 2. Bessadok, A., Mahjoub, M.A., Rekik, I.: Symmetric dual adversarial connectomic domain alignment for predicting isomorphic brain graph from a baseline graph. In: Medical Image Computing and Computer-Assisted Intervention. pp. 465–474. Springer (2019)
- <span id="page-63-5"></span>3. Cai, M., Li, M., Xiong, Z., Zhao, P., Li, E., Tang, J.: An advanced deep learning framework for video-based diagnosis of asd. In: Medical Image Computing and Computer-Assisted Intervention. pp. 434–444 (2022)
- <span id="page-63-9"></span>4. Craddock, C., Benhajali, Y., Chu, C., Chouinard, F., Evans, A., Jakab, A., Khundrakpam, B.S., Lewis, J.D., Li, Q., Milham, M., et al.: The neuro bureau preprocessing initiative: open sharing of preprocessed neuroimaging data and derivatives. Frontiers in Neuroinformatics **7**(27), 5 (2013)
- <span id="page-63-10"></span>5. Craddock, R.C., James, G.A., Holtzheimer III, P.E., Hu, X.P., Mayberg, H.S.: A whole brain fmri atlas generated via spatially constrained spectral clustering. Human Brain Mapping **33**(8), 1914–1928 (2012)
- <span id="page-63-6"></span>6. Cui, H., Dai, W., Zhu, Y., Kan, X., Gu, A.A.C., Lukemire, J., Zhan, L., He, L., Guo, Y., Yang, C.: Braingb: A benchmark for brain network analysis with graph neural networks. IEEE Transactions on Medical Imaging **42**(2), 493–506 (2022)
- <span id="page-63-0"></span>7. D'Souza, N.S., Nebel, M.B., Crocetti, D., Wymbs, N., Robinson, J., Mostofsky, S., Venkataraman, A.: A deep-generative hybrid model to integrate multimodal and dynamic connectivity for predicting spectrum-level deficits in autism. In: Medical Image Computing and Computer-Assisted Intervention. pp. 437–447. Springer (2020)
- <span id="page-63-7"></span>8. Eckart, C., Young, G.: The approximation of one matrix by another of lower rank. Psychometrika **1**(3), 211–218 (1936)
- <span id="page-63-8"></span>9. Jain, P., Meka, R., Dhillon, I.: Guaranteed rank minimization via singular value projection. Advances in Neural Information Processing Systems **23** (2010)
- <span id="page-63-1"></span>10. Jones, W., Klin, A.: Attention to eyes is present but in decline in 2–6-month-old infants later diagnosed with autism. Nature **504**(7480), 427–431 (2013)
- <span id="page-63-2"></span>11. Kan, X., Cui, H., Lukemire, J., Guo, Y., Yang, C.: Fbnetgen: Task-aware gnn-based fmri analysis via functional brain network generation. In: International Conference on Medical Imaging with Deep Learning. pp. 618–637. PMLR (2022)
- <span id="page-63-3"></span>12. Kan, X., Dai, W., Cui, H., Zhang, Z., Guo, Y., Yang, C.: Brain network transformer. Advances in Neural Information Processing Systems **35**, 25586–25599 (2022)

- <span id="page-64-9"></span>13. Kang, E., Heo, D.W., Suk, H.I.: Prototype learning of inter-network connectivity for asd diagnosis and personalized analysis. In: Medical Image Computing and Computer-Assisted Intervention. pp. 334–343 (2022)
- <span id="page-64-3"></span>14. Kawahara, J., Brown, C.J., Miller, S.P., Booth, B.G., Chau, V., Grunau, R.E., Zwicker, J.G., Hamarneh, G.: Brainnetcnn: Convolutional neural networks for brain networks; towards predicting neurodevelopment. NeuroImage **146**, 1038– 1049 (2017)
- <span id="page-64-16"></span>15. Kingma, D.P., Ba, J.: Adam: A method for stochastic optimization. arXiv preprint [arXiv:1412.6980](http://arxiv.org/abs/1412.6980) (2014)
- <span id="page-64-4"></span>16. Li, X., Dvornek, N.C., Zhuang, J., Ventola, P., Duncan, J.S.: Brain biomarker interpretation in asd using deep learning and fmri. In: Medical Image Computing and Computer-Assisted Intervention. pp. 206–214. Springer (2018)
- <span id="page-64-6"></span>17. Li, X., Zhou, Y., Dvornek, N., Zhang, M., Gao, S., Zhuang, J., Scheinost, D., Staib, L.H., Ventola, P., Duncan, J.S.: Braingnn: Interpretable brain graph neural network for fmri analysis. Medical Image Analysis **74**, 102233 (2021)
- <span id="page-64-0"></span>18. Loth, E., Spooren, W., Ham, L.M., Isaac, M.B., Auriche-Benichou, C., Banaschewski, T., Baron-Cohen, S., Broich, K., Boelte, S., Bourgeron, T., et al.: Identification and validation of biomarkers for autism spectrum disorders. Nature Reviews Drug Discovery **15**(1), 70–70 (2016)
- <span id="page-64-14"></span>19. Maas, A.L., Hannun, A.Y., Ng, A.Y., et al.: Rectifier nonlinearities improve neural network acoustic models. In: International Conference on Machine Learning. p. 3. Atlanta, GA (2013)
- <span id="page-64-1"></span>20. Nightingale, S.: Autism spectrum disorders. Nature Reviews Drug Discovery **11**(10), 745 (2012)
- <span id="page-64-10"></span>21. Parisot, S., Ktena, S.I., Ferrante, E., Lee, M., Guerrero, R., Glocker, B., Rueckert, D.: Disease prediction using graph convolutional networks: application to autism spectrum disorder and alzheimer's disease. Medical Image Analysis **48**, 117–130 (2018)
- <span id="page-64-7"></span>22. Peng, Z., Liu, H., Jia, Y., Hou, J.: Attention-driven graph clustering network. In: ACM International Conference on Multimedia. pp. 935–943 (2021)
- 23. Peng, Z., Liu, H., Jia, Y., Hou, J.: Adaptive attribute and structure subspace clustering network. IEEE Transactions on Image Processing **31**, 3430–3439 (2022)
- <span id="page-64-8"></span>24. Peng, Z., Liu, H., Jia, Y., Hou, J.: Egrc-net: Embedding-induced graph refinement clustering network. IEEE Transactions on Image Processing **32**, 6457–6468 (2023)
- <span id="page-64-11"></span>25. Price, T., Wee, C.Y., Gao, W., Shen, D.: Multiple-network classification of childhood autism using functional connectivity dynamics. In: Medical Image Computing and Computer-Assisted Intervention. pp. 177–184. Springer (2014)
- <span id="page-64-5"></span>26. Rakić, M., Cabezas, M., Kushibar, K., Oliver, A., Llado, X.: Improving the detection of autism spectrum disorder by combining structural and functional mri information. NeuroImage: Clinical **25**, 102181 (2020)
- <span id="page-64-15"></span>27. Recht, B., Fazel, M., Parrilo, P.A.: Guaranteed minimum-rank solutions of linear matrix equations via nuclear norm minimization. SIAM Review pp. 471–501 (2010)
- <span id="page-64-12"></span>28. Rosenberg, M.D., Finn, E.S.: How to establish robust brain–behavior relationships without thousands of individuals. Nature Neuroscience **25**(7), 835–837 (2022)
- <span id="page-64-13"></span>29. Wang, L., Li, G., Shi, F., Cao, X., Lian, C., Nie, D., Liu, M., Zhang, H., Li, G., Wu, Z., et al.: Volume-based analysis of 6-month-old infant brain mri for autism biomarker identification and early diagnosis. In: Medical Image Computing and Computer-Assisted Intervention. pp. 411–419. Springer (2018)
- <span id="page-64-2"></span>30. Wang, M., Zhang, D., Huang, J., Shen, D., Liu, M.: Low-rank representation for multi-center autism spectrum disorder identification. In: Medical Image Computing and Computer-Assisted Intervention. pp. 647–654. Springer (2018)

152 Z. Peng et al.

- <span id="page-65-1"></span>31. Ye, J.: Generalized low rank approximations of matrices. In: Proceedings of the twenty-first international conference on Machine learning. p. 112 (2004)
- <span id="page-65-0"></span>32. Ying, C., Cai, T., Luo, S., Zheng, S., Ke, G., He, D., Shen, Y., Liu, T.Y.: Do transformers really perform badly for graph representation? Advances in Neural Information Processing Systems **34**, 28877–28888 (2021)

Image /page/66/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below. The icon is a gray circle with a darker gray ribbon or bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# Gradient Guided Co-Retention Feature Pyramid Network for LDCT Image Denoising

Li Zho[u](http://orcid.org/0000-0001-6989-8080) **D**, Dayang Wang, Yongshun Xu, Shuo Han, Bahareh Morovati, Shuyi Fan, and Hengyong  $Yu^{(\boxtimes)}\bullet$  $Yu^{(\boxtimes)}\bullet$  $Yu^{(\boxtimes)}\bullet$ 

University of Massachusetts Lowell, Lowell, MA 01854, USA hengyong\<EMAIL>

Abstract. Low-dose computed tomography (LDCT) reduces the risks of radiation exposure but introduces noise and artifacts into CT images. The Feature Pyramid Network (FPN) is a conventional method for extracting multi-scale feature maps from input images. While upper layers in FPN enhance semantic value, details become generalized with reduced spatial resolution at each layer. In this work, we propose a Gradient Guided Co-Retention Feature Pyramid Network (G2CR-FPN) to address the connection between spatial resolution and semantic value beyond feature maps extracted from LDCT images. The network is structured with three essential paths: the bottom-up path utilizes the FPN structure to generate the hierarchical feature maps, representing multiscale spatial resolutions and semantic values. Meanwhile, the lateral path serves as a skip connection between feature maps with the same spatial resolution, while also functioning feature maps as directional gradients. This path incorporates a gradient approximation, deriving edge-like enhanced feature maps in horizontal and vertical directions. The topdown path incorporates a proposed co-retention block that learns the high-level semantic value embedded in the preceding map of the path. This learning process is guided by the directional gradient approximation of the high-resolution feature map from the bottom-up path. Experimental results on the clinical CT images demonstrated the promising performance of the model. Our code is available at: [https://github.com/](https://github.com/liz109/G2CR-FPN.) [liz109/G2CR-FPN.](https://github.com/liz109/G2CR-FPN.)

**Keywords:** LDCT denoising  $\cdot$  Retention  $\cdot$  Feature pyramid  $\cdot$  Directional gradients

# 1 Introduction

Compared with the normal-dose computed tomography (NDCT), low-dose computed tomography (LDCT) reduces the risks of ionizing radiation exposure but introduces noise and artifacts into the reconstructed images. A general solution is to develop denoising techniques to reduce or eliminate undesirable noise from CT images to improve their clarity and diagnostic values. Along this direction, deep learning models have been investigated for CT denoising, which learns an intrinsic feature map between noisy and clean CT images [\[7,](#page-74-0)[8,](#page-75-0)[28](#page-76-0)].

A pyramid structure [\[11\]](#page-75-1), illustrated as the bottom-up path in Fig.  $1(A)$  $1(A)$ , stands out as a widely employed backbone for feature extraction. Compared with the columnar structure  $[2]$ , where feature maps maintain the same size as the input image, the pyramid structure is cost-effective for addressing dense prediction tasks. This is because (1) the memory and computational costs are relatively low for the same input image size  $[24]$  $[24]$ , and  $(2)$  feature maps are at different spatial scales and channel complexities, enabling the extraction of both coarse-grained and fine-grained information [\[15](#page-75-2)]. This versatility is advantageous for tasks at pixel level, where objects of interest may exhibit variations in size or scale within images while preserving consistent patterns, as often encountered in CT images. In leveraging feature maps learned from a neural network, many researchers have explored the utilization of the attention mechanism [\[22\]](#page-75-3). The self-attention operation depicts the intra-feature learning for a given input, while the guided-attention operation learns the inter-feature interactions across multiple inputs. Both of the operations, along with their combined form known as co-attention [\[13,](#page-75-4)[27](#page-76-2)[,29](#page-76-3)], exhibit competitive performance in modeling various computer vision tasks [\[2,](#page-74-1)[12](#page-75-5)[,21](#page-75-6)[,23](#page-75-7)]. Notably, Sun *et al.* [\[19](#page-75-8)] recently redefined the attention mechanism as the retention mechanism in Natural Language Processing, showcasing competitive performance with the traditional attention mechanism and overcoming the quadratic computation complexity associated. As a followup work, in this paper we explore the possibility of the retention mechanism in pixel-level dense tasks, specifically focusing on inter- and intra-feature learning within the pyramid structure network. Targeting on model-based LDCT image denoising, previous studies often resulted in over-smoothed outputs with blurry edges. While edge enhancement methods [\[4,](#page-74-2)[5,](#page-74-3)[10](#page-75-9)] are introduced to preserve structural details in CT images, they compromise the diversity of feature maps and are confined to a fixed perceptual field, resembling columnar-like structures.

Thus, we draw inspiration from hierarchical feature maps, introducing the coretention mechanism in two parts. First, we propose a self-retention operation for intra-feature learning, focusing on the integral perceptual field of a feature map itself. This operation captures dependencies within the feature map, enhancing its interpretability. Meanwhile, we introduce a guided-retention operation for inter-feature learning, emphasizing mutual perceptual fields between two feature maps. This operation uncovers interdependencies between a feature map with high-level semantic value and a feature map with high resolution. Specifically, we employ a directional gradient approximation method, similar to the edge detection method [\[18](#page-75-10)], on the high-resolution feature map. This process generates edge-like enhanced feature maps for adaptive guided feature learning, wherein the method decomposes the high-resolution features into horizontal and vertical directions. As a result, our proposed model, the Gradient Guided Co-Retention Feature Pyramid Network (G2CR-FPN), effectively addresses the

LDCT denoising through comprehensive feature learning and detail preservation. The key contributions are summarized as follows: 1) We propose a G2CR-FPN, attempting to generate multi-scale feature maps and learn intrinsic information by bridging feature maps with high-level semantic value and high resolution for LDCT denoising. The model exhibits promising performance in the experimental results. 2) We introduce a co-retention mechanism for pixel-level dense tasks, comprising self-retention and guided-retention operations. The new mechanism focuses on intra- and inter-feature learning within hierarchical feature maps. 3) We validate the effectiveness of directional gradient approximation in feature maps. The introduced edge-like directions enhance structures within the feature maps, mitigating over-smoothing issues.

# 2 Methods

## 2.1 Overall Structure

Our objective is to introduce feature learning techniques for the pyramid structure of feature maps, enhancing interpretability and preserving details for pixellevel denoising tasks. The overall structure of G2CR-FPN is illustrated in Fig. [1\(](#page-68-0)A), and a typical level of G2CR-FPN, depicting the intra- and inter-feature learning among feature maps, is shown in Fig.  $1(B)$  $1(B)$ .

Image /page/68/Figure/5 description: The image displays a diagram illustrating a neural network architecture. Panel (A) shows a Feature Pyramid Network with a legend indicating input, output, bottom-up path, top-down path, and lateral path. Panel (B) details a level of G2CR-FPN, featuring an encoder with convolutional layers and a decoder with gradient processing (vertical and horizontal) and multi-scale retention (MSR) modules. Panel (C) zooms in on the Multi-Scale Retention module, showing its components including retention, concatenation, group normalization (GN), linear layers, and swish activation. A legend at the bottom explains symbols for upsampling, reshape, patch embedding, and element-wise addition.

<span id="page-68-0"></span>Fig. 1. Overview of the G2CR-FPN. (A) the overall structure and paths of the model. (B) the *i*-th level of G2CR-FPN, where connections between feature maps labeled as *BU* represent the bottom-up path, connections between *T D* represent the top-down path, and connections between *BU* and *T D* represent the lateral path. (C) multi-scale retention within a Decoder.

The proposed structure comprises five paths. To begin with, an image of size  $H \times W \times 1$  is processed into a feature map  $BU_1 \in \mathbb{R}^{H_1 \times W_1 \times C_1}$  through an input path. The path consists of two convolutional layers, each is connected to batch normalization and ReLU activation. Moving through the bottom-up path, a residual encoder is introduced to control the scales of feature maps,

resulting in more generalized details. Meanwhile, the lateral path acts as a skip connection between feature maps with the same spatial resolution, while also emphasizing directional gradient approximation represented as horizontal and vertical directions. Advancing through the top-down path, a co-retention decoder is introduced to incorporate high-level semantic value. This learning process is guided by directional gradient approximation from the high-resolution feature map. In the end, an output path integrates features into a denoised image of size  $H \times W \times 1$ . The output path consists of operations including layer normalization, a convolutional layer, batch normalization, and another convolutional layer.

## 2.2 Residual Encoder

The bottom-up path involves feed-forward convolutional computations for feature extraction, generating a pyramid of feature maps at various scales using a spatial shrinking factor  $(\alpha)$  and a channel expanding factor  $(\beta)$ . Inspired by the ResNets [\[6\]](#page-74-4), a residual encoder is employed to learn input features. The encoder includes a residual connection and sequences of convolutional layers, batch normalization, and ReLU activation (see Fig.  $1(B)$  $1(B)$ ). Specifically, the second convolutional layer in the block reduces spatial dimensions using a stride of α, while the last layer and residual connection expand channels via β. We define a stack of encoders as a level in the pyramid structure where the output sizes of the encoders are consistent, and opt for the last feature map at each level. We denote an encoder layer as  $Encoder_i$ , with the input feature map labeled  $BU_i \in$  $\mathbb{R}^{H_i \times W_i \times C_i}$  and the output feature map as  $BU_{i+1} \in \mathbb{R}^{(H_i/\alpha) \times (\tilde{W}_i/\alpha) \times (\beta C_i)}$ .

## 2.3 Directional Gradient Approximation

The purpose of the gradient approximation in the lateral path is to emphasize feature semantics with high spatial frequency and enrich the expression of feature maps in multiple view directions. In this work, we introduce a modification of the Sobel edge detection operator [\[18](#page-75-10)], which computes image intensity gradients through isotropic  $3 \times 3$  kernels. The kernels now work with learnable factors, enabling adaptive optimization during the training process to generate edge-like feature maps. In the directional gradient operation  $Gradient_i$ , gradient approximations are computed in horizontal and vertical kernels, respectively. The gradient approximations are expressed as

$$
BU_i^{Hor} = (w_i^{Hor} \cdot \begin{bmatrix} -1 & 0 & 1 \ -2 & 0 & 2 \ -1 & 0 & 1 \end{bmatrix}) * BU_i, \quad BU_i^{Ver} = (w_i^{Ver} \cdot \begin{bmatrix} -1 & -2 & -1 \ 0 & 0 & 0 \ 1 & 2 & 1 \end{bmatrix}) * BU_i, \tag{1}
$$

where  $BU_i^{Hor}$  and  $BU_i^{Ver} \in \mathbb{R}^{H_i \times W_i \times C_i}$  respectively denote the horizontal and vertical gradient approximations,  $w_i^{Hor}$  and  $w_i^{Ver} \in \mathbb{R}$  are learnable factors, and ∗ denotes the convolution operation.

## 2.4 Co-Retention Decoder

The retention mechanism is a pivotal module in the Retentive Network [\[19\]](#page-75-8), encoding sequences in an autoregressive manner and exhibiting dual forms of recurrence and parallelism. Given an input sequence  $X = [x_1, ..., x_{|\pi|}] \in \mathbb{R}^{|x| \times d}$ with an embedding dimension  $d$ , we formulate a sequence-to-sequence mapping  $f: X_n \mapsto O_n$  along with linear representations of value  $(V_n)$ , query  $(Q_n)$  and key  $(K_n)$  through state  $S_n$ . The linear representations are formulated as

$$
V = XW^{V}, \ Q = (XW^{Q}) \odot \Theta, \ K = (XW^{K}) \odot \overline{\Theta}, \tag{2}
$$

where  $W^V$ ,  $W^Q$ ,  $W^K \in \mathbb{R}^{d \times d}$  are learnable weighting matrices,  $\Theta$  is Extrapolatable Position Embedding (xPos) proposed by Sun *et al.* [\[20\]](#page-75-11),  $\overline{\Theta}$  is the conjugate of  $\Theta$ , and  $\odot$  is the element-wise multiplication.

Considering the parallel manner, the retention mechanism can be written as

$$
Retention(X) = GN((QKT \odot D)V), \quad D_{nm} = \begin{cases} \gamma^{n-m}, & n \ge m \\ 0, & n < m \end{cases}, \tag{3}
$$

where GN is short for Group Normalization [\[26\]](#page-76-4),  $D \in \mathbb{R}^{|x| \times |x|}$  denotes a decay mask, and  $\gamma$  is a scalar.

Instead of employing a single parallel retention mechanism to obtain representations as value, query, and key, characterized by parameter matrices  $W^V$ ,  $W^Q$ and  $W^K$ , it is advantageous to project the representations  $H$  times in each layer, each time using different parameter matrices  $W_h^V, W_h^Q$  and  $W_h^K \in \mathbb{R}^{d_{head} \times d_{head}}$ , where  $d_{head} = d/H$ . In addition, the retention is expanded into multi-scale retention (MSR), involving  $H$  retention heads operating in parallel with the scalars  $\Gamma = \{\gamma_h\}_{h=1}^H$ , respectively. Then, the outputs are concatenated and normalized. The MSR layer is defined as

<span id="page-70-0"></span>The following equations are:

$$
Head_{h} = Retention(X, \gamma_{h}),
$$
  
$$
Y = GN(Concat(Head_{1}, ..., Head_{H})),
$$
  
$$
MSR(X) = (Swish(XW^{G}) \odot Y)W^{O},
$$
  
(4)

where  $W^G$  and  $W^O \in \mathbb{R}^{d \times d}$  are learnable matrices,  $GN$  normalizes each head separately, and Swish [\[16](#page-75-12)] activation improves the non-linearity of layers. To incorporate the co-retention mechanism, which is designed to receive different sequences of token embeddings as input, we have to redefine the function as

$$
Head_h = Retention(V, Q, K, \gamma_h),
$$
  
\n
$$
MSR(V, Q, K) = (Swish(VW^G) \odot Y)W^O,
$$
\n(5)

where  $Y$  follows the same operations as shown in Eq.  $(4)$ .

In the following, we introduce the components of a co-retention decoder  $Decoder_i$ , as shown in Fig. [1.](#page-68-0) The decoder comprises self-retention and guidedretention operations. Operations are alternatively connected, accompanied by layer normalization (LN) and a residual connection. For an input feature map  $TD_{i+1} \in \mathbb{R}^{(H_i/\alpha)\times (W_i/\alpha)\times (\beta C_i)}$ , we use an upsampling layer followed by a patch embedding operation before each decoder block. Inspired by the ViT [\[2](#page-74-1)], the patch embedding operation involves partitioning the input into 2D patches and flattening/reshaping them into the patch embeddings  $\tilde{T}D_{i+1} \in \mathbb{R}^{K_i^2 \times P^2 \times C_i}$ . Specifically, we partition an input into  $K_i^2$  evenly spaced patches by a fixed patch size of  $P \times P$  in different levels, enabling inter-feature learning within matched perceptual fields among feature maps. The input is partitioned using a kernel size  $K_i = H_i/P = W_i/P$  in a convolutional layer. The embedding operation is also employed in the directional gradient approximations, resulting  $\tilde{BU}_{i}^{Hor}$  and  $\tilde{BU}^{Ver}_{i}$ . The embedded horizontal and vertical approximations respectively serve as  $Q$  and  $K$  in the second MSR of the decoder.

# 3 Experiments and Results

# 3.1 Experimental Setup

Datasets: We conduct experiments on the dataset from the 2016 NIH-AAPM-Mayo Clinic LDCT Grand Challenge [\[14\]](#page-75-13), consisting of 2,378 CT images with a slice thickness of 3.0 mm. The dataset was collected from ten different patients. We randomly select subject 'L506' for testing, and images from the remaining subjects are for training.

Implementation Details: In the G2CR-FPN model, there are five levels, denoted as  $L = 5$ . At each level, the structure includes a stack of two encoders  $(N = 2)$  in the BU path and a single decoder  $(M = 1)$  with  $H = 8$  heads in the TD path. The spatial factor ( $\alpha$ ) and the channel factor ( $\beta$ ) are both set to 2. The dimension of each patch is set as  $P = 32$ . The model is trained with MSE loss function and Adam optimizer with default settings for at most 200 epochs, and the best model with the minimal loss is saved. The learning rate is initially set as 0.001 and is halved for every 3,000 steps in the training stage.

Evaluation Metrics: For quantitative assessments, we employ two conventional metrics: root mean square error (RMSE) and structural similarity (SSIM) [\[25\]](#page-76-5). Additionally, we introduce the Edge Structural Similarity Index (E-SSIM) to evaluate the performance of the gradient approximation operation and the guided-retention operation. E-SSIM combines the SSIMs for both edge and nonedge regions, where  $E$ -SSIM = 0.5  $\times$  edge-SSIM + 0.5  $\times$  non-edge-SSIM. The edges are detected by the Sobel filter. The average results for the testing subject are presented within the Hounsfield Unit (HU) window of [−160, 240].

| Method          | RMSE $\downarrow$    | SSIM $\uparrow$     | E-SSIM $\uparrow$   | edge-SSIM $\uparrow$ |
|-----------------|----------------------|---------------------|---------------------|----------------------|
| <b>LDCT</b>     | $10.4833 \pm 1.5358$ | $0.9359 \pm 0.0170$ | $0.9164 \pm 0.0322$ | $0.9216 \pm 0.0334$  |
| <b>REDCNN</b>   | $9.0390 \pm 2.1339$  | $0.9466 \pm 0.0224$ | $0.9257 \pm 0.0283$ | $0.9287 \pm 0.0300$  |
| <b>MAPNN</b>    | $8.1332 \pm 1.6802$  | $0.9528 \pm 0.0188$ | $0.9303 \pm 0.0256$ | $0.9319 \pm 0.0267$  |
| <b>SwinIR</b>   | $7.5886 \pm 1.6717$  | $0.9556 \pm 0.0182$ | $0.9317 \pm 0.0248$ | $0.9341 \pm 0.0259$  |
| <b>SUNet</b>    | $7.3576 \pm 1.5899$  | $0.9584 \pm 0.0175$ | $0.9331 \pm 0.0250$ | $0.9376 \pm 0.0257$  |
| <b>CA-FPN</b>   | $7.7318 \pm 1.6450$  | $0.9552 \pm 0.0179$ | $0.9301 \pm 0.0249$ | $0.9328 \pm 0.0258$  |
| <b>G2CA-FPN</b> | $7.4341 \pm 1.6191$  | $0.9559 \pm 0.0186$ | $0.9346 \pm 0.0254$ | $0.9362 \pm 0.0263$  |
| <b>CR-FPN</b>   | $7.4127 \pm 1.5902$  | $0.9585 \pm 0.0172$ | $0.9340 \pm 0.0242$ | $0.9373 \pm 0.0251$  |
| <b>G2CR-FPN</b> | $7.0516 \pm 1.5358$  | $0.9602 \pm 0.0170$ | $0.9400 \pm 0.0253$ | $0.9420 \pm 0.0258$  |

<span id="page-72-0"></span>Table 1. Comparison with the SOTA and Ablation Study (mean±SDs). Bold:Best.

### 3.2 Experimental Results

To evaluate the performance of the proposed G2CR-FPN model, we compare it against several state-of-the-art models: REDCNN [\[1\]](#page-74-5), MAPNN [\[17](#page-75-14)], SwinIR [\[9\]](#page-75-15), and SUNet [\[3\]](#page-74-6). The parameters for the competing methods are configured according to the guidelines disclosed in the corresponding papers. Table [1](#page-72-0) summarizes the quantitative results, showing the mean±SDs (standard deviations) from the testing images using these methods. The LDCT images initially had the lowest scores due to the degradation from low dose radiation. All methods improved the scores, with G2CR-FPN achieving the best scores.

Moreover, a representative slice with lesions from Case L506 is selected to evaluate the performance of the aforementioned models. As shown in Fig. [2,](#page-73-0) the first row displays the denoised images from each model, with the red box indicating the region of interest (ROI) zoomed in the second row. It can be clearly observed that all the methods suppress image noise to various degrees. The SUNet and G2CR-FPN generate clearer noise-free images, and they can better discriminate low contrast regions in soft tissues than other models. To further differentiate the performance of the models, we conduct noise power spectrum (NPS) analysis on Fig. [2.](#page-73-0) As depicted in Fig.  $3(A)$  $3(A)$ , all images exhibit similar power spectra at low spatial frequencies, while differences became visible as frequencies increase. The G2CR-FPN achieves the lowest noise in the full frequency range. In summary, our proposed G2CR-FPN model is competitive with the state-of-the-art methods in LDCT denoising, evidenced by both quantitative metrics and qualitative visual results.

## 3.3 Ablation Study

As the aforementioned, the retention network is a successor to the transformer architecture. Our study aims to demonstrate the effectiveness of the Co-Retention module within the G2CR-FPN model by comparing it to the attention mechanism [\[22](#page-75-3)]. Specifically, we evaluate the Co-Retention decoder

Image /page/73/Picture/1 description: This image displays a comparison of different image reconstruction methods for CT scans of the liver. The top row shows full CT images with labels indicating the method and performance metrics (SSIM/RMSE). The methods compared are LDCT (0.8654/19.3131), REDCNN (0.8883/15.5584), MAPNN (0.9031/13.3570), SwinIR (0.9086/12.7017), SUNet (0.9131/12.1257), G2CR-FPN (Ours) (0.9169/11.6318), and NDCT (SSIM/RMSE). The bottom row shows magnified views of a specific region within the liver, highlighting the details and noise levels produced by each method. The NDCT image in the bottom row is marked with red arrows pointing to specific structures, suggesting areas of interest or improvement.

Fig. 2. Qualitative comparison from Case L506. The red boxes indicate the zoomed ROIs and the arrows point to the target lesions. The display window is [−160, 240] HU. (Color figure online)

<span id="page-73-0"></span>Image /page/73/Figure/3 description: The image displays two line graphs, labeled (A) and (B), both titled "Noise Power Spectrum (NPS)". Both graphs plot "Power Spectrum" on the y-axis against "Spatial Frequency" on the x-axis. Each graph shows multiple colored lines representing different methods: LDCT (blue), REDCNN (orange), MAPNN (green), SwinIR (brown), SUNet (purple), and G2CR-FPN (dark brown) in graph (A), and LDCT (blue), CA-FPN (orange), G2CA-FPN (green), CR-FPN (red), and G2CR-FPN (purple) in graph (B). Both graphs have a similar overall trend, with a sharp peak at low spatial frequencies followed by a decrease and then a leveling off. Red dashed vertical lines are present at spatial frequencies of approximately 20, 40, and 60 in graph (A), and approximately 20 and 40 in graph (B). Below each main graph, there is an inset graph that magnifies a portion of the data, showing more detailed fluctuations of the power spectrum for spatial frequencies roughly between 20 and 60.

<span id="page-73-1"></span>**Fig. 3.** Noise power spectrum analysis of the representative slice for  $(A)$  the comparison with the SOTA methods, and (B) the ablation study.

(G2CR-FPN) against the Co-Attention decoder (G2CA-FPN) within the architecture shown in Fig.  $1(B)$  $1(B)$ . Additionally, we aim to validate the impact of the trainable directional gradient approximation in the lateral path. To do so, we compare with models without gradient operations while maintaining consistent remaining structures, labeled as CR-FPN and CA-FPN respectively.

The quantitative results are also summarized in Table [1.](#page-72-0) The Co-Retentionbased models (G2CR-FPN and CR-FPN) outperform the Co-Attention-based models (G2CA-FPN and CA-FPN) in terms of both SSIM and RMSE. Furthermore, the inclusion of gradient operations leads to quantitative improvements in both G2CA-FPN and G2CR-FPN models. Although the improvement between G2CR-FPN and CR-FPN is small in terms of SSIM (0.0017), the RMSE is reduced by 5%, demonstrating that the combined mechanism is better and therefore the best model for LDCT denoising. A detailed examination of the outputs is provided in Fig.  $3(B)$  $3(B)$  in terms of NPS analysis. All the images denoised using our methods exhibit significantly lower power spectra than the LDCT image.

# 4 Conclusions

We introduce a novel gradient guided co-retention feature pyramid network (G2CR-FPN) for LDCT image denoising and demonstrate how the proposed directional feature gradient approximation and co-retention mechanisms cooperatively learn feature maps in high resolution and high semantic value. Specifically, we show how the gradient approximation operation perceives a feature map in horizontal and vertical directions, and how the co-retention mechanism can tackle high inter- and intra- feature interactions among different scales of levels in the pyramid structure network. Our experimental results indicate the potential of these mechanisms and achieve encouraging results for LDCT denoising.

Acknowledgments. This work was supported in part by NIH/NIBIB under grants R01EB032807 and R01EB034737, and NIH/NCI under grant R21CA264772.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-74-5"></span>1. Chen, H., Zhang, Y., Zhang, W., Liao, P., Li, K., Zhou, J., Wang, G.: Low-dose ct denoising with convolutional neural network. In: 2017 IEEE 14th International Symposium on Biomedical Imaging (ISBI 2017). pp. 143–146. IEEE (2017)
- <span id="page-74-1"></span>2. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-74-6"></span>3. Fan, C.M., Liu, T.J., Liu, K.H.: Sunet: swin transformer unet for image denoising. In: 2022 IEEE International Symposium on Circuits and Systems (ISCAS). pp. 2333–2337. IEEE (2022)
- <span id="page-74-2"></span>4. Gholizadeh-Ansari, M., Alirezaie, J., Babyn, P.: Deep learning for low-dose ct denoising using perceptual loss and edge detection layer. Journal of digital imaging 33, 504–515 (2020)
- <span id="page-74-3"></span>5. Han, S., Zhao, Y., Li, F., Ji, D., Li, Y., Zheng, M., Lv, W., Xin, X., Zhao, X., Qi, B., et al.: Dual-path deep learning reconstruction framework for propagation-based x-ray phase–contrast computed tomography with sparse-view projections. Optics Letters 46(15), 3552–3555 (2021)
- <span id="page-74-4"></span>6. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-74-0"></span>7. Immonen, E., Wong, J., Nieminen, M., Kekkonen, L., Roine, S., Törnroos, S., Lanca, L., Guan, F., Metsälä, E.: The use of deep learning towards dose optimization in low-dose computed tomography: A scoping review. Radiography  $28(1)$ , 208–214 (2022)

- <span id="page-75-0"></span>8. Kulathilake, K.S.H., Abdullah, N.A., Sabri, A.Q.M., Lai, K.W.: A review on deep learning approaches for low-dose computed tomography restoration. Complex & Intelligent Systems 9(3), 2713–2745 (2023)
- <span id="page-75-15"></span>9. Liang, J., Cao, J., Sun, G., Zhang, K., Van Gool, L., Timofte, R.: Swinir: Image restoration using swin transformer. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 1833–1844 (2021)
- <span id="page-75-9"></span>10. Liang, T., Jin, Y., Li, Y., Wang, T.: Edcnn: Edge enhancement-based densely connected network with compound loss for low-dose ct denoising. In: 2020 15th IEEE International Conference on Signal Processing (ICSP). vol. 1, pp. 193–198. IEEE (2020)
- <span id="page-75-1"></span>11. Lin, T.Y., Dollár, P., Girshick, R., He, K., Hariharan, B., Belongie, S.: Feature pyramid networks for object detection. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 2117–2125 (2017)
- <span id="page-75-5"></span>12. Liu, Z., Hu, H., Lin, Y., Yao, Z., Xie, Z., Wei, Y., Ning, J., Cao, Y., Zhang, Z., Dong, L., et al.: Swin transformer v2: Scaling up capacity and resolution. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 12009–12019 (2022)
- <span id="page-75-4"></span>13. Lu, J., Yang, J., Batra, D., Parikh, D.: Hierarchical question-image co-attention for visual question answering. Advances in neural information processing systems 29 (2016)
- <span id="page-75-13"></span>14. McCollough, C.H., Bartley, A.C., Carter, R.E., Chen, B., Drees, T.A., Edwards, P., Holmes III, D.R., Huang, A.E., Khan, F., Leng, S., et al.: Low-dose ct for the detection and classification of metastatic liver lesions: results of the 2016 low dose ct grand challenge. Medical physics 44(10), e339–e352 (2017)
- <span id="page-75-2"></span>15. Morovati, B., Lashgari, R., Hajihasani, M., Shabani, H.: Reduced deep convolutional activation features (r-decaf) in histopathology images to improve the classification performance for breast cancer diagnosis. arXiv preprint [arXiv:2301.01931](http://arxiv.org/abs/2301.01931) (2023)
- <span id="page-75-12"></span>16. Ramachandran, P., Zoph, B., Le, Q.V.: Swish: a self-gated activation function. arXiv preprint [arXiv:1710.05941](http://arxiv.org/abs/1710.05941) 7(1), 5 (2017)
- <span id="page-75-14"></span>17. Shan, H., Padole, A., Homayounieh, F., Kruger, U., Khera, R.D., Nitiwarangkul, C., Kalra, M.K., Wang, G.: Competitive performance of a modularized deep neural network compared to commercial algorithms for low-dose ct image reconstruction. Nature Machine Intelligence 1(6), 269–276 (2019)
- <span id="page-75-10"></span>18. Sobel, I.: An isotropic  $3 \times 3$  image gradient operater. Machine vision for threedimensional scenes pp. 376–379 (1990)
- <span id="page-75-8"></span>19. Sun, Y., Dong, L., Huang, S., Ma, S., Xia, Y., Xue, J., Wang, J., Wei, F.: Retentive network: A successor to transformer for large language models. arXiv preprint [arXiv:2307.08621](http://arxiv.org/abs/2307.08621) (2023)
- <span id="page-75-11"></span>20. Sun, Y., Dong, L., Patra, B., Ma, S., Huang, S., Benhaim, A., Chaudhary, V., Song, X., Wei, F.: A length-extrapolatable transformer. arXiv preprint [arXiv:2212.10554](http://arxiv.org/abs/2212.10554) (2022)
- <span id="page-75-6"></span>21. Touvron, H., Cord, M., Douze, M., Massa, F., Sablayrolles, A., Jégou, H.: Training data-efficient image transformers & distillation through attention. In: International conference on machine learning. pp. 10347–10357. PMLR (2021)
- <span id="page-75-3"></span>22. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, Ł., Polosukhin, I.: Attention is all you need. Advances in neural information processing systems 30 (2017)
- <span id="page-75-7"></span>23. Wang, D., Fan, F., Wu, Z., Liu, R., Wang, F., Yu, H.: Ctformer: convolutionfree token2token dilated vision transformer for low-dose ct denoising. Physics in Medicine & Biology 68(6), 065012 (2023)

- <span id="page-76-1"></span>24. Wang, W., Xie, E., Li, X., Fan, D.P., Song, K., Liang, D., Lu, T., Luo, P., Shao, L.: Pyramid vision transformer: A versatile backbone for dense prediction without convolutions. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 568–578 (2021)
- <span id="page-76-5"></span>25. Wang, Z., Bovik, A.C., Sheikh, H.R., Simoncelli, E.P.: Image quality assessment: from error visibility to structural similarity. IEEE transactions on image processing 13(4), 600–612 (2004)
- <span id="page-76-4"></span>26. Wu, Y., He, K.: Group normalization. In: Proceedings of the European conference on computer vision (ECCV). pp. 3–19 (2018)
- <span id="page-76-2"></span>27. Yu, Z., Yu, J., Cui, Y., Tao, D., Tian, Q.: Deep modular co-attention networks for visual question answering. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 6281–6290 (2019)
- <span id="page-76-0"></span>28. Zhang, F., Liu, J., Liu, Y., Zhang, X.: Research progress of deep learning in lowdose ct image denoising. Radiation Protection Dosimetry 199(4), 337–346 (2023)
- <span id="page-76-3"></span>29. Zhou, L., Luo, Y.: Deep features fusion with mutual attention transformer for skin lesion diagnosis. In: 2021 IEEE International Conference on Image Processing (ICIP). pp. 3797–3801. IEEE (2021)

Image /page/77/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a bookmark shape inside it. The circle is partially filled with a darker gray color, and the bookmark is a lighter gray color. The text is also in a lighter gray color.

# Gyri vs. Sulci: Core-Periphery Organization in Functional Brain Networks Organization in Functional Brain Networks

Xiaowei Yu<sup>1</sup>, Lu Zhang<sup>1</sup>, Chao Cao<sup>1</sup>, Tong Chen<sup>1</sup>, Yanjun Lyu<sup>1</sup>, Jing Zhang<sup>1</sup>, Tianming Liu<sup>2</sup>, and Dajiang Zhu<sup>1( $\overline{\otimes}$ )</sup>

<sup>1</sup> The University of Texas at Arlington, Arlington, TX 76019, USA {xxy1302,lu.zhang2,cxc0366,txc5603,yxl9168,jxz7537}@mavs.uta.edu, <EMAIL> <sup>2</sup> University of Georgia, Athens, GA 30602, USA <EMAIL>

Abstract. The human cerebral cortex is highly convoluted into convex gyri and concave sulci. It has been demonstrated that gyri and sulci are significantly different in their anatomy, connectivity, and function: besides exhibiting opposite shape patterns, long-distance axonal fibers connected to gyri are much denser than those connected to sulci, and neural signals on gyri are more complex in low-frequency while sulci are more complex in high-frequency. Although accumulating evidence shows significant differences between gyri and sulci, their primary roles in brain function have not been elucidated yet. To solve this fundamental problem, we design a novel Twin-Transformer framework to unveil the unique functional roles of gyri and sulci and their relationship in the whole brain function. Our Twin-Transformer framework adopts two structure-identical (twin) Transformers to disentangle spatial-temporal patterns of functional brain networks: one focuses on the spatial patterns and the other is on temporal patterns. The spatial transformer takes the spatially divided patches and generates spatial patterns, while the temporal transformer takes the temporally split patches and produces temporal patterns. We validated our Twin-Transformer on the HCP taskfMRI dataset, to elucidate the different roles of gyri and sulci in brain function. Our results suggest that gyri and sulci could work together in a core-periphery network manner, that is, gyri could serve as core networks for information gathering and distributing, while sulci could serve as periphery networks for specific local information processing. These findings have shed new light on our understanding of the brain's basic structural and functional mechanisms.

Keywords: Gyri and Sulci · Core-Periphery · Twin-Transformer

## 1 Introduction

Gyri and sulci are the standard morphological and anatomical nomenclature of the cerebral cortex (Fig. [1\)](#page-78-0) and are usually defined in anatomical domains [\[1\]](#page-85-0).

Image /page/78/Figure/1 description: This diagram illustrates a process for organizing brain networks. It begins with a 'Brain Grayordinate' which undergoes 'Segmentation' to produce a 'Sulci Mask' and a 'Gyri Mask'. Simultaneously, fMRI signals are fed into a 'Twin Transformer' which includes a 'Spatial Transformer' and a 'Temporal Transformer'. The outputs of the segmentation and the twin transformer converge to create 'Disentangled Brain Networks'. Finally, these disentangled networks are organized into 'Core-periphery Organized Brain Networks', visually represented by a central brain model with interconnected red nodes ('Core Networks') and blue nodes ('Periphery Networks'), with surrounding detailed views of these core and periphery network regions.

<span id="page-78-0"></span>Fig. 1. Core-periphery organized brain networks in gyri and sulci. The fMRI signals extracted from voxels are fed into the Twin-Transformer model. The disentangled brain networks are organized in a core-periphery manner, where gyri serve as the core network, and sulci serve as the periphery network.

Gyri and sulci serve as the basic building blocks to make up complex cortical folding patterns and are fundamental to realizing the brain's basic structural and functional mechanisms. Numerous efforts have been devoted to understanding the function-anatomy patterns of gyri and sulci from various perspectives, including genetics  $[2]$ , cell biology  $[3]$ , and neuroimaging  $[4,30]$  $[4,30]$ . It has been demonstrated consistently that gyri and sulci are significantly different in their anatomy, connectivity, and function. Several studies [\[6](#page-85-4),[19\]](#page-86-0) found that the formation of gyri/sulci may be closely related to the micro-structure of white matter. For example, diffusion tensor imaging (DTI) derived long-distance axonal fibers connected to gyri are significantly denser than those connected to sulci [\[11\]](#page-85-5). That is, the long-distance fiber terminations dominantly concentrate on gyri rather than sulci, and interestingly, this phenomenon is evolutionarily preserved across different primate species. Meanwhile, using functional magnetic resonance imaging (fMRI), a few functional measurements that can directly reflect brain functional activities on gyri and sulci have been explored, such as functional BOLD signals [\[4](#page-85-3)], correlation-based connectivity/interaction [\[5](#page-85-6)[,10](#page-85-7)[,20](#page-86-1)], and spatial distribution of functional networks [\[7](#page-85-8),[16\]](#page-86-2). Despite accumulating functional differences found between gyri and sulci, their basic roles as well as their relationship and interaction in the whole brain function have not been explored or elucidated yet.

To answer this fundamental question in brain science, we proposed a novel Twin-Transformer framework to explore and unveil the unique functional roles of gyri and sulci. Unlike traditional factorization-based approaches that assume linearity and independence, the transformer with self-attention mechanism [\[14](#page-86-3)] is an ideal backbone to characterize, represent and reveal the complex and deeply buried patterns in the observed brain functional data. The whole framework

is illustrated in Fig. [2.](#page-80-0) Our Twin-Transformer framework adopts two structureidentical (twin) Transformers to model and disentangle spatial-temporal patterns of gyri and sulci: one focuses on the spatial patterns and the other focuses on temporal patterns. To model the complex 4D (spatial-temporal) fMRI signals, within the framework, we designed a spatial transformer and a temporal transformer to disentangle and extract the patterns in both spatial and temporal domains from the original fMRI signals. After the model is well-trained, the spatial-temporal functional brain networks (FBNs) [\[15](#page-86-4)] specific to gyri and sulci can be recovered through gyri and sulci masks. We validated our Twin-Transformer on one of the largest brain imaging datasets (HCP task-fMRI gray-ordinate dataset), for the first time, to elucidate the different roles of gyri and sulci in brain function. Our results suggest that gyri and sulci could work together in a core-periphery network manner, that is, gyri could serve as core networks for information gathering and distributing in a global manner, while sulci could serve as periphery networks for specific local information processing. These findings have shed new light on our fundamental understanding of the brain's structural and functional mechanisms, provide further inspiration for brain-inspired neural network design [\[12](#page-86-5),[13\]](#page-86-6), and aid in the research of brain diseases [\[21](#page-86-7)[–29\]](#page-87-1). The contributions of this paper are summarized as follows:

- We proposed a novel Twin-Transformer framework to disentangle the spatialtemporal patterns of the functional brain networks from fMRI datasets.
- We used the proposed method to represent and unveil the fundamental functional roles of the two basic cortical folding patterns: gyri and sulci.
- We found that gyri and sulci may work together in a Core-Periphery network manner: gyri serve as core networks for information gathering and distributing, while the sulci serve as periphery networks for specific local information processing.

# 2 Methods

## 2.1 Data Preparation

We use the task fMRI (tfMRI) from the Human Connectome Project (HCP) dataset [\[17](#page-86-8)]. The publicly available preprocessed tfMRI data went through the minimal preprocessing pipelines specially designed for the HCP dataset [\[4](#page-85-3)]. The preprocessed tfMRI are 4D imaging data, which consists of a time series of 3D images of the brain. For emotion, gambling, language, motor, relational, social, and working memory (WM) task-fMRI, each voxel contains a series of brain signals of time length 176, 254, 316, 284, 232, 274, and 405. We rearrange the signals in each voxel into a 2D matrix. In this way, a 4D tfMRI imaging can be represented by a 2D matrix, where each row stores brain signals at each time step, and each column stores brain signals in a specific voxel (Fig. [2-](#page-80-0)a,b). We normalized the brain signals to zero mean and unit variance [\[18](#page-86-9)]. Since each subject of the preprocessed data has 59,412 voxels in standard gray ordinate space, the column dimension of the 2D matrix is 59,412. To facilitate patch partition, we

expand the space dimension according to our needs by adding zero vectors along the spatial dimension. For example, to disentangle the signal matrices into 50 spatial-temporal brain networks, the space dimension is extended from 59,412 to 59450 to divide the space dimension into 50 patches.

Image /page/80/Figure/2 description: The image displays a diagram illustrating the Twin-Transformer Framework for analyzing fMRI data. The framework is divided into five stages: (a) Brain Signal Extraction, where fMRI data is rearranged into a signal matrix; (b) Patch Division, which segments the signal matrix into spatial and temporal patches using space and time windows; (c) Position Encoding, which assigns spatial and temporal positions to these patches; (d) Spatial-Temporal Disentanglement, featuring separate Spatial and Temporal Transformers that process the patches; and (e) Reconstruction, where the disentangled spatial and temporal patterns are multiplied to reconstruct the signal matrix. The lower part of the diagram provides a more detailed view of the Spatial/Temporal Transformers (Twin), showing patch embedding, multi-head attention, and feed-forward layers with add & norm operations for both spatial and temporal patches.

<span id="page-80-0"></span>Fig. 2. Illustration of the proposed Twin-Transformer framework. Part (a) extracts the signals and rearranges them into a signal matrix. Part (b) shows the patch division of the signal matrix. Part (c) is the position encoding for the spatial and temporal patches. Part (d) is the Twin-Transformer, which includes spatial and temporal transformers for processing spatial and temporal patches. Part (e) is the reconstruction of the signal matrix from disentangled spatial and temporal patterns. The spatial/temporal transformers under the black dot line show the details of the Twin-Transformer.

### 2.2 Twin-Transformer

The architecture of the Twin-Transformer is illustrated in Fig. [2.](#page-80-0) There is a spatial and temporal transformer for disentangling spatial and temporal patterns of the brain networks, as shown in Fig. [2-](#page-80-0)d. The structure of the spatial transformer is identical to the temporal transformer, except they take spatial/temporal divided patches as input. For each input signal matrix, spatial patches are generated by shifting the spatial window along the space dimension, as illustrated by the orange arrow in Fig. [2-](#page-80-0)b, while temporal patches are generated by shifting the temporal window along the time dimension, as shown in the green arrow.

Specifically, within the spatial transformer, the self-attention operation across the spatial patches aims to learn the latent representations of spatial features and takes non-overlapping spatial patches as tokens to build attention across the spatial variant patches and generate spatial patterns. It divides the input signal matrix into P non-overlapping patches by shifting the sliding window (orange dotted box following orange arrow) from left to right along the space dimension. The size of the sliding window can be adjusted according to the size of the input data. Each spatial patch contains partial spatial but complete temporal information on the focal brain region. The  $P$  patches correspond to  $P$ patterns of brain networks. Patches are used as tokens, and each token is first fed into a linear projection layer to obtain the latent representation  $z_i \in \mathbb{R}^{1 \times D_1}$ and then the learnable spatial positional embedding,  $E_i^s \in \mathbb{R}^{1 \times D_1}$  is added to the representations of each input token. The spatial transformer encoder can be formulated as:

$$
Spa(Z) = MLP(MSA(LN(z_1^s||z_2^s||...||z_P^s)))
$$
\n(1)

where  $MSA()$  is the multi-head self-attention, MLP() represents multilayer perceptron, and LN() is layernorm.  $z_i^s = (z_i + E_i^s), i = 1, 2, ..., P$  and  $||$  denotes the stack operation.  $Spa(Z) \in P \times N$  is the output of the spatial Transformer, where P represents the number of brain networks, and  $N$  is the number of voxels in the brain.  $Spa(Z)$  models the activated status of voxels within each brain network.

The temporal transformer is designed to learn the latent representations of temporal patterns of brain networks. Similar to the spatial transformer, by shifting the sliding window (green dotted box following green arrow) from top to bottom along the time dimension, T non-overlapping temporal patches are generated. Each temporal patch contains partial temporal but complete spatial information of all the voxels. Correspondingly, the temporal transformer builds attention across the temporal variant patches and generates temporal features. The sliding window slides each unit of time, so the number of patches equals the length of the signals. After patch embedding and positional embedding, each patch is represented by  $z_i^t = (z_i + E_i^t), i = 1, 2, ..., T$ . The temporal self-attention module can be formulated as:

$$
Tem(Z) = MLP(MSA(LN(z_1^t || z_2^t || z_3^t || ... || z_T^t)))
$$
\n(2)

The outputs  $Tem(Z)$  of the temporal self-attention module have a dimension of  $Tem(Z) \in T \times P$ , where T represents the time length of the fMRI signals.  $Tem(Z)$  represents the temporal patterns of the brain networks. Taking  $Spa(Z)$ and  $Tem(Z)$  together, we can obtain both the spatial and temporal patterns of the brain networks of each subject.

## 2.3 Loss Function

There are two terms in the loss function. The first one is the signal matrix reconstruction loss. The whole framework is trained in a self-supervised manner, therefore, the input signal matrix can be reconstructed from the learned spatial and temporal patterns. This is crucial to ensure the learned spatial and temporal features can capture the complete spatial and temporal information of the input data. The reconstruction loss is formulated as:

$$
L_{reco} = \sum ||X - Tem(Z) \cdot Spa(Z)||_{L1}
$$
 (3)

where  $X$  is the input signal matrix, and we use the L1-norm to constrain the reconstruction term. In order to make spatial patterns distinct and limit the

scale of temporal patterns from being arbitrarily large, we add a normalization on temporal patterns, which is formulated as:

$$
L_{tem\_norm} = max(0, \frac{1}{P} \left( \sum_{i=1}^{P} ||Tem(Z_i[*, i])||_2 \right) - 1)
$$
\n(4)

Combining the two parts, the overall loss can be formulated as:

$$
L = L_{reco} + \alpha L_{tem\_norm}
$$
\n<sup>(5)</sup>

where the regularization parameter  $\alpha$  controls the influence of temporal normalization on the overall loss function.

Image /page/82/Figure/6 description: The image displays a grid of heatmaps, organized by subject and cognitive domain. There are five subjects labeled Subject1 through Subject5 on the left vertical axis. Across the top horizontal axis, there are six cognitive domains: EMOTION, MOTOR, GAMBLING, RELATIONAL, WM, SOCIAL, and LANGUAGE. Each subject has a row of six heatmaps, one for each cognitive domain. The heatmaps themselves are composed of many small colored squares, predominantly red and blue, arranged in a grid pattern. Some heatmaps have numerical labels associated with them, such as 16924, 17240, 17192, 16919, and 17173 on the left side, and 18445, 18483, 18521, 18363, and 18455 below the heatmaps for each subject. The overall impression is a visualization of data, likely representing connectivity or activity patterns, across different subjects and cognitive tasks.

<span id="page-82-0"></span>Fig. 3. Relationship matrix of gyri and sulci. Each row represents a subject. Each column represents a task. For each subject, the number of voxels in gyri and sulci are marked in red and blue. The connections between gyri-gyri, gyri-sulci, and sulci-sulci are shown in red, pink, and blue, respectively. These relationship matrices are generated under threshold 0.8. (Color figure online)

## 3 Results

Using the gray-ordinate fMRI signals of each subject, as input for the Twin-Transformer, the output of the well-train model includes spatial-temporal patterns, where spatial patterns can be interpreted as FBNs, and the corresponding temporal patterns can be treated as the representative signals of each FBN. Though signals on gyri and sulci are different, we set the same threshold. The activated voxels of gyri and sulci in each FBN can be obtained by applying the gyri and sulci mask to the FBN (Fig. [1\)](#page-78-0). We present the core-periphery organization of gyri and sulci, validation, and ablation study in the following subsections.

Table 1. The normalized independent probability of gyri and sulci brain networks under 100 patterns set, and the threshold is 0.8. The format is Mean(Variance).

<span id="page-83-0"></span>

| IP       | Emotion      | Motor        | Gambling     | Relational   | WM           | Social       | Language     |
|----------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
| $P_{GG}$ | $0.51(0.03)$ | $0.54(0.03)$ | $0.44(0.05)$ | $0.44(0.03)$ | $0.48(0.03)$ | $0.46(0.03)$ | $0.53(0.03)$ |
| $P_{GS}$ | $0.30(0.01)$ | $0.30(0.01)$ | $0.32(0.01)$ | $0.32(0.01)$ | $0.31(0.01)$ | $0.32(0.01)$ | $0.30(0.01)$ |
| $P_{SS}$ | $0.18(0.02)$ | $0.17(0.02)$ | $0.24(0.04)$ | $0.24(0.03)$ | $0.22(0.02)$ | $0.22(0.02)$ | $0.24(0.04)$ |

## 3.1 Core-Periphery Organization of Gyri and Sulci

We can identify the activated brain voxels in gyri and sulci whose weights are consistently above a pre-defined threshold across all spatial patterns. By connecting all the activated voxels, we can construct a relationship matrix of gyri and sulci and obtain their mapping on the brain surface. We randomly select 5 subjects and present their relationship matrix in Fig. [3.](#page-82-0) Since the number of voxels in gyri and sulci of different subjects are various, the size of the relationship matrices is various. In general, the relation matrix is sparse, which means only a few regions (voxels) are involved in a specific task at the same time. As shown in Fig. [3,](#page-82-0) the activated brain voxels in the gyri-gyri section incline to form larger and connected blocks or clusters, while the activated brain voxels in the sulci-sulci section tend to assemble as smaller and scattered patterns. Besides, compared to relational, other tasks, such as WM, social, language, and emotion involve larger sulcal regions that host more high-level functions.

To examine and prove the concept of the Core-Periphery organization of gyri and sulci, we compute the normalized independent probability (IP) P*GG*, P*SS* and  $P_{GS}$  for sub-matrices  $A_{GG}$ ,  $A_{SS}$ , and  $A_{GS}$  of the relationship matrix, which represents the interactions within gyri vertices (Core Network), sulci vertices (Periphery Network), and between gyri and sulci vertices (between Core and Periphery Networks). Independent probability [\[8](#page-85-9)] is defined as the probability that there is an edge between any pairs of nodes in a given matrix, and it is an

important measurement to indicate if the matrix or graph is organized as Core-Periphery pattern [\[9\]](#page-85-10). The independent probability and normalized independent probability are formulated as:

$$
I_{GG} = \frac{1_{A_{GG}}}{\|A_{GG}\|_1}, I_{GS} = \frac{1_{A_{GS}}}{\|A_{GS}\|_1}, I_{SS} = \frac{1_{A_{SS}}}{\|A_{SS}\|_1},
$$
  
\n
$$
P_{GG} = I_{GG}/I, P_{GS} = I_{GS}/I, P_{SS} = I_{SS}/I.
$$
\n(6)

where  $I = I_{GG} + I_{GS} + I_{SS}$ ,  $1_{Acc}$  represents the number of 1s in the sub-matrix of gyri-gyri and  $\|\bullet\|_1$  is the number of elements in this sub-matrix. The same procedure was applied to sub-matrices of gyri-sulci and sulci-sulci, respectively. We calculate the normalized independent probability, and the averaged results of group level are shown in Table [1.](#page-83-0) The results show that  $P_{GG} > P_{GS} > P_{SS}$ , which confirms that the derived brain networks of gyri and sulci have the coreperiphery organization.

## 3.2 Validation and Ablation Study

We conduct ablation studies to validate the robustness of the proposed Twin-Transformer framework and verify the core-periphery relationship of gyri and sulci under different thresholds. The normalized independent probability of the relationship matrix under thresholds of 0.7 and 0.9 are shown in Tables [2](#page-84-0) and [3,](#page-84-1) respectively. We can observe that the normalized independent probability of the relationship matrix of gyri and sulci under different experimental settings and pre-defined thresholds all satisfy  $P_{GG} > P_{GS} > P_{SS}$ , which further demonstrates that the gyri-sulci functional brain networks are organized in a core-periphery manner.

Table 2. The normalized independent probability of gyri and sulci brain networks under 100 patterns set, and the threshold is 0.9. The format is Mean(Variance).

<span id="page-84-0"></span>

| IP       | Emotion    | Motor      | Gambling   | Relational | WM         | Social     | Language   |
|----------|------------|------------|------------|------------|------------|------------|------------|
| $P_{GG}$ | 0.59(0.07) | 0.57(0.07) | 0.53(0.11) | 0.49(0.08) | 0.52(0.07) | 0.55(0.07) | 0.65(0.06) |
| $P_{GS}$ | 0.28(0.03) | 0.28(0.02) | 0.29(0.03) | 0.31(0.02) | 0.30(0.02) | 0.29(0.03) | 0.25(0.03) |
| $P_{SS}$ | 0.14(0.04) | 0.15(0.04) | 0.18(0.08) | 0.20(0.06) | 0.18(0.05) | 0.16(0.05) | 0.10(0.03) |

Table 3. The normalized independent probability of gyri and sulci brain networks under 100 patterns set, and the threshold is 0.7. The format is Mean(Variance).

<span id="page-84-1"></span>

| IP       | Emotion    | Motor      | Gambling   | Relational | WM         | Social     | Language   |
|----------|------------|------------|------------|------------|------------|------------|------------|
| $P_{GG}$ | 0.43(0.02) | 0.42(0.02) | 0.44(0.01) | 0.45(0.02) | 0.41(0.02) | 0.42(0.03) | 0.41(0.01) |
| $P_{GS}$ | 0.32(0.01) | 0.33(0.01) | 0.32(0.01) | 0.32(0.01) | 0.33(0.01) | 0.33(0.01) | 0.33(0.01) |
| $P_{SS}$ | 0.24(0.01) | 0.25(0.02) | 0.24(0.01) | 0.23(0.01) | 0.26(0.01) | 0.25(0.02) | 0.26(0.01) |

# 4 Conclusion

In this work, we proposed a novel data-driven Twin-Transformer framework and applied it to the HCP gray-ordinate tfMRI dataset to characterize the roles of cortical gyri and sulci on the functional brain networks. With this framework, we can disentangle the spatial and temporal patterns from the brain signals, providing us the possibility to analyze the difference between gyri and sulci. The most important finding in this study is that we identified the core-periphery relationship between gyri and sulci, as well as the corresponding core-periphery brain networks. Our results show that core-periphery networks broadly exist between gyri and sulci across different subjects and tasks. Overall, our proposed Twin-Transformer contributes to a better understanding of the roles of gyri and sulci as core and periphery in brain architecture.

Acknowledgments. This work was supported by National Institutes of Health (R01AG075582 and RF1NS128534).

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-85-0"></span>1. Jiang, X., Zhang, T., Zhang, S., Kendrick, K.M. and Liu, T.: Fundamental functional differences between gyri and sulci: implications for brain function, cognition, and behavior. Psychoradiology  $1(1)$ , 23–41 (2021)
- <span id="page-85-1"></span>2. Richiardi, J., et al.: Correlated gene expression supports synchronous activity in brain networks. Science 348(6240), 1241–1244 (2015)
- <span id="page-85-2"></span>3. Gertz, C.C. and Kriegstein, A.R.: Neuronal migration dynamics in the developing ferret cortex. J. Neurosci. 35(42), 14307–14315 (2015)
- <span id="page-85-3"></span>4. Liu, H., et al.: The cerebral cortex is bisectionally segregated into two fundamentally different functional units of gyri and sulci. Cerebral Cortex 29(10), 4238–4252 (2019)
- <span id="page-85-6"></span>5. Deng, F., et al.: A functional model of cortical gyri and sulci. Brain Struct. Funct. 219(4), 1473–1491 (2014)
- <span id="page-85-4"></span>6. Hilgetag, C.C., Barbas, H.: Developmental mechanics of the primate cerebral cortex. Anatomy Embryol. 210(5), 411–417 (2005)
- <span id="page-85-8"></span>7. Lv, J., et al.: Holistic atlases of functional networks and interactions reveal reciprocal organizational architecture of cortical function. IEEE Trans. Biomed. Eng. 62(4), 1120–1131 (2014)
- <span id="page-85-9"></span>8. Cucuringu, M., Rombach, P., Lee, S.H., Porter, M.A.: Detection of core-periphery structure in networks using spectral methods and geodesic paths. Eur. J. Appl. Math. 27(6), 846–887 (2016)
- <span id="page-85-10"></span>9. Rombach, M.P., Porter, M.A., Fowler, J.H. and Mucha, P.J.: Core-periphery structure in networks. SIAM J. Appl. Math. 74(1), 167–190 (2014)
- <span id="page-85-7"></span>10. Yu, X., et al.: Longitudinal infant functional connectivity prediction via conditional intensive triplet network. In: MICCAI, pp. 255–264 (2022)
- <span id="page-85-5"></span>11. Nie, J., et al.: Axonal fiber terminations concentrate on gyri. Cerebral cortex 22(12), 165–178 (2012)

- <span id="page-86-5"></span>12. Yu, X., Zhang, L., Zhu, D., Liu, T.: Robust core-periphery constrained transformer for domain adaptation. arXiv preprint [arXiv:2308.13515](http://arxiv.org/abs/2308.13515) (2023)
- <span id="page-86-6"></span>13. Yu, X., et al.: Core-periphery principle guided redesign of self-attention in transformers. arXiv preprint [arXiv:2303.15569](http://arxiv.org/abs/2303.15569) (2023).
- <span id="page-86-3"></span>14. Dosovitskiy, A., et al.: An image is worth 16x16 words: transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-86-4"></span>15. Yu, X., Zhang, L., Zhao, L., Lyu Y., Liu, T., Zhu, D.: Disentangling spatial-temporal functional brain networks via twin-transformers. arXiv preprint [arXiv:2204.09225](http://arxiv.org/abs/2204.09225) (2022)
- <span id="page-86-2"></span>16. Zhang, W., et al.: Experimental comparisons of sparse dictionary learning and independent component analysis for brain network inference from fMRI data. IEEE Trans. Biomed. Eng. 66(1), 289–299 (2018)
- <span id="page-86-8"></span>17. Glasser, M.F., Smith, S.M., Marcus, D.S., Andersson, J.L., Auerbach, E.J., Behrens, T.E., Coalson, T.S., Harms, M.P., Jenkinson, M., Moeller, S. and Robinson, E.C.: The human connectome project's neuroimaging approach. Nature neuroscience, 19(9), pp.1175-1187 (2016).
- <span id="page-86-9"></span>18. Hopfinger, J.B., Büchel, C., Holmes, A.P., Friston, K.J.: A study of analysis parameters that influence the sensitivity of event-related fMRI analyses. Neuroimage 11(4), 326–333 (2000)
- <span id="page-86-0"></span>19. Li, G., Liu, T., Ni, D., Lin, W., Gilmore, J.H., Shen, D.: Spatiotemporal patterns of cortical fiber density in developing infants, and their relationship with cortical thickness. Hum. Brain Map. 36(12), 5183–5195 (2015)
- <span id="page-86-1"></span>20. Zhang, L., Yu, X., Lyu, Y., Liu, T., Zhu, D.: Representative functional connectivity learning for multiple clinical groups in Alzheimer's disease. In: IEEE 20th International Symposium on Biomedical Imaging, pp. 1–5 (2023)
- <span id="page-86-7"></span>21. Yu, X., Zhang, L., Lyu, Y., Liu, T., Zhu, D.: Supervised deep tree in Alzheimer's disease. In: IEEE 20th International Symposium on Biomedical Imaging, pp. 1–5 (2023)
- 22. Lyu, Y., Yu, X., Zhu, D., Zhang, L.: Classification of alzheimer's disease via vision transformer. In: Proceedings of the 15th International Conference on PErvasive Technologies Related to Assistive Environments, pp. 463–468 (2022)
- 23. Lyu, Y., Yu, X., Zhang, L., Zhu, D.: Classification of mild cognitive impairment by fusing neuroimaging and gene expression data. In: Proceedings of the 14th International Conference on PErvasive Technologies Related to Assistive Environments, pp. 26–32 (2021)
- 24. Yu, X., Scheel, N., Zhang, L., Zhu, D.C. Zhang, R., Zhu, D.: Free water in T2 FLAIR white matter hyperintensity lesions. Alzheimer's & Dementia 17, e057398 (2021)
- 25. Zhang, L., et al.: Deep fusion of brain structure-function in mild cognitive impairment. Med. Image Anal. 72, 102082 (2021)
- 26. Zhang, L., Wang, L. Zhu, D.: April. Jointly analyzing Alzheimer's disease related structure-function using deep cross-model attention network. In: 2020 IEEE 17th International Symposium on Biomedical Imaging (ISBI), pp. 563–567. IEEE (2020)
- 27. Zhang, L., Zaman, A., Wang, L., Yan, J., Zhu, D.: A cascaded multi-modality analysis in mild cognitive impairment. In: Machine Learning in Medical Imaging: 10th International Workshop, MLMI 2019, Held in Conjunction with MICCAI 2019, Proceedings, vol. 10, pp. 557–565 (2019)
- 28. Zhang, L., Wang, L., Liu, T., Zhu, D.: Disease2Vec: encoding alzheimer's progression via disease embedding tree. Pharmacol. Res 199, 107038 (2024)

174 X. Yu et al.

- <span id="page-87-1"></span>29. Zhang, L., Na, S., Liu, T., Zhu, D., Huang, J.: Multimodal deep fusion in hyperbolic space for mild cognitive impairment study. In: Greenspan, H., (ed.) et al. Medical Image Computing and Computer Assisted Intervention - MICCAI 2023, MICCAI 2023, LNCS, vol. 14224, pp. 674–684. Springer, Cham (2023). [https://doi.org/10.](https://doi.org/10.1007/978-3-031-43904-9_65) [1007/978-3-031-43904-9\\_65](https://doi.org/10.1007/978-3-031-43904-9_65)
- <span id="page-87-0"></span>30. Zhang, L., et al.: Cortex2vector: anatomical embedding of cortical folding patterns. Cerebral Cortex 33(10), 5851–5862 (2023)

Image /page/88/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below it. The icon is a gray circle with a darker gray ribbon shape inside, pointing to the right. The text below the icon reads "Check for updates" in a gray sans-serif font.

# Is This Hard for You? Personalized Human Difficulty Estimation for Skin Lesion Diagnosis

Peter Johannes Tejlgaard Kampen<sup>( $\boxtimes$ )</sup>  $\blacksquare$ [,](http://orcid.org/0000-0002-3668-3128) Anders Nymark Christensen  $\blacksquare$ , and Morten Rieger Hannemos[e](http://orcid.org/0000-0002-9956-9226)

> Department of Applied Mathematics and Computer Science, Technical University of Denmark, Kgs. Lyngby, Denmark {pjtka,anym,mohan}@dtu.dk

**Abstract.** Predicting the probability of human error is an important problem with applications ranging from optimizing learning environments to distributing cases among doctors in a clinic. In both of these instances, predicting the probability of error is equivalent to predicting the difficulty of the assignment, e.g., diagnosing a specific image of a skin lesion. However, the difficulty of a case is subjective since what is difficult for one person is not necessarily difficult for another. We present a novel approach for personalized estimation of human difficulty, using a transformer-based neural network that looks at previous cases and if the user answered these correctly. We demonstrate our method on doctors diagnosing skin lesions and on a language learning data set showing generalizability across domains. Our approach utilizes domain representations by first encoding each case using pre-trained neural networks and subsequently using these as tokens in a sequence modeling task. We significantly outperform all baselines, both for cases that are in the training set and for unseen cases. Additionally, we show that our method is robust towards the quality of the embeddings and how the performance increases as more answers from a user are available. Our findings suggest that this approach could pave the way for truly personalized learning experiences in medical diagnostics, enhancing the quality of patient care.

**Keywords:** Learning · Difficulty estimation · Sequence modelling

## 1 Introduction

Doctors are dedicated to delivering optimal care to their patients, for which accurate diagnoses are paramount. Gaining experience is essential for proficiency, yet acquiring expertise in skin cancer diagnostics typically requires several years [\[16\]](#page-97-0). An alternative approach involves learning within a controlled environment, where exposure to cases with increasing difficulty can facilitate accelerated learning [\[12](#page-96-0)]. Prior research has concentrated on assessing a general case difficulty [\[6](#page-96-1)], or personal skill inferred purely from user answers [\[2\]](#page-96-2).

Image /page/89/Figure/1 description: This diagram illustrates a machine learning model for analyzing medical cases, likely skin lesions. The model processes multiple 'cases' (Case 1 to Case p), each containing an image and associated 'case information'. Each case image is fed into an 'Encoder' to produce a feature vector, denoted as x1, x2, ..., xp. These feature vectors are then combined with time difference information (Δt^1 to Δt^p) and case information (C1 to Cp) to form input sequences for a 'TRANSFORMER' model. The transformer processes these sequences, potentially considering masked inputs (MASK), and outputs a probability P(Answer = Correct) via a 'Classifier'. The 'Case Domain' box suggests a broader context or dataset from which these cases are drawn. The arrows indicate the flow of data through the different stages of the model.

<span id="page-89-0"></span>**Fig. 1.** Overview of our method. We predict the probability of a user answering correctly on a new case, given their history in the form of embeddings of the previous cases and which of these they answered right and wrong.

However, in reality, the difficulty of a specific case will be different from person to person and depend on their individual experience, which is the focus of this paper. Namely, we deal with the problem of estimating personalized difficulties in unseen cases. The ability to predict the correctness of a doctor's diagnosis has wide-ranging applications, such as optimizing the allocation of diagnostic cases between multiple doctors in a clinic, to maximize the probability of correct diagnoses. Additionally, personalized models for difficulty could assist in uncovering biases. The problem of predicting if a doctor will diagnose correctly is equivalent to predicting the correctness of a student's answer in a testing environment and we focus on the latter due to data availability. We use a transformer-based neural network that, based on previous cases and the doctor's answers, can estimate their probability of answering correctly on a new unseen image. An overview of our method is in Fig. [1.](#page-89-0) We compare our method to the difficulty estimation method by Hannemose *et al*. [\[6](#page-96-1)], the Elo rating system [\[4](#page-96-3)], Bayesian Knowledge Tracing (BKT) [\[2\]](#page-96-2) and the expected difficulty. We present results on two datasets, one consisting of images of skin lesions accompanied by diagnoses from medical students and the publicly available Duolingo SLAM dataset [\[14\]](#page-97-1). We show the efficacy of our method across domains and through the simulation of real-world scenarios. Put together, this opens the possibility of truly personalized learning tailored to each doctor. In summary, our contributions are:

- We combine the domain understanding of pre-trained models with sequence modeling, obtaining state-of-the-art personalized difficulty predictions.
- We can handle any number of previous answers from a user, with increasing performance for more answers, yet still outperform state-of-the-art for users with very few answers.
- Our method generalizes without modification to cases not present in the training data, with a wider performance gap compared to previous work.

<span id="page-89-1"></span>

## 2 Related Work

Estimating how difficult a given task is for a specific person is of great interest. Multiple prior works have tackled this issue. Tudor *et al*. [\[17\]](#page-97-2) use the time taken

for a visual search task as a proxy for the difficulty. They estimate this difficulty with a convolutional neural network, enabling them to predict the difficulties of new natural images. Hannemose *et al*. [\[6](#page-96-1)] estimated the diagnostic difficulty of medical images and demonstrated their method on dermoscopic and otoscopic images. Varshney *et al*. [\[18\]](#page-97-3) estimate the difficulty of instances for machine learning models in natural language processing. Finally, Settles *et al*. [\[15](#page-97-4)] estimate the difficulty of cases in English tests using natural language processing. However, these methods do not account for user variation and seek to model a 'global' difficulty. Multiple prior works have also attempted to estimate how the skills of a user can develop over time. Among these is Bayesian Knowledge Tracing (BKT) [\[2](#page-96-2)]. Many versions exist that vary in terms of, e.g., the possibility of forgetting [\[1](#page-96-4)] and individual learning parameters [\[21](#page-97-5)]. The Elo rating system [\[4\]](#page-96-3), widely used to rank chess players, can be used to handle the problem of users and cases in an environment similar to ours [\[9\]](#page-96-5). Both doctors and cases can be treated as players, and a doctor diagnosing a case correctly or incorrectly constitutes a win or loss, which then allows the difficulty of the case and the skill of the user to be updated. Klinkenberg *et al*. [\[9](#page-96-5)] implement this in an online learning setting and incorporate the time taken to answer. Hofman *et al*. [\[8](#page-96-6)] expanded on this to allow for statistical inference. We note that methods that estimate a single difficulty per case [\[6,](#page-96-1)[15,](#page-97-4)[17](#page-97-2)[,18](#page-97-3)] utilize information obtained directly from the case to ease the estimation. In contrast, methods that can estimate individual difficulties that change over time do not  $[4,9,21]$  $[4,9,21]$  $[4,9,21]$  $[4,9,21]$ . Our proposed approach combines both advantages by using information about the case in the form of pre-trained embeddings and estimating individualized difficulties.

# 3 Method

Most methods for difficulty estimation for continuously learning users e.g., Bayesian Knowledge Tracing [\[2\]](#page-96-2) and the Elo rating system rely on a single difficulty estimate for a specific case. This parameter is then governed by an underlying latent variable that describes the actual features of the case. However, these are generally not explicitly modeled. Therefore, these methods cannot model underlying similarities between cases, only how users usually respond to these. In this paper, we seek to model the tasks or problems directly by leveraging deep neural networks to yield explicit representations. Consider a set of test items belonging to a domain  $D$ , e.g. skin lesions, where the items are images of these. Let  $f: \mathcal{D} \to \mathbb{R}^n$  be a neural network mapping from this domain onto a vector representation. Suppose a user is repeatedly tested over time while improving based on feedback or teaching. We denote the sequence of test items  $i = 1, \ldots, k$ for user u by  $d_i^u \in \mathcal{D}$ . The corresponding latent representation of the sequence is then  $f(\boldsymbol{d}^u_i) = \boldsymbol{x}^u_i$ . We employ a transformer encoder-based model [\[19\]](#page-97-6), with an objective similar to the masked language modeling [\[3](#page-96-7)]. We, therefore create sequences of vector representations of the test items along with additional information. For the  $i^{\text{th}}$  test case presented to the user  $u, d_i^u$ , we represent whether the user answered correctly as a one-hot encoded vector  $c_i^u$ . We are interested in

<span id="page-91-0"></span>**Table 1.** Description of the model. Each transformer uses four attention heads, and MLP denotes multi-layer perceptrons. *h* is 512 and 1224 for the Skin lesions and Duolingo datasets respectively. *m* is the sequence length.

| Long skip connection | Module      | Input dim. | Output dim. |
|----------------------|-------------|------------|-------------|
|                      | MLP         | $(m, w)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Transformer | $(m, h)$   | $(m, h)$    |
|                      | Last Token  | $(m, h)$   | $(1, h)$    |
|                      | MLP         | $h + w$    | 2           |

predicting the probability of the user answering correctly  $P(c_p^u)$  on a new case with representation  $\mathbf{x}_p$ , given the sequence of preceding case representations and answers. Due to the temporal nature of learning, we employ temporal encodings rather than positional encodings based on the time since the answers. When predicting for  $\mathbf{x}_p$  answered at time  $t_p$ , conditioned on  $\mathbf{x}_i^u$  at time  $t_i^u$ , the temporal encoding for  $\mathbf{x}_i^u$  is then  $\sigma(t_p - t_i^u)$ , where  $\sigma(\cdot)$  is the sigmoid function. In most testing scenarios, additional information on the case e.g. the time taken to answer, may be available. We encode any additional information in a vector  $l_i^u$ . We loosely employ the terminology from Natural Language Processing (NLP). Hence, we shall refer to each input that forms our sequence as tokens, which are  $\mathbf{v}_i^u = [\mathbf{x}_i^u, \mathbf{l}_i^u, \mathbf{c}_i^u, \sigma(t_p - t_i^u)] \in \mathbb{R}^{w-n}$ . Since the model predicts on  $\mathbf{x}_p$ , we allow it to focus on this by concatenating it to all tokens in the sequence. Hence, the model is given sequences of the form

$$
\left( [\mathbf{v}_1^u, \mathbf{x}_p], [\mathbf{v}_2^u, \mathbf{x}_p], \ldots, [\mathbf{v}_k^u, \mathbf{x}_p], [\mathbf{x}_p, \mathbf{l}_p, [0 \quad 0], \sigma(0), \mathbf{x}_p] \right). \tag{1}
$$

At training time, sequences are generated by randomly sampling a sequence length and then sampling cases from a user. We employ a transformer-encoder architecture as described by Vaswani *et al*. [\[19](#page-97-6)]; however, we modify it using 'long' skip connections along with the skip connections of the original model architecture, see Table [1.](#page-91-0) As the computational complexity grows with the square of the maximum sequence length  $m$  in transformers, we present two approaches to improve performance when more than  $m$  answers are available. Our base model approach uses the previous  $m-1$  answers from the sequence. The first is to sample multiple random subsets of the user's previous answers and to average the predictions. Finally, we propose sampling the  $m-1$  cases with the highest cosine similarity to  $x_p$  and only including these in the sequence. Each model is

|              |            | Num. users | Mean num. answers | Data points | Mean correct |
|--------------|------------|------------|-------------------|-------------|--------------|
| Skin Lesions | Training   | 60         | 565               | 33,900      | 47.8%        |
|              | Validation | 11         | 565               | 6,215       | 47.8%        |
|              | Test       | 11         | 570               | 6,270       | 46.5%        |
| Duolingo     | Training   | 2074       | 145               | 300,730     | 62.4%        |
|              | Validation | 519        | 145               | 72,255      | 62.4%        |
|              | Test       | 2568       | 19                | 48,792      | 59.7%        |

<span id="page-92-0"></span>**Table 2.** Training, validation, and test splits. Splits are done on a user basis. Training and validation numbers are medians over the five cross validation splits.

trained for a maximum number of 400 epochs, with a batch size of 200, using early stopping on the validation set to prevent overfitting with balanced accuracy as the metric.

### 3.1 Baseline Methods

We compare with four baseline results, where the first is an *Expected difficulty* estimate. Let  $\mathcal{D}_p$  be the set that contains all answers on case p in the training set, where each element is either 1 for a correct answer or 0 for an incorrect answer. Then the probability of a user  $u$ , in either the validation or test set, answering correctly on case p is given by  $P(c_p^u) = \sum_{c \in \mathcal{D}_p} \frac{c}{|\mathcal{D}_p|}$ . For cases not present in the training set, we sample predictions according to the general class distribution. As the second method, we use the Elo rating system [\[4\]](#page-96-3), where each case and user is treated as a player in a tournament as described in Sect. [2.](#page-89-1) The method works by iteratively updating the ratings of users and cases. Let  $s_{u,t}$ and  $s_{p,t}$  be the ratings of a user and case at time t. Then the probability that the user answers correctly on the case is  $P(c_p^u) = \frac{1}{1+10^{\delta/T}}$  where  $\delta = s_{u,t} - s_{p,t}$ and the temperature,  $T$ , is a hyperparameter. Using the actual outcome,  $c_p^u$ , and the second hyperparameter  $k$ , the ratings are updated

$$
s_{u,t+1} = s_{u,t} + k(c_p^u - P(c_p^u)), \quad s_{p,t+1} = s_{p,t} + k(P(c_p^u) - c_p^u). \tag{2}
$$

The third model is based on Hannemose *et al*. [\[6\]](#page-96-1) (*Predicted global difficulty*), which utilizes embeddings similar to ours and trains an ExtraTrees regressor [\[5\]](#page-96-8) on the embeddings concatenated with a class label. This allows for predicting a constant difficulty estimate for unseen cases. Finally, we compare with Bayesian Knowledge Tracing (BKT) [\[1](#page-96-4)]. BKT models the student's knowledge as a latent variable in a hidden Markov model. Specifically, we use KT-IDEM [\[10](#page-96-9)], with a non-zero probability of forgetting. This version of BKT models both item difficulty and user skill. Hyperparameters for all baseline methods are found using a grid search to maximize the balanced accuracy on the validation sets.

<span id="page-93-0"></span>**Table 3.** Accuracy and balanced accuracy (B. accuracy) on the test set for the baselines and our base model with maximum sequence length  $m = 200$  and our two methods for handling sequences longer than *m*. The Duolingo test set has no sufficiently long sequences.

|                                   | Skin lesions                       |                                    | Duolingo                           |                                    |
|-----------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|
|                                   | Accuracy $\uparrow$                | B. accuracy $\uparrow$             | Accuracy $\uparrow$                | B. accuracy $\uparrow$             |
| Predicted global difficulty $[6]$ | 0.652 $\pm$ 0.02                   | 0.641 $\pm$ 0.02                   | 0.657 $\pm$ 0.00                   | 0.620 $\pm$ 0.00                   |
| BKT $[1,10]$                      | 0.566 $\pm$ 0.01                   | 0.562 $\pm$ 0.00                   | 0.601 $\pm$ 0.00                   | 0.544 $\pm$ 0.00                   |
| Expected difficulty               | 0.654 $\pm$ 0.00                   | 0.648 $\pm$ 0.00                   | 0.652 $\pm$ 0.00                   | 0.612 $\pm$ 0.00                   |
| Elo Rating $[4]$                  | 0.610 $\pm$ 0.01                   | 0.605 $\pm$ 0.01                   | 0.610 $\pm$ 0.00                   | 0.547 $\pm$ 0.00                   |
| <i>Our base model</i>             | 0.696 $\pm$ 0.01                   | 0.692 $\pm$ 0.010                  | <b>0.703 <math>\pm</math> 0.03</b> | <b>0.681 <math>\pm</math> 0.03</b> |
| + 10 $\times$ random sampling     | 0.705 $\pm$ 0.02                   | 0.704 $\pm$ 0.02                   | N/A                                |                                    |
| + cosine-similarity               | <b>0.717 <math>\pm</math> 0.02</b> | <b>0.715 <math>\pm</math> 0.02</b> |                                    |                                    |

# 4 Data

We demonstrate our method on two datasets from different domains, see Table [2.](#page-92-0)

**Skin lesions** imaged with dermoscopy were diagnosed into eight types of diagnoses by 82 medical students. Each medical student attempted to diagnose, on average, 566 dermoscopic images randomly sampled from a pool of 1723 images. We have access to all student and ground truth diagnoses and refer to Hannemose *et al*. [\[6\]](#page-96-1) for further information about the dataset. We randomly split the data on a person level, such that answers from one student only appear either in the training, validation, or test set. This was done to simulate a realworld setting, where such a model needs to generalize across users. Furthermore, the splits were obtained independently of the number of correct and incorrect answers to ensure limited bias, particularly for the constant difficulty baseline method. We make five train/validation splits, keeping a constant test set. For the encoder, we follow Hannemose *et al*. [\[6](#page-96-1)] and train a ResNet50 [\[7](#page-96-10)] with a multi-similarity loss function  $[20]$  $[20]$ . *l* contains the time to respond, the ground truth diagnosis, and the diagnosis from the user.

**Duolingo** SLAM is an open-source dataset [\[14\]](#page-97-1). We use the 'reverse translate' task with native English speakers translating from Spanish to English, with the original task being to predict errors on a word basis. However, since we consider overall problems, we collapse the labels such that if a single error is made, the entire translation is wrong. We subsume the allocated development set into the dataset and make five random train/validation splits. In total 3226 different cases are in the test set. The given test set is kept. We employ a SentenceBERT encoder with a DistilBERT base model [\[11](#page-96-11)[,13](#page-96-12)]. *l* contains the time to respond.

## 5 Results

We present our main results in Table [3.](#page-93-0) Our method significantly outperforms the baseline methods on both datasets. Furthermore, of the two approaches for handling longer sequences, selection based on the cosine similarity between cases also yields a further increase in performance.

|          | Ours                                           | Predicted global difficulty BKT | Elo                             |
|----------|------------------------------------------------|---------------------------------|---------------------------------|
|          | Skin Lesions $0.602 \pm 0.03$ $0.516 \pm 0.02$ |                                 | $0.558 \pm 0.0200.505 \pm 0.00$ |
| Duolingo | $\left 0.624 \pm 0.04\right 0.585 \pm 0.01$    |                                 | $0.517 \pm 0.0000.512 \pm 0.01$ |

<span id="page-94-0"></span>**Table 4.** Balanced accuracy score for cases not present in the training set.

In almost any learning environment, new cases will continuously be added to the curriculum. Estimating the difficulty of such cases is difficult even for experts. We have split our data such that there are cases in both test sets that are not present in training or validation. In the Skin lesions, these are 192 cases with 2112 responses, for Duolingo these are 509 cases with 9608 responses, and our performance on these are in Table [4.](#page-94-0) For Elo and BKT, we initialize their scores/difficulties as the mean of the training cases and let them update throughout the predictive process. All methods experience a notable drop in performance compared to Table [3,](#page-93-0) however, our method still significantly outperforms the baselines. Expected difficulty is not included, as it is undefined in this case.

<span id="page-94-1"></span>**Table 5.** Test results on the skin lesions for variations of our model,  $m = 200$ . The CLS token denotes a learned representation added to the sequence [\[3](#page-96-7)].

|                             | Encoder layers Num. heads Encoder dim. Balanced |          |          |            |                   |                   |
|-----------------------------|-------------------------------------------------|----------|----------|------------|-------------------|-------------------|
|                             | Pooling                                         | accuracy | Accuracy |            |                   |                   |
| Base model                  | 8                                               | 4        | 512      | Last token | $0.692 \pm 0.012$ | $0.696 \pm 0.013$ |
| Vary num. encoder layers    | 2                                               | 4        | 512      | Last token | $0.650 \pm 0.012$ | $0.655 \pm 0.012$ |
|                             | 4                                               | 4        | 512      | Last token | $0.662 \pm 0.028$ | $0.664 \pm 0.028$ |
|                             | 6                                               | 4        | 512      | Last token | $0.686 \pm 0.020$ | $0.687 \pm 0.020$ |
| Vary num. attention heads   | 8                                               | 2        | 512      | Last token | $0.676 \pm 0.018$ | $0.678 \pm 0.018$ |
|                             | 8                                               | 8        | 512      | Last token | $0.689 \pm 0.016$ | $0.691 \pm 0.017$ |
| Vary encoder dimensionality | 8                                               | 4        | 256      | Last token | $0.674 \pm 0.020$ | $0.677 \pm 0.020$ |
|                             | 8                                               | 4        | 1024     | Last token | $0.692 \pm 0.015$ | $0.696 \pm 0.016$ |
| Vary pooling                | 8                                               | 4        | 512      | Average    | $0.676 \pm 0.014$ | $0.680 \pm 0.013$ |
|                             | 8                                               | 4        | 512      | CLS token  | $0.675 \pm 0.016$ | $0.685 \pm 0.015$ |
| No long skip conn.          | 8                                               | 4        | 512      | Last token | $0.684 \pm 0.018$ | $0.691 \pm 0.018$ |

In Fig. [2a](#page-95-0) we test the reliance on the embedding quality for the two embedding-based methods, ours and predicted global difficulty [\[6](#page-96-1)]. They both use pre-trained encoders to compute the embeddings they use for prediction. We add increasing levels of noise to these embeddings and allow both models to finetune on the perturbed embeddings. We observe that our method is more robust for low noise levels, with only a slight drop in performance for the first four levels. At higher levels, most information in the embedding space is lost, and the performance reverts to approximately that of expected difficulty. In Fig. [2b](#page-95-0) we test the performance of the models as a function of the number of previous cases available. The performance of our model increases rapidly for the first few cases, and it requires only 20 cases to outperform the two constant difficulty baselines and always yields higher performance than Elo and BKT. To verify the choice of architecture, we present results for different hyperparameters in Table [5.](#page-94-1)

Image /page/95/Figure/2 description: This is a line graph showing the balanced accuracy of two different models, 'Ours' and 'PGD', on two datasets, 'Skin Lesion' and 'Duolingo', as a function of noise standard deviation. The x-axis represents the noise standard deviation, ranging from 0.0 to 1.0. The y-axis represents the balanced accuracy, ranging from 0.550 to 0.750. The graph displays four lines with error bars: 'Ours Skin Lesion' (orange diamonds), 'Ours Duolingo' (blue diamonds), 'PGD Skin Lesion' (orange circles), and 'PGD Duolingo' (blue circles). For 'Ours Skin Lesion', the accuracy starts at approximately 0.705 at 0.0 noise and fluctuates slightly, ending around 0.685 at 1.0 noise. For 'Ours Duolingo', the accuracy starts at approximately 0.675 at 0.0 noise and generally decreases, ending around 0.635 at 1.0 noise. For 'PGD Skin Lesion', the accuracy starts at approximately 0.645 at 0.0 noise and shows a general downward trend, ending around 0.575 at 1.0 noise. For 'PGD Duolingo', the accuracy starts at approximately 0.615 at 0.0 noise and also shows a downward trend, ending around 0.595 at 1.0 noise. The error bars indicate the variability or uncertainty in the measurements.

(a) Performance as a function of added noise to the embeddings. PGD denotes predicted global difficulty [6].

Image /page/95/Figure/4 description: A line graph displays the balanced accuracy of different methods against the available sequence length. The x-axis represents the available sequence length, ranging from 0 to 600. The y-axis represents the balanced accuracy, ranging from 0.55 to 0.80. Six lines are plotted, each representing a different method: Elo (red solid line), Ours w. cosine sim (blue dashed line), Ours w. sampling (orange dotted line), Ours Naive (green dashed-dotted line), BKT (cyan dotted line), PGD (purple dashed line), and Expected diff. (pink solid line). The graph shows that the balanced accuracy of most methods increases with the available sequence length, with 'Ours Naive' and 'Ours w. cosine sim' achieving the highest accuracies at longer sequence lengths.

<span id="page-95-0"></span>(b) Performance as a function of user data availability for the skin lesion dataset. For our method  $m = 200$ .

**Fig. 2.** Performance evaluation of the methods with noise and long sequences.

## 6 Discussion and Conclusion

We observe that having a measure of similarity between cases, be it from the attention mechanisms in the transformer model or the cosine similarity-based case selection, significantly improves the accuracy of the difficulty estimation. Methods such as Elo and BKT that rely solely on low-dimensional measures of skill and difficulty do not adequately capture the relationships between cases. For example, Fig. [2b](#page-95-0), demonstrates that our method requires significantly fewer cases to accurately encode the skill level compared to both Elo and BKT. This is useful in practical applications, especially early in the learning setting when few previous answers are available, as one would still want to provide examples with the right difficulty. However, our model also has a limitation in this aspect. As it contains no explicit term for the user's skill, initializing a new user at a specific skill level is not currently possible.

When presented with unseen cases, most baseline methods revert to almost random guessing. Our method is more robust in this setting, maintaining significantly higher performance. The performance exhibited by our model on unseen cases is encouraging for applications beyond the learning domain, such as in a clinical setting where all cases are unseen and should be distributed among doctors to diagnose. The model could be used to assign cases to those with the

highest probability of diagnosing correctly. Future work could also investigate including a neural network as a user in our model. This would provide a measure of the trust that should be placed on human and AI diagnoses respectively.

In conclusion, we have established a new state-of-the-art for individualized human difficulty estimation. By leveraging information from both cases and users, we achieve superior performance with fewer samples than other methods.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-96-4"></span>1. Badrinath, A., Wang, F., Pardos, Z.: pybkt: An accessible python library of bayesian knowledge tracing models (2021)
- <span id="page-96-2"></span>2. Corbett, A.T., Anderson, J.R.: Knowledge tracing: Modeling the acquisition of procedural knowledge. User Modeling and User-Adapted Interaction **4**, 253–278 (2005), <https://api.semanticscholar.org/CorpusID:19228797>
- <span id="page-96-7"></span>3. Devlin, J., Chang, M.W., Lee, K., Toutanova, K.: Bert: Pre-training of deep bidirectional transformers for language understanding (2019)
- <span id="page-96-3"></span>4. Elo, A.E.: The rating of chessplayers, past and present. Arco Pub. (1978)
- <span id="page-96-8"></span>5. Geurts, P., Louis, W.: Extremely randomized trees. Machine Learning (2006). [https://doi.org/10.1007/s10994-006-6226-1,](https://doi.org/10.1007/s10994-006-6226-1) [https://doi.org/10.1007/s10994-006-](https://doi.org/10.1007/s10994-006-6226-1) [6226-1](https://doi.org/10.1007/s10994-006-6226-1)
- <span id="page-96-1"></span>6. Hannemose, M.R., Sundgaard, J.V., Ternov, N.K., Paulsen, R.R., Christensen, A.N.: Was that so hard? estimating human classification difficulty. Applications of Medical Artificial Intelligence **13540**, 88 (2022). [https://doi.org/10.1007/978-3-](https://doi.org/10.1007/978-3-031-17721-7_10) [031-17721-7](https://doi.org/10.1007/978-3-031-17721-7_10) 10
- <span id="page-96-10"></span>7. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. CoRR **abs/1512.03385** (2015), <http://arxiv.org/abs/1512.03385>
- <span id="page-96-6"></span>8. Hofman, A.D., Brinkhuis, M.J., Bolsinova, M., Klaiber, J., Maris, G., van der Maas, H.L.: Tracking with (un) certainty. Journal of Intelligence **8**(1), 10 (2020)
- <span id="page-96-5"></span>9. Klinkenberg, S., Straatemeier, M., van der Maas, H.: Computer adaptive practice of maths ability using a new item response model for on the fly ability and difficulty estimation. Computers & Education **57**(2), 1813–1824 (2011). [https://doi.org/10.](https://doi.org/10.1016/j.compedu.2011.02.003) [1016/j.compedu.2011.02.003,](https://doi.org/10.1016/j.compedu.2011.02.003) [https://www.sciencedirect.com/science/article/pii/](https://www.sciencedirect.com/science/article/pii/S0360131511000418) [S0360131511000418](https://www.sciencedirect.com/science/article/pii/S0360131511000418)
- <span id="page-96-9"></span>10. Pardos, Z., Heffernan, N.: Kt-idem: Introducing item difficulty to the knowledge tracing model. pp. 243–254 (01 1970). [https://doi.org/10.1007/978-3-642-22362-](https://doi.org/10.1007/978-3-642-22362-4_21) 4 [21](https://doi.org/10.1007/978-3-642-22362-4_21)
- <span id="page-96-11"></span>11. Reimers, N., Gurevych, I.: Making monolingual sentence embeddings multilingual using knowledge distillation. In: Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing. Association for Computational Linguistics (11 2020), <https://arxiv.org/abs/2004.09813>
- <span id="page-96-0"></span>12. Roads, B.D., Xu, B., Robinson, J.K., Tanaka, J.W.: The easy-to-hard training advantage with real-world medical images. Cognitive Research: Principles and Implications **3**(1), 1–13 (2018)
- <span id="page-96-12"></span>13. Sanh, V., Debut, L., Chaumond, J., Wolf, T.: Distilbert, a distilled version of bert: smaller, faster, cheaper and lighter (2020)

- <span id="page-97-1"></span>14. Settles, B., Brust, C., Gustafson, E., Hagiwara, M., Madnani, N.: Second language acquisition modeling. In: Proceedings of the NAACL-HLT Workshop on Innovative Use of NLP for Building Educational Applications (BEA). ACL (2018)
- <span id="page-97-4"></span>15. Settles, B., T. LaFlair, G., Hagiwara, M.: Machine learning–driven language assessment. Transactions of the Association for computational Linguistics **8**, 247–263 (2020)
- <span id="page-97-0"></span>16. Ternov, N.K., Vestergaard, T., Hölmich, L.R., Karmisholt, K., Wagenblast, A., Klyver, H., Hald, M., Schøllhammer, L., Konge, L., Chakera, A.: Reliable test of clinicians' mastery in skin cancer diagnostics. Archives of Dermatological Research **313**, 235–243 (2021)
- <span id="page-97-2"></span>17. Tudor Ionescu, R., Alexe, B., Leordeanu, M., Popescu, M., Papadopoulos, D.P., Ferrari, V.: How hard can it be? estimating the difficulty of visual search in an image. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. pp. 2157–2166 (2016)
- <span id="page-97-3"></span>18. Varshney, N., Mishra, S., Baral, C.: ILDAE: Instance-level difficulty analysis of evaluation data. In: Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). pp. 3412–3425. Association for Computational Linguistics, Dublin, Ireland (May 2022). [https://doi.org/10.](https://doi.org/10.18653/v1/2022.acl-long.240) [18653/v1/2022.acl-long.240,](https://doi.org/10.18653/v1/2022.acl-long.240) <https://aclanthology.org/2022.acl-long.240>
- <span id="page-97-6"></span>19. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. Advances in neural information pro cessing systems **30** (2017)
- <span id="page-97-7"></span>20. Wang, X., Han, X., Huang, W., Dong, D., Scott, M.R.: Multi-similarity loss with general pair weighting for deep metric learning (2020)
- <span id="page-97-5"></span>21. Yudelson, M.V., Koedinger, K.R., Gordon, G.J.: Individualized bayesian knowledge tracing models. In: Lane, H.C., Yacef, K., Mostow, J., Pavlik, P. (eds.) Artificial Intelligence in Education. pp. 171–180. Springer Berlin Heidelberg, Berlin, Heidelberg (2013)

Image /page/98/Picture/0 description: A square button with rounded corners contains a circular icon and text. The icon is a circle with a curved arrow and a bookmark shape inside. The text below the icon reads "Check for updates".

# LaB-GATr: Geometric Algebra Transformers for Large Biomedical Surface and Volume Meshes

Julian Su[k](http://orcid.org/0000-0001-5505-475X)<sup>( $\boxtimes$  $\boxtimes$  $\boxtimes$ )</sup>  $\bullet$ [,](http://orcid.org/0000-0003-0729-047X) Baris Imre $\bullet$ , and Jelmer M. Wolterink $\bullet$ 

Department of Applied Mathematics, Technical Medical Centre, University of Twente, Enschede, The Netherlands {j.m.suk,j.m.wolterink}@utwente.nl

Abstract. Many anatomical structures can be described by surface or volume meshes. Machine learning is a promising tool to extract information from these 3D models. However, high-fidelity meshes often contain hundreds of thousands of vertices, which creates unique challenges in building deep neural network architectures. Furthermore, patientspecific meshes may not be canonically aligned which limits the generalisation of machine learning algorithms. We propose LaB-GATr, a transformer neural network with geometric tokenisation that can effectively learn with large-scale (bio-)medical surface and volume meshes through sequence compression and interpolation. Our method extends the recently proposed geometric algebra transformer (GATr) and thus respects all Euclidean symmetries, i.e. rotation, translation and reflection, effectively mitigating the problem of canonical alignment between patients. LaB-GATr achieves state-of-the-art results on three tasks in cardiovascular hemodynamics modelling and neurodevelopmental phenotype prediction, featuring meshes of up to 200,000 vertices. Our results demonstrate that LaB-GATr is a powerful architecture for learning with high-fidelity meshes which has the potential to enable interesting downstream applications. Our implementation is publicly available [\(github.com/sukjulian/lab-gatr\)](https://github.com/sukjulian/lab-gatr).

Keywords: Deep learning · Attention models · Cardiovascular hemodynamics · Neuroimaging · Geometric algebra

# 1 Introduction

Deep neural networks can leverage biomedical data to uncover previously unknown cause-and-effect relations [\[28](#page-108-0)] and enable novel ways of medical diagnosis and treatment  $[15,21]$  $[15,21]$ . There has been active research into using deep

J. Suk and B. Imre—Equal contribution.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_18.](https://doi.org/10.1007/978-3-031-72390-2_18)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 185–195, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_18)\_18

neural networks for biomedical modelling, such as cardiovascular biomechanics estimation [\[2\]](#page-106-0) and neuroimage analysis based on the cortical surface [\[29](#page-108-2)]. These applications feature 3D mesh representations of patient anatomy. Depending on the downstream application, 3D meshes either discretise the surface of an organ or vessel with, e.g., triangles, or the interior with, e.g., tetrahedra. Graph neural networks (GNN) have gained traction in the context of learning with 3D meshes due to their direct applicability, flexibility regarding mesh size, and good performance on several benchmarks [\[9](#page-107-1)[,23](#page-108-3)[,25](#page-108-4)]. However, GNNs are known to suffer from over-squashing, i.e. the loss of accuracy when compressing exponentially growing information into fixed-sized channels when propagating messages on long paths [\[1](#page-106-1)]. This makes them inefficient at accumulating large enough receptive fields, capable of learning global interactions across meshes. Point $Net++$  [\[17](#page-107-2)] can circumvent this issue to some extent via pooling to coarser surrogate graphs, but this requires problem-specific hyperparameter setup and might still fail around bottlenecks. In contrast, the transformer architecture [\[27](#page-108-5)], treats its input as a sequence of tokens (or patches) and models all pair-wise interactions, thus aggregating global context after a single layer. In medical imaging, transformers have been successfully applied in the context of, e.g., semantic segmentation of 2D histopathology images [\[12\]](#page-107-3) and 3D brain tumor magnetic resonance images (MRI) [\[10\]](#page-107-4). Nevertheless, applications to 3D biomedical meshes remain scarce due to the difficulty of finding a unified framework that addresses their sheer size, commonly in the hundreds of thousands of vertices. Dahan et al. [\[5,](#page-107-5)[6](#page-107-6)] addressed this by aligning 3D cortical surface meshes across subjects via morphing to an icosphere, which allows for segmentation into coarser triangular patches.

The recently proposed geometric algebra transformer (GATr) [\[4](#page-107-7)] has achieved state-of-the-art accuracy on several geometric tasks, largely due to the incorporation of task-specific symmetries (SE(3)-equivariance) and modelling of inter-actions via geometric algebra.<sup>[1](#page-99-0)</sup> Even though GATr uses memory-efficient attention [\[18](#page-107-8)] with linear complexity, large 3D meshes still cause it to exceed GPU memory during training. In the context of graph transformers, the common bottleneck of GPU memory has been addressed by (1) sparse, local attention mechanisms together with expander graph rewiring  $[7,22]$  $[7,22]$  $[7,22]$ , as well as  $(2)$  vertex clustering together with learned graph pooling and upsampling [\[11](#page-107-10)[,14](#page-107-11)].

In this work, we scale GATr to large-scale biomedical (LaB-GATr) surface and volume meshes. Since each of these meshes discretises a continuous shape, mesh connectivity should be treated as an artefact, not a feature. Thus, we opt for a vertex clustering approach to make self-attention tractable. We derive a general-purpose tokenisation algorithm and interpolation method with learned feature representations in geometric algebra space, which retains all symmetries of GATr as well as the original mesh resolution, while decreasing the number of active tokens used in self-attention. We demonstrate the efficacy of our method on three tasks involving biomedical meshes. LaB-GATr sets a new state-of-the-art in prediction of blood velocity in high-fidelity, synthetic coronary artery meshes [\[23](#page-108-3)] which would exceed 48 GB of VRAM when using GATr with

<span id="page-99-0"></span><sup>&</sup>lt;sup>1</sup> We refer the interested reader to  $\left[3, 19, 20\right]$  $\left[3, 19, 20\right]$  $\left[3, 19, 20\right]$  $\left[3, 19, 20\right]$  $\left[3, 19, 20\right]$ .

the same number of parameters. Furthermore, Lab-GATr excels at phenotype prediction of cortical surfaces from the [Developing Human Connectome Project](http://www.developingconnectome.org/) (dHCP) [\[8](#page-107-13)], setting a new state-of-the-art in postmenstrual age (PMA) estimation without morphing the cortical surface mesh to an icosphere. We provide a modular implementation of Lab-GATr and an interface with which the geometric algebra back-end can be treated as black box.

Image /page/100/Figure/2 description: This figure illustrates a deep learning architecture for processing brain data. The process begins with a brain image represented as (n x c\_in x 16), which undergoes a transformation indicated by a multiplication symbol and a triangle, leading to a series of stacked cards. Below this, section (a) shows a G(3, 0, 1) embedding with numerical and symbolic tokens like 1, delta, nu1, nu2, nu3, tau1/2, tau2/2, tau3/2, zeros, rho1, rho2, rho3, 1, and 0. Accompanying this are visual representations of a plane with a delta vector, a vector (nu1, nu2, nu3), and a point (rho1, rho2, rho3). Section (b) depicts tokenization, showing a brain split into two halves (blue and red) and then a surface representation with many points. A small network diagram labeled 'learnable' is also present. Section (c) shows a graph network with nodes and edges, suggesting graph-based processing. Section (d) illustrates interpolation, with nodes connected by lines, and cards with brain images and symbols. A green alligator icon is labeled 'GATr class token\*'. The process culminates in an output brain image of size (n x c\_out x 16), also transformed by multiplication and a triangle, resulting in another set of stacked cards. A sequence of tokens, starting with 's' and followed by nu1, nu2, nu3, and then many grayed-out tokens, is shown. Finally, 'feature extraction' is indicated by a downward triangle, with 's' belonging to R and 'nu' belonging to R3.

<span id="page-100-0"></span>Fig. 1. LaB-GATr takes input features in the form of multivectors, which are constructed by embedding, e.g., mesh vertices as points and surface normal vectors as planes (see Sect. [2.2\)](#page-101-0). In the tokenisation module, the features are pooled to a coarse subset of mesh vertices via message passing (see Sect. [2.4\)](#page-102-0). The tokenisation allows control over the number of tokens which are processed by the GATr module. Downstream, the interpolation module lifts the tokenisation back to original mesh resolution (see Sect. [2.4\)](#page-102-1). An optional (\*) class token is appended to the token sequence for mesh-level output. Subsequently, scalar or vector-valued output features are extracted.

# 2 Methods

Figure [1](#page-100-0) provides an overview of our method. We extend GATr by geometric tokenisation for scaling to high-fidelity meshes. In the following, we discuss background on geometric algebra and transformers before introducing our contribution.

## 2.1 Geometric Algebra

GATr is built on the geometric algebra  $\mathbf{G}(3,0,1)$  which uses projective geometry: a fourth coordinate is appended to 3D space, so translation can be expressed as linear maps. At the core of geometric algebra lies the introduction of an associative (but not commutative) geometric product of vectors y, z, simply denoted as yz. Given a 4D orthogonal basis  $\{e_i\}_i$ , it holds that  $e_0e_0 = 0$ ,  $e_ie_i = 1$  ( $i \neq 0$ ),

and  $e_i e_j = -e_j e_i$  ( $i \neq j$ ). All possible, linearly independent geometric products span a 16-dimensional vector space of multivectors  $x \in \mathbf{G}(3, 0, 1)$ . span a 16-dimensional vector space of multivectors  $x \in \mathbf{G}(3,0,1)$ :

<span id="page-101-2"></span>
$$
x = (x_s, \underbrace{x_0, x_1, x_2, x_3}_{\text{vectors}}, \underbrace{x_{01}, x_{02}, x_{03}, x_{12}, x_{13}, x_{23}}_{\text{bivectors}}, \underbrace{x_{012}, x_{013}, x_{023}, x_{123}}_{\text{trivectors}}, x_{0123})
$$
(1)

which can represent geometric quantities like points and planes.

<span id="page-101-0"></span>

## 2.2 Embedding Meshes in G(3, 0, 1)

Consider an arbitrary surface or volume mesh consisting of  $n$  vertices. For each vertex, we construct a d-dimensional positional encoding which describes its unique geometric properties within this mesh. Each positional encoding is composed of a set of c geometric objects, e.g. the surface normal vector (for surface meshes) or the (scalar) distance to the surface (for volume meshes). Table [1](#page-101-1) provides a look-up on how to embed relevant geometric objects, such as points and planes, as multivectors  $x \in \mathbf{G}(3,0,1)$  (see Fig. [1a](#page-100-0)). Consequently, we describe each mesh by a tensor  $X^{(0)} \in \mathbb{R}^{n \times d}$  with  $d = c \cdot 16$ .

<span id="page-101-1"></span>**Table 1.** Embedding of some common geometric objects as  $x \in \mathbf{G}(3, 0, 1)$ . See [\(1\)](#page-101-2) for the 16 multivector components. Other multivector components remain zero. In geometric algebra, geometric operations can be multivectors just like geometric objects.

| Geometric object / operation                                                                       | Multivector mapping                                    |
|----------------------------------------------------------------------------------------------------|--------------------------------------------------------|
| Scalar $s  in  \mathbb{R}$                                                                         | $x_s = s$                                              |
| Plane with normal $\nu  in  \mathbb{R}^3$ and offset $\delta  in  \mathbb{R} (x_0, x_1, x_2, x_3)$ | $= (\delta, \nu)$                                      |
| Point $\rho  in  \mathbb{R}^3$                                                                     | $(x_{012}, x_{013}, x_{023}, x_{123}) = (\rho, 1)$     |
| Translation $\tau  in  \mathbb{R}^3$                                                               | $(x_s, x_{01}, x_{02}, x_{03}) = (1, \frac{1}{2}\tau)$ |

## 2.3 Geometric Algebra Transformers

Given a tensor of input features  $X^{(l)}$ , we define transformer blocks as follows:

$$
A^{(l)} = X^{(l)} + \xi \left( \text{Concat}_{h} \quad \text{Softmax}\left( \frac{q_h(X^{(l)}) k_h(X^{(l)})^{\mathsf{T}}}{\sqrt{d}} \right) v_h(X^{(l)}) \right)
$$
$$
X^{(l+1)} = A^{(l)} + \text{MLP}(A^{(l)}).
$$

As is common practice,  $q_h, k_h, v_h: \mathbb{R}^{n \times d} \to \mathbb{R}^{n \times d}$  consist of layer normalisation composed with learned linear maps. Multi-head self-attention [\[27](#page-108-5)] over heads indexed by  $h$  is implemented via concatenation followed by a learned linear map  $\xi$ . GATr [\[4](#page-107-7)] introduced layer normalisation, linear and nonlinear maps  $\mathbf{G}(3,0,1)^{n\times c} \to \mathbf{G}(3,0,1)^{n\times c}$ , and gated activation functions which can be used to construct  $\xi$ ,  $q_h, k_v, v_h$ , and MLP in geometric algebra. GATr blocks are equivariant under rotations, translations and reflections  $\rho \in E(3)$  of the input geometry encoded in  $X^{(l)}$ , i.e. they map  $\rho X^{(l)} \mapsto \rho X^{(l+1)}$ .

### 2.4 Learned, Geometric Tokenisation

In the following, we introduce tokenisation layers for large surface and volume meshes, allowing us to compress the token sequence for the transformer blocks.

<span id="page-102-0"></span>**Pooling.** From the point cloud  $P_{\text{fine}}$  consisting of the n mesh vertices, we compute a subset of points  $P_{\text{coarse}}$  via farthest point sampling. We partition the mesh vertices into tokens by assigning each  $p \in P_{\text{coarse}}$  the (disjoint) cluster of closest points in  $P_{\text{fine}}$  (see Fig. [1b](#page-100-0)):

$$
C(p) = \{ v \in P_{\text{fine}} \mid p = \underset{q \in P_{\text{coarse}}}{\text{arg min}} \, ||v - q||_2 \}.
$$

This means that each point in  $P_{\text{fine}}$  is clustered with the point in  $P_{\text{coarse}}$  to which it is the closest. Define  $n_{\text{coarse}} = |P_{\text{coarse}}|$ . Given a tensor of input features  $X \in \mathbb{R}^{n \times d}$ , we perform learned message passing within these clusters (see Fig. [1c](#page-100-0)) as follows:

m<sub>v→p</sub> = MLP(X<sup>(0)</sup>|<sub>v</sub>, p - v) (message from P<sub>fine</sub> to P<sub>coarse</sub>)

  
X<sup>(1)</sup>|<sub>p</sub> = \frac{1}{|C(p)|} \sum\_{v \in C(p)} m\_{v \to p}

 (aggregation of  $X^{(1)} 
\in 
\mathbb{R}^{n_{\text{coarse}} \times d}$ )

where  $X|_q$  denotes the row of X corresponding to point q. This layer maps  $\mathbb{R}^{n \times d} \to \mathbb{R}^{n_{\text{coarse}} \times d}$  with  $n_{\text{coarse}} < n$ . Within this framework, we can embed  $p-v$ as translation (see Table [1\)](#page-101-1) and use the multilayer perceptron (MLP) introduced by [\[4](#page-107-7)] to reduce the number of tokens in a way that is fully compatible with GATr. In particular, this layer respects all symmetries of  $\mathbf{G}(3,0,1)$ .

<span id="page-102-1"></span>**Interpolation.** Given a tensor  $X^{(l)} \in \mathbb{R}^{n_{\text{coarse}} \times d}$  we define learned interpolation to the original mesh resolution  $Y \in \mathbb{R}^{n \times d}$  as follows:

$$
X^{(l+1)}|_{v} = \frac{\sum_{p} \lambda_{p,v} X^{(l)}|_{p}}{\sum_{p} \lambda_{p,v}}, \qquad \lambda_{p,v} := \frac{1}{\|p-v\|_{2}^{2} + \epsilon},
$$
$$
Y = \text{MLP}(X^{(l+1)}, X^{(0)})
$$

where for each  $v \in P_{\text{fine}}$  we sum over the three (four) closest points  $p \in P_{\text{coarse}}$  for surface (volume) meshes (see Fig. [1d](#page-100-0)) and  $\epsilon$  is a small constant. This layer lifts the tokenisation and ensures neural-network output in original mesh resolution. The interpolation provably behaves as expected in  $\mathbf{G}(3,0,1)$  (see appendix) and by using the MLP introduced by [\[4](#page-107-7)] this layer respects all symmetries of  $\mathbf{G}(3,0,1)$ .

<span id="page-102-2"></span>

### 2.5 Neural Network Architecture

LaB-GATr is composed of geometric algebra embedding, tokenisation module, GATr module, and interpolation module followed by feature extraction (see Fig. [1\)](#page-100-0). We embed the input mesh in  $\mathbf{G}(3,0,1)$  (see Sect. [2.2\)](#page-101-0) based on an application-specific set of geometric descriptors. The embedding  $X^{(0)} \in \mathbb{R}^{n \times d}$ is pooled (see Sect. [2.4\)](#page-102-0) and the resulting  $n_{\text{coarse}}$  tokens are fed into a GATr module. For mesh-level tasks, we append a global class token which we embed as the mean of the  $n_{\text{coarse}}$  tokens. For vertex-level tasks, the m output tokens of the transformer are interpolated (see Sect. [2.4\)](#page-102-1) back to the original mesh resolution. For classification tasks (like segmentation), Softmax can be used over the channel dimension at the mesh- or vertex-level output.

Image /page/103/Figure/2 description: The image displays two rows of computational fluid dynamics (CFD) simulations of blood flow in vascular structures. The top row shows a single, curved vessel with 5,227 vertices. It presents three visualizations: the CFD simulation results, the LaB-GATr simulation results, and the error between them, with a color bar indicating pressure in Pascals (Pa) ranging from 0 to 15. The bottom row shows a bifurcating vessel with 185,938 vertices, also with CFD and LaB-GATr simulation results, and a color bar indicating velocity in centimeters per second (cm/s) ranging from 0 to 30. Both rows show vector fields representing flow direction and magnitude, with color mapping indicating pressure or velocity.

<span id="page-103-0"></span>Fig. 2. Qualitative results of cardiovascular hemodynamics estimation on test-split arteries. Left: (top) surface and (bottom) volume mesh. Middle: (top) wall shear stress and (bottom) velocity field, via computational fluid dynamics (CFD). Right: LaB-GATr prediction.

# 3 Experiments and Results

We evaluate LaB-GATr on three tasks previously explored with GNNs and transformers. Since all of these are regression tasks, we train LaB-GATr under  $L<sup>1</sup>$  loss using the Adam optimiser [\[13](#page-107-14)] and learning rate 3e−4 with exponential decay on Nvidia L40 (48 GB) GPUs. We use the same number of channels and attention heads throughout all experiments, leading to around 320k trainable parameters.

## 3.1 Coronary Artery Models

Surface-Based WSS Estimation. The publicly available dataset [\[25](#page-108-4)] consists of 2,000 synthetic, coronary artery surface meshes (around 7,000 vertices) with simulated, steady-state wall shear stress (WSS) vectors. We chose  $n_{\text{coarse}} = 0.1 \cdot n$  <span id="page-104-0"></span>Table 2. Comparison of LaB-GATr with state-of-the-art baselines. For WSS and hemodynamics estimation, we report mean  $\varepsilon$  (lower is better) across the test set. For PMA estimation, some of the referenced works report lowest and some average MAE across three training runs. We report lowest MAE in brackets where applicable.

| Domain         | Dataset            | Model        | Disparity |          | Metric            |
|----------------|--------------------|--------------|-----------|----------|-------------------|
|                |                    |              | (average) | (lowest) |                   |
| Cardiovascular | WSS [25]           | GEM-CNN [24] | 7.8       |          | $\varepsilon$ [%] |
|                |                    | $GATr$ [4]   | 5.5       |          |                   |
|                |                    | $LaB-GATr$   | $5.5^*$   |          |                   |
|                |                    | $LaB-GATr^1$ | 7.0       |          |                   |
|                |                    | $LaB-GATr^2$ | 12.3      |          |                   |
|                | Velocity [23]      | SEGNN [23]   | 7.4       |          |                   |
|                |                    | $LaB-GATr$   | $\bf 3.3$ |          |                   |
| Neuroimaging   | PMA [8] (native)   | SiT [6]      | -         | (0.68)   | MAE [weeks]       |
|                | MS-SiT [5]<br>[26] | 0.59         | - (0.54)  |          |                   |
|                | $LaB-GATr$         | 0.54         | (0.52)    |          |                   |

\*with 10-fold compression  $\frac{1}{2}$  static pooling module  $\frac{2}{2}$  static interpolation module.

in the tokenisation (see Sect.  $2.4$ ) and embed vertex positions as points (see Table [1\)](#page-101-1), surface normal vectors as oriented planes, and geodesic distances to the artery inlet as scalars. The GATr back-end (see Sect. [2.5\)](#page-102-2) was set up identical to [\[4](#page-107-7)]. We trained LaB-GATr on a single GPU for  $4,000$  epochs (58  $\frac{\text{s}}{\text{epoch}}$ ) with batch size 8 on an 1600:200:200 split of the dataset. Figure [2](#page-103-0) shows an example of LaB-GATr prediction on a test-split mesh. Table [2](#page-104-0) shows approximation error  $\varepsilon$  [\[4,](#page-107-7)[24\]](#page-108-8) of LaB-GATr compared to the baselines. LaB-GATr matches GATr's accuracy  $\varepsilon = 5.5\%$  (standard deviation was  $\pm 2.0\%$  over 200 test cases) despite its 10fold compression of the token sequence. Ablation of the learnable parameters of the pooling and interpolation module reveals that learned interpolation plays a bigger role in performance than learned pooling.

Volume-Based Velocity Field Estimation. The publicly available dataset [\[23](#page-108-3)] consists of 2,000 synthetic, bifurcating coronary artery volume meshes (around 175,000 vertices) with simulated, steady-state velocity vectors. We chose  $n_{coarse} = 0.01 \cdot n$  for the tokenisation and embed vertex positions as points, directions to the artery inlet, outlets, and wall as oriented planes, and distances to the artery inlet, outlets, and wall as scalars. The size of these volume meshes caused GATr to exceed memory of an NVIDIA L40 (48 GB) GPU. Leveraging our tokenisation enabled training LaB-GATr with batch size 1. We trained LaB-GATr on four GPUs in parallel for 300 epochs  $(10:24 \frac{\text{min}}{\text{epoch}})$  on an 1600:200:200 split of the dataset. Figure [2](#page-103-0) shows an example of LaB-GATr prediction on a test-split mesh. Table [2](#page-104-0) compares LaB-GATr to [\[23](#page-108-3)]. LaB-GATr sets a new state-of-the-art for this dataset with  $\varepsilon = 3.3\%$  (standard deviation was  $\pm 4.4\%$  over 200 test cases) compared to the previous 7.4%.

## 3.2 Postmenstrual Age Prediction from the Cortical Surface

The publicly available third release of dHCP [\[8](#page-107-13)] consists of 530 newborns' cortical surface meshes (81,924 vertices each) which are symmetric across hemispheres. We estimate the subjects' postmenstrual age (PMA) at the time of scan. We chose  $n_{\text{coarse}} = 0.024 \cdot n$  for the tokenisation and embed vertex positions as points, surface normal vectors as oriented planes, the reflection planes between symmetric vertices as oriented planes, and myelination, curvature, cortical thickness, and sulcal depth as scalars. Since we observed quick convergence on the validation split, we trained LaB-GATr on a single GPU for only 200 epochs  $(1.38 \frac{\text{min}}{\text{epoch}})$  $(1.38 \frac{\text{min}}{\text{epoch}})$  $(1.38 \frac{\text{min}}{\text{epoch}})$  with batch size 4 on the 423:53:54 splits used in [\[5](#page-107-5)]. Figure 3 shows a Bland-Altman plot and the cortical surface of two subjects, indicating good accuracy. Table [2](#page-104-0) shows mean absolute error (MAE) and comparison to the baselines. In contrast to all baselines, LaB-GATr runs directly on the cortical surface mesh without morphing to a sphere. LaB-GATr sets a new state-of-the-art in PMA estimation on "native space" [\[9\]](#page-107-1) with average  $MAE = 0.54$  weeks (standard deviation was  $\pm 0.39$  weeks over 54 test cases times three runs).

Image /page/105/Figure/4 description: This image displays a Bland-Altman plot comparing a reference measurement to a predicted measurement for brain models. On the left and right sides of the plot are images of a 3D brain model. The central plot shows the difference between the reference and prediction on the y-axis, ranging from -2 to 2, and the average of the reference and prediction on the x-axis, ranging from approximately 25 to 45. Several blue data points are scattered across the plot. A dashed orange line at y=0 represents the mean difference (labeled as ".mu"), with an arrow pointing from it to a data point. Two dashed gray lines indicate the limits of agreement, labeled as "1.96\u03c3" at y=1.5 and "-1.96\u03c3" at y=-1.5.

<span id="page-105-0"></span>Fig. 3. Postmenstrual age (PMA) prediction (values in weeks) on test-split subjects. LaB-GATr predictions based on the newborn's cortical surface correlated well with the reference values.

## 4 Discussion and Conclusion

In this work, we propose LaB-GATr, a general-purpose geometric algebra transformer for large-scale surface and volume meshes, often found in biomedical engineering. We extend GATr by learned tokenisation and interpolation in geometric algebra. Our method can be understood as a thin  $PointNet++$  [\[17\]](#page-107-2) wrapper which is adapted to projective geometric algebra. Through the self-attention

mechanism, LaB-GATr models global interactions within the mesh, while avoiding the over-squashing phenomenon exhibited by GNNs. Notably, LaB-GATr is equivariant to rotations, translations, and reflections of the input mesh, thus circumventing the problem of cortical surface alignment. In our experiments, LaB-GATr matched the performance of GATr [\[4](#page-107-7)] on the same dataset, suggesting near loss-less compression. Even though LaB-GATr introduces additional, trainable parameters over GATr, we found that computing self-attention between less tokens outweighed the parameter overhead and led to favourable training times. Beside estimating PMA of subjects from dHCP, we also attempted gestational age (GA) estimation with LaB-GATr. However, we observed considerably lower accuracy. We believe that GA can be best explained by vertex-specific biomarkers such as myelination, which was provided on spherical and subsequently sub-sampled brains and back-projection to the cortical surface erased some of their spatial context.

In theory, our geometric pooling scheme introduces all necessary building blocks to define patch merging and to build a geometric version of sliding window (Swin) attention [\[16\]](#page-107-15), which is an interesting direction for future work. We believe that Lab-GATr has potential as general-purpose model for learning with large (bio-)medical surface and volume meshes, enabling interesting downstream applications. In particular, we are interested to explore brain parcellation and attention-map-based analysis of biomedical pathology. Geometric algebra introduces an inductive bias to our learning system. In future work, we aim to investigate to what extent this affects LaB-GATr predictions and derive theoretical guarantees.

Acknowledgments. This work is funded in part by the 4TU Precision Medicine programme supported by High Tech for a Sustainable Future, a framework commissioned by the four Universities of Technology of the Netherlands. Jelmer M. Wolterink was supported by the NWO domain Applied and Engineering Sciences Veni grant (18192).

We would like to thank Simon Dahan for his efforts in providing the dHCP dataset as well as Pim de Haan and Johann Brehmer for the fruitful discussions about geometric algebra.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-106-1"></span>1. Alon, U., Yahav, E.: On the bottleneck of graph neural networks and its practical implications. In: 9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021 (2021)
- <span id="page-106-0"></span>2. Arzani, A., Wang, J.X., Sacks, M., Shadden, S.: Machine learning for cardiovascular biomechanics modeling: Challenges and beyond. Annals of Biomedical Engineering 50, 1–13 (04 2022)
- <span id="page-106-2"></span>3. Brandstetter, J., van den Berg, R., Welling, M., Gupta, J.K.: Clifford neural layers for PDE modeling. In: The Eleventh International Conference on Learning Representations, ICLR 2023, Kigali, Rwanda, May 1-5, 2023 (2023)

- <span id="page-107-7"></span>4. Brehmer, J., de Haan, P., Behrends, S., Cohen, T.: Geometric algebra transformer. In: Advances in Neural Information Processing Systems. vol. 37 (2023)
- <span id="page-107-5"></span>5. Dahan, S., Fawaz, A., Suliman, M.A., da Silva, M., Williams, L.Z.J., Rueckert, D., Robinson, E.C.: The multiscale surface vision transformer. ArXiv (2023)
- <span id="page-107-6"></span>6. Dahan, S., Fawaz, A., Williams, L.Z.J., Yang, C., Coalson, T.S., Glasser, M.F., Edwards, A.D., Rueckert, D., Robinson, E.C.: Surface vision transformers: Attention-based modelling applied to cortical analysis. In: International Conference on Medical Imaging with Deep Learning, MIDL, 6-8 July 2022, Zurich, Switzerland (2022)
- <span id="page-107-9"></span>7. Deac, A., Lackenby, M., Velickovic, P.: Expander graph propagation. In: Rieck, B., Pascanu, R. (eds.) Learning on Graphs Conference, LoG, 9-12 December 2022, Virtual Event (2022)
- <span id="page-107-13"></span>8. Edwards, A.D., al.: The developing human connectome project neonatal data release. Frontiers in Neuroscience 16 (2022)
- <span id="page-107-1"></span>9. Fawaz, A., Williams, L.Z.J., Alansary, A., Bass, C., Gopinath, K., da Silva, M., Dahan, S., Adamson, C., Alexander, B., Thompson, D., Ball, G., Desrosiers, C., Lombaert, H., Rueckert, D., Edwards, A.D., Robinson, E.C.: Benchmarking geometric deep learning for cortical segmentation and neurodevelopmental phenotype prediction. bioRxiv (2021)
- <span id="page-107-4"></span>10. Hatamizadeh, A., Nath, V., Tang, Y., Yang, D., Roth, H.R., Xu, D.: Swin unetr: Swin transformers for semantic segmentation of brain tumors in mri images. In: Brainlesion: Glioma, Multiple Sclerosis, Stroke and Traumatic Brain Injuries. pp. 272–284. Springer International Publishing, Cham (2022)
- <span id="page-107-10"></span>11. Janny, S., Béneteau, A., Nadri, M., Digne, J., Thome, N., Wolf, C.: EAGLE: large-scale learning of turbulent fluid dynamics with mesh transformers. In: The Eleventh International Conference on Learning Representations, ICLR 2023, Kigali, Rwanda, May 1-5, 2023 (2023)
- <span id="page-107-3"></span>12. Ji, Y., Zhang, R., Wang, H., Li, Z., Wu, L., Zhang, S., Luo, P.: Multi-compound transformer for accurate biomedical image segmentation. In: Medical Image Computing and Computer Assisted Intervention – MICCAI 2021. pp. 326–336 (2021)
- <span id="page-107-14"></span>13. Kingma, D.P., Ba, J.: Adam: A method for stochastic optimization. In: 3rd International Conference on Learning Representations, ICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings (2015)
- <span id="page-107-11"></span>14. Kong, K., Chen, J., Kirchenbauer, J., Ni, R., Bruss, C.B., Goldstein, T.: GOAT: A global transformer on large-scale graphs. In: International Conference on Machine Learning, ICML, 23-29 July 2023, Honolulu, Hawaii, USA (2023)
- <span id="page-107-0"></span>15. Li, G., Wang, H., Zhang, M., Tupin, S., Qiao, A., Liu, Y., Ohta, M., Anzai, H.: Prediction of 3d cardiovascular hemodynamics before and after coronary artery bypass surgery via deep learning. Communications Biology 4 (01 2021)
- <span id="page-107-15"></span>16. Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., Zhang, Z., Lin, S., Guo, B.: Swin transformer: Hierarchical vision transformer using shifted windows. In: 2021 IEEE/CVF International Conference on Computer Vision, ICCV, Montreal, QC, Canada, October 10-17, 2021 (2021)
- <span id="page-107-2"></span>17. Qi, C.R., Yi, L., Su, H., Guibas, L.J.: Pointnet + +: Deep hierarchical feature learning on point sets in a metric space. In: Annual Conference on Neural Information Processing Systems 2017, December 4-9, 2017, Long Beach, CA, USA (2017)
- <span id="page-107-8"></span>18. Rabe, M.N., Staats, C.: Self-attention does not need  $O(n^2)$  memory. In: n/a (2021)
- <span id="page-107-12"></span>19. Ruhe, D., Brandstetter, J., Forré, P.: Clifford group equivariant neural networks. In: Thirty-seventh Conference on Neural Information Processing Systems (2023)

- <span id="page-108-7"></span>20. Ruhe, D., Gupta, J.K., Keninck, S.D., Welling, M., Brandstetter, J.: Geometric clifford algebra networks. In: International Conference on Machine Learning, ICML, 23-29 July 2023, Honolulu, Hawaii, USA (2023)
- <span id="page-108-1"></span>21. Sarasua, I., Pölsterl, S., Wachinger, C.: Transformesh: A transformer network for longitudinal modeling of anatomical meshes. In: Machine Learning in Medical Imaging. pp. 209–218. Springer International Publishing, Cham (2021)
- <span id="page-108-6"></span>22. Shirzad, H., Velingker, A., Venkatachalam, B., Sutherland, D.J., Sinop, A.K.: Exphormer: Sparse transformers for graphs. In: International Conference on Machine Learning, ICML, 23-29 July 2023, Honolulu, Hawaii, USA (2023)
- <span id="page-108-3"></span>23. Suk, J., Brune, C., Wolterink, J.M.: Se(3) symmetry lets graph neural networks learn arterial velocity estimation from small datasets. In: Bernard, O., Clarysse, P., Duchateau, N., Ohayon, J., Viallon, M. (eds.) Functional Imaging and Modeling of the Heart. pp. 445–454. Springer Nature Switzerland, Cham (2023)
- <span id="page-108-8"></span>24. Suk, J., de Haan, P., Lippe, P., Brune, C., Wolterink, J.M.: Mesh neural networks for se(3)-equivariant hemodynamics estimation on the artery wall. ArXiv (2022)
- <span id="page-108-4"></span>25. Suk, J., Haan, P.d., Lippe, P., Brune, C., Wolterink, J.M.: Mesh convolutional neural networks for wall shear stress estimation in 3d artery models. In: Statistical Atlases and Computational Models of the Heart. Multi-Disease, Multi-View, and Multi-Center Right Ventricular Segmentation in Cardiac MRI Challenge. pp. 93– 102. Springer International Publishing, Cham (2022)
- <span id="page-108-9"></span>26. Unyi, D., Gyires-Tóth, B.: Neurodevelopmental phenotype prediction: A state-ofthe-art deep learning model. In: Machine Learning for Health, ML4H, 28 November 2022, New Orleans, Lousiana, USA & Virtual. Proceedings of Machine Learning Research, vol. 193, pp. 279–289. PMLR (2022)
- <span id="page-108-5"></span>27. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. In: Annual Conference on Neural Information Processing Systems, December 4-9, 2017, Long Beach, CA, USA (2017)
- <span id="page-108-0"></span>28. Vosylius, V., Wang, A., Waters, C., Zakharov, A., Ward, F., Le Folgoc, L., Cupitt, J., Makropoulos, A., Schuh, A., Rueckert, D., Alansary, A.: Geometric deep learning for post-menstrual age prediction based on the neonatal white matter cortical surface. In: Uncertainty for Safe Utilization of Machine Learning in Medical Imaging, and Graphs in Biomedical Image Analysis. pp. 174–186. Springer International Publishing, Cham (2020)
- <span id="page-108-2"></span>29. Zhao, F., Wu, Z., Li, G.: Deep learning in cortical surface-based neuroimage analysis: a systematic review. Intelligent Medicine 3(1), 46–58 (2023)

Image /page/109/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a ribbon or bookmark shape inside it. The text below the icon reads "Check for updates".

# Learning Temporally Equivariance for Degenerative Disease Progression in OCT by Predicting Future Representations

Taha ${\rm Emre}^{\rm (\boxtimes)},$  Arunava Chakravarty, Dmitrii Lachinov, Antoine Rivail, Ursula Schmidt-Erfurth, and Hrvoje Bogunović

Department of Ophthalmology and Optometry, Medical University of Vienna, Vienna, Austria {taha.emre,hrvoje.bogunovic}@meduniwien.ac.at

Abstract. Contrastive pretraining provides robust representations by ensuring their invariance to different image transformations while simultaneously preventing representational collapse. Equivariant contrastive learning, on the other hand, provides representations sensitive to specific image transformations while remaining invariant to others. By introducing equivariance to time-induced transformations, such as disease-related anatomical changes in longitudinal imaging, the model can effectively capture such changes in the representation space. In this work, we propose a Time-equivariant Contrastive Learning (TC) method. First, an encoder embeds two unlabeled scans from different time points of the same patient into the representation space. Next, a temporal equivariance module is trained to predict the representation of a later visit based on the representation from one of the previous visits and the corresponding time interval with a novel regularization loss term while preserving the invariance property to irrelevant image transformations. On a large longitudinal dataset, our model clearly outperforms existing equivariant contrastive methods in predicting progression from intermediate agerelated macular degeneration (AMD) to advanced wet-AMD within a specified time-window.

**Keywords:** Equivariance  $\cdot$  Contrastive Learning  $\cdot$  AMD  $\cdot$  Prediction

# 1 Introduction

The advent of contrastive self-supervised learning (SSL) methods showed that it is possible to learn informative and discriminative image representations by learning invariances to image transformations that do not alter their semantics.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_19.](https://doi.org/10.1007/978-3-031-72390-2_19)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 196–206, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_19)\_19

The invariance is achieved by increasing the representational similarity between the original image and its transformed versions in the representation space, while pushing all other pairs apart as negatives using a Siamese network [\[1\]](#page-117-0). However, sensitivity (*equivariance*) to certain transformations may be crucial for specific downstream tasks, such as rotation degree information in histopathology slides [\[21](#page-118-0)]. Introducing equivariance to time-induced transformations, such as anatomical changes due to degenerative disease progression in longitudinal medical images could be pivotal in downstream tasks such as predicting the future risk of conversion of a patient from an early to a late-stage.

Retinal degenerative diseases such as Age-related Macular Degeneration (AMD) are frequently identified through non-invasive optical coherence tomography (OCT) scans, made up of sequential 2D cross-sectional slices (B-scans). As a leading cause of vision loss and blindness [\[3\]](#page-117-1), AMD leads to irreversible tissue damage, implying that the severity of the disease can be represented as a monotonically non-decreasing function over time. However, the speed of disease progression and the associated anatomical changes vary widely between patients. Also, the longitudinal nature of the AMD datasets makes them costly to label by clinicians. Consequently, a Time-Equivariant SSL stage could be crucial in identifying the patients at a higher risk of progression to an advanced disease stage from a single visit to provide timely treatment and personalized management.

Existing *Equivariant SSL* methods aim to learn such sensitivity, unlike contrastive methods that seek total invariance. Indeed, invariance to certain transformations is not always desirable [\[22\]](#page-119-0). This sensitivity can be achieved with pretext SSL, by learning to recover the parameter of the target transformation from the representations. Image rotation prediction [\[11\]](#page-118-1), video clip ordering [\[23\]](#page-119-1) and distinguishing close and far clips [\[13](#page-118-2)] for temporal equivariance can be given as examples. In medical imaging, the time difference [\[19\]](#page-118-3) or ordering two visits (scans) of a patient [\[16](#page-118-4)] is commonly used as a pretraining step for temporal tasks such as disease forecasting. In [\[27\]](#page-119-2) they decoupled the effect of aging from disease-related changes in brain MRI scans by carefully curating a temporal dataset consisting of old subjects without any underlying diseases. Then, they learned an aging trajectory in the representation space shared by all patients. Although these methods learn sensitivity to the transformation, they lack the desired invariance to image perturbations.

To address these issues, *Equivariant contrastive methods* have been explored to learn equivariance for specific transformations while ensuring invariance for the others. Unlike methods with specific architecture [\[6](#page-118-5)] for achieving equivariance, equivariant contrastive learning methods are mostly architecture agnostic. Dangovski et al. [\[7\]](#page-118-6) combined transformation parameter prediction with a contrastive loss for the transformation invariance, requiring four additional encoders for the rotation prediction task [\[11](#page-118-1)], increasing the computational overhead substantially *(ESSL)*. Similarly, in [\[14\]](#page-118-7), they proposed an additional temporal pretext task, such as frame ordering, to the contrastive task for videos. Emre et al. [\[9](#page-118-8)] introduced *TINC* for OCT scans where they exploited the time difference between visits by applying a similarity insensitivity increasing with the difference. In [\[17](#page-118-9)], *AugSelf* is proposed to enforce equivariance on the representation space by predicting transformation parameters between input pairs, then projected the learned representations into an invariant space for the contrastive task. However, transformation parameter recovery does not guarantee explicit equivariance, because it's possible for transformation parameters to be confined to a single dimension of the representation, leaving the rest completely invariant. In addition, parameter prediction models do not learn any explicit function for directly transforming the representations. To address these limitations, Devillers et al. [\[8\]](#page-118-10) proposed utilizing two different projections of the representation space; one for the invariance, and the second for the equivariance with a trainable module allowing direct manipulation of the projections with the image transformation parameter (*EquiMod*). Since invariance is a trivial solution for the equivariance where the equivariant representation transformation collapses to identity, they introduced an additional contrastive loss for the equivariance branch at the cost of an increased computational overhead. These methods aim to achieve equivariance using transformation parameter recovery, additional contrastive tasks, projection spaces, or pre-defined image transformation (e.g. rotation matrix) [\[10](#page-118-11)].

We propose to learn time-sensitive/equivariant representations directly on the *representation space* and learn to propagate representations in time, using longitudinal medical datasets in a contrastive SSL setting. Given that degenerative diseases either progress or stagnate over time, we have modeled disease progression in the representation space as an additive transformation parameterized by the time difference of a patient's visits, enabling direct temporal transformation of representations. We adapted a regularization loss on the calculated additive term to prevent the network from ignoring the time difference and producing identity transformation. Finally, we evaluated the learned representations for AMD progression prediction across multiple time-windows from a single visit scan. In summary, our contributions are (a) constructing contrastive pairs from different visits of a patient, (b) training a novel equivariance module to propagate representation in the future without accessing future visit scan, (c) introducing a novel regularization loss to prevent invariance to time.

# 2 Methodology

Our approach learns temporal equivariance in representation space, followed by a projection to invariant space within a contrastive SSL framework (Fig. [1\)](#page-112-0). We propose a novel loss term specifically tailored for the equivariance  $(Eq. 5)$  $(Eq. 5)$ , utilizing a longitudinal retinal OCT dataset (Sect. [3.1\)](#page-114-1) collected across multiple patient visits. First, we define the term time equivariant representation. Then, we detail our approach to learn the equivariance.

Time-Equivariant Representations. The disease progression in a patient is routinely monitored using a series of medical scans  $x \in \mathbb{I} \subseteq \mathbb{R}^{H \times W}$ , and  $x_t$  denotes the scan acquired at a time t sampled from  $\mathbb{T} = \{t \in \mathbb{N}_0; t \leq b\},\$ 

Image /page/112/Figure/1 description: This figure illustrates a deep learning architecture for analyzing temporal transformations in medical imaging, likely Optical Coherence Tomography (OCT). Part (A) shows a pipeline where two OCT images, r\_t and r\_{t+\Delta t}, are processed. Each image undergoes a random transformation and is then passed through a feature extractor 'f' and a projection layer 'g' to obtain representations z\_t and z\_{t+\Delta t}. An 'Equivariance Module h\_\psi' takes the outputs of 'f' from both images and the temporal transformation information \Delta t to enforce equivariance. The outputs of 'g' are fed into an 'Invariance Task'. Part (B) provides a more detailed view of the 'Equivariance Module h\_\psi'. It shows that the representation r\_t and the temporal difference \Delta t are used to compute a prediction \rho\_\psi, which is then combined with r\_t via an addition operation to produce a transformed representation r'\_{t+\Delta t}. This process also involves a 'Regularization Loss' and contributes to an 'Equivariance Loss'.

<span id="page-112-0"></span>Fig. 1. An overview of TC architecture. (A) Representations of two scans acquired at two time points and the time difference are fed into  $h_{\psi}$  for equivariance, then projected for the invariance loss. (B) Equivariance module with an additive displacement map.

b being the last visit date. Let  $\mu : \mathbb{I} \times \mathbb{T} \to \mathbb{I}$  represent a transformation in the image space for all possible time differences, which captures the complex anatomical changes between  $x_t$  and  $x_{t+\Delta t}$  due to the disease progression (and/or normal aging), where  $t + \Delta t \in \mathbb{T}$ . Although the actual transformation  $\mu$  is difficult to model in the image space, the result of the transformation is available as  $x_{t+\Delta t}$  for some specific  $\Delta t$ , from available visits of the patient, such that  $x_{t+\Delta t} := \mu(x_t, \Delta t)$ . Let  $f_\theta : \mathbb{I} \to \mathbb{R}^D$  with learnable parameters  $\theta$  be a deep learning based encoder, which can embed  $x_t$  as a representation  $r_t := f_\theta(x_t)$  and  $x_{t+\Delta t}$  as  $r_{t+\Delta t} := f_{\theta}(x_{t+\Delta t})$ . As  $\mu(\cdot, \Delta t)$  transforms  $x_t$  to  $x_{t+\Delta t}$  in the image space, a corresponding transformation  $h(\cdot, \Delta t)$  operates in the representation space such that  $r_{t+\Delta t} = h(r_t, \Delta t)$ . If  $f_\theta$  is equivariant in time, the **equivariant** relation is defined as:

<span id="page-112-1"></span>
$$
\exists h : \forall \Delta t \in \mathbb{T}, \quad f_{\theta}(\mu(\boldsymbol{x}_t, \Delta t)) = f_{\theta}(\boldsymbol{x}_{t + \Delta t}) \approx h(f_{\theta}(\boldsymbol{x}_t), \Delta t)
$$
(1)

An undesired trivial solution for Eq. [1](#page-112-1) would be *time-invariance* of  $f_{\theta}$  such that  $f_{\theta}(\boldsymbol{x}_t) = f_{\theta}(\boldsymbol{x}_{t+\Delta t})$  with disease progression being ignored, in turn making  $h(\cdot, \Delta t)$  an identity mapping. This implies that  $f_{\theta}$  learns only patient-specific features staying constant with respect to time while ignoring changes due to the progression. The transformation  $h(\cdot, \Delta t)$  should be able to capture the future changes in the scans specific to each patient, and we propose to approximate it with a deep neural network that can model complex changes.

Time-Equivariant Contrastive Learning (TC). Our model extends the existing contrastive methods with the introduction of a temporal equivariance property. The input pair to the Siamese network is created from two different visits of a patient and both are transformed with contrastive augmentations (Fig. [1.](#page-112-0)A). Recent equivariant contrastive methods with a learnable transformation [\[8](#page-118-10)[,10](#page-118-11)] on features, relied on two projection spaces, one for equivariance and the other for invariance, preventing them from directly steering the representations. Instead, we propose to enforce equivariance directly on the representation space, and then to learn the invariance on a *projection* space [\[15](#page-118-12)] (Fig. [1.](#page-112-0)A). This allows for the preservation of the time-sensitive information in the representation space while enforcing invariance in the projection space. A logical order since invariance is a trivial solution of equivariance. It also simplifies the network architecture and computational overhead, and facilitates transferring the encoder weights as a backbone.

We introduce an equivariance module  $h_{\psi}$  containing the learned transformation (**predictor**) with learnable parameters  $\psi$ , which takes the concatenation of representation  $r_t$  and time difference  $\Delta t$ , normalized between 0–1, as input, and transforms  $r_t$  to  $r'_{t+\Delta t}$  to satisfy the equivariance property defined in Eq. [1.](#page-112-1)<br>The predictor enables the model to generate future visits' representations by for-The predictor enables the model to generate future visits' representations by forwarding available scans in time to be used in predictive tasks. The equivariance loss [\[8](#page-118-10),[10\]](#page-118-11) for  $h_{\psi}$  is defined as:

$$
\ell_{\text{equiv}} = ||f_{\theta}(\boldsymbol{x}_{t+\Delta t}) - h_{\psi}(f_{\theta}(\boldsymbol{x}_t), \Delta t)||_2^2 = ||r_{t+\Delta t} - h_{\psi}(r_t, \Delta t)||_2^2 \tag{2}
$$

As it is highlighted in  $[8,10]$  $[8,10]$  $[8,10]$ , the contrastive loss imposes invariance to the image transformations including time-related changes. The learned invariance results in a trivial solution for the equivariance loss where  $h_{\psi}$  collapses to an identity mapping, ignoring  $\Delta t$ . The aforementioned methods rely on another set of computationally heavy contrastive task on  $r'_{t+\Delta t}$  to prevent collapse. For an MLP  $h_{\psi}$ , the collapse manifests as  $\frac{\partial h_{\psi}}{\partial t}$  becoming 0. To avoid an additional contrastive task or a regularization term on the gradients to prevent  $h_{\psi}$  from contrastive task or a regularization term on the gradients to prevent  $h_{\psi}$  from collapsing, we reparametrized  $h_{\psi}$  as an additive **displacement map** (DM):

<span id="page-113-0"></span>
$$
r'_{t+\Delta t} = h_{\psi}(r_t, \Delta t) = r_t \oplus \rho_{\psi}(r_t, \Delta t)
$$
\n(3)

Now, the equivariant transformation  $h_{\psi}$  has two parts: an MLP  $\rho_{\psi}$  for predicting DM as an additive term and *direct sum* of the DM with the representation (Fig. [1.](#page-112-0)B). In Eq. [3,](#page-113-0) the collapse condition is much easier to regularize since it is the zero norm for the predicted DM. The ideal regularization should prevent DM norm becoming zero, while not encouraging high norm values. The representation space should respond to the time change smoothly without predicting large DMs easily. We adapted a regularization loss term for preventing collapse from pair-wise loss of RankNet [\[4](#page-117-2)]. The original loss function aims to increase the probability of ranking two embeddings correctly. In our case, the ranking is always one-sided, because the norm cannot take negative values. Thus, the final DM regularization term becomes:

$$
\ell_{\text{reg}} = \log\left(1 + \exp(-||\rho_{\psi}(r_t, \Delta t)||_2)\right) \tag{4}
$$

The  $\ell_{\text{reg}}$  penalizes null displacements without encouraging very high norms.

For the invariant contrastive loss, we used VICReg [\[2](#page-117-3)]. It is calculated using projections  $z_t$  and  $z_{t+\Delta t}$ , obtained by projecting representations  $r_t$  and  $r_{t+\Delta t}$ with an MLP projector  $g_{\gamma}$ . As a *non-contrastive* method, it does not require very large batch sizes. It has 3 loss terms; an  $\ell_2$  term for increasing similarity within

pair, a covariance term to prevent *dimensional collapse* [\[15,](#page-118-12)[26](#page-119-3)] and a variance term for implicitly pushing the negatives apart which is calculated along a batch of projections, where each pair could have different  $\Delta t$  values.

Thus, the final training loss with a contrastive loss  $\ell_{\text{contr}}$  is:

<span id="page-114-0"></span>
$$
\mathcal{L}_{\text{TC}} = \ell_{\text{contr}} + \beta \cdot (\ell_{\text{equiv}} + v \cdot \ell_{\text{reg}}) \tag{5}
$$

## 3 Experiments and Results

We tested **TC** against three main equivariant contrastive methods: ESSL [\[7\]](#page-118-6), AugSelf [\[17](#page-118-9)] and EquiMod [\[8\]](#page-118-10). ImageNet pretrained ResNet50, VICReg [\[2](#page-117-3)] and TINC [\[9](#page-118-8)] were used as baselines. During pretraining each batch contains only a single image pair from a patient to avoid pushing apart the intra-patient scans with the contrastive loss. Clinically, only future representations matter for conversion risk assessment. Therefore, the equivariant models were pre-trained only with image pairs with a positive  $\Delta t$ .

### 3.1 Experimental Setup

Image /page/114/Figure/7 description: The image displays optical coherence tomography (OCT) scans of the retina over time, illustrating the progression to wet age-related macular degeneration (AMD). The left panel shows a timeline with three OCT scans. The first scan, at -4 months, shows a normal-looking retina with a yellow arrow pointing to a subtle irregularity. The second scan, at -2 months, also shows a normal retina with a yellow arrow indicating a similar irregularity. The third scan, at +2 months, shows significant changes, including subretinal fluid and pigment epithelial detachment, highlighted by a red box, indicating the conversion to wet AMD. A dotted red line marks time zero, labeled "Conversion to Wet-AMD." The right panel presents six additional OCT scans, likely representing different stages or examples of retinal conditions, some showing fluid accumulation and structural changes.

<span id="page-114-2"></span>Fig. 2. Left: OCT scans with conversion to wet-AMD over time. Right: Contrastive augmentations examples.

<span id="page-114-1"></span>Dataset. The SSL stage and the downstream evaluation were conducted on the HARBOR dataset (Fig. [2](#page-114-2) left), comprising OCT scans from 1,096 patients' *fellow-eyes*[1](#page-114-3) monitored for wet-AMD onset, with monthly follow-ups over 24 months. Intermediate stage visits (463 eyes - 10,108 scans) are used for predicting the onset of late, wet-AMD stage within 6 and 12 months  $[20, 24, 25]$  $[20, 24, 25]$  $[20, 24, 25]$  (117) converter eyes) as a forecasting task. The dataset was split with the eye level stratification into 4-fold cross-validation (80%) for hyper-parameter optimization and a hold-out test set (20%) for reporting the performance. The remaining 540 eyes (12,506 scans) are only used in contrastive pretraining step. We used

<span id="page-114-3"></span> $1$  The other eye that is not part of the clinical trial.

the fovea centered 2D B-scan, which is known to be most representative of the AMD state [\[18](#page-118-14)], resized to  $224 \times 224$  $224 \times 224$  $224 \times 224$ .

We followed the work of  $[9,12]$  $[9,12]$  for contrastive image transformations specific to B-scans (Fig. [2](#page-114-2) right). During pretraining, for each input pair,  $\Delta t$  is **randomly** sampled from an interval of 1 to 12 months and normalized between 0–1. The rationale behind the interval is that the set of all pair permutations for all possible time differences is very large (276 per patient) for a plausible training time. By limiting  $\Delta t$ , training  $h_{\psi}$  becomes much more convenient, because it only needs to produce DMs for a constrained but practically meaningful interval.

Implementation Details. A ResNet50 with a representation dimension of 2048 is used as the encoder  $f_{\theta}$ . Following the common practice of projecting representations to higher dimensions [\[2,](#page-117-3)[26\]](#page-119-3), the projector  $g_{\gamma}$  is a three-layer MLP with hidden dimensions of 4096. DM predictor  $\rho_{\psi}$  of TC is implemented as a two-layer MLP with an input size of  $(2048 + 1)$  for  $[r_t, \Delta t]$ , and it outputs a DM with the same dimension as the representations. In EquiMod, ESSL and AugSelf, we followed the original works for the predictor architecture.

All models were pretrained for 300 epochs with the AdamW optimizer with a learning rate of 5E-4 and a batch size of 128. A cosine scheduler with a warm-up was used for decaying learning rates. The same weight decay of 1E-6 was applied to all pretraining setups.  $\beta$  and  $v$  of  $\mathcal{L}_{TC}$  were set to 1 and 0.5, respectively.

## 3.2 Results

| Model                      | 6-months                            |                                     |                                     | 12-months                           |                                     |                                     |
|----------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|
|                            | AUROC $\uparrow$                    | PRAUC $\uparrow$                    | BAcc $\uparrow$                     | AUROC $\uparrow$                    | PRAUC $\uparrow$                    | BAcc $\uparrow$                     |
| ImageNet                   | 0.714 $\pm$ 0.018                   | 0.106 $\pm$ 0.007                   | 0.648 $\pm$ 0.016                   | 0.692 $\pm$ 0.014                   | 0.167 $\pm$ 0.009                   | 0.616 $\pm$ 0.045                   |
| VICReg [2]                 | 0.700 $\pm$ 0.029                   | 0.137 $\pm$ 0.031                   | 0.650 $\pm$ 0.021                   | 0.671 $\pm$ 0.033                   | 0.158 $\pm$ 0.019                   | 0.622 $\pm$ 0.023                   |
| TINC [9]                   | 0.729 $\pm$ 0.017                   | 0.151 $\pm$ 0.019                   | 0.652 $\pm$ 0.007                   | 0.688 $\pm$ 0.060                   | 0.210 $\pm$ 0.026                   | 0.629 $\pm$ 0.055                   |
| ESSL [7]                   | 0.718 $\pm$ 0.033                   | 0.157 $\pm$ 0.034                   | 0.649 $\pm$ 0.028                   | 0.699 $\pm$ 0.041                   | 0.187 $\pm$ 0.039                   | 0.637 $\pm$ 0.032                   |
| AugSelf [17]               | 0.752 $\pm$ 0.031                   | 0.190 $\pm$ 0.027                   | 0.670 $\pm$ 0.029                   | 0.735 $\pm$ 0.049                   | 0.245 $\pm$ 0.040                   | 0.651 $\pm$ 0.057                   |
| EquiMod [8]                | 0.734 $\pm$ 0.035                   | 0.153 $\pm$ 0.038                   | 0.652 $\pm$ 0.048                   | 0.718 $\pm$ 0.059                   | 0.226 $\pm$ 0.055                   | 0.657 $\pm$ 0.031                   |
| TC w/o DM                  | 0.761 $\pm$ 0.032                   | 0.163 $\pm$ 0.027                   | 0.658 $\pm$ 0.034                   | 0.748 $\pm$ 0.033                   | 0.220 $\pm$ 0.026                   | 0.653 $\pm$ 0.031                   |
| TC w/o $\ell_{\text{reg}}$ | 0.732 $\pm$ 0.021                   | 0.137 $\pm$ 0.024                   | 0.661 $\pm$ 0.009                   | 0.721 $\pm$ 0.035                   | 0.195 $\pm$ 0.025                   | 0.655 $\pm$ 0.028                   |
| TC                         | <b>0.769 <math>\pm</math> 0.027</b> | <b>0.185 <math>\pm</math> 0.032</b> | <b>0.677 <math>\pm</math> 0.026</b> | <b>0.752 <math>\pm</math> 0.034</b> | <b>0.265 <math>\pm</math> 0.043</b> | <b>0.671 <math>\pm</math> 0.032</b> |
| $TC_{syn}$                 | 0.784 $\pm$ 0.022                   | 0.188 $\pm$ 0.036                   | 0.683 $\pm$ 0.024                   | 0.775 $\pm$ 0.030                   | 0.304 $\pm$ 0.052                   | 0.695 $\pm$ 0.023                   |

<span id="page-115-0"></span>Table 1. Linear evaluation results for wet-AMD conversion prediction within two time-windows: 6 and 12 months

Downstream Prediction Performance. After pretraining, we evaluated the extracted representations with a linear classifier on the downstream tasks

 $(Sect. 3.1)$  $(Sect. 3.1)$ , as it is common practice in contrastive SSL [\[5\]](#page-117-4). A scan from a single time-point is used for future conversion prediction. The linear classifier is trained for 50 epochs with Adam optimizer and a learning rate of 1E−4. We reported the results (Table [1\)](#page-115-0) with AUROC, PRAUC and Balanced Accuracy (BAcc) due to a large class imbalance (6-month 1:20, 12-month 1:10). Accordingly, the random prediction PRAUC for 6-month is 0.05, and for 12-month is 0.11.

Our TC model surpassed all the other ones in AUROC and BAcc in both tasks. In terms of 12-month PRAUC, TC outperformed all while achieving competitive 6-month PRAUC performance. Interestingly, AugSelf achieved better results against the comparison methods, which can be attributed to its enforcement of the equivariance in the representation space rather than the projection space. A decline in 12-month prediction accuracy was noted for all the models, likely due to the extended time frame and the fact that the predictive biomarkers were more subtle. Finally, the performance gains of equivariant contrastive methods over the others (Table [1](#page-115-0) line 1–3) underscore the importance of including temporal equivariance in disease progression prediction.

Evaluation of the Equivariance Module. Each scan representation is synthetically propagated in time by 6 months using equivariance module  $h_{\psi}$  without accessing the future scans. Then they were combined with the initial representations by averaging. Their predictive capability was assessed using the same linear evaluation, with the improved performances in Table  $1$  -  $TC_{syn}$  demonstrating the equivariance module's ability to discern significant disease progression from a single scan. Notably, this performance increase was more distinct in the 12-month task, where the biomarkers are less distinguishable due to the longer conversion time-frame. Similar improvement is not possible with the other equivariant contrastive methods since they cannot manipulate the representation space.

Ablation Study. We investigated the impact of the explicit DM prediction (Table [1](#page-115-0) - TC w/o  $DM$ ) and the regularization loss term (Table 1 - TC w/o  $\ell_{\text{reg}}$ ) components of TC by removing each at the time. In both cases, the downstream task performance degraded considerably underscoring the importance of both components. When compared against the non-equivariant methods, the ablated models still perform better highlighting the importance of time component. When TC w/o  $DM$  compared against TC w/o  $\ell_{\text{reg}}$ , it performs considerably better. This can be attributed to TC w/o *DM* directly predicting the future representation without relying on an unregularized DM. This highlights the significance of  $\ell_{\text{reg}}$  to predict meaningful DMs.

Limitations. The main limitation of TC is that the displacement map and the Siamese pair forming rely on the assumption of irreversible disease progression. Even though the most degenerative diseases fall into this category, it limits the general use of the future prediction module for the other temporal tasks.

# 4 Conclusion

Predicting late-stage disease onset is challenging due to the varying progression speeds among patients and subtle prognostic biomarkers. In this study, we proposed exploiting temporal information in unlabeled longitudinal OCT datasets. We introduced Time-equivariant Contrastive (TC) SSL with a learnable equivariance module for directly propagating image representations in time. The module retains characteristics of disease progression, such as irreversible tissue loss or degeneration, over time. A novel regularization loss was introduced to avoid the trivial solution for the equivariance induced by the contrastive loss with minimal computational overhead, unlike other equivariant models. We tested TC for disease progression on a longitudinal dataset consisting of eye scans with different stages of AMD. The TC pretraining consistently outperformed other equivariant contrastive methods as well as non-equivariant ones. Results highlight the importance of temporal sensitivity in representations for accurately assessing a patient's risk of conversion to a late disease stage. Furthermore, we found that integrating the synthetically propagated representations with the training data enhanced the conversion prediction performance, illustrating not only that TC captured relevant longitudinal information but also provided a functional prediction module, demonstrating its ability to generate a proper time-equivariant representation space.

Acknowledgments. The work has been partially funded by FWF Austrian Science Fund (FG 9-N), and a Wellcome Trust Collaborative Award (PINNACLE Ref. 210572/Z/18/Z).

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-117-0"></span>1. Azizi, S., Mustafa, B., Ryan, F., Beaver, Z., Freyberg, J., Deaton, J., Loh, A., Karthikesalingam, A., Kornblith, S., Chen, T., et al.: Big self-supervised models advance medical image classification. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 3478–3488 (2021)
- <span id="page-117-3"></span>2. Bardes, A., Ponce, J., LeCun, Y.: Vicreg: Variance-invariance-covariance regularization for self-supervised learning. In: International Conference on Learning Representations (2022)
- <span id="page-117-1"></span>3. Bressler, N.M.: Age-Related Macular Degeneration Is the Leading Cause of Blindness . . . JAMA 291(15), 1900–1901 (04 2004). [https://doi.org/10.1001/jama.291.](https://doi.org/10.1001/jama.291.15.1900) [15.1900](https://doi.org/10.1001/jama.291.15.1900)
- <span id="page-117-2"></span>4. Burges, C., Shaked, T., Renshaw, E., Lazier, A., Deeds, M., Hamilton, N., Hullender, G.: Learning to rank using gradient descent. In: International Conference on Machine Learning. pp. 89– 96 (2005)
- <span id="page-117-4"></span>5. Chen, X., Xie, S., He, K.: An empirical study of training self-supervised vision transformers. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 9620–9629 (2021)

- <span id="page-118-5"></span>6. Cohen, T.S., Welling, M.: Group equivariant convolutional networks. In: International Conference on Machine Learning. PMLR, JMLR.org (2016)
- <span id="page-118-6"></span>7. Dangovski, R., Jing, L., Loh, C., Han, S., Srivastava, A., Cheung, B., Agrawal, P., Soljacic, M.: Equivariant self-supervised learning: Encouraging equivariance in representations. In: International Conference on Learning Representations (2022), <https://openreview.net/forum?id=gKLAAfiytI>
- <span id="page-118-10"></span>8. Devillers, A., Lefort, M.: Equimod: An equivariance module to improve visual instance discrimination. In: International Conference on Learning Representations (2023), <https://openreview.net/forum?id=eDLwjKmtYFt>
- <span id="page-118-8"></span>9. Emre, T., Chakravarty, A., Rivail, A., Riedl, S., Schmidt-Erfurth, U., Bogunović, H.: Tinc: Temporally informed non-contrastive learning for disease progression modeling in retinal oct volumes. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 625–634. Springer (2022)
- <span id="page-118-11"></span>10. Garrido, Q., Najman, L., Lecun, Y.: Self-supervised learning of split invariant equivariant representations. In: International Conference on Machine Learning. PMLR (2023)
- <span id="page-118-1"></span>11. Gidaris, S., Singh, P., Komodakis, N.: Unsupervised representation learning by predicting image rotations. In: International Conference on Learning Representations (2018)
- <span id="page-118-15"></span>12. Holland, R., et al.: Metadata-enhanced contrastive learning from retinal optical coherence tomography images. CoRR abs/2208.02529 (2022)
- <span id="page-118-2"></span>13. Jayaraman, D., Grauman, K.: Slow and steady feature analysis: higher order temporal coherence in video. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3852–3861 (2016)
- <span id="page-118-7"></span>14. Jenni, S., Jin, H.: Time-equivariant contrastive video representation learning. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 9970–9980 (2021)
- <span id="page-118-12"></span>15. Jing, L., Vincent, P., LeCun, Y., Tian, Y.: Understanding dimensional collapse in contrastive self-supervised learning. In: International Conference on Learning Representations (2022), <https://openreview.net/forum?id=YevsQ05DEN7>
- <span id="page-118-4"></span>16. Kim, H., Sabuncu, M.R.: Learning to compare longitudinal images. In: Medical Imaging with Deep Learning (2023)
- <span id="page-118-9"></span>17. Lee, H., Lee, K., Lee, K., Lee, H., Shin, J.: Improving transferability of representations via augmentation-aware self-supervision. In: Beygelzimer, A., Dauphin, Y., Liang, P., Vaughan, J.W. (eds.) Advances in Neural Information Processing Systems (2021), <https://openreview.net/forum?id=U34rQjnImpM>
- <span id="page-118-14"></span>18. Lin, A.C., Lee, C.S., Blazes, M., Lee, A.Y., Gorin, M.B.: Assessing the clinical utility of expanded macular octs using machine learning. Translational vision science & technology  $10(6)$ , 32-32 (2021)
- <span id="page-118-3"></span>19. Rivail, A., Schmidt-Erfurth, U., Vogl, W.D., Waldstein, S.M., Riedl, S., Grechenig, C., Wu, Z., Bogunovic, H.: Modeling disease progression in retinal octs with longitudinal self-supervised learning. In: International Workshop on PRedictive Intelligence In MEdicine. pp. 44–52. Springer (2019)
- <span id="page-118-13"></span>20. Russakoff, D.B., Lamin, A., Oakley, J.D., Dubis, A.M., Sivaprasad, S.: Deep learning for prediction of amd progression: a pilot study. Investigative ophthalmology & visual science  $60(2)$ , 712–722 (2019)
- <span id="page-118-0"></span>21. Veeling, B.S., Linmans, J., Winkens, J., Cohen, T., Welling, M.: Rotation equivariant cnns for digital pathology. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2018: 21st International Conference, Granada, Spain, September 16-20, 2018, Proceedings, Part II 11. pp. 210–218. Springer (2018)

- <span id="page-119-0"></span>22. Xiao, T., Wang, X., Efros, A.A., Darrell, T.: What should not be contrastive in contrastive learning. In: International Conference on Learning Representations (2020)
- <span id="page-119-1"></span>23. Xu, D., Xiao, J., Zhao, Z., Shao, J., Xie, D., Zhuang, Y.: Self-supervised spatiotemporal learning via video clip order prediction. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 10334–10343 (2019)
- <span id="page-119-4"></span>24. Yan, Q., Weeks, D.E., Xin, H., Swaroop, A., Chew, E.Y., Huang, H., Ding, Y., Chen, W.: Deep-learning-based prediction of late age-related macular degeneration progression. Nature machine intelligence 2(2), 141–150 (2020)
- <span id="page-119-5"></span>25. Yim, J., Chopra, R., Spitz, T., Winkens, J., Obika, A., Kelly, C., Askham, H., Lukic, M., Huemer, J., Fasler, K., et al.: Predicting conversion to wet age-related macular degeneration using deep learning. Nature Medicine 26(6), 892–899 (2020)
- <span id="page-119-3"></span>26. Zbontar, J., Jing, L., Misra, I., LeCun, Y., Deny, S.: Barlow twins: Self-supervised learning via redundancy reduction. In: International Conference on Machine Learning. pp. 12310–12320. PMLR (2021)
- <span id="page-119-2"></span>27. Zhao, Q., Liu, Z., Adeli, E., Pohl, K.M.: Longitudinal self-supervised learning. Medical Image Analysis 71, 102051 (2021). [https://doi.org/10.1016/j.media.2021.](https://doi.org/10.1016/j.media.2021.102051) [102051,](https://doi.org/10.1016/j.media.2021.102051) <https://www.sciencedirect.com/science/article/pii/S1361841521000979>

Image /page/120/Picture/0 description: A square button with a rounded border contains a circular icon and text. The icon is a circle with a bookmark shape inside. The text below the icon reads "Check for updates".

# Leveraging Image Captions for Selective Whole Slide Image Annotation

Jingna Qiu<sup>1(⊠)</sup>, Marc Aubreville<sup>2,3</sup>, Frauke Wilm<sup>1,4</sup>, Mathias Öttl<sup>1,4</sup>, Jonas Utz<sup>1</sup>, Maja Schlereth<sup>1</sup>, and Katharina Breininger<sup>1,5</sup>

 $^{\rm 1}$  Department Artificial Intelligence in Biomedical Engineering, FAU Erlangen-Nürnberg, Erlangen, Germany <EMAIL>

<EMAIL>

<sup>2</sup> Technische Hochschule Ingolstadt, Ingolstadt, Germany

<sup>3</sup> Flensburg University of Applied Sciences, Flensburg, Germany

<sup>4</sup> Pattern Recognition Lab, Department of Computer Science, FAU

Erlangen-Nürnberg, Erlangen, Germany

 $5$  Center for AI and Data Science, Universität Würzburg, Würzburg, Germany

**Abstract.** Acquiring annotations for whole slide images (WSIs)-based deep learning tasks, such as creating tissue segmentation masks or detecting mitotic figures, is a laborious process due to the extensive image size and the significant manual work involved in the annotation. This paper focuses on identifying and annotating specific image regions that optimize model training, given a limited annotation budget. While random sampling helps capture data variance by collecting annotation regions throughout the WSI, insufficient data curation may result in an inadequate representation of minority classes. Recent studies proposed diversity sampling to select a set of regions that maximally represent unique characteristics of the WSIs. This is done by pretraining on unlabeled data through self-supervised learning and then clustering all regions in the latent space. However, establishing the optimal number of clusters can be difficult and not all clusters are task-relevant. This paper presents prototype sampling, a new method for annotation region selection. It discovers regions exhibiting typical characteristics of each task-specific class. The process entails recognizing class prototypes from extensive histopathology image-caption databases and detecting unlabeled image regions that resemble these prototypes. Our results show that prototype sampling is more effective than random and diversity sampling in identifying annotation regions with valuable training information, resulting in improved model performance in semantic segmentation and mitotic figure detection tasks. Code is available at [https://github.com/DeepMicroscopy/](https://github.com/DeepMicroscopy/Prototype-sampling) [Prototype-sampling.](https://github.com/DeepMicroscopy/Prototype-sampling)

**Keywords:** Whole slide images · histopathology · annotation region selection  $\cdot$  low-data learning  $\cdot$  image caption

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_20) 20.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 207–217, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_20)\_20

## 1 Introduction

Deep learning models for whole slide image (WSI) semantic segmentation and mitotic figure detection can assist in tumor grading by identifying possible malignant areas  $[1,4,14,26]$  $[1,4,14,26]$  $[1,4,14,26]$  $[1,4,14,26]$  $[1,4,14,26]$  $[1,4,14,26]$ . High-quality annotations are crucial to properly train these models, but the acquisition process of these annotations requires pathologists as annotators and is extremely time-consuming due to the large image size in the gigapixel range and the substantial manual effort involved in the annotation. Segmentation masks are generated at the pixel level, and careful examination of thousands of cells is necessary to exhaustively identify mitotic figures (cells undergoing division). This work focuses on discovering specific annotation regions that optimize model training, while leaving the rest of the image unlabeled. This can help reduce the need for manual labeling or accommodate a limited availability of expert annotators. Decreasing the annotation area has been shown to efficiently streamline the process of producing segmentation masks for WSIs [\[13,](#page-129-2)[15,](#page-129-3)[22](#page-130-1)[,27\]](#page-130-2); but, to our knowledge, it has not been used for acquiring mitotic figure annotations. Here, annotators are frequently asked to exhaustively annotate manually selected regions of interest [\[3](#page-128-1)], which could be considerably simplified if cell examination was required only for a fraction of these regions that are identified as informative.

Given a pool of unlabeled WSIs and a certain annotation budget, the task is to find annotation regions that contain useful information for training the model in the downstream task. One natural idea is random sampling, which ensures data diversity by including tissue from different parts of the WSI. However, it may raise issues such as the selected regions inadequately representing minority classes. Some works involve manually selecting annotation regions [\[13](#page-129-2)], which necessitates expertise in estimating which information is most useful for model training. However, humans may not fully recognize the varying degrees of visual variety presented within each class [\[25\]](#page-130-3). In recent years, self-supervised learning (SSL) techniques have popularized diversity sampling, which identifies a set of regions that represent distinct features of the dataset with minimum duplication. Specifically, the pretrained model creates embeddings for all regions, the embeddings are clustered and annotation regions are selected at the center of each cluster  $[10,21]$  $[10,21]$  $[10,21]$  or to maximally cover each cluster  $[12,28]$  $[12,28]$  $[12,28]$ . However, the optimal number of clusters is often not clear, and some clusters may feature irrelevant information for training the downstream task model, such as artifacts.

In this work, we introduce a novel method called *prototype sampling* for the specified task. It identifies annotation regions that exhibit typical characteristics of each task-specific class without the need for clustering. Specifically, our method involves collecting prototype embeddings for each class from extensive histopathology image-caption databases, and identifying unlabeled regions that closely resemble these prototypes. Two databases are used in the study: *ARCH*, composed of figures and captions from PubMed articles and textbooks [\[9](#page-129-6)], and *OpenPath*, encompassing Tweets where pathologists discussed cases [\[11\]](#page-129-7). These resources significantly decrease the time and challenge of searching for class prototypes. They offer a valuable and diverse collection of images showcasing both

typical and ambiguous aspects of many diseases and include a wide range of data variations, including staining material and case origin. Similar to diversity sampling, our approach uses a pretrained model to extract feature embeddings for the following sampling process. However, we assign unlabeled regions to taskspecific classes based on detected prototypes instead of relying on clustering results. One use case of our method is the construction of the initial labeled set of region-based active learning (AL) [\[19\]](#page-129-8), which iteratively trains the model on existing labeled regions and uses the trained model to determine which regions to annotate next. An initial labeled set that efficiently trains the first AL model can more reliably identify informative annotation regions in the subsequent AL cycle. Another application is to create a preliminary model by annotating a small portion of the data, and then use the model to automate further annotations or propose reference annotations [\[5](#page-129-9)]. We test our method on semantic segmentation and mitotic figure detection tasks by training the model using the annotation regions selected through random, diversity, and prototype sampling.

# 2 Method

## 2.1 General Setup

Given a pool of *N* unlabeled WSIs and an annotation budget of *n* regions of size  $l \times l$  per WSI, the objective is to identify annotation regions containing useful training information for the downstream task. The annotation regions are detected based on a specified sampling method. In this section, we outline random sampling and diversity sampling, which serve as benchmark methods for our experiments. The random sampling method selects *n* regions from each WSI at random locations; regions with less than 10% tissue area are excluded by tissue detection [\[20](#page-129-10)]. We mainly followed [\[10](#page-129-4)] for diversity sampling. First, each WSI is partitioned into a grid of regions of size  $l \times l$ . For each region we calculate the feature embedding using a pretrained model. We then aggregate the embeddings of all regions from *<sup>N</sup>* WSIs and allocate them to *<sup>N</sup>* <sup>∗</sup>*<sup>n</sup>* clusters with K-means clustering, in order to prevent the selection of similar images regions from separate WSIs. Finally the regions centered at each cluster are selected. Next we describe prototype sampling in detail.

## 2.2 Prototype Sampling

Prototype sampling has two main components: 1) the acquisition of prototypes of each task-specific class from image-caption databases by keyword search or textto-image retrieval, and 2) the construction of a similarity map that shows the similarity of different image areas to the prototypes. The similarity map is used for the identification of annotation regions. An illustration is shown in Fig. [1.](#page-123-0)

**Class Prototypes Identification.** We obtain typical images of a specific class from histopathology image-caption databases by *keyword search* or *text-to-image retrieval*. *Keyword search* exploits the detailed information contained in cap-tions and is straightforward to conduct (Fig. [1](#page-123-0) (1a)). Specifically, we create a

Image /page/123/Figure/1 description: This figure illustrates a two-stage process for class prototype identification and similarity map construction. The first stage, 'Class prototypes identification,' has two sub-sections: (1a) Keyword search, which shows a list of text descriptions and corresponding recognized images, and (1b) Text-to-image retrieval, which takes a text prompt, processes it through a text encoder to get a text feature vector 'ft', and then calculates similarity scores between 'ft' and image features 'f1' through 'f6' from a database. The second stage, 'Similarity map construction,' takes an input image 'x' and processes it through an image encoder to obtain feature maps 'Fx'. It then compares these feature maps with a class prototype 'F(c)prototype' to generate a similarity map 'Mi(c)'.

<span id="page-123-0"></span>**Fig. 1.** Workflow of prototype sampling.

set of "with" and "without" keywords, and recognize images with captions that include all "with" keywords and none of the "without" keywords. The "with" keywords are created using class names with several synonyms (e.g., "cancer" and "tumor"), under the assumption that data analysis experts lack medical expertise. The "without" keywords are used to exclude irrelevant images, such as those of type photomicrograph. The recognized images are encoded into embeddings  $\mathcal{F}^{(c)}$  $F^{(c)}_{prototype}$  using a pre-trained image encoder, with *c* representing the class. *Text-to-image retrieval* employs text and image encoders created through contrastive language-image pretraining (CLIP) [\[23\]](#page-130-6) to align visual concepts from images with the perception contained in the text  $(Fig. 1 (1b))$  $(Fig. 1 (1b))$  $(Fig. 1 (1b))$ . We use a text prompt "An *<sup>H</sup>* &*<sup>E</sup>* image of {*class name*} tissue." and obtain its embedding from the text encoder. All images in the database are encoded using the image encoder to create  $\mathcal{F}_{database}$ .  $N_{prototype}$  database image embeddings that have the highest cosine similarity to the text prompt embedding are selected to form  $\boldsymbol{\mathcal{F}}^{(c)}$ *prototype*.

**Similarity Map Construction and Annotation Region Selection.** We now identify image regions from the WSI that are representative of the specified class. We partition the WSI  $X_i \in \mathbb{R}^{3 \times H_i \times W_i}$  into patches with a stride of *s* and then calculate their embeddings individually using a pretrained model. We denote a patch as  $x \in \mathbb{R}^{3 \times d \times d}$  and its embedding as  $f_x \in \mathbb{R}^{C_f}$ , where  $C_f$  denotes the embedding dimension. The size of the patches *d* and the level of magnification used to extract the patches from the WSI pyramid are chosen according to the task. For instance, a small patch extracted at high magnification is preferable for comparing its resemblance to prototype images of mitotic figures, whereas patches characterizing a gland should be sufficiently large to encompass all tissue components. Note that each annotation region of size *l* may contain multiple patches of size *d*. The representativeness of patch *x* for class *c* is defined as the highest cosine similarity between  $f_x$  and the embeddings in  $\mathcal{F}_{protocolype}^{(c)}$ , calculated as  $\text{sim}(f_x, \mathcal{F}_{\text{prototype}}^{(c)}) = \max_{f_p \in \mathcal{F}_{\text{prototype}}^{(c)}} \text{sim}(f_x, f_p)$ . Patch similarities are then combined into a similarity map  $M_i \in [0,1]^{\frac{H_i}{s} \times \frac{W_i}{s}}$  for the WSI. The map is used to select annotation regions using a standard or an adaptive region selection method, denoted as "prototype (standard)" and "prototype (adaptive)" accordingly. The standard method [\[19\]](#page-129-8) moves a sliding window of the annotation region size (i.e.,  $\frac{l}{s} \times \frac{l}{s}$  (note that *l* is defined at the original resolution)) across  $M_i$  with a stride of 1 pixel, evaluates the similarity of each region by summing up the similarities of patches within it, and then use non-maximum suppression to identify non-overlapping regions with the highest similarity values. The adaptive method [\[22\]](#page-130-1) allows for selecting regions that can adjust in shape and size to accommodate variations in histopathological structures. The process includes the following steps: 1) Locate the pixel with the highest similarity  $(u, v)$ ; 2) Create a binary mask by using a similarity threshold; 3) Identify the connected component that includes  $(u, v)$  and choose its bounding box; and 4) Determine the similarity threshold through bisection search to ensure that the bounding box falls within the range  $\left[\frac{1}{2}l \times \frac{1}{2}l, \frac{3}{2}l \times \frac{3}{2}l\right]$ .

# 3 Experiments

## 3.1 Image-Caption Pair Databases

Two histopathology image-caption databases are used in the study. The ARCH database [\[9\]](#page-129-6) contains 8*,* 617 histology or immunohistochemistry image-caption pairs extracted from PubMed medical articles and 3*,* 199 pairs from 10 textbooks. The captions are manually curated to only include text related to diagnostic and morphological descriptions. The OpenPath database [\[11](#page-129-7)] consists of 116*,* 504 image-caption pairs from Twitter posts and 59*,* 869 pairs from replies, gathered from 32 pathology subspecialty-specific hashtags. The database does not contain the extracted images and captions but only the links to the original posts. Image and caption embeddings obtained from PLIP [\[11](#page-129-7)], which is trained by fine-tuning a pre-trained CLIP model [\[23\]](#page-130-6) on OpenPath, are included in the database.

## 3.2 Semantic Segmentation

**Dataset.** The public dataset CAMELYON16 [\[17\]](#page-129-11) consists of 399 WSIs of Hematoxylin & Eosin (H&E)-stained excised regional sentinel lymph nodes. The task is the detection of the presence and extent of breast cancer metastases. Each WSI with metastases is accompanied by a segmentation mask that outlines all metastases at the pixel level. We followed data usages in [\[22\]](#page-130-1) for fair comparisons. **Training Setups.** We followed [\[22](#page-130-1)] using a segmentation framework based on patch classification, including all training setups, except for replacing the MobileNet [\[24](#page-130-7)] encoder with the two following encoders: 1) *ResNet18 SSL* [\[7\]](#page-129-12): A ResNet18 that is self-supervised pretrained on 0*.*4 million histological patches with SimCLR [\[6](#page-129-13)]; 2) *ViT\_PLIP*: The image encoder (ViT-32-Base) of PLIP. Training and inference patches were extracted at the resolution of  $0.25 \frac{\mu m}{px}$  with  $256 \times 256$  pixels and  $224 \times 224$  pixels for the two encoders, respectively.

**Creating Class Prototypes.** We used the ARCH and OpenPath databases for obtaining class prototypes for the experiments with the two encoders. From ARCH, we extracted breast cancer images by keyword search. We used {breast, abnormal (tumor, cancer, carcinoma, metastases, metastasis, metastic)} and {IHC (immunohistochemical, immunohistochemistry, immunostain), photomicrograph (photomicrography)} as the "with" and "without" keywords, respectively, where synonyms are given in the parentheses. In total 21 images were found. Details on the pre- and post-processing steps can be found in Table S1. These images were processed by the *ResNet SSL* encoder to generate image embeddings. Prior to this, the images were center-cropped to the size of model input to maintain the original magnification and prevent distortion of the structures. From the OpenPath database, where the image embeddings are provided, we selected the top  $N_{prototype} = 100$  image embeddings under the hashtag "#BreastPath" that best align with the embedding of the context prompt of "An  $H \& E$  image of breast tumor tissue." generated by the PILP text encoder. **WSI Similarity Map Calculation.** Patches of size  $256 \times 256$  pixels were extracted at resolution  $0.25 \frac{\mu m}{p x}$  with a stride of 256 pixels to ensure the incorporation of details required for metastases identification.

**Comparison Methods.** We compared to random sampling and diversity sampling. For fair comparisons, the two encoders were used to obtain region features in diversity sampling instead of pretraining a new model on the unlabeled data. Prior to this, the regions were downsampled to match the model input sizes.

**Evaluation Scenarios and Metrics.** Recent works have found that the efficiency of a sampling strategy may differ depending on the number and size of the annotation regions  $[18,19,22]$  $[18,19,22]$  $[18,19,22]$  $[18,19,22]$ , we follow  $[22]$  to evaluate on nine hyperparameter settings by combining  $n \in \{1, 3, 5\}$  and  $l \in \{4096, 8192, 12288\}$  pixels. We denote these three sizes as S (small), M (medium) and L (large), respectively. Slide-averaged intersection over union across test slides containing tumor (mIoU (tumor)) was used as the evaluation metric, following [\[22\]](#page-130-1).

## 3.3 Mitotic Figure Detection

**Dataset.** The MITOS\_WSI\_CMC [\[2](#page-128-2)] dataset consists of 21 slides of canine mammary carcinoma that are fully annotated with 13*,* 907 mitotic figures. The task is to identify all mitotic figures in the WSIs, as mitotic count is a key criteria for tumor grading [\[8](#page-129-15)]. We followed data usages in [\[2\]](#page-128-2) for fair comparison.

**Training Setups.** We followed [\[2\]](#page-128-2) using a RetinaNet [\[16\]](#page-129-16) (ResNet18 backbone pretrained on ImageNet) as the mitotic figure detection model and the same training schemes, the only modification was sampling 1*,* 000 training patches per epoch instead of 5*,* 000 when the annotated area was *<*5% to avoid overfitting.

**Creating Class Prototypes.** We used ARCH to extract prototypes of mitotic figures by keyword search. The "with" keyword set included {arrow (arrowhead, circle), mitotic (mitoses)}, as mitotic figures are often highlighted with arrows or circles (see examples in Fig. [3](#page-127-0) (a-b)). In total 19 images were identified, from each a section including the mitotic figure was manually cropped. We resized all sections to  $64 \times 64$  pixels and calculated features using the ResNet18 backbone. **WSI Similarity Map Calculation.** Patches of size  $64 \times 64$  pixels  $(0.25 \frac{\mu m}{px})$ , a stride of 64 pixels) were extracted to include only one or a few cells per patch. **Comparison Methods.** The same settings in the CAMELYON16 experiments were used, using the ResNet18 backbone to extract region features.

**Evaluation Scenarios and Metrics.** The same settings in the CAMELYON16 experiments were used. The three region sizes  $l \in \{4096, 8192, 12288\}$  pixels correspond to annotation areas of  $\{1.05, 4.19, 9.44\}$  mm<sup>2</sup>, enclosing the recommended area for performing mitotic count (10 high power field (HPF), 2*.*37 mm<sup>2</sup>). The F1 score was used as the evaluation metric, following [\[2](#page-128-2)].

### 3.4 Results

Image /page/126/Figure/4 description: This image contains three scatter plots arranged in a 2x3 grid. The top row displays plots (a), (b), and (c), each with 'annotated area (%)' on the x-axis and a performance metric on the y-axis. Plot (a) is titled 'CAMELYON16 (ResNet18\_SSL)' and plots mIoU (Tumor) against annotated area. Plot (b) is titled 'CAMELYON16 (ViT\_PLIP)' and also plots mIoU (Tumor) against annotated area. Plot (c) is titled 'MITOS\_WSI\_CMC (ResNet18)' and plots F1 against annotated area. Each plot shows data points for 'random' (blue circles), 'prototype (adaptive)' or 'prototype (standard)' (red circles), and 'diversity' (green crosses). A dashed line labeled 'full annotation' is present in all three plots. The bottom row shows three corresponding scatter plots, with 'annotated area (%)' on the x-axis and a different metric on the y-axis. The first plot in the bottom row shows 'annotated tumor area (%)' for CAMELYON16 (ResNet18\_SSL). The second plot in the bottom row shows 'annotated tumor area (%)' for CAMELYON16 (ViT\_PLIP). The third plot in the bottom row shows 'annotated mitotic figures (%)' for MITOS\_WSI\_CMC (ResNet18). These plots also use blue circles for 'random', red circles for 'prototype (adaptive)' or 'prototype (standard)', and green crosses for 'diversity'.

<span id="page-126-0"></span>**Fig. 2.** (a–b) Results on CAMELYON16 dataset: mIoU (Tumor) and annotated tumor area  $(\%)$  as functions of annotated tissue area  $(\%)$ . (c) Results on MITOS\_WSI\_CMC dataset: F1 and the ratio of annotated mitotic figures as functions of annotated tissue area (%). Prototype (adapt) can have different amounts of annotated area as the size of each selected region is dynamically determined. All other methods select regions of size  $l \times l$ . Random sampling can lead to a slightly smaller annotated area when no more non-overlapping region containing at least 10% tissue is found. All results show median values from five repetitions.

Image /page/127/Figure/1 description: The image displays four panels labeled (a), (b), (c), and (d). Panel (a) shows a micrograph of seminoma, classic type, with cells divided into packets by delicate fibrovascular septae. Lymphocytes are visible along the septae, and the nuclei are widely spaced with clear cytoplasm. Mitoses are indicated by a circle, and nuclei with distinct nuclear membranes and prominent nucleoli are highlighted by an arrowhead in an inset. Panel (b) presents a micrograph of sarcomatoid carcinoma, characterized by sheets of spindled cells with large nuclei and prominent nucleoli. A mitosis is indicated by an arrow. Panel (c) is a whole-slide image with blue squares highlighting specific regions of interest. Some areas are stained pink, while others are stained green. Panel (d) is a heatmap corresponding to panel (c), with a color bar on the right indicating values from 0.0 to 0.8. The heatmap shows varying intensity levels of green, yellow, and red across the tissue, with the blue squares from panel (c) overlaid.

<span id="page-127-0"></span>**Fig. 3.**  $(a-b)$  Two example prototype images of mitotic figure.  $(c-d)$  An example WSI and its similarity map. Ground truth mitotic figures marked green in (c) and red in (d). The blue boxes in (c) and (d) indicate regions selected with random and prototype sampling, respectively (hyperparameter: 3 M). (Color figure online)

Figure [2](#page-126-0) shows the results across all methods. Given a certain budget for annotation area, prototype sampling is more effective than random and diversity sampling in creating a labeled set that maximizes model outcome on both tasks of breast cancer metastases segmentation and mitotic figure detection. This could be attributed to the increased ratios of tumor area and mitotic figures identified and annotated. Notably, prototype sampling achieves near full annotation performance in CAMELYON16 (ViT PLIP) and MITOS WSI CMS (ResNet18) experiments by annotating less than 20% of the tissue area, without the iterative process of annotation region identification in the conventional AL procedures, leading to substantially reduced computational costs. Specifically, on the CAME-LYON16 dataset, prototype sampling shows a smaller performance benefit over the other two methods when selecting a large number of regions per WSI, (e.g., in the cases of  $3L$  and  $5M$ ). This might be because multiple similar regions all possessing tumor characteristics are selected from the same WSI, leading to low data diversity. A comparison between the adaptive and standard region selection methods in Fig. S1 provides further evidence to support this hypothesis: Annotation regions selected using "prototype (standard)" contain a larger amount of tumor area than the annotation regions selected by "prototype (adapt)", but the trained model shows inferior performance in segmenting tumors on the test set. The standard method can identify a larger area of tumor for annotation as it selects regions that have the highest number of patches resembling tumor, while the adaptive method selects annotation regions centered at the patches that are most similar to tumor prototypes. On the MITOS WSI CMC dataset, prototype sampling identifies annotation regions containing more mitotic figures than the other two methods, leading to a consistent increase in model performance. Here, "prototype (standard)" also outperforms "prototype (adapt)" as regions with a larger number of patches resembling mitotic figure are identified for annotation (Fig. S1). Figure [3](#page-127-0) (d) provides an example of similarity map, where areas with patches that closely resemble mitotic figure prototypes align well with the areas containing ground truth mitotic figure annotations.

## 4 Discussion and Conclusion

We presented a novel method for selecting valuable annotation regions that allows to effectively train the model without annotating the entire WSIs. These regions are detected as possessing typical characteristics of each task-specific class based on their strong resemblance to prototypes recognized from imagecaption databases by keyword search or text-to-image retrieval. The efficacy of our method has been proven in different tasks and evaluation settings.

Experiments on the breast cancer metastases segmentation task using both ARCH and OpenPath databases allow evaluating the impact of image-caption database quality on method robustness. While ARCH is well-curated with manually selected samples and cleaned captions, OpenPath is only subjected to image quality sampling testing and automatic text cleaning. We expected that prototypes obtained from OpenPath may have more noise, but we see no drawback of using OpenPath in our experiments. This evaluation was prioritized over an examination involving artificially introduced noise patterns (e.g., swapping captions of two randomly selected pairs), as it depicts "real-world" database quality fluctuations. Future work will investigate the impact of prototype set size.

Particularly, our method can aid the annotation of minority classes. While these classes may also have limited presence in image-caption databases, our method help identify rare but existing samples or discover prototypes of related diseases using prior knowledge. Concerns about finding prototypes for rare diseases are further alleviated by ongoing efforts in creating larger and more diverse histopathology image-caption databases to build powerful all-purpose models.

**Acknowledgments.** We acknowledge support by d.hip campus - Bavarian aim (J.Q. and K.B.), the German Research Foundation (DFG) project 460333672 CRC1540 EBM, project 405969122 FOR2886 Pandora, as well as the scientific support and HPC resources provided by the Erlangen National High Performance Computing Center (NHR@FAU) of the Friedrich-Alexander-Universität Erlangen-Nürnberg (FAU). The hardware is funded by DFG.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-128-0"></span>1. Aubreville, M., Bertram, C., Marzahl, C., Gurtner, C., Dettwiler, M., Schmidt, A., et al.: Deep learning algorithms out-perform veterinary pathologists in detecting the mitotically most active tumor region. Scientific Reports **10**(1), 16447 (2020)
- <span id="page-128-2"></span>2. Aubreville, M., Bertram, C.A., Donovan, T.A., Marzahl, C., Maier, A., Klopfleisch, R.: A completely annotated whole slide image dataset of canine breast cancer to aid human breast cancer research. Scientific data **7**(1), 417 (2020)
- <span id="page-128-1"></span>3. Aubreville, M., Stathonikos, N., Bertram, C.A., Klopfleisch, R., Ter Hoeve, N., Ciompi, F., Wilm, F., Marzahl, C., Donovan, T.A., Maier, A., et al.: Mitosis domain generalization in histopathology images-the MIDOG challenge. Medical Image Analysis **84**, 102699 (2023)

- <span id="page-129-0"></span>4. Bejnordi, B.E., Veta, M., Van Diest, P.J., Van Ginneken, B., Karssemeijer, N., Litjens, G., Van Der Laak, J.A., Hermsen, M., Manson, Q.F., Balkenhol, M., et al.: Diagnostic assessment of deep learning algorithms for detection of lymph node metastases in women with breast cancer. Jama **318**(22), 2199–2210 (2017)
- <span id="page-129-9"></span>5. Bertram, C.A., Aubreville, M., Marzahl, C., Maier, A., Klopfleisch, R.: A largescale dataset for mitotic figure assessment on whole slide images of canine cutaneous mast cell tumor. Scientific data **6**(1), 274 (2019)
- <span id="page-129-13"></span>6. Chen, T., Kornblith, S., Norouzi, M., Hinton, G.: A simple framework for contrastive learning of visual representations. In: International Conference on Machine Learning. pp. 1597–1607. PMLR (2020)
- <span id="page-129-12"></span>7. Ciga, O., Xu, T., Martel, A.L.: Self supervised contrastive learning for digital histopathology. Machine Learning with Applications **7**, 100198 (2022)
- <span id="page-129-15"></span>8. CW, E.: The value of histological grade in breast cancer: experience from a large study with long-term follow-up. Histopathology **19**, 403–410 (1991)
- <span id="page-129-6"></span>9. Gamper, J., Rajpoot, N.: Multiple instance captioning: Learning representations from histopathology textbooks and articles. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 16549–16559 (2021)
- <span id="page-129-4"></span>10. Hacohen, G., Dekel, A., Weinshall, D.: Active learning on a budget: Opposite strategies suit high and low budgets. In: International Conference on Machine Learning. pp. 8175–8195. PMLR (2022)
- <span id="page-129-7"></span>11. Huang, Z., Bianchi, F., Yuksekgonul, M., Montine, T., Zou, J.: Leveraging medical twitter to build a visual–language foundation model for pathology ai. bioRxiv pp. 2023–03 (2023)
- <span id="page-129-5"></span>12. Jin, Q., Yuan, M., Qiao, Q., Song, Z.: One-shot active learning for image segmentation via contrastive learning and diversity-based sampling. Knowledge-Based Systems **241**, 108278 (2022)
- <span id="page-129-2"></span>13. Jin, X., An, H., Wang, J., Wen, K., Wu, Z.: Reducing the annotation cost of whole slide histology images using active learning. In: 2021 3rd International Conference on Image Processing and Machine Vision (IPMV). pp. 47–52 (2021)
- <span id="page-129-1"></span>14. Kim, Y.J., Jang, H., Lee, K., Park, S., Min, S.G., Hong, C., Park, J.H., Lee, K., Kim, J., Hong, W., et al.: PAIP 2019: Liver cancer segmentation challenge. Medical image analysis **67**, 101854 (2021)
- <span id="page-129-3"></span>15. Lai, Z., Wang, C., Oliveira, L.C., Dugger, B.N., Cheung, S.C., Chuah, C.N.: Joint semi-supervised and active learning for segmentation of gigapixel pathology images with cost-effective labeling. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 591–600 (2021)
- <span id="page-129-16"></span>16. Lin, T.Y., Goyal, P., Girshick, R., He, K., Dollár, P.: Focal loss for dense object detection. In: Proceedings of the IEEE International Conference on Computer Vision. pp. 2980–2988 (2017)
- <span id="page-129-11"></span>17. Litjens, G., Bandi, P., Ehteshami Bejnordi, B., Geessink, O., Balkenhol, M., Bult, P., et al.: 1399 H&E-stained sentinel lymph node sections of breast cancer patients: the CAMELYON dataset. GigaScience **7**(6), giy065 (2018), data downloaded from the GigaScience database [http://gigadb.org/dataset/100439.](http://gigadb.org/dataset/100439)
- <span id="page-129-14"></span>18. L¨uth, C.T., Bungert, T.J., Klein, L., Jaeger, P.F.: Navigating the pitfalls of active learning evaluation: A systematic framework for meaningful performance assessment. In: 37th Conference on Neural Information Processing Systems (2023)
- <span id="page-129-8"></span>19. Mackowiak, R., Lenz, P., Ghori, O., et al.: Cereals-cost-effective region-based active learning for semantic segmentation. arXiv preprint [arXiv:1810.09726](http://arxiv.org/abs/1810.09726) (2018)
- <span id="page-129-10"></span>20. Otsu, N.: A threshold selection method from gray-level histograms. IEEE Transactions on Systems, Man, and Cybernetics **9**(1), 62–66 (1979)

- <span id="page-130-4"></span>21. Pourahmadi, K., Nooralinejad, P., Pirsiavash, H.: A simple baseline for low-budget active learning. arXiv preprint [arXiv:2110.12033](http://arxiv.org/abs/2110.12033) (2021)
- <span id="page-130-1"></span>22. Qiu, J., Wilm, F., Ottl, M., Schlereth, M., Liu, C., Heimann, T., Aubreville, M., ¨ Breininger, K.: Adaptive region selection for active learning in whole slide image semantic segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 90–100. Springer (2023)
- <span id="page-130-6"></span>23. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International Conference on Machine Learning. pp. 8748–8763. PMLR (2021)
- <span id="page-130-7"></span>24. Sandler, M., Howard, A., Zhu, M., Zhmoginov, A., Chen, L.C.: MobileNetV2: Inverted residuals and linear bottlenecks. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. pp. 4510–4520 (2018)
- <span id="page-130-3"></span>25. Shin, G., Xie, W., Albanie, S.: All you need are a few pixels: semantic segmentation with PixelPick. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 1687–1697 (2021)
- <span id="page-130-0"></span>26. Veta, M., Heng, Y.J., Stathonikos, N., Bejnordi, B.E., Beca, F., Wollmann, T., Rohr, K., Shah, M.A., Wang, D., Rousson, M., et al.: Predicting breast tumor proliferation from whole-slide images: the TUPAC16 challenge. Medical image analysis **54**, 111–121 (2019)
- <span id="page-130-2"></span>27. Yang, L., Zhang, Y., Chen, J., Zhang, S., Chen, D.Z.: Suggestive annotation: A deep active learning framework for biomedical image segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 399–407. Springer (2017)
- <span id="page-130-5"></span>28. Zheng, H., Yang, L., Chen, J., Han, J., Zhang, Y., Liang, P., Zhao, Z., Wang, C., Chen, D.Z.: Biomedical image segmentation via representative annotation. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 33, pp. 5901– 5908 (2019)

Image /page/131/Picture/0 description: A square button with a light gray background has a circular icon at the top and the text "Check for updates" below it. The icon is a gray circle with a darker gray bookmark shape inside. The bookmark is tilted to the left and has a curved edge on the right side.

# MEDBind: Unifying Language and Multimodal Medical Data Embeddings

Yuan Gao<sup>1,3,4,7</sup>, Sangwook Kim<sup>1,3,7</sup>, David E. Austin<sup>1,7</sup>, and Chris McIntosh<sup>1,2,3,4,5,6,7( $\boxtimes$ )</sup>

<sup>1</sup> Peter Munk Cardiac Centre, University Health Network (UHN), Toronto, Canada

*{*yuan.gao,sangwook.kim,chris.mcintosh*}*@uhn.ca <sup>2</sup> Department of Computer Science, University of Toronto (UofT), Toronto, Canada <sup>3</sup> Department of Medical Biophysics, UofT, Toronto, Canada

<sup>4</sup> Ted Rogers Centre for Heart Research, UHN, Toronto, Canada

<sup>5</sup> Toronto General Hospital Research Institute, UHN, Toronto, Canada

<sup>6</sup> Department of Medical Imaging, UofT, Toronto, Canada <sup>7</sup> Vector Institute, Toronto, Canada

**Abstract.** Medical vision-language pretraining models (VLPM) have achieved remarkable progress in fusing chest X-rays (CXR) with clinical texts, introducing image-text data binding approaches that enable zeroshot learning and downstream clinical tasks. However, the current landscape lacks the holistic integration of additional medical modalities, such as electrocardiograms (ECG). We present MEDBind (**M**edical **E**lectronic patient recor**D** Bind), which learns joint embeddings across CXR, ECG, and text. Using text data as the central anchor, MEDBind features trimodality binding, delivering competitive performance in top-K retrieval, zero-shot, and few-shot benchmarks against established VLPM, and the ability for CXR-to-ECG zero-shot classification and retrieval. This seamless integration is achieved by combining contrastive loss on modalitytext pairs with our proposed contrastive loss function, Edge-Modality Contrastive Loss, fostering a cohesive embedding space for CXR, ECG, and text. Finally, we demonstrate that MEDBind can improve downstream tasks by directly integrating CXR and ECG embeddings into a large-language model for multimodal prompt tuning.

**Keywords:** Vision-Language Pretraining · Contrastive Learning · Multimodal Deep Learning · Self-Supervised Learning

Y. Gao and S. Kim—Equal contribution.

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_21) 21.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 218–228, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_21)\_21

## 1 Introduction

Vision-language pre-training models (VLPM) have advanced the integration of medical texts with imaging data, facilitating the convergence of diverse modalities into a unified representation space. This fusion deepens the understanding of text-image relationships and enhances their zero-shot learning capabilities.

VLPMs have revolutionized the interpretation of chest X-ray (CXR) by effectively aligning CXR with connected radiological reports through self-supervised contrastive learning. GloRIA [\[9](#page-140-0)] and BioVIL [\[2\]](#page-139-0) showcased the potential to discern local and global visual features in CXR through textual analysis. Further, MedCLIP [\[29](#page-141-0)] and CXR-CLIP [\[30\]](#page-141-1) elevated training efficacy by improving imagetext specific loss functions. However, the scope of multimodal pre-training within the medical domain has been predominantly limited to image-text pairs, overlooking the potential integration of other clinical data types.

Incorporating more modalities from different domains is emerging as a critical research frontier. ImageBind [\[5](#page-139-1)] represents a significant stride in this direction by extending the VLPM contrastive learning approaches to accommodate more than two modalities within a unified embedding space, using images as the focal modality. ImageBind also broadened previous multimodal representations to additional tasks, including multimodal information retrieval and crossmodality zero-shot classification. Similarly, *all in one* [\[31](#page-141-2)] achieved alignment by integrating video and text into a transformer for joint feature extraction across different modalities. Med-PaLM M [\[26\]](#page-141-3) recently advanced medical multimodal models by instruction prompt tuning PaLM-E, a large language model (LLM). Unlike contrastive learning approaches, Med-PaLM M incorporated multimodal data with text without explicit binding via LLM prompt tuning.

However, self-supervised contrastive learning in binding more than two medical modalities has yet to be explored. We present MEDBind (**M**edical **E**lectronic patient recor**D** Bind), a contrastive learning model that explicitly binds CXR, electrocardiograms (ECG), and medical texts into a unified embedding space. We chose text as the central anchor for binding CXR and ECG since many medical modalities are interpreted and given clinical narrative summaries.

**Contributions:** MEDBind is the first tri-modality framework that employs contrastive learning to fuse CXR, ECG, and medical texts into a unified representation space. We introduce a non-text edge-modality contrastive loss (EMCL), which strengthens the binding of CXR and ECG and is adept at handling varying numbers of cross-modality pairs in datasets. MEDBind pretrained with EMCL improves information retrieval, zero-shot, and few-shot performance. We utilize MEDBind in downstream clinical tasks, where ECG and CXR embeddings are integrated with LLM to predict readmission and in-hospital mortality.

## 2 Methods and Materials

## 2.1 Model Architecture

MEDBind is designed to process and analyze data from three distinct modalities: CXR, ECG, and medical text. Inspired by ImageBind [\[5\]](#page-139-1), our model employs

Image /page/133/Figure/1 description: This diagram illustrates a multimodal learning framework for medical data. It shows three input data types: n size CXR samples, m size samples with both CXR and ECG, and n size ECG samples. These inputs are processed by CXR Encoder, TXT Encoder, and ECG Encoder, respectively. The TXT Encoder takes text descriptions like 'CXR shows cardiomegaly' and 'Hypertrophy in ECG'. The outputs of these encoders are represented as feature vectors (z) and text embeddings (t). The framework includes two loss functions, LTMCL and LEMCL, which are calculated based on similarity matrices between these embeddings, suggesting a cross-modal learning approach.

<span id="page-133-0"></span>**Fig. 1.** Proposed method. Batch size *n*: CXR (green), ECG (purple), and paired text (blue). Subset size m: paired ECG/CXR. Inputs are embedded and normalized  $(\blacktriangleright)$ . We used two losses: 1) Text-Modality Contrastive Loss (TMCL); 2) Edge-Modality Contrastive Loss (EMCL). Grey is positive-pair; light grey is additional related pairs. (Color figure online)

dedicated encoders for each modality to extract representations (Fig. [1\)](#page-133-0). **Modality Encoder:** For the **CXR encoder**, we used Swin Transformer [\[19](#page-140-1)] as our backbone following [\[29](#page-141-0)[,30](#page-141-1)]. For the **ECG encoder**, we employed a vanilla transformer backbone [\[27\]](#page-141-4). We loaded ECG into a transformer by converting the time-series data into sequences of embeddings where each time point is tokenized using a linear embedding. We utilized BioBERT  $[17]$ , a BERT  $[14]$  $[14]$  variant finetuned on medical texts to capture biomedical semantics for the **Text encoder**. We opted not to apply BioClinicalBert [\[1\]](#page-139-2) for the text encoder to preserve the integrity of our training datasets for downstream tasks and to avoid potential bias since BioClinicalBert was fine-tuned on MIMIC-III. Finally, we used class token embeddings for all modality encoders because they are a critical component in transformer-based models that aggregate the global context of the input. **Projection/Normalization**: We projected and normalized CXR, ECG, and text embeddings to 256 dimensions using modality-specific linear layer and L2 normalization. This ensures final embeddings are comparable across encoders.

## 2.2 Loss Function

We trained MEDBind using Text-Modality Contrastive Loss (TMCL) for textmodality binding and Edge-Modality Contrastive Loss (EMCL), a novel loss function we propose for improving cross-modality binding.

CLIP [\[24\]](#page-141-5) showed that noise-contrastive estimation information (infoNCE) loss can bind image-text pairs, where a single positive-paired text is attracted for each image while the remaining texts are repelled. However, infoNCE loss does not account for cases where two patients have the same clinical text, incorrectly repelling their associated images. We implemented **TMCL**, similar to infoNCE, to link text with other modalities, but we considered identical paired texts as additional positive pairs. This is highlighted in the TMCL matrix of Fig. [1,](#page-133-0) where light grey pairs are additional related pairs with the same clinical text (for example, we encourage CXR with the same report to bind together).

Adopting Supervised Contrastive Learning (SCL) from [\[16](#page-140-4),[21\]](#page-140-5), we applied SCL for VLPM training in TMCL, reflected in Eq. [1,](#page-134-0) where  $z^j$  and  $t^j$  denote embeddings for the non-text modality and text, respectively with  $j \in \{c :$  $CXR$ ,  $e: ECG$ . We denote  $i, l \in n$  as the  $i^{th}, l^{th}$  element in a batch size of  $n$ , yielding:

<span id="page-134-0"></span>
$$
L_{TMCL}^{t^j \to z^j} = -\sum_{i=1}^n \frac{1}{|P(i)|} \sum_{p \in P(i)} \log \frac{\exp(t_i^j \cdot z_p^j / \tau)}{\sum_{l=1}^n \exp(t_i^j \cdot z_l^j / \tau)}
$$
(1)

where  $p \in P(i)$  is the set of all positive pairs for text  $t_i^j$  and modality  $z^j$ . The temperature parameter  $\tau$  modulates the scale of distribution over embeddings. We use a symmetric loss for  $L_{TMCL}$ , so  $L_{TMCL} = \sum_{j \in \{c,e\}} (L_{TMCL}^{t^j \to z^j} + L_{TMCL}^{z^j \to t^j}).$ where  $t_j \to z_j$  denotes text-to-modality, e.g. text-to-ECG, and  $z_j \to t_j$  denotes modality-to-text, thereby enforcing consistency in both modality directions.

We introduce **EMCL**, a novel contrastive loss that refines binding between non-text modalities. Unlike ImageBind [\[5\]](#page-139-1), EMCL explicitly binds CXR to ECG and can dynamically adapt to different CXR-ECG pair counts in a batch. We defined positive pairs of CXR-ECG when a patient's CXR and ECG are taken during the same clinical visit, which pairs non-text modalities at the patient and temporal level. We sub-sampled paired CXR-ECG instances from  $n$  to optimize the usage for all training data. Thus, not all samples have corresponding CXR-ECG pairs (Fig. [1\)](#page-133-0). Note that sub-sampling leads to a varying subset of size  $m$ in each batch. We define EMCL as:

$$
L_{EMCL}^{z^{c} \to z^{c}} = -\sum_{u=1}^{m} \log \frac{\exp(z_{u}^{c} \cdot z_{u}^{e}/\tau)}{\frac{n}{m} \sum_{q=1}^{m} \exp(z_{u}^{c} \cdot z_{q}^{e}/\tau)}
$$
(2)

where  $u, q \in m$  are the  $u^{th}, q^{th}$  element of the batch, and the embeddings of CXR, z*<sup>c</sup>*, and ECG, z*<sup>e</sup>*, from the same patient case, are aligned to a unified embedding space. EMCL stabilizes the fluctuating cardinality of this subset by normalizing the denominator with a factor of  $\frac{n}{m}$  across different batch iterations. Similar to TMCL, we employed symmetric loss on L*EMCL* for bidirectional consistency, where  $L_{EMCL} = L_{EMCL}^{z_c \to z_e} + L_{EMCL}^{z_e \to z_c}$ . Our overall loss is defined as  $L_{TMCL}$  + L*EMCL*, where L*EMCL* equips MEDBind for cross-modality binding.

#### 2.3 ECG-CLIP and Tri-Modality Evaluations

To our knowledge, no VLPM has bound ECG and text, making direct comparisons with existing models challenging. Thus, we devised a novel **ECG-CLIP** as a baseline using our ECG and text encoders, trained with  $L_{TMCL}$  where  $j = e$ .

To assess the impact of tri-modality binding and EMCL, we introduce MED-Bind; **MEDBind**<sub>BD</sub> (bound) with  $L_{TMCL} + L_{EMCL}$  and **MEDBind**<sub>NM</sub> (normal) with only L*TMCL* as an ablation. Moreover, we assessed if separately trained CXR and ECG VLPM could perform similarly to MEDBind, given that all VLPM bind the text modality. For tasks needing CXR and ECG encoders, we assessed various CXR VLPM paired with ECG-CLIP as the ECG encoder. This multiple single-paired VLPM approach is analogous to "**encoder zoo**" in [\[22](#page-140-6)].

Image /page/135/Figure/1 description: The image displays three t-SNE plots, each representing a different model: CXR-CLIP/ECG-CLIP, MEDBindNM, and MEDBindBD. Each plot shows clusters of data points colored according to different medical conditions, including Cardiomegaly (pink), Atelectasis (dark blue), Edema (light green), Consolidation (orange), Pleural Effusion (light blue), Hypertrophy (red), STT Change (green), Normal (gray), Myocardial Infarction (dark blue), and Conduction Disorder (yellow). Below the plots, a legend associates colors with these conditions, categorized under CXR and ECG. To the right of the plots, a bar chart titled "Cross-Modality Top-K" illustrates the recall performance for four models (MedCLIP\*, CXR-CLIP\*, MEDBindNM, and MEDBindBD) at different Top-K values (1, 5, and 10). The bar chart shows that MEDBindBD consistently achieves the highest recall across all Top-K values, reaching approximately 0.55 at Top-K=10.

<span id="page-135-1"></span>**Fig. 2.** Embedding visualization and CXR-to-ECG cross-modality retrieval. (Left) t-SNE plots of CXR and ECG embeddings for various models. (Right) Cross-modality retrieval Top-K recall. MEDBind $_{BD}$  brings CXR and ECG clusters closer in t-SNE and tops cross-modality recall@*{*1,5,10*}*. <sup>∗</sup>CXR VLPM with ECG-CLIP as encoder zoo.

**Implementation Details:** For training, we normalized CXR followed by augmentations [\[29\]](#page-141-0). We normalized ECG and applied Gaussian noise augmentation. Input dimensions were  $224 \times 224$  for CXR and  $12 \times 1000$  for ECG. For efficiency, we truncated text to the first 100 words during pre-training, as 97% of CXR and ECG reports were under 100 words. We set the final embedding size to 256 and temperature  $\tau$  to 0.07. We trained models for 150 epochs with batch size 128 and used AdamW [\[20](#page-140-7)] with weight decay 1e−1, learning rate 4e−4 adjusted via cosine annealing. We used PyTorch on an NVIDIA A100 GPU.

# 3 Experiments and Results

<span id="page-135-0"></span>

## 3.1 Datasets

We present datasets and details in Table [1.](#page-136-0) We pretrained MEDBind on MIMIC-CXR and MIMIC-ECG, including MIMIC-PAIR subset. To avoid training contamination, we maintained the same patient-level splits for all MIMIC datasets.

Starting with CXR datasets, **MIMIC-CXR** [\[13\]](#page-140-8) consists of CXR with their paired reports and labels [\[6\]](#page-140-9). We pre-processed CXR and text using methods from MedCLIP [\[29](#page-141-0)]. In this study, we only included AP and PA view CXR. **CheXpert** [\[10](#page-140-10)] consists of a large number of CXR. Like [\[29](#page-141-0),[30\]](#page-141-1), we formed **CheXpert5x200** with 200 randomly selected CXR from 5 classes in [\[10](#page-140-10)]. We generated prompts for CXR-text retrieval tasks, as proposed in [\[29](#page-141-0)]. **COVID** [\[3](#page-139-3)] is a public dataset with binary COVID-19 labels. We generated prompts as suggested in [\[29\]](#page-141-0) (details in Appendix). **RSNA** [\[25](#page-141-6)] contains pneumonia cases from CXR, publicly available in the National Institutes of Health database.

**MIMIC-ECG** [\[7\]](#page-140-11) has 10-s 12-lead ECG at 500 Hz, downsampled to 100 Hz using a low-pass filter  $[15]$ . ECG has machine reports and links (cart<sub>id</sub>) to freeform text. We used free-form text where available or machine reports to generate ECG text. We created labels (Hypertrophy, STT Change, Normal, Myocardial Infarction, Conduction Disorder) from text using a rule-based method inspired by [\[10](#page-140-10)], excluding ECG with undetectable labels (Appendix). **PTB-XL** [\[28](#page-141-7)] has

| Dataset                  | Task                                         |   |    | LINK CLS Train             | Valid | Test          |
|--------------------------|----------------------------------------------|---|----|----------------------------|-------|---------------|
|                          | MIMIC-CXR [13] Pretrain/Retrieval/LLM-Prompt | ✓ | 12 | 86,853                     |       | 12,059 24,799 |
| Open-I $[4]$             | Retrieval                                    | Х |    |                            |       | 3,269         |
| $CheX$ pert $[10]$       | Retrieval                                    | Х | 5  |                            |       | 1,000         |
| $COVID$ [3]              | Zero-Shot/Few-Shot                           | Х | 2  | 11,028                     |       | 2,780         |
| $RSNA$ [25]              | Zero-Shot/Few-Shot                           | х | 2  | 18,678                     |       | 5,338         |
| MIMIC-ECG <sup>[7]</sup> | Pretrain/Retrieval/LLM-Prompt                | √ | 15 | 88,291                     |       | 12,065 24,644 |
| PTB-XL $[28]$            | Retrieval/Zero-Shot/Few-Shot                 | Х | 5  | 17,415                     | 2.183 | 2,198         |
| ICBEB $[18]$             | Zero-Shot/Few-Shot                           | Х | 19 | $\left 5.501\right\rangle$ |       | 1,376         |
| MIMIC-IV $[12]$          | LLM-Prompt                                   | ✓ |    | 218,787 40,995 72,473      |       |               |
| MIMIC-PAIR*              | Pretrain/Retrieval/LLM-Prompt                | √ |    | 22,397                     | 3.292 | 6.664         |

<span id="page-136-0"></span>**Table 1.** Overview of datasets, tasks, class count (CLS), and training split. LINK shows connected datasets. <sup>∗</sup>Subset from MIMIC-CXR and ECG with both CXR/ECG.

10 s 12-lead ECG at 100 Hz, and superclass labels [\[6,](#page-140-9)[28](#page-141-7)]. **ICBEB** [\[18\]](#page-140-13) has 6–60 seconds 12 leads ECG at 100 Hz and class labels. We used the first 10s for all ECG and zero-padded shorter ECG to 10 s.

**MIMIC-IV** [\[12\]](#page-140-14) contains health records from patients in MIMIC, including discharge notes. We derived in-hospital mortality labels with discharge loc and 30-day readmission from if a patient had a subsequent visit within a 30-day window, using patient's subject id and admission time admittime. We linked CXR and ECG to MIMIC-IV by subject id and modality recording times within 24 h. Using this pairing strategy, we linked MIMIC-CXR and MIMIC-ECG, referred to as **MIMIC-PAIR**, by linking visit identifiers (hadm\_id) in MIMIC-CXR and MIMIC-ECG if available. Without hadm id, we paired cases on subject id and if CXR and ECG recording times were within 24 h.

# 3.2 Modality-to-Text and Cross-Modality Retrieval

**Modality-to-Text Retrieval:** MEDBind preserved retrieval integrity for CXR and ECG compared to single-paired VLPM. We tested CXR and ECG *modality*-to-text retrieval accuracy using recall for the top-K correct clinical reports. In Table [2,](#page-137-0) we benchmarked MEDBind against MedCLIP [\[29\]](#page-141-0) and CXR − CLIP*SwinT* [\[30](#page-141-1)] for CXR-to-text retrieval, and ECG-CLIP for ECGto-text retrieval. MEDBind outperformed all separately trained VLPM in total RSUM *modality*-to-text retrieval. However, in Open-I dataset, MedBind<sub>NM</sub> outperformed MedBind $_{BD}$ , indicating that binding may lose some task depth in exchange for its breadth.

**Cross-Modality Retrieval:** In addition to *modality*-to-text retrieval, we also evaluated cross-modality retrieval between CXR and ECG on MIMIC-PAIR test set (Sect. [3.1\)](#page-135-0) and compared to encoder zoo (CXR-CLIP or MedCLIP with ECG-CLIP). In Fig. [2,](#page-135-1) top-K recall for cross-modality retrieval highlights  $\text{MEDBind}_{BD}$ , with EMCL, outperforms  $\text{MEDBind}_{NM}$  and other models. We visualize t-SNE plots to qualitatively prove that  $\text{MEDBind}_{BD}$  brings CXR and ECG clusters closer within a joint space while maintaining class clustering, com-

<span id="page-137-0"></span>**Table 2.** Results of CXR and ECG *modality*-to-text retrieval. Recall@K = $\{1, 10\}$  $(R_K)$ . C5x200 is CheXpert5x200. Total RSUM is sum of  $R_K$  per modality. **Bold**= best, underline = Second best. <sup>∗</sup>data splits differed from CXR-CLIP, so results taken from [\[30\]](#page-141-1).

| Model                                   |           | MIMIC-CXRC5x200 Open-I |                                          |  | MIMIC-ECG PTB-XL Total RSUM      |     |                   |           |                               |
|-----------------------------------------|-----------|------------------------|------------------------------------------|--|----------------------------------|-----|-------------------|-----------|-------------------------------|
|                                         | $\rm R_1$ | $R_{10}$               | $R_1 R_{10} R_1 R_{10}$                  |  | $\mathbf{R}_1$ $\mathbf{R}_{10}$ |     | $\rm R_1\ R_{10}$ | CXR ECG   |                               |
| <b>CLIP</b>                             | 1.0       | 10.5                   | $ 1.1\,13.9\, 0.8\, 8.1 $ -              |  |                                  |     |                   | $ 35.4 -$ |                               |
| MedCLIP                                 | 2.8       | - 18.0                 | $ 2.931,2 0.9 \t8.6$ -                   |  |                                  | - - |                   | $64.4 -$  |                               |
| CXR-CLIP                                |           | $ 21.6*60.2*$          | $ 2.2\;20.3\; 14.1\;39.3\; $ -           |  |                                  | L L |                   | $157.7 -$ |                               |
| ECG-CLIP                                |           |                        |                                          |  | 51.595.5                         |     | $2.117.4 -$       |           | 166.5                         |
| $\text{MEDBind}_{\text{NM}} 43.8\;88.5$ |           |                        | $2.4\;22.1\;14.3\;41.1\;50.2\;93.9$      |  |                                  |     |                   |           | $1.9$ 18.2 <b>212.2</b> 164.2 |
| $\text{MEDBind}_{BD}$ 44.7 91.0         |           |                        | $ 2.4\;20.0\; 13.6\;39.7\; 53.6\;94.5\;$ |  |                                  |     |                   |           | $1.6$ 19.2 211.4 168.9        |

Image /page/137/Figure/3 description: The image contains four line graphs, each plotting accuracy (%) against the number of shots per class. The first graph, titled "COVID Accuracy (%)", shows the performance of CLIP, MedCLIP, CXR-CLIP, MEDBindNM, and MEDBindBD. The second graph, "RSNA Pneumonia Accuracy (%)", displays the same models. The third graph, "PTB-XL Class Accuracy (%)", includes ECG-CLIP, MEDBindNM, and MEDBindBD. The fourth graph, "ICBEB Class Accuracy (%)", also features ECG-CLIP, MEDBindNM, and MEDBindBD. All graphs show an increasing trend in accuracy as the number of shots per class increases, with MEDBindBD generally achieving the highest accuracy across most datasets.

<span id="page-137-1"></span>**Fig. 3.** Results of zero-shot (denoted as astericks  $(*)$  on y-axis) and few-shot  $(K =$ *{*1, 2, 4, 8, 16*}*) classification using balanced accuracy (%) on CXR (green): COVID and RSNA datasets, and ECG (purple): PTB-XL and ICBEB datasets. (Color figure online)

pared to other models. These results demonstrate the ability of  $\text{MEDBind}_{BD}$  to match CXR to ECG and project modalities within a unified space.

## 3.3 Zero/Few-Shot and Cross-Modality Classification

**Modality-to-Text Zero/Few-Shot:** We evaluated zero-shot performance by calculating the cosine distance between text embeddings and non-text modality embeddings following [\[29\]](#page-141-0). Few-shot classification was assessed using embeddings from the frozen CXR or ECG encoders via the linear probing method [\[5\]](#page-139-1). We reported the average balanced accuracy for each shot over 300 different support sets. In Fig. [3,](#page-137-1) we compared MEDBind with other state-of-the-art models. In the zero-shot task, MEDBind<sub>BD</sub> beat MEDBind<sub>NM</sub> across all datasets and outperformed other models in three out of four datasets.  $\text{MEDBind}_{BD}$  also maintained strong performance in all few-shot scenarios. These results demonstrate that EMCL boosted performance without compromising CXR or ECG zero and

| Cross-Modality Zero-shot Task               |      | Inputs Query | Support                                   | ACC  |
|---------------------------------------------|------|--------------|-------------------------------------------|------|
| Hypertrophy vs. Other                       | CXR. | MedCLIP      | ECG-CLIP                                  | 60.9 |
| Given $CXR$ (query),                        |      | CXR-CLIP     | ECG-CLIP                                  | 72.9 |
| predict its ECG class using                 |      |              | $[MEDBindNM]MEDBindNM 73.7]$              |      |
| ECG support set                             |      |              | $MEDBind_{BD}$ MEDBind <sub>BD</sub> 82.1 |      |
| Cardiomegaly vs. Other                      | ECG  | ECG-CLIP     | $\rm [MedCLIP]$                           | 73.5 |
| Given $ECG$ (query),                        |      | ECG-CLIP     | CXR-CLIP                                  | 70.2 |
| predict its CXR class using CXR support set |      |              | $[MEDBindNM]MEDBindNM 69.1]$              |      |
|                                             |      |              | $MEDBind_{BD}$ MEDBind <sub>BD</sub> 84.6 |      |

<span id="page-138-0"></span>**Table 3.** Cross-modality zero-shot performance, ACC refers to balanced accuracy (%).

<span id="page-138-1"></span>**Table 4.** LLM prompt tuning task. Inputs include: medical text  $(TXT_{MD})$ ;  $CXR/ECG$ text (TXT<sub>C</sub>/ TXT<sub>E</sub>); CXR/ECG embedding ( $EMB<sub>C</sub>/EMB<sub>E</sub>$ ). \*discharge and admission text used for 30-day readmission (Readmit.) and in-hospital mortality (In Hosp.). **Bold** and <u>underline</u> denote best and second best mixed input models (✓), respectively.

| Method      | Mixed Input | CXR/ECG Interpreter    | LLM Inputs                                           | Readmit.    | In Hosp.    |
|-------------|-------------|------------------------|------------------------------------------------------|-------------|-------------|
|             |             |                        |                                                      | ACC         | ACC         |
| Text-Only   | <b>X</b>    | <i>Clinical Expert</i> | $\text{TXT}_{\text{MD}^*,	ext{C,E}}$                 | 65.0        | 74.5        |
| Encoder zoo | ✓           | MedCLIP/ECG-CLIP       | $\text{TXT}_{\text{MD}^*} + \text{EMB}_{\text{C,E}}$ | 60.5        | 72.0        |
|             | ✓           | CXR-CLIP/ECG-CLIP      | $\text{TXT}_{\text{MD}^*} + \text{EMB}_{\text{C,E}}$ | 59.9        | 71.6        |
| MEDBind     | ✓           | $MEDBind_{NM}$         | $\text{TXT}_{\text{MD}^*} + \text{EMB}_{\text{C,E}}$ | 60.5        | 73.6        |
|             | ✓           | <b>MEDBindBD</b>       | $\text{TXT}_{\text{MD}^*} + \text{EMB}_{\text{C,E}}$ | <b>64.3</b> | <b>74.8</b> |

few-shot capabilities. Notably, MEDBind $_{BD}$ 's zero-shot exceeded few-shot performance in the COVID dataset, highlighting its robustness on unseen classes. **Cross-Modality Zero-Shot**: Cardiomegaly and hypertrophy are commonly diagnosed from CXR and ECG, respectively. Both diseases can manifest pathophysiological signs detectable in CXR and ECG [\[23](#page-141-8)]. Thus, we introduce a novel cross-modality zero-shot classification task, assessing if we can detect hypertrophy via CXR and cardiomegaly via ECG, on MIMIC-PAIR test set. We calculated cosine distances between query and support embeddings. For example, we used CXR as query with ECG as support to predict hypertrophy. In Table [3,](#page-138-0) results showed that  $\text{MEDBind}_{BD}$  outperformed  $\text{MEDBind}_{NM}$  and encoder zoo. MEDBind*BD*'s strong cross-modality zero-shot performance implies its ability to integrate CXR and ECG into a unified space–a unique advantage of EMCL.

## 3.4 Multimodal LLM Integration

To assess the efficacy of MEDBind in integrating cross-modality data directly into an LLM, we conducted experiments on predicting 30-day hospital readmission and in-hospital mortality [\[11](#page-140-15)]. We used BioBERT due to its compatibility with BERT-based VLPM. While not generative, BioBERT, as a non-casual masked model, is well suited for classification [\[11\]](#page-140-15). Using MIMIC-IV, we provided BioBERT with discharge summaries for readmission and patient demographics notes for mortality predictions (Appendix). We excluded discharge texts to prevent bias in mortality information. If CXR or ECG were connected to a clinical visit, we provided the following inputs after the text: 1) CXR and ECG clinical interpretation from experts (**Text-only**), 2) embeddings from **encoder zoo**, or 3) embeddings from **MEDBind**. We extracted embeddings from frozen non-text modality encoders with a trainable linear projection layer for LLM integration following [\[22](#page-140-6)]. We used Low-Rank Adaption for efficient LLM prompt-tuning [\[8](#page-140-16)]. Table [4](#page-138-1) highlights the performance of MEDBind*BD* for prompt-tuning LLM, BioBERT, compared to  $\text{MEDBind}_{NM}$  and encoder zoo.  $\text{MEDBind}_{BD}$  outperforms its counterparts by binding CXR and ECG pairs using our proposed EMCL. While the text-only LLM performs similarly on downstream tasks, it relies on clinician-generated texts. Instead, MEDBind*BD* is more automated as it can directly process CXR and ECG–increasing clinical workflow efficiency.

# 4 Conclusion

We introduced MEDBind, a tri-modality binding framework integrating multimodal medical data of CXR, ECG, and text. We demonstrated its benefits in binding different modalities into a unified space via EMCL, which enhanced zeroshot and downstream task performance over single-paired VLPM. Our method is scalable and open for future expansion to include additional modalities.

**Acknowledgments.** Study was funded by NSERC RGPIN-2022-05117. CM holds the Chair in Medical Imaging at the Joint Department of Medical Imaging (UHN & UofT).

**Disclosure of Interests.** The authors have no competing interests to declare.

# References

- <span id="page-139-2"></span>1. Alsentzer, E., Murphy, J., Boag, W., Weng, W.H., Jindi, D., Naumann, T., McDermott, M.: Publicly available clinical bert embeddings. In: Proceedings of the 2nd Clinical Natural Language Processing Workshop (2019)
- <span id="page-139-0"></span>2. Boecking, B., Usuyama, N., Bannur, S., Castro, D.C., Schwaighofer, A., Hyland, S., Wetscherek, M., Naumann, T., Nori, A., Alvarez-Valle, J., et al.: Making the most of text semantics to improve biomedical vision–language processing. In: European conference on computer vision. pp. 1–21. Springer (2022)
- <span id="page-139-3"></span>3. Chowdhury, M.E., Rahman, T., Khandakar, A., Mazhar, R., Kadir, M.A., Mahbub, Z.B., Islam, K.R., Khan, M.S., Iqbal, A., Al Emadi, N., et al.: Can ai help in screening viral and covid-19 pneumonia? Ieee Access **8**, 132665–132676 (2020)
- <span id="page-139-4"></span>4. Demner-Fushman, D., Kohli, M.D., Rosenman, M.B., Shooshan, S.E., Rodriguez, L., Antani, S., Thoma, G.R., McDonald, C.J.: Preparing a collection of radiology examinations for distribution and retrieval. Journal of the American Medical Informatics Association **23**(2), 304–310 (2016)
- <span id="page-139-1"></span>5. Girdhar, R., El-Nouby, A., Liu, Z., Singh, M., Alwala, K.V., Joulin, A., Misra, I.: Imagebind: One embedding space to bind them all. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (2023)

- <span id="page-140-9"></span>6. Goldberger, A.L., Amaral, L.A., Glass, L., Hausdorff, J.M., Ivanov, P.C., Mark, R.G., Mietus, J.E., Moody, G.B., Peng, C.K., Stanley, H.E.: Physiobank, physiotoolkit, and physionet: components of a new research resource for complex physiologic signals. circulation **101**(23), e215–e220 (2000)
- <span id="page-140-11"></span>7. Gow, B., Pollard, T., Nathanson, L.A., Johnson, A., Moody, B., Fernandes, C., Greenbaum, N., Berkowitz, S., Moukheiber, D., Eslami, P., et al.: Mimic-iv-ecgdiagnostic electrocardiogram matched subset (2023)
- <span id="page-140-16"></span>8. Hu, E.J., Wallis, P., Allen-Zhu, Z., Li, Y., Wang, S., Wang, L., Chen, W., et al.: Lora: Low-rank adaptation of large language models. In: International Conference on Learning Representations (ICLR) (2021)
- <span id="page-140-0"></span>9. Huang, S.C., Shen, L., Lungren, M.P., Yeung, S.: Gloria: A multimodal global-local representation learning framework for label-efficient medical image recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV). pp. 3942–3951 (2021)
- <span id="page-140-10"></span>10. Irvin, J., Rajpurkar, P., Ko, M., Yu, Y., Ciurea-Ilcus, S., Chute, C., Marklund, H., Haghgoo, B., Ball, R., Shpanskaya, K., et al.: Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison. In: Proceedings of the AAAI conference on artificial intelligence. vol. 33, pp. 590–597 (2019)
- <span id="page-140-15"></span>11. Jiang, L.Y., Liu, X.C., Nejatian, N.P., Nasir-Moin, M., Wang, D., Abidin, A., Eaton, K., Riina, H.A., Laufer, I., Punjabi, P., et al.: Health system-scale language models are all-purpose prediction engines. Nature pp. 1–6 (2023)
- <span id="page-140-14"></span>12. Johnson, A.E., Bulgarelli, L., Shen, L., Gayles, A., Shammout, A., Horng, S., Pollard, T.J., Hao, S., Moody, B., Gow, B., et al.: Mimic-iv, a freely accessible electronic health record dataset. Scientific data **10**(1), 1 (2023)
- <span id="page-140-8"></span>13. Johnson, A.E., Pollard, T.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Peng, Y., Lu, Z., Mark, R.G., Berkowitz, S.J., Horng, S.: MIMIC-CXR-JPG, a large publicly available database of labeled chest radiographs. arXiv preprint [arXiv:1901.07042](http://arxiv.org/abs/1901.07042) (2019)
- <span id="page-140-3"></span>14. Kenton, J.D.M.W.C., Toutanova, L.K.: Bert: Pre-training of deep bidirectional transformers for language understanding. In: NAACL-HLT. vol. 1, p. 2 (2019)
- <span id="page-140-12"></span>15. Kher, R., et al.: Signal processing techniques for removing noise from ecg signals. J. Biomed. Eng. Res **3**(101), 1–9 (2019)
- <span id="page-140-4"></span>16. Khosla, P., Teterwak, P., Wang, C., Sarna, A., Tian, Y., Isola, P., Maschinot, A., Liu, C., Krishnan, D.: Supervised contrastive learning. Advances in neural information processing systems (NeurIPS) **33**, 18661–18673 (2020)
- <span id="page-140-2"></span>17. Lee, J., Yoon, W., Kim, S., Kim, D., Kim, S., So, C.H., Kang, J.: Biobert: a pre-trained biomedical language representation model for biomedical text mining. Bioinformatics **36**(4), 1234–1240 (2020)
- <span id="page-140-13"></span>18. Liu, F., Liu, C., Zhao, L., Zhang, X., Wu, X., Xu, X., Liu, Y., Ma, C., Wei, S., He, Z., et al.: An open access database for evaluating the algorithms of electrocardiogram rhythm and morphology abnormality detection. Journal of Medical Imaging and Health Informatics **8**(7), 1368–1373 (2018)
- <span id="page-140-1"></span>19. Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., Zhang, Z., Lin, S., Guo, B.: Swin transformer: Hierarchical vision transformer using shifted windows. In: Proceedings of the IEEE/CVF ICCV (2021)
- <span id="page-140-7"></span>20. Loshchilov, I., Hutter, F.: Decoupled weight decay regularization. In: ICLR (2018)
- <span id="page-140-5"></span>21. Mo, S., Kim, M., Lee, K., Shin, J.: S-clip: Semi-supervised vision-language learning using few specialist captions. Advances in NeurIPS **36** (2024)
- <span id="page-140-6"></span>22. Moon, S., Madotto, A., Lin, Z., Nagarajan, T., Smith, M., Jain, S., Yeh, C.F., Murugesan, P., Heidari, P., Liu, Y., et al.: Anymal: An efficient and scalable anymodality augmented language model. arXiv preprint [arXiv:2309.16058](http://arxiv.org/abs/2309.16058) (2023)

- <span id="page-141-8"></span>23. Nakamura, M., Sadoshima, J.: Mechanisms of physiological and pathological cardiac hypertrophy. Nature Reviews Cardiology **15**(7), 387–407 (2018)
- <span id="page-141-5"></span>24. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-141-6"></span>25. Shih, G., et al.: Augmenting the national institutes of health chest radiograph dataset with expert annotations of possible pneumonia. Radiology: Artificial Intelligence **1**(1), e180041 (2019)
- <span id="page-141-3"></span>26. Tu, T., Azizi, S., Driess, D., Schaekermann, M., Amin, M., Chang, P.C., Carroll, A., Lau, C., Tanno, R., Ktena, I., et al.: Towards generalist biomedical ai. NEJM AI **1**(3), AIoa2300138 (2024)
- <span id="page-141-4"></span>27. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. Advances in neural information pro cessing systems **30** (2017)
- <span id="page-141-7"></span>28. Wagner, P., Strodthoff, N., Bousseljot, R.D., Kreiseler, D., Lunze, F.I., Samek, W., Schaeffter, T.: Ptb-xl, a large publicly available electrocardiography dataset. Scientific data **7**(1), 154 (2020)
- <span id="page-141-0"></span>29. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. In: Proceedings of the 2022 Conference on Empirical Methods in Natural Language Processing. pp. 3876–3887 (2022)
- <span id="page-141-1"></span>30. You, K., Gu, J., Ham, J., Park, B., Kim, J., Hong, E.K., Baek, W., Roh, B.: Cxrclip: Toward large scale chest x-ray language-image pre-training. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 101–111. Springer (2023)
- <span id="page-141-2"></span>31. Zhang, C., Sun, X., Yang, Y., Liu, L., Liu, Q., Zhou, X., Wang, Y.: All in one: Exploring unified vision-language tracking with multi-modal alignment. In: Proceedings of the 31st ACM Multimedia. pp. 5552–5561 (2023)