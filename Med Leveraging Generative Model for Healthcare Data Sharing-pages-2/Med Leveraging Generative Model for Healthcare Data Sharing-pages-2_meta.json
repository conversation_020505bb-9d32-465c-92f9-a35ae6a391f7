{"table_of_contents": [{"title": "Feature Extraction for Generative Medical\nImaging Evaluation: New Evidence\nAgainst an Evolving Trend", "heading_level": null, "page_id": 0, "polygon": [[77.33788395904436, 51.74298095703125], [376.17747440273035, 51.74298095703125], [376.17747440273035, 102.************], [77.33788395904436, 102.************]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[39.04436860068259, 54.34844970703125], [134.40273037542661, 54.34844970703125], [134.40273037542661, 66.23590087890625], [39.04436860068259, 66.23590087890625]]}, {"title": "2 Methods", "heading_level": null, "page_id": 2, "polygon": [[51.80887372013652, 182.318335208099], [125.68359374999999, 182.318335208099], [125.68359374999999, 193.**********], [51.80887372013652, 193.**********]]}, {"title": "2.1 Generative Modeling", "heading_level": null, "page_id": 2, "polygon": [[52.55972696245733, 207.8278965129359], [186.21160409556313, 207.8278965129359], [186.21160409556313, 217.88232421875], [52.55972696245733, 217.88232421875]]}, {"title": "2.2 Human Evaluation", "heading_level": null, "page_id": 2, "polygon": [[52.55972696245733, 496.68616422947133], [174.19795221843003, 496.68616422947133], [174.19795221843003, 507.08935546875006], [52.55972696245733, 507.08935546875006]]}, {"title": "2.3 Fréchet Distances", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 488.85107421875006], [154.67576791808872, 488.85107421875006], [154.67576791808872, 500.57568359375006], [38.29351535836177, 500.57568359375006]]}, {"title": "3 Results", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 307.608154296875], [117.3046875, 307.608154296875], [117.3046875, 319.007080078125], [52.55972696245733, 319.007080078125]]}, {"title": "4 Discussion", "heading_level": null, "page_id": 7, "polygon": [[38.29351535836177, 507.08935546875006], [121.63822525597269, 507.08935546875006], [121.63822525597269, 520.76806640625], [38.29351535836177, 520.76806640625]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 432.16197975253095], [138.90784982935153, 432.16197975253095], [138.90784982935153, 444.55810546875], [52.55972696245733, 444.55810546875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[38.29351535836177, 129.3778076171875], [103.6083984375, 129.3778076171875], [103.6083984375, 143.3822021484375], [38.29351535836177, 143.3822021484375]]}, {"title": "Few-Shot Domain Adaptive Object\nDetection for Microscopic Images", "heading_level": null, "page_id": 11, "polygon": [[90.10238907849829, 52.31292724609375], [336.015625, 52.31292724609375], [336.015625, 84.9219970703125], [90.10238907849829, 84.9219970703125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 12, "polygon": [[52.55972696245733, 54.77052868391451], [148.779296875, 54.77052868391451], [148.779296875, 66.1951904296875], [52.55972696245733, 66.1951904296875]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 289.20703125], [137.0703125, 289.20703125], [137.0703125, 301.90869140625], [38.29351535836177, 301.90869140625]]}, {"title": "2.1 Domain Generalized Class Balancing Cut-Paste", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 417.20068359375], [305.078125, 417.1563554555681], [305.078125, 428.27392578125], [38.29351535836177, 428.41057367829023]]}, {"title": "2.2 Inter-domain Alignment and Intra-domain Class Consistency", "heading_level": null, "page_id": 14, "polygon": [[52.55972696245733, 515.4431946006749], [387.4402730375426, 515.4431946006749], [387.4402730375426, 525.97900390625], [52.55972696245733, 525.97900390625]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 557.458942632171], [222.25255972696243, 557.458942632171], [222.25255972696243, 568.31787109375], [52.55972696245733, 568.31787109375]]}, {"title": "3.1 Results", "heading_level": null, "page_id": 18, "polygon": [[52.55972696245733, 55.52080989876266], [117.51953125, 55.52080989876266], [117.51953125, 66.11376953125], [52.55972696245733, 66.11376953125]]}, {"title": "3.2 Ablation", "heading_level": null, "page_id": 18, "polygon": [[52.55972696245733, 333.12485939257596], [123.96484375, 333.12485939257596], [123.96484375, 343.59619140625], [52.55972696245733, 343.59619140625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 156.00244140625], [124.64163822525596, 156.00244140625], [124.64163822525596, 170.495361328125], [38.29351535836177, 170.495361328125]]}, {"title": "References", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 388.8662109375], [103.447265625, 388.8662109375], [103.447265625, 403.84765625], [38.29351535836177, 403.84765625]]}, {"title": "Few-Shot Lymph Node Metastasis\nClassification Meets High Performance\non Whole Slide Images\nvia the Informative Non-parametric\nClassifier", "heading_level": null, "page_id": 22, "polygon": [[86.58203125, 51.98724365234375], [365.6655290102389, 51.98724365234375], [365.6655290102389, 138.7412109375], [86.58203125, 138.7412109375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 23, "polygon": [[39.04436860068259, 54.77052868391451], [134.40273037542661, 54.77052868391451], [134.40273037542661, 66.23590087890625], [39.04436860068259, 66.23590087890625]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 24, "polygon": [[51.80887372013652, 393.42578125], [150.92150170648463, 393.42578125], [150.92150170648463, 405.150390625], [51.80887372013652, 405.150390625]]}, {"title": "2.1 Informative Similarity Logit for Non-parametric Classifier", "heading_level": null, "page_id": 24, "polygon": [[52.55972696245733, 563.4326171875], [372.42320819112626, 563.4326171875], [372.42320819112626, 573.8544921875], [52.55972696245733, 573.8544921875]]}, {"title": "2.2 Retrieval Aggregation of Informative Non-parametric Classifier", "heading_level": null, "page_id": 25, "polygon": [[38.29351535836177, 493.41064453125006], [385.1877133105802, 493.41064453125006], [385.1877133105802, 503.83251953125006], [38.29351535836177, 503.83251953125006]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 432.9122609673791], [147.91808873720134, 432.9122609673791], [147.91808873720134, 443.5810546875], [52.55972696245733, 443.5810546875]]}, {"title": "114 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 27, "polygon": [[39.04436860068259, 29.433654785156254], [117.94921875, 29.433654785156254], [117.94921875, 39.4891357421875], [39.04436860068259, 39.4891357421875]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 30, "polygon": [[52.55972696245733, 294.09228515625], [139.00390625, 294.09228515625], [139.00390625, 307.4453125], [52.55972696245733, 307.4453125]]}, {"title": "References", "heading_level": null, "page_id": 31, "polygon": [[39.02099609375, 52.8014**********], [103.662109375, 52.8014**********], [103.662109375, 66.72442626953125], [39.02099609375, 66.72442626953125]]}, {"title": "Fine-Grained Prompt Tuning: A\nParameter and Memory Efficient Transfer\nLearning Method for High-Resolution\nMedical Image Classification", "heading_level": null, "page_id": 33, "polygon": [[60.15625, 51.13232421875], [364.58984375, 51.13232421875], [364.58984375, 121.15429687500001], [60.15625, 121.15429687500001]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 33, "polygon": [[39.04436860068259, 559.85009765625], [134.40273037542661, 559.85009765625], [134.40273037542661, 572.22607421875], [39.04436860068259, 572.22607421875]]}, {"title": "2 Method", "heading_level": null, "page_id": 35, "polygon": [[38.29351535836177, 427.296875], [106.77734375, 427.296875], [106.77734375, 441.626953125], [38.29351535836177, 441.626953125]]}, {"title": "2.1 Side Tuning", "heading_level": null, "page_id": 35, "polygon": [[38.29351535836177, 452.**********], [126.1433447098976, 452.**********], [126.1433447098976, 465.72753906250006], [38.29351535836177, 465.72753906250006]]}, {"title": "2.2 Asymmetric Input", "heading_level": null, "page_id": 36, "polygon": [[52.55972696245733, 425.66845703125], [173.056640625, 425.66845703125], [173.056640625, 436.74169921875], [52.55972696245733, 436.74169921875]]}, {"title": "2.3 Fine-Grained Prompts and Fusion Module", "heading_level": null, "page_id": 36, "polygon": [[52.55972696245733, 578.08837890625], [294.3359375, 578.08837890625], [294.3359375, 589.16162109375], [52.55972696245733, 589.16162109375]]}, {"title": "2.4 Important Token Selection", "heading_level": null, "page_id": 37, "polygon": [[38.29351535836177, 512.6259765625], [200.4778156996587, 512.6259765625], [200.4778156996587, 523.69921875], [38.29351535836177, 523.69921875]]}, {"title": "2.5 Fine-Grained Features Preloading", "heading_level": null, "page_id": 38, "polygon": [[52.55972696245733, 108.289794921875], [251.58203124999997, 108.289794921875], [251.58203124999997, 120.01440429687501], [52.55972696245733, 120.01440429687501]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 38, "polygon": [[51.80887372013652, 230.58398437500003], [148.564453125, 230.58398437500003], [148.564453125, 243.28564453125003], [51.80887372013652, 243.28564453125003]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 38, "polygon": [[52.55972696245733, 255.17309570312503], [124.072265625, 255.17309570312503], [124.072265625, 266.897705078125], [52.55972696245733, 266.897705078125]]}, {"title": "3.2 Training and Evaluation Setup", "heading_level": null, "page_id": 38, "polygon": [[52.55972696245733, 365.09130859375], [235.46875, 365.09130859375], [235.46875, 376.81591796875], [52.55972696245733, 376.81591796875]]}, {"title": "3.3 Comparisons with State-of-the-Art", "heading_level": null, "page_id": 39, "polygon": [[38.29351535836177, 330.89453125], [241.484375, 330.89453125], [241.484375, 341.9677734375], [38.29351535836177, 341.9677734375]]}, {"title": "3.4 Impact of Components", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 257.94140625], [195.22184300341297, 257.94140625], [195.22184300341297, 269.666015625], [52.55972696245733, 269.666015625]]}, {"title": "3.5 Impact of Important Token Selection Ratio", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 456.93408203125006], [298.0887372013652, 456.93408203125006], [298.0887372013652, 469.31005859375006], [52.55972696245733, 469.31005859375006]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 558.**********], [139.21875, 558.**********], [139.21875, 571.**********], [52.55972696245733, 571.**********]]}, {"title": "References", "heading_level": null, "page_id": 41, "polygon": [[38.29351535836177, 305.328369140625], [103.**********, 305.328369140625], [103.**********, 319.332763671875], [38.29351535836177, 319.332763671875]]}, {"title": "FissionFusion: Fast Geometric Generation\nand Hierarchical Souping for Medical\nImage Analysis", "heading_level": null, "page_id": 44, "polygon": [[80.34129692832764, 50.3995361328125], [374.2578125, 50.3995361328125], [374.2578125, 102.427490234375], [80.34129692832764, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 44, "polygon": [[52.93212890625, 520.**********], [149.208984375, 520.**********], [149.208984375, 534.12109375], [52.93212890625, 534.12109375]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 47, "polygon": [[38.29351535836177, 461.16796875000006], [137.4061433447099, 461.16796875000006], [137.4061433447099, 474.84667968750006], [38.29351535836177, 474.84667968750006]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 49, "polygon": [[38.29351535836177, 221.13916015625], [134.40273037542661, 221.13916015625], [134.40273037542661, 233.51513671875003], [38.29351535836177, 233.51513671875003]]}, {"title": "4 Results and Discussion", "heading_level": null, "page_id": 50, "polygon": [[52.55972696245733, 259.244140625], [210.33203125, 259.244140625], [210.33203125, 271.94580078125], [52.55972696245733, 271.94580078125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 52, "polygon": [[52.55972696245733, 156.00244140625], [138.90784982935153, 156.00244140625], [138.90784982935153, 170.169677734375], [52.55972696245733, 170.169677734375]]}, {"title": "References", "heading_level": null, "page_id": 52, "polygon": [[51.80887372013652, 392.123046875], [117.734375, 392.123046875], [117.734375, 406.453125], [51.80887372013652, 406.453125]]}, {"title": "GBT: Geometric-Oriented Brain\nTransformer for Autism Diagnosis", "heading_level": null, "page_id": 55, "polygon": [[90.8532423208191, 51.946533203125], [334.296875, 51.946533203125], [334.296875, 84.7591552734375], [90.8532423208191, 84.7591552734375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 55, "polygon": [[39.04436860068259, 496.68616422947133], [134.4921875, 496.68616422947133], [134.4921875, 509.36914062500006], [39.04436860068259, 509.36914062500006]]}, {"title": "2 Proposed Method", "heading_level": null, "page_id": 57, "polygon": [[38.29351535836177, 495.36474609375006], [165.93856655290102, 495.36474609375006], [165.93856655290102, 509.04345703125006], [38.29351535836177, 509.04345703125006]]}, {"title": "2.1 Problem Definition", "heading_level": null, "page_id": 58, "polygon": [[51.80887372013652, 55.52080989876266], [176.38671875, 55.52080989876266], [176.38671875, 65.7880859375], [51.80887372013652, 65.7880859375]]}, {"title": "2.2 AWMA-Based Transformer Module", "heading_level": null, "page_id": 58, "polygon": [[52.55972696245733, 346.62992125984255], [260.60546875, 346.62992125984255], [260.60546875, 355.97216796875], [52.55972696245733, 355.97216796875]]}, {"title": "2.3 Geometric-Oriented Representation Learning Module", "heading_level": null, "page_id": 59, "polygon": [[38.29351535836177, 249.0933633295838], [335.63139931740614, 249.0933633295838], [335.63139931740614, 259.244140625], [38.29351535836177, 259.244140625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 60, "polygon": [[51.80887372013652, 207.46044921875], [148.45703125, 207.46044921875], [148.45703125, 219.18505859375], [51.80887372013652, 219.18505859375]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 60, "polygon": [[52.55972696245733, 232.21240234375003], [123.857421875, 232.21240234375003], [123.857421875, 242.63427734375003], [52.55972696245733, 242.63427734375003]]}, {"title": "3.2 Compared Methods", "heading_level": null, "page_id": 60, "polygon": [[52.55972696245733, 377.46728515625], [179.45392491467575, 377.46728515625], [179.45392491467575, 388.54052734375], [52.55972696245733, 388.54052734375]]}, {"title": "3.3 Evaluation Metrics", "heading_level": null, "page_id": 60, "polygon": [[52.55972696245733, 463.12207031250006], [175.7421875, 463.12207031250006], [175.7421875, 474.19531250000006], [52.55972696245733, 474.19531250000006]]}, {"title": "3.4 Training Procedure", "heading_level": null, "page_id": 60, "polygon": [[52.55972696245733, 548.77685546875], [178.10546875, 548.77685546875], [178.10546875, 559.85009765625], [52.55972696245733, 559.85009765625]]}, {"title": "3.5 Compared Results", "heading_level": null, "page_id": 61, "polygon": [[38.29351535836177, 165.77294921875], [158.984375, 165.77294921875], [158.984375, 177.8232421875], [38.29351535836177, 177.8232421875]]}, {"title": "3.6 Ablation Study", "heading_level": null, "page_id": 61, "polygon": [[39.04436860068259, 562.45556640625], [142.66211604095562, 562.45556640625], [142.66211604095562, 574.18017578125], [39.04436860068259, 574.18017578125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 62, "polygon": [[52.55972696245733, 467.68164062500006], [138.90784982935153, 467.68164062500006], [138.90784982935153, 480.05761718750006], [52.55972696245733, 480.05761718750006]]}, {"title": "References", "heading_level": null, "page_id": 63, "polygon": [[39.04436860068259, 165.6915283203125], [103.6083984375, 165.6915283203125], [103.6083984375, 179.45166015625], [39.04436860068259, 179.45166015625]]}, {"title": "Gradient Guided Co-Retention Feature\nPyramid Network for LDCT Image\nDenoising", "heading_level": null, "page_id": 66, "polygon": [[90.01953125, 51.09161376953125], [366.30859375, 51.09161376953125], [366.30859375, 102.************], [90.01953125, 102.************]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 66, "polygon": [[52.55972696245733, 528.2587890625], [148.66894197952217, 528.2587890625], [148.66894197952217, 541.2861328125], [52.55972696245733, 541.2861328125]]}, {"title": "2 Methods", "heading_level": null, "page_id": 68, "polygon": [[52.55972696245733, 203.************], [125.79101562499999, 203.************], [126.1433447098976, 216.************], [52.55972696245733, 216.************]]}, {"title": "2.1 Overall Structure", "heading_level": null, "page_id": 68, "polygon": [[52.55972696245733, 228.79272460937503], [168.65234375, 228.79272460937503], [168.65234375, 240.19165039062503], [52.55972696245733, 240.19165039062503]]}, {"title": "2.2 Residual Encoder", "heading_level": null, "page_id": 69, "polygon": [[38.29351535836177, 179.77734375], [154.47265625, 179.77734375], [154.47265625, 191.501953125], [38.29351535836177, 191.501953125]]}, {"title": "2.3 Directional Gradient Approximation", "heading_level": null, "page_id": 69, "polygon": [[38.29351535836177, 361.5087890625], [250.07812499999997, 361.5087890625], [250.07812499999997, 373.2333984375], [38.29351535836177, 373.2333984375]]}, {"title": "2.4 Co-Retention Decoder", "heading_level": null, "page_id": 70, "polygon": [[52.55972696245733, 54.77052868391451], [192.9692832764505, 55.52080989876266], [192.9692832764505, 65.7880859375], [52.55972696245733, 65.7880859375]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 71, "polygon": [[38.29351535836177, 258.429931640625], [208.291015625, 258.429931640625], [208.291015625, 272.108642578125], [38.29351535836177, 272.108642578125]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 71, "polygon": [[38.29351535836177, 283.3447265625], [167.36328125, 283.3447265625], [167.36328125, 296.04638671875], [38.29351535836177, 296.04638671875]]}, {"title": "3.2 Experimental Results", "heading_level": null, "page_id": 72, "polygon": [[52.55972696245733, 238.56323242187503], [188.4641638225256, 238.56323242187503], [188.4641638225256, 248.65942382812503], [52.55972696245733, 248.65942382812503]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 72, "polygon": [[52.55972696245733, 537.9516310461192], [156.92832764505118, 537.9516310461192], [156.92832764505118, 548.77685546875], [52.55972696245733, 548.77685546875]]}, {"title": "4 Conclusions", "heading_level": null, "page_id": 74, "polygon": [[51.80887372013652, 52.8014**********], [145.986328125, 52.8014**********], [145.986328125, 66.56158447265625], [51.80887372013652, 66.56158447265625]]}, {"title": "References", "heading_level": null, "page_id": 74, "polygon": [[52.55972696245733, 283.833251953125], [117.734375, 283.833251953125], [117.734375, 297.511962890625], [52.55972696245733, 297.511962890625]]}, {"title": "<PERSON><PERSON><PERSON> vs. <PERSON><PERSON>: Core-Periphery<br>Organization in Functional Brain Networks\nOrganization in Functional Brain Networks", "heading_level": null, "page_id": 77, "polygon": [[62.32081911262798, 52.51968503937008], [362.44140625, 52.51968503937008], [362.44140625, 96.71868896484375], [62.51953124999999, 96.71868896484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 77, "polygon": [[39.04436860068259, 558.54736328125], [134.4921875, 558.54736328125], [134.4921875, 570.27197265625], [39.04436860068259, 570.27197265625]]}, {"title": "2 Methods", "heading_level": null, "page_id": 79, "polygon": [[38.29351535836177, 402.87060546875], [111.50390625, 402.87060546875], [111.50390625, 416.54931640625], [38.29351535836177, 416.54931640625]]}, {"title": "2.1 Data Preparation", "heading_level": null, "page_id": 79, "polygon": [[38.29351535836177, 428.92529296875], [154.47265625, 428.92529296875], [154.47265625, 440.64990234375], [38.29351535836177, 440.64990234375]]}, {"title": "2.2 Twin-Transformer", "heading_level": null, "page_id": 80, "polygon": [[52.55972696245733, 382.64341957255346], [170.44368600682594, 382.64341957255346], [170.44368600682594, 392.44873046875], [52.55972696245733, 392.44873046875]]}, {"title": "2.3 Loss Function", "heading_level": null, "page_id": 81, "polygon": [[38.29351535836177, 458.421822272216], [135.15358361774744, 458.421822272216], [135.15358361774744, 468.98437500000006], [38.29351535836177, 468.98437500000006]]}, {"title": "3 Results", "heading_level": null, "page_id": 83, "polygon": [[38.29351535836177, 53.8192138671875], [103.984375, 53.8192138671875], [103.984375, 66.3580322265625], [38.29351535836177, 66.3580322265625]]}, {"title": "3.1 Core-Periphery Organization of Gyri and Sulci", "heading_level": null, "page_id": 83, "polygon": [[38.29351535836177, 328.2890625], [302.0703125, 328.2890625], [302.0703125, 339.3623046875], [38.29351535836177, 339.3623046875]]}, {"title": "3.2 Validation and Ablation Study", "heading_level": null, "page_id": 84, "polygon": [[52.55972696245733, 250.5939257592801], [235.01706484641636, 250.5939257592801], [235.01706484641636, 261.035400390625], [52.55972696245733, 261.035400390625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 85, "polygon": [[38.29351535836177, 53.0050048828125], [125.25390624999999, 53.0050048828125], [125.25390624999999, 66.5208740234375], [38.29351535836177, 66.5208740234375]]}, {"title": "References", "heading_level": null, "page_id": 85, "polygon": [[38.29351535836177, 294.74365234375], [104.19921875, 294.74365234375], [104.19921875, 308.748046875], [38.29351535836177, 308.748046875]]}, {"title": "Is This Hard for You? Personalized\nHuman Difficulty Estimation for Skin\nLesion Diagnosis", "heading_level": null, "page_id": 88, "polygon": [[90.986328125, 50.68450927734375], [363.0859375, 50.68450927734375], [363.0859375, 102.427490234375], [90.986328125, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 88, "polygon": [[53.14697265625, 495.36474609375006], [149.1015625, 495.36474609375006], [149.1015625, 507.74072265625006], [53.14697265625, 507.74072265625006]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 89, "polygon": [[38.29351535836177, 551.056640625], [141.9112627986348, 551.056640625], [141.9112627986348, 563.4326171875], [38.29351535836177, 563.4326171875]]}, {"title": "3 Method", "heading_level": null, "page_id": 90, "polygon": [[52.55972696245733, 360.53173828125], [120.13651877133105, 360.53173828125], [120.13651877133105, 372.90771484375], [52.55972696245733, 372.90771484375]]}, {"title": "3.1 Baseline Methods", "heading_level": null, "page_id": 92, "polygon": [[52.55972696245733, 261.8481439820023], [169.833984375, 261.8481439820023], [169.833984375, 271.6201171875], [52.55972696245733, 271.6201171875]]}, {"title": "4 Data", "heading_level": null, "page_id": 93, "polygon": [[38.29351535836177, 248.17089843750003], [89.052734375, 248.17089843750003], [89.052734375, 259.244140625], [38.29351535836177, 259.244140625]]}, {"title": "5 Results", "heading_level": null, "page_id": 93, "polygon": [[39.02099609375, 559.5244140625], [102.8668941979522, 559.5244140625], [102.8668941979522, 571.**********], [39.02099609375, 571.**********]]}, {"title": "6 Discussion and Conclusion", "heading_level": null, "page_id": 95, "polygon": [[38.29351535836177, 363.1361079865017], [216.24573378839588, 363.1361079865017], [216.24573378839588, 374.21044921875], [38.29351535836177, 374.21044921875]]}, {"title": "References", "heading_level": null, "page_id": 96, "polygon": [[51.80887372013652, 181.894287109375], [117.13310580204778, 181.894287109375], [117.13310580204778, 194.595947265625], [51.80887372013652, 194.595947265625]]}, {"title": "LaB-GATr: Geometric Algebra\nTransformers for Large Biomedical Surface\nand Volume Meshes", "heading_level": null, "page_id": 98, "polygon": [[76.58703071672355, 51.13232421875], [378.5546875, 51.13232421875], [378.5546875, 102.5089111328125], [76.58703071672355, 102.5089111328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 98, "polygon": [[53.20068359375, 480.70898437500006], [148.994140625, 480.70898437500006], [148.994140625, 494.38769531250006], [53.20068359375, 494.38769531250006]]}, {"title": "2 Methods", "heading_level": null, "page_id": 100, "polygon": [[52.55972696245733, 417.**********], [125.39249146757679, 417.**********], [125.39249146757679, 429.**********], [52.55972696245733, 429.**********]]}, {"title": "2.1 Geometric Algebra", "heading_level": null, "page_id": 100, "polygon": [[52.55972696245733, 508.39208984375006], [175.69965870307166, 508.39208984375006], [175.69965870307166, 518.81396484375], [52.55972696245733, 518.81396484375]]}, {"title": "2.2 Embedding Meshes in G(3, 0, 1)", "heading_level": null, "page_id": 101, "polygon": [[38.29351535836177, 143.8707275390625], [226.23046875, 143.8707275390625], [226.23046875, 154.7811279296875], [38.29351535836177, 154.7811279296875]]}, {"title": "2.3 Geometric Algebra Transformers", "heading_level": null, "page_id": 101, "polygon": [[39.04436860068259, 423.90888638920137], [232.0136518771331, 423.90888638920137], [232.0136518771331, 434.4619140625], [39.04436860068259, 434.4619140625]]}, {"title": "2.4 Learned, Geometric Tokenisation", "heading_level": null, "page_id": 102, "polygon": [[52.55972696245733, 55.52080989876266], [247.03071672354946, 55.52080989876266], [247.03071672354946, 65.7066650390625], [52.55972696245733, 65.7066650390625]]}, {"title": "2.5 Neural Network Architecture", "heading_level": null, "page_id": 102, "polygon": [[52.55972696245733, 560.4600674915636], [229.0102389078498, 560.4600674915636], [229.0102389078498, 569.9462890625], [52.55972696245733, 569.9462890625]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 103, "polygon": [[38.29351535836177, 447.81494140625], [207.98634812286687, 447.81494140625], [207.98634812286687, 460.84228515625006], [38.29351535836177, 460.84228515625006]]}, {"title": "3.1 Coronary Artery Models", "heading_level": null, "page_id": 103, "polygon": [[38.29351535836177, 548.77685546875], [190.78125, 548.77685546875], [190.78125, 560.50146484375], [38.29351535836177, 560.50146484375]]}, {"title": "3.2 Postmenstrual Age Prediction from the Cortical Surface", "heading_level": null, "page_id": 105, "polygon": [[38.29351535836177, 97.53655793025872], [349.89761092150167, 97.53655793025872], [349.89761092150167, 107.964111328125], [38.29351535836177, 107.964111328125]]}, {"title": "4 Discussion and Conclusion", "heading_level": null, "page_id": 105, "polygon": [[39.04436860068259, 510.1912260967379], [216.24573378839588, 510.1912260967379], [216.24573378839588, 521.41943359375], [39.04436860068259, 521.41943359375]]}, {"title": "References", "heading_level": null, "page_id": 106, "polygon": [[52.55972696245733, 482.98876953125006], [117.13310580204778, 482.98876953125006], [117.13310580204778, 494.71337890625006], [52.55972696245733, 494.71337890625006]]}, {"title": "Learning Temporally Equivariance\nfor Degenerative Disease Progression\nin OCT by Predicting Future\nRepresentations", "heading_level": null, "page_id": 109, "polygon": [[80.13671875, 51.0509033203125], [344.39453125, 51.0509033203125], [344.39453125, 120.58435058593751], [80.13671875, 120.58435058593751]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 109, "polygon": [[38.48388671875, 504.9392575928009], [135.029296875, 504.9392575928009], [135.029296875, 517.51123046875], [38.48388671875, 517.51123046875]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 111, "polygon": [[38.29351535836177, 454.97998046875006], [136.65529010238907, 454.97998046875006], [136.65529010238907, 468.00732421875006], [38.29351535836177, 468.00732421875006]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 114, "polygon": [[52.55972696245733, 143.30371203599552], [221.71875, 143.30371203599552], [221.71875, 154.4554443359375], [52.55972696245733, 154.4554443359375]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 114, "polygon": [[52.55972696245733, 269.34033203125], [180.20477815699658, 269.34033203125], [180.20477815699658, 279.4365234375], [52.55972696245733, 279.4365234375]]}, {"title": "3.2 Results", "heading_level": null, "page_id": 115, "polygon": [[38.29351535836177, 330.731689453125], [102.8668941979522, 330.731689453125], [102.8668941979522, 341.31640625], [38.29351535836177, 341.31640625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 117, "polygon": [[38.29351535836177, 52.8421630859375], [124.93164062499999, 52.8421630859375], [124.93164062499999, 66.5208740234375], [38.29351535836177, 66.5208740234375]]}, {"title": "References", "heading_level": null, "page_id": 117, "polygon": [[38.29351535836177, 403.1962890625], [103.232421875, 403.1962890625], [103.232421875, 416.875], [38.29351535836177, 416.875]]}, {"title": "Leveraging Image Captions for Selective\nWhole Slide Image Annotation", "heading_level": null, "page_id": 120, "polygon": [[82.5938566552901, 51.41729736328125], [371.25, 51.41729736328125], [371.25, 83.863525390625], [82.5938566552901, 83.863525390625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 121, "polygon": [[39.04436860068259, 54.75555419921875], [134.40273037542661, 54.75555419921875], [134.40273037542661, 66.31732177734375], [39.04436860068259, 66.31732177734375]]}, {"title": "2 Method", "heading_level": null, "page_id": 122, "polygon": [[51.80887372013652, 250.12500000000003], [120.13651877133105, 250.12500000000003], [120.13651877133105, 263.15234375], [51.80887372013652, 263.15234375]]}, {"title": "2.1 General Setup", "heading_level": null, "page_id": 122, "polygon": [[52.55972696245733, 274.2255859375], [152.75390625, 274.2255859375], [152.75390625, 285.9501953125], [52.55972696245733, 285.9501953125]]}, {"title": "2.2 Prototype Sampling", "heading_level": null, "page_id": 122, "polygon": [[52.55972696245733, 477.45214843750006], [181.328125, 477.45214843750006], [181.328125, 489.82812500000006], [52.55972696245733, 489.82812500000006]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 124, "polygon": [[52.55972696245733, 313.3076171875], [148.66894197952217, 313.3076171875], [148.66894197952217, 324.70654296875], [52.55972696245733, 324.70654296875]]}, {"title": "3.1 Image-Caption Pair Databases", "heading_level": null, "page_id": 124, "polygon": [[52.55972696245733, 338.38525390625], [233.51535836177473, 338.38525390625], [233.51535836177473, 348.80712890625], [52.55972696245733, 348.80712890625]]}, {"title": "3.2 Semantic Segmentation", "heading_level": null, "page_id": 124, "polygon": [[52.55972696245733, 495.69042968750006], [198.22525597269623, 495.69042968750006], [198.22525597269623, 506.11230468750006], [52.55972696245733, 506.11230468750006]]}, {"title": "3.3 Mitotic Figure Detection", "heading_level": null, "page_id": 125, "polygon": [[38.29351535836177, 479.08056640625006], [192.21843003412968, 479.08056640625006], [192.21843003412968, 490.80517578125006], [38.29351535836177, 490.80517578125006]]}, {"title": "3.4 Results", "heading_level": null, "page_id": 126, "polygon": [[52.55972696245733, 254.34533183352082], [118.056640625, 254.34533183352082], [118.056640625, 264.617919921875], [52.55972696245733, 264.617919921875]]}, {"title": "4 Discussion and Conclusion", "heading_level": null, "page_id": 128, "polygon": [[52.55972696245733, 54.77052868391451], [231.26279863481227, 54.77052868391451], [231.26279863481227, 66.39874267578125], [52.55972696245733, 66.39874267578125]]}, {"title": "References", "heading_level": null, "page_id": 128, "polygon": [[52.55972696245733, 471.91552734375006], [117.13310580204778, 471.91552734375006], [117.13310580204778, 483.64013671875006], [52.55972696245733, 483.64013671875006]]}, {"title": "MEDBind: Unifying Language\nand Multimodal Medical Data\nEmbeddings", "heading_level": null, "page_id": 131, "polygon": [[103.232421875, 51.5394287109375], [323.984375, 51.5394287109375], [323.984375, 102.3460693359375], [103.232421875, 102.3460693359375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 132, "polygon": [[52.55972696245733, 54.51129150390625], [148.349609375, 54.51129150390625], [148.349609375, 66.56158447265625], [52.55972696245733, 66.56158447265625]]}, {"title": "2 Methods and Materials", "heading_level": null, "page_id": 132, "polygon": [[52.55972696245733, 553.**********], [211.8359375, 553.**********], [211.8359375, 565.38671875], [52.55972696245733, 565.38671875]]}, {"title": "2.1 Model Architecture", "heading_level": null, "page_id": 132, "polygon": [[52.55972696245733, 576.13427734375], [178.70307167235495, 576.13427734375], [178.70307167235495, 587.20751953125], [52.55972696245733, 587.20751953125]]}, {"title": "2.2 Loss Function", "heading_level": null, "page_id": 133, "polygon": [[38.29351535836177, 444.55810546875], [135.15358361774744, 444.55810546875], [135.15358361774744, 455.63134765625006], [38.29351535836177, 455.63134765625006]]}, {"title": "2.3 ECG-CLIP and Tri-Modality Evaluations", "heading_level": null, "page_id": 134, "polygon": [[52.55972696245733, 481.68053993250845], [289.07849829351534, 481.68053993250845], [289.07849829351534, 490.80517578125006], [52.55972696245733, 490.80517578125006]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 135, "polygon": [[38.29351535836177, 359.5546875], [208.18359375, 359.5546875], [208.18359375, 371.279296875], [38.29351535836177, 371.279296875]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 135, "polygon": [[38.29351535836177, 383.98095703125], [109.62457337883959, 383.98095703125], [109.62457337883959, 394.40283203125], [38.29351535836177, 394.40283203125]]}, {"title": "3.2 Modality-to-Text and Cross-Modality Retrieval", "heading_level": null, "page_id": 136, "polygon": [[52.55972696245733, 411.6640625], [319.86348122866895, 410.361328125], [319.86348122866895, 422.0859375], [52.55972696245733, 423.388671875]]}, {"title": "3.3 Zero/Few-Shot and Cross-Modality Classification", "heading_level": null, "page_id": 137, "polygon": [[38.29351535836177, 452.37451171875], [314.1015625, 452.37451171875], [314.1015625, 464.75048828125006], [38.29351535836177, 464.75048828125006]]}, {"title": "3.4 Multimodal LLM Integration", "heading_level": null, "page_id": 138, "polygon": [[52.55972696245733, 533.79541015625], [227.50853242320818, 533.79541015625], [227.50853242320818, 544.21728515625], [52.55972696245733, 544.21728515625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 139, "polygon": [[38.29351535836177, 237.74902343750003], [124.64163822525596, 237.74902343750003], [124.64163822525596, 251.75341796875003], [38.29351535836177, 251.75341796875003]]}, {"title": "References", "heading_level": null, "page_id": 139, "polygon": [[39.04436860068259, 393.75146484375], [103.5546875, 393.75146484375], [103.5546875, 408.08154296875], [39.04436860068259, 408.08154296875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 39], ["Text", 5], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 27978, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 46], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 41], ["Footnote", 4], ["Reference", 4], ["ListItem", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 45], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 41], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["TableCell", 209], ["Line", 46], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12206, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["TableCell", 306], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 13370, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["TableCell", 208], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Line", 55], ["Span", 52], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 47], ["ListItem", 15], ["Reference", 14], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 45], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 39], ["Text", 5], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 39], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 74], ["Text", 4], ["Equation", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 769], ["TableCell", 548], ["Line", 40], ["Table", 3], ["Caption", 3], ["Reference", 3], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 19201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["TableCell", 120], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7323, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 38], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 40], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 50], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 10], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 40], ["Text", 6], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 80], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 819, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 43], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["Line", 43], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 59], ["TextInlineMath", 3], ["Text", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 634, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 656], ["TableCell", 116], ["Line", 40], ["Caption", 2], ["Table", 2], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 3826, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["TableCell", 192], ["Line", 42], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 39], ["TableCell", 15], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["Line", 31], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 51], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 51], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 43], ["Text", 10], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 70], ["Text", 4], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 850, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 42], ["Text", 4], ["ListItem", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 91], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 703, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 40], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 674, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 39], ["Text", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 426], ["TableCell", 191], ["Line", 51], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3343, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["TableCell", 136], ["Line", 66], ["Text", 5], ["SectionHeader", 3], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8444, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 44], ["ListItem", 7], ["Reference", 7], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 51], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 41], ["Line", 17], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 595, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Line", 105], ["Span", 84], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 46], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 32], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 43], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 43], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["TableCell", 247], ["Line", 54], ["Text", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 13420, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Line", 112], ["Span", 79], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1111, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 41], ["ListItem", 5], ["Reference", 5], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 51], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 30], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 564, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 45], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Line", 82], ["Span", 46], ["ListItem", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 58], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1284, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 54], ["Text", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 702, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 41], ["SectionHeader", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["TableCell", 56], ["Line", 54], ["Text", 3], ["SectionHeader", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 1056, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["TableCell", 84], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7286, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 45], ["ListItem", 12], ["Reference", 11], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 52], ["ListItem", 18], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 6], ["ListItem", 2], ["Reference", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 39], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 46], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 44], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 55], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1308, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 484], ["Line", 39], ["TextInlineMath", 5], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 39], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["TableCell", 100], ["Line", 40], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10124, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 53], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1515, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 41], ["ListItem", 7], ["Reference", 7], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 52], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 37], ["Line", 18], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 42], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 588, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 44], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 43], ["ListItem", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Line", 77], ["Span", 69], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 49], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Line", 109], ["Span", 85], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["TableCell", 56], ["Line", 37], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2724, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["TableCell", 112], ["Line", 42], ["TextInlineMath", 2], ["Caption", 2], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3096, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 44], ["ListItem", 11], ["Reference", 11], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 50], ["ListItem", 17], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 20], ["Line", 8], ["ListItem", 2], ["Reference", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 40], ["Text", 4], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 48], ["ListItem", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 44], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["TableCell", 50], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7352, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["TableCell", 80], ["Line", 49], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["Equation", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5473, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["TableCell", 87], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8411, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["TableCell", 188], ["Line", 39], ["Text", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2874, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Line", 72], ["Span", 70], ["Text", 3], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 46], ["ListItem", 13], ["Reference", 13], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 29], ["ListItem", 8], ["Reference", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 39], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 562, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 46], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 43], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 571], ["Line", 56], ["TableCell", 25], ["TextInlineMath", 3], ["Reference", 3], ["Equation", 2], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 43], ["TextInlineMath", 5], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2249, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 40], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["TableCell", 128], ["Line", 41], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11481, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 39], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 704, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 44], ["Text", 5], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 50], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 36], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 38], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 110, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 46], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 111, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 43], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 112, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 41], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 113, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 45], ["Text", 3], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 114, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 33], ["Reference", 4], ["TextInlineMath", 2], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 115, "text_extraction_method": "pdftext", "block_counts": [["Span", 755], ["TableCell", 175], ["Line", 39], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3854, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 116, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 117, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 43], ["ListItem", 5], ["Reference", 5], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 118, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 52], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 119, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 18], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 120, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 44], ["Text", 10], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1142, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 121, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 46], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 122, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 42], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 123, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 75], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 124, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 40], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 125, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 44], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 126, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 69], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 868, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 127, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 128, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 43], ["Text", 5], ["ListItem", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 129, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 51], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 130, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 29], ["ListItem", 8], ["Reference", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 131, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 36], ["Text", 7], ["Footnote", 3], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 132, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 44], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 133, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 69], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 134, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 70], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 135, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 52], ["Text", 5], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 752, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 136, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["TableCell", 77], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 137, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 81], ["TableCell", 80], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 138, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["TableCell", 119], ["Line", 42], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 9409, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 139, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 42], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 140, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 52], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 141, "text_extraction_method": "pdftext", "block_counts": [["Span", 71], ["Line", 29], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-2"}