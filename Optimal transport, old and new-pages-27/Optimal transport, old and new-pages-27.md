In Chapter 16, a conjecture was formulated about the links between displacement convexity and curvature-dimension bounds; its plausibility was justified by some formal computations based on <PERSON>'s calculus. In the present chapter I shall provide a rigorous justification of this conjecture. For this I shall use a Lagrangian point of view, in contrast with the Eulerian approach used in the previous chapter. Not only is the Lagrangian formalism easier to justify, but it will also lead to new curvature-dimension criteria based on so-called "distorted displacement convexity".

The main results in this chapter are Theorems 17.15 and 17.37.

### Displacement convexity classes

What I shall call displacement convexity class of order  $N$  is a family of convex nonlinearities satisfying a certain characteristic differential inequality of second order (recall (16.16)).

Definition 17.1 (Displacement convexity classes). Let  $N$  be a real parameter in [1,∞]. The class  $DC_N$  is defined as the set of continuous convex functions  $U : \mathbb{R}_+ \to \mathbb{R}$ , twice continuously differentiable on  $(0, +\infty)$ , such that  $U(0) = 0$ , and, with the notation

$$
p(r) = r U'(r) - U(r),
$$
  $p_2(r) = r p'(r) - p(r),$ 

U satisfies any one of the following equivalent differential conditions:

$$
(i)\ p_2+\frac{p}{N}\geq 0;
$$

(ii) 
$$
\frac{p(r)}{r^{1-\frac{1}{N}}}
$$
 is a nondecreasing function of r;
(iii) 
$$
u(\delta) := \begin{cases} \delta^{N} U(\delta^{-N}) & (\delta > 0) & \text{if } N < \infty \\ e^{\delta} U(e^{-\delta}) & (\delta \in \mathbb{R}) & \text{if } N = \infty \end{cases}
$$
 is a convex
function of \delta.

function of  $\delta$ .

**Remark 17.2.** Since U is convex and  $U(0) = 0$ , the function u appearing in (iii) is automatically nonincreasing.

**Remark 17.3.** It is clear (from condition (i) for instance) that  $DC_{N'} \subset$  $\mathcal{DC}_N$  if  $N' \geq N$ . So the smallest class of all is  $\mathcal{DC}_{\infty}$ , while  $\mathcal{DC}_1$  is the largest (actually, conditions (i)–(iii) are void for  $N = 1$ ).

**Remark 17.4.** If U belongs to  $DC_N$ , then for any  $a \geq 0$ ,  $b > 0$ ,  $c \in \mathbb{R}$ , the function  $r \mapsto a U(br) + cr$  also belongs to  $\mathcal{DC}_N$ .

**Remark 17.5.** The requirement for  $U$  to be twice differentiable on  $(0, +\infty)$  could be removed from many subsequent results involving displacement convexity classes. Still, this regularity assumption will simplify the proofs, without significantly restricting the generality of applications.

**Examples 17.6.** (i) For any  $\alpha \geq 1$ , the function  $U(r) = r^{\alpha}$  belongs to all classes  $DC_N$ .

(ii) If  $\alpha < 1$ , then the function  $U(r) = -r^{\alpha}$  belongs to  $\mathcal{DC}_N$  if and only if  $N \leq (1 - \alpha)^{-1}$  (that is,  $\alpha \geq 1 - 1/N$ ). The function  $-r^{1-1/N}$  is in some sense the minimal representative of  $DC_N$ .

(iii) The function  $U_{\infty}(r) = r \log r$  belongs to  $\mathcal{DC}_{\infty}$ . It can be seen as the limit of the functions  $U_N(r) = -N(r^{1-1/N} - r)$ , which are the same (up to multiplication and addition of a linear function) as the functions appearing in (ii) above.

*Proof of the equivalence in Definition 17.1.* Assume first  $N < \infty$ , and write  $r(\delta) = \delta^{-N}$ . By computation,  $u'(\delta) = -Np(r)/r^{1-1/N}$ . So u is convex if and only if  $p(r)/r^{1-1/N}$  is a nonincreasing function of  $\delta$ , i.e. a nondecreasing function of  $r$ . Thus (ii) and (iii) are equivalent.

Next, by computation again,

$$
u''(\delta) = N^2 r^{\frac{2}{N} - 1} \left( p_2(r) + \frac{p(r)}{N} \right).
$$
 (17.1)

So u is convex if and only if  $p_2 + p/N$  is nonnegative. This shows the equivalence between (i) and (iii).

In the case  $N = \infty$ , the arguments are similar, with the formulas

$$
r(\delta) = e^{-\delta},
$$
  $u'(\delta) = -\frac{p(r)}{r},$   $u''(\delta) = \frac{p_2(r)}{r}.$ 

The behavior of functions in  $DC_N$  will play an important role in the sequel of this course. Functions in  $DC_N$  may present singularities at the origin; for example  $U_N(r)$  is not differentiable at  $r = 0$ . It is often possible to get around this problem by replacing  $U_N(r)$  by a smooth approximation which still belongs to  $\mathcal{DC}_N$ , for instance  $-N(r(r+\varepsilon)^{-1/N}-r)$ , and later passing to the limit as  $\varepsilon \to 0$ . The next proposition provides more systematic ways to "regularize" functions in  $\mathcal{DC}_N$  near 0 or  $+\infty$ ; at the same time it gives additional information about the behavior of functions in  $\mathcal{DC}_N$ . The notation  $p(r)$  and  $p_2(r)$  is the same as in Definition 17.1.

## Proposition 17.7 (Behavior of functions in $DC_N$ ).

(i) Let  $N \in [1,\infty)$ , and  $\Psi \in C(\mathbb{R}_+;\mathbb{R}_+)$  such that  $\Psi(r)/r \to +\infty$ as  $r \to \infty$ ; then there exists  $U \in \mathcal{DC}_N$  such that  $0 \leq U \leq \Psi$ , and  $U(r)/r \longrightarrow +\infty \text{ as } r \to \infty.$ 

(ii) If  $U \in \mathcal{DC}_{\infty}$ , then either U is linear, or there exist constants  $a > 0, b \in \mathbb{R}$  such that

$$
\forall r \ge 0, \qquad U(r) \ge a \, r \log r + b \, r.
$$

(iii) Let  $N \in [1,\infty]$  and let  $U \in \mathcal{DC}_N$ . If  $r_0 \in (0,+\infty)$  is such that  $p(r_0) > 0$ , then there is a constant  $K > 0$  such that  $p'(r) \geq Kr^{-1/N}$ for all  $r \ge r_0$ . If on the contrary  $p(r_0) = 0$ , then U is linear on  $[0, r_0]$ . In particular, the set  $\{r; U''(r) = 0\}$  is either empty, or an interval of the form  $[0,r_0]$ .

(iv) Let  $N \in [1,\infty]$  and let  $U \in \mathcal{DC}_N$ . Then U is the pointwise nondecreasing limit of a sequence of functions  $(U_{\ell})_{\ell \in \mathbb{N}}$  in  $\mathcal{DC}_N$ , such that (a)  $U_{\ell}$  coincides with U on  $[0,r_{\ell}]$ , where  $r_{\ell}$  is arbitrarily large; (b) for each  $\ell$  there are  $a \geq 0$  and  $b \in \mathbb{R}$  such that  $U_{\ell}(r) = -a r^{1-\frac{1}{N}} + b r$ (or  $ar \log r + br$  if  $N = \infty$ ) for r large enough; (c)  $U'_{\ell}(\infty) \to U'(\infty)$ as  $\ell \to \infty$ .

(v) Let  $N \in [1,\infty]$  and let  $U \in \mathcal{DC}_N$ . Then U is the pointwise nonincreasing limit of a sequence of functions  $(U_{\ell})_{\ell \in \mathbb{N}}$  in  $\mathcal{DC}_N$ , such that (a)  $U_{\ell}$  coincides with U on  $[r_{\ell}, +\infty)$ , where  $r_{\ell}$  is an arbitrary real number such that  $p'(r_\ell) > 0$ ; (b)  $U_\ell(r)$  is a linear function of r close to the origin; (c)  $(U_{\ell})'(0) \to U'(0)$  as  $\ell \to \infty$ .

(vi) In statements (iv) and (v), one can also impose that  $U''_l \leq C U''$ , for some constant C independent of  $\ell$ . In statement  $(v)$ , one can also impose that  $U''_\ell$  increases nicely from 0, in the following sense: If  $[0,r_0]$ is the interval where  $U_{\ell}'' = 0$ , then there are  $r_1 > r_0$ , an increasing function  $h : [r_0, r_1] \rightarrow \mathbb{R}_+$ , and constants  $K_1, K_2$  such that  $K_1 h \leq$  $U'' \leq K_2 h$  on  $[r_0, r_1].$ 

(vii) Let  $N \in [1,\infty]$  and let  $U \in \mathcal{DC}_N$ . Then there is a sequence  $(U_{\ell})_{\ell \in \mathbb{N}}$  of functions in  $\mathcal{DC}_N$  such that  $U_{\ell} \in C^{\infty}((0, +\infty)), U_{\ell}$  converges to U monotonically and in  $C^2_{\text{loc}}((0, +\infty))$ ; and, with the notation  $p_{\ell}(r) = r U'_{\ell}(r) - U_{\ell}(r),$ 

$$
\inf_{r} \frac{p_{\ell}(r)}{r^{1-\frac{1}{N}}} \xrightarrow[\ell \to \infty]{}
$$
 
$$
\inf_{r} \frac{p(r)}{r^{1-\frac{1}{N}}}; \qquad \sup_{r} \frac{p_{\ell}(r)}{r^{1-\frac{1}{N}}} \xrightarrow[\ell \to \infty]{}
$$
 
$$
\sup_{r} \frac{p(r)}{r^{1-\frac{1}{N}}}.
$$

Here are some comments about these results. Statements (i) and (ii) show that functions in  $DC_N$  can grow as slowly as desired at infinity if  $N < \infty$ , but have to grow at least like r log r if  $N = \infty$ . Statements (iv) to (vi) make it possible to write any  $U \in \mathcal{DC}_N$  as a monotone limit (nonincreasing for small  $r$ , nondecreasing for large  $r$ ) of "very nice" functions  $U_{\ell} \in \mathcal{DC}_N$ , which behave linearly close to 0 and like  $b r - a r^{1-1/N}$  (or  $a r \log r + b r$ ) at infinity (see Figure 17.1). This approximation scheme makes it possible to extend many results which can be proven for very nice nonlinearities, to general nonlinearities in  $DC_N$ .

The proof of Proposition 17.7 is more tricky than one might expect, and it is certainly better to skip it at first reading.

*Proof of Proposition 17.7.* The case  $N = 1$  is not difficult to treat separately (recall that  $\mathcal{DC}_1$  is the class of all convex continuous functions U with  $U(0) = 0$  and  $U \in C^2((0, +\infty))$ . So in the sequel I shall assume  $N > 1$ . The strategy will always be the same: First approximate  $u$ , then reconstruct  $U$  from the approximation, thanks to the formula  $U(r) = r u(r^{-1/N})$   $(r u(\log 1/r))$  if  $N = \infty$ ).

Let us start with the proof of (i). Without loss of generality, we may assume that  $\Psi$  is identically 0 on [0, 1] (otherwise, replace  $\Psi$  by  $\chi \Psi$ , where  $0 \leq \chi \leq 1$  and  $\chi$  is identically 0 on [0, 1], identically 1 on  $[2, +\infty)$ ). Define a function  $u:(0, +\infty) \to \mathbb{R}$  by

Image /page/4/Figure/1 description: The image displays a graph with two curves, labeled U(r) and Uℓ(r). The U(r) curve is a solid line that starts from a low point on the left, rises, and then curves upwards sharply. The Uℓ(r) curve is a dashed line that starts from the same low point on the left and rises linearly. A horizontal line is present at a lower position on the graph, intersecting the U(r) curve. The graph appears to illustrate a comparison between two functions, U(r) and Uℓ(r), possibly in a physics or mathematics context.

Fig. 17.1.  $U_{\ell}$  (dashed line) is an approximation of U (solid line); it is linear close to the origin and almost affine at infinity. This regularization can be made without going out of the class  $DC_N$ , and without increasing too much the second derivative of  $U$ .

$$
u(\delta) = \delta^N \Psi(\delta^{-N}).
$$

Then  $u \equiv 0$  on  $[1, +\infty)$ , and  $\lim_{\delta \to 0^+} u(\delta) = +\infty$ .

The problem now is that u is not necessarily convex. So let  $\tilde{u}$  be the lower convex hull of u on  $(0, \infty)$ , i.e. the supremum of all linear functions bounded above by u. Then  $\tilde{u} \equiv 0$  on  $[1,\infty)$  and  $\tilde{u}$  is nonincreasing. Necessarily,

$$
\lim_{\delta \to 0^+} \tilde{u}(\delta) = +\infty. \tag{17.2}
$$

Indeed, suppose on the contrary that  $\lim_{\delta \to 0^+} \widetilde{u}(\delta) = M < +\infty$ , and let  $a \in \mathbb{R}$  be defined by  $a := \sup_{\delta \geq 0} \frac{M+1-u(\delta)}{\delta}$  (the latter function is nonpositive when  $\delta$  is small enough, so the supremum is finite). Then  $u(\delta) \geq M + 1 - a\delta$ , so  $\lim_{\delta \to 0^+} \tilde{u}(\delta) \geq M + 1$ , which is a contradiction. Thus (17.2) does hold true.

Then let

$$
U(r) := r \,\widetilde{u}(r^{-1/N}).
$$

Clearly U is continuous and nonnegative, with  $U \equiv 0$  on [0, 1]. By computation,  $U''(r) = N^{-2}r^{-1-1/N}(r^{-1/N}\tilde{u}''(r^{-1/N}) - (N-1)\tilde{u}'(r^{-1/N})).$ As  $\tilde{u}$  is convex and nonincreasing, it follows that U is convex. Hence  $U \in \mathcal{DC}_N$ . On the other hand, since  $\widetilde{u} \leq u$  and  $\Psi(r) = ru(r^{-1/N})$ , it is clear that  $U \leq \Psi$ ; and still (17.2) implies that  $U(r)/r$  goes to  $+\infty$  as  $r \to \infty$ .

Now consider Property (ii). If  $N = \infty$ , then the function U can be reconstructed from u by the formula

$$
U(r) = r u(\log(1/r)),
$$
\n(17.3)

As  $u$  is convex and nonincreasing, either  $u$  is constant (in which case  $U$ is linear), or there are constants  $a > 0$ ,  $b \in \mathbb{R}$ , such that  $u(\delta) \ge -a\delta + b$ , and then  $U(r) \ge -a r \log(1/r) + b r = a r \log r + b r$ .

Next let us turn to (iii). First assume  $N < \infty$ . The formula

$$
p(r) = r U'(r) - U(r) = -\frac{1}{N} r^{1-\frac{1}{N}} u'(r^{-\frac{1}{N}})
$$

shows that  $p(r_0) > 0$  if and only if  $u'(r_0^{-1/N}) < 0$ . Then for any  $r \le r_0$ ,  $u'(r^{-1/N}) \leq u'(r_0^{-1/N}),$  so

$$
p'(r) = r U''(r) = \frac{r^{-\frac{1}{N}}}{N^2} \left( r^{-\frac{1}{N}} u''(r^{-\frac{1}{N}}) - (N-1) u'(r^{-\frac{1}{N}}) \right)
$$
  
$$
\geq - \left( \frac{(N-1) u'(r_0^{-\frac{1}{N}})}{N^2} \right) r^{-\frac{1}{N}}.
$$

If on the other hand  $u'(r_0^{-1/N}) = 0$ , then necessarily  $u'(r^{-1/N}) = 0$  for all  $r \leq r_0$ , which means that u is constant on  $[r_0^{-1/N}, +\infty)$ , so U is linear on  $[0, r_0]$ .

The reasoning is the same in the case  $N = \infty$ , with the help of the formulas

$$
p(r) = -r u' \left(\log \frac{1}{r}\right), \qquad U''(r) = \frac{1}{r} \left(u'' \left(\log \frac{1}{r}\right) - u' \left(\log \frac{1}{r}\right)\right)
$$

and

$$
r \ge r_0 \Longrightarrow
$$
  $p'(r) = r U''(r) \ge -u' \Big(\log \frac{1}{r_0}\Big).$ 

Now consider statement (iv). The idea is to replace  $u$  by an affine function close to the origin, essentially by smoothing of the trivial  $C<sup>1</sup>$ approximation by the tangent. First let  $N \in [1,\infty)$ , let  $U \in \mathcal{DC}_N$  and let  $u(\delta) = \delta^N U(\delta^{-N})$ . We know that u is a nonincreasing, twice differentiable convex function on  $(0, +\infty)$ . If u is linear close to the origin, there is nothing to prove. Otherwise there is a sequence of positive numbers  $(a_{\ell})_{\ell \in \mathbb{N}}$  such that  $a_{\ell+1} \le a_{\ell}/4$  and  $u'(a_{\ell+1}) < u'(a_{\ell}/2) < 0$ . For each  $\ell$ , construct a  $C^2$  function  $u_{\ell}$  as follows:

- on  $[a_{\ell}, +\infty), u_{\ell}$  coincides with u;
- on  $[0, a_{\ell}], u''_{\ell} = \chi_{\ell} u''$ , where  $\chi_{\ell}$  is a smooth cutoff function such that  $0 \leq \chi_{\ell} \leq 1$ ,  $\chi_{\ell}(a_{\ell}) = 1$ ,  $\chi_{\ell}(\delta) = 0$  for  $\delta \leq a_{\ell}/2$ .

Since  $u_{\ell}$  is convex and  $u'_{\ell}(a_{\ell}) < 0$ , also  $u'_{\ell} < 0$  on  $(0, a_{\ell}]$ . By construction,  $u_{\ell}$  is linear on [0,  $a_{\ell}/2$ ]. Also  $u''_{\ell} \leq u''$ ,  $u'_{\ell}(a_{\ell}) = u'(a_{\ell})$ ,  $u_{\ell}(a_{\ell}) = u(a_{\ell});$  by writing the Taylor formula on  $[s, a_{\ell}]$  (with  $1/\ell$  as base point), we deduce that  $u_{\ell}(s) \leq u(s)$ ,  $u'_{\ell}(s) \geq u'(s)$  for all  $s \leq a_{\ell}$ (and therefore for all s).

For each  $\ell$ ,  $u_{\ell}$  lies above the tangent to u at  $1/\ell$ ; that is

$$
u_{\ell}(s) \ge u(a_{\ell}) + (s - a_{\ell}) u'(a_{\ell}) =: T_{\ell}(s).
$$

Since u' is nondecreasing and  $u'(a_{\ell+1}) < u'(a_{\ell}/2)$ , the curve  $T_{\ell}$  lies strictly below the curve  $T_{\ell+1}$  on  $[0, a_{\ell}/2]$ , and therefore on  $[0, a_{\ell+1}]$ . By choosing  $\chi_{\ell}$  in such a way that  $\int_{a_{\ell}/2}^{a_{\ell}} \chi_{\ell} u''$  is very small, we can make sure that  $u_{\ell}$  is very close to the line  $T_{\ell}(s)$  on  $[0, a_{\ell}]$ ; and in particular that the whole curve  $u_{\ell}$  is bounded above by  $T_{\ell+1}$  on  $[a_{\ell+1}, a_{\ell}]$ . This will ensure that  $u_{\ell}$  is a nondecreasing function of  $\ell$ .

To recapitulate:  $u_{\ell} \leq u$ ;  $u_{\ell+1} \leq u_{\ell}$ ;  $u_{\ell} = u$  on  $[a_{\ell}, +\infty)$ ;  $0 \leq u''_{\ell} \leq u''$ ;  $0 \ge u'_{\ell} \ge u'$ ;  $u_{\ell}$  is affine on  $[0, a_{\ell}/2]$ .

Now let

$$
U_{\ell}(r) = r u_{\ell}(r^{-1/N}).
$$

By direct computation,

$$
U''_{\ell}(r) = \frac{r^{-1-\frac{1}{N}}}{N^2} \left( r^{-\frac{1}{N}} u''_{\ell}(r^{-\frac{1}{N}}) - (N-1) u'_{\ell}(r^{-\frac{1}{N}}) \right).
$$
 (17.4)

Since  $u_{\ell}$  is convex nonincreasing, the above expression is nonnegative; so  $U_{\ell}$  is convex, and by construction, it lies in  $\mathcal{DC}_N$ . Moreover  $U_{\ell}$  satisfies the first requirement in (vi), since  $U''_{\ell}(r)$  is bounded above by  $(r^{-1-1/N}/N^2) (r^{-1/N}u''(r^{-1/N}) - (N-1) u'(r^{-1/N})) = U''(r).$ 

In the case  $N = \infty$ , things are similar, except that now u is defined on the whole of R, the sequence  $a_{\ell}$  converges to  $-\infty$  (say  $a_{\ell+1} \leq 2a_{\ell}$ ), and one should use the formulas

$$
U_{\ell}(r) = r u_{\ell}(\log 1/r); \qquad U_{\ell}''(r) = \frac{1}{r} \left( u_{\ell}''\left(\log \frac{1}{r}\right) - u_{\ell}'\left(\log \frac{1}{r}\right) \right).
$$

For  $(v)$ , the idea is to replace u by a *constant* function for large values of  $\delta$ . But this cannot be done in a  $C^1$  way, so the smoothing turns out to be more tricky. (Please consider again possibly skipping the rest of this proof.)

I shall distinguish four cases, according to the behavior of  $u$  at infinity, and the value of  $u'(+\infty) = \lim_{s \to +\infty} u'(s)$ . To fix ideas I shall assume that  $N < \infty$ ; but the case  $N = \infty$  can be treated similarly. In each case I shall also check the first requirement of (vi), which is  $U''_{\ell} \leq C U''$ .

**Case 1:** *u* is affine at infinity and  $u'(+\infty) = 0$ . This means that  $u(\delta) = c$  for  $\delta \geq \delta_0$  large enough, where c is some constant. Then  $U(r) = r u(r^{-1/N}) = cr$  for  $r \leq \delta_0^{-N}$ , and there is nothing to prove.

**Case 2:** *u* is affine at infinity and  $u'(+\infty) < 0$ . Let  $a := -u'(+\infty)$ , so  $u' \leq -a$ . By assumption there are  $\delta_0 > 0$  and  $b \in \mathbb{R}$  such that  $u(s) = -as + b$  for  $s \ge \delta_0$ . Let  $a_1 \ge \max(1, \delta_0)$ . I shall define recursively an increasing sequence  $(a_{\ell})_{\ell \in \mathbb{N}}$ , and  $C^2$  functions  $u_{\ell}$  such that:

- on  $[0, a_{\ell}], u_{\ell}$  coincides with u;
- on  $[a_{\ell}, +\infty), u''_{\ell}(s) = \chi_{\ell}(s)/s$ , where  $\chi_{\ell}$  is a continuous function with compact support in  $(a_{\ell}, +\infty)$ ,  $0 \leq \chi_{\ell} \leq 1$ . (So  $u_{\ell}$  is obtained by integrating this twice, and ensuring the  $C^1$  continuity at  $s = \ell$ ; note that  $u''(a_\ell) = 0$ , so the result will be  $C^2$ .)

Let us choose  $\chi_{\ell}$  to be supported in some interval  $(a_{\ell}, b_{\ell})$ , such that

$$
\int_{a_{\ell}}^{+\infty} \frac{\chi_{\ell}(s)}{s} ds = a.
$$

Such a  $\chi_{\ell}$  exists since  $\int_{a_{\ell}}^{+\infty} ds/s = +\infty$ . Then we let  $a_{\ell+1} \ge b_{\ell} + 1$ .

The function  $u_{\ell}$  is convex by construction, and affine at infinity; moreover,  $u'_{\ell}(+\infty) = u'(a_{\ell+1}) = u'(a_{\ell}) + \int_{a_{\ell}}^{+\infty} \chi_{\ell}(s) ds/s = 0$ , so  $u_{\ell}$  is actually constant at infinity and  $u'_{\ell} \leq 0$ . Obviously  $u''_{\ell} \geq u''$ , so  $u'_{\ell} \geq u'$ and  $u_{\ell} \geq u$ . Also, on  $[a_{\ell+1}, +\infty), u_{\ell+1} \leq u(a_{\ell+1}) \leq u_{\ell}(a_{\ell+1}) \leq u_{\ell},$ 

while on  $[0, a_{\ell+1}], u_{\ell+1} = u \leq u_{\ell}$ ; so the sequence  $(u_{\ell})$  is nonincreasing in  $\ell$ .

Let  $U_{\ell}(r) = r u_{\ell}(r^{-1/N})$ . Formula (17.4) shows again that  $U_{\ell}^{"'} \geq 0$ , and it is clear that  $U_{\ell}(0) = 0, U_{\ell} \in C(\mathbb{R}_{+}) \cap C^{2}((0, +\infty))$ ; so  $U_{\ell} \in \mathcal{DC}_{N}$ . It is clear also that  $U_{\ell} \geq U$ ,  $U_{\ell}$  coincides with U on  $[0, a_{\ell}^{-N}]$ ,  $U_{\ell}$  is linear on  $[0, b_{\ell}^{-N}]$ ,  $U_{\ell}$  converges monotonically to U as  $\ell \to \infty$ , and  $U'_{\ell}(0) = u_{\ell}(+\infty)$  converges to  $u(+\infty) = U'(0) = -\infty$ .

It only remains to check the bound  $U''_{\ell} \leq C U''$ . This bound is obvious on  $[a_{\ell}^{-N}, +\infty)$ ; for  $r \leq a_{\ell}^{-N}$  it results from the formulas

$$
U''_{\ell}(r) = \frac{r^{-1-\frac{1}{N}}}{N^2} \left( r^{-\frac{1}{N}} \chi_{\ell}(r^{-\frac{1}{N}}) r^{\frac{1}{N}} - (N-1) u'_{\ell}(r) \right)
$$
  
$$
\leq \frac{r^{-1-\frac{1}{N}}}{N^2} \left( 1 + (N-1)a \right);
$$
  
$$
U''(r) = \left( \frac{N-1}{N^2} \right) a r^{-1-\frac{1}{N}}.
$$

So  $C = 1 + 1/((N - 1)a)$  is admissible.

**Case 3:** *u* is not affine at infinity and  $u'(+\infty) = 0$ . The proof is based again on the same principle, but modified as follows:

- on  $[0, a_{\ell}], u_{\ell}$  coincides with u;
- on  $[a_{\ell}, +\infty), u''_{\ell}(s) = \zeta_{\ell}(s) u''(s)$ , where  $\zeta_{\ell}$  is a smooth function identically equal to 1 close to  $a_{\ell}$ , identically equal to 0 at infinity, with values in  $[0, 2]$ .

Choose  $a_{\ell} < b_{\ell} < c_{\ell}$ , and  $\zeta_{\ell}$  supported in  $[a_{\ell}, c_{\ell}]$ , so that  $1 \leq \zeta_{\ell} \leq 2$ on  $[a_{\ell}, b_{\ell}], 0 \leq \zeta_{\ell} \leq 2$  on  $[b_{\ell}, c_{\ell}],$  and

$$
\int_{a_{\ell}}^{b_{\ell}} \zeta_{\ell}(s) u''(s) ds > u'(b_{\ell}) - u'(a_{\ell}); \qquad \int_{a_{\ell}}^{c_{\ell}} \zeta_{\ell}(s) u''(s) ds = -u'(a_{\ell}).
$$

This is possible since u' and u'' are continuous and  $\int_{a_\ell}^{+\infty} (2u''(s)) ds =$  $2(u'(+\infty)-u'(a_{\ell})) > u'(+\infty)-u'(a_{\ell}) > 0$  (otherwise u would be affine on  $[a_{\ell}, +\infty)$ ). Then choose  $a_{\ell+1} > c_{\ell} + 1$ .

The resulting function  $u_{\ell}$  is convex and it satisfies  $u'_{\ell}(+\infty)$  =  $u'(a_{\ell}) - u'(a_{\ell}) = 0$ , so  $u'_{\ell} \leq 0$  and  $u_{\ell}$  is constant at infinity.

On  $[a_{\ell}, b_{\ell}], u''_{\ell} \geq u''$ , so  $u'_{\ell} \geq u'$  and  $u_{\ell} \geq u$ , and these inequalities are strict at  $b_{\ell}$ . Since u' and u'' are continuous, we can always arrange that  $b_{\ell}$  is so close to  $c_{\ell}$  that the inequalities  $u_{\ell} \geq u$  and  $u'_{\ell} \geq u'$  hold

true on  $[b_{\ell}, c_{\ell}]$ . Then these inequalities will also hold true on  $[c_{\ell}, +\infty)$ since  $u_{\ell}$  is constant there, and  $u$  is nonincreasing.

Define  $U_{\ell}(r) = r u_{\ell}(r^{-1/N})$ . The same reasoning as in the previous case shows that  $U_{\ell}$  lies in  $\mathcal{DC}_N$ ,  $U_{\ell} \geq U$ ,  $U_{\ell}$  is linear on  $[0, c_{\ell}^{-N}]$ ,  $U_{\ell}$ converges monotonically to U as  $\ell \to \infty$ , and  $U'_{\ell}(0) = u_{\ell}(+\infty)$  converges to  $u(+\infty) = U'(0)$ . The sequence  $(U_{\ell})$  satisfies all the desired properties; in particular the inequalities  $u''_{\ell} \leq 2u''$  and  $u'_{\ell} \geq u'$  ensure that  $U_{\ell}^{\prime\prime} \leq 2U^{\prime\prime}$ .

**Case 4:** *u* is not affine at infinity and  $u'(+\infty) < 0$ . In this case the proof is based on the same principle, and  $u_{\ell}$  is defined as follows:

- on  $[0, a_{\ell}], u_{\ell}$  coincides with u;
- on  $[a_{\ell}, +\infty), u''_{\ell}(s) = \eta_{\ell}(s) u''(s) + \chi_{\ell}(s)/s$ , where  $\chi_{\ell}$  and  $\eta_{\ell}$  are both valued in  $[0,1]$ ,  $\chi_{\ell}$  is a smooth cutoff function with compact support in  $(a_{\ell}, +\infty)$ , and  $\eta_{\ell}$  is a smooth function identically equal to 1 close to  $a_{\ell}$ , and identically equal to 0 close to infinity.

To construct these functions, first choose  $b_{\ell} > a_{\ell}$  and  $\chi_{\ell}$  supported in  $[a_{\ell}, b_{\ell}]$  in such a way that

$$
\int_{a_{\ell}}^{b_{\ell}} \frac{\chi_{\ell}(s)}{s} ds = -\left(\frac{u'(b_{\ell}) + u'(+\infty)}{2}\right).
$$

This is always possible since  $\int_{a_\ell}^{+\infty} ds/s = +\infty$ , u' is continuous and  $-(u'(b_{\ell})+u'(+\infty))/2$  approaches the finite limit  $-u'(+\infty)$  as  $b_{\ell} \to +\infty$ .

Then choose  $c_{\ell} > b_{\ell}$ , and  $\eta_{\ell}$  supported in  $[a_{\ell}, c_{\ell}]$  such that  $\eta_{\ell} = 1$ on  $[a_{\ell}, b_{\ell}]$  and

$$
\int_{b_{\ell}}^{c_{\ell}} \eta_{\ell} u'' = \frac{u'(+\infty) - u'(b_{\ell})}{2}.
$$

This is always possible since  $\int_{b_\ell}^{+\infty} u''(s) ds = u'(+\infty) - u'(b_\ell) >$  $[u'(+\infty) - u'(b_{\ell})]/2 > 0$  (otherwise u would be affine on  $[b_{\ell}, +\infty)$ ). Finally choose  $a_{\ell+1} \geq c_{\ell} + 1$ .

The function  $u_{\ell}$  so constructed is convex, affine at infinity, and

$$
u'_{\ell}(+\infty) = u'(a_{\ell}) + \int_{a_{\ell}}^{b_{\ell}} u'' + \int_{a_{\ell}}^{b_{\ell}} \frac{\chi_{\ell}(s)}{s} ds + \int_{b_{\ell}}^{c_{\ell}} \eta_{\ell} u'' = 0.
$$

So  $u_{\ell}$  is actually constant at infinity, and  $u'_{\ell} \leq 0$ .

On  $[a_{\ell}, b_{\ell}], u''_{\ell} \ge u''$ ,  $u'_{\ell}(a_{\ell}) = u'(a_{\ell}), u_{\ell}(a_{\ell}) = u(a_{\ell});$  so  $u'_{\ell} \ge u'$  and  $u_{\ell} \geq u$  on  $[a_{\ell}, b_{\ell}].$ 

On  $[b_{\ell}, +\infty)$ , one has  $u'_{\ell} \ge u'_{\ell}(b_{\ell}) = (u'(b_{\ell}) - u'(+\infty))/2 \ge u'(+\infty)$ if  $u'(b_{\ell}) \geq 3u'(+\infty)$ . We can always ensure that this inequality holds true by choosing  $a_1$  large enough that  $u'(a_1) \ge 3u'(+\infty)$ . Then  $u_\ell(s) \ge$  $u_{\ell}(b_{\ell}) + u'(\alpha)$   $(s - b_{\ell}) \geq u_{\ell}(b_{\ell}) + \int_{b_{\ell}}^{s} u' = u(s)$ ; so  $u_{\ell} \geq u$  also on  $[b_{\ell}, +\infty).$ 

Define  $U_{\ell}(r) = ru_{\ell}(r^{-1/N})$ . All the desired properties of  $U_{\ell}$  can be shown just as before, except for the bound on  $U''_{\ell}$ , which we shall now check. On  $[a_{\ell}^{-N}, +\infty), U_{\ell}'' = U''$ . On  $[0, a_{\ell}^{-N}),$  with the notation  $a = -u'(+\infty)$ , we have  $u'_{\ell}(r^{-1/N}) \ge -a$ ,  $u'(r^{-1/N}) \ge -3a$  (recall that we imposed  $u'(a_1) \geq -3a$ , so

$$
U''_{\ell}(r) \le \frac{r^{-1-\frac{1}{N}}}{N^2} \left( r^{-\frac{1}{N}} u''(r^{-\frac{1}{N}}) + 1 + 3(N-1)a \right),
$$
  
$$
U''(r) \ge \frac{r^{-1-\frac{1}{N}}}{N^2} \left( r^{-\frac{1}{N}} u''(r^{-\frac{1}{N}}) + (N-1)a \right),
$$

and once again  $U''_{\ell} \leq C U''$  with  $C = 3 + 1/((N - 1)a)$ .

It remains to prove the second part of (vi). This will be done by a further approximation scheme. So let  $U \in \mathcal{DC}_N$  be linear close to the origin. (We can always reduce to this case by  $(v)$ .) If U is linear on the whole of  $\mathbb{R}_+$ , there is nothing to do. Otherwise, by (iii), the set where  $U''$  vanishes is an interval  $[0, r_0]$ . The goal is to show that we may approximate U by  $U_{\ell}$  in such a way that  $U_{\ell} \in \mathcal{DC}_N$ ,  $U_{\ell}$  is nonincreasing in  $\ell$ ,  $U_{\ell}$  is linear on some interval  $[0, r_0(\ell)]$ , and  $U''_{\ell}$  increases nicely from 0 on  $[r_0(\ell), r_1(\ell))$ .

In this case,  $u$  is a nonincreasing function, identically equal to a constant on  $[s_0, +\infty)$ , with  $s_0 = r_0^{-1/N}$ ; and also u' is nonincreasing to 0, so in fact u is strictly decreasing up to  $s_0$ . Let  $a_1 \in (s_0/2, s_0)$ . We can recursively define real numbers  $a_{\ell}$  and  $C^2$  functions  $u_{\ell}$  as follows:

- on  $(0, a_{\ell}], u_{\ell}$  coincides with u;
- on  $[a_{\ell}, +\infty), (u_{\ell})'' = \chi_{\ell} u'' + \eta_{\ell}(-u'),$  where  $\chi_{\ell}$  and  $\eta_{\ell}$  are smooth functions valued in [0, 2],  $\chi_{\ell}(r)$  is identically equal to 1 for r close to  $a_{\ell}$ , and identically equal to 0 for  $r \geq b_{\ell}$ ; and  $\eta_{\ell}$  is compactly supported in  $[b_{\ell}, c_{\ell}]$  and decreasing to 0 close to  $c_{\ell}; a_{\ell} < b_{\ell} < c_{\ell} < s_0$ .

Let us choose  $\chi_{\ell}, \eta_{\ell}, b_{\ell}, c_{\ell}$  in such a way that

$$
\int_{a_{\ell}}^{b_{\ell}} \chi_{\ell} u'' > \int_{a_{\ell}}^{b_{\ell}} u''; \qquad \int_{a_{\ell}}^{b_{\ell}} \chi_{\ell} u'' + \int_{b_{\ell}}^{c_{\ell}} \eta_{\ell}(-u') = -u'_{\ell}(a_{\ell});
$$
$$
\int_{b_{\ell}}^{c_{\ell}} \eta_{\ell}(-u') > 0.
$$

This is possible since  $u', u''$  are continuous,  $\int_{a_\ell}^{s_0} (2u'') = -2u'_\ell(a_\ell) >$  $-u'_{\ell}(a_{\ell}),$  and  $(-u')$  is strictly positive on  $[a_{\ell}, s_0]$ . It is clear that  $u_{\ell} \geq u$ and  $u'_{\ell} \geq u'$  on  $[a_{\ell}, b_{\ell}]$ , with strict inequalities at  $b_{\ell}$ ; by choosing  $c_{\ell}$  very close to  $b_{\ell}$ , we can make sure that these inequalities are preserved on  $[b_{\ell}, c_{\ell}]$ . Then we choose  $a_{\ell+1} = (c_{\ell} + s_0)/2$ .

Let us check that  $U_{\ell}(r) := ru_{\ell}(r^{-1/N})$  satisfies all the required properties. To bound  $U''_{\ell}$ , note that for  $r \in [s_0^{-N}, (s_0/2)^{-N}],$ 

$$
U''_{\ell}(r) \leq C(N, r_0) \left( u''_{\ell}(r^{-1/N}) - u'_{\ell}(r^{-1/N}) \right) \leq 2C(N, r_0) \left( u''(r^{-1/N}) - u'(r^{-1/N}) \right)
$$

and

$$
U''(r) \ge K(N,r_0) \left( u''(r^{-1/N}) - u'(r^{-1/N}) \right),
$$

where  $C(N,r_0)$ ,  $K(N,r_0)$  are positive constants. Finally, on  $[b_{\ell}, c_{\ell}],$  $u''_{\ell} = \eta_{\ell}(-u')$  is decreasing close to  $c_{\ell}$  (indeed,  $\eta_{\ell}$  is decreasing close to  $c_{\ell}$ , and  $-u'$  is positive nonincreasing); and of course  $-u'_{\ell}$  is decreasing as well. So  $u''_{\ell}(r^{-1/N})$  and  $-u'_{\ell}(r^{-1/N})$  are increasing functions of r in a small interval  $[r_0, r_1]$ . This concludes the argument.

To prove (vi), we may first approximate u by a  $C^{\infty}$  convex, nonincreasing function  $u_{\ell}$ , in such a way that  $||u - u_{\ell}||_{C^{2}((a,b))} \to 0$  for any  $a,b > 0$ . This can be done in such a way that  $u_{\ell}(s)$  is nondecreasing for small s and nonincreasing for large s; and  $u'_{\ell}(0) \to u'(0)$ ,  $u'_{\ell}(\infty) \to u'(\infty)$ . The conclusion follows easily since  $p(r)/r^{1-1/N}$  is nondecreasing and equal to  $-(1/N)u'(r^{-1/N})$   $(-u'(\log 1/r))$  in the case  $N = \infty$ ).

## Domain of the functionals $U_{\nu}$

To each  $U \in \mathcal{DC}_N$  corresponds a functional  $U_{\nu}$ . However, some conditions might be needed to make sense of  $U_{\nu}(\mu)$ . Why is that so? If U is, say, nonnegative, then an integral such as  $\int U(\rho) d\nu$  always makes

sense in  $[0, +\infty]$ , so  $U_{\nu}$  is well-defined on the whole of  $P_2^{\text{ac}}(M)$ . But U might be partially negative, and then one should not exclude the possibility that both the negative and the positive parts of  $U(\rho)$  have infinite integrals. The problem comes from infinity and does not arise if M is a compact manifold, or more generally if  $\nu$  has finite mass.

Theorem 17.8 below solves this issue: It shows that under some integral growth condition on  $\nu$ , the quantity  $U_{\nu}(\mu)$  is well-defined if  $\mu$  has finite moments of order  $p$  large enough. This suggests that we study  $U_{\nu}$  on the set  $P_p^{\text{ac}}(M)$  of absolutely continuous measures with finite moment of order  $p$ , rather than on the whole space  $P_2^{\text{ac}}(M)$ .

Since this theorem only uses the metric structure, I shall state it in the context of general Polish spaces rather than Riemannian manifolds.

Theorem 17.8 (Moment conditions make sense of  $U_{\nu}(\mu)$ ). Let  $(X, d)$  be a Polish space and let  $\nu$  be a reference Borel measure on X. Let  $N \in [1,\infty]$ . Assume that there exists  $x_0 \in \mathcal{X}$  and  $p \in [2,+\infty)$  such that

$$
\begin{cases} \int_{\mathcal{X}} \frac{d\nu(x)}{[1+d(x_0,x)]^{p(N-1)}} < +\infty & \text{if } N < \infty, \\ \exists c > 0; \quad \int_M e^{-c \, d(x_0,x)^p} \, d\nu(x) < +\infty & \text{if } N = \infty. \end{cases} \tag{17.5}
$$

Then, for any  $U \in \mathcal{DC}_N$ , the formula

$$
U_{\nu}(\mu) = \int_{\mathcal{X}} U(\rho) d\nu, \qquad \mu = \rho \nu
$$

unambiguously defines a functional  $U_{\nu} : P_p^{\text{ac}}(\mathcal{X}) \to \mathbb{R} \cup \{+\infty\}$ , where  $P_p^{\text{ac}}(\mathcal{X})$  is the set of absolutely continuous probability measures on X with a finite moment of order p.

Even if no such p exists,  $U_{\nu}$  is still well-defined on  $P_c^{\text{ac}}(\mathcal{X})$ , the set of absolutely continuous compactly supported probability measures, provided that  $\nu$  is finite on compact sets.

**Example 17.9.** If  $\nu$  is the Lebesgue measure on  $\mathbb{R}^N$ , then  $U_{\nu}$  is welldefined on  $P_2^{\text{ac}}(\mathbb{R}^N)$  for all  $U \in \mathcal{DC}_N$ , as long as  $N \geq 3$ . For  $N = 2$ , Theorem 17.8 allows us to define  $U_{\nu}$  on  $P_p^{\text{ac}}(\mathbb{R}^N)$ , for any  $p > 2$ . In the case  $N = 1$ ,  $U_{\nu}$  is well-defined on  $P_c^{\text{ac}}(\mathbb{R}^N)$ . All this remains true if  $\mathbb{R}^N$  is replaced by an arbitrary N-dimensional Riemannian manifold with nonnegative Ricci curvature. (Indeed,  $vol[B_r(x_0)] = O(r^N)$  for any fixed  $x_0 \in M$ , so  $\int d\nu(x) / [1 + d(x_0, x)]^{p(N-1)} < +\infty$  if  $p(N-1) > N$ .)

Convention 17.10. In the sequel of this course I shall sometimes write " $p \in [2, +\infty) \cup \{c\}$  satisfying the assumptions of Theorem 17.8" or " $p \in [2, +\infty) \cup \{c\}$  satisfying (17.5)". This means that p is either a real number greater or equal than 2, satisfying (17.5) (the metric space  $(\mathcal{X}, d)$  and the reference measure  $\nu$  should be obvious from the context); or the symbol "c", so that  $P_p(\mathcal{X})$  stands for the set  $P_c(\mathcal{X})$  of compactly supported probability measures.

**Remark 17.11.** For any positive constant  $C$ , the set of probability measures  $\mu$  in  $P_p(\mathcal{X})$  with  $\int d(x_0, x)^p d\mu(x) \leq C$  is closed in  $P_2(\mathcal{X})$ ; but in general the whole set  $P_p(\mathcal{X})$  is not. Similarly, if K is a given compact subset of  $\mathcal X$ , then the set of probability measures with support in K is compact in  $P_2(\mathcal{X})$ ; but  $P_c(\mathcal{X})$  is not closed in general.

**Remark 17.12.** If  $\mathcal{X}$  is a length space (for instance a Riemannian manifold equipped with its geodesic distance), then  $P_p(M)$  is a geodesically convex subset of  $P_q(M)$ , for any  $q \in (1, +\infty)$ . Indeed, let  $(\mu_t)_{0 \le t \le 1}$  be a geodesic in  $P_q(M)$ ; according to Corollary 7.22, there is a random geodesic  $\gamma$  such that  $\mu_t = \text{law}(\gamma_t)$ ; then the bounds  $\mathbb{E} d(x_0, \gamma_0)^p < +\infty$ and  $\mathbb{E} d(x_0, \gamma_1)^p < +\infty$  together imply  $\mathbb{E} d(x_0, \gamma_t)^p < +\infty$ , in view of the inequality

$$
0 \le t \le 1 \Longrightarrow d(x_0, \gamma_t)^p \le 2^{2p-1} \big[ d(x_0, \gamma_0)^p + d(x_0, \gamma_1)^p \big].
$$

Combining this with Theorem 8.7, we deduce that  $P_p^{\text{ac}}(M)$  is geodesically convex in  $P_2(M)$ , and more precisely

$$
\int d(x_0, x)^p \,\mu_t(dx) \leq 2^{2p-1} \left( \int d(x_0, x)^p \,\mu_0(dx) + \int d(x_0, x)^p \,\mu_1(dx) \right).
$$

Thus even if the functional  $U_{\nu}$  is a priori only defined on  $P_p^{\text{ac}}(M)$ , it is not absurd to study its convexity properties along geodesics of  $P_2(M)$ .

*Proof of Theorem 17.8.* The problem is to show that under the assumptions of the theorem,  $U(\rho)$  is bounded below by a  $\nu$ -integrable function; then  $U_{\nu}(\mu) = \int U(\rho) d\nu$  will be well-defined in  $\mathbb{R} \cup \{+\infty\}.$ 

Suppose first that  $N < \infty$ . By convexity of u, there is a constant  $A > 0$  so that  $\delta^N U(\delta^{-N}) \ge -A\delta - A$ , which means

$$
U(\rho) \ge -A(\rho + \rho^{1 - \frac{1}{N}}). \tag{17.6}
$$

Of course,  $\rho$  lies in  $L^1(\nu)$ ; so it is sufficient to show that also  $\rho^{1-1/N}$ lies in  $L^1(\nu)$ . But this is a simple consequence of Hölder's inequality:

$$
\int_{\mathcal{X}} \rho(x)^{1-\frac{1}{N}} d\nu(x)
$$

$$
= \int_{\mathcal{X}} \left( (1 + d(x_0, x)^p) \rho(x) \right)^{1-\frac{1}{N}} (1 + d(x_0, x)^p)^{-1+\frac{1}{N}} d\nu(x)
$$

$$
\leq \left( \int_{\mathcal{X}} (1 + d(x_0, x)^p) \rho(x) d\nu(x) \right)^{1-\frac{1}{N}} \left( \int_{\mathcal{X}} (1 + d(x_0, x)^p)^{-(N-1)} d\nu(x) \right)^{\frac{1}{N}}.
$$

Now suppose that  $N = \infty$ . By Proposition 17.7(ii), there are positive constants  $a, b$  such that

$$
U(\rho) \ge a \rho \log \rho - b \rho. \tag{17.7}
$$

So it is sufficient to show that  $(\rho \log \rho)$ <sub>-</sub> ∈  $L^1(\nu)$ . Write

$$
\int_{\mathcal{X}} \rho(x) \log \rho(x) d\nu(x)
$$

$$
= \int_{\mathcal{X}} \rho(x) e^{c d(x_0, x)^p} \log \left( \rho(x) e^{c d(x_0, x)^p} \right) e^{-c d(x_0, x)^p} d\nu(x)
$$

$$
- c \int_{\mathcal{X}} d(x_0, x)^p \rho(x) d\nu(x). \quad (17.8)
$$

By Jensen's inequality, applied with the convex function  $r \to r \log r$ , the probability measure  $\frac{e^{-c d(x_0, \cdot)^p} d\nu}{\int_{\mathcal{X}} e^{-c d(x_0, \cdot)^p} d\nu}$  and the integrable function  $\rho e^{c d(x_0, \cdot)^p}$ , (17.8) can be bounded below by

$$
\left(\int_{\mathcal{X}} e^{-c d(x_0,x)^p} d\nu(x)\right) \left(\frac{\int_{\mathcal{X}} \rho d\nu}{\int_{\mathcal{X}} e^{-c d(x_0,x)^p} d\nu(x)}\right) \log\left(\frac{\int_{\mathcal{X}} \rho d\nu}{\int_{\mathcal{X}} e^{-c d(x_0,x)^p} d\nu(x)}\right) - c \int_{\mathcal{X}} d(x_0,x)^p \rho(x) d\nu(x).
$$

This concludes the argument. □

In the sequel of this chapter, I shall study properties of the functionals  $U_{\nu}$  on  $P_p^{\text{ac}}(M)$ , where M is a Riemannian manifold equipped with its geodesic distance.