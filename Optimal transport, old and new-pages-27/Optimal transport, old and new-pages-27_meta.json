{"table_of_contents": [{"title": "Displacement convexity II", "heading_level": null, "page_id": 0, "polygon": [[133.5, 98.2265625], [320.25, 98.2265625], [320.25, 111.76171875], [133.5, 111.76171875]]}, {"title": "Displacement convexity classes", "heading_level": null, "page_id": 0, "polygon": [[133.5, 393.75], [316.7578125, 393.75], [316.7578125, 405.28125], [133.5, 405.28125]]}, {"title": "Proposition 17.7 (Behavior of functions in DC_N).", "heading_level": null, "page_id": 2, "polygon": [[132.978515625, 290.25], [399.75, 290.25], [399.75, 301.5], [132.978515625, 301.5]]}, {"title": "468 17 Displacement convexity II", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.103515625], [285.75, 26.103515625], [285.75, 35.384765625], [133.5, 35.384765625]]}, {"title": "470 17 Displacement convexity II", "heading_level": null, "page_id": 7, "polygon": [[133.5, 26.25], [285.978515625, 26.25], [285.978515625, 35.578125], [133.5, 35.578125]]}, {"title": "472 17 Displacement convexity II", "heading_level": null, "page_id": 9, "polygon": [[133.5, 25.95849609375], [285.75, 25.95849609375], [285.75, 35.33642578125], [133.5, 35.33642578125]]}, {"title": "Domain of the functionals U_{\\nu}", "heading_level": null, "page_id": 11, "polygon": [[133.27734375, 522.0], [308.25, 522.0], [308.25, 533.671875], [133.27734375, 533.671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 28], ["Text", 4], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2002, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 563], ["Line", 52], ["TextInlineMath", 7], ["Text", 4], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 754], ["Line", 44], ["TextInlineMath", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 717], ["Line", 63], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 25], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 727], ["Line", 95], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1049, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 958], ["Line", 60], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 806], ["Line", 56], ["TextInlineMath", 7], ["Equation", 2], ["ListItem", 2], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 944], ["Line", 75], ["TextInlineMath", 9], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1133, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 904], ["Line", 72], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 907], ["Line", 62], ["TextInlineMath", 5], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 820], ["Line", 85], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1048, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 571], ["Line", 46], ["TextInlineMath", 6], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1110, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 730], ["Line", 58], ["TextInlineMath", 8], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 101], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4213, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-27"}