examples are discussed in more detail in <PERSON><PERSON> et al. (1995), who also show that the regularization improves the classification performance of LDA on independent test data by a factor of around 25% in the cases they tried.

## 12.7 Mixture Discriminant Analysis

Linear discriminant analysis can be viewed as a prototype classifier. Each class is represented by its centroid, and we classify to the closest using an appropriate metric. In many situations a single prototype is not sufficient to represent inhomogeneous classes, and mixture models are more appropriate. In this section we review Gaussian mixture models and show how they can be generalized via the FDA and PDA methods discussed earlier. A Gaussian mixture model for the kth class has density

$$
P(X|G = k) = \sum_{r=1}^{R_k} \pi_{kr} \phi(X; \mu_{kr}, \Sigma),
$$
\n(12.59)

where the *mixing proportions*  $\pi_{kr}$  sum to one. This has  $R_k$  prototypes for the kth class, and in our specification, the same covariance matrix  $\Sigma$  is used as the metric throughout. Given such a model for each class, the class posterior probabilities are given by

$$
P(G = k|X = x) = \frac{\sum_{r=1}^{R_k} \pi_{kr} \phi(X; \mu_{kr}, \Sigma) \Pi_k}{\sum_{\ell=1}^{K} \sum_{r=1}^{R_{\ell}} \pi_{\ell r} \phi(X; \mu_{\ell r}, \Sigma) \Pi_{\ell}},
$$
(12.60)

where  $\Pi_k$  represent the class prior probabilities.

We saw these calculations for the special case of two components in Chapter 8. As in LDA, we estimate the parameters by maximum likelihood, using the joint log-likelihood based on  $P(G, X)$ :

$$
\sum_{k=1}^{K} \sum_{g_i=k} \log \left[ \sum_{r=1}^{R_k} \pi_{kr} \phi(x_i; \mu_{kr}, \Sigma) \Pi_k \right].
$$
 (12.61)

The sum within the log makes this a rather messy optimization problem if tackled directly. The classical and natural method for computing the maximum-likelihood estimates (MLEs) for mixture distributions is the EM algorithm (Dempster et al., 1977), which is known to possess good convergence properties. EM alternates between the two steps:

#### 450 12. Flexible Discriminants

E-step: Given the current parameters, compute the responsibility of subclass  $c_{kr}$  within class k for each of the class-k observations  $(g_i = k)$ :

$$
W(c_{kr}|x_i, g_i) = \frac{\pi_{kr}\phi(x_i; \mu_{kr}, \Sigma)}{\sum_{\ell=1}^{R_k} \pi_{k\ell}\phi(x_i; \mu_{k\ell}, \Sigma)}.
$$
(12.62)

M-step: Compute the weighted MLEs for the parameters of each of the component Gaussians within each of the classes, using the weights from the E-step.

In the E-step, the algorithm apportions the unit weight of an observation in class  $k$  to the various subclasses assigned to that class. If it is close to the centroid of a particular subclass, and far from the others, it will receive a mass close to one for that subclass. On the other hand, observations halfway between two subclasses will get approximately equal weight for both.

In the M-step, an observation in class  $k$  is used  $R_k$  times, to estimate the parameters in each of the  $R_k$  component densities, with a different weight for each. The EM algorithm is studied in detail in Chapter 8. The algorithm requires initialization, which can have an impact, since mixture likelihoods are generally multimodal. Our software (referenced in the Computational Considerations on page 455) allows several strategies; here we describe the default. The user supplies the number  $R_k$  of subclasses per class. Within class  $k$ , a  $k$ -means clustering model, with multiple random starts, is fitted to the data. This partitions the observations into  $R_k$  disjoint groups, from which an initial weight matrix, consisting of zeros and ones, is created.

Our assumption of an equal component covariance matrix  $\Sigma$  throughout buys an additional simplicity; we can incorporate rank restrictions in the mixture formulation just like in LDA. To understand this, we review a littleknown fact about LDA. The rank-L LDA fit (Section 4.3.3) is equivalent to the maximum-likelihood fit of a Gaussian model,where the different mean vectors in each class are confined to a rank-L subspace of  $\mathbb{R}^p$  (Exercise 4.8). We can inherit this property for the mixture model, and maximize the loglikelihood (12.61) subject to rank constraints on *all* the  $\sum_k R_k$  centroids: rank $\{\mu_{k\ell}\}=L$ .

Again the EM algorithm is available, and the M-step turns out to be a weighted version of LDA, with  $R = \sum_{k=1}^{K} R_k$  "classes." Furthermore, we can use optimal scoring as before to solve the weighted LDA problem, which allows us to use a weighted version of FDA or PDA at this stage. One would expect, in addition to an increase in the number of "classes," a similar increase in the number of "observations" in the kth class by a factor of  $R_k$ . It turns out that this is not the case if linear operators are used for the optimal scoring regression. The enlarged indicator Y matrix collapses in this case to a *blurred* response matrix  $Z$ , which is intuitively pleasing. For example, suppose there are  $K = 3$  classes, and  $R_k = 3$  subclasses per class. Then Z might be

The image displays a matrix with associated row labels and column headers, along with an equation number.

$$
g_1 = 2 \begin{pmatrix} c_{11} & c_{12} & c_{13} & c_{21} & c_{22} & c_{23} & c_{31} & c_{32} & c_{33} \\ 0 & 0 & 0 & 0.3 & 0.5 & 0.2 & 0 & 0 & 0 \\ g_2 = 1 & 0.9 & 0.1 & 0.0 & 0 & 0 & 0 & 0 & 0 & 0 \\ g_3 = 1 & 0.1 & 0.8 & 0.1 & 0 & 0 & 0 & 0 & 0 & 0 \\ g_4 = 3 & 0 & 0 & 0 & 0 & 0 & 0 & 0.5 & 0.4 & 0.1 \\ g_5 = 2 & 0 & 0 & 0 & 0.7 & 0.1 & 0.2 & 0 & 0 & 0 \\ \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots \\ g_N = 3 & 0 & 0 & 0 & 0 & 0 & 0 & 0.1 & 0.1 & 0.8 \\ \end{pmatrix}, \quad (12.63)
$$

where the entries in a class-k row correspond to  $W(c_{kr}|x, g_i)$ . The remaining steps are the same:

$$
\hat{\mathbf{Z}} = \mathbf{SZ}
$$
  
$$
\mathbf{Z}^T \hat{\mathbf{Z}} = \boldsymbol{\Theta} \mathbf{D} \boldsymbol{\Theta}^T
$$
  
Update  $\pi$ s and  $\Pi$ s

These simple modifications add considerable flexibility to the mixture model:

- The dimension reduction step in LDA, FDA or PDA is limited by the number of classes; in particular, for  $K = 2$  classes no reduction is possible. MDA substitutes subclasses for classes, and then allows us to look at low-dimensional views of the subspace spanned by these subclass centroids. This subspace will often be an important one for discrimination.
- By using FDA or PDA in the M-step, we can adapt even more to particular situations. For example, we can fit MDA models to digitized analog signals and images, with smoothness constraints built in.

Figure 12.13 compares FDA and MDA on the mixture example.

## 12.7.1 Example: Waveform Data

We now illustrate some of these ideas on a popular simulated example, taken from Breiman et al. (1984, pages 49–55), and used in Hastie and Tibshirani (1996b) and elsewhere. It is a three-class problem with 21 variables, and is considered to be a difficult pattern recognition problem. The predictors are defined by

$$
X_j = Uh_1(j) + (1 - U)h_2(j) + \epsilon_j
$$
 Class 1,  
\n
$$
X_j = Uh_1(j) + (1 - U)h_3(j) + \epsilon_j
$$
 Class 2, (12.64)  
\n
$$
X_j = Uh_2(j) + (1 - U)h_3(j) + \epsilon_j
$$
 Class 3,

where  $j = 1, 2, ..., 21, U$  is uniform on  $(0, 1), \epsilon_j$  are standard normal variates, and the  $h_{\ell}$  are the shifted triangular waveforms:  $h_1(j) = \max(6 -$ 

Image /page/3/Figure/1 description: This image displays a scatter plot with two classes of data points, represented by orange and blue circles. The background is divided into regions by two decision boundaries: a solid black line and a dashed purple line. The plot also includes text indicating performance metrics: Training Error: 0.185, Test Error: 0.235, and Bayes Error: 0.210. The background is shaded with dots, with orange dots in the upper left and lower right regions, and blue dots in the central and upper right regions, indicating the classification regions defined by the decision boundaries.

FDA / MARS - Degree 2

Image /page/3/Figure/3 description: This is a scatter plot showing two classes of data points, represented by orange circles and light blue circles. The plot is divided into regions by a black solid line and a purple dashed line, indicating decision boundaries. The background is colored with a light orange dotted pattern in one region and a light blue dotted pattern in another, corresponding to the predicted classes. Several data points are highlighted with a thicker black outline, suggesting they are support vectors or key points. In the bottom left corner, text indicates performance metrics: Training Error: 0.17, Test Error: 0.22, and Bayes Error: 0.21.

MDA - 5 Subclasses per Class

FIGURE 12.13. FDA and MDA on the mixture data. The upper plot uses FDA with MARS as the regression procedure. The lower plot uses MDA with five mixture centers per class (indicated). The MDA solution is close to Bayes optimal, as might be expected given the data arise from mixtures of Gaussians. The broken purple curve in the background is the Bayes decision boundary.

Image /page/4/Figure/1 description: The image displays three plots, each titled "Class 1", "Class 2", and "Class 3". Each plot features a red triangular outline with a peak in the center. Inside each red outline, there are multiple dashed green lines, labeled with numbers from 1 to 5. These green lines represent data points that follow the shape of the red outline, with different lines showing variations in the data. The plots are arranged vertically, one below the other.

FIGURE 12.14. Some examples of the waveforms generated from model (12.64) before the Gaussian noise is added.

 $|j - 11|, 0$ ,  $h_2(j) = h_1(j - 4)$  and  $h_3(j) = h_1(j + 4)$ . Figure 12.14 shows some example waveforms from each class.

Table 12.4 shows the results of MDA applied to the waveform data, as well as several other methods from this and other chapters. Each training sample has 300 observations, and equal priors were used, so there are roughly 100 observations in each class. We used test samples of size 500. The two MDA models are described in the caption.

Figure 12.15 shows the leading canonical variates for the penalized MDA model, evaluated at the test data. As we might have guessed, the classes appear to lie on the edges of a triangle. This is because the  $h_i(i)$  are represented by three points in 21-space, thereby forming vertices of a triangle, and each class is represented as a convex combination of a pair of vertices, and hence lie on an edge. Also it is clear visually that all the information lies in the first two dimensions; the percentage of variance explained by the first two coordinates is 99.8%, and we would lose nothing by truncating the solution there. The Bayes risk for this problem has been estimated to be about 0.14 (Breiman et al., 1984). MDA comes close to the optimal rate, which is not surprising since the structure of the MDA model is similar to the generating model.

#### 454 12. Flexible Discriminants

TABLE 12.4. Results for waveform data. The values are averages over ten simulations, with the standard error of the average in parentheses. The five entries above the line are taken from Hastie et al. (1994). The first model below the line is MDA with three subclasses per class. The next line is the same, except that the discriminant coefficients are penalized via a roughness penalty to effectively 4df. The third is the corresponding penalized LDA or PDA model.

| Technique                          | Error Rates  |                                        |
|------------------------------------|--------------|----------------------------------------|
|                                    | Training     | Test                                   |
| LDA                                | 0.121(0.006) | 0.191(0.006)                           |
| QDA                                | 0.039(0.004) | 0.205(0.006)                           |
| CART                               | 0.072(0.003) | 0.289(0.004)                           |
| FDA/MARS (degree = 1)              | 0.100(0.006) | 0.191(0.006)                           |
| FDA/MARS (degree = 2)              | 0.068(0.004) | 0.215(0.002)                           |
| MDA (3 subclasses)                 | 0.087(0.005) | 0.169(0.006)                           |
| MDA (3 subclasses, penalized 4 df) | 0.137(0.006) | 0.157(0.005)                           |
| PDA (penalized 4 df)               | 0.150(0.005) | 0.171(0.005)                           |
| <b>Bayes</b>                       |              | <span style="color:green">0.140</span> |

Image /page/5/Figure/3 description: The image displays two scatter plots side-by-side, both titled "3 Subclasses, Penalized 4 df". The left plot has "Discriminant Var 2" on the y-axis and "Discriminant Var 1" on the x-axis, with data points colored blue, green, and orange, labeled with numbers 1, 2, and 3. Circles highlight clusters of points labeled with numbers 1, 2, and 3. The right plot has "Discriminant Var 4" on the y-axis and "Discriminant Var 3" on the x-axis, with similarly colored and labeled data points, and a central cluster of points highlighted by circles labeled 1, 2, and 3.

FIGURE 12.15. Some two-dimensional views of the MDA model fitted to a sample of the waveform model. The points are independent test data, projected on to the leading two canonical coordinates (left panel), and the third and fourth (right panel). The subclass centers are indicated.

### Computational Considerations

With  $N$  training cases,  $p$  predictors, and  $m$  support vectors, the support vector machine requires  $m^3 + mN + mpN$  operations, assuming  $m \approx N$ . They do not scale well with N, although computational shortcuts are available (Platt, 1999). Since these are evolving rapidly, the reader is urged to search the web for the latest technology.

LDA requires  $Np^2 + p^3$  operations, as does PDA. The complexity of FDA depends on the regression method used. Many techniques are linear in N, such as additive models and MARS. General splines and kernel-based regression methods will typically require  $N^3$  operations.

Software is available for fitting FDA, PDA and MDA models in the R package mda, which is also available in S-PLUS.

## Bibliographic Notes

The theory behind support vector machines is due to Vapnik and is described in Vapnik (1996). There is a burgeoning literature on SVMs; an online bibliography, created and maintained by Alex Smola and Bernhard Schölkopf, can be found at:

#### http://www.kernel-machines.org.

Our treatment is based on Wahba et al. (2000) and Evgeniou et al. (2000), and the tutorial by Burges (Burges, 1998).

Linear discriminant analysis is due to Fisher (1936) and Rao (1973). The connection with optimal scoring dates back at least to Breiman and Ihaka (1984), and in a simple form to Fisher (1936). There are strong connections with correspondence analysis (Greenacre, 1984). The description of flexible, penalized and mixture discriminant analysis is taken from Hastie et al. (1994), Hastie et al. (1995) and Hastie and Tibshirani (1996b), and all three are summarized in Hastie et al. (2000); see also Ripley (1996).

## Exercises

Ex. 12.1 Show that the criteria  $(12.25)$  and  $(12.8)$  are equivalent.

Ex. 12.2 Show that the solution to (12.29) is the same as the solution to (12.25) for a particular kernel.

Ex. 12.3 Consider a modification to (12.43) where you do not penalize the constant. Formulate the problem, and characterize its solution.

Ex. 12.4 Suppose you perform a reduced-subspace linear discriminant analysis for a K-group problem. You compute the canonical variables of di-

#### 456 12. Flexible Discriminants

mension  $L \leq K - 1$  given by  $z = \mathbf{U}^T x$ , where U is the  $p \times L$  matrix of discriminant coefficients, and  $p > K$  is the dimension of x.

(a) If  $L = K - 1$  show that

$$
\|z-\bar{z}_k\|^2 - \|z-\bar{z}_{k'}\|^2 = \|x-\bar{x}_k\|^2_{W} - \|x-\bar{x}_{k'}\|^2_{W},
$$

where  $\left\Vert \cdot\right\Vert _{W}$  denotes  $Mahalanobis$  distance with respect to the covariance W.

(b) If  $L < K - 1$ , show that the same expression on the left measures the difference in Mahalanobis squared distances for the distributions projected onto the subspace spanned by U.

Ex. 12.5 The data in phoneme. subset, available from this book's website

#### http://www-stat.stanford.edu/ElemStatLearn

consists of digitized log-periodograms for phonemes uttered by 60 speakers, each speaker having produced phonemes from each of five classes. It is appropriate to plot each vector of 256 "features" against the frequencies 0–255.

- (a) Produce a separate plot of all the phoneme curves against frequency for each class.
- (b) You plan to use a nearest prototype classification scheme to classify the curves into phoneme classes. In particular, you will use a  $K$ -means clustering algorithm in each class ( $km$ eans() in R), and then classify observations to the class of the closest cluster center. The curves are high-dimensional and you have a rather small sample-size-to-variables ratio. You decide to restrict all the prototypes to be smooth functions of frequency. In particular, you decide to represent each prototype  $m$ as  $m = B\theta$  where B is a 256  $\times$  J matrix of natural spline basis functions with  $J$  knots uniformly chosen in  $(0, 255)$  and boundary knots at 0 and 255. Describe how to proceed analytically, and in particular, how to avoid costly high-dimensional fitting procedures. (*Hint*: It may help to restrict  $B$  to be orthogonal.)
- (c) Implement your procedure on the phoneme data, and try it out. Divide the data into a training set and a test set (50-50), making sure that speakers are not split across sets (why?). Use  $K = 1, 3, 5, 7$  centers per class, and for each use  $J = 5, 10, 15$  knots (taking care to start the K-means procedure at the same starting values for each value of J), and compare the results.

Ex. 12.6 Suppose that the regression procedure used in FDA (Section 12.5.1) is a linear expansion of basis functions  $h_m(x)$ ,  $m = 1, \ldots, M$ . Let  $\mathbf{D}_{\pi} =$  ${\bf Y}^T{\bf Y}/N$  be the diagonal matrix of class proportions.

(a) Show that the optimal scoring problem (12.52) can be written in vector notation as

$$
\min_{\theta,\beta} \|\mathbf{Y}\theta - \mathbf{H}\beta\|^2, \tag{12.65}
$$

where  $\theta$  is a vector of K real numbers, and H is the  $N \times M$  matrix of evaluations  $h_i(x_i)$ .

- (b) Suppose that the normalization on  $\theta$  is  $\theta^T \mathbf{D}_{\pi} 1 = 0$  and  $\theta^T \mathbf{D}_{\pi} \theta = 1$ . Interpret these normalizations in terms of the original scored  $\theta(g_i)$ .
- (c) Show that, with this normalization, (12.65) can be partially optimized w.r.t.  $\beta$ , and leads to

$$
\max_{\theta} \theta^T \mathbf{Y}^T \mathbf{S} \mathbf{Y} \theta, \tag{12.66}
$$

subject to the normalization constraints, where S is the projection operator corresponding to the basis matrix H.

- (d) Suppose that the  $h_i$  include the constant function. Show that the largest eigenvalue of S is 1.
- (e) Let  $\Theta$  be a  $K \times K$  matrix of scores (in columns), and suppose the normalization is  $\mathbf{\Theta}^T \mathbf{D}_{\pi} \mathbf{\Theta} = \mathbf{I}$ . Show that the solution to (12.53) is given by the complete set of eigenvectors of S; the first eigenvector is trivial, and takes care of the centering of the scores. The remainder characterize the optimal scoring solution.

Ex. 12.7 Derive the solution to the penalized optimal scoring problem  $(12.57).$ 

Ex. 12.8 Show that coefficients  $\beta_{\ell}$  found by optimal scoring are proportional to the discriminant directions  $\nu_{\ell}$  found by linear discriminant analysis.

Ex. 12.9 Let  $\hat{\mathbf{Y}} = \mathbf{X}\hat{\mathbf{B}}$  be the fitted  $N \times K$  indicator response matrix after linear regression on the  $N \times p$  matrix **X**, where  $p > K$ . Consider the reduced features  $x_i^* = \hat{\mathbf{B}}^T x_i$ . Show that LDA using  $x_i^*$  is equivalent to LDA in the original space.

Ex. 12.10 Kernels and linear discriminant analysis. Suppose you wish to carry out a linear discriminant analysis (two classes) using a vector of transformations of the input variables  $h(x)$ . Since  $h(x)$  is high-dimensional, you will use a regularized within-class covariance matrix  $\mathbf{W}_h + \gamma \mathbf{I}$ . Show that the model can be estimated using only the inner products  $K(x_i, x_{i'}) =$  $\langle h(x_i), h(x_{i'})\rangle$ . Hence the kernel property of support vector machines is also shared by regularized linear discriminant analysis.

Ex. 12.11 The MDA procedure models each class as a mixture of Gaussians. Hence each mixture center belongs to one and only one class. A more general model allows each mixture center to be shared by all classes. We take the joint density of labels and features to be

458 12. Flexible Discriminants

$$
P(G, X) = \sum_{r=1}^{R} \pi_r P_r(G, X),
$$
\n(12.67)

a mixture of joint densities. Furthermore we assume

$$
P_r(G, X) = P_r(G)\phi(X; \mu_r, \Sigma). \tag{12.68}
$$

This model consists of regions centered at  $\mu_r$ , and for each there is a class profile  $P_r(G)$ . The posterior class distribution is given by

$$
P(G = k|X = x) = \frac{\sum_{r=1}^{R} \pi_r P_r(G = k) \phi(x; \mu_r, \Sigma)}{\sum_{r=1}^{R} \pi_r \phi(x; \mu_r, \Sigma)},
$$
(12.69)

where the denominator is the marginal distribution  $P(X)$ .

(a) Show that this model (called MDA2) can be viewed as a generalization of MDA since

$$
P(X|G=k) = \frac{\sum_{r=1}^{R} \pi_r P_r(G=k) \phi(x; \mu_r, \Sigma)}{\sum_{r=1}^{R} \pi_r P_r(G=k)},
$$
(12.70)

where  $\pi_{rk} = \pi_r P_r(G = k) / \sum_{r=1}^{R} \pi_r P_r(G = k)$  corresponds to the mixing proportions for the kth class.

- (b) Derive the EM algorithm for MDA2.
- (c) Show that if the initial weight matrix is constructed as in MDA, involving separate k-means clustering in each class, then the algorithm for MDA2 is identical to the original MDA procedure.

This is page 459 Printer: Opaque this

# 13 Prototype Methods and Nearest-Neighbors

## 13.1 Introduction

In this chapter we discuss some simple and essentially model-free methods for classification and pattern recognition. Because they are highly unstructured, they typically are not useful for understanding the nature of the relationship between the features and class outcome. However, as black box prediction engines, they can be very effective, and are often among the best performers in real data problems. The nearest-neighbor technique can also be used in regression; this was touched on in Chapter 2 and works reasonably well for low-dimensional problems. However, with high-dimensional features, the bias–variance tradeoff does not work as favorably for nearestneighbor regression as it does for classification.

## 13.2 Prototype Methods

Throughout this chapter, our training data consists of the N pairs  $(x_1, g_1)$ ,  $\ldots$ ,  $(x_n, g_N)$  where  $g_i$  is a class label taking values in  $\{1, 2, \ldots, K\}$ . Prototype methods represent the training data by a set of points in feature space. These prototypes are typically not examples from the training sample, except in the case of 1-nearest-neighbor classification discussed later.

Each prototype has an associated class label, and classification of a query point  $x$  is made to the class of the closest prototype. "Closest" is usually defined by Euclidean distance in the feature space, after each feature has

been standardized to have overall mean 0 and variance 1 in the training sample. Euclidean distance is appropriate for quantitative features. We discuss distance measures between qualitative and other kinds of feature values in Chapter 14.

These methods can be very effective if the prototypes are well positioned to capture the distribution of each class. Irregular class boundaries can be represented, with enough prototypes in the right places in feature space. The main challenge is to figure out how many prototypes to use and where to put them. Methods differ according to the number and way in which prototypes are selected.

## 13.2.1 K-means Clustering

K-means clustering is a method for finding clusters and cluster centers in a set of unlabeled data. One chooses the desired number of cluster centers, say  $R$ , and the  $K$ -means procedure iteratively moves the centers to minimize the total within cluster variance.<sup>1</sup> Given an initial set of centers, the  $K$ means algorithm alternates the two steps:

- for each center we identify the subset of training points (its cluster) that is closer to it than any other center;
- the means of each feature for the data points in each cluster are computed, and this mean vector becomes the new center for that cluster.

These two steps are iterated until convergence. Typically the initial centers are R randomly chosen observations from the training data. Details of the K-means procedure, as well as generalizations allowing for different variable types and more general distance measures, are given in Chapter 14.

To use K-means clustering for classification of labeled data, the steps are:

- apply K-means clustering to the training data in each class separately, using  $R$  prototypes per class;
- assign a class label to each of the  $K \times R$  prototypes;
- classify a new feature  $x$  to the class of the closest prototype.

Figure 13.1 (upper panel) shows a simulated example with three classes and two features. We used  $R = 5$  prototypes per class, and show the classification regions and the decision boundary. Notice that a number of the

<sup>&</sup>lt;sup>1</sup>The "K" in K-means refers to the number of cluster centers. Since we have already reserved  $K$  to denote the number of classes, we denote the number of clusters by  $R$ .

Image /page/12/Figure/1 description: This is a scatter plot showing data points classified into three categories, indicated by different colors and shapes. The background is filled with a dotted pattern, with teal dots in the upper left, yellow dots in the upper right, and light blue dots in the lower half. Several decision boundaries, depicted as solid black lines and dashed purple curves, divide the plot into regions corresponding to the classifications. Larger, filled circles with black outlines represent the data points. There are green circles, orange circles, and light blue circles scattered across the plot, with their positions generally aligning with the background color patterns and the decision boundaries. Some points are clustered together, while others are more isolated.

K-means - 5 Prototypes per Class

LVQ - 5 Prototypes per Class

Image /page/12/Figure/4 description: This is a scatter plot showing three classes of data points, represented by teal, orange, and light blue circles. Each class has a set of larger, solid-colored circles with black outlines, which appear to be support vectors or representative points for each class. The plot also displays decision boundaries, shown as solid black lines and dashed purple lines, which separate the regions of the plot assigned to each class. The background is filled with a dotted pattern corresponding to the predicted class in each region, with teal dots in the top left, orange dots in the top right, and light blue dots in the bottom half. The data points are distributed across the plot, with some overlap between the classes, indicating a classification task.

FIGURE 13.1. Simulated example with three classes and five prototypes per class. The data in each class are generated from a mixture of Gaussians. In the upper panel, the prototypes were found by applying the  $K$ -means clustering algorithm separately in each class. In the lower panel, the LVQ algorithm (starting from the K-means solution) moves the prototypes away from the decision boundary. The broken purple curve in the background is the Bayes decision boundary.

|  |  |  |  | Algorithm 13.1 Learning Vector Quantization—LVQ. |  |
|--|--|--|--|--------------------------------------------------|--|
|--|--|--|--|--------------------------------------------------|--|

- 1. Choose R initial prototypes for each class:  $m_1(k), m_2(k), \ldots, m_R(k)$ ,  $k = 1, 2, \ldots, K$ , for example, by sampling R training points at random from each class.
- 2. Sample a training point  $x_i$  randomly (with replacement), and let  $(j, k)$ index the closest prototype  $m_j(k)$  to  $x_i$ .
  - (a) If  $g_i = k$  (i.e., they are in the same class), move the prototype towards the training point:

$$
m_j(k) \leftarrow m_j(k) + \epsilon (x_i - m_j(k)),
$$

where  $\epsilon$  is the *learning rate*.

(b) If  $g_i \neq k$  (i.e., they are in different classes), move the prototype away from the training point:

$$
m_j(k) \leftarrow m_j(k) - \epsilon (x_i - m_j(k)).
$$

3. Repeat step 2, decreasing the learning rate  $\epsilon$  with each iteration towards zero.

prototypes are near the class boundaries, leading to potential misclassification errors for points near these boundaries. This results from an obvious shortcoming with this method: for each class, the other classes do not have a say in the positioning of the prototypes for that class. A better approach, discussed next, uses all of the data to position all prototypes.

#### 13.2.2 Learning Vector Quantization

In this technique due to Kohonen (1989), prototypes are placed strategically with respect to the decision boundaries in an ad-hoc way. LVQ is an *online* algorithm—observations are processed one at a time.

The idea is that the training points attract prototypes of the correct class, and repel other prototypes. When the iterations settle down, prototypes should be close to the training points in their class. The learning rate  $\epsilon$  is decreased to zero with each iteration, following the guidelines for stochastic approximation learning rates (Section 11.4.)

Figure 13.1 (lower panel) shows the result of LVQ, using the K-means solution as starting values. The prototypes have tended to move away from the decision boundaries, and away from prototypes of competing classes.

The procedure just described is actually called LVQ1. Modifications (LVQ2, LVQ3, etc.) have been proposed, that can sometimes improve performance. A drawback of learning vector quantization methods is the fact that they are defined by algorithms, rather than optimization of some fixed criteria; this makes it difficult to understand their properties.

#### 13.2.3 Gaussian Mixtures

The Gaussian mixture model can also be thought of as a prototype method, similar in spirit to K-means and LVQ. We discuss Gaussian mixtures in some detail in Sections 6.8, 8.5 and 12.7. Each cluster is described in terms of a Gaussian density, which has a centroid (as in K-means), and a covariance matrix. The comparison becomes crisper if we restrict the component Gaussians to have a scalar covariance matrix (Exercise 13.1). The two steps of the alternating EM algorithm are very similar to the two steps in  $K$ means:

- In the E-step, each observation is assigned a *responsibility* or weight for each cluster, based on the likelihood of each of the corresponding Gaussians. Observations close to the center of a cluster will most likely get weight 1 for that cluster, and weight 0 for every other cluster. Observations half-way between two clusters divide their weight accordingly.
- In the M-step, each observation contributes to the weighted means (and covariances) for every cluster.

As a consequence, the Gaussian mixture model is often referred to as a *soft* clustering method, while K-means is hard.

Similarly, when Gaussian mixture models are used to represent the feature density in each class, it produces smooth posterior probabilities  $\hat{p}(x)$  =  $\{\hat{p}_1(x), \ldots, \hat{p}_K(x)\}\$ for classifying x (see (12.60) on page 449.) Often this is interpreted as a soft classification, while in fact the classification rule is  $G(x) = \arg \max_k \hat{p}_k(x)$ . Figure 13.2 compares the results of K-means and Gaussian mixtures on the simulated mixture problem of Chapter 2. We see that although the decision boundaries are roughly similar, those for the mixture model are smoother (although the prototypes are in approximately the same positions.) We also see that while both procedures devote a blue prototype (incorrectly) to a region in the northwest, the Gaussian mixture classifier can ultimately ignore this region, while K-means cannot. LVQ gave very similar results to K-means on this example, and is not shown.

# 13.3 k-Nearest-Neighbor Classifiers

These classifiers are memory-based, and require no model to be fit. Given a query point  $x_0$ , we find the k training points  $x_{(r)}$ ,  $r = 1, \ldots, k$  closest in distance to  $x_0$ , and then classify using majority vote among the k neighbors.

Image /page/15/Figure/1 description: This is a scatter plot showing data points classified into two categories, represented by orange circles and blue circles. The plot is divided into regions by black and purple decision boundaries, indicating different classifications. The background is a grid of dots, colored light orange and light blue, corresponding to the regions. In the bottom left corner, there is text indicating performance metrics: Training Error: 0.170, Test Error: 0.243, and Bayes Error: 0.210. Several data points are highlighted with a black outline, suggesting they might be support vectors or misclassified points.

K-means - 5 Prototypes per Class

Gaussian Mixtures - 5 Subclasses per Class

Image /page/15/Figure/4 description: This is a scatter plot showing a classification boundary for Gaussian mixtures with three subclasses per class. The plot displays two classes of data points, represented by orange circles and light blue circles. Several points from each class are highlighted with a black outline. The background is divided into two regions by a solid black line and a dashed purple line, indicating the decision boundaries. The plot also includes text indicating the training error (0.17), test error (0.22), and Bayes error (0.21).

FIGURE 13.2. The upper panel shows the K-means classifier applied to the mixture data example. The decision boundary is piecewise linear. The lower panel shows a Gaussian mixture model with a common covariance for all component Gaussians. The EM algorithm for the mixture model was started at the  $K\text{-}means$ solution. The broken purple curve in the background is the Bayes decision boundary.

Ties are broken at random. For simplicity we will assume that the features are real-valued, and we use Euclidean distance in feature space:

$$
d_{(i)} = ||x_{(i)} - x_0||. \tag{13.1}
$$

Typically we first standardize each of the features to have mean zero and variance 1, since it is possible that they are measured in different units. In Chapter 14 we discuss distance measures appropriate for qualitative and ordinal features, and how to combine them for mixed data. Adaptively chosen distance metrics are discussed later in this chapter.

Despite its simplicity, k-nearest-neighbors has been successful in a large number of classification problems, including handwritten digits, satellite image scenes and EKG patterns. It is often successful where each class has many possible prototypes, and the decision boundary is very irregular. Figure 13.3 (upper panel) shows the decision boundary of a 15-nearestneighbor classifier applied to the three-class simulated data. The decision boundary is fairly smooth compared to the lower panel, where a 1-nearestneighbor classifier was used. There is a close relationship between nearestneighbor and prototype methods: in 1-nearest-neighbor classification, each training point is a prototype.

Figure 13.4 shows the training, test and tenfold cross-validation errors as a function of the neighborhood size, for the two-class mixture problem. Since the tenfold CV errors are averages of ten numbers, we can estimate a standard error.

Because it uses only the training point closest to the query point, the bias of the 1-nearest-neighbor estimate is often low, but the variance is high. A famous result of Cover and Hart (1967) shows that asymptotically the error rate of the 1-nearest-neighbor classifier is never more than twice the Bayes rate. The rough idea of the proof is as follows (using squared-error loss). We assume that the query point coincides with one of the training points, so that the bias is zero. This is true asymptotically if the dimension of the feature space is fixed and the training data fills up the space in a dense fashion. Then the error of the Bayes rule is just the variance of a Bernoulli random variate (the target at the query point), while the error of 1-nearest-neighbor rule is twice the variance of a Bernoulli random variate, one contribution each for the training and query targets.

We now give more detail for misclassification loss. At  $x$  let  $k^*$  be the dominant class, and  $p_k(x)$  the true conditional probability for class k. Then

Bayes error = 
$$
1 - p_{k^*}(x)
$$
, (13.2)

1-nearest-neighbor error = 
$$
\sum_{k}^{K} p_k(x) (1 - p_k(x)),
$$
 (13.3)

$$
\geq \quad {}^{k=1}2 p_{k^*}(x). \tag{13.4}
$$

The asymptotic 1-nearest-neighbor error rate is that of a random rule; we pick both the classification and the test point at random with probabili-

Image /page/17/Figure/1 description: This is a scatter plot with a decision boundary. There are three classes of data points, represented by green circles, orange circles, and blue circles. The background is a grid of dots, colored light blue, light green, and light orange, indicating the predicted class for each region. Black lines represent the decision boundaries between the classes, and dashed purple lines represent additional boundaries. The data points are clustered in different areas, with green circles concentrated in the upper left and center-left, orange circles in the center and right, and blue circles in the bottom and bottom-right.

15-Nearest Neighbors

1-Nearest Neighbor

Image /page/17/Figure/4 description: This is a scatter plot with a background grid of dots. There are three distinct regions colored light blue, light green, and light orange, separated by black and purple dashed lines. Scattered across the plot are numerous circles, colored light blue, green, and orange. The light blue circles are predominantly in the lower right and lower left portions of the plot, within the light blue dotted region. The green circles are concentrated in the upper left and central areas, within the light green dotted region. The orange circles are clustered in the central and upper right areas, within the light orange dotted region. The black lines appear to be decision boundaries, with the purple dashed lines possibly representing other boundaries or contours.

FIGURE 13.3. k-nearest-neighbor classifiers applied to the simulation data of Figure 13.1. The broken purple curve in the background is the Bayes decision  $boundary.$ 

Image /page/18/Figure/1 description: The image displays two plots related to k-Nearest-Neighbor classification. The top plot is a line graph showing misclassification errors against the number of neighbors. It includes three lines representing Test Error (blue), 10-fold CV (green), and Training Error (orange), along with a dashed purple line for Bayes Error. The x-axis is labeled 'Number of Neighbors' ranging from 0 to 30, and the y-axis is labeled 'Misclassification Errors' ranging from 0.00 to 0.30. The bottom plot is a scatter plot illustrating the decision boundaries for a 7-Nearest Neighbors classifier. It shows data points colored orange and blue, with decision boundaries depicted by black and purple lines. Below this plot, the training error is listed as 0.145, the test error as 0.225, and the Bayes error as 0.210.

FIGURE 13.4. k-nearest-neighbors on the two-class mixture data. The upper panel shows the misclassification errors as a function of neighborhood size. Standard error bars are included for 10-fold cross validation. The lower panel shows the decision boundary for 7-nearest-neighbors, which appears to be optimal for minimizing test error. The broken purple curve in the background is the Bayes  $decision\ boundary.$ 

ties  $p_k(x)$ ,  $k = 1, ..., K$ . For  $K = 2$  the 1-nearest-neighbor error rate is  $2p_{k^*}(x)(1-p_{k^*}(x)) \leq 2(1-p_{k^*}(x))$  (twice the Bayes error rate). More generally, one can show (Exercise 13.3)

$$
\sum_{k=1}^{K} p_k(x)(1 - p_k(x)) \le 2(1 - p_{k^*}(x)) - \frac{K}{K - 1}(1 - p_{k^*}(x))^2.
$$
 (13.5)

Many additional results of this kind have been derived; Ripley (1996) summarizes a number of them.

This result can provide a rough idea about the best performance that is possible in a given problem. For example, if the 1-nearest-neighbor rule has a  $10\%$  error rate, then asymptotically the Bayes error rate is at least 5%. The kicker here is the asymptotic part, which assumes the bias of the nearest-neighbor rule is zero. In real problems the bias can be substantial. The adaptive nearest-neighbor rules, described later in this chapter, are an attempt to alleviate this bias. For simple nearest-neighbors, the bias and variance characteristics can dictate the optimal number of near neighbors for a given problem. This is illustrated in the next example.

#### 13.3.1 Example: A Comparative Study

We tested the nearest-neighbors, K-means and LVQ classifiers on two simulated problems. There are 10 independent features  $X_i$ , each uniformly distributed on [0, 1]. The two-class 0-1 target variable is defined as follows:

Y = I(X\_1 > rac{1}{2}); problem 1: "easy",

$$
Y = I\left(X_1 > \frac{1}{2}\right)
$$
Y = I(sign{\prod\_{j=1}^{3}} (X\_j - rac{1}{2}) > 0); problem 2: "difficult."

$$
Y = I\left(\text{sign}\left\{\prod_{j=1}^{3} \left(X_j - \frac{1}{2}\right)\right\} > 0\right)
$$

$$
(13.6)
$$

Hence in the first problem the two classes are separated by the hyperplane  $X_1 = 1/2$ ; in the second problem, the two classes form a checkerboard pattern in the hypercube defined by the first three features. The Bayes error rate is zero in both problems. There were 100 training and 1000 test observations.

Figure 13.5 shows the mean and standard error of the misclassification error for nearest-neighbors, K-means and LVQ over ten realizations, as the tuning parameters are varied. We see that  $K$ -means and LVQ give nearly identical results. For the best choices of their tuning parameters, K-means and LVQ outperform nearest-neighbors for the first problem, and they perform similarly for the second problem. Notice that the best value of each tuning parameter is clearly situation dependent. For example 25 nearest-neighbors outperforms 1-nearest-neighbor by a factor of 70% in the

Image /page/20/Figure/1 description: This figure displays four plots comparing the misclassification error of two algorithms, Nearest Neighbors and K-means & LVQ, on both easy and difficult datasets. The top left plot shows the Nearest Neighbors algorithm on an easy dataset, with the x-axis representing the number of neighbors and the y-axis representing misclassification error. The error decreases as the number of neighbors increases up to around 20, then slightly increases. The top right plot shows the K-means & LVQ algorithm on an easy dataset, with the x-axis representing the number of prototypes per class and the y-axis representing misclassification error. The error is relatively low and stable, around 0.2, as the number of prototypes increases. The bottom left plot shows the Nearest Neighbors algorithm on a difficult dataset. The misclassification error increases as the number of neighbors increases, reaching a peak around 40 neighbors and then slightly decreasing. The bottom right plot shows the K-means & LVQ algorithm on a difficult dataset. The misclassification error decreases as the number of prototypes increases, stabilizing around 0.42 for 15 or more prototypes.

FIGURE 13.5. Mean  $\pm$  one standard error of misclassification error for nearest-neighbors, K-means (blue) and LVQ (red) over ten realizations for two simulated problems: "easy" and "difficult," described in the text.

Image /page/21/Figure/1 description: The image displays a grid of six plots. The top row contains three plots labeled "Spectral Band 1", "Spectral Band 2", and "Spectral Band 3". The bottom row contains three plots labeled "Spectral Band 4", "Land Usage", and "Predicted Land Usage". The spectral band plots show colorful, pixelated representations of land, with varying colors like purple, blue, yellow, and red. The "Land Usage" plot is a segmented map with distinct colored regions representing different land types, including yellow, orange, red, gray, blue, and cyan. The "Predicted Land Usage" plot is similar to the "Land Usage" plot, showing a classification of land types with the same color scheme, suggesting a comparison between actual and predicted land usage.

FIGURE 13.6. The first four panels are LANDSAT images for an agricultural area in four spectral bands, depicted by heatmap shading. The remaining two panels give the actual land usage (color coded) and the predicted land usage using a five-nearest-neighbor rule described in the text.

first problem, while 1-nearest-neighbor is best in the second problem by a factor of 18%. These results underline the importance of using an objective, data-based method like cross-validation to estimate the best value of a tuning parameter (see Figure 13.4 and Chapter 7).

## 13.3.2 Example: k-Nearest-Neighbors and Image Scene Classification

The STATLOG project (Michie et al., 1994) used part of a LANDSAT image as a benchmark for classification  $(82 \times 100 \text{ pixels})$ . Figure 13.6 shows four heat-map images, two in the visible spectrum and two in the infrared, for an area of agricultural land in Australia. Each pixel has a class label from the 7-element set  $G = \{red\, soil,\, cotton, \, vegetation\, stable, \, mixture,$ gray soil, damp gray soil, very damp gray soil}, determined manually by research assistants surveying the area. The lower middle panel shows the actual land usage, shaded by different colors to indicate the classes. The objective is to classify the land usage at a pixel, based on the information in the four spectral bands.

Five-nearest-neighbors produced the predicted map shown in the bottom right panel, and was computed as follows. For each pixel we extracted an 8-neighbor feature map—the pixel itself and its 8 immediate neighbors

| N | N            | N |
|---|--------------|---|
| N | $\mathbf{X}$ | N |
| N | N            | N |

FIGURE 13.7. A pixel and its 8-neighbor feature map.

(see Figure 13.7). This is done separately in the four spectral bands, giving  $(1+8) \times 4 = 36$  input features per pixel. Then five-nearest-neighbors classification was carried out in this 36-dimensional feature space. The resulting test error rate was about 9.5% (see Figure 13.8). Of all the methods used in the STATLOG project, including LVQ, CART, neural networks, linear discriminant analysis and many others, k-nearest-neighbors performed best on this task. Hence it is likely that the decision boundaries in  $\mathbb{R}^{36}$  are quite irregular.

## 13.3.3 Invariant Metrics and Tangent Distance

In some problems, the training features are invariant under certain natural transformations. The nearest-neighbor classifier can exploit such invariances by incorporating them into the metric used to measure the distances between objects. Here we give an example where this idea was used with great success, and the resulting classifier outperformed all others at the time of its development (Simard et al., 1993).

The problem is handwritten digit recognition, as discussed is Chapter 1 and Section 11.7. The inputs are grayscale images with  $16 \times 16 = 256$ pixels; some examples are shown in Figure 13.9. At the top of Figure 13.10, a "3" is shown, in its actual orientation (middle) and rotated 7.5° and 15° in either direction. Such rotations can often occur in real handwriting, and it is obvious to our eye that this "3" is still a "3" after small rotations. Hence we want our nearest-neighbor classifier to consider these two "3"s to be close together (similar). However the 256 grayscale pixel values for a rotated "3" will look quite different from those in the original image, and hence the two objects can be far apart in Euclidean distance in  $\mathbb{R}^{256}$ .

We wish to remove the effect of rotation in measuring distances between two digits of the same class. Consider the set of pixel values consisting of the original "3" and its rotated versions. This is a one-dimensional curve in  $\mathbb{R}^{256}$ , depicted by the green curve passing through the "3" in Figure 13.10. Figure 13.11 shows a stylized version of  $\mathbb{R}^{256}$ , with two images indicated by  $x_i$  and  $x_{i'}$ . These might be two different "3"s, for example. Through each image we have drawn the curve of rotated versions of that image, called

Image /page/23/Figure/1 description: This is a scatter plot showing the test misclassification error for different methods. The x-axis is labeled 'Method' and ranges from 2 to 14. The y-axis is labeled 'Test Misclassification Error' and ranges from 0.0 to 0.15. Several methods are plotted, including DANN, K-NN, LVQ, RBF, ALLOC80, CART, Neural, NewID, C4.5, QDA, SMART, Logistic, and LDA. DANN has the lowest misclassification error, around 0.07, and LDA has the highest, around 0.16. The other methods fall in between these values, generally showing an increasing trend in misclassification error as the method's position on the x-axis increases.

#### STATLOG results

FIGURE 13.8. Test-error performance for a number of classifiers, as reported by the STATLOG project. The entry DANN is a variant of k-nearest neighbors, using an adaptive metric (Section 13.4.2).

Image /page/23/Figure/4 description: The image displays a grid of handwritten digits. The grid is arranged in three rows and ten columns. Each cell in the grid contains a single digit from 0 to 9. The first row shows the digits 0 through 9 in order. The second row also shows the digits 0 through 9, but the digit 0 is slightly different, appearing more like a filled circle. The third row again shows the digits 0 through 9 in order. The digits are black and are presented on a white background within individual rectangular frames.

FIGURE 13.9. Examples of grayscale images of handwritten digits.

Image /page/24/Figure/1 description: The image displays a visualization of transformations applied to the digit '3' in pixel space. The top row shows five versions of the digit '3' with varying degrees of rotation, labeled from -15° to 15°. Below this, a 3D graph illustrates the concept of transformations, with axes labeled 'Pixel space' and a curve representing 'Transformations of 3' originating from a point labeled '3'. A red arrow indicates a 'Tangent' direction. The bottom row presents five more versions of the digit '3', labeled with alpha values from -0.2 to 0.2, demonstrating a linear equation for the images above. This section also includes the original digit '3' and a transformed version with '+ α'.

FIGURE 13.10. The top row shows a "3" in its original orientation (middle) and rotated versions of it. The green curve in the middle of the figure depicts this set of rotated " 3" in 256-dimensional space. The red line is the tangent line to the curve at the original image, with some " 3"s on this tangent line, and its equation shown at the bottom of the figure.

invariance manifolds in this context. Now, rather than using the usual Euclidean distance between the two images, we use the shortest distance between the two curves. In other words, the distance between the two images is taken to be the shortest Euclidean distance between any rotated version of first image, and any rotated version of the second image. This distance is called an invariant metric.

In principle one could carry out 1-nearest-neighbor classification using this invariant metric. However there are two problems with it. First, it is very difficult to calculate for real images. Second, it allows large transformations that can lead to poor performance. For example a "6" would be considered close to a "9" after a rotation of 180◦ . We need to restrict attention to small rotations.

The use of tangent distance solves both of these problems. As shown in Figure 13.10, we can approximate the invariance manifold of the image "3" by its tangent at the original image. This tangent can be computed by estimating the direction vector from small rotations of the image, or by more sophisticated spatial smoothing methods (Exercise 13.4.) For large rotations, the tangent image no longer looks like a "3," so the problem with large transformations is alleviated.

Image /page/25/Figure/1 description: The image displays a diagram illustrating distances between points and their transformations. Two points, labeled 'xi' and 'xi'', are shown. A blue line connects these two points, representing the 'Euclidean distance between xi and xi''. Two red lines, originating from 'xi' and 'xi'' respectively, are tangent to green curves. These green curves are labeled as 'Transformations of xi' and 'Transformations of xi''. A perpendicular line segment is drawn from the tangent line at 'xi' to the tangent line at 'xi'', indicating a distance measurement. A blue arrow points to this segment, labeling it as 'Distance between transformed xi and xi''. Another red line segment is labeled as 'Tangent distance'.

**FIGURE 13.11.** Tangent distance computation for two images  $x_i$  and  $x_{i'}$ . Rather than using the Euclidean distance between  $x_i$  and  $x_{i'}$ , or the shortest distance between the two curves, we use the shortest distance between the two tangent lines.

The idea then is to compute the invariant tangent line for each training image. For a query image to be classified, we compute its invariant tangent line, and find the closest line to it among the lines in the training set. The class (digit) corresponding to this closest line is our predicted class for the query image. In Figure 13.11 the two tangent lines intersect, but this is only because we have been forced to draw a two-dimensional representation of the actual 256-dimensional situation. In  $\mathbb{R}^{256}$  the probability of two such lines intersecting is effectively zero.

Now a simpler way to achieve this invariance would be to add into the training set a number of rotated versions of each training image, and then just use a standard nearest-neighbor classifier. This idea is called "hints" in Abu-Mostafa (1995), and works well when the space of invariances is small. So far we have presented a simplified version of the problem. In addition to rotation, there are six other types of transformations under which we would like our classifier to be invariant. There are translation (two directions), scaling (two directions), sheer, and character thickness. Hence the curves and tangent lines in Figures 13.10 and 13.11 are actually 7-dimensional manifolds and hyperplanes. It is infeasible to add transformed versions of each training image to capture all of these possibilities. The tangent manifolds provide an elegant way of capturing the invariances.

Table 13.1 shows the test misclassification error for a problem with 7291 training images and 2007 test digits (the U.S. Postal Services database), for a carefully constructed neural network, and simple 1-nearest-neighbor and

| Method                                | Error rate |
|---------------------------------------|------------|
| Neural-net                            | 0.049      |
| 1-nearest-neighbor/Euclidean distance | 0.055      |
| 1-nearest-neighbor/tangent distance   | 0.026      |

TABLE 13.1. Test error rates for the handwritten ZIP code problem.

tangent distance 1-nearest-neighbor rules. The tangent distance nearestneighbor classifier works remarkably well, with test error rates near those for the human eye (this is a notoriously difficult test set). In practice, it turned out that nearest-neighbors are too slow for online classification in this application (see Section 13.5), and neural network classifiers were subsequently developed to mimic it.

## 13.4 Adaptive Nearest-Neighbor Methods

When nearest-neighbor classification is carried out in a high-dimensional feature space, the nearest neighbors of a point can be very far away, causing bias and degrading the performance of the rule.

To quantify this, consider  $N$  data points uniformly distributed in the unit cube  $\left[-\frac{1}{2},\frac{1}{2}\right]$ <sup>p</sup>. Let R be the radius of a 1-nearest-neighborhood centered at the origin. Then

median(R) = 
$$
v_p^{-1/p} \left(1 - \frac{1}{2}^{1/N}\right)^{1/p}
$$
, (13.7)

where  $v_p r^p$  is the volume of the sphere of radius r in p dimensions. Figure 13.12 shows the median radius for various training sample sizes and dimensions. We see that median radius quickly approaches 0.5, the distance to the edge of the cube.

What can be done about this problem? Consider the two-class situation in Figure 13.13. There are two features, and a nearest-neighborhood at a query point is depicted by the circular region. Implicit in near-neighbor classification is the assumption that the class probabilities are roughly constant in the neighborhood, and hence simple averages give good estimates. However, in this example the class probabilities vary only in the horizontal direction. If we knew this, we would stretch the neighborhood in the vertical direction, as shown by the tall rectangular region. This will reduce the bias of our estimate and leave the variance the same.

In general, this calls for adapting the metric used in nearest-neighbor classification, so that the resulting neighborhoods stretch out in directions for which the class probabilities don't change much. In high-dimensional feature space, the class probabilities might change only a low-dimensional subspace and hence there can be considerable advantage to adapting the metric.

Image /page/27/Figure/1 description: The image is a line graph showing the relationship between Dimension on the x-axis and Median Radius on the y-axis. There are three lines representing different values of N: N=100 (green line), N=1,000 (orange line), and N=10,000 (gray line). All lines start at approximately (0,0) and increase as Dimension increases. The green line (N=100) rises the fastest, reaching a Median Radius of approximately 0.6 at Dimension 12. The orange line (N=1,000) rises slower, reaching a Median Radius of approximately 0.5 at Dimension 14. The gray line (N=10,000) rises the slowest, reaching a Median Radius of approximately 0.5 at Dimension 15. A dashed horizontal line is present at a Median Radius of 0.5.

FIGURE 13.12. Median radius of a 1-nearest-neighborhood, for uniform data with  $N$  observations in  $\boldsymbol{p}$  dimensions.

Image /page/27/Figure/3 description: The image is a scatter plot illustrating the concept of 5-Nearest Neighbors. It is titled '5-Nearest Neighborhoods'. The plot displays two classes of data points, represented by green circles and red circles, distributed across a 2D plane. A central black dot represents a query point. A vertical orange shaded region highlights a specific area, and a gray hatched circle surrounds the query point, indicating the neighborhood. The majority of green points are to the left of a vertical black line, while the majority of red points are to the right.

FIGURE 13.13. The points are uniform in the cube, with the vertical line separating class red and green. The vertical strip denotes the 5-nearest-neighbor region using only the horizontal coordinate to find the nearest-neighbors for the target point (solid dot). The sphere shows the 5-nearest-neighbor region using both coordinates, and we see in this case it has extended into the class-red region (and is dominated by the wrong class in this instance).