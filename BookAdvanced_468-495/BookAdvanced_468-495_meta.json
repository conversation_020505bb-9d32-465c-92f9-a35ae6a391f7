{"table_of_contents": [{"title": "12.7 Mixture Discriminant Analysis", "heading_level": null, "page_id": 0, "polygon": [[132.0, 174.7001953125], [363.375, 174.7001953125], [363.375, 188.0419921875], [132.0, 188.0419921875]]}, {"title": "450 12. Flexible Discriminants", "heading_level": null, "page_id": 1, "polygon": [[132.75, 89.25], [277.5, 89.25], [277.5, 98.66162109375], [132.75, 98.66162109375]]}, {"title": "12.7.1 Example: Waveform Data", "heading_level": null, "page_id": 2, "polygon": [[133.5, 488.25], [310.5, 488.25], [310.5, 499.640625], [133.5, 499.640625]]}, {"title": "454 12. Flexible Discriminants", "heading_level": null, "page_id": 5, "polygon": [[132.0, 88.5], [278.25, 88.5], [278.25, 98.5166015625], [132.0, 98.5166015625]]}, {"title": "Computational Considerations", "heading_level": null, "page_id": 6, "polygon": [[133.5, 112.5], [290.162109375, 112.5], [290.162109375, 123.75], [133.5, 123.75]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 6, "polygon": [[132.0, 289.265625], [255.796875, 289.265625], [255.796875, 302.80078125], [132.0, 302.80078125]]}, {"title": "http://www.kernel-machines.org.", "heading_level": null, "page_id": 6, "polygon": [[221.25, 364.5], [367.5, 364.5], [367.5, 374.537109375], [221.25, 374.537109375]]}, {"title": "Exercises", "heading_level": null, "page_id": 6, "polygon": [[132.0, 510.0], [189.755859375, 510.0], [189.755859375, 522.84375], [132.0, 522.84375]]}, {"title": "456 12. Flexible Discriminants", "heading_level": null, "page_id": 7, "polygon": [[132.0, 88.5], [277.013671875, 88.5], [277.013671875, 98.5166015625], [132.0, 98.5166015625]]}, {"title": "http://www-stat.stanford.edu/ElemStatLearn", "heading_level": null, "page_id": 7, "polygon": [[194.25, 279.0], [394.5, 279.0], [394.5, 288.298828125], [194.25, 288.298828125]]}, {"title": "13\nPrototype Methods and\nNearest-Neighbors", "heading_level": null, "page_id": 10, "polygon": [[131.25, 108.75], [330.75, 108.75], [330.75, 181.6611328125], [131.25, 181.6611328125]]}, {"title": "13.1 Introduction", "heading_level": null, "page_id": 10, "polygon": [[133.5, 354.75], [249.9697265625, 354.75], [249.9697265625, 367.3828125], [133.5, 367.3828125]]}, {"title": "13.2 Prototype Methods", "heading_level": null, "page_id": 10, "polygon": [[132.75, 529.5], [293.25, 529.5], [293.25, 542.1796875], [132.75, 542.1796875]]}, {"title": "460 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 11, "polygon": [[132.75, 88.5], [326.25, 88.5], [326.25, 98.7099609375], [132.75, 98.7099609375]]}, {"title": "13.2.1 K-means Clustering", "heading_level": null, "page_id": 11, "polygon": [[133.5, 254.25], [280.5, 254.25], [280.5, 266.255859375], [133.5, 266.255859375]]}, {"title": "462 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 13, "polygon": [[132.60498046875, 88.5], [327.216796875, 88.5], [327.216796875, 98.7099609375], [132.60498046875, 98.7099609375]]}, {"title": "13.2.2 Learning Vector Quantization", "heading_level": null, "page_id": 13, "polygon": [[133.5, 465.75], [328.5, 465.75], [328.5, 476.82421875], [133.5, 476.82421875]]}, {"title": "13.2.3 Gaussian Mixtures", "heading_level": null, "page_id": 14, "polygon": [[133.5, 158.25], [273.0, 158.25], [273.0, 169.3828125], [133.5, 169.3828125]]}, {"title": "13.3 k-Nearest-Neighbor Classifiers", "heading_level": null, "page_id": 14, "polygon": [[132.0, 588.75], [357.75, 588.75], [357.75, 602.89453125], [132.0, 602.89453125]]}, {"title": "464\n13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 15, "polygon": [[132.0, 87.75], [327.0, 87.75], [327.0, 99.0], [132.0, 99.0]]}, {"title": "13. Prototypes and Nearest-Neighbors\n466\\,", "heading_level": null, "page_id": 17, "polygon": [[132.0, 87.0], [327.0, 87.0], [327.0, 99.0], [132.0, 99.0]]}, {"title": "13.3.1 Example: A Comparative Study", "heading_level": null, "page_id": 19, "polygon": [[133.5, 350.947265625], [339.0, 350.947265625], [339.0, 361.775390625], [133.5, 361.775390625]]}, {"title": "470 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [327.515625, 88.5], [327.515625, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "13.3.2 Example: k-Nearest-Neighbors and Image Scene\nClassification", "heading_level": null, "page_id": 21, "polygon": [[133.5, 462.75], [422.25, 462.75], [422.25, 488.8125], [133.5, 488.8125]]}, {"title": "13.3.3 Invariant Metrics and Tangent Distance", "heading_level": null, "page_id": 22, "polygon": [[133.5, 357.0], [384.0, 357.75], [384.0, 368.736328125], [132.75, 368.736328125]]}, {"title": "STATLOG results", "heading_level": null, "page_id": 23, "polygon": [[262.2216796875, 144.75], [339.75, 144.75], [339.75, 154.5], [262.2216796875, 154.5]]}, {"title": "474 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 25, "polygon": [[132.0, 89.25], [326.25, 89.25], [326.25, 98.5166015625], [132.0, 98.5166015625]]}, {"title": "13.4 Adaptive Nearest-Neighbor Methods", "heading_level": null, "page_id": 26, "polygon": [[132.0, 293.90625], [398.337890625, 293.90625], [398.337890625, 306.66796875], [132.0, 306.66796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 49], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5376, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 48], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 52], ["Equation", 3], ["Text", 3], ["TextInlineMath", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "surya", "block_counts": [["Line", 43], ["Span", 9], ["Caption", 3], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1294, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 94], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 921, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 643], ["Line", 290], ["TableCell", 63], ["SectionHeader", 1], ["Text", 1], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7450, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 36], ["Text", 9], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["Line", 45], ["ListItem", 5], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 46], ["ListItem", 5], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 30], ["Text", 5], ["Equation", 4], ["ListItem", 3], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 25], ["Text", 3], ["SectionHeader", 3], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 37], ["Text", 6], ["ListItem", 5], ["SectionHeader", 2], ["ListGroup", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "surya", "block_counts": [["Line", 105], ["Span", 13], ["Figure", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1320, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 36], ["TableCell", 12], ["Text", 8], ["ListItem", 5], ["SectionHeader", 2], ["Equation", 2], ["Table", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 38], ["Text", 3], ["SectionHeader", 2], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "surya", "block_counts": [["Line", 37], ["Span", 11], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1270, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 45], ["Text", 7], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "surya", "block_counts": [["Line", 95], ["Span", 7], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1299, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "surya", "block_counts": [["Line", 46], ["Span", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 68], ["Text", 6], ["Equation", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1159, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 968, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 26], ["Text", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 43], ["TableCell", 9], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1253, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 54], ["Line", 24], ["Figure", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1397, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 35], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 38], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 47], ["TableCell", 16], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 976, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 50], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1472, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_468-495"}