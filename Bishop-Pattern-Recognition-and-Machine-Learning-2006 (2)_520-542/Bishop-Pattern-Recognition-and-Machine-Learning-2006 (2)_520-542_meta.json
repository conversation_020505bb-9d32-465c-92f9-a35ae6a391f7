{"table_of_contents": [{"title": "500 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 0, "polygon": [[30.0, 40.5], [240.0, 40.5], [240.0, 51.3402099609375], [30.0, 51.3402099609375]]}, {"title": "10.6.2 Optimizing the variational parameters", "heading_level": null, "page_id": 0, "polygon": [[138.0, 395.25], [387.3515625, 395.25], [387.3515625, 406.1689453125], [138.0, 406.1689453125]]}, {"title": "10.6.3 Inference of hyperparameters", "heading_level": null, "page_id": 2, "polygon": [[137.443359375, 513.0], [343.5, 513.0], [343.5, 524.865234375], [137.443359375, 524.865234375]]}, {"title": "504 10. APPROX<PERSON><PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 4, "polygon": [[29.25, 39.75], [240.75, 39.75], [240.75, 51.4215087890625], [29.25, 51.4215087890625]]}, {"title": "10.7. Expectation Propagation", "heading_level": null, "page_id": 5, "polygon": [[82.5, 327.30908203125], [274.640625, 327.30908203125], [274.640625, 340.8046875], [82.5, 340.8046875]]}, {"title": "506 10. APPR<PERSON><PERSON><PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 6, "polygon": [[30.0, 40.5], [240.0, 40.5], [240.0, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "508 10. AP<PERSON><PERSON><PERSON><PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 8, "polygon": [[30.0, 40.5], [241.294921875, 40.5], [241.294921875, 50.974365234375], [30.0, 50.974365234375]]}, {"title": "10.7. Expectation Propagation 509", "heading_level": null, "page_id": 9, "polygon": [[285.75, 40.5], [473.25, 40.5], [473.25, 50.9337158203125], [285.75, 50.9337158203125]]}, {"title": "Expectation Propagation", "heading_level": null, "page_id": 9, "polygon": [[137.443359375, 279.75], [254.25, 279.75], [254.25, 290.88720703125], [137.443359375, 290.88720703125]]}, {"title": "510 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 10, "polygon": [[29.1005859375, 40.5], [240.0, 40.5], [240.0, 51.5028076171875], [29.1005859375, 51.5028076171875]]}, {"title": "10.7.1 Example: The clutter problem", "heading_level": null, "page_id": 11, "polygon": [[138.75, 246.0], [345.0, 246.0], [345.0, 256.904296875], [138.75, 256.904296875]]}, {"title": "512 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 12, "polygon": [[29.2236328125, 40.5], [240.1875, 40.5], [240.1875, 51.1776123046875], [29.2236328125, 51.1776123046875]]}, {"title": "10.7.2 Expectation propagation on graphs", "heading_level": null, "page_id": 13, "polygon": [[138.0, 507.0], [376.5, 507.0], [376.5, 519.01171875], [138.0, 519.01171875]]}, {"title": "514 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 14, "polygon": [[29.25, 40.5], [241.171875, 40.5], [241.171875, 51.1776123046875], [29.25, 51.1776123046875]]}, {"title": "516 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 16, "polygon": [[29.25, 40.5], [240.0, 40.5], [240.0, 51.7060546875], [29.25, 51.7060546875]]}, {"title": "Exercises", "heading_level": null, "page_id": 17, "polygon": [[31.5, 399.75], [92.77734375, 400.5], [92.77734375, 413.6484375], [30.75, 413.6484375]]}, {"title": "518 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 18, "polygon": [[29.25, 40.3648681640625], [240.75, 40.3648681640625], [240.75, 51.5841064453125], [29.25, 51.5841064453125]]}, {"title": "520 10. APPROXIMATE INFERENCE", "heading_level": null, "page_id": 20, "polygon": [[30.0, 40.48681640625], [240.75, 40.48681640625], [240.75, 51.787353515625], [30.0, 51.787353515625]]}, {"title": "522 10. APPROXIM<PERSON><PERSON> INFERENCE", "heading_level": null, "page_id": 22, "polygon": [[30.0, 40.5], [240.75, 40.5], [240.75, 51.5028076171875], [30.0, 51.5028076171875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 54], ["Text", 9], ["Equation", 4], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5825, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 453], ["Line", 59], ["Text", 6], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 1740, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 69], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1912, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 47], ["Equation", 9], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1039, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 480], ["Line", 58], ["Equation", 10], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 51], ["Equation", 8], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 49], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 63], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 48], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 47], ["Equation", 6], ["ListItem", 5], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 48], ["Text", 5], ["ListItem", 3], ["Equation", 3], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 38], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Caption", 1], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 651], ["Line", 49], ["Equation", 10], ["Text", 6], ["TextInlineMath", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1160, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 38], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 53], ["Text", 7], ["Equation", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1359, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 591], ["Line", 66], ["Equation", 8], ["TextInlineMath", 3], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 788, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 525], ["Line", 58], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 41], ["ListItem", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 41], ["ListItem", 8], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 38], ["ListItem", 9], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 42], ["ListItem", 7], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 480], ["Line", 42], ["ListItem", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 31], ["Equation", 3], ["ListItem", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_520-542"}