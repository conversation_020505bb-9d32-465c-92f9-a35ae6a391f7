574 21 Isoperimetric-type inequalities

and the first term on the right-hand side vanishes by assumption. Similarly,

$$
\int \frac{|\nabla \rho|^2}{\rho} \left( \frac{\rho^{-\frac{2}{N}}}{\frac{1}{3} + \frac{2}{3}\rho^{-\frac{1}{N}}} \right) = \varepsilon^2 \int |\nabla f|^2 \, d\nu + o(\varepsilon^2).
$$

So (21.12) implies

$$
\frac{N-1}{N}\int \frac{f^2}{2} d\nu \le \frac{1}{2K}\left(\frac{N-1}{N}\right)^2 \int |\nabla f|^2 d\nu,
$$

and then inequality (21.20) follows.

In the case  $N = \infty$ , start from inequality (21.4) and apply a similar reasoning. (It is in fact a well-known property that a logarithmic Sobolev inequality with constant  $K$  implies a Poincaré inequality with constant K.) □

## Bibliographical notes

Popular sources dealing with classical isoperimetric inequalities are the book by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [176], and the survey by <PERSON><PERSON><PERSON> [664]. A very general discussion of isoperimetric inequalities can be found in <PERSON>kov and <PERSON>ud<PERSON> [129]. The subject is related to Poincaré inequalities, as can be seen through Cheeger's isoperimetric inequality (19.32). As part of his huge work on concentration of measure, Talagrand has put forward the use of isoperimetric inequalities in product spaces [772].

There are entire books devoted to logarithmic Sobolev inequalities; this subject goes back at least to Nelson [649] and Gross [441], in relation to hypercontractivity and quantum field theory; but it also has its roots in earlier works by Stam [758], Federbush [351] and Bonami [142]. A gentle introduction, and references, can be found in [41]. The 1992 survey by Gross [442], the Saint-Flour course by Bakry [54] and the book by Royer [711] are classical references. Applications to concentration of measure and deviation inequalities can also be found in those sources, or in Ledoux's synthesis works [544, 546].

The first and most famous logarithmic Sobolev inequality is the one that holds true for the Gaussian reference measure in  $\mathbb{R}^n$  (equation (21.5)). At the end of the fifties, Stam [758] established an inequality which can be recast (after simple changes of functions) as the usual logarithmic Sobolev inequality, found fifteen years later by Gross [441]. Stam's inequality reads  $N I \geq 1$ , where I is the Fisher information, and  $\mathcal N$  is the "power entropy". (In dimension n, this inequality should be replaced by  $\mathcal{N} I \geq n$ .) The main difference between these inequalities is that Stam's is expressed in terms of the Lebesgue reference measure, while Gross's is expressed in terms of the Gaussian reference measure. Although Stam is famous for his information-theoretical inequalities, it was only at the beginning of the nineties that specialists identified a version of the logarithmic Sobolev inequality in his work. (I learnt it from Carlen.) I personally use the name "Stam–Gross logarithmic Sobolev inequality" for (21.5); but this is of course debatable. Stam's argument was slightly flawed because of regularity issues, see [205, 783] for corrected versions.

At present, there are more than fifteen known proofs of (21.5); see Gross [442] for a partial list.

The Bakry–Emery theorem (Theorem 21.2) was proven in [56] by ´ a semigroup method which will be reinterpreted in Chapter 25 as a gradient flow argument. The proof was rewritten in a language of partial differential equations in [43], with emphasis on the link with the convergence to equilibrium for the heat-like equation  $\partial_t \rho = L \rho$ .

The proof of Theorem 21.2 given in these notes is essentially the one that appeared in my joint work with Otto [671]. When the manifold M is  $\mathbb{R}^n$  (and V is K-convex), there is a slightly simpler variant of that argument, due to Cordero-Erausquin [242]; there are also two quite different proofs, one by Caffarelli [188] (based on his log concave perturbation theorem) and one by Bobkov and Ledoux [131] (based on the Brunn–Minkowski inequality in  $\mathbb{R}^n$ ). It is likely that the distorted Prékopa–Leindler inequality (Theorem 19.16) can be used to derive an alternative proof of the Bakry–Emery theorem in the style of Bobkov– Ledoux.

In the definition of logarithmic Sobolev inequality (Definition 21.1), I imposed a Lipschitz condition on the probability density  $\rho$ . Relaxing this condition can be done by tedious approximation arguments in the style of [671, Appendix], [43], and [814, Proof of Theorem 9.17, Step 1]. (In these references this is done only for the Euclidean space.) By the way, the original proof by Bakry and Emery [56] is complete only for ´ compact manifolds. (Only formal computations were established in this paper, and integrations by parts were not taken care of rigorously, neither the proof of ergodicity of the diffusion in the entropy sense. For a compact manifold this is nothing; but on a noncompact manifold, one should be careful about the behavior of the density at infinity.) In his PhD thesis, Demange worked out much more complicated situations in full detail, so there is no doubt that the Bakry–Emery strategy can ´ be made rigorous also on noncompact manifolds, although the proof is probably nowhere to be found.

The Holley–Stroock perturbation theorem for logarithmic Sobolev inequalities, explained in Remark 21.5, was proven in [478]. The other criterion mentioned in Remark 21.5, namely  $\int e^{\alpha |\nabla v|^2} dv < \infty$  for  $\alpha$ large enough, is due to Aida [7]. Related results can be found in a paper by F.-Y. Wang [829].

Another theorem by F.-Y. Wang [828] shows that logarithmic Sobolev inequalities follow from curvature-dimension lower bounds together with square-exponential moments. More precisely, if  $(M, \nu)$  satisfies CD( $-K$ ,  $\infty$ ) for some  $K > 0$ , and  $\int e^{(\frac{K}{2} + \varepsilon) d(x,x_0)^2} \nu(dx) < +\infty$ for some  $\varepsilon > 0$  and  $x_0 \in M$ , then  $\nu$  satisfies a logarithmic Sobolev inequality. Barthe and Kolesnikov [76] derived more general results in the same spirit.

Logarithmic Sobolev inequalities in  $\mathbb{R}^n$  for the measure  $e^{-V(x)} dx$ require a sort of quadratic growth of the potential  $V$ , while Poincaré inequalities require a sort of linear growth. (Convexity is sufficient, as shown by Bobkov [126]; there is now an elementary proof of this fact [74], while refinements and generalizations can be found in [633].) It is natural to ask what happens in between, that is, when  $V(x)$  behaves like  $|x|^\beta$  as  $|x| \to \infty$ , with  $1 < \beta < 2$ . This subject has been studied by Latala and Oleszkiewicz [540], Barthe, Cattiaux and Roberto [75] and Gentil, Guillin and Miclo [410]. The former set of authors chose to focus on functional inequalities which interpolate between Poincaré and log Sobolev, and seem to be due to Beckner [78]; on the contrary, the latter set of authors preferred to focus on modified versions of logarithmic Sobolev inequalities, following the steps of Bobkov and Ledoux [130]. (Modified logarithmic Sobolev inequalities will be studied later, in Chapter 22.)

On the real line, there is a characterization of logarithmic Sobolev inequalities, in terms of weighted Hardy-type inequalities, due to Bobkov and Götze  $[128]$ ; see also Barthe and Roberto  $[77]$ . In the simpler context of Poincaré inequalities, the relation with Hardy inequalities goes back at least to Muckenhoupt [643].

The refinement of the constant in the logarithmic Sobolev inequalities by a dimensional factor of  $N/(N-1)$  is somewhat tricky; see for instance Ledoux [541]. As a limit case, on  $S^1$  there is a logarithmic Sobolev inequality with constant 1, although the Ricci curvature vanishes identically.

The normalized volume measure on a compact Riemannian manifold always satisfies a logarithmic Sobolev inequality, as Rothaus [710] showed long ago. But even on a compact manifold, this result does not diminish the interest of the Bakry–Emery theorem, because the constant in Rothaus' argument is not explicit. Saloff-Coste [727] proved a partial converse: If M has finite volume and Ricci curvature tensor bounded below by  $K$ , and the normalized volume measure satisfies a logarithmic Sobolev inequality with constant  $\lambda > 0$ , then M is compact and there is an explicit upper bound on its diameter:

$$
\text{diam}(M) \le C \sqrt{\text{dim}(M)} \max\left(\frac{1}{\sqrt{\lambda}}, \frac{K_{-}}{\lambda}\right),\tag{21.21}
$$

where C is numeric. (In view of the Bakry–Emery theorem, this can be thought of as a generalization of the Bonnet–Myers bound.) Simplified proofs of this result were given by Ledoux [544], then by Otto and myself [671, Theorem 4].

Like their logarithmic relatives, Sobolev inequalities also fill up books, but usually the emphasis is more on regularity issues. (In fact, for a long time logarithmic Sobolev inequalities and plain Sobolev inequalities were used and studied by quite different communities.) A standard reference is the book by Maz'ja [611], but there are many alternative sources.

A good synthetic discussion of the family (21.7) is in the course by Ledoux [545], which reviews many results obtained by Bakry and collaborators about Sobolev inequalities and  $CD(K, N)$  curvaturedimension bounds (expressed in terms of  $\Gamma$  and  $\Gamma_2$  operators). There it is shown how to deduce some geometric information from (21.7), including the Myers diameter bound (following [58]).

Demange recently obtained a derivation of (21.9) which is, from my point of view, very satisfactory, and will be explained later in Chapter 25. By Demange's method one can establish the following generalization of (21.9): Under adequate regularity assumptions, if  $(M, \nu)$ satisfies the curvature-dimension bound  $CD(K, N), U \in \mathcal{DC}_N$ , and A is defined by  $A(0) = 0$  and  $A(1) = 0$ ,  $A''(r) = r^{-1/N}U''(r)$ , then for any probability density  $\rho$ ,

578 21 Isoperimetric-type inequalities

$$
\int_M A(\rho) d\nu \le \frac{1}{2K} \int_M \rho^{1-\frac{1}{N}} \left| \nabla U'(\rho) \right|^2 d\nu.
$$

Many variants, some of them rather odd-looking, appear in Demange's work [290, 291, 293]. For instance, he is able to establish seemingly sharp inequalities for nonlinearities  $U$  satisfying the following condition:

$$
\frac{d}{dr}\left[r\left(\frac{r U''(r)}{U'(r)}+\frac{1}{N}\right)\right] \ge \frac{9N}{4(N+2)}\left(\frac{r U''(r)}{U'(r)}+\frac{1}{N}\right)^2.
$$

Demange also suggested that (21.8) might imply (21.6). It is interesting to note that (21.6) can be proven by a simple transport argument, while no such thing is known for (21.8).

The proof of Theorem 21.9 is taken from a collaboration with Lott [578].

The use of transport methods to study isoperimetric inequalities in  $\mathbb{R}^n$  goes back at least to Knothe [523]. Gromov [635, Appendix] revived the interest in Knothe's approach by using it to prove the isoperimetric inequality in  $\mathbb{R}^n$ . Recently, the method was put to a higher degree of sophistication by Cordero-Erausquin, Nazaret and myself [248]. In this work, we recover general optimal Sobolev inequalities in  $\mathbb{R}^n$ , together with some families of optimal Gagliardo–Nirenberg inequalities. (The proof of the Sobolev inequalities is reproduced in [814, Theorem 6.21].) The results themselves are not new, since optimal Sobolev inequalities in  $\mathbb{R}^n$  were established independently by Aubin, Talenti and Rodemich in the seventies (see [248] for references), while the optimal Gagliardo– Nirenberg inequalities were discovered recently by Del Pino and Dolbeault [283]. However, I think that all in all the transport approach is simpler, especially for the Gagliardo–Nirenberg family. In [248] the optimal Sobolev inequalities came with a "dual" family of inequalities, that can be interpreted as a particular case of so-called Faber–Krahn inequalities; there is still (at least for me) some mystery in this duality.

In the present chapter, I have modified slightly the argument of [248] to avoid the use of Alexandrov's theorem about second derivatives of convex functions (Theorem 14.25). The advantage is to get a more elementary proof; however, the computations are less precise, and some useful "magic" cancellations (such as  $x + (\nabla \varphi - x) = \nabla \varphi$ ) are not available any longer; I used a homogeneity argument to get around this problem. A drawback of this approach is that the discussion about cases of equality is not possible any longer (anyway a clean discussion of equality cases requires much more effort; see [248, Section 4]). The

proof presented here should work through if  $\mathbb{R}^n$  is replaced by a cone with nonnegative Ricci curvature, although I did not check details.

The homogeneity (under dilations) of the function  $-\int \rho^{1-1/N}$  is used in this argument to transform a seemingly nonoptimal inequality into the optimal one; I wonder whether a similar argument could lead from  $(21.6)$  to  $(21.8)$  on the sphere — but what would play the role of homogeneity? In fact, homogeneity also underlies [248]: if one wishes to interpret the proof in terms of "Otto calculus" it all amounts to the fact that  $-\int \rho^{1-1/N}$  is displacement convex and homogeneous. The Euclidean analog is the following: if  $\Phi$  is convex on a Euclidean space and minimal at 0 then  $\Phi(X) \leq X \cdot \nabla \Phi(X) + |\nabla \Phi(X)|^2/2$ ; but if in addition  $\Phi$  is  $\lambda$ -homogeneous  $(\Phi(tx) = t^{\lambda} \Phi(x))$  then  $X \cdot \nabla \Phi(X) =$  $\lambda \Phi(X)$  (Euler relation), so

$$
\Phi(X) \le X \cdot \nabla \Phi(X) + \frac{|\nabla \Phi(X)|^2}{2} = \lambda \Phi(X) + \frac{|\nabla \Phi(X)|^2}{2},
$$

so if  $\lambda < 1$  we obtain  $\Phi(X) \leq |\nabla \Phi(X)|^2/(2(1-\lambda)).$ 

Another inequality for which it would be interesting to have a transport proof is the Hardy–Littlewood–Sobolev inequality. Recently Calvez and Carrillo [198] obtained such a proof in dimension 1; further investigation is under way.

After [248], Maggi and I pushed the method even further [587], to recover "very optimal" Sobolev inequalities with trace terms, in  $\mathbb{R}^n$ . This settled some problems that had been left open in a classical work by Brézis and Lieb  $[173]$ . Much more information can be found in  $[587]$ ; recently we also wrote a sequel [588] in which limit cases (such as inequalities of Moser–Trudinger type) are considered.

As far as all these applications of transport to Sobolev or isoperimetric inequalities in  $\mathbb{R}^n$  are concerned, the Knothe coupling works just about as fine as the optimal coupling. (As a matter of fact, many such inequalities are studied in [134, 135] via the Knothe coupling.) But the choice of coupling method becomes important for refinements such as the characterization of minimizers [248], or even more establishing quantitative stability estimates around minimizers. This point is discussed in detail by Figalli, Maggi and Pratelli [369] who use the optimal coupling to refine Gromov's proof of isoperimetry, to the point of obtaining a beautiful (and sharp) quantitative isoperimetric theorem, giving a lower bound on how much the isoperimetric ratio of a set departs from the optimal ratio, in terms of how much the shape of this set departs from the optimal shape.

## 580 21 Isoperimetric-type inequalities

Interestingly enough, the transport method in all of these works is insensitive to the choice of norm in  $\mathbb{R}^n$ . (I shall come back to this observation in the concluding chapter.) Lutwak, Yang and Zhang [582] developed this remark and noticed that if a function  $f$  is given, then the problem of minimizing  $\|\nabla f\|_{L^1}$  over all norms on  $\mathbb{R}^n$  can be related to Minkowski's problem of prescribed Gauss curvature. The isoperimetric problem for a non-Euclidean norm, also known as the Wulff problem, is not an academic issue, since it is used in the modeling of surface energy (see the references in [369]).

In a Euclidean context, there are other more classical methods to attack these problems, such as rearrangement or symmetrization. For instance, quantitative symmetrization inequalities were used in [389] to prove the stability of the sharp Euclidean isoperimetric inequality. But even in this case, as noticed by Maggi [586], the most efficient strategy seems to be a combination of quantitative symmetrization inequalities with optimal transport techniques (for an auxiliary one-dimensional problem in that case). Similarly, the stability of optimal Sobolev inequalities is treated in [234, 388] by a combination of quantitative symmetrization (to reduce to radially symmetric functions) and optimal transport (with a quantitative version of [248] on radially symmetric functions).

In any case, if the reader is looking for a transport argument related to some geometric inequality in  $\mathbb{R}^n$ , I personally advise him or her to try the Knothe coupling first, and if this turns out to be insufficient because of some geometric reason, to go on with the optimal transport.

The Lévy–Gromov inequality was first conjectured by Lévy in the case when the manifold  $M$  is the boundary of a uniformly convex set (so the sectional curvatures are bounded below by a positive constant). Lévy thought he had a proof, but his argument was faulty and repaired by Gromov [438]. A lecture about Gromov's proof is available in [663]; one may also consult [394, Section 4.H].

There have also been some striking works by Bobkov, Bakry and Ledoux on the infinite-dimensional version of the Lévy–Gromov inequality (often called Gaussian isoperimetry); for this inequality there is an elegant functional formulation [57, 125], and the extremal subsets are half-spaces, rather than balls [145, 766]. On that subject I warmly recommend (as usual) the synthesis works by Ledoux [542, 543]. Further, see [76] where various isoperimetric inequalities are obtained via optimal transport.

In finite dimension, functional versions of the Lévy–Gromov inequality are also available [70, 740], but the picture is not so clear as in the infinite-dimensional case.

The Lichnerowicz spectral gap theorem is usually encountered as a simple application of the Bochner formula; it was obtained almost half a century ago by Lichnerowicz [553, p. 135]; see [100, Section D.I.] or [394, Theorem 4.70] for modern presentations. The above proof of Theorem 21.20 is a variant of the one which appears in my joint work with Lott [578]. Although less simple than the classical proof, it has the advantage, for the purpose of these notes, to be based on optimal transport. This is actually, to my knowledge, the first time that the dimensional refinement in the constants by a factor  $N/(N-1)$  in an "infinite-dimensional functional inequality" has been obtained from a transport argument.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.