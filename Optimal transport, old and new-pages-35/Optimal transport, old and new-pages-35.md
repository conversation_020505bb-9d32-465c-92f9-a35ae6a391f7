The theory of concentration of measure is a collection of results, tools and recipes built on the idea that if a set A is given in a metric probability space  $(\mathcal{X}, d, \mathbb{P})$ , then the enlargement  $A^r := \{x; d(x, A) \leq r\}$ might acquire a very high probability as  $r$  increases. There is an equivalent statement that Lipschitz functions  $\mathcal{X} \to \mathbb{R}$  are "almost constant" in the sense that they have a very small probability of deviating from some typical quantity, for instance their mean value. This theory was founded by <PERSON><PERSON><PERSON> and later developed by many authors, in particular <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>.

To understand the relation between the two sides of concentration (sets and functions), it is most natural to think in terms of median, rather than mean value. By definition, a real number  $m_f$  is a median of the random variable  $f : \mathcal{X} \to \mathbb{R}$  if

$$
\mathbb{P}[f \ge m_f] \ge \frac{1}{2}; \qquad \mathbb{P}[f \le m_f] \ge \frac{1}{2}.
$$

Then the two statements

(a) 
$$
\forall A \subset \mathcal{X}, \forall r \ge 0, \quad \mathbb{P}[A] \ge 1/2 \implies \mathbb{P}[A^r] \ge 1 - \psi(r)
$$
  
(b)  $\forall f \in \text{Lip}(\mathcal{X}), \forall r \ge 0, \quad \mathbb{P}[f > m_f + r] \le \psi(r/\|f\|_{\text{Lip}})$ 

are equivalent. Indeed, to pass from (a) to (b), first reduce to the case  $||f||_{\text{Lip}} = 1$  and let  $A = \{f \le m_f\}$ ; conversely, to pass from (b) to (a), let  $f = d(\cdot, A)$  and note that 0 is a median of f.

The typical and most emblematic example of concentration of measure occurs in the Gaussian probability space  $(\mathbb{R}^n, \gamma)$ :

$$
\gamma[A] \ge \frac{1}{2} \implies \forall r \ge 0, \quad \gamma[A^r] \ge 1 - e^{-\frac{r^2}{2}}.
$$
 (22.1)

Here is the translation in terms of Lipschitz functions: If  $X$  is a Gaussian random variable with law  $\gamma$ , then for all Lipschitz functions  $f:\mathbb{R}^n\to\mathbb{R},$ 

$$
\forall r \ge 0, \quad \mathbb{P}\left[f(X) \ge \mathbb{E} f(X) + r\right] \le \exp\left(-\frac{r^2}{2\|f\|_{\text{Lip}}^2}\right). \tag{22.2}
$$

Another famous example is the unit sphere  $S^N$ : if  $\sigma^N$  stands for the normalized volume on  $S^N$ , then the formulas above can be replaced by

$$
\sigma^N[A] \ge \frac{1}{2} \Longrightarrow \sigma^N[A^r] \ge 1 - e^{-\frac{(N-1)}{2}r^2},
$$
  
$$
\mathbb{P}\left[f(X) \ge \mathbb{E}f(X) + r\right] \le \exp\left(-\frac{(N-1)r^2}{2\|f\|_{\text{Lip}}^2}\right).
$$

In this example we see that the phenomenon of concentration of measure becomes more and more important as the dimension increases to infinity.

In this chapter I shall review the links between optimal transport and concentration, focusing on certain transport inequalities. The main results will be Theorems 22.10 (characterization of Gaussian concentration), 22.14 (concentration via Ricci curvature bounds), 22.17 (concentration via logarithmic Sobolev inequalities), 22.22 (concentration via Talagrand inequalities) and  $22.25$  (concentration via Poincaré inequalities). The chapter will be concluded by a recap, and a technical appendix about Hamilton–Jacobi equations, of independent interest.

## Optimal transport and concentration

As first understood by Marton, there is a simple and robust functional approach to concentration inequalities based on optimal transport. One can encode some information about the concentration of measure with respect to some reference measure  $\nu$ , by functional inequalities of the form

$$
\forall \mu \in P(\mathcal{X}), \quad C(\mu, \nu) \le \mathcal{E}_{\nu}(\mu), \tag{22.3}
$$

where  $C(\mu, \nu)$  is the optimal transport cost between  $\mu$  and  $\nu$ , and  $\mathcal{E}_{\nu}$  is some local nonlinear functional ("energy") of  $\mu$ , involving for instance the integral of a function of the density of  $\mu$  with respect to  $\nu$ .

This principle may be heuristically understood as follows. To any measurable set A, associate the conditional measure  $\mu_A = (1_A/\nu[A]) \nu$ . If the measure of A is not too small, then the associated energy  $\mathcal{E}_{\nu}(\mu_A)$ will not be too high, and by (22.3) the optimal transport cost  $C(\mu_A, \nu)$ will not be too high either. In that sense, the whole space  $\mathcal X$  can be considered as a "small enlargement" of just A.

Here is a fluid mechanics analogy: imagine  $\mu$  as the density of a fluid. The term on the right-hand side of (22.3) measures how difficult it is to prepare  $\mu$ , for instance to confine it within a set A (this has to do with the measure of  $A$ ); while the term on the left-hand side says how difficult it is for the fluid to invade the whole space, after it has been prepared initially with density  $\mu$ .

The most important class of functional inequalities of the type (22.3) occurs when the cost function is of the type  $c(x, y) = d(x, y)^p$ , and the "energy" functional is the square root of Boltzmann's  $H$  functional,

$$
H_{\nu}(\mu) = \int \rho \log \rho \, d\nu, \quad \mu = \rho \, \nu,
$$

with the understanding that  $H_{\nu}(\mu) = +\infty$  if  $\mu$  is not absolutely continuous with respect to  $\nu$ . Here is a precise definition of these functional inequalities:

**Definition 22.1** ( $T_p$  inequality). Let  $(\mathcal{X}, d)$  be a Polish space and let  $p \in [1,\infty)$ . Let  $\nu$  be a reference probability measure in  $P_p(\mathcal{X})$ , and let  $\lambda > 0$ . It is said that v satisfies a  $T_p$  inequality with constant  $\lambda$  if

$$
\forall \mu \in P_p(\mathcal{X}), \qquad W_p(\mu, \nu) \le \sqrt{\frac{2 \, H_\nu(\mu)}{\lambda}}.\tag{22.4}
$$

These inequalities are often called transportation-cost inequalities, or Talagrand inequalities, although the latter denomination is sometimes restricted to the case  $p = 2$ .

**Remark 22.2.** Since  $W_p \leq W_q$  for  $p \leq q$ , the  $T_p$  inequalities become stronger and stronger as  $p$  increases. The inequalities  $T_1$  and  $T_2$  have deserved most attention. It is an experimental fact that  $T_1$  is more handy and flexible, while  $T_2$  has more geometric content, and behaves better in large dimensions (see for instance Corollary 22.6 below).

There are two important facts to know about  $T_p$  inequalities when p varies in the range  $[1, 2]$ : they admit a *dual formulation*, and they tensorize. These properties are described in the two propositions below.

**Proposition 22.3 (Dual formulation of**  $T_p$ ). Let  $(\mathcal{X}, d)$  be a Polish space,  $p \in [1,2]$  and  $\nu \in P_p(\mathcal{X})$ . Then the following two statements are equivalent:

(a) 
$$
\nu
$$
 satisfies  $T_p(\lambda)$ ;  
\n(b) For any  $\varphi \in C_b(\mathcal{X})$ ,  
\n
$$
\begin{cases}\n\forall t \ge 0 \quad \int e^{\lambda t \inf_{y \in \mathcal{X}} \left[\varphi(y) + \frac{d(x,y)^p}{p}\right]} \nu(dx) \le e^{\lambda \left(\frac{1}{p} - \frac{1}{2}\right) t^{\frac{2}{2-p}}} e^{\lambda t \int \varphi \, d\nu} & (p < 2) \\
\int e^{\lambda \inf_{y \in \mathcal{X}} \left[\varphi(y) + \frac{d(x,y)^2}{2}\right]} \nu(dx) \le e^{\lambda \int \varphi \, d\nu} & (p = 2).\n\end{cases}
$$
\n(22.5)

Particular Case 22.4 (Dual formulation of  $T_1$ ). Let  $(\mathcal{X}, d)$  be a Polish space and  $\nu \in P_1(\mathcal{X})$ , then the following two statements are equivalent:

- (a)  $\nu$  satisfies  $T_1(\lambda)$ ;
- (b) For any  $\varphi \in C_b(\mathcal{X}),$

$$
\forall t \ge 0 \quad \int e^{t \inf_{y \in \mathcal{X}} \left[ \varphi(y) + d(x, y) \right]} \, \nu(dx) \le e^{\frac{t^2}{2\lambda}} \, e^{t \int \varphi \, d\nu}.
$$
 (22.6)

**Proposition 22.5 (Tensorization of**  $T_p$ ). Let  $(\mathcal{X}, d)$  be a Polish space,  $p \in [1,2]$  and let  $\nu \in P_p(\mathcal{X})$  be a reference probability measure satisfying an inequality  $T_p(\lambda)$ . Then for any  $N \in \mathbb{N}$ , the measure  $\nu^{\otimes N}$ satisfies an inequality  $T_p(N^{1-\frac{2}{p}}\lambda)$  on  $(\mathcal{X}^N,d_p,\nu^{\otimes N})$ , where the product distance  $d_p$  is defined by

$$
d_p((x_1,...,x_N); (y_1,...,y_N)) = \left(\sum_{i=1}^N d(x_i,y_i)^p\right)^{\frac{1}{p}}.
$$

Corollary 22.6 ( $T_2$  inequalities tensorize exactly). If v satisfies  $T_2(\lambda)$ , then also  $\mu^{\otimes N}$  satisfies  $T_2(\lambda)$  on  $(\mathcal{X}^N, d_2, \nu^{\otimes N})$ , for any  $N \in \mathbb{N}$ .

Proof of Proposition 22.3. Proposition 22.3 will be obtained as a consequence of Theorem 5.26. Recall the Legendre representation of the *H*-functional: For any  $\lambda > 0$ ,

$$
\begin{cases} \forall \mu \in P(\mathcal{X}), & \frac{H_{\nu}(\mu)}{\lambda} = \sup_{\varphi \in C_b(\mathcal{X})} \left[ \int \varphi \, d\mu - \frac{1}{\lambda} \log \left( \int_{\mathcal{X}} e^{\lambda \varphi} \, d\nu \right) \right] \\ \forall \varphi \in C_b(\mathcal{X}), & \frac{1}{\lambda} \log \left( \int_{\mathcal{X}} e^{\lambda \varphi} \, d\nu \right) = \sup_{\mu \in P(\mathcal{X})} \left[ \int \varphi \, d\mu - \frac{H_{\nu}(\mu)}{\lambda} \right] \end{cases}
$$
(22.7)

(See the bibliographical notes for proofs of these identities.)

Let us first treat the case  $p = 2$ . Apply Theorem 5.26 with  $c(x,y) = d(x,y)^2/2$ ,  $F(\mu) = (1/\lambda)H_{\nu}(\mu)$ ,  $\Lambda(\varphi) = (1/\lambda) \log \left( \int e^{\lambda \varphi} d\nu \right)$ . The conclusion is that  $\nu$  satisfies  $T_2(\lambda)$  if and only if

$$
\forall \phi \in C_b(\mathcal{X}), \quad \log \int \exp \left( \lambda \int \phi \, d\nu - \lambda \phi^c \right) \, d\nu \le 0,
$$

i.e.

$$
\int e^{-\lambda \phi^c} \, d\nu \le e^{-\lambda \int \phi \, d\nu},
$$

where  $\phi^c(x) := \sup_y (\phi(y) - d(x, y)^2/2)$ . Upon changing  $\phi$  for  $\varphi = -\phi$ , this is the desired result. Note that the Particular Case 22.4 is obtained from  $(22.5)$  by choosing  $p = 1$  and performing the change of variables  $t \rightarrow \lambda t$ .

The case  $p < 2$  is similar, except that now we appeal to the equivalence between (i') and (ii') in Theorem 5.26, and choose

$$
c(x,y) = \frac{d(x,y)^p}{p}; \qquad \Phi(r) = \frac{p^{\frac{2}{p}}}{2} r^{\frac{2}{p}} 1_{r \ge 0}; \qquad \Phi^*(t) = \left(\frac{1}{p} - \frac{1}{2}\right) t^{\frac{2}{2-p}}.
$$

Proof of Proposition 22.5. First we need to set up a bit of notation. Let  $\mu = \mu(dx_1 dx_2 \ldots dx_N)$  be a probability measure on  $\mathcal{X}^N$ , and let  $(x_1,...,x_N) \in \mathcal{X}^N$  be distributed randomly according to  $\mu$ . I shall write  $\mu_1(dx_1)$  for the law of  $x_1, \mu_2(dx_2|x_1)$  for the conditional law of  $x_2$  given  $x_1$ ,  $\mu_3(dx_3|x_1,x_2)$  for the conditional law of  $x_3$  given  $x_1$  and  $x_2$ , etc. I shall also use the shorthand  $x^i = (x_1, x_2, \ldots, x_i)$  (with the convention that  $x^0 = \emptyset$ , and write  $\mu^i$  for the law of  $x^i$ .

The proof is reminiscent of the strategy used to construct the Knothe–Rosenblatt coupling. First choose an optimal coupling (for the cost function  $c = d^p$ ) between  $\mu_1(dx_1)$  to  $\nu(dy_1)$ , call it  $\pi_1(dx_1 dy_1)$ . Then for each  $x_1$ , choose an optimal coupling between  $\mu_2(dx_2|x_1)$  and  $\nu(dy_2)$ , call it  $\pi_2(dx_2 dy_2|x_1)$ . Then for each  $(x_1, x_2)$ , choose an optimal

coupling between  $\mu_3(dx_3|x_1,x_2)$  and  $\nu(dy_3)$ , call it  $\pi_3(dx_3 dy_3|x_1,x_2)$ ; etc. In the end, glue these plans together to get a coupling

$$
\pi(dx_1 dy_1 dx_2 dy_2 \dots dx_N dy_N) \\ = \pi_1(dx_1 dy_1) \pi_2(dx_2 dy_2 | x_1) \pi_3(dx_3 dy_3 | x_1, x_2) \dots \\ \dots \pi_N(dx_N dy_N | x_1, \dots, x_{N-1}).
$$

In more compact notation,

$$
\pi(dx\,dy)=\pi_1(dx_1\,dy_1)\,\pi_2(dx_2\,dy_2|x^1)\ldots\,\pi_N(dx_N\,dy_N|x^{N-1}).
$$

Here something should be said about the measurability, since there is a priori no canonical way to choose  $\pi_i(\cdot | x^{i-1})$  as a function of  $x^{i-1}$ . But Corollary 5.22 ensures that this choice can be made in a measurable way.

By the definition of  $d_p$ ,

$$
\mathbb{E}_{\pi} d_p(x, y)^p = \sum_{i=1}^N \mathbb{E}_{\pi} d(x_i, y_i)^p
$$
  
= 
$$
\sum_{i=1}^N \int \left[ \mathbb{E}_{\pi(\cdot|x^{i-1})} d(x_i, y_i)^p \right] \pi^{i-1} (dx^{i-1} dy^{i-1})
$$
  
= 
$$
\sum_{i=1}^N \int \left[ \mathbb{E}_{\pi(\cdot|x^{i-1})} d(x_i, y_i)^p \right] \mu^{i-1} (dx^{i-1}), \qquad (22.8)
$$

where of course

$$
\pi^{i}(dx^{i} dy^{i}) = \pi_{1}(dx_{1} dy_{1}) \pi_{2}(dx_{2} dy_{2}|x^{1}) \dots \pi_{i}(dx_{i} dy_{i}|x^{i-1}).
$$

For each i and each  $x^{i-1} = (x_1, \ldots, x_{i-1})$ , the measure  $\pi(\cdot | x^{i-1})$  is an optimal transference plan between its marginals. So the right-hand side of (22.8) can be rewritten as

$$
\sum_{i=1}^{N} \int W_p(\mu_i(\cdot|x^{i-1}), \nu)^p \mu^{i-1}(dx^{i-1}).
$$

Since this cost is achieved for the transference plan  $\pi$ , we obtain the key estimate

$$
W_p(\mu, \nu^{\otimes N})^p \le \sum_{i=1}^N \int W_p(\mu_i(\cdot | x^{i-1}), \nu)^p \mu^{i-1}(dx^{i-1}). \tag{22.9}
$$

By assumption,  $\nu$  satisfies  $T_p(\lambda)$ , so the right-hand side in (22.9) is bounded above by

$$
\sum_{i} \int \left( \frac{2}{\lambda} H_{\nu}(\mu_i(\cdot | x^{i-1})) \right)^{\frac{p}{2}} \mu^{i-1}(dx^{i-1}). \tag{22.10}
$$

Since  $p \leq 2$ , we can apply Hölder's inequality, in the form  $\sum_{i \leq N} a_i^{p/2} \leq$  $N^{1-p/2}(\sum a_i)^{p/2}$ , and bound (22.10) by

$$
N^{1-\frac{p}{2}}\left(\frac{2}{\lambda}\right)^{\frac{p}{2}}\left[\int \left(\sum_{i=1}^{N} H_{\nu}\left(\mu_i(\cdot|x^{i-1})\right)\right)\mu^{i-1}(dx^{i-1})\right]^{\frac{p}{2}}.\tag{22.11}
$$

But the formula of additivity of entropy (Lemma 22.8 below) states that

$$
\sum_{1 \le i \le N} \int H_{\nu}(\mu_i(dx_i | x^{i-1})) \mu^{i-1}(dx^{i-1}) = H_{\nu^{\otimes N}}(\mu). \tag{22.12}
$$

Putting all the previous bounds back together, we end up with

$$
W_p(\mu, \nu^{\otimes N})^p \le N^{1-\frac{p}{2}} \left(\frac{2}{\lambda}\right)^{\frac{p}{2}} H_{\nu^{\otimes N}}(\mu)^{\frac{p}{2}},
$$

which is equivalent to the desired inequality. □

Remark 22.7. The same proof shows that the inequality

$$
\forall \mu \in P(\mathcal{X}), \qquad C(\mu, \nu) \le H_{\nu}(\mu)
$$

implies

$$
\forall \mu \in P(\mathcal{X}^N), \qquad C^N(\mu, \nu) \le H_{\nu^{\otimes N}}(\mu),
$$

where  $C^N$  is the optimal transport cost associated with the cost function

$$
c^N(x, y) = \sum c(x_i, y_i)
$$

on  $\mathcal{X}^N$ .

The following important lemma was used in the course of the proof of Proposition 22.5.

[]

Lemma 22.8 (Additivity of entropy). Let  $N \in \mathbb{N}$ , let  $\mathcal{X}_1, \ldots, \mathcal{X}_N$ be Polish spaces,  $\nu_i \in P(\mathcal{X}_i)$   $(1 \leq i \leq N)$ ,  $\mathcal{X} = \prod \mathcal{X}_i$ ,  $\nu = \bigotimes \nu_i$ , and  $\mu \in P(X)$ . Then, with the same notation as in the beginning of the proof of Proposition 22.5,

$$
H_{\nu}(\mu) = \sum_{1 \le i \le N} \int H_{\nu_i}(\mu_i(dx_i | x^{i-1})) \mu^{i-1}(dx^{i-1}). \tag{22.13}
$$

*Proof of Lemma 22.8.* By induction, it suffices to treat the case  $N = 2$ . Let  $\rho = \rho(x_1, x_2)$  be the density of  $\mu$  with respect to  $\nu_1 \otimes \nu_2$ . By an easy approximation argument based on the monotone convergence theorem, it is sufficient to establish (22.13) in the case when  $\rho$  is bounded.

The measure  $\mu_1(dx_1)$  has density  $\int \rho(x_1,x_2) \nu_2(dx_2)$ , while the conditional measure  $\mu_2(dx_2|x_1)$  has density  $\rho(x_1, x_2)/(\int \rho(x_1, x_2) \nu_2(dx_2)).$ From this and the additive property of the logarithm, we deduce

$$
\int H_{\nu}(\mu_2(\cdot|x_1)) \mu_1(dx_1)
$$

$$
= \int \left( \int \frac{\rho(x_1, x_2)}{\int \rho(x_1, x_2') \nu_2(dx_2')} \log \frac{\rho(x_1, x_2)}{\int \rho(x_1, x_2') \nu_2(dx_2')} \nu_2(dx_2) \right) \left( \int \rho(x_1, x_2') \nu_2(dx_2') \right) \nu_1(dx_1)
$$

$$
= \iint \rho(x_1, x_2) \log \rho(x_1, x_2) \nu_2(dx_2) \nu_1(dx_1)
$$

$$
- \int \left( \int \rho(x_1, x_2) \nu_2(dx_2) \right) \log \left( \int \rho(x_1, x_2) \nu_2(dx_2) \right) \nu_1(dx_1)
$$

$$
= H_{\nu}(\mu) - H_{\nu_1}(\mu_1).
$$

This concludes the proof. □

Exercise 22.9. Give an alternative proof of Proposition 22.5 based on the dual formulation of  $T_p$  inequalities (Proposition 22.3).

## Gaussian concentration and $T_1$ inequality

Gaussian concentration is a loose terminology meaning that some reference measure enjoys properties of concentration which are similar to those of the Gaussian measure. In this section we shall see that a certain form of Gaussian concentration is *equivalent* to a  $T_1$  inequality. Once again, this principle holds in very general metric spaces.

Theorem 22.10 (Gaussian concentration). Let  $(\mathcal{X}, d)$  be a Polish space, equipped with a reference probability measure  $\nu$ . Then the following properties are all equivalent:

- (i)  $\nu$  lies in  $P_1(\mathcal{X})$  and satisfies a  $T_1$  inequality;
- (ii) There is  $\lambda > 0$  such that for any  $\varphi \in C_b(\mathcal{X}),$

$$
\forall t \geq 0 \quad \int e^{t \inf_{y \in \mathcal{X}}} \left[ \varphi(y) + d(x, y) \right] \, \nu(dx) \leq e^{\frac{t^2}{2\lambda}} \, e^{t \int \varphi \, d\nu}.
$$

(iii) There is a constant  $C > 0$  such that for any Borel set  $A \subset \mathcal{X}$ ,

$$
\nu[A] \ge \frac{1}{2} \Longrightarrow \quad \forall r > 0, \quad \nu[A^r] \ge 1 - e^{-Cr^2}.
$$

(iv) There is a constant  $C > 0$  such that

 $\forall f \in L^1(\nu) \cap \text{Lip}(\mathcal{X}), \ \forall \varepsilon > 0,$ 

$$
\nu\Big[\big\{x\in\mathcal{X};\ f(x)\geq\int f\,d\nu+\varepsilon\big\}\Big]\leq\exp\left(-C\frac{\varepsilon^2}{\|f\|_{\mathrm{Lip}}^2}\right);
$$

(v) There is a constant  $C > 0$  such that

$$
\forall f \in L^{1}(\nu) \cap \text{Lip}(\mathcal{X}), \ \forall \varepsilon > 0, \ \forall N \in \mathbb{N},
$$
$$
\nu^{\otimes N} \Big[ \big\{ x \in \mathcal{X}^{N}; \ \frac{1}{N} \sum_{i=1}^{N} f(x_{i}) \ge \int f \, d\nu + \varepsilon \big\} \Big] \le \exp \left( -C \frac{N \, \varepsilon^{2}}{\|f\|_{\text{Lip}}^{2}} \right);
$$

(*vi*) There is a constant  $C > 0$  such that

$$
\forall f \in \text{Lip}(\mathcal{X}), \ \forall \ m_f = \text{median of } f, \ \forall \varepsilon > 0,
$$
$$
\nu [ \{ x \in \mathcal{X}; \ f(x) \ge m_f + \varepsilon \} ] \le \exp \left( -C \frac{\varepsilon^2}{\|f\|_{\text{Lip}}^2} \right);
$$

(vii) For any  $x_0 \in \mathcal{X}$  there is a constant  $a > 0$  such that

$$
\int e^{a d(x_0, x)^2} \nu(dx) < +\infty;
$$

(*viii*) There exists  $a > 0$  such that

$$
\int e^{a d(x,y)^2} \nu(dx) \nu(dy) < +\infty;
$$

(ix) There exist  $x_0 \in \mathcal{X}$  and  $a > 0$  such that

$$
\int e^{a d(x_0, x)^2} \nu(dx) < +\infty.
$$

Remark 22.11. One should not overestimate the power of Theorem 22.10. The simple (too simple?) criterion (ix) behaves badly in large dimensions, and in practice might lead to terrible constants at the level of (iii). In particular, this theorem alone is unable to recover dimensionfree concentration inequalities such as  $(22.1)$  or  $(22.2)$ . Statement  $(v)$ is dimension-independent, but limited to particular observables of the form  $(1/N) \sum f(x_i)$ . Here we see some limitations of the  $T_1$  inequality.

*Proof of Theorem 22.10.* We shall establish (i)  $\Rightarrow$  (ii)  $\Rightarrow$  (iv)  $\Rightarrow$  (vii),  $(i) \Rightarrow (v) \Rightarrow (iv), (i) \Rightarrow (iii) \Rightarrow (vi) \Rightarrow (vii) \Rightarrow (viii) \Rightarrow (ix) \Rightarrow (i),$  and this will prove the theorem.

 $(i) \Rightarrow (ii)$  was already seen in Particular Case 22.4.

To prove (ii)  $\Rightarrow$  (iv), it suffices to treat the case  $||f||_{\text{Lip}} = 1$  (replace  $\varepsilon$  by  $\varepsilon/\Vert f\Vert_{\text{Lip}}$  and f by  $f/\Vert f\Vert_{\text{Lip}}$ ). Then if f is 1-Lipschitz,

$$
\inf_{y \in \mathcal{X}} [f(y) + d(x, y)] = f(x),
$$

so (ii) implies

$$
\int e^{t f(x)} \nu(dx) \leq e^{\frac{t^2}{2\lambda}} e^{t \int f dx}.
$$

With the shorthand  $\langle f \rangle = \int f d\nu$ , this is the same as

$$
\int e^{t (f - \langle f \rangle)} d\nu \le e^{\frac{t^2}{2\lambda}}.
$$

Then by the exponential Chebyshev inequality,

$$
\nu\Big[\big\{f - \langle f \rangle \ge \varepsilon\big\}\Big] \le e^{-t\varepsilon} \int e^{t\,(f - \langle f \rangle)} \, d\nu \le e^{-t\varepsilon} e^{\frac{t^2}{2\lambda}};
$$

and (iv) is obtained by optimizing in t.  $(C = \lambda/2$  does the job.)

Now let us prove (iv)  $\Rightarrow$  (vii). Let  $\nu$  satisfy (iv) and let  $x_0 \in \mathcal{X}$ . First we shall check that  $d(\cdot, x_0) \in L^1(\nu)$ . Let  $m \in \mathbb{N}$ , and let  $f_m =$  $d(\cdot, x_0) \wedge m$ ; then  $f_m \in L^1(\nu) \cap \text{Lip}(\mathcal{X}),$  so

$$
\nu[f_m \ge s + \int f_m \, d\nu] \le e^{-Cs^2}.
$$

It follows that for any  $A \leq m$ ,

$$
\int f_m^2 \, d\nu = \int_0^{+\infty} 2s \, \nu[f_m \ge s] \, ds
$$

$$
\le \int_0^A 2s \, \nu[f_m \ge s] \, ds + \int_A^{+\infty} 2s \, \nu[f_m \ge s] \, ds
$$

$$
\leq A^{2} + \nu [f_{m} \geq A] \int_{A}^{\infty} 2s ds
$$

$$
+ \int_{0}^{\infty} 2(s + \int f_{m} d\nu) \nu [f_{m} \geq s + \int f_{m} d\nu] ds
$$

$$
\leq A^{2} + \nu [f_{m} \geq A] (\int f_{m} d\nu)^{2} + \int_{0}^{\infty} 2s e^{-Cs^{2}} ds
$$

$$
+ 2(\int f_{m} d\nu) \int_{0}^{\infty} e^{-Cs^{2}} ds
$$

$$
\leq A^{2} + \nu [f \geq A] (\int f_{m} d\nu)^{2} + \int_{0}^{\infty} 2s e^{-Cs^{2}} ds + \frac{1}{4} (\int f_{m} d\nu)^{2}
$$

$$
+ 8(\int_{0}^{\infty} e^{-Cs^{2}} ds)^{2}
$$

$$
\leq A^2 + \left(\int f_m^2 \, d\nu\right) \left(\nu[f \geq A] + \frac{1}{4}\right) + \overline{C},
$$

where  $\overline{C} = \int_0^{+\infty} 2s e^{-Cs^2} ds + 8 \left( \int_0^{+\infty} e^{-Cs^2} ds \right)^2$  is a finite constant. If A is large enough, then  $\nu[f \geq A] \leq 1/4$ , and the above inequality implies  $\int f_m^2 d\nu \le 2(A^2 + \overline{C})$ . By taking  $m \to \infty$  we deduce that  $\int f^2 d\nu < +\infty$ , in particular  $f \in L^1(\nu)$ . So we can apply directly (iv) to  $f = d(\cdot, x_0)$ , and we get that for any  $a < C$ ,

$$
\int e^{a d(x,x_0)^2} \nu(dx) = \int_0^{+\infty} 2ase^{as^2} \nu[f \ge s] ds
$$

$$
= \int_0^{\int f d\nu} 2ase^{as^2} \nu[f \ge s] ds
$$

$$
+ \int_0^{+\infty} 2a \left(s + \int f d\nu\right) e^{a \left(s + \int f d\nu\right)^2} \nu\left[f \ge s + \int f d\nu\right] ds
$$

$$
\le e^{a \left(\int f d\nu\right)^2} + \int_0^{+\infty} 2a \left(s + \int f d\nu\right) e^{a \left(s + \int f d\nu\right)^2} e^{-Cs^2} ds < +\infty.
$$

This proves (vii).

The next implication is (i)  $\Rightarrow$  (v). If  $\nu$  satisfies  $T_1(\lambda)$ , by Proposition 22.5  $\nu^{\otimes N}$  satisfies  $T_1(\lambda/N)$  on  $\mathcal{X}^N$  equipped with the distance  $d_1(x, y) = \sum d(x_i, y_i)$ . Let  $F: \mathcal{X}^N \to \mathbb{R}$  be defined by

$$
F(x) = \frac{1}{N} \sum_{i=1}^{N} f(x_i).
$$

If f is Lipschitz then  $||F||_{\text{Lip}} = ||f||_{\text{Lip}}/N$ . Moreover,  $\int F d\nu^{\otimes N} =$   $\int f d\nu$ . So if we apply (iv) with X replaced by  $\mathcal{X}^N$  and f replaced  $f d\nu$ . So if we apply (iv) with  $\mathcal X$  replaced by  $\mathcal X^N$  and  $f$  replaced by  $F$ , we obtain

$$
\nu^{\otimes N} \Big[ \Big\{ x \in \mathcal{X}^N; \ \frac{1}{N} \sum_{i=1}^N f(x_i) \ge \int f \, d\nu + \varepsilon \Big\} \Big]
$$
  
=  $\nu^{\otimes N} \Big[ \Big\{ x \in \mathcal{X}^N; \ F(x) \ge \int F \, d\nu + \varepsilon \Big\} \Big]$   
 $\le \exp \Big( - \frac{C}{N} \frac{\varepsilon^2}{(\|f\|_{\text{Lip}} / N)^2} \Big)$   
=  $\exp \Big( -C \frac{N \varepsilon^2}{\|f\|_{\text{Lip}}^2} \Big),$ 

where  $C = \lambda/2$  (cf. the remark at the end of the proof of (i)  $\Rightarrow$  (iv)).

The implication  $(v) \Rightarrow (iv)$  is trivial.

Let us now consider the implication (i)  $\Rightarrow$  (iii). Assume that

$$
\forall \mu \in P_1(\mathcal{X}), \qquad W_1(\mu, \nu) \le C \sqrt{H_\nu(\mu)}.
$$
 (22.14)

Choose A with  $\nu[A] \geq 1/2$ , and  $\mu = (1_A \nu)/\nu[A]$ . If  $\nu[A^r] = 1$  there is nothing to prove, otherwise let  $\tilde{\mu} = (1_{\mathcal{X}\backslash A^r} \nu)/\nu[\mathcal{X} \setminus A^r]$ . By an immediate computation,

$$
H_{\nu}(\mu) = \log \frac{1}{\nu[A]} \le \log 2, \qquad H_{\nu}(\widetilde{\mu}) = \log \left( \frac{1}{1 - \nu[A^r]} \right).
$$

By  $(22.14)$  and the triangle inequality for the distance  $W_1$ ,

$$
W_1(\mu, \widetilde{\mu}) \le W_1(\mu, \nu) + W_1(\widetilde{\mu}, \nu) \le C\sqrt{\log 2} + C\sqrt{\log \left(\frac{1}{1 - \nu[A^r]}\right)}.
$$

(22.15)

On the other hand, it is obvious that  $W_1(\mu, \tilde{\mu}) \geq r$  (all the mass has to go from A to  $\mathcal{X} \setminus A^r$ , so each unit of mass should travel a distance at least r). So  $(22.15)$  implies

$$
r \leq C\sqrt{\log 2} + C\sqrt{\log\left(\frac{1}{1 - \nu[A^r]}\right)},
$$

therefore

$$
\nu[A^r] \ge 1 - \exp\left[-\left(\frac{r}{C} - \sqrt{\log 2}\right)^2\right].
$$

This establishes a bound of the type  $\nu[A^r] \geq 1 - ae^{-Cr^2}$ , and (iii) follows. (To get rid of the constant a, it suffices to note that  $\nu[A^r] \geq$  $\max(1/2, 1 - ae^{-cr^2}) \ge 1 - e^{-c'r^2}$  for c' well-chosen.)

To prove (iii)  $\Rightarrow$  (vi), let  $A = \{y; f(y) \leq m_f\}$ . By the very definition of a median, A has probability at least  $1/2$ , so (iii) implies  $\nu[A^r] \geq$  $1 - e^{-Cr^2}$ . On the other hand,  $\{f \geq m_f + \varepsilon\}$  is included in  $\mathcal{X} \setminus A^r$ for any  $r < \varepsilon / ||f||_{\text{Lip.}}$  (Indeed, if  $f(x) \geq m_f + \varepsilon$  and  $y \in A$  then  $f(x) - f(y) \ge \varepsilon$ , so  $d(x, y) \ge \varepsilon / \|f\|_{\text{Lip}} > r$ .) This leads to the bound  $\nu[\lbrace f \geq m_f + \varepsilon \rbrace] \leq e^{-C(\varepsilon/||f||_{\text{Lip}})^2}$ , which is Property (vi).

To show (vi)  $\Rightarrow$  (vii), let A be a compact set such that  $\nu[A] \geq 1/2$ ; also, let  $x_0 \in A$ , and let R be the diameter of A. Further, let  $f(x) =$  $d(x, A)$ ; then f is a 1-Lipschitz function admitting 0 for median. So (vi) implies

$$
\nu\big[d(x,x_0)\geq R+r\big]\leq \nu[d(x,A)\geq r]\leq e^{-Cr^2}.
$$

It follows that for any  $a < C$ ,

$$
\int e^{a d(x,x_0)^2} \nu(dx) = \int_0^{+\infty} \nu[d(\cdot, x_0)^2 \ge s] 2ase^{as^2} ds
$$
  
\n
$$
\le \int_0^R 2ase^{as^2} ds + \int_R^{+\infty} \nu[d(\cdot, x_0)^2 \ge s] 2ase^{as^2} ds
$$
  
\n
$$
\le e^{aR^2} + \int_R^{\infty} e^{-C(s-R)^2} 2ase^{as^2} ds < +\infty.
$$

To prove (vii)  $\Rightarrow$  (viii), pick up any  $x_0 \in \mathcal{X}$  and write

$$
\int e^{a d(x,y)^2} \nu(dx) \nu(dy) \le \int e^{2a d(x,x_0)^2 + 2a d(x_0,y)^2} \nu(dx) \nu(dy)
$$
$$
= \left( \int e^{2a d(x,x_0)^2} \nu(dx) \right)^2.
$$

The implication (viii)  $\Rightarrow$  (ix) is obvious.

It only remains to establish (ix)  $\Rightarrow$  (i). If  $\nu$  satisfies (ix), then obviously  $\nu \in P_1(\mathcal{X})$ . To prove that  $\nu$  satisfies  $T_1$ , we shall establish the weighted Csiszár–Kullback–Pinsker inequality

$$
\left\| d(x_0, \cdot) \left( \mu - \nu \right) \right\|_{TV} \le \frac{\sqrt{2}}{a} \left( 1 + \log \int_{\mathcal{X}} e^{a \, d(x_0, x)^2} \, d\nu(x) \right)^{1/2} \sqrt{H_\nu(\mu)}.
$$
\n(22.16)

Inequality (22.16) implies a  $T_1$  inequality, since Theorem 6.15 yields

$$
W_1(\mu,\nu) \leq ||d(x_0,\,\cdot\,)(\mu-\nu)||_{TV}.
$$

So let us turn to the proof of  $(22.16)$ . We may assume that  $\mu$  is absolutely continuous with respect to  $\nu$ , otherwise (22.16) is trivial. Then let f be the density of  $\mu$ , and let  $u = f - 1$ , so that

$$
\mu = (1+u)\,\nu;
$$

note that  $u \ge -1$  and  $\int u \, d\nu = 0$ . We also define

$$
h(v) := (1 + v) \log(1 + v) - v \ge 0, \qquad v \in [-1, +\infty);
$$

so that

$$
H_{\nu}(\mu) = \int_{\mathcal{X}} h(u) \, d\nu. \tag{22.17}
$$

Finally, let  $\varphi(x) = a d(x_0, x)$ .

Since  $h(0) = h'(0) = 0$ , Taylor's formula (with integral remainder) yields

$$
h(u) = u^2 \int_0^1 \frac{1-t}{1+tu} dt,
$$

so

$$
H_{\nu}(\mu) = \int_{\mathcal{X}} \int_0^1 \frac{u^2(x) (1-t)}{1+tu(x)} d\nu(x) dt.
$$

On the other hand, by Cauchy–Schwarz inequality on  $(0, 1) \times \mathcal{X}$ ,

$$
\left(\int_0^1 (1-t) dt\right)^2 \left(\int_{\mathcal{X}} \varphi |u| d\nu\right)^2
$$
  
= 
$$
\left(\int_{(0,1)\times\mathcal{X}} (1-t) \varphi(x) |u(x)| d\nu(x) dt\right)^2
$$
  
$$
\leq \left(\iint (1-t) (1+tu(x)) \varphi^2(x) d\nu(x) dt\right) \times \left(\iint \frac{1-t}{1+tu(x)} |u(x)|^2 d\nu(x) dt\right);
$$

thus

$$
\left(\int \varphi |u| \, d\nu\right)^2 \leq CH_{\nu}(\mu),\tag{22.18}
$$

where

$$
C := \frac{\iint (1-t)(1+tu)\,\varphi^2\,d\nu\,dt}{\left(\int_0^1 (1-t)\,dt\right)^2}.
$$
  $(22.19)$ 

The numerator can be rewritten as follows:

$$
\iint (1-t) (1+tu) \varphi^2 d\nu dt
$$
  
=  $\int (1-t)t dt \int (1+u) \varphi^2 d\nu + \int (1-t)^2 dt \int \varphi^2 d\nu$   
=  $\frac{1}{6} \int \varphi^2 d\mu + \frac{1}{3} \int \varphi^2 d\nu.$   $(22.20)$ 

From the Legendre representation of the  $H$  functional,

$$
\int \varphi^2 d\mu \le H_{\nu}(\mu) + \log \int e^{\varphi^2} d\nu, \tag{22.21}
$$

and Jensen's inequality, in the form

$$
\int \varphi^2 \, d\nu \le \log \int e^{\varphi^2} \, d\nu,\tag{22.22}
$$

we deduce that the right-hand side of (22.20) is bounded above by

$$
\frac{1}{6}H_{\nu}(\mu) + \frac{1}{2}\log \int e^{\varphi^2} d\nu.
$$

Plugging this into (22.19) and (22.18), we conclude that

$$
\left(\int \varphi \left|u\right| d\nu\right)^{2} \leq \left(\frac{2}{3}H + 2L\right)H,\tag{22.23}
$$

where H stands for  $H_{\nu}(\mu)$  and L for log  $\int e^{\varphi^2} d\nu$ .

The preceding bound is relevant only for "small" values of H. To handle large values, note that

$$
\left(\int \varphi |u| \, d\nu\right)^2 \le \int \varphi^2 |u| \, d\nu \int |u| \, d\nu
$$
  
\n
$$
\le \left(\int \varphi^2 \, d\mu + \int \varphi^2 \, d\nu\right) \left(\int \, d\mu + \int \, d\nu\right)
$$
  
\n
$$
\le (H + 2L) \, 2 \tag{22.24}
$$

where I have successively used the Cauchy–Schwarz inequality, the inequality  $|u| \leq 1 + u + 1$  on  $[-1, +\infty)$  (which results in  $|u| \nu \leq \mu + \nu$ ), and finally (22.21) and (22.22).

By (22.23) and (22.24),

$$
\left(\int \varphi|u| \, d\nu\right)^2 \le \min\left((2H)\left(\frac{H}{3} + L\right), \, 2(H + 2L)\right).
$$

Then the elementary inequality

 $\min (At^2 + Bt, t+D) \leq Mt, \qquad M = \frac{1}{2}$ 2  $\left\{1 + B + \sqrt{(B-1)^2 + 4AD}\right\}$ 

implies

$$
\int \varphi |u| \, d\nu \, \leq \, m \sqrt{H(\mu | \nu)}
$$

where

$$
m = \sqrt{1 + L + \sqrt{(L-1)^2 + \frac{8}{3}L}} \le \sqrt{2}\sqrt{L+1}.
$$

This concludes the proof. □

Remark 22.12 (CKP inequality). In the particular case  $\varphi = 1$ , we can replace the inequality (22.21) by just  $\int d\mu = 1$ ; then instead of (22.23) we obtain

$$
\|\mu - \nu\|_{TV} \le \sqrt{2 H_{\nu}(\mu)}.
$$
\n(22.25)

This is the classical Csiszár–Kullback–Pinsker (CKP) inequality, with the sharp constant  $\sqrt{2}$ .

**Remark 22.13.** If  $\nu$  satisfies  $T_2(\lambda)$ , then also  $\nu^{\otimes N}$  satisfies  $T_2(\lambda)$ , independently of  $N$ ; so one might hope to improve the concentration inequality appearing in Theorem  $22.10(v)$ . But now the space  $\mathcal{X}^N$  should be equipped with the  $d_2$  distance, for which the function F :  $x \to (1/N) \sum f(x_i)$  is only  $\sqrt{N}$ -Lipschitz! In the end,  $T_2$  does not