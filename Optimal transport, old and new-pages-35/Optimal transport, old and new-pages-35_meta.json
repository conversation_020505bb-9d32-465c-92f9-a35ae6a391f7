{"table_of_contents": [{"title": "Concentration inequalities", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.7431640625], [320.25, 97.7431640625], [320.25, 111.4716796875], [133.5, 111.4716796875]]}, {"title": "584 22 Concentration inequalities", "heading_level": null, "page_id": 1, "polygon": [[133.5, 26.25], [286.5, 26.25], [286.5, 35.43310546875], [133.5, 35.43310546875]]}, {"title": "Optimal transport and concentration", "heading_level": null, "page_id": 1, "polygon": [[133.5, 435.0], [354.0, 435.0], [354.0, 446.2734375], [133.5, 446.2734375]]}, {"title": "588 22 Concentration inequalities", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.176025390625], [286.5, 26.176025390625], [286.5, 35.312255859375], [133.5, 35.312255859375]]}, {"title": "Gaussian concentration and T_1 inequality", "heading_level": null, "page_id": 7, "polygon": [[133.5, 498.48046875], [378.0, 498.48046875], [378.0, 510.0], [133.5, 510.0]]}, {"title": "596 22 Concentration inequalities", "heading_level": null, "page_id": 13, "polygon": [[133.5, 26.15185546875], [286.5, 26.15185546875], [286.5, 35.72314453125], [133.5, 35.72314453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 40], ["TextInlineMath", 3], ["Equation", 3], ["Text", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 67], ["TextInlineMath", 3], ["Equation", 3], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 425], ["Line", 40], ["TextInlineMath", 5], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 588], ["Line", 86], ["TextInlineMath", 5], ["Equation", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 683], ["Line", 93], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5499, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 95], ["Equation", 6], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2652, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 92], ["Text", 8], ["Equation", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 574, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 78], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1457, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 577], ["Line", 97], ["TextInlineMath", 7], ["Equation", 7], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 72], ["TextInlineMath", 8], ["Equation", 6], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 712], ["Line", 147], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3986, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 553], ["Line", 75], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3185, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 756], ["Line", 104], ["TextInlineMath", 5], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 537], ["Line", 82], ["Equation", 8], ["TextInlineMath", 7], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1187, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 85], ["Text", 8], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 73], ["Text", 6], ["TextInlineMath", 6], ["Equation", 5]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1081, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-35"}