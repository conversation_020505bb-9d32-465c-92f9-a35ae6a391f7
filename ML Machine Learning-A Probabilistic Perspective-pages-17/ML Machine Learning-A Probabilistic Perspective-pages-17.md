#### **13.5.1.2 Algorithms for group lasso**

There are a variety of algorithms for group lasso. Here we briefly mention two. The first approach is based on proximal gradient descent, discussed in Section 13.4.3. Since the regularizer is separable,  $R(\mathbf{w}) = \sum_{g} ||\mathbf{w}_{g}||_{p}$ , the proximal operator decomposes into G separate operators of the form

$$
\text{prox}_{R}(\mathbf{b}) = \underset{\mathbf{z} \in \mathbb{R}^{D_g}}{\text{argmin}} \left\| \mathbf{z} - \mathbf{b} \right\|_{2}^{2} + \lambda \|\mathbf{z}\|_{p} \tag{13.106}
$$

where  $\mathbf{b} = \theta_{kg} - t_k \mathbf{g}_{kg}$ . If  $p = 2$ , one can show (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2005) that this can be implemented as follows

$$
\text{prox}_R(\mathbf{b}) = \mathbf{b} - \text{proj}_{\lambda C}(\mathbf{b})
$$
\n(13.107)

where  $C = {\mathbf{z} : ||\mathbf{z}||_2 \le 1}$  is the  $\ell_2$  ball. Using Equation 13.74, if  $||\mathbf{b}||_2 < \lambda$ , we have

$$
\operatorname{prox}_R(\mathbf{b}) = \mathbf{b} - \mathbf{b} = \mathbf{0} \tag{13.108}
$$

otherwise we have

$$
\text{prox}_{R}(\mathbf{b}) = \mathbf{b} - \lambda \frac{\mathbf{b}}{||\mathbf{b}||_2} = \mathbf{b} \frac{||\mathbf{b}||_2 - \lambda}{||\mathbf{b}||_2}
$$
 (13.109)

We can combine these into a vectorial soft-threshold function as follows (Wright et al. 2009):

$$
\text{prox}_R(\mathbf{b}) = \mathbf{b} \frac{\max(||\mathbf{b}||_2 - \lambda, 0)}{\max(||\mathbf{b}||_2 - \lambda, 0) + \lambda}
$$
\n(13.110)

If  $p = \infty$ , we use  $C = {\mathbf{z} : ||\mathbf{z}||_1 \leq 1}$ , which is the  $\ell_1$  ball. We can project onto this in  $O(d_q)$ time using an algorithm described in (Duchi et al. 2008).

Another approach is to modify the EM algorithm. The method is almost the same as for vanilla lasso. If we define  $\tau_j^2 = \tau_{g(j)}^2$ , where  $g(j)$  is the group to which dimension j belongs, we can use the same full conditionals for  $\sigma^2$  and **w** as before. The only changes are as follows:

• We must modify the full conditional for the weight precisions, which are estimated based on a shared set of weights:

$$
\frac{1}{\tau_g^2} |\gamma, \mathbf{w}, \sigma^2, \mathbf{y}, \mathbf{X} \sim \text{InverseGaussian}\left(\sqrt{\frac{\gamma^2 \sigma^2}{||\mathbf{w}_g||_2^2}}, \gamma^2\right)
$$
\n(13.111)

where  $||\mathbf{w}_g||_2^2 = \sum_{j \in g} w_{jg}^2$ . For the E step, we can use

$$
\mathbb{E}\left[\frac{1}{\tau_g^2}\right] = \frac{\gamma \sigma}{||\mathbf{w}_g||_2} \tag{13.112}
$$

• We must modify the full conditional for the tuning parameter, which is now only estimated based on G values of  $\tau_a^2$ :

$$
p(\gamma^2|\tau) = \text{Ga}(a_{\gamma} + G/2, b_{\gamma} + \frac{1}{2} \sum_{g}^{G} \tau_g^2)
$$
\n(13.113)

Image /page/1/Figure/1 description: The image displays three plots labeled (a), (b), and (c). Plot (a) is a scatter plot with the y-axis labeled 'CGH' ranging from -1.5 to 0.5, and the x-axis labeled 'Index' ranging from 0 to 400. The plot contains numerous black circles representing data points, and a red step-like line indicating a trend. Plots (b) and (c) are grayscale images of the letters "FL". Plot (b) appears to be a noisy version of the letters, while plot (c) shows a clearer, pixelated representation of the letters against a darker background with some gradient.

**Figure 13.16** (a) Example of the fused lasso. The vertical axis represents array CGH (chromosomal genome hybridization) intensity, and the horizontal axis represents location along a genome. Source: Figure 1 of (Hoefling 2010). (b) Noisy image. (c) Fused lasso estimate using 2d lattice prior. Source: Figure 2 of (Hoefling 2010). Used with kind permission of Holger Hoefling.

# **13.5.2 Fused lasso**

In some problem settings (e.g., functional data analysis), we want neighboring coefficients to be similar to each other, in addition to being sparse. An example is given in Figure 13.16(a), where we want to fit a signal that is mostly "off", but in addition has the property that neighboring locations are typically similar in value. We can model this by using a prior of the form

$$
p(\mathbf{w}|\sigma^2) \propto \exp\left(-\frac{\lambda_1}{\sigma} \sum_{j=1}^D |w_j| - \frac{\lambda_2}{\sigma} \sum_{j=1}^{D-1} |w_{j+1} - w_j|\right)
$$
(13.114)

This is known as the **fused lasso** penalty. In the context of functional data analysis, we often use  $X = I$ , so there is one coefficient for each location in the signal (see Section 4.4.2.3). In this case, the overall objective has the form

$$
J(\mathbf{w}, \lambda_1, \lambda_2) = \sum_{i=1}^{N} (y_i - w_i)^2 + \lambda_1 \sum_{i=1}^{N} |w_i| + \lambda_2 \sum_{i=1}^{N-1} |w_{i+1} - w_i|
$$
 (13.115)

This is a sparse version of Equation 4.148.

It is possible to generalize this idea beyond chains, and to consider other graph structures, using a penalty of the form

$$
J(\mathbf{w}, \lambda_1, \lambda_2) = \sum_{s \in V} (y_s - w_s)^2 + \lambda_1 \sum_{s \in V} |w_s| + \lambda_2 \sum_{(s, t) \in E} |w_s - w_t|
$$
 (13.116)

This is called **graph-guided fused lasso** (see e.g., (Chen et al. 2010)). The graph might come from some prior knowledge, e.g., from a database of known biological pathways. Another example is shown in Figure 13.16(b-c), where the graph structure is a 2d lattice.

# **13.5.2.1 GSM interpretation of fused lasso**

One can show (Kyung et al. 2010) that the fused lasso model is equivalent to the following hierarchical model

$$
\mathbf{w}|\sigma^2, \tau, \omega \sim \mathcal{N}(\mathbf{0}, \sigma^2 \Sigma(\tau, \omega))
$$
\n(13.117)

$$
\tau_j^2|\gamma_1 \sim \text{Expon}\left(\frac{\gamma_1^2}{2}\right), \quad j=1:D \tag{13.118}
$$

$$
\omega_j^2 |\gamma_2 \sim \text{Expon}(\frac{\gamma_2^2}{2}), \ \ j = 1 : D - 1 \tag{13.119}
$$

where  $\Sigma = \Omega^{-1}$ , and  $\Omega$  is a tridiagonal precision matrix with

$$
\text{main diagonal} = \left\{ \frac{1}{\tau_j^2} + \frac{1}{\omega_{j-1}^2} + \frac{1}{\omega_j^2} \right\} \tag{13.120}
$$

off diagonal 
$$
= \left\{-\frac{1}{\omega_j^2}\right\}
$$
 (13.121)

where we have defined  $\omega_0^{-2} = \omega_D^{-2} = 0$ . This is very similar to the model in Section 4.4.2.3, where we used a chain-structured Gaussian Markov random field as the prior, with fixed variance. Here we just let the variance be random. In the case of graph-guided lasso, the structure of the graph is reflected in the zero pattern of the Gaussian precision matrix (see Section 19.4.4).

## **13.5.2.2 Algorithms for fused lasso**

It is possible to generalize the EM algorithm to fit the fused lasso model, by exploiting the Markov structure of the Gaussian prior for efficiency. Direct solvers (which don't use the latent variable trick) can also be derived (see e.g., (Hoefling 2010)). However, this model is undeniably more expensive to fit than the other variants we have considered.

# **13.5.3 Elastic net (ridge and lasso combined)**

Although lasso has proved to be effective as a variable selection technique, it has several problems (Zou and Hastie 2005), such as the following:

- If there is a group of variables that are highly correlated (e.g., genes that are in the same pathway), then the lasso tends to select only one of them, chosen rather arbitrarily. (This is evident from the LARS algorithm: once one member of the group has been chosen, the remaining members of the group will not be very correlated with the new residual and hence will not be chosen.) It is usually better to select all the relevant variables in a group. If we know the grouping structure, we can use group lasso, but often we don't know the grouping structure.
- In the  $D > N$  case, lasso can select at most N variables before it saturates.
- If  $N > D$ , but the variables are correlated, it has been empirically observed that the prediction performance of ridge is better than that of lasso.

Zou and Hastie (Zou and Hastie 2005) proposed an approach called the **elastic net**, which is a hybrid between lasso and ridge regression, which solves all of these problems. It is apparently called the "elastic net" because it is "like a stretchable fishing net that retains 'all the big fish"' (Zou and Hastie 2005).

## **13.5.3.1 Vanilla version**

The vanilla version of the model defines the following objective function:

$$
J(\mathbf{w}, \lambda_1, \lambda_2) = ||\mathbf{y} - \mathbf{X}\mathbf{w}||^2 + \lambda_2 ||\mathbf{w}||_2^2 + \lambda_1 ||\mathbf{w}||_1
$$
\n(13.122)

Notice that this penalty function is *strictly convex* (assuming  $\lambda_2 > 0$ ) so there is a unique global minimum, even if **X** is not full rank.

It can be shown (Zou and Hastie 2005) that any strictly convex penalty on **w** will exhibit a **grouping effect**, which means that the regression coefficients of highly correlated variables tend to be equal (up to a change of sign if they are negatively correlated). For example, if two features are equal, so  $\mathbf{X}_{:j} = \mathbf{X}_{:k}$ , one can show that their estimates are also equal,  $\hat{w}_j = \hat{w}_k$ . By contrast, with lasso, we may have that  $\hat{w}_j = 0$  and  $\hat{w}_k \neq 0$  or vice versa.

## **13.5.3.2 Algorithms for vanilla elastic net**

It is simple to show (Exercise 13.5) that the elastic net problem can be reduced to a lasso problem on modified data. In particular, define

$$
\tilde{\mathbf{X}} = c \begin{pmatrix} \mathbf{X} \\ \sqrt{\lambda_2} \mathbf{I}_D \end{pmatrix}, \quad \tilde{\mathbf{y}} = \begin{pmatrix} \mathbf{y} \\ \mathbf{0}_{D \times 1} \end{pmatrix}
$$
\n(13.123)

where  $c = (1 + \lambda_2)^{-\frac{1}{2}}$ . Then we solve

$$
\tilde{\mathbf{w}} = \arg\min_{\tilde{\mathbf{w}}} ||\tilde{\mathbf{y}} - \tilde{\mathbf{X}}\tilde{\mathbf{w}}||^2 + c\lambda_1 ||\tilde{\mathbf{w}}||_1
$$
\n(13.124)

and set  $\mathbf{w} = c\tilde{\mathbf{w}}$ .

We can use LARS to solve this subproblem; this is known as the LARS-EN algorithm. If we stop the algorithm after m variables have been included, the cost is  $O(m^3 + Dm^2)$ . Note that we can use  $m = D$  if we wish, since **X** has rank D. This is in contrast to lasso, which cannot select more than N variables (before jumping to the OLS solution) if  $N < D$ .

When using LARS-EN (or other  $\ell_1$  solvers), one typically uses cross-validation to select  $\lambda_1$  and  $\lambda_2$ .

### **13.5.3.3 Improved version**

Unfortunately it turns out that the "vanilla" elastic net does not produce functions that predict very accurately, unless it is very close to either pure ridge or pure lasso. Intuitively the reason is that it performs shrinkage twice: once due to the  $\ell_2$  penalty and again due to the  $\ell_1$  penalty. The solution is simple: undo the  $\ell_2$  shrinkage by scaling up the estimates from the vanilla version. In other words, if **w**<sup>∗</sup> is the solution of Equation 13.124, then a better estimate is

$$
\hat{\mathbf{w}} = \sqrt{1 + \lambda_2} \tilde{\mathbf{w}} \tag{13.125}
$$

We will call this a corrected estimate.

One can show that the corrected estimates are given by

$$
\hat{\mathbf{w}} = \arg\min_{\mathbf{w}} \mathbf{w}^T \left( \frac{\mathbf{X}^T \mathbf{X} + \lambda_2 \mathbf{I}}{1 + \lambda_2} \right) \mathbf{w} - 2\mathbf{y}^T \mathbf{X} \mathbf{w} + \lambda_1 ||\mathbf{w}||_1
$$
\n(13.126)

Now

$$
\frac{\mathbf{X}^T \mathbf{X} + \lambda_2 \mathbf{I}}{1 + \lambda_2} = (1 - \rho)\hat{\mathbf{\Sigma}} + \rho \mathbf{I}
$$
\n(13.127)

where  $\rho = \lambda_2/(1 + \lambda_2)$ . So the the elastic net is like lasso but where we use a version of **Σ**ˆ that is shrunk towards **I**. (See Section 4.2.6 for more discussion of regularized estimates of covariance matrices.)

#### **13.5.3.4 GSM interpretation of elastic net**

The implicit prior being used by the elastic net obviously has the form

$$
p(\mathbf{w}|\sigma^2) \propto \exp\left(-\frac{\gamma_1}{\sigma} \sum_{j=1}^D |w_j| - \frac{\gamma_2}{2\sigma^2} \sum_{j=1}^D w_j^2\right)
$$
 (13.128)

which is just a product of Gaussian and Laplace distributions.

This can be written as a hierarchical prior as follows (Kyung et al. 2010; Chen et al. 2011):

$$
w_j|\sigma^2, \tau_j^2 \sim \mathcal{N}(0, \sigma^2(\tau_j^{-2} + \gamma_2)^{-1})
$$
\n(13.129)

$$
\tau_j^2 |\gamma_1 \sim \operatorname{Expon}(\frac{\gamma_1^2}{2}) \tag{13.130}
$$

Clearly if  $\gamma_2 = 0$ , this reduces to the regular lasso.

It is possible to perform MAP estimation in this model using EM, or Bayesian inference using MCMC (Kyung et al. 2010) or variational Bayes (Chen et al. 2011).

# **13.6 Non-convex regularizers**

Although the Laplace prior results in a convex optimization problem, from a statistical point of view this prior is not ideal. There are two main problems with it. First, it does not put enough probability mass near 0, so it does not sufficiently suppress noise. Second, it does not put enough probability mass on large values, so it causes shrinkage of relevant coefficients, corresponding to "signal". (This can be seen in Figure 13.5(a): we see that  $\ell_1$  estimates of large coefficients are significantly smaller than their ML estimates, a phenomenon known as bias.)

Both problems can be solved by going to more flexible kinds of priors which have a larger spike at 0 and heavier tails. Even though we cannot find the global optimum anymore, these non-convex methods often outperform  $\ell_1$  regularization, both in terms of predictive accuracy and in detecting relevant variables (Fan and Li 2001; Schniter et al. 2008). We give some examples below.

## **13.6.1 Bridge regression**

A natural generalization of  $\ell_1$  regularization, known as **bridge regression** (Frank and Friedman 1993), has the form

$$
\hat{\mathbf{w}} = \text{NLL}(\mathbf{w}) + \lambda \sum_{j} |w_j|^b \tag{13.131}
$$

for  $b \geq 0$ . This corresponds to MAP estimation using a **exponential power distribution** given by

$$
\text{ExpPower}(w|\mu, a, b) \triangleq \frac{b}{2a\Gamma(1+1/b)} \exp\left(-\frac{|x-\mu|^b}{a}\right) \tag{13.132}
$$

If  $b = 2$ , we get the Gaussian distribution (with  $a = \sigma\sqrt{2}$ ), corresonding to ridge regression; if we set  $b = 1$ , we get the Laplace distribution, corresponding to lasso; if we set  $b = 0$ , we get  $\ell_0$  regression, which is equivalent to best subset selection. Unfortunately, the objective is not convex for  $b < 1$ , and is not sparsity promoting for  $b > 1$ . So the  $\ell_1$  norm is the tightest convex approximation to the  $\ell_0$  norm.

The effect of changing b is illustrated in Figure 13.17, where we plot the prior for  $b = 2$ ,  $b = 1$ and  $b = 0.4$ ; we assume  $p(\mathbf{w}) = p(w_1)p(w_2)$ . We also plot the posterior after seeing a single observation,  $(\mathbf{x}, y)$ , which imposes a single linear constraint of the form,  $y = \mathbf{w}^T \mathbf{x}$ , with a certain tolerance controlled by the observation noise (compare to Figure 7.11). We see see that the mode of the Laplace is on the vertical axis, corresponding to  $w_1 = 0$ . By contrast, there are two modes when using  $b = 0.4$ , corresponding to two different sparse solutions. When using the Gaussian, the MAP estimate is not sparse (the mode does not lie on either of the coordinate axes).

# **13.6.2 Hierarchical adaptive lasso**

Recall that one of the principal problems with lasso is that it results in biased estimates. This is because it needs to use a large value of  $\lambda$  to "squash" the irrelevant parameters, but this then over-penalizes the relevant parameters. It would be better if we could associate a different penalty parameter with each parameter. Of course, it is completely infeasible to tune D parameters by cross validation, but this poses no problem to the Bayesian: we simply make each  $\tau_i^2$  have its own private tuning parameter,  $\gamma_j$ , which are now treated as random variables coming from the conjugate prior  $\gamma_i \sim IG(a, b)$ . The full model is as follows:

$$
\gamma_j \sim \text{IG}(a, b) \tag{13.133}
$$

$$
\tau_j^2 |\gamma_j \sim \text{Ga}(1, \gamma_j^2/2) \tag{13.134}
$$

$$
w_j|\tau_j^2 \sim \mathcal{N}(0,\tau_j^2) \tag{13.135}
$$

See Figure 13.18(a). This has been called the **hierarchical adaptive lasso** (HAL) (Lee et al. 2010) (see also (Lee et al. 2011; Cevher 2009; Armagan et al. 2011)). We can integrate out  $\tau_i^2$ , which induces a Lap $(w_i | 0, 1/\gamma_i)$  distribution on  $w_i$  as before. The result is that  $p(w_i)$  is now a scaled mixture of Laplacians. It turns out that we can fit this model (i.e., compute a *local* posterior mode) using EM, as we explain below. The resulting estimate,  $\hat{w}_{HAL}$ , often works

Image /page/6/Figure/1 description: The image displays six contour plots, arranged in a 2x3 grid. Each plot features a pair of perpendicular axes, representing a coordinate system. The plots depict probability distributions, with color gradients indicating density, ranging from red (highest density) to blue (lowest density). The top row shows three distinct shapes: a circular distribution with concentric rings, a diamond-shaped distribution with sharp corners, and a star-shaped distribution with four points. Each of these top plots also includes two parallel black lines intersecting the distribution. The bottom row shows three elliptical distributions, each oriented differently. The first is elongated along a line from the bottom-left to the top-right. The second is similarly oriented but slightly wider. The third is a more complex, asymmetric distribution, with a concentrated peak and a trailing tail.

**Figure 13.17** Top: plot of log *prior* for three different distributions with unit variance: Gaussian, Laplace and exponential power. Bottom: plot of log *posterior* after observing a single observation, corresponding to a single linear constraint. The precision of this observation is shown by the diagonal lines in the top figure. In the case of the Gaussian prior, the posterior is unimodal and symmetric. In the case of the Laplace prior, the posterior is unimodal and asymmetric (skewed). In the case of the exponential prior, the posterior is bimodal. Based on Figure 1 of (Seeger 2008). Figure generated by sparsePostPlot, written by Florian Steinke.

much better than the estimate returned by lasso,  $\hat{w}_{L1}$ , in the sense that it is more likely to contain zeros in the right places (model selection consistency) and more likely to result in good predictions (prediction consistency) (Lee et al. 2010). We give an explanation for this behavior in Section ********.

### **13.6.2.1 EM for HAL**

Since the inverse Gamma is conjugate to the Laplace, we find that the E step for  $\gamma_i$  is given by

$$
p(\gamma_j|w_j) = \text{IG}(a+1, b+|w_j|) \tag{13.136}
$$

The E step for  $\sigma^2$  is the same as for vanilla lasso.

The prior for **w** has the following form:

$$
p(\mathbf{w}|\boldsymbol{\gamma}) = \prod_{j} \frac{1}{2\gamma_j} \exp(-|w_j|/\gamma_j)
$$
\n(13.137)

Hence the M step must optimize

$$
\hat{\mathbf{w}}^{(t+1)} = \underset{\mathbf{w}}{\operatorname{argmax}} \log \mathcal{N}(\mathbf{y}|\mathbf{X}\mathbf{w}, \sigma^2) - \sum_{j} |w_j| \mathbb{E}\left[1/\gamma_j\right]
$$
(13.138)

Image /page/7/Figure/1 description: The image displays two figures, labeled (a) and (b). Figure (a) is a graphical model illustrating a probabilistic relationship between variables. It shows nodes representing parameters like a, b, aσ, bσ, σ², γj, τ²j, wj, yi, and xi. These nodes are connected by arrows indicating dependencies, and some variables are enclosed in boxes labeled 'D' and 'N', suggesting they are part of a larger structure or dataset. Figure (b) is a plot with the title 'HAL' on the y-axis and numerical values ranging from -1 to 1 on both the x and y axes. The plot features three curves, distinguished by line styles (solid blue, dashed red, and dashed-dotted black), representing different parameter values: a=1, b=0.01; a=1, b=0.10; and a=1, b=1.00. These curves form a star-like shape, indicating a specific mathematical relationship or distribution.

**Figure 13.18** (a) DGM for hierarchical adaptive lasso. (b) Contours of Hierarchical adpative Laplace. Based on Figure 1 of (Lee et al. 2010). Figure generated by normalGammaPenaltyPlotDemo.

The expectation is given by

$$
\mathbb{E}\left[1/\gamma_j\right] = \frac{a+1}{b+|w_j^{(t)}|} \triangleq s_j^{(t)} \tag{13.139}
$$

Thus the M step becomes a weighted lasso problem:

$$
\hat{\mathbf{w}}^{(t+1)} = \underset{\mathbf{w}}{\text{argmin}} \, ||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2 + \sum_j s_j^{(t)} |w_j| \tag{13.140}
$$

This is easily solved using standard methods (e.g., LARS). Note that if the coefficient was estimated to be large in the previous iteration (so  $w_i^{(t)}$  is large), then the scaling factor  $s_i^{(t)}$  will be small, so large coefficients are not penalized heavily. Conversely, small coefficients *do* get penalized heavily. This is the way that the algorithm adapts the penalization strength of each coefficient. The result is an estimate that is often much sparser than returned by lasso, but also less biased.

Note that if we set  $a = b = 0$ , and we only perform 1 iteration of EM, we get a method that is closely related to the **adaptive lasso** of (Zou 2006; Zou and Li 2008). This EM algorithm is also closely related to some iteratively reweighted  $\ell_1$  methods proposed in the signal processing community (Chartrand and Yin 2008; Candes et al. 2008).

## ********** Understanding the behavior of HAL**

We can get a better understanding of HAL by integrating out  $\gamma_j$  to get the following marginal distribution,

$$
p(w_j|a,b) = \frac{a}{2b} \left(\frac{|w_j|}{b} + 1\right)^{-(a+1)}
$$
\n(13.14)

Image /page/8/Figure/1 description: The image contains two plots, (a) and (b), comparing different methods for estimating parameters. Plot (a), titled "Lasso", shows a blue solid line and a red dotted line. Both lines represent the relationship between wMLE on the x-axis and wMAP on the y-axis, ranging from -10 to 10. The blue line is a piecewise linear function, while the red dotted line represents a linear relationship. Plot (b), titled "HAL", displays the same axes as plot (a) and includes the red dotted line from plot (a). Additionally, plot (b) shows three other dashed lines: a black dotted line labeled "b = 0.010, a=1", a green dashed line labeled "b = 0.100, a=1", and a cyan dashed-dotted line labeled "b = 1.000, a=1". These lines also illustrate the relationship between wMLE and wMAP, showing variations based on the parameters b and a.

**Figure 13.19** Thresholding behavior of two penalty functions (negative log priors). (a) Laplace. (b) Hierarchical adaptive Laplace. Based on Figure 2 of (Lee et al. 2010). Figure generated by normalGammaThresholdPlotDemo.

This is an instance of the **generalized t distribution** (McDonald and Newey 1988) (in (Cevher 2009; Armagan et al. 2011), this is called the double Pareto distribution) defined as

$$
GT(w|\mu, a, c, q) \triangleq \frac{q}{2ca^{1/q}B(1/q, a)} \left(1 + \frac{|w - \mu|^{q}}{ac^{q}}\right)^{-(a+1/q)}
$$
(13.142)

where c is the scale parameter (which controls the degree of sparsity), and  $\alpha$  is related to the degrees of freedom. When  $q = 2$  and  $c = \sqrt{2}$  we recover the standard t distribution; when  $a \to \infty$ , we recover the exponential power distribution; and when  $q = 1$  and  $a = \infty$  we get the Laplace distribution. In the context of the current model, we see that  $p(w_i | a, b)$  =  $GT(w_i | 0, a, b/a, 1).$ 

The resulting penalty term has the form

$$
\pi_{\lambda}(w_j) \triangleq -\log p(w_j) = (a+1)\log(1+\frac{|w_j|}{b}) + \text{const}
$$
\n(13.143)

where  $\lambda = (a, b)$  are the tuning parameters. We plot this penalty in 2d (i.e., we plot  $\pi_{\lambda}(w_1)$  +  $\pi_{\lambda}(w_2)$ ) in Figure 13.18(b) for various values of b. Compared to the diamond-shaped Laplace penalty, shown in Figure 13.3(a), we see that the HAL penalty looks more like a "star fish": it puts much more density along the "spines", thus enforcing sparsity more aggressively. Note that this penalty is clearly not convex.

We can gain further understanding into the behavior of this penalty function by considering applying it to the problem of linear regression with an orthogonal design matrix. In this case,

| $p(\tau_i^2)$                                     | $p(\gamma_i)$ | $p(w_i)$                       | Ref                                                       |
|---------------------------------------------------|---------------|--------------------------------|-----------------------------------------------------------|
| $Ga(1, \frac{\gamma^2}{2})$                       | Fixed         | $Lap(0,1/\gamma)$              | (Andrews and Mallows 1974; West 1987)                     |
| $Ga(1, \frac{\gamma^2}{2})$                       | IG(a, b)      | $GT(0, a, b/a, 1)$             | (Lee et al. 2010, 2011; Cevher 2009; Armagan et al. 2011) |
| $Ga(1, \frac{\gamma^2}{2})$                       | Ga(a, b)      | NEG(a,b)                       | (Griffin and Brown 2007, 2010; Chen et al. 2011)          |
| $Ga(\delta, \frac{\gamma^2}{2})$                  | Fixed         | $NG(\delta, \gamma)$           | (Griffin and Brown 2007, 2010)                            |
| $Ga(\tau_j^2 0,0)$                                | -             | $NJ(w_j)$                      | (Figueiredo 2003)                                         |
| $IG(\frac{\delta}{2}, \frac{\delta \gamma^2}{2})$ | Fixed         | $\mathcal{T}(0,\delta,\gamma)$ | (Andrews and Mallows 1974; West 1987)                     |
| $C^+(0,\gamma)$                                   | $C^+(0,b)$    | horseshoe(b)                   | (Carvahlo et al. 2010)                                    |

**Table 13.2** Some scale mixtures of Gaussians. Abbreviations:  $C^+$  = half-rectified Cauchy; Ga = Gamma (shape and rate parameterization);  $GT =$  generalized t;  $IG =$  inverse Gamma;  $NEG = Normal-Exponential-$ Gamma; NG = Normal-Gamma; NJ = Normal-Jeffreys. The horseshoe distribution is the name we give to the distribution induced on  $w_i$  by the prior described in (Carvahlo et al. 2010); this has no simple analytic form. The definitions of the NEG and NG densities are a bit complicated, but can be found in the references. The other distributions are defined in the text.

one can show that the objective becomes

$$
J(\mathbf{w}) = \frac{1}{2} ||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2 + \sum_{j=1}^{D} \pi_{\lambda}(|w_j|)
$$
 (13.144)

$$
= \frac{1}{2}||\mathbf{y} - \hat{\mathbf{y}}||^2 + \frac{1}{2}\sum_{j=1}^{D} (\hat{w}_j^{mle} - w_j)^2 + \sum_{j=1}^{D} \pi_{\lambda}(|w_j|)
$$
(13.145)

where  $\hat{\mathbf{w}}^{mle} = \mathbf{X}^T \mathbf{y}$  is the MLE and  $\hat{\mathbf{y}} = \mathbf{X} \hat{\mathbf{w}}^{mle}$ . Thus we can compute the MAP estimate one dimension at a time by solving the following 1d optimization problem:

$$
\hat{w}_j = \underset{w_j}{\text{argmin}} \frac{1}{2} (\hat{w}_j^{mle} - w_j)^2 + \pi \chi(w_j)
$$
\n(13.146)

In Figure 13.19(a) we plot the lasso estimate,  $\hat{w}^{L1}$ , vs the ML estimate,  $\hat{w}^{mle}$ . We see that the  $\ell_1$  estimator has the usual soft-thresholding behavior seen earlier in Figure 13.5(a). However, this behavior is undesirable since the large magnitude coefficients are also shrunk towards 0, whereas we would like them to be equal to their unshrunken ML estimates.

In Figure 13.19(b) we plot the HAL estimate,  $\hat{w}^{HAL}$ , vs the ML estimate  $\hat{w}^{mle}$ . We see that this approximates the more desirable hard thresholding behavior seen earlier in Figure 13.5(b) much more closely.

## **13.6.3 Other hierarchical priors**

Many other hierarchical sparsity-promoting priors have been proposed; see Table 13.2 for a brief summary. In some cases, we can analytically derive the form of the marginal prior for  $w_j$ . Generally speaking, this prior is not concave.

A particularly interesting prior is the improper Normal-Jeffreys prior, which has been used in (Figueiredo 2003). This puts a non-informative Jeffreys prior on the variance,  $\text{Ga}(\tau_j^2 |0,0) \propto$ 

 $1/\tau_j^2$ ; the resulting marginal has the form  $p(w_j) = \text{NJ}(w_j) \propto 1/|w_j|$ . This gives rise to a thresholding rule that looks very similar to HAL in Figure 13.19(b), which in turn is very similar to hard thresholding. However, this prior has no free parameters, which is both a good thing (nothing to tune) and a bad thing (no ability to adapt the level of sparsity).

# **13.7 Automatic relevance determination (ARD)/sparse Bayesian learning (SBL)**

All the methods we have considered so far (except for the spike-and-slab methods in Section 13.2.1) have used a **factorial prior** of the form  $p(\mathbf{w}) = \prod_{i} p(w_i)$ . We have seen how these priors can be represented in terms of Gaussian scale mixtures of the form  $w_j \sim \mathcal{N}(0, \tau_j^2)$ , where  $\tau_i^2$  has one of the priors listed in Table 13.2. Using these latent variances, we can represent the model in the form  $\tau_j^2 \to w_j \to \mathbf{y} \leftarrow \mathbf{X}$ . We can then use EM to perform MAP estimation, where in the E step we infer  $p(\tau_j^2 | w_j)$ , and in the M step we estimate **w** from **y**, **X** and  $\tau$ . This M step either involves a closed-form weighted  $\ell_2$  optimization (in the case of Gaussian scale mixtures), or a weighted  $\ell_1$  optimization (in the case of Laplacian scale mixtures). We also discussed how to perform Bayesian inference in such models, rather than just computing MAP estimates.

In this section, we discuss an alternative approach based on type II ML estimation (empirical Bayes), whereby we integrate out **w** and maximize the marginal likelihood wrt *τ*. This EB procedure can be implemented via EM, or via a reweighted  $\ell_1$  scheme, as we will explain below. Having estimated the variances, we plug them in to compute the posterior mean of the weights,  $\mathbb{E}[\mathbf{w}|\hat{\tau}, \mathcal{D}]$ ; rather surprisingly (in view of the Gaussian prior), the result is an (approximately) sparse estimate, for reasons we explain below.

In the context of neural networks, this this method is called called **automatic relevance determination** or **ARD** (MacKay 1995b; Neal 1996): see Section ********. In the context of the linear models we are considering in this chapter, this method is called **sparse Bayesian learning** or **SBL** (Tipping 2001). Combining ARD/SBL with basis function expansion in a linear model gives rise to a technique called the relevance vector machine (RVM), which we will discuss in Section 14.3.2.

## **13.7.1 ARD for linear regression**

We will explain the procedure in the context of linear regression; ARD for GLMs requires the use of the Laplace (or some other) approximation. case can be It is conventional, when discussing ARD / SBL, to denote the weight precisions by  $\alpha_j = 1/\tau_j^2$ , and the measurement precision by  $\beta = 1/\sigma^2$  (do not confuse this with the use of  $\beta$  in statistics to represent the regression coefficients!). In particular, we will assume the following model:

$$
p(y|\mathbf{x}, \mathbf{w}, \beta) = \mathcal{N}(y|\mathbf{w}^T \mathbf{x}, 1/\beta)
$$
\n(13.147)

$$
p(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \mathbf{A}^{-1}) \tag{13.148}
$$

where  $\mathbf{A} = \text{diag}(\alpha)$ . The marginal likelihood can be computed analytically as follows:

$$
p(\mathbf{y}|\mathbf{X}, \alpha, \beta) = \int \mathcal{N}(\mathbf{y}|\mathbf{X}\mathbf{w}, \beta \mathbf{I}_N) \mathcal{N}(\mathbf{w}|\mathbf{0}, \mathbf{A}) d\mathbf{w}
$$
 (13.149)

$$
= \mathcal{N}(\mathbf{y}|\mathbf{0}, \beta \mathbf{I}_N + \mathbf{X} \mathbf{A}^{-1} \mathbf{X}^T) \tag{13.150}
$$

$$
= (2\pi)^{-N/2} |\mathbf{C}_{\alpha}|^{-\frac{1}{2}} \exp(-\frac{1}{2} \mathbf{y}^T \mathbf{C}_{\alpha}^{-1} \mathbf{y})
$$
(13.151)

where

$$
\mathbf{C}_{\alpha} \triangleq \beta^{-1} \mathbf{I}_N + \mathbf{X} \mathbf{A}^{-1} \mathbf{X}^T
$$
\n(13.152)

Compare this to the marginal likelihood in Equation 13.13 in the spike and slab model; modulo the  $\beta = 1/\sigma^2$  factor missing from the second term, the equations are the same, except we have replaced the binary  $\gamma_j \in \{0,1\}$  with continuous  $\alpha_j \in \mathbb{R}^+$ . In log form, the objective becomes

$$
\ell(\boldsymbol{\alpha}, \beta) \triangleq -\frac{1}{2}\log p(\mathbf{y}|\mathbf{X}, \boldsymbol{\alpha}, \beta) = \log |\mathbf{C}_{\alpha}| + \mathbf{y}^T \mathbf{C}_{\boldsymbol{\alpha}}^{-1} \mathbf{y}
$$
(13.153)

To regularize the problem, we may put a conjugate prior on each precision,  $\alpha_i \sim \text{Ga}(a, b)$ and  $\beta \sim \text{Ga}(c, d)$ . The modified objective becomes

$$
\ell(\boldsymbol{\alpha}, \beta) \quad \triangleq \quad -\frac{1}{2}\log p(\mathbf{y}|\mathbf{X}, \boldsymbol{\alpha}, \beta) + \sum_{j} \log \text{Ga}(\alpha_j|a, b) + \log \text{Ga}(\beta|c, d) \tag{13.154}
$$

$$
= \log |\mathbf{C}_{\alpha}| + \mathbf{y}^T \mathbf{C}_{\alpha}^{-1} \mathbf{y} + \sum_{j} (a \log \alpha_j - b \alpha_j) + c \log \beta - d\beta \tag{13.155}
$$

This is useful when performing Bayesian inference for  $\alpha$  and  $\beta$  (Bishop and Tipping 2000). However, when performing (type II) point estimation, we will use the improper prior  $a = b =$  $c = d = 0$ , which results in maximal sparsity.

Below we describe how to optimize  $\ell(\alpha, \beta)$  wrt the precision terms  $\alpha$  and  $\beta$ .<sup>7</sup> This is a proxy for finding the most probable model setting of *γ* in the spike and slab model, which in turn is closely related to  $\ell_0$  regularization. In particular, it can be shown (Wipf et al. 2010) that the objective in Equation 13.153 has many fewer local optima than the  $\ell_0$  objective, and hence is much easier to optimize.

Once we have estimated  $\alpha$  and  $\beta$ , we can compute the posterior over the parameters using

$$
p(\mathbf{w}|\mathcal{D}, \hat{\alpha}, \hat{\beta}) = \mathcal{N}(\boldsymbol{\mu}, \boldsymbol{\Sigma})
$$
\n(13.156)

$$
\Sigma^{-1} = \hat{\beta} \mathbf{X}^T \mathbf{X} + \mathbf{A} \tag{13.157}
$$

$$
\mu = \hat{\beta} \Sigma \mathbf{X}^T \mathbf{y} \tag{13.158}
$$

The fact that we compute a posterior over **w**, while simultaneously encouraging sparsity, is why the method is called "sparse Bayesian learning". Nevertheless, since there are many ways to be sparse and Bayesian, we will use the "ARD" term instead, even in the linear model context. (In addition, SBL is only "being Bayesian" about the values of the coefficients, rather than reflecting uncertainty about the set of relevant variables, which is typically of more interest.)

<sup>7.</sup> An alternative approach to optimizing  $\beta$  is to put a Gamma prior on  $\beta$  and to integrate it out to get a Student posterior for **w** (Buntine and Weigend 1991). However, it turns out that this results in a less accurate estimate for *α* (MacKay 1999). In addition, working with Gaussians is easier than working with the Student distribution, and the Gaussian case generalizes more easily to other cases such as logistic regression.

Image /page/12/Figure/1 description: The image displays two plots, labeled (a) and (b). Plot (a) shows a coordinate system with x and y axes. An ellipse, colored red, is centered at the origin and is tilted. A blue arrow originates from the center and points into the ellipse. A dotted green circle is also shown, centered at the origin and smaller than the ellipse. Plot (b) also shows a coordinate system with x and y axes. A dashed red and green circle, labeled C, is centered at the origin and appears to be a unit circle.

**Figure 13.20** Illustration of why ARD results in sparsity. The vector of inputs **x** does not point towards the vector of outputs **y**, so the feature should be removed. (a) For finite  $\alpha$ , the probability density is spread in directions away from **y**. (b) When  $\alpha = \infty$ , the probability density at **y** is maximized. Based on Figure 8 of (Tipping 2001).

## **13.7.2 Whence sparsity?**

If  $\hat{\alpha}_j \approx 0$ , we find  $\hat{w}_j \approx \hat{w}_j^{mle}$ , since the Gaussian prior shrinking  $w_j$  towards 0 has zero precision. However, if we find that  $\hat{\alpha}_j \approx \infty$ , then the prior is very confident that  $w_j = 0$ , and hence that feature j is "irrelevant". Hence the posterior mean will have  $\hat{w}_j \approx 0$ . Thus irrelevant features automatically have their weights "turned off" or "pruned out".

We now give an intuitive argument, based on (Tipping 2001), about why ML-II should encourage  $\alpha_i \to \infty$  for irrelevant features. Consider a 1d linear regression with 2 training examples, so  $X = x = (x_1, x_2)$ , and  $y = (y_1, y_2)$ . We can plot x and y as vectors in the plane, as shown in Figure 13.20. Suppose the feature is irrelevant for predicting the response, so **x** points in a nearly orthogonal direction to **y**. Let us see what happens to the marginal likelihood as we change  $\alpha$ . The marginal likelihood is given by  $p(\mathbf{y}|\mathbf{x}, \alpha, \beta) = \mathcal{N}(\mathbf{y}|\mathbf{0}, \mathbf{C})$ , where

$$
\mathbf{C} = \frac{1}{\beta} \mathbf{I} + \frac{1}{\alpha} \mathbf{x} \mathbf{x}^T
$$
 (13.159)

If  $\alpha$  is finite, the posterior will be elongated along the direction of **x**, as in Figure 13.20(a). However, if  $\alpha = \infty$ , we find  $\mathbf{C} = \frac{1}{\beta} \mathbf{I}$ , so **C** is spherical, as in Figure 13.20(b). If  $|\mathbf{C}|$  is held constant, the latter assigns higher probability density to the observed response vector **y**, so this is the preferred solution. In other words, the marginal likelihood "punishes" solutions where  $\alpha_i$ is small but  $\mathbf{X}_{:,j}$  is irrelevant, since these waste probability mass. It is more parsimonious (from the point of view of Bayesian Occam's razor) to eliminate redundant dimensions.

## **13.7.3 Connection to MAP estimation**

ARD seems quite different from the MAP estimation methods we have been considering earlier in this chapter. In particular, in ARD, we are not integrating out  $\alpha$  and optimizing **w**, but vice

versa. Because the parameters  $w_i$  become correlated in the posterior (due to explaining away), when we estimate  $\alpha_i$  we are borrowing information from all the features, not just feature j. Consequently, the effective prior  $p(\mathbf{w}|\hat{\alpha})$  is **non-factorial**, and furthermore it depends on the data D (and  $\sigma^2$ ). However, in (Wipf and Nagarajan 2007), it was shown that ARD can be viewed as the following MAP estimation problem:

$$
\hat{\mathbf{w}}^{ARD} = \arg \min_{\mathbf{w}} \beta ||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2 + g_{ARD}(\mathbf{w})
$$
\n(13.160)

$$
g_{ARD}(\mathbf{w}) \triangleq \min_{\alpha \ge 0} \sum_{j} \alpha_j w_j^2 + \log |\mathbf{C}_{\alpha}| \tag{13.161}
$$

The proof, which is based on convex analysis, is a little complicated and hence is omitted.

Furthermore, (Wipf and Nagarajan 2007; Wipf et al. 2010) prove that MAP estimation with non-factorial priors is strictly better than MAP estimation with any possible factorial prior in the following sense: the non-factorial objective always has fewer local minima than factorial objectives, while still satisfying the property that the global optimum of the non-factorial objective corresponds to the global optimum of the  $\ell_0$  objective — a property that  $\ell_1$  regularization, which has no local minima, does not enjoy.

## **13.7.4 Algorithms for ARD \***

In this section, we review several different algorithms for implementing ARD.

### ********** EM algorithm**

The easiest way to implement SBL/ARD is to use EM. The expected complete data log likelihood is given by

$$
Q(\boldsymbol{\alpha}, \beta) = \mathbb{E} \left[ \log \mathcal{N}(\mathbf{y} | \mathbf{X} \mathbf{w}, \sigma^2 \mathbf{I}) + \log \mathcal{N}(\mathbf{w} | \mathbf{0}, \mathbf{A}^{-1}) \right]
$$
(13.162)  
$$
= \frac{1}{2} \mathbb{E} \left[ N \log \beta - \beta ||\mathbf{y} - \mathbf{X} \mathbf{w}||^2 + \sum_j \log \alpha_j - \text{tr}(\mathbf{A} \mathbf{w} \mathbf{w}^T) \right] + \text{const} (13.163)
$$

$$
= \frac{1}{2}N\log\beta - \frac{\beta}{2}\left(||\mathbf{y} - \mathbf{X}\boldsymbol{\mu}||^2 + \text{tr}(\mathbf{X}^T\mathbf{X}\boldsymbol{\Sigma})\right) + \frac{1}{2}\sum_{j}\log\alpha_j - \frac{1}{2}\text{tr}[\mathbf{A}(\boldsymbol{\mu}\boldsymbol{\mu}^T + \boldsymbol{\Sigma})] + \text{const} \quad (13.164)
$$

where  $\mu$  and  $\Sigma$  are computed in the E step using Equation 13.158.

Suppose we put a  $Ga(a, b)$  prior on  $\alpha_i$  and a  $Ga(c, d)$  prior on  $\beta$ . The penalized objective becomes

$$
Q'(\alpha, \beta) = Q(\alpha, \beta) + \sum_{j} (a \log \alpha_j - b \alpha_j) + c \log \beta - d\beta
$$
\n(13.165)

Setting  $\frac{dQ'}{d\alpha_j} = 0$  we get the following M step:

$$
\alpha_j = \frac{1+2a}{\mathbb{E}\left[w_j^2\right] + 2b} = \frac{1+2a}{m_j^2 + \Sigma_{jj} + 2b} \tag{13.166}
$$

If  $\alpha_i = \alpha$ , and  $a = b = 0$ , the update becomes

$$
\alpha = \frac{D}{\mathbb{E}\left[\mathbf{w}^T \mathbf{w}\right]} = \frac{D}{\boldsymbol{\mu}^T \boldsymbol{\mu} + \text{tr}(\boldsymbol{\Sigma})}
$$
(13.167)

The update for  $\beta$  is given by

$$
\beta_{new}^{-1} = \frac{||\mathbf{y} - \mathbf{X}\boldsymbol{\mu}||^2 + \beta^{-1} \sum_{j} (1 - \alpha_j \Sigma_{jj}) + 2d}{N + 2c}
$$
\n(13.168)

(Deriving this is Exercise 13.2.)

## **13.7.4.2 Fixed-point algorithm**

A faster and more direct approach is to directly optimize the objective in Equation 13.155. One can show (Exercise 13.3) that the equations  $\frac{d\ell}{d\alpha_j} = 0$  and  $\frac{d\ell}{d\beta} = 0$  lead to the following fixed point updates:

$$
\alpha_j \quad \leftarrow \quad \frac{\gamma_j + 2a}{m_j^2 + 2b} \tag{13.169}
$$

$$
\beta^{-1} \leftarrow \frac{||\mathbf{y} - \mathbf{X}\boldsymbol{\mu}||^2 + 2d}{N - \sum_j \gamma_j + 2c} \tag{13.170}
$$

$$
\gamma_j \quad \triangleq \quad 1 - \alpha_j \Sigma_{jj} \tag{13.171}
$$

The quantity  $\gamma_j$  is a measure of how well-determined  $w_j$  is by the data (MacKay 1992). Hence  $\gamma = \sum_j \gamma_j$  is the effective degrees of freedom of the model. See Section 7.5.3 for further discussion.

Since  $\alpha$  and  $\beta$  both depend on  $\mu$  and  $\Sigma$  (which can be computed using Equation 13.158 or the Laplace approximation), we need to re-estimate these equations until convergence. (Convergence properties of this algorithm have been studied in (Wipf and Nagarajan 2007).) At convergence, the results are formally identical to those obtained by EM, but since the objective is non-convex, the results can depend on the initial values.

### ********** Iteratively reweighted $\ell_1$ algorithm**

Another approach to solving the ARD problem is based on the view that it is a MAP estimation problem. Although the log prior  $q(\mathbf{w})$  is rather complex in form, it can be shown to be a non-decreasing, concave function of  $|w_i|$ . This means that it can be solved by an iteratively reweighted  $\ell_1$  problem of the form

$$
\mathbf{w}^{t+1} = \arg\min_{\mathbf{w}} \text{NLL}(\mathbf{w}) + \sum_{j} \lambda_j^{(t)} |w_j|
$$
\n(13.172)

In (Wipf and Nagarajan 2010), the following procedure for setting the penalty terms is suggested (based on a convex bound to the penalty function). We initialize with  $\lambda_j^{(0)} = 1$ , and then at

iteration  $t + 1$ , compute  $\lambda_j^{(t+1)}$  by iterating the following equation a few times:<sup>8</sup>

$$
\lambda_j \leftarrow \left[ \mathbf{X}_{:,j} \left( \sigma^2 \mathbf{I} + \mathbf{X} \text{diag}(1/\lambda_j) \text{diag}(|w_j^{(t+1)}|) \right)^{-1} \mathbf{X}^T)^{-1} \mathbf{X}_{:,j} \right]^{\frac{1}{2}}
$$
(13.173)

We see that the new penalty  $\lambda_j$  depends on *all* the old weights. This is quite different from the adaptive lasso method of Section 13.6.2.

To understand this difference, consider the noiseless case where  $\sigma^2 = 0$ , and assume  $D \gg N$ . In this case, there are  $\begin{pmatrix} D \\ N \end{pmatrix}$ N solutions which perfectly reconstruct the data,  $\mathbf{Xw} = \mathbf{y}$ , and which have sparsity  $||\mathbf{w}||_0 = N$ ; these are called basic feasible solutions or BFS. What we want are solutions that satsify  $\mathbf{Xw} = \mathbf{y}$  but which are much sparser than this. Suppose the method has found a BFS. We do not want to increase the penalty on a weight just because it is small (as in adaptive lasso), since that will just reinforce our current local optimum. Instead, we want to increase the penalty on a weight if it is small and if we have  $||\mathbf{w}^{(t+1)}|| < N$ . The covariance term  $(\mathbf{X}diag(1/\lambda_j)diag(|w_j^{(t+1)}|))^{-1}$  has this effect: if **w** is a BFS, this matrix will be full rank, so the penalty will not increase much, but if **w** is sparser than N, the matrix will not be full rank, so the penalties associated with zero-valued coefficients will increase, thus reinforcing this solution (Wipf and Nagarajan 2010).

## **13.7.5 ARD for logistic regression**

Now consider binary logistic regression,  $p(y|\mathbf{x}, \mathbf{w}) = \text{Ber}(y|\text{sign}(\mathbf{w}^T\mathbf{x}))$ , using the same Gaussian prior,  $p(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \mathbf{A}^{-1})$ . We can no longer use EM to estimate  $\alpha$ , since the Gaussian prior is not conjugate to the logistic likelihood, so the E step cannot be done exactly. One approach is to use a variational approximation to the E step, as discussed in Section ********. A simpler approach is to use a Laplace approximation (see Section 8.4.1) in the E step. We can then use this approximation inside the same EM procedure as before, except we no longer need to update  $\beta$ . Note, however, that this is not guaranteed to converge.

An alternative is to use the techniques from Section ********. In this case, we can use exact methods to compute the inner weighted  $\ell_1$  regularized logistic regression problem, and no approximations are required.

# **13.8 Sparse coding \***

So far, we have been concentrating on sparse priors for supervised learning. In this section, we discuss how to use them for unsupervised learning.

In Section 12.6, we discussed ICA, which is like PCA except it uses a non-Gaussian prior for the latent factors  $z_i$ . If we make the non-Gaussian prior be sparsity promoting, such as a Laplace distribution, we will be approximating each observed vector  $\mathbf{x}_i$  as a sparse combination of basis vectors (columns of W); note that the sparsity pattern (controlled by  $z_i$ ) changes from data case to data case. If we relax the constraint that **W** is orthogonal, we get a method called

<sup>8.</sup> The algorithm in (Wipf and Nagarajan 2007) is equivalent to a single iteration of Equation 13.173. However, since the equation is cheap to compute (only  $O(ND||w^{(t+1)}||_0)$  time), it is worth iterating a few times before solving the more expensive  $\ell_1$  problem.

| Method        | $p(z_i)$  | $p(\mathbf{W})$ | W orthogonal |
|---------------|-----------|-----------------|--------------|
| PCA           | Gauss     | -               | yes          |
| FA            | Gauss     | -               | no           |
| ICA           | Non-Gauss | -               | yes          |
| Sparse coding | Laplace   | -               | no           |
| Sparse PCA    | Gauss     | Laplace         | maybe        |
| Sparse MF     | Laplace   | Laplace         | no           |

**Table 13.3** Summary of various latent factor models. A dash "-" in the  $p(\mathbf{W})$  column means we are performing ML parameter estimation rather than MAP parameter estimation. Summary of abbreviations:  $PCA$  = principal components analysis;  $FA$  = factor analysis;  $ICA$  = independent components analysis;  $MF =$ matrix factorization.

**sparse coding**. In this context, we call the factor loading matrix **W** a **dictionary**; each column is referred to as an **atom**.<sup>9</sup> In view of the sparse representation, it is common for  $L > D$ , in which case we call the representation **overcomplete**.

In sparse coding, the dictionary can be fixed or learned. If it is fixed, it is common to use a wavelet or DCT basis, since many natural signals can be well approximated by a small number of such basis functions. However, it is also possible to learn the dictionary, by maximizing the likelihood

$$
\log p(\mathcal{D}|\mathbf{W}) = \sum_{i=1}^{N} \log \int_{\mathbf{z}_i} \mathcal{N}(\mathbf{x}_i|\mathbf{W}\mathbf{z}_i, \sigma^2 \mathbf{I}) p(\mathbf{z}_i) d\mathbf{z}_i
$$
\n(13.174)

We discuss ways to optimize this below, and then we present several interesting applications.

Do not confuse sparse coding with **sparse PCA** (see e.g., (Witten et al. 2009; Journee et al. 2010)): this puts a sparsity promoting prior on the regression weights **W**, whereas in sparse coding, we put a sparsity promoting prior on the latent factors  $z_i$ . Of course, the two techniques can be combined; we call the result **sparse matrix factorization**, although this term is nonstandard. See Table 13.3 for a summary of our terminology.

## **13.8.1 Learning a sparse coding dictionary**

Since Equation 13.174 is a hard objective to maximize, it is common to make the following approximation:

$$
\log p(\mathcal{D}|\mathbf{W}) \approx \sum_{i=1}^{N} \max_{\mathbf{z}_i} \left[ \log \mathcal{N}(\mathbf{x}_i | \mathbf{W} \mathbf{z}_i, \sigma^2 \mathbf{I}) + \log p(\mathbf{z}_i) \right]
$$
(13.175)

If  $p(\mathbf{z}_i)$  is Laplace, we can rewrite the NLL as

$$
NLL(\mathbf{W}, \mathbf{Z}) = \sum_{i=1}^{N} \frac{1}{2} ||\mathbf{x}_i - \mathbf{W} \mathbf{z}_i||_2^2 + \lambda ||\mathbf{z}_i||_1
$$
\n(13.176)

<sup>9.</sup> It is common to denote the dictionary by **D**, and to denote the latent factors by  $\alpha_i$ . However, we will stick with the **<sup>W</sup>** and **<sup>z</sup>**<sup>i</sup> notation.

To prevent **W** from becoming arbitrarily large, it is common to constrain the  $\ell_2$  norm of its columns to be less than or equal to 1. Let us denote this constraint set by

$$
\mathcal{C} = \{ \mathbf{W} \in \mathbb{R}^{D \times L} \quad \text{s.t.} \quad \mathbf{w}_j^T \mathbf{w}_j \le 1 \}
$$
\n(13.177)

Then we want to solve  $\min_{\mathbf{W}\in\mathcal{C},\mathbf{Z}\in\mathbb{R}^{N\times L}}\text{NLL}(\mathbf{W},\mathbf{Z})$ . For a fixed  $\mathbf{z}_i$ , the optimization over **W** is a simple least squares problem. And for a fixed dictionary **W**, the optimization problem over **Z** is identical to the lasso problem, for which many fast algorithms exist. This suggests an obvious iterative optimization scheme, in which we alternate between optimizing **W** and **Z**. (Mumford 1994) called this kind of approach an **analysis-synthesis** loop, where estimating the basis **W** is the analysis phase, and estimating the coefficients **Z** is the synthesis phase. In cases where this is too slow, more sophisticated algorithms can be used, see e.g., (Mairal et al. 2010).

A variety of other models result in an optimization problem that is similar to Equation 13.176. For example, **non-negative matrix factorization** or **NMF** (Paatero and Tapper 1994; Lee and Seung 2001) requires solving an objective of the form

$$
\min_{\mathbf{W}\in\mathcal{C},\mathbf{Z}\in\mathbb{R}^{L\times N}}\frac{1}{2}\sum_{i=1}^{N}||\mathbf{x}_i-\mathbf{W}\mathbf{z}_i||_2^2 \quad \text{s.t.} \quad \mathbf{W}\geq 0, \mathbf{z}_i\geq 0 \tag{13.178}
$$

(Note that this has no hyper-parameters to tune.) The intuition behind this constraint is that the learned dictionary may be more interpretable if it is a positive sum of positive "parts", rather than a sparse sum of atoms that may be positive or negative. Of course, we can combine NMF with a sparsity promoting prior on the latent factors. This is called **non-negative sparse coding** (Hoyer 2004).

Alternatively, we can drop the positivity constraint, but impose a sparsity constraint on both the factors  $z_i$  and the dictionary **W**. We call this **sparse matrix factorization**. To ensure strict convexity, we can use an elastic net type penalty on the weights (Mairal et al. 2010) resulting in

$$
\min_{\mathbf{W},\mathbf{Z}} \frac{1}{2} \sum_{i=1}^{N} ||\mathbf{x}_i - \mathbf{W} \mathbf{z}_i||_2^2 + \lambda ||\mathbf{z}_i||_1 \quad \text{s.t.} \quad ||\mathbf{w}_j||_2^2 + \gamma ||\mathbf{w}_j||_1 \le 1 \tag{13.179}
$$

There are several related objectives one can write down. For example, we can replace the lasso NLL with group lasso or fused lasso (Witten et al. 2009).

We can also use other sparsity-promoting priors besides the Laplace. For example, (Zhou et al. 2009) propose a model in which the latent factors  $z_i$  are made sparse using the binary mask model of Section 13.2.2. Each bit of the mask can be generated from a Bernoulli distribution with parameter  $\pi$ , which can be drawn from a beta distribution. Alternatively, we can use a non-parametric prior, such as the beta process. This allows the model to use dictionaries of unbounded size, rather than having to specify  $L$  in advance. One can perform Bayesian inference in this model using e.g., Gibbs sampling or variational Bayes. One finds that the effective size of the dictionary goes down as the noise level goes up, due to the Bayesian Occam's razor. This can prevent overfitting. See (Zhou et al. 2009) for details.

## **13.8.2 Results of dictionary learning from image patches**

One reason that sparse coding has generated so much interest recently is because it explains an interesting phenomenon in neuroscience. In particular, the dictionary that is learned by applying

Image /page/18/Figure/1 description: This image displays six grids of small square images, labeled (a) through (f). Each grid contains 64 smaller images arranged in an 8x8 matrix. The smaller images appear to be visual filters or features, with varying patterns of light and dark shades, suggesting different orientations and textures. Grids (a), (c), and (e) are arranged on the left side of the image, stacked vertically. Grids (b), (d), and (f) are arranged on the right side, also stacked vertically. The overall impression is a collection of learned visual features from different methods.

Figure 13.21 Illustration of the filters learned by various methods when applied to natural image patches. (Each patch is first centered and normalized to unit norm.) (a) ICA. Figure generated by icaBasisDemo, kindly provided by Aapo Hyvarinen. (b) sparse coding. (c) PCA. (d) non-negative matrix factorization. (e) sparse PCA with low sparsity on weight matrix. (f) sparse PCA with high sparsity on weight matrix. Figure generated by sparseDictDemo, written by Julien Mairal.

sparse coding to patches of natural images consists of basis vectors that look like the filters that are found in simple cells in the primary visual cortex of the mammalian brain (Olshausen and Field 1996). In particular, the filters look like bar and edge detectors, as shown in Figure 13.21(b). (In this example, the parameter  $\lambda$  was chosen so that the number of active basis functions (non-zero components of  $z_i$ ) is about 10.) Interestingly, using ICA gives visually similar results, as shown in Figure 13.21(a). By contrast, applying PCA to the same data results in sinusoidal gratings, as shown in Figure 13.21(c); these do not look like cortical cell response patterns.<sup>10</sup> It has therefore been conjectured that parts of the cortex may be performing sparse coding of the sensory input; the resulting latent representation is then further processed by higher levels of the brain.

Figure 13.21(d) shows the result of using NMF, and Figure 13.21(e-f) show the results of sparse PCA, as we increase the sparsity of the basis vectors.

## **13.8.3 Compressed sensing**

Although it is interesting to look at the dictionaries learned by sparse coding, it is not necessarily very useful. However, there are some practical applications of sparse coding, which we discuss below.

Imagine that, instead of observing the data  $\mathbf{x} \in \mathbb{R}^D$ , we observe a low-dimensional projection of it,  $y = Rx + \epsilon$  where  $y \in \mathbb{R}^{\tilde{M}}$ , **R** is a  $M \times D$  matrix,  $M \ll D$ , and  $\epsilon$  is a noise term (usually Gaussian). We assume **R** is a known sensing matrix, corresponding to different linear projections of **x**. For example, consider an MRI scanner: each beam direction corresponds to a vector, encoded as a row in **R**. Figure 13.22 illustrates the modeling assumptions.

Our goal is to infer  $p(x|y, R)$ . How can we hope to recover all of x if we do not measure all of **x**? The answer is: we can use Bayesian inference with an appropriate prior, that exploits the fact that natural signals can be expressed as a weighted combination of a small number of suitably chosen basis functions. That is, we assume  $\mathbf{x} = \mathbf{Wz}$ , where **z** has a sparse prior, and **W** is suitable dictionary. This is called **compressed sensing** or **compressive sensing** (Candes et al. 2006; Baruniak 2007; Candes and Wakin 2008; Bruckstein et al. 2009).

For CS to work, it is important to represent the signal in the right basis, otherwise it will not be sparse. In traditional CS applications, the dictionary is fixed to be a standard form, such as wavelets. However, one can get much better performance by learning a domain-specific dictionary using sparse coding (Zhou et al. 2009). As for the sensing matrix **R**, it is often chosen to be a random matrix, for reasons explained in (Candes and Wakin 2008). However, one can get better performance by adapting the projection matrix to the dictionary (Seeger and Nickish 2008; Chang et al. 2009).

## **13.8.4 Image inpainting and denoising**

Suppose we have an image which is corrupted in some way, e.g., by having text or scratches sparsely superimposed on top of it, as in Figure 13.23. We might want to estimate the underlying

<sup>10.</sup> The reason PCA discovers sinusoidal grating patterns is because it is trying to model the covariance of the data, which, in the case of image patches, is translation invariant. This means  $cov[I(x, y), I(x', y')] = f[(x - x')^2 + (y - y')^2]$ <br>for some function f, where  $I(x, y)$  is the image intensity at location  $(x, y)$ . One can show (Hyvarinen et al. 2009, nl25) for some function f, where  $I(x, y)$  is the image intensity at location  $(x, y)$ . One can show (Hyvarinen et al. 2009, p125) that the eigenvectors of a matrix of this kind are always sinusoids of different phases, i.e., PCA discovers a Fourier basis.

Image /page/20/Figure/1 description: This is a directed graphical model. The variables are z, lambda, x, W, y, and R. There is an arrow from z to x, from lambda to z, from x to y, and from W to x, and from R to y. The variables z and x are represented by white circles, and the variables y and R are represented by gray circles. The variables lambda and W are represented by white circles.

**Figure 13.22** Schematic DGM for compressed sensing. We observe a low dimensional measurement **y** generated by passing **x** through a measurement matrix **R**, and possibly subject to observation noise with variance  $\sigma^2$ . We assume that **x** has a sparse decomposition in terms of the dictionary **W** and the latent variables **z**. the parameter  $\lambda$  controlls the sparsity level.

Image /page/20/Figure/3 description: Two images are presented side-by-side, labeled (a) and (b). Image (a) is a photograph of a street scene with a horse-drawn carriage. Red text is overlaid on the image, detailing the history of New Orleans. The text reads: "Since 1699, when French explorers landed at the great bend of the Mississippi River and celebrated the first Mardi Gras in North America, New Orleans has brewed a fascinating melange of cultures. It was French, then Spanish, then French again, then sold to the United States. Through all these years, and even into the 1900s, others arrived from everywhere: Acadians (Cajuns), Africans, indige-". Image (b) shows a similar street scene with a horse-drawn carriage, but without any overlaid text. The carriage in image (b) is occupied by several people, and a driver is seated at the front. The buildings in the background have balconies with ornate railings.

**Figure 13.23** An example of image inpainting using sparse coding. Left: original image. Right: reconstruction. Source: Figure 13 of (Mairal et al. 2008). Used with kind permission of Julien Mairal.

"clean" image. This is called **image inpainting**. One can use similar techniques for **image denoising**.

We can model this as a special kind of compressed sensing problem. The basic idea is as follows. We partition the image into overlapping patches,  $y_i$ , and concatenate them to form  $y$ . We define **R** so that the *i*'th row selects out patch *i*. Now define  $V$  to be the visible (uncorrupted) components of  $y$ , and  $H$  to be the hidden components. To perform image inpainting, we just compute  $p(\mathbf{y}_H|\mathbf{y}_V, \theta)$ , where  $\theta$  are the model parameters, which specify the dictionary **W** and the sparsity level  $\lambda$  of **z**. We can either learn a dictionary offline from a database of images, or we can learn a dictionary just for this image, based on the non-corrupted patches.

Figure 13.23 shows this technique in action. The dictionary (of size 256 atoms) was learned from  $7 \times 10^6$  undamaged  $12 \times 12$  color patches in the 12 mega-pixel image.

An alternative approach is to use a graphical model (e.g., the **fields of experts** model (S.

and Black 2009)) which directly encodes correlations between neighboring image patches, rather than using a latent variable model. Unfortunately such models tend to be computationally more expensive.

# **Exercises**

**Exercise 13.1** Partial derivative of the RSS

Define

$$
RSS(\mathbf{w}) = ||\mathbf{X}\mathbf{w} - \mathbf{y}||_2^2 \tag{13.180}
$$

a. Show that

∂

$$
\frac{\partial}{\partial w_k} RSS(\mathbf{w}) = a_k w_k - c_k \tag{13.181}
$$

$$
a_k = 2\sum_{i=1}^n x_{ik}^2 = 2||\mathbf{x}_{:,k}||^2
$$
\n(13.182)

$$
c_k = 2 \sum_{i=1}^{n} x_{ik} (y_i - \mathbf{w}_{-k}^T \mathbf{x}_{i,-k}) = 2 \mathbf{x}_{:,k}^T \mathbf{r}_k
$$
\n(13.183)

where  $\mathbf{w}_{-k} = \mathbf{w}$  without component k,  $\mathbf{x}_{i,-k}$  is  $\mathbf{x}_i$  without component k, and  $\mathbf{r}_k = \mathbf{y} - \mathbf{w}_{-k}^T \mathbf{x}_{i,-k}$ <br>is the residual due to using all the features except feature k. Hint: Partition the weig is the residual due to using all the features except feature  $k$ . Hint: Partition the weights into those involving  $k$  and those not involving  $k$ .

b. Show that if  $\frac{\partial}{\partial w_k} RSS(\mathbf{w}) = 0$ , then

$$
\hat{w}_k = \frac{\mathbf{x}_{:,k}^T \mathbf{r}_k}{||\mathbf{x}_{:,k}||^2}
$$
\n(13.184)

Hence when we sequentially add features, the optimal weight for feature  $k$  is computed by computing orthogonally projecting  $x_{:,k}$  onto the current residual.

**Exercise 13.2** Derivation of M step for EB for linear regression

Derive Equations 13.166 and 13.168. Hint: the following identity should be useful

$$
\Sigma \mathbf{X}^T \mathbf{X} = \Sigma \mathbf{X}^T \mathbf{X} + \beta^{-1} \Sigma \mathbf{A} - \beta^{-1} \Sigma \mathbf{A}
$$
\n(13.185)

 $=$  **Σ**(**X**<sup>T</sup>**X**β + **A**)β<sup>-1</sup> − β<sup>-1</sup> **ΣA** (13.186)

$$
= (\mathbf{A} + \beta \mathbf{X}^T \mathbf{X})^{-1} (\mathbf{X}^T \mathbf{X} \beta + \mathbf{A}) \beta^{-1} - \beta^{-1} \Sigma \mathbf{A}
$$
\n(13.187)

$$
= (\mathbf{I} - \mathbf{A}\boldsymbol{\Sigma})\boldsymbol{\beta}^{-1} \tag{13.188}
$$

**Exercise 13.3** Derivation of fixed point updates for EB for linear regression

Derive Equations 13.169 and 13.170. Hint: The easiest way to derive this result is to rewrite  $\log p(\mathcal{D}|\alpha,\beta)$ as in Equation 8.54. This is exactly equivalent, since in the case of a Gaussian prior and likelihood, the posterior is also Gaussian, so the Laplace "approximation" is exact. In this case, we get

$$
\log p(\mathcal{D}|\boldsymbol{\alpha},\beta) = \frac{N}{2}\log \beta - \frac{\beta}{2}||\mathbf{y} - \mathbf{X}\mathbf{w}||^2 + \frac{1}{2}\sum_{j}\log \alpha_j - \frac{1}{2}\mathbf{m}^T \mathbf{A}\mathbf{m} + \frac{1}{2}\log |\boldsymbol{\Sigma}| - \frac{D}{2}\log(2\pi)
$$
 $(13.189)$ 

The rest is straightforward algebra.

**Exercise 13.4** Marginal likelihood for linear regression

Suppose we use a *g*-prior of the form  $\Sigma_{\gamma} = g(\mathbf{X}_{\gamma}^{T} \mathbf{X}_{\gamma})^{-1}$ . Show that Equation 13.16 simplifies to

$$
p(\mathcal{D}|\boldsymbol{\gamma}) \propto (1+g)^{-D_{\gamma}/2} (2b_{\sigma} + S(\boldsymbol{\gamma}))^{-(2a_{\sigma}+N-1)/2}
$$
\n(13.190)

$$
S(\boldsymbol{\gamma}) = \mathbf{y}^T \mathbf{y} - \frac{g}{1+g} \mathbf{y}^T \mathbf{X}_{\gamma} (\mathbf{X}_{\gamma}^T \mathbf{X}_{\gamma})^{-1} \mathbf{X}_{\gamma}^T \mathbf{y}
$$
(13.191)

**Exercise 13.5** Reducing elastic net to lasso

Define

$$
J_1(\mathbf{w}) = |\mathbf{y} - \mathbf{X}\mathbf{w}|^2 + \lambda_2 |\mathbf{w}|^2 + \lambda_1 |\mathbf{w}|_1
$$
\n(13.192)

and

$$
J_2(\mathbf{w}) = |\tilde{\mathbf{y}} - \tilde{\mathbf{X}}\tilde{\mathbf{w}}|^2 + c\lambda_1 |\mathbf{w}|_1
$$
\n(13.193)

where  $c = (1 + \lambda_2)^{-\frac{1}{2}}$  and

$$
\tilde{\mathbf{X}} = c \begin{pmatrix} \mathbf{X} \\ \sqrt{\lambda}_2 \mathbf{I}_d \end{pmatrix}, \quad \tilde{\mathbf{y}} = \begin{pmatrix} \mathbf{y} \\ \mathbf{0}_{d \times 1} \end{pmatrix}
$$
\n(13.194)

Show

$$
\arg\min J_1(\mathbf{w}) = c(\arg\min J_2(\mathbf{w}))
$$
\n(13.195)

i.e.

$$
J_1(c\mathbf{w}) = J_2(\mathbf{w}) \tag{13.196}
$$

and hence that one can solve an elastic net problem using a lasso solver on modified data.

**Exercise 13.6** Shrinkage in linear regression

(Source: Jaakkola.) Consider performing linear regression with an orthonormal design matrix, so  $||\mathbf{x}_{:k}||_2^2 = 1$  for each column (feature) k, and  $\mathbf{x}_{:k}^T \mathbf{x}_{:j} = 0$ , so we can estimate each parameter  $w_k$  separate

Figure 13.24 plots  $\hat{w}_k$  vs  $c_k = 2\mathbf{y}^T \mathbf{x}_{:,k}$ , the correlation of feature k with the response, for 3 different esimation methods: ordinary least squares (OLS), ridge regression with parameter  $\lambda_2$ , and lasso wi parameter  $\lambda_1$ .

- a. Unfortunately we forgot to label the plots. Which method does the solid (1), dotted (2) and dashed (3) line correspond to? Hint: see Section 13.3.3.
- b. What is the value of  $\lambda_1$ ?
- c. What is the value of  $\lambda_2$ ?

**Exercise 13.7** Prior for the Bernoulli rate parameter in the spike and slab model

Consider the model in Section 13.2.1. Suppose we put a prior on the sparsity rates,  $\pi_i \sim \text{Beta}(\alpha_1, \alpha_2)$ . Derive an expression for  $p(\gamma|\alpha)$  after integrating out the  $\pi_j$ 's. Discuss some advantages and disadvantages of this approach compared to assuming  $\pi_j = \pi_0$  for fixed  $\pi_0$ .

Image /page/23/Figure/1 description: A line graph shows the relationship between Ck on the x-axis and Wk on the y-axis. The x-axis ranges from -2 to 2, and the y-axis ranges from -1 to 1. There are three lines plotted: a thick red line labeled '1', a thin dotted green line labeled '2', and a thick dotted black line labeled '3'. The red line is a straight line passing through (-2, -1) and (2, 1). The green line starts at approximately (-2, -0.4), increases linearly to approximately (0, 0), and then continues to increase linearly to (2, 0.45). The black line starts at approximately (-2, -0.4), remains constant at -0.4 until Ck = -1, then increases linearly to (0, 0), remains constant at 0 until Ck = 1, and then increases linearly to (2, 0.45).

**Figure 13.24** Plot of  $\hat{w}_k$  vs amount of correlation  $c_k$  for three different estimators.

## **Exercise 13.8** Deriving E step for GSM prior

Show that

$$
\mathbb{E}\left[\frac{1}{\tau_j^2}|w_j\right] = \frac{\pi'(w_j)}{|w_j|} \tag{13.197}
$$

where  $\pi(w_j) = -\log p(w_j)$  and  $p(w_j) = int \mathcal{N}(w_j | 0, \tau_j^2) p(\tau_j^2) d\tau_j^2$ . Hint 1:

$$
\frac{1}{\tau_j^2} \mathcal{N}(w_j | 0, \tau_j^2) \propto \frac{1}{\tau_j^2} \exp(-\frac{w_j^2}{2\tau_j^2})
$$
\n(13.198)

$$
= \frac{-1}{|w_j|} \frac{-2w_j}{2\tau_j^2} \exp(-\frac{w_j^2}{2\tau_j^2})
$$
\n(13.199)

$$
= \frac{-1}{|w_j|} \frac{d}{d|w_j|} \mathcal{N}(w_j|0, \tau_j^2)
$$
\n(13.200)

Hint 2:

$$
\frac{d}{d|w_j|}p(w_j) = \frac{1}{p(w_j)}\frac{d}{d|w_j|}\log p(w_j)
$$
\n(13.201)

**Exercise 13.9** EM for sparse probit regression with Laplace prior

Derive an EM algorithm for fitting a binary probit classifier (Section 9.4) using a Laplace prior on the weights. (If you get stuck, see (Figueiredo 2003; Ding and Harrison 2010).)

## **Exercise 13.10** GSM representation of group lasso

Consider the prior  $\tau_j^2 \sim \text{Ga}(\delta, \rho^2/2)$ , ignoring the grouping issue for now. The marginal distribution induced on the weights by a Gamma mixing distribution is called the **normal Gamma** distribution and is

given by

$$
NG(w_j|\delta,\rho) = \int \mathcal{N}(w_j|0,\tau_j^2)Ga(\tau_j^2|\delta,\rho^2/2)d\tau_j^2
$$
\n(13.202)

$$
= \frac{1}{Z} |w_j|^{\delta - 1/2} \mathcal{K}_{\delta - \frac{1}{2}}(\rho |w_j|) \tag{13.203}
$$

$$
1/Z = \frac{\rho^{\delta + \frac{1}{2}}}{\sqrt{\pi} \ 2^{\delta - 1/2} \ \rho(\delta)} \tag{13.204}
$$

where  $\mathcal{K}_{\alpha}(x)$  is the modified Bessel function of the second kind (the besselk function in Matlab). Now suppose we have the following prior on the variances

$$
p(\sigma_{1:D}^2) = \prod_{g=1}^G p(\sigma_{1:d_g}^2), \ p(\sigma_{1:d_g}^2) = \prod_{j \in g} \text{Ga}(\tau_j^2 | \delta_g, \rho^2 / 2)
$$
 (13.205)

The corresponding marginal for each group of weights has the form

$$
p(\mathbf{w}_g) \propto |u_g|^{\delta_g - d_g/2} \mathcal{K}_{\delta_g - d_g/2}(\rho u_g)
$$
\n(13.206)

where

$$
u_g \triangleq \sqrt{\sum_{j \in g} w_{g,j}^2} = ||\mathbf{w}_g||_2
$$
\n(13.207)

Now suppose  $\delta_g = (d_g + 1)/2$ , so  $\delta_g - d_g/2 = \frac{1}{2}$ . Conveniently, we have  $\mathcal{K}_{\frac{1}{2}}(z) = \sqrt{\frac{\pi}{2z}} \exp(-z)$ . Show that the resulting MAP estimate is equivalent to group lasso.

## **Exercise 13.11** Projected gradient descent for $\ell_1$ regularized least squares

Consider the BPDN problem  $\arg\min_{\theta} RSS(\theta) + \lambda ||\theta||_1$ . By using the split variable trick introducted in Section 7.4 (i.e., by defining  $\theta = [\theta_+, \theta_-]$ ), rewrite this as a quadratic program with a simple bound constraint. Then sketch how to use projected gradient descent to solve this problem. (If you get stuck, consult (Figueiredo et al. 2007).)

**Exercise 13.12** Subderivative of the hinge loss function

Let  $f(x) = (1-x)_+$  be the hinge loss function, where  $(z)_+ = \max(0, z)$ . What are  $\partial f(0), \partial f(1)$ , and  $\partial f(2)$ ?

**Exercise 13.13** Lower bounds to convex functions

Let f be a convex function. Explain how to find a global affine lower bound to f at an arbitrary point  $\mathbf{x} \in \text{dom}(f)$ .

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.