{"table_of_contents": [{"title": "13.5.1.2 Algorithms for group lasso", "heading_level": null, "page_id": 0, "polygon": [[84.75, 62.25], [246.0, 62.25], [246.0, 71.54736328125], [84.75, 71.54736328125]]}, {"title": "13.5.2 <PERSON><PERSON> lasso", "heading_level": null, "page_id": 1, "polygon": [[88.8046875, 264.0], [185.625, 264.0], [185.625, 275.115234375], [88.8046875, 275.115234375]]}, {"title": "13.5.2.1 GSM interpretation of fused lasso", "heading_level": null, "page_id": 2, "polygon": [[84.234375, 61.5], [274.5, 61.5], [274.5, 71.82421875], [84.234375, 71.82421875]]}, {"title": "13.5.2.2 Algorithms for fused lasso", "heading_level": null, "page_id": 2, "polygon": [[83.25, 327.0], [244.6875, 327.0], [244.6875, 336.97265625], [83.25, 336.97265625]]}, {"title": "13.5.3 Elastic net (ridge and lasso combined)", "heading_level": null, "page_id": 2, "polygon": [[87.890625, 408.0], [311.25, 408.0], [311.25, 418.2890625], [87.890625, 418.2890625]]}, {"title": "13.5.3.1 Vanilla version", "heading_level": null, "page_id": 3, "polygon": [[84.515625, 123.75], [194.90625, 123.75], [194.90625, 133.681640625], [84.515625, 133.681640625]]}, {"title": "13.5.3.2 Algorithms for vanilla elastic net", "heading_level": null, "page_id": 3, "polygon": [[83.25, 279.0], [273.0, 279.0], [273.0, 289.1953125], [83.25, 289.1953125]]}, {"title": "13.5.3.3 Improved version", "heading_level": null, "page_id": 3, "polygon": [[84.0, 502.5], [206.71875, 502.5], [206.71875, 512.578125], [84.0, 512.578125]]}, {"title": "13.5.3.4 GSM interpretation of elastic net", "heading_level": null, "page_id": 4, "polygon": [[84.0, 225.0], [271.5, 225.0], [271.5, 234.931640625], [84.0, 234.931640625]]}, {"title": "13.6 Non-convex regularizers", "heading_level": null, "page_id": 4, "polygon": [[94.5, 436.5], [255.75, 436.5], [255.75, 447.3984375], [94.5, 447.3984375]]}, {"title": "13.6.1 Bridge regression", "heading_level": null, "page_id": 5, "polygon": [[89.25, 61.5], [213.46875, 61.5], [213.46875, 71.54736328125], [89.25, 71.54736328125]]}, {"title": "13.6.2 Hierarchical adaptive lasso", "heading_level": null, "page_id": 5, "polygon": [[87.75, 381.75], [258.1875, 381.75], [258.1875, 392.34375], [87.75, 392.34375]]}, {"title": "13.6.2.1 EM for HAL", "heading_level": null, "page_id": 6, "polygon": [[83.8828125, 448.5], [182.25, 448.5], [182.25, 458.15625], [83.8828125, 458.15625]]}, {"title": "13.6.2.2 Understanding the behavior of HAL", "heading_level": null, "page_id": 7, "polygon": [[83.25, 519.75], [283.5, 519.75], [283.5, 529.98046875], [83.25, 529.98046875]]}, {"title": "13.6.3 Other hierarchical priors", "heading_level": null, "page_id": 9, "polygon": [[88.5, 523.5], [248.34375, 523.5], [248.34375, 533.4609375], [88.5, 533.4609375]]}, {"title": "13.7 Automatic relevance determination (ARD)/sparse Bayesian learning (SBL)", "heading_level": null, "page_id": 10, "polygon": [[95.25, 126.0], [504.75, 126.0], [504.75, 137.00390625], [95.25, 137.00390625]]}, {"title": "13.7.1 ARD for linear regression", "heading_level": null, "page_id": 10, "polygon": [[90.6328125, 431.25], [251.25, 431.25], [251.25, 441.38671875], [90.6328125, 441.38671875]]}, {"title": "13.7.2 Whence sparsity?", "heading_level": null, "page_id": 12, "polygon": [[88.734375, 299.25], [212.765625, 299.25], [212.765625, 309.4453125], [88.734375, 309.4453125]]}, {"title": "13.7.3 Connection to MAP estimation", "heading_level": null, "page_id": 12, "polygon": [[88.59375, 559.5], [274.5, 559.5], [274.5, 569.53125], [88.59375, 569.53125]]}, {"title": "13.7.4 Algorithms for ARD *", "heading_level": null, "page_id": 13, "polygon": [[88.3125, 276.75], [231.1875, 276.75], [231.1875, 286.822265625], [88.3125, 286.822265625]]}, {"title": "13.7.4.1 EM algorithm", "heading_level": null, "page_id": 13, "polygon": [[85.5, 321.0], [189.75, 321.0], [189.75, 330.01171875], [85.5, 330.01171875]]}, {"title": "13.7.4.2 Fixed-point algorithm", "heading_level": null, "page_id": 14, "polygon": [[83.25, 189.75], [225.0, 189.75], [225.0, 199.96875], [83.25, 199.96875]]}, {"title": "13.7.4.3 Iteratively reweighted \\ell_1 algorithm", "heading_level": null, "page_id": 14, "polygon": [[84.0, 442.5], [281.25, 442.5], [281.25, 452.25], [84.0, 452.25]]}, {"title": "13.7.5 ARD for logistic regression", "heading_level": null, "page_id": 15, "polygon": [[89.25, 301.5], [257.25, 301.5], [257.25, 311.66015625], [89.25, 311.66015625]]}, {"title": "13.8 Sparse coding *", "heading_level": null, "page_id": 15, "polygon": [[95.25, 456.0], [210.75, 456.0], [210.75, 467.015625], [95.25, 467.015625]]}, {"title": "13.8.1 Learning a sparse coding dictionary", "heading_level": null, "page_id": 16, "polygon": [[89.71875, 432.75], [301.5, 432.75], [301.5, 442.96875], [89.71875, 442.96875]]}, {"title": "13.8.2 Results of dictionary learning from image patches", "heading_level": null, "page_id": 17, "polygon": [[87.46875, 559.5], [366.75, 559.5], [366.75, 569.84765625], [87.46875, 569.84765625]]}, {"title": "13.8.3 Compressed sensing", "heading_level": null, "page_id": 19, "polygon": [[86.90625, 220.5], [227.25, 220.5], [227.25, 230.818359375], [86.90625, 230.818359375]]}, {"title": "13.8.4 Image inpainting and denoising", "heading_level": null, "page_id": 19, "polygon": [[88.8046875, 504.75], [280.5, 504.75], [280.5, 515.109375], [88.8046875, 515.109375]]}, {"title": "Exercises", "heading_level": null, "page_id": 21, "polygon": [[128.7421875, 114.75], [178.5, 114.75], [178.5, 124.5849609375], [128.7421875, 124.5849609375]]}, {"title": "Exercise 13.8 Deriving E step for GSM prior", "heading_level": null, "page_id": 23, "polygon": [[129.0, 255.0], [290.25, 255.0], [290.25, 265.939453125], [129.0, 265.939453125]]}, {"title": "Exercise 13.10 GSM representation of group lasso", "heading_level": null, "page_id": 23, "polygon": [[129.7265625, 507.75], [311.25, 507.75], [311.25, 517.95703125], [129.7265625, 517.95703125]]}, {"title": "Exercise 13.11 Projected gradient descent for \\ell_1 regularized least squares", "heading_level": null, "page_id": 24, "polygon": [[129.75, 348.75], [394.5, 348.75], [394.5, 357.85546875], [129.75, 357.85546875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 59], ["Equation", 8], ["TextInlineMath", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["Line", 148], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 893, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 52], ["Equation", 5], ["SectionHeader", 3], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 44], ["TextInlineMath", 7], ["Equation", 4], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 58], ["Text", 10], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1321, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 53], ["Equation", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 28], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 65], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 887, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 64], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 906, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 66], ["TableCell", 64], ["Text", 3], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3274, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 36], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 54], ["Equation", 10], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 35], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 650, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 467], ["Line", 70], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2991, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 44], ["TextInlineMath", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 49], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["TableCell", 56], ["Line", 52], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1279, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 56], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 34], ["Line", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 42], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 23], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1351, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["Line", 60], ["Text", 11], ["Equation", 9], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1111, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["Line", 51], ["Text", 9], ["Equation", 7], ["TextInlineMath", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 72], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 808, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 50], ["Text", 6], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-17"}