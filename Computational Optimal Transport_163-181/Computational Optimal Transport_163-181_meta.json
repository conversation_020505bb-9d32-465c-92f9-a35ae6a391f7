{"table_of_contents": [{"title": "10", "heading_level": null, "page_id": 0, "polygon": [[295.6205630354957, 135.2772216796875], [326.55419921875, 135.2772216796875], [326.55419921875, 164.2445068359375], [295.6205630354957, 164.2445068359375]]}, {"title": "Extensions of Optimal Transport", "heading_level": null, "page_id": 0, "polygon": [[198.8310893512852, 179.9431279620853], [423.17258261933904, 179.9431279620853], [423.17258261933904, 196.011962890625], [198.8310893512852, 196.011962890625]]}, {"title": "10.1 Multimarginal Problems", "heading_level": null, "page_id": 0, "polygon": [[104.910400390625, 472.3507109004739], [273.8616891064871, 472.3507109004739], [273.8616891064871, 483.17431640625], [104.910400390625, 483.17431640625]]}, {"title": "10.1. Multimarginal Problems 161", "heading_level": null, "page_id": 2, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 104.089111328125], [105.04283965728274, 104.089111328125]]}, {"title": "10.2 Unbalanced Optimal Transport", "heading_level": null, "page_id": 3, "polygon": [[105.04283965728274, 545.8274881516587], [311.3769889840881, 545.8274881516587], [311.3769889840881, 557.33056640625], [105.04283965728274, 557.33056640625]]}, {"title": "10.2. Unbalanced Optimal Transport 163", "heading_level": null, "page_id": 4, "polygon": [[104.6859130859375, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 103.7994384765625], [104.6859130859375, 103.7994384765625]]}, {"title": "10.3 Problems with Extra Constraints on the Couplings", "heading_level": null, "page_id": 6, "polygon": [[104.46142578125, 419.11753554502366], [418.6707466340269, 419.11753554502366], [418.6707466340269, 430.2607421875], [104.46142578125, 430.2607421875]]}, {"title": "10.4 Sliced Wasserstein Distance and Barycenters", "heading_level": null, "page_id": 7, "polygon": [[105.04283965728274, 326.89668246445495], [388.65850673194615, 326.89668246445495], [388.65850673194615, 338.144775390625], [105.04283965728274, 338.144775390625]]}, {"title": "10.4. <PERSON><PERSON><PERSON> and Barycenters 167", "heading_level": null, "page_id": 8, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.89599609375], [105.04283965728274, 103.89599609375]]}, {"title": "10.5 Transporting Vectors and Matrices", "heading_level": null, "page_id": 10, "polygon": [[104.9852294921875, 641.7971563981042], [333.4384765625, 641.7971563981042], [333.4384765625, 653.11572265625], [104.9852294921875, 653.11572265625]]}, {"title": "10.6 <PERSON><PERSON>ov–Wasserstein Distances", "heading_level": null, "page_id": 13, "polygon": [[105.04283965728274, 124.460663507109], [314.2822265625, 124.460663507109], [314.2822265625, 136.2427978515625], [105.04283965728274, 136.2427978515625]]}, {"title": "10.6.1 Hausdorff Distance", "heading_level": null, "page_id": 13, "polygon": [[105.04283965728274, 310.40189573459713], [244.59975520195837, 310.40189573459713], [244.59975520195837, 321.536865234375], [105.04283965728274, 321.536865234375]]}, {"title": "10.6.2 <PERSON><PERSON><PERSON><PERSON>f distance", "heading_level": null, "page_id": 14, "polygon": [[104.6859130859375, 253.41990521327014], [285.8665850673194, 253.41990521327014], [285.8665850673194, 263.4091796875], [104.6859130859375, 263.4091796875]]}, {"title": "10.6.3 <PERSON><PERSON><PERSON>–Wasserstein Distance", "heading_level": null, "page_id": 15, "polygon": [[104.29253365973072, 529.332701421801], [300.87270501835985, 529.332701421801], [300.87270501835985, 539.9501953125], [104.29253365973072, 539.9501953125]]}, {"title": "10.6. <PERSON><PERSON><PERSON><PERSON>Wasserstein Distances 175", "heading_level": null, "page_id": 16, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.702880859375], [105.04283965728274, 103.702880859375]]}, {"title": "10.6.4 Entropic Regularization", "heading_level": null, "page_id": 17, "polygon": [[104.3865966796875, 338.1431279620853], [265.6083231334149, 338.1431279620853], [265.6083231334149, 349.731689453125], [104.3865966796875, 349.731689453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 44], ["SectionHeader", 3], ["Text", 3], ["Equation", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4132, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 62], ["Text", 6], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 504], ["Line", 48], ["TextInlineMath", 5], ["Equation", 4], ["SectionHeader", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 48], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 501], ["Line", 69], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 41], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 36], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 62], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 77], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 35], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 713], ["Line", 67], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 766, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 50], ["TextInlineMath", 7], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 30], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 54], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 639, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 51], ["Equation", 2], ["Text", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 731, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 48], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1443, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_163-181"}