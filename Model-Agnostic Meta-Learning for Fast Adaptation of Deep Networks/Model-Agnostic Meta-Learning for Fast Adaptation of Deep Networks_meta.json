{"table_of_contents": [{"title": "Model-Agnostic Meta-Learning for Fast Adaptation of Deep Networks", "heading_level": null, "page_id": 0, "polygon": [[81.0, 89.25], [514.58203125, 89.25], [514.58203125, 103.7373046875], [81.0, 103.7373046875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 187.0751953125], [148.5, 187.0751953125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 474.75], [132.75, 474.75], [132.75, 486.4921875], [54.0, 486.4921875]]}, {"title": "2. Model-Agnostic Meta-Learning", "heading_level": null, "page_id": 1, "polygon": [[54.0, 282.0], [229.5, 282.0], [229.5, 293.1328125], [54.0, 293.1328125]]}, {"title": "2.1. Meta-Learning Problem Set-Up", "heading_level": null, "page_id": 1, "polygon": [[54.0, 362.25], [208.5, 362.25], [208.5, 373.763671875], [54.0, 373.763671875]]}, {"title": "2.2. A Model-Agnostic Meta-Learning Algorithm", "heading_level": null, "page_id": 1, "polygon": [[306.0, 425.25], [516.0, 425.25], [516.0, 435.4453125], [306.0, 435.4453125]]}, {"title": "3. Species of MAML", "heading_level": null, "page_id": 2, "polygon": [[306.0, 140.25], [412.5, 140.25], [412.5, 151.787109375], [306.0, 151.787109375]]}, {"title": "3.1. Supervised Regression and Classification", "heading_level": null, "page_id": 2, "polygon": [[305.25, 237.75], [499.5, 237.75], [499.5, 248.080078125], [305.25, 248.080078125]]}, {"title": "3.2. Reinforcement Learning", "heading_level": null, "page_id": 3, "polygon": [[54.0, 364.5], [177.75, 364.5], [177.75, 374.34375], [54.0, 374.34375]]}, {"title": "4. Related Work", "heading_level": null, "page_id": 3, "polygon": [[306.0, 477.59765625], [391.5, 477.59765625], [391.5, 488.42578125], [306.0, 488.42578125]]}, {"title": "5. Experimental Evaluation", "heading_level": null, "page_id": 4, "polygon": [[54.0, 634.5], [196.5, 634.5], [196.5, 645.8203125], [54.0, 645.8203125]]}, {"title": "5.1. <PERSON><PERSON>", "heading_level": null, "page_id": 4, "polygon": [[306.0, 219.75], [372.33984375, 219.75], [372.33984375, 229.904296875], [306.0, 229.904296875]]}, {"title": "5.2. Classification", "heading_level": null, "page_id": 5, "polygon": [[54.0, 556.5], [130.5, 556.5], [130.5, 566.9296875], [54.0, 566.9296875]]}, {"title": "5.3. Reinforcement Learning", "heading_level": null, "page_id": 6, "polygon": [[305.701171875, 569.25], [430.013671875, 569.25], [430.013671875, 578.91796875], [305.701171875, 578.91796875]]}, {"title": "6. Discussion and Future Work", "heading_level": null, "page_id": 7, "polygon": [[306.0, 425.25], [465.75, 425.25], [465.75, 436.21875], [306.0, 436.21875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[54.0, 68.25], [155.25, 68.25], [155.25, 79.80908203125], [54.0, 79.80908203125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 174.75], [111.75, 174.75], [111.75, 186.78515625], [54.0, 186.78515625]]}, {"title": "<PERSON>. Additional Experiment Details", "heading_level": null, "page_id": 10, "polygon": [[54.0, 68.25], [226.5, 68.25], [226.5, 79.470703125], [54.0, 79.470703125]]}, {"title": "A.1. Classification", "heading_level": null, "page_id": 10, "polygon": [[54.0, 125.25], [132.75, 125.25], [132.75, 135.6416015625], [54.0, 135.6416015625]]}, {"title": "A.2. Reinforcement Learning", "heading_level": null, "page_id": 10, "polygon": [[54.0, 360.0], [180.0, 360.0], [180.0, 370.669921875], [54.0, 370.669921875]]}, {"title": "<PERSON><PERSON> Additional Sinusoid Results", "heading_level": null, "page_id": 10, "polygon": [[54.0, 562.5], [211.5, 562.5], [211.5, 573.1171875], [54.0, 573.1171875]]}, {"title": "<PERSON><PERSON> Comparisons", "heading_level": null, "page_id": 10, "polygon": [[54.0, 657.75], [195.75, 657.75], [195.75, 669.0234375], [54.0, 669.0234375]]}, {"title": "C.1. Multi-task baselines", "heading_level": null, "page_id": 10, "polygon": [[306.75, 69.0], [413.578125, 69.0], [413.578125, 79.42236328125], [306.75, 79.42236328125]]}, {"title": "C.2. Context vector adaptation", "heading_level": null, "page_id": 10, "polygon": [[306.0, 591.75], [438.75, 591.75], [438.75, 601.34765625], [306.0, 601.34765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 89], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5247, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["Line", 107], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 693, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 762], ["Line", 147], ["TableCell", 25], ["Text", 11], ["Equation", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4104, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 952], ["Line", 152], ["TableCell", 61], ["TextInlineMath", 4], ["Table", 3], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 2813, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 103], ["Text", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 96], ["Text", 5], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1476, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["TableCell", 113], ["Line", 94], ["Text", 4], ["Caption", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["TableGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2956, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 118], ["Text", 6], ["Caption", 2], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 885, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 93], ["ListItem", 21], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 67], ["ListItem", 19], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 98], ["Text", 9], ["SectionHeader", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["TableCell", 124], ["Line", 56], ["Caption", 6], ["Table", 5], ["TableGroup", 3], ["Figure", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 1, "llm_tokens_used": 9212, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Line", 31], ["Span", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Model-Agnostic Meta-Learning for Fast Adaptation of Deep Networks"}