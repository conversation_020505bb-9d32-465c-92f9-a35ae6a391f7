{"table_of_contents": [{"title": "2.4. The Exponential Family 119", "heading_level": null, "page_id": 1, "polygon": [[294.0, 41.25], [474.0, 41.25], [474.0, 51.299560546875], [294.0, 51.299560546875]]}, {"title": "120 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 2, "polygon": [[30.0, 40.5], [251.25, 40.5], [251.25, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "2.5. Nonparametric Methods", "heading_level": null, "page_id": 2, "polygon": [[88.5, 249.0], [269.25, 249.0], [269.25, 262.107421875], [88.5, 262.107421875]]}, {"title": "2.5.1 Kernel density estimators", "heading_level": null, "page_id": 4, "polygon": [[137.25, 201.0], [315.75, 201.0], [315.75, 211.86474609375], [137.25, 211.86474609375]]}, {"title": "2.5. Nonparametric Methods 123", "heading_level": null, "page_id": 5, "polygon": [[291.75, 41.25], [474.0, 41.25], [474.0, 51.2589111328125], [291.75, 51.2589111328125]]}, {"title": "124 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 6, "polygon": [[30.0, 40.5], [251.25, 40.5], [251.25, 51.86865234375], [30.0, 51.86865234375]]}, {"title": "2.5.2 Nearest-neighbour methods", "heading_level": null, "page_id": 6, "polygon": [[137.25, 470.25], [329.25, 470.25], [329.25, 481.939453125], [137.25, 481.939453125]]}, {"title": "126 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 8, "polygon": [[30.0, 40.5], [251.25, 40.5], [251.25, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "Exercises", "heading_level": null, "page_id": 9, "polygon": [[31.5, 279.0], [93.0, 279.0], [93.0, 292.18798828125], [31.5, 292.18798828125]]}, {"title": "128 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 10, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.6654052734375], [30.0, 51.6654052734375]]}, {"title": "130 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 12, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.5841064453125], [30.0, 51.5841064453125]]}, {"title": "132 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 14, "polygon": [[30.0, 40.5], [251.25, 40.5], [251.25, 51.5841064453125], [30.0, 51.5841064453125]]}, {"title": "134 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 16, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.624755859375], [30.0, 51.624755859375]]}, {"title": "136 2. PROBABILITY DISTRIBUTIONS", "heading_level": null, "page_id": 18, "polygon": [[30.0, 40.5], [252.0, 40.5], [252.0, 51.299560546875], [30.0, 51.299560546875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 50], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 70], ["TextInlineMath", 6], ["Equation", 6], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 43], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 51], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 45], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 64], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 55], ["Text", 5], ["SectionHeader", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 54], ["Equation", 4], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1333, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 56], ["Text", 3], ["Figure", 2], ["SectionHeader", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1409, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 57], ["Equation", 6], ["Text", 5], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 57], ["Equation", 7], ["Text", 4], ["ListItem", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 54], ["Equation", 6], ["ListItem", 4], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 978, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 38], ["Equation", 8], ["Text", 8], ["ListItem", 4], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 40], ["ListItem", 8], ["Text", 3], ["Equation", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 51], ["Text", 8], ["Equation", 5], ["ListItem", 5], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1018, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 40], ["ListItem", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1], ["ListGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["Line", 51], ["ListItem", 7], ["Equation", 3], ["Text", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 42], ["ListItem", 8], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 41], ["ListItem", 10], ["Equation", 2], ["Text", 2], ["ListGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_138-156"}