{"table_of_contents": [{"title": "8\nModel Inference and Averaging", "heading_level": null, "page_id": 0, "polygon": [[132.0, 108.75], [393.75, 108.75], [393.75, 162.1318359375], [132.0, 162.1318359375]]}, {"title": "8.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[132.0, 354.0], [243.24609375, 354.0], [243.24609375, 367.76953125], [132.0, 367.76953125]]}, {"title": "8.2 The Bootstrap and Maximum Likelihood\nMethods", "heading_level": null, "page_id": 0, "polygon": [[132.0, 541.5], [419.853515625, 541.5], [419.853515625, 570.796875], [132.0, 570.796875]]}, {"title": "8.2.1 A Smoothing Example", "heading_level": null, "page_id": 0, "polygon": [[132.75, 584.25], [285.0, 584.25], [285.0, 596.70703125], [132.75, 596.70703125]]}, {"title": "8.2.2 Maximum Likelihood Inference", "heading_level": null, "page_id": 4, "polygon": [[132.75, 111.75], [329.90625, 111.75], [329.90625, 123.75], [132.75, 123.75]]}, {"title": "266 8. Model Inference and Averaging", "heading_level": null, "page_id": 5, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 99.193359375], [132.0, 99.193359375]]}, {"title": "8.2.3 Bootstrap versus Maximum Likelihood", "heading_level": null, "page_id": 6, "polygon": [[133.5, 366.99609375], [366.9609375, 366.99609375], [366.9609375, 377.4375], [133.5, 377.4375]]}, {"title": "8.3 Bayesian Methods", "heading_level": null, "page_id": 6, "polygon": [[132.0, 602.12109375], [277.5, 602.12109375], [277.5, 614.49609375], [132.0, 614.49609375]]}, {"title": "268 8. Model Inference and Averaging", "heading_level": null, "page_id": 7, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "8.4 Relationship Between the Bootstrap and\nBayesian Inference", "heading_level": null, "page_id": 10, "polygon": [[132.0, 111.0], [416.25, 111.0], [416.25, 140.8623046875], [132.0, 140.8623046875]]}, {"title": "272 8. Model Inference and Averaging", "heading_level": null, "page_id": 11, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 98.806640625], [132.0, 98.806640625]]}, {"title": "8.5 The EM Algorithm", "heading_level": null, "page_id": 11, "polygon": [[132.0, 468.0], [284.6337890625, 468.0], [284.6337890625, 481.8515625], [132.0, 481.8515625]]}, {"title": "8.5.1 Two-Component Mixture Model", "heading_level": null, "page_id": 11, "polygon": [[132.75, 549.0], [335.28515625, 549.0], [335.28515625, 559.96875], [132.75, 559.96875]]}, {"title": "274 8. Model Inference and Averaging", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [309.0, 89.25], [309.0, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "8.5.2 The EM Algorithm in General", "heading_level": null, "page_id": 15, "polygon": [[133.5, 292.5], [327.75, 292.5], [327.75, 304.34765625], [133.5, 304.34765625]]}, {"title": "8.5.3 EM as a Maximization–Maximization Procedure", "heading_level": null, "page_id": 16, "polygon": [[133.4267578125, 536.25], [417.0, 536.25], [417.0, 547.5], [133.4267578125, 547.5]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["Line", 22], ["Text", 4], ["SectionHeader", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3540, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 94], ["TextInlineMath", 5], ["Equation", 3], ["Figure", 2], ["Text", 2], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 208], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1185, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 61], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 52], ["Text", 7], ["Equation", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 937, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 77], ["Equation", 7], ["TextInlineMath", 5], ["Text", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 57], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 47], ["Text", 6], ["Equation", 4], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 79], ["Text", 3], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 8727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 122], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 988, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 40], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 37], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 57], ["TableCell", 12], ["Equation", 4], ["Text", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2503, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 52], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 63], ["TableCell", 26], ["Text", 6], ["TextInlineMath", 3], ["ListItem", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 70], ["Text", 3], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Picture", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1288, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 556], ["Line", 57], ["Text", 4], ["ListItem", 4], ["Equation", 4], ["TextInlineMath", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 39], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 823, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_280-297"}