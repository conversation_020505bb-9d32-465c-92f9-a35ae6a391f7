# Denoising Diffusion Probabilistic Models

<PERSON> Berkeley <EMAIL>

<PERSON><PERSON> <NAME_EMAIL>

<PERSON>el <NAME_EMAIL>

# Abstract

We present high quality image synthesis results using diffusion probabilistic models, a class of latent variable models inspired by considerations from nonequilibrium thermodynamics. Our best results are obtained by training on a weighted variational bound designed according to a novel connection between diffusion probabilistic models and denoising score matching with Langevin dynamics, and our models naturally admit a progressive lossy decompression scheme that can be interpreted as a generalization of autoregressive decoding. On the unconditional CIFAR10 dataset, we obtain an Inception score of 9.46 and a state-of-the-art FID score of 3.17. On 256x256 LSUN, we obtain sample quality similar to ProgressiveGAN. Our implementation is available at <https://github.com/hojonathanho/diffusion>.

# 1 Introduction

Deep generative models of all kinds have recently exhibited high quality samples in a wide variety of data modalities. Generative adversarial networks (GANs), autoregressive models, flows, and variational autoencoders (VAEs) have synthesized striking image and audio samples [\[14,](#page-9-0) [27,](#page-9-1) [3,](#page-8-0) [58,](#page-11-0) [38,](#page-10-0) [25,](#page-9-2) [10,](#page-9-3) [32,](#page-10-1) [44,](#page-10-2) [57,](#page-11-1) [26,](#page-9-4) [33,](#page-10-3) [45\]](#page-10-4), and there have been remarkable advances in energy-based modeling and score matching that have produced images comparable to those of GANs [\[11,](#page-9-5) [55\]](#page-11-2).

<span id="page-0-0"></span>Image /page/0/Picture/8 description: The image displays a grid of 100 smaller images, arranged in 10 rows and 10 columns. The smaller images are diverse, featuring various animals such as dogs, cats, horses, deer, and birds, as well as vehicles like cars, trucks, airplanes, and boats. Some images also show natural scenes and objects. To the left of this grid, there are four larger portraits of people. The top left portrait is of a woman with dark, curly hair smiling. The top right portrait is of a man with short hair, also smiling. The bottom left portrait is of a man with short black hair, smiling broadly. The bottom right portrait is of a woman with long, wavy brown hair, looking towards the viewer.

Figure 1: Generated samples on CelebA-HQ  $256 \times 256$  (left) and unconditional CIFAR10 (right)

34th Conference on Neural Information Processing Systems (NeurIPS 2020), Vancouver, Canada.

Image /page/1/Figure/0 description: This is a diagram illustrating a process involving variables xT, xt, xt-1, and x0. Arrows indicate a progression from xT through intermediate steps to x0. Specifically, there's an arrow from xt to xt-1 labeled "pθ(xt-1|xt)", and a dashed arrow from xt-1 back to xt labeled "q(xt|xt-1)". Below the progression, there are images: a noisy square below xT, a slightly less noisy square below xt, a square with a faint face below xt-1, and a clear image of a man's face below x0. This suggests a process of denoising or generation, starting from noise and ending with a clear image.

Figure 2: The directed graphical model considered in this work.

This paper presents progress in diffusion probabilistic models [\[53\]](#page-11-3). A diffusion probabilistic model (which we will call a "diffusion model" for brevity) is a parameterized Markov chain trained using variational inference to produce samples matching the data after finite time. Transitions of this chain are learned to reverse a diffusion process, which is a Markov chain that gradually adds noise to the data in the opposite direction of sampling until signal is destroyed. When the diffusion consists of small amounts of Gaussian noise, it is sufficient to set the sampling chain transitions to conditional Gaussians too, allowing for a particularly simple neural network parameterization.

Diffusion models are straightforward to define and efficient to train, but to the best of our knowledge, there has been no demonstration that they are capable of generating high quality samples. We show that diffusion models actually are capable of generating high quality samples, sometimes better than the published results on other types of generative models (Section [4\)](#page-4-0). In addition, we show that a certain parameterization of diffusion models reveals an equivalence with denoising score matching over multiple noise levels during training and with annealed Langevin dynamics during sampling (Section [3.2\)](#page-2-0) [\[55,](#page-11-2) [61\]](#page-11-4). We obtained our best sample quality results using this parameterization (Section [4.2\)](#page-5-0), so we consider this equivalence to be one of our primary contributions.

Despite their sample quality, our models do not have competitive log likelihoods compared to other likelihood-based models (our models do, however, have log likelihoods better than the large estimates annealed importance sampling has been reported to produce for energy based models and score matching [\[11,](#page-9-5) [55\]](#page-11-2)). We find that the majority of our models' lossless codelengths are consumed to describe imperceptible image details (Section [4.3\)](#page-5-1). We present a more refined analysis of this phenomenon in the language of lossy compression, and we show that the sampling procedure of diffusion models is a type of progressive decoding that resembles autoregressive decoding along a bit ordering that vastly generalizes what is normally possible with autoregressive models.

# 2 Background

Diffusion models [\[53\]](#page-11-3) are latent variable models of the form  $p_\theta(\mathbf{x}_0) := \int p_\theta(\mathbf{x}_{0:T}) d\mathbf{x}_{1:T}$ , where  $x_1, \ldots, x_T$  are latents of the same dimensionality as the data  $x_0 \sim q(x_0)$ . The joint distribution  $p_{\theta}(\mathbf{x}_{0:T})$  is called the *reverse process*, and it is defined as a Markov chain with learned Gaussian transitions starting at  $p(\mathbf{x}_T) = \mathcal{N}(\mathbf{x}_T; \mathbf{0}, \mathbf{I})$ :

$$
p_{\theta}(\mathbf{x}_{0:T}) \coloneqq p(\mathbf{x}_T) \prod_{t=1}^T p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t), \qquad p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t) \coloneqq \mathcal{N}(\mathbf{x}_{t-1}; \boldsymbol{\mu}_{\theta}(\mathbf{x}_t, t), \boldsymbol{\Sigma}_{\theta}(\mathbf{x}_t, t)) \qquad (1)
$$

What distinguishes diffusion models from other types of latent variable models is that the approximate posterior  $q(\mathbf{x}_{1:T} | \mathbf{x}_0)$ , called the *forward process* or *diffusion process*, is fixed to a Markov chain that gradually adds Gaussian noise to the data according to a variance schedule  $\beta_1, \ldots, \beta_T$ :

$$
q(\mathbf{x}_{1:T}|\mathbf{x}_0) := \prod_{t=1}^T q(\mathbf{x}_t|\mathbf{x}_{t-1}), \qquad q(\mathbf{x}_t|\mathbf{x}_{t-1}) := \mathcal{N}(\mathbf{x}_t; \sqrt{1-\beta_t}\mathbf{x}_{t-1}, \beta_t \mathbf{I})
$$
(2)

Training is performed by optimizing the usual variational bound on negative log likelihood:

$$
\mathbb{E}\left[-\log p_{\theta}(\mathbf{x}_{0})\right] \leq \mathbb{E}_{q}\left[-\log \frac{p_{\theta}(\mathbf{x}_{0:T})}{q(\mathbf{x}_{1:T}|\mathbf{x}_{0})}\right] = \mathbb{E}_{q}\left[-\log p(\mathbf{x}_{T}) - \sum_{t \geq 1} \log \frac{p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_{t})}{q(\mathbf{x}_{t}|\mathbf{x}_{t-1})}\right] =: L \quad (3)
$$

The forward process variances  $\beta_t$  can be learned by reparameterization [\[33\]](#page-10-3) or held constant as hyperparameters, and expressiveness of the reverse process is ensured in part by the choice of Gaussian conditionals in  $p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t)$ , because both processes have the same functional form when  $\beta_t$  are small [\[53\]](#page-11-3). A notable property of the forward process is that it admits sampling  $x_t$  at an arbitrary timestep t in closed form: using the notation  $\alpha_t := 1 - \beta_t$  and  $\bar{\alpha}_t := \prod_{s=1}^t \alpha_s$ , we have

<span id="page-1-2"></span><span id="page-1-1"></span><span id="page-1-0"></span>
$$
q(\mathbf{x}_t|\mathbf{x}_0) = \mathcal{N}(\mathbf{x}_t; \sqrt{\bar{\alpha}_t}\mathbf{x}_0, (1 - \bar{\alpha}_t)\mathbf{I})
$$
\n(4)

Efficient training is therefore possible by optimizing random terms of  $L$  with stochastic gradient descent. Further improvements come from variance reduction by rewriting  $L(3)$  $L(3)$  as:

$$
\mathbb{E}_{q}\left[\underbrace{D_{\text{KL}}(q(\mathbf{x}_{T}|\mathbf{x}_{0}) \parallel p(\mathbf{x}_{T}))}_{L_{T}} + \sum_{t>1} \underbrace{D_{\text{KL}}(q(\mathbf{x}_{t-1}|\mathbf{x}_{t}, \mathbf{x}_{0}) \parallel p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_{t}))}_{L_{t-1}} - \underbrace{\log p_{\theta}(\mathbf{x}_{0}|\mathbf{x}_{1})}_{L_{0}}\right] \tag{5}
$$

(See Appendix [A](#page-12-0) for details. The labels on the terms are used in Section [3.](#page-2-1)) Equation [\(5\)](#page-2-2) uses KL divergence to directly compare  $p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t)$  against forward process posteriors, which are tractable when conditioned on  $x_0$ :

<span id="page-2-4"></span><span id="page-2-2"></span>
$$
q(\mathbf{x}_{t-1}|\mathbf{x}_t,\mathbf{x}_0)=\mathcal{N}(\mathbf{x}_{t-1};\tilde{\boldsymbol{\mu}}_t(\mathbf{x}_t,\mathbf{x}_0),\tilde{\beta}_t\mathbf{I}),
$$
\n(6)

where 
$$
\tilde{\boldsymbol{\mu}}_t(\mathbf{x}_t, \mathbf{x}_0) \coloneqq \frac{\sqrt{\bar{\alpha}_{t-1}} \beta_t}{1 - \bar{\alpha}_t} \mathbf{x}_0 + \frac{\sqrt{\alpha_t}(1 - \bar{\alpha}_{t-1})}{1 - \bar{\alpha}_t} \mathbf{x}_t
$$
 and  $\tilde{\beta}_t \coloneqq \frac{1 - \bar{\alpha}_{t-1}}{1 - \bar{\alpha}_t} \beta_t$  (7)

Consequently, all KL divergences in Eq. [\(5\)](#page-2-2) are comparisons between Gaussians, so they can be calculated in a Rao-Blackwellized fashion with closed form expressions instead of high variance Monte Carlo estimates.

# <span id="page-2-1"></span>3 Diffusion models and denoising autoencoders

Diffusion models might appear to be a restricted class of latent variable models, but they allow a large number of degrees of freedom in implementation. One must choose the variances  $\beta_t$  of the forward process and the model architecture and Gaussian distribution parameterization of the reverse process. To guide our choices, we establish a new explicit connection between diffusion models and denoising score matching (Section [3.2\)](#page-2-0) that leads to a simplified, weighted variational bound objective for diffusion models (Section [3.4\)](#page-3-0). Ultimately, our model design is justified by simplicity and empirical results (Section [4\)](#page-4-0). Our discussion is categorized by the terms of Eq. [\(5\)](#page-2-2).

### 3.1 Forward process and $L_T$

We ignore the fact that the forward process variances  $\beta_t$  are learnable by reparameterization and instead fix them to constants (see Section [4](#page-4-0) for details). Thus, in our implementation, the approximate posterior q has no learnable parameters, so  $L_T$  is a constant during training and can be ignored.

#### <span id="page-2-0"></span>3.2 Reverse process and $L_{1:T-1}$

Now we discuss our choices in  $p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t) = \mathcal{N}(\mathbf{x}_{t-1}; \boldsymbol{\mu}_{\theta}(\mathbf{x}_t, t), \boldsymbol{\Sigma}_{\theta}(\mathbf{x}_t, t))$  for  $1 < t \leq T$ . First, we set  $\Sigma_{\theta}(\mathbf{x}_t, t) = \sigma_t^2 \mathbf{I}$  to untrained time dependent constants. Experimentally, both  $\sigma_t^2 = \beta_t$  and  $\sigma_t^2 = \tilde{\beta}_t = \frac{1-\bar{\alpha}_{t-1}}{1-\bar{\alpha}_t}$  $\frac{-\alpha_{t-1}}{1-\alpha_t}$  had similar results. The first choice is optimal for  $\mathbf{x}_0 \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$ , and the second is optimal for  $x_0$  deterministically set to one point. These are the two extreme choices corresponding to upper and lower bounds on reverse process entropy for data with coordinatewise unit variance [\[53\]](#page-11-3).

Second, to represent the mean  $\mu_{\theta}(\mathbf{x}_t, t)$ , we propose a specific parameterization motivated by the following analysis of  $L_t$ . With  $p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t) = \mathcal{N}(\mathbf{x}_{t-1}; \mu_\theta(\mathbf{x}_t, t), \sigma_t^2 \mathbf{I})$ , we can write:

<span id="page-2-5"></span><span id="page-2-3"></span>
$$
L_{t-1} = \mathbb{E}_q \left[ \frac{1}{2\sigma_t^2} ||\tilde{\boldsymbol{\mu}}_t(\mathbf{x}_t, \mathbf{x}_0) - \boldsymbol{\mu}_\theta(\mathbf{x}_t, t)||^2 \right] + C
$$
 (8)

where C is a constant that does not depend on  $\theta$ . So, we see that the most straightforward parameterization of  $\mu_{\theta}$  is a model that predicts  $\tilde{\mu}_t$ , the forward process posterior mean. However, we can expand Eq. [\(8\)](#page-2-3) further by reparameterizing Eq. [\(4\)](#page-1-1) as  $x_t(x_0, \epsilon) = \sqrt{\overline{\alpha}_t}x_0 + \sqrt{1 - \overline{\alpha}_t}\epsilon$  for  $\epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$  and applying the forward process posterior formula [\(7\)](#page-2-4):

$$
L_{t-1} - C = \mathbb{E}_{\mathbf{x}_0, \epsilon} \left[ \frac{1}{2\sigma_t^2} \left\| \tilde{\boldsymbol{\mu}}_t \left( \mathbf{x}_t(\mathbf{x}_0, \epsilon), \frac{1}{\sqrt{\bar{\alpha}_t}} (\mathbf{x}_t(\mathbf{x}_0, \epsilon) - \sqrt{1 - \bar{\alpha}_t} \epsilon) \right) - \boldsymbol{\mu}_\theta(\mathbf{x}_t(\mathbf{x}_0, \epsilon), t) \right\|^2 \right]
$$
(9)

$$
= \mathbb{E}_{\mathbf{x}_0,\boldsymbol{\epsilon}} \left[ \frac{1}{2\sigma_t^2} \left\| \frac{1}{\sqrt{\alpha_t}} \left( \mathbf{x}_t(\mathbf{x}_0,\boldsymbol{\epsilon}) - \frac{\beta_t}{\sqrt{1-\bar{\alpha}_t}} \boldsymbol{\epsilon} \right) - \boldsymbol{\mu}_\theta(\mathbf{x}_t(\mathbf{x}_0,\boldsymbol{\epsilon}),t) \right\|^2 \right]
$$
(10)

<span id="page-3-1"></span>

| <b>Algorithm 1 Training</b>                                                                                                                                                                                                                                                                                                                                             | <b>Algorithm 2 Sampling</b>                                                                                                                                                                                                                                                                                                                                                                                                                   |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1: repeat<br>2: $\mathbf{x}_0 acksim q(\mathbf{x}_0)$<br>3: $t acksim \text{Uniform}(\{1,\ldots,T\})$<br>4: $\epsilon acksim \mathcal{N}(\mathbf{0}, \mathbf{I})$<br>5: Take gradient descent step on<br>$\nabla_{\theta}   \epsilon - \epsilon_{\theta}(\sqrt{\bar{\alpha}_t} \mathbf{x}_0 + \sqrt{1-\bar{\alpha}_t} \epsilon, t)  ^2$<br>6: <b>until</b> converged | 1: $\mathbf{x}_T acksim \mathcal{N}(\mathbf{0}, \mathbf{I})$<br>2: for $t = T, \ldots, 1$ do<br>3: $\mathbf{z} acksim \mathcal{N}(\mathbf{0}, \mathbf{I})$ if $t > 1$ , else $\mathbf{z} = \mathbf{0}$<br>4: $\mathbf{x}_{t-1} = \frac{1}{\sqrt{\alpha_t}} \left( \mathbf{x}_t - \frac{1-\alpha_t}{\sqrt{1-\bar{\alpha}_t}} \boldsymbol{\epsilon}_{\theta}(\mathbf{x}_t, t) \right) + \sigma_t \mathbf{z}$<br>5: end for<br>6: return $x_0$ |

Equation [\(10\)](#page-2-5) reveals that  $\mu_{\theta}$  must predict  $\frac{1}{\sqrt{\alpha_t}}\left(\mathbf{x}_t - \frac{\beta_t}{\sqrt{1-\theta_t}}\right)$  $\left(\frac{\beta_t}{1-\bar{\alpha}_t}\epsilon\right)$  given  $\mathbf{x}_t$ . Since  $\mathbf{x}_t$  is available as input to the model, we may choose the parameterization

$$
\mu_{\theta}(\mathbf{x}_t, t) = \tilde{\mu}_t\left(\mathbf{x}_t, \frac{1}{\sqrt{\bar{\alpha}_t}}(\mathbf{x}_t - \sqrt{1 - \bar{\alpha}_t}\boldsymbol{\epsilon}_{\theta}(\mathbf{x}_t))\right) = \frac{1}{\sqrt{\alpha_t}}\left(\mathbf{x}_t - \frac{\beta_t}{\sqrt{1 - \bar{\alpha}_t}}\boldsymbol{\epsilon}_{\theta}(\mathbf{x}_t, t)\right)
$$
(11)

where  $\epsilon_{\theta}$  is a function approximator intended to predict  $\epsilon$  from  $\mathbf{x}_t$ . To sample  $\mathbf{x}_{t-1} \sim p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t)$  is to compute  $\mathbf{x}_{t-1} = \frac{1}{\sqrt{\alpha_t}} \left( \mathbf{x}_t - \frac{\beta_t}{\sqrt{1 - \mathbf{x}_t}} \right)$  $\frac{\beta_t}{1-\bar{\alpha}_t} \epsilon_\theta(\mathbf{x}_t, t)$  +  $\sigma_t \mathbf{z}$ , where  $\mathbf{z} \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$ . The complete sampling procedure, Algorithm [2,](#page-3-1) resembles Langevin dynamics with  $\epsilon_{\theta}$  as a learned gradient of the data density. Furthermore, with the parameterization [\(11\)](#page-3-2), Eq. [\(10\)](#page-2-5) simplifies to:

<span id="page-3-3"></span><span id="page-3-2"></span>
$$
\mathbb{E}_{\mathbf{x}_0,\epsilon} \left[ \frac{\beta_t^2}{2\sigma_t^2 \alpha_t (1 - \bar{\alpha}_t)} \left\| \boldsymbol{\epsilon} - \boldsymbol{\epsilon}_{\theta} (\sqrt{\bar{\alpha}_t} \mathbf{x}_0 + \sqrt{1 - \bar{\alpha}_t} \boldsymbol{\epsilon}, t) \right\|^2 \right]
$$
(12)

which resembles denoising score matching over multiple noise scales indexed by  $t$  [\[55\]](#page-11-2). As Eq. [\(12\)](#page-3-3) is equal to (one term of) the variational bound for the Langevin-like reverse process [\(11\)](#page-3-2), we see that optimizing an objective resembling denoising score matching is equivalent to using variational inference to fit the finite-time marginal of a sampling chain resembling Langevin dynamics.

To summarize, we can train the reverse process mean function approximator  $\mu_{\theta}$  to predict  $\tilde{\mu}_t$ , or by modifying its parameterization, we can train it to predict  $\epsilon$ . (There is also the possibility of predicting  $x<sub>0</sub>$ , but we found this to lead to worse sample quality early in our experiments.) We have shown that the  $\epsilon$ -prediction parameterization both resembles Langevin dynamics and simplifies the diffusion model's variational bound to an objective that resembles denoising score matching. Nonetheless, it is just another parameterization of  $p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t)$ , so we verify its effectiveness in Section [4](#page-4-0) in an ablation where we compare predicting  $\epsilon$  against predicting  $\tilde{\mu}_t$ .

### 3.3 Data scaling, reverse process decoder, and $L_0$

We assume that image data consists of integers in  $\{0, 1, \ldots, 255\}$  scaled linearly to  $[-1, 1]$ . This ensures that the neural network reverse process operates on consistently scaled inputs starting from the standard normal prior  $p(\mathbf{x}_T)$ . To obtain discrete log likelihoods, we set the last term of the reverse process to an independent discrete decoder derived from the Gaussian  $\mathcal{N}(\mathbf{x}_0; \boldsymbol{\mu}_{\theta}(\mathbf{x}_1, 1), \sigma_1^2 \mathbf{I})$ :

<span id="page-3-4"></span>
$$
p_{\theta}(\mathbf{x}_{0}|\mathbf{x}_{1}) = \prod_{i=1}^{D} \int_{\delta_{-}(x_{0}^{i})}^{\delta_{+}(x_{0}^{i})} \mathcal{N}(x; \mu_{\theta}^{i}(\mathbf{x}_{1}, 1), \sigma_{1}^{2}) dx
$$
  
\n
$$
\delta_{+}(x) = \begin{cases} \infty & \text{if } x = 1 \ x + \frac{1}{255} & \text{if } x < 1 \end{cases} \qquad \delta_{-}(x) = \begin{cases} - \infty & \text{if } x = -1 \ x - \frac{1}{255} & \text{if } x > -1 \end{cases}
$$
\n(13)

where  $D$  is the data dimensionality and the  $i$  superscript indicates extraction of one coordinate. (It would be straightforward to instead incorporate a more powerful decoder like a conditional autoregressive model, but we leave that to future work.) Similar to the discretized continuous distributions used in VAE decoders and autoregressive models [\[34,](#page-10-5) [52\]](#page-11-5), our choice here ensures that the variational bound is a lossless codelength of discrete data, without need of adding noise to the data or incorporating the Jacobian of the scaling operation into the log likelihood. At the end of sampling, we display  $\boldsymbol{\mu}_{\theta}(\mathbf{x}_1, 1)$  noiselessly.

#### <span id="page-3-0"></span>3.4 Simplified training objective

With the reverse process and decoder defined above, the variational bound, consisting of terms derived from Eqs. [\(12\)](#page-3-3) and [\(13\)](#page-3-4), is clearly differentiable with respect to  $\theta$  and is ready to be employed for

<span id="page-4-2"></span>

| Table 1: CIFAR10 results. NLL measured in bits/dim. |  |
|-----------------------------------------------------|--|
|-----------------------------------------------------|--|

| Model                                                                                  | IS                                                                       | <b>FID</b>                     | NLL Test (Train)                   | -Table 2: Unconditional CIFAR10 reverse                                                                                                                     |                                        |               |
|----------------------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------|------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|---------------|
| <b>Conditional</b>                                                                     |                                                                          |                                |                                    | process parameterization and training objec-                                                                                                                |                                        |               |
| <b>EBM</b> [11]<br><b>JEM [17]</b>                                                     | 8.30<br>8.76                                                             | 37.9<br>38.4                   |                                    | tive ablation. Blank entries were unstable to                                                                                                               |                                        |               |
| BigGAN <sup>[3]</sup>                                                                  | 9.22                                                                     | 14.73                          |                                    | train and generated poor samples with out-of-<br>range scores.                                                                                              |                                        |               |
| $StyleGAN2 + ADA (v1) [29]$                                                            | 10.06                                                                    | 2.67                           |                                    |                                                                                                                                                             |                                        |               |
| <b>Unconditional</b>                                                                   |                                                                          |                                |                                    | Objective                                                                                                                                                   | <b>IS</b>                              | <b>FID</b>    |
| Diffusion (original) [53]                                                              |                                                                          |                                | < 5.40                             | $\tilde{\mu}$ prediction (baseline)                                                                                                                         |                                        |               |
| Gated PixelCNN [59]                                                                    | 4.60                                                                     | 65.93                          | 3.03(2.90)                         | L, learned diagonal $\Sigma$                                                                                                                                | $7.28 \pm 0.10$                        | 23.69         |
| Sparse Transformer [7]                                                                 |                                                                          |                                | 2.80                               | L, fixed isotropic $\Sigma$                                                                                                                                 | $8.06 \pm 0.09$                        | 13.22         |
| PixelION [43]                                                                          | 5.29                                                                     | 49.46                          |                                    | $\ \tilde{\boldsymbol{\mu}}-\tilde{\boldsymbol{\mu}}_{\theta}\ ^2$                                                                                          |                                        |               |
| <b>EBM</b> [11]<br><b>NCSNv2</b> [56]                                                  | 6.78                                                                     | 38.2<br>31.75                  |                                    | $\epsilon$ prediction (ours)                                                                                                                                |                                        |               |
| <b>NCSN</b> [55]<br><b>SNGAN [39]</b><br>SNGAN-DDLS [4]<br>$StyleGAN2 + ADA (v1) [29]$ | $8.87 \pm 0.12$<br>$8.22 \pm 0.05$<br>$9.09 \pm 0.10$<br>$9.74 \pm 0.05$ | 25.32<br>21.7<br>15.42<br>3.26 |                                    | L, learned diagonal $\Sigma$<br>L, fixed isotropic $\Sigma$<br>$\ \tilde{\boldsymbol{\epsilon}}-\boldsymbol{\epsilon}_{\theta}\ ^2$ ( $L_{\text{simple}}$ ) | $7.67 \pm 0.13$<br>$9.46 \!\pm\! 0.11$ | 13.51<br>3.17 |
| Ours (L, fixed isotropic $\Sigma$ )<br>Ours $(L_{\text{simple}})$                      | $7.67 \pm 0.13$<br>$9.46 \pm 0.11$                                       | 13.51<br>3.17                  | $<$ 3.70 (3.69)<br>$<$ 3.75 (3.72) |                                                                                                                                                             |                                        |               |

training. However, we found it beneficial to sample quality (and simpler to implement) to train on the following variant of the variational bound:

<span id="page-4-1"></span>
$$
L_{\text{simple}}(\theta) \coloneqq \mathbb{E}_{t, \mathbf{x}_0, \epsilon} \left[ \left\| \boldsymbol{\epsilon} - \boldsymbol{\epsilon}_{\theta} (\sqrt{\bar{\alpha}_t} \mathbf{x}_0 + \sqrt{1 - \bar{\alpha}_t} \boldsymbol{\epsilon}, t) \right\|^2 \right] \tag{14}
$$

where t is uniform between 1 and T. The  $t = 1$  case corresponds to  $L_0$  with the integral in the discrete decoder definition [\(13\)](#page-3-4) approximated by the Gaussian probability density function times the bin width, ignoring  $\sigma_1^2$  and edge effects. The  $t > 1$  cases correspond to an unweighted version of Eq. [\(12\)](#page-3-3), analogous to the loss weighting used by the NCSN denoising score matching model [\[55\]](#page-11-2). ( $L_T$  does not appear because the forward process variances  $\beta_t$  are fixed.) Algorithm [1](#page-3-1) displays the complete training procedure with this simplified objective.

Since our simplified objective [\(14\)](#page-4-1) discards the weighting in Eq. [\(12\)](#page-3-3), it is a weighted variational bound that emphasizes different aspects of reconstruction compared to the standard variational bound [\[18,](#page-9-7) [22\]](#page-9-8). In particular, our diffusion process setup in Section [4](#page-4-0) causes the simplified objective to down-weight loss terms corresponding to small  $t$ . These terms train the network to denoise data with very small amounts of noise, so it is beneficial to down-weight them so that the network can focus on more difficult denoising tasks at larger  $t$  terms. We will see in our experiments that this reweighting leads to better sample quality.

# <span id="page-4-0"></span>4 Experiments

We set  $T = 1000$  for all experiments so that the number of neural network evaluations needed during sampling matches previous work [\[53,](#page-11-3) [55\]](#page-11-2). We set the forward process variances to constants increasing linearly from  $\beta_1 = 10^{-4}$  to  $\beta_T = 0.02$ . These constants were chosen to be small relative to data scaled to  $[-1, 1]$ , ensuring that reverse and forward processes have approximately the same functional form while keeping the signal-to-noise ratio at  $x_T$  as small as possible ( $L_T$  =  $D_{\text{KL}}(q(\mathbf{x}_T | \mathbf{x}_0) \mid \mathcal{N}(\mathbf{0}, \mathbf{I})) \approx 10^{-5}$  bits per dimension in our experiments).

To represent the reverse process, we use a U-Net backbone similar to an unmasked PixelCNN++ [\[52,](#page-11-5) [48\]](#page-10-9) with group normalization throughout [\[66\]](#page-11-8). Parameters are shared across time, which is specified to the network using the Transformer sinusoidal position embedding [\[60\]](#page-11-9). We use self-attention at the  $16 \times 16$  feature map resolution [\[63,](#page-11-10) [60\]](#page-11-9). Details are in Appendix [B.](#page-13-0)

### 4.1 Sample quality

Table [1](#page-4-2) shows Inception scores, FID scores, and negative log likelihoods (lossless codelengths) on CIFAR10. With our FID score of 3.17, our unconditional model achieves better sample quality than most models in the literature, including class conditional models. Our FID score is computed with respect to the training set, as is standard practice; when we compute it with respect to the test set, the score is 5.24, which is still better than many of the training set FID scores in the literature.

<span id="page-5-2"></span>Image /page/5/Picture/0 description: This is a collage of images. The left side of the collage features several architectural images of churches and cathedrals. The top left image shows a white church with golden domes. The image next to it shows a city view with a tall clock tower and stone buildings. Below these, there is an image of a stone church or castle on a rocky, green hillside. The image to the right of that shows a large, gothic-style stone church with a tall spire. The right side of the collage shows two images of bedrooms. The top right image shows a bedroom with a large wooden bed, white linens, and a red accent pillow. The bottom right image shows another bedroom with a large bed, white linens, and a purple accent wall.

Image /page/5/Picture/2 description: A collage of 20 different bedrooms, each with a bed, nightstand, and various decor. The bedrooms vary in style, color, and size, with some featuring large windows, balconies, or unique lighting fixtures. The overall impression is a collection of diverse and comfortable sleeping spaces.

Figure 3: LSUN Church samples. FID=7.89 Figure 4: LSUN Bedroom samples. FID=4.90

<span id="page-5-3"></span>

| <b>Algorithm 3</b> Sending $x_0$                                                                                               | <b>Algorithm 4 Receiving</b>                                              |
|--------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|
| 1: Send $\mathbf{x}_T \sim q(\mathbf{x}_T   \mathbf{x}_0)$ using $p(\mathbf{x}_T)$                                             | 1: Receive $\mathbf{x}_T$ using $p(\mathbf{x}_T)$                         |
| 2: for $t = T - 1, \dots, 2, 1$ do                                                                                             | 2: for $t = T - 1, \dots, 1, 0$ do                                        |
| 3: Send $\mathbf{x}_t \sim q(\mathbf{x}_t   \mathbf{x}_{t+1}, \mathbf{x}_0)$ using $p_\theta(\mathbf{x}_t   \mathbf{x}_{t+1})$ | 3: Receive $\mathbf{x}_t$ using $p_\theta(\mathbf{x}_t \mathbf{x}_{t+1})$ |
| 4: end for                                                                                                                     | 4: end for                                                                |
| 5: Send $\mathbf{x}_0$ using $p_\theta(\mathbf{x}_0 \mathbf{x}_1)$                                                             | 5: return $x_0$                                                           |

We find that training our models on the true variational bound yields better codelengths than training on the simplified objective, as expected, but the latter yields the best sample quality. See Fig. [1](#page-0-0) for CIFAR10 and CelebA-HQ 256  $\times$  256 samples, Fig. [3](#page-5-2) and Fig. [4](#page-5-2) for LSUN 256  $\times$  256 samples [\[71\]](#page-11-11), and Appendix [D](#page-14-0) for more.

# <span id="page-5-0"></span>4.2 Reverse process parameterization and training objective ablation

In Table [2,](#page-4-2) we show the sample quality effects of reverse process parameterizations and training objectives (Section [3.2\)](#page-2-0). We find that the baseline option of predicting  $\tilde{\mu}$  works well only when trained on the true variational bound instead of unweighted mean squared error, a simplified objective akin to Eq. [\(14\)](#page-4-1). We also see that learning reverse process variances (by incorporating a parameterized diagonal  $\Sigma_{\theta}(\mathbf{x}_t)$  into the variational bound) leads to unstable training and poorer sample quality compared to fixed variances. Predicting  $\epsilon$ , as we proposed, performs approximately as well as predicting  $\tilde{\mu}$  when trained on the variational bound with fixed variances, but much better when trained with our simplified objective.

# <span id="page-5-1"></span>4.3 Progressive coding

Table [1](#page-4-2) also shows the codelengths of our CIFAR10 models. The gap between train and test is at most 0.03 bits per dimension, which is comparable to the gaps reported with other likelihood-based models and indicates that our diffusion model is not overfitting (see Appendix [D](#page-14-0) for nearest neighbor visualizations). Still, while our lossless codelengths are better than the large estimates reported for energy based models and score matching using annealed importance sampling [\[11\]](#page-9-5), they are not competitive with other types of likelihood-based generative models [\[7\]](#page-8-1).

Since our samples are nonetheless of high quality, we conclude that diffusion models have an inductive bias that makes them excellent lossy compressors. Treating the variational bound terms  $L_1 + \cdots + L_T$ as rate and  $L_0$  as distortion, our CIFAR10 model with the highest quality samples has a rate of 1.78 bits/dim and a distortion of 1.97 bits/dim, which amounts to a root mean squared error of 0.95 on a scale from 0 to 255. More than half of the lossless codelength describes imperceptible distortions.

**Progressive lossy compression** We can probe further into the rate-distortion behavior of our model by introducing a progressive lossy code that mirrors the form of Eq. [\(5\)](#page-2-2): see Algorithms [3](#page-5-3) and [4,](#page-5-3) which assume access to a procedure, such as minimal random coding [\[19,](#page-9-9) [20\]](#page-9-10), that can transmit a sample  $\mathbf{x} \sim q(\mathbf{x})$  using approximately  $D_{\text{KL}}(q(\mathbf{x}) || p(\mathbf{x}))$  bits on average for any distributions p and q, for which only p is available to the receiver beforehand. When applied to  $\mathbf{x}_0 \sim q(\mathbf{x}_0)$ , Algorithms [3](#page-5-3) and [4](#page-5-3) transmit  $x_T, \ldots, x_0$  in sequence using a total expected codelength equal to Eq. [\(5\)](#page-2-2). The receiver, at any time t, has the partial information  $x_t$  fully available and can progressively estimate:

$$
\mathbf{x}_0 \approx \hat{\mathbf{x}}_0 = \left(\mathbf{x}_t - \sqrt{1 - \bar{\alpha}_t} \boldsymbol{\epsilon}_{\theta}(\mathbf{x}_t)\right) / \sqrt{\bar{\alpha}_t}
$$
(15)

due to Eq. [\(4\)](#page-1-1). (A stochastic reconstruction  $x_0 \sim p_\theta(x_0|x_t)$  is also valid, but we do not consider it here because it makes distortion more difficult to evaluate.) Figure [5](#page-6-0) shows the resulting ratedistortion plot on the CIFAR10 test set. At each time  $t$ , the distortion is calculated as the root mean squared error  $\sqrt{\|\mathbf{x}_0 - \hat{\mathbf{x}}_0\|^2/D}$ , and the rate is calculated as the cumulative number of bits received so far at time  $\dot{t}$ . The distortion decreases steeply in the low-rate region of the rate-distortion plot, indicating that the majority of the bits are indeed allocated to imperceptible distortions.

<span id="page-6-0"></span>Image /page/6/Figure/3 description: The image displays three scatter plots. The first plot shows Distortion (RMSE) on the y-axis and Reverse process steps (T-t) on the x-axis, with values decreasing from approximately 85 at 0 steps to 0 at 1000 steps. The second plot shows Rate (bits/dim) on the y-axis and Reverse process steps (T-t) on the x-axis, with values staying at 0 until around 800 steps, then rapidly increasing to approximately 1.7 at 1000 steps. The third plot shows Distortion (RMSE) on the y-axis and Rate (bits/dim) on the x-axis, illustrating a steep decrease in distortion as the rate increases from 0 to about 0.5, after which the distortion gradually decreases to approximately 5 at a rate of 1.5.

Figure 5: Unconditional CIFAR10 test set rate-distortion vs. time. Distortion is measured in root mean squared error on a [0, 255] scale. See Table [4](#page-12-1) for details.

Progressive generation We also run a progressive unconditional generation process given by progressive decompression from random bits. In other words, we predict the result of the reverse process,  $\hat{\mathbf{x}}_0$ , while sampling from the reverse process using Algorithm [2.](#page-3-1) Figures [6](#page-6-1) and [10](#page-15-0) show the resulting sample quality of  $\hat{x}_0$  over the course of the reverse process. Large scale image features appear first and details appear last. Figure [7](#page-6-2) shows stochastic predictions  $\mathbf{x}_0 \sim p_\theta(\mathbf{x}_0|\mathbf{x}_t)$  with  $\mathbf{x}_t$ frozen for various  $t$ . When  $t$  is small, all but fine details are preserved, and when  $t$  is large, only large scale features are preserved. Perhaps these are hints of conceptual compression [\[18\]](#page-9-7).

<span id="page-6-1"></span>Image /page/6/Figure/6 description: The image displays a grid of 4 rows and 10 columns, with each cell containing a progressively clearer image. The first column in each row shows noisy, abstract patterns. As you move across each row, the images transition through blurry shapes to recognizable objects. The first row shows blurry landscapes and then clearer images of airplanes. The second row shows blurry shapes that resolve into ostriches. The third row shows blurry shapes that become clearer images of airplanes. The fourth row shows blurry shapes that develop into images of lizards or geckos.

Figure 6: Unconditional CIFAR10 progressive generation  $(\hat{x}_0$  over time, from left to right). Extended samples and sample quality metrics over time in the appendix (Figs. [10](#page-15-0) and [14\)](#page-19-0).

<span id="page-6-2"></span>Image /page/6/Picture/8 description: The image displays a grid of portraits, likely generated by a machine learning model. The top row shows two women, one with a serious expression and the other smiling. The bottom row shows two women, one with a serious expression and the other with a neutral expression. Following these initial portraits, the grid transitions to a series of portraits of a man wearing sunglasses. These portraits are presented in columns labeled with decreasing values: "Share x1000", "Share x750", "Share x500", "Share x250", and "Share x0". Each column contains two images of the man. The images in the "Share x1000" and "Share x750" columns show the man with some noise or distortion. As the "Share" value decreases, the images become progressively more distorted and noisy, with the "Share x0" column showing the most degraded or abstract representation. The bottom row in the later columns also shows noisy or distorted images.

Figure 7: When conditioned on the same latent, CelebA-HQ  $256 \times 256$  samples share high-level attributes. Bottom-right quadrants are  $x_t$ , and other quadrants are samples from  $p_\theta(\mathbf{x}_0|\mathbf{x}_t)$ .

**Connection to autoregressive decoding** Note that the variational bound  $(5)$  can be rewritten as:

$$
L = D_{\mathrm{KL}}(q(\mathbf{x}_{T}) \parallel p(\mathbf{x}_{T})) + \mathbb{E}_{q} \left[ \sum_{t \geq 1} D_{\mathrm{KL}}(q(\mathbf{x}_{t-1}|\mathbf{x}_{t}) \parallel p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_{t})) \right] + H(\mathbf{x}_{0}) \tag{16}
$$

(See [A](#page-12-0)ppendix A for a derivation.) Now consider setting the diffusion process length  $T$  to the dimensionality of the data, defining the forward process so that  $q(\mathbf{x}_t|\mathbf{x}_0)$  places all probability mass on  $\mathbf{x}_0$  with the first t coordinates masked out (i.e.  $q(\mathbf{x}_t|\mathbf{x}_{t-1})$  masks out the t<sup>th</sup> coordinate), setting  $p(\mathbf{x}_T)$  to place all mass on a blank image, and, for the sake of argument, taking  $p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)$  to

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays a diagram illustrating a diffusion process for image generation on the left, and a series of generated faces on the right. The diagram shows a curved line representing an image manifold, with points labeled 'Source x0' and 'Source x0''. Arrows indicate a 'Diffused source' and 'Denoised interpolation' process. The right side of the image presents a grid of faces, categorized by 'Source', 'Rec.' (reconstruction), and various lambda values (λ=0.1 to λ=0.9). Each row shows a different individual, with the first column displaying the original source image, followed by reconstructions and interpolations at different lambda values, and ending with a final reconstruction and source image. The individuals featured appear to be a woman with dark hair, a woman with brown hair, and a man resembling Michael Jackson.

Figure 8: Interpolations of CelebA-HQ 256x256 images with 500 timesteps of diffusion.

be a fully expressive conditional distribution. With these choices,  $D_{KL}(q(\mathbf{x}_T) || p(\mathbf{x}_T)) = 0$ , and minimizing  $D_{\text{KL}}(q(\mathbf{x}_{t-1}|\mathbf{x}_t) \mid p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t))$  trains  $p_{\theta}$  to copy coordinates  $t + 1, \dots, T$  unchanged and to predict the  $t^{\text{th}}$  coordinate given  $t + 1, \ldots, T$ . Thus, training  $p_{\theta}$  with this particular diffusion is training an autoregressive model.

We can therefore interpret the Gaussian diffusion model [\(2\)](#page-1-2) as a kind of autoregressive model with a generalized bit ordering that cannot be expressed by reordering data coordinates. Prior work has shown that such reorderings introduce inductive biases that have an impact on sample quality [\[38\]](#page-10-0), so we speculate that the Gaussian diffusion serves a similar purpose, perhaps to greater effect since Gaussian noise might be more natural to add to images compared to masking noise. Moreover, the Gaussian diffusion length is not restricted to equal the data dimension; for instance, we use  $T = 1000$ , which is less than the dimension of the  $32 \times 32 \times 3$  or  $256 \times 256 \times 3$  images in our experiments. Gaussian diffusions can be made shorter for fast sampling or longer for model expressiveness.

#### 4.4 Interpolation

We can interpolate source images  $x_0$ ,  $x'_0 \sim q(x_0)$  in latent space using q as a stochastic encoder,  $\mathbf{x}_t, \mathbf{x}'_t \sim q(\mathbf{x}_t | \mathbf{x}_0)$ , then decoding the linearly interpolated latent  $\bar{\mathbf{x}}_t = (1 - \lambda)\mathbf{x}_0 + \lambda \mathbf{x}'_0$  into image space by the reverse process,  $\bar{\mathbf{x}}_0 \sim p(\mathbf{x}_0|\bar{\mathbf{x}}_t)$ . In effect, we use the reverse process to remove artifacts from linearly interpolating corrupted versions of the source images, as depicted in Fig. [8](#page-7-0) (left). We fixed the noise for different values of  $\lambda$  so  $x_t$  and  $x'_t$  remain the same. Fig. [8](#page-7-0) (right) shows interpolations and reconstructions of original CelebA-HQ 256  $\times$  256 images ( $t = 500$ ). The reverse process produces high-quality reconstructions, and plausible interpolations that smoothly vary attributes such as pose, skin tone, hairstyle, expression and background, but not eyewear. Larger t results in coarser and more varied interpolations, with novel samples at  $t = 1000$  (Appendix Fig. [9\)](#page-15-1).

# 5 Related Work

While diffusion models might resemble flows [\[9,](#page-9-11) [46,](#page-10-10) [10,](#page-9-3) [32,](#page-10-1) [5,](#page-8-3) [16,](#page-9-12) [23\]](#page-9-13) and VAEs [\[33,](#page-10-3) [47,](#page-10-11) [37\]](#page-10-12), diffusion models are designed so that q has no parameters and the top-level latent  $x_T$  has nearly zero mutual information with the data  $x_0$ . Our  $\epsilon$ -prediction reverse process parameterization establishes a connection between diffusion models and denoising score matching over multiple noise levels with annealed Langevin dynamics for sampling [\[55,](#page-11-2) [56\]](#page-11-7). Diffusion models, however, admit straightforward log likelihood evaluation, and the training procedure explicitly trains the Langevin dynamics sampler using variational inference (see Appendix [C](#page-14-1) for details). The connection also has the reverse implication that a certain weighted form of denoising score matching is the same as variational inference to train a Langevin-like sampler. Other methods for learning transition operators of Markov chains include infusion training [\[2\]](#page-8-4), variational walkback [\[15\]](#page-9-14), generative stochastic networks [\[1\]](#page-8-5), and others [\[50,](#page-10-13) [54,](#page-11-12) [36,](#page-10-14) [42,](#page-10-15) [35,](#page-10-16) [65\]](#page-11-13).

By the known connection between score matching and energy-based modeling, our work could have implications for other recent work on energy-based models [\[67–](#page-11-14)[69,](#page-11-15) [12,](#page-9-15) [70,](#page-11-16) [13,](#page-9-16) [11,](#page-9-5) [41,](#page-10-17) [17,](#page-9-6) [8\]](#page-9-17). Our rate-distortion curves are computed over time in one evaluation of the variational bound, reminiscent of how rate-distortion curves can be computed over distortion penalties in one run of annealed importance sampling [\[24\]](#page-9-18). Our progressive decoding argument can be seen in convolutional DRAW and related models [\[18,](#page-9-7) [40\]](#page-10-18) and may also lead to more general designs for subscale orderings or sampling strategies for autoregressive models [\[38,](#page-10-0) [64\]](#page-11-17).

# 6 Conclusion

We have presented high quality image samples using diffusion models, and we have found connections among diffusion models and variational inference for training Markov chains, denoising score matching and annealed Langevin dynamics (and energy-based models by extension), autoregressive models, and progressive lossy compression. Since diffusion models seem to have excellent inductive biases for image data, we look forward to investigating their utility in other data modalities and as components in other types of generative models and machine learning systems.

## Broader Impact

Our work on diffusion models takes on a similar scope as existing work on other types of deep generative models, such as efforts to improve the sample quality of GANs, flows, autoregressive models, and so forth. Our paper represents progress in making diffusion models a generally useful tool in this family of techniques, so it may serve to amplify any impacts that generative models have had (and will have) on the broader world.

Unfortunately, there are numerous well-known malicious uses of generative models. Sample generation techniques can be employed to produce fake images and videos of high profile figures for political purposes. While fake images were manually created long before software tools were available, generative models such as ours make the process easier. Fortunately, CNN-generated images currently have subtle flaws that allow detection [\[62\]](#page-11-18), but improvements in generative models may make this more difficult. Generative models also reflect the biases in the datasets on which they are trained. As many large datasets are collected from the internet by automated systems, it can be difficult to remove these biases, especially when the images are unlabeled. If samples from generative models trained on these datasets proliferate throughout the internet, then these biases will only be reinforced further.

On the other hand, diffusion models may be useful for data compression, which, as data becomes higher resolution and as global internet traffic increases, might be crucial to ensure accessibility of the internet to wide audiences. Our work might contribute to representation learning on unlabeled raw data for a large range of downstream tasks, from image classification to reinforcement learning, and diffusion models might also become viable for creative uses in art, photography, and music.

# Acknowledgments and Disclosure of Funding

This work was supported by ONR PECASE and the NSF Graduate Research Fellowship under grant number DGE-1752814. Google's TensorFlow Research Cloud (TFRC) provided Cloud TPUs.

# References

- <span id="page-8-5"></span>[1] Guillaume Alain, Yoshua Bengio, Li Yao, Jason Yosinski, Eric Thibodeau-Laufer, Saizheng Zhang, and Pascal Vincent. GSNs: generative stochastic networks. *Information and Inference: A Journal of the IMA*, 5(2):210–249, 2016.
- <span id="page-8-4"></span>[2] Florian Bordes, Sina Honari, and Pascal Vincent. Learning to generate samples from noise through infusion training. In *International Conference on Learning Representations*, 2017.
- <span id="page-8-0"></span>[3] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale GAN training for high fidelity natural image synthesis. In *International Conference on Learning Representations*, 2019.
- <span id="page-8-2"></span>[4] Tong Che, Ruixiang Zhang, Jascha Sohl-Dickstein, Hugo Larochelle, Liam Paull, Yuan Cao, and Yoshua Bengio. Your GAN is secretly an energy-based model and you should use discriminator driven latent sampling. *arXiv preprint arXiv:2003.06060*, 2020.
- <span id="page-8-3"></span>[5] Tian Qi Chen, Yulia Rubanova, Jesse Bettencourt, and David K Duvenaud. Neural ordinary differential equations. In *Advances in Neural Information Processing Systems*, pages 6571–6583, 2018.
- <span id="page-8-6"></span>[6] Xi Chen, Nikhil Mishra, Mostafa Rohaninejad, and Pieter Abbeel. PixelSNAIL: An improved autoregressive generative model. In *International Conference on Machine Learning*, pages 863–871, 2018.
- <span id="page-8-1"></span>[7] Rewon Child, Scott Gray, Alec Radford, and Ilya Sutskever. Generating long sequences with sparse transformers. *arXiv preprint arXiv:1904.10509*, 2019.

- <span id="page-9-17"></span>[8] Yuntian Deng, Anton Bakhtin, Myle Ott, Arthur Szlam, and Marc'Aurelio Ranzato. Residual energy-based models for text generation. *arXiv preprint arXiv:2004.11714*, 2020.
- <span id="page-9-11"></span>[9] Laurent Dinh, David Krueger, and Yoshua Bengio. NICE: Non-linear independent components estimation. *arXiv preprint arXiv:1410.8516*, 2014.
- <span id="page-9-3"></span>[10] Laurent Dinh, Jascha Sohl-Dickstein, and Samy Bengio. Density estimation using Real NVP. *arXiv preprint arXiv:1605.08803*, 2016.
- <span id="page-9-5"></span>[11] Yilun Du and Igor Mordatch. Implicit generation and modeling with energy based models. In *Advances in Neural Information Processing Systems*, pages 3603–3613, 2019.
- <span id="page-9-15"></span>[12] Ruiqi Gao, Yang Lu, Junpei Zhou, Song-Chun Zhu, and Ying Nian Wu. Learning generative ConvNets via multi-grid modeling and sampling. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages 9155–9164, 2018.
- <span id="page-9-16"></span>[13] Ruiqi Gao, Erik Nijkamp, Diederik P Kingma, Zhen Xu, Andrew M Dai, and Ying Nian Wu. Flow contrastive estimation of energy-based models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7518–7528, 2020.
- <span id="page-9-0"></span>[14] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *Advances in Neural Information Processing Systems*, pages 2672–2680, 2014.
- <span id="page-9-14"></span>[15] Anirudh Goyal, Nan Rosemary Ke, Surya Ganguli, and Yoshua Bengio. Variational walkback: Learning a transition operator as a stochastic recurrent net. In *Advances in Neural Information Processing Systems*, pages 4392–4402, 2017.
- <span id="page-9-12"></span>[16] Will Grathwohl, Ricky T. Q. Chen, Jesse Bettencourt, and David Duvenaud. FFJORD: Free-form continuous dynamics for scalable reversible generative models. In *International Conference on Learning Representations*, 2019.
- <span id="page-9-6"></span>[17] Will Grathwohl, Kuan-Chieh Wang, Joern-Henrik Jacobsen, David Duvenaud, Mohammad Norouzi, and Kevin Swersky. Your classifier is secretly an energy based model and you should treat it like one. In *International Conference on Learning Representations*, 2020.
- <span id="page-9-7"></span>[18] Karol Gregor, Frederic Besse, Danilo Jimenez Rezende, Ivo Danihelka, and Daan Wierstra. Towards conceptual compression. In *Advances In Neural Information Processing Systems*, pages 3549–3557, 2016.
- <span id="page-9-9"></span>[19] Prahladh Harsha, Rahul Jain, David McAllester, and Jaikumar Radhakrishnan. The communication complexity of correlation. In *Twenty-Second Annual IEEE Conference on Computational Complexity (CCC'07)*, pages 10–23. IEEE, 2007.
- <span id="page-9-10"></span>[20] Marton Havasi, Robert Peharz, and José Miguel Hernández-Lobato. Minimal random code learning: Getting bits back from compressed model parameters. In *International Conference on Learning Representations*, 2019.
- <span id="page-9-20"></span>[21] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. GANs trained by a two time-scale update rule converge to a local Nash equilibrium. In *Advances in Neural Information Processing Systems*, pages 6626–6637, 2017.
- <span id="page-9-8"></span>[22] Irina Higgins, Loic Matthey, Arka Pal, Christopher Burgess, Xavier Glorot, Matthew Botvinick, Shakir Mohamed, and Alexander Lerchner. beta-VAE: Learning basic visual concepts with a constrained variational framework. In *International Conference on Learning Representations*, 2017.
- <span id="page-9-13"></span>[23] Jonathan Ho, Xi Chen, Aravind Srinivas, Yan Duan, and Pieter Abbeel. Flow++: Improving flow-based generative models with variational dequantization and architecture design. In *International Conference on Machine Learning*, 2019.
- <span id="page-9-18"></span>[24] Sicong Huang, Alireza Makhzani, Yanshuai Cao, and Roger Grosse. Evaluating lossy compression rates of deep generative models. In *International Conference on Machine Learning*, 2020.
- <span id="page-9-2"></span>[25] Nal Kalchbrenner, Aaron van den Oord, Karen Simonyan, Ivo Danihelka, Oriol Vinyals, Alex Graves, and Koray Kavukcuoglu. Video pixel networks. In *International Conference on Machine Learning*, pages 1771–1779, 2017.
- <span id="page-9-4"></span>[26] Nal Kalchbrenner, Erich Elsen, Karen Simonyan, Seb Noury, Norman Casagrande, Edward Lockhart, Florian Stimberg, Aaron van den Oord, Sander Dieleman, and Koray Kavukcuoglu. Efficient neural audio synthesis. In *International Conference on Machine Learning*, pages 2410–2419, 2018.
- <span id="page-9-1"></span>[27] Tero Karras, Timo Aila, Samuli Laine, and Jaakko Lehtinen. Progressive growing of GANs for improved quality, stability, and variation. In *International Conference on Learning Representations*, 2018.
- <span id="page-9-19"></span>[28] Tero Karras, Samuli Laine, and Timo Aila. A style-based generator architecture for generative adversarial networks. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages

4401–4410, 2019.

- <span id="page-10-6"></span>[29] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Training generative adversarial networks with limited data. *arXiv preprint arXiv:2006.06676v1*, 2020.
- <span id="page-10-19"></span>[30] Tero Karras, Samuli Laine, Miika Aittala, Janne Hellsten, Jaakko Lehtinen, and Timo Aila. Analyzing and improving the image quality of StyleGAN. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 8110–8119, 2020.
- <span id="page-10-21"></span>[31] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In *International Conference on Learning Representations*, 2015.
- <span id="page-10-1"></span>[32] Diederik P Kingma and Prafulla Dhariwal. Glow: Generative flow with invertible 1x1 convolutions. In *Advances in Neural Information Processing Systems*, pages 10215–10224, 2018.
- <span id="page-10-3"></span>[33] Diederik P Kingma and Max Welling. Auto-encoding variational Bayes. *arXiv preprint arXiv:1312.6114*, 2013.
- <span id="page-10-5"></span>[34] Diederik P Kingma, Tim Salimans, Rafal Jozefowicz, Xi Chen, Ilya Sutskever, and Max Welling. Improved variational inference with inverse autoregressive flow. In *Advances in Neural Information Processing Systems*, pages 4743–4751, 2016.
- <span id="page-10-16"></span>[35] John Lawson, George Tucker, Bo Dai, and Rajesh Ranganath. Energy-inspired models: Learning with sampler-induced distributions. In *Advances in Neural Information Processing Systems*, pages 8501–8513, 2019.
- <span id="page-10-14"></span>[36] Daniel Levy, Matt D. Hoffman, and Jascha Sohl-Dickstein. Generalizing Hamiltonian Monte Carlo with neural networks. In *International Conference on Learning Representations*, 2018.
- <span id="page-10-12"></span>[37] Lars Maaløe, Marco Fraccaro, Valentin Liévin, and Ole Winther. BIVA: A very deep hierarchy of latent variables for generative modeling. In *Advances in Neural Information Processing Systems*, pages 6548–6558, 2019.
- <span id="page-10-0"></span>[38] Jacob Menick and Nal Kalchbrenner. Generating high fidelity images with subscale pixel networks and multidimensional upscaling. In *International Conference on Learning Representations*, 2019.
- <span id="page-10-8"></span>[39] Takeru Miyato, Toshiki Kataoka, Masanori Koyama, and Yuichi Yoshida. Spectral normalization for generative adversarial networks. In *International Conference on Learning Representations*, 2018.
- <span id="page-10-18"></span>[40] Alex Nichol. VQ-DRAW: A sequential discrete VAE. *arXiv preprint arXiv:2003.01599*, 2020.
- <span id="page-10-17"></span>[41] Erik Nijkamp, Mitch Hill, Tian Han, Song-Chun Zhu, and Ying Nian Wu. On the anatomy of MCMC-based maximum likelihood learning of energy-based models. *arXiv preprint arXiv:1903.12370*, 2019.
- <span id="page-10-15"></span>[42] Erik Nijkamp, Mitch Hill, Song-Chun Zhu, and Ying Nian Wu. Learning non-convergent non-persistent short-run MCMC toward energy-based model. In *Advances in Neural Information Processing Systems*, pages 5233–5243, 2019.
- <span id="page-10-7"></span>[43] Georg Ostrovski, Will Dabney, and Remi Munos. Autoregressive quantile networks for generative modeling. In *International Conference on Machine Learning*, pages 3936–3945, 2018.
- <span id="page-10-2"></span>[44] Ryan Prenger, Rafael Valle, and Bryan Catanzaro. WaveGlow: A flow-based generative network for speech synthesis. In *ICASSP 2019-2019 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)*, pages 3617–3621. IEEE, 2019.
- <span id="page-10-4"></span>[45] Ali Razavi, Aaron van den Oord, and Oriol Vinyals. Generating diverse high-fidelity images with VQ-VAE-2. In *Advances in Neural Information Processing Systems*, pages 14837–14847, 2019.
- <span id="page-10-10"></span>[46] Danilo Rezende and Shakir Mohamed. Variational inference with normalizing flows. In *International Conference on Machine Learning*, pages 1530–1538, 2015.
- <span id="page-10-11"></span>[47] Danilo Jimenez Rezende, Shakir Mohamed, and Daan Wierstra. Stochastic backpropagation and approximate inference in deep generative models. In *International Conference on Machine Learning*, pages 1278–1286, 2014.
- <span id="page-10-9"></span>[48] Olaf Ronneberger, Philipp Fischer, and Thomas Brox. U-Net: Convolutional networks for biomedical image segmentation. In *International Conference on Medical Image Computing and Computer-Assisted Intervention*, pages 234–241. Springer, 2015.
- <span id="page-10-20"></span>[49] Tim Salimans and Durk P Kingma. Weight normalization: A simple reparameterization to accelerate training of deep neural networks. In *Advances in Neural Information Processing Systems*, pages 901–909, 2016.
- <span id="page-10-13"></span>[50] Tim Salimans, Diederik Kingma, and Max Welling. Markov Chain Monte Carlo and variational inference: Bridging the gap. In *International Conference on Machine Learning*, pages 1218–1226, 2015.

- <span id="page-11-20"></span>[51] Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. In *Advances in Neural Information Processing Systems*, pages 2234–2242, 2016.
- <span id="page-11-5"></span>[52] Tim Salimans, Andrej Karpathy, Xi Chen, and Diederik P Kingma. PixelCNN++: Improving the PixelCNN with discretized logistic mixture likelihood and other modifications. In *International Conference on Learning Representations*, 2017.
- <span id="page-11-3"></span>[53] Jascha Sohl-Dickstein, Eric Weiss, Niru Maheswaranathan, and Surya Ganguli. Deep unsupervised learning using nonequilibrium thermodynamics. In *International Conference on Machine Learning*, pages 2256–2265, 2015.
- <span id="page-11-12"></span>[54] Jiaming Song, Shengjia Zhao, and Stefano Ermon. A-NICE-MC: Adversarial training for MCMC. In *Advances in Neural Information Processing Systems*, pages 5140–5150, 2017.
- <span id="page-11-2"></span>[55] Yang Song and Stefano Ermon. Generative modeling by estimating gradients of the data distribution. In *Advances in Neural Information Processing Systems*, pages 11895–11907, 2019.
- <span id="page-11-7"></span>[56] Yang Song and Stefano Ermon. Improved techniques for training score-based generative models. *arXiv preprint arXiv:2006.09011*, 2020.
- <span id="page-11-1"></span>[57] Aaron van den Oord, Sander Dieleman, Heiga Zen, Karen Simonyan, Oriol Vinyals, Alex Graves, Nal Kalchbrenner, Andrew Senior, and Koray Kavukcuoglu. WaveNet: A generative model for raw audio. *arXiv preprint arXiv:1609.03499*, 2016.
- <span id="page-11-0"></span>[58] Aaron van den Oord, Nal Kalchbrenner, and Koray Kavukcuoglu. Pixel recurrent neural networks. *International Conference on Machine Learning*, 2016.
- <span id="page-11-6"></span>[59] Aaron van den Oord, Nal Kalchbrenner, Oriol Vinyals, Lasse Espeholt, Alex Graves, and Koray Kavukcuoglu. Conditional image generation with PixelCNN decoders. In *Advances in Neural Information Processing Systems*, pages 4790–4798, 2016.
- <span id="page-11-9"></span>[60] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. Attention is all you need. In *Advances in Neural Information Processing Systems*, pages 5998–6008, 2017.
- <span id="page-11-4"></span>[61] Pascal Vincent. A connection between score matching and denoising autoencoders. *Neural Computation*, 23(7):1661–1674, 2011.
- <span id="page-11-18"></span>[62] Sheng-Yu Wang, Oliver Wang, Richard Zhang, Andrew Owens, and Alexei A Efros. Cnn-generated images are surprisingly easy to spot...for now. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, 2020.
- <span id="page-11-10"></span>[63] Xiaolong Wang, Ross Girshick, Abhinav Gupta, and Kaiming He. Non-local neural networks. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages 7794–7803, 2018.
- <span id="page-11-17"></span>[64] Auke J Wiggers and Emiel Hoogeboom. Predictive sampling with forecasting autoregressive models. *arXiv preprint arXiv:2002.09928*, 2020.
- <span id="page-11-13"></span>[65] Hao Wu, Jonas Köhler, and Frank Noé. Stochastic normalizing flows. *arXiv preprint arXiv:2002.06707*, 2020.
- <span id="page-11-8"></span>[66] Yuxin Wu and Kaiming He. Group normalization. In *Proceedings of the European Conference on Computer Vision (ECCV)*, pages 3–19, 2018.
- <span id="page-11-14"></span>[67] Jianwen Xie, Yang Lu, Song-Chun Zhu, and Yingnian Wu. A theory of generative convnet. In *International Conference on Machine Learning*, pages 2635–2644, 2016.
- [68] Jianwen Xie, Song-Chun Zhu, and Ying Nian Wu. Synthesizing dynamic patterns by spatial-temporal generative convnet. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages 7093–7101, 2017.
- <span id="page-11-15"></span>[69] Jianwen Xie, Zilong Zheng, Ruiqi Gao, Wenguan Wang, Song-Chun Zhu, and Ying Nian Wu. Learning descriptor networks for 3d shape synthesis and analysis. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages 8629–8638, 2018.
- <span id="page-11-16"></span>[70] Jianwen Xie, Song-Chun Zhu, and Ying Nian Wu. Learning energy-based spatial-temporal generative convnets for dynamic patterns. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2019.
- <span id="page-11-11"></span>[71] Fisher Yu, Yinda Zhang, Shuran Song, Ari Seff, and Jianxiong Xiao. LSUN: Construction of a large-scale image dataset using deep learning with humans in the loop. *arXiv preprint arXiv:1506.03365*, 2015.
- <span id="page-11-19"></span>[72] Sergey Zagoruyko and Nikos Komodakis. Wide residual networks. *arXiv preprint arXiv:1605.07146*, 2016.

# Extra information

<span id="page-12-2"></span>LSUN FID scores for LSUN datasets are included in Table [3.](#page-12-2) Scores marked with <sup>∗</sup> are reported by StyleGAN2 as baselines, and other scores are reported by their respective authors.

| Table 3: FID scores for LSUN $256 \times 256$ datasets |              |             |             |
|--------------------------------------------------------|--------------|-------------|-------------|
| Model                                                  | LSUN Bedroom | LSUN Church | LSUN Cat    |
| ProgressiveGAN [27]                                    | 8.34         | 6.42        | 37.52       |
| StyleGAN [28]                                          | <b>2.65</b>  | $4.21*$     | $8.53*$     |
| StyleGAN2 [30]                                         | -            | <b>3.86</b> | <b>6.93</b> |
| Ours ( $L_{\text{simple}}$ )                           | 6.36         | 7.89        | 19.75       |
| Ours ( $L_{\text{simple}}, large$ )                    | 4.90         | -           | -           |

Progressive compression Our lossy compression argument in Section [4.3](#page-5-1) is only a proof of concept, because Algorithms [3](#page-5-3) and [4](#page-5-3) depend on a procedure such as minimal random coding [\[20\]](#page-9-10), which is not tractable for high dimensional data. These algorithms serve as a compression interpretation of the variational bound [\(5\)](#page-2-2) of Sohl-Dickstein et al. [\[53\]](#page-11-3), not yet as a practical compression system.

<span id="page-12-1"></span>Table 4: Unconditional CIFAR10 test set rate-distortion values (accompanies Fig. [5\)](#page-6-0)

| Reverse process time $(T - t + 1)$ | Rate (bits/dim) | Distortion (RMSE $[0, 255]$ ) |
|------------------------------------|-----------------|-------------------------------|
| 1000                               | 1.77581         | 0.95136                       |
| 900                                | 0.11994         | 12.02277                      |
| 800                                | 0.05415         | 18.47482                      |
| 700                                | 0.02866         | 24.43656                      |
| 600                                | 0.01507         | 30.80948                      |
| 500                                | 0.00716         | 38.03236                      |
| 400                                | 0.00282         | 46.12765                      |
| 300                                | 0.00081         | 54.18826                      |
| 200                                | 0.00013         | 60.97170                      |
| 100                                | 0.00000         | 67.60125                      |

# <span id="page-12-0"></span>A Extended derivations

Below is a derivation of Eq. [\(5\)](#page-2-2), the reduced variance variational bound for diffusion models. This material is from Sohl-Dickstein et al. [\[53\]](#page-11-3); we include it here only for completeness.

$$
L = \mathbb{E}_q \left[ -\log \frac{p_\theta(\mathbf{x}_{0:T})}{q(\mathbf{x}_{1:T}|\mathbf{x}_0)} \right]
$$
(17)

$$
= \mathbb{E}_q \left[ -\log p(\mathbf{x}_T) - \sum_{t \ge 1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_t|\mathbf{x}_{t-1})} \right]
$$
(18)

$$
= \mathbb{E}_q \left[ -\log p(\mathbf{x}_T) - \sum_{t>1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_t|\mathbf{x}_{t-1})} - \log \frac{p_\theta(\mathbf{x}_0|\mathbf{x}_1)}{q(\mathbf{x}_1|\mathbf{x}_0)} \right]
$$
(19)

$$
= \mathbb{E}_q \left[ -\log p(\mathbf{x}_T) - \sum_{t>1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0)} \cdot \frac{q(\mathbf{x}_{t-1}|\mathbf{x}_0)}{q(\mathbf{x}_t|\mathbf{x}_0)} - \log \frac{p_\theta(\mathbf{x}_0|\mathbf{x}_1)}{q(\mathbf{x}_1|\mathbf{x}_0)} \right]
$$
(20)

$$
= \mathbb{E}_q \left[ -\log \frac{p(\mathbf{x}_T)}{q(\mathbf{x}_T|\mathbf{x}_0)} - \sum_{t>1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_{t-1}|\mathbf{x}_t,\mathbf{x}_0)} - \log p_\theta(\mathbf{x}_0|\mathbf{x}_1) \right]
$$
(21)

$$
= \mathbb{E}_{q} \left[ D_{\mathrm{KL}}(q(\mathbf{x}_{T}|\mathbf{x}_{0}) \parallel p(\mathbf{x}_{T})) + \sum_{t>1} D_{\mathrm{KL}}(q(\mathbf{x}_{t-1}|\mathbf{x}_{t}, \mathbf{x}_{0}) \parallel p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_{t})) - \log p_{\theta}(\mathbf{x}_{0}|\mathbf{x}_{1})) \right]
$$
(22)

The following is an alternate version of  $L$ . It is not tractable to estimate, but it is useful for our discussion in Section [4.3.](#page-5-1)

$$
L = \mathbb{E}_q \left[ -\log p(\mathbf{x}_T) - \sum_{t \ge 1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_t|\mathbf{x}_{t-1})} \right]
$$
(23)

$$
= \mathbb{E}_q \left[ -\log p(\mathbf{x}_T) - \sum_{t \ge 1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_{t-1}|\mathbf{x}_t)} \cdot \frac{q(\mathbf{x}_{t-1})}{q(\mathbf{x}_t)} \right]
$$
(24)

$$
= \mathbb{E}_q \left[ -\log \frac{p(\mathbf{x}_T)}{q(\mathbf{x}_T)} - \sum_{t \ge 1} \log \frac{p_\theta(\mathbf{x}_{t-1}|\mathbf{x}_t)}{q(\mathbf{x}_{t-1}|\mathbf{x}_t)} - \log q(\mathbf{x}_0) \right]
$$
(25)

$$
= D_{\mathrm{KL}}(q(\mathbf{x}_{T}) \parallel p(\mathbf{x}_{T})) + \mathbb{E}_{q} \left[ \sum_{t \geq 1} D_{\mathrm{KL}}(q(\mathbf{x}_{t-1}|\mathbf{x}_{t}) \parallel p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_{t})) \right] + H(\mathbf{x}_{0}) \tag{26}
$$

# <span id="page-13-0"></span>B Experimental details

Our neural network architecture follows the backbone of PixelCNN++ [\[52\]](#page-11-5), which is a U-Net [\[48\]](#page-10-9) based on a Wide ResNet [\[72\]](#page-11-19). We replaced weight normalization [\[49\]](#page-10-20) with group normalization [\[66\]](#page-11-8) to make the implementation simpler. Our  $32 \times 32$  models use four feature map resolutions ( $32 \times 32$ ) to  $4 \times 4$ ), and our  $256 \times 256$  models use six. All models have two convolutional residual blocks per resolution level and self-attention blocks at the  $16 \times 16$  resolution between the convolutional blocks [\[6\]](#page-8-6). Diffusion time  $t$  is specified by adding the Transformer sinusoidal position embedding [\[60\]](#page-11-9) into each residual block. Our CIFAR10 model has 35.7 million parameters, and our LSUN and CelebA-HQ models have 114 million parameters. We also trained a larger variant of the LSUN Bedroom model with approximately 256 million parameters by increasing filter count.

We used TPU v3-8 (similar to 8 V100 GPUs) for all experiments. Our CIFAR model trains at 21 steps per second at batch size 128 (10.6 hours to train to completion at 800k steps), and sampling a batch of 256 images takes 17 seconds. Our CelebA-HQ/LSUN (256<sup>2</sup> ) models train at 2.2 steps per second at batch size 64, and sampling a batch of 128 images takes 300 seconds. We trained on CelebA-HQ for 0.5M steps, LSUN Bedroom for 2.4M steps, LSUN Cat for 1.8M steps, and LSUN Church for 1.2M steps. The larger LSUN Bedroom model was trained for 1.15M steps.

Apart from an initial choice of hyperparameters early on to make network size fit within memory constraints, we performed the majority of our hyperparameter search to optimize for CIFAR10 sample quality, then transferred the resulting settings over to the other datasets:

- We chose the  $\beta_t$  schedule from a set of constant, linear, and quadratic schedules, all constrained so that  $L_T \approx 0$ . We set  $T = 1000$  without a sweep, and we chose a linear schedule from  $\beta_1 = 10^{-4}$  to  $\beta_T = 0.02$ .
- We set the dropout rate on CIFAR10 to 0.1 by sweeping over the values  $\{0.1, 0.2, 0.3, 0.4\}$ . Without dropout on CIFAR10, we obtained poorer samples reminiscent of the overfitting artifacts in an unregularized PixelCNN++ [\[52\]](#page-11-5). We set dropout rate on the other datasets to zero without sweeping.
- We used random horizontal flips during training for CIFAR10; we tried training both with and without flips, and found flips to improve sample quality slightly. We also used random horizontal flips for all other datasets except LSUN Bedroom.
- We tried Adam [\[31\]](#page-10-21) and RMSProp early on in our experimentation process and chose the former. We left the hyperparameters to their standard values. We set the learning rate to  $2 \times 10^{-4}$  without any sweeping, and we lowered it to  $2 \times 10^{-5}$  for the  $256 \times 256$  images, which seemed unstable to train with the larger learning rate.

- We set the batch size to 128 for CIFAR10 and 64 for larger images. We did not sweep over these values.
- We used EMA on model parameters with a decay factor of 0.9999. We did not sweep over this value.

Final experiments were trained once and evaluated throughout training for sample quality. Sample quality scores and log likelihood are reported on the minimum FID value over the course of training. On CIFAR10, we calculated Inception and FID scores on 50000 samples using the original code from the OpenAI [\[51\]](#page-11-20) and TTUR [\[21\]](#page-9-20) repositories, respectively. On LSUN, we calculated FID scores on 50000 samples using code from the StyleGAN2 [\[30\]](#page-10-19) repository. CIFAR10 and CelebA-HQ were loaded as provided by TensorFlow Datasets (<https://www.tensorflow.org/datasets>), and LSUN was prepared using code from StyleGAN. Dataset splits (or lack thereof) are standard from the papers that introduced their usage in a generative modeling context. All details can be found in the source code release.

# <span id="page-14-1"></span>C Discussion on related work

Our model architecture, forward process definition, and prior differ from NCSN [\[55,](#page-11-2) [56\]](#page-11-7) in subtle but important ways that improve sample quality, and, notably, we directly train our sampler as a latent variable model rather than adding it after training post-hoc. In greater detail:

- 1. We use a U-Net with self-attention; NCSN uses a RefineNet with dilated convolutions. We condition all layers on  $t$  by adding in the Transformer sinusoidal position embedding, rather than only in normalization layers (NCSNv1) or only at the output  $(v2)$ .
- 2. Diffusion models scale down the data with each forward process step (by a  $\sqrt{1-\beta_t}$  factor) so that variance does not grow when adding noise, thus providing consistently scaled inputs to the neural net reverse process. NCSN omits this scaling factor.
- 3. Unlike NCSN, our forward process destroys signal  $(D_{\text{KL}}(q(\mathbf{x}_T | \mathbf{x}_0) \mid \mathcal{N}(\mathbf{0}, \mathbf{I})) \approx 0)$ , ensuring a close match between the prior and aggregate posterior of  $x_T$ . Also unlike NCSN, our  $\beta_t$  are very small, which ensures that the forward process is reversible by a Markov chain with conditional Gaussians. Both of these factors prevent distribution shift when sampling.
- 4. Our Langevin-like sampler has coefficients (learning rate, noise scale, etc.) derived rigorously from  $\beta_t$  in the forward process. Thus, our training procedure directly trains our sampler to match the data distribution after  $T$  steps: it trains the sampler as a latent variable model using variational inference. In contrast, NCSN's sampler coefficients are set by hand post-hoc, and their training procedure is not guaranteed to directly optimize a quality metric of their sampler.

# <span id="page-14-0"></span>D Samples

Additional samples Figure [11,](#page-16-0) [13,](#page-18-0) [16,](#page-21-0) [17,](#page-22-0) [18,](#page-23-0) and [19](#page-24-0) show uncurated samples from the diffusion models trained on CelebA-HQ, CIFAR10 and LSUN datasets.

Latent structure and reverse process stochasticity During sampling, both the prior  $x_T \sim$  $\mathcal{N}(\mathbf{0}, \mathbf{I})$  and Langevin dynamics are stochastic. To understand the significance of the second source of noise, we sampled multiple images conditioned on the same intermediate latent for the CelebA 256 × 256 dataset. Figure [7](#page-6-2) shows multiple draws from the reverse process  $\mathbf{x}_0 \sim p_\theta(\mathbf{x}_0|\mathbf{x}_t)$  that share the latent  $x_t$  for  $t \in \{1000, 750, 500, 250\}$ . To accomplish this, we run a single reverse chain from an initial draw from the prior. At the intermediate timesteps, the chain is split to sample multiple images. When the chain is split after the prior draw at  $x_{T=1000}$ , the samples differ significantly. However, when the chain is split after more steps, samples share high-level attributes like gender, hair color, eyewear, saturation, pose and facial expression. This indicates that intermediate latents like  $x_{750}$  encode these attributes, despite their imperceptibility.

Coarse-to-fine interpolation Figure [9](#page-15-1) shows interpolations between a pair of source CelebA  $256 \times 256$  images as we vary the number of diffusion steps prior to latent space interpolation. Increasing the number of diffusion steps destroys more structure in the source images, which the

model completes during the reverse process. This allows us to interpolate at both fine granularities and coarse granularities. In the limiting case of 0 diffusion steps, the interpolation mixes source images in pixel space. On the other hand, after 1000 diffusion steps, source information is lost and interpolations are novel samples.

<span id="page-15-1"></span>Image /page/15/Figure/1 description: This image displays a grid of faces, organized by the number of steps on the left vertical axis and different lambda values (λ=0.1 to λ=0.9) across the top horizontal axis. The first column is labeled 'Source' and shows the original faces. The second column is labeled 'Rec.' and shows reconstructed faces. The remaining columns show faces generated with varying lambda values. The rows represent different numbers of steps, from 1000 steps at the top down to 0 steps at the bottom. The last two columns on the right are also labeled 'Rec.' and 'Source', showing reconstructed and original faces respectively, likely for comparison or as a reference.

Figure 9: Coarse-to-fine interpolations that vary the number of diffusion steps prior to latent mixing.

<span id="page-15-0"></span>Image /page/15/Figure/3 description: This image contains two scatter plots side-by-side. Both plots share the same x-axis label: "Reverse process steps (T - t)", with values ranging from 0 to 1,000. The left plot is titled "Inception Score" on the y-axis, which ranges from 0 to 10. The data points in this plot show an increasing trend, starting around 1.5 at 0 steps and rising to approximately 9.5 at 1,000 steps. The right plot is titled "FID" on the y-axis, which ranges from 0 to 400. The data points in this plot show a decreasing trend, starting around 350 at 0 steps and dropping to approximately 50 at 1,000 steps.

Figure 10: Unconditional CIFAR10 progressive sampling quality over time

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image is a grid of 80 celebrity headshots, arranged in 8 rows and 10 columns. Each headshot is a portrait of a different person, with a variety of ages, genders, ethnicities, and expressions. The grid appears to be a collection of generated or sampled images, possibly for a machine learning or AI project, as indicated by the caption at the bottom which reads 'Figure 11: CelebA HQ 256 x 256 generated samples'.

Figure 11: CelebA-HQ  $256 \times 256$  generated samples

Image /page/17/Picture/0 description: A grid of 40 headshots of various people, arranged in 5 rows and 8 columns. The individuals are diverse in age, gender, and ethnicity, with many smiling or looking directly at the camera. The images appear to be professional headshots, likely for actors or models.

(a) Pixel space nearest neighbors

Image /page/17/Picture/2 description: The image is a grid of 40 portraits of people, arranged in 5 rows and 8 columns. The portraits are of diverse individuals, featuring men and women of various ethnicities and ages. The individuals are mostly looking directly at the camera, with some smiling and others with neutral expressions. The background of each portrait is plain and varies from white to light gray or beige. The overall impression is a collection of headshots, likely for identification or comparison purposes.

(b) Inception feature space nearest neighbors

Figure 12: CelebA-HQ 256  $\times$  256 nearest neighbors, computed on a 100  $\times$  100 crop surrounding the faces. Generated samples are in the leftmost column, and training set nearest neighbors are in the remaining columns.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image is a photograph of an object or animal. The objects and animals depicted include cars, airplanes, ships, dogs, cats, horses, deer, frogs, birds, and other various subjects. The overall impression is a collage of diverse photographic samples, likely from a dataset or a visual catalog.

Figure 13: Unconditional CIFAR10 generated samples

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image displays a grid of generated images, likely from a machine learning model, organized into rows and columns. Each row appears to represent a progression or variation of image generation, starting with noisy or abstract inputs on the left and evolving into more recognizable objects on the right. The objects depicted include airplanes, animals such as horses, dogs, and deer, as well as vehicles like cars and ambulances, and boats. The overall impression is a visualization of generative capabilities, possibly showcasing different stages of image synthesis or the diversity of classes the model can generate.

Figure 14: Unconditional CIFAR10 progressive generation

Image /page/20/Figure/0 description: The image displays two grids of images, labeled (a) and (b). Grid (a) is titled "Pixel space nearest neighbors" and shows a collection of images, primarily featuring animals such as birds, cats, dogs, deer, and frogs, along with some airplanes and ships. Grid (b) is titled "Inception feature space nearest neighbors" and also contains a variety of images, including animals like birds, dogs, deer, and frogs, as well as airplanes and ships. Both grids appear to be demonstrating image similarity or nearest neighbor search results.

(b) Inception feature space nearest neighbors

Figure 15: Unconditional CIFAR10 nearest neighbors. Generated samples are in the leftmost column, and training set nearest neighbors are in the remaining columns.

<span id="page-21-0"></span>Image /page/21/Picture/0 description: This is a grid of 56 images of churches, arranged in 7 rows and 8 columns. The churches vary in architectural style, color, and condition, with some appearing well-maintained and others in ruins. The images are sourced from LSUN Church generated samples, with the figure number indicated as Figure 16 and FID score as 7.89.

Figure 16: LSUN Church generated samples. FID=7.89

<span id="page-22-0"></span>Image /page/22/Picture/0 description: This is a collage of 70 images of bedrooms, each featuring a bed. The images are arranged in a grid of 7 rows and 10 columns. The bedrooms vary in style, color, and size, showcasing different bed frames, bedding, and room decor. Some beds are neatly made, while others appear more lived-in. The overall impression is a diverse collection of bedroom interior designs.

Figure 17: LSUN Bedroom generated samples, large model. FID=4.90

<span id="page-23-0"></span>Image /page/23/Picture/0 description: This is a collage of 36 images of bedrooms. The images are arranged in a 6x6 grid. The bedrooms vary in style and decor, with some featuring modern designs and others having more traditional or rustic elements. Several images showcase large beds with plush bedding, while others highlight unique architectural features or scenic views from the windows. The overall impression is a diverse collection of bedroom interiors.

Figure 18: LSUN Bedroom generated samples, small model. FID=6.36

<span id="page-24-0"></span>Image /page/24/Picture/0 description: This is a grid of 64 images of cats. The images are arranged in 8 rows and 8 columns. The cats are of various breeds, colors, and ages. Some of the cats are kittens, while others are adults. Some of the cats are playful, while others are sleeping or resting. The images are all high quality and well-lit. The overall impression is one of cuteness and variety.

Figure 19: LSUN Cat generated samples. FID=19.75