{"table_of_contents": [{"title": "Denoising Diffusion Probabilistic Models", "heading_level": null, "page_id": 0, "polygon": [[153.75, 99.0], [456.75, 99.0], [456.75, 116.4990234375], [153.75, 116.4990234375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 222.75], [329.25, 222.75], [329.25, 234.3515625], [282.75, 234.3515625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 378.75], [191.25, 378.75], [191.25, 390.97265625], [107.25, 390.97265625]]}, {"title": "2 Background", "heading_level": null, "page_id": 1, "polygon": [[107.25, 419.9765625], [189.755859375, 419.9765625], [189.755859375, 430.8046875], [107.25, 430.8046875]]}, {"title": "3 Diffusion models and denoising autoencoders", "heading_level": null, "page_id": 2, "polygon": [[106.90576171875, 267.0], [357.0, 267.0], [357.0, 278.244140625], [106.90576171875, 278.244140625]]}, {"title": "3.1 Forward process and L_T", "heading_level": null, "page_id": 2, "polygon": [[106.5, 379.5], [236.2236328125, 379.5], [236.2236328125, 390.005859375], [106.5, 390.005859375]]}, {"title": "3.2 Reverse process and L_{1:T-1}", "heading_level": null, "page_id": 2, "polygon": [[106.5, 446.25], [247.5791015625, 446.25], [247.5791015625, 456.0], [106.5, 456.0]]}, {"title": "3.3 Data scaling, reverse process decoder, and L_0", "heading_level": null, "page_id": 3, "polygon": [[106.5, 461.25], [321.75, 461.25], [321.75, 471.796875], [106.5, 471.796875]]}, {"title": "3.4 Simplified training objective", "heading_level": null, "page_id": 3, "polygon": [[106.8310546875, 681.0], [251.25, 681.0], [251.25, 691.06640625], [106.8310546875, 691.06640625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 4, "polygon": [[107.25, 494.25], [192.0, 494.25], [192.0, 505.828125], [107.25, 505.828125]]}, {"title": "4.1 Sample quality", "heading_level": null, "page_id": 4, "polygon": [[106.5, 648.0], [195.0, 648.0], [195.0, 658.58203125], [106.5, 658.58203125]]}, {"title": "4.2 Reverse process parameterization and training objective ablation", "heading_level": null, "page_id": 5, "polygon": [[106.5, 376.083984375], [408.75, 376.083984375], [408.75, 386.912109375], [106.5, 387.0]]}, {"title": "4.3 Progressive coding", "heading_level": null, "page_id": 5, "polygon": [[107.20458984375, 497.70703125], [211.5703125, 497.70703125], [211.5703125, 508.53515625], [107.20458984375, 508.53515625]]}, {"title": "4.4 Interpolation", "heading_level": null, "page_id": 7, "polygon": [[106.5, 344.1796875], [187.5, 344.1796875], [187.5, 353.84765625], [106.5, 353.84765625]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 7, "polygon": [[107.25, 491.1328125], [198.0, 491.1328125], [198.0, 501.9609375], [107.25, 501.9609375]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 72.0], [183.75, 72.0], [183.75, 83.91796875], [106.5, 83.91796875]]}, {"title": "Broader Impact", "heading_level": null, "page_id": 8, "polygon": [[106.5, 180.75], [191.25, 180.75], [191.25, 191.5224609375], [106.5, 191.5224609375]]}, {"title": "Acknowledgments and Disclosure of Funding", "heading_level": null, "page_id": 8, "polygon": [[107.25, 452.25], [340.5, 452.25], [340.5, 463.2890625], [107.25, 463.2890625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[107.25, 515.49609375], [164.654296875, 515.49609375], [164.654296875, 526.32421875], [107.25, 526.32421875]]}, {"title": "Extra information", "heading_level": null, "page_id": 12, "polygon": [[106.5, 71.25], [202.5, 71.25], [202.5, 83.77294921875], [106.5, 83.77294921875]]}, {"title": "A Extended derivations", "heading_level": null, "page_id": 12, "polygon": [[106.5, 497.25], [238.5, 497.25], [238.5, 509.6953125], [106.5, 509.6953125]]}, {"title": "B Experimental details", "heading_level": null, "page_id": 13, "polygon": [[106.5, 318.0], [234.75, 318.0], [234.75, 328.904296875], [106.5, 328.904296875]]}, {"title": "C Discussion on related work", "heading_level": null, "page_id": 14, "polygon": [[107.25, 243.75], [267.0, 243.75], [267.0, 255.427734375], [107.25, 255.427734375]]}, {"title": "D Samples", "heading_level": null, "page_id": 14, "polygon": [[106.5, 511.5], [171.75, 511.5], [171.75, 522.45703125], [106.5, 522.45703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 31], ["Text", 6], ["SectionHeader", 3], ["Picture", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 649], ["Line", 73], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 709, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 977], ["Line", 138], ["Equation", 6], ["Reference", 6], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 936], ["Line", 134], ["TableCell", 8], ["TextInlineMath", 6], ["Reference", 5], ["Equation", 3], ["SectionHeader", 2], ["Table", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10394, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 650], ["TableCell", 93], ["Line", 83], ["Text", 4], ["Reference", 3], ["Table", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 910, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 45], ["TableCell", 24], ["Reference", 4], ["TextInlineMath", 3], ["Picture", 2], ["Caption", 2], ["Text", 2], ["SectionHeader", 2], ["PictureGroup", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5445, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 66], ["TextInlineMath", 3], ["Caption", 3], ["Reference", 3], ["Text", 2], ["Equation", 2], ["Figure", 2], ["FigureGroup", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2206, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 58], ["TextInlineMath", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 694, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 49], ["ListItem", 7], ["Reference", 7], ["Text", 5], ["SectionHeader", 4], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 56], ["ListItem", 21], ["Reference", 21], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 54], ["ListItem", 22], ["Reference", 22], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 55], ["ListItem", 22], ["Reference", 21], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 480], ["TableCell", 104], ["Line", 82], ["Equation", 5], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["Table", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3473, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 628], ["Line", 84], ["Equation", 5], ["ListItem", 4], ["Text", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 50], ["ListItem", 6], ["Text", 4], ["SectionHeader", 2], ["ListGroup", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 34], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1548, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 32], ["Line", 5], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1196, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Line", 20], ["Span", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 608, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Line", 20], ["Span", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Line", 24], ["Span", 9], ["Figure", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 643, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 5], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 601, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 607, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Denoising Diffusion Probabilistic Models"}