{"table_of_contents": [{"title": "21 Variational inference", "heading_level": null, "page_id": 0, "polygon": [[65.25, 92.70703125], [286.5, 92.70703125], [286.5, 143.490234375], [64.5, 143.490234375]]}, {"title": "21.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 217.845703125], [96.75, 217.845703125]]}, {"title": "21.2 Variational inference", "heading_level": null, "page_id": 1, "polygon": [[95.25, 60.75], [238.5, 60.75], [238.5, 71.666015625], [95.25, 71.666015625]]}, {"title": "21.2.1 Alternative interpretations of the variational objective", "heading_level": null, "page_id": 2, "polygon": [[89.71875, 61.5], [384.75, 61.5], [384.75, 71.78466796875], [89.71875, 71.78466796875]]}, {"title": "21.2.2 Forward or reverse KL? *", "heading_level": null, "page_id": 2, "polygon": [[88.3828125, 300.0], [248.25, 300.0], [248.25, 309.919921875], [88.3828125, 309.919921875]]}, {"title": "21.3 The mean field method", "heading_level": null, "page_id": 4, "polygon": [[95.25, 412.5], [250.5, 412.5], [250.5, 423.03515625], [95.25, 423.03515625]]}, {"title": "21.3.1 Derivation of the mean field update equations", "heading_level": null, "page_id": 5, "polygon": [[90.75, 509.25], [348.0, 509.25], [348.0, 519.5390625], [90.75, 519.5390625]]}, {"title": "21.3.2 Example: mean field for the Ising model", "heading_level": null, "page_id": 6, "polygon": [[88.8046875, 520.5], [322.5, 520.5], [322.5, 530.9296875], [88.8046875, 530.9296875]]}, {"title": "21.4 Structured mean field *", "heading_level": null, "page_id": 8, "polygon": [[95.25, 554.25], [252.0, 554.25], [252.0, 565.734375], [95.25, 565.734375]]}, {"title": "21.4.1 Example: factorial HMM", "heading_level": null, "page_id": 9, "polygon": [[90.75, 341.25], [246.0, 341.25], [246.0, 351.2109375], [90.75, 351.2109375]]}, {"title": "21.5 Variational Bayes", "heading_level": null, "page_id": 11, "polygon": [[95.25, 150.0], [219.796875, 150.0], [219.796875, 161.2880859375], [95.25, 161.2880859375]]}, {"title": "21.5.1 Example: VB for a univariate Gaussian", "heading_level": null, "page_id": 11, "polygon": [[90.0, 282.75], [314.25, 282.75], [314.25, 292.9921875], [90.0, 292.9921875]]}, {"title": "21.5.1.1 Target distribution", "heading_level": null, "page_id": 12, "polygon": [[85.5, 60.75], [212.25, 60.75], [212.25, 71.46826171875], [85.5, 71.46826171875]]}, {"title": "21.5.1.2 Updating q_\\mu(\\mu)", "heading_level": null, "page_id": 12, "polygon": [[84.75, 182.25], [200.25, 182.25], [200.25, 195.0], [84.75, 195.0]]}, {"title": "21.5.1.3 Updating q_\\lambda(\\lambda)", "heading_level": null, "page_id": 12, "polygon": [[84.75, 365.25], [199.6875, 365.25], [199.6875, 376.5], [84.75, 376.5]]}, {"title": "21.5.1.4 Computing the expectations", "heading_level": null, "page_id": 13, "polygon": [[84.515625, 62.25], [251.25, 62.25], [251.25, 71.5078125], [84.515625, 71.5078125]]}, {"title": "21.5.1.5 Illustration", "heading_level": null, "page_id": 13, "polygon": [[85.359375, 406.5], [179.296875, 406.5], [179.296875, 416.70703125], [85.359375, 416.70703125]]}, {"title": "21.5.1.6 Lower bound *", "heading_level": null, "page_id": 13, "polygon": [[84.75, 510.75], [194.25, 510.75], [194.25, 520.171875], [84.75, 520.171875]]}, {"title": "21.5.2 Example: VB for linear regression", "heading_level": null, "page_id": 15, "polygon": [[87.890625, 543.0], [291.75, 543.0], [291.75, 554.02734375], [87.890625, 554.02734375]]}, {"title": "21.6 Variational Bayes EM", "heading_level": null, "page_id": 18, "polygon": [[95.25, 495.75], [239.25, 495.75], [239.25, 506.8828125], [95.25, 506.8828125]]}, {"title": "21.6.1 Example: VBEM for mixtures of Gaussians *", "heading_level": null, "page_id": 19, "polygon": [[90.0, 422.25], [338.34375, 422.25], [338.34375, 432.52734375], [90.0, 432.52734375]]}, {"title": "21.6.1.1 The variational posterior", "heading_level": null, "page_id": 19, "polygon": [[86.25, 526.5], [237.75, 526.5], [237.75, 535.9921875], [86.25, 535.9921875]]}, {"title": "21.6.1.2 Derivation of q(z) (variational E step)", "heading_level": null, "page_id": 20, "polygon": [[84.75, 364.5], [294.0, 364.5], [294.0, 374.625], [84.75, 374.625]]}, {"title": "21.6.1.3 Derivation of q(\\theta) (variational M step)", "heading_level": null, "page_id": 21, "polygon": [[84.75, 336.75], [299.25, 336.75], [299.25, 348.99609375], [84.75, 348.99609375]]}, {"title": "21.6.1.4 Lower bound on the marginal likelihood", "heading_level": null, "page_id": 22, "polygon": [[84.75, 485.25], [306.0, 485.25], [306.0, 495.0], [84.75, 495.0]]}, {"title": "21.6.1.5 Posterior predictive distribution", "heading_level": null, "page_id": 23, "polygon": [[84.515625, 62.25], [269.25, 62.25], [269.25, 71.2705078125], [84.515625, 71.2705078125]]}, {"title": "21.6.1.6 Model selection using VBEM", "heading_level": null, "page_id": 23, "polygon": [[84.0, 317.25], [253.5, 317.25], [253.5, 327.1640625], [84.0, 327.1640625]]}, {"title": "21.6.1.7 Automatic sparsity inducing effects of VBEM", "heading_level": null, "page_id": 23, "polygon": [[84.75, 486.75], [322.59375, 486.75], [322.59375, 497.07421875], [84.75, 497.07421875]]}, {"title": "21.7 Variational message passing and VIBES", "heading_level": null, "page_id": 25, "polygon": [[96.0, 126.0], [333.0, 126.0], [333.0, 137.00390625], [96.0, 137.00390625]]}, {"title": "21.8 Local variational bounds *", "heading_level": null, "page_id": 25, "polygon": [[95.25, 344.25], [267.75, 342.75], [267.75, 355.32421875], [95.25, 355.32421875]]}, {"title": "21.8.1 Motivating applications", "heading_level": null, "page_id": 25, "polygon": [[90.0, 465.0], [241.171875, 465.0], [241.171875, 475.2421875], [90.0, 475.2421875]]}, {"title": "21.8.1.1 Variational logistic regression", "heading_level": null, "page_id": 25, "polygon": [[86.25, 521.25], [260.15625, 521.25], [260.15625, 531.24609375], [86.25, 531.24609375]]}, {"title": "21.8.1.2 Multi-task learning", "heading_level": null, "page_id": 26, "polygon": [[84.75, 276.75], [213.0, 276.75], [213.0, 287.138671875], [84.75, 287.138671875]]}, {"title": "21.8.1.3 Discrete factor analysis", "heading_level": null, "page_id": 26, "polygon": [[84.75, 381.0], [230.625, 381.0], [230.625, 391.39453125], [84.75, 391.39453125]]}, {"title": "21.8.1.4 Correlated topic model", "heading_level": null, "page_id": 26, "polygon": [[84.75, 485.25], [230.25, 485.25], [230.25, 495.4921875], [84.75, 495.4921875]]}, {"title": "21.8.2 <PERSON><PERSON><PERSON>'s quadratic bound to the log-sum-exp function", "heading_level": null, "page_id": 27, "polygon": [[86.765625, 61.5], [394.3125, 61.5], [394.3125, 71.7451171875], [86.765625, 71.7451171875]]}, {"title": "21.8.2.1 Applying <PERSON><PERSON><PERSON>'s bound to multinomial logistic regression", "heading_level": null, "page_id": 28, "polygon": [[84.09375, 62.25], [391.5, 62.25], [391.5, 71.666015625], [84.09375, 71.666015625]]}, {"title": "21.8.3 Bounds for the sigmoid function", "heading_level": null, "page_id": 29, "polygon": [[87.8203125, 559.5], [285.0, 559.5], [285.0, 569.53125], [87.8203125, 569.53125]]}, {"title": "21.8.4 Other bounds and approximations to the log-sum-exp function *", "heading_level": null, "page_id": 31, "polygon": [[89.015625, 429.75], [435.75, 429.75], [435.75, 439.5], [89.015625, 439.5]]}, {"title": "21.8.4.1 Product of sigmoids", "heading_level": null, "page_id": 31, "polygon": [[85.5, 498.75], [217.96875, 498.75], [217.96875, 508.1484375], [85.5, 508.1484375]]}, {"title": "21.8.4.2 <PERSON>'s inequality", "heading_level": null, "page_id": 32, "polygon": [[82.96875, 61.5], [212.34375, 61.5], [212.34375, 71.5078125], [82.96875, 71.5078125]]}, {"title": "21.8.4.3 Multivariate delta method", "heading_level": null, "page_id": 32, "polygon": [[83.390625, 233.25], [241.5, 233.25], [241.5, 243.158203125], [83.390625, 243.158203125]]}, {"title": "21.8.5 Variational inference based on upper bounds", "heading_level": null, "page_id": 32, "polygon": [[88.3125, 445.5], [343.5, 445.5], [343.5, 455.30859375], [88.3125, 455.30859375]]}, {"title": "Exercises", "heading_level": null, "page_id": 33, "polygon": [[129.0, 60.75], [178.734375, 60.75], [178.734375, 72.18017578125], [129.0, 72.18017578125]]}, {"title": "Exercise 21.2 La<PERSON> approximation to normal-gamma", "heading_level": null, "page_id": 33, "polygon": [[128.8828125, 122.25], [331.5, 122.25], [331.5, 131.8623046875], [128.8828125, 131.8623046875]]}, {"title": "Exercise 21.4 Variational lower bound for VB for GMMs", "heading_level": null, "page_id": 33, "polygon": [[129.0, 396.75], [333.0, 396.75], [333.0, 406.58203125], [129.0, 406.58203125]]}, {"title": "Exercise 21.7 Forwards vs reverse KL divergence", "heading_level": null, "page_id": 35, "polygon": [[129.75, 62.25], [306.0, 62.25], [306.0, 71.982421875], [129.75, 71.982421875]]}, {"title": "Exercise 21.10 VB for binary FA with probit link", "heading_level": null, "page_id": 35, "polygon": [[129.75, 398.25], [305.25, 398.25], [305.25, 407.53125], [129.75, 407.53125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 31], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10076, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 467], ["Line", 51], ["Equation", 9], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 41], ["Text", 7], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 54], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1535, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 55], ["Equation", 6], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 46], ["TableCell", 32], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 548], ["Line", 82], ["Equation", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1160, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 64], ["Equation", 11], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 87], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6942, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 50], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 913, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["Line", 104], ["Equation", 9], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3214, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 35], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 73], ["Equation", 7], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 55], ["Equation", 8], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 78], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2191, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 647], ["Line", 84], ["Equation", 11], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4544, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 74], ["Equation", 12], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 625], ["Line", 113], ["Equation", 8], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1433, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 985, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 49], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 65], ["Equation", 8], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1138, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["Line", 80], ["Equation", 11], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1196, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 56], ["Equation", 8], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 750, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 54], ["Equation", 6], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 77], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Equation", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 37], ["SectionHeader", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 45], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 613], ["Line", 64], ["Equation", 10], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 658], ["Line", 93], ["Equation", 7], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3384, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 657], ["Line", 62], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 66], ["Equation", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 938, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 74], ["Equation", 6], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 77], ["Equation", 7], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5133, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 47], ["Equation", 6], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 628], ["Line", 85], ["Equation", 12], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 38], ["TableCell", 30], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-25"}