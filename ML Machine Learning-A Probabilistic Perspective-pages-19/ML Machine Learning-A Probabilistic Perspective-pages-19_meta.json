{"table_of_contents": [{"title": "15 Gaussian processes", "heading_level": null, "page_id": 0, "polygon": [[66.0, 96.029296875], [272.25, 96.029296875], [270.0, 151.5], [64.5, 142.857421875]]}, {"title": "15.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 218.162109375], [96.75, 218.162109375]]}, {"title": "15.2 GPs for regression", "heading_level": null, "page_id": 1, "polygon": [[95.25, 357.75], [225.84375, 357.75], [225.84375, 368.9296875], [95.25, 368.9296875]]}, {"title": "15.2.1 Predictions using noise-free observations", "heading_level": null, "page_id": 2, "polygon": [[90.0, 252.0], [326.25, 252.0], [326.25, 261.984375], [90.0, 261.984375]]}, {"title": "15.2.2 Predictions using noisy observations", "heading_level": null, "page_id": 3, "polygon": [[89.0859375, 195.75], [304.3125, 195.75], [304.3125, 205.822265625], [89.0859375, 205.822265625]]}, {"title": "15.2.3 Effect of the kernel parameters", "heading_level": null, "page_id": 4, "polygon": [[88.453125, 390.75], [276.75, 390.75], [276.75, 401.51953125], [88.453125, 401.51953125]]}, {"title": "15.2.4 Estimating the kernel parameters", "heading_level": null, "page_id": 6, "polygon": [[89.2265625, 61.5], [288.0, 61.5], [288.0, 71.78466796875], [89.2265625, 71.78466796875]]}, {"title": "******** Example", "heading_level": null, "page_id": 6, "polygon": [[85.078125, 518.25], [168.0, 518.25], [168.0, 527.765625], [85.078125, 527.765625]]}, {"title": "******** Bayesian inference for the hyper-parameters", "heading_level": null, "page_id": 8, "polygon": [[84.0, 379.5], [322.5, 379.5], [322.5, 389.8125], [84.0, 389.8125]]}, {"title": "******** Multiple kernel learning", "heading_level": null, "page_id": 9, "polygon": [[84.0, 62.25], [234.28125, 62.25], [234.28125, 71.62646484375], [84.0, 71.62646484375]]}, {"title": "15.2.5 Computational and numerical issues *", "heading_level": null, "page_id": 9, "polygon": [[88.3125, 166.5], [311.25, 166.5], [311.25, 176.5546875], [88.3125, 176.5546875]]}, {"title": "15.2.6 Semi-parametric GPs *", "heading_level": null, "page_id": 9, "polygon": [[87.75, 480.75], [236.25, 480.75], [236.25, 490.74609375], [87.75, 490.74609375]]}, {"title": "15.3 GPs meet GLMs", "heading_level": null, "page_id": 10, "polygon": [[95.25, 353.25], [211.21875, 353.25], [211.21875, 364.18359375], [95.25, 364.18359375]]}, {"title": "15.3.1 Binary classification", "heading_level": null, "page_id": 10, "polygon": [[90.0, 474.0], [226.5, 473.25], [226.5, 484.41796875], [90.0, 484.41796875]]}, {"title": "******** Computing the posterior", "heading_level": null, "page_id": 10, "polygon": [[87.0, 543.0], [237.75, 543.0], [237.75, 552.12890625], [87.0, 552.12890625]]}, {"title": "15.3.1.2 Computing the posterior predictive", "heading_level": null, "page_id": 11, "polygon": [[84.75, 260.25], [282.75, 260.25], [282.75, 270.2109375], [84.75, 270.2109375]]}, {"title": "15.3.1.3 Computing the marginal likelihood", "heading_level": null, "page_id": 12, "polygon": [[84.75, 200.25], [282.75, 200.25], [282.75, 210.09375], [84.75, 210.09375]]}, {"title": "15.3.1.4 Numerically stable computation *", "heading_level": null, "page_id": 12, "polygon": [[84.75, 375.75], [274.5, 375.75], [274.5, 385.69921875], [84.75, 385.69921875]]}, {"title": "15.3.1.5 Example", "heading_level": null, "page_id": 13, "polygon": [[84.75, 401.25], [168.75, 401.25], [168.75, 410.37890625], [84.75, 410.37890625]]}, {"title": "15.3.2 Multi-class classification", "heading_level": null, "page_id": 13, "polygon": [[89.25, 492.75], [245.8125, 492.75], [245.8125, 503.71875], [89.25, 503.71875]]}, {"title": "15.3.2.1 Computing the posterior", "heading_level": null, "page_id": 15, "polygon": [[84.515625, 61.5], [238.5, 61.5], [238.5, 71.54736328125], [84.515625, 71.54736328125]]}, {"title": "15.3.2.2 Computing the posterior predictive", "heading_level": null, "page_id": 15, "polygon": [[83.25, 367.5], [283.5, 367.5], [283.5, 377.47265625], [83.25, 377.47265625]]}, {"title": "15.3.2.3 Computing the marginal likelihood", "heading_level": null, "page_id": 16, "polygon": [[84.0, 173.25], [282.375, 173.25], [282.375, 183.515625], [84.0, 183.515625]]}, {"title": "15.3.2.4 Numerical and computational issues", "heading_level": null, "page_id": 16, "polygon": [[84.0, 275.25], [288.0, 275.25], [288.0, 285.240234375], [84.0, 285.240234375]]}, {"title": "15.3.3 GPs for Poisson regression", "heading_level": null, "page_id": 16, "polygon": [[89.25, 345.0], [257.25, 345.0], [257.25, 355.0078125], [89.25, 355.0078125]]}, {"title": "15.4 Connection with other methods", "heading_level": null, "page_id": 17, "polygon": [[95.25, 263.25], [295.5, 263.25], [295.5, 274.482421875], [95.25, 274.482421875]]}, {"title": "15.4.1 Linear models compared to GPs", "heading_level": null, "page_id": 17, "polygon": [[90.75, 324.75], [280.5, 324.75], [280.5, 334.44140625], [90.75, 334.44140625]]}, {"title": "15.4.2 Linear smoothers compared to GPs", "heading_level": null, "page_id": 18, "polygon": [[89.25, 100.5], [295.59375, 100.5], [295.59375, 110.42578125], [89.25, 110.42578125]]}, {"title": "15.4.2.1 Degrees of freedom of linear smoothers", "heading_level": null, "page_id": 18, "polygon": [[84.9375, 444.75], [303.75, 444.75], [303.75, 454.9921875], [84.9375, 454.9921875]]}, {"title": "15.4.3 SVMs compared to GPs", "heading_level": null, "page_id": 19, "polygon": [[88.5, 188.25], [238.5, 187.5], [238.5, 198.38671875], [89.15625, 198.38671875]]}, {"title": "15.4.4 L1VM and RVMs compared to GPs", "heading_level": null, "page_id": 19, "polygon": [[89.25, 512.25], [288.0, 512.25], [288.0, 522.703125], [89.25, 522.703125]]}, {"title": "15.4.5 Neural networks compared to GPs", "heading_level": null, "page_id": 20, "polygon": [[89.25, 184.5], [291.0, 184.5], [291.0, 194.431640625], [89.25, 194.431640625]]}, {"title": "15.4.6 Smoothing splines compared to GPs *", "heading_level": null, "page_id": 21, "polygon": [[88.9453125, 472.5], [307.96875, 472.5], [307.96875, 482.51953125], [88.9453125, 482.51953125]]}, {"title": "15.4.6.1 Univariate splines", "heading_level": null, "page_id": 21, "polygon": [[84.75, 541.5], [208.265625, 541.5], [208.265625, 550.86328125], [84.75, 550.86328125]]}, {"title": "15.4.6.2 Regression splines", "heading_level": null, "page_id": 22, "polygon": [[83.25, 360.75], [210.75, 360.75], [210.75, 369.87890625], [83.25, 369.87890625]]}, {"title": "15.4.6.3 The connection with GPs", "heading_level": null, "page_id": 22, "polygon": [[83.8828125, 534.75], [238.21875, 534.75], [238.21875, 544.8515625], [83.8828125, 544.8515625]]}, {"title": "15.4.6.4 2d input (thin-plate splines)", "heading_level": null, "page_id": 23, "polygon": [[83.953125, 175.5], [250.5, 175.5], [250.5, 185.73046875], [83.953125, 185.73046875]]}, {"title": "15.4.6.5 Higher-dimensional inputs", "heading_level": null, "page_id": 23, "polygon": [[84.0, 354.75], [246.0, 354.75], [246.0, 364.81640625], [84.0, 364.81640625]]}, {"title": "15.4.7 RKHS methods compared to GPs *", "heading_level": null, "page_id": 23, "polygon": [[88.875, 559.5], [291.0, 559.5], [291.0, 569.21484375], [88.875, 569.21484375]]}, {"title": "15.5 GP latent variable model", "heading_level": null, "page_id": 25, "polygon": [[95.25, 214.5], [259.5, 214.5], [259.5, 225.28125], [95.25, 225.28125]]}, {"title": "15.6 Approximation methods for large datasets", "heading_level": null, "page_id": 27, "polygon": [[94.5, 101.25], [348.75, 101.25], [348.75, 113.115234375], [94.5, 113.115234375]]}, {"title": "Exercises", "heading_level": null, "page_id": 27, "polygon": [[129.0, 188.25], [179.25, 188.25], [179.25, 198.861328125], [129.0, 198.861328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 31], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 34], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 755, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 64], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 58], ["Equation", 8], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 43], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 778, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 66], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 757, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["Line", 65], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 43], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 57], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 43], ["TextInlineMath", 5], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 591], ["Line", 57], ["TableCell", 18], ["Equation", 6], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5368, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 636], ["Line", 56], ["Equation", 14], ["Text", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 528], ["Line", 51], ["Equation", 9], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["Line", 55], ["TextInlineMath", 6], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TextInlineMath", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 911, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 57], ["Equation", 10], ["Text", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4252, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 46], ["Text", 6], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 412], ["Line", 68], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 816, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 50], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 62], ["Equation", 5], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 460], ["Line", 50], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 58], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 77], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 57], ["Text", 6], ["Equation", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 72], ["Text", 9], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 57], ["Equation", 7], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 59], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 11], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-19"}