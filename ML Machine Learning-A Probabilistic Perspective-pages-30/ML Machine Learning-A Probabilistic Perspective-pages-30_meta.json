{"table_of_contents": [{"title": "26 Graphical model structure learning", "heading_level": null, "page_id": 0, "polygon": [[49.921875, 93.65625], [393.0, 93.65625], [392.25, 153.75], [49.921875, 144.439453125]]}, {"title": "26.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[95.25, 207.0], [195.75, 207.0], [195.75, 218.00390625], [95.25, 218.00390625]]}, {"title": "26.2 Structure learning for knowledge discovery", "heading_level": null, "page_id": 1, "polygon": [[93.0, 279.0], [354.375, 279.0], [354.375, 290.302734375], [93.0, 290.302734375]]}, {"title": "26.2.1 Relevance networks", "heading_level": null, "page_id": 1, "polygon": [[88.5, 399.75], [224.25, 399.75], [224.25, 410.0625], [88.5, 410.0625]]}, {"title": "26.2.2 Dependency networks", "heading_level": null, "page_id": 2, "polygon": [[87.0, 473.25], [234.0, 473.25], [234.0, 483.78515625], [87.0, 483.78515625]]}, {"title": "26.3 Learning tree structures", "heading_level": null, "page_id": 3, "polygon": [[93.0, 473.25], [255.9375, 473.25], [255.9375, 483.78515625], [93.0, 483.78515625]]}, {"title": "26.3.1 Directed or undirected tree?", "heading_level": null, "page_id": 4, "polygon": [[88.5, 192.0], [264.0, 192.0], [264.0, 203.1328125], [88.5, 203.1328125]]}, {"title": "26.3.2 <PERSON><PERSON> algorithm for finding the ML tree structure", "heading_level": null, "page_id": 5, "polygon": [[86.34375, 61.5], [382.5, 61.5], [382.5, 71.46826171875], [86.34375, 71.46826171875]]}, {"title": "26.3.3 Finding the MAP forest", "heading_level": null, "page_id": 5, "polygon": [[86.25, 499.5], [240.0, 499.5], [240.0, 509.73046875], [86.25, 509.73046875]]}, {"title": "26.3.4 Mixtures of trees", "heading_level": null, "page_id": 7, "polygon": [[86.34375, 212.25], [212.0625, 212.25], [212.0625, 222.275390625], [86.34375, 222.275390625]]}, {"title": "26.4 Learning DAG structures", "heading_level": null, "page_id": 7, "polygon": [[93.0, 378.75], [257.25, 378.75], [257.25, 390.12890625], [93.0, 390.12890625]]}, {"title": "26.4.1 <PERSON><PERSON> equivalence", "heading_level": null, "page_id": 7, "polygon": [[88.734375, 560.0390625], [225.0, 560.0390625], [225.0, 569.53125], [88.734375, 569.53125]]}, {"title": "26.4.2 Exact structural inference", "heading_level": null, "page_id": 9, "polygon": [[85.78125, 181.5], [253.5, 180.75], [253.5, 191.7421875], [85.78125, 191.7421875]]}, {"title": "26.4.2.1 Deriving the likelihood", "heading_level": null, "page_id": 9, "polygon": [[83.25, 237.75], [230.34375, 237.75], [230.34375, 247.587890625], [83.25, 247.587890625]]}, {"title": "26.4.2.2 Deriving the marginal likelihood", "heading_level": null, "page_id": 9, "polygon": [[81.75, 465.75], [272.25, 465.75], [272.25, 475.875], [81.75, 475.875]]}, {"title": "26.4.2.3 Setting the prior", "heading_level": null, "page_id": 10, "polygon": [[81.0703125, 447.75], [203.25, 447.75], [203.25, 458.15625], [81.0703125, 458.15625]]}, {"title": "26.4.2.4 Simple worked example", "heading_level": null, "page_id": 11, "polygon": [[81.703125, 242.25], [234.0, 242.25], [234.0, 252.650390625], [81.703125, 252.650390625]]}, {"title": "26.4.2.5 Example: analysis of the college plans dataset", "heading_level": null, "page_id": 11, "polygon": [[81.75, 526.5], [330.75, 526.5], [330.75, 536.30859375], [81.75, 536.30859375]]}, {"title": "26.4.2.6 The K2 algorithm", "heading_level": null, "page_id": 12, "polygon": [[81.5625, 531.75], [206.15625, 531.75], [206.15625, 541.6875], [81.5625, 541.6875]]}, {"title": "26.4.2.7 Handling non-tabular CPDs", "heading_level": null, "page_id": 13, "polygon": [[81.75, 111.0], [249.75, 111.0], [249.75, 120.8671875], [81.75, 120.8671875]]}, {"title": "26.4.3 Scaling up to larger graphs", "heading_level": null, "page_id": 13, "polygon": [[86.6953125, 271.5], [259.5, 271.5], [259.5, 282.076171875], [86.6953125, 282.076171875]]}, {"title": "26.4.3.1 Approximating the mode of the posterior", "heading_level": null, "page_id": 13, "polygon": [[83.8125, 417.75], [309.0, 417.75], [309.0, 428.09765625], [83.8125, 428.09765625]]}, {"title": "26.4.3.2 Approximating other functions of the posterior", "heading_level": null, "page_id": 14, "polygon": [[81.75, 517.5], [334.5, 517.5], [334.5, 528.08203125], [81.75, 528.08203125]]}, {"title": "26.5 Learning DAG structure with latent variables", "heading_level": null, "page_id": 15, "polygon": [[93.0, 208.5], [362.25, 208.5], [362.25, 219.744140625], [93.0, 219.744140625]]}, {"title": "26.5.1 Approximating the marginal likelihood when we have missing data", "heading_level": null, "page_id": 15, "polygon": [[88.3125, 424.5], [447.75, 424.5], [447.75, 434.7421875], [88.3125, 434.7421875]]}, {"title": "26.5.1.1 BIC approximation", "heading_level": null, "page_id": 15, "polygon": [[84.75, 504.0], [212.34375, 504.0], [212.34375, 514.16015625], [84.75, 514.16015625]]}, {"title": "26.5.1.2 <PERSON><PERSON><PERSON>-<PERSON><PERSON> approximation", "heading_level": null, "page_id": 16, "polygon": [[82.7578125, 61.5], [271.5, 61.5], [271.5, 71.62646484375], [82.7578125, 71.62646484375]]}, {"title": "26.5.1.3 Variational Bayes EM", "heading_level": null, "page_id": 16, "polygon": [[83.8125, 429.0], [222.1875, 429.0], [222.1875, 438.5390625], [83.8125, 438.5390625]]}, {"title": "26.5.1.4 Example: college plans revisited", "heading_level": null, "page_id": 17, "polygon": [[82.96875, 275.25], [270.0, 275.25], [270.0, 285.240234375], [82.96875, 285.240234375]]}, {"title": "26.5.2 Structural EM", "heading_level": null, "page_id": 18, "polygon": [[87.0, 100.5], [198.421875, 100.5], [198.421875, 110.583984375], [87.0, 110.583984375]]}, {"title": "26.5.3 Discovering hidden variables", "heading_level": null, "page_id": 19, "polygon": [[87.0, 375.75], [267.0, 375.75], [267.0, 385.69921875], [87.0, 385.69921875]]}, {"title": "26.5.4 Case study: Google's Rephil", "heading_level": null, "page_id": 21, "polygon": [[86.9765625, 349.5], [261.0, 349.5], [261.0, 359.75390625], [86.9765625, 359.75390625]]}, {"title": "26.5.5 Structural equation models *", "heading_level": null, "page_id": 22, "polygon": [[86.90625, 327.75], [267.0, 327.75], [267.0, 337.60546875], [86.90625, 337.60546875]]}, {"title": "26.6 Learning causal DAGs", "heading_level": null, "page_id": 24, "polygon": [[92.6015625, 234.0], [243.0, 234.0], [243.0, 244.265625], [92.6015625, 244.265625]]}, {"title": "26.6.1 Causal interpretation of DAGs", "heading_level": null, "page_id": 24, "polygon": [[88.171875, 414.75], [270.0, 414.75], [270.0, 424.93359375], [88.171875, 424.93359375]]}, {"title": "26.6.2 Using causal DAGs to resolve <PERSON>'s paradox", "heading_level": null, "page_id": 26, "polygon": [[86.0625, 325.5], [358.5, 325.5], [358.5, 335.390625], [86.0625, 335.390625]]}, {"title": "26.6.3 Learning causal DAG structures", "heading_level": null, "page_id": 28, "polygon": [[86.25, 571.5], [279.75, 571.5], [279.75, 581.87109375], [86.25, 581.87109375]]}, {"title": "26.6.3.1 Learning from observational data", "heading_level": null, "page_id": 29, "polygon": [[82.40625, 62.25], [274.5, 62.25], [274.5, 71.86376953125], [82.40625, 71.86376953125]]}, {"title": "26.6.3.2 Learning from interventional data", "heading_level": null, "page_id": 29, "polygon": [[80.6484375, 489.0], [276.75, 489.0], [276.75, 498.33984375], [80.6484375, 498.33984375]]}, {"title": "26.7 Learning undirected Gaussian graphical models", "heading_level": null, "page_id": 31, "polygon": [[93.0, 358.5], [378.0, 358.5], [378.0, 369.87890625], [93.0, 369.87890625]]}, {"title": "26.7.1 MLE for a GGM", "heading_level": null, "page_id": 31, "polygon": [[89.15625, 504.0], [203.34375, 504.0], [203.34375, 513.84375], [89.15625, 513.84375]]}, {"title": "26.7.2 Graphical lasso", "heading_level": null, "page_id": 32, "polygon": [[87.6796875, 505.5], [204.1875, 505.5], [204.1875, 515.7421875], [87.6796875, 515.7421875]]}, {"title": "26.7.3 Bayesian inference for GGM structure *", "heading_level": null, "page_id": 34, "polygon": [[87.75, 88.5], [315.0, 88.5], [315.0, 98.71875], [87.75, 98.71875]]}, {"title": "26.7.4 Handling non-Gaussian data using copulas *", "heading_level": null, "page_id": 35, "polygon": [[87.609375, 61.5], [339.75, 61.5], [339.75, 71.666015625], [87.609375, 71.666015625]]}, {"title": "26.8 Learning undirected discrete graphical models", "heading_level": null, "page_id": 35, "polygon": [[93.0, 228.0], [372.0, 228.0], [372.0, 239.203125], [93.0, 239.203125]]}, {"title": "26.8.1 Graphical lasso for MRFs/CRFs", "heading_level": null, "page_id": 35, "polygon": [[87.75, 349.5], [275.25, 349.5], [275.25, 359.75390625], [87.75, 359.75390625]]}, {"title": "26.8.2 Thin junction trees", "heading_level": null, "page_id": 37, "polygon": [[86.25, 290.25], [220.78125, 290.25], [220.78125, 300.427734375], [86.25, 300.427734375]]}, {"title": "Exercises", "heading_level": null, "page_id": 37, "polygon": [[129.7265625, 444.75], [178.5, 444.75], [178.5, 455.625], [129.7265625, 455.625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 33], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10118, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 57], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 739, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 103], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 37], ["Text", 6], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 47], ["Equation", 8], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 724, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 56], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1088, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 108], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1139, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 39], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 43], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 728, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 85], ["Equation", 5], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 70], ["Equation", 8], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["Line", 42], ["TableCell", 36], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2373, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 39], ["ListItem", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 673, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 47], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Footnote", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 110], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 44], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 45], ["Equation", 7], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 111], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 43], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 73], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 811, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 116], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 30], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 45], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 88], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3378, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 42], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 29], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 659, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["TableCell", 121], ["Line", 31], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Table", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 39], ["Equation", 11], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 35], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 50], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 880, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 42], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 494], ["Line", 70], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 8878, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 53], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 772, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 85], ["SectionHeader", 3], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2863, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 88], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 913, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 39], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1041, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-30"}