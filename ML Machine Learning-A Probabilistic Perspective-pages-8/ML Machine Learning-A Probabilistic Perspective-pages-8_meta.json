{"table_of_contents": [{"title": "6 Frequentist statistics", "heading_level": null, "page_id": 0, "polygon": [[84.515625, 97.294921875], [280.5, 97.294921875], [280.5, 143.173828125], [84.515625, 143.173828125]]}, {"title": "6.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 207.0], [195.75, 207.0], [195.75, 218.162109375], [101.25, 218.162109375]]}, {"title": "6.2 Sampling distribution of an estimator", "heading_level": null, "page_id": 0, "polygon": [[99.0, 483.75], [326.25, 483.75], [326.25, 495.80859375], [99.0, 495.80859375]]}, {"title": "6.2.1 Bootstrap", "heading_level": null, "page_id": 1, "polygon": [[93.75, 360.0], [176.25, 360.0], [176.25, 370.1953125], [93.75, 370.1953125]]}, {"title": "6.2.2 Large sample theory for the MLE *", "heading_level": null, "page_id": 2, "polygon": [[92.25, 183.75], [294.0, 183.75], [294.0, 193.95703125], [92.25, 193.95703125]]}, {"title": "6.3 Frequentist decision theory", "heading_level": null, "page_id": 3, "polygon": [[99.0, 443.25], [273.0, 443.25], [273.0, 454.359375], [99.0, 454.359375]]}, {"title": "6.3.1 Bay<PERSON> risk", "heading_level": null, "page_id": 4, "polygon": [[93.75, 258.0], [179.296875, 258.0], [179.296875, 268.154296875], [93.75, 268.154296875]]}, {"title": "6.3.2 Minimax risk", "heading_level": null, "page_id": 5, "polygon": [[92.25, 475.5], [194.25, 475.5], [194.25, 485.68359375], [92.25, 485.68359375]]}, {"title": "6.3.3 Admissible estimators", "heading_level": null, "page_id": 6, "polygon": [[92.25, 160.5], [234.0, 160.5], [234.0, 170.701171875], [92.25, 170.701171875]]}, {"title": "6.3.3.1 Example", "heading_level": null, "page_id": 6, "polygon": [[88.5, 264.75], [168.75, 264.75], [168.75, 274.482421875], [88.5, 274.482421875]]}, {"title": "6.3.3.2 <PERSON>'s paradox *", "heading_level": null, "page_id": 8, "polygon": [[86.34375, 171.0], [202.5, 171.0], [202.5, 180.984375], [86.34375, 180.984375]]}, {"title": "6.3.3.3 Admissibility is not enough", "heading_level": null, "page_id": 8, "polygon": [[86.5546875, 548.25], [249.0, 548.25], [249.0, 557.82421875], [86.5546875, 557.82421875]]}, {"title": "6.4 Desirable properties of estimators", "heading_level": null, "page_id": 9, "polygon": [[99.0, 222.75], [306.75, 222.75], [306.75, 233.666015625], [99.0, 233.666015625]]}, {"title": "6.4.1 Consistent estimators", "heading_level": null, "page_id": 9, "polygon": [[94.5, 307.5], [232.875, 307.5], [232.875, 317.671875], [94.5, 317.671875]]}, {"title": "6.4.2 Unbiased estimators", "heading_level": null, "page_id": 9, "polygon": [[92.390625, 440.25], [227.109375, 440.25], [227.109375, 450.5625], [92.390625, 450.5625]]}, {"title": "6.4.3 Minimum variance estimators", "heading_level": null, "page_id": 10, "polygon": [[93.0, 317.25], [271.5, 317.25], [271.5, 327.48046875], [93.0, 327.48046875]]}, {"title": "6.4.4 The bias-variance tradeoff", "heading_level": null, "page_id": 11, "polygon": [[92.7421875, 60.75], [255.75, 60.75], [255.75, 71.54736328125], [92.7421875, 71.54736328125]]}, {"title": "6.4.4.1 Example: estimating a Gaussian mean", "heading_level": null, "page_id": 11, "polygon": [[89.25, 389.25], [296.25, 389.25], [296.25, 399.62109375], [89.25, 399.62109375]]}, {"title": "6.4.4.2 Example: ridge regression", "heading_level": null, "page_id": 12, "polygon": [[87.75, 411.75], [243.84375, 411.75], [243.84375, 422.0859375], [87.75, 422.0859375]]}, {"title": "6.4.4.3 Bias-variance tradeoff for classification", "heading_level": null, "page_id": 12, "polygon": [[87.46875, 539.25], [300.75, 539.25], [300.75, 549.28125], [87.46875, 549.28125]]}, {"title": "6.5 Empirical risk minimization", "heading_level": null, "page_id": 13, "polygon": [[99.0, 490.5], [276.75, 490.5], [276.75, 501.50390625], [99.0, 501.50390625]]}, {"title": "6.5.1 Regularized risk minimization", "heading_level": null, "page_id": 14, "polygon": [[94.5, 480.75], [274.5, 480.75], [274.5, 490.74609375], [94.5, 490.74609375]]}, {"title": "6.5.2 Structural risk minimization", "heading_level": null, "page_id": 15, "polygon": [[92.25, 172.5], [264.75, 172.5], [264.75, 182.56640625], [92.25, 182.56640625]]}, {"title": "6.5.3 Estimating the risk using cross validation", "heading_level": null, "page_id": 15, "polygon": [[92.6015625, 358.5], [326.8125, 358.5], [326.8125, 368.61328125], [92.6015625, 368.61328125]]}, {"title": "6.5.3.1 Example: using CV to pick λ for ridge regression", "heading_level": null, "page_id": 16, "polygon": [[88.2421875, 336.75], [343.5, 336.75], [343.5, 346.1484375], [88.2421875, 346.1484375]]}, {"title": "6.5.3.2 The one standard error rule", "heading_level": null, "page_id": 17, "polygon": [[86.203125, 312.0], [250.5, 312.0], [250.5, 321.943359375], [86.203125, 321.943359375]]}, {"title": "6.5.3.3 CV for model selection in non-probabilistic unsupervised learning", "heading_level": null, "page_id": 18, "polygon": [[86.203125, 62.25], [418.5, 62.25], [418.5, 71.982421875], [86.203125, 71.982421875]]}, {"title": "6.5.4 Upper bounding the risk using statistical learning theory *", "heading_level": null, "page_id": 18, "polygon": [[92.109375, 166.5], [408.75, 166.5], [408.75, 176.5546875], [92.109375, 176.5546875]]}, {"title": "6.5.5 Surrogate loss functions", "heading_level": null, "page_id": 19, "polygon": [[93.0, 395.25], [245.25, 395.25], [245.25, 405.6328125], [93.0, 405.6328125]]}, {"title": "6.6 Pathologies of frequentist statistics *", "heading_level": null, "page_id": 20, "polygon": [[99.0, 469.5], [320.25, 469.5], [320.25, 481.25390625], [99.0, 481.25390625]]}, {"title": "6.6.1 Counter-intuitive behavior of confidence intervals", "heading_level": null, "page_id": 21, "polygon": [[92.8125, 60.75], [366.0, 60.75], [366.0, 71.38916015625], [92.8125, 71.38916015625]]}, {"title": "6.6.2 p-values considered harmful", "heading_level": null, "page_id": 22, "polygon": [[91.5, 61.5], [266.25, 61.5], [266.25, 71.62646484375], [91.5, 71.62646484375]]}, {"title": "6.6.3 The likelihood principle", "heading_level": null, "page_id": 23, "polygon": [[91.5, 469.5], [243.0, 469.5], [243.0, 479.671875], [91.5, 479.671875]]}, {"title": "6.6.4 Why isn't everyone a Bayesian?", "heading_level": null, "page_id": 24, "polygon": [[91.1953125, 219.75], [278.71875, 219.75], [278.71875, 229.869140625], [91.1953125, 229.869140625]]}, {"title": "Exercises", "heading_level": null, "page_id": 24, "polygon": [[129.75, 490.5], [178.5, 490.5], [178.5, 500.5546875], [129.75, 500.5546875]]}, {"title": "Exercise 6.1 Pessimism of LOOCV", "heading_level": null, "page_id": 24, "polygon": [[129.75, 510.75], [254.25, 510.75], [254.25, 519.85546875], [129.75, 519.85546875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 30], ["Text", 4], ["SectionHeader", 3], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6121, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 504], ["Line", 50], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 983, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 52], ["Text", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 51], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 65], ["Text", 8], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 169], ["Line", 33], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 44], ["Text", 4], ["ListItem", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 81], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1326, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 57], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 52], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 62], ["Text", 9], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 68], ["Equation", 9], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 55], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 961, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1009, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 448], ["Line", 54], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 38], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 79], ["Text", 5], ["Equation", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 65], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 824, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 56], ["TextInlineMath", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 41], ["Text", 8], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 37], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 44], ["TextInlineMath", 6], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 54], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 52], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 41], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 19], ["Text", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-8"}