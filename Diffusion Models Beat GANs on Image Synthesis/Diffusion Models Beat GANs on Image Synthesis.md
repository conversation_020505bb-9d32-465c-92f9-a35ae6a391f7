# **Diffusion Models Beat GANs on Image Synthesis**

<PERSON><PERSON><PERSON><PERSON><sup>∗</sup> OpenAI <EMAIL>

Alex <PERSON><sup>∗</sup> OpenAI <EMAIL>

## Abstract

We show that diffusion models can achieve image sample quality superior to the current state-of-the-art generative models. We achieve this on unconditional image synthesis by finding a better architecture through a series of ablations. For conditional image synthesis, we further improve sample quality with classifier guidance: a simple, compute-efficient method for trading off diversity for fidelity using gradients from a classifier. We achieve an FID of 2.97 on ImageNet  $128\times128$ , 4.59 on ImageNet  $256\times256$ , and 7.72 on ImageNet  $512\times512$ , and we match BigGAN-deep even with as few as 25 forward passes per sample, all while maintaining better coverage of the distribution. Finally, we find that classifier guidance combines well with upsampling diffusion models, further improving FID to 3.94 on ImageNet 256×256 and 3.85 on ImageNet 512×512. We release our code at <https://github.com/openai/guided-diffusion>.

## 1 Introduction

Image /page/0/Picture/6 description: This is a grid of 16 images, each featuring a different animal or object. The animals include an ostrich, flamingo, corgi dog, goldfish, golden retriever, bird, white wolf, snow leopard, monarch butterfly, chimpanzee, panda, red panda, husky dog, and a spider. There are also images of a tennis ball and a vintage white pickup truck. The images are arranged in four rows and four columns.

Figure 1: Selected samples from our best ImageNet  $512 \times 512$  model (FID 3.85)

Over the past few years, generative models have gained the ability to generate human-like natural language [\[6\]](#page-12-0), infinite high-quality synthetic images [\[5,](#page-12-1) [28,](#page-13-0) [51\]](#page-14-0) and highly diverse human speech and music [\[64,](#page-15-0) [13\]](#page-13-1). These models can be used in a variety of ways, such as generating images from text prompts [\[72,](#page-15-1) [50\]](#page-14-1) or learning useful feature representations [\[14,](#page-13-2) [7\]](#page-12-2). While these models are already

<sup>∗</sup>Equal contribution

capable of producing realistic images and sound, there is still much room for improvement beyond the current state-of-the-art, and better generative models could have wide-ranging impacts on graphic design, games, music production, and countless other fields.

GANs [\[19\]](#page-13-3) currently hold the state-of-the-art on most image generation tasks [\[5,](#page-12-1) [68,](#page-15-2) [28\]](#page-13-0) as measured by sample quality metrics such as FID [\[23\]](#page-13-4), Inception Score [\[54\]](#page-15-3) and Precision [\[32\]](#page-13-5). However, some of these metrics do not fully capture diversity, and it has been shown that GANs capture less diversity than state-of-the-art likelihood-based models [\[51,](#page-14-0) [43,](#page-14-2) [42\]](#page-14-3). Furthermore, GANs are often difficult to train, collapsing without carefully selected hyperparameters and regularizers [\[5,](#page-12-1) [41,](#page-14-4) [4\]](#page-12-3).

While GANs hold the state-of-the-art, their drawbacks make them difficult to scale and apply to new domains. As a result, much work has been done to achieve GAN-like sample quality with likelihood-based models [\[51,](#page-14-0) [25,](#page-13-6) [42,](#page-14-3) [9\]](#page-12-4). While these models capture more diversity and are typically easier to scale and train than GANs, they still fall short in terms of visual sample quality. Furthermore, except for VAEs, sampling from these models is slower than GANs in terms of wall-clock time.

Diffusion models are a class of likelihood-based models which have recently been shown to produce high-quality images [\[56,](#page-15-4) [59,](#page-15-5) [25\]](#page-13-6) while offering desirable properties such as distribution coverage, a stationary training objective, and easy scalability. These models generate samples by gradually removing noise from a signal, and their training objective can be expressed as a reweighted variational lower-bound [\[25\]](#page-13-6). This class of models already holds the state-of-the-art [\[60\]](#page-15-6) on CIFAR-10 [\[31\]](#page-13-7), but still lags behind GANs on difficult generation datasets like LSUN and ImageNet. [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) found that these models improve reliably with increased compute, and can produce high-quality samples even on the difficult ImageNet  $256 \times 256$  dataset using an upsampling stack. However, the FID of this model is still not competitive with BigGAN-deep [\[5\]](#page-12-1), the current state-of-the-art on this dataset.

We hypothesize that the gap between diffusion models and GANs stems from at least two factors: first, that the model architectures used by recent GAN literature have been heavily explored and refined; second, that GANs are able to trade off diversity for fidelity, producing high quality samples but not covering the whole distribution. We aim to bring these benefits to diffusion models, first by improving model architecture and then by devising a scheme for trading off diversity for fidelity. With these improvements, we achieve a new state-of-the-art, surpassing GANs on several different metrics and datasets.

The rest of the paper is organized as follows. In Section [2,](#page-1-0) we give a brief background of diffusion models based on [Ho et al.](#page-13-6) [\[25\]](#page-13-6) and the improvements from [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) and [Song](#page-15-7) [et al.](#page-15-7) [\[57\]](#page-15-7), and we describe our evaluation setup. In Section [3,](#page-3-0) we introduce simple architecture improvements that give a substantial boost to FID. In Section [4,](#page-5-0) we describe a method for using gradients from a classifier to guide a diffusion model during sampling. We find that a single hyperparameter, the scale of the classifier gradients, can be tuned to trade off diversity for fidelity, and we can increase this gradient scale factor by an order of magnitude without obtaining adversarial examples [\[61\]](#page-15-8). Finally, in Section [5](#page-8-0) we show that models with our improved architecture achieve state-of-the-art on unconditional image synthesis tasks, and with classifier guidance achieve state-ofthe-art on conditional image synthesis. When using classifier guidance, we find that we can sample with as few as 25 forward passes while maintaining FIDs comparable to BigGAN. We also compare our improved models to upsampling stacks, finding that the two approaches give complementary improvements and that combining them gives the best results on ImageNet  $256\times256$  and  $512\times512$ .

## <span id="page-1-0"></span>2 Background

In this section, we provide a brief overview of diffusion models. For a more detailed mathematical description, we refer the reader to Appendix [B.](#page-18-0)

On a high level, diffusion models sample from a distribution by reversing a gradual noising process. In particular, sampling starts with noise  $x_T$  and produces gradually less-noisy samples  $x_{T-1}, x_{T-2}, ...$ until reaching a final sample  $x_0$ . Each timestep t corresponds to a certain noise level, and  $x_t$  can be thought of as a mixture of a signal  $x_0$  with some noise  $\epsilon$  where the signal to noise ratio is determined by the timestep t. For the remainder of this paper, we assume that the noise  $\epsilon$  is drawn from a diagonal Gaussian distribution, which works well for natural images and simplifies various derivations.

A diffusion model learns to produce a slightly more "denoised"  $x_{t-1}$  from  $x_t$ . [Ho et al.](#page-13-6) [\[25\]](#page-13-6) parameterize this model as a function  $\epsilon_{\theta}(x_t, t)$  which predicts the noise component of a noisy sample  $x_t$ . To train these models, each sample in a minibatch is produced by randomly drawing a data sample  $x_0$ , a timestep t, and noise  $\epsilon$ , which together give rise to a noised sample  $x_t$  (Equation [17\)](#page-18-1). The training objective is then  $||\epsilon_{\theta}(x_t, t) - \epsilon||^2$ , i.e. a simple mean-squared error loss between the true noise and the predicted noise (Equation [26\)](#page-18-2).

It is not immediately obvious how to sample from a noise predictor  $\epsilon_{\theta}(x_t, t)$ . Recall that diffusion sampling proceeds by repeatedly predicting  $x_{t-1}$  from  $x_t$ , starting from  $x_T$ . [Ho et al.](#page-13-6) [\[25\]](#page-13-6) show that, under reasonable assumptions, we can model the distribution  $p_{\theta}(x_{t-1}|x_t)$  of  $x_{t-1}$  given  $x_t$  as a diagonal Gaussian  $\mathcal{N}(x_{t-1}; \mu_\theta(x_t, t), \Sigma_\theta(x_t, t))$ , where the mean  $\mu_\theta(x_t, t)$  can be calculated as a function of  $\epsilon_{\theta}(x_t, t)$  (Equation [27\)](#page-18-3). The variance  $\Sigma_{\theta}(x_t, t)$  of this Gaussian distribution can be fixed to a known constant [\[25\]](#page-13-6) or learned with a separate neural network head [\[43\]](#page-14-2), and both approaches yield high-quality samples when the total number of diffusion steps  $T$  is large enough.

[Ho et al.](#page-13-6) [\[25\]](#page-13-6) observe that the simple mean-sqaured error objective,  $L_{\text{simple}}$ , works better in practice than the actual variational lower bound  $L_{\text{vlb}}$  that can be derived from interpreting the denoising diffusion model as a VAE. They also note that training with this objective and using their corresponding sampling procedure is equivalent to the denoising score matching model from [Song and Ermon](#page-15-9) [\[58\]](#page-15-9), who use Langevin dynamics to sample from a denoising model trained with multiple noise levels to produce high quality image samples. We often use "diffusion models" as shorthand to refer to both classes of models.

### 2.1 Improvements

Following the breakthrough work of [Song and Ermon](#page-15-9) [\[58\]](#page-15-9) and [Ho et al.](#page-13-6) [\[25\]](#page-13-6), several recent papers have proposed improvements to diffusion models. Here we describe a few of these improvements, which we employ for our models.

[Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) find that fixing the variance  $\Sigma_{\theta}(x_t, t)$  to a constant as done in [Ho et al.](#page-13-6) [\[25\]](#page-13-6) is sub-optimal for sampling with fewer diffusion steps, and propose to parameterize  $\Sigma_{\theta}(x_t, t)$  as a neural network whose output  $v$  is interpolated as:

$$
\Sigma_{\theta}(x_t, t) = \exp(v \log \beta_t + (1 - v) \log \tilde{\beta}_t)
$$
\n(1)

Here,  $\beta_t$  and  $\tilde{\beta}_t$  (Equation [19\)](#page-18-4) are the variances in [Ho et al.](#page-13-6) [\[25\]](#page-13-6) corresponding to upper and lower bounds for the reverse process variances. Additionally, [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) propose a hybrid objective for training both  $\epsilon_\theta(x_t, t)$  and  $\Sigma_\theta(x_t, t)$  using the weighted sum  $L_{simple} + \lambda L_{vlb}$ . Learning the reverse process variances with their hybrid objective allows sampling with fewer steps without much drop in sample quality. We adopt this objective and parameterization, and use it throughout our experiments.

[Song et al.](#page-15-7) [\[57\]](#page-15-7) propose DDIM, which formulates an alternative non-Markovian noising process that has the same forward marginals as DDPM, but allows producing different reverse samplers by changing the variance of the reverse noise. By setting this noise to 0, they provide a way to turn any model  $\epsilon_{\theta}(x_t, t)$  into a deterministic mapping from latents to images, and find that this provides an alternative way to sample with fewer steps. We adopt this sampling approach when using fewer than 50 sampling steps, since [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) found it to be beneficial in this regime.

### 2.2 Sample Quality Metrics

For comparing sample quality across models, we perform quantitative evaluations using the following metrics. While these metrics are often used in practice and correspond well with human judgement, they are not a perfect proxy, and finding better metrics for sample quality evaluation is still an open problem.

Inception Score (IS) was proposed by [Salimans et al.](#page-15-3) [\[54\]](#page-15-3), and it measures how well a model captures the full ImageNet class distribution while still producing individual samples that are convincing examples of a single class. One drawback of this metric is that it does not reward covering the whole distribution or capturing diversity within a class, and models which memorize a small subset of the full dataset will still have high IS [\[3\]](#page-12-5). To better capture diversity than IS, Fréchet Inception Distance (FID) was proposed by [Heusel et al.](#page-13-4) [\[23\]](#page-13-4), who argued that it is more consistent with human

<span id="page-3-1"></span>

| Channels | Depth | Heads | Attention<br>resolutions | BigGAN<br>up/downsample | Rescale<br>resblock | FID<br>700K | FID<br>1200K |
|----------|-------|-------|--------------------------|-------------------------|---------------------|-------------|--------------|
| 160      | 2     | 1     | 16                       | $\times$                | $\times$            | 15.33       | 13.21        |
| 128      | 4     |       |                          |                         |                     | $-0.21$     | $-0.48$      |
|          |       | 4     | 32,16,8                  | $\checkmark$            |                     | $-0.54$     | $-0.82$      |
|          |       |       |                          |                         | $\checkmark$        | $-0.72$     | $-0.66$      |
|          |       |       |                          |                         |                     | $-1.20$     | $-1.21$      |
| 160      | 2     | 4     | 32,16,8                  | $\checkmark$            | $\times$            | -3.14       | -3.00        |

Table 1: Ablation of various architecture changes, evaluated at 700K and 1200K iterations

judgement than Inception Score. FID provides a symmetric measure of the distance between two image distributions in the Inception-V3 [\[62\]](#page-15-10) latent space. Recently, sFID was proposed by [Nash](#page-14-3) [et al.](#page-14-3) [\[42\]](#page-14-3) as a version of FID that uses spatial features rather than the standard pooled features. They find that this metric better captures spatial relationships, rewarding image distributions with coherent high-level structure. Finally, [Kynkäänniemi et al.](#page-13-5) [\[32\]](#page-13-5) proposed Improved Precision and Recall metrics to separately measure sample fidelity as the fraction of model samples which fall into the data manifold (precision), and diversity as the fraction of data samples which fall into the sample manifold (recall).

We use FID as our default metric for overall sample quality comparisons as it captures both diversity and fidelity and has been the de facto standard metric for state-of-the-art generative modeling work [\[27,](#page-13-8) [28,](#page-13-0) [5,](#page-12-1) [25\]](#page-13-6). We use Precision or IS to measure fidelity, and Recall to measure diversity or distribution coverage. When comparing against other methods, we re-compute these metrics using public samples or models whenever possible. This is for two reasons: first, some papers [\[27,](#page-13-8) [28,](#page-13-0) [25\]](#page-13-6) compare against arbitrary subsets of the training set which are not readily available; and second, subtle implementation differences can affect the resulting FID values [\[45\]](#page-14-5). To ensure consistent comparisons, we use the entire training set as the reference batch [\[23,](#page-13-4) [5\]](#page-12-1), and evaluate metrics for all models using the same codebase.

<span id="page-3-0"></span>

### 3 Architecture Improvements

In this section we conduct several architecture ablations to find the model architecture that provides the best sample quality for diffusion models.

[Ho et al.](#page-13-6) [\[25\]](#page-13-6) introduced the UNet architecture for diffusion models, which [Jolicoeur-Martineau](#page-13-9) [et al.](#page-13-9) [\[26\]](#page-13-9) found to substantially improve sample quality over the previous architectures [\[58,](#page-15-9) [33\]](#page-13-10) used for denoising score matching. The UNet model uses a stack of residual layers and downsampling convolutions, followed by a stack of residual layers with upsampling colvolutions, with skip connections connecting the layers with the same spatial size. In addition, they use a global attention layer at the  $16\times16$  resolution with a single head, and add a projection of the timestep embedding into each residual block. [Song et al.](#page-15-6) [\[60\]](#page-15-6) found that further changes to the UNet architecture improved performance on the CIFAR-10 [\[31\]](#page-13-7) and CelebA-64 [\[34\]](#page-14-6) datasets. We show the same result on ImageNet 128×128, finding that architecture can indeed give a substantial boost to sample quality on much larger and more diverse datasets at a higher resolution.

We explore the following architectural changes:

- Increasing depth versus width, holding model size relatively constant.
- Increasing the number of attention heads.
- Using attention at 32×32, 16×16, and 8×8 resolutions rather than only at 16×16.
- Using the BigGAN [\[5\]](#page-12-1) residual block for upsampling and downsampling the activations, following [\[60\]](#page-15-6).
- Rescaling residual connections with  $\frac{1}{\sqrt{2}}$  $\frac{1}{2}$ , following [\[60,](#page-15-6) [27,](#page-13-8) [28\]](#page-13-0).

For all comparisons in this section, we train models on ImageNet  $128 \times 128$  with batch size 256, and sample using 250 sampling steps. We train models with the above architecture changes and compare

| Number of heads | Channels per head | <b>FID</b> |
|-----------------|-------------------|------------|
| 1               |                   | 14.08      |
| 2               |                   | -0.50      |
| 4               |                   | -0.97      |
| 8               |                   | -1.17      |
|                 | 32                | -1.36      |
|                 | 64                | -1.03      |
|                 | 128               | -1.08      |

<span id="page-4-1"></span>Table 2: Ablation of various attention configurations. More heads or lower channels per heads both lead to improved FID.

<span id="page-4-0"></span>Image /page/4/Figure/2 description: The image contains two line graphs side-by-side, both plotting FID (Fidelity) on the y-axis against time (in hours) on the x-axis. The left graph shows several lines representing different configurations: 'ch=128, res=4', 'ch=160, res=2', 'ch=160, res=2, heads=4', 'ch=160, res=2, multi-res attn', 'ch=160, res=2, biggan up/down', 'ch=160, res=2, skip rescale', and 'ch=160, res=2, heads=4, multi-res attn/down'. The FID values range from approximately 14 to 26.5, and the time ranges from 30 to 180 hours. The right graph shows lines representing different numbers of attention heads and head channels: '1 head', '2 heads', '4 heads', '8 heads', '32 head channels', '64 head channels', and '128 head channels'. The FID values in this graph range from approximately 15.5 to 27.5, and the time ranges from 15 to 105 hours. Both graphs show a general downward trend, indicating that FID decreases over time, suggesting improvement in the model's performance.

<span id="page-4-2"></span>Figure 2: Ablation of various architecture changes, showing FID as a function of wall-clock time. FID evaluated over 10k samples instead of 50k for efficiency.

| Operation            | FID   |
|----------------------|-------|
| AdaGN                | 13.06 |
| Addition + GroupNorm | 15.08 |

Table 3: Ablating the element-wise operation used when projecting timestep and class embeddings into each residual block. Replacing AdaGN with the Addition + GroupNorm layer from [Ho et al.](#page-13-6) [\[25\]](#page-13-6) makes FID worse.

them on FID, evaluated at two different points of training, in Table [1.](#page-3-1) Aside from rescaling residual connections, all of the other modifications improve performance and have a positive compounding effect. We observe in Figure [2](#page-4-0) that while increased depth helps performance, it increases training time and takes longer to reach the same performance as a wider model, so we opt not to use this change in further experiments.

We also study other attention configurations that better match the Transformer architecture [\[66\]](#page-15-11). To this end, we experimented with either fixing attention heads to a constant, or fixing the number of channels per head. For the rest of the architecture, we use 128 base channels, 2 residual blocks per resolution, multi-resolution attention, and BigGAN up/downsampling, and we train the models for 700K iterations. Table [2](#page-4-1) shows our results, indicating that more heads or fewer channels per head improves FID. In Figure [2,](#page-4-0) we see 64 channels is best for wall-clock time, so we opt to use 64 channels per head as our default. We note that this choice also better matches modern transformer architectures, and is on par with our other configurations in terms of final FID.

<span id="page-5-1"></span>

### 3.1 Adaptive Group Normalization

We also experiment with a layer [\[43\]](#page-14-2) that we refer to as adaptive group normalization (AdaGN), which incorporates the timestep and class embedding into each residual block after a group normalization operation [\[69\]](#page-15-12), similar to adaptive instance norm [\[27\]](#page-13-8) and FiLM [\[48\]](#page-14-7). We define this layer as AdaGN(h, y) = y<sub>s</sub> GroupNorm(h) + y<sub>b</sub>, where h is the intermediate activations of the residual block following the first convolution, and  $y = [y_s, y_b]$  is obtained from a linear projection of the timestep and class embedding.

We had already seen AdaGN improve our earliest diffusion models, and so had it included by default in all our runs. In Table [3,](#page-4-2) we explicitly ablate this choice, and find that the adaptive group normalization layer indeed improved FID. Both models use 128 base channels and 2 residual blocks per resolution, multi-resolution attention with 64 channels per head, and BigGAN up/downsampling, and were trained for 700K iterations.

In the rest of the paper, we use this final improved model architecture as our default: variable width with 2 residual blocks per resolution, multiple heads with 64 channels per head, attention at 32, 16 and 8 resolutions, BigGAN residual blocks for up and downsampling, and adaptive group normalization for injecting timestep and class embeddings into residual blocks.

<span id="page-5-0"></span>

### 4 Classifier Guidance

In addition to employing well designed architectures, GANs for conditional image synthesis [\[39,](#page-14-8) [5\]](#page-12-1) make heavy use of class labels. This often takes the form of class-conditional normalization statistics [\[16,](#page-13-11) [11\]](#page-12-6) as well as discriminators with heads that are explicitly designed to behave like classifiers  $p(y|x)$  [\[40\]](#page-14-9). As further evidence that class information is crucial to the success of these models, [Lucic et al.](#page-14-10) [\[36\]](#page-14-10) find that it is helpful to generate synthetic labels when working in a label-limited regime.

Given this observation for GANs, it makes sense to explore different ways to condition diffusion models on class labels. We already incorporate class information into normalization layers (Section [3.1\)](#page-5-1). Here, we explore a different approach: exploiting a classifier  $p(y|x)$  to improve a diffusion generator. [Sohl-Dickstein et al.](#page-15-4) [\[56\]](#page-15-4) and [Song et al.](#page-15-6) [\[60\]](#page-15-6) show one way to achieve this, wherein a pre-trained diffusion model can be conditioned using the gradients of a classifier. In particular, we can train a classifier  $p_{\phi}(y|x_t, t)$  on noisy images  $x_t$ , and then use gradients  $\nabla_{x_t} \log p_{\phi}(y|x_t, t)$  to guide the diffusion sampling process towards an arbitrary class label y.

In this section, we first review two ways of deriving conditional sampling processes using classifiers. We then describe how we use such classifiers in practice to improve sample quality. We choose the notation  $p_{\phi}(y|x_t, t) = p_{\phi}(y|x_t)$  and  $\epsilon_{\theta}(x_t, t) = \epsilon_{\theta}(x_t)$  for brevity, noting that they refer to separate functions for each timestep  $t$  and at training time the models must be conditioned on the input  $t$ .

### 4.1 Conditional Reverse Noising Process

We start with a diffusion model with an unconditional reverse noising process  $p_{\theta}(x_t|x_{t+1})$ . To condition this on a label  $y$ , it suffices to sample each transition<sup>[2](#page-5-2)</sup> according to

<span id="page-5-3"></span>
$$
p_{\theta,\phi}(x_t|x_{t+1},y) = Zp_{\theta}(x_t|x_{t+1})p_{\phi}(y|x_t)
$$
\n(2)

where  $Z$  is a normalizing constant (proof in Appendix [H\)](#page-24-0). It is typically intractable to sample from this distribution exactly, but [Sohl-Dickstein et al.](#page-15-4) [\[56\]](#page-15-4) show that it can be approximated as a perturbed Gaussian distribution. Here, we review this derivation.

Recall that our diffusion model predicts the previous timestep  $x_t$  from timestep  $x_{t+1}$  using a Gaussian distribution:

$$
p_{\theta}(x_t|x_{t+1}) = \mathcal{N}(\mu, \Sigma) \tag{3}
$$

$$
\log p_{\theta}(x_t|x_{t+1}) = -\frac{1}{2}(x_t - \mu)^T \Sigma^{-1} (x_t - \mu) + C \tag{4}
$$

<span id="page-5-2"></span><sup>&</sup>lt;sup>2</sup>We must also sample  $x_T$  conditioned on y, but a noisy enough diffusion process causes  $x_T$  to be nearly Gaussian even in the conditional case.

<span id="page-6-0"></span>**Algorithm 1** Classifier guided diffusion sampling, given a diffusion model  $(\mu_{\theta}(x_t), \Sigma_{\theta}(x_t))$ , classifier  $p_{\phi}(y|x_t)$ , and gradient scale s.

Input: class label y, gradient scale s  $x_T \leftarrow$  sample from  $\mathcal{N}(0, I)$ for all  $t$  from  $T$  to 1 do  $\mu, \Sigma \leftarrow \mu_{\theta}(x_t), \Sigma_{\theta}(x_t)$  $x_{t-1} \leftarrow$  sample from  $\mathcal{N}(\mu + s \Sigma \nabla_{x_t} \log p_{\phi}(y|x_t), \Sigma)$ end for return  $x_0$ 

<span id="page-6-1"></span>**Algorithm 2** Classifier guided DDIM sampling, given a diffusion model  $\epsilon_{\theta}(x_t)$ , classifier  $p_{\phi}(y|x_t)$ , and gradient scale s.

Input: class label y, gradient scale s  $x_T \leftarrow$  sample from  $\mathcal{N}(0, I)$ for all  $t$  from  $T$  to 1 do  $\hat{\epsilon} \leftarrow \epsilon_{\theta}(x_t) - \sqrt{1 - \bar{\alpha}_t} \, \nabla_{x_t} \log p_{\phi}(y|x_t)$  $x_{t-1} \leftarrow \sqrt{\bar{\alpha}_{t-1}} \left( \frac{x_t - \sqrt{\bar{\alpha}_{t-1}}}{\sqrt{\bar{\alpha}_{t-1}}} \right)$  $\frac{\sqrt{1-\bar{\alpha}_t}\hat{\epsilon}}{\sqrt{1-\bar{\alpha}_t}}$  $\frac{\frac{1-\overline{\alpha}_{t} \epsilon}{1-\overline{\alpha}_{t} \epsilon}}{\overline{\alpha}_{t}}$  +  $\sqrt{1-\overline{\alpha}_{t-1}} \hat{\epsilon}$ end for return  $x_0$ 

We can assume that  $\log_{\phi} p(y|x_t)$  has low curvature compared to  $\Sigma^{-1}$ . This assumption is reasonable in the limit of infinite diffusion steps, where  $||\Sigma|| \to 0$ . In this case, we can approximate  $\log p_{\phi}(y|x_t)$ using a Taylor expansion around  $x_t = \mu$  as

$$
\log p_{\phi}(y|x_t) \approx \log p_{\phi}(y|x_t)|_{x_t=\mu} + (x_t - \mu) \nabla_{x_t} \log p_{\phi}(y|x_t)|_{x_t=\mu} \tag{5}
$$

$$
=(x_t - \mu)g + C_1 \tag{6}
$$

Here,  $g = \nabla_{x_t} \log p_{\phi}(y|x_t)|_{x_t=\mu}$ , and  $C_1$  is a constant. This gives

$$
\log(p_{\theta}(x_t|x_{t+1})p_{\phi}(y|x_t)) \approx -\frac{1}{2}(x_t - \mu)^T \Sigma^{-1} (x_t - \mu) + (x_t - \mu)g + C_2
$$
\n(7)

$$
= -\frac{1}{2}(x_t - \mu - \Sigma g)^T \Sigma^{-1} (x_t - \mu - \Sigma g) + \frac{1}{2}g^T \Sigma g + C_2 \quad (8)
$$

$$
= -\frac{1}{2}(x_t - \mu - \Sigma g)^T \Sigma^{-1} (x_t - \mu - \Sigma g) + C_3
$$
\n(9)

<span id="page-6-2"></span>
$$
= \log p(z) + C_4, z \sim \mathcal{N}(\mu + \Sigma g, \Sigma)
$$
\n(10)

We can safely ignore the constant term  $C_4$ , since it corresponds to the normalizing coefficient Z in Equation [2.](#page-5-3) We have thus found that the conditional transition operator can be approximated by a Gaussian similar to the unconditional transition operator, but with its mean shifted by  $\Sigma g$ . Algorithm [1](#page-6-0) summaries the corresponding sampling algorithm. We include an optional scale factor s for the gradients, which we describe in more detail in Section [4.3.](#page-7-0)

<span id="page-6-3"></span>

### 4.2 Conditional Sampling for DDIM

The above derivation for conditional sampling is only valid for the stochastic diffusion sampling process, and cannot be applied to deterministic sampling methods like DDIM [\[57\]](#page-15-7). To this end, we use a score-based conditioning trick adapted from [Song et al.](#page-15-6) [\[60\]](#page-15-6), which leverages the connection between diffusion models and score matching [\[59\]](#page-15-5). In particular, if we have a model  $\epsilon_{\theta}(x_t)$  that predicts the noise added to a sample, then this can be used to derive a score function:

$$
\nabla_{x_t} \log p_\theta(x_t) = -\frac{1}{\sqrt{1 - \bar{\alpha}_t}} \epsilon_\theta(x_t)
$$
\n(11)

<span id="page-7-1"></span>Image /page/7/Picture/0 description: The image is a collage of twelve photographs of dogs. The top row of the left side shows a fluffy dog with brown and white fur looking at the camera. Next to it is a fluffy orange cat with a long tail. The bottom left photo shows a dog lying in the snow. The bottom right photo shows the head of a dog with large ears and blue eyes. The top row of the right side shows a corgi with tan and white fur looking at the camera. Next to it is another corgi with tan and white fur. The next photo is of a corgi lying on grass. The bottom row of the right side shows a corgi lying down. Next to it is a close-up of a dog's face with its eyes narrowed. The next photo is of a dog standing. The last photo is of a dog with tan and white fur sitting down.

Figure 3: Samples from an unconditional diffusion model with classifier guidance to condition on the class "Pembroke Welsh corgi". Using classifier scale 1.0 (left; FID: 33.0) does not produce convincing samples in this class, whereas classifier scale 10.0 (right; FID: 12.0) produces much more class-consistent images.

We can now substitute this into the score function for  $p(x_t)p(y|x_t)$ :

$$
\nabla_{x_t} \log(p_\theta(x_t) p_\phi(y|x_t)) = \nabla_{x_t} \log p_\theta(x_t) + \nabla_{x_t} \log p_\phi(y|x_t)
$$
\n(12)

$$
= -\frac{1}{\sqrt{1-\bar{\alpha}_t}} \epsilon_{\theta}(x_t) + \nabla_{x_t} \log p_{\phi}(y|x_t)
$$
\n(13)

Finally, we can define a new epsilon prediction  $\hat{\epsilon}(x_t)$  which corresponds to the score of the joint distribution: √

$$
\hat{\epsilon}(x_t) \coloneqq \epsilon_\theta(x_t) - \sqrt{1 - \bar{\alpha}_t} \, \nabla_{x_t} \log p_\phi(y|x_t) \tag{14}
$$

We can then use the exact same sampling procedure as used for regular DDIM, but with the modified noise predictions  $\hat{\epsilon}_{\theta}(x_t)$  instead of  $\epsilon_{\theta}(x_t)$ . Algorithm [2](#page-6-1) summaries the corresponding sampling algorithm.

<span id="page-7-0"></span>

### 4.3 Scaling Classifier Gradients

To apply classifier guidance to a large scale generative task, we train classification models on ImageNet. Our classifier architecture is simply the downsampling trunk of the UNet model with an attention pool [\[49\]](#page-14-11) at the 8x8 layer to produce the final output. We train these classifiers on the same noising distribution as the corresponding diffusion model, and also add random crops to reduce overfitting. After training, we incorporate the classifier into the sampling process of the diffusion model using Equation [10,](#page-6-2) as outlined by Algorithm [1.](#page-6-0)

In initial experiments with unconditional ImageNet models, we found it necessary to scale the classifier gradients by a constant factor larger than 1. When using a scale of 1, we observed that the classifier assigned reasonable probabilities (around 50%) to the desired classes for the final samples, but these samples did not match the intended classes upon visual inspection. Scaling up the classifier gradients remedied this problem, and the class probabilities from the classifier increased to nearly 100%. Figure [3](#page-7-1) shows an example of this effect.

To understand the effect of scaling classifier gradients, note that  $s \cdot \nabla_x \log p(y|x) = \nabla_x \log \frac{1}{Z} p(y|x)^s$ , where  $Z$  is an arbitrary constant. As a result, the conditioning process is still theoretically grounded in a re-normalized classifier distribution proportional to  $p(y|x)^s$ . When  $s > 1$ , this distribution becomes sharper than  $p(y|x)$ , since larger values are amplified by the exponent. In other words, using a larger gradient scale focuses more on the modes of the classifier, which is potentially desirable for producing higher fidelity (but less diverse) samples.

In the above derivations, we assumed that the underlying diffusion model was unconditional, modeling  $p(x)$ . It is also possible to train conditional diffusion models,  $p(x|y)$ , and use classifier guidance in the exact same way. Table [4](#page-8-1) shows that the sample quality of both unconditional and conditional models can be greatly improved by classifier guidance. We see that, with a high enough scale, the guided unconditional model can get quite close to the FID of an unguided conditional model, although training directly with the class labels still helps. Guiding a conditional model further improves FID.

Table [4](#page-8-1) also shows that classifier guidance improves precision at the cost of recall, thus introducing a trade-off in sample fidelity versus diversity. We explicitly evaluate how this trade-off varies with

<span id="page-8-1"></span>

| Conditional | Guidance | Scale | FID          | sFID        | IS            | Precision   | Recall      |
|-------------|----------|-------|--------------|-------------|---------------|-------------|-------------|
| x           | x        |       | 26.21        | <b>6.35</b> | 39.70         | 0.61        | 0.63        |
| x           | ✓        | 1.0   | 33.03        | 6.99        | 32.92         | 0.56        | <b>0.65</b> |
| x           | ✓        | 10.0  | <b>12.00</b> | 10.40       | <b>95.41</b>  | <b>0.76</b> | 0.44        |
| ✓           | x        |       | 10.94        | 6.02        | 100.98        | 0.69        | <b>0.63</b> |
| ✓           | ✓        | 1.0   | <b>4.59</b>  | <b>5.25</b> | 186.70        | 0.82        | 0.52        |
| ✓           | ✓        | 10.0  | 9.11         | 10.93       | <b>283.92</b> | <b>0.88</b> | 0.32        |

Table 4: Effect of classifier guidance on sample quality. Both conditional and unconditional models were trained for 2M iterations on ImageNet 256×256 with batch size 256.

<span id="page-8-2"></span>Image /page/8/Figure/2 description: The image contains three line graphs plotted against 'gradient scale' on the x-axis. The first graph shows two lines, 'FID' (blue) and 'sFID' (orange), with FID values ranging from approximately 3.5 to 12.5 and sFID values ranging from approximately 5 to 15. The second graph shows a single line labeled 'IS' (blue) with values ranging from approximately 90 to 260. The third graph shows two lines, 'precision' (blue) and 'recall' (orange). Precision values range from approximately 0.7 to 0.9, while recall values range from approximately 0.7 to 0.3. The y-axis scales are different for each graph, with the first graph ranging from 4 to 16, the second from 100 to 300, and the third from 0.3 to 0.9.

Figure 4: Change in sample quality as we vary scale of the classifier gradients for a class-conditional ImageNet 128×128 model.

<span id="page-8-3"></span>Image /page/8/Figure/4 description: The image contains two plots. The left plot shows Precision on the x-axis and Recall on the y-axis. It displays two lines: a blue line labeled "BigGAN-deep" and an orange line labeled "Classifier guidance (ours)". The blue line starts at approximately (0.7, 0.65) and decreases to approximately (0.95, 0.05). The orange line starts at approximately (0.7, 0.65) and decreases to approximately (0.95, 0.05), generally staying above the blue line. The right plot shows IS on the x-axis and FID on the y-axis. It also displays two lines: a blue line labeled "BigGAN-deep" and an orange line labeled "Classifier guidance (ours)". The blue line starts at approximately (145, 6) and increases to approximately (275, 28). The orange line starts at approximately (95, 5.5) and decreases to approximately (150, 4), then increases to approximately (275, 12).

Figure 5: Trade-offs when varying truncation for BigGAN-deep and gradient scale for classifier guidance. Models are evaluated on ImageNet 128×128. The BigGAN-deep results were produced using the TFHub model [\[12\]](#page-12-7) at truncation levels  $[0.1, 0.2, 0.3, ..., 1.0]$ .

the gradient scale in Figure [4.](#page-8-2) We see that scaling the gradients beyond 1.0 smoothly trades off recall (a measure of diversity) for higher precision and IS (measures of fidelity). Since FID and sFID depend on both diversity and fidelity, their best values are obtained at an intermediate point. We also compare our guidance with the truncation trick from BigGAN in Figure [5.](#page-8-3) We find that classifier guidance is strictly better than BigGAN-deep when trading off FID for Inception Score. Less clear cut is the precision/recall trade-off, which shows that classifier guidance is only a better choice up until a certain precision threshold, after which point it cannot achieve better precision.

## <span id="page-8-0"></span>5 Results

To evaluate our improved model architecture on unconditional image generation, we train separate diffusion models on three LSUN [\[71\]](#page-15-13) classes: bedroom, horse, and cat. To evaluate classifier guidance, we train conditional diffusion models on the ImageNet [\[52\]](#page-14-12) dataset at  $128 \times 128$ ,  $256 \times 256$ , and  $512\times512$  resolution.

<span id="page-9-0"></span>

| Model                                 | FID  | sFID | Prec | Rec  | Model                          | <b>FID</b> | sFID  | Prec | Rec  |
|---------------------------------------|------|------|------|------|--------------------------------|------------|-------|------|------|
| <b>LSUN Bedrooms</b> $256 \times 256$ |      |      |      |      | ImageNet $128\times128$        |            |       |      |      |
| DCTransformer <sup>†</sup> [42]       | 6.40 | 6.66 | 0.44 | 0.56 | BigGAN-deep [5]                | 6.02       | 7.18  | 0.86 | 0.35 |
| <b>DDPM [25]</b>                      | 4.89 | 9.07 | 0.60 | 0.45 | LOGAN <sup>†</sup> [68]        | 3.36       |       |      |      |
| IDDPM $[43]$                          | 4.24 | 8.21 | 0.62 | 0.46 | <b>ADM</b>                     | 5.91       | 5.09  | 0.70 | 0.65 |
| StyleGAN [27]                         | 2.35 | 6.62 | 0.59 | 0.48 | ADM-G (25 steps)               | 5.98       | 7.04  | 0.78 | 0.51 |
| ADM (dropout)                         | 1.90 | 5.59 | 0.66 | 0.51 | <b>ADM-G</b>                   | 2.97       | 5.09  | 0.78 | 0.59 |
| LSUN Horses $256\times256$            |      |      |      |      | ImageNet $256\times256$        |            |       |      |      |
|                                       |      |      |      |      |                                |            |       |      |      |
| StyleGAN2 [28]                        | 3.84 | 6.46 | 0.63 | 0.48 | $DCTransformer$ [42]           | 36.51      | 8.24  | 0.36 | 0.67 |
| <b>ADM</b>                            | 2.95 | 5.94 | 0.69 | 0.55 | $VO$ -VAE-2 <sup>†‡</sup> [51] | 31.11      | 17.38 | 0.36 | 0.57 |
| <b>ADM</b> (dropout)                  | 2.57 | 6.81 | 0.71 | 0.55 | IDDPM $‡$ [43]                 | 12.26      | 5.42  | 0.70 | 0.62 |
|                                       |      |      |      |      | $SR3^{\dagger \ddagger}$ [53]  | 11.30      |       |      |      |
| LSUN Cats $256\times256$              |      |      |      |      | BigGAN-deep [5]                | 6.95       | 7.36  | 0.87 | 0.28 |
| <b>DDPM</b> [25]                      | 17.1 | 12.4 | 0.53 | 0.48 | <b>ADM</b>                     | 10.94      | 6.02  | 0.69 | 0.63 |
| StyleGAN2 <sup>[28]</sup>             | 7.25 | 6.33 | 0.58 | 0.43 | ADM-G (25 steps)               | 5.44       | 5.32  | 0.81 | 0.49 |
| ADM (dropout)                         | 5.57 | 6.69 | 0.63 | 0.52 | <b>ADM-G</b>                   | 4.59       | 5.25  | 0.82 | 0.52 |
|                                       |      |      |      |      |                                |            |       |      |      |
| ImageNet $64\times64$                 |      |      |      |      | ImageNet $512\times512$        |            |       |      |      |
| $BigGAN\$ [5]                         | 4.06 | 3.96 | 0.79 | 0.48 | BigGAN-deep [5]                | 8.43       | 8.13  | 0.88 | 0.29 |
| IDDPM $[43]$                          | 2.92 | 3.79 | 0.74 | 0.62 | <b>ADM</b>                     | 23.24      | 10.19 | 0.73 | 0.60 |
| <b>ADM</b>                            | 2.61 | 3.77 | 0.73 | 0.63 | $ADM-G (25 steps)$             | 8.41       | 9.67  | 0.83 | 0.47 |
| ADM (dropout)                         | 2.07 | 4.29 | 0.74 | 0.63 | <b>ADM-G</b>                   | 7.72       | 6.57  | 0.87 | 0.42 |

Table 5: Sample quality comparison with state-of-the-art generative models for each task. ADM refers to our ablated diffusion model, and ADM-G additionally uses classifier guidance. LSUN diffusion models are sampled using 1000 steps (see Appendix [J\)](#page-28-0). ImageNet diffusion models are sampled using 250 steps, except when we use the DDIM sampler with 25 steps. \*No BigGAN-deep model was available at this resolution, so we trained our own. <sup>†</sup>Values are taken from a previous paper, due to lack of public models or samples. ‡Results use two-resolution stacks.

### 5.1 State-of-the-art Image Synthesis

Table [5](#page-9-0) summarizes our results. Our diffusion models can obtain the best FID on each task, and the best sFID on all but one task. With the improved architecture, we already obtain state-of-the-art image generation on LSUN and ImageNet 64×64. For higher resolution ImageNet, we observe that classifier guidance allows our models to substantially outperform the best GANs. These models obtain perceptual quality similar to GANs, while maintaining a higher coverage of the distribution as measured by recall, and can even do so using only 25 diffusion steps.

Figure [6](#page-10-0) compares random samples from the best BigGAN-deep model to our best diffusion model. While the samples are of similar perceptual quality, the diffusion model contains more modes than the GAN, such as zoomed ostrich heads, single flamingos, different orientations of cheeseburgers, and a tinca fish with no human holding it. We also check our generated samples for nearest neighbors in the Inception-V3 feature space in Appendix [C,](#page-19-0) and we show additional samples in Appendices [K](#page-29-0)[-M.](#page-41-0)

### 5.2 Comparison to Upsampling

We also compare guidance to using a two-stage upsampling stack. [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) and [Saharia et al.](#page-14-13) [\[53\]](#page-14-13) train two-stage diffusion models by combining a low-resolution diffusion model with a corresponding upsampling diffusion model. In this approach, the upsampling model is trained to upsample images from the training set, and conditions on low-resolution images that are concatenated channel-wise to the model input using a simple interpolation (e.g. bilinear). During sampling, the low-resolution model produces a sample, and then the upsampling model is conditioned on this sample. This greatly improves FID on ImageNet  $256\times256$ , but does not reach the same performance as state-of-the-art models like BigGAN-deep [\[43,](#page-14-2) [53\]](#page-14-13), as seen in Table [5.](#page-9-0)

In Table [6,](#page-10-1) we show that guidance and upsampling improve sample quality along different axes. While upsampling improves precision while keeping a high recall, guidance provides a knob to trade

<span id="page-10-0"></span>Image /page/10/Picture/0 description: This image is a grid of photos. The top two rows show ostriches in various poses and environments. The next two rows feature flamingos, some in groups and others individually, in water and on land. The following two rows are filled with images of hamburgers and fries. The bottom two rows display photos of people holding large fish they have caught.

Figure 6: Samples from BigGAN-deep with truncation 1.0 (FID 6.95, left) vs samples from our diffusion model with guidance (FID 4.59, middle) and samples from the training set (right).

<span id="page-10-1"></span>

| Model                  | $S_{base}$ | $S_{	ext{upsample}}$ | FID   | sFID  | IS     | Precision | Recall |
|------------------------|------------|----------------------|-------|-------|--------|-----------|--------|
| ImageNet $256	imes256$ |            |                      |       |       |        |           |        |
| <b>ADM</b>             | 250        |                      | 10.94 | 6.02  | 100.98 | 0.69      | 0.63   |
| ADM-U                  | 250        | 250                  | 7.49  | 5.13  | 127.49 | 0.72      | 0.63   |
| $ADM-G$                | 250        |                      | 4.59  | 5.25  | 186.70 | 0.82      | 0.52   |
| ADM-G, ADM-U           | 250        | 250                  | 3.94  | 6.14  | 215.84 | 0.83      | 0.53   |
| ImageNet $512	imes512$ |            |                      |       |       |        |           |        |
| <b>ADM</b>             | 250        |                      | 23.24 | 10.19 | 58.06  | 0.73      | 0.60   |
| ADM-U                  | 250        | 250                  | 9.96  | 5.62  | 121.78 | 0.75      | 0.64   |
| $ADM-G$                | 250        |                      | 7.72  | 6.57  | 172.71 | 0.87      | 0.42   |
| ADM-G, ADM-U           | 25         | 25                   | 5.96  | 12.10 | 187.87 | 0.81      | 0.54   |
| ADM-G, ADM-U           | 250        | 25                   | 4.11  | 9.57  | 219.29 | 0.83      | 0.55   |
| ADM-G, ADM-U           | 250        | 250                  | 3.85  | 5.86  | 221.72 | 0.84      | 0.53   |

Table 6: Comparing our single, upsampling and classifier guided models. For upsampling, we use the upsampling stack from [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) combined with our architecture improvements, which we refer to as ADM-U. The base resolution for the two-stage upsampling models is 64 and 128 for the 256 and 512 models, respectively. When combining classifier guidance with upsampling, we only guide the lower resolution model.

off diversity for much higher precision. We achieve the best FIDs by using guidance at a lower resolution before upsampling to a higher resolution, indicating that these approaches complement one another.

## 6 Related Work

Score based generative models were introduced by [Song and Ermon](#page-15-5) [\[59\]](#page-15-5) as a way of modeling a data distribution using its gradients, and then sampling using Langevin dynamics [\[67\]](#page-15-14). [Ho et al.](#page-13-6) [\[25\]](#page-13-6) found a connection between this method and diffusion models [\[56\]](#page-15-4), and achieved excellent sample quality by leveraging this connection. After this breakthrough work, many works followed up with more promising results: [Kong et al.](#page-13-12) [\[30\]](#page-13-12) and [Chen et al.](#page-12-8) [\[8\]](#page-12-8) demonstrated that diffusion models

work well for audio; [Jolicoeur-Martineau et al.](#page-13-9) [\[26\]](#page-13-9) found that a GAN-like setup could improve samples from these models; [Song et al.](#page-15-6) [\[60\]](#page-15-6) explored ways to leverage techniques from stochastic differential equations to improve the sample quality obtained by score-based models; [Song et al.](#page-15-7) [\[57\]](#page-15-7) and [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) proposed methods to improve sampling speed; [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) and [Saharia et al.](#page-14-13) [\[53\]](#page-14-13) demonstrated promising results on the difficult ImageNet generation task using upsampling diffusion models. Also related to diffusion models, and following the work of [Sohl-Dickstein et al.](#page-15-4) [\[56\]](#page-15-4), [Goyal et al.](#page-13-13) [\[21\]](#page-13-13) described a technique for learning a model with learned iterative generation steps, and found that it could achieve good image samples when trained with a likelihood objective.

One missing element from previous work on diffusion models is a way to trade off diversity for fidelity. Other generative techniques provide natural levers for this trade-off. [Brock et al.](#page-12-1) [\[5\]](#page-12-1) introduced the truncation trick for GANs, wherein the latent vector is sampled from a truncated normal distribution. They found that increasing truncation naturally led to a decrease in diversity but an increase in fidelity. More recently, [Razavi et al.](#page-14-0) [\[51\]](#page-14-0) proposed to use classifier rejection sampling to filter out bad samples from an autoregressive likelihood-based model, and found that this technique improved FID. Most likelihood-based models also allow for low-temperature sampling [\[1\]](#page-12-9), which provides a natural way to emphasize modes of the data distribution (see Appendix [G\)](#page-23-0).

Other likelihood-based models have been shown to produce high-fidelity image samples. VQ-VAE [\[65\]](#page-15-15) and VQ-VAE-2 [\[51\]](#page-14-0) are autoregressive models trained on top of quantized latent codes, greatly reducing the computational resources required to train these models on large images. These models produce diverse and high quality images, but still fall short of GANs without expensive rejection sampling and special metrics to compensate for blurriness. DCTransformer [\[42\]](#page-14-3) is a related method which relies on a more intelligent compression scheme. VAEs are another promising class of likelihood-based models, and recent methods such as NVAE [\[63\]](#page-15-16) and VDVAE [\[9\]](#page-12-4) have successfully been applied to difficult image generation domains. Energy-based models are another class of likelihood-based models with a rich history [\[1,](#page-12-9) [10,](#page-12-10) [24\]](#page-13-14). Sampling from the EBM distribution is challenging, and [Xie et al.](#page-15-17) [\[70\]](#page-15-17) demonstrate that Langevin dynamics can be used to sample coherent images from these models. [Du and Mordatch](#page-13-15) [\[15\]](#page-13-15) further improve upon this approach, obtaining high quality images. More recently, [Gao et al.](#page-13-16) [\[18\]](#page-13-16) incorporate diffusion steps into an energy-based model, and find that doing so improves image samples from these models.

Other works have controlled generative models with a pre-trained classifier. For example, an emerging body of work [\[17,](#page-13-17) [47,](#page-14-14) [2\]](#page-12-11) aims to optimize GAN latent spaces for text prompts using pre-trained CLIP [\[49\]](#page-14-11) models. More similar to our work, [Song et al.](#page-15-6) [\[60\]](#page-15-6) uses a classifier to generate class-conditional CIFAR-10 images with a diffusion model. In some cases, classifiers can act as stand-alone generative models. For example, [Santurkar et al.](#page-15-18) [\[55\]](#page-15-18) demonstrate that a robust image classifier can be used as a stand-alone generative model, and [Grathwohl et al.](#page-13-18) [\[22\]](#page-13-18) train a model which is jointly a classifier and an energy-based model.

## 7 Limitations and Future Work

While we believe diffusion models are an extremely promising direction for generative modeling, they are still slower than GANs at sampling time due to the use of multiple denoising steps (and therefore forward passes). One promising work in this direction is from [Luhman and Luhman](#page-14-15) [\[37\]](#page-14-15), who explore a way to distill the DDIM sampling process into a single step model. The samples from the single step model are not yet competitive with GANs, but are much better than previous single-step likelihood-based models. Future work in this direction might be able to completely close the sampling speed gap between diffusion models and GANs without sacrificing image quality.

Our proposed classifier guidance technique is currently limited to labeled datasets, and we have provided no effective strategy for trading off diversity for fidelity on unlabeled datasets. In the future, our method could be extended to unlabeled data by clustering samples to produce synthetic labels [\[36\]](#page-14-10) or by training discriminative models to predict when samples are in the true data distribution or from the sampling distribution.

The effectiveness of classifier guidance demonstrates that we can obtain powerful generative models from the gradients of a classification function. This could be used to condition pre-trained models in a plethora of ways, for example by conditioning an image generator with a text caption using a noisy version of CLIP [\[49\]](#page-14-11), similar to recent methods that guide GANs using text prompts [\[17,](#page-13-17) [47,](#page-14-14) [2\]](#page-12-11). It also suggests that large unlabeled datasets could be leveraged in the future to pre-train powerful diffusion models that can later be improved by using a classifier with desirable properties.

## 8 Conclusion

We have shown that diffusion models, a class of likelihood-based models with a stationary training objective, can obtain better sample quality than state-of-the-art GANs. Our improved architecture is sufficient to achieve this on unconditional image generation tasks, and our classifier guidance technique allows us to do so on class-conditional tasks. In the latter case, we find that the scale of the classifier gradients can be adjusted to trade off diversity for fidelity. These guided diffusion models can reduce the sampling time gap between GANs and diffusion models, although diffusion models still require multiple forward passes during sampling. Finally, by combining guidance with upsampling, we can further improve sample quality on high-resolution conditional image synthesis.

#### 9 Acknowledgements

We thank Alec Radford, Mark Chen, Pranav Shyam and Raul Puri for providing feedback on this work.

## **References**

- <span id="page-12-9"></span>[1] David Ackley, Geoffrey Hinton, and Terrence Sejnowski. A learning algorithm for boltzmann machines. *Cognitive science, 9(1):147-169*, 1985.
- <span id="page-12-11"></span>[2] Adverb. The big sleep. [https://twitter.com/advadnoun/status/](https://twitter.com/advadnoun/status/1351038053033406468) [1351038053033406468](https://twitter.com/advadnoun/status/1351038053033406468), 2021.
- <span id="page-12-5"></span>[3] Shane Barratt and Rishi Sharma. A note on the inception score. *[arXiv:1801.01973](https://arxiv.org/abs/1801.01973)*, 2018.
- <span id="page-12-3"></span>[4] Andrew Brock, Theodore Lim, J. M. Ritchie, and Nick Weston. Neural photo editing with introspective adversarial networks. *[arXiv:1609.07093](https://arxiv.org/abs/1609.07093)*, 2016.
- <span id="page-12-1"></span>[5] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. *[arXiv:1809.11096](https://arxiv.org/abs/1809.11096)*, 2018.
- <span id="page-12-0"></span>[6] Tom B. Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, Sandhini Agarwal, Ariel Herbert-Voss, Gretchen Krueger, Tom Henighan, Rewon Child, Aditya Ramesh, Daniel M. Ziegler, Jeffrey Wu, Clemens Winter, Christopher Hesse, Mark Chen, Eric Sigler, Mateusz Litwin, Scott Gray, Benjamin Chess, Jack Clark, Christopher Berner, Sam McCandlish, Alec Radford, Ilya Sutskever, and Dario Amodei. Language models are few-shot learners. *[arXiv:2005.14165](https://arxiv.org/abs/2005.14165)*, 2020.
- <span id="page-12-2"></span>[7] Mark Chen, Alec Radford, Rewon Child, Jeffrey Wu, Heewoo Jun, David Luan, and Ilya Sutskever. Generative pretraining from pixels. In *International Conference on Machine Learning*, pages 1691–1703. PMLR, 2020.
- <span id="page-12-8"></span>[8] Nanxin Chen, Yu Zhang, Heiga Zen, Ron J. Weiss, Mohammad Norouzi, and William Chan. Wavegrad: Estimating gradients for waveform generation. *[arXiv:2009.00713](https://arxiv.org/abs/2009.00713)*, 2020.
- <span id="page-12-4"></span>[9] Rewon Child. Very deep vaes generalize autoregressive models and can outperform them on images. *[arXiv:2011.10650](https://arxiv.org/abs/2011.10650)*, 2021.
- <span id="page-12-10"></span>[10] Peter Dayan, Geoffrey E Hinton, Radford M Neal, and Richard S Zemel. The helmholtz machine. *Neural computation*, 7(5):889–904, 1995.
- <span id="page-12-6"></span>[11] Harm de Vries, Florian Strub, Jérémie Mary, Hugo Larochelle, Olivier Pietquin, and Aaron Courville. Modulating early visual processing by language. *[arXiv:1707.00683](https://arxiv.org/abs/1707.00683)*, 2017.
- <span id="page-12-7"></span>[12] DeepMind. Biggan-deep 128x128 on tensorflow hub. [https://tfhub.dev/deepmind/](https://tfhub.dev/deepmind/biggan-deep-128/1) [biggan-deep-128/1](https://tfhub.dev/deepmind/biggan-deep-128/1), 2018.

- <span id="page-13-1"></span>[13] Prafulla Dhariwal, Heewoo Jun, Christine Payne, Jong Wook Kim, Alec Radford, and Ilya Sutskever. Jukebox: A generative model for music. *[arXiv:2005.00341](https://arxiv.org/abs/2005.00341)*, 2020.
- <span id="page-13-2"></span>[14] Jeff Donahue and Karen Simonyan. Large scale adversarial representation learning. *[arXiv:1907.02544](https://arxiv.org/abs/1907.02544)*, 2019.
- <span id="page-13-15"></span>[15] Yilun Du and Igor Mordatch. Implicit generation and generalization in energy-based models. *[arXiv:1903.08689](https://arxiv.org/abs/1903.08689)*, 2019.
- <span id="page-13-11"></span>[16] Vincent Dumoulin, Jonathon Shlens, and Manjunath Kudlur. A learned representation for artistic style. *[arXiv:1610.07629](https://arxiv.org/abs/1610.07629)*, 2017.
- <span id="page-13-17"></span>[17] Federico A. Galatolo, Mario G. C. A. Cimino, and Gigliola Vaglini. Generating images from caption and vice versa via clip-guided generative latent space search. *[arXiv:2102.01645](https://arxiv.org/abs/2102.01645)*, 2021.
- <span id="page-13-16"></span>[18] Ruiqi Gao, Yang Song, Ben Poole, Ying Nian Wu, and Diederik P. Kingma. Learning energybased models by diffusion recovery likelihood. *[arXiv:2012.08125](https://arxiv.org/abs/2012.08125)*, 2020.
- <span id="page-13-3"></span>[19] Ian J. Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial networks. *[arXiv:1406.2661](https://arxiv.org/abs/1406.2661)*, 2014.
- <span id="page-13-19"></span>[20] Google. Cloud tpus. <https://cloud.google.com/tpu/>, 2018.
- <span id="page-13-13"></span>[21] Anirudh Goyal, Nan Rosemary Ke, Surya Ganguli, and Yoshua Bengio. Variational walkback: Learning a transition operator as a stochastic recurrent net. *[arXiv:1711.02282](https://arxiv.org/abs/1711.02282)*, 2017.
- <span id="page-13-18"></span>[22] Will Grathwohl, Kuan-Chieh Wang, Jörn-Henrik Jacobsen, David Duvenaud, Mohammad Norouzi, and Kevin Swersky. Your classifier is secretly an energy based model and you should treat it like one. *[arXiv:1912.03263](https://arxiv.org/abs/1912.03263)*, 2019.
- <span id="page-13-4"></span>[23] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. Gans trained by a two time-scale update rule converge to a local nash equilibrium. *Advances in Neural Information Processing Systems 30 (NIPS 2017)*, 2017.
- <span id="page-13-14"></span>[24] Geoffrey E Hinton. Training products of experts by minimizing contrastive divergence. *Neural computation*, 14(8):1771–1800, 2002.
- <span id="page-13-6"></span>[25] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. *[arXiv:2006.11239](https://arxiv.org/abs/2006.11239)*, 2020.
- <span id="page-13-9"></span>[26] Alexia Jolicoeur-Martineau, Rémi Piché-Taillefer, Rémi Tachet des Combes, and Ioannis Mitliagkas. Adversarial score matching and improved sampling for image generation. *[arXiv:2009.05475](https://arxiv.org/abs/2009.05475)*, 2020.
- <span id="page-13-8"></span>[27] Tero Karras, Samuli Laine, and Timo Aila. A style-based generator architecture for generative adversarial networks. *[arXiv:arXiv:1812.04948](https://arxiv.org/abs/arXiv:1812.04948)*, 2019.
- <span id="page-13-0"></span>[28] Tero Karras, Samuli Laine, Miika Aittala, Janne Hellsten, Jaakko Lehtinen, and Timo Aila. Analyzing and improving the image quality of stylegan. *[arXiv:1912.04958](https://arxiv.org/abs/1912.04958)*, 2019.
- <span id="page-13-20"></span>[29] Diederik P. Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *[arXiv:1412.6980](https://arxiv.org/abs/1412.6980)*, 2014.
- <span id="page-13-12"></span>[30] Zhifeng Kong, Wei Ping, Jiaji Huang, Kexin Zhao, and Bryan Catanzaro. Diffwave: A versatile diffusion model for audio synthesis. *[arXiv:2009.09761](https://arxiv.org/abs/2009.09761)*, 2020.
- <span id="page-13-7"></span>[31] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. CIFAR-10 (Canadian Institute for Advanced Research), 2009. URL <http://www.cs.toronto.edu/~kriz/cifar.html>.
- <span id="page-13-5"></span>[32] Tuomas Kynkäänniemi, Tero Karras, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Improved precision and recall metric for assessing generative models. *[arXiv:1904.06991](https://arxiv.org/abs/1904.06991)*, 2019.
- <span id="page-13-10"></span>[33] Guosheng Lin, Anton Milan, Chunhua Shen, and Ian Reid. Refinenet: Multi-path refinement networks for high-resolution semantic segmentation. *[arXiv:1611.06612](https://arxiv.org/abs/1611.06612)*, 2016.

- <span id="page-14-6"></span>[34] Ziwei Liu, Ping Luo, Xiaogang Wang, and Xiaoou Tang. Deep learning face attributes in the wild. In *Proceedings of International Conference on Computer Vision (ICCV)*, December 2015.
- <span id="page-14-17"></span>[35] Ilya Loshchilov and Frank Hutter. Decoupled weight decay regularization. *[arXiv:1711.05101](https://arxiv.org/abs/1711.05101)*, 2017.
- <span id="page-14-10"></span>[36] Mario Lucic, Michael Tschannen, Marvin Ritter, Xiaohua Zhai, Olivier Bachem, and Sylvain Gelly. High-fidelity image generation with fewer labels. *[arXiv:1903.02271](https://arxiv.org/abs/1903.02271)*, 2019.
- <span id="page-14-15"></span>[37] Eric Luhman and Troy Luhman. Knowledge distillation in iterative generative models for improved sampling speed. *[arXiv:2101.02388](https://arxiv.org/abs/2101.02388)*, 2021.
- <span id="page-14-18"></span>[38] Paulius Micikevicius, Sharan Narang, Jonah Alben, Gregory Diamos, Erich Elsen, David Garcia, Boris Ginsburg, Michael Houston, Oleksii Kuchaiev, Ganesh Venkatesh, and Hao Wu. Mixed precision training. *[arXiv:1710.03740](https://arxiv.org/abs/1710.03740)*, 2017.
- <span id="page-14-8"></span>[39] Mehdi Mirza and Simon Osindero. Conditional generative adversarial nets. *[arXiv:1411.1784](https://arxiv.org/abs/1411.1784)*, 2014.
- <span id="page-14-9"></span>[40] Takeru Miyato and Masanori Koyama. cgans with projection discriminator. *[arXiv:1802.05637](https://arxiv.org/abs/1802.05637)*, 2018.
- <span id="page-14-4"></span>[41] Takeru Miyato, Toshiki Kataoka, Masanori Koyama, and Yuichi Yoshida. Spectral normalization for generative adversarial networks. *[arXiv:1802.05957](https://arxiv.org/abs/1802.05957)*, 2018.
- <span id="page-14-3"></span>[42] Charlie Nash, Jacob Menick, Sander Dieleman, and Peter W. Battaglia. Generating images with sparse representations. *[arXiv:2103.03841](https://arxiv.org/abs/2103.03841)*, 2021.
- <span id="page-14-2"></span>[43] Alex Nichol and Prafulla Dhariwal. Improved denoising diffusion probabilistic models. *[arXiv:2102.09672](https://arxiv.org/abs/2102.09672)*, 2021.
- <span id="page-14-16"></span>[44] NVIDIA. Stylegan2. <https://github.com/NVlabs/stylegan2>, 2019.
- <span id="page-14-5"></span>[45] Gaurav Parmar, Richard Zhang, and Jun-Yan Zhu. On buggy resizing libraries and surprising subtleties in fid calculation. *[arXiv:2104.11222](https://arxiv.org/abs/2104.11222)*, 2021.
- <span id="page-14-19"></span>[46] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. Pytorch: An imperative style, high-performance deep learning library. *[arXiv:1912.01703](https://arxiv.org/abs/1912.01703)*, 2019.
- <span id="page-14-14"></span>[47] Or Patashnik, Zongze Wu, Eli Shechtman, Daniel Cohen-Or, and Dani Lischinski. Styleclip: Text-driven manipulation of stylegan imagery. *[arXiv:2103.17249](https://arxiv.org/abs/2103.17249)*, 2021.
- <span id="page-14-7"></span>[48] Ethan Perez, Florian Strub, Harm de Vries, Vincent Dumoulin, and Aaron Courville. Film: Visual reasoning with a general conditioning layer. *[arXiv:1709.07871](https://arxiv.org/abs/1709.07871)*, 2017.
- <span id="page-14-11"></span>[49] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. Learning transferable visual models from natural language supervision. *[arXiv:2103.00020](https://arxiv.org/abs/2103.00020)*, 2021.
- <span id="page-14-1"></span>[50] Aditya Ramesh, Mikhail Pavlov, Gabriel Goh, Scott Gray, Chelsea Voss, Alec Radford, Mark Chen, and Ilya Sutskever. Zero-shot text-to-image generation. *[arXiv:2102.12092](https://arxiv.org/abs/2102.12092)*, 2021.
- <span id="page-14-0"></span>[51] Ali Razavi, Aaron van den Oord, and Oriol Vinyals. Generating diverse high-fidelity images with VQ-VAE-2. *[arXiv:1906.00446](https://arxiv.org/abs/1906.00446)*, 2019.
- <span id="page-14-12"></span>[52] Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause, Sanjeev Satheesh, Sean Ma, Zhiheng Huang, Andrej Karpathy, Aditya Khosla, Michael Bernstein, Alexander C. Berg, and Li Fei-Fei. Imagenet large scale visual recognition challenge. *[arXiv:1409.0575](https://arxiv.org/abs/1409.0575)*, 2014.
- <span id="page-14-13"></span>[53] Chitwan Saharia, Jonathan Ho, William Chan, Tim Salimans, David J. Fleet, and Mohammad Norouzi. Image super-resolution via iterative refinement. *[arXiv:arXiv:2104.07636](https://arxiv.org/abs/arXiv:2104.07636)*, 2021.

- <span id="page-15-3"></span>[54] Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. *[arXiv:1606.03498](https://arxiv.org/abs/1606.03498)*, 2016.
- <span id="page-15-18"></span>[55] Shibani Santurkar, Dimitris Tsipras, Brandon Tran, Andrew Ilyas, Logan Engstrom, and Aleksander Madry. Image synthesis with a single (robust) classifier. *[arXiv:1906.09453](https://arxiv.org/abs/1906.09453)*, 2019.
- <span id="page-15-4"></span>[56] Jascha Sohl-Dickstein, Eric A. Weiss, Niru Maheswaranathan, and Surya Ganguli. Deep unsupervised learning using nonequilibrium thermodynamics. *[arXiv:1503.03585](https://arxiv.org/abs/1503.03585)*, 2015.
- <span id="page-15-7"></span>[57] Jiaming Song, Chenlin Meng, and Stefano Ermon. Denoising diffusion implicit models. *[arXiv:2010.02502](https://arxiv.org/abs/2010.02502)*, 2020.
- <span id="page-15-9"></span>[58] Yang Song and Stefano Ermon. Improved techniques for training score-based generative models. *[arXiv:2006.09011](https://arxiv.org/abs/2006.09011)*, 2020.
- <span id="page-15-5"></span>[59] Yang Song and Stefano Ermon. Generative modeling by estimating gradients of the data distribution. *[arXiv:arXiv:1907.05600](https://arxiv.org/abs/arXiv:1907.05600)*, 2020.
- <span id="page-15-6"></span>[60] Yang Song, Jascha Sohl-Dickstein, Diederik P. Kingma, Abhishek Kumar, Stefano Ermon, and Ben Poole. Score-based generative modeling through stochastic differential equations. *[arXiv:2011.13456](https://arxiv.org/abs/2011.13456)*, 2020.
- <span id="page-15-8"></span>[61] Christian Szegedy, Wojciech Zaremba, Ilya Sutskever, Joan Bruna, Dumitru Erhan, Ian Goodfellow, and Rob Fergus. Intriguing properties of neural networks. *[arXiv:1312.6199](https://arxiv.org/abs/1312.6199)*, 2013.
- <span id="page-15-10"></span>[62] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jonathon Shlens, and Zbigniew Wojna. Rethinking the inception architecture for computer vision. *[arXiv:1512.00567](https://arxiv.org/abs/1512.00567)*, 2015.
- <span id="page-15-16"></span>[63] Arash Vahdat and Jan Kautz. Nvae: A deep hierarchical variational autoencoder. *[arXiv:2007.03898](https://arxiv.org/abs/2007.03898)*, 2020.
- <span id="page-15-0"></span>[64] Aaron van den Oord, Sander Dieleman, Heiga Zen, Karen Simonyan, Oriol Vinyals, Alex Graves, Nal Kalchbrenner, Andrew Senior, and Koray Kavukcuoglu. Wavenet: A generative model for raw audio. *[arXiv:1609.03499](https://arxiv.org/abs/1609.03499)*, 2016.
- <span id="page-15-15"></span>[65] Aaron van den Oord, Oriol Vinyals, and Koray Kavukcuoglu. Neural discrete representation learning. *[arXiv:1711.00937](https://arxiv.org/abs/1711.00937)*, 2017.
- <span id="page-15-11"></span>[66] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N. Gomez, Lukasz Kaiser, and Illia Polosukhin. Attention is all you need. *[arXiv:1706.03762](https://arxiv.org/abs/1706.03762)*, 2017.
- <span id="page-15-14"></span>[67] Max Welling and Yee W Teh. Bayesian learning via stochastic gradient langevin dynamics. In *Proceedings of the 28th international conference on machine learning (ICML-11)*, pages 681–688. Citeseer, 2011.
- <span id="page-15-2"></span>[68] Yan Wu, Jeff Donahue, David Balduzzi, Karen Simonyan, and Timothy Lillicrap. Logan: Latent optimisation for generative adversarial networks. *[arXiv:1912.00953](https://arxiv.org/abs/1912.00953)*, 2019.
- <span id="page-15-12"></span>[69] Yuxin Wu and Kaiming He. Group normalization. *[arXiv:1803.08494](https://arxiv.org/abs/1803.08494)*, 2018.
- <span id="page-15-17"></span>[70] Jianwen Xie, Yang Lu, Song-Chun Zhu, and Ying Nian Wu. A theory of generative convnet. *[arXiv:1602.03264](https://arxiv.org/abs/1602.03264)*, 2016.
- <span id="page-15-13"></span>[71] Fisher Yu, Ari Seff, Yinda Zhang, Shuran Song, Thomas Funkhouser, and Jianxiong Xiao. Lsun: Construction of a large-scale image dataset using deep learning with humans in the loop. *[arXiv:1506.03365](https://arxiv.org/abs/1506.03365)*, 2015.
- <span id="page-15-1"></span>[72] Han Zhang, Tao Xu, Hongsheng Li, Shaoting Zhang, Xiaogang Wang, Xiaolei Huang, and Dimitris Metaxas. Stackgan: Text to photo-realistic image synthesis with stacked generative adversarial networks. *[arXiv:1612.03242](https://arxiv.org/abs/1612.03242)*, 2016.
- <span id="page-15-19"></span>[73] Ligeng Zhu. Thop. <https://github.com/Lyken17/pytorch-OpCounter>, 2018.

## A Computational Requirements

Compute is essential to modern machine learning applications, and more compute typically yields better results. It is thus important to compare our method's compute requirements to competing methods. In this section, we demonstrate that we can achieve results better than StyleGAN2 and BigGAN-deep with the same or lower compute budget.

#### A.1 Throughput

We first benchmark the throughput of our models in Table [7.](#page-16-0) For the theoretical throughput, we measure the theoretical FLOPs for our model using THOP [\[73\]](#page-15-19), and assume 100% utilization of an NVIDIA Tesla V100 (120 TFLOPs), while for the actual throughput we use measured wall-clock time. We include communication time across two machines whenever our training batch size doesn't fit on a single machine, where each of our machines has 8 V100s.

We find that a naive implementation of our models in PyTorch 1.7 is very inefficient, utilizing only 20-30% of the hardware. We also benchmark our optimized version, which use larger per-GPU batch sizes, fused GroupNorm-Swish and fused Adam CUDA ops. For our ImageNet  $128 \times 128$  model in particular, we find that we can increase the per-GPU batch size from 4 to 32 while still fitting in GPU memory, and this makes a large utilization difference. Our implementation is still far from optimal, and further optimizations should allow us to reach higher levels of utilization.

<span id="page-16-0"></span>

| Model      | Implementation | Batch Size<br>per GPU | Throughput<br>Imgs per V100-sec | Utilization |
|------------|----------------|-----------------------|---------------------------------|-------------|
| 64x64      | Theoretical    | -                     | 182.3                           | 100%        |
|            | Naive          | 32                    | 37.0                            | 20%         |
|            | Optimized      | 96                    | 74.1                            | 41%         |
| 128x128    | Theoretical    | -                     | 65.2                            | 100%        |
|            | Naive          | 4                     | 11.5                            | 18%         |
|            | Optimized      | 32                    | 24.8                            | 38%         |
| 256x256    | Theoretical    | -                     | 17.9                            | 100%        |
|            | Naive          | 4                     | 4.4                             | 25%         |
|            | Optimized      | 8                     | 6.4                             | 36%         |
| 64 -> 256  | Theoretical    | -                     | 31.7                            | 100%        |
|            | Naive          | 4                     | 6.3                             | 20%         |
|            | Optimized      | 12                    | 9.5                             | 30%         |
| 128 -> 512 | Theoretical    | -                     | 8.0                             | 100%        |
|            | Naive          | 2                     | 1.9                             | 24%         |
|            | Optimized      | 2                     | 2.3                             | 29%         |

Table 7: Throughput of our ImageNet models, measured in Images per V100-sec.

#### A.2 Early stopping

<span id="page-16-1"></span>In addition, we can train for many fewer iterations while maintaining sample quality superior to BigGAN-deep. Table [8](#page-16-1) and [9](#page-17-0) evaluate our ImageNet  $128 \times 128$  and  $256 \times 256$  models throughout training. We can see that the ImageNet  $128 \times 128$  model beats BigGAN-deep's FID (6.02) after 500K training iterations, only one eighth of the way through training. Similarly, the ImageNet  $256 \times 256$ model beats BigGAN-deep after 750K iterations, roughly a third of the way through training.

| Iterations | FID  | sFID | Precision | Recall |
|------------|------|------|-----------|--------|
| 250K       | 7.97 | 6.48 | 0.80      | 0.50   |
| 500K       | 5.31 | 5.97 | 0.83      | 0.49   |
| 1000K      | 4.10 | 5.80 | 0.81      | 0.51   |
| 2000K      | 3.42 | 5.69 | 0.83      | 0.53   |
| 4360K      | 3.09 | 5.59 | 0.82      | 0.54   |

Table 8: Evaluating an ImageNet  $128 \times 128$  model throughout training (classifier scale 1.0).

| Iterations | FID   | sFID | Precision | Recall |
|------------|-------|------|-----------|--------|
| 250K       | 12.21 | 6.15 | 0.78      | 0.50   |
| 500K       | 7.95  | 5.51 | 0.81      | 0.50   |
| 750K       | 6.49  | 5.39 | 0.81      | 0.50   |
| 1000K      | 5.74  | 5.29 | 0.81      | 0.52   |
| 1500K      | 5.01  | 5.20 | 0.82      | 0.52   |
| 1980K      | 4.59  | 5.25 | 0.82      | 0.52   |

<span id="page-17-0"></span>Table 9: Evaluating an ImageNet 256×256 model throughout training (classifier scale 1.0).

#### A.3 Compute comparison

Finally, in Table [10](#page-17-1) we compare the compute of our models with StyleGAN2 and BigGAN-deep, and show we can obtain better FIDs with a similar compute budget. For BigGAN-deep, [Brock et al.](#page-12-1) [\[5\]](#page-12-1) do not explicitly describe the compute requirements for training their models, but rather provide rough estimates in terms of days on a Google TPUv3 pod [\[20\]](#page-13-19). We convert their TPU-v3 estimates to V100 days according to 2 TPU-v3 day = 1 V100 day. For StyleGAN2, we use the reported throughput of 25M images over 32 days 13 hour on one V100 for config-f [\[44\]](#page-14-16). We note that our classifier training is relatively lightweight compared to training the generative model.

<span id="page-17-1"></span>

| Model                        | Generator Compute |                 | Classifier Compute | Total Compute | FID  | sFID | Precision | Recall |
|------------------------------|-------------------|-----------------|--------------------|---------------|------|------|-----------|--------|
| LSUN Horse $256	imes256$     |                   |                 |                    |               |      |      |           |        |
| StyleGAN2 [28]               |                   |                 |                    | 130           | 3.84 | 6.46 | 0.63      | 0.48   |
| ADM (250K)                   | 116               |                 |                    | 116           | 2.95 | 5.94 | 0.69      | 0.55   |
| ADM (dropout, 250K)          | 116               |                 |                    | 116           | 2.57 | 6.81 | 0.71      | 0.55   |
| LSUN Cat $256	imes256$       |                   |                 |                    |               |      |      |           |        |
| StyleGAN2[28]                |                   |                 |                    | 115           | 7.25 | 6.33 | 0.58      | 0.43   |
| ADM (dropout, 200K)          | 92                |                 |                    | 92            | 5.57 | 6.69 | 0.63      | 0.52   |
| ImageNet $128	imes128$       |                   |                 |                    |               |      |      |           |        |
| BigGAN-deep [5]              |                   |                 |                    | 64-128        | 6.02 | 7.18 | 0.86      | 0.35   |
| ADM-G (4360K)                | 521               | 9               |                    | 530           | 3.09 | 5.59 | 0.82      | 0.54   |
| ADM-G (450K)                 | 54                | 9               |                    | 63            | 5.67 | 6.19 | 0.82      | 0.49   |
| ImageNet $256	imes256$       |                   |                 |                    |               |      |      |           |        |
| BigGAN-deep [5]              |                   |                 |                    | 128-256       | 6.95 | 7.36 | 0.87      | 0.28   |
| ADM-G (1980K)                | 916               | 46              |                    | 962           | 4.59 | 5.25 | 0.82      | 0.52   |
| ADM-G (750K)                 | 347               | 46              |                    | 393           | 6.49 | 5.39 | 0.81      | 0.50   |
| ADM-G (750K)                 | 347               | $14^{\dagger}$  |                    | 361           | 6.68 | 5.34 | 0.81      | 0.51   |
| ADM-G (540K), ADM-U (500K)   | 329               | 30              |                    | 359           | 3.85 | 5.86 | 0.84      | 0.53   |
| ADM-G (540K), ADM-U (150K)   | 219               | 30              |                    | 249           | 4.15 | 6.14 | 0.82      | 0.54   |
| ADM-G (200K), ADM-U (150K)   | 110               | $10^{\ddagger}$ |                    | 126           | 4.93 | 5.82 | 0.82      | 0.52   |
| ImageNet $512	imes512$       |                   |                 |                    |               |      |      |           |        |
| BigGAN-deep [5]              |                   |                 |                    | 256-512       | 8.43 | 8.13 | 0.88      | 0.29   |
| ADM-G (4360K), ADM-U (1050K) | 1878              | 36              |                    | 1914          | 3.85 | 5.86 | 0.84      | 0.53   |
| ADM-G (500K), ADM-U (100K)   | 189               | 9*              |                    | 198           | 7.59 | 6.84 | 0.84      | 0.53   |

Table 10: Training compute requirements for our diffusion models compared to StyleGAN2 and BigGAN-deep. Training iterations for each diffusion model are mentioned in parenthesis. Compute is measured in V100-days. <sup>†</sup>ImageNet 256×256 classifier with 150K iterations (instead of 500K). ‡ ImageNet 64×64 classifier with batch size 256 (instead of 1024). \*ImageNet 128×128 classifier with batch size 256 (instead of 1024).

## <span id="page-18-0"></span>B Detailed Formulation of DDPM

Here, we provide a detailed review of the formulation of Gaussian diffusion models from [Ho et al.](#page-13-6) [\[25\]](#page-13-6). We start by defining our data distribution  $x_0 \sim q(x_0)$  and a Markovian noising process q which gradually adds noise to the data to produce noised samples  $x_1$  through  $x_T$ . In particular, each step of the noising process adds Gaussian noise according to some variance schedule given by  $\beta_t$ :

$$
q(x_t|x_{t-1}) := \mathcal{N}(x_t; \sqrt{1 - \beta_t} x_{t-1}, \beta_t \mathbf{I})
$$
\n(15)

[Ho et al.](#page-13-6) [\[25\]](#page-13-6) note that we need not apply q repeatedly to sample from  $x_t \sim q(x_t|x_0)$ . Instead,  $q(x_t|x_0)$  can be expressed as a Gaussian distribution. With  $\alpha_t := 1 - \beta_t$  and  $\bar{\alpha}_t := \prod_{s=0}^t \alpha_s$ 

$$
q(x_t|x_0) = \mathcal{N}(x_t; \sqrt{\bar{\alpha}_t}x_0, (1 - \bar{\alpha}_t)\mathbf{I})
$$
\n(16)

<span id="page-18-1"></span>
$$
= \sqrt{\bar{\alpha}}_t x_0 + \epsilon \sqrt{1 - \bar{\alpha}}_t, \ \epsilon \sim \mathcal{N}(0, \mathbf{I}) \tag{17}
$$

Here,  $1 - \bar{\alpha}_t$  tells us the variance of the noise for an arbitrary timestep, and we could equivalently use this to define the noise schedule instead of  $\beta_t$ .

Using Bayes theorem, one finds that the posterior  $q(x_{t-1}|x_t, x_0)$  is also a Gaussian with mean  $\tilde{\mu}_t(x_t, x_0)$  and variance  $\tilde{\beta}_t$  defined as follows:

$$
\tilde{\mu}_t(x_t, x_0) \coloneqq \frac{\sqrt{\bar{\alpha}_{t-1}} \beta_t}{1 - \bar{\alpha}_t} x_0 + \frac{\sqrt{\alpha_t} (1 - \bar{\alpha}_{t-1})}{1 - \bar{\alpha}_t} x_t \tag{18}
$$

<span id="page-18-4"></span>
$$
\tilde{\beta}_t := \frac{1 - \bar{\alpha}_{t-1}}{1 - \bar{\alpha}_t} \beta_t \tag{19}
$$

$$
q(x_{t-1}|x_t, x_0) = \mathcal{N}(x_{t-1}; \tilde{\mu}(x_t, x_0), \tilde{\beta}_t \mathbf{I})
$$
\n(20)

If we wish to sample from the data distribution  $q(x_0)$ , we can first sample from  $q(x_T)$  and then sample reverse steps  $q(x_{t-1}|x_t)$  until we reach  $x_0$ . Under reasonable settings for  $\beta_t$  and T, the distribution  $q(x_T)$  is nearly an isotropic Gaussian distribution, so sampling  $x_T$  is trivial. All that is left is to approximate  $q(x_{t-1}|x_t)$  using a neural network, since it cannot be computed exactly when the data distribution is unknown. To this end, [Sohl-Dickstein et al.](#page-15-4) [\[56\]](#page-15-4) note that  $q(x_{t-1}|x_t)$  approaches a diagonal Gaussian distribution as  $T \to \infty$  and correspondingly  $\beta_t \to 0$ , so it is sufficient to train a neural network to predict a mean  $\mu_{\theta}$  and a diagonal covariance matrix  $\Sigma_{\theta}$ :

$$
p_{\theta}(x_{t-1}|x_t) := \mathcal{N}(x_{t-1}; \mu_{\theta}(x_t, t), \Sigma_{\theta}(x_t, t))
$$
\n<sup>(21)</sup>

To train this model such that  $p(x_0)$  learns the true data distribution  $q(x_0)$ , we can optimize the following variational lower-bound  $L_{\text{vlb}}$  for  $p_{\theta}(x_0)$ :

$$
L_{\rm vlb} \coloneqq L_0 + L_1 + \dots + L_{T-1} + L_T \tag{22}
$$

$$
L_0 := -\log p_\theta(x_0|x_1) \tag{23}
$$

$$
L_{t-1} := D_{KL}(q(x_{t-1}|x_t, x_0) \mid p_{\theta}(x_{t-1}|x_t))
$$
\n(24)

$$
L_T \coloneqq D_{KL}(q(x_T|x_0) \mid p(x_T)) \tag{25}
$$

While the above objective is well-justified, [Ho et al.](#page-13-6) [\[25\]](#page-13-6) found that a different objective produces better samples in practice. In particular, they do not directly parameterize  $\mu_{\theta}(x_t, t)$  as a neural network, but instead train a model  $\epsilon_{\theta}(x_t, t)$  to predict  $\epsilon$  from Equation [17.](#page-18-1) This simplified objective is defined as follows:

$$
L_{\text{simple}} \coloneqq E_{t \sim [1,T], x_0 \sim q(x_0), \epsilon \sim \mathcal{N}(0, \mathbf{I})}[\|\epsilon - \epsilon_{\theta}(x_t, t)\|^2]
$$
\n(26)

During sampling, we can use substitution to derive  $\mu_{\theta}(x_t, t)$  from  $\epsilon_{\theta}(x_t, t)$ :

<span id="page-18-3"></span><span id="page-18-2"></span>
$$
\mu_{\theta}(x_t, t) = \frac{1}{\sqrt{\alpha_t}} \left( x_t - \frac{1 - \alpha_t}{\sqrt{1 - \bar{\alpha}_t}} \epsilon_{\theta}(x_t, t) \right)
$$
(27)

Note that  $L_{\text{simple}}$  does not provide any learning signal for  $\Sigma_{\theta}(x_t, t)$ . [Ho et al.](#page-13-6) [\[25\]](#page-13-6) find that instead of learning  $\Sigma_{\theta}(x_t, t)$ , they can fix it to a constant, choosing either  $\beta_t \mathbf{I}$  or  $\tilde{\beta}_t \mathbf{I}$ . These values correspond to upper and lower bounds for the true reverse step variance.

## <span id="page-19-0"></span>C Nearest Neighbors for Samples

<span id="page-19-1"></span>Image /page/19/Picture/1 description: This is a collage of many different images. The top half of the collage features ostriches, hamburgers, jellyfish, golden retrievers, people holding fish, and various birds. The bottom half of the collage features ostriches, hamburgers, jellyfish, golden retrievers, people holding fish, and various birds, along with Pekingese dogs, papillons, ducks, salamanders, corgis, and goldfish.

Figure 7: Nearest neighbors for samples from a classifier guided model on ImageNet  $256\times256$ . For each image, the top row is a sample, and the remaining rows are the top 3 nearest neighbors from the dataset. The top samples were generated with classifier scale 1 and 250 diffusion sampling steps (FID 4.59). The bottom samples were generated with classifier scale 2.5 and 25 DDIM steps (FID 5.44).

Our models achieve their best FID when using a classifier to reduce the diversity of the generations. One might fear that such a process could cause the model to recall existing images from the training dataset, especially as the classifier scale is increased. To test this, we looked at the nearest neighbors (in InceptionV3 [\[62\]](#page-15-10) feature space) for a handful of samples. Figure [7](#page-19-1) shows our results, revealing that the samples are indeed unique and not stored in the training set.

## D Effect of Varying the Classifier Scale

Image /page/19/Picture/5 description: The image displays a grid of animal photographs arranged in rows and columns. The top row features a sequence of husky dogs, transitioning from a full body shot to close-ups of their faces. The second row shows a yellow bird perched on branches, with slight variations in its pose and the surrounding foliage. The third row presents several images of pandas, some sitting and others eating, with subtle differences in their expressions and positions. The fourth row contains multiple pictures of orange butterflies on green leaves, with minor changes in their wing positions. The fifth row showcases tigers, mostly headshots with varying expressions and lighting. The bottom row displays elephants in a grassy field, with slight differences in their posture and the vegetation around them.

Figure 8: Samples when increasing the classifier scale from 0.0 (left) to 5.5 (right). Each row corresponds to a fixed noise seed. We observe that the classifier drastically changes some images, while leaving others relatively unaffected.

## E LSUN Diversity Comparison

Image /page/20/Picture/1 description: This is a collage of many images, primarily featuring cats and horses. The top section is dominated by various cat photos, including kittens, adult cats, and cats interacting with people. The middle section transitions to images of horses, with some showing riders, equestrian events, and close-ups of horse heads. The bottom section displays a variety of bedroom interiors, ranging from simple and modern to more elaborate and traditional designs. Some images also include people, particularly in the cat photos and some of the bedroom scenes.

Figure 9: Samples from StyleGAN2 (or StyleGAN for bedrooms) with truncation 1.0 (left) vs samples from our diffusion models (middle) and samples from the training set (right).

## F Interpolating Between Dataset Images Using DDIM

The DDIM [\[57\]](#page-15-7) sampling process is deterministic given the initial noise  $x_T$ , thus giving rise to an implicit latent space. It corresponds to integrating an ODE in the forward direction, and we can run the process in reverse to get the latents that produce a given real image. Here, we experiment with encoding real images into this latent space and then interpolating between them.

Equation 13 for the generative pass in DDIM looks like

$$
x_{t-1} - x_t = \sqrt{\bar{\alpha}_{t-1}} \left[ \left( \sqrt{1/\bar{\alpha}_t} - \sqrt{1/\bar{\alpha}_{t-1}} \right) x_t + \left( \sqrt{1/\bar{\alpha}_{t-1} - 1} - \sqrt{1/\bar{\alpha}_{t-1}} \right) \epsilon_\theta(x_t) \right]
$$

Thus, in the limit of small steps, we can expect the reversal of this ODE in the forward direction looks like

$$
x_{t+1} - x_t = \sqrt{\bar{\alpha}_{t+1}} \left[ \left( \sqrt{1/\bar{\alpha}_t} - \sqrt{1/\bar{\alpha}_{t+1}} \right) x_t + \left( \sqrt{1/\bar{\alpha}_{t+1} - 1} - \sqrt{1/\bar{\alpha}_t - 1} \right) \epsilon_\theta(x_t) \right]
$$

We found that this reverse ODE approximation gives latents with reasonable reconstructions, even with as few as 250 reverse steps. However, we noticed some noise artifacts when reversing all 250 steps, and find that reversing the first 249 steps gives much better reconstructions. To interpolate the latents, class embeddings, and classifier log probabilities, we use  $cos(\theta)x_0 + sin(\theta)x_1$  where  $\theta$ sweeps linearly from 0 to  $\frac{\pi}{2}$ .

Figures [10](#page-22-0)a through 10c show DDIM latent space interpolations on a class-conditional  $256\times256$ model, while varying the classifier scale. The left and rightmost images are ground truth dataset examples, and between them are reconstructed interpolations in DDIM latent space (including both endpoints). We see that the model with no guidance has almost perfect reconstructions due to its high recall, whereas raising the guidance scale to 2.5 only finds approximately similar reconstructions.

<span id="page-21-0"></span>Image /page/21/Picture/8 description: This is a grid of images showing various reconstructions and interpolations of real images. The top row displays cats, followed by cars in the second row. The third row shows trains, and the fourth row features dogs. The fifth row contains images of trombones and insects. The sixth row shows musicians playing instruments, and the seventh row displays lobsters. The bottom row shows guitar picks and children. The figure is labeled as 'figure. 10a: DDIM latent reconstructions and interpolations on real images with no classifier guidance'.

Figure 10a: DDIM latent reconstructions and interpolations on real images with no classifier guidance.

<span id="page-22-0"></span>Image /page/22/Picture/0 description: This is a collage of many different images. The top row shows images of cats and kittens. The second row shows images of trains and train tracks. The third row shows images of dogs, including puppies and adult dogs. The fourth row shows images of birds and insects. The fifth row shows images of musical instruments, including trombones and trumpets, as well as some decorative items. The sixth row shows images of lobsters and guitar picks. The seventh row shows images of bedrooms and beds. The eighth row shows images of various objects, including what appear to be parts of a vehicle or machinery, and a bookstore.

Figure 10b: DDIM latent reconstructions and interpolations on real images with classifier scale 1.0.

Image /page/22/Picture/2 description: The image is a grid of 35 smaller images, arranged in 7 rows and 5 columns. The images appear to be generated by a deep learning model, likely for image reconstruction or interpolation tasks, as suggested by the caption below. The grid showcases a variety of subjects including animals (dogs, raccoons, insects, lobsters, fish, lizards), objects (trains, lighthouses, musical instruments, furniture, shoes), and abstract patterns. Some images are clearly reconstructions of real-world objects, while others show variations or interpolations between different concepts. The overall presentation is a visual demonstration of the model's capabilities across diverse image categories.

Figure 10c: DDIM latent reconstructions and interpolations on real images with classifier scale 2.5.

## <span id="page-23-0"></span>G Reduced Temperature Sampling

We achieved our best ImageNet samples by reducing the diversity of our models using classifier guidance. For many classes of generative models, there is a much simpler way to reduce diversity: reducing the temperature [\[1\]](#page-12-9). The temperature parameter  $\tau$  is typically setup so that  $\tau = 1.0$  corresponds to standard sampling, and  $\tau < 1.0$  focuses more on high-density samples. We experimented with two ways of implementing this for diffusion models: first, by scaling the Gaussian noise used for each transition by  $\tau$ , and second by dividing  $\epsilon_{\theta}(x_t)$  by  $\tau$ . The latter implementation makes sense when thinking about  $\epsilon$  as a re-scaled score function (see Section [4.2\)](#page-6-3), and scaling up the score function is similar to scaling up classifier gradients.

To measure how temperature scaling affects samples, we experimented with our ImageNet  $128 \times 128$ model, evaluating FID, Precision, and Recall across different temperatures (Figure [11\)](#page-23-1). We find that two techniques behave similarly, and neither technique provides any substantial improvement in our evaluation metrics. We also find that low temperatures have both low precision and low recall, indicating that the model is not focusing on modes of the real data distribution. Figure [12](#page-23-2) highlights this effect, indicating that reducing temperature produces blurry, smooth images.

<span id="page-23-1"></span>Image /page/23/Figure/3 description: Three line graphs are displayed side-by-side. The x-axis for all graphs is labeled "1 - temperature" and uses a logarithmic scale. The y-axis of the first graph is labeled "FID" and ranges from 0 to 20. The second graph's y-axis is labeled "Precision" and ranges from 0.4 to 0.7. The third graph's y-axis is labeled "Recall" and ranges from 0.45 to 0.65. Each graph shows two lines: one blue line with circular markers representing "noise temperature" and one orange line with circular markers representing "epsilon temperature". In the FID graph, both lines start at approximately 5.5, slightly increase to around 6, then increase sharply after 10^-2. In the Precision graph, both lines start at approximately 0.7, slightly decrease to around 0.69, then decrease more rapidly after 10^-2. In the Recall graph, both lines start at approximately 0.65, slightly increase to around 0.655, then decrease gradually after 10^-3, with a steeper drop after 10^-2.

Figure 11: The effect of changing temperature for an ImageNet 128×128 model.

<span id="page-23-2"></span>Image /page/23/Picture/5 description: The image is a collage of 40 smaller images arranged in a 5x8 grid. The images depict a variety of subjects including animals (a snake, a butterfly, a dog, a cat, a monkey, a deer, a bird, a fish, a crab, a bee, a duck, a sheep, a wolf, a fox), food (a hamburger, a bowl of fruit, a jackfruit), objects (a gas mask, a pen, a phone, a car, a boat, a pumpkin, a bowl, a scale, a pencil, a cigarette butt, a lighter, a computer, a chair, a trash can, a fishing rod, a fishing net, a fishing lure, a fishing hook, a fishing line, a fishing reel, a fishing rod, a fishing lure, a fishing hook, a fishing line, a fishing reel), people (a woman in a dress, people in a rickshaw, a person jumping into water, a person with a ski mask, people playing sports, a person holding a fish, a person in a boat, a person in a kayak, a person in a canoe, a person in a raft, a person in a sailboat, a person in a motorboat, a person in a jet ski, a person in a paddleboat, a person in a rowboat, a person in a speedboat, a person in a tugboat, a person in a ferry, a person in a cruise ship, a person in a cargo ship, a person in a container ship, a person in a tanker ship, a person in a cruise ship, a person in a cargo ship, a person in a container ship, a person in a tanker ship), and landscapes (a mountain, a field, a beach, a forest, a desert, a city, a village, a town, a country, a state, a continent, a planet, a galaxy, a universe).

Figure 12: Samples at temperature 0.98 with epsilon scaling (left) and noise scaling (right).

## <span id="page-24-0"></span>H Conditional Diffusion Process

In this section, we show that conditional sampling can be achieved with a transition operator proportional to  $p_{\theta}(x_t|x_{t+1})p_{\phi}(y|x_t)$ , where  $p_{\theta}(x_t|x_{t+1})$  approximates  $q(x_t|x_{t+1})$  and  $p_{\phi}(y|x_t)$ approximates the label distribution for a noised sample  $x_t$ .

We start by defining a conditional Markovian noising process  $\hat{q}$  similar to q, and assume that  $\hat{q}(y|x_0)$ is a known and readily available label distribution for each sample.

$$
\hat{q}(x_0) \coloneqq q(x_0) \tag{28}
$$

$$
\hat{q}(y|x_0) :=
$$
 Known labels per sample\n
$$
(29)
$$

$$
\hat{q}(x_{t+1}|x_t, y) := q(x_{t+1}|x_t)
$$
\n(30)

$$
\hat{q}(x_{1:T}|x_0, y) := \prod_{t=1}^T \hat{q}(x_t|x_{t-1}, y)
$$
\n(31)

While we defined the noising process  $\hat{q}$  conditioned on y, we can prove that  $\hat{q}$  behaves exactly like  $q$  when not conditioned on  $y$ . Along these lines, we first derive the unconditional noising operator  $\hat{q}(x_{t+1}|x_t)$ :

$$
\hat{q}(x_{t+1}|x_t) = \int_y \hat{q}(x_{t+1}, y|x_t) \, dy \tag{32}
$$

$$
=\int_{y}\hat{q}(x_{t+1}|x_t,y)\hat{q}(y|x_t) dy
$$
\n(33)

$$
= \int_{y} q(x_{t+1}|x_t) \hat{q}(y|x_t) dy \tag{34}
$$

$$
= q(x_{t+1}|x_t) \int_y \hat{q}(y|x_t) dy \qquad (35)
$$

$$
= q(x_{t+1}|x_t) \tag{36}
$$

$$
= \hat{q}(x_{t+1}|x_t, y) \tag{37}
$$

Following similar logic, we find the joint distribution  $\hat{q}(x_{1:T} | x_0)$ :

$$
\hat{q}(x_{1:T}|x_0) = \int_y \hat{q}(x_{1:T}, y|x_0) \, dy \tag{38}
$$

$$
= \int_{y} \hat{q}(y|x_0)\hat{q}(x_{1:T}|x_0, y) \, dy \tag{39}
$$

$$
= \int_{y} \hat{q}(y|x_0) \prod_{t=1}^{T} \hat{q}(x_t|x_{t-1}, y) dy
$$
\n(40)

$$
= \int_{y} \hat{q}(y|x_0) \prod_{t=1}^{T} q(x_t|x_{t-1}) dy
$$
\n(41)

$$
= \prod_{t=1}^{T} q(x_t | x_{t-1}) \int_{y} \hat{q}(y | x_0) dy
$$
 (42)

$$
=\prod_{t=1}^{T} q(x_t|x_{t-1})
$$
\n(43)

<span id="page-24-1"></span>
$$
=q(x_{1:T}|x_0)\tag{44}
$$

Using Equation [44,](#page-24-1) we can now derive  $\hat{q}(x_t)$ :

$$
\hat{q}(x_t) = \int_{x_{0:t-1}} \hat{q}(x_0, ..., x_t) dx_{0:t-1}
$$
\n(45)

$$
= \int_{x_{0:t-1}} \hat{q}(x_0)\hat{q}(x_1,...,x_t|x_0) \, dx_{0:t-1} \tag{46}
$$

$$
= \int_{x_{0:t-1}} q(x_0) q(x_1, ..., x_t | x_0) dx_{0:t-1}
$$
\n(47)

$$
=\int_{x_{0:t-1}} q(x_0, ..., x_t) dx_{0:t-1}
$$
\n(48)

$$
=q(x_t) \tag{49}
$$

(50)

Using the identities  $\hat{q}(x_t) = q(x_t)$  and  $\hat{q}(x_{t+1}|x_t) = q(x_{t+1}|x_t)$ , it is trivial to show via Bayes rule that the unconditional reverse process  $\hat{q}(x_t|x_{t+1}) = q(x_t|x_{t+1})$ .

One observation about  $\hat{q}$  is that it gives rise to a noisy classification function,  $\hat{q}(y|x_t)$ . We can show that this classification distribution does not depend on  $x_{t+1}$  (a noisier version of  $x_t$ ), a fact which we will later use:

$$
\hat{q}(y|x_t, x_{t+1}) = \hat{q}(x_{t+1}|x_t, y) \frac{\hat{q}(y|x_t)}{\hat{q}(x_{t+1}|x_t)}
$$
\n(51)

$$
= \hat{q}(x_{t+1}|x_t) \frac{\hat{q}(y|x_t)}{\hat{q}(x_{t+1}|x_t)}
$$
(52)

$$
= \hat{q}(y|x_t) \tag{53}
$$

(54)

We can now derive the conditional reverse process:

$$
\hat{q}(x_t|x_{t+1}, y) = \frac{\hat{q}(x_t, x_{t+1}, y)}{\hat{q}(x_{t+1}, y)}
$$
\n(55)

$$
= \frac{\hat{q}(x_t, x_{t+1}, y)}{\hat{q}(y|x_{t+1})\hat{q}(x_{t+1})} \tag{56}
$$

$$
= \frac{\hat{q}(x_t|x_{t+1})\hat{q}(y|x_t, x_{t+1})\hat{q}(x_{t+1})}{\hat{q}(y|x_{t+1})\hat{q}(x_{t+1})}
$$
(57)

$$
= \frac{\hat{q}(x_t|x_{t+1})\hat{q}(y|x_t, x_{t+1})}{\hat{q}(y|x_{t+1})}
$$
(58)

$$
= \frac{\hat{q}(x_t|x_{t+1})\hat{q}(y|x_t)}{\hat{q}(y|x_{t+1})}
$$
\n(59)

$$
=\frac{q(x_t|x_{t+1})\hat{q}(y|x_t)}{\hat{q}(y|x_{t+1})}
$$
\n(60)

(61)

The  $\hat{q}(y|x_{t+1})$  term can be treated as a constant since it does not depend on  $x_t$ . We thus want to sample from the distribution  $Zq(x_t|x_{t+1})\hat{q}(y|x_t)$  where Z is a normalizing constant. We already have a neural network approximation of  $q(x_t|x_{t+1})$ , called  $p_\theta(x_t|x_{t+1})$ , so all that is left is an approximation of  $\hat{q}(y|x_t)$ . This can be obtained by training a classifier  $p_{\phi}(y|x_t)$  on noised images  $x_t$ derived by sampling from  $q(x_t)$ .

## I Hyperparameters

When choosing optimal classifier scales for our sampler, we swept over  $[0.5, 1, 2]$  for ImageNet  $128 \times 128$  and ImageNet  $256 \times 256$ , and  $[1, 2, 3, 3.5, 4, 4.5, 5]$  for ImageNet  $512 \times 512$ . For DDIM, we swept over values  $[0.5, 0.75, 1.0, 1.25, 2]$  for ImageNet  $128 \times 128$ ,  $[0.5, 1, 1.5, 2, 2.5, 3, 3.5]$  for ImageNet  $256 \times 256$ , and  $[3, 4, 5, 6, 7, 9, 11]$  for ImageNet  $512 \times 512$ .

Hyperparameters for training the diffusion and classification models are in Table [11](#page-26-0) and Table [12](#page-26-1) respectively. Hyperparameters for guided sampling are in Table [14.](#page-27-0) Hyperparameters used to train upsampling models are in Table [13.](#page-27-1) We train all of our models using Adam [\[29\]](#page-13-20) or AdamW [\[35\]](#page-14-17) with  $\beta_1 = 0.9$  and  $\beta_2 = 0.999$ . We train in 16-bit precision using loss-scaling [\[38\]](#page-14-18), but maintain 32-bit weights, EMA, and optimizer state. We use an EMA rate of 0.9999 for all experiments. We use PyTorch [\[46\]](#page-14-19), and train on NVIDIA Tesla V100s.

For all architecture ablations, we train with batch size 256, and sample using 250 sampling steps. For our attention heads ablations, we use 128 base channels, 2 residual blocks per resolution, multiresolution attention, and BigGAN up/downsampling, and we train the models for 700K iterations. By default, all of our experiments use adaptive group normalization, except when explicitly ablating for it.

When sampling with 1000 timesteps, we use the same noise schedule as for training. On ImageNet, we use the uniform stride from [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2) for 250 step samples and the slightly different uniform stride from [Song et al.](#page-15-7) [\[57\]](#page-15-7) for 25 step DDIM.

<span id="page-26-0"></span>

|                       | <b>LSUN</b> | ImageNet 64 | ImageNet 128 | ImageNet 256 | ImageNet 512    |
|-----------------------|-------------|-------------|--------------|--------------|-----------------|
| Diffusion steps       | 1000        | 1000        | 1000         | 1000         | 1000            |
| Noise Schedule        | linear      | cosine      | linear       | linear       | linear          |
| Model size            | 552M        | 296M        | 422M         | 554M         | 559M            |
| Channels              | 256         | 192         | 256          | 256          | 256             |
| Depth                 | 2           | 3           | 2            | 2            | 2               |
| Channels multiple     | 1,1,2,2,4,4 | 1,2,3,4     | 1,1,2,3,4    | 1,1,2,2,4,4  | 0.5,1,1,2,2,4,4 |
| Heads                 |             |             | 4            |              |                 |
| <b>Heads Channels</b> | 64          | 64          | 64           | 64           | 64              |
| Attention resolution  | 32,16,8     | 32,16,8     | 32,16,8      | 32,16,8      | 32,16,8         |
| BigGAN up/downsample  | ✓           | ✓           | ✓            | ✓            | ✓               |
| Dropout               | 0.1         | 0.1         | 0.0          | 0.0          | 0.0             |
| Batch size            | 256         | 2048        | 256          | 256          | 256             |
| <b>Iterations</b>     | varies*     | 540K        | 4360K        | 1980K        | 1940K           |
| Learning Rate         | 1e-4        | 3e-4        | 1e-4         | 1e-4         | 1e-4            |

Table 11: Hyperparameters for diffusion models. \*We used 200K iterations for LSUN cat, 250K for LSUN horse, and 500K for LSUN bedroom.

<span id="page-26-1"></span>

|                      | ImageNet 64 | ImageNet 128 | ImageNet 256 | ImageNet 512          |
|----------------------|-------------|--------------|--------------|-----------------------|
| Diffusion steps      | 1000        | 1000         | 1000         | 1000                  |
| Noise Schedule       | cosine      | linear       | linear       | linear                |
| Model size           | 65M         | 43M          | 54M          | 54M                   |
| Channels             | 128         | 128          | 128          | 128                   |
| Depth                | 4           | 2            | 2            | 2                     |
| Channels multiple    | 1,2,3,4     | 1,1,2,3,4    | 1,1,2,2,4,4  | 0.5, 1, 1, 2, 2, 4, 4 |
| Heads Channels       | 64          | 64           | 64           | 64                    |
| Attention resolution | 32,16,8     | 32,16,8      | 32,16,8      | 32,16,8               |
| BigGAN up/downsample | ✓           | ✓            | ✓            | ✓                     |
| Attention pooling    | ✓           | ✓            | ✓            | ✓                     |
| Weight decay         | 0.2         | 0.05         | 0.05         | 0.05                  |
| Batch size           | 1024        | $256*$       | 256          | 256                   |
| Iterations           | 300K        | 300K         | 500K         | 500K                  |
| Learning rate        | $6e-4$      | $3e-4*$      | $3e-4$       | $3e-4$                |

Table 12: Hyperparameters for classification models. \*For our ImageNet  $128 \times 128 \rightarrow 512 \times 512$ upsamples, we used a different classifier for the base model, with batch size 1024 and learning rate 6e-5.

<span id="page-27-1"></span>

|                      | ImageNet 64 → 256 | ImageNet 128 → 512 |
|----------------------|-------------------|--------------------|
| Diffusion steps      | 1000              | 1000               |
| Noise Schedule       | linear            | linear             |
| Model size           | 312M              | 309M               |
| Channels             | 192               | 192                |
| Depth                | 2                 | 2                  |
| Channels multiple    | 1,1,2,2,4,4       | 1,1,2,2,4,4*       |
| Heads                | 4                 |                    |
| Heads Channels       |                   | 64                 |
| Attention resolution | 32,16,8           | 32,16,8            |
| BigGAN up/downsample | ✓                 | ✓                  |
| Dropout              | 0.0               | 0.0                |
| Batch size           | 256               | 256                |
| <b>Iterations</b>    | 500K              | 1050K              |
| Learning Rate        | 1e-4              | 1e-4               |

Table 13: Hyperparameters for upsampling diffusion models. \*We chose this as an optimization, with the intuition that a lower-resolution path should be unnecessary for upsampling 128x128 images.

<span id="page-27-0"></span>

|                                 | ImageNet 64 | ImageNet 128 | ImageNet 256 | ImageNet 512 |
|---------------------------------|-------------|--------------|--------------|--------------|
| Gradient Scale (250 steps)      | 1.0         | 0.5          | 1.0          | 4.0          |
| Gradient Scale (DDIM, 25 steps) | -           | 1.25         | 2.5          | 9.0          |

Table 14: Hyperparameters for classifier-guided sampling.

## <span id="page-28-0"></span>J Using Fewer Sampling Steps on LSUN

We initially found that our LSUN models achieved much better results when sampling with 1000 steps rather than 250 steps, contrary to previous results from [Nichol and Dhariwal](#page-14-2) [\[43\]](#page-14-2). To address this, we conducted a sweep over sampling-time noise schedules, finding that an improved schedule can largely close the gap. We swept over schedules on LSUN bedrooms, and selected the schedule with the best FID for use on the other two datasets. Table [15](#page-28-1) details the findings of this sweep, and Table [16](#page-28-2) applies this schedule to three LSUN datasets.

<span id="page-28-1"></span>While sweeping over sampling schedules is not as expensive as re-training models from scratch, it does require a significant amount of sampling compute. As a result, we did not conduct an exhaustive sweep, and superior schedules are likely to exist.

| Schedule            | FID         |
|---------------------|-------------|
| 50, 50, 50, 50, 50  | 2.31        |
| 70, 60, 50, 40, 30  | 2.17        |
| 90, 50, 40, 40, 30  | 2.10        |
| 90, 60, 50, 30, 20  | 2.09        |
| 80, 60, 50, 30, 30  | 2.09        |
| 90, 50, 50, 30, 30  | 2.07        |
| 100, 50, 40, 30, 30 | 2.03        |
| 90, 60, 60, 20, 20  | <b>2.02</b> |

<span id="page-28-2"></span>Table 15: Results of sweeping over 250 step sampling schedules on LSUN bedrooms. The schedule is expressed as a sequence of five integers, where each integer is the number of steps allocated to one fifth of the diffusion process. The first integer corresponding to  $t \in [0, 199]$  and the last to  $t \in [T - 200, T - 1]$ . Thus, 50, 50, 50, 50, 50 is a uniform schedule, and 250, 0, 0, 0, 0, 0 is a schedule where all timesteps are spent near  $t = 0$ .

| Schedule                                       | FID  | sFID | Prec | Rec  |
|------------------------------------------------|------|------|------|------|
| <b>LSUN Bedrooms 256<math>\times</math>256</b> |      |      |      |      |
| 1000 steps                                     | 1.90 | 5.59 | 0.66 | 0.51 |
| 250 steps (uniform)                            | 2.31 | 6.12 | 0.65 | 0.50 |
| 250 steps (sweep)                              | 2.02 | 6.12 | 0.67 | 0.50 |
| <b>LSUN Horses 256<math>\times</math>256</b>   |      |      |      |      |
| 1000 steps                                     | 2.57 | 6.81 | 0.71 | 0.55 |
| 250 steps (uniform)                            | 3.45 | 7.55 | 0.68 | 0.56 |
| 250 steps (sweep)                              | 2.83 | 7.08 | 0.69 | 0.56 |
| <b>LSUN Cat 256<math>\times</math>256</b>      |      |      |      |      |
| 1000 steps                                     | 5.57 | 6.69 | 0.63 | 0.52 |
| 250 steps (uniform)                            | 7.03 | 8.24 | 0.60 | 0.53 |
| 250 steps (sweep)                              | 5.94 | 7.43 | 0.62 | 0.52 |

Table 16: Evaluations on LSUN bedrooms, horses, and cats using different sampling schedules. We find that the sweep schedule produces better results than the uniform 250 step schedule on all three datasets, and mostly bridges the gap to the 1000 step schedule.

<span id="page-29-0"></span>Image /page/29/Picture/0 description: This is a collage of 12 images. The top row shows four images of goldfish. The second row shows four images of arctic foxes, two close-ups of their faces and two of them in the snow. The third row shows four images of monarch butterflies on flowers. The fourth row shows three images of elephants, two of them in a group and one close-up of an elephant's head. The fifth row shows four images of flamingos, two close-ups of their necks and heads, and two of them standing in water. The bottom row shows two images of tennis balls, one image of a dog with a tennis ball in its mouth, and one image of a bottle of "Happy Belly" brand juice.

K Samples from ImageNet 512×512

Figure 13: Samples from our best 512×512 model (FID: 3.85). Classes are 1: goldfish, 279: arctic fox, 323: monarch butterfly, 386: african elephant, 130: flamingo, 852: tennis ball.

Image /page/30/Picture/0 description: This is a collage of 16 images arranged in a 4x4 grid. The top row features three close-up shots of hamburgers. The second row shows three images of fountains, one with a cityscape in the background, one at night with dramatic lighting, and one with a large plume of water. The third row displays a large blue balloon, a flock of hot air balloons against a blue sky, and a single hot air balloon with a cityscape in the background. The fourth row contains three close-up images of cats, two in color and one in black and white. The fifth row shows three colorful parrots, two in profile and one facing forward. The bottom row features three images of red and white spotted mushrooms, two in close-up detail.

Figure 14: Samples from our best 512×512 model (FID: 3.85). Classes are 933: cheeseburger, 562: fountain, 417: balloon, 281: tabby cat, 90: lorikeet, 992: agaric.

Image /page/31/Picture/0 description: This image is a collage of nine smaller images arranged in a 3x3 grid, with a caption below. The top row shows close-ups of bassoon reeds, a woman holding a bassoon, and a man playing a bassoon. The middle row displays various scenes involving yellow taxis and cars in what appears to be a city street, with one image showing a street sign that reads "MAINURE". The bottom row features three images of people riding bicycles, two of which are tandem bicycles, and one image of people playing baseball. The very bottom of the image shows three different coffee makers. The caption below the collage reads "Figure 15: Difficult class samples from our best 512x512 model (FID: 3.85). Classes are 432:". The collage appears to be a visual representation of different categories or classes of images, possibly used for evaluating a machine learning model.

Figure 15: Difficult class samples from our best 512×512 model (FID: 3.85). Classes are 432: bassoon, 468: cab, 424: barbershop, 444: bicycle-built-for-two, 981: ballplayer, 550: espresso maker.

Image /page/32/Picture/0 description: This is a grid of images showcasing various animals and objects. The top row features goldfish in water. The second row displays arctic foxes in snowy and rocky environments. The third row shows monarch butterflies on flowers. The fourth row depicts elephants, some in water and some on land. The fifth row presents flamingos in water. The bottom row contains close-ups of tennis balls and a small, possibly diseased, object on a leaf.

Figure 16: Samples from our guided 512×512 model using 250 steps with classifier scale 4.0 (FID 7.72). Classes are 1: goldfish, 279: arctic fox, 323: monarch butterfly, 386: african elephant, 130: flamingo, 852: tennis ball.

Image /page/33/Picture/0 description: This is a collage of 12 images arranged in a 4x3 grid. The top row features three images of burgers. The second row shows three images of fountains. The third row displays three images of hot air balloons. The fourth row contains three images of cats and three images of colorful birds. The bottom row shows three images of red and white spotted mushrooms.

Figure 17: Samples from our guided 512×512 model using 250 steps with classifier scale 4.0 (FID 7.72). Classes are 933: cheeseburger, 562: fountain, 417: balloon, 281: tabby cat, 90: lorikeet, 992: agaric.

Image /page/34/Picture/0 description: This is a collage of 24 images, arranged in a 4x6 grid. The images depict a variety of subjects including animals (dogs, a cat, a fox, a meerkat, a spider, a shark, a bird), objects (a white pickup truck, knee pads, a blue crystal, a piece of meat, a white candle with a bug, a red textured object, a white mattress, a church interior, a fireplace, a ski lift), and people (two people in protective suits, a person in a baseball uniform).

Figure 18: Random samples from our best ImageNet 512×512 model (FID 3.85).

Image /page/35/Picture/0 description: This is a collage of 32 images arranged in a 4x8 grid. The images depict a variety of subjects including animals (lizard, toucan, dog, ferret, monkey, komodo dragon, dog, bird, rabbit, fox, tarantula, bison), objects (microwave, basketball players, car, sunken ship, golf cart, bullet, truck, vest, pottery, pitcher, trolley, cauliflower, pocket watch, ship), and abstract designs (plate, stained glass).

Figure 19: Random samples from our guided 512×512 model using 250 steps with classifier scale 4.0 (FID 7.72).

Image /page/36/Picture/0 description: The image is a grid of 36 smaller images, arranged in 6 rows and 6 columns. The top row shows goldfish. The second row shows arctic foxes. The third row shows monarch butterflies. The fourth row shows elephants. The fifth row shows flamingos. The sixth row shows tennis balls. The seventh row shows hamburgers. The eighth row shows fountains. The ninth row shows hot air balloons. The tenth row shows cats. The eleventh row shows lorikeets. The twelfth row shows mushrooms. The caption at the bottom of the image reads "Figure 20: Samples using our best 256x256 model (FID 3.94). Classes are 1: goldfish, 279: arctic".

L Samples from ImageNet 256×256

Figure 20: Samples using our best 256×256 model (FID 3.94). Classes are 1: goldfish, 279: arctic fox, 323: monarch butterfly, 386: african elephant, 130: flamingo, 852: tennis ball, 933: cheeseburger, 562: fountain, 417: balloon, 281: tabby cat, 90: lorikeet, 992: agaric

Image /page/37/Picture/0 description: This is a grid of 35 images, arranged in 7 rows and 5 columns. The images depict a variety of subjects including goldfish, arctic foxes, monarch butterflies, elephants, flamingos, tennis balls, hamburgers, fountains, hot air balloons, cats, lorikeets, and mushrooms. The bottom of the image contains text that reads "Figure 21: Samples from our guided 256x256 model using 250 steps with classifier scale 1.0 (FID".

Figure 21: Samples from our guided 256×256 model using 250 steps with classifier scale 1.0 (FID 4.59). Classes are 1: goldfish, 279: arctic fox, 323: monarch butterfly, 386: african elephant, 130: flamingo, 852: tennis ball, 933: cheeseburger, 562: fountain, 417: balloon, 281: tabby cat, 90: lorikeet, 992: agaric

Image /page/38/Picture/0 description: This is a collage of 36 images arranged in a 6x6 grid. The images depict a variety of subjects including goldfish, arctic foxes, monarch butterflies, elephants, flamingos, tennis balls, hamburgers, fountains, hot air balloons, cats, parrots, and mushrooms. The top row features goldfish in water. The second row shows arctic foxes in snowy environments. The third row displays monarch butterflies on flowers. The fourth row presents elephants in natural settings. The fifth row captures flamingos in and around water. The sixth row shows tennis balls and other round objects. The seventh row displays various hamburgers. The eighth row features fountains in parks. The ninth row shows hot air balloons in the sky. The tenth row presents close-ups of cats. The eleventh row depicts colorful parrots. The bottom row features red and white mushrooms.

Figure 22: Samples from our guided 256×256 model using 25 DDIM steps with classifier scale 2.5 (FID 5.44). Classes are 1: goldfish, 279: arctic fox, 323: monarch butterfly, 386: african elephant, 130: flamingo, 852: tennis ball, 933: cheeseburger, 562: fountain, 417: balloon, 281: tabby cat, 90: lorikeet, 992: agaric

Image /page/39/Picture/0 description: This is a collage of 36 images, arranged in a 6x6 grid. The images depict a variety of subjects including animals (dogs, cats, birds, monkeys, a whale, a fox, a deer, a sheep, an orangutan, an axolotl, a butterfly, a duck, and a horse), buildings (a thatched-roof house, a modern building, and a brick building), natural scenes (a forest, a beach with a whale, a field with sheep, and a close-up of plants), objects (a rug, a corkscrew, a pink phone case, a golf ball, a fishing reel, a red chair, and a red bottle), and people (a child, two men, and a woman). Some images also include text or drawings, such as a pink drawing on a piece of paper and a drawing of a bird on a piece of paper.

Figure 23: Random samples from our best 256×256 model (FID 3.94).

Image /page/40/Picture/0 description: The image is a collage of 36 smaller images arranged in a 6x6 grid. The images depict a wide variety of subjects, including animals (dogs, cats, a bird, a peacock, a cheetah, a cow, a goose, a red panda, a monkey, a fox, a rabbit, a bison), objects (a wood stove, a trash can, a sailboat, a camera, a mortar and pestle, a pot, a jackfruit, a water tower, a piano, a golf cart, a horse-drawn carriage, a metal container, a pair of socks, a pair of lipstick tubes, a blue knitted heart, a blue rope), food items (vegetables, a bowl of food, a bowl of fruit, bell peppers), people (a person in a black shirt, a person in a white outfit, a person in a red shirt with the number 10, a person in a blue shirt, a person in a white outfit with a dog, people in a carriage, people in a golf cart), and natural scenes (a mountain landscape, a forest scene, a beach scene with a bird).

Figure 24: Random samples from our guided 256×256 model using 250 steps with classifier scale 1.0 (FID 4.59).

# <span id="page-41-0"></span>M Samples from LSUN

Image /page/41/Picture/1 description: This is a collage of 25 images of bedrooms. The images are arranged in a 5x5 grid. The bedrooms vary in style and decor, with some featuring modern designs and others more traditional or rustic elements. Several images showcase large, comfortable beds with various types of bedding and pillows. Some bedrooms have large windows with views of the outdoors, while others are more intimate and cozy. The lighting in the rooms ranges from bright and airy to dim and moody. Some of the bedrooms include additional furniture like nightstands, dressers, and seating areas. The overall impression is a diverse collection of bedroom interiors.

Figure 25: Random samples from our LSUN bedroom model using 1000 sampling steps. (FID 1.90)

Image /page/42/Picture/0 description: This is a collage of 24 images, each featuring horses in various settings and activities. The images include horses being ridden in competitions, close-ups of horses' heads, horses in fields, and horses with riders in different attire. Some images are in color, while others appear to be in black and white. The collage is arranged in a grid of 4 rows and 6 columns.

Figure 26: Random samples from our LSUN horse model using 1000 sampling steps. (FID 2.57)

Image /page/43/Picture/0 description: This is a collage of 24 images of cats and kittens. The images are arranged in a 4x6 grid. The cats are of various breeds, colors, and ages. Some of the cats are playing, sleeping, or looking at the camera. The collage also includes a few images of dogs. The overall impression is one of a collection of cute and playful animals.

Figure 27: Random samples from our LSUN cat model using 1000 sampling steps. (FID 5.57)