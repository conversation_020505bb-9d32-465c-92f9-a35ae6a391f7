# Appendix A. Dataset realism

In this section, we discuss the framework we use to assess the realism of a benchmark dataset. Realism is subtle to pin down and highly contextual, and assessing realism often requires consulting with domain experts and practitioners. As a general framework, we can view a benchmark dataset as comprising the data, a task and associated evaluation metric, and a train/test split that potentially reflects a distribution shift. Each of these components can independently be more or less realistic:

- 1. The **data**—which includes not just the inputs x but also any associated metadata (e.g., the domain that each data point came from)— is realistic if it accurately reflects what would plausibly be collected and available for a model to use in a real application. The realism of data also depends on the application context; for example, using medical images captured with state-of-the-art equipment might be realistic for well-equipped hospitals, but not necessarily for clinics that use older generations of the technology, or vice versa. Extreme examples of unrealistic data include the Gaussian distributions that are often used to cleanly illustrate the theoretical properties of various algorithms.
- 2. The task and evaluation metric is realistic if the task is relevant to a real application and if the metric measures how successful a model would be in that application. Here and with the other components, realism lies on a spectrum. For example, in a wildlife conservation application where the inputs are images from camera traps, the real task might be to estimate species populations (<PERSON><PERSON><PERSON> et al., 2017), i.e., the number of distinct individual animals of each species seen in the overall collection of images; a task that is less realistic but still relevant and useful for ecologists might be to classify what species of animal is seen in each image (Tabak et al., 2019). The choice of evaluation metric is also important. In the wildlife example, conservationists might care more about rare species than common species, so measuring average classification accuracy would be less realistic than a metric that prioritizes classifying the rare species correctly.
- 3. The distribution shift (train/test split) is realistic if it reflects training and test distributions that might arise in deployment for that dataset and task. For example, if a medical algorithm is trained on data from a few hospitals and then expected to be deployed more widely, then it would be realistic to test it on hospitals that are not in the training set. On the other hand, an example of a less realistic shift is to, for instance, train a pedestrian classifier entirely on daytime photos and then test it only on nighttime photos; in practice, any reasonable dataset for pedestrian detection that is used in a real application would include both daytime and nighttime photos.

Through the lens of this framework, existing ML benchmarks tend to focus on object recognition tasks with realistic data (e.g., photos) but not necessarily with realistic distribution shifts. With WILDS, we seek to address this gap by selecting datasets that represent a wide variety of tasks (with realistic evaluation metrics and data) and that reflect realistic distribution shifts, i.e., train/test splits that are likely to arise in real-world deployments.

To elaborate on the realism of the distribution shift, we associate each dataset in WILDS with the distribution shift (i.e., problem setting) that we believe best reflects the real-world challenges in the corresponding application area. For example, domain generalization is a realistic setting for the Camelyon17-wilds dataset as medical models are typically trained on data collected from a handful of hospitals, but with the goal of general deployment across different hospitals. On the other hand, subpopulation shift is appropriate for the CIVILCOMMENTS-WILDS dataset, as the real-world challenge is that some demographic subpopulations (domains) are underrepresented, rather than completely unseen, in the training data. The appropriate problem setting depends on many dataset-specific factors, but some common considerations include:

• Domain type. Certain types of domains are generally more appropriate for a particular setting. For example, if the domains represent time, as in FMOW-WILDS, then domain generalization

is suitable as a common challenge is to generalize from past data to future data. On the other hand, if the domains represent demographics and the goal is to improve performance on minority subpopulations, as in CivilComments-wilds, then subpopulation shift is typically more appropriate.

- Data collection challenges. When collecting data from a new domain is expensive, domain generalization is often appropriate, as we might want to train on data from a limited number of domains but still generalize to unseen domains. For example, it is difficult to collect patient data from multiple hospitals, as in CAMELYON17-WILDS, or survey data from new countries, as in POVERTYMAP-WILDS.
- Continuous addition of new domains. A special case of the above is when new domains are continuously created. For example, in Amazon-wilds, where domains correspond to users, new users are constantly signing up for the platform; and in IWILDCAM2020-WILDS, where domains correspond to camera traps, new cameras are constantly being deployed. These are natural domain generalization settings.

## Appendix B. Prior work on ML benchmarks for distribution shifts

In this section, we discuss existing ML distribution shift benchmarks in more detail, categorizing them by how they induce their respective distribution shifts. We focus here on work that has appeared in ML conferences and journals; we discuss related work from other research communities in Section 8 and Appendix [E.](#page-6-0) We also restrict our attention to publicly-available datasets. While others have studied some proprietary datasets with realistic distribution shifts, such as the StreetView StoreFronts dataset (Hendrycks et al., 2020b) or diabetic retinopathy datasets (D'Amour et al., 2020a), these datasets are not publicly available due to privacy and other commercial reasons.

Distribution shifts from transformations. Some of the most widely-adopted benchmarks induce distribution shifts by synthetically transforming the data. Examples include rotated and translated versions of MNIST and CIFAR (Worrall et al., 2017; Gulrajani and Lopez-Paz, 2020); surface variations such as texture, color, and corruptions like blur in Colored MNIST (Gulrajani and Lopez-Paz, 2020), Stylized ImageNet (Geirhos et al., 2018a), ImageNet-C (Hendrycks and Dietterich, 2019), and similar ImageNet variants (Geirhos et al., 2018b); and datasets that crop out objects and replace their backgrounds, as in the Backgrounds Challenge (Xiao et al., 2020) and other similar datasets (Sagawa et al., 2020a; Koh et al., 2020). Benchmarks for adversarial robustness also fall in this category of distribution shifts from transformations (Goodfellow et al., 2015; Croce et al., 2020). Though adversarial robustness is not a focus of this work, we note that recent work on temporal perturbations with the ImageNet-Vid-Robust and YTBB-Robust datasets (Shankar et al., 2019) represents a different form of distribution shift that also impacts real-world applications. Outside of visual object recognition, other work has used synthetic datasets and transformations to explore compositional generalization, e.g., SCAN (Lake and Baroni, 2018). We discuss this more in Section 8.

Synthetic-to-real transfers. Fully synthetic datasets such as SYNTHIA (Ros et al., 2016) and StreetHazards (Hendrycks et al., 2020a) have been adopted for out-of-distribution detection as well as domain adaptation and generalization, e.g., by testing robustness to transformations in the seasons, weather, time, or architectural style (Hoffman et al., 2018; Volpi et al., 2018). While the data is synthetic, it can still look realistic if a high-fidelity simulator is used. In particular, synthetic benchmarks that study transfers from synthetic to real data (Ganin and Lempitsky, 2015; Richter et al., 2016; Peng et al., 2018) can be important tools for tackling real-world problems: even though the data is synthesized and by definition, not real, the synthetic-to-real distribution shift can still be realistic in contexts where real data is much harder to acquire than synthetic data (Bellemare et al., 2020). In this work, we do not study these types of synthetic distribution shifts; instead, we focus on distribution shifts that occur in the wild between real data distributions.

Distribution shifts from constrained splits. Other benchmarks do not rely on transformations but instead split the data in a way that induces particular distribution shifts. These benchmarks have realistic data, e.g., the data points are derived from real-world photos, but they do not necessarily reflect distribution shifts that would arise in the wild. For example, BREEDS (Santurkar et al., 2020) and a related dataset (Hendrycks and Dietterich, 2019) test generalization to unseen ImageNet subclasses by holding out subclasses specified by several controllable parameters; similarly, NICO (He et al., 2020) considers subclasses that are defined by their context, such as dogs at home versus dogs on the beach; DeepFashion-Remixed (Hendrycks et al., 2020b) constrains the training set to include only photos from a single camera viewpoint and tests generalization to unseen camera viewpoints; BDD-Anomaly (Hendrycks et al., 2020a) uses a driving dataset but with all motorcycles, trains, and bicycles removed from the training set only; and ObjectNet (Barbu et al., 2019) comprises images taken from a few pre-specified viewpoints, allowing for systematic evaluation for robustness to camera angle changes but deviating from natural camera angles.

Distribution shifts across datasets. A well-studied special case of the above category is the class of distribution shifts obtained by combining several disparate datasets (Torralba and Efros, 2011), training on one or more of them and then testing on the remaining datasets. A recent influential example is the ImageNetV2 dataset (Recht et al., 2019), which was constructed to be similar to the original ImageNet dataset. Unlike ImageNetV2, however, many of these distribution shifts were constructed to be more drastic than might arise in the wild. For example, standard domain adaptation benchmarks include training on MNIST but testing on SVHN street signs (LeCun et al., 1998; Yuval et al., 2011; Tzeng et al., 2017; Hoffman et al., 2018), as well as transfers across datasets containing different renditions (e.g., photos, clipart, sketches) in DomainNet (Peng et al., 2019) and the Office-Home dataset (Venkateswara et al., 2017).

The main difference between domain adaptation and domain generalization is that in the latter, we do not assume access to unlabeled data from the test distribution. This makes it straightforward to use domain adaptation benchmarks for domain generalization, e.g., in DomainBed (Gulrajani and Lopez-Paz, 2020); we focus on domain generalization in this work, but further discuss unsupervised domain adaptation in Section [C.](#page-2-0) Other similar benchmarks that have been proposed for domain generalization include VLCS (Fang et al., 2013), which tests generalization across similar visual object recognition datasets; PACS (Li et al., 2017a), which (like DomainNet) tests generalization across datasets with different renditions; and ImageNet-R (Hendrycks et al., 2020b) and ImageNet-Sketch (Wang et al., 2019c), which also test generalization across different renditions by collecting separate datasets from Flickr and Google Image queries.

## <span id="page-2-0"></span>Appendix C. Potential extensions to other problem settings

In this paper, we have focused on two problem settings involving domain shifts: domain generalization and subpopulation shifts. Here, we discuss other problem settings within the framework of domain shifts that could also apply to WILDS datasets. Using WILDS to benchmark and develop algorithms for these settings is an important avenue for future work, and we welcome community contributions towards this effort.

#### C.1 Problem settings in domain shifts

Within the general framework of domain shifts, specific problem settings can differ along the following axes of variation:

1. Seen versus unseen test domains. Test domains may be seen during training time ( $\mathcal{D}^{\text{test}} \subseteq$  $\mathcal{D}^{\text{train}}$ ), as in subpopulation shift, or unseen  $(\mathcal{D}^{\text{train}} \cap \mathcal{D}^{\text{test}} = \emptyset)$ , as in domain generalization. The domain generalization and subpopulation shift settings mainly differ on this factor.

- 2. Train-time domain annotations. The domain identity  $d$  may be observed for none, some, or all of the training examples. Train-time domain annotations are straightforward to obtain in some settings, e.g., we should know which patients in the training sets came from which hospitals, but can be harder to obtain in some settings, e.g., we might only have demographic information on a subset of training users. In our domain generalization and subpopulation shift settings, d is always observed at training time.
- 3. Test-time domain annotations. The domain identity  $d$  may be observed for none, some, or all of the test examples. Test-time domain annotations allow models to be domain-specific, e.g., by treating domain identity as a feature if the train and test domains overlap. For example, if the domains correspond to continents and the data to satellite images from a continent, we would presumably know what continent each image was taken from. On the other hand, if the domains correspond to demographic information, this might be hard to obtain at test time (as well as training time, as mentioned above). In domain generalization, d may be observed at test time, but it is not helpful by itself as all of the test domains are unseen at training time. However, when combined with test-time unlabeled data, observing the domain d at test time could help with adaptation. In subpopulation shift, we typically assume that  $d$  is unobserved at test time, though this need not always be true.
- 4. Test-time unlabeled data. Varying amounts of unlabeled test data—samples of x drawn from the test distribution  $P<sup>test</sup>$ —may be available, from none to a small batch to a large pool. This affects the degree to which models can adapt to test distributions. For example, if the domains correspond to locations and the data points to photos taken at those locations, we might assume access to some unlabeled photos taken at the test locations.

Each combination of the above four factors corresponds to a specific problem setting with a different set of applicable methods. In the current version of the WILDS benchmark, we focus on domain generalization and subpopulation shifts, which represent specific configurations of these factors. We briefly discuss a few other problem settings in the remainder of this section.

### C.2 Unsupervised domain adaptation

In the presence of distribution shift, a potential source of leverage is observing unlabeled test points from the test distribution. In the unsupervised domain adaptation setting, we assume that at training time, we have access to a large amount of unlabeled data from each test distribution of interest, as well as the resources to train a separate model for each test distribution. For example, in a satellite imagery setting like FMOW-WILDS, it might be appropriate to assume that we have access to a large set of unlabeled recent satellite images from each continent and the wherewithal to train a separate model for each continent.

Many of the methods for domain generalization discussed in Section 6 were originally methods for domain adaptation, since methods for both settings share the common goal of learning models that can transfer between domains. For example, methods that learn features that have similar distributions across domains are equally applicable to both settings (Ben-David et al., 2006; Long et al., 2015; Sun et al., 2016; Ganin et al., 2016; Tzeng et al., 2017; Shen et al., 2018; Wu et al., 2019b). In fact, the CORAL algorithm that we use as a baseline in this work was originally developed for, and successfully applied in, unsupervised domain adaptation (Sun and Saenko, 2016). Other methods rely on knowing the test distribution and are thus specific to domain adaptation, e.g., learning to map data points from source to target domains (Hoffman et al., 2018), or estimating the test label distribution from unlabeled test data (Saerens et al., 2002; Zhang et al., 2013; Lipton et al., 2018; Azizzadenesheli et al., 2019; Alexandari et al., 2020; Garg et al., 2020).

### C.3 Test-time adaptation

A closely related setting to unsupervised domain adaptation is test-time adaptation, which also assumes the availability of unlabeled test data. For datasets where there are many potential test domains (e.g., in IWILDCAM2020-WILDS, we want a model that can ideally generalize to any camera trap), it might be infeasible to train a separate model for each test domain, as unsupervised domain adaptation would require. In the test-time adaptation setting, we assume that a model is allowed to adapt to a small amount of unlabeled test data in a way that is computationally much less intensive than typical domain adaptation methods. This is a difference of degree and not of kind, but it can have significant practical implications. For example, domain adaptation approaches typically require access to the training set and a large unlabeled test set at the same time, whereas test-time adaptation methods typically only require the learned model (which can be much smaller than the original training set) as well as a smaller amount of unlabeled test data.

A number of test-time adaptation methods have been recently proposed (Li et al., 2017c; Sun et al., 2020b; Wang et al., 2020a). For example, adaptive risk minimization (ARM) is a meta-learning approach that adapts models to each batch of test examples under the assumption that all data points in a batch come from the same domain (Zhang et al., 2020). Many datasets in WILDS are suitable for the test-time adaptation setting. For example, in IWILDCAM2020-WILDS, images from the same domain are highly similar, sharing the same location, background, and camera angle, and prior work has shown inferring these shared features can improve performance considerably (Beery et al., 2020b).

### C.4 Selective prediction

A different problem setting that is orthogonal to the settings described above is selective prediction. In the selective prediction setting, models are allowed to abstain on points where their confidence is below a certain threshold. This is appropriate when, for example, abstentions can be handled by backing off to human experts, such as pathologists for Camelyon17-wilds, content moderators for CIVILCOMMENTS-WILDS, wildlife experts for IWILDCAM2020-WILDS, etc. Many methods for selective prediction have been developed, from simply using softmax probabilities as a proxy for confidence (Cordella et al., 1995; Geifman and El-Yaniv, 2017), to methods involving ensembles of models (Gal and Ghahramani, 2016; Lakshminarayanan et al., 2017; Geifman et al., 2018) or jointly learning to abstain and classify (Bartlett and Wegkamp, 2008; Geifman and El-Yaniv, 2019; Feng et al., 2019).

Intuitively, even if a model is not robust to a distribution shift, it might at least be able to maintain high accuracies on some subset of points that are close to the training distribution, while abstaining on the other points. Indeed, prior work has shown that selective prediction can improve model accuracy under distribution shifts (Pimentel et al., 2014; Hendrycks and Gimpel, 2017; Liang et al., 2018; Ovadia et al., 2019; Feng et al., 2019; Kamath et al., 2020). However, distribution shifts still pose a problem for selective prediction methods; for instance, it is difficult to maintain desired abstention rates under distribution shifts (Kompa et al., 2020), and confidence estimates have been found to drift over time (e.g., Davis et al. (2017)).

<span id="page-4-0"></span>

## Appendix D. Additional experimental details

## D.1 Model hyperparameters

For each hyperparameter setting, we used early stopping to pick the epoch with the best OOD validation performance (as measured by the specified metrics for each dataset described in Section 4), and then picked the model hyperparameters with the best early-stopped validation performance. We found that this gave similar or slightly better OOD test performance than selecting hyperparameters using the ID validation set (Table [3\)](#page-5-0).

<span id="page-5-0"></span>Table 3: The performance of models trained with empirical risk minimization with hyperparameters tuned using the out-of-distribution (OOD) vs. in-distribution (ID) validation set. We excluded OGB-MOLPCBA, RXRX1-WILDS, and GLOBALWHEAT-WILDS, as they do not have separate ID validation sets, and CIVILCOMMENTS-WILDS, which is a subpopulation shift setting where we measure worst-group accuracy on a validation set that is already identically distributed to the training set.

| Dataset            | Metric              | ID performance  |                  | OOD performance |                  |
|--------------------|---------------------|-----------------|------------------|-----------------|------------------|
|                    |                     | Tuned on ID val | Tuned on OOD val | Tuned on ID val | Tuned on OOD val |
| iWildCAM2020-WILDS | Macro F1            | 47.2 (2.0)      | 47.0 (1.4)       | 29.8 (0.6)      | 31.0 (1.3)       |
| CAMELYON17-WILDS   | Average acc         | 98.7 (0.1)      | 93.2 (5.2)       | 65.8 (4.9)      | 70.3 (6.4)       |
| FMOW-WILDS         | Worst-region acc    | 58.0 (0.5)      | 57.4 (0.2)       | 31.9 (0.8)      | 32.8 (0.5)       |
| POVERTYMAP-WILDS   | Worst-U/R Pearson R | 0.65 (0.03)     | 0.62 (0.04)      | 0.46 (0.06)     | 0.46 (0.07)      |
| AMAZON-WILDS       | 10th percentile acc | 72.0 (0.0)      | 71.9 (0.1)       | 53.8 (0.8)      | 53.8 (0.8)       |
| Py150-WILDS        | Method/class acc    | 75.6 (0.0)      | 75.4 (0.4)       | 67.9 (0.1)      | 67.9 (0.1)       |

Using the OOD validation set for early stopping means that even if the training procedure does not explicitly use additional metadata, as in ERM, the metadata might still be implicitly (but mildly) used for model selection in one of two related ways. First, the metric might use the metadata directly (e.g., by computing the accuracy over different subpopulations defined in the metadata). Second, the OOD validation set is generally selected according to this metadata (e.g., comprising data from a disjoint set of domains as the training set). We expect that implicitly using the metadata in these ways should increase the OOD performance of each model. Nevertheless, as Sections 5 and 6 show, there are still large gaps between OOD and ID performance.

In general, we selected model hyperparameters with ERM and used the same hyperparameters for the other algorithm baselines (e.g., CORAL, IRM, or Group DRO). For CORAL and IRM, we did a subsequent grid search over the weight of the penalty term, using the defaults from Gulrajani and Lopez-Paz (2020). Specifically, we tried penalty weights of  $\{0.1, 1, 10\}$  for CORAL and penalty weights of  $\{1, 10, 100, 1000\}$  for IRM. We fixed the step size hyperparameter for Group DRO to its default value of 0.01 (Sagawa et al., 2020a).

#### D.2 Replicates

We typically use a fixed train/validation/test split and report results averaged across 3 replicates (random seeds for model initialization and minibatch order), as well as the unbiased standard deviation over those replicates. There are three exceptions to this. For POVERTYMAP-WILDS, we report results averaged over 5-fold cross validation, as model training is relatively fast on this dataset. For Camelyon17-wilds, results vary substantially between replicates, so we report results averaged over 10 replicates instead. Similarly, for CivilComments-wilds, we report results averaged over 5 replicates.

### D.3 Baseline algorithms

For all classification datasets, we train models against the cross-entropy loss. For the POVERTYMAPwilds regression dataset, we use the mean-squared-error loss.

We adapted the implementations of CORAL from Gulrajani and Lopez-Paz (2020); IRM from Arjovsky et al. (2019); and Group DRO from Sagawa et al. (2020a). We note that CORAL was originally proposed in the context of domain adaptation (Sun and Saenko, 2016), where it was shown to substantially improve performance on standard domain adaptation benchmarks, and it was subsequently adapted for domain generalization (Gulrajani and Lopez-Paz, 2020).

Following these implementations, we use minibatch stochastic optimizers to train models under each algorithm, and we sample uniformly from each domain regardless of the number of training examples in it. This means that the CORAL and IRM algorithms optimize for their respective penalty terms plus a reweighted ERM objective that weights each domain equally (i.e., effectively upweighting minority domains). The Group DRO objective is unchanged, as it still optimizes for the domain with the worst loss, but the uniform sampling improves optimization stability.

Both CORAL and IRM are designed for models with featurizers, i.e., models that first map each input to a feature representation and then predict based on the representation. To estimate the feature distribution for a domain, these algorithms need to see a sufficient number of examples from that domain in a minibatch. However, some of our datasets have large numbers of domains, making it infeasible for each minibatch to contain examples from all domains. For these algorithms, our data loaders form a minibatch by first sampling a few domains, and then sampling examples from those domains. For consistency in our experiments, we used the same total batch size for these algorithms and for ERM and Group DRO, with a default of 8 examples per domain in each minibatch (e.g., if the batch size was 32, then in each minibatch we would have  $8$  examples  $\times$  4 domains).

For Group DRO, as in Sagawa et al. (2020a), each example in the minibatch is sampled independently with uniform probabilities across domains, and therefore each minibatch does not need to only comprise a small number of domains. We note that reweighting methods like Group DRO are effective only when the training loss is non-vanishing, which we achieve through early stopping (Byrd and Lipton, 2019; Sagawa et al., 2020a,b).

## <span id="page-6-0"></span>Appendix E. Additional dataset details and results

In this section, we discuss each WILDS dataset in more detail. For completeness, we start by repeating the motivation behind each dataset from Section 4. We then describe the task, the distribution shift, and the evaluation criteria, and present baseline results that elaborate upon those in Sections 5 and 6. We also discuss the broader context behind each dataset and how it connects with other distribution shifts in similar applications. Finally, we describe how each dataset was modified from its original version in terms of the evaluation, splits, and data. Unless otherwise specified, all experiments follow the protocol laid out in Appendix [D.](#page-4-0)

#### E.1 iWildCam2020-wilds

Animal populations have declined 68% on average since 1970 (Grooten et al., 2020). To better understand and monitor wildlife biodiversity loss, ecologists commonly deploy camera traps—heat or motion-activated static cameras placed in the wild (Wearn and Glover-Kapfer, 2017)—and then use ML models to process the data collected (Weinstein, 2018; Norouzzadeh et al., 2019; Tabak et al., 2019; Beery et al., 2019; Ahumada et al., 2020). Typically, these models would be trained on photos from some existing camera traps and then used across new camera trap deployments. However, across different camera traps, there is drastic variation in illumination, color, camera angle, background, vegetation, and relative animal frequencies, which results in models generalizing poorly to new camera trap deployments (Beery et al., 2018).

We study this shift on a variant of the iWildCam 2020 dataset (Beery et al., 2020a).

### E.1.1 SETUP

Problem setting. We consider the domain generalization setting, where the domains are camera traps, and we seek to learn models that generalize to photos taken from new camera deployments (Figure 3). The task is multi-class species classification. Concretely, the input x is a photo taken by a camera trap, the label  $y$  is one of 182 different animal species, and the domain  $d$  is an integer that identifies the camera trap that took the photo.

Data. The dataset comprises 203,029 images from 323 different camera traps spread across multiple countries in different parts of the world. The original camera trap data comes from the Wildlife Conservation Society (<http://lila.science/datasets/wcscameratraps>). These images tend to be taken in short bursts following motion-activation of a camera trap, so the images can be additionally

<span id="page-7-0"></span>Image /page/7/Figure/0 description: This is a bar chart titled "# examples per location (sorted)". The x-axis is labeled "Location (sorted)" and ranges from 0 to 300. The y-axis is labeled "Count" and ranges from 0 to 8000. The chart displays a distribution of counts for different locations, with the counts generally increasing as the location value increases. The bars are very low for locations up to around 200, then gradually increase, and finally show a sharp rise for locations above 300, reaching a peak count of over 8000 at the highest location value.

Figure 16: Number of examples per location in the *IWILDCAM2020*-WILDS dataset. The locations are sorted such that locations with the least amount of examples are to the left on the x-axis.

grouped into sequences of images from the same burst, though our baseline models do not exploit this information and our evaluation metric treats each image individually. Each image is associated with the following metadata: camera trap ID, sequence ID, and datetime.

As is typical for camera trap data, approximately 35% of the total number of images are empty (i.e., do not contain any animal species); this corresponds to one of the 182 class labels. The ten most common classes across the full dataset are "empty" (34%), ocellated turkey (8%), great curassow (6%), impala (4%), black-fronted duiker (4%), white-lipped peccary (3%), Central American agouti  $(3\%)$ , ocelot  $(3\%)$ , gray fox  $(2\%)$  and cow  $(2\%)$ .

We note that the labels in this dataset can be somewhat noisy, as is typical of camera trap data. Some ecologists might label all images in a sequence as the same animal (which can result in empty/dark frames being labeled as an animal), whereas other ecologists might try to label it frame-by-frame. This label noise imposes a natural ceiling on model performance, though the label noise is equally present in ID vs. OOD data.

We split the dataset by randomly partitioning the data by camera traps:

- 1. Training: 129,809 images taken by 243 camera traps.
- 2. Validation (OOD): 14,961 images taken by 32 different camera traps.
- 3. Test (OOD): 42,791 images taken by 48 different camera traps.
- 4. Validation (ID): 7,314 images taken by the same camera traps as the training set, but on different days from the training and test (ID) images.
- 5. Test (ID): 8,154 images taken by the same camera traps as the training set, but on different days from the training and validation (ID) images.

The camera traps are randomly distributed across the training, validation (OOD), and test (OOD) sets. The number of examples per location vary widely from 1 to 8494, with a median of 194 images (Figure [16\)](#page-7-0). All images from the same sequence (i.e., all images taken in the same burst) are placed together in the same split. See Appendix E.1.4 for more details.

Evaluation. We evaluate models by their macro F1 score (i.e., we compute the F1 score for each class separately, then average those scores). We also report the average accuracy of each model across all test images, but primarily use the macro F1 score to better capture model performance on rare species. In the natural world, protected and endangered species are rare by definition, and are often the most important to accurately monitor. However, common species are much more likely

<span id="page-8-0"></span>Image /page/8/Figure/0 description: This image displays six histograms, arranged in a 2x3 grid, illustrating class label distributions across different data splits. The top row shows 'All splits' on the left and 'Train' on the right. The middle row shows 'Validation (OOD)' on the left and 'Test (OOD)' on the right. The bottom row shows 'Validation (ID)' on the left and 'Test (ID)' on the right. Each histogram has 'Class label' on the x-axis, ranging from 0 to 175, and 'Frequency' on the y-axis, ranging from 0.0 to 0.4. All histograms show a prominent peak at class label 0, with frequencies around 0.35. Other smaller peaks are visible at various class labels, notably around 150 in the 'Train', 'Validation (OOD)', 'Test (OOD)', and 'Validation (ID)' plots, and around 125 in the 'Validation (ID)' and 'Test (ID)' plots.

Figure 17: Label distribution for each IWILDCAM2020-WILDS split.

to be captured in camera trap images; this imbalance can make metrics like average accuracy an inaccurate picture of model effectiveness.

Potential leverage. Though the problem is challenging for existing ML algorithms, adapting to photos from different camera traps is simple and intuitive for humans. Repeated backgrounds and habitual animals, which cause each sensor to have a unique class distribution, provide a strong implicit signal across data from any one location. We anticipate that approaches that utilize the provided camera trap annotations can learn to factor out these common features and avoid learning spurious correlations between particular backgrounds and animal species.

#### E.1.2 Baseline results

Model. For all experiments, we use ResNet-50 models (He et al., 2016) that were pretrained on ImageNet, using a learning rate of 3e-5 and no  $L_2$ -regularization. As input, these models take in images resized to 448 by 448. We trained these models with the Adam optimizer and a batch size of 16 for 12 epochs. To pick hyperparameters, we did a grid search over learning rates  ${1 \times 10^{-5}, 3 \times 10^{-5}, 1 \times 10^{-4}}$  and  $L_2$  regularization strengths  ${0, 1 \times 10^{-3}, 1 \times 10^{-2}}$ . We report results aggregated over 3 random seeds.

ERM results and performance drops. Model performance dropped substantially and consistently going from the train-to-train in-distribution (ID) setting to the official out-of-distribution (OOD) setting (Table [4\)](#page-9-0), with a macro F1 score of 47.0 on the ID test set but only 31.0 on the OOD test set. We note that macro F1 and average accuracy both differ between the OOD validation and test sets: this is in part due to the difference in class balance between them, which in turn is due to differences in the proportion of classes across camera traps. In particular, macro F1 can vary between

| Algorithm          | Val (ID)   |            | Val (OOD)  |            | Test (ID)         |                   | Test (OOD)        |                   |
|--------------------|------------|------------|------------|------------|-------------------|-------------------|-------------------|-------------------|
|                    | Macro F1   | Avg acc    | Macro F1   | Avg acc    | Macro F1          | Avg acc           | Macro F1          | Avg acc           |
| ERM                | 48.8 (2.5) | 82.5 (0.8) | 37.4 (1.7) | 62.7 (2.4) | <b>47.0</b> (1.4) | <b>75.7</b> (0.3) | 31.0 (1.3)        | 71.6 (2.5)        |
| CORAL              | 46.7 (2.8) | 81.8 (0.4) | 37.0 (1.2) | 60.3 (2.8) | 43.5 (3.5)        | 73.7 (0.4)        | <b>32.8</b> (0.1) | <b>73.3</b> (4.3) |
| IRM                | 24.4 (8.4) | 66.9 (9.4) | 20.2 (7.6) | 47.2 (9.8) | 22.4 (7.7)        | 59.9 (8.1)        | 15.1 (4.9)        | 59.8 (3.7)        |
| Group DRO          | 42.3 (2.1) | 79.3 (3.9) | 26.3 (0.2) | 60.0 (0.7) | 37.5 (1.7)        | 71.6 (2.7)        | 23.9 (2.1)        | 72.7 (2.0)        |
| Reweighted (label) | 42.5 (0.5) | 77.5 (1.6) | 30.9 (0.3) | 57.8 (2.8) | 42.2 (1.4)        | 70.8 (1.5)        | 26.2 (1.4)        | 68.8 (1.6)        |

<span id="page-9-0"></span>Table 4: Baseline results on IWILDCAM2020-WILDS. In-distribution (ID) results correspond to the train-totrain setting. Parentheses show standard deviation across 3 replicates.

splits because we take the average F1 score across all classes that are present in the evaluation split, and not all splits have the same classes present (e.g., a rare species might be in the OOD validation set but not OOD test set, or vice versa). In additional, average accuracy can differ between splits due in part to variation in the fraction of empty images per location (e.g., the camera traps that were randomly assigned to the OOD validation set have a smaller proportion of empty images).

We only ran a train-to-train comparison because there are a relatively large number of domains (camera traps) split i.i.d. between the training and test sets, which suggests that the training and test sets should be "equally difficult". The size of the ID-OOD gap in macro F1 is large enough that we expect it should hold up even in a test-to-test comparison. However, the results in Table [4](#page-9-0) and Figure [17](#page-8-0) show that there is substantial variability between domains, and it would be useful for future work to establish the magnitude of the ID-OOD gap under the test-to-test or mixed-to-test comparisons.

Additional baseline methods. We trained models with CORAL, IRM, and Group DRO, treating each camera trap as a domain, and using the same model hyperparameters as ERM. These did not improve upon the ERM baseline (Table [4\)](#page-9-0). The IRM models performed especially poorly on this dataset; we suspect that this is because the default estimator of the IRM penalty term can be negatively biased when examples are sampled without replacement from small domains, but further investigation is needed. We also tried reweighting the training data so that each label had equal weight, but this did not improve over ERM either.

Discussion. Across locations, there is drastic variation in illumination, camera angle, background, vegetation, and color. This variation, coupled with considerable differences in the distribution of animals between camera traps, likely encourages the model to overfit to specific animal species appearing in specific locations, which may account for the performance drop.

The original iWildCam 2020 competition allows users to use MegaDetector (Beery et al., 2019), which is an animal detector trained on a large set of data beyond what is provided in the training set. Using an animal detection model like MegaDetector typically improves classification performance on camera traps (Beery et al., 2018). However, we intentionally do not use MegaDetector in our baselines for IWILDCAM2020-WILDS for two reasons. First, though the trained MegaDetector model is publicly available, the MegaDetector training set is not, which makes it difficult to build on top of it and run controlled experiments. Second, bounding box annotations are costly and harder to obtain, and there is much more data with image-level species label, so it would be useful to be able to train models that do not have to rely on bounding box annotations.

We still welcome leaderboard submissions that use MegaDetector, as it is useful to see how much better models can perform when they use MegaDetector or other similar animal detectors, but we will distinguish these submissions from others that only use what is provided in the training set.

A different source of leverage comes from the temporal signal in the camera trap images, which are organized into sequences that each correspond to a burst of images from a single motion trigger. Using this sequence information (e.g., by taking the median prediction across a sequence) can also