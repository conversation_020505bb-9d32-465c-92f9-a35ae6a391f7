{"table_of_contents": [{"title": "214 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 1, "polygon": [[132.0, 89.25], [293.748046875, 89.25], [293.748046875, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "6.8 Mixture Models for Density Estimation and\nClassification", "heading_level": null, "page_id": 1, "polygon": [[132.0, 207.75], [435.69140625, 207.75], [435.69140625, 236.671875], [132.0, 236.671875]]}, {"title": "216 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 3, "polygon": [[132.0, 88.5], [293.25, 88.5], [293.25, 99.0], [132.0, 99.0]]}, {"title": "6.9 Computational Considerations", "heading_level": null, "page_id": 3, "polygon": [[132.0, 111.0], [354.41015625, 111.0], [354.41015625, 124.13671875], [132.0, 124.13671875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 3, "polygon": [[132.0, 354.427734375], [255.19921875, 354.427734375], [255.19921875, 367.576171875], [132.0, 367.576171875]]}, {"title": "Exercises", "heading_level": null, "page_id": 3, "polygon": [[132.0, 501.0], [189.9052734375, 501.0], [189.9052734375, 513.5625], [132.0, 513.5625]]}, {"title": "218 6. <PERSON><PERSON> Methods", "heading_level": null, "page_id": 5, "polygon": [[132.0, 87.75], [294.0, 87.75], [294.0, 99.0], [132.0, 99.0]]}, {"title": "7\nModel Assessment and Selection", "heading_level": null, "page_id": 6, "polygon": [[132.0, 110.25], [402.0, 110.25], [402.0, 161.26171875], [132.0, 161.26171875]]}, {"title": "7.1 Introduction", "heading_level": null, "page_id": 6, "polygon": [[132.0, 354.0], [243.24609375, 354.0], [243.24609375, 367.576171875], [132.0, 367.576171875]]}, {"title": "7.2 <PERSON><PERSON>, <PERSON><PERSON><PERSON> and Model Complexity", "heading_level": null, "page_id": 6, "polygon": [[132.0, 513.75], [400.130859375, 513.75], [400.130859375, 527.09765625], [132.0, 527.09765625]]}, {"title": "222 7. Model Assessment and Selection", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "7.3 The Bias–<PERSON><PERSON><PERSON> Decomposition", "heading_level": null, "page_id": 10, "polygon": [[132.0, 222.0], [376.5234375, 222.0], [376.5234375, 234.931640625], [132.0, 234.931640625]]}, {"title": "226 7. Model Assessment and Selection", "heading_level": null, "page_id": 13, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "7.3.1 Example: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>off", "heading_level": null, "page_id": 13, "polygon": [[133.5, 207.75], [343.0546875, 207.75], [343.0546875, 219.26953125], [133.5, 219.26953125]]}, {"title": "7.4 Optimism of the Training Error Rate", "heading_level": null, "page_id": 15, "polygon": [[132.0, 160.875], [395.349609375, 160.875], [395.349609375, 175.376953125], [132.0, 175.376953125]]}, {"title": "230 7. Model Assessment and Selection", "heading_level": null, "page_id": 17, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "7.5 Estimates of In-Sample Prediction Error", "heading_level": null, "page_id": 17, "polygon": [[132.75, 314.208984375], [415.37109375, 314.208984375], [415.37109375, 327.357421875], [132.75, 327.357421875]]}, {"title": "7.6 The Effective Number of Parameters", "heading_level": null, "page_id": 19, "polygon": [[132.0, 441.0], [391.5, 441.0], [391.5, 455.16796875], [132.0, 455.16796875]]}, {"title": "7.7 The Bayesian Approach and BIC", "heading_level": null, "page_id": 20, "polygon": [[132.0, 388.5], [369.75, 388.5], [369.75, 401.4140625], [132.0, 401.4140625]]}, {"title": "234 7. Model Assessment and Selection", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 99.0], [132.0, 99.0]]}, {"title": "7.8 Minimum Description Length", "heading_level": null, "page_id": 22, "polygon": [[132.0, 323.25], [348.0, 323.25], [348.0, 336.4453125], [132.0, 336.4453125]]}, {"title": "236 7. Model Assessment and Selection", "heading_level": null, "page_id": 23, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "7.9 Vapnik–Chervonenkis Dimension", "heading_level": null, "page_id": 24, "polygon": [[132.75, 421.91015625], [369.3515625, 421.91015625], [369.3515625, 435.83203125], [132.75, 435.83203125]]}, {"title": "7.9.1 Example (Continued)", "heading_level": null, "page_id": 26, "polygon": [[133.4267578125, 560.25], [280.5, 560.25], [280.5, 572.34375], [133.4267578125, 572.34375]]}, {"title": "7.10 Cross-Validation", "heading_level": null, "page_id": 28, "polygon": [[132.60498046875, 411.75], [273.75, 411.75], [273.75, 425.00390625], [132.60498046875, 425.00390625]]}, {"title": "7.10.1 K-Fold Cross-Validation", "heading_level": null, "page_id": 28, "polygon": [[133.5, 560.25], [303.908203125, 560.25], [303.908203125, 572.34375], [133.5, 572.34375]]}, {"title": "242 7. Model Assessment and Selection", "heading_level": null, "page_id": 29, "polygon": [[132.0, 88.5], [314.068359375, 88.5], [314.068359375, 98.56494140625], [132.0, 98.56494140625]]}, {"title": "7.10.2 The Wrong and Right Way to Do Cross-validation", "heading_level": null, "page_id": 32, "polygon": [[133.5, 215.25], [436.5, 215.25], [436.5, 227.00390625], [133.5, 227.00390625]]}, {"title": "7.10.3 Does Cross-Validation Really Work?", "heading_level": null, "page_id": 34, "polygon": [[133.5, 242.25], [364.5703125, 242.25], [364.5703125, 253.880859375], [133.5, 253.880859375]]}, {"title": "7.11 Bootstrap Methods", "heading_level": null, "page_id": 36, "polygon": [[132.75, 391.359375], [291.75, 391.359375], [291.75, 403.734375], [132.75, 403.734375]]}, {"title": "252 7. Model Assessment and Selection", "heading_level": null, "page_id": 39, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 99.0], [132.0, 99.0]]}, {"title": "7.11.1 Example (Continued)", "heading_level": null, "page_id": 39, "polygon": [[133.5, 609.0], [285.75, 609.0], [285.75, 620.68359375], [133.5, 620.68359375]]}, {"title": "254 7. Model Assessment and Selection", "heading_level": null, "page_id": 41, "polygon": [[132.0, 88.5], [313.5, 88.5], [313.5, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "7.12 Conditional or Expected Test Error?", "heading_level": null, "page_id": 41, "polygon": [[132.75, 255.0], [398.25, 255.0], [398.25, 268.576171875], [132.75, 268.576171875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 44, "polygon": [[132.0, 210.955078125], [255.3486328125, 210.955078125], [255.3486328125, 224.103515625], [132.0, 224.103515625]]}, {"title": "Exercises", "heading_level": null, "page_id": 44, "polygon": [[132.75, 537.0], [189.75, 537.0], [189.75, 549.52734375], [132.75, 549.52734375]]}, {"title": "258 7. Model Assessment and Selection", "heading_level": null, "page_id": 45, "polygon": [[132.0, 88.5], [312.75, 88.5], [312.75, 98.85498046875], [132.0, 98.85498046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 46], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8402, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 42], ["TextInlineMath", 5], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 74], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2274, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 37], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 47], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 18], ["Text", 3], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 26], ["Text", 3], ["SectionHeader", 3], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 37], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 56], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 40], ["Text", 6], ["ListItem", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Picture", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 589, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["Line", 63], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2323, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 85], ["Text", 7], ["Equation", 4], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1187, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 42], ["Line", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 735, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["ListItem", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 43], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1298, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 59], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 56], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 49], ["Text", 9], ["Equation", 4], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 54], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 86], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1040, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 57], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 52], ["Text", 7], ["Equation", 5], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 48], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 50], ["TextInlineMath", 5], ["Text", 4], ["Equation", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 41], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Picture", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 34], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 694, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 62], ["Equation", 3], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 39], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 56], ["TableCell", 20], ["TextInlineMath", 6], ["Equation", 2], ["SectionHeader", 1], ["Table", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 41], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 675, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 83], ["Equation", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 40], ["Text", 6], ["ListItem", 5], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 40], ["ListItem", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 41], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 75], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 829, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 49], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 53], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 61], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1044, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 55], ["TextInlineMath", 4], ["Text", 4], ["Equation", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 586, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 45], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 819, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 42], ["Text", 5], ["SectionHeader", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 993, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 998, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 37], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 600], ["Line", 64], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 46], ["Text", 6], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_232-279"}