## 5.10 ARITHMETIC CODING

From the discussion of the previous sections, it is apparent that using a codeword length of log  $\frac{1}{p(x)}$  for the codeword corresponding to x is nearly optimal in that it has an expected length within 1 bit of the entropy. The optimal codes are Hu<PERSON>man codes, and these can be constructed by the procedure described in Section 5.6.

For small source alphabets, though, we have efficient coding only if we use long blocks of source symbols. For example, if the source is binary, and we code each symbol separately, we must use 1 bit per symbol irrespective of the entropy of the source. If we use long blocks, we can achieve an expected length per symbol close to the entropy rate of the source.

It is therefore desirable to have an efficient coding procedure that works for long blocks of source symbols. Huffman coding is not ideal for this situation, since it is a bottom-up procedure that requires the calculation of the probabilities of all source sequences of a particular block length and the construction of the corresponding complete code tree. We are then limited to using that block length. A better scheme is one which can be easily extended to longer block lengths without having to redo all the calculations. Arithmetic coding, a direct extension of the Shannon-Fano-Elias coding scheme of the last section, achieves this goal.

The essential idea of arithmetic coding is to efficiently calculate the probability mass function  $p(x^n)$  and the cumulative distribution function  $F(x^n)$  for the source sequence  $x^n$ . Using the ideas of Shannon-Fano-Elias coding, we can use a number in the interval  $(F(x^n) - p(x^n), F(x^n))$  as the code for x<sup>n</sup>. For example, expressing  $F(x^n)$  to an accuracy of  $\lceil \log \frac{1}{n(n)} \rceil$ will give us a code for the source. Using the same arguments as in the discussion of the Shannon-Fano-Elias code, it follows that the codeword corresponding to any sequence lies within the step in the cumulative distribution function (Figure 5.5) corresponding to that sequence, So the codewords for different sequences of length  $n$  are different. However, the procedure does not guarantee that the set of codewords is prefix-free. We can construct a prefix-free set by using  $\bar{F}(x)$  rounded off to  $\lceil \log \frac{1}{p(x)} \rceil + 1$ bits as in Section 5.9. In the algorithm described below, we will keep track of both  $F(x^n)$  and  $p(x^n)$  in the course of the algorithm, so we can calculate  $F(x)$  easily at any stage.

We now describe a simplified version of the arithmetic coding algorithm to illustrate some of the important ideas. We assume that we have a fixed block length  $n$  that is known to both the encoder and the decoder. With a small loss of generality, we will assume that the source alphabet is binary. We assume that we have a simple procedure to calculate  $p(x_1, x_2, \ldots, x_n)$  for any string  $x_1, x_2, \ldots, x_n$ . We will use the natural lexicographic order on strings, so that a string  $x$  is greater than

a string y if  $x_i = 1$ ,  $y_i = 0$  for the first i such that  $x_i \neq y_i$ . Equivalently,  $x > y$  if  $\Sigma$ ,  $x, 2^{-i} > \Sigma$ ,  $y, 2^{-i}$ , i.e., if the corresponding binary numbers satisfy  $0.x > 0.y$ . We can arrange the strings as the leaves of a tree of depth  $n$ , where each level of the tree corresponds to one bit. Such a tree is illustrated in Figure 5.6. In this figure, the ordering  $x > y$  corresponds to the fact that x is to the right of y on the same level of the tree.

From the discussion of the last section, it appears that we need to find  $p(y^n)$  for all  $y^n \leq x^n$  and use that to calculate  $F(x^n)$ . Looking at the tree, we might suspect that we need to calculate the probabilities of all the leaves to the left of  $x^n$  to find  $F(x^n)$ . The sum of these probabilities is the sum of the probabilities of all the subtrees to the left of  $x^n$ . Let  $T_{x_1x_2\cdots x_{k-1}0}$  be a subtree starting with  $x_1x_2\cdots x_{k-1}0$ . The probability of this subtree is

$$
p(T_{x_1x_2\cdots x_{k-1}0}) = \sum_{y_{k+1}\cdots y_n} p(x_1x_2\cdots x_{k-1}0y_{k+1}\cdots y_n)
$$
 (5.75)

$$
= p(x_1 x_2 \cdots x_{k-1} 0), \qquad (5.76)
$$

and hence can be calculated easily. Therefore we can rewrite  $F(x^n)$  as

Image /page/1/Figure/6 description: The image displays a binary tree structure, with nodes labeled '0' and '1' at the top. Several branches extend downwards, creating a complex branching pattern. Three distinct groups of nodes are highlighted with oval shapes. The first oval, labeled 'T1', encloses a cluster of four leaf nodes. The second oval, labeled 'T2', encloses a cluster of two leaf nodes. The third oval, labeled 'T3', encloses a single leaf node, which is also labeled 'x'. The caption below the figure reads 'Figure 5.6. Tree of strings for arithmetic coding'.

Figure 5.6. Tree of strings for arithmetic coding.

$$
F(x^n) = \sum_{y^n \leq x^n} p(y^n) \tag{5.77}
$$

$$
= \sum_{T \,:\, T \text{ is to the left of } x^n} p(T) \tag{5.78}
$$

$$
= \sum_{k:x_k=1} p(x_1x_2\cdots x_{k-1}0). \tag{5.79}
$$

Thus we can calculate  $F(x^n)$  quickly from  $p(x^n)$ .

**Example 5.10.1:** If  $X_1, X_2, \ldots, X_n$  are Bernoulli( $\theta$ ) in Figure 5.6, then

$$
F(01110) = p(T_1) + p(T_2) + p(T_3) = p(00) + p(010) + p(0110)
$$
 (5.80)

$$
= (1 - \theta)^2 + \theta(1 - \theta)^2 + \theta^2(1 - \theta)^2.
$$
 (5.81)

Note that these terms can be calculated recursively. For example,  $\theta^{3}(1-\theta)^{3} = (\theta^{2}(1-\theta)^{2})\theta(1-\theta).$ 

To encode the next bit of the source sequence, we need only calculate  $p(x^{i}x_{i+1})$  and update  $F(x^{i}x_{i+1})$  using the above scheme. Encoding can therefore be done sequentially, by looking at the bits as they come in.

To decode the sequence, we use the same procedure to calculate the cumulative distribution function and check whether it exceeds the value corresponding to the codeword. We then use the tree in Figure 5.6 as a decision tree. At the top node, we check to see if the received codeword  $F(x^n)$  is greater than  $p(0)$ . If it is, then the subtree starting with 0 is to the left of  $x^n$  and hence  $x_1 = 1$ . Continuing this process down the tree, we can decode the bits in sequence. Thus we can compress and decompress a source sequence in a sequential manner.

The above procedure depends on a model for which we can easily compute  $p(x^n)$ . Two examples of such models are i.i.d. sources, where

$$
p(x^n) = \prod_{i=1}^n p(x_i).
$$
 (5.82)

and Markov sources, where

$$
p(x^{n}) = p(x_{1}) \prod_{i=2}^{n} p(x_{i}|x_{i-1}).
$$
 (5.83)

In both cases, we can easily calculate  $p(x^n x_{n+1})$  from  $p(x^n)$ .

Note that it is not essential that the probabilities used in the encoding be equal to the true distribution of the source. In some cases, such as in image compression, it is difficult to describe a "true" distribution for the source. Even then, it is possible to apply the above

106

arithmetic coding procedure. The procedure will be efficient only if the model distribution is close to the empirical distribution of the source (Theorem 5.4.3). A more sophisticated use of arithmetic coding is to change the model dynamically to adapt to the source. Adaptive models work well for large classes of sources. The adaptive version of arithmetic coding is a simple example of a universal code, that is, a code that is designed to work with an arbitrary source distribution. Another example is the Lempel-Ziv code, which is discussed in Section 12.10.

The foregoing discussion of arithmetic coding has avoided discussion of the difficult implementation issues of computational accuracy, buffer sizes, etc. An introduction to some of these issues can be found in the tutorial introduction to arithmetic coding by Langdon [170].

## 5.11 COMPETITIVE OPTIMALITY OF THE SHANNON CODE

We have shown that Huffman coding is optimal in that it has minimum expected length. But what does that say about its performance on any particular sequence? For example, is it always better than any other code for all sequences? Obviously not, since there are codes which assign short codewords to infrequent source symbols. Such codes will be better than the Huffman code on those source symbols.

To formalize the question of competitive optimality, consider the following two-person zero sum game: Two people are given a probability distribution and are asked to design an instantaneous code for the distribution. Then a source symbol is drawn from this distribution and the payoff to player A is 1 or -1 depending on whether the codeword of player A is shorter or longer than the codeword of player B. The payoff is 0 for ties.

Dealing with Huffman codelengths is difficult, since there is no explicit expression for the codeword lengths. Instead, we will consider the Shannon code with codeword lengths  $l(x) = \lceil \log \frac{1}{p(x)} \rceil$ . In this case, we have the following theorem:

**Theorem 5.11.1:** Let  $l(x)$  be the codeword lengths associated with the Shannon code and let  $l'(x)$  be the codeword lengths associated with any other code. Then

$$
Pr(l(X) \ge l'(X) + c) \le \frac{1}{2^{c-1}}.
$$
\n(5.84)

Thus, for example, the probability that  $l'(X)$  is 5 or more bits shorter than  $l(X)$  is less than  $\frac{1}{16}$ .

Proof:

$$
Pr(l(X) \ge l'(X) + c) = Pr\left(\left\lceil \log \frac{1}{p(X)} \right\rceil \ge l'(X) + c\right) \tag{5.85}
$$

$$
\leq \Pr\Bigl(\log \frac{1}{p(X)} \geq l'(X) + c - 1\Bigr) \qquad (5.86)
$$

$$
= \Pr(p(X) \le 2^{-l'(X) - c + 1})
$$
\n(5.87)

$$
= \sum_{x \,:\, p(x) \leq 2^{-l'(x)-c+1}} p(x) \tag{5.88}
$$

$$
\leq \sum_{x \,:\, p(x)\leq 2^{-l'(x)-c+1}} 2^{-l'(x)-(c-1)} \tag{5.89}
$$

$$
\leq \sum_{x} 2^{-l'(x)} 2^{-(c-1)} \tag{5.90}
$$

$$
\leq 2^{-(c-1)}, \tag{5.91}
$$

since  $\sum 2^{-l'(x)} \le 1$  by the Kraft inequality.  $\square$ 

Hence, no other code can do much better than the Shannon code most of the time.

We now strengthen this result in two ways. First, there is the term + 1 that has been added, which makes the result non-symmetric. Also, in a game theoretic setting, one would like to ensure that  $l(x) < l'(x)$ more often than  $l(x) > l'(x)$ . The fact that  $l(x) \le l'(x) + 1$  with probability  $\geq \frac{1}{2}$  does not ensure this. We now show that even under this stricter criterion, Shannon coding is optimal. Recall that the probability mass function  $p(x)$  is dyadic if  $\log \frac{1}{p(x)}$  is an integer for all x.

**Theorem 5.11.2:** For a dyadic probability mass function  $p(x)$ , let  $l(x) =$  $\log \frac{1}{p(x)}$  be the word lengths of the binary Shannon code for the source, and let  $l'(x)$  be the lengths of any other uniquely decodable binary code for the source. Then

$$
Pr(l(X) < l'(X)) \ge Pr(l(X) > l'(X)),\tag{5.92}
$$

with equality iff  $l'(x) = l(x)$  for all x. Thus the code length assignment  $l(x) = \log \frac{1}{p(x)}$  is uniquely competitively optimal.

**Proof:** Define the function  $sgn(t)$  as follows:

$$
sgn(t) = \begin{cases} 1 & \text{if } t > 0 \\ 0 & \text{if } t = 0 \\ -1 & \text{if } t < 0 \end{cases}
$$
 (5.93)

Then it is easy to see from Figure 5.7 that

Image /page/5/Figure/1 description: The image displays a graph of the function sgn(x) plotted against x. The y-axis is labeled sgn(x) and the x-axis is labeled x. The graph passes through the origin (0,0). For positive values of x, the graph is a curve that increases and passes through the point (1,1). The equation for this part of the curve is labeled as 2^x - 1. For negative values of x, the graph is a curve that decreases and appears to approach a horizontal asymptote below the x-axis. There are tick marks on the x-axis at -1 and 1, and a tick mark on the y-axis at 1.

Figure 5.7. The sgn function and a bound.

$$
sgn(t) \le 2t - 1 \quad \text{for } t = 0, \pm 1, \pm 2, \dots \tag{5.94}
$$

Note that though this inequality is not satisfied for all  $t$ , it is satisfied at all integer values of t.

We can now write

$$
\Pr(l'(X) < l(X)) - \Pr(l'(X) > l(X)) = \sum_{x \,:\, l'(x) < l(x)} p(x) - \sum_{x \,:\, l'(x) > l(x)} p(x) \quad (5.95)
$$

$$
= \sum_{x} p(x) \, \text{sgn}(l(x) - l'(x)) \tag{5.96}
$$

$$
= E \, \text{sgn}(l(X) - l'(X)) \tag{5.97}
$$

$$
\stackrel{(a)}{\leq} \sum_{x} p(x) (2^{l(x)-l'(x)} - 1) \tag{5.98}
$$

$$
= \sum_{x} 2^{-l(x)} (2^{l(x)-l'(x)} - 1)
$$
 (5.99)

$$
= \sum_{x} 2^{-l'(x)} - \sum_{x} 2^{-l(x)} \tag{5.100}
$$

$$
=\sum_{x} 2^{-l'(x)}-1 \tag{5.101}
$$

$$
\stackrel{(b)}{\leq} 1 - 1 \tag{5.102}
$$

$$
=0\,,\qquad \qquad (5.103)
$$

where (a) follows from the bound on  $sgn(x)$  and (b) follows from the fact that  $l'(x)$  satisfies the Kraft inequality.

We have equality in the above chain only if we have equality in (a) and (b). We have equality in the bound for  $sgn(t)$  only if t is 0 or 1, i.e.,  $l(x) = l'(x)$  or  $l(x) = l'(x) + 1$ . Equality in (b) implies that  $l'(x)$  satisfy the Kraft inequality with equality. Combining these two facts implies that  $l'(x) = l(x)$  for all  $x$ .  $\Box$ 

Corollary: For non-dyadic probability mass functions,

$$
E \, \text{sgn}(l(X) - l'(X) - 1) \le 0 \tag{5.104}
$$

where  $l(x) = \lfloor \log \frac{1}{p(x)} \rfloor$  and  $l'(x)$  is any other code for the source.

**Proof:** Along the same lines as the preceding proof.  $\Box$ 

Hence we have shown that Shannon coding is optimal under a variety of criteria; it is robust with respect to the payoff function. In particular, for dyadic p,  $E(l - l') \le 0$ , E sgn( $l - l'$ )  $\le 0$ , and by use of inequality (5.94),  $Ef(l - l') \le 0$ , for any function f satisfying  $f(t) \le 2<sup>t</sup> - 1$ ,  $t = 0, \pm 1$ ,  $\pm 2, \ldots$ .

## 5.12 GENERATION OF DISCRETE DISTRIBUTIONS FROM FAIR COINS

In the early sections of this chapter, we considered the problem of representing a random variable by a sequence of bits such that the expected length of the representation was minimized. It can be argued (Problem 26) that the encoded sequence is essentially incompressible, and therefore has an entropy rate close to 1 bit per symbol. Therefore the bits of the encoded sequence are essentially fair coin flips.

In this section, we will take a slight detour from our discussion of source coding and consider the dual question. How many fair coin flips does it take to generate a random variable  $X$  drawn according to some specified probability mass function p? We first consider a simple example:

**Example 5.12.1:** Given a sequence of fair coin tosses (fair bits), suppose we wish to generate a random variable  $X$  with distribution

$$
X = \begin{cases} a & \text{with probability } \frac{1}{2} \\ b & \text{with probability } \frac{1}{4} \\ c & \text{with probability } \frac{1}{4} \end{cases} \tag{5.105}
$$

It is easy to guess the answer. If the first bit is 0, we let  $X = a$ . If the first two bits are 10, we let  $X = b$ . If we see 11, we let  $X = c$ . It is clear that X has the desired distribution.

We calculate the average number of fair bits required for generating the random variable in this case as  $\frac{1}{2}$ 1 +  $\frac{1}{4}$ 2 +  $\frac{1}{4}$ 2 = 1.5 bits. This is also the entropy of the distribution. Is this unusual? No, as the results of this section indicate.

The general problem can now be formulated as follows. We are given a sequence of fair coin tosses  $Z_1, Z_2, \ldots$ , and we wish to generate a discrete random variable  $X \in \mathcal{X} = \{1, 2, ..., m\}$  with probability mass function  $\mathbf{p} = (p_1, p_2, \dots, p_m)$ . Let the random variable T denote the number of coin flips used in the algorithm.

We can describe the algorithm mapping strings of bits  $Z_1, Z_2, \ldots$ , to possible outcomes  $X$  by a binary tree. The leaves of the tree are marked by output symbols  $X$  and the path to the leaves is given by the sequence of bits produced by the fair coin. For example, the tree for the distribution  $(\frac{1}{2}, \frac{1}{4}, \frac{1}{4})$  is shown in Figure 5.8.

The tree representing the algorithm must satisfy certain properties:

- 1. The tree should be complete, i.e., every node is either a leaf or has two descendants in the tree. The tree may be infinite, as we will see in some examples.
- 2. The probability of a leaf at depth k is  $2^{-k}$ . Many leaves may be labeled with the same output symbol--- the total probability of all these leaves should equal the desired probability of the output symbol.
- 3. The expected number of fair bits  $ET$  required to generate  $X$  is equal to the expected depth of this tree.

There are many possible algorithms that generate the same output distribution. For example, the mapping:  $00 \rightarrow a$ ,  $01 \rightarrow b$ ,  $10 \rightarrow c$ ,  $11 \rightarrow a$ also yields the distribution  $(\frac{1}{2}, \frac{1}{4}, \frac{1}{4})$ . However, this algorithm uses two fair bits to generate each sample, and is therefore not as efficient as the mapping given earlier, which used only 1.5 bits per sample. This brings up the question: What is the most efficient algorithm to generate a given distribution and how is this related to the entropy of the distribution?

Image /page/7/Figure/10 description: A simple diagram shows three points labeled 'a', 'b', and 'c'. Point 'a' is connected to a junction. From this junction, two lines extend. One line goes to point 'b', and the other line goes to point 'c'.

**Figure 5.8.** Tree for generation of the distribution  $(\frac{1}{2}, \frac{1}{4}, \frac{1}{4})$ .

We expect that we need at least as much randomness in the fair bits as we produce in the output samples. Since entropy is a measure of randomness, and each fair bit has an entropy of 1 bit, we expect that the number of fair bits used will be at least equal to the entropy of the output. This is proved in the following theorem.

We will need a simple lemma about trees in the proof of the theorem. Let  $\mathcal Y$  denote the set of leaves of a complete tree. Consider a distribution on the leaves, such that the probability of a leaf at depth  $k$  on the tree is  $2^{-k}$ . Let Y be a random variable with this distribution. Then we have the following lemma:

**Lemma 5.12.1:** For any complete tree, consider a probability distribution on the leaves, such that the probability of a leaf at depth  $k$  is  $2^{-k}$ . Then the expected depth of the tree is equal to the entropy of this distribution.

Proof: The expected depth of the tree

$$
ET = \sum_{y \in \mathcal{Y}} k(y) 2^{-k(y)} \tag{5.106}
$$

and the entropy of the distribution of Y is

$$
H(Y) = -\sum_{y \in \mathcal{Y}} \frac{1}{2^{k(y)}} \log \frac{1}{2^{k(y)}} \tag{5.107}
$$

$$
= \sum_{y \in \mathcal{Y}} k(y) 2^{-k(y)}, \qquad (5.108)
$$

where  $k(y)$  denotes the depth of leaf y. Thus

$$
H(Y) = ET \qquad \qquad \Box \qquad (5.109)
$$

**Theorem 5.12.1:** For any algorithm generating  $X$ , the expected number of fair bits used is greater than the entropy  $H(X)$ , i.e.,

$$
ET \ge H(X) \tag{5.110}
$$

**Proof:** Any algorithm generating  $X$  from fair bits can be represented by a binary tree. Label all the leaves of this tree by distinct symbols  $y \in \mathcal{Y} = \{1, 2, \dots\}$ . If the tree is infinite, the alphabet  $\mathcal{Y}$  is also infinite.

Now consider the random variable Y defined on the leaves of the tree, such that for any leaf y at depth k, the probability that  $Y = y$  is  $2^{-k}$ . By Lemma 5.12.1, the expected depth of this tree is equal to the entropy of Y, i.e.,

$$
ET = H(Y) \tag{5.111}
$$

Now the random variable  $X$  is a function of  $Y$  (one or more leaves map

onto an output symbol), and hence by the result of Problem 5 in Chapter 2, we have

$$
H(X) \le H(Y) \tag{5.112}
$$

Thus for any algorithm generating the random variable  $X$ , we have

$$
H(X) \le ET \qquad \Box \tag{5.113}
$$

The same argument answers the question of optimality for a dyadic distribution.

Theorem 5.12.2: Let the random variable X have a dyadic distribution. The optimal algorithm to generate  $X$  from fair coin flips requires an expected number of coin tosses precisely equal to the entropy, i.e.,

$$
ET = H(X). \tag{5.114}
$$

**Proof:** The previous theorem shows that we need at least  $H(X)$  bits to generate X.

For the constructive part, we use the Huffman code tree for  $X$  as the tree to generate the random variable. For a dyadic distribution, the Huffman code is the same as the Shannon code and achieves the entropy bound. For any  $x \in \mathcal{X}$ , the depth of the leaf in the code tree corresponding to x is the length of the corresponding codeword, which is  $\log \frac{1}{p(x)}$ . Hence when this code tree is used to generate  $X$ , the leaf will have a probability  $2^{-\log(1/p(x))} = p(x)$ .

The expected number of coin flips is the expected depth of the tree, which is equal to the entropy (because the distribution is dyadic). Hence for a dyadic distribution, the optimal generating algorithm achieves

$$
ET = H(X). \quad \Box \tag{5.115}
$$

What is not distribution is not dyadic is not dyadic. In this case, we can not dyadic use the case, we can not  $\frac{3}{4}$  in the contribution is not dyactic. In this case, we cannot use the same idea, since the code tree for the Huffman code will generate a dyadic distribution on the leaves, not the distribution with which we started. Since all the leaves of the tree have probabilities of the form  $2^{-k}$ , it follows that we should split any probability  $p_i$ , that is not of this form into atoms of this form. We can then allot these atoms to leaves on the tree.  $T_{\rm max}$  the expected depth of the tree, we should use atoms with  $T_{\rm max}$ 

to minimize the expected depth of the tree, we should use atoms with as large a probability as possible. So given a probability  $p_i$ , we lind the the tree atom of the form  $\mathbf{z}$  that is less than  $p_i$ , and anot this atom to the tree. Then we calculate the remainder and find that largest atom that will fit in the remainder. Continuing this process, we can split all the probabilities into dyadic atoms. This process is equivalent to finding the binary expansions of the probabilities. Let the binary expansion of the probability  $p_i$  be

$$
p_i = \sum_{j \ge 1} p_i^{(j)}, \tag{5.116}
$$

where  $p_i^{(j)} = 2^{-j}$  or 0. Then the atoms of the expansion are the  $\{p_i^{(j)} : i =$  $1, 2, \ldots, m, j \geq 1$ .

Since  $\Sigma_i$   $p_i = 1$ , the sum of the probabilities of these atoms is 1. We will allot an atom of probability  $2^{-j}$  to a leaf at depth j on the tree. The depths of the atoms satisfy the Kraft inequality, and hence by Theorem 5.2.1, we can always construct such a tree with all the atoms at the right depths.

We illustrate this procedure with an example:

**Example 5.12.2:** Let  $X$  have the distribution

$$
X = \begin{cases} a & \text{with probability } \frac{2}{3} \\ b & \text{with probability } \frac{1}{3} \end{cases} \tag{5.117}
$$

We find the binary expansions of these probabilities:

$$
\frac{2}{3} = 0.10101010\ldots_2\tag{5.118}
$$

$$
\frac{1}{3} = 0.01010101\ldots_2\tag{5.119}
$$

Hence the atoms for the expansion are

$$
\frac{2}{3} \rightarrow \left(\frac{1}{2}, \frac{1}{8}, \frac{1}{32}, \dots\right) \tag{5.120}
$$

$$
\frac{1}{3} \rightarrow \left(\frac{1}{4}, \frac{1}{16}, \frac{1}{64}, \dots\right) \tag{5.121}
$$

These can be allotted to a tree as shown in Figure 5.9.

This procedure yields a tree that generates the random variable X. We have argued that this procedure is optimal (gives a tree of minimum

Image /page/10/Figure/16 description: A branching diagram with labels 'a' and 'b' at the end of each branch. The diagram starts with a single branch labeled 'a', which then splits into two branches. The top branch continues without splitting, while the bottom branch splits again. This pattern of splitting continues, with alternating labels 'b' and 'a' appearing at the end of the branches, suggesting a recursive or iterative structure.

Figure 5.9. Tree to generate a  $(\frac{2}{3}, \frac{1}{3})$  distribution.

expected depth), but we will not give a formal proof. Instead, we bound the expected depth of the tree generated by this procedure.

Theorem 5.123: The expected number of fair bits required by the optimal algorithm to generate a random variable X lies between  $H(X)$ and  $H(X) + 2$ , *i.e.*,

$$
H(X) \le ET < H(X) + 2. \tag{5.122}
$$

Proof: The lower bound on the expected number of coin tosses is proved in Theorem 512.1.

For the upper bound, we write down an explicit expression for the expected number of coin tosses required for the procedure described above. We split all the probabilities  $(p_1, p_2, \ldots, p_m)$  into dyadic atoms, e.g.,

$$
p_1 \rightarrow (p_1^{(1)}, p_1^{(2)}, \dots), \tag{5.123}
$$

etc. Using these atoms (which form a dyadic distribution), we construct a tree with leaves corresponding to each of these atoms. The number of coin tosses required to generate each atom is its depth in the tree, and therefore the expected number of coin tosses is the expected depth of the tree, which is equal to the entropy of the dyadic distribution of the atoms. Hence

$$
ET = H(Y), \tag{5.124}
$$

where Y has the distribution,  $(p_1^{(1)}, p_1^{(2)}, \ldots, p_2^{(1)}, p_2^{(2)}, \ldots, p_m^{(1)}, p_m^{(2)}, \ldots)$ . Now since  $X$  is a function of  $Y$ , we have

$$
H(Y) = H(Y, X) = H(X) + H(Y|X), \qquad (5.125)
$$

and our objective is to show that  $H(Y|X) < 2$ . We now give an algebraic proof of this result. Expanding the entropy of Y, we have

$$
H(Y) = -\sum_{i=1}^{m} \sum_{j\geq 1} p_i^{(j)} \log p_i^{(j)}
$$
 (5.126)

$$
=\sum_{i=1}^{m}\sum_{j+p_{i}^{(j)}>0}j2^{-j},\tag{5.127}
$$

since each of the atoms is either 0 or  $2^{-k}$  for some k. Now consider the term in the expansion corresponding to each  $i$ , which we shall call  $T_i$ , i.e.,

$$
T_i = \sum_{j \,:\, p_i^{(j)} > 0} j 2^{-j} \,. \tag{5.128}
$$

We can find an *n* such that  $2^{-(n-1)} > p_i \ge 2^{-n}$ , or

$$
n-1 < -\log p_i \le n \tag{5.129}
$$

Then it follows that  $p_i^{(j)} > 0$  only if  $j \ge n$ , so that we can rewrite (5.128) as

$$
T_i = \sum_{j:j \ge n, \ p_i^{(j)} > 0} j2^{-j}.
$$
 (5.130)

We use the definition of the atom to write  $p_i$  as

$$
p_i = \sum_{j:j \ge n, \ p_i^{(j)} > 0} 2^{-j} \,. \tag{5.131}
$$

In order to prove the upper bound, we first show that  $T_i \le -p_i \log p_i +$  $2p_i$ . Consider the difference

$$
T_i + p_i \log p_i - 2p_i \stackrel{(a)}{<} T_i - p_i(n-1) - 2p_i \tag{5.132}
$$

$$
=T_i - (n-1+2)p_i \tag{5.133}
$$

$$
= \sum_{j:j \ge n, p_i^{(j)} > 0} j2^{-j} - (n+1) \sum_{j:j \ge n, p_i^{(j)} > 0} 2^{-j} (5.134)
$$

$$
= \sum_{j:j \ge n, \ p_i^{(j)} > 0} (j - n - 1) 2^{-j}
$$
 (5.135)

$$
= -2^{-n} + 0 + \sum_{j+j=n+2, p_i^{(j)}>0} (j-n-1)2^{-j} \quad (5.136)
$$

$$
\sum_{k=k}^{(b)} (n-2)^{-n} + \sum_{k \,:\, k \ge 1, \ p_i^{(k+n+1)} > 0} k 2^{-(k+n+1)} \tag{5.137}
$$

$$
\leq -2^{-n} + \sum_{k:k \geq 1} k2^{-(k+n+1)} \tag{5.138}
$$

$$
= -2^{-n} + 2^{-(n+1)}2 \tag{5.139}
$$

$$
=0\,,\tag{5.140}
$$

where (a) follows from (5.129), (b) from a change of variables for the summation and (c) from increasing the range of the summation. Hence we have shown that

$$
T_i \le -p_i \log p_i + 2p_i. \tag{5.141}
$$

Since  $ET = \sum_i T_i$ , it follows immediately that

$$
ET < -\sum_{i} p_i \log p_i + 2\sum_{i} p_i = H(X) + 2 \tag{5.142}
$$

completing the proof of the theorem.  $\Box$ 

### SUMMARY OF CHAPTER 5

**Kraft inequality:** Instantaneous codes  $\Leftrightarrow \sum D^{-l_i} \leq 1$ 

McMillan inequality: Uniquely decodable codes  $\Leftrightarrow$   $\sum D^{-l_i} \leq 1$ 

Entropy bound on data compression (Lower bound):

$$
L \stackrel{\triangle}{=} \sum p_i l_i \ge H_D(X). \tag{5.143}
$$

Shannon code:

$$
l_i = \left\lceil \log_b \frac{1}{p_i} \right\rceil \tag{5.144}
$$

$$
L < H_D(X) + 1 \tag{5.145}
$$

Huffman code:

$$
L^* = \min_{\sum D^{-l_i} \leq 1} \sum p_i l_i \,. \tag{5.146}
$$

$$
H_D(X) \le L^* < H_D(X) + 1 \,. \tag{5.147}
$$

Wrong code:  $X \sim p(x)$ ,  $l(x) = \lceil \log \frac{1}{q(x)} \rceil$ ,  $L = \sum p(x)l(x)$ :

$$
H(p) + D(p||q) \le L < H(p) + D(p||q) + 1. \tag{5.148}
$$

Stochastic processes:

$$
\frac{H(X_1, X_2, \dots, X_n)}{n} \le L_n < \frac{H(X_1, X_2, \dots, X_n)}{n} + \frac{1}{n} \,. \tag{5.149}
$$

Stationary processes:

$$
L_n \to H(\mathcal{X})\,. \tag{5.150}
$$

**Competitive optimality:**  $l(x) = \lfloor \log \frac{1}{p(x)} \rfloor$  (Shannon code) versus any other code  $l'(x)$ :

$$
Pr(l(X) \ge l'(X) + c) \le \frac{1}{2^{c-1}}.
$$
 (5.151)

Generation of random variables:

$$
H(X) \le ET < H(X) + 2 \tag{5.152}
$$

## PROBLEMS FOR CHAPTER 5

- 1. Uniquely decodable and instantaneous codes. Let  $L = \sum_{i=1}^{m} p_i l_i^{100}$  be the expected value of the 100th power of the word lengths associated with an encoding of the random variable X. Let  $L_1 = \min L$  over all instantaneous codes; and let  $L_2 = \min L$  over all uniquely decodable codes. What inequality relationship exists between  $L_1$  and  $L_2$ ?
- 2. How many fingers has a Martian? Let

$$
S=\begin{pmatrix}S_1,\ldots,S_m\\p_1,\ldots,p_m\end{pmatrix}.
$$

The  $S_i$ 's are encoded into strings from a D-symbol output alphabet in a uniquely decodable manner. If  $m = 6$  and the codeword lengths are  $(l_1, l_2, \ldots, l_6) = (1, 1, 2, 3, 2, 3)$ , find a good lower bound on D. You may wish to explain the title of the problem.

3. Slackness in the Kraft inequality. An instantaneous code has word lengths  $l_1, l_2, \ldots, l_m$  which satisfy the strict inequality

$$
\sum_{i=1}^m D^{-l_i} < 1.
$$

The code alphabet is  $\mathcal{D} = \{0, 1, 2, \ldots, D-1\}$ . Show that there exist arbitrarily long sequences of code symbols in  $\mathcal{D}^*$  which cannot be decoded into sequences of codewords:

4. Huffman coding. Consider the random variable

$$
X = \begin{pmatrix} x_1 & x_2 & x_3 & x_4 & x_5 & x_6 & x_7 \\ 0.49 & 0.26 & 0.12 & 0.04 & 0.04 & 0.03 & 0.02 \end{pmatrix}
$$

- (a) Find a binary Huffman code for X.
- (b) Find the expected codelength for this encoding.
- (c) Find a ternary Huffman code for X.
- 5. More Huffman codes. Find the binary Huffman code for the source with probabilities  $(1/3, 1/5, 1/5, 2/15, 2/15)$ . Argue that this code is also optimal for the source with probabilities  $(1/5, 1/5, 1/5, 1/5, 1/5)$ .
- 6. Bad codes. Which of these codes cannot be Huffman codes for any probability assignment?
  - (a)  $\{0, 10, 11\}.$
  - (b)  $\{00, 01, 10, 110\}.$
  - (c)  $\{01, 10\}$ .
- 7. Huffman 20 Questions. Consider a set of n objects. Let  $X_i = 1$  or 0 accordingly as the i-th object is good or defective. Let  $X_1, X_2, \ldots, X_n$ be independent with  $Pr{X_i = 1} = p_i$ ; and  $p_1 > p_2 > ... > p_n > 1/2$ . We are asked to determine the set of all defective objects. Any yes-no question you can think of is admissible.

- (a) Give a good lower bound on the minimum average number of questions required.
- (b) If the longest sequence of questions is required by nature's answers to our questions, what (in words) is the last question we should ask? And what two sets are we distinguishing with this question? Assume a compact (minimum average length) sequence of questions.
- (c) Give an upper bound (within 1 question) on the minimum average number of questions required.
- 8. Simple optimum compression of a Markov source. Consider the S-state Markov process  $U_1, U_2, \ldots$  having transition matrix

| $U_n$             | S,  | $S_{2}$ | $S_{3}$ |
|-------------------|-----|---------|---------|
| $S_{1}$           | 1/2 | 1/4     | 1/4     |
|                   | 1/4 | 1/2     | 1/4     |
| $\frac{S_2}{S_3}$ |     | 1/2     | 1/2     |

Thus the probability that  $S<sub>1</sub>$  follows  $S<sub>3</sub>$  is equal to zero. Design 3 codes  $C_1, C_2, C_3$  (one for each state  $S_1, S_2, S_3$ ), each code mapping elements of the set of  $S_i$ 's into sequences of 0's and 1's, such that this Markov process can be sent with maximal compression by the following scheme:

- (a) Note the present symbol  $S_i$ .
- (b) Select code  $C_i$ .
- (c) Note the next symbol  $S_i$  and send the codeword in  $C_i$  corresponding to  $S_i$ .
- (d) Repeat for the next symbol.

What is the average message length of the next symbol conditioned on the previous state  $S = S_i$  using this coding scheme? What is the unconditional average number of bits per source symbol? Relate this to the entropy rate  $H(\mathcal{U})$  of the Markov chain.

- 9. Optimal code lengths that require one bit above entropy. The source coding theorem shows that the optimal code for a random variable  $X$ has an expected length less than  $H(X) + 1$ . Give an example of a random variable for which the expected length of the optimal code is close to  $H(X) + 1$ , i.e., for any  $\epsilon > 0$ , construct a distribution for which the optimal code has  $L > H(X) + 1 - \epsilon$ .
- 10. Ternary codes that achieve the entropy bound. A random variable  $X$ takes on m values and has entropy  $H(X)$ . An instantaneous ternary code is found for this source, with average length

$$
L = \frac{H(X)}{\log_2 3} = H_3(X) \,. \tag{5.153}
$$

- (a) Show that each symbol of X has a probability of the form  $3^{-i}$  for some i.
- (b) Show that  $m$  is odd.
- 11. Suffix condition. Consider codes that satisfy the suffix condition, which says that no codeword is a suffix of any other codeword. Show that a suffix condition code is uniquely decodable, and show that the minimum average length over all codes satisfying the suffix condition is the same as the average length of the Huffman code for that random variable.
- 12. Shannon codes and Huffman codes. Consider a random variable X which takes on four values with probabilities  $(\frac{1}{3}, \frac{1}{3}, \frac{1}{4}, \frac{1}{12})$ .
  - (a) Construct a Huffman code for this random variable.
  - (b) Show that there exist two different sets of optimal lengths for the codewords, namely, show that codeword length assignments  $(1, 2, 3, 3)$  and  $(2, 2, 2, 2)$  are both optimal.
  - (c) Conclude that there are optimal codes with codeword lengths for some symbols that exceed the Shannon code length  $\lceil \log \frac{1}{p(x)} \rceil$ .
- 13. Twenty questions. Player A chooses some object in the universe, and player B attempts to identify the object with a series of yes-no questions. Suppose that player B is clever enough to use the code achieving the minimal expected length with respect to player A's distribution. We observe that player B requires an average of 38.5 questions to determine the object. Find a rough lower bound to the number of objects in the universe.
- 14. Huffman code. Find the (a) binary and (b) ternary Huffman codes for the random variable  $X$  with probabilities

$$
p=\left(\frac{1}{21},\frac{2}{21},\frac{3}{21},\frac{4}{21},\frac{5}{21},\frac{6}{21}\right).
$$

- (c) Calculate  $L = \sum p_i l_i$  in each case.
- 15. Classes of codes. Consider the code  $\{0, 01\}$ 
  - (a) Is it instantaneous?
  - (b) Is it uniquely decodable?
  - (c) Is it nonsingular?
- 16. The game of Hi-Lo.
  - (a) A computer generates a number  $X$  according to a known probability mass function  $p(x)$ ,  $x \in \{1, 2, ..., 100\}$ . The player asks a question, "Is  $X = i$  ?" and is told "Yes", "You're too high," or 'You're too low." He continues for a total of six questions. If he is right (i.e. he receives the answer 'Yes") during this sequence, he receives a prize of value  $v(X)$ . How should the player proceed to maximize his expected winnings?
  - (b) The above doesn't have much to do with information theory. Consider the following variation:  $X \sim p(x)$ , prize =  $v(x)$ ,  $p(x)$

known, as before. But arbitrary Yes-No questions are asked sequentially until  $X$  is determined. ("Determined" doesn't mean that a "Yes" answer is received.) Questions cost one unit each. How should the player proceed? What is his expected return?

- (c) Continuing (b), what if  $v(x)$  is fixed, but  $p(x)$  can be chosen by the computer (and then announced to the player)? The computer wishes to minimize the player's expected return. What should  $p(x)$ be? What is the expected return to the player?
- **17.** Huffman codes with costs. Words like Run! Help! and Fire! are short not because they are frequently used, but perhaps because time is precious in the situations in which these words are required. Suppose that  $X = i$  with probability  $p_i$ ,  $i = 1, 2, ..., m$ . Let  $l_i$  be the number of binary symbols in the codeword associated with  $X = i$ , and let  $c_i$ denote the cost per letter of the codeword when  $X = i$ . Thus the average cost C of the description of X is  $C = \sum_{i=1}^{m} p_i c_i l_i$ .
  - (a) Minimize C over all  $l_1, l_2, \ldots, l_m$  such that  $\Sigma 2^{-l_i} \leq 1$ . Ignore any implied integer constraints on  $l_i$ . Exhibit the minimizing  $l_1, l_2, \ldots, l_m$  and the associated minimum value  $C^*$ .
  - (b) How would you use the Huffman code procedure to minimize  $C$ over all uniquely decodable codes? Let  $C_{Huffman}$  denote this minimum.
  - (c) Can you show that

$$
C^* \leq C_{\text{Huffman}} \leq C^* + \sum_{i=1}^m p_i c_i?
$$

**18.** Conditions for unique decodability. Prove that a code  $C$  is uniquel decodable if (and only if) the extension

$$
C^k(x_1, x_2, \ldots, x_k) = C(x_1)C(x_2) \cdots C(x_k)
$$

is a one-to-one mapping from  $\mathcal{X}^k$  to  $D^*$  for every  $k \ge 1$ . (The only if part is obvious.)

- **19.** Average length of an optimal code. Prove that  $L(p_1,\ldots,p_m)$ , the average codeword length for an optimal D-ary prefix code for probabilities  $\{p_1, \ldots, p_m\}$ , is a continuous function of  $p_1, \ldots, p_m$ . This is true even though the optimal code changes discontinuously as the probabilities vary.
- **20.** Unused code sequences. Let C be a variable length code that satisfies the Kraft inequality with equality but does not satisfy the prefix condition.
  - (a) Prove that some finite sequence of code alphabet symbols is not the prefix of any sequence of codewords.
  - (b) (Optional) Prove or disprove: C has infinite decoding delay.
- **21.** Optimal codes for uniform distributions. Consider a random variabl with  $m$  equiprobable outcomes. The entropy of this information source is obviously  $\log_2 m$  bits.

- (a) Describe the optimal instantaneous binary code for this source and compute the average codeword length  $L_m$ .
- (b) For what values of m does the average codeword length  $L_m$  equal the entropy  $H = \log_2 m?$
- (c) We know that  $L < H + 1$  for any probability distribution. The redundancy of a variable length code is defined to be  $\rho = L - H$ . For what value(s) of m, where  $2^k \le m \le 2^{k+1}$ , is the redundancy of the code maximized? What is the limiting value of this worst case redundancy as  $m \rightarrow \infty$ ?
- 22. Optimal codeword lengths. Although the codeword lengths of an optimal variable length code are complicated functions of the message probabilities  $\{p_1, p_2, \ldots, p_m\}$ , it can be said that less probable symbols are encoded into longer codewords. Suppose that the message probabilities are given in decreasing order  $p_1 > p_2 \geq \cdots \geq p_m$ .
  - (a) Prove that for any binary Huffman code, if the most probable message symbol has probability  $p_1 > 2/5$ , then that symbol must be assigned a codeword of length 1.
  - (b) Prove that for any binary Huffman code, if the most probable message symbol has probability  $p<sub>1</sub> < 1/3$ , then that symbol must be assigned a codeword of length  $\geq 2$ .
- 23. Merges. Companies with values  $W_1, W_2, \ldots, W_m$  are merged as follows. The two least valuable companies are merged, thus forming a list of  $m-1$  companies. The value of the merge is the sum of the values of the two merged companies. This continues until one supercompany remains. Let V equal the sum of the values of the merges. Thus V represents the total reported dollar volume of the merges. For example, if  $W = (3,3,2,2)$ , the merges yield  $(3,3,2,2) \rightarrow$  $(4,3,3) \rightarrow (6,4) \rightarrow (10)$ , and  $V = 4 + 6 + 10 = 20$ .
  - (a) Argue that V is the minimum volume achievable by sequences of pair-wise merges terminating in one supercompany. (Hint: Compare to Huffman coding.)
  - (b) Let  $W = \sum W_i$ ,  $\tilde{W}_i = W_i/W$ , and show that the minimum merge volume V satisfies

$$
WH(\tilde{\mathbf{W}}) \le V \le WH(\tilde{\mathbf{W}}) + W. \tag{5.154}
$$

24. The Sardinas-Patterson test for unique decodability. A code is not uniquely decodable iff there exists a finite sequence of code symbols which can be resolved in two different ways into sequences of codewords. That is, a situation such as

$$
\begin{array}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c
$$

must occur where each  $A_i$  and each  $B_i$  is a codeword. Note that  $B_i$ must be a prefix of  $A_1$  with some resulting "dangling suffix." Each dangling suffix must in turn be either a prefix of a codeword or have another codeword as its prefix, resulting in another dangling suffix. Finally, the last dangling suffix in the sequence must also be a codeword. Thus one can set up a test for unique decodability (which is essentially the Sardinas-Patterson test [2281) in the following way: Construct a set  $S$  of all possible dangling suffixes. The code is uniquely decodable iff S contains no codeword.

- (a) State the precise rules for building the set S.
- (b) Suppose the codeword lengths are  $l_i$ ,  $i = 1, 2, \ldots, m$ . Find a good upper bound on the number of elements in the set S.
- (c) Determine which of the following codes is uniquely decodable:
  - i. {O,lO, 11).
  - ii. {O,Ol, 11).
  - iii.  $\{0, 01, 10\}$ .
  - iv. (0, Ol}.
  - v.  $\{00, 01, 10, 11\}.$
  - vi.  $\{110, 11, 10\}.$
  - vii.  $\{110, 11, 100, 00, 10\}$ .
- (d) For each uniquely decodable code in part (c), construct, if possible, an infinite encoded sequence with a known starting point, such that it can be resolved into codewords in two different ways. (This illustrates that unique decodability does not imply finite decodability.) Prove that such a sequence cannot arise in a prefix code.
- 25. Shannon code. Consider the following method for generating a code for a random variable X which takes on m values  $\{1, 2, \ldots, m\}$  with probabilities  $p_1, p_2, \ldots, p_m$ . Assume that the probabilities are ordered so that  $p_1 \geq p_2 \geq \cdots \geq p_m$ . Define

$$
F_i = \sum_{k=1}^{i-1} p_i , \qquad (5.155)
$$

the sum of the probabilities of all symbols less than i. Then the codeword for i is the number  $F_i \in [0, 1]$  rounded off to  $l_i$  bits, where  $l_i = \lceil \log \frac{1}{p} \rceil$ .

(a) Show that the code constructed by this process is prefix-free and the average length satisfies

$$
H(X) \le L < H(X) + 1. \tag{5.156}
$$

- (b) Construct the code for the probability distribution (0.5, 0.25, 0.125, 0.125).
- 26. Optimal codes for dyadic distributions. For a Huffman code tree, define the probability of a node as the sum of the probabilities of all the leaves under that node. Let the random variable  $X$  be drawn from a dyadic distribution, i.e.,  $p(x) = 2^{-i}$ , for some i, for all  $x \in \mathcal{X}$ . Now consider a binary Huffman code for this distribution.

- (a) Argue that for any node in the tree, the probability of the left child is equal to the probability of the right child.
- (b) Let  $X_1, X_2, \ldots, X_n$  be drawn i.i.d.  $\neg p(x)$ . Using the Huffman code for  $p(x)$ , we map  $X_1, X_2, \ldots, X_n$  to a sequence of bits  $Y_1, Y_2, \ldots$ ,  $Y_{k(X_1, X_2, \ldots, X_n)}$ . (The length of this sequence will depend on the outcome  $X_1, X_2, \ldots, X_n$ .) Use part (a) to argue that the sequence  $Y_1, Y_2, \ldots$ , forms a sequence of fair coin flips, i.e., that  $\Pr{\{\hat{Y}_i = 0\}}$  $= Pr{Y_i = 1} = \frac{1}{2}$ , independent of  $Y_1, Y_2, \ldots, Y_{i-1}$ .

Thus the entropy rate of the coded sequence is 1 bit/symbol.

(c) Give a heuristic argument why the encoded sequence of bits for any code that achieves the entropy bound cannot be compressible and therefore should have an entropy rate of 1 bit per symbol.

### HISTORICAL NOTES

The foundations for the material in this chapter can be found in Shannon's original paper [238], in which Shannon stated the source coding theorem and gave simple examples of codes. He described a simple code construction procedure (described in Problem 25), which he attributed to Fano. This method is now called the Shannon-Fano code construction procedure.

The Kraft inequality for uniquely decodable codes was first proved by McMillan (1931; the proof given here is due to Karush (1491. The Huffman coding procedure was first exhibited and proved to be optimal by Huffman [138].

In recent years, there has been considerable interest in designing source codes that are matched to particular applications such as magnetic recording. In these cases, the objective is to design codes so that the output sequences satisfy certain properties. Some of the results for this problem are described by Franaszek [116], Adler, Coppersmith and Hassner [2] and Marcus [184].

The arithmetic coding procedure has its roots in the Shannon-Fano code developed by Elias (unpublished), which was analyzed by Jelinek [146]. The procedure for the construction of a prefix-free code described in the text is due to Gilbert and Moore (121). Arithmetic coding itself was developed by Rissanen [217] and Pasco [207]; it was generalized by Rissanen and Langdon [171]. See also the enumerative methods in Cover [61]. Tutorial introductions to arithmetic coding can be found in Langdon [170] and Witten, Neal and Cleary [275]. We will discuss universal source coding algorithms in Chapter 12, where we will describe the popular Lempel-Ziv algorithm.

Section 5.12 on the generation of discrete distributions from fair coin flips follows the work of Knuth and Yao [155].