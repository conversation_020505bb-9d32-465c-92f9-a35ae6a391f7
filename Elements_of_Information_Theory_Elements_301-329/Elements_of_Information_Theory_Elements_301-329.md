# Information Theory and **Statistics**

We now explore the relationship between information theory and statistics. We begin by describing the method of types, which is a powerful technique in large deviation theory. We use the method of types to calculate the probability of rare events and to show the existence of universal source codes. We also consider the problem of testing hypotheses and derive the best possible error exponents for such tests (<PERSON>'s lemma). Finally, we treat the estimation of the parameters of a distribution and describe the role of Fisher information.

## 12.1 THE METHOD OF TYPES

The AEP for discrete random variables (Chapter 3) focuses our attention on a small subset of typical sequences. The method of types is an even more powerful procedure in which we consider the sequences that have the same empirical distribution. With this restriction, we can derive strong bounds on the number of sequences of a particular empirical distribution and the probability of each sequence in this set. It is then possible to derive strong error bounds for the channel coding theorem and prove a variety of rate-distortion results. The method of types was fully developed by <PERSON><PERSON><PERSON> and <PERSON> [83], who obtained most of their results from this point of view.

Let  $X_1, X_2, \ldots, X_n$  be a sequence of n symbols from an alphabet  $\mathscr{X} = \{a_1, a_2, \ldots, a_{|\mathscr{X}|}\}.$  We will use the notation  $x^n$  and **x** interchangeably to denote a sequence  $x_1, x_2, \ldots, x_n$ .

**Definition:** The type  $P_x$  (or empirical probability distribution) of a sequence  $x_1, x_2, \ldots, x_n$  is the relative proportion of occurrences of each symbol of  $\mathscr{X}$ , i.e.,  $P_x(a) = N(a|x)/n$  for all  $a \in \mathscr{X}$ , where  $N(a|x)$  is the number of times the symbol a occurs in the sequence  $\mathbf{x} \in \mathcal{X}^n$ .

The type of a sequence **x** is denoted as  $P_x$ . It is a probability mass function on  $\mathcal X$ . (Note that in this chapter, we will use capital letters to denote types and distributions. We will also loosely use the word "distribution" to mean a probability mass function.)

**Definition:** Let  $\mathcal{P}_n$  denote the set of types with denominator n.

For example, if  $\mathcal{X} = \{0, 1\}$ , then the set of possible types with denominator  $n$  is

$$
\mathscr{P}_n = \left\{ (P(0), P(1)) \colon \left( \frac{0}{n}, \frac{n}{n} \right), \left( \frac{1}{n}, \frac{n-1}{n} \right), \dots, \left( \frac{n}{n}, \frac{0}{n} \right) \right\}. \tag{12.1}
$$

**Definition:** If  $P \in \mathcal{P}_n$ , then the set of sequences of length n and type P is called the type class of  $P$ , denoted  $T(P)$ , i.e.,

$$
T(P) = \{ \mathbf{x} \in \mathcal{X}^n : P_{\mathbf{x}} = P \} . \tag{12.2}
$$

The type class is sometimes called the composition class of P.

**Example 12.1.1:** Let  $\mathcal{X} = \{1, 2, 3\}$ , a ternary alphabet. Let  $\mathbf{x} = 11321$ . Then the type  $P_x$  is

$$
P_x(1) = \frac{3}{5}
$$
,  $P_x(2) = \frac{1}{5}$ ,  $P_x(3) = \frac{1}{5}$ . (12.3)

The type class of  $P_x$  is the set of all sequences of length 5 with three 1's, one 2 and one 3. There are 20 such sequences, and

$$
T(P_x) = \{11123, 11132, 11213, \ldots, 32111\}.
$$
 (12.4)

The number of elements in  $T(P)$  is

$$
|T(P)| = {5 \choose 3, 1, 1} = \frac{5!}{3!1!1!} = 20.
$$
 (12.5)

The essential power of the method of types arises from the following theorem, which shows that the number of types is at most polynomial in n.

Theorem 12.1.1:

$$
|\mathcal{P}_n| \le (n+1)^{|\mathcal{X}|} \,. \tag{12.6}
$$

**Proof:** There are  $|\mathcal{X}|$  components in the vector that specifies  $P_x$ . The numerator in each component can take on only  $n + 1$  values. So there are at most  $(n + 1)^{|\mathcal{X}|}$  choices for the type vector. Of course, these choices are not independent (for example, the last choice is fixed by the others). But this is a sufficiently good upper bound for our needs.  $\square$ 

The crucial point here is that there are only a polynomial number of types of length n. Since the number of sequences is exponential in  $n$ , it follows that at least one type has exponentially many sequences in its type class. In fact, the largest type class has essentially the same number of elements as the entire set of sequences, to first order in the exponent.

Now, we will assume that the sequence  $X_1, X_2, \ldots, X_n$  is drawn i.i.d. according to a distribution  $Q(x)$ . All sequences with the same type will have the same probability, as shown in the following theorem. Let  $Q^{n}(x^{n}) = \prod_{i=1}^{n} Q(x_{i})$  denote the product distribution associated with Q.

**Theorem 12.1.2:** If  $X_1, X_2, \ldots, X_n$  are drawn i.i.d. according to  $Q(x)$ , then the probability of  $x$  depends only on its type and is given by

$$
Q^{n}(\mathbf{x}) = 2^{-n(H(P_{\mathbf{x}}) + D(P_{\mathbf{x}} \| Q))}.
$$
 (12.7)

Proof:

$$
Q^{n}(\mathbf{x}) = \prod_{i=1}^{n} Q(x_i)
$$
 (12.8)

$$
=\prod_{a\in\mathscr{X}}Q(a)^{N(a|\mathbf{x})}\tag{12.9}
$$

$$
=\prod_{a\in\mathscr{X}}Q(a)^{nP_{\mathbf{x}}(a)}\tag{12.10}
$$

$$
=\prod_{a\in\mathscr{X}}2^{nP_{\mathbf{x}}(a)\log Q(a)}\tag{12.11}
$$

$$
= \prod_{a \in \mathcal{X}} 2^{n(P_x(a) \log Q(a) - P_x(a) \log P_x(a) + P_x(a) \log P_x(a))}
$$
(12.12)

$$
=2^{n\sum_{a\in\mathscr{X}}(-P_{\mathbf{x}}(a)\log\frac{P_{\mathbf{x}}(a)}{\mathbf{Q}(a)}+P_{\mathbf{x}}(a)\log P_{\mathbf{x}}(a))}\tag{12.13}
$$

$$
=2^{n(-D(P_x||Q)-H(P_x))}.\quad \Box \tag{12.14}
$$

**Corollary:** If  $x$  is in the type class of  $Q$ , then

$$
Q^{n}(\mathbf{x}) = 2^{-nH(Q)}.
$$
 (12.15)

**Proof:** If  $x \in T(Q)$ , then  $P_x = Q$ , which can be substituted into  $(12.14)$ .  $\Box$ 

**Example 12.1.2:** The probability that a fair die produces a particular sequence of length n with precisely  $n/6$  occurrences of each face (n is a multiple of 6) is 2  $\binom{m}{s}$ ,  $\binom{s}{s}$ . . . . . . . . . . . . . . . . . . . has a probability mass function  $(\frac{1}{3}, \frac{1}{3}, \frac{1}{6}, \frac{1}{12}, \frac{1}{12}, 0)$ , the probability of observing a particular sequence with precisely these frequencies is precisely  $2^{-n\hat{H}(\frac{1}{3},\frac{1}{3},\frac{1}{6},\frac{1}{12},\frac{1}{12},0)}$  for *n* a multiple of 12. This is more interesting.

We now give an estimate of the size of a type class  $T(P)$ .

**Theorem 12.1.3** (Size of a type class  $T(P)$ ): For any type  $P \in \mathcal{P}_n$ ,

$$
\frac{1}{(n+1)^{|\mathscr{X}|}} 2^{nH(P)} \le |T(P)| \le 2^{nH(P)}.
$$
 (12.16)

**Proof:** The exact size of  $T(P)$  is easy to calculate. It is a simple combinatorial problem—the number of ways of arranging  $nP(a_1)$ ,  $nP(a_2), \ldots, nP(a_{|\mathscr{X}|})$  objects in a sequence, which is

$$
|T(P)| = {n \choose nP(a_1), nP(a_2), \dots, nP(a_{|\mathscr{X}|})}.
$$
 (12.17)

This value is hard to manipulate, so we derive simple exponential bounds on its value.

We suggest two alternative proofs for the exponential bounds.

The first proof uses Stirling's formula [110] to bound the factorial function, and after some algebra, we can obtain the bounds of the theorem.

We give an alternative proof. We first prove the upper bound. Since a type class must have probability  $\leq 1$ , we have

$$
1 \ge P^n(T(P)) \tag{12.18}
$$

$$
=\sum_{\mathbf{x}\in T(P)}P^{n}(\mathbf{x})
$$
\n(12.19)

$$
=\sum_{\mathbf{x}\in T(P)} 2^{-nH(P)}\tag{12.20}
$$

$$
= |T(P)| 2^{-nH(P)}, \t(12.21)
$$

using Theorem 12.1.2. Thus

$$
|T(P)| \le 2^{nH(P)} \tag{12.22}
$$

Now for the lower bound. We first prove that the type class  $T(P)$  has the highest probability among all type classes under the probability distribution P, i.e.,

$$
P^{n}(T(P)) \ge P^{n}(T(\hat{P})), \quad \text{for all } \hat{P} \in \mathcal{P}_{n}. \tag{12.23}
$$

We lower bound the ratio of probabilities,

$$
\frac{P^{n}(T(P))}{P^{n}(T(\hat{P}))} = \frac{|T(P)|\prod_{a \in \mathcal{X}} P(a)^{n^{P(a)}}}{|T(\hat{P})|\prod_{a \in \mathcal{X}} P(a)^{n^{P(a)}}}
$$
\n(12.24)

$$
= \frac{\binom{n}{p(a_1), nP(a_2), \dots, nP(a_{|\mathcal{X}|})} \prod_{a \in \mathcal{X}} P(a)^{nP(a)}}{\binom{n}{n\hat{P}(a_1), n\hat{P}(a_2), \dots, n\hat{P}(a_{|\mathcal{X}|})} \prod_{a \in \mathcal{X}} P(a)^{n\hat{P}(a)}} \qquad (12.25)
$$

$$
= \prod_{a \in \mathscr{X}} \frac{(n\hat{P}(a))!}{(nP(a))!} P(a)^{n(P(a) - \hat{P}(a))}.
$$
 (12.26)

Now using the simple bound (easy to prove by separately considering the cases  $m \ge n$  and  $m < n$ )

$$
\frac{m!}{n!} \ge n^{m-n} \,,\tag{12.27}
$$

we obtain

$$
\frac{P^{n}(T(P))}{P^{n}(T(\hat{P}))} \ge \prod_{a \in \mathcal{X}} (nP(a))^{n\hat{P}(a) - nP(a)} P(a)^{n(P(a) - \hat{P}(a))}
$$
\n(12.28)

$$
=\prod_{a\in\mathscr{X}}n^{n(\hat{P}(a)-P(a))}\tag{12.29}
$$

$$
=n^{n(\Sigma_{a\in\mathscr{X}}\hat{P}(a)-\Sigma_{a\in\mathscr{X}}P(a))}\tag{12.30}
$$

$$
= n^{n(1-1)} \tag{12.31}
$$

$$
=1. \t(12.32)
$$

Hence  $P^{n}(T(P)) \ge P^{n}(T(\hat{P}))$ . The lower bound now follows easily from this result, since

$$
1 = \sum_{Q \in \mathcal{P}_n} P^n(T(Q)) \tag{12.33}
$$

$$
\leq \sum_{Q \in \mathcal{P}_n} \max_{Q} P^n(T(Q)) \tag{12.34}
$$

$$
=\sum_{Q\in\mathscr{P}_n}P^n(T(P))\tag{12.35}
$$

$$
\leq (n+1)^{|\mathscr{X}|} P^{n}(T(P)) \tag{12.36}
$$

$$
=(n+1)^{|\mathcal{X}|}\sum_{\mathbf{x}\in T(P)}P^{n}(\mathbf{x})
$$
 (12.37)

$$
=(n+1)^{|\mathcal{X}|}\sum_{\mathbf{x}\in T(P)} 2^{-nH(P)} \tag{12.38}
$$

$$
= (n+1)^{|\mathscr{X}|} |T(P)| 2^{-nH(P)}, \qquad (12.39)
$$

where (12.36) follows from Theorem 12.1.1 and (12.38) follows from Theorem  $12.1.2$ .  $\Box$ 

We give a slightly better approximation for the binary case.

Example 12.1.3 (Binary alphabet): In this case, the type is defined by the number of l's in the sequence, and the size of the type class is therefore  $\binom{n}{k}$ . We show that

$$
\frac{1}{n+1} 2^{nH(\frac{k}{n})} \leq {n \choose k} \leq 2^{nH(\frac{k}{n})}.
$$
 (12.40)

These bounds can be proved using Stirling's approximation for the factorial function. But we provide a more intuitive proof below.

We first prove the upper bound. From the binomial formula, for any  $p$ ,

$$
\sum_{k=0}^{n} {n \choose k} p^{k} (1-p)^{n-k} = 1.
$$
 (12.41)

Since all the terms of the sum are positive for  $0 \le p \le 1$ , each of the terms is less than 1. Setting  $p = \frac{k}{n}$  and taking the k<sup>th</sup> term, we get

$$
1 \geq {n \choose k} \left(\frac{k}{n}\right)^k \left(1 - \frac{k}{n}\right)^{n-k} \tag{12.42}
$$

$$
=\binom{n}{k}2^{k\log\frac{k}{n}+(n-k)\log\frac{n-k}{n}}
$$
\n(12.43)

$$
=\binom{n}{k}2^{n(\frac{k}{n}\log\frac{k}{n}+\frac{n-k}{n}\log\frac{n-k}{n})}\tag{12.44}
$$

$$
=\binom{n}{k}2^{-nH(\frac{k}{n})}.
$$
 (12.45)

Hence

$$
\binom{n}{k} \le 2^{nH(\frac{k}{n})} \,. \tag{12.46}
$$

For the lower bound, let  $S$  be a random variable with a binomial distribution with parameters  $n$  and  $p$ . The most likely value of  $S$  is  $S = \langle np \rangle$ . This can be easily verified from the fact that

$$
\frac{P(S=i+1)}{P(S=i)} = \frac{n-i}{i+1} \frac{p}{1-p}
$$
(12.47)

and considering the cases when  $i < np$  and when  $i > np$ . Then, since there are  $n + 1$  terms in the binomial sum,

$$
1 = \sum_{k=0}^{n} {n \choose k} p^{k} (1-p)^{n-k} \leq (n+1) \max_{k} {n \choose k} p^{k} (1-p)^{n-k}
$$
(12.48)  
$$
= (n+1) {n \choose \langle np \rangle} p^{\langle np \rangle} (1-p)^{n-\langle np \rangle}
$$
. (12.49)

Now let  $p = \frac{k}{n}$ . Then we have

$$
1 \leq (n+1) {n \choose k} \left(\frac{k}{n}\right)^k \left(1-\frac{k}{n}\right)^{n-k}, \qquad (12.50)
$$

which by the arguments in  $(12.45)$  is equivalent to

$$
\frac{1}{n+1} \leq {n \choose k} 2^{-nH(\frac{k}{n})},
$$
\n(12.51)

or

$$
\binom{n}{k} \ge \frac{2^{nH(\frac{k}{n})}}{n+1} \tag{12.52}
$$

Combining the two results, we see that

$$
\binom{n}{k} \doteq 2^{nH\left(\frac{k}{n}\right)}.
$$
\n(12.53)

 $T$  is type cases of type cases): For any P E gn any P E gn and any P E gn any P E gn and any P E gn and any P E gn and any P E gn and any P E gn and any P E gn and any P E gn and any P E gn and any P E gn and any P E gn **Theorem 12.1.4** (Probability of type class). For any  $P \in \mathcal{P}_n$  and any  $\mu$  astribution  $Q$ , the probability of the type class  $\mu$ 

$$
\frac{1}{(n+1)^{|\mathscr{X}|}} 2^{-nD(P||Q)} \le Q^n(T(P)) \le 2^{-nD(P||Q)}.
$$
 (12.54)

Proof: We have

$$
Q^{n}(T(P)) = \sum_{\mathbf{x} \in T(P)} Q^{n}(\mathbf{x})
$$
 (12.55)

$$
=\sum_{\mathbf{x}\in T(P)} 2^{-n(D(P||Q)+H(P))} \tag{12.56}
$$

$$
= |T(P)| 2^{-n(D(P||Q) + H(P))}, \qquad (12.57)
$$

by Theorem 12.1.2. Using the bounds on  $|T(P)|$  derived in Theorem 12.1.3, we have

$$
\frac{1}{(n+1)^{|\mathcal{X}|}} 2^{-nD(P||Q)} \leq Q^{n}(T(P)) \leq 2^{-nD(P||Q)}.\quad \Box \qquad (12.58)
$$

We can summarize the basic theorems concerning types in four equations:

$$
|\mathcal{P}_n| \le (n+1)^{|\mathcal{X}|},\tag{12.59}
$$

$$
Q^{n}(\mathbf{x}) = 2^{-n(D(P_{\mathbf{x}}||Q) + H(P_{\mathbf{x}}))}, \qquad (12.60)
$$

$$
|T(P)| = 2^{nH(P)}, \qquad (12.61)
$$

$$
Q^{n}(T(P)) = 2^{-n(P||Q)}.
$$
 (12.62)

These equations state that there are only a polynomial number of types and that there are an exponential number of sequences of each type. We also have an exact formula for the probability of any sequence of type P under distribution Q and an approximate formula for the probability of a type class.

These equations allow us to calculate the behavior of long sequences based on the properties of the type of the sequence. For example, for long sequences drawn i.i.d. according to some distribution, the type of the sequence is close to the distribution generating the sequence, and we can use the properties of this distribution to estimate the properties of the sequence. Some of the applications that will be dealt with in the next few sections are as follows:

- The law of large numbers.
- Universal source coding.
- Sanov's theorem.
- Stein's lemma and hypothesis testing.
- Conditional probability and limit theorems.

## 12.2 THE LAW OF LARGE NUMBERS

The concept of type and type classes enables us to give an alternative interpretation to the law of large numbers. In fact, it can be used as a proof of a version of the weak law in the discrete case.

286

The most important property of types is that there are only a polynomial number of types, and an exponential number of sequences of each type. Since the probability of each type class depends exponentially on the relative entropy distance between the type  $P$  and the distribution Q, type classes that are far from the true distribution have exponentially smaller probability.

Given an  $\epsilon > 0$ , we can define a typical set  $T^{\epsilon}_{Q}$  of sequences for the distribution  $Q^n$  as

$$
T_{Q}^{\epsilon} = \{x^{n} : D(P_{x^{n}} \| Q) \le \epsilon\}.
$$
 (12.63)

Then the probability that  $x^n$  is not typical is

$$
1 - Q^{n}(T_{Q}^{\epsilon}) = \sum_{P: D(P||Q) > \epsilon} Q^{n}(T(P))
$$
\n(12.64)

$$
\leq \sum_{P\,:\,D(P||Q)>\epsilon} 2^{-nD(P||Q)} \quad (\text{Theorem 12.1.4}) \quad (12.65)
$$

$$
\leq \sum_{P \,:\, D(P||Q) > \epsilon} 2^{-n\epsilon} \tag{12.66}
$$

$$
\leq (n+1)^{|\mathscr{X}|} 2^{-n\epsilon}
$$
 (Theorem 12.1.1) (12.67)

$$
=2^{-n\left(\epsilon-|\mathscr{X}| \frac{\log(n+1)}{n}\right)},\tag{12.68}
$$

which goes to 0 as  $n \rightarrow \infty$ . Hence, the probability of the typical set goes to 1 as  $n \rightarrow \infty$ . This is similar to the AEP proved in Chapter 3, which is a form of the weak law of large numbers.

**Theorem 12.2.1:** Let  $X_1, X_2, ..., X_n$  be i.i.d.  $\sim P(x)$ . Then

$$
\Pr\{D(P_{x^n}||P) > \epsilon\} \le 2^{-n\left(\epsilon - |\mathcal{X}| \frac{\log(n+1)}{n}\right)},\tag{12.69}
$$

and consequently,  $D(P_{x^n}||P) \to 0$  with probability 1.

Proof: The inequality (12.69) was proved in (12.68). Summing over n, we find

$$
\sum_{n=1}^{\infty} \Pr\{D(P_{x^n} \| P) > \epsilon\} < \infty. \tag{12.70}
$$

Thus the expected number of occurrences of the event  ${D(P_{x^n}||P) > \epsilon}$ for all  $n$  is finite, which implies that the actual number of such occurrences is also finite with probability 1 (Borel-Cantelli lemma). Hence  $D(P_{x^n}||P) \to 0$  with probability 1.  $\square$ 

We now define a stronger version of typicality.

**Definition:** We will define the strongly typical set  $A_{i}^{\prime\prime\prime}$  to be the set of sequences in  $\mathcal{X}^n$  for which the sample frequencies are close to the true values, i.e.,

$$
A_{\epsilon}^{(n)} = \left\{ \mathbf{x} \in \mathcal{X}^n : \left| \frac{1}{n} N(a|\mathbf{x}) - P(a) \right| < \frac{\epsilon}{|\mathcal{X}|}, \text{ for all } a \in \mathcal{X} \right\} \tag{12.71}
$$

Hence the typical set consists of sequences whose type does not differ from the true probabilities by more than  $\epsilon/|\mathcal{X}|$  in any component.

By the strong law of large numbers, it follows that the probability of the strongly typical set goes to 1 as  $n \rightarrow \infty$ .

The additional power afforded by strong typicality is useful in proving stronger results, particularly in universal coding, rate distortion theory and large deviation theory.

## 12.3 UNIVERSAL SOURCE CODING

Huffman coding compresses an i.i.d. source with a known distribution  $p(x)$  to its entropy limit  $H(X)$ . However, if the code is designed for some incorrect distribution  $q(x)$ , a penalty of  $D(p||q)$  is incurred. Thus Huffman coding is sensitive to the assumed distribution.

What compression can be achieved if the true distribution  $p(x)$  is unknown? Is there a universal code of rate  $R$ , say, that suffices to describe every i.i.d. source with entropy  $H(X) < R$ ? The surprising answer is yes.

The idea is based on the method of types. There are  $2^{nH(P)}$  sequences of type P. Since there are only a polynomial number of types with denominator n, an enumeration of all sequences  $x^n$  with type  $P_{x^n}$  such that  $H(P_{n}) < R$  will require roughly nR bits. Thus, by describing all such  $\mathbf{x}(x, y) \geq \mathbf{x}$  will require reaging that sheet finds, by describing a  $\frac{1}{2}$  $\frac{1}{2}$  defined and the properties to distributing dispersion  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_i$  and  $\mathbf{m}_$ 

 $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{D}$   $\overline{$ wejimuwa.<br>..... which has an unknown distribution  $Q$  consists of two mappings, the encoder,  $f(x)$  rate  $f(x)$  of rate  $\mathcal{L}$  rate  $\mathcal{L}$ where the other distribution  $\alpha_1, \alpha_2, \ldots, \alpha_n$ 

$$
f_n: \mathcal{X}^n \to \{1, 2, \dots, 2^{nR}\},\tag{12.72}
$$

and the decoder,

$$
\phi_n: \{1, 2, \dots, 2^{nR}\} \to \mathcal{X}^n \,. \tag{12.73}
$$

Here  $R$  is called the rate of the code. The probability of error for the code with respect to the distribution  $Q$  is

$$
P_e^{(n)} = Q^n(X_1, X_2, \dots, X_n : \phi_n(f_n(X_1, X_2, \dots, X_n)) \neq (X_1, X_2, \dots, X_n))
$$
\n(12.74)

**Definition:** A rate  $R$  block code for a source will be called *universal* if the functions  $f_n$  and  $\phi_n$  do not depend on the distribution Q and if  $P_n^{(n)} \to 0$  as  $n \to \infty$  if  $R > H(Q)$ .

We now describe one such universal encoding scheme, due to Csiszar and Körner [83], that is based on the fact that the number of sequences of the type  $P$  increases exponentially with the entropy and the fact that there are only a polynomial number of types.

**Theorem 12.3.1:** There exists a sequence of  $(2^{nR}, n)$  universal source codes such that  $P_{e}^{(n)} \rightarrow 0$  for every source Q such that  $H(Q) < R$ .

**Proof:** Fix the rate  $R$  for the code. Let

$$
R_n = R - |\mathcal{X}| \frac{\log(n+1)}{n} \,. \tag{12.75}
$$

Consider the set of sequences

$$
A = \{ \mathbf{x} \in \mathcal{X}^n : H(P_{\mathbf{x}}) \le R_n \} . \tag{12.76}
$$

Then

$$
|A| = \sum_{P \in \mathcal{P}_n : H(P) \le R_n} |T(P)| \qquad (12.77)
$$

$$
\leq \sum_{P \in \mathcal{P}_n \; : H(P) \leq R_n} 2^{nH(P)} \tag{12.78}
$$

$$
\leq \sum_{P \in \mathcal{P}_n : H(P) \leq R_n} 2^{nR_n} \tag{12.79}
$$

$$
\leq (n+1)^{|\mathscr{X}|} 2^{nR_n} \tag{12.80}
$$

$$
=2^{n\left(R_n+\lvert\mathcal{X}\rvert\frac{\log(n+1)}{n}\right)}\tag{12.81}
$$

$$
=2^{nR}.
$$
 (12.82)

By indexing the elements of A, we define the encoding  $f_n$  as

$$
f_n(\mathbf{x}) = \begin{cases} \text{index of } \mathbf{x} \text{ in } A & \text{if } x \in A, \\ 0 & \text{otherwise.} \end{cases} \tag{12.83}
$$

The decoding function maps each index onto the corresponding element of A. Hence all the elements of A are encoded correctly, and all the remaining sequences result in an error. The set of sequences that are encoded correctly is illustrated in Figure 12.1.

We will now show that this encoding scheme is universal. Assume that the distribution of  $X_1, X_2, \ldots, X_n$  is Q and  $H(Q) < R$ . Then the probability of decoding error is given by

$$
P_e^{(n)} = 1 - Q^n(A) \tag{12.84}
$$

$$
= \sum_{P: H(P) > R_n} Q^n(T(P)) \tag{12.85}
$$

$$
\leq (n+1)^{|\mathcal{X}|} \max_{P: H(P) > R_n} Q^{n}(T(P))
$$
\n(12.86)

$$
\leq (n+1)^{|\mathscr{X}|} 2^{-n \min_{P \,:\, H(P) > R_n} D(P \,|\, Q)} \tag{12.87}
$$

Since  $R_n \nmid R$  and  $H(Q) < R$ , there exists  $n_0$  such that for all  $n \ge n_0$ ,  $R_n > H(Q)$ . Then for  $n \geq n_0$ ,  $\min_{P: H(P) > R_n} D(P||Q)$  must be greater than 0, and the probability of error  $P_{\epsilon}^{\prime\prime\prime}$  converges to 0 exponentially fast as  $n\rightarrow\infty$ .

On the other hand, if the distribution Q is such that the entropy  $H(Q)$ is greater than the rate  $R$ , then with high probability, the sequence will have a type outside the set A. Hence, in such cases the probability of error is close to 1.

Image /page/11/Figure/10 description: A black and white illustration shows a large equilateral triangle with a smaller circle inside it. The text "H(P) = R" is written below the circle.

Figure 12.1. Universal code and the probability simplex.

Image /page/12/Figure/1 description: A graph shows the relationship between the rate of code and error exponent. The x-axis is labeled "Rate of code" and the y-axis is labeled "Error exponent". The graph starts at the origin and increases exponentially as the rate of code increases. There is a vertical line at H(Q) on the x-axis, indicating a threshold value.

Figure 12.2. Error exponent for the universal code.

The exponent in the probability of error is

$$
D_{R,Q}^{*} = \min_{P:H(P)>R} D(P||Q), \qquad (12.88)
$$

which is illustrated in Figure 12.2.  $\Box$ 

The universal coding scheme described here is only one of many such schemes. It is universal over the set of i.i.d. distributions. There are other schemes like the Lempel-Ziv algorithm, which is a variable rate universal code for all ergodic sources. The Lempel-Ziv algorithm, discussed in Section 12.10, is often used in practice to compress data which cannot be modeled simply, such as English text or computer source code.

One may wonder why it is ever necessary to use Huffman codes, which are specific to a probability distribution. What do we lose in using a universal code?

Universal codes need a longer block length to obtain the same performance as a code designed specifically for the probability distribution. We pay the penalty for this increase in block length by the increased complexity of the encoder and decoder. Hence a distribution specific code is best if one knows the distribution of the source.

##### 12.4 LARGE DEVIATION THEORY

The subject of large deviation theory can be illustrated by an example. What is the probability that  $\frac{1}{n} \Sigma X_i$  is near 1/3, if  $X_1, X_2, \ldots, X_n$  are drawn i.i.d. Bernoulli $(1/3)$ ? This is a small deviation (from the expected outcome) and the probability is near 1. Now what is the probability that  $\frac{1}{n}$   $\sum X_i$  is greater than 3/4 given that  $X_1, X_2, \ldots, X_n$  are Bernoulli(1/3)? This is a large deviation, and the probability is exponentially small. We might estimate the exponent using the central limit theorem, but this is a poor approximation for more than a few standard deviations. We note that  $\frac{1}{n} \sum X_i = 3/4$  is equivalent to  $P_x = (1/4, 3/4)$ . Thus the probability that  $\overline{X}_n$  is near 3/4 is the probability of the corresponding type. The probability of this large deviation will turn out to be  $\approx 2^{-nD((\frac{3}{4},\frac{1}{4})\sqrt{(\frac{1}{3},\frac{2}{3}))}}$ . In this section, we estimate the probability of a set of non-typical types.

Let  $E$  be a subset of the set of probability mass functions. For example, E may be the set of probability mass functions with mean  $\mu$ . With a slight abuse of notation, we write

$$
Q^{n}(E) = Q^{n}(E \cap \mathcal{P}_{n}) = \sum_{\mathbf{x}: P_{\mathbf{x}} \in E \cap \mathcal{P}_{n}} Q^{n}(\mathbf{x}). \qquad (12.89)
$$

If  $E$  contains a relative entropy neighborhood of  $Q$ , then by the weak law of large numbers (Theorem 12.2.1),  $Q^n(E) \rightarrow 1$ . On the other hand, if E does not contain Q or a neighborhood of Q, then by the weak law of large numbers,  $Q^n(E) \rightarrow 0$  exponentially fast. We will use the method of types to calculate the exponent.

Let us first give some examples of the kind of sets  $E$  that we are considering. For example, assume that by observation we find that the sample average of  $g(X)$  is greater than or equal to  $\alpha$ , i.e.,  $\frac{1}{n} \sum_{i} g(X_i) \geq \alpha$ . This event is equivalent to the event  $P_x \in E \cap \mathcal{P}_n$ , where

$$
E = \left\{ P : \sum_{a \in \mathscr{X}} g(a)P(a) \ge \alpha \right\},\tag{12.90}
$$

because

$$
\frac{1}{n}\sum_{i=1}^{n}g(X_i)\geq \alpha \Leftrightarrow \sum_{a\in\mathcal{X}}P_{\mathbf{x}}(a)g(a)\geq \alpha
$$
\n(12.91)

$$
\Leftrightarrow P_{\mathbf{x}} \in E \cap \mathcal{P}_n. \tag{12.92}
$$

Thus

$$
\Pr\left(\frac{1}{n}\sum_{i=1}^{n}g(X_i)\geq\alpha\right)=Q^n(E\cap\mathcal{P}_n)=Q^n(E). \hspace{1cm} (12.93)
$$

Here  $E$  is a half space in the space of probability vectors, as illustrated in Figure 12.3.

**Theorem 12.4.1** (Sanov's theorem): Let  $X_1, X_2, \ldots, X_n$  be i.i.d.  $\sim Q(x)$ . Let  $E \subseteq \mathcal{P}$  be a set of probability distributions. Then

$$
Q^{n}(E) = Q^{n}(E \cap \mathcal{P}_{n}) \le (n+1)^{|\mathcal{X}|} 2^{-nD(P^{*}||Q)}, \qquad (12.94)
$$

where

$$
P^* = \arg\min_{P \in E} D(P||Q), \qquad (12.95)
$$

is the distribution in E that is closest to Q in relative entropy.

Image /page/14/Figure/1 description: A diagram shows a large triangle with two points labeled P\* and Q inside. A line segment connects two sides of the triangle, passing through P\*. The region above the line segment and within the triangle is labeled E.

Figure 12.3. The probability simplex and Sanov's theorem.

If, in addition, the set  $E$  is the closure of its interior, then

$$
\frac{1}{n}\log Q^{n}(E) \rightarrow -D(P^{*}||Q). \qquad (12.96)
$$

Proof: We first prove the upper bound:

$$
Q^{n}(E) = \sum_{P \in E \cap \mathcal{P}_{n}} Q^{n}(T(P))
$$
\n(12.97)

$$
\leq \sum_{P \in E \cap \mathcal{P}_n} 2^{-nD(P||Q)} \tag{12.98}
$$

$$
\leq \sum_{P \in E \cap \mathcal{P}_n} \max_{P \in E \cap \mathcal{P}_n} 2^{-nD(P||Q)} \tag{12.99}
$$

$$
=\sum_{P\in E\cap\mathcal{P}_n}2^{-n\min_{P\in E\cap\mathcal{P}_n}D(P\|Q)}\tag{12.100}
$$

$$
\leq \sum_{P \in E \cap \mathcal{P}_n} 2^{-n \min_{P \in E} D(P \| Q)} \tag{12.101}
$$

$$
=\sum_{P\in E\cap\mathscr{P}_n}2^{-nD(P^*||Q)}\tag{12.102}
$$

$$
\leq (n+1)^{|\mathcal{X}|} 2^{-nD(P^*||Q)} \tag{12.103}
$$

where the last inequality follows from Theorem 12.1.1.  $N_{\rm eff}$  is a member of  $P_{\rm eff}$  not be a member of  $P_{\rm eff}$  . We now come to the lower

bound, for which we need not be a member of  $n_n$ , we now can be set for all  $n_n$ . bound, for which we need a nice  $\sec \mu$ , so that for an large  $\mu$ , we can Find a distribution in  $E \cap \mathcal{F}_n$  which is close to  $F$ . If we now assume the interior must be non-empty),

then since  $\bigcup_{n} \mathcal{P}_n$  is dense in the set of all distributions, it follows that  $E \cap \mathscr{P}_n$  is non-empty for all  $n \geq n_0$  for some  $n_0$ . We can then find a sequence of distributions  $P_n$  such that  $P_n \n\in E \cap \mathcal{P}_n$  and  $D(P_n||Q)$  $\rightarrow D(P^*||Q)$ . For each  $n \geq n_0$ ,

$$
Q^{n}(E) = \sum_{P \in E \cap \mathcal{P}_n} Q^{n}(T(P)) \qquad (12.104)
$$

$$
\geq Q^n(T(P_n))\tag{12.105}
$$

$$
\geq \frac{1}{(n+1)^{|\mathscr{X}|}} 2^{-nD(P_n||Q)}.
$$
 (12.106)

Consequently,

$$
\liminf \frac{1}{n} \log Q^{n}(E) \ge \liminf \left( -\frac{|\mathcal{X}| \log(n+1)}{n} - D(P_{n} \| Q) \right)
$$

$$
= -D(P*\|Q).
$$
 (12.107)

Combining this with the upper bound establishes the theorem.  $\Box$ 

This argument can also be extended to continuous distributions using quantization.

##### 12.5 EXAMPLES OF SANOV'S THEOREM

Suppose we wish to find  $\Pr\{\frac{1}{n}\sum_{i=1}^n g_i(X_i) \geq \alpha_i, j=1,2,\ldots,k\}$ . Then the set  $E$  is defined as

$$
E = \left\{ P : \sum_{a} P(a)g_j(a) \ge \alpha_j, j = 1, 2, \ldots, k \right\}.
$$
 (12.108)

To find the closest distribution in E to Q, we minimize  $D(P||Q)$  subject to the constraints in (12.108). Using Lagrange multipliers, we construct the functional

$$
J(P) = \sum_{x} P(x) \log \frac{P(x)}{Q(x)} + \sum_{i} \lambda_i \sum_{x} P(x) g_i(x) + \nu \sum_{x} P(x) \,. \tag{12.109}
$$

We then differentiate and calculate the closest distribution to Q to be of the form

$$
P^*(x) = \frac{Q(x)e^{\sum_i \lambda_i g_i(x)}}{\sum_{a \in \mathscr{X}} Q(a)e^{\sum_i \lambda_i g_i(a)}},
$$
\n(12.110)

where the constants  $\lambda_i$  are chosen to satisfy the constraints. Note that if  $Q$  is uniform, then  $P^*$  is the maximum entropy distribution. Verification that  $P^*$  is indeed the minimum follows from the same kind of arguments as given in Chapter 11.

Let us consider some specific examples:

**Example 12.5.1** (Dice): Suppose we toss a fair die n times; what is the probability that the average of the throws is greater than or equal to 4? From Sanov's theorem, it follows that

$$
Q^{n}(E) \doteq 2^{-nD(P^*||Q)}, \qquad (12.111)
$$

where  $P^*$  minimizes  $D(P||Q)$  over all distributions P that satisfy

$$
\sum_{i=1}^{6} iP(i) \ge 4.
$$
 (12.112)

From  $(12.110)$ , it follows that  $P^*$  has the form

$$
P^*(x) = \frac{2^{\lambda x}}{\sum_{i=1}^6 2^{\lambda i}},
$$
 (12.113)

with  $\lambda$  chosen so that  $\Sigma iP^*(i) = 4$ . Solving numerically, we obtain  $\lambda = 0.2519$ , and  $P^* = (0.1031, 0.1227, 0.1461, 0.1740, 0.2072, 0.2468)$ , and therefore  $D(P^*||Q) = 0.0624$  bits. Thus, the probability that the average of 10000 throws is greater than or equal to 4 is  $\approx 2^{-624}$ .

**Example 12.5.2** (Coins): Suppose we have a fair coin, and want to estimate the probability of observing more than 700 heads in a series of 1000 tosses. The problem is like the previous example. The probability is

$$
P(\bar{X}_n \ge 0.7) = 2^{-nD(P^*||Q)} \tag{12.114}
$$

where  $P^*$  is the (0.7,0.3) distribution and Q is the (0.5,0.5) distribution. In this case,  $D(P^*||Q) = 1 - H(P^*) = 1 - H(0.7) = 0.119$ . Thus the probability of 700 or more heads in 1000 trials is approximately  $2^{-119}$ 

**Example 12.5.3** (Mutual dependence): Let  $Q(x, y)$  be a given joint distribution and let  $Q_0(x, y) = Q(x)Q(y)$  be the associated product distribution formed from the marginals of Q. We wish to know the likelihood that a sample drawn according to  $Q_0$  will "appear" to be jointly distributed according to Q. Accordingly, let  $(X_i, Y_i)$  be i.i.d.  $-Q_0(x, y) = Q(x)Q(y)$ . We define joint typicality as we did in Section 8.6, i.e.,  $(x^n, y^n)$  is jointly typical with respect to a joint distribution  $Q(x, y)$ iff the sample entropies are close to their true values, i.e.,

$$
\left| -\frac{1}{n} \log Q(x^n) - H(X) \right| \le \epsilon \,, \tag{12.115}
$$

$$
\left| -\frac{1}{n} \log Q(y^n) - H(Y) \right| \le \epsilon \;, \tag{12.116}
$$

and

$$
\left| -\frac{1}{n} \log Q(x^n, y^n) - H(X, Y) \right| \le \epsilon \ . \tag{12.117}
$$

We wish to calculate the probability (under the product distribution) of seeing a pair  $(x^n, y^n)$  that looks jointly typical of Q, i.e.,  $(x^n, y^n)$  satisfies  $(12.115)-(12.117)$ . Thus  $(x^n, y^n)$  are jointly typical with respect to  $Q(x, y)$  if  $P_{x^n, y^n} \in E \cap \mathcal{P}_n(X, Y)$ , where

$$
E = \left\{ P(x, y) : \left| -\sum_{x, y} P(x, y) \log Q(x) - H(X) \right| \le \epsilon ,
$$
$$
\left| -\sum_{x, y} P(x, y) \log Q(y) - H(Y) \right| \le \epsilon ,
$$
$$
\left| -\sum_{x, y} P(x, y) \log Q(x, y) - H(X, Y) \right| \le \epsilon \right\}. (12.118)
$$

Using Sanov's theorem, the probability is

$$
Q_0^n(E) = 2^{-nD(P^*||Q_0)}, \qquad (12.119)
$$

where  $P^*$  is the distribution satisfying the constraints that is closest to  $Q_0$  in relative entropy. In this case, as  $\epsilon \to 0$ , it can be verified (Problem 10) that  $P^*$  is the joint distribution Q, and  $Q_0$  is the product distribution, so that the probability is  $2^{-nD(Q(x,y))}$  =  $2^{-nD(x,y)}$ , which is the same as the result derived in Chapter 8 for the joint AEP

In the next section, we consider the empirical distribution of the sequence of outcomes given that the type is in a particular set of distributions E. We will show that not only is the probability of the set  $E$ essentially determined by  $D(P^*||Q)$ , the distance of the closest element of E to Q, but also that the conditional type is essentially  $P^*$ , so that given that we are in set  $E$ , the type is very likely to be close to  $P^*$ .

##### 12.6 THE CONDITIONAL LIMIT THEOREM

It has been shown that the probability of a set of types under a distribution Q is essentially determined by the probability of the closest element of the set to  $Q$ ; the probability is  $2^{-n}b^*$  to first order in the exponent, where

$$
D^* = \min_{P \in E} D(P||Q). \qquad (12.120)
$$

This follows because the probability of the set of types is the sum of the probabilities of each type, which is bounded by the largest term times the number of terms. Since the number of terms is polynomial in the length of the sequences, the sum is equal to the largest term to first order in the exponent.

We now strengthen the argument to show that not only is the probability of the set  $E$  essentially the same as the probability of the closest type  $P^*$  but also that the total probability of other types that are far away from  $P^*$  is negligible. This implies that with very high probability, the observed type is close to  $P^*$ . We call this a conditional limit theorem.

Before we prove this result, we prove a "Pythagorean" theorem, which gives some insight into the geometry of  $D(P||Q)$ . Since  $D(P||Q)$  is not a metric, many of the intuitive properties of distance are not valid for  $D(P||Q)$ . The next theorem shows a sense in which  $D(P||Q)$  behaves like the square of the Euclidean metric (Figure 12.4).

**Theorem 12.6.1:** For a closed convex set  $E \subset \mathcal{P}$  and distribution  $Q \not\in E$ , let  $P^* \in E$  be the distribution that achieves the minimum distance to Q. i.e.,

Image /page/18/Figure/8 description: A diagram shows a large triangle with the letter E inside it. Within the triangle, there are two points labeled P and P\*, and a point labeled Q. A line segment connects P and P\*. Another line segment connects P\* to Q. A third line segment connects P to Q. A line segment also passes through P and P\* and extends to the edges of the large triangle. Another line segment connects P to Q.

Figure 12.4. Pythagorean theorem for relative entropy.

$$
D(P^* \| Q) = \min_{P \in E} D(P \| Q) .
$$
 (12.121)

Then

$$
D(P||Q) \ge D(P||P^*) + D(P^*||Q)
$$
\n(12.122)

for all  $P \in E$ .

Note: The main use of this theorem is as follows: suppose we have a sequence  $P_n \in E$  that yields  $D(P_n \| Q) \to D(P^* \| Q)$ . Then from the Pythagorean theorem,  $D(P_n||P^*) \to 0$  as well.

**Proof:** Consider any  $P \in E$ . Let

$$
P_{\lambda} = \lambda P + (1 - \lambda)P^* \tag{12.123}
$$

Then  $P_{\lambda} \to P^*$  as  $\lambda \to 0$ . Also since E is convex,  $P_{\lambda} \in E$  for  $0 \le \lambda \le 1$ . Since  $D(P^*||\hat{Q})$  is the minimum of  $D(P_{\lambda}||Q)$  along the path  $P^* \to P$ , the derivative of  $D(P_{\lambda} || Q)$  as a function of  $\lambda$  is non-negative at  $\lambda = 0$ . Now

$$
D_{\lambda} = D(P_{\lambda} || Q) = \sum P_{\lambda}(x) \log \frac{P_{\lambda}(x)}{Q(x)}, \qquad (12.124)
$$

and

$$
\frac{dD_{\lambda}}{d\lambda} = \sum \Bigl( (P(x) - P^*(x)) \log \frac{P_{\lambda}(x)}{Q(x)} + (P(x) - P^*(x)) \Bigr) \ . \tag{12.125}
$$

Setting  $\lambda = 0$ , so that  $P_{\lambda} = P^*$  and using the fact that  $\sum P(x) = \sum P^*(x) =$ 1, we have

$$
0 \le \left(\frac{dD_{\lambda}}{d\lambda}\right)_{\lambda=0} \tag{12.126}
$$

$$
= \sum (P(x) - P^{*}(x)) \log \frac{P^{*}(x)}{Q(x)}
$$
 (12.127)

$$
= \sum P(x) \log \frac{P^*(x)}{Q(x)} - \sum P^*(x) \log \frac{P^*(x)}{Q(x)}
$$
(12.128)

$$
= \sum P(x) \log \frac{P(x)}{Q(x)} \frac{P^*(x)}{P(x)} - \sum P^*(x) \log \frac{P^*(x)}{Q(x)}
$$
(12.129)

$$
= D(P||Q) - D(P||P^*) - D(P^*||Q), \qquad (12.130)
$$

which proves the theorem.  $\Box$ 

298

Note that the relative entropy  $D(P||Q)$  behaves like the square of the Euclidean distance. Suppose we have a convex set E in  $\mathcal{R}^n$ . Let A be a point outside the set,  $B$  the point in the set closest to  $A$ , and  $C$  any other point in the set. Then the angle between the lines BA and BC must be obtuse, which implies that  $l_{AC} \geq l_{AB}^2 + l_{BC}^2$ , which is of the same form as the above theorem. This is illustrated in Figure 12.5.

We now prove a useful lemma which shows that convergence in relative entropy implies convergence in the  $\mathscr{L}_1$  norm.

**Definition:** The  $\mathscr{L}_1$  distance between any two distributions is defined as

$$
||P_1 - P_2||_1 = \sum_{a \in \mathcal{X}} |P_1(a) - P_2(a)|.
$$
 (12.131)

Let A be the set on which  $P_1(x) > P_2(x)$ . Then

$$
||P_1 - P_2||_1 = \sum_{x \in \mathcal{X}} |P_1(x) - P_2(x)| \tag{12.132}
$$

$$
= \sum_{x \in A} (P_1(x) - P_2(x)) + \sum_{x \in A^c} (P_2(x) - P_1(x)) \quad (12.133)
$$

$$
= P_1(A) - P_2(A) + P_2(A^c) - P_1(A^c)
$$
 (12.134)

$$
= P_1(A) - P_2(A) + 1 - P_2(A) - 1 + P_1(A) \qquad (12.135)
$$

$$
=2(P_1(A)-P_2(A))\,.
$$
 (12.136)

Also note that

$$
\max_{B \subseteq \mathcal{X}} (P_1(B) - P_2(B)) = P_1(A) - P_2(A) = \frac{\|P_1 - P_2\|_1}{2} \ . \tag{12.137}
$$

Image /page/20/Figure/13 description: A diagram shows a triangle ABC with a curved line passing through point B and intersecting side AC. Point A is at the top, point B is on the left side of the triangle and on the curved line, and point C is at the bottom right of the triangle.

Figure 12.5. Triangle inequality for distance squared.

The left hand side of (12.137) is called the *variational distance* between  $P_1$  and  $P_2$ .

###### Lemma 12.6.1:

$$
D(P_1 \| P_2) \ge \frac{1}{2 \ln 2} \| P_1 - P_2 \|_1^2 \tag{12.138}
$$

Proof: We first prove it for the binary case. Consider two binary distributions with parameters p and q with  $p \ge q$ . We will show that

$$
p \log \frac{p}{q} + (1-p) \log \frac{1-p}{1-q} \ge \frac{4}{2 \ln 2} (p-q)^2. \tag{12.139}
$$

The difference  $g(p, q)$  between the two sides is

$$
g(p,q) = p \log \frac{p}{q} + (1-p) \log \frac{1-p}{1-q} - \frac{4}{2 \ln 2} (p-q)^2.
$$
 (12.140)

Then

$$
\frac{dg(p,q)}{dq} = -\frac{p}{q \ln 2} + \frac{1-p}{(1-q) \ln 2} - \frac{4}{2 \ln 2} 2(q-p) \quad (12.141)
$$

$$
= \frac{q-p}{q(1-q)\ln 2} - \frac{4}{\ln 2}(q-p)
$$
 (12.142)

$$
\leq 0, \tag{12.143}
$$

since  $q(1 - q) \leq \frac{1}{4}$  and  $q \leq p$ . For  $q = p$ ,  $g(p, q) = 0$ , and hence  $g(p, q) \geq 0$ for  $q \leq p$ , which proves the lemma for the binary case.

For the general case, for any two distributions  $P_1$  and  $P_2$ , let

$$
A = \{x : P_1(x) > P_2(x)\} \tag{12.144}
$$

Define a new binary random variable  $Y = \phi(X)$ , the indicator of the set A, and let  $\hat{P}_1$  and  $\hat{P}_2$  be the distributions of Y. Thus  $\hat{P}_1$  and  $\hat{P}_2$  correspond to the quantized versions of  $P_1$  and  $P_2$ . Then by the data processing inequality applied to relative entropies (which is proved in the same way as the data processing inequality for mutual information), we have

$$
D(P_1 \| P_2) \ge D(P_1 \| P_2) \tag{12.145}
$$

$$
\geq \frac{4}{2\ln 2} (P_1(A) - P_2(A))^2
$$
 (12.146)

$$
= \frac{1}{2\ln 2} \|P_1 - P_2\|_1^2 \tag{12.147}
$$

by (12.137), and the lemma is proved.  $\Box$ 

We can now begin the proof of the conditional limit theorem. We first outline the method used. As stated at the beginning of the chapter, the essential idea is that the probability of a type under Q depends exponentially on the distance of the type from Q, and hence types that are further away are exponentially less likely to occur. We divide the set of types in  $E$  into two categories: those at about the same distance from  $Q$ as  $P^*$  and those a distance  $2\delta$  farther away. The second set has exponentially less probability than the first, and hence the first set has a conditional probability tending to 1. We then use the Pythagorean theorem to establish that all the elements in the first set are close to  $P^*$ , which will establish the theorem.

The following theorem is an important strengthening of the maximum entropy principle.

Theorem 12.6.2 (Conditional limit theorem): Let E be a closed convex subset of  $\mathscr Y$  and let  $Q$  be a distribution not in E. Let  $X_1, X_2, \ldots, X_n$  be aiscrete random variables drawn i.i.d.  $\sim$ Q. Let P\* achieve  $\min_{l}$  $D(P||Q)$ . Then

$$
\Pr(X_1 = a | P_{X^n} \in E) \to P^*(a) \tag{12.148}
$$

in probability as  $n \rightarrow \infty$ , i.e., the conditional distribution of  $X_1$ , given that the type of the sequence is in E, is close to  $P^*$  for large n.

**Example 12.6.1:** If  $X_i$  i.i.d.  $\sim Q$ , then

$$
\Pr\bigg\{X_1 = a \left| \frac{1}{n} \sum X_i^2 \ge a \right\} \to P^*(a),\tag{12.149}
$$

where  $P^*(a)$  minimizes  $D(P||Q)$  over P satisfying  $\sum P(a)a^2 \ge \alpha$ . This minimization results in

$$
P^*(a) = Q(a) \frac{e^{\lambda a^2}}{\sum_a Q(a)e^{\lambda a^2}},
$$
 (12.150)

where  $\lambda$  is chosen to satisfy  $\sum P^*(a)a^2 = \alpha$ . Thus the conditional distribution on  $X_1$  given a constraint on the sum of the squares is a (normalized) product of the original probability mass function and the maximum entropy probability mass function (which in this case is Gaussian).

Proof of Theorem: Define the sets

$$
S_t = \{ P \in \mathcal{P} : D(P||Q) \le t \} . \tag{12.151}
$$

The sets  $S_t$  are convex since  $D(P||Q)$  is a convex function of P. Let

$$
D^* = D(P^* || Q) = \min_{P \in E} D(P || Q).
$$
 (12.152)

Then  $P^*$  is unique, since  $D(P||Q)$  is strictly convex in P.

Now define the set

$$
A = S_{D^* + 2\delta} \cap E \tag{12.153}
$$

and

$$
B = E - S_{D^* + 2\delta} \cap E \,. \tag{12.154}
$$

Thus  $A \cup B = E$ . These sets are illustrated in Figure 12.6. Then

$$
Q^{n}(B) = \sum_{P \in E \cap \mathcal{P}_{n} : D(P \| Q) > D^{*} + 2\delta} Q^{n}(T(P))
$$
 (12.155)

$$
\leq \sum_{P \in E \cap \mathcal{P}_n : D(P||Q) > D^* + 2\delta} 2^{-nD(P||Q)} \tag{12.156}
$$

$$
\leq \sum_{P \in E \cap \mathcal{P}_n \,:\, D(P \parallel Q) > D^* + 2\delta} 2^{-n(D^* + 2\delta)} \tag{12.157}
$$

$$
\leq (n+1)^{|\mathscr{X}|} 2^{-n(D^*+2\delta)}, \tag{12.158}
$$

Image /page/23/Figure/15 description: A diagram shows a large triangle with vertices labeled E. Inside the triangle, there is a point labeled P\* and another point labeled Q. A line segment divides the triangle into two regions. There are two arcs centered at P\*, with labels S\_D\* + 2δ and S\_D\* + δ. Points A and B are on the line segment near P\*.

Figure 12.6. The conditional limit theorem.

since there are only a polynomial number of types. On the other hand,

$$
Q^{n}(A) \ge Q^{n}(S_{D^{*+}\delta} \cap E) \tag{12.159}
$$

$$
=\sum_{P\in E\cap\mathcal{P}_n\,:\,D(P\|\mathcal{Q})\leq D^*+\delta}Q^n(T(P))\tag{12.160}
$$

$$
\geq \sum_{P \in E \cap \mathcal{P}_n : D(P \| Q) \leq D^* + \delta} \frac{1}{(n+1)^{|\mathcal{X}|}} 2^{-nD(P \| Q)} \tag{12.161}
$$

$$
\geq \frac{1}{(n+1)^{|\mathscr{X}|}} 2^{-n(D^*+\delta)}, \qquad \text{for } n \text{ sufficiently large, } (12.162)
$$

since the sum is greater than one of the terms, and for sufficiently large *n*, there exists at least one type in  $S_{D^*+\delta} \cap E \cap \mathcal{P}_n$ . Then for *n* sufficiently large

$$
\Pr(P_{X^n} \in B | P_{X^n} \in E) = \frac{Q^n(B \cap E)}{Q^n(E)} \tag{12.163}
$$

$$
\leq \frac{Q^n(B)}{Q^n(A)}\tag{12.164}
$$

$$
\leq \frac{(n+1)^{|\mathscr{X}|} 2^{-n(D^*+2\delta)}}{\frac{1}{(n+1)^{|\mathscr{X}|}} 2^{-n(D^*+\delta)}}\tag{12.165}
$$

$$
=(n+1)^{2|\mathscr{X}|}2^{-n\delta}\,,\qquad(12.166)
$$

which goes to 0 as  $n \rightarrow \infty$ . Hence the conditional probability of B goes to 0 as  $n \rightarrow \infty$ , which implies that the conditional probability of A goes to 1.

We now show that all the members of A are close to  $P^*$  in relative entropy. For all members of A,

$$
D(P||Q) \le D^* + 2\delta \tag{12.167}
$$

Hence by the "Pythagorean" theorem (Theorem 12.6.1))

$$
D(P||P^*) + D(P^*||Q) \le D(P||Q) \le D^* + 2\delta , \qquad (12.168)
$$

which in turn implies that

$$
D(P||P^*) \le 2\delta ,\qquad (12.169)
$$

since  $D(P^*||Q) = D^*$ . Thus  $P_x \in A$  implies that  $D(P_x||Q) \le D^* + 2\delta$ , and therefore that  $D(P_x||P^*) \le 2\delta$ . Consequently, since  $Pr{P_{X^n} \in A|P_{X^n}}}$  $E\} \rightarrow 1$ , it follows that

$$
\Pr(D(P_{X^n} \| P^*) \le 2\delta | P_{X^n} \in E) \to 1 \tag{12.170}
$$

as  $n \rightarrow \infty$ .

By Lemma 12.6.1, the fact that the relative entropy is small implies that the  $\mathscr{L}_1$  distance is small, which in turn implies that max, $\epsilon_{\mathscr{F}}$  $|P_{x^n}(a) - P^*(a)|$  is small. Thus  $Pr(|P_{x^n}(a) - P^*(a)| \ge \epsilon | P_{x^n}(E) \to 0$  as  $n \rightarrow \infty$ . Alternatively, this can be written as

$$
Pr(X_1 = a | P_{X^n} \in E) \to P^*(a) \text{ in probability.} \qquad (12.171)
$$

In this theorem, we have only proved that the marginal distribution goes to  $P^*$  as  $n \to \infty$ . Using a similar argument, we can prove a stronger version of this theorem, *i.e.*,

$$
\Pr(X_1 = a_1, X_2 = a_2, \dots, X_m = a_m | P_{X^n} \in E) \to \prod_{i=1}^{m} P^*(a_i) \quad \text{in probability.}
$$
(12.172)

This holds for fixed m as  $n \to \infty$ . The result is not true for  $m = n$ , since there are end effects; given that the type of the sequence is in  $E$ , the last elements of the sequence can be determined from the remaining elements, and the elements are no longer independent. The conditional limit theorem states that the first few elements are asymptotically independent with common distribution P\*.

**Example 12.6.2:** As an example of the conditional limit theorem, let us consider the case when  $n$  fair dice are rolled. Suppose that the sum of the outcomes exceeds 4n. Then by the conditional limit theorem, the probability that the first die shows a number  $a \in \{1, 2, \ldots, 6\}$  is approximately  $P^*(a)$ , where  $P^*(a)$  is the distribution in E that is closest to the uniform distribution, where  $E = \{P : \sum P(a)a \ge 4\}$ . This is the maximum entropy distribution given by

$$
P^*(x) = \frac{2^{\lambda x}}{\sum_{i=1}^6 2^{\lambda i}},
$$
 (12.173)

with  $\lambda$  chosen so that  $\Sigma iP^*(i) = 4$  (see Chapter 11). Here  $P^*$  is the conditional distribution on the first (or any other) die. Apparently the first few dice inspected will behave as if they are independently drawn according to an exponential distribution.

##### 12.7 HYPOTHESIS TESTING

One of the standard problems in statistics is to decide between two alternative explanations for the observed data. For example, in medical testing, one may wish to test whether a new drug is effective or not. Similarly, a sequence of coin tosses may reveal whether the coin is biased or not.

These problems are examples of the general hypothesis testing problem. In the simplest case, we have to decide between two i.i.d. distributions. The general problem can be stated as follows:

**Problem:** Let  $X_1, X_2, \ldots, X_n$  be i.i.d.  $\sim Q(x)$ . We consider two hypotheses:

- $H_1: Q = P_1$ .
- $\cdot H_{\circ}$ :  $Q = P_{\circ}$ .

Consider the general decision function  $g(x_1, x_2, \ldots, x_n)$ , where  $g(x_1, x_2, \ldots, x_n) = 1$  implies that  $H_1$  is accepted and  $g(x_1, x_2, \ldots, x_n) = 2$ implies that  $H_2$  is accepted. Since the function takes on only two values, the test can also be specified by specifying the set A over which  $g(x_1, x_2, \ldots, x_n)$  is 1; the complement of this set is the set where  $g(x_1, x_2, \ldots, x_n)$  has the value 2. We define the two probabilities of error:

$$
\alpha = \Pr(g(X_1, X_2, \dots, X_n) = 2|H_1 \text{ true}) = P_1^n(A^c) \quad (12.174)
$$

and

$$
\beta = \Pr(g(X_1, X_2, \dots, X_n) = 1 | H_2 \text{ true}) = P_2^n(A). \quad (12.175)
$$

In general, we wish to minimize both probabilities, but there is a trade-off. Thus we minimize one of the probabilities of error subject to a constraint on the other probability of error. The best achievable error exponent in the probability of error for this problem is given by Stein's lemma.

We first prove the New Pearson lemma, which derives the form of  $\mathbf{P}_{\text{e}}$  $t_{\text{tot}}$  to the problem test between two hypotheses. We derive the result of the optimum test between two hypotheses. We derive the result for discrete distributions; the same results can be derived for continuous distributions as well.

 $\mathbf{r}$  (Newman-Pearson lemma): Let  $\mathbf{r}$  . . . . . . . . . . . . . . . . . . . **drawn i.e.**  $\alpha_1, \alpha_2, \ldots, \alpha_n$  be drawn i.i.d. according to probability mass function P. Consider the decision problem corresponding to hypotheses  $Q = P_1$  vs.  $Q = P_2$ . For  $T \ge 0$ , define a region

$$
A_n(T) = \left\{ \frac{P_1(x_1, x_2, \dots, x_n)}{P_2(x_1, x_2, \dots, x_n)} > T \right\}.
$$
 (12.176)

Let

$$
\alpha^* = P_1^n(A_n^c(T)), \qquad \beta^* = P_2^n(A_n(T)), \qquad (12.177)
$$

be the corresponding probabilities of error corresponding to decision region  $A_n$ . Let  $B_n$  be any other decision region with associated probabilities of error  $\alpha$  and  $\beta$ . If  $\alpha \le \alpha^*$ , then  $\beta \ge \beta^*$ .

**Proof:** Let  $A = A_n(T)$  be the region defined in (12.176) and let  $B \in \mathcal{X}^n$  be any other acceptance region. Let  $\phi_A$  and  $\phi_B$  be the indicator functions of the decision regions A and B respectively. Then for all  $\mathbf{x}=(x_1, x_2, \ldots, x_n) \in \mathcal{X}^n$ 

$$
[\phi_A(\mathbf{x}) - \phi_B(\mathbf{x})][P_1(\mathbf{x}) - TP_2(\mathbf{x})] \ge 0.
$$
 (12.178)

This can be seen by considering separately the cases  $\mathbf{x} \in A$  and  $\mathbf{x} \not\in A$ .

Multiplying out and integrating this over the entire space, we obtain

$$
0 \le \sum (\phi_A P_1 - T \phi_A P_2 - P_1 \phi_B + T P_2 \phi_B)
$$
 (12.179)

$$
= \sum_{A} (P_1 - TP_2) - \sum_{B} (P_1 - TP_2)
$$
 (12.180)

$$
= (1 - \alpha^*) - T\beta^* - (1 - \alpha) + T\beta \qquad (12.181)
$$

$$
=T(\beta-\beta^*)-(\alpha^*-\alpha). \qquad (12.182)
$$

Since  $T \geq 0$ , we have proved the theorem.  $\Box$ 

The Neyman-Pearson lemma indicates that the optimum test for two hypotheses is of the form

$$
\frac{P_1(X_1, X_2, \dots, X_n)}{P_2(X_1, X_2, \dots, X_n)} > T.
$$
\n(12.183)

This is the likelihood ratio test and the quantity  $\frac{P_1(X_1, X_2, \ldots, X_n)}{P_n(X_1, X_2, \ldots, X_n)}$  is called the likelihood ratio.

For example, in a test between two Gaussian distributions, i.e., between  $f_1 = \mathcal{N}(1, \sigma^2)$  and  $f_2 = \mathcal{N}(-1, \sigma^2)$ , the likelihood ratio becomes

$$
\frac{f_1(X_1, X_2, \dots, X_n)}{f_2(X_1, X_2, \dots, X_n)} = \frac{\prod_{i=1}^n \frac{1}{\sqrt{2\pi\sigma^2}} e^{-\frac{(X_i - 1)^2}{2\sigma^2}}}{\prod_{i=1}^n \frac{1}{\sqrt{2\pi\sigma^2}} e^{-\frac{(X_i + 1)^2}{2\sigma^2}}} \tag{12.184}
$$

$$
=e^{+\frac{2\sum_{i=1}^{n}X_i}{\sigma^2}}
$$
 (12.185)

$$
=e^{+\frac{2n\bar{X}_n}{\sigma^2}}.
$$
 (12.186)

Hence the likelihood ratio test consists of comparing the sample mean

 $\bar{X}_n$  with a threshold. If we want the two probabilities of error to be equal, we should set  $T = 1$ . This is illustrated in Figure 12.7.

In the above theorem, we have shown that the optimum test is a likelihood ratio test. We can rewrite the log-likelihood ratio as

$$
L(X_1, X_2, \dots, X_n) = \log \frac{P_1(X_1, X_2, \dots, X_n)}{P_2(X_1, X_2, \dots, X_n)}
$$
(12.187)

$$
= \sum_{i=1}^{n} \log \frac{P_1(X_i)}{P_2(X_i)}
$$
(12.188)

$$
= \sum_{a \in \mathcal{X}} n P_{X^n}(a) \log \frac{P_1(a)}{P_2(a)}
$$
 (12.189)

$$
= \sum_{a \in \mathcal{X}} n P_{X^n}(a) \log \frac{P_1(a)}{P_2(a)} \frac{P_{X^n}(a)}{P_{X^n}(a)}
$$
(12.190)

$$
=\sum_{a\in\mathscr{X}}nP_{X^n}(a)\log\frac{P_{X^n}(a)}{P_2(a)}-\sum_{a\in\mathscr{X}}nP_{X^n}(a)\log\frac{P_{X^n}(a)}{P_1(a)}
$$
  
(12.191)

$$
= nD(P_{X^n} || P_2) - nD(P_{X^n} || P_1), \qquad (12.192)
$$

the difference between the relative entropy distances of the sample type to each of the two distributions. Hence the likelihood ratio test

$$
\frac{P_1(X_1, X_2, \dots, X_n)}{P_2(X_1, X_2, \dots, X_n)} > T
$$
\n(12.193)

is equivalent to

$$
D(P_{X^n} \| P_2) - D(P_{X^n} \| P_1) > \frac{1}{n} \log T.
$$
 (12.194)

Image /page/28/Figure/13 description: The image displays a graph illustrating two Gaussian distributions. The x-axis is labeled 'x' and ranges from -5 to 5. The y-axis is labeled 'f(x)' and ranges from 0 to 0.4, with tick marks at intervals of 0.05. A solid black curve represents a Gaussian distribution centered around x = -2, with its peak at approximately f(x) = 0.4. A dashed black curve represents another Gaussian distribution centered around x = 2, also with its peak at approximately f(x) = 0.4. The two curves are positioned symmetrically around the y-axis, with the solid curve on the left and the dashed curve on the right. The caption below the graph reads "Figure 12.7. Testing between two Gaussian distributions."