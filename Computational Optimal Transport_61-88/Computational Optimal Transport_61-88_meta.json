{"table_of_contents": [{"title": "4", "heading_level": null, "page_id": 0, "polygon": [[303.1236230110159, 136.146240234375], [321.76513671875, 136.146240234375], [321.76513671875, 160.671875], [303.1236230110159, 160.671875]]}, {"title": "Entropic Regularization of Optimal Transport", "heading_level": null, "page_id": 0, "polygon": [[155.31334149326804, 180.69289099526065], [467.5322265625, 180.69289099526065], [467.5322265625, 196.205078125], [155.31334149326804, 196.205078125]]}, {"title": "4.1 Entropic Regularization", "heading_level": null, "page_id": 0, "polygon": [[104.29253365973072, 558.5734597156398], [263.69775390625, 558.5734597156398], [263.69775390625, 569.68994140625], [104.29253365973072, 569.68994140625]]}, {"title": "4.1. Entropic Regularization 59", "heading_level": null, "page_id": 2, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.9925537109375], [105.04283965728274, 103.9925537109375]]}, {"title": "4.2 Sinkhorn's Algorithm and Its Convergence", "heading_level": null, "page_id": 5, "polygon": [[104.29253365973072, 614.8056872037914], [367.64993880048956, 614.8056872037914], [367.64993880048956, 626.07958984375], [104.29253365973072, 626.07958984375]]}, {"title": "4.2. <PERSON><PERSON><PERSON>'s Algorithm and Its Convergence 65", "heading_level": null, "page_id": 8, "polygon": [[104.8355712890625, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 104.089111328125], [104.8355712890625, 104.089111328125]]}, {"title": "4.2. <PERSON><PERSON><PERSON>'s Algorithm and Its Convergence 69", "heading_level": null, "page_id": 12, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.89599609375], [105.04283965728274, 103.89599609375]]}, {"title": "4.3. <PERSON><PERSON> <PERSON>'s Iterations 73", "heading_level": null, "page_id": 16, "polygon": [[105.04283965728274, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 103.89599609375], [105.04283965728274, 103.89599609375]]}, {"title": "4.3 Speeding Up Sinkhorn's Iterations", "heading_level": null, "page_id": 16, "polygon": [[104.29253365973072, 402.62274881516583], [322.6315789473684, 402.62274881516583], [322.6315789473684, 414.0390625], [104.29253365973072, 414.0390625]]}, {"title": "4.3. <PERSON>ing <PERSON>'s Iterations 75", "heading_level": null, "page_id": 18, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.9925537109375], [105.04283965728274, 103.9925537109375]]}, {"title": "4.4 Stability and Log-Domain Computations", "heading_level": null, "page_id": 20, "polygon": [[104.29253365973072, 125.21042654028436], [357.89596083231334, 125.21042654028436], [357.89596083231334, 136.33935546875], [104.29253365973072, 136.33935546875]]}, {"title": "4.4. Stability and Log-Domain Computations 79", "heading_level": null, "page_id": 22, "polygon": [[105.04283965728274, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 104.089111328125], [105.04283965728274, 104.089111328125]]}, {"title": "4.5 Regularized Approximations of the Optimal Transport Cost", "heading_level": null, "page_id": 23, "polygon": [[104.29253365973072, 474.59999999999997], [459.937576499388, 474.59999999999997], [459.937576499388, 486.26416015625], [104.29253365973072, 486.26416015625]]}, {"title": "4.6 Generalized Sinkhorn", "heading_level": null, "page_id": 25, "polygon": [[104.29253365973072, 553.3251184834123], [250.677490234375, 553.3251184834123], [250.677490234375, 564.6689453125], [104.29253365973072, 564.6689453125]]}, {"title": "4.6. General<PERSON> Sinkhorn 83", "heading_level": null, "page_id": 26, "polygon": [[105.04283965728274, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 103.9925537109375], [105.04283965728274, 103.9925537109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 27], ["SectionHeader", 3], ["Text", 2], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3326, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 46], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 653, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 51], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1306, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 55], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1066, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 55], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 24], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 804, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 43], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 30], ["SectionHeader", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 619], ["Line", 109], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2001, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["Line", 74], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 30], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 750, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 60], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 64], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1954, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 69], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 87], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1271, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 576], ["Line", 48], ["TextInlineMath", 7], ["Equation", 4], ["SectionHeader", 2], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 72], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 49], ["TextInlineMath", 4], ["Equation", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 40], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["Line", 69], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 81], ["Equation", 7], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 78], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 49], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 63], ["Text", 7], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 53], ["Text", 4], ["Equation", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 75], ["Text", 7], ["Equation", 5], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_61-88"}