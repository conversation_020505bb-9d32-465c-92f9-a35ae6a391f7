**<PERSON> · <PERSON> · <PERSON><PERSON> · <PERSON><PERSON><PERSON> · <PERSON> · <PERSON><PERSON> · <PERSON> (Eds.)**

# **Medical Image Computing and Computer Assisted Intervention – MICCAI 2024**

**27th International Conference Marrakesh, Morocco, October 6–10, 2024 Proceedings, Part XII**

Image /page/0/Picture/4 description: The image shows the number 12 in large, light-colored font on a red background. To the right of the number 12, the text "Part XII" is displayed in white font.

Image /page/0/Picture/5 description: A stylized, translucent white letter M is shown against a solid red background. The letter M has a 3D effect, appearing to be made of glass or plastic, with subtle shading and highlights that give it depth. The bottom of the letter casts a soft shadow on the background, further enhancing its three-dimensional appearance.

# MICCAI

Image /page/0/Picture/7 description: The image is a logo for Springer, a publishing house. It features a white outline of a horse's head, resembling a chess knight, on the left side. To the right of the horse's head is the word "Springer" in white, stylized serif font. The background of the logo is a solid dark red color.

Image /page/0/Picture/8 description: A white rounded rectangle with the word MOREMEDIA in gray text is on a dark red background. To the right of the text is a gray circle with a white play button inside it.

# **Lecture Notes in Computer Science 15012**

Founding Editors

<PERSON>manis

## Editorial Board Members

Elisa Bertino, *Purdue University, West Lafayette, IN, USA* Wen Gao, *Peking University, Beijing, China* Bernhard Steffen [,](https://orcid.org/0000-0001-9619-1558) *TU Dortmund University, Dortmund, Germany* Moti Yung [,](https://orcid.org/0000-0003-0848-0873) *Columbia University, New York, NY, USA*

The series Lecture Notes in Computer Science (LNCS), including its subseries Lecture Notes in Artificial Intelligence (LNAI) and Lecture Notes in Bioinformatics (LNBI), has established itself as a medium for the publication of new developments in computer science and information technology research, teaching, and education.

LNCS enjoys close cooperation with the computer science  $R \& D$  community, the series counts many renowned academics among its volume editors and paper authors, and collaborates with prestigious societies. Its mission is to serve this international community by providing an invaluable service, mainly focused on the publication of conference and workshop proceedings and postproceedings. LNCS commenced publication in 1973.

Marius George Linguraru · Qi Dou · Aasa Feragen · Stamatia Giannarou · Ben Glocker · Karim Lekadir · Julia A. Schnabel Editors

# Medical Image Computing and Computer Assisted Intervention – MICCAI 2024

27th International Conference Marrakesh, Morocco, October 6–10, 2024 Proceedings, Part XII

Image /page/3/Picture/3 description: The logo for Springer, a publishing company, features a stylized black and white outline of a horse's head, resembling a chess knight, positioned to the left of the word "Springer" written in black, serif font.

*Editors* Mari[u](https://orcid.org/0000-0001-6175-8665)s George Linguraru<sup>D</sup> Children's National Hospital/George Washington University Washington, DC, USA

Aasa Ferage[n](https://orcid.org/0000-0002-9945-981X)<sup>D</sup> Technical University of Denmark Kgs Lyngby, Denmark

Ben Glocker<sup>D</sup> Imperial College London London, UK

Ju[l](https://orcid.org/0000-0001-6107-3009)ia A. Schnabel<sup>D</sup> Helmholtz Munich, Technical University of Munich and King's College London Munich, Germany

Qi Do[u](https://orcid.org/0000-0002-3416-9950) The Chinese University of Hong Kong Hong Kong, China

Stamatia Giannaro[u](https://orcid.org/0000-0002-8745-1343)<sup>D</sup> Imperial College London London, UK

Karim Lekadir Universitat de Barcelona Barcelona, Spain

ISSN 0302-9743 ISSN 1611-3349 (electronic) Lecture Notes in Computer Science ISBN 978-3-031-72389-6 ISBN 978-3-031-72390-2 (eBook) <https://doi.org/10.1007/978-3-031-72390-2>

© The Editor(s) (if applicable) and The Author(s), under exclusive license to Springer Nature Switzerland AG 2024

This work is subject to copyright. All rights are solely and exclusively licensed by the Publisher, whether the whole or part of the material is concerned, specifically the rights of translation, reprinting, reuse of illustrations, recitation, broadcasting, reproduction on microfilms or in any other physical way, and transmission or information storage and retrieval, electronic adaptation, computer software, or by similar or dissimilar methodology now known or hereafter developed.

The use of general descriptive names, registered names, trademarks, service marks, etc. in this publication does not imply, even in the absence of a specific statement, that such names are exempt from the relevant protective laws and regulations and therefore free for general use.

The publisher, the authors and the editors are safe to assume that the advice and information in this book are believed to be true and accurate at the date of publication. Neither the publisher nor the authors or the editors give a warranty, expressed or implied, with respect to the material contained herein or for any errors or omissions that may have been made. The publisher remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.

This Springer imprint is published by the registered company Springer Nature Switzerland AG The registered company address is: Gewerbestrasse 11, 6330 Cham, Switzerland

If disposing of this product, please recycle the paper.

## **Preface**

We are pleased to present the proceedings for the 27th International Conference on Medical Image Computing and Computer-Assisted Intervention (MICCAI). For the very first time, the MICCAI conference set its stage in Africa. This edition was held in an in-person format at the Palmeraie Conference Centre in Marrakesh, Morocco on October 6–10, 2024. The conference included a series of Satellite Events held on October 6 and October 10, including 41 workshops, 12 tutorials, 47 onsite challenges and 3 online challenges. Co-located with the conference were also the 4th Conference on Clinical Translation on Medical Image Computing and Computer-Assisted Intervention (CLINICCAI) on October 8, and the 1st Open Data Session on October 7.

MICCAI 2024 received the largest number of submissions in the conference history, with a 21% increase compared to 2023. We would like to thank all authors for their efforts in preparing their manuscripts and for supporting theMICCAI conference through their submissions. We received 2879 full submissions of which 2781 were subjected to full review. Ten submissions were incomplete and 88 were desk-rejected as they did not comply with the author submission guidelines. The number of papers accepted for presentation after review was 857, which represents an acceptance rate of about 30%. These papers were published in 12 volumes of Lecture Notes in Computer Science (LNCS) proceedings as follows:

- Part I, LNCS Volume 15001: Clinical Applications: Abdomen, Breast, Cardiac, Dermatological, Fetal, Lung, and Musculoskeletal Imaging
- Part II, LNCS Volume 15002: Neuroimaging and Image Registration
- Part III, LNCS Volume 15003: Cross-modal and Translational Imaging, Human-Centered AI and Health Equity
- Part IV, LNCS Volume 15004: Computational Pathology and Ultrasound
- Part V, LNCS Volume 15005: Computer Aided Diagnosis and Treatment Response and Outcome Prediction
- Part VI, LNCS Volume 15006: Image Guided Intervention, Visualization, Surgical Planning, and Surgical Data Science
- Part VII, LNCS Volume 15007: Image Reconstruction
- Part VIII, LNCS Volume 15008: Image Segmentation I
- Part IX, LNCS Volume 15009: Image Segmentation II
- Part X, LNCS Volume 15010: Machine Learning Generalizability/Explainability/ Fairness/Uncertainty
- Part XI, LNCS Volume 15011: Machine Learning Efficient Learning Strategies I
- Part XII, LNCS Volume 15012: Machine Learning Efficient Learning Strategies II and Foundation Models

The selection of papers for the proceedings was done through a rigorous doubleblind peer-review process. The Microsoft Conference Management Toolkit was used for paper management and the organization of the overall review process. The Toronto Paper Matching System was employed to ensure knowledgeable experts were assigned to

review appropriate papers. Area chairs and reviewers were selected following public calls to the community, and were vetted by the program chairs. The MICCAI 2024 Program Committee consisted of 153 area chairs and over 2050 reviewers, with representation from 49 countries across all major continents. About a third (29.4%) of our area chairs self-identified as women. Each area chair managed the review of about 20 papers and the average workload was of about 5 papers per reviewer. We are immensely grateful to all the area chairs and reviewers for the quality and timeliness of their work.

Based on the double-blinded reviews, area chairs' recommendations, and program chairs' decisions,  $11\%$  (287) papers were provisionally early accepted, 35% (956) papers were provisionally early rejected, and 54% (1533) papers proceeded to the rebuttal stage. Final decisions were made based on all available information, including paper screening, adherence to author submission guidelines, reviews, rebuttals, 2–3 independent meta reviews, communications during the review process, and, in some cases, additional assessment from the program chairs. The Program Committee scrutinized papers flagged during reviews, manuscripts of special interest, potential plagiarism, and appeals from authors.

The 857 accepted papers originated from 43 countries. Two thirds of these papers are from five countries or administrative regions, as identified by the primary author of each paper, i.e., China (327), USA (133), Germany (56), Hong Kong SAR (55), and UK (55). The next five countries in descending order of the number of accepted papers are Korea (34), Canada (26), France (22), Australia (16), and India (15). Thirty-one papers originated from lower- and middle-income countries other than China, i.e., Egypt, India, Morocco, Nepal, Nigeria, Pakistan, Senegal, Sri Lanka, and Vietnam. Six papers have the primary author based in Africa, including South Africa. Three papers originated from Latin America, all from Colombia. We welcome the increase in diversity at MICCAI while we acknowledge that much more needs to be done.

To celebrate the first MICCAI conference in Africa, the "MICCAI for Health Equity" session was introduced, which highlighted new approaches and applications that enhance access to healthcare and improve health outcomes for all. This first edition of "MICCAI for Health Equity" focused on geographical health equity and global health challenges. Hence, it inspired the submission of papers that contribute new methods and applications that are attuned to diverse healthcare settings, in terms of data, infrastructure, resources, and costs, especially to address challenges in limited-resource settings. This session showcased how innovation in Medical Image Computing (MIC) and Computer-Assisted Interventions (CAI) can bridge healthcare gaps and offer affordable and high-quality care to underserved populations worldwide. Consequently, during paper submission, in addition to MIC, CAI and "Clinical Translation of Methodology", a new category of "Health Equity" was available for authors to select. About 72% (620) of the accepted papers identified MIC as their primary area, 10% (81) CAI, 16% (134) MIC+CAI, and 2% (22) other. Clinical Translation of Methodology was selected by 6% (51) of the papers, and Health Equity by 4% (37) papers. Also new this year was a panel discussion on Health Equity, a "Debate on AI" session, an industry sponsor event, and rounding off the main conference with a "From MICCAI to AFRICAI" special session highlighting initiatives and grassroot efforts in Africa to strengthen the ecosystem for AI in medical imaging in the region.

To further our mission of enhancing diversity and inclusion at MICCAI, we offered travel grants to candidates from lower-to-middle-income countries as well as from countries historically underrepresented in the MICCAI community. Support for these grants was offered through the MICCAI Society Travel Grants, African Participation Grants, and RISE-MICCAI grants. We supported the travel of attendees from Africa (including Benin, Egypt, Ghana, Kenya, Morocco, Nigeria, Senegal, South Africa, Tunisia, and Uganda), Asia (including Bangladesh, India, Nepal, Pakistan, Sri Lanka, and Vietnam), and Latin America (including Mexico). We also provided partial support to additional participants through the MICCAI Society Registration Grants. Funding was been made possible by the generous support from the MICCAI Society, donations from individuals in the MICCAI community, and financial support from non-commercial and AFRICAI sponsors, namely GH Labs, the Children's National Hospital, Pierre Fabre, Computer Assisted Medical Interventions Labex, the Multidisciplinary Institute in Artificial Intelligence Grenoble Alpes, the Frugal Biomedical Innovation Program – Western University, The International Society of Radiology, Medtronic, the Pasqual Maragall Foundation, Delft Imaging, the Artificial Intelligence in Medicine Lab – Universitat de Barcelona, and Cadi Ayyad University and the National Center for Scientific and Technical Research – Morocco. US-based students and early career investigators were also funded thanks to the National Institutes of Health (NIH). Specifically, funding for this conference was made possible (in part) by R13EB35922 from the National Institute of Biomedical Imaging and Bioengineering. The views expressed in written conference materials or publications and by speakers and moderators do not necessarily reflect the official policies of the NIH; nor does mention by trade names, commercial practices, or organizations imply endorsement by the U.S. Government. Congratulations to all awardees!

We extend our sincere gratitude to everyone who contributed to the success of MIC-CAI 2024 and the quality of its proceedings. In particular, we would like to express our profound thanks to the MICCAI Submission System Manager, Kitty Wong, whose meticulous support throughout the paper submission, review, program planning, and proceeding preparation process has been invaluable. We are also indebted to Cecilia Judmann for coordinating the organizing teams and activities. Our workshop chairs, Maria A. Zuluaga, Hervé Lombaert, and Nicola Rieke, the African workshop chairs, Udunna Anazodo, Tinashe Mutsvangwa, and Celia Cintas, the tutorial chairs, Tammy Riklin and Ender Konukoglu, the challenge chairs, Shadi Albarqouni, Yunusa Mohammed, and Spyridon Bakas, the African challenge commissioners, Victor Campello, Udunna Anazodo, and Rachid Jennane, and the Open Data chairs, Martijn Starmans and Apostolia Tsirikoglou, worked tirelessly to assemble a strong program for the Satellite Events.

We acknowledge the special contributions of our keynote chairs, Tina Kapur and Olivier Salvado. Although not reflected in the proceedings, the conference greatly benefited from keynote talks from experts in their respective fields: Alexandra Golby (Americas), Michael Bronstein (Europe) and Aisha Walcott (Africa). Our industrial sponsorship chairs, Sandrine Voros, Mohammad Yaqub, Nassir Navab, Natasha Lepore, Kensaku Mori, Smriti Joshi, Mustafa Elattar, Albert Chung, Laura Igual, and Clarisa Sánchez, along with Dekon's Mehmet Eldegez, secured strong sponsorship in innovative ways, for which we are grateful. The MICCAI Student Board led by Naren Akash RJ put together student-run networking and social events including a Ph.D. thesis madness and early

career challenge event to offer spotlight to new graduates for their careers. Similarly, Women in MICCAI president Ruogo Fang and RISE president Islem Rekik further strengthened the quality of our technical program through their focused events. The contribution of the Diversity & Inclusion chairs, Islem Rekik and Jihad Zahir, and the CLINICCAI program chairs, Joël L. Lavanchy, Mariam Aboian, Idriss Ahmedou, Sandrine De Ribaupierre, Bassma Elsabaa, Daniel A. Hashimoto, Abdourahmane Ndong, Nicolas Padoy, Saad Slimani, Juan Verde, and Joe Yeong, was invaluable regarding our ongoing development of the diversity of attendees and program subjects. The local arrangement chairs, Jihad Zahir, Mohammed El Hassouni, Ilyass Ouazzani, and Noussair Lazrak, recruited Moroccan-based students, prepared invitation letters to attendees, and organized the posters for the welcome reception. They also helped coordinate the visits to the local sites in Marrakesh both during the selection of the site and organization of our local activities during the conference. Our Career Development and Students chairs, Antonio Porras and Anees Kazi, facilitated programs for career development. Antonio Porras together with Marius George Linguraru secured funding from the NIH for early career scientist participation in the conference. The communications chairs, Noussair Lazrak and Cecilia Judmann, increased the conference visibility through social media platforms and newsletters. Cecilia Judmann, Deborah Carraro, Veronika Zimmer, and Paloma Fernández Torres were our Executive Associates who provided support to all the committee meetings. The AFRICAI chairs, Marawan Elbatel, Ismaël Koné, Hasnae Zerouaoui, and Jean-Rassaire Fouefack, organized monthly webinars for capacity building in Africa, which contributed to an increase in paper submissions from Africa and raised awareness of MICCAI in Africa. We are grateful to all the organizing committee members for their strong contributions that made the conference successful.

We would like to thank the MICCAI Society Board of Directors and its Chair, Caroline Essert, for their support and feedback, they provided guidance on organizing a successful conference. Behind the scenes, we acknowledge the contributions of the MICCAI Society secretariat personnel, Janette Wallace and Johanne Langford, who kept oversight of logistics and budgets, and Diana Cunningham and Anna Van Vliet for timely conference announcements in the MICCAI Society newsletters. The site organization of the conference in Marrakech, budget financials, fundraising, and the smooth running of events would not have been possible without our Professional Conference Organization team from Dekon Congress and Tourism led by Mehmet Eldegez.

The proceedings of MICCAI 2024 are the result of the incredible and dedicated work of a large and diverse committee. We are especially grateful to all the members of the Program Committee and James Gee (representative program chair for MICCAI 2025) for their diligent work in the reviewer assignments and final paper selection, and to the many reviewers who volunteered and supported the entire process. Congratulations to the outstanding area chairs and reviewers! Lastly, and very importantly, we thank all authors for submitting and presenting their high-quality work, which played a pivotal role in making MICCAI 2024 a success. Congratulations to all!

We hope you enjoyed MICCAI 2024 in Marrakesh, our first conference in Africa. We now look forward to seeing you next year in Daejeon, Republic of Korea, when MICCAI 2025 goes to Asia.

October 2024

October 2024 Marius George Linguraru Qi Dou Aasa Feragen Stamatia (Matina) Giannarou Ben Glocker MICCAI 2024 Program Chairs

> Karim Lekadir Julia Schnabel MICCAI 2024 General Chairs

## **MICCAI 2024 Organization**

## **General Chairs**

| Karim Lekadir     | ICREA, Universitat de Barcelona, Spain                                                     |
|-------------------|--------------------------------------------------------------------------------------------|
| Julia A. Schnabel | Helmholtz Munich and Technical University of Munich, Germany and King's College London, UK |

## **Program Committee Chairs**

| Marius George Linguraru | Children's National Hospital and George<br>Washington University, USA |
|-------------------------|-----------------------------------------------------------------------|
| Qi Dou                  | Chinese University of Hong Kong, China                                |
| Aasa Feragen            | DTU Compute, Technical University of Denmark,<br>Denmark              |
| Stamatia Giannarou      | Imperial College London, UK                                           |
| Ben Glocker             | Imperial College London, UK                                           |

## **Keynote Chairs**

| Tina Kapur      | Brigham and Women's Hospital and Harvard<br>University, USA |
|-----------------|-------------------------------------------------------------|
| Olivier Salvado | CSIRO, Australia                                            |

## **Workshop Chairs**

Maria A Zuluaga
Hervé Lombaert
Nicola Rieke

Maria A Zuluaga EURECOM, France Hervé Lombaert École de technologie supérieure, Canada Nicola Rieke NVIDIA, Germany

## **African Workshop Chairs**

| Udunna Anazodo     | Montreal Neurological Institute, McGill<br>University, Canada and Medical Artificial<br>Intelligence Lab, Lagos, Nigeria |
|--------------------|--------------------------------------------------------------------------------------------------------------------------|
| Tinashe Mutsvangwa | IMT Atlantique, France and University of Cape<br>Town, South Africa                                                      |
| Celia Cintas       | IBM Research Africa, Kenya                                                                                               |

## **Challenges Chairs**

| Shadi Albarqouni | Universität Bonn and Helmholtz Munich,<br>Germany |
|------------------|---------------------------------------------------|
| Yunusa Mohammed  | Gombe State University, Nigeria                   |
| Spyridon Bakas   | Indiana University, USA                           |

## **African Challenges Commissioners**

| Victor Campello | Universitat de Barcelona, Spain           |
|-----------------|-------------------------------------------|
| Udunna Anazodo  | Montreal Neurological Institute, McGill   |
|                 | University, Canada and Medical Artificial |
|                 | Intelligence Lab, Lagos, Nigeria          |
| Rachid Jennane  | Université d'Orléans, France              |

## **Tutorial Chairs**

| Tammy Riklin    | Ben Gurion University, Israel |
|-----------------|-------------------------------|
| Ender Konukoglu | ETH Zürich, Switzerland       |

## **Open Data Chairs**

| <b>Martijn Starmans</b> | Erasmus University Medical Center, Netherlands |
|-------------------------|------------------------------------------------|
| Apostolia Tsirikoglou   | Karolinska Institutet, Sweden                  |

## **Local Chairs**

| Jihad Zahir           | Université Cadi Ayyad, Morocco          |
|-----------------------|-----------------------------------------|
| Ilyass Ouazzani Taybi | Université Cadi Ayyad, Morocco          |
| Mohammed El Hassouni  | Mohammed V University in Rabat, Morocco |
| Noussair Lazrak       | Universitat de Barcelona, Spain         |

## **Diversity and Inclusion Chairs**

| Islem Rekik | Imperial College London, UK    |
|-------------|--------------------------------|
| Jihad Zahir | Université Cadi Ayyad, Morocco |

## **AFRICAI Chairs**

| Marawan Elbatel        | Hong Kong University of Science and<br>Technology, China |
|------------------------|----------------------------------------------------------|
| Hasnae Zerouaoui       | Mohammed VI Polytechnic University, Morocco              |
| Jean-Rassaire Fouefack | MAIA Medical Technologies, France                        |
| Ismaël Koné            | MRC Unit, The Gambia at LSHTM, Gambia                    |

## **Career Development and Student Chairs**

| Antonio Porras | University of Colorado Anschutz Medical<br>Campus, USA |
|----------------|--------------------------------------------------------|
| Anees Kazi     | Harvard Medical School, USA                            |

## **Communication Chairs**

| Noussair Lazrak | Universitat de Barcelona, Spain |
|-----------------|---------------------------------|
| Cecilia Judmann | Universitat de Barcelona, Spain |

## **Sponsorship Chairs**

| Mohammad Yaqub | MBZ University of Artificial Intelligence,<br>Abu Dhabi |
|----------------|---------------------------------------------------------|
| Sandrine Voros | University of Grenoble, France                          |
| Nassir Navab   | Technical University of Munich, Germany                 |

| Natasha Lepore  | University South California, USA                         |
|-----------------|----------------------------------------------------------|
| Kensaku Mori    | Nagoya University, Japan                                 |
| Smriti Joshi    | Universitat de Barcelona, Spain                          |
| Mustafa Elattar | Nile University, Egypt                                   |
| Albert Chung    | Hong Kong University of Science and<br>Technology, China |
| Laura Igual     | Universitat de Barcelona, Spain                          |
| Clarisa Sánchez | University of Amsterdam, Netherlands                     |

## **Executive Associates**

| Cecilia Judmann         | Universitat de Barcelona, Spain         |
|-------------------------|-----------------------------------------|
| Paloma Fernández Torres | Universitat de Barcelona, Spain         |
| Veronika Zimmer         | Technical University of Munich, Germany |
| Deborah Carraro         | Technical University of Munich, Germany |

## **Women in MICCAI President**

| Ruogu Fang                         | University of Florida, USA   |
|------------------------------------|------------------------------|
| <b>RISE Committee President</b>    |                              |
| Islem Rekik                        | Imperial College, London, UK |
| <b>Submission Platform Manager</b> |                              |
| Kitty Wong                         | The MICCAI Society, Canada   |

## **Program Committee**

| Ehsan Adeli            | Stanford University, USA            |
|------------------------|-------------------------------------|
| Pablo Arbelaez         | Universidad de los Andes, Colombia  |
| Angelica Aviles-Rivero | University of Cambridge, UK         |
| Ulas Bagci             | Northwestern University, USA        |
| Wenjia Bai             | Imperial College London, UK         |
| Yaël Balbastre         | Massachusetts General Hospital, USA |
| Sophia Bano            | University College London, UK       |

Neslihan Bayranoglu
Ryoma Bise
Katharina Breininger

Weidong Cai
Gustavo Carneiro
Chen Chen
Cheng Chen
Geng Chen
Zhen Chen

Toby Collins **IRCAD**, France Olivier Colliot CNRS, France Niharika D'Souza IBM Research, USA Nicha Dvornek Yale University, USA

Yi Hong
Benjamin Hou
Baoru Huang
Yuankai Huo

Neslihan Bayramoglu University of Oulu, Finland Ryoma Bise Kyushu University, Japan Katharina Breininger Friedrich-Alexander-Universität Erlangen-Nürnberg, Germany Weidong Cai University of Sydney, Australia Gustavo Carneiro University of Surrey, UK Chen Chen University of Sheffield, UK Cheng Chen Chinese University of Hong Kong, China Geng Chen Northwestern Polytechnical University, China Zhen Chen Centre for Artificial Intelligence and Robotics, Hong Kong Institute of Science & Innovation, Chinese, Academy of Sciences, China Li Cheng University of Alberta, Canada Aladine Chetouani Université d'Orléans, France Zhiming Cui ShanghaiTech University, China Adrian Dalca Massachusetts Institute of Technology, USA Mostafa El Habib Daho University of Western Brittany, France Sandy Engelhardt Heidelberg University Hospital, Germany Pascal Fallavollita University of Ottawa, Canada Deng-Ping Fan Nankai University, China Ruogu Fang University of Florida, USA Moti Freiman Technion - Israel Institute of Technology, Israel Adrian Galdran Universitat Pompeu Fabra, Spain Zhifan Gao Sun Yat-sen University, China Zongyuan Ge Monash University, Australia Syed Zulqarnain Gilani Edith Cowan University, Australia Yun Gu Imperial College London, UK Houssem-Eddine Gueziri TÉLUQ University, Canada Prashnna Gyawali West Virginia University, USA Ilker Hacihaliloglu University of British Columbia, Canada Hu Han Institute of Computing Technology, Chinese Academy of Sciences, China Jaesung Hong Daegu Gyeongbuk Institute of Science and Technology, Korea Yi Hong Shanghai Jiao Tong University, China Benjamin Hou Imperial College London, UK Baoru Huang Imperial College London, UK Yuankai Huo Vanderbilt University, USA

Won-Ki Jeong Korea University, Korea Dakai Jin Alibaba USA, USA Davood Karimi Harvard University, USA Jin Tae Kwak Korea University, Korea Yuexiang Li Tencent, China Xiaofeng Liu Yale University, USA

Arrrate Muñoz-Barrutia
Saad Nadeem

Jana Hutter King's College London, UK Mobarakol Islam University College London, UK Amir Jamaludin University of Oxford, UK Yueming Jin National University of Singapore, Singapore Anand Joshi University of Southern California, USA Leo Joskowicz The Hebrew University of Jerusalem, Israel Samuel Kadoury Polytechnique Montréal, Canada Bernhard Kainz Imperial College London, UK and FAU Erlangen-Nürnberg, Germany Siva Teja Kakileti Niramai Health Analytix, India Tina Kapur Brigham and Women's Hospital, USA Anees Kazi Harvard Medical School, USA Marta Kersten-Oertel Concordia University, Canada Nadieh Khalili RadboudUMC, Netherlands Jinman Kim University of Sydney, Australia Seong Tae Kim Kyung Hee University, Korea Gang Li **University of North Carolina at Chapel Hill, USA** Hongwei Li Harvard Medical School, USA Lei Li University of Southampton, UK Xiang Li Massachusetts General Hospital and Harvard Medical School, USA Xiaomeng Li Hong Kong University of Science and Technology, China Xiaoxiao Li University of British Columbia, Canada Zeju Li Imperial College London, UK Jianming Liang Arizona State University, USA Daochang Liu University of Sydney, Australia Jianfei Liu National Institutes of Health Clinical Center, USA Ismini Lourentzou University of Illinois Urbana - Champaign, USA Gongning Luo **Harbin Institute of Technology, China** Jie Luo Harvard Medical School, USA Dwarikanath Mahapatra **Inception Institute of Artificial Intelligence**, United Arab Emirates Anne Martel Sunnybrook Research Institute, Canada Arrate Muñoz-Barrutia Universidad Carlos III de Madrid, Spain Saad Nadeem Memorial Sloan Kettering Cancer Center, USA

Dong Nie
Jack Noble
Masahiro Oda
Yoshito Otake
Sanghyun Park

Magdalini Paschali
Prateek Prasanna
Chen Qin
Wu Qiu

Hongming Shan Fudan University, China Sahar Soussa Nile University, Egypt Tanveer Syeda-Mahmood IBM Research, USA

Jeya Maria Jose Valanarasu Stanford University, USA Donglai Wei Boston College, USA

Shandong Wu
Ye Wu

Yiming Xiao
Yutong Xie
Xiaohan Xing

Dong Nie University of North Carolina at Chapel Hill, USA Jack Noble Vanderbilt University, USA Masahiro Oda Nagoya University, Japan Yoshito Otake Nara Institute of Science and Technology, Japan Sanghyun Park Daegu Gyeongbuk Institute of Science and Technology, Korea Magdalini Paschali Stanford University, USA Prateek Prasanna Stony Brook University, USA Chen Oin Imperial College London, UK Wu Qiu Huazhong University of Science and Technology, China Hongliang Ren Chinese University of Hong Kong, China Hassan Rivaz Concordia University, Canada Yang Song University of New South Wales, Australia Aristeidis Sotiras Washington University in St. Louis, USA Rachel Sparks King's College London, UK Jeremias Sulam Johns Hopkins University, USA Aurelle Tchagna Kouanou College of Technology - University of Buea, Cameroon Mathias Unberath Johns Hopkins University, USA Erdem Varol New York University, USA Archana Venkataraman Johns Hopkins University, USA Satish Viswanath Case Western Reserve University, USA Christian Wachinger Technical University of Munich, Germany Qian Wang ShanghaiTech University, China Yan Wang East China Normal University, China Matthias Wilms University of Calgary, Canada Jelmer Wolterink University of Twente, Netherlands Ken C. L. Wong **IBM Research – Almaden Research Center, USA** Jonghye Woo Massachusetts General Hospital/Harvard Medical School, USA Shandong Wu University of Pittsburgh, USA Ye Wu Nanjing University of Science and Technology, China Yiming Xiao Concordia University, Canada Yutong Xie University of Adelaide, Australia Xiaohan Xing Stanford University, USA

Ziyue Xu NVIDIA, USA Menglong Ye Moon Surgical, USA Chenyu You Yale University, USA Can Zhao NVIDIA, USA

Yuyin Zhou
Zongwei Zhou
Lei Zhu

Yan Xu Beihang University, China Yuan Xue Ohio State University, USA Ke Yan Alibaba DAMO Academy, China Guang Yang **Imperial College London**, UK Jiancheng Yang Swiss Federal Institute of Technology Lausanne, Switzerland Inas Yassine Cairo University, Egypt Chuyang Ye Beijing Institute of Technology, China Zhaozheng Yin Stony Brook University, USA Lequan Yu University of Hong Kong, China Fatemeh Zabihollahy University of Toronto, Canada Fan Zhang University of Electronic Science and Technology of China, China Jianpeng Zhang Alibaba DAMO Academy, China Jinwei Zhang Johns Hopkins University, USA Jiong Zhang Cixi Institute of Biomedical Engineering, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, China Lichi Zhang Shanghai Jiao Tong University, China Ling Zhang Alibaba USA Inc., USA Miaomiao Zhang University of Virginia, USA Shu Zhang Northwestern Polytechnical University, China Ya Zhang Shanghai Jiao Tong University, China Qingyu Zhao Weill Cornell Medicine, USA Rongchang Zhao Central South University, China Hao Zheng University of Notre Dame, USA Yefeng Zheng Siemens Corporate Research, USA Luping Zhou University of Sydney, Australia S. Kevin Zhou University of Science and Technology of China, China Tao Zhou Nanjing University of Science and Technology, China Yuyin Zhou UC Santa Cruz, USA Zongwei Zhou Johns Hopkins University, USA Lei Zhu Hong Kong University of Science and Technology (Guangzhou), China

## **Reviewers**

Momen Abayazid Syed Farhan Abbas Zeinab Abboud Maryam Abedi John Abel Kumar Abhishek Shahira Abousamra Mazdak Abulnaga Burak Acar Oscar Acosta Harshit Agrawal Vasundhara Agrawal Shadab Ahamed Seyed-Ahmad Ahmadi Badiaa Ait Ahmed Naren Akash R. J. Alireza Akhondi-asl Wafa Al Ghallabi Farshid Alambeigi Khaoula Alaoui Belghiti Julia Alekseenko Daniel Alexander Hassan Alhajj Kumail Alhamoud Omar Al-Kadi Ahmed Alksas Mohammed Al-masni Mohammad Alsharid Khaled Al-Thelaya Andre Altmann Pablo Alvarez Charlems Alvarez-Jimenez Lidia Al-Zogbi Kimberly Amador Amine Amyar Sion An Xing An Naga Surya Sandeep Angara David Anglada-Rotger André Anjos Sameer Antani Michel Antunes

Teresa Araújo Guilherme Aresta Ignacio Arganda-Carreras Siavash Arjomand Bigdeli Mohammad Ali Armin Josep Arnal Ida Arvidsson Marius Arvinte Muhammad Asad Yuta Asano Ameneh Asgari-Targhi John Ashburner Md Ashikuzzaman Benjamin Aubert Marc Aubreville Chloé Audigier Florent Autrusseau Akash Awasthi Suyash Awate Ishak Ayad Dogu Baran Aydogan Nicolás Ayobi Reza Azad Mohammad Farid Azampour Qinle Ba Meritxell Bach Cuadra Joseph Bae Seung Baek Abdolmahdi Bagheri Fatemeh Bagheri Cagla Bahadir Jieyun Bai Jun Bai Long Bai Pradeep Bajracharya Hossam Magdy Balaha Shafa Balaram Abhirup Banerjee Soumyanil Banerjee Sreya Banerjee Chandan Ganesh Bangalore Yogananda

Jordan Bannister Jaume Banus Shunxing Bao Omri Bar Nagore Barrena Joao Barreto Lalith Baru Hritam Basak Berke Basaran Tamer Basha Lennart Bastian Soumen Basu George Batchkala Deepti R. Bathula Michael Baumgartner John Baxter Nourhan Bayasi Siming Bayer Roza Bayrak Finn Behrendt Sean Benson Guy Ben-Yosef Sutanu Bera Cosmin Bercea Jacqueline Bereska Jorge Bernal Alaa Bessadok Sai Nethra Betgeri Riddhish Bhalodia Nithya Bhasker Debanjali Bhattacharya Indrani Bhattacharya Moinak Bhattacharya Binod Bhattarai Lei Bi Ning Bi Qi Bi Gui-Bin Bian Zhangxing Bian Ricardo Bigolin Lanfredi Benjamin Billot Kyriaki-Margarita Bintsi Manuel Birlo

George Biros Koushik Biswas Stefano Blumberg Sebastian Bodenstedt Lea Bogensperger Patrick Bolan Federico Bolelli Fabian Bongratz Gianpaolo Bontempo Bhushan Borotikar Pauline Bourigault Arnaud Boutillon Abdelbasset Brahim Kit Bransby Biagio Brattoli Clara Brémond Martin Stéphanie Bricq Christopher Bridge Esther Bron Rupert Brooks Tom Brosch Pierangela Bruno Michal Brzus Nhat-Tan Bui Phuoc-Nguyen Bui Ninon Burgos James Burke Mark Butala Joshua Butke Michal Byra Mariano Cabezas Congbo Cai Linghan Cai Qin Cai Qingling Cai Tongan Cai Zongyou Cai Gabriele Campanella Victor Campello Liane Canas Dongliang Cao Hu Cao Jianguo Cao Kangyang Cao Maosong Cao

Weiguo Cao Xu Cao Yankun Cao Zheniie Cao Owen Carmichael Francisco Carrillo-Perez Jacob Carse Adrià Casamitjana Alejandra Castelblanco Luca Cerny Oliveira Stefano Cerri Lorenzo Cerrone Matthieu Chabanas Yaqiong Chai Krishna Chaitanya Satrajit Chakrabarty Souradeep Chakraborty Arunava Chakravarty Emily Chan S.-H. Gary Chan Yi Hao Chan Shekhar Chandra Soumyadeep Chandra Ming-Ching Chang Qi Chang Yao-Jen Chang Hanqing Chao Sudhanya Chatterjee Muhammad Faizyab Ali Chaudhary Shivesh Chaudhary Joël Chavas Haoxuan Che Yiming Che Alvin Chen Antong Chen Bingzhi Chen Boqi Chen Dongdong Chen Guannan Chen Haomin Chen Haoyuan Chen Jiahe Chen Jiazhou Chen Jie Chen

Jintai Chen Jun Chen Li Chen Li Chen Lixuan Chen Liyun Chen Pingjun Chen Pingyi Chen Qi Chen Qiang Chen Sihao Chen Siyuan Chen Tong Chen Wanwen Chen Wenting Chen Xi Chen Xiang Chen Xiang Chen Xin Chen Xueli Chen Yang Chen Yen-Wei Chen Yixin Chen Yixiong Chen Yong-Sheng Chen Yuanyuan Chen Yufei Chen Zhennong Chen Zhihao Chen Zhineng Chen Zhuangzhuang Chen Zihao Chen Zijiao Chen Ziyang Chen Haojie Cheng Jiale Cheng Jian Cheng Jianhong Cheng Jun Cheng Junlong Cheng Ziming Cheng Vsevolod Chernyshev Mark Chiew Bhaskara Rao Chintada Eleni Chiou

Jungchan Cho Seungryong Cho Sue Min Cho Jang-Hwan Choi Min-Kook Choi Sang-Il Choi Yu-Cheng Chou Anders Christensen Argyrios Christodoulidis Yuetan Chu Kai-Cheng Chuang Il Yong Chun Hyungjin Chung Yunsung Chung Ozkan Cigdem Michaël Clément Dana Cobzas Julien Cohen-Adad Jules Collenne Marco Colussi Runmin Cong Laura Connolly William Consagra Pierre-Henri Conze Tim Cootes Valentina Corbetta Teresa Correia Ines Alejandro Cruz Guerrero Angel Cruz-Roa Beilei Cui Hui Cui Lei Cui Zhuo-Xu Cui Tolga Cukur Ariel Curiale Kathleen Curran Tobias Czempiel Rema Daher Haocheng Dai Sarada Dakua Tingting Dan Kang Dang Meghal Dani Bilel Daoud

Eleonora D'Arnese Abhijit Das Ankit Das Ananyananda Dasari Laura Daza Francesca De Benetti Thomas De Kerf Sandrine de Ribaupierre Lisa Anita De Santi Etienne Decenciere Nikoo Dehghani Charles Delahunt Herve Delingette Kubilay Can Demir Ruining Deng Wenlong Deng Yu Deng Zhiwei Deng Zhongying Deng Baudouin Denis De Senneville Elena Denisova Cem Deniz Stefan Denner Felix Denzinger Hrishikesh Deshpande Blake Dewey Rohan Dhamdhere Thameur Dhieb Mariachiara Di Cosmo Maxime Di Folco Avtantil Dimitriadis Hao Ding Jun-En Ding Kexin Ding Li Ding Xinpeng Ding Ying Ding Zhipeng Ding Nicola Dinsdale Kerol Djoumessi Jose Dolz Mischa Dombrowski Di Dong Haoyu Dong

Hexin Dong Hongdao Dong Liang Dong Siyuan Dong Zijian Dong Reuben Dorent Gianfranco Doretto Sven Dorkenwald Haoran Dou Mitchell Doughty Jason Dowling Guodong Du Jing Du Shiyi Du Siyi Du Tianming Du Wenchao Du Ye Du Yuexi Du Bin Duan Peiyu Duan Tiehang Duan Shikha Dubey Lisa Duff Benoit Dufumier James Duncan Dmitry V. Dylov Oleh Dzyubachyk Roy Eagleson Hannah Eichhorn Gudmundur Einarsson Favour Ekong Sidaty El Hadramy Mohammed El Hassouni Omar El Nahhas Ahmed Elazab Marawan Elbatel Shireen Elhabian David Ellis Randy Ellis Mohamed Elsharkawy Taha Emre Justin Engelmann Ertunc Erdil Lauren Erdman

Marius Erdt Floris Ernst Lorena Escudero Sanchez Nazila Esmaeili Alessio Fagioli Rachid Fahmi Kianoush Falahkheirkhah Di Fan Jiansong Fan Lei Fan Xiaoya Fan Yubo Fan Huihui Fang Jiansheng Fang Wei Fang Xi Fang Yu Fang Yugi Fang Zhenghan Fang Zhengqing Fang Manel Farhat Azade Farshad Botond Fazekas Paula Feldman Lina Felsner Chun-Mei Feng Mengling Feng Ruibin Feng Zishun Feng Zunlei Feng Martin Fergie Catalin Fetita Thomas Fevens Lucas Fidon Lukas Fischer Peter Fischer Jean-Rassaire Fouefack Loraine Franke Magda Friedjungová Paul Friedrich Yannik Frisch Sarah Frisken Guanghui Fu Jia Fu Jingru Fu

Xueyang Fu Ying Fu Yunguan Fu Moritz Fuchs Michael Gadermayr Mélanie Gaillochet Rohan Gala Francesca Galassi Christoforos Galazis John Galeotti Yu Gan Alireza Ganjdanesh Melanie Ganz Bingchen Gao Chang Gao Linlin Gao Shangqi Gao Yibo Gao Yifan Gao Yuan Gao Yue Gao Yurui Gao Zeyu Gao Zhongpai Gao Utpal Garain Alvaro Garcia-Tornel Garcia-Camba Gautam Gare Lidia Garrucho Moras Aimilia Gastounioti Romane Gauriau Eloy Geenjaar Martin Genet Bogdan Georgescu Beerend Gerats Elodie Germani Sapir Gershov Salah Ghamizi Negin Ghamsarian Ghazal Ghazaei Behnaz Gheflati Florin Ghesu Sayan Ghosal Shantanu Ghosh Yosr Ghozzi

Diana Giraldo Rémi Giraud Wesley Gohn Jacob Goldberger Negar Golestani Alvaro Gomariz Catalina Gomez Santiago Gómez Hernández Estibaliz Gómez-de-Mariscal Haifan Gong Xun Gong Ricardo Gonzales Camila Gonzalez German Gonzalez Alvaro Gonzalez-Jimenez Marlies Goorden Sharath Gopal Vivek Gopalakrishnan Karthik Gopinath Vandan Gorade Pietro Gori Michael Götz Maged Goubran Sobhan Goudarzi Darshana Govind Camille Graëff Robert Graf Mark Graham Alejandro Granados Thomas Greer Johannes Gregori Thomas Grenier Irina Grigorescu Daniel Grzech Michal Grzeszczyk Ang Nan Gu Feng Gu Jin Gu Lin Gu Pengfei Gu Qiangqiang Gu Ran Gu Shixuan Gu

Xianfeng Gu Yi Gu Yiwen Gu Zaiwang Gu Niklas Gunnarsson Dazhou Guo Erjian Guo Hengtao Guo Jun Guo Rui Guo Wenzhangzhi Guo Xiaoqing Guo Xueqi Guo Cota Navin Gupta Gagan Gupta Ishaan Gupta Sung Min Ha Atlas Haddadi Avval Stathis Hadjidemetriou Ibrahim Hadzic Ida Häggström Fatemeh Haghighi Ibrahim Ethem Hamamci Ghassan Hamarneh Chendi Han Hua Han Jinyoung Han Kangfu Han Luyi Han Seungjae Han Tao Han Tianyu Han Wenchao Han Xiangmin Han Lasse Hansen Degan Hao Huaying Hao Jinkui Hao Luoying Hao Xiaoke Hao Nazim Haouchine Shota Harada Michael Hardisty Shahar Harel Jeffry Hartanto

Katherine Hartmann Md Kamrul Hasan Michel Hayoz Jianzhong He Shenghua He Xiaoxiao He Xinwei He Yuting He Zhenqi He Zhibin He Moein Heidari Mattias Heinrich Alexander Hermans Monica Hernandez Kilian Hett Daniel Hieber Christian Hinge Rebecca Hisey David Ho Edmond S. L. Ho John Hoffman Felix Holm Gregory Holste Qingqi Hong Yoonmi Hong Tonmoy Hossain Golriz Hosseinimanesh Peyman Hosseinzadeh Kassani Mohammad Reza Hosseinzadeh Taher Junlin Hou Franko Hrzic William Hsu Chuanfei Hu Dan Hu Dianlin Hu Renjiu Hu Rongyao Hu Shishuai Hu Xiaoling Hu Xinrong Hu Xintao Hu Yang Hu Yipeng Hu

Bin Huang Chaoqin Huang Chun-Rong Huang Jiahao Huang Jiashuang Huang Junzhou Huang Kai Huang Kuan Huang Pan Huang Qinwen Huang Ruobing Huang Shijie Huang Shuo Huang Weitian Huang Wenhui Huang Wenqi Huang Yaping Huang Ying Huang Yongsong Huang Yuhao Huang Yuliang Huang Yunzhi Huang Zengan Huang Zhe Huang Zhenxing Huang Ziyi Huang Arnaud Huaulmé Edward Hui Alex Hung Jiayu Huo Seong Jae Hwang Ilknur Icke Azeez Idris Ayodeji Ijishakin Tales Imbiriba Abdullah Al Zubaer Imran Muhammad Imran Shubham Innani Asim Iqbal Carlos Isla Kh Tohidul Islam Md Saiful Islam Lukman Ismaila Koichi Ito Krithika Iyer

Cristian Izquierdo Mohamed Yaseen Jabarulla Srikrishna Jaganathan Paul Jäger Mohit Jain Amoon Jamzad Ananya Jana Pierre Jannin Uditha Jarayathne Ronnachai Jaroensri Syed Ashar Javed Yasith Jayawardana Alissa Jell Alexander Jenke Mark Jenkinson Rachid Jennane Duhee Jeon Ji-Hoon Jeong Anna Jezierska Debesh Jha Bing Ji Ge-Peng Ji Yuanfeng Ji Zexuan Ji Zhanghexuan Ji Dengqiang Jia Haozhe Jia Xi Jia Xiaoyan Jia Bailiang Jian Baichuan Jiang Caiwen Jiang Hao Jiang Hongchao Jiang Hongxu Jiang Meirui Jiang Nina Jiang Tingting Jiang Xi Jiang Xiajun Jiang Xiaoyi Jiang Yuncheng Jiang Zekun Jiang Zhifan Jiang

Jing Jiao Yining Jiao Ana Jimenez-Pastor Amelia Jiménez-Sánchez Ge Jin Haibo Jin Qunchao Jin Amal Jlassi Charles Jones Sarang Joshi Shantanu Joshi Smriti Joshi Lie Ju Yohan Jun Wonsik Jung Manjunath K N Ali Kafaei Zad Tehrani Joshua Kaggie Niveditha Kalavakonda Kaveri Kale John Kalkhof Sreeram Kamabattula Konstantinos Kamnitsas Sharif Amit Kamran Junghwa Kang Ming Kang Mingon Kang Qingbo Kang Dmitrii Kaplun Tamás Karácsony Mokshagna Sai Teja Karanam Neerav Karani Turkay Kart Satyananda Kashyap Tushar Kataria Eytan Kats Alexander Katzmann Mohammad Mahdi Kazemi Esfeh Amirhossein Kazerouni Hamza Kebiri Youngwook Kee Matthias Keicher Elif Keles

Erwan Kerrien Krishna Nand Keshava Murthy Dimitri Kessler Firas Khader Khaled Khairy Saif Khalid Abbas Khan Izhar Ahmed Khan Muhammad Irfan Khan Sheheryar Khan Bidur Khanal Pulkit Khandelwal Ron Kikinis Benjamin Killeen Chaehyeon Kim Daeseung Kim Hosung Kim Hyeongsub Kim Hyun-Woo Kim Jinhee Kim Jinman Kim Mansu Kim Minkyung Kim Minwoo Kim Sangwook Kim Seongjun Kim Won Hwa Kim Young-Min Kim Yunsoo Kim Andrew King Atilla Kiraly Gabriel Kiss Andreas Kist Yoshiro Kitamura Tobias Klinder Kazuma Kobayashi Lisa Koch Valentin Koch Florian Kofler Kiran Kokilepersaud Iris Kolenbrander Anudeep Konda Satoshi Kondo Ismael Koné

Fanwei Kong Jun Kong Ender Konukoglu Aishik Konwer Nicholas Konz Thijs Kooi Ivica Kopriva Yilmaz Korkmaz Kivanc Kose Axel Krieger Anithapriya Krishnan Florian Kromp Frithjof Kruggel Elizabeth Krupinski Hulin Kuang Thomas Kuestner Amar Kumar Kuldeep Kumar Nilesh Kumar Nishant Kumar Sachin Kumar Sayantan Kumar Kassymzhomart Kunanbayev Manuela Kunz Holger Kunze Chih-En Kuo Yusuke Kurose Jan Kybic Aymen Laadhari Dmitrii Lachinov Bolin Lai Alain Lalande Bennett Landman Daniel Lang Frank Langbein Georg Langs Qicheng Lao Othmane Laousy Axel Largent Mai Chan Lau Shlomi Laufer Max-Heinrich Laves Trung-Nghia Le William Le

Loic Le Folgoc Christian Ledig Eung-Joo Lee Hyekyoung Lee Jangho Lee Jong-Min Lee Moon Hwan Lee Sing Chun Lee Soochahn Lee Won Hee Lee Woonghee Lee Étienne Léger Antoine Legouhy Baiying Lei Long Lei Pengcheng Lei Wen Hui Lei Dimitrios Lenis Bo Li Caizi Li Chao Li Chaoyi Li Chen Li Chen Li Cheng Li Chengyin Li Dawei Li Fangda Li Guang Li Han Li Hansheng Li Hao Li Haofeng Li Haoran Li Honglin Li Hongming Li Huiqi Li Jiacheng Li Jialu Li Jiawen Li Jiu Qiang Li Jun Li Jupeng Li Kang Li Lei Li

Lin Li Ming Li Qi Li Qiyue Li Rui Li Shangxuan Li Shulong Li Siqi Li Tiancheng Li Wen Li Wengiang Li Xiangyu Li Xiao-Xin Li Xiaoxu Li Xinyue Li Xuelu Li Xueshen Li Yamin Li Ye Li Yicong Li Yuemeng Li Yuheng Li Zhen Li Zheng Li Zhiyuan Li Zhjin Li Zhuhui Li Zi Li Zihan Li Sheng Lian Ke Liang Libin Liang Yixiong Liang Ziyun Liang Hongen Liao Wei Liao Zehui Liao Roxane Licandro Andrzej Liebert Gilbert Lim Fangzhou Lin Hongxiang Lin Huei-Yung Lin Jianxin Lin Li Lin

Manxi Lin Mingquan Lin Tiancheng Lin Xian Lin Yi Lin Yiqun Lin Claudia Lindner Simone Lionetti Geert Litjens Baodi Liu Chang Liu Chao Liu Chen Liu Chenyu Liu Chuanbin Liu Dichao Liu Dongnan Liu Fenglin Liu Han Liu Haozhe Liu Hongzhi Liu Huafeng Liu Jiameng Liu Jianan Liu Jiang Liu Jiaqing Liu Jiawei Liu Jia-Wei Liu Jie Liu Jinduo Liu Jing Liu Jingxin Liu Jinhua Liu Jun Liu Jundong Liu Kechun Liu Meng Liu Mianxin Liu Mingyuan Liu Peirong Liu Quan Liu Shuting Liu Sidong Liu Tiange Liu Wei Liu

Xiabi Liu Xianglong Liu Xiaoyu Liu Xinyang Liu Xinyu Liu Xiuwen Liu Yan Liu Yang Liu Yangying Liu Yi Liu Yihao Liu Yikang Liu Yilin Liu Yilong Liu Yiqiao Liu Yue Liu Yuhang Liu Zelong Liu Zhenhong Liu Zhenyu Liu Zhi Liu Zhiyuan Liu Francesca Lizzi Andrea Loddo Nicolas Loménie Yonghao Long Daniel Lopes Miguel López-Pérez Ange Lou Jingjiao Lou Meng Lou Wei Lou Yiwei Lou Charles Lu Chun-Shien Lu Guangming Lu Hongtao Lu Jiaxuan Lu Jingpei Lu Xing Lu Zhengda Lu Zhongkang Lu Xiao Luan Gaoxiang Luo Guibo Luo

Luyang Luo Ma Luo Mingyuan Luo Naisong Luo Xiangde Luo Xiaoling Luo Xinzhe Luo Xuexiong Luo Yong Luo Zhiming Luo Jinxin Lv Fei Lyu Ilwoo Lyu Junyan Lyu Mengye Lyu Andy J. Ma Benteng Ma Dongao Ma Hehuan Ma Jun Ma Junbo Ma Qiang Ma Wenao Ma Xinke Ma Zhuoqi Ma Pedro Macias Gordaliza Derek Magee Dwarikanath Mahapatra S. Sara Mahdavi Huayu Mai Andreas Maier Jennifer Maier Klaus H. Maier-Hein Lena Maier-Hein Mary Margot Maleckar Norberto Malpica Michail Mamalakis Zhehua Mao Jan Margeta Brett Marinelli Zdravko Marinov Viktoria Markova Pablo Márquez Neila Carsten Marr Robert Marti

Nicolas Martin Najmeh Mashhadi Aidana Massalimova Nuno Matela Tejas Sudharshan Mathai Sasan Matinfar Christos Matsoukas Petr Matula Lev Matveev Dimitrios Mavroeidis Perla Mayo Jean-Paul Mazellier Evangelos Mazomenos Amarachi Mbakwe Ashery Mbilinyi Adam McCarthy Jamie McClelland Julian McGinnis Atif Mehmood Deval Mehta Xueyan Mei Felix Meissen Felix Meister Alaa Melek Qianhui Men Mingyuan Meng Qingjie Meng Runqi Meng Yanda Meng Martin Menten Talha Meraj Dorit Merhof Odyssée Merveille Adrien Meyer Juzheng Miao Leo Milecki Fausto Milletari Zhe Min Duy Minh Ho Nguyen Carlos Minutti Golrokh Mirzaei Nahal Mirzaie Deepak Mishra Rashika Mishra Suraj Mishra

Gabriel Mistelbauer Debasis Mitra Pooya Mobadersany Sara Moccia Marc Modat Sovesh Mohapatra Omid Mohareri Ramtin Mojtahedi Tony C. W. Mok Rafael Molina Sara Monii Azad Javier Montoya Pedro Morais José Morano Catarina Moreira Lia Morra Ana Mota Lei Mou Maximilian Mueller Pritam Mukherjee Anirban Mukhopadhyay Johanna Müller Philip Müller Aditya Murali Ana Murillo Balamurali Murugesan Gowtham Krishnan Murugesan Yasmine Mustafa Muhammad Muzammel Werner Nahm Noor Nakhaei Sahil Nalawade Jakub Nalepa Khashayar Namdar Yang Nan Nandakishor Nandakishor Vishwesh Nath Rodrigo Nava Sergio Naval Marimont Ahmed Nebli Amin Nejatbakhsh Favour Nerrise Hang Nguyen Tan Nguyen

Trong-Thuan Nguyen Trung Thanh Nguyen Truong Nguyen Viet Dung Nguyen Dong Ni Haomiao Ni Jinjie Ni Brennan Nichyporuk Joshua Niemeijer Markus Nilsson Lipeng Ning Hareem Nisar Kazuya Nishimura Chuang Niu Tianye Niu Jorge Novo Julian Nubert Chinedu Nwoye Gilberto Ochoa-Ruiz Hirohisa Oda Freddy Odille Hyun-Jic Oh Ajibola Oladokun Bruno Oliveira Hugo Oliveira Sara Oliveira Juan Olmos Jimena Olveres Olatunji Omisore Doruk Oner John Onofrey Felipe Orihuela-Espina José Orlando Mauricio Alberto Ortega-Ruíz Andreas Østvik Richard Osuala Chubin Ou Yafei Ou Geoffroy Oudoumanessah Cheng Ouyang Jiahong Ouyang Xi Ouyang Zhihao Ouyang Utku Ozbulak

Caner Ozer Ece Ozkan Ege Özsoy Martina Paccini Carolina Pacheco Harshith Padigela Miguel Padilla-Castañeda Johannes Paetzold José Blas Pagador Carrasco Eduardo Pais Pooch Daniel Pak Sourabh Palande Christoph Palm Chengwei Pan Hongyi Pan Jiazhen Pan Yimu Pan Egor Panfilov Haowen Pang Jiaxuan Pang Anja Pantovic Bartlomiej Papiez Nripesh Parajuli Jay Paranjape Abhijeet Parida Bogyu Park Hyunjin Park Jinsun Park Seyoun Park Akash Parvatikar Daniele Passaretti Vito Paolo Pastore Sushant Patkar Mayank Patwari Angshuman Paul Riti Paul Rasmus Paulsen João Pedrosa Yuchen Pei Yuru Pei Himashi Peiris Chantal Pellegrini Linkai Peng Tao Peng

Tingying Peng Wei Peng Yige Peng Yunsong Peng Matteo Pennisi Jacob Peoples Fernando Pérez-García Matthias Perkonigg Daniele Perlo Mattia Perrone Terry Peters Eike Petersen Simon Pezold Laura Pfaff Micha Pfeiffer Manna Philip Thomas Pinetz Pramod Pisharady Theodoros Pissas Clement Playout Szymon Płotka Gasper Podobnik Sebastian Pölsterl Dirk H. J. Poot Chinmay Prabhakar Tim Prangemeier Raphael Prevost William Prew Juan Prieto Federica Proietto Salanitri Sergi Pujades Anshul Pundhir Lorenzo Putzu Elodie Puybareau Talha Qaiser Xiaohua Qian Mengyun Qiao Yuchuan Qiao Wenjian Qin Yuxiang Qin Hejia Qiu Huaqi Qiu Jie Qiu Liang Qiu Peijie Qiu

Shi Qiu Tian Qiu Zhongwei Qiu Hui Qu Jiaqi Qu Liangqiong Qu Linhao Qu Sandro Queirós Gwenolé Quellec Hamid R. Rabiee Laya Rafiee Sevyeri Md Mostafijur Rahman Umaima Rahman Md Mahfuzur Rahman Siddiquee Ankita Raj Hamidreza Rajabzadeh-Oghaz Jagath Rajapakse Kashif Rajpoot Ashwin Raju Alexander Rakowski João Ramalhinho Vishwesh Ramanathan Xuming Ran Kapil Rana Roshan Rane Amin Ranem Bryan Ranger Aneesh Rangnekar Hatem Rashwan Brice Rauby Daniele Ravi Andrew Reader Razmig Rechichian Ingerid Reinertsen Islem Rekik Samuel Remedios Mengwei Ren Qin Ren Sucheng Ren Yudan Ren Zhihang Ren Mauricio Reyes Abel Reyes-Angulo

Hadrien Reynaud Helena Ribeiro Torres Daniel Riccio David Richmond Anne-Marie Rickmann Tammy Riklin Raviv Laurent Risser Leonhard Rist Leticia Rittner Emma Robinson Robert Robinson Jessica Rodgers Ranga Rodrigo Rafael Rodrigues Giorgio Roffo Robert Rohling Matteo Ronchetti Pooneh Roshanitabrizi Alberto Rota Holger Roth José Rouco Emmanuel Roux Saikat Roy Dan Ruan Jiacheng Ruan Su Ruan Daniel Rueckert Patryk Rygiel Jongbin Ryu Kanghyun Ryu Muhammad Muneeb Saad Ario Sadafi Vijay Sadashivaiah Numan Saeed Farhang Sahba Rajkumar Saini Gulfam Saju Md Sirajus Salekin Mohammad R. Salmanpour Thomas Sanchez Sara Sangalli Sivaramakrishnan Sankarapandian Gerard Sanroma-Guell

Rodrigo Santa Cruz Alberto Santamaria-Pang Alice Santilli Bikash Santra Nishchal Sapkota Alessia Sarica Olivier Saut Shier Nee Saw Asma Sbai Elisa Scalco Cian Scannell Alexander Schlaefer Maja Schlereth Leopold Schmetterer Adam Schmidt Helen Schneider Klaus Schoeffmann Thomas Schultz Evan Schwab Christina Schwarz-Gsaxner Michaël Sdika Lalithkumar Seenivasan Constantin Seibold Matthias Seibold Raghavendra Selvan Sourya Sengupta Thilo Sentker Carmen Serrano Sharmishtaa Seshamani Ahmed Shaffie Chintan Shah Jay Shah S. Shailja Fahad Shamshad Wei Shao Maxim Sharaev Mostafa Sharifzadeh Anuja Sharma Ojaswa Sharma Saurav Sharma Vanshali Sharma Daniel Shea Chuyun Shen Fei Shen

Linlin Shen Mali Shen Mingren Shen Xiongri Shen Yiqing Shen Zhenrong Shen Zhiqiang Shen Jun Shi Lulin Shi Ruohua Shi Xiaoshuang Shi Yaying Shi Yi Shi Yonggang Shi Zhan Shi Shu-Fu Shih Akinobu Shimizu Keewon Shin Yejee Shin Zhongyi Shui Nadya Shusharina Weixin Si Alberto Signoroni Carlos Silva Wilson Silva Julio Silva-Rodríguez Margarida Silveira Ivor Simpson Matthew Sinclair Aline Sindel Dilbag Singh Abhishek Singh Sambyal Rohit Singla Ayushi Sinha Ilyas Sirazitdinov Raviteja Sista Elena Sizikova Gregory Slabaugh Kevin Smith Roger Soberanis-Mukul Mahdieh Soleymani Baghshah Hong Song Jingwei Song Sifan Song

Weinan Song Mazen Soufi Raissa Souza Roberto Souza Georgia Sovatzidi Tyler Spears Ziga Spiclin Pradeeba Sridar Arvind Krishna Sridhar Vinkle Srivastav Ekta Srivastava Lawrence Staib Ivan Štajduhar Marc Stamminger Emma Stanley Marius Staring Johannes Stegmaier Jan Steinbrener Nil Stolt-Ansó Skylar Stolte Joshua Stough Justin Strait Martin Styner Ruisheng Su Vaishnavi Subramanian Prasad Sudhakar Carole Sudre Masanori Suganuma Yao Sui Heung-Il Suk Julian Suk Waqas Sultani Hongfu Sun Jinghan Sun Jingpeng Sun Liyan Sun Shanlin Sun Xiaofei Sun Xiaowu Sun Yihua Sun Vaanathi Sundaresan Kyung Sung Hanna Suominen Raphael Sznitman Behrad Taghibeyglou

Amir Tahmasebi Pablo Tahoces Hugues Talbot Roger Tam Bingyao Tan Jeremy Tan Jing Wei Tan Wenjun Tan Zimeng Tan Fenghe Tang Haoteng Tang Siyi Tang Yucheng Tang Yuxing Tang Zhenyu Tang Zihao Tang Michael Tanzer Austin Tapp Elias Tappeiner Mickael Tardy Giacomo Tarroni Sergio Tascon-Morales Wallapak Tavanapong Jacopo Teneggi Kaveri Thakoor Felix Thielke Paul Thienphrapa Mareike Thies Bertrand Thirion Belvin Thomas Sarina Thomas Cristiana Tiago Lin Tian Lixia Tian Xuanyu Tian Yingli Tian Yun Tian Matthew Toews Ren Togo Maryam Toloubidokhti Marwan Torki Benjamin Towle Stefano Trebeschi Prasun Tripathi Jakob Troidl

Apostolia Tsirikoglou Satoshi Tsutsui Puxun Tu Sudhakar Tummala Md Turja Aayush Tyagi Georgios Tziritas Kwang-Hyun Uhm Vladimír Ulman Tamas Ungi Balagopal Unnikrishnan Yonatan Urman Martin Urschler Hristina Uzunova Valentina Vadori Theodoros Vagenas Simao Valente Juan Miguel Valverde Fons van der Sommen Tom van Sonsbeek Gijs van Tulder Nathan Van Woudenberg Daniel Vašata Francisco Vasconcelos Maria Vasconcelos Serge Vasylechko S. Swaroop Vedula Harini Veeraraghavan Yordanka Velikova Sulaiman Vesal Mitko Veta Vibujithan Vigneshwaran Christiaan Viviers Athanasios Vlontzos Wolf-Dieter Vogl Jayneel Vora Sandrine Voros Vibashan V S Trinh Thi Le Vuong Felix Wagner Victor Wåhlstrand Skärström Shouhong Wan An Wang Bin Wang

Bo Wang Ce Wang Changmiao Wang Changwei Wang Cheng Wang Chengyan Wang Chenhui Wang Chong Wang Dingrong Wang Dongang Wang Edward Wang Fanwen Wang Guangming Wang Guotai Wang Haohan Wang Haoran Wang Hongkai Wang Hongxiao Wang Jiacheng Wang Jiahao Wang Jian Wang Jiaqi Wang Jiazhen Wang Jinfeng Wang Jing Wang Jiyao Wang Jue Wang Jui-Kai Wang Junchen Wang Kai Wang Ke Wang Lei Wang Liansheng Wang Lin Wang Ling Wang Linwei Wang Na Wang Peiqi Wang Qingfeng Wang Qingwang Wang Qiuli Wang Renzhen Wang Ruixuan Wang Shujun Wang Shuo Wang

Tao Wang Tianyu Wang Wei Wang Xi Wang Xiaofei Wang Xin Wang Xingyue Wang Xinyi Wang Yan Wang Yan Wang Yi Wang Yifeng Wang Yinuo Wang Yiqing Wang Yixin Wang Yiyang Wang Yuli Wang Zeyi Wang Zeyu Wang Zhao Wang Zhaoyang Wang Zhewei Wang Zhiwei Wang Zhu Wang Zifu Wang Ziqin Wang Zuhui Wang Matthew Watson Dong Wei Donglai Wei Jia Wei Jie Wei Meng Wei Qijie Wei Ruofeng Wei Shuwen Wei Ziquan Wei Wolfgang Wein Venera Weinhardt Oliver Weinheimer Lu Wen Zheyu Wen Thomas Wendler Nina Weng Wenhai Weng

Andrew Wentland Markus Wenzel Rhydian Windsor Alexander Winkler Eric Wisotzky Marek Wodzinski Ivo Wolf Lior Wolf Tom Nuno Wolf Julia Wolleb Ka-Chun Wong Ken C. L. Wong McKell Woodland Stefan Wörz Binghong Wu Chenwei Wu Huaqian Wu Jianghao Wu Jiangjie Wu Jie Ying Wu Jiong Wu Junde Wu Junyang Wu Kejun Wu Linshan Wu Mengqi Wu Qing Wu Ruoyou Wu Shaoiu Wu Shuang Wu Xiangjun Wu Xiao Wu Yawen Wu Ye Wu Yicheng Wu Yinzhe Wu Yixuan Wu Yunheng Wu Zhengwang Wu Zhenyu Wu Zihao Wu Nan Xi Chao Xia Jing Xia Jun Xia

Peng Xia Tian Xia Wenjun Xia Yihao Xia Yong Xia Jinhai Xiang Lei Xiang Suncheng Xiang Tiange Xiang Deqiang Xiao Li Xiao Sa Xiao Xiaojiao Xiao Zunjie Xiao Bin Xie Hai Xie Jianyang Xie Long Xie Wangduo Xie Xingyu Xie Yiting Xie Bowen Xin Shuwei Xing Xiaodan Xing Zhaohu Xing Conghao Xiong Chi Xu Di Xu Dongrong Xu Haozheng Xu Hongyan Xu Huahu Xu Jialang Xu Jiangchang Xu Jiaqi Xu Jilan Xu Lian Xu Mengya Xu Min Xu Moucheng Xu Rui Xu Weixin Xu Xiayu Xu Xiuyuan Xu Xuanang Xu

Yanwu Xu Yanyu Xu Yixi Xu Yongchao Xu Yunqiu Xu Zhe Xu Zhenghua Xu Zhoubing Xu Zikang Xu Cheng Xue Tengfei Xue Wufeng Xue Yuyang Xue Dilek Yalcinkaya Qingsen Yan Siyuan Yan Xiangyi Yan Yuguang Yan Zengqiang Yan Zipei Yan Baoyao Yang Bo Yang Carl Yang Chen Yang Chen Yang Gang Yang Guanyu Yang Huijuan Yang Jingyun Yang Peng Yang Qi Yang Qiushi Yang Sejung Yang Su Yang Wei Yang Xiaochen Yang Xikai Yang Xin Yang Xinyi Yang Xuan Yang Yan Yang Yanwu Yang Yifan Yang Yijun Yang Yingyu Yang

Zhicheng Yang Zhiwen Yang Ziyuan Yang Huifeng Yao Jiawen Yao Qingsong Yao Tianyuan Yao Wang Yao Xiaohui Yao Yongcheng Yao Yu Yao Zhao Yao Choon Hwai Yap Cheng Ye Jiayu Ye Johan Ye Kai Ye Wenqian Ye Yiwen Ye Chun-Hsiao Yeh Si Yong Yeo Jirong Yi Murong Yi Weixi Yi Yugen Yi George Yiasemis Chong Yin Wenzhe Yin Yi Yin Zhaozheng Yin Zijin Yin Chunwei Ying Jaejun Yoo Youngjin Yoo Jihun Yoon Xin You Han Yu Heng Yu Qi Yu Qian Yu Qinji Yu Renping Yu Shiqi Yu Sigang Yu Thomas Yu

Weihao Yu Weimin Yu Xiaoming Yu Xiaowei Yu Xin Yu Yang Yu Zhen Yu Zigi Yu Chenxi Yuan Kun Yuan Xiaohan Yuan Xinrui Yuan Huanjing Yue Boxiang Yun Paul Yushkevich Niloufar Zakariaei Anna Zapaishchykova Samira Zare Alexey Zaytsev Tal Zeevi Rachid Zeghlache Ramy Zeineldin Bolun Zeng Dewen Zeng Dong Zeng Qi Zeng Qingjie Zeng Tianyi Zeng Wei Zeng Kilian Zepf Hasnae Zerouaoui Ruyi Zha Baochang Zhang Chenxi Zhang Daoqiang Zhang Dong Zhang Gongyu Zhang Haoyue Zhang Haozheng Zhang Hongrun Zhang Jiajin Zhang Jianwei Zhang Jiazhen Zhang Jingqing Zhang Jingwei Zhang

Jingyang Zhang Ke Zhang Lefei Zhang Lei Zhang Lei Zhang Li Zhang Liping Zhang Liwen Zhang Min Zhang Minghui Zhang Minqing Zhang Molin Zhang Qiang Zhang Qianni Zhang Ranran Zhang Rongzhao Zhang Ruipeng Zhang Ruisi Zhang Ruobing Zhang Shaoteng Zhang Shengxuming Zhang Shuai Zhang Shuaitong Zhang Song Zhang Tong Zhang Tuo Zhang Wei Zhang Weijia Zhang Weitong Zhang Wenhua Zhang Wenqiang Zhang Xiao Zhang Xiaodan Zhang Xiaofan Zhang Xiaoran Zhang Xin Zhang Xinyuan Zhang Xuhong Zhang Xukun Zhang Xuzhe Zhang Yanbo Zhang Yanfu Zhang Yao Zhang Yejia Zhang Yi Zhang

Yifan Zhang Yilan Zhang Yinglin Zhang Yizhe Zhang You Zhang Youshan Zhang Yue Zhang Yuhan Zhang Yunlong Zhang Zhenxi Zhang Zheyuan Zhang Zhicheng Zhang Ziqi Zhang Ziyu Zhang Zuyu Zhang Baoliang Zhao Chongyue Zhao Fenqiang Zhao He Zhao Jianfeng Zhao Jichao Zhao Jun Zhao Kun Zhao Lingxiao Zhao Mengliu Zhao Mingyang Zhao Peiang Zhao Shang Zhao Shen Zhao Wei Zhao Wenzhao Zhao Xinkai Zhao Yidong Zhao Yitian Zhao Yizhou Zhao Fudan Zheng Haiyong Zheng Hao Zheng Haoxin Zheng Meng Zheng Shenhai Zheng Sisi Zheng Tianshu Zheng Xiqiang Zheng Yalin Zheng

Yushan Zheng Zhou Zheng Lanfeng Zhong Liang Zhong Chengfeng Zhou Haoyin Zhou Houliang Zhou Juan Helen Zhou Juexiao Zhou Qian Zhou Qin Zhou Rong Zhou Shuo Zhou Wei Zhou Wu Zhou

Yan-Jie Zhou Yaxuan Zhou Yi Zhou Youjia Zhou Yukun Zhou Yuyue Zhou Zhen Zhou Huaiyu Zhu Lei Zhu Lingting Zhu Meilu Zhu Wei Zhu Wenhui Zhu Xiaofeng Zhu Xin Zhu

Yanjun Zhu Yan Zhuang Muhammad Zia Ur Rehman Alexander Ziller David Zimmerer Fangrong Zong Weiwei Zong Yongshuo Zong Jing Zou Ke Zou Maria A. Zuluaga Lianrui Zuo Gerald Zwettler

## **Outstanding Area Chairs**

| Katharina Breininger | Friedrich-Alexander-Universität<br>Erlangen-Nürnberg, Germany |
|----------------------|---------------------------------------------------------------|
| Olivier Colliot      | CNRS, France                                                  |
| Pablo Arbeláez       | Universidad de los Andes, Colombia                            |

### **Outstanding Reviewers**

Nicolás Ayobi Universidad de los Andes, Colombia Finn Behrendt Hamburg University of Technology, Germany Mariano Cabezas University of Sydney, Australia Hannah Eichhorn Helmholtz Munich/Technical University of Munich, Germany Manuela Kunz National Research Council Canada, Canada Won Hee Lee Kyung Hee University, Korea Dong Ni Shenzhen University, China Ajibola Oladokun University of Cape Town, South Africa Egor Panfilov University of Oulu, Finland Sebastian Pölsterl AstraZeneca, Germany Ziga Spiclin University of Ljubljana, Slovenia Yixuan Wu Johns Hopkins University, USA

### **Honorable Mentions (Reviewers)**

Andrew King King's College London, UK Eleonora D'Arnese Politecnico di Milano, Italy Felix Wagner University of Oxford, UK Hongxiang Lin Zhejiang Lab, China Irina Grigorescu King's College London, UK Joshua Butke RIKEN AIP, Japan Julio Silva-Rodríguez ETS Montreal, Canada

Kwang-Hyun Uhm Korea University, Korea Kimberly Amador University of Calgary, Canada Konstantinos Kamnitsas University of Oxford, UK Krishna Chaitanya ETH Zurich, Switzerland

Seyed-Ahmad Ahmadi NVIDIA, Germany Ines Alejandro Cruz Guerrero University of Colorado Anschutz Medical Campus, USA Alireza Ganjdanesh University of Maryland, College Park, USA Bailiang Jian Technical University of Munich, Germany Benjamin Billot Massachusetts Institute of Technology, USA Blake Dewey Johns Hopkins University, USA Yu Yao University of Chinese Academy of Sciences, China Catalina Gomez Johns Hopkins University, USA Changmiao Wang Shenzhen Research Institute of Big Data, China Cosmin Bercea Technical University Munich/Helmholtz AI/Helmholtz Center Munich, Germany Deval Mehta Monash University, Australia Evangelos Mazomenos University College London, UK Eloy Geenjaar Georgia Institute of Technology, USA Elodie Germani Univ Rennes 1, Inria, CNRS, Inserm, France Emmanuel Roux Universite Claude Bernard Lyon 1, France Fabian Bongratz Technical University of Munich, Germany Farshid Alambeigi University of Texas, Austin, USA Gregory Holste University of Texas at Austin, USA Giacomo Tarroni City, University of London, UK Ivica Kopriva Rudjer Boskovich Institute, Croatia John Baxter Université de Rennes, France Johannes Stegmaier RWTH Aachen University, Germany Julian Mcginnis Technical University of Munich, Germany Klaus H. Maier-Hein German Cancer Research Center (DKFZ), Germany Krishna Nand Keshava Murthy Memorial Sloan Kettering Cancer Center, USA

Linhao Qu Fudan University, China Moon Hwan Lee DGIST, Korea Pierre-Henri Conze IMT Atlantique, France Ricardo Bigolin Lanfredi University of Utah, USA Tobias Klinder Philips, Germany

Lei Wang University of Wollongong, Australia Lia Morra Politecnico di Torino, Italy Liane Canas King's College London, UK Lina Felsner Technical University of Munich, Germany Meng Lou University of Hong Kong, China Lukas Fischer Software Competence Center Hagenberg, Austria Xinke Ma Northwestern Polytechnical University, China McKell Woodland Rice University; MD Anderson Cancer Center, **USA** Mingon Kang University of Nevada, Las Vegas, USA Mingyuan Meng University of Sydney, Australia Negin Ghamsarian University of Bern, Switzerland Omar Al-Kadi University of Jordan, Jordan Omar El Nahhas EKFZ for Digital Health, Germany Owen Carmichael Pennington Biomedical Research Center, USA Paul Yushkevich University of Pennsylvania, USA Robert Rohling University of British Columbia, Canada Sandro Queirós University of Minho, Portugal Satoshi Kondo Muroran Institute of Technology, Japan Thomas Schultz University of Bonn, Germany Sebastian Bodenstedt National Center for Tumor Diseases (NCT) Dresden, Germany Shadab Ahamed University of British Columbia, Canada Simon Pezold Clarunis – University Digestive Health Care Center Basel, Switzerland Simone Lionetti Lucerne University of Applied Sciences and Arts, Switzerland Thilo Sentker University Medical Center Hamburg-Eppendorf, Germany Martin Urschler Medical University Graz, Austria S. Swaroop Vedula Johns Hopkins University, USA Vito Paolo Pastore University of Genoa, Italy Yan Wang Sichuan University, China Wenting Chen City University of Hong Kong, China Peiqi Wang Massachusetts Institute of Technology, USA Xian Lin Huazhong University of Science and Technology, China

Yuan Gao
Yejia Zhang
Zhenghan Fang
Zhanghexuan Ji
Fudan Zheng
Zhifan Jiang
Youjia Zhou
Jingyang Zhang

Yuan Gao Alibaba Group, China Yejia Zhang University of Notre Dame, USA Zhenghan Fang Johns Hopkins University, USA<br>
Zhanghexuan Ji University at Buffalo, SUNY, US University at Buffalo, SUNY, USA Fudan Zheng Sun Yat-sen University, China Zhifan Jiang Children's National Hospital, USA University of Utah, USA Jingyang Zhang Chinese University of Hong Kong, China

# **Contents – Part XII**

## **Machine Learning - Efficient Learning Strategies II**

| Biophysics Informed Pathological Regularisation for Brain Tumour Segmentation                                                                                                                                                                         | 3   |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----|
| Lipei Zhang, Yanqi Cheng, Lihao Liu, Carola-Bibiane Schönlieb, and Angelica I Aviles-Rivero                                                                                                                                                           |     |
| Class-Balancing Deep Active Learning with Auto-Feature Mixing and Minority Push-Pull Sampling                                                                                                                                                         | 14  |
| Hongxin Lin, Chu Zhang, Mingyu Wang, Bin Huang, Jingjing Shao, Jinxiang Zhang, Zhenhua Gao, Xianfen Diao, and Bingsheng Huang                                                                                                                         |     |
| CoBooM: Codebook Guided Bootstrapping for Medical Image Representation Learning                                                                                                                                                                       | 23  |
| Azad Singh and Deepak Mishra                                                                                                                                                                                                                          |     |
| Continual Domain Incremental Learning for Privacy-Aware Digital Pathology                                                                                                                                                                             | 34  |
| Pratibha Kumari, Daniel Reisenbüchler, Lucas Luttner, Nadine S. Schaadt, Friedrich Feuerhake, and Dorit Merhof                                                                                                                                        |     |
| Decoupled Training for Semi-supervised Medical Image Segmentation with Worst-Case-Aware Learning                                                                                                                                                      | 45  |
| Ankit Das, Chandan Gautam, Hisham Cholakkal, Pritee Agrawal, Feng Yang, Ramasamy Savitha, and Yong Liu                                                                                                                                                |     |
| DiffRect: Latent Diffusion Label Rectification for Semi-supervised Medical Image Segmentation                                                                                                                                                         | 56  |
| Xinyu Liu, Wuyang Li, and Yixuan Yuan                                                                                                                                                                                                                 |     |
| Disentangled Hybrid Transformer for Identification of Infants with Prenatal Drug Exposure                                                                                                                                                             | 67  |
| Jiale Cheng, Zhengwang Wu, Xinrui Yuan, Li Wang, Weili Lin, Karen Grewen, and Gang Li                                                                                                                                                                 |     |
| Diversified and Structure-Realistic Fundus Image Synthesis for Diabetic Retinopathy Lesion Segmentation                                                                                                                                               | 77  |
| Xiaoyi Feng, Minqing Zhang, Mengxian He, Mengdi Gao, Hao Wei, and Wu Yuan                                                                                                                                                                             |     |
| Feature Extraction for Generative Medical Imaging Evaluation: New Evidence Against an Evolving Trend                                                                                                                                                  | 87  |
| McKell Woodland, Austin Castelo, Mais Al Taie,<br>Jessica Albuquerque Marques Silva, Mohamed Eltaher, Frank Mohn,<br>Alexander Shieh, Suprateek Kundu, Joshua P. Yung, Ankit B. Patel,<br>and Kristy K. Brock                                         |     |
| Few-Shot Domain Adaptive Object Detection for Microscopic Images<br>Sumayya Inayat, Nimra Dilawar, Waqas Sultani, and Mohsen Ali                                                                                                                      | 98  |
| Few-Shot Lymph Node Metastasis Classification Meets High Performance<br>on Whole Slide Images via the Informative Non-parametric Classifier<br>Yi Li, Qixiang Zhang, Tianqi Xiang, Yiqun Lin, Qingling Zhang,<br>and Xiaomeng Li                      | 109 |
| Fine-Grained Prompt Tuning: A Parameter and Memory Efficient Transfer<br>Learning Method for High-Resolution Medical Image Classification<br>Yijin Huang, Pujin Cheng, Roger Tam, and Xiaoying Tang                                                   | 120 |
| FissionFusion: Fast Geometric Generation and Hierarchical Souping<br>for Medical Image Analysis<br>Santosh Sanjeev, Nuren Zhaksylyk, Ibrahim Almakky,<br>Anees Ur Rehman Hashmi, Mohammad Areeb Qazi,<br>and Mohammad Yaqub                           | 131 |
| GBT: Geometric-Oriented Brain Transformer for Autism Diagnosis<br>Zhihao Peng, Zhibin He, Yu Jiang, Pengyu Wang, and Yixuan Yuan                                                                                                                      | 142 |
| Gradient Guided Co-Retention Feature Pyramid Network for LDCT<br>Image Denoising<br>Li Zhou, Dayang Wang, Yongshun Xu, Shuo Han, Bahareh Morovati,<br>Shuyi Fan, and Hengyong Yu                                                                      | 153 |
| Gyri vs. Sulci: Core-Periphery Organization in Functional Brain Networks<br>Xiaowei Yu, Lu Zhang, Chao Cao, Tong Chen, Yanjun Lyu, Jing Zhang,<br>Tianming Liu, and Dajiang Zhu                                                                       | 164 |
| Is This Hard for You? Personalized Human Difficulty Estimation for Skin<br>Lesion Diagnosis<br>Peter Johannes Tejlgaard Kampen, Anders Nymark Christensen,<br>and Morten Rieger Hannemose                                                             | 175 |
| LaB-GATr: Geometric Algebra Transformers for Large Biomedical<br>Surface and Volume Meshes<br>Julian Suk, Baris Imre, and Jelmer M. Wolterink                                                                                                         | 185 |
| Learning Temporally Equivariance for Degenerative Disease Progression<br>in OCT by Predicting Future Representations<br>Taha Emre, Arunava Chakravarty, Dmitrii Lachinov, Antoine Rivail,<br>Ursula Schmidt-Erfurth, and Hrvoje Bogunović             | 196 |
| Leveraging Image Captions for Selective Whole Slide Image Annotation<br>Jingna Qiu, Marc Aubreville, Frauke Wilm, Mathias Öttl, Jonas Utz,<br>Maja Schlereth, and Katharina Breininger                                                                | 207 |
| MEDBind: Unifying Language and Multimodal Medical Data Embeddings<br>Yuan Gao, Sangwook Kim, David E. Austin, and Chris McIntosh                                                                                                                      | 218 |
| MedContext: Learning Contextual Cues for Efficient Volumetric Medical<br>Segmentation<br>Hanan Gani, Muzammal Naseer, Fahad Khan, and Salman Khan                                                                                                     | 229 |
| Medical Image Synthesis via Fine-Grained Image-Text Alignment<br>and Anatomy-Pathology Prompting<br>Wenting Chen, Pengyu Wang, Hui Ren, Lichao Sun, Quanzheng Li,<br>Yixuan Yuan, and Xiang Li                                                        | 240 |
| Multi-Dataset Multi-Task Learning for COVID-19 Prognosis<br>Filippo Ruffini, Lorenzo Tronchin, Zhuoru Wu, Wenting Chen,<br>Paolo Soda, Linlin Shen, and Valerio Guarrasi                                                                              | 251 |
| PEMMA: Parameter-Efficient Multi-Modal Adaptation for Medical Image<br>Segmentation<br>Nada Saadi, Numan Saeed, Mohammad Yaqub, and Karthik Nandakumar                                                                                                | 262 |
| Reducing Annotation Burden: Exploiting Image Knowledge for Few-Shot<br>Medical Video Object Segmentation via Spatiotemporal Consistency<br>Relearning<br>Zixuan Zheng, Yilei Shi, Chunlei Li, Jingliang Hu, Xiao Xiang Zhu,<br>and Lichao Mou         | 272 |
| SBC-AL: Structure and Boundary Consistency-Based Active Learning<br>for Medical Image Segmentation<br>Taimin Zhou, Jin Yang, Lingguo Cui, Nan Zhang, and Senchun Chai                                                                                 | 283 |
| Semi-Supervised Learning for Deep Causal Generative Models<br>Yasin Ibrahim, Hermione Warr, and Konstantinos Kamnitsas                                                                                                                                | 294 |
| SynCellFactory: Generative Data Augmentation for Cell Tracking<br>Moritz Sturm, Lorenzo Cerrone, and Fred A. Hamprecht                                                                                                                                | 304 |
| TAPoseNet: Teeth Alignment Based on Pose Estimation via Multi-scale Graph Convolutional Network<br>Qingxin Deng, Xunyu Yang, Minghan Huang, Landu Jiang,<br>and Dian Zhang                                                                            | 314 |
| TE-SSL: Time and Event-Aware Self Supervised Learning for Alzheimer's Disease Progression Analysis<br>Jacob Thrasher, Alina Devkota, Ahmad P. Tafti,<br>Binod Bhattarai, Prashnna Gyawali,<br>and for the Alzheimer's Disease Neuroimaging Initiative | 324 |
| Training ViT with Limited Data for Alzheimer's Disease Classification:<br>An Empirical Study<br>Kassymzhomart Kunanbayev, Vyacheslav Shen, and Dae-Shik Kim                                                                                           | 334 |
| TrIND: Representing Anatomical Trees by Denoising Diffusion of Implicit Neural Fields<br>Ashish Sinha and Ghassan Hamarneh                                                                                                                            | 344 |
| Universal Semi-supervised Learning for Medical Image Classification<br>Lie Ju, Yicheng Wu, Wei Feng, Zhen Yu, Lin Wang, Zhuoting Zhu,<br>and Zongyuan Ge                                                                                              | 355 |
| Vertex Proportion Loss for Multi-class Cell Detection from Label Proportions<br>Carolina Pacheco, Florence Yellin, René Vidal, and Benjamin Haeffele                                                                                                  | 366 |
| <b>Machine Learning - Foundation Models</b>                                                                                                                                                                                                           |     |
| A Foundation Model for Brain Lesion Segmentation with Mixture of Modality Experts<br>Xinru Zhang, Ni Ou, Berke Doga Basaran, Marco Visentin,<br>Mengyun Qiao, Renyang Gu, Cheng Ouyang, Yaou Liu,<br>Paul M. Matthews, Chuyang Ye, and Wenjia Bai     | 379 |
|                                                                                                                                                                                                                                                       |     |

xl Contents – Part XII

A New Non-invasive AI-Based Diagnostic System for Automated Diagnosis of Acute Renal Rejection in Kidney Transplantation: Analysis of ADC Maps Extracted from Matched 3D Iso-Regions of the Transplanted Kidney . . . . . . . . . . . . . . . . . . . *Ibrahim Abdelhalim, Mohamed Abou El-Ghar, Amy Dwyer, Rosemary Ouseph, Sohail Contractor, and Ayman El-Baz*

| A Refer-and-Ground Multimodal Large Language Model for Biomedicine ..... 399                                                                                                                                                                                                |     |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----|
| Xiaoshuang Huang, Haifeng Huang, Lingdong Shen, Yehui Yang,                                                                                                                                                                                                                 |     |
| Fangxin Shang, Junwei Liu, and Jia Liu                                                                                                                                                                                                                                      |     |
| A Unified Model for Longitudinal Multi-Modal Multi-View Prediction<br>Boqi Chen, Junier Oliva, and Marc Niethammer                                                                                                                                                          | 410 |
| An Approach to Building Foundation Models for Brain Image Analysis<br>Davood Karimi                                                                                                                                                                                         | 421 |
| An Empirical Study on the Fairness of Foundation Models for Multi-Organ<br>Image Segmentation<br>Qing Li, Yizhe Zhang, Yan Li, Jun Lyu, Meng Liu, Longyu Sun,<br>Mengting Sun, Qirong Li, Wenyue Mao, Xinran Wu, Yajing Zhang,<br>Yinghua Chu, Shuo Wang, and Chengyan Wang | 432 |
| BAPLe: Backdoor Attacks on Medical Foundational Models Using Prompt Learning<br>Asif Hanif, Fahad Shamshad, Muhammad Awais, Muzammal Naseer,<br>Fahad Shahbaz Khan, Karthik Nandakumar, Salman Khan,<br>and Rao Muhammad Anwer                                              | 443 |
| Black-Box Adaptation for Medical Image Segmentation<br>Jay N. Paranjape, Shameema Sikder, S. Swaroop Vedula,<br>and Vishal M. Patel                                                                                                                                         | 454 |
| CLEFT: Language-Image Contrastive Learning with Efficient Large<br>Language Model and Prompt Fine-Tuning<br>Yuexi Du, Brian Chang, and Nicha C. Dvornek                                                                                                                     | 465 |
| CT2Rep: Automated Radiology Report Generation for 3D Medical Imaging<br>Ibrahim Ethem Hamamci, Sezgin Er, and Bjoern Menze                                                                                                                                                  | 476 |
| Curriculum Prompting Foundation Models for Medical Image Segmentation<br>Xiuqi Zheng, Yuhang Zhang, Haoran Zhang, Hongrui Liang,<br>Xueqi Bao, Zhuqing Jiang, and Qicheng Lao                                                                                               | 487 |
| DB-SAM: Delving into High Quality Universal Medical Image Segmentation<br>Chao Qin, Jiale Cao, Huazhu Fu, Fahad Shahbaz Khan,<br>and Rao Muhammad Anwer                                                                                                                     | 498 |
| DeSAM: Decoupled Segment Anything Model for Generalizable Medical Image Segmentation<br>Yifan Gao, Wei Xia, Dingdu Hu, Wenkui Wang, and Xin Gao                                                                                                                             | 509 |
| DinoBloom: A Foundation Model for Generalizable Cell Embeddings                                                                                                                                                                                                             | 520 |
| in Hematology<br>Valentin Koch, Sophia J. Wagner, Salome Kazeminia, Ece Sancar,<br>Matthias Hehr, Julia A. Schnabel, Tingying Peng, and Carsten Marr                                                                                                                        |     |
| FACMIC: Federated Adaptative CLIP Model for Medical Image                                                                                                                                                                                                                   |     |
| Classification<br>Yihang Wu, Christian Desrosiers, and Ahmad Chaddad                                                                                                                                                                                                        | 531 |
| FastSAM3D: An Efficient Segment Anything Model for 3D Volumetric                                                                                                                                                                                                            |     |
| Medical Images<br>Yiqing Shen, Jingxing Li, Xinyuan Shao, Blanca Inigo Romillo,<br>Ankush Jindal, David Dreizin, and Mathias Unberath                                                                                                                                       | 542 |
| Few-Shot Adaptation of Medical Vision-Language Models<br>Fereshteh Shakeri, Yunshi Huang, Julio Silva-Rodríguez, Houda Bahig,<br>An Tang, Jose Dolz, and Ismail Ben Ayed                                                                                                    | 553 |
| fTSPL: Enhancing Brain Analysis with FMRI-Text Synergistic Prompt                                                                                                                                                                                                           |     |
| Learning<br>Pengyu Wang, Huaqi Zhang, Zhibin He, Zhihao Peng, and Yixuan Yuan                                                                                                                                                                                               | 564 |
| HiA: Towards Chinese Multimodal LLMs for Comparative<br>High-Resolution Joint Diagnosis<br>Xinpeng Ding, Yongqiang Chu, Renjie Pi, Hualiang Wang,<br>and Xiaomeng Li                                                                                                        | 575 |
| Knowledge-Grounded Adaptation Strategy for Vision-Language Models:<br>Building a Unique Case-Set for Screening Mammograms for Residents                                                                                                                                     |     |
| Training<br>Aisha Urooj Khan, John Garrett, Tyler Bradshaw, Lonie Salkowski,<br>Jiwoong Jeong, Amara Tariq, and Imon Banerjee                                                                                                                                               | 587 |
| Learnable Skeleton-Based Medical Landmark Estimation with Graph                                                                                                                                                                                                             |     |
| Sparsity and Fiedler Regularizations<br>Yao Wang, Jiahao Chen, Wenjian Huang, Pei Dong, and Zhen Qian                                                                                                                                                                       | 599 |
| LGA: A Language Guide Adapter for Advancing the SAM Model's                                                                                                                                                                                                                 |     |
| Capabilities in Medical Image Segmentation<br>Jihong Hu, Yinhao Li, Hao Sun, Yu Song, Chujie Zhang, Lanfen Lin,<br>and Yen-Wei Chen                                                                                                                                         | 610 |
| $M4oE$ : A Foundation Model for Medical Multimodal Image Segmentation                                                                                                                                                                                                       |     |
| with Mixture of Experts<br>Yufeng Jiang and Yiqing Shen                                                                                                                                                                                                                     | 621 |
| Contents – Part XII                                                                                                                                                                                                                                                         | xlv |
|                                                                                                                                                                                                                                                                             |     |

| Mammo-CLIP: A Vision Language Foundation Model to Enhance Data<br>Efficiency and Robustness in Mammography<br>Shantanu Ghosh, Clare B. Poynton, Shyam Visweswaran,<br>and Kayhan Batmanghelich                                                  | 632 |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----|
| MedCLIP-SAM: Bridging Text and Image Towards Universal Medical<br>Image Segmentation<br>Taha Koleilat, Hojat Asgariandehkordi, Hassan Rivaz, and Yiming Xiao                                                                                    | 643 |
| MedSynth: Leveraging Generative Model for Healthcare Data Sharing<br>Renuga Kanagavelu, Madhav Walia, Yuan Wang, Huazhu Fu,<br>Qingsong Wei, Yong Liu, and Rick Siow Mong Goh                                                                   | 654 |
| One Registration is Worth Two Segmentations<br>Shiqi Huang, Tingfa Xu, Ziyi Shen, Shaheer Ullah Saeed, Wen Yan,<br>Dean Barratt, and Yipeng Hu                                                                                                  | 665 |
| PEPSI: Pathology-Enhanced Pulse-Sequence-Invariant Representations<br>for Brain MRI<br>Peirong Liu, Oula Puonti, Annabel Sorby-Adams, W. Taylor Kimberly,<br>and Juan E. Iglesias                                                               | 676 |
| Prompting Vision-Language Models for Dental Notation Aware<br>Abnormality Detection<br>Chenlin Du, Xiaoxuan Chen, Jingyi Wang, Junjie Wang, Zhongsen Li,<br>Zongjiu Zhang, and Qicheng Lao                                                      | 687 |
| PromptSmooth: Certifying Robustness of Medical Vision-Language<br>Models via Prompt Learning<br>Noor Hussein, Fahad Shamshad, Muzammal Naseer,<br>and Karthik Nandakumar                                                                        | 698 |
| RET-CLIP: A Retinal Image Foundation Model Pre-trained with Clinical<br>Diagnostic Reports<br>Jiawei Du, Jia Guo, Weihang Zhang, Shengzhu Yang, Hanruo Liu,<br>Huiqi Li, and Ningli Wang                                                        | 709 |
| S-SAM: SVD-Based Fine-Tuning of Segment Anything Model<br>for Medical Image Segmentation<br>Jay N. Paranjape, Shameema Sikder, S. Swaroop Vedula,<br>and Vishal M. Patel                                                                        | 720 |
| ShapeMamba-EM: Fine-Tuning Foundation Model with Local Shape<br>Descriptors and Mamba Blocks for 3D EM Image Segmentation<br>Ruohua Shi, Qiufan Pang, Lei Ma, Lingyu Duan, Tiejun Huang,<br>and Tingting Jiang                                  | 731 |
| Symmetry Awareness Encoded Deep Learning Framework for Brain<br>Imaging Analysis<br>Yang Ma, Dongang Wang, Peilin Liu, Lynette Masters, Michael Barnett,<br>Weidong Cai, and Chenyu Wang                                                        | 742 |
| UrFound: Towards Universal Retinal Foundation Models<br>via Knowledge-Guided Masked Modeling<br>Kai Yu, Yang Zhou, Yang Bai, Zhi Da Soh, Xinxing Xu,<br>Rick Siow Mong Goh, Ching-Yu Cheng, and Yong Liu                                        | 753 |
| VertFound: Synergizing Semantic and Spatial Understanding<br>for Fine-Grained Vertebrae Classification via Foundation Models<br>Yinhao Wu, Jinzhou Tang, Zequan Yao, Mingjie Li, Yuan Hong,<br>Dongdong Yu, Zhifan Gao, Bin Chen, and Shen Zhao | 763 |
| XCoOp: Explainable Prompt Learning for Computer-Aided Diagnosis<br>via Concept-Guided Context Optimization<br>Yequan Bie, Luyang Luo, Zhixuan Chen, and Hao Chen                                                                                | 773 |
| Author Index                                                                                                                                                                                                                                    | 785 |

# **Machine Learning - Efficient Learning Strategies II**

Image /page/46/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line segment on the left and a flag shape on the right. The button is light gray with a subtle gradient, and the text is dark gray.

# <span id="page-46-0"></span>**Biophysics Informed Pathological Regularisation for Brain Tumour Segmentation**

Lipei Zhang<sup>( $\boxtimes$ )</sup>, Yanqi Cheng, Lihao Liu, Carola-Bibiane Schönlieb, and Angelica I Aviles-Rivero

Department of Applied Mathematics and Theoretical Physics, University of Cambridge, Cambridge, UK {lz452,yc443,ll610,cbs31,ai323}@cam.ac.uk

Abstract. Recent advancements in deep learning have significantly improved brain tumour segmentation techniques; however, the results still lack confidence and robustness as they solely consider image data without biophysical priors or pathological information. Integrating biophysicsinformed regularisation is one effective way to change this situation, as it provides an prior regularisation for automated end-to-end learning. In this paper, we propose a novel approach that designs brain tumour growth Partial Differential Equation (PDE) models as a regularisation with deep learning, operational with any network model. Our method introduces tumour growth PDE models directly into the segmentation process, improving accuracy and robustness, especially in data-scarce scenarios. This system estimates tumour cell density using a periodic activation function. By effectively integrating this estimation with biophysical models, we achieve a better capture of tumour characteristics. This approach not only aligns the segmentation closer to actual biological behaviour but also strengthens the model's performance under limited data conditions. We demonstrate the effectiveness of our framework through extensive experiments on the BraTS 2023 dataset, showcasing significant improvements in both precision and reliability of tumour segmentation.

**Keywords:** Glioma · Segmentation · Partial Differential Equations · Deep Learning · Representation Regularisation

## **1 Introduction**

Glioblastoma, the most aggressive brain cancer, represents 14.3% of primary malignant central nervous system tumours. It exhibits rapid, heterogeneous growth that complicates detection and treatment. Thus often leading to poor prognosis despite advanced interventions [\[16,](#page-55-0)[17](#page-55-1)]. Magnetic Resonance Imaging (MRI) plays a crucial role in diagnosing brain tumours. It provides high-resolution images

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_1) 1.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 3–13, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_1)\_1

that are essential for accurate tumour margin segmentation. These images also guide treatment decisions across various modalities, such as T1, T1-weighted with contrast (T1w-gd), T2-weighted (T2w), and T2 Fluid Attenuated Inversion Recovery (FLAIR) [\[4\]](#page-54-0). Advances in deep learning, particularly with introduction of the U-Net architecture [\[24\]](#page-56-0), have significantly improved segmentation in different imaging modalities [\[1](#page-54-1)[,6](#page-55-2),[11,](#page-55-3)[13](#page-55-4)[,20](#page-55-5)[,22](#page-56-1)]. The integration of U-Net and Transformer models [\[8](#page-55-6),[9,](#page-55-7)[14](#page-55-8)], utilising long-range attention mechanisms, show improved accuracy in the Brain Tumour Segmentation (BraTS) Challenge [\[3\]](#page-54-2).

However, the limited data availability in biology and medicine highlights the challenges of the generalisability issues arising from the limited availability of medical data. This underscores the importance of incorporating domain-specific knowledge into deep learning models to enhance their utility in medical imaging. Physics-Informed Neural Networks (PINNs) offer a promising approach to overcome generalisability issues. They integrate boundary conditions and partial differential equations (PDEs) into the learning process. It has enabled their application across various fields. For example, it is used in elasticity reconstruction [\[23\]](#page-56-2), Alzheimer's disease analysis [\[28\]](#page-56-3), and glioma progression estimation [\[19](#page-55-9)]. However, the effort in glioma has primarily focused on parameter estimation. Previous efforts have not succeeded in directly integrating physics-informed learning with automated segmentation models. The integration could leverage the inherent structure and dynamics of biological systems. This approach has the potential to yield more accurate and biologically plausible segmentation results. This method could connect data-driven models with model-driven models that include specific expert knowledge in their learning.

In this work, we propose a novel method that designs biophysics-informed regularisation to improve brain tumor segmentation through deep learning. This approach embeds pathological insights into tumour growth dynamics, enhancing segmentation accuracy and robustness. Our contributions are:

- 1. A unified framework that integrate deep learning with brain tumour growth PDE models through a biophysics-informed regularisation, enhancing segmentation precision and robustness across various scenarios.
- 2. Introducing MRI-driven tumour cell density estimator estimates along with biophysics-informed regularisation, achieving segmentations that reflect real tumour growth mechanism. In this estimator, a designed periodic activation function in the high-level feature extracting layers captures the non-linear dynamics of the biophysical model, enabling detailed signal and spatial derivative learning for calculation of biophysics-informed regularisation term.
- 3. These innovations have yielded exceptional segmentation results on the BraTS 2023 dataset, proving effective across various architectures, dataset sizes, missing modalities and combinations with other losses.

#### **2 Proposed Method**

*Brain Tumour Segmentation.* In this problem, we aim to segment 4 regions: normal region  $(y_0)$ , tumour core  $(TC, y_1)$ , and whole tumour  $(WT, y_2)$ , enhancing tumour (ET,  $y_3$ ), in a given MRI data with T1  $(i_0)$ , T1Gd  $(i_1)$ , T2  $(i_2)$ , and FLAIR  $(i_3)$  volumes. The segmentation model can be defined as  $f_\theta$  such that the probability of each class of P can be obtained as  $P = f_{\theta}(I)$ , where  $I = \{i_0, i_1, i_2, i_3\}, P = \{p_0, p_1, p_2, p_3\}, \text{ and } Y = \{y_0, y_1, y_2, y_3\} \text{ indicates one-}$ hot mask of each subregion. The function  $f_{\theta}$  is usually nonlinear and  $\theta$  is a large vector of parameters. The learning phase selects  $\theta$  in order to minimise a loss function L that measures the accuracy of the predicted segmentation  $f_{\theta}(I)$ . This overall training flow is shown in Fig. [1\(](#page-48-0)a). The parameters  $\theta$  are obtained by minimising a loss function plus a regularisation term. Consider a dice loss with supervised manner, we optimise the network by:

$$
\arg\min_{\theta} \mathcal{L}(I, Y, \theta) = \arg\min_{\theta} \left( \sum_{n \in \{0, 1, 2, 3\}} \left( 1 - \frac{2y_n p_n + 1}{y_n + p_n + 1} \right) + R_{\theta}(f_{\theta}(I)) \right) \tag{1}
$$

*Biophysics-Informed Optimisation.* Exploring effective regularisation terms  $R_{\theta}(\cdot)$  is essential to improve model robustness. Unlike current regularisation approaches based on VAE [\[21\]](#page-56-4), we propose that incorporating biophysical models of tumour growth model into the training process as an more explainable learning bias. It enables the segmentation model to capture more accurate pathological features representative of tumour areas. To this end, we introduce a biophysics-informed regularisation module designed as a plug-and-play component. This module integrates two key elements: additional fully connected layers with periodic activation functions for pathological information extraction, and a combination of proliferation and diffusion PDEs alongside boundary conditions.

Image /page/48/Figure/5 description: This is a diagram illustrating a deep learning model for 3D medical image segmentation, incorporating biophysics-informed regularization. The diagram is divided into four main sections: A, B, C, and D. Section A shows a convolutional neural network (CNN) architecture that takes 3D multiple modalities of medical images as input and outputs a 3D segmentation prediction. This prediction is then used to calculate a segmentation loss. Section B depicts a neural network (NN) that takes spatial coordinates (x) and time (t) as input, along with parameters (θu), to predict a value (û). Section C shows the calculation of a partial differential equation (PDE) involving time derivative and divergence of a diffusion term, which is related to the predicted value û. Section D shows another PDE involving the gradient of û and a boundary condition. Both the PDE calculations in C and D contribute to a biophysics-informed regularization term, which is then added to the segmentation loss to form the final loss function.

<span id="page-48-0"></span>**Fig. 1.** The Biophysics-informed optimisation for segmentation. A. Main structure for brain tumour segmentation. B. Tumour cell density estimator (the flattened feature map will be concatenated with assumed time matrix T). C. Calculation of PDE loss. D. Calculation of boundary loss.

This integration not only improves the segmentation's ability to capture tumour details by embedding biophysical regularisation directly into the end-to-end optimisation process but also ensures the model's outputs align more closely with actual biological behaviours. The overall objective loss, balanced by weights  $\lambda_1$ and  $\lambda_2$ , is formulated as:

$$
\mathcal{L}_{\text{Total}} = \mathcal{L}_{\text{Dice}} + \underbrace{\lambda_1 \mathcal{L}_{\text{PDE}} + \lambda_2 \mathcal{L}_{\text{BC}}}_{\text{Biophysics Informed Regularisation}} \tag{2}
$$

*Tumour Cell Density Estimator.* In this first part of this module, we introduce a neural network component,  $NN(x, t; \theta^u)$ , which employs multiple fully connected layers with periodic activation functions to disentangle high-level feature maps from middle stage to potential tumour cell density  $\hat{u}$ . At these stages, the feature maps primarily highlight pathological features, focusing on tumour regions. This architecture offers a significant advantage, that it learns the nonlinear aspects of governing PDEs indirectly, thus endowing the model with the capability to capture detailed signals and spatial derivatives without specifying them explicitly.

The process begins with a set of feature maps  $(B, C, H, W, D)$ , where B represents batch size, C is channel number and  $H, W, D$  denote height, width and depth, respectively. These maps are flattened across  $H, W, D$  dimensions to form a matrix X with dimensions  $(B, C, H \times W \times D)$ . This procedure ensures efficient sequential processing and maintains the independence of the information of each channel. This approach, different from SIREN's coordinate consideration [\[27](#page-56-5)], incorporates an assumed temporal dimension  $T$ , viewing each segmentation optimisation step as a discrete moment in time. The temporal dimension  $T$ , a matrix of the same shape as  $X$ , but filled with the assumed time step t, is concatenated with  $X$ . Time embedding is crucial for simulating tumour dynamics during training, ensuring the model's output aligns with expected growth dynamics. It also facilitates the calculation of the first-order time derivative, integrating temporal dynamics into our model. The concatenated features  $y = (X, T) = ((x_1, t_1)(x_2, t_2), ..., (x_t, t_C))$  with size of  $(B, C, H \times W \times D \times 2)$ , with  $x_c$  and  $t_c$  represent dimensional and temporal matrix on the  $c^{th}$  channel respectively, undergo transformation as follows:

$$
\hat{u} = \Gamma(y) = W_n(\gamma_{n-1} \circ \gamma_{n-2} \circ \cdots \circ \gamma_0)(y) + b_n, \quad y_i \mapsto \gamma_i(y_i) = \sin(W_i y_i + b_i) \tag{3}
$$

where,  $\gamma_i : \mathbb{R}^{M_i} \to \mathbb{R}^{N_i}$  denotes the *i*<sup>th</sup> layer of the transformation. It comprises the affine transforms defined by the weight matrix  $W_i \in \mathbb{R}^{N_i \times M_i}$ , and the biases  $b_i \in \mathbb{R}^{N_i}$  utilised on the input  $y_i \in \mathbb{R}^{M_i}$ , followed by the sine nonlinearity. This process extracts the tumour cell density  $\hat{u}$ , facilitating the calculation of tumour proliferation and diffusion PDEs. During the inference phase, this module can be pruned to avoid any increase in processing time.

*Calculation of Biophysics-Informed Regularisation.* Tumour cell density proliferation and diffusion across the brain is predominantly based on the reaction-diffusion equation [\[12](#page-55-10)], the model incorporates logistic growth to represent cell proliferation and enforces Neumann boundary conditions to model tumour boundaries within the brain domain  $\Omega$ ,

$$
\frac{\partial u}{\partial t} = \underbrace{\nabla \cdot (d\nabla u)}_{\text{Diffusion}} + \underbrace{f(u)}_{\text{Reaction}}
$$

$$
f(u) = \rho u (1 - u), \quad d\nabla u \cdot u_{\partial \Omega} = 0
$$
(4)

These equations describes the evolution of the tumour cell density  $u$ , which infiltrates neighbouring tissues with a diffusion tensor d, and proliferates according to the law defined with  $f(u)$ . We consider the reaction term with a logistic growth term with a net proliferation rate  $\rho$ . Here, d and  $\rho$  have same dimension with u. The  $\frac{\partial u}{\partial t}$  with respect to time t is computed through automatic differentiation due to t embedded with input x. The predicted cell density vectors u are reshaped back to 3D format  $(B, C, H, W, D)$ .  $F_{\Delta}(u) = \nabla \cdot (d\nabla u)$ , is approximated using a 3D Laplacian kernel  $(K)$ . This kernel is more efficient than traditional central finite difference method for preserving spatial information when computing the second derivative and boundary condition related to location.

$$
K = \begin{bmatrix} 0 & 0 & 0 \\ 0 & 1 & 0 \\ 0 & 0 & 0 \end{bmatrix} \begin{bmatrix} 0 & 1 & 0 \\ 1 & -6 & 1 \\ 0 & 1 & 0 \end{bmatrix} \begin{bmatrix} 0 & 0 & 0 \\ 0 & 1 & 0 \\ 0 & 0 & 0 \end{bmatrix} \begin{bmatrix} 0 \\ 0 \\ 0 \end{bmatrix}
$$
 (5)

After considering all voxels with total number of  $N_{biophy} = H \times W \times D$  in feature maps, the PDE loss term takes the following form:

$$
\mathcal{L}_{\rm PDE} = \left\| \frac{1}{N_{biophy}} \sum_{i=1}^{N_{biophy}} \left( \frac{\partial u_i}{\partial t_i} - \nabla \cdot (D \nabla u_i) - \rho u_i (1 - u_i) \right)^2 \right\| \tag{6}
$$

||·|| represents the norm across channels and batches. Additionally, boundary constraints are specified to model the diffusion process accurately, ensuring the diffusion remains non-negative and aligns with the spatial domain of the brain within the feature maps. Here, this can be describe by the following formula:

$$
0 \le x \le H, \quad 0 \le y \le W, \quad 0 \le z \le D
$$
  

$$
u_x(0, y, z) = 0, \quad u_x(H, y, z) = 0
$$
  

$$
u_y(x, 0, z) = 0, \quad u_y(x, W, z) = 0
$$
  

$$
u_z(x, y, 0) = 0, \quad u_z(x, y, D) = 0
$$
 $(7)$ 

The corresponding boundary constraints take the form, with **1** indicates the indicator function:

$$
\mathcal{L}_{\rm BC} = \Big\| d \sum_{x=0}^{H} \sum_{y=0}^{W} \sum_{z=0}^{D} \Big[ \frac{\mathbb{1}_{\{0,H\}}(x)}{WD} u_x^2(y,z) + \frac{\mathbb{1}_{\{0,W\}}(y)}{HD} u_y^2(x,z) + \frac{\mathbb{1}_{\{0,D\}}(z)}{HW} u_z^2(x,y) \Big] \Big\| \tag{8}
$$

## **3 Experiment**

*Datasets.* Our evaluations were applied on the BraTS 2023 dataset [\[3](#page-54-2)], consisting of 1251 cases within the official training partition. Each case comprises mpMRI scans with four modalities: T1, T1c, T2, and FLAIR, each having dimensions of  $240 \times 240 \times 155$ . Three tumor subregions—enhancing tumour (ET), tumour core (TC), and whole tumour (WT)—were labelled by neuroradiologists. Given the absence of labels in the official validation and testing sets, the 1251 cases was split into training, validation, and test sets with a ratio of 7:1:2. During training, the 4-channel MRI volumes centred on the tumour were cropped to  $128 \times 128 \times 128$  voxel patches. To mitigate overfitting, augmentation techniques were employed. Z-score standardisation was performed on non-zero voxels across each channel, with outlier values being clipped. For inference, same centred cropping on the tumour of 4-channel MRI volumes was conducted, complemented by test-time augmentation (TTA) techniques [\[26](#page-56-6)] and overlapped sliding window inference  $[11]$ . Other preprocessing setups aligned with  $[5]$ .

*Main Experimental Details and Compared methods.* All experiments, developed on MONAI v1.3.0 [\[18](#page-55-11)] and Pytorch 2.1.0, was executed on an Nvidia A10 GPU with 24GB memory with automatic mixed precision training. The number of epoch was 175 epochs and batch size was set to 1. We used the Ranger 2020 optimiser [\[15](#page-55-12)[,29](#page-56-7)] with a starting learning rate of  $3 \times 10^{-4}$  and a cosine decay schedule. Dice Loss [\[20\]](#page-55-5) was computed both batch-wise and channelwise, unweighted, alongside a consistent loss weight for PDE and BC ( $\lambda_1$  and  $\lambda_2$ ) set to 1). Final models were used for testing. Baseline comparisons included 3D UNet [\[24\]](#page-56-0), R2-UNet [\[1](#page-54-1)], nn-UNet [\[11](#page-55-3)], UNETR [\[9](#page-55-7)], SegResNet, and SegResNet-VAE [\[2\]](#page-54-4), all employing single dice loss. We compared these with our proposed biophysics-informed loss, excluding SegResNetVAE for regularization comparison. More configurations are shown in appendix. The diffusion coefficient  $d$ and proliferation rate  $\rho$  are in the range of [0.02, 1.5]  $mm^2/day$  and  $\rho$  between  $[0.002, 0.2]/day$  for tumour tissues respectively [\[12](#page-55-10)]. High-level feature maps, targeting tumour region, assume isotropic diffusion across voxels due to gliomas' tendency to disrupt neural pathways, leading to uniform diffusion. The input size of feature map for the tumour cell estimator was  $(B \times C \times 16 \times 16 \times 16)$  to ensure computational efficiency. The d and  $\rho$  values randomly sampled from the specified ranges for each voxel. This random sampling introduces regularisation, ensuring biophysical priors fall within a valid distribution.

**Evaluation Metrics.** The results were compared with 2 quantitative criteria, Dice similarity coefficient (Dice) [\[7\]](#page-55-13) and Hausdorff Distance (HD) [\[10](#page-55-14)], on each of the three sub-regions. We take the norm as Euclidean norm for HD. Outliers were removed using the 95<sup>th</sup> percentile (HD95) approach [\[5](#page-54-3)].

*Results and Discussion.* The segmentation results for the BraTS2023 dataset, shown in Table [1,](#page-52-0) indicate that integrating biophysics-informed regularisation with UNet architectures leads to better accuracy than standard methods. Our approach not only improved the Dice coefficients for all tumour regions but also

| Method             | Dice $\uparrow$   | Hausdorff Distance $(mm)$ $\downarrow$ |
|--------------------|-------------------|----------------------------------------|
|                    | TC/WT/ET          | TC/WT/ET                               |
| UNet               | 90.68/92.29/87.28 | 6.16/7.85/10.99                        |
| UNet (Biophy)      | 91.83/92.34/88.26 | 3.99/6.94/7.47                         |
| R2-UNet            | 90.75/91.86/87.29 | 5.82/7.34/11.24                        |
| R2-UNet (Biophy)   | 91.02/91.91/87.53 | 4.83/7.14/9.25                         |
| nn-UNet            | 91.24/92.48/87.38 | 4.44/6.95/10.85                        |
| nn-UNet (Biophy)   | 91.70/92.69/87.55 | 4.00/6.51/10.39                        |
| UNet-TR            | 88.20/90.73/85.41 | 6.75/11.36/12.62                       |
| UNet-TR (Biophy)   | 89.06/91.10/85.96 | 5.97/8.98/11.12                        |
| SegResNet          | 91.96/91.99/87.28 | 3.91/7.53/10.80                        |
| SegResNet-VAE      | 92.00/91.02/87.02 | 3.88/6.80/10.73                        |
| SegResNet (Biophy) | 92.13/92.19/87.46 | 3.62/6.68/10.56                        |

<span id="page-52-0"></span>**Table 1.** Comparison table of UNet family with and without the biophysics-informed regularisation on mean and standard deviation of Dice score and Hausdorff distance.

Image /page/52/Figure/3 description: This image contains four bar charts labeled (a) BC and Activation function, (b) Modality, (c) Trainning Size, and (d) Loss. Chart (a) shows Dice (%) on the y-axis and different metrics (TC, WT, ET) on the x-axis. It compares three methods: PDE+BC+Sine, PDE+Sine, and PDE+BC+ReLU. For TC, PDE+BC+Sine and PDE+Sine both show around 91.9%, while PDE+BC+ReLU shows around 91.1%. For WT, PDE+BC+Sine and PDE+Sine both show around 92.4%, while PDE+BC+ReLU shows around 92.0%. For ET, PDE+BC+Sine shows around 88.3%, PDE+Sine shows around 87.4%, and PDE+BC+ReLU shows around 86.8%. Chart (b) shows Dice (%) on the x-axis and different modalities (T1/T2/FLAIR, T2/FLAIR, T1/FLAIR) on the y-axis. It compares two methods: UNet and UNet+Bio. For T1/T2/FLAIR, UNet+Bio shows around 77.8% and UNet shows around 77.0%. For T2/FLAIR, UNet+Bio shows around 76.8% and UNet shows around 76.3%. For T1/FLAIR, UNet+Bio shows around 77.8% and UNet shows around 76.8%. Chart (c) shows Dice (%) on the x-axis and training sizes (30%, 50%, 70%) on the y-axis. It compares UNet and UNet+Bio. For 30% training size, UNet+Bio shows around 89.4% and UNet shows around 88.6%. For 50% training size, UNet+Bio shows around 89.7% and UNet shows around 89.2%. For 70% training size, UNet+Bio shows around 89.7% and UNet shows around 89.2%. Chart (d) shows Dice (%) on the x-axis and loss functions (Dice+CE, Focal, Jaccard) on the y-axis. It compares UNet and UNet+Bio. For Dice+CE, UNet+Bio shows around 90.1% and UNet shows around 89.7%. For Focal, UNet+Bio shows around 90.0% and UNet shows around 89.5%. For Jaccard, UNet+Bio shows around 90.9% and UNet shows around 90.6%.

<span id="page-52-1"></span>**Fig. 2.** (a) shows the impact of activation functions and boundary conditions on the biophysics-informed UNet performance on Dice score. (b)-(d) shows comparisons of UNet, with and without the biophysics-informed module with mean on Dice score over the 3 regions. (b) explores performance variations using two or three modalities, (c) presents comparisons on different training set sizes, (d) compares different loss combinations.

decreased Hausdorff distances, proving its effectiveness in enhancing segmentation precision and better geometric accuracy in tumour margin prediction. This method also showed consistency across different models, suggesting its robustness. By integrating biophysics into training, we can achieve more reliable segmentations, potentially improving clinical outcomes.

The additional studies depicted in Fig. [2](#page-52-1) present an comprehensive experiments of the biophysics-informed UNet model. In Fig. [2](#page-52-1) (a), the employment of Sine over ReLU activation functions is scrutinised, revealing superior performance in identifying multiple regions, and the inclusion of boundary conditions mitigates excessive predictions in the potential infiltrated area (ET). These findings demonstrate that the biophysics-informed regularisation effectively offers a prior understanding of growth and marginal constraints. Figure [2](#page-52-1) (b) illustrates the model's robustness even with reduced imaging modalities, where using only T1 and FLAIR yields comparable results to including an additional T2 modality.

Image /page/53/Figure/1 description: This image displays a grid of brain MRI scans, each showing a tumor segmentation. The top row includes 'Ground Truth', 'UNet', 'UNet (Biophy)', 'SegResNet', 'SegResNet-VAE', and 'SegResNet (Biophy)'. The bottom row shows 'R2UNet', 'R2UNet(Biophy)', 'UNet-TR', 'UNet-TR (Biophy)', 'nn-UNet', and 'nn-UNet (Biophy)'. A legend on the right indicates that blue represents NCR, green represents ED, and red represents ET. All scans show a tumor with varying degrees of segmentation accuracy across the different models.

<span id="page-53-0"></span>**Fig. 3.** MRI tumour segmentation comparison: All enhanced by biophysics-informed regularisation. The GD-enhancing tumour (ET), the peritumoral edematous tissue (ED), and the necrotic tumour core (NCR). The WT combines red, blue, and green; the TC merges green and blue; and the ET is represented by red. (Color figure online)

Image /page/53/Figure/3 description: The image displays a comparison of segmentation results for brain tumors using different UNet models. The first panel shows the 'Ground Truth' with a brain MRI slice overlaid with segmented tumor regions in green, red, and blue. The subsequent panels present heatmaps generated by UNet and UNet (Biophy) models, visualized at the 'Last layer in encoder (low-resolution)' and 'Last layer in decoder (high-resolution)'. A color bar on the right indicates the intensity scale from 0.0 to 1.0, likely representing confidence or probability scores.

<span id="page-53-1"></span>**Fig. 4.** Localisation in Brain Tumour Segmentation: The images compare the UNet with its biophysics-informed version using GradCAM, revealing more precise tumour localisation. The ground truth scan is shown first, followed by GradCAM visualisations of last encoder and decoder layers, with and without biophysics-informed regularisation.

Training size variations in Fig. [2](#page-52-1) (c) show our efficiency in data-scarce scenarios. Integrating our module into UNet models even outperform standard UNets on larger datasets. Lastly, Fig. [2](#page-52-1) (d) explores different loss functions, including Dice+Cross Entropy, Focal, and Jaccard Loss. The consistent enhancement of UNet's performance, due to biophysics informed regularisation, showcases the module's flexibility. This adaptability enhances network optimisation and performance of segmentation.

Main Comparison Visualisation in Fig. [3](#page-53-0) demonstrates that biophysics informed regularisation notably enhances tumour segmentation in various networks, yielding results that align more closely with ground truth. This method surpasses traditional dice loss and VAE regularisation by capturing tumour boundaries and structures more effectively, showcasing its potential to improve segmentation across different neural architectures.

Interpretability Visualisation in Fig. [4,](#page-53-1) utilising Grad-CAM [\[25\]](#page-56-8), shows how biophysics-informed regularisation refines UNet performance. The focused activation on tumour areas, guided by biophysical tumour characteristics, leads to

precise localisation and enhanced interpretability, ensuring model outputs mirror real tumour pathology for accurate medical segmentation.

## **4 Conclusion**

Our study showcases the effectiveness of biophysics-informed regularisation in improving brain tumour segmentation accuracy and robustness. Integrating tumour growth dynamics with deep learning, we enhance traditional models significantly. Tested on the BraTS 2023 dataset, our method boosts various networks performance, proves robust against data scarcity and varying training losses, and sets a foundation for future exploration of domain-specific knowledge in deep learning for medical imaging, particularly in weakly supervised or unsupervised settings.

**Acknowledgments.** AAR gratefully acknowledges funding from the Cambridge Centre for Data-Driven Discovery and Accelerate Programme for Scientific Discovery, made possible by a donation from Schmidt Futures, ESPRC Digital Core Capability Award, and CMIH and CCIMI, University of Cambridge. This project also was supported with funding from the Oracle for Research Project Award. CBS acknowledges support from the Philip Leverhulme Prize, the Royal Society Wolfson Fellowship, the EPSRC advanced career fellowship EP/V029428/1, EPSRC grants EP/S026045/1 and EP/T003553/1, EP/N014588/1, EP/T017961/1, the Wellcome Innovator Awards  $215733/Z/19/Z$  and  $221633/Z/20/Z$ , CCMI and the Alan Turing Institute.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-54-1"></span>1. Alom, M.Z., Hasan, M., Yakopcic, C., Taha, T.M., Asari, V.K.: Recurrent residual convolutional neural network based on u-net (r2u-net) for medical image segmentation. arXiv preprint [arXiv:1802.06955](http://arxiv.org/abs/1802.06955) (2018)
- <span id="page-54-4"></span>2. Badrinarayanan, V., Kendall, A., Cipolla, R.: Segnet: A deep convolutional encoder-decoder architecture for image segmentation. IEEE transactions on pattern analysis and machine intelligence **39**(12), 2481–2495 (2017)
- <span id="page-54-2"></span>3. Baid, U., Ghodasara, S., Mohan, S., Bilello, M., Calabrese, E., Colak, E., Farahani, K., Kalpathy-Cramer, J., Kitamura, F.C., Pati, S., et al.: The rsna-asnr-miccai brats 2021 benchmark on brain tumor segmentation and radiogenomic classification. arXiv preprint [arXiv:2107.02314](http://arxiv.org/abs/2107.02314) (2021)
- <span id="page-54-0"></span>4. Bakas, S., Akbari, H., Sotiras, A., Bilello, M., Rozycki, M., Kirby, J.S., Freymann, J.B., Farahani, K., Davatzikos, C.: Advancing the cancer genome atlas glioma mri collections with expert segmentation labels and radiomic features. Scientific data **4**(1), 1–13 (2017)
- <span id="page-54-3"></span>5. Carré, A., Deutsch, E., Robert, C.: Automatic brain tumor segmentation with a bridge-unet deeply supervised enhanced with downsampling pooling combination, atrous spatial pyramid pooling, squeeze-and-excitation and evonorm. In: International MICCAI Brainlesion Workshop. pp. 253–266. Springer (2021)

- <span id="page-55-2"></span>6. Çiçek, Ö., Abdulkadir, A., Lienkamp, S.S., Brox, T., Ronneberger, O.: 3d u-net: learning dense volumetric segmentation from sparse annotation. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2016: 19th International Conference, Athens, Greece, October 17-21, 2016, Proceedings, Part II 19. pp. 424– 432. Springer (2016)
- <span id="page-55-13"></span>7. Dice, L.R.: Measures of the amount of ecologic association between species. Ecology **26**(3), 297–302 (1945)
- <span id="page-55-6"></span>8. Hatamizadeh, A., Nath, V., Tang, Y., Yang, D., Roth, H.R., Xu, D.: Swin unetr: Swin transformers for semantic segmentation of brain tumors in mri images. In: International MICCAI Brainlesion Workshop. pp. 272–284. Springer (2021)
- <span id="page-55-7"></span>9. Hatamizadeh, A., Tang, Y., Nath, V., Yang, D., Myronenko, A., Landman, B., Roth, H.R., Xu, D.: Unetr: Transformers for 3d medical image segmentation. In: Proceedings of the IEEE/CVF winter conference on applications of computer vision. pp. 574–584 (2022)
- <span id="page-55-14"></span>10. Huttenlocher, D.P., Klanderman, G.A., Rucklidge, W.J.: Comparing images using the hausdorff distance. IEEE Transactions on pattern analysis and machine intelligence **15**(9), 850–863 (1993)
- <span id="page-55-3"></span>11. Isensee, F., Jaeger, P.F., Kohl, S.A., Petersen, J., Maier-Hein, K.H.: nnu-net: a self-configuring method for deep learning-based biomedical image segmentation. Nature methods **18**(2), 203–211 (2021)
- <span id="page-55-10"></span>12. Lê, M., Delingette, H., Kalpathy-Cramer, J., Gerstner, E.R., Batchelor, T., Unkelbach, J., Ayache, N.: Bayesian personalization of brain tumor growth model. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2015: 18th International Conference, Munich, Germany, October 5-9, 2015, Proceedings, Part II 18. pp. 424–432. Springer (2015)
- <span id="page-55-4"></span>13. Liu, L., Hu, X., Zhu, L., Fu, C.W., Qin, J., Heng, P.A.:  $\psi$ -net: Stacking densely convolutional lstms for sub-cortical brain structure segmentation. IEEE transactions on medical imaging **39**(9), 2806–2817 (2020)
- <span id="page-55-8"></span>14. Liu, L., Huang, Z., Liò, P., Schönlieb, C.B., Aviles-Rivero, A.I.: Pc-swinmorph: Patch representation for unsupervised medical image registration and segmentation. arXiv preprint [arXiv:2203.05684](http://arxiv.org/abs/2203.05684) (2022)
- <span id="page-55-12"></span>15. Liu, L., Jiang, H., He, P., Chen, W., Liu, X., Gao, J., Han, J.: On the variance of the adaptive learning rate and beyond. arXiv preprint [arXiv:1908.03265](http://arxiv.org/abs/1908.03265) (2019)
- <span id="page-55-0"></span>16. Louis, D.N., Perry, A., Reifenberger, G., Von Deimling, A., Figarella-Branger, D., Cavenee, W.K., Ohgaki, H., Wiestler, O.D., Kleihues, P., Ellison, D.W.: The 2016 world health organization classification of tumors of the central nervous system: a summary. Acta neuropathologica **131**(6), 803–820 (2016)
- <span id="page-55-1"></span>17. Low, J.T., Ostrom, Q.T., Cioffi, G., Neff, C., Waite, K.A., Kruchko, C., Barnholtz-Sloan, J.S.: Primary brain and other central nervous system tumors in the united states (2014-2018): A summary of the cbtrus statistical report for clinicians. Neurooncology practice **9**(3), 165–182 (2022)
- <span id="page-55-11"></span>18. Ma, N., et al.: Project-monai/monai: 1.3. 0. zenodo (2023)
- <span id="page-55-9"></span>19. Meaney, C., Das, S., Colak, E., Kohandel, M.: Deep learning characterization of brain tumours with diffusion weighted imaging. Journal of Theoretical Biology **557**, 111342 (2023)
- <span id="page-55-5"></span>20. Milletari, F., Navab, N., Ahmadi, S.A.: V-net: Fully convolutional neural networks for volumetric medical image segmentation. In: 2016 fourth international conference on 3D vision (3DV). pp. 565–571. Ieee (2016)

- <span id="page-56-4"></span>21. Myronenko, A.: 3d mri brain tumor segmentation using autoencoder regularization. In: Brainlesion: Glioma, Multiple Sclerosis, Stroke and Traumatic Brain Injuries: 4th International Workshop, BrainLes 2018, Held in Conjunction with MICCAI 2018, Granada, Spain, September 16, 2018, Revised Selected Papers, Part II 4. pp. 311–320. Springer (2019)
- <span id="page-56-1"></span>22. Oktay, O., Schlemper, J., Folgoc, L.L., Lee, M., Heinrich, M., Misawa, K., Mori, K., McDonagh, S., Hammerla, N.Y., Kainz, B., et al.: Attention u-net: Learning where to look for the pancreas. arXiv preprint  $arXiv:1804.03999$  (2018)
- <span id="page-56-2"></span>23. Ragoza, M., Batmanghelich, K.: Physics-informed neural networks for tissue elasticity reconstruction in magnetic resonance elastography. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 333–343. Springer (2023)
- <span id="page-56-0"></span>24. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2015: 18th International Conference, Munich, Germany, October 5-9, 2015, Proceedings, Part III 18. pp. 234–241. Springer (2015)
- <span id="page-56-8"></span>25. Selvaraju, R.R., Cogswell, M., Das, A., Vedantam, R., Parikh, D., Batra, D.: Gradcam: Visual explanations from deep networks via gradient-based localization. In: Proceedings of the IEEE international conference on computer vision. pp. 618–626 (2017)
- <span id="page-56-6"></span>26. Shanmugam, D., Blalock, D., Balakrishnan, G., Guttag, J.: Better aggregation in test-time augmentation. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 1214–1223 (2021)
- <span id="page-56-5"></span>27. Sitzmann, V., Martel, J., Bergman, A., Lindell, D., Wetzstein, G.: Implicit neural representations with periodic activation functions. Advances in neural information processing systems **33**, 7462–7473 (2020)
- <span id="page-56-3"></span>28. Song, T.A., Chowdhury, S.R., Yang, F., Jacobs, H.I., Sepulcre, J., Wedeen, V.J., Johnson, K.A., Dutta, J.: A physics-informed geometric learning model for pathological tau spread in alzheimer's disease. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2020: 23rd International Conference, Lima, Peru, October 4–8, 2020, Proceedings, Part VII 23. pp. 418–427. Springer (2020)
- <span id="page-56-7"></span>29. Zhang, M., Lucas, J., Ba, J., Hinton, G.E.: Lookahead optimizer: k steps forward, 1 step back. Advances in neural information processing systems **32** (2019)

<span id="page-57-0"></span>Image /page/57/Picture/0 description: A square button with rounded corners, colored in a light gray gradient. At the top of the button, there is a circular icon with a bookmark-like shape inside. The bookmark is gray and appears to be partially unfurled. Below the icon, the text "Check for updates" is displayed in a slightly darker shade of gray, with "Check for" on the first line and "updates" on the second line.

# **Class-Balancing Deep Active Learning with Auto-Feature Mixing and Minority Push-Pull Sampling**

Hongxin Lin<sup>1,2</sup>, Chu Zhang<sup>1,2</sup>, Mingyu Wang<sup>1,4</sup>, Bin Huang<sup>1,2</sup>, Jingjing Shao<sup>3</sup>, Jinxiang Zhang<sup>3</sup>, Zhenhua Gao<sup>3( $\boxtimes$ )</sup>, Xianfen Diao<sup>2( $\boxtimes$ )</sup>, and Bingsheng Huang<sup>1,2( $\boxtimes$ )</sup>

<sup>1</sup> Medical AI Lab, School of Biomedical Engineering, Medical School, Shenzhen University, Shenzhen, China

<EMAIL>

<sup>2</sup> National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, Medical School, Shenzhen University, Shenzhen, China

<EMAIL>

<sup>3</sup> Department of Radiology, The First Affiliated Hospital, Sun Yat-Sen University, Guangzhou, China

<EMAIL>

<sup>4</sup> Ainimal Animal Medical Technology (Shenzhen) Co., Ltd, Shenzhen, China

**Abstract.** Deep neural networks demand large-scale labeled dataset for optimal performance, yet the cost of annotation remains high. Deep active learning (DAL) offers a promising approach to reduce annotation cost while maintaining performance. However, traditional DAL methods often fail to balance performance and computational efficiency, and overlook the challenge posed by class imbalance. To address these challenges, we propose a novel framework, named **C**lass-**B**alancing **D**eep **A**ctive **L**earning(**CB-DAL**), comprising two key modules: auto-mode feature mixing (Auto-FM) and minority push-pull sampling (MPPS). Auto-FM identifies informative samples by simply detecting in inconsistencies in predicted labels after feature mixing, while MPPS mitigates the class imbalance within the selected training pool by selecting candidates whose features close to the minority class centroid while distant from features of the labelled majority class. Evaluated across varying class imbalance ratios and dataset scales, CB-DAL outperforms traditional DAL methods and the counterparts designed for imbalanced dataset. Our method provides a simple yet effective solution to the class imbalance problem in DAL, with broad potential applications.

**Keywords:** Active learning · Class imbalance · Medical image classification

H. Lin and C. Zhang—Contribute equally to this work.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 14–22, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_2)\_2

Image /page/58/Figure/1 description: This figure illustrates the class imbalance problem in Deep Active Learning (DAL) for medical image classification and proposes a solution called Class-Balancing Deep Active Learning (CB-DAL). The top section compares three methods: Uncertainty-based, Diversity-based, and Hybrid. It highlights limitations of existing methods, such as unreliable predictions and substantial computational demands. The proposed CB-DAL method is shown to extract features from labeled and unlabeled samples, identify informative samples by evaluating changes in pseudo-labels with low computational demand, and avoid errors from unreliable predictions. The bottom section visually compares the decision boundaries of the three traditional methods with CB-DAL, showing CB-DAL achieving a better boundary with low CIR. A bar chart compares the CIR after sampling for Random, Uncertainty, Diversity, Hybrid, and CB-DAL (Ours) methods across different CIR ratios (2:1, 3:1, 5:1) before sampling, demonstrating that CB-DAL significantly reduces class imbalance ratio after DAL selection, especially under higher CIR.

<span id="page-58-0"></span>**Fig. 1.** Limitations and solutions for the class imbalance problem in DAL of medical image classification

#### **1 Introduction**

Deep neural networks (DNNs) have achieved great success on medical image analysis [\[13\]](#page-65-0). However, their training heavily relies on large-scale labeled datasets to achieve high performance. Annotating medical image is not only laborious and time-consuming, but requires costly expert effort. To tackle this challenge, deep active learning(DAL) [\[17\]](#page-65-1) is devised to identify and annotate valuable samples from unlabeled data pool. By iteratively training on updated labeled data pool, DAL achieves comparable performance to models trained on fully labelled dataset while reducing annotation costs. Typically, traditional DAL methods utilize criteria such as uncertainty, diversity, or a hybrid approach combining both for sample selection. Nevertheless, they often struggle to strike a balance between optimal performance and computational cost. Although methods solely relying on uncertainty  $[9,11]$  $[9,11]$  or diversity  $[6,19]$  $[6,19]$  $[6,19]$  offer simplicity in implementation, they often yield suboptimal results due to their inherent limitations. Conversely, the state-of-the-art(SOTA) hybrid approach [\[2\]](#page-64-0) exhibits superior performance, but demands substantial computational resources.

However, traditional DAL methods overlook the class distribution bias in datasets, especially for medical image datasets. Furthermore, the sampling bias issue inherent in DAL  $[8,14]$  $[8,14]$  $[8,14]$  leads to a more complex optimization process and suboptimal performance  $[3,21]$  $[3,21]$  $[3,21]$ . Although some studies  $[1,7,10]$  $[1,7,10]$  $[1,7,10]$  $[1,7,10]$  have begun to address the class imbalance issue in DAL. Nevertheless, VaB-AL [\[7](#page-65-9)] and BAL [\[10\]](#page-65-10) resort to difficult-to-train variational autoencoders (VAEs) to model the overall data distribution, substantially increasing computational demands. Moreover, among these works, only BAL [\[10\]](#page-65-10) has been validated on a real-world imbalanced medical dataset.

Rethinking these limitations of existing DAL methods, as depicted in Fig. [1,](#page-58-0) we propose a framework, named Class-Balancing Deep Active Learning (CB-DAL), and validate our method on two real-world imbalanced dataset. Our contributions are: 1) We propose an effective DAL framework to consider uncertainty and class imbalance at the latent feature space. Employing Auto-mode Feature Mixing(Auto-FM) between labeled feature centroids and unlabeled feature representations, we efficiently identify informative samples via evaluating pseudolabel changes. 2) We propose Minority Push-Pull Sampling (MPPS) based on Euclidean distance to select candidates whose features are close to the centroid of the minority class while distant from features of labelled majority class. 3) Combining Auto-FM with MPPS, CB-DAL achieves superior performance than traditional DAL methods and the counterparts designed for imbalanced dataset.

Image /page/59/Figure/2 description: This diagram illustrates a semi-supervised learning framework. The process begins with unlabeled and labeled data feeding into a backbone network. This is followed by an Auto-Feature Mixing stage where features are mixed using a weighted sum of two features, alpha\*z1 + (1-alpha)\*z2, to create new features. The classifier then processes these mixed features, leading to a consistency or inconsistency outcome. Inconsistent samples are selected and fed into the Minority Push-Pull Sampling module. This module involves calculating differences between features (e.g., delta\_zu = |zu\_a - zu\_b|) and ranking them. Based on this ranking, samples are either 'pulled' or 'pushed' towards or away from each other. The Oracle interacts with the labeled data and candidates, influencing the process. The diagram also defines classes as circles and squares, with squares representing labeled data and hollow squares representing unlabeled data. Plus signs denote centroids, and hollow hexagons represent average features.

<span id="page-59-0"></span>**Fig. 2.** Overview of the proposed CB-DAL framework for imbalanced medical datasets

## **2 Methodology**

Inspired by BAL [\[10\]](#page-65-10) and ALFA-Mix [\[15](#page-65-11)], our framework enhances active learning efficacy by explicitly addressing the challenges posed by class imbalance (Fig. [2\)](#page-59-0). It contains two modules: Auto-FM for more effectively selecting informative unlabelled samples, and MPPS for mitigating the impact of class imbalance and sampling bias introducing by traditional active learning.

#### **2.1 Framework Formulation**

In the framework, given a data pool <sup>P</sup>, we allocate a labeled data pool <sup>P</sup>*<sup>L</sup>* and an unlabeled data pool  $P_U$ ,  $P = P_L \cup P_U$  and  $\oslash = P_L \cap P_U$ . We first use the initial  $P_L$ to pretrain the model  $f(\theta)$  for K classes, which parameterized by  $\theta = {\theta_e, \theta_c}$ . Here,  $f_e: \mathcal{X} \to \mathbb{R}^D$  is the backbone encoding the input into a D-dimensional representation in a latent space, *i.e.*  $z = f_e(x; \theta_e)$ . We compute the average representation  $z^*$  of the labelled samples per class. Furthermore,  $f_c : \mathbb{R}^D \to \mathbb{R}^K$ acts as a classifier, mapping unseen samples from their representations to their corresponding logits. For each round, we first maximize the performance of model with  $P_L$ . The subsequent query process can be divided into two stages: In the Auto-FM stage, we select  $P_{S1}$  for informative samples. After that, we introduce MPPS to select  $P_{S2}$  for class balancing. Finally, we get  $P_S = P_{S1} + P_{S2}$ , and then  $P<sub>S</sub>$  is annotated by the oracle and moved from  $P<sub>U</sub>$  to  $P<sub>L</sub>$ . When the performance of  $f_c(\theta)$  reaches a plateau or the budget is exhausted, active learning can be terminated.

#### **2.2 Auto-Feature Mixing**

**Feature Mixing in Auto Mode.** The feature mixing process involves interpolating between the representations of the unlabelled and labelled samples. This is based on the assumption that features from samples near the decision boundary can be easily influenced to predict differently after interpolating.

Firstly, we utilizes  $f_e$  to extract features from all samples, and aggregate the features from labelled data pool for each class by computing the average. Secondly, we mix features extracting from unlabeled samples  $Z_u$  with each average feature  $Z^*$  to obtain new features  $Z_a$ .  $Z_a$  is denoted below:

$$
Z_a = \alpha Z^* + (1 - \alpha) Z^u, \ \ \alpha \in [0, 1)^D
$$
 (1)

Finally,  $Z_a$  is fed into  $f_c$  to obtain updated predictions, if the predictions differ from the original ones, the corresponding unlabeled samples are considered as informative candidates. To optimize the feature mixing process more effectively, we set  $\alpha$  as a learnable Gaussian-like matrix, L1 norm of  $\alpha$  is added into the loss function to mitigate model overfitting. Thereby, Auto-FM simplifies the process of solving for  $\alpha$  and reduces computational demands, while maintaining the high performance advantages of the original approach.

**Variation in The Loss Function After Feature Mixing.** Applying Taylor expansion to calculate the loss for mixed features yields:

$$
\ell(f_c(Z_a), y^*) \approx \ell(f_c(Z^u), y^*) + \left(\alpha \left(Z^* - Z^u\right)^T \cdot \nabla \ell(f_c(Z^u), y^*)\right) \tag{2}
$$

$$
\max\left[\ell\left(f_c\left(Z_a\right), y^*\right) - \ell\left(f_c\left(Z^u\right), y^*\right)\right] \approx \max\left[\left(\alpha\left(Z^* - Z^u\right)^T \cdot \nabla \ell\left(f_c\left(Z^u\right), y^*\right)\right)\right]
$$
\n(3)

The variation in the loss function after feature mixing is influenced by two conditions: 1) the disparity between  $Z^*$  and  $Z_u$ ; 2) the gradient of the loss. The former indicates the distinctiveness of the features and the degree of feature discrepancy between labeled and unlabeled data, while the latter affects the model's sensitivity to features. If there is a difference in labeled and unlabeled data yet this results in only minimal changes in the model's loss gradient, these features may not be distinct enough for the model. Therefore, during training, the final loss function is denoted below:

$$
Loss = 1 - \ell(f(Z^a, y^*)) + ||\alpha|| \tag{4}
$$

#### **2.3 Minority Push-Pull Sampling**

To address the challenges posed by class imbalance and mitigate the negative impact of sampling bias of AL, we introduce Minority Push-Pull Sampling (MPPS). Unlike label-based methods for imbalance, MPPS is a feature-based approach that identifies samples belonging to minority class in latent feature space. By "pulling" features from unlabeled samples while "pushing" them away from the majority class features, MPPS is denoted below:

$$
X_s = argmin\left\{ \ell\left(e\left(x_u\right), \mu\left(c_t^{minor}\right)\right) - \min_{\forall c_t^{maj} \in C_t^{MAJ}} \ell\left(e\left(x_u\right), \mu\left(c_t^{maj}\right)\right) \right\} \tag{5}
$$

 $X_s$  represents the selected samples,  $\ell(\cdot)$  represents the Euclidean distance between two sets of features,  $e(x_u)$  represents the encoding vectors of an unla-<br>beled sample, and  $\mu$  represents the average feature values for a specific class beled sample, and  $\mu$  represents the average feature values for a specific class<br>within the labeled samples  $c^{minor}$  denotes features of minority classes  $c^{maj}$ within the labeled samples.  $c_i^{minor}$  denotes features of minority classes,  $c_i^{maj}$ <br>denotes features of majority classes, and t indicates the current t-th round of denotes features of majority classes, and  $t$  indicates the current  $t$ -th round of iteration.

The expression aims to minimize the difference between the features of the current sample and those of the minority class while maximizing the difference with the majority class in the labeled data pool.

#### **3 Experiments and Results**

#### **3.1 Dataset and Evaluation**

**Bone Tumor Dataset.** This private dataset comprises radiographs obtained from four centers, totaling 333 patients(osteolytic OS:136, GCT:197). Two radiologists, each with over 10 years of experience in reading musculoskeletal radiographs and blinded to the study, independently reviewed all radiographs and selected the patients included in the study. All included bone tumors were pathologically confirmed. We designate the primary center A as the internal dataset(osteolytic OS:124, GCT:155), while the remaining three centers(osteolytic OS:12, GCT:42) serve as the external test set.

**ISIC2020 Dataset.** This public dataset [\[18](#page-65-12)] contains 33,126 images (benign:32542, malignant:584) of benign and malignant skin lesions from 2,056 patients. The dataset was curated by the International Skin Imaging Collaboration (ISIC) and includes images from six different sources. All malignant diagnoses have been confirmed via histopathology, while benign diagnoses have been confirmed using expert agreement, longitudinal follow-up, or histopathology.

We primarily utilize the area under the receiver operating characteristic (AUC) for evaluation.

#### **3.2 Implementation Details**

For Bone Tumor Dataset, all X-ray images are resized to  $1080 \times 1080$ . We randomly split the internal dataset into a training set and an internal test set. Considering its small data scale, we adopt a partial annotation setting at different percentages(50%, 60%, 70%, 80%) of the training set. We randomly selected 30% of each class from the training set to initialize the model, while the remaining data is used for the DAL process. As the class imbalance in the original dataset is not severe enough, We resample the original training set to three imbalanced ratios (2:1, 3:1, 5:1) to validate our method. The batch size is set to 2.

For ISIC2020 Dataset, we follow the setting in [\[22](#page-65-13)]. We randomly divide this dataset into a training set, a validation set and a test set at a ratio of 8: 1: 1. Considering its large data scale, we adopt a partial annotation setting at different percentages( $10\%$ ,  $20\%$ ,  $50\%$ ) of training set. We randomly selected 5% of each class from the training set to initialize the model, while the remaining data is used for AL process. The batch size is set to 128.

For both datasets, in each round, we allocate an equal budget to select the most informative samples and minority class samples. We adopt EfficientNet-B6 with weights pretrained on ImageNet, using its backbone as the feature extractor and fully connected layers as the classifier. Binary Cross Entropy (BCE) loss is employed as the loss function. We employ the Adam optimizer and utilize cosine annealing to reduce the learning rate from 1e–4 to 1e–12 over all 300 epochs. Our method is implemented in Pytorch, using an NVIDIA RTX TITAN GPU with 24 GB memory. The weights used for testing were selected based on the best-performing AUC on the internal test set or validation set.

#### **3.3 Comparison and Ablation Experiments**

To analyze of the effectiveness of Auto-FM, we compare our CB-DAL\* (CB-DAL without MPPS) with other traditional DAL methods, including Entropy [\[20\]](#page-65-14), Core-Set [\[19\]](#page-65-5), BALD [\[11](#page-65-3)], BADGE [\[2](#page-64-0)], and ALFA-Mix ( $\alpha = 0.5$ ) [\[15\]](#page-65-11). The results of these comparison experiments on the original Bone Tumor Dataset are reported in Table [1.](#page-63-0) CB-DAL\* outperforms all traditional DAL methods and random sampling method at each annotation budget, particularly under low budget conditions.

We also analyze the effectiveness of auto-learned  $\alpha$ . As shown in Fig. [3,](#page-63-1) CB- $\text{DAL}^*$  with auto-learned  $\alpha$  demonstrates optimal performance and avoids disadvantages introduced by manual settings.

To address class imbalance problem in DAL, we combine Auto-FM with MPPS, and validate the effectiveness of MPPS on three class imbalance ratios (2:1, 3:1, 5:1) of the Bone Tumor Dataset. With MPPS, CB-DAL demonstrates a significant improvement over CB-DAL\*. To further validate the effectiveness of MPPS, we compare MPPS with other counterparts for class imbalance, such as over-sampling  $[4,16]$  $[4,16]$  and Focal loss  $[12]$  and LADM  $[5]$  $[5]$ . CB-DAL maintains its advantage, especially under high class imbalance ratio. All results of these experiments are shown in Fig. [4](#page-63-2) (Table [2\)](#page-64-5).

| Method         | Annotation Ratio   |                    |                    |                    |                    |
|----------------|--------------------|--------------------|--------------------|--------------------|--------------------|
|                | 50%                | 60%                | 70%                | 80%                | 100%               |
| Full           | /                  | /                  | /                  | /                  | <b>0.935/0.925</b> |
| Random         | 0.521/0.581        | 0.669/0.658        | 0.769/0.776        | 0.834/0.839        | /                  |
| Entropy [20]   | 0.710/0.679        | 0.766/0.806        | 0.802/0.828        | 0.855/0.853        | /                  |
| Core-Set [19]  | 0.748/0.702        | 0.815/0.790        | 0.866/0.857        | 0.877/0.877        | /                  |
| BALD[11]       | 0.753/0.754        | 0.839/0.802        | 0.869/0.827        | 0.893/0.849        | /                  |
| BADGE [2]      | 0.755/0.738        | 0.838/0.816        | 0.862/0.849        | 0.890/0.887        | /                  |
| ALFA-Mix [15]  | 0.783/0.772        | 0.860/0.845        | 0.899/0.889        | 0.918/0.899        | /                  |
| <b>CB-DAL*</b> | <b>0.820/0.800</b> | <b>0.872/0.865</b> | <b>0.902/0.899</b> | <b>0.923/0.903</b> | /                  |

<span id="page-63-0"></span>**Table 1.** Comparison experiments on internal/external test set of Bone Tumor Dataset, CB-DAL\* is our proposed DAL without MPPS for class imbalance

Image /page/63/Figure/3 description: The image displays two line graphs side-by-side, titled "Internal Validation" and "External Validation". Both graphs plot the Area Under the Curve (AUC) on the y-axis against the Annotation Ratio on the x-axis, ranging from 0.5 to 0.8. Each graph shows six lines, each representing a different alpha value (0.1, 0.3, 0.5, 0.7, 0.9, and auto), indicated by different colors and markers. In the "Internal Validation" graph, the AUC generally increases with the annotation ratio for all alpha values. The "Alpha\_auto" line shows the highest AUC values across most annotation ratios, starting around 0.82 at 0.5 and reaching approximately 0.93 at 0.8. The "External Validation" graph shows a similar trend, with AUC increasing with the annotation ratio for all alpha values. Again, "Alpha\_auto" generally performs the best, with AUC values starting around 0.8 at 0.5 and reaching approximately 0.91 at 0.8. The other alpha values show varying performance, with "Alpha\_0.1" generally having the lowest AUC.

<span id="page-63-1"></span>**Fig. 3.** The effectiveness of auto-learned  $\alpha$  on the Bone Tumor Dataset.

Image /page/63/Figure/5 description: This figure contains six line graphs, arranged in a 2x3 grid. The top row displays "Internal Validation" results with ratios of 2:1, 3:1, and 5:1. The bottom row displays "External Validation" results with the same ratios: 2:1, 3:1, and 5:1. All graphs plot "AUC" on the y-axis against "Annotation Ratio" on the x-axis, ranging from 0.5 to 0.8. Each graph shows multiple lines representing different methods: "Full", "Random", "AL\_OS", "AL\_FL", "AL\_LADM", "CB-DAL\*", and "CB-DAL". The "Full" method is represented by a dashed blue line with star markers, consistently showing the highest AUC across all annotation ratios. The "Random" method is shown with a black line and circle markers, generally performing the lowest. The other "AL" and "CB-DAL" methods are plotted with various colored lines and markers, showing performance that generally increases with the annotation ratio and is better than "Random" but lower than "Full".

<span id="page-63-2"></span>**Fig. 4.** The comparison experiments with other class-balancing counterparts and the ablation experiments of CB-BAL on the Bone Tumor Dataset

<span id="page-64-5"></span>

| Method | Annotation Ratio |              |              |              |
|--------|------------------|--------------|--------------|--------------|
|        | 10%              | 30%          | 50%          | 100%         |
| Full   | /                | /            | /            | <b>0.904</b> |
| Random | 0.793            | 0.803        | 0.830        | /            |
| BAL    | 0.834            | 0.873        | 0.876        | /            |
| CB-DAL | <b>0.852</b>     | <b>0.883</b> | <b>0.896</b> | /            |

**Table 2.** Comparison experiments on ISIC2020 Dataset

#### **4 Conclusion**

In this paper, we proposed a simple yet effective framework, CB-DAL, to strike a balance between performance and computational efficiency and solve class imbalance problem in DAL for medical image classification. Based on Auto-FM and MPPS, CB-DAL achieves optimal performance and high efficiency through simple arithmetic operations between features. CB-DAL outperforms traditional DAL methods and counterparts designed for imbalanced datasets across varying class imbalance ratios and data scales, especially when faced with limited resources and high class imbalance.

**Acknowledgments.** This work was funded by Natural Science Foundation of Guangdong Province, China (Grant No.2022A1515011593) and Medical Science, Technology Foundation of Guangdong Province, China (Grant No. A2021010) and Shenzhen Municipal Scheme for Basic Research (JCYJ20210324100208022).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

#### **References**

- <span id="page-64-2"></span>1. Aggarwal, U., Popescu, A., Hudelot, C.: Active Learning for Imbalanced Datasets. In: IEEE Winter Conference on Applications of Computer Vision, pp. 1417-1426 (2020)
- <span id="page-64-0"></span>2. Ash, J.T., Zhang, C., Krishnamurthy, A., Langford, J., Agarwal, A.: Deep Batch Active Learning by Diverse, Uncertain Gradient Lower Bounds. In: International Conference on Learning Representations (2020)
- <span id="page-64-1"></span>3. Bengar, J.Z., van de Weijer, J., Fuentes, L.L., Raducanu, B.: Class-Balancing active learning for image classification. In: Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. pp. 1536-1545 (2022)
- <span id="page-64-3"></span>4. Bria, A., Marrocco, C., Tortorella, F.: Addressing class imbalance in deep learning for small lesion detection on medical images. Computers in Biology and Medicine **120**, 103735 (2020)
- <span id="page-64-4"></span>5. Cao, K., Wei, C., Gaidon, A., Arechiga, N., Ma, T.: Learning Imbalanced Datasets with Label-Distribution-Aware Margin Loss, in: Advances in Neural Information Processing Systems **32**, (2019)

- <span id="page-65-4"></span>6. Cao, X.: A divide-and-conquer approach to geometric sampling for active learning. Expert Systems with Applications **140**, 112907.(2020)
- <span id="page-65-9"></span>7. Choi, J., Yi, K.M., Kim, J., Choo, J., Kim, B., Chang, J., Gwon, Y., Chang, H.J.: VaB-AL: Incorporating Class Imbalance and Difficulty With Variational Bayes for Active Learning. In: Presented at the Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 6749-6758 (2021)
- <span id="page-65-6"></span>8. Dasgupta, S., Hsu, D. Hierarchical sampling for active learning, In: Proceedings of the 25th International Conference on Machine Learning. pp. 208-215. (2008)
- <span id="page-65-2"></span>9. Gal, Y., Ghahramani, Z.: Dropout as a bayesian approximation: Representing model uncertainty in deep learning, In: International Conference on Machine Learning. pp. 1050-1059. (2016)
- <span id="page-65-10"></span>10. Jin, Q., Yuan, M., Wang, H., Wang, M., Song, Z.: Deep active learning models for imbalanced image classification. Knowledge-Based Systems **257**, 109817. (2022)
- <span id="page-65-3"></span>11. Kirsch A, Van Amersfoort J, Gal Y. Batchbald: Efficient and diverse batch acquisition for deep bayesian active learning[J]. Advances in neural information processing systems 32. pp. 7026-7037. (2019)
- <span id="page-65-16"></span>12. Lin, T.-Y. et al.: Focal Loss for Dense Object Detection, In. Proceedings of the IEEE International Conference on Computer Vision, pp. 2980-2988.(2017)
- <span id="page-65-0"></span>13. Litjens, G., Kooi, T., Bejnordi, B.E., Setio, A.A.A., Ciompi, F., Ghafoorian, M., van der Laak, J.A.W.M., van Ginneken, B., Sánchez, C.I.: A survey on deep learning in medical image analysis. Medical Image Analysis **42**, 60-88. (2017)
- <span id="page-65-7"></span>14. MacKay, D.J.: Information-based objective functions for active data selection. Neural computation **4**, 590-604. (1992)
- <span id="page-65-11"></span>15. Parvaneh, A., Abbasnejad, E., Teney, D., Haffari, G. (Reza), van den Hengel, A., Shi, J.Q.: Active Learning by Feature Mixing. In: Presented at the Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 12237-12246. (2022)
- <span id="page-65-15"></span>16. Qu, W., Balki, I., Mendez, M., Valen, J., Levman, J., Tyrrell, P.N.: Assessing and mitigating the effects of class imbalance in machine learning with application to X-ray imaging. Int J Comput Assist Radiol Surg **15**, 2041-2048. (2020)
- <span id="page-65-1"></span>17. Ren, P., Xiao, Y., Chang, X., Huang, P.-Y., Li, Z., Gupta, B.B., Chen, X., Wang, X.: A Survey of Deep Active Learning. ACM Computing Surveys **54**, 180:1-180:40. (2021)
- <span id="page-65-12"></span>18. Rotemberg, V., Kurtansky, N., Betz-Stablein, B., et al.: A patient-centric dataset of images and metadata for identifying melanomas using clinical context. Scientific data, **8**(1), 34. (2021)
- <span id="page-65-5"></span>19. Sener, O., Savarese, S.: Active Learning for Convolutional Neural Networks: A Core-Set Approach. arXiv preprint [arXiv:1708.00489](http://arxiv.org/abs/1708.00489) (2018)
- <span id="page-65-14"></span>20. Shannon, C. E.: A Mathematical Theory of Communication. SIGMOBILE Mob. Comput. Commun. Rev. **5**(1), 3-55. (2001)
- <span id="page-65-8"></span>21. Yang, Y., Xu, Z.: Rethinking the value of labels for improving class-imbalanced learning. Advances in neural information processing systems 33, pp. 19290-19301. (2020)
- <span id="page-65-13"></span>22. Oztürk, S. and Cukur, T.: Deep Clustering via Center-Oriented Margin Free-Triplet Loss for Skin Lesion Detection in Highly Imbalanced Datasets. IEEE Journal of Biomedical and Health Informatics, **26**(9), pp. 4679-4690.(2022)

Image /page/66/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line segment on the left and a flag shape on the right. The button is light gray with a subtle gradient, and the text is dark gray.

# <span id="page-66-0"></span>**CoBooM: Codebook Guided Bootstrapping for Medical Image Representation Learning**

Az[a](http://orcid.org/0000-0002-4078-9400)d Singh<sup>( $\boxtimes$ [\)](http://orcid.org/0000-0002-6607-1130)</sup> and Deepak Mishra<sup>t</sup>

Indian Institute of Technology, Jodhpur, Jodhpur 342307, Rajasthan, India *{*singh.63,dmishra*}*@iitj.ac.in

**Abstract.** Self-supervised learning (SSL) has emerged as a promising paradigm for medical image analysis by harnessing unannotated data. Despite their potential, the existing SSL approaches overlook the high anatomical similarity inherent in medical images. This makes it challenging for SSL methods to capture diverse semantic content in medical images consistently. This work introduces a novel and generalized solution that implicitly exploits anatomical similarities by integrating codebooks in SSL. The codebook serves as a concise and informative dictionary of visual patterns, which not only aids in capturing nuanced anatomical details but also facilitates the creation of robust and generalized feature representations. In this context, we propose *CoBooM*, a novel framework for self-supervised medical image learning by integrating continuous and discrete representations. The continuous component ensures the preservation of fine-grained details, while the discrete aspect facilitates coarse-grained feature extraction through the structured embedding space. To understand the effectiveness of CoBooM, we conduct a comprehensive evaluation of various medical datasets encompassing chest X-rays and fundus images. The experimental results reveal a significant performance gain in classification and segmentation tasks.

**Keywords:** Self-supervised Learning · Codebook · Chest X-ray

## **1 Introduction**

Expensive annotations for medical images promote Self-Supervised Learning (SSL) [\[6](#page-75-0),[8,](#page-75-1)[13](#page-75-2)[,15](#page-75-3)]. Recent developments demonstrate its effectiveness across diverse modalities, such as X-rays, MRIs, CT, and histopathology [\[16](#page-75-4)[,23](#page-76-0)]. However, despite the advancements, existing methods like SimCLR [\[6\]](#page-75-0), MoCo [\[15\]](#page-75-3), BYOL [\[13](#page-75-2)], and VICReg [\[3](#page-75-5)] encounter challenges when applied to medical images, in terms of effectively creating positive and negative pairs. The complexity occurs due to inherent feature overlapping among different anatomical sub-structures and across diverse image samples. Current SSL methods oversee the anatomical overlapping and, thus, potentially compromise the model's performance and generalization capabilities.

In this work, we propose a simple yet effective technique involving learning generalized features guided by a codebook [\[24,](#page-76-1)[32\]](#page-76-2), enabling the capturing of concise discrete features. By associating similar anatomical features with common codes and distinguishing features with distinct codes, the codebook facilitates a structured learning process, which overcomes the challenges associated, such as defining effective positive and negative pairs [\[27](#page-76-3)]. This establishes a systematic representation where recurring patterns are encoded consistently. For instance, the presence of lung fields, ribs, and cardiac contours, common across chest X-rays, may share the same or similar codes, providing a concise and shared representation of prevalent features and creating a sparse but informative summary of the entire dataset. This introduces a strong structured inductive bias by implicitly guiding the SSL model toward making assumptions about the common patterns and structures present.

In this context, we propose an SSL framework named CoBooM: Codebook Guided Bootstrapping for Medical Image Representation Learning. Specifically, CoBooM encompasses a Context and Target Encoders for learning continuous features and a Quantizer module to quantize the features using codebook and integrate them with continuous features using the novel DiversiFuse sub-module. The DiversiFuse sub-module utilizes cross-attention mechanisms that capitalize on the complementary information offered by these two representations. The introduction of the codebook encourages the SSL model to recognize and prioritize the shared generalized common features during the training process. In addition, the complementary integration of the continuous and discrete representations allows the model to capture fine-grained features, contributing to a smooth and rich embedding space. This leads to a more holistic and refined understanding of the underlying data. We conduct experiments across diverse modalities to validate its effectiveness, encompassing chest X-ray and fundus images. We evaluate the proposed approach under linear probing and semi-supervised evaluation protocols and observe more than 3% performance gains in downstream classification and segmentation tasks.

#### **2 Background**

**Discriminative SSL Approaches:** Discriminative SSL has seen advancements with approaches like SimCLR  $[6]$  $[6]$ , MoCo  $[7,15]$  $[7,15]$  $[7,15]$ , BYOL  $[13]$  $[13]$ , Barlow-Twins  $[30]$ , that captures generalized features by enhancing the similarity between positive pairs while maximizing the dissimilarity between negative pairs either explicitly or implicitly. In medical images, discriminative SSL techniques, especially contrastive approaches, have gained substantial attention and found meaningful applicability. Various adaptations of contrastive methods, like MoCo-CXR [\[22\]](#page-76-5), for chest X-rays, MICLe [\[2\]](#page-74-0) using multiple patient images, and MedAug [\[25\]](#page-76-6) with metadata-based positive pair selection, contribute to the improvement of medical image representations. Simultaneously, another approach, DiRA [\[14](#page-75-7)], unites the discriminative, restorative, and adversarial learning to capture the complementary features. Zhou *et al.* propose PCRL [\[34\]](#page-76-7) for X-ray and CT modalities,

later improved with PCRLv2 [\[33](#page-76-8)] addressing pixel-level restoration and scale information. Kaku *et al.* enhance contrastive learning with intermediate-layer closeness in their approach  $[18]$  $[18]$ . In  $[9]$  $[9]$ , SimCLR was used for pre-training on multiple unlabeled histopathology datasets, improving feature quality and superior performance over ImageNet-pretrained networks. In other studies [\[4](#page-75-10),[19\]](#page-75-11), authors showcased the efficacy of different SSL methods on large-scale pathology data. While the existing methods show advancements, however they oversight the significant anatomical similarities in medical data. The proposed approach implicitly harnesses the anatomical similarities to capture more informative features.

**Codebook in Medical Image Analysis:** Using codebook in medical image analysis holds the promising potential [\[12](#page-75-12)]. By discretizing the data, codebooks can simplify complex medical image features, making them easier to analyze [\[20,](#page-76-9)[26](#page-76-10)]. Recent studies [\[11,](#page-75-13)[31](#page-76-11)] highlight the effectiveness of learning discrete representations through codebooks across various domains in achieving interpretable and robust medical image retrieval, generation, recognition, and segmentation.

Image /page/68/Figure/3 description: This is a diagram illustrating a neural network architecture for a generative model. The architecture includes two encoders, 'Target' and 'Context', which process input data 'x' into latent representations 'z\_phi' and 'z\_theta' respectively. The 'Target' encoder uses functions 'f\_phi' and 'g\_phi', while the 'Context' encoder uses 'f\_theta' and 'g\_theta'. An EMA (Exponential Moving Average) connection is shown between the 'Target' and 'Context' encoders. A 'Quantizer' module takes 'y\_phi' and 'y\_d' as input and interacts with a 'Codebook' labeled 'D', containing elements 'e0, e1, e2, ..., ek'. The 'Quantizer' outputs 'y\_d' and 'y\_phi' to a 'DiversiFuse' module. DiversiFuse involves operations with 'q', 'k', and 'v' functions, producing intermediate outputs 'y\_d^q', 'y\_c^k', and 'y\_c^v'. These are combined through multiplication and sigma activation to produce 'y\_dc'. Another multiplication operation combines 'y\_dc' with 'y\_c^v' to yield 'y\_dc\_prime'. The output 'y\_dc\_prime' is then passed to a decoder function 'f\_theta\_prime'. The diagram also shows loss functions 'l1(z\_theta, z\_phi)' and 'l2(z\_theta, z\_prime\_theta)', and a reconstruction loss 'lr(x, x\_prime)' where 'x\_prime' is the final output of the model.

<span id="page-68-0"></span>**Fig. 1.** The architecture overview of the proposed framework. EMA is an exponential moving average used to update the parameters of the Target encoder.  $g_{\theta}$  and  $g_{\phi}$  are the three MLP networks that serve as projection heads for Context and Target encoders.  $f'_{\theta}$  serves as the decoder network.

#### **3 Methodology**

Figure [1](#page-68-0) provides an architectural layout of the proposed SSL framework, comprising a Context encoder parameterized by  $\theta$ , a Target encoder parameterized by  $\phi$  and a Quantizer module. Additionally, two projection heads are denoted

as  $q_{\theta}$  and  $p_{\theta}$  and a decoder  $f_{\theta}'$ . The proposed framework adheres to the self-<br>distillation-based non-contrastive SSL paradigm [13]. The parameters  $\theta$  undergo distillation-based non-contrastive SSL paradigm [\[13\]](#page-75-2). The parameters  $\theta$  undergo updates through back-propagation of the loss, while the parameters  $\phi$  are the earlier version of the  $\theta$ , updated using exponential moving average(EMA). Given an input sample x, it creates two augmented views  $x_1$  and  $x_2$  by applying the random set of augmentations.  $x_1$  is processed by  $f_\phi$  to output feature map  $y_\phi$ while  $f_{\theta}$  produces  $y_{\theta}$  from  $x_2$ . Further,  $y_{\theta}$  and  $y_{\phi}$  after passing through the global average pooling layer, fed to predictor heads  $g_{\theta}$  and  $g_{\phi}$  to output the embeddings  $z_{\theta}$  and  $z_{\phi}$  carrying the global features. Subsequently, the target feature map <sup>y</sup><sup>φ</sup> is quantized through the *Quantizer* module, utilizing a *Codebook* and *DiversiFuse* sub-module to represent and compress the features effectively. The following subsection provides details of the proposed quantization process.

#### **3.1 Quantizer**

The Quantizer module utilizes codebook, a predefined table containing  $K$  discrete codewords represented as vectors  $e_k$ , each of size D. These codewords are employed to quantize the lower-dimensional continuous feature maps  $y_{\phi}$  received from the target encoder  $f_{\phi}$ . The Quantizer module compares the features from  $y_{\phi}$  with each K codewords in the codebook to measure similarity by employing the Euclidean distance. The module identifies the closest codeword to the encoded data through an iterative process across the codebook. Subsequently, the module replaces the continuous encoded data  $y_{\phi}$  with the selected codewords, effectively transforming the representation from continuous to discrete  $y_d$ . This quantization is executed with the objective of minimizing the quantization loss  $\mathcal{L}_q = l_{cb} + \alpha * l_{ce}$  comprising of two terms, codebook loss  $(l_{cb} = ||SG[y_\phi] - e_k||_2^2)$ <br>and the commitment loss  $(l_{cb} - ||y_{tt} - SG[e, ||]^2)$ . Here SG denotes the stopand the commitment loss  $(l_{ce} = ||y_{\phi} - SG[e_k]||_2^2)$ . Here, SG denotes the stop-<br>gradient operator, and  $\alpha$  specifies the weight of *l*. The codebook loss guides gradient operator, and  $\alpha$  specifies the weight of  $l_{ce}$ . The codebook loss guides the adjustment of the codewords  $e_k$  towards  $y_\phi$ . Simultaneously, the commitment loss enforces  $y_{\phi}$  to adhere to specific embeddings in the codebook, thus preventing unregulated expansion.

**DiversiFuse (Feature Fusion with Multi-head Cross Attention):** Within Quantizer, the DiversiFuse sub-module guides the model through discrete representations  $y_d$  in determining which parts of the continuous information  $y_\phi$ are more relevant. It enables the model to learn to focus on different aspects of the continuous representation based on the specific values in the discrete features, potentially capturing more complex patterns and dependencies within the data. It involves a multi-head cross-attention mechanism where the quantized features  $y_d$  pass through q to output  $y_d^q$ , and the continuous features  $y_{\phi}$  pass through k and v to output  $z_c^k$  and  $y_c^v$  respectively. The similarity scores between discrete queries  $y_d^q$  and the continuous keys  $y_c^k$  are calculated as  $S_{Score}(y_d^q, y_c^k) = z_d^q \cdot y_c^{kT}$ . Subsequently, the scores are transformed into atten-<br>tion weights using the softmax function:  $\sigma(S_{G_{\text{max}}}(y_d^q, y_g^k))$  denoted as  $y_d$ . The tion weights using the softmax function:  $\sigma(S_{Score}(y_d^q, y_c^k))$  denoted as  $y_{dc}$ . The<br>continuous values  $y_v^v$  are then weighted by the attention weights  $y_x$ , and summed: continuous values  $y_c^v$  are then weighted by the attention weights  $y_{dc}$  and summed:

 $W_{Sum}(y_{dc}, y_c^v) = \sum y_{dc} \cdot y_c^v$ . The keys  $y_c^k$  help determine which parts of the continuous information should be attended to and the values provide the actual tinuous information should be attended to, and the values provide the actual information to be attended to. The process is repeated for all attention heads. The resulting aggregated representation  $y'_{dc}$  is obtained through concatenation<br>across all attention heads. This integration of discrete and continuous repreacross all attention heads. This integration of discrete and continuous representations enables the exchange of complementary information, enhancing the model's ability to capture complex patterns and improve performance.

#### **3.2 Loss Function**

The output of the Quantizer module, denoted as  $y'_{dc}$ , undergoes an average<br>pooling layer and is subsequently projected into a lower-dimensional space using pooling layer and is subsequently projected into a lower-dimensional space using the projection head  $p_{\theta}$ . The resulting output of  $p_{\theta}$  is denoted as  $z'_{\theta}$ . To optimize<br>the parameters  $\theta$  the similarity scores between  $z_{\theta}$  and  $z_{\theta}$  as well as between  $z_{\theta}$ the parameters  $\theta$ , the similarity scores between  $z_{\theta}$  and  $z_{\phi}$ , as well as between  $z_{\theta}$ and  $z'_{\theta}$ , are calculated using the loss function defined in Eq. [\(1\)](#page-70-0).

<span id="page-70-0"></span>
$$
\mathcal{L}_1 = \frac{\langle z_\theta, z_\phi \rangle}{||z_\theta||_2.||z_\phi||_2}, \mathcal{L}_2 = \frac{\langle z_\theta, z'_\theta \rangle}{||z_\theta||_2.||z'_\theta||_2}
$$
(1)

Additionally,  $y'_{dc}$  also fed to the decoder  $f'_{\theta}$  to output the reconstructed image  $x'$  enabling the model to capture local complementary features, formulated as x', enabling the model to capture local complementary features, formulated as  $\mathcal{L} = ||x - x'||_2$ . The final loss  $L_2 = \alpha(\mathcal{L}_1 + \mathcal{L}_2) + \mathcal{L}_1 + \gamma \mathcal{L}_2$ , where  $\alpha$  and  $\gamma$  set to  $\mathcal{L}_r = ||x - x'||_2$ . The final loss  $L_\theta = \alpha(\mathcal{L}_1 + \mathcal{L}_2) + \mathcal{L}_q + \gamma \mathcal{L}_r$ , where  $\alpha$  and  $\gamma$  set to  $0.5$ . Additionally the symmetric form of the loss  $L_\theta$  is utilized by interchangeably 0.5. Additionally, the symmetric form of the loss  $L_{\theta}$  is utilized by interchangeably feeding the views  $x_1$  and  $x_2$  to  $f_\theta$  and  $f_\phi$ .

## **4 Experimental Setup**

**Descriptions of Datasets:** For pre-training, we utilize a publicly available official train set from NIH-Chest X-ray 14 [\[28\]](#page-76-12) consisting of 86,524 X-ray images and the fundus images from the EyePACS [\[10](#page-75-14)] dataset, have 35,126 samples. The downstream classification task is performed on the officially available test set, with 25,596 samples, and the retinal images from MuReD [\[21\]](#page-76-13) and ODIR  $[17,35]$  $[17,35]$ datasets have 2,208 and 7,000 samples, respectively, with 20% allocated as the test set. To assess the performance for the downstream segmentation task, we utilize the SIIM-ACR [\[1\]](#page-74-1) dataset, consisting of 12,047 samples for pneumothorax detection. We use equal numbers of positive and negative samples and allocate 20% for validation.

**Implementation Details:** We train the models on the Nvidia RTX A6000 with the PyTorch framework. For backbone encoders  $(f_\theta$  and  $f_\phi$ ), we use ResNet18 architecture, with an input image size of  $224\times224$ , batch size of 64, and number of epochs of 300. The number of codebook vectors are 1024, each of size 512. All projection and prediction heads are three-layer MLP networks with an output size of 256. For optimizing the parameters  $\theta$ , we employ LARS [\[29](#page-76-15)] optimization, a base learning rate set at 0.02. Additionally, we implement a cosine decay learning rate scheduler without restarts. Codes are available at [GitHub.](https://github.com/azad6629/coboom/)

**Baselines for Comparison:** To assess the performance of our proposed approach, we compare it with supervised learning, with random initialization (Sup.) and several established SSL methods, encompassing contrastive, non-contrastive, and clustering-based techniques including SimCLR [\[6\]](#page-75-0), BYOL [\[13\]](#page-75-2), VICReg [\[3\]](#page-75-5), SwAV [\[5](#page-75-16)], DiRA [\[14\]](#page-75-7), CAiD [\[23\]](#page-76-0) and PCRLv2 [\[33\]](#page-76-8). Notably, we conduct the pre-training for the baselines following their official implementations and using the same training protocol as our proposed method.

<span id="page-71-0"></span>**Table 1.** Performance evaluation of the proposed approach in terms of AUC score on the NIH, MuRed, and the ODIR datasets, and dice score for the pneumothorax segmentation (SIIM) under linear probing. The best results are bold, SD is not shown due to low variability.

| Methods       | NIH         |             |             |             |             | SIIM        | MuReD       | ODIR        |
|---------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|               | 1%          | 5%          | 10%         | 30%         | All         |             |             |             |
| Sup.          | 51.6        | 55.1        | 57.1        | 61.1        | 61.8        | 48.4        | 58.6        | 56.4        |
| SimCLR        | 56.9        | 59.7        | 62.7        | 67.6        | 70.0        | 50.3        | 72.1        | 70.2        |
| BYOL          | 54.7        | 58.3        | 61.7        | 66.3        | 69.0        | 49.8        | 70.5        | 67.4        |
| SwAV          | 55.5        | 59.1        | 62.4        | 67.7        | 70.2        | 53.4        | 71.6        | 70.8        |
| VICReg        | 58.7        | 60.7        | 62.7        | 66.2        | 67.3        | 48.7        | 72.4        | 66.5        |
| CAid          | 63.7        | 67.2        | 68.9        | 70.3        | 73.5        | 55.3        | 70.7        | 69.5        |
| PCRLv2        | 61.9        | 66.4        | 68.3        | 71.5        | 73.8        | 56.4        | 72.6        | 72.4        |
| DiRA          | 60.8        | 65.8        | 68.6        | 72.6        | 74.1        | 56.8        | 71.7        | 70.8        |
| Ours w/o Dec. | <b>65.1</b> | <b>70.1</b> | <b>72.0</b> | <b>73.6</b> | <b>74.8</b> | 55.6        | 75.8        | <b>76.0</b> |
| Ours w/ Dec.  | 64.9        | <b>70.3</b> | <b>72.4</b> | 73.3        | 74.3        | <b>57.5</b> | <b>76.0</b> | 75.3        |
| Ours w/o DF.  | 63.3        | 68.6        | 70.9        | 72.1        | 73.4        | 54.9        | 74.6        | 73.8        |

## **5 Results and Discussion**

Linear Probing Evaluation: Table [1](#page-71-0) presents the experimental results on NIH, SIIM-ACR, and fundus datasets under linear probing protocol. Specifically, the parameters of encoder  $f_{\theta}$  remain frozen while that of the linear layer gets updated. For NIH, we evaluate the performance by sample labeled subsets from the official train set and report the official test set results in terms of AUC score. Similarly, on MuRed and ODIR datasets, the test set AUC score is reported by evaluating 10% of labeled training data. For pneumothorax segmentation on SIIM-ACR, we report the results in terms of dice score by updating the parameters of the decoder network while that of the encoder remains frozen. Supervised learning (Sup.) notably yields lower AUC scores than the SSL methods. The proposed approach consistently outperforms other baselines across varying degrees of labeled data. Specifically for the 1% subset from NIH, when trained without

the decoder  $(f'_{\theta})$ , our approach achieves the highest AUC score of 65.1% with an average performance gain of more than  $3\%$  from all the baseline methods. When average performance gain of more than **3%** from all the baseline methods. When training the model with  $f'_{\theta}$ , the AUC score is 64.9%, which is comparable to the model's performance  $w/\alpha f'$ . Figure 2 presents the diagnostic maps for different model's performance  $w/o f'_{\theta}$ . Figure [2](#page-72-0) presents the diagnostic maps for different<br>pathological conditions corresponding to the 10% labeled samples from NIH pathological conditions corresponding to the 10% labeled samples from NIH. The diagnostic maps are obtained during the downstream phase with the help of Gardcam using the available ground truth details that include the annotated regions and labels for a subset of NIH samples. A similar trend is observed for MuReD and the ODIR dataset, where the proposed approach outperforms the baselines with a considerable average margin of more than **3%**. This indicates the method's ability to extract meaningful representations from unlabeled data for the subsequent downstream training using limited labeled samples. Furthermore, a similar improvement in AUC scores is observed with increased labeled data. The proposed approach also results in the highest dice score of 57.5% with the decoder on pneumothorax segmentation, with an improvement of  $1\%$ compared to the best-performing baseline.

Image /page/72/Figure/2 description: This image displays a grid of chest X-rays with superimposed heatmaps and bounding boxes, comparing four different methods (Ours, DiRA, PCRLv2, CAiD) across two conditions (Atelec. and Effus. in the left column, Cardio. and Mass in the right column). Each row represents a different condition, and each column within a condition represents a different method. The heatmaps highlight areas of interest on the X-rays, and the bounding boxes further delineate specific regions. For 'Atelec.', the 'Ours' method shows a small box on the right side of the heart, while 'DiRA' shows a box slightly higher and more central. 'PCRLv2' and 'CAiD' both show larger, more diffuse heatmaps over the heart region with smaller boxes. For 'Effus.', all methods show heatmaps and boxes concentrated on the lower left lung area. For 'Cardio.', all methods show significant heatmaps over the entire heart region, with 'Ours', 'DiRA', 'PCRLv2', and 'CAiD' all displaying bounding boxes encompassing the heart. For 'Mass', the 'Ours' and 'DiRA' methods show small boxes on the right side of the chest, with diffuse heatmaps. 'PCRLv2' and 'CAiD' show larger heatmaps and bounding boxes in the central and right lung areas.

<span id="page-72-0"></span>**Fig. 2.** Diagnostic maps for Atelectasis, Effusion, Cardiomegaly, and Mass corresponding to the X-ray images from NIH indicate that CoBooM captures pathological features effectively compared to other best-performing baseline methods. The bounding box indicates the ground truth.

**Semi-supervised Evaluation:** Table [2](#page-73-0) presents the test/val set performance of the baseline methods and the proposed approach under the semi-supervised evaluation protocol. We present the performance evaluation in terms of AUC score on the NIH, MuReD, and ODIR and dice score on SIIM-ACR by finetuning the backbone encoder  $f_{\theta}$  along with the linear layer using various subsets of labeled data extracted from the training samples. We observe consistently superior performance of the proposed approach over existing SSL methods across all the subsets. Notably, our method  $(w / o f'_{\theta})$  achieves the highest AUC score<br>of 65.8% with 1% of the training samples surpassing the baselines by a margin of 65.8%, with 1% of the training samples surpassing the baselines by a margin exceeding 2%. When pre-trained w/  $f'_{\theta}$ , the AUC score is almost similar w/o the  $f'$ . The trand persists as the labeled data increases to 100%, with the proposed  $f'_{\theta}$ . The trend persists as the labeled data increases to 100%, with the proposed approach consistently outperforming the baselines and maintaining an average approach consistently outperforming the baselines and maintaining an average gain of 2%. MuRed and ODIR datasets have a similar performance gain, with the

<span id="page-73-0"></span>

| Methods       | NIH         |             |             |             |             | SIIM        | MuReD       | ODIR        |
|---------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|               | 1%          | 5%          | 10%         | 30%         | All         |             |             |             |
| SUP.          | 57.7        | 62.7        | 65.6        | 70.7        | 74.1        | 51.2        | 66.7        | 63.2        |
| SimCLR        | 62.1        | 65.7        | 68.9        | 72.2        | 75.6        | 53.3        | 80.9        | 73.4        |
| <b>BYOL</b>   | 61.0        | 65.2        | 67.7        | 71.6        | 74.8        | 52.8        | 78.6        | 71.3        |
| SwAV          | 61.7        | 65.6        | 66.9        | 72.1        | 75.8        | 54.4        | 79.4        | 72.7        |
| <b>VICReg</b> | 60.0        | 64.8        | 68.4        | 71.8        | 75.4        | 54.4        | 78.3        | 72.9        |
| CAiD          | 64.4        | 69.6        | 71.3        | 73.8        | 77.4        | 56.5        | 81.0        | 73.1        |
| PCRLv2        | 63.0        | 68.7        | 70.6        | 73.1        | 76.1        | 57.3        | 82.4        | 74.6        |
| DiRA          | 62.7        | 67.3        | 71.2        | 74.5        | 77.8        | 58.8        | 81.6        | 73.4        |
| Ours w/o Dec. | <b>65.8</b> | <b>70.6</b> | <b>72.3</b> | <b>76.7</b> | <b>79.6</b> | 57.8        | 84.4        | <b>75.8</b> |
| Ours w/ Dec.  | 65.6        | <b>70.8</b> | <b>72.1</b> | <b>77.1</b> | <b>79.3</b> | <b>59.6</b> | <b>84.8</b> | 75.7        |
| Ours w/o DF.  | 63.7        | <b>70.0</b> | <b>72.2</b> | <b>76.3</b> | <b>78.9</b> | <b>57.1</b> | <b>83.1</b> | 74.2        |

**Table 2.** Semi-supervised fine-tuning evaluation in terms of AUC score (%) on the NIH, MuRed, and the ODIR datasets, and dice score for the pneumothorax segmentation.

highest AUC scores of 84.4 and 75.8 ( $w/o f'_{\theta}$ ), respectively. For pneumothorax<br>segmentation also, we observe the highest dice score of 59.6% with a margin segmentation also, we observe the highest dice score of 59.6% with a margin of more than 2% compared to the best-performing baseline method. Further, a paired t-test comparing our model with the best baseline method, DiRA, on the SIIM dataset yielded a significant p-value of 0.012, indicating performance differences.

**Optimal Performance with Minimal Fine-Tuning:** Upon comparing the results presented in Table [1](#page-71-0) and [2,](#page-73-0) a noteworthy observation is that our proposed method demonstrates minimal or no need for fine-tuning of the backbone encoder, especially with lower numbers of labeled training samples. Specifically, at 1%, the proposed method achieves AUC scores of 65.1% and 65.8% under the linear-probing and semi-supervised fine-tuning evaluation protocols, respectively. Similarly, for 5% and 10% labeled training samples, our method's AUC scores remain comparable with negligible margins. This trend contrasts baseline methods, where a substantial performance gain is observed from linear probing to semi-supervised fine-tuning. This highlights the effectiveness of our proposed method while demonstrating a remarkable capacity to achieve optimal performance with minimal fine-tuning to adapt to different tasks. This signifies the proposed approach's adaptability and highlights its potential to derive meaningful and transferable representations with minimal fine-tuning, which aligns with the practical requirements of real-world settings where computational resources may be limited.

**Ablation Studies:** We conduct an ablation study to examine the impact of different components of the proposed approach under both linear probing and semi-supervised evaluation protocols. In our first study, we evaluate the model's performance by performing the pre-training, with and without the decoder, by keeping the DiversiFuse module. We pre-train the model without the Diversi-Fuse module and the decoder for another study. Table [1](#page-71-0) and [2](#page-73-0) present the test set results across various downstream tasks for these studies. We observe no effect of the decoder on the model's performance during classification tasks in the downstream evaluations. The results are comparable w/ and w/o the decoder. However, while evaluating the performance on the segmentation task, we observed superior performance when pre-training the model with the decoder under both evaluation protocols. By reconstructing the input image from the output of the DiversiFuse sub-module, the decoder encourages the model to focus on capturing fine-grained details, which are critical for segmentation. When pre-train the model without the DiversiFuse sub-module in the Quantizer, we observe a decline of around 2% across all tasks on evaluating the model's performance under linear probing. Under semi-supervised evaluation, the model can maintain its performance even without the DiversiFuse sub-module; however, for classification with 1% labeled samples from NIH, we observe a degradation in the AUC score of 2%. This highlights the importance of the DiversiFuse sub-module in improving the quality of the learned representations with the help of discrete features.

## **6 Conclusion**

In this work, we propose an efficient SSL pre-training by integrating the discrete and continuous features with the help of a codebook. We propose a novel DiversiFuse sub-module, which guides the model in learning generalized and better representation and does not require much fine-tuning, especially when labeled data is limited. We highlight the proposed model's ability to capture complex medical attributes with limited resource availability through empirical studies. We evaluate the performance of the proposed approach by comparing it with various SSL methods under both linear probing and semi-supervised evaluations for both classification and segmentation tasks. This highlights its effectiveness in handling various tasks associated with medical image analysis.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-74-1"></span>1. Society for imaging informatics in medicine: Siim-acr pneumothorax segmentation (2019), [https://www.kaggle.com/c/siim-acr-pneumothorax-segmentation/](https://www.kaggle.com/c/siim-acr-pneumothorax-segmentation/overview/description) [overview/description](https://www.kaggle.com/c/siim-acr-pneumothorax-segmentation/overview/description)
- <span id="page-74-0"></span>2. Azizi, S., Mustafa, B., Ryan, F., Beaver, Z., Freyberg, J., Deaton, J., Loh, A., Karthikesalingam, A., Kornblith, S., Chen, T., et al.: Big self-supervised models advance medical image classification. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 3478–3488 (2021)

- <span id="page-75-5"></span>3. Bardes, A., Ponce, J., Lecun, Y.: Vicreg: Variance-invariance-covariance regularization for self-supervised learning. In: International Conference on Learning Representations (2022)
- <span id="page-75-10"></span>4. Boyd, J., Liashuha, M., Deutsch, E., Paragios, N., Christodoulidis, S., Vakalopoulou, M.: Self-supervised representation learning using visual field expansion on digital pathology. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 639–647 (2021)
- <span id="page-75-16"></span>5. Caron, M., Misra, I., Mairal, J., Goyal, P., Bojanowski, P., Joulin, A.: Unsupervised learning of visual features by contrasting cluster assignments. Advances in Neural Information Processing Systems **33**, 9912–9924 (2020)
- <span id="page-75-0"></span>6. Chen, T., Kornblith, S., Norouzi, M., Hinton, G.: A simple framework for contrastive learning of visual representations. In: International conference on machine learning. pp. 1597–1607. PMLR (2020)
- <span id="page-75-6"></span>7. Chen, X., Fan, H., Girshick, R., He, K.: Improved baselines with momentum contrastive learning. arXiv preprint [arXiv:2003.04297](http://arxiv.org/abs/2003.04297) (2020)
- <span id="page-75-1"></span>8. Chen, X., He, K.: Exploring simple siamese representation learning. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 15750–15758 (2021)
- <span id="page-75-9"></span>9. Ciga, O., Xu, T., Martel, A.L.: Self supervised contrastive learning for digital histopathology. Machine Learning with Applications **7**, 100198 (2022)
- <span id="page-75-14"></span>10. Dugas, E., Jared, Jorge, Cukierski, W.: Diabetic retinopathy detection (2015), <https://kaggle.com/competitions/diabetic-retinopathy-detection>
- <span id="page-75-13"></span>11. Gangloff, H., Pham, M.T., Courtrai, L., Lefèvre, S.: Leveraging vector-quantized variational autoencoder inner metrics for anomaly detection. In: 2022 26th International Conference on Pattern Recognition (ICPR). pp. 435–441. IEEE (2022)
- <span id="page-75-12"></span>12. Gorade, V., Mittal, S., Jha, D., Bagci, U.: Synergynet: Bridging the gap between discrete and continuous representations for precise medical image segmentation. In: Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. pp. 7768–7777 (2024)
- <span id="page-75-2"></span>13. Grill, J.B., Strub, F., Altché, F., Tallec, C., Richemond, P., Buchatskaya, E., Doersch, C., Avila Pires, B., Guo, Z., Gheshlaghi Azar, M., et al.: Bootstrap your own latent-a new approach to self-supervised learning. Advances in neural information processing systems **33**, 21271–21284 (2020)
- <span id="page-75-7"></span>14. Haghighi, F., Taher, M.R.H., Gotway, M.B., Liang, J.: Dira: Discriminative, restorative, and adversarial learning for self-supervised medical image analysis. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 20824–20834 (2022)
- <span id="page-75-3"></span>15. He, K., Fan, H., Wu, Y., Xie, S., Girshick, R.: Momentum contrast for unsupervised visual representation learning (2020)
- <span id="page-75-4"></span>16. Huang, S.C., Pareek, A., Jensen, M., Lungren, M.P., Yeung, S., Chaudhari, A.S.: Self-supervised learning for medical image classification: a systematic review and implementation guidelines. NPJ Digital Medicine **6**(1), 74 (2023)
- <span id="page-75-15"></span>17. kaggle: Ocular disease recognition, [https://www.kaggle.com/andrewmvd/ocular](https://www.kaggle.com/andrewmvd/ocular-disease-recognition-odir5k)[disease-recognition-odir5k](https://www.kaggle.com/andrewmvd/ocular-disease-recognition-odir5k)
- <span id="page-75-8"></span>18. Kaku, A., Upadhya, S., Razavian, N.: Intermediate layers matter in momentum contrastive self supervised learning. Advances in Neural Information Processing Systems **34**, 24063–24074 (2021)
- <span id="page-75-11"></span>19. Kang, M., Song, H., Park, S., Yoo, D., Pereira, S.: Benchmarking self-supervised learning on diverse pathology datasets. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3344–3354 (2023)

- <span id="page-76-9"></span>20. Kobayashi, K., Hataya, R., Kurose, Y., Miyake, M., Takahashi, M., Nakagawa, A., Harada, T., Hamamoto, R.: Decomposing normal and abnormal features of medical images for content-based image retrieval of glioma imaging. Medical image analysis **74**, 102227 (2021)
- <span id="page-76-13"></span>21. Rodríguez, M.A., AlMarzouqi, H., Liatsis, P.: Multi-label retinal disease classification using transformers. IEEE Journal of Biomedical and Health Informatics (2022)
- <span id="page-76-5"></span>22. Sowrirajan, H., Yang, J., Ng, A.Y., Rajpurkar, P.: Moco pretraining improves representation and transferability of chest x-ray models. In: Medical Imaging with Deep Learning. pp. 728–744. PMLR (2021)
- <span id="page-76-0"></span>23. Taher, M.R.H., Haghighi, F., Gotway, M.B., Liang, J.: Caid: Context-aware instance discrimination for self-supervised learning in medical imaging. In: International Conference on Medical Imaging with Deep Learning. pp. 535–551. PMLR (2022)
- <span id="page-76-1"></span>24. Van Den Oord, A., Vinyals, O., et al.: Neural discrete representation learning. Advances in neural information processing systems **30** (2017)
- <span id="page-76-6"></span>25. Vu, Y.N.T., Wang, R., Balachandar, N., Liu, C., Ng, A.Y., Rajpurkar, P.: Medaug: Contrastive learning leveraging patient metadata improves representations for chest x-ray interpretation. In: Machine Learning for Healthcare Conference. pp. 755–769. PMLR (2021)
- <span id="page-76-10"></span>26. Wang, J., Han, X.H., Xu, Y., Lin, L., Hu, H., Jin, C., Chen, Y.W., et al.: Sparse codebook model of local structures for retrieval of focal liver lesions using multiphase medical images. International journal of biomedical imaging **2017** (2017)
- <span id="page-76-3"></span>27. Wang, J., Zeng, Z., Chen, B., Dai, T., Xia, S.T.: Contrastive quantization with code memory for unsupervised image retrieval. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 36, pp. 2468–2476 (2022)
- <span id="page-76-12"></span>28. Wang, X., Peng, Y., Lu, L., Lu, Z., Bagheri, M., Summers, R.M.: Chestx-ray8: Hospital-scale chest x-ray database and benchmarks on weakly-supervised classification and localization of common thorax diseases. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 2097–2106 (2017)
- <span id="page-76-15"></span>29. You, Y., Gitman, I., Ginsburg, B.: Large batch training of convolutional networks. arXiv preprint [arXiv:1708.03888](http://arxiv.org/abs/1708.03888) (2017)
- <span id="page-76-4"></span>30. Zbontar, J., Jing, L., Misra, I., LeCun, Y., Deny, S.: Barlow twins: Self-supervised learning via redundancy reduction. In: International conference on machine learning. pp. 12310–12320. PMLR (2021)
- <span id="page-76-11"></span>31. Zhang, Y., Sun, K., Liu, Y., Ou, Z., Shen, D.: Vector quantized multi-modal guidance for alzheimer's disease diagnosis based on feature imputation. In: International Workshop on Machine Learning in Medical Imaging. pp. 403–412. Springer (2023)
- <span id="page-76-2"></span>32. Zheng, C., Vedaldi, A.: Online clustered codebook. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 22798–22807 (2023)
- <span id="page-76-8"></span>33. Zhou, H.Y., Lu, C., Chen, C., Yang, S., Yu, Y.: A unified visual information preservation framework for self-supervised pre-training in medical image analysis. IEEE Transactions on Pattern Analysis and Machine Intelligence (2023)
- <span id="page-76-7"></span>34. Zhou, H.Y., Lu, C., Yang, S., Han, X., Yu, Y.: Preservational learning improves self-supervised medical image models by reconstructing diverse contexts. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 3499–3509 (2021)
- <span id="page-76-14"></span>35. Zhou, Y., Wang, B., Huang, L., Cui, S., Shao, L.: A benchmark for studying diabetic retinopathy: segmentation, grading, and transferability. IEEE Transactions on Medical Imaging **40**(3), 818–828 (2020)

<span id="page-77-0"></span>Image /page/77/Picture/0 description: A square button with rounded corners contains a circular icon and text. The icon is a circle with a ribbon or bookmark shape inside it. The text below the icon reads "Check for updates".

# **Continual Domain Incremental Learning for Privacy-Aware Digital Pathology**

Pratibha Kumari<sup>1( $\boxtimes$ </sup>)  $\Box$ [,](http://orcid.org/0000-0003-1103-9077) Daniel Reisenbüchler<sup>1</sup> $\Box$ , Lucas Luttner<sup>1</sup> $\Box$ , Nadine S. Schaadt<sup>2</sup>  $\bullet$ [,](http://orcid.org/0000-0002-1234-982X) Friedrich Feuerhake<sup>2</sup>  $\bullet$ , and Dorit Merhof<sup>1,[3](http://orcid.org/0000-0002-1672-2185)</sup>  $\bullet$ 

<sup>1</sup> Faculty of Informatics and Data Science, University of Regensburg, Regensburg, Germany

<EMAIL>

<sup>2</sup> Institute of Pathology, Hannover Medical School, Hannover, Germany <sup>3</sup> Fraunhofer Institute for Digital Medicine MEVIS, Bremen, Germany

**Abstract.** In recent years, there has been remarkable progress in the field of digital pathology, driven by the ability to model complex tissue patterns using advanced deep-learning algorithms. However, the robustness of these models is often severely compromised in the presence of data shifts (e.g., different stains, organs, centers, etc.). Alternatively, continual learning (CL) techniques aim to reduce the forgetting of past data when learning new data with distributional shift conditions. Specifically, rehearsal-based CL techniques, which store some past data in a buffer and then replay it with new data, have proven effective in medical image analysis tasks. However, privacy concerns arise as these approaches store past data, prompting the development of our novel Generative Latent Replay-based CL (GLRCL) approach. GLRCL captures the previous distribution through Gaussian Mixture Models instead of storing past samples, which are then utilized to generate features and perform latent replay with new data. We systematically evaluate our proposed framework under different shift conditions in histopathology data, including stain and organ shift. Our approach significantly outperforms popular buffer-free CL approaches and performs similarly to rehearsal-based CL approaches that require large buffers causing serious privacy violations.

**Keywords:** Continual learning · Digital pathology · Domain shift

## **1 Introduction**

Rapid advancements in deep learning models have revolutionized digital pathology. However, such models are mostly validated in stationary environments

P. Kumari and D. Reisenbüchler—Equal Contribution.

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_4) 4.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 34–44, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_4)\_4

Image /page/78/Figure/1 description: The image displays a series of immunohistochemistry stained tissue samples. The top row is labeled "Stain-shift" and shows samples stained with HE, CD8, TRI, CK5/14, and PAS. The bottom row is labeled "Organ-shift" and shows samples from Breast, Colon, Liver, Ovary, and Stomach, all stained with an unspecified stain that results in a pale blue or brown coloration.

<span id="page-78-0"></span>**Fig. 1.** Example of domain-shift in digital pathology. *Left*: different stainings of breast samples. *Right*: Hematoxylin samples of different organs for tumor detection.

where data is presumed to follow a static distribution, which is usually not the case in clinical settings. Histopathology slides originating from different organs, staining protocols, centers, etc. possess various levels of covariance shifts [\[10\]](#page-86-0) (see examples in Fig. [1\)](#page-78-0). However, deep learning models would show a drastic performance degradation on datasets that do not follow the data distribution initially used for training the model [\[13\]](#page-86-1). On the other hand, naively updating the model with training data of a new domain may cause disruption in the previously learned domain, also known as catastrophic forgetting [\[17\]](#page-86-2). One possible solution to prevent catastrophic forgetting is to retrain the deployed model on accumulated datasets of both past and new domains. However, sharing past datasets is often not feasible with medical data due to privacy concerns [\[24\]](#page-87-0). Also, past data may no longer be accessible, which would also hamper federated learning approaches. Further, such approaches demand considerable amounts of computation time and memory to store datasets and would require full model retraining each time distribution shifts in the dataset are encountered.

Alternatively, Continual Learning (CL) has emerged as a promising incremental learning paradigm to avoid catastrophic forgetting [\[7](#page-86-3)]. CL sequentially accumulates knowledge over a stream of datasets, frequently referred to as tasks, each with possible shifts, without truly revisiting the previous task. There are various CL strategies detailed in [\[18\]](#page-87-1), all aiming to reduce catastrophic forgetting and tackle performance drops. Medical imaging researchers have also started exploring these CL strategies for various medical image analysis applications that are prone to encounter domain shifts and novel classes, including segmentation, disease classification, drug discovery, etc. [\[18](#page-87-1)]. In digital pathology, Bandi et al. [\[1](#page-85-0)] provide a comparative study of existing CL strategies on cancer detection datasets, reflecting shifts among three organs (breast, colon, and head-neck). Similarly, Kaustaban et al. [\[15](#page-86-4)] simulate five domain incremental datasets by changing stain composition in a colon cancer dataset (CRC) and benchmark existing CL approaches. Some other works [\[7](#page-86-3)[,29](#page-87-2)] benchmark popular CL approaches on less complex datasets such as MedMNIST. Further CL research in digital pathology indicates that rehearsal-based strategies that require storing some past samples in a memory buffer tend to perform better compared to others  $[1,15]$  $[1,15]$ . However, storing past samples may cause privacy violations [\[12](#page-86-5)], which can be a major bottleneck of such CL approaches in medical applications. A less concerning direction, storing features instead of actual images, refereed as latent-replay (e.g., chest x-ray classification [\[26](#page-87-3)], Ultrasound cardiac view classification [\[24](#page-87-0)]) is also explored. Besides the promising results from current research, such a strategy is compromised by complexities in sharing features across hospitals and large buffer requirements.

Image /page/79/Figure/1 description: This diagram illustrates a machine learning model for domain adaptation in medical imaging. The model processes 'Past domains', 'Current domain', and 'Future domains', each represented by stacks of histology images. A frozen low-level generic feature extractor processes the current domain's images, generating features (f1 to fn). These features are concatenated with generated features for latent replay from past domains. The combined features are then fed into a domain-specific discriminative feature extractor, consisting of fully connected (FC) layers and a classifier. The extracted features are accumulated temporarily and used to fit a Gaussian Mixture Model (GMM). The GMM is then updated and stored in a GMM-pool, which also contains GMMs from past domains. This process allows the model to adapt to new domains while retaining knowledge from previous ones.

<span id="page-79-0"></span>**Fig. 2.** Overview of proposed CL framework for digital pathology

To address these limitations related to the sharing of medical data in histopathology, we propose a novel, buffer-free CL approach that takes advantage of a Gaussian Mixture Model (GMM) to encapsulate the traits of past training domains, enabling feature generation for incorporation in subsequent training sessions. We acquire real-world histological slides from different environments and curate domain-incremental scenarios to show shifts in terms of stain, organ, center, or a mix of those. An extensive evaluation is performed on three different domain shift problems in digital pathology, i.e., (1) breast tumor detection across different histological stains, (2) tumor detection across different organs, and (3) tumor detection in the presence of heterogeneous types of shifts. For comparison, we consider established buffer-free and buffer-based CL approaches.

### **2 Method**

In the following, we provide the problem formulation and introduce the two key novelties of our proposed CL framework: (a) generative latent replay and (b) domain specific generators. Figure [2](#page-79-0) serves as overview of our proposed framework.

**Problem Formulation:** The goal of GLRCL is to learn sequentially from datasets containing domain shifts and without storing any samples from past datasets while maintaining performance over previous datasets. Such an incremental learning setting where the classification task is fixed, but domain shift is observed over time, is referred to as continual learning under domain incremental scenarios. Each dataset in the sequence is termed as a CL **task** here. Formally, there is a sequence of tasks  $\{T_1, T_2, \ldots\}$ , where each task  $T_t$  has training  $D_t$  and evaluation  $E_t$  set for tumor classification. Here, the training set  $D_t = \{x_t, y_t\}$  contains  $x_t$ patches extracted from Whole Slide Images (WSIs) and corresponding  $y_t$  annotations for  $\{1, \ldots C\}$  classes. The training data of  $t^{th}$  task offers samples for learning in<br>the current session ( $t^{th}$  domain) only and is not accessible in past or future training the current session  $(t^{th}$  domain) only and is not accessible in past or future training sessions. Meanwhile, the testing data from each task is available throughout the sessions. The CL classification model  $\mathbb{M}_t(\cdot)$  is initialized with model parameters  $\theta_{t-1}$  learned during the immediate past session. M<sub>t</sub>( $\cdot$ ) then aims to mitigate catastrophic forgetting, by learning the model parameters  $\theta_t$  that minimize the loss  $(L)$ over all the past domains. In the proposed GLRCL framework,  $\mathbb{M}_t(\cdot)$  learns jointly on the training data from the current domain  $D_t$  and latent vectors produced from a pool of generators representing past domains.

**a) Generative Latent Replay:** The early layers of deep learning models are responsible for extracting low-level features from input images. After undergoing pre-training on large datasets, these layers' weights become stable and can be effectively reused in various applications, including medical image processing tasks. On the other hand, layers close to the classification head tend to extract discriminative features tailored to specific classes and domains, and fine-tuning them is often essential for maximizing accuracy. We propose to extract rich lowlevel features from an early layer, coined as generative-replay-layer, and learn generators from these activations. Thus, rather than storing input histopathology samples in an external memory in raw or latent form; we just store the generators and hence avoid possible privacy violation. At the arrival of any new domain, separate generators are learned on tumor and healthy images and then the pool containing generators is updated with these newly learned generators. These generators then can be used to produce latent representations mimicking past domains which serve as replay features. In each Adam-based mini-batch training, patterns coming from the input layer are concatenated with the replay features at the generative-replay-layer on the mini-batch dimension.

$$
\arg\min_{\theta_t} \boldsymbol{L}\left(\mathbb{M}_{t(\bar{\theta},\theta_t)}((x_t,y_t)\oplus(\mathbb{G}_{t-1}(\mathbf{f}_{t-1}),y_{t-1})\oplus\ldots),\forall t>0\right)
$$

where,  $\bar{\theta}$  refers to freezed parameters,  $\mathbb{G}_{t-1}(\cdot)$  represents  $(t-1)^{th}$  domain generator, and ⊕ indicates concatenation operation. To uphold the stability of the generated features and the validity of stored generators, we propose freezing all layers beneath the generative-replay-layer and allowing the layers above to learn autonomously, i.e., the forward and backward passes are performed only on layers after generative-replay-layer. Our approach offers applicability to a wide range of encoder models. We use a pre-trained ResNet50 [\[11](#page-86-6)] as our feature extraction model. Then fully connected layers and a classification head are added.

**b) Domain Specific Generators:** For each of the encountered domains having a unique tuple of attributes in set {stain, organ, center}, we propose to maintain light-weight generators, separately for each class. The generators are learned from extracted image features and hence cannot be used to produce raw images, ensuring data privacy. Moreover, this opens an avenue for restricted data-sharing in terms of distribution parameter sharing. We utilize GMMs as the feature generators. GMMs are well known for their diverse range of data modeling capabilities, also evidenced by their popularity in encoding latent features in deep layers  $[2,27]$  $[2,27]$  $[2,27]$ , encoding raw images  $[23]$ , features  $[28,30]$  $[28,30]$  $[28,30]$ , etc. Further, they typically require fewer parameters (only mean and variance) compared to any deep neural network-based generators which would otherwise require storing the entire model weights. The cost-effective generators are particularly advantageous, especially in CL scenarios involving multiple distinct domains that require a large number of generators. Specifically, during training, we extract features from the last batch normalization layers of the ResNet50 model. Then, at the end of each training session, the accumulated features  $\mathbf{f}_t \leftarrow BN(x_t)$  are used to learn domain-specific GMMs, for each class. For  $t^{th}$  domain and  $i^{th}$  class, the generator  $\mathbb{G}_t^i(\cdot)$  posses K multivariate Gaussians  $\{g_1, g_2...g_K\}$  in the mixture with probability density function (PDF) as <sup>th</sup> domain and *i*<sup>th</sup> class, the<br> $\{a_1, a_2, a_3\}$  in the mixture with probability density function (PDF) as:

$$
p(\mathbf{f}_t) = \sum_{k=1}^K w_k \cdot \mathcal{N}(\mathbf{f}_t \mid \mu_k, \Sigma_k); \ \mu_k = \frac{1}{N_t} \sum_{n=1}^{N_t} \mathbf{f}_t^n; \ \Sigma_k = \frac{1}{N_t} \sum_{n=1}^{N_t} (\mathbf{f}_t^n - \mu_k)(\mathbf{f}_t^n - \mu_k)^T
$$

where,  $N_t$  denotes the number of samples in t<br> $\mathcal{N}(f \mid \mu_t, \Sigma_t)$  represents PDF of each  $k^{th}$  Gas where,  $N_t$  denotes the number of samples in  $t^{th}$  domain for a particular class,  $\mathcal{N}(\mathbf{f} \mid \mu_k, \Sigma_k)$  represents PDF of each  $k^{th}$  Gaussian component  $g_k$  with mean  $\mu_k$  and covariance matrix  $\Sigma_k$  and  $w_k$  is a positive weight associated with  $k^{th}$  $\mu_k$  and covariance matrix  $\Sigma_k$ , and  $w_i$  is a positive weight associated with  $k^{th}$ <br>Gaussian such that  $\sum_{k=1}^K w_k = 1$ . For a given value of K, the parameters  $\mu_k, \Sigma_k$ <br>for  $k \in \{1, 2, K\}$  of mixture distribution are for  $k \in \{1, 2...K\}$  of mixture distribution are estimated via expectation maxi-mization [\[6\]](#page-86-7). The fitted mixture model  $\mathbb{G}_t^i$  allows to generate random samples reflecting the *i*<sup>th</sup> class and  $t^{th}$  domain. Here, the optimal value of K is learned<br>using the Bayesian Information Criterion (BIC) [9]. The candidate values for K using the Bayesian Information Criterion (BIC) [\[9](#page-86-8)]. The candidate values for  $K$ lie in range 1 and a given upper limit  $K_{max}$ , and the one that minimizes BIC is selected. Finally, the learnt generators from  $t^{th}$  domain are then added to GMM-<br>pool containing all past generators  $\mathbb{G}^i$  for  $i \in \{1, \ldots, C\}$  and  $t \in \{1, \ldots, t-1\}$ pool containing all past generators  $\mathbb{G}_t^i$  for  $i \in \{1, ..., C\}$  and  $t \in \{1, ..., t-1\}$ .

#### **3 Experimental Setup**

**(1) Datasets:** Various datasets characterizing shifts in staining, organ, and center are curated. We outline these datasets in Table [1.](#page-81-0) Please note that traintest split does not follow slide-level split except for the organ shift datasets.

**Stain Shift (SS):** The data was acquired at center C1 and includes healthy and tumorous breast tissue. Experienced pathologists carefully annotated breast cancer regions in detail for Hematoxylin and eosin (HE), Cytokeratin (CK5/14),

| Details of shift                                 | Datasets                                                                  | #Train patches                                          | #Test patches |
|--------------------------------------------------|---------------------------------------------------------------------------|---------------------------------------------------------|---------------|
| SS: Staining varies, organ (breast), center (C1) | HE, CD8, TRI, CK5/14, PAS                                                 | 1510, 3370, 4706, 4704, 3372                            | 1000 each     |
| OS: staining (H), organ<br>varies, center (C2),  | Breast, Colon, Liver, Lung, Ovary,<br>Pancreas, Prostate, Stomach, Uterus | 1704, 1982, 1694, 1472, 1338, 1308,<br>1916, 1836, 1900 | 600 each      |
| HS: Heterogeneous                                | CD8/breast/C1, HE/breast/C3 (BRACS),<br>H/colon/C2, HE/colon/C4 (CRC)     | 3370, 6096, 1982, 6600                                  | 600–1000      |

<span id="page-81-0"></span>**Table 1.** Details of shift types and available datasets used in experiments

|            | $Exp. \rightarrow$                                                                                                                                                                                                                                                                                                                                       | SS                                           |       |                  |               |                 |              | НS                             |              |            |
|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------|-------|------------------|---------------|-----------------|--------------|--------------------------------|--------------|------------|
|            | Approach                                                                                                                                                                                                                                                                                                                                                 | $BWT\uparrow$ Acc. $\uparrow$ ILM $\uparrow$ |       |                  | $BWT\uparrow$ | Acc. $\uparrow$ | <b>ILM</b> ↑ | $BWT\uparrow$ $ Acc.\uparrow $ |              | ILM↑       |
|            | $\exists$ Naive                                                                                                                                                                                                                                                                                                                                          | -31.09                                       | 71.72 | 77.54            | –7.03         | 79.41           | 82.72        | -38.80 66.92                   |              | 73.88      |
|            | $\frac{1}{6}$ Joint                                                                                                                                                                                                                                                                                                                                      |                                              | 98.04 |                  |               | 95.46           |              |                                | 86.73        |            |
|            | $\tilde{\mathsf{Z}}$ Cumulative - 0.30                                                                                                                                                                                                                                                                                                                   |                                              |       | 98.00 98.18 8.12 |               |                 | 95.8094.99   | 0.07                           |              | 88.6793.77 |
|            | GEM                                                                                                                                                                                                                                                                                                                                                      | -0.83                                        | 86.64 | 83.87            | $^{-1.77}$    | 88.70           | 89.02        | -19.63  69.11                  |              | 80.93      |
| <u>sed</u> | AGEM                                                                                                                                                                                                                                                                                                                                                     | 5.63                                         | 91.64 | 89.83            | $^{-2.18}$    | 87.39           | 89.38        | -20.07 69.31                   |              | 80.53      |
|            |                                                                                                                                                                                                                                                                                                                                                          | -1.62                                        | 96.80 | 97.10            | 7.83          | 96.22 94.32     |              | -2.60                          | 90.01 93.53  |            |
|            | $\begin{array}{c}\n\stackrel{\text{ad}}{=} \text{ER} \\ \stackrel{\text{def}}{=} \text{LR}\n\end{array}$                                                                                                                                                                                                                                                 | -1.06                                        | 96.72 | 97.20            | 5.99          | 94.61           | 93.71        | $-2.17$                        | 87.54  92.89 |            |
|            | $\tilde{\mathbf{p}}_{\text{ER}}$                                                                                                                                                                                                                                                                                                                         | -3.42                                        | 94.12 | 95.87            | 2.65          | 95.50           | 93.64        | -8.08                          | 89.07        | 91.94      |
|            | $_{\rm LR^*}$                                                                                                                                                                                                                                                                                                                                            | $-2.41$                                      | 95.28 | 95.97            | 5.97          | 93.76           | 92.87        | $-5.02$                        | 85.97        | 91.54      |
|            | $\begin{array}{l} \mathop {\underleftarrow {\mathop {\mathop {\vphantom {\left( {\rm{g}} \right } _{_{\rm{w}}}}\prod\limits_{\rm{w}}^{\rm{w}} {{\rm{E}}\rm{W}}\rm{F}}}} \atop {\underleftarrow {\mathop {\mathop {\mathop {\mathop {\vphantom {\mathop {\mathop {\mathop {\rm{g}}\rm{w}}\rm{E}}\rm{W}}\rm{C}}}} \atop {_{\rm{w}}\rm{EWC}}}} \end{array}$ | -26.86                                       | 78.56 | 79.83            | -6.77         | 82.31           | 84.51        | -35.92  70.71                  |              | 75.45      |
|            |                                                                                                                                                                                                                                                                                                                                                          | -28.99                                       | 70.00 | 78.28            | -4.15         | 83.44           | 84.45        | -30.32 74.72                   |              | 77.50      |
|            |                                                                                                                                                                                                                                                                                                                                                          | $-27.16$                                     | 77.56 | 80.15            | -9.12         | 81.00           | 82.69        | -39.42  66.41                  |              | 73.54      |
| $\approx$  | Proposed                                                                                                                                                                                                                                                                                                                                                 | $-1.65$                                      | 96.20 | 96.76            | 6.43          | 94.48           | 93.23        | $-2.58$                        | 87.38        | 92.66      |

<span id="page-82-0"></span>**Table 2.** Best performance result in buffer-based/buffer-based with low buffer/bufferfree categories indicated in red/blue/green, respectively. **Bold:** Upper bound.

and Cluster of differentiation (CD8) stained WSIs. For preprocessing, we selected foreground tissue regions using the CLAM preprocessing pipeline [\[21\]](#page-87-8). As stainings highly vary in colorization, we manually adjusted tissue detection thresholds. Each annotated region was tiled into  $512 \times 512$  px patches at  $40 \times$  magnification. Since we obtained  $5\times$  more patches from CK5/14 and CD8 compared to HE and to include more inter-stain variation, we used half of both datasets and augmented them using Cycle-GAN (cGAN) into artificial trichome (TRI) and periodic acid-Schiff (PAS) stainings, respectively.

**Organ Shift (OS):** This dataset from center C2 comprises 20 tissue microarray (TMA) cores, occupied with tissue of 9 organs in hematoxylin (H) staining. TMA cores were systematically sampled from a diverse cohort of patients and contained cancerous and healthy tissue. The same preprocessing strategy as in stain-variation is applied here. We split datasets spot-wise to prevent data leakage at both, patient and tissue level.

**Heterogeneous Shift (HS):** In addition to homogeneous kinds of shift (i.e., either SS or OS), we also consider heterogeneous shifts involving a mix of different stains, organs, and centers, simultaneously. Specifically, we consider the "CD8" dataset from the SS sequence (stain:CD8, organ:breast, center:C1), the "Colon" dataset from the OS sequence (stain:H, organ:colon, center:C2), a subset from the BRACS dataset [\[4](#page-86-9)] (stain:HE, organ:breast, center:C3), and a subset from the CRC dataset [\[14](#page-86-10)] (stain:HE, organ:colon, center:C4). All these datasets are curated such that they comprise only normal and tumor classes.

**(2) Comparable Methods:** Both non-CL and CL approaches are considered for comparison. The non-CL approaches include (a) lower-bound performance obtained by a naive method (i.e. training dataset of the current task is used only) and (b) upper-bound performance by a joint method (i.e. training datasets from all tasks are used) and a cumulative method (training datasets are accumulated from seen tasks). For CL, we consider frequently used approaches in the two categories as (a) buffer-based (i.e. a few past samples are stored in raw or latent form) and (b) buffer-free (no storing of past samples). In the buffer-based category, we consider GEM  $[20]$  $[20]$ , A-GEM  $[5]$ , ER  $[25]$  $[25]$ , and LR  $[22]$ . In the buffer-free category, EWC [\[17](#page-86-2)], SI [\[31\]](#page-87-12), and LwF [\[19\]](#page-87-13) are considered.

**(3) Implementation Details:** In the following, we describe the training strategies of our models and the included evaluation metrics.

**cGAN Pre-training:** For cGAN pre-training we extracted patches in the scale of  $512 \times 512$  px from TRI and PAS stained WSIs as provided by the KPMP database [\[16](#page-86-12)]. Regarding hyperparameter settings, we followed the literature [\[3](#page-86-13)] and performed a grid search around an order of magnitude around the base parameters. We selected the best-performing model by visually inspecting the derived stain augmentations, resulting in the number of iterations of 30  $\in$ [20, 30, 40] epochs and a learning rate of  $1.5e-4 \in [1.0e-3, 1.5e-3, \ldots, 1e-5]$ . To prevent any data leakage, we used unlabeled excess tiles from CK5/14 and CD8 from our stain-variation WSIs to train our cGAN using target domains from the KPMP dataset. A total of 5000 images were used for training. The training and inference time was approximately 8 h per stain.

**CL Training:** We selected the hyperparameters according to common CL evaluation schemes. For all comparable methods, we used the CL benchmark library Avalanche<sup>[1](#page-83-0)</sup>. We used AdamW as an optimizer and used learning of  $1e - 03$ . Images were resized to  $256 \times 256$  px and batched with a size of 64. All methods used a pre-trained ResNet50 model initialized with ImageNet weights. After the last batch normalization layer in ResNet50, 5 fully connected layers {512, 256, 128, 64, 32} are added. For all the experiments, we keep  $K_{max} = 10$ . All experiments were conducted using an Nvidia A100 GPU, with training sessions typically completed within a maximum duration of 4 h across all approaches.

**Evaluation Metrics:** The performance is assessed using the classification accuracy metric. For any experiment with  $T$  tasks in sequence, we get a train-test matrix A of size  $T \times T$  where the cell value  $A_{ij}$  represents accuracy on  $j^{th}$  task

<span id="page-83-0"></span><sup>1</sup> [https://avalanche.continualai.org/.](https://avalanche.continualai.org/)

after training up to  $i^{th}$  task. This matrix can be used to compute various metrics<br>to compare approaches. We consider popularly used metrics in CL literature to compare approaches. We consider popularly used metrics in CL literature, specifically Backward Transfer (BWT) for measuring forgetting  $(Eq. 3$  in  $[8]$ ), Incremental Learning Metric (ILM) to measure incremental learning capability (Eq. 2 in [\[8](#page-86-14)]) and average accuracy across all the tasks after learning the last task (Eq. 2 in [\[20\]](#page-87-9)). For all these metrics a higher positive value indicates superiority.

Image /page/84/Figure/2 description: This image contains six line graphs, arranged in a 2x3 grid. The top row graphs are titled 'SS with buffer-based', 'OS with buffer-based', and 'HS with buffer-based'. The bottom row graphs are titled 'SS with buffer-free', 'OS with buffer-free', and 'HS with buffer-free'. All graphs plot 'Avg. Accuracy' on the y-axis against 'Session' on the x-axis. The y-axis ranges from 60 to 100. The 'SS with buffer-based' graph shows 5 sessions, the 'OS with buffer-based' graph shows 9 sessions, and the 'HS with buffer-based' graph shows 4 sessions. The 'SS with buffer-free' graph shows 5 sessions, the 'OS with buffer-free' graph shows 9 sessions, and the 'HS with buffer-free' graph shows 4 sessions. A legend on the right side of the top row graphs lists several methods: 'proposed', 'ER\*', 'ER', 'LR\*', 'LR', 'GEM', 'AGEM', 'joint', 'cummulative', 'EWC', 'LwF', 'SI', and 'naive', each represented by a different color and marker.

<span id="page-84-0"></span>**Fig. 3.** Performance over time for different shifts (SS, OS, HS) and for buffer-based *(upper row)* versus buffer-free CL approaches + non-CL *(lower row)*.

#### **4 Results and Discussion**

Within each shift scenario (SS, OS, HS), a random ordering of the available data is created and utilized for evaluating different approaches (see Table [2\)](#page-82-0). Our supplementary file provides results on further random orderings (showing same trends) and further discussion on individual evaluation metrics. As expected, "cumulative" and "joint" represent the upper, and "naive" indicates the lower bound. For all three evaluation metrics, there is a large performance gap between buffer-based approaches and buffer-free approaches across all experiments (SS, OS, and HS). This is in line with previous findings that rehearsalbased approaches handle forgetting in medical data better [\[7](#page-86-3)[,18](#page-87-1)]. In the bufferbased category, ER and LR approaches largely offer the best results. Our bufferfree GLRCL approach (green) achieves performances slightly worse than the best-performing buffer-based approaches with a large buffer (red). However, when reducing the buffer size by  $1/4$  (see ER<sup>\*</sup> and LR<sup>\*</sup>), a significant performance drop is observed (best results in blue), resulting in performance values mostly below our GLRCL approach in all experiments. This shows that the performance of buffer-based approaches gets compromised when reducing the buffer size. When comparing with existing buffer-free CL approaches, we observe that our novel GLRCL method clearly outperforms these methods by a large margin across all three domain shift conditions. Further, in terms of accuracy (Acc. and ILM), HS performs generally worse than SS and OS, due to the multitude of variations (stain, organ, center shifts) encountered in HS.

To analyze performance over time, we report the average accuracy of the cancer classification tasks after each training session for each of our three domain shift experiments (see Fig. [3\)](#page-84-0). The first row shows a comparison of our GLRCL approach (green, "proposed") with buffer-based, and the second row with buffer-free and non-CL approaches, respectively. These graphs clearly show that GLRCL significantly outperforms the buffer-free approaches and performs similarly to the buffer-based approaches, creating a new CL benchmark and an alternative to buffer-based CL approaches for digital pathology.

## **5 Conclusion**

Our novel privacy-aware CL approach for histopathology tumor detection in domain incremental scenarios outperforms existing buffer-free CL approaches by a large margin, and mitigates data privacy concerns compared to image-storing buffer-based CL methods with comparable performance. Our work significantly advances privacy-aware tumor detection from histology data, pushing the boundaries of current CL strategies under privacy preservation. For future work, we investigate for a better choice of latent representations so that the learned GMM better captures the domain and class differences. Additionally, we aim for a single generator with a dynamic update mechanism triggered with drift detection module which eventually deals with reoccurring and overlapping domains.

**Acknowledgements.** This work was supported by the German Research Foundation (Deutsche Forschungsgemeinschaf, DFG) under project number 445703531 and the German Federal Ministry of Education and Research (Bundesministerium für Bildung und Forschung, BMBF) under project numbers 01IS21067A and 01IS21067B. The authors gratefully acknowledge the computational and data resources provided by the Leibniz Supercomputing Centre [\(www.lrz.de\)](www.lrz.de).

**Disclosure of Interests.** The authors declare that they have no conflicts of interest related to this work.

## **References**

- <span id="page-85-0"></span>1. B´andi, P., Balkenhol, M., van Dijk, M., Kok, M., van Ginneken, B., van der Laak, J., Litjens, G.: Continual learning strategies for cancer-independent detection of lymph node metastases. Medical Image Analysis **85**, 102755 (2023)
- <span id="page-85-1"></span>2. Bengio, Y., Courville, A., Vincent, P.: Representation learning: A review and new perspectives. IEEE transactions on pattern analysis and machine intelligence **35**(8), 1798–1828 (2013)

- <span id="page-86-13"></span>3. Bouteldja, N., Hölscher, D.L., Klinkhammer, B.M., Buelow, R.D., Lotz, J., Weiss, N., Daniel, C., Amann, K., Boor, P.: Stain-independent deep learning–based analysis of digital kidney histopathology. The American Journal of Pathology **193**(1), 73–83 (Jan 2023)
- <span id="page-86-9"></span>4. Brancati, N., Anniciello, A.M., Pati, P., Riccio, D., Scognamiglio, G., Jaume, G., De Pietro, G., Di Bonito, M., Foncubierta, A., Botti, G., et al.: Bracs: A dataset for breast carcinoma subtyping in h&e histology images. Database **2022**, baac093 (2022)
- <span id="page-86-11"></span>5. Chaudhry, A., Ranzato, M., Rohrbach, M., Elhoseiny, M.: Efficient lifelong learning with a-gem. arXiv preprint [arXiv:1812.00420](http://arxiv.org/abs/1812.00420) (2018)
- <span id="page-86-7"></span>6. Dempster, A.P., Laird, N.M., Rubin, D.B.: Maximum likelihood from incomplete data via the em algorithm. Journal of the royal statistical society: series B (methodological) **39**(1), 1–22 (1977)
- <span id="page-86-3"></span>7. Derakhshani, M.M., Najdenkoska, I., van Sonsbeek, T., Zhen, X., Mahapatra, D., Worring, M., Snoek, C.G.: Lifelonger: A benchmark for continual disease classification. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 314–324. Springer (2022)
- <span id="page-86-14"></span>8. Díaz-Rodríguez, N., Lomonaco, V., Filliat, D., Maltoni, D.: Don't forget, there is more than forgetting: new metrics for continual learning. arXiv preprint [arXiv:1810.13166](http://arxiv.org/abs/1810.13166) (2018)
- <span id="page-86-8"></span>9. Fraley, C., Raftery, A.E.: How many clusters? which clustering method? answers via model-based cluster analysis. The computer journal **41**(8), 578–588 (1998)
- <span id="page-86-0"></span>10. Guan, H., Liu, M.: Domain adaptation for medical image analysis: a survey. IEEE Transactions on Biomedical Engineering **69**(3), 1173–1185 (2021)
- <span id="page-86-6"></span>11. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-86-5"></span>12. Holub, P., Müller, H., Bíl, T., Pireddu, L., Plass, M., Prasser, F., Schlünder, I., Zatloukal, K., Nenutil, R., Brázdil, T.: Privacy risks of whole-slide image sharing in digital pathology. Nature Communications **14**(1), 2577 (2023)
- <span id="page-86-1"></span>13. Karani, N., Chaitanya, K., Baumgartner, C., Konukoglu, E.: A lifelong learning approach to brain mr segmentation across scanners and protocols. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 476–484. Springer (2018)
- <span id="page-86-10"></span>14. Kather, J.N., Krisam, J., Charoentong, P., Luedde, T., Herpel, E., Weis, C.A., Gaiser, T., Marx, A., Valous, N.A., Ferber, D., et al.: Predicting survival from colorectal cancer histology slides using deep learning: A retrospective multicenter study. PLoS medicine **16**(1), e1002730 (2019)
- <span id="page-86-4"></span>15. Kaustaban, V., Ba, Q., Bhattacharya, I., Sobh, N., Mukherjee, S., Martin, J., Miri, M.S., Guetter, C., Chaturvedi, A.: Characterizing continual learning scenarios for tumor classification in histopathology images. In: International Workshop on Medical Optical Imaging and Virtual Microscopy Image Analysis. pp. 177–187. Springer (2022)
- <span id="page-86-12"></span>16. Kidney Precision Medicine Project: Kidney Precision Medicine Project Data. Accessed October 15, 2023. [https://www.kpmp.org,](https://www.kpmp.org) the results here are in whole or part based upon data generated by the Kidney Precision Medicine Project. Funded by the National Institute of Diabetes and Digestive and Kidney Diseases
- <span id="page-86-2"></span>17. Kirkpatrick, J., Pascanu, R., Rabinowitz, N., Veness, J., Desjardins, G., Rusu, A.A., Milan, K., Quan, J., Ramalho, T., Grabska-Barwinska, A., et al.: Overcoming catastrophic forgetting in neural networks. Proceedings of the national academy of sciences **114**(13), 3521–3526 (2017)

- <span id="page-87-1"></span>18. Kumari, P., Chauhan, J., Bozorgpour, A., Azad, R., Merhof, D.: Continual learning in medical imaging analysis: A comprehensive review of recent advancements and future prospects. arXiv preprint [arXiv:2312.17004](http://arxiv.org/abs/2312.17004) (2023)
- <span id="page-87-13"></span>19. Li, Z., Hoiem, D.: Learning without forgetting. IEEE Transactions on Pattern Analysis and Machine Intelligence **40**(12), 2935–2947 (2018)
- <span id="page-87-9"></span>20. Lopez-Paz, D., Ranzato, M.: Gradient episodic memory for continual learning. Advances in neural information processing systems **30** (2017)
- <span id="page-87-8"></span>21. Lu, M.Y., Williamson, D.F., Chen, T.Y., Chen, R.J., Barbieri, M., Mahmood, F.: Data-efficient and weakly supervised computational pathology on whole-slide images. Nature Biomedical Engineering **5**(6), 555–570 (2021)
- <span id="page-87-11"></span>22. Pellegrini, L., Graffieti, G., Lomonaco, V., Maltoni, D.: Latent replay for realtime continual learning. In: 2020 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). pp. 10203–10209. IEEE (2020)
- <span id="page-87-5"></span>23. Pfülb, B., Gepperth, A.: Overcoming catastrophic forgetting with gaussian mixture replay. In: 2021 International Joint Conference on Neural Networks (IJCNN). pp. 1–9. IEEE (2021)
- <span id="page-87-0"></span>24. Ravishankar, H., Venkataramani, R., Anamandra, S., Sudhakar, P., Annangi, P.: Feature transformers: privacy preserving lifelong learners for medical imaging. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2019: 22nd International Conference, Shenzhen, China, October 13–17, 2019, Proceedings, Part IV 22. pp. 347–355. Springer (2019)
- <span id="page-87-10"></span>25. Rolnick, D., Ahuja, A., Schwarz, J., Lillicrap, T., Wayne, G.: Experience replay for continual learning. Advances in Neural Information Processing Systems **32** (2019)
- <span id="page-87-3"></span>26. Srivastava, S., Yaqub, M., Nandakumar, K., Ge, Z., Mahapatra, D.: Continual domain incremental learning for chest x-ray classification in low-resource clinical settings. In: MICCAI Workshop on Domain Adaptation and Representation Transfer. pp. 226–238. Springer (2021)
- <span id="page-87-4"></span>27. Viroli, C., McLachlan, G.J.: Deep gaussian mixture models. Statistics and Computing **29**, 43–51 (2019)
- <span id="page-87-6"></span>28. Yang, B., Lin, M., Zhang, Y., Liu, B., Liang, X., Ji, R., Ye, Q.: Dynamic support network for few-shot class incremental learning. IEEE Transactions on Pattern Analysis and Machine Intelligence **45**(3), 2945–2951 (2022)
- <span id="page-87-2"></span>29. Yang, H., Huang, W., Liu, J., Li, C., Wang, S.: Few-shot class-incremental learning for cross-domain disease classification. arXiv preprint [arXiv:2304.05734](http://arxiv.org/abs/2304.05734) (2023)
- <span id="page-87-7"></span>30. Yang, Y., Cui, Z., Xu, J., Zhong, C., Wang, R., Zheng, W.S.: Continual learning with bayesian model based on a fixed pre-trained feature extractor. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 397–406 (2021)
- <span id="page-87-12"></span>31. Zenke, F., Poole, B., Ganguli, S.: Continual learning through synaptic intelligence. In: International conference on machine learning. pp. 3987–3995. PMLR (2017)

Image /page/88/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below it. The icon is a circle with a bookmark shape inside. The text reads "Check for updates".

# <span id="page-88-0"></span>**Decoupled Training for Semi-supervised Medical Image Segmentation with Worst-Case-Aware Learning**

Ankit Das<sup>1( $\boxtimes$ )</sup>, Chandan Gautam<sup>2,3</sup>, Hisham Cholakkal<sup>4</sup>, Pritee Agrawal<sup>1</sup>, Feng Yang<sup>1</sup>, Ramasamy Savitha<sup>2,3</sup>, and Yong Liu<sup>1</sup>

<sup>1</sup> Institute of High Performance Computing (IHPC), Agency for Science, Technology and Research (A\*STAR), Singapore, Singapore

{dasak,yangf,liuyong}@ihpc.a-star.edu.s <sup>2</sup> Institute for Infocomm Research (I2R), A\*STAR, Singapore, Singapore

 ${g}$ autamc,ramasamysa}@i2r.a-star.edu.sg<br><sup>3</sup> International Research Laboratory on Artificial Intelligence (IPAL), CNRS@CREATE, Singapore, Singapore

<sup>4</sup> Mohamed bin Zayed University of Artificial Intelligence (MBZUAI), Abu Dhabi, UAE <EMAIL>

**Abstract.** While semi-supervised learning (SSL) has demonstrated remarkable success in natural image segmentation, tackling medical image segmentation with limited annotated data remains a highly relevant and challenging research problem. Many existing approaches rely on a shared network for learning from both labeled and unlabeled data, facing difficulties in fully exploiting labeled data due to interference from unreliable pseudo-labels. Additionally, they suffer from degradation in model quality resulting from training with unreliable pseudo-labels. To address these challenges, we propose a novel training strategy that uses two distinct decoders-one for labeled data and another for unlabeled data. This decoupling enhances the model's ability to fully leverage the knowledge embedded within the labeled data. Moreover, we introduce an additional decoder, referred to as the "worst-case-aware decoder," which indirectly assesses potential worst case scenario that might emerge from pseudo-label training. We employ adversarial training of the encoder to learn features aimed at avoiding this worst case scenario. Our experimental results on three medical image segmentation datasets demonstrate that our method shows improvements in range of  $5.6\%$  –  $28.10\%$  (in terms of dice score) compared to the state-of-the-art techniques. The source code is available at [https://github.com/thesupermanreturns/decoupled.](https://github.com/thesupermanreturns/decoupled)

**Keywords:** SSL · Decoupled Training · Medical Imaging

A. Das and C. Gauta—Joint first author.

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_5) 5.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 45–55, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_5)\_5

#### **1 Introduction**

Accurate segmentation of medical images is vital for various clinical applications [\[3,](#page-96-0)[14,](#page-97-0)[18](#page-97-1)]. While supervised semantic segmentation methods have demonstrated effectiveness in numerous segmentation tasks [\[25](#page-98-0),[28\]](#page-98-1), their reliance on a substantial number of labeled samples poses challenges. Acquiring an adequate amount of labeled data proves to be expensive and arduous, especially within the medical domain. Semi-supervised learning (SSL) [\[7](#page-97-2),[10,](#page-97-3)[13\]](#page-97-4) emerges as one of the most practical methods for training models with limited annotated data by providing a mechanism to leverage unlabeled data, consequently diminishing the demand for labeled data. The enhanced performance offered by SSL generally comes at a low cost, given that unlabeled data can be used with minimal human involvement. Consequently, numerous SSL techniques have been developed for the segmentation task  $[6,9,27]$  $[6,9,27]$  $[6,9,27]$  $[6,9,27]$  in the last decade. Notably, in recent years, semi-supervised segmentation for medical images (Med-SemSeg) has garnered significant attention from the research community  $[2,5,15,24]$  $[2,5,15,24]$  $[2,5,15,24]$  $[2,5,15,24]$  $[2,5,15,24]$ , primarily owing to the restricted availability of annotated data in the medical domain.

In the Med-SemSeg literature, the predominant methods often rely on pseudo-labeling and consistency-based regularization (CBR) [\[11](#page-97-9)[,16](#page-97-10)[,23,](#page-98-4)[24\]](#page-98-3). These existing methods have demonstrated the ability to harness knowledge from unlabeled data for learning by using a shared network between the labeled and unlabeled data. For example, a CBR-based mean-teacher network: where a network is shared between the teacher and student. Similarly, a typical pseudolabeled-based approach [\[19\]](#page-98-5) which also uses a shared network for labeled and unlabeled data. These shared network-based approaches iteratively improve the model's performance by using its predictions on the unlabeled data as pseudolabels. While these approaches have shown encouraging outcomes, the performance of these shared networks suffers from the inherent unreliability of pseudolabels, introducing two significant issues:

*Prob.1: Training a shared network with both labeled (true labels) and unlabeled (unreliable pseudo-labels) data interferes with the model's capability to fully exploit the true labels.*

*Prob.2: Training models using unreliable pseudo-labels can degrade model quality, particularly through iterative training with these pseudo-labels.*

The first issue is addressed by introducing a decoupled training approach. It involves the utilization of a common encoder in combination with two specialized decoders for labeled and unlabeled data, ensuring a clear distinction between the tasks of pseudo-label generation and application. This decoupling of decoders facilitates the network in retaining valuable information from labeled data using one decoder, enabling it to generate high-quality pseudo-labels.

To address the second issue of model degradation from training with unreliable pseudo-labels, one way is to compute the error from pseudo-labels and adjust the model to nullify its effect. However, directly measuring this error is not feasible due to the absence of ground truth data for the unlabeled samples. The nature of unreliable pseudo-labels (due to absence of ground truth) may cause the model to effectively learn only from the labeled data while making errors on the unlabeled data. This is the worst case scenario for the model which cannot learn new information from the unlabeled data. As a solution, we introduce a worst-case decoder  $\mathcal{D}^w$ , that optimizes the encoder to generate features to avoid this worst case, helping improve the model quality and performance on unlabeled data. Overall, the contributions are summarized as follows:

- We address two challenges in shared training approaches for Med-SemSeg: (i) partial exploitation of labeled samples due to a shared network (addressed by decoupled training), and (ii) Decreased model quality resulting from training with unreliable pseudo-labels (addressed by improved feature generation via introducing a worst-case decoder).
- To validate the efficacy of the proposed method, we performed experiments on three publicly available medical imaging datasets which showcase significant improvements compared to the state-of-the-art (SOTA) methods.

### **2 Proposed Method**

In semi-supervised medical image semantic segmentation, the goal is to achieve generalization from a combined dataset consisting of both pixel-wise labeled images  $\mathcal{X}_l = \{(x_l^b, y_l^b) : b \in (1, ..., B_l)\}\$ and unlabeled images  $\mathcal{X}_u = \{x_u^b :$  $b \in (1, \ldots, B_u)$ , where  $x_l^b$  denotes training labeled samples and  $y_l^b$  represents its corresponding ground truth masks,  $x_u^b$  denotes unlabeled samples,  $B_l$  and  $B_u$  denote the number of batches of labeled and unlabeled data, respectively and  $B_l \ll B_u$ . The overall objective of semi-supervised medical image semantic segmentation can be expressed as a combination of losses on labeled  $(\mathcal{L}^l)$  and unlabeled  $(\mathcal{L}^u)$  data as follows:

$$
\mathcal{L} = \mathcal{L}^l + \lambda \mathcal{L}^u,\tag{1}
$$

where  $\lambda$  is the trade-off parameter between losses on labeled and unlabeled data. In the realm of semi-supervised methods,  $\mathcal{L}^u$  plays a pivotal role in differentiating these methods. Conversely,  $\mathcal{L}^l$  typically computes cross-entropy loss between the predicted and ground truth mask.

#### **2.1 Method**

The overall architecture of the proposed method is illustrated in Fig. [1.](#page-91-0) It comprises an encoder and three decoders: (i) the main decoder  $(\mathcal{D}^l)$ , (ii) the strong augmented decoder  $(\mathcal{D}^s)$ , and (iii) the worst-case-aware decoder  $(\mathcal{D}^w)$ . It processes batches containing both labeled and unlabeled data to generate segmentations for medical images. The main decoder  $(\mathcal{D}^l)$  is trained solely on labeled samples using a supervised loss  $\mathcal{L}_{sup}^{l}$  and is responsible for generating pseudolabels. The remaining decoders ( $\mathcal{D}^s$  and  $\mathcal{D}^w$ ) address challenges related to the partial exploitation of labeled data and the model quality resulting from unreliable pseudo-label training. The strong augmented decoder  $(\mathcal{D}^s)$  is trained with a weak-to-strong augmentation and consumes the pseudo-labels. Meanwhile, the worst-case-aware decoder  $(\mathcal{D}^w)$  engages in a min-max game to estimate the worst possible scenario of pseudo-labeling and generates features to avoid this situation. It should be noted that  $(\mathcal{D}^l)$  is used during inference.

<span id="page-91-0"></span>Image /page/91/Figure/1 description: This figure illustrates a deep learning model architecture for medical image segmentation. The model utilizes labeled and unlabeled data, with various augmentation strategies. The architecture includes an encoder (E) and three decoders (Dl, Ds, Dw) for different prediction tasks. The legend explains the notation for forward and backward passes, predictions for labeled data (pl), weakly augmented data (pw, pu, pl), and strongly augmented data (ps1, ps2). The model incorporates several loss functions, including Lsup, Lwsl, Lws2, Lua, and Lwa, to guide the training process. Confidence thresholding is also depicted as a mechanism for filtering predictions.

**Fig. 1.** Illustration of our proposed method.

#### **2.2 Decoupled Training**

The effectiveness of shared network-based Med-SemSeg methods is affected by the use of unreliable pseudo-labels, leading to a deterioration in model performance and a further decline in the quality of pseudo-labels. To address this issue, we propose decoupling the generation and application of pseudo-labels through two decoders,  $\mathcal{D}^l$  and  $\mathcal{D}^s$ . The main decoder  $\mathcal{D}^l$  (Fig. [1\)](#page-91-0) is exclusively optimized using labeled data, avoiding the influence of unreliable pseudo-labels, while providing predictions (pseudo-labels) for unlabeled data. This prevents the main decoder  $\mathcal{D}^l$  from being affected by unreliable pseudo-labels. Additionally, we introduce another decoder,  $\mathcal{D}^s$ , trained using pseudo-labels obtained from  $\mathcal{D}^l$ . Pseudo-labels generated by  $\mathcal{D}^l$  are employed by  $\mathcal{D}^s$  using weak-to-strong consistency regularization for enhanced representational learning. Leveraging two strong views and a weak view of unlabeled data, the model's weak view output serves as pseudo-labels for strongly augmented views. This approach enables the model to generate diverse predictions for the same input, fostering robust feature representation through weak-to-strong consistency. Thus, the proposed method effectively decouples the generation and application of pseudo-labels, minimizing the following loss for unlabeled data in decoupled training:

<span id="page-91-1"></span>
$$
\mathcal{L}_{ws1}^{u}(E, \mathcal{D}^{s}) = \frac{1}{B_{u}} \sum_{i=1}^{B_{u}} \mathbb{1}(\max(p^{w}) \ge \eta) \mathbb{H}_{c}(\mathcal{D}^{l}(E(\mathcal{A}^{w}(x_{u}^{i}))), \mathcal{D}^{s}(E(\mathcal{A}^{s1}(x_{u}^{i})))) + \frac{1}{B_{u}} \sum_{i=1}^{B_{u}} \mathbb{1}(\max(p^{w}) \ge \eta) \mathbb{H}_{d}(\mathcal{D}^{l}(E(\mathcal{A}^{w}(x_{u}^{i}))), \mathcal{D}^{s}(E(\mathcal{A}^{s1}(x_{u}^{i})))) \quad (2)
$$

where  $\mathbb{H}_c$  denotes entropy minimization between two probabilistic distributions,  $\mathbb{H}_d$  denotes standard dice loss,  $\eta$  represents a threshold,  $p_i^w = \mathcal{D}^l(E(\mathcal{A}^w(x_i^i))$ 

denotes class distribution of pseudo-label,  $p_i^{s1} = \mathcal{D}^s(E(\mathcal{A}^{s1}(x_u^i)))$  denotes predicted class distribution of  $i^{t\bar{h}}$  unlabeled data for the first strong augmentation  $\mathcal{A}^{s1}$ . Similar to Eq. [2,](#page-91-1) we compute  $\mathcal{L}_{ws2}^{u}(E, \mathcal{D}^s)$  loss using second strong augmentation  $\mathcal{A}^{s2}$  instead of  $\mathcal{A}^{s1}$  in Eq. [2.](#page-91-1) The final loss for the decoder  $\mathcal{D}^s$  is as follows:

$$
\mathcal{L}_{ws}(E, \mathcal{D}^s) = \mathcal{L}_{ws1}^u(E, \mathcal{D}^s) + \mathcal{L}_{ws2}^u(E, \mathcal{D}^s)
$$
\n(3)

#### **2.3 Worst-Case-Aware Learning**

To address the risk of model degradation stemming from unreliable pseudolabels, it is essential to estimate the errors and adjust the model accordingly. However, due to the lack of ground truth for unlabeled data, estimating errors becomes infeasible, posing a challenge for the model to adapt and make necessary adjustments. Consequently, the model may only learn to classify labeled data, potentially leading to misclassification of unlabeled data. This represents the worst-case scenario that can arise from unreliable pseudo-label training. To address this challenge in the absence of a direct solution, we employ adversarial learning to avoid this worst case. We introduce a worst-case-aware decoder,  $\mathcal{D}^w$ , which acts as an adversary who's objective is to correctly classify labeled data while misclassifying unlabeled data. Employing a min-max adversarial setting between the encoder E and this decoder  $\mathcal{D}^w$ , compels the encoder E to generate features that evade this worst-case scenario. This ensures that the model avoids the aforementioned situation, thereby enhancing overall model quality.

$$
\mathcal{L}_{wa}(E, \mathcal{D}^w) = \mathcal{L}_{wa}^u(E, \mathcal{D}^w) + \mathcal{L}_{wa}^l(E, \mathcal{D}^w)
$$
\n
$$
= \min_{E} \max_{\mathcal{D}^w} \frac{1}{B_u} \sum_{i=1}^{B_u} \mathbb{1}(\max(p_i^w) \ge \eta) \mathbb{H}_c(p^w, \mathcal{D}^w(E(\mathcal{A}^w(x_u^i)))) - \frac{1}{B_l} \sum_{i=1}^{B_l} \mathbb{H}_c(p_i^l, \mathcal{D}^w(E(x_l^i))),
$$
\n(4)

where  $p_{adv,i}^u = \mathcal{D}^w(E(\mathcal{A}^w(x_u^i))))$  and  $p_{adv,i}^l = \mathcal{D}^w(E(x_l^i)))$  denote predicted class distribution of  $i^{th}$  unlabeled and labeled data by  $\mathcal{D}^w$ , respectively. The predicted class distribution of labeled data by  $\mathcal{D}^l$  is denoted by  $p_i^l = \mathcal{D}^l(E(x_i^i))$ .

**Overall Loss Function:** The optimization loss function of the proposed method is formalized as follows:

$$
\min_{E,\mathcal{D}^l,\mathcal{D}^S} \max_{\mathcal{D}^w} \lambda_{sup} \mathcal{L}_{sup}^l(E,\mathcal{D}^l) + \lambda_{ws} \mathcal{L}_{ws}(E,\mathcal{D}^s) + \lambda_{wa} \mathcal{L}_{wa}(E,\mathcal{D}^w),
$$
 (5)

where  $\lambda_{sup}$ ,  $\lambda_{ws}$ , and  $\lambda_{wa}$  are the coefficient of different losses;  $\mathcal{L}_{sup}^l(E, \mathcal{D}^l)$  minimizes sum of cross-entropy loss and dice loss on the labeled data using encoder  $E$  and decoder  $\mathcal{D}^l$ .

#### **3 Experiments and Results**

We evaluate our proposed method on three commonly used datasets for medical semantic segmentation, i.e., ProstateX [\[1](#page-96-2)], PROMISE12 [\[12\]](#page-97-11), and CHAOS [\[8](#page-97-12)].

<span id="page-93-0"></span>Image /page/93/Figure/0 description: The image displays a grid of segmentation masks, likely from a medical imaging study. The title "A. Das et al." is present, along with the number "50" on the left. The rows are labeled "Promise12(7)" and "Promise12(3)". The columns are labeled "Original", "GT", "MT", "EM", "URPC", "ICT", "CCT", "SSNet", "DCNet", and "Ours". Each cell in the grid contains a segmentation mask, with the "Original" column showing the original image data and the other columns showing segmentation results from different methods. The segmentation masks are predominantly yellow against a dark purple background.

**Fig. 2.** Visual comparison with different SOTA methods on PROMISE12 dataset for different percentages of labeled data. The visual comparison of ProstateX, and CHAOS datasets are provided in Fig. 1 of supplementary.

#### **3.1 Datasets and Evaluation Metrics**

**ProstateX.** The dataset includes 201 MRI scans with true mask segmentations from the ProstateX Challenge [\[1](#page-96-2)]. It is divided into training, validation, and test sets comprising 151, 19, and 31 scans, respectively.

**Prostate MR Image Segmentation (PROMISE12).** The dataset includes transversal T2-weighted MRI scans of the prostate from 50 patients, with corresponding true mask segmentations [\[12\]](#page-97-11). It is split into 35, 5, and 10 cases for training, validation, and testing sets, respectively.

**Combined Healthy Abdominal Organ Segmentation (CHAOS).** The dataset consists of 20 MRI volumes from the 2019 CHAOS Challenge [\[8](#page-97-12)], divided into subsets of 10, 5, and 5 cases for training, validation, and testing, respectively.

**Evaluation Metrics.** During the inference stage, predictions are produced slice by slice and then stacked into a 3D volume. Our results are presented based on three commonly used evaluation metrics: the Dice Similarity Score (DSC), Hausdorff Distance 95 (HD95), and Average Symmetric Distance (ASD) [\[4](#page-97-13)].

#### **3.2 Implementation Details**

We implemented the proposed method using PyTorch and conducted experiments on an NVIDIA A40 GPU with 128 GB RAM. The encoder-decoder architecture utilized a standard U-Net [\[18\]](#page-97-1) backbone, trained from scratch across all datasets. Model convergence was achieved using an ADAM optimizer, running for 600 epochs with a batch size of 12 and a learning rate of 0.01 for all experiments. Consistent weights were maintained for different losses throughout the experiments:  $\lambda_{sup} = 1$ ,  $\lambda_{ws} = 0.5$ ,  $\lambda_{wa} = 2$ . The ablation of  $\lambda_{ws}$  and  $\lambda_{wa}$  is provided in Table 1 and Table 2 of supplementary, respectively.

**Data Augmentation.** In our implementation, we utilize five types of data augmentation techniques, namely  $(1)$  random rotation,  $(2)$  random flipping,  $(3)$ color jitter, (4) random Gaussian blur, and (5) CutMix [\[26\]](#page-98-6). Random rotation and flipping are applied as weak augmentation methods, while color jitter, random Gaussian blur, and CutMix are utilized as strong augmentation techniques.

#### **3.3 Comparison with State-of-the-Art Methods**

In our experiments, we vary the labeled data percentages and compare the performance with state-of-the-art (SOTA) methods, including MT [\[20](#page-98-7)], EM [\[22\]](#page-98-8), URPC  $[16]$ , ICT  $[21]$  $[21]$ , CCT  $[17]$  $[17]$ , SSNet  $[24]$  and DCnet  $[5]$ . This study enables us to evaluate the impact of varying amounts of labeled data on the model's performance. We present the results of all three datasets with different percentages in Table [1.](#page-94-0) In this table, the proposed method surpasses all SOTA methods for all three datasets by at least 5.6%, 16.6%, and 8.1% margin in the case of 13%, 30%, and 20% labeled data for ProstateX, CHAOS, and PROMISE12 datasets, respectively. Further, when we experiment with a lesser percentage of the labeled data, then the proposed method outperformed the SOTA methods with even greater extent compared to earlier, i.e., outperformed by at least 11.9%, 28.10%, and 24.3% margin in the case of 3%, 11%, and 8% labeled data for ProstateX, CHAOS, and PROMISE12 datasets, respectively. In the scenario of a smaller number of labeled data, the proposed method performs well, however, most of the SOTA methods that utilize a shared network become even more corrupted by unreliable pseudo-labels. Further, the qualitative assessment of the outcomes of the proposed and (SOTA) methods across various label percentages is visually presented in Fig. [2.](#page-93-0) Here, it can be observed that our method obtains better predictions on all datasets against the SOTA methods. In both instances of the PROMISE12, specifically with the three cases, it is evident that the other competing methods struggle to perform accurate segmentation. Conversely, our method demonstrates proficiency in successfully segmenting the data.

| Method              | $Labeled\%$ ProstateX |                         |                | $Labeled\%$ Chaos |                   |                                           | $\text{Labeled}\%$ Promise12    |                    |           |
|---------------------|-----------------------|-------------------------|----------------|-------------------|-------------------|-------------------------------------------|---------------------------------|--------------------|-----------|
|                     |                       | $_{\mathrm{DSC}}$       | Hd95 ASSD      |                   | $_{\mathrm{DSC}}$ | Hd95 ASSD                                 |                                 | DSC                | Hd95 ASSD |
| FS $(UNet) 20(13%)$ |                       | $0.696$ 12.11 3.77      |                | $3(30\%)$         |                   |                                           | $ 0.561 $ 51.83 $ 17.37 7(20%)$ | $0.539$ 26.25 5.57 |           |
| МT                  |                       | $0.717$ 11.47 3.71      |                |                   |                   | 0.582 44.29 17.58                         |                                 | 0.610 36.44 6.27   |           |
| ΕM                  |                       | $ 0.739 $ 7.57 $ 2.68 $ |                |                   |                   | 0.574 49.38 17.98                         |                                 | 0.646 14.38 3.93   |           |
| <b>URPC</b>         |                       | $0.726$ 7.32            | 2.29           |                   |                   | $ 0.601 $ 32.81 $ 12.84 $                 |                                 | 0.677 30.32 7.03   |           |
| ICT                 |                       | $0.755$ 7.51            | 2.72           |                   |                   | $ 0.634 $ 42.63 $ 13.52 $                 |                                 | 0.684 16.74 4.15   |           |
| CCT                 |                       | $0.729$ 6.89            | $ 1.99\rangle$ |                   |                   | 0.609 43.17 18.62                         |                                 | 0.624 16.65 2.06   |           |
| SSnet               |                       | $0.760$ 6.16            | $ 1.44\rangle$ |                   |                   | 0.666 38.52 12.02                         |                                 | 0.730 34.24 10.98  |           |
| <b>DCNet</b>        |                       | $0.749$ 7.98            | 1.84           |                   |                   | 0.673 38.23 14.40                         |                                 | 0.760 6.48 2.10    |           |
| Ours                |                       | 0.805 2.83 0.53         |                |                   |                   | 0.807 13.73 3.71                          |                                 | 0.827 2.51 0.33    |           |
| FS (UNet) $5(3\%)$  |                       | 0.604 25.4 7.03         |                | $1(11\%)$         |                   |                                           | 0.381 60.27 23.70 3(8%)         | 0.319 45.23 19.39  |           |
| МT                  |                       | 0.633 25.8              | 6.23           |                   |                   | 0.357 59.63 23.66                         |                                 | 0.313 40.86 15.28  |           |
| ΕM                  |                       | 0.630 33.7 11.89        |                |                   |                   | 0.365 62.38 25.61                         |                                 | 0.353 49.28 21.47  |           |
| <b>URPC</b>         |                       | $0.614$ 17.64 3.87      |                |                   |                   | 0.385 52.65 17.97                         |                                 | 0.377 31.27 9.64   |           |
| ICT                 |                       | $0.636$ 33.13 11.63     |                |                   |                   | 0.429 65.61 26.72                         |                                 | 0.474 37.53 5.86   |           |
| CCT                 |                       | 0.595 36.26 9.92        |                |                   |                   | 0.445 50.92 21.30                         |                                 | 0.282 21.59 10.10  |           |
| SSnet               |                       | 0.676 15.99 4.22        |                |                   |                   | $ 0.494 $ 42.98   18.18                   |                                 | 0.499 25.46 11.87  |           |
| <b>DCNet</b>        |                       | $0.678$ 12.31 3.1       |                |                   | 0.477 67.30 25.42 |                                           |                                 | 0.599 16.03 6.86   |           |
| Ours                |                       | $ 0.77 $ 3.38 $ 0.55 $  |                |                   |                   | $\vert 0.687\vert 19.72\vert 7.38\rangle$ |                                 | 0.791 3.37 0.93    |           |

<span id="page-94-0"></span>**Table 1.** Comparison of the proposed method with SOTA Med-SemSeg methods.

#### **3.4 Ablation Studies**

**Impact of Different Losses.** The proposed method integrates five distinct losses: supervised loss  $(\mathcal{L}sup)$ , worst-case-aware losses  $(\mathcal{L}wa^{l}$  and  $\mathcal{L}wa^{u}$ ), and strong-augmentation losses ( $\mathcal{L}ws^1$  and  $\mathcal{L}ws^2$ ). In our experimentation, we analyze the impact of including or excluding each loss while keeping  $\mathcal{L}sup$  fixed as the base loss. Results in Table [2](#page-95-0) show that removing either of the strong augmentations leads to a similar decline in model performance, as expected due to their similar functions. Excluding worst-case-aware losses based on labeled  $(\mathcal{L}wa^l)$  and unlabeled  $(\mathcal{L}wa^u)$  data is explored in the third and fourth rows. The absence of  $\mathcal{L}_{wa}^u$  has a more significant impact, primarily due to its role in fine-tuning the encoder with unlabeled data to avoid the worst case scenario and generate higher-quality features.

<span id="page-95-0"></span>

|            | <b>Table 2.</b> Ablation on different losses on |  |  |
|------------|-------------------------------------------------|--|--|
| PROMISE12. |                                                 |  |  |

| Methods                         | $3(8\%)$         |                  | $7(20\%)$   |                  |  |
|---------------------------------|------------------|------------------|-------------|------------------|--|
|                                 | DSC <sub>1</sub> | $HD95\downarrow$ | <b>DSC1</b> | $HD95\downarrow$ |  |
| $w/\sigma \mathcal{L}_{ws}^2$   | 0.776            | 3.95             | 0.801       | 3.10             |  |
| $w/\sigma$ $\mathcal{L}_{ws}^1$ | 0.787            | 3.88             | 0.803       | 2.72             |  |
| $w/\sigma$ $\mathcal{L}_{wa}^l$ | 0.748            | 3.71             | 0.799       | 3.26             |  |
| $w/\sigma \mathcal{L}_{wa}^u$   | 0.756            | 5.03             | 0.781       | 3.58             |  |
| Ours                            | 0.791            | 3.37             | 0.827       | 2.51             |  |

Image /page/95/Figure/5 description: The image is a line graph titled "PROMISE12". The x-axis is labeled "Threshold" and ranges from 0.70 to 1.00. The y-axis is labeled "Dice Score" and ranges from 0.1 to 0.9. There are two lines on the graph. The blue line, labeled "3", starts at approximately 0.71 and increases to approximately 0.78 at a threshold of 0.95, then drops slightly to approximately 0.77 at a threshold of 1.00. The orange line, labeled "7", starts at approximately 0.78 and increases to approximately 0.82 at a threshold of 0.95, then drops slightly to approximately 0.81 at a threshold of 1.00. The orange line is consistently above the blue line.

**Fig. 3.** Ablation on confidence threshold values for PROMISE12.

<span id="page-95-1"></span>

|                               | <b>Table 3.</b> Ablation on PROMISE12 with |  |
|-------------------------------|--------------------------------------------|--|
| and without $\mathcal{D}^w$ . |                                            |  |

|                                   | 3(8%) |                                                                                         | $ 7 \ (20\%)$   |  |  |
|-----------------------------------|-------|-----------------------------------------------------------------------------------------|-----------------|--|--|
|                                   |       | $ {\rm DSC}^{\uparrow} {\rm HD}95\downarrow {\rm DSC}^{\uparrow} {\rm HD}95\downarrow $ |                 |  |  |
| w/o $\mathcal{D}^w$  0.748  8.25  |       |                                                                                         | 0.778 4.13      |  |  |
| with $\mathcal{D}^{w} 0.791 3.37$ |       |                                                                                         | $0.827$ $ 2.51$ |  |  |

**Table 4.** Ablation with different augmentations on PROMISE12.

| $# \text{ Aug}   3 (8\%)$ |              |                       | $ 7 \ (20\%)$     |  |  |  |
|---------------------------|--------------|-----------------------|-------------------|--|--|--|
|                           |              | DSC† HD95↓ DSC† HD95↓ |                   |  |  |  |
| $\mathfrak{D}$            | $0.791$ 3.37 |                       | $0.83 \quad 2.51$ |  |  |  |
| 3                         | $0.803$ 2.95 |                       | $0.814$ 2.74      |  |  |  |
| $\overline{A}$            | $0.792$ 3.16 |                       | $0.823$ 2.50      |  |  |  |

**Impact of the Worst-Case-Aware Decoder.** Table [3](#page-95-1) highlights the significant impact of excluding a worst-case-aware decoder on the proposed method's performance. Without this decoder, the encoder-generated features may result in pseudo-labels that deviate from their true labels due to sub-optimal hyperplane.

**Impact of Threshold.** By varying the threshold from 0.7 to 0.99 in Fig. [3,](#page-95-0) it can be observed that the model consistently achieved the best performance at 0.95 for both the cases of 3 and 7 on the PROMISE12 dataset.

**Effectiveness of Multiple Augmentations.** We analyzed our method by increasing the number of strong augmentations on decoder  $\mathcal{D}^s$  and presented results in Table [4.](#page-95-1) Additional augmentations do not consistently improve performance, reaching saturation where they no longer provide new information.

#### **4 Conclusion**

In this paper, we recognized the inherent challenges associated with shared network-based approaches and introduced a novel semi-supervised medical image segmentation method to overcome these issues. The proposed method is developed based on decoupled training combined with worst-case-aware learning. Experimental results on three medical imaging datasets demonstrated that our approach attains state-of-the-art performance, highlighting its effectiveness compared to existing methods.

**Acknowledgments.** This work was supported by the Agency for Science, Technology and Research (A\*STAR) through its AME Programmatic Funding Scheme Under Project A20H4b0141. This research is also part of the programme DesCartes and is supported by the National Research Foundation, Prime Minister's Office, Singapore under its Campus for Research Excellence and Technological Enterprise (CREATE) programme.

**Disclosure of Interests.** Author Yong Liu has received research grants from Company Agency for Science, Technology and Research (A\*STAR), and Author Ramasamy Savitha has received research grants from Company National Research Foundation.

#### **References**

- <span id="page-96-2"></span>1. Armato III, S.G., Huisman, H., Drukker, K., Hadjiiski, L., Kirby, J.S., Petrick, N., Redmond, G., Giger, M.L., Cha, K., Mamonov, A., et al.: Prostatex challenges for computerized classification of prostate lesions from multiparametric magnetic resonance images. Journal of Medical Imaging **5**(4), 044501–044501 (2018)
- <span id="page-96-1"></span>2. Bortsova, G., Dubost, F., Hogeweg, L., Katramados, I., De Bruijne, M.: Semisupervised medical image segmentation via learning consistency under transformations. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2019: 22nd International Conference, Shenzhen, China, October 13–17, 2019, Proceedings, Part VI 22. pp. 810–818. Springer (2019)
- <span id="page-96-0"></span>3. Cai, H., Li, S., Qi, L., Yu, Q., Shi, Y., Gao, Y.: Orthogonal annotation benefits barely-supervised medical image segmentation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3302–3311 (2023)

- <span id="page-97-13"></span>4. Chaitanya, K., Erdil, E., Karani, N., Konukoglu, E.: Contrastive learning of global and local features for medical image segmentation with limited annotations. Advances in neural information processing systems **33**, 12546–12558 (2020)
- <span id="page-97-7"></span>5. Chen, F., Fei, J., Chen, Y., Huang, C.: Decoupled consistency for semi-supervised medical image segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 551–561. Springer (2023)
- <span id="page-97-5"></span>6. Chen, X., Yuan, Y., Zeng, G., Wang, J.: Semi-supervised semantic segmentation with cross pseudo supervision. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 2613–2622 (2021)
- <span id="page-97-2"></span>7. Chen, Y., Tan, X., Zhao, B., Chen, Z., Song, R., Liang, J., Lu, X.: Boosting semi-supervised learning by exploiting all unlabeled data. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7548– 7557 (2023)
- <span id="page-97-12"></span>8. Kavur, A.E., Gezer, N.S., Barış, M., Aslan, S., Conze, P.H., Groza, V., Pham, D.D., Chatterjee, S., Ernst, P., Ozkan, S., et al.: Chaos challenge-combined (ctmr) healthy abdominal organ segmentation. Medical Image Analysis **69**, 101950 (2021)
- <span id="page-97-6"></span>9. Lai, X., Tian, Z., Jiang, L., Liu, S., Zhao, H., Wang, L., Jia, J.: Semi-supervised semantic segmentation with directional context-aware consistency. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 1205–1214 (2021)
- <span id="page-97-3"></span>10. Li, J., Xiong, C., Hoi, S.C.: Comatch: Semi-supervised learning with contrastive graph regularization. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 9475–9484 (2021)
- <span id="page-97-9"></span>11. Li, X., Yu, L., Chen, H., Fu, C.W., Xing, L., Heng, P.A.: Transformationconsistent self-ensembling model for semisupervised medical image segmentation. IEEE Transactions on Neural Networks and Learning Systems **32**(2), 523–534 (2020)
- <span id="page-97-11"></span>12. Litjens, G., Toth, R., Van De Ven, W., Hoeks, C., Kerkstra, S., Van Ginneken, B., Vincent, G., Guillard, G., Birbeck, N., Zhang, J., et al.: Evaluation of prostate segmentation algorithms for mri: the promise12 challenge. Medical image analysis **18**(2), 359–373 (2014)
- <span id="page-97-4"></span>13. Lucas, T., Weinzaepfel, P., Rogez, G.: Barely-supervised learning: Semi-supervised learning with very few labeled images. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 36, pp. 1881–1889 (2022)
- <span id="page-97-0"></span>14. Luo, X., Chen, J., Song, T., Wang, G.: Semi-supervised medical image segmentation through dual-task consistency. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 35, pp. 8801–8809 (2021)
- <span id="page-97-8"></span>15. Luo, X., Chen, J., Song, T., Wang, G.: Semi-supervised medical image segmentation through dual-task consistency. In: Proceedings of the AAAI conference on artificial intelligence. vol. 35, pp. 8801–8809 (2021)
- <span id="page-97-10"></span>16. Luo, X., Wang, G., Liao, W., Chen, J., Song, T., Chen, Y., Zhang, Shichuan, D.N.M., Zhang, S.: Semi-supervised medical image segmentation via uncertainty rectified pyramid consistency. Medical Image Analysis **80**, 102517 (2022)
- <span id="page-97-14"></span>17. Ouali, Y., Hudelot, C., Tami, M.: Semi-supervised semantic segmentation with cross-consistency training. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 12674–12684 (2020)
- <span id="page-97-1"></span>18. Ronneberger, O., Fischer, P., Brox, T.: U-Net: Convolutional networks for biomedical image segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 234–241. Springer (2015)

- <span id="page-98-5"></span>19. Tarvainen, A., Valpola, H.: Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results. Advances in neural information processing systems **30** (2017)
- <span id="page-98-7"></span>20. Tarvainen, A., Valpola, H.: Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results. Advances in neural information processing systems **30** (2017)
- <span id="page-98-9"></span>21. Verma, V., Lamb, A., Kannala, J., Bengio, Y., Lopez-Paz, D.: Interpolation consistency training for semi-supervised learning. In: Proceedings of the Twenty-Eighth International Joint Conference on Artificial Intelligence, IJCAI-19. pp. 3635–3641 (2019)
- <span id="page-98-8"></span>22. Vu, T.H., Jain, H., Bucher, M., Cord, M., Pérez, P.: Advent: Adversarial entropy minimization for domain adaptation in semantic segmentation. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 2517– 2526 (2019)
- <span id="page-98-4"></span>23. Wu, H., Wang, Z., Song, Y., Yang, L., Qin, J.: Cross-patch dense contrastive learning for semi-supervised segmentation of cellular nuclei in histopathologic images. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 11666–11675 (2022)
- <span id="page-98-3"></span>24. Wu, Y., Wu, Z., Wu, Q., Ge, Z., Cai, J.: Exploring smoothness and class-separation for semi-supervised medical image segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 34–43. Springer (2022)
- <span id="page-98-0"></span>25. Yu, C., Wang, J., Peng, C., Gao, C., Yu, G., Sang, N.: Bisenet: Bilateral segmentation network for real-time semantic segmentation. In: Proceedings of the European conference on computer vision (ECCV). pp. 325–341 (2018)
- <span id="page-98-6"></span>26. Yun, S., Han, D., Oh, S.J., Chun, S., Choe, J., Yoo, Y.: Cutmix: Regularization strategy to train strong classifiers with localizable features. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 6023–6032 (2019)
- <span id="page-98-2"></span>27. Zhong, Y., Yuan, B., Wu, H., Yuan, Z., Peng, J., Wang, Y.X.: Pixel contrastive-consistent semi-supervised semantic segmentation. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 7273–7282 (2021)
- <span id="page-98-1"></span>28. Zhou, T., Wang, W., Konukoglu, E., Van Gool, L.: Rethinking semantic segmentation: A prototype view. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 2582–2593 (2022)

<span id="page-99-0"></span>Image /page/99/Picture/0 description: A square button with a rounded corner is shown. The button is light gray and has a darker gray border. In the center of the button, there is a circular icon with a ribbon or bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a darker gray color.

# **DiffRect: Latent Diffusion Label Rectification for Semi-supervised Medical Image Segmentation**

Xinyu Liu, Wuyang Li, and Yixuan Yuan<sup>( $\boxtimes$ )</sup>

Department of Electronic Engineering, The Chinese University of Hong Kong, Shatin, Hong <NAME_EMAIL>

**Abstract.** Semi-supervised medical image segmentation aims to leverage limited annotated data and rich unlabeled data to perform accurate segmentation. However, existing semi-supervised methods are highly dependent on the quality of self-generated pseudo labels, which are prone to incorrect supervision and confirmation bias. Meanwhile, they are insufficient in capturing the label distributions in latent space and suffer from limited generalization to unlabeled data. To address these issues, we propose a Latent Diffusion Label Rectification Model (DiffRect) for semisupervised medical image segmentation. DiffRect first utilizes a Label Context Calibration Module (LCC) to calibrate the biased relationship between classes by learning the category-wise correlation in pseudo labels, then apply Latent Feature Rectification Module (LFR) on the latent space to formulate and align the pseudo label distributions of different levels via latent diffusion. It utilizes a denoising network to learn the coarse to fine and fine to precise consecutive distribution transportations. We evaluate DiffRect on three public datasets: ACDC, MS-CMRSEG 2019, and Decathlon Prostate. Experimental results demonstrate the effectiveness of DiffRect, e.g. it achieves 82.40% Dice score on ACDC with only 1% labeled scan available, outperforms the previous state-of-the-art by 4.60% in Dice, and even rivals fully supervised performance. Code is released at [https://](https://github.com/CUHK-AIM-Group/DiffRect) [github.com/CUHK-AIM-Group/DiffRect.](https://github.com/CUHK-AIM-Group/DiffRect)

**Keywords:** Semi-supervised · Medical Image Segmentation · Diffusion Models · Label Rectification

## **1 Introduction**

Medical image segmentation is crucial for clinical applications but often requires large amounts of pixel-wise or voxel-wise labeled data, which is tedious and timeconsuming to obtain  $[1,17-19,28]$  $[1,17-19,28]$  $[1,17-19,28]$  $[1,17-19,28]$  $[1,17-19,28]$  $[1,17-19,28]$ . Such a heavy annotation cost has motivated

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_6) 6.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 56–66, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_6)\_6

the community to develop semi-supervised learning methods [\[10](#page-108-2)[,14](#page-108-3)[,20](#page-108-4),[38\]](#page-109-1). Existing semi-supervised image segmentation methods can be generally categorized into self-training and consistency regularization. For self-training methods [\[1,](#page-107-0)[4,](#page-108-5) [7,](#page-108-6)[15](#page-108-7)[,23](#page-108-8),[31,](#page-109-2)[34](#page-109-3)[,40](#page-109-4)], they generate pseudo labels for unlabeled images, then use the pseudo-labeled images in conjunction with labeled images to update the segmentation model iteratively. This paradigm could effectively incorporate unlabeled data by minimizing their entropy. For consistency regularization methods [\[5](#page-108-9)[,9](#page-108-10),[21,](#page-108-11)[22,](#page-108-12) [27,](#page-109-5)[30](#page-109-6)[,33,](#page-109-7)[35](#page-109-8)[,37](#page-109-9)], they are designed based on the assumption that perturbations should not change the predictions of the model, and have achieved more promising performance recently. Perturbations are applied on the input or the network level, and the models are enforced to achieve an invariance of predictions.

Despite the progress, the semi-supervised medical image segmentation remains challenging due to the following factors. (1) **Reliance Risk**: Existing methods typically rely on self-generated pseudo labels to optimize the model [\[11](#page-108-13)[,30](#page-109-6),[35,](#page-109-8)[37\]](#page-109-9), which is ill-posed since errors in pseudo labels are preserved during iterative optimization. The overfitting to incorrect supervision could lead to severe confirmation bias [\[16\]](#page-108-14) and considerable performance degradation. Besides, they do not fully utilize the category-wise correlation in the pseudo labels, and the label quality is sensitive to the perturbation design and network structure. (2) **Distribution Misalignment**: Most methods only apply consistency regularization and auxiliary supervision at the output mask level to encourage the model to produce consistent mask predictions between different perturbations [\[5](#page-108-9)[,27](#page-109-5)]. However, these approaches are insufficient in capturing the semantics in the latent space and tend to overlook the underlying label distributions, resulting in limited generalization to unlabeled data.

To address the reliance risk issue, we first propose a **L**abel **C**ontext **C**alibration Module (LCC). Different from methods that directly use the self-generated pseudo labels, LCC calibrates the biased semantic context, *i.e.*, the relationships between different semantic categories, and reduce the errors in the pseudo labels. It starts with a semantic coloring scheme that encodes the one-hot pseudo labels and ground truth masks into the visual space, and subsequently feeds them into a semantic context embedding block to adjust the features of the pseudo labels in the latent space. Notably, LCC introduces explicit calibration guidance by encoding the dice score between the pseudo labels and the ground truth, thereby providing more reliable calibration directions for model optimization.

To tackle the distribution misalignment problem, some previous works have proposed to model data distributions with VAE [\[41](#page-109-10)] or GAN [\[42](#page-109-11)]. However, their adversarial training scheme could suffer from mode collapse and conflict between generation and segmentation tasks, resulting in suboptimal performance. Different from them, the denoising diffusion probabilistic model (DDPM) is a new class of generative models trained using variational inference [\[8](#page-108-15)[,12](#page-108-16)[,13](#page-108-17),[24\]](#page-109-12), which alleviates the above problem by formulating the complex data distribution with probabilistic models. Therefore, we design a **L**atent **F**eature **R**ectification Module (LFR), which models the consecutive refinement between different latent distributions with a generative latent DDPM [\[25\]](#page-109-13). LFR leverages the power of DDPM to learn the latent structure of the semantic labels. Specifically, it first applies Gaussian noise on fine-grained label features with a diffusion schedule,

then uses the coarse-grained label features as conditions to recover the clean feature. With the denoising process, the consecutive transportations of coarse to fine and fine to precise distributions of the pseudo labels are formulated and aligned, and the pseudo labels are progressively rectified for better supervision. Based on LCC and LFR, we construct a semi-supervised medical image segmentation framework named Latent **Diff**usion Label **Rect**ification Model (*DiffRect*). Extensive experimental results show that our method outperforms prior methods by significant margins.

### **2 Methodology**

#### **2.1 Preliminary: Conditional DDPM**

DDPM is a class of latent variable generative model that learns a data distribution by denoising noisy images [\[8](#page-108-15)]. The forward process diffuses the data samples with pre-defined noise schedules. Concretely, given a clean data  $z<sup>0</sup>$ , sampling of  $z<sup>t</sup>$  is expressed in a closed form:

$$
q(z^t||z^0) = \mathcal{N}(z^t; \sqrt{\overline{\alpha}_t}z^0, (1 - \overline{\alpha}_t)\mathbf{I}),
$$
\n(1)

where  $\overline{\alpha}_t$  is the noise schedule variable [\[8,](#page-108-15)[24\]](#page-109-12). During the reverse process, we are given an optional condition  $\rho$  [\[6](#page-108-18)], and each step is expressed as a Gaussian transition with learned mean  $\mu_{\epsilon}$  and variance  $\sigma_{\epsilon}$  from the denoising model  $\epsilon$ :

$$
p(z^{t-1} | z^t, \rho) := \mathcal{N}(z^{t-1}; \mu_{\epsilon}(z^t, t, \rho), \sigma_{\epsilon}(z^t, t, \rho) \mathbf{I}). \tag{2}
$$

By decomposing the above equation, we have:

$$
z^{t-1} \leftarrow \frac{1}{\sqrt{\alpha_t}} (z^t - \frac{1 - \alpha_t}{\sqrt{1 - \overline{\alpha}_t}} \epsilon(z^t, t, \rho)) + \sigma_{\epsilon} \eta,
$$
\n(3)

where  $\eta \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$  is a sampled noise that ensures each step is stochastic. In this work, we extend the conditional DDPM to the latent space of pseudo labels, and model the distribution transportations for label rectification.

#### **2.2 Label Context Calibration Module (LCC)**

Existing semi-supervised training schemes that rely extensively on self-generated pseudo labels are often ill-posed, where errors in low-quality pseudo labels accumulate and degrade performance. To address this issue, we introduce LCC that effectively captures and calibrates the semantic context within the visual space, thereby mitigating the impact of noisy labels. As in Fig.  $1(a)$  $1(a)$ , given the one-hot pseudo labels  $y_s, y_w \in \mathbb{R}^{H \times W \times C}$  with height H and width W from the segmentation network, we encode them to semantic pseudo labels  $m_s$  and  $m_w$  with dimensions of  $\mathbb{R}^{H \times W \times 3}$ , using a proposed *semantic coloring scheme (SCS)*.

Concretely, for a dataset that contains C different classes, we build a color set  $M_C$  that is composed of C RGB colors, and each color is represented by a tuple of three values within the range [0, 255]. We maximize the color difference

Image /page/102/Figure/1 description: This is a diagram illustrating a deep learning model for medical image segmentation. The model consists of three main modules: (a) Label Context Calibration Module, (b) Latent Feature Rectification Module, and (c) Segmentation Network. The Label Context Calibration Module takes semantic pseudo-labels (ms, mw, ml) and semantic ground truth (semantic GT) as input, processes them through a Semantic Context Embedding Block, and outputs calibrated guidance (tau) and latent features (zs, zw, zl). The Latent Feature Rectification Module refines these latent features using denoising Unets, distinguishing between strong pseudo-label (Strong PL) and weak pseudo-label (Weak PL) pathways, and also incorporates labeled diffusion and unlabeled diffusion. The Segmentation Network takes input images (Is, Iw) and the refined latent features to produce segmentation outputs (ys, yw). The diagram also includes a legend explaining various components and flows, such as iterative denoising, labeled/unlabeled diffusion, labeled/unlabeled segment, labeled/unlabeled coloring, supervision, skip connection, and inference flow.

<span id="page-102-0"></span>**Fig. 1.** Overall framework of DiffRect. (a) Label Context Calibration Module (LCC). (b) Latent Feature Rectification Module (LFR). (c) Segmentation Network.

between each encoded category to avoid semantic confusion. Therefore, it can be represented by a functional mapping  $f: C \to M_C$ , which is defined as:

$$
m_{(h,w)} = f(y_{(h,w)}), \quad \forall h \in [1, 2, ..., H], w \in [1, 2, ..., W],
$$
\n(4)

where m is the semantic pseudo label in the visual space, and  $m_{(h,w)}$  represents the mapped RGB color of the pixel at location  $(h, w)$  in m. The  $y_{(h, w)}$  represents the class of the corresponding pixel in one-hot mask y. The semantic coloring scheme can effectively incorporate color information into the segmentation task, which enables the model to exploit additional cues with the rich semantics from colors, and improves the discrimination ability  $[3,32]$  $[3,32]$  $[3,32]$  as well as the interpretability of the model.

To perform context calibration with the semantic labels, we design a semantic context embedding block  $\mathbf{B}_{sem}$ , which embeds the pseudo labels to the latent features  $z_s, z_w, z_l$  with the dimensions of  $\mathbb{R}^{\frac{H}{16} \times \frac{W}{16} \times 256}$ . Specially, additional *calibration guidance* (CG)  $\tau^u$  for unlabeled data and  $\tau^l$  for labeled data are also encoded into the block using the sinusoidal embeddings [\[8,](#page-108-15)[29](#page-109-15)],

$$
\{z_s, z_w\} = \mathbf{B}_{\text{sem}}(m_s, m_w || \tau^u) \quad \text{for unlabeled data,}
$$
$$
\{z_w, z_l\} = \mathbf{B}_{\text{sem}}(m_w, m_l || \tau^l) \quad \text{for labeled data,}
$$
$$
(5)
$$

where the  $\tau^u$  and  $\tau^l$  values for unlabeled and labeled data are computed using the dice coefficient between the one-hot segmentation masks of different qualities, which is denoted as follows:

$$
\tau^u = \text{Dice}(y_s, y_w), \quad \tau^l = \text{Dice}(y_w, y_l). \tag{6}
$$

By using the dice coefficient as the calibration guidance factor, the model can simultaneously measure the quality of pseudo labels and integrate this information into the learning process. It enables the model to better capture the semantic context and refine the pseudo labels for both unlabeled and labeled data.

#### **2.3 Latent Feature Rectification Module (LFR)**

To address the distribution misalignment issue between the pseudo labels with different levels of quality, we propose a Latent Feature Rectification Module  $(LFR)$ , which is illustrated in Fig.  $1(b)$  $1(b)$ .

Concretely, LFR applies a latent diffusion process to model the transportation of label quality distributions. For each unlabeled data  $I_u$ , the strongly and weakly semantic context embedding  $z_s$  and  $z_w$  are first obtained with LCC. We then construct a diffusion process from  $z_w$  to the diffused noisy feature  $z_w^T$  with T timestamps as follows:

$$
z_w^T = \sqrt{\alpha_T} z_w^{T-1} + \sqrt{1 - \alpha_T} \eta^{T-1}
$$
  
= \dots = \sqrt{\overline{\alpha\_T}} z\_w + \sqrt{1 - \overline{\alpha\_T}} \eta, (7)

where  $\alpha_T$  and  $\overline{\alpha}_T$  are the schedule variables in the diffusion forward process,  $(e.g., \text{ cosine } [24]), \text{ and } \overline{\alpha}_T = \prod_{i=1}^T \alpha_i. \text{ The } \eta^t \text{ is the corresponding noise sam-}$  $(e.g., \text{ cosine } [24]), \text{ and } \overline{\alpha}_T = \prod_{i=1}^T \alpha_i. \text{ The } \eta^t \text{ is the corresponding noise sam-}$  $(e.g., \text{ cosine } [24]), \text{ and } \overline{\alpha}_T = \prod_{i=1}^T \alpha_i. \text{ The } \eta^t \text{ is the corresponding noise sam-}$ pled from Gaussian distribution at the  $t$ -th step. Then, we train a denoising U-Net  $\epsilon$  to learn to reverse this process. Since the individual reverse diffusion process is unconditioned, we add  $z<sub>s</sub>$  as the conditional input and also feed it into the denoising model. Therefore, the model is encouraged to learn the distribution transportation from *coarse-grained masks*  $p(z<sub>s</sub>)$  *(strong pseudo labels)* to the latent distributions of *fine-grained masks*  $p(z_w)$  *(weak pseudo labels)*, where we denote it as a *strong-to-weak transportation (S2W)*. The reverse diffusion is formulated as the following Markov chain:

$$
p_{\epsilon}\left(z_{w}^{0:T}\right) := p\left(z_{w}^{T}\right) \prod_{t=1}^{T} p_{\epsilon}\left(z_{w}^{t-1} \mid z_{w}^{t}, z_{s}\right), \quad z_{w}^{T} \sim \mathcal{N}(\mathbf{0}, \mathbf{I}) \quad (8)
$$

$$
p_{\epsilon}\left(z_{w}^{t-1} \mid z_{w}^{t}, z_{s}\right) := \mathcal{N}\left(z_{w}^{t-1}; \boldsymbol{\mu}_{\epsilon}\left(z_{w}^{t}, t, z_{s}\right), \sigma_{\epsilon}\left(z_{w}^{t}, t, z_{s}\right) \mathbf{I}\right),
$$

where  $\mu$  and  $\sigma$  are the predicted data mean and variance from the denoising U-Net model. For the training with unlabeled input, the *latent loss* for optimization can be expressed as follows:

$$
\mathcal{L}_{\text{Lat-U}} = E_{z_w, t} \left[ \|z_w - r_w\|_2 \right],\tag{9}
$$

where  $r_w = \epsilon(z_w^T, z_s, t)$ , which is the reconstructed version of the weakly semantic context embedding  $z_w$ . The objective minimizes the  $\ell_2$  distance between the clean and denoised feature and encourages the model to learn the distribution transportation from a coarse pseudo label to a fine pseudo label.

Similarly, we can obtain the weak semantic context embedding of labeled data  $z_w$  and the ground truth  $z_l$ . We then learn the reverse process that recovers  $z_l$  based on the T-timestamp diffused noisy feature  $z_l^T$ , with the  $z_w$  as condition:

$$
p_{\epsilon}\left(z_{l}^{0:T}\right) := p\left(z_{l}^{T}\right) \prod_{t=1}^{T} p_{\epsilon}\left(z_{l}^{t-1} \mid z_{l}^{t}, z_{w}\right), \quad z_{l}^{T} \sim \mathcal{N}(\mathbf{0}, \mathbf{I})
$$
  

$$
p_{\epsilon}\left(z_{l}^{t-1} \mid z_{l}^{t}, z_{w}\right) := \mathcal{N}\left(z_{l}^{t-1}; \boldsymbol{\mu}_{\epsilon}\left(z_{l}^{t}, t, z_{w}\right), \sigma_{\epsilon}\left(z_{l}^{t}, t, z_{w}\right) \mathbf{I}\right),
$$
 $(10)$ 

and the training objective for the reconstructed feature  $r_l = \epsilon \left( z_l^T, z_w, t \right)$  is:

$$
\mathcal{L}_{\text{Lat-L}} = E_{z_l, t} \left[ \|z_l - r_l\|_2 \right]. \tag{11}
$$

With the above latent diffusion process, the continual distribution transportations from *fine-grained mask distributions*  $p(z_w)$  *(weak pseudo labels)* to *precise mask distributions*  $p(z_l)$  *(ground truth)* are also formulated in the latent space, which is denoted as the *weak-to-groud truth transportation (W2G)*. The denoising U-Net is hence capable to achieve latent feature rectification.

Afterwards, the weak pseudo labels of unlabeled data are fed into the denoising U-Net for obtaining the rectified features with progressive denoising. Specifically, we randomly sample a Gaussian noise  $r_l^T \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$  as the input of the denoising U-Net, which simulates the T-timestamp noisy feature of the rectified pseudo label  $y_r$ . The rectified feature  $r_l$  is generated via a progressive reverse diffusion process, with the weak pseudo label features  $z_w$  as condition. Mathematically, a single denoising from step t to  $t - 1$  is formulated as:

$$
r_l^{t-1} \leftarrow \frac{1}{\sqrt{\alpha_t}} (r_l^t - \frac{1 - \alpha_t}{\sqrt{1 - \overline{\alpha}_t}} \epsilon(r_l^t, t, z_w)) + \sigma_{\epsilon} \eta,
$$
\n(12)

where  $\eta \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$  which ensures each step is stochastic as in DDPM [\[8\]](#page-108-15). The rectified label is obtained with an upsampling of the feature  $r_l$  to the input resolution  $y_r = Upsample(r_l)$ , which is utilized as a better and more precise supervision signal for the segmentation model.

#### **2.4 Loss Function**

The training of the DiffRect frameworks includes two parts: (1) the optimization of segmentation U-Net  $\theta$  (with Seg Loss) and (2) the joint optimization of the rectification components  $\mathbf{B}_{sem}$  and  $\epsilon$  (with Diff Loss). The overall loss is:

$$
\mathcal{L}_{\text{DiffRect}} = \underbrace{\mathcal{L}_{\text{Semi}}^{\text{Seg}} + \mathcal{L}_{\text{Rect}}}_{\text{Seg Loss}} + \underbrace{\mathcal{L}_{\text{Semi}}^{\text{Lat}} + \lambda_1 \mathcal{L}_{\text{Lat-U}} + \lambda_2 \mathcal{L}_{\text{Lat-L}}}_{\text{Diff Loss}},
$$
(13)

where  $\mathcal{L}_{\text{Semi}}^{\text{Seg}}$  and  $\mathcal{L}_{\text{Semi}}^{\text{Lat}}$  are the semi-supervised losses for segmentation as in [\[27\]](#page-109-5). The  $\lambda_1$  and  $\lambda_2$  are trade-off factors to balance the contribution of each term.  $\mathcal{L}_{\text{Rect}}$  is the rectified supervision loss between  $y_w$  and the rectified pseudo label  $y_r$ , where the summation of cross-entropy and Dice score are used:

$$
\mathcal{L}_{\text{Rect}} = \text{CE}(y_w, y_r) + \text{Dice}(y_w, y_r). \tag{14}
$$

During inference, the input is directly fed into segmentation network in Fig. [1\(](#page-102-0)c) to produce the segmentation result, thus no extra inference cost is required.

#### **3 Experiments**

#### **3.1 Experimental Setup**

We examine all methods with identical settings for fair comparison, and trained on a NVIDIA 4090 GPU for 30k iterations. For the  $\mathbf{B}_{sem}$  which downsamples

| Method           |       | Labeled Ratio ACDC Validation Set          |                   |                                                           |             | ACDC Test Set |                         |            |                |
|------------------|-------|--------------------------------------------|-------------------|-----------------------------------------------------------|-------------|---------------|-------------------------|------------|----------------|
|                  |       | $\text{Dice} \uparrow \text{Jac} \uparrow$ |                   | $ HD95\downarrow ASD\downarrow Dice\uparrow Jac\uparrow $ |             |               |                         | HD95↓ ASD↓ |                |
| UAMT $[39]$      | $1\%$ | 42.28 32.21                                |                   | 40.74                                                     |             |               | 18.58 43.86 33.36 38.60 |            | 18.33          |
| FixMatch [27]    |       |                                            | 69.67 58.34 37.92 |                                                           |             |               | 14.41 60.80 49.14 36.81 |            | 14.75          |
| CPS [5]          |       |                                            | 56.70 44.31 24.97 |                                                           |             |               | 10.48 52.28 41.68 20.38 |            | 7.35           |
| ICT $[30]$       |       |                                            | 43.03 30.58 34.92 |                                                           |             |               | 15.23 42.91 32.81 25.42 |            | 10.80          |
| $MCNetV2$ [35]   |       |                                            | 57.49 43.29 31.31 |                                                           |             |               | 10.97 49.92 39.16 24.64 |            | 8.47           |
| $INCL$ [43]      |       |                                            | 77.80 66.13 11.69 |                                                           | $3.22\,$    |               | 67.01 56.22 13.43       |            | $ 3.35\rangle$ |
| DiffRect (Ours)  |       |                                            |                   | 82.4071.96 10.04                                          | $\bm{2.90}$ |               | 71.85 61.53 5.79        |            | $\bf 2.12$     |
| <b>UAMT</b> [39] | $5\%$ |                                            | 72.71 60.89 21.48 |                                                           | 7.15        |               | 69.93 58.45 17.01       |            | 5.25           |
| FixMatch [27]    |       |                                            | 83.12 73.59 9.86  |                                                           | 2.61        |               | 74.68 64.12 11.18       |            | 2.93           |
| CPS [5]          |       | 75.24 64.67                                |                   | 10.93                                                     | 2.98        |               | 74.67 63.51 9.37        |            | 2.55           |
| ICT $[30]$       |       |                                            | 74.20 62.90 17.01 |                                                           | 4.32        |               | 73.10 60.69 11.92       |            | 3.70           |
| $MCNetV2$ [35]   |       |                                            | 78.96 68.15 12.13 |                                                           | 3.91        |               | 75.86 65.20 9.85        |            | 2.88           |
| $INCL$ [43]      |       |                                            | 85.43 75.76 6.37  |                                                           | 1.37        |               | 80.64 70.78 5.29        |            | 1.42           |
| DiffRect (Ours)  |       |                                            | 86.95 78.08 4.07  |                                                           | 1.23        |               | 82.46 71.76 7.18        |            | 1.94           |
| $UAMT$ [39]      | 10%   |                                            | 85.14 75.90 6.25  |                                                           | 1.80        |               | 86.23 76.72 9.40        |            | 2.56           |
| FixMatch [27]    |       | 88.31                                      | 79.97             | 7.35                                                      | 1.79        |               | 87.96 79.37 5.43        |            | 1.59           |
| CPS [5]          |       | 84.63                                      | 75.20             | 7.57                                                      | 2.27        |               | 85.61 75.76 9.29        |            | 3.00           |
| ICT $[30]$       |       | 85.15                                      | 76.05             | $ 4.27\rangle$                                            | 1.46        |               | 86.77 77.43 8.01        |            | 2.16           |
| $MCNetV2$ [35]   |       | 85.97                                      | 77.21             | 7.55                                                      | 2.11        |               | 88.75 80.28 6.16        |            | 1.64           |
| INCL $[43]$      |       |                                            | 88.28 80.09 1.67  |                                                           | 0.49        |               | 88.68 80.27 4.34        |            | $ 1.13\rangle$ |
| DiffRect (Ours)  |       |                                            | 90.1882.721.38    |                                                           | 0.48        |               | 89.27 81.13 3.85        |            | 1.00           |
| Supervised [26]  | 100%  |                                            | 91.48 84.87 1.12  |                                                           | 0.34        |               | 91.65 84.95 1.14        |            | 0.50           |

<span id="page-105-0"></span>Table 1. Segmentation results on the ACDC validation and test sets.

the input to  $\frac{H}{16} \times \frac{W}{16}$ , we use two  $3 \times 3$  convolution layers followed by BN and LeakyReLU before the  $2 \times$  downsample in each stage, and repeat for four stages. The Denoising U-Net  $\epsilon$  down and upsamples the input by  $4\times$ , which also uses two 3×3 convolution layers per stage. The multi-scale image feature is embedded into the model via concatenation as in [\[36](#page-109-16)]. For the weak perturbation, we apply random flipping and rotation. For the strong perturbation, we apply random Gaussian blur and additional random image adjustments, including contrast, sharpness, and brightness enhancement. For ACDC, we test the 1%, 5%, and 10% labeling regimes following [\[20](#page-108-4)]. For MS-CMRSEG 2019, 20% labeling regime is tested, while 10% labeled data is used in Decathlon Prostate.

#### **3.2 Comparison with State-of-the-Art Methods**

We validate the effectiveness of the proposed approach on the ACDC dataset [\[2](#page-107-2)] in Table [1.](#page-105-0) Our method shows superior results under all labeling regimes. Compared with MCNetV2 [\[35\]](#page-109-8), our method possesses superior capability with increments of 24.91%, 7.99%, 4.21% in Dice, 28.67%, 9.93%, 5.51% in Jaccard on the

| Method          | Dice $\uparrow$ | Jac $\uparrow$ | HD95 $\downarrow$ | ASD $\downarrow$ |
|-----------------|-----------------|----------------|-------------------|------------------|
| UAMT [39]       | 84.27           | 73.69          | 12.15             | 4.18             |
| FixMatch [27]   | 84.31           | 73.57          | 17.79             | 4.81             |
| CPS [5]         | 83.66           | 73.03          | 15.01             | 4.30             |
| ICT [30]        | 83.66           | 73.06          | 17.24             | 4.85             |
| MCNetV2 [35]    | 83.93           | 73.45          | 13.10             | 3.39             |
| INCL [43]       | 84.33           | 73.92          | 9.95              | 2.61             |
| <b>DiffRect</b> | <b>86.78</b>    | <b>77.13</b>   | <b>6.39</b>       | <b>1.85</b>      |
| Supervised [26] | 88.19           | 79.28          | 4.21              | 1.32             |

<span id="page-106-0"></span>**Table 2.** Segmentation results on MS-CMRSEG 2019 with 20% data labeled.

<span id="page-106-1"></span>

| <b>Table 3.</b> Segmentation results on |  |  |
|-----------------------------------------|--|--|
| Decathlon Prostate with 10% data        |  |  |
| labeled.                                |  |  |

| Method          | Dice↑        | Jac↑         | HD95↓        | ASD↓        |
|-----------------|--------------|--------------|--------------|-------------|
| UAMT [39]       | 40.91        | 29.13        | 28.32        | 10.45       |
| FixMatch [27]   | 54.70        | 41.07        | 16.82        | 5.24        |
| CPS [5]         | 43.51        | 31.18        | 26.93        | 8.31        |
| ICT [30]        | 39.91        | 28.95        | 24.73        | 7.59        |
| MCNetV2 [35]    | 40.58        | 28.77        | 21.29        | 7.11        |
| INCL [43]       | 55.67        | 41.91        | 31.09        | 15.78       |
| <b>DiffRect</b> | <b>62.23</b> | <b>48.64</b> | <b>10.36</b> | <b>3.41</b> |
| Supervised [26] | 73.81        | 61.25        | 7.28         | 1.94        |

<span id="page-106-2"></span>**Table 4.** Ablation study of the proposed modules.

| Method     | w/o        | Dice↑        | Jac↑         | HD95↓        | ASD↓        | Choice      | Dice <span style="vertical-align: super;"></span> | Jac <span style="vertical-align: super;"></span> | HD95 <span style="vertical-align: super; vertical-align: sub;"></span> | ASD <span style="vertical-align: super; vertical-align: sub;"></span> |
|------------|------------|--------------|--------------|--------------|-------------|-------------|---------------------------------------------------|--------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------------------|
| Baseline   | -          | 69.67        | 58.34        | 37.92        | 14.41       | Dice        | 82.40                                             | 71.96                                            | 10.04                                                                  | 2.90                                                                  |
| +LCC       | <b>SCS</b> | 73.83        | 61.83        | 29.49        | 11.71       | Jaccard     | 82.37                                             | 71.82                                            | 11.33                                                                  | 2.87                                                                  |
|            | CG         | 76.12        | 64.69        | 26.24        | 8.31        | Fixed       | 80.34                                             | 69.67                                            | 14.97                                                                  | 4.47                                                                  |
|            | -          | 78.28        | 66.97        | 20.46        | 5.60        | Random      | 80.60                                             | 69.99                                            | 13.15                                                                  | 3.75                                                                  |
| +LCC & LFR | S2W        | 79.97        | 69.31        | 14.07        | 4.91        | <b>Both</b> | 81.67                                             | 71.45                                            | 10.28                                                                  | 2.47                                                                  |
|            | W2G        | 78.57        | 66.38        | 21.07        | 5.91        |             |                                                   |                                                  |                                                                        |                                                                       |
|            | -          | <b>82.40</b> | <b>71.96</b> | <b>10.04</b> | <b>2.90</b> |             |                                                   |                                                  |                                                                        |                                                                       |

<span id="page-106-3"></span>**Table 5.** Ablation study of different calibration guidance choices in LCC.

validation set with  $1\%$ ,  $5\%$ , and  $10\%$  scans available. DiffRect displays better segmentation performance even when the labeled samples are extremely scarce (*e.g.* 82.40% Dice with 1% scans available), suggesting it can model the transportation of the pseudo label distributions precisely and produce refined masks. Results in MS-CMRSEG 2019 are shown in Table [2.](#page-106-0) DiffRect shows consistent performance gain on all metrics, with 86.78% in Dice, 77.13% in Jaccard, 6.39 mm in HD95, and 1.85 mm in ASD, outperforming the state-of-the-art method INCL [\[43](#page-109-18)] by 2.45% Dice, 3.21% Jaccard, 3.56 mm HD95, and 0.76 mm in ASD, respectively. On Decathlon Prostate in Table [3,](#page-106-1) DiffRect remains showing compelling results, demonstrating its capability in various modalities.

#### **3.3 Further Analysis**

**Ablation Study of the Proposed Modules.** We evaluate the effect of individual modules in DiffRect in Table [4.](#page-106-2) Adopting LCC achieves 78.28% Dice and 66.97% Jaccard, with 8.61% and 8.63% gains compared with the Fixmatch baseline [\[27](#page-109-5)]. Removing the semantic coloring scheme (SGS) shows a large performance drop (73.83% Dice and 61.83% Jaccard), showing the importance of exploiting the semantics in the visual domain. No calibration guidance (CG) causes 2.16% Dice drop due to the impact of noisy calibration directions. Adding LFR improves Dice by 4.12% and 10.42 mm in HD95. Removing the strong to weak transportation (S2W) shows a 2.43% Dice drop while removing the weak to ground truth (W2G) causes a severe Dice drop to 78.57%. The results demonstrate the necessity of each sub-component.

**Different Calibration Guidance Choices.** To analyze the effectiveness and the optimal choice of calibration guidance, experiments were conducted to compare the performance of models trained with different calibration guidance in Table [5,](#page-106-3) including Dice score, Jaccard score, Fixed (using a fixed value 0.5), Random (using a random sampled value within 0∼1), and Both (using the summation of Dice and Jaccard). It is shown that Dice, Jaccard, and Both have similar performance, and outperform the fixed and random strategies, which validates the reliable directions provided for optimization.

## **4 Conclusion**

In this paper, we identify the reliance risk and distribution misalignment issues in semi-supervised medical image segmentation, and propose DiffRect, a diffusionbased framework for this task. It comprises two modules: the LCC aims to calibrate the biased relationship between classes in pseudo labels by learning category-wise correlation, and the LFR models the consecutive transportations between coarse to fine and fine to precise distributions of the pseudo labels accurately with latent diffusion. Extensive experiments on three datasets demonstrate that DiffRect outperforms existing methods by remarkable margins.

**Acknowledgments.** This work was supported by Hong Kong Research Grants Council (RGC) General Research Fund 14204321.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-107-0"></span>1. Bai, W., Oktay, O., Sinclair, M., Suzuki, H., Rajchl, M., Tarroni, G., Glocker, B., King, A., Matthews, P.M., Rueckert, D.: Semi-supervised learning for networkbased cardiac mr image segmentation. In: MICCAI. pp. 253–260. Springer (2017)
- <span id="page-107-2"></span>2. Bernard, O., Lalande, A., Zotti, C., Cervenansky, F., Yang, X., Heng, P.A., Cetin, I., Lekadir, K., Camara, O., Ballester, M.A.G., et al.: Deep learning techniques for automatic mri cardiac multi-structures segmentation and diagnosis: is the problem solved? IEEE Trans. Med. Imaging **37**(11), 2514–2525 (2018)
- <span id="page-107-1"></span>3. Chen, J., Lu, J., Zhu, X., Zhang, L.: Generative semantic segmentation. In: CVPR. pp. 7111–7120 (2023)

- <span id="page-108-5"></span>4. Chen, S., Bortsova, G., García-Uceda Juárez, A., Van Tulder, G., De Bruijne, M.: Multi-task attention-based semi-supervised learning for medical image segmentation. In: MICCAI. pp. 457–465. Springer (2019)
- <span id="page-108-9"></span>5. Chen, X., Yuan, Y., Zeng, G., Wang, J.: Semi-supervised semantic segmentation with cross pseudo supervision. In: CVPR. pp. 2613–2622 (2021)
- <span id="page-108-18"></span>6. Choi, J., Kim, S., Jeong, Y., Gwon, Y., Yoon, S.: Ilvr: Conditioning method for denoising diffusion probabilistic models. In: ICCV. pp. 14347–14356. IEEE (2021)
- <span id="page-108-6"></span>7. Feng, Z., Zhou, Q., Cheng, G., Tan, X., Shi, J., Ma, L.: Semi-supervised semantic segmentation via dynamic self-training and classbalanced curriculum. arXiv preprint [arXiv:2004.08514](http://arxiv.org/abs/2004.08514) **1**(2), 5 (2020)
- <span id="page-108-15"></span>8. Ho, J., Jain, A., Abbeel, P.: Denoising diffusion probabilistic models. NeurIPS **33**, 6840–6851 (2020)
- <span id="page-108-10"></span>9. Hu, H., Wei, F., Hu, H., Ye, Q., Cui, J., Wang, L.: Semi-supervised semantic segmentation via adaptive equalization learning. NeurIPS **34**, 22106–22118 (2021)
- <span id="page-108-2"></span>10. Jiao, R., Zhang, Y., Ding, L., Cai, R., Zhang, J.: Learning with limited annotations: a survey on deep semi-supervised learning for medical image segmentation. arXiv preprint [arXiv:2207.14191](http://arxiv.org/abs/2207.14191) (2022)
- <span id="page-108-13"></span>11. Li, C., Lin, M., Ding, Z., Lin, N., Zhuang, Y., Huang, Y., Ding, X., Cao, L.: Knowledge condensation distillation. In: ECCV. pp. 19–35 (2022)
- <span id="page-108-16"></span>12. Li, C., Liu, H., Liu, Y., Feng, B.Y., Li, W., Liu, X., Chen, Z., Shao, J., Yuan, Y.: Endora: Video generation models as endoscopy simulators. arXiv preprint [arXiv:2403.11050](http://arxiv.org/abs/2403.11050) (2024)
- <span id="page-108-17"></span>13. Li, C., Liu, X., Li, W., Wang, C., Liu, H., Yuan, Y.: U-kan makes strong backbone for medical image segmentation and generation. [arXiv:2406.02918](http://arxiv.org/abs/2406.02918) (2024)
- <span id="page-108-3"></span>14. Li, C., Ma, W., Sun, L., Ding, X., Huang, Y., Wang, G., Yu, Y.: Hierarchical deep network with uncertainty-aware semi-supervised learning for vessel segmentation. NCA pp. 1–14 (2022)
- <span id="page-108-7"></span>15. Li, C., Zhang, Y., Liang, Z., Ma, W., Huang, Y., Ding, X.: Consistent posterior distributions under vessel-mixing: a regularization for cross-domain retinal artery/vein classification. In: ICIP. pp. 61–65. IEEE (2021)
- <span id="page-108-14"></span>16. Li, J., Socher, R., Hoi, S.C.: Dividemix: Learning with noisy labels as semisupervised learning. In: ICLR (2019)
- <span id="page-108-0"></span>17. Liu, X., Guo, X., Liu, Y., Yuan, Y.: Consolidated domain adaptive detection and localization framework for cross-device colonoscopic images. Medical image analysis **71**, 102052 (2021)
- 18. Liu, X., Li, W., Yuan, Y.: Decoupled unbiased teacher for source-free domain adaptive medical object detection. IEEE Trans. Neural Netw. Learn. Syst. (2023)
- <span id="page-108-1"></span>19. Liu, X., Yuan, Y.: A source-free domain adaptive polyp detection framework with style diversification flow. IEEE Transactions on Medical Imaging **41**(7), 1897–1908 (2022)
- <span id="page-108-4"></span>20. Luo, X.: SSL4MIS. <https://github.com/HiLab-git/SSL4MIS> (2020)
- <span id="page-108-11"></span>21. Luo, X., Hu, M., Song, T., Wang, G., Zhang, S.: Semi-supervised medical image segmentation via cross teaching between cnn and transformer. In: MIDL. pp. 820– 833. PMLR (2022)
- <span id="page-108-12"></span>22. Luo, X., Wang, G., Liao, W., Chen, J., Song, T., Chen, Y., Zhang, S., Metaxas, D.N., Zhang, S.: Semi-supervised medical image segmentation via uncertainty rectified pyramid consistency. Med. Image Anal. **80**, 102517 (2022)
- <span id="page-108-8"></span>23. Mendel, R., Rauber, D., de Souza Jr, L.A., Papa, J.P., Palm, C.: Errorcorrecting mean-teacher: Corrections instead of consistency-targets applied to semi-supervised medical image segmentation. CIBM **154**, 106585 (2023)

- <span id="page-109-12"></span>24. Nichol, A.Q., Dhariwal, P.: Improved denoising diffusion probabilistic models. In: ICML. pp. 8162–8171. PMLR (2021)
- <span id="page-109-13"></span>25. Rombach, R., Blattmann, A., Lorenz, D., Esser, P., Ommer, B.: High-resolution image synthesis with latent diffusion models. In: CVPR. pp. 10684–10695 (2022)
- <span id="page-109-19"></span>26. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation. In: MICCAI. pp. 234–241. Springer (2015)
- <span id="page-109-5"></span>27. Sohn, K., Berthelot, D., Carlini, N., Zhang, Z., Zhang, H., Raffel, C.A., Cubuk, E.D., Kurakin, A., Li, C.L.: Fixmatch: Simplifying semi-supervised learning with consistency and confidence. NeurIPS **33** (2020)
- <span id="page-109-0"></span>28. Sun, L., Li, C., Ding, X., Huang, Y., Chen, Z., Wang, G., Yu, Y., Paisley, J.: Few-shot medical image segmentation using a global correlation network with discriminative embedding. CBM **140**, 105067 (2022)
- <span id="page-109-15"></span>29. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. NeurIPS **30** (2017)
- <span id="page-109-6"></span>30. Verma, V., Kawaguchi, K., Lamb, A., Kannala, J., Solin, A., Bengio, Y., Lopez-Paz, D.: Interpolation consistency training for semi-supervised learning. Neural Netw. **145**, 90–106 (2022)
- <span id="page-109-2"></span>31. Vu, T.H., Jain, H., Bucher, M., Cord, M., Pérez, P.: Advent: Adversarial entropy minimization for domain adaptation in semantic segmentation. In: CVPR. pp. 2517–2526 (2019)
- <span id="page-109-14"></span>32. Wang, X., Wang, W., Cao, Y., Shen, C., Huang, T.: Images speak in images: A generalist painter for in-context visual learning. In: CVPR. pp. 6830–6839 (2023)
- <span id="page-109-7"></span>33. Wang, Y., Xiao, B., Bi, X., Li, W., Gao, X.: Mcf: Mutual correction framework for semi-supervised medical image segmentation. In: CVPR. pp. 15651–15660 (2023)
- <span id="page-109-3"></span>34. Wang, Y., Wang, H., Shen, Y., Fei, J., Li, W., Jin, G., Wu, L., Zhao, R., Le, X.: Semi-supervised semantic segmentation using unreliable pseudo-labels. In: CVPR. pp. 4248–4257 (2022)
- <span id="page-109-8"></span>35. Wu, Y., Ge, Z., Zhang, D., Xu, M., Zhang, L., Xia, Y., Cai, J.: Mutual consistency learning for semi-supervised medical image segmentation. MIA **81**, 102530 (2022)
- <span id="page-109-16"></span>36. Xing, Z., Wan, L., Fu, H., Yang, G., Zhu, L.: Diff-unet: A diffusion embedded network for volumetric segmentation. arXiv preprint [arXiv:2303.10326](http://arxiv.org/abs/2303.10326) (2023)
- <span id="page-109-9"></span>37. Yang, L., Qi, L., Feng, L., Zhang, W., Shi, Y.: Revisiting weak-to-strong consistency in semi-supervised semantic segmentation. In: CVPR. pp. 7236–7246 (2023)
- <span id="page-109-1"></span>38. Yang, Q., Liu, X., Chen, Z., Ibragimov, B., Yuan, Y.: Semi-supervised medical image classification with temporal knowledge-aware regularization. In: MICCAI. pp. 119–129. Springer (2022)
- <span id="page-109-17"></span>39. Yu, L., Wang, S., Li, X., Fu, C.W., Heng, P.A.: Uncertainty-aware self-ensembling model for semi-supervised 3d left atrium segmentation. In: MICCAI. pp. 605–613. Springer (2019)
- <span id="page-109-4"></span>40. Zhang, R., Liu, S., Yu, Y., Li, G.: Self-supervised correction learning for semisupervised biomedical image segmentation. In: MICCAI. pp. 134–144. Springer (2021)
- <span id="page-109-10"></span>41. Zhang, X., Yao, L., Yuan, F.: Adversarial variational embedding for robust semisupervised learning. KDD (2019)
- <span id="page-109-11"></span>42. Zhang, Y., Li, C., Lin, X., Sun, L., Zhuang, Y., Huang, Y., Ding, X., Liu, X., Yu, Y.: Generator versus segmentor: Pseudo-healthy synthesis. In: MICCAI. pp. 150–160 (2021)
- <span id="page-109-18"></span>43. Zhu, Y., Yang, J., Liu, S., Zhang, R.: Inherent consistent learning for accurate semi-supervised medical image segmentation. In: MIDL (2023)

Image /page/110/Picture/0 description: A square button with a light gray background. In the center of the button, there is a circular icon with a bookmark symbol inside. Below the icon, the text "Check for updates" is displayed in a gray sans-serif font.

# <span id="page-110-0"></span>**Disentangled Hybrid Transformer for Identification of Infants with Prenatal Drug Exposure**

Jiale Cheng<sup>1,2</sup>, Zhengwang Wu<sup>1</sup>, Xinrui Yuan<sup>1</sup>, Li Wang<sup>1</sup>, Weili Lin<sup>1</sup>, Karen Grewen<sup>3</sup>, and Gang Li<sup>1( $\boxtimes$ )</sup>

<sup>1</sup> Department of Radiology and Biomedical Research Imaging Center, University of North Carolina at Chapel Hill, Chapel Hill, NC 27599, USA

gang\<EMAIL>

 $<sup>2</sup>$  Joint Department of Biomedical Engineering, University of North Carolina at Chapel Hill and</sup> North Carolina State University, Chapel Hill, NC 27599, USA

<sup>3</sup> Department of Psychiatry, University of North Carolina at Chapel Hill, Chapel Hill,

NC 27599, USA

**Abstract.** Prenatal drug exposure, which occurs during a time of extraordinary and critical brain development, is typically associated with cognitive, behavioral, and physiological deficits during infancy, childhood, and adolescence. Early identifying infants with prenatal drug exposures and associated biomarkers using neuroimages can help inform earlier, more effective, and personalized interventions to greatly improve later cognitive outcomes. To this end, we propose a novel deep learning model called disentangled hybrid volume-surface transformer for identifying individual infants with prenatal drug exposures. Specifically, we design two distinct branches, a volumetric network for learning non-cortical features in 3D image space, and a surface network for learning features on the highly convoluted cortical surface manifold. To better capture long-range dependency and generate highly discriminative representations, image and surface transformers are respectively employed for the volume and surface branches. Then, a disentanglement strategy is further proposed to separate the representations from two branches into complementary variables and common variables, thus removing redundant information and boosting expressive capability. After that, the disentangled representations are concatenated to a classifier to determine if there is an existence of prenatal drug exposures. We have validated our method on 210 infant MRI scans and demonstrated its superior performance, compared to ablated models and state-of-the-art methods.

**Keywords:** Prenatal Drug Exposure · Cortical Surface · Transformer

## **1 Introduction**

Prenatal drug exposure is a significant public concern and occurs during a time of extremely dynamic and critical brain development. It can lead to long-term cognitive and behavioral disadvantages that may persist throughout an individual's life and potentially

into the next generation, underscoring the importance of early prognostication of infants who are at risk for poor developmental outcomes [\[1\]](#page-118-0). Noninvasive MR imaging holds great potential in early identifying infants with prenatal drug exposure and revealing brain structural abnormalities and biomarkers associated with prenatal drug exposure. This will help inform earlier, more effective, and personalized interventions to greatly improve later cognitive outcomes in this highly vulnerable population.

However, this is very challenging because the intrinsic patterns for identifying an individual with prenatal drug exposure from the normal ones are overwhelmed by the rapid and complex brain development. Conventional methods [\[2,](#page-118-1) [3\]](#page-118-2) designed for neuroimage-based brain disorder diagnosis for adults and older children typically learn image-based features and only suit the scenario with subtle longitudinal brain changes and thus typically fail to work on dynamic infant brains. To tackle this issue, previous studies [\[4–](#page-118-3)[6\]](#page-118-4) take advantages of cortical surface-based features [\[7\]](#page-118-5) instead of imagebased features to capture the dynamic and complex neurobiological changes of the cerebral cortex during infancy, e.g., for infant cognition prediction [\[4\]](#page-118-3). As prenatal drug exposure can affect both cortical and deep noncortical regions, a flexible framework that can leverage cortical surfaces to capture complex, subtle cortical abnormal developmental patterns and MRI volumes to identify deficits in noncortical regions is critically desired.

Therefore, in this paper, we propose a novel deep learning method called disentangled hybrid volume-surface transformer (DHT) and apply it to identify infants with prenatal drug exposure, by taking advantages of both cortical surface-based representation and volumetric image-based representation. To this end, we transform them into an embedding space, where the regularity and variability of infant brain representations of surfaces and volumes can be effectively measured. Specifically, the Vision Transformer [\[8\]](#page-118-6) and Spherical Surface Transformer [\[9\]](#page-118-7) are chosen as the basic models to boost the discrimination capabilities for the volumed-based data and surface-based data, respectively, by leveraging their superior capabilities in modeling the long-range dependency. The motivation is that the Vision Transformer is well suitable for learning image-related features, especially for non-cortical regions, while the Spherical Surface Transformer is ideally capable of learning complex features on the cortical surface manifold with an intrinsic spherical topology. Then, we minimize the redundancy between surface-based and volume-based information and extract their complementary information to boost identification accuracy by separating their shared information from their specific information. To achieve this, we disentangle the latent variables of two encoders into representationshared codes and representation-specific codes and enforce the representation-shared codes obtained from different representations to be as similar as possible, while the representation-specific codes to be different from each other as much as possible. This approach thus helps to not only offer a general, unified, and comparable embedding space by unifying hybrid representations in a single embedding space, but also effectively extract discriminative information for classification. To validate its effectiveness, we evaluated our method on 210 infant MRI scans to identify infants with prenatal opioid exposure and demonstrated its superior performance, compared to ablated models and state-of-the-art methods.

#### **2 Method**

#### **2.1 Overview**

As the schematic diagram shown in Fig. [1,](#page-112-0) our DHT works on the T1w and T2w MR images and cortical surfaces of both hemispheres (each with 40,962 vertices). The architecture has two main parts, including a hybrid Transformer-based encoding branch to produce effective feature representation for volume data and surface data in a unified embedding space, and a disentanglement block for semantically separating the redundant volume-surface shared information and the representative volume-specific and surface-specific information, finally boosting the identification accuracy.

Image /page/112/Figure/4 description: The image displays the overall framework of a hybrid volume-surface transformer (DHT) model for brain analysis. The framework takes cortical surface data (left and right hemispheres) and T1w and T2w MRI volumes as input. The cortical surface data is processed by separate surface transformers (ESl and ESr), while the MRI volumes are processed by volume transformers (Ei1 and Ei2). These processed features are then passed through transformer layers (Os and Oi) and global average pooling. The outputs from the surface and volume pathways are combined through concatenation and averaging operations, along with a common code, to form a final representation. This representation is fed into a Multi-Layer Perceptron (MLP) to predict a category, with a loss function (L) that combines L1 and L2 losses. Below the main framework, detailed architectures for the Surface Transformer (b) and Volume Transformer (c) are shown. The Surface Transformer consists of linear mapping, spherical transformer layers with layer normalization and add operations, followed by spherical pooling, repeated five times. The Volume Transformer includes linear mapping, multi-head self-attention, layer normalization and add operations, followed by a feed-forward network, layer normalization and add operations, repeated eight times.

<span id="page-112-0"></span>**Fig. 1.** The schematic diagram of our DHT that learns to embed the hybrid volume-surface data into a unified embedded space in an end-to-end architecture and further leverages the complementary information to boost the discriminative representation extraction.

Our data can be formulated as  $(s_l, s_r, i_1, i_2, y)$ , where  $s_l$  and  $s_r$  are the cortical surface feature maps for left hemisphere and right hemisphere, respectively, while  $i_1$  and  $i_2$  are T1w image and T2w image, respectively; *y* is the category of the existence of prenatal drug exposure.

#### **2.2 Hybrid Volume-Surface Transformer**

For each component in the input, we employ a neural network as its respective encoder  $E_x$ , where  $x \in \{s_l, s_r, i_1, i_2\}$ . The encoding branches  $E_{s_l}$  and  $E_{s_r}$  for cortical surfaces are five spherical transformer blocks as illustrated in Fig. [1\(](#page-112-0)b). Each spherical transformer block adopts a spherical transformer layer [\[9\]](#page-118-7), which includes a 2-ring hexagonal multihead self-attention layer, followed by layer normalization [\[10\]](#page-119-0) and ReLU activation to extract vertex-wise representation, which are then downsampled by another spherical transformer layer and a hexagonal mean pooling layer [\[11,](#page-119-1) [12\]](#page-119-2) (except the last spherical transformer block) to serve as the input of the subsequent layer. On the other side, we design the encoding branches for  $E_{i_1}$  and  $E_{i_2}$  similarly as shown in Fig. [1\(](#page-112-0)c), each of which includes eight vision transformer blocks. Each block consists of a multi-head self-attention layer and a feed forward layer followed by the layer normalization and ReLU.

Based on the outputs from these four encoders, we propose a learnable spatial attention mechanism for localization of discriminative brain regions. Specifically, taken the surface branches as an example, let  $f_{s_i} = E_{s_i}(s_i)$  and  $f_{s_r} = E_{s_r}(s_r) \in \mathbb{R}^{162 \times C}$  be the vertex-wise representations (produced by the last spherical transformer block) for the left and right hemispheres, respectively. We first concatenate them as a matrix with the shape of  $324 \times C$ , where C is the dimension of the unified embedding space we created. Then, a self-attention operation  $[13]$   $O<sub>s</sub>$  is applied to capture cross-hemisphere long-range dependencies, which refines the vertex-wise representations from both hemispheric surfaces, resulting in a unified feature matrix  $O_s(f_{s_l}, f_{s_r}) \in \mathbb{R}^{324 \times C}$ . As shown in Fig. [1\(](#page-112-0)a),  $O_s(f_{s_l}, f_{s_r})$  is further input into a global average pool (*GAP*) layer to be a holistic feature vector  $h_s = GAP(O_s(f_{s_l}, f_{s_r})) \in \mathbb{R}^C$  representing the latent variable of the whole cerebral cortex. Similarly, we can obtain the latent variable for the volumebased data as  $h_i = \frac{GAP(O_i(f_{i_1}, f_{i_2}))}{P_i}$ . Notably, by using  $h_s$  and  $h_i$  to identify infants with prenatal drug exposure,  $\vec{\boldsymbol{O}_s}$  and  $\vec{\boldsymbol{O}_i}$  highlight discriminative cortex regions on the surface and deep brain regions in the volume, respectively.

#### **2.3 Latent Variable Disentanglement**

To better learn the combined information from surfaces and volumes, shared and complementary information should be separated. Here,  $h_m$ , where  $m \in \{s, i\}$ , is disentangled into two parts:  $h_m^{Spe}$  and  $h_m^{Com} \in \mathbb{R}^{C/2}$ .  $h_m^{Com}$  is the common code representing the shared information amongst surfaces and volumes, while  $h_m^{Spe}$  is the specific code representing the complementary information that differentiates one from the other. The basic requirements of the disentanglement are: 1) the concatenation of  $h_m^{Com}$  and  $h_m^{Spe}$  equals  $h_m$ ; 2)  $h_i^{Com}$  and  $h_s^{Com}$  should be as similar as possible; 3)  $h_i^{Spe}$  should differ from  $h_s^{Spe}$  as much as possible. Therefore,  $\mathcal{L}_1$  is defined as:

$$
\mathcal{L}_1 = \mathcal{L}_{Com} / \mathcal{L}_{Spe},\tag{1}
$$

$$
\mathcal{L}_{Com} = ||\boldsymbol{h}_s^{Com} - \boldsymbol{h}_i^{Com}||_2,
$$
\n(2)

$$
\mathcal{L}_{Spe} = ||\boldsymbol{h}_s^{Spe} - \boldsymbol{h}_i^{Spe}||_2.
$$
 (3)

Since each latent variable has been disentangled into the common code  $h_m^{Com}$  and the specific code  $h_m^{Spe}$ , the combined information is formed as  $h_{s,i} = (h_i^{Spe}, h_{s,i}^{Com}, h_s^{Spe})$ , where  $h_{s,i}^{Com} = (h_s^{Com} + h_i^{Com})$  /2. A multi-layer perceptron neural network (*MLP*) is then designed as a classifier to predict the category of each subject from  $h_{s,i}$ . Finally, the objective function to end-to-end optimize DHT is written as:

$$
L = \lambda_1 \mathcal{L}_1 + \lambda_2 \mathcal{L}_2,\tag{4}
$$

$$
\mathcal{L}_2 = -log(P(y|s_l, s_r, i_1, i_2; \theta)),\tag{5}
$$

where  $P(y|s_1, s_r, i_1, i_2; \theta)$  is the probability of correct prediction for input given the DHT parameter  $\theta$ , while  $\lambda_1$  and  $\lambda_2$  are the hyperparameters to balance the two loss terms.

#### **3 Experiments**

#### **3.1 Dataset and Preprocessing**

In this study, we verified the effectiveness of our proposed DHT model on the identification of infants with prenatal opioid exposure for its high risk and increasingly growing prevalence. Specifically, we used an in-house high-quality MRI dataset including 210 structural MRI scans (with both T1w and T2w images) (76 positive / 134 negative samples) acquired at different ages ranging from 6 to 439 days. The resolution of both T1w and T2w images is  $0.8 \times 0.8 \times 0.8$ *mm*<sup>3</sup>. All structural MR images were processed by a state-of-the-art infant-tailored pipeline [\(https://www.ibeat.cloud/\)](https://www.ibeat.cloud/) [\[14](#page-119-4)[–17\]](#page-119-5), including co-registration, intensity inhomogeneity correction, skull stripping, cerebellum removal, tissue segmentation, hemispheres separation, topological correction, and surface reconstruction. Eight types of morphological features, i.e., local gyrification index, average convexity, mean curvature, sulcal depth, cortical thickness, surface area, cortical volume, and myelin content were computed as the input feature for each vertex on the cortical surface. Then, all spherical surfaces were aligned onto their age-matched templates in the 4D Infant Cortical Surface Atlas [\(https://www.nitrc.org/projects/infantsurfatlas/\)](https://www.nitrc.org/projects/infantsurfatlas/) [\[14,](#page-119-4) [16\]](#page-119-6) and further resampled to have the same tessellation on the 6th subdivision of icosahedron with 40,962 vertices.

#### **3.2 Experimental Settings**

To validate our method, a 5-fold cross-validation strategy was employed. To quantitatively evaluate the performance, we applied four metrics to evaluate the classification performance, including accuracy (ACC), sensitivity (SEN), specificity (SPE), and the area under receiver operating characteristic curve (AUC), which are respectively defined as:  $ACC = (TP + TN)/(TP + TN + FP + FN)$ ,  $SEN = TP/(TP + FN)$ ,  $SPE =$  $TN/(TN + FP)$ , where TP, TN, FP and FN are denoted as true positive, true negative, false positive, and false negative values, respectively. ACC, SEN, and SPE are calculated using the default threshold of 0.5. AUC is calculated on all possible pairs of true positive rate ( $TPR = SEM$ ) and false positive rate ( $FPR = 1 - SPE$ ) by changing the thresholds performed on the prediction results from our trained DHT network. In the testing phase, the mean and standard deviation of the 5-fold results were reported.

In our implementation, the feature representations produced by the five spherical transformer blocks in both  $E_s$ , and  $E_s$ , have 32, 32, 64, 64, and 128 channels, respectively. Correspondingly, the latent space dimension for volume transformer is 128, i.e.,  $C =$ 128. The classifier was designed as a two-layer MLP with the ReLU activation function and the dimension of {128, 1}.

We implemented the model with PyTorch and accelerated by an NVIDIA GeForce RTX 3090 GPU. Adam was employed as optimizer with the weight decay of  $10^{-4}$ and the learning rate was cyclically tuned within  $[10^{-6}, 10^{-3}]$ . The batch size was set to 16. The maximum training epoch is 500. After comparison, we empirically set the hyperparameters as  $\lambda_1$ =0.001 and  $\lambda_2$ =1.0. During the training phase, we augmented the T1w and T2w images by random erasing [\[18\]](#page-119-7) with the probability of 0.9.

<span id="page-115-0"></span>**Table 1.** Classification results obtained by the competing volume-based methods, surface-based methods, and our DHT. Mean and standard deviation values (mean  $\pm$  std) of the testing results based on 5-fold cross validation were reported.

|               | Method          | ACC (%)         | AUC (%)         | SEN (%)         | SPE (%)         |
|---------------|-----------------|-----------------|-----------------|-----------------|-----------------|
| <i>Volume</i> | ResNet3D        | $73.1 
es 11.2$ | $64.0 
es 6.0$  | $51.7 
es 35.3$ | $76.0 
es 8.6$  |
|               | GFNet           | $68.9 
es 12.4$ | $55.8 
es 8.7$  | $51.9 
es 24.7$ | $75.7 
es 11.0$ |
|               | DAMIDL          | $72.5 
es 6.1$  | $61.0 
es 5.2$  | $51.3 
es 25.9$ | $74.5 
es 4.0$  |
| Surface       | <i>SUNet</i>    | $73.6 
es 3.5$  | $67.0 
es 4.7$  | $56.0 
es 16.4$ | $80.1 
es 8.5$  |
|               | UGSCNN          | $79.0 
es 4.3$  | $67.9 
es 14.6$ | $35.0 
es 23.1$ | $77.5 
es 4.0$  |
|               | S-Transformer   | $76.7 
es 2.2$  | $71.0 
es 8.4$  | $65.2 
es 9.4$  | $83.4 
es 6.9$  |
| <b>Both</b>   | <b>Proposed</b> | $80.8 
es 2.0$  | $78.3 
es 9.6$  | $78.6 
es 4.8$  | $88.1 
es 4.5$  |

#### **3.3 Results**

We compared our DHT with six baseline methods, including a conventional volumebased method (i.e., ResNet3D [\[19\]](#page-119-8)), two state-of-the-art volume-based methods for Alzheimer's disease diagnosis (i.e., GFNet [\[3\]](#page-118-2), DIMADL [\[2\]](#page-118-1)), and three advanced cortical surface-based methods (i.e., SUNet [\[11\]](#page-119-1), UGSCNN [\[20\]](#page-119-9), Spherical Transformer (S-Transformer) [\[4\]](#page-118-3)). We implemented the competing methods based on their released code and took their encoders' output as the latent representation with a two-layer perceptron as the classifier.

As shown in Table [1,](#page-115-0) our DHT method achieves better performance over baselines in identifying infants with prenatal drug exposure on all evaluation metrics (i.e., ACC  $= 80.8\%$ , SEN  $= 78.6\%$ , SPE  $= 88.1\%$ , and AUC  $= 78.3\%$ ). Additionally, all surfacebased methods (i.e., SUNet, UGSCNN, and S-Transformer) outperform volume-based methods (i.e., ResNet3D and DAMIDL). This indicates that surface-based methods can capture more discriminative cortex features under the overwhelming dramatic brain

development during infancy. Meanwhile, among the baselines, the transformer basedmethod (i.e., S-Transformer) also yields better results than other five methods in most cases (i.e.,  $SEN = 65.2\%$ ,  $SPE = 83.4\%$ , and  $AUC = 71.0\%$ ). The main reason could be that the transformer-based method is more suitable for the surface-based classification task by formulating the long-range dependency and leveraging the parameters more effectively and thus is less prone to overfitting with limited data size. Furthermore, compared with the advanced surface-based method UGSCNN, our DHT outperforms it by a minor margin (1.8%) in ACC. Nevertheless, our DHT has a superior improvement over UGSCNN on the sensitivity metric (SEN), which implies that our proposed method has much lower missed identification rate on the existence of prenatal drug exposure.

To further evaluate the effectiveness of the components used in our study, we further compared the proposed DHT method with its counterparts, i.e., the model with either volume data (*w/o Surface Data*) or surface data (*w/o Volume Data*), and the model without disentanglement (*w/o Disentanglement*). We performed *w/o Disentanglement* by setting  $\lambda_1=0$ . As shown in Table [2,](#page-116-0) each component proposed in our DHT contributes to the better identification performance. For instance, our DHT with disentanglement loss *L*<sup>1</sup> has higher accuracy than its counterpart *w/o Disentanglement*. These results indicate that using hybrid volume-surface data with disentanglement strategy is more effective in enhancing the discriminative features for identification of infants with prenatal drug exposure, thus achieving better performance.

| Component           | ACC(%)            | AUC $(\%)$        | $SEN$ $(\%)$      | $SPE(\%)$         |
|---------------------|-------------------|-------------------|-------------------|-------------------|
| w/o Surface Data    | $74.87 \pm 12.32$ | $71.36 \pm 12.95$ | $74.34 \pm 17.61$ | $81.60 \pm 10.98$ |
| w/o Volume Data     | $76.66 \pm 2.15$  | $71.02 \pm 8.38$  | $65.20 \pm 9.40$  | $83.41 \pm 6.85$  |
| w/o Disentanglement | $77.83 \pm 4.16$  | $73.96 \pm 6.86$  | $71.91 \pm 10.16$ | $85.96 \pm 5.48$  |
| <b>Proposed</b>     | $80.84 \pm 1.96$  | $78.31 \pm 9.59$  | $78.63 \pm 4.83$  | $88.14 \pm 4.51$  |

<span id="page-116-0"></span>**Table 2.** Ablation study of each component of DHT. Mean and standard deviation values (mean  $\pm$  std) of the testing results based on 5-fold cross validation were reported.

Based on our proposed model DHT, the prenatal opioid exposure identification rate of infants is about 80%, suggesting the plausible existence of imaging biomarkers. Moreover, our method can automatically identify potential abnormal locations in both MR images and cortical surfaces for researchers and doctors to conduct further analysis. That is, our method can suggest subject-specific discriminative brain regions, including relatively informative patches on both image volumes and cortical surfaces as shown in Fig. [2.](#page-117-0) Specifically, in Fig. [2\(](#page-117-0)a), by analyzing the attention map in the Transformer layer  $O_i$ , we highlighted 50 potential discriminative volume patches with the patch size of 16 of two randomly selected subjects, which cover  $\sim 2\%$  voxels in the whole image. It can be observed that most of selected patches are located at the deep non-cortical regions, which validates the effectiveness of our disentanglement mechanism that enforces the surface-based and volume-based representations to learn information from complementary brain regions. Moreover, we demonstrated the discriminative surface patches in Fig. [2\(](#page-117-0)b) by analyzing the attention map in the Transformer layer  $O_s$ , and provided the population-based importance distribution of different morphological features through Grad-Cam [\[21\]](#page-119-10) in Fig. [2\(](#page-117-0)c), which are in line with the findings in some conventional statistical studies. For example, in Fig. [2\(](#page-117-0)b), the postcentral gyrus and the lateral occipital cortex illustrated relatively high importance, while some studies have observed the developmental abnormality of these regions in children with prenatal opioid exposure [\[22,](#page-119-11) [23\]](#page-119-12).

Image /page/117/Figure/2 description: The image displays a three-part figure related to brain imaging. Part (a) shows three views (coronal, axial, and sagittal) of the brain with highlighted regions in red, indicating specific anatomical areas. Part (b) presents colorful surface renderings of the brain from different angles, with a color bar on the left indicating a scale from 0 to 1, likely representing some measured value. Part (c) is a bar chart comparing 'Left' and 'Right' values for several brain regions labeled LGI, CUR, CON, ARE, VOL, SDE, THI, and MYE. The x-axis of the bar chart ranges from 0.0 to 0.2, with bars representing different magnitudes for each region and hemisphere.

<span id="page-117-0"></span>**Fig. 2.** The illustrations of the discriminative regions of (a) the image volumes and (b) cortical surfaces, and (c) the importance distribution of morphological features on both hemispheres. Specifically, we calculated the local gyrification index (LGI), mean curvature (CUR), average convexity (CON), surface area (ARE), volume (VOL), sulcal depth (SDE), cortical thickness (THI), and myelin content (MYE) as features for each vertex.

## **4 Conclusion**

In this paper, we proposed the first disentangled hybrid volume-surface transformer and applied it for automatic identification of infants with prenatal drug exposure. By employing the surface-based representation for the cerebral cortex and image-based representation for deep noncortical regions, the abnormal changes affected by prenatal drug exposure can be effectively detected under the overwhelming dynamic and complicated

brain development during infancy. To boost the discriminative feature representation, we introduced the Spherical Transformer and Vision Transformer, respectively, which further embedded the hybrid data into a unified, comparable space. By disentangling their common and specific information, our DHT successfully captures the individualized representation of the infant brain for prenatal drug exposure identification. The superior identification rate compared to state-of-the-art methods demonstrates the advantages and effectiveness of our DHT, which is a powerful generic framework applicable for diagnosis of other brain disorders.

**Acknowledgments.** This work was supported in part by NIH grants (MH116225, MH123202, ES033518, AG075582, NS128534, and NS135574).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

#### **References**

- <span id="page-118-0"></span>1. Morie, K. P., Crowley, M. J., Mayes, L. C., Potenza, M. N.: Prenatal drug exposure from infancy through emerging adulthood: results from neuroimaging. Drug and Alcohol Dependence **198**, 39-53 (2019).
- <span id="page-118-1"></span>2. Zhu,W., Sun, L., Huang, J., Han, L., Zhang, D.: Dual attention multi-instance deep learning for Alzheimer's disease diagnosis with structural MRI. IEEE Transactions on Medical Imaging **40**(9), 2354-2366 (2021).
- <span id="page-118-2"></span>3. Zhang, S., Chen, X., Ren, B., Yang, H., Yu, Z., Zhang, X., Zhou, Y.: 3D global fourier network for Alzheimer's disease diagnosis using structural MRI. In Proceedings of Medical Image Computing and Computer Assisted Intervention, pp. 34–43 (2022).
- <span id="page-118-3"></span>4. Cheng, J., Zhang, X., Zhao, F., Wu, Z., Wang, Y., Huang, Y., Lin, W., Wang, L., Li, G.: Spherical transformer for quality assessment of pediatric cortical surfaces. In 2022 IEEE 19<sup>th</sup> International Symposium on Biomedical Imaging (ISBI), pp. 1–5 (2022).
- 5. Hu, D., Wang, F., Zhang, H., Wu, Z., Wang, L., Lin, W., Li, G., Shen, D., and UNC/UMN Baby Connectome Project Consortium: Disentangled intensive triplet autoencoder for infant functional connectome fingerprinting. In Proceedings of Medical Image Computing and Computer Assisted Intervention, pp. 72–82 (2020).
- <span id="page-118-4"></span>6. Yuan, X., Cheng, J., Zhao, F., Wu, Z., Wang, L., Lin, W., Zhang, Y., Li, G.: Multi-task joint prediction of infant cortical morphological and cognitive development. In International Conference on Medical Image Computing and Computer-Assisted Intervention (MICCAI), pp. 545–554 (2023).
- <span id="page-118-5"></span>7. Fischl, B., Martin I. S., and Anders M. D.: Cortical surface-based analysis: II: inflation, flattening, and a surface-based coordinate system. NeuroImage **9**(2), 195-207 (1999).
- <span id="page-118-6"></span>8. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., Uszkoreit, J., Houlsby, N.: An image is worth 16x16 words: Transformers for image recognition at scale. In International Conference on Learning Representations (2020).
- <span id="page-118-7"></span>9. Cheng, J., Zhang, X., Zhao, F., Wu, Z., Yuan, X., Gilmore, J.H., Wang, L., Lin, W., Li, G.: Spherical transformer on cortical surfaces. In Proceedings of International Machine Learning in Medical Imaging Workshop, pp. 406–415 (2022).

- <span id="page-119-0"></span>10. Ba, J. L., Kiros, J. R., Hinton, G. E.: Layer normalization. In Advances in Neural Information Processing Systems 2016 Deep Learning Symposium (2016).
- <span id="page-119-1"></span>11. Zhao, F., Wu, Z., Wang, L., Lin, W., Gilmore, J.H., Xia, S., Shen, D., Li, G.: Spherical deformable u-net: Application to cortical surface parcellation and development prediction. IEEE Transactions on Medical Imaging, **40**(4), 1217-1228 (2021).
- <span id="page-119-2"></span>12. Zhao, F., Xia, S., Wu, Z., Duan, D., Wang, L., Lin, W., Gilmore, J.H., Shen, D., Li, G.: Spherical U-Net on cortical surfaces: methods and applications. In Information Processing in Medical Imaging: 26th International Conference (IPMI 2019), pp. 855–866 (2019).
- <span id="page-119-3"></span>13. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L, Gomez, A.N., Kaiser, L, Polosukhin, I.: Attention is all you need. Advances in Neural Information Processing Systems, 30 (2017).
- <span id="page-119-4"></span>14. Li, G., Wang, L., Shi, F., Gilmore, J.H., Lin, W., Shen, D.: Construction of 4D high-definition cortical surface atlases of infants: Methods and applications*.* Medical Image Analysis **25**(1), 22-36 (2015).
- 15. Li, G., Wang, L., Yap, P.T., Wang, F., Wu, Z., Meng, Y., Dong, P., Kim, P., Shi, F., Rekik, I., Lin, W., Shen, D.: Computational neuroanatomy of baby brains: A review. NeuroImage **185**, 906-925 (2019).
- <span id="page-119-6"></span>16. Wu, Z., Li, W., Lin, W., Gilmore, J.H., Li, G., Shen, D.: Construction of 4D infant cortical surface atlases with sharp folding patterns via spherical patch-based group-wise sparse representation. Human Brain Mapping **40**(13), 3860-3880 (2019).
- <span id="page-119-5"></span>17. Wang, L., Wu, Z., Chen, L., Sun, Y., Lin, W., Li, G.: iBEAT V2. 0: a multisite-applicable, deep learning-based pipeline for infant cerebral cortical surface reconstruction. Nature Protocols, **18**, 1488–1509 (2023).
- <span id="page-119-7"></span>18. Zhong, Z., Zheng, L., Kang, G., Li, S., Yang, Y.: Random erasing data augmentation. In Proceedings of the AAAI Conference on Artificial Intelligence **34**(7), pp. 13001-13008 (2020).
- <span id="page-119-8"></span>19. He, K., Zhang, X., Ren, S., Sun, J: Deep residual learning for image recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pp. 770–778 (2016).
- <span id="page-119-9"></span>20. Jiang, C., Huang, J., Kashinath, K., Marcus, P., Niessner, M.: Spherical CNNs on unstructured grids. In International Conference on Learning Representations (2019).
- <span id="page-119-10"></span>21. Selvaraju, R. R., Cogswell, M., Das, A., Vedantam, R., Parikh, D., Batra, D.: Grad-cam: Visual explanations from deep networks via gradient-based localization. In Proceedings of the IEEE International Conference on Computer Vision, pp. 618–626 (2017).
- <span id="page-119-11"></span>22. Radhakrishnan, R., Vishnubhotla, R.V., Guckien, Z., Zhao, Y., Sokol, G.M., Haas, D.M., Sahasivam, S.: Thalamocortical functional connectivity in infants with prenatal opioid exposure correlates with severity of neonatal opioid withdrawal syndrome. Neuroradiology, **64**(8), 1649-1659 (2022).
- <span id="page-119-12"></span>23. Merhar, S. L., Kline, J. E., Braimah, A., Kline-Fath, B., Tkach, J. A., Mekibib, A., He, L., Parikh, N. A.: Prenatal opioid exposure is associated with smaller brain volumes in multiple regions. Pediatric Research, **90**(2), 397-402 (2021).

Image /page/120/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a segment removed, and a ribbon or flag shape is inside the circle. The text below the icon reads "Check for updates".

# <span id="page-120-0"></span>Diversified and Structure-Realistic Fundus Image Synthesis for Diabetic Retinopathy Lesion Segmentation

Xiaoyi Feng<sup>[1](http://orcid.org/0000-0002-7214-0569)</sup> [,](http://orcid.org/0009-0003-5291-8294) Minqing Zhang<sup>1</sup> , Mengxian He<sup>1,2</sup> , Mengdi Gao<sup>1</sup> , Hao Wei<sup>1</sup> **D**[,](http://orcid.org/0000-0002-5719-8826) and Wu Yuan<sup>1( $\boxtimes$ [\)](http://orcid.org/0000-0001-9405-519X) **D**</sup>

<sup>1</sup> The Chinese University of Hong Kong, Sha Tin, New Territories, <NAME_EMAIL>

<sup>2</sup> Johns Hopkins University, Baltimore, MD, USA

Abstract. Automated diabetic retinopathy (DR) lesion segmentation aids in improving the efficiency of DR detection. However, obtaining lesion annotations for model training heavily relies on domain expertise and is a labor-intensive process. In addition to classical methods for alleviating label scarcity issues, such as self-supervised and semi-supervised learning, with the rapid development of generative models, several studies have indicated that utilizing synthetic image-mask pairs as data augmentation is promising. Due to the insufficient labeled data available to train powerful generative models, however, the synthetic fundus data suffers from two drawbacks: 1) unrealistic anatomical structures, 2) limited lesion diversity. In this paper, we propose a novel framework to synthesize fundus with DR lesion masks under limited labels. To increase lesion variation, we designed a learnable module to generate anatomically plausible masks as the condition, rather than directly using lesion masks from the limited dataset. To reduce the difficulty of learning intricate structures, we avoid directly generating images solely from lesion mask conditions. Instead, we developed an inpainting strategy that enables the model to generate lesions only within the mask area based on easily accessible healthy fundus images. Subjective evaluations indicate that our approach can generate more realistic fundus images with lesions compared to other generative methods. The downstream lesion segmentation experiments demonstrate that our synthetic data resulted in the most improvement across multiple network architectures, surpassing state-ofthe-art methods.

**Keywords:** DR lesion segmentation  $\cdot$  Labeled data scarcity  $\cdot$  Data augmentation · Image-mask pair synthesis · Diffusion model

X. Feng and M. Zhang—Equally contribute to this paper.

-c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 77–86, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_8)\_8

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_8.](https://doi.org/10.1007/978-3-031-72390-2_8)

#### 1 Introduction

Diabetic retinopathy (DR) is one of the most widespread ophthalmic disorders, with global DR prevalence projected to reach approximately 160 million by 2045 [\[14\]](#page-129-0). Since DR can lead to a heightened risk of irreversible vision impairment, early detection and appropriate treatments of DR hold great clinical value [\[10\]](#page-129-1). The screening of DR typically involves analyzing DR lesions using fundus images, including microaneurysms (MAs), hemorrhages (HEs), soft exudates (SEs), and hard exudates (EXs) [\[12\]](#page-129-2). Therefore, automatic DR lesion segmentation is an important computer-aided diagnosis application with the potential to enhance the efficiency and accuracy of clinical DR screening [\[5\]](#page-128-0). With the advancement of deep learning, many approaches based on deep neural networks [\[5,](#page-128-0)[6,](#page-129-3)[8](#page-129-4)[,9](#page-129-5),[16\]](#page-129-6) have been proposed to tackle the DR lesion segmentation task, but they suffer from insufficient segmentation accuracy. This is because training models with satisfactory performance demands abundant labeled data. However, the public availability of annotated datasets for DR lesions remains scarce, with even the largest benchmark FGADR [\[21\]](#page-129-7) comprising only 1,741 annotated images. Such limited availability of annotated data poses considerable challenges for training well-performing segmentation methods.

Image /page/121/Figure/3 description: This image illustrates two methods for generating retinal images with lesions. The left side, labeled "Traditional Generation Method," shows a black image with colored blobs (red, yellow, blue) representing lesions, which are then processed through "Direct Generation" to produce a retinal image. The right side, labeled "Proposed Inpainting Method," starts with a retinal image, then shows a segmentation mask of lesions (red and green blobs on a black background), followed by a "Lesion Inpainting" step, which takes the original retinal image and the lesion mask as input to produce a final retinal image with lesions inpainted.

<span id="page-121-0"></span>Fig. 1. Comparison of the pipeline for traditional conditional generative approaches and the proposed method.

Recently, generative AI has gained significant popularity, especially diffusion models [\[7](#page-129-8)]. Empirical studies suggest the utilization of synthetically generated labeled data for data augmentation exhibits promising potential to alleviate the paucity of publicly available training data for segmentation task [\[2,](#page-128-1)[3](#page-128-2)[,13,](#page-129-9)[17\]](#page-129-10). However, in the domain of DR lesion segmentation, leveraging generative models to produce diverse and anatomically plausible synthetic labeled data is hindered by the limited availability of public data, giving rise to two critical challenges: 1) As illustrated in Fig. [1,](#page-121-0) during inference phase, conventional generative approaches directly leverage the limited lesion masks available in the training data to guide the image synthesis process. However, the scarcity of segmentation masks in publicly available datasets poses a considerable challenge for generating diverse synthetic data. 2) As shown in Fig. [1](#page-121-0) traditional generative methods directly employ DR lesion segmentation annotations as conditions, attempting to generate complete fundus images containing both lesion regions and complex

physiological structures in a single step. However, this approach demands substantial training data, as training on limited public datasets can lead to a lack of realism in generated physiological structures outside lesion areas.

Therefore, we proposed a two-stage realistic DR fundus image synthesis method, which is capable of synthesizing diverse DR images with realistic physiological structures. In stage I, to synthesize anatomically plausible DR lesion masks, our framework utilizes the anatomical structure information from healthy fundus images, including the region of interest (ROI), optic disc (OD), and vessel (VE), to guide the synthesis of the lesion masks. In stage II, to alleviate the challenge of limited training data, we propose a *Learnable Training Augmentation* (LTA) module. This module enables augmenting our training data in a learnable manner during the training of the second stage. Simultaneously, to mitigate the challenge of generating complex physiological structures in fundus images, as illustrated in Fig. [1,](#page-121-0) we devise an inpainting approach. Our approach generates DR lesions within the masked regions of readily available healthy fundus images, thereby circumventing the need to synthesize complex physiological structures.

In this study, our contributions are as follows: 1) We highlight two key drawbacks in generating paired DR lesion segmentation data, stemming from limited training data: insufficient lesion diversity and difficulties in synthesizing complex physiological structures. 2) To address the paucity of training data and two stemming drawbacks, we propose a two-stage approach for DR image generation that can synthesize diverse images with realistic physiological structures. 3) We perform comprehensive experiments validating the realism of our generated images and their efficacy in boosting downstream segmentation performance. Compared to state-of-the-art approaches, our method achieves the most realistic generation and highest segmentation gains.

#### 2 Method

As illustrated in Fig. [2,](#page-123-0) our approach consists of two stages that enable the synthesis of lesions at specified locations on healthy fundus images. In stage I, leveraging structural information as conditional inputs, we employ a diffusion model to generate diverse and anatomically plausible lesion masks. In stage II, guided by these generated masks, we task the conditional diffusion model to focus on generating lesions within the designated areas, avoiding the need to generate complex physiological structures. Concurrently, we introduce the LTA module, which enables learnable augmentation of the training data used in stage II training.

#### 2.1 Structure Guided Mask Synthesis

Inference. The distribution and structure of DR lesions on fundus images are closely related to their structures, namely the ROI, OD, and VE [\[18](#page-129-11)]. As illustrated in Fig. [2,](#page-123-0) in the inference phase of the first stage, to obtain anatomically plausible lesion masks  $\tilde{y}$ , we utilize a conditional diffusion model, leveraging the structures m as to generate diverse and anatomical coherent lesion masks  $\tilde{y}$  from Gaussian noise  $y_T$ .

Training. For the training of this conditional diffusion model, the forward process is the same as the unconditional diffusion model. Through the Markov chain, gaussian noise is gradually added to the original image  $y_0$  for a total of T timesteps, this process can be formulated as  $q(y_{1:T} | y_0)$ :

$$
q(y_t|y_{t-1}) = \mathcal{N}(y_t; \sqrt{1 - \beta_t} y_{t-1}, \beta_t \mathbf{I})
$$
  
$$
q(y_{1:T}|y_0) = \prod_{t=1}^T q(y_t|y_{t-1})
$$
 $(1)$ 

where  $\beta_t$  is the schedule-defined constant, t denotes the timestep and  $y_t$  indicates the noised image at timestep t. With eq.  $(2)$ , the corresponding  $y_t$  at any timestep t can be effectively sampled. Where  $\alpha_t = 1 - \beta_t$  and  $\bar{\alpha}_t := \prod_{i=1}^T \alpha_i$ 

<span id="page-123-1"></span>
$$
y_t = \sqrt{\bar{\alpha}} y_0 + \sqrt{1 - \bar{\alpha}} \epsilon; \epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})
$$
\n(2)

Image /page/123/Figure/6 description: This figure illustrates a two-stage deep learning model for synthesizing diabetic retinopathy (DR) lesion maps and performing conditional multi-lesion inpainting. Stage I, "Conditional DR Lesion Maps Synthesis," takes a retinal image 's' as input, processes it through a generator (epsilon) and discriminator (D) to produce a synthesized lesion map 'y~' and a mask 'm'. Stage II, "Conditional Multi-Lesion Inpainting," takes an original retinal image 'x' and a masked version 'xt' as input, along with the mask 'm' from Stage I. This stage also uses a generator (epsilon) and discriminator (D) to perform conditional insertion and mask operation, ultimately producing an inpainted retinal image 'x~'.

<span id="page-123-0"></span>Fig. 2. An image to illustrate our two-stage method: Conditional lesion map generation and conditional image inpainting.

In the reverse process of training, sampled  $y_t \in \mathbb{R}^{H \times W \times C}$  is fed into the network, while the structure  $s \in \mathbb{R}^{H \times W}$  will be fed into the network as a condition. Therefore, the model is trained by minimizing the loss  $\mathcal{L}_{condition}$  between the sampled noise  $\epsilon$  and the noise  $\epsilon_{\theta}(x_t, s)$  estimated by network  $\epsilon_{\theta}$ . Where the loss function  $\mathcal{L}_{condition}$  is defined as follows:

$$
\mathcal{L}_{condition} = E_{t,x,s}[\|\epsilon - \epsilon_{\theta}(x_t, s)\|]
$$
\n(3)

#### 2.2 Mask-Guided Lesion Inpainting

Inference. In the inference phase of the second stage, to circumvent the need for the model to generate complex physiological structures, we employ the generated lesion masks as guidance, allowing the conditional diffusion model to focus solely on generating lesions within the masked regions. Specifically, as shown in Fig. [2,](#page-123-0) we first merge the generated four channel lesion masks  $\tilde{y} \in \mathbb{R}^{H \times W \times 4}$  into a single binary mask  $m \in \mathbb{R}^{H \times W}$ . This mask m determines the regions that need to be modified in the healthy image  $x$ . With the well-trained inpainting model, we can generate the image with lesions  $\tilde{x}$ , where the region outside of m is the same as that of  $x$  and the region inside  $m$  contains corresponding lesions generated based on the four channel lesion maps. We follow [\[15](#page-129-12)] and use spatially adaptive normalization in the model architecture, This module can incorporate the masks as conditional inputs into the network decoder and guide the conditional generation.

Training. Obtaining a satisfactory inpainting model to inpaint lesions on the healthy image requires substantial data, yet public datasets for DR lesion segmentation are limited. Even the largest dataset, FGADR [\[21](#page-129-7)], contains only 1,741 images with lesion annotations. To enable the inpainting model to achieve effective performance with limited data, we devise the *Learnable Training Augmentation*, tailored to the characteristics of our inpainting approach, which can augment the for the training of inpainting model in a learnable manner.

According to the unique characteristics of the inpainting method, for each  $(x, y, m)$  in Our training dataset  $D = \{x^n, y^n, m^n\}$ , the region of x to be learned is determined by m, where  $m \in \mathbb{R}^{H \times W}$  = fuse( $y \in \mathbb{R}^{H \times W \times 4}$ ). For each channel c of lesion map y, which is denoted as  $y_c$  can be viewed as a combination of j connected regions r, where  $y_c = \{r_i | j = 1, 2, 3, \dots J\}$ . Therefore, an approach to achieve data augmentation is, while training with each data pair  $(x, y, m)$ , we randomly drop a proportion p of connected regions in y. Thus, by modifying y, we can obtain new data pairs  $(x, y_{new}, m_{new})$  for training. However, p as a hyperparameter, manually searching for the optimal value of it is not reasonable and time-consuming. Therefore, we follow the insight of Bayesian optimization [\[4\]](#page-128-3), optimizing p every epoch based on the LPIPS loss calculated during validation. Thus, the value of  $p$  can be upgraded and the LTA module can enable learnable augmentation for our training data with the optimized  $p$ . The approach can be formulated as the following equation:

$$
p^* = \underset{p \in (0.1,1)}{\text{arg min}} f(p) \tag{4}
$$

where p stands for the dropping ratio and  $f(r)$  means the LPIPS loss calculated in validation. The range of p is set to  $(0.1, 1)$ . In the forward process of training, we generate a noisy image  $\tilde{x}_t$  based on the original image  $x_0$ , and then replace the non-lesion regions of  $\tilde{x_t}$  with the original image  $x_0$  according to the mask m, resulting in the final noised image  $x_t$ . This process can be formulated as follows:

$$
\tilde{x}_t = \sqrt{\bar{\alpha}} x_0 + \sqrt{1 - \bar{\alpha}} \epsilon; \epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})
$$
  
$$
x_t = \tilde{x}_t \odot m + x_0 \odot (1 - m)
$$
 (5)

where the  $\epsilon$  denotes the added Gaussian noise. In the reverse process of inpainting training, the noised image  $x_t$  is input into the network encoder  $\epsilon$ , while the lesion mask  $y \in \mathcal{R}^{H \times W \times 4}$  is inserted into the network decoder  $D$  as a condition. The estimated noise is indicated as  $\mathcal{D}(\varepsilon(x_t), y)$ . The loss function of the inpainting model is as follows:

$$
\mathcal{L}_{Inpairting} = E_{t,x_0,m,y}[\|\epsilon - \mathcal{D}(\varepsilon(x_t), y)\|]
$$
\n(6)

#### 3 Experiments

#### 3.1 Implementation Details

| Methods       | Objective Metrics | Subjective Metrics |              |               |              |
|---------------|-------------------|--------------------|--------------|---------------|--------------|
|               | LPIPS (↓)         | Accuracy (↓)       | Recall (↓)   | Precision (↓) | F1-score (↓) |
| RetinaGAN [8] | 0.594             | 0.800              | 0.625        | 1.000         | 0.769        |
| SDM [15]      | 0.572             | 0.700              | 0.714        | 1.000         | 0.833        |
| Ours          | <b>0.523</b>      | <b>0.550</b>       | <b>0.545</b> | <b>0.600</b>  | <b>0.571</b> |

<span id="page-125-0"></span>Table 1. Comparison of image quality for different data generative methods.

Datasets. Our experiments were conducted on two public DR lesion segmentation datasets, FGADR [\[21](#page-129-7)] and OIA-DDR [\[11](#page-129-13)]. The FGADR dataset consists of 101 healthy images and 1741 images with lesion labels. The OIA-DDR dataset includes data for DR classification and DR lesion segmentation, with 757 images containing DR lesion annotations. The structure of the training images is obtained by fine-tuned SAM. To evaluate our method's effectiveness, we divided the FGADR dataset into 975 training, 244 validation, and 522 test images. For OIA-DDR, we split it into 383 training, 149 validation, and 225 test images. All images were margin-cropped and resized to  $1024 \times 1024$ .

Inpainting Training Protocol. During the training phase of our generative model. For each dataset, we utilized the training set for training and the validation set to compute the LIPIS loss between the generated images and the original images for validation. We set the diffusion step to 1000, batch size to 4, learning rate to 0.0001, and employed AdamW as the optimizer. Each stage of training the generative model was conducted on NVIDIA RTX 4090 GPUs.

Segmentation Experimental Settings. In this work, we conduct a comparative evaluation of our proposed approach against various data augmentation techniques across two different segmentation networks. The comparative methods encompass the traditional augmentation technique CutMix [\[19\]](#page-129-14), as well as three data generation approaches: RetinaGAN [\[8](#page-129-4)], SDM [\[15](#page-129-12)], and Polyp-DDPM [\[2\]](#page-128-1). Two segmentation networks are Dense UNet [\[1](#page-128-4)], a medical segmentation network designed based on CNN, Trans2U-Net [\[20\]](#page-129-15), a network specifically tailored for Retinal Lesion segmentation.

For the comparative experiments setting in Table [2,](#page-127-0) each network's baseline and CutMix [\[19\]](#page-129-14) methods are trained using the original training set. For the three generative approaches and the proposed approach, we generate the same number of data as the training sets: 975 images for FGADR and 383 images for OIA-DDR. We then combine the generated data with the original training set to form new training sets for these methods, and the segmentation models are trained on these new training sets.

Image /page/126/Figure/2 description: This image displays a grid of retinal fundus images, categorized into three rows: 'Healthy Images', 'Generated Masks', and 'Inpainted Images'. Each column presents a set of corresponding images. The 'Healthy Images' row shows six different retinal scans. The 'Generated Masks' row displays segmentation masks for each corresponding healthy image, highlighting areas of microaneurysms (MAs) in blue, hemorrhages (HEs) in red, hard exudates (EXs) in yellow, and soft exudates (SEs) in green. The 'Inpainted Images' row shows the retinal scans after the identified lesions have been inpainted or removed. A legend at the bottom clarifies the color coding for each type of lesion: blue for Microaneurysms (MAs), red for Hemorrhages (HEs), yellow for Hard Exudates (EXs), and green for Soft Exudates (SEs).

<span id="page-126-0"></span>Fig. 3. Display of generated fundus images with lesions, healthy images in the first row, generated lesion maps in the second row, and the results of inpainting in the third row. The legend at the bottom indicates the mask color for four types of retinal lesions.

#### 3.2 Image Quality Evaluation

To validate the realism of the images generated by our method, we evaluate it against two other well-performing methods using both objective and subjective metrics. As shown in Table [1,](#page-125-0) for objective evaluation, we employ the LPIPS as the evaluation metrics, and our method outperforms the other two methods. For the subjective evaluation, we randomly selected 40 images for each method, with 20 real images and 20 generated images. Professional ophthalmologists are tasked to discriminate which image is generated. Table [1](#page-125-0) presents the Accuracy, Precision, Recall, and F1 scores for this subjective assessment. The results demonstrate that the synthetic images produced by our approach exhibit the highest degree of realism, most closely resembling the real data distribution, thereby posing significant challenges for discrimination.

#### 3.3 Qualitative Analysis

To further illustrate the image quality of our proposed method, we present visualization results in Fig. [3,](#page-126-0) where the left three columns and right three columns indicate the images produced by our models trained on FGADR and OIA-DDR datasets, respectively. As shown in Fig. [3,](#page-126-0) our method generates diverse and anatomic plausible masks. Subsequently, our method inpaint lesions within masked regions while preserving the complex retinal physiological structure. Moreover, the distinction between lesion classes is noticeable.

| Methods          | FGADR [21]  |             |             |             |                    | OIA-DDR [11] |             |             |             |                    |
|------------------|-------------|-------------|-------------|-------------|--------------------|--------------|-------------|-------------|-------------|--------------------|
|                  | MA          | HE          | EX          | SE          | Mean               | MA           | HE          | EX          | SE          | Mean               |
| Dense UNet [1]   | 25.9        | 52.4        | 52.9        | 32.0        | 40.8               | 25.1         | 45.3        | 53.3        | 19.0        | 35.7               |
| +Cutmix [19]     | 25.2        | 55.4        | 58.8        | 33.3        | 43.2 (+2.4)        | 24.3         | 46.0        | 58.0        | 19.1        | 36.8 (+1.1)        |
| +Polyp-DDPM [2]  | 28.5        | 57.0        | 57.3        | 32.2        | 43.8 (+3.0)        | 27.4         | 48.4        | 56.2        | 18.3        | 37.6 (+1.9)        |
| +SDM [15]        | 28.3        | 58.5        | 60.3        | 35.5        | 45.7 (+4.9)        | 27.1         | 49.8        | 58.3        | 19.5        | 38.7 (+3.0)        |
| +RetinaGAN [8]   | 26.5        | 57.8        | 59.1        | 33.2        | 44.2 (+3.4)        | 26.3         | 48.2        | 58.4        | 19.1        | 38.0 (+2.3)        |
| +Ours (w/o LTA)  | 27.9        | 59.2        | 60.2        | 34.8        | 45.5 (+4.7)        | 28.4         | 51.6        | 61.3        | 21.1        | 40.6 (+4.9)        |
| +Ours            | <b>29.0</b> | <b>61.1</b> | <b>61.8</b> | <b>37.9</b> | <b>47.5 (+6.7)</b> | <b>30.6</b>  | <b>53.9</b> | <b>63.9</b> | <b>23.7</b> | <b>43.0 (+7.3)</b> |
| Trans2U-Net [20] | 32.7        | 66.0        | 61.4        | 35.8        | 49.0               | 32.6         | 53.7        | 54.7        | 41.3        | 45.6               |
| +Cutmix [19]     | 32.4        | 66.8        | 63.1        | 36.3        | 49.7 (+0.7)        | 32.8         | 54.9        | 56.2        | 41.6        | 46.4 (+0.8)        |
| +Polyp-DDPM [2]  | 33.2        | 67.9        | 64.3        | 36.6        | 50.5 (+1.5)        | 33.4         | 56.0        | 55.8        | 41.8        | 46.8 (+1.2)        |
| +SDM [15]        | 33.4        | 68.5        | 65.3        | 37.4        | 51.2 (+2.2)        | 33.1         | 56.9        | 57.3        | 42.6        | 47.5 (+1.9)        |
| +RetinaGAN [8]   | 31.9        | 67.2        | 64.8        | 36.5        | 50.1 (+1.1)        | 32.7         | 55.8        | 58.4        | 42.3        | 47.3 (+1.7)        |
| +Ours (w/o LTA)  | 32.7        | 69.4        | 65.8        | 38.0        | 52.0 (+3.0)        | 34.6         | 58.2        | 59.7        | 43.9        | 49.1 (+3.5)        |
| +Ours            | <b>33.9</b> | <b>72.1</b> | <b>67.1</b> | <b>39.6</b> | <b>53.2 (+4.2)</b> | <b>36.2</b>  | <b>60.4</b> | <b>61.1</b> | <b>45.8</b> | <b>50.9 (+5.3)</b> |

<span id="page-127-0"></span>Table 2. Comparison of different data augmentation methods for enhancing three lesion segmentation baselines.

#### 3.4 Downstream Lesion Segmentation Task

The Dice scores of the downstream segmentation result are presented in Table [2.](#page-127-0) Our proposed method consistently outperforms other data augmentation methods in enhancing the segmentation performance across two networks. Specifically, on the FGADR dataset, our method improved the performance of Dense UNet by 6.7%, surpassing the second-best method, SDM, by 1.8%. Moreover, our method enhances Trans2U-Net gain a 4.2% improvement, surpassing the secondbest method, SDM, by 2.0%. On the OIA-DDR dataset, our method resulted in a 7.3% performance gain for Dense UNet, surpassing the second-best method, SDM, by 4.3%. Furthermore, our method contributed to a 5.3% enhancement for

Trans2U-Net, surpassing the second-best method, SDM, by 3.4%. As illustrated in Table [2,](#page-127-0) we conducted ablation experiments to evaluate the effectiveness of the LTA module. Our experimental results demonstrate that the LTA module consistently improves the performance of segmentation models on both of the two datasets.

#### 4 Conclusion

In this work, we underscore two critical limitations in generating paired DR lesion segmentation data when faced with scarce training data and stemming drawbacks: The lack of lesion variability and the challenges in synthesizing intricate physiological structures. To mitigate the limitations of data scarcity in DR lesion segmentation, we proposed a novel pipeline that can transform healthy fundus images into realistic lesion-containing images under limited public data. The extensive experiment results on two DR lesion segmentation datasets and evaluation of the image quality of different generative methods demonstrate that our method outperforms others in terms of both image quality and boosting the performance of segmentation models.

Acknowledgments. This work was supported in part by the Research Grants Council (RGC) of Hong Kong SAR (GRF14203821, GRF14216222), the Innovation and Technology Fund (ITF) of Hong Kong SAR (ITS/240/21, ITS/252/23), and the Science, Technology, and Innovation Commission (STIC) of Shenzhen Municipality (SGDX20220530111005039), and the Strategic Seed Funding for Collaborative Research Scheme of CUHK (SSFCRS 3133341).

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

#### References

- <span id="page-128-4"></span>1. Cai, S., Tian, Y., Lui, H., Zeng, H., Wu, Y., Chen, G.: Dense-unet: a novel multiphoton in vivo cellular image segmentation model based on a convolutional neural network. Quantitative imaging in medicine and surgery 10(6), 1275 (2020)
- <span id="page-128-1"></span>2. Dorjsembe, Z., Pao, H.K., Xiao, F.: Polyp-ddpm: Diffusion-based semantic polyp synthesis for enhanced segmentation. arXiv preprint [arXiv:2402.04031](http://arxiv.org/abs/2402.04031) (2024)
- <span id="page-128-2"></span>3. Du, Y., Jiang, Y., Tan, S., Wu, X., Dou, Q., Li, Z., Li, G., Wan, X.: Arsdm: colonoscopy images synthesis with adaptive refinement semantic diffusion models. In: International conference on medical image computing and computer-assisted intervention. pp. 339–349. Springer (2023)
- <span id="page-128-3"></span>4. Frazier, P.I.: A tutorial on bayesian optimization. arXiv preprint [arXiv:1807.02811](http://arxiv.org/abs/1807.02811) (2018)
- <span id="page-128-0"></span>5. Guo, T., Yang, J., Yu, Q.: Diabetic retinopathy lesion segmentation using deep multi-scale framework. Biomedical Signal Processing and Control 88, 105050 (2024)

- <span id="page-129-3"></span>6. He, A., Wang, K., Li, T., Bo, W., Kang, H., Fu, H.: Progressive multiscale consistent network for multiclass fundus lesion segmentation. IEEE transactions on medical imaging 41(11), 3146–3157 (2022)
- <span id="page-129-8"></span>7. Ho, J., Jain, A., Abbeel, P.: Denoising diffusion probabilistic models. Advances in neural information processing systems 33, 6840–6851 (2020)
- <span id="page-129-4"></span>8. Hou, B.: High-fidelity diabetic retina fundus image synthesis from freestyle lesion maps. Biomedical Optics Express  $14(2)$ , 533–549 (2023)
- <span id="page-129-5"></span>9. Huang, S., Li, J., Xiao, Y., Shen, N., Xu, T.: Rtnet: relation transformer network for diabetic retinopathy multi-lesion segmentation. IEEE Transactions on Medical Imaging 41(6), 1596–1607 (2022)
- <span id="page-129-1"></span>10. Lawrenson, J., Bourmpaki, E., Bunce, C., Stratton, I., Gardner, P., Anderson, J., Group, E.S.: Trends in diabetic retinopathy screening attendance and associations with vision impairment attributable to diabetes in a large nationwide cohort. Diabetic Medicine 38(4), e14425 (2021)
- <span id="page-129-13"></span>11. Li, T., Gao, Y., Wang, K., Guo, S., Liu, H., Kang, H.: Diagnostic assessment of deep learning algorithms for diabetic retinopathy screening. Information Sciences 501, 511 – 522 (2019). [https://doi.org/10.1016/j.ins.2019.06.011,](https://doi.org/10.1016/j.ins.2019.06.011) [http://](http://www.sciencedirect.com/science/article/pii/S0020025519305377) [www.sciencedirect.com/science/article/pii/S0020025519305377](http://www.sciencedirect.com/science/article/pii/S0020025519305377)
- <span id="page-129-2"></span>12. Salamat, N., Missen, M.M.S., Rashid, A.: Diabetic retinopathy techniques in retinal images: A review. Artificial intelligence in medicine 97, 168–188 (2019)
- <span id="page-129-9"></span>13. Shrivastava, A., Fletcher, P.T.: Nasdm: Nuclei-aware semantic histopathology image generation using diffusion models. arXiv preprint [arXiv:2303.11477](http://arxiv.org/abs/2303.11477) (2023)
- <span id="page-129-0"></span>14. Teo, Z.L., Tham, Y.C., Yu, M., Chee, M.L., Rim, T.H., Cheung, N., Bikbov, M.M., Wang, Y.X., Tang, Y., Lu, Y., et al.: Global prevalence of diabetic retinopathy and projection of burden through 2045: systematic review and meta-analysis. Ophthalmology 128(11), 1580–1591 (2021)
- <span id="page-129-12"></span>15. Wang, W., Bao, J., Zhou, W., Chen, D., Chen, D., Yuan, L., Li, H.: Semantic image synthesis via diffusion models. arXiv preprint [arXiv:2207.00050](http://arxiv.org/abs/2207.00050) (2022)
- <span id="page-129-6"></span>16. Yin, M., Soomro, T.A., Jandan, F.A., Fatihi, A., Ubaid, F.B., Irfan, M., Afifi, A.J., Rahman, S., Telenyk, S., Nowakowski, G.: Dual-branch u-net architecture for retinal lesions segmentation on fundus image. IEEE Access 11, 130451–130465 (2023)
- <span id="page-129-10"></span>17. Yu, X., Li, G., Lou, W., Liu, S., Wan, X., Chen, Y., Li, H.: Diffusion-based data augmentation for nuclei image segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 592–602. Springer (2023)
- <span id="page-129-11"></span>18. Yu, Z., Yan, R., Yu, Y., Ma, X., Liu, X., Liu, J., Ren, Q., Lu, Y.: Multiple lesions insertion: boosting diabetic retinopathy screening through poisson editing. Biomedical Optics Express 12(5), 2773–2789 (2021)
- <span id="page-129-14"></span>19. Yun, S., Han, D., Oh, S.J., Chun, S., Choe, J., Yoo, Y.: Cutmix: Regularization strategy to train strong classifiers with localizable features. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 6023–6032 (2019)
- <span id="page-129-15"></span>20. Zhang, L., Fang, Z., Li, T., Xiao, Y., Zhou, J.T., Yang, F.: Retinal multi-lesion segmentation by reinforcing single-lesion guidance with multi-view learning. Biomedical Signal Processing and Control 86, 105349 (2023)
- <span id="page-129-7"></span>21. Zhou, Y., Wang, B., Huang, L., Cui, S., Shao, L.: A benchmark for studying diabetic retinopathy: segmentation, grading, and transferability. IEEE Transactions on Medical Imaging 40(3), 818–828 (2020)