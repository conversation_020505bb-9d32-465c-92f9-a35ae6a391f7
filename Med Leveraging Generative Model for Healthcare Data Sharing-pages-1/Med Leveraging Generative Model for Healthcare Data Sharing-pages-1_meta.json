{"table_of_contents": [{"title": "Medical Image Computing \nand Computer Assisted \nIntervention – MICCAI 2024", "heading_level": null, "page_id": 0, "polygon": [[76.484375, 169.192626953125], [388.19112627986345, 169.192626953125], [388.19112627986345, 268.526123046875], [76.484375, 268.526123046875]]}, {"title": "MICCAI", "heading_level": null, "page_id": 0, "polygon": [[79.**********, 501.1878515185602], [213.99317406143342, 501.1878515185602], [213.99317406143342, 530.**********], [79.**********, 530.**********]]}, {"title": "Lecture Notes in Computer Science 15012", "heading_level": null, "page_id": 1, "polygon": [[51.058020477815695, 57.021372328458945], [401.7064846416382, 57.021372328458945], [401.7064846416382, 74.21514892578125], [51.058020477815695, 74.21514892578125]]}, {"title": "Editorial Board Members", "heading_level": null, "page_id": 1, "polygon": [[51.80887372013652, 150.80652418447696], [177.2013651877133, 150.80652418447696], [177.2013651877133, 164.958740234375], [51.80887372013652, 164.958740234375]]}, {"title": "Medical Image Computing\nand Computer Assisted\nIntervention – MICCAI 2024", "heading_level": null, "page_id": 3, "polygon": [[51.80887372013652, 194.43310546875], [382.8515625, 194.43310546875], [382.8515625, 284.32177734375], [51.80887372013652, 284.32177734375]]}, {"title": "Preface", "heading_level": null, "page_id": 5, "polygon": [[203.349609375, 57.77165354330709], [250.03412969283275, 57.77165354330709], [250.03412969283275, 71.7318115234375], [203.349609375, 71.7318115234375]]}, {"title": "MICCAI 2024 Organization", "heading_level": null, "page_id": 10, "polygon": [[141.160409556314, 56.75036621093751], [313.10580204778154, 56.75036621093751], [313.10580204778154, 72.2203369140625], [141.160409556314, 72.2203369140625]]}, {"title": "General Chairs", "heading_level": null, "page_id": 10, "polygon": [[51.80887372013652, 117.79415073115861], [134.40273037542661, 117.79415073115861], [134.40273037542661, 132.1461181640625], [51.80887372013652, 132.1461181640625]]}, {"title": "Program Committee Chairs", "heading_level": null, "page_id": 10, "polygon": [[51.058020477815695, 220.58267716535434], [198.22525597269623, 220.58267716535434], [198.22525597269623, 234.49218750000003], [51.058020477815695, 234.49218750000003]]}, {"title": "Keynote Chairs", "heading_level": null, "page_id": 10, "polygon": [[51.80887372013652, 362.38582677165357], [135.15358361774744, 362.38582677165357], [135.15358361774744, 376.16455078125], [51.80887372013652, 376.16455078125]]}, {"title": "Workshop Chairs", "heading_level": null, "page_id": 10, "polygon": [[51.80887372013652, 453.9201349831271], [146.4163822525597, 453.9201349831271], [146.4163822525597, 467.4251968503937], [51.80887372013652, 467.4251968503937]]}, {"title": "African Workshop Chairs", "heading_level": null, "page_id": 11, "polygon": [[37.54266211604095, 55.52080989876266], [175.69965870307166, 55.52080989876266], [175.69965870307166, 69.6148681640625], [37.54266211604095, 69.6148681640625]]}, {"title": "Challenges Chairs", "heading_level": null, "page_id": 11, "polygon": [[38.29351535836177, 183.06861642294714], [135.15358361774744, 183.06861642294714], [135.15358361774744, 197.03857421875], [38.29351535836177, 197.03857421875]]}, {"title": "African Challenges Commissioners", "heading_level": null, "page_id": 11, "polygon": [[37.54266211604095, 286.60742407199103], [221.50170648464163, 286.60742407199103], [221.50170648464163, 300.931640625], [37.54266211604095, 300.931640625]]}, {"title": "Tutorial Chairs", "heading_level": null, "page_id": 11, "polygon": [[36.791808873720136, 402.1507311586052], [120.88737201365187, 402.1507311586052], [120.88737201365187, 417.20068359375], [36.791808873720136, 417.20068359375]]}, {"title": "Open Data Chairs", "heading_level": null, "page_id": 11, "polygon": [[37.54266211604095, 481.68053993250845], [135.15358361774744, 481.68053993250845], [135.15358361774744, 495.69042968750006], [37.54266211604095, 495.69042968750006]]}, {"title": "Local Chairs", "heading_level": null, "page_id": 12, "polygon": [[51.058020477815695, 54.77052868391451], [122.38907849829351, 54.77052868391451], [122.38907849829351, 69.57415771484375], [51.058020477815695, 69.57415771484375]]}, {"title": "Diversity and Inclusion Chairs", "heading_level": null, "page_id": 12, "polygon": [[51.058020477815695, 159.80989876265468], [212.4914675767918, 159.80989876265468], [212.4914675767918, 173.31496062992127], [51.058020477815695, 173.31496062992127]]}, {"title": "AFRICAI Chairs", "heading_level": null, "page_id": 12, "polygon": [[51.058020477815695, 239.33970753655794], [144.91467576791808, 239.33970753655794], [144.91467576791808, 253.54467773437503], [51.058020477815695, 253.54467773437503]]}, {"title": "Career Development and Student Chairs", "heading_level": null, "page_id": 12, "polygon": [[51.80887372013652, 356.3835770528684], [265.05119453924914, 356.3835770528684], [265.05119453924914, 369.9765625], [51.80887372013652, 369.9765625]]}, {"title": "Communication Chairs", "heading_level": null, "page_id": 12, "polygon": [[51.80887372013652, 446.4173228346457], [175.69965870307166, 446.4173228346457], [175.69965870307166, 461.16796875000006], [51.80887372013652, 461.16796875000006]]}, {"title": "Sponsorship Chairs", "heading_level": null, "page_id": 12, "polygon": [[51.058020477815695, 525.947131608549], [156.92832764505118, 525.947131608549], [156.92832764505118, 539.9833984375], [51.058020477815695, 539.9833984375]]}, {"title": "Executive Associates", "heading_level": null, "page_id": 13, "polygon": [[36.791808873720136, 182.************], [146.4163822525597, 182.************], [146.4163822525597, 196.550048828125], [36.791808873720136, 196.550048828125]]}, {"title": "Women in MICCAI President", "heading_level": null, "page_id": 13, "polygon": [[37.54266211604095, 286.60742407199103], [194.47098976109214, 286.60742407199103], [194.47098976109214, 301.745849609375], [37.54266211604095, 301.745849609375]]}, {"title": "Program Committee", "heading_level": null, "page_id": 13, "polygon": [[36.791808873720136, 485.4319460067492], [146.4163822525597, 485.4319460067492], [146.4163822525597, 499.92431640625006], [36.791808873720136, 499.92431640625006]]}, {"title": "", "heading_level": null, "page_id": 14, "polygon": [[52.4755859375, 522.07080078125], [110.21484375, 522.07080078125], [110.21484375, 531.18994140625], [52.4755859375, 531.18994140625]]}, {"title": "Reviewers", "heading_level": null, "page_id": 18, "polygon": [[52.55972696245733, 57.77165354330709], [106.62116040955631, 57.77165354330709], [106.62116040955631, 69.2891845703125], [52.55972696245733, 69.2891845703125]]}, {"title": "Outstanding Area Chairs", "heading_level": null, "page_id": 33, "polygon": [[38.29351535836177, 258.755615234375], [170.44368600682594, 258.755615234375], [170.44368600682594, 271.457275390625], [38.29351535836177, 271.457275390625]]}, {"title": "Outstanding Reviewers", "heading_level": null, "page_id": 33, "polygon": [[38.29351535836177, 362.48583984375], [159.18088737201364, 362.48583984375], [159.18088737201364, 374.86181640625], [38.29351535836177, 374.86181640625]]}, {"title": "Honorable Mentions (Reviewers)", "heading_level": null, "page_id": 34, "polygon": [[51.80887372013652, 57.77165354330709], [223.00341296928326, 57.77165354330709], [223.00341296928326, 68.882080078125], [51.80887372013652, 68.882080078125]]}, {"title": "Contents – Part XII", "heading_level": null, "page_id": 37, "polygon": [[165.93856655290102, 54.77052868391451], [288.3276450511945, 54.77052868391451], [288.3276450511945, 71.650390625], [165.93856655290102, 71.650390625]]}, {"title": "Machine Learning - Efficient Learning Strategies II", "heading_level": null, "page_id": 37, "polygon": [[51.058020477815695, 117.79415073115861], [275.5631399317406, 117.79415073115861], [275.5631399317406, 132.04949381327336], [51.058020477815695, 132.04949381327336]]}, {"title": "Machine Learning - Efficient Learning\nStrategies II", "heading_level": null, "page_id": 45, "polygon": [[59.317406143344705, 312.1169853768279], [394.19795221843003, 313.6175478065242], [394.19795221843003, 357.27490234375], [59.317406143344705, 357.27490234375]]}, {"title": "Biophysics Informed Pathological\nRegularisation for Brain Tumour\nSegmentation", "heading_level": null, "page_id": 46, "polygon": [[107.37201365187713, 50.48095703125], [348.046875, 50.48095703125], [348.046875, 102.101806640625], [107.37201365187713, 102.101806640625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 46, "polygon": [[52.55972696245733, 482.01171875000006], [148.66894197952217, 482.01171875000006], [148.66894197952217, 495.03906250000006], [52.55972696245733, 495.03906250000006]]}, {"title": "2 Proposed Method", "heading_level": null, "page_id": 48, "polygon": [[51.80887372013652, 54.77052868391451], [180.20477815699658, 54.77052868391451], [180.20477815699658, 66.64300537109375], [51.80887372013652, 66.64300537109375]]}, {"title": "3 Experiment", "heading_level": null, "page_id": 51, "polygon": [[38.29351535836177, 53.0050048828125], [129.1467576791809, 53.0050048828125], [129.1467576791809, 66.3580322265625], [38.29351535836177, 66.3580322265625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 54, "polygon": [[52.55972696245733, 96.6466064453125], [139.111328125, 96.6466064453125], [139.111328125, 109.8367919921875], [52.55972696245733, 109.8367919921875]]}, {"title": "References", "heading_level": null, "page_id": 54, "polygon": [[52.55972696245733, 380.72412109375], [117.13310580204778, 380.72412109375], [117.13310580204778, 395.05419921875], [52.55972696245733, 395.05419921875]]}, {"title": "Class-Balancing Deep Active Learning\nwith Auto-Feature Mixing and Minority\nPush-Pull Sampling", "heading_level": null, "page_id": 57, "polygon": [[68.857421875, 52.272216796875], [357.9296875, 52.272216796875], [357.9296875, 103.24169921875], [68.857421875, 103.24169921875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 58, "polygon": [[53.173828125, 256.5961754780653], [147.91808873720134, 256.5961754780653], [147.91808873720134, 268.526123046875], [53.173828125, 268.526123046875]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 59, "polygon": [[38.29351535836177, 434.4619140625], [138.14453125, 434.4619140625], [138.14453125, 448.7919921875], [38.29351535836177, 448.7919921875]]}, {"title": "2.1 Framework Formulation", "heading_level": null, "page_id": 59, "polygon": [[38.29351535836177, 536.0751953125], [186.591796875, 536.0751953125], [186.591796875, 548.451171875], [38.29351535836177, 548.451171875]]}, {"title": "2.2 Auto-Feature Mixing", "heading_level": null, "page_id": 60, "polygon": [[52.55972696245733, 193.4560546875], [186.21160409556313, 193.4560546875], [186.21160409556313, 203.2265625], [52.55972696245733, 203.2265625]]}, {"title": "2.3 <PERSON> <PERSON><PERSON>-<PERSON><PERSON>", "heading_level": null, "page_id": 61, "polygon": [[38.29351535836177, 110.895263671875], [213.99317406143342, 110.895263671875], [213.99317406143342, 122.29418945312501], [38.29351535836177, 122.29418945312501]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 61, "polygon": [[38.29351535836177, 359.88037109375], [207.98634812286687, 359.88037109375], [207.98634812286687, 372.25634765625], [38.29351535836177, 372.25634765625]]}, {"title": "3.1 Dataset and Evaluation", "heading_level": null, "page_id": 61, "polygon": [[38.29351535836177, 381.37548828125], [184.7098976109215, 381.37548828125], [184.7098976109215, 393.10009765625], [38.29351535836177, 393.10009765625]]}, {"title": "3.2 Implementation Details", "heading_level": null, "page_id": 62, "polygon": [[52.55972696245733, 56.2710911136108], [198.22525597269623, 56.2710911136108], [198.22525597269623, 65.58453369140625], [52.55972696245733, 65.58453369140625]]}, {"title": "3.3 Comparison and Ablation Experiments", "heading_level": null, "page_id": 62, "polygon": [[52.55972696245733, 369.13835770528686], [277.06484641638224, 369.13835770528686], [277.06484641638224, 378.7**********], [52.55972696245733, 378.7**********]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 64, "polygon": [[51.80887372013652, 182.************], [138.90784982935153, 182.************], [138.90784982935153, 194.************], [51.80887372013652, 194.************]]}, {"title": "References", "heading_level": null, "page_id": 64, "polygon": [[52.55972696245733, 413.9**********], [117.13310580204778, 413.9**********], [117.13310580204778, 425.66845703125], [52.55972696245733, 425.66845703125]]}, {"title": "CoBooM: Codebook Guided\nBootstrapping for Medical Image\nRepresentation Learning", "heading_level": null, "page_id": 66, "polygon": [[108.12286689419794, 51.2137451171875], [347.1875, 51.2137451171875], [347.1875, 102.6717529296875], [108.12286689419794, 102.6717529296875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 66, "polygon": [[53.310580204778155, 459.53955078125006], [149.53125, 459.53955078125006], [149.53125, 472.56689453125006], [53.310580204778155, 472.56689453125006]]}, {"title": "2 Background", "heading_level": null, "page_id": 67, "polygon": [[38.29351535836177, 432.5078125], [129.8976109215017, 432.5078125], [129.8976109215017, 444.**********], [38.29351535836177, 444.**********]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 68, "polygon": [[52.55972696245733, 544.21728515625], [150.92150170648463, 544.21728515625], [150.92150170648463, 556.59326171875], [52.55972696245733, 556.59326171875]]}, {"title": "3.1 Quantizer", "heading_level": null, "page_id": 69, "polygon": [[38.29351535836177, 216.416748046875], [114.88054607508532, 216.416748046875], [114.88054607508532, 227.16430664062503], [38.29351535836177, 227.16430664062503]]}, {"title": "3.2 Loss Function", "heading_level": null, "page_id": 70, "polygon": [[52.55972696245733, 154.536865234375], [149.423828125, 154.536865234375], [149.423828125, 165.77294921875], [52.55972696245733, 165.77294921875]]}, {"title": "4 Experimental Setup", "heading_level": null, "page_id": 70, "polygon": [[52.55972696245733, 348.80712890625], [191.85546875, 348.80712890625], [191.85546875, 361.83447265625], [52.55972696245733, 361.83447265625]]}, {"title": "5 Results and Discussion", "heading_level": null, "page_id": 71, "polygon": [[38.29351535836177, 433.48486328125], [195.22184300341297, 433.48486328125], [195.22184300341297, 446.51220703125], [38.29351535836177, 446.51220703125]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 74, "polygon": [[51.80887372013652, 300.931640625], [138.251953125, 300.931640625], [138.251953125, 313.958984375], [51.80887372013652, 313.958984375]]}, {"title": "References", "heading_level": null, "page_id": 74, "polygon": [[51.80887372013652, 500.25000000000006], [117.3046875, 500.25000000000006], [117.3046875, 513.27734375], [51.80887372013652, 513.27734375]]}, {"title": "Continual Domain Incremental Learning\nfor Privacy-Aware Digital Pathology", "heading_level": null, "page_id": 77, "polygon": [[68.32764505119454, 50.969482421875], [358.359375, 50.969482421875], [358.359375, 84.7591552734375], [68.32764505119454, 84.7591552734375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 77, "polygon": [[38.88671875, 490.15380859375006], [134.599609375, 490.15380859375006], [134.599609375, 503.18115234375006], [38.88671875, 503.18115234375006]]}, {"title": "2 Method", "heading_level": null, "page_id": 79, "polygon": [[38.29351535836177, 408.15298087739035], [106.62116040955631, 408.15298087739035], [106.62116040955631, 420.783203125], [38.29351535836177, 420.783203125]]}, {"title": "3 Experimental Setup", "heading_level": null, "page_id": 81, "polygon": [[38.29351535836177, 377.1416015625], [177.4609375, 377.1416015625], [177.4609375, 389.517578125], [38.29351535836177, 389.517578125]]}, {"title": "4 Results and Discussion", "heading_level": null, "page_id": 84, "polygon": [[52.55972696245733, 390.1462317210349], [209.48805460750853, 390.1462317210349], [209.48805460750853, 401.2421875], [52.55972696245733, 401.2421875]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 85, "polygon": [[38.29351535836177, 227.97851562500003], [124.93164062499999, 227.97851562500003], [124.93164062499999, 242.30859375000003], [38.29351535836177, 242.30859375000003]]}, {"title": "References", "heading_level": null, "page_id": 85, "polygon": [[39.04436860068259, 504.80957031250006], [103.**********, 504.80957031250006], [103.**********, 519.**********], [39.04436860068259, 519.**********]]}, {"title": "Decoupled Training for Semi-supervised\nMedical Image Segmentation\nwith Worst-Case-Aware Learning", "heading_level": null, "page_id": 88, "polygon": [[83.359375, 50.7252197265625], [370.1706484641638, 50.7252197265625], [370.1706484641638, 102.101806640625], [83.359375, 102.101806640625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 89, "polygon": [[39.04436860068259, 54.59271240234375], [134.599609375, 54.59271240234375], [134.599609375, 66.48016357421875], [39.04436860068259, 66.48016357421875]]}, {"title": "2 Proposed Method", "heading_level": null, "page_id": 90, "polygon": [[52.55972696245733, 220.976318359375], [180.20477815699658, 220.976318359375], [180.20477815699658, 233.67797851562503], [52.55972696245733, 233.67797851562503]]}, {"title": "2.1 Method", "heading_level": null, "page_id": 90, "polygon": [[52.55972696245733, 444.**********], [119.990234375, 444.**********], [119.990234375, 455.95703125000006], [52.55972696245733, 455.95703125000006]]}, {"title": "2.2 Decoupled Training", "heading_level": null, "page_id": 91, "polygon": [[38.29351535836177, 296.3610798650169], [164.462890625, 296.3610798650169], [164.462890625, 307.608154296875], [38.29351535836177, 307.608154296875]]}, {"title": "2.3 Worst-Case-Aware Learning", "heading_level": null, "page_id": 92, "polygon": [[52.55972696245733, 155.30821147356582], [222.25255972696243, 155.30821147356582], [222.25255972696243, 165.5286865234375], [52.55972696245733, 165.5286865234375]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 92, "polygon": [[52.55972696245733, 559.7097862767155], [222.36328125, 559.7097862767155], [222.36328125, 570.27197265625], [52.55972696245733, 570.27197265625]]}, {"title": "3.1 Datasets and Evaluation Metrics", "heading_level": null, "page_id": 93, "polygon": [[39.04436860068259, 158.5264892578125], [231.6015625, 158.5264892578125], [231.6015625, 169.518310546875], [39.04436860068259, 169.518310546875]]}, {"title": "3.2 Implementation Details", "heading_level": null, "page_id": 93, "polygon": [[38.29351535836177, 417.85205078125], [184.7098976109215, 417.85205078125], [184.7098976109215, 428.92529296875], [38.29351535836177, 428.92529296875]]}, {"title": "3.3 Comparison with State-of-the-Art Methods", "heading_level": null, "page_id": 94, "polygon": [[52.55972696245733, 55.52080989876266], [298.84765625, 55.52080989876266], [298.84765625, 65.66595458984375], [52.55972696245733, 65.66595458984375]]}, {"title": "3.4 Ablation Studies", "heading_level": null, "page_id": 95, "polygon": [[38.29351535836177, 55.24407958984375], [150.283203125, 55.24407958984375], [150.283203125, 65.99163818359375], [38.29351535836177, 65.99163818359375]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 96, "polygon": [[52.55972696245733, 175.706298828125], [139.65870307167233, 175.706298828125], [139.65870307167233, 188.082275390625], [52.55972696245733, 188.082275390625]]}, {"title": "References", "heading_level": null, "page_id": 96, "polygon": [[52.55972696245733, 440.32421875], [117.13310580204778, 440.32421875], [117.13310580204778, 452.**********], [52.55972696245733, 452.**********]]}, {"title": "DiffRect: Latent Diffusion Label\nRectification for Semi-supervised Medical\nImage Segmentation", "heading_level": null, "page_id": 99, "polygon": [[63.0716723549488, 50.7252197265625], [362.87109375, 50.7252197265625], [362.87109375, 102.6717529296875], [63.0716723549488, 102.6717529296875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 99, "polygon": [[38.75244140625, 504.80957031250006], [134.814453125, 504.80957031250006], [134.814453125, 518.48828125], [38.75244140625, 518.48828125]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 101, "polygon": [[38.29351535836177, 168.866943359375], [137.0703125, 168.866943359375], [137.0703125, 181.568603515625], [38.29351535836177, 181.568603515625]]}, {"title": "2.1 Preliminary: Conditional DDPM", "heading_level": null, "page_id": 101, "polygon": [[38.29351535836177, 194.107421875], [231.26279863481227, 194.107421875], [231.26279863481227, 204.85498046875], [38.29351535836177, 204.85498046875]]}, {"title": "2.2 Label Context Calibration Module (LCC)", "heading_level": null, "page_id": 101, "polygon": [[38.29351535836177, 468.33300781250006], [276.2890625, 468.33300781250006], [276.2890625, 478.75488281250006], [38.29351535836177, 478.75488281250006]]}, {"title": "2.3 Latent Feature Rectification Module (LFR)", "heading_level": null, "page_id": 103, "polygon": [[38.29351535836177, 93.7154541015625], [284.5733788395904, 93.7154541015625], [284.5733788395904, 104.4630126953125], [38.29351535836177, 104.4630126953125]]}, {"title": "2.4 Loss Function", "heading_level": null, "page_id": 104, "polygon": [[52.55972696245733, 336.12598425196853], [149.419795221843, 336.12598425196853], [149.419795221843, 346.52734375], [52.55972696245733, 346.52734375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 104, "polygon": [[52.55972696245733, 542.26318359375], [148.66894197952217, 542.26318359375], [148.66894197952217, 552.68505859375], [52.55972696245733, 552.68505859375]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 104, "polygon": [[52.55972696245733, 564.9617547806524], [180.361328125, 564.9617547806524], [180.361328125, 574.18017578125], [52.55972696245733, 574.18017578125]]}, {"title": "3.2 Comparison with State-of-the-Art Methods", "heading_level": null, "page_id": 105, "polygon": [[38.29351535836177, 531.84130859375], [284.5733788395904, 531.84130859375], [284.5733788395904, 542.91455078125], [38.29351535836177, 542.91455078125]]}, {"title": "3.3 Further Analysis", "heading_level": null, "page_id": 106, "polygon": [[52.55972696245733, 526.6974128233971], [165.1877133105802, 526.6974128233971], [165.1877133105802, 537.3779296875], [52.55972696245733, 537.3779296875]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 107, "polygon": [[38.29351535836177, 258.104248046875], [125.57617187499999, 258.104248046875], [125.57617187499999, 272.10**********], [38.29351535836177, 272.10**********]]}, {"title": "References", "heading_level": null, "page_id": 107, "polygon": [[38.29351535836177, 466.37890625000006], [103.5546875, 466.37890625000006], [103.5546875, 480.70898437500006], [38.29351535836177, 480.70898437500006]]}, {"title": "Disentangled Hybrid Transformer\nfor Identification of Infants with Prenatal Drug\nExposure", "heading_level": null, "page_id": 110, "polygon": [[80.34129692832764, 54.0634765625], [375.1171875, 54.0634765625], [375.1171875, 105.1143798828125], [80.34129692832764, 105.1143798828125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 110, "polygon": [[52.55972696245733, 536.0751953125], [135.90443686006824, 536.0751953125], [135.90443686006824, 549.75390625], [52.55972696245733, 549.75390625]]}, {"title": "2 Method", "heading_level": null, "page_id": 112, "polygon": [[51.80887372013652, 57.64599609375001], [109.7314453125, 57.64599609375001], [109.7314453125, 69.1263427734375], [51.80887372013652, 69.1263427734375]]}, {"title": "2.1 Overview", "heading_level": null, "page_id": 112, "polygon": [[52.55972696245733, 82.53093363329585], [115.63139931740614, 82.53093363329585], [115.63139931740614, 92.331298828125], [52.55972696245733, 92.331298828125]]}, {"title": "2.2 Hybrid Volume-Surface Transformer", "heading_level": null, "page_id": 113, "polygon": [[38.29351535836177, 58.78588867187501], [220.0, 58.78588867187501], [220.0, 68.6378173828125], [38.29351535836177, 68.6378173828125]]}, {"title": "2.3 Latent Variable Disentanglement", "heading_level": null, "page_id": 113, "polygon": [[38.29351535836177, 416.40607424071993], [202.73037542662115, 416.40607424071993], [202.73037542662115, 426.6455078125], [38.29351535836177, 426.6455078125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 114, "polygon": [[51.80887372013652, 225.861572265625], [135.15358361774744, 225.861572265625], [135.15358361774744, 236.93481445312503], [51.80887372013652, 236.93481445312503]]}, {"title": "3.1 Dataset and Preprocessing", "heading_level": null, "page_id": 114, "polygon": [[52.55972696245733, 251.34420697412824], [187.71331058020476, 251.34420697412824], [187.71331058020476, 261.035400390625], [52.55972696245733, 261.035400390625]]}, {"title": "3.2 Experimental Settings", "heading_level": null, "page_id": 114, "polygon": [[51.80887372013652, 480.70898437500006], [169.6928327645051, 480.70898437500006], [169.6928327645051, 490.47949218750006], [51.80887372013652, 490.47949218750006]]}, {"title": "3.3 Results", "heading_level": null, "page_id": 115, "polygon": [[38.29351535836177, 432.83349609375], [92.35494880546075, 432.83349609375], [92.35494880546075, 443.90673828125], [38.29351535836177, 443.90673828125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 117, "polygon": [[38.29351535836177, 521.09375], [113.65234375, 521.09375], [113.65234375, 534.7724609375], [38.29351535836177, 534.7724609375]]}, {"title": "References", "heading_level": null, "page_id": 118, "polygon": [[52.55972696245733, 262.5984251968504], [110.3754266211604, 262.5984251968504], [110.3754266211604, 274.062744140625], [52.55972696245733, 274.062744140625]]}, {"title": "Diversified and Structure-Realistic Fundus\nImage Synthesis for Diabetic Retinopathy\nLesion Segmentation", "heading_level": null, "page_id": 120, "polygon": [[78.08873720136518, 51.25445556640625], [377.05078125, 51.25445556640625], [377.05078125, 102.101806640625], [78.08873720136518, 102.101806640625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 121, "polygon": [[39.04436860068259, 53.8192138671875], [134.40273037542661, 53.8192138671875], [134.40273037542661, 66.3580322265625], [39.04436860068259, 66.3580322265625]]}, {"title": "2 Method", "heading_level": null, "page_id": 122, "polygon": [[51.80887372013652, 385.64454443194603], [120.13651877133105, 385.64454443194603], [120.13651877133105, 397.00830078125], [51.80887372013652, 397.00830078125]]}, {"title": "2.1 Structure Guided Mask Synthesis", "heading_level": null, "page_id": 122, "polygon": [[51.80887372013652, 537.05224609375], [250.03412969283275, 537.05224609375], [250.03412969283275, 546.82275390625], [51.80887372013652, 546.82275390625]]}, {"title": "2.2 Mask-Guided Lesion Inpainting", "heading_level": null, "page_id": 124, "polygon": [[52.55972696245733, 56.2710911136108], [239.52218430034128, 56.2710911136108], [239.52218430034128, 65.************], [52.55972696245733, 65.************]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 125, "polygon": [[38.29351535836177, 152.1************], [134.70703125, 152.1************], [134.70703125, 164.2259521484375], [38.29351535836177, 164.2259521484375]]}, {"title": "3.1 Implementation Details", "heading_level": null, "page_id": 125, "polygon": [[38.29351535836177, 176.************], [183.95904436860067, 176.************], [183.95904436860067, 187.************], [38.29351535836177, 187.************]]}, {"title": "3.2 Image Quality Evaluation", "heading_level": null, "page_id": 126, "polygon": [[52.55972696245733, 425.3427734375], [208.7372013651877, 425.3427734375], [208.7372013651877, 435.11328125], [52.55972696245733, 435.11328125]]}, {"title": "3.3 Qualitative Analysis", "heading_level": null, "page_id": 127, "polygon": [[38.29351535836177, 54.02276611328125], [167.79296875, 54.02276611328125], [167.79296875, 66.56158447265625], [38.29351535836177, 66.56158447265625]]}, {"title": "3.4 Downstream Lesion Segmentation Task", "heading_level": null, "page_id": 127, "polygon": [[38.29351535836177, 475.49804687500006], [264.6875, 475.49804687500006], [264.6875, 487.87402343750006], [38.29351535836177, 487.87402343750006]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 128, "polygon": [[52.55972696245733, 133.6116943359375], [138.90784982935153, 133.6116943359375], [138.90784982935153, 145.8248291015625], [52.55972696245733, 145.8248291015625]]}, {"title": "References", "heading_level": null, "page_id": 128, "polygon": [[51.80887372013652, 410.361328125], [117.13310580204778, 410.361328125], [117.13310580204778, 422.7373046875], [51.80887372013652, 422.7373046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 23], ["Line", 16], ["Picture", 4], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 20948, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["Line", 9], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 9], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 11], ["Text", 2], ["SectionHeader", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 46], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 41], ["ListItem", 12], ["Text", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 27], ["Line", 13], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 46], ["Span", 45], ["Line", 22], ["SectionHeader", 5], ["Table", 3], ["Text", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 58], ["Span", 49], ["Line", 25], ["SectionHeader", 5], ["Table", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 6632, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 76], ["Span", 54], ["Line", 27], ["SectionHeader", 6], ["Table", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 6795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 92], ["Span", 56], ["Line", 28], ["Table", 3], ["SectionHeader", 3], ["TableOfContents", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 7419, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 43], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 43], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2357, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 587, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 131], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 136], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 64], ["TableCell", 14], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 43], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["TableCell", 74], ["Line", 32], ["SectionHeader", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["TableCell", 44], ["Line", 37], ["Text", 1], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 35], ["TableCell", 22], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableOfContents", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7874, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["TableCell", 47], ["Line", 37], ["TableOfContents", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9397, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["TableCell", 50], ["Line", 35], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10387, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 37], ["TableCell", 36], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2662, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["TableCell", 40], ["Line", 34], ["TableOfContents", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5556, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["TableCell", 24], ["Line", 21], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 39], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 41], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 61], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1758, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 44], ["Text", 3], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 480], ["Line", 97], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2142, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 44], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["TableCell", 80], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10807, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 66], ["Line", 48], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1304, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 42], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 49], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 34], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 42], ["Text", 10], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 608, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 61], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 49], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 49], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 53], ["Text", 6], ["SectionHeader", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 44], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 118], ["TableCell", 115], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 12274, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["TableCell", 54], ["Line", 39], ["Reference", 6], ["ListItem", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1530, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 47], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 44], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 67], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 869, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 43], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 45], ["TextInlineMath", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["TableCell", 223], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7868, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 44], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 808, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["TableCell", 222], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 41], ["Text", 3], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 51], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 51], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 39], ["Text", 8], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 608, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 58], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 62], ["TableCell", 32], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["TableCell", 163], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 39], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Line", 112], ["Span", 80], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 788, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 52], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 41], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 41], ["Text", 8], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 560, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 47], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 42], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 77], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1915, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["Line", 59], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 61], ["Text", 5], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 676, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["TableCell", 200], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["TableCell", 91], ["Line", 63], ["Table", 5], ["Text", 2], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 7, "llm_error_count": 5, "llm_tokens_used": 10784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 38], ["Text", 5], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 51], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 36], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 39], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 583, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 57], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 87], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 634], ["Line", 116], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2509, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 51], ["TextInlineMath", 6], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["TableCell", 234], ["Line", 41], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["TableCell", 355], ["Line", 58], ["Table", 5], ["Reference", 4], ["Caption", 3], ["TableGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 1, "llm_tokens_used": 16276, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 38], ["Text", 5], ["ListItem", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 51], ["ListItem", 20], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 50], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 110, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 40], ["Text", 9], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 570, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 111, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 45], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 112, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 86], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 772, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 113, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 45], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 114, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 38], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 115, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["TableCell", 96], ["Line", 39], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3912, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 116, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 41], ["TableCell", 25], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 117, "text_extraction_method": "pdftext", "block_counts": [["Span", 60], ["Line", 38], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 677, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 118, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 43], ["ListItem", 9], ["Reference", 8], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 119, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 42], ["ListItem", 14], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 120, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 40], ["Text", 7], ["Picture", 1], ["SectionHeader", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 568, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 121, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 41], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 644, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 122, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 42], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 123, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 44], ["Equation", 3], ["Text", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 124, "text_extraction_method": "pdftext", "block_counts": [["Span", 459], ["Line", 47], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 125, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["TableCell", 53], ["Line", 38], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1436, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 126, "text_extraction_method": "pdftext", "block_counts": [["Span", 59], ["Line", 30], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 127, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["TableCell", 339], ["Line", 37], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3675, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 128, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 40], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 129, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 49], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-1"}