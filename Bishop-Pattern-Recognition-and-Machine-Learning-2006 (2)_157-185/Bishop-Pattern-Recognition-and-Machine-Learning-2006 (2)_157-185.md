Image /page/0/Picture/0 description: The image is a title slide for a presentation or chapter. The number 3 is prominently displayed in red at the top. Below it, the text "Linear Models for Regression" is written in black, bold, sans-serif font. The background of the slide features a textured, abstract pattern that resembles rippling water or a metallic surface with reflections, rendered in shades of gold, brown, and white.

The focus so far in this book has been on unsupervised learning, including topics such as density estimation and data clustering. We turn now to a discussion of supervised learning, starting with regression. The goal of regression is to predict the value of one or more continuous *target* variables t given the value of a D-dimensional vector **x** of *input* variables. We have already encountered an example of a regression problem when we considered polynomial curve fitting in Chapter 1. The polynomial is a specific example of a broad class of functions called linear regression models, which share the property of being linear functions of the adjustable parameters, and which will form the focus of this chapter. The simplest form of linear regression models are also linear functions of the input variables. However, we can obtain a much more useful class of functions by taking linear combinations of a fixed set of nonlinear functions of the input variables, known as *basis functions*. Such models are linear functions of the parameters, which gives them simple analytical properties, and yet can be nonlinear with respect to the input variables.

Given a training data set comprising N observations  $\{x_n\}$ , where  $n = 1, \ldots, N$ , together with corresponding target values  $\{t_n\}$ , the goal is to predict the value of t for a new value of **x**. In the simplest approach, this can be done by directly constructing an appropriate function  $y(x)$  whose values for new inputs **x** constitute the predictions for the corresponding values of  $t$ . More generally, from a probabilistic perspective, we aim to model the predictive distribution  $p(t|\mathbf{x})$  because this expresses our uncertainty about the value of <sup>t</sup> for each value of **x**. From this conditional distribution we can make predictions of  $t$ , for any new value of  $x$ , in such a way as to minimize the expected value of a suitably chosen loss function. As discussed in Section 1.5.5, a common choice of loss function for real-valued variables is the squared loss, for which the optimal solution is given by the conditional expectation of  $t$ .

Although linear models have significant limitations as practical techniques for pattern recognition, particularly for problems involving input spaces of high dimensionality, they have nice analytical properties and form the foundation for more sophisticated models to be discussed in later chapters.

# **3.1. Linear Basis Function Models**

The simplest linear model for regression is one that involves a linear combination of the input variables

$$
y(\mathbf{x}, \mathbf{w}) = w_0 + w_1 x_1 + \ldots + w_D x_D \tag{3.1}
$$

where  $\mathbf{x} = (x_1, \dots, x_D)^T$ . This is often simply known as *linear regression*. The key property of this model is that it is a linear function of the parameters  $w_0, \ldots, w_D$ . It is also, however, a linear function of the input variables  $x_i$ , and this imposes significant limitations on the model. We therefore extend the class of models by considering linear combinations of fixed nonlinear functions of the input variables, of the form

$$
y(\mathbf{x}, \mathbf{w}) = w_0 + \sum_{j=1}^{M-1} w_j \phi_j(\mathbf{x})
$$
\n(3.2)

where  $\phi_i(\mathbf{x})$  are known as *basis functions*. By denoting the maximum value of the index j by  $M - 1$ , the total number of parameters in this model will be M.

The parameter  $w_0$  allows for any fixed offset in the data and is sometimes called a *bias* parameter (not to be confused with 'bias' in a statistical sense). It is often convenient to define an additional dummy 'basis function'  $\phi_0(\mathbf{x})=1$  so that

$$
y(\mathbf{x}, \mathbf{w}) = \sum_{j=0}^{M-1} w_j \phi_j(\mathbf{x}) = \mathbf{w}^{T} \phi(\mathbf{x})
$$
 (3.3)

where  $\mathbf{w} = (w_0, \dots, w_{M-1})^T$  and  $\boldsymbol{\phi} = (\phi_0, \dots, \phi_{M-1})^T$ . In many practical applications of pattern recognition, we will apply some form of fixed pre-processing,

or feature extraction, to the original data variables. If the original variables comprise the vector **x**, then the features can be expressed in terms of the basis functions  $\{\phi_i(\mathbf{x})\}.$ 

By using nonlinear basis functions, we allow the function  $y(\mathbf{x}, \mathbf{w})$  to be a nonlinear function of the input vector **x**. Functions of the form (3.2) are called linear models, however, because this function is linear in **w**. It is this linearity in the parameters that will greatly simplify the analysis of this class of models. However, it also leads to some significant limitations, as we discuss in Section 3.6.

The example of polynomial regression considered in Chapter 1 is a particular example of this model in which there is a single input variable  $x$ , and the basis functions take the form of powers of x so that  $\phi_i(x) = x^j$ . One limitation of polynomial basis functions is that they are global functions of the input variable, so that changes in one region of input space affect all other regions. This can be resolved by dividing the input space up into regions and fit a different polynomial in each region, leading to *spline functions* (Hastie *et al.*, 2001).

There are many other possible choices for the basis functions, for example

$$
\phi_j(x) = \exp\left\{-\frac{(x - \mu_j)^2}{2s^2}\right\}
$$
\n(3.4)

where the  $\mu_j$  govern the locations of the basis functions in input space, and the parameter s governs their spatial scale. These are usually referred to as 'Gaussian' basis functions, although it should be noted that they are not required to have a probabilistic interpretation, and in particular the normalization coefficient is unimportant because these basis functions will be multiplied by adaptive parameters  $w_i$ .

Another possibility is the sigmoidal basis function of the form

$$
\phi_j(x) = \sigma\left(\frac{x - \mu_j}{s}\right) \tag{3.5}
$$

where  $\sigma(a)$  is the logistic sigmoid function defined by

$$
\sigma(a) = \frac{1}{1 + \exp(-a)}.\tag{3.6}
$$

Equivalently, we can use the 'tanh' function because this is related to the logistic sigmoid by  $\tanh(a)=2\sigma(a) - 1$ , and so a general linear combination of logistic sigmoid functions is equivalent to a general linear combination of 'tanh' functions. These various choices of basis function are illustrated in Figure 3.1.

Yet another possible choice of basis function is the Fourier basis, which leads to an expansion in sinusoidal functions. Each basis function represents a specific frequency and has infinite spatial extent. By contrast, basis functions that are localized to finite regions of input space necessarily comprise a spectrum of different spatial frequencies. In many signal processing applications, it is of interest to consider basis functions that are localized in both space and frequency, leading to a class of functions known as *wavelets*. These are also defined to be mutually orthogonal, to simplify their application. Wavelets are most applicable when the input values live

Image /page/3/Figure/1 description: The image displays three plots side-by-side. The first plot shows several curves ranging from -1 to 1 on both the x and y axes. Some curves start at (-1, 1) and end at (1, 1), while others start at (-1, -1) and end at (1, 1). A straight green line connects (-1, -1) to (1, 1). The second plot features multiple bell-shaped curves, each peaking at y=1 and centered at different x-values between -1 and 1. The third plot shows a series of sigmoid-like curves, all starting near y=0 at x=-1 and rising to y=1 at x=1, with each curve shifted horizontally relative to the others.

**Figure 3.1** Examples of basis functions, showing polynomials on the left, Gaussians of the form (3.4) in the centre, and sigmoidal of the form (3.5) on the right.

on a regular lattice, such as the successive time points in a temporal sequence, or the pixels in an image. Useful texts on wavelets include Ogden (1997), Mallat (1999), and Vidakovic (1999).

Most of the discussion in this chapter, however, is independent of the particular choice of basis function set, and so for most of our discussion we shall not specify the particular form of the basis functions, except for the purposes of numerical illustration. Indeed, much of our discussion will be equally applicable to the situation in which the vector  $\phi(\mathbf{x})$  of basis functions is simply the identity  $\phi(\mathbf{x}) = \mathbf{x}$ . Furthermore, in order to keep the notation simple, we shall focus on the case of a single target variable  $t$ . However, in Section 3.1.5, we consider briefly the modifications needed to deal with multiple target variables.

# **3.1.1 Maximum likelihood and least squares**

In Chapter 1, we fitted polynomial functions to data sets by minimizing a sumof-squares error function. We also showed that this error function could be motivated as the maximum likelihood solution under an assumed Gaussian noise model. Let us return to this discussion and consider the least squares approach, and its relation to maximum likelihood, in more detail.

As before, we assume that the target variable  $t$  is given by a deterministic function  $y(\mathbf{x}, \mathbf{w})$  with additive Gaussian noise so that

$$
t = y(\mathbf{x}, \mathbf{w}) + \epsilon \tag{3.7}
$$

where  $\epsilon$  is a zero mean Gaussian random variable with precision (inverse variance)  $\beta$ . Thus we can write

$$
p(t|\mathbf{x}, \mathbf{w}, \beta) = \mathcal{N}(t|y(\mathbf{x}, \mathbf{w}), \beta^{-1}).
$$
\n(3.8)

Recall that, if we assume a squared loss function, then the optimal prediction, for a *Section 1.5.5* new value of **x**, will be given by the conditional mean of the target variable. In the case of a Gaussian conditional distribution of the form (3.8), the conditional mean

# **3.1. Linear Basis Function Models 141**

will be simply

$$
\mathbb{E}[t|\mathbf{x}] = \int tp(t|\mathbf{x}) dt = y(\mathbf{x}, \mathbf{w}).
$$
\n(3.9)

Note that the Gaussian noise assumption implies that the conditional distribution of <sup>t</sup> given **x** is unimodal, which may be inappropriate for some applications. An extension to mixtures of conditional Gaussian distributions, which permit multimodal conditional distributions, will be discussed in Section 14.5.1.

Now consider a data set of inputs  $X = \{x_1, \ldots, x_N\}$  with corresponding target values  $t_1,\ldots,t_N$ . We group the target variables  $\{t_n\}$  into a column vector that we denote by **t** where the typeface is chosen to distinguish it from a single observation of a multivariate target, which would be denoted **t**. Making the assumption that these data points are drawn independently from the distribution (3.8), we obtain the following expression for the likelihood function, which is a function of the adjustable parameters **w** and  $\beta$ , in the form

$$
p(\mathbf{t}|\mathbf{X}, \mathbf{w}, \beta) = \prod_{n=1}^{N} \mathcal{N}(t_n | \mathbf{w}^{T} \boldsymbol{\phi}(\mathbf{x}_n), \beta^{-1})
$$
(3.10)

where we have used  $(3.3)$ . Note that in supervised learning problems such as regression (and classification), we are not seeking to model the distribution of the input variables. Thus **x** will always appear in the set of conditioning variables, and so from now on we will drop the explicit **x** from expressions such as  $p(\mathbf{t}|\mathbf{x}, \mathbf{w}, \beta)$  in order to keep the notation uncluttered. Taking the logarithm of the likelihood function, and making use of the standard form (1.46) for the univariate Gaussian, we have

$$
\ln p(\mathbf{t}|\mathbf{w}, \beta) = \sum_{n=1}^{N} \ln \mathcal{N}(t_n|\mathbf{w}^{T}\boldsymbol{\phi}(\mathbf{x}_n), \beta^{-1})
$$
$$
= \frac{N}{2} \ln \beta - \frac{N}{2} \ln(2\pi) - \beta E_D(\mathbf{w}) \qquad (3.11)
$$

where the sum-of-squares error function is defined by

$$
E_D(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \{t_n - \mathbf{w}^{\mathrm{T}} \phi(\mathbf{x}_n)\}2.
$$
 (3.12)

Having written down the likelihood function, we can use maximum likelihood to determine **w** and <sup>β</sup>. Consider first the maximization with respect to **w**. As observed already in Section 1.2.5, we see that maximization of the likelihood function under a conditional Gaussian noise distribution for a linear model is equivalent to minimizing a sum-of-squares error function given by  $E_D(\mathbf{w})$ . The gradient of the log likelihood function (3.11) takes the form

$$
\nabla \ln p(\mathbf{t}|\mathbf{w}, \beta) = \sum_{n=1}^{N} \left\{ t_n - \mathbf{w}^{T} \boldsymbol{\phi}(\mathbf{x}_n) \right\} \boldsymbol{\phi}(\mathbf{x}_n)^{T}.
$$
 (3.13)

Setting this gradient to zero gives

$$
0 = \sum_{n=1}^{N} t_n \phi(\mathbf{x}_n)^{\mathrm{T}} - \mathbf{w}^{\mathrm{T}} \left( \sum_{n=1}^{N} \phi(\mathbf{x}_n) \phi(\mathbf{x}_n)^{\mathrm{T}} \right).
$$
 (3.14)

Solving for **w** we obtain

$$
\mathbf{w}_{\mathrm{ML}} = \left(\mathbf{\Phi}^{\mathrm{T}}\mathbf{\Phi}\right)^{-1}\mathbf{\Phi}^{\mathrm{T}}\mathbf{t}
$$
(3.15)

which are known as the *normal equations* for the least squares problem. Here  $\Phi$  is an  $N \times M$  matrix called the *design matrix*, whose elements are given by  $\Phi \to -\phi/(\mathbf{x})$ .  $N \times M$  matrix, called the *design matrix*, whose elements are given by  $\Phi_{nj} = \phi_j(\mathbf{x}_n)$ , so that

$$
\Phi = \left(\begin{array}{cccc} \phi_0(\mathbf{x}_1) & \phi_1(\mathbf{x}_1) & \cdots & \phi_{M-1}(\mathbf{x}_1) \\ \phi_0(\mathbf{x}_2) & \phi_1(\mathbf{x}_2) & \cdots & \phi_{M-1}(\mathbf{x}_2) \\ \vdots & \vdots & \ddots & \vdots \\ \phi_0(\mathbf{x}_N) & \phi_1(\mathbf{x}_N) & \cdots & \phi_{M-1}(\mathbf{x}_N) \end{array}\right).
$$
(3.16)

The quantity

$$
\Phi^{\dagger} \equiv (\Phi^{\mathrm{T}} \Phi)^{-1} \Phi^{\mathrm{T}}
$$
\n(3.17)

is known as the *Moore-Penrose pseudo-inverse* of the matrix **Φ** (Rao and Mitra, 1971; Golub and Van Loan, 1996). It can be regarded as a generalization of the notion of matrix inverse to nonsquare matrices. Indeed, if **Φ** is square and invertible, then using the property  $(AB)^{-1} = B^{-1}A^{-1}$  we see that  $\Phi^{\dagger} \equiv \Phi^{-1}$ .<br>At this point, we can gain some insight into the role of the bias pa

At this point, we can gain some insight into the role of the bias parameter  $w_0$ . If we make the bias parameter explicit, then the error function (3.12) becomes

$$
E_D(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \{t_n - w_0 - \sum_{j=1}^{M-1} w_j \phi_j(\mathbf{x}_n)\}2.
$$
 (3.18)

Setting the derivative with respect to  $w_0$  equal to zero, and solving for  $w_0$ , we obtain

$$
w_0 = \bar{t} - \sum_{j=1}^{M-1} w_j \overline{\phi_j}
$$
 (3.19)

where we have defined

$$
\bar{t} = \frac{1}{N} \sum_{n=1}^{N} t_n, \qquad \qquad \overline{\phi_j} = \frac{1}{N} \sum_{n=1}^{N} \phi_j(\mathbf{x}_n). \qquad (3.20)
$$

Thus the bias  $w_0$  compensates for the difference between the averages (over the training set) of the target values and the weighted sum of the averages of the basis function values.

We can also maximize the log likelihood function  $(3.11)$  with respect to the noise precision parameter  $\beta$ , giving

$$
\frac{1}{\beta_{\rm ML}} = \frac{1}{N} \sum_{n=1}^{N} \{t_n - \mathbf{w}_{\rm ML}^{\rm T} \boldsymbol{\phi}(\mathbf{x}_n)\}2
$$
 (3.21)

# **3.1. Linear Basis Function Models 143**

**Figure 3.2** Geometrical interpretation of the least-squares solution, in an  $N$ -dimensional space whose axes are the values of  $t_1, \ldots, t_N$ . The least-squares regression function is obtained by finding the orthogonal projection of the data vector **t** onto the subspace spanned by the basis functions  $\phi_i(\mathbf{x})$ in which each basis function is viewed as a vector  $\varphi_i$  of length N with elements  $\phi_j(\mathbf{x}_n)$ .

Image /page/6/Picture/2 description: A diagram shows a red square labeled S. From a point on the bottom edge of the square, three vectors originate. A blue vector labeled y points towards the interior of the square. A red vector labeled phi 1 points away from the square. A black vector labeled phi 2 points towards the top edge of the square. A green vector labeled t originates from the same point as y and phi 2, and points away from the square.

and so we see that the inverse of the noise precision is given by the residual variance of the target values around the regression function.

### **3.1.2 Geometry of least squares**

At this point, it is instructive to consider the geometrical interpretation of the least-squares solution. To do this we consider an  $N$ -dimensional space whose axes are given by the  $t_n$ , so that  $\mathbf{t} = (t_1, \dots, t_N)^T$  is a vector in this space. Each basis function  $\phi_i(\mathbf{x}_n)$ , evaluated at the N data points, can also be represented as a vector in the same space, denoted by  $\varphi_i$ , as illustrated in Figure 3.2. Note that  $\varphi_i$  corresponds to the j<sup>th</sup> column of  $\Phi$ , whereas  $\phi(\mathbf{x}_n)$  corresponds to the  $n^{\text{th}}$  row of  $\Phi$ . If the number  $M$  of basis functions is smaller than the number  $N$  of data points, then the M vectors  $\phi_i(\mathbf{x}_n)$  will span a linear subspace S of dimensionality M. We define **y** to be an N-dimensional vector whose  $n^{\text{th}}$  element is given by  $y(\mathbf{x}_n, \mathbf{w})$ , where  $n = 1, \ldots, N$ . Because **y** is an arbitrary linear combination of the vectors  $\varphi_i$ , it can live anywhere in the  $M$ -dimensional subspace. The sum-of-squares error  $(3.12)$  is then equal (up to a factor of  $1/2$ ) to the squared Euclidean distance between **y** and **<sup>t</sup>**. Thus the least-squares solution for **w** corresponds to that choice of **<sup>y</sup>** that lies in subspace  $S$  and that is closest to **t**. Intuitively, from Figure 3.2, we anticipate that this solution corresponds to the orthogonal projection of **t** onto the subspace  $S$ . This is indeed the case, as can easily be verified by noting that the solution for **y** is given *Exercise 3.2* by  $\Phi w_{ML}$ , and then confirming that this takes the form of an orthogonal projection.

*Exercise 3.2*

In practice, a direct solution of the normal equations can lead to numerical difficulties when  $\Phi^T \Phi$  is close to singular. In particular, when two or more of the basis vectors  $\varphi_i$  are co-linear, or nearly so, the resulting parameter values can have large magnitudes. Such near degeneracies will not be uncommon when dealing with real data sets. The resulting numerical difficulties can be addressed using the technique of *singular value decomposition*, or *SVD* (Press *et al.*, 1992; Bishop and Nabney, 2008). Note that the addition of a regularization term ensures that the matrix is nonsingular, even in the presence of degeneracies.

## **3.1.3 Sequential learning**

Batch techniques, such as the maximum likelihood solution (3.15), which involve processing the entire training set in one go, can be computationally costly for large data sets. As we have discussed in Chapter 1, if the data set is sufficiently large, it may be worthwhile to use *sequential* algorithms, also known as *on-line* algorithms,

in which the data points are considered one at a time, and the model parameters updated after each such presentation. Sequential learning is also appropriate for realtime applications in which the data observations are arriving in a continuous stream, and predictions must be made before all of the data points are seen.

We can obtain a sequential learning algorithm by applying the technique of *stochastic gradient descent*, also known as *sequential gradient descent*, as follows. If the error function comprises a sum over data points  $\overline{E} = \sum_n E_n$ , then after presentation of pattern  $n$ , the stochastic gradient descent algorithm updates the parameter vector **w** using

$$
\mathbf{w}^{(\tau+1)} = \mathbf{w}^{(\tau)} - \eta \nabla E_n \tag{3.22}
$$

where  $\tau$  denotes the iteration number, and  $\eta$  is a learning rate parameter. We shall discuss the choice of value for  $\eta$  shortly. The value of **w** is initialized to some starting vector  $\mathbf{w}^{(0)}$ . For the case of the sum-of-squares error function (3.12), this gives

$$
\mathbf{w}^{(\tau+1)} = \mathbf{w}^{(\tau)} + \eta (t_n - \mathbf{w}^{(\tau)} \phi_n) \phi_n \tag{3.23}
$$

where  $\phi_n = \phi(\mathbf{x}_n)$ . This is known as *least-mean-squares* or the *LMS algorithm*. The value of  $\eta$  needs to be chosen with care to ensure that the algorithm converges (Bishop and Nabney, 2008).

### **3.1.4 Regularized least squares**

In Section 1.1, we introduced the idea of adding a regularization term to an error function in order to control over-fitting, so that the total error function to be minimized takes the form

$$
E_D(\mathbf{w}) + \lambda E_W(\mathbf{w})
$$
\n(3.24)

where  $\lambda$  is the regularization coefficient that controls the relative importance of the data-dependent error  $E_D(\mathbf{w})$  and the regularization term  $E_W(\mathbf{w})$ . One of the simplest forms of regularizer is given by the sum-of-squares of the weight vector elements

$$
E_W(\mathbf{w}) = \frac{1}{2} \mathbf{w}^{\mathrm{T}} \mathbf{w}.
$$
 (3.25)

If we also consider the sum-of-squares error function given by

$$
E(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \{t_n - \mathbf{w}^{T} \phi(\mathbf{x}_n)\}2
$$
 (3.26)

then the total error function becomes

$$
\frac{1}{2}\sum_{n=1}^{N}\left\{t_n - \mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}(\mathbf{x}_n)\right\}^2 + \frac{\lambda}{2}\mathbf{w}^{\mathrm{T}}\mathbf{w}.\tag{3.27}
$$

This particular choice of regularizer is known in the machine learning literature as *weight decay* because in sequential learning algorithms, it encourages weight values to decay towards zero, unless supported by the data. In statistics, it provides an example of a *parameter shrinkage* method because it shrinks parameter values towards

Image /page/8/Figure/1 description: The image displays four graphs plotted on a Cartesian coordinate system. Each graph features a green curve and is labeled with a value of q. The first graph, labeled "q = 0.5", shows a four-pointed star shape with sharp points along the x and y axes. The second graph, labeled "q = 1", depicts a diamond shape, which is a square rotated by 45 degrees. The third graph, labeled "q = 2", presents a circle. The fourth graph, labeled "q = 4", shows a shape resembling a square with rounded corners.

**Figure 3.3** Contours of the regularization term in (3.29) for various values of the parameter q.

zero. It has the advantage that the error function remains a quadratic function of **w**, and so its exact minimizer can be found in closed form. Specifically, setting the gradient of (3.27) with respect to **w** to zero, and solving for **w** as before, we obtain

$$
\mathbf{w} = \left(\lambda \mathbf{I} + \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi}\right)^{-1} \mathbf{\Phi}^{\mathrm{T}} \mathbf{t}.
$$
 (3.28)

This represents a simple extension of the least-squares solution (3.15).

A more general regularizer is sometimes used, for which the regularized error takes the form

$$
\frac{1}{2}\sum_{n=1}^{N}\left\{t_{n}-\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}(\mathbf{x}_{n})\right\}^{2}+\frac{\lambda}{2}\sum_{j=1}^{M}|w_{j}|^{q}
$$
(3.29)

where  $q = 2$  corresponds to the quadratic regularizer (3.27). Figure 3.3 shows contours of the regularization function for different values of q.

The case of  $q = 1$  is know as the *lasso* in the statistics literature (Tibshirani, 1996). It has the property that if  $\lambda$  is sufficiently large, some of the coefficients  $w_i$  are driven to zero, leading to a *sparse* model in which the corresponding basis functions play no role. To see this, we first note that minimizing (3.29) is equivalent *Exercise 3.5* to minimizing the unregularized sum-of-squares error (3.12) subject to the constraint

$$
\sum_{j=1}^{M} |w_j|^q \leqslant \eta \tag{3.30}
$$

# Appendix E

for an appropriate value of the parameter  $\eta$ , where the two approaches can be related *Appendix E* using Lagrange multipliers. The origin of the sparsity can be seen from Figure 3.4, which shows that the minimum of the error function, subject to the constraint  $(3.30)$ . As  $\lambda$  is increased, so an increasing number of parameters are driven to zero.

> Regularization allows complex models to be trained on data sets of limited size without severe over-fitting, essentially by limiting the effective model complexity. However, the problem of determining the optimal model complexity is then shifted from one of finding the appropriate number of basis functions to one of determining a suitable value of the regularization coefficient  $\lambda$ . We shall return to the issue of model complexity later in this chapter.

**Figure 3.4** Plot of the contours of the unregularized error function (blue) along with the constraint region (3.30) for the quadratic regularizer  $q = 2$  on the left and the lasso regularizer  $q = 1$  on the right, in which the optimum value for the parameter vector **w** is denoted by **w**. The lasso gives a sparse solution in which  $w_1^* = 0$ .

Image /page/9/Figure/2 description: The image displays two plots side-by-side, both illustrating optimization problems in a 2D space with axes labeled w1 and w2. Each plot features a set of concentric blue circles, representing level sets of a cost function, with a blue dot at the center indicating the unconstrained minimum. A point labeled w\* is marked on the boundary of a shaded region, representing the constrained minimum. The left plot shows a circular shaded region, indicating an L2 norm constraint. The right plot shows a diamond-shaped shaded region, indicating an L1 norm constraint. In both plots, the point w\* is located at the intersection of the innermost blue circle and the boundary of the shaded region, demonstrating how different constraint shapes lead to different optimal solutions.

For the remainder of this chapter we shall focus on the quadratic regularizer (3.27) both for its practical importance and its analytical tractability.

## **3.1.5 Multiple outputs**

So far, we have considered the case of a single target variable  $t$ . In some applications, we may wish to predict  $K > 1$  target variables, which we denote collectively by the target vector **t**. This could be done by introducing a different set of basis functions for each component of **t**, leading to multiple, independent regression problems. However, a more interesting, and more common, approach is to use the same set of basis functions to model all of the components of the target vector so that

$$
\mathbf{y}(\mathbf{x}, \mathbf{w}) = \mathbf{W}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}) \tag{3.31}
$$

where **y** is a K-dimensional column vector, **W** is an  $M \times K$  matrix of parameters, and  $\phi(\mathbf{x})$  is an M-dimensional column vector with elements  $\phi_i(\mathbf{x})$ , with  $\phi_0(\mathbf{x})=1$ as before. Suppose we take the conditional distribution of the target vector to be an isotropic Gaussian of the form

$$
p(\mathbf{t}|\mathbf{x}, \mathbf{W}, \beta) = \mathcal{N}(\mathbf{t}|\mathbf{W}^{\mathrm{T}}\boldsymbol{\phi}(\mathbf{x}), \beta^{-1}\mathbf{I}).
$$
 (3.32)

If we have a set of observations  $\mathbf{t}_1, \ldots, \mathbf{t}_N$ , we can combine these into a matrix **T** of size  $N \times K$  such that the  $n^{\text{th}}$  row is given by  $\mathbf{t}_n^{\text{T}}$ . Similarly, we can combine the number of  $\mathbf{v}_n$  into a matrix **X**. The log likelihood function is then given input vectors  $\mathbf{x}_1, \ldots, \mathbf{x}_N$  into a matrix **X**. The log likelihood function is then given by

$$
\ln p(\mathbf{T}|\mathbf{X}, \mathbf{W}, \beta) = \sum_{n=1}^{N} \ln \mathcal{N}(\mathbf{t}_n | \mathbf{W}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n), \beta^{-1} \mathbf{I})
$$
$$
= \frac{NK}{2} \ln \left( \frac{\beta}{2\pi} \right) - \frac{\beta}{2} \sum_{n=1}^{N} \left\| \mathbf{t}_n - \mathbf{W}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n) \right\|^2. (3.33)
$$

# **3.2. The Bias-Variance Decomposition 147**

As before, we can maximize this function with respect to **W**, giving

$$
\mathbf{W}_{\mathrm{ML}} = \left(\mathbf{\Phi}^{\mathrm{T}}\mathbf{\Phi}\right)^{-1}\mathbf{\Phi}^{\mathrm{T}}\mathbf{T}.\tag{3.34}
$$

If we examine this result for each target variable  $t_k$ , we have

$$
\mathbf{w}_k = \left(\mathbf{\Phi}^{\mathrm{T}}\mathbf{\Phi}\right)^{-1}\mathbf{\Phi}^{\mathrm{T}}\mathbf{t}_k = \mathbf{\Phi}^{\dagger}\mathbf{t}_k \tag{3.35}
$$

where  $\mathbf{t}_k$  is an N-dimensional column vector with components  $t_{nk}$  for  $n = 1, \ldots N$ . Thus the solution to the regression problem decouples between the different target variables, and we need only compute a single pseudo-inverse matrix  $\Phi^{\dagger}$ , which is shared by all of the vectors  $\mathbf{w}_t$ . shared by all of the vectors  $w_k$ .

The extension to general Gaussian noise distributions having arbitrary covari-*Exercise 3.6* ance matrices is straightforward. Again, this leads to a decoupling into K independent regression problems. This result is unsurprising because the parameters **W** define only the mean of the Gaussian noise distribution, and we know from Section 2.3.4 that the maximum likelihood solution for the mean of a multivariate Gaussian is independent of the covariance. From now on, we shall therefore consider a single target variable  $t$  for simplicity.

# **3.2. The Bias-Variance Decomposition**

So far in our discussion of linear models for regression, we have assumed that the form and number of basis functions are both fixed. As we have seen in Chapter 1, the use of maximum likelihood, or equivalently least squares, can lead to severe over-fitting if complex models are trained using data sets of limited size. However, limiting the number of basis functions in order to avoid over-fitting has the side effect of limiting the flexibility of the model to capture interesting and important trends in the data. Although the introduction of regularization terms can control over-fitting for models with many parameters, this raises the question of how to determine a suitable value for the regularization coefficient  $\lambda$ . Seeking the solution that minimizes the regularized error function with respect to both the weight vector **w** and the regularization coefficient  $\lambda$  is clearly not the right approach since this leads to the unregularized solution with  $\lambda = 0$ .

As we have seen in earlier chapters, the phenomenon of over-fitting is really an unfortunate property of maximum likelihood and does not arise when we marginalize over parameters in a Bayesian setting. In this chapter, we shall consider the Bayesian view of model complexity in some depth. Before doing so, however, it is instructive to consider a frequentist viewpoint of the model complexity issue, known as the *biasvariance* trade-off. Although we shall introduce this concept in the context of linear basis function models, where it is easy to illustrate the ideas using simple examples, the discussion has more general applicability.

In Section 1.5.5, when we discussed decision theory for regression problems, we considered various loss functions each of which leads to a corresponding optimal prediction once we are given the conditional distribution  $p(t|\mathbf{x})$ . A popular choice is

*Exercise 3.6*

the squared loss function, for which the optimal prediction is given by the conditional expectation, which we denote by  $h(\mathbf{x})$  and which is given by

$$
h(\mathbf{x}) = \mathbb{E}[t|\mathbf{x}] = \int tp(t|\mathbf{x}) dt.
$$
 (3.36)

At this point, it is worth distinguishing between the squared loss function arising from decision theory and the sum-of-squares error function that arose in the maximum likelihood estimation of model parameters. We might use more sophisticated techniques than least squares, for example regularization or a fully Bayesian approach, to determine the conditional distribution  $p(t|\mathbf{x})$ . These can all be combined with the squared loss function for the purpose of making predictions.

We showed in Section 1.5.5 that the expected squared loss can be written in the form

$$
\mathbb{E}[L] = \int \left\{ y(\mathbf{x}) - h(\mathbf{x}) \right\}^2 p(\mathbf{x}) \, \mathrm{d}\mathbf{x} + \int \left\{ h(\mathbf{x}) - t \right\}^2 p(\mathbf{x}, t) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t. \tag{3.37}
$$

Recall that the second term, which is independent of  $y(x)$ , arises from the intrinsic noise on the data and represents the minimum achievable value of the expected loss. The first term depends on our choice for the function  $y(x)$ , and we will seek a solution for  $y(x)$  which makes this term a minimum. Because it is nonnegative, the smallest that we can hope to make this term is zero. If we had an unlimited supply of data (and unlimited computational resources), we could in principle find the regression function  $h(x)$  to any desired degree of accuracy, and this would represent the optimal choice for  $y(x)$ . However, in practice we have a data set  $D$  containing only a finite number  $N$  of data points, and consequently we do not know the regression function  $h(\mathbf{x})$  exactly.

If we model the  $h(\mathbf{x})$  using a parametric function  $y(\mathbf{x}, \mathbf{w})$  governed by a parameter vector **w**, then from a Bayesian perspective the uncertainty in our model is expressed through a posterior distribution over **w**. A frequentist treatment, however, involves making a point estimate of  $w$  based on the data set  $D$ , and tries instead to interpret the uncertainty of this estimate through the following thought experiment. Suppose we had a large number of data sets each of size  $N$  and each drawn independently from the distribution  $p(t, \mathbf{x})$ . For any given data set  $\mathcal{D}$ , we can run our learning algorithm and obtain a prediction function  $y(\mathbf{x}; \mathcal{D})$ . Different data sets from the ensemble will give different functions and consequently different values of the squared loss. The performance of a particular learning algorithm is then assessed by taking the average over this ensemble of data sets.

Consider the integrand of the first term in (3.37), which for a particular data set D takes the form

$$
\{y(\mathbf{x}; \mathcal{D}) - h(\mathbf{x})\}^2.
$$
\n(3.38)

Because this quantity will be dependent on the particular data set  $D$ , we take its average over the ensemble of data sets. If we add and subtract the quantity  $\mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})]$ 

# **3.2. The Bias-Variance Decomposition 149**

inside the braces, and then expand, we obtain

$$
\left\{y(\mathbf{x}; \mathcal{D}) - \mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})]\right\} + \left\{\mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})] - h(\mathbf{x})\right\}^{2}
= \left\{y(\mathbf{x}; \mathcal{D}) - \mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})]\right\}^{2} + \left\{\mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})] - h(\mathbf{x})\right\}^{2}
+ 2\left\{y(\mathbf{x}; \mathcal{D}) - \mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})]\right\} \left\{\mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})] - h(\mathbf{x})\right\}.
$$
(3.39)

We now take the expectation of this expression with respect to  $D$  and note that the final term will vanish, giving

$$
\mathbb{E}_{\mathcal{D}} \left[ \left\{ y(\mathbf{x}; \mathcal{D}) - h(\mathbf{x}) \right\}^2 \right] = \underbrace{\left\{ \mathbb{E}_{\mathcal{D}} [y(\mathbf{x}; \mathcal{D})] - h(\mathbf{x}) \right\}^2}_{(\text{bias})^2} + \underbrace{\mathbb{E}_{\mathcal{D}} \left[ \left\{ y(\mathbf{x}; \mathcal{D}) - \mathbb{E}_{\mathcal{D}} [y(\mathbf{x}; \mathcal{D})] \right\}^2 \right]}_{\text{variance}} . \tag{3.40}
$$

We see that the expected squared difference between  $y(\mathbf{x}; \mathcal{D})$  and the regression function  $h(x)$  can be expressed as the sum of two terms. The first term, called the squared *bias*, represents the extent to which the average prediction over all data sets differs from the desired regression function. The second term, called the *variance*, measures the extent to which the solutions for individual data sets vary around their average, and hence this measures the extent to which the function  $y(\mathbf{x}; \mathcal{D})$  is sensitive to the particular choice of data set. We shall provide some intuition to support these definitions shortly when we consider a simple example.

So far, we have considered a single input value **x**. If we substitute this expansion back into (3.37), we obtain the following decomposition of the expected squared loss

$$
expected loss = (bias)^2 + variance + noise
$$
 (3.41)

where

$$
(\text{bias})^2 = \int {\mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})] - h(\mathbf{x})}^2 p(\mathbf{x}) d\mathbf{x}
$$
 (3.42)

variance = 
$$
\int \mathbb{E}_{\mathcal{D}} \left[ \{ y(\mathbf{x}; \mathcal{D}) - \mathbb{E}_{\mathcal{D}}[y(\mathbf{x}; \mathcal{D})] \}^2 \right] p(\mathbf{x}) d\mathbf{x}
$$
 (3.43)

noise = 
$$
\int {\{h(\mathbf{x}) - t\}^2 p(\mathbf{x}, t) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t}
$$
 (3.44)

and the bias and variance terms now refer to integrated quantities.

Our goal is to minimize the expected loss, which we have decomposed into the sum of a (squared) bias, a variance, and a constant noise term. As we shall see, there is a trade-off between bias and variance, with very flexible models having low bias and high variance, and relatively rigid models having high bias and low variance. The model with the optimal predictive capability is the one that leads to the best balance between bias and variance. This is illustrated by considering the sinusoidal *Appendix A* data set from Chapter 1. Here we generate 100 data sets, each containing  $N = 25$ data points, independently from the sinusoidal curve  $h(x) = \sin(2\pi x)$ . The data sets are indexed by  $l = 1, \ldots, L$ , where  $L = 100$ , and for each data set  $\mathcal{D}^{(l)}$  we

*Appendix A*

Image /page/13/Figure/1 description: This figure displays three rows of plots, each illustrating the dependence of bias and variance on model complexity, governed by a regularization parameter lambda. The left column shows multiple red curves representing different model fits for a given lambda value, with the corresponding ln(lambda) indicated in each plot: 2.6, -0.31, and -2.4 from top to bottom. The right column shows a single green curve and a single red curve for each row, likely representing the true function and the average model fit, respectively. All plots have 'x' on the horizontal axis ranging from 0 to 1, and 't' on the vertical axis ranging from -1 to 1.

Figure 3.5 Illustration of the dependence of bias and variance on model complexity, governed by a regularization parameter  $\lambda$ , using the sinusoidal data set from Chapter 1. There are  $L = 100$  data sets, each having  $N = 25$ data points, and there are 24 Gaussian basis functions in the model so that the total number of parameters is  $M = 25$  including the bias parameter. The left column shows the result of fitting the model to the data sets for various values of  $\ln \lambda$  (for clarity, only 20 of the 100 fits are shown). The right column shows the corresponding average of the 100 fits (red) along with the sinusoidal function from which the data sets were generated (green).

# **3.2. The Bias-Variance Decomposition 151**

**Figure 3.6** Plot of squared bias and variance, together with their sum, corresponding to the results shown in Figure 3.5. Also shown is the average test set error for a test data set size of 1000 points. The minimum value 0.09 of  $(bias)^2$  + variance occurs around  $\ln \lambda = -0.31$ , which is close to the value that gives the minimum error on the test data.

Image /page/14/Figure/2 description: The image is a line graph showing the relationship between ln lambda and several error metrics. The x-axis is labeled "ln \u03bb" and ranges from -3 to 2. The y-axis is labeled with values from 0 to 0.15. Four lines are plotted: a blue line representing "(bias)\\\u00b2", a red line representing "variance", a magenta line representing "(bias)\\\u00b2 + variance", and a black line representing "test error". The "(bias)\\\u00b2" line starts near 0 and increases to approximately 0.05 at ln \u03bb = 1.5. The "variance" line starts at approximately 0.025 and decreases to approximately 0.01 at ln \u03bb = 1.5. The "(bias)\\\u00b2 + variance" line starts at approximately 0.025 and decreases to a minimum around ln \u03bb = -0.5, then increases to approximately 0.055 at ln \u03bb = 1.5. The "test error" line starts at approximately 0.07 at ln \u03bb = -2.5, decreases to a minimum around ln \u03bb = -1.5, and then increases to approximately 0.1 at ln \u03bb = 1.5.

fit a model with 24 Gaussian basis functions by minimizing the regularized error function (3.27) to give a prediction function  $y^{(l)}(x)$  as shown in Figure 3.5. The top row corresponds to a large value of the regularization coefficient  $\lambda$  that gives low variance (because the red curves in the left plot look similar) but high bias (because the two curves in the right plot are very different). Conversely on the bottom row, for which  $\lambda$  is small, there is large variance (shown by the high variability between the red curves in the left plot) but low bias (shown by the good fit between the average model fit and the original sinusoidal function). Note that the result of averaging many solutions for the complex model with  $M = 25$  is a very good fit to the regression function, which suggests that averaging may be a beneficial procedure. Indeed, a weighted averaging of multiple solutions lies at the heart of a Bayesian approach, although the averaging is with respect to the posterior distribution of parameters, not with respect to multiple data sets.

We can also examine the bias-variance trade-off quantitatively for this example. The average prediction is estimated from

$$
\overline{y}(x) = \frac{1}{L} \sum_{l=1}^{L} y^{(l)}(x)
$$
\n(3.45)

and the integrated squared bias and integrated variance are then given by

$$
(\text{bias})^2 = \frac{1}{N} \sum_{n=1}^{N} {\{\overline{y}(x_n) - h(x_n)\}}^2
$$
 (3.46)

variance = 
$$
\frac{1}{N} \sum_{n=1}^{N} \frac{1}{L} \sum_{l=1}^{L} \left\{ y^{(l)}(x_n) - \overline{y}(x_n) \right\}^2
$$
 (3.47)

where the integral over x weighted by the distribution  $p(x)$  is approximated by a finite sum over data points drawn from that distribution. These quantities, along with their sum, are plotted as a function of  $\ln \lambda$  in Figure 3.6. We see that small values of  $\lambda$  allow the model to become finely tuned to the noise on each individual

data set leading to large variance. Conversely, a large value of  $\lambda$  pulls the weight parameters towards zero leading to large bias.

Although the bias-variance decomposition may provide some interesting insights into the model complexity issue from a frequentist perspective, it is of limited practical value, because the bias-variance decomposition is based on averages with respect to ensembles of data sets, whereas in practice we have only the single observed data set. If we had a large number of independent training sets of a given size, we would be better off combining them into a single large training set, which of course would reduce the level of over-fitting for a given model complexity.

Given these limitations, we turn in the next section to a Bayesian treatment of linear basis function models, which not only provides powerful insights into the issues of over-fitting but which also leads to practical techniques for addressing the question model complexity.

# **3.3. Bayesian Linear Regression**

In our discussion of maximum likelihood for setting the parameters of a linear regression model, we have seen that the effective model complexity, governed by the number of basis functions, needs to be controlled according to the size of the data set. Adding a regularization term to the log likelihood function means the effective model complexity can then be controlled by the value of the regularization coefficient, although the choice of the number and form of the basis functions is of course still important in determining the overall behaviour of the model.

This leaves the issue of deciding the appropriate model complexity for the particular problem, which cannot be decided simply by maximizing the likelihood function, because this always leads to excessively complex models and over-fitting. Independent hold-out data can be used to determine model complexity, as discussed in Section 1.3, but this can be both computationally expensive and wasteful of valuable data. We therefore turn to a Bayesian treatment of linear regression, which will avoid the over-fitting problem of maximum likelihood, and which will also lead to automatic methods of determining model complexity using the training data alone. Again, for simplicity we will focus on the case of a single target variable  $t$ . Extension to multiple target variables is straightforward and follows the discussion of Section 3.1.5.

#### **3.3.1 Parameter distribution**

We begin our discussion of the Bayesian treatment of linear regression by introducing a prior probability distribution over the model parameters **w**. For the moment, we shall treat the noise precision parameter  $\beta$  as a known constant. First note that the likelihood function  $p(\mathbf{t}|\mathbf{w})$  defined by (3.10) is the exponential of a quadratic function of **w**. The corresponding conjugate prior is therefore given by a Gaussian distribution of the form

$$
p(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{m}_0, \mathbf{S}_0)
$$
\n(3.48)

having mean  $\mathbf{m}_0$  and covariance  $\mathbf{S}_0$ .

# **3.3. Bayesian Linear Regression 153**

Next we compute the posterior distribution, which is proportional to the product of the likelihood function and the prior. Due to the choice of a conjugate Gaussian prior distribution, the posterior will also be Gaussian. We can evaluate this distribution by the usual procedure of completing the square in the exponential, and then finding the normalization coefficient using the standard result for a normalized *Exercise 3.7* Gaussian. However, we have already done the necessary work in deriving the general result (2.116), which allows us to write down the posterior distribution directly in the form

$$
p(\mathbf{w}|\mathbf{t}) = \mathcal{N}(\mathbf{w}|\mathbf{m}_N, \mathbf{S}_N)
$$
\n(3.49)

where

$$
\mathbf{m}_N = \mathbf{S}_N \left( \mathbf{S}_0^{-1} \mathbf{m}_0 + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{t} \right) \tag{3.50}
$$

$$
\mathbf{S}_N^{-1} = \mathbf{S}_0^{-1} + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi}.
$$
 (3.51)

Note that because the posterior distribution is Gaussian, its mode coincides with its mean. Thus the maximum posterior weight vector is simply given by  $\mathbf{w}_{MAP} = \mathbf{m}_N$ .<br>If we consider an infinitely broad prior  $\mathbf{S}_0 = \alpha^{-1}\mathbf{I}$  with  $\alpha \to 0$  the mean  $\mathbf{m}_N$ If we consider an infinitely broad prior  $\mathbf{S}_0 = \alpha^{-1}\mathbf{I}$  with  $\alpha \to 0$ , the mean  $\mathbf{m}_N$  of the posterior distribution reduces to the maximum likelihood value  $\mathbf{w}_{\text{ML}}$  given of the posterior distribution reduces to the maximum likelihood value  $w_{ML}$  given by (3.15). Similarly, if  $N = 0$ , then the posterior distribution reverts to the prior. Furthermore, if data points arrive sequentially, then the posterior distribution at any stage acts as the prior distribution for the subsequent data point, such that the new *Exercise* 3.8 posterior distribution is again given by (3.49).

> For the remainder of this chapter, we shall consider a particular form of Gaussian prior in order to simplify the treatment. Specifically, we consider a zero-mean isotropic Gaussian governed by a single precision parameter  $\alpha$  so that

$$
p(\mathbf{w}|\alpha) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \alpha^{-1}\mathbf{I})
$$
 (3.52)

and the corresponding posterior distribution over **w** is then given by (3.49) with

$$
\mathbf{m}_N = \beta \mathbf{S}_N \mathbf{\Phi}^{\mathrm{T}} \mathbf{t} \tag{3.53}
$$
\n
$$
\mathbf{S}^{-1} = \mathbf{J} + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi} \tag{3.54}
$$

$$
\mathbf{S}_N^{-1} = \alpha \mathbf{I} + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi}.
$$
 (3.54)

The log of the posterior distribution is given by the sum of the log likelihood and the log of the prior and, as a function of **w**, takes the form

$$
\ln p(\mathbf{w}|\mathbf{t}) = -\frac{\beta}{2} \sum_{n=1}^{N} \{t_n - \mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n)\}^2 - \frac{\alpha}{2} \mathbf{w}^{\mathrm{T}} \mathbf{w} + \text{const.}
$$
 (3.55)

Maximization of this posterior distribution with respect to **w** is therefore equivalent to the minimization of the sum-of-squares error function with the addition of a quadratic regularization term, corresponding to (3.27) with  $\lambda = \alpha/\beta$ .

We can illustrate Bayesian learning in a linear basis function model, as well as the sequential update of a posterior distribution, using a simple example involving straight-line fitting. Consider a single input variable  $x$ , a single target variable  $t$  and

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

*Exercise 3.8*

a linear model of the form  $y(x, w) = w_0 + w_1x$ . Because this has just two adaptive parameters, we can plot the prior and posterior distributions directly in parameter space. We generate synthetic data from the function  $f(x, \mathbf{a}) = a_0 + a_1x$  with parameter values  $a_0 = -0.3$  and  $a_1 = 0.5$  by first choosing values of  $x_n$  from the uniform distribution  $U(x|-1, 1)$ , then evaluating  $f(x_n, a)$ , and finally adding Gaussian noise with standard deviation of 0.2 to obtain the target values  $t_n$ . Our goal is to recover the values of  $a_0$  and  $a_1$  from such data, and we will explore the dependence on the size of the data set. We assume here that the noise variance is known and hence we set the precision parameter to its true value  $\beta = (1/0.2)^2 = 25$ . Similarly, we fix the parameter  $\alpha$  to 2.0. We shall shortly discuss strategies for determining  $\alpha$  and  $\beta$  from the training data. Figure 3.7 shows the results of Bayesian learning in this model as the size of the data set is increased and demonstrates the sequential nature of Bayesian learning in which the current posterior distribution forms the prior when a new data point is observed. It is worth taking time to study this figure in detail as it illustrates several important aspects of Bayesian inference. The first row of this figure corresponds to the situation before any data points are observed and shows a plot of the prior distribution in **w** space together with six samples of the function  $y(x, \mathbf{w})$  in which the values of **w** are drawn from the prior. In the second row, we see the situation after observing a single data point. The location  $(x, t)$  of the data point is shown by a blue circle in the right-hand column. In the left-hand column is a plot of the likelihood function  $p(t|x, \mathbf{w})$  for this data point as a function of **w**. Note that the likelihood function provides a soft constraint that the line must pass close to the data point, where close is determined by the noise precision  $\beta$ . For comparison, the true parameter values  $a_0 = -0.3$  and  $a_1 = 0.5$  used to generate the data set are shown by a white cross in the plots in the left column of Figure 3.7. When we multiply this likelihood function by the prior from the top row, and normalize, we obtain the posterior distribution shown in the middle plot on the second row. Samples of the regression function  $y(x, \mathbf{w})$  obtained by drawing samples of **w** from this posterior distribution are shown in the right-hand plot. Note that these sample lines all pass close to the data point. The third row of this figure shows the effect of observing a second data point, again shown by a blue circle in the plot in the right-hand column. The corresponding likelihood function for this second data point alone is shown in the left plot. When we multiply this likelihood function by the posterior distribution from the second row, we obtain the posterior distribution shown in the middle plot of the third row. Note that this is exactly the same posterior distribution as would be obtained by combining the original prior with the likelihood function for the two data points. This posterior has now been influenced by two data points, and because two points are sufficient to define a line this already gives a relatively compact posterior distribution. Samples from this posterior distribution give rise to the functions shown in red in the third column, and we see that these functions pass close to both of the data points. The fourth row shows the effect of observing a total of 20 data points. The left-hand plot shows the likelihood function for the  $20<sup>th</sup>$  data point alone, and the middle plot shows the resulting posterior distribution that has now absorbed information from all 20 observations. Note how the posterior is much sharper than in the third row. In the limit of an infinite number of data points, the

Image /page/18/Figure/1 description: This figure illustrates sequential Bayesian learning for a simple linear model of the form y(x, w) = w0 + w1x. The figure is organized into four rows and three columns. The columns are labeled 'likelihood', 'prior/posterior', and 'data space'. Each row represents a different stage of the learning process. The 'likelihood' column shows heatmaps representing the likelihood function, with axes labeled w0 and w1. The 'prior/posterior' column shows heatmaps representing the prior and posterior distributions of the model parameters w0 and w1. The 'data space' column shows scatter plots of data points (x, y) along with several red lines representing the model with different parameter values. In the first row, the prior is a broad Gaussian centered at (0,0). In the second row, a single data point is introduced, and the posterior becomes more concentrated along a diagonal line. In the third row, another data point is added, further refining the posterior. In the fourth row, multiple data points are shown, and the posterior is tightly clustered around the optimal parameters, with the red lines in the 'data space' plot converging to fit the data points.

**Figure 3.7** Illustration of sequential Bayesian learning for a simple linear model of the form  $y(x, \mathbf{w}) =$  $w_0 + w_1x$ . A detailed description of this figure is given in the text.

posterior distribution would become a delta function centred on the true parameter values, shown by the white cross.

Other forms of prior over the parameters can be considered. For instance, we can generalize the Gaussian prior to give

$$
p(\mathbf{w}|\alpha) = \left[\frac{q}{2}\left(\frac{\alpha}{2}\right)^{1/q} \frac{1}{\Gamma(1/q)}\right]^M \exp\left(-\frac{\alpha}{2}\sum_{j=1}^M |w_j|^q\right) \tag{3.56}
$$

in which  $q = 2$  corresponds to the Gaussian distribution, and only in this case is the prior conjugate to the likelihood function (3.10). Finding the maximum of the posterior distribution over **w** corresponds to minimization of the regularized error function (3.29). In the case of the Gaussian prior, the mode of the posterior distribution was equal to the mean, although this will no longer hold if  $q \neq 2$ .

## **3.3.2 Predictive distribution**

In practice, we are not usually interested in the value of **w** itself but rather in making predictions of <sup>t</sup> for new values of **x**. This requires that we evaluate the *predictive distribution* defined by

$$
p(t|\mathbf{t}, \alpha, \beta) = \int p(t|\mathbf{w}, \beta) p(\mathbf{w}|\mathbf{t}, \alpha, \beta) \, \mathrm{d}\mathbf{w}
$$
 (3.57)

in which **t** is the vector of target values from the training set, and we have omitted the corresponding input vectors from the right-hand side of the conditioning statements to simplify the notation. The conditional distribution  $p(t|\mathbf{x}, \mathbf{w}, \beta)$  of the target variable is given by (3.8), and the posterior weight distribution is given by (3.49). We see that (3.57) involves the convolution of two Gaussian distributions, and so making use of the result (2.115) from Section 8.1.4, we see that the predictive distribution *Exercise 3.10* takes the form

$$
p(t|\mathbf{x}, \mathbf{t}, \alpha, \beta) = \mathcal{N}(t|\mathbf{m}_{N}^{T}\boldsymbol{\phi}(\mathbf{x}), \sigma_{N}^{2}(\mathbf{x}))
$$
\n(3.58)

where the variance  $\sigma_N^2(\mathbf{x})$  of the predictive distribution is given by

$$
\sigma_N^2(\mathbf{x}) = \frac{1}{\beta} + \phi(\mathbf{x})^T \mathbf{S}_N \phi(\mathbf{x}).
$$
\n(3.59)

The first term in (3.59) represents the noise on the data whereas the second term reflects the uncertainty associated with the parameters **w**. Because the noise process and the distribution of **w** are independent Gaussians, their variances are additive. Note that, as additional data points are observed, the posterior distribution becomes narrower. As a consequence it can be shown (Qazaz *et al.*, 1997) that  $\sigma_{N+1}^2(\mathbf{x}) \leq \sigma^2(\mathbf{x})$ . In the limit  $N \to \infty$  the second term in (3.50) goes to zero, and the variance *Exercise* 3.11  $\sigma_N^2(\mathbf{x})$ . In the limit  $N \to \infty$ , the second term in (3.59) goes to zero, and the variance of the predictive distribution arises solely from the additive noise governed by the parameter  $\beta$ .

> As an illustration of the predictive distribution for Bayesian linear regression models, let us return to the synthetic sinusoidal data set of Section 1.1. In Figure 3.8,

*Exercise 3.10*

Image /page/20/Figure/1 description: The image displays four plots, each showing a function (green line) with a predictive distribution (pink shaded area) and observed data points (blue circles). The x-axis is labeled 'x' and ranges from 0 to 1, while the y-axis is labeled 't' and ranges from -1 to 1. The top left plot shows a sine wave function with two data points, and the predictive distribution is wide. The top right plot shows a similar sine wave with two data points, but the function is shifted. The bottom left plot shows a function with three data points, and the predictive distribution is narrower than in the top plots. The bottom right plot shows a more complex function with multiple data points, and the predictive distribution is relatively narrow, closely following the data.

**Figure 3.8** Examples of the predictive distribution (3.58) for a model consisting of 9 Gaussian basis functions of the form (3.4) using the synthetic sinusoidal data set of Section 1.1. See the text for a detailed discussion.

we fit a model comprising a linear combination of Gaussian basis functions to data sets of various sizes and then look at the corresponding posterior distributions. Here the green curves correspond to the function  $sin(2\pi x)$  from which the data points were generated (with the addition of Gaussian noise). Data sets of size  $N = 1$ ,  $N = 2$ ,  $N = 4$ , and  $N = 25$  are shown in the four plots by the blue circles. For each plot, the red curve shows the mean of the corresponding Gaussian predictive distribution, and the red shaded region spans one standard deviation either side of the mean. Note that the predictive uncertainty depends on  $x$  and is smallest in the neighbourhood of the data points. Also note that the level of uncertainty decreases as more data points are observed.

The plots in Figure 3.8 only show the point-wise predictive variance as a function of  $x$ . In order to gain insight into the covariance between the predictions at different values of <sup>x</sup>, we can draw samples from the posterior distribution over **w**, and then plot the corresponding functions  $y(x, \mathbf{w})$ , as shown in Figure 3.9.

Image /page/21/Figure/1 description: The image displays four plots arranged in a 2x2 grid. Each plot shows a graph with the x-axis labeled 'x' ranging from 0 to 1, and the y-axis labeled 't' ranging from -1 to 1. The plots feature several red curves, representing functions, and some blue circles, indicating data points or specific function evaluations. The top-left plot shows five red curves and one thick green curve, with a single blue circle at approximately (0.2, 0.1). The top-right plot contains five red curves and two blue circles, one at approximately (0.3, 0.2) and another at (0.6, -0.2). The bottom-left plot displays five red curves and three blue circles, located at approximately (0.1, 0.3), (0.4, -0.1), and (0.7, 0.1). The bottom-right plot shows five red curves and multiple blue circles scattered along a sinusoidal-like pattern, with points ranging from x=0 to x=1 and t values between -1 and 1. The overall impression is a visualization of functions and data points, likely related to regression or modeling.

**Figure 3.9** Plots of the function  $y(x, w)$  using samples from the posterior distributions over w corresponding to the plots in Figure 3.8.

If we used localized basis functions such as Gaussians, then in regions away from the basis function centres, the contribution from the second term in the predictive variance (3.59) will go to zero, leaving only the noise contribution  $\beta^{-1}$ . Thus, the model becomes very confident in its predictions when extrapolating outside the region occupied by the basis functions, which is generally an undesirable behaviour. This problem can be avoided by adopting an alternative Bayesian approach to re-*Section 6.4* gression known as a Gaussian process.

Note that, if both **w** and  $\beta$  are treated as unknown, then we can introduce a conjugate prior distribution  $p(\mathbf{w}, \beta)$  that, from the discussion in Section 2.3.6, will *Exercise 3.12* be given by a Gaussian-gamma distribution (Denison *et al.*, 2002). In this case, the *Exercise* 3.13 predictive distribution is a Student's t-distribution.

*Section 6.4*

**Figure 3.10** The equivalent kernel  $k(x,x')$  for the Gaussian basis functions in Figure 3.1, shown as a plot of  $x$  versus  $x'$ , together with three slices through this matrix corresponding to three different values of  $x$ . The data set used to generate this kernel comprised 200 values of  $x$  equally spaced over the interval  $(-1, 1).$ 

Image /page/22/Figure/2 description: The image displays three blue wavy lines on the left side, each with a horizontal yellow line indicating a baseline. These lines are connected by horizontal yellow lines to a colorful heatmap on the right. The heatmap shows a diagonal band of bright yellow and red, indicating high correlation, surrounded by areas of blue and green, indicating lower correlation. The pattern suggests a relationship between the three wavy lines and the heatmap, possibly representing time-series data and its correlation matrix.

#### **3.3.3 Equivalent kernel**

The posterior mean solution (3.53) for the linear basis function model has an interesting interpretation that will set the stage for kernel methods, including Gaussian *Chapter 6* processes. If we substitute (3.53) into the expression (3.3), we see that the predictive mean can be written in the form

$$
y(\mathbf{x}, \mathbf{m}_N) = \mathbf{m}_N^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}) = \beta \boldsymbol{\phi}(\mathbf{x})^{\mathrm{T}} \mathbf{S}_N \boldsymbol{\Phi}^{\mathrm{T}} \mathbf{t} = \sum_{n=1}^N \beta \boldsymbol{\phi}(\mathbf{x})^{\mathrm{T}} \mathbf{S}_N \boldsymbol{\phi}(\mathbf{x}_n) t_n \qquad (3.60)
$$

where  $\mathbf{S}_N$  is defined by (3.51). Thus the mean of the predictive distribution at a point **x** is given by a linear combination of the training set target variables  $t_n$ , so that we can write

$$
y(\mathbf{x}, \mathbf{m}_N) = \sum_{n=1}^{N} k(\mathbf{x}, \mathbf{x}_n) t_n
$$
 (3.61)

where the function

$$
k(\mathbf{x}, \mathbf{x}') = \beta \phi(\mathbf{x})^{\mathrm{T}} \mathbf{S}_N \phi(\mathbf{x}')
$$
 (3.62)

is known as the *smoother matrix* or the *equivalent kernel*. Regression functions, such as this, which make predictions by taking linear combinations of the training set target values are known as *linear smoothers*. Note that the equivalent kernel depends on the input values  $x_n$  from the data set because these appear in the definition of  $\mathbf{S}_N$ . The equivalent kernel is illustrated for the case of Gaussian basis functions in Figure 3.10 in which the kernel functions  $k(x, x')$  have been plotted as a function of  $x'$  for three different values of x. We see that they are localized around x, and so the mean of the predictive distribution at x, given by  $y(x, \mathbf{m}_N)$ , is obtained by forming a weighted combination of the target values in which data points close to  $x$  are given higher weight than points further removed from  $x$ . Intuitively, it seems reasonable that we should weight local evidence more strongly than distant evidence. Note that this localization property holds not only for the localized Gaussian basis functions but also for the nonlocal polynomial and sigmoidal basis functions, as illustrated in Figure 3.11.

*Chapter 6*

**Figure 3.11** Examples of equivalent kernels  $k(x,x')$  for  $x = 0$ plotted as a function of  $x'$ , corresponding (left) to the polynomial basis functions and (right) to the sigmoidal basis functions shown in Figure 3.1. Note that these are localized functions of  $x'$  even though the corresponding basis functions are nonlocal.  $-1$  0 1

Image /page/23/Figure/2 description: Two plots are shown side-by-side. Both plots have the x-axis labeled from -1 to 1 and the y-axis labeled from 0 to 0.04. A blue curve is plotted in each graph, and a red 'x' is marked at the origin (0,0) in both plots. The left plot shows a curve that peaks at approximately 0.025 at x=0, with oscillations that decay as x moves away from 0. The right plot shows a curve that peaks at approximately 0.042 at x=0, with oscillations that decay more slowly than in the left plot.

Further insight into the role of the equivalent kernel can be obtained by considering the covariance between  $y(x)$  and  $y(x')$ , which is given by

$$
\begin{array}{rcl}\n\text{cov}[y(\mathbf{x}), y(\mathbf{x}')] & = & \text{cov}[\phi(\mathbf{x})^{\text{T}} \mathbf{w}, \mathbf{w}^{\text{T}} \phi(\mathbf{x}')] \\
& = & \phi(\mathbf{x})^{\text{T}} \mathbf{S}_N \phi(\mathbf{x}') = \beta^{-1} k(\mathbf{x}, \mathbf{x}')\n\end{array} \tag{3.63}
$$

where we have made use of  $(3.49)$  and  $(3.62)$ . From the form of the equivalent kernel, we see that the predictive mean at nearby points will be highly correlated, whereas for more distant pairs of points the correlation will be smaller.

The predictive distribution shown in Figure 3.8 allows us to visualize the pointwise uncertainty in the predictions, governed by (3.59). However, by drawing samples from the posterior distribution over **w**, and plotting the corresponding model functions  $y(x, w)$  as in Figure 3.9, we are visualizing the joint uncertainty in the posterior distribution between the y values at two (or more) x values, as governed by the equivalent kernel.

The formulation of linear regression in terms of a kernel function suggests an alternative approach to regression as follows. Instead of introducing a set of basis functions, which implicitly determines an equivalent kernel, we can instead define a localized kernel directly and use this to make predictions for new input vectors **x**, given the observed training set. This leads to a practical framework for regression (and classification) called *Gaussian processes*, which will be discussed in detail in Section 6.4.

We have seen that the effective kernel defines the weights by which the training set target values are combined in order to make a prediction at a new value of **x**, and it can be shown that these weights sum to one, in other words

$$
\sum_{n=1}^{N} k(\mathbf{x}, \mathbf{x}_n) = 1
$$
\n(3.64)

*Exercise 3.14*

*Exercise 3.14* for all values of **x**. This intuitively pleasing result can easily be proven informally by noting that the summation is equivalent to considering the predictive mean  $\hat{y}(\mathbf{x})$ for a set of target data in which  $t_n = 1$  for all n. Provided the basis functions are linearly independent, that there are more data points than basis functions, and that one of the basis functions is constant (corresponding to the bias parameter), then it is clear that we can fit the training data exactly and hence that the predictive mean will

be simply  $\hat{y}(\mathbf{x})=1$ , from which we obtain (3.64). Note that the kernel function can be negative as well as positive, so although it satisfies a summation constraint, the corresponding predictions are not necessarily convex combinations of the training set target variables.

Finally, we note that the equivalent kernel (3.62) satisfies an important property *Chapter 6* shared by kernel functions in general, namely that it can be expressed in the form an inner product with respect to a vector  $\psi(\mathbf{x})$  of nonlinear functions, so that

$$
k(\mathbf{x}, \mathbf{z}) = \psi(\mathbf{x})^{\mathrm{T}} \psi(\mathbf{z})
$$
 (3.65)

where  $\psi(\mathbf{x}) = \beta^{1/2} \mathbf{S}_N^{1/2} \phi(\mathbf{x}).$ 

# **3.4. Bayesian Model Comparison**

In Chapter 1, we highlighted the problem of over-fitting as well as the use of crossvalidation as a technique for setting the values of regularization parameters or for choosing between alternative models. Here we consider the problem of model selection from a Bayesian perspective. In this section, our discussion will be very general, and then in Section 3.5 we shall see how these ideas can be applied to the determination of regularization parameters in linear regression.

As we shall see, the over-fitting associated with maximum likelihood can be avoided by marginalizing (summing or integrating) over the model parameters instead of making point estimates of their values. Models can then be compared directly on the training data, without the need for a validation set. This allows all available data to be used for training and avoids the multiple training runs for each model associated with cross-validation. It also allows multiple complexity parameters to be determined simultaneously as part of the training process. For example, in Chapter 7 we shall introduce the *relevance vector machine*, which is a Bayesian model having one complexity parameter for every training data point.

The Bayesian view of model comparison simply involves the use of probabilities to represent uncertainty in the choice of model, along with a consistent application of the sum and product rules of probability. Suppose we wish to compare a set of  $L$ models  $\{M_i\}$  where  $i = 1, \ldots, L$ . Here a model refers to a probability distribution over the observed data  $D$ . In the case of the polynomial curve-fitting problem, the distribution is defined over the set of target values **<sup>t</sup>**, while the set of input values **X** is assumed to be known. Other types of model define a joint distributions over **X** *Section 1.5.4* and **t**. We shall suppose that the data is generated from one of these models but we are uncertain which one. Our uncertainty is expressed through a prior probability distribution  $p(\mathcal{M}_i)$ . Given a training set  $\mathcal{D}$ , we then wish to evaluate the posterior distribution

$$
p(\mathcal{M}_i|\mathcal{D}) \propto p(\mathcal{M}_i)p(\mathcal{D}|\mathcal{M}_i).
$$
\n(3.66)

The prior allows us to express a preference for different models. Let us simply assume that all models are given equal prior probability. The interesting term is the *model evidence*  $p(\mathcal{D}|\mathcal{M}_i)$  which expresses the preference shown by the data for

*Section 1.5.4*

different models, and we shall examine this term in more detail shortly. The model evidence is sometimes also called the *marginal likelihood* because it can be viewed as a likelihood function over the space of models, in which the parameters have been marginalized out. The ratio of model evidences  $p(\mathcal{D}|\mathcal{M}_i)/p(\mathcal{D}|\mathcal{M}_i)$  for two models is known as a *Bayes factor* (Kass and Raftery, 1995).

Once we know the posterior distribution over models, the predictive distribution is given, from the sum and product rules, by

$$
p(t|\mathbf{x}, \mathcal{D}) = \sum_{i=1}^{L} p(t|\mathbf{x}, \mathcal{M}_i, \mathcal{D}) p(\mathcal{M}_i | \mathcal{D}).
$$
 (3.67)

This is an example of a *mixture distribution* in which the overall predictive distribution is obtained by averaging the predictive distributions  $p(t|\mathbf{x}, M_i, \mathcal{D})$  of individual models, weighted by the posterior probabilities  $p(\mathcal{M}_i|D)$  of those models. For instance, if we have two models that are a-posteriori equally likely and one predicts a narrow distribution around  $t = a$  while the other predicts a narrow distribution around  $t = b$ , the overall predictive distribution will be a bimodal distribution with modes at  $t = a$  and  $t = b$ , not a single model at  $t = (a + b)/2$ .

A simple approximation to model averaging is to use the single most probable model alone to make predictions. This is known as *model selection*.

For a model governed by a set of parameters **w**, the model evidence is given, from the sum and product rules of probability, by

$$
p(\mathcal{D}|\mathcal{M}_i) = \int p(\mathcal{D}|\mathbf{w}, \mathcal{M}_i) p(\mathbf{w}|\mathcal{M}_i) \, \mathrm{d}\mathbf{w}.
$$
 (3.68)

# Chapter 11

*Chapter 11* From a sampling perspective, the marginal likelihood can be viewed as the probability of generating the data set  $D$  from a model whose parameters are sampled at random from the prior. It is also interesting to note that the evidence is precisely the normalizing term that appears in the denominator in Bayes' theorem when evaluating the posterior distribution over parameters because

$$
p(\mathbf{w}|\mathcal{D}, \mathcal{M}_i) = \frac{p(\mathcal{D}|\mathbf{w}, \mathcal{M}_i)p(\mathbf{w}|\mathcal{M}_i)}{p(\mathcal{D}|\mathcal{M}_i)}.
$$
(3.69)

We can obtain some insight into the model evidence by making a simple approximation to the integral over parameters. Consider first the case of a model having a single parameter  $w$ . The posterior distribution over parameters is proportional to  $p(\mathcal{D}|w)p(w)$ , where we omit the dependence on the model  $\mathcal{M}_i$  to keep the notation uncluttered. If we assume that the posterior distribution is sharply peaked around the most probable value  $w_{\text{MAP}}$ , with width  $\Delta w_{\text{posterior}}$ , then we can approximate the integral by the value of the integrand at its maximum times the width of the peak. If we further assume that the prior is flat with width  $\Delta w_{\text{prior}}$  so that  $p(w)=1/\Delta w_{\text{prior}}$ , then we have

$$
p(\mathcal{D}) = \int p(\mathcal{D}|w)p(w) \, dw \simeq p(\mathcal{D}|w_{\text{MAP}}) \frac{\Delta w_{\text{posterior}}}{\Delta w_{\text{prior}}} \tag{3.70}
$$

# **3.4. Bayesian Model Comparison 163**

**Figure 3.12** We can obtain a rough approximation to the model evidence if we assume that the posterior distribution over parameters is sharply peaked around its mode  $w_{\text{MAP}}$ .

Image /page/26/Figure/2 description: The image displays a graph with the x-axis labeled 'w' and the y-axis representing probability density. Two curves are plotted: a blue curve and a red curve. The blue curve is wider and flatter, representing a prior distribution, and is labeled with a double-headed arrow indicating '∆w\_prior'. The red curve is narrower and taller, peaking at 'w\_MAP', representing a posterior distribution, and is labeled with a double-headed arrow indicating '∆w\_posterior'. The graph illustrates how a posterior distribution can be more concentrated around a maximum a posteriori (MAP) estimate compared to a prior distribution.

and so taking logs we obtain

$$
\ln p(\mathcal{D}) \simeq \ln p(\mathcal{D}|w_{\text{MAP}}) + \ln \left(\frac{\Delta w_{\text{posterior}}}{\Delta w_{\text{prior}}}\right). \tag{3.71}
$$

This approximation is illustrated in Figure 3.12. The first term represents the fit to the data given by the most probable parameter values, and for a flat prior this would correspond to the log likelihood. The second term penalizes the model according to its complexity. Because  $\Delta w_{\text{posterior}} < \Delta w_{\text{prior}}$  this term is negative, and it increases in magnitude as the ratio  $\Delta w_{\text{posterior}} / \Delta w_{\text{prior}}$  gets smaller. Thus, if parameters are finely tuned to the data in the posterior distribution, then the penalty term is large.

For a model having a set of  $M$  parameters, we can make a similar approximation for each parameter in turn. Assuming that all parameters have the same ratio of  $\Delta w_{\text{posterior}}/\Delta w_{\text{prior}}$ , we obtain

$$
\ln p(\mathcal{D}) \simeq \ln p(\mathcal{D}|\mathbf{w}_{\text{MAP}}) + M \ln \left(\frac{\Delta w_{\text{posterior}}}{\Delta w_{\text{prior}}}\right). \tag{3.72}
$$

Thus, in this very simple approximation, the size of the complexity penalty increases linearly with the number  $M$  of adaptive parameters in the model. As we increase the complexity of the model, the first term will typically decrease, because a more complex model is better able to fit the data, whereas the second term will increase due to the dependence on  $M$ . The optimal model complexity, as determined by the maximum evidence, will be given by a trade-off between these two competing terms. We shall later develop a more refined version of this approximation, based on *Section 4.4.1* a Gaussian approximation to the posterior distribution.

> We can gain further insight into Bayesian model comparison and understand how the marginal likelihood can favour models of intermediate complexity by considering Figure 3.13. Here the horizontal axis is a one-dimensional representation of the space of possible data sets, so that each point on this axis corresponds to a specific data set. We now consider three models  $\mathcal{M}_1$ ,  $\mathcal{M}_2$  and  $\mathcal{M}_3$  of successively increasing complexity. Imagine running these models generatively to produce example data sets, and then looking at the distribution of data sets that result. Any given

*Section 4.4.1*

**Figure 3.13** Schematic illustration of the distribution of data sets for  $p(\mathcal{D})$ three models of different complexity, in which  $\mathcal{M}_1$  is the simplest and  $\mathcal{M}_3$  is the most complex. Note that the distributions are normalized. In this example, for the particular observed data set  $\mathcal{D}_0$ . the model  $\mathcal{M}_2$  with intermediate complexity has the largest evidence.

Image /page/27/Figure/2 description: The image is a graph with the y-axis labeled "p(D)" and the x-axis labeled "D". There are three curves plotted on the graph, labeled M1, M2, and M3. M1 is a blue curve that starts high on the left and drops sharply to zero. M2 is a red curve that starts at a medium height, has a peak with a red dot at D0, and then drops sharply to zero. M3 is a green curve that starts at a low height and stays constant for a while before dropping sharply to zero. The red dot on M2 is connected to the x-axis by a dashed red line at D0.

model can generate a variety of different data sets since the parameters are governed by a prior probability distribution, and for any choice of the parameters there may be random noise on the target variables. To generate a particular data set from a specific model, we first choose the values of the parameters from their prior distribution  $p(\mathbf{w})$ , and then for these parameter values we sample the data from  $p(\mathcal{D}|\mathbf{w})$ . A simple model (for example, based on a first order polynomial) has little variability and so will generate data sets that are fairly similar to each other. Its distribution  $p(\mathcal{D})$ is therefore confined to a relatively small region of the horizontal axis. By contrast, a complex model (such as a ninth order polynomial) can generate a great variety of different data sets, and so its distribution  $p(\mathcal{D})$  is spread over a large region of the space of data sets. Because the distributions  $p(\mathcal{D}|\mathcal{M}_i)$  are normalized, we see that the particular data set  $\mathcal{D}_0$  can have the highest value of the evidence for the model of intermediate complexity. Essentially, the simpler model cannot fit the data well, whereas the more complex model spreads its predictive probability over too broad a range of data sets and so assigns relatively small probability to any one of them.

Implicit in the Bayesian model comparison framework is the assumption that the true distribution from which the data are generated is contained within the set of models under consideration. Provided this is so, we can show that Bayesian model comparison will on average favour the correct model. To see this, consider two models  $\mathcal{M}_1$  and  $\mathcal{M}_2$  in which the truth corresponds to  $\mathcal{M}_1$ . For a given finite data set, it is possible for the Bayes factor to be larger for the incorrect model. However, if we average the Bayes factor over the distribution of data sets, we obtain the expected Bayes factor in the form

$$
\int p(\mathcal{D}|\mathcal{M}_1) \ln \frac{p(\mathcal{D}|\mathcal{M}_1)}{p(\mathcal{D}|\mathcal{M}_2)} d\mathcal{D}
$$
\n(3.73)

where the average has been taken with respect to the true distribution of the data. *Section 1.6.1* This quantity is an example of the *Kullback-Leibler* divergence and satisfies the property of always being positive unless the two distributions are equal in which case it is zero. Thus on average the Bayes factor will always favour the correct model.

> We have seen that the Bayesian framework avoids the problem of over-fitting and allows models to be compared on the basis of the training data alone. However,

Section 1.6.1

a Bayesian approach, like any approach to pattern recognition, needs to make assumptions about the form of the model, and if these are invalid then the results can be misleading. In particular, we see from Figure 3.12 that the model evidence can be sensitive to many aspects of the prior, such as the behaviour in the tails. Indeed, the evidence is not defined if the prior is improper, as can be seen by noting that an improper prior has an arbitrary scaling factor (in other words, the normalization coefficient is not defined because the distribution cannot be normalized). If we consider a proper prior and then take a suitable limit in order to obtain an improper prior (for example, a Gaussian prior in which we take the limit of infinite variance) then the evidence will go to zero, as can be seen from (3.70) and Figure 3.12. It may, however, be possible to consider the evidence ratio between two models first and then take a limit to obtain a meaningful answer.

In a practical application, therefore, it will be wise to keep aside an independent test set of data on which to evaluate the overall performance of the final system.

# **3.5. The Evidence Approximation**

In a fully Bayesian treatment of the linear basis function model, we would introduce prior distributions over the hyperparameters  $\alpha$  and  $\beta$  and make predictions by marginalizing with respect to these hyperparameters as well as with respect to the parameters **w**. However, although we can integrate analytically over either **w** or over the hyperparameters, the complete marginalization over all of these variables is analytically intractable. Here we discuss an approximation in which we set the hyperparameters to specific values determined by maximizing the *marginal likelihood function* obtained by first integrating over the parameters **w**. This framework is known in the statistics literature as *empirical Bayes* (Bernardo and Smith, 1994; Gelman *et al.*, 2004), or *type 2 maximum likelihood* (Berger, 1985), or *generalized maximum likelihood* (Wahba, 1975), and in the machine learning literature is also called the *evidence approximation* (Gull, 1989; MacKay, 1992a).

If we introduce hyperpriors over  $\alpha$  and  $\beta$ , the predictive distribution is obtained by marginalizing over **w**,  $\alpha$  and  $\beta$  so that

$$
p(t|\mathbf{t}) = \iiint p(t|\mathbf{w}, \beta) p(\mathbf{w}|\mathbf{t}, \alpha, \beta) p(\alpha, \beta|\mathbf{t}) \, d\mathbf{w} \, d\alpha \, d\beta \tag{3.74}
$$

where  $p(t|\mathbf{w}, \beta)$  is given by (3.8) and  $p(\mathbf{w}|\mathbf{t}, \alpha, \beta)$  is given by (3.49) with  $\mathbf{m}_N$  and  $\mathbf{S}_N$  defined by (3.53) and (3.54) respectively. Here we have omitted the dependence on the input variable **x** to keep the notation uncluttered. If the posterior distribution  $p(\alpha, \beta | \mathbf{t})$  is sharply peaked around values  $\hat{\alpha}$  and  $\hat{\beta}$ , then the predictive distribution is obtained simply by marginalizing over **w** in which  $\alpha$  and  $\beta$  are fixed to the values  $\hat{\alpha}$ and  $\hat{\beta}$ , so that

$$
p(t|\mathbf{t}) \simeq p(t|\mathbf{t}, \widehat{\alpha}, \widehat{\beta}) = \int p(t|\mathbf{w}, \widehat{\beta}) p(\mathbf{w}|\mathbf{t}, \widehat{\alpha}, \widehat{\beta}) \, \mathrm{d}\mathbf{w}.
$$
 (3.75)