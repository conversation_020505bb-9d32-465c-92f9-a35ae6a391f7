{"table_of_contents": [{"title": "138 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 1, "polygon": [[30.0, 40.5], [277.59375, 40.5], [277.59375, 51.7060546875], [30.0, 51.7060546875]]}, {"title": "3.1. Linear Basis Function Models", "heading_level": null, "page_id": 1, "polygon": [[88.5, 276.75], [304.5, 276.75], [304.5, 289.7490234375], [88.5, 289.7490234375]]}, {"title": "140 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 3, "polygon": [[30.0, 40.5], [279.0703125, 40.5], [279.0703125, 50.89306640625], [30.0, 50.89306640625]]}, {"title": "3.1.1 Maximum likelihood and least squares", "heading_level": null, "page_id": 3, "polygon": [[137.25, 384.0], [385.5, 385.5], [385.5, 396.75], [137.25, 396.4130859375]]}, {"title": "3.1. Linear Basis Function Models 141", "heading_level": null, "page_id": 4, "polygon": [[267.75, 40.5], [472.5, 40.5], [472.5, 51.0556640625], [267.75, 51.0556640625]]}, {"title": "142 3. LINEA<PERSON> MODELS FOR REGRESSION", "heading_level": null, "page_id": 5, "polygon": [[30.0, 40.5], [278.0859375, 40.5], [278.0859375, 51.624755859375], [30.0, 51.624755859375]]}, {"title": "3.1. Linear Basis Function Models 143", "heading_level": null, "page_id": 6, "polygon": [[265.78125, 41.25], [473.25, 41.25], [473.25, 51.136962890625], [265.78125, 51.136962890625]]}, {"title": "3.1.2 Geometry of least squares", "heading_level": null, "page_id": 6, "polygon": [[138.75, 225.75], [321.0, 225.75], [321.0, 236.90478515625], [138.75, 236.90478515625]]}, {"title": "3.1.3 Sequential learning", "heading_level": null, "page_id": 6, "polygon": [[137.8125, 552.75], [282.26953125, 552.75], [282.26953125, 564.8642578125], [137.8125, 564.8642578125]]}, {"title": "144 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 7, "polygon": [[30.0, 40.5], [278.578125, 40.5], [278.578125, 51.8280029296875], [30.0, 51.8280029296875]]}, {"title": "3.1.4 Regularized least squares", "heading_level": null, "page_id": 7, "polygon": [[136.828125, 307.5], [317.25, 307.5], [317.25, 318.69140625], [136.828125, 318.69140625]]}, {"title": "146 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 9, "polygon": [[29.9619140625, 40.5], [277.59375, 40.5], [277.59375, 51.21826171875], [29.9619140625, 51.21826171875]]}, {"title": "3.1.5 Multiple outputs", "heading_level": null, "page_id": 9, "polygon": [[137.56640625, 293.25], [264.75, 293.25], [264.75, 305.19580078125], [137.56640625, 305.19580078125]]}, {"title": "3.2. <PERSON> Bias-<PERSON><PERSON><PERSON> 147", "heading_level": null, "page_id": 10, "polygon": [[250.5, 40.5], [473.25, 40.5], [473.25, 51.1776123046875], [250.5, 51.1776123046875]]}, {"title": "3.2. <PERSON> Bias-<PERSON><PERSON><PERSON> Decomposition", "heading_level": null, "page_id": 10, "polygon": [[88.2861328125, 315.75], [330.75, 315.75], [330.75, 328.60986328125], [88.2861328125, 328.60986328125]]}, {"title": "148 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 11, "polygon": [[30.0, 40.5], [279.0703125, 40.5], [279.0703125, 51.5028076171875], [30.0, 51.5028076171875]]}, {"title": "3.2. <PERSON> Bias-<PERSON><PERSON><PERSON>om<PERSON> 149", "heading_level": null, "page_id": 12, "polygon": [[250.5, 41.25], [474.0, 41.25], [474.0, 51.136962890625], [250.5, 51.136962890625]]}, {"title": "3.2. <PERSON> Bias-<PERSON><PERSON><PERSON> Decomposition 151", "heading_level": null, "page_id": 14, "polygon": [[250.03125, 41.25], [472.5, 41.25], [472.5, 50.7711181640625], [250.03125, 50.7711181640625]]}, {"title": "152 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 15, "polygon": [[30.0, 40.5], [279.0703125, 40.5], [279.0703125, 51.86865234375], [30.0, 51.86865234375]]}, {"title": "3.3. Bayesian Linear Regression", "heading_level": null, "page_id": 15, "polygon": [[88.5, 252.0], [294.0, 253.5], [294.0, 266.009765625], [88.5, 266.009765625]]}, {"title": "3.3.1 Parameter distribution", "heading_level": null, "page_id": 15, "polygon": [[138.0, 500.25], [297.75, 500.25], [297.75, 511.20703125], [138.0, 511.20703125]]}, {"title": "3.3. Bayesian Linear Regression 153", "heading_level": null, "page_id": 16, "polygon": [[277.34765625, 41.25], [474.0, 41.25], [474.0, 51.2589111328125], [277.34765625, 51.2589111328125]]}, {"title": "154 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 17, "polygon": [[30.0, 40.5], [278.33203125, 40.5], [278.33203125, 51.380859375], [30.0, 51.380859375]]}, {"title": "156 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 19, "polygon": [[30.0, 40.5], [279.0703125, 40.5], [279.0703125, 51.8280029296875], [30.0, 51.8280029296875]]}, {"title": "3.3.2 Predictive distribution", "heading_level": null, "page_id": 19, "polygon": [[138.0, 242.25], [296.296875, 242.25], [296.296875, 254.14013671875], [138.0, 254.14013671875]]}, {"title": "158 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 21, "polygon": [[30.0, 40.5], [277.5, 40.5], [277.5, 51.299560546875], [30.0, 51.299560546875]]}, {"title": "3.3.3 Equivalent kernel", "heading_level": null, "page_id": 22, "polygon": [[138.75, 225.0], [271.5, 225.0], [271.5, 235.7666015625], [138.75, 235.7666015625]]}, {"title": "160 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 23, "polygon": [[30.0, 40.5], [278.33203125, 40.5], [278.33203125, 51.299560546875], [30.0, 51.299560546875]]}, {"title": "3.4. Bayesian Model Comparison", "heading_level": null, "page_id": 24, "polygon": [[89.25, 226.5], [299.25, 226.5], [299.25, 239.83154296875], [89.25, 239.83154296875]]}, {"title": "162 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 25, "polygon": [[30.0, 40.5], [278.578125, 40.5], [278.578125, 51.54345703125], [30.0, 51.54345703125]]}, {"title": "3.4. Bayesian Model Comparison 163", "heading_level": null, "page_id": 26, "polygon": [[271.1953125, 41.25], [473.25, 41.25], [473.25, 51.21826171875], [271.1953125, 51.21826171875]]}, {"title": "164 3. LINEAR MODELS FOR REGRESSION", "heading_level": null, "page_id": 27, "polygon": [[30.0, 40.5], [278.33203125, 40.5], [278.33203125, 51.54345703125], [30.0, 51.54345703125]]}, {"title": "3.5. The Evidence Approximation", "heading_level": null, "page_id": 28, "polygon": [[89.25, 264.75], [299.25, 264.75], [299.25, 277.87939453125], [89.25, 277.87939453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["Line", 19], ["Picture", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7004, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 44], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 49], ["TextInlineMath", 5], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 47], ["Text", 6], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 54], ["Text", 5], ["Equation", 5], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1034, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 88], ["Text", 8], ["Equation", 8], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1806, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 46], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2], ["Caption", 1], ["Picture", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 51], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 48], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1236, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 54], ["Text", 3], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1838, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 47], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 44], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 500], ["Line", 52], ["Text", 7], ["Equation", 6], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 169], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 778, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 71], ["Text", 3], ["Equation", 3], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 899, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 42], ["Text", 7], ["SectionHeader", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 51], ["Text", 8], ["Equation", 7], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1274, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 46], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Line", 87], ["Span", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 60], ["Text", 6], ["Equation", 4], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 44], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 38], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1417, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 52], ["Text", 3], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1231, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 59], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1267, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 42], ["Text", 5], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 46], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 577, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 40], ["Text", 4], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 50], ["Text", 4], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1256, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 39], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_157-185"}