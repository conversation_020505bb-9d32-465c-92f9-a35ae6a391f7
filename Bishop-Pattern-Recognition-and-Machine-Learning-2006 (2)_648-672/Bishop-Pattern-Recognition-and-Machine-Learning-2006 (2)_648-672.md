From the product rule, we then have

$$
p(\mathbf{x}_1,\ldots,\mathbf{x}_n)=\prod_{m=1}^n c_m
$$
 (13.57)

and so

$$
\alpha(\mathbf{z}_n) = p(\mathbf{z}_n | \mathbf{x}_1, \dots, \mathbf{x}_n) p(\mathbf{x}_1, \dots, \mathbf{x}_n) = \left(\prod_{m=1}^n c_m\right) \widehat{\alpha}(\mathbf{z}_n).
$$
 (13.58)

We can then turn the recursion equation (13.36) for  $\alpha$  into one for  $\hat{\alpha}$  given by

$$
c_n \widehat{\alpha}(\mathbf{z}_n) = p(\mathbf{x}_n | \mathbf{z}_n) \sum_{\mathbf{z}_{n-1}} \widehat{\alpha}(\mathbf{z}_{n-1}) p(\mathbf{z}_n | \mathbf{z}_{n-1}).
$$
 (13.59)

Note that at each stage of the forward message passing phase, used to evaluate  $\hat{\alpha}(\mathbf{z}_n)$ , we have to evaluate and store  $c_n$ , which is easily done because it is the coefficient that normalizes the right-hand side of (13.59) to give  $\widehat{\alpha}(\mathbf{z}_n)$ .

We can similarly define re-scaled variables  $\hat{\beta}(\mathbf{z}_n)$  using

$$
\beta(\mathbf{z}_n) = \left(\prod_{m=n+1}^N c_m\right) \widehat{\beta}(\mathbf{z}_n)
$$
\n(13.60)

which will again remain within machine precision because, from  $(13.35)$ , the quantities  $\widehat{\beta}(\mathbf{z}_n)$  are simply the ratio of two conditional probabilities

$$
\widehat{\beta}(\mathbf{z}_n) = \frac{p(\mathbf{x}_{n+1}, \dots, \mathbf{x}_N | \mathbf{z}_n)}{p(\mathbf{x}_{n+1}, \dots, \mathbf{x}_N | \mathbf{x}_1, \dots, \mathbf{x}_n)}.
$$
(13.61)

The recursion result (13.38) for  $\beta$  then gives the following recursion for the re-scaled variables

$$
c_{n+1}\widehat{\beta}(\mathbf{z}_n) = \sum_{\mathbf{z}_{n+1}} \widehat{\beta}(\mathbf{z}_{n+1}) p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1}) p(\mathbf{z}_{n+1}|\mathbf{z}_n).
$$
 (13.62)

In applying this recursion relation, we make use of the scaling factors  $c_n$  that were previously computed in the  $\alpha$  phase.

From (13.57), we see that the likelihood function can be found using

$$
p(\mathbf{X}) = \prod_{n=1}^{N} c_n.
$$
 (13.63)

Similarly, using (13.33) and (13.43), together with (13.63), we see that the required *Exercise 13.15* marginals are given by

$$
\gamma(\mathbf{z}_n) = \widehat{\alpha}(\mathbf{z}_n)\widehat{\beta}(\mathbf{z}_n)
$$
\n(13.64)

$$
\xi(\mathbf{z}_{n-1}, \mathbf{z}_n) = c_n \widehat{\alpha}(\mathbf{z}_{n-1}) p(\mathbf{x}_n | \mathbf{z}_n) p(\mathbf{z}_n | \mathbf{z}_{n-1}) \beta(\mathbf{z}_n).
$$
 (13.65)

Finally, we note that there is an alternative formulation of the forward-backward algorithm (Jordan, 2007) in which the backward pass is defined by a recursion based the quantities  $\gamma(\mathbf{z}_n) = \hat{\alpha}(\mathbf{z}_n)\hat{\beta}(\mathbf{z}_n)$  instead of using  $\hat{\beta}(\mathbf{z}_n)$ . This  $\alpha$ - $\gamma$  recursion requires that the forward pass be completed first so that all the quantities  $\hat{\alpha}(\mathbf{z}_n)$ are available for the backward pass, whereas the forward and backward passes of the  $\alpha-\beta$  algorithm can be done independently. Although these two algorithms have comparable computational cost, the  $\alpha-\beta$  version is the most commonly encountered *Section 13.3* one in the case of hidden Markov models, whereas for linear dynamical systems a recursion analogous to the  $\alpha-\gamma$  form is more usual.

### 13.2.5 The Viterbi algorithm

In many applications of hidden Markov models, the latent variables have some meaningful interpretation, and so it is often of interest to find the most probable sequence of hidden states for a given observation sequence. For instance in speech recognition, we might wish to find the most probable phoneme sequence for a given series of acoustic observations. Because the graph for the hidden Markov model is a directed tree, this problem can be solved exactly using the max-sum algorithm. We recall from our discussion in Section 8.4.5 that the problem of finding the most probable sequence of latent states is not the same as that of finding the set of states that are individually the most probable. The latter problem can be solved by first running the forward-backward (sum-product) algorithm to find the latent variable marginals  $\gamma(\mathbf{z}_n)$  and then maximizing each of these individually (Duda *et al.*, 2001). However, the set of such states will not, in general, correspond to the most probable sequence of states. In fact, this set of states might even represent a sequence having zero probability, if it so happens that two successive states, which in isolation are individually the most probable, are such that the transition matrix element connecting them is zero.

In practice, we are usually interested in finding the most probable *sequence* of states, and this can be solved efficiently using the max-sum algorithm, which in the context of hidden Markov models is known as the *Viterbi* algorithm (Viterbi, 1967). Note that the max-sum algorithm works with log probabilities and so there is no need to use re-scaled variables as was done with the forward-backward algorithm. Figure 13.16 shows a fragment of the hidden Markov model expanded as lattice diagram. As we have already noted, the number of possible paths through the lattice grows exponentially with the length of the chain. The Viterbi algorithm searches this space of paths efficiently to find the most probable path with a computational cost that grows only linearly with the length of the chain.

As with the sum-product algorithm, we first represent the hidden Markov model as a factor graph, as shown in Figure 13.15. Again, we treat the variable node  $z_N$ as the root, and pass messages to the root starting with the leaf nodes. Using the results (8.93) and (8.94), we see that the messages passed in the max-sum algorithm are given by

$$
\mu_{\mathbf{z}_n \to f_{n+1}}(\mathbf{z}_n) = \mu_{f_n \to \mathbf{z}_n}(\mathbf{z}_n)
$$
\n(13.66)

$$
\mu_{f_{n+1}\to\mathbf{z}_{n+1}}(\mathbf{z}_{n+1}) = \max_{\mathbf{z}_n} \left\{ \ln f_{n+1}(\mathbf{z}_n, \mathbf{z}_{n+1}) + \mu_{\mathbf{z}_n\to f_{n+1}}(\mathbf{z}_n) \right\}.
$$
 (13.67)

*Section 13.3*

**Figure 13.16** A fragment of the HMM lattice showing two possible paths. The Viterbi algorithm efficiently determines the most probable path from amongst the exponentially many possibilities. For any given path, the corresponding probability is given by the product of the elements of the transition matrix  $A_{ik}$ , corresponding to the probabilities  $p(\mathbf{z}_{n+1}|\mathbf{z}_n)$  for each segment of the path, along with the emission densities  $p(\mathbf{x}_n|k)$  associated with each node on the path.

 $k = 1$  $k = 2$  $k = 3$  $n-2$  n − 1 n n + 1

If we eliminate  $\mu_{\mathbf{z}_n \to f_{n+1}}(\mathbf{z}_n)$  between these two equations, and make use of (13.46), we obtain a recursion for the  $f \rightarrow z$  messages of the form

$$
\omega(\mathbf{z}_{n+1}) = \ln p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1}) + \max_{\mathbf{z}_n} \left\{ \ln p(\mathbf{x}_{n+1}|\mathbf{z}_n) + \omega(\mathbf{z}_n) \right\} \tag{13.68}
$$

where we have introduced the notation  $\omega(\mathbf{z}_n) \equiv \mu_{f_n \to \mathbf{z}_n}(\mathbf{z}_n)$ . From (8.95) and (8.96), these messages are initialized using

$$
\omega(\mathbf{z}_1) = \ln p(\mathbf{z}_1) + \ln p(\mathbf{x}_1 | \mathbf{z}_1).
$$
 (13.69)

where we have used (13.45). Note that to keep the notation uncluttered, we omit the dependence on the model parameters  $\theta$  that are held fixed when finding the most probable sequence.

The Viterbi algorithm can also be derived directly from the definition (13.6) of the joint distribution by taking the logarithm and then exchanging maximizations *Exercise 13.16* and summations. It is easily seen that the quantities  $\omega(\mathbf{z}_n)$  have the probabilistic interpretation

$$
\omega(\mathbf{z}_n) = \max_{\mathbf{z}_1,\dots,\mathbf{z}_{n-1}} p(\mathbf{x}_1,\dots,\mathbf{x}_n,\mathbf{z}_1,\dots,\mathbf{z}_n).
$$
 (13.70)

Once we have completed the final maximization over  $z_N$ , we will obtain the value of the joint distribution  $p(X, Z)$  corresponding to the most probable path. We also wish to find the sequence of latent variable values that corresponds to this path. To do this, we simply make use of the back-tracking procedure discussed in Section 8.4.5. Specifically, we note that the maximization over  $z_n$  must be performed for each of the K possible values of  $z_{n+1}$ . Suppose we keep a record of the values of  $z_n$  that correspond to the maxima for each value of the K values of  $z_{n+1}$ . Let us denote this function by  $\psi(k_n)$  where  $k \in \{1, \ldots, K\}$ . Once we have passed messages to the end of the chain and found the most probable state of  $\mathbf{z}_N$ , we can then use this function to backtrack along the chain by applying it recursively

$$
k_n^{\max} = \psi(k_{n+1}^{\max}).
$$
\n(13.71)

*Exercise 13.16*

Intuitively, we can understand the Viterbi algorithm as follows. Naively, we could consider explicitly all of the exponentially many paths through the lattice, evaluate the probability for each, and then select the path having the highest probability. However, we notice that we can make a dramatic saving in computational cost as follows. Suppose that for each path we evaluate its probability by summing up products of transition and emission probabilities as we work our way forward along each path through the lattice. Consider a particular time step  $n$  and a particular state  $k$  at that time step. There will be many possible paths converging on the corresponding node in the lattice diagram. However, we need only retain that particular path that so far has the highest probability. Because there are  $K$  states at time step  $n$ , we need to keep track of K such paths. At time step  $n + 1$ , there will be  $K^2$  possible paths to consider, comprising K possible paths leading out of each of the K current states, but again we need only retain  $K$  of these corresponding to the best path for each state at time  $n+1$ . When we reach the final time step N we will discover which state corresponds to the overall most probable path. Because there is a unique path coming into that state we can trace the path back to step  $N - 1$  to see what state it occupied at that time, and so on back through the lattice to the state  $n = 1$ .

#### 13.2.6 Extensions of the hidden Markov model

The basic hidden Markov model, along with the standard training algorithm based on maximum likelihood, has been extended in numerous ways to meet the requirements of particular applications. Here we discuss a few of the more important examples.

We see from the digits example in Figure 13.11 that hidden Markov models can be quite poor generative models for the data, because many of the synthetic digits look quite unrepresentative of the training data. If the goal is sequence classification, there can be significant benefit in determining the parameters of hidden Markov models using discriminative rather than maximum likelihood techniques. Suppose we have a training set of R observation sequences  $X_r$ , where  $r = 1, \ldots, R$ , each of which is labelled according to its class m, where  $m = 1, \ldots, M$ . For each class, we have a separate hidden Markov model with its own parameters  $\theta_m$ , and we treat the problem of determining the parameter values as a standard classification problem in which we optimize the cross-entropy

$$
\sum_{r=1}^{R} \ln p(m_r | \mathbf{X}_r). \tag{13.72}
$$

Using Bayes' theorem this can be expressed in terms of the sequence probabilities associated with the hidden Markov models

$$
\sum_{r=1}^{R} \ln \left\{ \frac{p(\mathbf{X}_r | \boldsymbol{\theta}_r) p(m_r)}{\sum_{l=1}^{M} p(\mathbf{X}_r | \boldsymbol{\theta}_l) p(l_r)} \right\}
$$

(13.73)

where  $p(m)$  is the prior probability of class m. Optimization of this cost function is more complex than for maximum likelihood (Kapadia, 1998), and in particular

**Figure 13.17** Section of an autoregressive hidden Markov model, in which the distribution of the observation  $x_n$  depends on a subset of the previous observations as well as on the hidden state  $z_n$ . In this example, the distribution of  $x_n$  depends on the two previous observations  $x_{n-1}$ and  $\mathbf{x}_{n-2}$ .

Image /page/4/Figure/2 description: This is a diagram illustrating a dynamic Bayesian network. It features two rows of nodes, representing latent variables (top row, labeled z\_n-1, z\_n, z\_n+1) and observed variables (bottom row, labeled x\_n-1, x\_n, x\_n+1). The latent variables are depicted as open circles, while the observed variables are filled blue circles. Arrows indicate the flow of influence: each latent variable influences the next latent variable in the sequence (horizontal arrows), and each latent variable influences its corresponding observed variable (vertical arrows). Additionally, there are curved arrows showing that each observed variable also influences the next observed variable in the sequence, indicating a temporal dependency or feedback loop.

requires that every training sequence be evaluated under each of the models in order to compute the denominator in (13.73). Hidden Markov models, coupled with discriminative training methods, are widely used in speech recognition (Kapadia, 1998).

A significant weakness of the hidden Markov model is the way in which it represents the distribution of times for which the system remains in a given state. To see the problem, note that the probability that a sequence sampled from a given hidden Markov model will spend precisely T steps in state k and then make a transition to a different state is given by

$$
p(T) = (A_{kk})^T (1 - A_{kk}) \propto \exp(-T \ln A_{kk})
$$
 (13.74)

and so is an exponentially decaying function of  $T$ . For many applications, this will be a very unrealistic model of state duration. The problem can be resolved by modelling state duration directly in which the diagonal coefficients  $A_{kk}$  are all set to zero, and each state k is explicitly associated with a probability distribution  $p(T|k)$  of possible duration times. From a generative point of view, when a state  $k$  is entered, a value  $T$  representing the number of time steps that the system will remain in state  $k$ is then drawn from  $p(T|k)$ . The model then emits T values of the observed variable  $x_t$ , which are generally assumed to be independent so that the corresponding emission density is simply  $\prod_{t=1}^{T} p(\mathbf{x}_t|k)$ . This approach requires some straightforward<br>modifications to the FM ontimization procedure (Rabiner, 1989) modifications to the EM optimization procedure (Rabiner, 1989).

Another limitation of the standard HMM is that it is poor at capturing longrange correlations between the observed variables (i.e., between variables that are separated by many time steps) because these must be mediated via the first-order Markov chain of hidden states. Longer-range effects could in principle be included by adding extra links to the graphical model of Figure 13.5. One way to address this is to generalize the HMM to give the *autoregressive hidden Markov model* (Ephraim *et al.*, 1989), an example of which is shown in Figure 13.17. For discrete observations, this corresponds to expanded tables of conditional probabilities for the emission distributions. In the case of a Gaussian emission density, we can use the linear-Gaussian framework in which the conditional distribution for  $x_n$  given the values of the previous observations, and the value of  $z_n$ , is a Gaussian whose mean is a linear combination of the values of the conditioning variables. Clearly the number of additional links in the graph must be limited to avoid an excessive the number of free parameters. In the example shown in Figure 13.17, each observation depends on

#### 13.2. Hidden Markov Models

**Figure 13.18** Example of an input-output hidden Markov model. In this case, both the emission probabilities and the transition probabilities depend on the values of a sequence of observations  $\mathbf{u}_1, \ldots, \mathbf{u}_N$ .

Image /page/5/Figure/2 description: This is a graphical representation of a dynamic Bayesian network. It shows three time steps, indexed by n-1, n, and n+1. At each time step, there are three variables: u, z, and x. The variable u is represented by a blue circle, and the variables z and x are represented by white circles. There are directed edges indicating dependencies between these variables. Specifically, u\_{n-1} influences z\_n and x\_{n-1}. u\_n influences z\_{n+1} and x\_n. u\_{n+1} influences x\_{n+1}. Additionally, z\_{n-1} influences z\_n, and z\_n influences z\_{n+1}. The variables z and x are also connected sequentially, with z\_{n-1} influencing x\_{n-1}, z\_n influencing x\_n, and z\_{n+1} influencing x\_{n+1}. The arrows indicate the direction of influence or causality.

the two preceding observed variables as well as on the hidden state. Although this graph looks messy, we can again appeal to d-separation to see that in fact it still has a simple probabilistic structure. In particular, if we imagine conditioning on  $z_n$  we see that, as with the standard HMM, the values of  $z_{n-1}$  and  $z_{n+1}$  are independent, corresponding to the conditional independence property (13.5). This is easily verified by noting that every path from node  $z_{n-1}$  to node  $z_{n+1}$  passes through at least one observed node that is head-to-tail with respect to that path. As a consequence, we can again use a forward-backward recursion in the E step of the EM algorithm to determine the posterior distributions of the latent variables in a computational time that is linear in the length of the chain. Similarly, the M step involves only a minor modification of the standard M-step equations. In the case of Gaussian emission densities this involves estimating the parameters using the standard linear regression equations, discussed in Chapter 3.

We have seen that the autoregressive HMM appears as a natural extension of the standard HMM when viewed as a graphical model. In fact the probabilistic graphical modelling viewpoint motivates a plethora of different graphical structures based on the HMM. Another example is the *input-output* hidden Markov model (Bengio and Frasconi, 1995), in which we have a sequence of observed variables  $\mathbf{u}_1, \dots, \mathbf{u}_N$ , in addition to the output variables  $\mathbf{x}_1, \ldots, \mathbf{x}_N$ , whose values influence either the distribution of latent variables or output variables, or both. An example is shown in Figure 13.18. This extends the HMM framework to the domain of supervised learning for sequential data. It is again easy to show, through the use of the d-separation criterion, that the Markov property (13.5) for the chain of latent variables still holds. To verify this, simply note that there is only one path from node  $z_{n-1}$  to node  $z_{n+1}$ and this is head-to-tail with respect to the observed node  $z_n$ . This conditional independence property again allows the formulation of a computationally efficient learning algorithm. In particular, we can determine the parameters  $\theta$  of the model by maximizing the likelihood function  $L(\theta) = p(\mathbf{X}|\mathbf{U}, \theta)$  where **U** is a matrix whose rows are given by  $\mathbf{u}_n^{\mathrm{T}}$ . As a consequence of the conditional independence property (13.5) this likelihood function can be maximized efficiently using an EM algorithm (13.5) this likelihood function can be maximized efficiently using an EM algorithm *Exercise 13.18* in which the E step involves forward and backward recursions.

> Another variant of the HMM worthy of mention is the *factorial hidden Markov model* (Ghahramani and Jordan, 1997), in which there are multiple independent

**Figure 13.19** A factorial hidden Markov model comprising two Markov chains of latent variables. For continuous observed variables **x**, one possible choice of emission model is a linear-Gaussian density in which the mean of the Gaussian is a linear combination of the states of the corresponding latent variables.

Image /page/6/Figure/2 description: This is a diagram illustrating a probabilistic graphical model. It shows two layers of latent variables, denoted as z^{(1)} and z^{(2)}, evolving over time steps n-1, n, and n+1. Each latent variable at time step n is connected to the latent variable at time step n+1, indicating a temporal dependency. Additionally, each latent variable at time step n is connected to an observed variable x\_n, suggesting that the observed variables are generated from the latent variables. The diagram uses circles to represent latent variables (white circles) and observed variables (blue circles), and arrows to represent probabilistic dependencies. The arrows between latent variables indicate transitions, and the arrows from latent variables to observed variables indicate emission or observation probabilities.

Markov chains of latent variables, and the distribution of the observed variable at a given time step is conditional on the states of all of the corresponding latent variables at that same time step. Figure 13.19 shows the corresponding graphical model. The motivation for considering factorial HMM can be seen by noting that in order to represent, say, 10 bits of information at a given time step, a standard HMM would need  $K = 2^{10} = 1024$  latent states, whereas a factorial HMM could make use of 10 binary latent chains. The primary disadvantage of factorial HMMs, however, lies in the additional complexity of training them. The M step for the factorial HMM model is straightforward. However, observation of the **x** variables introduces dependencies between the latent chains, leading to difficulties with the E step. This can be seen by noting that in Figure 13.19, the variables  $z_n^{(1)}$  and  $z_n^{(2)}$  are connected by a path which is head-to-head at node **x** and hence they are not d-separated. The exact E which is head-to-head at node  $x_n$  and hence they are not d-separated. The exact E step for this model does *not* correspond to running forward and backward recursions along the  $M$  Markov chains independently. This is confirmed by noting that the key conditional independence property (13.5) is not satisfied for the individual Markov chains in the factorial HMM model, as is shown using d-separation in Figure 13.20. Now suppose that there are  $M$  chains of hidden nodes and for simplicity suppose that all latent variables have the same number  $K$  of states. Then one approach would be to note that there are  $K^M$  combinations of latent variables at a given time step

**Figure 13.20** Example of a path, highlighted in green, which is head-to-head at the observed nodes **x**<sup>n</sup>−<sup>1</sup> and **x**n+1, and head-to-tail at the unobserved nodes  $\mathbf{z}_{n-1}^{(2)}$ ,  $\mathbf{z}_n^{(2)}$  and  $\mathbf{z}_{n+1}^{(2)}$ . Thus the path is not blocked and so the conditional independence property (13.5) does not hold for the individual latent chains of the factorial HMM model. As a consequence, there is no efficient exact E step for this model.

Image /page/6/Figure/5 description: This is a diagram illustrating a temporal model with two layers of latent variables, denoted as z^(1) and z^(2), and observed variables x. The diagram shows three time steps: n-1, n, and n+1. In each time step, there are two latent variables and one observed variable. The latent variables are represented by circles, with z^(1) variables in the bottom layer and z^(2) variables in the top layer. The observed variables x are represented by filled blue circles. Arrows indicate the flow of information or dependencies. There are horizontal arrows connecting latent variables within the same layer across time steps, suggesting a temporal dependency. There are also vertical arrows connecting latent variables to observed variables within the same time step, indicating that observed variables depend on latent variables. Additionally, there are curved arrows connecting latent variables from the previous time step to the current time step in the same layer, and also across layers, suggesting more complex dependencies. Some latent variables are depicted as white circles, while others are depicted as blue circles, though the provided text does not specify the meaning of this color difference. The diagram highlights temporal evolution and dependencies between variables over time.

and so we can transform the model into an equivalent standard HMM having a single chain of latent variables each of which has  $K^M$  latent states. We can then run the standard forward-backward recursions in the E step. This has computational complexity  $O(NK^{2M})$  that is exponential in the number M of latent chains and so will be intractable for anything other than small values of  $M$ . One solution would be to use sampling methods (discussed in Chapter 11). As an elegant deterministic al-*Section 10.1* ternative, Ghahramani and Jordan (1997) exploited variational inference techniques to obtain a tractable algorithm for approximate inference. This can be done using a simple variational posterior distribution that is fully factorized with respect to the latent variables, or alternatively by using a more powerful approach in which the variational distribution is described by independent Markov chains corresponding to the chains of latent variables in the original model. In the latter case, the variational inference algorithms involves running independent forward and backward recursions along each chain, which is computationally efficient and yet is also able to capture correlations between variables within the same chain.

> Clearly, there are many possible probabilistic structures that can be constructed according to the needs of particular applications. Graphical models provide a general technique for motivating, describing, and analysing such structures, and variational methods provide a powerful framework for performing inference in those models for which exact solution is intractable.

## 13.3. Linear Dynamical Systems

In order to motivate the concept of linear dynamical systems, let us consider the following simple problem, which often arises in practical settings. Suppose we wish to measure the value of an unknown quantity **z** using a noisy sensor that returns a observation **x** representing the value of **z** plus zero-mean Gaussian noise. Given a single measurement, our best guess for **z** is to assume that  $z = x$ . However, we can improve our estimate for **z** by taking lots of measurements and averaging them, because the random noise terms will tend to cancel each other. Now let's make the situation more complicated by assuming that we wish to measure a quantity **z** that is changing over time. We can take regular measurements of **x** so that at some point in time we have obtained  $x_1, \ldots, x_N$  and we wish to find the corresponding values  $z_1, \ldots, x_N$ . If we simply average the measurements, the error due to random noise will be reduced, but unfortunately we will just obtain a single averaged estimate, in which we have averaged over the changing value of **z**, thereby introducing a new source of error.

Intuitively, we could imagine doing a bit better as follows. To estimate the value of  $z_N$ , we take only the most recent few measurements, say  $x_{N-L}, \ldots, x_N$  and just average these. If **z** is changing slowly, and the random noise level in the sensor is high, it would make sense to choose a relatively long window of observations to average. Conversely, if the signal is changing quickly, and the noise levels are small, we might be better just to use  $\mathbf{x}_N$  directly as our estimate of  $\mathbf{z}_N$ . Perhaps we could do even better if we take a weighted average, in which more recent measurements

*Section 10.1*

make a greater contribution than less recent ones.

Although this sort of intuitive argument seems plausible, it does not tell us how to form a weighted average, and any sort of hand-crafted weighing is hardly likely to be optimal. Fortunately, we can address problems such as this much more systematically by defining a probabilistic model that captures the time evolution and measurement processes and then applying the inference and learning methods developed in earlier chapters. Here we shall focus on a widely used model known as a *linear dynamical system*.

As we have seen, the HMM corresponds to the state space model shown in Figure 13.5 in which the latent variables are discrete but with arbitrary emission probability distributions. This graph of course describes a much broader class of probability distributions, all of which factorize according to (13.6). We now consider extensions to other distributions for the latent variables. In particular, we consider continuous latent variables in which the summations of the sum-product algorithm become integrals. The general form of the inference algorithms will, however, be the same as for the hidden Markov model. It is interesting to note that, historically, hidden Markov models and linear dynamical systems were developed independently. Once they are both expressed as graphical models, however, the deep relationship between them immediately becomes apparent.

One key requirement is that we retain an efficient algorithm for inference which is linear in the length of the chain. This requires that, for instance, when we take a quantity  $\hat{\alpha}(\mathbf{z}_{n-1})$ , representing the posterior probability of  $\mathbf{z}_n$  given observations  $x_1, \ldots, x_n$ , and multiply by the transition probability  $p(\mathbf{z}_n|\mathbf{z}_{n-1})$  and the emission probability  $p(\mathbf{x}_n|\mathbf{z}_n)$  and then marginalize over  $\mathbf{z}_{n-1}$ , we obtain a distribution over  $z_n$  that is of the same functional form as that over  $\hat{\alpha}(z_{n-1})$ . That is to say, the distribution must not become more complex at each stage, but must only change in its parameter values. Not surprisingly, the only distributions that have this property of being closed under multiplication are those belonging to the exponential family.

Here we consider the most important example from a practical perspective, which is the Gaussian. In particular, we consider a linear-Gaussian state space model so that the latent variables  $\{z_n\}$ , as well as the observed variables  $\{x_n\}$ , are multivariate Gaussian distributions whose means are linear functions of the states of their parents in the graph. We have seen that a directed graph of linear-Gaussian units is equivalent to a joint Gaussian distribution over all of the variables. Furthermore, marginals such as  $\hat{\alpha}(z_n)$  are also Gaussian, so that the functional form of the messages is preserved and we will obtain an efficient inference algorithm. By contrast, suppose that the emission densities  $p(\mathbf{x}_n|\mathbf{z}_n)$  comprise a mixture of K Gaussians each of which has a mean that is linear in  $z_n$ . Then even if  $\hat{\alpha}(z_1)$  is Gaussian, the quantity  $\hat{\alpha}(z_2)$  will be a mixture of K Gaussians,  $\hat{\alpha}(z_3)$  will be a mixture of  $K^2$ Gaussians, and so on, and exact inference will not be of practical value.

We have seen that the hidden Markov model can be viewed as an extension of the mixture models of Chapter 9 to allow for sequential correlations in the data. In a similar way, we can view the linear dynamical system as a generalization of the continuous latent variable models of Chapter 12 such as probabilistic PCA and factor analysis. Each pair of nodes  $\{z_n, x_n\}$  represents a linear-Gaussian latent variable

model for that particular observation. However, the latent variables  $\{z_n\}$  are no longer treated as independent but now form a Markov chain.

Because the model is represented by a tree-structured directed graph, inference problems can be solved efficiently using the sum-product algorithm. The forward recursions, analogous to the  $\alpha$  messages of the hidden Markov model, are known as the *Kalman filter* equations (Kalman, 1960; Zarchan and Musoff, 2005), and the backward recursions, analogous to the β messages, are known as the *Kalman smoother* equations, or the *Rauch-Tung-Striebel* (RTS) equations (Rauch *et al.*, 1965). The Kalman filter is widely used in many real-time tracking applications.

Because the linear dynamical system is a linear-Gaussian model, the joint distribution over all variables, as well as all marginals and conditionals, will be Gaussian. It follows that the sequence of individually most probable latent variable values is *Exercise 13.19* the same as the most probable latent sequence. There is thus no need to consider the analogue of the Viterbi algorithm for the linear dynamical system.

> Because the model has linear-Gaussian conditional distributions, we can write the transition and emission distributions in the general form

$$
p(\mathbf{z}_n|\mathbf{z}_{n-1}) = \mathcal{N}(\mathbf{z}_n|\mathbf{A}\mathbf{z}_{n-1}, \mathbf{\Gamma}) \tag{13.75}
$$

$$
p(\mathbf{x}_n|\mathbf{z}_n) = \mathcal{N}(\mathbf{x}_n|\mathbf{C}\mathbf{z}_n, \boldsymbol{\Sigma}). \tag{13.76}
$$

The initial latent variable also has a Gaussian distribution which we write as

$$
p(\mathbf{z}_1) = \mathcal{N}(\mathbf{z}_1 | \boldsymbol{\mu}_0, \mathbf{V}_0). \tag{13.77}
$$

Note that in order to simplify the notation, we have omitted additive constant terms from the means of the Gaussians. In fact, it is straightforward to include them if *Exercise 13.24* desired. Traditionally, these distributions are more commonly expressed in an equivalent form in terms of noisy linear equations given by

$$
\mathbf{z}_n = \mathbf{A}\mathbf{z}_{n-1} + \mathbf{w}_n \tag{13.78}
$$

$$
\mathbf{x}_n = \mathbf{Cz}_n + \mathbf{v}_n \tag{13.79}
$$

$$
\mathbf{z}_1 = \boldsymbol{\mu}_0 + \mathbf{u} \tag{13.80}
$$

where the noise terms have the distributions

$$
\mathbf{w} \sim \mathcal{N}(\mathbf{w}|\mathbf{0}, \mathbf{\Gamma}) \tag{13.81}
$$

**v** ∼ N(**v**|**0**, **Σ**) (13.82) (13.82)

$$
\mathbf{u} \sim \mathcal{N}(\mathbf{u}|\mathbf{0}, \mathbf{V}_0). \tag{13.83}
$$

The parameters of the model, denoted by  $\theta = {\mathbf{A}, \mathbf{\Gamma}, \mathbf{C}, \mathbf{\Sigma}, \mu_0, \mathbf{V}_0}$ , can be determined using maximum likelihood through the EM algorithm. In the E step, we need to solve the inference problem of determining the local posterior marginals for the latent variables, which can be solved efficiently using the sum-product algorithm, as we discuss in the next section.

*Exercise 13.19*

## 13.3.1 Inference in LDS

We now turn to the problem of finding the marginal distributions for the latent variables conditional on the observation sequence. For given parameter settings, we also wish to make predictions of the next latent state  $z_n$  and of the next observation **x**<sub>n</sub> conditioned on the observed data  $\mathbf{x}_1, \ldots, \mathbf{x}_{n-1}$  for use in real-time applications. These inference problems can be solved efficiently using the sum-product algorithm, which in the context of the linear dynamical system gives rise to the Kalman filter and Kalman smoother equations.

It is worth emphasizing that because the linear dynamical system is a linear-Gaussian model, the joint distribution over all latent and observed variables is simply a Gaussian, and so in principle we could solve inference problems by using the standard results derived in previous chapters for the marginals and conditionals of a multivariate Gaussian. The role of the sum-product algorithm is to provide a more efficient way to perform such computations.

Linear dynamical systems have the identical factorization, given by (13.6), to hidden Markov models, and are again described by the factor graphs in Figures 13.14 and 13.15. Inference algorithms therefore take precisely the same form except that summations over latent variables are replaced by integrations. We begin by considering the forward equations in which we treat  $\mathbf{z}_N$  as the root node, and propagate messages from the leaf node  $h(\mathbf{z}_1)$  to the root. From (13.77), the initial message will be Gaussian, and because each of the factors is Gaussian, all subsequent messages will also be Gaussian. By convention, we shall propagate messages that are normalized marginal distributions corresponding to  $p(\mathbf{z}_n|\mathbf{x}_1,\ldots,\mathbf{x}_n)$ , which we denote by

$$
\widehat{\alpha}(\mathbf{z}_n) = \mathcal{N}(\mathbf{z}_n | \boldsymbol{\mu}_n, \mathbf{V}_n).
$$
\n(13.84)

This is precisely analogous to the propagation of scaled variables  $\hat{\alpha}(\mathbf{z}_n)$  given by (13.59) in the discrete case of the hidden Markov model, and so the recursion equation now takes the form

$$
c_n \widehat{\alpha}(\mathbf{z}_n) = p(\mathbf{x}_n | \mathbf{z}_n) \int \widehat{\alpha}(\mathbf{z}_{n-1}) p(\mathbf{z}_n | \mathbf{z}_{n-1}) \, d\mathbf{z}_{n-1}.
$$
 (13.85)

Substituting for the conditionals  $p(\mathbf{z}_n|\mathbf{z}_{n-1})$  and  $p(\mathbf{x}_n|\mathbf{z}_n)$ , using (13.75) and (13.76), respectively, and making use of (13.84), we see that (13.85) becomes

$$
c_n 
\mathcal{N}(\mathbf{z}_n | \boldsymbol{\mu}_n, \mathbf{V}_n) = \mathcal{N}(\mathbf{x}_n | \mathbf{C} \mathbf{z}_n, \boldsymbol{\Sigma})
$$

$$
\int \mathcal{N}(\mathbf{z}_n | \mathbf{A} \mathbf{z}_{n-1}, \boldsymbol{\Gamma}) \mathcal{N}(\mathbf{z}_{n-1} | \boldsymbol{\mu}_{n-1}, \mathbf{V}_{n-1}) \, d\mathbf{z}_{n-1}. (13.86)
$$

Here we are supposing that  $\mu_{n-1}$  and  $V_{n-1}$  are known, and by evaluating the integral in (13.86), we wish to determine values for  $\mu_n$  and  $V_n$ . The integral is easily evaluated by making use of the result (2.115), from which it follows that

$$
\int \mathcal{N}(\mathbf{z}_n | \mathbf{A} \mathbf{z}_{n-1}, \mathbf{\Gamma}) \mathcal{N}(\mathbf{z}_{n-1} | \boldsymbol{\mu}_{n-1}, \mathbf{V}_{n-1}) \, d\mathbf{z}_{n-1}
$$

$$
= \mathcal{N}(\mathbf{z}_n | \mathbf{A} \boldsymbol{\mu}_{n-1}, \mathbf{P}_{n-1})
$$
(13.87)

#### 13.3. Linear Dynamical Systems

where we have defined

$$
\mathbf{P}_{n-1} = \mathbf{A}\mathbf{V}_{n-1}\mathbf{A}^{\mathrm{T}} + \mathbf{\Gamma}.
$$
 (13.88)

We can now combine this result with the first factor on the right-hand side of  $(13.86)$ by making use of  $(2.115)$  and  $(2.116)$  to give

$$
\mu_n = \mathbf{A}\mu_{n-1} + \mathbf{K}_n(\mathbf{x}_n - \mathbf{C}\mathbf{A}\mu_{n-1})
$$
(13.89)

$$
\mathbf{V}_n = (\mathbf{I} - \mathbf{K}_n \mathbf{C}) \mathbf{P}_{n-1}
$$
(13.90)  
$$
\mathbf{A}^T = \mathbf{G} \mathbf{A} \mathbf{G} \mathbf{D} \mathbf{G} \mathbf{A} \mathbf{G} \mathbf{D} \mathbf{G} \mathbf{A} \mathbf{G} \mathbf{D}
$$
(13.91)

$$
c_n = \mathcal{N}(\mathbf{x}_n | \mathbf{CA}\boldsymbol{\mu}_{n-1}, \mathbf{CP}_{n-1}\mathbf{C}^{\mathrm{T}} + \boldsymbol{\Sigma}). \tag{13.91}
$$

Here we have made use of the matrix inverse identities  $(C.5)$  and  $(C.7)$  and also defined the *Kalman gain matrix*

$$
\mathbf{K}_n = \mathbf{P}_{n-1} \mathbf{C}^{\mathrm{T}} \left( \mathbf{C} \mathbf{P}_{n-1} \mathbf{C}^{\mathrm{T}} + \boldsymbol{\Sigma} \right)^{-1} . \tag{13.92}
$$

Thus, given the values of  $\mu_{n-1}$  and  $V_{n-1}$ , together with the new observation  $x_n$ , we can evaluate the Gaussian marginal for  $z_n$  having mean  $\mu_n$  and covariance  $V_n$ , as well as the normalization coefficient  $c_n$ .

The initial conditions for these recursion equations are obtained from

$$
c_1\widehat{\alpha}(\mathbf{z}_1) = p(\mathbf{z}_1)p(\mathbf{x}_1|\mathbf{z}_1).
$$
 (13.93)

Because  $p(\mathbf{z}_1)$  is given by (13.77), and  $p(\mathbf{x}_1|\mathbf{z}_1)$  is given by (13.76), we can again make use of (2.115) to calculate  $c_1$  and (2.116) to calculate  $\mu_1$  and  $\mathbf{V}_1$  giving

$$
\mu_1 = \mu_0 + \mathbf{K}_1(\mathbf{x}_1 - \mathbf{C}\mu_0) \tag{13.94}
$$
\n
$$
\mathbf{V}_1 = (\mathbf{I} - \mathbf{K}_1 \mathbf{C}) \mathbf{V}_1 \tag{13.95}
$$

$$
\mathbf{V}_1 = (\mathbf{I} - \mathbf{K}_1 \mathbf{C}) \mathbf{V}_0 \tag{13.95}
$$
\n
$$
\mathbf{V}_1 = \mathbf{V}_1 (\mathbf{C} \mathbf{I} \mathbf{C} \mathbf{I} \mathbf{C} \mathbf{I} \mathbf{C} \mathbf{I} \mathbf{C} \mathbf{I} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf{A} \mathbf{C} \mathbf
$$

$$
c_1 = \mathcal{N}(\mathbf{x}_1 | \mathbf{C} \boldsymbol{\mu}_0, \mathbf{C} \mathbf{V}_0 \mathbf{C}^{\mathrm{T}} + \boldsymbol{\Sigma}) \tag{13.96}
$$

where

$$
\mathbf{K}_1 = \mathbf{V}_0 \mathbf{C}^{\mathrm{T}} \left( \mathbf{C} \mathbf{V}_0 \mathbf{C}^{\mathrm{T}} + \boldsymbol{\Sigma} \right)^{-1} . \tag{13.97}
$$

Similarly, the likelihood function for the linear dynamical system is given by (13.63) in which the factors  $c_n$  are found using the Kalman filtering equations.

We can interpret the steps involved in going from the posterior marginal over  $z_{n-1}$  to the posterior marginal over  $z_n$  as follows. In (13.89), we can view the quantity  $\mathbf{A}\boldsymbol{\mu}_{n-1}$  as the prediction of the mean over  $\mathbf{z}_n$  obtained by simply taking the mean over  $z_{n-1}$  and projecting it forward one step using the transition probability matrix **A**. This predicted mean would give a predicted observation for  $x_n$  given by **CAz**<sup>n</sup>−<sup>1</sup> obtained by applying the emission probability matrix **<sup>C</sup>** to the predicted hidden state mean. We can view the update equation (13.89) for the mean of the hidden variable distribution as taking the predicted mean  $A\mu_{n-1}$  and then adding a correction that is proportional to the error  $\mathbf{x}_n - \mathbf{C} \mathbf{A} \mathbf{z}_{n-1}$  between the predicted observation and the actual observation. The coefficient of this correction is given by the Kalman gain matrix. Thus we can view the Kalman filter as a process of making successive predictions and then correcting these predictions in the light of the new observations. This is illustrated graphically in Figure 13.21.

Image /page/12/Figure/1 description: The image displays three plots, each showing probability distributions. The first plot, labeled "zn-1", shows a single blue bell curve centered around a peak. The second plot, labeled "zn", shows two curves: a dashed blue bell curve and a solid red curve that is wider and shorter than the blue curve, with its peak shifted to the right. The third plot, also labeled "zn", displays three curves: a solid blue bell curve, a solid green curve that is shorter and wider than the blue curve, and a dashed red curve that is even wider and shorter than the green curve, with its peak shifted further to the right than the green curve. All curves are plotted against a horizontal axis labeled with "zn" or "zn-1".

**Figure 13.21** The linear dynamical system can be viewed as a sequence of steps in which increasing uncertainty in the state variable due to diffusion is compensated by the arrival of new data. In the left-hand plot, the blue curve shows the distribution  $p(\mathbf{z}_{n-1}|\mathbf{x}_1,\ldots,\mathbf{x}_{n-1})$ , which incorporates all the data up to step  $n-1$ . The diffusion arising from the nonzero variance of the transition probability  $p(z_n|z_{n-1})$  gives the distribution p(**z**n|**x**1,..., **x**<sup>n</sup>−<sup>1</sup>), shown in red in the centre plot. Note that this is broader and shifted relative to the blue curve (which is shown dashed in the centre plot for comparison). The next data observation  $x_n$  contributes through the emission density  $p(\mathbf{x}_n|\mathbf{z}_n)$ , which is shown as a function of  $\mathbf{z}_n$  in green on the right-hand plot. Note that this is not a density with respect to **z**<sup>n</sup> and so is not normalized to one. Inclusion of this new data point leads to a revised distribution  $p(\mathbf{z}_n|\mathbf{x}_1,\ldots,\mathbf{x}_n)$  for the state density shown in blue. We see that observation of the data has shifted and narrowed the distribution compared to p(**z**n|**x**1,..., **x**<sup>n</sup>−<sup>1</sup>) (which is shown in dashed in the right-hand plot for comparison).

*Exercise 13.27*

If we consider a situation in which the measurement noise is small compared to the rate at which the latent variable is evolving, then we find that the posterior *Exercise 13.27* distribution for  $z_n$  depends only on the current measurement  $x_n$ , in accordance with the intuition from our simple example at the start of the section. Similarly, if the latent variable is evolving slowly relative to the observation noise level, we find that the posterior mean for  $z_n$  is obtained by averaging all of the measurements obtained *Exercise* 13.28 up to that time.

> One of the most important applications of the Kalman filter is to tracking, and this is illustrated using a simple example of an object moving in two dimensions in Figure 13.22.

> So far, we have solved the inference problem of finding the posterior marginal for a node  $z_n$  given observations from  $x_1$  up to  $x_n$ . Next we turn to the problem of finding the marginal for a node  $z_n$  given all observations  $x_1$  to  $x_N$ . For temporal data, this corresponds to the inclusion of future as well as past observations. Although this cannot be used for real-time prediction, it plays a key role in learning the parameters of the model. By analogy with the hidden Markov model, this problem can be solved by propagating messages from node  $\mathbf{x}_N$  back to node  $\mathbf{x}_1$  and combining this information with that obtained during the forward message passing stage used to compute the  $\widehat{\alpha}(\mathbf{z}_n)$ .

> In the LDS literature, it is usual to formulate this backward recursion in terms of  $\gamma(\mathbf{z}_n) = \hat{\alpha}(\mathbf{z}_n)\hat{\beta}(\mathbf{z}_n)$  rather than in terms of  $\hat{\beta}(\mathbf{z}_n)$ . Because  $\gamma(\mathbf{z}_n)$  must also be Gaussian, we write it in the form

$$
\gamma(\mathbf{z}_n) = \widehat{\alpha}(\mathbf{z}_n)\widehat{\beta}(\mathbf{z}_n) = \mathcal{N}(\mathbf{z}_n|\widehat{\boldsymbol{\mu}}_n, \widehat{\mathbf{V}}_n).
$$
 (13.98)

To derive the required recursion, we start from the backward recursion (13.62) for

**Figure 13.22** An illustration of a linear dynamical system being used to track a moving object. The blue points indicate the true positions of the object in a two-dimensional space at successive time steps, the green points denote noisy measurements of the positions, and the red crosses indicate the means of the inferred posterior distributions of the positions obtained by running the Kalman filtering equations. The covariances of the inferred positions are indicated by the red ellipses, which correspond to contours having one standard deviation.

Image /page/13/Picture/2 description: A scatter plot shows three dashed lines in blue, green, and red, connecting several points. Each point is marked with a colored circle (blue, green, or red) and a red 'x' symbol. Around some of the points, there are large red circles, indicating some form of uncertainty or region of interest. The plot has a white background with a grid of faint black lines and tick marks on the x and y axes.

 $\beta(\mathbf{z}_n)$ , which, for continuous latent variables, can be written in the form

$$
c_{n+1}\widehat{\beta}(\mathbf{z}_n) = \int \widehat{\beta}(\mathbf{z}_{n+1}) p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1}) p(\mathbf{z}_{n+1}|\mathbf{z}_n) d\mathbf{z}_{n+1}.
$$
 (13.99)

We now multiply both sides of (13.99) by  $\hat{\alpha}(\mathbf{z}_n)$  and substitute for  $p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1})$ and  $p(\mathbf{z}_{n+1}|\mathbf{z}_n)$  using (13.75) and (13.76). Then we make use of (13.89), (13.90) *Exercise 13.29* and (13.91), together with (13.98), and after some manipulation we obtain

$$
\widehat{\boldsymbol{\mu}}_n = \boldsymbol{\mu}_n + \mathbf{J}_n \left( \widehat{\boldsymbol{\mu}}_{n+1} - \mathbf{A} \boldsymbol{\mu}_N \right) \tag{13.100}
$$

$$
\widehat{\mathbf{V}}_n = \mathbf{V}_n + \mathbf{J}_n \left( \widehat{\mathbf{V}}_{n+1} - \mathbf{P}_n \right) \mathbf{J}_n^{\mathrm{T}}
$$
\n(13.101)

where we have defined

$$
\mathbf{J}_n = \mathbf{V}_n \mathbf{A}^{\mathrm{T}} \left( \mathbf{P}_n \right)^{-1} \tag{13.102}
$$

and we have made use of  $AV_n = P_n J_n^T$ . Note that these recursions require that the forward pass be completed first so that the quantities  $\mu$  and  $V$  will be available forward pass be completed first so that the quantities  $\mu_n$  and  $V_n$  will be available for the backward pass.

For the EM algorithm, we also require the pairwise posterior marginals, which can be obtained from (13.65) in the form

$$
\xi(\mathbf{z}_{n-1}, \mathbf{z}_n) = (c_n)^{-1} \widehat{\alpha}(\mathbf{z}_{n-1}) p(\mathbf{x}_n | \mathbf{z}_n) p(\mathbf{z}_n | \mathbf{z}_{n-1}) \widehat{\beta}(\mathbf{z}_n)
$$
  
= 
$$
\frac{\mathcal{N}(\mathbf{z}_{n-1} | \boldsymbol{\mu}_{n-1}, \mathbf{V}_{n-1}) \mathcal{N}(\mathbf{z}_n | \mathbf{A} \mathbf{z}_{n-1}, \boldsymbol{\Gamma}) \mathcal{N}(\mathbf{x}_n | \mathbf{C} \mathbf{z}_n, \boldsymbol{\Sigma}) \mathcal{N}(\mathbf{z}_n | \widehat{\boldsymbol{\mu}}_n, \widehat{\mathbf{V}}_n)}{c_n \widehat{\alpha}(\mathbf{z}_n)}.
$$
(13.103)

Substituting for  $\widehat{\alpha}(\mathbf{z}_n)$  using (13.84) and rearranging, we see that  $\xi(\mathbf{z}_{n-1}, \mathbf{z}_n)$  is a Gaussian with mean given with components  $\gamma(\mathbf{z}_{n-1})$  and  $\gamma(\mathbf{z}_n)$ , and a covariance *Exercise 13.31* between  $z_n$  and  $z_{n-1}$  given by

$$
cov[\mathbf{z}_n, \mathbf{z}_{n-1}] = \mathbf{J}_{n-1} \hat{\mathbf{V}}_n.
$$
 (13.104)

*Exercise 13.29*

## 13.3.2 Learning in LDS

So far, we have considered the inference problem for linear dynamical systems, assuming that the model parameters  $\theta = {\mathbf{A}, \mathbf{\Gamma}, \mathbf{C}, \mathbf{\Sigma}, \mu_0, \mathbf{V}_0}$  are known. Next, we consider the determination of these parameters using maximum likelihood (Ghahramani and Hinton, 1996b). Because the model has latent variables, this can be addressed using the EM algorithm, which was discussed in general terms in Chapter 9.

We can derive the EM algorithm for the linear dynamical system as follows. Let us denote the estimated parameter values at some particular cycle of the algorithm by  $\theta$ <sup>old</sup>. For these parameter values, we can run the inference algorithm to determine the posterior distribution of the latent variables  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$ , or more precisely those local posterior marginals that are required in the M step. In particular, we shall require the following expectations

$$
\mathbb{E}\left[\mathbf{z}_n\right] = \hat{\boldsymbol{\mu}}_n \tag{13.105}
$$

$$
\mathbb{E}\left[\mathbf{z}_n \mathbf{z}_{n-1}^{\mathrm{T}}\right] = \mathbf{J}_{n-1} \widehat{\mathbf{V}}_n + \widehat{\boldsymbol{\mu}}_n \widehat{\boldsymbol{\mu}}_{n-1}^{\mathrm{T}} \tag{13.106}
$$

$$
\mathbb{E}\left[\mathbf{z}_n\mathbf{z}_n^{\mathrm{T}}\right] = \widehat{\mathbf{V}}_n + \widehat{\boldsymbol{\mu}}_n \widehat{\boldsymbol{\mu}}_n^{\mathrm{T}} \tag{13.107}
$$

where we have used (13.104).

Now we consider the complete-data log likelihood function, which is obtained by taking the logarithm of (13.6) and is therefore given by

$$
\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\theta}) = \ln p(\mathbf{z}_1 | \boldsymbol{\mu}_0, \mathbf{V}_0) + \sum_{n=2}^{N} \ln p(\mathbf{z}_n | \mathbf{z}_{n-1}, \mathbf{A}, \boldsymbol{\Gamma}) + \sum_{n=1}^{N} \ln p(\mathbf{x}_n | \mathbf{z}_n, \mathbf{C}, \boldsymbol{\Sigma})
$$
(13.108)

in which we have made the dependence on the parameters explicit. We now take the expectation of the complete-data log likelihood with respect to the posterior distribution  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$  which defines the function

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{\text{old}}) = \mathbb{E}_{\mathbf{Z}|\boldsymbol{\theta}^{\text{old}}} [\ln p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})]. \qquad (13.109)
$$

In the M step, this function is maximized with respect to the components of  $\theta$ .

Consider first the parameters  $\mu_0$  and  $\mathbf{V}_0$ . If we substitute for  $p(\mathbf{z}_1|\mu_0, \mathbf{V}_0)$  in (13.108) using (13.77), and then take the expectation with respect to **Z**, we obtain

$$
Q(\boldsymbol{\theta},\boldsymbol{\theta}^{\text{old}})=-\frac{1}{2}\ln|\mathbf{V}_0|-\mathbb{E}_{\mathbf{Z}|\boldsymbol{\theta}^{\text{old}}}\left[\frac{1}{2}(\mathbf{z}_1-\boldsymbol{\mu}_0)^{\text{T}}\mathbf{V}_0^{-1}(\mathbf{z}_1-\boldsymbol{\mu}_0)\right]+\text{const}
$$

where all terms not dependent on  $\mu_0$  or  $V_0$  have been absorbed into the additive constant. Maximization with respect to  $\mu_0$  and  $V_0$  is easily performed by making use of the maximum likelihood solution for a Gaussian distribution discussed in *Exercise 13.32* Section 2.3.4, giving

*Exercise 13.32*

#### 13.3. Linear Dynamical Systems

$$
\mu_0^{\text{new}} = \mathbb{E}[\mathbf{z}_1] \tag{13.110}
$$
\n
$$
\mathbf{W}^{\text{new}} = \mathbb{E}[\mathbf{z}_1] \quad \mathbb{E}[\mathbf{z}_1] \tag{13.111}
$$

$$
\mathbf{V}_0^{\text{new}} = \mathbb{E}[\mathbf{z}_1 \mathbf{z}_1^{\text{T}}] - \mathbb{E}[\mathbf{z}_1] \mathbb{E}[\mathbf{z}_1^{\text{T}}]. \tag{13.111}
$$

Similarly, to optimize **A** and **Γ**, we substitute for  $p(\mathbf{z}_n|\mathbf{z}_{n-1}, \mathbf{A}, \mathbf{\Gamma})$  in (13.108) using (13.75) giving

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{\text{old}}) = -\frac{N-1}{2} \ln |\boldsymbol{\Gamma}|
$$

$$
-\mathbb{E}_{\mathbf{Z}|\boldsymbol{\theta}^{\text{old}}}\left[\frac{1}{2}\sum_{n=2}^{N}(\mathbf{z}_n - \mathbf{A}\mathbf{z}_{n-1})^{\text{T}}\boldsymbol{\Gamma}^{-1}(\mathbf{z}_n - \mathbf{A}\mathbf{z}_{n-1})\right] + \text{const} \quad (13.112)
$$

in which the constant comprises terms that are independent of **A** and **Γ**. Maximizing *Exercise 13.33* with respect to these parameters then gives

$$
\mathbf{A}^{\text{new}} = \left( \sum_{n=2}^{N} \mathbb{E} \left[ \mathbf{z}_{n} \mathbf{z}_{n-1}^{\text{T}} \right] \right) \left( \sum_{n=2}^{N} \mathbb{E} \left[ \mathbf{z}_{n-1} \mathbf{z}_{n-1}^{\text{T}} \right] \right)^{-1}
$$
(13.113)  
$$
\mathbf{\Gamma}^{\text{new}} = \frac{1}{N-1} \sum_{n=2}^{N} \left\{ \mathbb{E} \left[ \mathbf{z}_{n} \mathbf{z}_{n}^{\text{T}} \right] - \mathbf{A}^{\text{new}} \mathbb{E} \left[ \mathbf{z}_{n-1} \mathbf{z}_{n}^{\text{T}} \right] - \mathbb{E} \left[ \mathbf{z}_{n} \mathbf{z}_{n-1}^{\text{T}} \right] \mathbf{A}^{\text{new}} + \mathbf{A}^{\text{new}} \mathbb{E} \left[ \mathbf{z}_{n-1} \mathbf{z}_{n-1}^{\text{T}} \right] \left( \mathbf{A}^{\text{new}} \right)^{\text{T}} \right\}. \quad (13.114)
$$

Note that  $A^{new}$  must be evaluated first, and the result can then be used to determine **Γ**new.

Finally, in order to determine the new values of  $C$  and  $\Sigma$ , we substitute for  $p(\mathbf{x}_n|\mathbf{z}_n, \mathbf{C}, \mathbf{\Sigma})$  in (13.108) using (13.76) giving

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{\text{old}}) = -\frac{N}{2} \ln |\boldsymbol{\Sigma}|
$$
  
 
$$
-\mathbb{E}_{\mathbf{Z}|\boldsymbol{\theta}^{\text{old}}}\left[\frac{1}{2}\sum_{n=1}^{N}(\mathbf{x}_n - \mathbf{C}\mathbf{z}_n)^{\text{T}}\boldsymbol{\Sigma}^{-1}(\mathbf{x}_n - \mathbf{C}\mathbf{z}_n)\right] + \text{const.}
$$

*Exercise 13.34* Maximizing with respect to **C** and **Σ** then gives

$$
\mathbf{C}^{\text{new}} = \left(\sum_{n=1}^{N} \mathbf{x}_n \mathbb{E}\left[\mathbf{z}_n^{\text{T}}\right]\right) \left(\sum_{n=1}^{N} \mathbb{E}\left[\mathbf{z}_n \mathbf{z}_n^{\text{T}}\right]\right)^{-1} \tag{13.115}
$$

$$
\Sigma^{\text{new}} = \frac{1}{N} \sum_{n=1}^{N} \left\{ \mathbf{x}_{n} \mathbf{x}_{n}^{\text{T}} - \mathbf{C}^{\text{new}} \mathbb{E} \left[ \mathbf{z}_{n} \right] \mathbf{x}_{n}^{\text{T}} - \mathbf{x}_{n} \mathbb{E} \left[ \mathbf{z}_{n}^{\text{T}} \right] \mathbf{C}^{\text{new}} + \mathbf{C}^{\text{new}} \mathbb{E} \left[ \mathbf{z}_{n} \mathbf{z}_{n}^{\text{T}} \right] \mathbf{C}^{\text{new}} \right\}. \qquad (13.116)
$$

*Exercise 13.33*

We have approached parameter learning in the linear dynamical system using maximum likelihood. Inclusion of priors to give a MAP estimate is straightforward, and a fully Bayesian treatment can be found by applying the analytical approximation techniques discussed in Chapter 10, though a detailed treatment is precluded here due to lack of space.

### 13.3.3 Extensions of LDS

As with the hidden Markov model, there is considerable interest in extending the basic linear dynamical system in order to increase its capabilities. Although the assumption of a linear-Gaussian model leads to efficient algorithms for inference and learning, it also implies that the marginal distribution of the observed variables is simply a Gaussian, which represents a significant limitation. One simple extension of the linear dynamical system is to use a Gaussian mixture as the initial distribution for  $z_1$ . If this mixture has K components, then the forward recursion equations (13.85) will lead to a mixture of K Gaussians over each hidden variable  $z_n$ , and so the model is again tractable.

For many applications, the Gaussian emission density is a poor approximation. If instead we try to use a mixture of  $K$  Gaussians as the emission density, then the posterior  $\hat{\alpha}(\mathbf{z}_1)$  will also be a mixture of K Gaussians. However, from (13.85) the posterior  $\hat{\alpha}(\mathbf{z}_2)$  will comprise a mixture of  $K^2$  Gaussians, and so on, with  $\hat{\alpha}(\mathbf{z}_n)$ being given by a mixture of  $K<sup>n</sup>$  Gaussians. Thus the number of components grows exponentially with the length of the chain, and so this model is impractical.

More generally, introducing transition or emission models that depart from the linear-Gaussian (or other exponential family) model leads to an intractable inference problem. We can make deterministic approximations such as assumed den-*Chapter 10* sity filtering or expectation propagation, or we can make use of sampling methods, as discussed in Section 13.3.4. One widely used approach is to make a Gaussian approximation by linearizing around the mean of the predicted distribution, which gives rise to the *extended Kalman filter* (Zarchan and Musoff, 2005).

> As with hidden Markov models, we can develop interesting extensions of the basic linear dynamical system by expanding its graphical representation. For example, the *switching state space model* (Ghahramani and Hinton, 1998) can be viewed as a combination of the hidden Markov model with a set of linear dynamical systems. The model has multiple Markov chains of continuous linear-Gaussian latent variables, each of which is analogous to the latent chain of the linear dynamical system discussed earlier, together with a Markov chain of discrete variables of the form used in a hidden Markov model. The output at each time step is determined by stochastically choosing one of the continuous latent chains, using the state of the discrete latent variable as a switch, and then emitting an observation from the corresponding conditional output distribution. Exact inference in this model is intractable, but variational methods lead to an efficient inference scheme involving forward-backward recursions along each of the continuous and discrete Markov chains independently. Note that, if we consider multiple chains of discrete latent variables, and use one as the switch to select from the remainder, we obtain an analogous model having only discrete latent variables known as the *switching hidden Markov model*.

# Chapter 10

### 13.3.4 Particle filters

For dynamical systems which do not have a linear-Gaussian, for example, if *Chapter 11* they use a non-Gaussian emission density, we can turn to sampling methods in order to find a tractable inference algorithm. In particular, we can apply the samplingimportance-resampling formalism of Section 11.1.5 to obtain a sequential Monte Carlo algorithm known as the particle filter.

> Consider the class of distributions represented by the graphical model in Figure 13.5, and suppose we are given the observed values  $X_n = (x_1, \ldots, x_n)$  and we wish to draw L samples from the posterior distribution  $p(\mathbf{z}_n|\mathbf{X}_n)$ . Using Bayes' theorem, we have

$$
\mathbb{E}[f(\mathbf{z}_n)] = \int f(\mathbf{z}_n) p(\mathbf{z}_n | \mathbf{X}_n) d\mathbf{z}_n
$$
  
$$
= \int f(\mathbf{z}_n) p(\mathbf{z}_n | \mathbf{x}_n, \mathbf{X}_{n-1}) d\mathbf{z}_n
$$
  
$$
= \frac{\int f(\mathbf{z}_n) p(\mathbf{x}_n | \mathbf{z}_n) p(\mathbf{z}_n | \mathbf{X}_{n-1}) d\mathbf{z}_n}{\int p(\mathbf{x}_n | \mathbf{z}_n) p(\mathbf{z}_n | \mathbf{X}_{n-1}) d\mathbf{z}_n}
$$
  
$$
\approx \sum_{l=1}^{L} w_n^{(l)} f(\mathbf{z}_n^{(l)}) \qquad (13.117)
$$

where  ${\bf z}_n^{(l)}$  is a set of samples drawn from  $p({\bf z}_n|{\bf X}_{n-1})$  and we have made use of the conditional independence property  $p({\bf x} \mid {\bf z} \cdot {\bf X}_{n-1}) = p({\bf x} \mid {\bf z})$  which follows the conditional independence property  $p(\mathbf{x}_n|\mathbf{z}_n, \mathbf{X}_{n-1}) = p(\mathbf{x}_n|\mathbf{z}_n)$ , which follows from the graph in Figure 13.5. The sampling weights  $\{w_n^{(l)}\}$  are defined by

$$
w_n^{(l)} = \frac{p(\mathbf{x}_n | \mathbf{z}_n^{(l)})}{\sum_{m=1}^{L} p(\mathbf{x}_n | \mathbf{z}_n^{(m)})}
$$
(13.118)

where the same samples are used in the numerator as in the denominator. Thus the posterior distribution  $p(\mathbf{z}_n|\mathbf{x}_n)$  is represented by the set of samples  $\{\mathbf{z}_n^{(l)}\}$  together with the corresponding weights  $\{w_n^{(l)}\}$ . Note that these weights satisfy  $0 \leq w_n^{(l)}$ 1 and  $\sum_l w_n^{(l)} = 1$ .

Because we wish to find a sequential sampling scheme, we shall suppose that a set of samples and weights have been obtained at time step  $n$ , and that we have subsequently observed the value of  $x_{n+1}$ , and we wish to find the weights and samples at time step  $n + 1$ . We first sample from the distribution  $p(\mathbf{z}_{n+1}|\mathbf{X}_n)$ . This is

*Chapter 11*

straightforward since, again using Bayes' theorem

 $p(\mathbf{z}_{n+1}|\mathbf{X}_n) = \int p(\mathbf{z}_{n+1}|\mathbf{z}_n, \mathbf{X}_n) p(\mathbf{z}_n|\mathbf{X}_n) d\mathbf{z}_n$   
 $= \int p(\mathbf{z}_{n+1}|\mathbf{z}_n) p(\mathbf{z}_n|\mathbf{X}_n) d\mathbf{z}_n$   
 $= \int p(\mathbf{z}_{n+1}|\mathbf{z}_n) p(\mathbf{z}_n|\mathbf{X}_n, \mathbf{X}_{n-1}) d\mathbf{z}_n$   
 $= \frac{\int p(\mathbf{z}_{n+1}|\mathbf{z}_n) p(\mathbf{x}_n|\mathbf{z}_n) p(\mathbf{z}_n|\mathbf{X}_{n-1}) d\mathbf{z}_n}{\int p(\mathbf{x}_n|\mathbf{z}_n) p(\mathbf{z}_n|\mathbf{X}_{n-1}) d\mathbf{z}_n}$   
 $= \sum_l w_n^{(l)} p(\mathbf{z}_{n+1}|\mathbf{z}_n^{(l)}) \qquad (13.119)$ 

where we have made use of the conditional independence properties

$$
p(\mathbf{z}_{n+1}|\mathbf{z}_n, \mathbf{X}_n) = p(\mathbf{z}_{n+1}|\mathbf{z}_n)
$$
(13.120)  
$$
p(\mathbf{z}_n|\mathbf{z}_n) = p(\mathbf{z}_{n+1}|\mathbf{z}_n)
$$
(13.121)

$$
p(\mathbf{x}_n|\mathbf{z}_n, \mathbf{X}_{n-1}) = p(\mathbf{x}_n|\mathbf{z}_n)
$$
 (13.121)

which follow from the application of the d-separation criterion to the graph in Figure 13.5. The distribution given by (13.119) is a mixture distribution, and samples can be drawn by choosing a component  $l$  with probability given by the mixing coefficients  $w^{(l)}$  and then drawing a sample from the corresponding component.

In summary, we can view each step of the particle filter algorithm as comprising two stages. At time step  $n$ , we have a sample representation of the posterior distribution  $p(\mathbf{z}_n|\mathbf{X}_n)$  expressed as samples  $\{\mathbf{z}_n^{(l)}\}$  with corresponding weights  $\{w_n^{(l)}\}$ .<br>This can be viewed as a mixture representation of the form (13,119). To obtain the This can be viewed as a mixture representation of the form (13.119). To obtain the corresponding representation for the next time step, we first draw  $L$  samples from the mixture distribution (13.119), and then for each sample we use the new observation  $\mathbf{x}_{n+1}$  to evaluate the corresponding weights  $w_{n+1}^{(l)} \propto p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1}^{(l)})$ . This is illustrated for the case of a single variable z in Figure 13.23 illustrated, for the case of a single variable  $z$ , in Figure 13.23.

The particle filtering, or sequential Monte Carlo, approach has appeared in the literature under various names including the *bootstrap filter* (Gordon *et al.*, 1993), *survival of the fittest* (Kanazawa *et al.*, 1995), and the *condensation* algorithm (Isard and Blake, 1998).

# Exercises

 $\star$  **www** Use the technique of d-separation, discussed in Section 8.2, to verify that the Markov model shown in Figure 13.3 having  $N$  nodes in total satisfies the conditional independence properties (13.3) for  $n = 2, \ldots, N$ . Similarly, show that a model described by the graph in Figure 13.4 in which there are N nodes in total

Image /page/19/Figure/1 description: This is a diagram illustrating a probabilistic model. It shows four horizontal lines representing different probability distributions, labeled from top to bottom as p(zn|Xn), p(zn+1|Xn), p(xn+1|zn+1), and p(zn+1|Xn+1). The z-axis is indicated on the bottom right. Blue circles of varying sizes are placed along the top three lines, connected by blue arrows to circles on the lines below. The bottom line shows blue circles of varying sizes, with arrows pointing to them from the line above. A red curve, resembling a Gaussian distribution, is plotted between the second and third horizontal lines, intersecting the vertical lines connecting the circles.

**Figure 13.23** Schematic illustration of the operation of the particle filter for a one-dimensional latent space. At time step n, the posterior  $p(z_n|\mathbf{x}_n)$  is represented as a mixture distribution, shown schematically as circles whose sizes are proportional to the weights  $w_n^{(l)}$ . A set of  $L$  samples is then drawn from this distribution and the new weights  $w_{n+1}^{(l)}$  evaluated using  $p(\mathbf{x}_{n+1}|\mathbf{z}_{n+1}^{(l)})$ .

satisfies the conditional independence properties

$$
p(\mathbf{x}_n|\mathbf{x}_1,\ldots,\mathbf{x}_{n-1}) = p(\mathbf{x}_n|\mathbf{x}_{n-1},\mathbf{x}_{n-2})
$$
 (13.122)

for  $n = 3, \ldots, N$ .

**13.2**  $(\star \star)$  Consider the joint probability distribution (13.2) corresponding to the directed graph of Figure 13.3. Using the sum and product rules of probability, verify that this joint distribution satisfies the conditional independence property (13.3) for  $n =$  $2, \ldots, N$ . Similarly, show that the second-order Markov model described by the joint distribution (13.4) satisfies the conditional independence property

$$
p(\mathbf{x}_n|\mathbf{x}_1,\ldots,\mathbf{x}_{n-1})=p(\mathbf{x}_n|\mathbf{x}_{n-1},\mathbf{x}_{n-2})
$$
\n(13.123)

for  $n = 3, \ldots, N$ .

- **13.3** ( $\star$ ) By using d-separation, show that the distribution  $p(\mathbf{x}_1, \dots, \mathbf{x}_N)$  of the observed data for the state space model represented by the directed graph in Figure 13.5 does not satisfy any conditional independence properties and hence does not exhibit the Markov property at any finite order.
- **13.4**  $(\star \star)$  **www** Consider a hidden Markov model in which the emission densities are represented by a parametric model  $p(x|z, w)$ , such as a linear regression model or a neural network, in which **w** is a vector of adaptive parameters. Describe how the parameters **w** can be learned from data using maximum likelihood.

- **13.5**  $(\star \star)$  Verify the M-step equations (13.18) and (13.19) for the initial state probabilities and transition probability parameters of the hidden Markov model by maximization of the expected complete-data log likelihood function (13.17), using appropriate Lagrange multipliers to enforce the summation constraints on the components of  $\pi$ and **A**.
- **13.6** ( $\star$ ) Show that if any elements of the parameters  $\pi$  or **A** for a hidden Markov model are initially set to zero, then those elements will remain zero in all subsequent updates of the EM algorithm.
- **13.7 ( )** Consider a hidden Markov model with Gaussian emission densities. Show that maximization of the function  $Q(\theta, \theta^{\text{old}})$  with respect to the mean and covariance parameters of the Gaussians gives rise to the M-step equations (13.20) and (13.21).
- **13.8**  $(\star \star)$  **www** For a hidden Markov model having discrete observations governed by a multinomial distribution, show that the conditional distribution of the observations given the hidden variables is given by  $(13.22)$  and the corresponding M step equations are given by (13.23). Write down the analogous equations for the conditional distribution and the M step equations for the case of a hidden Markov with multiple binary output variables each of which is governed by a Bernoulli conditional distribution. Hint: refer to Sections 2.1 and 2.2 for a discussion of the corresponding maximum likelihood solutions for i.i.d. data if required.
- **13.9**  $(\star \star)$  **www** Use the d-separation criterion to verify that the conditional independence properties  $(13.24)$ – $(13.31)$  are satisfied by the joint distribution for the hidden Markov model defined by (13.6).
- **13.10**  $(\star \star \star)$  By applying the sum and product rules of probability, verify that the conditional independence properties (13.24)–(13.31) are satisfied by the joint distribution for the hidden Markov model defined by (13.6).
- **13.11**  $(\star \star)$  Starting from the expression (8.72) for the marginal distribution over the variables of a factor in a factor graph, together with the results for the messages in the sum-product algorithm obtained in Section 13.2.3, derive the result (13.43) for the joint posterior distribution over two successive latent variables in a hidden Markov model.
- **13.12**  $(\star \star)$  Suppose we wish to train a hidden Markov model by maximum likelihood using data that comprises  $R$  independent sequences of observations, which we denote by  $X^{(r)}$  where  $r = 1, ..., R$ . Show that in the E step of the EM algorithm, we simply evaluate posterior probabilities for the latent variables by running the  $\alpha$ and  $\beta$  recursions independently for each of the sequences. Also show that in the M step, the initial probability and transition probability parameters are re-estimated

using modified forms of (13.18 ) and (13.19) given by

 $\overline{D}$ 

$$
\pi_k = \frac{\sum_{r=1}^{R} \gamma(z_{1k}^{(r)})}{\sum_{r=1}^{R} \sum_{j=1}^{K} \gamma(z_{1j}^{(r)})}
$$
(13.124)  
$$
A_{jk} = \frac{\sum_{r=1}^{R} \sum_{n=2}^{N} \xi(z_{n-1,j}^{(r)}, z_{n,k}^{(r)})}{\sum_{r=1}^{R} \sum_{l=1}^{K} \sum_{n=2}^{N} \xi(z_{n-1,j}^{(r)}, z_{n,l}^{(r)})}
$$
(13.125)

where, for notational convenience, we have assumed that the sequences are of the same length (the generalization to sequences of different lengths is straightforward). Similarly, show that the M-step equation for re-estimation of the means of Gaussian emission models is given by

$$
\mu_{k} = \frac{\sum_{r=1}^{R} \sum_{n=1}^{N} \gamma(z_{nk}^{(r)}) \mathbf{x}_{n}^{(r)}}{\sum_{r=1}^{R} \sum_{n=1}^{N} \gamma(z_{nk}^{(r)})}
$$
(13.126)

Note that the M-step equations for other emission model parameters and distributions take an analogous form.

- **13.13**  $(\star \star)$  **www** Use the definition (8.64) of the messages passed from a factor node to a variable node in a factor graph, together with the expression (13.6) for the joint distribution in a hidden Markov model, to show that the definition (13.50) of the alpha message is the same as the definition (13.34).
- **13.14**  $(\star \star)$  Use the definition (8.67) of the messages passed from a factor node to a variable node in a factor graph, together with the expression (13.6) for the joint distribution in a hidden Markov model, to show that the definition (13.52) of the beta message is the same as the definition (13.35).
- **13.15**  $(\star \star)$  Use the expressions (13.33) and (13.43) for the marginals in a hidden Markov model to derive the corresponding results (13.64) and (13.65) expressed in terms of re-scaled variables.
- **13.16**  $(\star \star \star)$  In this exercise, we derive the forward message passing equation for the Viterbi algorithm directly from the expression (13.6) for the joint distribution. This involves maximizing over all of the hidden variables  $z_1, \ldots, z_N$ . By taking the logarithm and then exchanging maximizations and summations, derive the recursion

(13.68) where the quantities  $\omega(\mathbf{z}_n)$  are defined by (13.70). Show that the initial condition for this recursion is given by (13.69).

- **13.17** ( $\star$ ) **www** Show that the directed graph for the input-output hidden Markov model, given in Figure 13.18, can be expressed as a tree-structured factor graph of the form shown in Figure 13.15 and write down expressions for the initial factor  $h(\mathbf{z}_1)$  and for the general factor  $f_n(\mathbf{z}_{n-1}, \mathbf{z}_n)$  where  $2 \leq n \leq N$ .
- **13.18**  $(\star \star \star)$  Using the result of Exercise 13.17, derive the recursion equations, including the initial conditions, for the forward-backward algorithm for the input-output hidden Markov model shown in Figure 13.18.
- **13.19** ( $\star$ ) **www** The Kalman filter and smoother equations allow the posterior distributions over individual latent variables, conditioned on all of the observed variables, to be found efficiently for linear dynamical systems. Show that the sequence of latent variable values obtained by maximizing each of these posterior distributions individually is the same as the most probable sequence of latent values. To do this, simply note that the joint distribution of all latent and observed variables in a linear dynamical system is Gaussian, and hence all conditionals and marginals will also be Gaussian, and then make use of the result (2.98).
- **13.20**  $(\star \star)$  **www** Use the result (2.115) to prove (13.87).
- **13.21**  $(\star \star)$  Use the results (2.115) and (2.116), together with the matrix identities (C.5) and (C.7), to derive the results (13.89), (13.90), and (13.91), where the Kalman gain matrix  $\mathbf{K}_n$  is defined by (13.92).
- **13.22**  $(\star \star)$  **www** Using (13.93), together with the definitions (13.76) and (13.77) and the result  $(2.115)$ , derive  $(13.96)$ .
- **13.23**  $(\star \star)$  Using (13.93), together with the definitions (13.76) and (13.77) and the result (2.116), derive (13.94), (13.95) and (13.97).
- **13.24**  $(\star \star)$  **www** Consider a generalization of (13.75) and (13.76) in which we include constant terms **a** and **c** in the Gaussian means, so that

$$
p(\mathbf{z}_n|\mathbf{z}_{n-1}) = \mathcal{N}(\mathbf{z}_n|\mathbf{A}\mathbf{z}_{n-1} + \mathbf{a}, \mathbf{\Gamma})
$$
(13.127)

$$
p(\mathbf{x}_n|\mathbf{z}_n) = \mathcal{N}(\mathbf{x}_n|\mathbf{Cz}_n + \mathbf{c}, \Sigma).
$$
 (13.128)

Show that this extension can be re-case in the framework discussed in this chapter by defining a state vector **z** with an additional component fixed at unity, and then augmenting the matrices **A** and **C** using extra columns corresponding to the parameters **a** and **c**.

**13.25**  $(\star \star)$  In this exercise, we show that when the Kalman filter equations are applied to independent observations, they reduce to the results given in Section 2.3 for the maximum likelihood solution for a single Gaussian distribution. Consider the problem of finding the mean  $\mu$  of a single Gaussian random variable x, in which we are given a set of independent observations  $\{x_1, \ldots, x_N\}$ . To model this we can use

a linear dynamical system governed by (13.75) and (13.76), with latent variables  $\{z_1,\ldots,z_N\}$  in which **C** becomes the identity matrix and where the transition probability  $A = 0$  because the observations are independent. Let the parameters  $m_0$ and  $\dot{V}_0$  of the initial state be denoted by  $\mu_0$  and  $\sigma_0^2$ , respectively, and suppose that  $\Sigma$  becomes  $\sigma^2$ . Write down the corresponding Kalman filter equations starting from  $\Sigma$  becomes  $\sigma^2$ . Write down the corresponding Kalman filter equations starting from the general results (13.89) and (13.90), together with (13.94) and (13.95). Show that these are equivalent to the results  $(2.141)$  and  $(2.142)$  obtained directly by considering independent data.

- **13.26**  $(\star \star \star)$  Consider a special case of the linear dynamical system of Section 13.3 that is equivalent to probabilistic PCA, so that the transition matrix  $\mathbf{A} = \mathbf{0}$ , the covariance  $\Gamma = I$ , and the noise covariance  $\Sigma = \sigma^2 I$ . By making use of the matrix inversion identity (C.7) show that, if the emission density matrix **C** is denoted **W**, then the posterior distribution over the hidden states defined by (13.89) and (13.90) reduces to the result (12.42) for probabilistic PCA.
- **13.27** ( $\star$ ) **www** Consider a linear dynamical system of the form discussed in Section 13.3 in which the amplitude of the observation noise goes to zero, so that  $\Sigma = 0$ . Show that the posterior distribution for  $z_n$  has mean  $x_n$  and zero variance. This accords with our intuition that if there is no noise, we should just use the current observation  $x_n$  to estimate the state variable  $z_n$  and ignore all previous observations.
- **13.28**  $(\star \star \star)$  Consider a special case of the linear dynamical system of Section 13.3 in which the state variable  $z_n$  is constrained to be equal to the previous state variable, which corresponds to  $\mathbf{A} = \mathbf{I}$  and  $\mathbf{\Gamma} = \mathbf{0}$ . For simplicity, assume also that  $\mathbf{V}_0 \rightarrow \infty$ so that the initial conditions for **z** are unimportant, and the predictions are determined purely by the data. Use proof by induction to show that the posterior mean for state  $z_n$  is determined by the average of  $x_1, \ldots, x_n$ . This corresponds to the intuitive result that if the state variable is constant, our best estimate is obtained by averaging the observations.
- **13.29**  $(\star \star \star)$  Starting from the backwards recursion equation (13.99), derive the RTS smoothing equations (13.100) and (13.101) for the Gaussian linear dynamical system.
- **13.30**  $(\star \star)$  Starting from the result (13.65) for the pairwise posterior marginal in a state space model, derive the specific form (13.103) for the case of the Gaussian linear dynamical system.
- **13.31** ( $\star\star$ ) Starting from the result (13.103) and by substituting for  $\hat{\alpha}(\mathbf{z}_n)$  using (13.84), verify the result (13.104) for the covariance between  $z_n$  and  $z_{n-1}$ .
- **13.32**  $(\star \star)$  **www** Verify the results (13.110) and (13.111) for the M-step equations for  $\mu_0$  and  $\mathbf{V}_0$  in the linear dynamical system.
- **13.33**  $(\star \star)$  Verify the results (13.113) and (13.114) for the M-step equations for **A** and **Γ** in the linear dynamical system.

**13.34**  $(\star \star)$  Verify the results (13.115) and (13.116) for the M-step equations for **C** and  $\Sigma$ in the linear dynamical system.