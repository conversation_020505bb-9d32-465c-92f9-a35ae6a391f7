$$
\frac{\ddot{\mathcal{D}}_{\perp}}{\mathcal{D}_{\perp}} \le -\frac{\text{Ric}(\dot{\gamma})}{n-1}.
$$
\n(14.22)

To summarize: The basic inequalities for  $\ell_{\perp}$  and  $\ell_{\parallel}$  are the same as for  $\ell$ , but with the exponent n replaced by  $n-1$  in the case of  $\ell_{\perp}$ , and 1 in the case of  $\ell_{\ell}$ ; and the number Ric( $\dot{\gamma}$ ) replaced by 0 in the case of  $\ell_{//}$ . The same for  $\mathcal{D}_{\perp}$  and  $\mathcal{D}_{//}$ .

## Positivity of the Jacobian

Unlike the distance function, the exponential map is always smooth. But this does not prevent the Jacobian determinant  $\mathcal{J}(t)$  from vanishing, i.e. the matrix  $J(t)$  from becoming singular (not invertible). Then computations such as (14.9) break down. So all the computations performed before are only valid if  $\mathcal{J}(t)$  is positive for all  $t \in (0,1)$ .

In terms of  $\ell(t) = -\log \mathcal{J}(t)$ , the vanishing of the Jacobian determinant corresponds to a divergence  $\ell(t) \to \infty$ . Readers familiar with ordinary differential equations will have no trouble believing that these events are not rare: Indeed,  $\ell$  solves the Ricatti-type equation (14.16), and such equations often lead to blow-up in finite time. For instance, consider a function  $\ell(t)$  that solves

$$
\ddot{\ell} \ge \frac{(\dot{\ell})^2}{n-1} + K,
$$

where  $K > 0$ . Consider a time  $t_0$  where  $\ell$  has a minimum, so  $\ell(t_0) = 0$ . Then,  $\ell$  cannot be defined on a time-interval larger than  $[t_0-T,t_0+T]$ , where  $T := \pi \sqrt{(n-1)/K}$ . So the Jacobian has to vanish at some time, and we even have a bound on this time. (With a bit more work, this estimate implies the Bonnet–Myers theorem, which asserts that the diameter of M cannot be larger than  $\pi \sqrt{(n-1)/K}$  if Ric  $\geq Kg$ .

The vanishing of the Jacobian may occur even along geodesics that are minimizing for all times: Consider for instance  $\xi(x) = -2x$  in  $\mathbb{R}^n$ ; then the image of  $\exp(t\xi)$  is reduced to a single point when  $t = 1/2$ . However, in the case of optimal transport, the Jacobian cannot vanish at intermediate times, at least for almost all initial points: Recall indeed the last part of Theorem 11.3. This property can be seen as a result of the very special choice of the velocity field  $\xi$ , which is the gradient of a  $d^2/2$ -convex function; or as a consequence of the "no-crossing" property explored in Chapter 8. (There is also an interpretation in terms of Jacobi fields, see Proposition 14.31 in the Third Appendix.)

## Bochner's formula

So far, we have discussed curvature from a *Lagrangian* point of view, that is, by going along a geodesic path  $\gamma(t)$ , keeping the memory of the initial position. It is useful to be also familiar with the Eulerian point of view, in which the focus is not on the trajectory, but on the velocity field  $\xi = \xi(t, x)$ . To switch from Lagrangian to Eulerian description, just write

$$
\dot{\gamma}(t) = \xi(t, \gamma(t)).\tag{14.23}
$$

In general, this can be a subtle issue because two trajectories might cross, and then there might be no way to define a meaningful velocity field  $\xi(t, \cdot)$  at the crossing point. However, if a smooth vector field  $\xi = \xi(0, \cdot)$  is given, then around any point  $x_0$  the trajectories  $\gamma(t,x) = \exp(t \xi(x))$  do not cross for |t| small enough, and one can define  $\xi(t,x)$  without ambiguity. The covariant differentiation of (14.23) along  $\xi$  itself, and the geodesic equation  $\ddot{\gamma} = 0$ , yield

$$
\frac{\partial \xi}{\partial t} + \nabla_{\xi} \xi = 0, \qquad (14.24)
$$

which is the **pressureless Euler equation**. From a physical point of view, this equation describes the velocity field of a bunch of particles which travel along geodesic curves without interacting. The derivation of (14.24) will fail when the geodesic paths start to cross, at which point the solution to (14.24) would typically lose smoothness and need reinterpretation. But for the sequel, we only need (14.24) to be satisfied for small values of  $|t|$ , and locally around x.

All the previous discussion about Ricci curvature can be recast in Eulerian terms. Let  $\gamma(t,x) = \exp_x(t\xi(x))$ ; by the definition of the covariant gradient, we have

$$
\mathbf{J}(t,x) = \nabla \xi(t,\gamma(t,x)) \mathbf{J}(t,x)
$$

(the same formula that we had before at time  $t = 0$ ). Under the identification of  $\mathbb{R}^n$  with  $T_{\gamma(t)}M$  provided by the basis  $\mathbf{E}(t)$ , we can identify **J** with the matrix  $J$ , and then

$$
U(t, x) = \dot{J}(t, x) J(t, x)^{-1} = \nabla \xi(t, \gamma(t, x)), \qquad (14.25)
$$

where again the linear operator  $\nabla \xi$  is identified with its matrix in the basis E.

Then tr  $U(t, x) = \text{tr }\nabla \xi(t, x)$  coincides with the divergence of  $\xi(t, \cdot)$ , evaluated at x. By the chain-rule and (14.24),

$$
\frac{d}{dt}(\text{tr } U)(t, x) = \frac{d}{dt}(\nabla \cdot \xi)(t, \gamma(t, x))
$$

$$
= \nabla \cdot \left(\frac{\partial \xi}{\partial t}(t, \gamma(t, x))\right) + \dot{\gamma}(t, x) \cdot \nabla(\nabla \cdot \xi)(t, \gamma(t, x))
$$

$$
= \left(-\nabla \cdot (\nabla_{\xi}\xi) + \xi \cdot \nabla(\nabla \cdot \xi)\right)(t, \gamma(t, x)).
$$

Thus the Lagrangian formula (14.12) can be translated into the Eulerian formula

$$
-\nabla \cdot (\nabla_{\xi} \xi) + \xi \cdot \nabla (\nabla \cdot \xi) + \text{tr} (\nabla \xi)^2 + \text{Ric}(\xi) = 0.
$$
 (14.26)

All functions here are evaluated at  $(t, \gamma(t,x))$ , and of course we can choose  $t = 0$ , and x arbitrary. So (14.26) is an identity that holds true for any smooth (say  $C^2$ ) vector field  $\xi$  on our manifold M. Of course it can also be established directly by a coordinate computation.<sup>1</sup>

While formula (14.26) holds true for all vector fields  $\xi$ , if  $\nabla \xi$  is symmetric then two simplifications arise:

(a)  $\nabla_{\xi} \xi = \nabla \xi \cdot \xi = \nabla \frac{|\xi|^2}{2}$  $\frac{1}{2}$ ;

(b) tr  $(\nabla \xi)^2 = ||\nabla \xi||_{\text{HS}}^2$ , HS standing for Hilbert–Schmidt norm.

So (14.26) becomes

$$
-\Delta \frac{|\xi|^2}{2} + \xi \cdot \nabla (\nabla \cdot \xi) + \|\nabla \xi\|_{\text{HS}}^2 + \text{Ric}(\xi) = 0. \tag{14.27}
$$

We shall apply it only in the case when  $\xi$  is a gradient:  $\xi = \nabla \psi$ ; then  $\nabla \xi = \nabla^2 \psi$  is indeed symmetric, and the resulting formula is

$$
-\Delta \frac{|\nabla \psi|^2}{2} + \nabla \psi \cdot \nabla (\Delta \psi) + \|\nabla^2 \psi\|_{\text{HS}}^2 + \text{Ric}(\nabla \psi) = 0. \quad (14.28)
$$

<sup>&</sup>lt;sup>1</sup> With the notation  $\nabla_{\xi} = \xi \cdot \nabla$  (which is classical in fluid mechanics), and tr  $(\nabla \xi)^2$  =  $\nabla \xi \cdot \nabla \xi$ , (14.26) takes the amusing form  $-\nabla \cdot \xi \cdot \nabla \xi + \xi \cdot \nabla \nabla \cdot \xi + \nabla \xi \cdot \nabla \xi + \text{Ric}(\xi) = 0$ .

The identity (14.26), or its particular case (14.28), is called the Bochner–Weitzenböck–Lichnerowicz formula, or just Bochner's formula.<sup>2</sup>

**Remark 14.5.** With the ansatz  $\xi = \nabla \psi$ , the pressureless Euler equation (14.24) reduces to the Hamilton–Jacobi equation

$$
\frac{\partial \psi}{\partial t} + \frac{|\nabla \psi|^2}{2} = 0.
$$
 (14.29)

One can use this equation to obtain (14.28) directly, instead of first deriving (14.26). Here equation (14.29) is to be understood in a viscosity sense (otherwise there are many spurious solutions); in fact the reader might just as well take the identity

$$
\psi(t,x) = \inf_{y \in M} \left[ \psi(y) + \frac{d(x,y)^2}{2t} \right]
$$

as the definition of the solution of (14.29). Then the geodesic curves  $\gamma$  starting with  $\gamma(0) = x$ ,  $\dot{\gamma}(0) = \nabla \psi(x)$  are called **characteristic** curves of equation (14.29).

Remark 14.6. Here I have not tried to derive Bochner's formula for nonsmooth functions. This could be done for semiconvex  $\psi$ , with an appropriate "compensated" definition for  $-\Delta \frac{|\nabla \psi|^2}{2} + \nabla \psi \cdot \nabla (\Delta \psi)$ . In fact, the semiconvexity of  $\nabla \psi$  prevents the formation of instantaneous shocks, and will allow the Lagrangian/Eulerian duality for a short time.

**Remark 14.7.** The operator  $U(t, x)$  coincides with  $\nabla^2 \psi(t, \gamma(t, x))$ , which is another way to see that it is symmetric for  $t > 0$ .

From this point on, we shall only work with (14.28). Of course, by using the Cauchy–Schwarz identity as before, we can bound below  $\|\nabla^2 \psi\|_{\text{HS}}^2$  by  $(\Delta \psi)^2/n$ ; therefore (14.25) implies

$$
\Delta + \nabla \nabla^* + \text{Ric} = 0,
$$

 $2$  In (14.26) or (14.28) I have written Bochner's formula in purely "metric" terms, which will probably look quite ugly to many geometer readers. An equivalent but more "topological" way to write Bochner's formula is

where  $\Delta = -(dd^* + d^*d)$  is the Laplace operator on 1-forms,  $\nabla$  is the covariant differentiation (under the identification of a 1-form with a vector field) and the adjoints are in  $L^2(\text{vol})$ . Also I should note that the name "Bochner formula" is attributed to a number of related identities.

$$
\Delta \frac{|\nabla \psi|^2}{2} - \nabla \psi \cdot \nabla (\Delta \psi) \ge \frac{(\Delta \psi)^2}{n} + \text{Ric}(\nabla \psi). \tag{14.30}
$$

Apart from regularity issues, this inequality is strictly equivalent to (14.13), and therefore to (14.14) or (14.15).

Not so much has been lost when going from  $(14.28)$  to  $(14.30)$ : there is still equality in (14.30) at all points x where  $\nabla^2 \psi(x)$  is a multiple of the identity.

One can also take out the direction of motion,  $\widehat{\nabla \psi} := (\nabla \psi)/|\nabla \psi|$ , from the Bochner identity. The Hamilton–Jacobi equation implies  $\partial_t \nabla \widetilde{\psi} + \nabla^2 \psi \cdot \nabla \widetilde{\psi} = 0$ , so

$$
\partial_t \left\langle \nabla^2 \psi \cdot \widehat{\nabla \psi}, \widehat{\nabla \psi} \right\rangle = - \left\langle \nabla^2 (|\nabla \psi|^2 / 2) \cdot \widehat{\nabla \psi}, \widehat{\nabla \psi} \right\rangle - 2 \left\langle \nabla^2 \psi \cdot (\nabla^2 \psi \cdot \widehat{\nabla \psi}), \widehat{\nabla \psi} \right\rangle,
$$

and by symmetry the latter term can be rewritten  $-2 |(\nabla^2 \psi) \cdot \nabla \tilde{\psi}|^2$ . From this one easily obtains the following refinement of Bochner's formula: Define

$$
\Delta_{//} f = \langle \nabla^2 f \cdot \widehat{\nabla \psi}, \widehat{\nabla \psi} \rangle, \qquad \Delta_{\perp} = \Delta - \Delta_{//},
$$

then

$$
\begin{cases}
\Delta_{//} \frac{|\nabla \psi|^2}{2} - \nabla \psi \cdot \nabla \Delta_{//} \psi + 2 |(\nabla^2 \psi) \cdot \widehat{\nabla \psi}|^2 \ge (\Delta_{//} \psi)^2 \\
\Delta_{\perp} \frac{|\nabla \psi|^2}{2} - \nabla \psi \cdot \nabla \Delta_{\perp} \psi - 2 |(\nabla^2 \psi) \cdot \widehat{\nabla \psi}|^2 \ge ||\nabla_{\perp}^2 \psi||_{\text{HS}}^2 + \text{Ric}(\nabla \psi)
\end{cases}
$$
(14.31)

This is the "Bochner formula with the direction of motion taken out". I have to confess that I never saw these frightening formulas anywhere, and don't know whether they have any use. But of course, they are equivalent to their Lagrangian counterpart, which will play a crucial role in the sequel.

## Analytic and geometric consequences of Ricci curvature bounds

Inequalities  $(14.13)$ ,  $(14.14)$ ,  $(14.15)$  and  $(14.30)$  are the "working" heart" of Ricci curvature analysis. Many gometric and analytic consequences follow from these estimates.

Here is a first example coming from analysis and partial differential equations theory: If the Ricci curvature of M is globally bounded below (inf<sub>x</sub> Ric<sub>x</sub> >  $-\infty$ ), then there exists a unique **heat kernel**, i.e. a measurable function  $p_t(x,y)$   $(t > 0, x \in M, y \in M)$ , integrable in y, smooth outside of the diagonal  $x = y$ , such that  $f(t,x) := \int p_t(x,y) f_0(y) dvol(y)$  solves the heat equation  $\partial_t f = \Delta f$ with initial datum  $f_0$ .

Here is another example in which some topological information can be recovered from Ricci bounds: If  $M$  is a manifold with nonnegative Ricci curvature (for each x, Ric<sub>x</sub>  $\geq$  0), and there exists a line in M, that is, a geodesic  $\gamma$  which is minimizing for all values of time  $t \in \mathbb{R}$ , then M is isometric to  $\mathbb{R} \times M'$ , for some Riemannian manifold  $M'$ . This is the splitting theorem, in a form proven by Cheeger and Gromoll.

Many quantitative statements can be obtained from (i) a lower bound on the Ricci curvature and (ii) an upper bound on the dimension of the manifold. Below is a (grossly nonexhaustive) list of some famous such results. In the statements to come,  $M$  is always assumed to be a smooth, complete Riemannian manifold, vol stands for the Riemannian volume on  $M$ ,  $\Delta$  for the Laplace operator and d for the Riemannian distance;  $K$  is the lower bound on the Ricci curvature, and  $n$  is the dimension of M. Also, if A is a measurable set, then  $A<sup>r</sup>$  will denote its r-neighborhood, which is the set of points that lie at a distance at most  $r$  from  $A$ . Finally, the "model space" is the simply connected Riemannian manifold with constant sectional curvature which has the same dimension as  $M$ , and Ricci curvature constantly equal to  $K$  (more rigorously, to  $Kg$ , where g is the metric tensor on the model space).

1. Volume growth estimates: The Bishop–Gromov inequality (also called Riemannian volume comparison theorem) states that the volume of balls does not increase faster than the volume of balls in the model space. In formulas: for any  $x \in M$ ,

$$
\frac{\text{vol}\left[B_r(x)\right]}{V(r)}
$$
 is a nonincreasing function of r,

where

$$
V(r) = \int_0^r S(r') dr',
$$

$$
S(r) = c_{n,K} \begin{cases} \sin^{n-1} \left( \sqrt{\frac{K}{n-1}} r \right) & \text{if } K > 0 \\ r^{n-1} & \text{if } K = 0 \\ \sinh^{n-1} \left( \sqrt{\frac{|K|}{n-1}} r \right) & \text{if } K < 0. \end{cases}
$$

Here of course  $S(r)$  is the surface area of  $B<sub>r</sub>(0)$  in the model space, that is the  $(n-1)$ -dimensional volume of  $\partial B_r(0)$ , and  $c_{n,K}$  is a nonessential normalizing constant. (See Theorem 18.8 later in this course.)

2. Diameter estimates: The Bonnet–Myers theorem states that, if  $K > 0$ , then M is compact and more precisely

$$
\text{diam}(M) \le \pi \sqrt{\frac{n-1}{K}},
$$

with equality for the model sphere.

**3.** Spectral gap inequalities: If  $K > 0$ , then the spectral gap  $\lambda_1$  of the nonnegative operator  $-\Delta$  is bounded below:

$$
\lambda_1 \ge \frac{n\,K}{n-1},
$$

with equality again for the model sphere. (See Theorem 21.20 later in this course.)

4. (Sharp) Sobolev inequalities: If  $K > 0$  and  $n \geq 3$ , let  $\mu =$ vol/vol[M] be the normalized volume measure on  $M$ ; then for any smooth function on M,

$$
||f||_{L^{2^{\star}}(\mu)}^2 \leq ||f||_{L^2(\mu)}^2 + \frac{4}{Kn(n-2)} ||\nabla f||_{L^2(\mu)}^2, \qquad 2^{\star} = \frac{2n}{n-2},
$$

and those constants are sharp for the model sphere.

5. Heat kernel bounds: There are many of them, in particular the well-known Li–Yau estimates: If  $K \geq 0$ , then the heat kernel  $p_t(x, y)$ satisfies

$$
p_t(x, y) \leq \frac{C}{\text{vol}[B_{\sqrt{t}}(x)]} \exp\left(-\frac{d(x, y)^2}{2Ct}\right),
$$

for some constant C which only depends on n. For  $K < 0$ , a similar bound holds true, only now  $C$  depends on  $K$  and there is an additional factor  $e^{Ct}$ . There are also pointwise estimates on the derivatives of  $\log p_t$ , in relation with **Harnack inequalities**.

The list could go on. More recently, Ricci curvature has been at the heart of Perelman's solution of the celebrated Poincaré conjecture, and more generally the topological classification of three-dimensional manifolds. Indeed, Perelman's argument is based on Hamilton's idea to use Ricci curvature in order to define a "heat flow" in the space of metrics, via the partial differential equation

$$
\frac{\partial g}{\partial t} = -2 \operatorname{Ric}(g),\tag{14.32}
$$

where  $\text{Ric}(q)$  is the Ricci tensor associated with the metric q, which can be thought of as something like  $-\Delta q$ . The flow defined by (14.32) is called the Ricci flow. Some time ago, Hamilton had already used its properties to show that a compact simply connected three-dimensional Riemannian manifold with positive Ricci curvature is automatically diffeomorphic to the sphere  $S^3$ .

### Change of reference measure and effective dimension

For various reasons, one is often led to consider a reference measure  $\nu$ that is not the volume measure vol, but, say,  $\nu(dx) = e^{-V(x)} \text{vol}(dx)$ , for some function  $V : M \to \mathbb{R}$ , which in this chapter will always be assumed to be of class  $C^2$ . The metric–measure space  $(M, d, \nu)$ , where d stands for the geodesic distance, may be of interest in its own right, or may appear as a limit of Riemannian manifolds, in a sense that will be studied in Part III of these notes.

Of course, such a change of reference measure affects Jacobian determinants; so Ricci curvature estimates will lose their geometric meaning unless one changes the definition of Ricci tensor to take the new reference measure into account. This might perturb the dependence of all estimates on the dimension, so it might also be a good idea to introduce an "effective dimension"  $N$ , which may be larger than the "true" dimension  $n$  of the manifold.

The most well-known example is certainly the Gaussian measure in  $\mathbb{R}^n$ , which I shall denote by  $\gamma^{(n)}$  (do not confuse it with a geodesic!):

$$
\gamma^{(n)}(dx) = \frac{e^{-|x|^2} dx}{(2\pi)^{n/2}}, \qquad x \in \mathbb{R}^n.
$$

It is a matter of experience that most theorems which we encounter about the Gaussian measure can be written just the same in dimension 1 or in dimension  $n$ , or even in infinite dimension, when properly interpreted. In fact, the effective dimension of  $(\mathbb{R}^n, \gamma^{(n)})$  is infinite, in a certain sense, whatever  $n$ . I admit that this perspective may look strange, and might be the result of lack of imagination; but in any case, it will fit very well into the picture (in terms of sharp constants for geometric inequalities, etc.).

So, again let

$$
T_t(x) = \gamma(t, x) = \exp_x(t\nabla\psi(x));
$$

now the Jacobian determinant is

$$
\mathcal{J}(t,x) = \lim_{r \downarrow 0} \frac{\nu \left[ T_t(B_r(x)) \right]}{\nu[B_r(x)]} = \frac{e^{-V(T_t(x))}}{e^{-V(x)}} \mathcal{J}_0(t,x),
$$

where  $\mathcal{J}_0$  is the Jacobian corresponding to  $V \equiv 0$  (that is, to  $\nu = \text{vol}$ ). Then (with dots still standing for derivation with respect to  $t$ ),

$$
(\log \mathcal{J})^{\cdot}(t, x) = (\log \mathcal{J}_0)^{\cdot}(t, x) - \dot{\gamma}(t, x) \cdot \nabla V(\gamma(t, x)),
$$
  
$$
(\log \mathcal{J})^{\cdot\cdot}(t, x) = (\log \mathcal{J}_0)^{\cdot\cdot}(t, x) - \left\langle \nabla^2 V(\gamma(t, x)) \cdot \dot{\gamma}(t, x), \dot{\gamma}(t, x) \right\rangle
$$

For later purposes it will be useful to keep track of all error terms in the inequalities. So rewrite (14.12) as

$$
(\text{tr } U) + \frac{(\text{tr } U)^2}{n} + \text{Ric}(\dot{\gamma}) = -\left\| U - \left(\frac{\text{tr } U}{n}\right) I_n \right\|_{\text{HS}}^2.
$$
 (14.33)

.

Then the left-hand side in (14.33) becomes

$$
\begin{aligned} (\log \mathcal{J}_0)^{\cdot \cdot} &+ \frac{[(\log \mathcal{J}_0)^{\cdot}]^2}{n} + \text{Ric}(\dot{\gamma}) \\ & = (\log \mathcal{J})^{\cdot \cdot} + \langle \nabla^2 V(\gamma) \cdot \dot{\gamma}, \dot{\gamma} \rangle + \frac{[(\log \mathcal{J})^{\cdot} + \dot{\gamma} \cdot \nabla V(\gamma)]^2}{n} + \text{Ric}(\dot{\gamma}). \end{aligned}
$$

By using the identity

$$
\frac{a^2}{n} = \frac{(a+b)^2}{N} - \frac{b^2}{N-n} + \frac{n}{N(N-n)} \left(b - a\frac{N-n}{n}\right)^2, \quad (14.34)
$$

we see that

$$
\frac{\left[ (\log \mathcal{J})^{\cdot} + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2}{n}
$$

$$
= \frac{\left[ (\log \mathcal{J})^{\cdot} \right]^2}{N} - \frac{(\dot{\gamma} \cdot \nabla V(\gamma))^2}{N - n} + \frac{n}{N(N - n)} \left[ \left( \frac{N - n}{n} \right) (\log \mathcal{J})^{\cdot} + \frac{N}{n} \dot{\gamma} \cdot \nabla V(\gamma) \right]^2
$$

$$
= \frac{\left[ (\log \mathcal{J})^{\cdot} \right]^2}{N} - \frac{(\dot{\gamma} \cdot \nabla V(\gamma))^2}{N - n} + \frac{n}{N(N - n)} \left[ \frac{N - n}{n} (\log \mathcal{J}_0)^{\cdot} + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2
$$

$$
= \frac{\left[ (\log \mathcal{J})^{\cdot} \right]^2}{N} - \frac{(\dot{\gamma} \cdot \nabla V(\gamma))^2}{N - n} + \frac{n}{N(N - n)} \left[ \frac{N - n}{n} \text{ tr } U + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2
$$

To summarize these computations it will be useful to introduce some more notation: first, as usual, the negative logarithm of the Jacobian determinant:

$$
\ell(t, x) := -\log \mathcal{J}(t, x); \tag{14.35}
$$

and then, the generalized Ricci tensor:

$$
\text{Ric}_{N,\nu} := \text{Ric} + \nabla^2 V - \frac{\nabla V \otimes \nabla V}{N - n},\tag{14.36}
$$

where the tensor product  $\nabla V \otimes \nabla V$  is a quadratic form on TM, defined by its action on tangent vectors as

$$
(\nabla V \otimes \nabla V)_x(v) = (\nabla V(x) \cdot v)^2;
$$

so

$$
Ric_{N,\nu}(\dot{\gamma}) = (Ric + \nabla^2 V)(\dot{\gamma}) - \frac{(\nabla V \cdot \dot{\gamma})^2}{N - n}.
$$

It is implicitly assumed in (14.36) that  $N \geq n$  (otherwise the correct definition is Ric<sub>N,v</sub> =  $-\infty$ ); if N = n the convention is  $0 \times \infty = 0$ , so (14.36) still makes sense if  $\nabla V = 0$ . Note that  $\text{Ric}_{\infty,\nu} = \text{Ric} + \nabla^2 V$ , while  $\text{Ric}_{n,\text{vol}} = \text{Ric}$ .

The conclusion of the preceding computations is that

$$
\ddot{\ell} = \frac{\dot{\ell}^2}{N} + \text{Ric}_{N,\nu}(\dot{\gamma}) + \left\| U - \left(\frac{\text{tr } U}{n}\right) I_n \right\|_{\text{HS}}^2 + \frac{n}{N(N-n)} \left[ \left(\frac{N-n}{n}\right) \text{tr } U + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2.
$$
 (14.37)

When  $N = \infty$  this takes a simpler form:

$$
\ddot{\ell} = \text{Ric}_{\infty,\nu}(\dot{\gamma}) + \left\| U - \left(\frac{\text{tr } U}{n}\right) I_n \right\|_{\text{HS}}^2 \tag{14.38}
$$

When  $N < \infty$  one can introduce

$$
\mathcal{D}(t) := \mathcal{J}(t)^{\frac{1}{N}},
$$

and then formula (14.37) becomes

$$
- N \frac{\ddot{\mathcal{D}}}{\mathcal{D}} = \text{Ric}_{N,\nu}(\dot{\gamma}) + \left\| U - \left(\frac{\text{tr } U}{n}\right) I_n \right\|_{\text{HS}}^2 + \frac{n}{N(N-n)} \left[ \left(\frac{N-n}{n}\right) \text{tr } U + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2.
$$
 (14.39)

Of course, it is a trivial corollary of (14.37) and (14.39) that

$$
\left\{\begin{array}{l} \ddot{\ell} \geq \frac{\dot{\ell}^{2}}{N} + \text{Ric}_{N,\nu}(\dot{\gamma}) \\ -N\frac{\ddot{\mathcal{D}}}{\mathcal{D}} \geq \text{Ric}_{N,\nu}(\dot{\gamma})\end{array}\right. \tag{14.40}
$$

,

Finally, if one wishes, one can also take out the direction of motion (skip at first reading and go directly to the next section). Define, with self-explicit notation,

$$
\mathcal{J}_{\perp}(t,x) = \mathcal{J}_{0,\perp}(t,x) \, \frac{e^{-V(T_t(x))}}{e^{-V(x)}}
$$

and  $\ell_{\perp} = -\log \mathcal{J}_{\perp}, \mathcal{D}_{\perp} = \mathcal{J}_{\perp}^{\frac{1}{N}}$  $\perp^N$ . Now, in place of (14.33), use

$$
(\text{tr } U_{\perp})^{.} + \frac{(\text{tr } U_{\perp})^{2}}{n-1} + \text{Ric}(\dot{\gamma}) = -\left\| U_{\perp} - \left(\frac{\text{tr } U_{\perp}}{n-1}\right) I_{n-1} \right\|_{\text{HS}}^{2} - \sum_{j=2}^{n} u_{1j}^{2}
$$
\n(14.41)

as a starting point. Computations quite similar to the ones above lead to

$$
\ddot{\ell}_{\perp} = \frac{(\dot{\ell}_{\perp})^2}{N-1} + \text{Ric}_{N,\nu}(\dot{\gamma}) + \left\| U_{\perp} - \left(\frac{\text{tr } U_{\perp}}{n-1}\right) I_{n-1} \right\|_{\text{HS}}^2 + \frac{n-1}{(N-1)(N-n)} \left[ \left(\frac{N-n}{n-1}\right) \text{tr } U + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2 + \sum_{j=2}^n u_{1j}^2. \tag{14.42}
$$

In the case  $N = \infty$ , this reduces to

$$
\ddot{\ell}_{\perp} = \text{Ric}_{\infty,\nu}(\dot{\gamma}) + \left\| U_{\perp} - \left( \frac{\text{tr } U_{\perp}}{n-1} \right) I_{n-1} \right\|_{\text{HS}}^2 + \sum_{j=2}^n u_{1j}^2; \qquad (14.43)
$$

and in the case  $N < \infty$ , to

$$
-N\frac{\ddot{\mathcal{D}}_{\perp}}{\mathcal{D}_{\perp}} = \text{Ric}_{N,\nu}(\dot{\gamma}) + \left\| U_{\perp} - \left(\frac{\text{tr } U_{\perp}}{n-1}\right) I_{n-1} \right\|_{\text{HS}}^2 + \frac{n-1}{(N-1)(N-n)} \left[ \left(\frac{N-n}{n-1}\right) \text{tr } U + \dot{\gamma} \cdot \nabla V(\gamma) \right]^2 + \sum_{j=2}^n u_{1j}^2. \tag{14.44}
$$

As corollaries,

The following inequalities are presented:

$$
\begin{cases}
 \ddot{\ell}_{\perp} \ge \frac{(\dot{\ell}_{\perp})^2}{N-1} + \text{Ric}_{N,\nu}(\dot{\gamma}) \\
 -N\frac{\ddot{\mathcal{D}}_{\perp}}{\mathcal{D}_{\perp}} \ge \text{Ric}_{N,\nu}(\dot{\gamma}).
 \end{cases}
$$

$$
(14.45)
$$

## Generalized Bochner formula and $\Gamma_2$ formalism

Of course there is an Eulerian translation of all that. This Eulerian formula can be derived either from the Lagrangian calculation, or from the Bochner formula, by a calculation parallel to the above one; the latter approach is conceptually simpler, while the former is faster. In any case the result is best expressed in terms of the differential operator

$$
L = \Delta - \nabla V \cdot \nabla, \qquad (14.46)
$$

and can be written

$$
L \frac{\left|\nabla \psi\right|^2}{2} - \nabla \psi \cdot \nabla L\psi
$$
  
= 
$$
\left\Vert \nabla^2 \psi \right\Vert_{\text{HS}}^2 + (\text{Ric} + \nabla^2 V)(\nabla \psi)
$$
  
= 
$$
\frac{(L\psi)^2}{N} + \text{Ric}_{N,\nu}(\nabla \psi)
$$
  
+ 
$$
\left( \left\Vert \nabla^2 \psi - \left( \frac{\Delta \psi}{n} \right) I_n \right\Vert_{\text{HS}}^2 + \frac{n}{N(N-n)} \left[ \left( \frac{N-n}{n} \right) \Delta \psi + \nabla V \cdot \nabla \psi \right]^2 \right).
$$

It is convenient to reformulate this formula in terms of the  $\Gamma_2$  formalism. Given a general linear operator  $L$ , one defines the associated  $\Gamma$  operator (or *carré du champ*) by the formula

$$
\Gamma(f,g) = \frac{1}{2} [L(fg) - fLg - gLf]. \tag{14.47}
$$

Note that  $\Gamma$  is a bilinear operator, which in some sense encodes the deviation of L from being a derivation operator. In our case, for (14.46),

$$
\Gamma(f,g) = \nabla f \cdot \nabla g.
$$

Next introduce the  $\Gamma_2$  operator (or *carré du champ itéré*):

$$
\Gamma_2(f,g) = \frac{1}{2} \big[ LT(fg) - \Gamma(f,Lg) - \Gamma(g,Lf) \big]. \tag{14.48}
$$

In the case of (14.46), the important formula for later purposes is

$$
\Gamma_2(\psi) := \Gamma_2(\psi, \psi) = L \frac{|\nabla \psi|^2}{2} - \nabla \psi \cdot \nabla (L\psi). \tag{14.49}
$$

Then our previous computations can be rewritten as

$$
\Gamma_2(\psi) = \frac{(L\psi)^2}{N} + \text{Ric}_{N,\nu}(\nabla\psi)
$$
$$
+ \left( \left\| \nabla^2 \psi - \left( \frac{\Delta \psi}{n} \right) I_n \right\|_{\text{HS}}^2 + \frac{n}{N(N-n)} \left[ \left( \frac{N-n}{n} \right) \Delta \psi + \nabla V \cdot \nabla \psi \right]^2 \right).
$$
(14.50)

Of course, a trivial corollary is

$$
\Gamma_2(\psi) \ge \frac{(L\psi)^2}{N} + \text{Ric}_{N,\nu}(\nabla \psi). \tag{14.51}
$$

And as the reader has certainly guessed, one can now take out the direction of motion (this computation is provided for completeness but will not be used): As before, define

$$
\widehat{\nabla\psi} = \frac{\nabla\psi}{|\nabla\psi|},
$$

then if f is a smooth function, let  $\nabla^2 f$  be  $\nabla^2 f$  restricted to the space orthogonal to  $\nabla \psi$ , and  $\Delta_{\perp} f = \text{tr}(\nabla^2_{\perp} f)$ , i.e.