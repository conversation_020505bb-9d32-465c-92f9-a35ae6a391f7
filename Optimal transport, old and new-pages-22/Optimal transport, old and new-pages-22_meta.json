{"table_of_contents": [{"title": "Positivity of the Jacobian", "heading_level": null, "page_id": 0, "polygon": [[133.5, 174.75], [286.5, 174.75], [286.5, 186.1083984375], [133.5, 186.1083984375]]}, {"title": "<PERSON><PERSON><PERSON>'s formula", "heading_level": null, "page_id": 1, "polygon": [[133.5, 116.25], [243.0, 116.25], [243.0, 127.5205078125], [133.5, 127.5205078125]]}, {"title": "Analytic and geometric consequences of <PERSON>icci curvature\nbounds", "heading_level": null, "page_id": 4, "polygon": [[133.5, 510.0], [465.75, 510.0], [465.75, 535.21875], [133.5, 535.21875]]}, {"title": "Change of reference measure and effective dimension", "heading_level": null, "page_id": 7, "polygon": [[133.5, 314.25], [447.0, 314.25], [447.0, 325.423828125], [133.5, 325.423828125]]}, {"title": "394 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 8, "polygon": [[133.5, 26.25], [243.0, 26.25], [243.0, 35.19140625], [133.5, 35.19140625]]}, {"title": "396 14 <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 10, "polygon": [[133.5, 25.5], [243.75, 25.5], [243.75, 35.25], [133.5, 35.25]]}, {"title": "Generalized <PERSON><PERSON><PERSON> formula and \\Gamma_2 formalism", "heading_level": null, "page_id": 11, "polygon": [[133.5, 336.75], [414.0, 336.75], [414.0, 351.0], [133.5, 351.0]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 43], ["TextInlineMath", 5], ["Equation", 2], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1994, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 38], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["Line", 55], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["ListItem", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1132, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 44], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 82], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1249, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 41], ["Text", 3], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 75], ["Equation", 5], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1034, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 45], ["Text", 3], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 103], ["Text", 8], ["Equation", 6], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1093, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 122], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3234, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 146], ["Equation", 7], ["TextInlineMath", 4], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3258, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 131], ["Equation", 5], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4368, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 73], ["Equation", 7], ["Text", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1238, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-22"}