{"table_of_contents": [{"title": "14.1. Bayesian Model Averaging", "heading_level": null, "page_id": 1, "polygon": [[79.98046875, 400.5], [285.22265625, 400.5], [285.22265625, 413.9736328125], [79.98046875, 413.9736328125]]}, {"title": "14.2. Committees", "heading_level": null, "page_id": 2, "polygon": [[82.318359375, 519.662109375], [196.013671875, 519.0], [196.013671875, 532.01953125], [82.318359375, 532.01953125]]}, {"title": "Exercise 14.2 then we obtain", "heading_level": null, "page_id": 4, "polygon": [[30.7001953125, 71.25], [183.75, 71.25], [183.75, 83.819091796875], [30.7001953125, 83.819091796875]]}, {"title": "658 14. COMBINING MODELS", "heading_level": null, "page_id": 5, "polygon": [[29.25, 40.5], [210.75, 40.5], [210.75, 51.949951171875], [29.25, 51.949951171875]]}, {"title": "AdaBoost", "heading_level": null, "page_id": 5, "polygon": [[137.56640625, 246.75], [185.25, 246.75], [185.25, 257.2294921875], [137.56640625, 257.2294921875]]}, {"title": "14.3.1 Minimizing exponential error", "heading_level": null, "page_id": 6, "polygon": [[138.75, 375.75], [339.0, 375.75], [339.0, 386.982421875], [138.75, 386.982421875]]}, {"title": "660 14. COMBINING MODELS", "heading_level": null, "page_id": 7, "polygon": [[29.25, 40.5], [210.0, 40.5], [210.0, 51.299560546875], [29.25, 51.299560546875]]}, {"title": "14.3.2 Error functions for boosting", "heading_level": null, "page_id": 8, "polygon": [[137.56640625, 467.25], [335.671875, 467.25], [335.671875, 478.3623046875], [137.56640625, 478.3623046875]]}, {"title": "662 14. COMBINING MODELS", "heading_level": null, "page_id": 9, "polygon": [[29.25, 41.25], [210.0, 41.25], [210.0, 51.8280029296875], [29.25, 51.8280029296875]]}, {"title": "14.4. Tree-based Models", "heading_level": null, "page_id": 10, "polygon": [[82.5, 246.75], [239.25, 246.75], [239.25, 260.96923828125], [82.5, 260.96923828125]]}, {"title": "664 14. COMBINING MODELS", "heading_level": null, "page_id": 11, "polygon": [[29.25, 40.5], [210.75, 40.5], [210.75, 51.7467041015625], [29.25, 51.7467041015625]]}, {"title": "", "heading_level": null, "page_id": 11, "polygon": [[31.10009765625, 569.4169921875], [97.822265625, 569.4169921875], [97.822265625, 579.1728515625], [31.10009765625, 579.1728515625]]}, {"title": "666 14. COMBINING MODELS", "heading_level": null, "page_id": 13, "polygon": [[29.25, 40.5], [210.75, 40.5], [210.75, 51.4215087890625], [29.25, 51.4215087890625]]}, {"title": "14.5. Conditional Mixture Models", "heading_level": null, "page_id": 13, "polygon": [[80.4111328125, 471.0], [290.25, 471.0], [290.25, 483.890625], [80.4111328125, 483.890625]]}, {"title": "14.5.1 Mixtures of linear regression models", "heading_level": null, "page_id": 14, "polygon": [[138.75, 140.25], [383.25, 140.25], [383.25, 151.459716796875], [138.75, 151.459716796875]]}, {"title": "668 14. COMBINING MODELS", "heading_level": null, "page_id": 15, "polygon": [[29.25, 40.5], [210.0, 40.5], [210.0, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "14.5. Conditional Mixture Models 669", "heading_level": null, "page_id": 16, "polygon": [[270.0, 40.5], [473.25, 40.5], [473.25, 51.380859375], [270.0, 51.380859375]]}, {"title": "670 14. COMBINING MODELS", "heading_level": null, "page_id": 17, "polygon": [[29.25, 40.5], [210.0, 40.5], [210.0, 51.380859375], [29.25, 51.380859375]]}, {"title": "14.5.2 Mixtures of logistic models", "heading_level": null, "page_id": 17, "polygon": [[138.0, 381.0], [330.0, 381.0], [330.0, 392.8359375], [138.0, 392.8359375]]}, {"title": "672 14. COMBINING MODELS", "heading_level": null, "page_id": 19, "polygon": [[29.25, 40.5], [210.0, 40.5], [210.0, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "14.5.3 Mixtures of experts", "heading_level": null, "page_id": 19, "polygon": [[138.0, 426.0], [288.0, 426.0], [288.0, 436.412109375], [138.0, 436.412109375]]}, {"title": "14.5. Conditional Mixture Models 673", "heading_level": null, "page_id": 20, "polygon": [[267.2578125, 40.5], [473.25, 40.5], [473.25, 50.89306640625], [267.2578125, 50.89306640625]]}, {"title": "674 14. COMBINING MODELS", "heading_level": null, "page_id": 21, "polygon": [[29.25, 40.5], [210.75, 40.5], [210.75, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "Exercises", "heading_level": null, "page_id": 21, "polygon": [[30.0, 145.5], [117.75, 147.0], [117.75, 167.25], [30.0, 167.25]]}, {"title": "676 14. COMBINING MODELS", "heading_level": null, "page_id": 23, "polygon": [[29.25, 40.5], [210.75, 40.5], [210.75, 51.7467041015625], [29.25, 51.7467041015625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 42], ["Line", 18], ["Text", 3], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 40], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 50], ["Text", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 75], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 52], ["ListItem", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 2], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1816, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 50], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 61], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1880, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 54], ["Text", 6], ["Equation", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1139, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 46], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1954, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 39], ["Text", 4], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1413, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 42], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 48], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 45], ["Text", 6], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 61], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 426], ["Line", 63], ["TextInlineMath", 5], ["Equation", 4], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1234, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 73], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 79], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 77], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1757, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["Line", 49], ["TextInlineMath", 6], ["Equation", 4], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 64], ["Text", 5], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1818, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 43], ["ListItem", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 41], ["ListItem", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 24], ["ListItem", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_673-696"}