{"table_of_contents": [{"title": "Semantic Image Synthesis with Spatially-Adaptive Normalization", "heading_level": null, "page_id": 0, "polygon": [[94.2802734375, 105.75], [498.0, 105.75], [498.0, 119.3994140625], [94.2802734375, 119.3994140625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.4833984375, 480.0], [191.25, 480.0], [191.25, 491.1328125], [144.4833984375, 491.1328125]]}, {"title": "https://github.com/NVlabs/SPADE.", "heading_level": null, "page_id": 0, "polygon": [[307.5, 480.75], [497.25, 480.75], [497.25, 491.90625], [307.5, 491.90625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[305.701171875, 506.25], [385.5, 506.25], [385.5, 517.4296875], [305.701171875, 517.4296875]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[48.0, 326.25], [134.25, 326.25], [134.25, 337.60546875], [48.0, 337.60546875]]}, {"title": "3. Semantic Image Synthesis", "heading_level": null, "page_id": 1, "polygon": [[307.5, 471.75], [455.25, 471.75], [455.25, 482.25], [307.5, 482.25]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 3, "polygon": [[48.75, 667.5], [128.25, 667.5], [128.25, 678.69140625], [48.75, 678.69140625]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.494140625, 512.25], [378.75, 512.25], [378.75, 524.390625], [307.494140625, 524.390625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.96630859375], [48.75, 83.96630859375]]}, {"title": "<PERSON>. Additional Implementation Details", "heading_level": null, "page_id": 10, "polygon": [[48.0, 72.0], [243.0, 72.0], [243.0, 83.57958984375], [48.0, 83.57958984375]]}, {"title": "B. Additional Ablation Study", "heading_level": null, "page_id": 12, "polygon": [[48.0, 72.75], [199.5, 72.75], [199.5, 83.77294921875], [48.0, 83.77294921875]]}, {"title": "<PERSON>. Additional Results", "heading_level": null, "page_id": 12, "polygon": [[307.5, 216.75], [418.5, 216.75], [418.5, 227.970703125], [307.5, 227.970703125]]}, {"title": "", "heading_level": null, "page_id": 17, "polygon": [[284.9326171875, 263.548828125], [313.76953125, 263.548828125], [313.76953125, 273.216796875], [284.9326171875, 273.216796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 54], ["Text", 5], ["SectionHeader", 4], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3524, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 103], ["Text", 7], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 631], ["Line", 130], ["TextInlineMath", 7], ["Reference", 4], ["Equation", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1305, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 92], ["Text", 7], ["ListItem", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 748, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["TableCell", 138], ["Line", 39], ["Reference", 4], ["Caption", 3], ["Text", 3], ["Figure", 2], ["FigureGroup", 2], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7161, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 56], ["TableCell", 44], ["Text", 6], ["Caption", 2], ["Reference", 2], ["Picture", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["TableCell", 186], ["Line", 61], ["Text", 4], ["Reference", 4], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 7762, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 48], ["Text", 6], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 632, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 501], ["Line", 115], ["ListItem", 33], ["Reference", 33], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 416], ["Line", 97], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 90], ["Text", 5], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2330, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 53], ["TableCell", 15], ["Text", 4], ["Reference", 4], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["TableCell", 84], ["Line", 61], ["Text", 7], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2850, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 100], ["Span", 13], ["Line", 4], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9958, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 90], ["Span", 13], ["Line", 4], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 6], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 138], ["Span", 16], ["Line", 3], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9732, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["Line", 9], ["Text", 5], ["Picture", 2], ["Reference", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 8, "llm_error_count": 0, "llm_tokens_used": 5411, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 5], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Semantic Image Synthesis with Spatially-Adaptive Normalization"}