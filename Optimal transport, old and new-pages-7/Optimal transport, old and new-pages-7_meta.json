{"table_of_contents": [{"title": "Displacement interpolation", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.5], [326.25, 97.5], [326.25, 111.568359375], [133.5, 111.568359375]]}, {"title": "Deterministic interpolation via action-minimizing curves", "heading_level": null, "page_id": 1, "polygon": [[133.5, 195.75], [468.75, 195.75], [468.75, 206.5078125], [133.5, 206.5078125]]}, {"title": "130 7 Displacement interpolation", "heading_level": null, "page_id": 5, "polygon": [[133.5, 25.95849609375], [285.0, 25.95849609375], [285.0, 35.6748046875], [133.5, 35.6748046875]]}, {"title": "Definition 7.6 (Classical conditions on a La<PERSON>ngian function).", "heading_level": null, "page_id": 5, "polygon": [[133.5, 204.57421875], [469.5, 204.57421875], [469.5, 215.7890625], [133.5, 215.7890625]]}, {"title": "132 7 Displacement interpolation", "heading_level": null, "page_id": 7, "polygon": [[133.5, 26.25], [285.0, 26.25], [285.0, 35.698974609375], [133.5, 35.698974609375]]}, {"title": "Abstract Lagrangian action", "heading_level": null, "page_id": 8, "polygon": [[133.5, 335.25], [297.7822265625, 335.25], [297.7822265625, 346.88671875], [133.5, 346.88671875]]}, {"title": "134 7 Displacement interpolation", "heading_level": null, "page_id": 9, "polygon": [[133.5, 26.25], [285.0, 26.25], [285.0, 35.650634765625], [133.5, 35.650634765625]]}, {"title": "Interpolation of random variables", "heading_level": null, "page_id": 12, "polygon": [[133.5, 525.75], [333.0, 525.75], [333.0, 537.15234375], [133.5, 537.15234375]]}, {"title": "Corollary 7.23 (Uniqueness of displacement interpolation).\nWith the same assumptions as in Theorem 7.21, if:", "heading_level": null, "page_id": 15, "polygon": [[133.5, 240.75], [463.5, 240.75], [463.5, 265.482421875], [133.5, 265.482421875]]}, {"title": "142 7 Displacement interpolation", "heading_level": null, "page_id": 17, "polygon": [[133.5, 25.86181640625], [285.0, 25.86181640625], [285.0, 35.8681640625], [133.5, 35.8681640625]]}, {"title": "144 7 Displacement interpolation", "heading_level": null, "page_id": 19, "polygon": [[133.5, 25.86181640625], [285.0, 25.86181640625], [285.0, 35.43310546875], [133.5, 35.43310546875]]}, {"title": "146 7 Displacement interpolation", "heading_level": null, "page_id": 21, "polygon": [[133.5, 26.25], [285.0, 26.25], [285.0, 35.52978515625], [133.5, 35.52978515625]]}, {"title": "148 7 Displacement interpolation", "heading_level": null, "page_id": 23, "polygon": [[133.5, 25.86181640625], [285.0, 25.86181640625], [285.0, 35.52978515625], [133.5, 35.52978515625]]}, {"title": "Displacement interpolation between intermediate times\nand restriction", "heading_level": null, "page_id": 25, "polygon": [[133.5, 495.0], [462.0, 495.0], [462.0, 520.5234375], [133.5, 520.5234375]]}, {"title": "154 7 Displacement interpolation", "heading_level": null, "page_id": 29, "polygon": [[133.5, 25.934326171875], [285.75, 25.934326171875], [285.75, 35.457275390625], [133.5, 35.457275390625]]}, {"title": "Interpolation of prices", "heading_level": null, "page_id": 30, "polygon": [[133.5, 503.12109375], [267.0, 503.12109375], [267.0, 514.72265625], [133.5, 514.72265625]]}, {"title": "160 7 Displacement interpolation", "heading_level": null, "page_id": 35, "polygon": [[133.5, 26.25], [285.0, 26.25], [285.0, 35.892333984375], [133.5, 35.892333984375]]}, {"title": "Appendix: Paths in metric structures", "heading_level": null, "page_id": 35, "polygon": [[133.5, 214.5], [356.25, 214.5], [356.25, 226.037109375], [133.5, 226.037109375]]}, {"title": "162 7 Displacement interpolation", "heading_level": null, "page_id": 37, "polygon": [[133.5, 26.2001953125], [285.0, 26.2001953125], [285.0, 35.43310546875], [133.5, 35.43310546875]]}, {"title": "164 7 Displacement interpolation", "heading_level": null, "page_id": 39, "polygon": [[133.5, 26.127685546875], [285.0, 26.127685546875], [285.0, 35.505615234375], [133.5, 35.505615234375]]}, {"title": "168 7 Displacement interpolation", "heading_level": null, "page_id": 43, "polygon": [[133.5, 26.25], [285.0, 26.25], [285.0, 35.62646484375], [133.5, 35.62646484375]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 44, "polygon": [[233.25, 360.0], [358.892578125, 360.0], [358.892578125, 371.443359375], [233.25, 371.443359375]]}, {"title": "172 7 Displacement interpolation", "heading_level": null, "page_id": 47, "polygon": [[133.5, 26.103515625], [285.0, 26.103515625], [285.0, 35.384765625], [133.5, 35.384765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 27], ["Text", 2], ["ListItem", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4753, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 44], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 44], ["TextInlineMath", 6], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 49], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["ListItem", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 47], ["ListItem", 5], ["Equation", 3], ["TextInlineMath", 2], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1932, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 42], ["Text", 6], ["TextInlineMath", 5], ["SectionHeader", 2], ["ListItem", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 57], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 57], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 528], ["Line", 57], ["Text", 7], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1093, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 508], ["Line", 43], ["TextInlineMath", 9], ["Equation", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 542], ["Line", 56], ["TextInlineMath", 7], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 711], ["Line", 51], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 45], ["TextInlineMath", 5], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 37], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 610], ["Line", 57], ["TextInlineMath", 8], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["Line", 36], ["TextInlineMath", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 40], ["ListItem", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 693], ["Line", 91], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 778], ["Line", 87], ["TextInlineMath", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 822], ["Line", 73], ["TextInlineMath", 6], ["Equation", 2], ["ListItem", 2], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 872], ["Line", 193], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3993, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 724], ["Line", 76], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 53], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 48], ["TextInlineMath", 4], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 56], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 590], ["Line", 82], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 690], ["Line", 45], ["TextInlineMath", 8], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 663], ["Line", 79], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 1000], ["Line", 84], ["TextInlineMath", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1377, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 702], ["Line", 83], ["Equation", 5], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1416, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 35], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 90], ["TextInlineMath", 5], ["Equation", 4], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 46], ["TextInlineMath", 3], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["Line", 68], ["Equation", 5], ["TextInlineMath", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 62], ["TextInlineMath", 7], ["Equation", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 37], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 44], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 620], ["Line", 48], ["TextInlineMath", 6], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 44], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 58], ["TextInlineMath", 6], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 491], ["Line", 66], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1693, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 46], ["ListItem", 3], ["Text", 2], ["TextInlineMath", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 43], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 76], ["Text", 5], ["Equation", 3], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1119, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 37], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 40], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 48], ["Text", 4], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 48], ["Text", 3], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 40], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 38], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-7"}