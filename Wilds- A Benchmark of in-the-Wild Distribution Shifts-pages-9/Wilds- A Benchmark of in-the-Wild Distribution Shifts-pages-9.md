# E.7 FMoW-wilds

ML models for satellite imagery can enable global-scale monitoring of sustainability and economic challenges, aiding policy and humanitarian efforts in applications such as deforestation tracking (<PERSON> et al., 2013), population density mapping (<PERSON><PERSON><PERSON> et al., 2017), crop yield prediction (<PERSON> et al., 2020b), and other economic tracking applications (<PERSON><PERSON> et al., 2018). As satellite data constantly changes due to human activity and environmental processes, these models must be robust to distribution shifts over time. Moreover, as there can be disparities in the data available between regions, these models should ideally have uniformly high accuracies instead of only doing well on data-rich regions and countries.

We study this problem on a variant of the Functional Map of the World dataset (<PERSON> et al., 2018).

## E.7.1 SETUP

Problem setting. We consider a hybrid domain generalization and subpopulation shift problem, where the input x is a RGB satellite image (resized to  $224 \times 224$  pixels), the label y is one of 62 building or land use categories, and the domain d represents both the year the image was taken as well as its geographical region (Africa, the Americas, Oceania, Asia, or Europe). We aim to solve both a domain generalization problem across time and improve subpopulation performance across regions.

Data. FMOW-WILDS is based on the Functional Map of the World dataset (<PERSON> et al., 2018), which collected and categorized high-resolution satellite images from over 200 countries based on the functional purpose of the buildings or land in the image, over the years 2002–2018 (see Figure 9). We use a subset of this data and split it into three time range domains, 2002–2013, 2013–2016, and 2016–2018, as well as five geographical regions as subpopulations (Africa, Americas, Oceania, Asia, and Europe). For each example, we also provide the timestamp and location coordinates, though our baseline models only use the coarse time ranges and geographical regions instead of these additional metadata.

We use the following data splits:

- 1. Training: 76,863 images from the years 2002–2013.
- 2. Validation (OOD): 19,915 images from the years from 2013–2016.
- 3. Test (OOD): 22,108 images from the years from 2016–2018.
- 4. Validation (ID): 11,483 images from the years from 2002–2013.
- 5. Test (ID): 11,327 images from the years from 2002–2013.

The original dataset did not evaluate models under distribution shifts. Our training split is a subset of the original training dataset, filtered for images in the appropriate time range; similarly, our OOD and ID validation splits are subsets of the original validation dataset, and our OOD and ID test splits are subsets of the original test dataset. See Appendix [E.7.4](#page-5-0) for more dataset details.

The train/val/test data splits contain images from disjoint location coordinates, and all splits contain data from all 5 geographic regions. The ID and OOD splits within the test and validation sets may have overlapping locations, but have non-overlapping time ranges. There is a disparity in the number of examples in each region, with Africa and Oceania having the least examples (Figure [23\)](#page-1-0); this could be due to bias in sampling and/or a lack of infrastructure and land data in certain regions.

Evaluation. We evaluate models by their average and worst-region OOD accuracies. The former measures the ability of the model to generalize across time, while the latter additionally measures how well models do across different regions/subpopulations under a time shift.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image contains two bar charts side-by-side. The left chart is titled "In-domain (before 2013)" and the right chart is titled "OOD (after 2013)". Both charts have the x-axis labeled "Region" with categories Asia, Europe, Africa, Americas, and Oceania. The y-axis for both charts is labeled "Proportion" and ranges from 0.0 to 0.4. In the "In-domain" chart, the proportions are approximately: Asia 0.23, Europe 0.45, Africa 0.03, Americas 0.27, Oceania 0.03. In the "OOD" chart, the proportions are approximately: Asia 0.23, Europe 0.41, Africa 0.10, Americas 0.44, Oceania 0.04.

Figure 23: Number of examples from each region of the world in FMoW-wilds on the ID vs. OOD splits of the data. There is much less data from Africa and Oceania than other regions.

<span id="page-1-1"></span>Table 18: Average and worst-region accuracies (%) under time shifts in FMoW-wILDS. Models are trained on data before 2013 and tested on held-out location coordinates from in-distribution (ID) or out-of-distribution (OOD) test sets. ID results correspond to the train-to-train setting. Parentheses show standard deviation across 3 replicates.

|              | Validation (ID) | Validation (OOD) | Test (ID)          | Test (OOD)         |
|--------------|-----------------|------------------|--------------------|--------------------|
| Average      |                 |                  |                    |                    |
| <b>ERM</b>   | 61.2 (0.52)     | 59.5 (0.37)      | <b>59.7</b> (0.65) | <b>53.0</b> (0.55) |
| CORAL        | 58.3 (0.28)     | 56.9 (0.25)      | 57.2 (0.90)        | 50.5 (0.36)        |
| <b>IRM</b>   | 58.6 (0.07)     | 57.4 (0.37)      | 57.7 (0.10)        | 50.8 (0.13)        |
| Group DRO    | 60.5 (0.36)     | 58.8 (0.19)      | 59.4 (0.11)        | 52.1 (0.50)        |
| Worst-region |                 |                  |                    |                    |
| <b>ERM</b>   | 59.2 (0.69)     | 48.9 (0.62)      | <b>58.3</b> (0.92) | <b>32.3</b> (1.25) |
| CORAL        | 55.9 (0.50)     | 47.1 (0.43)      | 55.0 (1.02)        | 31.7 (1.24)        |
| <b>IRM</b>   | 56.6 (0.59)     | 47.5 (1.57)      | 56.0 (0.34)        | 30.0 (1.37)        |
| Group DRO    | 57.9 (0.62)     | 46.5 (0.25)      | 57.8 (0.60)        | 30.8 (0.81)        |

|            | Asia        | Europe      | Africa      | Americas    | Oceania     | Worst region |
|------------|-------------|-------------|-------------|-------------|-------------|--------------|
| OOD Test   |             |             |             |             |             |              |
| ERM        | 55.4 (0.95) | 55.6 (0.53) | 32.3 (1.25) | 55.7 (0.48) | 59.1 (0.85) | 32.3 (1.25)  |
| CORAL      | 52.4 (0.96) | 52.6 (0.82) | 31.7 (1.24) | 53.3 (0.27) | 56.0 (2.02) | 31.7 (1.24)  |
| <b>IRM</b> | 52.9 (0.73) | 53.9 (0.28) | 30.0 (1.37) | 53.7 (0.51) | 55.0 (2.22) | 30.0 (1.37)  |
| Group DRO  | 54.7 (0.52) | 55.1 (0.39) | 30.8 (0.81) | 54.6 (0.48) | 58.5 (1.65) | 30.8 (0.81)  |
| ID Test    |             |             |             |             |             |              |
| <b>ERM</b> | 58.9 (1.19) | 58.4 (0.81) | 69.1 (2.64) | 61.4 (0.35) | 69.9 (0.53) | 58.3 (0.92)  |
| CORAL      | 56.6 (1.35) | 55.0 (1.02) | 69.2 (2.92) | 59.7 (0.83) | 70.8 (2.53) | 55.0 (1.02)  |
| <b>IRM</b> | 56.9 (0.62) | 56.0 (0.34) | 69.7 (2.16) | 59.7 (0.49) | 68.3 (2.00) | 56.0 (0.34)  |
| Group DRO  | 58.7 (0.33) | 57.9 (0.74) | 69.2 (0.28) | 61.1 (0.57) | 68.8 (2.38) | 57.8 (0.60)  |

<span id="page-2-1"></span>Table 19: The regional accuracies of models trained on data before 2013 and tested on held-out locations from ID ( $<$  2013) or OOD ( $\geq$  2016) test sets in FMoW-willos. ID results correspond to the train-to-train setting. Standard deviations over 3 trials are in parentheses.

<span id="page-2-0"></span>Table 20: Mixed-to-test comparison for ERM models on FMoW-will b. In the official setting, we train on ID examples (i.e., data from 2002–2013), whereas in the mixed-to-test ID setting, we train on  $ID + OOD$ examples (i.e., the same amount of data but half from 2002–2013 and half from 2013–2018, using a held-out set of data from 2013–2018). In both settings, we test on the same Test (ID) data (from 2002–2013) and Test (OOD) data (from 2013–2018) described in the official split. Models trained on the official split degrade in performance under the time shift, especially on the last year (2017) of the test data, and also fare poorly on the subpopulation shift, with low worst-region accuracy. Models trained on the mixed-to-test split have higher OOD average and last year accuracy and much higher OOD worst-region accuracy. Standard deviations over 3 trials are in parentheses.

| Setting       | Algorithm | Test (ID)   |              | Test (OOD)  |             |              |
|---------------|-----------|-------------|--------------|-------------|-------------|--------------|
|               |           | Average     | Worst-region | Average     | Last year   | Worst-region |
| Official      | ERM       | 59.7 (0.65) | 58.3 (0.92)  | 53.0 (0.55) | 48.1 (1.20) | 32.3 (1.25)  |
| Mixed-to-test | ERM       | 59.0 (0.47) | 56.9 (0.80)  | 57.4 (0.27) | 54.3 (0.22) | 48.6 (0.89)  |

Potential leverage. FMOW-WILDS considers both domain generalization across time and subpopulation shift across regions. As we provide both time and region annotations, models can leverage the structure across both space and time to improve robustness. For example, one hypothesis is that infrastructure development occurs smoothly over time. Utilizing this gradual shift structure with the timestamp metadata may enable adaptation across longer time periods (Kumar et al., 2020). The data distribution may also shift smoothly over spatial locations, and so enforcing some consistency with respect to spatial structure may improve predictions (Rolf et al., 2020; Jean et al., 2018). Furthermore, to mitigate the fact that some regions (e.g., Africa) have less labeled data, one could potentially transfer knowledge of other regions with similar economies and infrastructure. The location coordinate metadata allows for transfer learning across similar locations at any spatial scale.

#### E.7.2 Baseline results

Model. For all experiments, we follow Christie et al. (2018) and use a DenseNet-121 model (Huang et al., 2017) pretrained on ImageNet and with no  $L_2$  regularization. We use the Adam optimizer (Kingma and Ba, 2015) with an initial learning rate of  $10^{-4}$  that decays by 0.96 per epoch, and train for 50 epochs for with early stopping and with a batch size of 64. All reported results are averaged over 3 random seeds.

ERM results and performance drops. In the train-to-train comparison, Table [20](#page-2-0) shows that average accuracy drops by 6.7% when evaluated on the OOD test set ( $\geq$  2016) compared to the ID test set setting. The drop in average accuracy is especially large (11.6%) on images from the last year of the dataset (2017), furthest in the future from the training set. In addition, there is a substantial 26.0% drop in worst-region accuracy, with the model performing much worse in Africa than other regions (Table [19\)](#page-2-1).

We also ran a mixed-to-test comparison where we mixed in some data from the OOD period (2013–2018) into the training set, while keeping the overall training set size constant. A model trained on this mixed split had a much smaller drop in performance under the time and region shifts (Table [20\)](#page-2-0). While the magnitude of the ID-OOD gap in worst-region accuracy shrinks from 26.0% in the train-to-train setting to 16.3% in the mixed-to-test setting, the gap remains significant, implying that the drop in performance is largely due to the distribution shift across time and region instead of a change in the intrinsic difficulty of the OOD data.

Additional baseline methods. We compare ERM against CORAL, IRM, and Group DRO, using examples from different years as distinct domains. Table [18](#page-1-1) shows that many of these methods are comparable or worse than ERM in terms of both ID and OOD test performance. As with most other datasets, our grid search selected the lowest values of the penalty weights for CORAL ( $\lambda = 0.1$ ) and IRM  $(\lambda = 1)$ .

Discussion. Intriguingly, a large subpopulation shift across regions only occurs with a combination of time and region shift. This is corroborated by the mixed-split region shift results (Table [20\)](#page-2-0), which do not have a time shift between training and test sets, and correspondingly do not display a large disparity in performance across regions. This drop in performance may be partially due to label shift: from Figure [24,](#page-4-0) we see that the label distributions between Africa and other regions are very different, e.g., with a large drop in recreational facilities and a sharp increase in single residential units. We do not find a similarly large label shift between  $\lt 2013$  and  $\gt 2013$  splits of the dataset.

Despite having the smallest number of training examples (Figure [23\)](#page-1-0), the baseline models do not suffer a drop in performance in Oceania on validation or test sets (Table [19\)](#page-2-1). We hypothesize that infrastructure in Oceania is more similar to regions with a large amount of data than Africa. In contrast, Africa may be more distinct and may have changed more drastically over 2002-2018, the time extent of the dataset. This suggests that the subpopulation shift is not merely a function of the number of training examples.

We note that our dataset splits can separate on particular factors such as the introduction of new sensors, which is natural with progression over time. For example, the WorldView-3 sensor came online in 2014. Future work should look into the role of auxiliary factors such as new sensors that are associated with time but may be controllable. We did not find a sharp difference in performance due to the introduction of WorldView-3; we found that the performance decays gradually over time, suggesting that the performance drop comes from other factors.

As with PovertyMap-wilds, there are important ethical considerations associated with remote sensing applications, e.g., around surveillance and privacy issues, as well as the potential for systematic biases that negatively affect particular populations. As an example of the latter, the poor model performance on satellite images from Africa that we observe in FMoW-wilds raises issues of bias and fairness. With regard to privacy, we note that the image resolution in FMOW-WILDS is lower than that of other public and easily-accessible satellite data such as that from Google Maps. We refer interested readers to the UNICEF discussion paper by Berman et al. (2018) for a more in-depth discussion of the ethics of remote sensing especially as it pertains to development and humanitarian endeavors.

<span id="page-4-0"></span>Image /page/4/Figure/0 description: The image displays two bar charts side-by-side, labeled "Non-Africa" and "Africa" at the top. Both charts share the same y-axis on the left, which lists various class labels such as "zoo", "wind\_farm", "water\_treatment\_facility", and many others down to "airport". The x-axis for both charts is labeled "Proportion" and ranges from 0.0 to 0.2. Each bar represents the proportion of examples for a specific class label in either the Non-Africa or Africa category. The "Non-Africa" chart shows a few classes with proportions around 0.1 or slightly higher, including "recreational\_facility", "parking\_lot\_or\_garage", "place\_of\_worship", and "shopping\_mall". The "Africa" chart shows a significantly higher proportion for "shopping\_mall" (around 0.25), and also notable proportions for "recreational\_facility" (around 0.15) and "multi-unit\_residential" (around 0.15). Other classes have very low proportions in both categories. The figure is titled "Figure 24: Number of examples from each category in EMoW-WILDS in non-African and African regions."

Figure 24: Number of examples from each category in FMOW-WILDS in non-African and African regions. There is a large label shift between non-African regions and Africa.

## E.7.3 Broader context

Recognizing infrastructure and land features is crucial to many remote sensing applications. For example, in crop land prediction Wang et al. (2020b), recognizing gridded plot lines, plot circles, farm houses, and other visible features are important in recognizing crop fields. However, farming practices and equipment evolve over time and vary widely across the world, requiring both robust object recognition and synthesis of their different usage patterns.

Although the data is typically limited, we desire generalization on a global scale without requiring frequent large-scale efforts to gather more ground-truth data. It is natural to have labeled data with limited temporal or spatial extent since ground truth generally must be verified on the ground or requires manual annotations from domain experts (i.e., they are often hard to be crowdsourced). A number of existing remote sensing datasets have limited spatial or temporal scope, including the UC Merced Land Use Dataset (Yang and Newsam, 2010), TorontoCity (Wang et al., 2017), and SpaceNet (DigitalGlobe and Works, 2016). However, works based on these datasets generally do not systematically study shifts in time or location.

<span id="page-5-0"></span>

### E.7.4 Additional details

Data processing and modifications to the original dataset. The FMOW-WILDS dataset is derived from Christie et al. (2018), which collected over 1 million satellite images from over 200 countries over 2002-2018. We use the RGB version of the original dataset, which contains 523,846 total examples, excluding the multispectral version of the images. Methods that can utilize a sequence of images can group the images from the same location across multiple years together as input, but we consider the simple formulation here for our baseline evaluation.

The original dataset from Christie et al. (2018) is provided as a set of hierarchical directories with JPEG images of varying sizes. To reduce download times and I/O usage, we resize these images to  $224 \times 224$  pixels, and then store them as PNG images. We also collect all the metadata into CSV format for easy processing.

The original dataset is posed as a image time-series classification problem, where the model has access to a sequence of images at each location. For simplicity, we treat each image as a separate example, while making sure that the data splits all contain disjoint locations. We use the train/val/test splits from the original dataset, but separate out two OOD time segments: we treat the original validation data from 2013-2016 as OOD val and the original test data from 2016-2018 as OOD test. We remove data from after 2013 from the training set, which reduces the size of the training set in comparison to the original dataset.

Additional challenges in high-resolution satellite datasets. Compared to POVERTYMAPwilds, FMoW-wilds contains much higher resolution images (sub-meter resolution vs. 30m resolution) and contains a larger variety of viewpoints/tilts, both of which could present computational or algorithmic challenges. For computational purposes, we resized all images to  $224 \times 224$ (following Christie et al. (2018)), but raw images can be thousands of pixels wide. Some recent works have tried to balance this tradeoff between viewing overall context and the fine-grained detail (Uzkent and Ermon, 2020; Kim et al., 2016a), but how best to do this is an open question. FMOW-WILDS also contains additional information on azimuth and cloud cover which could be used to correct for the variety in viewpoints and image quality.

## E.8 PovertyMap-wilds

A different application of satellite imagery is poverty estimation across different spatial regions, which is essential for targeted humanitarian efforts in poor regions (Abelson et al., 2014; Espey et al., 2015). However, ground-truth measurements of poverty are lacking for much of the developing world, as field surveys are expensive (Blumenstock et al., 2015; Xie et al., 2016; Jean et al., 2016). For example, at least 4 years pass between nationally representative consumption or asset wealth surveys in the

majority of African countries, with seven countries that had either never conducted a survey or had gaps of over a decade between surveys (Yeh et al., 2020). One approach to this problem is to train ML models on countries with ground truth labels and then deploy them to different countries where we have satellite data but no labels.

We study this problem through a variant of the poverty mapping dataset collected by Yeh et al. (2020).

## E.8.1 SETUP

Problem setting. We consider a hybrid domain generalization and subpopulation shift problem, where the input x is a multispectral LandSat satellite image with 8 channels (resized to  $224 \times 224$ pixels), the output  $y$  is a real-valued asset wealth index computed from Demographic and Health Surveys (DHS) data, and the domain d represents the country the image was taken in and whether the image is of an urban or rural area. We aim to solve both a domain generalization problem across country borders and improve subpopulation performance across urban and rural areas.

Data. POVERTYMAP-WILDS is based on a dataset collected by Yeh et al. (2020), which assembles satellite imagery and survey data at 19,669 villages from 23 African countries between 2009 and 2016 (Figure 10). Each input image has 8 channels: 7 from the LandSat satellite and an 8th channel for nighttime light intensity from a separate satellite, as prior work has established that these night lights correlate with poverty measures (Noor et al., 2008; Elvidge et al., 2009).

There are  $23 \times 2 = 46$  domains corresponding to the 23 countries and whether the location is urban or rural. Each example comes with metadata on its location coordinates, survey year, and its urban/rural classification.

In contrast to other datasets, which have a single fixed ID/OOD split, the relatively small size of PovertyMap-wilds allows us to use 5 different folds, where each fold defines a different set of OOD countries. In each fold, we use the following splits of the data (the number of countries and images in each split varies slightly from fold to fold):

- 1. Training: ∼10000 images from 13–14 countries.
- 2. Validation (OOD): ∼4000 images from 4–5 different countries (distinct from training and test (OOD) countries).
- 3. Test (OOD): ∼4000 images from 4–5 different countries (distinct from training and validation (OOD) countries).
- 4. Validation (ID):  $\sim$ 1000 images from the same 13–14 countries in the training set.
- 5. Test (ID): ∼1000 images from the same 13–14 countries in the training set.

All splits contain images of both urban and rural locations, with the countries assigned randomly to each split in each fold.

The distribution of wealth may shift across countries due to differing levels economic development, agricultural practices, and other factors. For example, Abelson et al. (2014) use thatched vs. metal roofs to distinguish between poor and wealthy households, respectively in Kenya and Uganda. However, other countries may have a different mapping of roof type to wealth where metal roofs signify more poor households. Similar issues can arise when looking at the health of crops (related to vegetation indices such as NDVI that are simple functions of the multispectral channels in the satellite image) as a sign for wealth in rural areas, since crop health is related to climate and the choice of crops, which vary upon region.

Asset wealth may also shift dramatically between countries. Figure [25](#page-7-0) shows the mean asset wealth per country, as well as urban vs. rural asset wealth per country. Mean asset wealth ranges from  $-0.4$  to  $+0.8$  depending on the country. There is a stark difference between mean asset wealth

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image contains two bar charts side-by-side. The left chart displays asset wealth for various countries, with countries listed on the y-axis and asset wealth on the x-axis, ranging from -0.4 to 0.8. Ghana has the highest asset wealth at approximately 0.8, while the Democratic Republic of Congo has the lowest at approximately -0.4. The right side of the image shows two more bar charts, labeled 'Rural' and 'Urban', also displaying asset wealth for the same countries. In the 'Rural' chart, asset wealth varies, with Angola showing the highest at around 0.8 and Nigeria the lowest at around -0.1. In the 'Urban' chart, asset wealth is generally higher across most countries compared to the rural chart, with Ghana having the highest value at approximately 1.0.

Figure 25: Mean asset wealth by country on aggregate as well as urban and rural splits for each country, computed on the full dataset.

in urban and rural areas, with urban asset wealth being positive in all countries while rural mean asset wealth being mostly negative.

Evaluation. As is standard in the literature (Jean et al., 2016; Yeh et al., 2020), the models are evaluated on the Pearson correlation  $(r)$  between their predicted and actual asset wealth indices. We measure the average correlation, to test generalization under country shifts, and also the lower of the correlations on the urban and rural subpopulations, to test generalization between urban and rural subpopulations. We report the latter as previous works on poverty prediction from satellite imagery have noted that a significant part of model performance relies on distinguishing urban vs. rural areas, and improving performance within these subpopulations is an ongoing challenge, with rural areas generally faring worse under existing models (Jean et al., 2016; Yeh et al., 2020).

We average all correlations across the 5 different folds, using 1 random seed per fold. The resulting standard deviations reflect the fact that different folds have different levels of difficulty (e.g., depending on how similar the ID and OOD countries are). For the purposes of comparing different algorithms and models, we note that these standard deviations might make the comparisons appear noisier than they are, since a model might perform similarly across random seeds but still have a high standard deviation if it has different performances on different folds on the data. In contrast, other Wilds datasets report results on the same data split but averaged across different random seeds.

Potential leverage. Large socioeconomic differences between countries makes generalization across borders challenging. However, some indicators of wealth are known to be robust and are able to be seen from space. For example, roof type (e.g. thatched or metal roofing) has been shown to be a reliable proxy for wealth (Abelson et al., 2014), and contextual factors such as the health of nearby croplands, the presence of paved roads, and connections to urban areas are plausibly reliable signals for measuring poverty. Poverty measures are also known to be highly correlated across space, meaning nearby villages will likely have similar poverty measures, and methods can utilize this spatial structure (using the provided location coordinate metadata) to improve predictions (Jean et al., 2018; Rolf et al., 2020). We show the correlation with distance in Figure [26,](#page-8-0) which plots the distance between pairs of data points against the absolute differences in asset wealth between pairs.

## E.8.2 Baseline results

Model. For all experiments, we follow Yeh et al. (2020) and train a ResNet-18 model (He et al., 2016) to minimize squared error. We use the Adam optimizer (Kingma and Ba, 2015) with an initial learning rate of 10<sup>-3</sup> that decays by 0.96 per epoch, and train for 200 epochs for with early stopping (on OOD  $r$ ) and with a batch size of 64.

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The scatter plot shows the relationship between distance in kilometers on the x-axis and the mean absolute difference in asset wealth on the y-axis. The x-axis is on a logarithmic scale, ranging from 10^0 to 10^4. The y-axis ranges from 0.8 to 1.1. The data points generally increase as the distance increases. At a distance of 10^0 km, the mean absolute difference is around 0.8. As the distance increases to 10^1 km, the value rises to approximately 0.88. The trend continues upwards, reaching about 0.9 at 10^2 km. Beyond 10^3 km, the values start to increase more rapidly, with the highest data point reaching over 1.05 at approximately 10^4 km.

Figure 26: Mean absolute difference in asset wealth between two data points in the full dataset as a function of (great circle) distance between the two points. Smaller distances between data points correlate with more similar asset wealth measures. The pairs are binned by distance on a log (base 10) scale (100 bins), and the mean value of each bin is plotted at the midpoint distance of each bin.

<span id="page-8-1"></span>Table 21: Pearson correlation r (higher is better) on in-distribution and out-of-distribution (unseen countries) held-out sets in PovertyMap-wilds, including results on the rural subpopulations. ID results correspond to the train-to-train setting. All results are averaged over 5 different OOD country folds taken from Yeh et al. (2020), with standard deviations across different folds in parentheses.

|                          | Validation (ID) | Validation (OOD) | Test (ID)          | Test (OOD)         |
|--------------------------|-----------------|------------------|--------------------|--------------------|
| Overall                  |                 |                  |                    |                    |
| ERM                      | 0.82 (0.02)     | 0.80 (0.04)      | 0.82 (0.03)        | <b>0.78</b> (0.04) |
| CORAL                    | 0.82 (0.00)     | 0.80 (0.04)      | <b>0.83</b> (0.01) | 0.78 (0.05)        |
| IRM                      | 0.82 (0.02)     | 0.81 (0.03)      | 0.82 (0.02)        | 0.77 (0.05)        |
| Group DRO                | 0.78 (0.03)     | 0.78 (0.05)      | 0.80 (0.03)        | 0.75 (0.07)        |
| Worst urban/rural subpop |                 |                  |                    |                    |
| ERM                      | 0.58 (0.07)     | 0.51 (0.06)      | 0.57 (0.07)        | <b>0.45</b> (0.06) |
| CORAL                    | 0.59 (0.04)     | 0.52 (0.06)      | <b>0.59</b> (0.03) | 0.44 (0.06)        |
| IRM                      | 0.57 (0.06)     | 0.53 (0.05)      | 0.57 (0.08)        | 0.43 (0.07)        |
| Group DRO                | 0.49 (0.08)     | 0.46 (0.04)      | 0.54 (0.11)        | 0.39 (0.06)        |

ERM results and performance drops. When shifting across country borders, Table [21](#page-8-1) shows that ERM suffers a 0.04 drop in average  $r$  in the official OOD setting compared to the train-to-train ID setting. Moreover, the drop in performance is exacerbated when looking at urban and rural subpopulations, even though all splits contain urban and rural examples; the difference in worst  $r$  over the urban and rural subpopulations triples from 0.04 to 0.12 compared to the difference in average r. Correlation is consistently lower on the rural subpopulation than the urban subpopulation.

We ran an additional mixed-to-test comparison where we considered an alternative training set with data that was uniformly sampled from all countries, while keeping the overall training set size constant (i.e., compared to the standard training set, it has fewer examples from each country, but data from more countries). A model trained on this mixed split had a much smaller drop in performance between the ID and OOD test sets (Table [22\)](#page-9-0), which implies that the performance drop between the ID and OOD test sets is largely due to the distribution shift from seen to unseen countries.

<span id="page-9-0"></span>Table 22: Mixed-to-test comparison for ERM models on POVERTYMAP-WILDS. In the official OOD setting, we train on data from one set of countries, and then test on a different set of countries. In the mixed-to-test setting, we train on the same amount of data but sampled uniformly from all countries, and then test on data from the same countries as in the official setting. The Test (ID) and Test (OOD) sets used for the mixed-to-test results are smaller (subsampled at random) than those used for the official results, as some test examples were used for the training set in the mixed-to-test setting. Models trained on the official split degrade in performance, especially on rural subpopulations, while models trained on the mixed-to-test split do not.

| Setting       | Test (ID)   |             |             | Test (OOD)  |             |             |
|---------------|-------------|-------------|-------------|-------------|-------------|-------------|
|               | Overall $r$ | Rural $r$   | Urban $r$   | Overall $r$ | Rural $r$   | Urban $r$   |
| Official      | 0.82 (0.03) | 0.57 (0.07) | 0.66 (0.04) | 0.78 (0.04) | 0.46 (0.05) | 0.59 (0.11) |
| Mixed-to-test | 0.83 (0.02) | 0.61 (0.08) | 0.65 (0.06) | 0.83 (0.03) | 0.60 (0.06) | 0.65 (0.06) |

Additional baseline methods. We trained models with CORAL, IRM, and Group DRO, taking examples from different countries as coming from distinct domains. Table [21](#page-8-1) shows that these baselines are generally comparable to ERM and that they continue to be susceptible to shifts across countries and urban/rural areas. As with most other datasets, our grid search selected the lowest values of the penalty weights for CORAL ( $\lambda = 0.1$ ) and IRM ( $\lambda = 1$ ).

Discussion. These results corroborate performance drops seen in previous out-of-country generalization tests for poverty prediction from satellite imagery (Jean et al., 2016). In general, differences in infrastructure, economic development, agricultural practices, and even cultural differences can cause large shifts across country borders. Differences between urban and rural subpopulations have also been well-documented (Jean et al., 2016; Yeh et al., 2020). Models based on nighttime light information could suffer more in rural areas where nighttime light intensity is uniformly low or even zero.

Since survey years are also available, we could also investigate the robustness of the model over time. This would enable the models to be used for a longer time before needing more updated survey data, and we leave this to future work. Yeh et al. (2020) investigated predicting the change in asset wealth for individual villages in the World Bank Living Standards Measurement Surveys (LSMS), which is a longitudinal study containing multiple samples from the same village. POVERTYMAP-WILDS only contains cross-sectional samples which do not provide direct supervision for changes over time at any one location, but it is still possible to consider aggregate shifts across years.

As with FMOW-WILDS, there are important ethical considerations associated with remote sensing applications, e.g., around surveillance and privacy issues, as well as the potential for systematic biases that negatively affect particular populations. As we describe in Section [E.8.4,](#page-10-0) noise has been added to the location metadata in POVERTYMAP-WILDS to protect privacy. The distribution shifts across country and urban/rural boundaries that we study in PovertyMap-wilds are an example of a bias that affects model performance and therefore could have adverse policy consequences. We refer interested readers to the UNICEF discussion paper by Berman et al. (2018) for a more in-depth discussion of the ethics of remote sensing especially as it pertains to development and humanitarian endeavors.

## E.8.3 Broader context

Computational sustainability applications in the developing world also include tracking child mortality (Burke et al., 2016; Osgood-Zimmerman et al., 2018; Reiner et al., 2018), educational attainment (Graetz et al., 2018), and food security and crop yield prediction (You et al., 2017; Wang et al., 2020b; Xie et al., 2020). Remote sensing data and satellite imagery has the potential to enable high-resolution maps of many of these sustainability challenges, but as with poverty measures, ground truth labels in these applications come from expensive surveys or observations from human workers in the field. Some prior works consider using spatial structure (Jean et al., 2018; Rolf et al., 2020), unlabeled data (Xie et al., 2016; Jean et al., 2018; Xie et al., 2020), or weak sources of supervision (Wang et al., 2020b) to improve global models despite the lack of ground-truth data. We hope that POVERTYMAP-WILDS can be used to improve the robustness of machine learning techniques on satellite data, providing an avenue for cheaper and faster measurements that can be used to make progress on a general set of computational sustainability challenges.

#### <span id="page-10-0"></span>E.8.4 Additional details

Data processing. The POVERTYMAP-WILDS dataset is derived from Yeh et al. (2020), which gathers LandSat imagery and Demographic and Health Surveys (DHS) data from 19669 villages across 23 countries in Africa. The images are  $224 \times 224$  pixels large over 7 multispectral channels and an eighth nighttime light intensity channel. The LandSat satellite has a 30m resolution, meaning that each pixel of the image covers a  $30m^2$  spatial area. The location metadata is perturbed by the DHS as a privacy protection scheme; urban locations are randomly displaced by up to 2km and rural locations are perturbed by up to 10km. While this adds noise to the data, having a large enough image can guarantee that the location is in the image most of the time. The target is a real-valued composite asset wealth index computed as the first principal component of survey responses about household assets, which is thought to be a less noisy measure of households' longer-run economic well-being than other welfare measurements like consumption expenditure (Sahn and Stifel, 2003; Filmer and Scott, 2011). Asset wealth also has the advantage of not requiring adjustments for inflation or for purchasing power parity (PPP), as it is not based on a currency.

We normalize each channel by the pixel-wise mean and standard deviation for each channel, following (Yeh et al., 2020). We also do a similar data augmentation scheme, adding random horizontal and vertical flips as well as color jitter (brightness factor 0.8, contrast factor 0.8, saturation factor 0.8, hue factor 0.1).

The data download process provided by Yeh et al. (2020) involves downloading and processing imagery from Google Earth Engine. We process each image into a compressed NumPy array with 8 channels. We also provide all the metadata in a CSV format.

Additional results. We also ran an ablation where we removed the nighttime light intensity channel. This resulted in a drop in OOD  $r$  of 0.04 on average and 0.06 on the rural subpopulation, demonstrating the usefulness of the nightlight data in asset wealth estimation.

Modifications to the original dataset. We report a much larger drop in correlation due to spatial shift than in Yeh et al. (2020). To explain this, we note that our data splitting method is slightly different from theirs. They have two separate experiments (with different data splits) to test in-distribution vs. out-of-distribution generalization. In contrast, our data splits on both held-out in-distribution and out-of-distribution points at the same time with respect to the same training set, thus allowing us to compare both metrics simultaneously on one model as a more direct comparison. We use the same OOD country folds as the original dataset. However, Yeh et al. (2020) split the ID train/val/test while making sure that the spatial extent of the images between each split never overlap, while we simply take uniformly random splits of the ID data. This means that between our ID train/val/test splits, we may have images that have share some overlapping spatial extent, for example for two very nearby locations. Thus, a model can utilize some memorization here to improve ID performance. We believe this is reasonable since, with more ID data, more of the spatial area will be labeled and memorization should become an increasingly viable strategy for generalization in-domain.