{"table_of_contents": [{"title": "2 1. INTRODUCTION", "heading_level": null, "page_id": 1, "polygon": [[29.25, 40.5], [165.0, 40.5], [165.0, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "4 1. INTRODUCTION", "heading_level": null, "page_id": 3, "polygon": [[29.25, 40.974609375], [165.0, 40.974609375], [165.0, 51.462158203125], [29.25, 51.462158203125]]}, {"title": "1.1. Example: Polynomial Curve Fitting", "heading_level": null, "page_id": 3, "polygon": [[89.25, 416.25], [334.5, 416.25], [334.5, 429.908203125], [89.25, 429.908203125]]}, {"title": "1.1. Example: Polynomial Curve Fitting 5", "heading_level": null, "page_id": 4, "polygon": [[254.25, 40.5], [474.0, 40.5], [474.0, 51.5841064453125], [254.25, 51.5841064453125]]}, {"title": "6 1. INTRODUCTION", "heading_level": null, "page_id": 5, "polygon": [[29.25, 41.218505859375], [164.390625, 41.218505859375], [164.390625, 51.380859375], [29.25, 51.380859375]]}, {"title": "8 1. INTRODUCTION", "heading_level": null, "page_id": 7, "polygon": [[29.25, 40.5], [165.0, 40.5], [165.0, 51.5028076171875], [29.25, 51.5028076171875]]}, {"title": "10 1. INTRODUCTION", "heading_level": null, "page_id": 9, "polygon": [[30.0, 40.5], [170.25, 40.5], [170.25, 50.974365234375], [30.0, 50.974365234375]]}, {"title": "1.1. Example: Polynomial Curve Fitting 11", "heading_level": null, "page_id": 10, "polygon": [[247.5, 40.5], [473.25, 40.5], [473.25, 51.380859375], [247.5, 51.380859375]]}, {"title": "1.2. Probability Theory", "heading_level": null, "page_id": 11, "polygon": [[89.25, 131.25], [235.5, 131.25], [235.5, 145.19970703125], [89.25, 145.19970703125]]}, {"title": "14 1. INTRODUCTION", "heading_level": null, "page_id": 13, "polygon": [[30.0, 40.5], [171.28125, 40.5], [171.28125, 51.380859375], [30.0, 51.380859375]]}, {"title": "The Rules of Probability", "heading_level": null, "page_id": 13, "polygon": [[135.966796875, 471.0], [250.5, 471.0], [250.5, 481.2890625], [135.966796875, 481.2890625]]}, {"title": "1.2. Probability Theory 17", "heading_level": null, "page_id": 16, "polygon": [[322.5, 41.25], [473.25, 41.25], [473.25, 51.5841064453125], [322.5, 51.5841064453125]]}, {"title": "1.2.1 Probability densities", "heading_level": null, "page_id": 16, "polygon": [[138.75, 495.0], [288.75, 495.0], [288.75, 505.6787109375], [138.75, 505.6787109375]]}, {"title": "18 1. INTRODUCTION", "heading_level": null, "page_id": 17, "polygon": [[30.0, 40.5], [170.25, 40.5], [170.25, 51.380859375], [30.0, 51.380859375]]}, {"title": "1.2.2 Expectations and covariances", "heading_level": null, "page_id": 18, "polygon": [[138.75, 402.75], [341.25, 402.75], [341.25, 413.9736328125], [138.75, 413.9736328125]]}, {"title": "20 1. INTRODUCTION", "heading_level": null, "page_id": 19, "polygon": [[29.25, 40.5], [170.7890625, 40.5], [170.7890625, 51.7060546875], [29.25, 51.7060546875]]}, {"title": "1.2.3 Bayesian probabilities", "heading_level": null, "page_id": 20, "polygon": [[138.75, 71.25], [299.25, 71.25], [299.25, 83.5751953125], [138.75, 83.5751953125]]}, {"title": "<PERSON>\n1701–1761", "heading_level": null, "page_id": 20, "polygon": [[105.75, 500.25], [193.5, 501.0], [193.5, 524.865234375], [105.75, 524.865234375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 17], ["Text", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 41], ["Text", 5], ["SectionHeader", 1], ["Caption", 1], ["Picture", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 603, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 47], ["Text", 3], ["SectionHeader", 2], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 48], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 40], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1273, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["TableCell", 110], ["Line", 59], ["TextInlineMath", 2], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["ListItem", 1], ["Table", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2517, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 46], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1420, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 56], ["TextInlineMath", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 714, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["TableCell", 88], ["Line", 59], ["Text", 5], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["ListItem", 1], ["Figure", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8668, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 35], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 607], ["Line", 50], ["TextInlineMath", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 673, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 42], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 999, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 42], ["Equation", 8], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 33], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 709, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 53], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 52], ["Text", 4], ["Equation", 4], ["TextInlineMath", 4], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2176, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 39], ["Text", 6], ["Equation", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 47], ["Text", 11], ["Equation", 8], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 55], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 566, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_21-41"}