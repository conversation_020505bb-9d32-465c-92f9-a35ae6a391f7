{"table_of_contents": [{"title": "MedContext: Learning Contextual Cues\nfor Efficient Volumetric Medical\nSegmentation", "heading_level": null, "page_id": 0, "polygon": [[83.251953125, 51.3765869140625], [372.109375, 51.3765869140625], [372.109375, 101.6947021484375], [83.251953125, 101.6947021484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[39.04436860068259, 54.6334228515625], [134.40273037542661, 54.6334228515625], [134.40273037542661, 66.439453125], [39.04436860068259, 66.439453125]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 53.3306884765625], [137.71484375, 53.3306884765625], [137.71484375, 67.**********], [38.29351535836177, 67.**********]]}, {"title": "2.1 Architecture", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 78.69329833984375], [129.1467576791809, 78.69329833984375], [129.1467576791809, 90.1329345703125], [38.29351535836177, 90.1329345703125]]}, {"title": "2.2 Volumetric Masking Strategy", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 344.24755859375], [213.99317406143342, 344.24755859375], [213.99317406143342, 355.97216796875], [38.29351535836177, 355.97216796875]]}, {"title": "2.3 Voxel-Wise Segmentation Reconstruction", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 521.**********], [274.140625, 521.**********], [274.140625, 532.818359375], [38.29351535836177, 532.818359375]]}, {"title": "2.4 Supervised Voxel-Wise Segmentation", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 495.69042968750006], [267.30375426621157, 495.69042968750006], [267.30375426621157, 507.41503906250006], [52.55972696245733, 507.41503906250006]]}, {"title": "2.5 Overall Multi-task Objective", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 53.77850341796875], [210.9765625, 53.77850341796875], [210.9765625, 66.15447998046875], [38.29351535836177, 66.15447998046875]]}, {"title": "2.6 Optimization Strategy", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 169.518310546875], [178.75, 169.518310546875], [178.75, 181.894287109375], [38.29351535836177, 181.894287109375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 329.91748046875], [133.84765625, 329.91748046875], [133.84765625, 343.59619140625], [38.29351535836177, 343.59619140625]]}, {"title": "3.1 Comparison with State-of-the-Art Baselines", "heading_level": null, "page_id": 6, "polygon": [[52.55972696245733, 54.42987060546875], [301.2109375, 54.42987060546875], [301.2109375, 65.99163818359375], [52.55972696245733, 65.99163818359375]]}, {"title": "3.2 Few-Shot Performance", "heading_level": null, "page_id": 7, "polygon": [[38.29351535836177, 311.67919921875], [180.20477815699658, 311.67919921875], [180.20477815699658, 322.75244140625], [38.29351535836177, 322.75244140625]]}, {"title": "3.3 Comaprison with Pretraining-Finetuning Baselines", "heading_level": null, "page_id": 7, "polygon": [[38.29351535836177, 445.53515625], [321.40625, 445.53515625], [321.40625, 456.60839843750006], [38.29351535836177, 456.60839843750006]]}, {"title": "3.4 Ablation Studies", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 54.1448974609375], [164.677734375, 54.1448974609375], [164.677734375, 66.0323486328125], [52.55972696245733, 66.0323486328125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 308.259521484375], [138.90784982935153, 308.259521484375], [138.90784982935153, 321.286865234375], [52.55972696245733, 321.286865234375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[51.80887372013652, 508.06640625000006], [117.197265625, 508.06640625000006], [117.197265625, 521.**********], [51.80887372013652, 521.**********]]}, {"title": "Medical Image Synthesis\nvia Fine-Grained Image-Text Alignment\nand Anatomy-Pathology Prompting", "heading_level": null, "page_id": 11, "polygon": [[68.212890625, 51.946533203125], [357.9296875, 51.946533203125], [357.9296875, 102.9974365234375], [68.212890625, 102.9974365234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 11, "polygon": [[39.04436860068259, 534.2002249718786], [134.70703125, 534.2002249718786], [134.70703125, 546.82275390625], [39.04436860068259, 546.82275390625]]}, {"title": "2 Method", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 540.96044921875], [106.240234375, 540.96044921875], [106.240234375, 554.63916015625], [38.29351535836177, 554.63916015625]]}, {"title": "2.1 Anatomy-Pathology Prompting", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 562.78125], [224.296875, 562.78125], [224.296875, 575.**********], [38.29351535836177, 575.**********]]}, {"title": "2.2 Fine-Grained Alignment Based Synthesis Module", "heading_level": null, "page_id": 15, "polygon": [[38.29351535836177, 344.89892578125], [315.358361774744, 344.89892578125], [315.358361774744, 356.62353515625], [38.29351535836177, 356.62353515625]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 396.03125], [222.578125, 396.03125], [222.578125, 409.05859375], [52.55972696245733, 409.05859375]]}, {"title": "3.1 Experiment Setting", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 421.**********], [179.072265625, 421.**********], [179.072265625, 433.1591796875], [52.55972696245733, 433.1591796875]]}, {"title": "3.2 Comparison with State-of-the-Arts", "heading_level": null, "page_id": 17, "polygon": [[38.29351535836177, 549.1025390625], [242.34375, 549.1025390625], [242.34375, 561.478515625], [38.29351535836177, 561.478515625]]}, {"title": "3.3 Semantic Analysis", "heading_level": null, "page_id": 18, "polygon": [[52.55972696245733, 424.69140625], [171.94539249146757, 424.69140625], [171.94539249146757, 435.7646484375], [52.55972696245733, 435.7646484375]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 232.37524414062503], [125.25390624999999, 232.37524414062503], [125.25390624999999, 246.37**********03], [38.29351535836177, 246.37**********03]]}, {"title": "References", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 471.58984375000006], [103.7158203125, 471.58984375000006], [103.7158203125, 484.61718750000006], [38.29351535836177, 484.61718750000006]]}, {"title": "Multi-Dataset Multi-Task Learning\nfor COVID-19 Prognosis", "heading_level": null, "page_id": 22, "polygon": [[100.61433447098976, 50.76593017578125], [354.4921875, 50.76593017578125], [354.4921875, 83.6192626953125], [100.61433447098976, 83.6192626953125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 23, "polygon": [[39.04436860068259, 54.77052868391451], [134.40273037542661, 54.77052868391451], [134.40273037542661, 66.1951904296875], [39.04436860068259, 66.1951904296875]]}, {"title": "2 Methods", "heading_level": null, "page_id": 24, "polygon": [[52.55972696245733, 293.44091796875], [125.39249146757679, 293.44091796875], [125.39249146757679, 305.16552734375], [52.55972696245733, 305.16552734375]]}, {"title": "3 Experimental Setup", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 292.6096737907762], [191.46757679180885, 292.6096737907762], [191.46757679180885, 304.025634765625], [52.55972696245733, 304.025634765625]]}, {"title": "3.1 Datasets and Tasks", "heading_level": null, "page_id": 26, "polygon": [[52.55972696245733, 371.279296875], [176.4505119453925, 371.279296875], [176.4505119453925, 381.0498046875], [52.55972696245733, 381.0498046875]]}, {"title": "3.2 Experimental Configurations", "heading_level": null, "page_id": 27, "polygon": [[38.29351535836177, 305.81689453125], [210.98976109215016, 305.81689453125], [210.98976109215016, 316.564453125], [38.29351535836177, 316.564453125]]}, {"title": "4 Results and Discussions", "heading_level": null, "page_id": 28, "polygon": [[52.55972696245733, 455.4206974128234], [214.84375, 455.4206974128234], [214.84375, 467.68164062500006], [52.55972696245733, 467.68164062500006]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 29, "polygon": [[38.80615234375, 557.89599609375], [124.71679687499999, 557.89599609375], [124.71679687499999, 571.57470703125], [38.80615234375, 571.57470703125]]}, {"title": "References", "heading_level": null, "page_id": 30, "polygon": [[51.80887372013652, 411.6640625], [117.13310580204778, 411.6640625], [117.13310580204778, 424.**********], [51.80887372013652, 424.**********]]}, {"title": "PEMMA: Parameter-Efficient\nMulti-Modal Adaptation for Medical\nImage Segmentation", "heading_level": null, "page_id": 33, "polygon": [[79.814453125, 51.**********], [346.7578125, 51.**********], [346.7578125, 102.427490234375], [79.814453125, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 33, "polygon": [[38.**********, 520.1**********], [134.70703125, 520.1**********], [134.70703125, 533.14404296875], [38.**********, 533.14404296875]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 35, "polygon": [[38.29351535836177, 189.710693359375], [137.0703125, 189.710693359375], [137.0703125, 204.366455078125], [37.54266211604095, 204.366455078125]]}, {"title": "2.1 Standard Adaptation Methods", "heading_level": null, "page_id": 35, "polygon": [[38.29351535836177, 401.56787109375], [220.64453125, 401.56787109375], [220.64453125, 413.29248046875], [38.29351535836177, 413.29248046875]]}, {"title": "2.2 Proposed Adaptation Method: PEMMA", "heading_level": null, "page_id": 37, "polygon": [[38.29351535836177, 417.5263671875], [268.80546075085323, 417.5263671875], [268.80546075085323, 429.2509765625], [38.29351535836177, 429.2509765625]]}, {"title": "3 Experimental Set-up", "heading_level": null, "page_id": 39, "polygon": [[38.29351535836177, 380.72412109375], [183.046875, 380.72412109375], [183.046875, 395.05419921875], [38.29351535836177, 395.05419921875]]}, {"title": "3.1 Dataset Description", "heading_level": null, "page_id": 39, "polygon": [[38.29351535836177, 402.87060546875], [167.578125, 402.87060546875], [167.578125, 415.24658203125], [38.29351535836177, 415.24658203125]]}, {"title": "4 Results and Discussion", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 54.77052868391451], [210.009765625, 54.77052868391451], [210.009765625, 66.11376953125], [52.55972696245733, 66.11376953125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 40, "polygon": [[52.55972696245733, 409.65354330708664], [138.7890625, 409.65354330708664], [138.7890625, 421.**********], [52.55972696245733, 421.**********]]}, {"title": "References", "heading_level": null, "page_id": 41, "polygon": [[39.04436860068259, 96.4837646484375], [104.091796875, 96.4837646484375], [104.091796875, 109.9996337890625], [39.04436860068259, 109.9996337890625]]}, {"title": "Reducing Annotation Burden: Exploiting\nImage Knowledge for Few-Shot Medical\nVideo Object Segmentation\nvia Spatiotemporal Consistency\nRelearning", "heading_level": null, "page_id": 43, "polygon": [[64.57337883959045, 50.60308837890625], [360.9375, 50.60308837890625], [360.9375, 138.578369140625], [64.57337883959045, 138.578369140625]]}, {"title": "<sup>1</sup> MedAI Technology (Wuxi) Co. Ltd., Wuxi, China\n<EMAIL>", "heading_level": null, "page_id": 43, "polygon": [[106.62116040955631, 198.07424071991002], [318.3984375, 198.829833984375], [318.3984375, 221.953369140625], [106.62116040955631, 221.953369140625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 43, "polygon": [[38.779296875, 491.45654296875006], [134.40273037542661, 491.45654296875006], [134.40273037542661, 503.83251953125006], [38.779296875, 503.83251953125006]]}, {"title": "2 Method", "heading_level": null, "page_id": 45, "polygon": [[38.29351535836177, 345.55029296875], [106.62116040955631, 345.55029296875], [106.62116040955631, 360.53173828125], [38.29351535836177, 360.53173828125]]}, {"title": "2.1 Task Definition", "heading_level": null, "page_id": 45, "polygon": [[38.29351535836177, 371.9306640625], [142.66211604095562, 371.9306640625], [142.66211604095562, 384.306640625], [38.29351535836177, 384.306640625]]}, {"title": "2.2 Learning Few-Shot Segmentation with Images", "heading_level": null, "page_id": 45, "polygon": [[38.29351535836177, 541.9375], [298.0887372013652, 541.9375], [298.0887372013652, 554.3134765625], [38.29351535836177, 554.3134765625]]}, {"title": "2.3 Inference on Individual Video Frames", "heading_level": null, "page_id": 46, "polygon": [[52.55972696245733, 472.56689453125006], [269.84375, 472.56689453125006], [269.84375, 482.98876953125006], [52.55972696245733, 482.98876953125006]]}, {"title": "2.4 Spatiotemporal Consistency Relearning for Videos", "heading_level": null, "page_id": 47, "polygon": [[38.29351535836177, 55.52080989876266], [320.6143344709897, 55.52080989876266], [320.6143344709897, 66.0323486328125], [38.29351535836177, 66.0323486328125]]}, {"title": "3 Experiment", "heading_level": null, "page_id": 48, "polygon": [[51.80887372013652, 208.600341796875], [143.73046875, 208.600341796875], [143.73046875, 219.999267578125], [51.80887372013652, 219.999267578125]]}, {"title": "3.1 Experimental Setups", "heading_level": null, "page_id": 48, "polygon": [[52.55972696245733, 229.44409179687503], [185.1953125, 229.44409179687503], [185.1953125, 239.86596679687503], [52.55972696245733, 239.86596679687503]]}, {"title": "3.2 Results", "heading_level": null, "page_id": 49, "polygon": [[38.29351535836177, 405.150390625], [103.017578125, 405.150390625], [103.017578125, 416.2236328125], [38.29351535836177, 416.2236328125]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 50, "polygon": [[52.55972696245733, 54.9591064453125], [158.017578125, 54.9591064453125], [158.017578125, 66.276611328125], [52.55972696245733, 66.276611328125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 50, "polygon": [[52.55972696245733, 532.**********], [138.90784982935153, 532.**********], [138.90784982935153, 545.**********], [52.55972696245733, 545.**********]]}, {"title": "References", "heading_level": null, "page_id": 51, "polygon": [[39.04436860068259, 227.81567382812503], [103.447265625, 227.81567382812503], [103.447265625, 242.47143554687503], [39.04436860068259, 242.47143554687503]]}, {"title": "SBC-AL: Structure and Boundary\nConsistency-Based Active Learning\nfor Medical Image Segmentation", "heading_level": null, "page_id": 54, "polygon": [[104.306640625, 52.1907958984375], [351.9140625, 52.1907958984375], [351.9140625, 102.5089111328125], [104.306640625, 102.5089111328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 55, "polygon": [[39.04436860068259, 98.28683914510687], [134.40273037542661, 98.28683914510687], [134.40273037542661, 109.918212890625], [39.04436860068259, 109.918212890625]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 56, "polygon": [[51.80887372013652, 276.668212890625], [150.92150170648463, 276.668212890625], [150.92150170648463, 290.346923828125], [51.80887372013652, 290.346923828125]]}, {"title": "2.1 Segmentation Network", "heading_level": null, "page_id": 56, "polygon": [[51.80887372013652, 439.34716796875], [194.970703125, 439.34716796875], [194.970703125, 451.07177734375], [51.80887372013652, 451.07177734375]]}, {"title": "2.2 Uncertainty Evaluation and Consistency Score", "heading_level": null, "page_id": 57, "polygon": [[38.29351535836177, 436.09033203125], [300.13671875, 436.09033203125], [300.13671875, 447.81494140625], [38.29351535836177, 447.81494140625]]}, {"title": "2.3 Selective Annotation", "heading_level": null, "page_id": 59, "polygon": [[38.29351535836177, 264.78076171875], [171.19453924914674, 264.78076171875], [171.19453924914674, 275.20263671875], [38.29351535836177, 275.20263671875]]}, {"title": "2.4 Loss Function", "heading_level": null, "page_id": 59, "polygon": [[38.29351535836177, 410.361328125], [135.15358361774744, 410.361328125], [135.15358361774744, 420.783203125], [38.29351535836177, 420.783203125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 60, "polygon": [[52.55972696245733, 190.362060546875], [148.45703125, 190.362060546875], [148.45703125, 201.760986328125], [52.55972696245733, 201.760986328125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 62, "polygon": [[52.55972696245733, 482.4308211473566], [138.90784982935153, 482.4308211473566], [138.90784982935153, 493.73632812500006], [52.55972696245733, 493.73632812500006]]}, {"title": "References", "heading_level": null, "page_id": 63, "polygon": [[38.29351535836177, 93.7154541015625], [103.6083984375, 93.7154541015625], [103.6083984375, 106.9056396484375], [38.29351535836177, 106.9056396484375]]}, {"title": "Semi-Supervised Learning for Deep\nCausal Generative Models", "heading_level": null, "page_id": 65, "polygon": [[85.59726962457337, 51.620849609375], [339.453125, 51.620849609375], [339.453125, 84.840576171875], [85.59726962457337, 84.840576171875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 65, "polygon": [[39.04436860068259, 437.39306640625], [134.40273037542661, 437.39306640625], [134.40273037542661, 449.76904296875], [39.04436860068259, 449.76904296875]]}, {"title": "2 Background", "heading_level": null, "page_id": 66, "polygon": [[51.80887372013652, 428.92529296875], [144.16382252559725, 428.92529296875], [144.16382252559725, 440.64990234375], [51.80887372013652, 440.64990234375]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 67, "polygon": [[38.29351535836177, 474.52099609375006], [137.4061433447099, 474.52099609375006], [137.4061433447099, 487.54833984375006], [38.29351535836177, 487.54833984375006]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 69, "polygon": [[38.29351535836177, 478.75488281250006], [134.27734375, 478.75488281250006], [134.27734375, 491.78222656250006], [38.29351535836177, 491.78222656250006]]}, {"title": "4.1 Causal Analysis on Colour Morpho-MNIST", "heading_level": null, "page_id": 69, "polygon": [[38.29351535836177, 508.39208984375006], [285.52734375, 508.39208984375006], [285.52734375, 520.1**********], [38.29351535836177, 520.1**********]]}, {"title": "4.2 Counterfactuals for Medical Imaging Data", "heading_level": null, "page_id": 71, "polygon": [[38.29351535836177, 557.89599609375], [279.31740614334467, 557.89599609375], [279.31740614334467, 568.96923828125], [38.29351535836177, 568.96923828125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 73, "polygon": [[38.29351535836177, 52.**********], [124.64163822525596, 52.**********], [124.64163822525596, 66.76513671875], [38.29351535836177, 66.76513671875]]}, {"title": "References", "heading_level": null, "page_id": 73, "polygon": [[38.29351535836177, 337.408203125], [102.**********, 337.408203125], [102.**********, 351.73828125], [38.29351535836177, 351.73828125]]}, {"title": "SynCellFactory: Generative Data\nAugmentation for Cell Tracking", "heading_level": null, "page_id": 75, "polygon": [[96.10921501706484, 51.3765869140625], [329.35546875, 51.3765869140625], [329.35546875, 96.71868896484375], [96.10921501706484, 96.71868896484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 75, "polygon": [[39.04436860068259, 376.16455078125], [134.40273037542661, 376.16455078125], [134.40273037542661, 389.19189453125], [39.04436860068259, 389.19189453125]]}, {"title": "2 Method", "heading_level": null, "page_id": 76, "polygon": [[52.55972696245733, 471.26416015625006], [120.95703125, 471.26416015625006], [120.95703125, 484.94287109375006], [52.55972696245733, 484.94287109375006]]}, {"title": "2.1 Motion Model", "heading_level": null, "page_id": 77, "polygon": [[38.29351535836177, 410.68701171875], [138.1569965870307, 410.68701171875], [138.1569965870307, 423.71435546875], [38.29351535836177, 423.71435546875]]}, {"title": "2.2 ControlNet", "heading_level": null, "page_id": 77, "polygon": [[38.29351535836177, 568.31787109375], [123.13993174061433, 568.31787109375], [123.13993174061433, 581.34521484375], [38.29351535836177, 581.34521484375]]}, {"title": "2.3 SynCellFactory Inference", "heading_level": null, "page_id": 79, "polygon": [[38.29351535836177, 238.56323242187503], [192.71484375, 238.56323242187503], [192.71484375, 250.61352539062503], [38.29351535836177, 250.61352539062503]]}, {"title": "2.4 Segmentation Pseudo Ground Truth", "heading_level": null, "page_id": 79, "polygon": [[38.29351535836177, 360.857421875], [249.28327645051192, 360.857421875], [249.28327645051192, 373.2333984375], [38.29351535836177, 373.2333984375]]}, {"title": "2.5 Automated Training", "heading_level": null, "page_id": 79, "polygon": [[38.29351535836177, 542.5888671875], [168.65234375, 542.5888671875], [168.65234375, 554.96484375], [38.29351535836177, 554.96484375]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 80, "polygon": [[52.55972696245733, 130.761962890625], [222.578125, 130.761962890625], [222.578125, 143.789306640625], [52.55972696245733, 143.789306640625]]}, {"title": "3.1 Data Sets", "heading_level": null, "page_id": 80, "polygon": [[52.55972696245733, 154.69970703125], [129.1467576791809, 154.69970703125], [129.1467576791809, 166.261474609375], [52.55972696245733, 166.261474609375]]}, {"title": "3.2 Experimental Setup", "heading_level": null, "page_id": 80, "polygon": [[52.55972696245733, 322.4267578125], [180.8984375, 322.4267578125], [180.8984375, 333.82568359375], [52.55972696245733, 333.82568359375]]}, {"title": "3.3 Quantitative Results", "heading_level": null, "page_id": 82, "polygon": [[52.55972696245733, 54.0634765625], [183.95904436860067, 54.0634765625], [183.95904436860067, 65.7066650390625], [52.55972696245733, 65.7066650390625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 82, "polygon": [[52.55972696245733, 368.99951171875], [138.90784982935153, 368.99951171875], [138.90784982935153, 382.02685546875], [52.55972696245733, 382.02685546875]]}, {"title": "References", "heading_level": null, "page_id": 83, "polygon": [[38.29351535836177, 125.71386718750001], [103.76953125, 125.71386718750001], [103.76953125, 139.881103515625], [38.29351535836177, 139.881103515625]]}, {"title": "TAPoseNet: Teeth Alignment Based\non Pose Estimation via Multi-scale Graph\nConvolutional Network", "heading_level": null, "page_id": 85, "polygon": [[65.32423208191126, 51.3765869140625], [361.3671875, 51.3765869140625], [361.3671875, 102.5089111328125], [65.32423208191126, 102.5089111328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 85, "polygon": [[38.9404296875, 483.31445312500006], [134.40273037542661, 483.31445312500006], [134.40273037542661, 495.69042968750006], [38.9404296875, 495.69042968750006]]}, {"title": "2 Method", "heading_level": null, "page_id": 87, "polygon": [[38.29351535836177, 180.10302734375], [105.8703071672355, 180.10302734375], [105.8703071672355, 193.78173828125], [38.29351535836177, 193.78173828125]]}, {"title": "2.1 Teeth Features Extraction Module", "heading_level": null, "page_id": 88, "polygon": [[51.80887372013652, 121.47998046875001], [254.37499999999997, 121.47998046875001], [254.37499999999997, 132.064697265625], [51.80887372013652, 132.064697265625]]}, {"title": "2.2 Post-orthodontic Teeth Alignment Target Prediction Module", "heading_level": null, "page_id": 89, "polygon": [[38.29351535836177, 54.26702880859375], [373.9249146757679, 54.26702880859375], [373.9249146757679, 65.66595458984375], [38.29351535836177, 65.66595458984375]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 90, "polygon": [[51.80887372013652, 54.18560791015625], [222.36328125, 54.18560791015625], [222.36328125, 66.39874267578125], [51.80887372013652, 66.39874267578125]]}, {"title": "3.1 Dataset", "heading_level": null, "page_id": 90, "polygon": [[52.55972696245733, 79.79248046875], [119.0234375, 79.79248046875], [119.0234375, 90.21435546875], [52.55972696245733, 90.21435546875]]}, {"title": "3.2 Implementation and Evaluation Methods", "heading_level": null, "page_id": 90, "polygon": [[52.55972696245733, 332.52294921875], [287.4609375, 332.52294921875], [287.4609375, 343.59619140625], [52.55972696245733, 343.59619140625]]}, {"title": "3.3 Results", "heading_level": null, "page_id": 91, "polygon": [[38.29351535836177, 107.964111328125], [103.447265625, 107.964111328125], [103.447265625, 120.34008789062501], [38.29351535836177, 120.34008789062501]]}, {"title": "4 Discussions and Conclusions", "heading_level": null, "page_id": 93, "polygon": [[38.29351535836177, 53.20855712890625], [227.50853242320818, 53.20855712890625], [227.50853242320818, 66.31732177734375], [38.29351535836177, 66.31732177734375]]}, {"title": "References", "heading_level": null, "page_id": 93, "polygon": [[39.04436860068259, 272.271484375], [103.876953125, 272.271484375], [103.876953125, 286.27587890625], [39.04436860068259, 286.27587890625]]}, {"title": "TE-SSL: Time and Event-Aware Self\nSupervised Learning for Alzheimer's\nDisease Progression Analysis", "heading_level": null, "page_id": 95, "polygon": [[83.251953125, 51.01019287109375], [343.3203125, 51.01019287109375], [343.3203125, 102.59033203125], [83.251953125, 102.59033203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 96, "polygon": [[52.55972696245733, 54.77052868391451], [148.779296875, 54.77052868391451], [148.779296875, 66.39874267578125], [52.55972696245733, 66.39874267578125]]}, {"title": "2 Methods", "heading_level": null, "page_id": 97, "polygon": [[38.29351535836177, 333.5], [112.041015625, 333.5], [112.041015625, 346.52734375], [38.29351535836177, 346.52734375]]}, {"title": "2.1 TE-SSL: Time and Event-Aware Self supervised Learning", "heading_level": null, "page_id": 97, "polygon": [[38.779296875, 577.43701171875], [355.1535836177474, 577.43701171875], [355.1535836177474, 588.51025390625], [38.779296875, 588.51025390625]]}, {"title": "2.2 Time-To-Event Prediction", "heading_level": null, "page_id": 99, "polygon": [[38.29351535836177, 426.91001124859395], [196.7235494880546, 426.91001124859395], [196.7235494880546, 437.0673828125], [38.29351535836177, 437.0673828125]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 100, "polygon": [[52.55972696245733, 307.77099609375], [221.50390625, 307.77099609375], [221.50390625, 319.49560546875], [52.55972696245733, 319.49560546875]]}, {"title": "3.1 Results", "heading_level": null, "page_id": 101, "polygon": [[39.04436860068259, 490.80517578125006], [102.8668941979522, 490.80517578125006], [102.8668941979522, 502.52978515625006], [39.04436860068259, 502.52978515625006]]}, {"title": "3.2 Ablation Analysis", "heading_level": null, "page_id": 102, "polygon": [[52.55972696245733, 323.3712035995501], [169.6928327645051, 323.3712035995501], [169.6928327645051, 333.82568359375], [52.55972696245733, 333.82568359375]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 102, "polygon": [[52.55972696245733, 506.4398200224972], [138.1569965870307, 506.4398200224972], [138.1569965870307, 517.8369140625], [52.55972696245733, 517.8369140625]]}, {"title": "References", "heading_level": null, "page_id": 103, "polygon": [[39.04436860068259, 349.7841796875], [103.5546875, 349.7841796875], [103.5546875, 363.462890625], [39.04436860068259, 363.462890625]]}, {"title": "Training ViT with Limited Data\nfor Alzheimer's Disease Classification:\nAn Empirical Study", "heading_level": null, "page_id": 105, "polygon": [[78.310546875, 51.78369140625], [345.8984375, 51.78369140625], [345.8984375, 103.1602783203125], [78.310546875, 103.1602783203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 105, "polygon": [[39.04436860068259, 448.46630859375], [134.40273037542661, 448.46630859375], [134.40273037542661, 460.84228515625006], [39.04436860068259, 460.84228515625006]]}, {"title": "2 Method", "heading_level": null, "page_id": 106, "polygon": [[51.80887372013652, 425.3427734375], [120.3125, 425.3427734375], [120.3125, 438.3701171875], [51.80887372013652, 438.3701171875]]}, {"title": "2.1 Training Strategies", "heading_level": null, "page_id": 106, "polygon": [[52.55972696245733, 450.42041015625], [175.7421875, 450.42041015625], [175.7421875, 461.49365234375006], [52.55972696245733, 461.49365234375006]]}, {"title": "2.2 Datasets", "heading_level": null, "page_id": 108, "polygon": [[52.55972696245733, 399.1496062992126], [123.13993174061433, 399.1496062992126], [123.13993174061433, 409.7099609375], [52.55972696245733, 409.7099609375]]}, {"title": "2.3 Experimental Setup", "heading_level": null, "page_id": 109, "polygon": [[38.29351535836177, 238.72607421875003], [166.2890625, 238.72607421875003], [166.2890625, 249.79931640625003], [38.29351535836177, 249.79931640625003]]}, {"title": "3 Results and Discussion", "heading_level": null, "page_id": 109, "polygon": [[38.88671875, 486.89697265625006], [195.72265625, 486.89697265625006], [195.72265625, 498.62158203125006], [38.88671875, 498.62158203125006]]}, {"title": "3.1 Pre-training Findings", "heading_level": null, "page_id": 109, "polygon": [[38.29351535836177, 575.80859375], [174.19795221843003, 575.80859375], [174.19795221843003, 586.23046875], [38.29351535836177, 586.23046875]]}, {"title": "3.2 Ablation Study", "heading_level": null, "page_id": 111, "polygon": [[38.29351535836177, 287.2529296875], [142.66211604095562, 287.2529296875], [142.66211604095562, 298.00048828125], [38.29351535836177, 298.00048828125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 112, "polygon": [[52.55972696245733, 253.87036132812503], [138.90784982935153, 253.87036132812503], [138.90784982935153, 265.594970703125], [52.55972696245733, 265.594970703125]]}, {"title": "References", "heading_level": null, "page_id": 113, "polygon": [[38.8330078125, 53.08642578125], [102.**********, 53.08642578125], [102.**********, 66.8465576171875], [38.8330078125, 66.8465576171875]]}, {"title": "TrIND: Representing Anatomical Trees\nby Denoising Diffusion of Implicit Neural\nFields", "heading_level": null, "page_id": 115, "polygon": [[65.32423208191126, 52.109375], [361.3671875, 52.109375], [361.3671875, 102.59033203125], [65.32423208191126, 102.59033203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 115, "polygon": [[39.04436860068259, 461.16796875000006], [134.40273037542661, 461.16796875000006], [134.40273037542661, 473.54394531250006], [39.04436860068259, 473.54394531250006]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 117, "polygon": [[38.29351535836177, 306.956787109375], [137.4061433447099, 306.956787109375], [137.4061433447099, 319.984130859375], [38.29351535836177, 319.984130859375]]}, {"title": "3 Datasets, Results, and Implementation Details", "heading_level": null, "page_id": 118, "polygon": [[52.55972696245733, 533.14404296875], [349.765625, 533.14404296875], [349.765625, 544.86865234375], [52.55972696245733, 544.86865234375]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 123, "polygon": [[38.29351535836177, 105.4400634765625], [125.39249146757679, 105.4400634765625], [125.39249146757679, 118.793**********1], [38.29351535836177, 118.793**********1]]}, {"title": "References", "heading_level": null, "page_id": 123, "polygon": [[39.04436860068259, 352.3896484375], [103.33984375, 352.3896484375], [103.33984375, 366.7197265625], [39.04436860068259, 366.7197265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 39], ["Text", 6], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 27059, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 43], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 62], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 39], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 57], ["TextInlineMath", 4], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 44], ["SectionHeader", 3], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 284], ["Span", 101], ["Line", 33], ["Text", 4], ["Reference", 4], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 7654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 114], ["Span", 111], ["Line", 35], ["Text", 5], ["Reference", 5], ["Table", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 12497, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 40], ["SectionHeader", 3], ["Text", 3], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 50], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 39], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 44], ["Text", 8], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 46], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Line", 72], ["Span", 69], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 71], ["Line", 29], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 49], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Code", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 42], ["TextInlineMath", 3], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["TableCell", 128], ["Line", 53], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 10477, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 42], ["Reference", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 50], ["ListItem", 18], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 46], ["Line", 20], ["ListItem", 7], ["Reference", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 41], ["Text", 5], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 46], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 37], ["Text", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 660, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 48], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 48], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 48], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["TableCell", 135], ["Line", 38], ["Text", 4], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 45], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 44], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 51], ["ListItem", 20], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 24], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 568, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 44], ["ListItem", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 42], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 662, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["TableCell", 50], ["Line", 42], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3938, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["Line", 43], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["TableCell", 204], ["Line", 46], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 43], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 47], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 9], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 39], ["Text", 5], ["SectionHeader", 3], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 47], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 40], ["SectionHeader", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 39], ["Text", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 58], ["Text", 8], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 43], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["TableCell", 212], ["Line", 49], ["Caption", 3], ["Table", 3], ["Reference", 3], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 14749, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 55], ["Line", 30], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 46], ["ListItem", 11], ["Reference", 11], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 52], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 20], ["Line", 10], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 43], ["Text", 6], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 44], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 41], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 51], ["Equation", 6], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 52], ["TextInlineMath", 4], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 37], ["TextInlineMath", 2], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 1338], ["TableCell", 308], ["Line", 45], ["Table", 3], ["Reference", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 15185, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["TableCell", 98], ["Line", 38], ["Caption", 3], ["Text", 3], ["Reference", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 7376, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 49], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["Line", 25], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 38], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1161, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 40], ["ListItem", 4], ["Text", 3], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 38], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 717], ["Line", 51], ["TextInlineMath", 6], ["Equation", 6], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 39], ["Text", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 54], ["Caption", 3], ["Figure", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 817], ["TableCell", 631], ["Line", 61], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6683, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 36], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 44], ["ListItem", 7], ["Reference", 7], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 41], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 39], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 43], ["ListItem", 5], ["Text", 3], ["TextInlineMath", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["Line", 36], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 38], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 39], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 40], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 109], ["TableCell", 88], ["Reference", 3], ["Table", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9318, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 42], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 48], ["ListItem", 15], ["Reference", 15], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 48], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 40], ["Text", 8], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 573, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 45], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 53], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 41], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 43], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["TableCell", 38], ["Line", 34], ["Text", 2], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Table", 1], ["Picture", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 35], ["TableCell", 30], ["Text", 2], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2992, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 43], ["ListItem", 9], ["Reference", 9], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 30], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 41], ["Text", 6], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 572, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 45], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 52], ["ListItem", 3], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 332], ["Line", 51], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 51], ["Text", 4], ["Equation", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 44], ["TextInlineMath", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 39], ["TableCell", 30], ["Caption", 2], ["TextInlineMath", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["TableCell", 56], ["Line", 38], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 46], ["ListItem", 7], ["Reference", 7], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 49], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 43], ["Text", 5], ["ListItem", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 52], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["TableCell", 60], ["Line", 37], ["Text", 3], ["Reference", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1242, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 40], ["TableCell", 25], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 110, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 111, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 55], ["Text", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 112, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 41], ["TableCell", 40], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 113, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 50], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 114, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 50], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 115, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 39], ["Text", 5], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 560, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 116, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 43], ["TextInlineMath", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 678, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 117, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 49], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 699, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 118, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 49], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 119, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 30], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1541, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 120, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["TableCell", 156], ["Line", 43], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 711, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 121, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 68], ["TableCell", 49], ["Reference", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 122, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 27], ["Picture", 2], ["Caption", 2], ["Text", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1241, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 123, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 43], ["ListItem", 9], ["Reference", 9], ["TextInlineMath", 2], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 124, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 51], ["ListItem", 23], ["Reference", 23], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 125, "text_extraction_method": "pdftext", "block_counts": [["Span", 69], ["Line", 24], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-3"}