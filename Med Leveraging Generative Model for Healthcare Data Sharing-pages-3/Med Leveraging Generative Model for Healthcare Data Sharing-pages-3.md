Image /page/0/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a gray bookmark shape inside. The bookmark shape is a rectangle with a curved top and a tab sticking out from the left side. The text below the icon reads "Check for updates" in gray capital letters.

# **MedContext: Learning Contextual Cues for Efficient Volumetric Medical Segmentation**

<PERSON><PERSON><sup>1( $\boxtimes$ )</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>1,2</sup>, and <PERSON><PERSON><sup>1,3</sup>

<sup>1</sup> <PERSON> University of Artificial Intelligence (MBZUAI), Abu Dhabi, United Arab Emirates

*{*hanan.ghani,muzammal.naseer,fahad.khan,salman.khan*}*@mbzuai.ac.ae <sup>2</sup> Link¨oping University, Link¨oping, Sweden

<sup>3</sup> Australian National University, Canberra, Australia

Abstract. Deep neural networks have significantly improved volumetric medical segmentation, but they generally require large-scale annotated data to achieve better performance, which can be expensive and prohibitive to obtain. To address this limitation, existing works typically perform transfer learning or design dedicated pretraining-finetuning stages to learn representative features. However, the mismatch between the source and target domain can make it challenging to learn optimal representation for volumetric data, while the multi-stage training demands higher compute as well as careful selection of stage-specific design choices. In contrast, we propose a universal training framework called MedContext that is architecture-agnostic and can be incorporated into any existing training framework for 3D medical segmentation. Our approach effectively learns self-supervised contextual cues jointly with the supervised voxel segmentation task without requiring large-scale annotated volumetric medical data or dedicated pretraining-finetuning stages. The proposed approach induces contextual knowledge in the network by learning to reconstruct the missing organ or parts of an organ in the output segmentation space. The effectiveness of MedContext is validated across multiple 3D medical datasets and four state-of-the-art model architectures. Our approach demonstrates consistent gains in segmentation performance across datasets and architectures even in fewshot scenarios. Our code is available at [https://github.com/hananshafi/](https://github.com/hananshafi/medcontext) [medcontext.](https://github.com/hananshafi/medcontext)

**Keywords:** Volumetric medical segmentation · Masked image modeling · Knowledge distillation

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_22) 22.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 229–239, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_22)\_22

# **1 Introduction**

Deep neural networks have greatly improved volumetric medical segmentation. The convolutional encoder-decoder networks, U-NET [\[9](#page-9-0),[26\]](#page-10-0), as well as the development of vision transformers  $[12]$  $[12]$ , has led to hybrid architectures  $[4,20]$  $[4,20]$  $[4,20]$  with complementary strengths of self-attention and convolution for medical segmentation. Despite the architectural advances, deep neural networks generally require large-scale annotated data to achieve better performance. However, collecting and annotating medical images at a large scale can be expensive and prohibitive due to privacy concerns.

Image /page/1/Figure/3 description: The image displays a comparison of segmentation results for medical images. On the left, a grid of images shows abdominal CT scans with different organs segmented. The columns are labeled 'Input', 'Ground Truth', 'nnFormer', and 'nnFormer + MedContext'. Each row presents a different slice of the abdomen. A legend below indicates the color coding for various organs: Spleen (purple), R-Kid (blue), L-Kid (green), Gal (orange), Eso (orange), Liv (green), Sto (yellow), Aor (pink), ICV (purple), PSV (red), Pan (cyan), Rad. (purple), Lad. (yellow). On the right, a similar grid shows cardiac MRI scans with heart chambers segmented. The columns are labeled 'Input', 'Ground Truth', 'nnFormer', and 'nnFormer + MedContext'. Each row shows a different view of the heart. A legend below indicates the color coding for heart components: RV cavity (yellow), Myocardium (green), LV cavity (blue). Red boxes highlight specific areas of interest in some of the 'nnFormer' segmentation results, indicating potential discrepancies or areas for improvement.

<span id="page-1-0"></span>**Fig. 1.** Qualitative Comparison between the baseline nnFormer and our proposed Med-Context integrated with nnFormer. The examples display different abdominal organs (Synapse) (**Left**) and regions of the heart (ACDC) (**Right**), with their labels in the legend below. The baseline nnFormer struggles to accurately segment the organs and heart regions, giving false segmentation results highlighted in red boxes. Best viewed zoomed in. Refer to supplementary material for additional qualitative comparisons.

To deal with the data scarcity, weights learned on ImageNet [\[10\]](#page-9-3) can be used to initialize the encoder, however, pre-training on 2D natural images may not capture the contextual information essential to understanding 3D medical images. Recent studies [\[15](#page-9-4),[17,](#page-9-5)[28\]](#page-10-2) explore self-supervised pre-training on extra auxiliary medical data, but this approach has two limitations: a) it involves a computationally expensive two-stage pre-training process on auxiliary data followed by fine-tuning on target data, and b) the success of fine-tuning depends on how well the auxiliary data distribution matches the target data. Moreover, there may not be a direct relationship between the self-supervised objectives and voxel-wise segmentation. Therefore jointly optimizing such self-supervised losses with 3D segmentation is non-trivial.

To address these limitations, we propose a generic training framework dubbed *MedContext* to learn self-supervised contextual cues jointly with supervised voxel segmentation without requiring large-scale annotated volumetric medical data.

Our approach involves reconstructing masked organs or organ parts in the output segmentation space. This reconstruction aligns well with the voxel-wise prediction task, enabling joint optimization of both tasks. To further reduce the disparity between the two tasks, we deploy a student-teacher distillation strategy [\[5](#page-9-6)[,19](#page-10-3)[,25](#page-10-4)] to guide reconstruction from a slow-moving online teacher model which also helps avoid representation collapse. Predicting the representation of an input from a representation of another input leads to versatile visual representations [\[2\]](#page-8-0). MedContext encourages contextual learning within the model and allows it to learn local-global relationships between different input components. This leads to better segmentation of organ boundaries (see Fig. [1\)](#page-1-0).

Our proposed approach is architecture-agnostic and can be incorporated into any training framework, making it universally applicable. We integrate our approach into three recent state-of-the-art medical 3D transformer based architectures: UNETR [\[16](#page-9-7)], SwinUNETR [\[15](#page-9-4)] and nnFormer [\[30\]](#page-10-5); and one CNN based 3D architecture PCRLv2 [\[31\]](#page-10-6). Using these architectures, we validate our approach across three medical imaging datasets: Multi-organ Synapse [\[21\]](#page-10-7), ACDC [\[3\]](#page-9-8) and BraTS [\[1](#page-8-1)[,22](#page-10-8)]. Our evaluation reveals consistent performance improvements across all compared methods. In summary, our contributions are three-fold: (1) We propose a universal training framework to jointly optimize supervised segmentation and self-supervised segmentation reconstruction via student-teacher knowledge distillation. (2) Our approach induces contextual knowledge in the model by learning to reconstruct the missing organ or organ parts in the output segmentation space. (3) We validate the effectiveness of our approach across multiple 3D medical datasets and state-of-the-art model architectures.

Image /page/2/Figure/3 description: This diagram illustrates a self-supervised learning framework for medical image segmentation. The input medical image X undergoes a masking function g(.) to create a masked version X^M. This masked input, along with the original input X, is fed into a 'Student' network F\_theta\_s, which consists of an encoder and a decoder with skip connections. The Student network produces two sets of predictions: F\_s (from the original input) and F\_s^M (from the masked input). Simultaneously, the original input X is fed into a 'Teacher' network F\_theta\_t, which is an Exponential Moving Average (EMA) of the Student network. The Teacher network's predictions, F\_t, are used to compute a 'Masked Reconstruction Loss' with the Student's masked predictions F\_s^M. The Student's full predictions F\_s are used to compute a 'Soft Dice loss' with the ground truth Y. The 'Overall Loss' is a combination of the Masked Reconstruction Loss and the Soft Dice loss. The Teacher network's gradients are stopped to prevent direct learning from the Teacher.

<span id="page-2-0"></span>**Fig. 2.** *Overview of our MedContext approach:* The original 3D volume is masked and fed to the student model (top-row) along with the original input. The teacher model (bottom-row) is only fed with the original volume. The difference between the semantic voxelwise predictions for the masked and original inputs corresponding to the student and teacher networks respectively is minimized to guide the reconstruction of masked regions in the output segmentation space.

## **2 Methodology**

### **2.1 Architecture**

Our approach is complementary and can be applied to the existing encoderdecoder architectures designed for 3D medical image segmentation. As shown in Fig. [2](#page-2-0) our design includes a student  $\mathcal{F}_s$  and a teacher network  $\mathcal{F}_t$  that operate on the input volume  $\mathbf{X} \in \mathbb{R}^{H \times W \times D}$  and its masked version  $\mathbf{X}^M \in \mathbb{R}^{H \times W \times D}$ generated using the masking function  $g(.)$ . Here, H, W, and D represent the height, width, and depth of the 3D volume, respectively. During the training phase, the input views are fed to the student-teacher framework as 3D patches, generating voxel-wise semantic logits for each input view. The student network is provided with both the masked  $(X^M)$  and unmasked  $(X)$  inputs, and the corresponding output voxel-wise semantic logits are denoted as  $\mathbf{F}_s$  and  $\mathbf{F}_s^M$ , respectively. On the other hand, the teacher network is provided with the original unmasked input  $\boldsymbol{X}$  which outputs voxel-wise semantic logits denoted as  $\boldsymbol{F}_t$ . The feature map produced at intermediate layers of the 3D architecture has a shape of  $\frac{H}{P_1} \times \frac{W}{P_2} \times \frac{D}{P_3} \times C$ , where  $(P_1, P_2, P_3)$  is the resolution of each patch and C is the feature dimension. For each output prediction from the student and C is the feature dimension. For each output prediction from the student,<br>a supervised loss is computed using the ground truth label  $\bf{Y}$  as shown in the a supervised loss is computed using the ground truth label *Y* , as shown in the figure. Additionally, a self-supervised objective is minimized between the masked student logits  $\mathbf{F}_s^M$  and the teacher logits  $\mathbf{F}_t$ . Finally, both the supervised and selfsupervised objectives are jointly optimized during single-stage training process.

### **2.2 Volumetric Masking Strategy**

To model contextual relationships, we employ a masking technique on the patch tokens of the original input *X* to reconstruct missing parts in the segmentation space. We ensure mask consistency across the depth to prevent information leakage from neighboring cubes by applying the same mask to all subsequent slices in the volume as shown in Fig. [2.](#page-2-0) To generate a masked view  $X^{\overline{M}}$ , we randomly mask a certain fraction  $\delta$  of the patch tokens. Following [\[11\]](#page-9-9), the masked tokens are replaced with learnable tokens  $\mathcal{H}_{\xi}$ , such that,

$$
\mathbf{X}^{M} = g(\mathbf{X}, \delta) = \mathbf{X} \circ (1 - \mathbf{I}_{\delta}) + \mathcal{H}_{\xi} \circ \mathbf{I}_{\delta}, \tag{1}
$$

where  $I_\delta$  is a binary mask generated according to a Bernoulli distribution using  $g(.)$ , i.e.,  $I_\delta \sim \text{Bernoulli}(\delta)$  and  $\circ$  denotes the element-wise product.

<span id="page-3-0"></span>

### **2.3 Voxel-Wise Segmentation Reconstruction**

We utilize masked input to reconstruct segmentation maps, facilitating the learning of contextual semantic relationships. To achieve this, we employ a studentteacher strategy where teacher weights are updated by a moving average of the student weights. Leveraging cumulative knowledge from prior weight updates enhances masked view reconstruction and induces enriched contextual cues. The teacher network provides soft semantic targets, guiding student network training. Both the student model  $\mathcal{F}_s$  and teacher model  $\mathcal{F}_t$  begin with the same randomly initialized weight parameters. The student network processes both original and masked inputs, while the teacher network only receives the original non-masked input. The networks generate voxel-wise semantic logits, represented by  $\{F_s^M,$  $F_s$  and  $F_t$  respectively. Subsequently, we reconstruct semantic voxel-wise logits of the masked input from the student model, guided by two supervised signals: *supervision through knowledge distillation* and *ground truth labels*.

**Reconstruction Through Knowledge Distillation:** A self-supervised distillation loss (Eq. [2\)](#page-4-0) is used to guide the training of the student network to encourage modeling the contextual consistency. It minimizes the difference between the voxel-wise logits  $\mathbf{F}_t$  generated by the teacher network given the original input *X* and the voxel-wise logits  $F_s^M$  produced by the student network using the masked input  $X^M$ . The objective function, referred to as Consistency Loss (CL), is denoted as  $\mathcal{L}_c(\mathbf{F}_s^M, \mathbf{F}_t)$  and is expressed as,

<span id="page-4-0"></span>
$$
\mathcal{L}_c(\mathbf{F}_s^M, \mathbf{F}_t) = \frac{\| \mathbf{F}_s^M - \mathbf{F}_t \|_2^2}{\| \mathbf{F}_t \|_2^2}.
$$
 (2)

**Reconstruction Through Ground Truth Labels:** The voxel-wise semantic logits  $\mathbf{F}_s^M$  output by the student for  $\mathbf{X}^M$  are further reconstructed using the ground truth labels. This is achieved by minimizing the soft dice loss [\[23\]](#page-10-9) using the ground truth labels  $\boldsymbol{Y}$ . The general expression for Dice-CE Loss for some arbitrary output prediction  $\boldsymbol{F}$  is given as,

<span id="page-4-1"></span>
$$
\mathcal{L}_{Dice-CE}(\boldsymbol{Y}, \boldsymbol{F}) = 1 - \sum_{c=1}^{C} \left( \frac{2 * \sum_{v=1}^{V} \boldsymbol{Y}_{v,c} \cdot \boldsymbol{F}_{v,c}}{\sum_{v=1}^{V} \boldsymbol{Y}_{v,c}^{2} + \sum_{v=1}^{V} \boldsymbol{F}_{v,c}^{2}} + \sum_{v=1}^{V} \boldsymbol{Y}_{v,c} \log \boldsymbol{F}_{v,c} \right), (3)
$$

where, C denotes the number of classes; V denotes the number of voxels;  $Y_{v,i}$ and  $\mathbf{F}_{v,i}$  denote the ground truths and output probabilities for class i at voxel v, respectively. In our case, the supervised reconstruction objective is calculated using above Dice-CE loss between the ground truth label  $Y$  and voxel-wise semantic logits  $F_s^M$  and is denoted as  $\mathcal{L}_{Dice-CE}(Y, F_s^M)$  and referred to as Masked Student Loss (MSL). Both CL and MSL encourage the network to can-Masked Student Loss (MSL). Both CL and MSL encourage the network to capture intricate relationships between various organs.

<span id="page-4-2"></span>

### **2.4 Supervised Voxel-Wise Segmentation**

Our primary task of supervised voxel-wise segmentation takes place in conjunction with the voxel-wise segmentation reconstruction as discussed above. For the supervised voxel-wise segmentation, we optimize the predictions of the of the student network  $\mathbf{F}_s$  on  $\mathbf{X}$  through the supervision of the ground truth labels *Y* using Soft Dice Loss (Eq. [3\)](#page-4-1) denoted by the objective  $\mathcal{L}_{Dice-CE}(\boldsymbol{Y}, \boldsymbol{F_s})$ .

### **2.5 Overall Multi-task Objective**

Our framework leverages a combination of supervised and self-supervised losses for optimization, synergistically reinforcing each other to offer complementary advantages. The overall loss objective  $\mathcal L$  is defined as,

$$
\mathcal{L} = \mathcal{L}_{Dice-CE}(\boldsymbol{Y}, \boldsymbol{F_s}) + \mathcal{L}_{Dice-CE}(\boldsymbol{Y}, \boldsymbol{F_s^M}) + \beta \mathcal{L}_c(\boldsymbol{F_s^M}, \boldsymbol{F_t}),
$$
(4)

where the hyperparameter  $\beta$  controls the contribution of self-supervised consistency loss during optimization.

### **2.6 Optimization Strategy**

Following a typical student-teacher optimization strategy as ultilized by [\[5,](#page-9-6)[13\]](#page-9-10), the gradient of the total loss is backpropagated through the student network and parameters are updated as:  $\Theta \leftarrow \Theta - \alpha \cdot \nabla_{\Theta}(\mathcal{L})$ , where  $\Theta$  represents the joint parameters of student network  $(\theta_s)$  and learnable mask embeddings  $(\xi)$ i.e.  $\Theta = \{\theta_s; \xi\}$ . The teacher network is updated via exponential moving average (EMA) of the weights of the student network using:  $\theta_t \leftarrow \lambda \theta_t + (1 - \lambda)\theta_s$ , where  $\theta_t$ denote the parameters of teacher and  $\lambda$  follows the cosine schedule from 0.996 to 1 during training. The gradient step through the student network comprises of the contributions from both the supervised and self-supervised objectives, thereby aiding in the reconstruction of the masked input by updating the differentiable volumetric embeddings associated with the masked regions.

## **3 Experiments**

**Datasets:** We evaluate on three volumetric medical datasets. **Synapse BTCV**: The synapse BTCV dataset [\[21\]](#page-10-7) for multi-organ CT Segmentation, includes abdominal CT scans of 30 subjects. We adopt the dataset split of [\[6](#page-9-11)] with 18 train and 12 test samples. We evaluate the performance on eight abdominal organs. **ACDC:** The ACDC dataset [\[3\]](#page-9-8) is a collection of cardiac MRI images and associated segmentation annotations for the right ventricle (RV), left ventricle (LV), and myocardium (MYO) of 100 patients. We split the dataset into 80 training and 20 testing samples following [\[30\]](#page-10-5). **BraTS:** We use two versions of BraTS dataset: BraTS17 [\[22](#page-10-8)] and BraTS21 [\[1\]](#page-8-1). For *UNETR, SwinUNTER* and *PCRLv2* we report results on the BraTS21 dataset to be consistent with the baseline. The BraTS21 dataset includes 1251 subjects with annotations for three sub-regions: Whole Tumor (WT), Tumor Core (TC), and Enhancing Tumor (ET). Following [\[15](#page-9-4)], we train on 1000 subjects and test on 251 subjects. For *nnFormer*, we use BraTS17 dataset comprising of 484 MRI images. Following [\[30\]](#page-10-5), we train on 387 samples and test on 73 cases. **Evaluation Metrics:** To evaluate the models' performance, we utilize two metrics: the Dice Similarity Score (DSC) and the 95% Hausdorff Distance (HD95). **Training and Implementation details:** Our approach utilizes Pytorch version 1.10.1 in conjunction with MONAI libraries [\[24](#page-10-10)] for implementation. Specifically, we use an input size of  $128 \times 128 \times 64$  for all datasets when training with *nnFormer*, and  $96 \times 96 \times 96$  for *UNETR*, *Swin-UNTER* and *PCRLv2*. All models are trained on a single A100 40GB GPU.

### **3.1 Comparison with State-of-the-Art Baselines**

**Synapse BTCV Dataset**: Table [1](#page-6-0) shows the results on the synapse multi-organ dataset. UNETR with our approach achieves 2.5% higher Dice Score (81.13%) than the baseline  $(78.76\%)$ , and over  $1\%$  reduction in HD95 score. With hierarchical SwinUNETR. Dice Score increases by  $>1\%$  (80.66% to 82.00%) and HD95 improves. Similar trend is observed with nnFormer. **ACDC Dataset**: Table 2 presents results on the larger ACDC dataset. UNETR with our approach outperforms the baseline by about  $4\%$  in Dice score (80.60% vs. 76.67%). SwinUNETR shows over 2.5% Dice score improvement, and nnFormer achieves a Dice score of 90.73% compared to the baseline's 90.50%. Per organ dice scores are also higher with our approach in each case. **BraTS Dataset**: Table 3 demonstrates that UNETR yields a 0.54% increase in the overall DSC compared to baseline. SwinUNTER achieves a DSC of 89.83%, surpassing the baseline DSC 89.44%. Additionally, nnFormer exhibits an improvement in the overall DSC (72.78%) compared to the baseline DSC (72.36%). Overall we show that our approach achieves gains even on larger datasets such as BraTS, but has more pronounced improvement for the low-data setups. See further results in Supple-mentary (Table [2](#page-6-1) and [3\)](#page-6-2).

<span id="page-6-0"></span>**Table 1.** Abdominal multi-organ Synapse: Our MedContext consistently improves the segmentation performance of all organs across different models. We observe significant improvements in HD95 along with the dice score (DSC). Best results in bold.

| Models            | MedContext | Spleen | Right Kidney | Left Kidney | Gallbladder | Liver | Stomach | Aorta | Pancreas | Average      |              |
|-------------------|------------|--------|--------------|-------------|-------------|-------|---------|-------|----------|--------------|--------------|
|                   |            |        |              |             |             |       |         |       |          | HD95<br>↓    | DSC<br>↑     |
| <b>UNETR</b>      | <br>       | 89.64  | 83.02        | 84.86       | 63.06       | 95.58 | 73.06   | 87.47 | 53.40    | 11.04        | 78.76        |
|                   | ✓          | 90.73  | 83.36        | 86.03       | 67.94       | 95.59 | 78.62   | 87.30 | 59.51    | <b>9.44</b>  | <b>81.13</b> |
| <b>Swin-UNETR</b> | <br>       | 86.33  | 80.63        | 84.07       | 67.24       | 94.98 | 74.97   | 90.53 | 66.49    | 20.32        | 80.66        |
|                   | ✓          | 91.45  | 80.80        | 84.85       | 67.70       | 94.60 | 76.20   | 90.88 | 67.74    | <b>14.45</b> | <b>82.00</b> |
| <b>nnFormer</b>   | <br>       | 90.51  | 86.25        | 86.57       | 70.17       | 96.84 | 86.83   | 92.04 | 83.35    | 10.63        | 86.57        |
|                   | ✓          | 95.97  | 87.05        | 87.63       | 72.87       | 96.43 | 84.57   | 91.85 | 82.40    | <b>8.29</b>  | <b>87.35</b> |

<span id="page-6-1"></span>**Table 2.** ACDC: We report DSC on RV, LV and MYO.

<span id="page-6-2"></span>**Table 3.** BraTS: We report DSC on 3 brain tumour types

<span id="page-6-3"></span>**Table 4.** Few-shot settings (5 train samples).

| Models          | MedContext RV |                   |       | Mvo LV Average                                       | Models       | MedContext   WT ET TC   Average |  |                           |                                                    | Models           | MedContext | Synapse | ACDC  |
|-----------------|---------------|-------------------|-------|------------------------------------------------------|--------------|---------------------------------|--|---------------------------|----------------------------------------------------|------------------|------------|---------|-------|
| <b>UNETR</b>    |               |                   |       | 77.81 72.74 79.46   76.67<br>84.77 75.82 81.21 80.60 | <b>UNETR</b> |                                 |  |                           | 87.35 90.88 84.29 87.50<br>87.43 91.45 85.23 88.04 | <b>UNETR</b>     | x          | 53.83   | 18.53 |
| SwinUNETR.      |               |                   |       | 83.47 75.54 83.09 80.70<br>84.79 79.17 86.15 83.38   | SwinUNETR.   |                                 |  | 90.36 91.72 86.24   89.44 | $ 90.57 \t92.30 \t86.64 $ 89.83                    |                  | ✓          | 56.25   | 28.63 |
| nnFormer        |               | 91.18 86.24 94.07 |       | 90.50<br>92.14 86.52 93.52 90.73                     | nnFormer     |                                 |  | 80.80 58.86 77.42 72.36   | 81.00 59.87 77.45 72.78                            | <b>SwinUNETR</b> | x          | 54.13   | 32.62 |
| ✓               | 61.15         | 35.80             |       |                                                      |              |                                 |  |                           |                                                    |                  |            |         |       |
| <b>nnFormer</b> | x             | 67.90             | 52.23 |                                                      |              |                                 |  |                           |                                                    |                  |            |         |       |
|                 | ✓             | 70.96             | 58.05 |                                                      |              |                                 |  |                           |                                                    |                  |            |         |       |

<span id="page-7-0"></span>**Table 5.** MedContext vs. pretrainingfinetuning [\[8\]](#page-9-12) methods. DSC (%) on Synapse dataset with UNETR architecture. Best viewed zoomed in.

<span id="page-7-1"></span>**Table 6.** Improving PCRLv2 with our proposed MedContext without pretraining across three datasets. We report Average Dice scores  $(\%)$ .

| Method                                                                                 | retrain<br>Spleen                                                                                                                                                                                                                                              | RKid | <b>Kid</b> | ā |  | $\begin{array}{cccccccccc} \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \mathbb{S} & \$ |  | 훈 |  | g |  |
|----------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------|------------|---|--|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|---|--|---|--|
| Baseline                                                                               | X 89.0 89.2 87.7 47.6 48.9 94.4 74.7 82.0 77.3 61.7 64.4 56.6 46.9 70.8                                                                                                                                                                                        |      |            |   |  |                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |   |  |   |  |
| SimCLR<br>MAE<br>SimMIM                                                                | $\checkmark$ 91.1 91.3 89.7 48.7 50.0 96.6 76.5 83.9 79.1 63.2 65.9 57.9 48.1 72.4<br>$\checkmark$ 94.8 95.0 93.4 50.6 52.1 98.6 79.7 87.4 82.4 65.9 68.6 60.5 50.1 75.3<br>$\checkmark$ 95.2 95.4 93.7 51.9 52.3 98.7 79.9 87.7 82.6 66.0 68.9 60.7 51.2 75.7 |      |            |   |  |                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |   |  |   |  |
| MedContext X   93.8 93.7 93.6 54.9 72.6 96.6 80.3 89.9 83.3 72.9 73.9 64.4 65.3   79.6 |                                                                                                                                                                                                                                                                |      |            |   |  |                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |   |  |   |  |

<span id="page-7-3"></span>

| Method              | Pretrain | Brats21      | ACDC         | Synapse      |
|---------------------|----------|--------------|--------------|--------------|
| PCRLv2              | ✓        | 79.90        | 78.53        | 64.00        |
| PCRLv2 + MedContext | ✗        | <b>82.03</b> | <b>82.57</b> | <b>72.30</b> |

loss component. report avg dice score  $(\%)$ .

<span id="page-7-4"></span><span id="page-7-2"></span>**Table 7.** Effect of each **Table 8.** Effect of mask-**Table 9.** Effect of knowling ratio. We report average edge distillation for leverag-DSC (%). ing contextual cues.

| MSL. | CL |                | Average Dice Score | Masking ratio     |                         | Average Dice Score      | Models       | Student-Teacher   Average DSC |
|------|----|----------------|--------------------|-------------------|-------------------------|-------------------------|--------------|-------------------------------|
|      |    |                | UNETR SwinUNETR    |                   |                         | UNETR SwinUNETR         | <b>UNETR</b> | 79.60<br>81.13                |
|      |    | 78.69          | 81.03              | 30%<br>40%<br>50% | 79.54<br>80.47<br>80.00 | 80.92<br>82.00<br>81.03 | SwinUNETR    | 80.83<br>82.03                |
|      |    | 79.46<br>80.32 | 81.25<br>81.70     | 60%<br>80%        | 80.20<br>79.90          | 81.70<br>81.27          | nnFormer     | 86.85<br>87.36                |

### **3.2 Few-Shot Performance**

We validate our approach in a few-shot scenario in Table [4,](#page-6-3) comparing its performance with baselines in a 5-shot setting on synapse BTCV and ACDC datasets using three model architectures. Specifically, on synapse, our approach yields a 3–10% increase in Dice score across all cases, indicating substantial segmentation accuracy improvement. Similarly, on the larger ACDC dataset, our approach consistently achieves higher Dice scores compared to baselines, highlighting its potential for enhancing segmentation accuracy in situations with limited annotated data and supporting data-efficient training.

### **3.3 Comaprison with Pretraining-Finetuning Baselines**

We demonstrate the effectiveness of MedContext by comparing its performance (DSC) with existing pretraining-finetuning methods in Table [5.](#page-7-0) The baseline [\[8](#page-9-12)] utilizes improved weight initialization through pretraining on a large dataset [\[14](#page-9-13)], incorporating state-of-the-art self-supervised methods [\[7,](#page-9-14)[18](#page-10-11)[,29](#page-10-12)], and then fine-tunes on the target dataset. In contrast, MedContext directly learns contextual cues from the small target dataset, outperforming methods using the pretraining-finetuning paradigm. When integrated into PCRLv2 [\[31\]](#page-10-6), a 3D CNN architecture pretrained in a self-supervised manner on [\[27](#page-10-13)], MedContext consistently enhances the performance (Avg. DSC) of PCRLv2 without pretraining as seen in Table  $6$ , affirming its versatility applicable to various CNN architectures.

### **3.4 Ablation Studies**

**Effect of Different Losses:** Our proposed method incorporates multiple supervised and self-supervised losses during training as elaborated in Sects. [2.3](#page-3-0) and [2.4.](#page-4-2) We perform an ablative analysis on the synapse dataset, focusing on the Masked Student Loss (MSL) and Consistency Loss (CL) in Table [7.](#page-7-2) Eliminating either loss component leads to a decrease in DSC, underscoring the mutual synergy between these losses in inducing contextual cues for effective 3D medical segmentation. **Effect of Student-Teacher framework:** Using a single model for both original and masked input using supervised loss may not effectively capture contextual relationships as it overlooks knowledge acquired during previous weight updates. To address this, we adopt a student-teacher framework, leveraging information from past updates. Table [9](#page-7-3) illustrates our claim with empirical evidence on the synapse dataset, showing a consistent performance drop without the student-teacher framework. **Effect of Masking Ratio:** Our method encourages learning contextual cues by reconstructing masked regions in the segmentation space. The fraction of patches to be masked for reconstruction may influence the model's performance. Our approach produces gains on all masking ratios, however, our analysis in Table [8](#page-7-4) reveals a 40% masking ratio to be optimal for learning contextual cues.

## **4 Conclusion**

In this paper, we propose a universal training framework called *MedContext* which effectively learns self-supervised contextual cues jointly with the supervised voxel segmentation task without requiring large-scale annotated volumetric medical data. Our proposed approach employs a student-teacher distillation strategy to reconstruct missing parts in the output segmentation space. Through extensive experimentation, our approach demonstrates complementary benefits to existing 3D medical segmentation architectures in both conventional and few-shot settings without pretraining on large-scale datasets. Moreover, the plug-and-play design of our approach allows for its easy integration into any architectural design.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-8-1"></span>1. Baid, U., Ghodasara, S., Mohan, S., et al.: The rsna-asnr-miccai brats 2021 benchmark on brain tumor segmentation and radiogenomic classification. arXiv preprint [arXiv:2107.02314](http://arxiv.org/abs/2107.02314) (2021)
- <span id="page-8-0"></span>2. Bardes, A., Garrido, Q., Ponce, J., Rabbat, M., LeCun, Y., Assran, M., Ballas, N.: Revisiting feature prediction for learning visual representations from video. arXiv preprint (2024)

- <span id="page-9-8"></span>3. Bernard, O., Lalande, A., Zotti, C.e.a.: Deep learning techniques for automatic mri cardiac multi-structures segmentation and diagnosis: Is the problem solved? IEEE Transactions on Medical Imaging **37**(11), 2514–2525 (2018)
- <span id="page-9-2"></span>4. Cao, H., Wang, Y., Chen, J., Jiang, D., Zhang, X., Tian, Q., Wang, M.: Swinunet: Unet-like pure transformer for medical image segmentation. In: European Conference on Computer Vision Workshops (2022)
- <span id="page-9-6"></span>5. Caron, M., Touvron, H., Misra, I., Jégou, H., Mairal, J., Bojanowski, P., Joulin, A.: Emerging properties in self-supervised vision transformers. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 9650–9660 (2021)
- <span id="page-9-11"></span>6. Chen, J., Lu, Y., Yu, Q., Luo, X., Adeli, E., Wang, Y., Lu, L., Yuille, A.L., Zhou, Y.: Transunet: Transformers make strong encoders for medical image segmentation. arXiv preprint [arXiv:2102.04306](http://arxiv.org/abs/2102.04306) (2021)
- <span id="page-9-14"></span>7. Chen, T., Kornblith, S., Norouzi, M., Hinton, G.: A simple framework for contrastive learning of visual representations. In: International conference on machine learning. pp. 1597–1607. PMLR (2020)
- <span id="page-9-12"></span>8. Chen, Z., Agarwal, D., Aggarwal, K., Safta, W., Balan, M.M., Brown, K.: Masked image modeling advances 3d medical image analysis. In: Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV). pp. 1970–1980 (January 2023)
- <span id="page-9-0"></span>9. Çiçek, O., Abdulkadir, A., Lienkamp, S.S., Brox, T., Ronneberger, O.: 3d unet: learning dense volumetric segmentation from sparse annotation. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2016. pp. 424– 432. Springer (2016)
- <span id="page-9-3"></span>10. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A large-scale hierarchical image database. In: 2009 IEEE Conference on Computer Vision and Pattern Recognition. pp. 248–255 (2009)
- <span id="page-9-9"></span>11. Devlin, J., Chang, M.W., Lee, K., Toutanova, K.: Bert: Pre-training of deep bidirectional transformers for language understanding. preprint [arXiv:1810.04805](http://arxiv.org/abs/1810.04805) (2018)
- <span id="page-9-1"></span>12. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth  $16x16$  words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-9-10"></span>13. Grill, J.B., Strub, F., Altch´e, F., Tallec, C., Richemond, P., Buchatskaya, E., Doersch, C., Avila Pires, B., Guo, Z., Gheshlaghi Azar, M., et al.: Bootstrap your own latent-a new approach to self-supervised learning. Advances in neural information processing systems **33**, 21271–21284 (2020)
- <span id="page-9-13"></span>14. Harmon, S.A., Sanford, T., Xu, S., et al.: Artificial intelligence for the detection of covid-19 pneumonia on chest ct using multinational datasets. Nature Communications **11** (2020)
- <span id="page-9-4"></span>15. Hatamizadeh, A., Nath, V., Tang, Y., Yang, D., Roth, H.R., Xu, D.: Swin unetr: Swin transformers for semantic segmentation of brain tumors in mri images. In: International MICCAI Brainlesion Workshop (2022)
- <span id="page-9-7"></span>16. Hatamizadeh, A., Tang, Y., Nath, V., Yang, D., Myronenko, A., Landman, B., Roth, H.R., Xu, D.: Unetr: Transformers for 3d medical image segmentation. In: Proceedings of the IEEE/CVF winter conference on applications of computer vision. pp. 574–584 (2022)
- <span id="page-9-5"></span>17. Hatamizadeh, A., Xu, Z., Yang, D., Li, W., Roth, H., Xu, D.: Unetformer: A unified vision transformer model and pre-training framework for 3d medical image segmentation. arXiv preprint [arXiv:2204.00631](http://arxiv.org/abs/2204.00631) (2022)

- <span id="page-10-11"></span>18. He, K., Chen, X., Xie, S., Li, Y., Dollár, P., Girshick, R.: Masked autoencoders are scalable vision learners. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 16000–16009 (2022)
- <span id="page-10-3"></span>19. Hinton, G., Vinyals, O., Dean, J.: Distilling the knowledge in a neural network. arXiv preprint [arXiv:1503.02531](http://arxiv.org/abs/1503.02531) (2015)
- <span id="page-10-1"></span>20. Karimi, D., Vasylechko, S.D., Gholipour, A.: Convolution-free medical image segmentation using transformers. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2021. pp. 78–88. Springer (2021)
- <span id="page-10-7"></span>21. Landman, B., Xu, Z., Igelsias, J., Styner, M., Langerak, T., Klein, A.: Miccai multi-atlas labeling beyond the cranial vault–workshop and challenge. In: MICCAI Multi-Atlas Labeling Beyond Cranial Vault-Workshop Challenge (2015)
- <span id="page-10-8"></span>22. Menze, B.H., Jakab, A., Bauer, S., et al.: The multimodal brain tumor image segmentation benchmark (brats). IEEE Transactions on Medical Imaging **34**(10), 1993–2024 (2015). <https://doi.org/10.1109/TMI.2014.2377694>
- <span id="page-10-9"></span>23. Milletari, F., Navab, N., Ahmadi, S.A.: V-net: Fully convolutional neural networks for volumetric medical image segmentation. In: Fourth International Conference on 3D Vision (3DV) (2016)
- <span id="page-10-10"></span>24. Project-MONAI: Medical open network for ai. [https://github.com/Project-](https://github.com/Project-MONAI/MONAI)[MONAI/MONAI](https://github.com/Project-MONAI/MONAI) (2020)
- <span id="page-10-4"></span>25. Richemond, P.H., Grill, J.B., Altch´e, F., et al.: Byol works even without batch statistics. arXiv preprint [arXiv:2010.10241](http://arxiv.org/abs/2010.10241) (2020)
- <span id="page-10-0"></span>26. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2015: 18th International Conference, Munich, Germany, October 5-9, 2015, Proceedings, Part III 18. pp. 234–241. Springer (2015)
- <span id="page-10-13"></span>27. Setio, A.A.A., Traverso, A., et. al.: Validation, comparison, and combination of algorithms for automatic detection of pulmonary nodules in computed tomography images: The luna16 challenge. Medical Image Analysis **42**, 1–13 (2017)
- <span id="page-10-2"></span>28. Xie, Y., Zhang, J., Xia, Y., Wu, Q.: Unified 2d and 3d pre-training for medical image classification and segmentation. arXiv preprint [arXiv:2112.09356](http://arxiv.org/abs/2112.09356) (2021)
- <span id="page-10-12"></span>29. Xie, Z., Zhang, Z., Cao, Y., Lin, Y., Bao, J., Yao, Z., Dai, Q., Hu, H.: Simmim: A simple framework for masked image modeling. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 9653–9663 (2022)
- <span id="page-10-5"></span>30. Zhou, H.Y., Guo, J., Zhang, Y., Yu, L., Wang, L., Yu, Y.: nnformer: Interleaved transformer for volumetric segmentation. arXiv preprint [arXiv:2109.03201](http://arxiv.org/abs/2109.03201) (2021)
- <span id="page-10-6"></span>31. Zhou, H.Y., Lu, C., Chen, C., Yang, S., Yu, Y.: Pcrlv2: A unified visual information preservation framework for self-supervised pre-training in medical image analysis. arXiv preprint [arXiv:2301.00772](http://arxiv.org/abs/2301.00772) (2023)

Image /page/11/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below. The icon is a gray circle with a gray ribbon shape inside. The ribbon is folded over itself, with the folded part pointing to the left. The text below the icon reads "Check for updates" in gray capital letters.

# **Medical Image Synthesis via Fine-Grained Image-Text Alignment and Anatomy-Pathology Prompting**

Wenting Chen<sup>1</sup>, Pengyu Wang<sup>2</sup>, Hui Ren<sup>3</sup>, Lichao Sun<sup>4</sup>, Quanzheng Li<sup>3</sup>, Yixuan Yuan<sup>1,2( $\boxtimes$ )</sup>, and Xiang  $Li^{3(\boxtimes)}$ 

> <sup>1</sup> City University of Hong Kong, Hong Kong, China <EMAIL>

<sup>2</sup> The Chinese University of Hong Kong, Hong Kong, China

<sup>3</sup> Massachusetts General Hospital and Harvard Medical School, Boston, USA

<EMAIL>

<sup>4</sup> Lehigh University, Bethlehem, USA

**Abstract.** Data scarcity and privacy concerns limit the availability of high-quality medical images for public use, which can be mitigated through medical image synthesis. However, current medical image synthesis methods often struggle to accurately capture the complexity of detailed anatomical structures and pathological conditions. To address these challenges, we propose a novel medical image synthesis model that leverages fine-grained image-text alignment and anatomy-pathology prompts to generate highly detailed and accurate synthetic medical images. Our method integrates advanced natural language processing techniques with image generative modeling, enabling precise alignment between descriptive text prompts and the synthesized images' anatomical and pathological details. The proposed approach consists of two key components: an anatomy-pathology prompting module and a fine-grained alignment-based synthesis module. The anatomy-pathology prompting module automatically generates descriptive prompts for high-quality medical images. To further synthesize high-quality medical images from the generated prompts, the fine-grained alignment-based synthesis module pre-defines a visual codebook for the radiology dataset and performs fine-grained alignment between the codebook and generated prompts to obtain key patches as visual clues, facilitating accurate image synthesis. We validate the superiority of our method through experiments on public chest X-ray datasets and demonstrate that our synthetic images preserve accurate semantic information, making them valuable for various medical applications.

## **1 Introduction**

In the medical field, high-quality medical images are scarce and difficult to access due to data privacy concerns and the labor-intensive process of collecting such data [\[10](#page-20-0)]. This scarcity of medical images can hinder the development and training of artificial intelligence (AI) models for various medical applications, such

as diagnosis  $[26]$ , segmentation  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$  $[6,7,14,19-21,25]$ , report generation  $[3]$  $[3]$ , image synthesis [\[4](#page-20-6)[,5](#page-20-7)[,8](#page-20-8)], detection [\[27](#page-21-2)], and abnormality classification. One solution to overcome this challenge is to use medical image synthesis techniques to generate synthetic data that can replace or supplement real medical images.

Several chest X-ray generation methods have been investigated to mitigate these issues, which can be categorized into three main groups: generative adversarial networks  $(GAN)$  based  $[16, 22, 28]$  $[16, 22, 28]$  $[16, 22, 28]$  $[16, 22, 28]$  $[16, 22, 28]$  $[16, 22, 28]$ , diffusion based  $[1, 2]$  $[1, 2]$  $[1, 2]$ , and transformer based [\[17](#page-20-10)[,18](#page-20-11)] methods. Madani *et al.* [\[22](#page-21-3)] and Zhang *et al.* [\[28\]](#page-21-4) utilize unconditional GANs to synthesize medical images as a form of data augmentation to improve segmentation and abnormality classification performance. To leverage medical reports, some diffusion-based methods [\[1](#page-19-1),[2\]](#page-19-2) take the impression section of medical reports and random Gaussian noise as input for chest X-ray generation, ignoring the finding section that includes more detailed descriptions. To consider more details in medical reports, several transformer-based methods [\[17,](#page-20-10)[18\]](#page-20-11) take both finding and impression sections of medical reports as input to synthesize chest X-rays. However, current methods generate medical images based on the given ground-truth report from the dataset, which may not fully describe all the details of the medical image. In fact, medical images contain different anatomical structures (lobe, heart, and mediastinal) and pathological conditions (opacity, effusion, and consolidation), which are important for clinical diagnosis. As a result, the generated medical images often lack this detailed information. Thus, there is a need for a medical image synthesis method that can generate high-quality medical images with detailed anatomical and pathological descriptions.

Another significant challenge for current medical image synthesis methods is the substantial inter-modal gap between medical images and reports. Medical images, comprising thousands of pixels, visualize rich textures and colors, while medical reports consist of only a few sentences to summarize the findings and impressions of the medical images. This disparity leads to a great imbalance in the amount of information contained in each modality, resulting in a large inter-modal gap between medical reports and images [\[12](#page-20-12)]. As a result, the generated medical images may not accurately reflect the content of the corresponding medical reports, as the synthesis models struggle to bridge this information gap. Furthermore, the limited information provided in the medical reports may not be sufficient to guide the synthesis of highly detailed and accurate medical images, which are crucial for clinical diagnosis and decision-making. Thus, it is necessary to develop techniques that can effectively mitigate the information imbalance and minimize the inter-modal gap between medical reports and images. By doing so, the synthesized medical images can better capture the detailed anatomical structures and pathological conditions described in the medical reports, leading to more reliable and informative synthetic data for various medical applications.

To address these issues, we propose a novel medical image synthesis model that leverages the capabilities of fine-grained image-text alignment and anatomypathology prompts to generate highly detailed and accurate synthetic medical images. Our approach consists of two key components: an **anatomy-pathology** **prompting** and a **fine-grained alignment based synthesis module**. The **anatomy-pathology prompting** aims to automatically generate descriptive reports for high-quality medical images. It first constructs the anatomy and pathology vocabularies from radiology reports under the guidance of radiologists, and then employs GPT-4 to write reports based on the given vocabularies. This ensures that the generated reports contain comprehensive and accurate descriptions of the anatomical structures and pathological conditions present in the medical images. To further synthesize high-quality medical images from the generated reports, we introduce a **fine-grained alignment based synthesis module**. This module pre-defines a visual codebook containing multiple patches commonly observed in the radiology dataset and performs fine-grained alignment between the generated reports and the visual codebook. Through this alignment, the module extracts the most matched keypatches that provide visual clues for the large language model (LLM) during the synthesis process. The LLM takes the generated reports, keypatches, and instructions as input and outputs visual tokens, which are then decoded by a VQ-GAN decoder to produce the final synthetic medical images. We conduct extensive experiments on publicly available chest X-ray (CXR) datasets to validate the superiority of our method compared to existing approaches. Furthermore, we perform semantic analysis on both real and synthetic images to demonstrate that our synthetic images preserve accurate semantic information, including anatomical structures and pathological conditions, making them valuable for various medical applications.

Image /page/13/Figure/2 description: This is a flowchart illustrating a method for generating medical images based on radiology reports. The process begins with radiology reports, which are processed to extract anatomy and pathology vocabularies. An expert-guided screening step identifies top-K nouns and adjectives. This information, along with the original report, is fed into a report writing module, which generates a new report. This generated report is then processed by a text encoder. Separately, a visual codebook of medical images is used with an image encoder to create a word-patch similarity matrix. The generated report and the similarity matrix are fed into a Large Language Model (LLM) which, along with matched keypatches extracted from the visual codebook, generates a new medical image using a VQ-GAN decoder. The process is divided into two main modules: Anatomy-Pathology Prompting and Fine-Grained Alignment based Synthesis Module. The final output is a generated image, labeled as x'.

<span id="page-13-0"></span>**Fig. 1.** The overview of the proposed method. It consists of an anatomy-pathology prompting module to generate descriptive reports with given anatomy and pathology words, and a fine-grained alignment based synthesis module using fine-grained imagetext alignment to facilitate image generation.

## **2 Method**

### **2.1 Anatomy-Pathology Prompting**

Since current methods struggle to synthesize medical images with complex anatomical structures (lobe, heart, and mediastinal) and pathological conditions (opacity, effusion, and consolidation), we introduce an anatomy-pathology prompting to automatically generate descriptive reports for high-quality medical image generation. This prompting module contains two main steps, including the design of anatomy and pathology vocabularies and prompts generation.

**Designing Anatomy and Pathology Vocabularies.** As illustrated in Fig. [1,](#page-13-0) we have developed anatomy and pathology vocabularies to extract instancelevel anatomical and pathological terms from radiological reports and images. Recognizing that anatomical and pathological terms are typically nouns and adjectives, we employ a word filter to extract all nouns and adjectives from the impression and findings sections of reports in the MIMIC-CXR dataset [\[15\]](#page-20-13). We then select the top-K nouns and adjectives based on their occurrence frequencies. Finally, under expert guidance, we manually remove any remaining non-medical nouns and adjectives that GPT-4 is unable to filter out, and categorize the screened words into anatomy and pathology vocabularies according to their medical attributes. The number of words in anatomy and pathology vocabularies is 75 and 44, respectively. We demonstrate the word frequency of the anatomy and pathology vocabularies, as shown in Fig. [2.](#page-14-0)

Image /page/14/Figure/3 description: The image contains two line graphs, one above the other, both with labels on the x-axis and a numerical scale on the y-axis. The top graph is titled "Anatomy" and displays a purple line. The x-axis labels include "lung", "pulmonary", "lungs", "chest", "silhouette", "mediastinal", "cardiomegaly", "heart", "hilar", "tube", "osseous", "lobe", "vascular", "thoracic", "catheter", "interval", "bibasilar", "aorta", "vasculature", "interstitial", "svc", "spine", "ribs", "sternal", "tracheal", "bony", "sternotomy", "retrocardiac", "aortic", "basilar", "clips", "costophrenic", "abdomen", "atrium", "wires", "venous", "nasogastric", "fluid", "ventricle", "pacemaker", "jugular", "bronchial", "vasculature", "enteric", "hila", "diaphragm", "perihilar", "port-a-cath", "arch", "hemithorax", "subclavian", "tissue", "caval", "vertebral", "trachea", "carotid", "artery", "hilar", "trachea", "vein", "cabg", "subcutaneous", "tubes", "esophagus", "stent", "vessels", "cervical", "sternal", "neck", "junction". The y-axis ranges from 0 to 2000. The bottom graph is titled "Pathology" and displays a teal line. The x-axis labels include "pneumothorax", "consolidation", "focal", "cardiac", "atelectasis", "edema", "opacity", "effusions", "opacities", "pneumonia", "congestion", "cardiomegaly", "carina", "opacification", "degenerative", "fractures", "fracture", "calcifications", "chronic", "mediastinum", "calcification", "tortuosity", "emphysema", "disease", "infection", "thickening", "parenchymal", "atherosclerosis", "nodular", "hernia", "deformity", "engorgement", "collapse", "nodule", "multifocal", "infectious", "pneumothorax", "density", "diffuse", "streaky". The y-axis ranges from 0 to 1500. Both graphs show fluctuating data trends across their respective x-axis labels.

<span id="page-14-0"></span>**Fig. 2.** The word frequency of the anatomy and pathology vocabularies.

**Prompts Generation.** With the anatomy and pathology vocabularies, we employ GPT4 to automatically generate the medical reports. Specifically, we first provide the vocabularies to GPT4 and require it to randomly select  $N$  and M words from anatomy and pathology vocabularies, respectively, which can be combined as the findings. Then, these words are passed to GPT4 to write a report with reasonable findings for a chest X-ray image. To let GPT4 write reports as our requirement, we use the following instructions.

```
anatomy list = ['pleural', 'lung', ......,'neck', 'junction']
pathology list = ['effusion', 'pneumothorax', ......, 'diffuse', 'streaky']
Here are two lists of anatomy and pathology for chest X-rays. Please write some findings
that only include 2 words from the anatomy list and 2 from the pathology list, and
do not write any negative sentences in the findings. These four words can be randomly
selected from the two lists, respectively. Please ensure the findings are reasonable for
a chest x-ray in real medical scenarios. The output should be in 50 words. Here is an
example:
anatomy list = ['heart', 'diaphragm']
pathology list = ['effusion', 'opacity']
Findings: Presence of opacity observed near the heart and diaphragm regions suggestive
of effusion.
Please generate the output in the following format:
\texttt{anatomy-list} = ['word1', 'word2']pathology list = ['word3', 'word4']
Findings:
```

This instruction example requires GPT4 to use two words from anatomy and pathology vocabularies, respectively. Actually, we can use more than two words and set N and M for the number of words we used in anatomy and pathology vocabularies, respectively. Then, we collect the anatomy-pathology prompts generated by GPT4, where each prompt contains an anatomy word list (e.g. ['heart', 'diaphragm']), a pathology word list (e.g. ['effusion', 'opacity']), and a generated report (e.g. Presence of opacity observed near the heart and diaphragm regions suggestive of effusion.). With these generated anatomy-pathology prompts, we can provide the synthesis model descriptive reports with detailed anatomical structures and pathological conditions.

### **2.2 Fine-Grained Alignment Based Synthesis Module**

Since there is an information imbalance and the inter-modal gap between medical reports and images, we devise a fine-grained alignment based synthesis module to leverage the fine-grained image-text alignment to facilitate image generation. The fine-grained alignment between medical reports and visual codebook to obtain matched keypatches as a clue for image synthesis. This module includes three steps for medical image synthesis, i.e. visual codebook construction, keypatches extraction, and image synthesis.

**Visual Codebook Construction.** To construct a visual codebook, we first identify the most common patches in the training set images and designate them as keypatches. This process involves matching patches from CXR images with textual tokens from their corresponding medical reports. We select the top  $\kappa_1$  CXR-report pairs that exhibit the highest report-to-CXR similarities, denoted as  $s^T$ . For each selected CXR-report pair, we calculate the maximum similarity between each textual token and the image patches, resulting in wordpatch maximum similarity scores. The embeddings of textual tokens and image patches are extracted by the pre-trained text and encoders [\[3\]](#page-19-0), respectively. These scores are then ranked, and the patches corresponding to the top  $\kappa_2$ similarities are extracted and included in the visual codebook as keypatches. Each keypatch in the codebook consists of the patch itself and its associated features.

**Keypatches Extraction.** With the visual codebook, we establish a correspondence between the features of keypatches and the textual tokens of the generated report. This is achieved by matching the features of each keypatch in the visual codebook with the textual tokens, resulting in the creation of a word-patch similarity matrix, denoted as  $s^W \in \mathbb{R}^{(\kappa_1 \times \kappa_2) \times K}$ , where K represents the total number of textual tokens in the report. To identify the keypatches that are most relevant to the generated report, we perform a ranking operation on the word-patch similarity matrix along the dimension of keypatches. For each textual token, we select the top  $\kappa_3$  keypatches with the highest word-patch similarity scores. Finally, we extract the features of these selected keypatches, denoted as  $k<sup>I</sup>$ , which serve as a compact representation of the visual information most closely associated with the textual content of the generated report.

**Image Synthesis.** After acquiring the keypatches, we employ a frozen VQ-GAN encoder [\[11\]](#page-20-14) E to transform the matched keypatches  $k<sup>I</sup>$  into image tokens  $E(k^{I})$ . These image tokens are then fed into a pre-trained large language model (LLM) [\[3\]](#page-19-0) along with the instruction and the generated report. The input to the LLM follows an instruction-following format. By providing the LLM with the instruction, generated report, and image tokens of the keypatches, we enable the model to predict image tokens that correspond to the desired CXR image. Finally, the predicted image tokens are decoded using the VQ-GAN decoder, resulting in the generation of the CXR image  $x^{I'}$ . This process leverages the power of the pre-trained LLM to interpret the textual instruction and report, while utilizing the visual information encoded in the keypatches to guide the generation of a realistic and coherent CXR image.

By adopting the fine-grained alignment based synthesis module, we can generate high-quality medical images with the detailed anatomical structures and pathological conditions described in the medical reports.

## **3 Experiments and Results**

### **3.1 Experiment Setting**

**Datasets.** In our experiments, we utilize two widely used publicly available chest X-ray datasets: MIMIC-CXR [\[15](#page-20-13)] and OpenI [\[9\]](#page-20-15). The MIMIC-CXR dataset is a large-scale dataset consisting of 473,057 images and 206,563 corresponding medical reports from 63,478 patients. We adhere to the official dataset splits, which allocate 368,960 samples for training, 2,991 for validation, and 5,159 for testing. On the other hand, the OpenI dataset is smaller in size, containing 3,684 report-image pairs. The dataset is divided into 2,912 samples for training and 772 for testing.

**Implementation and Metrics.** We use the pre-trained image encoder, text encoder and LLM [\[3](#page-19-0)] in the fine-grained alignment synthesis module. The pretrained VQ-GAN model [\[11\]](#page-20-14) is adopted to encode image patches to image tokens, and decode the image tokens to images. All the models are frozen in

| Methods                   | MIMIC-CXR |        | OpenI   |        |
|---------------------------|-----------|--------|---------|--------|
|                           | FID ↓     | NIQE ↓ | FID ↓   | NIQE ↓ |
| Stable diffusion [24]     | 14.5194   | 5.7455 | 11.3305 | 5.7455 |
| Chambon <i>et al.</i> [2] | 12.7408   | 4.4534 | 8.2887  | 4.4534 |
| RoentGen [1]              | 13.1979   | 5.1286 | 6.5666  | 5.1286 |
| UniXGen [17]              | 14.0569   | 6.2759 | 7.5210  | 6.2759 |
| LLM-CXR [18]              | 11.9873   | 4.5876 | 5.9869  | 4.5876 |
| <b>Ours</b>               | 8.8213    | 4.1138 | 5.7455  | 4.1138 |

<span id="page-17-0"></span>**Table 1.** Comparison of report-to-CXR generation performance on the MIMIC-CXR and the OpenI datasets.

Image /page/17/Figure/3 description: This image is a comparison of different methods for generating chest X-rays. It is organized into two rows, each representing a different case. The first row shows a case with "lung, aorta" anatomy and "atelectasis, opacity, consolidation" pathology, with a report mentioning "lung atelectasis with consolidation and opacity near the aorta." The second row shows a case with "pleural, vascular" anatomy and "effusion, congestion, cardiomegaly" pathology, with a report mentioning "vascular congestion with pleural effusion, suggestive of cardiomegaly." Each row displays the results from "Ours," "LLM-CXR," "Chambon et al.," "UniXGen," and "Stable Diffusion." The "Ours" column in both rows highlights specific areas with red dashed outlines, indicating the regions of interest or abnormalities.

**Fig. 3.** The generated chest X-ray images of the MIMIC-CXR dataset with highlighted regions.

the framework. To assess the image quality, we use the Fréchet inception distance (FID) [\[13\]](#page-20-16) and Natural Image Quality Evaluator (NIQE) [\[23\]](#page-21-6). The lower values indicate the better performance.

<span id="page-17-2"></span>**Table 2.** Anatomy and pathology classification performance (%) comparison of MIMIC-CXR dataset and CXR images generated by our method.

<span id="page-17-1"></span>

| Data source | Anatomy      |              | Pathology |       | Overall      |              |
|-------------|--------------|--------------|-----------|-------|--------------|--------------|
|             | Accuracy     | AUC          | Accuracy  | AUC   | Accuracy     | AUC          |
| MIMIC-CXR   | 91.21        | 78.17        | 92.19     | 74.42 | 91.59        | 76.74        |
| <b>Ours</b> | <b>94.74</b> | <b>83.88</b> | 92.11     | 77.02 | <b>93.74</b> | <b>81.27</b> |

### **3.2 Comparison with State-of-the-Arts**

We conducted a quantitative comparison of our method with state-of-the-art text-to-image generation methods, such as Stable Diffusion [\[24\]](#page-21-5), and reportto-CXR generation approaches, including Chambon *et al.* [\[2\]](#page-19-2), RoentGen [\[1\]](#page-19-1),

Image /page/18/Figure/1 description: The image displays four scatter plots, labeled (a) Stable Diffusion, (b) RoentGen, (c) UniXGen, and (d) Ours. Each plot visualizes data points colored red and blue, with a legend indicating that red dots represent 'Real' data and blue dots represent 'Synthetic' data. The plots are arranged in a 2x2 grid, though only a single row of four plots is visible. The x and y axes of each plot range approximately from -40 to 60 and -40 to 60 respectively, suggesting a dimensionality reduction technique like t-SNE has been applied. The distribution and overlap of red and blue points vary across the four plots, indicating differences in the quality or characteristics of the synthetic data generated by each method compared to the real data.

<span id="page-18-0"></span>**Fig. 4.** The t-SNE visualization of the real and synthetic CXR images on the MIMIC-CXR dataset.

UniXGen [\[17](#page-20-10)], and LLM-CXR [\[18\]](#page-20-11). As shown in Table [1,](#page-17-0) our method achieves the highest FID scores on both datasets, demonstrating its superior performance in generating CXR images with descriptive reports. To further investigate the highlevel feature distribution of the generated CXR images, we randomly selected 1,000 cases from the test set and performed t-SNE visualization on both real and synthetic CXR images from the MIMIC-CXR dataset. Figure [4](#page-18-0) illustrates that while the synthetic CXR images generated by current methods exhibit notable differences from the real ones, our method produces images that nearly overlap with the real images in the t-SNE visualization, highlighting its exceptional ability to generate highly realistic CXR images.

Figure [3](#page-17-1) presents a comparison of CXR images generated by our method and existing approaches on both the MIMIC-CXR and OpenI datasets. In the first example, our proposed method successfully synthesizes the 'opacity near the aorta' described in the input report, while other methods struggle to generate this specific feature. This observation highlights the superior capability of our method in producing highly realistic and accurate CXR images that faithfully reflect the content of the corresponding reports.

### **3.3 Semantic Analysis**

To further analyze the semantic information of the synthetic images, we pretrain a classifier on the MIMIC-CXR dataset for the multi-label anatomy and pathology classification. Then, we test the classification performance of the real and synthetic images. In Table [2,](#page-17-2) we show the classification performance for the test set of the MIMIC-CXR dataset and CXR images generated by our method. Our method significantly outperforms the real data by a large margin with an accuracy of 2.15%, implying our synthetic data with accurate semantic information about anatomical structures and pathological conditions. Moreover, we also show the performance of each category for anatomy and pathology classification. As visualized in Fig. [5,](#page-19-3) our method achieves higher precision than the real data in most categories. These indicate the medical images generated by our method preserve more semantic information in terms of anatomy and pathology.

Image /page/19/Figure/1 description: The image contains two bar charts, one above the other. Both charts compare 'Real data' (teal bars) and 'Synthetic data' (yellow bars) across various classifications. The top chart is titled 'Anatomy Classification' and displays classifications such as pleural, lung, pulmonary, lungs, chest, silhouette, mediastinal, cardiomediastinal, heart, hilar, tube, osseous, vascular, thoracic, catheter, and others. The bottom chart is titled 'Pathology Classification' and includes classifications like effusion, pneumothorax, consolidation, focal, cardiac, atelectasis, edema, opacity, effusions, opacities, pneumonia, congestion, helapharagm, cardiomegaly, carina, ap, spcification, degenerative, calcified, thickening, parenchymal, atherosclerotic, nodular, hernia, deformity, engorgement, collapse, nodule, multilocal, infectious, pneumothoraces, density, diffuse, and streaky. The y-axis for both charts ranges from 0 to 1, indicating a proportion or probability.

<span id="page-19-3"></span>**Fig. 5.** Anatomy and pathology classification performance of each category. Each column shows the precision score.

## **4 Conclusion**

To synthesize high-quality medical images with detailed anatomical and pathology information, we introduce a medical image synthesis model to generate anatomy-pathology prompts and highly detailed medical images. In order to provide the descriptive reports with anatomy and pathology information, we design an anatomy-pathology prompting to establish anatomy and pathology vocabularies and employ GPT4 to automatically generate reports. With the descriptive reports, we devise a fine-grained alignment based synthesis module to perform alignment between the reports and pre-defined visual codebook to obtain matched keypatches. Moreover, this module utilizes the LLM and VQ-GAN to convert reports, instructions, and matched keypatches to synthetic images.

**Acknowledgement.** This work was supported by the Hong Kong Research Grants Council (RGC) General Research Fund 14204321.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-19-1"></span>1. Chambon, P., Bluethgen, C., Delbrouck, J.B., Van der Sluijs, R., Polacin, M., Chaves, J.M.Z., Abraham, T.M., Purohit, S., Langlotz, C.P., Chaudhari, A.: Roentgen: vision-language foundation model for chest x-ray generation. arXiv preprint [arXiv:2211.12737](http://arxiv.org/abs/2211.12737) (2022)
- <span id="page-19-2"></span>2. Chambon, P., Bluethgen, C., Langlotz, C.P., Chaudhari, A.: Adapting pretrained vision-language foundational models to medical imaging domains. arXiv preprint [arXiv:2210.04133](http://arxiv.org/abs/2210.04133) (2022)
- <span id="page-19-0"></span>3. Chen, W., Li, X., Shen, L., Yuan, Y.: Fine-grained image-text alignment in medical imaging enables cyclic image-report generation. arXiv preprint [arXiv:2312.08078](http://arxiv.org/abs/2312.08078) (2023)

- <span id="page-20-6"></span>4. Chen, W., Liu, J., Chow, T.W., Yuan, Y.: Star-rl: Spatial-temporal hierarchical reinforcement learning for interpretable pathology image super-resolution. IEEE Trans. Med. Imag. (2024)
- <span id="page-20-7"></span>5. Chen, W., Liu, Y., Hu, J., Yuan, Y.: Dynamic depth-aware network for endoscopy super-resolution. IEEE J. Biomed. Health Inform. **26**(10), 5189–5200 (2022)
- <span id="page-20-1"></span>6. Chen, W., Yu, S., Ma, K., Ji, W., Bian, C., Chu, C., Shen, L., Zheng, Y.: Tw-gan: Topology and width aware gan for retinal artery/vein classification. Med. Image Anal. **77**, 102340 (2022)
- <span id="page-20-2"></span>7. Chen, W., Yu, S., Wu, J., Ma, K., Bian, C., Chu, C., Shen, L., Zheng, Y.: Tr-gan: Topology ranking gan with triplet loss for retinal artery/vein classification. In: MICCAI. pp. 616–625. Springer (2020)
- <span id="page-20-8"></span>8. Chen, W., Zhao, W., Chen, Z., Liu, T., Liu, L., Liu, J., Yuan, Y.: Mask-aware transformer with structure invariant loss for ct translation. Med. Image Anal. **96**, 103205 (2024)
- <span id="page-20-15"></span>9. Demner-Fushman, D., Kohli, M.D., Rosenman, M.B., Shooshan, S.E., Rodriguez, L., Antani, S., Thoma, G.R., McDonald, C.J.: Preparing a collection of radiology examinations for distribution and retrieval. JAMIA **23**(2), 304–310 (2016)
- <span id="page-20-0"></span>10. El Jiani, L., El Filali, S., et al.: Overcome medical image data scarcity by data augmentation techniques: A review. In: ICM. pp. 21–24. IEEE (2022)
- <span id="page-20-14"></span>11. Esser, P., Rombach, R., Ommer, B.: Taming transformers for high-resolution image synthesis. In: CVPR. pp. 12873–12883 (2021)
- <span id="page-20-12"></span>12. Henning, C.A., Ewerth, R.: Estimating the information gap between textual and visual representations. In: ICMR. pp. 14–22 (2017)
- <span id="page-20-16"></span>13. Heusel, M., Ramsauer, H., Unterthiner, T., Nessler, B., Hochreiter, S.: Gans trained by a two time-scale update rule converge to a local nash equilibrium. NeurIPS **30**, 6629-6640 (2017)
- <span id="page-20-3"></span>14. Ji, W., Chen, W., Yu, S., Ma, K., Cheng, L., Shen, L., Zheng, Y.: Uncertainty quantification for medical image segmentation using dynamic label factor allocation among multiple raters. In: MICCAI on QUBIQ workshop. vol. 2 (2020)
- <span id="page-20-13"></span>15. Johnson, A.E., Pollard, T.J., Berkowitz, S.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Mark, R.G., Horng, S.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Scientific data **6**(1), 317 (2019)
- <span id="page-20-9"></span>16. Karbhari, Y., Basu, A., Geem, Z.W., Han, G.T., Sarkar, R.: Generation of synthetic chest x-ray images and detection of covid-19: A deep learning based approach. Diagnostics **11**(5), 895 (2021)
- <span id="page-20-10"></span>17. Lee, H., Kim, W., Kim, J.H., Kim, T., Kim, J., Sunwoo, L., Choi, E.: Unified chest x-ray and radiology report generation model with multi-view chest x-rays. arXiv preprint [arXiv:2302.12172](http://arxiv.org/abs/2302.12172) (2023)
- <span id="page-20-11"></span>18. Lee, S., Kim, W.J., Ye, J.C.: Llm itself can read and generate cxr images. arXiv preprint [arXiv:2305.11490](http://arxiv.org/abs/2305.11490) (2023)
- <span id="page-20-4"></span>19. Liu, J., Guo, X., Yuan, Y.: Graph-based surgical instrument adaptive segmentation via domain-common knowledge. IEEE Trans. Med. Imag. **41**(3), 715–726 (2021)
- 20. Liu, J., Guo, X., Yuan, Y.: Prototypical interaction graph for unsupervised domain adaptation in surgical instrument segmentation. In: MICCAI. pp. 272– 281. Springer (2021)
- <span id="page-20-5"></span>21. Liu, J., Zhang, Y., Chen, J.N., Xiao, J., Lu, Y., A Landman, B., Yuan, Y., Yuille, A., Tang, Y., Zhou, Z.: Clip-driven universal model for organ segmentation and tumor detection. In: ICCV. pp. 21152–21164 (2023)

- <span id="page-21-3"></span>22. Madani, A., Moradi, M., Karargyris, A., Syeda-Mahmood, T.: Chest x-ray generation and data augmentation for cardiovascular abnormality classification. In: Medical imaging 2018: Image processing. vol. 10574, pp. 415–420. SPIE (2018)
- <span id="page-21-6"></span>23. Mittal, A., Soundararajan, R., Bovik, A.C.: Making a "completely blind" image quality analyzer. IEEE Signal Process. Lett. **20**(3), 209–212 (2012)
- <span id="page-21-5"></span>24. Rombach, R., Blattmann, A., Lorenz, D., Esser, P., Ommer, B.: High-resolution image synthesis with latent diffusion models. In: CVPR. pp. 10684–10695 (2022)
- <span id="page-21-1"></span>25. Wenting, C., Jie, L., Yixuan, Y.: Bi-vlgm: Bi-level class-severity-aware visionlanguage graph matching for text guided medical image segmentation. arXiv preprint [arXiv:2305.12231](http://arxiv.org/abs/2305.12231) (2023)
- <span id="page-21-0"></span>26. Wu, J., Yu, S., Chen, W., Ma, K., Fu, R., Liu, H., Di, X., Zheng, Y.: Leveraging undiagnosed data for glaucoma classification with teacher-student learning. In: MICCAI. pp. 731–740. Springer (2020)
- <span id="page-21-2"></span>27. Yang, X., Li, X., Li, X., Chen, W., Shen, L., Li, X., Deng, Y.: Two-stream regression network for dental implant position prediction. Expert Syst. with Appl. **235**, 121135 (2024)
- <span id="page-21-4"></span>28. Zhang, T., Fu, H., Zhao, Y., Cheng, J., Guo, M., Gu, Z., Yang, B., Xiao, Y., Gao, S., Liu, J.: Skrgan: Sketching-rendering unconditional generative adversarial networks for medical image synthesis. In: MICCAI. pp. 777–785. Springer (2019)

Image /page/22/Picture/0 description: A square button with rounded corners has a gray background. In the center of the button is a circular icon with a bookmark shape inside. Below the icon, the text "Check for updates" is written in gray capital letters.

# **Multi-Dataset Multi-Task Learning for COVID-19 Prognosis**

Filippo Ruffini<sup>1</sup>, Lorenzo Tronchin<sup>1</sup>, Zhuoru Wu<sup>2</sup>, Wenting Chen<sup>3</sup>, Paolo Soda<sup>1</sup>, Linlin Shen<sup>2</sup>, and Valerio Guarrasi<sup>1( $\boxtimes$ )</sup>

Unit of Computer Systems and Bioinformatics, Department of Engineering, Universit`a Campus Bio-Medico di Roma, Rome, Italy <EMAIL>

 $2$  College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China

<sup>3</sup> Department of Electrical Engineering, City University of Hong Kong, Hong Kong, China

**Abstract.** In the fight against the COVID-19 pandemic, leveraging artificial intelligence to predict disease outcomes from chest radiographic images represents a significant scientific aim. The challenge, however, lies in the scarcity of large, labeled datasets with compatible tasks for training deep learning models without leading to overfitting. Addressing this issue, we introduce a novel multi-dataset multi-task training framework that predicts COVID-19 prognostic outcomes from chest Xrays (CXR) by integrating correlated datasets from disparate sources, distant from conventional multi-task learning approaches, which rely on datasets with multiple and correlated labeling schemes. Our framework hypothesizes that assessing severity scores enhances the model's ability to classify prognostic severity groups, thereby improving its robustness and predictive power. The proposed architecture comprises a deep convolutional network that receives inputs from two publicly available CXR datasets, AIforCOVID for severity prognostic prediction and BRIXIA for severity score assessment, and branches into task-specific fully connected output networks. Moreover, we propose a multi-task loss function, incorporating an indicator function, to exploit multi-dataset integration. The effectiveness and robustness of the proposed approach are demonstrated through significant performance improvements in prognosis classification tasks across 18 different convolutional neural network backbones in different evaluation strategies. This improvement is evident over single-task baselines and standard transfer learning strategies, supported by extensive statistical analysis, showing great application potential.

**Keywords:** Chest X-rays · Deep Learning · CNN · Transfer Learning

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_24) 24.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 251–261, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_24)\_24

# **1 Introduction**

Numerous studies have applied deep learning techniques to COVID-19 for both diagnosis and prognosis. While most focus on image-based AI solutions for COVID-19 diagnosis [\[28](#page-32-0)], prognosis prediction receives less attention [\[6\]](#page-31-0). Many prognostic studies employ single-task learning (STL) methods to predict outcomes like intensive care unit (ICU) admission or mortality [\[2,](#page-30-0)[21,](#page-31-1)[23](#page-31-2)[,24,](#page-31-3)[26\]](#page-32-1). Alternative approaches predict patient severity progression [\[14](#page-31-4)[,17](#page-31-5),[25,](#page-31-6)[27\]](#page-32-2), using datasets like AIforCOVID [\[30\]](#page-32-3), to classify patients into mild and severe based on treatment outcomes. Some STL models assess COVID-19 pneumonia severity from CXR using structured severity scores [\[8,](#page-31-7)[9](#page-31-8)[,34](#page-32-4)], like the Brixia-score [\[29](#page-32-5)] that segments the lungs into six areas for severity grading  $[4]$ , with these scores statistically linked to treatment outcomes, underlining their prognostic value [\[5\]](#page-30-2).

Alternately, multi-task learning (MTL) trains models on multiple related tasks simultaneously to enhance reciprocal learning [\[7](#page-31-9)]. In healthcare, studies show that combining two relevant tasks can boost model performance, utilizing various task combinations, architectures for parameter sharing, or loss functions [\[33](#page-32-6)]. Specifically for COVID-19, MTL has been applied to diagnostic predictions and lesion segmentation on imaging data [\[1,](#page-30-3)[15](#page-31-10)], with some studies exploring the synergy between diagnosis and prognosis for a more integrated disease perspective [\[3](#page-30-4)[,31](#page-32-7)]. While MTL exhibits promising performance, its primary application remains within datasets having multiple labels per instance. Integrating different data sources for distinct tasks is challenging due to the need for universal annotations across datasets, requiring substantial efforts in data harmonization.

Research in MTL is evolving towards multi-dataset multi-task (MDMT) learning approaches, aiming to leverage diverse data sources to uncover indirect task connections beyond direct data instance links [\[16](#page-31-11)[,18,](#page-31-12)[19](#page-31-13)[,32](#page-32-8)]. This strategy, akin to transfer learning principles, seeks to enrich the learning process by integrating varied datasets. Nonetheless, much of this research does not specifically address medical imaging. For instance, in [\[32](#page-32-8)] it was implemented an MDMT framework on EEG datasets for action recognition with partially overlapping labels, while in [\[19\]](#page-31-13) it was developed a MDMT framework for video-based action recognition, focusing on closely related activities.

Our MDMT learning model aims to predict COVID-19 severity by integrating two distinct datasets, AIforCOVID [\[30\]](#page-32-3) and BRIXIA [\[29](#page-32-5)], each highlighting different facets of the same medical condition. BRIXIA focuses on radiological signs of the disease, showcasing the patient's condition shortly after diagnosis, whereas AIforCOVID forecasts future health status based on clinical evolution. By merging these tasks, we intend to show that combining severity assessments can provide deeper insights into patient outcome predictions, thereby enhancing severity classification accuracy. Furthermore, the use of datasets with different labeling schemes offers an innovative approach in healthcare, allowing for efficient multi-source data integration without the need for extensive relabeling, often required in traditional data merging methods. Our main contributions are as follows: *i*) we introduce a MDMT model for two publicly available COVID-19

Image /page/24/Figure/1 description: This diagram illustrates a machine learning model architecture. Two datasets, D^T1 and D^T2, are fed into a component labeled X. X is then processed by a component fs, resulting in H. H is then processed by two parallel branches, f^T1 and f^T2, producing outputs O^T1 and O^T2 respectively. Each branch also involves a loss calculation, L^T1 and L^T2. The outputs of these loss calculations are combined with a plus sign, leading to a final loss L. Red arrows indicate the forward pass, and blue arrows indicate backpropagation. A legend clarifies that purple and orange circles represent datasets.

<span id="page-24-0"></span>**Fig. 1.** Overview of the MDMT model architecture

CXR datasets, each assigned to a different task; *ii*) we implement a multi-task loss function incorporating an indicator function to facilitate multi-task optimization across data sources; *iii*) through an extensive evaluation using 18 CNN architectures, we demonstrate significant performance gains compared to conventional transfer learning and STL approaches.

<span id="page-24-1"></span>

## **2 Methods**

In the context of the proposed MDMT model, we propose a novel framework designed to leverage shared and task-specific features across multiple datasets, thereby enhancing the model's ability to generalize and perform distinct tasks simultaneously. This approach not only capitalizes on the inherent diversity and complementarity of multi-source data, but also introduces a flexible architecture that adapts to the unique requirements of each task through dedicated heads and a unified backbone for feature extraction. In Fig. [1,](#page-24-0) we report an overview of the proposed MDMT model.

Given two datasets,  $D^{\tau_1}$  and  $D^{\tau_2}$ , each referring to a task  $\tau_1$  and  $\tau_2$ , respectively, a single batch  $X$  is formed by randomly selecting elements such that  $X_i \in D^{\tau_1} \cup D^{\tau_2}$  for any element  $X_i$  in X. A shared backbone feature extraction network, denoted as  $f^s$ , processes X to yield a set of shared features H. This shared representation is then fed into two task-specific fully connected network heads,  $f^{\tau_1}$  and  $f^{\tau_2}$ , for tasks  $\tau_1$  and  $\tau_2$ , respectively, producing outputs  $O^{\tau_1}$  and  $O^{\tau_2}$ . The model employs an indicator function  $\mathcal{I}(X_i, \tau_i)$  to determine the association of a sample  $X_i$  from batch X to a task  $\tau_j$ , crucial for task-specific loss computations. The losses for the tasks  $\tau_1$  and  $\tau_2$ , represented as  $\mathcal{L}^{\tau_1}$  and  $\mathcal{L}^{\tau_2}$ , respectively, are computed using  $O^{\tau_1}$ ,  $Y^{\tau_1}$  and  $O^{\tau_2}$ ,  $Y^{\tau_2}$ , where  $Y^{\tau_1}$  and  $Y^{\tau_2}$ denote the true labels of X for the respective tasks.

In the following, we delve deeper into the specifics of each component within the network architecture, encompassing the shared backbone, the task-specific heads and the MDMT learning mechanism.

In our model, the shared backbone consists of CNN layers that act as a common trainable module for the flow of information regardless of the tasks. The output of this shared network is represented by the function  $H = f<sup>s</sup>(X; \theta<sup>s</sup>)$ , where X denotes the input batch from the combined datasets  $D^{\tau_1}$  and  $D^{\tau_2}$ , and  $\theta^s$  symbolizes the trainable parameters of the CNN backbone. This unified feature extraction mechanism is crucial for capturing generalized representations that are beneficial across multiple tasks.

In our architecture, we enhance the shared CNN backbone by appending two task-specific heads of fully connected layers, which identify distinct task-specific modules for  $D^{\tau_1}$  and  $D^{\tau_2}$  and their respective tasks. The output vector for each task-specific branch is defined as:

$$
O^{\tau_j} = f^{\tau_j}(H, \theta^{\tau_j}), \quad j \in \{1, 2\}
$$
 (1)

where H is the output from the shared backbone and  $\theta^{\tau_j}$  are the parameters of the task-specific fully connected networks, ensuring a dedicated head for processing the features according to the specific requirements of each task.

The uniqueness of our approach lies in the integration of the two loss functions  $\mathcal{L}^{\tau_1}$  and  $\mathcal{L}^{\tau_2}$  through the adoption of an indicator function, inspired by Lee et al.'s approach to handling censored data in overall survival analysis [\[22](#page-31-14)]. This adaptation is crucial given the context where, within a mixed batch, no samples bear labels for both tasks concurrently, making it impractical to directly apply a loss function across different datasets. Hence, the proposed total loss function,  $\mathcal{L}$ , is crafted as:

$$
\mathcal{L} = \sum_{i=1}^{|X|} \mathcal{I}(X_i, \tau_1) \cdot \mathcal{L}^{\tau_1}(O_i^{\tau_1}, Y_i^{\tau_1}) + \mathcal{I}(X_i, \tau_2) \cdot \mathcal{L}^{\tau_2}(O_i^{\tau_2}, Y_i^{\tau_2})
$$
(2)

where  $|X|$  is the number of instances in X.

The indicator function  $\mathcal{I}(X_i, \tau_i)$ , plays a pivotal role in the MDMT model by allowing the integration of multi-source data into a unified framework, especially when computing task-specific losses. This function is designed to identify the association of a given sample with its specific task, which is crucial for models that handle data from multiple datasets to perform multiple task optimization simultaneously. It can be defined as:

$$
\mathcal{I}(X_i, \tau_j) = \begin{cases} 1 & \text{if sample } X_i \text{ is associated with task } \tau_j \\ 0 & \text{otherwise.} \end{cases}
$$

Since the batch X contains elements from both datasets  $D^{\tau_1}$  and  $D^{\tau_2}$ , and each dataset may be associated with different tasks,  $\mathcal{I}(X_i, \tau_i)$  helps to selectively compute the loss for each task by including only those samples related to the task in question. The use of an indicator function in this manner adds flexibility and scalability to the model. It allows for the seamless addition of new tasks or datasets by simply adjusting the indicator function to include new elements in the loss computation process without needing significant architectural changes. In the proposed MDMT model, the training process is designed to be end-toend, indicating that all model components are trained simultaneously during the training phase. This approach ensures that the model learns to optimize its parameters in a cohesive manner, leveraging gradients derived from the total loss to update each component of the architecture.

For the two task-specific loss functions, we selected  $\mathcal{L}^{\tau_1}$  and  $\mathcal{L}^{\tau_2}$  considering each task characteristics and labeling schemes, as detailed in Sect. [3.](#page-26-0) Given that  $\tau_1$ , for the severity group prediction, and  $\tau_2$ , for the severity assessment, are both classification tasks, we selected the Cross-Entropy Loss, defined as follows:

$$
\mathcal{L}^{\tau_j}(O_i^{\tau_j}, Y_i^{\tau_j}) = -\sum_{k=1}^{c^{\tau_j}} Y_{i,k}^{\tau_j} \log(O_{i,k}^{\tau_j})
$$
\n(3)

where  $Y_i^{\tau_j}$  is the real label,  $O_i^{\tau_j}$  the predicted probabilities for the sample  $X_i$  and  $c^{\tau_j}$  is the number of class labels for the considered task  $\tau_j$ . These loss functions are tailored to the distinct characteristics of each task within the model, allowing for precise adjustments to the model parameters based on the nature of the tasks at hand. The ability to compute task-specific losses accurately is crucial for the model to balance learning across tasks, preventing the dominance of any singletask over the others and promoting a synergistic improvement in performance.

<span id="page-26-0"></span>

### **3.1 Datasets and Tasks**

This section presents our evaluation framework for the MDMT model against conventional approaches, detailing the datasets, preprocessing steps and experimental configurations.

### **3.2 Experimental Configurations**

Our study leverages two distinct and publicly available CXR datasets, namely AIforCOVID [\[30\]](#page-32-3) and BRIXIA [\[29\]](#page-32-5), each assigned to a distinct task: severity prognosis and severity assessment, respectively. These datasets were specifically chosen for their complementary characteristics, enabling a comprehensive assessment of the model's ability to generalize across different tasks and data distributions.

The AIforCOVID dataset  $D^{\tau_1}$  encompasses 1586 CXR examinations from COVID-19 positive adult patients, confirmed via RT-PCR tests, from six different centers. This dataset has been compiled into three releases, each expanding the dataset's size: the initial release included 820 patients, followed by an additional 284 patients, and concluded with an additional 482 patients. Patients are classified into two categories based on treatment outcomes: mild (home isolation or hospitalization without ventilatory support) and severe (requiring noninvasive ventilation, ICU admission, or resulting in death), making the task a binary classification problem, with the target variable  $Y_i^{\tau_1} \in \{0,1\}$  representing these outcomes.

The BRIXIA dataset  $D^{\tau_2}$  consists of 4707 CXR images from COVID-19 positive patients treated in sub-intensive and intensive care units, obtained from various machine manufacturers and varied acquisition parameters. A noteworthy feature of this dataset is the Brixia score, which grades lung opacity across six regions on a scale of 0 to 3. To simplify the scoring system, we sum the regional scores into a global score  $G_i$  for each image  $X_i$ . This score is then categorized into four severity levels through a threshold function:

$$
Y_i^{\tau_2} = \begin{cases} G_i < 5, \qquad \text{Category } 0\\ 5 \le G_i < 9, \qquad \text{Category } 1\\ 9 \le G_i < 14, \qquad \text{Category } 2\\ G_i \ge 14, \qquad \text{Category } 3 \end{cases} \tag{4}
$$

This categorization yields a multi-class target variable  $Y_i^{\tau_2} \in \{0, 1, 2, 3\}$ , facilitating the model's task of predicting the severity of lung involvement in COVID-19 patients.

To ensure the data from both AIforCOVID and BRIXIA datasets are coherent and prepared for our model, we employed a preprocessing pipeline that includes lung segmentation via a U-NET model, bounding-box extraction, resizing and normalization. For ease of reproducibility, these steps are the same used in [\[10,](#page-31-15)[12](#page-31-16)[–14](#page-31-4)].

### **3.3 Experimental Configurations**

To comprehensively validate the efficacy of our MDMT model, we designed three distinct experimental configurations, aimed at demonstrating the model's superiority over traditional STL and fine-tuning approaches.

We establish a baseline by training the CNN backbone on each dataset and task separately. This step serves as a foundation for comparison, highlighting the potential enhancements brought by MTL. The experiments are denoted as  $STL^{\tau_1}$  and  $STL^{\tau_2}$  for  $D^{\tau_1}$  and  $D^{\tau_2}$  datasets, respectively. Each backbone utilized in this phase is pre-trained on the ImageNet dataset [\[20\]](#page-31-17).

As a comparative benchmark, we explore a conventional fine-tuning strategy where the model pre-trained on the BRIXIA dataset  $STL^{\tau_2}$  is fine-tuned for the AIforCOVID task  $D^{\tau_1}$ . This experiment, denoted as FT, investigates the effectiveness of transferring learned features from one task to another, contrasting it against the integrated learning approach of the MDMT model.

The proposed MDMT Learning model employs as pre-training the average of the weights from the baseline models  $STL^{\tau_1}$  and  $STL^{\tau_2}$ . This experiment, denoted *MDMT*, allows simultaneous training on both tasks, leveraging shared and task-specific features, as detailed in Sect. [2.](#page-24-1)

To evaluate the robustness of the proposed methodology, all the aforementioned experiments are conducted on 18 CNN backbones across 6 major architectural families: DenseNet, EfficientNet, GoogLeNet, MobileNet, ResNet and ShuffleNet. For all experiments and networks, uniform training configurations were applied. Optimization was carried out using the Adam optimizer, with an initial learning rate of 0.001, momentum of 0.9, and weight decay of 0.0001.

| Experiment     | CV          |             |             | LOCO        |             |             |
|----------------|-------------|-------------|-------------|-------------|-------------|-------------|
|                | ACC         | F1          | GM          | ACC         | F1          | GM          |
| $STL^{\tau_1}$ | 66.0        | 63.9        | 65.9        | 61.2        | 57.3        | 60.1        |
| FT             | 67.3        | 63.1        | 66.9        | 64.8        | 58.9        | 63.8        |
| <b>MDMT</b>    | <b>68.6</b> | <b>66.6</b> | <b>68.5</b> | <b>65.7</b> | <b>64.3</b> | <b>66.0</b> |

<span id="page-28-0"></span>**Table 1.** Average model performance metrics for task  $\tau_1$ , calculated across different backbone architectures.

<span id="page-28-1"></span>**Table 2.** Statistical comparison of *MDMT* vs  $STL<sup>71</sup>$  and *MDMT* vs *FT*. Significance levels are marked with  $*$  for a p-value  $< 0.05$ ,  $**$  for a p-value  $< 0.01$  and  $** *$  for a  $p$ -value  $< 0.001$ .

| Statistic | Test                     | CV    |       |       | LOCO  |       |       |
|-----------|--------------------------|-------|-------|-------|-------|-------|-------|
|           |                          | ACC   | F1    | GM    | ACC   | F1    | GM    |
| $\mu$     | $MDMT$ vs $STL^{\tau_1}$ | $***$ | $***$ | $***$ | $***$ | $***$ | $***$ |
|           | $MDMT$ vs $FT$           | $**$  | $***$ | $**$  |       |       |       |
| $\sigma$  | $MDMT$ vs $STL^{\tau_1}$ | $*$   | $*$   | $*$   | $*$   |       | $*$   |
|           | $MDMT$ vs $FT$           |       |       |       | $*$   |       | $*$   |

Training included a 40-epoch warm-up period and was limited to a maximum of 300 epochs, incorporating early stopping after 40 epochs to prevent overfitting. All training sessions were conducted on four NVIDIA TESLA A100 GPUs, utilizing a batch size of 128.

To ensure the reliability and generalizability of our findings, we employ both 5-fold stratified cross-validation (CV) and leave-one-center-out (LOCO) validation strategies. By evaluating the Accuracy (ACC), F1-score (F1) and G-mean (GM), we perform a comprehensive evaluation of the model's performance across different partitions of the data, highlighting its ability to generalize to unseen data and mitigate bias towards specific data distributions.

### **4 Results and Discussions**

To evaluate the effectiveness of our proposed MDMT method, we compared our MDMT framework with each experimental configuration, validation strategy and CNN backbone employed, as shown in Table [1](#page-28-0) and in more detail in Table S1. We opted to discuss the results obtained on  $\tau_1$  task, given its unique relationship with the task  $\tau_2$ . The task  $\tau_2$  not only temporally precedes  $\tau_1$  but also possesses strong predictive power for prognostic prediction, therefore incorporating  $\tau_2$  can lead to performance improvement on  $\tau_1$ .

To provide a comprehensive overview of our results, Table [1](#page-28-0) displays the average performance metrics across all experimental configurations and CNN architectures tested in our study. Our analysis reveals a clear superiority of the MDMT approach over the conventional methodologies, including both the STL and fine-tuning strategies. In Table S1, all the performances for all the shared backbones are reported. These results not only confirm MDMT's superior average performance, but also demonstrate that our method achieves the highest absolute performance over backbone architectures, experimental scenarios and validation strategies.

To rigorously evaluate the effectiveness of the proposed MDMT method, we employed a single-tail t-test for statistical comparisons across the various experimental configurations. This analysis involved assessing both the average performance metrics  $\mu$  and their standard deviations  $\sigma$  across test folds. Our goal was to determine whether there was a statistically significant increase in  $\mu$ and a decrease in  $\sigma$  for *MDMT* in comparison to the  $STL^{\tau_1}$  and the *FT* settings. Notably, an increase in  $\mu$  indicates a significant increase in performance and a reduction of  $\sigma$  indicates enhanced model robustness. The findings, detailed in Table [2,](#page-28-1) show the statistical significance achieved by MDMT.

Analyzing the results in CV, we observed that the *MDMT* method consistently outperformed the  $STL^{\tau_1}$  and the *FT* approaches in terms of average performance metrics across the CNN architectures, further validated through statistical comparisons. When compared to  $STL^{\tau_1}$ ,  $MDMT$  not only demonstrates superior performance across all metrics but also shows a reduction in standard deviation, indicating both higher performance and increased robustness. In contrast, while *MDMT* exhibits statistically significant performance enhancements over *FT*, the improvement in robustness is not statistically significant. To clarify, we conducted additional tests on  $\sigma$ , confirming that *MDMT*'s robustness is statistically comparable to that of the fine-tuning strategies.

In the LOCO validation scenario, *MDMT* demonstrated superior performance relative to both the  $STL^{T_1}$  and the *FT* methods. This performance superiority underscores the robustness of *MDMT* across various backbone architectures and its enhanced ability to generalize across different clinical settings. The statistical analysis highlights a significant improvement in performance metrics for  $MDMT$  compared to  $STL^{\tau_1}$ . Regarding the standard deviation, the reduction in variability for ACC and F1 metrics suggests increased reliability of *MDMT* over the compared methods. To address any potential concerns about non-significant differences, we conducted one-tail t-tests, which confirmed that *MDMT*'s performance is not statistically inferior to that of the *FT* approach.

For brevity, we presented results only from the final release, though all experiments were conducted across all three releases of the AIforCOVID dataset, yielding consistent results and conclusions. Furthermore, all experiments and analyses were similarly conducted on  $\tau_2$ , showing no significant change in performance with respect to the  $STL^{\tau_2}$  and the corresponding  $FT$ , underscoring that while  $\tau_2$  benefits  $\tau_1$ , the converse is not necessarily true.

## **5 Conclusion**

In this study, we introduced a MDMT approach aimed at enhancing the generalization capabilities of deep neural networks for COVID-19 prognosis prediction. Our approach demonstrates thorough an extensive analysis that performance improvements are achievable by extracting prognostic information from multiple datasets. Our method not only showcases higher performance across various experimental benchmarks but also establishes itself as a robust solution within the biomedical field for the integration of diverse datasets for different tasks into a unified training framework, leading to a multi-perspective analysis of the disease.

However, the application of this approach is limited by the need to select datasets and tasks that follow a well-determined logic, ensuring that the different prognostic labelling schemas correlate with each other. In future work, we plan to develop a model capable of processing prognostic information from a broader range of heterogeneous datasets, together with the adoption of explainable AI techniques [\[11\]](#page-31-18) to understand the importance of each task and the integration of multimodal data aimed to achieve a holistic understanding of the disease's aspects, all with the aim to achieve more precise, trustworthy and complete systems.

**Acknowledgments.** Filippo Ruffini is a PhD student enrolled in the National PhD in Artificial Intelligence, XXXVIII cycle, course on Health and life sciences, organized by Universit`a Campus Bio-Medico di Roma. This work was partially supported by: i) the Italian Ministry of Foreign Affairs and International Cooperation, grant number PGR01156, ii) PNRR MUR project PE0000013-FAIR, iii) PNRR - DM 118/2023. Resources are provided by the National Academic Infrastructure for Supercomputing in Sweden (NAISS) and the Swedish National Infrastructure for Computing (SNIC) at Alvis @ C3SE, partially funded by the Swedish Research Council through grant agreements no. 2022-06725 and no. 2018-05973.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

### **References**

- <span id="page-30-3"></span>1. Amyar, A., et al.: Multi-task deep learning based CT imaging analysis for COVID-19 pneumonia: Classification and segmentation. Comput. Biol. Med. **126**, 104037 (2020)
- <span id="page-30-0"></span>2. Bae, J., et al.: Predicting mechanical ventilation and mortality in COVID-19 using radiomics and deep learning on chest radiographs: a multi-institutional study. Diagnostics **11**(10), 1812 (2021)
- <span id="page-30-4"></span>3. Bao, G., et al.: COVID-MTL: Multitask learning with Shift3D and randomweighted loss for COVID-19 diagnosis and severity assessment. Pattern Recogn. **124**, 108499 (2022)
- <span id="page-30-1"></span>4. Borghesi, A., et al.: COVID-19 outbreak in Italy: experimental chest X-ray scoring system for quantifying and monitoring disease progression. Radiol. Med. (Torino) **125**(5), 509–513 (2020)
- <span id="page-30-2"></span>5. Borghesi, A., et al.: Chest X-ray versus chest computed tomography for outcome prediction in hospitalized patients with COVID-19. Radiol. Med. (Torino) **127**(3), 305–308 (2022)

- <span id="page-31-0"></span>6. Buttia, C., et al.: Prognostic models in COVID-19 infection that predict severity: a systematic review. Eur. J. Epidemiol. **38**(4), 355–372 (2023)
- <span id="page-31-9"></span>7. Caruana, R.: Multitask learning. Machine learning **28**, 41–75 (1997)
- <span id="page-31-7"></span>8. Cohen, J.P., et al.: Predicting covid-19 pneumonia severity on chest x-ray with deep learning. Cureus **12**(7) (2020)
- <span id="page-31-8"></span>9. Danilov, V.V., et al.: Automatic scoring of COVID-19 severity in X-ray imaging based on a novel deep learning workflow. Sci. Rep. **12**(1), 12791 (2022)
- <span id="page-31-15"></span>10. Guarrasi, V., et al.: A multi-expert system to detect COVID-19 cases in X-ray images. In: 2021 IEEE 34th International Symposium on Computer-Based Medical Systems (CBMS). pp. 395–400. IEEE (2021)
- <span id="page-31-18"></span>11. Guarrasi, V., et al.: Multimodal explainability via latent shift applied to COVID-19 stratification. Pattern Recogn. **156**, 110825 (2024)
- <span id="page-31-16"></span>12. Guarrasi, V., et al.: Optimized fusion of CNNs to diagnose pulmonary diseases on chest X-Rays. In: International Conference on Image Analysis and Processing. pp. 197–209. Springer (2022)
- 13. Guarrasi, V., et al.: Pareto optimization of deep networks for COVID-19 diagnosis from chest X-rays. Pattern Recogn. **121**, 108242 (2022)
- <span id="page-31-4"></span>14. Guarrasi, V., et al.: Multi-objective optimization determines when, which and how to fuse deep networks: An application to predict COVID-19 outcomes. Comput. Biol. Med. **154**, 106625 (2023)
- <span id="page-31-10"></span>15. He, K., et al.: Synergistic learning of lung lobe segmentation and hierarchical multi-instance classification for automated severity assessment of COVID-19 in CT images. Pattern Recogn. **113**, 107828 (2021)
- <span id="page-31-11"></span>16. Hosseini, S., et al.: Distill-2MD-MTL: Data distillation based on multi-dataset multi-domain multi-task frame work to solve face related tasksks, multi task learning, semi-supervised learning. arXiv preprint [arXiv:1907.03402](http://arxiv.org/abs/1907.03402) (2019)
- <span id="page-31-5"></span>17. Jiao, Z., et al.: Prognostication of patients with COVID-19 using artificial intelligence based on chest x-rays and clinical data: a retrospective study. The Lancet Digital Health **3**(5), e286–e294 (2021)
- <span id="page-31-12"></span>18. Kaiser, L., et al.: One model to learn them all. arXiv preprint [arXiv:1706.05137](http://arxiv.org/abs/1706.05137) (2017)
- <span id="page-31-13"></span>19. Kapidis, G., et al.: Multi-dataset, multitask learning of egocentric vision tasks. IEEE Transactions on Pattern Analysis and Machine Intelligence (2021)
- <span id="page-31-17"></span>20. Ke, A., et al.: CheXtransfer: performance and parameter efficiency of ImageNet models for chest X-Ray interpretation. In: Proceedings of the conference on health, inference, and learning. pp. 116–124 (2021)
- <span id="page-31-1"></span>21. Kulkarni, A.R., et al.: Deep learning model to predict the need for mechanical ventilation using chest X-ray images in hospitalised patients with COVID-19. BMJ innovations pp. bmjinnov–2020 (2021)
- <span id="page-31-14"></span>22. Lee, C., et al.: Deephit: A deep learning approach to survival analysis with competing risks. In: Proceedings of the AAAI conference on artificial intelligence. vol. 32 (2018)
- <span id="page-31-2"></span>23. Lee, J.H., et al.: Development and Validation of a Multimodal-Based Prognosis and Intervention Prediction Model for COVID-19 Patients in a Multicenter Cohort. Sensors **22**(13), 5007 (2022)
- <span id="page-31-3"></span>24. Li, H., et al.: Predicting intensive care need for COVID-19 patients using deep learning on chest radiography. Journal of Medical Imaging **10**(4), 044504 (2023)
- <span id="page-31-6"></span>25. Li, Z., et al.: A multistage multimodal deep learning model for disease severity assessment and early warnings of high-risk patients of COVID-19. Front. Public Health **10**, 982289 (2022)

- <span id="page-32-1"></span>26. Rahman, T., et al.: BIO-CXRNET: a robust multimodal stacking machine learning technique for mortality risk prediction of COVID-19 patients using chest X-ray images and clinical data. Neural Computing and Applications pp. 1–23 (2023)
- <span id="page-32-2"></span>27. Schöning, V., et al.: Development and validation of a prognostic COVID-19 severity assessment (COSA) score and machine learning models for patient triage at a tertiary hospital. J. Transl. Med. **19**(1), 1–11 (2021)
- <span id="page-32-0"></span>28. Shi, F., et al.: Review of artificial intelligence techniques in imaging data acquisition, segmentation, and diagnosis for COVID-19. IEEE Rev. Biomed. Eng. **14**, 4–15 (2020)
- <span id="page-32-5"></span>29. Signoroni, A., et al.: BS-Net: Learning COVID-19 pneumonia severity on a large chest X-ray dataset. Med. Image Anal. **71**, 102046 (2021)
- <span id="page-32-3"></span>30. Soda, P., et al.: AIforCOVID: Predicting the clinical outcomes in patients with COVID-19 applying AI to chest-X-rays. An Italian multicentre study. Medical image analysis **74**, 102216 (2021)
- <span id="page-32-7"></span>31. Wang, S., et al.: A fully automatic deep learning system for COVID-19 diagnostic and prognostic analysis. European Respiratory Journal **56**(2) (2020)
- <span id="page-32-8"></span>32. Xie, Y., et al.: Cross-dataset transfer learning for motor imagery signal classification via multi-task learning and pre-training. J. Neural Eng. **20**(5), 056037 (2023)
- <span id="page-32-6"></span>33. Zhao, Y., et al.: Multi-task deep learning for medical image computing and analysis: A review. Comput. Biol. Med. **153**, 106496 (2023)
- <span id="page-32-4"></span>34. Zhu, J., et al.: Deep transfer learning artificial intelligence accurately stages COVID-19 lung disease severity on portable chest radiographs. PLoS ONE **15**(7), e0236621 (2020)

Image /page/33/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a curved line segment on the left and a flag shape on the right. The text below the icon reads "Check for updates".

# **PEMMA: Parameter-Efficient Multi-Modal Adaptation for Medical Image Segmentation**

Nada Saadi(B), Numan Saeed, Mohammad Yaqub, and Karthik Nandakumar

Mohamed bin Zayed University of Artificial Intelligence, Abu Dhabi, UAE {nada.saadi,numan.saeed,mohammad.yaqub,karthik.nandakumar}@mbzuai.ac.ae

**Abstract.** Imaging modalities such as Computed Tomography (CT) and Positron Emission Tomography (PET) are key in cancer detection, inspiring Deep Neural Networks (DNN) models that merge these scans for tumor segmentation. When both CT and PET scans are available, it is common to combine them as two channels of the input to the segmentation model. However, this method requires both scan types during training and inference, posing a challenge due to the limited availability of PET scans, thereby sometimes limiting the process to CT scans only. Hence, there is a need to develop a flexible DNN architecture that can be trained/updated using only CT scans but can effectively utilize PET scans when they become available. In this work, we propose a **p**arameter**e**fficient **m**ulti-**m**odal **a**daptation (PEMMA) framework for lightweight upgrading of a transformer-based segmentation model trained only on CT scans to also incorporate PET scans. The benefits of the proposed approach are two-fold. Firstly, we leverage the inherent modularity of the transformer architecture and perform low-rank adaptation (LoRA) of the attention weights to achieve parameter-efficient adaptation. Secondly, since the PEMMA framework attempts to minimize cross-modal entanglement, it is possible to subsequently update the combined model using only one modality, without causing catastrophic forgetting of the other modality. Our proposed method achieves comparable results with the performance of early fusion techniques with just 8% of the trainable parameters, especially with a remarkable +28% improvement on the average dice score on PET scans when trained on a single modality.

**Keywords:** Multi-modal Adaptation · Low-rank Adaptation · Parameter-Efficiency · Cross-modal Entanglement · 3D Medical Image Segmentation

## **1 Introduction**

In clinical practice, precisely identifying cancerous tumors through various imaging techniques presents a significant challenge due to the intricate nature of the

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_25) 25.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 262–271, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_25)\_25

disease. The necessity to interpret numerous imaging modalities complicates and lengthens the diagnostic and prognostic process, often making it arduous and monotonous. Consequently, there is a critical need for the advancement of Deep Neural Network (DNN) models that can automate this process. Moreover, there is a need to refine the integration of diverse modalities, mirroring the nuanced fusion approach instinctively employed by oncologists, thereby substantially enhancing accuracy and minimizing the intra- and inter-observer variability.

It is well-known that integration of both CT and PET imaging modalities using DNNs can significantly enhance the accuracy of tumor segmentation [\[8\]](#page-41-0). This can be attributed to the comprehensive evaluation of anatomical structures by the CT scans and the ability of PET to capture metabolic activity. However, the availability of PET scans may be restricted in practice due to cost and clinical necessity. Joint processing of these two imaging modalities has been extensively studied in the literature [\[11](#page-42-0)], [\[6\]](#page-41-1). These approaches involve either merging the modalities at the input level as channels [\[3](#page-41-2)] or at the output level after independent processing of each modality [\[9\]](#page-41-3). While the former approach assumes constant availability of both modalities, the latter approach doubles the number of trained parameters [\[12](#page-42-1)]. Hence, there is a need for more sophisticated method for jointly handling CT and PET scans.

In terms of DNN architecture used to process CT and PET modalities, several models such as Convolutional Neural Networks (CNNs), Graph Neural Networks, and transformers have been studied in the literature [\[10](#page-41-4)]. While CNN-based models have demonstrated impressive performance in various applications, opting for transformer-based models for segmentation tasks offers several advantages. Specifically, due to their unparalleled ability to capturelong-range dependencies andintricate patternswithin the data, transformer basedmodels canlead tomore refined and accurate outcomes. In this context, the UNETR model  $[4]$  $[4]$  marks a significant departure from traditional CNNs and uses the Vision Transformer (ViT) architecture. In general, the image encoder in a transformer-based segmentation model converts the given image **x** into a set of N patch tokens  $\mathcal{T}_0 = {\mathbf{t}_1, \cdots, \mathbf{t}_N}$  via a patch embedding layer  $\mathcal{E}_{\theta_{\text{PE}}}$ , where  $\theta_{\text{PE}}$  denotes the patch embedding parameters. These tokens are processed through a sequence of  $L$  identical self-attention transformer blocks, where the operation of each block can be expressed as  $\mathcal{T}_{\ell} = \mathcal{G}_{\theta_{\ell}}(\mathcal{T}_{\ell-1})$ , where  $\theta_{\ell}$  represents the parameters of the  $\ell^{th}$  transformer block,  $\ell \in [1, L]$ .

In this work, we address the problem of efficient multi-modal adaptation of a transformer-based tumor segmentation model. Suppose that a healthcare institution already has access to a pre-trained segmentation model based on CT scans. If new data containing both CT and PET scans becomes available, adapting the existing model to effectively utilize the PET information is not straightforward. We explore whether recent developments in parameter-efficient fine-tuning (PEFT) techniques such as Low-Rank Adaptation (LoRA) [\[5\]](#page-41-6) can be leveraged to bridge the above gap. LoRA is one of the most PEFTs for adapting large language models like GPT-3 (with 175 billion parameters) to downstream tasks. It is based on the hypothesis that weight updates to the multi-head self-attention (MHSA) layer of a transformer during adaptation have a low intrinsic rank. Hence, LoRA injects trainable low-rank matrices in parallel to the attention layer while keeping the pre-trained model weights frozen. Based on LoRA, we propose a Parameter Efficient Multimodal Adaptation Approach (PEMMA) that makes best use of the existing pre-trained model for CT and progressively integrates additional modalities (e.g., PET). Our contributions are:

- We propose a method that offers an efficient incorporation of new modalities into an existing model with minimal cross-modal entanglement.
- We demonstrate the feasibility of flexibly and efficiently fine-tuning the adapted model when only one modality is available.
- We show how our approach can effectively retain the knowledge learned in the previous steps when trained on new data.

## **2 Methodology**

**Problem Statement:** Suppose that a pre-trained transformer-based tumor segmentation model  $\mathcal{F}_{\Theta}^C : \mathcal{X}_{C} \to \mathcal{M}$  for CT scans is available, where  $\mathcal{F}^C$  denotes the uni-modal (CT) model architecture with parameters  $\Theta$ ,  $\mathcal{X}_C$  is the input space of CT scans, and M represents the output segmentation mask space. Our *primary* **Problem Statement:** Suppose that a pre-trained transform mentation model  $\mathcal{F}_G^C : \mathcal{X}_C \to \mathcal{M}$  for CT scans is available, we uni-modal (CT) model architecture with parameters  $\Theta$ ,  $\mathcal{X}_C$  CT scans, and  $\mathcal M$  re  $\widetilde{C}^{CP}_\Phi : \mathcal{X}_C \times \mathcal{X}_P \to \mathcal{M}$ can utilize both CT and PET scans to achieve **better segmentation accuracy**, mentation model  $\mathcal{F}_{\Theta}^{\vee} : \mathcal{X}_{C} \to \mathcal{M}$  for CT scans is available, where  $\mathcal{F}^{\vee}$  denotes the<br>uni-modal (CT) model architecture with parameters  $\Theta$ ,  $\mathcal{X}_{C}$  is the input space of<br>CT scans, and  $\mathcal{M}$  eters  $\Phi$  and  $\mathcal{X}_P$  is the input space of PET scans. We also have two secondary objectives: (i) the model adaptation must be **parameter-efficient**, i.e., the number of parameters in the multi-modal model (denoted as  $|\Phi|$ ) must be close to that of the uni-modal model (denoted as  $|\Theta|$ ), and (ii) the adaptation must **minimize crossmodal entanglement**, i.e., it should be possible to subsequently fine-tune the eters  $\varPhi$  and  $\mathcal{X}_P$  is the i<br>tives: (i) the model ad<br>parameters in the mul<br>uni-modal model (der<br>**modal entangleme**<br>multi-modal model  $\widetilde{\mathcal{F}}$  $C_P^{CP}$  using only one modality (either CT or PET scans), without causing catastrophic forgetting of the other modality.

### **2.1 Standard Adaptation Methods**

**Early Fusion:** The most common approach to train a multi-modal CT+PET segmentation model is to combine the two modalities as different channels of the same input, i.e., create a new multimodal input space  $\mathcal{X}_{CP}$ , and train a new **2.1 Standard Adaptation Methods**<br> **Early Fusion:** The most common approach to train a multi-modal CT+PET<br>
segmentation model is to combine the two modalities as different channels of<br>
the same input, i.e., create a new m architecture (e.g., UNETR  $[4]$  $[4]$ ) is used, this involves two main changes to the DNN architecture. Firstly, the uni-modal patch embedding (linear projection) layer that transforms the input data to a set of patch tokens is replaced with a new multi-modal patch embedding layer. Secondly, the direct skip connection between the input and the decoder needs to be modified to accommodate the increase in the number of channels. After making these two architectural changes, the entire model can be learned from the available CT+PET training data.

Specifically, let  $\mathbf{x}_C \in \mathcal{X}_C$  be the CT image and  $\mathcal{T}_0^C = \mathcal{E}_{\theta_{PE}^C}(\mathbf{x}_C)$  denote the set of CT patch tokens, where  $\mathcal{E}_{\theta_{\text{PE}}^C}$  is the patch embedding layer of the uni-modal model  $\mathcal{F}_{\Theta}^C$ . Given the PET image  $\mathbf{x}_P \in \mathcal{X}_P$ , we first generate a combined image  $\mathbf{x}_{CP} = [\mathbf{x}_C || \mathbf{x}_P] \in \mathcal{X}_{CP}$ , where || denotes channel-wise concatenation. Next, we train a new multi-modal patch embedding layer  $\mathcal{E}_{\theta_\text{PE}^{CP}},$  which generates a new set

Image /page/36/Figure/1 description: This is a diagram illustrating the architecture of a REMMA model. The model takes PET and CT images as input, processes them through linear projections and patch tokenization. The PET tokens are processed by a trainable LoRA module with multi-head attention and an MLP. The CT tokens are processed through a series of convolutional layers. The outputs from both PET and CT processing are combined at various stages. The diagram shows different layers with specific dimensions and operations like normalization, attention, convolution, and deconvolution. Some layers are marked as trainable (with a fire icon) and others as frozen (with a snowflake icon). The final output is a reconstructed image with dimensions H x W x D x 1.

<span id="page-36-0"></span>**Fig. 1.** *Overview of our proposed architecture PEMMA:* At the input level, we separate the path for CT and PET by adding the PET Skip Connection  $\theta_{SK}^P$ . We freeze both the encoder and decoder part of the base UNetr model and introduce LoRA, after each ViT block (x12) as the only trainable layers.

of CT-PET patch tokens as  $\mathcal{T}_0^{CP} = \mathcal{E}_{\theta_{PE}^{CP}}(\mathbf{x}_{CP})$  that are passed to the subsequent transformer blocks in the encoder. Similarly, let  $\mathbf{z}_C = \mathcal{S}_{\theta_{SK}^C}(\mathbf{x}_C)$  be the output of the direct skip connection layer  $(\mathcal{S}_{\theta_{\rm SK}^C})$  between the input and the decoder in the uni-modal model  $\mathcal{F}_{\Theta}^C$ . Again, we replace  $\mathcal{S}_{\theta_{SK}^C}$  with a new skip connection layer  $\mathcal{S}_{\theta_{SK}^{CP}}$ , which outputs  $\mathbf{z}_{CP} = \mathcal{S}_{\theta_{SK}^{CP}}(\mathbf{x}_{CP})$ .

The advantage of this adaptation approach is good parameter efficiency because only a minimal number of parameters need to be added to the patch embedding and input skip connection layers to account for the increase in the number of channels. However, the main limitation is that features from both CT and PET modalities get entangled in the combined model. Consequently, any attempt to fine-tune this model further using new data from only one modality (say CT only) will lead to the forgetting of the other modality. Since only minor architectural changes are involved in the adaptation, most of the parameters in the combined model can be initialized using the parameters of the pre-trained uni-modal model. However, for the newly introduced PET-related parameters in the combined model, there are three possible initialization strategies: (i) *random initialization* - initial weights are assigned stochastically, (ii) *zero initialization* - initial weights are set to zero, and (iii) *cross-modal initialization* - the weights of the other modality (CT) in the pre-trained model are re-used. The cross-

| Tasks                  | Centers                                                  | Train Samples | Val Samples | Pet/CT Scanner                                                               |
|------------------------|----------------------------------------------------------|---------------|-------------|------------------------------------------------------------------------------|
| Pre-training           | <b>CHUS</b><br><b>CHUV</b><br><b>CHUM</b><br><b>CHUP</b> | 203           | 51          | GeminiGXL 16, Philips, Siemens<br>Biograph mCT 40 TOF,<br>Discovery D690 TOF |
| Multi-Modal Adaptation | MDA                                                      | 152           | 44          | Discovery HR, RX, ST, and STE                                                |
| New Dataset 1          | HGJ                                                      | 39            | 15          | Discovery ST, GE Healthcare                                                  |
| New Dataset 2          | HMR                                                      | 13            | 4           | Discover ST, GE Healthcare                                                   |

<span id="page-37-0"></span>**Table 1.** HECKTOR Dataset description with the division of training and validation data across different centers.

modal initialization approach can significantly accelerate the learning process and enhance model performance by guiding the PET channel with a pre-learned context of medical imaging, thereby facilitating a more effective CT-PET fusion.

Late Fusion: Another straightforward approach to incorporate a new modality such as PET is to train a completely new segmentation model  $\mathcal{F}_{\Psi}^P : \mathcal{X}_P \to \mathcal{M}$ for PET scans, where  $\mathcal{F}^P$  denotes the uni-modal (PET) model architecture with parameters  $\Psi$ , and combine the masks from the two models at the output stage. Let  $M_C \in \mathcal{M}$  and  $M_P \in \mathcal{M}$  be the masks produced by the uni-modal CT  $(\mathcal{F}_{\theta}^C)$ and PET  $(\mathcal{F}_{\Psi}^P)$  models, respectively. The combined mask can be computed as:

$$
M_{CP} = w_C M_C + (1 - w_C) M_P,
$$
\n(1)

where  $w_C$  is the weight assigned to the CT modality. While this method offers a great degree of flexibility in dealing with the dynamic availability of the two modalities during training and/or inference, it results in a two-fold increase in the number of parameters  $(|\Phi| = |\Theta| + |\Psi|)$  and may not provide optimal accuracy.

### **2.2 Proposed Adaptation Method: PEMMA**

In light of the strengths and weaknesses of existing adaptation methods, we introduce a novel framework (see Fig. [1\)](#page-36-0) for lightweight adaptation of a uni-modal model into a multi-model modal leveraging the inherent modularity of the transformer architecture. Our proposed framework, referred to as parameter-efficient multi-modal adaptation (PEMMA), is inspired by the concepts of visual prompt tuning (VPT) [\[7](#page-41-7)] and low-rank adaptation (LoRA) [\[5](#page-41-6)]. The PEMMA framework has three core components. Firstly, we introduce the new PET modality as a set of visual prompts (or context tokens) to the uni-modal model simply by adding a new patch embedding layer. Secondly, instead of fine-tuning all the parameters of the transformer encoder, we focus only on the attention layers and fine-tune these attention layers through LoRA matrices. Finally, instead of replacing the existing input skip layer in the uni-modal model, we add an additional uni-modal skip layer for PET that operates in parallel to the existing skip layer and the outputs of these two layers are linearly combined.

Given the PET image  $\mathbf{x}_P$ , we first add a new PET patch embedding layer  $\mathcal{E}_{\theta_{\text{PE}}^P}$ , which generates a new set of N PET patch tokens as  $\mathcal{T}_0^P = \mathcal{E}_{\theta_{\text{PE}}^P}(\mathbf{x}_P)$ that are passed to the subsequent transformer blocks in the encoder. Now, the operations of a transformer block can be represented as  $\{T_{\ell}^C, T_{\ell}^P\} =$  $\mathcal{G}_{\theta_{\ell}}(\{T_{\ell-1}^C, T_{\ell-1}^P\})$ . Note that the above modification increases the number of tokens processed by the transformer encoder from  $N$  to  $2N$ . However, the decoder in the pre-trained uni-modal model can handle only N tokens. In order to avoid making changes to the decoder architecture, we allow only the N CT tokens from the intermediate transformer blocks  $(\mathcal{T}_{\ell}^C)$  to pass through to the decoder. Refer to Table [2](#page-39-0) in the Appendix for Dimensionality Reduction results. It must be emphasized that the self-attention architecture ensures that the knowledge from the PET tokens gets distilled into the CT tokens, even though the PET tokens are ignored by the decoder layers. This is similar to VPT, where the learnable prompts only serve as the context and this contextual information gets distilled into the other tokens, even when the additional prompts are ignored in the end.

The MHSA parameters of a transformer block  $\ell$  can be considered as a collection of four weight matrices denoted as  $\{W_{O,\ell}, W_{O,\ell}, W_{K,\ell}, W_{V,\ell}\}$ . In LoRA, the updates to  $\mathbf{W}_{Q,\ell}$  and  $\mathbf{W}_{V,\ell}$  are decomposed into a pair of low rank matrices  $\mathbf{A} \in \mathbb{R}^{r \times d}$  and  $\mathbf{B} \in \mathbb{R}^{d \times r}$ , where r represents the rank of the two matrices. Let  $h_{*,\ell}$  and  $\tilde{h}_{*,\ell}$  be the input and output, respectively, of an attention layer in the  $\ell^{\text{th}}$  block. Then, LoRA operation can be summarized as:

$$
\tilde{h}_{Q,\ell} = \mathbf{W}_{Q,\ell} h_{Q,\ell} + \alpha \mathbf{B}_{Q,\ell} \mathbf{A}_{Q,\ell} h_{Q,\ell}
$$
\n
$$
\tilde{h}_{V,\ell} = \mathbf{W}_{V,\ell} h_{V,\ell} + \alpha \mathbf{B}_{V,\ell} \mathbf{A}_{V,\ell} h_{V,\ell}
$$
\n(2)

where  $\alpha$  is a fixed scalar. Note that parameter-efficiency is achieved by allowing  $\theta_{\text{LoRA}} = {\mathbf{A}_{Q,\ell}, \mathbf{A}_{V,\ell}, \mathbf{B}_{Q,\ell}, \mathbf{B}_{V,\ell}}\}_{\ell=1}^L$  as the only learnable parameters and freezing the rest of the parameters in the transformer encoder.

Finally, in contrast to the early fusion approach where  $S_{\theta_{SK}}^C$  is replaced with  $\mathcal{S}_{\theta_{\rm SK}^{CP}}$ , we leave  $\mathcal{S}_{\theta_{\rm SK}^C}$  untouched and introduce an additional parallel path  $\mathcal{S}_{\theta_{\rm SK}^P}$  for the PET image. Let  $\mathbf{z}_P = \mathcal{S}_{\theta_{\mathbb{S}^K}^P}(\mathbf{x}_P)$  be the output of the additional direct skip connection layer introduced for the PET modality. In this case, the combined output of the input skip layer is  $\mathbf{z}_{CP} = \mathbf{z}_C + \beta \mathbf{z}_P$ , where  $\beta$  is the weight assigned to the PET modality. The main motivation for the introduction of new patch embedding  $(\mathcal{E}_{\theta^P_{\text{PE}}})$  and input skip layers  $(\mathcal{S}_{\theta^P_{\text{SK}}})$  for the PET modality (rather than replacing them as in early fusion) is to minimize cross-modal entanglement. With the introduction of these additional layers, it is possible to subsequently fine-tune the multi-modal model using only one of the modalities, without affecting the model's ability to handle the other modality.

**Flexible Training and Inference Strategy** : The PEMMA framework introduces three new parameters  $\theta_{\rm PE}^P$ ,  $\theta_{\rm LoRA}$ , and  $\theta_{\rm SK}^P$ . When adapting the uni-modal model to the multimodal scenario, both CT and PET training data is required and all the new parameters  $\{\theta_{\text{PE}}^P, \theta_{\text{LoRA}}, \theta_{\text{SK}}^P\}$  are learned, while the parameters of the pre-trained unimodal model  $\Theta$  are completely frozen. Thus, the

parameters of the multi-modal model are  $\Phi = {\Theta, \theta_{\text{PE}}^P, \theta_{\text{LoRA}}, \theta_{\text{SK}}^P}$  and  $|\Phi|$  is only marginally higher than  $|\Theta|$  (hence, parameter-efficient). Subsequently, if new data is available to update the multi-modal model, only  $\theta_{\text{LoRA}}$  needs to be updated and all other parameters can be frozen. This allows the flexibility of fine-tuning the multi-modal model using one or both modalities (if a modality is unavailable, the corresponding input can be set to zero). Similarly, the multimodal allows flexible inference - it can effectively utilize both modalities when they are available, but can also be applied to only a single modality (albeit with some degradation in the segmentation accuracy).

<span id="page-39-0"></span>**Table 2.** Results for the different Adaptation Approaches performances on the Training and Inference Modalities. We compare the number of trainable parameters with the UNETR model where  $\Phi = 92.58$  M params. We compute the Tumor, Lymph, and Average Dice scores for each experiment.  $C = CT$ ;  $P = PET$ ;  $CP = CT + PET$ .

| Dataset                                  | $-\cdot$ |      | Multimodal Adaptation |              |      | New Dataset 1  |                |      |               |              |      | New Dataset 2 |              |      |               |              |
|------------------------------------------|----------|------|-----------------------|--------------|------|----------------|----------------|------|---------------|--------------|------|---------------|--------------|------|---------------|--------------|
| Train Modalities                         | $-\cdot$ | CP   |                       |              | C    |                |                | CP   |               |              | Ċ    |               |              | CP   |               |              |
| <b>Infer Modalities</b>                  | $-\cdot$ | CP   | $\mathbf C$           | $\mathbf{P}$ | CP   | $\overline{C}$ | $\overline{P}$ | CP   | $\mathcal{C}$ | $\mathbf{P}$ | CP   | $\mathcal{C}$ | $\mathbf{P}$ | CP   | $\mathcal{C}$ | $\mathbf{P}$ |
| Late Fusion                              | Tumor    | 0.69 | 0.37                  | 0.43         | 0.67 | 0.45           | 0.32           | 0.40 | 0.36          | 0.23         | 0.68 | 0.43          | 0.05         | 0.47 | 0.46          | 0.24         |
|                                          | Lymph    | 0.64 | 0.51                  | 0.51         | 0.61 | 0.56           | 0.21           | 0.48 | 0.46          | 0.27         | 0.56 | 0.52          | 0.32         | 0.30 | 0.34          | 0.11         |
| (params= $2 \phi$ )                      | Avg      | 0.65 | 0.43                  | 0.47         | 0.64 | 0.49           | 0.17           | 0.44 | 0.39          | 0.25         | 0.62 | 0.48          | 0.19         | 0.39 | 0.35          | 0.18         |
|                                          | Tumor    | 0.65 | 0.41                  | 0.68         | 0.76 | 0.71           | 0.17           | 0.81 | 0.42          | 0.30         | 0.82 | 0.34          | 0.01         | 0.63 | 0.35          | 0.42         |
| Early Fusion<br>(params= $1.0043 \phi$ ) | Lymph    | 0.63 | 0.48                  | 0.64         | 0.63 | 0.64           | 0.01           | 0.56 | 0.48          | 0.37         | 0.58 | 0.38          | 0.04         | 0.56 | 0.35          | 0.25         |
|                                          | Avg      | 0.67 | 0.43                  | 0.65         | 0.70 | 0.68           | 0.09           | 0.69 | 0.43          | 0.31         | 0.07 | 0.36          | 0.03         | 0.60 | 0.35          | 0.34         |
|                                          | Tumor    | 0.67 | 0.41                  | 0.64         | 0.82 | 0.70           | 0.29           | 0.81 | 0.43          | 0.31         | 0.86 | 0.38          | 0.34         | 0.64 | 0.35          | 0.39         |
|                                          | Lymph    | 0.60 | 0.50                  | 0.63         | 0.61 | 0.72           | 0.30           | 0.57 | 0.48          | 0.40         | 0.68 | 0.50          | 0.26         | 0.57 | 0.48          | 0.26         |
| PEMMA (Ours)<br>(params= $0.08 \phi$ )   | Avg      | 0.63 | 0.44                  | 0.65         | 0.74 | 0.69           | 0.28           | 0.72 | 0.45          | 0.33         | 0.75 | 0.43          | 0.31         | 0.63 | 0.41          | 0.32         |

## **3 Experimental Set-up**

### **3.1 Dataset Description**

**Pre-processing:** The dataset [\[1\]](#page-41-8) is publicly available on the MICCAI 2022 HEad and neCK TumOR (HECKTOR) challenge website. In total, the dataset compromises 522 samples, with a breakdown provided in Table [1](#page-37-0) detailing distribution across various centers and specifying the scanner types utilized for scan acquisition. The training enhancements applied to both scans involve extracting four random crops sized  $96\times96\times96$ . These augmentations aim to diversify and represent the training data more comprehensively For further details on the pre-processing steps refer to Table [1](#page-37-0) in the Appendix.

**Implementation Details:** Our approach runs on PyTorch version 2.1.0 utilizing the MONAI Library [\[2\]](#page-41-9). We train all models for a maximum of 18k steps and select the best model based on the highest average dice score on validation set. We use a learning rate of 1e-4 and weight decay of 1e-5 for all experiments and train using AdamW optimizer with a batch size of 2. Our entire pipeline runs on a single Nvidia A6000 RTX 48GB GPU.

### **4 Results and Discussion**

Our experimental approach leverages four centers of the HECKTOR Dataset (CHUM, CHUV, CHUP and CHUS) to construct our initial pre-trained unimodal (CT only), as shown in Table [1.](#page-37-0) We then fine-tune both modalities in the multi-modal adaptation setting, leveraging data from MDA center.

We test our method on both joint (CT+PET) and separate modalities. We compare our findings with the standard adaptation approaches defined previously: Early and Late. We observe that PEMMA yields results on par with the early fusion approach, yet it is  $12x$  more efficient. To adapt to new datasets, we introduce the CT and PET scans from two new centers (HGJ and HMR) and design two training scenarios that reflect real-world medical environments: one model trained on CT only (when PET data is missing), and another with both modalities available. PEMMA surpasses both early and late fusion techniques across the various adaptation and inference strategies, particularly in enhancing the extra-modality (PET) tuning, evidenced by a remarkable increase of **+19%** and **+28%** in Average Dice scores for new datasets 1 and 2, respectively.

These outcomes highlight LoRA's effectiveness in progressively integrating additional modality information while significantly reducing the need for comprehensive model retraining, cutting down trainable parameters by **92%**. Finally, the proposed approach can also be useful in a continual learning setting, where new tasks with different data distributions may be introduced sequentially. Since LoRA is highly parameter-efficient, only very few parameters  $(|\theta_{\text{LoBA}}| \ll |\Phi|)$ need to be updated for each task. Hence, it becomes much more feasible to store all task-specific parameters in memory and achieve good forward transfer as well as non-negative backward transfer (thereby circumventing catastrophic forgetting). We, therefore, successfully achieve the parameter efficiency and minimization of the cross-modality entanglement goals.

### **5 Conclusion**

In this study, we introduce a Parameter-Efficient Multimodality Adaptation method to enhance the proposed model's adaptability and efficiency across various data sources and modalities without substantial retraining or a major increase in the number of the model's parameters. This technique allows our model to retain past knowledge and further learn from new data modalities, improving its ability to process evolving multimodal information. Looking ahead, we aim to test this method with other medical imaging types, like MRI, and expand our research to include diverse datasets. Further evaluation is needed to assess the model's reliability and flexibility in a wider range of medical imaging scenarios, potentially creating more effective diagnostic tools. Finally, an interesting research avenue that could be built on our multimodality adaptation approach is to investigate Parameter-Efficient Multi-disease Adaptation to allow DNNs to adapt efficiently and effectively when trained on data from different diseases.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-41-8"></span>1. Andrearczyk, V., Oreiller, V., Boughdad, S., Rest, C.C.L., Elhalawani, H., Jreige, M., Prior, J.O., Vallières, M., Visvikis, D., Hatt, M., Depeursinge, A.: Overview of the hecktor challenge at miccai 2021: Automatic head and neck tumor segmentation and outcome prediction in pet/ct images (2022)
- <span id="page-41-9"></span>2. Cardoso, M.J., Li, W., Brown, R., Ma, N., Kerfoot, E., Wang, Y., Murrey, B., Myronenko, A., Zhao, C., Yang, D., Nath, V., He, Y., Xu, Z., Hatamizadeh, A., Myronenko, A., Zhu, W., Liu, Y., Zheng, M., Tang, Y., Yang, I., Zephyr, M., Hashemian, B., Alle, S., Darestani, M.Z., Budd, C., Modat, M., Vercauteren, T., Wang, G., Li, Y., Hu, Y., Fu, Y., Gorman, B., Johnson, H., Genereaux, B., Erdal, B.S., Gupta, V., Diaz-Pinto, A., Dourson, A., Maier-Hein, L., Jaeger, P.F., Baumgartner, M., Kalpathy-Cramer, J., Flores, M., Kirby, J., Cooper, L.A.D., Roth, H.R., Xu, D., Bericat, D., Floca, R., Zhou, S.K., Shuaib, H., Farahani, K., Maier-Hein, K.H., Aylward, S., Dogra, P., Ourselin, S., Feng, A.: Monai: An open-source framework for deep learning in healthcare (2022)
- <span id="page-41-2"></span>3. Farag, S., IJzerman, N.S., Houdijk, M.P., Reyners, A., Arens, A., Grünhagen, D.J., Desar, I.M., Gelderblom, H., Steeghs, N., Geus-Oei, L.d.: Early response evaluation using 18f-fdg-pet/ct does not influence management of patients with metastatic gastrointestinal stromal tumors (gist) treated with palliative intent. Nuklearmedizin - NuclearMedicine **60**, 411–416 (2021). [https://doi.org/10.1055/](https://doi.org/10.1055/a-1542-6211) [a-1542-6211](https://doi.org/10.1055/a-1542-6211)
- <span id="page-41-5"></span>4. Hatamizadeh, A., Tang, Y., Nath, V., Yang, D., Myronenko, A., Landman, B., Roth, H., Xu, D.: Unetr: Transformers for 3d medical image segmentation (2021)
- <span id="page-41-6"></span>5. Hu, E.J., Shen, Y., Wallis, P., Allen-Zhu, Z., Li, Y., Wang, S., Wang, L., Chen, W.: Lora: Low-rank adaptation of large language models  $(2021)$
- <span id="page-41-1"></span>6. Huang, H., Qiu, L., Yang, S., Li, L., Nan, J., Li, Y., Han, C., Zhu, F., Zhao, C., Zhou, W.: Vision transformer-based multimodal feature fusion network for lymphoma segmentation on pet/ct images (2024)
- <span id="page-41-7"></span>7. Jia, M., Tang, L., Chen, B.C., Cardie, C., Belongie, S., Hariharan, B., Lim, S.N.: Visual prompt tuning (2022)
- <span id="page-41-0"></span>8. Jintao Ren, Jesper Grau Eriksen, J.N., Korreman, S.S.: Comparing different ct, pet and mri multi-modality image combinations for deep learning-based head and neck tumor segmentation. Acta Oncologica **60**(11), 1399–1406 (2021). [https://](https://doi.org/10.1080/0284186X.2021.1949034) [doi.org/10.1080/0284186X.2021.1949034,](https://doi.org/10.1080/0284186X.2021.1949034) [https://doi.org/10.1080/0284186X.2021.](https://doi.org/10.1080/0284186X.2021.1949034) [1949034,](https://doi.org/10.1080/0284186X.2021.1949034) pMID: 34264157
- <span id="page-41-3"></span>9. Kadoury, S., Wood, B.J., Venkatesan, A.M., Dalal, S., Xu, S., Kruecker, J.: Accuracy assessment of an automatic image-based pet/ct registration for ultrasoundguided biopsies and ablations. SPIE Proceedings (2011). [https://doi.org/10.1117/](https://doi.org/10.1117/12.878067) [12.878067](https://doi.org/10.1117/12.878067)
- <span id="page-41-4"></span>10. Peng, J., Peng, L., Zhou, Z., Han, X., Xu, H., Lu, L., Lv, W.: Multi-level fusion graph neural network: Application to pet and ct imaging for risk stratification of head and neck cancer. Biomedical Signal Processing and Control **92**, 106137 (2024). [https://doi.org/10.1016/j.bspc.2024.106137,](https://doi.org/10.1016/j.bspc.2024.106137) [https://www.](https://www.sciencedirect.com/science/article/pii/S1746809424001952) [sciencedirect.com/science/article/pii/S1746809424001952](https://www.sciencedirect.com/science/article/pii/S1746809424001952)

- <span id="page-42-0"></span>11. Saeed, N., Sobirov, I., Al Majzoub, R., Yaqub, M.: Tmss: An end-to-end transformer-based multimodal network for segmentation and survival prediction. In: Wang, L., Dou, Q., Fletcher, P.T., Speidel, S., Li, S. (eds.) Medical Image Computing and Computer Assisted Intervention – MICCAI 2022. pp. 319–329. Springer Nature Switzerland, Cham (2022)
- <span id="page-42-1"></span>12. Wang, Y.e.a.: Deep learning based time-to-event analysis with pet, ct and joint pet/ct for head and neck cancer prognosis. Computer Methods and Programs in Biomedicine **222**, 106948 (2022). <https://doi.org/10.1016/j.cmpb.2022.106948>

Image /page/43/Picture/0 description: A square button with rounded corners contains a circular icon and text. The icon is a circle with a ribbon or bookmark shape inside it. The text below the icon reads "Check for updates".

# **Reducing Annotation Burden: Exploiting Image Knowledge for Few-Shot Medical Video Object Segmentation via Spatiotemporal Consistency Relearning**

Zixuan Zheng<sup>1</sup>, Yilei Shi<sup>1</sup>, Chunlei Li<sup>1</sup>, Jingliang Hu<sup>1</sup>, Xiao Xiang Zhu<sup>2</sup>, and Lichao Mou<sup>1( $\boxtimes$ )</sup>

### <sup>1</sup> MedAI Technology (Wuxi) Co. Ltd., Wuxi, China <EMAIL>

<sup>2</sup> Technical University of Munich, Munich, Germany

**Abstract.** Few-shot video object segmentation aims to reduce annotation costs; however, existing methods still require abundant dense frame annotations for training, which are scarce in the medical domain. We investigate an extremely low-data regime that utilizes annotations from only a few video frames and leverages existing labeled images to minimize costly video annotations. Specifically, we propose a two-phase framework. First, welearn a few-shot segmentationmodelusinglabeledimages.Subsequently, to improve performance without full supervision, we introduce a spatiotemporal consistency relearning approach on medical videos that enforces consistency between consecutive frames. Constraints are also enforced between the image model and relearning model at both feature and prediction levels. Experiments demonstrate the superiority of our approach over state-ofthe-art few-shot segmentationmethods.Ourmodel bridges the gap between abundant annotated medical images and scarce, sparsely labeled medical videos to achieve strong video segmentation performance in this low data regime. Code is available at [https://github.com/MedAITech/RAB.](https://github.com/MedAITech/RAB)

**Keywords:** medical video segmentation  $\cdot$  few-shot learning  $\cdot$  relearning

### **1 Introduction**

The emergence of deep learning has propelled the advancement of numerous medical data analysis tasks, particularly achieving significant accomplishments in segmentation tasks. The automatic segmentation of medical data plays a pivotal role in various clinical applications such as computer-aided diagnosis and disease progression monitoring  $[1,2]$  $[1,2]$  $[1,2]$ . However, in many practical scenarios, segmentation models often face challenges due to the prohibitive cost of pixel-level

Z. Zheng,Y. Shi—Equal contribution.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 272–282, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_26)\_26

annotation from clinical experts and the limited availability of samples for rare anomalies and unusual pathological conditions. To circumvent these constraints, there has been a growing interest in few-shot segmentation techniques that can learn to segment new classes with only a few annotated examples [\[3\]](#page-51-2).

Prior few-shot semantic segmentation methods in computer vision can be categorized into three main approaches: conditional networks, latent space optimization, and prototypical learning. Conditional networks commonly comprise two modules—a conditioning branch that ingests support samples to produce model parameters, and a segmentation branch that predicts masks for query images based on these dynamic parameters  $[4,18]$  $[4,18]$ . Latent space optimization methods instead leverage generative models to synthesize images by optimizing latent vectors to match the distribution of queries. Segmentation is then performed by transferring representations from the synthesized examples [\[5](#page-51-4)]. Finally, prototypical networks derive class prototypes from support images which are then compared against features of query images to predict segmentation masks [\[6](#page-51-5),[27\]](#page-52-1). In the medical domain, prototypical networks have garnered substantial traction for few-shot medical image segmentation [\[7](#page-51-6),[8\]](#page-51-7).

From image to video,  $[9,10]$  $[9,10]$  $[9,10]$  explore segmenting objects of novel categories in query videos using only a few annotated support frames. However, these approaches still require densely annotated frames for training and do not fully alleviate annotation costs. In addition, since videos are often not recorded and stored in medical imaging applications, publicly available medical video datasets remain relatively scarce and limited in scale, posing challenges for medical video object segmentation tasks.

In this paper, we introduce a novel task of few-shot medical video object segmentation with image datasets. The goal is to segment a medical video given sparse annotations—only a few frames (e.g. the first) have ground truth masks, along with abundant labeled images. This represents an extremely low-data regime for video segmentation that minimizes costly pixel-level video annotations. To address this, we propose a two-phase framework. First, we learn a few-shot segmentation model using labeled images. Specifically, a pre-trained backbone extracts features from support and query images. These features and support masks are integrated to generate coarse query masks that approximately localize target objects. The coarse masks are then fused with support and query features to output fine segmentation masks. However, without temporal modeling, the few-shot image segmentation model may perform sub-optimally on videos. Thus, we propose a spatiotemporal consistency relearning approach for medical videos that is performed in the second phase. We leverage the temporal continuity prior, which assumes consistency between consecutive frames in a video, to obtain effective spatiotemporal features. Furthermore, we introduce spatial consistency constraints between the model trained in the first phase and the relearning model at both feature and prediction levels. These effectively minimize discrepancies between the two models to ensure model performance on videos. Our key contributions are three-fold:

– We explore few-shot medical video object segmentation using image datasets, without reliance on full video annotations.

- We introduce a novel self-supervised framework that exploits spatiotemporal consistency as additional supervision to achieve improved segmentations in this challenging setting.
- Extensive experiments demonstrate the superiority of our approach over existing state-of-the-art few-shot segmentation methods.

Image /page/45/Figure/3 description: This is a diagram illustrating a deep learning model for analyzing cardiac ultrasound images over time. The model takes a sequence of ultrasound images as input, indicated by the 'time' axis on the left. The top branch shows an Encoder and Decoder processing these images to extract 'Features'. These features are then fed into a 'Head' which produces 'Predictions'. The bottom branch shows a similar Encoder and Decoder, but with 'initialization' from the top branch. The features from the bottom branch are processed by a 'TAU' module, which also receives inputs related to 'Lf' and 'Lt' (likely loss functions). The TAU module then feeds into another 'Head', which also produces 'Predictions'. A loss function 'Lp' is shown connecting the predictions from both branches. The overall architecture suggests a method for temporal analysis and prediction of cardiac activity using ultrasound data.

<span id="page-45-0"></span>**Fig. 1.** Pipeline of the proposed few-shot segmentation method for medical video object segmentation.

## **2 Method**

### **2.1 Task Definition**

We address the task of few-shot video object segmentation, where given annotated frames from the beginning of a medical video sequence, the goal is to segment subsequent frames. Our approach leverages labeled images to train a model, which is then applied to test videos containing unseen object classes. Both training and testing are performed in an episodic manner, where each episode contains data sampled from the same class. Specifically, an episode comprises a support set  $S = \{(\mathbf{s}_i, \mathbf{m}_i)\}_{i=1}^N$  and a query set  $\mathcal{Q} = \{(\mathbf{q}_k, \mathbf{y}_k)\}_{k=1}^K$ , where  $\mathbf{s}_i$  and  $m_i$  represent the *i*-th support image and its ground truth mask, respectively, while  $q_k$  and  $y_k$  denote the k-th query image and the corresponding annotation mask. The model segments  $q_k$  guided by S. For video data, we use the first N frames as the support set and the remaining frames as query data.

### **2.2 Learning Few-Shot Segmentation with Images**

We first train a few-shot medical image segmentation network that consists of three key components: a pseudo mask generation module, a cross-resolution feature fusion module, and a segmentation head. We extract visual features from

support and query images using a pre-trained backbone. The pseudo mask generation module is designed to roughly localize query objects with minor cost. The cross-resolution feature fusion module then facilitates interaction between query features and support features along with pseudo masks at multiple scales, thereby preserving fine image details and precise localization cues. The output features from this module are fed into the segmentation head, comprising a  $3 \times 3$ convolutional layer followed by a  $1 \times 1$  convolution with softmax, to predict segmentation masks.

**Pseudo Mask Generation.** Pseudo masks have been widely adopted in fewshot segmentation models [\[11](#page-51-10)[,12](#page-52-2)] to provide coarse object localization. Specifically, they measure the cosine similarity between query and support features. We utilize high-level features to acquire pseudo masks, as they encode richer semantic information. Let  $\mathcal F$  denote the backbone and  $p_k$  represent the generated pseudo mask for  $q_k$ . We have

<span id="page-46-0"></span>
$$
\boldsymbol{p}_k = \cos\left(\mathcal{F}(\boldsymbol{q}_k), \mathcal{F}(\boldsymbol{s}_i \odot \boldsymbol{m}_i)\right), \qquad (1)
$$

where  $\odot$  is the Hadamard product. We then normalize  $p_k$  to the range of  $[0, 1]$ using min-max normalization.

**Cross-Resolution Feature Fusion.** First, we combine the resulting pseudo mask  $p_k$ , containing localization cues, with support and query features as follows:

$$
\boldsymbol{f} = \text{Conv}_{1\times 1} \circ \text{Concat}(\text{up}(\boldsymbol{p}_k), \mathcal{G}(\boldsymbol{q}_k), \mathcal{G}(\boldsymbol{s}_i \odot \boldsymbol{m}_i)), \qquad (2)
$$

where  $\circ$  is a composition function, up(·) upsamples the pseudo mask, G denotes the first half of the backbone network, and  $Concat(\cdot)$  represents channel-wise concatenation. Note that we utilize intermediate features (via  $\mathcal{G}$ ) instead of high-level ones as in Eq. [\(1\)](#page-46-0), since they retain finer-grained details. Then, to derive multi-scale representations, we exploit the heavy neck module from GiraffeDet [\[13\]](#page-52-3), and its output features are finally fed into the segmentation head to predict segmentation masks.

### **2.3 Inference on Individual Video Frames**

A straightforward approach to applying the above-mentioned few-shot segmentation model trained on images to videos is to treat each video frame as an independent sample, using the first few annotated frames as support data, with the remaining frames as individual query inputs.

However, empirical evidence suggests that this direct application does not yield optimal segmentation results. The fundamental issue arises from treating query data, i.e., video frames, as isolated inputs, inherently disregarding the temporal continuity inherent in video content.

### **2.4 Spatiotemporal Consistency Relearning for Videos**

**Temporal Consistency.** As mentioned above, the few-shot segmentation model, which is merely trained on images, fails to consider inter-frame correlations. A common approach to mitigate this issue, while reducing computational complexity, involves integrating new temporal modules with a frozen base model, such as adapters [\[32](#page-53-0)]. However, such methods typically require extra dense video annotations for parameter learning, which undermines the intention of few-shot learning in medical videos.

To overcome these issues, we first introduce a temporal attention unit [\[14](#page-52-4)] placed between the cross-resolution feature fusion module and segmentation head of the trained image model, designed to capture connections between frames. Then, we relearn this new model in a self-supervised manner. Under the temporal continuity prior, we utilize cosine similarity to regularize the consistency of features of consecutive frames. This serves to capture temporal relationships without supervision while improving segmentation.

Formally, let  $f_t$  denote feature maps of the  $t$ -th frame from the inserted temporal attention module in a batch  $\beta$  which contains consecutive frames. We can have the following temporal consistency regularization:

$$
\mathcal{L}_t = 1 - \frac{1}{|\mathcal{B}|} \sum_{t=1}^{|\mathcal{B}-1|} \cos(f_t, f_{t+1}). \tag{3}
$$

**Feature and Prediction Consistency Constraints.** In the relearning phase, altering model parameters could cause the model to diverge from its training, thus deteriorating its performance on video data. For stability, we introduce the following two consistency constraints.

The feature consistency implies that given the same inputs, features generated by the relearning model should remain correlated to the feature distribution of the trained, frozen image model. Let  $z_{i,j}$  and  $\hat{z}_{i,j}$  denote features from crossresolution feature fusion modules of the two models, respectively. The feature consistency constraint can be defined as:

$$
\mathcal{L}_f = \frac{1}{|\mathcal{B}| D} \sum_{t=1}^{|\mathcal{B}|} \sum_{j=1}^{N} (\hat{z}_{i,j} - z_{i,j})^2, \qquad (4)
$$

where  $j$  is a spatial index, and  $N$  denotes the number of feature vectors.

The prediction consistency minimizes the discrepancy between segmentation masks predicted by the two models for the same frames. It can be formulated as:

$$
\mathcal{L}_p = \frac{1}{|\mathcal{B}|} \sum_{t=1}^{|\mathcal{B}|} (\hat{\mathbf{y}}_t - \mathbf{y}_t)^2.
$$
 (5)

**Overall Objective.** The weights of our relearning model, excluding the temporal attention unit, are initialized by the model pre-trained on images in the first

phase. We fix the segmentation head and make the other parameters trainable. The overall objective for relearning is

$$
\mathcal{L} = \lambda_1 \mathcal{L}_t + \lambda_2 \mathcal{L}_f + \lambda_3 \mathcal{L}_p, \qquad (6)
$$

where  $\lambda_1$ ,  $\lambda_2$ , and  $\lambda_3$  are trade-off hyperparameters.

Figure [1](#page-45-0) shows our method. The shift from medical images to videos leads to performance degradation when deploying an image model on videos. The proposed relearning method enables the model to adapt to test video characteristics, mitigating this domain shift. By fine-tuning a subset of parameters, coupled with newly introduced ones, on a few labeled frames from the test data, the model can better capture the underlying distribution of the test data, improving performance.

### **3 Experiment**

### **3.1 Experimental Setups**

**Datasets.** For medical images, we consider the Breast Ultrasound Images (BUSI) dataset [\[20](#page-52-5)], the Multi-Modality Ovarian Tumor Ultrasound (MMOTU) dataset [\[23](#page-52-6)], the TN3K dataset [\[21\]](#page-52-7), the Digital Database Thyroid Image (DDTI) dataset [\[22](#page-52-8)], and the Laryngeal Endoscopic dataset [\[24](#page-52-9)]. For medical videos, we use the HMC-QU dataset [\[25](#page-52-10)] and the ASU-Mayo Clinic Colonoscopy Video (ASU-Mayo) dataset [\[26](#page-52-11)]. To train a few-shot segmenter for ultrasound videos, we utilize BUSI, MMOTU, TN3K, and DDTI as training images, and perform relearning as well as testing on HMC-QU. Similarly, for experiments on endoscopic videos, we employ the Laryngeal Endoscopic dataset and ASU-Mayo. To enhance the performance of the image model learned in the first phase, we leverage a training strategy that combines natural image datasets, PASCAL-5*<sup>i</sup>* [\[18\]](#page-52-0) and COCO-20*<sup>i</sup>* [\[19\]](#page-52-12), with the above-mentioned medical image datasets. Note that models are trained on base image classes and evaluated on novel video classes with no class overlap, assessing generalization to unseen data.

**Evaluation Metrics.** Following prior works [\[15](#page-52-13),[16\]](#page-52-14), we adopt the Dice score and foreground-background IoU as evaluation metrics to assess the performance of few-shot segmentation models.

**Implementation Details.** For a fair comparison, all experiments are conducted under the most challenging one-shot setting, where only one annotated image (first phase) or frame (second phase) is used as the support set. We employ ResNet50 [\[17\]](#page-52-15) pre-trained on ImageNet as our backbone network. During training of the few-shot medical image segmentation model, the backbone's weights are frozen except for block  $#4$ , which is fine-tuned to learn more robust feature maps. The model is trained using the Adam optimizer with a learning rate of 1e-4 for the first 5K iterations, followed by the SGD optimizer with a learning rate of 1e-5 for subsequent iterations. The batch size is set to 8. In the second phase, we use the SGD optimizer with a learning rate of 1e-5 and a batch size of 4. Our method is implemented in PyTorch and runs on NVIDIA RTX 4090 GPUs.

<span id="page-49-0"></span>**Table 1.** Quantitative comparison with competing methods on the HMC-QU ultrasound video dataset for few-shot segmentation. Performance is reported in terms of Dice  $(\%)$  and IoU  $(\%)$ .

| Images      | Thyroid      |              | Breast       |              | Ovary        |              | All          |              |
|-------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|             | methods      | Dice         | IoU          | Dice         | IoU          | Dice         | IoU          | Dice         |
| PATNet [27] | 32.91        | 21.82        | 32.57        | 20.37        | 35.65        | 22.44        | 36.61        | 22.76        |
| HSNet [28]  | 67.97        | 52.24        | 70.60        | 55.28        | 68.63        | 53.01        | 69.71        | 54.15        |
| DCAMA [29]  | 60.09        | 41.02        | 75.61        | 61.33        | 63.03        | 46.52        | 68.75        | 52.99        |
| DCP [30]    | 51.34        | 35.26        | 47.47        | 31.63        | 45.43        | 30.10        | 43.23        | 27.74        |
| AFA [31]    | 72.36        | 57.41        | 75.11        | 60.63        | 67.99        | 52.26        | 65.03        | 49.26        |
| Ours        | <b>78.15</b> | <b>64.17</b> | <b>85.79</b> | <b>75.54</b> | <b>84.55</b> | <b>73.26</b> | <b>71.90</b> | <b>56.42</b> |

<span id="page-49-1"></span>**Table 2.** Comparison with other methods on the ASU-Mayo endoscopic video dataset.

| Images      | Laryngeal |       |
|-------------|-----------|-------|
| methods     | Dice      | IoU   |
| PATNet [27] | 44.39     | 33.54 |
| HSNet [28]  | 59.21     | 46.96 |
| DCAMA[29]   | 56.67     | 45.60 |
| DCP [30]    | 49.74     | 37.49 |
| AFA [31]    | 60.38     | 47.42 |
| Ours        | 63.97     | 48.40 |

<span id="page-49-2"></span>**Table 3.** Ablation studies on the effectiveness of each proposed consistency regularization term.

|  | HMC-QU                                                                                             | ASU-Mayo   |  |
|--|----------------------------------------------------------------------------------------------------|------------|--|
|  | $\mathcal{L}_t \mathcal{L}_f \mathcal{L}_p $ Dice  IoU                                             | Dice   IoU |  |
|  | <b>x</b>   <b>x</b>   <b>x</b>   <b>x</b>   83.55   71.89   62.69   48.05                          |            |  |
|  | $\times$ $\checkmark$ $\checkmark$ 82.10 70.17 60.36 46.43                                         |            |  |
|  | $\checkmark$   $\checkmark$   $\checkmark$  83.37  71.26  61.85  47.65                             |            |  |
|  | $\checkmark$   $\checkmark$   $\checkmark$  1.25  1.07  0.02  0.01                                 |            |  |
|  | $\sqrt{\left \checkmark\right }\right \sqrt{\left 85.79\right }75.54\right 63.97\left 48.4\right $ |            |  |

### **3.2 Results**

We compare the proposed model with state-of-the-art few-shot segmentation approaches, including PATNet [\[27\]](#page-52-1), HSNet [\[28\]](#page-52-16), DCAMA [\[29](#page-52-17)], DCP [\[30](#page-53-1)], and AFA [\[31](#page-53-2)]. PATNet and AFA utilize the prototype learning paradigm, whereas HSNet, DCAMA, and DCP introduce novel mechanisms to fully leverage information from support image-mask pairs. In Table [1,](#page-49-0) "thyroid", "breast", and "ovary" denote the use of image datasets TN3K and DDTI, BUSI, and MMOTU, respectively, to train the few-shot medical image segmentation model. We also train the model with the combined ultrasound image datasets. In Table [2,](#page-49-1) "laryngeal" indicates the usage of the endoscopic dataset [\[24](#page-52-9)]. Experimental results in Table [1](#page-49-0) and Table [2](#page-49-1) demonstrate that the proposed method outperforms competing approaches. The consistent superiority of our method across different datasets indicates its effectiveness and generalizability. Additionally, qualitative results (see Fig. [2\)](#page-50-0) show that our method generates better segmentation results for medical videos. Regarding time complexity, our model achieves 78 fps, comparable to baselines.

### **3.3 Ablation Study**

We conduct ablation studies to investigate the effectiveness of each component in the proposed spatiotemporal consistency relearning approach. Table [3](#page-49-2) reports numerical results. We set the few-shot segmentation model pre-trained on images as the baseline. Then, we evaluate our relearning network under different loss combination settings. Compared to the baseline, lacking the temporal consistency regularization, i.e.,  $\mathcal{L}_t$ , the Dice and IoU scores on both datasets decrease. Moreover, we observe that the constraints on feature consistency and prediction consistency, especially the latter, are essential for aligning distributions between the two phases, thereby guaranteeing model performance. By learning with all the introduced consistency losses, our model outperforms all competing approaches.

Image /page/50/Figure/3 description: The image displays a grid of medical images, likely from an endoscopy and echocardiography. The top row features three endoscopic views of internal tissue, with colored markers (yellow and teal) highlighting specific areas. The numbers #1, #396, #556, #662, and #718 are associated with these images. The bottom two rows present echocardiogram images, each showing a cross-section of a heart. These echocardiograms are also annotated with teal and white outlines, segmenting different parts of the heart. The numbers #1, #3, #10, #15, #18, #2, #7, #11, and #16 are present below these echocardiogram images, likely indicating frame numbers or sequences.

<span id="page-50-0"></span>**Fig. 2.** Qualitative results of our few-shot video object segmentation model on the HMC-QU and ASU-Mayo datasets. The first column shows annotated support frames with ground truth masks (yellow). The remaining columns illustrate our model' (Color figure online)s segmentation predictions (green masks) on sampled query frames from videos. Ground truth masks for the query frames are outlined in white for reference.

## **4 Conclusion**

In conclusion, this work presents a novel approach to few-shot video object segmentation that bridges the gap between abundant annotated medical images and sparse video annotations. By first performing few-shot segmentation pretraining on labeled images and then introducing a spatiotemporal consistency relearning strategy on medical videos, our two-phase framework achieves strong performance in this extremely low-data regime without requiring costly dense video annotations. The relearning phase enforces consistency across consecutive frames while also maintaining constraints with the pre-trained image model. Experiments validate the superiority of our method over existing state-of-the-art few-shot segmentation techniques for video data. Overall, this approach provides an effective solution to leverage existing image labels for the video domain, minimizing costly new annotations. By enabling accurate video segmentation from just a few labeled frames, it opens up new possibilities for applications in the medical field where video data is prevalent but annotations are limited.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this paper.

## **References**

- <span id="page-51-0"></span>1. Che, H., Chen, S., Chen, H.: Image quality-aware diagnosis via meta-knowledge co-embedding. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 19819–19829. (2023)
- <span id="page-51-1"></span>2. Che, H., Cheng, Y., Jin, H., Chen, H.: Towards generalizable diabetic retinopathy grading in unseen domains. In: International Conference on Medical Image Computing and Computer proceted Intervention, pp. 430–440. (2023)
- <span id="page-51-2"></span>3. Sun, L., Li, C., Ding, X., Huang, Y., Chen, Z., Wang, G., Yu, Y., Paisley, J.: Few-shot medical image segmentation using a global correlation network with discriminative embedding. Computers in Biology and Medicine **140**, 105067 (2022)
- <span id="page-51-3"></span>4. Rakelly, K., Shelhamer, E., Darrell, T., Efros, A., Levine, S.: Conditional networks for few-shot semantic segmentation. arXiv preprint [arXiv:1806.07373](http://arxiv.org/abs/1806.07373) (2018)
- <span id="page-51-4"></span>5. Tritrong, N., Rewatbowornwong, P., Suwajanakorn, S.: Repurposing GANs for oneshot semantic part segmentation. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 4475–4485. (2021)
- <span id="page-51-5"></span>6. Dong, N., Xing, E.P.: Few-shot semantic segmentation with prototype learning. In: British Machine Vision Conference, p. 79 (2018)
- <span id="page-51-6"></span>7. Roy, A.G., Siddiqui, S., Pölsterl, S., Navab, N., Wachinger, C.: 'Squeeze & excite' guided few-shot segmentation of volumetric images. Medical Image Analysis **59**, 101587 (2020)
- <span id="page-51-7"></span>8. Li, Y., Fu, Y., Gayo, I.J.M.B., Yang, Q., Min, Z., Saeed, S.U., Yan, W., Wang, Y., Noble, J.A., Emberton, M.: Prototypical few-shot segmentation for crossinstitution male pelvic structures with spatial registration. Medical Image Analysis **90**, 102935 (2023)
- <span id="page-51-8"></span>9. Chen, H., Wu, H., Zhao, N., Ren, S., He, S.: Delving deep into many-to-many attention for few-shot video object segmentation. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 14040–14049. (2021)
- <span id="page-51-9"></span>10. Yan, K., Li, X., Wei, F., Wang, J., Zhang, C., Wang, P., Lu, Y.: Two-shot video object segmentation. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 2257–2267. (2023)
- <span id="page-51-10"></span>11. Liu, J., J., Bao, Y., Xie, G., Xiong, H., Sonke, J.-J. and Gavves, E.: Dynamic prototype convolution network for few-shot semantic segmentation. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 11543–11552. (2022)

- <span id="page-52-2"></span>12. Zhang, G., Kang, G., Yang, Y., Wei, Y.: Few-shot segmentation via cycle-consistent Transformer. In: Advances in Neural Information Processing Systems, pp. 21984– 21996. (2021)
- <span id="page-52-3"></span>13. Jiang, Y., Tan, Z., Wang, J., Sun, X., Lin, M. and Li, H.: GiraffeDet: A heavy-neck paradigm for object detection. arXiv preprint [arXiv:2202.04256](http://arxiv.org/abs/2202.04256) (2022)
- <span id="page-52-4"></span>14. Tan, C., Gao, Z., Li, S., Xu, Y., Li, S. Z.: Temporal attention unit: Towards efficient spatiotemporal predictive learning. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 18770–18782. (2022)
- <span id="page-52-13"></span>15. Liu, Y., Zhang, X., Zhang, S., He, X.: Part-aware prototype network for few-shot semantic segmentation. In: European Conference on Computer Vision, pp. 142– 158. (2020)
- <span id="page-52-14"></span>16. Ouyang, C., Biffi, C., Chen, C., Kart, T., Qiu, H., Rueckert, D.: Self-supervised learning for few-shot medical image segmentation. IEEE Transactions on Medical Imaging **41**(7), 1837–1848 (2022)
- <span id="page-52-15"></span>17. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: IEEE Conference on Computer Vision and Pattern Recognition, pp. 770–778. (2016)
- <span id="page-52-0"></span>18. Shaban, A., Bansal, S., Liu, Z., Essa, I., Boots, B.: One-shot learning for semantic segmentation. arXiv preprint [arXiv:1709.03410](http://arxiv.org/abs/1709.03410) (2017)
- <span id="page-52-12"></span>19. Nguyen, K., Todorovic, S.: Feature weighting and boosting for few-shot segmentation. In: IEEE/CVF International Conference on Computer Vision, pp. 622–631. (2019)
- <span id="page-52-5"></span>20. Al-Dhabyani, W., Gomaa, M., Khaled, H., Fahmy, A.: Dataset of breast ultrasound images. Data in Brief **28**, 104863 (2020)
- <span id="page-52-7"></span>21. Gong, H., Chen, J., Chen, G., Li, H., Chen, F., Li, G.: Thyroid region prior guided attention for ultrasound segmentation of thyroid nodules. Computers in Biology and Medicine, **155**, 106389 (2023)
- <span id="page-52-8"></span>22. Pedraza, L., Vargas, C., Narváez, F., Durán, O., Muñoz, E., Romero, E.: An open access thyroid ultrasound image database. In: International Symposium on Medical Information Processing and Analysis, pp. 188–193. (2015)
- <span id="page-52-6"></span>23. Zhao, Q., Lyu, S., Bai, W., Cai, L., Liu, B., Wu, M., Sang, X., Yang, M., Chen, L.: A multi-modality ovarian tumor ultrasound image dataset for unsupervised cross-domain semantic segmentation. arXiv preprint [arXiv:2207.06799](http://arxiv.org/abs/2207.06799) (2022)
- <span id="page-52-9"></span>24. Laves, M.H., Bicker, J., Kahrs, L.A., Ortmaier, T.: A dataset of laryngeal endoscopic images with comparative study on convolution neural network-based semantic segmentation. International Journal of Computer Assisted Radiology and Surgery **14**, 483–492 (2019)
- <span id="page-52-10"></span>25. Degerli, A., Zabihi, M., Kiranyaz, S., Hamid, T., Mazhar, R., Hamila, R., Gabbouj, M.: Early detection of myocardial infarction in low-quality echocardiography. IEEE Access **9**, 34442–34453 (2021)
- <span id="page-52-11"></span>26. Tajbakhsh, N., Gurudu, S. R., Liang, J.: Automated polyp detection in colonoscopy videos using shape and context information. IEEE Transactions on Medical Imaging **35**(2), 630–644 (2015)
- <span id="page-52-1"></span>27. Wang, K., Liew, J. H., Zou, Y., Zhou, D., Feng, J.: PANet: Few-shot image semantic segmentation with prototype alignment. In: IEEE/CVF International Conference on Computer Vision, pp. 9197–9206. (2019)
- <span id="page-52-16"></span>28. Min, J., Kang, D., Cho, M.: Hypercorrelation squeeze for few-shot segmentation. In: IEEE/CVF International Conference on Computer Vision, pp. 6941–6952. (2021)
- <span id="page-52-17"></span>29. Shi, X., Wei, D., Zhang, Y., Lu, D., Ning, M., Chen, J., Ma, K., Zheng, Y.: Dense cross-query-and-support attention weighted mask aggregation for few-shot segmentation. In: European Conference on Computer Vision, pp. 151–168. (2022)

- <span id="page-53-1"></span>30. Lang, C., Cheng, G., Tu, B., Han, J.: Few-shot segmentation via divide-andconquer proxies. International Journal of Computer Vision **132**(1), 261–283 (2024)
- <span id="page-53-2"></span>31. Karimijafarbigloo, S., Azad, R., Merhof, D.: Self-supervised few-shot learning for semantic segmentation: An annotation-free approach. In: International Conference on Medical Image Computing and Computer Assisted Intervention Workshops, pp. 159-171. (2023)
- <span id="page-53-0"></span>32. Park, J., Lee, J., Sohn, K.: Dual-path adaptation from image to video Transformers. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 2203–2213. (2023)

Image /page/54/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below. The icon is a gray circle with a darker gray ribbon or bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# SBC-AL: Structure and Boundary Consistency-Based Active Learning for Medical Image Segmentation

Taimin Zhou<sup>1</sup>, Jin Yang<sup>2</sup>, Lingguo Cui<sup>1</sup>, Nan Zhang<sup>3</sup>, and Senchun Chai<sup>1( $\boxtimes$ )</sup>

 $1$  The School of Automation, Beijing Institute of Technology, Beijing, China

<EMAIL> 2 Department of Radiology, Washington University in St. Louis, St. Louis, MO, USA

<sup>3</sup> The Department of Radiology, Beijing An Zhen Hospital: Capital Medical University Affiliated Anzhen Hospital, Beijing, China

Abstract. Deep learning-based (DL) models have shown superior representation capabilities in medical image segmentation tasks. However, these representation powers require DL models to be trained by extensive annotated data, but the high annotation costs hinder this, thus limiting their performance. Active learning (AL) is a feasible solution for efficiently training models to demonstrate representation powers under low annotation budgets. It is achieved by querying unlabeled data for new annotations to continuously train models. Thus, the performance of AL methods largely depends on the query strategy. However, designing an efficient query strategy remains challenging due to limited information from unlabeled data for querying. Another challenge is that few methods exploit information in segmentation results for querying. To address them, first, we propose a Structure-aware Feature Prediction (SFP) and Attentional Segmentation Refinement (ASR) module to enable models to generate segmentation results with sufficient information for querying. The incorporation of these modules enhances the models to capture information related to the anatomical structures and boundaries. Additionally, we propose an uncertainty-based querying strategy to leverage information in segmentation results. Specifically, uncertainty is evaluated by assessing the consistency of anatomical structure and boundary information within segmentation results by calculating Structure Consistency Score (SCS) and Boundary Consistency Score (BCS). Subsequently, data is queried for annotations based on uncertainty. The incorporation of SFP and ASR-enhanced segmentation models and this uncertainty-based querying strategy into a standard AL strategy leads to a novel method, termed Structure and Boundary Consistency-based Active Learning (SBC-AL). Experimental evaluations conducted on the ACDC dataset and KiTS19 dataset demonstrate the superior performance of SBC-AL on efficient model training under low annotation budgets over other AL methods. Our code is available at [https://github.](https://github.com/Tmin16/SBC-AL) [com/Tmin16/SBC-AL.](https://github.com/Tmin16/SBC-AL)

-c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 283–293, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_27)\_27

**Keywords:** Active Learning  $\cdot$  Medical Image Segmentation  $\cdot$  Query Metrics · Consistency Scores · Uncertainty

# 1 Introduction

The superior representation capabilities of deep learning-based (DL) segmentation methods require them to be trained by extensive annotated data. However, manual annotation is labor-intensive and time-consuming, resulting in a bottleneck to limit their segmentation performance. Active learning (AL) is a feasible solution to enhance the performance of DL-based methods while minimizing the burden of extensive annotation efforts  $[9,13,18]$  $[9,13,18]$  $[9,13,18]$  $[9,13,18]$ . Recognizing the great potential of AL, it has been increasingly adopted for medical image segmentation tasks [\[3](#page-63-2)]. AL enables models to query the most informative samples from unlabeled data and subsequently annotate these data for model retraining. Thus, the effectiveness of AL significantly depends on the query strategy employed. Various strategies have been proposed to enable models to annotate the most informative samples and maximize the benefits of these annotations. For instance, a query strategy is designed to select samples with high uncertainty evaluated by cosine similarity [\[19](#page-64-1)]. Similarly, another uncertainty-based query strategy is employed to select samples by evaluating uncertainty in attention maps [\[12\]](#page-63-3). In EdgeAL, the query strategy is designed to evaluate uncertainty by measuring the divergence and entropy in subject edges [\[8\]](#page-63-4). In PID-AL, uncertainty is evaluated by measuring divergence during querying [\[17\]](#page-64-2). However, few AL-based methods exploit information in segmentation results for querying. Additionally, the effectiveness of querying is often constrained by insufficient information from unlabeled data.

To address these limitations, first, we propose a Structure-aware Feature Prediction (SFP) and Attentional Segmentation Refinement (ASR) module to enable the segmentation models to generate results with sufficient information for querying. Segmentation results from models can provide some information for querying, but the incorporation of SFP and ASR enhances the models to capture more information related to the anatomical structures and boundaries. Additionally, we propose an uncertainty-based querying strategy to efficiently leverage information in segmentation results. Specifically, due to the enhancement of SFP and ASR modules, uncertainty can be evaluated by assessing the consistency of anatomical structure and boundary information within segmentation results. This consistency is assessed by calculating Structure Consistency Score (SCS) and Boundary Consistency Score (BCS). Following this strategy, samples with low consistency in segmentation results will be queried for annotations. Models retrained by these samples can learn sufficient information about anatomical structures and organ boundaries. Finally, we propose a novel AL method for medical image segmentation by integrating the SFP and ASR-enhanced segmentation models, and this uncertainty-based querying strategy, termed Structure and Boundary Consistency-based Active Learning (SBC-AL). These designs effectively enable SBC-AL to exploit sufficient information about anatomical

structures and organ boundaries for querying. Experimental results on ACDC and KiTS19 datasets demonstrate the superior performance of SBC-AL compared to other state-of-the-art AL methods.

Our contribution has threefolds: (i) We propose Structure-aware Feature Prediction (SFP) and Attentional Segmentation Refinement (ASR) modules. They enable models to generate segmentations with sufficient information about anatomical structures and boundaries, thus facilitating the evaluation of consistency and querying. (ii) We propose an uncertainty-based query strategy. It evaluates the uncertainty by measuring the consistency scores for anatomical structure and boundary information, including Structure Consistency Score (SCS) and Boundary Consistency Score (BCS). (iii) We propose a Structure and Boundary Consistency-based Active Learning (SBC-AL) method for efficient medical image segmentation under low annotation budgets. It exploits information on anatomical structures and organ boundaries from segmentation results for querying. Extensive experimental results on ACDC and KiTS19 datasets demonstrate that our SBC-AL method outperforms other AL methods.

## 2 Methodology

The SBC-AL method consists of four major stages (Fig. [1\)](#page-57-0). First, we train the segmentation network with a few labeled data. Second, this trained network generates segmentation results for unlabeled data. Specifically, coarse segmentation results are generated by the backbone, while Structure-aware Feature Prediction (SFP) and Attentional Segmentation Refinement (ASR) modules are employed to refine these coarse results. Subsequently, sufficient information is extracted for querying via distance transformation from segmentation results, and uncertainty is evaluated based on this information by calculating the Structural Consistency Score (SCS) and the Boundary Consistency Score (BCS). Finally, based on evaluation results, we select data and obtain annotations from an Oracle.

### 2.1 Segmentation Network

The segmentation network consists of a U-Net backbone [\[14\]](#page-63-5), and two additional modules, Structure-aware Feature Prediction (SFP) and Attentional Segmentation Refinement (ASR) modules. Firstly, the segmentation network is trained on a few labeled data. Subsequently, this trained network is utilized to generate segmentation results for unlabeled data during querying.

Structure-Aware Feature Prediction (SFP). To enable the segmentation network to capture multi-scale structure-related features, we utilize a Structureaware Feature Prediction (SFP) module. The SFP module is built by cascading four Distance blocks (DisBlocks) (Fig. [1\)](#page-57-0). The DisBlock is utilized to capture structure-related features from semantic contextual information in the encoding

Image /page/57/Figure/1 description: This figure illustrates a deep learning framework for medical image segmentation. The framework consists of a Dataset module, a Segmentation module, and a Query module. The Dataset module includes an Unlabeled Dataset and a Labeled Dataset, which are fed into the Segmentation module. The Segmentation module contains a series of convolutional layers and DisBlock modules, leading to an output segmentation map and an Attentional Segmentation Refinement (ASR) module. The ASR module refines the segmentation map. The Query module takes input from the Labeled Dataset and an Oracle for annotation, and it processes structure and boundary consistency using distance transform. The right side of the figure details the architecture of a DisBlock and the ASR module, showing convolutional layers, batch normalization, concatenation, softmax, and element-wise multiplication operations.

<span id="page-57-0"></span>Fig. 1. The overall architecture of the SBC-AL as well as the detailed architecture of the DisBlock and the ASR module.

path. By employing the Disblock in both high levels and low levels, the SFP module extracts multi-scale structure-aware features progressively and ultimately outputs structure-related information. The structure-related information captured by the SFP module is utilized twofold: First, they are used to refine coarse results from the backbone within the ASR module. Second, they are provided as additional information to improve uncertainty evaluation during querying.

Attentional Segmentation Refinement (ASR). To utilize structure-related features extracted from the SFP module to refine coarse results from the backbone, we propose an Attentional Segmentation Refinement (ASR) module (Fig. [1\)](#page-57-0). Specifically, structure-related features are converted to attention maps by two parallel convolution and batch normalization layers, and a Softmax function. Subsequently, these attention maps are utilized to adaptively refine spatial features from the backbone and thus highlight important anatomical structures and boundary regions.

### 2.2 Uncertainty Evaluation and Consistency Score

In the querying stage, SBC-AL aims to query unlabeled data by evaluating the uncertainty in their segmentation results predicted by the network. Uncertainty is evaluated by calculating two consistency scores, termed Structure Consistency Score (SCS) and Boundary Consistency Score (BCS). These two scores are introduced to evaluate consistency in significant anatomical structures and boundary information, respectively.

Distance Transformation. First, we convert segmentation results to structure-aware distance heatmaps for consistency calculation via a distance transformation. Consider results from the segmentation network  $X =$  ${x_1, x_2, ..., x_{H\times W}} \in \mathbb{R}^{H\times W}$ , a distance transformation is performed on their corresponding ground truth  $Y = \{y_1, y_2, ..., y_{H\times W}\}\in \mathbb{R}^{H\times W}$ . For any label  $i \in \{0, 1, ..., c\}$ , a one-hot heatmap  $L_i$  is generated from the set of pixels p labeled as  $i$  in ground truth  $Y$ .

$$
L_i = \begin{cases} 1, p = i \\ 0, p \neq i \end{cases}
$$

Subsequently, the set of boundary pixels is constructed by selecting pixels from boundary regions of the one-hot heatmap  $L_i$  for any label i. Considering  $m \in M$ are pixels in the boundary of  $L_i$  and  $n \in N$  are arbitrary pixels in  $L_i$ , the structure-aware distance heatmap D*<sup>i</sup>* is calculated.

$$
D_i = \begin{cases} \min||m - n||_2, L_i = 1\\ 0, L_i \neq 1 \end{cases}
$$

Consistency Scores. Consistency score (CS) is calculated by combining SCS and BCS, where w*<sup>s</sup>* and w*<sup>b</sup>* are balancing hyper-parameters to determine the relative significance of the SCS and the BCS.

$$
CS = w_sSCS + w_bBCS.
$$

Structure Consistency Score (SCS). The Structure Consistency Score (SCS) is calculated to evaluate the uncertainty in the anatomical structures of segmentation results. Specifically, a structure-aware heatmap is generated from the output of the SFP module via distance transformation, and subsequently, the SCS is calculated based on this map and results from the segmentation network. SCS consists of two metrics, cosine similarity-based consistency score  $Q_{cos}(x)$ and dice coefficient-based consistency score  $Q_{dice}(x)$ . When the structure map is described as a multi-dimensional distribution of the prediction result, the cosine similarity-based consistency score  $Q_{cos}(x)$  is calculated to assess the consistency of the distribution between the segmentation results  $d_s(y|x)$  and output of the SFP module  $d_o(x)$  for any unlabeled data as follows:

$$
Q_{cos}(x) = \text{Cos}(d_s(y|x), d_o(x)) = -\frac{\sum (d_s(y|x) \times d_o(x))}{||d_s(y|x)||_2^2 \times ||d_o(x)||_2^2}.
$$

Dice coefficient-based consistency score  $Q_{dice}(x)$  is derived from the dice similarity coefficient (DSC). The dice similarity coefficient is usually used to evaluate the segmentation accuracy by calculating the overlap percentage between the prediction and ground truth. Thus, dice coefficient-based consistency score  $Q_{dice}(x)$  is used to assess pixel-level consistency by evaluating the spatial similarity between the segmentation results and the output of the SFP module.

$$
Q_{dice}(x) = \text{Dice}(d_s(y|x), d_o(x)) = 1 - \frac{2 \times \sum (d_s(y|x) \times d_o(x))}{\sum d_s(y|x) + \sum d_o(x)}.
$$

Now, Structure Consistency Score (SCS) can be calculated where w*<sup>c</sup>* and w*<sup>d</sup>* are used to balance the significance of the two metrics.

$$
SCS = w_c Q_{cos}(x) + w_d Q_{dice}.
$$

Boundary Consistency Score (BCS). Boundary regions provide important contextual information about the geometric shapes of organs. However, the boundaries of organs are hard to segment, so boundary regions often have high uncertainty. Thus, evaluating the uncertainty in the boundary regions is an effective way to evaluate uncertainty. To achieve this, we propose a Boundary Consistency Score (BCS) based on the Hausdorff distance (HD). HD is used in evaluating the difference in boundaries between segmentation results [\[6](#page-63-6)]. Boundary Consistency Score is calculated from coarse segmentation results  $d_c(y_c|x_c)$ and segmentation results refined by ASR  $d_s(y_s|x_s)$ . Subsequently, HD-based BCS is used to measure inconsistency and evaluate the uncertainty in the boundary regions between these two results.

BCS = max(h(y*s*, y*<sup>c</sup>*), <sup>h</sup>(y*c*, y*<sup>s</sup>*)).

where  $h(y_s, y_c) = \max_{x_s \in y_s} \min_{x_c \in y_c} ||x_s - x_c||$  and  $h(y_c, y_s) = \max_{x_c \in y_c} \min_{x_s \in y_s} ||x_c - x_s||$  are calculated by the Euclidean distance.

### 2.3 Selective Annotation

After evaluating uncertainty for unlabeled data, selective annotation is implemented. At each selection, the  $K$  unlabeled samples with the highest uncertainty evaluated by consistency scores are selected to be accurately annotated by Oracle. These samples with their accurate labels are combined with other labeled data to re-train the segmentation network to improve its segmentation performance. Then we will repeat the iteration (segmentation, uncertainty evaluation, and selective annotations) on other unlabeled data. This active learning iteration runs until all data are annotated or the segmentation accuracy by the segmentation network on unlabeled data reaches requirements.

### 2.4 Loss Function

We use a multi-categorical cross-entropy loss L*SEG* to evaluate segmentation results where  $y_{ic}$  and  $p_{ic}$  are the real label of a pixel point  $i \in \{1, 2, ..., N\}$  for a category  $c \in \{1, 2, ..., K\}$  and its predicted probability as follows:

$$
L_{SEG} = -\frac{1}{N} \sum_{i=1}^{N} \sum_{c=1}^{K} y_{ic} \log p_{ic}.
$$

The output of the SFP module is a distance-transformed map of the target region. Thus, its optimization is a regression task and the mean-square error is used as the loss function  $L_{SFP}$ .  $y_{ij}$  and  $p_{ij}$  are the true distance value of a pixel point  $i \in \{1, 2, ..., N\}$  on the channel  $j \in \{1, 2, ..., M\}$  and its predicted value.

$$
L_{SFP} = \frac{1}{MN} \sum_{i=1}^{N} \sum_{j=1}^{M} (p_{ij} - y_{ij})^2.
$$

The output of the ASR module is the final refined segmentation result, so we use the loss function L*ASR* which is a combination of multi-categorical cross-entropy loss L*SEG* and the region-based Dice loss L*DICE*.

$$
L_{ASR} = L_{SEG} + L_{DICE}.
$$

Thus, the whole model is trained end-to-end by the final joint loss function  $L$ , where  $\alpha$ ,  $\beta$ , and  $\gamma$  are hyper-parameters to balance the importance of each loss function.

$$
L = \alpha L_{SEG} + \beta L_{SFP} + \gamma L_{ASR}.
$$

### 3 Experiments

Datasets. We used two publicly available datasets to evaluate SBC-AL. The first one is the Automated Cardiac Diagnosis Challenge dataset (ACDC) [\[2](#page-63-7)]. It consists of 150 MR images with three labels, right ventricle (RV), myocardium (MYO), and left ventricle (LV). The second one is the 2019 Kidney Tumor Segmentation dataset (KiTS19) [\[5](#page-63-8)]. It consists of 210 CT images with labels of kidney and mass regions. To avoid the effects of unbalanced data, only kidney labels were used. In pre-processing, 2D patches with the dimension  $256 \times 256$ were extracted from 3D volumes.

**Experimental Details.** Our models were implemented by  $Python$ . We optimized the network using the SGD with a momentum of 0.9 and weight decay of 0.0001. The joint optimization of the regression and classification functions is unstable. To address this issue, we set initial learning rates to 0.01 and 0.0001 for the backbone and the SFP module, respectively. Then learning rates gradually reduced by a factor of 0.9 every 500 iterations. In ACDC, the batch size was set to 16, and the training epoch was 60, while in KiTS19 these were 8 and 100, respectively. In our best practice, hyperparameters for consistency scores were set as  $w_s = 0.8$ ,  $w_b = 0.2$ ,  $w_c = 0.6$  and  $w_d = 0.4$ . We compared the segmentation performance of the SBC-AL method with six well-known AL strategies, including Random, Maximal Entropy (MaxEntropy) [\[16](#page-64-3)], Least Confidence (LC) [\[1](#page-63-9)[,10](#page-63-10)], Softmax Margin (Margin) [\[7\]](#page-63-11), Mean Standard Deviation (Mean STD) [\[4\]](#page-63-12), and Variational Adversarial Active Learning (VA-AL) [\[15\]](#page-63-13). The initial labeled set was generated randomly and was the same for all AL methods in each experiment. Subsequently, we repeated experiments five times with different initial sets. The dice similarity coefficient was used to evaluate segmentation results, and average values and their standard deviation among these experiments were shown in experimental results.

<span id="page-60-0"></span> $\frac{1}{1}$  [https://pytorch.org/.](https://pytorch.org/)

<span id="page-61-0"></span>

| Table 1. Comparison of segmentation performance between SBL-AL and other AL |
|-----------------------------------------------------------------------------|
| methods on the ACDC dataset. ( <b>Bold</b> represents the best result).     |

| Methods    | 6.67%                              | 10.00%                             | 13.33%                             | 16.67%                             | 20.00%                             | 23.33%                             | 26.67%                             | 30.00%                             | 33.33%                             | 100%                               |
|------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|------------------------------------|
| Random     | $66.11 \pm 0.04$                   | $72.70 \pm 0.03$                   | $80.50 \pm 0.02$                   | $84.70 \pm 0.01$                   | $84.89 \pm 0.03$                   | $86.74 \pm 0.01$                   | $86.33 \pm 0.01$                   | $87.43 \pm 0.01$                   | $87.92 \pm 0.02$                   | $91.37 \pm 0.01$                   |
| MaxEntropy | $66.11 \pm 0.04$                   | $75.58 \pm 0.02$                   | $81.34 \pm 0.02$                   | $85.57 \pm 0.01$                   | $87.07 \pm 0.01$                   | $87.41 \pm 0.01$                   | $88.00 \pm 0.01$                   | $88.65 \pm 0.02$                   | $88.78 \pm 0.01$                   | $91.37 \pm 0.01$                   |
| LC         | $66.11 \pm 0.04$                   | $74.65 \pm 0.01$                   | $83.32 \pm 0.01$                   | $84.38 \pm 0.01$                   | $86.16 \pm 0.02$                   | $87.47 \pm 0.01$                   | $88.52 \pm 0.01$                   | $89.03 \pm 0.01$                   | $89.08 \pm 0.02$                   | $91.37 \pm 0.01$                   |
| Margin     | $66.11 \pm 0.04$                   | $75.05 \pm 0.01$                   | $82.71 \pm 0.01$                   | $86.01 \pm 0.03$                   | $86.74 \pm 0.01$                   | $87.63 \pm 0.01$                   | $88.10 \pm 0.01$                   | $88.75 \pm 0.02$                   | $89.13 \pm 0.02$                   | $91.37 \pm 0.01$                   |
| Mean STD   | $66.11 \pm 0.02$                   | $78.51 \pm 0.01$                   | $81.92 \pm 0.01$                   | $83.02 \pm 0.02$                   | $85.46 \pm 0.01$                   | $87.52 \pm 0.01$                   | $88.22 \pm 0.02$                   | $88.16 \pm 0.02$                   | $88.62 \pm 0.01$                   | $91.37 \pm 0.01$                   |
| VA-AL      | <b><math>66.40 \pm 0.05</math></b> | $78.60 \pm 0.01$                   | $82.64 \pm 0.01$                   | $86.10 \pm 0.03$                   | $87.00 \pm 0.01$                   | $87.89 \pm 0.02$                   | $88.20 \pm 0.01$                   | $88.65 \pm 0.01$                   | $89.05 \pm 0.02$                   | $91.37 \pm 0.01$                   |
| SBC-AL     | $66.20 \pm 0.05$                   | <b><math>81.43 \pm 0.01</math></b> | <b><math>84.55 \pm 0.01</math></b> | <b><math>87.55 \pm 0.02</math></b> | <b><math>88.16 \pm 0.01</math></b> | <b><math>89.14 \pm 0.01</math></b> | <b><math>89.27 \pm 0.01</math></b> | <b><math>89.99 \pm 0.01</math></b> | <b><math>91.05 \pm 0.01</math></b> | <b><math>91.37 \pm 0.01</math></b> |

<span id="page-61-1"></span>Table 2. Comparison of segmentation performance between SBL-AL and other AL methods on the KiTS19 dataset. (Bold represents the best result).

| Methods    | 6.67%            | 10.00%           | 13.33%           | 16.67%           | 20.00%           | 23.33%           | 26.67%           | 30.00%           | 100%             |
|------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|
| Random     | $54.97 \pm 0.04$ | $67.30 \pm 0.01$ | $84.89 \pm 0.01$ | $85.70 \pm 0.04$ | $88.00 \pm 0.02$ | $83.41 \pm 0.02$ | $89.67 \pm 0.03$ | $92.56 \pm 0.03$ | $96.94 \pm 0.01$ |
| MaxEntropy | $54.97 \pm 0.04$ | $81.07 \pm 0.02$ | $86.48 \pm 0.01$ | $86.45 \pm 0.01$ | $89.65 \pm 0.03$ | $93.67 \pm 0.02$ | $92.71 \pm 0.01$ | $94.88 \pm 0.02$ | $96.94 \pm 0.01$ |
| LC         | $54.97 \pm 0.04$ | $79.31 \pm 0.05$ | $87.09 \pm 0.03$ | $92.57 \pm 0.01$ | $91.85 \pm 0.02$ | $93.84 \pm 0.02$ | $95.07 \pm 0.01$ | $95.72 \pm 0.01$ | $96.94 \pm 0.01$ |
| Margin     | $54.97 \pm 0.04$ | $81.84 \pm 0.04$ | $89.40 \pm 0.01$ | $92.55 \pm 0.02$ | $93.62 \pm 0.01$ | $94.45 \pm 0.03$ | $93.69 \pm 0.01$ | $95.19 \pm 0.02$ | $96.94 \pm 0.01$ |
| SBC-AL     | $54.97 \pm 0.03$ | $86.51 \pm 0.01$ | $89.61 \pm 0.01$ | $93.59 \pm 0.01$ | $94.52 \pm 0.02$ | $95.19 \pm 0.01$ | $95.81 \pm 0.02$ | $96.73 \pm 0.01$ | $96.94 \pm 0.01$ |

Main Results. Tables [1](#page-61-0) and [2](#page-61-1) compared the performance of SBL-AL with other recent state-of-the-art AL methods in ACDC and KiTS19 datasets, respectively, and Fig. [2](#page-61-2) demonstrates the qualitative comparison results on the ACDC dataset. Figure [3](#page-62-0) shows the qualitative results from SFP and ASR modules and samples with high CS and low CS. The results of using 100% data for training were the upper limit for the model performance. Using different ratios of annotated data for training, SBC-AL achieved the highest segmentation accuracy than other AL methods at all stages except at 6.6% data. The results of using 6.6% data showed the performance of different AL methods trained by the same initial set, and VAAL utilized a U-Net encoder for initial self-encoding training. SBC-AL achieved over 95% performance with only 16.67% annotated data in both two datasets.

Image /page/61/Picture/6 description: The image displays a grid of cardiac MRI scans, comparing different segmentation methods. The top row shows axial views, and the bottom row shows sagittal views. Each column represents a different method: GT (Ground Truth), Random, MaxEntropy, LC, Margin, Mean STD, VA-AL, and SBC-AL. All scans are overlaid with segmentation masks in yellow and green, highlighting different cardiac structures. The GT column shows the reference segmentation, while the other columns show segmentations produced by various algorithms.

Fig. 2. The qualitative comparison of SBC-AL and other methods using 33*.*3% of the annotated data from the ACDC dataset.

<span id="page-61-2"></span>Ablation Study. To evaluate the architectural effectiveness of ASR, different from ASR where the results from the SFP were converted to an attention map, ASR (A) and ASR (C) were designed where the coarse results from the

Image /page/62/Figure/1 description: The image displays a diagram illustrating a medical imaging processing pipeline. The top section shows a workflow starting with an 'Input' image, followed by a block labeled 'SFP', leading to 'Output' and 'Ground Truth (GT)' images, which appear to be heatmaps or segmentation masks. To the right, another workflow begins with 'Input1' and 'Input2', which are also medical images, and then processed by a block labeled 'ASR' to produce an 'Output' image. The bottom section presents a comparison of 'High CS Data with Label' and 'Low CS Data with Label'. For each category, pairs of 'GT' (Ground Truth) and 'Output' images are shown, demonstrating segmentation results on medical scans, with some outputs highlighted by red bounding boxes.

<span id="page-62-0"></span>Fig. 3. The qualitative representations of output from SFP and ASR modules, and qualitative results with high CS and low CS.

on the ACDC dataset.

<span id="page-62-1"></span>

| Designs | RV           | MYO          | LV           | Avg.         |
|---------|--------------|--------------|--------------|--------------|
| No ASR  | 88.36        | 85.64        | 94.36        | 89.45        |
| ASR (A) | 90.59        | 87.14        | 95.63        | 90.79        |
| ASR (C) | 90.90        | 87.21        | 95.10        | 90.09        |
| ASR     | <b>91.28</b> | <b>87.60</b> | <b>95.23</b> | <b>91.37</b> |

**Table 3.** The results of the ablation study **Table 4.** The results of the ablation study on the KiTS19 dataset.

<span id="page-62-2"></span>

| Methods    | UNet         | UNet++       | Att-UNet     |
|------------|--------------|--------------|--------------|
| Random     | 67.30        | 82.23        | 80.15        |
| MaxEntropy | 81.07        | 89.29        | 87.44        |
| LC         | 79.31        | 91.86        | 89.36        |
| Margin     | 81.84        | 88.68        | 86.84        |
| SBC-AL     | <b>86.51</b> | <b>92.86</b> | <b>91.85</b> |

backbone were combined with the results from the SFP module via addition and concatenation, respectively. Models incorporated with ASR showed superior performance than models incorporated with ASR (A), ASR (C), or the model without ASR (Table [3\)](#page-62-1). To evaluate the superior performance of SBC-AL on different segmentation networks than other AL methods, we implemented an ablation study in three segmentation networks, including UNet  $[14]$  $[14]$ , UNet $++$ [\[20](#page-64-4)], and Attention U-Net (Att-UNet) [\[11\]](#page-63-14). Table [4](#page-62-2) demonstrates the results of using 10% labeled data from the KiTS19 dataset. When different networks were used as backbones, SBC-AL always achieved the best segmentation accuracy than other AL methods.

### 4 Conclusion

We propose a novel Structure and Boundary Consistency Active Learning (SBC-AL) method for efficient medical image segmentation. It enhances the segmentation performance of DL models with limited annotated data available. SBC-AL demonstrates superior performance than other popular AL methods, and we believe it can achieve promising segmentation performance on various tasks.

Acknowledgments. No acknowledgments or competing interests.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-63-9"></span>1. Agrawal, A., Tripathi, S., Vardhan, M.: Active learning approach using a modified least confidence sampling strategy for named entity recognition. Progress in Artificial Intelligence 10, 113–128 (2021)
- <span id="page-63-7"></span>2. Bernard, O., Lalande, A., Zotti, C., Cervenansky, F., Yang, X., Heng, P.A., Cetin, I., Lekadir, K., Camara, O., Ballester, M.A.G., et al.: Deep learning techniques for automatic mri cardiac multi-structures segmentation and diagnosis: is the problem solved? IEEE transactions on medical imaging 37(11), 2514–2525 (2018)
- <span id="page-63-2"></span>3. Budd, S., Robinson, E.C., Kainz, B.: A survey on active learning and human-inthe-loop deep learning for medical image analysis. Medical Image Analysis 71, 102062 (2021)
- <span id="page-63-12"></span>4. Gal, Y., Islam, R., Ghahramani, Z.: Deep bayesian active learning with image data. In: International conference on machine learning. pp. 1183–1192. PMLR (2017)
- <span id="page-63-8"></span>5. Heller, N., Sathianathen, N., Kalapara, A., Walczak, E., Moore, K., Kaluzniak, H., Rosenberg, J., Blake, P., Rengel, Z., Oestreich, M., et al.: The kits19 challenge data: 300 kidney tumor cases with clinical context, ct semantic segmentations, and surgical outcomes. arXiv preprint [arXiv:1904.00445](http://arxiv.org/abs/1904.00445) (2019)
- <span id="page-63-6"></span>6. Huttenlocher, D.P., Klanderman, G.A., Rucklidge, W.J.: Comparing images using the hausdorff distance. IEEE Transactions on pattern analysis and machine intelligence 15(9), 850–863 (1993)
- <span id="page-63-11"></span>7. Joshi, A.J., Porikli, F., Papanikolopoulos, N.: Multi-class active learning for image classification. In: 2009 ieee conference on computer vision and pattern recognition. pp. 2372–2379. IEEE (2009)
- <span id="page-63-4"></span>8. Kadir, M.A., Alam, H.M.T., Sonntag, D.: Edgeal: An edge estimation based active learning approach for oct segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 79–89. Springer (2023)
- <span id="page-63-0"></span>9. Konyushkova, K., Sznitman, R., Fua, P.: Learning active learning from data. Advances in neural information processing systems 30 (2017)
- <span id="page-63-10"></span>10. Lewis, D.D., Catlett, J.: Heterogeneous uncertainty sampling for supervised learning. In: Machine learning proceedings 1994, pp. 148–156. Elsevier (1994)
- <span id="page-63-14"></span>11. Oktay, O., Schlemper, J., Folgoc, L.L., Lee, M., Heinrich, M., Misawa, K., Mori, K., McDonagh, S., Hammerla, N.Y., Kainz, B., et al.: Attention u-net: Learning where to look for the pancreas. arXiv preprint  $arXiv:1804.03999$  (2018)
- <span id="page-63-3"></span>12. Qu, C., Zhang, T., Qiao, H., Tang, Y., Yuille, A.L., Zhou, Z., et al.: Abdomenatlas-8k: Annotating 8,000 ct volumes for multi-organ segmentation in three weeks. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-63-1"></span>13. Ren, P., Xiao, Y., Chang, X., Huang, P.Y., Li, Z., Gupta, B.B., Chen, X., Wang, X.: A survey of deep active learning. ACM computing surveys (CSUR) 54(9), 1–40 (2021)
- <span id="page-63-5"></span>14. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2015: 18th International Conference, Munich, Germany, October 5-9, 2015, Proceedings, Part III 18. pp. 234–241. Springer (2015)
- <span id="page-63-13"></span>15. Sinha, S., Ebrahimi, S., Darrell, T.: Variational adversarial active learning. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 5972–5981 (2019)

- <span id="page-64-3"></span>16. Stoyanov, D., Taylor, Z., Carneiro, G., Syeda-Mahmood, T., Martel, A., Maier-Hein, L., Tavares, J.M.R., Bradley, A., Papa, J.P., Belagiannis, V., et al.: Deep learning in medical image analysis and multimodal learning for clinical decision support: 4th international workshop, dlmia 2018, and 8th international workshop, ml-cds 2018, held in conjunction with miccai 2018, granada, spain, september 20, 2018, proceedings, vol. 11045. Springer (2018)
- <span id="page-64-2"></span>17. Tang, Y., Hu, Y., Li, J., Lin, H., Xu, X., Huang, K., Lin, H.: Pld-al: Pseudolabel divergence-based active learning in carotid intima-media segmentation for ultrasound images. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 57–67. Springer (2023)
- <span id="page-64-0"></span>18. Wang, K., Zhang, D., Li, Y., Zhang, R., Lin, L.: Cost-effective active learning for deep image classification. IEEE Transactions on Circuits and Systems for Video Technology 27(12), 2591–2600 (2016)
- <span id="page-64-1"></span>19. Yang, L., Zhang, Y., Chen, J., Zhang, S., Chen, D.Z.: Suggestive annotation: A deep active learning framework for biomedical image segmentation. In: Medical Image Computing and Computer Assisted Intervention- MICCAI 2017: 20th International Conference, Quebec City, QC, Canada, September 11-13, 2017, Proceedings, Part III 20. pp. 399–407. Springer (2017)
- <span id="page-64-4"></span>20. Zhou, Z., Rahman Siddiquee, M.M., Tajbakhsh, N., Liang, J.: Unet++: A nested u-net architecture for medical image segmentation. In: Deep Learning in Medical Image Analysis and Multimodal Learning for Clinical Decision Support: 4th International Workshop, DLMIA 2018, and 8th International Workshop, ML-CDS 2018, Held in Conjunction with MICCAI 2018, Granada, Spain, September 20, 2018, Proceedings 4. pp. 3–11. Springer (2018)

Image /page/65/Picture/0 description: A square button with a light gray background and a darker gray border. In the center of the button, there is a circular icon with a bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a dark gray sans-serif font.

# **Semi-Supervised Learning for Deep Causal Generative Models**

Yasin Ibrahim<sup>1( $\boxtimes$ )</sup>, Hermione Warr<sup>1</sup>, and Konstantinos Kamnitsas<sup>1,2,3</sup>

<sup>1</sup> Department of Engineering Science, University of Oxford, Oxford, UK {yasin.ibrahim,hermione.warr}@eng.ox.ac.uk <sup>2</sup> Department of Computing, Imperial College London, London, UK

2 Department of Computing, Imperial College London, London, UK

<sup>3</sup> School of Computer Science, University of Birmingham, Birmingham, UK

**Abstract.** Developing models that are capable of answering questions of the form "How would  $x$  change if  $y$  had been  $z$ ?" is fundamental to advancing medical image analysis. Training causal generative models that address such counterfactual questions, though, currently requires that all relevant variables have been observed and that the corresponding labels are available in the training data. However, clinical data may not have complete records for all patients and state of the art causal generative models are unable to take full advantage of this. We thus develop, for the first time, a semi-supervised deep causal generative model that exploits the causal relationships between variables to maximise the use of all available data. We explore this in the setting where each sample is either fully labelled or fully unlabelled, as well as the more clinically realistic case of having different labels missing for each sample. We leverage techniques from causal inference to infer missing values and subsequently generate realistic counterfactuals, even for samples with incomplete labels. Code is available at: [https://github.com/yi249/ssl](https://github.com/yi249/ssl-causal)[causal](https://github.com/yi249/ssl-causal)

**Keywords:** Causal Inference · Semi-Supervised · Generative Models

### **1 Introduction**

The deployment of deep learning models to real-world applications faces a variety of challenges [\[16](#page-74-0)], with many arguing that this is due to lack of causal considerations  $[4,18]$  $[4,18]$  $[4,18]$ . A growing research area is the generation of counterfactuals (CFs), the manifestation of a sample in an alternative world where an upstream variable has been changed [\[10,](#page-74-2)[17](#page-74-3)[,21](#page-74-4)]. Such techniques are particularly useful in medical image analysis, where models are often hampered by lack of diversity in training data [\[5\]](#page-73-1), so methods to generate realistic synthetic samples from underrepresented classes are critical [\[13\]](#page-74-5). Incorporating structural causal equations into a

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_28) 28.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 294–303, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_28)\_28

deep learning framework has been shown to provide a powerful tool for counterfactual generation [\[17](#page-74-3)]. These ideas were extended by the development of a hierarchical VAE structure for greater image fidelity [\[7\]](#page-73-2). This method consists, however, of a separately trained generative model and structural causal model, represented by a directed acyclic graph (DAG), and hence, the two components are unable to leverage information from one another during training. Moreover, these methods rely on fully labelled samples, so are unable to use additional data where true values are unavailable for some (or all) variables of the causal graph.

Data with limited labels are ubiquitous, so semi-supervised methods are of particular interest. A common approach to semi-supervised learning is consistency regularisation under transformations of the input  $[1,11,22]$  $[1,11,22]$  $[1,11,22]$  $[1,11,22]$ . Alongside our generative model, we present this approach from a causal perspective and demonstrate how it fits naturally into our framework. Semi-supervised methods also have a causal motivation [\[19](#page-74-8)] due to the principle of independence of cause and mechanism (ICM) [\[18](#page-74-1)], which suggests that possessing information on the effect (image) alone is beneficial for learning the joint distribution of cause (labels) and effect. In summary, we make the following contributions:

- Introduce a semi-supervised deep causal generative model,
- Generate and evaluate counterfactuals with missing causal variables,
- Provide a causal perspective on the consistency regularisation technique for semi-supervised learning,
- Inspired by the ICM, investigate the performance of our method when parent variables are missing versus when child variables are missing.

To illustrate this, we first use a semi-synthetic dataset based on Morpho-MNIST [\[6](#page-73-4)] which allows us to explicitly measure performance given the known underlying causal relationships. We then assess our method on the MIMIC-CXR dataset [\[9](#page-74-9)[,12](#page-74-10)] to demonstrate its capabilities on real, more complex, medical data.

### **2 Background**

A (Markovian) Structural Causal Model (SCM) is a 4-tuple [\[21](#page-74-4)]:

$$
\mathcal{M} = \langle V, U, \mathcal{F}, P(U) \rangle
$$

where,  $V = \{v_1, \ldots, v_n\}$  is the set of endogenous variables of interest,  $U =$  $\{u_1, \ldots, u_n\}$  is the set of exogenous (noise) variables,  $P(U)$  is the prior distribution over them, and  $\mathcal{F} = \{f_1, \ldots, f_n\}$  is a set of functions assigning values to the endogenous variables. Moreover, we assume that each endogenous variable,  $x_i$ , is assigned deterministically by its direct causes, i.e. its parents pa<sub>i</sub>  $\subseteq V \setminus \{v_i\}$ , and the corresponding noise variable,  $u_i$  via the structural assignments,

<span id="page-66-0"></span>
$$
v_i := f_i(\text{pa}_i, u_i). \tag{1}
$$

This supersedes conventional Bayesian approaches as it allows for greater control by explicitly considering the structural relationships between variables. We achieve this through the do-operation [\[3](#page-73-5)], which makes assignments of the form  $d\sigma(x_i = a)$ . This disconnects  $x_i$  from its parents, and we obtain an intervened distribution over the endogenous variables,

$$
P(V|\text{do}(v_i = a)) = \prod_{j \neq i} p(v_j|pa_j) \cdot 1\!\!1_{\{v_i = a\}}
$$

However, such interventions provide only population-level effect estimations [\[3\]](#page-73-5). To narrow this down to unit-level and generate counterfactuals for individual samples, the following procedure is carried out [\[18\]](#page-74-1):

- 1. **Abduction**: Use the data to update the prior probability on the exogenous noise  $p(U)$  to obtain  $p(U|V)$
- 2. **Action**: Perform an intervention  $dofV = A$  to obtain a modified SCM, denoted by  $\mathcal{\tilde{M}}_{do(V = A)}$ .
- 3. **Prediction**: Use  $\tilde{\mathcal{M}}_{\text{do}(V = A)}$  to estimate the values of the desired variables.

Image /page/67/Figure/7 description: This image displays three diagrams related to a machine learning model. The first diagram, on the left, shows an input variable 'x' feeding into a neural network labeled 'q\_phi(.)|x'. This network outputs variables 'y\_C', 'y\_E', and 'z\_L'. There are also variables 'y\_C\*' and 'y\_E\*' shown above, with dashed lines connecting them to 'y\_C' and 'y\_E' respectively. The second diagram, in the middle, shows a generative process labeled 'p\_theta(.)'. It takes 'y\_C', 'y\_E', and 'z\_1' as inputs and outputs 'x''. The third diagram, on the right, is a causal graph. It shows 'x' influencing 'y\_E'' and 'z'. 'z' influences 'x~'. 'y\_E'' and 'y\_C' influence 'u'. 'y\_C' and 'y\_E' also influence 'u'. 'y\_C' and 'y\_E' are influenced by 'y\_C\*' and 'y\_E\*' respectively. 'y\_C' and 'y\_E' also influence 'x~'.

<span id="page-67-0"></span>Fig. 1. Model outline. Green: observed, Grey: latent, Red: predicted, Blue: causal generative, Yellow: decoding. (left) Training; we use the *y* predictions for decoding unless they are observed, (right) CF generation. (Color figure online)

## **3 Methodology**

An overview of our method is shown in Fig. [1.](#page-67-0) Herein, the endogenous variables consist of image, *x*, and variables, *y*; denoted by *y*<sup>∗</sup> when observed and by *y* when predicted. Latent variables  $z = z_{1:L}$  make up part of the exogenous noise for *x*, modelled using a hierarchical latent structure, following [\[7](#page-73-2)[,20](#page-74-11)]. Our model extends this structure with a predictive part that infers the endogenous variables, *y*, enabling counterfactual generation in the case of missing labels. For clarity, we limit derivations to the case with a single cause variable,  $y_C$ , and effect variable,

 $y<sub>E</sub>$ , but this can be extended to any finite number of variables with any causal structure. The ELBO loss for **fully labelled samples** drawn from  $\mathcal{D}_L$  is  $\mathcal{S}(x, y)$ :

<span id="page-68-1"></span>
$$
\log p_{\theta}(x) \geq \mathbb{E}_{q_{\phi}(z|x,y_E,y_C)} \left[ \log \frac{p_{\theta}(x|z,y_E,y_C)p_{\theta}(z)p_{\theta}(y_E|y_C)p_{\theta}(y_C)}{q_{\phi}(z|x,y_E,y_C)} \right]
$$
  

$$
\Rightarrow \mathcal{S}(x,y) := -\mathcal{L}(x,y) - \log p_{\theta}(y_E|y_C) - \log p_{\theta}(y_C)
$$

where  $\mathcal{L}(x, y)$  is the ELBO for a conditional VAE and  $p_{\theta}(\cdot)$  are (Gaussian) priors. For the **unlabelled samples**, drawn from  $\mathcal{D}_U$ , we minimise the loss:

$$
\mathcal{U}(x) := -\mathbb{E}_{q_{\phi(y_E|x)}} \left[ \mathbb{E}_{q_{\phi(y_C|x,y_E)}}(\mathcal{L}(x,y) - D_{\text{KL}}\{q_{\phi}(y_C|x,y_E)||p_{\theta}(y_C)\}) \right] \tag{2}
$$
  
 
$$
+ D_{\text{KL}}\{q_{\phi}(y_E|x)||p_{\theta}(y_E|y_C)\}.
$$

Here, we predict the labels using  $y' \sim q_{\phi}(y|x)$  and regularise them via the KLdivergence with their respective prior  $p_{\phi}(y)$ . When **only cause y<sub>C</sub>** is labelled, for samples  $(x, y_C) \in \mathcal{D}_C$ , we minimise the loss:

<span id="page-68-0"></span>
$$
\mathcal{C}(x,y_C) := -\mathbb{E}_{q_{\phi}(y_E|x)}[\mathcal{L}(x,y)] - \log p_{\theta}(y_C) + D_{\mathrm{KL}}\{q_{\phi}(y_E|x)||p_{\theta}(y_E|y_C)\}.
$$
 (3)

When **only effect**  $y<sub>E</sub>$  is labelled, we minimise the loss:

$$
\mathcal{E}(x,y_E) := -\mathbb{E}_{q_{\phi}(y_C|x,y_E)}[\mathcal{L}(x,y) + \log p_{\theta}(y_E|y_C)] + D_{\text{KL}}\{q_{\phi}(y_C|x,y_E)||p_{\theta}(y_C)\},\,
$$

for samples  $(x, y_E) \in \mathcal{D}_E$ . In the case of discrete variables, when the true labels, *y*<sup>∗</sup>, are not provided, we supplement these losses by inversely weighting samples with missing labels by the entropy of the labels predicted by the encoder,  $H_{\phi}(y'|x)$ . For example, when  $y_E$  is missing, we multiply the expectation in [\(3\)](#page-68-0) by  $1 - H_{\phi}(y_E'|x)$ . We use entropy here as an indicator for predictive uncertainty, to inform us how much to 'trust' the predicted label.

Under the current construction, the parent predictors,  $q_{\phi}(y_E|x)$  and  $q_{\phi}(y_C | x, y_E)$ , are only trained when the parent variables are unobserved. To ensure the model is able to learn a good representation of *y*, we include additional classification terms in the supervised loss [\[14](#page-74-12)], giving the **total loss** to train our model:

$$
\mathcal{T}(x,y) := \sum_{(x,y)\in\mathcal{D}_L} \mathcal{S}(x,y) + \sum_{x\in\mathcal{D}_U} \mathcal{U}(x) + \sum_{(x,y_C)\in\mathcal{D}_C} \mathcal{C}(x,y_C)
$$

$$
+ \sum_{(x,y_E)\in\mathcal{D}_E} \mathcal{E}(x,y_E) - \mathbb{E}_{(x,y)\in\mathcal{D}_L} [\log q_\phi(y_i|y_{
$$

In the last term, labeled variables  $y_i$  are placed in a topological ordering  $[8]$ starting with the root nodes. Thus for all  $i$ , the ancestors of  $y_i$  are a subset of  $y_{\leq i}$  and its descendants are a subset of  $y_{\geq i}$ . In [\(2\)](#page-68-1), we see that in the unlabelled case, we require expectations over the labels. For a single discrete label *y*, this can be achieved by summing over all possible values of *y* [\[14\]](#page-74-12),

$$
\mathbb{E}_{q_{\phi}(y|x)}[f(y,\cdot)] = \sum_{y} q_{\phi}(y|x) \cdot f(y,\cdot).
$$

However, this quickly becomes computationally expensive as the number of variables or classes grows, and is intractable for continuous variables. We avoid using a Monte-Carlo sampler, where taking more samples leads to similar computational costs. Instead, we propose lowering this variance by beginning training using only the labelled data. By doing so, predictors  $q_{\phi}(\cdot|x)$  reach a sufficiently high accuracy before being used to estimate missing labels for the rest of the data.

**Counterfactual Regularisation.** To further improve our model, we turn to a causal treatment of consistency regularisation [\[22\]](#page-74-7). For this, we restrict perturbations of the input image to interventions on the DAG governing the causal variables. For example, for input image x with variables  $(y_C, y_E)$ , we alter effect variable  $y_E$  via do $(y_E = \tilde{e})$ , to obtain new image  $\tilde{x}$  with causal variables  $(\tilde{y}_C, \tilde{y}_E)$ . If the DAG is obeyed, the cause variable should remain invariant to this perturbation and we can thus impose a loss that penalises divergence between  $y_C$  and *y*<sup>C</sup>. In the context of our model, we predict  $\tilde{y}_C \sim q_{\phi}(\cdot|\tilde{x})$  and minimise *D*(*y*<sub>C</sub>*,*  $\tilde{y}_C$ *)*, where  $D(\cdot, \cdot)$  is an appropriate distance metric. If  $y_C$  is unknown, we predict its value using  $y_C \sim q_\phi(\cdot|x)$ . Suppose instead we alter the cause variable; in this case, the effect should change given the DAG. As such, we predict  $\tilde{y}_E \sim q_\phi(\cdot|\tilde{x})$ and then compute the counterfactual of  $y_E$  under the proposed intervention on *y<sub>C</sub>*. When *y*<sub>E</sub> is unlabelled, we first predict it using  $y_E \sim q_\phi(\cdot|x)$ . This causal interpretation improves the robustness not only of the generative component of the model, but also the causal inference elements,  $p_{\theta}(y_i|y_{\leq i})$ .

**Counterfactual Generation.** Once the generative model is trained, we use the predictive component  $p_{\theta}(y_i|y_{\leq i})$  to generate counterfactuals,  $\tilde{x}$ . For abduction we require the structural assignments  $(1)$  to be invertible in  $u$ , so we encode  $p_{\theta}(y_i|y_{\leq i})$  as an invertible  $g_{y_{\leq i}}(u), u \sim p(u)$ , parameterised by the causal parents *y*<sub> $\lt i$ </sub> [\[17](#page-74-3)]. Counterfactual generation can then be expressed as  $\tilde{y}_i = g_{\tilde{y}_{i}}(g_{y(i)}^{-1}(y_i))$ so that each counterfactual value can be calculated in sequence. If a label  $y_i$  is missing, we use our trained predictor  $q_{\phi}(y_i|x, y_{>i})$  to impute it, e.g. Fig. [1](#page-67-0) (right) for when  $y_E$  is unobserved and we intervene on  $y_C$ .

## **4 Experiments**

### **4.1 Causal Analysis on Colour Morpho-MNIST**

True causal generative processes for medical imaging data are unknown. Therefore, to evaluate the causal aspects of our method in depth, we first use data adapted from Morpho-MNIST (60k training, 10k test samples) [\[6](#page-73-4)], considering the thickness (*t*) and intensity (*i*) of each digit. We increase the complexity of the causal structure by colouring the foreground  $(f)$  and background  $(b)$ , with each colour drawn from a distribution conditional on the digit (*d*), as in Fig. [2a](#page-70-0).

Image /page/70/Figure/1 description: The image displays two Bayesian networks, labeled (a) and (b). Both networks depict a central node 'x' in green, representing an outcome. Surrounding 'x' are several blue nodes, each labeled with a letter, indicating variables. Red nodes, labeled with 'U' followed by a letter, represent unobserved variables or noise. Arrows indicate causal relationships. In network (a), nodes 'f', 'd', 'b', 't', and 'i' are connected to 'x'. 'f' is influenced by 'Uf', 'd' by 'Ud', 'b' by 'Ub', 't' by 'Ut', and 'i' by 'Ui'. 'f' influences 'd' and 'x'. 'd' influences 'b' and 'x'. 'b' influences 'x'. 't' influences 'b' and 'i'. 'i' influences 'x'. Network (b) shows nodes 's', 'a', 'd', and 'r' connected to 'x'. 's' is influenced by 'Us', 'a' by 'Ua', 'd' by 'Ud', and 'r' by 'Ur'. 's' influences 'x'. 'a' influences 'd' and 'x'. 'd' influences 'x'. 'r' influences 'x'.

**Fig. 2.** (a) DAG for Colour MorphoMNIST, *d*: digit, *f*: foreground (digit) color, *b*: background color, *t*: thickness, *i*: intensity. (b) DAG for MIMIC-CXR, *s*: sex, *a*: age, *d*: disease status, *r*: race. *U*: respective exogenous noise variables.

Image /page/70/Figure/3 description: The image contains two plots. The plot on the left shows the accuracy (%) on the y-axis versus the number of labelled samples on the x-axis. There are three lines: SSL (blue dots), Flexible (red dots), and Supervised (green dots). The SSL and Flexible lines reach nearly 100% accuracy with 1000 labelled samples and stay at 100% accuracy thereafter. The Supervised line starts at about 12% accuracy with 500 labelled samples and increases to about 90% accuracy with 5000 labelled samples. The plot on the right shows the accuracy (%) on the y-axis versus the number of fully labelled samples on the x-axis. There are two lines: SSL+Flexible w/ CF Reg (blue dots) and SSL+Flexible (red dots). Both lines start at around 60% accuracy with 100 fully labelled samples and increase to about 98% accuracy with 500 fully labelled samples. The SSL+Flexible w/ CF Reg line generally shows slightly higher accuracy than the SSL+Flexible line for most of the data points.

(a) Supervised vs SSL vs Flexible.

<span id="page-70-1"></span><span id="page-70-0"></span>(b) SSL+Flexible for very few labels.

**Fig. 3.** Colour Morpho-MNIST: Accuracy of  $d\Omega$  = *k*) on random test images for uniformly random  $k \in \{0, \ldots, 9\} \backslash d$  where *d* is the digit of the original image. For SSL, the *x*-axis represents to the number of fully labelled samples; for Flexible it represents the number of labels for each variable across all the samples. For SSL+Flexible we use 600 randomly allocated labels for each variable in addition to the number of fully labelled samples denoted by the *x*-axis.

**Counterfactual Effectiveness.** As baseline, we use the state of the art supervised method for counterfactual image generation [\[7](#page-73-2)] trained only on the labelled samples of each experiment. We compare this against our method for a labelledunlabelled split (SSL in figures) and for labels missing randomly for each variable (Flexible). We measure the effectiveness [\[15](#page-74-14)] of our counterfactual generations by abducting and intervening on test images with random interventions before using classifiers or regressors,  $q(\cdot|x)$ , trained independently on uncorrelated data, to measure how well the desired change has been captured.

Figure [3a](#page-70-1) highlights the improvement by our method over the purely supervised approach. Even when only 1000 samples (∼ 1*.*67%) are labelled, we achieve near perfect effectiveness for changed digit counterfactuals. This holds both when the data is split into distinct labelled-unlabelled sets and when these labels are missing randomly. Moreover, in Fig. [3b](#page-70-1), counterfactual regularisation improves performance for very low labelled sample sizes by an average of ∼ 2*.*2%. Table [1](#page-71-0) demonstrates how the causal relationships are learned significantly better using

| Model      | Labelled | MAE<br>(↓) | $p_{	heta}(f)$ | $p_{	heta}(b)$ | $p_{	heta}(i,t)$ | $q(f 	ilde{x})$ | $q(b 	ilde{x})$ | $q(i,t 	ilde{x})$ |
|------------|----------|------------|----------------|----------------|------------------|-----------------|-----------------|-------------------|
| Supervised | 1000     | 3.91       | $-1.46$        | $-1.49$        | $-38.21$         | $-1.01$         | $-1.17$         | $-32.98$          |
|            | 5000     | 3.84       | $-1.31$        | $-1.38$        | $-21.38$         | $-0.63$         | $-0.93$         | $-28.44$          |
|            | 60,000   | 3.75       | $1.20$         | $1.24$         | $-5.55$          | $1.17$          | $1.18$          | $-14.42$          |
| SSL        | 1000     | 3.85       | $1.05$         | $1.10$         | $-14.26$         | $0.81$          | $1.08$          | $-26.02$          |
|            | 5000     | 3.83       | $1.10$         | $1.12$         | $-7.40$          | $1.01$          | $1.19$          | $-22.39$          |
| Flexible   | 1000     | 3.86       | $1.11$         | $1.13$         | $-17.93$         | $0.77$          | $1.15$          | $-28.20$          |
|            | 5000     | 3.84       | $1.14$         | $1.16$         | $-13.98$         | $1.07$          | $1.10$          | $-19.56$          |

<span id="page-71-0"></span>**Table 1.** Colour Morpho-MNIST: Log likelihoods (↑) of the child variables. Colour log likelihoods  $\in (-\infty, 2.239]$ , intensity log likelihoods  $\in (-\infty, -1.336]$ .

our method, with regards to both the distributions inferred from the DAG,  $p_{\theta}(\cdot)$ , and the manifestations of these changes in the counterfactual image,  $q(\cdot|\tilde{x})$ .

**Independence of Cause and Mechanism.** Inspired by insights on the ICM [\[19](#page-74-8)], we analyse how our method performs in the specific cases of the cause variable missing and the effect present, and vice-versa, by varying the number of thickness and intensity labels while keeping the others. Table [2](#page-71-1) (left) shows that the settings with greater proportions of intensity (effect) labels tend to produce better joint distributions, supporting the ICM hypothesis [\[19](#page-74-8)]. This is significant for domains with limited labelled data such as healthcare, as it suggests that, given an identified cause-effect relationship and limited capability to obtain labels, focusing on labelling the effect should provide improved performance.

<span id="page-71-1"></span>**Table 2.** (left) Colour Morpho-MNIST: Cause and mechanism experiment. (right) MIMIC-CXR: For each intervention  $do(\cdot)$ , the 3 rows correspond to training with 10%, 20%, 30% of variables labelled, all using CF regularisation. Semi-supervision in both settings (SSL, Flexible) outperforms pure supervision (Sup.).

| i labels | t labels | do(t)                 |                        | do(i)               |                     |              | Disease (↑) |       |             | Age (↓) |       |             | Sex (↑)     |       |  | Race (↑) |  |  |
|----------|----------|-----------------------|------------------------|---------------------|---------------------|--------------|-------------|-------|-------------|---------|-------|-------------|-------------|-------|--|----------|--|--|
|          |          | $p_{\theta}(i,t)$ (↑) | $q(i,t \tilde{x})$ (↑) | $ t-\tilde{t} $ (↓) | $ i-\tilde{i} $ (↓) | Sup.         | SSL         | Flex. | Sup.        | SSL     | Flex. | Sup.        | SSL         | Flex. |  |          |  |  |
| 300      | 2700     | -21.84                | -29.47                 | <b>0.089</b>        | 0.293               |              |             |       |             |         |       |             |             |       |  |          |  |  |
| 600      | 2400     | -18.89                | -26.17                 | 0.102               | 0.203               |              |             |       |             |         |       |             |             |       |  |          |  |  |
| 1200     | 1800     | -18.01                | -24.78                 | 0.099               | 0.221               |              |             |       |             |         |       |             |             |       |  |          |  |  |
| 1500     | 1500     | -19.40                | -25.43                 | 0.132               | 0.172               |              |             |       |             |         |       |             |             |       |  |          |  |  |
| 1800     | 1200     | -16.27                | -22.13                 | 0.120               | 0.161               |              |             |       |             |         |       |             |             |       |  |          |  |  |
| 2400     | 600      | -14.62                | -19.32                 | 0.146               | 0.093               |              |             |       |             |         |       |             |             |       |  |          |  |  |
| 2700     | 300      | <b>-14.15</b>         | <b>-17.56</b>          | 0.152               | <b>0.088</b>        |              |             |       |             |         |       |             |             |       |  |          |  |  |
| do(d)    | 10%      | 0.55                  | 0.70                   | <b>0.71</b>         | 15.62               | 9.12         | 9.19        | 0.95  | 0.95        | 0.94    | 0.71  | 0.71        | 0.68        |       |  |          |  |  |
|          | 20%      | 0.57                  | <b>0.78</b>            | 0.77                | 14.21               | 8.65         | 8.52        | 0.94  | 0.99        | 0.99    | 0.74  | 0.77        | 0.77        |       |  |          |  |  |
|          | 30%      | 0.68                  | 0.83                   | <b>0.84</b>         | 12.95               | 8.53         | 7.82        | 0.98  | 1.00        | 0.95    | 0.77  | 0.82        | 0.81        |       |  |          |  |  |
| do(a)    | 10%      | 0.87                  | 0.91                   | 0.93                | 15.01               | <b>13.38</b> | 13.75       | 0.90  | 0.96        | 0.90    | 0.72  | 0.81        | 0.77        |       |  |          |  |  |
|          | 20%      | 0.87                  | 0.96                   | 0.96                | 15.40               | <b>12.57</b> | 13.21       | 0.94  | 1.00        | 0.99    | 0.71  | 0.83        | 0.80        |       |  |          |  |  |
|          | 30%      | 0.90                  | 0.96                   | 0.95                | 14.26               | <b>12.07</b> | 12.15       | 0.98  | 1.00        | 1.00    | 0.78  | 0.85        | 0.84        |       |  |          |  |  |
| do(s)    | 10%      | 0.84                  | 0.91                   | 0.91                | 14.15               | 9.31         | 9.31        | 0.69  | <b>0.97</b> | 0.90    | 0.69  | 0.74        | 0.77        |       |  |          |  |  |
|          | 20%      | 0.86                  | 0.97                   | 0.96                | 13.57               | 8.25         | 7.87        | 0.73  | <b>1.00</b> | 0.99    | 0.69  | 0.80        | 0.83        |       |  |          |  |  |
|          | 30%      | 0.89                  | 0.97                   | 0.98                | 12.92               | 7.99         | 7.95        | 0.78  | <b>0.99</b> | 0.99    | 0.77  | 0.80        | 0.82        |       |  |          |  |  |
| do(r)    | 10%      | 0.84                  | 0.93                   | 0.95                | 15.08               | 9.76         | 9.73        | 0.96  | 0.98        | 0.95    | 0.46  | <b>0.53</b> | 0.52        |       |  |          |  |  |
|          | 20%      | 0.88                  | 0.95                   | 0.95                | 14.27               | 7.87         | 7.79        | 0.95  | 1.00        | 0.99    | 0.50  | <b>0.57</b> | 0.56        |       |  |          |  |  |
|          | 30%      | 0.93                  | 0.96                   | 0.97                | 14.11               | 7.37         | 7.61        | 0.98  | 1.00        | 1.00    | 0.55  | 0.62        | <b>0.63</b> |       |  |          |  |  |

### **4.2 Counterfactuals for Medical Imaging Data**

To evaluate our method on medical imaging data, we apply it to the MIMIC-CXR dataset (50k training, 20k test samples) [\[9,](#page-74-9)[12](#page-74-10)]. We assume the casual structure used in [\[7\]](#page-73-2) with variables disease  $(d)$ , age  $(a)$ , sex  $(s)$ , race  $(r)$ , with the only non-independence being that *a* causes *d* (Fig. [2b](#page-70-0)). For disease, we use the presence of pleural effusion as a binary variable and we train models using 10%, 20%, 30%, 40% and 50% of the total labels for each of the three models (Supervised, SSL, Flexible). As the causal structure is simpler, we measure performance, over 3 seeds, by intervening on each variable separately before measuring the ROCAUC for the discrete variables and the MAE for age.

Image /page/72/Figure/2 description: The image on the left is a line graph showing the relationship between labeled samples and average AUC. The x-axis represents the percentage of labeled samples, ranging from 10% to 50%. The y-axis represents the average AUC, ranging from 0.55 to 0.85. There are two lines on the graph: a blue line representing 'SSL w/ CF Reg' and a red line representing 'SSL'. The blue line shows a generally increasing trend, starting at approximately 0.73 at 10% labeled samples and reaching approximately 0.83 at 50% labeled samples. The red line also shows an increasing trend, but at a lower level, starting at approximately 0.56 at 10% labeled samples and reaching approximately 0.66 at 50% labeled samples. The image on the right displays six chest X-ray images, labeled (1) through (6), arranged in two rows of three. These images appear to be medical scans of lungs.

<span id="page-72-0"></span>**Fig. 4.** (a) CF Regularisation on MIMIC-CXR. (b) MIMIC-CXR CFs from model trained on 40% labels. From top-left: (1) original: white, healthy, 20-year-old male, (2)  $do(age=90), (3) do(diseased), (4) do(asian), (5) do(female), (6) do(all).$ 

From the cells on the diagonal of Table [2](#page-71-1) (right), we see that our method tends to improve upon the supervised approach with regards to implementing interventions. The other cells are essentially a measure of reconstruction quality, since they involve evaluating variables that have not been intervened on. As such, the closeness of these values for the various models suggests that the achieved counterfactual generation gains are primarily due to differences in the causal inference component. This indicates that it would be fruitful to focus future efforts on improving this section of the model. This holds for both SSL and Flexible, demonstrating that practitioners implementing our approach need not prioritise achieving full labelling for any given sample over collecting as many labels as possible, bolstering the usability of the model. Figure [4a](#page-72-0) demonstrates the increased interventional accuracy provided by CF regularisation. Moreover, as shown in Fig. [4b](#page-72-0), our model is able to exhibit clear visual changes for the various CFs, indicating the numerical results are not due to minute changes undetectable to the human eye [\[2\]](#page-73-6). To build upon this, an avenue of future research would be to use this approach to generate additional training data for underrepresented populations in medical datasets and evaluate how this aids downstream tasks.

## **5 Conclusion**

This study introduces a semi supervised deep causal generative model to enable training on causal data with missing labels in medical imaging. Experiments on a coloured Morpho-MNIST dataset, where the whole generative process is known, along with experiments on real clinical data from MIMIC-CXR, demonstrate that our approach uses unlabelled and partially labelled data effectively and improves over the state of the art fully supervised causal generative models. The key practical contribution of this work is that it enables training causal models on clinical databases where patient data may have missing labels, which previous models could not use, relaxing one of the main requirements for training a causal model. A limitation of our work is that we assume the DAG structure is known a priori. Hence, if this is misspecified, there are no guarantees on the correctness of the generated counterfactuals. A possible next step could thus be to explore cases with limited information on the DAG structure of the causal variables.

**Acknowledgments.** Yasin Ibrahim and Hermione Warr are supported by the EPSRC Centre for Doctoral Training in Health Data Science (EP/S02428X/1). The authors also acknowledge the use of the University of Oxford Advanced Research Computing (ARC) facility in carrying out this work [\(http://dx.doi.org/10.5281/zenodo.22558\)](http://dx.doi.org/10.5281/zenodo.22558).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-73-3"></span>1. Bachman, P., Alsharif, O., Precup, D.: Learning with pseudo-ensembles. ArXiv **abs/1412.4864** (2014)
- <span id="page-73-6"></span>2. Banerjee, I., Bhimireddy, A.R., Burns, J.L., Celi, L.A., Chen, L.C., Correa, R., Dullerud, N., Ghassemi, M., Huang, S.C., Kuo, P.C., Lungren, M.P., Palmer, L.J., Price, B., Purkayastha, S., Pyrros, A., Oakden-Rayner, L., Okechukwu, C., Seyyed-Kalantari, L., Trivedi, H., Wang, R., Zaiman, Z., Zhang, H., Gichoya, J.W.: Ai recognition of patient race in medical imaging: a modelling study. The Lancet. Digital health **4**, e406 – e414 (2021)
- <span id="page-73-5"></span>3. Bareinboim, E., Correa, J.D., Ibeling, D., Icard, T.F.: On pearl's hierarchy and the foundations of causal inference. Probabilistic and Causal Inference (2022)
- <span id="page-73-0"></span>4. Bengio, Y., Courville, A.C., Vincent, P.: Representation learning: A review and new perspectives. IEEE Transactions on Pattern Analysis and Machine Intelligence **35**, 1798–1828 (2012)
- <span id="page-73-1"></span>5. Coelho de Castro, D., Walker, I., Glocker, B.: Causality matters in medical imaging. Nature Communications **11** (12 2020)
- <span id="page-73-4"></span>6. Castro, D.C., Tan, J., Kainz, B., Konukoglu, E., Glocker, B.: Morpho-MNIST: Quantitative assessment and diagnostics for representation learning. Journal of Machine Learning Research **20**(178) (2019)
- <span id="page-73-2"></span>7. De Sousa Ribeiro, F., Xia, T., Monteiro, M., Pawlowski, N., Glocker, B.: High fidelity image counterfactuals with probabilistic causal models. In: Proceedings of the 40th International Conference on Machine Learning. Proceedings of Machine Learning Research, vol. 202, pp. 7390–7425 (07 2023)

- <span id="page-74-13"></span>8. Gagrani, M., Rainone, C., Yang, Y., Teague, H., Jeon, W., Hoof, H.V., Zeng, W.W., Zappi, P., Lott, C., Bondesan, R.: Neural topological ordering for computation graphs (2022)
- <span id="page-74-9"></span>9. Goldberger, A.L., Amaral, L.A.N., Glass, L., Hausdorff, J.M., Ivanov, P.C., Mark, R.G., Mietus, J.E., Moody, G.B., Peng, C.K., Stanley, H.E.: Physionet: Components of a new research resource for complex physiologic signals". circu-lation vol (2000)
- <span id="page-74-2"></span>10. Hess, K., Melnychuk, V., Frauen, D., Feuerriegel, S.: Bayesian neural controlled differential equations for treatment effect estimation. In: The Twelfth International Conference on Learning Representations (2024)
- <span id="page-74-6"></span>11. Hu, W., Miyato, T., Tokui, S., Matsumoto, E., Sugiyama, M.: Learning discrete representations via information maximizing self-augmented training. ArXiv (2017)
- <span id="page-74-10"></span>12. Johnson, A.E.W., Pollard, T.J., Berkowitz, S.J., Greenbaum, N.R., Lungren, M.P., ying Deng, C., Mark, R.G., Horng, S.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Scientific Data **6** (2019)
- <span id="page-74-5"></span>13. Jones, C., Castro, D.C., Ribeiro, F.D.S., Oktay, O., McCradden, M., Glocker, B.: No fair lunch: A causal perspective on dataset bias in machine learning for medical imaging (2023)
- <span id="page-74-12"></span>14. Kingma, D.P., Rezende, D.J., Mohamed, S., Welling, M.: Semi-supervised learning with deep generative models
- <span id="page-74-14"></span>15. Monteiro, M., Ribeiro, F.D.S., Pawlowski, N., Castro, D.C., Glocker, B.: Measuring axiomatic soundness of counterfactual image models (2023)
- <span id="page-74-0"></span>16. Paleyes, A., Urma, R.G., Lawrence, N.D.: Challenges in deploying machine learning: A survey of case studies. ACM Computing Surveys **55**, 1 – 29 (2020)
- <span id="page-74-3"></span>17. Pawlowski, N., Castro, D.C., Glocker, B.: Deep structural causal models for tractable counterfactual inference. In: Advances in Neural Information Processing Systems (2020)
- <span id="page-74-1"></span>18. Peters, J., Janzing, D., Schölkopf, B.: Elements of Causal Inference: Foundations and Learning Algorithms. Adaptive Computation and Machine Learning, MIT Press (2017)
- <span id="page-74-8"></span>19. Sch¨olkopf, B., Janzing, D., Peters, J., Sgouritsa, E., Zhang, K., Mooij, J.: On causal and anticausal learning. Proceedings of the 29th International Conference on Machine Learning, ICML 2012 **2** (06 2012)
- <span id="page-74-11"></span>20. Sønderby, C.K., Raiko, T., Maaløe, L., Sønderby, S.K., Winther, O.: Ladder variational autoencoders
- <span id="page-74-4"></span>21. Xia, K., Lee, K.Z., Bengio, Y., Bareinboim, E.: The causal-neural connection: Expressiveness, learnability, and inference. In: Neural Information Processing Systems (2021)
- <span id="page-74-7"></span>22. Xie, Q., Dai, Z., Hovy, E.H., Luong, M.T., Le, Q.V.: Unsupervised data augmentation for consistency training. [arXiv: Learning](http://arxiv.org/abs/Learning) (2019)

Image /page/75/Picture/0 description: A square button with a rounded border contains a circular icon and text. The icon is a gray circle with a gray ribbon shape inside, pointing to the right. Below the icon, the text "Check for updates" is displayed in gray, sans-serif font.

# SynCellFactory: Generative Data Augmentation for Cell Tracking

Moritz  $\operatorname{Sturm}^{(\boxtimes)}$ , Lorenzo Cerrone, and Fred A. Hamprecht

IWR, Heidelberg University, 69120 Heidelberg, Germany <EMAIL>, <EMAIL>, <EMAIL>

Abstract. Cell tracking remains a pivotal yet challenging task in biomedical research. The full potential of deep learning for this purpose is often untapped due to the limited availability of comprehensive and varied training data sets. In this paper, we present SynCellFactory, a generative method for cell video augmentation. At the heart of SynCell-Factory lies the ControlNet architecture, which has been fine-tuned to synthesize cell imagery with photorealistic accuracy in style and motion patterns. This technique enables the creation of synthetic, annotated cell videos that mirror the complexity of authentic microscopy time-lapses. Our experiments demonstrate that SynCellFactory boosts the performance of well-established deep learning models for cell tracking, particularly when original training data is sparse.

**Keywords:** Cell Tracking  $\cdot$  Generative Data Augmentation  $\cdot$  Microscopy Time-lapses

## 1 Introduction

Digital time-lapse microscopy allows for large-scale observations of cells over time, providing a deeper understanding of cellular processes  $[2,4,11]$  $[2,4,11]$  $[2,4,11]$  $[2,4,11]$  $[2,4,11]$ . However, to fully harness the potential of time-lapse imaging, automated cell tracking approaches are needed, which can provide a quantitative analysis of cell behavior for vast amounts of data.

Cell tracking is characterized by challenges such as variable image contrast, intricate behaviors such as cell division and, in some examples, indistinguishability of cells. Recent advancements in computer vision have shown that neural networks are highly effective in multi-object tracking tasks  $[6,8,15]$  $[6,8,15]$  $[6,8,15]$  $[6,8,15]$ . However, their application in cell tracking remains limited and exploratory, primarily due to the scarcity of annotated cell tracking data [\[13\]](#page-83-6).

In recent years, although medium-scale annotated tracking data sets in the order of several thousand timeframes have become available [\[13,](#page-83-6)[21,](#page-84-0)[27](#page-84-1)], they are limited to specific cell styles.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_29.](https://doi.org/10.1007/978-3-031-72390-2_29)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 304–313, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_29)\_29

To address these challenges, this paper introduces *SynCellFactory*, a generative data augmentation strategy specifically designed for cell tracking. We embrace the power of conditioned 2D diffusion models [\[19](#page-84-2)[,28](#page-84-3)] to generate highquality, fully annotated synthetic cell videos that mimic the appearance and behavior of real cell data sets.

Utilizing as little as one annotated cell video for training, *SynCellFactory* can generate an extensive library of annotated videos in a consistent style, effectively augmenting the available training data. This advancement holds the potential to transform cell tracking by enabling the application of sophisticated deep learning models previously hindered by data scarcity. Our research focuses on evaluating the impact of this data augmentation on the performance of state-of-the-art cell tracking models. The results indicate that this novel approach addresses the data scarcity issue and enhances tracking accuracy. We aim to open the door for large-scale deep learning architectures in cell tracking.

Previous work already demonstrated the effectiveness of generative data augmentation using diffusion models in terms of achievable image classification accuracy [\[1,](#page-83-7)[9](#page-83-8)[,20](#page-84-4)[,25](#page-84-5)]. These advances also start to impact the medical imaging domain, where they show great promise [\[5,](#page-83-9)[17](#page-84-6)[,18,](#page-84-7)[24\]](#page-84-8) and have already proven effective when used to train deep learning models [\[3,](#page-83-10)[7\]](#page-83-11). Closely related to our work, [\[22\]](#page-84-9) generates an entire video at once using a 3D diffusion model guided by optical flow; crucially, their approach does not produce tracking pseudo ground truth labels, while *SynCellFactory* does. In [\[10\]](#page-83-12), the authors propose a model based on CycleGAN [\[29](#page-84-10)] which is capable of generating raw data as well as lineage and segmentation pseudo ground truth. However, it relies on simulated ground truth segmentation masks to condition the data generation.

To summarize, our contributions are:

- *SynCellFactory*, a generative data augmentation pipeline for cell tracking.
- The proposed pipeline demonstrates out-of-the-box robust results on a wide variety of data sets without complex hyperparameter tuning or domainspecific knowledge.
- Empirical proof that the proposed data augmentation strategy can further enhance accuracy of a leading deep learning cell tracking method.

## 2 Method

*SynCellFactory* operates on the principle of decoupling cell dynamics from their appearance. Our model comprises two principal components:

- 1. A simple 2D motion model that simulates the spatial distribution and dynamics of cell cultures. It uses statistical parameters derived from real data, enabling *SynCellFactory* to produce coherent and physically plausible timelapses. This simulation approach enhances the diversity and realism of the generated data.
- 2. Two distinct ControlNets, each with a specific function, are trained for photorealistic rendering. The first, CN-Pos, is adept at inpainting cells at accurate

Image /page/77/Figure/1 description: The image displays a grid of microscopy images of cells, organized into rows labeled 'Real', 'T1', 'T2', and 'T3', and columns labeled with different cell types and imaging techniques: 'Fluo-C2DL-Huh7', 'Fluo-C2DL-MSC', 'Fluo-N2DH-GOWT1', 'PhC-C2DL-PSC', 'DIC-C2DH-HeLa', 'Fluo-N2DL-HeLa', and 'PhC-C2DH-U373'. The 'Real' row shows initial cell states. The 'T1', 'T2', and 'T3' rows likely represent observations at different time points or conditions. The 'Fluo' columns show fluorescent images, highlighting specific cellular structures, while 'PhC' and 'DIC' columns show phase contrast and differential interference contrast images, respectively, revealing cell morphology and texture.

<span id="page-77-0"></span>Fig. 1. Showcase of real (top row) and synthetic (other rows) images generated using *SynCellFactory*. Generated videos are provided in the supplementary materials. The training data sets are a subset of the 2D Cell Tracking Challenge [\[13](#page-83-6)[,26\]](#page-84-11) and provide a broad spectrum of cell lines and microscopy modalities.

spatial positions. The second, CN-Mov, focuses on the temporal displacement of individual cells across consecutive frames, ensuring temporal consistency. These ControlNets show efficient training capabilities, making them well suited for augmenting data sets in cell tracking applications.

In the following sections, we will detail the methodology of our model and introduce an automated protocol for training *SynCellFactory* on new data sets.

## 2.1 Motion Model

The goal of our motion model is to generate plausible spatial cell configuration and displacement. Our engine represents cells as 2D disks. The motion model initializes a population of cells with randomly sampled positions and sizes drawn from the cell area distribution of the training data. This population dynamically evolves following a stochastic Brownian motion, guided by statistics extracted from annotated real cell videos, which we model using a gamma distribution. Collision detection and resolution occur when two cells overlap. Using a hard sphere model, positions are adjusted with a repulsion vector until overlaps are resolved. By manipulating variables such as the number of cells, their movement speed, and the frequency of cell splitting events, we can tailor the complexity of the tracking task.

## 2.2 ControlNet

ControlNet [\[28](#page-84-3)] is a popular architecture for enabling the conditioning of text-toimage generative models. Our ControlNet uses the standard pre-trained Stable

Image /page/78/Figure/1 description: This figure illustrates a two-stage process for generating synthetic cell microscopy images. The top section, labeled "Automated Training," shows a sequence of real raw cell images and their corresponding ground truth segmentations over time (T', T'-1, T'-2, ..., 0). This data is used to train three components: CN-Pos, CN-Mov, and a Motion Model. The Motion Model is further detailed with a diagram showing cell representations (circles) and parameters like Area, Displacement, Distribution, and Split Rate. The bottom section, labeled "SynCellFactory Inference," demonstrates the application of these trained components. It shows a temporal sequence of motion models, conditioning data (segmentations with color-coded cell identities and motion vectors), and the resulting synthetic raw cell images. The inference process progresses from T to T-1, T-2, and so on, down to time 0. Finally, two example conditioning outputs are shown: "CN-Pos Conditioning" with colored cell masks and "CN-Mov Conditioning" with colored cell masks and motion vectors indicating cell movement.

<span id="page-78-0"></span>Fig. 2. *SynCellFactory* is a data augmentation pipeline designed to create unlimited high-quality synthetic raw video data and corresponding pseudo ground truth. It trains three key components using a small, and possibly sparsely labeled data set: Positional ControlNet (CN-Pos), Movement ControlNet (CN-Mov), and a 2D movement engine for realistic simulations. The process initiates in reverse, with the motion model generating a conditioning image at time T for CN-Pos. This image illustrates the expected centers of cells using colored dots, where each color signifies a specific cell state in the mitotic cycle: green during the interphase and blue during the different phases of cell division. CN-Pos then employs this information to generate a realistic frame for time T. Subsequently, CN-Mov assumes the role of producing the next frame T *−*1, using as conditioning an RGB image that combines the previously generated frame (in the red channel) with the projected positions and movement patterns (in green and blue channels). Derived from the motion model, these patterns represent each cell's trajectory from its current to its anticipated next position as a line connecting the two. By iteratively applying CN-Mov, *SynCellFactory* can efficiently produce time-lapse sequences of any desired length, suitable for training deep learning pipelines in cell tracking. (Color figure online)

Diffusion v1.5 backbone trained on natural images, fine-tuned to the appearance of biological images. Training a ControlNet for latent text-to-image diffusion models uses triplets  $(c_{\text{txt}}, c_{\text{img}}, i_{\text{tgt}})$ . The text conditioning for our experiments was fixed to the prompt "cell, microscopy, image". In the following sections, we describe the specific details and  $c_{\text{img}}$  conditioning the CN-Pos and CN-Move models.

Positional ControlNet. The positional ControlNet CN-Pos is tasked with drawing realistic looking cells conditioned on a given position. This model is used to generate the last frame of our synthetic videos and is the pre-trained backbone of the CN-Move model. At train time, the position map is constructed by computing the center coordinates for each cell given the corresponding detection ground truth. Each detection is represented in the conditioning image  $c_{\text{img}}$ as a disk with a fixed radius of  $r = \frac{\sqrt{A_c/\pi}}{4}$  $\frac{4c}{4}$ , where  $A_c$  is the data set average cell area in pixels. The disks are colored according to the stage in the cell cycle, changing colors through the phases of Mitosis and reverting post-cytokinesis. At inference time, the conditioning image  $c_{\text{img}}$  is derived by the state simulated by the motion model, converting the center of simulated cells into color-coded disks.

Movement ControlNet. CN-Mov is tasked with predicting frame  $t-1$  conditioned on the frame at time t, the position map at time  $t-1$ , and the displacement vectors. Displacement vectors describe the movement of the cells between frames and are encoded as lines connecting the center position of cells.

### 2.3 SynCellFactory Inference

During the sampling process, we apply the trained ControlNets on the output of our motion module to generate realistic-looking videos (see Fig. [2\)](#page-78-0). The inference process is initiated with CN-Pos generating frame t. Subsequently, CN-Mov iteratively takes the generated frame  $t$  and, in conjunction with the motion model, samples a new frame  $t - 1$ . This iterative process continues until the desired video length of 12 frames is reached, which is double the minimum mitosis cycle duration of tested datasets.

### 2.4 Segmentation Pseudo Ground Truth

*SynCellFactory* produces only raw video, detection, and lineage ground truth. To address the challenge of not producing instance segmentation ground truth, we rely on Cellpose [\[16](#page-84-12),[23\]](#page-84-13), a renowned deep learning framework for cell segmentation, to create pseudo-ground truth segmentation. We fine-tuned a pre-trained model from Cellpose for 100 epochs using ground truth segmentation masks from our training data.

We integrated the motion model into the segmentation process to improve accuracy. When a ground truth detection is present without a corresponding segmentation mask, we generate one by drawing a circle with a radius  $r = \sqrt{A_c/\pi}$ , providing an approximate mask for cells that are challenging to segment. In cases where a segmentation mask does not overlap with any part of the generated detection ground truth, it is removed.

### 2.5 Automated Training

To reduce the domain-specific expertise required to train *SynCellFactory*, we propose a fully automated pipeline for training the ControlNets and the sampling from the motion module.

The only required hyperparameters that have to be manually specified to produce our synthetic videos are: the number of videos to be generated, the number of frames per video, and the characteristic length of the mitosis cycle.

Other parameters, such as movement statistics for the motion model, are automatically inferred from the raw data and ground truth annotations.

## 3 Experiments and Results

### 3.1 Data Sets

To validate the proposed *SynCellFactory*, we use the publicly available Cell Tracking Challenge (CTC)  $[13,26]$  $[13,26]$  $[13,26]$ <sup>[1](#page-80-0)</sup> data sets.

For our experiments, we focus on the seven 2D data sets enumerated in Fig. [1.](#page-77-0) Each data set consists of two timelapses with full tracking annotations of ground truth and only partial hand-curated segmentation. The tracking ground truth includes detection and identity masks for each frame and the corresponding cell lineages. The number of frames per video ranges from 30 (Fluo-C2DL-Huh7) to 300 (PhC-C2DL-PSC).

Our experiments used a single time-lapse for the training and validation and one for testing tracking accuracy. Sample still frames from the mentioned data sets can be found in Fig. [1.](#page-77-0)

### 3.2 Experimental Setup

The training of CN-Pos and CN-Move follows the standard procedure as presented in [\[28\]](#page-84-3); the only major difference is that we also fine-tune the stable diffusion UNet decoder block. Since we could only access a single annotated timelaps for training our ControlNet, we relied on data augmentation. In particular, we found random cropping  $(h/2 \times w/2)$  of the images and random 90 °C rotations beneficial. To evaluate the usefulness of *SynCellFactory* as a data augmentation strategy, we used it in conjunction with the state-of-the-art deep learning model EmbedTrack [\[12\]](#page-83-13). EmbedTrack uses CNNs to predict cell segmentation and tracking jointly and already incorporates standard data augmentation techniques during training. The generative data augmentation by *SynCellFactory* can therefore be seen as additional data augmentation.

Tracking Metric. To evaluate the tracking performance of our trained models, we use the official tracking accuracy measure (TRA) provided by the Cell Tracking Challenge [\[14\]](#page-83-14). The TRA score is based on the concept of Acyclic Oriented Graph Matching AOGM. The TRA score is defined as TRA =  $1 - \frac{\min(AOGM,AOGM_0)}{AOGM_0}$ , where AOGM represents the weighted sum of operations required to build the prediction graph and  $AOGM<sub>0</sub>$  represents the weighted sum of operations required to build the ground truth graph. Higher TRA scores indicate better tracking performance.

<span id="page-80-0"></span> $^{-1}$ [http://celltrackingchallenge.net/2d-datasets/.](http://celltrackingchallenge.net/2d-datasets/)

<span id="page-81-0"></span>Table 1. Tracking Accuracy Measure TRA (higher is better) obtained with and without our *SynCellFactory*. The proposed data augmentation increases tracking accuracy for all but one of the CTC tested data sets. The  $\alpha$  mixing coefficient has been set to the optimal value for each data set. Error bars indicate the standard deviation over three runs. The number of tracking predictions underlying the TRA score are reported in supplementary material Table 1.

| data set        | W/o SynCellFactory                  | With SynCellFactory                 | $\alpha$ |
|-----------------|-------------------------------------|-------------------------------------|----------|
| Fluo-C2DL-Huh7  | <b><math>0.960 \pm 0.002</math></b> | <b><math>0.966 \pm 0.003</math></b> | 0.66     |
| Fluo-C2DL-MSC   | <b><math>0.624 \pm 0.060</math></b> | <b><math>0.685 \pm 0.060</math></b> | 0.50     |
| DIC-C2DH-HeLa   | <b><math>0.968 \pm 0.001</math></b> | <b><math>0.974 \pm 0.001</math></b> | 0.80     |
| Fluo-N2DH-GOWT1 | <b><math>0.980 \pm 0.003</math></b> | <b><math>0.989 \pm 0.002</math></b> | 0.48     |
| Fluo-N2DL-HeLa  | <b><math>0.939 \pm 0.002</math></b> | <b><math>0.981 \pm 0.002</math></b> | 0.87     |
| PhC-C2DL-PSC    | <b><math>0.958 \pm 0.001</math></b> | <b><math>0.960 \pm 0.001</math></b> | 0.20     |
| PhC-C2DH-U373   | <b><math>0.938 \pm 0.008</math></b> | <b><math>0.935 \pm 0.007</math></b> | 0.87     |

<span id="page-81-2"></span>Table 2. Comparison of the official CTC results. We improved the performance of EmbedTrack by using our data generation for all three data sets. Originally, Embed-Track was inapplicable to Fluo-C2DL-Huh7 due to a shortage of segmentation masks for training. The generated data sets provided a sufficient amount of segmentation masks, enabling EmbedTrack to track Fluo-C2DL-Huh7.

| data set       | EmbedTrack [12] | Ours  |
|----------------|-----------------|-------|
| Fluo-C2DL-Huh7 | -               | 0.920 |
| Fluo-C2DL-MSC  | 0.693           | 0.703 |
| DIC-C2DH-HeLa  | 0.934           | 0.943 |

Image /page/81/Figure/5 description: The image displays a grid of seven scatter plots, each illustrating the relationship between the number of training frames on the x-axis and tracking accuracy measure on the y-axis. Each plot is color-coded according to a mixing ratio alpha, indicated by a color bar on the right ranging from 0.0 to 1.0. The plots are arranged in a 3x3 grid, with the top-left, top-middle, and top-right plots in the first row, the middle-left and middle-right plots in the second row, and the bottom-left and bottom-middle plots in the third row. The bottom-right position is empty. The plots show data for different cell types and experimental conditions, as indicated by the legends within each plot: Fluo-C2DL-Huh7, Fluo-C2DL-MSC, Fluo-N2DH-GOWT1, Fluo-N2DL-HeLa, DIC-C2DH-HeLa, and PhC-C2DH-U373, PhC-C2DL-PSC. Error bars are present for most data points, indicating variability. The overall trend across most plots suggests that tracking accuracy generally improves or plateaus with an increasing number of training frames, with some variations depending on the specific dataset.

<span id="page-81-1"></span>Fig. 3. Quantitative results according to the Tracking Accuracy Measure TRA (higher is better). We trained the EmbedTrack model without data augmentation (black square) and with different real and synthetic training data mixing ratios  $\alpha$ . Here, one can observe that although *SynCellFactory* augmentation positively impacts the TRA score in all but one of the tested data sets, the correct choice of  $\alpha$  is critical for the model we benchmarked. Error bars indicate the standard deviation over three runs.

### 3.3 Quantitative Results

In all but one of the test data sets, our *SynCellFactory* data augmentation improved the tracking quality as measured by the TRA score; the results can be found in Table [1.](#page-81-0)

As shown in previous work using generative models for data augmentation [\[9](#page-83-8)[,25](#page-84-5)], the ratio between synthetic and real data  $\alpha = \frac{\text{\#syn-frames}}{\text{\#syn-frames}}$  in the training set is crucial. This is also true for the proposed methods; in Fig. [3,](#page-81-1) we show the tracking accuracy achieved using different mixing ratios  $\alpha$ . Our experiments showed two behaviors between fluorescence microscopy data sets (denominated as Fluo-\*) and all other microscopy modalities. In fluorescence datasets, TRA scores typically increased with the mixing ratio up to an optimal  $0.5 < \alpha < 0.7$ , then dropped, except in one dataset where improvement continued steadily. In Phase Contrast (PhC-\*) and Differential Interference Contrast (DIC- \*) experiments, most datasets initially showed a performance drop at low  $\alpha$ , followed by consistent improvement at high  $\alpha \sim 0.8$ , with one exhibiting an initial increase, a subsequent drop, and then improvement.

CTC Results. In addition to our standard experimental setup, we tested our strategy on the official cell tracking challenge evaluation data set. The CTC organizers evaluate the submissions on a private ground truth. We submitted the results for three data sets. Here, we trained *SynCellFactory* and the EmbedTrack model on all available training data and using the optimal mixing ratio  $\alpha$ . The results are presented in Table [2](#page-81-2) and show an improvement on all three data sets compared to those in [\[12\]](#page-83-13).

## 4 Conclusion

Despite the positive results, *SynCellFactory* is not without limitations.

The current motion module in *SynCellFactory* is simplistic, focusing on basic cell movements. Future iterations of *SynCellFactory* could benefit from a more sophisticated motion module that can accurately model a broader range of biological behaviors and interactions. Addressing this limitation would enhance the model's utility in more complex biological environments. *SynCellFactory* can sample videos of arbitrary length, but there is a noticeable drop in quality for extended sequences with more than 30 frames. Future developments could focus on maintaining consistent quality across varying video lengths.

In conclusion, *SynCellFactory* represents a significant step forward in the field of biological data augmentation. Its ability to generate realistic and diverse data sets holds great potential for advancing deep learning-based cell tracking pipelines.

Acknowledgments. This work is supported by the Deutsche Forschungsgemeinschaft (DFG, German Research Foundation) - Projektnummer 240245660 - SFB 1129 and under Germany's Excellence Strategy EXC-2181/1 - 390900948 (the Heidelberg STRUCTURES Excellence Cluster).

Code Availability. The code is publicly available at: [https://github.com/sciai-lab/](https://github.com/sciai-lab/SynCellFactory) [SynCellFactory.](https://github.com/sciai-lab/SynCellFactory)

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-83-7"></span>1. Azizi, S., Kornblith, S., Saharia, C., Norouzi, M., Fleet, D.J.: Synthetic data from diffusion models improves imagenet classification. Transactions on Machine Learning Research (2023)
- <span id="page-83-0"></span>2. Burke, R.T., Orth, J.D.: Through the looking glass: Time-lapse microscopy and longitudinal tracking of single cells to study anti-cancer therapeutics. Journal of visualized experiments: JoVE (2016)
- <span id="page-83-10"></span>3. Chen, R.J., Lu, M.Y., Chen, T.Y., Williamson, D.F.K., Mahmood, F.: Synthetic data in machine learning for medicine and healthcare. Nature Biomedical Engineering (2021)
- <span id="page-83-1"></span>4. Collins, J.L., van Knippenberg, B., Ding, K., Kofman, A.V.: Time-lapse microscopy. In: Cell Culture, chap. 3. IntechOpen (2018)
- <span id="page-83-9"></span>5. Dorjsembe, Z., Pao, H.K., Odonchimed, S., Xiao, F.: Conditional diffusion models for semantic 3d medical image synthesis. Authorea Preprints (2023)
- <span id="page-83-3"></span>6. Du, Y., Zhao, Z., Song, Y., Zhao, Y., Su, F., Gong, T., Meng, H.: Strongsort: Make deepsort great again (2023)
- <span id="page-83-11"></span>7. Fernandez, V., Pinaya, W.H.L., Borges, P., Tudosiu, P.D., Graham, M.S., Vercauteren, T., Cardoso, M.J.: Can segmentation models be trained with fully synthetically generated data? In: International Workshop on Simulation and Synthesis in Medical Imaging. pp. 79–90. Springer (2022)
- <span id="page-83-4"></span>8. Hassan, S., Mujtaba, G., Rajput, A., Fatima, N.: Multi-object tracking: a systematic literature review. Multimedia Tools and Applications (2023)
- <span id="page-83-8"></span>9. He, R., Sun, S., Yu, X., Xue, C., Zhang, W., Torr, P., Bai, S., QI, X.: IS SYNTHETIC DATA FROM GENERATIVE MODELS READY FOR IMAGE RECOGNITION? In: The Eleventh International Conference on Learning Representations (2023)
- <span id="page-83-12"></span>10. Liu, Q., Gaeta, I.M., Zhao, M., Deng, R., Jha, A., Millis, B.A., Mahadevan-Jansen, A., Tyska, M.J., Huo, Y.: Asist: annotation-free synthetic instance segmentation and tracking by adversarial simulations. Computers in biology and medicine (2021)
- <span id="page-83-2"></span>11. Loewke, K.E., Pera, R.A.R.: The Role of Time-Lapse Microscopy in Stem Cell Research and Therapy, pp. 181–191 (2011)
- <span id="page-83-13"></span>12. Löffler, K., Mikut, R.: Embedtrack-simultaneous cell segmentation and tracking through learning offsets and clustering bandwidths. IEEE Access 10, 77147–77157 (2022). 10.1109/ACCESS.2022.3192880
- <span id="page-83-6"></span>13. Maska, M., Ulman, V., Delgado-Rodriguez, P., Gomez-de Mariscal, E., Necasova, T., et al.: The cell tracking challenge: 10 years of objective benchmarking. Nature Methods (2023)
- <span id="page-83-14"></span>14. Matula, P., Maška, M., Sorokin, D.V., Matula, P., Ortiz-de Solórzano, C., Kozubek, M.: Cell tracking accuracy measurement based on comparison of acyclic oriented graphs. PLOS ONE (2015)
- <span id="page-83-5"></span>15. Meinhardt, T., Kirillov, A., Leal-Taixe, L., Feichtenhofer, C.: Trackformer: Multiobject tracking with transformers. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition (2022)

- <span id="page-84-12"></span>16. Pachitariu, M., Stringer, C.: Cellpose 2.0: how to train your own model. Nature Methods (2022)
- <span id="page-84-6"></span>17. Pinaya, W.H., Graham, M.S., Kerfoot, E., Tudosiu, P.D., Dafflon, J., Fernandez, V., Sanchez, P., Wolleb, J., da Costa, P.F., Patel, A., et al.: Generative ai for medical imaging: extending the monai framework. arXiv preprint [arXiv:2307.15208](http://arxiv.org/abs/2307.15208) (2023)
- <span id="page-84-7"></span>18. Pinaya, W.H., Tudosiu, P.D., Dafflon, J., Da Costa, P.F., Fernandez, V., Nachev, P., Ourselin, S., Cardoso, M.J.: Brain imaging generation with latent diffusion models. In: MICCAI Workshop on Deep Generative Models (2022)
- <span id="page-84-2"></span>19. Rombach, R., Blattmann, A., Lorenz, D., Esser, P., Ommer, B.: High-resolution image synthesis with latent diffusion models. 2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) (2021)
- <span id="page-84-4"></span>20. Sariyildiz, M.B., Alahari, K., Larlus, D., Kalantidis, Y.: Fake it till you make it: Learning transferable representations from synthetic imagenet clones. In: CVPR 2023–IEEE/CVF Conference on Computer Vision and Pattern Recognition (2023)
- <span id="page-84-0"></span>21. Schwartz, M.S., Moen, E., Miller, G., Dougherty, T., Borba, E., Ding, R., Graf, W., Pao, E., Valen, D.V.: Caliban: Accurate cell tracking and lineage construction in live-cell imaging experiments with deep learning. bioRxiv (2023)
- <span id="page-84-9"></span>22. Serna-Aguilera, M., Luu, K., Harris, N., Zou, M.: Neural cell video synthesis via optical-flow diffusion (2022)
- <span id="page-84-13"></span>23. Stringer, C., Wang, T., Michaelos, M., Pachitariu, M.: Cellpose: a generalist algorithm for cellular segmentation. Nature Methods (2021)
- <span id="page-84-8"></span>24. Sun, L., Chen, J., Xu, Y., Gong, M., Yu, K., Batmanghelich, K.: Hierarchical amortized gan for 3d high resolution medical image synthesis. IEEE journal of biomedical and health informatics (2022)
- <span id="page-84-5"></span>25. Trabucco, B., Doherty, K., Gurinas, M.A., Salakhutdinov, R.: Effective data augmentation with diffusion models. In: The Twelfth International Conference on Learning Representations (2024)
- <span id="page-84-11"></span>26. Ulman, V., Maška, M., Magnusson, K.E.G., Ronneberger, O., Haubold, C., Harder, N., Matula, P., Matula, P., Svoboda, D., Radojevic, M., Smal, I., Rohr, K., Jaldén, J., Blau, H.M., Dzyubachyk, O., Lelieveldt, B., Xiao, P., Li, Y., Cho, S.Y., Dufour, A.C., Olivo-Marin, J.C., Reyes-Aldasoro, C.C., Solis-Lemus, J.A., Bensch, R., Brox, T., Stegmaier, J., Mikut, R., Wolf, S., Hamprecht, F.A., Esteves, T., Quelhas, P., Demirel, O., Malmström, L., Jug, F., Tomancak, P., Meijering, E., Muñoz-Barrutia, A., Kozubek, M., Ortiz-de Solorzano, C.: An objective comparison of cell-tracking algorithms. Nature Methods (2017)
- <span id="page-84-1"></span>27. Zargari, A., Lodewijk, G.A., Mashhadi, N., Cook, N., Neudorf, C.W., Araghbidikashani, K., Hays, R., Kozuki, S., Rubio, S., Hrabeta-Robinson, E., Brooks, A., Hinck, L., Shariati, S.A.: Deepsea is an efficient deep-learning model for singlecell segmentation and tracking in time-lapse microscopy. Cell Reports Methods (2023)
- <span id="page-84-3"></span>28. Zhang, L., Rao, A., Agrawala, M.: Adding conditional control to text-to-image diffusion models. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (2023)
- <span id="page-84-10"></span>29. Zhu, J.Y., Park, T., Isola, P., Efros, A.A.: Unpaired image-to-image translation using cycle-consistent adversarial networks. In: Proceedings of the IEEE international conference on computer vision (2017)

Image /page/85/Picture/0 description: A square button with a light gray background and a darker gray border. At the top of the button, there is a circular icon with a bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a darker gray color.

# TAPoseNet: Teeth Alignment Based on Pose Estimation via Multi-scale Graph Convolutional Network

Qingxin Deng<sup>1</sup>, Xunyu Yang<sup>1</sup>, Minghan Huang<sup>1</sup>, Landu Jiang<sup>2</sup>, and Dian Zhang<sup>1( $\boxtimes$ )</sup>

<sup>1</sup> College of Computer Science and Software Engineering, Shenzhen University, Nanhai Avenue, Shenzhen 3688, China

<EMAIL>

<sup>2</sup> The Hongkong University of Science and Technology (Guangzhou), Duxue Road 1, Guangzhou, China

<EMAIL>

Abstract. Teeth alignment plays an important role in orthodontic treatment. Automating the prediction of teeth alignment target can significantly aid both doctors and patients. Traditional methods often utilize rule-based approach or deep learning method to generate teeth alignment target. However, they usually require extra manual design by doctors, or produce deformed teeth shapes, even fail to address severe misalignment cases. To tackle the problem, we introduce a pose prediction model which can better describe the space representation of the tooth. We also consider geometric information to fully extracted features of teeth. In the meanwhile, we build a multi-scale Graph Convolutional Network(GCN) to characterize the teeth relationships from different levels (global, local, intersection). Finally the target pose of each tooth can be predicted and so the teeth movement from the initial pose to the target pose can be obtained without deforming teeth shapes. Our method has been validated in clinical orthodontic treatment cases and shows promising results both qualitatively and quantitatively.

Keywords: Deep learning  $\cdot$  3D point cloud  $\cdot$  Orthodontic treatment

### 1 Introduction

Teeth alignment is a critical concern in dentistry, satisfying the human requirement to become more beautiful and healthy [\[2\]](#page-93-0). As a result, the demand for orthodontic treatment is rising dramatically. In orthodontic treatment, the determination of the teeth alignment target is crucial, as it directly determines the subsequent treatment plans and the design of the orthodontic appliance. While computer-aided modeling techniques such as intra oral scans have revolutionized the field of orthodontics and provided increased patient comfort, they do require substantial time commitment from doctors and orthodontists to determine teeth alignment target. Therefore, it is essential to develop a fully automated system to determine an optimal teeth arrangement target. A system like this would not only alleviate the burden of manual operations for dentists but would also enhance the communication effectiveness with patients, by allowing patients to envisage the results of their future dental arrangements.

Existing methods to address this challenge can be roughly divided into three categories: 1) Automatic teeth alignment based on expertise rules. These methods usually require extensive manual intervention from doctors, and typically require prior information such as teeth landmarks. Cheng et al. [\[5](#page-93-1)] propose an accurate teeth arrangement system with complete teeth model. But, this system requires manual intervention from doctors to set the alignment target of incisors. Deng et al. [\[7](#page-93-2)] propose an automatic approach for maxilla and mandible alignment. However this alignment requires many pre-defined teeth landmarks. 2) Automatic teeth alignment based on generative model. These methods are mainly used for 2D images, with some designed for 3D models that don't need any pre-segmentation of teeth. However, this approach might lead to severe distortions in the 3D models of teeth. Chen et al. [\[4](#page-93-3)] present a method to predict the visual outcome of orthodontic treatment in a portrait image via latent style code manipulation. Yang et al. [\[11](#page-94-0)] propose a system which takes a frontal face image of a patient along with a corresponding 3D teeth model as input and generates a facial image with aligned teeth. Zhang et al. [\[17](#page-94-1)] present the first parametric 3D morphable dental model for both teeth and gum which can be used to smoothly interpolate between pre-orthodontic teeth and post-orthodontic teeth. However, the size and shape of the teeth might be changed during interpolation. 3) Automatic teeth alignment based on regressing the transformaion matrices for each tooth. PSTN [\[10](#page-94-2)] proposed by Li et al. inspired by [\[9](#page-93-4)] uses PointNet [\[12\]](#page-94-3) and PointNet $++$  [\[13](#page-94-4)] for global and local features extraction and then directly regresses the transformation matrices. TANet [\[16\]](#page-94-5) proposed by Wei et al. uses graph-based feature propagation module to update features extracted by Point-Net [\[12](#page-94-3)] to solve the 6-DOF pose prediction problem of each tooth. Wang et al. [\[14](#page-94-6)] uses anatomical landmark constraints to improve tooth alignment results instead of directly regressing tooth motion. However, these methods require pairs of pre-orthodontic and post-orthodontic teeth models to train the model and it is difficult to handle severe misalignment cases.

In order to address aforementioned challenges, we propose a system named TAPoseNet, which can fully automate the prediction of post-orthodontic teeth alignment target without any deformation of teeth. TAPoseNet includes a Teeth Pose Estimation module based on DGCNN [\[15](#page-94-7)] to explicitly estimate the pose of each tooth in dental arch, paired with a Geometric Information Extraction Module that extracts each tooth's geometric information. Afterwards the extracted features are fed into Teeth alignment target prediction module to predict the post-orthodontic pose of each tooth. Finally, transformation matrices from preorthodontic poses to post-orthodontic poses are used for transitioning the preorthodontic teeth to the predicted post-orthodontic arrangement.

The contributions of our work are as follows: 1) We present a method to automatically predict the post-orthodontic teeth alignment target based on initial teeth pose estimation and target teeth pose prediction, without deforming teeth shapes. 2) To the best of our knowledge, we introduce the first deep learning based method to estimate the pose of teeth, which can better describe the space representation of the tooth and contribute to the prediction of post-orthodontic teeth arrangement target with clinical interpretability. 3) We build a multi-scale Graph Convolutional Network (GCN) to characterize the spatial relationships of teeth in multi-scale from different levels (global, local, intersection).

## 2 Method

TAPoseNet is composed of two major components: 1) Teeth features extraction module and 2) Teeth alignment target prediction module (Fig. [1\)](#page-87-0).

Image /page/87/Figure/4 description: This is a flowchart illustrating a deep learning model for dental alignment. The model takes an input of teeth, downsamples it to a point cloud, and then processes it through a 'Teeth features extraction module'. This module includes sub-modules for 'Teeth Pose Estimation' using DGCNN, 'Geometric Information Extraction' with an encoder-decoder structure, and 'Teeth Centers' extraction. The extracted features are then fed into a 'Teeth alignment target prediction module' which utilizes Graph Convolutional Networks (GCNs) and an MLP. The model also incorporates 'Initial pose' and 'Target pose' information, along with 'Transform matrices'. The output of the model is an aligned set of teeth.

<span id="page-87-0"></span>Fig. 1. The overall network architecture of our method.

The central idea is to approach the prediction of teeth alignment target as a problem of predicting the target pose of each tooth given the initial pose of each tooth. To accurately estimate the initial pose of teeth, we propose a teeth pose estimation module to regress the local coordinates of teeth. For the geometric information of teeth, we model it as a latent code that can be effectively extracted from the input teeth point cloud. Finally, 3 GCNs with different adjacency matrices are proposed to aggregating the teeth features in multi-scale.

TAPoseNet operates as follows. The input of our network is a segmented and classified teeth crown point cloud  $P = \{P_v \subseteq \mathbb{R}^{N_t \times 3} \mid v \in V\}$  down-sampled<br>from the are orthodontic tooth model  $T = \{T_v \mid v \in V\}$  where y denotes the from the pre-orthodontic teeth model  $T = \{T_v | v \in V\}$ , where V denotes the set of tooth labels which are assigned according to FDI two digit notation for permanent teeth and  $N_t$  is the number of sampled points of each tooth. The teeth features extraction module mainly extracts the geometric information *geocode* and the pre-orthodontic pose  $Pose<sub>pre</sub>$  of each tooth. The post-orthodontic teeth alignment target prediction module predicts the post-orthodontic pose of each tooth *P osepost* by aggregating the extracted tooth features in multi-scale. Applying the transformation matrices from initial poses to predicted poses to the pre-orthodontic teeth model, we can obtain the post-orthodontic teeth model  $T' = \{T'_v \mid v \in V\}.$ 

### 2.1 Teeth Features Extraction Module

The features of a tooth consists of two parts: the pose of the tooth and the geometric information of the tooth. The pose of a tooth is a representation of its position and posture in the local coordinate system *L* relative to the world coordinate system *W*, described by rotation and translation in three-dimensional space. Assuming that there is a point p on a specific tooth, the coordinate of p in *L* is  $p_L$ , the coordinate of *p* in *W* is  $p_W$ , the rotation matrix  $\mathbf{R} \in SO(3)$  is for the rotation and the vector *C* is for the translation, the transformation from  $p_W$  to  $p_L$  can be expressed as:

$$
p_L = R \cdot (p_W - C) \tag{1}
$$

Consequently, the pose of the tooth is represented by the quaternion rotation *R*−<sup>1</sup> which is a four-dimensional vector and the centroid of the tooth *C* which is a three-dimensional vector.

To obtain the local coordinate system of a specific tooth, we propose a 3 head architectures of DGCNN [\[15](#page-94-7)] for predicting the *x*, *y*, *z* coordinate of the tooth in the local coordinate system (Fig. [1\)](#page-87-0). For the stability of training, we discretize the local coordinates to transform the coordinate regression problem into a point cloud classification problem. Specifically, the input of this module is a point cloud of a segmented tooth  $P_v \subseteq \mathbb{R}^{N_t} \times 3$ , the output of a head is of size  $N_t \times Num_{class}$ , where  $Num_{class}$  is the number of categories in the *x*  $(y, z)$ direction.

In the process of determining the target position of orthodontic treatment, the abstract pose of teeth cannot be solely considered. The shape of different teeth, surface texture, bite groove and other information have a significant impact on the teeth alignment target determination. In order to effectively extract the geometric shape information of teeth surfaces, we trained a deep Autoencoder (AE) to encode the geometric shape features of teeth [\[1\]](#page-93-5). Specifically, for the input tooth point cloud  $P_v$ , the encoder based on PointNet  $[12]$ outputs a latent feature  $\text{geocode} \subseteq R^{N_{latent}}$ . The decoder based on MLP then outputs the reconstructed point cloud  $P'_v$  given the latent feature as input. Using  $\Gamma$ Chamfer distance [\(2\)](#page-88-0) as the loss function for training,

<span id="page-88-0"></span>Loss 
$$
(P_v, P'_v) = \sum_{x \in P_v} \min_{y \in P'_v} ||x - y||_2^2 + \sum_{y \in P'_v} \min_{x \in P_v} ||x - y||_2^2
$$
 (2)

the encoder can effectively extract the geometric shape information of the tooth. At the inference stage, we only retain the encoder part for extracting the geometric information of the tooth point cloud.

### 2.2 Post-orthodontic Teeth Alignment Target Prediction Module

We predict the post-orthodontic pose *P osepost* of each tooth using a deep model based on the pre-orthodontic pose *P osepre* and geometric information *geocode* extracted from the teeth features extraction module. Specifically, we concatenate the initial pose vector containing a 4 dimensional quaternion and a 3 dimensional centroid coordinate with geometric feature vector which is a 100 dimensional latent vector as input  $X = (Pose_{pre}, geocode)$  for each tooth. Therefore the input embedding is in shape of  $N \times (7+100)$ , *N* being the total number of teeth of a patient (usually 28). This embedding is then fed into Teeth alignment target prediction module composed of GCN-based encoder and MLP-based decoder. The encoder employs 3 GCNs with different adjacency matrices constructed from different spatial dependencies between teeth in the dental arch *<sup>G</sup>global*, *<sup>G</sup>local* and *<sup>G</sup>intersection*. Specifically, by examining each tooth as a node within the dental arch, three types of adjacency matrices are formulated: 1) Global adjacency matrix. This matrix treats the dental arch as a fully interconnected graph so that every tooth connects with all others. This allows the network to extract and interpret the holistic arch shape information. 2) Local adjacency matrix. In this matrix, each tooth is linked not only with the opposing tooth but also with the adjacent ones, as well as their opposite adjacent teeth. This linkage allows the network to discern the localized crowded occlusion within the dental arch. 3) Intersection adjacency matrix, every tooth is connected to both the opposing tooth and the adjacent ones. This structure primarily serves to prevent potential collisions between teeth. The outputs of the GCNs are concatenated and then fed into the MLP-based decoder *D*. The output of the decoder is in shape of  $N \times 7$ , which indicates the predicted post-orthodontic pose of each tooth of a patient.

$$
Pose_{post} = D(G_{global}(X) \oplus G_{local}(X) \oplus G_{intersection}(X))
$$
 (3)

To train the network, we adopt a loss function to compute the difference between  $Pose_{post}$  and the ground truth pose  $Pose_{gt}$ .

$$
Loss = Loss_{rotation} + Loss_{translation}
$$
 (4)

Specifically, *Lossrotation* measures the cosine similarity between prediction quaternion posture and ground truth quaternion posture, *Losstranslation* calculates the distance between prediction position of tooth and ground truth position of tooth. Given the poses before and after orthodontic treatment, we can calculate the transformation matrices  $(Trans_{pose_{pre}\rightarrow pose_{post}})$ *v* for each tooth. Therefore the post-orthodontic teeth model can be obtained by applying the transformation matrices to each tooth of the patient.

$$
T' = \left\{ (Trans_{pose_{pre} \rightarrow pose_{post}})_v \times T_v | v \in \mathcal{V}, T_v \subseteq T \right\}
$$
 (5)

### 3 Experiments and Results

#### 3.1 Dataset

Our experiment was conducted on a dataset of clinical orthodontic cases sourced from a dental hospital. This dataset comprises post-orthodontic oral scan data from 50 patients, along with pre- and post-orthodontic treatment oral scan data from 25 pairs. Given the potential discrepancies in the orientations of oral scan data due to the varying features of oral scanning devices, we employed the ICP registration method [\[6](#page-93-6)] to standardize the orientation across all dataset. Then we use the harmonic field method  $[3,18]$  $[3,18]$  $[3,18]$  to segment each tooth from the oral scan and remove the gingival part, leaving only the crown part. Finally, we label each tooth crown using the FDI digit. For network training, we use post orthodontic oral scan data from 50 patients. During training, we utilized data augmentation methods (e.g., randomly rotate or translate) in each epoch to reverse-generate diverse initial poses (pre-orthodontic) as input for each case in the training set with ideal target poses (post-orthodontic), reflecting different orthodontic symptoms. For validation and testing we utilize 25 pairs of oral scan data that were gathered before and after orthodontic treatment (10 for validation and 15 for testing). In the inference stage, initial pose of each tooth from the input is estimated by the pre-trained Teeth Pose Estimation module, so that we can predict the target pose.

#### 3.2 Implementation and Evaluation Methods

The implementation detail of TAPoseNet is described below. We randomly down sample 1024 points from each tooth crown of a patient. During the teeth pose estimation, The output dimension of each DGCNN network is 32. In teeth alignment target prediction module, the MLP-based decoder includes several shared FC layers, a squeeze-and-excitation(SE) block [\[8](#page-93-8)] with reduction ratio 4, and skip connection.

We trained the model for 4000 epochs with a batch size of 2 using Adam opimizer. The learning rate was initialized as 1e-4 and use the cosine learning rate scheduler with the minimun learning rate set to be 1e-6. The models were trained on an NVIDIA RTX-2080 Ti.

The prediction accuracy of TAPoseNet was evaluated quantitatively and qualitatively by comparing it to representative methods PSTN [\[10\]](#page-94-2), TAligNet [\[11](#page-94-0)] and TANet [\[16](#page-94-5)]. Given that in clinical practice, the pre- and post-orthodontic oral scan teeth models typically do not share the same coordinate system and often display inconsistencies in the model's vertex counts, we adopted the Chamfer Distance(CD) as one of our evaluation metrics. This metric evaluates the distance between the predicted post-orthodontic teeth model and the ground truth post-orthodontic teeth model post-rigid registration implemented through the Iterative Closest Point (ICP) algorithm [\[6\]](#page-93-6). Additionally, the measurement of matrix similarity can act as an accuracy index since the 3D transformation of teeth are described by spatial transformation matrix. Therefore, we randomly disarrange the post-orthodontic teeth and calculate the commonly used cosine similarity accuracy (CSA) to measure the difference between the generated transformation matrix and its ground truth.

### 3.3 Results

The results of quantitative evaluation of TAPoseNet mainly focus on prediction accuracy of teeth alignment target. We compared our methd with representation methods, TANet [\[16](#page-94-5)], PSTN [\[10](#page-94-2)] and TAligNet [\[11](#page-94-0)] for comparison. Significantly, TANet [\[16\]](#page-94-5) and PSTN [\[10](#page-94-2)] are fully automatic method without any prior information, while TAligNet [\[11\]](#page-94-0) and the Post-orthodontic teeth alignment target prediction module of our TAPoseNet need the input of the initial pose information of the teeth. To ensure the fairness, we use the teeth pose estimation module of our TAPoseNet to estimate the pose of teeth automatically. The results are shown in Table [1.](#page-91-0) As shown in the table, our network achieves the lowest Chamfer Distance and the highest Cosine Similarity Accuracy.

<span id="page-91-0"></span>Table 1. Result comparison of different methods.

| Methods    |           | Chamfer Distance (mm) $\downarrow$ | CSA% $\uparrow$ |
|------------|-----------|------------------------------------|-----------------|
| non-pose   | PSTN      | 0.68667                            | 84.52           |
|            | TANet     | 0.68533                            | 84.54           |
| pose-based | TAligNet  | 0.62281                            | 85.94           |
|            | TAPoseNet | <b>0.60457</b>                     | <b>86.77</b>    |

<span id="page-91-1"></span>Image /page/91/Picture/6 description: This image displays a grid of dental models, comparing different methods for aligning teeth. The grid is organized into four rows labeled (a), (b), (c), and (d), and five columns representing different processing stages: Input, TANet, PSTN, TAlignNet, and Ground truth. Rows (a) and (d) show frontal views of upper teeth, while rows (b) and (c) show occlusal (top-down) views of the entire dental arch. Red ellipses are used in several images to highlight specific areas of teeth that are being compared or adjusted by the different methods. The 'Ground truth' column shows the final, presumably correct, alignment.

Fig. 2. Visualization result of comparison experiments with other methods. (a) is the front view of the whole dental model of the first patient, (b) is mandible of the first patient (c) is maxilla of the first patient. (d) is the front view of the second patient.

Figure [2](#page-91-1) presents the results of different methods tested on pre-orthodontic teeth models. Taking the patient with severe misaligned teeth as an example(the first three rows). From the front view $(a)$ , we can see that TANet and PSTN which directly regress the motion of each tooth cannot tackle the severe misalignment. The positions and postures of some teeth are obviously still misaligned. TAligNet based on pose prediction without multi-scale feature aggregation performs better in this case, but the relationship between maxilla and mandible is unreasonable. From the mandible(b) and maxilla(c), we can see that TAPoseNet provides the most optimal results, particularly when managing the relationships between teeth. Taking the patient with mild underbite problem as an example(the last row), we focus on: 1) The distance on the vertical direction where the upper row of teeth covers the lower row of teeth; 2) Whether the midline of the teeth is aligned or not. We can see that TAPoseNet performs best in these two aspects.

From the quantitative evaluation and qualitative evaluation, we can learn that the methods using pose information perform better than methods that directly regress the motion of teeth. Therefore, the accuracy of pose estimation is essential. We also conduct quantitative evaluation (Table [2\)](#page-92-0) using the mean point-wise distance and maximum point-wise distance as metric on our teeth pose estimation module. We use the Oriented Bounding Box (OBB) and the Axis Aligned Bounding Box (AABB) as visualization for qualitative evaluation (Fig. [3\(](#page-92-1)a)). In practice, the error of the estimated pose is acceptable (Fig. 3(b));

Image /page/92/Figure/3 description: The image displays two figures, labeled (a) and (b). Figure (a) shows two sets of dental models, labeled 'case 1' and 'case 2'. Each set consists of two arch-shaped dental models, with individual teeth colored in shades of blue and yellow, suggesting some form of data visualization or analysis applied to the teeth. Figure (b) presents a 3D scatter plot within a wireframe cube, showing two distinct clusters of points, one colored red and the other green, possibly representing different data sets or states.

<span id="page-92-1"></span>Fig. 3. The visualization of teeth pose estimation. (a) is the axis aligned bounding box and oriented bounding box based on estimated pose of the dental model. (b) is a comparison between the predicted teeth pose (green) and the ground truth teeth pose (red) whose mean point-wise distance is 1.52326. (Color figure online)

<span id="page-92-0"></span>Table 2. The mean point-wise distance and maximum point-wise distance between the predicted teeth pose and ground truth teeth pose.

| teeth categories | Mean point-wise distance (mm) | Maximum point-wise distance (mm) |
|------------------|-------------------------------|----------------------------------|
| Incisor          | 0.39352                       | 0.52463                          |
| Canine           | 1.00160                       | 1.52326                          |
| Premolar         | 1.14999                       | 1.43654                          |
| Molar            | 0.52546                       | 0.82379                          |

## 4 Discussions and Conclusions

We proposed a deep learning-based framework, TAPoseNet to predict teeth alignment target. The quantitative evaluation and qualitative evaluation demonstrate the effectiveness of TAPoseNet in orthodontic treatment planning. An integral component of TAPoseNet is the teeth pose estimation module, which automatically estimates teeth poses, significantly contributing to various facets of orthodontic treatment planning. For future work, we need to consider the occlusion of the upper and lower jaw. Additionally, missing teeth or wisdom teeth cannot be handled in our work, which will be handled in future work.

Acknowledgments. This work was supported in part by Stable Support Project of Shenzhen (Project No. 20231122145548001), the JCYJ under Grant 20220531 091407016 and the HKUST(GZ)-ROP2023056.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-93-5"></span>1. Achlioptas, P., Diamanti, O., Mitliagkas, I., Guibas, L.: Learning representations and generative models for 3d point clouds. In: International conference on machine learning. pp. 40–49. PMLR (2018)
- <span id="page-93-0"></span>2. Andrews, L.F.: The six keys to normal occlusion. Am J orthod  $62(3)$ ,  $296-309$ (1972)
- <span id="page-93-7"></span>3. Au, O.K.C., Zheng, Y., Chen, M., Xu, P., Tai, C.L.: Mesh segmentation with concavity-aware fields. IEEE Transactions on Visualization and Computer Graphics 18(7), 1125–1134 (2011)
- <span id="page-93-3"></span>4. Chen, B., Fu, H., Zhou, K., Zheng, Y.: Orthoaligner: image-based teeth alignment prediction via latent style manipulation. IEEE Transactions on Visualization and Computer Graphics (2022)
- <span id="page-93-1"></span>5. Cheng, C., Cheng, X., Dai, N., Liu, Y., Fan, Q., Hou, Y., Jiang, X.: Personalized orthodontic accurate tooth arrangement system with complete teeth model. Journal of medical systems 39, 1–12 (2015)
- <span id="page-93-6"></span>6. Chetverikov, D., Svirko, D., Stepanov, D., Krsek, P.: The trimmed iterative closest point algorithm. In: 2002 International Conference on Pattern Recognition. vol. 3, pp. 545–548. IEEE (2002)
- <span id="page-93-2"></span>7. Deng, H., Yuan, P., Wong, S., Gateno, J., Garrett, F.A., Ellis, R.K., English, J.D., Jacob, H.B., Kim, D., Xia, J.J.: An automatic approach to reestablish final dental occlusion for 1-piece maxillary orthognathic surgery. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2019: 22nd International Conference, Shenzhen, China, October 13–17, 2019, Proceedings, Part V 22. pp. 345–353. Springer (2019)
- <span id="page-93-8"></span>8. Hu, J., Shen, L., Sun, G.: Squeeze-and-excitation networks. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 7132–7141 (2018)
- <span id="page-93-4"></span>9. Jaderberg, M., Simonyan, K., Zisserman, A., et al.: Spatial transformer networks. Advances in neural information processing systems 28 (2015)

- <span id="page-94-2"></span>10. Li, X., Bi, L., Kim, J., Li, T., Li, P., Tian, Y., Sheng, B., Feng, D.: Malocclusion treatment planning via pointnet based spatial transformation network. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 105–114. Springer (2020)
- <span id="page-94-0"></span>11. Lingchen, Y., Zefeng, S., Yiqian, W., Xiang, L., Kun, Z., Hongbo, F., Zheng, Y.: iorthopredictor: model-guided deep prediction of teeth alignment. ACM Transactions on Graphics 39(6), 216 (2020)
- <span id="page-94-3"></span>12. Qi, C.R., Su, H., Mo, K., Guibas, L.J.: Pointnet: Deep learning on point sets for 3d classification and segmentation. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 652–660 (2017)
- <span id="page-94-4"></span>13. Qi, C.R., Yi, L., Su, H., Guibas, L.J.: Pointnet + +: Deep hierarchical feature learning on point sets in a metric space. Advances in neural information processing systems 30 (2017)
- <span id="page-94-6"></span>14. Wang, C., Wei, G., Wei, G., Wang, W., Zhou, Y.: Tooth alignment network based on landmark constraints and hierarchical graph structure. IEEE Transactions on Visualization and Computer Graphics (2022)
- <span id="page-94-7"></span>15. Wang, Y., Sun, Y., Liu, Z., Sarma, S.E., Bronstein, M.M., Solomon, J.M.: Dynamic graph cnn for learning on point clouds. ACM Transactions on Graphics (tog)  $38(5)$ , 1–12 (2019)
- <span id="page-94-5"></span>16. Wei, G., Cui, Z., Liu, Y., Chen, N., Chen, R., Li, G., Wang, W.: Tanet: towards fully automatic tooth arrangement. In: Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part XV 16. pp. 481– 497. Springer (2020)
- <span id="page-94-1"></span>17. Zhang, C., Elgharib, M., Fox, G., Gu, M., Theobalt, C., Wang, W.: An implicit parametric morphable dental model. ACM Transactions on Graphics  $(TOG)$  41(6), 1–13 (2022)
- <span id="page-94-8"></span>18. Zou, B.j., Liu, S.j., Liao, S.h., Ding, X., Liang, Y.: Interactive tooth partition of dental mesh base on tooth-target harmonic field. Computers in biology and medicine 56, 132–144 (2015)

Image /page/95/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a segment missing, and a gray flag shape is inside the circle, pointing towards the missing segment.

# TE-SSL: Time and Event-Aware Self Supervised Learning for Alzheimer's Disease Progression Analysis

Jacob Thrasher<br/> $^{1(\boxtimes)}$ , Alina Devkota $^{1}$ , Ahmad P. Tafti<br/>², Binod Bhattarai $^{3},$ Prashnna Gyawali<sup>1</sup>, and for the Alzheimer's Disease Neuroimaging Initiative

> <sup>1</sup> West Virginia University, Morgantown, USA <EMAIL> <sup>2</sup> University of Pittsburgh, Pittsburgh, USA

<sup>3</sup> University of Aberdeen, Aberdeen, UK

Abstract. Alzheimer's Disease (AD) represents one of the most pressing challenges in the field of neurodegenerative disorders, with its progression analysis being crucial for understanding disease dynamics and developing targeted interventions. Recent advancements in deep learning and various representation learning strategies, including self-supervised learning (SSL), have shown significant promise in enhancing medical image analysis, providing innovative ways to extract meaningful patterns from complex data. Notably, the computer vision literature has demonstrated that incorporating supervisory signals into SSL can further augment model performance by guiding the learning process with additional relevant information. However, the application of such supervisory signals in the context of disease progression analysis remains largely unexplored. This gap is particularly pronounced given the inherent challenges of incorporating both event and time-to-event information into the learning paradigm. Addressing this, we propose a novel framework, Time and Event-aware SSL (TE-SSL), which integrates timeto-event and event and data as supervisory signals to refine the learning process. Our comparative analysis with existing SSL-based methods in the downstream task of survival analysis shows superior performance across standard metrics. The full code can be found here: [https://github.com/jacob-thrasher/TE-SSL.](https://github.com/jacob-thrasher/TE-SSL)

Keywords: Alzheimer's · Survival Analysis · Self-supervised learning

Data used in preparation of this article were obtained from the Alzheimer's Disease Neuroimaging Initiative (ADNI) database (adni.loni.usc.edu). As such, the investigators within the ADNI contributed to the design and implementation of ADNI and/or provided data but did not participate in analysis or writing of this report. A complete listing of ADNI investigators can be found [here.](http://adni.loni.usc.edu/wp-content/uploads/how_to_apply/ADNI_Acknowledgement_List.pdf)

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_31.](https://doi.org/10.1007/978-3-031-72390-2_31)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 324–333, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_31)\_31

#### 1 Introduction

Advancement in deep-learning-based medical image analysis have shown remarkable promise in revolutionizing the study of Alzeimer's Dementia (AD) through medical imaging  $[2,6,19,20]$  $[2,6,19,20]$  $[2,6,19,20]$  $[2,6,19,20]$  $[2,6,19,20]$ , potentially aiding in the management and treatments of the estimated 6.7 million people aged  $65+$  who live with AD in the United States [\[3](#page-103-2)]. For instance, Chang *et al.* (2023) [\[6](#page-103-1)] developed MRI-based deep learning framework for differentiating between Alzheimer's disease, temporal lobe epilepsy and healthy controls. These approaches have been shown to be successful in identifying subtle patterns in brain images that may be challenging for human detection. While significant strides have been made in utilizing computer-assisted tools for detecting Alzheimer's Disease, the focus on progression analysis, such as time-to-event prediction, has been comparatively less prominent. However, the ability to accurately predict the trajectory of Alzheimer's over time is crucial for early diagnosis and can dramatically transform the clinical workflow. Tools capable of forecasting disease progression offer invaluable insights for personalized patient care, enabling timely interventions and better management of the disease's impact on patients' lives.

Representation learning within deep learning has demonstrated remarkable potential in extracting meaningful features from data, significantly enhancing the performance of downstream tasks  $[4,5,15]$  $[4,5,15]$  $[4,5,15]$  $[4,5,15]$ . This approach has been particularly transformative in medical imaging, where it has contributed to breakthroughs in disease detection and diagnosis [\[12](#page-104-3)]. Self-supervised learning (SSL) and its variants stand out as key strategies in learning these useful representations without relying on labeled data [\[7](#page-103-5)[,15](#page-104-2)[,22](#page-104-4)]. When labels are available, incorporating them into SSL frameworks can further refine model performance by effectively clustering data points from the same class closer together while distancing those from different classes [\[11\]](#page-104-5). Despite its success across domains, including medical image analysis [\[9,](#page-104-6)[17,](#page-104-7)[21](#page-104-8)], the application of SSL in analyzing disease progression, such as in Alzheimer's disease, remains under explored. Furthermore, there appears to be a significant gap in the literature, as no existing studies have leveraged the supervisory signals provided by available labels, such as event indicators, to enhance the capabilities of self-supervised learning models specifically for progression analysis in Alzheimer's diseases. Our research aims to bridge this gap by integrating these supervisory signals into our self-supervised learning framework, thereby potentially improving downstream tasks in progression analysis.

Toward this goal, we initially explored the potential of self-supervised learning (SSL) in the progression analysis of Alzheimer's disease. As anticipated, the initial results showcased improved outcomes that further encouraged us to incorporate supervisory signals to enhance representation learning for AD progression analysis. We integrated the event label to better guide the representation learning specifically tailored for progression analysis. Recognizing the significance of time-to-event information, we developed a novel self-supervised learning framework, Time and Event-aware SSL (TE-SSL), which incorporates both event and time-to-event labels as additional supervisory signals. Through evaluation on

Image /page/97/Figure/1 description: The image displays three diagrams illustrating different self-supervised learning frameworks. The first diagram, labeled 'Self supervised framework', shows a green rectangle with 'SSL' inside, with arrows pointing up to three circles labeled 'zi', 'zj', and 'zk'. An arrow also points from a stack of MRI images up to the 'SSL' rectangle. The second diagram, 'Event-aware SSL', depicts a light blue circle containing three circles labeled 'zi', 'zj', and 'zk', with bidirectional arrows connecting them, indicating observed events. Below this, a peach-colored circle contains a dark circle labeled 'z0', representing censored events. The third diagram, 'Time and Event-aware SSL', also features a light blue circle with 'zi', 'zj', and 'zk' inside, showing observed events. Arrows between 'zi' and 'zk' are labeled with time differences 'Δi,k'. Arrows between 'zi' and 'zj' are labeled 'Δi,j', and between 'zj' and 'zk' are labeled 'Δj,k'. Similar to the second diagram, a peach-colored circle with 'z0' inside represents censored events.

<span id="page-97-1"></span>Fig. 1. Schematic diagram of the proposed time- and event-aware SSL, where  $\Delta_{*,*}$ represents the time difference between two data elements z<sup>∗</sup>

the ADNI dataset, TE-SSL demonstrated an improvement in the downstream performance of time-to-event prediction. Overall, our contributions are:

- 1. The use of supervisory signals in the form of event occurrence for progression analysis, offering a novel approach to understanding disease dynamics.
- 2. A novel framework, TE-SSL, that uses time and event labels for SSL training.
- 3. Demonstrated improved downstream performance for time-to-event prediction across different metrics, showcasing the practical efficacy of our approach in enhancing the predictive capabilities for Alzheimer's disease progression.

## 2 Methods

We consider a set of labeled training examples  $\mathcal X$  with the corresponding labels  $\mathcal Y$ . Since, we are interested in modeling disease progression, we consider a setup of survival analysis, where the labels  $\mathcal Y$  contains both time and event information. Specifically, for each instance i,  $\mathcal{Y}_i = (T_i, \delta_i)$ , where  $T_i$  denotes the time to event or censoring and  $\delta_i$  is the event indicator, with  $\delta_i = 1$  if the event (disease progression) occurred, and  $\delta_i = 0$  if the data is censored. Here, censoring refers to instances where the event *has not yet occurred.* Importantly, this does not mean the event will never occur, only that it was not observed during the study. The goal of survival analysis is to model the survival function  $S(t) = P(T > t)$ , which estimates the probability of an event not occurring by time t. Building upon this setup, we first learn appropriate representations using our proposed selfsupervised learning approach, TE-SSL (Sect. [2.1\)](#page-97-0), which is a contrastive learning paradigm that utilizes the additional time labels afforded by a survival analysis to strengthen the pull of elements at similar stages of development to improve feature extraction. Figure [1](#page-97-1) provides an illustration of our proposed SSL framework. We then finetune the network with a task-specific objective function to predict survival outcomes at individual time points (Sect. [2.2\)](#page-99-0).

<span id="page-97-0"></span>

### 2.1 TE-SSL: Time and Event-Aware Self supervised Learning

We provide a background of contrastive self-supervised learning, a learning paradigm designed to use unlabeled data to learn representations by performing some pretext task. We then provide details for enhancing the SSL learning paradigm by introducing supervisory signals, before finally laying out our proposed TE-SSL which leverage both event and time-to-event labels.

Self-supervised Learning. Self-supervised learning (SSL) is an unsupervised method for learning representations through pretext tasks, without labeled data. Formally, given training examples  $\mathcal{X}$ , they are transformed into a modified version  $\tilde{\mathcal{X}}$  using a set of transformations T to create multi-viewed examples. Transformations  $\mathcal T$  include a set of augmentation functions that do not alter the intrinsic information contained within the data, thus creating a separate view of the original data. SSL frameworks are trained with these multi-viewed batches. For each index  $i \in I \equiv 1, 2, ..., 2N$  in such a multi-viewed batch, let j represent the corresponding index of the transformed pair to sample  $i$ . In such a setup, a loss function for the self-supervised learning objective can be represented as:

<span id="page-98-0"></span>
$$
\mathcal{L}_{\text{SSL}} = -\sum_{i \in I} \log \frac{\exp (z_i \cdot z_j/\tau)}{\sum_{a \in A(i)} \exp (z_i \cdot z_a/\tau)}
$$
(1)

where  $z_*$  is the corresponding projection of the input **x**,  $\tau$  is a temperature parameter, and  $A(i) \equiv I - \{i\}$ . Since augmented images are semantically identical to one another, this method provides anchor points for the model to "pull" together two augmented views while "pushing" other samples in a batch.

Supervisory Signal for SSL Training. The standard SSL setup described above cannot maximize similarity between samples of the same class (e.g., disease category or event vs. censored) in a batch due to the absence of a supervisory signal. While it may seem that standard SSL could eventually differentiate such attributes over time, recent work has shown that incorporating supervisory signals significantly enhances the standard SSL setup [\[11\]](#page-104-5).

To incorporate supervisory signals using available label information (in our case, event labels), the SSL objective (in Eq. [1\)](#page-98-0) can be generalized as follows:

$$
\mathcal{L}_{\text{E-SSL}} = \sum_{i \in I} \frac{-1}{|P(i)|} \sum_{p \in P(i)} \log \frac{\exp(z_i \cdot z_p/\tau)}{\sum_{a \in A(i)} \exp(z_i \cdot z_a)/\tau}
$$
(2)

where  $P(i) \equiv \{p \in A(i)|\tilde{y}_p = \tilde{y}_i\}$  represents the set of indices for all positive samples in the multi-viewed batch that are distinct from i, and  $|P(i)|$  is the cardinality of this set. If sample  $i$  has an event indicator during the study period, this objective classifies all samples with an event indicator as positive cases and those that are censored as negative cases, and vice-versa. Clinically, this would translate to maximizing feature representations of multiple patients' imaging data (within a batch) who eventually convert to AD.

Time-To-Event and Event Labels for SSL Training. Progression analysis uniquely benefits from having both event indicators and time-to-event information as labels, providing a comprehensive view of patient outcomes. When considering disease progression, it is reasonable to assume that the features of two patients at similar stages of progression will be more similar than those from a late-stage and early-stage patient pair. We therefore hypothesize that utilizing both types of labels in SSL training enhances the learning process, leading to nuanced models that can accurately predict the event timing and occurrence, thus significantly improving disease progression analysis.

Towards this, we devise weighing schemes for each sample in the batch relative to the anchor point, based on their time-to-event information. We calculate the time difference between the anchor point and other samples in a batch, using the maximum and minimum differences to assign weights to each pair. These weights determine the strength of feature similarity, enforcing that patients at similar stages in development will have a stronger pull than an early/late stage patient pair. Our proposed SSL learning objective function incorporating both time-to-event and labels is represented as:

$$
\mathcal{L}_{\text{TE-SSL}} = \sum_{i \in I} \frac{-1}{|P(i)|} \sum_{p \in P(i)} \log \frac{\omega_{i,p} \exp(z_i \cdot z_p/\tau)}{\sum_{a \in A(i)} \omega_{i,a} \exp(z_i \cdot z_a)/\tau}
$$
(3)

where the weight term  $\omega_{**}$  for each anchor point is calculated as:

<span id="page-99-1"></span>
$$
\omega_{i,j} = \frac{\alpha - \beta}{s - l} \Delta_{i,j} + \frac{\beta - \alpha}{s - l} s + \alpha \tag{4}
$$

where  $\Delta_{i,j} = |T_i - T_j|$  is the time difference associated with data points i and j. Additionally, we compute  $\Lambda = {\{\Delta_{i,j} | i,j \in I, i \neq j\}}$  and take  $s = \min \Lambda$ and  $l = \max \Lambda$  to establish the maximum and minimum time span differences between samples in the batch. Finally,  $\alpha$  and  $\beta$  serve as hyperparameters defining the maximum and minimum weight values, respectively. Specifically, the pair with the smallest time difference is assigned the highest weight,  $\alpha$ , and the pair with the largest time difference receives the lowest weight,  $\beta$ .

<span id="page-99-0"></span>

### 2.2 Time-To-Event Prediction

To leverage the feature space learned with SSL frameworks, we construct a deep learning framework consisting of an encoder network  $\mathcal{E}(\cdot)$  and a projection head  $\mathcal{P}(\cdot)$ . During pretraining (Sect. [2.1\)](#page-97-0), multiviewed data (original and it's augmented copy  $\mathcal{X}$ ) is passed through the encoder module to obtain  $r = \mathcal{E}(\tilde{\mathcal{X}}) \in \mathbb{R}^{d_E}$ , where  $d_E$  is the dimension of r. A final representation  $z = \mathcal{P}(r) \in \mathbb{R}^{d_P}$  (*d<sub>P</sub>* is the dimension *z*) is computed and normalized for the pretraining procedure. After pretraining,  $\mathcal{P}(\cdot)$  is discarded and replaced with task-specific head, which is then finetuned together with the encoder network on the time-to-event objective.

For our task head, we adopt the DeepHit framework [\[13](#page-104-9)], a deep learning approach to survival analysis. With DeepHit framework, instead of predicting a single hazard coefficient for a given input, we output a distribution of hazards at discrete time points. This allows the model to learn the first hitting times

(predicted time until the occurrence of the first event of interest for each subject) directly without making assumptions about the underlying form of the data. In specific, the model learns to minimize the loss function  $\mathcal{L}_{total} = \mathcal{L}_1 + \mathcal{L}_2$ , where  $\mathcal{L}_1$  is the log-likelihood of the distribution of the hitting time, defined as

$$
\mathcal{L}_1 = -\sum_{i=1}^{N} [\mathbb{1}(\delta_i = 1) * \log h_i^{T_i} + \mathbb{1}(\delta_i \neq 1) * \log (1 - \hat{F}(T_i | x_i)] \tag{5}
$$

where, 1 is an indicator function evaluating to 1 iff  $\delta_i = 1$  (event occurred),  $h_i^{T_i}$  corresponds to the predicted hazard for input  $X_i$  at time  $T_i$ , and  $\hat{F}(T_i|x_i)$  is the estimated cumulative incidence function (CIF) which approximates the is the estimated cumulative incidence function (CIF) which approximates the probability that the event will occur on or before time  $T_i$ .  $\mathcal{L}_2$  incorporates a combination of cause-specific ranking loss functions and is defined as:

$$
\mathcal{L}_2 = \gamma \sum_{i \neq j} A_{i,j} \cdot \exp(\hat{F}(T_i|x_i), \hat{F}(T_i|x_j))
$$
\n(6)

where  $\gamma$  is a hyperparameter which indicates the intensity of the ranking loss and  $A_{i,j} = \mathbb{1}(T_i \lt T_j)$  represents an indicator function which evaluates to 1 if a pair  $(i, j)$  experience an event at different times.

### 3 Experiments and Results

ADNI Dataset: Our data consists of a cohort of 493 unique patients in the Alzheimer's Disease Neuroimaging Initiative (ADNI) [\[16\]](#page-104-10) dataset. Each subject has one or more visits containing a 3D T1-weighted MR Image, yielding a total of 2007 data points. Patients are diagnosed as being cognitively normal (CN), having mild cognitive impairment (MCI), or Alzheimer's dementia (AD) at every visit. We define *converters* as subjects whom were CN or MCI during their initial visit, but developed AD within the duration of the study. Additionally, each visit contains the number of months since the baseline observation, which acts as the time-to-event signal. The data were preprocessed via the pipeline laid out by [\[14\]](#page-104-11) and divided based on the unique participants to avoid data leakage. For patients with multiple visits, we treat each visit as a unique data point.

Implementation Details: We utilize a 3D CNN adapted from [\[14\]](#page-104-11) as our backbone MRI encoder for both pretraining and finetuning tasks. The encoder takes in  $X \in \mathbb{R}^{N \times 96 \times 96 \times 96}$  and outputs a representation  $\mathcal{E}(X) = r \in \mathbb{R}^{N \times 1024}$ , where  $N$  is the batch size.

*Pretrain Phase:* Contrastive based SSL techniques require large batch sizes to train properly. Due to hardware constraints, we selected  $N = 16$  and accumulated gradients for 8 iterations before backpropagation to simulate a batch size of 128. The encoded representation  $r$  is then passed through the projection head to achieve  $z = \mathcal{P}(r) \in \mathbb{R}^{N \times 128}$ . We assigned a temperature of  $\tau = 0.07$  for all

| Methods        | C-td $\uparrow$                                         | IBS $\downarrow$                                        |
|----------------|---------------------------------------------------------|---------------------------------------------------------|
| No pretraining | 0.7329                                                  | 0.2099                                                  |
| SSL            | 0.7511                                                  | <span style="text-decoration: underline;">0.1985</span> |
| E-SSL          | <span style="text-decoration: underline;">0.7720</span> | 0.1997                                                  |
| TE-SSL         | <b>0.7873</b>                                           | <b>0.1889</b>                                           |

<span id="page-101-0"></span>Table 1. Comparison between different time-to-event prediction approach for C-td ↑ and IBS ↓ scores. Bold and underline denote 1st and 2nd best values, respectively.

Image /page/101/Figure/3 description: The image displays three scatter plots side-by-side, each representing a feature space. The first plot is titled "Feature space of SSL", the second is titled "Feature space of E-SSL", and the third is titled "Feature space of TE-SSL". Each plot contains numerous data points colored blue, green, red, and gray. A legend on the right indicates that blue dots represent "Times: [0,18]", green dots represent "Times: [18,36]", red dots represent "Times: [36,66]", and gray dots represent "Censored" data points. The distribution of colored points varies across the three plots, with the first plot showing a more uniform distribution, while the second and third plots show more clustered patterns, particularly in the second half of the plots.

<span id="page-101-1"></span>Fig. 2. t-SNE analysis of representations across different SSL frameworks. Individual points, if not censored, are labeled with different time-to-event groups.

contrastive loss functions and optimized the model using LARS with a learning rate of  $.3 \times N/256 = .15$  [\[8](#page-104-12)] and a momentum of 0.9.

*Finetune Phase:* We first discard the projection head of the pretrained model and replace it with a task-specific  $P_t(\cdot)$  to predict the probability mass function (PMF) associated with the input. We then apply the Adam optimizer with a learning rate of  $1e - 4$  during training.

*Evaluation:* We compute the Time-dependent Concordance Index (C-td) [\[1\]](#page-103-6), which measures the extent to which the ordering of actual survival times of pairs agrees with the ordering of their predicted risk. Additionally, we evaluate the Integrated Brier Scores (IBS) [\[10\]](#page-104-13), which measures an overall assessment of the model's performance across all available times considered in the study. These metrics are calculated using the Pycox library.

### 3.1 Results

Our primary results in Table [1](#page-101-0) showcase a comparison between our E-SSL and TE-SSL frameworks and baseline models: *No Pretraining* and *SSL*. For fairness, all models, including E-SSL and TE-SSL, were finetuned using the same model as *No Pretraining*. We trained each model with three random seeds and reported their average results. Our frameworks outperform others in both C-td (higher is better) and IBS (lower is better) metrics. Notably, E-SSL introduces the novel use of event labels in SSL training for progression analysis, while TE-SSL's

| $\alpha$ | $\beta$ | C-td $\uparrow$ | IBS $\downarrow$ |
|----------|---------|-----------------|------------------|
| 1        | 0.5     | 0.7636          | 0.1954           |
| 1        | 0.7     | 0.7581          | 0.1954           |
| <b>1</b> | 0.9     | <b>0.7883</b>   | <b>0.1889</b>    |
| 1.1      | 1       | 0.7714          | 0.1981           |
| 1.3      | 1       | 0.761           | 0.1931           |
| 1.5      | 1       | 0.733           | 0.2027           |

<span id="page-102-0"></span>**Table 2.** Model performance on different values of  $\alpha$  and  $\beta$  for TE-SSL pretraining.

innovative incorporation of both time-to-event and event information leads to the best performance, highlighting its efficacy in progression analysis.

In Fig. [2,](#page-101-1) we present t-SNE plots [\[18\]](#page-104-14) to examine the feature spaces learned by the SSL frameworks considered in this study. The left panel displays the feature space resulting from standard SSL training, which lacks the clear separation between censored patient data and patient data with events observed in the middle and right panels, where supervisory signals enhance SSL training. Among the two SSL frameworks with supervisory signals, the incorporation of both timeto-event and event information appears to achieve superior separability.

### 3.2 Ablation Analysis

We conducted the ablation analysis to better understand the roles of  $\alpha$  and  $\beta$  in our proposed TE-SSL (Eq. [4\)](#page-99-1), which serve to define the intensity of the weight values for a pair of inputs  $(i, j)$ . The difference between  $\alpha$  and  $\beta$  dictates how strongly to differentiate distant pairs. For instance, in a batch of N samples if  $(i, j)$  represents the pair with the largest time difference, setting  $\beta = 0$ , effectively considers  $j$  as a negative sample relative to the anchor  $i$ . Therefore, we explored sensible configurations of  $\alpha$  and  $\beta$ , with  $1 \leq \alpha \leq 1.5$  and  $0.5 \leq \beta \leq 1$ , noting that an  $\alpha - \beta < 0.5$  would inappropriately diminish the negative impact of distant pairs. The results, presented in Table [2,](#page-102-0) demonstrate the method's relative stability within these selected ranges. It is also noteworthy that TE-SSL outperforms both standard SSL and the baseline no-pretraining time-to-event prediction model in five out of six experiments, highlighting its efficacy.

#### 4 Conclusion

We introduce the Time and Event-aware SSL framework, which integrates both event and time-to-event information to guide the learning process of feature representations. As demonstrated, our approach surpasses existing self-supervised learning methods, including those supervised versions that incorporate only the event label. This underscores the critical importance of utilizing both event and time-to-event information in the progression analysis of Alzheimer's disease. Our evaluation using the ADNI dataset showcases the practical applicability and effectiveness of our proposed method, significantly contributing to the advancement of AD progression study.

Acknowledgments. This research was supported by West Virginia Higher Education Policy Commission's Research Challenge Grant Program 2023 and DARPA/FIU AI-CRAFT grant. Data collection and sharing for the Alzheimer's Disease Neuroimaging Initiative (ADNI) is funded by the National Institute on Aging (National Institutes of Health Grant U19 AG024904). The grantee organization is the Northern California Institute for Research and Education. In the past, ADNI has also received funding from the National Institute of Biomedical Imaging and Bioengineering, the Canadian Institutes of Health Research, and private sector contributions through the Foundation for the National Institutes of Health (FNIH) including generous contributions from the following: AbbVie, Alzheimer's Association; Alzheimer's Drug Discovery Foundation; Araclon Biotech; BioClinica, Inc.; Biogen; Bristol-Myers Squibb Company; CereSpir, Inc.; Cogstate; Eisai Inc.; Elan Pharmaceuticals, Inc.; Eli Lilly and Company; EuroImmun; F. Hoffmann-La Roche Ltd and its affiliated company Genentech, Inc.; Fujirebio; GE Healthcare; IXICO Ltd.; Janssen Alzheimer Immunotherapy Research & Development, LLC.; Johnson & Johnson Pharmaceutical Research & Development LLC.; Lumosity; Lundbeck; Merck & Co., Inc.; Meso Scale Diagnostics, LLC.; NeuroRx Research; Neurotrack Technologies; Novartis Pharmaceuticals Corporation; Pfizer Inc.; Piramal Imaging; Servier; Takeda Pharmaceutical Company; and Transition Therapeutics.

Disclosure of Interests. The authors have no competing interests to report.

## References

- <span id="page-103-6"></span>1. Laura Antolini, Patrizia Boracchi, and Elia Biganzoli. A time-dependent discrimination index for survival data. *Statistics in medicine*, 24(24):3927–3944, 2005.
- <span id="page-103-0"></span>2. Akhilesh Deep Arya, Sourabh Singh Verma, Prasun Chakarabarti, Tulika Chakrabarti, Ahmed A Elngar, Ali-Mohammad Kamali, and Mohammad Nami. A systematic review on machine learning and deep learning techniques in the effective diagnosis of alzheimer's disease. *Brain Informatics*, 10(1):17, 2023.
- <span id="page-103-2"></span>3. No authors listed. 2023 alzheimer's disease facts and figures. *ALZHEIMER'S ASSOCIATION REPORT*, 2023.
- <span id="page-103-3"></span>4. Philip Bachman, R Devon Hjelm, and William Buchwalter. Learning representations by maximizing mutual information across views. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-103-4"></span>5. Yoshua Bengio, Aaron Courville, and Pascal Vincent. Representation learning: A review and new perspectives. *IEEE transactions on pattern analysis and machine intelligence*, 35(8):1798–1828, 2013.
- <span id="page-103-1"></span>6. Allen J Chang, Rebecca Roth, Eleni Bougioukli, Theodor Ruber, Simon S Keller, Daniel L Drane, Robert E Gross, James Welsh, Anees Abrol, Vince Calhoun, et al. Mri-based deep learning can discriminate between temporal lobe epilepsy, alzheimer's disease, and healthy controls. *Communications Medicine*, 3(1):33, 2023.
- <span id="page-103-5"></span>7. Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations. In *International conference on machine learning*, pages 1597–1607. PMLR, 2020.

- <span id="page-104-12"></span>8. Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations. 2 2020.
- <span id="page-104-6"></span>9. Nanqing Dong, Michael Kampffmeyer, and Irina Voiculescu. Self-supervised multitask representation learning for sequential medical images. In *Joint European Conference on Machine Learning and Knowledge Discovery in Databases*, pages 779– 794. Springer, 2021.
- <span id="page-104-13"></span>10. Erika Graf, Claudia Schmoor, Willi Sauerbrei, and Martin Schumacher. Assessment and comparison of prognostic classification schemes for survival data. *Statistics in medicine*, 18(17-18):2529–2545, 1999.
- <span id="page-104-5"></span>11. Prannay Khosla, Piotr Teterwak, Chen Wang, Aaron Sarna, Yonglong Tian, Phillip Isola, Aaron Maschinot, Ce Liu, and Dilip Krishnan. Supervised contrastive learning. *Advances in neural information processing systems*, 33:18661–18673, 2020.
- <span id="page-104-3"></span>12. Rayan Krishnan, Pranav Rajpurkar, and Eric J Topol. Self-supervised learning in medicine and healthcare. *Nature Biomedical Engineering*, 6(12):1346–1352, 2022.
- <span id="page-104-9"></span>13. Changhee Lee, William Zame, Jinsung Yoon, and Mihaela Van Der Schaar. Deephit: A deep learning approach to survival analysis with competing risks. In *Proceedings of the AAAI conference on artificial intelligence*, volume 32, 2018.
- <span id="page-104-11"></span>14. Sheng Liu, Chhavi Yadav, Carlos Fernandez-Granda, Narges Razavian, Adrian V Dalca, Matthew Mcdermott, Emily Alsentzer, Sam Finlayson, Michael Oberst, Fabian Falck, and Brett Beaulieu-Jones. On the design of convolutional neural networks for automatic detection of alzheimer's disease, 2020.
- <span id="page-104-2"></span>15. Ishan Misra and Laurens van der Maaten. Self-supervised learning of pretextinvariant representations. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 6707–6717, 2020.
- <span id="page-104-10"></span>16. R. C. Petersen, P. S. Aisen, L. A. Beckett, M. C. Donohue, A. C. Gamst, D. J. Harvey, C. R. Jack, W. J. Jagust, L. M. Shaw, A. W. Toga, and et al. Alzheimer's disease neuroimaging initiative (adni). *Neurology*, 74(3):201-209, Jan 2010.
- <span id="page-104-7"></span>17. Luca Rettenberger, Marcel Schilling, Stefan Elser, Moritz Böhland, and Markus Reischl. Self-supervised learning for annotation efficient biomedical image segmentation. *IEEE Transactions on Biomedical Engineering*, 2023.
- <span id="page-104-14"></span>18. Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008.
- <span id="page-104-0"></span>19. Samuel L Warren and Ahmed A Moustafa. Functional magnetic resonance imaging, deep learning, and alzheimer's disease: A systematic review. *Journal of Neuroimaging*, 33(1):5–18, 2023.
- <span id="page-104-1"></span>20. Junhao Wen, Elina Thibeau-Sutre, Mauricio Diaz-Melo, Jorge Samper-González, Alexandre Routier, Simona Bottani, Didier Dormont, Stanley Durrleman, Ninon Burgos, Olivier Colliot, et al. Convolutional neural networks for classification of alzheimer's disease: Overview and reproducible evaluation. *Medical image analysis*, 63:101694, 2020.
- <span id="page-104-8"></span>21. Pengshuai Yang, Zhiwei Hong, Xiaoxu Yin, Chengzhan Zhu, and Rui Jiang. Selfsupervised visual representation learning for histopathological images. In *Medical Image Computing and Computer Assisted Intervention–MICCAI 2021: 24th International Conference, Strasbourg, France, September 27–October 1, 2021, Proceedings, Part II 24*, pages 47–57. Springer, 2021.
- <span id="page-104-4"></span>22. Jure Zbontar, Li Jing, Ishan Misra, Yann LeCun, and Stéphane Deny. Barlow twins: Self-supervised learning via redundancy reduction. In *International Conference on Machine Learning*, pages 12310–12320. PMLR, 2021.

Image /page/105/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a gray ribbon or bookmark shape inside. The text below the icon reads "Check for updates" in gray capital letters.

# Training ViT with Limited Data for Alzheimer's Disease Classification: An Empirical Study

Kassymzhomart Kunanbayev, Vyacheslav Shen, and Dae-Shik Kim<sup>( $\boxtimes$ )</sup>

KAIST, 291 Daehak-ro, Yuseong-gu, Daejeon, South Korea {kkassymzhomart,shen9910,daeshik}@kaist.ac.kr

Abstract. In this paper, we conduct an extensive exploration of a Vision Transformer (ViT) in brain medical imaging in a low-data regime. The recent and ongoing success of Vision Transformers in computer vision has motivated its development in medical imaging, but trumping it with inductive bias in a brain imaging domain imposes a real challenge since collecting and accessing large amounts of brain medical data is a laborintensive process. Motivated by the need to bridge this data gap, we embarked on an investigation into alternative training strategies ranging from self-supervised pre-training to knowledge distillation to determine the feasibility of producing a practical plain ViT model. To this end, we conducted an intensive set of experiments using a small amount of labeled 3D brain MRI data for the task of Alzheimer's disease classification. As a result, our experiments yield an optimal training recipe, thus paving the way for Vision Transformer-based models for other lowdata medical imaging applications. To bolster further development, we release our assortment of pre-trained models for a variety of MRI-related applications: [https://github.com/qasymjomart/ViT\\_recipe\\_for\\_AD.](https://github.com/qasymjomart/ViT_recipe_for_AD)

Keywords: Vision Transformer *·* Alzheimer's Disease *·* Low-data regime

### 1 Introduction

The decade-long triumph of convolutional neural network-based (CNN) models in computer vision has been overtaken by Vision Transformer (ViT) [\[7\]](#page-113-0) models. Despite having vision-specific inductive biases such as locality and spatial invariance [\[1,](#page-113-1)[7\]](#page-113-0), CNN models fall short in accommodating long-range global dependencies *−* the feature that transformer-based models tend to naturally possess [\[25\]](#page-114-0). Long-range global dependencies are principally important in analyzing medical images, including brain MRI, the 3D high-dimensional nature of which requires precise focus on important discriminative locations. Furthermore, it is observed that 3D CNN models demand greater computational resources compared to the ViT models. Yet, to achieve remarkable performance, Vision Transformers remain data-hungry, i.e. they tend to require large amounts of data

to secure inductive bias [\[7](#page-113-0)]. A substantial number of works have been focused on training Vision Transformers in a low-data regime [\[16](#page-114-1),[18](#page-114-2)[,23](#page-114-3)], but only a handful of works focus on brain imaging applications [\[17](#page-114-4)].

Accordingly, the limited availability of structural brain MRI datasets, especially related to specific diseases like Alzheimer's disease (AD), may hamstring the development of accurate diagnostic tools. Although many prior works utilized CNN models with remarkable results, CNN-based approaches often downsample the input features, leading to the potential loss of information [\[21](#page-114-5)]. Other works that adopt Vision Transformers either combine with CNN feature extractors in a hybrid fashion or use large datasets for training [\[14,](#page-114-6)[17\]](#page-114-4).

In this paper, we conducted empirical experiments with a plain ViT model to process a limited amount of 3D brain MRI data. The main reason for focusing on the plain version is to examine its potential without CNN-based feature extractors. By examining various training strategies and their effectiveness, our empirical experiments illustrate that a holistic orchestration of certain training strategies can boost the performance of a ViT model when trained with a few labeled samples. To summarize, our contributions can be outlined as follows:

- We pre-train the plain ViT model via self-supervised learning with the separate brain MRI data from different clinical cohorts and transfer it to fine-tune on AD classification task with few labeled data
- We explore the impact of pre-training with different pre-training data sizes and several non-homogeneous pre-training datasets
- In fine-tuning, we further examine the performance of the model in more extreme low-data scenarios
- We also investigate the application of different training methods such as 3D data augmentations, regularization, and knowledge distillation
- To bolster further research in the brain MRI community, we make available our assortment of pre-trained ViT models, trained with various combinations of non-homogeneous MRI datasets.

## 2 Method

### 2.1 Training Strategies

Model Architecture. Encoder model architecture is important for processing brain MRI data and extracting global feature vector representations for further downstream applications, like classification. In this work, we experiment with the widely used configuration of the Vision Transformer *−*ViT-B [\[7](#page-113-0)], which has balanced computational efficiency and model complexity with 12 layers, 12 heads, a width of 768, and 86 million parameters. Since our input is 3D dimensional, we design our model for 3D input by replacing a linear 2D patch embedding with a 3D equivalent (see Fig. [1\)](#page-107-0) with the same patch size of 16.

Pre-training. has become a de facto one of the key ingredients in developing successful deep learning models [\[9](#page-113-2),[22\]](#page-114-7) and providing Vision Transformers with inductive bias [\[7](#page-113-0)]. Furthermore, given the prevalence of a low-data regime in

Image /page/107/Figure/1 description: This figure illustrates two processes, (a) and (b), related to a Vision Transformer (ViT) model applied to medical imaging, likely MRI scans of the brain. Process (a) depicts a masked autoencoder approach. A 128x128x128 input volume is processed by a ViT Encoder, which breaks it into patches. Some patches are masked (represented by white cubes), and others are encoded (represented by purple cubes). A Decoder then reconstructs the masked patches based on the encoded ones, with an L2 loss guiding the reconstruction. Process (b) shows a classification task. A 128x128x128 input volume is fed into a ViT Encoder, and the output is passed through a Fully Connected (FC) layer, resulting in a classification output labeled 'CN AD', likely representing 'Cognitively Normal' and 'Alzheimer's Disease'.

<span id="page-107-0"></span>Fig. 1. Masked Autoencoders (MAE) pre-training (a) and fine-tuning (b) for 3D brain MRI data. For fine-tuning, pre-trained weights from the ViT encoder are transferred.  $ViT = Vision Transformer; FC = fully connected layer; CN = C$  Cognitively normal; AD = Alzheimer's disease

medical imaging, particularly when related to diseases, we conjecture the importance of self-supervised pre-training using available medical data. To this end, we pre-train the model with separate MRI data unrelated to Alzheimer's disease or dementia, for their wide availability and abundance among public datasets.

In this paper, we leverage Masked Autoencoders (MAE) [\[9\]](#page-113-2) as a selfsupervised pre-training technique, due to its success with ViT. At first, an input 3D MRI image is divided into non-overlapping patches with a fixed, sine-cosine 3D positional embedding. Then, we follow the original implementation [\[9](#page-113-2)]: a subset of the patches is randomly masked and reconstructed by a lightweight decoder that takes as input the encoder representations of the visible subset of patches as well as the masked tokens, as shown in Fig [1.](#page-107-0) Similar to [\[9\]](#page-113-2), we use an asymmetrical architecture for the decoder with 8 layers, 16 heads, but with an embedding hidden dimension of 576 due to 3D data dimensionality. This decoder architecture has a total of 36*.*9 million parameters.

Fine-Tuning. The pre-trained weights of the encoder are transferred for finetuning, while the fully connected classification head is randomly initialized. The absolute sine-cosine positional embeddings are also transferred as initialization weights for learnable positional embeddings. Then, the training of the model was conducted in a supervised fashion using the labeled data.

We test MAE-based pre-training with three different masking ratios: 25%, 50%, and 75%. Furthermore, we explore the effect of the pre-training data size on downstream performance as well as the combination of several non-homogeneous pre-training datasets.

Fine-Tuning with Different Amounts of Data. The transformers-based models have been shown to excel in downstream tasks with few samples after pre-training [\[4](#page-113-3)]. We similarly investigate it in brain imaging and further study the generalizability of the ViT model under various low-data settings, that is, we fine-tune with different fractions of labeled training data  $-10\%$ ,  $20\%$ ,  $40\%$ , 60%, 80%.

| Dataset    | Magnet strength | AD  | CN   | \$P_m\$ (%) |
|------------|-----------------|-----|------|-------------|
| BRATS 2023 | 3T              | -   | 1251 | -           |
| IXI        | 1.5T/3T         | -   | 581  | -           |
| OASIS-3    | 3T              | -   | 625  | -           |
| ADNI1      | 1.5T            | 192 | 229  | 54.4        |
| ADNI2      | 3T              | 159 | 201  | 55.8        |

<span id="page-108-1"></span>**Table 1.** Dataset details.  $P_m$  (%) indicates the share of the majority class. CN = Cognitively normal;  $AD = Alzheimer's$  disease

Hyperparameters Ablation. In fine-tuning, we investigate the effect of data augmentation as well as a group of regularization methods of dropout, attention dropout, and drop path [\[11](#page-113-4)]. Following [\[8](#page-113-5)], our data augmentation strategy incorporates a set of 3D medical data augmentations, which includes random affine, random flipping, random rotation for 90 degrees, random scaling, and a random shift of intensity, all with a probability of 0.2.

Knowledge Distillation through attention, which was introduced in ViT with an additional distillation token by Touvron *et al.* [\[24\]](#page-114-8), underscored the significance of knowledge distillation [\[10\]](#page-113-6) in enhancing transformer architecture efficiency without supervised pre-training on the huge amount of external data. The distillation token is assumed to interact with all other self-attention embeddings and train with the distillation loss function [\[24](#page-114-8)], thus learning from a teacher model's predictions simultaneously. We similarly included this type of knowledge distillation in our ablation study.

### 2.2 Datasets

Pre-training Datasets. For pre-training based on MAE, we utilized three different, public, and non-homogeneous T1-weighted structural MRI datasets: BRATS 2023  $[2,3,20]$  $[2,3,20]$  $[2,3,20]$  $[2,3,20]$ , IXI<sup>[1](#page-108-0)</sup>, and OASIS-3  $[15]$ . Note that BRATS 2023 and IXI datasets are unrelated to dementia and Alzheimer's disease, while OASIS-3 includes images of individuals with various stages of cognitive decline, but we utilized images of only cognitively normal cases. We did brain extraction of all images of datasets using HD-BET [\[12\]](#page-113-9). More information on datasets is provided in Table [1.](#page-108-1)

Fine-Tuning Datasets. For AD classification experiments we collected two T1-weighted structural MRI datasets from the Alzheimer's Disease Neuroimag-ing Initiative (ADNI) database<sup>[2](#page-108-2)</sup> [\[13\]](#page-114-11), namely baseline collections of ADNI1 and

<span id="page-108-0"></span><sup>1</sup> [https://brain-development.org/ixi-dataset.](https://brain-development.org/ixi-dataset)

<span id="page-108-2"></span><sup>2</sup> [https://adni.loni.usc.edu.](https://adni.loni.usc.edu)

|                                                              | ADNI1 |                                     | ADNI2 |  |
|--------------------------------------------------------------|-------|-------------------------------------|-------|--|
| Training from scratch                                        |       | $66.0 \pm 0.86$ 69.3 $\pm$ 1.81     |       |  |
| MAE-based fine-tuning $25\%$ 74.5 $\pm$ 1.36 74.0 $\pm$ 1.67 |       |                                     |       |  |
|                                                              |       | $ 50\% 79.5 \pm 1.09 79.2 \pm 0.48$ |       |  |
|                                                              |       | $75\%$ 79.6 ± 0.82 81.9 ± 2.17      |       |  |

<span id="page-109-0"></span>Table 2. Cross-validation accuracies  $(\%)$  for training from scratch and fine-tuning of MAE pre-training across different masking ratios (25%, 50%, and 75%). Pre-training significantly enhances accuracy. Best results are in **bold**.

ADNI2. Each dataset contains structural MRI images of Alzheimer's disease patients and cognitively normal adults. Similar to pre-training datasets, we only perform brain extraction to pre-process the data minimally. More information on datasets is also provided in Table [1.](#page-108-1)

### 2.3 Experimental Setup

The default experimental setting is as follows. In all experiments, brain MRI images are transformed in the following order: images are first resampled to the same voxel spacing  $(1.75 \times 1.75 \times 1.75)$ , followed by foreground crop, resizing  $(128 \times 128 \times 128)$ , and intensity normalization.

Following He *et al.* [\[9](#page-113-2)], we pre-train the model using the AdamW optimizer [\[19](#page-114-12)] at an initial learning rate of 10−<sup>4</sup>, and employ a half-cycle cosine scheduler with a 40-epoch linear warmup. We pre-train for 1000 epochs with a batch size of 32. On top of the abovementioned transformations, we only apply random spatial cropping.

In fine-tuning, we used an optimizer with an initial learning rate of  $10^{-5}$  and a cosine annealing scheduler. We train using cross-entropy loss for 50 epochs with a batch size of 4. To determine a more accurate estimate of the performance in a low-data regime, we run each fine-tuning experiment as a stratified 4-fold crossvalidation and use the best validation epoch to calculate the cross-validation accuracy. We repeat all experiments three times and report the average.

All experiments are implemented using PyTorch and data augmentations are accessed from MONAI [\[5\]](#page-113-10). The GPU configuration consisted of NVIDIA Titan RTX with 24 GB of VRAM.

### 3 Results and Discussion

In this section, we discuss experimental results and our key findings. Unless otherwise specified, reported results are for the pre-training with a masking ratio of 75% and using all combined pre-training datasets. Also, all results are with the best set of training methods found from our ablation study in Table [3.](#page-112-0)

#### 3.1 Pre-training Findings

Pre-training Gives a Considerable Boost to the Accuracy. Table [2](#page-109-0) compares the performance of fine-tuning with training from scratch. In general, we

Image /page/110/Figure/1 description: This is a bar chart comparing the cross-validation accuracy (%) for two datasets, ADNI1 and ADNI2. For ADNI1, the accuracy for BRATS 2023 is approximately 76%, for BRATS 2023 & IXI is approximately 78%, and for BRATS 2023 & IXI & OASIS-3 is approximately 79.5%. For ADNI2, the accuracy for BRATS 2023 is approximately 76.5%, for BRATS 2023 & IXI is approximately 81.8%, and for BRATS 2023 & IXI & OASIS-3 is approximately 81.8%. Error bars are shown for each bar, indicating variability in the results.

<span id="page-110-0"></span>Fig. 2. Impact of the pre-training dataset size on fine-tuning. More pre-training data leads to better fine-tuning. Pre-training was performed with a 75% masking ratio.

observe that *fine-tuning the pre-trained weights enhances the accuracy of the ViT model by a considerable margin* in both AD classification datasets. When tested across different masking ratios, 75% exhibited the highest performance increase of up to 13*.*6% in ADNI1 and 12*.*6% in ADNI2. This is in line with the findings of the original implementation of MAE in computer vision [\[9](#page-113-2)], and other related works in medical imaging [\[6](#page-113-11)[,26](#page-114-13)]. Nevertheless, we emphasize that, in our experiments, we perform pre-training with the data unrelated to Alzheimer's disease or dementia. Thus, our results demonstrate the transferability of pre-trained features of ViT across different domains as well as its ability to boost performance in downstream applications.

Pre-training Data Size is Crucial and Combining Non-homogeneous Pre-training Datasets is Effective. Since we conducted pre-training with three various datasets, we also investigated the impact of pre-training data size on further fine-tuning accuracy. Figure [2](#page-110-0) illustrates that the pre-training data size is proportional to the fine-tuning accuracy, implying that *more pre-training data yields improved fine-tuning*. We also observe that *combining different nonhomogeneous datasets from different sources is effective* in boosting the accuracy. Despite having a mix of magnetic strengths (Table [1\)](#page-108-1) in the pre-training data, both ADNI1 and ADNI2 benefited with increasing accuracy. We conjecture that further experiments on pre-training with data augmentations would possibly result in improved performance, and leave it for future research.

Pre-training Allows to Succeed with Fewer Labeled Data Even Under Extreme Low-Data Settings. Figure [3](#page-111-0) represents training with different fractions of labeled data ranging from 10% to 100%. Generally, we observe that *fine-tuning with as few as 20% of training set produces the model more accurate than training from scratch with 100% data*. Note that 20% of labeled data corresponds to roughly 60 samples from both datasets. As a consequence, we conclude that *the ViT model enormously benefits from pre-training when trained under low-data scenarios in brain imaging*. Self-supervised pre-training with the available data, which is unrelated to the downstream task, could therefore be

Image /page/111/Figure/1 description: The image displays two plots, ADNI1 and ADNI2, comparing the CV accuracy (%) on the y-axis against labeled data fractions (%) on the x-axis. Both plots show two lines: a dashed line representing 'train from scratch (100% labeled data)' and a solid line with markers representing 'MAE pre-trained'. In ADNI1, the 'train from scratch' line is a horizontal dashed line at approximately 66%. The 'MAE pre-trained' line starts at around 65% for 10% labeled data, increases to about 68% at 20%, 73% at 40%, 76% at 60%, 78% at 80%, and reaches approximately 80% at 100% labeled data. Error bars are present for the 'MAE pre-trained' data points. In ADNI2, the 'train from scratch' line is also a horizontal dashed line at approximately 69%. The 'MAE pre-trained' line starts at about 71% for 10% labeled data, increases to 74% at 20%, 77% at 40%, 79% at 60%, and reaches approximately 82% at 80% and 84% at 100% labeled data, with error bars shown for these points as well. The overall trend in both plots indicates that the 'MAE pre-trained' method generally achieves higher CV accuracy than 'train from scratch', and accuracy increases with a higher fraction of labeled data.

<span id="page-111-0"></span>Fig. 3. Training with different fractions of labeled data. While pre-training is essential, labeled data remains critical as well.

advantageous in developing practical ViT models with small fine-tuning datasets. Notwithstanding, we note that the amount of labeled data remains crucial for further improvements.

#### 3.2 Ablation Study

Table [3](#page-112-0) illustrates the ablation study with different training methods in order to find the most optimal training ingredients for fine-tuning the pre-trained ViT model in a low-data regime.

One *important component* for successful training is data augmentations, which elevated the accuracy by up to 3.4%. However, we *did not observe significant improvements* with regularization: including both stochastic drop path and attention dropout improve the performance, meanwhile, dropout had an apparent effect on the fine-tuning accuracy with the performance drop in both datasets ADNI1 and ADNI2. With a similar observation, Steiner *et al.* [\[23](#page-114-3)] concluded that regularization may hurt the performance as training data gets large. In our case, ADNI1 originally includes more samples than ADNI2 (Table [1\)](#page-108-1) and had more performance drop, thus corroborating the conclusion.

Knowledge Distillation through attention with a distillation token was another subject of ablation. We employed a 3D Resnet-152 network from the MONAI library as a teacher model. Similar to the above experimental setup, this model was trained separately for each of the three seeds<sup>[3](#page-111-1)</sup>. Our experimentation, detailed in Table [3,](#page-112-0) first involved applying knowledge distillation to a randomly initialized model. Later, we explored the effects of applying distillation after the pre-training. Notably, *pre-training yielded a performance boost compared to training from scratch and outperformed our model without distillation by 0.1%*. However, our findings indicate that the primary performance

<span id="page-111-1"></span> $^3$  Teacher network has average accuracy of  $84.63\%$  and  $82.51\%$  for ADNI2 and ADNI1 respectively.

| Ablation 1                      |  | Data aug. Drop path Attn dropout Dropout Distill. token ADNI1 |  | ADNI2                           |
|---------------------------------|--|---------------------------------------------------------------|--|---------------------------------|
| None (default)                  |  |                                                               |  | $79.6 \pm 0.8281.9 \pm 2.17$    |
| Data aug.                       |  |                                                               |  | $ 77.4 \pm 2.06 78.0 \pm 1.15 $ |
| Regularization                  |  |                                                               |  | $78.9 + 1.4481.4 + 1.44$        |
|                                 |  |                                                               |  | $78.2 \pm 0.9080.7 \pm 2.33$    |
|                                 |  |                                                               |  | $ 78.7 \pm 1.07 80.0 \pm 1.69$  |
| Dist. w/o pre-train. $\sqrt{ }$ |  |                                                               |  | $67.7 \pm 0.62$ 69.8 $\pm 2.52$ |
| Dist. with pre-train. $\sqrt{}$ |  |                                                               |  | $ 79.7 \pm 1.19 82.0 \pm 1.37$  |

<span id="page-112-0"></span>Table 3. Ablation study on different training approaches for fine-tuning experiments.

improvement comes from the pre-training, showing its superiority as an effective method of enhancing model performance. Additionally, the knowledge distillation training requires increased GPU memory resulting from the utilization of a 3D convolutional teacher model.

### 4 Conclusion

We investigated the optimal training for the Vision Transformer (ViT) model in brain imaging, namely for Alzheimer's disease classification, in a low-data regime by leveraging various training strategies ranging from self-supervised pre-training to selecting an optimal set of training methods, including data augmentations, and regularizations. We demonstrated that pre-training immensely contributes to the increase in fine-tuning accuracy, even when trained under extreme low-data scenarios. We conducted all pre-training on the data unrelated to the downstream application, showing the generalizability of pre-trained features for fine-tuning disease classification. Additionally, while we showed that pre-training data size remains critical, we also confirmed that it is not only possible but also beneficial to combine different non-homogeneous datasets for pre-training. Finally, we presented an optimal training recipe which is useful in further boosting the fine-tuning accuracy with the ViT. We believe that this work will contribute to the development of optimal models not only for brain imaging but also for other medical imaging applications with limited data and computational resources.

As for limitations, we note the use of a single pre-training method and evaluation of a single task. Therefore we consider our future work to experiment with contrastive learning-based pre-training methods and extend our evaluations on more tasks, such as for example classifying mild cognitive impairment. Our future work will also continue with more exhaustive experiments on other Vision Transformer architectures and training methods.

Acknowledgments. This work was supported by the Engineering Research Center of Excellence (ERC) Program supported by National Research Foundation (NRF), Korean Ministry of Science & ICT (MSIT) (Grant No. NRF-2017R1A5A101470823).

Disclosure of Interests. The authors have no competing interests.

## References

- <span id="page-113-1"></span>1. Arizumi, N.: Studying inductive biases in image classification task (2022), [https://](https://arxiv.org/abs/2210.17141) [arxiv.org/abs/2210.17141](https://arxiv.org/abs/2210.17141)
- <span id="page-113-7"></span>2. Baid, U., et al.: The RSNA-ASNR-MICCAI BraTS 2021 Benchmark on Brain Tumor Segmentation and Radiogenomic Classification (2021), [https://arxiv.org/](https://arxiv.org/abs/2107.02314) [abs/2107.02314](https://arxiv.org/abs/2107.02314)
- <span id="page-113-8"></span>3. Bakas, S., Akbari, H., Sotiras, A., Bilello, M., Rozycki, M., Kirby, J.S., Freymann, J.B., Farahani, K., Davatzikos, C.: Advancing The Cancer Genome Atlas glioma MRI collections with expert segmentation labels and radiomic features. Scientific Data 4(1) (Sep 2017)
- <span id="page-113-3"></span>4. Brown, T.B., Mann, B., Ryder, N., Subbiah, M., Kaplan, J., Dhariwal, P., Neelakantan, A., Shyam, P., Sastry, G., Askell, A., Agarwal, S., Herbert-Voss, A., Krueger, G., Henighan, T., Child, R., Ramesh, A., Ziegler, D.M., Wu, J., Winter, C., Hesse, C., Chen, M., Sigler, E., Litwin, M., Gray, S., Chess, B., Clark, J., Berner, C., McCandlish, S., Radford, A., Sutskever, I., Amodei, D.: Language Models are Few-Shot Learners (2020), <https://arxiv.org/abs/2005.14165>
- <span id="page-113-10"></span>5. Cardoso, M.J., Li, W., Brown, R., Ma, N., Kerfoot, E., Wang, Y., Murrey, B., Myronenko, A., Zhao, C., Yang, D., Nath, V., He, Y., Xu, Z., Hatamizadeh, A., Myronenko, A., Zhu, W., Liu, Y., Zheng, M., Tang, Y., Yang, I., Zephyr, M., Hashemian, B., Alle, S., Darestani, M.Z., Budd, C., Modat, M., Vercauteren, T., Wang, G., Li, Y., Hu, Y., Fu, Y., Gorman, B., Johnson, H., Genereaux, B., Erdal, B.S., Gupta, V., Diaz-Pinto, A., Dourson, A., Maier-Hein, L., Jaeger, P.F., Baumgartner, M., Kalpathy-Cramer, J., Flores, M., Kirby, J., Cooper, L.A.D., Roth, H.R., Xu, D., Bericat, D., Floca, R., Zhou, S.K., Shuaib, H., Farahani, K., Maier-Hein, K.H., Aylward, S., Dogra, P., Ourselin, S., Feng, A.: MONAI: An opensource framework for deep learning in healthcare (2022), [https://arxiv.org/abs/](https://arxiv.org/abs/2211.02701) [2211.02701](https://arxiv.org/abs/2211.02701)
- <span id="page-113-11"></span>6. Chen, Z., Agarwal, D., Aggarwal, K., Safta, W., Hirawat, S., Sethuraman, V., Balan, M.M., Brown, K.: Masked Image Modeling Advances 3D Medical Image Analysis (2022), <https://arxiv.org/abs/2204.11716>
- <span id="page-113-0"></span>7. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., Uszkoreit, J., Houlsby, N.: An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale. ICLR (2021)
- <span id="page-113-5"></span>8. Hatamizadeh, A., Tang, Y., Nath, V., Yang, D., Myronenko, A., Landman, B., Roth, H.R., Xu, D.: UNETR: Transformers for 3D medical image segmentation. In: Proceedings of the IEEE/CVF winter conference on applications of computer vision. pp. 574–584 (2022)
- <span id="page-113-2"></span>9. He, K., Chen, X., Xie, S., Li, Y., Dollár, P., Girshick, R.: Masked autoencoders are scalable vision learners. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 16000–16009 (2022)
- <span id="page-113-6"></span>10. Hinton, G., Vinyals, O., Dean, J.: Distilling the Knowledge in a Neural Network (2015), <https://arxiv.org/abs/1503.02531>
- <span id="page-113-4"></span>11. Huang, G., Sun, Y., Liu, Z., Sedra, D., Weinberger, K.: Deep Networks with Stochastic Depth (2016), <https://arxiv.org/abs/1603.09382>
- <span id="page-113-9"></span>12. Isensee, F., Schell, M., Pflueger, I., Brugnara, G., Bonekamp, D., Neuberger, U., Wick, A., Schlemmer, H.P., Heiland, S., Wick, W., Bendszus, M., Maier-Hein, K.H., Kickingereder, P.: Automated brain extraction of multisequence MRI using artificial neural networks. Human Brain Mapping  $40(17)$ , 4952–4964 (aug 2019)

- <span id="page-114-11"></span>13. Jack, C.R., Bernstein, M.A., Fox, N.C., Thompson, P., Alexander, G., Harvey, D., Borowski, B., Britson, P.J., Whitwell, J.L., Ward, C., Dale, A.M., Felmlee, J.P., Gunter, J.L., Hill, D.L., Killiany, R., Schuff, N., Fox-Bosetti, S., Lin, C., Studholme, C., DeCarli, C.S., Krueger, G., Ward, H.A., Metzger, G.J., Scott, K.T., Mallozzi, R., Blezek, D., Levy, J., Debbins, J.P., Fleisher, A.S., Albert, M., Green, R., Bartzokis, G., Glover, G., Mugler, J., and, M.W.W.: The alzheimer's disease neuroimaging initiative (ADNI): MRI methods. Journal of Magnetic Resonance Imaging 27(4), 685–691 (2008)
- <span id="page-114-6"></span>14. Jang, J., Hwang, D.: M3T: three-dimensional Medical image classifier using Multiplane and Multi-slice Transformer. In: 2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 20686–20697. IEEE, New Orleans, LA, USA (Jun 2022)
- <span id="page-114-10"></span>15. LaMontagne, P.J., Benzinger, T.L., Morris, J.C., Keefe, S., Hornbeck, R., Xiong, C., Grant, E., Hassenstab, J., Moulder, K., Vlassenko, A.G., Raichle, M.E., Cruchaga, C., Marcus, D.: OASIS-3: Longitudinal Neuroimaging, Clinical, and Cognitive Dataset for Normal Aging and Alzheimer Disease. medRxiv (2019)
- <span id="page-114-1"></span>16. Lee, S.H., Lee, S., Song, B.C.: Vision Transformer for Small-Size Datasets (2021), <https://arxiv.org/abs/2112.13492>
- <span id="page-114-4"></span>17. Li, C., Cui, Y., Luo, N., Liu, Y., Bourgeat, P., Fripp, J., Jiang, T.: Trans-ResNet: Integrating Transformers and CNNs for Alzheimer's disease classification. In: 2022 IEEE 19th International Symposium on Biomedical Imaging (ISBI). pp. 1–5 (Mar 2022), iSSN: 1945-8452
- <span id="page-114-2"></span>18. Liu, Y., Sangineto, E., Bi, W., Sebe, N., Lepri, B., Nadai, M.D.: Efficient Training of Visual Transformers with Small Datasets (2021), [https://arxiv.org/abs/2106.](https://arxiv.org/abs/2106.03746) [03746](https://arxiv.org/abs/2106.03746)
- <span id="page-114-12"></span>19. Loshchilov, I., Hutter, F.: Decoupled Weight Decay Regularization (2019), [https://](https://arxiv.org/abs/1711.05101) [arxiv.org/abs/1711.05101](https://arxiv.org/abs/1711.05101)
- <span id="page-114-9"></span>20. Menze, B.H., Jakab, A., Bauer, S., Kalpathy-Cramer, J., Farahani, K., Kirby, J., et al.: The Multimodal Brain Tumor Image Segmentation Benchmark (BRATS). IEEE Transactions on Medical Imaging 34(10), 1993-2024 (Oct 2015)
- <span id="page-114-5"></span>21. Nirthika, R., Manivannan, S., Ramanan, A., Wang, R.: Pooling in convolutional neural networks for medical image analysis: a survey and an empirical study. Neural Computing and Applications  $34(7)$ , 5321–5347 (feb 2022)
- <span id="page-114-7"></span>22. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., Krueger, G., Sutskever, I.: Learning Transferable Visual Models From Natural Language Supervision (2021), [https://arxiv.org/abs/](https://arxiv.org/abs/2103.00020) [2103.00020](https://arxiv.org/abs/2103.00020)
- <span id="page-114-3"></span>23. Steiner, A., Kolesnikov, A., Zhai, X., Wightman, R., Uszkoreit, J., Beyer, L.: How to train your vit? data, augmentation, and regularization in vision transformers (2022), <https://arxiv.org/abs/2106.10270>
- <span id="page-114-8"></span>24. Touvron, H., Cord, M., Douze, M., Massa, F., Sablayrolles, A., Jégou, H.: Training data-efficient image transformers & distillation through attention. In: International conference on machine learning. pp. 10347–10357. PMLR (2021)
- <span id="page-114-0"></span>25. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention Is All You Need (2023), [https://arxiv.org/abs/1706.](https://arxiv.org/abs/1706.03762) [03762](https://arxiv.org/abs/1706.03762)
- <span id="page-114-13"></span>26. Zhou, L., Liu, H., Bae, J., He, J., Samaras, D., Prasanna, P.: Self Pre-training with Masked Autoencoders for Medical Image Classification and Segmentation (2023), <https://arxiv.org/abs/2203.05573>

Image /page/115/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a ribbon or bookmark shape inside. The text below the icon reads "Check for updates".

# *TrIND***: Representing Anatomical Trees by Denoising Diffusion of Implicit Neural Fields**

As[h](http://orcid.org/0000-0001-5040-7448)ish Sinha<sup>( $\boxtimes$ </sup>[\)](http://orcid.org/0000-0003-1395-6629) and Ghassan Hamarneh<sup>o</sup>

Medical Image Analysis Lab., School of Computing Science, Simon Fraser University, Burnaby, BC, Canada {ashish sinha,hamarneh}@sfu.ca

**Abstract.** Anatomical trees play a central role in clinical diagnosis and treatment planning. However, accurately representing anatomical trees is challenging due to their varying and complex topology and geometry. Traditional methods for representing tree structures, captured using medical imaging, while invaluable for visualizing vascular and bronchial networks, exhibit drawbacks in terms of limited resolution, flexibility, and efficiency. Recently, implicit neural representations (INRs) have emerged as a powerful tool for representing shapes accurately and efficiently. We propose a novel approach, *TrIND*, for representing anatomical trees using INR, while also capturing the distribution of a set of trees via denoising diffusion in the space of INRs. We accurately capture the intricate geometries and topologies of anatomical trees at any desired resolution. Through extensive qualitative and quantitative evaluation, we demonstrate high-fidelity tree reconstruction with arbitrary resolution yet compact storage, and versatility across anatomical sites and tree complexities. Our code is available [here.](https://github.com/sfu-mial/TreeDiffusion)

**Keywords:** Anatomical Tree · Vasculature · Bronchial Tree · Representation Learning · Neural Fields · Implicit Neural Representations · Denoising Diffusion

### **1 Introduction**

*Motivation for Studying Anatomical Trees.* Accurate extraction and analysis of anatomical trees, including vasculature and airways, is crucial for predicting fatal diseases like ischemic heart disease [\[37\]](#page-125-0) and aiding clinical tasks like surgical planning [\[30](#page-124-0)], computational fluid dynamics (CFD) [\[35\]](#page-125-1), and disease prognosis [\[1\]](#page-123-0). However, the application of extracted trees for such purposes may be hindered by their explicit representation as meshes or volumetric grids.

*Previous Works on Tree Representation.* Prior works have utilized discrete and explicit representations, such as medial axis [\[25\]](#page-124-1), minimal paths [\[14\]](#page-124-2), grammar [\[15](#page-124-3)], voxel grids [\[43](#page-125-2)], and meshes [\[39\]](#page-125-3), to represent anatomical trees.

Image /page/116/Figure/1 description: The image displays a two-by-two grid of diagrams illustrating different computational processes. The top left diagram, labeled (a) Per-instance optimization, shows a neural network being optimized and then flattened into a vector representation. The top right diagram, labeled (b) Isosurface extraction from INR, depicts an unflattened neural network representation being processed by a 'Marching Cubes' algorithm to generate a 3D isosurface. The bottom left diagram illustrates a diffusion model training and sampling process, represented by stacked rectangles and arrows. The bottom right section shows a collection of teal-colored, branching 3D structures, likely the output of the processes described in the upper diagrams.

(c) Learn tree-space via denoising diffusion (d) Generate novel tree structures

<span id="page-116-0"></span>**Fig. 1.** *TrIND* **overview**: (a) An INR is optimized for each sample in the training dataset, and then flattened to a 1D vector. (b) During inference, the INR is recovered from the flattened vector, followed by MC to extract the underlying signal. (c) The diffusion transformer takes the flattened vectors as input to model the diffusion process. After training, novel INRs can be sampled and used for downstream tasks via (b). (d) Novel tree structures visualized as mesh.

However, all these explicit representations have their own peculiar shortcomings, requiring complex ad-hoc machinery  $[12,28]$  $[12,28]$  $[12,28]$  to obtain a smooth surface essen-tial for vascular modeling [\[1\]](#page-123-0), and/or large memory footprint [\[19](#page-124-6)], up to  $O(n^3)$ for voxels, to preserve fine details. Although rule-based representations, such as L-system, offer some degree of control over the produced structures [\[27](#page-124-7)], the complexity of rules and parameters and the computational cost of generating and rendering the shapes limit their use in current medical applications. Therefore, there is a need for a continuous shape representation that is independent of spatial resolution, is memory efficient, and can easily be integrated into deep learning pipelines.

*Implicit Neural Representation (INR).* Recently, INRs (or neural fields, implicit fields) have been proposed to address the shortcomings of explicit representations, which use multi-layer perceptrons (MLP) to fit a continuous function that *implicitly* represents a signal of interest [\[21](#page-124-8),[23,](#page-124-9)[29\]](#page-124-10) as level sets. This approach not only learns a representation of arbitrary shapes with a small number of parameters while still achieving a high surface accuracy, but also supports reconstructions at any arbitrary resolution. To this end, we propose to use INRs for representing anatomical trees and demonstrate their efficiency and effectiveness in modeling simple and complex, vasculature and airways, in 2D and 3D, on both synthetic and real data.

*Generative Modeling of Tree Distributions.* While implicit representations and level sets have been widely used for segmentation  $[2,18]$  $[2,18]$  and 3D modeling  $[11,13]$  $[11,13]$  $[11,13]$  of vessel structures, there are limited works that address the modeling of the "shape space" of tree structures. Previous works that attempt to analyze and synthesize tree structures are either *model-based* or *data-driven*. *Model-based* approaches [\[8,](#page-123-2)[9](#page-123-3)[,33](#page-125-4)[,40](#page-125-5)] record the tree topology along with branch attributes [\[7](#page-123-4),[42\]](#page-125-6), *e.g.*, length and angle. However, for trees with diverse topologies (*e.g.*, different number of branches), a fixed-size representation no longer suffices, and altering the model size across trees impairs statistical analysis. *Data-driven* methods use generative modeling to represent the distribution of training data and sample the distribution to synthesize novel instances. GANs and VAEs were used to model the distribution of vessels [\[6,](#page-123-5)[38](#page-125-7)] but did not leverage the descriptive power of INRs as  $[1,23]$  $[1,23]$  $[1,23]$  do. Diffusion models  $[10]$  were employed in  $[4]$  to learn the distribution on latent VAE codes of shapes but required large training sets for faithful encoding. For a more faithful representation, INRs were used in [\[5\]](#page-123-7), though neither [\[4,](#page-123-6)[5\]](#page-123-7) modeled tubular structures or trees.

*Our Contributions.* To address the shortcomings of previous works, we are the first to make the following contributions: (1) we train INRs to achieve a faithful representation of complex anatomical trees and demonstrate their usage in segmenting trees from medical images; (2) we employ diffusion models on the space of INR-represented trees for learning tree distributions and generating plausible novel trees with complex topology; (3) we showcase the versatility of our approach in representing trees of varying dimensionality, complexity, and anatomy; and (4) we quantitatively assess our method's compactness representation and reconstruction accuracy, at arbitrarily high resolution.

## **2 Methodology**

*Overview.* We propose *TrIND* (pronounced as *Trendy*), a diffusion-based generative approach comprising two steps. First, we optimize an INR realized as an MLP by overfitting its parameters to each tree in a dataset so that each INR faithfully represents each tree as a neural occupancy field (Fig. [1a](#page-116-0)). Second, the optimized INRs are flattened into 1D vectors and used to train a denoising diffusion model (DDM) that captures the statistical distribution of the trees (Fig. [1b](#page-116-0)). The DDM is used to synthesize new INRs, via a reverse diffusion process (Fig. [1c](#page-116-0)), which represents the occupancy field (or a mesh, if marching cubes  $[17]$  $[17]$  is applied) of the tree (Fig. [1d](#page-116-0)).

Image /page/117/Figure/5 description: The image displays a two-stage process for generating novel neural representations. Stage 1 shows a mesh being sampled into a point cloud, which is then used to train an Implicit Neural Representation (INR) with parameters denoted by \u03b8. The INR predicts occupancy, and a loss function \u039b\_{inr} and its gradient \u2207\u039b\_{inr} are used to update the INR. Stage 2 illustrates a diffusion transformer model. It takes optimized INRs {\u03b8}\_{i=1}^N, noise, and time as input, processes them through multiple projection and transformer layers, and outputs a predicted \u03b8. This is followed by DDIM sampling, which involves a forward and reverse process to generate a novel \u03b8.

<span id="page-117-0"></span>Fig. 2. (a) Given a 3D mesh, sampled points, and GT occupancies (inside/outside), an INR  $(\theta)$  is optimized to fit the shape. (b) Optimized INRs are flattened to a 1D vector, fed to the transformer-based diffusion model  $\mathcal{D}(\phi)$ , and optimized to predict noise. Novel INRs can then be sampled from the trained  $\mathcal{D}(\phi)$  in the reverse process.

*Stage 1: Learning Per-sample INR.* We represent each tree, in a dataset  $\{S_i\}_{i=1}^N$  of N trees, as a neural occupancy field, or INR,  $f(x; \theta) : \mathbb{R}^3 \to [0, 1],$ modelled as an MLP and parameterized by  $\theta \in \mathbb{R}^D$ . Note that, unlike prior works [\[23,](#page-124-9)[41](#page-125-8)] that involve parameter sharing across the entire dataset, we optimize an INR separately for each tree sample. Given  $o_i(x)$ , the ground truth occupancy of x for  $S_i$ , we optimize  $\theta_i$  as:

$$
\underset{\theta_i}{\arg \min} \sum_{x_j \in \mathbb{R}^3} \| f_{\theta_i}(x_j) - o_i(x_j) \|_2. \tag{1}
$$

Similar to  $[21,22]$  $[21,22]$  $[21,22]$ , our MLP architecture is a fully-connected network with L layers, of hidden size D, and ReLU activation functions. Note that our network architecture is a design choice and can be replaced with others [\[29](#page-124-10),[34\]](#page-125-9), as stage 1 focuses on overfitting. To learn high-fidelity neural representations of anatomical trees, we follow [\[21](#page-124-8)] to first instantiate all training instances  $S_i$  in Lipschitz domain  $\Omega \in [-1, 1]$  and then adaptively sample points and groundtruth occupancies both inside and outside the 3D surface of  $S_i$ , to supervise the optimization of each MLP until convergence. Figure [2a](#page-117-0) illustrates this optimization process.

**Stage 2: Diffusion on INRs.** We train a diffusion model on the space of INRs, *i.e.*, the weights and biases  $\{\theta_i\}_{i=1}^N$ . Our transformer-based [\[36](#page-125-10)] diffusion model  $\mathcal{D}(\phi)$  is inspired by [\[5](#page-123-7)[,24](#page-124-17)], and takes the flattened 1D vectors of  $\theta_i$  as input. However, before passing the input to  $\mathcal{D}(\phi)$ , each  $\theta_i$  undergoes a *layerby-layer* decomposition into k tokens by MLPs [\[24\]](#page-124-17). This is essential due to the potential variation in the dimensionality of different layers in  $\theta_i$  and ensures correspondence of any element j across all trees  $\theta_i$ ,  $\forall i, j$ . During forward diffusion, we apply noise  $\eta_t$  at timestep t to each vector  $\theta_i$  to obtain a noisy vector  $\theta_i^*$ . Following [\[5](#page-123-7),[24\]](#page-124-17), the sinusoidal embedding of t along with  $\theta_i^*$  is fed to a linear projection layer, whose output is concatenated with a learnable positional encoding vector to obtain  $\mathcal G$ . The resultant  $\mathcal G$  is then passed to the transformer, which outputs denoised INRs  $\hat{\theta}_i$ . Parameters  $\phi$  of  $\mathcal{D}$  are optimized using:

$$
\arg\min_{\phi} \frac{1}{N} \sum_{i=1}^{N} \|\hat{\theta}_i - \theta_i\|_2.
$$
 (2)

After training, we employ DDIM [\[31\]](#page-124-18) sampling, *i.e.*, a reverse diffusion process, to sample new INRs by feeding-in noise as  $\hat{\theta}_i$  to  $\mathcal{D}(\phi)$ , and gradually denoising it. Figure [2b](#page-117-0) shows an illustration of the overall diffusion process.

### **3 Datasets, Results, and Implementation Details**

**Datasets.** We use the following datasets to evaluate our approach: (1) VascuSynth: 120 synthetic 3D vascular trees, ranging from 1 branch to complex trees with multiple branches and bifurcations  $[9]$  $[9]$ ; (2) IntRA: 103 3D meshes of intracranial vasculature extracted from MRA [\[39\]](#page-125-3); (3) BraTS: 40 3D MRI brain scans  $[20]$  $[20]$ ; (4) HaN-Seg: 20 segmentations of Head&Neck CTA scans  $[26]$ ; (5) DRIVE: 20 2D retinal fundus color images with vessel segmentation masks [\[32\]](#page-124-21); (6) EXACT: One CT scan of a bronchial tree  $[16]$  $[16]$ ; (7) WBMRA: One whole body MRA; and (8) CoW: One circle of Willis mesh. Our evaluation covers the following aspects.

Image /page/119/Figure/2 description: The image displays a comparison of different medical imaging datasets and their generated models. The top row shows generated models in light gray, while the bottom row shows generated models in a reddish-brown color. Each dataset is presented with its corresponding model and evaluation metrics. The datasets include VascuSynth, Bronchial Airways, IntRA, Retinal Vessel, Circle of Willis, Whole Body MRA, Bifurcation 2, Bifurcation 4, and Bifurcation 10. The evaluation metrics provided are CD (Compactness Descriptor) for VascuSynth (15.35), Bronchial Airways (13.79), and IntRA (14.70), and MSE (Mean Squared Error) for Retinal Vessel (0.90), Circle of Willis (0.06), and Whole Body MRA (0.36). The Bifurcation datasets are shown with their models but without specific numerical metrics in this view.

<span id="page-119-1"></span>(a) Various anatomical sites and imaging modalities (b) Various complexities

**Fig. 3. Versatility**: Visualization of different synthetic and real anatomical trees represented as an INR from various medical imaging modalities and organs. We normalize all shapes to  $[-1, 1]$  and images to  $[0, 1]$  to report the reconstruction error using MSE and CD ( $\times$ 10<sup>-3</sup>) between ground truth and the underlying signal extracted from INR.

Image /page/119/Figure/5 description: The image displays two line graphs on the left and four 3D scatter plots on the right. The line graphs plot 'Error' on the y-axis against 'Size (MB)' on the x-axis, with three lines representing different 'Loss Types': INR CD (blue circles), Mesh CD (orange squares), and Volume L1 (green triangles). The first graph shows a general decrease in error with increasing size for Mesh CD and Volume L1, while INR CD remains relatively stable. The second graph shows a similar trend but with different error values. The 3D scatter plots visualize reconstructed structures at different decimation levels: GT 101^3 (blue), Decimated 52^3 (red), Decimated 34^3 (green), and Decimated 23^3 (gray). Each plot shows a network-like structure, with zoomed-in boxes highlighting specific regions. The decimation level clearly affects the density and connectivity of the reconstructed structures, with higher decimation leading to sparser and more fragmented representations.

<span id="page-119-0"></span>**Fig. 4. Compression** *vs* **Reconstruction Accuracy**: (Left) We decimate/downscale the mesh/volume to occupy  $\approx$  same memory space as INR, and report the reconstruction error using Chamfer distance (CD) and edge length loss (Edge) for meshes and INRs, and  $L_1$  and  $L_2$  for volumes. Note the higher error for meshes and volumes w.r.t INRs for the same storage. (Right) Illustration of GT and downsampled volumes. Notice the disconnected components.

*Fidelity and Compactness.* Following [\[21](#page-124-8)], we use Chamfer distance (CD) to evaluate the fidelity of INRs in representing anatomical trees and summarize it in Tables [1](#page-120-0) and [2.](#page-121-0) Figure [4](#page-119-0) shows that we can achieve higher reconstruction accuracy with a smaller memory footprint. For example, as seen in Table [2](#page-121-0) we need 68 MB for volumes and 12 MB for meshes compared to INRs that only occupy 0.75 MB and 0.63 MB, respectively, offering  $\approx 90 \times$  and  $\approx 19 \times$  compression, respectively, with minimal loss of reconstruction accuracy. Similarly, Table [1](#page-120-0) shows that  $10k$  parameters of INR are enough to encode the geometry of a 128<sup>3</sup> volume of IntRA, resulting in  $\approx 220 \times$  compression.

<span id="page-120-0"></span>**Table 1.** Quantitative comparison of reconstruction accuracy and compression ratio on the VascuSynth (**V**) [\[9](#page-123-3)] and IntRA (**I**) [\[39](#page-125-3)] for different configurations of our MLP architecture. CD denotes chamfer distance (10*−*<sup>3</sup>) between the reconstructed mesh and the ground truth, whereas compression denotes the ratio between size of INR and size of the original input modality, *i.e.*, raw volume. ↑ denotes higher is better and ↓ lower is better.

|          | $L=1$                              |      |      |                        |      |                  | $L=3$                                |       |  |                          |  | $L=5$            |                                      |        |  |                            |  |            |
|----------|------------------------------------|------|------|------------------------|------|------------------|--------------------------------------|-------|--|--------------------------|--|------------------|--------------------------------------|--------|--|----------------------------|--|------------|
|          |                                    |      |      | Compression (1) CD (1) |      |                  |                                      |       |  | Compression $(†) CD ( )$ |  |                  |                                      |        |  | Compression $(†)$ CD $(1)$ |  |            |
| D        | $\#$ Params (M) $ Size_{irr}$ (MB) |      | v    |                        | V    |                  | $\#$ Params (M) $ Size_{inv}$ (MB) V |       |  |                          |  |                  | $\#$ Params (M)Size <sub>inr</sub> ( | (MB) V |  |                            |  |            |
| 64       | 0.01                               | 0.03 |      | 27.05 223.57           |      | 12.95 29.84 0.03 |                                      | 0.10  |  | 9.47 78.25               |  | 8.65 15.48 0.04  |                                      | 0.16   |  | 5.73 47.37                 |  | 8.74 21.90 |
|          | 128 0.03                           | 0.13 | 7.32 | 60.50                  |      | 10.36 24.21 0.10 |                                      | 0.38  |  | 2.49 20.55               |  | 7.09 8.33 0.17   |                                      | 0.63   |  | 1.50 12.37                 |  | 7.99 17.85 |
| 256 0.13 |                                    | 0.51 | 1.88 | 15.53                  | 9.24 | 18.190.40        |                                      | 1.51  |  | 0.61, 5.06               |  | 6.46 7.48 0.66   |                                      | 2.51   |  | $0.38 \, 3.12$             |  | 7.07 11.33 |
| 512 0.53 |                                    | 2.01 | 0.47 | 3.86                   |      | 10.24 24.66 1.58 |                                      | 6.02  |  | 0.08 1.29                |  | 7.40 19.99 2.63  |                                      | 10.03  |  | $0.09 \, 0.77$             |  | 6.96 12.50 |
|          | 10242.10                           | 8.03 | 0.04 | 0.96                   |      | 11.37 40.06 6.30 |                                      | 24.04 |  | $0.04 \, 0.32$           |  | 7.37 15.87 10.50 |                                      | 42.48  |  | $0.02 \, 0.19$             |  | 7.64 17.26 |

*Versatility.* We assess the effectiveness of INRs in representing anatomical trees from various medical image modalities. Figure [3a](#page-119-1) and Table [2](#page-121-0) qualitatively and quantitatively show the adaptability of INRs in modeling DRIVE vessel masks, 3D meshes, CTA and MRA volumes from VascuSynth, IntRA, EXACT and CoW. Figure [3b](#page-119-1) depicts how the same MLP architecture can represent trees of varying complexity.

*Arbitrary Resolution.* Achieving high-quality mesh and volume reconstruction is crucial for the accurate representation of vasculature, *e.g.*, for fluid flow simulation. Figure [5](#page-120-1) illustrates how INRs can reconstruct anatomical trees from both IntRA and VascuSynth at various resolutions without the need for adjusting the model size or retraining.

Image /page/120/Figure/5 description: This figure displays a comparison of 3D models at different resolutions and representations. Part (a) shows a coral-like 3D object, labeled (i) 1x, with a purple bounding box highlighting a section. This section is then zoomed into in part (ii) 2x, with an orange bounding box. Further zooms are shown in part (iii) 4x with a teal bounding box, and part (iv) 8x. Each part (ii), (iii), and (iv) shows two views of the zoomed-in section. Part (b) presents close-ups of the models from part (iv), showing both the wireframe (Edges) and the shaded surface (Surface) representations for two different sections of the object. The top row of part (b) corresponds to the top view in part (iv), and the bottom row corresponds to the bottom view in part (iv).

<span id="page-120-1"></span>**Fig. 5. Arbitrary Resolution**: (a) Comparison of 2x, 4x, and 8x zoom on an IntRA sample represented as a volumetric grid (top) and INR (bottom). The resolution for each sub-figure is shown on top as volume<sup>3</sup>/ $INR<sup>3</sup>$ . Notice the smoothness of the surface even at 8x zoom. (b) Zoomed-in regions of a VascuSynth mesh reconstructed from INRs and ground truth at different mesh resolutions displayed using faces and edges.

Image /page/121/Figure/1 description: The image displays four plots labeled (a), (b), (c), and (d). Plot (a) is a heatmap showing a matrix with values ranging from 0.01 to 0.03, with darker colors indicating higher values. The axes are labeled '# Bifurcations' from 1 to 12. Plot (b) is a scatter plot with various shapes colored according to a legend that maps numbers 1 through 12 to different colors, ranging from purple to red. Each shape is enclosed in a bounding box, with some boxes colored red and others blue. Plots (c) and (d) are histograms with superimposed probability density curves. Plot (c) is titled 'Tortuosity' on the x-axis and 'Probability' on the y-axis, showing distributions for 'GT' (pink) and 'Generated' (red). The x-axis ranges from 2.5 to 17.5. Plot (d) is titled 'Total Length' on the x-axis and 'Probability' on the y-axis, also showing distributions for 'GT' (pink) and 'Generated' (red). The x-axis ranges from 1 to 7.

<span id="page-121-1"></span>**Fig. 6. Tree Statistics** computed on VascuSynth. (a) Distance between INRs of trees with similar bifurcations is low and increases as the complexity increases. (b) t-SNE plot of the space of trees as INRs. Histograms of (c) tortuosity and (d) total length for ground truth and generated trees.

<span id="page-121-0"></span>**Table 2.** Quantitative results on tree structures present in different medical imaging modalities represented using INRs. We report the relative percentage error (%) between the reconstructed signal and ground ter;  $\downarrow$ : lower is better. For 1truth.

<span id="page-121-2"></span>**Table 3.** Quantitative results for novel tree generation on VascuSynth. ↑: higher is bet-NNA, 50% is ideal.

| Modality          | Rel. Error (%) | Input Size (MB)   | INR Size (MB)        |
|-------------------|----------------|-------------------|----------------------|
| DRIVE (RGB) [32]  | 0.018          | $0.37 pm 0.0055$ | 0.066↓ $	imes$ 5.60  |
| DRIVE (Mask) [32] | 1.204          | $0.02 pm 0.0013$ | 0.003↓ $	imes$ 6.60  |
| BraTS [20]        | 0.039          | $68.11 pm 0.00$  | 0.753↓ $	imes$ 90.45 |
| HAN-Seg [26]      | 5.627          | $12.1 pm 1.55$   | 0.630↓ $	imes$ 19.20 |

**Space of INR-based Trees.** Once trees are represented via INRs, we study the space of INRs. The t-SNE plot in Fig. [6b](#page-121-1) illustrates how trees with similar number of bifurcations, *i.e.*, topological complexities, tend to cluster in the 2D t-SNE space. However, we observe cases where trees with the same number of branches are distant, which may be due to how t-SNE projects high-dimensional samples to 2D thus distort inter-sample distances. Therefore, to further investigate, we compute the matrix of  $L_2$  distances between INRs of VascuSynth trees with i and j bifurcations, where  $i, j \in \{1, ..., 12\}$  (Fig. [6a](#page-121-1)). The reported distances are the average across 10 trees per bifurcation count. We notice low ( $\approx 0$ ) values along the diagonal, indicating that trees with the same branch count have similar INRs. As the difference in branch count increases, the distance increases, visible by the color gradient in Fig. [6a](#page-121-1). Furthermore, for a constant difference in branch count  $(e.g., |3-4| = 1 = |8-9|)$ , the dissimilarity is more pronounced with larger branch counts, despite the numerical difference being the same.

*Tree Synthesis.* Training a DDM on INR-based trees results in a model that captures the tree distribution and can be sampled using DDIM [\[31](#page-124-18)] to generate novel trees. First, in Fig. [7,](#page-122-0) we show qualitative results, generated by our model, of novel INR-based trees with varied complexities. We used samples specifically from VascuSynth [\[9\]](#page-123-3), and not IntRA [\[39](#page-125-3)], due to the former's diverse tree topologies. In contrast, synthesized trees from IntRA [\[39](#page-125-3)] would look visually similar, as all depict the brain's circle of Willis without easily observable differences. Next, since the evaluation of unconditional generation of tree-like structures

Image /page/122/Picture/1 description: The image displays a collection of 16 small, teal-colored, abstract sculptures arranged in two rows of eight. Each sculpture appears to be made of interconnected, branching, rod-like elements, resembling coral or stylized trees. The sculptures are presented against a plain white background, with subtle shadows cast beneath them, suggesting they are lit from above and slightly to the front. The arrangement is a grid-like presentation of variations on a theme, with each sculpture having a unique configuration of branches and angles.

**Fig. 7. Tree Synthesis.** Some novel INRs sampled from the diffusion model, visualized as meshes.

<span id="page-122-0"></span>can be challenging due to lack of direct correspondence to ground truth data, we follow [\[4](#page-123-6)[,5](#page-123-7)] to quantitatively assess the samples generated using diffusion. Specifically, we use minimum matching distance (MMD), coverage (COV), and 1-nearest neighbor accuracy (1-NNA) to measure quality, diversity, and plausibility, respectively. Moreover, since the generations are random, we run the evaluation 3 times and report the mean values in Table [3.](#page-121-2) Additionally, we follow [\[6](#page-123-5),[38\]](#page-125-7) and report vessel-based metrics in Figs. [6c](#page-121-1) and [6d](#page-121-1), where we see high similarities in tortuosity and centerline length histograms between ground truth training data and synthesized trees.

*INR-based Image Segmentation.* We present two proof-of-concept experiments on how INR representation is leveraged to perform vessel tree segmentation. Figure [8](#page-122-1) depicts the evolution of INR-represented segmentation masks as they gradually fit to target vasculature in both CoW and WBMRA.

Image /page/122/Picture/5 description: The image displays a grid of medical scans, with two rows and eight columns. The top row, labeled "Circle of Willis," shows detailed vascular structures in the brain, with some images highlighted in yellow. The bottom row, labeled "Whole Body MRA," presents full-body scans of the vascular system, also with several images highlighted in yellow. The scans appear to be magnetic resonance angiography (MRA) images, illustrating blood vessels throughout the body and specifically in the brain.

**Fig. 8. Segmentation**. The evolution of INR towards segmenting two vessel trees. Analogous to Mumford-Shah based segmentation [\[3\]](#page-123-8), we use a piecewise-constant version of the INR (via simple thresholding) as it is being optimized to faithfully represent the original image.

<span id="page-122-1"></span>*Implementation Details.* We used PyTorch and ADAM optimizer with learning rate  $\alpha = 10^{-3}$  for all experiments. To fit INRs to trees, we optimize the MLP architecture in [\[21](#page-124-8)] for max  $5k$  iterations on 12GB NVIDIA GeForce GTX TITAN X GPU, taking ∼1 min/tree. We train the diffusion model (128-D hidden size, 4 layers, and 4 self-attention modules, as in [\[24\]](#page-124-17)) on 48GB NVIDIA A40 GPU for 6k epochs, with batch size 8,  $0.9 \times \alpha$  decay per 200 epoch, 1k diffusion time-steps, linear noise scheduler  $\in [10^{-4}, 10^{-2}]$ , taking  $\approx 3$  days.

## **4 Conclusion**

We presented *TrIND*, the first work to use implicit neural fields for faithful representation of topologically-complex anatomical trees and to learn their distribution via training a diffusion model in the space of neural fields. We demonstrated qualitatively and quantitatively the advantages of our method: versatility (*e.g.*, representing 2D/3D; vascular/airway; simple/complex topology), lower memory footprint while achieving highly accurate reconstructions at arbitrary high resolutions; synthesis of plausible trees; and proof-of-concept application to segmentation of medical images. Further, our representation is amenable to integration into deep learning pipelines and can be easily transformed into other shape representations. Future work may include integrating our method into more advanced deep segmentation pipelines while encoding semantic annotations of tree parts.

**Acknowledgement.** The authors thank Paula Feldman for providing evaluation scripts for tortuosity and centerline length. This research was supported by the Natural Sciences and Engineering Research Council of Canada (NSERC) and computational resources from the Digital Research Alliance of Canada and NVIDIA Corporation.

**Disclosure of Interests.** The authors declare no competing interests.

## **References**

- <span id="page-123-0"></span>1. Alblas, D., et al.: Going off-grid: Continuous implicit neural representations for 3D vascular modeling. In: International Workshop on Statistical Atlases and Computational Models of the Heart. pp. 79–90. Springer (2022)
- <span id="page-123-1"></span>2. van Bemmel, C.M., et al.: Level-set based carotid artery segmentation for stenosis grading. In: MICCAI. pp. 36–43. Springer (2002)
- <span id="page-123-8"></span>3. Chan, T.F., et al.: Active contours without edges. IEEE Transactions of Image Processing **10**(2), 266–277 (2001)
- <span id="page-123-6"></span>4. Chou, G., et al.: Diffusion-SDF: Conditional generative modeling of signed distance functions. In: CVPR. pp. 2262–2272 (2023)
- <span id="page-123-7"></span>5. Erkoç, Z., et al.: HyperDiffusion: Generating implicit neural fields with weightspace diffusion. arXiv preprint [arXiv:2303.17015](http://arxiv.org/abs/2303.17015) (2023)
- <span id="page-123-5"></span>6. Feldman, P., et al.: VesselVAE: Recursive variational autoencoders for 3D blood vessel synthesis. In: MICCAI. pp. 67–76. Springer (2023)
- <span id="page-123-4"></span>7. Feragen, A., et al.: Toward a theory of statistical tree-shape analysis. IEEE TPAMI **35**(8), 2008–2021 (2012)
- <span id="page-123-2"></span>8. Galarreta-Valverde, M.A., et al.: Three-dimensional synthetic blood vessel generation using stochastic L-systems. In: Medical Imaging 2013: Image Processing. vol. 8669, pp. 414–419. SPIE (2013)
- <span id="page-123-3"></span>9. Hamarneh, G., et al.: VascuSynth: Simulating vascular trees for generating volumetric image data with ground-truth segmentation and tree analysis. Computerized medical imaging and graphics **34**(8), 605–616 (2010)

- <span id="page-124-14"></span>10. Ho, J., et al.: Denoising diffusion probabilistic models. NeurIPS **33**, 6840–6851 (2020)
- <span id="page-124-12"></span>11. Hong, Q., et al.: High-quality vascular modeling and modification with implicit extrusion surfaces for blood flow computations. Computer Methods and Programs in Biomedicine **196**, 105598 (2020)
- <span id="page-124-4"></span>12. Hu, S.M., et al.: Subdivision-based mesh convolution networks. ACM (TOG) **41**(3), 1–16 (2022)
- <span id="page-124-13"></span>13. Kretschmer, J., et al.: Interactive patient-specific vascular modeling with sweep surfaces. IEEE TVCG **19**(12), 2828–2837 (2013)
- <span id="page-124-2"></span>14. Li, H., et al.: Vessels as 4-D curves: Global minimal 4-D paths to extract 3-D tubular surfaces and centerlines. IEEE TMI **26**(9), 1213–1223 (2007)
- <span id="page-124-3"></span>15. Lindenmayer, A., et al.: Mathematical models for cellular interactions in development I. filaments with one-sided inputs. Journal of theoretical biology **18**(3), 280–299 (1968)
- <span id="page-124-22"></span>16. Lo, P., et al.: Extraction of airways from CT (EXACT'09). IEEE TMI **31**(11), 2093–2107 (2012)
- <span id="page-124-15"></span>17. Lorensen, W.E., et al.: Marching cubes: A high resolution 3D surface construction algorithm. In: Seminal graphics: pioneering efforts that shaped the field, pp. 347– 353 (1998)
- <span id="page-124-11"></span>18. Lorigo, L.M., et al.: CURVES: Curve evolution for vessel segmentation. MedIA **5**(3), 195–206 (2001)
- <span id="page-124-6"></span>19. Maturana, D., et al.: VoxNet: A 3D convolutional neural network for real-time object recognition. In: 2015 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). pp. 922–928. IEEE (2015)
- <span id="page-124-19"></span>20. Menze, B.H., et al.: The multimodal brain tumor image segmentation benchmark (BRATS). IEEE TMI **34**(10), 1993–2024 (2014)
- <span id="page-124-8"></span>21. Mescheder, L., et al.: Occupancy networks: Learning 3D reconstruction in function space. In: CVPR. pp. 4460–4470 (2019)
- <span id="page-124-16"></span>22. Niemeyer, M., et al.: Differentiable volumetric rendering: Learning implicit 3D representations without 3D supervision. In: CVPR. pp. 3504–3515 (2020)
- <span id="page-124-9"></span>23. Park, J.J., et al.: DeepSDF: Learning continuous signed distance functions for shape representation. In: CVPR. pp. 165–174 (2019)
- <span id="page-124-17"></span>24. Peebles, W., et al.: Learning to learn with generative models of neural network checkpoints. arXiv preprint [arXiv:2209.12892](http://arxiv.org/abs/2209.12892) (2022)
- <span id="page-124-1"></span>25. Pizaine, G., et al.: Implicit medial representation for vessel segmentation. In: Medical Imaging 2011: Image Processing. vol. 7962, pp. 1184–1190. SPIE (2011)
- <span id="page-124-20"></span>26. Podobnik, G., et al.: HaN-Seg: The head and neck organ-at-risk CT and MR segmentation dataset. Medical physics **50**(3), 1917–1927 (2023)
- <span id="page-124-7"></span>27. Prusinkiewicz, P., et al.: Synthetic topiary. In: Annual Conference Series. ACM SIGGRAPH, Addison Wesley. vol. 10 (1994)
- <span id="page-124-5"></span>28. Qi, C.R., et al.: Volumetric and multi-view CNNs for object classification on 3D data. In: CVPR. pp. 5648–5656 (2016)
- <span id="page-124-10"></span>29. Sitzmann, V., et al.: Implicit neural representations with periodic activation functions. NeurIPS **33**, 7462–7473 (2020)
- <span id="page-124-0"></span>30. Sobocinski, J., et al.: The benefits of EVAR planning using a 3D workstation. European Journal of Vascular and Endovascular Surgery **46**(4), 418–423 (2013)
- <span id="page-124-18"></span>31. Song, J., et al.: Denoising diffusion implicit models. arXiv preprint [arXiv:2010.02502](http://arxiv.org/abs/2010.02502) (2020)
- <span id="page-124-21"></span>32. Staal, J., et al.: Ridge-based vessel segmentation in color images of the retina. IEEE TMI **23**(4), 501–509 (2004)

- <span id="page-125-4"></span>33. Talou, G.D.M., et al.: Adaptive constrained constructive optimisation for complex vascularisation processes. Scientific Reports **11**(1), 6180 (2021)
- <span id="page-125-9"></span>34. Tancik, M., et al.: Fourier features let networks learn high frequency functions in low dimensional domains. Advances in Neural Information Processing Systems **33**, 7537–7547 (2020)
- <span id="page-125-1"></span>35. Tran, K., et al.: Patient-specific computational flow modelling for assessing hemodynamic changes following fenestrated endovascular aneurysm repair. JVS-vascular science **2**, 53–69 (2021)
- <span id="page-125-10"></span>36. Vaswani, A., et al.: Attention is all you need. NeurIPS **30** (2017)
- <span id="page-125-0"></span>37. WHO, et al.: Global health estimates: Leading causes of death (2019)
- <span id="page-125-7"></span>38. Wolterink, J.M., et al.: Blood vessel geometry synthesis using generative adversarial networks. arXiv preprint [arXiv:1804.04381](http://arxiv.org/abs/1804.04381) (2018)
- <span id="page-125-3"></span>39. Yang, X., et al.: IntrA: 3D intracranial aneurysm dataset for deep learning. In: CVPR (2020)
- <span id="page-125-5"></span>40. Zamir, M., et al.: Arterial branching within the confines of fractal L-system formalism. The Journal of general physiology **118**(3), 267–276 (2001)
- <span id="page-125-8"></span>41. Zhang, B., et al.: 3DShape2VecSet: A 3D shape representation for neural fields and generative diffusion models. arXiv preprint [arXiv:2301.11445](http://arxiv.org/abs/2301.11445) (2023)
- <span id="page-125-6"></span>42. Zhao, M., et al.: Leveraging tree statistics for extracting anatomical trees from 3D medical images. In: CRV. pp. 131–138. IEEE (2017)
- <span id="page-125-2"></span>43. Zhao, M., et al.: Tree-LSTM: using LSTM to encode memory in anatomical tree prediction from 3D images. In: MICCAI Workshop (MLMI). pp. 637–645. Springer (2019)