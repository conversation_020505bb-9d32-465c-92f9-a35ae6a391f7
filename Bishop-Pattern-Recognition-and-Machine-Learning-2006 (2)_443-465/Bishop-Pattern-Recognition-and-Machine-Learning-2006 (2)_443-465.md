Image /page/0/Picture/0 description: The image is a title slide for a presentation or document. The number 9 is prominently displayed in red at the top. Below it, the text "Mixture Models" is written in large black font, followed by "and EM" in slightly smaller black font. The background is a blurred, abstract pattern resembling rippling water or molten metal with golden and silver hues.

If we define a joint distribution over observed and latent variables, the corresponding distribution of the observed variables alone is obtained by marginalization. This allows relatively complex marginal distributions over observed variables to be expressed in terms of more tractable joint distributions over the expanded space of observed and latent variables. The introduction of latent variables thereby allows complicated distributions to be formed from simpler components. In this chapter, we shall see that mixture distributions, such as the Gaussian mixture discussed in Section 2.3.9, can be interpreted in terms of discrete latent variables. Continuous latent variables will form the subject of Chapter 12.

As well as providing a framework for building more complex probability distributions, mixture models can also be used to cluster data. We therefore begin our discussion of mixture distributions by considering the problem of finding clusters in a set of data points, which we approach first using a nonprobabilistic technique *Section 9.1* called the K-means algorithm (<PERSON>, 1982). Then we introduce the latent variable

Section 9

*Section 9.3*

*Section 9.4*

view of mixture distributions in which the discrete latent variables can be interpreted *Section 9.2* as defining assignments of data points to specific components of the mixture. A general technique for finding maximum likelihood estimators in latent variable models is the expectation-maximization (EM) algorithm. We first of all use the Gaussian mixture distribution to motivate the EM algorithm in a fairly informal way, and then *Section 9.3* we give a more careful treatment based on the latent variable viewpoint. We shall see that the K-means algorithm corresponds to a particular nonprobabilistic limit of *Section 9.4* EM applied to mixtures of Gaussians. Finally, we discuss EM in some generality.

> Gaussian mixture models are widely used in data mining, pattern recognition, machine learning, and statistical analysis. In many applications, their parameters are determined by maximum likelihood, typically using the EM algorithm. However, as we shall see there are some significant limitations to the maximum likelihood approach, and in Chapter 10 we shall show that an elegant Bayesian treatment can be given using the framework of variational inference. This requires little additional computation compared with EM, and it resolves the principal difficulties of maximum likelihood while also allowing the number of components in the mixture to be inferred automatically from the data.

# **9.1.** K**-means Clustering**

We begin by considering the problem of identifying groups, or clusters, of data points in a multidimensional space. Suppose we have a data set  $\{x_1, \ldots, x_N\}$  consisting of <sup>N</sup> observations of a random <sup>D</sup>-dimensional Euclidean variable **x**. Our goal is to partition the data set into some number  $K$  of clusters, where we shall suppose for the moment that the value of  $K$  is given. Intuitively, we might think of a cluster as comprising a group of data points whose inter-point distances are small compared with the distances to points outside of the cluster. We can formalize this notion by first introducing a set of D-dimensional vectors  $\mu_k$ , where  $k = 1, \ldots, K$ , in which  $\mu_k$  is a prototype associated with the  $k^{\text{th}}$  cluster. As we shall see shortly, we can think of the  $\mu_k$  as representing the centres of the clusters. Our goal is then to find an assignment of data points to clusters, as well as a set of vectors  $\{\mu_k\}$ , such that the sum of the squares of the distances of each data point to its closest vector  $\mu_k$ , is a minimum.

It is convenient at this point to define some notation to describe the assignment of data points to clusters. For each data point  $x_n$ , we introduce a corresponding set of binary indicator variables  $r_{nk} \in \{0, 1\}$ , where  $k = 1, \ldots, K$  describing which of the K clusters the data point  $x_n$  is assigned to, so that if data point  $x_n$  is assigned to cluster k then  $r_{nk} = 1$ , and  $r_{nj} = 0$  for  $j \neq k$ . This is known as the 1-of-K coding scheme. We can then define an objective function, sometimes called a *distortion measure*, given by

$$
J = \sum_{n=1}^{N} \sum_{k=1}^{K} r_{nk} ||\mathbf{x}_n - \boldsymbol{\mu}_k||^2
$$
(9.1)

which represents the sum of the squares of the distances of each data point to its

assigned vector  $\mu_k$ . Our goal is to find values for the  $\{r_{nk}\}\$  and the  $\{\mu_k\}\$  so as to minimize J. We can do this through an iterative procedure in which each iteration involves two successive steps corresponding to successive optimizations with respect to the  $r_{nk}$  and the  $\mu_k$ . First we choose some initial values for the  $\mu_k$ . Then in the first phase we minimize J with respect to the  $r_{nk}$ , keeping the  $\mu_k$  fixed. In the second phase we minimize J with respect to the  $\mu_k$ , keeping  $r_{nk}$  fixed. This two-stage optimization is then repeated until convergence. We shall see that these two stages of updating  $r_{nk}$  and updating  $\mu_k$  correspond respectively to the E (expectation) and *Section 9.4* M (maximization) steps of the EM algorithm, and to emphasize this we shall use the terms E step and M step in the context of the K-means algorithm.

> Consider first the determination of the  $r_{nk}$ . Because J in (9.1) is a linear function of  $r_{nk}$ , this optimization can be performed easily to give a closed form solution. The terms involving different  $n$  are independent and so we can optimize for each *n* separately by choosing  $r_{nk}$  to be 1 for whichever value of k gives the minimum value of  $\|\mathbf{x}_n - \boldsymbol{\mu}_k\|^2$ . In other words, we simply assign the  $n^{\text{th}}$  data point to the closest cluster centre. More formally, this can be expressed as

$$
r_{nk} = \begin{cases} 1 & \text{if } k = \arg \min_j ||\mathbf{x}_n - \boldsymbol{\mu}_j||^2 \\ 0 & \text{otherwise.} \end{cases}
$$
(9.2)

Now consider the optimization of the  $\mu_k$  with the  $r_{nk}$  held fixed. The objective function J is a quadratic function of  $\mu_k$ , and it can be minimized by setting its derivative with respect to  $\mu_k$  to zero giving

$$
2\sum_{n=1}^{N} r_{nk}(\mathbf{x}_n - \boldsymbol{\mu}_k) = 0
$$
\n(9.3)

which we can easily solve for  $\mu_k$  to give

$$
\mu_k = \frac{\sum_n r_{nk} \mathbf{x}_n}{\sum_n r_{nk}}.
$$
\n(9.4)

The denominator in this expression is equal to the number of points assigned to cluster k, and so this result has a simple interpretation, namely set  $\mu_k$  equal to the mean of all of the data points  $x_n$  assigned to cluster k. For this reason, the procedure is known as the K*-means* algorithm.

The two phases of re-assigning data points to clusters and re-computing the cluster means are repeated in turn until there is no further change in the assignments (or until some maximum number of iterations is exceeded). Because each phase reduces *Exercise 9.1* the value of the objective function *J*, convergence of the algorithm is assured. However, it may converge to a local rather than global minimum of J. The convergence properties of the K-means algorithm were studied by MacQueen (1967).

*Appendix A* The *K*-means algorithm is illustrated using the Old Faithful data set in Figure 9.1. For the purposes of this example, we have made a linear re-scaling of the data, known as *standardizing*, such that each of the variables has zero mean and unit standard deviation. For this example, we have chosen  $K = 2$ , and so in this

*Section 9.4*

*Exercise 9.1*

*Appendix A*

Image /page/3/Figure/1 description: This image displays a 3x3 grid of scatter plots, illustrating the steps of the k-means algorithm. Each plot shows data points colored blue and red, with blue and red crosses representing cluster centroids. A magenta line in some plots indicates a decision boundary. Plot (a) shows initial data points and centroids. Plots (b) through (h) show the algorithm's progression, with points reassigning to clusters and centroids updating. Plot (i) shows the final clustered data with updated centroids. The axes in each plot range from -2 to 2 for both x and y coordinates.

Figure 9.1 Illustration of the K-means algorithm using the re-scaled Old Faithful data set. (a) Green points denote the data set in a two-dimensional Euclidean space. The initial choices for centres  $\mu_1$  and  $\mu_2$  are shown by the red and blue crosses, respectively. (b) In the initial E step, each data point is assigned either to the red cluster or to the blue cluster, according to which cluster centre is nearer. This is equivalent to classifying the points according to which side of the perpendicular bisector of the two cluster centres, shown by the magenta line, they lie on. (c) In the subsequent M step, each cluster centre is re-computed to be the mean of the points assigned to the corresponding cluster. (d)–(i) show successive E and M steps through to final convergence of the algorithm.

**Figure 9.2** Plot of the cost function J given by (9.1) after each E step (blue points) and M step (red points) of the  $K$ means algorithm for the example shown in Figure 9.1. The algorithm has converged after the third M step, and the final EM cycle produces no changes in either the assignments or the prototype vectors.

Image /page/4/Figure/2 description: A line graph shows the value of J on the y-axis against a numerical scale on the x-axis from 0 to 4. The line starts at approximately 1050 at x=0, then drops sharply to approximately 300 at x=1. It continues to decrease to approximately 150 at x=1.5, then to approximately 100 at x=2. From x=2 onwards, the line remains relatively flat, hovering around 100. There are data points marked with blue circles at x=0, x=1.5, x=2.5, and x=3.5. There are data points marked with red circles at x=1, x=2, x=3, and x=4. The y-axis is labeled with values 0, 500, and 1000.

case, the assignment of each data point to the nearest cluster centre is equivalent to a classification of the data points according to which side they lie of the perpendicular bisector of the two cluster centres. A plot of the cost function  $J$  given by (9.1) for the Old Faithful example is shown in Figure 9.2.

Note that we have deliberately chosen poor initial values for the cluster centres so that the algorithm takes several steps before convergence. In practice, a better initialization procedure would be to choose the cluster centres  $\mu_k$  to be equal to a random subset of  $K$  data points. It is also worth noting that the  $K$ -means algorithm itself is often used to initialize the parameters in a Gaussian mixture model before *Section 9.2.2* applying the EM algorithm.

> A direct implementation of the K-means algorithm as discussed here can be relatively slow, because in each E step it is necessary to compute the Euclidean distance between every prototype vector and every data point. Various schemes have been proposed for speeding up the K-means algorithm, some of which are based on precomputing a data structure such as a tree such that nearby points are in the same subtree (Ramasubramanian and Paliwal, 1990; Moore, 2000). Other approaches make use of the triangle inequality for distances, thereby avoiding unnecessary distance calculations (Hodgson, 1998; Elkan, 2003).

So far, we have considered a batch version of  $K$ -means in which the whole data set is used together to update the prototype vectors. We can also derive an on-line *Section 2.3.5* stochastic algorithm (MacQueen, 1967) by applying the Robbins-Monro procedure to the problem of finding the roots of the regression function given by the derivatives *Exercise* 9.2 of *J* in (9.1) with respect to  $\mu_k$ . This leads to a sequential update in which, for each data point  $x_n$  in turn, we update the nearest prototype  $\mu_k$  using

$$
\mu_k^{\text{new}} = \mu_k^{\text{old}} + \eta_n(\mathbf{x}_n - \mu_k^{\text{old}})
$$
\n(9.5)

where  $\eta_n$  is the learning rate parameter, which is typically made to decrease monotonically as more data points are considered.

The K-means algorithm is based on the use of squared Euclidean distance as the measure of dissimilarity between a data point and a prototype vector. Not only does this limit the type of data variables that can be considered (it would be inappropriate for cases where some or all of the variables represent categorical labels for instance),

*Section 2.3.5*

*Exercise 9.2*

*Section 2.3.7* but it can also make the determination of the cluster means nonrobust to outliers. We can generalize the  $K$ -means algorithm by introducing a more general dissimilarity measure  $V(\mathbf{x}, \mathbf{x}')$  between two vectors **x** and **x**' and then minimizing the following distortion measure distortion measure

$$
\widetilde{J} = \sum_{n=1}^{N} \sum_{k=1}^{K} r_{nk} \mathcal{V}(\mathbf{x}_n, \boldsymbol{\mu}_k)
$$
\n(9.6)

which gives the K-*medoids* algorithm. The E step again involves, for given cluster prototypes  $\mu_k$ , assigning each data point to the cluster for which the dissimilarity to the corresponding prototype is smallest. The computational cost of this is  $O(KN)$ , as is the case for the standard  $K$ -means algorithm. For a general choice of dissimilarity measure, the M step is potentially more complex than for  $K$ -means, and so it is common to restrict each cluster prototype to be equal to one of the data vectors assigned to that cluster, as this allows the algorithm to be implemented for any choice of dissimilarity measure  $V(\cdot, \cdot)$  so long as it can be readily evaluated. Thus the M step involves, for each cluster k, a discrete search over the  $N_k$  points assigned to that cluster, which requires  $O(N_k^2)$  evaluations of  $\mathcal{V}(\cdot, \cdot)$ .

One notable feature of the  $K$ -means algorithm is that at each iteration, every data point is assigned uniquely to one, and only one, of the clusters. Whereas some data points will be much closer to a particular centre  $\mu_k$  than to any other centre, there may be other data points that lie roughly midway between cluster centres. In the latter case, it is not clear that the hard assignment to the nearest cluster is the most appropriate. We shall see in the next section that by adopting a probabilistic approach, we obtain 'soft' assignments of data points to clusters in a way that reflects the level of uncertainty over the most appropriate assignment. This probabilistic formulation brings with it numerous benefits.

## **9.2.1 Image segmentation and compression**

As an illustration of the application of the  $K$ -means algorithm, we consider the related problems of image segmentation and image compression. The goal of segmentation is to partition an image into regions each of which has a reasonably homogeneous visual appearance or which corresponds to objects or parts of objects (Forsyth and Ponce, 2003). Each pixel in an image is a point in a 3-dimensional space comprising the intensities of the red, blue, and green channels, and our segmentation algorithm simply treats each pixel in the image as a separate data point. Note that strictly this space is not Euclidean because the channel intensities are bounded by the interval  $[0, 1]$ . Nevertheless, we can apply the K-means algorithm without difficulty. We illustrate the result of running  $K$ -means to convergence, for any particular value of K, by re-drawing the image replacing each pixel vector with the  $\{R, G, B\}$ intensity triplet given by the centre  $\mu_k$  to which that pixel has been assigned. Results for various values of  $K$  are shown in Figure 9.3. We see that for a given value of  $K$ , the algorithm is representing the image using a palette of only  $K$  colours. It should be emphasized that this use of  $K$ -means is not a particularly sophisticated approach to image segmentation, not least because it takes no account of the spatial proximity of different pixels. The image segmentation problem is in general extremely difficult

Image /page/6/Figure/1 description: The image displays a comparison of image segmentation results using the K-means clustering algorithm. The top row shows the original image and its segmented versions with K=2, K=3, and K=10 clusters. The bottom row presents another set of segmented images of a child, also with different values of K, alongside the original image. The segmentation results demonstrate how increasing the number of clusters (K) leads to a more detailed representation of the image, closer to the original.

**Figure 9.3** Two examples of the application of the K-means clustering algorithm to image segmentation showing the initial images together with their  $K$ -means segmentations obtained using various values of  $K$ . This also illustrates of the use of vector quantization for data compression, in which smaller values of  $K$  give higher compression at the expense of poorer image quality.

and remains the subject of active research and is introduced here simply to illustrate the behaviour of the K-means algorithm.

We can also use the result of a clustering algorithm to perform data compression. It is important to distinguish between *lossless data compression*, in which the goal is to be able to reconstruct the original data exactly from the compressed representation, and *lossy data compression*, in which we accept some errors in the reconstruction in return for higher levels of compression than can be achieved in the lossless case. We can apply the  $K$ -means algorithm to the problem of lossy data compression as follows. For each of the  $N$  data points, we store only the identity  $k$  of the cluster to which it is assigned. We also store the values of the  $K$  cluster centres  $\mu_k$ , which typically requires significantly less data, provided we choose  $K \ll N$ . Each data point is then approximated by its nearest centre  $\mu_k$ . New data points can similarly be compressed by first finding the nearest  $\mu_k$  and then storing the label k instead of the original data vector. This framework is often called *vector quantization*, and the vectors  $\mu_k$  are called *code-book vectors*.

The image segmentation problem discussed above also provides an illustration of the use of clustering for data compression. Suppose the original image has  $N$ pixels comprising  $\{R, G, B\}$  values each of which is stored with 8 bits of precision. Then to transmit the whole image directly would cost  $24N$  bits. Now suppose we first run  $K$ -means on the image data, and then instead of transmitting the original pixel intensity vectors we transmit the identity of the nearest vector  $\mu_k$ . Because there are K such vectors, this requires  $\log_2 K$  bits per pixel. We must also transmit the K code book vectors  $\mu_k$ , which requires 24K bits, and so the total number of bits required to transmit the image is  $24K + N \log_2 K$  (rounding up to the nearest integer). The original image shown in Figure 9.3 has  $240 \times 180 = 43,200$  pixels and so requires  $24 \times 43,200 = 1,036,800$  bits to transmit directly. By comparison, the compressed images require 43, 248 bits  $(K = 2)$ , 86, 472 bits  $(K = 3)$ , and 173, 040 bits ( $K = 10$ ), respectively, to transmit. These represent compression ratios compared to the original image of 4.2%, 8.3%, and 16.7%, respectively. We see that there is a trade-off between degree of compression and image quality. Note that our aim in this example is to illustrate the  $K$ -means algorithm. If we had been aiming to produce a good image compressor, then it would be more fruitful to consider small blocks of adjacent pixels, for instance  $5 \times 5$ , and thereby exploit the correlations that exist in natural images between nearby pixels.

## **9.2. Mixtures of Gaussians**

In Section 2.3.9 we motivated the Gaussian mixture model as a simple linear superposition of Gaussian components, aimed at providing a richer class of density models than the single Gaussian. We now turn to a formulation of Gaussian mixtures in terms of discrete *latent* variables. This will provide us with a deeper insight into this important distribution, and will also serve to motivate the expectation-maximization algorithm.

Recall from (2.188) that the Gaussian mixture distribution can be written as a linear superposition of Gaussians in the form

$$
p(\mathbf{x}) = \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x} | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k).
$$
 (9.7)

Let us introduce a <sup>K</sup>-dimensional binary random variable **z** having a 1-of-<sup>K</sup> representation in which a particular element  $z_k$  is equal to 1 and all other elements are equal to 0. The values of  $z_k$  therefore satisfy  $z_k \in \{0, 1\}$  and  $\sum_k z_k = 1$ , and we see that there are <sup>K</sup> possible states for the vector **z** according to which element is nonzero. We shall define the joint distribution  $p(x, z)$  in terms of a marginal distribution  $p(\mathbf{z})$  and a conditional distribution  $p(\mathbf{x}|\mathbf{z})$ , corresponding to the graphical model in Figure 9.4. The marginal distribution over **z** is specified in terms of the mixing coefficients  $\pi_k$ , such that

$$
p(z_k=1)=\pi_k
$$

### 9.2. Mixtures of Gaussians

**Figure 9.4** Graphical representation of a mixture model, in which the joint distribution is expressed in the form  $p(\mathbf{x}, \mathbf{z}) =$  $p(\mathbf{z})p(\mathbf{x}|\mathbf{z}).$ 

where the parameters  $\{\pi_k\}$  must satisfy

$$
0 \leqslant \pi_k \leqslant 1\tag{9.8}
$$

together with

 $\sum_{k=1}^{K}$  $k=1$  $\pi_k = 1$  (9.9)

in order to be valid probabilities. Because **z** uses a 1-of-<sup>K</sup> representation, we can also write this distribution in the form

$$
p(\mathbf{z}) = \prod_{k=1}^{K} \pi_k^{z_k}.
$$
\n(9.10)

Similarly, the conditional distribution of **x** given a particular value for **z** is a Gaussian

$$
p(\mathbf{x}|z_k=1) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)
$$

which can also be written in the form

$$
p(\mathbf{x}|\mathbf{z}) = \prod_{k=1}^{K} \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)^{z_k}.
$$
 (9.11)

The joint distribution is given by  $p(\mathbf{z})p(\mathbf{x}|\mathbf{z})$ , and the marginal distribution of **x** is *Exercise 9.3* then obtained by summing the joint distribution over all possible states of **z** to give

$$
p(\mathbf{x}) = \sum_{\mathbf{z}} p(\mathbf{z}) p(\mathbf{x}|\mathbf{z}) = \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)
$$
(9.12)

where we have made use of  $(9.10)$  and  $(9.11)$ . Thus the marginal distribution of **x** is a Gaussian mixture of the form (9.7). If we have several observations  $x_1, \ldots, x_N$ , then, because we have represented the marginal distribution in the form  $p(x) = \sum_{n} p(x, z)$  it follows that for every observed data point x, there is a corresponding  $\sum_{\mathbf{z}} p(\mathbf{x}, \mathbf{z})$ , it follows that for every observed data point  $\mathbf{x}_n$  there is a corresponding latent variable **z** latent variable  $z_n$ .

We have therefore found an equivalent formulation of the Gaussian mixture involving an explicit latent variable. It might seem that we have not gained much by doing so. However, we are now able to work with the joint distribution  $p(x, z)$ 

instead of the marginal distribution  $p(x)$ , and this will lead to significant simplifications, most notably through the introduction of the expectation-maximization (EM) algorithm.

Another quantity that will play an important role is the conditional probability of **z** given **x**. We shall use  $\gamma(z_k)$  to denote  $p(z_k = 1|\mathbf{x})$ , whose value can be found using Bayes' theorem

$$
\gamma(z_k) \equiv p(z_k = 1 | \mathbf{x}) = \frac{p(z_k = 1)p(\mathbf{x}|z_k = 1)}{\sum_{j=1}^{K} p(z_j = 1)p(\mathbf{x}|z_j = 1)}
$$
$$
= \frac{\pi_k \mathcal{N}(\mathbf{x} | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_{j=1}^{K} \pi_j \mathcal{N}(\mathbf{x} | \boldsymbol{\mu}_j, \boldsymbol{\Sigma}_j)} \text{ (9.13)}
$$

We shall view  $\pi_k$  as the prior probability of  $z_k = 1$ , and the quantity  $\gamma(z_k)$  as the corresponding posterior probability once we have observed **x**. As we shall see later,  $\gamma(z_k)$  can also be viewed as the *responsibility* that component k takes for 'explaining' the observation **x**.

*Section 8.1.2* We can use the technique of ancestral sampling to generate random samples distributed according to the Gaussian mixture model. To do this, we first generate a value for **z**, which we denote  $\hat{z}$ , from the marginal distribution  $p(z)$  and then generate a value for **x** from the conditional distribution  $p(\mathbf{x}|\hat{\mathbf{z}})$ . Techniques for sampling from standard distributions are discussed in Chapter 11. We can depict samples from the joint distribution  $p(x, z)$  by plotting points at the corresponding values of x and then colouring them according to the value of **z**, in other words according to which Gaussian component was responsible for generating them, as shown in Figure 9.5(a). Similarly samples from the marginal distribution  $p(x)$  are obtained by taking the samples from the joint distribution and ignoring the values of **z**. These are illustrated in Figure 9.5(b) by plotting the **x** values without any coloured labels.

> We can also use this synthetic data set to illustrate the 'responsibilities' by evaluating, for every data point, the posterior probability for each component in the mixture distribution from which this data set was generated. In particular, we can represent the value of the responsibilities  $\gamma(z_{nk})$  associated with data point  $\mathbf{x}_n$  by plotting the corresponding point using proportions of red, blue, and green ink given by  $\gamma(z_{nk})$  for  $k = 1, 2, 3$ , respectively, as shown in Figure 9.5(c). So, for instance, a data point for which  $\gamma(z_{n1})=1$  will be coloured red, whereas one for which  $\gamma(z_{n2}) = \gamma(z_{n3})=0.5$  will be coloured with equal proportions of blue and green ink and so will appear cyan. This should be compared with Figure 9.5(a) in which the data points were labelled using the true identity of the component from which they were generated.

## **9.2.1 Maximum likelihood**

Suppose we have a data set of observations  $\{x_1, \ldots, x_N\}$ , and we wish to model this data using a mixture of Gaussians. We can represent this data set as an  $N \times D$ 

*Section 8.1.2*

Image /page/10/Figure/1 description: The image displays three scatter plots, labeled (a), (b), and (c), arranged horizontally. Each plot shows data points distributed across a 2D plane with x and y axes ranging from 0 to 1. Plot (a) features three distinct clusters of points colored red, green, and blue. Plot (b) shows a single, large cluster of magenta points. Plot (c) presents a distribution of points colored red, green, and blue, with some points exhibiting a gradient from green to brown, suggesting a potential overlap or transition between clusters.

**Figure 9.5** Example of 500 points drawn from the mixture of 3 Gaussians shown in Figure 2.23. (a) Samples from the joint distribution  $p(z)p(x|z)$  in which the three states of z, corresponding to the three components of the mixture, are depicted in red, green, and blue, and (b) the corresponding samples from the marginal distribution  $p(x)$ , which is obtained by simply ignoring the values of  $z$  and just plotting the  $x$  values. The data set in (a) is said to be *complete*, whereas that in (b) is *incomplete.* (c) The same samples in which the colours represent the value of the responsibilities  $\gamma(z_{nk})$  associated with data point  $x_n$ , obtained by plotting the corresponding point using proportions of red, blue, and green ink given by  $\gamma(z_{nk})$  for  $k = 1, 2, 3$ , respectively

matrix **X** in which the  $n^{\text{th}}$  row is given by  $\mathbf{x}_n^{\text{T}}$ . Similarly, the corresponding latent variables will be denoted by an  $N \times K$  matrix **Z** with rows  $\mathbf{z}^{\text{T}}$ . If we assume that variables will be denoted by an  $N \times K$  matrix **Z** with rows  $z_n^T$ . If we assume that the data points are drawn independently from the distribution then we can express the data points are drawn independently from the distribution, then we can express the Gaussian mixture model for this i.i.d. data set using the graphical representation shown in Figure 9.6. From (9.7) the log of the likelihood function is given by

$$
\ln p(\mathbf{X}|\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Sigma}) = \sum_{n=1}^{N} \ln \left\{ \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right\}.
$$
 (9.14)

Before discussing how to maximize this function, it is worth emphasizing that there is a significant problem associated with the maximum likelihood framework applied to Gaussian mixture models, due to the presence of singularities. For simplicity, consider a Gaussian mixture whose components have covariance matrices given by  $\Sigma_k = \sigma_k^2 \mathbf{I}$ , where **I** is the unit matrix, although the conclusions will hold for general covariance matrices. Suppose that one of the components of the mixture for general covariance matrices. Suppose that one of the components of the mixture model, let us say the  $j^{\text{th}}$  component, has its mean  $\mu_j$  exactly equal to one of the data

Image /page/10/Figure/6 description: Figure 9.6 shows a graphical representation of a Gaussian mixture model for a set of N independent and identically distributed data points {xn}, with corresponding latent points {zn}, where n ranges from 1 to N. The diagram illustrates the relationship between the latent variable zn and the observed data point xn, with a parameter pi influencing the latent variable.

N *µ* ← <u></u> ← ∑

**Figure 9.7** Illustration of how singularities in the likelihood function arise with mixtures of Gaussians. This should be com-  $p(x)$ pared with the case of a single Gaussian shown in Figure 1.14 for which no singularities arise.

Image /page/11/Figure/2 description: The image displays a graph with the x-axis labeled 'x' and the y-axis labeled '(x)'. The graph shows two distinct curves. The first curve is a broad, bell-shaped curve, colored red, with several blue dots plotted along its path. Green vertical lines connect these blue dots to points on the x-axis. There are six such blue dots and green lines. The second curve is a very narrow, tall, and sharp peak, also colored red, located to the right of the first curve. This second peak also has a blue dot at its apex, connected by a green vertical line to the x-axis. A thin black curve is visible beneath the second red peak, suggesting it might be a component of the overall distribution. Several black dots are also present on the x-axis, corresponding to the base of the green lines.

points so that  $\mu_i = \mathbf{x}_n$  for some value of *n*. This data point will then contribute a term in the likelihood function of the form

$$
\mathcal{N}(\mathbf{x}_n|\mathbf{x}_n, \sigma_j^2 \mathbf{I}) = \frac{1}{(2\pi)^{1/2}} \frac{1}{\sigma_j}.
$$
\n(9.15)

If we consider the limit  $\sigma_j \to 0$ , then we see that this term goes to infinity and so the log likelihood function will also go to infinity. Thus the maximization of the log likelihood function is not a well posed problem because such singularities will always be present and will occur whenever one of the Gaussian components 'collapses' onto a specific data point. Recall that this problem did not arise in the case of a single Gaussian distribution. To understand the difference, note that if a single Gaussian collapses onto a data point it will contribute multiplicative factors to the likelihood function arising from the other data points and these factors will go to zero exponentially fast, giving an overall likelihood that goes to zero rather than infinity. However, once we have (at least) two components in the mixture, one of the components can have a finite variance and therefore assign finite probability to all of the data points while the other component can shrink onto one specific data point and thereby contribute an ever increasing additive value to the log likelihood. This is illustrated in Figure 9.7. These singularities provide another example of the severe over-fitting that can occur in a maximum likelihood approach. We shall see *Section 10.1* that this difficulty does not occur if we adopt a Bayesian approach. For the moment, however, we simply note that in applying maximum likelihood to Gaussian mixture models we must take steps to avoid finding such pathological solutions and instead seek local maxima of the likelihood function that are well behaved. We can hope to avoid the singularities by using suitable heuristics, for instance by detecting when a Gaussian component is collapsing and resetting its mean to a randomly chosen value while also resetting its covariance to some large value, and then continuing with the optimization.

> A further issue in finding maximum likelihood solutions arises from the fact that for any given maximum likelihood solution, a  $K$ -component mixture will have a total of K! equivalent solutions corresponding to the K! ways of assigning K sets of parameters to K components. In other words, for any given (nondegenerate) point in the space of parameter values there will be a further  $K!-1$  additional points all of which give rise to exactly the same distribution. This problem is known as

Section 10.1

*identifiability* (Casella and Berger, 2002) and is an important issue when we wish to interpret the parameter values discovered by a model. Identifiability will also arise when we discuss models having continuous latent variables in Chapter 12. However, for the purposes of finding a good density model, it is irrelevant because any of the equivalent solutions is as good as any other.

Maximizing the log likelihood function (9.14) for a Gaussian mixture model turns out to be a more complex problem than for the case of a single Gaussian. The difficulty arises from the presence of the summation over  $k$  that appears inside the logarithm in (9.14), so that the logarithm function no longer acts directly on the Gaussian. If we set the derivatives of the log likelihood to zero, we will no longer obtain a closed form solution, as we shall see shortly.

One approach is to apply gradient-based optimization techniques (Fletcher, 1987; Nocedal and Wright, 1999; Bishop and Nabney, 2008). Although gradient-based techniques are feasible, and indeed will play an important role when we discuss mixture density networks in Chapter 5, we now consider an alternative approach known as the EM algorithm which has broad applicability and which will lay the foundations for a discussion of variational inference techniques in Chapter 10.

## **9.2.2 EM for Gaussian mixtures**

An elegant and powerful method for finding maximum likelihood solutions for models with latent variables is called the *expectation-maximization* algorithm, or *EM* algorithm (Dempster *et al.*, 1977; McLachlan and Krishnan, 1997). Later we shall give a general treatment of EM, and we shall also show how EM can be generalized *Section 10.1* to obtain the variational inference framework. Initially, we shall motivate the EM algorithm by giving a relatively informal treatment in the context of the Gaussian mixture model. We emphasize, however, that EM has broad applicability, and indeed it will be encountered in the context of a variety of different models in this book.

> Let us begin by writing down the conditions that must be satisfied at a maximum of the likelihood function. Setting the derivatives of  $\ln p(\mathbf{X}|\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Sigma})$  in (9.14) with respect to the means  $\mu_k$  of the Gaussian components to zero, we obtain

$$
0 = -\sum_{n=1}^{N} \underbrace{\frac{\pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_j \pi_j \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_j, \boldsymbol{\Sigma}_j)}}_{\gamma(z_{nk})} \boldsymbol{\Sigma}_k(\mathbf{x}_n - \boldsymbol{\mu}_k)
$$
(9.16)

where we have made use of the form  $(2.43)$  for the Gaussian distribution. Note that the posterior probabilities, or responsibilities, given by (9.13) appear naturally on the right-hand side. Multiplying by  $\Sigma_k^{-1}$  (which we assume to be nonsingular) and rearranging we obtain rearranging we obtain

$$
\mu_k = \frac{1}{N_k} \sum_{n=1}^{N} \gamma(z_{nk}) \mathbf{x}_n
$$
\n(9.17)

where we have defined

$$
N_k = \sum_{n=1}^{N} \gamma(z_{nk}).
$$
\n(9.18)

We can interpret  $N_k$  as the effective number of points assigned to cluster k. Note carefully the form of this solution. We see that the mean  $\mu_k$  for the  $k^{\text{th}}$  Gaussian component is obtained by taking a weighted mean of all of the points in the data set, in which the weighting factor for data point  $\mathbf{x}_n$  is given by the posterior probability  $\gamma(z_{nk})$  that component k was responsible for generating  $x_n$ .

If we set the derivative of  $\ln p(\mathbf{X}|\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Sigma})$  with respect to  $\boldsymbol{\Sigma}_k$  to zero, and follow a similar line of reasoning, making use of the result for the maximum likelihood *Section 2.3.4* solution for the covariance matrix of a single Gaussian, we obtain

$$
\Sigma_k = \frac{1}{N_k} \sum_{n=1}^{N} \gamma(z_{nk}) (\mathbf{x}_n - \boldsymbol{\mu}_k) (\mathbf{x}_n - \boldsymbol{\mu}_k)^{\mathrm{T}}
$$
(9.19)

which has the same form as the corresponding result for a single Gaussian fitted to the data set, but again with each data point weighted by the corresponding posterior probability and with the denominator given by the effective number of points associated with the corresponding component.

Finally, we maximize  $\ln p(\mathbf{X}|\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Sigma})$  with respect to the mixing coefficients  $\pi_k$ . Here we must take account of the constraint (9.9), which requires the mixing *Appendix E* coefficients to sum to one. This can be achieved using a Lagrange multiplier and maximizing the following quantity

$$
\ln p(\mathbf{X}|\boldsymbol{\pi}, \boldsymbol{\mu}, \boldsymbol{\Sigma}) + \lambda \left(\sum_{k=1}^{K} \pi_k - 1\right)
$$
\n(9.20)

which gives

$$
0 = \sum_{n=1}^{N} \frac{\mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_j \pi_j \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_j, \boldsymbol{\Sigma}_j)} + \lambda
$$
(9.21)

where again we see the appearance of the responsibilities. If we now multiply both sides by  $\pi_k$  and sum over k making use of the constraint (9.9), we find  $\lambda = -N$ . Using this to eliminate  $\lambda$  and rearranging we obtain

$$
\pi_k = \frac{N_k}{N} \tag{9.22}
$$

so that the mixing coefficient for the  $k<sup>th</sup>$  component is given by the average responsibility which that component takes for explaining the data points.

It is worth emphasizing that the results  $(9.17)$ ,  $(9.19)$ , and  $(9.22)$  do not constitute a closed-form solution for the parameters of the mixture model because the responsibilities  $\gamma(z_{nk})$  depend on those parameters in a complex way through (9.13). However, these results do suggest a simple iterative scheme for finding a solution to the maximum likelihood problem, which as we shall see turns out to be an instance of the EM algorithm for the particular case of the Gaussian mixture model. We first choose some initial values for the means, covariances, and mixing coefficients. Then we alternate between the following two updates that we shall call the E step

*Section 2.3.4*

*Appendix E*

Image /page/14/Figure/1 description: This figure displays six scatter plots illustrating the EM algorithm. Plots (a) and (b) show initial states with two distinct clusters represented by blue and green/red points, and two circles indicating initial cluster centers. Plots (c) through (f) show the progression of the EM algorithm with increasing values of L (1, 2, 5, and 20 respectively). In these plots, the data points are colored in a gradient from blue to red, suggesting a mixture of two Gaussian distributions. Ellipses are overlaid on the data points, representing the estimated covariance of the clusters, which become more refined and aligned with the data distribution as L increases.

**Figure 9.8** Illustration of the EM algorithm using the Old Faithful set as used for the illustration of the K-means algorithm in Figure 9.1. See the text for details.

and the M step, for reasons that will become apparent shortly. In the *expectation* step, or E step, we use the current values for the parameters to evaluate the posterior probabilities, or responsibilities, given by (9.13). We then use these probabilities in the *maximization* step, or M step, to re-estimate the means, covariances, and mixing coefficients using the results (9.17), (9.19), and (9.22). Note that in so doing we first evaluate the new means using (9.17) and then use these new values to find the covariances using (9.19), in keeping with the corresponding result for a single Gaussian distribution. We shall show that each update to the parameters resulting from an E step followed by an M step is guaranteed to increase the log likelihood *Section 9.4* function. In practice, the algorithm is deemed to have converged when the change in the log likelihood function, or alternatively in the parameters, falls below some threshold. We illustrate the EM algorithm for a mixture of two Gaussians applied to the rescaled Old Faithful data set in Figure 9.8. Here a mixture of two Gaussians is used, with centres initialized using the same values as for the  $K$ -means algorithm in Figure 9.1, and with precision matrices initialized to be proportional to the unit matrix. Plot (a) shows the data points in green, together with the initial configuration of the mixture model in which the one standard-deviation contours for the two

*Section 9.4*

Gaussian components are shown as blue and red circles. Plot (b) shows the result of the initial E step, in which each data point is depicted using a proportion of blue ink equal to the posterior probability of having been generated from the blue component, and a corresponding proportion of red ink given by the posterior probability of having been generated by the red component. Thus, points that have a significant probability for belonging to either cluster appear purple. The situation after the first M step is shown in plot (c), in which the mean of the blue Gaussian has moved to the mean of the data set, weighted by the probabilities of each data point belonging to the blue cluster, in other words it has moved to the centre of mass of the blue ink. Similarly, the covariance of the blue Gaussian is set equal to the covariance of the blue ink. Analogous results hold for the red component. Plots (d), (e), and (f) show the results after 2, 5, and 20 complete cycles of EM, respectively. In plot (f) the algorithm is close to convergence.

Note that the EM algorithm takes many more iterations to reach (approximate) convergence compared with the  $K$ -means algorithm, and that each cycle requires significantly more computation. It is therefore common to run the  $K$ -means algorithm in order to find a suitable initialization for a Gaussian mixture model that is subsequently adapted using EM. The covariance matrices can conveniently be initialized to the sample covariances of the clusters found by the K-means algorithm, and the mixing coefficients can be set to the fractions of data points assigned to the respective clusters. As with gradient-based approaches for maximizing the log likelihood, techniques must be employed to avoid singularities of the likelihood function in which a Gaussian component collapses onto a particular data point. It should be emphasized that there will generally be multiple local maxima of the log likelihood function, and that EM is not guaranteed to find the largest of these maxima. Because the EM algorithm for Gaussian mixtures plays such an important role, we summarize it below.

## EM for Gaussian Mixtures

Given a Gaussian mixture model, the goal is to maximize the likelihood function with respect to the parameters (comprising the means and covariances of the components and the mixing coefficients).

- 1. Initialize the means  $\mu_k$ , covariances  $\Sigma_k$  and mixing coefficients  $\pi_k$ , and evaluate the initial value of the log likelihood.
- 2. **E step**. Evaluate the responsibilities using the current parameter values

$$
\gamma(z_{nk}) = \frac{\pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_{j=1}^K \pi_j \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_j, \boldsymbol{\Sigma}_j)}.
$$
 (9.23)

3. **M step**. Re-estimate the parameters using the current responsibilities

$$
\mu_k^{\text{new}} = \frac{1}{N_k} \sum_{n=1}^{N} \gamma(z_{nk}) \mathbf{x}_n \tag{9.24}
$$

$$
\Sigma_k^{\text{new}} = \frac{1}{N_k} \sum_{n=1}^N \gamma(z_{nk}) \left( \mathbf{x}_n - \boldsymbol{\mu}_k^{\text{new}} \right) \left( \mathbf{x}_n - \boldsymbol{\mu}_k^{\text{new}} \right)^{\text{T}} \qquad (9.25)
$$

$$
\pi_k^{\text{new}} = \frac{N_k}{N} \tag{9.26}
$$

where

$$
N_k = \sum_{n=1}^{N} \gamma(z_{nk}).
$$
\n(9.27)

4. Evaluate the log likelihood

$$
\ln p(\mathbf{X}|\boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi}) = \sum_{n=1}^{N} \ln \left\{ \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right\}
$$
(9.28)

and check for convergence of either the parameters or the log likelihood. If the convergence criterion is not satisfied return to step 2.

# 9.3. An Alternative View of EM

In this section, we present a complementary view of the EM algorithm that recognizes the key role played by latent variables. We discuss this approach first of all in an abstract setting, and then for illustration we consider once again the case of Gaussian mixtures.

The goal of the EM algorithm is to find maximum likelihood solutions for models having latent variables. We denote the set of all observed data by  $X$ , in which the  $n^{\text{th}}$  row represents  $x^{\text{T}}$  and similarly we denote the set of all latent variables by Z.  $n^{\text{th}}$  row represents  $\mathbf{x}_n^{\text{T}}$ , and similarly we denote the set of all latent variables by **Z**, with a corresponding row  $\mathbf{z}^{\text{T}}$ . The set of all model parameters is denoted by  $\boldsymbol{\theta}$  and with a corresponding row  $z_n^T$ . The set of all model parameters is denoted by  $\theta$ , and so the log likelihood function is given by so the log likelihood function is given by

$$
\ln p(\mathbf{X}|\boldsymbol{\theta}) = \ln \left\{ \sum_{\mathbf{Z}} p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta}) \right\}.
$$
 (9.29)

Note that our discussion will apply equally well to continuous latent variables simply by replacing the sum over **Z** with an integral.

A key observation is that the summation over the latent variables appears inside the logarithm. Even if the joint distribution  $p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$  belongs to the exponential family, the marginal distribution  $p(X|\theta)$  typically does not as a result of this summation. The presence of the sum prevents the logarithm from acting directly on the joint distribution, resulting in complicated expressions for the maximum likelihood solution.

Now suppose that, for each observation in **X**, we were told the corresponding value of the latent variable **Z**. We shall call  $\{X, Z\}$  the *complete* data set, and we shall refer to the actual observed data **X** as *incomplete*, as illustrated in Figure 9.5. The likelihood function for the complete data set simply takes the form  $\ln p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$ , and we shall suppose that maximization of this complete-data log likelihood function is straightforward.

In practice, however, we are not given the complete data set  $\{X, Z\}$ , but only the incomplete data **X**. Our state of knowledge of the values of the latent variables in **Z** is given only by the posterior distribution  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta})$ . Because we cannot use the complete-data log likelihood, we consider instead its expected value under the posterior distribution of the latent variable, which corresponds (as we shall see) to the E step of the EM algorithm. In the subsequent M step, we maximize this expectation. If the current estimate for the parameters is denoted  $\theta$ <sup>old</sup>, then a pair of successive E and M steps gives rise to a revised estimate  $\theta^{\text{new}}$ . The algorithm is initialized by choosing some starting value for the parameters  $\theta_0$ . The use of the expectation may seem somewhat arbitrary. However, we shall see the motivation for this choice when we give a deeper treatment of EM in Section 9.4.

In the E step, we use the current parameter values  $\theta$ <sup>old</sup> to find the posterior distribution of the latent variables given by  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$ . We then use this posterior distribution to find the expectation of the complete-data log likelihood evaluated for some general parameter value  $\theta$ . This expectation, denoted  $\mathcal{Q}(\theta, \theta^{\text{old}})$ , is given by

$$
Q(\theta, \theta^{\text{old}}) = \sum_{\mathbf{Z}} p(\mathbf{Z}|\mathbf{X}, \theta^{\text{old}}) \ln p(\mathbf{X}, \mathbf{Z}|\theta).
$$
 (9.30)

In the M step, we determine the revised parameter estimate  $\theta^{\text{new}}$  by maximizing this function

$$
\boldsymbol{\theta}^{\text{new}} = \arg \max_{\boldsymbol{\theta}} \mathcal{Q}(\boldsymbol{\theta}, \boldsymbol{\theta}^{\text{old}}). \tag{9.31}
$$

Note that in the definition of  $\mathcal{Q}(\theta, \theta^{\text{old}})$ , the logarithm acts directly on the joint distribution  $p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$ , and so the corresponding M-step maximization will, by supposition, be tractable.

The general EM algorithm is summarized below. It has the property, as we shall show later, that each cycle of EM will increase the incomplete-data log likelihood *Section 9.4* (unless it is already at a local maximum).

#### The General EM Algorithm

Given a joint distribution  $p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\theta})$  over observed variables **X** and latent variables **Z**, governed by parameters  $\theta$ , the goal is to maximize the likelihood function  $p(\mathbf{X}|\boldsymbol{\theta})$  with respect to  $\boldsymbol{\theta}$ .

1. Choose an initial setting for the parameters  $\theta^{\text{old}}$ .

## *Section 9.4*

- 2. **E step** Evaluate  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$ .
- 3. **M step** Evaluate *θ*new given by

$$
\theta^{\text{new}} = \underset{\theta}{\text{arg max }} \mathcal{Q}(\theta, \theta^{\text{old}})
$$
 (9.32)

where

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{\text{old}}) = \sum_{\mathbf{Z}} p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}}) \ln p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta}).
$$
 (9.33)

4. Check for convergence of either the log likelihood or the parameter values. If the convergence criterion is not satisfied, then let

$$
\boldsymbol{\theta}^{\text{old}} \leftarrow \boldsymbol{\theta}^{\text{new}} \tag{9.34}
$$

and return to step 2.

The EM algorithm can also be used to find MAP (maximum posterior) solutions *Exercise 9.4* for models in which a prior  $p(\theta)$  is defined over the parameters. In this case the E step remains the same as in the maximum likelihood case, whereas in the M step the quantity to be maximized is given by  $\mathcal{Q}(\theta, \theta^{\text{old}}) + \ln p(\theta)$ . Suitable choices for the prior will remove the singularities of the kind illustrated in Figure 9.7.

> Here we have considered the use of the EM algorithm to maximize a likelihood function when there are discrete latent variables. However, it can also be applied when the unobserved variables correspond to missing values in the data set. The distribution of the observed values is obtained by taking the joint distribution of all the variables and then marginalizing over the missing ones. EM can then be used to maximize the corresponding likelihood function. We shall show an example of the application of this technique in the context of principal component analysis in Figure 12.11. This will be a valid procedure if the data values are *missing at random*, meaning that the mechanism causing values to be missing does not depend on the unobserved values. In many situations this will not be the case, for instance if a sensor fails to return a value whenever the quantity it is measuring exceeds some threshold.

## **9.3.1 Gaussian mixtures revisited**

We now consider the application of this latent variable view of EM to the specific case of a Gaussian mixture model. Recall that our goal is to maximize the log likelihood function (9.14), which is computed using the observed data set **X**, and we saw that this was more difficult than for the case of a single Gaussian distribution due to the presence of the summation over  $k$  that occurs inside the logarithm. Suppose then that in addition to the observed data set **X**, we were also given the values of the corresponding discrete variables **Z**. Recall that Figure 9.5(a) shows a 'complete' data set (i.e., one that includes labels showing which component generated each data point) while Figure 9.5(b) shows the corresponding 'incomplete' data set. The graphical model for the complete data is shown in Figure 9.9.

**Figure 9.9** This shows the same graph as in Figure 9.6 except that we now suppose that the discrete variables  $z_n$  are observed, as well as the data variables  $x_n$ .

Image /page/19/Figure/2 description: This is a graphical representation of a probabilistic model. The model includes variables denoted by pi, mu, and Sigma, which influence latent variables zn and observed variables xn. The entire structure is enclosed within a box labeled 'N', indicating that this represents a collection of N such structures, likely for a sequence or dataset. The arrows indicate the flow of probabilistic dependencies: pi influences zn, and both mu and Sigma influence xn, with zn also influencing xn. This diagram is commonly used in machine learning and statistics to illustrate generative models like mixture models or latent variable models.

Now consider the problem of maximizing the likelihood for the complete data set  $\{X, Z\}$ . From (9.10) and (9.11), this likelihood function takes the form

$$
p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi}) = \prod_{n=1}^{N} \prod_{k=1}^{K} \pi_k^{z_{nk}} \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)^{z_{nk}}
$$
(9.35)

where  $z_{nk}$  denotes the  $k^{\text{th}}$  component of  $z_n$ . Taking the logarithm, we obtain

$$
\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi}) = \sum_{n=1}^{N} \sum_{k=1}^{K} z_{nk} \left\{ \ln \pi_k + \ln \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right\}.
$$
 (9.36)

Comparison with the log likelihood function (9.14) for the incomplete data shows that the summation over k and the logarithm have been interchanged. The logarithm now acts directly on the Gaussian distribution, which itself is a member of the exponential family. Not surprisingly, this leads to a much simpler solution to the maximum likelihood problem, as we now show. Consider first the maximization with respect to the means and covariances. Because  $z_n$  is a K-dimensional vector with all elements equal to 0 except for a single element having the value 1, the complete-data log likelihood function is simply a sum of  $K$  independent contributions, one for each mixture component. Thus the maximization with respect to a mean or a covariance is exactly as for a single Gaussian, except that it involves only the subset of data points that are 'assigned' to that component. For the maximization with respect to the mixing coefficients, we note that these are coupled for different values of  $k$  by virtue of the summation constraint (9.9). Again, this can be enforced using a Lagrange multiplier as before, and leads to the result

$$
\pi_k = \frac{1}{N} \sum_{n=1}^{N} z_{nk}
$$
\n(9.37)

so that the mixing coefficients are equal to the fractions of data points assigned to the corresponding components.

Thus we see that the complete-data log likelihood function can be maximized trivially in closed form. In practice, however, we do not have values for the latent variables so, as discussed earlier, we consider the expectation, with respect to the posterior distribution of the latent variables, of the complete-data log likelihood.

Using (9.10) and (9.11) together with Bayes' theorem, we see that this posterior distribution takes the form

$$
p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi}) \propto \prod_{n=1}^{N} \prod_{k=1}^{K} \left[ \pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right]^{z_{nk}}.
$$
 (9.38)

and hence factorizes over n so that under the posterior distribution the  ${\bf z}_n$  are *Exercise* 9.5 independent. This is easily verified by inspection of the directed graph in Figure 9.6 *Section 8.2* and making use of the d-separation criterion. The expected value of the indicator variable  $z_{nk}$  under this posterior distribution is then given by

$$
\mathbb{E}[z_{nk}] = \frac{\sum_{z_{nk}} z_{nk} \left[ \pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right]^{z_{nk}}}{\sum_{z_{nj}} \left[ \pi_j \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_j, \boldsymbol{\Sigma}_j) \right]^{z_{nj}}}
$$

$$
= \frac{\pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_{j=1}^K \pi_j \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_j, \boldsymbol{\Sigma}_j)} = \gamma(z_{nk}) \tag{9.39}
$$

which is just the responsibility of component k for data point  $\mathbf{x}_n$ . The expected value of the complete-data log likelihood function is therefore given by

$$
\mathbb{E}_{\mathbf{Z}}[\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi})] = \sum_{n=1}^{N} \sum_{k=1}^{K} \gamma(z_{nk}) \left\{ \ln \pi_k + \ln \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) \right\}.
$$
 (9.40)

We can now proceed as follows. First we choose some initial values for the parameters  $\mu^{\text{old}}$ ,  $\Sigma^{\text{old}}$  and  $\pi^{\text{old}}$ , and use these to evaluate the responsibilities (the E step). We then keep the responsibilities fixed and maximize (9.40) with respect to  $\mu_k$ ,  $\Sigma_k$ and  $\pi_k$  (the M step). This leads to closed form solutions for  $\mu^{\text{new}}$ ,  $\hat{\Sigma}^{\text{new}}$  and  $\pi^{\text{new}}$ *Exercise* 9.8 given by (9.17), (9.19), and (9.22) as before. This is precisely the EM algorithm for Gaussian mixtures as derived earlier. We shall gain more insight into the role of the expected complete-data log likelihood function when we give a proof of convergence of the EM algorithm in Section 9.4.

#### **9.3.2 Relation to** K**-means**

Comparison of the  $K$ -means algorithm with the EM algorithm for Gaussian mixtures shows that there is a close similarity. Whereas the  $K$ -means algorithm performs a *hard* assignment of data points to clusters, in which each data point is associated uniquely with one cluster, the EM algorithm makes a *soft* assignment based on the posterior probabilities. In fact, we can derive the  $K$ -means algorithm as a particular limit of EM for Gaussian mixtures as follows.

Consider a Gaussian mixture model in which the covariance matrices of the mixture components are given by  $\epsilon I$ , where  $\epsilon$  is a variance parameter that is shared

Exercise 9.5
Section 8.2

*Exercise 9.8*

by all of the components, and **I** is the identity matrix, so that

$$
p(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) = \frac{1}{(2\pi\epsilon)^{1/2}} \exp\left\{-\frac{1}{2\epsilon} ||\mathbf{x} - \boldsymbol{\mu}_k||^2\right\}.
$$
 (9.41)

We now consider the EM algorithm for a mixture of  $K$  Gaussians of this form in which we treat  $\epsilon$  as a fixed constant, instead of a parameter to be re-estimated. From  $(9.13)$  the posterior probabilities, or responsibilities, for a particular data point  $\mathbf{x}_n$ , are given by

$$
\gamma(z_{nk}) = \frac{\pi_k \exp\left\{-\|\mathbf{x}_n - \boldsymbol{\mu}_k\|^2/2\epsilon\right\}}{\sum_j \pi_j \exp\left\{-\|\mathbf{x}_n - \boldsymbol{\mu}_j\|^2/2\epsilon\right\}}.
$$
\n(9.42)

If we consider the limit  $\epsilon \to 0$ , we see that in the denominator the term for which  $\|\mathbf{x}_n - \boldsymbol{\mu}_i\|^2$  is smallest will go to zero most slowly, and hence the responsibilities  $\gamma(z_{nk})$  for the data point  $\mathbf{x}_n$  all go to zero except for term j, for which the responsibility  $\gamma(z_{ni})$  will go to unity. Note that this holds independently of the values of the  $\pi_k$  so long as none of the  $\pi_k$  is zero. Thus, in this limit, we obtain a hard assignment of data points to clusters, just as in the K-means algorithm, so that  $\gamma(z_{nk}) \to r_{nk}$ where  $r_{nk}$  is defined by (9.2). Each data point is thereby assigned to the cluster having the closest mean.

The EM re-estimation equation for the  $\mu_k$ , given by (9.17), then reduces to the  $K$ -means result (9.4). Note that the re-estimation formula for the mixing coefficients (9.22) simply re-sets the value of  $\pi_k$  to be equal to the fraction of data points assigned to cluster  $k$ , although these parameters no longer play an active role in the algorithm.

Finally, in the limit  $\epsilon \to 0$  the expected complete-data log likelihood, given by *Exercise 9.11* (9.40), becomes

$$
\mathbb{E}_{\mathbf{Z}}[\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi})] \rightarrow -\frac{1}{2} \sum_{n=1}^{N} \sum_{k=1}^{K} r_{nk} ||\mathbf{x}_n - \boldsymbol{\mu}_k||^2 + \text{const.}
$$
 (9.43)

Thus we see that in this limit, maximizing the expected complete-data log likelihood is equivalent to minimizing the distortion measure  $J$  for the  $K$ -means algorithm given by  $(9.1)$ .

Note that the K-means algorithm does not estimate the covariances of the clusters but only the cluster means. A hard-assignment version of the Gaussian mixture model with general covariance matrices, known as the *elliptical* K*-means* algorithm, has been considered by Sung and Poggio (1994).

#### **9.3.3 Mixtures of Bernoulli distributions**

So far in this chapter, we have focussed on distributions over continuous variables described by mixtures of Gaussians. As a further example of mixture modelling, and to illustrate the EM algorithm in a different context, we now discuss mixtures of discrete binary variables described by Bernoulli distributions. This model is also known as *latent class analysis* (Lazarsfeld and Henry, 1968; McLachlan and Peel, 2000). As well as being of practical importance in its own right, our discussion of Bernoulli mixtures will also lay the foundation for a consideration of hidden *Section 13.2* Markov models over discrete variables.

*Exercise 9.11*

#### **9.3. An Alternative View of EM**

Consider a set of D binary variables  $x_i$ , where  $i = 1, \ldots, D$ , each of which is governed by a Bernoulli distribution with parameter  $\mu_i$ , so that

$$
p(\mathbf{x}|\boldsymbol{\mu}) = \prod_{i=1}^{D} \mu_i^{x_i} (1 - \mu_i)^{(1 - x_i)}
$$
(9.44)

where  $\mathbf{x} = (x_1, \dots, x_D)^\text{T}$  and  $\boldsymbol{\mu} = (\mu_1, \dots, \mu_D)^\text{T}$ . We see that the individual variables  $x_i$  are independent, given  $\mu$ . The mean and covariance of this distribution are easily seen to be

$$
\mathbb{E}[\mathbf{x}] = \boldsymbol{\mu} \tag{9.45}
$$
\n
$$
\mathbb{E}[\mathbf{x}] = \mathbb{E}[\mathbf{x} \cdot (1 - \mathbf{x})] \tag{9.46}
$$

$$
cov[\mathbf{x}] = diag{\mu_i(1-\mu_i)}.
$$
 (9.46)

Now let us consider a finite mixture of these distributions given by

$$
p(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\pi}) = \sum_{k=1}^{K} \pi_k p(\mathbf{x}|\boldsymbol{\mu}_k)
$$
\n(9.47)

where  $\mu = {\mu_1, \ldots, \mu_K}, \pi = {\pi_1, \ldots, \pi_K},$  and

 $\mathbf{r}$ 

$$
p(\mathbf{x}|\boldsymbol{\mu}_k) = \prod_{i=1}^{D} \mu_{ki}^{x_i} (1 - \mu_{ki})^{(1 - x_i)}.
$$
 (9.48)

*Exercise* 9.12 The mean and covariance of this mixture distribution are given by

$$
\mathbb{E}[\mathbf{x}] = \sum_{k=1}^{K} \pi_k \boldsymbol{\mu}_k \tag{9.49}
$$

$$
cov[\mathbf{x}] = \sum_{k=1}^{K} \pi_k \left\{ \mathbf{\Sigma}_k + \boldsymbol{\mu}_k \boldsymbol{\mu}_k^{\mathrm{T}} \right\} - \mathbb{E}[\mathbf{x}] \mathbb{E}[\mathbf{x}]^{\mathrm{T}}
$$
(9.50)

where  $\Sigma_k = \text{diag} \{ \mu_{ki} (1 - \mu_{ki}) \}.$  Because the covariance matrix cov[**x**] is no longer diagonal, the mixture distribution can capture correlations between the variables, unlike a single Bernoulli distribution.

If we are given a data set  $X = \{x_1, \ldots, x_N\}$  then the log likelihood function for this model is given by

$$
\ln p(\mathbf{X}|\boldsymbol{\mu}, \boldsymbol{\pi}) = \sum_{n=1}^{N} \ln \left\{ \sum_{k=1}^{K} \pi_k p(\mathbf{x}_n | \boldsymbol{\mu}_k) \right\}.
$$
 (9.51)

Again we see the appearance of the summation inside the logarithm, so that the maximum likelihood solution no longer has closed form.

We now derive the EM algorithm for maximizing the likelihood function for the mixture of Bernoulli distributions. To do this, we first introduce an explicit latent