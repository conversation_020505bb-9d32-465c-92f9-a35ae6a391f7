# Solution of the Monge problem I: Global approach

In the present chapter and the next one I shall investigate the solvability of the Monge problem for a Lagrangian cost function. Recall from Theorem 5.30 that it is sufficient to identify conditions under which the initial measure  $\mu$  does not see the set of points where the c-subdifferential of a c-convex function  $\psi$  is multivalued.

Consider a Riemannnian manifold  $M$ , and a cost function  $c(x, y)$  on  $M \times M$ , deriving from a Lagrangian function  $L(x, v, t)$  on  $TM \times [0, 1]$ satisfying the classical conditions of Definition 7.6. Let  $\mu_0$  and  $\mu_1$  be two given probability measures, and let  $(\mu_t)_{0 \leq t \leq 1}$  be a displacement interpolation, written as the law of a random minimizing curve  $\gamma$  at time t.

If the Lagrangian satisfies adequate regularity and convexity properties, Theorem 8.5 shows that the coupling  $(\gamma(s), \gamma(t))$  is always deterministic as soon as  $0 < s < 1$ , however singular  $\mu_0$  and  $\mu_1$  might be. The question whether one can construct a deterministic coupling of  $(\mu_0, \mu_1)$  is much more subtle, and cannot be answered without regularity assumptions on  $\mu_0$ . In this chapter, a simple approach to this problem will be attempted, but only with partial success, since eventually it will work out only for a particular class of cost functions, including at least the quadratic cost in Euclidean space (arguably the most important case).

Our main assumption on the cost function  $c$  will be:

**Assumption (C):** For any c-convex function  $\psi$  and any  $x \in M$ , the c-subdifferential  $\partial_c \psi(x)$  is pathwise connected.

**Example 9.1.** Consider the cost function  $c(x, y) = -x \cdot y$  in  $\mathbb{R}^n$ . Let  $y_0$  and  $y_1$  belong to  $\partial_c \psi(x)$ ; then, for any  $z \in \mathbb{R}^n$  one has

218 9 Solution of the Monge problem I: Global approach

$$
\psi(x) + y_0 \cdot (z - x) \le \psi(z);
$$
  $\psi(x) + y_1 \cdot (z - x) \le \psi(z).$ 

It follows that  $\psi(x)+y_t\cdot(z-x)\leq \psi(z)$ , where  $y_t:=(1-t)y_0+t y_1$ . Thus the line segment  $(y_t)_{0 \leq t \leq 1}$  is entirely contained in the subdifferential of  $\psi$  at x. The same computation applies to  $c(x, y) = |x - y|^2/2$ , or to any cost function of the form  $a(x) - x \cdot y + b(y)$ .

Actually, there are not so many examples where Assumption (C) is known to be satisfied. Before commenting more on this issue, let me illustrate the interest of this assumption by showing how it can be used.

### Theorem 9.2 (Conditions for single-valued subdifferentials).

Let M be a smooth n-dimensional Riemannian manifold, and c a real-valued cost function, bounded below, deriving from a Lagrangian  $L(x, v, t)$  on TM  $\times$  [0, 1], satisfying the classical conditions of Definition 7.6 and such that:

(i) Assumption  $(C)$  is satisfied.

(ii) The conclusion of Theorem 8.1 (Mather's shortening lemma), in the form of inequality (8.4), holds true for  $t_0 = 1/2$  with an exponent  $\beta > 1 - (1/n)$ , and a uniform constant. More explicitly: Whenever  $x_1, x_2, y_1, y_2$  are four points in M satisfying  $c(x_1, y_1) + c(x_2, y_2) \leq$  $c(x_1,y_2) + c(x_2,y_1)$ , and  $\gamma_1$ ,  $\gamma_2$  are two action-minimizing curves with  $\gamma_1(0) = x_1, \gamma_1(1) = y_1, \gamma_2(0) = x_2, \gamma_2(1) = y_2$ , then

$$
\sup_{0 \le t \le 1} d(\gamma_1(t), \gamma_2(t)) \le C d(\gamma_1(1/2), \gamma_2(1/2))^{\beta}.
$$
 (9.1)

Then, for any c-convex function  $\psi$ , there is a set  $Z \subset M$  of Hausdorff dimension at most  $(n-1)/\beta < n$  (and therefore of zero n-dimensional measure), such that the c-subdifferential  $\partial_c \psi(x)$  contains at most one element if  $x \notin Z$ .

*Proof of Theorem 9.2.* Let Z be the set of points x for which  $\psi(x)$  <  $+\infty$  but  $\partial_c\psi(x)$  is not single-valued; the problem is to show that Z is of dimension at most  $(n-1)/\beta$ .

Let  $x \in M$  with  $\psi(x) < +\infty$ , and let  $y \in \partial_c \psi(x)$ . Introduce an action-minimizing curve  $\gamma = \gamma^{x,y}$  joining  $x = \gamma(0)$  to  $y = \gamma(1)$ . I claim that the map

$$
F: \ \gamma\left(\frac{1}{2}\right) \ \longmapsto x
$$

is well-defined on its domain of definition, which is the union of all  $\gamma^{x,y}(1/2)$ . (I mean,  $m = \gamma(1/2)$  determines x unambiguously; there cannot be two different points x for which  $\gamma(1/2)$  is the same.) Indeed, assume  $y \in \partial_c \psi(x)$  and  $y' \in \partial_c \psi(x')$ , with  $\psi(x) < +\infty$ ,  $\psi(x') < +\infty$ , and let  $\gamma$  and  $\gamma'$  be minimizing geodesics between x and y on the one hand,  $x'$  and  $y'$  on the other hand. It follows from the definitions of subdifferential that

$$
\begin{cases} \psi(x) + c(x, y) \le \psi(x') + c(x', y) \\ \psi(x') + c(x', y') \le \psi(x) + c(x, y'). \end{cases}
$$

Thus

$$
c(x, y) + c(x', y') \le c(x, y') + c(x', y).
$$

Then by  $(9.1)$ ,

$$
d(x, x') \le C \ d\left(\gamma\left(\frac{1}{2}\right), \ \gamma'\left(\frac{1}{2}\right)\right)^{\beta}.
$$

This implies that  $m = \gamma(1/2)$  determines  $x = F(m)$  unambiguously, and even that F is Hölder- $\beta$ . (Obviously, this is the same reasoning as in the proof of Theorem 8.5.)

Now, cover  $M$  by a countable number of open sets in which  $M$  is diffeomorphic to a subset U of  $\mathbb{R}^n$ , via some diffeomorphism  $\varphi_U$ . In each of these open sets U, consider the union  $H_U$  of all hyperplanes passing through a point of rational coordinates, orthogonal to a unit vector with rational coordinates. Transport this set back to M thanks to the local diffeomorphism, and take the union over all the sets  $U$ . This gives a set  $D \subset M$  with the following properties: (i) It is of dimension  $n-1$ ; (ii) It meets every nontrivial continuous curve drawn on  $M$  (to see this, write the curve locally in terms of  $\varphi_U$  and note that, by continuity, at least one of the coordinates of the curve has to become rational at some time).

Next, let  $x \in Z$ , and let  $y_0, y_1$  be two distinct elements of  $\partial_c \psi(x)$ . By assumption there is a continuous curve  $(y_t)_{0 \leq t \leq 1}$  lying entirely in  $\partial_c\psi(x)$ . For each t, introduce an action-minimizing curve  $(\gamma_t(s))_{0\leq s\leq 1}$ between x and  $y_t$  (s here is the time parameter along the curve). Define  $m_t := \gamma_t(1/2)$ . This is a continuous path, nontrivial (otherwise  $\gamma_0(1/2) = \gamma_1(1/2)$ , but two minimizing trajectories starting from x cannot cross in their middle, or they have to coincide at all times by  $(9.1)$ . So there has to be some t such that  $y_t \in D$ . Moreover, the map F constructed above satisfies  $F(y_t) = x$  for all t. It follows that  $x \in F(D)$ . (See Figure 9.1.)

As a conclusion,  $Z \subset F(D)$ . Since D is of Hausdorff dimension  $n-1$ <br>d F is  $\beta$ -Hölder, the dimension of  $F(D)$  is at most  $(n-1)/\beta$ .  $\square$ and F is β-Hölder, the dimension of  $F(D)$  is at most  $(n-1)/\beta$ .

Image /page/3/Figure/2 description: The image displays a diagram with several curved lines originating from a point labeled 'x'. These lines fan out and curve upwards towards the right. Two thicker, curved lines are highlighted within this fan. One thick line is labeled 'm0' and the other is labeled 'm1'. The upper boundary of the fan is marked by a thick line labeled 'y0', and the lower boundary is marked by a thick line labeled 'y1'. The overall impression is of a set of paths or trajectories diverging from a single point.

Fig. 9.1. Scheme of proof for Theorem 9.2. Here there is a curve  $(y_t)_{0 \leq t \leq 1}$  lying entirely in  $\partial_c \psi(x)$ , and there is a nontrivial path  $(m_t)_{0 \leq t \leq 1}$  obtained by taking the midpoint between x and y<sub>t</sub>. This path has to meet D; but its image by  $\gamma(1/2) \mapsto \gamma(0)$ is  $\{x\}$ , so  $x \in F(D)$ .

Now come the consequences in terms of Monge transport.

Corollary 9.3 (Solution of the Monge problem, I). Let M be a Riemannian manifold, let c be a cost function on  $M \times M$ , with associated cost functional C, and let  $\mu$ ,  $\nu$  be two probability measures on M. Assume that:

- (i)  $C(\mu, \nu) < +\infty;$
- (ii) the assumptions of Theorem 9.2 are satisfied;
- (iii)  $\mu$  gives zero probability to sets of dimension at most  $(n-1)/\beta$ .

Then there is a unique (in law) optimal coupling  $(x, y)$  of  $\mu$  and  $\nu$ ; it is deterministic, and characterized (among all couplings of  $(\mu, \nu)$ ) by the existence of a c-convex function  $\psi$  such that

$$
y \in \partial_c \psi(x) \qquad almost \ surely. \tag{9.2}
$$

Equivalently, there is a unique optimal transport plan  $\pi$ ; it is determin*istic, and characterized by the existence of a c-convex*  $\psi$  *such that* (9.2) holds true  $\pi$ -almost surely.

Proof of Corollary 9.3. The conclusion is obtained by just putting together Theorems 9.2 and 5.30. □

We have now solved the Monge problem in an absolutely painless way; but under what assumptions? At least we can treat the important cost function  $c(x,y) = -x \cdot y$ . Indeed the notion of c-convexity reduces to plain convexity (plus lower semicontinuity), and the c-subdifferential of a convex function  $\psi$  is just its usual subdifferential, which I shall denote by  $\partial \psi$ . Moreover, under an assumption of finite second moments, for the Monge problem this cost is just as good as the usual squared Euclidean distance, since  $|x - y|^2 = |x|^2 - 2x \cdot y + |y|^2$ , and  $\int (|x|^2 + |y|^2) d\pi(x, y)$  is independent of the choice of  $\pi \in \Pi(\mu, \nu)$ . Particular as it may seem, this case is one of the most important for applications, so I shall state the result as a separate theorem.

Theorem 9.4 (Monge problem for quadratic cost, first result). Let  $c(x, y) = |x - y|^2$  in  $\mathbb{R}^n$ . Let  $\mu$ ,  $\nu$  be two probability measures on  $\mathbb{R}^n$  such that

$$
\int |x|^2 \, d\mu(x) + \int |y|^2 \, d\nu(y) < +\infty \tag{9.3}
$$

and  $\mu$  does not give mass to sets of dimension at most n – 1. (This is true in particular if  $\mu$  is absolutely continuous with respect to the Lebesgue measure.) Then there is a unique (in law) optimal coupling  $(x, y)$  of  $\mu$  and  $\nu$ ; it is deterministic, and characterized, among all couplings of  $(\mu, \nu)$ , by the existence of a lower semicontinuous convex function  $\psi$  such that

$$
y \in \partial \psi(x) \qquad almost \ surely.
$$
 (9.4)

In other words, there is a unique optimal transference  $\pi$ ; it is a Monge transport plan, and it is characterized by the existence of a lower semicontinuous convex function  $\psi$  whose subdifferential contains  $\text{Spt } \pi$ .

**Remark 9.5.** The assumption that  $\mu$  does not give mass to sets of dimension at most  $n-1$  is optimal for the existence of a Monge coupling, as can be seen by choosing  $\mu = \mathcal{H}^1|_{[0,1] \times \{0\}}$  (the one-dimensional Hausdorff measure concentrated on the segment  $[0,1] \times \{0\}$  in  $\mathbb{R}^2$ , and then  $\nu = (1/2) \mathcal{H}^1|_{[0,1] \times \{-1\} \cup [0,1] \times \{+1\}}$ . (See Figure 9.2.) It is also optimal for the uniqueness, as can be seen by taking  $\mu = (1/2) \mathcal{H}_{\{0\} \times [-1,1]}^1$  and  $\nu = (1/2) \mathcal{H}^1_{[-1,1]\times\{0\}}$ . In fact, whenever  $\mu, \nu \in P_2(\mathbb{R}^n)$  are supported on orthogonal subspaces of  $\mathbb{R}^n$ , then *any* transference plan is optimal! To see this, define a function  $\psi$  by  $\psi = 0$  on Conv(Spt  $\mu$ ),  $\psi = +\infty$  elsewhere; then  $\psi$  is convex lower semicontinuous,  $\psi^* = 0$  on Conv(Spt $\nu$ ), so  $\partial \psi$  contains Spt  $\mu \times S$ pt  $\nu$ , and any transference plan is supported in  $\partial \psi$ .

Image /page/5/Figure/1 description: The image contains two distinct parts. On the left, there are two horizontal lines, one above and one below a thicker horizontal line. Between the top and bottom lines, and crossing the middle line, are five dashed vertical arrows pointing downwards. On the right side of the image, there is a simple cross shape formed by two perpendicular lines intersecting at their midpoints.

Fig. 9.2. The source measure is drawn as a thick line, the target measure as a thin line; the cost function is quadratic. On the left, there is a unique optimal coupling but no optimal Monge coupling. On the right, there are many optimal couplings, in fact any transference plan is optimal.

In the next chapter, we shall see that Theorem 9.4 can be improved in at least two ways: Equation (9.4) can be rewritten  $y = \nabla \psi(x)$ ; and the assumption (9.3) can be replaced by the weaker assumption  $C(\mu,\nu) < +\infty$  (finite optimal transport cost).

Now if one wants to apply Theorem 9.2 to nonquadratic cost functions, the question arises of how to identify those cost functions  $c(x,y)$ which satisfy Assumption (C). Obviously, there might be some geometric obstructions imposed by the domains  $\mathcal X$  and  $\mathcal Y$ : For instance, if  $\mathcal Y$  is a nonconvex subset of  $\mathbb R^n$ , then Assumption (C) is violated even by the quadratic cost function. But even in the whole of, say,  $\mathbb{R}^n$ , Assumption (C) is not a generic condition, and so far there is only a short list of known examples. These include the cost functions  $c(x, y) =$  $\sqrt{1+|x-y|^2}$  on  $\mathbb{R}^n \times \mathbb{R}^n$ , or more generally  $c(x,y) = (1+|x-y|^2)^{p/2}$  $(1 < p < 2)$  on  $B_R(0) \times B_R(0) \subset \mathbb{R}^n \times \mathbb{R}^n$ , where  $R = 1/\sqrt{p-1}$ ; and  $c(x, y) = d(x, y)^2$  on  $S^{n-1} \times S^{n-1}$ , where d is the geodesic distance on the sphere. For such cost functions, the Monge problem can be solved by combining Theorems 8.1, 9.2 and 5.30, exactly as in the proof of Theorem 9.4.

This approach suffers, however, from two main drawbacks: First it seems to be limited to a small number of examples; secondly, the verification of Assumption  $(C)$  is subtle. In the next chapter we shall investigate a more pedestrian approach, which will apply in much greater generality.

I shall end this chapter with a simple example of a cost function which *does not* satisfy Assumption (C).

Proposition 9.6 (Non-connectedness of the c-subdifferential). Let  $p > 2$  and let  $c(x, y) = |x - y|^p$  on  $\mathbb{R}^2 \times \mathbb{R}^2$ . Then there is a c-convex function  $\psi : \mathbb{R}^2 \to \mathbb{R}$  such that  $\partial_c \psi(0)$  is not connected.

*Proof of Proposition 9.6.* For  $t \in [-1, 1]$  define  $y_t = (0, t) \in \mathbb{R}^2$ , and

$$
\eta_t(x) = -c(x, y_t) + c(0, y_t) + \beta(t) = -(x_1^2 + (x_2 - t)^2)^{p/2} + |t|^p + \beta(t),
$$

where  $\beta$  is a smooth even function,  $\beta(0) = 0$ ,  $\beta'(t) > 0$  for  $|t| > 0$ . Further, let  $r > 0$  and  $X_{\pm} = (\pm r, 0)$ . (The fact that the segments  $[X_-, X_+]$  and  $[y_{-1}, y_1]$  are orthogonal is not accidental.) Then  $\eta_t(0) =$  $\beta(t)$  is an increasing function of |t|; while  $\eta_t(X_{\pm}) = -(r^2+t^2)^{p/2} + |t|^p +$  $\beta(t)$  is a decreasing function of |t| if  $0 < \beta'(t) < pt$  [ $(r^2+t^2)^{p/2-1}-t^{p-2}$ ], which we shall assume. Now define  $\psi(x) = \sup \{\eta_t(x); t \in [-1, 1]\}.$  By construction this is a c-convex function, and  $\psi(0) = \beta(1) > 0$ , while  $\psi(X_{\pm}) = \eta_0(X_{\pm}) = -r^p.$ 

We shall check that  $\partial_c \psi(0)$  is not connected. First,  $\partial_c \psi(0)$  is not empty: this can be shown by elementary means or as a consequence of Example 10.20 and Theorem 10.24 in the next chapter. Secondly,  $\partial_c\psi(0) \subset \{(y_1, y_2) \in \mathbb{R}^2; y_1 = 0\}$ : This comes from the fact that all functions  $\eta_t$  are decreasing as a function of |x<sub>1</sub>|. (So  $\psi$  is also nonincreasing in  $|x_1|$ , and if  $(y_1, y_2) \in \partial_c \psi(0,0)$ , then  $(y_1^2 + y_2^2)^{p/2} + \psi(0,0) \le$  $|y_2|^p + \psi(y_1, 0) \le |y_2|^p + \psi(0, 0)$ , which imposes  $y_1 = 0$ .) Obviously,  $\partial_c\psi(0)$  is a symmetric subset of the line  $\{y_1 = 0\}$ . But if  $0 \in \partial_c\psi(0)$ , then  $0 < \psi(0) \leq |X_{\pm}|^p + \psi(X_{\pm}) = 0$ , which is a contradiction. So  $\partial_c\psi(0)$  does not contain 0, therefore it is not connected.

(What is happening is the following. When replacing  $\eta_0$  by  $\psi$ , we have surelevated the origin, but we have kept the points  $(X_{\pm}, \eta_0(X_{\pm}))$ in place, which forbids us to touch the graph of  $\psi$  from below at the origin with a translation of  $\eta_0$ .) □

### Bibliographical notes

It is classical that the image of a set of Hausdorff dimension d by a Lipschitz map is contained in a set of Hausdorff dimension at most d: See for instance [331, p. 75]. There is no difficulty in modifying the proof to show that the image of a set of Hausdorff dimension d by a Hölder- $\beta$  map is contained in a set of dimension at most  $d/\beta$ .

### 224 9 Solution of the Monge problem I: Global approach

The proof of Theorem 9.2 is adapted from a classical argument according to which a real-valued convex function  $\psi$  on  $\mathbb{R}^n$  has a singlevalued subdifferential everywhere out of a set of dimension at most  $n-1$ ; see [11, Theorem 2.2]. The key estimate for the proof of the latter theorem is that  $(\text{Id} + \partial \psi)^{-1}$  exists and is Lipschitz; but this can be seen as a very particular case of the Mather shortening lemma. In the next chapter another line of argumentation for that differentiability theorem, more local, will be provided.

The paternity of Theorem 9.4 is shared by Brenier [154, 156] with Rachev and Rüschendorf [722]; it builds upon earlier work by Knott and Smith [524], who already knew that an optimal coupling lying entirely in the subdifferential of a convex function would be optimal. Brenier rewrote the result as a beautiful polar factorization theorem, which is presented in detail in [814, Chapter 3].

The nonuniqueness statement in Remark 9.5 was formulated by Mc-Cann [613]. Related problems (existence and uniqueness of optimal couplings between measures supported on polygons) are discussed by Gangbo and McCann [400], in relation to problems of shape recognition.

Other forms of Theorem 9.4 appear in Rachev and Rüschendorf [696], in particular an extension to infinite-dimensional separable Hilbert spaces; the proof is reproduced in [814, Second Proof of Theorem 2.9]. (This problem was also considered in [2, 254].) All these arguments are based on duality; then more direct proofs, which do not use the Kantorovich duality explicitly, were found by Gangbo [395], and also Caffarelli [187] (who gives credit to Varadhan for this approach).

A probabilistic approach of Theorem 9.4 was studied by Mikami and Thieullen [628, 630]. The idea is to consider a minimization problem over paths which are not geodesics, but geodesics perturbed by some noise; then let the noise vanish. This is reminiscent of Nelson's approach to quantum mechanics, see the bibliographical notes of Chapters 7 and 23.

McCann [613] extended Theorem 9.4 by removing the assumption of bounded second moment and even the weaker assumption of finite transport cost: Whenever  $\mu$  does not charge sets of dimension  $n-1$ , there exists a unique coupling of  $(\mu, \nu)$  which takes the form  $y = \nabla \Psi(x)$ , where  $\Psi$  is a lower semicontinuous convex function. The tricky part in this statement is the uniqueness. This theorem will be proven in

the next chapter (see Theorem 10.42, Corollary 10.44 and Particular Case 10.45).

Ma, Trudinger and X.-J. Wang [585, Section 7.5] were the first to seriously study Assumption (C); they had the intuition that it was connected to a certain fourth-order differential condition on the cost function which plays a key role in the smoothness of optimal transport. Later Trudinger and Wang [793], and Loeper [570] showed that the above-mentioned differential condition is essentially, under adequate geometric and regularity assumptions, equivalent to Assumption (C). These issues will be discussed in more detail in Chapter 12. (See in particular Proposition 12.15(iii).)

The counterexample in Proposition 9.6 is extracted from [585]. The fact that  $c(x, y) = (1 + |x - y|^2)^{p/2}$  satisfies Assumption (C) on the ball of radius  $1/\sqrt{p-1}$  is also taken from [585, 793]. It is Loeper [571] who discovered that the squared geodesic distance on  $S^{n-1}$  satisfies Assumption (C); then a simplified argument was devised by von Nessi [824].

As mentioned in the end of the chapter, by combining Loeper's result with Theorems 8.1, 9.2 and 5.30, one can mimick the proof of Theorem 9.4 and get the unique solvability of the Monge problem for the quadratic distance on the sphere, as soon as  $\mu$  does not see sets of dimension at most  $n-1$ . Such a theorem was first obtained for general compact Riemannian manifolds by McCann [616], with a completely different argument.

Other examples of cost functions satisfying Assumption (C) will be listed in Chapter 12 (for instance  $|x - y|^{-2}$ , or  $-|x - y|^p/p$  for  $-2 \le p \le 1$ , or  $|x-y|^2 + |f(x) - g(y)|^2$ , where f and g are convex and 1-Lipschitz). But these other examples do not come from a smooth convex Lagrangian, so it is not clear whether they satisfy Assumption (ii) in Theorem 9.2.

In the particular case when  $\nu$  has finite support, one can prove the unique solvability of the Monge problem under much more general assumptions, namely that the cost function is continuous, and  $\mu$  does not charge sets of the form  $\{x; c(x,a) - c(x,b) = k\}$  (where a, b, k are arbitrary), see [261]. This condition was recently used again by Gozlan [429].

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.