# Hierarchical Text-Conditional Image Generation with CLIP Latents

<PERSON><PERSON><PERSON><sup>∗</sup> OpenAI <EMAIL>

Praful<PERSON> Dhariwal<sup>∗</sup> OpenAI <EMAIL>

Alex <PERSON><sup>∗</sup> OpenAI <EMAIL>

Casey Chu<sup>∗</sup> OpenAI <EMAIL>

Mark <NAME_EMAIL>

## Abstract

Contrastive models like CLIP have been shown to learn robust representations of images that capture both semantics and style. To leverage these representations for image generation, we propose a two-stage model: a prior that generates a CLIP image embedding given a text caption, and a decoder that generates an image conditioned on the image embedding. We show that explicitly generating image representations improves image diversity with minimal loss in photorealism and caption similarity. Our decoders conditioned on image representations can also produce variations of an image that preserve both its semantics and style, while varying the non-essential details absent from the image representation. Moreover, the joint embedding space of CLIP enables language-guided image manipulations in a zero-shot fashion. We use diffusion models for the decoder and experiment with both autoregressive and diffusion models for the prior, finding that the latter are computationally more efficient and produce higher-quality samples.

# 1 Introduction

Recent progress in computer vision has been driven by scaling models on large datasets of captioned images collected from the internet [\[10,](#page-18-0) [44,](#page-20-0) [60,](#page-21-0) [39,](#page-20-1) [31,](#page-19-0) [16\]](#page-19-1). Within this framework, CLIP [\[39\]](#page-20-1) has emerged as a successful representation learner for images. CLIP embeddings have a number of desirable properties: they are robust to image distribution shift, have impressive zero-shot capabilities, and have been fine-tuned to achieve state-of-the-art results on a wide variety of vision and language tasks [\[45\]](#page-20-2). Concurrently, diffusion models [\[46,](#page-20-3) [48,](#page-20-4) [25\]](#page-19-2) have emerged as a promising generative modeling framework, pushing the state-of-the-art on image and video generation tasks [\[11,](#page-18-1) [26,](#page-19-3) [24\]](#page-19-4). To achieve best results, diffusion models leverage a guidance technique [\[11,](#page-18-1) [24\]](#page-19-4) which improves sample fidelity (for images, photorealism) at the cost of sample diversity.

In this work, we combine these two approaches for the problem of text-conditional image generation. We first train a diffusion *decoder* to invert the CLIP image *encoder*. Our inverter is non-deterministic, and can produce multiple images corresponding to a given image embedding. The presence of an encoder and its approximate inverse (the decoder) allows for capabilities beyond text-to-image translation. As in GAN inversion [\[62,](#page-21-1) [55\]](#page-21-2), encoding and decoding an input image produces semantically similar output images (Figure [3\)](#page-4-0). We can also interpolate between input images by inverting interpolations of their image embeddings (Figure [4\)](#page-5-0). However, one notable advantage of using the CLIP latent space is the ability to semantically modify images by moving in the direction of any encoded text vector (Figure [5\)](#page-6-0), whereas discovering these directions in GAN latent space involves

<sup>∗</sup>Equal contribution

<span id="page-1-0"></span>Image /page/1/Picture/0 description: A surrealist painting depicts a split portrait of a man, with one half appearing robotic and the other half human. The robotic side features metallic textures, a green circular lens with a white ring, and red oval accents. The human side is a portrait of Salvador Dalí, complete with his signature mustache and intense gaze, rendered in a painterly style with warm tones. Both halves are set against a vibrant yellow background, and the man is wearing a black suit and tie.

vibrant portrait painting of Salvador Dalí with a robotic half face a shiba inu wearing a beret and black turtleneck a close up of a handpalm with leaves growing from it

Image /page/1/Picture/2 description: A Shiba Inu dog wearing a black beret and a red sweater is looking forward. The dog has brown and white fur, with dark eyes and a black nose. The background is a light-colored brick wall.

Image /page/1/Picture/4 description: A close-up shot shows a person's cupped hands holding a small green plant with six leaves radiating from a central stem. The background is dark and out of focus, with hints of green foliage. The lighting highlights the texture of the hands and the vibrant green of the leaves.

Image /page/1/Picture/6 description: An abstract, surrealist painting depicts a coffee machine with a face-like structure. The machine is predominantly orange and appears to be made of metal. It has two round, bulbous elements resembling eyes, and a long, curved, nose-like protrusion. Below the 

an espresso machine that makes coffee from human souls, artstation panda mad scientist mixing sparkling chemicals, artstation a corgi's head depicted as an explosion of a nebula

Image /page/1/Picture/8 description: A panda wearing a lab coat and goggles is holding a flask filled with green liquid. Bubbles are rising from the flask. Another flask with blue liquid is on the table, along with various colorful vials and test tubes. The background is dark and moody.

panda mad scientist mixing sparkling chemicals, artstation

Image /page/1/Picture/10 description: A close-up, digital painting of a corgi's head emerging from a nebula. The corgi has brown and white fur, with its head tilted slightly to the left. Its eyes are dark and expressive. The background is a swirling galaxy of orange, yellow, and blue cosmic dust, with stars scattered throughout. The overall effect is ethereal and majestic.

Image /page/1/Picture/12 description: A dolphin wearing a space helmet and a backpack floats in space next to a ringed planet. The planet has swirling bands of orange, yellow, and blue. The dolphin is white and gray, with its fins extended. The background is dark blue, speckled with stars and nebulae.

Image /page/1/Picture/14 description: A white cat dressed as Napoleon Bonaparte, wearing a red military jacket with gold buttons and epaulets, and a black bicorne hat. The cat is holding a wedge of Swiss cheese in its white-gloved paw. The background is a bright yellow.

a dolphin in an astronaut suit on saturn, artstation a propaganda poster depicting a cat dressed as french emperor<br>
a teddy bear on a skateboard in times square<br>
a teddy bear on a skateboard in times square<br>
a teddy bear o

Image /page/1/Picture/16 description: A teddy bear is riding a skateboard across a crosswalk in a city. The teddy bear is facing away from the camera, and its back is to the viewer. The skateboard is black with yellow wheels. In the background, there are blurred figures of people walking and colorful neon signs.

Figure 1: Selected  $1024 \times 1024$  samples from a production version of our model.

Image /page/2/Figure/0 description: This is a diagram illustrating a text-to-image generation model. On the left, the text prompt "a corgi playing a flame throwing trumpet" is fed into a text encoder. The output of the text encoder is then processed through a CLIP objective, which interacts with an image encoder. The image encoder then generates an image of a corgi playing a trumpet. Below the CLIP objective, the text encoder output is also fed into a prior network, which then feeds into a decoder. The decoder generates a second image of a corgi playing a trumpet, this time with flames emanating from the trumpet.

Figure 2: A high-level overview of unCLIP. Above the dotted line, we depict the CLIP training process, through which we learn a joint representation space for text and images. Below the dotted line, we depict our text-to-image generation process: a CLIP text embedding is first fed to an autoregressive or diffusion prior to produce an image embedding, and then this embedding is used to condition a diffusion decoder which produces a final image. Note that the CLIP model is frozen during training of the prior and decoder.

luck and diligent manual examination. Furthermore, encoding and decoding images also provides us with a tool for observing which features of the image are recognized or disregarded by CLIP.

To obtain a full generative model of images, we combine the CLIP image embedding *decoder* with a *prior* model, which generates possible CLIP image embeddings from a given text caption. We compare our text-to-image system with other systems such as DALL-E [\[40\]](#page-20-5) and GLIDE [\[35\]](#page-20-6), finding that our samples are comparable in quality to GLIDE, but with greater diversity in our generations. We also develop methods for training diffusion priors in latent space, and show that they achieve comparable performance to autoregressive priors, while being more compute-efficient. We refer to our full text-conditional image generation stack as *unCLIP*, since it generates images by inverting the CLIP image encoder.

## 2 Method

Our training dataset consists of pairs  $(x, y)$  of images x and their corresponding captions y. Given an image x, let  $z_i$  and  $z_t$  be its CLIP image and text embeddings, respectively. We design our generative stack to produce images from captions using two components:

- A *prior*  $P(z_i|y)$  that produces CLIP image embeddings  $z_i$  conditioned on captions y.
- A *decoder*  $P(x|z_i, y)$  that produces images x conditioned on CLIP image embeddings  $z_i$  (and optionally text captions  $y$ ).

The decoder allows us to invert images given their CLIP image embeddings, while the prior allows us to learn a generative model of the image embeddings themselves. Stacking these two components yields a generative model  $P(x|y)$  of images x given captions y:

$$
P(x|y) = P(x, zi|y) = P(x|zi, y)P(zi|y).
$$

The first equality holds because  $z_i$  is a deterministic function of x. The second equality holds because of the chain rule. Thus, we can sample from the true conditional distribution  $P(x|y)$  by first sampling  $z_i$  using the prior, and then sampling x using the decoder. In the following sections, we describe our decoder and prior stacks. For training details and hyperparameters, refer to Appendix [C.](#page-22-0)

### 2.1 Decoder

We use diffusion models [\[25,](#page-19-2) [48\]](#page-20-4) to produce images conditioned on CLIP image embeddings (and optionally text captions). Specifically, we modify the architecture described in [Nichol et al.](#page-20-6) [\(2021\)](#page-20-6) by projecting and adding CLIP embeddings to the existing timestep embedding, and by projecting CLIP embeddings into four extra tokens of context that are concatenated to the sequence of outputs from the GLIDE text encoder. We retained the text conditioning pathway present in the original GLIDE model, hypothesizing that it could allow the diffusion model to learn aspects of natural language that CLIP fails to capture (e.g. variable binding), but find that it offers little help in this regard (Section [7\)](#page-15-0).

While we can sample from the conditional distribution of the decoder directly, past work using diffusion models shows using guidance on the conditioning information [\[11,](#page-18-1) [24,](#page-19-4) [35\]](#page-20-6) improves sample quality a lot. We enable classifier-free guidance [\[24\]](#page-19-4) by randomly setting the CLIP embeddings to zero (or a learned embedding) 10% of the time, and randomly dropping the text caption 50% of the time during training.

To generate high resolution images, we train two diffusion upsampler models [\[34,](#page-20-7) [43\]](#page-20-8): one to upsample images from  $64\times64$  to  $256\times256$  resolution, and another to further upsample those to  $1024\times1024$  resolution. To improve the robustness of our upsamplers, we slightly corrupt the conditioning images during training. For the first upsampling stage, we use gaussian blur [\[43\]](#page-20-8), and for the second, we use a more diverse BSR degradation [\[42,](#page-20-9) [59\]](#page-21-3). To reduce training compute and improve numerical stability, we follow [Rombach et al.](#page-20-9) [\[42\]](#page-20-9) and train on random crops of images that are one-fourth the target size. We use only spatial convolutions in the model (i.e., no attention layers) and at inference time directly apply the model at the target resolution, observing that it readily generalizes to the higher resolution. We found no benefit from conditioning the upsamplers on the caption, and use unconditional ADMNets [\[11\]](#page-18-1) with no guidance.

### 2.2 Prior

While a decoder can invert CLIP image embeddings  $z_i$  to produce images x, we need a prior model that produces  $z_i$  from captions y to enable image generations from text captions. We explore two different model classes for the prior model:

- Autoregressive (AR) prior: the CLIP image embedding  $z_i$  is converted into a sequence of discrete codes and predicted autoregressively conditioned on the caption y.
- *Diffusion* prior: The continuous vector  $z_i$  is directly modelled using a Gaussian diffusion model conditioned on the caption y.

In addition to the caption, we can condition the prior on the CLIP text embedding  $z_t$  since it is a deterministic function of the caption. To improve sample quality we also enable sampling using classifier-free guidance for both the AR and diffusion prior, by randomly dropping this text conditioning information 10% of the time during training.

To train and sample from the AR prior more efficiently, we first reduce the dimensionality of the CLIP image embeddings  $z_i$  by applying Principal Component Analysis (PCA) [\[37\]](#page-20-10). In particular, we find that the rank of the CLIP representation space is drastically reduced when training CLIP with SAM [\[15\]](#page-18-2) while slightly improving evaluation metrics. We are able to preserve nearly all of the information<sup>[2](#page-3-0)</sup> by retaining only 319 principal components out of the original 1,024. After applying PCA, we order the principal components by decreasing eigenvalue magnitude, quantize each of the 319 dimensions into 1,024 discrete buckets, and

<span id="page-3-0"></span> $2$ I.e., less than 1% average mean-squared error in reconstructing the image representations.

<span id="page-4-0"></span>Image /page/4/Picture/0 description: The image displays a grid of nine variations of the OpenAI logo, each set against a colorful, gradient background. To the left of the logo grid, there is a collage of paintings in the style of Salvador Dalí, featuring melting clocks and surreal landscapes. The top painting is a single, larger rendition of this theme, while the bottom section is divided into a 3x3 grid of smaller, similar paintings.

Figure 3: Variations of an input image by encoding with CLIP and then decoding with a diffusion model. The variations preserve both semantic information like presence of a clock in the painting and the overlapping strokes in the logo, as well as stylistic elements like the surrealism in the painting and the color gradients in the logo, while varying the non-essential details.

predict the resulting sequence using a Transformer [\[53\]](#page-21-4) model with a causal attention mask. This results in a threefold reduction in the number of tokens predicted during inference, and improves training stability.

We condition the AR prior on the text caption and the CLIP text embedding by encoding them as a prefix to the sequence. Additionally, we prepend a token indicating the (quantized) dot product between the text embedding and image embedding,  $z_i \cdot z_t$ . This allows us to condition the model on a higher dot product, since higher text-image dot products correspond to captions which better describe the image. In practice, we find it beneficial to sample the dot product from the top half of the distribution.<sup>[3](#page-4-1)</sup>

For the diffusion prior, we train a decoder-only Transformer with a causal attention mask on a sequence consisting of, in order: the encoded text, the CLIP text embedding, an embedding for the diffusion timestep, the noised CLIP image embedding, and a final embedding whose output from the Transformer is used to predict the unnoised CLIP image embedding. We choose not to condition the diffusion prior on  $z_i \cdot z_t$  like in the AR prior; instead, we improve quality during sampling time by generating two samples of  $z_i$  and selecting the one with a higher dot product with  $z_t$ . Instead of using the  $\epsilon$ -prediction formulation from [Ho et al.](#page-19-2) [\[25\]](#page-19-2), we find it better to train our model to predict the unnoised  $z_i$  directly, and use a mean-squared error loss on this prediction:

$$
L_{\text{prior}} = \mathbb{E}_{t \sim [1, T], z_i^{(t)} \sim q_t} \big[ \| f_\theta(z_i^{(t)}, t, y) - z_i \|^2 \big]
$$

<span id="page-4-1"></span><sup>&</sup>lt;sup>3</sup>We swept over percentiles 50%, 70%, 85%, 95% and found 50% to be optimal in all experiments.

<span id="page-5-0"></span>Image /page/5/Picture/0 description: The image displays a grid of artworks. The top section features a collage of images inspired by Van Gogh's "Starry Night" interspersed with photographs of corgi dogs. The middle section showcases a collection of ceramic pottery shaped like fish and teapots, with intricate patterns. The bottom section presents a series of black and white geometric sculptures, possibly Mobius strips or similar topological forms, rendered with detailed textures and patterns.

Figure 4: Variations between two images by interpolating their CLIP image embedding and then decoding with a diffusion model. We fix the decoder seed across each row. The intermediate variations naturally blend the content and style from both input images.

## 3 Image Manipulations

Our approach allows us to encode any given image x into a bipartite latent representation  $(z_i, x_T)$  that is sufficient for the decoder to produce an accurate reconstruction. The latent  $z_i$  describes the aspects of the image that are recognized by CLIP, while the latent  $x_T$  encodes all of the residual information necessary for the decoder to reconstruct  $x$ . The former is obtained by simply encoding the image with the CLIP image encoder. The latter is obtained by applying DDIM inversion (Appendix F in [\[11\]](#page-18-1)) to x using the decoder, while conditioning on  $z_i$ . We describe three different kinds of manipulations that are enabled by this bipartite representation.

### 3.1 Variations

Given an image x, we can produce related images that share the same essential content but vary in other apects, such as shape and orientation (Figure [3\)](#page-4-0). To do this, we apply the decoder to the bipartite representation  $(z_i, x_T)$  using DDIM with  $\eta > 0$  for sampling. With  $\eta = 0$ , the decoder becomes deterministic and will reconstruct the given image x. Larger values of  $\eta$  introduce stochasticity into successive sampling steps, resulting in variations that are perceptually "centered" around the original image x. As  $\eta$  increases, these variations tell us what information was captured in the CLIP image embedding (and thus is preserved across samples), and what was lost (and thus changes across the samples).

<span id="page-6-0"></span>Image /page/6/Picture/0 description: The image displays a sequence of seven images, transitioning from a realistic photograph of a white cat wearing a suit and tie to an anime-style drawing of a white cat with spiky blonde hair, a suit, and a tie, resembling a Super Saiyan. The first image is a photo of a white cat in a dark gray chair, wearing a dark tie. The subsequent images show the cat gradually transforming into an anime character. The final images depict the cat with increasingly exaggerated anime features, including fiery blonde hair and intense yellow eyes, seated in a chair that changes from dark gray to orange. The text below the images reads "a photo of a cat → an anime drawing of a super saiyan cat, artstation."

**ITLE THE** 

Image /page/6/Picture/2 description: The image contains text that reads "a photo of a victorian house" followed by an arrow and then "a photo of a modern house".

Image /page/6/Picture/3 description: The image displays a sequence of lion faces, transitioning from an adult lion to a lion cub. The first two images show an adult lion with a mane, looking forward. The subsequent images show younger lions, progressively becoming smaller and appearing as cubs. The text below the images reads "a photo of an adult lion → a photo of lion cub", indicating the progression shown.

Image /page/6/Picture/4 description: A panoramic view of a forest landscape transitioning from winter to autumn. The left side of the image depicts a snowy scene with bare trees covered in frost and snow, under a soft pink and white sky. The right side transitions into an autumn forest with trees displaying vibrant colors of orange, yellow, and red foliage, with some trees still retaining their leaves. The ground on the right is covered in fallen leaves and reddish undergrowth. The sky remains a soft gradient of pink and white across the entire panorama.

a photo of a landscape in winter  $\rightarrow$  a photo of a landscape in fall

Figure 5: Text diffs applied to images by interpolating between their CLIP image embeddings and a normalised difference of the CLIP text embeddings produced from the two descriptions. We also perform DDIM inversion to perfectly reconstruct the input image in the first column, and fix the decoder DDIM noise across each row.

### 3.2 Interpolations

It is also possible to blend two images  $x_1$  and  $x_2$  for variations (Figure [4\)](#page-5-0), traversing all of the concepts in CLIP's embedding space that occur between them. To do this, we rotate between their CLIP embeddings  $z_{i_1}$ and  $z_{i_2}$  using spherical interpolation, yielding intermediate CLIP representations  $z_{i_\theta} = \text{slerp}(z_{i_1}, z_{i_2}, \theta)$ as  $\theta$  is varied from 0 to 1. There are two options for producing the intermediate DDIM latents along the trajectory. The first option involves interpolating between their DDIM inverted latents  $x_{T_1}$  and  $x_{T_2}$  (by setting  $x_{T_{\theta}} = \text{slerp}(x_{T_1}, x_{T_2}, \theta)$ , which yields a single trajectory whose endpoints reconstruct  $x_1$  and  $x_2$ . The second option involves fixing the DDIM latent to a randomly-sampled value for all interpolates in the trajectory. This results in an infinite number of trajectories between  $x_1$  and  $x_2$ , though the endpoints of these trajectories will generally no longer coincide with the original images. We use this approach in Figure [4.](#page-5-0)

### 3.3 Text Diffs

A key advantage of using CLIP compared to other models for image representations is that it embeds images and text to the same latent space, thus allowing us to apply language-guided image manipulations (i.e., text diffs), which we show in Figure [5.](#page-6-0) To modify the image to reflect a new text description  $y$ , we first obtain its CLIP text embedding  $z_t$ , as well as the CLIP text embedding  $z_{t_0}$  of a caption describing the current image<sup>[4](#page-6-1)</sup>. We then compute a *text diff* vector  $z_d = norm(z_t - z_{t_0})$  from these by taking their difference and

<span id="page-6-1"></span><sup>&</sup>lt;sup>4</sup>Instead of a description of the current image, we also experimented with using a dummy caption like "a photo" for the baseline, or removing it altogether. These also worked well.

<span id="page-7-0"></span>Image /page/7/Picture/0 description: The image displays a grid of apples, with the top row showing three individual apples. The first apple is plain, the second has a white label with "iPod" written on it, and the third has a white label with "PIZZA" written on it. The bottom two rows contain multiple smaller images of apples, some with labels such as "Ppok", "Pocle", "PnbI", "APA", "Prizo", "PIZA", "Piot", "P,o", "PuPO", "IDIZA", "PPITE", "Piz", "PPJOE", "Polri", "PMAZ", and "PIZZA". Below the grid, there are three sets of text indicating the percentage of recognition for "Granny Smith", "iPod", and "Pizza" for each of the three main apples shown in the top row. The first set reads: Granny Smith: 100%, iPod: 0%, Pizza: 0%. The second set reads: Granny Smith: 0.02%, iPod: 99.98%, Pizza: 0%. The third set reads: Granny Smith: 94.33%, iPod: 0%, Pizza: 5.66%.

Figure 6: Variations of images featuring typographic attacks [\[20\]](#page-19-5) paired with the CLIP model's predicted probabilities across three labels. Surprisingly, the decoder still recovers Granny Smith apples even when the predicted probability for this label is near 0%. We also find that our CLIP model is slightly less susceptible to the "pizza" attack than the models investigated in [\[20\]](#page-19-5).

normalizing. Now, we can rotate between the image CLIP embedding  $z_i$  and the text diff vector  $z_d$  using spherical interpolation, yielding intermediate CLIP representations  $z_{\theta} = \text{slerp}(z_i, z_d, \theta)$ , where  $\theta$  is increased linearly from 0 to a maximum value that is typically in [0.25, 0.50]. We produce the final outputs by decoding the interpolates  $z_{\theta}$ , fixing the base DDIM noise to  $x_T$  throughout the entire trajectory.

<span id="page-7-1"></span>

## 4 Probing the CLIP Latent Space

Our decoder model provides a unique opportunity to explore CLIP latent space by allowing us to directly visualize what the CLIP image encoder is seeing. As an example use case, we can revisit cases where CLIP makes incorrect predictions, such as typographic attacks [\[20\]](#page-19-5). In these adversarial images, a piece of text is overlayed on top of an object, which causes CLIP to predict the object described by the text rather than the object depicted in the image. This piece of text essentially hides the original object in terms of output probabilities. In Figure [6,](#page-7-0) we show an example of this attack from [\[20\]](#page-19-5), wherein an apple can be misclassified as an iPod. Surprisingly, we find that our decoder still generates pictures of apples with high probability even though the predicted probability of "Granny Smith" is near zero. Even more notable, the model never produces pictures of iPods, despite the very high relative predicted probability of this caption.

<span id="page-8-0"></span>Image /page/8/Picture/0 description: The image is a collage of three rows of images. The top row features close-ups of food items, including sliced potatoes, olives, tomatoes, and basil. The middle row displays a series of cityscape images, showcasing modern skyscrapers and urban architecture under a cloudy sky. The bottom row presents pastoral scenes with rolling green hills, mountains in the background, and cows grazing in the fields. The overall arrangement suggests a comparison or collection of diverse visual content.

Figure 7: Visualization of reconstructions of CLIP latents from progressively more PCA dimensions (20, 30, 40, 80, 120, 160, 200, 320 dimensions), with the original source image on the far right. The lower dimensions preserve coarse-grained semantic information, whereas the higher dimensions encode finer-grained details about the exact form of the objects in the scene.

PCA reconstructions offer another tool for probing the structure of the CLIP latent space. In Figure [7,](#page-8-0) we take the CLIP image embeddings of a handful of source images and reconstruct them with progressively more PCA dimensions, and then visualize the reconstructed image embeddings using our decoder with DDIM on a fixed seed. This allows us to see what semantic information the different dimensions encode. We observe that the early PCA dimensions preserve coarse-grained semantic information such as what types of objects are in the scene, whereas the later PCA dimensions encode finer-grained detail such as the shapes and exact form of the objects. For example, in the first scene, the earlier dimensions seem to encode that there is food and perhaps a container present, whereas the later dimensions encode tomatoes and a bottle specifically. Figure [7](#page-8-0) also serves as a visualization of what the AR prior is modeling, since the AR prior is trained to explicitly predict these principal components in this order.

## 5 Text-to-Image Generation

### 5.1 Importance of the Prior

Although we train a prior to generate CLIP image embeddings from captions, the prior is not strictly necessary for caption-to-image generation. For instance, our decoder can condition on both CLIP image embeddings and captions, but the CLIP image embedding is dropped 5% of the time during training in order to enable classifier-free guidance. Therefore, at sampling time, we can condition on only the caption, although this underperforms a model trained fully in this way (this model is GLIDE, and we do a thorough comparison with GLIDE in Sections [5.2](#page-9-0) and [5.3\)](#page-11-0). Another possibility is to feed the decoder the CLIP text embedding as if it were an image embedding, as previously observed [\[61,](#page-21-5) [54\]](#page-21-6). The first two rows of Figure [8](#page-9-1) depicts samples obtained in these two ways; the third row depicts samples obtained with a prior. Conditioning the decoder on just the caption is clearly worst, but conditioning on text embeddings zero-shot does produce reasonable results. Building on this observation, another approach would be to train the decoder to condition on CLIP text embeddings [\[9\]](#page-18-3) instead of CLIP image embeddings (although we would lose the capabilities mentioned in Section [4\)](#page-7-1).

To quantify the effectiveness of these alternate approaches, we train two models: a small decoder conditioned on CLIP text embeddings, and a small unCLIP stack (diffusion prior and decoder). We then compare samples from the text-embedding decoder, samples from the unCLIP stack, and samples obtained from feeding text

<span id="page-9-1"></span>Image /page/9/Figure/0 description: The image is a grid of nine images, arranged in three rows and three columns. The first row contains images of people playing baseball, a person wearing a hat, a hedgehog, a motorcycle, and a pair of black boots. The second row contains images of a crowd of baseball players, an oil painting of a corgi wearing a party hat, a hedgehog using a calculator, a motorcycle parked in a parking lot, and a wire rack holding several pairs of glasses and sunglasses. The third row contains images of baseball players from behind, a corgi wearing a party hat, a hedgehog using a calculator, two motorcycles, and a wire rack holding several pairs of shoes and sandals. The text below the images provides captions and descriptions for each image. The captions are: "A group of baseball players is crowded at the mound.", "an oil painting of a corgi wearing a party hat", "a hedgehog using a calculator", "A motorcycle parked in a parking space next to another motorcycle.", and "This wire metal rack holds several pairs of shoes and sandals."

Figure 8: Samples using different conditioning signals for the *same* decoder. In the first row, we pass the text caption to the decoder, and pass a zero vector for the CLIP embedding. In the second row, we pass both the text caption and the CLIP text embedding of the caption. In the third row, we pass the text and a CLIP image embedding generated by an autoregressive prior for the given caption. Note that this decoder is only trained to do the text-to-image generation task (without the CLIP image representation) 5% of the time.

embeddings to the unCLIP decoder zero-shot, sweeping across guidance scales for all models. We find that these approaches respectively score FIDs of 9.16, 7.99, and 16.55 on a test set, suggesting the unCLIP approach is best. We also run human evaluations comparing the first two settings, sweeping over sampling hyperparameters for each using our human evaluation proxy model (Appendix [A\)](#page-22-1). We find that humans prefer the full unCLIP stack 57.0%  $\pm$  3.1% of the time for photorealism and 53.1%  $\pm$  3.1% of the time for caption similarity.

Given the importance of the prior, it is worth evaluating different approaches for training it. We compare both the AR and diffusion priors throughout our experiments. In all cases (Sections [5.2,](#page-9-0) [5.4,](#page-12-0) and [5.5\)](#page-12-1), we find that the diffusion prior outperforms the AR prior for comparable model size and reduced training compute.

<span id="page-9-0"></span>

### 5.2 Human Evaluations

We observe in Figure [1](#page-1-0) that unCLIP is capable of synthesizing complex, realistic images. While we can compare sample quality to past models using FID, it is not always aligned with human judgment. To better gauge the generation capabilities of our system, we conduct systematic human evaluations comparing unCLIP to GLIDE for photorealism, caption similarity, and sample diversity.

We follow the protocol of [Ramesh et al.,](#page-20-5) [Nichol et al.](#page-20-6) [\[40,](#page-20-5) [35\]](#page-20-6) for the first two evaluations: for photorealism, users are presented with pairs of images and must choose which looks more photorealistic; for caption

<span id="page-10-1"></span>Image /page/10/Figure/0 description: This image displays a grid of images featuring bouquets of red roses in vases. The grid is organized into four rows, labeled 1.0, 2.0, 3.0, and 4.0 from top to bottom. Each row contains six images. The first three columns of images are labeled 'unCLIP' at the bottom, and the last three columns are labeled 'GLIDE'. The roses vary in their arrangement and the types of vases used, with some vases being blue and others green. The background settings also differ, showing indoor environments with tables and some with windows. The overall presentation compares different image generation results for bouquets of roses.

Figure 9: Samples when increasing guidance scale for both unCLIP and GLIDE, using the prompt, "A green vase filled with red roses sitting on top of table." For unCLIP, we fix the latent vectors sampled from the prior, and only vary the guidance scale of the decoder. For both models, we fix the diffusion noise seed for each column. Samples from unCLIP improve in quality (more realistic lighting and shadows) but do not change in content as we increase guidance scale, preserving semantic diversity even at high decoder guidance scales.

| unCLIP Prior | Photorealism       | Caption Similarity | Diversity          |
|--------------|--------------------|--------------------|--------------------|
| AR.          | $47.1\% \pm 3.1\%$ | $41.1\% \pm 3.0\%$ | $62.6\% \pm 3.0\%$ |
| Diffusion    | $48.9\% \pm 3.1\%$ | $45.3\% \pm 3.0\%$ | $70.5\% \pm 2.8\%$ |

<span id="page-10-0"></span>Table 1: Human evaluations comparing unCLIP to GLIDE. We compare to both the AR and diffusion prior for unCLIP. Reported figures are 95% confidence intervals of the probability that the unCLIP model specified by the row beats GLIDE. Sampling hyperparameters for all models were swept to optimize an automated proxy for human photorealism evaluations.

similarity, users are additionally prompted with a caption, and must choose which image better matches the caption. In both evaluations, there is a third "Not sure" option. For diversity, we propose a new evaluation protocol in which humans are presented with two  $4 \times 4$  grids of samples and must choose which is more diverse (with a third option, "Not sure"). For this evaluation, we produce sample grids using 1,000 captions from the MS-COCO validation set, and always compare sample grids for the same caption. Before running human comparisons, we swept over sampling hyperparameters for each model using a CLIP linear probe trained to be a proxy for human photorealism evaluations (Appendix [A\)](#page-22-1). These hyperparameters are fixed across all three types of evaluation.

We present our results in Table [1.](#page-10-0) In general, the diffusion prior performs better than the AR prior in pairwise comparisons against GLIDE. We find that humans still slightly prefer GLIDE to unCLIP in terms of photorealism, but the gap is very small. Even with similar photorealism, unCLIP is strongly preferred over GLIDE in terms of diversity, highlighting one of its benefits.

<span id="page-11-1"></span>Image /page/11/Figure/0 description: A line graph shows the frequency unCLIP was preferred over GLIDE, plotted against the GLIDE guidance scale. The x-axis ranges from 1.0 to 3.0, and the y-axis ranges from 20% to 80%. Three lines are plotted: blue for 'in terms of photorealism', orange for 'in terms of caption similarity', and green for 'in terms of diversity'. A dashed line at 50% indicates the point where preference is equal. The blue line starts around 68% at a scale of 1.0, decreases to about 50% at 2.0, and stays around 50% at 3.0. The orange line starts around 73% at 1.0, decreases to about 55% at 1.5, then to about 50% at 2.0, and finally to about 45% at 3.0. The green line starts at about 21% at 1.0, increases to about 42% at 1.5, then to about 58% at 2.0, and reaches about 70% at 3.0. Error bars are shown for each data point.

<span id="page-11-2"></span>Figure 10: When comparing unCLIP (with our best sampling settings) to various settings of guidance scale for GLIDE, unCLIP was preferred by human evaluators on at least one axis among photorealism, caption similarity, and diversity for each comparison. At the higher guidance scales used to generate photorealistic images, unCLIP yields greater diversity for comparable photorealism and caption similarity.

Image /page/11/Figure/2 description: This is a line graph showing the MS-COCO FID score on the y-axis against the Guidance Scale on the x-axis. There are three lines representing different models: GLIDE (blue), unCLIP (AR) (orange), and unCLIP (Diffusion) (green). The GLIDE model shows a significant increase in FID score as the guidance scale increases from 1.0 to 4.0, starting at approximately 13 and ending at approximately 19. The unCLIP (AR) and unCLIP (Diffusion) models show much lower and relatively stable FID scores, fluctuating between approximately 10.5 and 11.5 across the same range of guidance scales.

Figure 11: FID versus guidance scale for unCLIP and GLIDE. For the unCLIP priors, we swept over sampling hyperparameters and fixed to the settings with the best minimum FID.

<span id="page-11-0"></span>

### 5.3 Improved Diversity-Fidelity Trade-off with Guidance

Compared to GLIDE, we qualitatively observe that unCLIP is able to generate more diverse images while leveraging the guidance technique to improve sample quality. To understand why, consider Figure [9](#page-10-1) where we increase guidance scale for both GLIDE and unCLIP. For GLIDE, the semantics (camera angle, color, size) converge as we increase guidance scale, whereas for unCLIP the semantic information of the scene is frozen in the CLIP image embedding and therefore does not collapse when guiding the decoder.

In Section [5.2,](#page-9-0) we observed that unCLIP achieves similar photorealism as GLIDE while maintaining more diversity, but that its caption matching capabilities were slightly worse. It is natural to ask whether GLIDE's guidance scale can be lowered to obtain the same diversity level as unCLIP while maintaining better caption

<span id="page-12-2"></span>

| Model                             | <b>FID</b>  | Zero-shot FID | Zero-shot FID (filt) |
|-----------------------------------|-------------|---------------|----------------------|
| AttnGAN (Xu et al., 2017)         | 35.49       |               |                      |
| DM-GAN (Zhu et al., 2019)         | 32.64       |               |                      |
| DF-GAN (Tao et al., 2020)         | 21.42       |               |                      |
| DM-GAN + CL (Ye et al., 2021)     | 20.79       |               |                      |
| XMC-GAN (Zhang et al., 2021)      | 9.33        |               |                      |
| LAFITE (Zhou et al., 2021)        | 8.12        |               |                      |
| Make-A-Scene (Gafni et al., 2022) | <b>7.55</b> |               |                      |
| DALL-E (Ramesh et al., 2021)      | ~ 28        |               |                      |
| LAFITE (Zhou et al., 2021)        | 26.94       |               |                      |
| GLIDE (Nichol et al., 2021)       | 12.24       |               | 12.89                |
| Make-A-Scene (Gafni et al., 2022) |             |               | 11.84                |
| unCLIP (AR prior)                 |             | 10.63         | 11.08                |
| unCLIP (Diffusion prior)          |             | 10.39         | 10.87                |

Table 2: Comparison of FID on MS-COCO  $256 \times 256$ . We use guidance scale 1.25 for the decoder for both the AR and diffusion prior, and achieve the best results using the diffusion prior.

matching. In Figure [10,](#page-11-1) we conduct a more careful study of this question by performing human evaluations across several GLIDE guidance scales. We find that GLIDE at guidance scale 2.0 is very close to the photorealism and caption similarity of unCLIP, while still producing less diverse samples.

Finally, in Figure [11](#page-11-2) we compute MS-COCO zero-shot FID [\[23\]](#page-19-7) while sweeping over guidance scale for both unCLIP and GLIDE, finding that guidance hurts the FID of unCLIP much less so than for GLIDE. In this evaluation, we fix the guidance scale of the unCLIP prior and only vary the guidance scale of the decoder. This is another indication that guidance hurts the diversity of GLIDE much more than unCLIP, since FID heavily penalizes non-diverse generations.

<span id="page-12-0"></span>

### 5.4 Comparison on MS-COCO

In the text-conditional image generation literature, it has become standard practice to evaluate FID on the MS-COCO [\[28\]](#page-19-8) validation set. We present results on this benchmark in Table [2.](#page-12-2) Like GLIDE and DALL-E, unCLIP is not directly trained on the MS-COCO training set, but can still generalize to the validation set zero-shot. We find that, compared to these other zero-shot models, unCLIP achieves a new state-of-the-art FID of 10.39 when sampling with the diffusion prior. In Figure [12,](#page-13-0) we visually compare unCLIP to various recent text-conditional image generation models on several captions from MS-COCO. We find that, like the other methods, unCLIP produces realistic scenes that capture the text prompts.

<span id="page-12-1"></span>

### 5.5 Aesthetic Quality Comparison

We additionally perform automated aesthetic quality evaluations comparing unCLIP to GLIDE. Our goal with this evaluation is to assess how well each model produces artistic illustrations and photographs. To this end, we generated 512 "artistic" captions using GPT-3 [\[4\]](#page-18-4) by prompting it with captions for existing artwork (both real and AI generated). Next, we trained a CLIP linear probe to predict human aesthetic judgments using the AVA dataset [\[33\]](#page-20-12) (Appendix [A\)](#page-22-1). For each model and set of sampling hyperparameters, we produce four images for each prompt, and report the mean predicted aesthetic judgment over the full batch of 2048 images.

In Figure [13,](#page-14-0) we present results on our aesthetic quality evaluation. We find that guidance improves aesthetic quality for both GLIDE and unCLIP. For unCLIP, we only guide the decoder (we found that guiding the prior hurt results). We also plot the aesthetic quality against Recall<sup>[5](#page-12-3)</sup>, since guidance typically induces a trade-off

<span id="page-12-3"></span><sup>&</sup>lt;sup>5</sup>Recall is computed with respect to the training dataset.

<span id="page-13-0"></span>Image /page/13/Picture/0 description: This image is a grid of 15 images, arranged in 5 rows and 3 columns. The rows are labeled on the left side as "Real Image", "DALL-E", "GLIDE", "Make-A-Scene", "unCLIP", and "unCLIP (prod.)". The columns are not explicitly labeled but contain different scenes. The first column shows images of trains. The second column shows images of people skiing on a snowy mountain. The third column shows images of kitchens. The fourth column shows images of elephants. The fifth column shows images of living areas. Below the grid, there are captions for some of the images: "a green train is coming down the tracks", "a group of skiers are preparing to ski down a mountain.", "a small kitchen with a low ceiling", "a group of elephants walking in muddy water.", and "a living area with a television and a table". The title of the figure is "Figure 12: Random image samples on MS-COCO prompts."

Figure 12: Random image samples on MS-COCO prompts.

<span id="page-14-0"></span>Image /page/14/Figure/0 description: The image contains two plots. The left plot shows the mean AVA prediction on the y-axis and the guidance scale on the x-axis. It displays three lines: GLIDE (blue circles), unCLIP (AR) (orange crosses), and unCLIP (diffusion) (green crosses). The right plot shows recall on the y-axis and mean AVA prediction on the x-axis, with the same three lines representing GLIDE, unCLIP (AR), and unCLIP (diffusion).

Figure 13: Aesthetic quality evaluations comparing GLIDE and unCLIP using 512 auto-generated artistic prompts. We find that both models benefit from guidance, but unCLIP does not sacrifice recall for aesthetic quality.

between fidelity and diversity. Interestingly, we find that guiding unCLIP does not decrease Recall while still improving aesthetic quality according to this metric.

## 6 Related Work

Synthetic image generation is a well studied problem, and most popular techniques for unconditional image generation have also been applied to the text-conditional setting. Many previous works have trained GANs [\[21\]](#page-19-9) on publicly available image captioning datasets to produce text-conditional image samples [\[56,](#page-21-7) [63,](#page-21-8) [49,](#page-20-11) [58,](#page-21-10) [57\]](#page-21-9). Other works have adapted the VQ-VAE approach [\[52\]](#page-21-11) to text-conditional image generation by training autoregressive transformers on sequences of text tokens followed by image tokens [\[40,](#page-20-5) [12,](#page-18-5) [1\]](#page-18-6). Finally, some works have applied diffusion models to the problem, training either continuous [\[35\]](#page-20-6) or discrete [\[22\]](#page-19-10) diffusion models with auxiliary text encoders to handle textual input.

Previous works have leveraged hierarchical generative processes to create high-quality synthetic images. [Razavi et al.](#page-20-13) [\[41\]](#page-20-13) trains a multi-layer discrete autoencoder, allowing them to first sample coarse-grained latent codes and then use this as conditioning information when sampling higher-resolution latent codes. [Child,](#page-18-7) [Vahdat and Kautz](#page-20-14) [\[5,](#page-18-7) [50\]](#page-20-14) generate images using VAEs with a hierarchy of latent codes that increase progressively with resolution. Concurrently with our work, [Gafni et al.](#page-19-6) [\[17\]](#page-19-6) conditions a generative image model on segmentation masks, allowing for a generative process that first samples a semantic map of an image and then conditions the generated image on this information.

The computational benefits of using diffusion to model a latent space has been noted by previous works. [Preechakul et al.](#page-20-15) [\[38\]](#page-20-15) propose an autoencoder framework where diffusion models are used to render latent variables as images, and a second diffusion model is used to generate these latents (similar to our diffusion prior). [Vahdat et al.](#page-21-12) [\[51\]](#page-21-12) use a score-based model for the latent space of a VAE, while [Rombach et al.](#page-20-9) [\[42\]](#page-20-9) use diffusion models on the latents obtained from a VQGAN [\[14\]](#page-18-8) like autoencoder.

Since its release, CLIP [\[39\]](#page-20-1) has been used extensively to steer generative image models towards text prompts. [Galatolo et al.,](#page-19-11) [Patashnik et al.,](#page-20-16) [Murdock,](#page-19-12) [Gal et al.](#page-19-13) [\[19,](#page-19-11) [36,](#page-20-16) [32,](#page-19-12) [18\]](#page-19-13) guide GANs using gradients from a CLIP model. For diffusion models, [Dhariwal and Nichol](#page-18-1) [\[11\]](#page-18-1) introduced classifier guidance as a way to use gradients from a classifier trained on noised images to steer the model towards higher quality generations. [Nichol et al.](#page-20-6) [\[35\]](#page-20-6) train a CLIP model on noised images and guide a text-conditional diffusion model, while [Crowson,](#page-18-9) [Crowson](#page-18-10) [\[7,](#page-18-9) [8\]](#page-18-10) use an unnoised CLIP model to guide unconditional or class-conditional diffusion models. [Ho and Salimans](#page-19-4) [\[24\]](#page-19-4) introduced classifier-free guidance and showed that one can perform guidance

<span id="page-15-1"></span>Image /page/15/Picture/0 description: The image displays a grid of 36 smaller images, each featuring 3D rendered cubes and dice in various arrangements and color combinations, primarily red and blue. The bottom of the image contains text labels: '(a) unCLIP' and '(b) GLIDE', suggesting a comparison between two different methods or models for generating these cube and dice arrangements.

Figure 14: Samples from unCLIP and GLIDE for the prompt "a red cube on top of a blue cube".

implictly from the predictions of the model with and without the conditioning information, thus removing the need for a classifier. [Nichol et al.](#page-20-6) [\[35\]](#page-20-6) showed classifier-free guidance works more favorably than CLIP guidance for text conditional image generation.

Several previous works have trained generative image models that are directly conditioned on CLIP embeddings. [Zhou et al.](#page-21-5) [\[61\]](#page-21-5) condition GAN models on randomly perturbed CLIP image embeddings, finding that these models can generalize to CLIP text embeddings to produce text-conditional images. [Crowson](#page-18-3) [\[9\]](#page-18-3) trained diffusion models conditioned on CLIP text embeddings, allowing for direct text-conditional image generation. [Wang et al.](#page-21-6) [\[54\]](#page-21-6) train an autoregressive generative model conditioned on CLIP image embeddings, finding that it generalizes to CLIP text embeddings well enough to allow for text-conditional image synthesis.

[Bordes et al.](#page-18-11) [\[3\]](#page-18-11) train diffusion models conditioned on image representations from contrastive models. While the diffusion models themselves cannot generate images unconditionally, the authors experimented with a simple approach for two-stage image generation by employing Kernel Density Estimation to sample image representations. By feeding these generated representations to the diffusion model, they can generate images end-to-end in a way similar to our proposed technique. However, our work differs from this in two ways: first, we use multimodal contrastive representations rather than image-only representations; second, we employ much more powerful generative models for the first stage of the generation hierarchy, and these generative models are conditioned on text.

<span id="page-15-0"></span>

## 7 Limitations and Risks

Although conditioning image generation on CLIP embeddings improves diversity, this choice does come with certain limitations. In particular, unCLIP is worse at binding attributes to objects than a corresponding GLIDE model. In Figure [14,](#page-15-1) we find that unCLIP struggles more than GLIDE with a prompt where it must bind two separate objects (cubes) to two separate attributes (colors). We hypothesize that this occurs because the CLIP embedding itself does not explicitly bind attributes to objects, and find that reconstructions from the decoder often mix up attributes and objects, as shown in Figure [15.](#page-16-0) A similar and likely related issue is that unCLIP

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image displays three distinct sections. The top left section features a 3D rendering of two stacked cubes, one red on top of a larger blue cube. The top center section shows a corgi wearing a red party hat and a green bow tie. The top right section contains a glass of milk next to a large chocolate chip cookie. The bottom half of the image is a grid of smaller images. The bottom left grid contains 16 variations of stacked red and blue cubes in different orientations and configurations. The bottom center grid shows multiple corgis, each wearing a party hat and a bow tie, in various poses. The bottom right grid displays multiple glasses of milk alternating with chocolate chip cookies.

Figure 15: Reconstructions from the decoder for difficult binding problems. We find that the reconstructions mix up objects and attributes. In the first two examples, the model mixes up the color of two objects. In the rightmost example, the model does not reliably reconstruct the relative size of two objects.

<span id="page-16-1"></span>Image /page/16/Picture/2 description: The image is a collage of four signs with text. The first sign is a white rectangular sign on a wooden post with the text "Dee'p". The second sign is a blue rectangular sign with a textured frame, displaying the text "Deinp Lerpt:". The third sign is a brown square with rounded corners, featuring the text "Diep Deep" in white script. The fourth sign is a weathered wooden plank with blue lettering that reads "TPONEELH".

Figure 16: Samples from unCLIP for the prompt, "A sign that says deep learning."

struggles at producing coherent text, as illustrated in Figure [16;](#page-16-1) it is possible that the CLIP embedding does not precisely encode spelling information of rendered text. This issue is likely made worse because the BPE encoding we use obscures the spelling of the words in a caption from the model, so the model needs to have independently seen each token written out in the training images in order to learn to render it.

We also note that our stack still has a hard time producing details in complex scenes (Figure [17\)](#page-17-0). We hypothesize that this is a limitation of our decoder hierarchy producing an image at a base resolution of  $64 \times 64$  and then upsampling it. Training our unCLIP decoder at a higher base resolution should be able to alleviate this, at the cost of additional training and inference compute.

As discussed in the GLIDE paper, image generation models carry risks related to deceptive and otherwise harmful content. unCLIP's performance improvements also raise the risk profile over GLIDE. As the technology matures, it leaves fewer traces and indicators that outputs are AI-generated, making it easier to mistake generated images for authentic ones and vice versa. More research is also needed on how the change in architecture changes how the model learns biases in training data.

<span id="page-17-0"></span>Image /page/17/Picture/0 description: A split image shows two dogs in outdoor settings. On the left, a Brittany Spaniel mix with white and brown fur runs towards the camera with its mouth open and tongue out, appearing happy. It is near a body of water with grassy hills in the background. On the right, a brown and white speckled dog with floppy ears is crouched low in a grassy field, looking directly at the camera with a serious expression. The background on the right is a clear blue sky and rolling hills.

(a) A high quality photo of a dog playing in a green field next to a lake.

Image /page/17/Picture/2 description: The image is a split view of a cityscape with tall buildings and bright digital billboards. The left side of the image shows the buildings during the day with a clear blue sky, while the right side shows the same cityscape at night, illuminated by the vibrant lights of the billboards. The perspective is looking up at the skyscrapers, creating a dramatic and immersive view of the urban environment.

(b) A high quality photo of Times Square.

Figure 17: unCLIP samples show low levels of detail for some complex scenes.

The risks of these models should be assessed in relation to the particular deployment context, which includes training data, guardrails in place, the deployment space, and who will have access. A preliminary analysis of these issues in the context of the DALL·E 2 Preview platform (the first deployment of an unCLIP model), can be found in [Mishkin et al.](#page-19-14) [\[30\]](#page-19-14).

## 8 Acknowledgements

We'd like to thank Jong Wook Kim, Hyeonwoo Noh, Alec Radford, Pranav Shyam, and Ilya Sutskever for helpful discussions and contributions to our work. We'd also like to thank Yunxin Jiao for creating several figures used in the paper. We are grateful to the Acceleration and Supercomputing teams at OpenAI for their work on software and hardware infrastructure this project used.

## References

- <span id="page-18-6"></span>[1] Armen Aghajanyan, Bernie Huang, Candace Ross, Vladimir Karpukhin, Hu Xu, Naman Goyal, Dmytro Okhonko, Mandar Joshi, Gargi Ghosh, Mike Lewis, and Luke Zettlemoyer. CM3: A Causal Masked Multimodal Model of the Internet. *[arXiv:2201.07520](https://arxiv.org/abs/2201.07520)*, 2022.
- <span id="page-18-14"></span>[2] Fan Bao, Chongxuan Li, Jun Zhu, and Bo Zhang. Analytic-DPM: an Analytic Estimate of the Optimal Reverse Variance in Diffusion Probabilistic Models. *CoRR*, abs/2201.06503, 2022. URL [https:](https://arxiv.org/abs/2201.06503) [//arxiv.org/abs/2201.06503](https://arxiv.org/abs/2201.06503).
- <span id="page-18-11"></span>[3] Florian Bordes, Randall Balestriero, and Pascal Vincent. High Fidelity Visualization of What Your Self-Supervised Representation Knows About. *[arXiv:2112.09164](https://arxiv.org/abs/2112.09164)*, 2021.
- <span id="page-18-4"></span>[4] Tom B. Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, Sandhini Agarwal, Ariel Herbert-Voss, Gretchen Krueger, Tom Henighan, Rewon Child, Aditya Ramesh, Daniel M. Ziegler, Jeffrey Wu, Clemens Winter, Christopher Hesse, Mark Chen, Eric Sigler, Mateusz Litwin, Scott Gray, Benjamin Chess, Jack Clark, Christopher Berner, Sam McCandlish, Alec Radford, Ilya Sutskever, and Dario Amodei. Language Models are Few-Shot Learners. *[arXiv:2005.14165](https://arxiv.org/abs/2005.14165)*, 2020.
- <span id="page-18-7"></span>[5] Rewon Child. Very Deep VAEs Generalize Autoregressive Models and Can Outperform Them on Images. *[arXiv:2011.10650](https://arxiv.org/abs/2011.10650)*, 2021.
- <span id="page-18-13"></span>[6] Katherine Crowson. AVA Linear Probe. [https://twitter.com/RiversHaveWings/status/](https://twitter.com/RiversHaveWings/status/1472346186728173568?s=20&t=T-HRr3Gw5HRGjQaMDtRe3A) [1472346186728173568?s=20&t=T-HRr3Gw5HRGjQaMDtRe3A](https://twitter.com/RiversHaveWings/status/1472346186728173568?s=20&t=T-HRr3Gw5HRGjQaMDtRe3A), 2021.
- <span id="page-18-9"></span>[7] Katherine Crowson. CLIP guided diffusion HQ 256x256. [https://colab.research.google.com/](https://colab.research.google.com/drive/12a_Wrfi2_gwwAuN3VvMTwVMz9TfqctNj) [drive/12a\\_Wrfi2\\_gwwAuN3VvMTwVMz9TfqctNj](https://colab.research.google.com/drive/12a_Wrfi2_gwwAuN3VvMTwVMz9TfqctNj), 2021.
- <span id="page-18-10"></span>[8] Katherine Crowson. CLIP Guided Diffusion 512x512, Secondary Model Method. [https://twitter.](https://twitter.com/RiversHaveWings/status/1462859669454536711) [com/RiversHaveWings/status/1462859669454536711](https://twitter.com/RiversHaveWings/status/1462859669454536711), 2021.
- <span id="page-18-3"></span>[9] Katherine Crowson. v-diffusion. <https://github.com/crowsonkb/v-diffusion-pytorch>, 2021.
- <span id="page-18-0"></span>[10] Karan Desai and Justin Johnson. VirTex: Learning Visual Representations from Textual Annotations. *[arXiv:2006.06666](https://arxiv.org/abs/2006.06666)*, 2020.
- <span id="page-18-1"></span>[11] Prafulla Dhariwal and Alex Nichol. Diffusion Models Beat GANs on Image Synthesis. *[arXiv:2105.05233](https://arxiv.org/abs/2105.05233)*, 2021.
- <span id="page-18-5"></span>[12] Ming Ding, Zhuoyi Yang, Wenyi Hong, Wendi Zheng, Chang Zhou, Da Yin, Junyang Lin, Xu Zou, Zhou Shao, Hongxia Yang, and Jie Tang. CogView: Mastering Text-to-Image Generation via Transformers. *[arXiv:2105.13290](https://arxiv.org/abs/2105.13290)*, 2021.
- <span id="page-18-12"></span>[13] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, Jakob Uszkoreit, and Neil Houlsby. An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale. *[arXiv:2010.11929](https://arxiv.org/abs/2010.11929)*, 2020.
- <span id="page-18-8"></span>[14] Patrick Esser, Robin Rombach, and Björn Ommer. Taming Transformers for High-Resolution Image Synthesis. *[arXiv:2012.09841](https://arxiv.org/abs/2012.09841)*, 2020.
- <span id="page-18-2"></span>[15] Pierre Foret, Ariel Kleiner, Hossein Mobahi, and Behnam Neyshabur. Sharpness-Aware Minimization for Efficiently Improving Generalization. *[arXiv:2010.01412](https://arxiv.org/abs/2010.01412)*, 2020.

- <span id="page-19-1"></span>[16] Andreas Fürst, Elisabeth Rumetshofer, Viet Thuong Tran, Hubert Ramsauer, Fei Tang, Johannes Lehner, D P Kreil, Michael K Kopp, Günter Klambauer, Angela Bitto-Nemling, and Sepp Hochreiter. CLOOB: Modern Hopfield Networks with InfoLOOB Outperform CLIP, 2022. URL [https://openreview.](https://openreview.net/forum?id=qw674L9PfQE) [net/forum?id=qw674L9PfQE](https://openreview.net/forum?id=qw674L9PfQE).
- <span id="page-19-6"></span>[17] Oran Gafni, Adam Polyak, Oron Ashual, Shelly Sheynin, Devi Parikh, and Yaniv Taigman. Make-A-Scene: Scene-Based Text-to-Image Generation with Human Priors. *[arXiv:2203.13131](https://arxiv.org/abs/2203.13131)*, 2022.
- <span id="page-19-13"></span>[18] Rinon Gal, Or Patashnik, Haggai Maron, Gal Chechik, and Daniel Cohen-Or. StyleGAN-NADA: CLIP-Guided Domain Adaptation of Image Generators. *[arXiv:2108.00946](https://arxiv.org/abs/2108.00946)*, 2021.
- <span id="page-19-11"></span>[19] Federico A. Galatolo, Mario G. C. A. Cimino, and Gigliola Vaglini. Generating images from caption and vice versa via CLIP-Guided Generative Latent Space Search. *[arXiv:2102.01645](https://arxiv.org/abs/2102.01645)*, 2021.
- <span id="page-19-5"></span>[20] Gabriel Goh, Nick Cammarata †, Chelsea Voss †, Shan Carter, Michael Petrov, Ludwig Schubert, Alec Radford, and Chris Olah. Multimodal Neurons in Artificial Neural Networks. *Distill*, 2021. doi: 10.23915/distill.00030. https://distill.pub/2021/multimodal-neurons.
- <span id="page-19-9"></span>[21] Ian J. Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative Adversarial Networks. *[arXiv:1406.2661](https://arxiv.org/abs/1406.2661)*, 2014.
- <span id="page-19-10"></span>[22] Shuyang Gu, Dong Chen, Jianmin Bao, Fang Wen, Bo Zhang, Dongdong Chen, Lu Yuan, and Baining Guo. Vector Quantized Diffusion Model for Text-to-Image Synthesis. *[arXiv:2111.14822](https://arxiv.org/abs/2111.14822)*, 2021.
- <span id="page-19-7"></span>[23] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. GANs Trained by a Two Time-Scale Update Rule Converge to a Local Nash Equilibrium. *Advances in Neural Information Processing Systems 30 (NIPS 2017)*, 2017.
- <span id="page-19-4"></span>[24] Jonathan Ho and Tim Salimans. Classifier-Free Diffusion Guidance. In *NeurIPS 2021 Workshop on Deep Generative Models and Downstream Applications*, 2021. URL [https://openreview.net/](https://openreview.net/forum?id=qw8AKxfYbI) [forum?id=qw8AKxfYbI](https://openreview.net/forum?id=qw8AKxfYbI).
- <span id="page-19-2"></span>[25] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising Diffusion Probabilistic Models. *[arXiv:2006.11239](https://arxiv.org/abs/2006.11239)*, 2020.
- <span id="page-19-3"></span>[26] Jonathan Ho, Chitwan Saharia, William Chan, David J. Fleet, Mohammad Norouzi, and Tim Salimans. Cascaded Diffusion Models for High Fidelity Image Generation. *[arXiv:2106.15282](https://arxiv.org/abs/2106.15282)*, 2021.
- <span id="page-19-15"></span>[27] Diederik P. Kingma and Jimmy Ba. Adam: A Method for Stochastic Optimization. *[arXiv:1412.6980](https://arxiv.org/abs/1412.6980)*, 2014.
- <span id="page-19-8"></span>[28] Tsung-Yi Lin, Michael Maire, Serge Belongie, Lubomir Bourdev, Ross Girshick, James Hays, Pietro Perona, Deva Ramanan, C. Lawrence Zitnick, and Piotr Dollár. Microsoft COCO: Common Objects in Context. *[arXiv:1405.0312](https://arxiv.org/abs/1405.0312)*, 2014.
- <span id="page-19-16"></span>[29] Ilya Loshchilov and Frank Hutter. Decoupled Weight Decay Regularization. *[arXiv:1711.05101](https://arxiv.org/abs/1711.05101)*, 2017.
- <span id="page-19-14"></span>[30] Pamela Mishkin, Lama Ahmad, Miles Brundage, Gretchen Krueger, and Girish Sastry. DALL·E 2 Preview - Risks and Limitations. 2022. URL [https://github.com/openai/dalle-2-preview/](https://github.com/openai/dalle-2-preview/blob/main/system-card.md) [blob/main/system-card.md](https://github.com/openai/dalle-2-preview/blob/main/system-card.md).
- <span id="page-19-0"></span>[31] Norman Mu, Alexander Kirillov, David Wagner, and Saining Xie. SLIP: Self-supervision meets Language-Image Pre-training. *[arXiv:2112.12750](https://arxiv.org/abs/2112.12750)*, 2021.
- <span id="page-19-12"></span>[32] Ryan Murdock. The Big Sleep. [https://twitter.com/advadnoun/status/](https://twitter.com/advadnoun/status/1351038053033406468) [1351038053033406468](https://twitter.com/advadnoun/status/1351038053033406468), 2021.

- <span id="page-20-12"></span>[33] Naila Murray, Luca Marchesotti, and Florent Perronnin. AVA: A large-scale database for aesthetic visual analysis. In *2012 IEEE Conference on Computer Vision and Pattern Recognition*, pages 2408–2415, 2012. doi: 10.1109/CVPR.2012.6247954.
- <span id="page-20-7"></span>[34] Alex Nichol and Prafulla Dhariwal. Improved Denoising Diffusion Probabilistic Models. *[arXiv:2102.09672](https://arxiv.org/abs/2102.09672)*, 2021.
- <span id="page-20-6"></span>[35] Alex Nichol, Prafulla Dhariwal, Aditya Ramesh, Pranav Shyam, Pamela Mishkin, Bob McGrew, Ilya Sutskever, and Mark Chen. GLIDE: Towards Photorealistic Image Generation and Editing with Text-Guided Diffusion Models. *[arXiv:2112.10741](https://arxiv.org/abs/2112.10741)*, 2021.
- <span id="page-20-16"></span>[36] Or Patashnik, Zongze Wu, Eli Shechtman, Daniel Cohen-Or, and Dani Lischinski. StyleCLIP: Text-Driven Manipulation of StyleGAN Imagery. *[arXiv:2103.17249](https://arxiv.org/abs/2103.17249)*, 2021.
- <span id="page-20-10"></span>[37] Karl Pearson. LIII. On lines and planes of closest fit to systems of points in space, November 1901. URL <https://doi.org/10.1080/14786440109462720>.
- <span id="page-20-15"></span>[38] Konpat Preechakul, Nattanat Chatthee, Suttisak Wizadwongsa, and Supasorn Suwajanakorn. Diffusion Autoencoders: Toward a Meaningful and Decodable Representation. *[arXiv:2111.15640](https://arxiv.org/abs/2111.15640)*, 2021.
- <span id="page-20-1"></span>[39] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. Learning Transferable Visual Models From Natural Language Supervision. *[arXiv:2103.00020](https://arxiv.org/abs/2103.00020)*, 2021.
- <span id="page-20-5"></span>[40] Aditya Ramesh, Mikhail Pavlov, Gabriel Goh, Scott Gray, Chelsea Voss, Alec Radford, Mark Chen, and Ilya Sutskever. Zero-Shot Text-to-Image Generation. *[arXiv:2102.12092](https://arxiv.org/abs/2102.12092)*, 2021.
- <span id="page-20-13"></span>[41] Ali Razavi, Aaron van den Oord, and Oriol Vinyals. Generating Diverse High-Fidelity Images with VQ-VAE-2. *[arXiv:1906.00446](https://arxiv.org/abs/1906.00446)*, 2019.
- <span id="page-20-9"></span>[42] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-Resolution Image Synthesis with Latent Diffusion Models. *[arXiv:2112.10752](https://arxiv.org/abs/2112.10752)*, 2021.
- <span id="page-20-8"></span>[43] Chitwan Saharia, Jonathan Ho, William Chan, Tim Salimans, David J. Fleet, and Mohammad Norouzi. Image Super-Resolution via Iterative Refinement. *[arXiv:arXiv:2104.07636](https://arxiv.org/abs/arXiv:2104.07636)*, 2021.
- <span id="page-20-0"></span>[44] Mert Bulent Sariyildiz, Julien Perez, and Diane Larlus. Learning Visual Representations with Caption Annotations. *[arXiv:2008.01392](https://arxiv.org/abs/2008.01392)*, 2020.
- <span id="page-20-2"></span>[45] Sheng Shen, Liunian Harold Li, Hao Tan, Mohit Bansal, Anna Rohrbach, Kai-Wei Chang, Zhewei Yao, and Kurt Keutzer. How Much Can CLIP Benefit Vision-and-Language Tasks? *[arXiv:2107.06383](https://arxiv.org/abs/2107.06383)*, 2021.
- <span id="page-20-3"></span>[46] Jascha Sohl-Dickstein, Eric A. Weiss, Niru Maheswaranathan, and Surya Ganguli. Deep Unsupervised Learning using Nonequilibrium Thermodynamics. *[arXiv:1503.03585](https://arxiv.org/abs/1503.03585)*, 2015.
- <span id="page-20-17"></span>[47] Jiaming Song, Chenlin Meng, and Stefano Ermon. Denoising Diffusion Implicit Models. *[arXiv:2010.02502](https://arxiv.org/abs/2010.02502)*, 2020.
- <span id="page-20-4"></span>[48] Yang Song and Stefano Ermon. Improved Techniques for Training Score-Based Generative Models. *[arXiv:2006.09011](https://arxiv.org/abs/2006.09011)*, 2020.
- <span id="page-20-11"></span>[49] Ming Tao, Hao Tang, Songsong Wu, Nicu Sebe, Xiao-Yuan Jing, Fei Wu, and Bingkun Bao. DF-GAN: Deep Fusion Generative Adversarial Networks for Text-to-Image Synthesis. *[arXiv:2008.05865](https://arxiv.org/abs/2008.05865)*, 2020.
- <span id="page-20-14"></span>[50] Arash Vahdat and Jan Kautz. NVAE: A Deep Hierarchical Variational Autoencoder. *[arXiv:2007.03898](https://arxiv.org/abs/2007.03898)*, 2020.

- <span id="page-21-12"></span>[51] Arash Vahdat, Karsten Kreis, and Jan Kautz. Score-based Generative Modeling in Latent Space. In *Neural Information Processing Systems (NeurIPS)*, 2021.
- <span id="page-21-11"></span>[52] Aaron van den Oord, Oriol Vinyals, and Koray Kavukcuoglu. Neural Discrete Representation Learning. *[arXiv:1711.00937](https://arxiv.org/abs/1711.00937)*, 2017.
- <span id="page-21-4"></span>[53] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N. Gomez, Lukasz Kaiser, and Illia Polosukhin. Attention Is All You Need. *[arXiv:1706.03762](https://arxiv.org/abs/1706.03762)*, 2017.
- <span id="page-21-6"></span>[54] Zihao Wang, Wei Liu, Qian He, Xinglong Wu, and Zili Yi. CLIP-GEN: Language-Free Training of a Text-to-Image Generator with CLIP. *[arXiv:2203.00386](https://arxiv.org/abs/2203.00386)*, 2022.
- <span id="page-21-2"></span>[55] Weihao Xia, Yulun Zhang, Yujiu Yang, Jing-Hao Xue, Bolei Zhou, and Ming-Hsuan Yang. GAN Inversion: A Survey. *[arXiv:2101.05278](https://arxiv.org/abs/2101.05278)*, 2021.
- <span id="page-21-7"></span>[56] Tao Xu, Pengchuan Zhang, Qiuyuan Huang, Han Zhang, Zhe Gan, Xiaolei Huang, and Xiaodong He. AttnGAN: Fine-Grained Text to Image Generation with Attentional Generative Adversarial Networks. *[arXiv:1711.10485](https://arxiv.org/abs/1711.10485)*, 2017.
- <span id="page-21-9"></span>[57] Hui Ye, Xiulong Yang, Martin Takac, Rajshekhar Sunderraman, and Shihao Ji. Improving Text-to-Image Synthesis Using Contrastive Learning. *[arXiv:2107.02423](https://arxiv.org/abs/2107.02423)*, 2021.
- <span id="page-21-10"></span>[58] Han Zhang, Jing Yu Koh, Jason Baldridge, Honglak Lee, and Yinfei Yang. Cross-Modal Contrastive Learning for Text-to-Image Generation. *[arXiv:2101.04702](https://arxiv.org/abs/2101.04702)*, 2021.
- <span id="page-21-3"></span>[59] Kai Zhang, Jingyun Liang, Luc Van Gool, and Radu Timofte. Designing a Practical Degradation Model for Deep Blind Image Super-Resolution. *2021 IEEE/CVF International Conference on Computer Vision (ICCV)*, Oct 2021. doi: 10.1109/iccv48922.2021.00475. URL [http://dx.doi.org/10.1109/](http://dx.doi.org/10.1109/ICCV48922.2021.00475) [ICCV48922.2021.00475](http://dx.doi.org/10.1109/ICCV48922.2021.00475).
- <span id="page-21-0"></span>[60] Yuhao Zhang, Hang Jiang, Yasuhide Miura, Christopher D. Manning, and Curtis P. Langlotz. Contrastive Learning of Medical Visual Representations from Paired Images and Text. *[arXiv:2010.00747](https://arxiv.org/abs/2010.00747)*, 2020.
- <span id="page-21-5"></span>[61] Yufan Zhou, Ruiyi Zhang, Changyou Chen, Chunyuan Li, Chris Tensmeyer, Tong Yu, Jiuxiang Gu, Jinhui Xu, and Tong Sun. LAFITE: Towards Language-Free Training for Text-to-Image Generation. *[arXiv:2111.13792](https://arxiv.org/abs/2111.13792)*, 2021.
- <span id="page-21-1"></span>[62] Jun-Yan Zhu, Philipp Krähenbühl, Eli Shechtman, and Alexei A. Efros. Generative Visual Manipulation on the Natural Image Manifold. *[arXiv:1609.03552](https://arxiv.org/abs/1609.03552)*, 2016.
- <span id="page-21-8"></span>[63] Minfeng Zhu, Pingbo Pan, Wei Chen, and Yi Yang. DM-GAN: Dynamic Memory Generative Adversarial Networks for Text-to-Image Synthesis. *[arXiv:1904.01310](https://arxiv.org/abs/1904.01310)*, 2019.

<span id="page-22-1"></span>

## A Linear Probes for Evaluations

For our evaluations, we leverage two new linear probes on top of a CLIP ViT-L/14 [\[13\]](#page-18-12) model. To automate aesthetic quality evaluations, we follow the procedure used by [Crowson](#page-18-13) [\[6\]](#page-18-13), training a linear regression model on images and mean ratings from the AVA dataset [\[33\]](#page-20-12). To reduce the cost of hyperparameter sweeps before conducting human evaluations, we train a logistic regression model to predict win probabilities between pairs of images. To train this model, we used 15,000 pairwise image comparisons gathered from all of our previous human evaluations. For each comparison i, we computed CLIP image embeddings  $x_i$  and  $y_i$  for the two images in the pair. We then trained a linear model  $f(x)$  such that  $1/(1 + \exp(f(x_i) - f(y_i)))$  approximates the probability that a human prefers the image for  $y_i$ . This can be reduced to a logistic regression problem with inputs equal to  $y_i - x_i$ .

## B Error Bars for Human Evaluation

When computing error bars for human evaluations, we use the normal approximation interval with  $p = 0.95$ . We expect the normal approximation to be accurate for such a large sample size of  $n = 1000$ .

<span id="page-22-0"></span>

## C Training Details

The unCLIP models used for the experiments in this paper were trained with the hyperparameters described below, unless otherwise noted. We additionally trained a production version of unCLIP using similarly sized models but with modified architectures and trained for longer; we include changes to accommodate product and safety requirements (e.g. inpainting, preventing unwanted memorization), and train on a larger dataset that is filtered for aesthetic quality and safety. We report model and training hyperparameters for the paper models in Table [3.](#page-23-0) All models were trained using Adam [\[27\]](#page-19-15) with corrected weight decay [\[29\]](#page-19-16) and momentum  $\beta_1 = 0.9$ .

Our CLIP model uses a ViT-H/16 [\[13\]](#page-18-12) image encoder that consumes  $256 \times 256$  resolution images, and has width 1280 with 32 Transformer [\[53\]](#page-21-4) blocks. The text encoder also follows the architecture described in [Radford et al.](#page-20-1) [\[39\]](#page-20-1): it is a Transformer [\[53\]](#page-21-4) with a causal attention mask, with width 1024 and 24 Transformer blocks. Both models are trained with learning rate  $3 \times 10^{-4}$  and SAM [\[15\]](#page-18-2) with  $\rho = 0.1$ , where the perturbations are applied independently by the replicas, each of which uses batch size 64. The remaining hyperparameters are the same as those reported in [Radford et al.](#page-20-1) [\[39\]](#page-20-1).

When training the encoder, we sample from the CLIP [\[39\]](#page-20-1) and DALL-E [\[40\]](#page-20-5) datasets (approximately 650M images in total) with equal probability. When training the decoder, upsamplers, and prior, we use only the DALL-E dataset [\[40\]](#page-20-5) (approximately 250M images). Incorporating the noisier CLIP dataset while training the generative stack negatively impacted sample quality in our initial evaluations.

Our decoder architecture is the 3.5 billion parameter GLIDE model, with the same architecture and diffusion hyperparameters as in [Nichol et al.](#page-20-6) [\[35\]](#page-20-6). We train with learned sigma and sample with 250 strided sampling steps as in [Nichol and Dhariwal](#page-20-7) [\[34\]](#page-20-7).

We use the ADMNet architecture [\[11\]](#page-18-1) for the upsamplers. In the first upsampling stage, we use a cosine noising schedule, 320 channels and a depth of 3 resblocks per resolution inside the ADMNet. We also apply gaussian blur (kernel size 3, sigma 0.6) as described in [Saharia et al.](#page-20-8) [\[43\]](#page-20-8). In the second upsampling stage, we use a linear noising schedule, 192 channels, a depth of 2 resblocks per resolution, and train with the BSR degradation from [Rombach et al.](#page-20-9) [\[42\]](#page-20-9). Neither upsampler uses attention. To reduce inference time, we use DDIM [\[47\]](#page-20-17) and manually tune the number of steps, with 27 steps for  $256 \times 256$  model, and 15 steps for the  $1024 \times 1024$  model.

For the AR prior, we use a Transformer text encoder with width 2048 and 24 blocks and a decoder with a causal attention mask, width 1664, and 24 blocks. For the diffusion prior, we use a Transformer with width 2048 and 24 blocks, and sample with Analytic DPM [\[2\]](#page-18-14) with 64 strided sampling steps. To reuse hyperparameters tuned for diffusion noise schedules on images from [Dhariwal and Nichol](#page-18-1) [\[11\]](#page-18-1), we scale the CLIP embedding inputs by 17.2 to match the empirical variance of RGB pixel values of ImageNet images scaled to  $[-1, 1]$ .

<span id="page-23-0"></span>

|                          | AR prior  | Diffusion prior | 64           | $64 
ightarrow 256$ | $256 
ightarrow 1024$ |
|--------------------------|-----------|-----------------|--------------|---------------------|-----------------------|
| Diffusion steps          | -         | 1000            | 1000         | 1000                | 1000                  |
| Noise schedule           | -         | cosine          | cosine       | cosine              | linear                |
| Sampling steps           | -         | 64              | 250          | 27                  | 15                    |
| Sampling variance method | -         | analytic $[2]$  | learned [34] | <b>DDIM</b> [47]    | <b>DDIM</b> [47]      |
| Crop fraction            | -         | -               | -            | 0.25                | 0.25                  |
| Model size               | 1B        | 1B              | 3.5B         | 700M                | 300M                  |
| Channels                 | -         | -               | 512          | 320                 | 192                   |
| Depth                    | -         | -               | 3            | 3                   | 2                     |
| Channels multiple        | -         | -               | 1,2,3,4      | 1,2,3,4             | 1,1,2,2,4,4           |
| Heads channels           | -         | -               | 64           | -                   | -                     |
| Attention resolution     | -         | -               | 32,16,8      | -                   | -                     |
| Text encoder context     | 256       | 256             | 256          | -                   | -                     |
| Text encoder width       | 2048      | 2048            | 2048         | -                   | -                     |
| Text encoder depth       | 24        | 24              | 24           | -                   | -                     |
| Text encoder heads       | 32        | 32              | 32           | -                   | -                     |
| Latent decoder context   | 384       | -               | -            | -                   | -                     |
| Latent decoder width     | 1664      | -               | -            | -                   | -                     |
| Latent decoder depth     | 24        | -               | -            | -                   | -                     |
| Latent decoder heads     | 26        | -               | -            | -                   | -                     |
| Dropout                  | -         | -               | 0.1          | 0.1                 | -                     |
| Weight decay             | $4.0e-2$  | $6.0e-2$        | -            | -                   | -                     |
| Batch size               | 4096      | 4096            | 2048         | 1024                | 512                   |
| Iterations               | 1M        | 600K            | 800K         | 1M                  | 1M                    |
| Learning rate            | $1.6e-4$  | $1.1e-4$        | $1.2e-4$     | $1.2e-4$            | $1.0e-4$              |
| Adam $eta_2$            | 0.91      | 0.96            | 0.999        | 0.999               | 0.999                 |
| Adam $\epsilon$          | $1.0e-10$ | $1.0e-6$        | $1.0e-8$     | $1.0e-8$            | $1.0e-8$              |
| EMA decay                | 0.999     | 0.9999          | 0.9999       | 0.9999              | 0.9999                |

Table 3: Hyperparameters for the models

## D Random samples

In Figures [18,](#page-24-0) [19](#page-25-0) and [20](#page-26-0) we show random samples from our production model for some of the prompts from Figure [1.](#page-1-0)

<span id="page-24-0"></span>Image /page/24/Picture/2 description: The image is a grid of 16 portraits, each in a distinct, vibrant, and surreal style, reminiscent of Salvador Dalí. All portraits feature a man with a prominent mustache, and many incorporate mechanical or robotic elements, such as gears, wires, and metallic parts integrated into the face or body. The backgrounds are a mix of bright yellow, blue, and abstract colorful patterns. The overall impression is a collection of surreal, artistic interpretations of a single subject.

Figure 18: Random samples from unCLIP for prompt "Vibrant portrait painting of Salvador Dali with a robotic half face"

<span id="page-25-0"></span>Image /page/25/Picture/0 description: This is a collage of 12 close-up images, each featuring a pair of hands holding or interacting with a plant or leaf. The images are arranged in a 3x4 grid. The plants vary in size, color, and leaf shape, from small seedlings to larger, more mature leaves. The hands also vary in skin tone and are shown in different positions, some cupping the plants, others gently touching the leaves. The backgrounds are mostly dark, emphasizing the plants and hands.

Figure 19: Random samples from unCLIP for prompt "A close up of a handpalm with leaves growing from it."

<span id="page-26-0"></span>Image /page/26/Picture/0 description: The image displays a grid of 16 photographs, each featuring a teddy bear on a skateboard in Times Square, New York City. The teddy bears are depicted in various poses and outfits, riding skateboards on crosswalks and sidewalks. The background of each image shows the bustling environment of Times Square with its bright billboards and crowds of people. The photographs are arranged in a 4x4 grid.

Figure 20: Random samples from unCLIP for prompt "A teddybear on a skateboard in Times Square."