{"table_of_contents": [{"title": "Hierarchical Text-Conditional\nImage Generation with CLIP Latents", "heading_level": null, "page_id": 0, "polygon": [[166.5, 99.0], [444.65625, 99.0], [444.65625, 136.0283203125], [166.5, 136.0283203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.69140625, 292.5], [328.5, 292.5], [328.5, 303.767578125], [282.69140625, 303.767578125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.3828125, 480.75], [191.3994140625, 480.75], [191.3994140625, 491.90625], [106.3828125, 491.90625]]}, {"title": "2 Method", "heading_level": null, "page_id": 2, "polygon": [[90.75, 452.84765625], [150.75, 452.84765625], [150.75, 464.44921875], [90.75, 464.44921875]]}, {"title": "2.1 Decoder", "heading_level": null, "page_id": 3, "polygon": [[90.75, 132.0], [150.98291015625, 132.0], [150.98291015625, 142.892578125], [90.75, 142.892578125]]}, {"title": "2.2 Prior", "heading_level": null, "page_id": 3, "polygon": [[90.6943359375, 396.0], [138.65625, 396.0], [138.65625, 406.0546875], [90.6943359375, 406.0546875]]}, {"title": "3 Image Manipulations", "heading_level": null, "page_id": 5, "polygon": [[90.75, 438.15234375], [220.5, 438.15234375], [220.5, 449.75390625], [90.75, 449.75390625]]}, {"title": "3.1 Variations", "heading_level": null, "page_id": 5, "polygon": [[90.75, 554.16796875], [159.0, 554.16796875], [159.0, 564.99609375], [90.75, 564.99609375]]}, {"title": "3.2 Interpolations", "heading_level": null, "page_id": 6, "polygon": [[90.6943359375, 414.75], [174.9638671875, 414.75], [174.9638671875, 425.390625], [90.6943359375, 425.390625]]}, {"title": "3.3 Text Diffs", "heading_level": null, "page_id": 6, "polygon": [[90.6943359375, 547.5], [156.884765625, 547.5], [156.884765625, 557.6484375], [90.6943359375, 557.6484375]]}, {"title": "4 Probing the CLIP Latent Space", "heading_level": null, "page_id": 7, "polygon": [[90.75, 529.5], [271.7841796875, 529.5], [271.7841796875, 541.40625], [90.75, 541.40625]]}, {"title": "5 Text-to-Image Generation", "heading_level": null, "page_id": 8, "polygon": [[90.75, 436.9921875], [244.5, 436.9921875], [244.5, 448.59375], [90.75, 448.59375]]}, {"title": "5.1 Importance of the Prior", "heading_level": null, "page_id": 8, "polygon": [[90.75, 462.0], [216.75, 462.0], [216.75, 472.18359375], [90.75, 472.18359375]]}, {"title": "5.2 Human Evaluations", "heading_level": null, "page_id": 9, "polygon": [[90.75, 559.96875], [200.25, 559.96875], [200.25, 570.0234375], [90.75, 570.0234375]]}, {"title": "5.3 Improved Diversity-Fidelity Trade-off with Guidance", "heading_level": null, "page_id": 11, "polygon": [[90.544921875, 538.5], [342.0, 538.5], [342.0, 549.52734375], [90.544921875, 549.52734375]]}, {"title": "5.4 Comp<PERSON>on on MS-COCO", "heading_level": null, "page_id": 12, "polygon": [[90.75, 397.93359375], [231.591796875, 397.93359375], [231.591796875, 408.76171875], [90.75, 408.76171875]]}, {"title": "5.5 Aesthetic Quality Comparison", "heading_level": null, "page_id": 12, "polygon": [[90.75, 508.5], [245.25, 508.5], [245.25, 518.9765625], [90.75, 518.9765625]]}, {"title": "6 Related Work", "heading_level": null, "page_id": 14, "polygon": [[90.75, 325.5], [181.5, 325.5], [181.5, 337.21875], [90.75, 337.21875]]}, {"title": "7 Limitations and Risks", "heading_level": null, "page_id": 15, "polygon": [[90.75, 560.25], [222.75, 560.25], [222.75, 572.73046875], [90.75, 572.73046875]]}, {"title": "8 Acknowledgements", "heading_level": null, "page_id": 17, "polygon": [[90.75, 583.5], [210.076171875, 583.5], [210.076171875, 595.546875], [90.75, 595.546875]]}, {"title": "References", "heading_level": null, "page_id": 18, "polygon": [[90.75, 96.0], [148.5, 96.0], [148.5, 106.541015625], [90.75, 106.541015625]]}, {"title": "A Linear Probes for Evaluations", "heading_level": null, "page_id": 22, "polygon": [[90.75, 95.25], [267.0, 95.25], [267.0, 106.927734375], [90.75, 106.927734375]]}, {"title": "B Error Bars for Human Evaluation", "heading_level": null, "page_id": 22, "polygon": [[90.75, 240.75], [287.25, 240.75], [287.25, 251.947265625], [90.75, 251.947265625]]}, {"title": "C Training Details", "heading_level": null, "page_id": 22, "polygon": [[90.75, 309.0], [195.75, 309.0], [195.75, 320.783203125], [90.75, 320.783203125]]}, {"title": "D Random samples", "heading_level": null, "page_id": 24, "polygon": [[88.5, 93.0], [201.75, 93.0], [201.75, 108.0], [88.5, 108.0]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 53], ["Text", 8], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5299, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 27], ["Picture", 9], ["Caption", 9], ["PictureGroup", 8], ["Line", 4], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 10, "llm_error_count": 0, "llm_tokens_used": 5821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 48], ["Text", 6], ["ListItem", 2], ["Figure", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 643, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 43], ["Text", 7], ["SectionHeader", 2], ["ListItem", 2], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 31], ["Text", 2], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 20], ["Text", 2], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 607, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 27], ["Picture", 4], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2485, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 31], ["Text", 2], ["Reference", 2], ["Picture", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 864, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 32], ["Text", 3], ["SectionHeader", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 41], ["Text", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 820, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 30], ["TableCell", 12], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 50], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1538, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["TableCell", 111], ["Line", 44], ["Text", 6], ["Reference", 4], ["SectionHeader", 2], ["Table", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7131, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 19], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 830, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 60], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 27], ["Text", 4], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 47], ["Line", 19], ["Text", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1281, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["Line", 13], ["Text", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1218, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 40], ["ListItem", 15], ["Reference", 15], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 41], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 40], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 31], ["ListItem", 13], ["Reference", 13], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 42], ["TextInlineMath", 4], ["SectionHeader", 3], ["Text", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 336], ["Span", 130], ["Line", 38], ["Text", 2], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 6], ["SectionHeader", 1], ["Text", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 616, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 603, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Hierarchical Text-Conditional Image Generation with CLIP Latents"}