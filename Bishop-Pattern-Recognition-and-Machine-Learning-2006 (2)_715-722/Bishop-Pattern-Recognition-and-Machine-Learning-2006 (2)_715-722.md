# **Appendix C. Properties of Matrices**

In this appendix, we gather together some useful properties and identities involving matrices and determinants. This is not intended to be an introductory tutorial, and it is assumed that the reader is already familiar with basic linear algebra. For some results, we indicate how to prove them, whereas in more complex cases we leave the interested reader to refer to standard textbooks on the subject. In all cases, we assume that inverses exist and that matrix dimensions are such that the formulae are correctly defined. A comprehensive discussion of linear algebra can be found in <PERSON> and <PERSON> (1996), and an extensive collection of matrix properties is given by <PERSON><PERSON><PERSON><PERSON> (1996). Matrix derivatives are discussed in <PERSON> and <PERSON> (1999).

## **Basic Matrix Identities**

A matrix A has elements  $A_{ij}$  where i indexes the rows, and j indexes the columns. We use  $I_N$  to denote the  $N \times N$  identity matrix (also called the unit matrix), and where there is no ambiguity over dimensionality we simply use **I**. The transpose matrix  $\mathbf{A}^T$  has elements  $(\mathbf{A}^T)_{ij} = A_{ji}$ . From the definition of transpose, we have

$$
(\mathbf{A}\mathbf{B})^{\mathrm{T}} = \mathbf{B}^{\mathrm{T}}\mathbf{A}^{\mathrm{T}} \tag{C.1}
$$

which can be verified by writing out the indices. The inverse of **A**, denoted  $A^{-1}$ , satisfies

$$
AA^{-1} = A^{-1}A = I.
$$
 (C.2)

Because  $ABB^{-1}A^{-1} = I$ , we have

$$
(\mathbf{A}\mathbf{B})^{-1} = \mathbf{B}^{-1}\mathbf{A}^{-1}.
$$
 (C.3)

Also we have

$$
\left(\mathbf{A}^{\mathrm{T}}\right)^{-1} = \left(\mathbf{A}^{-1}\right)^{\mathrm{T}}
$$
 (C.4)

which is easily proven by taking the transpose of  $(C.2)$  and applying  $(C.1)$ .

A useful identity involving matrix inverses is the following

$$
(\mathbf{P}^{-1} + \mathbf{B}^{\mathrm{T}} \mathbf{R}^{-1} \mathbf{B})^{-1} \mathbf{B}^{\mathrm{T}} \mathbf{R}^{-1} = \mathbf{P} \mathbf{B}^{\mathrm{T}} (\mathbf{B} \mathbf{P} \mathbf{B}^{\mathrm{T}} + \mathbf{R})^{-1}.
$$
 (C.5)

which is easily verified by right multiplying both sides by  $(BPB<sup>T</sup> + R)$ . Suppose that **P** has dimensionality  $N \times N$  while **R** has dimensionality  $M \times M$ , so that **B** is  $M \times N$ . Then if  $M \ll N$ , it will be much cheaper to evaluate the right-hand side of (C.5) than the left-hand side. A special case that sometimes arises is

$$
(I + AB)^{-1}A = A(I + BA)^{-1}
$$
. (C.6)

Another useful identity involving inverses is the following:

$$
(\mathbf{A} + \mathbf{B} \mathbf{D}^{-1} \mathbf{C})^{-1} = \mathbf{A}^{-1} - \mathbf{A}^{-1} \mathbf{B} (\mathbf{D} + \mathbf{C} \mathbf{A}^{-1} \mathbf{B})^{-1} \mathbf{C} \mathbf{A}^{-1}
$$
 (C.7)

which is known as the *Woodbury identity* and which can be verified by multiplying both sides by  $(A + BD^{-1}C)$ . This is useful, for instance, when **A** is large and diagonal, and hence easy to invert, while **B** has many rows but few columns (and conversely for **C**) so that the right-hand side is much cheaper to evaluate than the left-hand side.

 $\sum_{n} \alpha_n \mathbf{a}_n = 0$  holds only if all  $\alpha_n = 0$ . This implies that none of the vectors can be expressed as a linear combination of the remainder. The rank of a matrix is A set of vectors  $\{a_1, \ldots, a_N\}$  is said to be *linearly independent* if the relation can be expressed as a linear combination of the remainder. The rank of a matrix is the maximum number of linearly independent rows (or equivalently the maximum number of linearly independent columns).

## **Traces and Determinants**

Trace and determinant apply to square matrices. The trace  $Tr(A)$  of a matrix  $A$ is defined as the sum of the elements on the leading diagonal. By writing out the indices, we see that

$$
Tr(AB) = Tr(BA).
$$
 (C.8)

By applying this formula multiple times to the product of three matrices, we see that

$$
Tr(ABC) = Tr(CAB) = Tr(BCA)
$$
 (C.9)

which is known as the *cyclic* property of the trace operator and which clearly extends to the product of any number of matrices. The determinant  $|\mathbf{A}|$  of an  $N \times N$  matrix **A** is defined by

$$
|\mathbf{A}| = \sum (\pm 1) A_{1i_1} A_{2i_2} \cdots A_{Ni_N}
$$
 (C.10)

in which the sum is taken over all products consisting of precisely one element from each row and one element from each column, with a coefficient  $+1$  or  $-1$  according

to whether the permutation  $i_1i_2 \ldots i_N$  is even or odd, respectively. Note that  $|\mathbf{I}| = 1$ . Thus, for a  $2 \times 2$  matrix, the determinant takes the form

$$
|\mathbf{A}| = \begin{vmatrix} a_{11} & a_{12} \\ a_{21} & a_{22} \end{vmatrix} = a_{11}a_{22} - a_{12}a_{21}.
$$
 (C.11)

The determinant of a product of two matrices is given by

$$
|\mathbf{AB}| = |\mathbf{A}||\mathbf{B}| \tag{C.12}
$$

as can be shown from (C.10). Also, the determinant of an inverse matrix is given by

$$
\left|\mathbf{A}^{-1}\right| = \frac{1}{|\mathbf{A}|}\tag{C.13}
$$

which can be shown by taking the determinant of  $(C.2)$  and applying  $(C.12)$ .

If **A** and **B** are matrices of size  $N \times M$ , then

$$
\left| \mathbf{I}_N + \mathbf{A} \mathbf{B}^{\mathrm{T}} \right| = \left| \mathbf{I}_M + \mathbf{A}^{\mathrm{T}} \mathbf{B} \right|.
$$
 (C.14)

A useful special case is

$$
\left| \mathbf{I}_N + \mathbf{a} \mathbf{b}^{\mathrm{T}} \right| = 1 + \mathbf{a}^{\mathrm{T}} \mathbf{b}
$$
 (C.15)

where **a** and **b** are <sup>N</sup>-dimensional column vectors.

## **Matrix Derivatives**

Sometimes we need to consider derivatives of vectors and matrices with respect to scalars. The derivative of a vector  $\bf{a}$  with respect to a scalar x is itself a vector whose components are given by

$$
\left(\frac{\partial \mathbf{a}}{\partial x}\right)_i = \frac{\partial a_i}{\partial x} \tag{C.16}
$$

with an analogous definition for the derivative of a matrix. Derivatives with respect to vectors and matrices can also be defined, for instance

$$
\left(\frac{\partial x}{\partial \mathbf{a}}\right)_i = \frac{\partial x}{\partial a_i} \tag{C.17}
$$

and similarly

$$
\left(\frac{\partial \mathbf{a}}{\partial \mathbf{b}}\right)_{ij} = \frac{\partial a_i}{\partial b_j}.
$$
\n(C.18)

The following is easily proven by writing out the components

$$
\frac{\partial}{\partial \mathbf{x}} (\mathbf{x}^{\mathrm{T}} \mathbf{a}) = \frac{\partial}{\partial \mathbf{x}} (\mathbf{a}^{\mathrm{T}} \mathbf{x}) = \mathbf{a}.
$$
 (C.19)

Similarly

$$
\frac{\partial}{\partial x} (AB) = \frac{\partial A}{\partial x} B + A \frac{\partial B}{\partial x}.
$$
\nThe derivative of the inverse of a matrix can be expressed as\n
$$
\frac{\partial}{\partial x} (AB) = \frac{\partial A}{\partial x} (AB) + B \frac{\partial B}{\partial x}.
$$
\n(6.20)

The derivative of the inverse of a matrix can be expressed as

$$
\frac{\partial}{\partial x} \left( \mathbf{A}^{-1} \right) = -\mathbf{A}^{-1} \frac{\partial \mathbf{A}}{\partial x} \mathbf{A}^{-1}
$$
 (C.21)

as can be shown by differentiating the equation  $A^{-1}A = I$  using (C.20) and then right multiplying by **<sup>A</sup>**<sup>−</sup><sup>1</sup>. Also

$$
\frac{\partial}{\partial x} \ln |\mathbf{A}| = \text{Tr} \left( \mathbf{A}^{-1} \frac{\partial \mathbf{A}}{\partial x} \right)
$$
 (C.22)

which we shall prove later. If we choose  $x$  to be one of the elements of  $A$ , we have

$$
\frac{\partial}{\partial A_{ij}} \text{Tr} (\mathbf{A} \mathbf{B}) = B_{ji}
$$
 (C.23)

as can be seen by writing out the matrices using index notation. We can write this result more compactly in the form

$$
\frac{\partial}{\partial \mathbf{A}} \text{Tr}(\mathbf{A}\mathbf{B}) = \mathbf{B}^{\text{T}}.
$$
 (C.24)

With this notation, we have the following properties

$$
\frac{\partial}{\partial \mathbf{A}} \text{Tr} \left( \mathbf{A}^{\text{T}} \mathbf{B} \right) = \mathbf{B} \tag{C.25}
$$

$$
\frac{\partial}{\partial \mathbf{A}} \text{Tr}(\mathbf{A}) = \mathbf{I} \tag{C.26}
$$

$$
\frac{\partial}{\partial \mathbf{A}} \text{Tr}(\mathbf{A} \mathbf{B} \mathbf{A}^{\text{T}}) = \mathbf{A}(\mathbf{B} + \mathbf{B}^{\text{T}})
$$
 (C.27)

which can again be proven by writing out the matrix indices. We also have

$$
\frac{\partial}{\partial \mathbf{A}} \ln |\mathbf{A}| = (\mathbf{A}^{-1})^{\mathrm{T}}
$$
 (C.28)

which follows from (C.22) and (C.26).

## **Eigenvector Equation**

For a square matrix **A** of size  $M \times M$ , the eigenvector equation is defined by

$$
\mathbf{A}\mathbf{u}_i = \lambda_i \mathbf{u}_i \tag{C.29}
$$

for  $i = 1, \ldots, M$ , where  $\mathbf{u}_i$  is an *eigenvector* and  $\lambda_i$  is the corresponding *eigenvalue*. This can be viewed as a set of M simultaneous homogeneous linear equations, and the condition for a solution is that

$$
|\mathbf{A} - \lambda_i \mathbf{I}| = 0 \tag{C.30}
$$

which is known as the *characteristic equation*. Because this is a polynomial of order M in  $\lambda_i$ , it must have M solutions (though these need not all be distinct). The rank of **A** is equal to the number of nonzero eigenvalues.

Of particular interest are symmetric matrices, which arise as covariance matrices, kernel matrices, and Hessians. Symmetric matrices have the property that  $A_{ij} = A_{ji}$ , or equivalently  $A^T = A$ . The inverse of a symmetric matrix is also symmetric, as can be seen by taking the transpose of  $A^{-1}A = I$  and using  $AA^{-1} = I$ together with the symmetry of **I**.

In general, the eigenvalues of a matrix are complex numbers, but for symmetric matrices the eigenvalues  $\lambda_i$  are real. This can be seen by first left multiplying (C.29) by  $(\mathbf{u}_i^{\star})^{\mathrm{T}}$ , where  $\star$  denotes the complex conjugate, to give

$$
\left(\mathbf{u}_{i}^{\star}\right)^{\mathrm{T}}\mathbf{A}\mathbf{u}_{i}=\lambda_{i}\left(\mathbf{u}_{i}^{\star}\right)^{\mathrm{T}}\mathbf{u}_{i}.
$$
 (C.31)

Next we take the complex conjugate of  $(C.29)$  and left multiply by  $\mathbf{u}_i^{\mathrm{T}}$  to give

$$
\mathbf{u}_i^{\mathrm{T}} \mathbf{A} \mathbf{u}_i^{\star} = \lambda_i^{\star} \mathbf{u}_i^{\mathrm{T}} \mathbf{u}_i^{\star}.
$$
 (C.32)

where we have used  $A^* = A$  because we consider only real matrices A. Taking the transpose of the second of these equations, and using  $A<sup>T</sup> = A$ , we see that the left-hand sides of the two equations are equal, and hence that  $\lambda_i^* = \lambda_i$  and so  $\lambda_i$ must be real.

The eigenvectors  $\mathbf{u}_i$  of a real symmetric matrix can be chosen to be orthonormal (i.e., orthogonal and of unit length) so that

$$
\mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j = I_{ij} \tag{C.33}
$$

where  $I_{ij}$  are the elements of the identity matrix **I**. To show this, we first left multiply (C.29) by  $\mathbf{u}_j^{\mathrm{T}}$  to give

$$
\mathbf{u}_j^{\mathrm{T}} \mathbf{A} \mathbf{u}_i = \lambda_i \mathbf{u}_j^{\mathrm{T}} \mathbf{u}_i
$$
 (C.34)

and hence, by exchange of indices, we have

$$
\mathbf{u}_i^{\mathrm{T}} \mathbf{A} \mathbf{u}_j = \lambda_j \mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j. \tag{C.35}
$$

We now take the transpose of the second equation and make use of the symmetry property  $A^T = A$ , and then subtract the two equations to give

$$
(\lambda_i - \lambda_j) \mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j = 0.
$$
 (C.36)

Hence, for  $\lambda_i \neq \lambda_j$ , we have  $\mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j = 0$ , and hence  $\mathbf{u}_i$  and  $\mathbf{u}_j$  are orthogonal. If the two eigenvalues are equal, then any linear combination  $\alpha \mathbf{u} \cdot + \beta \mathbf{u} \cdot$  is also an eigentwo eigenvalues are equal, then any linear combination  $\alpha \mathbf{u}_i + \beta \mathbf{u}_j$  is also an eigenvector with the same eigenvalue, so we can select one linear combination arbitrarily,

and then choose the second to be orthogonal to the first (it can be shown that the degenerate eigenvectors are never linearly dependent). Hence the eigenvectors can be chosen to be orthogonal, and by normalizing can be set to unit length. Because there are  $M$  eigenvalues, the corresponding  $M$  orthogonal eigenvectors form a complete set and so any M-dimensional vector can be expressed as a linear combination of the eigenvectors.

We can take the eigenvectors  $\mathbf{u}_i$  to be the columns of an  $M \times M$  matrix **U**, which from orthonormality satisfies

$$
\mathbf{U}^{\mathrm{T}}\mathbf{U} = \mathbf{I}.\tag{C.37}
$$

Such a matrix is said to be *orthogonal*. Interestingly, the rows of this matrix are also orthogonal, so that  $UU^T = I$ . To show this, note that (C.37) implies  $U^T U U^{-1} =$ <br> $U^{-1} = U^T$  and so  $UU^{-1} = UU^T = I$  Using (C.12) it also follows that  $|U| = 1$  $U^{-1} = U^{T}$  and so  $UU^{-1} = UU^{T} = I$ . Using (C.12), it also follows that  $|U| = 1$ .<br>The eigenvector equation (C 29) can be expressed in terms of **U** in the form

The eigenvector equation (C.29) can be expressed in terms of **U** in the form

$$
AU = U\Lambda \tag{C.38}
$$

where  $\Lambda$  is an  $M \times M$  diagonal matrix whose diagonal elements are given by the eigenvalues  $\lambda_i$ .

If we consider a column vector **x** that is transformed by an orthogonal matrix **U** to give a new vector

$$
\widetilde{\mathbf{x}} = \mathbf{U}\mathbf{x} \tag{C.39}
$$

then the length of the vector is preserved because

$$
\widetilde{\mathbf{x}}^{\mathrm{T}}\widetilde{\mathbf{x}} = \mathbf{x}^{\mathrm{T}}\mathbf{U}^{\mathrm{T}}\mathbf{U}\mathbf{x} = \mathbf{x}^{\mathrm{T}}\mathbf{x}
$$
 (C.40)

and similarly the angle between any two such vectors is preserved because

$$
\widetilde{\mathbf{x}}^{\mathrm{T}}\widetilde{\mathbf{y}} = \mathbf{x}^{\mathrm{T}}\mathbf{U}^{\mathrm{T}}\mathbf{U}\mathbf{y} = \mathbf{x}^{\mathrm{T}}\mathbf{y}.\tag{C.41}
$$

Thus, multiplication by **U** can be interpreted as a rigid rotation of the coordinate system.

From (C.38), it follows that

$$
\mathbf{U}^{\mathrm{T}}\mathbf{A}\mathbf{U} = \mathbf{\Lambda} \tag{C.42}
$$

and because **Λ** is a diagonal matrix, we say that the matrix **A** is *diagonalized* by the matrix **U**. If we left multiply by **U** and right multiply by  $U^T$ , we obtain

$$
\mathbf{A} = \mathbf{U}\mathbf{\Lambda}\mathbf{U}^{\mathrm{T}} \tag{C.43}
$$

Taking the inverse of this equation, and using (C.3) together with  $U^{-1} = U^{T}$ , we have

$$
\mathbf{A}^{-1} = \mathbf{U}\mathbf{\Lambda}^{-1}\mathbf{U}^{\mathrm{T}}.\tag{C.44}
$$

These last two equations can also be written in the form

$$
\mathbf{A} = \sum_{i=1}^{M} \lambda_i \mathbf{u}_i \mathbf{u}_i^{\mathrm{T}}
$$
 (C.45)

$$
\mathbf{A}^{-1} = \sum_{i=1}^{M} \frac{1}{\lambda_i} \mathbf{u}_i \mathbf{u}_i^{\mathrm{T}}.
$$
 (C.46)

If we take the determinant of  $(C.43)$ , and use  $(C.12)$ , we obtain

$$
|\mathbf{A}| = \prod_{i=1}^{M} \lambda_i.
$$
 (C.47)

Similarly, taking the trace of (C.43), and using the cyclic property (C.8) of the trace operator together with  $U<sup>T</sup>U = I$ , we have

$$
Tr(\mathbf{A}) = \sum_{i=1}^{M} \lambda_i.
$$
 (C.48)

We leave it as an exercise for the reader to verify (C.22) by making use of the results (C.33), (C.45), (C.46), and (C.47).

A matrix **A** is said to be *positive definite*, denoted by  $A \succ 0$ , if  $w^T A w > 0$  for all values of the vector **w**. Equivalently, a positive definite matrix has  $\lambda_i > 0$  for all of its eigenvalues (as can be seen by setting **w** to each of the eigenvectors in turn, and by noting that an arbitrary vector can be expanded as a linear combination of the eigenvectors). Note that positive definite is not the same as all the elements being positive. For example, the matrix

$$
\left(\begin{array}{cc} 1 & 2 \\ 3 & 4 \end{array}\right) \tag{C.49}
$$

has eigenvalues  $\lambda_1 \simeq 5.37$  and  $\lambda_2 \simeq -0.37$ . A matrix is said to be *positive semidefinite* if  $\mathbf{w}^T \mathbf{A} \mathbf{w} \ge 0$  holds for all values of **w**, which is denoted  $\mathbf{A} \succeq 0$ , and is equivalent to  $\lambda_i \ge 0$ equivalent to  $\lambda_i \geqslant 0$ .

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.