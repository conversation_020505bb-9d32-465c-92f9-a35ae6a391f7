{"table_of_contents": [{"title": "14.3.3 Object Dissimilarity", "heading_level": null, "page_id": 0, "polygon": [[133.5, 111.75], [279.75, 111.75], [279.75, 123.0732421875], [133.5, 123.0732421875]]}, {"title": "14.3.4 Clustering Algorithms", "heading_level": null, "page_id": 2, "polygon": [[133.5, 273.75], [289.5, 273.75], [289.5, 285.78515625], [133.5, 285.78515625]]}, {"title": "14.3.5 Combinatorial Algorithms", "heading_level": null, "page_id": 2, "polygon": [[133.5, 537.0], [310.5, 537.0], [310.5, 547.98046875], [133.5, 547.98046875]]}, {"title": "508 14. Unsupervised Learning", "heading_level": null, "page_id": 3, "polygon": [[132.0, 88.5], [279.75, 88.5], [279.75, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "14.3.6 K-means", "heading_level": null, "page_id": 4, "polygon": [[133.5, 333.0], [224.5693359375, 333.0], [224.5693359375, 344.953125], [133.5, 344.953125]]}, {"title": "510 14. Unsupervised Learning", "heading_level": null, "page_id": 5, "polygon": [[132.0, 88.5], [280.4501953125, 88.5], [280.4501953125, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "14.3.7 Gaussian Mixtures as Soft K-means Clustering", "heading_level": null, "page_id": 5, "polygon": [[133.5, 608.25], [419.255859375, 608.25], [419.255859375, 620.296875], [133.5, 620.296875]]}, {"title": "512 14. Unsupervised Learning", "heading_level": null, "page_id": 7, "polygon": [[132.0, 88.5], [280.5, 88.5], [280.5, 98.419921875], [132.0, 98.419921875]]}, {"title": "14.3.8 Example: Human Tumor Microarray Data", "heading_level": null, "page_id": 7, "polygon": [[133.5, 609.0], [393.0, 609.0], [393.0, 620.68359375], [133.5, 620.68359375]]}, {"title": "14.3.9 Vector Quantization", "heading_level": null, "page_id": 9, "polygon": [[133.5, 461.25], [280.5, 461.25], [280.5, 473.34375], [133.5, 473.34375]]}, {"title": "14.3.10 K-medoids", "heading_level": null, "page_id": 10, "polygon": [[133.5, 608.25], [240.1083984375, 608.25], [240.1083984375, 619.5234375], [133.5, 619.5234375]]}, {"title": "516 14. Unsupervised Learning", "heading_level": null, "page_id": 11, "polygon": [[132.0, 88.5], [280.1513671875, 88.5], [280.1513671875, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "Example: Country Dissimilarities", "heading_level": null, "page_id": 12, "polygon": [[133.5, 447.0], [280.5, 447.0], [280.5, 457.1015625], [133.5, 457.1015625]]}, {"title": "14.3.11 Practical Issues", "heading_level": null, "page_id": 13, "polygon": [[133.5, 369.0], [262.5, 369.0], [262.5, 380.91796875], [133.5, 380.91796875]]}, {"title": "14.3.12 Hierarchical Clustering", "heading_level": null, "page_id": 15, "polygon": [[133.5, 417.0], [300.75, 417.0], [300.75, 429.2578125], [133.5, 429.2578125]]}, {"title": "Agglomerative Clustering", "heading_level": null, "page_id": 18, "polygon": [[133.5, 256.5], [244.740234375, 256.5], [244.740234375, 266.8359375], [133.5, 266.8359375]]}, {"title": "Example: Human Cancer Microarray Data (Continued)", "heading_level": null, "page_id": 20, "polygon": [[133.5, 478.5], [375.75, 478.5], [375.75, 488.42578125], [133.5, 488.42578125]]}, {"title": "526 14. Unsupervised Learning", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [279.75, 88.5], [279.75, 98.806640625], [132.0, 98.806640625]]}, {"title": "Divisive Clustering", "heading_level": null, "page_id": 21, "polygon": [[133.4267578125, 323.25], [216.7998046875, 323.25], [216.7998046875, 332.96484375], [133.4267578125, 332.96484375]]}, {"title": "528 14. Unsupervised Learning", "heading_level": null, "page_id": 23, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 98.806640625], [132.0, 98.806640625]]}, {"title": "14.4 Self-Organizing Maps", "heading_level": null, "page_id": 23, "polygon": [[132.75, 281.25], [304.5, 281.25], [304.5, 295.06640625], [132.75, 295.06640625]]}, {"title": "Example: Document Organization and Retrieval", "heading_level": null, "page_id": 27, "polygon": [[132.75, 430.5], [343.5, 430.5], [343.5, 440.0859375], [132.75, 440.0859375]]}, {"title": "14.5 Principal Components, Curves and Surfaces", "heading_level": null, "page_id": 29, "polygon": [[132.75, 450.0], [442.86328125, 450.0], [442.86328125, 463.67578125], [132.75, 463.67578125]]}, {"title": "14.5.1 Principal Components", "heading_level": null, "page_id": 29, "polygon": [[133.5, 584.25], [291.0, 584.25], [291.0, 596.3203125], [133.5, 596.3203125]]}, {"title": "Example: Handwritten Digits", "heading_level": null, "page_id": 31, "polygon": [[133.4267578125, 501.75], [261.0, 501.75], [261.0, 512.40234375], [133.4267578125, 512.40234375]]}, {"title": "Example: Procrustes Transformations and S<PERSON>pe Averaging", "heading_level": null, "page_id": 34, "polygon": [[133.4267578125, 169.5], [393.0, 169.5], [393.0, 180.984375], [133.4267578125, 180.984375]]}, {"title": "540 14. Unsupervised Learning", "heading_level": null, "page_id": 35, "polygon": [[132.0, 88.5], [279.75, 88.5], [279.75, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "14.5.2 Principal Curves and Surfaces", "heading_level": null, "page_id": 36, "polygon": [[133.5, 410.25], [333.0, 410.25], [333.0, 421.91015625], [133.5, 421.91015625]]}, {"title": "544 14. Unsupervised Learning", "heading_level": null, "page_id": 39, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "14.5.3 Spectral Clustering", "heading_level": null, "page_id": 39, "polygon": [[133.5, 285.75], [273.75, 285.75], [273.75, 297.7734375], [133.5, 297.7734375]]}, {"title": "14.5.4 Kernel Principal Components", "heading_level": null, "page_id": 42, "polygon": [[133.5, 393.0], [328.5, 393.0], [328.5, 404.89453125], [133.5, 404.89453125]]}, {"title": "548 14. Unsupervised Learning", "heading_level": null, "page_id": 43, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "550 14. Unsupervised Learning", "heading_level": null, "page_id": 45, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 99.0966796875], [132.0, 99.0966796875]]}, {"title": "14.5.5 Sparse Principal Components", "heading_level": null, "page_id": 45, "polygon": [[133.5, 203.25], [327.75, 203.25], [327.75, 215.40234375], [133.5, 215.40234375]]}, {"title": "14.6 Non-negative Matrix Factorization", "heading_level": null, "page_id": 48, "polygon": [[132.0, 111.0], [385.5, 111.0], [385.5, 123.75], [132.0, 123.75]]}, {"title": "554 14. Unsupervised Learning", "heading_level": null, "page_id": 49, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "14.6.1 Archetypal Analysis", "heading_level": null, "page_id": 49, "polygon": [[133.5, 290.25], [277.5, 290.25], [277.5, 301.25390625], [133.5, 301.25390625]]}, {"title": "14.7 Independent Component Analysis and\nExploratory Projection Pursuit", "heading_level": null, "page_id": 52, "polygon": [[132.0, 447.75], [409.095703125, 447.75], [409.095703125, 478.37109375], [132.0, 478.37109375]]}, {"title": "14.7.1 Latent Variables and Factor Analysis", "heading_level": null, "page_id": 53, "polygon": [[133.5, 522.75], [368.15625, 525.0], [368.15625, 536.37890625], [133.5, 536.37890625]]}, {"title": "560 14. Unsupervised Learning", "heading_level": null, "page_id": 55, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "14.7.2 Independent Component Analysis", "heading_level": null, "page_id": 55, "polygon": [[133.5, 321.0], [347.537109375, 321.0], [347.537109375, 333.3515625], [133.5, 333.3515625]]}, {"title": "562 14. Unsupervised Learning", "heading_level": null, "page_id": 57, "polygon": [[132.0, 88.5], [280.5, 88.5], [280.5, 98.5166015625], [132.0, 98.5166015625]]}, {"title": "Example: Handwritten Digits", "heading_level": null, "page_id": 58, "polygon": [[133.4267578125, 585.75], [261.0, 585.75], [261.0, 596.3203125], [133.4267578125, 596.3203125]]}, {"title": "Example: EEG Time Courses", "heading_level": null, "page_id": 59, "polygon": [[133.5, 373.5], [264.0, 373.5], [264.0, 383.431640625], [133.5, 383.431640625]]}, {"title": "14.7.3 Exploratory Projection Pursuit", "heading_level": null, "page_id": 60, "polygon": [[133.5, 286.5], [335.25, 286.5], [335.25, 298.93359375], [133.5, 298.93359375]]}, {"title": "14.7.4 A Direct Approach to ICA", "heading_level": null, "page_id": 60, "polygon": [[132.75439453125, 549.75], [312.0, 549.75], [312.0, 562.2890625], [132.75439453125, 562.2890625]]}, {"title": "568 14. Unsupervised Learning", "heading_level": null, "page_id": 63, "polygon": [[132.0, 88.5], [279.75, 88.5], [279.75, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "Example: Simulations", "heading_level": null, "page_id": 64, "polygon": [[133.5, 297.75], [228.75, 297.75], [228.75, 308.408203125], [133.5, 308.408203125]]}, {"title": "570 14. Unsupervised Learning", "heading_level": null, "page_id": 65, "polygon": [[132.0, 88.5], [279.75, 88.5], [279.75, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "14.8 Multidimensional Scaling", "heading_level": null, "page_id": 65, "polygon": [[133.5, 319.5], [329.607421875, 319.5], [329.607421875, 332.771484375], [133.5, 332.771484375]]}, {"title": "14.9 Nonlinear Dimension Reduction and Local\nMultidimensional Scaling", "heading_level": null, "page_id": 67, "polygon": [[132.5302734375, 477.0], [435.09375, 477.0], [435.09375, 507.76171875], [132.5302734375, 507.76171875]]}, {"title": "574 14. Unsupervised Learning", "heading_level": null, "page_id": 69, "polygon": [[132.0, 89.25], [280.30078125, 89.25], [280.30078125, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "14.10 The Google PageRank Algorithm", "heading_level": null, "page_id": 71, "polygon": [[132.60498046875, 420.75], [385.787109375, 420.75], [385.787109375, 433.8984375], [132.60498046875, 433.8984375]]}, {"title": "578 14. Unsupervised Learning", "heading_level": null, "page_id": 73, "polygon": [[132.0, 88.5], [280.5, 88.5], [280.5, 98.419921875], [132.0, 98.419921875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 73, "polygon": [[132.0, 482.23828125], [255.0, 482.23828125], [255.0, 495.38671875], [132.0, 495.38671875]]}, {"title": "Exercises", "heading_level": null, "page_id": 74, "polygon": [[132.0, 353.25], [190.65234375, 353.25], [190.65234375, 366.22265625], [132.0, 366.22265625]]}, {"title": "580 14. Unsupervised Learning", "heading_level": null, "page_id": 75, "polygon": [[132.0, 88.5], [280.001953125, 88.5], [280.001953125, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "582 14. Unsupervised Learning", "heading_level": null, "page_id": 77, "polygon": [[132.0, 89.25], [279.75, 89.25], [279.75, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "Ex. 14.22", "heading_level": null, "page_id": 78, "polygon": [[132.6796875, 517.5], [174.75, 517.5], [174.75, 528.64453125], [132.6796875, 528.64453125]]}, {"title": "584 14. Unsupervised Learning", "heading_level": null, "page_id": 79, "polygon": [[131.9326171875, 88.5], [280.5, 88.5], [280.5, 99.0], [131.9326171875, 99.0]]}, {"title": "15\nRandom Forests", "heading_level": null, "page_id": 82, "polygon": [[132.0, 109.5], [269.25, 109.5], [269.25, 162.03515625], [132.0, 162.03515625]]}, {"title": "15.1 Introduction", "heading_level": null, "page_id": 82, "polygon": [[132.75, 354.0], [250.119140625, 354.0], [250.119140625, 367.576171875], [132.75, 367.576171875]]}, {"title": "15.2 Definition of Random Forests", "heading_level": null, "page_id": 82, "polygon": [[132.0, 588.75], [354.41015625, 588.75], [354.41015625, 603.28125], [132.0, 603.28125]]}, {"title": "590 15. Random Forests", "heading_level": null, "page_id": 85, "polygon": [[132.0, 88.5], [252.75, 88.5], [252.75, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "15.3 Details of Random Forests", "heading_level": null, "page_id": 87, "polygon": [[132.0, 382.5], [337.376953125, 382.5], [337.376953125, 396.7734375], [132.0, 396.7734375]]}, {"title": "15.3.1 Out of Bag Samples", "heading_level": null, "page_id": 87, "polygon": [[133.5, 608.25], [279.75, 608.25], [279.75, 620.296875], [133.5, 620.296875]]}, {"title": "15.3.2 Variable Importance", "heading_level": null, "page_id": 88, "polygon": [[133.5, 289.5], [280.5, 289.5], [280.5, 300.673828125], [133.5, 300.673828125]]}, {"title": "15.3.3 Proximity Plots", "heading_level": null, "page_id": 90, "polygon": [[133.5, 381.0], [258.0380859375, 381.0], [258.0380859375, 393.099609375], [133.5, 393.099609375]]}, {"title": "15.3.4 Random Forests and Overfitting", "heading_level": null, "page_id": 91, "polygon": [[133.27734375, 177.75], [340.5, 177.75], [340.5, 189.5888671875], [133.27734375, 189.5888671875]]}, {"title": "15.4 Analysis of Random Forests", "heading_level": null, "page_id": 92, "polygon": [[132.90380859375, 440.25], [345.146484375, 440.25], [345.146484375, 453.62109375], [132.90380859375, 453.62109375]]}, {"title": "15.4.1 V<PERSON><PERSON> and the De-Correlation Effect", "heading_level": null, "page_id": 92, "polygon": [[133.5, 567.75], [378.9140625, 567.75], [378.9140625, 579.69140625], [133.5, 579.69140625]]}, {"title": "600 15. Random Forests", "heading_level": null, "page_id": 95, "polygon": [[132.0, 88.5], [252.75, 88.5], [252.75, 98.46826171875], [132.0, 98.46826171875]]}, {"title": "15.4.2 Bias", "heading_level": null, "page_id": 95, "polygon": [[133.5, 608.25], [200.25, 608.25], [200.25, 619.91015625], [133.5, 619.91015625]]}, {"title": "15.4.3 Adaptive Nearest Neighbors", "heading_level": null, "page_id": 96, "polygon": [[133.5, 451.5], [317.25, 451.5], [317.25, 463.2890625], [133.5, 463.2890625]]}, {"title": "602 15. Random Forests", "heading_level": null, "page_id": 97, "polygon": [[132.0, 88.5], [255.0498046875, 88.5], [255.0498046875, 98.17822265625], [132.0, 98.17822265625]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 97, "polygon": [[132.0, 338.25], [255.0498046875, 338.25], [255.0498046875, 351.52734375], [132.0, 351.52734375]]}, {"title": "Exercises", "heading_level": null, "page_id": 98, "polygon": [[132.0, 192.0], [190.951171875, 192.0], [190.951171875, 204.380859375], [132.0, 204.380859375]]}, {"title": "604 15. Random Forests", "heading_level": null, "page_id": 99, "polygon": [[132.0, 87.75], [252.75, 87.75], [252.75, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "16\nEnsemble Learning", "heading_level": null, "page_id": 100, "polygon": [[132.0, 108.75], [292.5, 108.75], [292.5, 162.1318359375], [132.0, 162.1318359375]]}, {"title": "16.1 Introduction", "heading_level": null, "page_id": 100, "polygon": [[132.75, 354.75], [250.41796875, 354.75], [250.41796875, 367.576171875], [132.75, 367.576171875]]}, {"title": "606 16. Ensemble Learning", "heading_level": null, "page_id": 101, "polygon": [[132.0, 88.5], [264.0, 88.5], [264.0, 98.806640625], [132.0, 98.806640625]]}, {"title": "16.2 Boosting and Regularization Paths", "heading_level": null, "page_id": 102, "polygon": [[132.0, 110.98828125], [387.75, 110.98828125], [387.75, 123.943359375], [132.0, 123.943359375]]}, {"title": "16.2.1 Penalized Regression", "heading_level": null, "page_id": 102, "polygon": [[133.5, 254.25], [283.5, 254.25], [283.5, 266.0625], [133.5, 266.0625]]}, {"title": "610 16. Ensemble Learning", "heading_level": null, "page_id": 105, "polygon": [[132.0, 88.5], [264.0, 88.5], [264.0, 99.0], [132.0, 99.0]]}, {"title": "16.2.2 The \"Bet on Sparsity\" Principle", "heading_level": null, "page_id": 105, "polygon": [[133.5, 488.25], [343.353515625, 488.25], [343.353515625, 500.80078125], [133.5, 500.80078125]]}, {"title": "16.2.3 Regularization Paths, Over-fitting and Margins", "heading_level": null, "page_id": 108, "polygon": [[133.5, 426.75], [418.95703125, 426.75], [418.95703125, 440.47265625], [133.5, 440.47265625]]}, {"title": "16.3 Learning Ensembles", "heading_level": null, "page_id": 111, "polygon": [[132.75, 601.5], [295.5, 601.5], [295.5, 614.49609375], [132.75, 614.49609375]]}, {"title": "16.3.1 Learning a Good Ensemble", "heading_level": null, "page_id": 112, "polygon": [[133.5, 609.0], [314.068359375, 609.0], [314.068359375, 619.91015625], [133.5, 619.91015625]]}, {"title": "620 16. Ensemble Learning", "heading_level": null, "page_id": 115, "polygon": [[132.0, 88.5], [264.75, 88.5], [264.75, 98.806640625], [132.0, 98.806640625]]}, {"title": "622 16. Ensemble Learning", "heading_level": null, "page_id": 117, "polygon": [[131.9326171875, 87.75], [264.0, 87.75], [264.0, 99.2900390625], [131.9326171875, 99.2900390625]]}, {"title": "16.3.2 Rule Ensembles", "heading_level": null, "page_id": 117, "polygon": [[133.1279296875, 111.75], [258.0, 111.75], [258.0, 122.8798828125], [133.1279296875, 122.8798828125]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 118, "polygon": [[132.0, 538.5], [255.0, 538.5], [255.0, 551.84765625], [132.0, 551.84765625]]}, {"title": "624 16. Ensemble Learning", "heading_level": null, "page_id": 119, "polygon": [[132.0, 88.5], [264.75, 88.5], [264.75, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "Exercises", "heading_level": null, "page_id": 119, "polygon": [[132.0, 306.75], [190.65234375, 306.75], [190.65234375, 319.623046875], [132.0, 319.623046875]]}, {"title": "17\nUndirected Graphical Models", "heading_level": null, "page_id": 120, "polygon": [[132.0, 108.75], [378.0, 108.75], [378.0, 163.58203125], [132.0, 163.58203125]]}, {"title": "17.1 Introduction", "heading_level": null, "page_id": 120, "polygon": [[133.5, 354.75], [250.2685546875, 354.75], [250.2685546875, 367.576171875], [133.5, 367.576171875]]}, {"title": "626 17. Undirected Graphical Models", "heading_level": null, "page_id": 121, "polygon": [[131.3349609375, 88.5], [306.0, 88.5], [306.0, 98.85498046875], [131.3349609375, 98.85498046875]]}, {"title": "17.2 Markov Graphs and Their Properties", "heading_level": null, "page_id": 122, "polygon": [[132.0, 421.91015625], [402.22265625, 421.91015625], [402.22265625, 435.83203125], [132.0, 435.83203125]]}, {"title": "630 17. Undirected Graphical Models", "heading_level": null, "page_id": 125, "polygon": [[132.0, 89.25], [306.0, 89.25], [306.0, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "17.3 Undirected Graphical Models for Continuous\nVariables", "heading_level": null, "page_id": 125, "polygon": [[132.75, 269.25], [449.4375, 269.25], [449.4375, 297.580078125], [132.75, 297.580078125]]}, {"title": "17.3.1 Estimation of the Parameters when the Graph\nStructure is Known", "heading_level": null, "page_id": 126, "polygon": [[133.5, 429.75], [414.474609375, 429.75], [414.474609375, 455.94140625], [133.5, 455.94140625]]}, {"title": "17.3.2 Estimation of the Graph Structure", "heading_level": null, "page_id": 130, "polygon": [[133.5, 112.5], [354.41015625, 112.5], [354.41015625, 123.2666015625], [133.5, 123.2666015625]]}, {"title": "Algorithm 17.2 Graphical Lasso.", "heading_level": null, "page_id": 131, "polygon": [[132.75, 113.25], [287.173828125, 113.25], [287.173828125, 123.556640625], [132.75, 123.556640625]]}, {"title": "638 17. Undirected Graphical Models", "heading_level": null, "page_id": 133, "polygon": [[132.0, 89.25], [306.0, 89.25], [306.0, 98.46826171875], [132.0, 98.46826171875]]}, {"title": "17.4 Undirected Graphical Models for Discrete\nVariables", "heading_level": null, "page_id": 133, "polygon": [[132.75, 233.96484375], [429.75, 233.96484375], [429.75, 263.7421875], [132.75, 263.7421875]]}, {"title": "17.4.1 Estimation of the Parameters when the Graph\nStructure is Known", "heading_level": null, "page_id": 134, "polygon": [[133.1279296875, 290.25], [414.0, 290.25], [414.0, 316.529296875], [133.1279296875, 316.529296875]]}, {"title": "17.4.2 <PERSON> Nodes", "heading_level": null, "page_id": 136, "polygon": [[133.5, 346.5], [248.25, 346.5], [248.25, 358.681640625], [133.5, 358.681640625]]}, {"title": "17.4.3 Estimation of the Graph Structure", "heading_level": null, "page_id": 137, "polygon": [[133.5, 273.75], [353.513671875, 273.75], [353.513671875, 285.3984375], [133.5, 285.3984375]]}, {"title": "17.4.4 Restricted Boltzmann Machines", "heading_level": null, "page_id": 138, "polygon": [[132.978515625, 357.75], [339.0, 357.75], [339.0, 370.08984375], [132.978515625, 370.08984375]]}, {"title": "644 17. Undirected Graphical Models", "heading_level": null, "page_id": 139, "polygon": [[132.0, 89.25], [306.0, 89.25], [306.0, 98.46826171875], [132.0, 98.46826171875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 140, "polygon": [[132.0, 423.75], [255.0, 423.75], [255.0, 436.9921875], [132.0, 436.9921875]]}, {"title": "Exercises", "heading_level": null, "page_id": 140, "polygon": [[132.0, 584.25], [189.75, 584.25], [189.75, 596.70703125], [132.0, 596.70703125]]}, {"title": "Ex. 17.8", "heading_level": null, "page_id": 142, "polygon": [[132.0, 341.25], [171.52734375, 341.25], [171.52734375, 352.30078125], [132.0, 352.30078125]]}, {"title": "18\nHigh-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 144, "polygon": [[132.0, 108.75], [436.5, 108.75], [436.5, 163.58203125], [132.0, 163.58203125]]}, {"title": "18.1 When p is Much Bigger than N", "heading_level": null, "page_id": 144, "polygon": [[132.75, 354.75], [369.3515625, 354.75], [369.3515625, 367.962890625], [132.75, 367.962890625]]}, {"title": "18.2 Diagonal Linear Discriminant Analysis and\nNearest Shrunken Centroids", "heading_level": null, "page_id": 146, "polygon": [[133.5, 383.23828125], [439.875, 383.23828125], [439.875, 411.85546875], [133.5, 411.85546875]]}, {"title": "652 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 147, "polygon": [[132.0, 89.25], [332.296875, 89.25], [332.296875, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "654 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 149, "polygon": [[132.0, 88.5], [332.25, 88.5], [332.25, 98.56494140625], [132.0, 98.56494140625]]}, {"title": "18.3 Linear Classifiers with Quadratic\nRegularization", "heading_level": null, "page_id": 149, "polygon": [[132.75, 330.75], [378.9140625, 330.75], [378.9140625, 361.1953125], [132.75, 361.1953125]]}, {"title": "Number of Genes", "heading_level": null, "page_id": 150, "polygon": [[282.392578125, 110.25], [334.5, 110.25], [334.5, 119.25], [282.392578125, 119.25]]}, {"title": "18.3.1 Regularized Discriminant Analysis", "heading_level": null, "page_id": 151, "polygon": [[133.5, 504.75], [353.25, 504.75], [353.25, 516.26953125], [133.5, 516.26953125]]}, {"title": "18.3.2 Logistic Regression with Quadratic Regularization", "heading_level": null, "page_id": 152, "polygon": [[133.5, 264.0], [430.5, 264.0], [430.5, 275.34375], [133.5, 275.34375]]}, {"title": "18.3.3 The Support Vector Classifier", "heading_level": null, "page_id": 152, "polygon": [[133.5, 608.25], [329.30859375, 608.25], [329.30859375, 619.5234375], [133.5, 619.5234375]]}, {"title": "658 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 153, "polygon": [[132.0, 89.25], [334.08984375, 89.25], [334.08984375, 98.3232421875], [132.0, 98.3232421875]]}, {"title": "18.3.4 Feature Selection", "heading_level": null, "page_id": 153, "polygon": [[133.5, 500.25], [264.0, 500.25], [264.0, 512.40234375], [133.5, 512.40234375]]}, {"title": "18.3.5 Computational Shortcuts When p \\gg N", "heading_level": null, "page_id": 154, "polygon": [[133.5, 287.25], [376.5, 287.25], [376.5, 299.126953125], [133.5, 299.126953125]]}, {"title": "660 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 155, "polygon": [[132.0, 89.25], [332.296875, 89.25], [332.296875, 98.3232421875], [132.0, 98.3232421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 83], ["Equation", 5], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 25866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 219], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1097, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 42], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 96], ["Text", 7], ["Equation", 6], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 59], ["Text", 5], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1023, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 60], ["Text", 8], ["Equation", 4], ["ListItem", 3], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Line", 18], ["Span", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 45], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 814, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["TableCell", 112], ["Line", 43], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2100, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 31], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 618, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 44], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 53], ["Text", 7], ["ListItem", 3], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 288], ["Span", 146], ["Line", 47], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10427, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 54], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 48], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 71], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 814, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 46], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 76], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 865, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 53], ["Text", 5], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 29], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 46], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 44], ["Text", 5], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 12], ["Line", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 663, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 47], ["Text", 4], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 44], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 101], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1927, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 27], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 58], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 789, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Line", 66], ["Span", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 821, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 41], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 630], ["Line", 54], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 120], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 895, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 21], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["Equation", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 577, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 1462], ["Line", 677], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2868, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 33], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 49], ["Text", 7], ["TextInlineMath", 3], ["Equation", 3], ["ListItem", 3], ["SectionHeader", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 40], ["Text", 3], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 636, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 89], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 118], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 932, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 43], ["TextInlineMath", 4], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 403], ["Line", 67], ["TextInlineMath", 5], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1046, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 57], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 949, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 44], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 44], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1181, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 49], ["Text", 7], ["ListItem", 3], ["SectionHeader", 2], ["Equation", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1039, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 41], ["Text", 3], ["TextInlineMath", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 619, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 44], ["Line", 19], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 589, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 50], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1293, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 40], ["Text", 5], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 38], ["Line", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 35], ["Text", 4], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 61], ["Line", 20], ["ListItem", 3], ["Figure", 2], ["Text", 2], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1216, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 22], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 53], ["Equation", 4], ["TextInlineMath", 4], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1138, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 44], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 33], ["Equation", 4], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 2883], ["Line", 1284], ["Text", 3], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3214, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 3820], ["Line", 1243], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4032, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 35], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Picture", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 562, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Line", 74], ["Span", 29], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 699, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 76], ["TableCell", 8], ["ListItem", 6], ["Text", 5], ["Equation", 3], ["TextInlineMath", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 933, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 89], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 993, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 59], ["Text", 5], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 787, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 60], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2], ["Footnote", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 60], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 116], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 780, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 61], ["Text", 4], ["Equation", 4], ["TextInlineMath", 4], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1543, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 47], ["Text", 6], ["TextInlineMath", 2], ["Equation", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 56], ["Text", 6], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 38], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 67], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 40], ["Text", 4], ["ListItem", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 48], ["Text", 8], ["TextInlineMath", 3], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 39], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 2], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 61], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 85], ["Equation", 6], ["Text", 6], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 74], ["Text", 7], ["ListItem", 3], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1767, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 24], ["Text", 5], ["SectionHeader", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 42], ["ListItem", 6], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 41], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 785, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 34], ["Text", 3], ["Footnote", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 41], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 850, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 34], ["TextInlineMath", 2], ["SectionHeader", 2], ["Text", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 39], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1318, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 44], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 714, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 40], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Picture", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1417, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 39], ["Text", 5], ["Equation", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 721, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 39], ["Text", 5], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 192], ["Line", 56], ["Text", 3], ["SectionHeader", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 957, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 43], ["Text", 4], ["TextInlineMath", 2], ["Equation", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 861], ["Line", 396], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1513, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["Line", 39], ["Text", 6], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 15], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 61], ["Line", 27], ["Text", 5], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 64], ["TableCell", 63], ["Text", 4], ["ListItem", 3], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["Footnote", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 60], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 40], ["ListItem", 4], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 53], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 878, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 44], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 41], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 88], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1279, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 574, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 50], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 814, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 110, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 85], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 857, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 111, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 44], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 112, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 55], ["Text", 7], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 113, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 38], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 809, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 114, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 41], ["ListItem", 8], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 115, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 31], ["Text", 3], ["ListItem", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 735, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 116, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 45], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 117, "text_extraction_method": "pdftext", "block_counts": [["Span", 332], ["Line", 42], ["Text", 6], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7850, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 118, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 35], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 119, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 30], ["Text", 6], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 120, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 26], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 121, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 38], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 787, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 122, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 123, "text_extraction_method": "pdftext", "block_counts": [["Span", 440], ["Line", 39], ["Text", 7], ["ListItem", 4], ["TextInlineMath", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 124, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 51], ["Text", 5], ["Equation", 3], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 125, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 55], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 126, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 49], ["Text", 5], ["Equation", 3], ["TextInlineMath", 2], ["ListItem", 2], ["SectionHeader", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 127, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 45], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 128, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 58], ["Equation", 7], ["TextInlineMath", 7], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 129, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 55], ["ListItem", 5], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 728, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 130, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 43], ["Text", 7], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 131, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 46], ["ListItem", 6], ["TextInlineMath", 4], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 132, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 58], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 133, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 47], ["Text", 5], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 134, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 58], ["Text", 6], ["Equation", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 135, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 46], ["ListItem", 3], ["Text", 2], ["Equation", 1], ["TextInlineMath", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 136, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 50], ["Text", 5], ["ListItem", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Picture", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 137, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 45], ["TextInlineMath", 3], ["Text", 2], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 138, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 37], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 660, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 139, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 46], ["Text", 3], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 140, "text_extraction_method": "pdftext", "block_counts": [["Span", 51], ["Line", 43], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 141, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 34], ["ListItem", 6], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["ListGroup", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1232, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 142, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 49], ["Text", 6], ["Equation", 5], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 143, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 45], ["Text", 4], ["ListItem", 4], ["Equation", 3], ["TextInlineMath", 3], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 144, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 28], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 145, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 55], ["Text", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 877, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 146, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 42], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 147, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 61], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 148, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["Line", 40], ["TextInlineMath", 3], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 638, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 149, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 43], ["Text", 5], ["SectionHeader", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 656, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 150, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 38], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1090, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 151, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["TableCell", 84], ["Line", 38], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4792, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 152, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 57], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 153, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 43], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 154, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 44], ["Text", 6], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 155, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 59], ["TextInlineMath", 4], ["Text", 4], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_524-679"}