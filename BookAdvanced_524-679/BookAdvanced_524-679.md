# 14.3.3 Object Dissimilarity

Next we define a procedure for combining the  $p$ -individual attribute dissimilarities  $d_j(x_{ij}, x_{i'j}), j = 1, 2, \ldots, p$  into a single overall measure of dissimilarity  $D(x_i, x_{i'})$  between two objects or observations  $(x_i, x_{i'})$  possessing the respective attribute values. This is nearly always done by means of a weighted average (convex combination)

$$
D(x_i, x_{i'}) = \sum_{j=1}^{p} w_j \cdot d_j(x_{ij}, x_{i'j}); \quad \sum_{j=1}^{p} w_j = 1.
$$
 (14.24)

Here  $w_j$  is a weight assigned to the *j*th attribute regulating the relative influence of that variable in determining the overall dissimilarity between objects. This choice should be based on subject matter considerations.

It is important to realize that setting the weight  $w_i$  to the same value for each variable (say,  $w_j = 1 \forall j$ ) does not necessarily give all attributes equal influence. The influence of the jth attribute  $X_i$  on object dissimilarity  $D(x_i, x_{i'})$  (14.24) depends upon its relative contribution to the average object dissimilarity measure over all pairs of observations in the data set

$$
\bar{D} = \frac{1}{N^2} \sum_{i=1}^{N} \sum_{i'=1}^{N} D(x_i, x_{i'}) = \sum_{j=1}^{p} w_j \cdot \bar{d}_j,
$$

with

$$
\bar{d}_j = \frac{1}{N^2} \sum_{i=1}^{N} \sum_{i'=1}^{N} d_j(x_{ij}, x_{i'j})
$$
\n(14.25)

being the average dissimilarity on the jth attribute. Thus, the relative influence of the jth variable is  $w_j \cdot \bar{d}_j$ , and setting  $w_j \sim 1/\bar{d}_j$  would give all attributes equal influence in characterizing overall dissimilarity between objects. For example, with  $p$  quantitative variables and squared-error distance used for each coordinate, then (14.24) becomes the (weighted) squared Euclidean distance

$$
D_I(x_i, x_{i'}) = \sum_{j=1}^{p} w_j \cdot (x_{ij} - x_{i'j})^2
$$
 (14.26)

between pairs of points in an  $\mathbb{R}^p$ , with the quantitative variables as axes. In this case (14.25) becomes

$$
\bar{d}_j = \frac{1}{N^2} \sum_{i=1}^{N} \sum_{i'=1}^{N} (x_{ij} - x_{i'j})^2 = 2 \cdot \text{var}_j,
$$
\n(14.27)

where var<sub>j</sub> is the sample estimate of  $Var(X_i)$ . Thus, the relative importance of each such variable is proportional to its variance over the data

Image /page/1/Figure/1 description: The image displays two scatter plots side-by-side. Both plots have an x-axis labeled "X1" and a y-axis labeled "X2". The left plot shows two distinct clusters of points: an orange cluster on the left and a blue cluster on the right. The x-axis ranges from -6 to 4, and the y-axis ranges from -4 to 6. The right plot also shows two clusters of points, but they are arranged differently. There is an orange cluster in the lower half and a blue cluster in the upper half. The x-axis of the right plot ranges from -2 to 2, and the y-axis ranges from -2 to 2.

**FIGURE 14.5.** Simulated data: on the left, K-means clustering (with  $K=2$ ) has been applied to the raw data. The two colors indicate the cluster memberships. On the right, the features were first standardized before clustering. This is equivalent to using feature weights  $1/[2 \cdot \text{var}(X_j)]$ . The standardization has obscured the two well-separated groups. Note that each plot uses the same units in the horizontal and vertical axes.

set. In general, setting  $w_j = 1/\bar{d}_j$  for all attributes, irrespective of type, will cause each one of them to equally influence the overall dissimilarity between pairs of objects  $(x_i, x_{i'})$ . Although this may seem reasonable, and is often recommended, it can be highly counterproductive. If the goal is to segment the data into groups of similar objects, all attributes may not contribute equally to the (problem-dependent) notion of dissimilarity between objects. Some attribute value differences may reflect greater actual object dissimilarity in the context of the problem domain.

If the goal is to discover natural groupings in the data, some attributes may exhibit more of a grouping tendency than others. Variables that are more relevant in separating the groups should be assigned a higher influence in defining object dissimilarity. Giving all attributes equal influence in this case will tend to obscure the groups to the point where a clustering algorithm cannot uncover them. Figure 14.5 shows an example.

Although simple generic prescriptions for choosing the individual attribute dissimilarities  $d_j(x_{ij}, x_{i'j})$  and their weights  $w_j$  can be comforting, there is no substitute for careful thought in the context of each individual problem. Specifying an appropriate dissimilarity measure is far more important in obtaining success with clustering than choice of clustering algorithm. This aspect of the problem is emphasized less in the clustering literature than the algorithms themselves, since it depends on domain knowledge specifics and is less amenable to general research.

Finally, often observations have missing values in one or more of the attributes. The most common method of incorporating missing values in dissimilarity calculations (14.24) is to omit each observation pair  $x_{ij}, x_{i'j}$ having at least one value missing, when computing the dissimilarity between observations  $x_i$  and  $x'_i$ . This method can fail in the circumstance when both observations have no measured values in common. In this case both observations could be deleted from the analysis. Alternatively, the missing values could be imputed using the mean or median of each attribute over the nonmissing data. For categorical variables, one could consider the value "missing" as just another categorical value, if it were reasonable to consider two objects as being similar if they both have missing values on the same variables.

# 14.3.4 Clustering Algorithms

The goal of cluster analysis is to partition the observations into groups ("clusters") so that the pairwise dissimilarities between those assigned to the same cluster tend to be smaller than those in different clusters. Clustering algorithms fall into three distinct types: combinatorial algorithms, mixture modeling, and mode seeking.

Combinatorial algorithms work directly on the observed data with no direct reference to an underlying probability model. *Mixture modeling* supposes that the data is an *i.i.d* sample from some population described by a probability density function. This density function is characterized by a parameterized model taken to be a mixture of component density functions; each component density describes one of the clusters. This model is then fit to the data by maximum likelihood or corresponding Bayesian approaches. Mode seekers ("bump hunters") take a nonparametric perspective, attempting to directly estimate distinct modes of the probability density function. Observations "closest" to each respective mode then define the individual clusters.

Mixture modeling is described in Section 6.8. The PRIM algorithm, discussed in Sections 9.3 and 14.2.5, is an example of mode seeking or "bump hunting." We discuss combinatorial algorithms next.

# 14.3.5 Combinatorial Algorithms

The most popular clustering algorithms directly assign each observation to a group or cluster without regard to a probability model describing the data. Each observation is uniquely labeled by an integer  $i \in \{1, \dots, N\}$ . A prespecified number of clusters  $K < N$  is postulated, and each one is labeled by an integer  $k \in \{1, ..., K\}$ . Each observation is assigned to one and only one cluster. These assignments can be characterized by a manyto-one mapping, or encoder  $k = C(i)$ , that assigns the *i*th observation to the kth cluster. One seeks the particular encoder  $C^*(i)$  that achieves the

# 508 14. Unsupervised Learning

required goal (details below), based on the dissimilarities  $d(x_i, x_{i'})$  between every pair of observations. These are specified by the user as described above. Generally, the encoder  $C(i)$  is explicitly delineated by giving its value (cluster assignment) for each observation  $i$ . Thus, the "parameters" of the procedure are the individual cluster assignments for each of the N observations. These are adjusted so as to minimize a "loss" function that characterizes the degree to which the clustering goal is not met.

One approach is to directly specify a mathematical loss function and attempt to minimize it through some combinatorial optimization algorithm. Since the goal is to assign close points to the same cluster, a natural loss (or "energy") function would be

$$
W(C) = \frac{1}{2} \sum_{k=1}^{K} \sum_{C(i)=k} \sum_{C(i')=k} d(x_i, x_{i'}).
$$
 (14.28)

This criterion characterizes the extent to which observations assigned to the same cluster tend to be close to one another. It is sometimes referred to as the "within cluster" point scatter since

$$
T = \frac{1}{2} \sum_{i=1}^{N} \sum_{i'=1}^{N} d_{ii'} = \frac{1}{2} \sum_{k=1}^{K} \sum_{C(i)=k} \left( \sum_{C(i')=k} d_{ii'} + \sum_{C(i')\neq k} d_{ii'} \right),
$$

or

$$
T = W(C) + B(C),
$$

where  $d_{ii'} = d(x_i, x_{i'})$ . Here T is the *total* point scatter, which is a constant given the data, independent of cluster assignment. The quantity

$$
B(C) = \frac{1}{2} \sum_{k=1}^{K} \sum_{C(i)=k} \sum_{C(i') \neq k} d_{ii'}
$$
 (14.29)

is the between-cluster point scatter. This will tend to be large when observations assigned to different clusters are far apart. Thus one has

$$
W(C) = T - B(C)
$$

and minimizing  $W(C)$  is equivalent to *maximizing B(C)*.

Cluster analysis by combinatorial optimization is straightforward in principle. One simply minimizes  $W$  or equivalently maximizes  $B$  over all possible assignments of the  $N$  data points to  $K$  clusters. Unfortunately, such optimization by complete enumeration is feasible only for very small data sets. The number of distinct assignments is (Jain and Dubes, 1988)

$$
S(N,K) = \frac{1}{K!} \sum_{k=1}^{K} (-1)^{K-k} \binom{K}{k} k^N.
$$
 (14.30)

For example,  $S(10, 4) = 34, 105$  which is quite feasible. But,  $S(N, K)$  grows very rapidly with increasing values of its arguments. Already  $S(19,4) \simeq$ 

 $10^{10}$ , and most clustering problems involve much larger data sets than  $N = 19$ . For this reason, practical clustering algorithms are able to examine only a very small fraction of all possible encoders  $k = C(i)$ . The goal is to identify a small subset that is likely to contain the optimal one, or at least a good suboptimal partition.

Such feasible strategies are based on iterative greedy descent. An initial partition is specified. At each iterative step, the cluster assignments are changed in such a way that the value of the criterion is improved from its previous value. Clustering algorithms of this type differ in their prescriptions for modifying the cluster assignments at each iteration. When the prescription is unable to provide an improvement, the algorithm terminates with the current assignments as its solution. Since the assignment of observations to clusters at any iteration is a perturbation of that for the previous iteration, only a very small fraction of all possible assignments (14.30) are examined. However, these algorithms converge to local optima which may be highly suboptimal when compared to the global optimum.

## 14.3.6 K-means

The K-means algorithm is one of the most popular iterative descent clustering methods. It is intended for situations in which all variables are of the quantitative type, and squared Euclidean distance

$$
d(x_i, x_{i'}) = \sum_{j=1}^{p} (x_{ij} - x_{i'j})^2 = ||x_i - x_{i'}||^2
$$

is chosen as the dissimilarity measure. Note that weighted Euclidean distance can be used by redefining the  $x_{ij}$  values (Exercise 14.1).

The within-point scatter (14.28) can be written as

$$
W(C) = \frac{1}{2} \sum_{k=1}^{K} \sum_{C(i)=k} \sum_{C(i')=k} ||x_i - x_{i'}||^2
$$
  
= 
$$
\sum_{k=1}^{K} N_k \sum_{C(i)=k} ||x_i - \bar{x}_k||^2,
$$
 (14.31)

where  $\bar{x}_k = (\bar{x}_{1k}, \dots, \bar{x}_{pk})$  is the mean vector associated with the kth cluster, and  $N_k = \sum_{i=1}^N I(C(i) = k)$ . Thus, the criterion is minimized by assigning the  $N$  observations to the  $K$  clusters in such a way that within each cluster the average dissimilarity of the observations from the cluster mean, as defined by the points in that cluster, is minimized.

An iterative descent algorithm for solving

# 510 14. Unsupervised Learning

Algorithm 14.1 K-means Clustering.

- 1. For a given cluster assignment  $C$ , the total cluster variance (14.33) is minimized with respect to  $\{m_1, \ldots, m_K\}$  yielding the means of the currently assigned clusters (14.32).
- 2. Given a current set of means  $\{m_1, \ldots, m_K\}$ , (14.33) is minimized by assigning each observation to the closest (current) cluster mean. That is,

$$
C(i) = \underset{1 \le k \le K}{\text{argmin}} ||x_i - m_k||^2. \tag{14.34}
$$

3. Steps 1 and 2 are iterated until the assignments do not change.

$$
C^* = \min_C \sum_{k=1}^K N_k \sum_{C(i)=k} ||x_i - \bar{x}_k||^2
$$

can be obtained by noting that for any set of observations S

$$
\bar{x}_S = \underset{m}{\text{argmin}} \sum_{i \in S} ||x_i - m||^2. \tag{14.32}
$$

Hence we can obtain  $C^*$  by solving the enlarged optimization problem

$$
\min_{C, \{m_k\}_1^K} \sum_{k=1}^K N_k \sum_{C(i)=k} ||x_i - m_k||^2.
$$
\n(14.33)

This can be minimized by an alternating optimization procedure given in Algorithm 14.1.

Each of steps 1 and 2 reduces the value of the criterion (14.33), so that convergence is assured. However, the result may represent a suboptimal local minimum. The algorithm of Hartigan and Wong (1979) goes further, and ensures that there is no single switch of an observation from one group to another group that will decrease the objective. In addition, one should start the algorithm with many different random choices for the starting means, and choose the solution having smallest value of the objective function.

Figure 14.6 shows some of the  $K$ -means iterations for the simulated data of Figure 14.4. The centroids are depicted by "O"s. The straight lines show the partitioning of points, each sector being the set of points closest to each centroid. This partitioning is called the Voronoi tessellation. After 20 iterations the procedure has converged.

# 14.3.7 Gaussian Mixtures as Soft K-means Clustering

The K-means clustering procedure is closely related to the EM algorithm for estimating a certain Gaussian mixture model. (Sections 6.8 and 8.5.1).

Image /page/6/Figure/1 description: This image displays a k-means clustering algorithm's progression through four plots. The top-left plot, labeled "Initial Centroids", shows three distinct clusters of data points (green, blue, and orange) with their initial centroid locations marked by larger colored circles. The top-right plot, "Initial Partition", illustrates the initial assignment of data points to clusters based on the initial centroids, with partitioning lines drawn. The bottom-left plot, "Iteration Number 2", shows the state after two iterations, with updated centroid positions and refined partitioning lines. The bottom-right plot, "Iteration Number 20", depicts the final state after 20 iterations, demonstrating the convergence of the algorithm with the centroids and partitions stabilized.

FIGURE 14.6. Successive iterations of the K-means clustering algorithm for the simulated data of Figure 14.4.

# 512 14. Unsupervised Learning

Image /page/7/Figure/1 description: This image displays four plots, arranged in a 2x2 grid, illustrating the effect of standard deviation (sigma) on probability distributions and responsibilities. The top row shows plots for sigma = 1.0, and the bottom row shows plots for sigma = 0.2. The left column plots show two overlapping bell curves, one blue and one orange, representing probability distributions. A green dashed line indicates a point of intersection, with a blue square to its left and an orange square to its right. The right column plots show two sigmoid curves, one blue and one orange, representing responsibilities. The y-axis for these plots is labeled 'Responsibilities' and ranges from 0.0 to 1.0. A green dashed line marks a point where the blue curve drops sharply and the orange curve rises sharply, indicating a transition in responsibilities. In both rows, the curves are closer together and the transition is steeper when sigma is smaller (0.2) compared to when sigma is larger (1.0).

FIGURE 14.7. (Left panels:) two Gaussian densities  $g_0(x)$  and  $g_1(x)$  (blue and orange) on the real line, and a single data point (green dot) at  $x = 0.5$ . The colored squares are plotted at  $x = -1.0$  and  $x = 1.0$ , the means of each density. (Right panels:) the relative densities  $g_0(x)/(g_0(x) + g_1(x))$  and  $g_1(x)/(g_0(x) + g_1(x))$ , called the "responsibilities" of each cluster, for this data point. In the top panels, the Gaussian standard deviation  $\sigma = 1.0$ ; in the bottom panels  $\sigma = 0.2$ . The EM algorithm uses these responsibilities to make a "soft" assignment of each data point to each of the two clusters. When  $\sigma$  is fairly large, the responsibilities can be near 0.5 (they are 0.36 and 0.64 in the top right panel). As  $\sigma \to 0$ , the responsibilities  $\rightarrow$  1, for the cluster center closest to the target point, and 0 for all other clusters. This "hard" assignment is seen in the bottom right panel.

The E-step of the EM algorithm assigns "responsibilities" for each data point based in its relative density under each mixture component, while the M-step recomputes the component density parameters based on the current responsibilities. Suppose we specify  $K$  mixture components, each with a Gaussian density having scalar covariance matrix  $\sigma^2 I$ . Then the relative density under each mixture component is a monotone function of the Euclidean distance between the data point and the mixture center. Hence in this setup EM is a "soft" version of  $K$ -means clustering, making probabilistic (rather than deterministic) assignments of points to cluster centers. As the variance  $\sigma^2 \to 0$ , these probabilities become 0 and 1, and the two methods coincide. Details are given in Exercise 14.2. Figure 14.7 illustrates this result for two clusters on the real line.

# 14.3.8 Example: Human Tumor Microarray Data

We apply K-means clustering to the human tumor microarray data described in Chapter 1. This is an example of high-dimensional clustering.

Image /page/8/Figure/1 description: This is a line graph showing the relationship between the number of clusters (K) on the x-axis and the sum of squares on the y-axis. The x-axis ranges from 2 to 10, labeled as "Number of Clusters K". The y-axis ranges from 160000 to 240000, labeled as "Sum of Squares". There are 9 data points plotted on the graph, connected by a dashed red line. The sum of squares decreases as the number of clusters increases. The data points are approximately at (1, 250000), (2, 240000), (3, 220000), (4, 210000), (5, 200000), (6, 185000), (7, 175000), (8, 170000), (9, 165000), and (10, 160000).

FIGURE 14.8. Total within-cluster sum of squares for K-means clustering applied to the human tumor microarray data.

TABLE 14.2. Human tumor data: number of cancer cases of each type, in each of the three clusters from K-means clustering.

| Cluster | Breast   | CNS   | Colon   | K562     | Leukemia | MCF7    |
|---------|----------|-------|---------|----------|----------|---------|
| 1       | 3        | 5     | 0       | 0        | 0        | 0       |
| 2       | 2        | 0     | 0       | 2        | 6        | 2       |
| 3       | 2        | 0     | 7       | 0        | 0        | 0       |
| Cluster | Melanoma | NSCLC | Ovarian | Prostate | Renal    | Unknown |
| 1       | 1        | 7     | 6       | 2        | 9        | 1       |
| 2       | 7        | 2     | 0       | 0        | 0        | 0       |
| 3       | 0        | 0     | 0       | 0        | 0        | 0       |

The data are a  $6830 \times 64$  matrix of real numbers, each representing an expression measurement for a gene (row) and sample (column). Here we cluster the samples, each of which is a vector of length 6830, corresponding to expression values for the 6830 genes. Each sample has a label such as breast (for breast cancer), melanoma, and so on; we don't use these labels in the clustering, but will examine posthoc which labels fall into which clusters.

We applied  $K$ -means clustering with  $K$  running from 1 to 10, and computed the total within-sum of squares for each clustering, shown in Figure 14.8. Typically one looks for a kink in the sum of squares curve (or its logarithm) to locate the optimal number of clusters (see Section 14.3.11). Here there is no clear indication: for illustration we chose  $K = 3$  giving the three clusters shown in Table 14.2.

Image /page/9/Picture/1 description: Three black and white images of an elderly man with a white beard and glasses. The man is looking directly at the camera. The first image is a standard black and white photograph. The second image appears to be a digitally altered version of the first, with increased contrast and some areas appearing more pixelated. The third image is a highly stylized, posterized version of the man's face, with only a few shades of gray and black, creating a stark, graphic effect.

FIGURE 14.9. Sir Ronald A. Fisher  $(1890 - 1962)$  was one of the founders of modern day statistics, to whom we owe maximum-likelihood, sufficiency, and many other fundamental concepts. The image on the left is a  $1024 \times 1024$  grayscale image at 8 bits per pixel. The center image is the result of  $2 \times 2$  block VQ, using 200 code vectors, with a compression rate of 1.9 bits/pixel. The right image uses only four code vectors, with a compression rate of 0.50 bits/pixel

We see that the procedure is successful at grouping together samples of the same cancer. In fact, the two breast cancers in the second cluster were later found to be misdiagnosed and were melanomas that had metastasized. However, K-means clustering has shortcomings in this application. For one, it does not give a linear ordering of objects within a cluster: we have simply listed them in alphabetic order above. Secondly, as the number of clusters K is changed, the cluster memberships can change in arbitrary ways. That is, with say four clusters, the clusters need not be nested within the three clusters above. For these reasons, hierarchical clustering (described later), is probably preferable for this application.

# 14.3.9 Vector Quantization

The K-means clustering algorithm represents a key tool in the apparently unrelated area of image and signal compression, particularly in vector quan*tization* or VQ (Gersho and Gray, 1992). The left image in Figure  $14.9^2$  is a digitized photograph of a famous statistician, Sir Ronald Fisher. It consists of  $1024 \times 1024$  pixels, where each pixel is a grayscale value ranging from 0 to 255, and hence requires 8 bits of storage per pixel. The entire image occupies 1 megabyte of storage. The center image is a VQ-compressed version of the left panel, and requires 0.239 of the storage (at some loss in quality). The right image is compressed even more, and requires only 0.0625 of the storage (at a considerable loss in quality).

The version of VQ implemented here first breaks the image into small blocks, in this case  $2\times2$  blocks of pixels. Each of the  $512\times512$  blocks of four

<sup>2</sup>This example was prepared by Maya Gupta.

numbers is regarded as a vector in  $\mathbb{R}^4$ . A K-means clustering algorithm (also known as Lloyd's algorithm in this context) is run in this space. The center image uses  $K = 200$ , while the right image  $K = 4$ . Each of the  $512 \times 512$  pixel blocks (or points) is approximated by its closest cluster centroid, known as a codeword. The clustering process is called the encoding step, and the collection of centroids is called the codebook.

To represent the approximated image, we need to supply for each block the identity of the codebook entry that approximates it. This will require  $log<sub>2</sub>(K)$  bits per block. We also need to supply the codebook itself, which is  $K \times 4$  real numbers (typically negligible). Overall, the storage for the compressed image amounts to  $\log_2(K)/(4 \cdot 8)$  of the original (0.239 for  $K = 200, 0.063$  for  $K = 4$ ). This is typically expressed as a *rate* in bits per pixel:  $log_2(K)/4$ , which are 1.91 and 0.50, respectively. The process of constructing the approximate image from the centroids is called the decoding step.

Why do we expect VQ to work at all? The reason is that for typical everyday images like photographs, many of the blocks look the same. In this case there are many almost pure white blocks, and similarly pure gray blocks of various shades. These require only one block each to represent them, and then multiple pointers to that block.

What we have described is known as *lossy* compression, since our images are degraded versions of the original. The degradation or distortion is usually measured in terms of mean squared error. In this case  $D = 0.89$ for  $K = 200$  and  $D = 16.95$  for  $K = 4$ . More generally a rate/distortion curve would be used to assess the tradeoff. One can also perform lossless compression using block clustering, and still capitalize on the repeated patterns. If you took the original image and losslessly compressed it, the best you would do is 4.48 bits per pixel.

We claimed above that  $\log_2(K)$  bits were needed to identify each of the K codewords in the codebook. This uses a fixed-length code, and is inefficient if some codewords occur many more times than others in the image. Using Shannon coding theory, we know that in general a variable length code will do better, and the rate then becomes  $-\sum_{\ell=1}^{K} p_{\ell} \log_2(p_{\ell})/4$ . The term in the numerator is the entropy of the distribution  $p_\ell$  of the codewords in the image. Using variable length coding our rates come down to 1.42 and 0.39, respectively. Finally, there are many generalizations of VQ that have been developed: for example, tree-structured VQ finds the centroids with a top-down, 2-means style algorithm, as alluded to in Section 14.3.12. This allows successive refinement of the compression. Further details may be found in Gersho and Gray (1992).

# 14.3.10 K-medoids

As discussed above, the K-means algorithm is appropriate when the dissimilarity measure is taken to be squared Euclidean distance  $D(x_i, x_{i'})$ 

# 516 14. Unsupervised Learning

Algorithm 14.2 K-medoids Clustering.

1. For a given cluster assignment  $C$  find the observation in the cluster minimizing total distance to other points in that cluster:

$$
i_k^* = \underset{\{i:C(i)=k\}}{\text{argmin}} \sum_{C(i')=k} D(x_i, x_{i'}). \tag{14.35}
$$

Then  $m_k = x_{i_k^*}, k = 1, 2, ..., K$  are the current estimates of the cluster centers.

2. Given a current set of cluster centers  $\{m_1, \ldots, m_K\}$ , minimize the total error by assigning each observation to the closest (current) cluster center:

$$
C(i) = \underset{1 \le k \le K}{\operatorname{argmin}} D(x_i, m_k). \tag{14.36}
$$

3. Iterate steps 1 and 2 until the assignments do not change.

(14.112). This requires all of the variables to be of the quantitative type. In addition, using squared Euclidean distance places the highest influence on the largest distances. This causes the procedure to lack robustness against outliers that produce very large distances. These restrictions can be removed at the expense of computation.

The only part of the K-means algorithm that assumes squared Euclidean distance is the minimization step  $(14.32)$ ; the cluster representatives  ${m_1, \ldots, m_K}$  in (14.33) are taken to be the means of the currently assigned clusters. The algorithm can be generalized for use with arbitrarily defined dissimilarities  $D(x_i, x_{i'})$  by replacing this step by an explicit optimization with respect to  $\{m_1, \ldots, m_K\}$  in (14.33). In the most common form, centers for each cluster are restricted to be one of the observations assigned to the cluster, as summarized in Algorithm 14.2. This algorithm assumes attribute data, but the approach can also be applied to data described only by proximity matrices (Section 14.3.1). There is no need to explicitly compute cluster centers; rather we just keep track of the indices  $i_k^*$ .

Solving  $(14.32)$  for each provisional cluster k requires an amount of computation proportional to the number of observations assigned to it, whereas for solving (14.35) the computation increases to  $O(N_k^2)$ . Given a set of cluster "centers,"  $\{i_1, \ldots, i_K\}$ , obtaining the new assignments

$$
C(i) = \underset{1 \le k \le K}{\operatorname{argmin}} d_{ii^*_{k}} \tag{14.37}
$$

requires computation proportional to  $K \cdot N$  as before. Thus, K-medoids is far more computationally intensive than K-means.

Alternating between (14.35) and (14.37) represents a particular heuristic search strategy for trying to solve

|            | BEL  | BRA  | CHI  | CUB  | EGY  | FRA  | IND  | ISR  | USA  | USS  | YUG  |
|------------|------|------|------|------|------|------|------|------|------|------|------|
| <b>BRA</b> | 5.58 |      |      |      |      |      |      |      |      |      |      |
| CHI        | 7.00 | 6.50 |      |      |      |      |      |      |      |      |      |
| CUB        | 7.08 | 7.00 | 3.83 |      |      |      |      |      |      |      |      |
| EGY        | 4.83 | 5.08 | 8.17 | 5.83 |      |      |      |      |      |      |      |
| <b>FRA</b> | 2.17 | 5.75 | 6.67 | 6.92 | 4.92 |      |      |      |      |      |      |
| <b>IND</b> | 6.42 | 5.00 | 5.58 | 6.00 | 4.67 | 6.42 |      |      |      |      |      |
| <b>ISR</b> | 3.42 | 5.50 | 6.42 | 6.42 | 5.00 | 3.92 | 6.17 |      |      |      |      |
| <b>USA</b> | 2.50 | 4.92 | 6.25 | 7.33 | 4.50 | 2.25 | 6.33 | 2.75 |      |      |      |
| <b>USS</b> | 6.08 | 6.67 | 4.25 | 2.67 | 6.00 | 6.17 | 6.17 | 6.92 |      |      |      |
| YUG        | 5.25 | 6.83 | 4.50 | 3.75 | 5.75 | 5.42 | 6.08 | 5.83 | 6.67 | 3.67 |      |
| ZAI        | 4.75 | 3.00 | 6.08 | 6.67 | 5.00 | 5.58 | 4.83 | 6.17 | 5.67 | 6.50 | 6.92 |

TABLE 14.3. Data from a political science survey: values are average pairwise dissimilarities of countries from a questionnaire given to political science students.

$$
\min_{C, \ \{i_k\}_1^K} \sum_{k=1}^K \sum_{C(i)=k} d_{ii_k}.\tag{14.38}
$$

Kaufman and Rousseeuw (1990) propose an alternative strategy for directly solving (14.38) that provisionally exchanges each center  $i_k$  with an observation that is not currently a center, selecting the exchange that produces the greatest reduction in the value of the criterion (14.38). This is repeated until no advantageous exchanges can be found. Massart et al. (1983) derive a branch-and-bound combinatorial method that finds the global minimum of (14.38) that is practical only for very small data sets.

### Example: Country Dissimilarities

This example, taken from Kaufman and Rousseeuw (1990), comes from a study in which political science students were asked to provide pairwise dissimilarity measures for 12 countries: Belgium, Brazil, Chile, Cuba, Egypt, France, India, Israel, United States, Union of Soviet Socialist Republics, Yugoslavia and Zaire. The average dissimilarity scores are given in Table 14.3. We applied 3-medoid clustering to these dissimilarities. Note that K-means clustering could not be applied because we have only distances rather than raw observations. The left panel of Figure 14.10 shows the dissimilarities reordered and blocked according to the 3-medoid clustering. The right panel is a two-dimensional multidimensional scaling plot, with the 3-medoid clusters assignments indicated by colors (multidimensional scaling is discussed in Section 14.8.) Both plots show three well-separated clusters, but the MDS display indicates that "Egypt" falls about halfway between two clusters.

Image /page/13/Figure/1 description: The image displays two plots side-by-side. The left plot is a reordered dissimilarity matrix, visualized as a heatmap with shades of red and black. The rows and columns are labeled with country codes: CHI, CUB, USS, YUG, BRA, IND, ZAI, BEL, EGY, FRA, ISR, and USA. The dissimilarity matrix shows a gradient from lighter red (lower dissimilarity) to darker red and black (higher dissimilarity). The right plot is a scatter plot showing the results of a Multidimensional Scaling (MDS) analysis. The x-axis is labeled "First MDS Coordinate" and ranges from -2 to 4. The y-axis is labeled "Second MDS Coordinate" and ranges from -2 to 3. The scatter plot displays country codes as points, colored differently: green for BRA, ZAI, and IND; blue for USA, BEL, ISR, and FRA; and red for CHI, CUB, USS, and YUG. The points are clustered in different regions of the plot, suggesting relationships between the countries based on their dissimilarities.

FIGURE 14.10. Survey of country dissimilarities. (Left panel:) dissimilarities reordered and blocked according to 3-medoid clustering. Heat map is coded from most similar (dark red) to least similar (bright red). (Right panel:) two-dimensional multidimensional scaling plot, with 3-medoid clusters indicated by different colors.

# 14.3.11 Practical Issues

In order to apply K-means or K-medoids one must select the number of clusters  $K^*$  and an initialization. The latter can be defined by specifying an initial set of centers  $\{m_1, \ldots, m_K\}$  or  $\{i_1, \ldots, i_K\}$  or an initial encoder  $C(i)$ . Usually specifying the centers is more convenient. Suggestions range from simple random selection to a deliberate strategy based on forward stepwise assignment. At each step a new center  $i_k$  is chosen to minimize the criterion (14.33) or (14.38), given the centers  $i_1, \ldots, i_{k-1}$  chosen at the previous steps. This continues for  $K$  steps, thereby producing  $K$  initial centers with which to begin the optimization algorithm.

A choice for the number of clusters K depends on the goal. For data segmentation  $K$  is usually defined as part of the problem. For example, a company may employ K sales people, and the goal is to partition a customer database into  $K$  segments, one for each sales person, such that the customers assigned to each one are as similar as possible. Often, however, cluster analysis is used to provide a descriptive statistic for ascertaining the extent to which the observations comprising the data base fall into natural distinct groupings. Here the number of such groups  $K^*$  is unknown and one requires that it, as well as the groupings themselves, be estimated from the data.

Data-based methods for estimating  $K^*$  typically examine the withincluster dissimilarity  $W_K$  as a function of the number of clusters K. Separate solutions are obtained for  $K \in \{1, 2, ..., K_{\text{max}}\}$ . The corresponding values

 ${W_1, W_2, \ldots, W_{K_{\text{max}}}}$  generally decrease with increasing K. This will be the case even when the criterion is evaluated on an independent test set, since a large number of cluster centers will tend to fill the feature space densely and thus will be close to all data points. Thus cross-validation techniques, so useful for model selection in supervised learning, cannot be utilized in this context.

The intuition underlying the approach is that if there are actually  $K^*$ distinct groupings of the observations (as defined by the dissimilarity measure), then for  $K < K^*$  the clusters returned by the algorithm will each contain a subset of the true underlying groups. That is, the solution will not assign observations in the same naturally occurring group to different estimated clusters. To the extent that this is the case, the solution criterion value will tend to decrease substantially with each successive increase in the number of specified clusters,  $W_{K+1} \ll W_K$ , as the natural groups are successively assigned to separate clusters. For  $K > K^*$ , one of the estimated clusters must partition at least one of the natural groups into two subgroups. This will tend to provide a smaller decrease in the criterion as  $K$  is further increased. Splitting a natural group, within which the observations are all quite close to each other, reduces the criterion less than partitioning the union of two well-separated groups into their proper constituents.

To the extent this scenario is realized, there will be a sharp decrease in successive differences in criterion value,  $W_K - W_{K+1}$ , at  $K = K^*$ . That is,  $\{W_K - W_{K+1} | K < K^*\} \gg \{W_K - W_{K+1} | K \geq K^*\}.$  An estimate  $\hat{K}^*$  for  $K^*$  is then obtained by identifying a "kink" in the plot of  $W_K$  as a function of  $K$ . As with other aspects of clustering procedures, this approach is somewhat heuristic.

The recently proposed Gap statistic (Tibshirani et al., 2001b) compares the curve  $\log W_K$  to the curve obtained from data uniformly distributed over a rectangle containing the data. It estimates the optimal number of clusters to be the place where the gap between the two curves is largest. Essentially this is an automatic way of locating the aforementioned "kink." It also works reasonably well when the data fall into a single cluster, and in that case will tend to estimate the optimal number of clusters to be one. This is the scenario where most other competing methods fail.

Figure 14.11 shows the result of the Gap statistic applied to simulated data of Figure 14.4. The left panel shows  $\log W_K$  for  $k = 1, 2, \ldots, 8$  clusters (green curve) and the expected value of  $\log W_K$  over 20 simulations from uniform data (blue curve). The right panel shows the gap curve, which is the expected curve minus the observed curve. Shown also are error bars of halfwidth  $s'_{K} = s_{K} \sqrt{1 + 1/20}$ , where  $s_{K}$  is the standard deviation of log  $W_{K}$ over the 20 simulations. The Gap curve is maximized at  $K = 2$  clusters. If  $G(K)$  is the Gap curve at K clusters, the formal rule for estimating  $K^*$  is

$$
K^* = \underset{K}{\text{argmin}} \{ K | G(K) \ge G(K+1) - s'_{K+1} \}. \tag{14.39}
$$

Image /page/15/Figure/1 description: The image contains two plots. The left plot shows the relationship between the number of clusters and log WK - log W1. The x-axis is labeled "Number of Clusters" and ranges from 2 to 8. The y-axis is labeled "log WK - log W1" and ranges from -3.0 to 0.0. There are two dashed lines with circular markers, one blue and one green, showing a decreasing trend. The right plot shows the relationship between the number of clusters and the gap. The x-axis is labeled "Number of Clusters" and ranges from 2 to 8. The y-axis is labeled "Gap" and ranges from -0.5 to 1.0. There is a red line with circular markers and error bars, showing a fluctuating trend.

FIGURE 14.11. (Left panel): observed (green) and expected (blue) values of  $\log W_K$  for the simulated data of Figure 14.4. Both curves have been translated to equal zero at one cluster. (Right panel): Gap curve, equal to the difference between the observed and expected values of  $\log W_K$ . The Gap estimate  $K^*$  is the smallest K producing a gap within one standard deviation of the gap at  $K + 1$ ; here  $K^* = 2$ .

This gives  $K^* = 2$ , which looks reasonable from Figure 14.4.

# 14.3.12 Hierarchical Clustering

The results of applying K-means or K-medoids clustering algorithms depend on the choice for the number of clusters to be searched and a starting configuration assignment. In contrast, hierarchical clustering methods do not require such specifications. Instead, they require the user to specify a measure of dissimilarity between (disjoint) groups of observations, based on the pairwise dissimilarities among the observations in the two groups. As the name suggests, they produce hierarchical representations in which the clusters at each level of the hierarchy are created by merging clusters at the next lower level. At the lowest level, each cluster contains a single observation. At the highest level there is only one cluster containing all of the data.

Strategies for hierarchical clustering divide into two basic paradigms: agglomerative (bottom-up) and divisive (top-down). Agglomerative strategies start at the bottom and at each level recursively merge a selected pair of clusters into a single cluster. This produces a grouping at the next higher level with one less cluster. The pair chosen for merging consist of the two groups with the smallest intergroup dissimilarity. Divisive methods start at the top and at each level recursively split one of the existing clusters at that level into two new clusters. The split is chosen to produce two new groups with the largest between-group dissimilarity. With both paradigms there are  $N-1$  levels in the hierarchy.

Each level of the hierarchy represents a particular grouping of the data into disjoint clusters of observations. The entire hierarchy represents an ordered sequence of such groupings. It is up to the user to decide which level (if any) actually represents a "natural" clustering in the sense that observations within each of its groups are sufficiently more similar to each other than to observations assigned to different groups at that level. The Gap statistic described earlier can be used for this purpose.

Recursive binary splitting/agglomeration can be represented by a rooted binary tree. The nodes of the trees represent groups. The root node represents the entire data set. The N terminal nodes each represent one of the individual observations (singleton clusters). Each nonterminal node ("parent") has two daughter nodes. For divisive clustering the two daughters represent the two groups resulting from the split of the parent; for agglomerative clustering the daughters represent the two groups that were merged to form the parent.

All agglomerative and some divisive methods (when viewed bottom-up) possess a monotonicity property. That is, the dissimilarity between merged clusters is monotone increasing with the level of the merger. Thus the binary tree can be plotted so that the height of each node is proportional to the value of the intergroup dissimilarity between its two daughters. The terminal nodes representing individual observations are all plotted at zero height. This type of graphical display is called a *dendrogram*.

A dendrogram provides a highly interpretable complete description of the hierarchical clustering in a graphical format. This is one of the main reasons for the popularity of hierarchical clustering methods.

For the microarray data, Figure 14.12 shows the dendrogram resulting from agglomerative clustering with average linkage; agglomerative clustering and this example are discussed in more detail later in this chapter. Cutting the dendrogram horizontally at a particular height partitions the data into disjoint clusters represented by the vertical lines that intersect it. These are the clusters that would be produced by terminating the procedure when the optimal intergroup dissimilarity exceeds that threshold cut value. Groups that merge at high values, relative to the merger values of the subgroups contained within them lower in the tree, are candidates for natural clusters. Note that this may occur at several different levels, indicating a clustering hierarchy: that is, clusters nested within clusters.

Such a dendrogram is often viewed as a graphical summary of the data itself, rather than a description of the results of the algorithm. However, such interpretations should be treated with caution. First, different hierarchical methods (see below), as well as small changes in the data, can lead to quite different dendrograms. Also, such a summary will be valid only to the extent that the pairwise observation dissimilarities possess the hierar-

Image /page/17/Figure/1 description: A dendrogram shows the hierarchical clustering of different cancer types. The clusters are labeled with cancer types such as LEUKEMIA, MELANOMA, RENAL, BREAST, NSCLC, OVARIAN, PROSTATE, COLON, and CNS. Some samples are also labeled with specific cell lines like K562B-repro, K562A-repro, MCF7A-repro, and MCF7D-repro. The dendrogram visually represents the relationships and similarities between these cancer samples.

FIGURE 14.12. Dendrogram from agglomerative hierarchical clustering with average linkage to the human tumor microarray data.

chical structure produced by the algorithm. Hierarchical methods impose hierarchical structure whether or not such structure actually exists in the data.

The extent to which the hierarchical structure produced by a dendrogram actually represents the data itself can be judged by the cophenetic correlation coefficient. This is the correlation between the  $N(N-1)/2$  pairwise observation dissimilarities  $d_{ii'}$  input to the algorithm and their corresponding *cophenetic* dissimilarities  $C_{ii'}$  derived from the dendrogram. The cophenetic dissimilarity  $C_{ii'}$  between two observations  $(i, i')$  is the intergroup dissimilarity at which observations  $i$  and  $i'$  are first joined together in the same cluster.

The cophenetic dissimilarity is a very restrictive dissimilarity measure. First, the  $C_{ii'}$  over the observations must contain many ties, since only  $N-1$ of the total  $N(N-1)/2$  values can be distinct. Also these dissimilarities obey the ultrametric inequality

$$
C_{ii'} \le \max\{C_{ik}, C_{i'k}\}\tag{14.40}
$$

for any three observations  $(i, i', k)$ . As a geometric example, suppose the data were represented as points in a Euclidean coordinate system. In order for the set of interpoint distances over the data to conform to (14.40), the triangles formed by all triples of points must be isosceles triangles with the unequal length no longer than the length of the two equal sides (Jain and Dubes, 1988). Therefore it is unrealistic to expect general dissimilarities over arbitrary data sets to closely resemble their corresponding cophenetic dissimilarities as calculated from a dendrogram, especially if there are not many tied values. Thus the dendrogram should be viewed mainly as a description of the *clustering* structure of the data as imposed by the particular algorithm employed.

### Agglomerative Clustering

Agglomerative clustering algorithms begin with every observation representing a singleton cluster. At each of the  $N-1$  steps the closest two (least dissimilar) clusters are merged into a single cluster, producing one less cluster at the next higher level. Therefore, a measure of dissimilarity between two clusters (groups of observations) must be defined.

Let G and H represent two such groups. The dissimilarity  $d(G, H)$  between  $G$  and  $H$  is computed from the set of pairwise observation dissimilarities  $d_{ii'}$  where one member of the pair i is in G and the other i' is in  $H$ . Single linkage (SL) agglomerative clustering takes the intergroup dissimilarity to be that of the closest (least dissimilar) pair

$$
d_{SL}(G, H) = \min_{\substack{i \in G \\ i' \in H}} d_{ii'}.
$$
\n(14.41)

This is also often called the nearest-neighbor technique. Complete linkage (CL) agglomerative clustering (furthest-neighbor technique) takes the intergroup dissimilarity to be that of the furthest (most dissimilar) pair

$$
d_{CL}(G, H) = \max_{\substack{i \in G \\ i' \in H}} d_{ii'}.
$$
\n(14.42)

Group average (GA) clustering uses the average dissimilarity between the groups

$$
d_{GA}(G, H) = \frac{1}{N_G N_H} \sum_{i \in G} \sum_{i' \in H} d_{ii'} \tag{14.43}
$$

where  $N_G$  and  $N_H$  are the respective number of observations in each group. Although there have been many other proposals for defining intergroup dissimilarity in the context of agglomerative clustering, the above three are the ones most commonly used. Figure 14.13 shows examples of all three.

If the data dissimilarities  $\{d_{ii'}\}$  exhibit a strong clustering tendency, with each of the clusters being compact and well separated from others, then all three methods produce similar results. Clusters are compact if all of the

Image /page/19/Figure/1 description: The image displays three dendrograms side-by-side, illustrating different linkage methods in hierarchical clustering. The leftmost dendrogram, labeled 'Average Linkage', is colored yellow. The middle dendrogram, labeled 'Complete Linkage', is colored light blue. The rightmost dendrogram, labeled 'Single Linkage', is colored green. All three dendrograms show a hierarchical structure of clusters, with branches extending upwards and merging at different levels, indicating the process of grouping data points based on similarity.

FIGURE 14.13. Dendrograms from agglomerative hierarchical clustering of human tumor microarray data.

observations within them are relatively close together (small dissimilarities) as compared with observations in different clusters. To the extent this is not the case, results will differ.

Single linkage (14.41) only requires that a single dissimilarity  $d_{ii'}$ ,  $i \in G$ and  $i' \in H$ , be small for two groups G and H to be considered close together, irrespective of the other observation dissimilarities between the groups. It will therefore have a tendency to combine, at relatively low thresholds, observations linked by a series of close intermediate observations. This phenomenon, referred to as chaining, is often considered a defect of the method. The clusters produced by single linkage can violate the "compactness" property that all observations within each cluster tend to be similar to one another, based on the supplied observation dissimilarities  $\{d_{ii'}\}$ . If we define the *diameter*  $D_G$  of a group of observations as the largest dissimilarity among its members

$$
D_G = \max_{\substack{i \in G \\ i' \in G}} d_{ii'},\tag{14.44}
$$

then single linkage can produce clusters with very large diameters.

Complete linkage (14.42) represents the opposite extreme. Two groups G and H are considered close only if all of the observations in their union are relatively similar. It will tend to produce compact clusters with small diameters (14.44). However, it can produce clusters that violate the "closeness" property. That is, observations assigned to a cluster can be much closer to members of other clusters than they are to some members of their own cluster.

Group average clustering (14.43) represents a compromise between the two extremes of single and complete linkage. It attempts to produce relatively compact clusters that are relatively far apart. However, its results depend on the numerical scale on which the observation dissimilarities  $d_{ii'}$ are measured. Applying a monotone strictly increasing transformation  $h(\cdot)$ to the  $d_{ii'}$ ,  $h_{ii'} = h(d_{ii'})$ , can change the result produced by (14.43). In contrast, (14.41) and (14.42) depend only on the ordering of the  $d_{ii'}$  and are thus invariant to such monotone transformations. This invariance is often used as an argument in favor of single or complete linkage over group average methods.

One can argue that group average clustering has a statistical consistency property violated by single and complete linkage. Assume we have attribute-value data  $X^T = (X_1, \ldots, X_p)$  and that each cluster k is a random sample from some population joint density  $p_k(x)$ . The complete data set is a random sample from a mixture of  $K$  such densities. The group average dissimilarity  $d_{G,A}(G, H)$  (14.43) is an estimate of

$$
\int \int d(x, x') \, p_G(x) \, p_H(x') \, dx \, dx', \tag{14.45}
$$

where  $d(x, x')$  is the dissimilarity between points x and x' in the space of attribute values. As the sample size N approaches infinity  $d_{GA}(G, H)$ (14.43) approaches (14.45), which is a characteristic of the relationship between the two densities  $p_G(x)$  and  $p_H(x)$ . For single linkage,  $d_{SL}(G, H)$ (14.41) approaches zero as  $N \to \infty$  independent of  $p_G(x)$  and  $p_H(x)$ . For complete linkage,  $d_{CL}(G, H)$  (14.42) becomes infinite as  $N \to \infty$ , again independent of the two densities. Thus, it is not clear what aspects of the population distribution are being estimated by  $d_{SL}(G, H)$  and  $d_{CL}(G, H)$ .

### Example: Human Cancer Microarray Data (Continued)

The left panel of Figure 14.13 shows the dendrogram resulting from average linkage agglomerative clustering of the samples (columns) of the microarray data. The middle and right panels show the result using complete and single linkage. Average and complete linkage gave similar results, while single linkage produced unbalanced groups with long thin clusters. We focus on the average linkage clustering.

Like K-means clustering, hierarchical clustering is successful at clustering simple cancers together. However it has other nice features. By cutting off the dendrogram at various heights, different numbers of clusters emerge, and the sets of clusters are nested within one another. Secondly, it gives some partial ordering information about the samples. In Figure 14.14, we have arranged the genes (rows) and samples (columns) of the expression matrix in orderings derived from hierarchical clustering.

# 526 14. Unsupervised Learning

Note that if we flip the orientation of the branches of a dendrogram at any merge, the resulting dendrogram is still consistent with the series of hierarchical clustering operations. Hence to determine an ordering of the leaves, we must add a constraint. To produce the row ordering of Figure 14.14, we have used the default rule in S-PLUS: at each merge, the subtree with the tighter cluster is placed to the left (toward the bottom in the rotated dendrogram in the figure.) Individual genes are the tightest clusters possible, and merges involving two individual genes place them in order by their observation number. The same rule was used for the columns. Many other rules are possible—for example, ordering by a multidimensional scaling of the genes; see Section 14.8.

The two-way rearrangement of Figure 14.14 produces an informative picture of the genes and samples. This picture is more informative than the randomly ordered rows and columns of Figure 1.3 of Chapter 1. Furthermore, the dendrograms themselves are useful, as biologists can, for example, interpret the gene clusters in terms of biological processes.

### Divisive Clustering

Divisive clustering algorithms begin with the entire data set as a single cluster, and recursively divide one of the existing clusters into two daughter clusters at each iteration in a top-down fashion. This approach has not been studied nearly as extensively as agglomerative methods in the clustering literature. It has been explored somewhat in the engineering literature (Gersho and Gray, 1992) in the context of compression. In the clustering setting, a potential advantage of divisive over agglomerative methods can occur when interest is focused on partitioning the data into a relatively small number of clusters.

The divisive paradigm can be employed by recursively applying any of the combinatorial methods such as  $K$ -means (Section 14.3.6) or  $K$ -medoids (Section 14.3.10), with  $K = 2$ , to perform the splits at each iteration. However, such an approach would depend on the starting configuration specified at each step. In addition, it would not necessarily produce a splitting sequence that possesses the monotonicity property required for dendrogram representation.

A divisive algorithm that avoids these problems was proposed by Macnaughton Smith et al. (1965). It begins by placing all observations in a single cluster G. It then chooses that observation whose average dissimilarity from all the other observations is largest. This observation forms the first member of a second cluster  $H$ . At each successive step that observation in  $G$  whose average distance from those in  $H$ , minus that for the remaining observations in  $G$  is largest, is transferred to  $H$ . This continues until the corresponding difference in averages becomes negative. That is, there are no longer any observations in G that are, on average, closer to those in H. The result is a split of the original cluster into two daughter clusters,

Image /page/22/Figure/1 description: The image displays a dendrogram and a heatmap, likely representing the results of a cluster analysis. The dendrogram, a tree-like diagram, shows the hierarchical clustering of data points, with branches extending from a common root. To the right of the dendrogram is a heatmap, a graphical representation of data where values are depicted by color. The heatmap is composed of numerous small colored cells, predominantly red and green, arranged in a grid. The arrangement of these colored cells appears to correspond to the clustering shown in the dendrogram, suggesting a correlation between the hierarchical structure and the data patterns visualized in the heatmap. The title "14.3 Cluster Analysis" is visible at the top of the image.

FIGURE 14.14. DNA microarray data: average linkage hierarchical clustering has been applied independently to the rows (genes) and columns (samples), determining the ordering of the rows and columns (see text). The colors range from bright green (negative, under-expressed) to bright red (positive, over-expressed).

### 528 14. Unsupervised Learning

the observations transferred to  $H$ , and those remaining in  $G$ . These two clusters represent the second level of the hierarchy. Each successive level is produced by applying this splitting procedure to one of the clusters at the previous level. Kaufman and Rousseeuw (1990) suggest choosing the cluster at each level with the largest diameter (14.44) for splitting. An alternative would be to choose the one with the largest average dissimilarity among its members

$$
\bar{d}_G = \frac{1}{N_G} \sum_{i \in G} \sum_{i' \in G} d_{ii'}.
$$

The recursive splitting continues until all clusters either become singletons or all members of each one have zero dissimilarity from one another.

# 14.4 Self-Organizing Maps

This method can be viewed as a constrained version of K-means clustering, in which the prototypes are encouraged to lie in a one- or two-dimensional manifold in the feature space. The resulting manifold is also referred to as a constrained topological map, since the original high-dimensional observations can be mapped down onto the two-dimensional coordinate system. The original SOM algorithm was online—observations are processed one at a time—and later a batch version was proposed. The technique also bears a close relationship to principal curves and surfaces, which are discussed in the next section.

We consider a SOM with a two-dimensional rectangular grid of K prototypes  $m_j \in \mathbb{R}^p$  (other choices, such as hexagonal grids, can also be used). Each of the  $K$  prototypes are parametrized with respect to an integer coordinate pair  $\ell_j \in \mathcal{Q}_1 \times \mathcal{Q}_2$ . Here  $\mathcal{Q}_1 = \{1, 2, \ldots, q_1\}$ , similarly  $\mathcal{Q}_2$ , and  $K = q_1 \cdot q_2$ . The  $m_i$  are initialized, for example, to lie in the two-dimensional principal component plane of the data (next section). We can think of the prototypes as "buttons," "sewn" on the principal component plane in a regular pattern. The SOM procedure tries to bend the plane so that the buttons approximate the data points as well as possible. Once the model is fit, the observations can be mapped down onto the two-dimensional grid.

The observations  $x_i$  are processed one at a time. We find the closest prototype  $m_j$  to  $x_i$  in Euclidean distance in  $\mathbb{R}^p$ , and then for all neighbors  $m_k$  of  $m_j$ , move  $m_k$  toward  $x_i$  via the update

$$
m_k \leftarrow m_k + \alpha (x_i - m_k). \tag{14.46}
$$

The "neighbors" of  $m_i$  are defined to be all  $m_k$  such that the distance between  $\ell_j$  and  $\ell_k$  is small. The simplest approach uses Euclidean distance, and "small" is determined by a threshold  $r$ . This neighborhood always includes the closest prototype  $m_i$  itself.

Notice that distance is defined in the space  $\mathcal{Q}_1 \times \mathcal{Q}_2$  of integer topological coordinates of the prototypes, rather than in the feature space  $\mathbb{R}^p$ . The effect of the update (14.46) is to move the prototypes closer to the data, but also to maintain a smooth two-dimensional spatial relationship between the prototypes.

The performance of the SOM algorithm depends on the learning rate  $\alpha$  and the distance threshold r. Typically  $\alpha$  is decreased from say 1.0 to 0.0 over a few thousand iterations (one per observation). Similarly  $r$  is decreased linearly from starting value  $R$  to 1 over a few thousand iterations. We illustrate a method for choosing  $R$  in the example below.

We have described the simplest version of the SOM. More sophisticated versions modify the update step according to distance:

$$
m_k \leftarrow m_k + \alpha h(||\ell_j - \ell_k||)(x_i - m_k), \qquad (14.47)
$$

where the *neighborhood function* h gives more weight to prototypes  $m_k$  with indices  $\ell_k$  closer to  $\ell_j$  than to those further away.

If we take the distance  $r$  small enough so that each neighborhood contains only one point, then the spatial connection between prototypes is lost. In that case one can show that the SOM algorithm is an online version of K-means clustering, and eventually stabilizes at one of the local minima found by  $K$ -means. Since the SOM is a constrained version of  $K$ -means clustering, it is important to check whether the constraint is reasonable in any given problem. One can do this by computing the reconstruction error  $||x - m_j||^2$ , summed over observations, for both methods. This will necessarily be smaller for  $K$ -means, but should not be much smaller if the SOM is a reasonable approximation.

As an illustrative example, we generated 90 data points in three dimensions, near the surface of a half sphere of radius 1. The points were in each of three clusters—red, green, and blue—located near  $(0, 1, 0)$ ,  $(0, 0, 1)$  and  $(1, 0, 0)$ . The data are shown in Figure 14.15

By design, the red cluster was much tighter than the green or blue ones. (Full details of the data generation are given in Exercise 14.5.) A  $5 \times 5$  grid of prototypes was used, with initial grid size  $R = 2$ ; this meant that about a third of the prototypes were initially in each neighborhood. We did a total of 40 passes through the dataset of 90 observations, and let r and  $\alpha$ decrease linearly over the 3600 iterations.

In Figure 14.16 the prototypes are indicated by circles, and the points that project to each prototype are plotted randomly within the corresponding circle. The left panel shows the initial configuration, while the right panel shows the final one. The algorithm has succeeded in separating the clusters; however, the separation of the red cluster indicates that the manifold has folded back on itself (see Figure 14.17). Since the distances in the two-dimensional display are not used, there is little indication in the SOM projection that the red cluster is tighter than the others.

Image /page/25/Figure/1 description: A 3D scatter plot shows three clusters of data points, colored blue, red, and green, distributed around a wireframe dome. The dome is colored yellow at its apex and transitions to green towards its base. The x, y, and z axes are labeled with values ranging from -1 to 1.5. The blue points are clustered on the left side of the dome, the red points are clustered at the top of the dome, and the green points are clustered on the right side of the dome.

FIGURE 14.15. Simulated data in three classes, near the surface of a half– sphere.

Image /page/25/Figure/3 description: The image displays two 5x5 grids, each containing several circles. Within some of the circles, there are clusters of colored dots: red, blue, and green. The left grid shows circles with dots in the following positions (row, column): (1,2) red, (1,3) red, (1,4) red, (2,1) blue, (2,2) blue, (2,3) blue, (2,4) green, (3,1) blue, (3,2) blue, (3,3) green, (3,4) green, (4,2) red, (4,3) green, (4,4) green, (5,2) blue, (5,3) blue, (5,4) green. The right grid shows circles with dots in the following positions: (1,1) red, (1,2) red, (1,3) red, (1,4) green, (1,5) green, (2,1) blue, (2,2) blue, (2,3) green, (2,4) green, (2,5) green, (3,1) blue, (3,2) blue, (3,3) green, (3,4) green, (3,5) green, (4,1) blue, (4,2) blue, (4,3) green, (4,4) green, (4,5) green, (5,1) blue, (5,2) blue, (5,3) blue, (5,4) red, (5,5) red. The axes are labeled from 1 to 5 for both rows and columns.

FIGURE 14.16. Self-organizing map applied to half-sphere data example. Left panel is the initial configuration, right panel the final one. The  $5 \times 5$  grid of prototypes are indicated by circles, and the points that project to each prototype are plotted randomly within the corresponding circle.

Image /page/26/Figure/1 description: A scatter plot displays a network of points connected by lines. The points are colored red, green, and purple, and are clustered in a roughly arched shape. The red points are concentrated at the top center of the arch, while the purple and green points are distributed along the lower curve of the arch. Several purple points are also scattered within the arch, connecting to other points.

**FIGURE 14.17.** Wiremesh representation of the fitted SOM model in  $\mathbb{R}^3$ . The lines represent the horizontal and vertical edges of the topological lattice. The double lines indicate that the surface was folded diagonally back on itself in order to model the red points. The cluster members have been jittered to indicate their color, and the purple points are the node centers.

Figure 14.18 shows the reconstruction error, equal to the total sum of squares of each data point around its prototype. For comparison we carried out a K-means clustering with 25 centroids, and indicate its reconstruction error by the horizontal line on the graph. We see that the SOM significantly decreases the error, nearly to the level of the K-means solution. This provides evidence that the two-dimensional constraint used by the SOM is reasonable for this particular dataset.

In the batch version of the SOM, we update each  $m_i$  via

$$
m_j = \frac{\sum w_k x_k}{\sum w_k}.
$$
\n(14.48)

The sum is over points  $x_k$  that mapped (i.e., were closest to) neighbors  $m_k$ of  $m_j$ . The weight function may be rectangular, that is, equal to 1 for the neighbors of  $m_k$ , or may decrease smoothly with distance  $\|\ell_k-\ell_j\|$  as before. If the neighborhood size is chosen small enough so that it consists only of  $m_k$ , with rectangular weights, this reduces to the K-means clustering procedure described earlier. It can also be thought of as a discrete version of principal curves and surfaces, described in Section 14.5.

Image /page/27/Figure/1 description: The image is a line graph showing the reconstruction error over iterations. The x-axis is labeled "Iteration" and ranges from 0 to 2500. The y-axis is labeled "Reconstruction Error" and ranges from 0 to 50. The line starts at approximately 37 at iteration 0, peaks at about 54 at iteration 100, then drops sharply to around 22 at iteration 200. It fluctuates between 20 and 30 until around iteration 400, after which it generally decreases, with some fluctuations, to a plateau around 8-9 from iteration 1500 onwards. A horizontal green line is present at y=0, indicating the baseline or zero error level.

FIGURE 14.18. Half-sphere data: reconstruction error for the SOM as a function of iteration. Error for k-means clustering is indicated by the horizontal line.

### Example: Document Organization and Retrieval

Document retrieval has gained importance with the rapid development of the Internet and the Web, and SOMs have proved to be useful for organizing and indexing large corpora. This example is taken from the WEBSOM homepage http://websom.hut.fi/ (Kohonen et al., 2000). Figure 14.19 represents a SOM fit to 12,088 newsgroup comp.ai.neural-nets articles. The labels are generated automatically by the WEBSOM software and provide a guide as to the typical content of a node.

In applications such as this, the documents have to be reprocessed in order to create a feature vector. A term-document matrix is created, where each row represents a single document. The entries in each row are the relative frequency of each of a predefined set of terms. These terms could be a large set of dictionary entries (50,000 words), or an even larger set of bigrams (word pairs), or subsets of these. These matrices are typically very sparse, and so often some preprocessing is done to reduce the number of features (columns). Sometimes the SVD (next section) is used to reduce the matrix; Kohonen et al. (2000) use a randomized variant thereof. These reduced vectors are then the input to the SOM.

Image /page/28/Figure/1 description: This is a heatmap visualization of a self-organizing map (SOM) model. The heatmap displays various words arranged in a grid-like structure, with color intensity indicating the density or importance of each word within the model. Words such as 'interpreted', 'universities', 'workshop', 'warren', 'costa', 'postdoctoral', 'aisb', 'neuron', 'phoneme', 'signal', 'engines', 'genesis', 'connect', 'programmer', 'bootstrap', 'extrapolation', 'curves', 'x', 'atree', 'weightless', 'pdp', 'neurocomputing', 'paradigm', 'elman', 'personnel', 'judgement', 'java', 'ai', 'principle', 'conjugate', 'ga', 'intelligence', 'trading', 'fortran', 'brain', 'alamos', 'toolbox', 'consciousness', 'rbf', 'levensberg-marquardt', 'neurotransmitters', 'backpropagator's', 'robot', 'signals', 'scheduling', 'papers', 'snns', 'noise', 'variable', 'tools', 'mining', 'tdl', 'decay', 'encoding', 'bayes', 'unsupervised', 'hidden', 'neurofuzzy', 'benchmark', 'popular', 'sigmoid', 'validation', and 'rate' are visible. The color gradient ranges from yellow (indicating higher density/importance) to dark red (indicating lower density/importance).

FIGURE 14.19. Heatmap representation of the SOM model fit to a corpus of 12,088 newsgroup comp.ai.neural-nets contributions (courtesy WEBSOM homepage). The lighter areas indicate higher-density areas. Populated nodes are automatically labeled according to typical content.

Image /page/29/Figure/1 description: This image depicts a scatter plot of data points, represented by red dots, arranged along a diagonal line. A black line, labeled with a vector "v1" at its upper right end, represents a principal component or direction of maximum variance. Dotted lines extend from each red data point perpendicularly to the black line, illustrating the projection of each point onto this principal component. One such projection is highlighted with a cyan segment and labeled "ui1d1", indicating the component of the data point "xi" along the direction "v1".

FIGURE 14.20. The first linear principal component of a set of data. The line minimizes the total squared distance from each point to its orthogonal projection onto the line.

In this application the authors have developed a "zoom" feature, which allows one to interact with the map in order to get more detail. The final level of zooming retrieves the actual news articles, which can then be read.

# 14.5 Principal Components, Curves and Surfaces

Principal components are discussed in Sections 3.4.1, where they shed light on the shrinkage mechanism of ridge regression. Principal components are a sequence of projections of the data, mutually uncorrelated and ordered in variance. In the next section we present principal components as linear manifolds approximating a set of N points  $x_i \in \mathbb{R}^p$ . We then present some nonlinear generalizations in Section 14.5.2. Other recent proposals for nonlinear approximating manifolds are discussed in Section 14.9.

# 14.5.1 Principal Components

The principal components of a set of data in  $\mathbb{R}^p$  provide a sequence of best linear approximations to that data, of all ranks  $q \leq p$ .

Denote the observations by  $x_1, x_2, \ldots, x_N$ , and consider the rank-q linear model for representing them

14.5 Principal Components, Curves and Surfaces 535

$$
f(\lambda) = \mu + \mathbf{V}_q \lambda,\tag{14.49}
$$

where  $\mu$  is a location vector in  $\mathbb{R}^p$ ,  $\mathbf{V}_q$  is a  $p \times q$  matrix with q orthogonal unit vectors as columns, and  $\lambda$  is a q vector of parameters. This is the parametric representation of an affine hyperplane of rank q. Figures 14.20 and 14.21 illustrate for  $q = 1$  and  $q = 2$ , respectively. Fitting such a model to the data by least squares amounts to minimizing the reconstruction error

$$
\min_{\mu, \{\lambda_i\}, \mathbf{V}_q} \sum_{i=1}^N \|x_i - \mu - \mathbf{V}_q \lambda_i\|^2.
$$
 (14.50)

We can partially optimize for  $\mu$  and the  $\lambda_i$  (Exercise 14.7) to obtain

$$
\hat{\mu} = \bar{x}, \qquad (14.51)
$$

$$
\hat{\lambda}_i = \mathbf{V}_q^T (x_i - \bar{x}). \tag{14.52}
$$

This leaves us to find the orthogonal matrix  $V_q$ :

$$
\min_{\mathbf{V}_q} \sum_{i=1}^N ||(x_i - \bar{x}) - \mathbf{V}_q \mathbf{V}_q^T (x_i - \bar{x})||^2.
$$
\n(14.53)

For convenience we assume that  $\bar{x} = 0$  (otherwise we simply replace the observations by their centered versions  $\tilde{x}_i = x_i - \bar{x}$ . The  $p \times p$  matrix  $\mathbf{H}_q = \mathbf{V}_q \mathbf{V}_q^T$  is a projection matrix, and maps each point  $x_i$  onto its rankq reconstruction  $\mathbf{H}_q x_i$ , the orthogonal projection of  $x_i$  onto the subspace spanned by the columns of  $V_q$ . The solution can be expressed as follows. Stack the (centered) observations into the rows of an  $N \times p$  matrix **X**. We construct the *singular value decomposition* of  $X$ :

$$
\mathbf{X} = \mathbf{U} \mathbf{D} \mathbf{V}^T. \tag{14.54}
$$

This is a standard decomposition in numerical analysis, and many algorithms exist for its computation (Golub and Van Loan, 1983, for example). Here **U** is an  $N \times p$  orthogonal matrix  $(\mathbf{U}^T \mathbf{U} = \mathbf{I}_p)$  whose columns  $\mathbf{u}_i$  are called the *left singular vectors*; **V** is a  $p \times p$  orthogonal matrix  $(\mathbf{V}^T \mathbf{V} = \mathbf{I}_p)$ with columns  $v_i$  called the *right singular vectors*, and **D** is a  $p \times p$  diagonal matrix, with diagonal elements  $d_1 \geq d_2 \geq \cdots \geq d_p \geq 0$  known as the singular values. For each rank q, the solution  $V_q$  to (14.53) consists of the first  $q$  columns of  $V$ . The columns of  $UD$  are called the principal components of **X** (see Section 3.5.1). The N optimal  $\hat{\lambda}_i$  in (14.52) are given by the first q principal components (the N rows of the  $N \times q$  matrix  $U_q D_q$ ).

The one-dimensional principal component line in  $\mathbb{R}^2$  is illustrated in Figure 14.20. For each data point  $x_i$ , there is a closest point on the line, given by  $u_{i1}d_1v_1$ . Here  $v_1$  is the direction of the line and  $\hat{\lambda}_i = u_{i1}d_1$  measures distance along the line from the origin. Similarly Figure 14.21 shows the

Image /page/31/Figure/1 description: The image displays two plots. The plot on the left is a 3D representation showing three clusters of points (blue, red, and green) projected onto a plane. Lines extend from each point to the plane, indicating the projection. The plot on the right is a 2D scatter plot showing the same three clusters of points, colored blue, red, and green. The x-axis is labeled "First principal component" and ranges from -1.0 to 1.0. The y-axis is labeled "Second principal component" and ranges from -1.0 to 1.0. The blue points are clustered in the upper left, the red points are in the center, and the green points are in the lower right.

FIGURE 14.21. The best rank-two linear approximation to the half-sphere data. The right panel shows the projected points with coordinates given by  $U_2D_2$ , the first two principal components of the data.

two-dimensional principal component surface fit to the half-sphere data (left panel). The right panel shows the projection of the data onto the first two principal components. This projection was the basis for the initial configuration for the SOM method shown earlier. The procedure is quite successful at separating the clusters. Since the half-sphere is nonlinear, a nonlinear projection will do a better job, and this is the topic of the next section.

Principal components have many other nice properties, for example, the linear combination  $\mathbf{X}v_1$  has the highest variance among all linear combinations of the features;  $\mathbf{X}v_2$  has the highest variance among all linear combinations satisfying  $v_2$  orthogonal to  $v_1$ , and so on.

### Example: Handwritten Digits

Principal components are a useful tool for dimension reduction and compression. We illustrate this feature on the handwritten digits data described in Chapter 1. Figure 14.22 shows a sample of 130 handwritten 3's, each a digitized  $16 \times 16$  grayscale image, from a total of 658 such 3's. We see considerable variation in writing styles, character thickness and orientation. We consider these images as points  $x_i$  in  $\mathbb{R}^{256}$ , and compute their principal components via the SVD (14.54).

Figure 14.23 shows the first two principal components of these data. For each of these first two principal components  $u_{i1}$  and  $u_{i2}$ , we computed the 5%, 25%, 50%, 75% and 95% quantile points, and used them to define the rectangular grid superimposed on the plot. The circled points indicate

Image /page/32/Picture/1 description: A grid of 80 handwritten digits, all of which are the number 3. The digits are arranged in 8 rows and 10 columns, with green lines separating each digit. Some of the digits are slightly different from others, showing variations in handwriting style.

FIGURE 14.22. A sample of 130 handwritten 3's shows a variety of writing styles.

those images close to the vertices of the grid, where the distance measure focuses mainly on these projected coordinates, but gives some weight to the components in the orthogonal subspace. The right plot shows the images corresponding to these circled points. This allows us to visualize the nature of the first two principal components. We see that the  $v_1$  (horizontal movement) mainly accounts for the lengthening of the lower tail of the three, while  $v_2$  (vertical movement) accounts for character thickness. In terms of the parametrized model (14.49), this two-component model has the form

$$
\hat{f}(\lambda) = \bar{x} + \lambda_1 v_1 + \lambda_2 v_2
$$
\n
$$
= \begin{bmatrix} - \\ + \lambda_1 \end{bmatrix} + \lambda_2 \cdot \begin{bmatrix} - \\ - \end{bmatrix} + \lambda_3 \cdot (14.55)
$$

Here we have displayed the first two principal component directions,  $v_1$ and  $v_2$ , as images. Although there are a possible 256 principal components, approximately 50 account for 90% of the variation in the threes, 12 account for 63%. Figure 14.24 compares the singular values to those obtained for equivalent uncorrelated data, obtained by randomly scrambling each column of X. The pixels in a digitized image are inherently correlated, and since these are all the same digit the correlations are even stronger.

Image /page/33/Figure/1 description: The image displays a scatter plot on the left and a grid of handwritten digits on the right. The scatter plot has the x-axis labeled "First Principal Component" ranging from -6 to 8, and the y-axis labeled "Second Principal Component" ranging from -5 to 5. Numerous green dots are scattered across the plot, with some of them circled in red. The grid on the right contains 20 images of the handwritten digit "3", arranged in 4 rows and 5 columns. The digits vary slightly in their appearance and clarity.

FIGURE 14.23. (Left panel:) the first two principal components of the handwritten threes. The circled points are the closest projected images to the vertices of a grid, defined by the marginal quantiles of the principal components. (Right panel:) The images corresponding to the circled points. These show the nature of the first two principal components.

Image /page/33/Figure/3 description: This is a scatter plot comparing the singular values of a real trace and a randomized trace against the dimension. The x-axis represents the dimension, ranging from 0 to 250. The y-axis represents the singular values, ranging from 0 to 80. The plot shows two sets of data points: 'Real Trace' in blue and 'Randomized Trace' in orange. The 'Real Trace' starts with a high singular value of approximately 85 at dimension 0 and decreases sharply as the dimension increases, reaching a value close to 0 at dimension 250. The 'Randomized Trace' starts with a singular value of approximately 28 at dimension 0 and shows a more gradual decrease as the dimension increases, reaching a value of approximately 5 at dimension 250. The legend indicates that the blue dots represent the 'Real Trace' and the orange dots represent the 'Randomized Trace'.

FIGURE 14.24. The 256 singular values for the digitized threes, compared to those for a randomized version of the data (each column of  $X$  was scrambled).

A relatively small subset of the principal components serve as excellent lower-dimensional features for representing the high-dimensional data.

### Example: Procrustes Transformations and Shape Averaging

Image /page/34/Figure/3 description: The image displays two side-by-side plots, each containing two sets of data points. The left plot shows a green curve that starts at the top left, moves horizontally to the right, then curves downwards and to the left, forming a loop. A second orange curve starts from the top center, curves downwards and to the right, forming a loop, and then moves upwards and to the right. The right plot shows a similar arrangement, with a green curve starting from the top left, moving diagonally down and to the right, then curving upwards and to the right. An orange curve starts from the top center, curves downwards and to the left, forming a loop, and then moves upwards and to the right, crossing over the green curve.

FIGURE 14.25. (Left panel:) Two different digitized handwritten Ss, each represented by 96 corresponding points in  $\mathbb{R}^2$ . The green S has been deliberately rotated and translated for visual effect. (Right panel:) A Procrustes transformation applies a translation and rotation to best match up the two set of points.

Figure 14.25 represents two sets of points, the orange and green, in the same plot. In this instance these points represent two digitized versions of a handwritten S, extracted from the signature of a subject "Suresh." Figure 14.26 shows the entire signatures from which these were extracted (third and fourth panels). The signatures are recorded dynamically using touch-screen devices, familiar sights in modern supermarkets. There are  $N = 96$  points representing each S, which we denote by the  $N \times 2$  matrices  $\mathbf{X}_1$  and  $\mathbf{X}_2$ . There is a correspondence between the points—the *i*th rows of  $X_1$  and  $X_2$  are meant to represent the same positions along the two S's. In the language of morphometrics, these points represent landmarks on the two objects. How one finds such corresponding landmarks is in general difficult and subject specific. In this particular case we used *dynamic time* warping of the speed signal along each signature (Hastie et al., 1992), but will not go into details here.

In the right panel we have applied a translation and rotation to the green points so as best to match the orange—a so-called *Procrustes*<sup>3</sup> transformation (Mardia et al., 1979, for example).

Consider the problem

$$
\min_{\mu,\mathbf{R}} ||\mathbf{X}_2 - (\mathbf{X}_1 \mathbf{R} + \mathbf{1} \mu^T)||_F, \tag{14.56}
$$

<sup>3</sup>Procrustes was an African bandit in Greek mythology, who stretched or squashed his visitors to fit his iron bed (eventually killing them).

# 540 14. Unsupervised Learning

with  $X_1$  and  $X_2$  both  $N \times p$  matrices of corresponding points, **R** an orthonormal  $p \times p$  matrix<sup>4</sup>, and  $\mu$  a *p*-vector of location coordinates. Here  $||\mathbf{X}||_F^2 = \text{trace}(\mathbf{X}^T \mathbf{X})$  is the squared *Frobenius* matrix norm.

Let  $\bar{x}_1$  and  $\bar{x}_2$  be the column mean vectors of the matrices, and  $\tilde{X}_1$  and  $\tilde{\mathbf{X}}_2$  be the versions of these matrices with the means removed. Consider the SVD  $\tilde{\mathbf{X}}_1^T \tilde{\mathbf{X}}_2 = \mathbf{UDV}^T$ . Then the solution to (14.56) is given by (Exercise 14.8)

$$
\hat{\mathbf{R}} = \mathbf{U}\mathbf{V}^T \n\hat{\mu} = \bar{x}_2 - \hat{\mathbf{R}}\bar{x}_1,
$$
\n(14.57)

and the minimal distances is referred to as the Procrustes distance. From the form of the solution, we can center each matrix at its column centroid, and then ignore location completely. Hereafter we assume this is the case.

The Procrustes distance with scaling solves a slightly more general problem,

$$
\min_{\beta, \mathbf{R}} ||\mathbf{X}_2 - \beta \mathbf{X}_1 \mathbf{R}||_F, \tag{14.58}
$$

where  $\beta > 0$  is a positive scalar. The solution for **R** is as before, with  $\hat{\beta} = \text{trace}(D) / ||\mathbf{X}_1||_F^2.$ 

Related to Procrustes distance is the Procrustes average of a collection of L shapes, which solves the problem

$$
\min_{\{\mathbf{R}_{\ell}\}_{1}^{L}, \mathbf{M}} \sum_{\ell=1}^{L} ||\mathbf{X}_{\ell} \mathbf{R}_{\ell} - \mathbf{M}||_{F}^{2};
$$
\n(14.59)

that is, find the shape M closest in average squared Procrustes distance to all the shapes. This is solved by a simple alternating algorithm:

- 0. Initialize  $\mathbf{M} = \mathbf{X}_1$  (for example).
- 1. Solve the L Procrustes rotation problems with M fixed, yielding  $\mathbf{X}_\ell' \leftarrow \mathbf{X}\hat{\mathbf{R}}_\ell.$
- 2. Let  $\mathbf{M} \leftarrow \frac{1}{L} \sum_{\ell=1}^{L} \mathbf{X}'_{\ell}$ .

Steps 1. and 2. are repeated until the criterion (14.59) converges.

Figure 14.26 shows a simple example with three shapes. Note that we can only expect a solution up to a rotation; alternatively, we can impose a constraint, such as that M be upper-triangular, to force uniqueness. One can easily incorporate scaling in the definition (14.59); see Exercise 14.9.

Most generally we can define the affine-invariant average of a set of shapes via

<sup>4</sup>To simplify matters, we consider only orthogonal matrices which include reflections as well as rotations [the  $O(p)$  group]; although reflections are unlikely here, these methods can be restricted further to allow only rotations  $[SO(p)]$  group].

Image /page/36/Figure/1 description: The image displays four panels, each containing a signature and a stylized representation of a letter 'S'. The signature, written in blue cursive, appears to be the same in all panels. The 'S' is depicted in red, with a gray dotted line tracing its path. The gray dotted line shows slight variations in the execution of the 'S' across the panels, suggesting different attempts or variations in writing the letter. The first panel shows a more complex, intertwined path for the 'S', while the subsequent panels show a smoother, more conventional 'S' shape.

**FIGURE 14.26.** The Procrustes average of three versions of the leading  $S$  in Suresh's signatures. The left panel shows the preshape average, with each of the shapes  $\mathbf{X}'_l$  in preshape space superimposed. The right three panels map the preshape M separately to match each of the original S's.

$$
\min_{\{\mathbf{A}_{\ell}\}_{\ell}^{L}, \mathbf{M}} \sum_{\ell=1}^{L} ||\mathbf{X}_{\ell} \mathbf{A}_{\ell} - \mathbf{M}||_{F}^{2},
$$
\n(14.60)

where the  $A_\ell$  are any  $p \times p$  nonsingular matrices. Here we require a standardization, such as  $M^T M = I$ , to avoid a trivial solution. The solution is attractive, and can be computed without iteration (Exercise 14.10):

- 1. Let  $\mathbf{H}_{\ell} = \mathbf{X}_{\ell} (\mathbf{X}_{\ell}^T \mathbf{X}_{\ell})^{-1} \mathbf{X}_{\ell}^T$  be the rank-p projection matrix defined by  $\mathbf{X}_{\ell}$ .
- 2. M is the  $N\times p$  matrix formed from the p largest eigenvectors of  $\bar{H}$  =  $\frac{1}{L}\sum_{\ell=1}^L \mathbf{H}_\ell.$

# 14.5.2 Principal Curves and Surfaces

Principal curves generalize the principal component line, providing a smooth one-dimensional curved approximation to a set of data points in  $\mathbb{R}^p$ . A principal surface is more general, providing a curved manifold approximation of dimension 2 or more.

We will first define principal curves for random variables  $X \in \mathbb{R}^p$ , and then move to the finite data case. Let  $f(\lambda)$  be a parameterized smooth curve in  $\mathbb{R}^p$ . Hence  $f(\lambda)$  is a vector function with p coordinates, each a smooth function of the single parameter  $\lambda$ . The parameter  $\lambda$  can be chosen, for example, to be arc-length along the curve from some fixed origin. For each data value x, let  $\lambda_f(x)$  define the closest point on the curve to x. Then  $f(\lambda)$  is called a principal curve for the distribution of the random vector  $X$  if

$$
f(\lambda) = \mathcal{E}(X|\lambda_f(X) = \lambda).
$$
 (14.61)

This says  $f(\lambda)$  is the average of all data points that project to it, that is, the points for which it is "responsible." This is also known as a self-consistency property. Although in practice, continuous multivariate distributes have infinitely many principal curves (Duchamp and Stuetzle, 1996), we are

Image /page/37/Figure/1 description: The image displays a scatter plot with a curved line representing a function f(lambda) = [f1(lambda), f2(lambda)]. Red dots are scattered around the curve, with dashed lines connecting some of the dots to the curve, indicating deviations or errors. A single green dot is highlighted on the curve, with two light blue lines perpendicular to the curve passing through it, suggesting a local analysis or tangent approximation. The curve itself is smooth and appears to be a representation of a parameterized function.

FIGURE 14.27. The principal curve of a set of data. Each point on the curve is the average of all data points that project there.

interested mainly in the smooth ones. A principal curve is illustrated in Figure 14.27.

Principal points are an interesting related concept. Consider a set of k prototypes and for each point  $x$  in the support of a distribution, identify the closest prototype, that is, the prototype that is responsible for it. This induces a partition of the feature space into so-called Voronoi regions. The set of  $k$  points that minimize the expected distance from  $X$  to its prototype are called the principal points of the distribution. Each principal point is self-consistent, in that it equals the mean of  $X$  in its Voronoi region. For example, with  $k = 1$ , the principal point of a circular normal distribution is the mean vector; with  $k = 2$  they are a pair of points symmetrically placed on a ray through the mean vector. Principal points are the distributional analogs of centroids found by K-means clustering. Principal curves can be viewed as  $k = \infty$  principal points, but constrained to lie on a smooth curve, in a similar way that a SOM constrains K-means cluster centers to fall on a smooth manifold.

To find a principal curve  $f(\lambda)$  of a distribution, we consider its coordinate functions  $f(\lambda) = [f_1(\lambda), f_2(\lambda), \dots, f_p(\lambda)]$  and let  $X^T = (X_1, X_2, \dots, X_p)$ . Consider the following alternating steps:

(a) 
$$
\hat{f}_j(\lambda) \leftarrow \mathbf{E}(X_j|\lambda(X) = \lambda); j = 1, 2, ..., p,
$$
  
\n(b)  $\hat{\lambda}_f(x) \leftarrow \operatorname{argmin}_{\lambda'} ||x - \hat{f}(\lambda')||^2.$  (14.62)

The first equation fixes  $\lambda$  and enforces the self-consistency requirement (14.61). The second equation fixes the curve and finds the closest point on

Image /page/38/Figure/1 description: The image displays two plots side-by-side. The left plot is a 3D representation of a curved surface with a grid overlay. Several colored points (red, blue, and green) are scattered on and around this surface, with thin black lines extending vertically from some of the points to the surface. The right plot is a 2D scatter plot with the x-axis labeled "λ1" and the y-axis labeled "λ2". This plot also shows three distinct clusters of colored points: red points are clustered around (-0.1, -0.05), blue points are clustered around (0.0, 0.15), and green points are scattered between (0.0, -0.1) and (0.2, 0.15). The axes in the right plot range from -0.2 to 0.2 for λ1 and -0.2 to 0.2 for λ2.

FIGURE 14.28. Principal surface fit to half-sphere data. (Left panel:) fitted two-dimensional surface. (Right panel:) projections of data points onto the surface, resulting in coordinates  $\lambda_1, \lambda_2$ .

the curve to each data point. With finite data, the principal curve algorithm starts with the linear principal component, and iterates the two steps in (14.62) until convergence. A scatterplot smoother is used to estimate the conditional expectations in step (a) by smoothing each  $X_i$  as a function of the arc-length  $\lambda(X)$ , and the projection in (b) is done for each of the observed data points. Proving convergence in general is difficult, but one can show that if a linear least squares fit is used for the scatterplot smoothing, then the procedure converges to the first linear principal component, and is equivalent to the power method for finding the largest eigenvector of a matrix.

Principal surfaces have exactly the same form as principal curves, but are of higher dimension. The mostly commonly used is the two-dimensional principal surface, with coordinate functions

$$
f(\lambda_1, \lambda_2) = [f_1(\lambda_1, \lambda_2), \dots, f_p(\lambda_1, \lambda_2)].
$$

The estimates in step (a) above are obtained from two-dimensional surface smoothers. Principal surfaces of dimension greater than two are rarely used, since the visualization aspect is less attractive, as is smoothing in high dimensions.

Figure 14.28 shows the result of a principal surface fit to the half-sphere data. Plotted are the data points as a function of the estimated nonlinear coordinates  $\hat{\lambda}_1(x_i), \hat{\lambda}_2(x_i)$ . The class separation is evident.

Principal surfaces are very similar to self-organizing maps. If we use a kernel surface smoother to estimate each coordinate function  $f_i(\lambda_1, \lambda_2)$ , this has the same form as the batch version of SOMs (14.48). The SOM weights  $w_k$  are just the weights in the kernel. There is a difference, however:

### 544 14. Unsupervised Learning

the principal surface estimates a separate prototype  $f(\lambda_1(x_i), \lambda_2(x_i))$  for each data point  $x_i$ , while the SOM shares a smaller number of prototypes for all data points. As a result, the SOM and principal surface will agree only as the number of SOM prototypes grows very large.

There also is a conceptual difference between the two. Principal surfaces provide a smooth parameterization of the entire manifold in terms of its coordinate functions, while SOMs are discrete and produce only the estimated prototypes for approximating the data. The smooth parameterization in principal surfaces preserves distance locally: in Figure 14.28 it reveals that the red cluster is tighter than the green or blue clusters. In simple examples the estimates coordinate functions themselves can be informative: see Exercise 14.13.

# 14.5.3 Spectral Clustering

Traditional clustering methods like K-means use a spherical or elliptical metric to group data points. Hence they will not work well when the clusters are non-convex, such as the concentric circles in the top left panel of Figure 14.29. Spectral clustering is a generalization of standard clustering methods, and is designed for these situations. It has close connections with the local multidimensional-scaling techniques (Section 14.9) that generalize MDS.

The starting point is a  $N \times N$  matrix of pairwise similarities  $s_{ii'} \geq 0$  between all observation pairs. We represent the observations in an undirected similarity graph  $G = \langle V, E \rangle$ . The N vertices  $v_i$  represent the observations, and pairs of vertices are connected by an edge if their similarity is positive (or exceeds some threshold). The edges are weighted by the  $s_{ii'}$ . Clustering is now rephrased as a graph-partition problem, where we identify connected components with clusters. We wish to partition the graph, such that edges between different groups have low weight, and within a group have high weight. The idea in spectral clustering is to construct similarity graphs that represent the local neighborhood relationships between observations.

To make things more concrete, consider a set of N points  $x_i \in \mathbb{R}^p$ , and let  $d_{ii'}$  be the Euclidean distance between  $x_i$  and  $x_{i'}$ . We will use as similarity matrix the radial-kernel gram matrix; that is,  $s_{ii'} = \exp(-d_{ii'}^2/c)$ , where  $c > 0$  is a scale parameter.

There are many ways to define a similarity matrix and its associated similarity graph that reflect local behavior. The most popular is the *mutual* K-nearest-neighbor graph. Define  $\mathcal{N}_K$  to be the symmetric set of nearby pairs of points; specifically a pair  $(i, i')$  is in  $\mathcal{N}_K$  if point i is among the  $K$ -nearest neighbors of  $i'$ , or vice-versa. Then we connect all symmetric nearest neighbors, and give them edge weight  $w_{ii'} = s_{ii'}$ ; otherwise the edge weight is zero. Equivalently we set to zero all the pairwise similarities not in  $\mathcal{N}_K$ , and draw the graph for this modified similarity matrix.

Alternatively, a fully connected graph includes all pairwise edges with weights  $w_{ii'} = s_{ii'}$ , and the local behavior is controlled by the scale parameter c.

The matrix of edge weights  $\mathbf{W} = \{w_{ii'}\}$  from a similarity graph is called the *adjacency matrix*. The *degree* of vertex *i* is  $g_i = \sum_{i'} w_{ii'}$ , the sum of the weights of the edges connected to it. Let G be a diagonal matrix with diagonal elements  $g_i$ .

Finally, the graph Laplacian is defined by

$$
\mathbf{L} = \mathbf{G} - \mathbf{W} \tag{14.63}
$$

This is called the unnormalized graph Laplacian; a number of normalized versions have been proposed—these standardize the Laplacian with respect to the node degrees  $g_i$ , for example,  $\tilde{\mathbf{L}} = \mathbf{I} - \mathbf{G}^{-1} \mathbf{W}$ .

Spectral clustering finds the m eigenvectors  $\mathbf{Z}_{N\times m}$  corresponding to the m smallest eigenvalues of  $\bf{L}$  (ignoring the trivial constant eigenvector). Using a standard method like  $K$ -means, we then cluster the rows of  $Z$  to yield a clustering of the original data points.

An example is presented in Figure 14.29. The top left panel shows 450 simulated data points in three circular clusters indicated by the colors. Kmeans clustering would clearly have difficulty identifying the outer clusters. We applied spectral clustering using a 10-nearest neighbor similarity graph, and display the eigenvector corresponding to the second and third smallest eigenvalue of the graph Laplacian in the lower left. The 15 smallest eigenvalues are shown in the top right panel. The two eigenvectors shown have identified the three clusters, and a scatterplot of the rows of the eigenvector matrix  $\mathbf Y$  in the bottom right clearly separates the clusters. A procedure such as K-means clustering applied to these transformed points would easily identify the three groups.

Why does spectral clustering work? For any vector f we have

$$
\mathbf{f}^T \mathbf{L} \mathbf{f} = \sum_{i=1}^N g_i f_i^2 - \sum_{i=1}^N \sum_{i'=1}^N f_i f_{i'} w_{ii'}
$$
  
= 
$$
\frac{1}{2} \sum_{i=1}^N \sum_{i'=1}^N w_{ii'} (f_i - f_{i'})^2.
$$
 (14.64)

Formula 14.64 suggests that a small value of  $f^T L f$  will be achieved if pairs of points with large adjacencies have coordinates  $f_i$  and  $f_{i'}$  close together.

Since  $\mathbf{1}^T \mathbf{L} \mathbf{1} = 0$  for any graph, the constant vector is a trivial eigenvector with eigenvalue zero. Not so obvious is the fact that if the graph is connected<sup>5</sup>, it is the *only* zero eigenvector (Exercise 14.21). Generalizing this argument, it is easy to show that for a graph with  $m$  connected components,

<sup>5</sup>A graph is connected if any two nodes can be reached via a path of connected nodes.

Image /page/41/Figure/1 description: The image displays four plots related to spectral clustering. The top-left plot shows three concentric circles of data points, colored orange, blue, and green, with axes labeled x1 and x2. The top-right plot is a scatter plot titled 'Eigenvalue' on the y-axis and 'Number' on the x-axis, showing a monotonically increasing trend of eigenvalues from 0.0 to 0.5 as the number increases from 1 to 15. The bottom-left plot, titled 'Eigenvectors', shows the '2nd Smallest' and '3rd Smallest' eigenvectors plotted against 'Index'. The 2nd smallest eigenvector shows three distinct segments of colors (orange, blue, green) separated by dashed lines. The 3rd smallest eigenvector shows orange points clustered at the bottom, blue points in the middle, and green points at the top, also separated by dashed lines. The bottom-right plot, titled 'Spectral Clustering', is a scatter plot with 'Second Smallest Eigenvector' on the x-axis and 'Third Smallest Eigenvector' on the y-axis, showing three distinct clusters of points corresponding to the colors orange, blue, and green.

FIGURE 14.29. Toy example illustrating spectral clustering. Data in top left are 450 points falling in three concentric clusters of 150 points each. The points are uniformly distributed in angle, with radius 1, 2.8 and 5 in the three groups, and Gaussian noise with standard deviation 0.25 added to each point. Using  $a k = 10$ nearest-neighbor similarity graph, the eigenvector corresponding to the second and third smallest eigenvalues of  $L$  are shown in the bottom left; the smallest eigenvector is constant. The data points are colored in the same way as in the top left. The 15 smallest eigenvalues are shown in the top right panel. The coordinates of the 2nd and 3rd eigenvectors (the 450 rows of  $Z$ ) are plotted in the bottom right panel. Spectral clustering does standard (e.g., K-means) clustering of these points and will easily recover the three original clusters.

the nodes can be reordered so that L is block diagonal with a block for each connected component. Then  **has**  $m$  **eigenvectors of eigenvalue zero, and** the eigenspace of eigenvalue zero is spanned by the indicator vectors of the connected components. In practice one has strong and weak connections, so zero eigenvalues are approximated by small eigenvalues.

Spectral clustering is an interesting approach for finding non-convex clusters. When a normalized graph Laplacian is used, there is another way to view this method. Defining  $\mathbf{P} = \mathbf{G}^{-1}\mathbf{W}$ , we consider a random walk on the graph with transition probability matrix P. Then spectral clustering yields groups of nodes such that the random walk seldom transitions from one group to another.

There are a number of issues that one must deal with in applying spectral clustering in practice. We must choose the type of similarity graph—eg. fully connected or nearest neighbors, and associated parameters such as the number of nearest of neighbors  $k$  or the scale parameter of the kernel  $c$ . We must also choose the number of eigenvectors to extract from  **and finally,** as with all clustering methods, the number of clusters. In the toy example of Figure 14.29 we obtained good results for  $k \in [5, 200]$ , the value 200 corresponding to a fully connected graph. With  $k < 5$  the results deteriorated. Looking at the top-right panel of Figure 14.29, we see no strong separation between the smallest three eigenvalues and the rest. Hence it is not clear how many eigenvectors to select.

# 14.5.4 Kernel Principal Components

Spectral clustering is related to kernel principal components, a non-linear version of linear principal components. Standard linear principal components (PCA) are obtained from the eigenvectors of the covariance matrix, and give directions in which the data have maximal variance. Kernel PCA  $(Schölkopf et al., 1999)$  expand the scope of PCA, mimicking what we would obtain if we were to expand the features by non-linear transformations, and then apply PCA in this transformed feature space.

We show in Section 18.5.2 that the principal components variables  $Z$  of a data matrix  $\bf{X}$  can be computed from the inner-product (gram) matrix  $\mathbf{K} = \mathbf{X}\mathbf{X}^T$ . In detail, we compute the eigen-decomposition of the doublecentered version of the gram matrix

$$
\widetilde{\mathbf{K}} = (\mathbf{I} - \mathbf{M})\mathbf{K}(\mathbf{I} - \mathbf{M}) = \mathbf{U}\mathbf{D}^2\mathbf{U}^T, \tag{14.65}
$$

with  $M = 11<sup>T</sup>/N$ , and then  $Z = UD$ . Exercise 18.15 shows how to compute the projections of new observations in this space.

Kernel PCA simply mimics this procedure, interpreting the kernel matrix  $\mathbf{K} = \{K(x_i, x_{i'})\}$  as an inner-product matrix of the implicit features  $\langle \phi(x_i), \phi(x_{i'}) \rangle$  and finding its eigenvectors. The elements of the *mth* component  $z_m$  (*mth* column of **Z**) can be written (up to centering) as  $z_{im} = \sum_{j=1}^{N} \alpha_{jm} K(x_i, x_j)$ , where  $\alpha_{jm} = u_{jm}/d_m$  (Exercise 14.16).

### 548 14. Unsupervised Learning

We can gain more insight into kernel PCA by viewing the  $z_m$  as sample evaluations of principal component functions  $g_m \in \mathcal{H}_K$ , with  $\mathcal{H}_K$  the reproducing kernel Hilbert space generated by  $K$  (see Section 5.8.1). The first principal component function  $g_1$  solves

$$
\max_{g_1 \in \mathcal{H}_K} \text{Var}_{\mathcal{T}} g_1(X) \text{ subject to } ||g_1||_{\mathcal{H}_K} = 1 \tag{14.66}
$$

Here  $\text{Var}_{\mathcal{T}}$  refers to the sample variance over training data  $\mathcal{T}$ . The norm constraint  $||g_1||_{\mathcal{H}_K} = 1$  controls the size and roughness of the function  $g_1$ , as dictated by the kernel  $K$ . As in the regression case it can be shown that the solution to (14.66) is finite dimensional with representation  $g_1(x)$  =  $\sum_{j=1}^{N} c_j K(x, x_j)$ . Exercise 14.17 shows that the solution is defined by  $\hat{c}_j =$  $\alpha_{j1}, j = 1, \ldots, N$  above. The second principal component function is defined in a similar way, with the additional constraint that  $\langle g_1, g_2 \rangle_{\mathcal{H}_K} = 0$ , and so on.<sup>6</sup>

Schölkopf et al. (1999) demonstrate the use of kernel principal components as features for handwritten-digit classification, and show that they can improve the performance of a classifier when these are used instead of linear principal components.

Note that if we use the radial kernel

$$
K(x, x') = \exp(-\|x - x'\|^2/c), \qquad (14.67)
$$

then the kernel matrix  $\bf{K}$  has the same form as the similarity matrix  $\bf{S}$  in spectral clustering. The matrix of edge weights  $W$  is a localized version of K, setting to zero all similarities for pairs of points that are not nearest neighbors.

Kernel PCA finds the eigenvectors corresponding to the largest eigenvalues of  $K$ ; this is equivalent to finding the eigenvectors corresponding to the smallest eigenvalues of

$$
\mathbf{I} - \tilde{\mathbf{K}}.\tag{14.68}
$$

This is almost the same as the Laplacian (14.63), the differences being the centering of  $K$  and the fact that  $G$  has the degrees of the nodes along the diagonal.

Figure 14.30 examines the performance of kernel principal components in the toy example of Figure 14.29. In the upper left panel we used the radial kernel with  $c = 2$ , the same value that was used in spectral clustering. This does not separate the groups, but with  $c = 10$  (upper right panel), the first component separates the groups well. In the lower-left panel we applied kernel PCA using the nearest-neighbor radial kernel W from spectral clustering. In the lower right panel we use the kernel matrix itself as the

 ${}^{6}$ This section benefited from helpful discussions with Jonathan Taylor.

Image /page/44/Figure/1 description: This figure displays four scatter plots, each illustrating the results of applying different kernel methods to an example dataset. The top row shows two plots titled "Radial Kernel (c=2)" and "Radial Kernel (c=10)". Both plots have "First Largest Eigenvector" on the x-axis and "Second Largest Eigenvector" on the y-axis. The "Radial Kernel (c=2)" plot shows two distinct clusters of points, one in orange and one in blue, with a few green points in between. The "Radial Kernel (c=10)" plot also shows three distinct clusters of points, colored orange, blue, and green, arranged in vertical columns. The bottom row contains two plots: "NN Radial Kernel (c=2)" on the left and "Radial Kernel Laplacian (c=2)" on the right. The "NN Radial Kernel (c=2)" plot has "First Largest Eigenvector" on the x-axis and "Second Largest Eigenvector" on the y-axis, displaying a curved distribution of orange points with a single green point at the origin. The "Radial Kernel Laplacian (c=2)" plot has "Second Smallest Eigenvector" on the x-axis and "Third Smallest Eigenvector" on the y-axis, showing a circular arrangement of green points with a cluster of blue and orange points in the center. The x-axis ranges for the plots are approximately -0.10 to 0.05, -0.06 to 0.06, 0.00 to 0.15, and -0.05 to 0.15, respectively. The y-axis ranges are approximately -0.10 to 0.10, -0.05 to 0.05, -0.2 to 0.2, and -0.10 to 0.15, respectively.

FIGURE 14.30. Kernel principal components applied to the toy example of Figure 14.29, using different kernels. (Top left:) Radial kernel (14.67) with  $c = 2$ . (Top right:) Radial kernel with  $c = 10$ . (Bottom left): Nearest neighbor radial kernel W from spectral clustering. (Bottom right:) Spectral clustering with Laplacian constructed from the radial kernel.

### 550 14. Unsupervised Learning

similarity matrix for constructing the Laplacian (14.63) in spectral clustering. In neither case do the projections separate the two groups. Adjusting c did not help either.

In this toy example, we see that kernel PCA is quite sensitive to the scale and nature of the kernel. We also see that the nearest-neighbor truncation of the kernel is important for the success of spectral clustering.

# 14.5.5 Sparse Principal Components

We often interpret principal components by examining the direction vectors  $v_i$ , also known as *loadings*, to see which variables play a role. We did this with the image loadings in  $(14.55)$ . Often this interpretation is made easier if the loadings are sparse. In this section we briefly discuss some methods for deriving principal components with sparse loadings. They are all based on lasso  $(L_1)$  penalties.

We start with an  $N \times p$  data matrix **X**, with centered columns. The proposed methods focus on either the maximum-variance property of principal components, or the minimum reconstruction error. The SCoTLASS procedure of Joliffe et al. (2003) takes the first approach, by solving

$$
\max v^T (\mathbf{X}^T \mathbf{X}) v, \text{ subject to } \sum_{j=1}^p |v_j| \le t, v^T v = 1. \tag{14.69}
$$

The absolute-value constraint encourages some of the loadings to be zero and hence  $v$  to be sparse. Further sparse principal components are found in the same way, by forcing the kth component to be orthogonal to the first  $k-1$  components. Unfortunately this problem is not convex and the computations are difficult.

Zou et al. (2006) start instead with the regression/reconstruction property of PCA, similar to the approach in Section 14.5.1. Let  $x_i$  be the *i*th row of X. For a single component, their sparse principal component technique solves

$$
\min_{\theta, v} \sum_{i=1}^{N} ||x_i - \theta v^T x_i||_2^2 + \lambda ||v||_2^2 + \lambda_1 ||v||_1 \quad (14.70)
$$

$$
subject to ||\theta||_2 = 1.
$$

Let's examine this formulation in more detail.

- If both  $\lambda$  and  $\lambda_1$  are zero and  $N > p$ , it is easy to show that  $v = \theta$ and is the largest principal component direction.
- When  $p \gg N$  the solution is not necessarily unique unless  $\lambda > 0$ . For any  $\lambda > 0$  and  $\lambda_1 = 0$  the solution for v is proportional to the largest principal component direction.
- The second penalty on  $v$  encourages sparseness of the loadings.

Image /page/46/Figure/1 description: The image displays two rows of diagrams, each illustrating a curved shape with an inner red line. The top row is labeled "Walking Speed" and shows two such diagrams. The bottom row is labeled "Verbal Fluency" and also shows two diagrams of the same curved shape with an inner red line. The diagrams appear to be representations of anatomical structures, possibly related to brain imaging, with the red lines indicating variations or measurements within the structure.

Principal Components Sparse Principal Components

FIGURE 14.31. Standard and sparse principal components from a study of the corpus callosum variation. The shape variations corresponding to significant principal components (red curves) are overlaid on the mean CC shape (black curves).

For multiple components, the sparse principal components procedures minimizes

$$
\sum_{i=1}^{N} ||x_i - \mathbf{\Theta} \mathbf{V}^T x_i||^2 + \lambda \sum_{k=1}^{K} ||v_k||_2^2 + \sum_{k=1}^{K} \lambda_{1k} ||v_k||_1,
$$
 (14.71)

subject to  $\mathbf{\Theta}^T \mathbf{\Theta} = \mathbf{I}_K$ . Here **V** is a  $p \times K$  matrix with columns  $v_k$  and  $\mathbf{\Theta}$ is also  $p \times K$ .

Criterion (14.71) is not jointly convex in  $V$  and  $\Theta$ , but it is convex in each parameter with the other parameter fixed<sup>7</sup>. Minimization over  $V$  with  $\Theta$  fixed is equivalent to K elastic net problems (Section 18.4) and can be done efficiently. On the other hand, minimization over  $\Theta$  with V fixed is a version of the Procrustes problem (14.56), and is solved by a simple SVD calculation (Exercise 14.12). These steps are alternated until convergence.

Figure 14.31 shows an example of sparse principal components analysis using  $(14.71)$ , taken from Sjöstrand et al.  $(2007)$ . Here the shape of the mid-sagittal cross-section of the corpus callosum (CC) is related to various clinical parameters in a study involving 569 elderly persons 8 . In this exam-

 $7$ Note that the usual principal component criterion, for example  $(14.50)$ , is not jointly convex in the parameters either. Nevertheless, the solution is well defined and an efficient algorithm is available.

<sup>&</sup>lt;sup>8</sup>We thank Rasmus Larsen and Karl Sjöstrand for suggesting this application, and supplying us with the postscript figures reproduced here.

Image /page/47/Picture/1 description: A sagittal MRI scan of a human brain is shown, with the corpus callosum highlighted by a dotted red line. The scan displays various brain structures, including the cerebellum, brainstem, and cerebrum. The corpus callosum, a large C-shaped bundle of nerve fibers, is clearly delineated in the central part of the brain.

FIGURE 14.32. An example of a mid-saggital brain slice, with the corpus collosum annotated with landmarks.

ple PCA is applied to shape data, and is a popular tool in morphometrics. For such applications, a number of landmarks are identified along the circumference of the shape; an example is given in Figure 14.32. These are aligned by Procrustes analysis to allow for rotations, and in this case scaling as well (see Section 14.5.1). The features used for PCA are the sequence of coordinate pairs for each landmark, unpacked into a single vector.

In this analysis, both standard and sparse principal components were computed, and components that were significantly associated with various clinical parameters were identified. In the figure, the shape variations corresponding to significant principal components (red curves) are overlaid on the mean CC shape (black curves). Low walking speed relates to CCs that are thinner (displaying atrophy) in regions connecting the motor control and cognitive centers of the brain. Low verbal fluency relates to CCs that are thinner in regions connecting auditory/visual/cognitive centers. The sparse principal components procedure gives a more parsimonious, and potentially more informative picture of the important differences.

# 14.6 Non-negative Matrix Factorization

Non-negative matrix factorization (Lee and Seung, 1999) is a recent alternative approach to principal components analysis, in which the data and components are assumed to be non-negative. It is useful for modeling non-negative data such as images.

The  $N \times p$  data matrix **X** is approximated by

$$
\mathbf{X} \approx \mathbf{WH} \tag{14.72}
$$

where **W** is  $N \times r$  and **H** is  $r \times p$ ,  $r \leq \max(N, p)$ . We assume that  $x_{ij}, w_{ik}, h_{kj} \geq 0.$ 

The matrices  $\bf{W}$  and  $\bf{H}$  are found by maximizing

$$
L(\mathbf{W}, \mathbf{H}) = \sum_{i=1}^{N} \sum_{j=1}^{p} [x_{ij} \log(\mathbf{W} \mathbf{H})_{ij} - (\mathbf{W} \mathbf{H})_{ij}].
$$
 (14.73)

This is the log-likelihood from a model in which  $x_{ij}$  has a Poisson distribution with mean  $(\mathbf{WH})_{ii}$ —quite reasonable for positive data.

The following alternating algorithm (Lee and Seung, 2001) converges to a local maximum of  $L(W, H)$ :

$$
w_{ik} \\leftarrow w_{ik} \frac{\sum_{j=1}^{p} h_{kj} x_{ij} / (\mathbf{WH})_{ij}}{\sum_{j=1}^{p} h_{kj}}
$$
  
\n
$$
h_{kj} \\leftarrow h_{kj} \frac{\sum_{i=1}^{N} w_{ik} x_{ij} / (\mathbf{WH})_{ij}}{\sum_{i=1}^{N} w_{ik}}
$$
 (14.74)

This algorithm can be derived as a minorization procedure for maximizing  $L(W, H)$  (Exercise 14.23) and is also related to the iterative-proportionalscaling algorithm for log-linear models (Exercise 14.24).

Figure 14.33 shows an example taken from Lee and Seung  $(1999)^9$ , comparing non-negative matrix factorization (NMF), vector quantization (VQ, equivalent to k-means clustering) and principal components analysis (PCA). The three learning methods were applied to a database of  $N = 2,429$  facial images, each consisting of  $19 \times 19$  pixels, resulting in a  $2,429 \times 381$ matrix **X**. As shown in the  $7 \times 7$  array of montages (each a  $19 \times 19$  image), each method has learned a set of  $r = 49$  basis images. Positive values are illustrated with black pixels and negative values with red pixels. A particular instance of a face, shown at top right, is approximated by a linear superposition of basis images. The coefficients of the linear superposition are shown next to each montage, in a  $7 \times 7$  array<sup>10</sup>, and the resulting superpositions are shown to the right of the equality sign. The authors point

<sup>9</sup>We thank Sebastian Seung for providing this image.

 $^{10}\mathrm{These}$  7  $\times$  7 arrangements allow for a compact display, and have no structural significance.

### 554 14. Unsupervised Learning

out that unlike VQ and PCA, NMF learns to represent faces with a set of basis images resembling parts of faces.

Donoho and Stodden (2004) point out a potentially serious problem with non-negative matrix factorization. Even in situations where  $X = WH$  holds exactly, the decomposition may not be unique. Figure 14.34 illustrates the problem. The data points lie in  $p = 2$  dimensions, and there is "open space" between the data and the coordinate axes. We can choose the basis vectors  $h_1$  and  $h_2$  anywhere in this open space, and represent each data point exactly with a nonnegative linear combination of these vectors. This nonuniqueness means that the solution found by the above algorithm depends on the starting values, and it would seem to hamper the interpretability of the factorization. Despite this interpretational drawback, the non-negative matrix factorization and its applications has attracted a lot of interest.

# 14.6.1 Archetypal Analysis

This method, due to Cutler and Breiman (1994), approximates data points by prototypes that are themselves linear combinations of data points. In this sense it has a similar flavor to K-means clustering. However, rather than approximating each data point by a single nearby prototype, archetypal analysis approximates each data point by a convex combination of a collection of prototypes. The use of a convex combination forces the prototypes to lie on the convex hull of the data cloud. In this sense, the prototypes are "pure,", or "archetypal."

As in (14.72), the  $N \times p$  data matrix **X** is modeled as

$$
\mathbf{X} \approx \mathbf{WH} \tag{14.75}
$$

where **W** is  $N \times r$  and **H** is  $r \times p$ . We assume that  $w_{ik} \ge 0$  and  $\sum_{k=1}^{r} w_{ik} =$ 1  $\forall i$ . Hence the N data points (rows of X) in p-dimensional space are represented by convex combinations of the  $r$  archetypes (rows of  $H$ ). We also assume that

$$
\mathbf{H} = \mathbf{B}\mathbf{X} \tag{14.76}
$$

where **B** is  $r \times N$  with  $b_{ki} \geq 0$  and  $\sum_{i=1}^{N} b_{ki} = 1 \forall k$ . Thus the archetypes themselves are convex combinations of the data points. Using both (14.75) and (14.76) we minimize

$$
J(\mathbf{W}, \mathbf{B}) = ||\mathbf{X} - \mathbf{W}\mathbf{H}||^2
$$
  
=  $||\mathbf{X} - \mathbf{W}\mathbf{B}\mathbf{X}||^2$  (14.77)

over the weights  $W$  and  $B$ . This function is minimized in an alternating fashion, with each separate minimization involving a convex optimization. The overall problem is not convex however, and so the algorithm converges to a local minimum of the criterion.

Image /page/50/Figure/1 description: The image displays a comparison of three dimensionality reduction techniques: NMF, VQ, and PCA. Each technique is illustrated with a diagram showing a large matrix of facial features, a smaller matrix representing coefficients or codes, a multiplication symbol, an equals sign, and a reconstructed facial image. The 'Original' image is shown at the top right, serving as a reference. The NMF section shows a large matrix of facial parts, a smaller matrix with varying shades of gray and black, and a reconstructed face. The VQ section shows a large matrix of complete faces, a small matrix with a single black square, and a reconstructed face. The PCA section shows a large matrix with facial parts in grayscale and red, a smaller matrix with red, gray, and black squares, and a reconstructed face. The text 'NMF', 'VQ', and 'PCA' are labels for each section. The image also contains the text 'Original' above the reference face.

FIGURE 14.33. Non-negative matrix factorization (NMF), vector quantization (VQ, equivalent to k-means clustering) and principal components analysis (PCA) applied to a database of facial images. Details are given in the text. Unlike VQ and PCA, NMF learns to represent faces with a set of basis images resembling parts of faces.

556 14. Unsupervised Learning

Image /page/51/Figure/1 description: A scatter plot shows several blue dots distributed within a quadrant defined by two axes labeled h1 and h2. The h1 axis points upwards and to the left, while the h2 axis points downwards and to the right. The dots are arranged in a somewhat rectangular pattern, with a few clustered near the h1 axis and others spread out towards the h2 axis.

FIGURE 14.34. Non-uniqueness of the non-negative matrix factorization. There are 11 data points in two dimensions. Any choice of the basis vectors  $h_1$ and  $h_2$  in the open space between the coordinate axes and data, gives an exact reconstruction of the data.

Figure 14.35 shows an example with simulated data in two dimensions. The top panel displays the results of archetypal analysis, while the bottom panel shows the results from K-means clustering. In order to best reconstruct the data from convex combinations of the prototypes, it pays to locate the prototypes on the convex hull of the data. This is seen in the top panels of Figure 14.35 and is the case in general, as proven by Cutler and Breiman (1994). K-means clustering, shown in the bottom panels, chooses prototypes in the middle of the data cloud.

We can think of K-means clustering as a special case of the archetypal model, in which each row of  $W$  has a single one and the rest of the entries are zero.

Notice also that the archetypal model (14.75) has the same general form as the non-negative matrix factorization model (14.72). However, the two models are applied in different settings, and have somewhat different goals. Non-negative matrix factorization aims to approximate the columns of the data matrix X, and the main output of interest are the columns of W representing the primary non-negative components in the data. Archetypal analysis focuses instead on the approximation of the rows of  $X$  using the rows of H, which represent the archetypal data points. Non-negative matrix factorization also assumes that  $r \leq p$ . With  $r = p$ , we can get an exact reconstruction simply choosing  $W$  to be the data  $X$  with columns scaled so that they sum to 1. In contrast, archetypal analysis requires  $r \leq N$ , but allows  $r > p$ . In Figure 14.35, for example,  $p = 2, N = 50$  while  $r = 2, 4$  or 8. The additional constraint (14.76) implies that the archetypal approximation will not be perfect, even if  $r > p$ .

Figure 14.36 shows the results of archetypal analysis applied to the database of 3's displayed in Figure 14.22. The three rows in Figure 14.36 are the resulting archetypes from three runs, specifying two, three and four

Image /page/52/Figure/0 description: The image contains text that reads "14.7 Independent Component Analysis and Exploratory Projection Pursuit 557".

Image /page/52/Figure/1 description: The image displays a 2x3 grid of scatter plots. The top row shows scatter plots with red dots representing prototypes, labeled '2 Prototypes', '4 Prototypes', and '8 Prototypes' from left to right. The bottom row shows similar scatter plots but with blue dots representing prototypes, also labeled '2 Prototypes', '4 Prototypes', and '8 Prototypes' from left to right. Each scatter plot contains numerous small black circles representing data points, arranged in a generally diagonal pattern from bottom left to top right, with some clustering in the upper middle section.

FIGURE 14.35. Archetypal analysis (top panels) and K-means clustering (bottom panels) applied to 50 data points drawn from a bivariate Gaussian distribution. The colored points show the positions of the prototypes in each case.

archetypes, respectively. As expected, the algorithm has produced extreme 3's both in size and shape.

# 14.7 Independent Component Analysis and Exploratory Projection Pursuit

Multivariate data are often viewed as multiple indirect measurements arising from an underlying source, which typically cannot be directly measured. Examples include the following:

- Educational and psychological tests use the answers to questionnaires to measure the underlying intelligence and other mental abilities of subjects.
- EEG brain scans measure the neuronal activity in various parts of the brain indirectly via electromagnetic signals recorded at sensors placed at various positions on the head.
- The trading prices of stocks change constantly over time, and reflect various unmeasured factors such as market confidence, external in-

Image /page/53/Picture/1 description: The image displays a grid of seven pixelated images, each featuring the digit '3'. The images are arranged in a triangular pattern with two images in the first row, three in the second, and two in the third. The digit '3' in each image is rendered in yellow and white against a red background. The pixelation is evident, giving the digits a slightly blurred or abstract appearance.

FIGURE 14.36. Archetypal analysis applied to the database of digitized 3's. The rows in the figure show the resulting archetypes from three runs, specifying two, three and four archetypes, respectively.

fluences, and other driving forces that may be hard to identify or measure.

Factor analysis is a classical technique developed in the statistical literature that aims to identify these latent sources. Factor analysis models are typically wed to Gaussian distributions, which has to some extent hindered their usefulness. More recently, independent component analysis has emerged as a strong competitor to factor analysis, and as we will see, relies on the non-Gaussian nature of the underlying sources for its success.

# 14.7.1 Latent Variables and Factor Analysis

The singular-value decomposition  $X = UDV^T$  (14.54) has a latent variable representation. Writing  $\mathbf{S} = \sqrt{N}\mathbf{U}$  and  $\mathbf{A}^T = \mathbf{D}\mathbf{V}^T/\sqrt{N}$ , we have  $\mathbf{X} =$  $\mathbf{S} \mathbf{A}^T$ , and hence each of the columns of **X** is a linear combination of the columns of S. Now since U is orthogonal, and assuming as before that the columns of  $X$  (and hence U) each have mean zero, this implies that the columns of S have zero mean, are uncorrelated and have unit variance. In terms of random variables, we can interpret the SVD, or the corresponding principal component analysis (PCA) as an estimate of a latent variable model

14.7 Independent Component Analysis and Exploratory Projection Pursuit 559

$$
X_1 = a_{11}S_1 + a_{12}S_2 + \cdots + a_{1p}S_p
$$

$$
X_2 = a_{21}S_1 + a_{22}S_2 + \cdots + a_{2p}S_p
$$

$$
\vdots \qquad \vdots
$$

$$
X_p = a_{p1}S_1 + a_{p2}S_2 + \cdots + a_{pp}S_p,
$$
 $(14.78)$ 

or simply  $X = AS$ . The correlated  $X_j$  are each represented as a linear expansion in the uncorrelated, unit variance variables  $S_{\ell}$ . This is not too satisfactory, though, because given any orthogonal  $p \times p$  matrix **R**, we can write

$$
X = AS
$$
  
= AR<sup>T</sup>RS  
= A<sup>\*</sup>S<sup>\*</sup>, (14.79)

and  $Cov(S^*) = \mathbf{R} Cov(S) \mathbf{R}^T = \mathbf{I}$ . Hence there are many such decompositions, and it is therefore impossible to identify any particular latent variables as unique underlying sources. The SVD decomposition does have the property that any rank  $q < p$  truncated decomposition approximates X in an optimal way.

The classical factor analysis model, developed primarily by researchers in psychometrics, alleviates these problems to some extent; see, for example, Mardia et al. (1979). With  $q < p$ , a factor analysis model has the form

$$
X_1 = a_{11}S_1 + \dots + a_{1q}S_q + \varepsilon_1
$$
  
\n
$$
X_2 = a_{21}S_1 + \dots + a_{2q}S_q + \varepsilon_2
$$
  
\n
$$
\vdots \qquad \vdots
$$
  
\n
$$
X_p = a_{p1}S_1 + \dots + a_{pq}S_q + \varepsilon_p,
$$
  
\n(14.80)

or  $X = AS + \varepsilon$ . Here S is a vector of  $q < p$  underlying latent variables or factors, **A** is a  $p \times q$  matrix of factor *loadings*, and the  $\varepsilon_i$  are uncorrelated zero-mean disturbances. The idea is that the latent variables  $S_{\ell}$  are common sources of variation amongst the  $X_j$ , and account for their correlation structure, while the uncorrelated  $\varepsilon_j$  are unique to each  $X_j$  and pick up the remaining unaccounted variation. Typically the  $S_{\ell}$  and the  $\varepsilon_j$  are modeled as Gaussian random variables, and the model is fit by maximum likelihood. The parameters all reside in the covariance matrix

$$
\Sigma = AA^T + D_{\varepsilon}, \qquad (14.81)
$$

where  $\mathbf{D}_{\varepsilon} = \text{diag}[\text{Var}(\varepsilon_1), \dots, \text{Var}(\varepsilon_p)].$  The  $S_{\ell}$  being Gaussian and uncorrelated makes them statistically independent random variables. Thus a battery of educational test scores would be thought to be driven by the independent underlying factors such as *intelligence*, *drive* and so on. The columns of A are referred to as the factor loadings, and are used to name and interpret the factors.

### 560 14. Unsupervised Learning

Unfortunately the identifiability issue (14.79) remains, since **A** and  $AR<sup>T</sup>$ are equivalent in (14.81) for any  $q \times q$  orthogonal **R**. This leaves a certain subjectivity in the use of factor analysis, since the user can search for rotated versions of the factors that are more easily interpretable. This aspect has left many analysts skeptical of factor analysis, and may account for its lack of popularity in contemporary statistics. Although we will not go into details here, the SVD plays a key role in the estimation of (14.81). For example, if the  $\text{Var}(\varepsilon_i)$  are all assumed to be equal, the leading q components of the SVD identify the subspace determined by A.

Because of the separate disturbances  $\varepsilon_i$  for each  $X_i$ , factor analysis can be seen to be modeling the correlation structure of the  $X_i$  rather than the covariance structure. This can be easily seen by standardizing the covariance structure in (14.81) (Exercise 14.14). This is an important distinction between factor analysis and PCA, although not central to the discussion here. Exercise 14.15 discusses a simple example where the solutions from factor analysis and PCA differ dramatically because of this distinction.

# 14.7.2 Independent Component Analysis

The independent component analysis (ICA) model has exactly the same form as (14.78), except the  $S_{\ell}$  are assumed to be *statistically indepen*dent rather than uncorrelated. Intuitively, lack of correlation determines the second-degree cross-moments (covariances) of a multivariate distribution, while in general statistical independence determines all of the crossmoments. These extra moment conditions allow us to identify the elements of A uniquely. Since the multivariate Gaussian distribution is determined by its second moments alone, it is the exception, and any Gaussian independent components can be determined only up to a rotation, as before. Hence identifiability problems in (14.78) and (14.80) can be avoided if we assume that the  $S_{\ell}$  are independent and non-Gaussian.

Here we will discuss the full  $p$ -component model as in  $(14.78)$ , where the  $S_{\ell}$  are independent with unit variance; ICA versions of the factor analysis model (14.80) exist as well. Our treatment is based on the survey article by Hyvärinen and Oja  $(2000)$ .

We wish to recover the mixing matrix  $\mathbf{A}$  in  $X = \mathbf{A}S$ . Without loss of generality, we can assume that  $X$  has already been *whitened* to have  $Cov(X) = I$ ; this is typically achieved via the SVD described above. This in turn implies that  $A$  is orthogonal, since  $S$  also has covariance I. So solving the ICA problem amounts to finding an orthogonal A such that the components of the vector random variable  $S = \mathbf{A}^T \bar{X}$  are independent (and non-Gaussian).

Figure 14.37 shows the power of ICA in separating two mixed signals. This is an example of the classical *cocktail party problem*, where different microphones  $X_i$  pick up mixtures of different independent sources  $S_\ell$ (music, speech from different speakers, etc.). ICA is able to perform blind

Image /page/56/Figure/1 description: The image displays a comparison of source signals, measured signals, and solutions from Principal Component Analysis (PCA) and Independent Component Analysis (ICA). The top left panel shows the 'Source Signals' with a green sine wave and a red sawtooth wave. The top right panel shows the 'Measured Signals', which appear to be a mixture of the source signals, depicted as a purple wave and a yellow wave. The bottom left panel presents the 'PCA Solution', showing a purple wave and a yellow wave that are somewhat similar to the measured signals but with some differences. The bottom right panel shows the 'ICA Solution', which successfully separates the original source signals, displaying a red sawtooth wave and a green sine wave.

FIGURE 14.37. Illustration of ICA vs. PCA on artificial time-series data. The upper left panel shows the two source signals, measured at 1000 uniformly spaced time points. The upper right panel shows the observed mixed signals. The lower two panels show the principal components and independent component solutions.

source separation, by exploiting the independence and non-Gaussianity of the original sources.

Many of the popular approaches to ICA are based on entropy. The differential entropy H of a random variable Y with density  $g(y)$  is given by

$$
H(Y) = -\int g(y) \log g(y) dy.
$$
 (14.82)

A well-known result in information theory says that among all random variables with equal variance, Gaussian variables have the maximum entropy. Finally, the *mutual information*  $I(Y)$  between the components of the random vector  $Y$  is a natural measure of dependence:

$$
I(Y) = \sum_{j=1}^{p} H(Y_j) - H(Y).
$$
 (14.83)

The quantity  $I(Y)$  is called the Kullback–Leibler distance between the density  $g(y)$  of Y and its independence version  $\prod_{j=1}^p g_j(y_j)$ , where  $g_j(y_j)$ is the marginal density of  $Y_j$ . Now if X has covariance **I**, and  $Y = \mathbf{A}^T X$ with **A** orthogonal, then it is easy to show that

$$
I(Y) = \sum_{j=1}^{p} H(Y_j) - H(X) - \log|\det \mathbf{A}| \qquad (14.84)
$$

$$
= \sum_{j=1}^{p} H(Y_j) - H(X). \tag{14.85}
$$

Finding an **A** to minimize  $I(Y) = I(A^T X)$  looks for the orthogonal transformation that leads to the most independence between its components. In

### 562 14. Unsupervised Learning

Image /page/57/Figure/1 description: The image displays four scatter plots arranged in a 2x2 grid. The top-left plot is titled "Source S" and shows a random distribution of green dots. The top-right plot is titled "Data X" and shows a diagonal distribution of red dots. The bottom-left plot is titled "PCA Solution" and shows a diamond-shaped distribution of purple dots. The bottom-right plot is titled "ICA Solution" and shows a random distribution of cyan dots, similar to the "Source S" plot.

FIGURE 14.38. Mixtures of independent uniform random variables. The upper left panel shows 500 realizations from the two independent uniform sources, the upper right panel their mixed versions. The lower two panels show the PCA and ICA solutions, respectively.

light of (14.84) this is equivalent to minimizing the sum of the entropies of the separate components of  $Y$ , which in turn amounts to maximizing their departures from Gaussianity.

For convenience, rather than using the entropy  $H(Y_i)$ , Hyvärinen and Oja (2000) use the *negentropy* measure  $J(Y_j)$  defined by

$$
J(Y_j) = H(Z_j) - H(Y_j),
$$
\n(14.86)

where  $Z_j$  is a Gaussian random variable with the same variance as  $Y_j$ . Negentropy is non-negative, and measures the departure of  $Y_j$  from Gaussianity. They propose simple approximations to negentropy which can be computed and optimized on data. The ICA solutions shown in Figures 14.37– 14.39 use the approximation

$$
J(Y_j) \approx [\mathrm{E}G(Y_j) - \mathrm{E}G(Z_j)]^2,\tag{14.87}
$$

where  $G(u) = \frac{1}{a} \log \cosh(au)$  for  $1 \le a \le 2$ . When applied to a sample of  $x_i$ , the expectations are replaced by data averages. This is one of the options in the FastICA software provided by these authors. More classical (and less robust) measures are based on fourth moments, and hence look for departures from the Gaussian via kurtosis. See Hyvärinen and Oja (2000) for more details. In Section 14.7.4 we describe their approximate Newton algorithm for finding the optimal directions.

In summary then, ICA applied to multivariate data looks for a sequence of orthogonal projections such that the projected data look as far from

Image /page/58/Figure/1 description: This is a scatter plot matrix showing the relationship between 5 PCA components and 5 ICA components. The rows represent PCA components 1 through 5, and the columns represent ICA components 1 through 5. Each cell in the matrix displays a scatter plot of the data points for the corresponding PCA and ICA components. The plots show varying degrees of correlation and clustering between the components. Some plots show distinct clusters of red and blue points, while others show more diffuse distributions of green points.

ICA Components

FIGURE 14.39. A comparison of the first five ICA components computed using FastICA (above diagonal) with the first five PCA components(below diagonal). Each component is standardized to have unit variance.

Gaussian as possible. With pre-whitened data, this amounts to looking for components that are as independent as possible.

ICA starts from essentially a factor analysis solution, and looks for rotations that lead to independent components. From this point of view, ICA is just another factor rotation method, along with the traditional "varimax" and "quartimax" methods used in psychometrics.

### Example: Handwritten Digits

We revisit the handwritten threes analyzed by PCA in Section 14.5.1. Figure 14.39 compares the first five (standardized) principal components with the first five ICA components, all shown in the same standardized units. Note that each plot is a two-dimensional projection from a 256-dimensional

Image /page/59/Figure/1 description: The image displays a grid of six pairs of handwritten digits, all of which are the number 3. The first column is labeled "Mean" and shows two versions of the digit 3. The subsequent five columns are labeled "ICA 1" through "ICA 5", each also showing two versions of the digit 3. The top row of each pair appears to be a slightly different representation or reconstruction of the digit, while the bottom row shows a more consistent, bolder rendering of the digit 3.

FIGURE 14.40. The highlighted digits from Figure 14.39. By comparing with the mean digits, we see the nature of the ICA component.

space. While the PCA components all appear to have joint Gaussian distributions, the ICA components have long-tailed distributions. This is not too surprising, since PCA focuses on variance, while ICA specifically looks for non-Gaussian distributions. All the components have been standardized, so we do not see the decreasing variances of the principal components.

For each ICA component we have highlighted two of the extreme digits, as well as a pair of central digits and displayed them in Figure 14.40. This illustrates the nature of each of the components. For example, ICA component five picks up the long sweeping tailed threes.

### Example: EEG Time Courses

ICA has become an important tool in the study of brain dynamics—the example we present here uses ICA to untangle the components of signals in multi-channel electroencephalographic (EEG) data (Onton and Makeig, 2006).

Subjects wear a cap embedded with a lattice of 100 EEG electrodes, which record brain activity at different locations on the scalp. Figure 14.41<sup>11</sup> (top panel) shows 15 seconds of output from a subset of nine of these electrodes from a subject performing a standard "two-back" learning task over a 30 minute period. The subject is presented with a letter (B, H, J, C, F, or K) at roughly 1500-ms intervals, and responds by pressing one of two buttons to indicate whether the letter presented is the same or different from that presented two steps back. Depending on the answer, the subject earns or loses points, and occasionally earns bonus or loses penalty points. The time-course data show spatial correlation in the EEG signals—the signals of nearby sensors look very similar.

The key assumption here is that signals recorded at each scalp electrode are a mixture of independent potentials arising from different cortical ac-

 $11$ Reprinted from *Progress in Brain Research*, Vol. 159, Julie Onton and Scott Makeig, "Information based modeling of event-related brain dynamics," Page 106 , Copyright (2006), with permission from Elsevier. We thank Julie Onton and Scott Makeig for supplying an electronic version of the image.

tivities, as well as non-cortical artifact domains; see the reference for a detailed overview of ICA in this domain.

The lower part of Figure 14.41 shows a selection of ICA components. The colored images represent the estimated unmixing coefficient vectors  $\hat{a}_i$ as heatmap images superimposed on the scalp, indicating the location of activity. The corresponding time-courses show the activity of the learned ICA components.

For example, the subject blinked after each performance feedback signal (colored vertical lines), which accounts for the location and artifact signal in IC1 and IC3. IC12 is an artifact associated with the cardiac pulse. IC4 and IC7 account for frontal theta-band activities, and appear after a stretch of correct performance. See Onton and Makeig (2006) for a more detailed discussion of this example, and the use of ICA in EEG modeling.

# 14.7.3 Exploratory Projection Pursuit

Friedman and Tukey (1974) proposed exploratory projection pursuit, a graphical exploration technique for visualizing high-dimensional data. Their view was that most low (one- or two-dimensional) projections of highdimensional data look Gaussian. Interesting structure, such as clusters or long tails, would be revealed by non-Gaussian projections. They proposed a number of projection indices for optimization, each focusing on a different departure from Gaussianity. Since their initial proposal, a variety of improvements have been suggested (Huber, 1985; Friedman, 1987), and a variety of indices, including entropy, are implemented in the interactive graphics package Xgobi (Swayne et al., 1991, now called GGobi). These projection indices are exactly of the same form as  $J(Y_i)$  above, where  $Y_j = a_j^T X$ , a normalized linear combination of the components of X. In fact, some of the approximations and substitutions for cross-entropy coincide with indices proposed for projection pursuit. Typically with projection pursuit, the directions  $a_j$  are not constrained to be orthogonal. Friedman (1987) transforms the data to look Gaussian in the chosen projection, and then searches for subsequent directions. Despite their different origins, ICA and exploratory projection pursuit are quite similar, at least in the representation described here.

# 14.7.4 A Direct Approach to ICA

Image /page/60/Picture/7 description: A yellow, cartoonish face with wide eyes and an open mouth is depicted. The face is holding its hands to its ears, with the mouth agape in a scream. The background is black.

Independent components have by definition a joint product density

$$
f_S(s) = \prod_{j=1}^p f_j(s_j),
$$
\n(14.88)

so here we present an approach that estimates this density directly using generalized additive models (Section 9.1). Full details can be found in

Image /page/61/Figure/1 description: The image displays EEG data over 15 seconds, categorized by events like 'Penalty', 'Wrong', 'Correct', and 'Bonus'. The top section shows EEG signals from selected channels on a schematic of a head, with vertical lines indicating event timings. A scale bar indicates 95 microvolts. The bottom section presents various EEG components labeled EOG, theta, alpha, ERP, ECG, and EMG, also time-locked to the events. On the left and right sides, topographical maps (IC1, IC4, IC6, IC8, IC55, IC3, IC5, IC7, IC12) illustrate the spatial distribution of brain activity associated with these components. The x-axis is labeled 'Time (s)' and ranges from 1 to 14. A legend indicates 'Letter On' for a specific time segment.

FIGURE 14.41. Fifteen seconds of EEG data (of 1917 seconds) at nine (of 100) scalp channels (top panel), as well as nine ICA components (lower panel). While nearby electrodes record nearly identical mixtures of brain and non-brain activity, ICA components are temporally distinct. The colored scalps represent the ICA unmixing coefficients  $\hat{a}_j$  as a heatmap, showing brain or scalp location of the source.

Hastie and Tibshirani (2003), and the method is implemented in the R package ProDenICA, available from CRAN.

In the spirit of representing departures from Gaussianity, we represent each  $f_i$  as

$$
f_j(s_j) = \phi(s_j)e^{g_j(s_j)},
$$
\n(14.89)

a *tilted* Gaussian density. Here  $\phi$  is the standard Gaussian density, and  $g_j$  satisfies the normalization conditions required of a density. Assuming as before that  $X$  is pre-whitened, the log-likelihood for the observed data  $X = AS$  is

$$
\ell(\mathbf{A}, \{g_j\}_1^p; \mathbf{X}) = \sum_{i=1}^N \sum_{j=1}^p \left[ \log \phi_j(a_j^T x_i) + g_j(a_j^T x_i) \right],\tag{14.90}
$$

which we wish to maximize subject to the constraints that  $A$  is orthogonal and that the  $g_i$  result in densities in (14.89). Without imposing any further restrictions on  $g_j$ , the model (14.90) is over-parametrized, so we instead maximize a regularized version

$$
\sum_{j=1}^{p} \left[ \frac{1}{N} \sum_{i=1}^{N} \left[ \log \phi(a_j^T x_i) + g_j(a_j^T x_i) \right] - \int \phi(t) e^{g_j(t)} dt - \lambda_j \int \{g_j'''(t)\}^2(t) dt \right].
$$
\n(14.91)

We have subtracted two penalty terms (for each  $j$ ) in (14.91), inspired by Silverman (1986, Section 5.4.4):

- The first enforces the density constraint  $\int \phi(t)e^{\hat{g}_j(t)}dt = 1$  on any solution  $\hat{q}_i$ .
- The second is a roughness penalty, which guarantees that the solution  $\hat{g}_j$  is a quartic-spline with knots at the observed values of  $s_{ij} = a_j^T x_i$ .

It can further be shown that the solution densities  $f_j = \phi e^{\hat{g}_j}$  each have mean zero and variance one (Exercise 14.18). As we increase  $\lambda_i$ , these solutions approach the standard Gaussian  $\phi$ .

| Algorithm 14.3 Product Density ICA Algorithm: ProDenICA |  |  |  |  |  |  |  |
|---------------------------------------------------------|--|--|--|--|--|--|--|
|---------------------------------------------------------|--|--|--|--|--|--|--|

- 1. Initialize A (random Gaussian matrix followed by orthogonalization).
- 2. Alternate until convergence of A:
  - (a) Given **A**, optimize (14.91) w.r.t.  $g_i$  (separately for each j).
  - (b) Given  $g_j$ ,  $j = 1, \ldots, p$ , perform one step of a fixed point algorithm towards finding the optimal A.

We fit the functions  $g_j$  and directions  $a_j$  by optimizing (14.91) in an alternating fashion, as described in Algorithm 14.3.

### 568 14. Unsupervised Learning

Step 2(a) amounts to a semi-parametric density estimation, which can be solved using a novel application of generalized additive models. For convenience we extract one of the p separate problems,

$$
\frac{1}{N} \sum_{i=1}^{N} \left[ \log \phi(s_i) + g(s_i) \right] - \int \phi(t) e^{g(t)} dt - \lambda \int \{g'''(t)\}^2(t) dt. \tag{14.92}
$$

Although the second integral in (14.92) leads to a smoothing spline, the first integral is problematic, and requires an approximation. We construct a fine grid of L values  $s^*_\ell$  in increments  $\Delta$  covering the observed values  $s_i,$ and count the number of  $s_i$  in the resulting bins:

$$
y_{\ell}^* = \frac{\#s_i \in (s_{\ell}^* - \Delta/2, s_{\ell}^* + \Delta/2)}{N}.
$$
 (14.93)

Typically we pick  $L$  to be 1000, which is more than adequate. We can then approximate (14.92) by

$$
\sum_{\ell=1}^{L} \left\{ y_i^* \left[ \log(\phi(s_\ell^*)) + g(s_\ell^*) \right] - \Delta \phi(s_\ell^*) e^{g(s_\ell^*)} \right\} - \lambda \int g'''^2(s) ds. \tag{14.94}
$$

This last expression can be seen to be proportional to a penalized Poisson log-likelihood with response  $y_{\ell}^*/\Delta$  and penalty parameter  $\lambda/\Delta$ , and mean  $\mu(s) = \phi(s)e^{g(s)}$ . This is a generalized additive spline model (Hastie and Tibshirani, 1990; Efron and Tibshirani, 1996), with an offset term  $\log \phi(s)$ , and can be fit using a Newton algorithm in  $O(L)$  operations. Although a quartic spline is called for, we find in practice that a cubic spline is adequate. We have p tuning parameters  $\lambda_i$  to set; in practice we make them all the same, and specify the amount of smoothing via the effective degrees-of-freedom  $df(\lambda)$ . Our software uses 5df as a default value.

Step 2(b) in Algorithm 14.3 requires optimizing (14.92) with respect to A, holding the  $\hat{g}_j$  fixed. Only the first terms in the sum involve A, and since **A** is orthogonal, the collection of terms involving  $\phi$  do not depend on A (Exercise 14.19). Hence we need to maximize

C(\mathbf{A}) = \frac{1}{N} \sum\_{j=1}^{p} \sum\_{i=1}^{N} \hat{g}\_j(a\_j^T x\_i) \quad (14.95)

= \sum\_{j=1}^{p} C\_j(a\_j)

 $C(A)$  is a log-likelihood ratio between the fitted density and a Gaussian, and can be seen as an estimate of negentropy (14.86), with each  $\hat{g}_i$  a contrast function as in  $(14.87)$ . The fixed point update in step  $2(b)$  is a modified Newton step (Exercise 14.20)

1. For each  $i$  update

$$
a_j \leftarrow \mathbf{E} \left\{ X \hat{g}'_j (a_j^T X) - \mathbf{E} [\hat{g}''_j (a_j^T X)] a_j \right\},\tag{14.96}
$$

where E represents expectation w.r.t the sample  $x_i$ . Since  $\hat{g}_j$  is a fitted quartic (or cubic) spline, the first and second derivatives are readily available.

2. Orthogonalize A using the symmetric square-root transformation  $(AA^T)^{-\frac{1}{2}}A$ . If  $A = UDV^T$  is the SVD of A, it is easy to show that this leads to the update  $\mathbf{A} \leftarrow \mathbf{U}\mathbf{V}^T$ .

Our ProDenICA algorithm works as well as FastICA on the artificial time series data of Figure 14.37, the mixture of uniforms data of Figure 14.38, and the digit data in Figure 14.39.

### Example: Simulations

Image /page/64/Figure/7 description: The image displays a grid of 18 subplots labeled 'a' through 'r' on the left, each showing a different probability distribution. To the right of the grid is a line graph with error bars. The x-axis of the line graph is labeled 'Distribution' and shows the labels 'a' through 'r'. The y-axis is labeled 'Amari Distance from True A' and has tick marks at 0.01, 0.02, 0.05, 0.10, 0.20, and 0.50. Three lines are plotted on the graph, representing 'FastICA' (yellow), 'KernelICA' (blue), and 'ProdDenICA' (green). The lines show the Amari distance for each distribution, with error bars indicating variability.

FIGURE 14.42. The left panel shows 18 distributions used for comparisons. These include the "t", uniform, exponential, mixtures of exponentials, symmetric and asymmetric Gaussian mixtures. The right panel shows (on the log scale) the average Amari metric for each method and each distribution, based on 30 simulations in  $\mathbb{R}^2$  for each distribution.

Figure 14.42 shows the results of a simulation comparing ProDenICA to FastICA, and another semi-parametric competitor KernelICA (Bach and Jordan, 2002). The left panel shows the 18 distributions used as a basis of comparison. For each distribution, we generated a pair of independent components ( $N = 1024$ ), and a random mixing matrix in  $\mathbb{R}^2$  with condition number between 1 and 2. We used our R implementations of FastICA, using the negentropy criterion (14.87), and ProDenICA. For KernelICA we used

# 570 14. Unsupervised Learning

the authors MATLAB code.<sup>12</sup> Since the search criteria are nonconvex, we used five random starts for each method. Each of the algorithms delivers an orthogonal mixing matrix  $A$  (the data were *pre-whitened*), which is available for comparison with the generating orthogonalized mixing matrix  $A_0$ . We used the Amari metric (Bach and Jordan, 2002) as a measure of the closeness of the two frames:

$$
d(\mathbf{A}_0, \mathbf{A}) = \frac{1}{2p} \sum_{i=1}^p \left( \frac{\sum_{j=1}^p |r_{ij}|}{\max_j |r_{ij}|} - 1 \right) + \frac{1}{2p} \sum_{j=1}^p \left( \frac{\sum_{i=1}^p |r_{ij}|}{\max_i |r_{ij}|} - 1 \right), \tag{14.97}
$$

where  $r_{ij} = (\mathbf{A}_o \mathbf{A}^{-1})_{ij}$ . The right panel in Figure 14.42 compares the averages (on the log scale) of the Amari metric between the truth and the estimated mixing matrices. ProDenICA is competitive with FastICA and KernelICA in all situations, and dominates most of the mixture simulations.

# 14.8 Multidimensional Scaling

Both self-organizing maps and principal curves and surfaces map data points in  $\mathbb{R}^p$  to a lower-dimensional manifold. Multidimensional scaling (MDS) has a similar goal, but approaches the problem in a somewhat different way.

We start with observations  $x_1, x_2, \ldots, x_N \in \mathbb{R}^p$ , and let  $d_{ij}$  be the distance between observations  $i$  and  $j$ . Often we choose Euclidean distance  $d_{ij} = ||x_i - x_j||$ , but other distances may be used. Further, in some applications we may not even have available the data points  $x_i$ , but only have some *dissimilarity* measure  $d_{ij}$  (see Section 14.3.10). For example, in a wine tasting experiment,  $d_{ij}$  might be a measure of how different a subject judged wines  $i$  and  $j$ , and the subject provides such a measure for all pairs of wines i, j. MDS requires only the dissimilarities  $d_{ij}$ , in contrast to the SOM and principal curves and surfaces which need the data points  $x_i$ .

Multidimensional scaling seeks values  $z_1, z_2, \ldots, z_N \in \mathbb{R}^k$  to minimize the so-called *stress* function<sup>13</sup>

$$
S_M(z_1, z_2, \dots, z_N) = \sum_{i \neq i'} (d_{ii'} - ||z_i - z_{i'}||)^2.
$$
 (14.98)

This is known as least squares or Kruskal–Shephard scaling. The idea is to find a lower-dimensional representation of the data that preserves the pairwise distances as well as possible. Notice that the approximation is

 $^{12}\mathrm{Francis}$  Bach kindly supplied this code, and helped us set up the simulations.

<sup>&</sup>lt;sup>13</sup>Some authors define stress as the square-root of  $S_M$ ; since it does not affect the optimization, we leave it squared to make comparisons with other criteria simpler.

in terms of the distances rather than squared distances (which results in slightly messier algebra). A gradient descent algorithm is used to minimize  $S_M$ .

A variation on least squares scaling is the so-called Sammon mapping which minimizes

$$
S_{Sm}(z_1, z_2, \dots, z_N) = \sum_{i \neq i'} \frac{(d_{ii'} - ||z_i - z_{i'}||)^2}{d_{ii'}}.
$$
 (14.99)

Here more emphasis is put on preserving smaller pairwise distances.

In classical scaling, we instead start with similarities  $s_{ii'}$ : often we use the centered inner product  $s_{ii'} = \langle x_i - \bar{x}, x_{i'} - \bar{x} \rangle$ . The problem then is to minimize

$$
S_C(z_1, z_2, \dots, z_N) = \sum_{i,i'} (s_{ii'} - \langle z_i - \bar{z}, z_{i'} - \bar{z} \rangle)^2
$$
(14.100)

over  $z_1, z_2, \ldots, z_N \in \mathbb{R}^k$ . This is attractive because there is an explicit solution in terms of eigenvectors: see Exercise 14.11. If we have distances rather than inner-products, we can convert them to centered inner-products if the distances are  $Euclidean$ <sup>14</sup>, see (18.31) on page 671 in Chapter 18. If the similarities are in fact centered inner-products, classical scaling is exactly equivalent to principal components, an inherently linear dimensionreduction technique. Classical scaling is not equivalent to least squares scaling; the loss functions are different, and the mapping can be nonlinear.

Least squares and classical scaling are referred to as metric scaling methods, in the sense that the actual dissimilarities or similarities are approximated. Shephard–Kruskal nonmetric scaling effectively uses only ranks. Nonmetric scaling seeks to minimize the stress function

$$
S_{NM}(z_1, z_2, \dots, z_N) = \frac{\sum_{i \neq i'} \left[ ||z_i - z_{i'}|| - \theta(d_{ii'}) \right]^2}{\sum_{i \neq i'} ||z_i - z_{i'}||^2}
$$
(14.101)

over the  $z_i$  and an arbitrary increasing function  $\theta$ . With  $\theta$  fixed, we minimize over  $z_i$  by gradient descent. With the  $z_i$  fixed, the method of isotonic regression is used to find the best monotonic approximation  $\theta(d_{ii'})$ to  $||z_i - z_{i'}||$ . These steps are iterated until the solutions stabilize.

Like the self-organizing map and principal surfaces, multidimensional scaling represents high-dimensional data in a low-dimensional coordinate system. Principal surfaces and SOMs go a step further, and approximate the original data by a low-dimensional manifold, parametrized in the low dimensional coordinate system. In a principal surface and SOM, points

<sup>&</sup>lt;sup>14</sup>An  $N \times N$  distance matrix is Euclidean if the entries represent pairwise Euclidean distances between N points in some dimensional space.

Image /page/67/Figure/1 description: This is a scatter plot showing three clusters of data points. The x-axis is labeled "First MDS Coordinate" and ranges from -1.0 to 1.0. The y-axis is labeled "Second MDS Coordinate" and also ranges from -1.0 to 1.0. There are three distinct clusters of points: a cluster of red points on the left side of the plot, a cluster of blue points in the upper central to upper right portion of the plot, and a cluster of green points in the lower central to lower right portion of the plot. The red cluster is centered around x = -0.7 and y = 0.0. The blue cluster is centered around x = 0.2 and y = 0.8. The green cluster is centered around x = 0.6 and y = -0.4.

FIGURE 14.43. First two coordinates for half-sphere data, from classical multidimensional scaling.

close together in the original feature space should map close together on the manifold, but points far apart in feature space might also map close together. This is less likely in multidimensional scaling since it explicitly tries to preserve all pairwise distances.

Figure 14.43 shows the first two MDS coordinates from classical scaling for the half-sphere example. There is clear separation of the clusters, and the tighter nature of the red cluster is apparent.

# 14.9 Nonlinear Dimension Reduction and Local Multidimensional Scaling

Several methods have been recently proposed for nonlinear dimension reduction, similar in spirit to principal surfaces. The idea is that the data lie close to an intrinsically low-dimensional nonlinear manifold embedded in a high-dimensional space. These methods can be thought of as "flattening" the manifold, and hence reducing the data to a set of low-dimensional coordinates that represent their relative positions in the manifold. They are useful for problems where signal-to-noise ratio is very high (e.g., physical systems), and are probably not as useful for observational data with lower signal-to-noise ratios.

The basic goal is illustrated in the left panel of Figure 14.44. The data lie near a parabola with substantial curvature. Classical MDS does not pre-

Image /page/68/Figure/1 description: The image displays two scatter plots side-by-side, both illustrating dimensionality reduction techniques. The left plot is titled 'Classical MDS' and the right plot is titled 'Local MDS'. Both plots have 'x1' on the horizontal axis and 'x2' on the vertical axis, with tick marks ranging from -5 to 5 for x1 and 0 to -15 for x2. In both plots, there are two sets of points: a cluster of blue points along the bottom (around x2 = -15) and a curve of orange points arching upwards. Lines connect some of the orange points to each other, forming a yellow curve, and also connect some orange points to the blue points. The 'Classical MDS' plot shows more crisscrossing lines between the orange points and the blue points, suggesting a more complex relationship or distortion. The 'Local MDS' plot shows straighter, more vertical lines connecting the orange points to the blue points, indicating a simpler or more localized representation.

FIGURE 14.44. The orange points show data lying on a parabola, while the blue points shows multidimensional scaling representations in one dimension. Classical multidimensional scaling (left panel) does not preserve the ordering of the points along the curve, because it judges points on opposite ends of the curve to be close together. In contrast, local multidimensional scaling (right panel) does a good job of preserving the ordering of the points along the curve.

serve the ordering of the points along the curve, because it judges points on opposite ends of the curve to be close together. The right panel shows the results of local multi-dimensional scaling, one of the three methods for non-linear multi-dimensional scaling that we discuss below. These methods use only the coordinates of the points in  $p$  dimensions, and have no other information about the manifold. Local MDS has done a good job of preserving the ordering of the points along the curve.

We now briefly describe three new approaches to nonlinear dimension reduction and manifold mapping.

Isometric feature mapping (ISOMAP) (Tenenbaum et al., 2000) constructs a graph to approximate the geodesic distance between points along the manifold. Specifically, for each data point we find its neighbors—points within some small Euclidean distance of that point. We construct a graph with an edge between any two neighboring points. The geodesic distance between any two points is then approximated by the shortest path between points on the graph. Finally, classical scaling is applied to the graph distances, to produce a low-dimensional mapping.

Local linear embedding (Roweis and Saul, 2000) takes a very different approach, trying to preserve the local affine structure of the high-dimensional data. Each data point is approximated by a linear combination of neighboring points. Then a lower dimensional representation is constructed that

### 574 14. Unsupervised Learning

best preserves these local approximations. The details are interesting, so we give them here.

- 1. For each data point  $x_i$  in  $p$  dimensions, we find its  $K$ -nearest neighbors  $\mathcal{N}(i)$  in Euclidean distance.
- 2. We approximate each point by an affine mixture of the points in its neighborhood:

$$
\min_{W_{ik}} ||x_i - \sum_{k \in \mathcal{N}(i)} w_{ik} x_k||^2 \tag{14.102}
$$

over weights  $w_{ik}$  satisfying  $w_{ik} = 0, k \notin \mathcal{N}(i), \sum_{k=1}^{N} w_{ik} = 1$ .  $w_{ik}$ is the contribution of point  $k$  to the reconstruction of point  $i$ . Note that for a hope of a unique solution, we must have  $K < p$ .

3. Finally, we find points  $y_i$  in a space of dimension  $d < p$  to minimize

$$
\sum_{i=1}^{N} ||y_i - \sum_{k=1}^{N} w_{ik} y_k||^2
$$
 (14.103)

with  $w_{ik}$  fixed.

In step 3, we minimize

$$
\text{tr}[(\mathbf{Y} - \mathbf{W}\mathbf{Y})^T(\mathbf{Y} - \mathbf{W}\mathbf{Y})] = \text{tr}[\mathbf{Y}^T(\mathbf{I} - \mathbf{W})^T(\mathbf{I} - \mathbf{W})\mathbf{Y}] \qquad (14.104)
$$

where **W** is  $N \times N$ ; **Y** is  $N \times d$ , for some small  $d < p$ . The solutions  $\hat{Y}$ are the trailing eigenvectors of  $\mathbf{M} = (\mathbf{I} - \mathbf{W})^T (\mathbf{I} - \mathbf{W})$ . Since 1 is a trivial eigenvector with eigenvalue  $0$ , we discard it and keep the next  $d$ . This has the side effect that  $\mathbf{1}^T \mathbf{Y} = 0$ , and hence the embedding coordinates are mean centered.

Local MDS (Chen and Buja, 2008) takes the simplest and arguably the most direct approach. We define  $\mathcal N$  to be the symmetric set of nearby pairs of points; specifically a pair  $(i, i')$  is in  $\mathcal N$  if point  $i$  is among the K-nearest neighbors of i', or vice-versa. Then we construct the stress function

$$
S_L(z_1, z_2, ..., z_N) = \sum_{(i,i') \in \mathcal{N}} (d_{ii'} - ||z_i - z_{i'}||)^2 + \sum_{(i,i') \notin \mathcal{N}} w \cdot (D - ||z_i - z_{i'}||)^2. (14.105)
$$

Here  $D$  is some large constant and  $w$  is a weight. The idea is that points that are not neighbors are considered to be very far apart; such pairs are given a small weight  $w$  so that they don't dominate the overall stress function. To simplify the expression, we take  $w \sim 1/D$ , and let  $D \to \infty$ . Expanding (14.105), this gives

Image /page/70/Figure/1 description: This figure displays a scatter plot of faces mapped into an embedding space. Numerous blue dots represent individual faces, with some faces highlighted by small inset images and blue circles. A red line connects a subset of these faces, illustrating a path or progression through the embedding space. The faces shown exhibit variations in expression and orientation. Below the scatter plot, a row of small, grayscale images of faces is presented, likely representing the data points in the embedding space.

FIGURE 14.45. Images of faces mapped into the embedding space described by the first two coordinates of LLE. Next to the circled points, representative faces are shown in different parts of the space. The images at the bottom of the plot correspond to points along the top right path (linked by solid line), and illustrate one particular mode of variability in pose and expression.

576 14. Unsupervised Learning

$$
S_L(z_1, z_2, \dots, z_N) = \sum_{(i,i') \in \mathcal{N}} (d_{ii'} - ||z_i - z_{i'}||)^2 - \tau \sum_{(i,i') \notin \mathcal{N}} ||z_i - z_{i'}||,
$$
\n(14.106)

where  $\tau = 2wD$ . The first term in (14.106) tries to preserve local structure in the data, while the second term encourages the representations  $z_i, z_{i'}$ for pairs  $(i, i')$  that are non-neighbors to be farther apart. Local MDS minimizes the stress function  $(14.106)$  over  $z_i$ , for fixed values of the number of neighbors K and the tuning parameter  $\tau$ .

The right panel of Figure 14.44 shows the result of local MDS, using  $k = 2$ neighbors and  $\tau = 0.01$ . We used coordinate descent with multiple starting values to find a good minimum of the (nonconvex) stress function (14.106). The ordering of the points along the curve has been largely preserved,

Figure 14.45 shows a more interesting application of one of these methods (LLE)<sup>15</sup>. The data consist of 1965 photographs, digitized as  $20 \times 28$ grayscale images. The result of the first two-coordinates of LLE are shown and reveal some variability in pose and expression. Similar pictures were produced by local MDS.

In experiments reported in Chen and Buja (2008), local MDS shows superior performance, as compared to ISOMAP and LLE. They also demonstrate the usefulness of local MDS for graph layout. There are also close connections between the methods discussed here, spectral clustering (Section 14.5.3) and kernel PCA (Section 14.5.4).

# 14.10 The Google PageRank Algorithm

In this section we give a brief description of the original *PageRank* algorithm used by the Google search engine, an interesting recent application of unsupervised learning methods.

We suppose that we have  $N$  web pages and wish to rank them in terms of importance. For example, the  $N$  pages might all contain a string match to "statistical learning" and we might wish to rank the pages in terms of their likely relevance to a websurfer.

The *PageRank* algorithm considers a webpage to be important if many other webpages point to it. However the linking webpages that point to a given page are not treated equally: the algorithm also takes into account both the importance (PageRank) of the linking pages and the number of outgoing links that they have. Linking pages with higher PageRank are given more weight, while pages with more outgoing links are given less weight. These ideas lead to a recursive definition for *PageRank*, detailed next.

<sup>15</sup>Sam Roweis and Lawrence Saul kindly provided this figure.

Let  $L_{ij} = 1$  if page j points to page i, and zero otherwise. Let  $c_j =$  $\sum_{i=1}^{N} L_{ij}$  equal the number of pages pointed to by page j (number of outlinks). Then the Google *PageRanks*  $p_i$  are defined by the recursive relationship

$$
p_i = (1 - d) + d \sum_{j=1}^{N} \left(\frac{L_{ij}}{c_j}\right) p_j \tag{14.107}
$$

where  $d$  is a positive constant (apparently set to 0.85).

The idea is that the importance of page  $i$  is the sum of the importances of pages that point to that page. The sums are weighted by  $1/c_j$ , that is, each page distributes a total vote of 1 to other pages. The constant  $d$  ensures that each page gets a  $PageRank$  of at least  $1-d$ . In matrix notation

$$
\mathbf{p} = (1 - d)\mathbf{e} + d \cdot \mathbf{L} \mathbf{D}_c^{-1} \mathbf{p}
$$
 (14.108)

where **e** is a vector of N ones and  $D_c = \text{diag}(c)$  is a diagonal matrix with diagonal elements  $c_j$ . Introducing the normalization  $e^T \mathbf{p} = N$  (i.e., the average *PageRank* is 1), we can write  $(14.108)$  as

$$
\mathbf{p} = [(1-d)\mathbf{e}\mathbf{e}^T/N + d\mathbf{L}\mathbf{D}_c^{-1}]\mathbf{p}
$$
  
=  $\mathbf{A}\mathbf{p}$  (14.109)

where the matrix **A** is the expression in square braces.

Exploiting a connection with Markov chains (see below), it can be shown that the matrix  $\bf{A}$  has a real eigenvalue equal to one, and one is its largest eigenvalue. This means that we can find  $\hat{\mathbf{p}}$  by the power method: starting with some  $\mathbf{p} = \mathbf{p}_0$  we iterate

$$
\mathbf{p}_k \leftarrow \mathbf{A} \mathbf{p}_{k-1}; \quad \mathbf{p}_k \leftarrow N \frac{\mathbf{p}_k}{\mathbf{e}^T \mathbf{p}_k}.
$$
 (14.110)

The fixed points  $\hat{\mathbf{p}}$  are the desired *PageRanks*.

In the original paper of Page et al. (1998), the authors considered PageRank as a model of user behavior, where a random web surfer clicks on links at random, without regard to content. The surfer does a random walk on the web, choosing among available outgoing links at random. The factor  $1 - d$  is the probability that he does not click on a link, but jumps instead to a random webpage.

Some descriptions of *PageRank* have  $(1 - d)/N$  as the first term in definition (14.107), which would better coincide with the random surfer interpretation. Then the page rank solution (divided by  $N$ ) is the stationary distribution of an irreducible, aperiodic Markov chain over the N webpages.

Definition (14.107) also corresponds to an irreducible, aperiodic Markov chain, with different transition probabilities than those from he  $(1 - d)/N$ version. Viewing PageRank as a Markov chain makes clear why the matrix A has a maximal real eigenvalue of 1. Since A has positive entries with

### 578 14. Unsupervised Learning

Image /page/73/Figure/1 description: The image displays a diagram illustrating the navigation flow between four web pages, labeled Page 1, Page 2, Page 3, and Page 4. Blue arrows indicate the direction of the links. Page 1 links to Page 2. Page 2 links to Page 3. Page 3 links to Page 1. Page 4 links to Page 3. The pages themselves show different website layouts and content, suggesting a user journey through a website.

FIGURE 14.46. PageRank algorithm: example of a small network

each column summing to one, Markov chain theory tells us that it has a unique eigenvector with eigenvalue one, corresponding to the stationary distribution of the chain (Bremaud, 1999).

A small network is shown for illustration in Figure 14.46. The link matrix is

$$
\mathbf{L} = \begin{pmatrix} 0 & 0 & 1 & 0 \ 1 & 0 & 0 & 0 \ 1 & 1 & 0 & 1 \ 0 & 0 & 0 & 0 \end{pmatrix} (14.111)
$$

and the number of outlinks is  $\mathbf{c} = (2, 1, 1, 1).$ 

The *PageRank* solution is  $\hat{\mathbf{p}} = (1.49, 0.78, 1.58, 0.15)$ . Notice that page 4 has no incoming links, and hence gets the minimum *PageRank* of 0.15.

# Bibliographic Notes

There are many books on clustering, including Hartigan (1975), Gordon (1999) and Kaufman and Rousseeuw (1990). K-means clustering goes back at least to Lloyd (1957), Forgy (1965), Jancey (1966) and MacQueen (1967). Applications in engineering, especially in image compression via vector quantization, can be found in Gersho and Gray (1992). The k-medoid procedure is described in Kaufman and Rousseeuw (1990). Association rules are outlined in Agrawal et al. (1995). The self-organizing map was proposed by Kohonen (1989) and Kohonen (1990); Kohonen et al. (2000) give a more recent account. Principal components analysis and multidimensional scaling are described in standard books on multivariate analysis, for example, Mardia et al. (1979). Buja et al. (2008) have implemented a powerful environment called Ggvis for multidimensional scaling, and the user manual contains a lucid overview of the subject. Figures 14.17, 14.21 (left panel) and 14.28 (left panel) were produced in Xgobi, a multidimensional data visualization package by the same authors. GGobi is a more recent implementation (Cook and Swayne, 2007). Goodall (1991) gives a technical overview of Procrustes methods in statistics, and Ramsay and Silverman (1997) discuss the shape registration problem. Principal curves and surfaces were proposed in Hastie (1984) and Hastie and Stuetzle (1989). The idea of principal points was formulated in Flury (1990), Tarpey and Flury (1996) give an exposition of the general concept of self-consistency. An excellent tutorial on spectral clustering can be found in von Luxburg (2007); this was the main source for Section 14.5.3. Luxborg credits Donath and Hoffman (1973) and Fiedler (1973) with the earliest work on the subject. A history of spectral clustering my be found in Spielman and Teng (1996). Independent component analysis was proposed by Comon (1994), with subsequent developments by Bell and Sejnowski (1995); our treatment in Section 14.7 is based on Hyvärinen and Oja  $(2000)$ . Projection pursuit was proposed by Friedman and Tukey (1974), and is discussed in detail in Huber (1985). A dynamic projection pursuit algorithm is implemented in GGobi.

# Exercises

Ex. 14.1 Weights for clustering. Show that weighted Euclidean distance

$$
d_e^{(w)}(x_i, x_{i'}) = \frac{\sum_{l=1}^p w_l (x_{il} - x_{i'l})^2}{\sum_{l=1}^p w_l}
$$

satisfies

$$
d_e^{(w)}(x_i, x_{i'}) = d_e(z_i, z_{i'}) = \sum_{l=1}^p (z_{il} - z_{i'l})^2,
$$
\n(14.112)

where

$$
z_{il} = x_{il} \cdot \left(\frac{w_l}{\sum_{l=1}^{p} w_l}\right)^{1/2}.
$$
 (14.113)

Thus weighted Euclidean distance based on  $x$  is equivalent to unweighted Euclidean distance based on z.

Ex. 14.2 Consider a mixture model density in p-dimensional feature space,

$$
g(x) = \sum_{k=1}^{K} \pi_k g_k(x),
$$
 (14.114)

where  $g_k = N(\mu_k, \mathbf{L} \cdot \sigma^2)$  and  $\pi_k \geq 0$   $\forall k$  with  $\sum_k \pi_k = 1$ . Here  $\{\mu_k, \pi_k\}, k =$  $1, \ldots, K$  and  $\sigma^2$  are unknown parameters.

# 580 14. Unsupervised Learning

Suppose we have data  $x_1, x_2, \ldots, x_N \sim g(x)$  and we wish to fit the mixture model.

- 1. Write down the log-likelihood of the data
- 2. Derive an EM algorithm for computing the maximum likelihood estimates (see Section 8.1).
- 3. Show that if  $\sigma$  has a known value in the mixture model and we take  $\sigma \to 0$ , then in a sense this EM algorithm coincides with K-means clustering.

Ex. 14.3 In Section 14.2.6 we discuss the use of CART or PRIM for constructing generalized association rules. Show that a problem occurs with either of these methods when we generate the random data from the productmarginal distribution; i.e., by randomly permuting the values for each of the variables. Propose ways to overcome this problem.

Ex. 14.4 Cluster the demographic data of Table 14.1 using a classification tree. Specifically, generate a reference sample of the same size of the training set, by randomly permuting the values within each feature. Build a classification tree to the training sample (class 1) and the reference sample (class 0) and describe the terminal nodes having highest estimated class 1 probability. Compare the results to the PRIM results near Table 14.1 and also to the results of K-means clustering applied to the same data.

Ex. 14.5 Generate data with three features, with 30 data points in each of three classes as follows:

$$
\theta_1 = U(-\pi/8, \pi/8)
$$
  

$$
\phi_1 = U(0, 2\pi)
$$
  

$$
x_1 = \sin(\theta_1)\cos(\phi_1) + W_{11}
$$
  

$$
y_1 = \sin(\theta_1)\sin(\phi_1) + W_{12}
$$
  

$$
z_1 = \cos(\theta_1) + W_{13}
$$
  

$$
\theta_2 = U(\pi/2 - \pi/4, \pi/2 + \pi/4)
$$
  

$$
\phi_2 = U(-\pi/4, \pi/4)
$$
  

$$
x_2 = \sin(\theta_2)\cos(\phi_2) + W_{21}
$$
  

$$
y_2 = \sin(\theta_2)\sin(\phi_2) + W_{22}
$$
  

$$
z_2 = \cos(\theta_2) + W_{23}
$$
  

$$
\theta_3 = U(\pi/2 - \pi/4, \pi/2 + \pi/4)
$$
  

$$
\phi_3 = U(\pi/2 - \pi/4, \pi/2 + \pi/4)
$$
  

$$
x_3 = \sin(\theta_3)\cos(\phi_3) + W_{31}
$$
  

$$
y_3 = \sin(\theta_3)\sin(\phi_3) + W_{32}
$$
  

$$
z_3 = \cos(\theta_3) + W_{33}
$$

Here  $U(a, b)$  indicates a uniform variate on the range  $[a, b]$  and  $W_{jk}$  are independent normal variates with standard deviation 0.6. Hence the data lie near the surface of a sphere in three clusters centered at  $(1, 0, 0)$ ,  $(0, 1, 0)$ and (0, 0, 1).

Write a program to fit a SOM to these data, using the learning rates given in the text. Carry out a K-means clustering of the same data, and compare the results to those in the text.

Ex. 14.6 Write programs to implement K-means clustering and a selforganizing map (SOM), with the prototype lying on a two-dimensional grid. Apply them to the columns of the human tumor microarray data, using  $K = 2, 5, 10, 20$  centroids for both. Demonstrate that as the size of the SOM neighborhood is taken to be smaller and smaller, the SOM solution becomes more similar to the K-means solution.

Ex. 14.7 Derive (14.51) and (14.52) in Section 14.5.1. Show that  $\hat{\mu}$  is not unique, and characterize the family of equivalent solutions.

Ex. 14.8 Derive the solution (14.57) to the Procrustes problem (14.56). Derive also the solution to the Procrustes problem with scaling (14.58).

Ex. 14.9 Write an algorithm to solve

$$
\min_{\{\beta_{\ell}, \mathbf{R}_{\ell}\}_{1}^{L}, \mathbf{M}} \sum_{\ell=1}^{L} ||\mathbf{X}_{\ell} \mathbf{R}_{\ell} - \mathbf{M}||_{F}^{2}.
$$
 (14.115)

Apply it to the three S's, and compare the results to those shown in Figure 14.26.

Ex. 14.10 Derive the solution to the affine-invariant average problem (14.60). Apply it to the three S's, and compare the results to those computed in Exercise 14.9.

Ex. 14.11 Classical multidimensional scaling. Let S be the centered inner product matrix with elements  $\langle x_i - \bar{x}, x_j - \bar{x} \rangle$ . Let  $\lambda_1 > \lambda_2 > \cdots >$  $\lambda_k$  be the k largest eigenvalues of **S**, with associated eigenvectors  $\mathbf{E}_k =$  $(e_1, e_2, \ldots, e_k)$ . Let  $D_k$  be a diagonal matrix with diagonal entries  $\sqrt{\lambda_1}$ ,  $\overline{\lambda_2}, \ldots, \overline{\lambda_k}$ . Show that the solutions  $z_i$  to the classical scaling problem  $(14.100)$  are the *rows* of  $\mathbf{E}_k \mathbf{D}_k$ .

Ex. 14.12 Consider the sparse PCA criterion (14.71).

- 1. Show that with  $\Theta$  fixed, solving for **V** amounts to K separate elasticnet regression problems, with responses the K elements of  $\Theta^T x_i$ .
- 2. Show that with V fixed, solving for  $\Theta$  amounts to a reduced-rank version of the Procrustes problem, which reduces to

$$
\max_{\Theta} \text{trace}(\Theta^T \mathbf{M}) \text{ subject to } \Theta^T \Theta = \mathbf{I}_K, \tag{14.116}
$$

where **M** and  $\Theta$  are both  $p \times K$  with  $K \leq p$ . If  $\mathbf{M} = \mathbf{U} \mathbf{D} \mathbf{Q}^T$  is the SVD of M, show that the optimal  $\mathbf{\Theta} = \mathbf{U} \mathbf{Q}^T$ .

### 582 14. Unsupervised Learning

Ex. 14.13 Generate 200 data points with three features, lying close to a helix. In detail, define  $X_1 = \cos(s) + 0.1 \cdot Z_1, X_2 = \sin(s) + 0.1 \cdot Z_2, X_3 =$  $s + 0.1 \cdot Z_3$  where s takes on 200 equally spaced values between 0 and  $2\pi$ , and  $Z_1, Z_2, Z_3$  are independent and have standard Gaussian distributions.

- (a) Fit a principal curve to the data and plot the estimated coordinate functions. Compare them to the underlying functions  $cos(s), sin(s)$ and s.
- (b) Fit a self-organizing map to the same data, and see if you can discover the helical shape of the original point cloud.

Ex. 14.14 Pre- and post-multiply equation (14.81) by a diagonal matrix containing the inverse variances of the  $X_i$ . Hence obtain an equivalent decomposition for the correlation matrix, in the sense that a simple scaling is applied to the matrix A.

Ex. 14.15 Generate 200 observations of three variates  $X_1, X_2, X_3$  according to

$$
X_1 \sim Z_1\nX_2 = X_1 + 0.001 \cdot Z_2\nX_3 = 10 \cdot Z_3
$$
\n(14.117)

where  $Z_1, Z_2, Z_3$  are independent standard normal variates. Compute the leading principal component and factor analysis directions. Hence show that the leading principal component aligns itself in the maximal variance direction  $X_3$ , while the leading factor essentially ignores the uncorrelated component  $X_3$ , and picks up the correlated component  $X_2 + X_1$  (Geoffrey Hinton, personal communication).

Ex. 14.16 Consider the kernel principal component procedure outlined in Section 14.5.4. Argue that the number  $M$  of principal components is equal to the rank of  $K$ , which is the number of non-zero elements in  $D$ . Show that the mth component  $z_m$  (mth column of Z) can be written (up to centering) as  $z_{im} = \sum_{j=1}^{N} \alpha_{jm} K(x_i, x_j)$ , where  $\alpha_{jm} = u_{jm}/d_m$ . Show that the mapping of a new observation  $x_0$  to the mth component is given by  $z_{0m} = \sum_{j=1}^{N} \alpha_{jm} K(x_0, x_j).$ 

Ex. 14.17 Show that with  $g_1(x) = \sum_{j=1}^{N} c_j K(x, x_j)$ , the solution to (14.66) is given by  $\hat{c}_j = u_{j1}/d_1$ , where  $\mathbf{u}_1$  is the first column of U in (14.65), and  $d_1$  the first diagonal element of  $D$ . Show that the second and subsequent principal component functions are defined in a similar manner (hint: see Section 5.8.1.)

Ex. 14.18 Consider the regularized log-likelihood for the density estimation problem arising in ICA,

Exercises 583

$$
\frac{1}{N} \sum_{i=1}^{N} \left[ \log \phi(s_i) + g(s_i) \right] - \int \phi(t) e^{g(t)} dt - \lambda \int \{g'''(t)\}^2(t) dt. \quad (14.118)
$$

The solution  $\hat{q}$  is a quartic smoothing spline, and can be written as  $\hat{q}(s)$  =  $\hat{q}(s) + \hat{q}_{\perp}(s)$ , where q is a quadratic function (in the null space of the penalty). Let  $q(s) = \theta_0 + \theta_1 s + \theta_2 s^2$ . By examining the stationarity conditions for  $\hat{\theta}_k$ ,  $k = 1, 2, 3$ , show that the solution  $\hat{f} = \phi e^{\hat{g}}$  is a density, and has mean zero and variance one. If we used a second-derivative penalty  $\int {g''(t)}^2(t)dt$  instead, what simple modification could we make to the problem to maintain the three moment conditions?

Ex. 14.19 If **A** is  $p \times p$  orthogonal, show that the first term in (14.92) on page 568

$$
\sum_{j=1}^{p} \sum_{i=1}^{N} \log \phi(a_j^T x_i),
$$

with  $a_i$  the j<sup>th</sup> column of **A**, does not depend on **A**.

Ex. 14.20 Fixed point algorithm for ICA (Hyvärinen et al., 2001). Consider maximizing  $C(a) = E\{g(a^T X)\}\$  with respect to a, with  $||a|| = 1$  and  $Cov(X) = I$ . Use a Lagrange multiplier to enforce the norm constraint, and write down the first two derivatives of the modified criterion. Use the approximation

$$
E\{XX^Tg''(a^TX)\}\approx E\{XX^T\}E\{g''(a^TX)\}
$$

to show that the Newton update can be written as the fixed-point update  $(14.96).$ 

Ex. 14.21 Consider an undirected graph with non-negative edge weights  $w_{ii'}$  and graph Laplacian L. Suppose there are m connected components  $A_1, A_2, \ldots, A_m$  in the graph. Show that there are m eigenvectors of **L** corresponding to eigenvalue zero, and the indicator vectors of these components  $I_{A_1}, I_{A_2}, \ldots, I_{A_m}$  span the zero eigenspace.

## Ex. 14.22

- (a) Show that definition (14.108) implies that the sum of the PageRanks  $p_i$  is N, the number of web pages.
- (b) Write a program to compute the PageRank solutions by the power method using formulation (14.107). Apply it to the network of Figure 14.47.

Ex. 14.23 Algorithm for non-negative matrix factorization (Wu and Lange, 2007). A function  $q(x, y)$  to said to *minorize* a function  $f(x)$  if

# 584 14. Unsupervised Learning

Image /page/79/Figure/1 description: This is a diagram showing the relationships between six pages. Page 1 has outgoing arrows to Page 2 and Page 3, and an incoming arrow from Page 4. Page 2 has an incoming arrow from Page 1 and an outgoing arrow to Page 3. Page 3 has incoming arrows from Page 1 and Page 2, and outgoing arrows to Page 4 and Page 5. Page 4 has an outgoing arrow to Page 1 and an incoming arrow from Page 3. Page 5 has an outgoing arrow to Page 3 and an incoming arrow from Page 3. Page 6 has incoming arrows from Page 3 and Page 5, and an outgoing arrow to Page 2.

FIGURE 14.47. Example of a small network.

$$
g(x, y) \le f(x), \ \ g(x, x) = f(x) \tag{14.119}
$$

for all  $x, y$  in the domain. This is useful for maximizing  $f(x)$  since it is easy to show that  $f(x)$  is nondecreasing under the update

$$
x^{s+1} = \operatorname{argmax}_{x} g(x, x^s)
$$
\n
$$
(14.120)
$$

There are analogous definitions for majorization, for minimizing a function  $f(x)$ . The resulting algorithms are known as MM algorithms, for "minorizemaximize" or "majorize-minimize" (Lange, 2004). It also can be shown that the EM algorithm (8.5) is an example of an MM algorithm: see Section 8.5.3 and Exercise 8.2 for details.

(a) Consider maximization of the function  $L(W, H)$  in (14.73), written here without the matrix notation

$$
L(\mathbf{W}, \mathbf{H}) = \sum_{i=1}^{N} \sum_{j=1}^{p} \left[ x_{ij} \log \left( \sum_{k=1}^{r} w_{ik} h_{kj} \right) - \sum_{k=1}^{r} w_{ik} h_{kj} \right].
$$

Using the concavity of  $log(x)$ , show that for any set of r values  $y_k \ge 0$ and  $0 \leq c_k \leq 1$  with  $\sum_{k=1}^r c_k = 1$ ,

$$
\log\left(\sum_{k=1}^r y_k\right) \ge \sum_{k=1}^r c_k \log(y_k/c_k)
$$

Hence

$$
\log \left(\sum_{k=1}^r w_{ik} h_{kj}\right) \geq \sum_{k=1}^r \frac{a_{ikj}^s}{b_{ij}^s} \log \left(\frac{b_{ij}^s}{a_{ikj}^s} w_{ik} h_{kj}\right),
$$

where

$$
a_{ikj}^s = w_{ik}^s h_{kj}^s
$$
 and  $b_{ij}^s = \sum_{k=1}^r w_{ik}^s h_{kj}^s$ ,

and s indicates the current iteration.

(b) Hence show that, ignoring constants, the function

$$
g(\mathbf{W}, \mathbf{H} \mid \mathbf{W}^{s}, \mathbf{H}^{s}) = \sum_{i=1}^{N} \sum_{j=1}^{p} \sum_{k=1}^{r} u_{ij} \frac{a_{ikj}^{s}}{b_{ij}^{s}} \Big( \log w_{ik} + \log h_{kj} \Big) - \sum_{i=1}^{N} \sum_{j=1}^{p} \sum_{k=1}^{r} w_{ik} h_{kj}
$$

minorizes  $L(W, H)$ .

(c) Set the partial derivatives of  $g(\mathbf{W}, \mathbf{H} \mid \mathbf{W}^s, \mathbf{H}^s)$  to zero and hence derive the updating steps (14.74).

Ex. 14.24 Consider the non-negative matrix factorization (14.72) in the rank one case  $(r = 1)$ .

(a) Show that the updates (14.74) reduce to

$$
w_i \leftarrow w_i \frac{\sum_{j=1}^p x_{ij}}{\sum_{j=1}^p w_i h_j}
$$
  
\n
$$
h_j \leftarrow h_j \frac{\sum_{i=1}^N x_{ij}}{\sum_{i=1}^N w_i h_j}
$$
 (14.121)

where  $w_i = w_{i1}, h_j = h_{1j}$ . This is an example of the *iterative pro*portional scaling procedure, applied to the independence model for a two-way contingency table (Fienberg, 1977, for example).

(b) Show that the final iterates have the explicit form

$$
w_i = c \cdot \frac{\sum_{j=1}^p x_{ij}}{\sum_{i=1}^N \sum_{j=1}^p x_{ij}}, \qquad h_k = \frac{1}{c} \cdot \frac{\sum_{i=1}^N x_{ik}}{\sum_{i=1}^N \sum_{j=1}^p x_{ij}} \qquad (14.122)
$$

for any constant  $c > 0$ . These are equivalent to the usual row and column estimates for a two-way independence model.

Ex. 14.25 Fit a non-negative matrix factorization model to the collection of two's in the digits database. Use 25 basis elements, and compare with a 24- component (plus mean) PCA model. In both cases display the W and H matrices as in Figure 14.33.

586 14. Unsupervised Learning

This is page 587 Printer: Opaque this

# 15 Random Forests

# 15.1 Introduction

Bagging or bootstrap aggregation (section 8.7) is a technique for reducing the variance of an estimated prediction function. Bagging seems to work especially well for high-variance, low-bias procedures, such as trees. For regression, we simply fit the same regression tree many times to bootstrapsampled versions of the training data, and average the result. For classification, a committee of trees each cast a vote for the predicted class.

Boosting in Chapter 10 was initially proposed as a committee method as well, although unlike bagging, the committee of *weak learners* evolves over time, and the members cast a weighted vote. Boosting appears to dominate bagging on most problems, and became the preferred choice.

Random forests (Breiman, 2001) is a substantial modification of bagging that builds a large collection of de-correlated trees, and then averages them. On many problems the performance of random forests is very similar to boosting, and they are simpler to train and tune. As a consequence, random forests are popular, and are implemented in a variety of packages.

# 15.2 Definition of Random Forests

The essential idea in bagging (Section 8.7) is to average many noisy but approximately unbiased models, and hence reduce the variance. Trees are ideal candidates for bagging, since they can capture complex interaction Algorithm 15.1 Random Forest for Regression or Classification.

1. For  $b = 1$  to B:

- (a) Draw a bootstrap sample  $\mathbf{Z}^*$  of size N from the training data.
- (b) Grow a random-forest tree  $T<sub>b</sub>$  to the bootstrapped data, by recursively repeating the following steps for each terminal node of the tree, until the minimum node size  $n_{min}$  is reached.
  - i. Select  $m$  variables at random from the  $p$  variables.
  - ii. Pick the best variable/split-point among the  $m$ .
  - iii. Split the node into two daughter nodes.
- 2. Output the ensemble of trees  $\{T_b\}_1^B$ .

To make a prediction at a new point  $x$ :

Regression:  $\hat{f}_{\text{rf}}^B(x) = \frac{1}{B} \sum_{b=1}^B T_b(x)$ .

*Classification:* Let  $\hat{C}_b(x)$  be the class prediction of the bth random-forest tree. Then  $\hat{C}_{\text{rf}}^B(x) = \text{majority vote } \{\hat{C}_b(x)\}_1^B.$ 

structures in the data, and if grown sufficiently deep, have relatively low bias. Since trees are notoriously noisy, they benefit greatly from the averaging. Moreover, since each tree generated in bagging is identically distributed  $(i.d.)$ , the expectation of an average of  $B$  such trees is the same as the expectation of any one of them. This means the bias of bagged trees is the same as that of the individual trees, and the only hope of improvement is through variance reduction. This is in contrast to boosting, where the trees are grown in an adaptive way to remove bias, and hence are not i.d.

An average of B i.i.d. random variables, each with variance  $\sigma^2$ , has variance  $\frac{1}{B}\sigma^2$ . If the variables are simply i.d. (identically distributed, but not necessarily independent) with positive pairwise correlation  $\rho$ , the variance of the average is (Exercise 15.1)

$$
\rho \sigma^2 + \frac{1 - \rho}{B} \sigma^2. \tag{15.1}
$$

As B increases, the second term disappears, but the first remains, and hence the size of the correlation of pairs of bagged trees limits the benefits of averaging. The idea in random forests (Algorithm 15.1) is to improve the variance reduction of bagging by reducing the correlation between the trees, without increasing the variance too much. This is achieved in the tree-growing process through random selection of the input variables.

Specifically, when growing a tree on a bootstrapped dataset:

Before each split, select  $m \leq p$  of the input variables at random as candidates for splitting.

Typically values for m are  $\sqrt{p}$  or even as low as 1.

After B such trees  $\{T(x; \Theta_b)\}_1^B$  are grown, the random forest (regression) predictor is

$$
\hat{f}_{\rm rf}^B(x) = \frac{1}{B} \sum_{b=1}^B T(x; \Theta_b).
$$
 (15.2)

As in Section 10.9 (page 356),  $\Theta_b$  characterizes the bth random forest tree in terms of split variables, cutpoints at each node, and terminal-node values. Intuitively, reducing  $m$  will reduce the correlation between any pair of trees in the ensemble, and hence by (15.1) reduce the variance of the average.

Image /page/84/Figure/5 description: This is a line graph titled "Spam Data" that plots test error against the number of trees. The x-axis ranges from 0 to 2500, labeled "Number of Trees". The y-axis ranges from 0.040 to 0.070, labeled "Test Error". Three lines are plotted: "Bagging" (yellow), "Random Forest" (blue), and "Gradient Boosting (5 Node)" (green). The Bagging line is relatively flat around 0.054. The Random Forest line starts around 0.052, fluctuates, and ends around 0.049. The Gradient Boosting line starts high around 0.070, decreases sharply, and then fluctuates around 0.044, showing the best performance.

FIGURE 15.1. Bagging, random forest, and gradient boosting, applied to the spam data. For boosting, 5-node trees were used, and the number of trees were chosen by 10-fold cross-validation (2500 trees). Each "step" in the figure corresponds to a change in a single misclassification (in a test set of 1536).

Not all estimators can be improved by shaking up the data like this. It seems that highly nonlinear estimators, such as trees, benefit the most. For bootstrapped trees,  $\rho$  is typically small (0.05 or lower is typical; see Figure 15.9), while  $\sigma^2$  is not much larger than the variance for the original tree. On the other hand, bagging does not change linear estimates, such as the sample mean (hence its variance either); the pairwise correlation between bootstrapped means is about 50% (Exercise 15.4).

# 590 15. Random Forests

Random forests are popular. Leo Breiman's<sup>1</sup> collaborator Adele Cutler maintains a random forest website<sup>2</sup> where the software is freely available, with more than 3000 downloads reported by 2002. There is a randomForest package in R, maintained by Andy Liaw, available from the CRAN website.

The authors make grand claims about the success of random forests: "most accurate," "most interpretable," and the like. In our experience random forests do remarkably well, with very little tuning required. A random forest classifier achieves 4.88% misclassification error on the spam test data, which compares well with all other methods, and is not significantly worse than gradient boosting at 4.5%. Bagging achieves 5.4% which is significantly worse than either (using the McNemar test outlined in Exercise 10.6), so it appears on this example the additional randomization helps.

Image /page/85/Figure/3 description: This is a box plot titled "Nested Spheres". The y-axis is labeled "Test Misclassification Error" and ranges from 0.00 to 0.15. The x-axis shows five categories: RF-1, RF-3, Bagging, GBM-1, and GBM-6. A dashed red line labeled "Bayes Error" is present at the 0.00 mark on the y-axis. The box plots show the following approximate test misclassification errors: RF-1 has a median around 0.12, RF-3 has a median around 0.14, Bagging has a median around 0.15, GBM-1 has a median around 0.05, and GBM-6 has a median around 0.055. There are outlier points shown for RF-1 (around 0.10) and GBM-6 (around 0.065).

FIGURE 15.2. The results of 50 simulations from the "nested spheres" model in  $\mathbb{R}^{10}$ . The Bayes decision boundary is the surface of a sphere (additive). "RF-3" refers to a random forest with  $m = 3$ , and "GBM-6" a gradient boosted model with interaction order six; similarly for "RF-1" and "GBM-1." The training sets were of size 2000, and the test sets 10, 000.

Figure 15.1 shows the test-error progression on 2500 trees for the three methods. In this case there is some evidence that gradient boosting has started to overfit, although 10-fold cross-validation chose all 2500 trees.

<sup>1</sup>Sadly, Leo Breiman died in July, 2005.

 $^{2}$ http://www.math.usu.edu/ $\sim$ adele/forests/

Image /page/86/Figure/1 description: This is a line graph showing the test average absolute error as a function of the number of trees. The x-axis ranges from 0 to 1000, and the y-axis ranges from 0.32 to 0.44. There are four lines plotted, representing different models: RF m=2 (orange), RF m=6 (green), GBM depth=4 (cyan), and GBM depth=6 (red). The orange line (RF m=2) starts at approximately 0.44 and quickly decreases to around 0.38, then stays relatively flat. The green line (RF m=6) starts at approximately 0.37 and decreases to around 0.355, then stays relatively flat. The cyan line (GBM depth=4) starts at approximately 0.39 and decreases to around 0.355, then stays relatively flat. The red line (GBM depth=6) starts at approximately 0.44 and decreases steadily to around 0.33. The red line shows the lowest test average absolute error at the end of the graph.

**California Housing Data**

FIGURE 15.3. Random forests compared to gradient boosting on the California housing data. The curves represent mean absolute error on the test data as a function of the number of trees in the models. Two random forests are shown, with  $m = 2$  and  $m = 6$ . The two gradient boosted models use a shrinkage parameter  $\nu = 0.05$  in (10.41), and have interaction depths of 4 and 6. The boosted models outperform random forests.

Figure 15.2 shows the results of a simulation<sup>3</sup> comparing random forests to gradient boosting on the nested spheres problem [Equation (10.2) in Chapter 10]. Boosting easily outperforms random forests here. Notice that smaller  $m$  is better here, although part of the reason could be that the true decision boundary is additive.

Figure 15.3 compares random forests to boosting (with shrinkage) in a regression problem, using the California housing data (Section 10.14.1). Two strong features that emerge are

- Random forests stabilize at about 200 trees, while at 1000 trees boosting continues to improve. Boosting is slowed down by the shrinkage, as well as the fact that the trees are much smaller.
- Boosting outperforms random forests here. At 1000 terms, the weaker boosting model (GBM depth 4) has a smaller error than the stronger

<sup>3</sup>Details: The random forests were fit using the R package randomForest 4.5-11, with 500 trees. The gradient boosting models were fit using R package gbm 1.5, with shrinkage parameter set to 0.05, and 2000 trees.

Image /page/87/Figure/1 description: This is a line graph showing the misclassification error as a function of the number of trees. The x-axis represents the number of trees, ranging from 0 to 2500. The y-axis represents the misclassification error, ranging from 0.045 to 0.075. There are two lines plotted: the orange line represents the OOB Error, and the blue line represents the Test Error. Both lines initially decrease sharply as the number of trees increases. After a certain point, the OOB Error fluctuates around 0.052, while the Test Error stabilizes around 0.047, with a dashed line indicating this approximate value. The OOB error is generally higher than the test error for a larger number of trees.

FIGURE 15.4. OOB error computed on the spam training data, compared to the test error computed on the test set.

random forest (RF  $m = 6$ ); a Wilcoxon test on the mean differences in absolute errors has a p-value of 0.007. For larger m the random forests performed no better.

# 15.3 Details of Random Forests

We have glossed over the distinction between random forests for classification versus regression. When used for classification, a random forest obtains a class vote from each tree, and then classifies using majority vote (see Section 8.7 on bagging for a similar discussion). When used for regression, the predictions from each tree at a target point  $x$  are simply averaged, as in (15.2). In addition, the inventors make the following recommendations:

- For classification, the default value for  $m$  is  $\lfloor\sqrt{p}\rfloor$  and the minimum node size is one.
- For regression, the default value for m is  $|p/3|$  and the minimum node size is five.

In practice the best values for these parameters will depend on the problem, and they should be treated as tuning parameters. In Figure 15.3  $m = 6$ performs much better than the default value  $\lfloor 8/3 \rfloor = 2$ .

# 15.3.1 Out of Bag Samples

An important feature of random forests is its use of *out-of-bag* (OOB) samples:

For each observation  $z_i = (x_i, y_i)$ , construct its random forest predictor by averaging only those trees corresponding to bootstrap samples in which  $z_i$  did not appear.

An OOB error estimate is almost identical to that obtained by N-fold crossvalidation; see Exercise 15.2. Hence unlike many other nonlinear estimators, random forests can be fit in one sequence, with cross-validation being performed along the way. Once the oob error stabilizes, the training can be terminated.

Figure 15.4 shows the OOB misclassification error for the span data, compared to the test error. Although 2500 trees are averaged here, it appears from the plot that about 200 would be sufficient.

## 15.3.2 Variable Importance

Variable importance plots can be constructed for random forests in exactly the same way as they were for gradient-boosted models (Section 10.13). At each split in each tree, the improvement in the split-criterion is the importance measure attributed to the splitting variable, and is accumulated over all the trees in the forest separately for each variable. The left plot of Figure 15.5 shows the variable importances computed in this way for the spam data; compare with the corresponding Figure 10.6 on page 354 for gradient boosting. Boosting ignores some variables completely, while the random forest does not. The candidate split-variable selection increases the chance that any single variable gets included in a random forest, while no such selection occurs with boosting.

Random forests also use the oob samples to construct a different variableimportance measure, apparently to measure the prediction strength of each variable. When the bth tree is grown, the OOB samples are passed down the tree, and the prediction accuracy is recorded. Then the values for the jth variable are randomly permuted in the oob samples, and the accuracy is again computed. The decrease in accuracy as a result of this permuting is averaged over all trees, and is used as a measure of the importance of variable  $j$  in the random forest. These are expressed as a percent of the maximum in the right plot in Figure 15.5. Although the rankings of the two methods are similar, the importances in the right plot are more uniform over the variables. The randomization effectively voids the effect of a variable, much like setting a coefficient to zero in a linear model (Exercise 15.7). This does not measure the effect on prediction were this variable not available, because if the model was refitted without the variable, other variables could be used as surrogates.

Image /page/89/Figure/1 description: The image displays two bar charts side-by-side, both titled "Variable Importance" on the x-axis. The left chart is titled "Gini" and uses red bars, while the right chart is titled "Randomization" and uses blue bars. Both charts rank various terms by their importance. In the Gini chart, the top terms are 'table', 'parts', 'cs', '3d', 'addresses', '857', '415', 'direct', 'conference', 'project', 'original', 'report', 'telnet', 'lab', '[', '85', 'technology', 'data', 'font', 'credit', '#', 'make', 'people', 'pm', 'address', 'order', 'labs', 'meeting', '650', ';', 'mail', 'over', 'receive', 're', 'email', 'all', 'will', '(', 'internet', '1999', 'business', '000', 'hpl', 'edu', 'you', 'our', 'george', 'CAPTOT', 'your', 'CAPMAX', 'hp', 'CAPAVE', 'free', '\$', 'remove', '!'. The Randomization chart shows a similar ranking of terms, with 'table', 'parts', '3d', 'addresses', 'direct', 'report', 'cs', 'make', '415', '#', '857', 'conference', 'credit', 'data', 'project', 'people', 'telnet', 'lab', 'original', 'address', '85', '[', 'labs', 'all', 'order', 'technology', 'mail', 'font', ';', 'email', 'over', 'receive', 'pm', '650', 'internet', 'will', '(', 'money', 'meeting', '000', 'business', 'hpl', 'you', 're', '1999', 'our', 'your', 'CAPTOT', 'george', 'edu', 'CAPMAX', 'hp', 'CAPAVE', 'free', '\$', 'remove', '!'. The bars in both charts extend to varying lengths along the x-axis, indicating different levels of variable importance, generally ranging from 0 to 100.

FIGURE 15.5. Variable importance plots for a classification random forest grown on the spam data. The left plot bases the importance on the Gini splitting index, as in gradient boosting. The rankings compare well with the rankings produced by gradient boosting (Figure 10.6 on page 354). The right plot uses OOB randomization to compute variable importances, and tends to spread the importances more uniformly.

Image /page/90/Figure/1 description: The image displays two plots side-by-side. The left plot is titled "Proximity Plot" and shows data points plotted against "Dimension 1" on the x-axis and "Dimension 2" on the y-axis. There are blue and orange data points, with some points labeled with numbers 1 through 6 in white squares. The right plot is titled "Random Forest Classifier" and shows data points plotted against "X1" on the x-axis and "X2" on the y-axis. This plot also contains blue and orange data points, and includes contour lines representing the classifier's decision boundaries, as well as points labeled with numbers 1 through 6 in white squares.

FIGURE 15.6. (Left): Proximity plot for a random forest classifier grown to the mixture data. (Right): Decision boundary and training data for random forest on mixture data. Six points have been identified in each plot.

# 15.3.3 Proximity Plots

One of the advertised outputs of a random forest is a *proximity plot*. Figure 15.6 shows a proximity plot for the mixture data defined in Section 2.3.3 in Chapter 2. In growing a random forest, an  $N \times N$  proximity matrix is accumulated for the training data. For every tree, any pair of oob observations sharing a terminal node has their proximity increased by one. This proximity matrix is then represented in two dimensions using multidimensional scaling (Section 14.8). The idea is that even though the data may be high-dimensional, involving mixed variables, etc., the proximity plot gives an indication of which observations are effectively close together in the eyes of the random forest classifier.

Proximity plots for random forests often look very similar, irrespective of the data, which casts doubt on their utility. They tend to have a star shape, one arm per class, which is more pronounced the better the classification performance.

Since the mixture data are two-dimensional, we can map points from the proximity plot to the original coordinates, and get a better understanding of what they represent. It seems that points in pure regions class-wise map to the extremities of the star, while points nearer the decision boundaries map nearer the center. This is not surprising when we consider the construction of the proximity matrices. Neighboring points in pure regions will often end up sharing a bucket, since when a terminal node is pure, it is no longer split by a random forest tree-growing algorithm. On the other hand, pairs of points that are close but belong to different classes will sometimes share a terminal node, but not always.

## 15.3.4 Random Forests and Overfitting

When the number of variables is large, but the fraction of relevant variables small, random forests are likely to perform poorly with small  $m$ . At each split the chance can be small that the relevant variables will be selected. Figure 15.7 shows the results of a simulation that supports this claim. Details are given in the figure caption and Exercise 15.3. At the top of each pair we see the hyper-geometric probability that a relevant variable will be selected at any split by a random forest tree (in this simulation, the relevant variables are all equal in stature). As this probability gets small, the gap between boosting and random forests increases. When the number of relevant variables increases, the performance of random forests is surprisingly robust to an increase in the number of noise variables. For example, with 6 relevant and 100 noise variables, the probability of a relevant variable being selected at any split is 0.46, assuming  $m = \sqrt{(6 + 100)} \approx 10$ . According to Figure 15.7, this does not hurt the performance of random forests compared with boosting. This robustness is largely due to the relative insensitivity of misclassification cost to the bias and variance of the probability estimates in each tree. We consider random forests for regression in the next section.

Another claim is that random forests "cannot overfit" the data. It is certainly true that increasing B does not cause the random forest sequence to overfit; like bagging, the random forest estimate (15.2) approximates the expectation

$$
\hat{f}_{\rm rf}(x) = \mathcal{E}_{\Theta} T(x; \Theta) = \lim_{B \to \infty} \hat{f}(x)_{\rm rf}^B \tag{15.3}
$$

with an average over B realizations of  $\Theta$ . The distribution of  $\Theta$  here is conditional on the training data. However, this limit can overfit the data; the average of fully grown trees can result in too rich a model, and incur unnecessary variance. Segal (2004) demonstrates small gains in performance by controlling the depths of the individual trees grown in random forests. Our experience is that using full-grown trees seldom costs much, and results in one less tuning parameter.

Figure 15.8 shows the modest effect of depth control in a simple regression example. Classifiers are less sensitive to variance, and this effect of overfitting is seldom seen with random-forest classification.

Image /page/92/Figure/1 description: This box plot compares the test misclassification error of Random Forest and Gradient Boosting models across different numbers of (relevant, noise) variables. The x-axis displays the number of variables, ranging from (2, 5) to (2, 150). The y-axis represents the test misclassification error, with values from 0.10 to 0.30. The top axis shows corresponding values of 0.52, 0.34, 0.25, 0.19, and 0.15. A dashed green line indicates the Bayes Error at 0.10. For each pair of (relevant, noise) variables, there are two box plots: red for Random Forest and blue for Gradient Boosting. The box plots show the distribution of misclassification errors, including medians, quartiles, and outliers. Generally, both models show a decrease in misclassification error as the number of variables increases, with Random Forest performing slightly worse than Gradient Boosting at higher numbers of variables.

FIGURE 15.7. A comparison of random forests and gradient boosting on problems with increasing numbers of noise variables. In each case the true decision boundary depends on two variables, and an increasing number of noise variables are included. Random forests uses its default value  $m = \sqrt{p}$ . At the top of each pair is the probability that one of the relevant variables is chosen at any split. The results are based on 50 simulations for each pair, with a training sample of 300, and a test sample of 500.

# 15.4 Analysis of Random Forests

Image /page/92/Picture/4 description: A yellow, cartoonish depiction of Edvard Munch's 'The Scream' is shown. The figure has wide, circular eyes, an open mouth in a scream, and hands pressed to its ears. The figure is wearing a black shirt.

In this section we analyze the mechanisms at play with the additional randomization employed by random forests. For this discussion we focus on regression and squared error loss, since this gets at the main points, and bias and variance are more complex with 0–1 loss (see Section 7.3.1). Furthermore, even in the case of a classification problem, we can consider the random-forest average as an estimate of the class posterior probabilities, for which bias and variance are appropriate descriptors.

## 15.4.1 Variance and the De-Correlation Effect

The limiting form  $(B \to \infty)$  of the random forest regression estimator is

$$
\hat{f}_{\rm rf}(x) = \mathcal{E}_{\Theta|\mathbf{Z}} T(x; \Theta(\mathbf{Z})),\tag{15.4}
$$

where we have made explicit the dependence on the training data **Z**. Here we consider estimation at a single target point  $x$ . From  $(15.1)$  we see that

Image /page/93/Figure/1 description: This box plot shows the relationship between minimum node size and mean squared test error for shallow and deep models. The x-axis represents the minimum node size, with values of 50, 30, 20, 10, and 5. The y-axis represents the mean squared test error, with values ranging from 1.00 to 1.10. The plot indicates that as the minimum node size decreases (from 50 to 5), the mean squared test error generally increases. The box plots for the 'Shallow' models are on the left, and the box plots for the 'Deep' models are on the right. For both shallow and deep models, smaller minimum node sizes correspond to higher mean squared test errors.

FIGURE 15.8. The effect of tree size on the error in random forest regression. In this example, the true surface was additive in two of the 12 variables, plus additive unit-variance Gaussian noise. Tree depth is controlled here by the minimum node size; the smaller the minimum node size, the deeper the trees.

$$
\text{Var}\hat{f}_{\text{rf}}(x) = \rho(x)\sigma^2(x). \tag{15.5}
$$

Here

•  $\rho(x)$  is the *sampling* correlation between any pair of trees used in the averaging:

$$
\rho(x) = \text{corr}[T(x; \Theta_1(\mathbf{Z})), T(x; \Theta_2(\mathbf{Z}))],
$$
\n(15.6)

where  $\Theta_1(\mathbf{Z})$  and  $\Theta_2(\mathbf{Z})$  are a randomly drawn pair of random forest trees grown to the randomly sampled Z;

•  $\sigma^2(x)$  is the sampling variance of any single randomly drawn tree,

$$
\sigma^2(x) = \text{Var}\,T(x;\Theta(\mathbf{Z})).\tag{15.7}
$$

It is easy to confuse  $\rho(x)$  with the average correlation between fitted trees in a *given* random-forest ensemble; that is, think of the fitted trees as  $N$ vectors, and compute the average pairwise correlation between these vectors, conditioned on the data. This is not the case; this conditional correlation is not directly relevant in the averaging process, and the dependence on x in  $\rho(x)$  warns us of the distinction. Rather,  $\rho(x)$  is the theoretical correlation between a pair of random-forest trees evaluated at  $x$ , induced by repeatedly making training sample draws Z from the population, and then drawing a pair of random forest trees. In statistical jargon, this is the correlation induced by the *sampling distribution* of  $\mathbf{Z}$  and  $\Theta$ .

More precisely, the variability averaged over in the calculations in (15.6) and (15.7) is both

- conditional on Z: due to the bootstrap sampling and feature sampling at each split, and
- a result of the sampling variability of **Z** itself.

In fact, the conditional covariance of a pair of tree fits at  $x$  is zero, because the bootstrap and feature sampling is i.i.d; see Exercise 15.5.

Image /page/94/Figure/4 description: This is a box plot showing the correlation between trees on the y-axis and the number of randomly selected splitting variables m on the x-axis. The y-axis ranges from 0.00 to 0.08, with increments of 0.02. The x-axis ranges from 1 to 49, with tick marks at 1, 4, 7, 13, 19, 25, 31, 37, 43, and 49. The box plots show an increasing trend in correlation as the number of splitting variables increases. The boxes are blue, and there are individual data points represented by circles, some of which are outliers.

FIGURE 15.9. Correlations between pairs of trees drawn by a random-forest regression algorithm, as a function of m. The boxplots represent the correlations at 600 randomly chosen prediction points x.

The following demonstrations are based on a simulation model

$$
Y = \frac{1}{\sqrt{50}} \sum_{j=1}^{50} X_j + \varepsilon,
$$
\n(15.8)

with all the  $X_i$  and  $\varepsilon$  iid Gaussian. We use 500 training sets of size 100, and a single set of test locations of size 600. Since regression trees are nonlinear in Z, the patterns we see below will differ somewhat depending on the structure of the model.

Figure 15.9 shows how the correlation (15.6) between pairs of trees decreases as  $m$  decreases: pairs of tree predictions at  $x$  for different training sets Z are likely to be less similar if they do not use the same splitting variables.

In the left panel of Figure 15.10 we consider the variances of single tree predictors,  $VarT(x; \Theta(\mathbf{Z}))$  (averaged over 600 prediction points x drawn randomly from our simulation model). This is the total variance, and can be

### 600 15. Random Forests

decomposed into two parts using standard conditional variance arguments (see Exercise 15.5):

$$
Var_{\Theta, \mathbf{Z}} T(x; \Theta(\mathbf{Z})) = Var_{\mathbf{Z}} E_{\Theta|\mathbf{Z}} T(x; \Theta(\mathbf{Z})) + E_{\mathbf{Z}} Var_{\Theta|\mathbf{Z}} T(x; \Theta(\mathbf{Z}))
$$
  
Total Variance = Var<sub>**Z**</sub> $\hat{f}_{\text{rf}}(x) + within-Z Variance$   
(15.9)

The second term is the within-Z variance—a result of the randomization, which increases as  $m$  decreases. The first term is in fact the sampling variance of the random forest ensemble (shown in the right panel), which decreases as m decreases. The variance of the individual trees does not change appreciably over much of the range of  $m$ , hence in light of  $(15.5)$ , the variance of the ensemble is dramatically lower than this tree variance.

Image /page/95/Figure/4 description: The image displays two plots side-by-side. The left plot, titled "Single Tree", shows the "Variance" on the y-axis and "m" on the x-axis, ranging from 0 to 50. There are two lines plotted: an orange line labeled "Total" and a green line labeled "Within Z". Both lines show an initial increase and then plateau or decrease slightly. A horizontal light blue line is also present at approximately 1.92. The right plot, titled "Random Forest Ensemble", shows "Mean Squared Error and Squared Bias" on the left y-axis and "Variance" on the right y-axis, with "m" on the x-axis, ranging from 0 to 50. Three lines are plotted: a blue line representing "Mean Squared Error", a pink line representing "Squared Bias", and an orange line representing "Variance". The "Mean Squared Error" increases from approximately 0.85 at m=0 to about 0.18 at m=50. The "Squared Bias" decreases from approximately 0.83 at m=0 to about 0.07 at m=50. The "Variance" increases from approximately 0.07 at m=0 to about 0.12 at m=50. A horizontal light blue line is present at approximately 0.06 on the right y-axis.

FIGURE 15.10. Simulation results. The left panel shows the average variance of a single random forest tree, as a function of m. "Within  $\mathbf{Z}$ " refers to the average within-sample contribution to the variance, resulting from the bootstrap sampling and split-variable sampling (15.9). "Total" includes the sampling variability of Z. The horizontal line is the average variance of a single fully grown tree (without bootstrap sampling). The right panel shows the average mean-squared error, squared bias and variance of the ensemble, as a function of m. Note that the variance axis is on the right (same scale, different level). The horizontal line is the average squared-bias of a fully grown tree.

## 15.4.2 Bias

As in bagging, the bias of a random forest is the same as the bias of any of the individual sampled trees  $T(x; \Theta(\mathbf{Z}))$ :

15.4 Analysis of Random Forests 601

Bias(x) = 
$$
\mu(x) - \mathbf{E}_{\mathbf{Z}} \hat{f}_{\text{rf}}(x)
$$
  
=  $\mu(x) - \mathbf{E}_{\mathbf{Z}} \mathbf{E}_{\Theta|\mathbf{Z}} T(x; \Theta(\mathbf{Z})).$  (15.10)

This is also typically greater (in absolute terms) than the bias of an unpruned tree grown to Z, since the randomization and reduced sample space impose restrictions. Hence the improvements in prediction obtained by bagging or random forests are solely a result of variance reduction.

Any discussion of bias depends on the unknown true function. Figure 15.10 (right panel) shows the squared bias for our additive model simulation (estimated from the 500 realizations). Although for different models the shape and rate of the bias curves may differ, the general trend is that as m decreases, the bias increases. Shown in the figure is the mean-squared error, and we see a classical bias-variance trade-off in the choice of m. For all  $m$  the squared bias of the random forest is greater than that for a single tree (horizontal line).

These patterns suggest a similarity with ridge regression (Section 3.4.1). Ridge regression is useful (in linear models) when one has a large number of variables with similarly sized coefficients; ridge shrinks their coefficients toward zero, and those of strongly correlated variables toward each other. Although the size of the training sample might not permit all the variables to be in the model, this regularization via ridge stabilizes the model and allows all the variables to have their say (albeit diminished). Random forests with small  $m$  perform a similar averaging. Each of the relevant variables get their turn to be the primary split, and the ensemble averaging reduces the contribution of any individual variable. Since this simulation example (15.8) is based on a linear model in all the variables, ridge regression achieves a lower mean-squared error (about 0.45 with  $df(\lambda_{opt}) \approx 29$ ).

## 15.4.3 Adaptive Nearest Neighbors

The random forest classifier has much in common with the  $k$ -nearest neighbor classifier (Section 13.3); in fact a weighted version thereof. Since each tree is grown to maximal size, for a particular  $\Theta^*$ ,  $T(x; \Theta^*(\mathbf{Z}))$  is the response value for one of the training samples<sup>4</sup>. The tree-growing algorithm finds an "optimal" path to that observation, choosing the most informative predictors from those at its disposal. The averaging process assigns weights to these training responses, which ultimately vote for the prediction. Hence via the random-forest voting mechanism, those observations close to the target point get assigned weights—an equivalent kernel—which combine to form the classification decision.

Figure 15.11 demonstrates the similarity between the decision boundary of 3-nearest neighbors and random forests on the mixture data.

<sup>4</sup>We gloss over the fact that pure nodes are not split further, and hence there can be more than one observation in a terminal node

### 602 15. Random Forests

Image /page/97/Figure/1 description: The image displays two plots side-by-side, each illustrating a classification model. The left plot is titled "Random Forest Classifier" and the right plot is titled "3-Nearest Neighbors". Both plots show a scatter of blue and orange circles, representing data points, against a background of colored dots indicating decision regions. Black lines represent decision boundaries, and a dashed pink line is also present in each plot. Below each plot, performance metrics are listed: "Training Error", "Test Error", and "Bayes Error" with corresponding numerical values. For the Random Forest Classifier, the errors are 0.000, 0.238, and 0.210, respectively. For the 3-Nearest Neighbors, the errors are 0.130, 0.242, and 0.210, respectively.

FIGURE 15.11. Random forests versus 3-NN on the mixture data. The axis-oriented nature of the individual trees in a random forest lead to decision regions with an axis-oriented flavor.

# Bibliographic Notes

Random forests as described here were introduced by Breiman (2001), although many of the ideas had cropped up earlier in the literature in different forms. Notably Ho (1995) introduced the term "random forest," and used a consensus of trees grown in random subspaces of the features. The idea of using stochastic perturbation and averaging to avoid overfitting was introduced by Kleinberg (1990), and later in Kleinberg (1996). Amit and Geman (1997) used randomized trees grown on image features for image classification problems. Breiman (1996a) introduced bagging, a precursor to his version of random forests. Dietterich (2000b) also proposed an improvement on bagging using additional randomization. His approach was to rank the top 20 candidate splits at each node, and then select from the list at random. He showed through simulations and real examples that this additional randomization improved over the performance of bagging. Friedman and Hall (2007) showed that sub-sampling (without replacement) is an effective alternative to bagging. They showed that growing and averaging trees on samples of size  $N/2$  is approximately equivalent (in terms bias/variance considerations) to bagging, while using smaller fractions of N reduces the variance even further (through decorrelation).

There are several free software implementations of random forests. In this chapter we used the randomForest package in R, maintained by Andy Liaw, available from the CRAN website. This allows both split-variable selection, as well as sub-sampling. Adele Cutler maintains a random forest website http://www.math.usu.edu/∼adele/forests/ where (as of August 2008) the software written by Leo Breiman and Adele Cutler is freely available. Their code, and the name "random forests", is exclusively licensed to Salford Systems for commercial release. The Weka machine learning archive http://www.cs.waikato.ac.nz/ml/weka/ at Waikato University, New Zealand, offers a free java implementation of random forests.

# **Exercises**

Ex. 15.1 Derive the variance formula (15.1). This appears to fail if  $\rho$  is negative; diagnose the problem in this case.

Ex. 15.2 Show that as the number of bootstrap samples  $B$  gets large, the oob error estimate for a random forest approaches its N-fold CV error estimate, and that in the limit, the identity is exact.

Ex. 15.3 Consider the simulation model used in Figure 15.7 (Mease and Wyner, 2008). Binary observations are generated with probabilities

$$
\Pr(Y = 1|X) = q + (1 - 2q) \cdot 1 \left[ \sum_{j=1}^{J} X_j > J/2 \right],\tag{15.11}
$$

where  $X \sim U[0,1]^p$ ,  $0 \le q \le \frac{1}{2}$ , and  $J \le p$  is some predefined (even) number. Describe this probability surface, and give the Bayes error rate.

Ex. 15.4 Suppose  $x_i$ ,  $i = 1, ..., N$  are iid  $(\mu, \sigma^2)$ . Let  $\bar{x}_1^*$  and  $\bar{x}_2^*$  be two bootstrap realizations of the sample mean. Show that the sampling correlation  $\text{corr}(\bar{x}_1^*, \bar{x}_2^*) = \frac{n}{2n-1} \approx 50\%$ . Along the way, derive  $\text{var}(\bar{x}_1^*)$  and the variance of the bagged mean  $\bar{x}_{bag}$ . Here  $\bar{x}$  is a *linear* statistic; bagging produces no reduction in variance for linear statistics.

Ex. 15.5 Show that the sampling correlation between a pair of randomforest trees at a point  $x$  is given by

$$
\rho(x) = \frac{\text{Var}_{\mathbf{Z}}[\text{E}_{\Theta|\mathbf{Z}}T(x;\Theta(\mathbf{Z}))]}{\text{Var}_{\mathbf{Z}}[\text{E}_{\Theta|\mathbf{Z}}T(x;\Theta(\mathbf{Z}))] + \text{E}_{\mathbf{Z}}\text{Var}_{\Theta|\mathbf{Z}}[T(x;\Theta(\mathbf{Z}))]}.
$$
(15.12)

The term in the numerator is  $Var_{\mathbf{Z}}[\hat{f}_{\rm rf}(x)]$ , and the second term in the denominator is the expected conditional variance due to the randomization in random forests.

Ex. 15.6 Fit a series of random-forest classifiers to the spam data, to explore the sensitivity to the parameter  $m$ . Plot both the OOB error as well as the test error against a suitably chosen range of values for m.

## 604 15. Random Forests

Ex. 15.7 Suppose we fit a linear regression model to  $N$  observations with response  $y_i$  and predictors  $x_{i1}, \ldots, x_{ip}$ . Assume that all variables are standardized to have mean zero and standard deviation one. Let RSS be the mean-squared residual on the training data, and  $\hat{\beta}$  the estimated coefficient. Denote by  $RSS_j^*$  the mean-squared residual on the training data using the same  $\hat{\beta}$ , but with the N values for the jth variable randomly permuted before the predictions are calculated. Show that

$$
E_P[RSS_j^* - RSS] = 2\hat{\beta}_j^2,
$$
\n(15.13)

where  $E_P$  denotes expectation with respect to the permutation distribution. Argue that this is approximately true when the evaluations are done using an independent test set.

This is page 605 Printer: Opaque this

# 16 Ensemble Learning

# 16.1 Introduction

The idea of ensemble learning is to build a prediction model by combining the strengths of a collection of simpler base models. We have already seen a number of examples that fall into this category.

Bagging in Section 8.7 and random forests in Chapter 15 are ensemble methods for classification, where a committee of trees each cast a vote for the predicted class. Boosting in Chapter 10 was initially proposed as a committee method as well, although unlike random forests, the committee of weak learners evolves over time, and the members cast a weighted vote. Stacking (Section 8.8) is a novel approach to combining the strengths of a number of fitted models. In fact one could characterize any dictionary method, such as regression splines, as an ensemble method, with the basis functions serving the role of weak learners.

Bayesian methods for nonparametric regression can also be viewed as ensemble methods: a large number of candidate models are averaged with respect to the posterior distribution of their parameter settings (e.g. (Neal and Zhang, 2006)).

Ensemble learning can be broken down into two tasks: developing a population of base learners from the training data, and then combining them to form the composite predictor. In this chapter we discuss boosting technology that goes a step further; it builds an ensemble model by conducting a regularized and supervised search in a high-dimensional space of weak learners.

## 606 16. Ensemble Learning

An early example of a learning ensemble is a method designed for multiclass classification using error-correcting output codes (Dietterich and Bakiri, 1995, ECOC). Consider the 10-class digit classification problem, and the coding matrix C given in Table 16.1.

TABLE 16.1. Part of a 15-bit error-correcting coding matrix C for the 10-class digit classification problem. Each column defines a two-class classification problem.

| Digit |                | C <sub>2</sub> | $C_3$          | $C_4$          | $C_{5}$        | $C_6$            | $\cdots$ . | 1.5 |
|-------|----------------|----------------|----------------|----------------|----------------|------------------|------------|-----|
| 0     |                |                | $\overline{0}$ | $\overline{0}$ | $\overline{0}$ |                  | .          |     |
| 1     |                | $\overline{0}$ | $\overline{1}$ | $\mathbf{1}$   | $\overline{1}$ | $\mathbf{1}$     | .          |     |
| 2     |                | $\Omega$       | $\Omega$       | $\overline{1}$ | $\Omega$       | $\left( \right)$ | .          |     |
| ٠     | ٠<br>$\bullet$ | ٠<br>٠         |                | ٠<br>٠         | ٠              |                  | .          |     |
| 8     |                |                |                |                |                |                  |            |     |
| 9     |                |                |                |                |                |                  |            |     |

Note that the  $\ell$ th column of the coding matrix  $C_{\ell}$  defines a two-class variable that merges all the original classes into two groups. The method works as follows:

- 1. Learn a separate classifier for each of the  $L = 15$  two class problems defined by the columns of the coding matrix.
- 2. At a test point x, let  $\hat{p}_{\ell}(x)$  be the predicted probability of a one for the  $\ell$ th response.
- 3. Define  $\delta_k(x) = \sum_{\ell=1}^L |C_{k\ell} \hat{p}_{\ell}(x)|$ , the discriminant function for the kth class, where  $C_{k\ell}$  is the entry for row k and column  $\ell$  in Table 16.1.

Each row of  $C$  is a binary code for representing that class. The rows have more bits than is necessary, and the idea is that the redundant "errorcorrecting" bits allow for some inaccuracies, and can improve performance. In fact, the full code matrix  $C$  above has a minimum Hamming distance<sup>1</sup> of 7 between any pair of rows. Note that even the indicator response coding (Section 4.2) is redundant, since 10 classes require only  $\lceil \log_2 10 \rceil = 4$  bits for their unique representation. Dietterich and Bakiri (1995) showed impressive improvements in performance for a variety of multiclass problems when classification trees were used as the base classifier.

James and Hastie (1998) analyzed the ECOC approach, and showed that random code assignment worked as well as the optimally constructed error-correcting codes. They also argued that the main benefit of the coding was in variance reduction (as in bagging and random forests), because the different coded problems resulted in different trees, and the decoding step (3) above has a similar effect as averaging.

 $^1\mathrm{The}$  Hamming distance between two vectors is the number of mismatches between corresponding entries.

# 16.2 Boosting and Regularization Paths

In Section 10.12.2 of the first edition of this book, we suggested an analogy between the sequence of models produced by a gradient boosting algorithm and regularized model fitting in high-dimensional feature spaces. This was primarily motivated by observing the close connection between a boosted version of linear regression and the lasso (Section 3.4.2). These connections have been pursued by us and others, and here we present our current thinking in this area. We start with the original motivation, which fits more naturally in this chapter on ensemble learning.

## 16.2.1 Penalized Regression

Intuition for the success of the shrinkage strategy (10.41) of gradient boosting (page 364 in Chapter 10) can be obtained by drawing analogies with penalized linear regression with a large basis expansion. Consider the dictionary of all possible J-terminal node regression trees  $\mathcal{T} = \{T_k\}$  that could be realized on the training data as basis functions in  $\mathbb{R}^p$ . The linear model is

$$
f(x) = \sum_{k=1}^{K} \alpha_k T_k(x),
$$
\n(16.1)

where  $K = \text{card}(\mathcal{T})$ . Suppose the coefficients are to be estimated by least squares. Since the number of such trees is likely to be much larger than even the largest training data sets, some form of regularization is required. Let  $\hat{\alpha}(\lambda)$  solve

$$
\min_{\alpha} \left\{ \sum_{i=1}^{N} \left( y_i - \sum_{k=1}^{K} \alpha_k T_k(x_i) \right)^2 + \lambda \cdot J(\alpha) \right\},
$$
\n(16.2)

 $J(\alpha)$  is a function of the coefficients that generally penalizes larger values. Examples are

$$
J(\alpha) = \sum_{k=1}^{K} |\alpha_k|^2
$$
 ridge regression, (16.3)

$$
J(\alpha) = \sum_{k=1}^{K} |\alpha_k| \quad \text{lasso}, \tag{16.4}
$$

 $(16.5)$ 

both covered in Section 3.4. As discussed there, the solution to the lasso problem with moderate to large  $\lambda$  tends to be sparse; many of the  $\hat{\alpha}_k(\lambda)$  = 0. That is, only a small fraction of all possible trees enter the model (16.1). Algorithm 16.1 Forward Stagewise Linear Regression.

- 1. Initialize  $\check{\alpha}_k = 0, k = 1, \ldots, K$ . Set  $\varepsilon > 0$  to some small constant, and M large.
- 2. For  $m = 1$  to  $M$ :
  - (a)  $(\beta^*, k^*) = \arg \min_{\beta, k} \sum_{i=1}^N \left( y_i \sum_{l=1}^K \check{\alpha}_l T_l(x_i) \beta T_k(x_i) \right)^2$ .
  - (b)  $\check{\alpha}_{k^*} \leftarrow \check{\alpha}_{k^*} + \varepsilon \cdot \text{sign}(\beta^*).$

3. Output 
$$
f_M(x) = \sum_{k=1}^K \check{\alpha}_k T_k(x)
$$
.

This seems reasonable since it is likely that only a small fraction of all possible trees will be relevant in approximating any particular target function. However, the relevant subset will be different for different targets. Those coefficients that are not set to zero are shrunk by the lasso in that their absolute values are smaller than their corresponding least squares values<sup>2</sup>:  $|\hat{\alpha}_k(\lambda)| < |\hat{\alpha}_k(0)|$ . As  $\lambda$  increases, the coefficients all shrink, each one ultimately becoming zero.

Owing to the very large number of basis functions  $T_k$ , directly solving  $(16.2)$  with the lasso penalty  $(16.4)$  is not possible. However, a feasible forward stagewise strategy exists that closely approximates the effect of the lasso, and is very similar to boosting and the forward stagewise Algorithm 10.2. Algorithm 16.1 gives the details. Although phrased in terms of tree basis functions  $T_k$ , the algorithm can be used with any set of basis functions. Initially all coefficients are zero in line 1; this corresponds to  $\lambda = \infty$  in (16.2). At each successive step, the tree  $T_{k^*}$  is selected that best fits the current residuals in line 2(a). Its corresponding coefficient  $\check{\alpha}_{k^*}$ is then incremented or decremented by an infinitesimal amount in 2(b), while all other coefficients  $\check{\alpha}_k$ ,  $k \neq k^*$  are left unchanged. In principle, this process could be iterated until either all the residuals are zero, or  $\beta^* = 0$ . The latter case can occur if  $K < N$ , and at that point the coefficient values represent a least squares solution. This corresponds to  $\lambda = 0$  in (16.2).

After applying Algorithm 16.1 with  $M < \infty$  iterations, many of the coefficients will be zero, namely, those that have yet to be incremented. The others will tend to have absolute values smaller than their corresponding least squares solution values,  $|\check{\alpha}_k(M)| < |\hat{\alpha}_k(0)|$ . Therefore this M-iteration solution qualitatively resembles the lasso, with M inversely related to  $\lambda$ .

Figure 16.1 shows an example, using the prostate data studied in Chapter 3. Here, instead of using trees  $T_k(X)$  as basis functions, we use the origi-

<sup>&</sup>lt;sup>2</sup>If  $K > N$ , there is in general no unique "least squares value," since infinitely many solutions will exist that fit the data perfectly. We can pick the minimum  $L_1$ -norm solution amongst these, which is the unique lasso solution.

Image /page/104/Figure/1 description: This image displays two plots side-by-side, comparing Lasso and Forward Stagewise regression techniques. The left plot, titled "Lasso", shows coefficient values on the y-axis against a measure of regularization strength 't' on the x-axis, represented by the sum of absolute values of coefficients. The right plot, titled "Forward Stagewise", shows coefficient values on the y-axis against the iteration number on the x-axis. Both plots illustrate how coefficients for different variables (lcavol, svi, lweight, pgg45, lbph, gleason, age, lcp) change as the model complexity increases. In the Lasso plot, coefficients generally increase or decrease smoothly with 't'. In the Forward Stagewise plot, coefficients change in discrete steps with each iteration. The 'lcavol' variable shows a strong positive coefficient in both plots, while 'gleason', 'age', and 'lcp' show negative coefficients, particularly in the Lasso plot.

FIGURE 16.1. Profiles of estimated coefficients from linear regression, for the prostate data studied in Chapter 3. The left panel shows the results from the lasso, for different values of the bound parameter  $t = \sum_{k} |\alpha_k|$ . The right panel shows the results of the stagewise linear regression Algorithm 16.1, using  $M = 220$ consecutive steps of size  $\varepsilon = .01$ .

nal variables  $X_k$  themselves; that is, a multiple linear regression model. The left panel displays the profiles of estimated coefficients from the lasso, for different values of the bound parameter  $t = \sum_{k} |\alpha_k|$ . The right panel shows the results of the stagewise Algorithm 16.1, with  $M = 250$  and  $\varepsilon = 0.01$ . [The left and right panels of Figure 16.1 are the same as Figure 3.10 and the left panel of Figure 3.19, respectively.] The similarity between the two graphs is striking.

In some situations the resemblance is more than qualitative. For example, if all of the basis functions  $T_k$  are mutually uncorrelated, then as  $\varepsilon \downarrow 0$ ,  $M \uparrow$ such that  $M \epsilon \rightarrow t$ , Algorithm 16.1 yields exactly the same solution as the lasso for bound parameter  $t = \sum_k |\alpha_k|$  (and likewise for all solutions along the path). Of course, tree-based regressors are not uncorrelated. However, the solution sets are also identical if the coefficients  $\hat{\alpha}_k(\lambda)$  are all monotone functions of  $\lambda$ . This is often the case when the correlation between the variables is low. When the  $\hat{\alpha}_k(\lambda)$  are not monotone in  $\lambda$ , then the solution sets are not identical. The solution sets for Algorithm 16.1 tend to change less rapidly with changing values of the regularization parameter than those of the lasso.

## 610 16. Ensemble Learning

Efron et al. (2004) make the connections more precise, by characterizing the exact solution paths in the  $\varepsilon$ -limiting case. They show that the coefficient paths are piece-wise linear functions, both for the lasso and forward stagewise. This facilitates efficient algorithms which allow the entire paths to be computed with the same cost as a single least-squares fit. This least angle regression algorithm is described in more detail in Section 3.8.1.

Hastie et al. (2007) show that this infinitesimal forward stagewise algorithm  $(FS_0)$  fits a monotone version of the lasso, which optimally reduces at each step the loss function for a given increase in the arc length of the coefficient path (see Sections 16.2.3 and 3.8.1). The arc-length for the  $\epsilon > 0$ case is  $M\epsilon$ , and hence proportional to the number of steps.

Tree boosting (Algorithm 10.3) with shrinkage (10.41) closely resembles Algorithm 16.1, with the learning rate parameter  $\nu$  corresponding to  $\varepsilon$ . For squared error loss, the only difference is that the optimal tree to be selected at each iteration  $T_{k*}$  is approximated by the standard top-down greedy tree-induction algorithm. For other loss functions, such as the exponential loss of AdaBoost and the binomial deviance, Rosset et al. (2004a) show similar results to what we see here. Thus, one can view tree boosting with shrinkage as a form of monotone ill-posed regression on all possible (Jterminal node) trees, with the lasso penalty (16.4) as a regularizer. We return to this topic in Section 16.2.3.

The choice of no shrinkage  $[\nu = 1$  in equation (10.41)] is analogous to forward-stepwise regression, and its more aggressive cousin best-subset selection, which penalizes the *number* of non zero coefficients  $J(\alpha) = \sum_{k} |\alpha_k|^0$ . With a small fraction of dominant variables, best subset approaches often work well. But with a moderate fraction of strong variables, it is well known that subset selection can be excessively greedy (Copas, 1983), often yielding poor results when compared to less aggressive strategies such as the lasso or ridge regression. The dramatic improvements often seen when shrinkage is used with boosting are yet another confirmation of this approach.

# 16.2.2 The "Bet on Sparsity" Principle

As shown in the previous section, boosting's forward stagewise strategy with shrinkage approximately minimizes the same loss function with a lasso-style  $L_1$  penalty. The model is built up slowly, searching through "model space" and adding shrunken basis functions derived from important predictors. In contrast, the  $L_2$  penalty is computationally much easier to deal with, as shown in Section 12.3.7. With the basis functions and  $L_2$ penalty chosen to match a particular positive-definite kernel, one can solve the corresponding optimization problem without explicitly searching over individual basis functions.

However, the sometimes superior performance of boosting over procedures such as the support vector machine may be largely due to the implicit use of the  $L_1$  versus  $L_2$  penalty. The shrinkage resulting from the

 $L_1$  penalty is better suited to *sparse* situations, where there are few basis functions with nonzero coefficients (among all possible choices).

We can strengthen this argument through a simple example, taken from Friedman et al. (2004). Suppose we have 10, 000 data points and our model is a linear combination of a million trees. If the true population coefficients of these trees arose from a Gaussian distribution, then we know that in a Bayesian sense the best predictor is ridge regression (Exercise 3.6). That is, we should use an  $L_2$  rather than an  $L_1$  penalty when fitting the coefficients. On the other hand, if there are only a small number (e.g., 1000) coefficients that are nonzero, the lasso  $(L_1 \text{ penalty})$  will work better. We think of this as a sparse scenario, while the first case (Gaussian coefficients) is dense. Note however that in the dense scenario, although the  $L_2$  penalty is best, neither method does very well since there is too little data from which to estimate such a large number of nonzero coefficients. This is the curse of dimensionality taking its toll. In a sparse setting, we can potentially do well with the  $L_1$  penalty, since the number of nonzero coefficients is small. The  $L_2$  penalty fails again.

In other words, use of the  $L_1$  penalty follows what we call the "bet on sparsity" principle for high-dimensional problems:

Use a procedure that does well in sparse problems, since no procedure does well in dense problems.

These comments need some qualification:

- For any given application, the degree of sparseness/denseness depends on the unknown true target function, and the chosen dictionary  $\mathcal{T}$ .
- The notion of sparse versus dense is relative to the size of the training data set and/or the noise-to-signal ratio (NSR). Larger training sets allow us to estimate coefficients with smaller standard errors. Likewise in situations with small NSR, we can identify more nonzero coefficients with a given sample size than in situations where the NSR is larger.
- The size of the dictionary plays a role as well. Increasing the size of the dictionary may lead to a sparser representation for our function, but the search problem becomes more difficult leading to higher variance.

Figure 16.2 illustrates these points in the context of linear models using simulation. We compare ridge regression and lasso, both for classification and regression problems. Each run has 50 observations with 300 independent Gaussian predictors. In the top row all 300 coefficients are nonzero, generated from a Gaussian distribution. In the middle row, only 10 are nonzero and generated from a Gaussian, and the last row has 30 non zero Gaussian coefficients. For regression, standard Gaussian noise is

Image /page/107/Figure/1 description: This figure displays box plots comparing regression and classification performance across different noise-to-signal ratios. The figure is divided into two main columns: Regression and Classification. Each column contains three rows, representing different subset sizes: Gaussian, Subset 10, and Subset 30. Within each row, there are two subplots, one for Lasso and one for Ridge. The y-axis for the Regression plots represents 'Percentage Squared Prediction Error Explained', ranging from 0.0 to 1.0. The y-axis for the Classification plots represents 'Percentage Misclassification Error Explained', also ranging from 0.0 to 1.0. The x-axis for all plots is labeled 'Noise-to-Signal Ratio', with values from 0.1 to 0.5. The Regression plots show that for Gaussian and Subset 10, Lasso generally performs better (lower error) at higher noise-to-signal ratios compared to Ridge. For Subset 30, both Lasso and Ridge show similar performance, with Lasso performing slightly better at lower ratios. The Classification plots show that for Gaussian, both Lasso and Ridge have low misclassification error across all ratios. For Subset 10 and Subset 30, Lasso generally shows higher misclassification error than Ridge, especially at lower noise-to-signal ratios.

Regression

Classification

FIGURE 16.2. Simulations that show the superiority of the  $L_1$  (lasso) penalty over  $L_2$  (ridge) in regression and classification. Each run has 50 observations with 300 independent Gaussian predictors. In the top row all 300 coefficients are nonzero, generated from a Gaussian distribution. In the middle row, only 10 are nonzero, and the last row has 30 nonzero. Gaussian errors are added to the linear predictor  $\eta(X)$  for the regression problems, and binary responses generated via the inverse-logit transform for the classification problems. Scaling of  $\eta(X)$  resulted in the noise-to-signal ratios shown. Lasso is used in the left sub-columns, ridge in the right. We report the optimal percentage of error explained on test data (relative to the error of a constant model), displayed as boxplots over 20 realizations for each combination. In the only situation where ridge beats lasso (top row), neither do well.

added to the linear predictor  $\eta(X) = X^T \beta$  to produce a continuous response. For classification the linear predictor is transformed via the inverselogit to a probability, and a binary response is generated. Five different noise-to-signal ratios are presented, obtained by scaling  $\eta(X)$  prior to generating the response. In both cases this is defined to be  $NSR =$  $Var(Y|\eta(X))$ /Var $(\eta(X))$ . Both the ridge regression and lasso coefficient paths were fit using a series of 50 values of  $\lambda$  corresponding to a range of df from 1 to 50 (see Chapter 3 for details). The models were evaluated on a large test set (infinite for Gaussian, 5000 for binary), and in each case the value for  $\lambda$  was chosen to minimize the test-set error. We report percentage variance explained for the regression problems, and percentage misclassification error explained for the classification problems (relative to a baseline error of 0.5). There are 20 simulation runs for each scenario.

Note that for the classification problems, we are using squared-error loss to fit the binary response. Note also that we do not using the training data to select  $\lambda$ , but rather are reporting the best possible behavior for each method in the different scenarios. The  $L_2$  penalty performs poorly everywhere. The Lasso performs reasonably well in the only two situations where it can (sparse coefficients). As expected the performance gets worse as the NSR increases (less so for classification), and as the model becomes denser. The differences are less marked for classification than for regression.

These empirical results are supported by a large body of theoretical results (Donoho and Johnstone, 1994; Donoho and Elad, 2003; Donoho, 2006b; Candes and Tao, 2007) that support the superiority of  $L_1$  estimation in sparse settings.

# 16.2.3 Regularization Paths, Over-fitting and Margins

Image /page/108/Picture/5 description: A yellow, cartoonish depiction of Edvard Munch's "The Scream" painting. The figure has wide, staring eyes, an open mouth, and is holding its hands to its head. The background is white, and the figure is wearing a black shirt.

It has often been observed that boosting "does not overfit," or more astutely is "slow to overfit." Part of the explanation for this phenomenon was made earlier for random forests — misclassification error is less sensitive to variance than is mean-squared error, and classification is the major focus in the boosting community. In this section we show that the regularization paths of boosted models are "well behaved," and that for certain loss functions they have an appealing limiting form.

Figure 16.3 shows the coefficient paths for lasso and infinitesimal forward stagewise  $(FS_0)$  in a simulated regression setting. The data consists of a dictionary of 1000 Gaussian variables, strongly correlated ( $\rho = 0.95$ ) within blocks of 20, but uncorrelated between blocks. The generating model has nonzero coefficients for 50 variables, one drawn from each block, and the coefficient values are drawn from a standard Gaussian. Finally, Gaussian noise is added, with a noise-to-signal ratio of 0.72 (Exercise 16.1.) The FS<sub>0</sub> algorithm is a limiting form of algorithm 16.1, where the step size  $\varepsilon$ is shrunk to zero (Section 3.8.1). The grouping of the variables is intended to mimic the correlations of nearby trees, and with the forward-stagewise

Image /page/109/Figure/1 description: This image contains two plots side-by-side, both titled with "LASSO" on the left and "Forward Stagewise" on the right. Both plots display "Standardized Coefficients" on the y-axis, ranging from -20 to 30, and "|α(m)|/|α(∞)|" on the x-axis, ranging from 0.0 to 1.0. Each plot shows multiple colored lines representing different coefficients as they change across the x-axis values. The LASSO plot shows coefficients that start at various values and tend to move towards zero as the x-axis value increases, with many crossing the zero line. The Forward Stagewise plot shows coefficients that generally increase or decrease monotonically from their starting values, with some becoming zero and staying there, while others continue to change.

FIGURE 16.3. Comparison of lasso and infinitesimal forward stagewise paths on simulated regression data. The number of samples is 60 and the number of variables is 1000. The forward-stagewise paths fluctuate less than those of lasso in the final stages of the algorithms.

algorithm, this setup is intended as an idealized version of gradient boosting with shrinkage. For both these algorithms, the coefficient paths can be computed exactly, since they are piecewise linear (see the LARS algorithm in Section 3.8.1).

Here the coefficient profiles are similar only in the early stages of the paths. For the later stages, the forward stagewise paths tend to be monotone and smoother, while those for the lasso fluctuate widely. This is due to the strong correlations among subsets of the variables —lasso suffers somewhat from the multi-collinearity problem (Exercise 3.28).

The performance of the two models is rather similar (Figure 16.4), and they achieve about the same minimum. In the later stages forward stagewise takes longer to overfit, a likely consequence of the smoother paths.

Hastie et al. (2007) show that  $FS_0$  solves a monotone version of the lasso problem for squared error loss. Let  $\mathcal{T}^a = \mathcal{T} \cup \{-\mathcal{T}\}\$ be the augmented dictionary obtained by including a negative copy of every basis element in T. We consider models  $f(x) = \sum_{T_k \in \mathcal{T}^a} \alpha_k T_k(x)$  with non-negative coefficients  $\alpha_k \geq 0$ . In this expanded space, the lasso coefficient paths are positive, while those of  $FS_0$  are monotone nondecreasing.

The monotone lasso path is characterized by a differential equation

$$
\frac{\partial \alpha}{\partial \ell} = \rho^{ml}(\alpha(\ell)),\tag{16.6}
$$

Image /page/110/Figure/1 description: This is a scatter plot showing the relationship between Mean Squared Error on the y-axis and |α(m)| on the x-axis. The plot displays two sets of data points, one colored orange representing 'Lasso' and the other colored teal representing 'Forward Stagewise'. The x-axis ranges from 0 to 70, and the y-axis ranges from 25 to 55. Both datasets show a general trend of increasing Mean Squared Error as |α(m)| increases, after reaching a minimum around |α(m)| = 20. The 'Lasso' data points are clustered at lower |α(m)| values, while the 'Forward Stagewise' data points extend to higher |α(m)| values.

FIGURE 16.4. Mean squared error for lasso and infinitesimal forward stagewise on the simulated data. Despite the difference in the coefficient paths, the two models perform similarly over the critical part of the regularization path. In the right tail, lasso appears to overfit more rapidly.

with initial condition  $\alpha(0) = 0$ , where  $\ell$  is the  $L_1$  arc-length of the path  $\alpha(\ell)$  (Exercise 16.2). The monotone lasso move direction (velocity vector)  $\rho^{ml}(\alpha(\ell))$  decreases the loss at the optimal quadratic rate per unit increase in the  $L_1$  arc-length of the path. Since  $\rho_k^{ml}(\alpha(\ell)) \geq 0 \ \forall k,\ell$ , the solution paths are monotone.

The lasso can similarly be characterized as the solution to a differential equation as in (16.6), except that the move directions decrease the loss optimally per unit increase in the  $L_1$  norm of the path. As a consequence, they are not necessarily positive, and hence the lasso paths need not be monotone.

In this augmented dictionary, restricting the coefficients to be positive is natural, since it avoids an obvious ambiguity. It also ties in more naturally with tree boosting—we always find trees positively correlated with the current residual.

There have been suggestions that boosting performs well (for two-class classification) because it exhibits maximal-margin properties, much like the support-vector machines of Chapters 4.5.2 and 12. Schapire et al. (1998) define the normalized  $L_1$  margin of a fitted model  $f(x) = \sum_k \alpha_k T_k(x)$  as

$$
m(f) = \min_{i} \frac{y_i f(x_i)}{\sum_{k=1}^{K} |\alpha_k|}.
$$
 (16.7)

Here the minimum is taken over the training sample, and  $y_i \in \{-1, +1\}.$ Unlike the  $L_2$  margin (4.40) of support vector machines, the  $L_1$  margin  $m(f)$  measures the distance to the closest training point in  $L_{\infty}$  units (maximum coordinate distance).

Image /page/111/Figure/1 description: The image contains two plots side-by-side, both with the x-axis labeled "Number of Trees" ranging from 0 to 10K. The left plot has the y-axis labeled "Margin" ranging from -0.3 to 0.1. It shows a blue curve that starts at approximately -0.3 at 0 trees and increases rapidly, leveling off around 0.05 after about 2K trees. A dashed orange line indicates the 0.0 mark on the y-axis and a vertical dashed orange line is at 0 on the x-axis. The right plot has the y-axis labeled "Test Error" ranging from 0.25 to 0.29. It shows a green curve that starts at approximately 0.25 at 0 trees and increases rapidly, reaching a peak around 0.285 at about 1K trees, then fluctuates slightly before ending around 0.285 at 10K trees. A dashed orange line indicates the 0.28 mark on the y-axis and a vertical dashed orange line is at 0 on the x-axis.

**FIGURE 16.5.** The left panel shows the  $L_1$  margin  $m(f)$  for the Adaboost classifier on the mixture data, as a function of the number of 4-node trees. The model was fit using the R package gbm, with a shrinkage factor of 0.02. After 10, 000 trees,  $m(f)$  has settled down. Note that when the margin crosses zero, the training error becomes zero. The right panel shows the test error, which is minimized at 240 trees. In this case, Adaboost overfits dramatically if run to convergence.

Schapire et al. (1998) prove that with separable data, Adaboost increases  $m(f)$  with each iteration, converging to a margin-symmetric solution. Rätsch and Warmuth (2002) prove the asymptotic convergence of Adaboost with shrinkage to a  $L_1$ -margin-maximizing solution. Rosset et al. (2004a) consider regularized models of the form (16.2) for general loss functions. They show that as  $\lambda \downarrow 0$ , for particular loss functions the solution converges to a margin-maximizing configuration. In particular they show this to be the case for the exponential loss of Adaboost, as well as binomial deviance.

Collecting together the results of this section, we reach the following summary for boosted classifiers:

The sequence of boosted classifiers form an  $L_1$ -regularized monotone path to a margin-maximizing solution.

Of course the margin-maximizing end of the path can be a very poor, overfit solution, as it is in the example in Figure 16.5. Early stopping amounts to picking a point along the path, and should be done with the aid of a validation dataset.

# 16.3 Learning Ensembles

The insights learned from the previous sections can be harnessed to produce a more effective and efficient ensemble model. Again we consider functions of the form

$$
f(x) = \alpha_0 + \sum_{T_k \in \mathcal{T}} \alpha_k T_k(x), \qquad (16.8)
$$

where  $\mathcal T$  is a dictionary of basis functions, typically trees. For gradient boosting and random forests,  $|\mathcal{T}|$  is very large, and it is quite typical for the final model to involve many thousands of trees. In the previous section we argue that gradient boosting with shrinkage fits an  $L_1$  regularized monotone path in this space of trees.

Friedman and Popescu (2003) propose a hybrid approach which breaks this process down into two stages:

- A finite dictionary  $\mathcal{T}_L = \{T_1(x), T_2(x), \ldots, T_M(x)\}\$ of basis functions is induced from the training data;
- A family of functions  $f_{\lambda}(x)$  is built by fitting a lasso path in this dictionary:

$$
\alpha(\lambda) = \arg\min_{\alpha} \sum_{i=1}^{N} L[y_i, \alpha_0 + \sum_{m=1}^{M} \alpha_m T_m(x_i)] + \lambda \sum_{m=1}^{M} |\alpha_m|.
$$
 (16.9)

In its simplest form this model could be seen as a way of post-processing boosting or random forests, taking for  $\mathcal{T}_L$  the collection of trees produced by the gradient boosting or random forest algorithms. By fitting the lasso path to these trees, we would typically use a much reduced set, which would save in computations and storage for future predictions. In the next section we describe modifications of this prescription that reduce the correlations in the ensemble  $\mathcal{T}_L$ , and improve the performance of the lasso post processor.

As an initial illustration, we apply this procedure to a random forest ensemble grown on the spam data.

Figure 16.6 shows that a lasso post-processing offers modest improvement over the random forest (blue curve), and reduces the forest to about 40 trees, rather than the original 1000. The post-processed performance matches that of gradient boosting. The orange curves represent a modified version of random forests, designed to reduce the correlations between trees even more. Here a random sub-sample (without replacement) of 5% of the training sample is used to grow each tree, and the trees are restricted to be shallow (about six terminal nodes). The post-processing offers more dramatic improvements here, and the training costs are reduced by a factor of about 100. However, the performance of the post-processed model falls somewhat short of the blue curves.

## 16.3.1 Learning a Good Ensemble

Not all ensembles  $\mathcal{T}_L$  will perform well with post-processing. In terms of basis functions, we want a collection that covers the space well in places

Image /page/113/Figure/1 description: This is a line graph titled "Spam Data" that plots test error against the number of trees. The x-axis ranges from 0 to 500, labeled "Number of Trees". The y-axis ranges from 0.04 to 0.09, labeled "Test Error". There are three lines on the graph: a light blue dashed line labeled "Random Forest" at approximately 0.049, a yellow dashed line labeled "Random Forest (5%, 6)" at approximately 0.086, and a purple dashed line labeled "Gradient Boost (5 node)" at approximately 0.045. The yellow line shows a high test error that decreases sharply initially and then fluctuates around 0.06. The light blue line shows a sharp decrease from a high value to around 0.048 and then fluctuates slightly. The purple line shows a sharp decrease to around 0.045 and remains relatively stable.

FIGURE 16.6. Application of the lasso post-processing (16.9) to the spam data. The horizontal blue line is the test error of a random forest fit to the spam data, using 1000 trees grown to maximum depth (with  $m = 7$ ; see Algorithm 15.1). The jagged blue curve is the test error after post-processing the first 500 trees using the lasso, as a function of the number of trees with nonzero coefficients. The orange curve/line use a modified form of random forest, where a random draw of 5% of the data are used to grow each tree, and the trees are forced to be shallow (typically six terminal nodes). Here the post-processing offers much greater improvement over the random forest that generated the ensemble.

where they are needed, and are sufficiently different from each other for the post-processor to be effective.

Friedman and Popescu (2003) gain insights from numerical quadrature and importance sampling. They view the unknown function as an integral

$$
f(x) = \int \beta(\gamma)b(x;\gamma)d\gamma,
$$
 (16.10)

where  $\gamma \in \Gamma$  indexes the basis functions  $b(x; \gamma)$ . For example, if the basis functions are trees, then  $\gamma$  indexes the splitting variables, the split-points and the values in the terminal nodes. Numerical quadrature amounts to finding a set of M evaluation points  $\gamma_m \in \Gamma$  and corresponding weights  $\alpha_m$  so that  $f_M(x) = \alpha_0 + \sum_{m=1}^M \alpha_m b(x; \gamma_m)$  approximates  $f(x)$  well over the domain of x. Importance sampling amounts to sampling  $\gamma$  at random, but giving more weight to relevant regions of the space Γ. Friedman and Popescu (2003) suggest a measure of (lack of) relevance that uses the loss function (16.9):

16.3 Learning Ensembles 619

$$
Q(\gamma) = \min_{c_0, c_1} \sum_{i=1}^{N} L(y_i, c_0 + c_1 b(x_i; \gamma)),
$$
\n(16.11)

evaluated on the training data.

If a single basis function were to be selected (e.g., a tree), it would be the global minimizer  $\gamma^* = \arg \min_{\gamma \in \Gamma} Q(\gamma)$ . Introducing randomness in the selection of  $\gamma$  would necessarily produce less optimal values with  $Q(\gamma)$  $Q(\gamma^*)$ . They propose a natural measure of the characteristic width  $\sigma$  of the sampling scheme  $S$ ,

$$
\sigma = \mathcal{E}_{\mathcal{S}}[Q(\gamma) - Q(\gamma^*)].\tag{16.12}
$$

- $\sigma$  too narrow suggests too many of the  $b(x; \gamma_m)$  look alike, and similar to  $b(x; \gamma^*);$
- $\sigma$  too wide implies a large spread in the  $b(x; \gamma_m)$ , but possibly consisting of many irrelevant cases.

Friedman and Popescu (2003) use sub-sampling as a mechanism for introducing randomness, leading to their ensemble-generation algorithm 16.2.

Algorithm 16.2 ISLE Ensemble Generation.

- 1.  $f_0(x) = \arg \min_c \sum_{i=1}^{N} L(y_i, c)$
- 2. For  $m = 1$  to  $M$  do
  - (a)  $\gamma_m = \arg \min_{\gamma} \sum_{i \in S_m(\eta)} L(y_i, f_{m-1}(x_i) + b(x_i; \gamma))$
  - (b)  $f_m(x) = f_{m-1}(x) + \nu b(x; \gamma_m)$
- 3.  $\mathcal{T}_{ISLE} = \{b(x; \gamma_1), b(x; \gamma_2), \dots, b(x; \gamma_M)\}.$

 $S_m(\eta)$  refers to a subsample of  $N \cdot \eta$  ( $\eta \in (0,1]$ ) of the training observations, typically without replacement. Their simulations suggest picking  $η \leq \frac{1}{2}$ , and for large N picking  $η \sim 1/\sqrt{N}$ . Reducing  $η$  increases the randomness, and hence the width  $\sigma$ . The parameter  $\nu \in [0,1]$  introduces memory into the randomization process; the larger  $\nu$ , the more the procedure avoids  $b(x; \gamma)$  similar to those found before. A number of familiar randomization schemes are special cases of Algorithm 16.2:

*Bagging* has  $\eta = 1$ , but samples with replacement, and has  $\nu = 0$ . Friedman and Hall (2007) argue that sampling without replacement with  $\eta = 1/2$  is equivalent to sampling with replacement with  $\eta = 1$ , and the former is much more efficient.

## 620 16. Ensemble Learning

- Random forest sampling is similar, with more randomness introduced by the selection of the splitting variable. Reducing  $\eta$  < 1/2 in algorithm 16.2 has a similar effect to reducing  $m$  in random forests, but does not suffer from the potential biases discussed in Section 15.4.2.
- Gradient boosting with shrinkage (10.41) uses  $\eta = 1$ , but typically does not produce sufficient width  $\sigma$ .

Stochastic gradient boosting (Friedman, 1999) follows the recipe exactly.

The authors recommend values  $\nu = 0.1$  and  $\eta \le \frac{1}{2}$ , and call their combined procedure (ensemble generation and post processing) Importance sampled learning ensemble (ISLE).

Figure 16.7 shows the performance of an ISLE on the spam data. It does

Image /page/115/Figure/6 description: This is a line graph titled "Spam Data". The x-axis represents the "Number of Trees" and ranges from 0 to 2500. The y-axis represents the "Test Error" and ranges from 0.040 to 0.060. There are two lines plotted: one in yellow representing "Gradient Boosting (5 Node)" and another in teal representing "Lasso Post-processed". A dashed yellow line is present at approximately 0.042, indicating a baseline or threshold. Both lines show a general downward trend in test error as the number of trees increases, with some fluctuations.

FIGURE 16.7. Importance sampling learning ensemble (ISLE) fit to the spam data. Here we used  $\eta = 1/2$ ,  $\nu = 0.05$ , and trees with five terminal nodes. The lasso post-processed ensemble does not improve the prediction error in this case, but it reduces the number of trees by a factor of five.

not improve the predictive performance, but is able to produce a more parsimonious model. Note that in practice the post-processing includes the selection of the regularization parameter  $\lambda$  in (16.9), which would be chosen by cross-validation. Here we simply demonstrate the effects of postprocessing by showing the entire path on the test data.

Figure 16.8 shows various ISLEs on a regression example. The generating

Image /page/116/Figure/3 description: This is a line graph showing the relationship between the number of trees and the mean squared error for different algorithms. The x-axis represents the number of trees, ranging from 0 to 2500. The y-axis represents the mean squared error, ranging from 1.0 to 3.5. There are four lines plotted: GBM (1, 0.01) in orange, GBM (0.1, 0.01) in green, ISLE GB in blue, and ISLE RF in brown. A dashed red line indicates the performance of a Random Forest, which is approximately at a mean squared error of 2.5. The blue line (ISLE GB) shows the fastest decrease in mean squared error, reaching a minimum around 1.0 at approximately 400 trees. The green line (GBM (0.1, 0.01)) also shows a decreasing trend, reaching around 1.0 at 2000 trees. The orange line (GBM (1, 0.01)) shows a slower decrease, leveling off around 1.3 at 2500 trees. The brown line (ISLE RF) initially decreases but then increases slightly, reaching around 2.0 at 1000 trees.

FIGURE 16.8. Demonstration of ensemble methods on a regression simulation example. The notation GBM (0.1, 0.01) refers to a gradient boosted model, with parameters  $(\eta, \nu)$ . We report mean-squared error from the true (known) function. Note that the sub-sampled GBM model (green) outperforms the full GBM model (orange). The lasso post-processed version achieves similar error. The random forest is outperformed by its post-processed version, but both fall short of the other models.

function is

$$
f(X) = 10 \cdot \prod_{j=1}^{5} e^{-2X_j^2} + \sum_{j=6}^{35} X_j,
$$
 (16.13)

where  $X \sim U[0, 1]^{100}$  (the last 65 elements are noise variables). The response  $Y = f(X) + \varepsilon$  where  $\varepsilon \sim N(0, \sigma^2)$ ; we chose  $\sigma = 1.3$  resulting in a signal-to-noise ratio of approximately 2. We used a training sample of size 1000, and estimated the mean squared error  $E(\hat{f}(X)-f(X))^2$  by averaging over a test set of 500 samples. The sub-sampled GBM curve (light blue) is an instance of stochastic gradient boosting (Friedman, 1999) discussed in Section 10.12, and it outperforms gradient boosting on this example.

## 622 16. Ensemble Learning

## 16.3.2 Rule Ensembles

Here we describe a modification of the tree-ensemble method that focuses on individual rules (Friedman and Popescu, 2003). We encountered rules in Section 9.3 in the discussion of the PRIM method. The idea is to enlarge an ensemble of trees by constructing a set of rules from each of the trees in the collection.

Image /page/117/Figure/3 description: A decision tree diagram is shown. Node 0 is the root node. From node 0, there are two branches: one labeled 'X1 < 2.1' leading to node 1, and another labeled 'X1 >= 2.1' leading to node 2. From node 2, there are two branches: one labeled 'X3 ∈ {S}' leading to node 3, and another labeled 'X3 ∈ {M, L}' leading to node 4. From node 3, there are two branches: one labeled 'X7 < 4.5' leading to node 5, and another labeled 'X7 >= 4.5' leading to node 6. Nodes 1, 4, 5, and 6 are leaf nodes.

FIGURE 16.9. A typical tree in an ensemble, from which rules can be derived.

Figure 16.9 depicts a small tree, with numbered nodes. The following rules can be derived from this tree:

$$
R_1(X) = I(X_1 < 2.1)
$$
  
$$
R_2(X) = I(X_1 \ge 2.1)
$$
  
$$
R_3(X) = I(X_1 \ge 2.1) \cdot I(X_3 \in \{S\})
$$
  
$$
R_4(X) = I(X_1 \ge 2.1) \cdot I(X_3 \in \{M, L\})
$$
  
$$
R_5(X) = I(X_1 \ge 2.1) \cdot I(X_3 \in \{S\}) \cdot I(X_7 < 4.5)
$$
  
$$
R_6(X) = I(X_1 \ge 2.1) \cdot I(X_3 \in \{S\}) \cdot I(X_7 \ge 4.5)
$$
(16.14)

A linear expansion in rules 1, 4, 5 and 6 is equivalent to the tree itself (Exercise 16.3); hence (16.14) is an over-complete basis for the tree.

For each tree  $T_m$  in an ensemble  $\mathcal T$ , we can construct its mini-ensemble of rules  $\mathcal{T}^m_{\textit{RULE}}$ , and then combine them all to form a larger ensemble

$$
\mathcal{T}_{\text{RULE}} = \bigcup_{m=1}^{M} \mathcal{T}_{\text{RULE}}^{m}.
$$
\n(16.15)

This is then treated like any other ensemble, and post-processed via the lasso or similar regularized procedure.

There are several advantages to this approach of deriving rules from the more complex trees:

• The space of models is enlarged, and can lead to improved performance.

Image /page/118/Figure/1 description: This is a box plot comparing the mean squared error for two different methods: "Rules" and "Rules + Linear". The y-axis represents the Mean Squared Error, with values ranging from 0.9 to 1.3. The "Rules" method has a median mean squared error of approximately 1.15, with the box extending from about 1.08 to 1.22. There is an outlier at approximately 0.88. The "Rules + Linear" method has a median mean squared error of approximately 1.12, with the box extending from about 1.03 to 1.24. The whiskers for both methods extend to roughly 1.03 and 1.28 for "Rules", and 0.87 and 1.27 for "Rules + Linear".

FIGURE 16.10. Mean squared error for rule ensembles, using 20 realizations of the simulation example (16.13).

- Rules are easier to interpret than trees, so there is the potential for a simplified model.
- It is often natural to augment  $\mathcal{T}_{\text{RULE}}$  by including each variable  $X_i$ separately as well, thus allowing the ensemble to model linear functions well.

Friedman and Popescu (2008) demonstrate the power of this procedure on a number of illustrative examples, including the simulation example (16.13). Figure 16.10 shows boxplots of the mean-squared error from the true model for twenty realizations from this model. The models were all fit using the Rulefit software, available on the ESL homepage<sup>3</sup>, which runs in an automatic mode.

On the same training set as used in Figure 16.8, the rule based model achieved a mean-squared error of 1.06. Although slightly worse than the best achieved in that figure, the results are not comparable because crossvalidation was used here to select the final model.

# Bibliographic Notes

As noted in the introduction, many of the new methods in machine learning have been dubbed "ensemble" methods. These include neural networks boosting, bagging and random forests; Dietterich (2000a) gives a survey of tree-based ensemble methods. Neural networks (Chapter 11) are perhaps more deserving of the name, since they simultaneously learn the parameters

<sup>3</sup>ESL homepage: www-stat.stanford.edu/ElemStatLearn

## 624 16. Ensemble Learning

of the hidden units (basis functions), along with how to combine them. Bishop (2006) discusses neural networks in some detail, along with the Bayesian perspective (MacKay, 1992; Neal, 1996). Support vector machines (Chapter 12) can also be regarded as an ensemble method; they perform  $L_2$  regularized model fitting in high-dimensional feature spaces. Boosting and lasso exploit sparsity through  $L_1$  regularization to overcome the highdimensionality, while SVMs rely on the "kernel trick" characteristic of  $L_2$ regularization.

C5.0 (Quinlan, 2004) is a commercial tree and rule generation package, with some goals in common with Rulefit.

There is a vast and varied literature often referred to as "combining classifiers" which abounds in ad-hoc schemes for mixing methods of different types to achieve better performance. For a principled approach, see Kittler et al. (1998).

# **Exercises**

Ex. 16.1 Describe exactly how to generate the block correlated data used in the simulation in Section 16.2.3.

Ex. 16.2 Let  $\alpha(t) \in \mathbb{R}^p$  be a piecewise-differentiable and continuous coefficient profile, with  $\alpha(0) = 0$ . The  $L_1$  arc-length of  $\alpha$  from time 0 to t is defined by

$$
\Lambda(t) = \int_0^t |\dot{\alpha}(t)|_1 dt.
$$
\n(16.16)

Show that  $\Lambda(t) \geq |\alpha(t)|_1$ , with equality iff  $\alpha(t)$  is monotone.

Ex. 16.3 Show that fitting a linear regression model using rules 1, 4, 5 and 6 in equation (16.14) gives the same fit as the regression tree corresponding to this tree. Show the same is true for classification, if a logistic regression model is fit.

Ex. 16.4 Program and run the simulation study described in Figure 16.2.

This is page 625 Printer: Opaque this

# 17 Undirected Graphical Models

# 17.1 Introduction

A graph consists of a set of vertices (nodes), along with a set of edges joining some pairs of the vertices. In graphical models, each vertex represents a random variable, and the graph gives a visual way of understanding the joint distribution of the entire set of random variables. They can be useful for either unsupervised or supervised learning. In an *undirected graph*, the edges have no directional arrows. We restrict our discussion to undirected graphical models, also known as Markov random fields or Markov networks. In these graphs, the absence of an edge between two vertices has a special meaning: the corresponding random variables are conditionally independent, given the other variables.

Figure 17.1 shows an example of a graphical model for a flow-cytometry dataset with  $p = 11$  proteins measured on  $N = 7466$  cells, from Sachs et al. (2005). Each vertex in the graph corresponds to the real-valued expression level of a protein. The network structure was estimated assuming a multivariate Gaussian distribution, using the graphical lasso procedure discussed later in this chapter.

Sparse graphs have a relatively small number of edges, and are convenient for interpretation. They are useful in a variety of domains, including genomics and proteomics, where they provide rough models of cell pathways. Much work has been done in defining and understanding the structure of graphical models; see the Bibliographic Notes for references.

## 626 17. Undirected Graphical Models

Image /page/121/Figure/1 description: This is a network diagram showing the relationships between various biological molecules. The molecules are represented by circles with their names labeled next to them. The labels include Raf, Mek, Plcg, PIP2, PIP3, Erk, Akt, PKA, PKC, P38, and Jnk. Lines connect different molecules, indicating a relationship or interaction between them. Specifically, Mek is connected to Raf, Plcg, PIP2, and P38. Plcg is connected to Mek, PIP2, and P38. PIP2 is connected to Mek, Plcg, PIP3, Erk, Akt, PKA, and P38. PIP3 is connected to PIP2 and Erk. Erk is connected to PIP2, PIP3, Akt, and P38. Akt is connected to PIP2, Erk, PKA, and P38. PKA is connected to PIP2, Akt, and P38. PKC is connected to P38. P38 is connected to Mek, Plcg, PIP2, Erk, Akt, PKA, and PKC. Jnk is connected to Mek and P38.

FIGURE 17.1. Example of a sparse undirected graph, estimated from a flow cytometry dataset, with  $p = 11$  proteins measured on  $N = 7466$  cells. The network structure was estimated using the graphical lasso procedure discussed in this chapter.

As we will see, the edges in a graph are parametrized by values or potentials that encode the strength of the conditional dependence between the random variables at the corresponding vertices. The main challenges in working with graphical models are model selection (choosing the structure of the graph), estimation of the edge parameters from data, and computation of marginal vertex probabilities and expectations, from their joint distribution. The last two tasks are sometimes called *learning* and *inference* in the computer science literature.

We do not attempt a comprehensive treatment of this interesting area. Instead, we introduce some basic concepts, and then discuss a few simple methods for estimation of the parameters and structure of undirected graphical models; methods that relate to the techniques already discussed in this book. The estimation approaches that we present for continuous and discrete-valued vertices are different, so we treat them separately. Sections 17.3.1 and 17.3.2 may be of particular interest, as they describe new, regression-based procedures for estimating graphical models.

There is a large and active literature on directed graphical models or Bayesian networks; these are graphical models in which the edges have directional arrows (but no directed cycles). Directed graphical models represent probability distributions that can be factored into products of conditional distributions, and have the potential for causal interpretations. We refer the reader to Wasserman (2004) for a brief overview of both undirected and directed graphs; the next section follows closely his Chapter 18.

Image /page/122/Figure/1 description: The image displays four different graphs labeled (a), (b), (c), and (d). Graph (a) shows three nodes labeled X, Y, and Z, with edges connecting Y to X and Y to Z. Graph (b) shows four nodes labeled X, Y, W, and Z. Edges connect Y to X, Y to W, and X to W. Node Z is isolated. Graph (c) shows four nodes labeled Y, X, W, and Z, arranged in a square with edges connecting Y to X, X to W, W to Z, and Z to Y. Graph (d) shows four nodes labeled X, Y, Z, and W arranged in a line, with edges connecting X to Y, Y to Z, and Z to W.

FIGURE 17.2. Examples of undirected graphical models or Markov networks. Each node or vertex represents a random variable, and the lack of an edge between two nodes indicates conditional independence. For example, in graph (a), X and  $Z$  are conditionally independent, given  $Y$ . In graph  $(b)$ ,  $Z$  is independent of each of  $X$ ,  $Y$ , and  $W$ .

A longer list of useful references is given in the Bibliographic Notes on page 645.

# 17.2 Markov Graphs and Their Properties

In this section we discuss the basic properties of graphs as models for the joint distribution of a set of random variables. We defer discussion of (a) parametrization and estimation of the edge parameters from data, and (b) estimation of the topology of a graph, to later sections.

Figure 17.2 shows four examples of undirected graphs. A graph  $\mathcal G$  consists of a pair  $(V, E)$ , where V is a set of vertices and E the set of edges (defined by pairs of vertices). Two vertices  $X$  and  $Y$  are called *adjacent* if there is a edge joining them; this is denoted by  $X \sim Y$ . A path  $X_1, X_2, \ldots, X_n$ is a set of vertices that are joined, that is  $X_{i-1} \sim X_i$  for  $i = 2, \ldots, n$ . A complete graph is a graph with every pair of vertices joined by an edge. A subgraph  $U \in V$  is a subset of vertices together with their edges. For example,  $(X, Y, Z)$  in Figure 17.2(a) form a path but not a complete graph.

Suppose that we have a graph  $\mathcal G$  whose vertex set V represents a set of random variables having joint distribution  $P$ . In a Markov graph  $\mathcal{G}$ , the absence of an edge implies that the corresponding random variables are conditionally independent given the variables at the other vertices. This is expressed with the following notation:

628 17. Undirected Graphical Models

No edge joining X and  $Y \iff X \perp Y$  |rest (17.1)

where "rest" refers to all of the other vertices in the graph. For example in Figure 17.2(a)  $X \perp Z|Y$ . These are known as the *pairwise Markov* independencies of G.

If  $A, B$  and  $C$  are subgraphs, then  $C$  is said to *separate*  $A$  and  $B$  if every path between A and B intersects a node in C. For example, Y separates X and Z in Figures 17.2(a) and (d), and Z separates Y and W in (d). In Figure 17.2(b) Z is not connected to  $X, Y, W$  so we say that the two sets are separated by the empty set. In Figure 17.2(c),  $C = \{X, Z\}$  separates Y and W.

Separators have the nice property that they break the graph into conditionally independent pieces. Specifically, in a Markov graph  $G$  with subgraphs  $A, B$  and  $C$ ,

if C separates A and B then 
$$
A \perp B|C
$$
. (17.2)

These are known as the *global Markov properties* of G. It turns out that the pairwise and global Markov properties of a graph are equivalent (for graphs with positive distributions). That is, the set of graphs with associated probability distributions that satisfy the pairwise Markov independencies and global Markov assumptions are the same. This result is useful for inferring global independence relations from simple pairwise properties. For example in Figure 17.2(d)  $X \perp Z | \{Y, W\}$  since it is a Markov graph and there is no link joining  $X$  and  $Z$ . But  $Y$  also separates  $X$  from  $Z$  and  $W$  and hence by the global Markov assumption we conclude that  $X \perp Z|Y$  and  $X \perp W|Y$ . Similarly we have  $Y \perp W/Z$ .

The global Markov property allows us to decompose graphs into smaller more manageable pieces and thus leads to essential simplifications in computation and interpretation. For this purpose we separate the graph into cliques. A clique is a complete subgraph— a set of vertices that are all adjacent to one another; it is called maximal if it is a clique and no other vertices can be added to it and still yield a clique. The maximal cliques for the graphs of Figure 17.2 are

- (a)  $\{X, Y\}, \{Y, Z\},\$
- (b)  $\{X, Y, W\}, \{Z\},\$
- (c)  $\{X, Y\}, \{Y, Z\}, \{Z, W\}, \{X, W\}, \text{and}$
- (d)  $\{X, Y\}, \{Y, Z\}, \{Z, W\}.$

Although the following applies to both continuous and discrete distributions, much of the development has been for the latter. A probability density function  $f$  over a Markov graph  $\mathcal G$  can be can represented as

17.2 Markov Graphs and Their Properties 629

$$
f(x) = \frac{1}{Z} \prod_{C \in \mathcal{C}} \psi_C(x_C)
$$
 (17.3)

where C is the set of maximal cliques, and the positive functions  $\psi_C(\cdot)$  are called *clique potentials*. These are not in general density functions<sup>1</sup>, but rather are affinities that capture the dependence in  $X_C$  by scoring certain instances  $x_C$  higher than others. The quantity

$$
Z = \sum_{x \in \mathcal{X}} \prod_{C \in \mathcal{C}} \psi_C(x_C) \tag{17.4}
$$

is the normalizing constant, also known as the partition function. Alternatively, the representation (17.3) implies a graph with independence properties defined by the cliques in the product. This result holds for Markov networks  $G$  with positive distributions, and is known as the *Hammersley*-Clifford theorem (Hammersley and Clifford, 1971; Clifford, 1990).

Many of the methods for estimation and computation on graphs first decompose the graph into its maximal cliques. Relevant quantities are computed in the individual cliques and then accumulated across the entire graph. A prominent example is the join tree or junction tree algorithm for computing marginal and low order probabilities from the joint distribution on a graph. Details can be found in Pearl (1986), Lauritzen and Spiegelhalter (1988), Pearl (1988), Shenoy and Shafer (1988), Jensen et al. (1990), or Koller and Friedman (2007).

Image /page/124/Figure/6 description: A diagram shows three green circles labeled X, Y, and Z. Lines connect X to Y, Y to Z, and X to Z, forming a triangle.

FIGURE 17.3. A complete graph does not uniquely specify the higher-order dependence structure in the joint distribution of the variables.

A graphical model does not always uniquely specify the higher-order dependence structure of a joint probability distribution. Consider the complete three-node graph in Figure 17.3. It could represent the dependence structure of either of the following distributions:

$$
f^{(2)}(x, y, z) = \frac{1}{Z} \psi(x, y) \psi(x, z) \psi(y, z);
$$
  
\n
$$
f^{(3)}(x, y, z) = \frac{1}{Z} \psi(x, y, z).
$$
\n(17.5)

The first specifies only second order dependence (and can be represented with fewer parameters). Graphical models for discrete data are a special

<sup>&</sup>lt;sup>1</sup>If the cliques are separated, then the potentials can be densities, but this is in general not the case.

### 630 17. Undirected Graphical Models

case of loglinear models for multiway contingency tables (Bishop et al., 1975, e.g.); in that language  $f^{(2)}$  is referred to as the "no second-order interaction" model.

For the remainder of this chapter we focus on pairwise Markov graphs (Koller and Friedman, 2007). Here there is a potential function for each edge (pair of variables as in  $f^{(2)}$  above), and at most second-order interactions are represented. These are more parsimonious in terms of parameters, easier to work with, and give the minimal complexity implied by the graph structure. The models for both continuous and discrete data are functions of only the pairwise marginal distributions of the variables represented in the edge set.

# 17.3 Undirected Graphical Models for Continuous Variables

Here we consider Markov networks where all the variables are continuous. The Gaussian distribution is almost always used for such graphical models, because of its convenient analytical properties. We assume that the observations have a multivariate Gaussian distribution with mean  $\mu$  and covariance matrix  $\Sigma$ . Since the Gaussian distribution represents at most second-order relationships, it automatically encodes a pairwise Markov graph. The graph in Figure 17.1 is an example of a Gaussian graphical model.

The Gaussian distribution has the property that all conditional distributions are also Gaussian. The inverse covariance matrix  $\Sigma^{-1}$  contains information about the partial covariances between the variables; that is, the covariances between pairs  $i$  and  $j$ , conditioned on all other variables. In particular, if the *ij*th component of  $\Theta = \Sigma^{-1}$  is zero, then variables *i* and  $j$  are conditionally independent, given the other variables (Exercise 17.3).

It is instructive to examine the conditional distribution of one variable versus the rest, where the role of  $\Theta$  is explicit. Suppose we partition  $X =$  $(Z, Y)$  where  $Z = (X_1, \ldots, X_{p-1})$  consists of the first  $p-1$  variables and  $Y = X_p$  is the last. Then we have the conditional distribution of Y give Z (Mardia et al., 1979, e.g.)

$$
Y|Z = z \sim N(\mu_Y + (z - \mu_Z)^T \Sigma_{ZZ}^{-1} \sigma_{ZY}, \sigma_{YY} - \sigma_{ZY}^T \Sigma_{ZZ}^{-1} \sigma_{ZY}), \quad (17.6)
$$

where we have partitioned  $\Sigma$  as

$$
\Sigma = \begin{pmatrix} \Sigma_{ZZ} & \sigma_{ZY} \\ \sigma_{ZY}^T & \sigma_{YY} \end{pmatrix} . \tag{17.7}
$$

The conditional mean in (17.6) has exactly the same form as the population multiple linear regression of  $Y$  on  $Z$ , with regression coefficient  $\beta = \sum_{ZZ}^{-1} \sigma_{ZY}$  [see (2.16) on page 19]. If we partition  $\Theta$  in the same way, since  $\Sigma\Theta = I$  standard formulas for partitioned inverses give

17.3 Undirected Graphical Models for Continuous Variables 631

$$
\theta_{ZY} = -\theta_{YY} \cdot \Sigma_{ZZ}^{-1} \sigma_{ZY},\tag{17.8}
$$

where  $1/\theta_{YY} = \sigma_{YY} - \sigma_{ZY}^T \Sigma_{ZZ}^{-1} \sigma_{ZY} > 0$ . Hence

$$
\begin{array}{rcl}\n\beta & = & \Sigma_{ZZ}^{-1} \sigma_{ZY} \\
& = & -\theta_{ZY} / \theta_{YY}.\n\end{array} \tag{17.9}
$$

We have learned two things here:

- The dependence of Y on Z in  $(17.6)$  is in the mean term alone. Here we see explicitly that zero elements in  $\beta$  and hence  $\theta_{ZY}$  mean that the corresponding elements of  $Z$  are conditionally independent of  $Y$ , given the rest.
- We can learn about this dependence structure through multiple linear regression.

Thus Θ captures all the second-order information (both structural and quantitative) needed to describe the conditional distribution of each node given the rest, and is the so-called "natural" parameter for the Gaussian graphical model<sup>2</sup>.

Another (different) kind of graphical model is the covariance graph or relevance network, in which vertices are connected by bidirectional edges if the covariance (rather than the partial covariance) between the corresponding variables is nonzero. These are popular in genomics, see especially Butte et al. (2000). The negative log-likelihood from these models is not convex, making the computations more challenging (Chaudhuri et al., 2007).

# 17.3.1 Estimation of the Parameters when the Graph Structure is Known

Given some realizations of  $X$ , we would like to estimate the parameters of an undirected graph that approximates their joint distribution. Suppose first that the graph is complete (fully connected). We assume that we have N multivariate normal realizations  $x_i$ ,  $i = 1, ..., N$  with population mean  $\mu$  and covariance  $\Sigma$ . Let

$$
\mathbf{S} = \frac{1}{N} \sum_{i=1}^{N} (x_i - \bar{x})(x_i - \bar{x})^T
$$
 (17.10)

be the empirical covariance matrix, with  $\bar{x}$  the sample mean vector. Ignoring constants, the log-likelihood of the data can be written as

<sup>2</sup>The distribution arising from a Gaussian graphical model is a Wishart distribution. This is a member of the exponential family, with canonical or "natural" parameter  $\Theta = \Sigma^{-1}$ . Indeed, the partially maximized log-likelihood (17.11) is (up to constants) the Wishart log-likelihood.

632 17. Undirected Graphical Models

$$
\ell(\Theta) = \log \det \Theta - \text{trace}(\mathbf{S}\Theta). \tag{17.11}
$$

In (17.11) we have partially maximized with respect to the mean parameter  $\mu$ . The quantity  $-\ell(\Theta)$  is a convex function of  $\Theta$ . It is easy to show that the maximum likelihood estimate of  $\Sigma$  is simply S.

Now to make the graph more useful (especially in high-dimensional settings) let's assume that some of the edges are missing; for example, the edge between PIP3 and Erk is one of several missing in Figure 17.1. As we have seen, for the Gaussian distribution this implies that the corresponding entries of  $\Theta = \Sigma^{-1}$  are zero. Hence we now would like to maximize (17.11) under the constraints that some pre-defined subset of the parameters are zero. This is an equality-constrained convex optimization problem, and a number of methods have been proposed for solving it, in particular the iterative proportional fitting procedure (Speed and Kiiveri, 1986). This and other methods are summarized for example in Whittaker (1990) and Lauritzen (1996). These methods exploit the simplifications that arise from decomposing the graph into its maximal cliques, as described in the previous section. Here we outline a simple alternate approach, that exploits the sparsity in a different way. The fruits of this approach will become apparent later when we discuss the problem of estimation of the graph structure.

The idea is based on linear regression, as inspired by (17.6) and (17.9). In particular, suppose that we want to estimate the edge parameters  $\theta_{ij}$  for the vertices that are joined to a given vertex  $i$ , restricting those that are not joined to be zero. Then it would seem that the linear regression of the node i values on the other relevant vertices might provide a reasonable estimate. But this ignores the dependence structure among the predictors in this regression. It turns out that if instead we use our current (model-based) estimate of the cross-product matrix of the predictors when we perform our regressions, this gives the correct solutions and solves the constrained maximum-likelihood problem exactly. We now give details.

To constrain the log-likelihood (17.11), we add Lagrange constants for all missing edges

$$
\ell_C(\mathbf{\Theta}) = \log \det \mathbf{\Theta} - \text{trace}(\mathbf{S}\mathbf{\Theta}) - \sum_{(j,k)\notin E} \gamma_{jk} \theta_{jk}.
$$
 (17.12)

The gradient equation for maximizing (17.12) can be written as

$$
\Theta^{-1} - \mathbf{S} - \mathbf{\Gamma} = \mathbf{0},\tag{17.13}
$$

using the fact that the derivative of log det  $\Theta$  equals  $\Theta^{-1}$  (Boyd and Vandenberghe, 2004, for example, page 641).  $\Gamma$  is a matrix of Lagrange parameters with nonzero values for all pairs with edges absent.

We will show how we can use regression to solve for  $\Theta$  and its inverse  $W = \Theta^{-1}$  one row and column at a time. For simplicity let's focus on the last row and column. Then the upper right block of equation (17.13) can be written as

17.3 Undirected Graphical Models for Continuous Variables 633

$$
w_{12} - s_{12} - \gamma_{12} = 0. \tag{17.14}
$$

Here we have partitioned the matrices into two parts as in (17.7): part 1 being the first  $p-1$  rows and columns, and part 2 the pth row and column. With **W** and its inverse  $\Theta$  partitioned in a similar fashion, we have

$$
\begin{pmatrix} \mathbf{W}_{11} & w_{12} \\ w_{12}^T & w_{22} \end{pmatrix} \begin{pmatrix} \Theta_{11} & \theta_{12} \\ \theta_{12}^T & \theta_{22} \end{pmatrix} = \begin{pmatrix} \mathbf{I} & 0 \\ 0^T & 1 \end{pmatrix}.
$$
 (17.15)

This implies

$$
w_{12} = -\mathbf{W}_{11}\theta_{12}/\theta_{22} \tag{17.16}
$$

$$
= \mathbf{W}_{11}\beta \tag{17.17}
$$

where  $\beta = -\theta_{12}/\theta_{22}$  as in (17.9). Now substituting (17.17) into (17.14) gives

$$
\mathbf{W}_{11}\beta - s_{12} - \gamma_{12} = 0. \tag{17.18}
$$

These can be interpreted as the  $p-1$  estimating equations for the constrained regression of  $X_p$  on the other predictors, except that the observed mean cross-products matrix  $S_{11}$  is replaced by  $W_{11}$ , the current estimated covariance matrix from the model.

Now we can solve (17.18) by simple subset regression. Suppose there are  $p-q$  nonzero elements in  $\gamma_{12}$ —i.e.,  $p-q$  edges constrained to be zero. These  $p - q$  rows carry no information and can be removed. Furthermore we can reduce  $\beta$  to  $\beta^*$  by removing its  $p - q$  zero elements, yielding the reduced  $q \times q$  system of equations

$$
\mathbf{W}_{11}^* \beta^* - s_{12}^* = 0, \tag{17.19}
$$

with solution  $\hat{\beta}^* = \mathbf{W}_{11}^*^{-1} s_{12}^*$ . This is padded with  $p - q$  zeros to give  $\hat{\beta}$ .

Although it appears from (17.16) that we only recover the elements  $\theta_{12}$ up to a scale factor  $1/\theta_{22}$ , it is easy to show that

$$
\frac{1}{\theta_{22}} = w_{22} - w_{12}^T \beta \tag{17.20}
$$

(using partitioned inverse formulas). Also  $w_{22} = s_{22}$ , since the diagonal of  $\Gamma$  in (17.13) is zero.

This leads to the simple iterative procedure given in Algorithm 17.1 for estimating both  $\hat{W}$  and its inverse  $\hat{\Theta}$ , subject to the constraints of the missing edges.

Note that this algorithm makes conceptual sense. The graph estimation problem is not p separate regression problems, but rather p coupled problems. The use of the common  $W$  in step (b), in place of the observed cross-products matrix, couples the problems together in the appropriate fashion. Surprisingly, we were not able to find this procedure in the literature. However it is related to the covariance selection procedures of Algorithm 17.1 A Modified Regression Algorithm for Estimation of an Undirected Gaussian Graphical Model with Known Structure.

1. Initialize  $W = S$ .

- 2. Repeat for  $j = 1, 2, \ldots, p$  until convergence:
  - (a) Partition the matrix  $W$  into part 1: all but the *j*th row and column, and part 2: the jth row and column.
  - (b) Solve  $\mathbf{W}_{11}^* \beta^* s_{12}^* = 0$  for the unconstrained edge parameters  $\beta^*$ , using the reduced system of equations as in (17.19). Obtain  $\hat{\beta}$  by padding  $\hat{\beta}^*$  with zeros in the appropriate positions.
  - (c) Update  $w_{12} = \mathbf{W}_{11} \hat{\beta}$
- 3. In the final cycle (for each j) solve for  $\hat{\theta}_{12} = -\hat{\beta} \cdot \hat{\theta}_{22}$ , with  $1/\hat{\theta}_{22} =$  $s_{22} - w_{12}^T \hat{\beta}.$

Image /page/129/Figure/8 description: The image displays a graph with four nodes labeled X1, X2, X3, and X4, arranged in a square formation. Lines connect X1 to X2, X2 to X3, X3 to X4, and X4 to X1, forming a cycle. To the right of the graph, a 4x4 matrix labeled 'S' is presented. The matrix contains the following numerical values: the first row is [10, 1, 5, 4], the second row is [1, 10, 2, 6], the third row is [5, 2, 10, 3], and the fourth row is [4, 6, 3, 10].

FIGURE 17.4. A simple graph for illustration, along with the empirical covariance matrix.

Dempster (1972), and is similar in flavor to the iterative conditional fitting procedure for covariance graphs, proposed by Chaudhuri et al. (2007).

Here is a little example, borrowed from Whittaker (1990). Suppose that our model is as depicted in Figure 17.4, along with its empirical covariance matrix S. We apply algorithm (17.1) to this problem; for example, in the modified regression for variable 1 in step (b), variable 3 is left out. The procedure quickly converged to the solutions:

$$
\hat{\Sigma} = \begin{pmatrix}\n10.00 & 1.00 & 1.31 & 4.00 \\
1.00 & 10.00 & 2.00 & 0.87 \\
1.31 & 2.00 & 10.00 & 3.00 \\
4.00 & 0.87 & 3.00 & 10.00\n\end{pmatrix}, \quad\n\hat{\Sigma}^{-1} = \begin{pmatrix}\n0.12 & -0.01 & 0.00 & -0.05 \\
-0.01 & 0.11 & -0.02 & 0.00 \\
0.00 & -0.02 & 0.11 & -0.03 \\
-0.05 & 0.00 & -0.03 & 0.13\n\end{pmatrix}.
$$

Note the zeroes in  $\hat{\Sigma}^{-1}$ , corresponding to the missing edges (1,3) and (2,4). Note also that the corresponding elements in  $\hat{\Sigma}$  are the only elements different from S. The estimation of  $\hat{\Sigma}$  is an example of what is sometimes called the positive definite "completion" of S.

## 17.3.2 Estimation of the Graph Structure

In most cases we do not know which edges to omit from our graph, and so would like to try to discover this from the data itself. In recent years a number of authors have proposed the use of  $L_1$  (lasso) regularization for this purpose.

Meinshausen and Bühlmann (2006) take a simple approach to the problem: rather than trying to fully estimate  $\Sigma$  or  $\Theta = \Sigma^{-1}$ , they only estimate which components of  $\theta_{ij}$  are nonzero. To do this, they fit a lasso regression using each variable as the response and the others as predictors. The component  $\theta_{ij}$  is then estimated to be nonzero if either the estimated coefficient of variable i on j is nonzero, on the estimated coefficient of variable j on  $i$  is nonzero (alternatively they use an AND rule). They show that asymptotically this procedure consistently estimates the set of nonzero elements of Θ.

We can take a more systematic approach with the lasso penalty, following the development of the previous section. Consider maximizing the penalized log-likelihood

$$
\log \det \mathbf{\Theta} - \text{trace}(\mathbf{S}\mathbf{\Theta}) - \lambda ||\mathbf{\Theta}||_1, \tag{17.21}
$$

where  $||\mathbf{\Theta}||_1$  is the  $L_1$  norm—the sum of the absolute values of the elements of  $\Sigma^{-1}$ , and we have ignored constants. The negative of this penalized likelihood is a convex function of Θ.

It turns out that one can adapt the lasso to give the exact maximizer of the penalized log-likelihood. In particular, we simply replace the modified regression step (b) in Algorithm 17.1 by a modified lasso step. Here are the details.

The analog of the gradient equation (17.13) is now

$$
\Theta^{-1} - \mathbf{S} - \lambda \cdot \text{Sign}(\Theta) = 0. \tag{17.22}
$$

Here we use sub-gradient notation, with  $\text{Sign}(\theta_{jk}) = \text{sign}(\theta_{jk})$  if  $\theta_{jk} \neq 0$ , else  $Sign(\theta_{jk}) \in [-1, 1]$  if  $\theta_{jk} = 0$ . Continuing the development in the previous section, we reach the analog of (17.18)

$$
\mathbf{W}_{11}\boldsymbol{\beta} - s_{12} + \lambda \cdot \text{Sign}(\boldsymbol{\beta}) = 0 \qquad (17.23)
$$

(recall that  $\beta$  and  $\theta_{12}$  have opposite signs). We will now see that this system is exactly equivalent to the estimating equations for a lasso regression.

Consider the usual regression setup with outcome variables y and predictor matrix Z. There the lasso minimizes

$$
\frac{1}{2}(\mathbf{y} - \mathbf{Z}\beta)^{T}(\mathbf{y} - \mathbf{Z}\beta) + \lambda \cdot ||\beta||_{1}
$$
 (17.24)

[see  $(3.52)$  on page 68; here we have added a factor  $\frac{1}{2}$  for convenience]. The gradient of this expression is

## Algorithm 17.2 Graphical Lasso.

- 1. Initialize  $W = S + \lambda I$ . The diagonal of W remains unchanged in what follows.
- 2. Repeat for  $j = 1, 2, \ldots, p, 1, 2, \ldots, p, \ldots$  until convergence:
  - (a) Partition the matrix  $\bf{W}$  into part 1: all but the *j*th row and column, and part 2: the jth row and column.
  - (b) Solve the estimating equations  $\mathbf{W}_{11}\beta s_{12} + \lambda \cdot \text{Sign}(\beta) = 0$ using the cyclical coordinate-descent algorithm (17.26) for the modified lasso.
  - (c) Update  $w_{12} = \mathbf{W}_{11} \hat{\beta}$
- 3. In the final cycle (for each j) solve for  $\hat{\theta}_{12} = -\hat{\beta} \cdot \hat{\theta}_{22}$ , with  $1/\hat{\theta}_{22} =$  $w_{22} - w_{12}^T \hat{\beta}.$

$$
\mathbf{Z}^T \mathbf{Z} \beta - \mathbf{Z}^T \mathbf{y} + \lambda \cdot \text{Sign}(\beta) = 0 \tag{17.25}
$$

So up to a factor  $1/N$ ,  $\mathbf{Z}^T \mathbf{y}$  is the analog of  $s_{12}$ , and we replace  $\mathbf{Z}^T \mathbf{Z}$  by  $W_{11}$ , the estimated cross-product matrix from our current model.

The resulting procedure is called the graphical lasso, proposed by Friedman et al. (2008b) building on the work of Banerjee et al. (2008). It is summarized in Algorithm 17.2.

Friedman et al. (2008b) use the pathwise coordinate descent method (Section 3.8.6) to solve the modified lasso problem at each stage. Here are the details of pathwise coordinate descent for the graphical lasso algorithm. Letting  $V = W_{11}$ , the update has the form

$$
\hat{\beta}_j \leftarrow S\Big(s_{12j} - \sum_{k \neq j} V_{kj} \hat{\beta}_k, \lambda\Big) / V_{jj} \tag{17.26}
$$

for  $j = 1, 2, \ldots, p - 1, 1, 2, \ldots, p - 1, \ldots$ , where S is the soft-threshold operator:

$$
S(x,t) = sign(x)(|x| - t) +.
$$
 (17.27)

The procedure cycles through the predictors until convergence.

It is easy to show that the diagonal elements  $w_{jj}$  of the solution matrix **W** are simply  $s_{jj} + \lambda$ , and these are fixed in step 1 of Algorithm 17.2<sup>3</sup>.

The graphical lasso algorithm is extremely fast, and can solve a moderately sparse problem with 1000 nodes in less than a minute. It is easy to modify the algorithm to have edge-specific penalty parameters  $\lambda_{ik}$ ; since

<sup>3</sup>An alternative formulation of the problem (17.21) can be posed, where we don't penalize the diagonal of  $\Theta$ . Then the diagonal elements  $w_{jj}$  of the solution matrix are  $s_{ij}$ , and the rest of the algorithm is unchanged.

 $\lambda_{jk} = \infty$  will force  $\hat{\theta}_{jk}$  to be zero, this algorithm subsumes Algorithm 17.1. By casting the sparse inverse-covariance problem as a series of regressions, one can also quickly compute and examine the solution paths as a function of the penalty parameter  $\lambda$ . More details can be found in Friedman et al. (2008b).

Image /page/132/Figure/2 description: The image displays four network diagrams, each labeled with a different value of lambda: \u03bb = 36, \u03bb = 27, \u03bb = 7, and \u03bb = 0. Each diagram shows nodes representing biological entities such as Raf, Mek, Plcg, PIP2, PIP3, Erk, Akt, PKA, PKC, P38, and Jnk. Lines connect these nodes, indicating relationships within the network. The complexity of the connections increases as the lambda value decreases, with \u03bb = 0 showing a highly interconnected, almost complete graph.

FIGURE 17.5. Four different graphical-lasso solutions for the flow-cytometry data.

Figure 17.1 shows the result of applying the graphical lasso to the flowcytometry dataset. Here the lasso penalty parameter  $\lambda$  was set at 14. In practice it is informative to examine the different sets of graphs that are obtained as  $\lambda$  is varied. Figure 17.5 shows four different solutions. The graph becomes more sparse as the penalty parameter is increased.

Finally note that the values at some of the nodes in a graphical model can be unobserved; that is, missing or hidden. If only some values are missing at a node, the EM algorithm can be used to impute the missing values

### 638 17. Undirected Graphical Models

(Exercise 17.9). However, sometimes the entire node is hidden or latent. In the Gaussian model, if a node has all missing values, due to linearity one can simply average over the missing nodes to yield another Gaussian model over the observed nodes. Hence the inclusion of hidden nodes does not enrich the resulting model for the observed nodes; in fact, it imposes additional structure on its covariance matrix. However in the discrete model (described next) the inherent nonlinearities make hidden units a powerful way of expanding the model.

# 17.4 Undirected Graphical Models for Discrete Variables

Undirected Markov networks with all discrete variables are popular, and in particular pairwise Markov networks with binary variables being the most common. They are sometimes called *Ising models* in the statistical mechanics literature, and *Boltzmann machines* in the machine learning literature, where the vertices are referred to as "nodes" or "units" and are binary-valued.

In addition, the values at each node can be observed ("visible") or unobserved ("hidden"). The nodes are often organized in layers, similar to a neural network. Boltzmann machines are useful both for unsupervised and supervised learning, especially for structured input data such as images, but have been hampered by computational difficulties. Figure 17.6 shows a restricted Boltzmann machine (discussed later), in which some variables are hidden, and only some pairs of nodes are connected. We first consider the simpler case in which all p nodes are visible with edge pairs  $(j, k)$  enumerated in E.

Denoting the binary valued variable at node j by  $X_j$ , the Ising model for their joint probabilities is given by

$$
p(X, \Theta) = \exp\left[\sum_{(j,k)\in E} \theta_{jk} X_j X_k - \Phi(\Theta)\right] \text{ for } X \in \mathcal{X},\tag{17.28}
$$

with  $\mathcal{X} = \{0, 1\}^p$ . As with the Gaussian model of the previous section, only pairwise interactions are modeled. The Ising model was developed in statistical mechanics, and is now used more generally to model the joint effects of pairwise interactions.  $\Phi(\Theta)$  is the log of the partition function, and is defined by

$$
\Phi(\mathbf{\Theta}) = \log \sum_{x \in \mathcal{X}} \Big[ \exp \Big( \sum_{(j,k) \in E} \theta_{jk} x_j x_k \Big) \Big]. \tag{17.29}
$$

The partition function ensures that the probabilities add to one over the sample space. The terms  $\theta_{jk}X_jX_k$  represent a particular parametrization of the (log) potential functions (17.5), and for technical reasons requires a constant node  $X_0 \equiv 1$  to be included (Exercise 17.10), with "edges" to all the other nodes. In the statistics literature, this model is equivalent to a first-order-interaction Poisson log-linear model for multiway tables of counts (Bishop et al., 1975; McCullagh and Nelder, 1989; Agresti, 2002).

The Ising model implies a logistic form for each node conditional on the others (exercise 17.11):

$$
\Pr(X_j = 1 | X_{-j} = x_{-j}) = \frac{1}{1 + \exp(-\theta_{j0} - \sum_{(j,k) \in E} \theta_{jk} x_k)},\tag{17.30}
$$

where  $X_{-j}$  denotes all of the nodes except j. Hence the parameter  $\theta_{jk}$ measures the dependence of  $X_j$  on  $X_k$ , conditional on the other nodes.

# 17.4.1 Estimation of the Parameters when the Graph Structure is Known

Given some data from this model, how can we estimate the parameters? Suppose we have observations  $x_i = (x_{i1}, x_{i2}, \dots, x_{ip}) \in \{0, 1\}^p$ ,  $i = 1, \dots, N$ . The log-likelihood is

$$
\ell(\Theta) = \sum_{i=1}^{N} \log \Pr_{\Theta}(X_i = x_i)
$$

$$
= \sum_{i=1}^{N} 
\left[ 
\sum_{(j,k) \in E} \theta_{jk} x_{ij} x_{ik} - \Phi(\Theta)
\right]
$$
 $(17.31)$ 

The gradient of the log-likelihood is

$$
\frac{\partial \ell(\mathbf{\Theta})}{\partial \theta_{jk}} = \sum_{i=1}^{N} x_{ij} x_{ik} - N \frac{\partial \Phi(\mathbf{\Theta})}{\partial \theta_{jk}}
$$
(17.32)

and

$$
\frac{\partial \Phi(\mathbf{\Theta})}{\partial \theta_{jk}} = \sum_{x \in \mathcal{X}} x_j x_k \cdot p(x, \mathbf{\Theta}) \\ = \mathbf{E}_{\mathbf{\Theta}}(X_j X_k) \quad (17.33)
$$

Setting the gradient to zero gives

$$
\hat{\mathbf{E}}(X_j X_k) - \mathbf{E}_{\Theta}(X_j X_k) = 0 \tag{17.34}
$$

where we have defined

640 17. Undirected Graphical Models

$$
\hat{E}(X_j X_k) = \frac{1}{N} \sum_{i=1}^{N} x_{ij} x_{ik},
$$
\n(17.35)

the expectation taken with respect to the empirical distribution of the data. Looking at (17.34), we see that the maximum likelihood estimates simply match the estimated inner products between the nodes to their observed inner products. This is a standard form for the score (gradient) equation for exponential family models, in which sufficient statistics are set equal to their expectations under the model.

To find the maximum likelihood estimates, we can use gradient search or Newton methods. However the computation of  $E_{\Theta}(X_i X_k)$  involves enumeration of  $p(X, \Theta)$  over  $2^{p-2}$  of the  $|\mathcal{X}| = 2^p$  possible values of X, and is not generally feasible for large  $p$  (e.g., larger than about 30). For smaller p, a number of standard statistical approaches are available:

- Poisson log-linear modeling, where we treat the problem as a large regression problem (Exercise 17.12). The response vector y is the vector of  $2^p$  counts in each of the cells of the multiway tabulation of the data<sup>4</sup>. The predictor matrix **Z** has  $2^p$  rows and up to  $1+p+p^2$  columns that characterize each of the cells, although this number depends on the sparsity of the graph. The computational cost is essentially that of a regression problem of this size, which is  $O(p^4 2^p)$  and is manageable for  $p < 20$ . The Newton updates are typically computed by iteratively reweighted least squares, and the number of steps is usually in the single digits. See Agresti (2002) and McCullagh and Nelder (1989) for details. Standard software (such as the R package glm) can be used to fit this model.
- Gradient descent requires at most  $O(p^2 2^{p-2})$  computations to compute the gradient, but may require many more gradient steps than the second–order Newton methods. Nevertheless, it can handle slightly larger problems with  $p \leq 30$ . These computations can be reduced by exploiting the special clique structure in sparse graphs, using the junction-tree algorithm. Details are not given here.
- Iterative proportional fitting (IPF) performs cyclical coordinate descent on the gradient equations (17.34). At each step a parameter is updated so that its gradient equation is exactly zero. This is done in a cyclical fashion until all the gradients are zero. One complete cycle costs the same as a gradient evaluation, but may be more efficient. Jirousek and Přeučil (1995) implement an efficient version of IPF, using junction trees.

<sup>4</sup>Each of the cell counts is treated as an independent Poisson variable. We get the multinomial model corresponding to  $(17.28)$  by conditioning on the total count N (which is also Poisson under this framework).

When p is large  $(> 30)$  other approaches have been used to approximate the gradient.

- The mean field approximation (Peterson and Anderson, 1987) estimates  $E_{\Theta}(X_i X_k)$  by  $E_{\Theta}(X_i)E_{\Theta}(X_i)$ , and replaces the input variables by their means, leading to a set of nonlinear equations for the parameters  $\theta_{jk}$ .
- To obtain near-exact solutions, Gibbs sampling (Section 8.6) is used to approximate  $E_{\Theta}(X_i X_k)$  by successively sampling from the estimated model probabilities  $Pr_{\Theta}(X_j | X_{-j})$  (see e.g. Ripley (1996)).

We have not discussed *decomposable models*, for which the maximum likelihood estimates can be found in closed form without any iteration whatsoever. These models arise, for example, in *trees*: special graphs with tree-structured topology. When computational tractability is a concern, trees represent a useful class of models and they sidestep the computational concerns raised in this section. For details, see for example Chapter 12 of Whittaker (1990).

# 17.4.2 Hidden Nodes

Image /page/136/Picture/6 description: A cartoon drawing of Edvard Munch's The Scream painting. The figure is yellow with wide eyes and an open mouth, holding its hands to its face. The background is black.

We can increase the complexity of a discrete Markov network by including latent or hidden nodes. Suppose that a subset of the variables  $X_{\mathcal{H}}$  are unobserved or "hidden", and the remainder  $X_{\mathcal{V}}$  are observed or "visible." Then the log-likelihood of the observed data is

$$
\ell(\Theta) = \sum_{i=1}^{N} \log[\Pr_{\Theta}(X_{\mathcal{V}} = x_{i\mathcal{V}})]
$$
$$
= \sum_{i=1}^{N} \left[ \log \sum_{x_{\mathcal{H}} \in \mathcal{X}_{\mathcal{H}}} \exp \sum_{(j,k) \in E} (\theta_{jk} x_{ij} x_{ik} - \Phi(\Theta)) \right]. \tag{17.36}
$$

The sum over  $x_{\mathcal{H}}$  means that we are summing over all possible  $\{0, 1\}$  values for the hidden units. The gradient works out to be

$$
\frac{d\ell(\mathbf{\Theta})}{d\theta_{jk}} = \hat{\mathbf{E}}_{\mathcal{V}} \mathbf{E}_{\mathbf{\Theta}}(X_j X_k | X_{\mathcal{V}}) - \mathbf{E}_{\mathbf{\Theta}}(X_j X_k)
$$
(17.37)

The first term is an empirical average of  $X_jX_k$  if both are visible; if one or both are hidden, they are first imputed given the visible data, and then averaged over the hidden variables. The second term is the unconditional expectation of  $X_iX_k$ .

The inner expectation in the first term can be evaluated using basic rules of conditional expectation and properties of Bernoulli random variables. In detail, for observation  $i$ 

642 17. Undirected Graphical Models

$$
\mathcal{E}_{\Theta}(X_j X_k | X_V = x_{iV}) = \begin{cases} x_{ij} x_{ik} & \text{if } j, k \in \mathcal{V} \\ x_{ij} \text{Pr}_{\Theta}(X_k = 1 | X_V = x_{iV}) & \text{if } j \in \mathcal{V}, k \in \mathcal{H} \\ \text{Pr}_{\Theta}(X_j = 1, X_k = 1 | X_V = x_{iV}) & \text{if } j, k \in \mathcal{H}. \end{cases}
$$
\n(17.38)

Now two separate runs of Gibbs sampling are required; the first to estimate  $E_{\Theta}(X_i X_k)$  by sampling from the model as above, and the second to estimate  $E_{\Theta}(X_iX_k|X_{\mathcal{V}}=x_{i\mathcal{V}})$ . In this latter run, the visible units are fixed ("clamped") at their observed values and only the hidden variables are sampled. Gibbs sampling must be done for each observation in the training set, at each stage of the gradient search. As a result this procedure can be very slow, even for moderate-sized models. In Section 17.4.4 we consider further model restrictions to make these computations manageable.

## 17.4.3 Estimation of the Graph Structure

The use of a lasso penalty with binary pairwise Markov networks has been suggested by Lee et al. (2007) and Wainwright et al. (2007). The first authors investigate a conjugate gradient procedure for exact maximization of a penalized log-likelihood. The bottleneck is the computation of  $E_{\Theta}(X_i X_k)$ in the gradient; exact computation via the junction tree algorithm is manageable for sparse graphs but becomes unwieldy for dense graphs.

The second authors propose an approximate solution, analogous to the Meinshausen and Bühlmann (2006) approach for the Gaussian graphical model. They fit an  $L_1$ -penalized logistic regression model to each node as a function of the other nodes, and then symmetrize the edge parameter estimates in some fashion. For example if  $\tilde{\theta}_{jk}$  is the estimate of the j-k edge parameter from the logistic model for outcome node  $j$ , the "min" symmetrization sets  $\hat{\theta}_{jk}$  to either  $\tilde{\theta}_{jk}$  or  $\tilde{\theta}_{kj}$ , whichever is smallest in absolute value. The "max" criterion is defined similarly. They show that under certain conditions either approximation estimates the nonzero edges correctly as the sample size goes to infinity. Hoefling and Tibshirani (2008) extend the graphical lasso to discrete Markov networks, obtaining a procedure which is somewhat faster than conjugate gradients, but still must deal with computation of  $E_{\Theta}(X_i X_k)$ . They also compare the exact and approximate solutions in an extensive simulation study and find the "min" or "max" approximations are only slightly less accurate than the exact procedure, both for estimating the nonzero edges and for estimating the actual values of the edge parameters, and are much faster. Furthermore, they can handle denser graphs because they never need to compute the quantities  $E_{\boldsymbol{\Theta}}(X_iX_k).$ 

Finally, we point out a key difference between the Gaussian and binary models. In the Gaussian case, both  $\Sigma$  and its inverse will often be of interest, and the graphical lasso procedure delivers estimates for both of these quantities. However, the approximation of Meinshausen and Bühlmann (2006) for Gaussian graphical models, analogous to the Wainwright et al. (2007) 17.4 Undirected Graphical Models for Discrete Variables 643

Image /page/138/Figure/1 description: This is a diagram of a neural network with two visible layers, V1 and V2, and one hidden layer, H. The visible layer V1 consists of three green nodes labeled Xj. The hidden layer H consists of five blue nodes labeled Xk. The visible layer V2 consists of two yellow nodes labeled Xℓ. All nodes in the hidden layer are connected to all nodes in the visible layer V1. Some nodes in the hidden layer are connected to nodes in the visible layer V2. The connection between Xk and Xj is labeled with θjk.

FIGURE 17.6. A restricted Boltzmann machine (RBM) in which there are no connections between nodes in the same layer. The visible units are subdivided to allow the RBM to model the joint density of feature  $V_1$  and their labels  $V_2$ .

approximation for the binary case, only yields an estimate of  $\Sigma^{-1}$ . In contrast, in the Markov model for binary data,  $\Theta$  is the object of interest, and its inverse is not of interest. The approximate method of Wainwright et al.  $(2007)$  estimates  $\Theta$  efficiently and hence is an attractive solution for the binary problem.

# 17.4.4 Restricted Boltzmann Machines

In this section we consider a particular architecture for graphical models inspired by neural networks, where the units are organized in layers. A restricted Boltzmann machine (RBM) consists of one layer of visible units and one layer of hidden units with no connections within each layer. It is much simpler to compute the conditional expectations (as in 17.37 and 17.38) if the connections between hidden units are removed  $5$ . Figure 17.6 shows an example; the visible layer is divided into input variables  $V_1$  and output variables  $V_2$ , and there is a hidden layer  $H$ . We denote such a network by

$$
\mathcal{V}_1 \leftrightarrow \mathcal{H} \leftrightarrow \mathcal{V}_2. \tag{17.39}
$$

For example,  $V_1$  could be the binary pixels of an image of a handwritten digit, and  $V_2$  could have 10 units, one for each of the observed class labels 0-9.

The restricted form of this model simplifies the Gibbs sampling for estimating the expectations in (17.37), since the variables in each layer are independent of one another, given the variables in the other layers. Hence they can be sampled together, using the conditional probabilities given by expression (17.30).

The resulting model is less general than a Boltzmann machine, but is still useful; for example it can learn to extract interesting features from images.

<sup>5</sup>We thank Geoffrey Hinton for assistance in the preparation of the material on RBMs.

### 644 17. Undirected Graphical Models

By alternately sampling the variables in each layer of the RBM shown in Figure 17.6, it is possible to generate samples from the joint density model. If the  $\mathcal{V}_1$  part of the visible layer is clamped at a particular feature vector during the alternating sampling, it is possible to sample from the distribution over labels given  $\mathcal{V}_1$ . Alternatively classification of test items can also be achieved by comparing the unnormalized joint densities of each label category with the observed features. We do not need to compute the partition function as it is the same for all of these combinations.

As noted the restricted Boltzmann machine has the same generic form as a single hidden layer neural network (Section 11.3). The edges in the latter model are directed, the hidden units are usually real-valued, and the fitting criterion is different. The neural network minimizes the error (crossentropy) between the targets and their model predictions, conditional on the input features. In contrast, the restricted Boltzmann machine maximizes the log-likelihood for the joint distribution of all visible units—that is, the features and targets. It can extract information from the input features that is useful for predicting the labels, but, unlike supervised learning methods, it may also use some of its hidden units to model structure in the feature vectors that is not immediately relevant for predicting the labels. These features may turn out to be useful, however, when combined with features derived from other hidden layers.

Unfortunately, Gibbs sampling in a restricted Boltzmann machine can be very slow, as it can take a long time to reach stationarity. As the network weights get larger, the chain mixes more slowly and we need to run more steps to get the unconditional estimates. Hinton (2002) noticed empirically that learning still works well if we estimate the second expectation in (17.37) by starting the Markov chain at the data and only running for a few steps (instead of to convergence). He calls this contrastive divergence: we sample H given  $\mathcal{V}_1, \mathcal{V}_2$ , then  $\mathcal{V}_1, \mathcal{V}_2$  given H and finally H given  $\mathcal{V}_1, \mathcal{V}_2$ again. The idea is that when the parameters are far from the solution, it may be wasteful to iterate the Gibbs sampler to stationarity, as just a single iteration will reveal a good direction for moving the estimates.

We now give an example to illustrate the use of an RBM. Using contrastive divergence, it is possible to train an RBM to recognize hand-written digits from the MNIST dataset (LeCun et al., 1998). With 2000 hidden units, 784 visible units for representing binary pixel intensities and one 10-way multinomial visible unit for representing labels, the RBM achieves an error rate of 1.9% on the test set. This is a little higher than the 1.4% achieved by a support vector machine and comparable to the error rate achieved by a neural network trained with backpropagation. The error rate of the RBM, however, can be reduced to 1.25% by replacing the 784 pixel intensities by 500 features that are produced from the images without using any label information. First, an RBM with 784 visible units and 500 hidden units is trained, using contrastive divergence, to model the set of images. Then the hidden states of the first RBM are used as data for training a

Exercises 645

Image /page/140/Figure/1 description: This image displays a diagram of a neural network architecture alongside a grid of handwritten digits. The neural network diagram shows a top layer with "2000 hidden units" connected to two parallel layers: one with "10 label units" and another with "500 hidden units". Both of these layers are connected to a subsequent layer with "500 hidden units", which then leads to a final box labeled "image: 28 x 28 pixels". Arrows indicate the flow of information between these layers. To the right of the diagram, a grid of 35 black and white images of handwritten digits is presented. The digits are arranged in five rows and seven columns, showcasing various examples of handwritten numbers from 0 to 9, with some digits appearing multiple times and in different styles.

FIGURE 17.7. Example of a restricted Boltzmann machine for handwritten digit classification. The network is depicted in the schematic on the left. Displayed on the right are some difficult test images that the model classifies correctly.

second RBM that has 500 visible units and 500 hidden units. Finally, the hidden states of the second RBM are used as the features for training an RBM with 2000 hidden units as a joint density model. The details and justification for learning features in this greedy, layer-by-layer way are described in Hinton et al. (2006). Figure 17.7 gives a representation of the composite model that is learned in this way and also shows some examples of the types of distortion that it can cope with.

# Bibliographic Notes

Much work has been done in defining and understanding the structure of graphical models. Comprehensive treatments of graphical models can be found in Whittaker (1990), Lauritzen (1996), Cox and Wermuth (1996), Edwards (2000), Pearl (2000), Anderson (2003), Jordan (2004), and Koller and Friedman (2007). Wasserman (2004) gives a brief introduction, and Chapter 8 of Bishop (2006) gives a more detailed overview. Boltzmann machines were proposed in Ackley et al. (1985). Ripley (1996) has a detailed chapter on topics in graphical models that relate to machine learning. We found this particularly useful for its discussion of Boltzmann machines.

# Exercises

Ex. 17.1 For the Markov graph of Figure 17.8, list all of the implied conditional independence relations and find the maximal cliques.

646 17. Undirected Graphical Models

Image /page/141/Figure/1 description: A diagram shows six nodes labeled X1 through X6, arranged in a graph. Node X1 is connected to X2, X3, X4, and X6. Node X2 is connected to X1 and X6. Node X3 is connected to X1 and X4. Node X4 is connected to X1 and X3. Node X5 is connected to X6. Node X6 is connected to X1, X2, and X5. The diagram is labeled as FIGURE 17.8.

Ex. 17.2 Consider random variables  $X_1, X_2, X_3, X_4$ . In each of the following cases draw a graph that has the given independence relations:

- (a)  $X_1 \perp X_3 | X_2$  and  $X_2 \perp X_4 | X_3$ .
- (b)  $X_1 \perp X_4 | X_2, X_3$  and  $X_2 \perp X_4 | X_1, X_3$ .
- (c)  $X_1 \perp X_4 | X_2, X_3, X_1 \perp X_3 | X_2, X_4$  and  $X_3 \perp X_4 | X_1, X_2$ .

Ex. 17.3 Let  $\Sigma$  be the covariance matrix of a set of p variables X. Consider the partial covariance matrix  $\Sigma_{a,b} = \Sigma_{aa} - \Sigma_{ab} \Sigma_{bb}^{-1} \Sigma_{ba}$  between the two subsets of variables  $X_a = (X_1, X_2)$  consisting of the first two, and  $X_b$ the rest. This is the covariance matrix between these two variables, after linear adjustment for all the rest. In the Gaussian distribution, this is the covariance matrix of the conditional distribution of  $X_a|X_b$ . The partial correlation coefficient  $\rho_{jk|\text{rest}}$  between the pair  $X_a$  conditional on the rest  $X_b$ , is simply computed from this partial covariance. Define  $\Theta = \Sigma^{-1}$ .

- 1. Show that  $\Sigma_{a,b} = \Theta_{aa}^{-1}$ .
- 2. Show that if any off-diagonal element of  $\Theta$  is zero, then the partial correlation coefficient between the corresponding variables is zero.
- 3. Show that if we treat  $\Theta$  as if it were a covariance matrix, and compute the corresponding "correlation" matrix

$$
\mathbf{R} = \text{diag}(\Theta)^{-1/2} \cdot \mathbf{\Theta} \cdot \text{diag}(\Theta)^{-1/2}, \quad (17.40)
$$

then  $r_{jk} = -\rho_{jk|rest}$ 

Ex. 17.4 Denote by

$$
f(X_1|X_2,X_3,\ldots,X_p)
$$

the conditional density of  $X_1$  given  $X_2, \ldots, X_p$ . If

$$
f(X_1|X_2, X_3, \ldots, X_p) = f(X_1|X_3, \ldots, X_p),
$$

show that  $X_1 \perp X_2 | X_3, \ldots, X_p$ .

646

Ex. 17.5 Consider the setup in Section 17.3.1 with no missing edges. Show that

$$
\mathbf{S}_{11}\boldsymbol{\beta}-s_{12}=0
$$

are the estimating equations for the multiple regression coefficients of the last variable on the rest.

Ex. 17.6 Recovery of  $\hat{\Theta} = \hat{\Sigma}^{-1}$  from Algorithm 17.1. Use expression (17.16) to derive the standard partitioned inverse expressions

$$
\theta_{12} = -\mathbf{W}_{11}^{-1} w_{12} \theta_{22} \tag{17.41}
$$

$$
\theta_{22} = 1/(w_{22} - w_{12}^T \mathbf{W}_{11}^{-1} w_{12}). \qquad (17.42)
$$

Since  $\hat{\beta} = \mathbf{W}_{11}^{-1} w_{12}$ , show that  $\hat{\theta}_{22} = 1/(w_{22} - w_{12}^T \hat{\beta})$  and  $\hat{\theta}_{12} = -\hat{\beta} \hat{\theta}_{22}$ . Thus  $\hat{\theta}_{12}$  is a simply rescaling of  $\hat{\beta}$  by  $-\hat{\theta}_{22}$ .

Ex. 17.7 Write a program to implement the modified regression procedure (17.1) for fitting the Gaussian graphical model with pre-specified edges missing. Test it on the flow cytometry data from the book website, using the graph of Figure 17.1.

## Ex. 17.8

- (a) Write a program to fit the lasso using the coordinate descent procedure (17.26). Compare its results to those from the lars program or some other convex optimizer, to check that it is working correctly.
- (b) Using the program from (a), write code to implement the graphical lasso algorithm (17.2). Apply it to the flow cytometry data from the book website. Vary the regularization parameter and examine the resulting networks.

Ex. 17.9 Suppose that we have a Gaussian graphical model in which some or all of the data at some vertices are missing.

(a) Consider the EM algorithm for a dataset of  $N$  i.i.d. multivariate observations  $x_i \in \mathbb{R}^p$  with mean  $\mu$  and covariance matrix  $\Sigma$ . For each sample  $i$ , let  $o_i$  and  $m_i$  index the predictors that are observed and missing, respectively. Show that in the E step, the observations are imputed from the current estimates of  $\mu$  and  $\Sigma$ :

$$
\hat{x}_{i,m_i} = \mathcal{E}(x_{i,m_i}|x_{i,o_i}, \theta) = \hat{\mu}_{m_i} + \hat{\Sigma}_{m_i,o_i} \hat{\Sigma}_{o_i,o_i}^{-1} (x_{i,o_i} - \hat{\mu}_{o_i})
$$
\n(17.43)

while in the M step,  $\mu$  and  $\Sigma$  are re-estimated from the empirical mean and (modified) covariance of the imputed data:

$$
\hat{\mu}_j = \sum_{i=1}^N \hat{x}_{ij}/N
$$

648 17. Undirected Graphical Models

$$
\hat{\Sigma}_{jj'} = \sum_{i=1}^{N} [(\hat{x}_{ij} - \hat{\mu}_j)(\hat{x}_{ij'} - \hat{\mu}_{j'}) + c_{i,jj'}]/N \qquad (17.44)
$$

where  $c_{i,jj'} = \hat{\Sigma}_{jj'}$  if  $j, j' \in m_i$  and zero otherwise. Explain the reason for the correction term  $c_{i,jj'}$  (Little and Rubin, 2002).

- (b) Implement the EM algorithm for the Gaussian graphical model using the modified regression procedure from Exercise 17.7 for the M-step.
- (c) For the flow cytometry data on the book website, set the data for the last protein Jnk in the first 1000 observations to missing, fit the model of Figure 17.1, and compare the predicted values to the actual values for Jnk. Compare the results to those obtained from a regression of Jnk on the other vertices with edges to Jnk in Figure 17.1, using only the non-missing data.

Ex. 17.10 Using a simple binary graphical model with just two variables, show why it is essential to include a constant node  $X_0 \equiv 1$  in the model.

Ex. 17.11 Show that the Ising model (17.28) for the joint probabilities in a discrete graphical model implies that the conditional distributions have the logistic form (17.30).

Ex. 17.12 Consider a Poisson regression problem with  $p$  binary variables  $x_{ij}, j = 1, \ldots, p$  and response variable  $y_i$  which measures the number of observations with predictor  $x_i \in \{0, 1\}^p$ . The design is balanced, in that all  $n = 2<sup>p</sup>$  possible combinations are measured. We assume a log-linear model for the Poisson mean in each cell

$$
\log \mu(X) = \theta_{00} + \sum_{(j,k)\in E} x_{ij} x_{ik} \theta_{jk},
$$
\n(17.45)

using the same notation as in Section 17.4.1 (including the constant variable  $x_{i0} = 1 \forall i$ . We assume the response is distributed as

$$
\Pr(Y = y | X = x) = \frac{e^{-\mu(x)}\mu(x)^y}{y!}.
$$
\n(17.46)

Write down the conditional log-likelihood for the observed responses  $y_i$ , and compute the gradient.

- (a) Show that the gradient equation for  $\theta_{00}$  computes the partition function (17.29).
- (b) Show that the gradient equations for the remainder of the parameters are equivalent to the gradient (17.34).

This is page 649 Printer: Opaque this

# 18 High-Dimensional Problems: $p \gg N$

# 18.1 When $p$ is Much Bigger than N

In this chapter we discuss prediction problems in which the number of features  $p$  is much larger than the number of observations  $N$ , often written  $p \gg N$ . Such problems have become of increasing importance, especially in genomics and other areas of computational biology. We will see that high variance and overfitting are a major concern in this setting. As a result, simple, highly regularized approaches often become the methods of choice. The first part of the chapter focuses on prediction in both the classification and regression settings, while the second part discusses the more basic problem of feature selection and assessment.

To get us started, Figure 18.1 summarizes a small simulation study that demonstrates the "less fitting is better" principle that applies when  $p \gg N$ . For each of  $N = 100$  samples, we generated p standard Gaussian features  $X$  with pairwise correlation 0.2. The outcome  $Y$  was generated according to a linear model

$$
Y = \sum_{j=1}^{p} X_j \beta_j + \sigma \varepsilon \tag{18.1}
$$

where  $\varepsilon$  was generated from a standard Gaussian distribution. For each dataset, the set of coefficients  $\beta_i$  were also generated from a standard Gaussian distribution. We investigated three cases:  $p = 20, 100,$  and 1000. The standard deviation  $\sigma$  was chosen in each case so that the signal-to-noise ratio  $Var[E(Y|X)]/\sigma^2$  equaled 2. As a result, the number of significant uni-

Image /page/145/Figure/1 description: This image displays three box plots, each representing a different number of features: 20, 100, and 1000. The y-axis for all plots is labeled "Test Error" and ranges from 1.0 to 3.0. The x-axis for all plots is labeled "Effective Degrees of Freedom". For the "20 features" plot, there are three box plots corresponding to effective degrees of freedom of 20, 9, and 2. The test errors are approximately 1.2, 1.55, and 2.5, respectively. For the "100 features" plot, there are three box plots corresponding to effective degrees of freedom of 99, 35, and 7. The test errors are approximately 2.0, 1.5, and 2.25, respectively. For the "1000 features" plot, there are three box plots corresponding to effective degrees of freedom of 99, 87, and 43. The test errors are approximately 2.0, 1.8, and 1.65, respectively.

FIGURE 18.1. Test-error results for simulation experiments. Shown are boxplots of the relative test errors over 100 simulations, for three different values of p, the number of features. The relative error is the test error divided by the Bayes error,  $\sigma^2$ . From left to right, results are shown for ridge regression with three different values of the regularization parameter  $\lambda$ : 0.001, 100 and 1000. The (average) effective degrees of freedom in the fit is indicated below each plot.

variate regression coefficients<sup>1</sup> was 9, 33 and 331, respectively, averaged over the 100 simulation runs. The  $p = 1000$  case is designed to mimic the kind of data that we might see in a high-dimensional genomic or proteomic dataset, for example.

We fit a ridge regression to the data, with three different values for the regularization parameter  $\lambda$ : 0.001, 100, and 1000. When  $\lambda = 0.001$ , this is nearly the same as least squares regression, with a little regularization just to ensure that the problem is non-singular when  $p > N$ . Figure 18.1 shows boxplots of the relative test error achieved by the different estimators in each scenario. The corresponding average degrees of freedom used in each ridge-regression fit is indicated (computed using formula (3.50) on page 68<sup>2</sup> ). The degrees of freedom is a more interpretable parameter than λ. We see that ridge regression with  $\lambda = 0.001$  (20 df) wins when  $p = 20$ ;  $\lambda = 100$  (35 df) wins when  $p = 100$ , and  $\lambda = 1000$  (43 df) wins when  $p = 1000,$ 

Here is an explanation for these results. When  $p = 20$ , we fit all the way and we can identify as many of the significant coefficients as possible with

<sup>&</sup>lt;sup>1</sup>We call a regression coefficient significant if  $|\hat{\beta}_j/\hat{\mathbf{se}}_j| \geq 2$ , where  $\hat{\beta}_j$  is the estimated (univariate) coefficient and  $\hat{\mathbf{s}}_{i}$  is its estimated standard error.

<sup>&</sup>lt;sup>2</sup>For a fixed value of the regularization parameter  $\lambda$ , the degrees of freedom depends on the observed predictor values in each simulation. Hence we compute the average degrees of freedom over simulations.

low bias. When  $p = 100$ , we can identify some non-zero coefficients using moderate shrinkage. Finally, when  $p = 1000$ , even though there are many nonzero coefficients, we don't have a hope for finding them and we need to shrink all the way down. As evidence of this, let  $t_j = \hat{\beta}_j / \hat{\mathbf{s}} \hat{\mathbf{e}}_j$ , where  $\hat{\beta}_j$ is the ridge regression estimate and  $\hat{\mathbf{s}}_{i}$  its estimated standard error. Then using the optimal ridge parameter in each of the three cases, the median value of  $|t_i|$  was 2.0, 0.6 and 0.2, and the average number of  $|t_i|$  values exceeding 2 was equal to 9.8, 1.2 and 0.0.

Ridge regression with  $\lambda = 0.001$  successfully exploits the correlation in the features when  $p < N$ , but cannot do so when  $p \gg N$ . In the latter case there is not enough information in the relatively small number of samples to efficiently estimate the high-dimensional covariance matrix. In that case, more regularization leads to superior prediction performance.

Thus it is not surprising that the analysis of high-dimensional data requires either modification of procedures designed for the  $N > p$  scenario, or entirely new procedures. In this chapter we discuss examples of both kinds of approaches for high dimensional classification and regression; these methods tend to regularize quite heavily, using scientific contextual knowledge to suggest the appropriate form for this regularization. The chapter ends with a discussion of feature selection and multiple testing.

# 18.2 Diagonal Linear Discriminant Analysis and Nearest Shrunken Centroids

Gene expression arrays are an important new technology in biology, and are discussed in Chapters 1 and 14. The data in our next example form a matrix of 2308 genes (columns) and 63 samples (rows), from a set of microarray experiments. Each expression value is a log-ratio  $log(R/G)$ . R is the amount of gene-specific RNA in the target sample that hybridizes to a particular (gene-specific) spot on the microarray, and  $G$  is the corresponding amount of RNA from a reference sample. The samples arose from small, round blue-cell tumors (SRBCT) found in children, and are classified into four major types: BL (Burkitt lymphoma), EWS (Ewing's sarcoma), NB (neuroblastoma), and RMS (rhabdomyosarcoma). There is an additional test data set of 20 observations. We will not go into the scientific background here.

Since  $p \gg N$ , we cannot fit a full linear discriminant analysis (LDA) to the data; some sort of regularization is needed. The method we describe here is similar to the methods of Section 4.3.1, but with important modifications that achieve feature selection. The simplest form of regularization assumes that the features are independent within each class, that is, the within-class covariance matrix is diagonal. Despite the fact that features will rarely be independent within a class, when  $p \gg N$  we don't have

### 652 18. High-Dimensional Problems: $p \gg N$

enough data to estimate their dependencies. The assumption of independence greatly reduces the number of parameters in the model and often results in an effective and interpretable classifier.

Thus we consider the diagonal-covariance LDA rule for classifying the classes. The *discriminant score* [see  $(4.12)$  on page 110] for class k is

$$
\delta_k(x^*) = -\sum_{j=1}^p \frac{(x_j^* - \bar{x}_{kj})^2}{s_j^2} + 2\log \pi_k. \tag{18.2}
$$

Here  $x^* = (x_1^*, x_2^*, \dots, x_p^*)^T$  is a vector of expression values for a test observation,  $s_i$  is the pooled within-class standard deviation of the jth gene, and  $\bar{x}_{kj} = \sum_{i \in C_k} x_{ij}/N_k$  is the mean of the  $N_k$  values for gene j in class k, with  $C_k$  being the index set for class k. We call  $\tilde{x}_k = (\bar{x}_{k1}, \bar{x}_{k2}, \dots \bar{x}_{kp})^T$ the *centroid* of class  $k$ . The first part of  $(18.2)$  is simply the (negative) standardized squared distance of  $x^*$  to the k<sup>th</sup> centroid. The second part is a correction based on the class *prior probability*  $\pi_k$ , where  $\sum_{k=1}^K \pi_k = 1$ . The classification rule is then

$$
C(x^*) = \ell \text{ if } \delta_\ell(x^*) = \max_k \delta_k(x^*). \tag{18.3}
$$

We see that the diagonal LDA classifier is equivalent to a nearest centroid classifier after appropriate standardization. It is also a special case of the naive-Bayes classifier, as described in Section 6.6.3. It assumes that the features in each class have independent Gaussian distributions with the same variance.

The diagonal LDA classifier is often effective in high dimensional settings. It is also called the "independence rule" in Bickel and Levina (2004), who demonstrate theoretically that it will often outperform standard linear discriminant analysis in high-dimensional problems. Here the diagonal LDA classifier yielded five misclassification errors for the 20 test samples. One drawback of the diagonal LDA classifier is that it uses all of the features (genes), and hence is not convenient for interpretation. With further regularization we can do better—both in terms of test error and interpretability.

We would like to regularize in a way that automatically drops out features that are not contributing to the class predictions. We can do this by shrinking the classwise mean toward the overall mean, for each feature separately. The result is a regularized version of the nearest centroid classifier, or equivalently a regularized version of the diagonal-covariance form of LDA. We call the procedure nearest shrunken centroids (NSC).

The shrinkage procedure is defined as follows. Let

$$
d_{kj} = \frac{\bar{x}_{kj} - \bar{x}_j}{m_k(s_j + s_0)},
$$
\n(18.4)

where  $\bar{x}_j$  is the overall mean for gene j,  $m_k^2 = 1/N_k - 1/N$  and  $s_0$  is a small positive constant, typically chosen to be the median of the  $s_i$  values.

Image /page/148/Figure/1 description: A graph shows two lines originating from the origin (0,0). One line is solid red and goes diagonally up and to the right, and diagonally down and to the left. The other line is dashed orange and also goes diagonally up and to the right, and diagonally down and to the left. The dashed orange line is below the solid red line in the first and third quadrants. In the first quadrant, a dotted line extends from the solid red line down to the dashed orange line, with the label "∆" next to it.

FIGURE 18.2. Soft thresholding function  $sign(x)(|x|-\Delta)_+$  is shown in orange, along with the 45◦ line in red.

This constant guards against large  $d_{kj}$  values that arise from expression values near zero. With constant within-class variance  $\sigma^2$ , the variance of the contrast  $\bar{x}_{kj} - \bar{x}_j$  in the numerator is  $m_k^2 \sigma^2$ , and hence the form of the standardization in the denominator. We shrink the  $d_{kj}$  toward zero using soft thresholding

$$
d'_{kj} = \text{sign}(d_{kj})(|d_{kj}| - \Delta)_{+};
$$
\n(18.5)

see Figure 18.2. Here  $\Delta$  is a parameter to be determined; we used 10-fold cross-validation in the example (see the top panel of Figure 18.4). Each  $d_{kj}$ is reduced by an amount  $\Delta$  in absolute value, and is set to zero if its value is less than zero. The soft-thresholding function is shown in Figure 18.2; the same thresholding is applied to wavelet coefficients in Section 5.9. An alternative is to use hard thresholding

$$
d'_{kj} = d_{kj} \cdot I(|d_{kj}| \ge \Delta); \tag{18.6}
$$

we prefer soft-thresholding, as it is a smoother operation and typically works better. The shrunken versions of  $\bar{x}_{kj}$  are then obtained by reversing the transformation in (18.4):

$$
\bar{x}'_{kj} = \bar{x}_j + m_k(s_j + s_0)d'_{kj}.
$$
\n(18.7)

We then use the shrunken centroids  $\bar{x}'_{kj}$  in place of the original  $\bar{x}_{kj}$  in the discriminant score (18.2). The estimator (18.5) can also be viewed as a lasso-style estimator for the class means (Exercise 18.2).

Notice that only the genes that have a nonzero  $d'_{kj}$  for at least one of the classes play a role in the classification rule, and hence the vast majority of genes can often be discarded. In this example, all but 43 genes were discarded, leaving a small interpretable set of genes that characterize each class. Figure 18.3 represents the genes in a heatmap.

Figure 18.4 (top panel) demonstrates the effectiveness of the shrinkage. With no shrinkage we make 5/20 errors on the test data, and several errors

### 654 18. High-Dimensional Problems: $p \gg N$

on the training and CV data. The shrunken centroids achieve zero test errors for a fairly broad band of values for ∆. The bottom panel of Figure 18.4 shows the four centroids for the SRBCT data (gray), relative to the overall centroid. The blue bars are shrunken versions of these centroids, obtained by soft-thresholding the gray bars, using  $\Delta = 4.3$ . The discriminant scores (18.2) can be used to construct class probability estimates:

$$
\hat{p}_k(x^*) = \frac{e^{\frac{1}{2}\delta_k(x^*)}}{\sum_{\ell=1}^K e^{\frac{1}{2}\delta_\ell(x^*)}}.\tag{18.8}
$$

These can be used to rate the classifications, or to decide not to classify a particular sample at all.

Note that other forms of feature selection can be used in this setting, including hard thresholding. Fan and Fan (2008) show theoretically the importance of carrying out some kind of feature selection with diagonal linear discriminant analysis in high-dimensional problems.

# 18.3 Linear Classifiers with Quadratic Regularization

Ramaswamy et al. (2001) present a more difficult microarray classification problem, involving a training set of 144 patients with 14 different types of cancer, and a test set of 54 patients. Gene expression measurements were available for 16, 063 genes.

Table 18.1 shows the prediction results from eight different classification methods. The data from each patient was first standardized to have mean 0 and variance 1; this seems to improve prediction accuracy overall this example, suggesting that the "shape" of each gene-expression profile is important, rather than the absolute expression levels. In each case, the

Image /page/149/Figure/8 description: A heatmap displays gene expression data across four different conditions: BL, EWS, NB, and RMS. Each condition is represented by a column, and rows represent individual genes or samples. The heatmap uses a color scale where blue indicates low expression and yellow indicates high expression. Within each column, there are distinct patterns of blue and yellow blocks, suggesting varying expression levels of genes under different conditions. The data is organized into four main blocks, separated by white lines, with the first block showing predominantly blue, the second block showing a mix of blue and yellow, the third block showing more yellow, and the fourth block showing a strong yellow pattern.

FIGURE 18.3. Heat-map of the chosen 43 genes. Within each of the horizontal partitions, we have ordered the genes by hierarchical clustering, and similarly for the samples within each vertical partition. Yellow represents over- and blue under-expression.

Image /page/150/Figure/1 description: The top panel of the figure displays a line graph illustrating misclassification error against the amount of shrinkage (Δ). The x-axis is labeled "Amount of Shrinkage Δ" and ranges from 0 to 8. The top x-axis shows the corresponding "Number of Genes", with values decreasing from 2308 to 1. The y-axis is labeled "Misclassification Error" and ranges from 0.0 to 0.8. Three lines are plotted: "Training" (green), "10-fold CV" (orange), and "Test" (blue). The "Training" and "10-fold CV" lines remain close to 0.0 for shrinkage values up to approximately 4.5, after which they gradually increase. The "Test" line starts at a misclassification error of approximately 0.25, decreases to near 0.0 for shrinkage values up to approximately 4.5, and then increases sharply. A vertical dashed line is present at Δ = 4.5. The bottom panel of the figure consists of four separate plots, labeled "BL", "EWS", "NB", and "RMS" from left to right. Each plot shows a series of horizontal gray lines representing genes, with a vertical blue line indicating the overall centroid. Blue bars are superimposed on some of the gray lines, indicating the average expression centered at the overall centroid for specific genes. The y-axis for these plots is labeled "Gene" and ranges from 0 to 2000. The x-axis for each of these plots ranges from -1.0 to 1.0, representing the centered average expression.

### Number of Genes

FIGURE 18.4. (Top): Error curves for the SRBCT data. Shown are the training, 10-fold cross-validation, and test misclassification errors as the threshold parameter  $\Delta$  is varied. The value  $\Delta = 4.34$  is chosen by CV, resulting in a subset of 43 selected genes. (Bottom): Four centroids profiles  $d_{kj}$  for the SRBCT data (gray), relative to the overall centroid. Each centroid has 2308 components, and we see considerable noise. The blue bars are shrunken versions  $d'_{kj}$  of these centroids, obtained by soft-thresholding the gray bars, using  $\Delta = 4.3$ .

TABLE 18.1. Prediction results for microarray data with 14 cancer classes. Method 1 is described in Section 18.2. Methods 2, 3 and 6 are discussed in Section 18.3, while 4, 7 and 8 are discussed in Section 18.4. Method 5 is described in Section 13.3. The elastic-net penalized multinomial does the best on the test data, but the standard error of each test-error estimate is about 3, so such comparisons are inconclusive.

| Methods                                      | $CV$ errors $(SE)$<br>Out of 144 | Test errors<br>Out of 54 | Number of<br>Genes Used |
|----------------------------------------------|----------------------------------|--------------------------|-------------------------|
| 1. Nearest shrunken centroids                | 35 (5.0)                         | 17                       | 6,520                   |
| 2. $L_2$ -penalized discriminant<br>analysis | 25 (4.1)                         | 12                       | 16,063                  |
| 3. Support vector classifier                 | 26 (4.2)                         | 14                       | 16,063                  |
| 4. Lasso regression (one vs all)             | 30.7 (1.8)                       | 12.5                     | 1,429                   |
| 5. $k$ -nearest neighbors                    | 41 (4.6)                         | 26                       | 16,063                  |
| 6. $L_2$ -penalized multinomial              | 26 (4.2)                         | 15                       | 16,063                  |
| 7. $L_1$ -penalized multinomial              | 17 (2.8)                         | 13                       | 269                     |
| 8. Elastic-net penalized<br>multinomial      | 22 (3.7)                         | 11.8                     | 384                     |

regularization parameter has been chosen to minimize the cross-validation error, and the test error at that value of the parameter is shown. When more than one value of the regularization parameter yields the minimal cross-validation error, the average test error at these values is reported.

RDA (regularized discriminant analysis), regularized multinomial logistic regression, and the support vector machine are more complex methods that try to exploit multivariate information in the data. We describe each in turn, as well as a variety of regularization methods, including both  $L_1$  and  $L_2$  and some in between.

## 18.3.1 Regularized Discriminant Analysis

Regularized discriminant analysis (RDA) is described in Section 4.3.1. Linear discriminant analysis involves the inversion of a  $p \times p$  within-covariance matrix. When  $p \gg N$ , this matrix can be huge, has rank at most  $N < p$ , and hence is singular. RDA overcomes the singularity issues by regularizing the within-covariance estimate  $\Sigma$ . Here we use a version of RDA that shrinks  $\Sigma$  towards its diagonal:

$$
\hat{\Sigma}(\gamma) = \gamma \hat{\Sigma} + (1 - \gamma) \text{diag}(\hat{\Sigma}), \text{ with } \gamma \in [0, 1]. \tag{18.9}
$$

Note that  $\gamma = 0$  corresponds to diagonal LDA, which is the "no shrinkage" version of nearest shrunken centroids. The form of shrinkage in (18.9) is much like ridge regression (Section 3.4.1), which shrinks the total covariance matrix of the features towards a diagonal (scalar) matrix. In fact, viewing linear discriminant analysis as linear regression with optimal scoring of the categorical response [see (12.58) in Section 12.6], the equivalence becomes more precise.

The computational burden of inverting this large  $p \times p$  matrix is overcome using the methods discussed in Section 18.3.5. The value of  $\gamma$  was chosen by cross-validation in line 2 of Table 18.1; all values of  $\gamma \in (0.002, 0.550)$ gave the same CV and test error. Further development of RDA, including shrinkage of the centroids in addition to the covariance matrix, can be found in Guo et al. (2006).

## 18.3.2 Logistic Regression with Quadratic Regularization

Logistic regression (Section 4.4) can be modified in a similar way, to deal with the  $p \gg N$  case. With K classes, we use a symmetric version of the multiclass logistic model (4.17) on page 119:

$$
\Pr(G = k | X = x) = \frac{\exp(\beta_{k0} + x^T \beta_k)}{\sum_{\ell=1}^K \exp(\beta_{\ell0} + x^T \beta_\ell)}.
$$
(18.10)

This has K coefficient vectors of log-odds parameters  $\beta_1, \beta_2, \ldots, \beta_K$ . We regularize the fitting by maximizing the penalized log-likelihood

$$
\max_{\{\beta_{0k}, \beta_k\}_{1}^{K}} \left[ \sum_{i=1}^{N} \log \Pr(g_i | x_i) - \frac{\lambda}{2} \sum_{k=1}^{K} ||\beta_k||_2^2 \right]. \tag{18.11}
$$

This regularization automatically resolves the redundancy in the parametrization, and forces  $\sum_{k=1}^{K} \hat{\beta}_{kj} = 0, j = 1, \ldots, p$  (Exercise 18.3). Note that the constant terms  $\beta_{k0}$  are not regularized (and so one should be set to zero). The resulting optimization problem is convex, and can be solved by a Newton algorithm or other numerical techniques. Details are given in Zhu and Hastie (2004). Friedman et al. (2010) provide software for computing the regularization path for the two- and multiclass logistic regression models. Table 18.1, line 6 reports the results for the multiclass logistic regression model, referred to there as "multinomial". It can be shown (Rosset et al., 2004a) that for separable data, as  $\lambda \to 0$ , the regularized (twoclass) logistic regression estimate (renormalized) converges to the maximal margin classifier (Section 12.2). This gives an attractive alternative to the support-vector machine, discussed next, especially in the multiclass case.

## 18.3.3 The Support Vector Classifier

The support vector classifier is described for the two-class case in Section 12.2. When  $p > N$ , it is especially attractive because in general the

### 658 18. High-Dimensional Problems: $p \gg N$

classes are perfectly separable by a hyperplane unless there are identical feature vectors in different classes. Without any regularization the support vector classifier finds the separating hyperplane with the largest margin; that is, the hyperplane yielding the biggest gap between the classes in the training data. Somewhat surprisingly, when  $p \gg N$  the unregularized support vector classifier often works about as well as the best regularized version. Overfitting often does not seem to be a problem, partly because of the insensitivity of misclassification loss.

There are many different methods for generalizing the two-class supportvector classifier to  $K > 2$  classes. In the "one versus one" (OVO) approach, we compute all  $\binom{K}{2}$  pairwise classifiers. For each test point, the predicted class is the one that wins the most pairwise contests. In the "one versus all" (OVA) approach, each class is compared to all of the others in  $K$  two-class comparisons. To classify a test point, we compute the confidences (signed distance from the hyperplane) for each of the  $K$  classifiers. The winner is the class with the highest confidence. Finally, Vapnik (1998) and Weston and Watkins (1999) suggested (somewhat complex) multiclass criteria which generalize the two-class criterion (12.6).

Tibshirani and Hastie (2007) propose the margin tree classifier, in which support-vector classifiers are used in a binary tree, much as in CART (Chapter 9). The classes are organized in a hierarchical manner, which can be useful for classifying patients into different cancer types, for example.

Line 3 of Table 18.1 shows the results for the support vector classifier using the ova method; Ramaswamy et al. (2001) reported (and we confirmed) that this approach worked best for this problem. The errors are very similar to those in line 6, as we might expect from the comments at the end of the previous section. The error rates are insensitive to the choice of  $C$  [the regularization parameter in  $(12.8)$  on page 420], for values of  $C > 0.001$ . Since  $p > N$ , the support vector hyperplane can perfectly separate the training data by setting  $C = \infty$ .

# 18.3.4 Feature Selection

Feature selection is an important scientific requirement for a classifier when  $p$  is large. Neither discriminant analysis, logistic regression, nor the supportvector classifier perform feature selection automatically, because all use quadratic regularization. All features have nonzero weights in both models. Ad-hoc methods for feature selection have been proposed, for example, removing genes with small coefficients, and refitting the classifier. This is done in a backward stepwise manner, starting with the smallest weights and moving on to larger weights. This is known as recursive feature elimination (Guyon et al., 2002). It was not successful in this example; Ramaswamy et al. (2001) report, for example, that the accuracy of the support-vector classifier starts to degrade as the number of genes is reduced from the full set of 16, 063. This is rather remarkable, as the number of training samples is only 144. We do not have an explanation for this behavior.

All three methods discussed in this section (RDA, LR and SVM) can be modified to fit nonlinear decision boundaries using kernels. Usually the motivation for such an approach is to increase the model complexity. With  $p \gg N$  the models are already sufficiently complex and overfitting is always a danger. Yet despite the high dimensionality, radial kernels (Section 12.3.3) sometimes deliver superior results in these high dimensional problems. The radial kernel tends to dampen inner products between points far away from each other, which in turn leads to robustness to outliers. This occurs often in high dimensions, and may explain the positive results. We tried a radial kernel with the SVM in Table 18.1, but in this case the performance was inferior.

## 18.3.5 Computational Shortcuts When $p \gg N$

The computational techniques discussed in this section apply to any method that fits a linear model with quadratic regularization on the coefficients. That includes all the methods discussed in this section, and many more. When  $p > N$ , the computations can be carried out in an N-dimensional space, rather than  $p$ , via the singular value decomposition introduced in Section 14.5. Here is the geometric intuition: just like two points in threedimensional space always lie on a line,  $N$  points in  $p$ -dimensional space lie in an  $(N-1)$ -dimensional affine subspace.

Given the  $N \times p$  data matrix **X**, let

$$
\mathbf{X} = \mathbf{U} \mathbf{D} \mathbf{V}^T \tag{18.12}
$$

$$
= \quad \mathbf{R} \mathbf{V}^T \tag{18.13}
$$

be the singular-value decomposition (SVD) of **X**; that is, **V** is  $p \times N$  with orthonormal columns, U is  $N \times N$  orthogonal, and D a diagonal matrix with elements  $d_1 \geq d_2 \geq d_N \geq 0$ . The matrix **R** is  $N \times N$ , with rows  $r_i^T$ .

As a simple example, let's first consider the estimates from a ridge regression:

$$
\hat{\beta} = (\mathbf{X}^T \mathbf{X} + \lambda \mathbf{I})^{-1} \mathbf{X}^T \mathbf{y}.
$$
 (18.14)

Replacing **X** by  $\mathbf{R}V^T$  and after some further manipulations, this can be shown to equal

$$
\hat{\beta} = \mathbf{V}(\mathbf{R}^T \mathbf{R} + \lambda \mathbf{I})^{-1} \mathbf{R}^T \mathbf{y}
$$
 (18.15)

(Exercise 18.4). Thus  $\hat{\beta} = \mathbf{V}\hat{\theta}$ , where  $\hat{\theta}$  is the ridge-regression estimate using the N observations  $(r_i, y_i)$ ,  $i = 1, 2, ..., N$ . In other words, we can simply reduce the data matrix from  $X$  to  $R$ , and work with the rows of **R**. This trick reduces the computational cost from  $O(p^3)$  to  $O(pN^2)$  when  $p > N$ .

### 660 18. High-Dimensional Problems: $p \gg N$

These results can be generalized to all models that are linear in the parameters and have quadratic penalties. Consider any supervised learning problem where we use a linear function  $f(X) = \rho_0 + X^T \beta$  to model a parameter in the conditional distribution of  $Y|X$ . We fit the parameters  $\beta$ by minimizing some loss function  $\sum_{i=1}^{N} L(y_i, f(x_i))$  over the data with a quadratic penalty on  $\beta$ . Logistic regression is a useful example to have in mind. Then we have the following simple theorem:

Let  $f^*(r_i) = \theta_0 + r_i^T \theta$  with  $r_i$  defined in (18.13), and consider the pair of optimization problems:

$$
(\hat{\beta}_0, \hat{\beta}) = \arg \min_{\beta_0, \beta \in \mathbb{R}^p} \sum_{i=1}^N L(y_i, \beta_0 + x_i^T \beta) + \lambda \beta^T \beta; \qquad (18.16)
$$

$$
(\hat{\theta}_0, \hat{\theta}) = \arg \min_{\theta_0, \theta \in \mathbb{R}^N} \sum_{i=1}^N L(y_i, \theta_0 + r_i^T \theta) + \lambda \theta^T \theta.
$$
 (18.17)

Then the  $\hat{\beta}_0 = \hat{\theta}_0$ , and  $\hat{\beta} = \mathbf{V}\hat{\theta}$ .

The theorem says that we can simply replace the p vectors  $x_i$  by the  $N$ -vectors  $r_i$ , and perform our penalized fit as before, but with far fewer predictors. The N-vector solution  $\hat{\theta}$  is then transformed back to the pvector solution via a simple matrix multiplication. This result is part of the statistics folklore, and deserves to be known more widely—see Hastie and Tibshirani (2004) for further details.

Geometrically, we are rotating the features to a coordinate system in which all but the first N coordinates are zero. Such rotations are allowed since the quadratic penalty is invariant under rotations, and linear models are equivariant.

This result can be applied to many of the learning methods discussed in this chapter, such as regularized (multiclass) logistic regression, linear discriminant analysis (Exercise 18.6), and support vector machines. It also applies to neural networks with quadratic regularization (Section 11.5.2). Note, however, that it does not apply to methods such as the lasso, which uses nonquadratic  $(L_1)$  penalties on the coefficients.

Typically we use cross-validation to select the parameter  $\lambda$ . It can be seen (Exercise 18.12) that we only need to construct  $\bf{R}$  once, on the original data, and use it as the data for each of the CV folds.

The support vector "kernel trick" of Section 12.3.7 exploits the same reduction used in this section, in a slightly different context. Suppose we have at our disposal the  $N \times N$  gram (inner-product) matrix  $\mathbf{K} = \mathbf{X}\mathbf{X}^T$ . From (18.12) we have  $\mathbf{K} = \mathbf{U} \mathbf{D}^2 \mathbf{U}^T$ , and so **K** captures the same information as R. Exercise 18.13 shows how we can exploit the ideas in this section to fit a ridged logistic regression with K using its SVD.