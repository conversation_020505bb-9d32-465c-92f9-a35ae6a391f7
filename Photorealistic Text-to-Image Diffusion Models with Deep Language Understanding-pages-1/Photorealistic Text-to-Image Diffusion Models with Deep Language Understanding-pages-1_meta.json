{"table_of_contents": [{"title": "Photorealistic Text-to-Image Diffusion Models\nwith Deep Language Understanding", "heading_level": null, "page_id": 0, "polygon": [[134.25, 99.75], [477.826171875, 99.75], [477.826171875, 136.705078125], [134.25, 136.705078125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.392578125, 309.75], [328.5, 309.75], [328.5, 320.396484375], [282.392578125, 320.396484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 538.5], [191.25, 538.5], [191.25, 549.52734375], [107.25, 549.52734375]]}, {"title": "2 Imagen", "heading_level": null, "page_id": 2, "polygon": [[107.25, 454.5], [165.75, 454.5], [165.75, 464.8359375], [107.25, 464.8359375]]}, {"title": "2.1 Pretrained text encoders", "heading_level": null, "page_id": 2, "polygon": [[106.8310546875, 506.25], [236.25, 506.25], [236.25, 516.65625], [106.8310546875, 516.65625]]}, {"title": "2.2 Diffusion models and classifier-free guidance", "heading_level": null, "page_id": 3, "polygon": [[107.25, 133.5], [321.0, 134.25], [321.0, 143.3759765625], [107.25, 143.3759765625]]}, {"title": "2.3 Large guidance weight samplers", "heading_level": null, "page_id": 3, "polygon": [[106.5, 459.75], [268.5, 459.75], [268.5, 469.86328125], [106.5, 469.86328125]]}, {"title": "2.4 Robust cascaded diffusion models", "heading_level": null, "page_id": 4, "polygon": [[106.5, 73.5], [275.25, 73.5], [275.25, 83.4345703125], [106.5, 83.4345703125]]}, {"title": "2.5 Neural network architecture", "heading_level": null, "page_id": 4, "polygon": [[106.5, 274.5], [252.75, 274.5], [252.75, 284.625], [106.5, 284.625]]}, {"title": "3 Evaluating Text-to-Image Models", "heading_level": null, "page_id": 4, "polygon": [[106.5, 484.5], [297.75, 484.5], [297.75, 495.38671875], [106.5, 495.38671875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[107.25, 594.7734375], [192.146484375, 594.7734375], [192.146484375, 606.375], [107.25, 606.375]]}, {"title": "4.1 Training details", "heading_level": null, "page_id": 5, "polygon": [[107.1298828125, 663.0], [199.1689453125, 663.0], [199.1689453125, 673.27734375], [107.1298828125, 673.27734375]]}, {"title": "4.2 Results on COCO", "heading_level": null, "page_id": 6, "polygon": [[106.5, 409.53515625], [208.5, 409.53515625], [208.5, 420.36328125], [106.5, 420.36328125]]}, {"title": "4.3 Results on DrawBench", "heading_level": null, "page_id": 6, "polygon": [[107.25, 552.234375], [228.75, 552.234375], [228.75, 562.2890625], [107.25, 562.2890625]]}, {"title": "4.4 Analysis of Imagen", "heading_level": null, "page_id": 6, "polygon": [[107.25, 657.75], [213.0, 657.75], [213.0, 668.63671875], [107.25, 668.63671875]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 7, "polygon": [[106.5, 648.0], [198.0, 648.0], [198.0, 660.515625], [106.5, 660.515625]]}, {"title": "6 Conclusions, Limitations and Societal Impact", "heading_level": null, "page_id": 8, "polygon": [[106.5, 169.5], [359.25, 169.5], [359.25, 181.37109375], [106.5, 181.37109375]]}, {"title": "7 Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[106.5, 282.498046875], [225.75, 282.498046875], [225.75, 294.099609375], [106.5, 294.099609375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 531.75], [164.25, 531.75], [164.25, 542.1796875], [107.25, 542.1796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 45], ["Text", 5], ["SectionHeader", 3], ["Footnote", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4198, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 58], ["Line", 15], ["Caption", 8], ["Picture", 7], ["PictureGroup", 6], ["Text", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 7, "llm_error_count": 0, "llm_tokens_used": 4202, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 56], ["ListItem", 5], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["Line", 57], ["TextInlineMath", 8], ["SectionHeader", 2], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 54], ["TextInlineMath", 4], ["SectionHeader", 3], ["Text", 2], ["ListItem", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 37], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 778, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["TableCell", 102], ["Line", 67], ["Text", 4], ["Reference", 4], ["TextInlineMath", 3], ["SectionHeader", 3], ["Table", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 12575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 77], ["Text", 6], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 57], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 51], ["ListItem", 5], ["Reference", 5], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 52], ["ListItem", 14], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 47], ["ListItem", 19], ["Reference", 16], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 47], ["ListItem", 18], ["Reference", 16], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 49], ["ListItem", 17], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 33], ["ListItem", 11], ["Reference", 8], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Photorealistic Text-to-Image Diffusion Models with Deep Language Understanding-pages-1"}