{"table_of_contents": [{"title": "14 Kernels", "heading_level": null, "page_id": 0, "polygon": [[66.75, 97.611328125], [186.75, 97.611328125], [184.5, 143.33203125], [65.25, 143.33203125]]}, {"title": "14.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 218.162109375], [96.75, 218.162109375]]}, {"title": "14.2 Kernel functions", "heading_level": null, "page_id": 0, "polygon": [[95.25, 483.75], [216.75, 483.75], [216.75, 495.80859375], [95.25, 495.80859375]]}, {"title": "14.2.1 RBF kernels", "heading_level": null, "page_id": 1, "polygon": [[89.4375, 60.75], [187.59375, 60.75], [187.59375, 71.46826171875], [89.4375, 71.46826171875]]}, {"title": "14.2.2 Kernels for comparing documents", "heading_level": null, "page_id": 1, "polygon": [[88.6640625, 287.25], [291.0, 287.25], [291.0, 297.263671875], [88.6640625, 297.263671875]]}, {"title": "14.2.3 Mercer (positive definite) kernels", "heading_level": null, "page_id": 2, "polygon": [[89.2265625, 207.0], [285.75, 207.0], [285.75, 217.212890625], [89.2265625, 217.212890625]]}, {"title": "14.2.4 Linear kernels", "heading_level": null, "page_id": 3, "polygon": [[88.59375, 288.0], [198.75, 288.0], [198.75, 298.6875], [88.59375, 298.6875]]}, {"title": "14.2.5 Matern kernels", "heading_level": null, "page_id": 3, "polygon": [[89.25, 509.25], [201.75, 509.25], [201.75, 519.5390625], [89.25, 519.5390625]]}, {"title": "14.2.6 String kernels", "heading_level": null, "page_id": 4, "polygon": [[87.75, 176.25], [196.875, 176.25], [196.875, 186.6796875], [87.75, 186.6796875]]}, {"title": "PHRRDLCSRSIWLARKIRSDLTALTESYVKHQGLWSELTEAERLQENLQAYRTFHVLLA\nRLLEDQQVHFTPTEGDFHQAIHTLLLQVAAFAYQIEELMILLEYKIPRNEADGMLFEKK\nLWGLKVLQELSQWTVRSIHDLRFISSHQTGIP", "heading_level": null, "page_id": 4, "polygon": [[129.65625, 357.75], [441.28125, 357.75], [441.28125, 391.078125], [129.65625, 391.078125]]}, {"title": "14.2.7 Pyramid match kernels", "heading_level": null, "page_id": 5, "polygon": [[89.25, 421.5], [240.890625, 421.5], [240.890625, 431.89453125], [89.25, 431.89453125]]}, {"title": "14.2.8 Kernels derived from probabilistic generative models", "heading_level": null, "page_id": 6, "polygon": [[87.1875, 61.5], [382.5, 61.5], [382.5, 71.86376953125], [87.1875, 71.86376953125]]}, {"title": "******** Probability product kernels", "heading_level": null, "page_id": 6, "polygon": [[84.75, 129.75], [249.75, 129.75], [249.75, 139.6142578125], [84.75, 139.6142578125]]}, {"title": "******** Fisher kernels", "heading_level": null, "page_id": 6, "polygon": [[83.25, 405.0], [192.0, 405.0], [192.0, 415.125], [83.25, 415.125]]}, {"title": "14.3 Using kernels inside GLMs", "heading_level": null, "page_id": 7, "polygon": [[95.25, 344.25], [267.0, 344.25], [267.0, 355.32421875], [95.25, 355.32421875]]}, {"title": "14.3.1 Kernel machines", "heading_level": null, "page_id": 7, "polygon": [[90.75, 405.75], [209.25, 405.75], [209.25, 415.7578125], [90.75, 415.7578125]]}, {"title": "14.3.2 L1VMs, RVMs, and other sparse vector machines", "heading_level": null, "page_id": 8, "polygon": [[89.25, 469.5], [357.0, 469.5], [357.0, 480.3046875], [89.25, 480.3046875]]}, {"title": "14.4 The kernel trick", "heading_level": null, "page_id": 9, "polygon": [[95.25, 507.75], [213.75, 507.75], [213.75, 518.58984375], [95.25, 518.58984375]]}, {"title": "14.4.1 Kernelized nearest neighbor classification", "heading_level": null, "page_id": 10, "polygon": [[90.0, 450.75], [330.0, 450.75], [330.0, 461.63671875], [90.0, 461.63671875]]}, {"title": "14.4.2 Kernelized K-medoids clustering", "heading_level": null, "page_id": 10, "polygon": [[88.59375, 558.75], [285.0, 558.75], [285.0, 569.84765625], [88.59375, 569.84765625]]}, {"title": "Algorithm 14.1: K-medoids algorithm", "heading_level": null, "page_id": 13, "polygon": [[132.890625, 64.5], [285.328125, 64.5], [285.328125, 74.197265625], [132.890625, 74.197265625]]}, {"title": "14.4.3 Kernelized ridge regression", "heading_level": null, "page_id": 13, "polygon": [[88.453125, 170.25], [261.0, 170.25], [261.0, 180.66796875], [88.453125, 180.66796875]]}, {"title": "14.4.3.1 The primal problem", "heading_level": null, "page_id": 13, "polygon": [[85.5, 237.75], [216.75, 237.75], [216.75, 247.74609375], [85.5, 247.74609375]]}, {"title": "14.4.3.2 The dual problem", "heading_level": null, "page_id": 13, "polygon": [[84.0, 355.5], [207.75, 355.5], [207.75, 365.1328125], [84.0, 365.1328125]]}, {"title": "14.4.3.3 Computational cost", "heading_level": null, "page_id": 14, "polygon": [[84.0, 458.25], [215.25, 458.25], [215.25, 467.33203125], [84.0, 467.33203125]]}, {"title": "14.4.4 Kernel PCA", "heading_level": null, "page_id": 14, "polygon": [[88.453125, 550.5], [184.5, 550.5], [184.5, 560.671875], [88.453125, 560.671875]]}, {"title": "14.5 Support vector machines (SVMs)", "heading_level": null, "page_id": 17, "polygon": [[95.25, 342.0], [297.0, 342.0], [297.0, 352.79296875], [95.25, 352.79296875]]}, {"title": "14.5.1 SVMs for regression", "heading_level": null, "page_id": 18, "polygon": [[90.75, 430.5], [225.0, 430.5], [225.0, 440.4375], [90.75, 440.4375]]}, {"title": "14.5.2 SVMs for classification", "heading_level": null, "page_id": 19, "polygon": [[89.25, 496.5], [237.0, 496.5], [237.0, 506.25], [89.25, 506.25]]}, {"title": "******** Hinge loss", "heading_level": null, "page_id": 19, "polygon": [[85.078125, 552.75], [175.78125, 552.75], [175.78125, 561.9375], [85.078125, 561.9375]]}, {"title": "******** The large margin principle", "heading_level": null, "page_id": 22, "polygon": [[84.0, 62.25], [246.0, 62.25], [246.0, 71.62646484375], [84.0, 71.62646484375]]}, {"title": "******** Probabilistic output", "heading_level": null, "page_id": 23, "polygon": [[83.53125, 350.25], [216.5625, 350.25], [216.5625, 360.38671875], [83.53125, 360.38671875]]}, {"title": "******** SVMs for multi-class classification", "heading_level": null, "page_id": 24, "polygon": [[83.25, 253.5], [276.75, 253.5], [276.75, 263.56640625], [83.25, 263.56640625]]}, {"title": "14.5.3 <PERSON><PERSON>ing C", "heading_level": null, "page_id": 25, "polygon": [[88.3828125, 254.25], [187.875, 254.25], [187.875, 264.357421875], [88.3828125, 264.357421875]]}, {"title": "14.5.4 Summary of key points", "heading_level": null, "page_id": 25, "polygon": [[89.25, 477.75], [241.03125, 477.75], [241.03125, 488.53125], [89.25, 488.53125]]}, {"title": "14.5.5 A probabilistic interpretation of SVMs", "heading_level": null, "page_id": 26, "polygon": [[88.5234375, 270.0], [308.25, 270.0], [308.25, 280.3359375], [88.5234375, 280.3359375]]}, {"title": "14.6 Comparison of discriminative kernel methods", "heading_level": null, "page_id": 26, "polygon": [[95.203125, 543.0], [366.75, 543.0], [366.75, 554.02734375], [95.203125, 554.02734375]]}, {"title": "14.7 Kernels for building generative models", "heading_level": null, "page_id": 28, "polygon": [[95.25, 291.0], [332.25, 291.0], [332.25, 302.958984375], [95.25, 302.958984375]]}, {"title": "14.7.1 Smoothing kernels", "heading_level": null, "page_id": 28, "polygon": [[90.28125, 375.75], [219.375, 375.75], [219.375, 386.96484375], [90.28125, 386.96484375]]}, {"title": "14.7.2 Kernel density estimation (KDE)", "heading_level": null, "page_id": 29, "polygon": [[89.25, 290.25], [281.25, 290.25], [281.25, 300.75], [89.25, 300.427734375]]}, {"title": "14.7.3 From KDE to KNN", "heading_level": null, "page_id": 30, "polygon": [[89.25, 513.75], [215.71875, 513.75], [215.71875, 524.6015625], [89.25, 524.6015625]]}, {"title": "14.7.4 Kernel regression", "heading_level": null, "page_id": 31, "polygon": [[89.25, 524.25], [213.75, 524.25], [213.75, 534.09375], [89.25, 534.09375]]}, {"title": "14.7.5 Locally weighted regression", "heading_level": null, "page_id": 33, "polygon": [[89.25, 147.75], [261.0, 147.75], [261.0, 158.1240234375], [89.25, 158.1240234375]]}, {"title": "Exercises", "heading_level": null, "page_id": 33, "polygon": [[129.1640625, 544.5], [178.5, 544.5], [178.5, 554.66015625], [129.1640625, 554.66015625]]}, {"title": "Exercise 14.2 Linear separability", "heading_level": null, "page_id": 34, "polygon": [[129.75, 264.0], [249.0, 264.0], [249.0, 273.0], [129.75, 273.0]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 195], ["Line", 27], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7514, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 75], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 584], ["Line", 68], ["Equation", 9], ["Text", 7], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 58], ["Text", 7], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 950, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 40], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 27], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 709, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 52], ["Text", 8], ["Equation", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 64], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 809, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 65], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1000, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 43], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 61], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 887, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 64], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 947, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 991, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 50], ["Text", 6], ["Equation", 6], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 71], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 960, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 760], ["Line", 83], ["TextInlineMath", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TextInlineMath", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 50], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 920, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 58], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 806, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 47], ["Equation", 8], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 57], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 33], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 56], ["TextInlineMath", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 37], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 817, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 48], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 54], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 934, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["TableCell", 96], ["Line", 50], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1474, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 42], ["ListItem", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 57], ["Text", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 52], ["Text", 9], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 59], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 946, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 56], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 772, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 72], ["Equation", 9], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 57], ["Text", 5], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 18], ["ListItem", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-18"}