{"table_of_contents": [{"title": "9.2 Tree-Based Methods", "heading_level": null, "page_id": 0, "polygon": [[132.0, 111.0], [292.5, 111.0], [292.5, 124.13671875], [132.0, 124.13671875]]}, {"title": "9.2.1 Background", "heading_level": null, "page_id": 0, "polygon": [[133.5, 138.75], [232.189453125, 138.75], [232.189453125, 149.3701171875], [133.5, 149.3701171875]]}, {"title": "9.2.2 Regression Trees", "heading_level": null, "page_id": 2, "polygon": [[133.5, 156.0], [256.5, 156.0], [256.5, 167.642578125], [133.5, 167.642578125]]}, {"title": "308 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 3, "polygon": [[132.0, 89.25], [367.857421875, 89.25], [367.857421875, 98.46826171875], [132.0, 98.46826171875]]}, {"title": "9.2.3 Classification Trees", "heading_level": null, "page_id": 3, "polygon": [[133.27734375, 597.0], [270.0, 597.0], [270.0, 607.921875], [133.27734375, 607.921875]]}, {"title": "310 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 5, "polygon": [[132.0, 88.5], [367.857421875, 88.5], [367.857421875, 98.419921875], [132.0, 98.419921875]]}, {"title": "9.2.4 Other Issues", "heading_level": null, "page_id": 5, "polygon": [[133.5, 286.5], [235.5, 286.5], [235.5, 298.353515625], [133.5, 298.353515625]]}, {"title": "Categorical Predictors", "heading_level": null, "page_id": 5, "polygon": [[133.5, 306.0], [231.591796875, 306.0], [231.591796875, 316.3359375], [133.5, 316.3359375]]}, {"title": "The Loss Matrix", "heading_level": null, "page_id": 5, "polygon": [[133.5, 561.75], [207.38671875, 561.75], [207.38671875, 572.34375], [133.5, 572.34375]]}, {"title": "Missing Predictor Values", "heading_level": null, "page_id": 6, "polygon": [[133.5, 246.0], [243.75, 246.0], [243.75, 255.62109375], [133.5, 255.62109375]]}, {"title": "Why Binary Splits?", "heading_level": null, "page_id": 6, "polygon": [[133.5, 549.0], [221.1328125, 549.0], [221.1328125, 559.96875], [133.5, 559.96875]]}, {"title": "312 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 7, "polygon": [[132.0, 88.5], [368.25, 88.5], [368.25, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "Other Tree-Building Procedures", "heading_level": null, "page_id": 7, "polygon": [[133.5, 114.0], [271.3359375, 114.0], [271.3359375, 123.169921875], [133.5, 123.169921875]]}, {"title": "Linear Combination Splits", "heading_level": null, "page_id": 7, "polygon": [[133.5, 291.0], [249.9697265625, 291.0], [249.9697265625, 301.25390625], [133.5, 301.25390625]]}, {"title": "Instability of Trees", "heading_level": null, "page_id": 7, "polygon": [[132.75, 420.75], [216.7998046875, 420.75], [216.7998046875, 431.19140625], [132.75, 431.19140625]]}, {"title": "Lack of Smoothness", "heading_level": null, "page_id": 7, "polygon": [[133.5, 561.75], [220.5, 561.75], [220.5, 572.34375], [133.5, 572.34375]]}, {"title": "Difficulty in Capturing Additive Structure", "heading_level": null, "page_id": 8, "polygon": [[133.5, 258.75], [316.5, 258.75], [316.5, 268.962890625], [133.5, 268.962890625]]}, {"title": "9.2.5 Spam Example (Continued)", "heading_level": null, "page_id": 8, "polygon": [[133.20263671875, 441.0], [311.677734375, 441.0], [311.677734375, 453.234375], [133.20263671875, 453.234375]]}, {"title": "9.3 PRIM: Bump Hunting", "heading_level": null, "page_id": 12, "polygon": [[132.0, 601.734375], [305.701171875, 601.734375], [305.701171875, 614.8828125], [132.0, 614.8828125]]}, {"title": "318 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 13, "polygon": [[132.0, 88.5], [368.15625, 88.5], [368.15625, 98.806640625], [132.0, 98.806640625]]}, {"title": "Algorithm 9.3 Patient Rule Induction Method.", "heading_level": null, "page_id": 15, "polygon": [[132.75, 113.25], [345.0, 113.25], [345.0, 123.**********], [132.75, 123.**********]]}, {"title": "9.3.1 Spam Example (Continued)", "heading_level": null, "page_id": 15, "polygon": [[133.5, 596.25], [312.0, 596.25], [312.0, 608.6953125], [133.5, 608.6953125]]}, {"title": "9.4 MARS: Multivariate Adaptive Regression\nSplines", "heading_level": null, "page_id": 16, "polygon": [[132.0, 479.25], [422.25, 479.25], [422.25, 509.30859375], [132.0, 509.30859375]]}, {"title": "9.4.1 Spam Example (Continued)", "heading_level": null, "page_id": 21, "polygon": [[133.5, 468.0], [312.0, 468.0], [312.0, 481.46484375], [133.5, 481.46484375]]}, {"title": "9.4.2 Example (Simulated Data)", "heading_level": null, "page_id": 22, "polygon": [[132.75, 111.75], [307.79296875, 111.75], [307.79296875, 123.**********], [132.75, 123.**********]]}, {"title": "9.4.3 Other Issues", "heading_level": null, "page_id": 23, "polygon": [[132.75, 216.75], [234.75, 216.75], [234.75, 228.357421875], [132.75, 228.357421875]]}, {"title": "MARS for Classification", "heading_level": null, "page_id": 23, "polygon": [[132.0, 235.5], [240.85546875, 235.5], [240.85546875, 246.33984375], [132.0, 246.33984375]]}, {"title": "Relationship of MARS to CART", "heading_level": null, "page_id": 23, "polygon": [[133.5, 492.75], [276.8642578125, 492.75], [276.8642578125, 503.12109375], [133.5, 503.12109375]]}, {"title": "Mixed Inputs", "heading_level": null, "page_id": 24, "polygon": [[133.5, 203.25], [192.4453125, 203.25], [192.4453125, 213.46875], [133.5, 213.46875]]}, {"title": "9.5 Hierarchical Mixtures of Experts", "heading_level": null, "page_id": 24, "polygon": [[132.0, 338.25], [367.55859375, 338.25], [367.55859375, 351.720703125], [132.0, 351.720703125]]}, {"title": "332 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 27, "polygon": [[132.0, 88.5], [367.55859375, 88.5], [367.55859375, 98.419921875], [132.0, 98.419921875]]}, {"title": "9.6 Missing Data", "heading_level": null, "page_id": 27, "polygon": [[132.0, 236.25], [246.75, 236.25], [246.75, 249.240234375], [132.0, 249.240234375]]}, {"title": "334 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 29, "polygon": [[132.0, 88.5], [369.3515625, 88.5], [369.3515625, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "9.7 Computational Considerations", "heading_level": null, "page_id": 29, "polygon": [[132.0, 111.0], [354.41015625, 111.0], [354.41015625, 123.943359375], [132.0, 123.943359375]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 29, "polygon": [[132.0, 411.46875], [255.0, 411.46875], [255.0, 424.6171875], [132.0, 424.6171875]]}, {"title": "Exercises", "heading_level": null, "page_id": 30, "polygon": [[132.0, 111.0], [190.5029296875, 111.0], [190.5029296875, 124.2333984375], [132.0, 124.2333984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 44], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7214, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 30], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 53], ["Text", 7], ["Equation", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 52], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 53], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1975, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 42], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 43], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 40], ["SectionHeader", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 39], ["TableCell", 22], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 983, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 32], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 81], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1335, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 62], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 41], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 45], ["Text", 9], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 4350], ["Line", 2015], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5519, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 35], ["ListItem", 6], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["TableCell", 62], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8444, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 49], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 23], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 846, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 30], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1168, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 48], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 55], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 833, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 44], ["Text", 8], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 39], ["TableCell", 16], ["Text", 5], ["SectionHeader", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1998, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 47], ["TextInlineMath", 2], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 814, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 52], ["Text", 7], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 39], ["Text", 5], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 39], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 38], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 118], ["TableCell", 24], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7542, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 36], ["ListItem", 7], ["TextInlineMath", 3], ["Text", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_324-355"}