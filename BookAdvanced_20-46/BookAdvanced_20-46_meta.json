{"table_of_contents": [{"title": "1\nIntroduction", "heading_level": null, "page_id": 0, "polygon": [[132.0, 110.5048828125], [237.0, 110.5048828125], [237.0, 161.9384765625], [132.0, 161.9384765625]]}, {"title": "2 1. Introduction", "heading_level": null, "page_id": 1, "polygon": [[132.0, 88.5], [225.1669921875, 88.5], [225.1669921875, 99.0], [132.0, 99.0]]}, {"title": "Example 1: <PERSON><PERSON>", "heading_level": null, "page_id": 1, "polygon": [[133.5, 387.0], [257.25, 387.0], [257.25, 397.93359375], [133.5, 397.93359375]]}, {"title": "Example 2: Prostate Cancer", "heading_level": null, "page_id": 2, "polygon": [[132.75, 560.25], [279.0, 560.25], [279.0, 572.34375], [132.75, 572.34375]]}, {"title": "Example 3: Handwritten Digit Recognition", "heading_level": null, "page_id": 3, "polygon": [[133.5, 501.0], [351.0, 501.0], [351.0, 512.7890625], [133.5, 512.7890625]]}, {"title": "Example 4: DNA Expression Microarrays", "heading_level": null, "page_id": 4, "polygon": [[133.5, 157.5], [347.23828125, 157.5], [347.23828125, 169.189453125], [133.5, 169.189453125]]}, {"title": "6 1. Introduction", "heading_level": null, "page_id": 5, "polygon": [[131.25, 87.75], [225.017578125, 87.75], [225.017578125, 98.806640625], [131.25, 98.806640625]]}, {"title": "Who Should Read this Book", "heading_level": null, "page_id": 6, "polygon": [[134.25, 112.5], [276.8642578125, 112.5], [276.8642578125, 123.36328125], [134.25, 123.36328125]]}, {"title": "How This Book is Organized", "heading_level": null, "page_id": 6, "polygon": [[133.5, 345.75], [280.5, 345.75], [280.5, 356.748046875], [133.5, 356.748046875]]}, {"title": "Book Website", "heading_level": null, "page_id": 7, "polygon": [[132.75, 219.75], [204.0, 219.75], [204.0, 231.837890625], [132.75, 231.837890625]]}, {"title": "http://www-stat.stanford.edu/ElemStatLearn", "heading_level": null, "page_id": 7, "polygon": [[192.5947265625, 261.75], [394.5, 261.75], [394.5, 271.86328125], [192.5947265625, 271.86328125]]}, {"title": "Note for Instructors", "heading_level": null, "page_id": 7, "polygon": [[133.5, 326.25], [237.0, 326.25], [237.0, 337.9921875], [133.5, 337.9921875]]}, {"title": "2\nOverview of Supervised Learning", "heading_level": null, "page_id": 8, "polygon": [[130.66259765625, 109.5], [408.0, 109.5], [408.0, 162.03515625], [130.66259765625, 162.03515625]]}, {"title": "2.1 Introduction", "heading_level": null, "page_id": 8, "polygon": [[132.0, 354.75], [243.3955078125, 354.75], [243.3955078125, 367.576171875], [132.0, 367.576171875]]}, {"title": "2.2 Variable Types and Terminology", "heading_level": null, "page_id": 8, "polygon": [[132.0, 541.5], [367.259765625, 541.5], [367.259765625, 554.94140625], [132.0, 554.94140625]]}, {"title": "10 2. Overview of Supervised Learning", "heading_level": null, "page_id": 9, "polygon": [[132.75, 88.5], [312.0, 88.5], [312.0, 98.85498046875], [132.75, 98.85498046875]]}, {"title": "2.3 Two Simple Approaches to Prediction: Least\nSquares and Nearest Neighbors", "heading_level": null, "page_id": 10, "polygon": [[132.0, 345.33984375], [441.0703125, 345.33984375], [441.0703125, 375.1171875], [132.0, 375.1171875]]}, {"title": "2.3.1 Linear Models and Least Squares", "heading_level": null, "page_id": 10, "polygon": [[133.20263671875, 480.75], [339.75, 480.75], [339.75, 491.90625], [133.20263671875, 491.90625]]}, {"title": "12 2. Overview of Supervised Learning", "heading_level": null, "page_id": 11, "polygon": [[132.0, 88.5], [312.0, 88.5], [312.0, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "14 2. Overview of Supervised Learning", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [312.0, 89.25], [312.0, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "2.3.2 Nearest-Neighbor Methods", "heading_level": null, "page_id": 13, "polygon": [[133.35205078125, 275.25], [304.8046875, 275.25], [304.8046875, 286.751953125], [133.35205078125, 286.751953125]]}, {"title": "16 2. Overview of Supervised Learning", "heading_level": null, "page_id": 15, "polygon": [[132.0, 88.5], [312.0, 88.5], [312.0, 99.0], [132.0, 99.0]]}, {"title": "2.3.3 From Least Squares to Nearest Neighbors", "heading_level": null, "page_id": 15, "polygon": [[133.5, 417.0], [382.201171875, 417.0], [382.201171875, 428.484375], [133.5, 428.484375]]}, {"title": "2.4 Statistical Decision Theory", "heading_level": null, "page_id": 17, "polygon": [[132.0, 229.517578125], [332.595703125, 229.517578125], [332.595703125, 243.439453125], [132.0, 243.439453125]]}, {"title": "22 2. Overview of Supervised Learning", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [311.37890625, 88.5], [311.37890625, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "2.5 Local Methods in High Dimensions", "heading_level": null, "page_id": 21, "polygon": [[132.0, 338.572265625], [383.09765625, 338.572265625], [383.09765625, 351.333984375], [132.0, 351.333984375]]}, {"title": "24 2. Overview of Supervised Learning", "heading_level": null, "page_id": 23, "polygon": [[132.0, 88.5], [312.0, 88.5], [312.0, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "26 2. Overview of Supervised Learning", "heading_level": null, "page_id": 25, "polygon": [[132.0, 88.5], [312.0, 88.5], [312.0, 98.75830078125], [132.0, 98.75830078125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 25], ["ListItem", 5], ["Text", 4], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5687, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["TableCell", 57], ["Line", 39], ["Text", 7], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3460, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 5447], ["Line", 2294], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6272, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 25], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 41], ["Text", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["Line", 31], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1663, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 42], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 19], ["Text", 6], ["SectionHeader", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 24], ["Text", 3], ["SectionHeader", 3], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 46], ["Text", 6], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 332], ["Line", 41], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 54], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 35], ["Line", 30], ["Caption", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 46], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 55], ["Line", 32], ["Text", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 211], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 982, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 36], ["Text", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 48], ["Equation", 5], ["ListItem", 4], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 43], ["TextInlineMath", 4], ["Text", 4], ["Equation", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 44], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Line", 31], ["Span", 19], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 42], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 53], ["Text", 3], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 40], ["Text", 4], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 101], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1285, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["Line", 93], ["TextInlineMath", 3], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 14054, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 52], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_20-46"}