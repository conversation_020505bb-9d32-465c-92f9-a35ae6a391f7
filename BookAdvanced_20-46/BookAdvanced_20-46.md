This is page 1 Printer: Opaque this

# 1 Introduction

Statistical learning plays a key role in many areas of science, finance and industry. Here are some examples of learning problems:

- Predict whether a patient, hospitalized due to a heart attack, will have a second heart attack. The prediction is to be based on demographic, diet and clinical measurements for that patient.
- Predict the price of a stock in 6 months from now, on the basis of company performance measures and economic data.
- Identify the numbers in a handwritten ZIP code, from a digitized image.
- Estimate the amount of glucose in the blood of a diabetic person, from the infrared absorption spectrum of that person's blood.
- Identify the risk factors for prostate cancer, based on clinical and demographic variables.

The science of learning plays a key role in the fields of statistics, data mining and artificial intelligence, intersecting with areas of engineering and other disciplines.

This book is about learning from data. In a typical scenario, we have an outcome measurement, usually quantitative (such as a stock price) or categorical (such as heart attack/no heart attack), that we wish to predict based on a set of features (such as diet and clinical measurements). We have a training set of data, in which we observe the outcome and feature

## 1. Introduction

TABLE 1.1. Average percentage of words or characters in an email message equal to the indicated word or character. We have chosen the words and characters showing the largest difference between spam and email.

|       | george | you  | your | hp   | free | hpl  | !    | our  | re   | edu  | remove |
|-------|--------|------|------|------|------|------|------|------|------|------|--------|
| spam  | 0.00   | 2.26 | 1.38 | 0.02 | 0.52 | 0.01 | 0.51 | 0.51 | 0.13 | 0.01 | 0.28   |
| email | 1.27   | 1.27 | 0.44 | 0.90 | 0.07 | 0.43 | 0.11 | 0.18 | 0.42 | 0.29 | 0.01   |

measurements for a set of objects (such as people). Using this data we build a prediction model, or learner, which will enable us to predict the outcome for new unseen objects. A good learner is one that accurately predicts such an outcome.

The examples above describe what is called the supervised learning problem. It is called "supervised" because of the presence of the outcome variable to guide the learning process. In the unsupervised learning problem, we observe only the features and have no measurements of the outcome. Our task is rather to describe how the data are organized or clustered. We devote most of this book to supervised learning; the unsupervised problem is less developed in the literature, and is the focus of Chapter 14.

Here are some examples of real learning problems that are discussed in this book.

## Example 1: Email Spam

The data for this example consists of information from 4601 email messages, in a study to try to predict whether the email was junk email, or "spam." The objective was to design an automatic spam detector that could filter out spam before clogging the users' mailboxes. For all 4601 email messages, the true outcome (email type) email or spam is available, along with the relative frequencies of 57 of the most commonly occurring words and punctuation marks in the email message. This is a supervised learning problem, with the outcome the class variable email/spam. It is also called a classification problem.

Table 1.1 lists the words and characters showing the largest average difference between spam and email.

Our learning method has to decide which features to use and how: for example, we might use a rule such as

if 
$$
(\% \text{george} < 0.6) \& (\% \text{you} > 1.5)
$$
 then spam else email.

Another form of a rule might be:

if  $(0.2 \cdot \text{%you} - 0.3 \cdot \text{%george}) > 0$  then spam else email.

Image /page/2/Figure/1 description: This is a scatterplot matrix of prostate cancer data. The matrix is an 8x8 grid of scatterplots, where each plot shows the relationship between two variables. The variables are labeled on the axes and include 'lpsa', 'lcavol', 'lweight', 'age', 'lbph', 'svi', 'lcp', and 'gleason'. The diagonal plots are not shown, but the off-diagonal plots display the pairwise relationships between these variables. The axes of the plots are labeled with numerical ranges corresponding to the data values for each variable. The plots are filled with purple dots representing individual data points.

FIGURE 1.1. Scatterplot matrix of the prostate cancer data. The first row shows the response against each of the predictors in turn. Two of the predictors, svi and gleason, are categorical.

For this problem not all errors are equal; we want to avoid filtering out good email, while letting spam get through is not desirable but less serious in its consequences. We discuss a number of different methods for tackling this learning problem in the book.

# Example 2: Prostate Cancer

The data for this example, displayed in Figure  $1.1<sup>1</sup>$ , come from a study by Stamey et al. (1989) that examined the correlation between the level of

<sup>&</sup>lt;sup>1</sup>There was an error in these data in the first edition of this book. Subject 32 had a value of 6.1 for lweight, which translates to a 449 gm prostate! The correct value is 44.9 gm. We are grateful to Prof. Stephen W. Link for alerting us to this error.

Image /page/3/Picture/1 description: The image displays a grid of handwritten digits from 0 to 9, arranged in rows and columns. Each row appears to contain a sequence of digits, with some variations in handwriting style. The grid is overlaid with green lines, clearly demarcating each individual digit. The digits are presented in black ink on a white background. The overall impression is a dataset or a visual representation of handwritten numerical characters.

FIGURE 1.2. Examples of handwritten digits from U.S. postal envelopes.

prostate specific antigen (PSA) and a number of clinical measures, in 97 men who were about to receive a radical prostatectomy.

The goal is to predict the log of PSA (1psa) from a number of measurements including log cancer volume (lcavol), log prostate weight lweight, age, log of benign prostatic hyperplasia amount lbph, seminal vesicle invasion svi, log of capsular penetration lcp, Gleason score gleason, and percent of Gleason scores 4 or 5 pgg45. Figure 1.1 is a scatterplot matrix of the variables. Some correlations with lpsa are evident, but a good predictive model is difficult to construct by eye.

This is a supervised learning problem, known as a regression problem, because the outcome measurement is quantitative.

## Example 3: Handwritten Digit Recognition

The data from this example come from the handwritten ZIP codes on envelopes from U.S. postal mail. Each image is a segment from a five digit ZIP code, isolating a single digit. The images are  $16 \times 16$  eight-bit grayscale maps, with each pixel ranging in intensity from 0 to 255. Some sample images are shown in Figure 1.2.

The images have been normalized to have approximately the same size and orientation. The task is to predict, from the  $16 \times 16$  matrix of pixel intensities, the identity of each image  $(0, 1, \ldots, 9)$  quickly and accurately. If it is accurate enough, the resulting algorithm would be used as part of an automatic sorting procedure for envelopes. This is a classification problem for which the error rate needs to be kept very low to avoid misdirection of mail. In order to achieve this low error rate, some objects can be assigned to a "don't know" category, and sorted instead by hand.

## Example 4: DNA Expression Microarrays

DNA stands for deoxyribonucleic acid, and is the basic material that makes up human chromosomes. DNA microarrays measure the expression of a gene in a cell by measuring the amount of mRNA (messenger ribonucleic acid) present for that gene. Microarrays are considered a breakthrough technology in biology, facilitating the quantitative study of thousands of genes simultaneously from a single sample of cells.

Here is how a DNA microarray works. The nucleotide sequences for a few thousand genes are printed on a glass slide. A target sample and a reference sample are labeled with red and green dyes, and each are hybridized with the DNA on the slide. Through fluoroscopy, the log (red/green) intensities of RNA hybridizing at each site is measured. The result is a few thousand numbers, typically ranging from say  $-6$  to 6, measuring the expression level of each gene in the target relative to the reference sample. Positive values indicate higher expression in the target versus the reference, and vice versa for negative values.

A gene expression dataset collects together the expression values from a series of DNA microarray experiments, with each column representing an experiment. There are therefore several thousand rows representing individual genes, and tens of columns representing samples: in the particular example of Figure 1.3 there are 6830 genes (rows) and 64 samples (columns), although for clarity only a random sample of 100 rows are shown. The figure displays the data set as a heat map, ranging from green (negative) to red (positive). The samples are 64 cancer tumors from different patients.

The challenge here is to understand how the genes and samples are organized. Typical questions include the following:

- (a) which samples are most similar to each other, in terms of their expression profiles across genes?
- (b) which genes are most similar to each other, in terms of their expression profiles across samples?
- (c) do certain genes show very high (or low) expression for certain cancer samples?

We could view this task as a regression problem, with two categorical predictor variables—genes and samples—with the response variable being the level of expression. However, it is probably more useful to view it as unsupervised learning problem. For example, for question (a) above, we think of the samples as points in 6830–dimensional space, which we want to cluster together in some way.

## 1. Introduction

Image /page/5/Figure/1 description: A heatmap displays DNA microarray data, showing the expression of 6830 genes across various tissue types. The tissue types are listed along the bottom axis and include BREAST, RENAL, MELANOMA, COLON, K562B-repro, MCF7D-repro, NSCLC, LEUKEMIA, CNS, OVARIAN, PROSTATE, and UNKNOWN. The genes are listed along the right side of the heatmap with identifiers such as SIDW299104, GNAL, and ESTsChr.1. The heatmap uses a color scale where red indicates high expression, green indicates low expression, and gray indicates missing data. The pattern of gene expression varies across the different tissue types, suggesting distinct biological profiles for each.

FIGURE 1.3. DNA microarray data: expression matrix of 6830 genes (rows) and 64 samples (columns), for the human tumor data. Only a random sample of 100 rows are shown. The display is a heat map, ranging from bright green (negative, under expressed) to bright red (positive, over expressed). Missing values are gray. The rows and columns are displayed in a randomly chosen order.

## Who Should Read this Book

This book is designed for researchers and students in a broad variety of fields: statistics, artificial intelligence, engineering, finance and others. We expect that the reader will have had at least one elementary course in statistics, covering basic topics including linear regression.

We have not attempted to write a comprehensive catalog of learning methods, but rather to describe some of the most important techniques. Equally notable, we describe the underlying concepts and considerations by which a researcher can judge a learning method. We have tried to write this book in an intuitive fashion, emphasizing concepts rather than mathematical details.

As statisticians, our exposition will naturally reflect our backgrounds and areas of expertise. However in the past eight years we have been attending conferences in neural networks, data mining and machine learning, and our thinking has been heavily influenced by these exciting fields. This influence is evident in our current research, and in this book.

## How This Book is Organized

Our view is that one must understand simple methods before trying to grasp more complex ones. Hence, after giving an overview of the supervising learning problem in Chapter 2, we discuss linear methods for regression and classification in Chapters 3 and 4. In Chapter 5 we describe splines, wavelets and regularization/penalization methods for a single predictor, while Chapter 6 covers kernel methods and local regression. Both of these sets of methods are important building blocks for high-dimensional learning techniques. Model assessment and selection is the topic of Chapter 7, covering the concepts of bias and variance, overfitting and methods such as cross-validation for choosing models. Chapter 8 discusses model inference and averaging, including an overview of maximum likelihood, Bayesian inference and the bootstrap, the EM algorithm, Gibbs sampling and bagging, A related procedure called boosting is the focus of Chapter 10.

In Chapters 9–13 we describe a series of structured methods for supervised learning, with Chapters 9 and 11 covering regression and Chapters 12 and 13 focusing on classification. Chapter 14 describes methods for unsupervised learning. Two recently proposed techniques, random forests and ensemble learning, are discussed in Chapters 15 and 16. We describe undirected graphical models in Chapter 17 and finally we study highdimensional problems in Chapter 18.

At the end of each chapter we discuss computational considerations important for data mining applications, including how the computations scale with the number of observations and predictors. Each chapter ends with Bibliographic Notes giving background references for the material.

8 1. Introduction

We recommend that Chapters 1–4 be first read in sequence. Chapter 7 should also be considered mandatory, as it covers central concepts that pertain to all learning methods. With this in mind, the rest of the book can be read sequentially, or sampled, depending on the reader's interest.

The symbol  $\mathbf{\mathbf{\mathcal{U}}}$  indicates a technically difficult section, one that can be skipped without interrupting the flow of the discussion.

# Book Website

The website for this book is located at

#### http://www-stat.stanford.edu/ElemStatLearn

It contains a number of resources, including many of the datasets used in this book.

## Note for Instructors

We have successively used the first edition of this book as the basis for a two-quarter course, and with the additional materials in this second edition, it could even be used for a three-quarter sequence. Exercises are provided at the end of each chapter. It is important for students to have access to good software tools for these topics. We used the R and S-PLUS programming languages in our courses.

This is page 9 Printer: Opaque this

# 2 Overview of Supervised Learning

## 2.1 Introduction

The first three examples described in Chapter 1 have several components in common. For each there is a set of variables that might be denoted as inputs, which are measured or preset. These have some influence on one or more *outputs*. For each example the goal is to use the inputs to predict the values of the outputs. This exercise is called supervised learning.

We have used the more modern language of machine learning. In the statistical literature the inputs are often called the predictors, a term we will use interchangeably with inputs, and more classically the *independent* variables. In the pattern recognition literature the term features is preferred, which we use as well. The outputs are called the *responses*, or classically the dependent variables.

## 2.2 Variable Types and Terminology

The outputs vary in nature among the examples. In the glucose prediction example, the output is a *quantitative* measurement, where some measurements are bigger than others, and measurements close in value are close in nature. In the famous Iris discrimination example due to R. A. Fisher, the output is qualitative (species of Iris) and assumes values in a finite set  $\mathcal{G} = \{ \text{Virginica}, \text{Setosa} \text{ and } \text{Versicolor} \}.$  In the handwritten digit example the output is one of 10 different digit *classes*:  $\mathcal{G} = \{0, 1, \ldots, 9\}$ . In both of

these there is no explicit ordering in the classes, and in fact often descriptive labels rather than numbers are used to denote the classes. Qualitative variables are also referred to as categorical or discrete variables as well as factors.

For both types of outputs it makes sense to think of using the inputs to predict the output. Given some specific atmospheric measurements today and yesterday, we want to predict the ozone level tomorrow. Given the grayscale values for the pixels of the digitized image of the handwritten digit, we want to predict its class label.

This distinction in output type has led to a naming convention for the prediction tasks: regression when we predict quantitative outputs, and classification when we predict qualitative outputs. We will see that these two tasks have a lot in common, and in particular both can be viewed as a task in function approximation.

Inputs also vary in measurement type; we can have some of each of qualitative and quantitative input variables. These have also led to distinctions in the types of methods that are used for prediction: some methods are defined most naturally for quantitative inputs, some most naturally for qualitative and some for both.

A third variable type is ordered categorical, such as small, medium and large, where there is an ordering between the values, but no metric notion is appropriate (the difference between medium and small need not be the same as that between large and medium). These are discussed further in Chapter 4.

Qualitative variables are typically represented numerically by codes. The easiest case is when there are only two classes or categories, such as "success" or "failure," "survived" or "died." These are often represented by a single binary digit or bit as 0 or 1, or else by  $-1$  and 1. For reasons that will become apparent, such numeric codes are sometimes referred to as targets. When there are more than two categories, several alternatives are available. The most useful and commonly used coding is via dummy variables. Here a  $K$ -level qualitative variable is represented by a vector of  $K$  binary variables or bits, only one of which is "on" at a time. Although more compact coding schemes are possible, dummy variables are symmetric in the levels of the factor.

We will typically denote an input variable by the symbol  $X$ . If  $X$  is a vector, its components can be accessed by subscripts  $X_i$ . Quantitative outputs will be denoted by  $Y$ , and qualitative outputs by  $G$  (for group). We use uppercase letters such as  $X, Y$  or  $G$  when referring to the generic aspects of a variable. Observed values are written in lowercase; hence the ith observed value of X is written as  $x_i$  (where  $x_i$  is again a scalar or vector). Matrices are represented by bold uppercase letters; for example, a set of N input p-vectors  $x_i$ ,  $i = 1, ..., N$  would be represented by the  $N \times p$ matrix  $X$ . In general, vectors will not be bold, except when they have  $N$ components; this convention distinguishes a *p*-vector of inputs  $x_i$  for the ith observation from the N-vector  $x_i$  consisting of all the observations on variable  $X_i$ . Since all vectors are assumed to be column vectors, the *i*th row of **X** is  $x_i^T$ , the vector transpose of  $x_i$ .

For the moment we can loosely state the learning task as follows: given the value of an input vector  $X$ , make a good prediction of the output  $Y$ , denoted by  $\hat{Y}$  (pronounced "y-hat"). If Y takes values in R then so should Y; likewise for categorical outputs, G should take values in the same set  $\mathcal G$ associated with G.

For a two-class  $G$ , one approach is to denote the binary coded target as Y, and then treat it as a quantitative output. The predictions  $\tilde{Y}$  will typically lie in [0, 1], and we can assign to  $\tilde{G}$  the class label according to whether  $\hat{y} > 0.5$ . This approach generalizes to K-level qualitative outputs as well.

We need data to construct prediction rules, often a lot of it. We thus suppose we have available a set of measurements  $(x_i, y_i)$  or  $(x_i, g_i)$ ,  $i =$  $1, \ldots, N$ , known as the *training data*, with which to construct our prediction rule.

# 2.3 Two Simple Approaches to Prediction: Least Squares and Nearest Neighbors

In this section we develop two simple but powerful prediction methods: the linear model fit by least squares and the k-nearest-neighbor prediction rule. The linear model makes huge assumptions about structure and yields stable but possibly inaccurate predictions. The method of  $k$ -nearest neighbors makes very mild structural assumptions: its predictions are often accurate but can be unstable.

### 2.3.1 Linear Models and Least Squares

The linear model has been a mainstay of statistics for the past 30 years and remains one of our most important tools. Given a vector of inputs  $X^T = (X_1, X_2, \ldots, X_p)$ , we predict the output Y via the model

$$
\hat{Y} = \hat{\beta}_0 + \sum_{j=1}^{p} X_j \hat{\beta}_j.
$$
\n(2.1)

The term  $\hat{\beta}_0$  is the intercept, also known as the *bias* in machine learning. Often it is convenient to include the constant variable 1 in X, include  $\hat{\beta}_0$  in the vector of coefficients  $\hat{\beta}$ , and then write the linear model in vector form as an inner product

$$
\hat{Y} = X^T \hat{\beta},\tag{2.2}
$$

where  $X<sup>T</sup>$  denotes vector or matrix transpose  $(X$  being a column vector). Here we are modeling a single output, so  $\hat{Y}$  is a scalar; in general  $\hat{Y}$  can be a K–vector, in which case  $\beta$  would be a  $p \times K$  matrix of coefficients. In the  $(p + 1)$ -dimensional input–output space,  $(X, \hat{Y})$  represents a hyperplane. If the constant is included in  $X$ , then the hyperplane includes the origin and is a subspace; if not, it is an affine set cutting the Y -axis at the point  $(0, \hat{\beta}_0)$ . From now on we assume that the intercept is included in  $\hat{\beta}$ .

Viewed as a function over the p-dimensional input space,  $f(X) = X^T \beta$ is linear, and the gradient  $f'(X) = \beta$  is a vector in input space that points in the steepest uphill direction.

How do we fit the linear model to a set of training data? There are many different methods, but by far the most popular is the method of *least squares*. In this approach, we pick the coefficients  $\beta$  to minimize the residual sum of squares

$$
RSS(\beta) = \sum_{i=1}^{N} (y_i - x_i^T \beta)^2.
$$
 (2.3)

 $RSS(\beta)$  is a quadratic function of the parameters, and hence its minimum always exists, but may not be unique. The solution is easiest to characterize in matrix notation. We can write

$$
RSS(\beta) = (\mathbf{y} - \mathbf{X}\beta)^{T}(\mathbf{y} - \mathbf{X}\beta),
$$
\n(2.4)

where **X** is an  $N \times p$  matrix with each row an input vector, and **y** is an N-vector of the outputs in the training set. Differentiating w.r.t.  $\beta$  we get the normal equations

$$
\mathbf{X}^T(\mathbf{y} - \mathbf{X}\beta) = 0.
$$
 (2.5)

If  $X^T X$  is nonsingular, then the unique solution is given by

$$
\hat{\beta} = (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{y},\tag{2.6}
$$

and the fitted value at the *i*th input  $x_i$  is  $\hat{y}_i = \hat{y}(x_i) = x_i^T \hat{\beta}$ . At an arbitrary input  $x_0$  the prediction is  $\hat{y}(x_0) = x_0^T \hat{\beta}$ . The entire fitted surface is characterized by the p parameters  $\hat{\beta}$ . Intuitively, it seems that we do not need a very large data set to fit such a model.

Let's look at an example of the linear model in a classification context. Figure 2.1 shows a scatterplot of training data on a pair of inputs  $X_1$  and  $X_2$ . The data are simulated, and for the present the simulation model is not important. The output class variable  $G$  has the values BLUE or ORANGE, and is represented as such in the scatterplot. There are 100 points in each of the two classes. The linear regression model was fit to these data, with the response Y coded as 0 for BLUE and 1 for **ORANGE**. The fitted values Y are converted to a fitted class variable  $G$  according to the rule

$$
\hat{G} = \begin{cases} \text{orance} & \text{if } \hat{Y} > 0.5, \\ \text{blue} & \text{if } \hat{Y} \le 0.5. \end{cases} \tag{2.7}
$$

Image /page/12/Figure/1 description: This is a scatter plot showing a linear regression for a binary response. The plot displays two classes of data points, represented by blue circles and orange circles. A black line, representing the regression line, separates the two classes. The background is filled with a dotted pattern, with blue dots in the lower region and orange dots in the upper region, indicating the predicted class for different areas of the plot.

Linear Regression of 0/1 Response

FIGURE 2.1. A classification example in two dimensions. The classes are coded as a binary variable (BLUE = 0, ORANGE = 1), and then fit by linear regression. The line is the decision boundary defined by  $x^T \hat{\beta} = 0.5$ . The orange shaded region denotes that part of input space classified as ORANGE, while the blue region is classified as BLUE.

The set of points in  $\mathbb{R}^2$  classified as **ORANGE** corresponds to  $\{x : x^T\hat{\beta} > 0.5\},\$ indicated in Figure 2.1, and the two predicted classes are separated by the *decision boundary*  $\{x : x^T\hat{\beta} = 0.5\}$ , which is linear in this case. We see that for these data there are several misclassifications on both sides of the decision boundary. Perhaps our linear model is too rigid— or are such errors unavoidable? Remember that these are errors on the training data itself, and we have not said where the constructed data came from. Consider the two possible scenarios:

- **Scenario 1:** The training data in each class were generated from bivariate Gaussian distributions with uncorrelated components and different means.
- Scenario 2: The training data in each class came from a mixture of 10 lowvariance Gaussian distributions, with individual means themselves distributed as Gaussian.

A mixture of Gaussians is best described in terms of the generative model. One first generates a discrete variable that determines which of

the component Gaussians to use, and then generates an observation from the chosen density. In the case of one Gaussian per class, we will see in Chapter 4 that a linear decision boundary is the best one can do, and that our estimate is almost optimal. The region of overlap is inevitable, and future data to be predicted will be plagued by this overlap as well.

In the case of mixtures of tightly clustered Gaussians the story is different. A linear decision boundary is unlikely to be optimal, and in fact is not. The optimal decision boundary is nonlinear and disjoint, and as such will be much more difficult to obtain.

We now look at another classification and regression procedure that is in some sense at the opposite end of the spectrum to the linear model, and far better suited to the second scenario.

### 2.3.2 Nearest-Neighbor Methods

Nearest-neighbor methods use those observations in the training set  $\mathcal T$  closest in input space to x to form  $\hat{Y}$ . Specifically, the k-nearest neighbor fit for  $\hat{Y}$  is defined as follows:

$$
\hat{Y}(x) = \frac{1}{k} \sum_{x_i \in N_k(x)} y_i,
$$
\n(2.8)

where  $N_k(x)$  is the neighborhood of x defined by the k closest points  $x_i$  in the training sample. Closeness implies a metric, which for the moment we assume is Euclidean distance. So, in words, we find the  $k$  observations with  $x_i$  closest to  $x$  in input space, and average their responses.

In Figure 2.2 we use the same training data as in Figure 2.1, and use 15-nearest-neighbor averaging of the binary coded response as the method of fitting. Thus  $\overline{Y}$  is the proportion of **ORANGE**'s in the neighborhood, and so assigning class **ORANGE** to  $\tilde{G}$  if  $\tilde{Y} > 0.5$  amounts to a majority vote in the neighborhood. The colored regions indicate all those points in input space classified as BLUE or ORANGE by such a rule, in this case found by evaluating the procedure on a fine grid in input space. We see that the decision boundaries that separate the **BLUE** from the **ORANGE** regions are far more irregular, and respond to local clusters where one class dominates.

Figure 2.3 shows the results for 1-nearest-neighbor classification:  $Y$  is assigned the value  $y_{\ell}$  of the closest point  $x_{\ell}$  to x in the training data. In this case the regions of classification can be computed relatively easily, and correspond to a *Voronoi tessellation* of the training data. Each point  $x_i$ has an associated tile bounding the region for which it is the closest input point. For all points x in the tile,  $\hat{G}(x) = g_i$ . The decision boundary is even more irregular than before.

The method of k-nearest-neighbor averaging is defined in exactly the same way for regression of a quantitative output Y, although  $k = 1$  would be an unlikely choice.

Image /page/14/Figure/1 description: This is a scatter plot showing the decision boundary of a 10-Nearest Neighbor Classifier. The plot displays two classes of data points, represented by orange circles and blue circles. The background is filled with a grid of dots, colored according to the predicted class of each region. An irregular black line separates the regions classified as orange from those classified as blue. The orange region is predominantly in the upper left, while the blue region occupies the lower and right portions of the plot. The data points are scattered across the plot, with some overlap between the classes, particularly near the decision boundary.

15-Nearest Neighbor Classifier

**FIGURE 2.2.** The same classification example in two dimensions as in Figure 2.1. The classes are coded as a binary variable (BLUE =  $0,0$ RANGE = 1) and then fit by 15-nearest-neighbor averaging as in  $(2.8)$ . The predicted class is hence chosen by majority vote amongst the 15-nearest neighbors.

In Figure 2.2 we see that far fewer training observations are misclassified than in Figure 2.1. This should not give us too much comfort, though, since in Figure 2.3 none of the training data are misclassified. A little thought suggests that for  $k$ -nearest-neighbor fits, the error on the training data should be approximately an increasing function of  $k$ , and will always be 0 for  $k = 1$ . An independent test set would give us a more satisfactory means for comparing the different methods.

It appears that  $k$ -nearest-neighbor fits have a single parameter, the number of neighbors  $k$ , compared to the p parameters in least-squares fits. Although this is the case, we will see that the *effective* number of parameters of k-nearest neighbors is  $N/k$  and is generally bigger than p, and decreases with increasing  $k$ . To get an idea of why, note that if the neighborhoods were nonoverlapping, there would be  $N/k$  neighborhoods and we would fit one parameter (a mean) in each neighborhood.

It is also clear that we cannot use sum-of-squared errors on the training set as a criterion for picking k, since we would always pick  $k = 1!$  It would seem that  $k$ -nearest-neighbor methods would be more appropriate for the mixture Scenario 2 described above, while for Gaussian data the decision boundaries of  $k$ -nearest neighbors would be unnecessarily noisy.

Image /page/15/Figure/1 description: The image displays a 1-Nearest Neighbor Classifier. It features a scatter plot with two classes of data points, represented by orange and blue circles. Black lines delineate the decision boundaries of the classifier, separating regions of the plot based on predicted class membership. The background is filled with a dotted pattern, indicating the predicted class for each point in the feature space.

FIGURE 2.3. The same classification example in two dimensions as in Figure 2.1. The classes are coded as a binary variable (BLUE = 0, ORANGE = 1), and then predicted by 1-nearest-neighbor classification.

### 2.3.3 From Least Squares to Nearest Neighbors

The linear decision boundary from least squares is very smooth, and apparently stable to fit. It does appear to rely heavily on the assumption that a linear decision boundary is appropriate. In language we will develop shortly, it has low variance and potentially high bias.

On the other hand, the k-nearest-neighbor procedures do not appear to rely on any stringent assumptions about the underlying data, and can adapt to any situation. However, any particular subregion of the decision boundary depends on a handful of input points and their particular positions, and is thus wiggly and unstable—high variance and low bias.

Each method has its own situations for which it works best; in particular linear regression is more appropriate for Scenario 1 above, while nearest neighbors are more suitable for Scenario 2. The time has come to expose the oracle! The data in fact were simulated from a model somewhere between the two, but closer to Scenario 2. First we generated 10 means  $m_k$ from a bivariate Gaussian distribution  $N((1,0)^T, I)$  and labeled this class BLUE. Similarly, 10 more were drawn from  $N((0,1)^T, I)$  and labeled class ORANGE. Then for each class we generated 100 observations as follows: for each observation, we picked an  $m_k$  at random with probability 1/10, and

Image /page/16/Figure/1 description: This is a line graph titled "2.5 Least Squares and Nearest Neighbors". The x-axis is labeled "Degrees of Freedom - N/k" and has tick marks and labels for values 2, 3, 5, 8, 12, 18, 29, 67, and 200. Above the x-axis, there is a secondary x-axis labeled "k - Number of Nearest Neighbors" with tick marks and labels for values 151, 101, 69, 45, 31, 21, 11, 7, 5, 3, and 1. The y-axis is labeled "Test Error" and has tick marks and labels for values 0.10, 0.15, 0.20, 0.25, and 0.30. There are three lines plotted on the graph: a cyan line labeled "Train" which shows a decreasing trend with some fluctuations, an orange dashed line labeled "Test" which shows a generally decreasing trend followed by an increasing trend, and a purple horizontal line labeled "Bayes" at approximately 0.21. There are also two square markers on the graph: an orange square labeled "Linear" at approximately (k=31, Test Error=0.28) and a cyan square at approximately (k=31, Test Error=0.27).

FIGURE 2.4. Misclassification curves for the simulation example used in Figures 2.1, 2.2 and 2.3. A single training sample of size 200 was used, and a test sample of size 10,000. The orange curves are test and the blue are training error for k-nearest-neighbor classification. The results for linear regression are the bigger orange and blue squares at three degrees of freedom. The purple line is the optimal Bayes error rate.

then generated a  $N(m_k, I/5)$ , thus leading to a mixture of Gaussian clusters for each class. Figure 2.4 shows the results of classifying 10,000 new observations generated from the model. We compare the results for least squares and those for  $k$ -nearest neighbors for a range of values of  $k$ .

A large subset of the most popular techniques in use today are variants of these two simple procedures. In fact 1-nearest-neighbor, the simplest of all, captures a large percentage of the market for low-dimensional problems. The following list describes some ways in which these simple procedures have been enhanced:

- Kernel methods use weights that decrease smoothly to zero with distance from the target point, rather than the effective 0/1 weights used by k-nearest neighbors.
- In high-dimensional spaces the distance kernels are modified to emphasize some variable more than others.

- 18 2. Overview of Supervised Learning
  - Local regression fits linear models by locally weighted least squares, rather than fitting constants locally.
  - Linear models fit to a basis expansion of the original inputs allow arbitrarily complex models.
  - Projection pursuit and neural network models consist of sums of nonlinearly transformed linear models.

# 2.4 Statistical Decision Theory

In this section we develop a small amount of theory that provides a framework for developing models such as those discussed informally so far. We first consider the case of a quantitative output, and place ourselves in the world of random variables and probability spaces. Let  $X \in \mathbb{R}^p$  denote a real valued random input vector, and  $Y \in \mathbb{R}$  a real valued random output variable, with joint distribution  $Pr(X, Y)$ . We seek a function  $f(X)$ for predicting  $Y$  given values of the input  $X$ . This theory requires a loss function  $L(Y, f(X))$  for penalizing errors in prediction, and by far the most common and convenient is squared error loss:  $L(Y, f(X)) = (Y - f(X))^2$ . This leads us to a criterion for choosing  $f$ ,

$$
EPE(f) = E(Y - f(X))^2
$$
 (2.9)

$$
= \int [y - f(x)]^2 \Pr(dx, dy), \qquad (2.10)
$$

the expected (squared) prediction error . By conditioning<sup>1</sup> on  $X$ , we can write EPE as

$$
EPE(f) = E_X E_{Y|X} ([Y - f(X)]^2 | X)
$$
\n(2.11)

and we see that it suffices to minimize EPE pointwise:

$$
f(x) = \operatorname{argmin}_{c} \mathbb{E}_{Y|X} ([Y - c]^2 | X = x).
$$
 (2.12)

The solution is

$$
f(x) = E(Y|X = x),
$$
\n(2.13)

the conditional expectation, also known as the regression function. Thus the best prediction of Y at any point  $X = x$  is the conditional mean, when best is measured by average squared error.

The nearest-neighbor methods attempt to directly implement this recipe using the training data. At each point  $x$ , we might ask for the average of all

<sup>&</sup>lt;sup>1</sup> Conditioning here amounts to factoring the joint density  $Pr(X, Y) = Pr(Y|X)Pr(X)$ where  $Pr(Y|X) = Pr(Y, X)/Pr(X)$ , and splitting up the bivariate integral accordingly.

those  $y_i$ s with input  $x_i = x$ . Since there is typically at most one observation at any point  $x$ , we settle for

$$
\hat{f}(x) = \text{Ave}(y_i | x_i \in N_k(x)),\tag{2.14}
$$

where "Ave" denotes average, and  $N_k(x)$  is the neighborhood containing the k points in  $\mathcal T$  closest to x. Two approximations are happening here:

- expectation is approximated by averaging over sample data;
- conditioning at a point is relaxed to conditioning on some region "close" to the target point.

For large training sample size  $N$ , the points in the neighborhood are likely to be close to  $x$ , and as  $k$  gets large the average will get more stable. In fact, under mild regularity conditions on the joint probability distribution  $Pr(X, Y)$ , one can show that as  $N, k \to \infty$  such that  $k/N \to 0$ ,  $\widehat{f}(x) \to E(Y|X=x)$ . In light of this, why look further, since it seems we have a universal approximator? We often do not have very large samples. If the linear or some more structured model is appropriate, then we can usually get a more stable estimate than  $k$ -nearest neighbors, although such knowledge has to be learned from the data as well. There are other problems though, sometimes disastrous. In Section 2.5 we see that as the dimension  $p$  gets large, so does the metric size of the  $k$ -nearest neighborhood. So settling for nearest neighborhood as a surrogate for conditioning will fail us miserably. The convergence above still holds, but the *rate* of convergence decreases as the dimension increases.

How does linear regression fit into this framework? The simplest explanation is that one assumes that the regression function  $f(x)$  is approximately linear in its arguments:

$$
f(x) \approx x^T \beta. \tag{2.15}
$$

This is a model-based approach—we specify a model for the regression function. Plugging this linear model for  $f(x)$  into EPE (2.9) and differentiating we can solve for  $\beta$  theoretically:

$$
\beta = [E(XX^T)]^{-1}E(XY). \tag{2.16}
$$

Note we have *not* conditioned on  $X$ ; rather we have used our knowledge of the functional relationship to *pool* over values of  $X$ . The least squares solution (2.6) amounts to replacing the expectation in (2.16) by averages over the training data.

So both k-nearest neighbors and least squares end up approximating conditional expectations by averages. But they differ dramatically in terms of model assumptions:

• Least squares assumes  $f(x)$  is well approximated by a globally linear function.

- 20 2. Overview of Supervised Learning
  - k-nearest neighbors assumes  $f(x)$  is well approximated by a locally constant function.

Although the latter seems more palatable, we have already seen that we may pay a price for this flexibility.

Many of the more modern techniques described in this book are model based, although far more flexible than the rigid linear model. For example, additive models assume that

$$
f(X) = \sum_{j=1}^{p} f_j(X_j).
$$
 (2.17)

This retains the additivity of the linear model, but each coordinate function  $f_i$  is arbitrary. It turns out that the optimal estimate for the additive model uses techniques such as k-nearest neighbors to approximate univariate conditional expectations simultaneously for each of the coordinate functions. Thus the problems of estimating a conditional expectation in high dimensions are swept away in this case by imposing some (often unrealistic) model assumptions, in this case additivity.

Are we happy with the criterion (2.11)? What happens if we replace the  $L_2$  loss function with the  $L_1: E|Y - f(X)|$ ? The solution in this case is the conditional median,

$$
\hat{f}(x) = \text{median}(Y|X=x),\tag{2.18}
$$

which is a different measure of location, and its estimates are more robust than those for the conditional mean.  $L_1$  criteria have discontinuities in their derivatives, which have hindered their widespread use. Other more resistant loss functions will be mentioned in later chapters, but squared error is analytically convenient and the most popular.

What do we do when the output is a categorical variable  $G$ ? The same paradigm works here, except we need a different loss function for penalizing prediction errors. An estimate  $\hat{G}$  will assume values in  $\mathcal{G}$ , the set of possible classes. Our loss function can be represented by a  $K \times K$  matrix **L**, where  $K = \text{card}(\mathcal{G})$ . L will be zero on the diagonal and nonnegative elsewhere, where  $L(k, \ell)$  is the price paid for classifying an observation belonging to class  $\mathcal{G}_k$  as  $\mathcal{G}_\ell$ . Most often we use the *zero–one* loss function, where all misclassifications are charged a single unit. The expected prediction error is

$$
EPE = E[L(G, \hat{G}(X))],
$$
\n(2.19)

where again the expectation is taken with respect to the joint distribution  $Pr(G, X)$ . Again we condition, and can write EPE as

$$
EPE = E_X \sum_{k=1}^{K} L[\mathcal{G}_k, \hat{G}(X)] Pr(\mathcal{G}_k|X)
$$
\n(2.20)

Image /page/20/Figure/1 description: The image displays a scatter plot illustrating a Bayes Optimal Classifier. The plot is divided into regions by black, curved decision boundaries. One region is colored with light orange dots and contains mostly orange circles, representing one class. The other region is colored with light blue dots and contains mostly blue circles, representing another class. The title of the plot is "Bayes Optimal Classifier".

FIGURE 2.5. The optimal Bayes decision boundary for the simulation example of Figures 2.1, 2.2 and 2.3. Since the generating density is known for each class, this boundary can be calculated exactly (Exercise 2.2).

and again it suffices to minimize EPE pointwise:

$$
\hat{G}(x) = \operatorname{argmin}_{g \in \mathcal{G}} \sum_{k=1}^{K} L(\mathcal{G}_k, g) \Pr(\mathcal{G}_k | X = x).
$$
 (2.21)

With the  $0-1$  loss function this simplifies to

$$
\hat{G}(x) = \operatorname{argmin}_{g \in \mathcal{G}} [1 - \Pr(g|X = x)] \tag{2.22}
$$

or simply

$$
\hat{G}(x) = \mathcal{G}_k \text{ if } \Pr(\mathcal{G}_k | X = x) = \max_{g \in \mathcal{G}} \Pr(g | X = x). \tag{2.23}
$$

This reasonable solution is known as the *Bayes classifier*, and says that we classify to the most probable class, using the conditional (discrete) distribution  $Pr(G|X)$ . Figure 2.5 shows the Bayes-optimal decision boundary for our simulation example. The error rate of the Bayes classifier is called the Bayes rate.

Again we see that the  $k$ -nearest neighbor classifier directly approximates this solution—a majority vote in a nearest neighborhood amounts to exactly this, except that conditional probability at a point is relaxed to conditional probability within a neighborhood of a point, and probabilities are estimated by training-sample proportions.

Suppose for a two-class problem we had taken the dummy-variable approach and coded  $G$  via a binary  $Y$ , followed by squared error loss estimation. Then  $\tilde{f}(X) = E(Y|X) = Pr(G = \mathcal{G}_1|X)$  if  $\mathcal{G}_1$  corresponded to  $Y = 1$ . Likewise for a K-class problem,  $E(Y_k|X) = Pr(G = \mathcal{G}_k|X)$ . This shows that our dummy-variable regression procedure, followed by classification to the largest fitted value, is another way of representing the Bayes classifier. Although this theory is exact, in practice problems can occur, depending on the regression model used. For example, when linear regression is used,  $f(X)$  need not be positive, and we might be suspicious about using it as an estimate of a probability. We will discuss a variety of approaches to modeling  $Pr(G|X)$  in Chapter 4.

# 2.5 Local Methods in High Dimensions

We have examined two learning techniques for prediction so far: the stable but biased linear model and the less stable but apparently less biased class of k-nearest-neighbor estimates. It would seem that with a reasonably large set of training data, we could always approximate the theoretically optimal conditional expectation by k-nearest-neighbor averaging, since we should be able to find a fairly large neighborhood of observations close to any  $x$ and average them. This approach and our intuition breaks down in high dimensions, and the phenomenon is commonly referred to as the *curse* of dimensionality (Bellman, 1961). There are many manifestations of this problem, and we will examine a few here.

Consider the nearest-neighbor procedure for inputs uniformly distributed in a p-dimensional unit hypercube, as in Figure 2.6. Suppose we send out a hypercubical neighborhood about a target point to capture a fraction  $r$  of the observations. Since this corresponds to a fraction  $r$  of the unit volume, the expected edge length will be  $e_p(r) = r^{1/p}$ . In ten dimensions  $e_{10}(0.01)$  = 0.63 and  $e_{10}(0.1) = 0.80$ , while the entire range for each input is only 1.0. So to capture 1% or 10% of the data to form a local average, we must cover 63% or 80% of the range of each input variable. Such neighborhoods are no longer "local." Reducing  $r$  dramatically does not help much either, since the fewer observations we average, the higher is the variance of our fit.

Another consequence of the sparse sampling in high dimensions is that all sample points are close to an edge of the sample. Consider  $N$  data points uniformly distributed in a p-dimensional unit ball centered at the origin. Suppose we consider a nearest-neighbor estimate at the origin. The median

Image /page/22/Figure/1 description: The image displays two plots. On the left, a wireframe diagram of a unit cube is shown with axes labeled 0 and 1. A smaller orange cube, labeled as a 'Neighborhood', is positioned at the origin (0,0,0) of the unit cube. On the right, a line graph plots 'Distance' on the y-axis against 'Fraction of Volume' on the x-axis. Three distinct curves, labeled p=1, p=2, p=3, and p=10, represent different relationships between distance and volume fraction. The x-axis ranges from 0.0 to 0.6, and the y-axis ranges from 0.0 to 1.0. A dashed vertical line is present at approximately x=0.1.

FIGURE 2.6. The curse of dimensionality is well illustrated by a subcubical neighborhood for uniform data in a unit cube. The figure on the right shows the side-length of the subcube needed to capture a fraction r of the volume of the data, for different dimensions p. In ten dimensions we need to cover 80% of the range of each coordinate to capture 10% of the data.

distance from the origin to the closest data point is given by the expression

$$
d(p, N) = \left(1 - \frac{1}{2}^{1/N}\right)^{1/p} \tag{2.24}
$$

(Exercise 2.3). A more complicated expression exists for the mean distance to the closest point. For  $N = 500, p = 10, d(p, N) \approx 0.52$ , more than halfway to the boundary. Hence most data points are closer to the boundary of the sample space than to any other data point. The reason that this presents a problem is that prediction is much more difficult near the edges of the training sample. One must extrapolate from neighboring sample points rather than interpolate between them.

Another manifestation of the curse is that the sampling density is proportional to  $N^{1/p}$ , where p is the dimension of the input space and N is the sample size. Thus, if  $N_1 = 100$  represents a dense sample for a single input problem, then  $N_{10} = 100^{10}$  is the sample size required for the same sampling density with 10 inputs. Thus in high dimensions all feasible training samples sparsely populate the input space.

Let us construct another uniform example. Suppose we have  $1000$  training examples  $x_i$  generated uniformly on  $[-1, 1]^p$ . Assume that the true relationship between  $X$  and  $Y$  is

$$
Y = f(X) = e^{-8||X||^2},
$$

without any measurement error. We use the 1-nearest-neighbor rule to predict  $y_0$  at the test-point  $x_0 = 0$ . Denote the training set by T. We can

compute the expected prediction error at  $x_0$  for our procedure, averaging over all such samples of size 1000. Since the problem is deterministic, this is the mean squared error (MSE) for estimating  $f(0)$ :

$$
MSE(x_0) = E_{\mathcal{T}}[f(x_0) - \hat{y}_0]^2
$$
  
=  $E_{\mathcal{T}}[\hat{y}_0 - E_{\mathcal{T}}(\hat{y}_0)]^2 + [E_{\mathcal{T}}(\hat{y}_0) - f(x_0)]^2$   
=  $Var_{\mathcal{T}}(\hat{y}_0) + Bias^2(\hat{y}_0).$  (2.25)

Figure 2.7 illustrates the setup. We have broken down the MSE into two components that will become familiar as we proceed: variance and squared bias. Such a decomposition is always possible and often useful, and is known as the bias–variance decomposition. Unless the nearest neighbor is at 0,  $\hat{y}_0$  will be smaller than  $f(0)$  in this example, and so the average estimate will be biased downward. The variance is due to the sampling variance of the 1-nearest neighbor. In low dimensions and with  $N = 1000$ , the nearest neighbor is very close to 0, and so both the bias and variance are small. As the dimension increases, the nearest neighbor tends to stray further from the target point, and both bias and variance are incurred. By  $p = 10$ , for more than 99% of the samples the nearest neighbor is a distance greater than 0.5 from the origin. Thus as  $p$  increases, the estimate tends to be 0 more often than not, and hence the MSE levels off at 1.0, as does the bias, and the variance starts dropping (an artifact of this example).

Although this is a highly contrived example, similar phenomena occur more generally. The complexity of functions of many variables can grow exponentially with the dimension, and if we wish to be able to estimate such functions with the same accuracy as function in low dimensions, then we need the size of our training set to grow exponentially as well. In this example, the function is a complex interaction of all  $p$  variables involved.

The dependence of the bias term on distance depends on the truth, and it need not always dominate with 1-nearest neighbor. For example, if the function always involves only a few dimensions as in Figure 2.8, then the variance can dominate instead.

Suppose, on the other hand, that we know that the relationship between  $Y$  and  $X$  is linear,

$$
Y = X^T \beta + \varepsilon,\tag{2.26}
$$

where  $\varepsilon \sim N(0, \sigma^2)$  and we fit the model by least squares to the training data. For an arbitrary test point  $x_0$ , we have  $\hat{y}_0 = x_0^T \hat{\beta}$ , which can be written as  $\hat{y}_0 = x_0^T \beta + \sum_{i=1}^N \ell_i(x_0) \varepsilon_i$ , where  $\ell_i(x_0)$  is the *i*th element of  $X(X<sup>T</sup>X)<sup>-1</sup>x<sub>0</sub>$ . Since under this model the least squares estimates are

Image /page/24/Figure/1 description: This image contains four plots related to k-nearest neighbors (k-NN) and dimensionality. The top-left plot, titled "1-NN in One Dimension", shows a probability density function (green curve) with a vertical line at x=0.2 and a blue dot indicating a point at (0.2, 0.7). The x-axis is labeled "X" and ranges from -1.0 to 1.0, with tick marks at intervals of 0.2. The y-axis is labeled "f(X)" and ranges from 0.0 to 1.0. The bottom of the plot shows rug plots indicating data points along the x-axis. The top-right plot, titled "1-NN in One vs. Two Dimensions", is a scatter plot with "X1" on the x-axis and "X2" on the y-axis, both ranging from -1.0 to 1.0. Green dots represent data points. A black dot is at the center (0,0), surrounded by a blue circle and a hatched orange rectangle indicating a region. The bottom-left plot, titled "Distance to 1-NN vs. Dimension", is a scatter plot with "Dimension" on the x-axis ranging from 2 to 10, and "Average Distance to Nearest Neighbor" on the y-axis ranging from 0.0 to 0.8. Red dots connected by a dashed red line show an increasing trend. The bottom-right plot, titled "MSE vs. Dimension", is a scatter plot with "Dimension" on the x-axis ranging from 2 to 10, and "Mse" on the y-axis ranging from 0.0 to 1.0. It displays three lines: MSE (blue circles), Variance (green dots), and Sq. Bias (red dots). The MSE and Sq. Bias increase with dimension, while Variance remains low. Below the plots, the text "FIGURE 3.7 A simulation example, demonstrating the curse of dimensional" is partially visible.

FIGURE 2.7. A simulation example, demonstrating the curse of dimensionality and its effect on MSE, bias and variance. The input features are uniformly distributed in  $[-1,1]^p$  for  $p=1,\ldots,10$  The top left panel shows the target function (no noise) in  $\mathbb{R} : f(X) = e^{-8||X||^2}$ , and demonstrates the error that 1-nearest neighbor makes in estimating  $f(0)$ . The training point is indicated by the blue tick mark. The top right panel illustrates why the radius of the 1-nearest neighborhood increases with dimension p. The lower left panel shows the average radius of the 1-nearest neighborhoods. The lower-right panel shows the MSE, squared bias and variance curves as a function of dimension p.

Image /page/25/Figure/1 description: The image contains two plots. The left plot is titled "1-NN in One Dimension" and shows a function f(X) plotted against X. The x-axis ranges from -1.0 to 1.0, and the y-axis ranges from 0 to 4. The plot displays a curve that increases exponentially. There are also rug plots along the x-axis indicating data points. A vertical line is drawn at X=0.0, and a dashed line connects a point on the curve at X=0.2 to the x-axis and y-axis. The right plot is titled "MSE vs. Dimension" and shows Mean Squared Error (MSE), Variance, and Squared Bias plotted against Dimension. The x-axis ranges from 2 to 10, and the y-axis ranges from 0.0 to 0.25. The plot shows three lines: MSE (yellow dots), Variance (blue dots), and Squared Bias (green dots). All three metrics generally increase with dimension, with MSE and Variance showing a steeper increase than Squared Bias.

FIGURE 2.8. A simulation example with the same setup as in Figure 2.7. Here the function is constant in all but one dimension:  $F(X) = \frac{1}{2}(X_1 + 1)^3$ . The variance dominates.

unbiased, we find that

$$
\begin{array}{rcl}\text{EPE}(x_0) & = & E_{y_0|x_0} E_{\tau}(y_0 - \hat{y}_0)^2 \\& = & \mathrm{Var}(y_0|x_0) + E_{\tau}[\hat{y}_0 - E_{\tau}\hat{y}_0]^2 + [E_{\tau}\hat{y}_0 - x_0^T\beta]^2 \\& = & \mathrm{Var}(y_0|x_0) + \mathrm{Var}_{\tau}(\hat{y}_0) + \mathrm{Bias}^2(\hat{y}_0) \\& = & \sigma^2 + E_{\tau}x_0^T (\mathbf{X}^T \mathbf{X})^{-1} x_0 \sigma^2 + 0^2.\end{array} \tag{2.27}
$$

Here we have incurred an additional variance  $\sigma^2$  in the prediction error, since our target is not deterministic. There is no bias, and the variance depends on  $x_0$ . If N is large and  $\mathcal T$  were selected at random, and assuming  $E(X) = 0$ , then  $X^T X \rightarrow NCov(X)$  and

$$
E_{x_0}EPE(x_0) \sim E_{x_0}x_0^T \text{Cov}(X)^{-1}x_0\sigma^2/N + \sigma^2
$$
  
= trace[ $\text{Cov}(X)^{-1}\text{Cov}(x_0)$ ] $\sigma^2/N + \sigma^2$   
=  $\sigma^2(p/N) + \sigma^2$ . (2.28)

Here we see that the expected EPE increases linearly as a function of  $p$ , with slope  $\sigma^2/N$ . If N is large and/or  $\sigma^2$  is small, this growth in variance is negligible (0 in the deterministic case). By imposing some heavy restrictions on the class of models being fitted, we have avoided the curse of dimensionality. Some of the technical details in (2.27) and (2.28) are derived in Exercise 2.5.

Figure 2.9 compares 1-nearest neighbor vs. least squares in two situations, both of which have the form  $Y = f(X) + \varepsilon$ , X uniform as before, and  $\varepsilon \sim N(0, 1)$ . The sample size is  $N = 500$ . For the orange curve,  $f(x)$ 

26

Image /page/26/Figure/1 description: The image is a line graph titled "Expected Prediction Error of TRN vs. SES". The y-axis is labeled "EPE Ratio" and ranges from 1.6 to 2.1. The x-axis is labeled "Dimension" and ranges from 2 to 10. There are two lines plotted: a yellow line representing "Linear" and a blue line representing "Cubic". The "Linear" line starts at approximately 2.0 and increases slightly to approximately 2.1 as the dimension increases from 2 to 10. The "Cubic" line starts at approximately 1.6 and increases more steeply to approximately 1.9 as the dimension increases from 2 to 10. The "Cubic" line is consistently below the "Linear" line.

Expected Prediction Error of 1NN vs. OLS

**FIGURE 2.9.** The curves show the expected prediction error (at  $x_0 = 0$ ) for 1-nearest neighbor relative to least squares for the model  $Y = f(X) + \varepsilon$ . For the orange curve,  $f(x) = x_1$ , while for the blue curve  $f(x) = \frac{1}{2}(x_1 + 1)^3$ .

is linear in the first coordinate, for the blue curve, cubic as in Figure 2.8. Shown is the relative EPE of 1-nearest neighbor to least squares, which appears to start at around 2 for the linear case. Least squares is unbiased in this case, and as discussed above the EPE is slightly above  $\sigma^2 = 1$ . The EPE for 1-nearest neighbor is always above 2, since the variance of  $\hat{f}(x_0)$  in this case is at least  $\sigma^2$ , and the ratio increases with dimension as the nearest neighbor strays from the target point. For the cubic case, least squares is biased, which moderates the ratio. Clearly we could manufacture examples where the bias of least squares would dominate the variance, and the 1-nearest neighbor would come out the winner.

By relying on rigid assumptions, the linear model has no bias at all and negligible variance, while the error in 1-nearest neighbor is substantially larger. However, if the assumptions are wrong, all bets are off and the 1-nearest neighbor may dominate. We will see that there is a whole spectrum of models between the rigid linear models and the extremely flexible 1-nearest-neighbor models, each with their own assumptions and biases, which have been proposed specifically to avoid the exponential growth in complexity of functions in high dimensions by drawing heavily on these assumptions.

Before we delve more deeply, let us elaborate a bit on the concept of statistical models and see how they fit into the prediction framework.