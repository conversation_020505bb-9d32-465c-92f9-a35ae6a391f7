{"table_of_contents": [{"title": "Bayesian PCA\n12.2.3", "heading_level": null, "page_id": 1, "polygon": [[137.25, 477.0], [258.75, 477.0], [258.75, 489.09375], [137.25, 489.09375]]}, {"title": "582 12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 3, "polygon": [[29.25, 30.75], [275.625, 30.75], [275.625, 41.6656494140625], [29.25, 41.6656494140625]]}, {"title": "12.2.4 Factor analysis", "heading_level": null, "page_id": 4, "polygon": [[144.0, 567.4658203125], [270.75, 567.4658203125], [270.75, 578.5224609375], [144.0, 578.5224609375]]}, {"title": "586 12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 7, "polygon": [[29.25, 33.0], [275.25, 33.0], [275.25, 43.576171875], [29.25, 43.576171875]]}, {"title": "12.3. <PERSON><PERSON> peA", "heading_level": null, "page_id": 7, "polygon": [[81.75, 323.25], [191.25, 323.25], [191.25, 336.251953125], [81.75, 336.251953125]]}, {"title": "588 12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 9, "polygon": [[30.515625, 30.75], [278.578125, 30.75], [278.578125, 41.868896484375], [30.515625, 41.868896484375]]}, {"title": "590\n12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 11, "polygon": [[33.0, 30.75], [279.0, 30.75], [279.0, 41.5843505859375], [33.0, 41.5843505859375]]}, {"title": "12.4. Nonlinear Latent Variable Models", "heading_level": null, "page_id": 12, "polygon": [[86.1328125, 84.0], [328.2890625, 84.0], [328.2890625, 97.39599609375], [86.1328125, 97.39599609375]]}, {"title": "12.4.1 Independent component analysis", "heading_level": null, "page_id": 12, "polygon": [[141.75, 236.25], [367.5, 236.25], [367.5, 247.96142578125], [141.75, 247.96142578125]]}, {"title": "12.4.2 Autoassociative neural networks", "heading_level": null, "page_id": 13, "polygon": [[137.935546875, 558.685546875], [360.0, 558.685546875], [360.0, 569.7421875], [137.935546875, 569.7421875]]}, {"title": "594 12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 15, "polygon": [[31.5, 28.5], [277.59375, 28.5], [277.59375, 39.0234375], [31.5, 39.0234375]]}, {"title": "12.4.3 Modelling nonlinear manifolds", "heading_level": null, "page_id": 16, "polygon": [[144.0, 165.75], [352.5, 165.75], [352.5, 177.8818359375], [144.0, 177.8818359375]]}, {"title": "Chapter 3", "heading_level": null, "page_id": 19, "polygon": [[29.25, 291.75], [72.75, 291.75], [72.75, 302.1064453125], [29.25, 302.1064453125]]}, {"title": "Exercises", "heading_level": null, "page_id": 20, "polygon": [[39.0, 221.25], [120.75, 221.62060546875], [120.75, 241.5], [39.0, 241.5]]}, {"title": "600 12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 21, "polygon": [[29.25, 29.73504638671875], [276.36328125, 29.73504638671875], [276.36328125, 41.40142822265625], [29.25, 41.40142822265625]]}, {"title": "602 12. CONTINUOUS LATENT VARIABLES", "heading_level": null, "page_id": 23, "polygon": [[29.25, 30.487060546875], [275.25, 30.487060546875], [275.25, 42.2347412109375], [29.25, 42.2347412109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 1148], ["Line", 44], ["Text", 4], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3481, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "surya", "block_counts": [["Span", 40], ["Line", 32], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 603, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "surya", "block_counts": [["Line", 66], ["Span", 32], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 792], ["Line", 39], ["Text", 5], ["Equation", 2], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1005], ["Line", 42], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 930], ["Line", 46], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["ListItem", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 734, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "surya", "block_counts": [["Span", 54], ["Line", 44], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 831], ["Line", 36], ["TextInlineMath", 5], ["Equation", 4], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2000, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "surya", "block_counts": [["Span", 40], ["Line", 20], ["Equation", 4], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 795], ["Line", 41], ["Text", 6], ["Equation", 6], ["TextInlineMath", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 40], ["Equation", 6], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6971, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "surya", "block_counts": [["Span", 50], ["Line", 29], ["TextInlineMath", 2], ["SectionHeader", 1], ["Picture", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 649, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 973], ["Line", 45], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 981], ["Line", 45], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 984], ["Line", 52], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 729], ["Line", 47], ["Figure", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1380, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 1131], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 1166], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 1174], ["Line", 48], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "surya", "block_counts": [["Line", 35], ["Span", 35], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 678, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 1123], ["Line", 40], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 1000], ["Line", 39], ["ListItem", 11], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 1128], ["Line", 43], ["ListItem", 8], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 1190], ["Line", 43], ["ListItem", 7], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_599-624"}