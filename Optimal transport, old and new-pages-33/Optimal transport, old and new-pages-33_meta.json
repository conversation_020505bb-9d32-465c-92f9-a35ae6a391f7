{"table_of_contents": [{"title": "Isoperimetric-type inequalities", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.5], [350.25, 97.5], [350.25, 111.568359375], [133.5, 111.568359375]]}, {"title": "Logarithmic Sobolev inequalities", "heading_level": null, "page_id": 1, "polygon": [[133.5, 48.315673828125], [327.515625, 48.315673828125], [327.515625, 59.11962890625], [133.5, 59.11962890625]]}, {"title": "Sobolev inequalities", "heading_level": null, "page_id": 3, "polygon": [[133.5, 495.0], [251.25, 495.0], [251.25, 506.21484375], [133.5, 506.21484375]]}, {"title": "566 21 Isoperimetric-type inequalities", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.25], [303.75, 26.25], [303.75, 35.72314453125], [133.5, 35.72314453125]]}, {"title": "570 21 Isoperimetric-type inequalities", "heading_level": null, "page_id": 9, "polygon": [[133.5, 26.24853515625], [303.75, 26.24853515625], [303.75, 35.4814453125], [133.5, 35.4814453125]]}, {"title": "Isoperimetric inequalities", "heading_level": null, "page_id": 10, "polygon": [[133.5, 48.0], [283.7373046875, 48.0], [283.7373046875, 58.82958984375], [133.5, 58.82958984375]]}, {"title": "Poincaré inequalities", "heading_level": null, "page_id": 11, "polygon": [[133.5, 47.51806640625], [257.25, 47.51806640625], [257.25, 59.11962890625], [133.5, 59.11962890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 23], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 62], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 41], ["TextInlineMath", 6], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 51], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 84], ["Equation", 5], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2140, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 501], ["Line", 94], ["TextInlineMath", 5], ["Equation", 4], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3481, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 67], ["TextInlineMath", 5], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["Line", 118], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4610, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 556], ["Line", 114], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 960, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 613], ["Line", 80], ["TextInlineMath", 5], ["Equation", 4], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 49], ["Text", 6], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 57], ["Text", 5], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 448], ["Line", 59], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-33"}