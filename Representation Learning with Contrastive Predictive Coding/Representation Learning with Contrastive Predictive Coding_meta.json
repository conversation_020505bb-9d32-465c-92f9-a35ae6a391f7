{"table_of_contents": [{"title": "Representation Learning with\nContrastive Predictive Coding", "heading_level": null, "page_id": 0, "polygon": [[194.25, 99.75], [417.76171875, 99.75], [417.76171875, 136.2216796875], [194.25, 136.2216796875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 243.0], [329.009765625, 243.0], [329.009765625, 254.07421875], [282.75, 254.07421875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 433.125], [191.84765625, 433.125], [191.84765625, 443.953125], [107.25, 443.953125]]}, {"title": "2 Contrastive Predicting Coding", "heading_level": null, "page_id": 1, "polygon": [[106.90576171875, 453.0], [282.09375, 453.0], [282.09375, 464.0625], [106.90576171875, 464.0625]]}, {"title": "2.1 Motivation and Intuitions", "heading_level": null, "page_id": 1, "polygon": [[106.5, 522.75], [240.75, 522.75], [240.75, 532.8984375], [106.5, 532.8984375]]}, {"title": "2.2 Contrastive Predictive Coding", "heading_level": null, "page_id": 2, "polygon": [[106.5, 183.0], [259.5, 183.0], [259.5, 193.0693359375], [106.5, 193.0693359375]]}, {"title": "2.3 InfoNCE Loss and Mutual Information Estimation", "heading_level": null, "page_id": 2, "polygon": [[106.8310546875, 618.0], [347.25, 618.0], [347.25, 627.64453125], [106.8310546875, 627.64453125]]}, {"title": "2.4 Related Work", "heading_level": null, "page_id": 3, "polygon": [[107.25, 345.0], [190.65234375, 345.0], [190.65234375, 355.201171875], [107.25, 355.201171875]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 3, "polygon": [[107.25, 600.0], [192.2958984375, 600.0], [192.2958984375, 610.5], [107.25, 610.5]]}, {"title": "3.1 Audio", "heading_level": null, "page_id": 3, "polygon": [[106.5, 681.0], [156.75, 681.0], [156.75, 691.453125], [106.5, 691.453125]]}, {"title": "3.2 Vision", "heading_level": null, "page_id": 5, "polygon": [[106.5, 636.5390625], [158.25, 636.5390625], [158.25, 645.8203125], [106.5, 645.8203125]]}, {"title": "3.3 Natural Language", "heading_level": null, "page_id": 6, "polygon": [[106.5, 597.48046875], [210.0, 597.48046875], [210.0, 608.30859375], [106.5, 608.30859375]]}, {"title": "3.4 Reinforcement Learning", "heading_level": null, "page_id": 8, "polygon": [[106.5, 209.21484375], [236.2236328125, 209.21484375], [236.2236328125, 220.04296875], [106.5, 220.04296875]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[107.25, 469.86328125], [183.75, 469.86328125], [183.75, 481.46484375], [107.25, 481.46484375]]}, {"title": "5 Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[106.5, 607.921875], [225.75, 607.921875], [225.75, 619.5234375], [106.5, 619.5234375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[107.25, 669.75], [165.0, 669.75], [165.0, 681.01171875], [107.25, 681.01171875]]}, {"title": "A Appendix", "heading_level": null, "page_id": 12, "polygon": [[106.5, 72.0], [180.0, 72.0], [180.0, 83.8212890625], [106.5, 83.8212890625]]}, {"title": "A.1 Estimating the Mutual Information with InfoNCE", "heading_level": null, "page_id": 12, "polygon": [[107.20458984375, 96.75], [347.25, 96.75], [347.25, 106.734375], [107.20458984375, 106.734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 51], ["Text", 8], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3987, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 48], ["Text", 5], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 783, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 63], ["Text", 5], ["TextInlineMath", 5], ["Equation", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 419], ["Line", 62], ["Text", 7], ["TextInlineMath", 3], ["SectionHeader", 3], ["Equation", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["TableCell", 79], ["Line", 75], ["Caption", 4], ["Reference", 4], ["Text", 3], ["Figure", 2], ["Table", 2], ["Footnote", 2], ["FigureGroup", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 7749, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 49], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 739, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 33], ["Text", 6], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 618, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["TableCell", 130], ["Line", 57], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["Text", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 9384, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 44], ["Text", 7], ["SectionHeader", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 54], ["ListItem", 21], ["Reference", 21], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 53], ["ListItem", 20], ["Reference", 20], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 36], ["ListItem", 12], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 760], ["Line", 142], ["Equation", 10], ["TextInlineMath", 3], ["SectionHeader", 2], ["Text", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Representation Learning with Contrastive Predictive Coding"}