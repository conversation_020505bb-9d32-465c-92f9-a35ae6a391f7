{"table_of_contents": [{"title": "23 Monte Carlo inference", "heading_level": null, "page_id": 0, "polygon": [[52.3828125, 95.6337890625], [294.0, 95.6337890625], [292.78125, 152.25], [52.3828125, 143.0947265625]]}, {"title": "23.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[95.25, 207.0], [195.75, 207.0], [195.75, 218.00390625], [95.25, 218.00390625]]}, {"title": "23.2 Sampling from standard distributions", "heading_level": null, "page_id": 0, "polygon": [[93.0, 498.0], [324.75, 498.0], [324.75, 509.09765625], [93.0, 509.09765625]]}, {"title": "23.2.1 Using the cdf", "heading_level": null, "page_id": 0, "polygon": [[89.15625, 559.5], [195.46875, 559.5], [195.46875, 569.84765625], [89.15625, 569.84765625]]}, {"title": "23.2.2 Sampling from a Gaussian (Box-Muller method)", "heading_level": null, "page_id": 2, "polygon": [[86.625, 61.5], [354.75, 60.75], [354.75, 71.5869140625], [86.625, 71.5869140625]]}, {"title": "23.3 Rejection sampling", "heading_level": null, "page_id": 2, "polygon": [[93.0, 344.25], [230.25, 344.25], [230.25, 355.32421875], [93.0, 355.32421875]]}, {"title": "23.3.1 Basic idea", "heading_level": null, "page_id": 2, "polygon": [[89.25, 405.75], [179.71875, 405.75], [179.71875, 415.7578125], [89.25, 415.7578125]]}, {"title": "23.3.2 Example", "heading_level": null, "page_id": 3, "polygon": [[86.2734375, 455.25], [171.75, 455.25], [171.75, 465.1171875], [86.2734375, 465.1171875]]}, {"title": "23.3.3 Application to Bayesian statistics", "heading_level": null, "page_id": 4, "polygon": [[87.75, 392.25], [286.5, 392.25], [286.5, 402.78515625], [87.75, 402.78515625]]}, {"title": "23.3.4 Adaptive rejection sampling", "heading_level": null, "page_id": 4, "polygon": [[87.75, 559.5], [263.25, 559.5], [263.25, 569.84765625], [87.75, 569.84765625]]}, {"title": "23.3.5 Rejection sampling in high dimensions", "heading_level": null, "page_id": 5, "polygon": [[86.6953125, 224.25], [315.0, 224.25], [315.0, 234.45703125], [86.6953125, 234.45703125]]}, {"title": "23.4 Importance sampling", "heading_level": null, "page_id": 5, "polygon": [[93.0, 391.5], [240.75, 391.5], [240.75, 402.78515625], [93.0, 402.78515625]]}, {"title": "23.4.1 Basic idea", "heading_level": null, "page_id": 5, "polygon": [[89.25, 482.25], [179.15625, 482.25], [179.15625, 492.9609375], [89.25, 492.9609375]]}, {"title": "23.4.2 Handling unnormalized distributions", "heading_level": null, "page_id": 6, "polygon": [[87.75, 379.5], [306.0, 379.5], [306.0, 390.12890625], [87.75, 390.12890625]]}, {"title": "23.4.3 Importance sampling for a DGM: likelihood weighting", "heading_level": null, "page_id": 7, "polygon": [[86.90625, 161.25], [384.0, 161.25], [384.0, 171.80859375], [86.90625, 171.80859375]]}, {"title": "23.4.4 Sampling importance resampling (SIR)", "heading_level": null, "page_id": 7, "polygon": [[87.6796875, 518.25], [311.25, 518.25], [311.25, 528.3984375], [87.6796875, 528.3984375]]}, {"title": "23.5 Particle filtering", "heading_level": null, "page_id": 8, "polygon": [[93.0, 475.5], [213.75, 475.5], [213.75, 486.31640625], [93.0, 486.31640625]]}, {"title": "23.5.1 Sequential importance sampling", "heading_level": null, "page_id": 9, "polygon": [[89.015625, 61.5], [283.640625, 61.5], [283.640625, 71.78466796875], [89.015625, 71.78466796875]]}, {"title": "23.5.2 The degeneracy problem", "heading_level": null, "page_id": 10, "polygon": [[87.75, 183.0], [246.75, 183.0], [246.75, 193.166015625], [87.75, 193.166015625]]}, {"title": "23.5.3 The resampling step", "heading_level": null, "page_id": 10, "polygon": [[87.46875, 426.0], [225.75, 426.0], [225.75, 436.95703125], [87.46875, 436.95703125]]}, {"title": "23.5.4 The proposal distribution", "heading_level": null, "page_id": 12, "polygon": [[87.5390625, 266.25], [250.5, 266.25], [250.5, 276.5390625], [87.5390625, 276.5390625]]}, {"title": "23.5.5 Application: robot localization", "heading_level": null, "page_id": 13, "polygon": [[86.2734375, 220.5], [275.0625, 220.5], [275.0625, 230.66015625], [86.2734375, 230.66015625]]}, {"title": "23.5.6 Application: visual object tracking", "heading_level": null, "page_id": 13, "polygon": [[86.25, 516.75], [294.0, 516.75], [294.0, 527.44921875], [86.25, 527.44921875]]}, {"title": "23.5.7 Application: time series forecasting", "heading_level": null, "page_id": 16, "polygon": [[87.328125, 61.5], [298.6875, 61.5], [298.6875, 71.86376953125], [87.328125, 71.86376953125]]}, {"title": "23.6 Rao-Blackwellised particle filtering (RBPF)", "heading_level": null, "page_id": 16, "polygon": [[93.0, 168.75], [346.5, 168.75], [346.5, 179.560546875], [93.0, 179.560546875]]}, {"title": "23.6.1 RBPF for switching LG-SSMs", "heading_level": null, "page_id": 16, "polygon": [[88.2421875, 324.0], [264.0, 324.0], [264.0, 334.125], [88.2421875, 334.125]]}, {"title": "23.6.2 Application: tracking a maneuvering target", "heading_level": null, "page_id": 17, "polygon": [[86.25, 523.5], [334.5, 523.5], [334.5, 534.09375], [86.25, 534.09375]]}, {"title": "Algorithm 23.3: One step of look-ahead RBPF for SLDS using optimal proposal", "heading_level": null, "page_id": 18, "polygon": [[133.875, 64.5], [453.75, 64.5], [453.75, 74.5927734375], [133.875, 74.5927734375]]}, {"title": "23.6.3 Application: Fast SLAM", "heading_level": null, "page_id": 19, "polygon": [[86.25, 450.0], [240.0, 450.0], [240.0, 460.6875], [86.25, 460.6875]]}, {"title": "Exercises", "heading_level": null, "page_id": 20, "polygon": [[129.515625, 329.25], [178.5, 329.25], [178.5, 339.8203125], [129.515625, 339.8203125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 29], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 30], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 675, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 51], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["Line", 130], ["Text", 3], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2173, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 426], ["Line", 54], ["Equation", 4], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 882, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 37], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 89], ["Equation", 7], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 56], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 57], ["Text", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 640], ["Line", 74], ["Equation", 10], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 48], ["Text", 6], ["Equation", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 403], ["Line", 35], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 59], ["Text", 8], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 42], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 834, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 46], ["Line", 21], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 787, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 65], ["Text", 6], ["Equation", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 566], ["Line", 50], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 68], ["TableCell", 66], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["TextInlineMath", 2], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14822, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 77], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 998, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-27"}