The goal of the present chapter is to translate displacement convexity inequalities of the form "the graph of a convex function lies below the chord" into inequalities of the form "the graph of a convex function lies above the tangent"  $-$  just as in statements (ii) and (iii) of Proposition 16.2. This corresponds to the limit  $t \to 0$  in the convexity inequality.

The main results in this chapter are the HWI inequality (Corollary 20.13); and its generalized version, the distorted HWI inequality (Theorem 20.10).

### Time-derivative of the energy

As a preliminary step, a useful lower bound will now be given for a derivative of  $U_{\nu}(\mu_t)$ , where  $(\mu_t)_{0 \leq t \leq 1}$  is a Wasserstein geodesic and  $U_{\nu}$ an energy functional with a reference measure  $\nu$ . This computation hardly needs any regularity on the space, and for later use I shall state it in a more general setting than Riemannian manifolds.

In the next theorem, I consider a locally compact, complete geodesic space  $\mathcal X$  equipped with a distance d and a locally finite measure  $\nu$ . Then  $U : [0, +\infty) \to \mathbb{R}$  is a continuous convex function, twice differentiable on  $(0, +\infty)$ . To U is associated the functional

$$
U_{\nu}(\mu) = \int_{\mathcal{X}} U(\rho) d\nu \qquad \mu = \rho \nu.
$$

The statement below will involve norms of gradients. Even though there is no natural notion for the gradient  $\nabla f$  of a function f defined

on a nonsmooth length space, there are still natural definitions for the *norm* of the gradient,  $|\nabla f|$ . The most common one is

$$
|\nabla f|(x) := \limsup_{y \to x} \frac{|f(y) - f(x)|}{d(x, y)}.
$$
 (20.1)

Rigorously speaking, this formula makes sense only if  $x$  is not isolated, which will always be the case in the sequel. A slightly finer notion is the following:

$$
|\nabla^{-} f|(x) := \limsup_{y \to x} \frac{[f(y) - f(x)]}{d(x, y)},
$$
\n(20.2)

where  $a_$  = max( $-a$ , 0) stands for the negative part of a (which is a nonnegative number!). It is obvious that  $|\nabla^- f| \leq |\nabla f|$ , and both notions coincide with the usual one if  $f$  is differentiable. Note that  $|\nabla^-f|(x)$  is automatically 0 if x is a local minimum of f.

Theorem 20.1 (Differentiating an energy along optimal trans**port).** Let  $(\mathcal{X}, d, \nu)$  and U be as above, and let  $(\mu_t)_{0 \leq t \leq 1}$  be a geodesic in  $P_2(\mathcal{X})$ , such that each  $\mu_t$  is absolutely continuous with respect to  $\nu$ , with density  $\rho_t$ , and  $U(\rho_t)$  is v-integrable for all t. Further assume that  $\rho_0$  is Lipschitz continuous,  $U(\rho_0)$  and  $\rho_0 U'(\rho_0)$  are v-integrable, and U' is Lipschitz continuous on  $\rho_0(\mathcal{X})$ . Then

$$
\liminf_{t \downarrow 0} \left[ \frac{U_{\nu}(\mu_t) - U_{\nu}(\mu_0)}{t} \right] \ge - \int_{\mathcal{X}} U''(\rho_0(x_0)) |\nabla^{-} \rho_0|(x_0) d(x_0, x_1) \pi(dx_0 dx_1), \quad (20.3)
$$

where  $\pi$  is an optimal coupling of  $(\mu_0, \mu_1)$  associated with the geodesic path  $(\mu_t)_{0 \leq t \leq 1}$ .

**Remark 20.2.** The technical assumption on the negative part of  $U(\rho_t)$ being integrable is a standard way to make sure that  $U_{\nu}(\mu_t)$  is welldefined, with values in  $\mathbb{R} \cup \{+\infty\}$ . As for the assumption about U' being Lipschitz on  $\rho_0(\mathcal{X})$ , it means in practice that either U is twice (right-)differentiable at the origin, or  $\rho_0$  is bounded away from 0.

Remark 20.3. Here is a more probabilistic reformulation of (20.3) (which will also make more explicit the link between  $\pi$  and  $\mu_t$ ): Let  $\gamma$  be a random geodesic such that  $\mu_t = \text{law}(\gamma_t)$ , then

$$
\liminf_{t\downarrow 0}\left[\frac{U_{\nu}(\mu_t)-U_{\nu}(\mu_0)}{t}\right] \geq -\mathbb{E}\left[U''(\rho_0(\gamma_0))\,|\nabla^-\rho_0|(\gamma_0)\,d(\gamma_0,\gamma_1)\right].
$$

Proof of Theorem 20.1. By convexity,

$$
U(\rho_t) - U(\rho_0) \ge U'(\rho_0) \ (\rho_t - \rho_0), \tag{20.4}
$$

where  $U'(0)$  is the right-derivative of U at 0.

On the one hand,  $U(\rho_0)$  and  $U(\rho_t)$ <sub>−</sub> are  $\nu$ -integrable by assumption, so the integral in the left-hand side of (20.4) makes sense in  $\mathbb{R} \cup \{+\infty\}$ (and the integral of each term is well-defined). On the other hand,  $\rho_0 U'(\rho_0)$  is integrable by assumption, while  $\rho_t U'(\rho_0)$  is bounded above by  $(\max U')\rho_t$ , which is integrable; so the integral of the right-hand side makes sense in  $\mathbb{R} \cup \{-\infty\}$ . All in all, inequality (20.4) can be integrated into

$$
U_{\nu}(\mu_t) - U_{\nu}(\mu_0) \ge \int U'(\rho_0) \rho_t \, d\nu - \int U'(\rho_0) \rho_0 \, d\nu
$$
  
= 
$$
\int U'(\rho_0) \, d\mu_t - \int U'(\rho_0) \, d\mu_0.
$$

Now let  $\gamma$  be a random geodesic, such that  $\mu_t = \text{law}(\gamma_t)$ . Then the above inequality can be rewritten

$$
U_{\nu}(\mu_t) - U_{\nu}(\mu_0) \geq \mathbb{E} U'(\rho_0(\gamma_t)) - \mathbb{E} U'(\rho_0(\gamma_0))
$$
  
= 
$$
\mathbb{E} \left[ U'(\rho_0(\gamma_t)) - U'(\rho_0(\gamma_0)) \right].
$$

Since  $U'$  is nondecreasing,

$$
U'(\rho_0(\gamma_t)) - U'(\rho_0(\gamma_0)) \geq [U'(\rho_0(\gamma_t)) - U'(\rho_0(\gamma_0))] 1_{\rho_0(\gamma_0) > \rho_0(\gamma_t)}.
$$

Multiplying and dividing by  $\rho_0(\gamma_t) - \rho_0(\gamma_0)$ , and then by  $d(\gamma_0, \gamma_t)$ , one arrives at

$$
\begin{aligned} \left[U_{\nu}(\mu_t) - U_{\nu}(\mu_0)\right] &\geq\\ \mathbb{E}\left(\frac{U'(\rho_0(\gamma_t)) - U'(\rho_0(\gamma_0))}{\rho_0(\gamma_t) - \rho_0(\gamma_0)}\right) \left(\frac{\rho_0(\gamma_t) - \rho_0(\gamma_0)}{d(\gamma_0, \gamma_t)} 1_{\rho_0(\gamma_0) > \rho_0(\gamma_t)}\right) d(\gamma_0, \gamma_t). \end{aligned}
$$

After division by t and use of the identity  $d(\gamma_0, \gamma_t) = t d(\gamma_0, \gamma_1)$ , one obtains in the end

$$
\frac{1}{t} \left[ U_{\nu}(\mu_t) - U_{\nu}(\mu_0) \right] \ge \mathbb{E} \left( \frac{U'(\rho_0(\gamma_t)) - U'(\rho_0(\gamma_0))}{\rho_0(\gamma_t) - \rho_0(\gamma_0)} \right) \left( \frac{\rho_0(\gamma_t) - \rho_0(\gamma_0)}{d(\gamma_0, \gamma_t)} 1_{\rho_0(\gamma_0) > \rho_0(\gamma_t)} \right) d(\gamma_0, \gamma_1).
$$
(20.5)

It remains to pass to the limit in the right-hand side of (20.5) as  $t \to 0$ . Since  $\rho_0$  is continuous, for almost each geodesic  $\gamma$  one has  $\rho_0(\gamma_t) \to \rho_0(\gamma_0) > 0$  as  $t \to 0$ , and in particular,

$$
\frac{U'(\rho_0(\gamma_t))-U'(\rho_0(\gamma_0))}{\rho_0(\gamma_t)-\rho_0(\gamma_0)}\xrightarrow[t\to 0]{} U''(\rho_0(\gamma_0)),
$$

Similarly,

$$
\liminf_{t\to 0}\left(\frac{\rho_0(\gamma_t)-\rho_0(\gamma_0)}{d(\gamma_0,\gamma_t)}\,1_{\rho_0(\gamma_0)>\rho_0(\gamma_t)}\right)\geq -|\nabla^-\rho_0|(\gamma_0).
$$

So, if  $v_t(\gamma)$  stands for the integrand in the right-hand side of (20.5), one has

$$
\liminf_{t \to 0} v_t(\gamma) \ge -U''(\rho_0(\gamma_0)) |\nabla^- \rho_0|(\gamma_0) d(\gamma_0, \gamma_1).
$$

On the other hand,  $\rho_0$  is Lipschitz by assumption, and also U' is Lipschitz on the range of  $\rho_0$ . So  $|v_t(\gamma)| \leq C d(\gamma_0, \gamma_1)$ , where C is the product of the Lipschitz constants of  $\rho_0$  and U'. This uniform domination makes it possible to apply Fatou's lemma, in the form  $\liminf_{t\to 0} \mathbb{E} v_t(\gamma) \geq \mathbb{E} \liminf v_t(\gamma)$ . Thus

$$
\liminf_{t \to 0} \frac{1}{t} \Big[ U_{\nu}(\mu_t) - U_{\nu}(\mu_0) \Big] \geq - \mathbb{E} U''(\rho_0(\gamma_0)) |\nabla^{-} \rho_0|(\gamma_0) d(\gamma_0, \gamma_1),
$$

as desired. ⊓⊔

**Remark 20.4.** This theorem does not assume smoothness of  $\mathcal{X}$ , and does not either assume structural restrictions on the function U. On the other hand, when  $\mathcal X$  is a Riemannian manifold of dimension  $n$ ,  $\nu = e^{-V}$  vol, and  $\mu$  is compactly supported, then there is a more precise result:

$$
\lim_{t \to 0} \frac{[U_{\nu}(\mu_t) - U_{\nu}(\mu_0)]}{t} = -\int p(\rho_0)(L\psi) d\nu,
$$
\n(20.6)

where  $\psi$  is such that  $T = \exp(\nabla \psi)$  is the unique optimal transport from  $\mu_0$  to  $\mu_1$ , and  $L\psi = \Delta \psi - \nabla V \cdot \nabla \psi$  (defined almost everywhere). It is not clear a priori how this compares with the result of Theorem 20.1, but then, under slightly more stringent regularity assumptions, one can justify the integration by parts formula

$$
-\int p(\rho_0) L\psi \,d\nu \ge \int \rho_0 U''(\rho_0) \nabla \rho_0 \cdot \nabla \psi \,d\nu \tag{20.7}
$$

(note indeed that  $p'(r) = r U''(r)$ ). Since  $\pi = (\rho_0 \nu) \otimes \delta_{x_1 = T(x_0)}$  with  $T = \exp \nabla \psi$ , the right-hand side can be rewritten

$$
\int U''(\rho_0)\nabla\rho_0\cdot\nabla\psi\,d\pi.
$$

As  $|\nabla \psi(x_0)| = d(x_0, x_1)$ , this integral is obviously an upper bound for the expression in (20.3). In the present chapter, the more precise result (20.6) will not be useful, but later in Chapter 23 we shall have to go through it (see the proof of Theorem 23.14). More comments are in the bibliographical notes.

**Exercise 20.5.** Use Otto's calculus to guess that  $(d/dt)U_{\nu}(\mu_t)$  should coincide with the right-hand side of (20.7).

### HWI inequalities

Recall from Chapters 16 and 17 that  $CD(K, N)$  bounds imply convexity properties of certain functionals  $U_{\nu}$  along displacement interpolation. For instance, if a Riemannian manifold  $M$ , equipped with a reference measure  $\nu$ , satisfies CD(0,  $\infty$ ), then by Theorem 17.15 the Boltzmann  $H$  functional is displacement convex.

If  $(\mu_t)_{0 \le t \le 1}$  is a geodesic in  $P_2^{\text{ac}}(M)$ , for  $t \in (0,1]$  the convexity inequality

$$
H_{\nu}(\mu_t) \le (1-t) H_{\nu}(\mu_0) + t H_{\nu}(\mu_1)
$$

may be rewritten as

$$
\frac{H_{\nu}(\mu_t) - H_{\nu}(\mu_0)}{t} \leq H_{\nu}(\mu_1) - H_{\nu}(\mu_0).
$$

Under suitable assumptions we may then apply Theorem 20.1 to pass to the limit as  $t \to 0$ , and get

$$
-\int \frac{|\nabla \rho_0(x_0)|}{\rho_0(x_0)} d(x_0, x_1) \,\pi(dx_0\,dx_1) \leq H_\nu(\mu_1) - H_\nu(\mu_0).
$$

This implies, by Cauchy–Schwarz inequality,

$$
H_{\nu}(\mu_0) - H_{\nu}(\mu_1) \le \sqrt{\int d(x_0, x_1)^2 \pi(dx_0 dx_1)} \sqrt{\int \frac{|\nabla \rho_0(x_0)|}{\rho_0(x_0)^2} \pi(dx_0 dx_1)}
$$
  
$$
= W_2(\mu_0, \mu_1) \sqrt{\int \frac{|\nabla \rho_0|^2}{\rho_0} d\nu},
$$
  $(20.8)$ 

where I have used the fact that the first marginal of  $\pi$  is  $\mu_0 = \rho_0 \nu$ .

Inequality (20.8) is the HWI inequality: It is expressed in terms of

- the *H*-functional  $H_{\nu}(\mu) = \int \rho \log \rho \, d\nu$  (as usual  $\rho = d\mu/d\nu$ );
- the Wasserstein distance of order 2,  $W_2$ ;
- the Fisher information I, defined by  $I_{\nu}(\mu) = \int \frac{|\nabla \rho|^2}{\rho}$  $\frac{\rho_{\perp}}{\rho}$  dv.

The present section is devoted to establishing such inequalities.

For technical reasons (such as the treatment of small values of  $\rho_0$ in noncompact manifolds, or finite-dimensional generalizations) it will be convenient to recast this discussion in the more general setting of distorted HWI inequalities, which involve distortion coefficients. Let  $\beta_t = \beta_t^{(K,N)}$  be the reference distortion coefficients defined in (14.61). Note that  $\beta_1(x_0, x_1) = 1$ ,  $\beta'_0(x_0, x_1) = 0$ , where the prime stands for partial derivation with respect to  $t$ . For brevity I shall write

$$
\beta(x_0, x_1) = \beta_0(x_0, x_1); \qquad \beta'(x_0, x_1) = \beta'_1(x_0, x_1).
$$

By explicit computation,

$$
\beta(x_0, x_1) = \begin{cases} \left(\frac{\alpha}{\sin \alpha}\right)^{N-1} > 1 & \text{if } K > 0\\ 1 & \text{if } K = 0\\ \left(\frac{\alpha}{\sinh \alpha}\right)^{N-1} < 1 & \text{if } K < 0, \end{cases} (20.9)
$$

$$
\beta'(x_0, x_1) = \begin{cases} -(N-1)\left(1 - \frac{\alpha}{\tan \alpha}\right) < 0 & \text{if } K > 0 \\ 0 & \text{if } K = 0 \\ (N-1)\left(\frac{\alpha}{\tanh \alpha} - 1\right) > 0 & \text{if } K < 0, \end{cases} \tag{20.10}
$$

where

$$
\alpha = \sqrt{\frac{|K|}{N-1}} d(x_0, x_1).
$$

Moreover, a standard Taylor expansion shows that, as  $\alpha \to 0$  while K is fixed (which means that either  $d(x_0, x_1) \to 0$  or  $N \to \infty$ ), then

$$
\beta \simeq 1 - \frac{K}{6} d(x_0, x_1)^2, \qquad \beta' \simeq -\frac{K}{3} d(x_0, x_1)^2,
$$

whatever the sign of K.

The next definition is a generalization of the classical notion of Fisher information:

Definition 20.6 (Generalized Fisher information). Let U be a continuous convex function  $\mathbb{R}_+ \to \mathbb{R}$ , twice continuously differentiable on  $(0, +\infty)$ . Let M be a Riemannian manifold, equipped with a Borel reference measure  $\nu$ . Let  $\mu \in P^{\rm ac}(M)$  be a probability measure on M, whose density  $\rho$  is locally Lipschitz. Define

$$
I_{U,\nu}(\mu) = \int \rho U''(\rho)^2 |\nabla \rho|^2 d\nu = \int \frac{|\nabla p(\rho)|^2}{\rho} d\nu = \int \rho |\nabla U'(\rho)|^2 d\nu,
$$
\n(20.11)

where  $p(r) = r U'(r) - U(r)$ .

Particular Case 20.7 (Fisher information). When  $U(r) = r \log r$ ,  $(20.11)$  becomes

$$
I_{\nu}(\mu) = \int \frac{|\nabla \rho|^2}{\rho} d\nu.
$$

Remark 20.8. The identity in (20.11) comes from the chain-rule:

$$
\nabla p(\rho) = p'(\rho) \nabla \rho = \rho U''(\rho) \nabla \rho = \rho \nabla U'(\rho).
$$

(Strictly speaking this is true only if  $\rho > 0$ , but the integral in (20.11) may be restricted to the set  $\{\rho > 0\}$ .) Also, in Definition 20.6 one can replace  $|\nabla \rho|$  by  $|\nabla^- \rho|$  and  $|\nabla p(\rho)|$  by  $|\nabla^- p(\rho)|$  since a locally Lipschitz function is differentiable almost everywhere.

**Remark 20.9.** If  $p(\rho) \in L^1_{loc}(M)$ , then the convexity of  $(p, r) \to |p|^2/r$ on  $\mathbb{R}^n \times \mathbb{R}_+$  makes it possible to define  $\int |\nabla p(\rho)|^2 / \rho d\nu$  in  $[0, +\infty]$  even if  $\rho$  is not locally Lipschitz. In particular,  $I_{\nu}(\mu)$  makes sense in  $[0, +\infty]$  for all probability measures  $\mu$  (with the understanding that  $I_{\nu}(\mu) = +\infty$ if  $\mu$  is singular). I shall not develop this remark, and in the sequel shall only consider densities which are locally (and even globally) Lipschitz. Theorem 20.10 (Distorted HWI inequality). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvature-dimension bound  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $N \in (1, \infty]$ . Let  $U \in \mathcal{DC}_N$  and let  $p(r) = r U'(r) - U(r)$ . Let  $\mu_0 = \rho_0 \nu$  and  $\mu_1 = \rho_1 \nu$  be two absolutely continuous probability measures such that:

- (a)  $\mu_0, \mu_1 \in P_p^{\text{ac}}(M)$ , where  $p \in [2, +\infty) \cup \{c\}$  satisfies (17.30);
- (b)  $\rho_0$  is Lipschitz.

If  $N = \infty$ , further assume  $\rho_0 \log_+ \rho_0$  and  $\rho_1 \log_+ \rho_1$  belong to  $L^1(\nu)$ . If  $K > 0$ , further assume  $\rho_0 U'(\rho_0) \in L^1(\nu)$ . If  $K > 0$  and  $N = \infty$ , further assume  $p(\rho_0)^2/\rho_0 \in L^1(\nu)$ . Then

$$
\int_{M} U(\rho_{0}) d\nu \leq \int_{M \times M} U\left(\frac{\rho_{1}(x_{1})}{\beta(x_{0}, x_{1})}\right) \beta(x_{0}, x_{1}) \pi(dx_{0}|x_{1}) \nu(dx_{1}) \\ + \int_{M \times M} p(\rho_{0}(x_{0})) \beta'(x_{0}, x_{1}) \pi(dx_{1}|x_{0}) \nu(dx_{0}) \\ + \int_{M \times M} U''(\rho_{0}(x_{0})) |\nabla \rho_{0}(x_{0})| d(x_{0}, x_{1}) \pi(dx_{0} dx_{1}), \quad (20.12)
$$

where  $\pi$  is the unique optimal coupling of  $(\mu_0, \mu_1)$  and the coefficients β, β' are defined in  $(20.9)$ - $(20.10)$ .

In particular,

(i) If 
$$
K = 0
$$
 and  $U_{\nu}(\mu_1) < +\infty$ , then  
\n
$$
U_{\nu}(\mu_0) - U_{\nu}(\mu_1) \le \int U''(\rho_0(x_0)) |\nabla \rho_0(x_0)| d(x_0, x_1) \pi(dx_0 dx_1)
$$
\n
$$
\le W_2(\mu_0, \mu_1) \sqrt{I_{U,\nu}(\mu_0)}.
$$
\n(20.13)

(ii) If 
$$
N = \infty
$$
 and  $U_{\nu}(\mu_1) < +\infty$ , then

$$
U_{\nu}(\mu_0) - U_{\nu}(\mu_1)
$$

$$
\leq \int U''(\rho_0(x_0)) |\nabla \rho_0(x_0)| d(x_0, x_1) \pi(dx_0 dx_1) - K_{\infty, U} \frac{W_2(\mu_0, \mu_1)^2}{2}
$$

$$
\leq W_2(\mu_0, \mu_1) \sqrt{I_{U, \nu}(\mu_0)} - K_{\infty, U} \frac{W_2(\mu_0, \mu_1)^2}{2}, (20.14)
$$

where  $K_{\infty,U}$  is defined in (17.10).

(iii) If  $N < \infty$ ,  $K \geq 0$  and  $U_{\nu}(\mu_1) < +\infty$  then

$$
U_{\nu}(\mu_{0}) - U_{\nu}(\mu_{1})
$$

$$
\leq \int U''(\rho_{0}(x_{0})) |\nabla \rho_{0}(x_{0})| d(x_{0}, x_{1}) \pi(dx_{0} dx_{1}) - K\lambda_{N,U} \max(||\rho_{0}||_{L^{\infty}(\nu)}, ||\rho_{1}||_{L^{\infty}(\nu)})^{-\frac{1}{N}} \frac{W_{2}(\mu_{0}, \mu_{1})^{2}}{2}
$$

$$
\leq W_{2}(\mu_{0}, \mu_{1}) \sqrt{I_{U, \nu}(\mu_{0})} - K\lambda_{N,U} \max(||\rho_{0}||_{L^{\infty}(\nu)}, ||\rho_{1}||_{L^{\infty}(\nu)})^{-\frac{1}{N}} \frac{W_{2}(\mu_{0}, \mu_{1})^{2}}{2},
$$

$$
(20.15)
$$

where

$$
\lambda_{N,U} = \lim_{r \to 0} \frac{p(r)}{r^{1-\frac{1}{N}}}.
$$
\n(20.16)

**Exercise 20.11.** When  $U$  is well-behaved, give a more direct derivation of (20.14), via plain displacement convexity (rather than distorted displacement convexity). The same for (20.15), with the help of Exercise 17.23.

Remark 20.12. As the proof will show, Theorem 20.10(iii) extends to negative curvature modulo the following changes: replace  $\lim_{r\to 0}$ in (20.16) by  $\lim_{r\to\infty}$ ; and  $(\max(\|\rho_0\|_{L^{\infty}}, \|\rho_1\|_{L^{\infty}}))^{-1/N}$  in (20.15) by  $\max(||1/\rho_0||_{L^{\infty}}, ||1/\rho_1||_{L^{\infty}})^{1/N}$ . (This result is not easy to derive by plain displacement convexity.)

Corollary 20.13 (HWI inequalities). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvature-dimension bound  $CD(K, \infty)$  for some  $K \in \mathbb{R}$ . Then:

(i) Let  $p \in [2, +\infty) \cup \{c\}$  satisfy (17.30) for  $N = \infty$ , and let  $\mu_0 =$  $\rho_0 \nu$ ,  $\mu_1 = \rho_1 \nu$  be any two probability measures in  $P_p^{\text{ac}}(M)$ , such that  $H_{\nu}(\mu_1) < +\infty$  and  $\rho_0$  is Lipschitz; then

$$
H_{\nu}(\mu_0) - H_{\nu}(\mu_1) \le W_2(\mu_0, \mu_1) \sqrt{I_{\nu}(\mu_0)} - K \frac{W_2(\mu_0, \mu_1)^2}{2}.
$$

(ii) If  $\nu \in P_2(M)$  then for any  $\mu \in P_2(M)$ ,

$$
H_{\nu}(\mu) \le W_2(\mu, \nu) \sqrt{I_{\nu}(\mu)} - K \frac{W_2(\mu, \nu)^2}{2}.
$$
 (20.17)

Remark 20.14. The HWI inequality plays the role of a nonlinear interpolation inequality: it shows that the Kullback information  $H$  is controlled by a bit of the Fisher information  $I$  (which is stronger, in the sense that it involves smoothness) and the Wasserstein distance  $W_2$  (which is weaker). A related "linear" interpolation inequality is  $||h||_{L^2} \leq \sqrt{||h||_{H^{-1}} ||h||_{H^1}},$  where  $H^1$  is the Sobolev space defined by the  $L^2$ -norm of the gradient, and  $H^{-1}$  is the dual of  $H^1$ .

Proof of Corollary 20.13. Statement (i) follows from Theorem 20.10 by choosing  $N = \infty$  and  $U(r) = r \log r$ . Statement (ii) is obtained by approximation: One just needs to find a sequence of probability densities  $\rho_{0,k} \rightarrow \rho_0$  in such a way that each  $\rho_0$  is Lipschitz and  $H_{\nu}(\rho_{0,k}\,\nu) \longrightarrow H_{\nu}(\mu), W_2(\rho_{0,k}\,\nu,\nu) \longrightarrow W_2(\mu,\nu), I_{\nu}(\rho_{0,k}\,\nu) \longrightarrow I_{\nu}(\mu).$ I shall not go into this argument and refer to the bibliographical notes for more information. □

Proof of Theorem 20.10. First recall from the proof of Theorem 17.8 that  $U_{-}(\rho_0)$  is integrable; since  $\rho_0 U'(\rho_0) \geq U(\rho_0)$ , the integrability of  $\rho_0 U'(\rho_0)$  implies the integrability of  $U(\rho_0)$ . Moreover, if  $N = \infty$  then  $U(r) \geq a r \log r - b r$  for some positive constants a, b (unless U is linear). So

$$
\left[\rho_0 U'(\rho_0) \in L^1\right] \Longrightarrow \left[U(\rho_0) \in L^1\right] \stackrel{\text{if } N = \infty}{\Longrightarrow} \left[\rho_0 \log_+ \rho_0 \in L^1\right].
$$

The proof of (20.12) will be performed in three steps.

**Step 1:** In this step I shall assume that U and  $\beta$  are nice. More precisely,

- If  $N < \infty$  then U is Lipschitz, U' is Lipschitz and  $\beta$ ,  $\beta'$  are bounded;
- If  $N = \infty$  then  $U(r) = O(r \log(2 + r))$  and U' is Lipschitz.

Let  $(\mu_t = \rho_t \nu)_{0 \leq t \leq 1}$  be the unique Wasserstein geodesic joining  $\mu_0$ to  $\mu_1$ . Recall from Theorem 17.37 the displacement convexity inequality

$$
\begin{align*}
\int_{M} U(\rho_t) d\nu &\leq (1-t) \int_{M \times M} U\left(\frac{\rho_0(x_0)}{\beta_{1-t}(x_0, x_1)}\right) \beta_{1-t}(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0) \\
&+ t \int_{M \times M} U\left(\frac{\rho_1(x_1)}{\beta_t(x_0, x_1)}\right) \beta_t(x_0, x_1) \pi(dx_0|x_1) \nu(dx_1),
\end{align*}
$$

and transform this into

HWI inequalities 551

$$
\int_{M \times M} U\left(\frac{\rho_0}{\beta_{1-t}}\right) \beta_{1-t} \pi \, d\nu \le \int_{M \times M} U\left(\frac{\rho_1}{\beta_t}\right) \beta_t \pi \, d\nu
$$

$$
+ \int_{M \times M} \left[ \frac{U\left(\frac{\rho_0}{\beta_{1-t}}\right) \beta_{1-t} - U(\rho_0)}{t} \right] \pi \, d\nu - \frac{1}{t} \int_M \left[ U(\rho_t) - U(\rho_0) \right] \, d\nu. \tag{20.18}
$$

The problem is to pass to the limit as  $t \to 0$ . Let us consider the four terms in (20.18) one after the other.

First term of  $(20.18)$ : If  $K = 0$  there is nothing to do.

If  $K > 0$  then  $\beta_t(x_0, x_1)$  is a decreasing function of t; since  $U(r)/r$  is a nondecreasing function of r it follows that  $U(\rho_0/\beta)\beta \leq$  $U(\rho_0/\beta_{1-t})\beta_{1-t} \uparrow U(\rho_0)$  (as  $t \to 0$ ). By the proof of Theorem 17.28,  $U-(\rho_0/\beta)$  is integrable, so we may apply the monotone convergence theorem to conclude that

$$
\int U\left(\frac{\rho_0(x_0)}{\beta_{1-t}(x_0, x_1)}\right) \beta_{1-t}(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0) \xrightarrow[t \to 0]{} \int U(\rho_0) d\nu.
$$
\n(20.19)

If  $K < 0$  then  $\beta_{1-t}$  is an increasing function of t,  $U(\rho_0/\beta) \beta \ge$  $U(\rho_0/\beta_{1-t})\beta_{1-t} \downarrow U(\rho_0)$ , and now we should check the integrability of  $U_{+}(\rho_0/\beta)\beta$ . In the case  $N < \infty$ , this is a consequence of the Lipschitz continuity of U. In the case  $N = \infty$ , this comes from

$$
\int U\left(\frac{\rho_0(x_0)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
\leq C \int \rho_0(x_0) \log \left(2 + \rho_0(x_0) + \frac{1}{\beta(x_0, x_1)}\right) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
\leq C \int \rho_0 \log(2 + \rho_0) d\nu + C \int \log \left(2 + \frac{1}{\beta(x_0, x_1)}\right) \pi(dx_0 dx_1)
$$

$$
\leq C \left(1 + \int \rho_0 \log \rho_0 d\nu + W_2(\mu_0, \mu_1)^2\right),
$$

where  $C$  stands for various numeric constants. Then  $(20.19)$  also holds true for  $K < 0$ .

Second term of  $(20.18)$ : This is the same as for the first term except that the inequalities are reversed. If  $K > 0$  then  $U(\rho_1) \geq U(\rho_1/\beta_t) \beta_t \downarrow$  $U(\rho_1/\beta)\beta$ , and to pass to the limit it suffices to check the integrability of  $U_{+}(\rho_1)$ . If  $N < \infty$  this follows from the Lipschitz continuity of U, while if  $N = \infty$  this comes from the assumption  $\rho_1 \log_+ \rho_1 \in L^1(\nu)$ .

If  $K < 0$  then  $U(\rho_1) \leq U(\rho_1/\beta_t) \beta_t \uparrow U(\rho_1/\beta) \beta$ , and now we can conclude because  $U_-(\rho_1)$  is integrable by Theorem 17.8. In either case,

$$
\int U\left(\frac{\rho_1(x_1)}{\beta_t(x_0, x_1)}\right) \beta_t(x_0, x_1) \pi(dx_0 | x_1) \nu(dx_1)
$$

$$
\xrightarrow[t \to 0]{} \int U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1) \pi(dx_0 | x_1) \nu(dx_1). \quad (20.20)
$$

Third term of  $(20.18)$ : This term exists only if  $K \neq 0$ . By convexity of U, the function  $b \mapsto U(r/b)$  is convex, with derivative  $-p(r/b)$ ; so

$$
U(\rho_0) - U\left(\frac{\rho_0}{\beta_{1-t}}\right) \beta_{1-t} \ge -p\left(\frac{\rho_0}{\beta_{1-t}}\right) (1 - \beta_{1-t});
$$

or equivalently

$$
\frac{U\left(\frac{\rho_0}{\beta_{1-t}}\right)\beta_{1-t} - U(\rho_0)}{t} \le p\left(\frac{\rho_0}{\beta_{1-t}}\right)\left(\frac{1-\beta_{1-t}}{t}\right). \tag{20.21}
$$

Since U is convex, p is nondecreasing. If  $K > 0$  then  $\beta_{1-t}$  decreases as t decreases to 0, so  $p(\rho_0/\beta_{1-t})$  increases to  $p(\rho_0)$ , while  $(1 - \beta_{1-t}(x_0, x_1))/t$  increases to  $\beta'(x_0, x_1)$ ; so the right-hand side of (20.21) increases to  $p(\rho) \beta'$ . The same is true if  $K < 0$  (the inequalities are reversed but the product of two decreasing nonnegative functions is nondecreasing). Moreover, for  $t = 1$  the left-hand side of (20.21) is integrable. So the monotone convergence theorem implies

$$
\limsup_{t \downarrow 0} \int \left[ \frac{U\left(\frac{\rho_0(x_0)}{\beta_{1-t}(x_0, x_1)}\right) \beta_{1-t}(x_0, x_1)}{t} \right] \pi(dx_1|x_0) \nu(dx_0)
$$
  
$$
\leq \int p(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0).
$$
 (20.22)

Fourth term of  $(20.18)$ : By Theorem 20.1,

$$
\limsup_{t \downarrow 0} \left( -\frac{1}{t} \int [U(\rho_t) - U(\rho_0)] d\pi \right)
$$
  
 
$$
\leq \int U''(\rho_0(x_0)) |\nabla^-\rho_0|(x_0) d(x_0, x_1) \pi(dx_0 dx_1). \quad (20.23)
$$

All in all, (20.12) follows from (20.19), (20.20), (20.22), (20.23).

Step 2: Relaxation of the assumptions on  $U$ .

By Proposition 17.7 we can find a sequence  $(U_{\ell})_{\ell \in \mathbb{N}}$  such that  $U_{\ell}$ coincides with U on  $[\ell^{-1}, \ell], U_{\ell}(r)$  is nonincreasing in  $\ell$  for  $r \leq 1$  and nondecreasing for  $r \geq 1$ ,  $U_{\ell}$  is linear close to the origin,  $U'_{\ell}$  is Lipschitz,  $U''_{\ell} \leq C U''$ , and

- if  $N < \infty$ ,  $U_{\ell}$  is Lipschitz;
- if  $N = \infty$ ,  $U_{\ell}(r) = O(r \log(2+r)).$

Then by Step 1, with the notation  $p_{\ell}(r) = r U_{\ell}'(r) - U_{\ell}(r)$ ,

$$
\int_{M} U_{\ell}(\rho_{0}) d\nu \leq \int_{M \times M} U_{\ell} \left( \frac{\rho_{1}(x_{1})}{\beta(x_{0}, x_{1})} \right) \beta(x_{0}, x_{1}) \pi(dx_{0}|x_{1}) \nu(dx_{1})
$$

$$
+ \int_{M \times M} p_{\ell}(\rho_{0}(x_{0})) \beta'(x_{0}, x_{1}) \pi(dx_{1}|x_{0}) \nu(dx_{0})
$$

$$
+ \int_{M \times M} U_{\ell}''(\rho_{0}(x_{0})) |\nabla \rho_{0}(x_{0})| d(x_{0}, x_{1}) \pi(dx_{0} dx_{1}).
$$
(20.24)

Passing to the limit in  $\int U_{\ell}(\rho_0)$  and  $\int U_{\ell}(\rho_1/\beta) \beta$  is performed as in the proof of Theorem 17.37.

Next I claim that

$$
\limsup_{\ell \to \infty} \int_{M \times M} p_{\ell}(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
\leq \int_{M \times M} p(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0). (20.25)
$$

To prove (20.25), first note that  $p(0) = 0$  (because  $p(r)/r^{1-1/N}$  is nondecreasing), so  $p_{\ell}(r) \to p(r)$  for all r, and the integrand in the left-hand side converges to the integrand in the right-hand side.

Moreover, since  $p_\ell(0) = 0$  and  $p'_\ell(r) = r U''_\ell(r) \leq C r U''(r) =$  $C p'(r)$ , we have  $0 \leq p_{\ell}(r) \leq C p(r)$ . Then:

- If  $K = 0$  then  $\beta' = 0$  and there is nothing to prove.
- If  $K < 0$  then  $\beta' > 0$ . If  $\int p(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)$  $+\infty$  then the left-hand side converges to the right-hand side by dominated convergence; otherwise the inequality is obvious.
- If  $K > 0$  and  $N < \infty$  then  $\beta'$  is bounded and we may conclude by dominated convergence as soon as  $\int p(\rho_0(x_0)) d\nu(x_0) < +\infty$ . This in turn results from the fact that  $\rho_0 U'(\rho_0), U_-(\rho_0) \in L^1(\nu)$ .

• If  $K > 0$  and  $N = \infty$ , then  $\beta'(x_0, x_1) = -(K/3) d(x_0, x_1)^2$ , so the same reasoning applies if

$$
\int p(\rho_0(x_0)) d(x_0, x_1)^2 \pi(dx_1|x_0) \nu(dx_0) < +\infty.
$$
 (20.26)

But the left-hand side of (20.26) is bounded by

$$
\sqrt{\int \frac{p(\rho_0(x_0))^2}{\rho_0(x_0)} \nu(dx_0)} \sqrt{\int d(x_0, x_1)^2 \pi(dx_0 dx_1)} = \sqrt{\int \frac{p(\rho_0)^2}{\rho_0} d\nu} W_2(\mu_0, \mu_1),
$$

which is finite by assumption.

It remains to take care of the last term in (20.24), i.e. show that

$$
\limsup_{\ell \to \infty} \int U''_{\ell}(\rho_0(x_0)) |\nabla \rho_0(x_0)| d(x_0, x_1) \pi(dx_0 dx_1)
$$
  
$$
\leq \int U''(\rho_0(x_0)) |\nabla \rho_0(x_0)| d(x_0, x_1) \pi(dx_0 dx_1).
$$

If the integral on the right-hand side is infinite, the inequality is obvious. Otherwise the left-hand side converges to the right-hand side by dominated convergence, since  $U''_{\ell}(\rho_0(x_0)) \leq C U''(\rho_0(x_0)).$ 

In the end we can pass to the limit in (20.24), and recover

$$
\int_{M} U(\rho_{0}) d\nu \leq \int_{M \times M} U\left(\frac{\rho_{1}(x_{1})}{\beta(x_{0}, x_{1})}\right) \beta(x_{0}, x_{1}) \pi(dx_{0}|x_{1}) \nu(dx_{1})
$$

$$
+ \int_{M \times M} p(\rho_{0}(x_{0})) \beta'(x_{0}, x_{1}) \pi(dx_{1}|x_{0}) \nu(dx_{0})
$$

$$
+ \int_{M \times M} U''(\rho_{0}(x_{0})) |\nabla \rho_{0}(x_{0})| d(x_{0}, x_{1}) \pi(dx_{0} dx_{1})
$$
(20.27)

Step 3: Relaxation of the assumption on  $\beta$ .

If  $N < \infty$  I have assumed that  $\beta, \beta'$  are bounded, which is true if  $K \leq 0$  or if diam  $(M) < D_{K,N} = \pi \sqrt{(N-1)/K}$ . The only problem is when  $K > 0$  and diam  $(M) = D_{K,N}$ . In this case it suffices to establish (20.27) with N replaced by  $N' > N$  and then pass to the limit as  $N' \downarrow N$ . Explicitly:

$$
\int_{M} U(\rho_{0}) d\nu \leq \int_{M \times M} U\left(\frac{\rho_{1}(x_{1})}{\beta_{0}^{K,N'}(x_{0},x_{1})}\right) \beta_{0}^{K,N'}(x_{0},x_{1}) \pi(dx_{0}|x_{1}) \nu(dx_{1}) + \int_{M \times M} p(\rho_{0}(x_{0})) (\beta^{K,N'})'_{1}(x_{0},x_{1}) \pi(dx_{1}|x_{0}) \nu(dx_{0}) + \int_{M \times M} U''(\rho_{0}(x_{0})) |\nabla \rho_{0}(x_{0})| d(x_{0},x_{1}) \pi(dx_{0} dx_{1}).
$$

Passing to the limit is allowed because the right-hand side is decreasing as  $N' \downarrow N$ . Indeed,  $\beta^{(K,N')}$  is increasing, so  $U(\rho_1/\beta^{K,N'})\beta^{(K,N')}$ is decreasing; and  $(\beta^{(K,N')})_1'$  is decreasing. This concludes the proof of (20.12).

Next, (20.13) is obtained by considering the particular case  $K = 0$ in (20.12) (so  $\beta = 1$  and  $\beta' = 0$ ), and then applying the Cauchy-Schwarz inequality:

$$
\int U''(\rho_0(x_0)) |\nabla \rho_0(x_0)| d(x_0, x_1) \pi(dx_0 dx_1)
$$
  
\n
$$
\leq \sqrt{\int d(x_0, x_1)^2 \pi(dx_0 dx_1)} \sqrt{\int U''(\rho_0(x_0))^2 |\nabla \rho_0(x_0)|^2 \pi(dx_0 dx_1)}.
$$

The case  $N = \infty$  requires a bit more work. Let  $u(\delta) = U(e^{-\delta}) e^{\delta}$ , then *u* is convex, and  $u'(\delta) = -e^{\delta} p(e^{-\delta}),$  so

$$
U(e^{-\delta_1}) e^{\delta_1} \ge U(e^{-\delta_2}) e^{\delta_2} - e^{\delta_2} p(e^{-\delta_2}) (\delta_1 - \delta_2).
$$

In particular,

$$
U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \frac{\beta(x_0, x_1)}{\rho_1(x_1)} \leq U(\rho_1(x_1)) \frac{1}{\rho_1(x_1)}
$$
$$
+ \frac{\beta(x_0, x_1)}{\rho_1(x_1)} p\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \left(\log \frac{1}{\rho_1(x_1)} - \log \frac{\beta(x_0, x_1)}{\rho_1(x_1)}\right)
$$
$$
= \frac{U(\rho_1(x_1))}{\rho_1(x_1)} - \frac{\beta(x_0, x_1)}{\rho_1(x_1)} p\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \frac{K}{6} d(x_0, x_1)^2
$$
$$
\leq \frac{U(\rho_1(x_1))}{\rho_1(x_1)} - \frac{K_{\infty, U}}{6} d(x_0, x_1)^2.
$$

Thus

$$
\int U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1) \pi(dx_0 | x_1) \nu(dx_1)
$$
$$
\leq \int U(\rho_1(x_1)) \nu(dx_1) - \frac{K_{\infty, U}}{6} \int \rho_1(x_1) d(x_0, x_1)^2 \pi(dx_0 | x_1) \nu(dx_1)
$$
$$
= \int U(\rho_1) d\nu - \frac{K_{\infty, U}}{6} W_2(\mu_0, \mu_1)^2.
$$

On the other hand,

$$
\int p(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
\leq -\frac{K_{\infty, U}}{3} \int \rho_0(x_0) d(x_0, x_1)^2 \pi(dx_1|x_0) \nu(dx_0)
$$

$$
= -\frac{K_{\infty, U}}{3} W_2(\mu_0, \mu_1)^2. \tag{20.29}
$$

Plugging (20.28) and (20.29) into (20.12) finishes the proof of (20.14).

The proof of (iii) is along the same lines: I shall establish the identity

$$
\int U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1) \pi(dx_0 | x_1) \nu(dx_1) \\ + \int p(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1 | x_0) \nu(dx_0) \\ \le U_\nu(\mu_1) - K\lambda \left(\frac{(\sup \rho_0)^{-\frac{1}{N}}}{3} + \frac{(\sup \rho_1)^{-\frac{1}{N}}}{6}\right) W_2(\mu_0, \mu_1)^2. (20.30)
$$

This combined with Corollary 19.5 will lead from (20.12) to (20.15).

So let us prove (20.30). By convexity of  $s \longmapsto s^N U(s^{-N}),$ 

$$
U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \frac{\beta(x_0, x_1)}{\rho_1(x_1)} \le \frac{U(\rho_1(x_1))}{\rho_1(x_1)} + N\left(\frac{\beta(x_0, x_1)}{\rho_1(x_1)}\right)^{1-\frac{1}{N}} p\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \left[\left(\frac{\beta(x_0, x_1)}{\rho_1(x_1)}\right)^{\frac{1}{N}} - \frac{1}{\rho_1(x_1)^{1/N}}\right],
$$

which is the same as

$$
U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1) \le U(\rho_1(x_1)) + N p\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1)^{1-\frac{1}{N}} (\beta(x_0, x_1)^{\frac{1}{N}} - 1).
$$

As a consequence,

$$
\int U\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1) \pi(dx_0 | x_1) \nu(dx_1) \le U_{\nu}(\mu_1) + N \int p\left(\frac{\rho_1(x_1)}{\beta(x_0, x_1)}\right) \beta(x_0, x_1)^{1-\frac{1}{N}} \left(\beta(x_0, x_1)^{\frac{1}{N}} - 1\right) \pi(dx_0 | x_1) \nu(dx_1).
$$
(20.31)

Since  $K \geq 0$ ,  $\beta(x_0, x_1)$  coincides with  $(\alpha / \sin \alpha)^{N-1}$  $\sqrt{ }$ , where  $\alpha =$  $K/(N-1) d(x_0, x_1)$ . By the elementary inequality

$$
0 \le \alpha \le \pi \Longrightarrow \qquad N\left(\left(\frac{\alpha}{\sin \alpha}\right)^{\frac{N-1}{N}} - 1\right) \ge \left(\frac{N-1}{6}\right)\alpha^2 \quad (20.32)
$$

(see the bibliographical notes for details), the right-hand side of (20.31) is bounded above by

$$
U_{\nu}(\mu_{1}) - \frac{K}{6} \int p\left(\frac{\rho_{1}(x_{1})}{\beta(x_{0}, x_{1})}\right) \beta(x_{0}, x_{1})^{1-\frac{1}{N}} \pi(dx_{0}|x_{1}) \nu(dx_{1})
$$

$$
\leq U_{\nu}(\mu_{1}) - \frac{K}{6} \left[ \inf_{r>0} \frac{p(r)}{r^{1-\frac{1}{N}}} \right] \int \rho_{1}(x_{1})^{1-\frac{1}{N}} d(x_{0}, x_{1})^{2} \pi(dx_{0}|x_{1}) \nu(dx_{1})
$$

$$
\leq U_{\nu}(\mu_{1}) - \frac{K}{6} \left( \lim_{r \to 0} \frac{p(r)}{r^{1-\frac{1}{N}}} \right) (\sup \rho_{1})^{-\frac{1}{N}} \int \rho_{1}(x_{1}) d(x_{0}, x_{1})^{2} \pi(dx_{0}|x_{1}) \nu(dx_{1})
$$

$$
= U_{\nu}(\mu_{1}) - \frac{K}{6} \lambda (\sup \rho_{1})^{-\frac{1}{N}} W_{2}(\mu_{0}, \mu_{1})^{2},
$$

(20.33)

where  $\lambda = \lambda_{N,U}$ .

On the other hand, since  $\beta'(x_0, x_1) = -(N-1)(1-(\alpha/\tan \alpha)) < 0$ , we can use the elementary inequality

$$
0 < \alpha \le \pi \Longrightarrow \qquad (N-1)\,\left(1 - \frac{\alpha}{\tan \alpha}\right) \ge (N-1)\,\frac{\alpha^2}{3} \qquad (20.34)
$$

(see the bibliographical notes again) to deduce

$$
\int p(\rho_0(x_0)) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)
$$
(20.35)

$$
\leq \left(\inf_{r>0} \frac{p(r)}{r^{1-\frac{1}{N}}}\right) \int \rho_0(x_0)^{1-\frac{1}{N}} \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
\leq \left(\lim_{r\to 0} \frac{p(r)}{r^{1-\frac{1}{N}}}\right) (\sup \rho_0)^{-\frac{1}{N}} \int \rho_0(x_0) \beta'(x_0, x_1) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
\leq \left(\lim_{r\to 0} \frac{p(r)}{r^{1-\frac{1}{N}}}\right) (\sup \rho_0)^{-\frac{1}{N}} \int \rho_0(x_0) \left(\frac{Kd(x_0, x_1)^2}{3}\right) \pi(dx_1|x_0) \nu(dx_0)
$$

$$
= \frac{K\lambda (\sup \rho_0)^{-\frac{1}{N}}}{3} \int d(x_0, x_1)^2 \pi(dx_0 dx_1)
$$

$$
= \frac{K\lambda (\sup \rho_0)^{-\frac{1}{N}}}{3} W_2(\mu_0, \mu_1)^2.
$$
(20.36)

The combination of  $(20.33)$  and  $(20.36)$  implies  $(20.30)$  and concludes the proof of Theorem 20.10. ⊓⊔

## Bibliographical notes

Formula (20.6) appears as Theorem 5.30 in my book [814] when the space is  $\mathbb{R}^n$ ; there were precursors, see for instance [669, 671]. The integration by parts leading from (20.6) to (20.7) is quite tricky, especially in the noncompact case; this will be discussed later in more detail in Chapter 23 (see Theorem 23.14 and the bibliographical notes). Here I preferred to be content with Theorem 20.1, which is much less technical, and still sufficient for most applications known to me. Moreover, it applies to nonsmooth spaces, which will be quite useful in Part III of this course. The argument is taken from my joint work with Lott [577] (where the space  $\mathcal X$  is assumed to be compact, which simplifies slightly the assumptions).

Fisher introduced the Fisher information as part of his theory of "efficient statistics" [373]. It plays a crucial role in the Cramér–Rao inequality [252, Theorem 12.11.1], determines the asymptotic variance of the maximum likelihood estimate [802, Chapter 4] and the rate function for large deviations of time-averages of solutions of heat-like equations [313]. The Boltzmann–Gibbs–Shannon–Kullback information on the one hand, the Fisher information on the other hand, play the two leading roles in information theory [252, 295]. They also have a leading part in statistical mechanics and kinetic theory (see e.g. [817, 812]).

The HWI inequality was established in my joint work with Otto [671]; it obviously extends to any reasonable K-displacement convex functional. A precursor inequality was studied by Otto [669]. An application to a "concrete" problem of partial differential equations can be found in [213, Section 5]. Recently Gao and Wu [405] used the HWI inequality to derive new criteria of uniqueness for certain spin systems.

It is shown in [671, Appendix] and [814, Proof of Theorem 9.17, Step 1] how to devise approximating sequences of smooth densities in such a way that the  $H_{\nu}$  and  $I_{\nu}$  functionals pass to the limit. By adapting these arguments one may conclude the proof of Corollary 20.13.

The role of the HWI inequality as an interpolation inequality is briefly discussed in [814, Section 9.4] and turned into application in [213, Proof of Theorem 5.1]: in that reference we study rates of convergence for certain nonlinear partial differential equations, and combine a bound on the Fisher information with a convergence estimate in Wasserstein distance, to establish a convergence estimate in a stronger sense  $(L^1 \text{ norm}, \text{ for instance}).$ 

The HWI inequality is also interesting as an "infinite-dimensional" interpolation inequality; this is applied in [445] to the study of the limit behavior of the entropy in a hydrodynamic limit.

A slightly different derivation of the HWI inequality is due to Cordero-Erausquin [242]; a completely different derivation is due to Bobkov, Gentil and Ledoux [127]. Variations of these inequalities were studied by Agueh, Ghoussoub and Kang [5]; and Cordero-Erausquin, Gangbo and Houdré [245].

There is no well-identified analog of the HWI inequality for nonquadratic cost functions. For nonquadratic costs in  $\mathbb{R}^n$ , some inequalities in the spirit of HWI are established in [76], where they are used to derive various isoperimetric-type inequalities.

The first somewhat systematic studies of HWI-type inequalities in the case  $N < \infty$  are due to Lott and myself [577, 578].

The elementary inequalities (20.32) and (20.34) are proven in [578, Section 5], where they are used to derive the Lichnerowicz spectral gap inequality (Theorem 21.20 in Chapter 21).

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.