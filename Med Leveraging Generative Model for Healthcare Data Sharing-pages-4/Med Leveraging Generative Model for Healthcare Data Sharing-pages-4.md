# **Universal Semi-supervised Learning for Medical Image Classification**

Lie Ju<sup>1,2,3</sup>, <PERSON><PERSON><sup>3,4</sup>, <PERSON><sup>1,2,3</sup>, <PERSON><PERSON><sup>1,2,3</sup>, <PERSON><sup>1,3,5</sup>, <PERSON><PERSON><sup>6</sup>, and Zongyuan  $\mathbf{Ge}^{1,2,3(\mathbb{E})}$ 

<sup>1</sup> Monash-Airdoc Research, Monash University, Melbourne, Australia *{*Lie.Ju1,zongyuan.ge*}*@monash.edu <sup>2</sup> eResearch Centre, Monash University, Melbourne, Australia

2 eResearch Centre, Monash University, Melbourne, Australia

<sup>3</sup> Monash Medical AI Group, Monash University, Melbourne, Australia

<sup>4</sup> Faculty of Information Technology, Monash University, Melbourne, Australia <sup>5</sup> Harbin Engineering University, Harbin, China

<sup>6</sup> Centre for Eye Research Australia, Melbourne University, Melbourne, Australia https://www.monash.edu/mmai-group

**Abstract.** Semi-supervised learning (SSL) has attracted much attention since it reduces the expensive costs of collecting adequate welllabeled training data, especially for deep learning methods. However, traditional SSL is built upon an assumption that labeled and unlabeled data should be from the same distribution *e.g.,* classes and domains. However, in practical scenarios, unlabeled data would be from unseen classes or unseen domains, and it is still challenging to exploit them by existing SSL methods. Therefore, in this paper, we proposed a unified framework to leverage these unseen unlabeled data for open-scenario semi-supervised medical image classification. We first design a novel scoring mechanism, called dual-path outliers estimation, to identify samples from unseen classes. Meanwhile, to extract unseen-domain samples, we then apply an effective variational autoencoder (VAE) pre-training. After that, we conduct domain adaptation to fully exploit the value of the detected unseen-domain samples to boost semi-supervised training. We evaluated our proposed framework on dermatology and ophthalmology tasks. Extensive experiments demonstrate our model can achieve superior classification performance in various medical SSL scenarios. The code implementations are accessible at: [https://github.com/PyJulie/USSL4MIC.](https://github.com/PyJulie/USSL4MIC)

**Keywords:** Semi-supervised Learning  $\cdot$  Open-set  $\cdot$  Dermatology  $\cdot$  Ophthalmology

# **1 Introduction**

Training a satisfied deep model for medical classification tasks remains highly challenging due to the expensive costs of collecting adequate high-quality anno-

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_34) 34.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 355–365, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_34)\_34

<span id="page-1-0"></span>**Fig. 1.** Problem illustration. (a) Close-set SSL. The samples in the labeled and unlabeled data share the same classes and are collected under the same environment, *i.e.,* dermatoscopes. (b) Open-set SSL. There are unknown classes (UKC) in the unlabeled data, *e.g.,* BCC and BKL. (c) Universal SSL. In addition to the unknown classes, the samples in the unlabeled data may come from other unknown domains (UKD), *e.g.,* samples from other datasets with different imaging and condition settings.

tated data. Hence, semi-supervised learning (SSL) [\[1](#page-8-0),[15,](#page-9-0)[16](#page-9-1)[,19](#page-9-2),[25\]](#page-10-0) becomes a popular technique to exploit unlabeled data with only limited annotated data. Essentially, most existing SSL methods are based on an assumption that labeled and unlabeled data should be from the same close-set distribution and neglect the realistic scenarios. However, in practical clinical tasks (*e.g.,* skin lesion classification), unlabeled data may contain samples from unknown/open-set classes which do not present in the training set, leading to sub-optimal performance.

We further illustrate this problem in Fig. [1.](#page-1-0) Specifically, Fig.  $1-(a)$  $1-(a)$  shows a classic close-set SSL setting: the labeled and unlabeled data from ISIC 2019 dataset [\[10\]](#page-9-3) share the same classes, *i.e.,* melanocytic nevus (NV) and melanoma (MEL). Figure [1-](#page-1-0)(b) shows a condition of **Open-set SSL**, where novel classes of basal cell carcinoma (BCC) and benign keratosis (BKL) are introduced. Recent works for Open-set SSL [\[17,](#page-9-4)[23\]](#page-10-1) mainly focused on identifying those outliers during the model training. Unlike them, here, we further consider a more realistic scenario that also greatly violates the close-set assumption posted above, as shown in Fig.  $1-(c)$  $1-(c)$ . The unlabeled data may share the same classes but come from quite different domains, *e.g.,* MEL from Der7point dataset [\[5\]](#page-9-5), which contains clinical images in addition to dermoscopic images. Meanwhile, there are some unknown novel classes, *e.g.,* BCC from Der7point dataset, leading to both **seen/unseen class** and **domain** mismatch.

To handle this mismatch issue, *Huang* et al. [\[9\]](#page-9-6) proposed CAFA to combine the open-set recognition (OSR) and domain adaptation (DA), namely **Universal SSL**. Specifically, they proposed to measure the possibility of a sample to be unknown classes (UKC) or unknown domains (UKD), which are leveraged to reweight the unlabeled samples. The domain adaptation term can adapt features

from unknown domains into the known domain, ensuring that the model can fully exploit the value of UKD samples. The effectiveness of CAFA relies heavily on the detection of open-set samples where the proposed techniques always fail to generalize on medical datasets. For medical images, such as skin data, UKC and UKD samples can be highly inseparable (*e.g.,* MEL in Fig. [1](#page-1-0) - (a) vs. BCC in Fig.  $1 - (b)$  $1 - (b)$ , particularly when training with limited samples in a semi-supervised setting.

Therefore, in this work, we propose a novel universal semi-supervised framework for medical image classification for both class and domain mismatch. Specifically, to measure the possibility of an unlabeled sample being UKC, we propose a dual-path outlier estimation technique, to measure the possibility of an unlabeled sample being UKC in both feature and classifier levels using prototypes and prediction confidence. In addition, we first present a scoring mechanism to measure the possibility of an unlabeled sample being UKD by pre-training a Variational AutoEncoder (VAE) model, which is more suitable for medical image domain separation with less labeled samples required. With the detected UKD samples, we applied domain adaptation methods for feature matching for different domains. After that, the labeled and unlabeled samples (including featureadapted UKD samples) could be optimized using traditional SSL techniques.

Our contributions can be summarized as: (1) We present a novel framework for universal semi-supervised medical image classification, which enables the model to learn from unknown classes/domains using open-set recognition and domain adaptation techniques. (2) We propose a novel scoring mechanism to improve the reliability of the detection of outliers from UKC/UKD for further unified training. (3) Experiments on datasets with various modalities demonstrate our proposed method can perform well in different open-set SSL scenarios.

# **2 Methodology**

## **2.1 Overview**

The overview of our proposed framework is shown in Fig. [2.](#page-4-0) The framework mainly contains a feature extractor  $\mathcal{F}$ , an adversarial discriminator  $\mathcal{D}$ , a multiclass classifier  $\mathcal{C}$ , and a non-adversarial discriminator  $\mathcal{D}'$ . The feature extractor encodes the inputs  $\mathcal X$  into features  $\mathcal V$ . The multi-class classifier  $\mathcal C$  outputs the predictions of exact diseases. The non-adversarial discriminator predicts the possibility of an instance from unlabeled data to be UKD. The adversarial discriminator conducts feature adaptation on the samples from known and detected unknown domains. To summarize, our target is to score unlabeled samples from unseen classes/domains for further training using SSL and domain adaptation.

## **2.2 Dual-Path Outlier Estimation**

Recent open-set SSL methods [\[8](#page-9-7),[23\]](#page-10-1) mainly focus on the detection of UKC samples, which is known as the OSR task. Those outliers will be removed during the training phase. In this section, we propose a novel OSR technique namely Dualpath Outlier Estimation (DOE) for the assessment of UKC based on both feature similarities and confidence of classifier predictions. Formally, given labeled samples  $\mathcal{X}_l$ , we first warm-up the model with standard cross-entropy loss. Unlike CAFA [\[9](#page-9-6)], which computes instance-wise feature similarity, we argue that samples from known classes should have closer distances to the centric representations, e.g., prototypes, than outliers. The prototypes of a class can be computed as average outputs of its corresponding samples  $x_{l,i} \in \mathcal{X}_l$ :

$$
\mathbf{v}_{l,c_j} = \frac{\sum_{i=1, x_{l,i} \in \mathcal{X}_{l,c_j}}^{N_{c_j}} \mathcal{F}(x_{l,i})}{N_{c_j}},
$$
(1)

where  $N_{c_j}$  denotes the number of instances of class j and  $\mathbf{v}_{c_j}$  is a vector with the shape of  $1\times$ D after the average global pooling layer. Then, the feature similarity of an instance  $x_{u,i} \in \mathcal{X}_U$  to each known class can be calculated as:

$$
\mathbf{d} = \{d_{i,x_i \in c_j}\}_{j=1}^{N_{c_j}} = \{ \left\| \mathcal{F}(x_{u,i|c_j}) - \mathbf{v}_{l,c_j} \right\|_2 \}_{j=1}^{N_{c_j}}.
$$
 (2)

We can assume that if a sample is relatively far from all class-specific prototypes, it should have a larger average value  $d_{avg}$  of distance **d** and can be considered a potential outlier [\[13](#page-9-8),[20,](#page-9-9)[28\]](#page-10-2). Then, we perform strong augmentations on unlabeled inputs and generate two views  $x_{u'_{i,1}}$  and  $x_{u'_{i,2}}$ , which are also subsequently fed into the pre-trained networks and obtain the predictions  $\mathbf{p}_{u_{i,1}}$  and  $\mathbf{p}_{u_{i,2}}$ . Inspired by agreement maximization principle [\[24\]](#page-10-3), a sample to be outliers or not can be determined by the consistency of these two predictions:

$$
p_{ood}(x_i) = |\max(\mathbf{p}_{u_{i,1}}) - \max(\mathbf{p}_{u_{i,2}})|. \tag{3}
$$

Finally, we combine the prototype-based and prediction-based scores:

$$
w_{u,c} = 1 - \sigma(d_{avg} \cdot p_{ood}), \tag{4}
$$

where  $\sigma$  is a normalization function that maps the original distribution into (0.1).

## **2.3 Class-Agnostic Domain Separation**

Although our proposed DOE can help detect potential UKC samples, an obvious issue is that the domain difference can easily disturb the estimation of UKC, *e.g.,* UKD samples have larger distances to the prototypes of known domains. Different from the detection of UKC, distinguishing UKD samples in the unlabeled data is less difficult since there is more environmental gap among different domains such as imaging devices, modalities, or other artifacts. To this end, we adopt the VAE which is agnostic to the supervised signal and can pay more attention to the global style of the domain [\[26](#page-10-4)]. Formally, VAE consists of an encoder  $g(\cdot)$  and a decoder  $f(\cdot)$ , where the encoder compresses high-dimensional

**Fig. 2.** The overview of our proposed framework.

input features  $x_i$  to a low-dimension embedding space and the decoder aims to reconstruct from that by minimizing the errors:

<span id="page-4-0"></span>
$$
\mathcal{L}_{re} = ||x_i - f(g(x_i))||_2^2.
$$
 (5)

In our scenario, we pre-train a VAE model using labeled data and evaluated it on the unlabeled data to obtain the reconstruction errors. Then, we fit a two-component Gaussian Mixture Model (GMM) using the Expectation-Maximization algorithm, which has flexibility in the sharpness of distribution and is more sensitive to low-dimension distribution [\[18](#page-9-10)], *i.e.,* the reconstruction losses  $\mathcal{L}_{re}$ . For each sample  $x_i$ , we have its posterior probability as  $w_{d,i}$  for domain separation. With known domain samples from labeled data (denoted as  $y_{l,d,i} = 0$ ) and the possibility of UKC samples from unlabeled data (denoted as  $y_{u,d,j} = w_{d,j}$ , we optimize a binary cross-entropy loss for non-adversarial discriminator  $\mathcal{D}'$ :

$$
\mathcal{L}_{dom} = -\frac{1}{N_l} \sum_{i=1}^{N_l} 1 - \log(\hat{y}_{l,d,i}) - \frac{1}{N_u} \sum_{j=1}^{N_u} w_{d,j} \cdot \log(\hat{y}_{u,d,j}) - (1 - w_{d,j}) \cdot (\log(1 - \hat{y}_{u,d,j})).
$$

(6)

### **2.4 Optimization with Adversarial Training and SSL**

To make a domain adaptation for distinguished unknown domains, we adopt a regular adversarial training manner [\[2](#page-8-1)], with the labeled data treated as the target domain and the unlabeled data as the source domain. Note that we use two weights  $w'_{d,u}$  from the non-adversarial discriminator and  $w_{c,u}$  from DOE to determine which samples to adapt. The adversarial loss can be formulated as:

$$
\max_{\theta_F} \min_{\theta_D} \mathcal{L}_{adv} = -(1 - y_t) \cdot \log(1 - D(F(\mathcal{X}_l))) - y_s \cdot w'_{u,d} \cdot w_{u,c} \cdot \log D(F(\mathcal{X}_u)), \tag{7}
$$

where  $\theta$  denotes the parameters of the specific module and  $y_s = 1$  and  $y_t = 0$  are the initial domain labels for the source domain and target domain. Then, we can perform unified training from the labeled data and selectively feature-adapted unlabeled data under weights controlled. The overall loss can be formulated as:

$$
\mathcal{L}_{overall} = \mathcal{L}_{CE}(\mathcal{X}_l) - \alpha \cdot \mathcal{L}_{adv}(\mathcal{X}_u | w'_{u,d}, w_{u,c}) + \beta \cdot \mathcal{L}_{SSL}(\mathcal{X}_u | w_{u,c}),
$$
(8)

where  $\alpha$  and  $\beta$  are coefficients. For the semi-supervised term  $\mathcal{L}_{SSL}$ , we adopt Π-model [\[15](#page-9-0)] here. Thus, we can perform a global optimization to better utilize the unlabeled data with the class/domain mismatch.

# **3 Experiments**

## **3.1 Datasets and Implementation Details**

**Dermatology.** For skin lesion recognition, we use four datasets to evaluate our methods: ISIC 2019 [\[10\]](#page-9-3), PAD-UFES-20 [\[22\]](#page-10-5), Derm7pt  $[14]$  and Dermnet [\[4\]](#page-9-12). The statistics of four datasets can be found in our supplementary documents. The images in ISIC2019 are captured from dermatoscopes. The images in PAD-UFES-20 and Dermnet datasets are captured from a clinical scenario, where Derm7pt dataset contains both. Firstly, we divide ISIC 2019 dataset into 4 (NV, MEL, BCC, BKL) + 4 (AK, SCC, VASC, DF) classes as known classes and unknown classes respectively. We sample 500 instances per class from known classes to construct the labeled datasets. Then we sample 250 / 250 instances per class from known classes to construct validation datasets and test datasets, We sample 30% close-set samples and all open-set samples from the left 17,331 instances to form the unlabeled dataset. For the other three datasets, we mix each dataset with ISIC 2019 unlabeled dataset, to validate the effectiveness of our proposed methods on training from different unknown domains.

**Ophthalmology.** We also evaluate our proposed methods on in-house fundus datasets, which were collected from regular fundus cameras, handheld fundus cameras, and ultra-widefield fundus imaging, covering the field of view of 60◦,  $45^{\circ}$ , and  $200^{\circ}$  respectively. We follow [\[11](#page-9-13),[12\]](#page-9-14) and take the diabetic retinopathy (DR) grading with 5 sub-classes (normal, mild DR, moderate DR, severe DR, and proliferative DR) as known classes. We sample 1000/500/500 instances per class to construct the training/validation/test dataset. The samples with the presence of age-related macular degeneration (AMD) which have similar features to DR, are introduced as 4 unknown classes (small drusen, big drusen, dry AMD, and wet AMD). Please refer to our supplementary files for more details.

|                         | $_{\text{Datasets}}$ | <b>ISIC 2019</b>   | $+$ Derm7pt | $+$ PAD-UFES $+$ Dermnet                                                                                              |  |
|-------------------------|----------------------|--------------------|-------------|-----------------------------------------------------------------------------------------------------------------------|--|
| Supervised              | ERM                  | 69.2 ( $\pm$ 0.89) |             |                                                                                                                       |  |
| Close-set SSL $ PL 16 $ |                      |                    |             | 69.4 ( $\pm$ 0.65) 70.1 ( $\pm$ 0.10) 70.2 ( $\pm$ 0.83) 66.9 ( $\pm$ 0.34)                                           |  |
|                         | $PI$ [15]            |                    |             | 70.2 ( $\pm$ 0.33) $\left  70.7 \right  (\pm 0.64)$ $\left  70.3 \right  (\pm 0.22)$ $\left  69.6 \right  (\pm 0.87)$ |  |
|                         | MT [27]              |                    |             | 69.6 ( $\pm$ 0.45) 70.1 ( $\pm$ 0.20) 68.7 ( $\pm$ 0.36) 65.1 ( $\pm$ 1.43)                                           |  |
|                         | VAT $[21]$           |                    |             | 70.3 ( $\pm$ 0.29) 69.9 ( $\pm$ 0.23) 69.2 ( $\pm$ 0.48) 69.6 ( $\pm$ 0.59)                                           |  |
|                         | MM <sub>1</sub>      |                    |             | 59.4 ( $\pm$ 2.23) 60.2 ( $\pm$ 0.55) 60.3 ( $\pm$ 1.65) 41.7 ( $\pm$ 3.97)                                           |  |
|                         | $FM$ [25]            |                    |             | 59.8 ( $\pm$ 1.88) 65.1 ( $\pm$ 0.63) 63.0 ( $\pm$ 1.01) 53.2 ( $\pm$ 1.87)                                           |  |
| Open-set SSL UASD [3]   |                      |                    |             | 70.0 ( $\pm$ 0.47) 67.5 ( $\pm$ 1.30) 68.5 ( $\pm$ 1.01) 61.2 ( $\pm$ 2.85)                                           |  |
|                         | DS3L [6]             |                    |             | 69.3 ( $\pm$ 0.87) 69.8 ( $\pm$ 0.55) 68.9 ( $\pm$ 0.76) 65.3 ( $\pm$ 1.14)                                           |  |
|                         |                      |                    |             | MTCF [29] 65.4 ( $\pm$ 1.99) 69.2 ( $\pm$ 0.89) 66.3 ( $\pm$ 0.76) 66.8 ( $\pm$ 1.01)                                 |  |
|                         | $T2T$ $[8]$          |                    |             | 60.2 ( $\pm$ 0.23) 61.7 ( $\pm$ 0.15) 60.3 ( $\pm$ 0.21) 60.9 ( $\pm$ 0.10)                                           |  |
|                         | OM [23]              |                    |             | 70.1 ( $\pm$ 0.29) 69.6 ( $\pm$ 0.54) 65.8 ( $\pm$ 0.46) 65.4 ( $\pm$ 0.54)                                           |  |
| Universal SSL CAFA [9]  |                      |                    |             | 68.3 ( $\pm$ 1.08) 63.3 ( $\pm$ 1.02) 65.3 ( $\pm$ 1.70) 63.3 ( $\pm$ 1.88)                                           |  |
|                         | Ours                 |                    |             | 71.1 ( $\pm$ 1.31) 70.9 ( $\pm$ 1.01) 70.8 ( $\pm$ 0.98) 69.6 ( $\pm$ 1.28)                                           |  |

<span id="page-6-0"></span>**Table 1.** The comparative results on skin datasets (5-trial average accuracy%).

<span id="page-6-1"></span>**Table 2.** The comparative results on fundus datasets (5-trial average accuracy%).

|               | Datasets | Regular                                | + Handheld                             | + UWF                                  |
|---------------|----------|----------------------------------------|----------------------------------------|----------------------------------------|
| Supervised    | ERM      | $70.96  ( \pm  0.98)$                  |                                        |                                        |
| Close-set SSL | PI [15]  | $\underline{75.01}  (\pm  0.86)$       | $\underline{74.51}  (\pm  1.20)$       | $\underline{73.85}  (\pm  1.65)$       |
| Open-set SSL  | UASD [3] | $73.25  (\pm  2.51)$                   | $73.05  (\pm  1.35)$                   | $\underline{73.96}  (\pm  1.77)$       |
| Universal SSL | CAFA [9] | $72.00  (\pm  2.34)$                   | $73.52  (\pm  1.76)$                   | $65.21  (\pm  4.21)$                   |
|               | Ours     | <b><math>77.55  (\pm  2.85)</math></b> | <b><math>78.02  (\pm  2.01)</math></b> | <b><math>74.10  (\pm  2.36)</math></b> |

**Implementation Details.** All Skin images are resized into 224×224 pixels and all fundus images are resized into  $512\times512$  pixels. We take ResNet-50 [\[7\]](#page-9-17) as our backbones for the classification model and VAE training. We use Π-model [\[15\]](#page-9-0) as a SSL regularizer. We warm up the model using exponential rampup [\[15\]](#page-9-0) with 80 out of 200 epochs, to adjust the coefficients of adversarial training  $\alpha$  and SSL β. We use SGD optimizer with a learning rate of  $3\times10^{-4}$  and a batch size of 32. Some regular augmentation techniques are applied such as random crop, and flip, with color jitter and gaussian blur as strong augmentations for the assessment of UKC. For a fair comparison study, we kept all basic hyper-parameters such as augmentations, batch size, and learning rate the same on comparison methods.

## **3.2 Comparison Study**

**Performance on Skin Dataset.** As shown in Table [1,](#page-6-0) we have compared our proposed methods with existing SSL methods, which are grouped with respect

| Datasets     | ISIC         | D7pt         | PAD          | DN           |
|--------------|--------------|--------------|--------------|--------------|
| Confidence   | 59.16        | 61.94        | 63.01        | 68.41        |
| Pertur. [9]  | 61.12        | 62.55        | 61.32        | 60.17        |
| VAE          | 66.02        | 66.32        | 63.28        | 79.09        |
| OVA-Net [23] | 58.17        | 59.82        | 56.71        | 79.46        |
| <b>Ours</b>  | <b>67.99</b> | <b>66.99</b> | <b>65.32</b> | <b>83.21</b> |

<span id="page-7-0"></span>**Table 3.** Domain separation AUC%.

**Table 4.** Ablation study results.

<span id="page-7-1"></span>

| Datasets | ISIC | D7pt | PAD  | DN   |
|----------|------|------|------|------|
| w/o SSL  | 69.2 |      |      |      |
| w/o DOE  | 68.7 | 68.2 | 67.9 | 66.0 |
| w/o CDS  | 55.6 | 59.3 | 61.1 | 51.9 |
| w/o DA   | 70.5 | 69.3 | 68.5 | 65.6 |
| Ours     | 71.1 | 70.9 | 70.8 | 69.6 |

**Fig. 3.** The visualized examples from unlabeled data with normalized scores.

to different realistic scenarios. It can be seen that our proposed methods achieve competitive results on all datasets with different domain shifts. An interesting finding is that existing methods in open-set SSL, such as MTCF [\[29\]](#page-10-8), do not work well in our settings. This is because these methods rely heavily on the estimation and removal of UKC samples. However, the OSR techniques used, which are designed for classical image classification, are not applicable to medical images.

**Performance on Fundus Dataset.** Table [2](#page-6-1) reports the comparative results on fundus datasets. We select one technique for comparison study that shows the best results in each group for different SSL scenarios. Our proposed methods achieve the best performance over other competitors and significant improvements over baseline models in all settings. Unlike the results on the skin dataset, CAFA achieves satisfactory results except for UWF. UASD improves the performance over the baseline ERM model. This is probably because DR and AMD share similar features or semantic information such as hemorrhage and exudates, which can well enhance the feature learning [\[12](#page-9-14)].

**Novel Domain Detection.** As we claim that our proposed scoring mechanism can well identify UKD samples, we perform experiments on the unknown domain separation using different techniques. Our proposed CDS scoring mechanism achieves the best results for unknown domain separation. Moreover, we can find that UKD samples are also sensitive to prototype distances, *e.g.,* with a high AUC of 80.79% in terms of Dermnet (DN), which confirms the importance and necessity of disentangling the domain information for the detection of UKC.

## **3.3 Ablation Study**

**Analysis on the Components.** We perform an ablation study on each component, and the results are shown in Table [3.](#page-7-0) The 'w/o DOE' denotes that we use the unlabeled data re-weighed by  $w_d$  to train the II-model without considering the class mismatch. The 'w/o CDS' denotes that we directly take the whole unlabeled data as the unknown domains for domain adaptation without considering the condition of domain mix, *e.g.,* ISIC & Dermnet. The 'w/o DA' denotes that we exploit  $w_c$  and  $w_d$  to re-weight the unlabeled samples but without the adversarial training term. It is found that the dataset with a larger domain gap such as Dermnet suffers from more performance degradation (from 69.6% to 65.6%).

**Semantic Correlations of Open-Set Samples.** To explore semantic correlations between samples exhibiting class/domain mismatch, we visualize instances from unlabeled data alongside their corresponding normalized UKC/UKD scores, as depicted in Fig. [3.](#page-7-1) It is noteworthy that while Fig.  $3-(c)$  $3-(c)$  exhibits no AMD-related lesions, it yields the highest  $w_{u,c}$ , indicating a higher semantic similarity in the feature space, e.g., hemorrhages. Incorporating such samples into unified training can effectively enhance model performance by enriching useful features.

# **4 Conclusion**

In this work, we propose a novel universal semi-supervised learning framework for medical image classification. We propose two novel scoring mechanisms for the separation of samples from unknown classes and unknown domains. Then, we adopt regular semi-supervised learning and domain adaptation on the reweighted unlabeled samples for unified training. Our experimental results show that our proposed methods can perform well under different realistic scenarios.

**Acknowledgments.** The work was partially supported by Airdoc medical AI projects donation Phase 2, Monash-Airdoc Research Centre, and in part by the MRFF NCRI GA89126.

**Disclosure of Interests.** The authors declare that they have no competing interests.

## **References**

- <span id="page-8-0"></span>1. Berthelot, D., Carlini, N., Goodfellow, I., Papernot, N., Oliver, A., Raffel, C.A.: Mixmatch: A holistic approach to semi-supervised learning. Advances in Neural Information Processing Systems **32** (2019)
- <span id="page-8-1"></span>2. Cao, Z., Ma, L., Long, M., Wang, J.: Partial adversarial domain adaptation. In: European Conference on Computer Vision. pp. 135–150 (2018)

- <span id="page-9-15"></span>3. Chen, Y., Zhu, X., Li, W., Gong, S.: Semi-supervised learning under class distribution mismatch. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 34(4), pp. 3569–3576 (2020)
- <span id="page-9-12"></span>4. Dermnet: Dermnet (2023), <https://dermnet.com/>
- <span id="page-9-5"></span>5. Esteva, A., Kuprel, B., Novoa, R.A., Ko, J., Swetter, S.M., Blau, H.M., Thrun, S.: Dermatologist-level classification of skin cancer with deep neural networks. Nature **542**(7639), 115–118 (2017)
- <span id="page-9-16"></span>6. Guo, L.Z., Zhang, Z.Y., Jiang, Y., Li, Y.F., Zhou, Z.H.: Safe deep semi-supervised learning for unseen-class unlabeled data. In: International Conference on Machine Learning. pp. 3897–3906. PMLR (2020)
- <span id="page-9-17"></span>7. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 770–778 (2016)
- <span id="page-9-7"></span>8. Huang, J., Fang, C., Chen, W., Chai, Z., Wei, X., Wei, P., Lin, L., Li, G.: Trash to treasure: harvesting ood data with cross-modal matching for open-set semisupervised learning. In: IEEE/CVF International Conference on Computer Vision. pp. 8310–8319 (2021)
- <span id="page-9-6"></span>9. Huang, Z., Xue, C., Han, B., Yang, J., Gong, C.: Universal semi-supervised learning. Advances in Neural Information Processing Systems **34**, 26714–26725 (2021) 10. ISIC: Isic archive (2023), <https://www.isic-archive.com/>
- <span id="page-9-13"></span><span id="page-9-3"></span>11. Ju, L., Wang, X., Zhao, X., Bonnington, P., Drummond, T., Ge, Z.: Leveraging regular fundus images for training uwf fundus diagnosis models via adversarial learning and pseudo-labeling. IEEE Transactions on Medical Imaging **40**(10), 2911–2925 (2021)
- <span id="page-9-14"></span>12. Ju, L., Wang, X., Zhao, X., Lu, H., Mahapatra, D., Bonnington, P., Ge, Z.: Synergic adversarial label learning for grading retinal diseases via knowledge distillation and multi-task learning. IEEE Journal of Biomedical and Health Informatics **25**(10), 3709–3720 (2021)
- <span id="page-9-8"></span>13. Ju, L., Wu, Y., Wang, L., Yu, Z., Zhao, X., Wang, X., Bonnington, P., Ge, Z.: Flexible sampling for long-tailed skin lesion classification. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2022. pp. 462–471. Springer (2022)
- <span id="page-9-11"></span>14. Kawahara, J., Daneshvar, S., Argenziano, G., Hamarneh, G.: Seven-point checklist and skin lesion classification using multitask multimodal neural nets. IEEE Journal of Biomedical and Health Informatics **23**(2), 538–546 (2018)
- <span id="page-9-0"></span>15. Laine, S., Aila, T.: Temporal ensembling for semi-supervised learning. arXiv preprint [arXiv:1610.02242](http://arxiv.org/abs/1610.02242) (2016)
- <span id="page-9-1"></span>16. Lee, D.H., et al.: Pseudo-label: The simple and efficient semi-supervised learning method for deep neural networks. In: ICML Workshop on challenges in representation learning. vol. 3(2), p. 896 (2013)
- <span id="page-9-4"></span>17. Lee, D., Kim, S., Kim, I., Cheon, Y., Cho, M., Han, W.S.: Contrastive regularization for semi-supervised learning. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3911–3920 (2022)
- <span id="page-9-10"></span>18. Li, J., Socher, R., Hoi, S.C.: Dividemix: Learning with noisy labels as semisupervised learning. In: International Conference on Learning Representations (2020)
- <span id="page-9-2"></span>19. Liu, Q., Yu, L., Luo, L., Dou, Q., Heng, P.A.: Semi-supervised medical image classification with relation-driven self-ensembling model. IEEE Transactions on Medical Imaging **39**(11), 3429–3440 (2020)
- <span id="page-9-9"></span>20. Ming, Y., Sun, Y., Dia, O., Li, Y.: How to exploit hyperspherical embeddings for out-of-distribution detection? arXiv preprint [arXiv:2203.04450](http://arxiv.org/abs/2203.04450) (2022)

- <span id="page-10-7"></span>21. Miyato, T., Maeda, S.i., Koyama, M., Ishii, S.: Virtual adversarial training: a regularization method for supervised and semi-supervised learning. IEEE Transactions on Pattern Analysis and Machine Intelligence **41**(8), 1979–1993 (2018)
- <span id="page-10-5"></span>22. Pacheco, A.G., Lima, G.R., Salomao, A.S., Krohling, B., Biral, I.P., de Angelo, G.G., Alves Jr, F.C., Esgario, J.G., Simora, A.C., Castro, P.B., et al.: Pad-ufes-20: A skin lesion dataset composed of patient data and clinical images collected from smartphones. Data in Brief **32**, 106221 (2020)
- <span id="page-10-1"></span>23. Saito, K., Kim, D., Saenko, K.: Openmatch: Open-set semi-supervised learning with open-set consistency regularization. Advances in Neural Information Processing Systems **34**, 25956–25967 (2021)
- <span id="page-10-3"></span>24. Sindhwani, V., Niyogi, P., Belkin, M.: A co-regularization approach to semisupervised learning with multiple views. In: Proceedings of ICML workshop on learning with multiple views. vol. 2005, pp. 74–79 (2005)
- <span id="page-10-0"></span>25. Sohn, K., Berthelot, D., Carlini, N., Zhang, Z., Zhang, H., Raffel, C.A., Cubuk, E.D., Kurakin, A., Li, C.L.: Fixmatch: Simplifying semi-supervised learning with consistency and confidence. Advances in Neural Information Processing Systems **33**, 596–608 (2020)
- <span id="page-10-4"></span>26. Sun, X., Yang, Z., Zhang, C., Ling, K.V., Peng, G.: Conditional gaussian distribution learning for open set recognition. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 13480–13489 (2020)
- <span id="page-10-6"></span>27. Tarvainen, A., Valpola, H.: Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results. Advances in Neural Information Processing Systems **30** (2017)
- <span id="page-10-2"></span>28. Ye, H., Xie, C., Cai, T., Li, R., Li, Z., Wang, L.: Towards a theoretical framework of out-of-distribution generalization. Advances in Neural Information Processing Systems **34**, 23519–23531 (2021)
- <span id="page-10-8"></span>29. Yu, Q., Ikami, D., Irie, G., Aizawa, K.: Multi-task curriculum framework for openset semi-supervised learning. In: European Conference on Computer Vision. pp. 438–454. Springer (2020)

# **Vertex Proportion Loss for Multi-class Cell Detection from Label Proportions**

Carolina Pacheco<sup>1( $\boxtimes$ )</sup>, Florence Yellin<sup>2</sup>, René Vidal<sup>3</sup>, and Benjamin Haeffele<sup>1</sup>

<sup>1</sup> Mathematical Institute for Data Science (MINDS), Johns Hopkins University, Baltimore, MD, USA *{*cpachec2,bhaeffele*}*@jhu.edu <sup>2</sup> Kitware Inc., Clifton Park, NY, USA

<sup>3</sup> Center for Innovation in Data Engineering and Science (IDEAS), University of Pennsylvania, Philadelphia, Pennsylvania, USA

**Abstract.** Learning from label proportions (LLP) is a weakly supervised classification task in which training instances are grouped into bags annotated only with class proportions. While this task emerges naturally in many applications, its performance is often evaluated on bags generated artificially by sampling uniformly from balanced, annotated datasets. In contrast, we study the LLP task in multi-class blood cell detection, where each image can be seen as a "bag" of cells and class proportions can be obtained using a hematocytometer. This application introduces several challenges that are not appropriately captured by the usual LLP evaluation regime, including variable bag size, noisy proportion annotations, and inherent class imbalance. In this paper, we propose the Vertex Proportion loss, a new, principled loss for LLP, which uses optimal transport to infer instance labels from label proportions, and a Deep Sparse Detector that leverages the sparsity of the images to localize and learn a useful representation of the cells in a self-supervised way. We demonstrate the advantages of the proposed method over existing approaches when evaluated in real and synthetic white blood cell datasets.

# **1 Introduction**

Large, annotated datasets played a critical role in the early success of deep models. Since then, extending this success to unsupervised and weakly supervised regimes has been an active focus of research. One example is *Learning from Label Proportions (LLP)*, a weakly supervised task that aims to learn an instance classifier without instance-level annotations. In particular, LLP assumes that the classifier has access to bags of instances during training, each one annotated only with the proportion of instances corresponding to each class (e.g., 70% class A, 30% class B). This LLP paradigm not only reduces the annotation burden but also arises naturally in diverse applications. For example, in vision-based

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_35) 35.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 366–376, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_35)\_35

<span id="page-12-0"></span>**Fig. 1.** DSD performs multi-class cell detection by learning a structured latent representation of the image, suitable for object classification and image reconstruction. The classification task is weakly supervised by label proportions via the proposed VP loss, while the reconstruction task is self-supervised. For lensless imaging, a physics-based, sparsifying transformation  $\mathcal{T}(\cdot)$  is incorporated to the model.

blood count, the goal is to detect and classify all the blood cells (instances) in an image (bag). Annotating the location and class of each cell becomes prohibitively expensive given the large number of cells and required expertise; however, class proportion information can be obtained using a hematocytometer [\[31](#page-21-0)]. Similarly, when analyzing lung images of emphysema patients, one might be interested in predicting segmentation masks (instances) corresponding to damaged tissue, but due to annotation cost only have access to the proportion of diseased tissue in each image (bag) [\[4](#page-20-0)]. Other relevant LLP applications include assisted reproductive technology [\[11](#page-20-1)[,12](#page-20-2)] and vision-based sperm cell classification [\[7](#page-20-3)].

While several approaches to address the LLP task have been explored in the past, existing methods rely on ad-hoc heuristics, are very slow to compute, require precise hyperparameter tuning, or have numerous trivial solutions. Recent methods perform extensions to multi-class, high-dimensional tasks like image classification [\[6](#page-20-4),[15](#page-20-5)[–18](#page-20-6)[,24](#page-21-1)[,26](#page-21-2),[30\]](#page-21-3); however, their performance in real-world LLP settings remains unknown since they have been consistently evaluated in an artificial LLP scenario. Namely, images are uniformly sampled from balanced classification datasets (MNIST, CIFAR-10, CIFAR-100, among others) to create bags of a fixed size, and their class proportions are obtained by aggregating the ground-truth (GT) instance labels. This evaluation setting fails to replicate key aspects observed in real-world applications such as class imbalance, noisy label proportions, and variable bag size.

We aim to advance the understanding of LLP by studying it in the context of a relevant application: multi-class cell detection in lensless imaging. We focus on white blood cell (WBC) subtype classification, which is critical in the assessment of infections and the status of the immune system in general [\[2\]](#page-19-0). Among other challenges, this task presents a strong class imbalance, with granulocytes and monocytes representing more than 50% and less than 12% of the WBCs, respectively [\[5](#page-20-7)]. Moreover, WBC proportions from complete blood count (CBC) reports are imprecise for this task, since they are not obtained from the same field of view as our images, and the detection process incorporates additional noise. In this context, the contributions of this paper are the following:

- 1. We propose a principled LLP loss, the *vertex proportion (VP) loss*, which is based on optimal transport (OT) and propagates global annotations to instance labels as latent variables. Unlike OT-based pseudo-labeling approaches  $[6,15,18]$  $[6,15,18]$  $[6,15,18]$  $[6,15,18]$ , the VP loss computation is simple as it does not require additional hyperparameters, alternating updates, or postprocessing steps.
- 2. We introduce the *deep sparse detector (DSD)*, a self-supervised model that localizes cells and learns useful representations to classify them in a unified framework. Unlike the greedy state-of-the-art detector for this problem [\[31\]](#page-21-0), DSD can detect hundreds of cells in an image with just a single forward pass, and it does not rely on alternative data sources for training.
- 3. We study, for the first time, the *WBC subtype classification task as an LLP problem*. We evaluate our method along with other common LLP approaches in a real dataset, and also generate a new, synthetic dataset<sup>[1](#page-13-0)</sup> which allows for the computation of detailed metrics. Unlike artificial scenarios [\[6\]](#page-20-4), in this case we observe that inferring instance-level labels during training is critical.

# **2 Proposed Approach**

We address this weakly supervised multi-class cell detection task through a unified architecture, the DSD model in Fig. [1,](#page-12-0) and divide the learning problem into self-supervised localization and LLP classification. Section [2.1](#page-13-1) introduces the LLP problem and our VP loss to learn from proportions (given a bag of cell embeddings), and Sect. [2.2](#page-15-0) explains how DSD predicts cell locations and embeddings.

<span id="page-13-1"></span>

## **2.1 Learning from Label Proportions (LLP)**

Let  $\{(\mathbf{x}_i, \mathbf{y}_i)\}_{i=1}^N$  be the set of N cells present in an image (bag), where  $\mathbf{x}_i \in \mathbb{C}^d$ is the embedding associated with the *i*-th object (we use complex-valued features because they arise naturally in lensless imaging), and  $y_i \in \mathbb{R}^K$  is a one-hot vector indicating its GT class (out of  $K$  classes). The classification task aims to find a function  $C_{\Theta_C}$  that correctly estimates the class labels , i.e.,  $C_{\Theta_C}(\mathbf{x}_i) := \hat{\mathbf{y}}_i \approx$  $y_i$ ,  $\forall i$ . This is usually achieved by minimizing the cross-entropy loss; however, its computation requires cell-level class annotations  $y_i$ , which are not available in LLP. In contrast, we need to learn from class proportions  $Y = \frac{1}{N} \sum_{i=1}^{N} \mathbf{y}_i$ .

As an initial approach to this problem  $[1,6]$  $[1,6]$ , one might consider the Kullback-Leibler divergence (KL-div) loss between the predicted proportions and the true proportions, given as  $D_{KL}(\big\{Y \parallel \hat{Y}\big\}) = \sum$ K  $Y^k \cdot \log \left(\frac{Y^k}{\hat{Y}^k}\right)$ ), with  $\hat{Y} = \frac{1}{N} \sum_{i=1}^{N} \hat{y}_i$ .

 $k=1$ However, note that the  $D_{KL}(Y||\hat{Y})$  loss is somewhat ill-posed for the LLP problem as it is not only minimized in the desired scenario  $(\hat{\mathbf{y}}_i = \mathbf{y}_i, \forall i)$ , but also in cases where no information is learned about the class of a particular instance (e.g.,  $\hat{\mathbf{y}}_i = Y, \forall i$  is a global optimum). This is a common disadvantage of losses that operate directly on the aggregated information  $\hat{Y}$  (i.e., bag-level

<span id="page-13-0"></span><sup>&</sup>lt;sup>1</sup> This dataset and code relevant to this paper can be found in [GitHub.](https://github.com/carolina-pacheco/LLP_multiclass_cell_detection/)

losses). In the literature this has not been reported as a problem  $[1,6,17]$  $[1,6,17]$  $[1,6,17]$  $[1,6,17]$ , likely because they have studied artificial LLP scenarios where bags of instances can be randomly drawn from large datasets, with direct control over the bag sizes, thus providing enough bag diversity for training. For the application of LLP to real-world settings, we propose a new loss, the **VP loss**, designed to infer instance-level labels during training by matching the distributions of predicted and GT class labels, as opposed to relying only on aggregated information.

**VP Loss to Learn from Proportions..** We are interested in matching the distribution of predicted labels  $q(\hat{y})$  to the distribution of GT labels  $p(y)$ . In supervised learning, annotations directly define an assignment between the predicted label  $\hat{\mathbf{y}}_i$  and the GT label  $\mathbf{y}_i$  of each instance, and learning is achieved by minimizing the empirical expectation of a cost  $c(\mathbf{y}, \hat{\mathbf{y}})$ , that can be, for example, the cross-entropy loss. In LLP we aim to do the same, yet the label assignment is unknown. What we do know, however, is the distribution of  $p(\mathbf{y})$ . Namely, given GT class proportions Y, we can write  $p(\mathbf{y}) = \sum_{k=1}^{K} Y^k \cdot \delta(\mathbf{y} - \mathbf{e}_k)$ , where  $\mathbf{e}_k \in \mathbb{R}^K$  corresponds to a canonical vector that is non-zero in its k-th entry, and  $\delta(\cdot)$  represents the delta function that is non-zero at the origin. Since we assume that each cell belongs to one class, the support of  $p(\mathbf{y})$  is concentrated on the vertices of the simplex (blue dots in Fig. [2\)](#page-14-0) and the corresponding probability mass associated with each vertex (class) is defined by the entries of  $Y$ .

OT aims to find a transportation plan (i.e., assignment) of minimal cost to match the probability mass between two distributions [\[25\]](#page-21-4). Although it is a well-studied problem with numerous applications, it is intractable to solve in the general case. However, here the structure of  $p(\mathbf{y})$  significantly simplifies the formulation. In particular, given a transport cost function  $c(\cdot, \cdot)$ , GT proportions Y, and predicted labels  $\{\hat{\mathbf{y}}_i\}_{i=1}^N$ , after approximating an analytic expectation with an empirical expectation based on samples  $\{\hat{\mathbf{y}}_i\}_{i=1}^N \sim q$ , the OT problem reduces to (see details in the supplement) the following linear program (LP)

<span id="page-14-0"></span>**Fig. 2.** Distributions of interest.  $p(\mathbf{y})$ : GT labels,  $q(\hat{y})$ : class predictions, and  $c(\mathbf{y}, \hat{\mathbf{y}})$ : cost for moving a unit of mass from  $y$  to  $\hat{y}$ .

<span id="page-14-1"></span>
$$
\max_{\Phi \in \mathbb{R}^K, \mathbf{s} \in \mathbb{R}^N} \langle Y, \Phi \rangle - \frac{1}{N} \langle \mathbf{s}, \mathbf{1}_N \rangle \quad \text{s.t.} \quad s_i \ge \phi_k - c(\mathbf{e}_k, \hat{\mathbf{y}}_i), \forall (i, k) \in [N] \times [K], \tag{1}
$$

where  $\mathbf{1}_N \in \mathbb{R}^N$  is the vector of all ones,  $\phi_k$  is the k-th entry of  $\Phi$ ,  $s_i$  is the *i*-th entry of **s**, and  $[N]$  is the set of integers from 1 to N. The optimal label assignment for the *i*-th cell is given by  $\mathbf{e}_{k^*}$ , with  $k^* \in \arg\max_k \{ \phi_k^* - c(\mathbf{e}_k, \hat{\mathbf{y}}_i) \}$ and  $\phi_k^*$  the k-th entry of the optimal value of  $\Phi$  in Eq. [\(1\)](#page-14-1).

Now, we would like to use gradient-based optimization to train our classifier  $\mathcal{C}_{\Theta_{C}}(\cdot)$ , using as a loss function the expectation of  $c(\cdot, \cdot)$  given the optimal assignments. In particular, we need to compute the gradient of the loss with respect to the output of the network  $\hat{\mathbf{y}}_i$ . Note that (i) the value of this loss is by definition the same as the optimal value of  $\overline{OT}$ , i.e., the solution to Eq. [\(1\)](#page-14-1), and (ii) the learnable parameters in Eq. [\(1\)](#page-14-1) appear in the evaluation of the costs  $c(\mathbf{e}_k, \hat{\mathbf{y}}_i)$ , with  $\hat{\mathbf{y}}_i = C_{\Theta_C}(\mathbf{x}_i)$ . Therefore, we need to compute the gradient of Eq. [\(1\)](#page-14-1) with respect to  $c(\mathbf{e}_k, \hat{\mathbf{y}}_i)$ , and then the gradient of  $c(\mathbf{e}_k, \hat{\mathbf{y}}_i)$  with respect to  $\hat{\mathbf{y}}_i$  can be obtained in closed form or via auto-differentiation. Given the linear structure of the problem, the gradient of the loss with respect to the constraints  $c(\mathbf{e}_k, \hat{\mathbf{y}}_i)$ *conveniently equals the optimal value of the dual variable* [\[20\]](#page-21-5), and thus it is obtained directly from the computation of the loss, without having to explicitly compute or store the assignments.

**Connections to other LLP Approaches.** Current LLP approaches can be divided into three categories: new losses  $[1,6]$  $[1,6]$  $[1,6]$ ; pseudo-labeling strategies to alternate with supervised losses  $[6,15,18]$  $[6,15,18]$  $[6,15,18]$  $[6,15,18]$ ; and representation learning approaches [\[16](#page-20-9),[17,](#page-20-8)[19,](#page-20-10)[24](#page-21-1)[,26](#page-21-2),[30\]](#page-21-3). Our loss belongs to the first group, yet it enjoys advantages similar to pseudo-labeling as it also infers instance-level information for training.

<span id="page-15-0"></span>

## **2.2 Deep Sparse Detector (DSD)**

The LLP approach described in Sec. [2.1](#page-13-1) assumes that cells have already been detected in the image and that the corresponding features  $\mathbf{x}_i$  have been extracted. Here, we focus on the problem of detecting cells and extracting features. To do so, DSD leverages the sparsity of the imaged specimen, i.e. blood, to generate a structured latent representation of the image, referred to as a *sparse encoding volume*. This representation is spatially sparse, and its local features are discriminative for cell classification. In absence of detailed annotations, we incorporate reconstruction as an auxiliary task, which has been found useful for unsupervised detection [\[13,](#page-20-11)[21](#page-21-6)[,31](#page-21-0)]. While this design is tailored to sparse images, which can be reconstructed from localized feature maps, lensless imaging captures the intensity of the diffraction pattern of the objects as *holograms*, and thus even if the specimen is sparse, the recorded hologram is not. Fortunately, there exist physics-based models for the diffraction process [\[14](#page-20-12)], which can be well approximated by a linear transformation  $I = \mathcal{T}(H)$ , where H is a real-valued hologram and I is a complex-valued image (see Fig. [3\)](#page-16-0). Therefore, we incorporate  $\mathcal T$  as the first layer of DSD, and its inverse as the last layer of the reconstruction head. This strategy can be applied to other cases where images are approximately sparse under invertible, differentiable transformations. Due to the limited resolution of lensless images and the small cell size, this detection task is closer to keypoint estimation than to object detection, as the spatial localization of a cell can be characterized by the location of its center. Lacking supervision, we enforce sparsity by introducing structure into the model as described next.

**Deep Sparse Encoder.** Given an image  $I \in \mathbb{C}^{L \times L}$  containing N objects, the goal of the encoder is to generate a sparse volume as  $A = E_{\Theta_E}(I)$ , where  $E_{\Theta_E}$  corresponds to a sequence of complex-valued CNN layers followed by ReLU non-linearities, and  $A \in \mathbb{C}^{d \times L \times L}$  is the encoding volume composed of d complexvalued feature maps. Unlike other works relying on heatmap annotations for

training [\[8](#page-20-13)[,28](#page-21-7),[29\]](#page-21-8), our encoder encourages spatially "peaked" representations by means of a highly local version of softmax [\[3](#page-19-2)]. More specifically, we compute a heatmap at pixel  $(x, y)$  as  $H_A(x, y) =$  LocalSoftMax $(\|\lambda A(\cdot, x, y)\|_2^2)$ , where  $||A(\cdot, x, y)||_2^2$  is the "strength" of the detection at pixel  $(x, y)$  defined as the  $L_2$ norm squared of the encoding vector at  $(x, y)$ ,  $\lambda \in \mathbb{R}^+$  is a hyperparameter that controls the decay rate from the largest value, and the normalization of the softmax is computed locally in a patch of size  $m \times m$  centered at  $(x, y)$ . The LocalSoftMax equation is easily implemented with a sequence of point-wise operations for the numerator, and then pooling for the denominator. To obtain the sparsest possible representation for each object, we also apply thresholding and non-maximum suppression (NMS) in small patches. We utilize the resulting sparse mask as  $A' = M_A \odot A$ , where  $\odot$  denotes point-wise multiplication, and  $M_A(\cdot) \in \mathbb{R}^{d \times L \times L}$  represents a spatial mask which is constant across the first dimension (features), and at each pixel contains the thresholded, NMS version of  $H_A$ . From this sparse encoding volume,  $A' \in \mathbb{C}^{d \times L \times L}$ , one can extract both the location of the detected objects from the spatial support of A , and their encodings as  $\mathbf{x}_i = A'(\cdot, x_i, y_i) \in \mathbb{C}^d$ , for  $(x_i, y_i)$  in the support of A'.

**Reconstruction head.** We aim to reconstruct the input image from the output of the encoder as  $\hat{I} = R_{\Theta_R}(A')$ . Inspired by the common practice of generating GT keypoint heatmaps by convolving a sparse mask with small, smoothing filters [\[23](#page-21-9)[,27](#page-21-10)], we use a sequence of complex-valued CNN layers with small filters to parameterize  $R_{\Theta_B}$ , thus limiting the spatial extent of the reconstructed cells. We train the network with the Frobenius norm squared as a loss. Since the recorded images correspond to holograms, we apply the inverse of the physicsbased transformation  $\hat{H} = \mathcal{T}^{-1}(\hat{I})$  to compute the loss in the hologram domain.

**Classification head.** From a cell encoding  $\mathbf{x}_i$ , the classifier aims to correctly predict its class. Assuming that objects from distinct classes look different, DSD encodings are expected to contain somewhat disentangled class information because they are trained to reconstruct the appearance of different cells. We thus use a simple classifier (fully connected layers followed by ReLU non-linearities and a softmax) and train it from label proportions, as described in Sec. [2.1.](#page-13-1)

# **3 Experiments**

**Datasets.** We evaluate our method in a real [\[31\]](#page-21-0) and a [new synthetic](https://github.com/carolina-pacheco/LLP_multiclass_cell_detection/) [WBC holographic dataset.](https://github.com/carolina-pacheco/LLP_multiclass_cell_detection/) Fig. [3](#page-16-0) shows examples from both. The real dataset contains images from 33 donors, and it is annotated with the approximated proportions of granulocytes, lymphocytes, and monocytes  $(K = 3)$  for each donor. In this dataset, the main evaluation metric corresponds to *mean absolute error in the proportion prediction*

<span id="page-16-0"></span>**Fig. 3.** Simulated (top) versus Real (bottom) image crops. Synthetic cells (left), holograms (middle), and holograms propagated to the object plane via  $\mathcal T$  (right).

after hard assignment [\[24\]](#page-21-1), but we also use WBC concentration as a proxy for detection evaluation. The synthetic dataset was generated modeling the geometric and optical properties of the WBC subtypes. It simulates data from 500 subjects, divided in train, validation, and test sets  $(200/100/200)$ . The evaluation metrics correspond to precision, recall and f1-score for detection, as well as classification accuracy.

**LLP Classification Results.** We implement two variants of the proposed VP loss by using the squared loss (VP-L2) and the cross-entropy loss (VP-CE) as cost functions in the OT problem. We compare them to bag-level losses (MSE and KL-div [\[6\]](#page-20-4)) and pseudo-labeling approaches (Feature-Label Matching (FLM) [\[30](#page-21-3)], Prototypic Clustering (PC) [\[15](#page-20-5)], LLP-PLOT [\[18](#page-20-6)]) under the same model, optimizer, learning rate, and number of epochs (see supplement for implementation details). We first train the encoder with the reconstruction loss, and then the classifier. As usual, instance-level approaches are initialized by pretraining with the KL-div, and hyperparameters are chosen as recommended by the authors. Tables [1](#page-18-0) and [2](#page-18-1) report results for synthetic and real data, respectively. *Bag-level losses fail to learn discriminative features for synthetic data*, generating high-entropy predictions (0.841 and 0.790 for MSE and KL-div, resp.) close to the global class proportions (0.816 avg. entropy). In contrast, *VP-L2 and VP-CE lead to predictions with significantly lower entropy* (0.162 and 0.196, respectively). Similar trends hold for real data, where bag-level losses and simple pseudo-labeling methods fail to recover cells from the least populated class.

Our proposed loss, as well as LLP-PLOT [\[18](#page-20-6)], consistently outperform the KL-div baseline, with *VP-CE achieving the best instance-level accuracy* for synthetic data, and *VP-L2 the smallest donor-level proportion prediction error* for real data. LLP-PLOT performs similarly to the VP loss for the best selection of parameters, however it is very sensitive to the choice of regularizer weight  $\gamma$  and postprocessing variants. Moreover, the slow convergence of the Sinkhorn algorithm leads to a significant increase

<span id="page-17-0"></span>**Fig. 4.** Instance-level accuracy vs training time in synthetic data. Red circles represent LLP-PLOT [\[16\]](#page-20-9), for which different variants are explored (regularizer weight  $\gamma$  and postprocessing strategies)

in training time (see Fig. [4\)](#page-17-0). In contrast, the *VP-CE loss achieves similar or better performance without additional hyperparameters and reduces the training time by one order of magnitude.* Additionally, experiments confirm the *complementary nature of the VP loss with representation learning LLP methods*  $[17, 19, 30]$  $[17, 19, 30]$  $[17, 19, 30]$  $[17, 19, 30]$  $[17, 19, 30]$  (see details in the supplement).

We also compare our approach to CSC priors [\[31](#page-21-0)], the state of the art for multi-class cell detection in the real WBC dataset. It achieves 5.97 mean absolute error, which is similar to the performance of VP-L2 and VP-CE in Table [2.](#page-18-1) However, CSC priors exhibits several practical drawbacks: it requires images of purified data obtained through specialized biochemical processes; it relies on

|         | MSE    | KL-div | FLM [30] | PC [15] | LLP-PLOT [18] | VP-L2 | VP-CE        |
|---------|--------|--------|----------|---------|---------------|-------|--------------|
| Lymp.   | 0.00   | 0.00   | 0.00     | 0.00    | 65.41         | 59.35 | 66.80        |
| Mono.   | 0.00   | 0.00   | 0.00     | 0.00    | 37.20         | 6.45  | 39.23        |
| Gran.   | 100.00 | 100.00 | 100.00   | 100.00  | 82.48         | 84.80 | 83.24        |
| Average | 33.33  | 33.33  | 33.33    | 33.33   | 61.70         | 50.20 | <b>63.09</b> |

<span id="page-18-0"></span>**Table 1.** Classification accuracy in *synthetic data* for LLP approaches.

**Table 2.** Mean absolute error of proportion prediction in *real data* for LLP approaches.

<span id="page-18-1"></span>

|         | MSE  | KL-div | FLM [30] | PC [15] | LLP-PLOT [18] | VP-L2       | VP-CE |
|---------|------|--------|----------|---------|---------------|-------------|-------|
| Lymp.   | 6.94 | 6.21   | 28.91    | 40.67   | 5.84          | 5.37        | 4.71  |
| Mono.   | 8.07 | 7.96   | 8.94     | 9.09    | 5.09          | 5.09        | 5.43  |
| Gran.   | 6.69 | 9.51   | 37.85    | 43.87   | 6.37          | 6.26        | 7.11  |
| Average | 7.23 | 7.89   | 25.23    | 31.21   | 5.77          | <b>5.58</b> | 5.75  |

a large CBC database of 300K patients to model proportion priors; it requires reconstruction as preprocessing; and it does not learn a representation of the cells suitable for downstream tasks. In synthetic data, we outperform CSC priors for proportion prediction with 4.90 and 4.25 absolute error for VP-L2 and VP-CE, respectively compared to 8.95 for CSC priors.

## **Comparison to Proportion Pre-**

**diction Methods.** We compare the proposed detection-based approach to regression and classification approaches for class proportion prediction (given oracle detections) in real data. We evaluate both, architectures used in the literature for these tasks, as well as our encoder. Implementation details can be found in the supplement, while Table [3](#page-18-2) summarizes their performance. First, regression approaches have limited performance, probably due to the small amount of annotations, as they tend to predict the average proportions of the training data. Thus, the *introduction of structure in the form of detection is beneficial*. Second, our encoder out-

<span id="page-18-2"></span>**Table 3.** Mean absolute error of proportion prediction in *real data*. (Init: initialization, R: random, P: pretrained with rec. loss). Classifier and encoder are jointly optimized.

| Type                  | Encoder           | Init. | Loss      | Error |
|-----------------------|-------------------|-------|-----------|-------|
| Direct                | $ResNet-152$ [29] | R     | KL-div    | 8.5   |
| regression            | Ours              | R     | KL-div    | 7.6   |
|                       | Ours              | P     | KL-div    | 7.1   |
|                       | $C$ -FCRN $[10]$  | R     | KL-div    | 8.3   |
| Heatmap<br>regression | Ours              | R     | KL-div    | 7.9   |
|                       | Ours              | P     | KL-div    | 7.0   |
|                       | Le Net $[9]$      | R     | KL-div    | 11.5  |
|                       | Le Net $[9]$      | R     | $VP-L2$   | 8.4   |
|                       | Le Net $[9]$      | R     | $VP-CE$   | 8.2   |
| Classification        | Ours              | R     | $KI$ -div | 9.7   |
| (oracle               | Ours              | R     | $VP-L2$   | 7.3   |
| detections)           | Ours              | R     | $VP-CE$   | 7.6   |
|                       | Ours              | P     | KL-div    | 9.3   |
|                       | Ours              | P     | $VP-L2$   | 6.3   |
|                       | Ours              | P     | VP-CE     | 6.1   |

performs the ones proposed in the literature, even when both are randomly initialized and trained with the same losses. Therefore, *considering data availability in the architectural design is advantageous*. Third, initializing our encoder with weights learned by the reconstruction task boosts performance (compare pretrained (P) to randomly initialized (R) cases in Table [3\)](#page-18-2). Thus, *there is a positive contribution of using reconstruction as an auxiliary task*.

**Detection Results.** We compare the detection performance of the proposed DSD to *Cellpose* [\[22\]](#page-21-11) (a popular pretrained model for general cell segmentation); (2) a *baseline* approach (which applies local softmax and NMS directly on the absolute value of the input image); and (3) *CSC priors* [\[31](#page-21-0)], previously proposed for cell detection in lensless imaging. Detailed results can be found in the supplement, with the following high-level findings: (i) Cellpose obtains the lowest performance across all metrics, which supports the need of specialized models for lensless imaging, (2) our model outperforms the baseline and achieves the best precision, (3) CSC priors reaches the best performance in most metrics, yet at the expense of additional data labeling and computational complexity.

# **4 Conclusion**

We study LLP in a real-world application in which instances are naturally grouped into bags and (noisy) proportion annotations are easily obtained. Results show that inference of instance-level information is critical during training. We propose a self-supervised detector and an OT-based loss that achieves state-of-the-art results for weakly supervised classification and class proportion prediction, with practical advantages with respect to prior work. **Limitations.** Our detector trades recall for computational efficiency, so it might miss relevant objects. Also, it relies on the assumption that images are sparse under a known, differentiable transformation, thus it is not suitable for dense images in general.

**Acknowledgments.** This work is supported by NIH NIA #1R01AG067396 and NIH NIAID #1R21AI169363. CP was supported by Amazon under the JHU-Amazon Initiative for Interactive AI (AI2AI).

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-19-1"></span>1. Ardehaly, E.M., Culotta, A.: Co-training for demographic classification using deep learning from label proportions. In: 2017 IEEE International Conference on Data Mining Workshops (ICDMW). pp. 1017–1024. IEEE (2017)
- <span id="page-19-0"></span>2. Barnes, P., McFadden, S., Machin, S., Simson, E., et al.: The international consensus group for hematology review: suggested criteria for action following automated cbc and wbc differential analysis. Laboratory hematology: official publication of the International Society for Laboratory Hematology **11**(2), 83–90 (2005)
- <span id="page-19-2"></span>3. Barroso-Laguna, A., Riba, E., Ponsa, D., Mikolajczyk, K.: Key. net: Keypoint detection by handcrafted and learned cnn filters. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 5836–5844 (2019)

- <span id="page-20-0"></span>4. Bortsova, G., Dubost, F., Ørting, S., Katramados, I., Hogeweg, L., Thomsen, L., Wille, M., de Bruijne, M.: Deep learning from label proportions for emphysema quantification. In: Medical Image Computing and Computer Assisted Intervention– MICCAI 2018: 21st International Conference, Granada, Spain, September 16-20, 2018, Proceedings, Part II 11. pp. 768–776. Springer (2018)
- <span id="page-20-7"></span>5. Ciesla, B.: Hematology in practice. Fa Davis (2018)
- <span id="page-20-4"></span>6. Dulac-Arnold, G., Zeghidour, N., Cuturi, M., Beyer, L., Vert, J.P.: Deep multi-class learning from label proportions. arXiv preprint [arXiv:1905.12909](http://arxiv.org/abs/1905.12909) (2019)
- <span id="page-20-3"></span>7. González-Castro, V., Alaiz-Rodríguez, R., Fernández-Robles, L., Guzmán-Martínez, R., Alegre, E.: Estimating class proportions in boar semen analysis using the hellinger distance. In: Trends in Applied Intelligent Systems: 23rd International Conference on Industrial Engineering and Other Applications of Applied Intelligent Systems, IEA/AIE 2010, Cordoba, Spain, June 1-4, 2010, Proceedings, Part I 23. pp. 284–293. Springer (2010)
- <span id="page-20-13"></span>8. Guo, Y., Stein, J., Wu, G., Krishnamurthy, A.: Sau-net: A universal deep network for cell counting. In: Proceedings of the 10th ACM international conference on bioinformatics, computational biology and health informatics. pp. 299–306 (2019)
- <span id="page-20-15"></span>9. Habibzadeh, M., Krzy˙zak, A., Fevens, T.: White blood cell differential counts using convolutional neural networks for low resolution images. In: Artificial Intelligence and Soft Computing: 12th International Conference, ICAISC 2013, Zakopane, Poland, June 9-13, 2013, Proceedings, Part II 12. pp. 263–274. Springer (2013)
- <span id="page-20-14"></span>10. He, S., Minn, K.T., Solnica-Krezel, L., Anastasio, M.A., Li, H.: Deeply-supervised density regression for automatic cell counting in microscopy images. Medical Image Analysis **68**, 101892 (2021)
- <span id="page-20-1"></span>11. Hernández-González, J., Inza, I., Crisol-Ortíz, L., Guembe, M.A., Iñarra, M.J., Lozano, J.A.: Fitting the data from embryo implantation prediction: Learning from label proportions. Statistical methods in medical research **27**(4), 1056–1066 (2018)
- <span id="page-20-2"></span>12. Hernández-González, J., Inza, I., Lozano, J.A.: Learning bayesian network classifiers from label proportions. Pattern Recognition **46**(12), 3425–3440 (2013)
- <span id="page-20-11"></span>13. Hou, L., Nguyen, V., Kanevsky, A.B., Samaras, D., Kurc, T.M., Zhao, T., Gupta, R.R., Gao, Y., Chen, W., Foran, D., et al.: Sparse autoencoder for unsupervised nucleus detection and representation in histopathology images. Pattern recognition **86**, 188–200 (2019)
- <span id="page-20-12"></span>14. Kim, M.K.: Principles and techniques of digital holographic microscopy. SPIE reviews **1**(1), 018005 (2010)
- <span id="page-20-5"></span>15. La Rosa, L.E.C., Oliveira, D.A.B.: Learning from label proportions with prototypical contrastive clustering. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 36 (2), pp. 2153–2161 (2022)
- <span id="page-20-9"></span>16. Liu, J., Qi, Z., Wang, B., Tian, Y., Shi, Y.: Self-llp: Self-supervised learning from label proportions with self-ensemble. Pattern Recognition **129**, 108767 (2022)
- <span id="page-20-8"></span>17. Liu, J., Wang, B., Qi, Z., Tian, Y., Shi, Y.: Learning from label proportions with generative adversarial networks. Advances in neural information processing systems **32** (2019)
- <span id="page-20-6"></span>18. Liu, J., Wang, B., Shen, X., Qi, Z., Tian, Y.: Two-stage training for learning from label proportions. In: Zhou, Z.H. (ed.) Proceedings of the Thirtieth International Joint Conference on Artificial Intelligence, IJCAI-21. pp. 2737–2743. International Joint Conferences on Artificial Intelligence Organization (8 2021). [https://doi.org/](https://doi.org/10.24963/ijcai.2021/377) [10.24963/ijcai.2021/377,](https://doi.org/10.24963/ijcai.2021/377) [https://doi.org/10.24963/ijcai.2021/377,](https://doi.org/10.24963/ijcai.2021/377) main Track
- <span id="page-20-10"></span>19. Nandy, J., Saket, R., Jain, P., Chauhan, J., Ravindran, B., Raghuveer, A.: Domainagnostic contrastive representations for learning from label proportions. In: Pro-

ceedings of the 31st ACM International Conference on Information & Knowledge Management. pp. 1542–1551 (2022)

- <span id="page-21-5"></span>20. Rockafellar, R.T.: Conjugate duality and optimization. Society for Industrial and Applied Mathematics (1974)
- <span id="page-21-6"></span>21. Sinha, A., Lee, J., Li, S., Barbastathis, G.: Lensless computational imaging through deep learning. Optica **4**(9), 1117–1125 (2017). [https://doi.org/10.1364/OPTICA.](https://doi.org/10.1364/OPTICA.4.001117) [4.001117,](https://doi.org/10.1364/OPTICA.4.001117) <https://opg.optica.org/optica/abstract.cfm?URI=optica-4-9-1117>
- <span id="page-21-11"></span>22. Stringer, C., Wang, T., Michaelos, M., Pachitariu, M.: Cellpose: a generalist algorithm for cellular segmentation. Nature methods **18**(1), 100–106 (2021)
- <span id="page-21-9"></span>23. Tompson, J., Goroshin, R., Jain, A., LeCun, Y., Bregler, C.: Efficient object localization using convolutional networks. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 648–656 (2015)
- <span id="page-21-1"></span>24. Tsai, K.H., Lin, H.T.: Learning from label proportions with consistency regularization. In: Asian Conference on Machine Learning. pp. 513–528. PMLR (2020)
- <span id="page-21-4"></span>25. Villani, C.: Topics in optimal transportation, vol. 58. American Mathematical Soc. (2021)
- <span id="page-21-2"></span>26. Wang, B., Sun, Y., Tong, Q.: Llp-aae: Learning from label proportions with adversarial autoencoder. Neurocomputing **537**, 282–295 (2023)
- <span id="page-21-10"></span>27. Wei, S.E., Ramakrishna, V., Kanade, T., Sheikh, Y.: Convolutional pose machines. In: Proceedings of the IEEE conference on Computer Vision and Pattern Recognition. pp. 4724–4732 (2016)
- <span id="page-21-7"></span>28. Xie, W., Noble, J.A., Zisserman, A.: Microscopy cell counting and detection with fully convolutional regression networks. Computer methods in biomechanics and biomedical engineering: Imaging & Visualization **6**(3), 283–292 (2018)
- <span id="page-21-8"></span>29. Xue, Y., Ray, N., Hugh, J., Bigras, G.: Cell counting by regression using convolutional neural network. In: Computer Vision–ECCV 2016 Workshops: Amsterdam, The Netherlands, October 8-10 and 15-16, 2016, Proceedings, Part I 14. pp. 274– 290. Springer (2016)
- <span id="page-21-3"></span>30. Yang, H., Zhang, W., Lam, W.: A two-stage training framework with feature-label matching mechanism for learning from label proportions. In: Asian Conference on Machine Learning. pp. 1461–1476. PMLR (2021)
- <span id="page-21-0"></span>31. Yellin, F., Haeffele, B.D., Roth, S., Vidal, R.: Multi-cell detection and classification using a generative convolutional model. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 8953–8961 (2018)

# **Machine Learning - Foundation Models**

Image /page/23/Picture/0 description: A square button with rounded corners, colored in a light gray gradient. At the top of the button, there is a circular icon with a ribbon or bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a darker gray color.

# A Foundation Model for Brain Lesion Segmentation with Mixture of Modality Experts

Xinru Zhang<sup>1,2</sup>, Ni Ou<sup>3</sup>, Berke Doga Basaran<sup>4</sup>, Marco Visentin<sup>4</sup>, Mengyun Qiao<sup>2</sup>, Renyang Gu<sup>5</sup>, Cheng Ouyang<sup>6</sup>, Yaou Liu<sup>7</sup>, Paul M. Matthews<sup>2,8</sup>, Chuyang Ye<sup>1( $\boxtimes$ )</sup>, and Wenjia Bai<sup>2,4( $\boxtimes$ )</sup>

<sup>1</sup> School of Integrated Circuits and Electronics, Beijing Institute of Technology, Beijing, China

<EMAIL>

<sup>2</sup> Department of Brain Sciences, Imperial College London, London, UK

<sup>3</sup> School of Automation, Beijing Institute of Technology, Beijing, China <sup>4</sup> Department of Computing, Imperial College London, London, UK

<EMAIL>

<sup>5</sup> Department of Bioengineering, Imperial College London, London, UK

<sup>6</sup> Institute of Clinical Sciences, Imperial College London, London, UK

<sup>7</sup> Beijing Tiantan Hospital, Capital Medical University, Beijing, China

<sup>8</sup> UK Dementia Research Institute, Imperial College London, London, UK

Abstract. Brain lesion segmentation plays an essential role in neurological research and diagnosis. As brain lesions can be caused by various pathological alterations, different types of brain lesions tend to manifest with different characteristics on different imaging modalities.Due to this complexity, brain lesion segmentation methods are often developed in a taskspecific manner. A specific segmentation model is developed for a particular lesion type and imaging modality.However, the use of task-specific models requires predetermination of the lesion type and imaging modality, which complicates their deployment in real-world scenarios.In this work, we propose a universal foundation model for 3D brain lesion segmentation, which can automatically segment different types of brain lesions for input data of various imaging modalities.We formulate a novel *Mixture of Modality Experts* (MoME) framework with multiple expert networks attending to different imaging modalities. A hierarchical gating network combines the expert predictions and fosters expertise collaboration. Furthermore, we introduce a curriculum learning strategy during training to avoid the degeneration of each expert network and preserve their specialisation. We evaluated the proposed method on nine brain lesion datasets, encompassing five imaging modalities and eight lesion types.The results show that our model outperforms state-of-the-art universal models and provides promising generalisation to unseen datasets.

Work conducted as a visiting PhD student at Imperial College London, under the joint supervision of corresponding authors.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 379–389, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_36)\_36

Keywords: Foundation Model · Brain Lesion Segmentation · Mixture of Experts

# **1 Introduction**

Brain lesion segmentation plays an essential role in neurological research and diagnosis by offering a quantitative description of pathologies  $[14,34]$  $[14,34]$  $[14,34]$ , and deep learning has greatly advanced the progress of brain lesion segmentation in recent years [\[3,](#page-31-0)[9](#page-32-1)[,31](#page-33-1)]. Since brain lesions tend to manifest with different characteristics due to various patient-dependent pathological alterations, the segmentation of different types of brain lesions requires the use of different *magnetic resonance imaging* (MRI) modalities [\[26](#page-32-2)[,27](#page-33-2),[32\]](#page-33-3), which increases the segmentation complexity. In the traditional paradigm (Fig. [1a](#page-25-0)), separate task-specific models are trained to segment specific types of brain lesions on the corresponding MRI modalities that are sensitive to the lesions [\[34\]](#page-33-0). This paradigm necessitates the development of numerous models for clinical analysis tasks [\[10](#page-32-3),[28\]](#page-33-4). Clinicians are compelled to manually choose the optimal model for each patient-specific MRI scan based on predetermined brain lesion type criteria. This process hinders practical deployment of the *artificial intelligence* (AI) models.

Contrary to this traditional paradigm, we propose a task-agnostic universal foundational model (Fig. [1b](#page-25-0)), which is capable of automatically segmenting various types of brain lesions based on inputs of different MRI modalities.

Foundation models have gained popularity in medical image segmentation, thanks to the success of the *Segment Anything Model* (SAM) [\[15](#page-32-4)] and its variants  $[11, 20, 29]$  $[11, 20, 29]$  $[11, 20, 29]$  $[11, 20, 29]$ , as well as other innovative foundation models  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  $[4, 10, 28, 36]$  specifically developed for medical imaging. Earlier works aim to develop a universal model across all modalities, organs, and tasks. However, due to the complexity of medical imaging data and tasks, the segmentation accuracy of such a universal model is often limited [\[33](#page-33-7)]. Thus, foundation models focusing on narrower scopes are explored, which are designed for a particular modality or a particular organ/task [\[33](#page-33-7)]. For example, several models are proposed specifically for *computed tomography* (CT) image segmentation [\[19](#page-32-7),[28,](#page-33-4)[30\]](#page-33-8), showing promising results for anatomical structure and large tumour segmentation.

Despite these progresses, most existing foundation models may not be directly applicable to brain lesion segmentation. Most noticeably, universal brain lesion segmentation requires the capability of handling the challenge of input modality diversity, which is not addressed by many of the existing models. SAM-inspired methods, such as SAM-Med3D [\[29\]](#page-33-5) and MedSAM [\[20](#page-32-6)], require human-given prompts, which can be impractical for brain lesion segmentation as the lesions can be scattered across the brain. Neuralizer [\[9\]](#page-32-1) demonstrates the potential of a foundation model for brain imaging that generalises to new tasks by learning transferable features from a context set to the input image. It is evaluated on 2D coronal slices, which might limit its use on 3D brain lesion segmentation task. Hermes [\[10\]](#page-32-3) is proposed with the awareness of the modality diversity, where modality information is embedded as a prior in model training.

Image /page/25/Figure/1 description: This figure illustrates two paradigms for medical image analysis: the traditional paradigm (a) and the foundation model paradigm (b). In the traditional paradigm, separate task-specific models are used for different conditions like tumors (T2), stroke (DWI), and multiple sclerosis (T1), with each model taking specific MRI sequences as input and producing a segmented output. The foundation model paradigm (b) proposes a single model that can handle all these tasks, taking various MRI sequences as input and producing corresponding segmented outputs. Panel (c) details a component of the foundation model, showing a gating network that directs input to multiple 'expert' networks. These experts process the input, and their outputs are combined (summed) to produce a final segmentation, exemplified by a tumor segmentation in pink on a black background.

<span id="page-25-0"></span>Fig. 1. Different paradigms for brain lesion segmentation: a) the traditional paradigm that trains multiple task-specific models; b) the foundation model paradigm that trains a single universal model for multiple tasks; c) the proposed *mixture of modality experts* (MoME) framework for constructing the foundation model.

However, it does not fully explore the relationship between different modalities. The development of a universal foundational model for brain lesion segmentation with diverse input modalities is still an open problem.

In this study, we propose a *Mixture of Modality Experts* (MoME) model, a foundation model for brain lesion segmentation capable of handling diverse imaging modalities and accommodating various types of brain lesions. As shown in Fig. [1c](#page-25-0), inspired by *Mixture of Experts* (MoE) [\[5](#page-31-2)[,23](#page-32-8),[37\]](#page-33-9), MoME enhances the model capacity by assembling a team of experts, where each expert is specialised to handle a particular imaging modality. A hierarchical gating network is developed to combine the expert predictions and foster collaborative expertise exploration. Moreover, to avoid the degeneration of each expert network and preserve their specialisation, we introduce a curriculum learning strategy for training the MoME model. For evaluation, we conducted comphrehensive experiments on nine brain imaging datasets comprising a total number of 6,585 annotated 3D images, with five MRI modalities and eight lesion types. The results show that MoME outperforms competing methods in multiple aspects and provides improved generalisation to unseen datasets. The codes of our method are available at [https://github.com/ZhangxinruBIT/MoME.](https://github.com/ZhangxinruBIT/MoME)

# **2 Methods**

## **2.1 Method Overview**

The Architecture of MoME. Features two major components, namely the expert networks and the gating network. Each expert network is adept in a specific domain of imaging modality, contributing to expert specialisation. The gating network functions as a decision-maker and ensembles the expert networks by dynamically adjusting the weights of their outputs with hierarchical guidance. Regarding the training of MoME, an anti-degeneration strategy based on curriculum learning is developed, where model training gradually transitions from specialising the role of each expert to determining the entire model through collaboration.

## **2.2 Detailed Design**

**Expert Specialisation.** Suppose there are N imaging modalities  $\{\mathcal{M}_i\}_{i=1}^N$ of interest, where  $\mathcal{M}_i$  represents the *i*-th modality. To address the problem of modality diversity in brain lesion segmentation, in MoME, N expert networks  ${E_i}_{i=1}^N$  are developed, where  $E_i$  denotes the *i*-th expert. Each expert  $E_i$  is expected to specialise in a specific modality  $\mathcal{M}_i$ . Without losing generality, we assume that each expert network follows a multi-resolution encoderdecoder architecture, which is common for medical image segmentation such as in U-Net [\[13\]](#page-32-9). We focus on the five most widely used brain imaging modalities, including T1-weighted, T2-weighted, T1 contrast-enhanced (T1ce), FLAIR, and *diffusion weighted imaging* (DWI), and thus  $N = 5$ . Note that the experts here are built for 3D brain image segmentation, distinguishing them from sparse MoE architectures for 2D natural images where a higher number of experts can be afforded and routing is employed for expert selection [\[24,](#page-32-10)[35](#page-33-10)]. Due to the smaller number of experts, we do not explore expert routing in this work.

Hierarchical Gating Network. Following the specialisation of expert networks, we propose a gating network G that facilitates the collaboration and conducts voxel-wise weighted aggregation among the expert outputs. Note that different imaging modalities can be correlated and provide complementary information. For example, FLAIR appears similar to T2 but with fluid being attenuated. They may both contribute to lesion segmentation with different features, and the outputs of an expert that is not specialized in the input modality may still provide complementary useful information. To enhance collaboration, the gating network is hierarchical and leverages multi-resolution information jointly.

Mathematically, given an input image  $\mathbf{X}$ , each expert  $E_i$  generates outputs at multiple resolution levels on the decoder side, denoted by  $E_i(\mathbf{X}) = {\mathbf{e}_{i}^{l}}_{l=1}^{L}$ , where  $e^{l}$  is the output of E<sub>i</sub> at the *l*<sub>i</sub>th resolution level and *L* indicates the where  $\mathbf{e}_i^l$  is the output of  $E_i$  at the *l*-th resolution level and *L* indicates the number of resolution levels. The gating network *G* generates a set of hierarchical number of resolution levels. The gating network G generates a set of hierarchical voxel-wise weight maps at different resolution levels for each expert, denoted as  $\{g_i^l\}_{l=1}^L$ . Combining the expert networks and the gating network, the final output **O** is formulated as output **O** is formulated as

$$
\mathbf{O} = \left\{ \mathbf{o}^{l} \right\}_{l=1}^{L} \quad \text{with} \quad \mathbf{o}^{l} = \sum_{i=1}^{N} \mathbf{e}_{i}^{l} \odot \mathbf{g}_{i}^{l}, \tag{1}
$$

where  $\mathbf{o}^l$  is the aggregated output of all experts at the *l*-th level and  $\odot$  repre-<br>sents voyel-wise multiplication. The segmentation at the *l*-th level is obtained by sents voxel-wise multiplication. The segmentation at the l-th level is obtained by applying the softmax operator to  $\mathbf{o}^l$ . During training, deep supervision  $[13,17]$  $[13,17]$  $[13,17]$ <br>is employed to guide the segmentation at multiple resolution levels. At the inferis employed to guide the segmentation at multiple resolution levels. At the inference stage, only the highest resolution level determines the final segmentation result [\[13,](#page-32-9)[17\]](#page-32-11).

Curriculum Learning to Alleviate Expert Degeneration. Although it is feasible to simply jointly train the experts and gating network, this may lead to expert degeneration, where each expert no longer focuses on a specific modality and their activation becomes imbalanced  $[6,7]$  $[6,7]$ , reducing the capability of the whole model to handle diverse modalities. To address this issue, we propose a curriculum learning strategy for model training, which gradually transitions from specialising the experts to determining the whole model, including gating-based expert collaboration and expert refinement.

Specifically, the curriculum learning is achieved with a dynamic loss  $\mathcal{L}_{\text{cl}}$  that comprises the specialisation loss  $\mathcal{L}_i$  and the collaboration loss  $\mathcal{L}_{\text{MoME}}$ :

<span id="page-27-0"></span>
$$
\mathcal{L}_{\text{cl}} = f_{\text{epoch}} \cdot \sum_{i=1}^{N} \mathbb{1}_{\mathbf{X} \in \mathcal{M}_i} \cdot \mathcal{L}_i + (1 - f_{\text{epoch}}) \cdot \mathcal{L}_{\text{MoME}}.
$$
 (2)

Here,  $\mathbf{1}_{\mathbf{X} \in \mathcal{M}}$  is an indicator function that is one when the input image **X** belongs to the modality  $\mathcal{M}_i$  and zero otherwise, and  $f_{\text{epoch}}$  is a dynamic weight that changes with the epoch iteration.  $f_{\text{epoch}}$  is designed as  $f_{\text{epoch}} =$  $(1 - epoch_{\text{current}}/epoch_{\text{total}})^2$ , where  $epoch_{\text{current}}$  and  $epoch_{\text{total}}$  are the current epoch and total number of epochs, respectively. In this way, during the early training stage,  $f_{\text{epoch}}$  is greater, which emphasises the loss  $\mathcal{L}_i$  ( $i \in \{1, ..., N\}$ ) for each expert  $E_i$  that matches the modality of **X**. During the later training stage,  $f_{\text{epoch}}$  decreases and  $\mathcal{L}_{\text{MoME}}$  becomes gradually more emphasised.

 $\mathcal{L}_i$  is designed with deep supervision at each resolution level as

$$
\mathcal{L}_i = \sum_{l=1}^L k^l \cdot \mathcal{L}(\sigma(\mathbf{e}_i^l), d^l(\mathbf{Y})),
$$
\n(3)

where  $k_l$  is a weight for the *l*-th level,  $\sigma(\cdot)$  is the softmax activation,  $d^l(\cdot)$  is a down-sampling operator that matches the resolution of the annotation **Y** with that of the *l*-th level, and  $\mathcal{L}(\cdot, \cdot)$  is the combination of the soft Dice loss and cross-entropy loss [\[13](#page-32-9)].  $\mathcal{L}_{\text{MoME}}$  is formulated with the collaboration between experts as

<span id="page-27-1"></span>
$$
\mathcal{L}_{\text{MoME}} = \sum_{l=1}^{L} k^l \cdot \mathcal{L}(\sigma(\mathbf{o}^l), d^l(\mathbf{Y})).
$$
\n(4)

### **2.3 Implementation Details**

We implement each expert network and the gating network with the nnU-Net architecture [\[13\]](#page-32-9), which is a strong segmentation backbone as shown in [\[13](#page-32-9),[28\]](#page-33-4). The input to the gating network is a combination of raw images and shallow features extracted from the first encoder layers of the experts.

As each expert network is expected to specialise in one imaging modality, the expert networks are first trained separately to acquire the modality knowledge. Then, the gating network is trained jointly with the fine-tuning of the expert networks based on the dynamic curriculum learning loss Eq. [2.](#page-27-0)

All the hyperparameters (including  ${k_l}_{l=1}^L$ ) follow the default setting in nnU-Net [\[13\]](#page-32-9), except that 1) a low learning rate of  $1e^{-3}$  is set for the dynamic curriculum learning loss [\[25](#page-32-12)] and 2) the number of training epochs is set to 800, a sufficient number for convergence.

# **3 Experiments**

## **3.1 Dataset and Task Description**

We curated six publicly available brain lesion segmentation datasets [\(MSSEG](https://portal.fli-iam.irisa.fr/msseg-challenge/) [\[8\]](#page-32-13), [OASIS](https://www.oasis-brains.org/) [\[22](#page-32-14)], [ISLES2022](https://www.isles-challenge.org/) [\[12\]](#page-32-15), [BraTS2021](http://www.brainTumoursegmentation.org/) [\[2](#page-31-5)], [WMH2017](https://wmh.isi.uu.nl/#_Toc122355653) [\[16](#page-32-16)], and ATLAS2.0 [\[18\]](#page-32-17)) and three in-house datasets (Tumour1, Tumour2, and WMHsMix), amounting to a total number of 6,585 3D images with brain lesion annotations. The dataset detail is reported in Supple. Table II. All images were registered to the MNI152 template using [ANTs](https://stnava.github.io/ANTs/) [\[1\]](#page-31-6). Then, brain masks were applied to the images for skull removal. Cropping was performed so that the size of all images was standardised to  $160 \times 196 \times 160$  with a voxel size of  $1 \times 1 \times 1$  mm<sup>3</sup>. Each of the six public datasets was split into training and test sets. The MoME model was trained on the combined training sets from all six public datasets. It was then evaluated on the test sets from the public datasets (seen datasets) and the three in-house datasets (unseen datasets), which were never used for training and allowed the assessment of the generalisation of MoME.

## **3.2 Competing Task-Specific and Foundation Models**

MoME was compared with the following task-specific and foundation models.

Task-Specific nnU-Nets: The nine datasets cover a variety of brain lesions and modalities. We regard the segmentation of each modality for each dataset as a distinct task [\[4](#page-31-1)], resulting in a total number of 17 tasks (14 tasks for seen datasets and 3 tasks for unseen datasets; task IDs listed in Supple. Table I). Thus, 17 supervised task-specific nnU-Net models were trained.

nnU-Net: A single nnU-Net [\[13\]](#page-32-9) was trained with the combined training sets from all public datasets. It can be viewed as a baseline foundation model.

Multi-talent [\[28\]](#page-33-4): Multi-Talent demonstrates superior performance in handling conflicting class definitions for multi-dataset training and shows promising lesion segmentation results. We trained it with the combined training sets.

Hermes [\[10\]](#page-32-3): Hermes is a foundation model that injects context prior knowledge (e.g. anatomical region and imaging modality) into the segmentation network to address the challenges of data and modality diversities. We adapted Hermes for our task and incorporated the imaging modality information as prior knowledge. Hermes was also trained with the combined training sets.

SAM-Med3D [\[29\]](#page-33-5): This is a SAM-based [\[15](#page-32-4)] model trained on a large-scale 3D medical image dataset. We fine-tuned [SAM-Med3D](https://drive.google.com/file/d/1MuqYRQKIZb4YPtEraK8zTKKpp-dUQIR9/view) with the combined training sets and provided a 10-point prompt [\[29\]](#page-33-5) during the inference.

All models were trained for 800 epochs on Nvidia A100 GPUs. We used the default hyperparameters for the transformer-based SAM-Med3D. All the other models were based on nnU-Net and used its default hyperparameters.

### **3.3 Results**

Performance on Seen Datasets. Table [1](#page-30-0) compares the average Dice score of MoME with the competing models and reports the overall GPU memory usage for training each model. To mitigate the bias caused by the varying numbers of images in different test sets, we report the Dice score with different levels of averaging: across the 14 seen tasks (task-level), across 6 seen datasets (dataset-level), and across 1,230 test images (image-level). MoME outperforms the other foundation models, for example, achieving 2% to 4% higher Dice scores than nnU-Net, Multi-Talent, and Hermes. It outperforms SAM-Med3D by a larger margin of 7% to 15%. This is possibly because SAM-Med3D requires point prompts to indicate a foreground region, which may not be suitable for brain lesion segmentation, where lesions can be numerous, small, and scattered across the brain, such as *white matter hyperintensity* (WMH) lesions [\[3](#page-31-0),[16\]](#page-32-16). The performance of MoME is comparable to task-specific nnU-Nets. However, the latter requires the training of 14 separate models, and its total amount of GPU memory consumption is about three times that of MoME.

A more detailed analysis of the results is shown in Fig. [2.](#page-30-1) Figure [2a](#page-30-1) presents a comprehensive comparison between the foundation models from 8 more perspectives, reporting the average Dice score for different modalities and different lesion types. For 7 out of the 8 perspectives, MoME (blue curve) outperforms the other foundation models (SAM-Med3D is not shown due to relatively lower performance but reported in Supple. Table I). Figure [2b](#page-30-1) analyses the latent features obtained at the bottleneck of the gating network in MoME and the single nnU-Net using t-SNE plots [\[21\]](#page-32-18). It shows that MoME achieves a more discriminative clustering for images of different modalities and lesion types compared to the nnU-Net, which may be relevant to its improved performance.

Generalisation to Unseen Datasets. Table [2](#page-30-2) reports the segmentation performance on the three unseen in-house datasets for evaluating model generalisability. Multi-Talent is not included as it cannot be directly applied to unseen datasets. Again, MoME outperforms other competing foundation models. On two unseen datasets (Tumour1 and Tumour2), it even exceeds the task-specific nnU-Nets, which can be considered the upper-bound benchmark as they had access to the training data from the unseen datasets. On the other unseen dataset (WMHsMix), MoME still performs relatively well and achieves an average Dice score of 0.7015, where the dataset contains diverse WMH lesions of five diseases (see Supple. Table II).

Ablation Studies. Ablation studies were performed on the seen and unseen datasets to verify the contributions of the designs in MoME with results pre-sented in Table [3.](#page-30-3) For the modality experts,  $\mathbf{X}/\mathbf{V}$  denotes randomly initialised experts versus specialised experts based on the imaging modality; for the hierarchical gating,  $\mathbf{X}/\mathbf{V}$  denotes the basic gating without hierarchical structure versus our hierarchical gating; and for the curriculum learning,  $\mathbf{x}/\mathbf{v}$  denotes <span id="page-30-0"></span>Table 1. Average Dice score of MoME and the competing methods on seen datasets. The best results among the foundation models are highlighted in bold. The GPU memory usage for model training is also displayed.

| Dice                  | Foundation Model |           |              |        |               | Task-specific nnU-Net |
|-----------------------|------------------|-----------|--------------|--------|---------------|-----------------------|
|                       | nnU-Net          | SAM-Med3D | Multi-talent | Hermes | MoME          |                       |
| Image-level           | 0.8007           | 0.7322    | 0.7948       | 0.8017 | <b>0.8204</b> | 0.8202                |
| Task-level            | 0.6654           | 0.5249    | 0.6825       | 0.6720 | <b>0.7026</b> | 0.7099                |
| Dataset-level         | 0.6561           | 0.5083    | 0.6681       | 0.6603 | <b>0.6938</b> | 0.6950                |
| GPU memory usage (GB) | 7.5              | 32.5      | 11.9         | 7.5    | 38.2          | 104.4                 |

<span id="page-30-2"></span>Table 2. Average Dice scores on unseen datasets. The top scores are highlighted in bold.

| Datasets            | Foundation Model |           |        |               | Task-specific nnU-Nets |
|---------------------|------------------|-----------|--------|---------------|------------------------|
|                     | nnU-Net          | SAM-Med3D | Hermes | MoME          |                        |
| Tumour1             | 0.8358           | 0.8339    | 0.8446 | <b>0.8545</b> | 0.8518                 |
| Tumour2             | 0.7959           | 0.8146    | 0.7968 | <b>0.8293</b> | 0.8157                 |
| WMHsMix             | 0.7032           | 0.5479    | 0.6968 | 0.7015        | <b>0.7508</b>          |
| Modality experts    | ✔                | ✔         | ✔      | x             |                        |
| Hierarchical gating | ✔                | ✔         | x      | x             |                        |
| Curriculum learning | ✔                | x         | x      | x             |                        |
| Seen<br>Datasets    | Image-level      | 0.8204    | 0.8142 | 0.8117        | 0.8027                 |
|                     | Task-level       | 0.7026    | 0.6969 | 0.6878        | 0.6529                 |
|                     | Dataset-level    | 0.6938    | 0.6852 | 0.6777        | 0.6488                 |
|                     | Unseen Datasets  | 0.7951    | 0.7889 | 0.7912        | 0.7814                 |

<span id="page-30-3"></span>Table 3. Ablation studies of MoME. The best scores are in bold.

<span id="page-30-1"></span>Fig. 2. More detailed analysis of the MoME result on seen datasets. a) A radar chart that compares the average Dice score of foundation models from the perspectives of different modalities and lesion types. b) t-SNE plots of latent spaces for nnU-Net and MoME, where each dot represents a brain image.

the application of Eq.  $(4)$  versus Eq.  $(2)$  during model training. When the three contributions are sequentially removed, the segmentation performance tends to decrease gradually, which confirms the benefits of these contributions.

Experts Degeneration. We further investigated the issue of expert degeneration by comparing the expert output, weighted by gating, without and with the

proposed curriculum learning strategy for avoiding degeneration. The detailed results are given in Supple. Figure I. The figure shows that without the curriculum learning strategy, certain experts can become inactive, losing their modalityspecific expertise. The proposed curriculum learning strategy ensures that the experts remain sensitive to modality-specific information.

# **4 Conclusion**

We have developed a novel universal foundation model MoME for brain lesion segmentation, where a mixture of modality experts is incorporated to address the challenge of modality diversity. MoME enables collaboration between experts with hierarchical gating and mitigates expert degeneration with a curriculum learning strategy. Extensive experimental results on nine public and in-house datasets and 17 tasks show that it outperforms competing foundation models across different modalities and lesion types. It is also more efficient than taskspecific models, providing a flexible way for model deployment.

Acknowledgements. C. Ye is supported by the Beijing Municipal Natural Science Foundation (7242273) and the Fundamental Research Funds for the Central Universities (2024CX06040). W. Bai is co-funded by EPSRC DeepGeM Grant (EP/W01842X/1) and NIHR Imperial Biomedical Research Centre (BRC). The views expressed are those of the authors and not necessarily those of the NIHR or the Department of Health and Social Care.

Disclosure of Interests. The authors declare no competing interests.

## **References**

- <span id="page-31-6"></span>1. Avants, B.B., Tustison, N.J., Song, G., et al.: A reproducible evaluation of ANTs similarity metric performance in brain image registration. NeuroImage  $54(3)$ (2011)
- <span id="page-31-5"></span>2. Baid, U., Ghodasara, S., Bilello, M., et al.: The RSNA-ASNR-MICCAI BraTS 2021 benchmark on brain tumor segmentation and radiogenomic classification. arXiv preprint [arXiv:2107.02314](http://arxiv.org/abs/2107.02314) (2021)
- <span id="page-31-0"></span>3. Basaran, B.D., Zhang, W., Qiao, M., et al.: LesionMix: A lesion-level data augmentation method for medical image segmentation. arXiv preprint [arXiv:2308.09026](http://arxiv.org/abs/2308.09026) (2023)
- <span id="page-31-1"></span>4. Butoi, V.I., Gonzalez Ortiz, J.J., Ma, T., et al.: UniverSeg: Universal medical image segmentation. International Conference on Computer Vision (2023)
- <span id="page-31-2"></span>5. Chen, T., Chen, X., Du, X., et al.: AdaMV-MoE: Adaptive multi-task vision mixture-of-experts. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (2023)
- <span id="page-31-3"></span>6. Chen, Z., Shen, Y., Ding, M., et al.: Mod-Squad: Designing mixtures of experts as modular multi-task learners. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (2023)
- <span id="page-31-4"></span>7. Chi, Z., Dong, L., Huang, S., et al.: On the representation collapse of sparse mixture of experts. Advances in Neural Information Processing Systems 35 (2022)

- <span id="page-32-13"></span>8. Commowick, O., Istace, A., Kain, M., et al.: Objective evaluation of multiple sclerosis lesion segmentation using a data management and processing infrastructure. Scientific Reports 8(1) (2018)
- <span id="page-32-1"></span>9. Czolbe, S., Dalca, A.V.: Neuralizer: General neuroimage analysis without retraining. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (2023)
- <span id="page-32-3"></span>10. Gao, Y., Li, Z., Liu, D., et al.: Training like a medical resident: Universal medical image segmentation via context prior learning. arXiv preprint [arXiv:2306.02416](http://arxiv.org/abs/2306.02416) (2023)
- <span id="page-32-5"></span>11. Gong, S., Zhong, Y., Ma, W., et al.: 3DSAM-adapter: Holistic adaptation of SAM from 2D to 3D for promptable medical image segmentation. arXiv preprint [arXiv:2306.13465](http://arxiv.org/abs/2306.13465) (2023)
- <span id="page-32-15"></span>12. Hernandez Petzsche, M.R., de la Rosa, E., Hanning, U., et al.: ISLES 2022: A multicenter magnetic resonance imaging stroke lesion segmentation dataset. Scientific Data 9(1) (2022)
- <span id="page-32-9"></span>13. Isensee, F., Jaeger, P.F., Kohl, S.A., et al.: nnU-Net: a self-configuring method for deep learning-based biomedical image segmentation. Nature Methods 18(2) (2021)
- <span id="page-32-0"></span>14. Kamnitsas, K., Ledig, C., Newcombe, V.F., et al.: Efficient multi-scale 3D CNN with fully connected CRF for accurate brain lesion segmentation. Medical Image Analysis 36 (2017)
- <span id="page-32-4"></span>15. Kirillov, A., Mintun, E., Ravi, N., et al.: Segment anything. ICCV (2023)
- <span id="page-32-16"></span>16. Kuijf, H.J., Biesbroek, J.M., De Bresser, J., et al.: Standardized assessment of automatic segmentation of white matter hyperintensities and results of the WMH segmentation challenge. IEEE Transactions on Medical Imaging 38(11) (2019)
- <span id="page-32-11"></span>17. Lee, C.Y., Xie, S., Gallagher, P., et al.: Deeply-supervised nets. In: Artificial Intelligence and Statistics (2015)
- <span id="page-32-17"></span>18. Liew, S.L., Anglin, J.M., Banks, N.W., et al.: A large, open source dataset of stroke anatomical brain images and manual lesion segmentations. Scientific Data 5(1) (2018)
- <span id="page-32-7"></span>19. Liu, J., Zhang, Y., Chen, J.N., et al.: Clip-driven universal model for organ segmentation and tumor detection. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (2023)
- <span id="page-32-6"></span>20. Ma, J., He, Y., Li, F., et al.: Segment anything in medical images. Nature Communications  $15(1)$  (2024)
- <span id="page-32-18"></span>21. Van der Maaten, L., Hinton, G.: Visualizing data using t-SNE. Journal of Machine Learning Research 9(11) (2008)
- <span id="page-32-14"></span>22. Marcus, D.S., Fotenos, A.F., Csernansky, J.G., et al.: Open access series of imaging studies: longitudinal MRI data in nondemented and demented older adults. Journal of Cognitive Neuroscience 22(12) (2010)
- <span id="page-32-8"></span>23. Ou, Y., Yuan, Y., Huang, X., et al.: Patcher: Patch transformers with mixture of experts for precise medical image segmentation. In: MICCAI (2022)
- <span id="page-32-10"></span>24. Puigcerver, J., Riquelme, C., Mustafa, B., et al.: From sparse to soft mixtures of experts. arXiv preprint [arXiv:2308.00951](http://arxiv.org/abs/2308.00951) (2023)
- <span id="page-32-12"></span>25. Rajbhandari, S., Li, C., Yao, Z., et al.: DeepSpeed-MoE: Advancing mixture-ofexperts inference and training to power next-generation AI scale. In: International Conference on Machine Learning (2022)
- <span id="page-32-2"></span>26. Schmidt, G.P., Wintersperger, B., Graser, A., et al.: High-resolution whole-body magnetic resonance imaging applications at 1.5 and 3 Tesla: a comparative study. Investigative Radiology 42(6) (2007)

- <span id="page-33-2"></span>27. Shah, A.H., Snelling, B., Bregy, A., et al.: Discriminating radiation necrosis from tumor progression in gliomas: a systematic review what is the best imaging modality? Journal of Neuro-Oncology 112 (2013)
- <span id="page-33-4"></span>28. Ulrich, C., Isensee, F., Wald, T., et al.: MultiTalent: A multi-dataset approach to medical image segmentation. In: MICCAI (2023)
- <span id="page-33-5"></span>29. Wang, H., Guo, S., Ye, J., et al.: SAM-Med3D. arXiv preprint [arXiv:2310.15161](http://arxiv.org/abs/2310.15161) (2023)
- <span id="page-33-8"></span>30. Wasserthal, J., Breit, H.C., Meyer, M.T., et al.: TotalSegmentator: Robust segmentation of 104 anatomic structures in CT images. Radiology: Artificial Intelligence 5(5) (2023)
- <span id="page-33-1"></span>31. Wood, D.A., Kafiabadi, S., Al Busaidi, A., et al.: Deep learning models for triaging hospital head MRI examinations. Medical Image Analysis 78 (2022)
- <span id="page-33-3"></span>32. Wu, O., Christensen, S., Hjort, N., et al.: Characterizing physiological heterogeneity of infarction risk in acute human ischaemic stroke using MRI. Brain 129(9) (2006)
- <span id="page-33-7"></span>33. Zhang, S., Metaxas, D.: On the challenges and perspectives of foundation models for medical image analysis. Medical Image Analysis 91 (2024)
- <span id="page-33-0"></span>34. Zhang, X., Liu, C., Ou, N., et al.: CarveMix: a simple data augmentation method for brain lesion segmentation. NeuroImage 271 (2023)
- <span id="page-33-10"></span>35. Zhang, Y., Cai, R., Chen, T., et al.: Robust mixture-of-expert training for convolutional neural networks. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (2023)
- <span id="page-33-6"></span>36. Zhou, Y., Chia, M.A., Wagner, S.K., et al.: A foundation model for generalizable disease detection from retinal images. Nature 622(7981) (2023)
- <span id="page-33-9"></span>37. Zhu, J., Zhu, X., Wang, W., et al.: Uni-perceiver-MoE: Learning sparse generalist models with conditional MoEs. Advances in Neural Information Processing Systems 35 (2022)

# A New Non-invasive AI-Based Diagnostic System for Automated Diagnosis of Acute Renal Rejection in Kidney Transplantation: Analysis of ADC Maps Extracted from Matched 3D Iso-Regions of the Transplanted Kidney

 $\rm Ibrahim$  Abdelhalim $^{1(\boxtimes)}$  , Mohamed Abou El-Ghar $^2,$  Amy Dwyer $^3,$ Rosemary Ouseph<sup>3</sup>, Sohail Contractor<sup>4</sup>, and Ayman El-Baz<sup>1</sup>

<sup>1</sup> Department of Bioengineering, University of Louisville, Louisville, KY, USA <EMAIL>

<sup>2</sup> Radiology Department, Urology and Nephrology Center, Mansoura, Egypt

<sup>3</sup> Kidney Transplantation-Kidney Disease Center, University of Louisville, Louisville, KY, USA

<sup>4</sup> Department of Radiology, University of Louisville, Louisville, KY, USA

Abstract. Acute allograft rejection poses a significant challenge in kidney transplantation, the primary remedy for end-stage renal disease. Timely detection is crucial for intervention and graft preservation. A notable obstacle involves ensuring consistency across Diffusion Weighted Magnetic Resonance Imaging (DW-MRI) scanning protocols at various Tesla levels. To tackle this, we propose a novel, non-invasive framework for automated diagnosis of acute renal rejection using DW-MRI. Our method comprises several key steps: Initially, we register the segmented kidney across different scanners, aligning them from the cortex to the medulla. Afterwards, the Apparent Diffusion Coefficient (ADC) is estimated for the segmented kidney. Then, the ADC maps are partitioned into a 3D iso-surface from the cortex to the medulla using the fast-marching level sets method. Next, the Cumulative Distribution Function (CDF) of the ADC for each iso-surface is computed, and Spearman correlation is applied to these CDFs. Finally, we introduce a Transformer-based Correlations to Classes Converter (T3C) model to leverage these correlations for distinguishing between normal and acutely rejected transplants. Evaluation on a cohort of 94 subjects (40 with acute renal rejection and 54 control subjects) yields promising results, with a mean accuracy of 98.723%, a mean sensitivity of 97%, and a mean specificity of 100%, employing a leave-one-subject testing approach. These findings underscore the effectiveness and robustness of our proposed framework.

Keywords: Renal Rejection · DW-MRI · Tesla Levels · Transformer

# **1 Introduction**

Renal transplantation, a primary procedure in organ transplantation, is the preferred treatment for end-stage renal failure [\[2](#page-41-0)]. Despite progress, post-transplant complications and increased rates of renal insufficiency pose challenges to its effectiveness [\[7](#page-41-1)]. Therefore, timely and accurate diagnosis and treatment are essential for the survival of transplanted kidneys. Emerging imaging techniques like Magnetic Resonance Imaging (MRI) offer noninvasive assessment of transplanted kidneys [\[7](#page-41-1)]. The apparent diffusion coefficient (ADC) from Diffusion-Weighted Imaging (DWI) is a reliable biomarker for renal allograft function [\[1\]](#page-41-2). Notably, patients with higher creatinine clearance typically have higher ADC values [\[6\]](#page-41-3). It has been suggested that the use of Machine Learning (ML) with high-dimensional radiomics features of MRI can provide promising performance advantages, including improved diagnostic, prognostic, and predictive accuracy, which may lead to a rapid rise in the potential use of ML in renal imaging [\[4](#page-41-4),[5\]](#page-41-5). Moreover, Deep Learning (DL) techniques have garnered attention in this field. For instance, Zhi et al. [\[9](#page-42-0)] employed a Convolutional Neural Network (CNN) to analyze clinical and MRI data from 252 kidney-allografted patients, achieving a macro Area Under the Curve of 76.2% in distinguishing allograft conditions and predicting impaired graft function. Furthermore, Shehata et al. [\[8\]](#page-42-1) developed a DL-based classifier using DWI, demonstrating a prediction accuracy of 97% for acute renal transplant rejection. In summary, both ML and DL models demonstrate proficiency in predicting acute renal transplant rejection. However, existing research overlooks the importance of correlations between the iso-surfaces of the kidney itself. Furthermore, significant challenges exist, including ensuring consistency across Diffusion Weighted Magnetic Resonance Imaging (DW-MRI) scanning protocols at various Tesla levels (e.g.,  $1.5$  T  $(T)$  and  $3T$ ) and addressing the shape deformation that may occur in the transplanted kidney (graft), leading to alterations in MRI diffusion signals. Additionally, models such as transformers, which hold promise for delivering more nuanced outcomes, have not been extensively utilized in this context. Moreover, there is a need for an expanded examination of different classifiers and their respective trade-offs. Also, renal transplant care is complex. Achieving a complete and accurate diagnosis during patient follow-up is an equally daunting task, fraught with challenges. The dynamic nature of graft health necessitates not just a one-time assessment but a series of exhaustive examinations using diverse MRI scanning protocols. To address these challenges, this study introduces a transformer-based framework for classifying acute renal transplant rejection using DW-MRI data obtained through various scanning protocols (specifically, 1.5T and 3T). It streamlines the difficult process of examining diverse MRI scanning protocols, thereby enhancing the accuracy of follow-up diagnoses. The framework involves several key steps. Firstly, the segmented kidney is registered with a reference image by applying a 12-degree-of-freedom affine-based registration, followed by a non-rigid 3D Bspline transformation [\[3](#page-41-6)] to ensure alignment of the main regions within the kidney (cortex, medulla, renal pyramids, renal pelvis, etc.). In the next step, the ADC maps of the segmented kidney are calculated. After that, iso-surfaces are generated from these maps. Then, we compute the Cumulative Distribution Function (CDF) for each iso-surface. The Spearman correlation algorithm is then applied to these CDFs corresponding to iso-surfaces. Finally, a transformer-based model is employed to leverage these correlations to distinguish between normal and acutely rejected transplants. To sum up, our contributions are twofold:

- We introduce a novel transformer-based framework for classifying acute renal transplant rejection using DW-MRI data obtained through various scanning protocols, which is a new approach that accounts for variations in scanner types and mitigates potential effects on MRI signals based on the correlations.
- We propose a Transformer-based Correlations to Classes Converter (T3C) model to leverage the correlations of the CDFs to distinguish between normal and acutely rejected transplants, highlighting the effectiveness and versatility of the transformer model in this context.

# **2 Methodology**

A transformer-based framework is introduced to predict acute renal transplant rejection using diverse DW-MRI scanning protocols (see Fig. [1\)](#page-36-0). The framework begins with preprocessing, which yields segmented ADC maps. Then, iso-surfaces are generated to represent distinct kidney regions. Afterwards, the T3C model leverages the correlations of CDFs which correspond to these regions for a comprehensive prediction of kidney states.

<span id="page-36-0"></span>Fig. 1. An overview of the proposed framework employs data from various DW-MRI scanning protocols to predict both normal and acute rejection.

## **2.1 Preprocessing**

As depicted in Fig. [1,](#page-36-0) this step involves subjecting the DW-MRI scans to segmentation to extract kidney using in-house software. A common challenge in DW-MRIs is the shape deformation that can lead to the alteration of MRI diffusion signals. The geometric shapes of anatomical structures can also affect the prediction accuracy. Therefore, the segmented kidneys undergo processing to align them and eliminate their deformation in intra-patient scenarios, such as due to breathing and/or heart beating, and to account for the kidney's variability due to inter-patient anatomical differences. Specifically, one of the 3D images is chosen as a reference image and all other 3D images are aligned to it by applying a 12-degree-of-freedom affine-based registration. To further correct the shape deformation, a non-rigid 3D B-spline transformation [\[3](#page-41-6)] is applied (see Fig. [2\)](#page-37-0). Afterwards, the registered kidneys are transformed to yield their corresponding ADC maps. These ADC maps are then used as inputs for the next step.

<span id="page-37-0"></span>Fig. 2. This figure illustrates a cross-section of 3D images through the registration process. (a) displays the reference image, while (b) presents the moving image. (c) and (d) show the outcomes following a 12-degree-of-freedom affine-based registration and the result obtained from a non-rigid 3D B-spline transformation, respectively.

## **2.2 ISO-Surface Analysis**

This study aims to develop a non-invasive method for identifying potential abnormalities indicative of renal rejection. To achieve this, we leverage correlations of CDFs which correspond to iso-surfaces of the kidney. Since the correlation measures the strength and direction of a linear relationship between two variables (in this case, the CDFs of iso-surfaces), we focus on the relationship patterns between distributions of different kidney regions using correlation rather than analyzing the raw imaging data directly. This approach mitigates the impact of scannerinduced variations on the raw imaging data values, which can be influenced by the specific characteristics of the scanner. To identify iso-surfaces, we employ a fast marching algorithm. Furthermore, the Spearman correlation is used for calculating correlations. The rationale behind using the Spearman correlation algorithm is its ability to assess monotonic relationships, determining whether there is a linear pattern or not between iso-surfaces. Upon visual inspection of Fig. [3,](#page-38-0) which displays the correlations 3D iso-surfaces for two patients (a) and (b), it is evident that the acutely rejected kidney, as shown in (b), exhibits variations in correlation when compared to the normal kidney, as illustrated in (a). These variations suggest potential abnormalities within these regions, indicative of renal rejection.

<span id="page-38-0"></span>Fig. 3. The process flow from acquiring the iso-surfaces to calculating the corresponding correlations is presented. (a) represents a normal case, while (b) shows an acute rejection case where the correlations experience variations, indicative of renal rejection.

## **2.3 Transformer-Based Correlations to Classes Converter (T3C)**

In the final stage, the correlations derived from the previous step undergo transformation via Principal Component Analysis (PCA). This is done to reduce the dimensionality of the obtained correlations, discarding those of lesser significance while preserving the essential ones (see Figs. [1](#page-36-0) and [4\)](#page-39-0). This procedure aids in the

development of a robust model that can accurately predict the status of transplanted kidneys, differentiating between normal function and instances of acute rejection. To achieve this, we have designed a T3C model that learns to map correlations to class scores. Specifically, a correlation tensor  $x \in \mathbb{R}^{N \times R \times C}$  is defined, where *N* represents the number of b-values for each patient, *R* is the number of rows, and *C* is the number of columns. This tensor is reshaped and linearly transformed to produce a sequence of correlation embeddings  $x = [e_1, ..., e_N] \in \mathbb{R}^{N \times L}$ , where  $e \in \mathbb{R}^L$ . To maintain positional information, learnable position embeddings  $p = [p_1, ..., p_N] \in \mathbb{R}^{N \times L}$  are added to the correlation embeddings, resulting in the input sequence of tokens  $z = x + p$ . The T3C model's transformer encoder, composed of *H* layers, processes the input sequences *z*, generating contextualized encodings  $z_H \in \mathbb{R}^{N \times L}$ . Each transformer layer consists of a multi-headed self-attention (MSA) block followed by a point-wise MLP block, with layer normalization (LN) applied before and residual connections added after each block:

$$
a_{i-1} = \text{MSA}(\text{LN}(z_{i-1})) + z_{i-1},
$$
  
$$
z_i = \text{MLP}(\text{LN}(a_{i-1})) + a_{i-1},
$$

where  $i \in \{1, ..., H\}$ . The self-attention mechanism computes queries  $\mathbf{Q} \in \mathbb{R}^{N \times d}$ , keys  $\mathbf{K} \in \mathbb{R}^{N \times d}$ , and values  $\mathbf{V} \in \mathbb{R}^{N \times d}$  via three point-wise linear layers and then computes self-attention as:

$$
\mathbf{MSA}(\mathbf{Q}, \mathbf{K}, \mathbf{V}) = \mathrm{softmax}\left(\frac{\mathbf{Q}\mathbf{K}^T}{\sqrt{d}}\right)\mathbf{V}
$$

The transformer encoder maps the input sequences to a contextualized encoding sequence  $z_H = [z_{H,1},...,z_{H,N}]$  that contains rich salient information. Subsequently,  $z_H$  is utilized by a Fully Connected (FC) classifier to obtain class scores corresponding to normal and acute rejection. Please refer to Fig. [1](#page-36-0) for a better understanding of the developed T3C model.

<span id="page-39-0"></span>Fig. 4. The procedure involves applying PCA to the correlations. (a) depicts the original Spearman correlation matrix, while (b) illustrates the matrix after retaining significant correlations and discarding insignificant ones.

# **3 Experiments and Results**

Dataset. This study's dataset includes DW-MRI scans from 94 patients. Of these, 34 were obtained using a 1.5T SIGNA Horizon scanner, adhering to specific parameters for coronal DW-MR images. The scans included 11 b-values sequences and baseline b0 scans, capturing blood perfusion and water diffusion effects. Each sequence used a single DW-MRI direction with equal gradient amplitudes. Furthermore, 60 DW-MRI scans were performed using a 3T scanner, following similar protocols with minor adjustments due to different scanner models. Each scan also included 11 b-values and the baseline b0.

Setting. We utilize an Adam optimizer with a learning rate of 0.001 over 100 epochs. Leave-one-out cross-validation is employed for evaluating the models, and the reported results represent the mean of ten runs. Additionally, L2 loss is utilized for training the model. The implementation was carried out using PyTorch on a single NVIDIA Quadro P4000 GPU with 8GB of memory.

Results and Analysis. We conducted two main experiments: the first used classical ML classifiers to categorize correlations, while the second utilized the proposed transformer-based model. As shown in Table [1,](#page-40-0) among the ML classifiers, Random Forest (RF) achieved the highest mean Accuracy (ACC) of 51.915%, mean Specificity (SPE) of 70.370%, and mean Area Under the Curve (AUC) of 48.685%. Decision Tree (DT) attained the highest mean Sensitivity (SEN) of 42.250% compared to Multi-Layer Perceptron (MLP) and Gradient Boosting (GB). However, the T3C model outperformed all others, boasting a mean ACC of 98.723%, mean SEN of 97%, mean SPE of 100%, and mean AUC of 98.5%, surpassing RF, DT, MLP, and GB. To further validate our model's superiority, we conducted a Mann-Whitney statistical test, revealing significant statistical superiority with a p-value less than 0.001 compared to other classifiers.

<span id="page-40-0"></span>Table 1. A comparison of the classification results between the T3C model and classical ML classifiers is presented. The mean*±*standard deviation, expressed in percentage, along with the statistical significance, are reported. Notably, the p-value for our model, when compared to other classifiers, was found to be less than 0.001, indicating a high level of statistical significance.

| Classifier ACC |                                                                                    | <b>SEN</b> | <b>SPE</b> | <b>AUC</b>                                                              | Mann-Whitney          |
|----------------|------------------------------------------------------------------------------------|------------|------------|-------------------------------------------------------------------------|-----------------------|
| RF             | $51.915 \pm 3.184$ 27.000 $\pm 7.399$                                              |            |            | $70.370 \pm 5.617$ 48.685 $\pm 3.340$                                   | $1.62 \times 10^{-4}$ |
| DT             | $ 41.596 + 3.478 $ $ 42.250 + 4.931 $                                              |            |            | $ 41.111 \pm 4.040 41.681 \pm 3.538$                                    | $1.62 \times 10^{-4}$ |
| MLP            | $ 44.787 + 1.809 $ $ 32.250 + 2.839 $                                              |            |            | $1.46 \times 10^{-4}$ $\pm 2.84543.162 \pm 1.777$ $1.46 \times 10^{-4}$ |                       |
| GB             | $ 47.553 \pm 1.069 28.750 \pm 1.250 61.481 \pm 1.814 45.116 \pm 0.991 $            |            |            |                                                                         | $1.33 \times 10^{-4}$ |
| T3C            | $\ket{98.723\pm1.241}$ 97.000 $\pm$ 2.915 100.000 $\pm$ 0 $\ket{98.500\pm1.458}$ – |            |            |                                                                         |                       |

Our findings have profound implications for clinical practice, as accurate prediction of normalcy or acute rejection in transplanted kidneys is crucial for effective treatment planning and monitoring. However, it is important to acknowledge the limitations of our approach, notably the relatively small cohort of only 94 patients.

# **4 Conclusions and Future Work**

This research paper introduces a novel framework for predicting both normal kidney function and instances of acute rejection. The framework leverages diverse DW-MRI datasets obtained through various scanning protocols, specifically, 1.5T and 3T. This approach effectively addresses the primary challenge of creating a system robust to variations in DW-MRI protocol across different scanners. The framework's remarkable sensitivity and specificity results establish its potential as a significant tool for treatment planning and monitoring patient responses to kidney transplants. Future work plans to broaden the study with larger cohorts, investigate additional imaging modalities, explore the impact of different correlation algorithms, develop an end-to-end detection framework, and evaluate the integration of clinical biomarkers such as creatinine clearance and serum creatinine.

Disclosure of Interests. The authors have no competing interests.

## **References**

- <span id="page-41-2"></span>1. Baliyan, V., Das, C.J., Sharma, R., Gupta, A.K.: Diffusion weighted imaging: technique and applications. World journal of radiology 8(9), 785 (2016)
- <span id="page-41-0"></span>2. Cavallo, M., Sepe, V., Conte, F., Abelli, M., Ticozzelli, E., Bottazzi, A., Geraci, P.: Cost-effectiveness of kidney transplantation from dcd in italy. In: Transplantation proceedings. vol. 46, pp. 3289–3296. Elsevier (2014)
- <span id="page-41-6"></span>3. Glocker, B., Komodakis, N., Paragios, N., Navab, N.: Non-rigid registration using discrete mrfs: Application to thoracic ct images. In: Workshop Evaluation of Methods for Pulmonary Image Registration in conjunction with Medical Image Computing and Computer-Assisted Intervention. Springer International Publishing (2010)
- <span id="page-41-4"></span>4. Kline, T.L., Korfiatis, P., Edwards, M.E., Bae, K.T., Yu, A., Chapman, A.B., Mrug, M., Grantham, J.J., Landsittel, D., Bennett, W.M., et al.: Image texture features predict renal function decline in patients with autosomal dominant polycystic kidney disease. Kidney international 92(5), 1206–1216 (2017)
- <span id="page-41-5"></span>5. Lee, H.C., Yoon, H.K., Nam, K., Cho, Y.J., Kim, T.K., Kim, W.H., Bahk, J.H.: Derivation and validation of machine learning approaches to predict acute kidney injury after cardiac surgery. Journal of clinical medicine 7(10), 322 (2018)
- <span id="page-41-3"></span>6. Palmucci, S., Mauro, L., Failla, G., Foti, P., Milone, P., Sinagra, N., Zerbo, D., Veroux, P., Ettorre, G., Veroux, M.: Magnetic resonance with diffusion-weighted imaging in the evaluation of transplanted kidneys: updating results in 35 patients. In: Transplantation proceedings. vol. 44, pp. 1884–1888. Elsevier (2012)
- <span id="page-41-1"></span>7. Sharfuddin, A.: Renal relevant radiology: imaging in kidney transplantation. Clinical journal of the American Society of Nephrology: CJASN 9(2), 416 (2014)

- <span id="page-42-1"></span>8. Shehata, M., Khalifa, F., Soliman, A., Ghazal, M., Taher, F., Abou El-Ghar, M., Dwyer, A.C., Gimel'farb, G., Keynton, R.S., El-Baz, A.: Computer-aided diagnostic system for early detection of acute renal transplant rejection using diffusionweighted mri. IEEE Transactions on Biomedical Engineering 66(2), 539–552 (2018)
- <span id="page-42-0"></span>9. Zhi, R., Zhang, X.D., Hou, Y., Jiang, K.W., Li, Q., Zhang, J., Zhang, Y.D.: Rtnet: a deep hybrid neural network for the identification of acute rejection and chronic allograft nephropathy after renal transplantation using multiparametric mri. Nephrology Dialysis Transplantation 37(12), 2581–2590 (2022)

# A Refer-and-Ground Multimodal Large Language Model for Biomedicine

Xiaoshuang Huang1,2, Haifeng Huang<sup>1</sup>, Lingdong Shen<sup>3</sup>, Yehui Yang<sup>1</sup>, Fangxin Shang<sup>1</sup>, Junwei Liu<sup>1</sup>, and Jia Liu<sup>1( $\approx$ )</sup>

<sup>1</sup> Healthcare Group, Baidu Inc, Beijing 100085, China <EMAIL>, <EMAIL> <sup>2</sup> China Agricultural University, Beijing 100083, China <sup>3</sup> MAIS, Institute of Automation, Chinese Academy of Sciences (CASIA), Beijing 100086, China

Abstract. With the rapid development of multimodal large language models (MLLMs), especially their capabilities in visual chat through refer and ground functionalities, their significance is increasingly recognized. However, the biomedical field currently exhibits a substantial gap in this area, primarily due to the absence of a dedicated refer and ground dataset for biomedical images. To address this challenge, we devised the Med-GRIT-270k dataset. It comprises 270k question-and-answer pairs and spans eight distinct medical imaging modalities. Most importantly, it is the first dedicated to the biomedical domain and integrating refer and ground conversations. The key idea is to sample large-scale biomedical image-mask pairs from medical segmentation datasets and generate instruction datasets from text using chatGPT. Additionally, we introduce a Refer-and-GrounD Multimodal Large Language Model for Biomedicine (BiRD) by using this dataset and multi-task instruction learning. Extensive experiments have corroborated the efficacy of the Med-GRIT-270k dataset and the multi-modal, fine-grained interactive capabilities of the BiRD model. This holds significant reference value for the exploration and development of intelligent biomedical assistants. The repository is at [https://github.com/ShawnHuang497/BiRD.](https://github.com/ShawnHuang497/BiRD)

Keywords: Referring and grounding *·* Instruction dataset *·* Biomedicine

# **1 Introduction**

Multimodal large language models (MLLMs) have become a popular area of research, with numerous applications in the field of visual languages, such

X. Huang—Work performed during an internship at Baidu Inc.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_38.](https://doi.org/10.1007/978-3-031-72390-2_38)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 399–409, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_38)\_38

<span id="page-44-0"></span>Fig. 1. BiRD empowers multimodal large language models in biomedicine with sophisticated referring and grounding capabilities. For more equitable comparison, we append spatial information to each LLaVa-med test, such as "The image size is [w, h], and the origin of the coordinate system is located in the upper left corner of the image.", where w and h denote width and height, respectively.

as, Visual Question Answering (VQA), open vocabulary detection, and so on. Nonetheless, the unique challenges presented by the realm of biomedicine, which starkly contrasts with the natural world, often render conventional visual assistants inept. They may either refrain from responding to biomedical queries or, worse, provide inaccurate responses or entirely fabricated information [\[11](#page-52-0)].

Despite existing research within the realm of biomedical MLLMs, current studies have predominantly focused on image description and VQA, leaving a notable gap in capabilities concerning referring and grounding (shown in Fig. [1\)](#page-44-0). The act of referring demands a model's accurate semantic comprehension of specified regions, while grounding necessitates the localization of regions based on semantic descriptions provided [\[27](#page-53-0)]. These fine-grained multimodal capabilities are essential for both the interaction process between intelligent biomedical assistants and patients and for biomedical education. This capability not only makes the information exchange process more intuitive but also significantly enhances the accuracy and efficiency of information exchange. A key factor hindering the development of this capability in the field of biomedicine is the lack of multi-modal fine-grained interactive datasets.

To address these challenges, we develop the BioMedical Ground-and-Refer Instruction-Tuning (Med-GRIT-270k) dataset by leveraging the medical segmentation dataset (SA-Med2D-20M [\[26](#page-53-1)]). Then a biomedical refer-and-ground multimodal large language model was explored with the Med-GRIT-270k and

multi-task instruction learning method. The paper principally contributes the following:

- Med-GRIT-270k Dataset. Large-scale biomedical image-mask pairs are transformed into multi-modal conversations by leveraging chatGPT [\[19](#page-52-1)] in a novel process. It is the first dataset in biomedicine to integrate referring, grounding, and conversations.
- The first Biomedical Refer-and-grounD Multimodal Large Language Model (BiRD). It is fine-tuned by multi-task instruction learning for the biomedical domain with self-generated data. This validates the effectiveness of multi-task instruction tuning and highlights best practices for adapting the MLLMs to the specialized domain.
- To advance biomedical multi-modal learning research, we will release the Med-GRIT-270k dataset and a comprehensive codebase for community use.

## **2 Related Work**

Biomedical Multi-modal Large Language Models. Amidst the rapid development of Large Language Models (LLMs) and the success of instruction-tuned LLMs within the general domain [\[5](#page-52-2)[,17](#page-52-3)[,25](#page-53-2),[27,](#page-53-0)[28](#page-53-3)[,30](#page-53-4)], researchers in the biomedical field have been fervently exploring the expansion of these models' capabilities. Recent studies have increasingly concentrated on the domain of MLLMs, with notable endeavors within the biomedical sector including BioMedGPT [\[18\]](#page-52-4), RadFM  $[24]$ , LLaVa-Med  $[12]$ , and so on  $[7, 8, 16, 21, 23, 29]$  $[7, 8, 16, 21, 23, 29]$  $[7, 8, 16, 21, 23, 29]$  $[7, 8, 16, 21, 23, 29]$  $[7, 8, 16, 21, 23, 29]$  $[7, 8, 16, 21, 23, 29]$ . These methodologies have significantly propelled the development of MLLMs in the biomedical realm. For instance, LLaVa-Med [\[12\]](#page-52-5), utilizing pre-trained LLMs for visual instruction tuning, has established a unique, end-to-end multi-modal biomedical chatbot capable of processing image inputs. RadFM [\[24\]](#page-53-5) is a MLLM supporting 2D/3D radiographic imaging input for the medical domain. However, due to various challenges, biomedical MLLMs capable of supporting fine-grained interactions have yet to emerge.

MLLMs for Referring and Grounding. In natural images, the large-scale public datasets have greatly supported the exploration into the sophisticated understanding abilities of multimodal large language models (MLLMs), such as Gpt4ROI  $[30]$  $[30]$ , Ferret  $[27]$ , QWen-VL  $[3]$ , and so on. Although some work  $[9]$ , [14\]](#page-52-11) has already begun to investigate grounding in biomedicine, it can only be applied to small models, as the amount of data is limited and there are only a few modalities. The paramount factor underlying the success of these initiatives is their access to pertinent, large-scale datasets. For instance, QWen-VL uses around 80M data for referring and grounding. However, the multi-modal finegrained interactive dataset in biomedical is virtually nonexistent.

<span id="page-46-0"></span>Fig. 2. An instance of our generated instruction-following data. Top: the meta information is created according to rules in medical segmentation datasets, and the image caption was generated from chatGPT. Bottom: the instruction following data generated by chatGPT.

# **3 Med-GRIT-270k: Biomedical Ground-and-Refer Instruction-Tuning Dataset**

We've created the first biomedical refer-and-ground instruction-tuning dataset to address the lack of such resources. It was generated through the collaborative efforts of humans and Artificial Intelligence (AI), derived from large-scale biomedical image segmentation datasets. The generation process can be divided into three steps: (i) Manually generating instance-level meta information for each image based on its mask. (ii) Employing an AI assistant to generate global information for the images. (iii) Utilizing the AI assistant to craft fine-grained conversations based on the meta information and global image information obtained in the previous steps.

Generating Instance-level Meta Information. We first sampled biomedical image-mask pairs from the SA-Med2D-20M [\[26](#page-53-1)]. Ultimately, approximately 60K images were sampled from this dataset, considering the diversity of modality and redundancy. For instance, the original dataset includes a plethora of 2D slices from 3D data, leading to excessive data similarity. Subsequently, we calculated the coordinates of each instance based on the instance-level masks. Specifically, spatial locations are delineated via the textual representation in the format  $[X_{topleft}$ ,  $Y_{topleft}$ ,  $X_{bottomright}$ ,  $Y_{bottomright}$ , and normalize the coordinates to fall within the range [0,1]. Finally, we enrich the images with additional details to compile the meta information, which includes modality, scanned region, orientation, and object coordinates.

Generating Image Captions. We utilize meticulously designed prompts along with the meta information provided to ChatGPT [\[19\]](#page-52-1), thereby acquiring the global information for each image.

Biomedical Instruction-Tuning Data. Spatial understanding is manifested through various task formats. This primarily encompasses two distinct types and their corresponding task names: (i) Region-in and Text-out: Referring Object Classification (ROC), Referring Captioning (RC), (ii) Text-in and Region-out: Visual Grounding (VG), and (iii) Text-in and Text-out: Medical Image Analysis (MIA). To reduce ambiguity and enhance the model's capability for fine-grained visual comprehension, some essential strategies are adopted. The special tokens (*<*ref*>* and *<*/ref*>*) are introduced, marking the content referred to by the bounding box. This aptly associates bounding boxes with their corresponding descriptive words or sentences. Subsequently, we instructed ChatGPT to design a question and answer for each task.

Finally, We mapped the coordinates within the range [0, 1000] and reformatted them as  $(X_{topleft}, Y_{topleft})$ ,  $(X_{bottomright}, Y_{bottomright})$ . To differentiate between detection strings and regular text strings, two special tokens (*<*box*>* and  $\langle$ box $\rangle$  are appended at the start and end of the bounding box string, respectively. Figure [2](#page-46-0) shows an example of our instruction-following data.

## **4 Multi-Task Instruction Learning**

We aim to imbue MLLMs with grounding and referring capacities via multi-task learning, simultaneously ensuring the retention of the MLLM's essential conversational proficiency. This section will henceforth elucidate from two perspectives: the architecture of the model and multi-task instruction training.

### **4.1 Model Architecture**

We utilize Qwen-VL [\[3](#page-51-0)], a comprehensive multimodal conversational model, as the foundational general-domain language model. Specifically, the visual encoder employs the Vision Transformer (ViT) [\[6\]](#page-52-12) architecture, initialized with pre-trained weights from OpenAI's CLIP ViT-BigG [\[10](#page-52-13)]. The vision-language adapter utilizes cross-attention with a trainable query. The large language model incorporates the pre-trained Qwen-7B [\[2](#page-51-1)].

<span id="page-48-0"></span>Fig. 3. Overreview. Left: the training set (Top) and test set (Bottom) distribution of conversation turns in Med-GRIT-270k we collected. Right: the architecture of the Biomedical refer-and-ground multimodal large language model (BiRD), which is based on Qwen-VL [\[3](#page-51-0)]. We have developed it from the 240k data and evaluated it on 30k data.

### **4.2 Multi-Task Instruction Training**

Considering that the base model already possesses the capability to refer or ground within natural images, we employ only one stage to finetune it based on the pre-trained base model on the Med-GRIT-240k dataset. As illustrated in Fig. [3\)](#page-48-0), We solely fine-tune the cross-attention and LLM parameters, while the visual encoder remains frozen. The input images are processed through the ViT-BigG [\[10\]](#page-52-13) and vision-language adapter, yielding fixed-length sequences of visual features. We then append the markers (*<*img*>* and *<*/img*>*) to the start and end of the image feature sequence, respectively, to denote the beginning and end of visual content. We fine-tuned the model using a dataset comprising 60k images and a total of 240k dialogue turns. The global training batch size is 128. The learning rate is 2*e−*5 and the scheduler is cosine. The multi-task instruction training just took 30 h on  $4 \times A100(40)$  GPUs.

# **5 Experiments**

In this section, we execute a thorough evaluation across diverse multimodal tasks to holistically gauge our models' proficiency in visual comprehension.

| Model              | Test dataset     | VG (Recall@0.5 \$\uparrow\$) | ROC (Recall \$\uparrow\$) | RC (SPICE \$\uparrow\$) | MIA (mBMR \$\uparrow\$) | Average      |
|--------------------|------------------|------------------------------|---------------------------|-------------------------|-------------------------|--------------|
| LLaVa-Med [12]     | Med-GRIT-Test30k |                              | 2.75                      | 8.18                    | 11.20                   | 5.53         |
| BiRD-Med-GRIT-20k  | Med-GRIT-Test30k | 38.59                        | 47.94                     | 29.02                   | 27.22                   | 35.69        |
| BiRD-Med-GRIT-40k  | Med-GRIT-Test30k | 46.30                        | 51.84                     | 50.32                   | 30.14                   | 44.65        |
| BiRD-Med-GRIT-80k  | Med-GRIT-Test30k | 52.87                        | 52.02                     | 52.84                   | 44.83                   | 50.64        |
| BiRD-Med-GRIT-270k | Med-GRIT-Test30k | 53.92                        | <b>65.33</b>              | <b>55.23</b>            | <b>52.17</b>            | <b>56.66</b> |
| LLaVa-Med [12]     | LLaVa-Med-qa0.2k | -                            | -                         | -                       | 20.04                   | -            |
| BiRD-Med-GRIT-270k | LLaVa-Med-qa0.2k | -                            | -                         | -                       | 10.55                   | -            |

<span id="page-49-1"></span>Table 1. Comparison with LLaVa-Med [\[12\]](#page-52-5) and study on the multimodal dataset scales.

<span id="page-49-0"></span>Table 2. The capabilities of various biomedical MLLMs. Note that the "Modality" denotes image modality.

|                   |                  |    |               | MIA ROC RC VG Modality |
|-------------------|------------------|----|---------------|------------------------|
| $LLaVa-Med [12]$  |                  |    |               | 5                      |
| RadFM $[24]$      |                  | x. |               | 6                      |
| Med-palm $m$ [22] | $\boldsymbol{x}$ |    | $\chi$ $\chi$ | 6                      |
| <b>BiRD</b>       |                  |    |               |                        |

Fig. 4. The example of object hallucination in BiRD.

Evaluation dataset. We randomly selected approximately 12% of the images and dialogues from the constructed Med-GRIT-270k dataset to serve as the test set. Given that a single 3D dataset contains multiple data slices, we extracted cases in their entirety to prevent leakage of test set data into the training set. This ensures that different slices from the same 3D dataset do not concurrently appear in both the training and test sets, thereby guaranteeing the reliability of the test results.

Evaluation metrics. The evaluation metrics for the four tasks are Recall@0.5, Recall, Spice [\[1](#page-51-2)], and mBMR, respectively. Recall@0.5 denotes a prediction as correct only when the intersection over union (IoU) between the predicted bounding box and the ground truth exceeds 0.5. The mBMR utilized for assessing the MIA task is the mean value of BLEU@4 [\[20](#page-52-14)], METEOR [\[4\]](#page-51-3), and ROUGE-L [\[15\]](#page-52-15), offering a more comprehensive evaluation of the prediction quality than a solitary metric.

Comparison. As shown in Table [2,](#page-49-0) we are the pioneers in developing a medical MLLM with referring and grounding capabilities, and existing MLLMs (such as Qwen-VL [\[3\]](#page-51-0), GPT-4 [\[19](#page-52-1)], MiniGPT-v2 [\[5\]](#page-52-2), etc.) have not seen medical referring and grounding data. So we will not compare them on evaluation metrics, as it would be profoundly unfair.

As illustrated in Table [1,](#page-49-1) we present the quantitative test outcomes for LLaVa-Med [\[12](#page-52-5)] and the impact of the data scale on these results. Between rows 3

|          | Metric      | CT    | MR    | X-ray | PET   | Endoscopy | Dermoscopy | Fundus | Ultrasound | Average |
|----------|-------------|-------|-------|-------|-------|-----------|------------|--------|------------|---------|
| VG       | Recall@0.5↑ | 44.47 | 29.26 | 41.73 | 56.46 | 53.60     | 75.63      | 84.15  | 46.04      | 53.92   |
| ROC      | Recall↑     | 34.76 | 61.79 | 53.74 | -     | 60.40     | 96.61      | -      | 84.65      | 65.33   |
| RC       | Spice↑      | 41.88 | 51.69 | 37.39 | 47.95 | 54.07     | 77.44      | 48.73  | 82.65      | 55.23   |
| MIA      | mBMR↑       | 47.01 | 49.35 | 37.17 | 57.15 | 39.91     | 72.13      | 48.87  | 65.78      | 52.17   |
| Average- | -           | 43.03 | 48.02 | 42.51 | 53.85 | 51.99     | 80.45      | 60.58  | 69.78      | -       |

<span id="page-50-0"></span>Table 3. The performance of the BiRD model across various tasks and modalities on the Med-GRIT-270k test dataset.

and 6, we observe the performance of the BiRD-Med-GRIT model across varying data scales. With the expansion of training data, all metrics exhibit significant enhancements, with the average rising from 35.69 to 56.66. This underscores the efficacy of augmenting dataset size in bolstering the model's proficiency on multimodal datasets. Notably, at the 240k dataset level, the model achieved the highest scores across all metrics, showcasing optimal overall performance.

From the first and sixth rows of Table [1,](#page-49-1) it is evident that the LLaVa-Med [\[12](#page-52-5)] model demonstrates subpar performance on the Med-GRIT-Test30k dataset, particularly in terms of no efficacy in region-level visual content localization (with the Recall@0.5 of 0). Simultaneously, we evaluated our model on the LLaVa-Med qa-0.2k test set as well. As indicated in the last two rows of Table [1,](#page-49-1) due to not being trained on the LLaVa-Med [\[12](#page-52-5)] dataset, our performance metrics on its test set were marginally lower than its own. However, on similar MIA tasks within our test set, LLaVa-Med [\[12\]](#page-52-5)(with an mBMR of 11.20), significantly underperformed in comparison to our model (with an mBMR of 52.17).

Main Results. As shown in Table [3,](#page-50-0) we display the performance of the BiRD model across four distinct tasks in eight different medical imaging modalities. The ROC task tests the MLLM's understanding of text related to specific image areas and their visual details. The PET and Fundus, which focus on only one category, are not trained or evaluated. We find the recall of ROC mainly depends on the variety and distinctiveness of objects and features across image modalities. The RC task tests the model's ability to recognize image regions and describe them in words. The model does well with Ultrasound and Dermoscopy images but struggles with the more diverse CT images, where performance lags. The VG task tests how well the model matches text descriptions to image areas. MR modality performed the worst, likely because it mostly features tumor tissues, with far fewer anatomical structures. This issue is also seen in ultrasound images. The MIA task checks the model's understanding of medical images. The 4th row in Table [3](#page-50-0) shows the model has some level of analysis and understanding across almost all modalities.

Across the four evaluated tasks, it is apparent that the Dermoscopy modality consistently exhibits the highest performance metrics. This can be attributed to the distinct visual features, a reduced number of object categories, and the substantial proportion of the image occupied by the object regions, collectively simplifying the task for this particular modality.

Object Hallucination. As Fig. [4](#page-49-0) shows, we have also observed instances of object hallucination in BiRD. This phenomenon is common and has also been observed in other MLLMs [\[13](#page-52-16)]. We believe this is attributed to the fact that the model's visual encoder is frozen, and its initialized parameters have scarcely encountered medical imaging, resulting in a lack of comprehensive understanding of specific domains or topics in feature extraction. In a word, this phenomenon should receive increased attention in future research endeavors.

## **6 Conclusion**

In this paper, to develop a single MLLM assistant capable of handling multiple vision-language tasks, we propose a Med-GRIT-270k dataset. By leveraging the dataset, we introduce the BiRD model, a Biomedical Refer-and-GrounD Multimodal Large Language Model. We verified BiRD on a diverse 30k question-andanswer test set, encompassing multimodal and multitask scenarios. The BiRD showcases a highly promising direction for developing intelligent biomedical assistants. To our knowledge, Med-GRIT-270k and BiRD are respectively the first refer-and-ground dataset and fine-grained interactive MLLM in the realm of biomedicine. We will release both the dataset and model to foster the development of intelligent biomedical assistants.

Limitations. Although this work developed a novel multimodal dataset in biomedicine, during the data construction process, most of the raw datasets only annotated certain organs or diseases for a sample. This makes it difficult to construct highly correlated negative samples. This issue will be a focus in the subsequent data construction work.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-51-2"></span>1. Anderson, P., Fernando, B., Johnson, M., Gould, S.: Spice: Semantic propositional image caption evaluation. In: Computer Vision–ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part V 14. pp. 382–398. Springer (2016)
- <span id="page-51-1"></span>2. Bai, J., Bai, S., Chu, Y., Cui, Z., Dang, K., Deng, X., Fan, Y., Ge, W., Han, Y., Huang, F., et al.: Qwen technical report. arXiv preprint [arXiv:2309.16609](http://arxiv.org/abs/2309.16609) (2023)
- <span id="page-51-0"></span>3. Bai, J., Bai, S., Yang, S., Wang, S., Tan, S., Wang, P., Lin, J., Zhou, C., Zhou, J.: Qwen-vl: A versatile vision-language model for understanding, localization, text reading, and beyond (2023)
- <span id="page-51-3"></span>4. Banerjee, S., Lavie, A.: Meteor: An automatic metric for mt evaluation with improved correlation with human judgments. In: Proceedings of the acl workshop on intrinsic and extrinsic evaluation measures for machine translation and/or summarization. pp. 65–72 (2005)

- <span id="page-52-2"></span>5. Chen, J., Zhu, D., Shen, X., Li, X., Liu, Z., Zhang, P., Krishnamoorthi, R., Chandra, V., Xiong, Y., Elhoseiny, M.: Minigpt-v2: large language model as a unified interface for vision-language multi-task learning. arXiv preprint [arXiv:2310.09478](http://arxiv.org/abs/2310.09478) (2023)
- <span id="page-52-12"></span>6. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-52-6"></span>7. Eslami, S., Meinel, C., De Melo, G.: Pubmedclip: How much does clip benefit visual question answering in the medical domain? In: Findings of the Association for Computational Linguistics: EACL 2023. pp. 1151–1163 (2023)
- <span id="page-52-7"></span>8. Han, T., Adams, L.C., Nebelung, S., Kather, J.N., Bressem, K.K., Truhn, D.: Multimodal large language models are generalist medical image interpreters. medRxiv pp. 2023–12 (2023)
- <span id="page-52-10"></span>9. Huang, X., Li, H., Cao, M., Chen, L., You, C., An, D.: Cross-modal conditioned reconstruction for language-guided medical image segmentation. arXiv preprint [arXiv:2404.02845](http://arxiv.org/abs/2404.02845) (2024)
- <span id="page-52-13"></span>10. Ilharco, G., Wortsman, M., Wightman, R., Gordon, C., Carlini, N., Taori, R., Dave, A., Shankar, V., Namkoong, H., Miller, J., et al.: Openclip (2021). URL: [https://](https://doi.org/10.5281/zenodo.5143772) [doi.org/10.5281/zenodo.5143772](https://doi.org/10.5281/zenodo.5143772)
- <span id="page-52-0"></span>11. Lee, P., Bubeck, S., Petro, J.: Benefits, limits, and risks of gpt-4 as an ai chatbot for medicine. New England Journal of Medicine p. 1233-1239 (Mar 2023). [https://](https://doi.org/10.1056/nejmsr2214184) [doi.org/10.1056/nejmsr2214184](https://doi.org/10.1056/nejmsr2214184)
- <span id="page-52-5"></span>12. Li, C., Wong, C., Zhang, S., Usuyama, N., Liu, H., Yang, J., Naumann, T., Poon, H., Gao, J.: Llava-med: Training a large language-and-vision assistant for biomedicine in one day. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-52-16"></span>13. Li, Y., Du, Y., Zhou, K., Wang, J., Zhao, W.X., Wen, J.R.: Evaluating object hallucination in large vision-language models. arXiv preprint [arXiv:2305.10355](http://arxiv.org/abs/2305.10355) (2023)
- <span id="page-52-11"></span>14. Li, Z., Li, Y., Li, Q., Wang, P., Guo, D., Lu, L., Jin, D., Zhang, Y., Hong, Q.: Lvit: language meets vision transformer in medical image segmentation. IEEE transactions on medical imaging (2023)
- <span id="page-52-15"></span>15. Lin, C.Y.: Rouge: A package for automatic evaluation of summaries. In: Text summarization branches out. pp. 74–81 (2004)
- <span id="page-52-8"></span>16. Liu, F., Zhu, T., Wu, X., Yang, B., You, C., Wang, C., Lu, L., Liu, Z., Zheng, Y., Sun, X., et al.: A medical multimodal large language model for future pandemics. NPJ Digital Medicine 6(1), 226 (2023)
- <span id="page-52-3"></span>17. Liu, H., Li, C., Wu, Q., Lee, Y.J.: Visual instruction tuning. Advances in neural information processing systems 36 (2024)
- <span id="page-52-4"></span>18. Luo, Y., Zhang, J., Fan, S., Yang, K., Wu, Y., Qiao, M., Nie, Z.: Biomedgpt: Open multimodal generative pre-trained transformer for biomedicine. arXiv preprint [arXiv:2308.09442](http://arxiv.org/abs/2308.09442) (2023)
- <span id="page-52-1"></span>19. OpenAI, O.: Gpt-4 technical report (Mar 2023)
- <span id="page-52-14"></span>20. Papineni, K., Roukos, S., Ward, T., Zhu, W.J.: Bleu: a method for automatic evaluation of machine translation. In: Proceedings of the 40th annual meeting of the Association for Computational Linguistics. pp. 311–318 (2002)
- <span id="page-52-9"></span>21. Shen, L., Shang, F., Yang, Y., Huang, X., Xiang, S.: Segicl: A universal in-context learning framework for enhanced segmentation in medical imaging. arXiv preprint [arXiv:2403.16578](http://arxiv.org/abs/2403.16578) (2024)

- <span id="page-53-8"></span>22. Tu, T., Azizi, S., Driess, D., Schaekermann, M., Amin, M., Chang, P.C., Carroll, A., Lau, C., Tanno, R., Ktena, I., et al.: Towards generalist biomedical ai. NEJM AI 1(3), AIoa2300138 (2024)
- <span id="page-53-6"></span>23. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. arXiv preprint [arXiv:2210.10163](http://arxiv.org/abs/2210.10163) (2022)
- <span id="page-53-5"></span>24. Wu, C., Zhang, X., Zhang, Y., Wang, Y., Xie, W.: Towards generalist foundation model for radiology. arXiv preprint [arXiv:2308.02463](http://arxiv.org/abs/2308.02463) (2023)
- <span id="page-53-2"></span>25. Wu, S., Fei, H., Qu, L., Ji, W., Chua, T.S.: Next-gpt: Any-to-any multimodal llm. arXiv preprint [arXiv:2309.05519](http://arxiv.org/abs/2309.05519) (2023)
- <span id="page-53-1"></span>26. Ye, J., Cheng, J., Chen, J., Deng, Z., Li, T., Wang, H., Su, Y., Huang, Z., Chen, J., Jiang, L., et al.: Sa-med2d-20m dataset: Segment anything in 2d medical imaging with 20 million masks. arXiv preprint [arXiv:2311.11969](http://arxiv.org/abs/2311.11969) (2023)
- <span id="page-53-0"></span>27. You, H., Zhang, H., Gan, Z., Du, X., Zhang, B., Wang, Z., Cao, L., Chang, S.F., Yang, Y.: Ferret: Refer and ground anything anywhere at any granularity. arXiv preprint [arXiv:2310.07704](http://arxiv.org/abs/2310.07704) (2023)
- <span id="page-53-3"></span>28. Zhan, J., Dai, J., Ye, J., Zhou, Y., Zhang, D., Liu, Z., Zhang, X., Yuan, R., Zhang, G., Li, L., et al.: Anygpt: Unified multimodal llm with discrete sequence modeling. arXiv preprint [arXiv:2402.12226](http://arxiv.org/abs/2402.12226) (2024)
- <span id="page-53-7"></span>29. Zhang, S., Xu, Y., Usuyama, N., Bagga, J., Tinn, R., Preston, S., Rao, R., Wei, M., Valluri, N., Wong, C., et al.: Large-scale domain-specific pretraining for biomedical vision-language processing. arXiv preprint [arXiv:2303.00915](http://arxiv.org/abs/2303.00915) 2(3), 6 (2023)
- <span id="page-53-4"></span>30. Zhang, S., Sun, P., Chen, S., Xiao, M., Shao, W., Zhang, W., Chen, K., Luo, P.: Gpt4roi: Instruction tuning large language model on region-of-interest. arXiv preprint [arXiv:2307.03601](http://arxiv.org/abs/2307.03601) (2023)

# **A Unified Model for Longitudinal Multi-Modal Multi-View Prediction with Missingness**

Boqi Chen<sup>( $\boxtimes$ )</sup>, Junier Oliva, and Marc Niethammer

Department of Computer Science, University of North Carolina at Chapel Hill, Chapel Hill, NC, US <EMAIL>

**Abstract.** Medical records often consist of different modalities, such as images, text, and tabular information. Integrating all modalities offers a holistic view of a patient's condition, while analyzing them longitudinally provides a better understanding of disease progression. However, real-world longitudinal medical records present challenges: 1) patients may lack some or all of the data for a specific timepoint, and 2) certain modalities or views might be absent for all patients during a particular period. In this work, we introduce a unified model for longitudinal multi-modal multi-view prediction with missingness. Our method allows as many timepoints as desired for input, and aims to leverage all available data, regardless of their availability. We conduct extensive experiments on the knee osteoarthritis dataset from the Osteoarthritis Initiative (OAI) for pain and Kellgren-Lawrence grade (KLG) prediction at a future timepoint. We demonstrate the effectiveness of our method by comparing results from our unified model to specific models that use the same modality and view combinations during training and evaluation. We also show the benefit of having extended temporal data and provide post-hoc analysis for a deeper understanding of each modality/view's importance for different tasks. Our code can be found at [https://github.com/uncbiag/UniLMMV.](https://github.com/uncbiag/UniLMMV)

# **1 Introduction**

In recent years, deep learning methods have revolutionized various domains, particularly in computer vision and natural language processing, owing to the accessibility of expansive datasets. The surge in available data has also catalyzed remarkable advances in medical data analysis [\[11](#page-63-0),[17\]](#page-63-1), including image segmentation, registration, and prediction [\[4](#page-62-0)]. However, analyzing medical records presents both opportunities and challenges. These records are often rich in modalities and views, spanning from demographic information to images of various regions and doctor's notes, that can provide multifaceted disease insights.

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_39)<sub>-39</sub>.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 410–420, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_39)\_39

However, they also pose challenges, ranging from the difficulty of acquiring all data to tracking patients over extended time periods.

Multi-modal models have demonstrated remarkable efficacy using natural language, images, audio, etc. [\[8,](#page-63-2)[24](#page-64-0)]. However, many existing approaches assume the simultaneous availability of all modalities during training and testing, which is not always realistic for medical records. Although strategies such as missing record synthesis [\[16,](#page-63-3)[20\]](#page-63-4) have been explored to tackle this problem, few can handle large numbers of modalities or data captured from different patient regions. Moreover, these approaches require imputation, which is challenging by itself.

Longitudinal data often exists in medical applications, and the patient's historical trajectory holds invaluable clues for present diagnoses and future predictions. Many studies [\[2](#page-62-1),[22\]](#page-64-1) use longitudinal data, but the issue of missingness, particularly in the context of longitudinal data and across multiple modalities or views, remains a substantial challenge.

In this work, we propose a novel and unified method to tackle the aforementioned challenges whilst bypassing the need for missing data imputation. The main contributions of our work are as follows:

- 1. We propose a unified model for longitudinal multi-modal multi-view prediction, offering flexibility in both the number of inputs and timepoints.
- 2. We evaluate our approach on WOMAC pain [\[23](#page-64-2)] and KLG [\[13](#page-63-5)] prediction for osteoarthritis, where we elucidate the benefits of utilizing diverse modalities, views, and multiple timepoints.
- 3. We demonstrate the generality of our unified model, which can handle different input combinations during evaluation.
- 4. We conduct post-hoc analyses to assess the significance of each modality and view for different tasks.

## **2 Related Works**

Medical records frequently encounter the issue of incompleteness, which can be categorized into missing at random (MAR), missing completely at random (MCAR), or missing not at random (MNAR). For the case of MAR, recent works have focused on synthesizing the missing data / features  $[16, 20]$  $[16, 20]$  $[16, 20]$  or learning joint multi-modal embeddings [\[18](#page-63-6),[30\]](#page-64-3), which then allow replacing one modality by the other. However, these methods are generally limited to data of the same subject or between a few modalities that can be effectively aligned. For diverse medical modalities and views, the most naive approach involves either removing samples with missingness or filling them in with special values. Recent works have also used a mask indicator to help ignore missing data [\[19](#page-63-7),[31\]](#page-64-4), offering a straightforward yet effective solution. In this work, we extend the masking-based strategies for complex missing patterns across longitudinal data.

Longitudinal data analysis has been a popular area, especially in the medical field [\[2](#page-62-1)[,22](#page-64-1)]. Historically, many studies [\[7\]](#page-63-8) have relied on traditional parametric statistical methods to analyze associations between variables, but they can have difficulty in capturing high-dimensional data. Recent advances in machine learning offer solutions to this challenge, with various innovative architectures being proposed. One of the simplest methods is through feature summarization [\[1\]](#page-62-2), which aggregates all temporal information either at the input or in the feature space. More sophisticated techniques include applying recurrent models for disease prediction [\[5,](#page-63-9)[15\]](#page-63-10). However, previous works on medical prediction often aim to produce a single output from a fixed number of inputs. In order to accommodate varying numbers of timepoints, we employ a transformer decoder model [\[25\]](#page-64-5), enabling prediction at every timepoint, where each prediction depends only on the preceding inputs.

# pred $r_{\text{pred}}$ pred . . . . . . (more months) (more months) $0$ month 12 month 48 month

# **3 Method**

<span id="page-56-0"></span>**Fig. 1.** Our proposed model consists of an encoder for each modality and view, an attention block for summarizing the features, and a decoder block that predicts the result at each timepoint, focusing solely on previous data. [SUM] and [PAD] are learnable embeddings, where [SUM] outputs the summarized feature of all inputs, and [PAD] represents the modality or view that is absent for all patients.

Our work presents a unified model for longitudinal multi-modal multi-view prediction, as shown in Fig. [1.](#page-56-0) Our novel model design is inspired by the following: 1) we want to process various types of modalities (e.g., tabular, radiography) and views (e.g., knee, pelvis); thus, we propose utilizing multiple encoders to represent information stemming from different modalities and views<sup>[1](#page-56-1)</sup>; 2) at any timepoint, there may be completely different patterns of missingness both within and between patients; thus, we propose utilizing a masked attention scheme to discard the missing data; 3) patients may have different numbers of available timepoints; thus, we propose a decoder model that can pay attention to various numbers of timepoints, each of which may consist of a subset of views.

<span id="page-56-1"></span> $1$  For simplicity, we use view to represent both modality and view in the following.

### **3.1 Feature Extraction**

We consider a dataset of multiple patients, which can be observed through multiple views at multiple timepoints. Let  $x_a[i, t]$  denote the a-th view  $(a \in \{1, ..., n\})$ for the *i*-th patient at timepoint t. We use a neural network  $\mathcal{F}_{\theta_a}$  to extract features from view a. We use convolutional neural networks for images and a transformer for tabular data, resulting in a feature vector:

$$
F_a[i, t] = \mathcal{F}_{\theta_a}(x_a[i, t]). \tag{1}
$$

We use n different encoders for the n different views. However, the same encoder is used for different timepoints of the same view.

### **3.2 Feature Summarization**

To summarize features from various views, we use an attention block on the extracted features between all views  ${F_a[i, t]}$ . We first use a linear layer on the tabular feature vector to match the feature dimension of the image modalities. Subsequently, we ensure a uniform number of views by padding with a learnable [PAD] embedding. We also include a learnable [SUM] embedding, where its output serves as the summarized feature representing all views. The feature embeddings and their corresponding view embeddings (an embedding representing which view a feature belongs to) are added. To support a subset of available views during evaluation, we randomly drop each view 50% of the time during training of our unified model. For these dropped views, the mask indicator  $M$ , which we introduce below, is set to 0. We apply the attention block on all features (see Fig. [1\)](#page-56-0). The attention block includes multiple layers of transformer self-attention [\[29\]](#page-64-6) blocks, where  $Q, K, V$  represent the query, key, and value.

Given the possibility of missing views in medical records, we incorporate a mask indicator  $\mathcal{M} \in \mathbb{R}^{n \times n}$  during training, where  $\mathcal{M}_{i,j} = \{0,1\}$  represents absence or presence of the view. This allows us to manually assign a very low attention score  $\delta = -1e^9$  to the missing view [\[19\]](#page-63-7), which ensures that the summarized feature does not focus on the missing data:

$$
Attention(Q, K, V) = softmax\left(\frac{QK^T}{\sqrt{d_k}} \cdot \mathcal{M} + \delta \cdot (1 - \mathcal{M})\right) V, \qquad (2)
$$

where  $d_k$  is the dimension of the key embedding,  $Q = W_Q X$ ,  $K = W_K X$ ,  $V = W_V X$  with X the input embedding features and  $\{W_Q, W_K, W_V\}$  learnable parameters. The final summarized feature  $F_{[SUM]}[i, t]$  is obtained after multiple layers, each with a combination of multiple heads of the above formula.

### **3.3 Longitudinally-Aware Prediction**

Following the extraction of a summarized feature at each timepoint, our transformer decoder block disregards future timepoints [\[25](#page-64-5)], and the prediction is based solely on preceding timepoints:

$$
p(o_{i,t}) = p(o_{i,t} | F_{[SUM]}[i, \le t]),
$$
\n(3)

where  $o_{i,t}$  is the output at timepoint t and  $F_{[SUM]}[i, \leq t]$  is the set of all summarized features of patient i with timepoint  $\leq t$ . In scenarios where labels of certain timepoints are missing, we do not consider these predictions when calculating the loss. We use a weighted cross-entropy loss that solely considers instances with available prediction labels:

$$
loss = \begin{cases} -\frac{1}{m \cdot l - |\mathcal{D}|} \sum_{i=1}^{m} \sum_{t=1}^{l} \mathbb{1}_{(i,t) \notin \mathcal{D}} \cdot w_{i,t} \cdot y_{i,t} \cdot log(p(o_{i,t})) , & \text{if } m \cdot l > |\mathcal{D}| \\ 0, & \text{otherwise} \end{cases}
$$
(4)

where  $m$  is the number of elements in a mini-batch,  $l$  is the total number of timepoints,  $\mathcal D$  is the set of the ignored samples in the mini-batch,  $w$  is the weight for balancing each class, and  $y$  is the given label.

# **4 Experimental Results**

## **4.1 Dataset**

We evaluate our approach using the OAI dataset<sup>[2](#page-58-0)</sup>, which contains 4, 796 patients between 45 to 79 years old at the time of recruitment. Each patient is longitudinally followed for up to 96 months, with separate evaluations for the left and right knees. Tabular data exists for all patients at every timepoint, but may contain missing attributes. Image data are less complete. See Appendix A for the distribution of input image data at different timepoints.

Our goal is to predict outcomes 24 month ahead, thus our inputs only include data up to 72 month. We use 6 timepoints for pain prediction and 5 timepoints for KLG prediction due to the absence of KLG labels at the 60 month timepoint. We randomly select 50% of the patients for training, 12.5% for validation, and the remainder for testing. We conduct all experiments 5 times with different seeds for training and report the mean  $\pm$  standard deviation for average precision (AP), AUC ROC  $(ROC)^3$  $(ROC)^3$ , and macro accuracy (Macro ACC) for combinations between tabular (T), femoral and tibial cartilage thickness maps (C), knee radiography (K), and pelvis radiography (P).

We evaluate our method on WOMAC pain and KLG prediction, where the labels range from  $0 \sim 20$  and  $0 \sim 4$ , respectively. For pain prediction, we define WOMAC< 5 as no pain and the rest as pain, while for KLG prediction, we merge KLG=  $0\&1$  since osteoarthritis is considered definitive only when KLG $\geq 2$  [\[14\]](#page-63-11).

<span id="page-58-0"></span> $^2$ [https://nda.nih.gov/oai/.](https://nda.nih.gov/oai/)

<span id="page-58-1"></span><sup>3</sup> AP and ROC are originally defined for binary classes. For KLG prediction with 4 classes, we use the one-vs-rest scheme.

<span id="page-59-0"></span>**Fig. 2.** Comparison of average precision scores between view-specific models and the models obtained via modality dropout from our unified model. The y-axis represents the combination of different views, e.g., TCKP represents using **t**abular, **c**artilage thickness maps, **k**nee radiography, and **p**elvis radiography.

## **4.2 Data Preprocessing**

Our dataset includes both tabular data and images. All images are resized to  $128 \times 128$  for feature extraction with augmentation of randomly rotating up to 15◦ and adding Gaussian noise. Data preprocessing details for each view are described below.

**Tabular (T).** We filter the tabular attributes from [\[12\]](#page-63-12) and keep only those that can be easily captured, leaving us with 17 attributes as detailed in Appendix B. An additional 'side' indicator is added to indicate if the left or the right knee is used for prediction. We fill in the missing entries with −1 for continuous attributes and 'Missing' for categorical attributes. Subsequently, all categorical values are encoded into numerical values for the ease of feature extraction.

**Cartilage Thickness Maps (C).** The femoral and tibial cartilage thickness maps are not directly available in the OAI dataset. They are extracted from the DESS MR images through cartilage segmentation, mesh extraction, registration to a common atlas, and 2D thickness projection as detailed in [\[10](#page-63-13)].

**Knee Radiography (K).** The knee radiographs encompass substantial areas of the femur and tibia. To extract the joint region, we employ the method proposed in [\[28\]](#page-64-7) for keypoint extraction. Then, a region of  $140mm \times 140mm$  is extracted. To mitigate potential noise introduced during image acquisition, we normalize the radiographs by linearly scaling the intensities such that the smallest 99% of values map to  $[0, 0.99]$ . Additionally, we apply horizontal flipping to all right knees [\[3](#page-62-3)] and add random contrast adjustments.

**Pelvis Radiography (P).** For pelvis radiographs, we extract a region of  $350mm \times 400mm$  through center cropping. Similar to the knee radiographs, we apply normalization and contrast adjustments during augmentation. We do not flip images because the pelvis radiographs show the entire pelvis, including both the left and right sides.

## **4.3 Network Training**

We use the SAINT model [\[27\]](#page-64-8) to extract features from tabular data and the ResNet18 model [\[9](#page-63-14)] for image feature extraction. During training, we freeze the first ResNet18 block to avoid overfitting but train all other blocks. We initialize ResNet18 using the pretrained ImageNet [\[6](#page-63-15)] parameters and train the SAINT model from scratch. Both our attention and decoder blocks use 6 layers of selfattention, each containing 8 heads. For all experiments, we train for 30 epochs with a batch size of 256. We use AdamW [\[21\]](#page-63-16) as the optimizer and the one cycle scheduler [\[26](#page-64-9)] with a maximum learning rate of  $1e-6$  for the tabular and cartilage thickness maps encoders and  $1e - 5$  for all remaining components for pain prediction. We increase the learning rate of all image encoders by a factor of 10 for KLG prediction. The network parameters resulting in the best average precision scores on the validation set were selected for testing.

<span id="page-60-0"></span>**Table 1.** Mean  $\pm$  STD of the average precision score for pain and KLG prediction for 96 month given varying numbers of previous timepoints.  $X \to Y$  represents using timepoints starting from month *X* to month *Y* . The last row presents results from our unified model, and all others are from our view-specific model.

|    |     | <b>Views</b> | Pain Prediction |                     |                                                                                                                | <b>KLG</b> Prediction |                                      |                        |  |  |
|----|-----|--------------|-----------------|---------------------|----------------------------------------------------------------------------------------------------------------|-----------------------|--------------------------------------|------------------------|--|--|
|    |     |              | $TCR$ $P$ $72$  | $24 \rightarrow 72$ | $0 \rightarrow 72$                                                                                             | 72                    | $24 \rightarrow 72$                  | $10 \rightarrow 72$    |  |  |
| ✓  |     |              |                 |                     | $[0.282 \pm 0.005   0.309 \pm 0.005  $ $\mathbf{0.321 \pm 0.006}   \mathbf{0.269 \pm 0.002}   0.268 \pm 0.003$ |                       |                                      | $0.269 \pm 0.002$      |  |  |
|    | ✓   |              |                 |                     | $0.270 \pm 0.005$ $0.276 \pm 0.003$ $0.280 \pm 0.002$ $0.513 \pm 0.011$                                        |                       | $0.526 \pm 0.007$                    | $0.542 \pm 0.006$      |  |  |
|    |     | V            |                 |                     | $0.289 \pm 0.002$ $0.304 \pm 0.004$ $0.307 \pm$ $0.003$ $0.525 \pm 0.014$                                      |                       | $0.549 \pm 0.014$                    | $0.555 + 0.014$        |  |  |
| ✓✓ |     |              |                 |                     | $0.314 \pm 0.006$ $0.329 \pm 0.006$ $0.337 \pm 0.006$ $0.532 \pm 0.019$                                        |                       | $0.557 \pm 0.012$ (0.556 $\pm$ 0.014 |                        |  |  |
| ✓  |     |              |                 |                     | $0.319 \pm 0.004$ (0.340 $\pm$ 0.003 (0.348 $\pm$ 0.004 (0.531 $\pm$ 0.004 )                                   |                       | $0.553 \pm 0.011$                    | $\ket{0.555\pm0.015}$  |  |  |
| ✓  |     |              |                 |                     | ✓ $0.280 \pm 0.007$ $0.311 \pm 0.005$ $0.321 \pm 0.006$ $0.269 \pm 0.003$                                      |                       | $0.276 + 0.004$                      | $0.282 + 0.005$        |  |  |
|    | ンノ  |              |                 |                     | $10.291 \pm 0.006$ 10.298 $\pm$ 0.0031 <b>0.303</b> $\pm$ <b>0.002</b> 10.528 $\pm$ 0.018                      |                       | $0.550 \pm 0.021$                    | $0.559 + 0.013$        |  |  |
|    | ノノノ |              |                 |                     | $10.325 \pm 0.004$ $0.337 \pm 0.003$ $0.342 \pm 0.003$ $0.559 \pm 0.017$                                       |                       | $0.580 \pm 0.013$                    | $\bm{0.587 \pm 0.015}$ |  |  |
| ノノ |     |              |                 |                     | $\mathcal{V}$ [0.312 $\pm$ 0.006[0.337 $\pm$ 0.005[ <b>0.345</b> $\pm$ <b>0.005</b> [0.532 $\pm$ 0.013]        |                       | $0.567 \pm 0.007$                    | $\bm{0.576 \pm 0.016}$ |  |  |
| ✓  |     |              |                 |                     | $\mathcal{U} \mathcal{U} 0.318 \pm 0.013 \, 0.342 \pm 0.005 \, 0.349 \pm 0.004 \, 0.526 \pm 0.013$             |                       | $0.548 \pm 0.021$                    | $\ket{0.550\pm0.019}$  |  |  |
|    |     |              |                 |                     | $\sqrt{\sqrt{0.290} \pm 0.002}$ (0.300 ± 0.002 <b>0.305</b> ± <b>0.004</b> [0.539 ± 0.021                      |                       | $0.550 \pm 0.026$                    | $0.567 + 0.021$        |  |  |
|    |     |              |                 |                     | $\sqrt{\sqrt{2}}$ (0.322 ± 0.0020.333 ± 0.0070.339 ± 0.0070.541 ± 0.020                                        |                       | $0.573 \pm 0.013$                    | $0.579 + 0.015$        |  |  |
|    |     |              |                 |                     | $\nabla$ $\nabla$ 0.327 $\pm$ 0.007 0.342 $\pm$ 0.007 <b>0.346</b> $\pm$ <b>0.003</b> 0.586 $\pm$ 0.015        |                       | $0.606 \pm 0.014$ (0.600 $\pm$ 0.012 |                        |  |  |

## **4.4 Results**

In this section, we show our experimental results by investigating the following: *Can our unified model, trained with all available views, perform on par with viewspecific models during evaluation?* To assess the generalizability of our unified model to fewer views during testing, we excluded up to two views at a time and compared the results with view-specific models (results for each model can be found in Appendix C). Figure [2](#page-59-0) and Appendix D show bar chart comparisons. We observe that our unified model performs on par with view-specific models while providing the flexibility to use views whenever available. Note that when cartilage thickness maps are not available, our unified model performs slightly worse than view-specific models but generally better when they are present (regardless of what other views are missing). In particular, this is the case for KLG prediction. We hypothesize that this is due to the ease in predicting higher KLG (which involves cartilage degradation/thinning) from cartilage thickness maps. Further insights into this phenomenon are provided below.

*Does an extended observation period enhance prediction results?* In order to show the benefit of using longitudinal data, we predict results at the final 96 month timepoint, varying the number of previous timepoints provided. Table [1](#page-60-0) shows that in most cases, it is beneficial to include an increased number of timepoints, underlining the importance of longitudinal data in improving predictive accuracy. However, few exceptions appear for predicting KLG with the inclusion of tabular data. This can be attributed to the limitations of tabular data in providing information for KLG, which is scored only based on knee radiographs.

<span id="page-61-0"></span>**Fig. 3.** Visualization of the most influential view for pain and KLG prediction. Left: Percentage of data where the view is deemed the most influential. Right: Normalized heatmaps showing the most influential view for each class.

*How significant is each view in contributing to our unified model?* Having a unified model allows us to easily determine the most pivotal view. We systematically exclude one at a time and observe the resulting change in prediction scores relative to the gold-standard labels. The view whose exclusion led to the worst performance is then considered the most crucial. Figure [3a](#page-61-0) shows that knee radiography emerged as the primary view for both tasks, followed by knee cartilage thickness maps. Tabular emerged as being helpful for pain prediction.

*Are there patterns between view importance and different prediction labels?* Similar to assessing overall view significance, our unified model easily allows us to assess the view importance stratified by class. Figure [3b](#page-61-0) shows this view importance normalized by the number of instances in each class. We observe that knee radiography is the pivotal view in predicting non-pain instances, while tabular data emerged as more influential for instances associated with pain. For KLG prediction, cartilage thickness maps notably influenced higher grades, whereas knee images played a crucial role in lower grades. This observation aligns with the understanding that cartilage thinning becomes increasingly apparent with higher KLG, a characteristic readily discernible from cartilage thickness maps. Pelvis radiography emerged as the least impactful view across both experiments.

# **5 Conclusion**

In this work, we proposed a unified model for longitudinal multi-modal multiview prediction with missingness. Our model offers flexibility by accommodating various modalities, views, and timepoints, while adeptly handling missing data in the input. Through evaluation on the OAI dataset, we show the advantages of our unified model being able to generalize to different view combinations during evaluation. We also demonstrated the benefit of incorporating longitudinal data. Further, having a unified model allows us to easily probe and analyze the importance of different views for different prediction tasks. Future directions could include expanding the scope of our model by incorporating additional views. We also aim to implement an automatic view pruning technique, ensuring optimal prediction performance with the least number of views acquired.

**Acknowledgments.** This work was supported by NSF grants IIS2133595, DMS2324394, and NIH grants 1R01AR072013, 1R01AR082684, 1R01AA02687901A1, 1OT2OD032581-02-321. The work expresses the views of the authors, not of the NSF or NIH. The knee data were obtained from the controlled access datasets distributed from the Osteoarthritis Initiative (OAI), a data repository housed within the NIMH Data Archive. OAI is a collaborative informatics system created by NIMH and NIAMS to provide a worldwide resource for biomarker identification, scientific investigation and OA drug development. Dataset identifier: NIMH Data Archive Collection ID: 2343.

**Disclosure of Interests.** The authors have no competing interests.

## **References**

- <span id="page-62-2"></span>1. Bhagwat, N., Viviano, J.D., Voineskos, A.N., Chakravarty, M.M., Initiative, A.D.N., et al.: Modeling and prediction of clinical symptom trajectories in Alzheimer's disease using longitudinal data. PLoS computational biology **14**(9), e1006376 (2018)
- <span id="page-62-1"></span>2. Cascarano, A., Mur-Petit, J., Hernandez-Gonzalez, J., Camacho, M., de Toro Eadie, N., Gkontra, P., Chadeau-Hyam, M., Vitria, J., Lekadir, K.: Machine and deep learning for longitudinal biomedical data: a review of methods and applications. Artificial Intelligence Review **56**(Suppl 2), 1711–1771 (2023)
- <span id="page-62-3"></span>3. Chen, B., Niethammer, M.: MRIS: A multi-modal retrieval approach for image synthesis on diverse modalities. In: MICCAI. pp. 271–281. Springer (2023)
- <span id="page-62-0"></span>4. Chen, X., Wang, X., Zhang, K., Fung, K.M., Thai, T.C., Moore, K., Mannel, R.S., Liu, H., Zheng, B., Qiu, Y.: Recent advances and clinical applications of deep learning in medical image analysis. MedIA **79**, 102444 (2022)

- <span id="page-63-9"></span>5. Cui, R., Liu, M., Initiative, A.D.N., et al.: RNN-based longitudinal analysis for diagnosis of Alzheimer's disease. Computerized Medical Imaging and Graphics **73**, 1–10 (2019)
- <span id="page-63-15"></span>6. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A large-scale hierarchical image database. In: CVPR. pp. 248–255 (2009)
- <span id="page-63-8"></span>7. Gibbons, R.D., Hedeker, D., DuToit, S.: Advances in analysis of longitudinal data. Annual review of clinical psychology **6**, 79–107 (2010)
- <span id="page-63-2"></span>8. Girdhar, R., El-Nouby, A., Liu, Z., Singh, M., Alwala, K.V., Joulin, A., Misra, I.: Imagebind: One embedding space to bind them all. In: CVPR. pp. 15180–15190 (2023)
- <span id="page-63-14"></span>9. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: CVPR. pp. 770–778 (2016)
- <span id="page-63-13"></span>10. Huang, C., Xu, Z., Shen, Z., Luo, T., Li, T., Nissman, D., Nelson, A., Golightly, Y., Niethammer, M., Zhu, H.: DADP: dynamic abnormality detection and progression for longitudinal knee magnetic resonance images from the osteoarthritis initiative. MedIA **77**, 102343 (2022)
- <span id="page-63-0"></span>11. Ikram, M.A., Brusselle, G., Ghanbari, M., Goedegebure, A., Ikram, M.K., Kavousi, M., Kieboom, B.C., Klaver, C.C., de Knegt, R.J., Luik, A.I., et al.: Objectives, design and main findings until 2020 from the Rotterdam study. European journal of epidemiology **35**, 483–517 (2020)
- <span id="page-63-12"></span>12. Keefe, T.H., Minnig, M.C., Arbeeva, L., Niethammer, M., Xu, Z., Shen, Z., Chen, B., Nissman, D.B., Golightly, Y.M., Marron, J., et al.: Patterns of variation among baseline femoral and tibial cartilage thickness and clinical features: Data from the osteoarthritis initiative. Osteoarthritis and Cartilage Open **5**(1), 100334 (2023)
- <span id="page-63-5"></span>13. Kellgren, J.H., Lawrence, J.: Radiological assessment of osteoarthrosis. Annals of the rheumatic diseases **16**(4), 494 (1957)
- <span id="page-63-11"></span>14. Kohn, M.D., Sassoon, A.A., Fernando, N.D.: Classifications in brief: Kellgren-Lawrence classification of osteoarthritis. Clinical Orthopaedics and Related Research **474**, 1886–1893 (2016)
- <span id="page-63-10"></span>15. Li, Y., Mamouei, M., Salimi-Khorshidi, G., Rao, S., Hassaine, A., Canoy, D., Lukasiewicz, T., Rahimi, K.: Hi-BEHRT: Hierarchical transformer-based model for accurate prediction of clinical events using multimodal longitudinal electronic health records. IEEE journal of biomedical and health informatics **27**(2), 1106– 1117 (2022)
- <span id="page-63-3"></span>16. Lin, Y., Gou, Y., Liu, Z., Li, B., Lv, J., Peng, X.: Completer: Incomplete multi-view clustering via contrastive prediction. In: CVPR. pp. 11174–11183 (2021)
- <span id="page-63-1"></span>17. Littlejohns, T.J., Sudlow, C., Allen, N.E., Collins, R.: UK biobank: opportunities for cardiovascular research. European heart journal **40**(14), 1158–1166 (2019)
- <span id="page-63-6"></span>18. Liu, C., Wen, J., Luo, X., Huang, C., Wu, Z., Xu, Y.: DICNet: Deep instancelevel contrastive network for double incomplete multi-view multi-label classification. [arXiv:2303.08358](http://arxiv.org/abs/2303.08358) (2023)
- <span id="page-63-7"></span>19. Liu, C., Wen, J., Luo, X., Xu, Y.: Incomplete multi-view multi-label learning via label-guided masked view-and category-aware transformers. [arXiv:2303.07180](http://arxiv.org/abs/2303.07180) (2023)
- <span id="page-63-4"></span>20. Liu, Y., Yue, L., Xiao, S., Yang, W., Shen, D., Liu, M.: Assessing clinical progression from subjective cognitive decline to mild cognitive impairment with incomplete multi-modal neuroimages. MedIA **75**, 102266 (2022)
- <span id="page-63-16"></span>21. Loshchilov, I., Hutter, F.: Decoupled weight decay regularization. [arXiv:1711.05101](http://arxiv.org/abs/1711.05101) (2017)

- <span id="page-64-1"></span>22. Martí-Juan, G., Sanroma-Guell, G., Piella, G.: A survey on machine and statistical learning for longitudinal analysis of neuroimaging data in Alzheimer's disease. Computer methods and programs in biomedicine **189**, 105348 (2020)
- <span id="page-64-2"></span>23. McConnell, S., Kolopack, P., Davis, A.M.: The Western Ontario and McMaster universities osteoarthritis index (WOMAC): a review of its utility and measurement properties. Arthritis Care & Research: Official Journal of the American College of Rheumatology **45**(5), 453–461 (2001)
- <span id="page-64-0"></span>24. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: ICML. pp. 8748–8763 (2021)
- <span id="page-64-5"></span>25. Radford, A., Wu, J., Child, R., Luan, D., Amodei, D., Sutskever, I., et al.: Language models are unsupervised multitask learners. OpenAI blog **1**(8), 9 (2019)
- <span id="page-64-9"></span>26. Smith, L.N., Topin, N.: Super-convergence: Very fast training of neural networks using large learning rates. In: Artificial intelligence and machine learning for multidomain operations applications. vol. 11006, pp. 369–386 (2019)
- <span id="page-64-8"></span>27. Somepalli, G., Goldblum, M., Schwarzschild, A., Bruss, C.B., Goldstein, T.: Saint: Improved neural networks for tabular data via row attention and contrastive pretraining. [arXiv:2106.01342](http://arxiv.org/abs/2106.01342) (2021)
- <span id="page-64-7"></span>28. Tiulpin, A., Melekhov, I., Saarakkala, S.: KNEEL: Knee anatomical landmark localization using hourglass networks. In: ICCV Workshop. pp. 0–0 (2019)
- <span id="page-64-6"></span>29. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. NeurIPS **30** (2017)
- <span id="page-64-3"></span>30. Xu, J., Tang, H., Ren, Y., Peng, L., Zhu, X., He, L.: Multi-level feature learning for contrastive multi-view clustering. In: CVPR. pp. 16051–16060 (2022)
- <span id="page-64-4"></span>31. Zhou, Q., Zou, H., Jiang, H., Wang, Y.: Incomplete multimodal learning for visual acuity prediction after cataract surgery using masked self-attention. In: MICCAI. pp. 735–744 (2023)

# An Approach to Building Foundation Models for Brain Image Analysis

Davood Karimi<sup>( $\boxtimes$ )</sup>

Computational Radiology Laboratory, Department of Radiology Boston Children's Hospital, and Harvard Medical School, Boston, MA, USA <EMAIL>

Abstract. Existing machine learning methods for brain image analysis are mostly based on supervised training. They require large labeled datasets, which can be costly or impossible to obtain. Moreover, the trained models are useful only for the narrow task defined by the labels. In this work, we developed a new method, based on the concept of foundation models, to overcome these limitations. Our model is an attentionbased neural network that is trained using a novel self-supervised approach. Specifically, the model is trained to generate brain images in a patch-wise manner, thereby learning the brain structure. To facilitate learning of image details, we propose a new method that encodes highfrequency information using convolutional kernels with random weights. We trained our model on a pool of 10 public datasets. We then applied the model on five independent datasets to perform segmentation, lesion detection, denoising, and brain age estimation. Results showed that the foundation model achieved competitive or better results on all tasks, while significantly reducing the required amount of labeled training data. Our method enables leveraging large unlabeled neuroimaging datasets to effectively address diverse brain image analysis tasks and reduce the time and cost requirements of acquiring labels.

Keywords: foundation models  $\cdot$  neuroimaging  $\cdot$  deep learning  $\cdot$  brain

# **1 Introduction**

Existing machine learning methods for brain image analysis have three critical shortcomings: (1) They are mostly trained with supervised learning, which requires large labeled datasets. It can be costly or impossible to collect such datasets, for example for segmenting complex structures or detecting rare abnormalities. (2) Trained models are restricted to a narrowly-defined task. For example, a model trained to detect stroke lesions may be useless for detecting tumors. (3) They fail to leverage massive unlabeled neuroimaging datasets that are becoming increasingly available. Therefore, the goal of this work is to develop a new approach, based on foundation models, to overcome these limitations.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_40.](https://doi.org/10.1007/978-3-031-72390-2_40)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 421–431, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_40)\_40

## **1.1 Review of Related Works**

Not-Fully-Supervised Methods: There is much interest in unsupervised, self-supervised, and related machine learning methods for medical image analysis [\[11](#page-73-0),[22,](#page-74-0)[23\]](#page-74-1). These methods can reduce/eliminate the need for labeled data. For brain abnormality detection, as an example, prior unsupervised works include reconstruction-based and representation-based methods [\[4](#page-73-1),[10\]](#page-73-2). However, in general, prior works have failed to perform as well as supervised methods [\[5](#page-73-3)[,31](#page-74-2)].

Foundation Models: The central idea behind foundation models is to leverage massive unlabeled data to develop powerful "general-purpose" models, which can subsequently tackle a wide range of tasks  $[6,46]$  $[6,46]$  $[6,46]$ . Foundation models are trained using self-supervised techniques. They have become dominant in natural language processing (NLP) and computer vision [\[12](#page-73-5),[14\]](#page-74-3) and offer important advantages: (i) They do not require collecting and labeling a large dataset for each new task. (ii) They can learn patterns that are not restricted by a specific label information or tied to a specific task. (iii) They can have better performance on rare events and corrupted or out-of-distribution data [\[6,](#page-73-4)[30](#page-74-4)].

Vision Transformers and Their Limitations: Attention-based models such as vision transformers have emerged as alternatives to CNNs for image analysis [\[13](#page-73-6),[24\]](#page-74-5). They are superior in learning spatial correlations. However, they also have drawbacks such as higher computational and memory requirements and lower data efficiency. A limitation of these models that we consider in this work is that they are not effective in learning high-frequency information [\[2](#page-73-7)[,32](#page-74-6)]. Hence, they are not good at reconstructing sharp features, i.e., edges and texture, which can impact the performance in image analysis tasks.

## **1.2 Contributions**

(1) We propose a framework for developing foundation models for brain image analysis. Our model is based on a transformer network that is trained in a selfsupervised manner to *generate* brain images in a patch-wise fashion, thereby learning the detailed structure of brain images. Our novel training strategy is based on auto-regressive prediction and random input masking, both applied at the level of 3D patches. (2) To facilitate learning of high-frequency detail, we propose a novel approach based on convolutional kernels with random weights. (3) We train the model on 10 unlabeled datasets. We then apply the model on five different tasks and show that it can achieve competitive or better results than the state of the art while significantly reducing the required labeled data.

# **2 Methods**

As shown in Fig. [1,](#page-68-0) our method consists of a transformer network that is trained to generate brain images in a patch-wise fashion. It uses a set of 3D patches

as the input context to predict an adjacent patch. This way, the model learns the detailed structure of brain images. Our method is based on a simple but powerful idea: *normal brains are very similar*. In other words, brain structure is characterized by features (e.g., cortical foldings and subcortical structures) that are similar across brains. Hence, a well-devised model can learn these structures after being trained in a self-supervised manner on large datasets to generate these images. In this sense, our approach follows the NLP foundation models that are trained to predict the next word in text. The method works as follows:

- 1. Given an image, cubes of size  $D^3$  are extracted, where D is in voxels. The cube is divided into patches of size  $d^3$ , where  $d=D/n$ , resulting in  $n^3$  patches.
- 2. The corner patch (red in Fig. [1\)](#page-68-0) is the prediction target. The remaining  $n^3 -$ 1 patches (yellow/orange in Fig. [1\)](#page-68-0) constitute the input context, which the model uses to predict the target. The input patches go through high-frequency feature encoding (block  $\bf{F}$  in Fig. [1\)](#page-68-0), described further below.
- 3. Frequency encoding is followed by embedding into  $\mathbb{R}^m$ , resulting in a sequence S of length  $n^3 - 1$ , where each element is in  $\mathbb{R}^m$ . Positional encoding (P<sub>2</sub> in Fig. [1\)](#page-68-0) is added to S and then the elements of S are masked (i.e., removed) at random (block M). For positional encoding, we use fixed (i.e., non-learned) encodings as in standard transformers [\[28,](#page-74-7)[43\]](#page-75-1). For masking, we remove each element independently with a probability  $p$ . This generates a shorter sequence  $S^*$  of length  $n^*$ .
- 4. The reduced sequence  $S^*$  is passed to a network (**T** in Fig. [1\)](#page-68-0), consisting of  $K_{tr}$  transformer blocks with architecture similar to those in [\[20](#page-74-8)].
- 5. The output of the last transformer block goes through unmasking (U in Fig. [1\)](#page-68-0). This simply restores the sequence length to  $n^3 - 1$  based on the same masking pattern used to generate  $S^*$ . The masked elements are given a value of 0 in this stage. The sequence is passed to  $K_{\text{fc}}$  fully-connected layers.
- 6. The output of the last fully connected layer is projected back to  $\mathbb{R}^{d^3}$  and reshaped  $(R \text{ in Fig. 1})$  to form the model's prediction of the target patch.

High-frequency encoding: To address transformer's limitation in modeling highfrequency information, we encode this information using convolutional kernels with random weights. Neural networks with random weights are highly effective in extracting high-frequency information  $[9,35]$  $[9,35]$  $[9,35]$ . Our aim is to design q kernels  ${k_i}_{i=1:q}$  such that there is low redundancy among the feature maps computed by the set. We first generate a much larger set of candidates  ${k_i^*}_{i=1:Q}$ , where  $Q$  ≫  $q$ , using the initialization method proposed in [\[15\]](#page-74-10). We apply  ${k_i^*}_{i=1:Q}$  on some training images  $(x)$  to generate feature maps  $f_i^*(x) = k_i^* \otimes x$ . We quantify the similarity between pairs of feature maps  $(f_i^*(x)$  and  $f_i^*(x))$  using projection weighted Canonical Correlation Analysis (pwCCA) [\[27](#page-74-11)], which we denote as  $\rho_{ij}$ . We select the subset of q kernels in a greedy manner: (step 1) We choose  $k_1$  to be  $k_1^*$ ; (step 2) We choose  $k_2$  from  $\{k_i^*\}_{i=2:Q}$  such that  $\rho_{1i}$  is the lowest, i.e.,  $f_i$ is least similar to  $f_1$ ; (steps 3-q) We proceed in a similar manner, in every step choosing the kernel that has the lowest maximum similarity (lowest maximum  $\rho$ ) compared with already-selected kernels. We used  $q = 8$  and  $Q = 100$ . To avoid

<span id="page-68-0"></span>Fig. 1. Proposed method. F: Frequency encoding block, where  $f_1$ - $f_n$  denote different frequency encodings and  $P_1$  is positional encoding. M: Positional encoding and masking, where  $P_2$  is positional encoding and  $B$  denotes a Bernoulli random process used to mask the sequence. T: Vision transformer block. U: Unmasking. R: Reshaping.

increasing the dimensionality of the network input, we *add* the high-frequency features to the input. Specifically, if  $x$  is the input patch to the high-frequency encoding block, the output is  $x + \sum_i P_{1i} k_i \otimes x$ , where  $P_1$  is fixed "positional" encoding [\[43](#page-75-1)]. Note that these kernels are fixed and not updated during training. Training approach: Our novel training approach is based on two dominant themes in self-supervised learning  $[1,12]$  $[1,12]$  $[1,12]$ : (1) Generative methods: they train the model to predict masked/corrupted parts of the input. (2) Invariance-based methods: they train the model to compute similar representations for different *views* of the same input. As shown in Fig. [1,](#page-68-0) our method is based on autoregressive prediction of a missing (corner) patch; hence it follows a generative approach. However, we also randomly mask the input patches, thereby giving the model a different view of the input in each training iteration. Each element in S is masked based on a Bernoulli distribution with probability  $p$ . The sequence is unmasked to its original length before the fully connected layers, which serve as the decoder section of the network. This approach forces the model to predict the target patch based on a random selection of non-masked patches, which is an invariance-based method and, additionally, serves as data augmentation. Implementation: We trained our model on a pool of 10 public datasets  $[7,8,$  $[7,8,$  $[7,8,$ [16](#page-74-12)[,29](#page-74-13),[33,](#page-74-14)[36,](#page-74-15)[37](#page-75-2)[,39](#page-75-3)[–41\]](#page-75-4). From these datasets, we used 11,000 structural MRI and 12,000 diffusion MRI (dMRI) volumes (acquired with different diffusion gradient directions and strengths). Parameter settings were (see Fig. [1\)](#page-68-0):  $D = 32$ ,  $d = 8$ ,  $n= 4, m= 512, p= 0.10, K_{tr} = 5, K_{fc} = 2.$  We used the  $\ell_1$  norm of the difference between the predicted and true voxel intensity of the target patch as the loss function. During training, we sample cubes from random locations in the training images and use them to optimize the model. On a test image, we start at one corner of the image and proceed in a sliding-window fashion to predict the image. To predict all patches that are close to the brain boundaries, we apply the model

in different directions (i.e., left/right, superior/inferior, and anterior/posterior).

We trained the model on an Nvidia RTX A4500 GPU for 15 d. We used a batch size of 10, SGD optimizer with a learning rate of 0.001.

Two factors may influence the model's performance and generalizability: (i) Resolution. We account for this factor by resampling all images to an isotropic resolution of 1 mm. (ii) Intensity. We normalize each image such that the voxel intensities have a mean of zero and standard deviation of one.

### **2.1 Experiments and Evaluation Strategy**

In order to perform *extrinsic evaluation* [\[6](#page-73-4)] of our foundation model, we applied the trained model on five different downstream tasks.

Task 1- Cortical plate segmentation. We used 100 T2 images and cortical plate segmentations from the dHCP dataset [\[3](#page-73-12)]. We compared the foundation model with nnU-Net [\[17](#page-74-16)] and a transformer model [\[21\]](#page-74-17). Both these competing networks were trained in a fully-supervised manner.

Task 2- White matter tract segmentation. We used tract segmentation data from the TractSeg project [\[45\]](#page-75-5). We focused on three tracts: corticospinal tract (CST), middle cerebellar peduncle (MCP), and optic radiation (OPR). We compared with nnU-Net, trained in a fully-supervised manner.

Task 3- dMRI denoising. We used dMRI scans of 100 subjects from the HCP Development dataset [\[38](#page-75-6)]. Compared methods included: MPPCA [\[44](#page-75-7)], which is a widely used method based on random matrix theory, and SDnDTI [\[42\]](#page-75-8), which is a recent deep learning method.

Task 4- Lesion detection. We used a dataset of 30 acute stroke patients [\[34\]](#page-74-18). We compared with an unsupervised technique based on variational autoencoders (VAE) [\[5\]](#page-73-3).

Task 5- Brain age estimation. We used 300 T2 images from the dHCP dataset [\[3](#page-73-12)]. We compared our foundation model with a residual CNN (ResCNN) [\[19](#page-74-19)].

Fine-tuning and application to the target tasks: For segmentation (Tasks 1 and 2) and brain age estimation (Task 5), we fine-tuned the foundation model on small numbers of labeled data from the target tasks (details given below). For these tasks, the last layer of the network had to be modified to output the correct size (For Tasks 1 and 2:  $\mathbb{R}^{d^3 \times 2}$  to represent the foreground and background segmentation predictions; For Task 5: a scalar to represent the brain age). For Tasks 1 and 2 we fine-tuned the model using a cross-entropy loss; for Task 5 we fine-tuned with an  $\ell_2$  loss. Fine-tuning was performed on the output layer and fully-connected layers; we did not fine-tune the transformer blocks. For Tasks 3 and 4 no fine-tuning was performed. For Task 3 (lesion detection) we expected that the model trained on normal brain images would display large reconstruction errors on brain lesions due to their deviation from normal brain appearance. This is the common assumption in abnormality detection  $[4,5]$  $[4,5]$  $[4,5]$ . Hence, we applied our model on a test image, computed the reconstruction error, and used an empirical threshold of 1.50 to detect the lesions. For Task 4, following a similar argument, we expected that the trained model learned to reconstruct the true dMRI signal and not the random noise. This is the rationale behind deep learning-based image denoising [\[25](#page-74-20)[,26](#page-74-21)]. Hence, we applied the foundation model on the test images and used the model predictions as the denoised image.

Evaluation metrics: For segmentation (Tasks 1 and 2) we used Dice Similarity Coefficient (DSC), 95 percentile of the Hausdorff Distance (HD95), and Average Symmetric Surface Distance (ASSD). We assessed the denoising performance (Task 3) in two ways: (i) We used all 94 measurements in the  $b=1500$  shell to estimate a "ground truth" diffusion tensor. Then, we chose 15 measurements from these 94 measurements, denoised them, estimated the diffusion tensor using the denoised measurements, and computed fractional anisotropy (FA) and mean diffusivity (MD) from the tensor and compared these with FA and MD computed from the ground truth diffusion tensor. We denote the difference with the ground truth as  $\Delta$ FA and  $\Delta$ MD. (ii) We divided the 186 dMRI measurements (in b=1500 and b=3000 shells) for each subject into two subsets of 93 measurements. We used MSMT-CSD [\[18](#page-74-22)] to compute the fiber orientation distribution from each subset and extracted the peak orientation direction for voxels in white matter. We computed the angle between the peaks estimated from the two subsets, denoted as  $\Delta\theta$ , as a measure of disagreement that is caused, in part, by residual noise. For lesion detection (Task 4) we used F1 score. For Task 5, we computed the error as the difference between the predicted age and the true age.

## **3 Results and Discussion**

*Intrinsic evaluation* [\[6\]](#page-73-4): As shown in Fig. [2,](#page-70-0) our model can accurately reconstruct test images. A neuroradiologist reviewed pairs of true and reconstructed images in a blind fashion and confirmed that the images were indistinguishable even in minute details. Quantitatively, on 25 test images from the HCP dataset our method achieved a voxel-wise reconstruction error of  $0.098 \pm 0.024$ , compared with  $0.132 \pm 0.026$  for an auto-encoder model [\[4\]](#page-73-1). The difference was statistically significant (p < 0.001, computed with a paired t-test) *despite the fact that*, unlike the auto-encoder, our model did not use the target patch in its context input.

<span id="page-70-0"></span>Fig. 2. Example test images reconstructed by the proposed method. Left: a T2 image from the dHCP dataset; Right: a T1 image from the HCP dataset.

Task 1: With only 15 labeled training images in the target domain, our foundation model achieved segmentation metrics on par with the competing methods trained with 50–250 labeled images (example plot shown in Fig.  $3(a)$  $3(a)$ ). Figure  $3(b)$  shows that the foundation model can achieve highly accurate segmentation with few labeled images in the target domain. Paired t-tests for DSC, HD95, and ASSD showed that for every number of labeled training images the foundation model achieved significantly  $(p < 0.001)$  more accurate results than the two compared methods. Moreover, DSC, HD95, and ASSD for the foundation model fine-tuned with 15 labeled images were not different from the results achieved by the two compared methods trained with 250 labeled images  $(p > 0.10)$ .

<span id="page-71-0"></span>Fig. 3. (a) Plots of DSC for our foundation model and competing methods on Task 1. (b) Example results for the foundation model fine-tuned with 15 labeled images. (Green voxels: correct segmentation, blue: over-segmentation, red: under-segmentation.) (Color figure online)

Task 2: As Table [1](#page-71-1) shows, with fewer labeled images in the target domain, the foundation model achieved segmentation performance that was comparable with or better than nnU-Net. For all three tracts and all three numbers of labeled images, the foundation model achieved significantly higher DSC than nnU-Net  $(p < 0.001$ , computed with paired t-tests). For all three tracts, the DSC achieved with foundation model using 15 labeled images was not different  $(p > 0.10)$  than that of nnU-Net with 50 labeled images. As shown in Fig. [4,](#page-72-0) the foundation model can segment diverse and complex structures from few labeled images.

Table 1. DSC for the proposed method and nnU-Net on Task 2. *m* denotes the number of labeled images used to fine-tune our foundation model and train nnU-Net.

<span id="page-71-1"></span>

| Method           | m = 5 |       |       | m = 15 |       |       | m = 50 |       |       |
|------------------|-------|-------|-------|--------|-------|-------|--------|-------|-------|
|                  | CST   | MCP   | OPR   | CST    | MCP   | OPR   | CST    | MCP   | OPR   |
| Foundation model | 0.871 | 0.831 | 0.786 | 0.874  | 0.840 | 0.797 | 0.882  | 0.851 | 0.810 |
| nnU-Net          | 0.814 | 0.807 | 0.718 | 0.850  | 0.822 | 0.790 | 0.864  | 0.833 | 0.800 |

Task 3: Examples in Fig. [5](#page-72-1) show that our foundation model, without any finetuning on the target task data, can effectively reconstruct the genuine dMRI signal while suppressing the noise. Table [2](#page-72-2) shows that the foundation model achieves comparable results with the other two methods. All three methods

<span id="page-72-0"></span>Fig. 4. Example CST, MCP, and OPR segmented with the foundation model fine-tuned on 15 images. Green: correct; blue: false positive; red: false negative. (Higher magnification image in supp. material.) (Color figure online)

<span id="page-72-1"></span>Fig. 5. Example dMRI denoising results by the foundation model in Task 3. Left: noisy; right: denoised.(Higher magnification image in supp. material.)

achieved  $\Delta$ FA and  $\Delta$ MD that were statistically not different ( $p > 0.10$ ). In terms of  $\Delta\theta$ , our model achieved a statistically smaller error than SDnDTI ( $p < 0.001$ ). While our method was not fine-tuned on any training data from the target dataset, SDnDTI was trained with 50 images.

Table 2. Results for Task 3.

<span id="page-72-2"></span>

| Method   | $\delta$ FA | $\delta$ MD | $\delta\theta$ (°) |
|----------|-------------|-------------|--------------------|
| Proposed | 0.031       | 0.039       | 4.20               |
| SDnDTI   | 0.031       | 0.041       | 4.29               |
| MPPCA    | 0.030       | 0.039       | 4.24               |

Table 3. Mean absolute error (weeks) in brain age estimation (Task 5).

<span id="page-72-3"></span>

| Method   | m = 100 | m = 200 | m = 400 |
|----------|---------|---------|---------|
| Proposed | 0.87    | 0.86    | 0.86    |
| ResCNN   | 1.03    | 0.90    | 0.89    |

Task 4: Foundation model achieved an F1 score of 0.688 compared with 0.605 for VAE. Wilcoxon signed-rank test showed that the difference was significant  $(p < 0.001)$ .

Task 5: Table [3](#page-72-3) shows that our method fine-tuned with 100 images achieved a lower prediction error than ResCNN trained with 400 images. With each of the three different numbers of training images in the target domain, our method achieved significantly  $(p < 0.001)$  lower prediction errors than ResCNN.

Ablation experiments: Extensive experiments showed a substantial positive impact for the proposed high-frequency encoding approach. For example, disabling the high-frequency encoding in Tasks 1 and 2 reduced the DSC achieved by the method such that it was no longer significantly better than nnU-Net. Disabling the high-frequency encoding in Task 4 reduced our F1 score to 0.550, making it not significantly better than VAE.

## **4 Conclusions**

Our results show that the proposed method can be used to develop foundation models to address a wide range of brain image analysis tasks. The model size and datasets used in this work were much smaller than in current foundation

models in NLP and computer vision. Therefore, we believe the results reported in this paper will significantly improve by employing larger models and datasets. Nonetheless, our results show that our method can lead to (1) improvements in the accuracy of machine learning methods in analyzing brain images; (2) reduction in labeled data requirements for building new machine learning methods to address existing problems and emerging needs; (3) expansion of the range of brain image analysis tasks that can be addressed with machine learning, such as detection of rare abnormalities, where training data can be very scarce.

Acknowledgments. This study was supported in part by the National Institute of Neurological Disorders and Stroke and Eunice Kennedy Shriver National Institute of Child Health and Human Development of the National Institutes of Health (NIH) under award numbers R01HD110772 and R01NS128281. The content of this publication is solely the responsibility of the author and does not necessarily represent the official views of the NIH.

Disclosure of Interests. The author has no competing interests to declare that are relevant to the content of this article.

## **References**

- <span id="page-73-9"></span>1. Assran, M., et al.: Masked siamese networks for label-efficient learning. In: European Conference on Computer Vision. pp. 456–473. Springer (2022)
- <span id="page-73-7"></span>2. Basri, R., et al.: Frequency bias in neural networks for input of non-uniform density. In: International Conference on Machine Learning. pp. 685–694. PMLR (2020)
- <span id="page-73-12"></span>3. Bastiani, M., et al.: Automated processing pipeline for neonatal diffusion mri in the developing human connectome project. NeuroImage 185, 750–763 (2019)
- <span id="page-73-1"></span>4. Baur, C., et al.: Deep autoencoding models for unsupervised anomaly segmentation in brain mr images. In: 4th International Workshop, BrainLes 2018, Held in Conjunction with MICCAI 2018. pp. 161–169. Springer (2019)
- <span id="page-73-3"></span>5. Baur, C., et al.: Autoencoders for unsupervised anomaly segmentation in brain mr images: a comparative study. Medical Image Analysis 69, 101952 (2021)
- <span id="page-73-4"></span>6. Bommasani, R., et al.: On the opportunities and risks of foundation models. arXiv preprint [arXiv:2108.07258](http://arxiv.org/abs/2108.07258) (2021)
- <span id="page-73-10"></span>7. Bookheimer, S.Y., et al.: The lifespan human connectome project in aging: an overview. Neuroimage 185, 335–348 (2019)
- <span id="page-73-11"></span>8. Botvinik-Nezer, R., et al.: Paingen placebo. OpenNeuro (2023). [https://doi.org/](https://doi.org/10.18112/openneuro.ds004746.v1.0.1) [10.18112/openneuro.ds004746.v1.0.1](https://doi.org/10.18112/openneuro.ds004746.v1.0.1)
- <span id="page-73-8"></span>9. Cao, W., Wang, X., Ming, Z., Gao, J.: A review on neural networks with random weights. Neurocomputing 275, 278–287 (2018)
- <span id="page-73-2"></span>10. Chen, X., Konukoglu, E.: Unsupervised detection of lesions in brain mri using constrained adversarial auto-encoders. arXiv preprint [arXiv:1806.04972](http://arxiv.org/abs/1806.04972) (2018)
- <span id="page-73-0"></span>11. Cheplygina, V., et al.: Not-so-supervised: a survey of semi-supervised, multiinstance, and transfer learning in medical image analysis. Medical image analysis 54, 280–296 (2019)
- <span id="page-73-5"></span>12. Devlin, J., et al.: Bert: Pre-training of deep bidirectional transformers for language understanding. arXiv preprint [arXiv:1810.04805](http://arxiv.org/abs/1810.04805) (2018)
- <span id="page-73-6"></span>13. Dosovitskiy, A., et al.: An image is worth  $16 \times 16$  words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)

- <span id="page-74-3"></span>14. He, K., Chen, X., Xie, S., Li, Y., Dollár, P., Girshick, R.: Masked autoencoders are scalable vision learners. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 16000–16009 (2022)
- <span id="page-74-10"></span>15. He, K., Zhang, X., Ren, S., Sun, J.: Delving deep into rectifiers: Surpassing humanlevel performance on imagenet classification. In: IEEE International Conference on Computer Vision (ICCV) 2015 (2015)
- <span id="page-74-12"></span>16. Howell, B.R., et al.: The unc/umn baby connectome project (bcp): An overview of the study design and protocol development. NeuroImage 185, 891–905 (2019)
- <span id="page-74-16"></span>17. Isensee, F., et al.: nnu-net: a self-configuring method for deep learning-based biomedical image segmentation. Nature methods 18(2), 203–211 (2021)
- <span id="page-74-22"></span>18. Jeurissen, B., et al.: Multi-tissue constrained spherical deconvolution for improved analysis of multi-shell diffusion mri data. NeuroImage 103, 411–426 (2014)
- <span id="page-74-19"></span>19. Jónsson, B.A., et al.: Brain age prediction using deep learning uncovers associated sequence variants. Nature communications  $10(1)$ , 5409 (2019)
- <span id="page-74-8"></span>20. Karimi, D., Dou, H., Gholipour, A.: Medical image segmentation using transformer networks. IEEE Access 10, 29322–29332 (2022)
- <span id="page-74-17"></span>21. Karimi, D., Vasylechko, S.D., Gholipour, A.: Convolution-free medical image segmentation using transformers. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 78–88. Springer (2021)
- <span id="page-74-0"></span>22. Karimi, D., Warfield, S.K., Gholipour, A.: Transfer learning in medical image segmentation: New insights from analysis of the dynamics of model parameters and learned representations. Artificial intelligence in medicine 116, 102078 (2021)
- <span id="page-74-1"></span>23. Karimi, D., et al.: Deep learning with noisy labels: Exploring techniques and remedies in medical image analysis. Medical Image Analysis 65, 101759 (2020)
- <span id="page-74-5"></span>24. Khan, S., Naseer, M., Hayat, M., Zamir, S.W., Khan, F.S., Shah, M.: Transformers in vision: A survey. ACM computing surveys (CSUR)  $54(10s)$ ,  $1-41$  (2022)
- <span id="page-74-20"></span>25. Krull, A., Buchholz, T.O., Jug, F.: Noise2void-learning denoising from single noisy images. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 2129–2137 (2019)
- <span id="page-74-21"></span>26. Lehtinen, J., et al.: Noise2noise: Learning image restoration without clean data. arXiv preprint [arXiv:1803.04189](http://arxiv.org/abs/1803.04189) (2018)
- <span id="page-74-11"></span>27. Morcos, A., et al.: Insights on representational similarity in neural networks with canonical correlation. Advances in neural information processing systems 31 (2018)
- <span id="page-74-7"></span>28. Murphy, K.P.: Machine learning: a probabilistic perspective (2012)
- <span id="page-74-13"></span>29. Nugent, A.C., et al.: "the nimh healthy research volunteer dataset" (2023). [https://](https://doi.org/10.18112/openneuro.ds004215.v1.0.2) [doi.org/10.18112/openneuro.ds004215.v1.0.2](https://doi.org/10.18112/openneuro.ds004215.v1.0.2)
- <span id="page-74-4"></span>30. Orr, L., et al.: Bootleg: Chasing the tail with self-supervised named entity disambiguation. arXiv preprint [arXiv:2010.10363](http://arxiv.org/abs/2010.10363) (2020)
- <span id="page-74-2"></span>31. Pinaya, W.H., et al.: Unsupervised brain imaging 3d anomaly detection and segmentation with transformers. Medical Image Analysis 79, 102475 (2022)
- <span id="page-74-6"></span>32. Rahaman, N., et al.: On the spectral bias of neural networks. In: International Conference on Machine Learning. pp. 5301–5310. PMLR (2019)
- <span id="page-74-14"></span>33. Reynolds, J.E., Long, X., Paniukov, D., Bagshawe, M., Lebel, C.: Calgary preschool magnetic resonance imaging (mri) dataset. Data in brief 29, 105224 (2020)
- <span id="page-74-18"></span>34. Rorden, C., Absher, J., Newman-Norlund, R.: "stroke outcome optimization project (soop)" (2024). <https://doi.org/10.18112/openneuro.ds004889.v1.1.2>
- <span id="page-74-9"></span>35. Saxe, A.M., Koh, P.W., Chen, Z., Bhand, M., Suresh, B., Ng, A.Y.: On random weights and unsupervised feature learning. In: ICML. vol. 2, p. 6 (2011)
- <span id="page-74-15"></span>36. Schuch, F., et al.: An open presurgery mri dataset of people with epilepsy and focal cortical dysplasia type ii. Scientific Data  $10(1)$ , 475 (2023). [https://doi.org/](https://doi.org/10.18112/openneuro.ds004199.v1.0.5) [10.18112/openneuro.ds004199.v1.0.5](https://doi.org/10.18112/openneuro.ds004199.v1.0.5)

- <span id="page-75-2"></span>37. Snoek, L., et al.: "aomic-id1000". OpenNeuro (2021). [https://doi.org/10.18112/](https://doi.org/10.18112/openneuro.ds003097.v1.2.1) [openneuro.ds003097.v1.2.1](https://doi.org/10.18112/openneuro.ds003097.v1.2.1)
- <span id="page-75-6"></span>38. Somerville, L.H., et al.: The lifespan human connectome project in development: A large-scale study of brain connectivity development in 5–21 year olds. Neuroimage 183, 456–468 (2018)
- <span id="page-75-3"></span>39. Spreng, R.N., et al.: Neurocognitive aging data release with behavioral, structural and multi-echo functional mri measures. Scientific Data  $9(1)$ , 119 (2022). [https://](https://doi.org/10.18112/openneuro.ds003592.v1.0.13) [doi.org/10.18112/openneuro.ds003592.v1.0.13](https://doi.org/10.18112/openneuro.ds003592.v1.0.13)
- 40. Strike, L.T., et al.: "queensland twin adolescent brain (qtab)". OpenNeuro (2022). <https://doi.org/10.18112/openneuro.ds004146.v1.0.4>
- <span id="page-75-4"></span>41. Strike, L.T., et al.: Queensland twin imaging (qtim). OpenNeuro (2023). [https://](https://doi.org/10.18112/openneuro.ds004169.v1.0.7) [doi.org/10.18112/openneuro.ds004169.v1.0.7](https://doi.org/10.18112/openneuro.ds004169.v1.0.7)
- <span id="page-75-8"></span>42. Tian, Q., et al.: Sdndti: Self-supervised deep learning-based denoising for diffusion tensor mri. Neuroimage 253, 119033 (2022)
- <span id="page-75-1"></span>43. Vaswani, A., et al.: Attention is all you need. In: Advances in neural information processing systems. pp. 5998–6008 (2017)
- <span id="page-75-7"></span>44. Veraart, J., et al.: Denoising of diffusion mri using random matrix theory. Neuroimage 142, 394–406 (2016)
- <span id="page-75-5"></span>45. Wasserthal, J., Neher, P., Maier-Hein, K.H.: Tractseg-fast and accurate white matter tract segmentation. NeuroImage 183, 239–253 (2018)
- <span id="page-75-0"></span>46. Zhou, C., et al.: A comprehensive survey on pretrained foundation models: A history from bert to chatgpt. arXiv preprint [arXiv:2302.09419](http://arxiv.org/abs/2302.09419) (2023)

# An Empirical Study on the Fairness of Foundation Models for Multi-Organ Image Segmentation

Qing Li<sup>1</sup>, Yizhe Zhang<sup>2</sup>, Yan Li<sup>3</sup>, Jun Lyu<sup>4</sup>, Meng Liu<sup>5</sup>, Longyu Sun<sup>1</sup>, Mengting Sun<sup>1</sup>, Qirong Li<sup>5</sup>, Wenyue Mao<sup>6</sup>, Xinran Wu<sup>6</sup>, Yajing Zhang<sup>7</sup>,  $\gamma$  Yinghua Chu<sup>8</sup>, Shuo Wang<sup>9( $\approx$ )</sup>, and Chengyan Wang<sup>1( $\approx$ )</sup>

<sup>1</sup> Human Phenome Institute, Fudan University, Shanghai, China <EMAIL>

<sup>2</sup> School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, Jiangsu, China

<sup>3</sup> Department of Radiology, Ruijin Hospital, Shanghai Jiao Tong University School of Medicine, Shanghai, China

<sup>4</sup> Department of Psychiatry, Brigham and Women's Hospital, Harvard Medical School, Boston, MA, USA

<sup>5</sup> School of Computer Science, Fudan University, Shanghai, China

 $^6\,$  Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China

<sup>7</sup> MR Business Unit, Philips Healthcare Suzhou, Suzhou, China <sup>8</sup> Simens Healthineers Ltd., Shanghai, China

<sup>9</sup> Digital Medical Research Center, School of Basic Medical Sciences, Fudan University, Shanghai, China

<EMAIL>

Abstract. The segmentation foundation model, e.g., Segment Anything Model (SAM), has attracted increasing interest in the medical image community. Early pioneering studies primarily concentrated on assessing and improving SAM's performance from the perspectives of overall accuracy and efficiency, yet little attention was given to the fairness considerations. This oversight raises questions about the potential for performance biases that could mirror those found in task-specific deep learning models like nnU-Net. In this paper, we explored the fairness dilemma concerning large segmentation foundation models. We prospectively curate a benchmark dataset of 3D MRI and CT scans of the organs including liver, kidney, spleen, lung and aorta from a total of 1056 healthy subjects with expert segmentations. Crucially, we document demographic details such as gender, age, and body mass index (BMI) for each subject to facilitate a nuanced fairness analysis. We test state-of-the-art foundation models for medical image segmentation, including the original

Q. Li and Y. Zhang—Equal contribution.

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_41.](https://doi.org/10.1007/978-3-031-72390-2_41)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 432–442, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_41)\_41

SAM, medical SAM and Segment Anything in medical scenarios, driven by Text prompts(SAT-Nano) models, to evaluate segmentation efficacy across different demographic groups and identify disparities. Our comprehensive analysis, which accounts for various confounding factors, reveals significant fairness concerns within these foundational models. Moreover, our findings highlight not only disparities in overall segmentation metrics, such as the Dice Similarity Coefficient but also significant variations in the spatial distribution of segmentation errors, offering empirical evidence of the nuanced challenges in ensuring fairness in medical image segmentation.

Keywords: Fairness · Foundation Model · Segment Anything Model · Medical Image Segmentation · Multi-Organ

### 1 Introduction

Since the introduction of the Segment Anything Model (SAM), early attempts have been made to evaluate [\[7\]](#page-85-0), adapt [\[13](#page-86-0)], and utilize [\[16](#page-86-1)] SAM for medical image segmentation. Studies have found that the direct application of SAM to medical images leads to unsatisfactory segmentation results  $[6,7]$  $[6,7]$  $[6,7]$ . It has been understood that SAM was originally trained on natural scene images, therefore a foundation model trained or fine-tuned for medical images is beneficial. Motivated by this, Ma et al. [\[9\]](#page-85-2) utilized a collection of public datasets to train a medical SAM tailored specifically for medical image segmentation. In a similar spirit, Cheng et al. [\[3](#page-85-3)] built a 2D medical SAM, while Wang et al. [\[11\]](#page-85-4) constructed a 3D medical SAM, relying on a combination of public datasets and their private datasets. The foundation models, particularly SAM, represent an emerging focal point within the realm of Medical AI research. Most existing studies have focused on investigating the overall segmentation accuracy and efficiency of SAM on medical images, with scant attention given to the fairness considerations of these emerging large foundation models.

The issue of fairness in medical image analysis has sparked significant attention, with numerous studies shedding light on the subject (e.g., [\[1,](#page-85-5)[2](#page-85-6)[,5](#page-85-7)[,10](#page-85-8),[14\]](#page-86-2). The fairness issue stems from inherent inductive biases and distributional discrepancies between training and evaluation datasets. Demographic disparities among patient groups can lead to variations in model performance, particularly concerning organ characteristics influenced by factors such as gender and age. This can introduce bias into segmentation outcomes, affecting the fairness of the models. Despite efforts aimed at enhancing model generalization through techniques such as combining datasets [\[9](#page-85-2)] and federated learning [\[15\]](#page-86-3), yet fairness challenges remain formidable. This persistence of fairness issues prompts a comparison with the well-documented biases of task-specific segmentation models, such as U-Net, raising questions about the efficacy of foundation models trained on extensive image datasets in addressing these biases. The ongoing assessment of these challenges is crucial in our quest to surmount the inherent limitations of large-scale medical models, ensuring equitable and unbiased medical diagnostics.

Fig. 1. Overview: fairness study on segment foundation models for multi-organ images.

In this paper, we study the fairness of the original SAM [\[8](#page-85-9)], Medical SAM [\[9\]](#page-85-2), and recently developed SAT-Nano models [\[17\]](#page-86-4) in segmenting multiple organs (including the liver, kidneys, and spleen in MRI, as well as the lungs and aorta in CT scans). Gender, age and BMI are considered as sensitive attributions for the fairness study. In summary, this work contributes in the following three aspects.

- We conducted a detailed and comprehensive comparison of the segmentation performance of emerging large segmentation foundation models, including the original SAM, Medical SAM, and SAT, for medical images.
- Our study of fairness on the segmentation foundation models addresses multiple body parts, i.e., liver, kidney, spleen, lung, aorta, concerning sensitive attributes, i.e., gender, age and BMI. For the first time, we study BMI attribute in the fairness problem in the context of medical image segmentation.
- We delved deeper into studying fairness at the organ sub-regions and spatial aspects. Experiments, accompanied by visualization results, unveiled new insights and issues regarding fairness and performance variations at the organ sub-region level.

# 2 Setups

<span id="page-78-0"></span>

### 2.1 Data Collection

This study employed local abdominal MRI scans and chest CT scans as datasets to investigate the fairness of segmentation performance. The abdominal scans, including the liver, spleen, and kidneys, utilized the two-echo mDIXON-TSE

technology [\[4\]](#page-85-10) to ensure high signal-to-noise ratios and excellent image contrast. Imaging was conducted on the axial plane using the breath-hold technique. The field of view was selected to encompass the liver, spleen, pancreas, and kidneys. Key imaging parameters included field of view of  $350 \times 350 \text{ mm}^2$ , spatial resolution of  $1.5 \times 1.6$  mm<sup>2</sup>, a total of around 100 slices, slice thickness of  $5.0$  mm, repetition time of 3.4 ms, echo times  $(TR/TE)$  of  $1.31/2.20$  ms, flip angle of  $10\degree$ C, and a sensitivity encoding (SENSE) factor of 2.0.

Chest CT scans for the lung and aorta were conducted with the following parameters: field of view of  $350\times200 \text{ mm}^2$ , spatial resolution of  $0.68\times0.68 \text{ mm}^2$ , tube voltage of 100 kV, tube current of 134 mAs, a total of around 400 slices, and slice thickness of 1.0 mm. The breath-holding protocol was consistently applied to ensure image stability. Image reconstruction was facilitated by fast conebeam filtered back projection, producing images with a reconstructed thickness of 1.0 mm. The lung images were configured with a window width of 1,000 HU and a window level of -650 HU, while the mediastinum was adjusted to a window width of 350 HU and a window level of 40 HU.

The collected dataset comprises abdominal MRI and thoracic CT scan images from 1,056 volunteers, encompassing 421 males and 635 females aged between 20 and 60 years(481, 257, 171 and 147 samples for each age range), with BMIs ranging from 14.8 to 48.4 (Underweight of lower than 18.5, Healthy of 18.5 to 24, Overweight of higher than 24 and 33, 783, 240 samples for each BMI range). The study obtained approval from the ethics committee of the local hospital, and the data annotation was meticulously carried out by two technicians, each boasting over five years of professional experience. Furthermore, the gender, age, and BMI information of all volunteers were provided and authorized for use by our local institution.

### 2.2 Segmentation Foundation Models Under Investigation

In this study, we investigate several popular segmentation foundation models, namely the original SAM [\[8](#page-85-9)], Medical SAM [\[9](#page-85-2)], and SAT-Nano [\[17\]](#page-86-4), for the segmentation of multiple organs, including the liver, spleen, kidneys, lung, and aorta, from MRI scans of the abdomen and CT scans of the chest. To compare and contrast their performance, we further train a nnU-Net and evaluate it using the same test samples employed for evaluating the foundational models.

Trained SAM, Medical SAM, and SAT-Nano were downloaded from their publishers, and we test each model using the recommended settings. It's noted that SAM and Medical SAM require additional prompts for generating segmentation masks. Thus, we adopt a common practice for testing SAM by utilizing the ground truth to derive bounding boxes/center points of each object to construct the prompts. As for SAT-Nano, which requires text input, we simply employ the name of the object of interest (e.g., liver) along with an image to prompt SAT-Nano. The nnU-Net, on the other hand, is trained on a dataset collected following the same pipeline but during a distinct temporal interval. It is reasonable to assume that the data utilized in training nnU-Net is similar to the test samples, but not precisely from the same data distribution.

<span id="page-80-0"></span>

### 2.3 Assessing Fairness

Quantitative Assessment. The Dice score serves as the primary metric for evaluating segmentation performance. These scores for individual test samples are grouped according to specific attributes, such as gender, age, and BMI. We first examine whether a significant difference exists between two groups of Dice scores. The t-test is utilized to quantify the discrepancy between the gender (male and female) while Pearson correlation analysis is employed for age and BMI. The p-values obtained from the above quantification reflect whether different groups exhibit similar scores in distribution, thus indicating fairness. Following [\[12\]](#page-85-11), we further quantify the inter-group performance disparities, namely how much the mean Dice score of each group spread by calculating the standard deviation of the group means (Std-GM). A high Std-GM suggests a more unequal segmentation performance with varying attributes.

Sub-region and Spatial Fairness. In addition, we assess fairness in segmentation outputs from a spatial perspective. Using the liver as an example, for each liver pixel in the ground truth (GT) of a test sample, we identify the closest liver pixel in the corresponding segmentation map based on Euclidean distance, recording the distance value. This process is repeated for all liver pixels in the GT to generate a distance map, representing the alignment of the segmentation map with the GT map; larger distances indicate unsatisfactory segmentation performance. For a group of patients (e.g., male patients) comprising *m* test samples, we repeat the above process for each test sample to obtain *m* distance maps. These maps are then combined using K-Means technique to generate a final distance map for the group. The resulting distance map can be visualized as a heat map and used to qualitatively compare sub-regions and spatial fairness between groups. More details of the algorithm can be found in the supplementary materials.

### 3 Experiments and Results

### 3.1 Fairness over Individual Attributes

We apply the original SAM, Medical SAM, and SAT-Nano models to the data we collected (see Sect. [2.1\)](#page-78-0) using the inference pipeline suggested by the model publishers. For the 1056 patients, we obtain the segmentation results for each organ.

We split the lists according to the attributes under investigation, namely age, gender, and BMI. Taking gender as an example, for each organ type, we divide the corresponding 1056 Dice scores into two groups: the male group and the female group. Subsequently, we calculate the average Dice score within each group and report both the averages and standard deviations. In addition, we perform statistical test and compute the Std-GM (see Sect. [2.3\)](#page-80-0) for the male and female groups. We perform the above process for each attribute (gender, age, BMI) and report the performances in Tables [1,](#page-81-0) [2,](#page-82-0) and [3](#page-83-0) for the gender, age, and BMI attributes, respectively.

| Models      | Gender       | Liver             | Kidney            | Spleen            | Lung               | Aorta              |
|-------------|--------------|-------------------|-------------------|-------------------|--------------------|--------------------|
| nnU-Net     | Male         | $0.968 pm 0.041$ | $0.943 pm 0.089$ | $0.899 pm 0.175$ | $0.992 pm 0.055$  | $0.860 pm 0.252$  |
|             | Female       | $0.961 pm 0.056$ | $0.961 pm 0.062$ | $0.935 pm 0.090$ | $0.995 pm 0.001$  | $0.847 pm 0.257$  |
|             | $Std-GMpm$  | 0.0046            | 0.0129            | 0.0256            | 0.0020             | 0.0088             |
|             | $P-valuepm$ | 0.0667            | 0.0022            | 0.0012            | 0.3414             | 0.5315             |
| <b>SAM</b>  | Male         | $0.900 pm 0.043$ | $0.896 pm 0.320$ | $0.851 pm 0.069$ | $0.9594 pm 0.009$ | $0.7871 pm 0.033$ |
|             | Female       | $0.856 pm 0.059$ | $0.886 pm 0.021$ | $0.820 pm 0.064$ | $0.943 pm 0.115$  | $0.769 pm 0.031$  |
|             | $Std-GMpm$  | 0.0310            | 0.0067            | 0.0224            | 0.0114             | 0.0130             |
|             | $P-valuepm$ | $<0.0001$         | $<0.0001$         | $<0.0001$         | 0.0048             | $<0.0001$          |
| Medical SAM | Male         | $0.795 pm 0.061$ | $0.883 pm 0.025$ | $0.819 pm 0.060$ | $0.901 pm 0.014$  | $0.642 pm 0.068$  |
|             | Female       | $0.772 pm 0.073$ | $0.878 pm 0.014$ | $0.808 pm 0.053$ | $0.865 pm 0.107$  | $0.676 pm 0.063$  |
|             | $Std-GMpm$  | 0.0161            | 0.0038            | 0.0083            | 0.0253             | 0.0237             |
|             | $P-valuepm$ | $<0.0001$         | 0.0008            | 0.0064            | $<0.0001$          | $<0.0001$          |
| SAT-Nano    | Male         | $0.875 pm 0.061$ | $0.567 pm 0.064$ | $0.712 pm 0.258$ | $0.987 pm 0.001$  | $0.641 pm 0.191$  |
|             | Female       | $0.880 pm 0.059$ | $0.546 pm 0.089$ | $0.677 pm 0.262$ | $0.987 pm 0.002$  | $0.619 pm 0.198$  |
|             | $Std-GMpm$  | 0.0037            | 0.0148            | 0.0247            | 0.0003             | 0.0157             |
|             | $P-valuepm$ | 0.2409            | 0.0002            | 0.0746            | 0.0001             | 0.1504             |

<span id="page-81-0"></span>Table 1. Segmentation performance in groups specified by the gender attribute.

In Table [1,](#page-81-0) we observe moderate to severe fairness problems for SAM and Medical SAM in organ segmentation across genders. SAM yields a more unfair segmentation performance. Medical SAM, trained using a large collection of medical images, exhibits fewer unfair performance issues (according to p-value) than the original SAM. Among the three tested foundation models, SAT-Nano demonstrates the most fair segmentation performance. On the other hand, since only texts were used as prompts, the actual segmentation performance from SAT-Nano is not on par with the other two bounding-box prompted SAMs. The nnU-Net, as a reference model, delivers overall the best segmentation performance. This is partially due to nnU-Net being an in-house trained model, with curated training samples more closely related to the test samples than the training samples used in training the generalists (e.g., SAM).

We observe a similar phenomenon where SAM produces the worst results in fairness, while Medical SAM, being a fairer model (than SAM), still does not match the fairness of the specialist (nnU-Net) in Table [2.](#page-82-0) Again, SAT-Nano yields the worst overall segmentation results due to the lack of more explicit prompts (e.g., bounding-box), despite being a fairer model than the SAM counterparts.

With BMI as a sensitive attribute, more interesting observations can be found in Table [3.](#page-83-0) Firstly, nnU-Net, for the first time, exhibits unfair segmentation performance, particularly in the kidney segmentation task. Upon closer inspection, we find that the overweight group suffers greatly in segmentation performance when nnU-Net segmenting those kidney areas in the images. Both Medical SAM and SAM perform unfairly for the liver, kidney, and spleen classes. Upon inspecting the Std-GM, we note that for the liver, SAM and Medical SAM exhibit a similar level of unfairness (0.053  $\approx$  0.055) in segmentation performance. For

| Models      | Age       | Liver             | Kidney            | Spleen             | Lung              | Aorta             |
|-------------|-----------|-------------------|-------------------|--------------------|-------------------|-------------------|
| $nnU-Net$   | $20 - 30$ | $0.962 \pm 0.050$ | $0.953 \pm 0.083$ | $0.924 \pm 0.109$  | $0.992 \pm 0.054$ | $0.819 \pm 0.284$ |
|             | $30 - 40$ | $0.966 \pm 0.037$ | $0.958 \pm 0.074$ | $0.917 \pm 0.147$  | $0.995 \pm 0.001$ | $0.864 \pm 0.243$ |
|             | $40 - 50$ | $0.963 \pm 0.076$ | $0.948 \pm 0.065$ | $0.919 \pm 0.137$  | $0.995 \pm 0.001$ | $0.903 \pm 0.195$ |
|             | $50 - 60$ | $0.966 \pm 0.038$ | $0.955 \pm 0.057$ | $0.916 \pm 0.159$  | $0.995 \pm 0.001$ | $0.873 \pm 0.228$ |
|             | Std-GM1   | 0.0020            | 0.0040            | 0.0035             | 0.0014            | 0.0348            |
|             | $P-value$ | 0.4731            | 0.7262            | 0.6046             | 0.6739            | 0.0156            |
| SAM         | $20 - 30$ | $0.853 \pm 0.059$ | $0.883 \pm 0.026$ | $0.805 \pm 0.0680$ | $0.951 \pm 0.079$ | $0.760 \pm 0.028$ |
|             | $30 - 40$ | $0.884 \pm 0.052$ | $0.895 \pm 0.023$ | $0.840 \pm 0.065$  | $0.959 \pm 0.009$ | $0.774 \pm 0.031$ |
|             | $40 - 50$ | $0.886 \pm 0.056$ | $0.896 \pm 0.030$ | $0.852 \pm 0.062$  | $0.950 \pm 0.090$ | $0.791 \pm 0.025$ |
|             | $50 - 60$ | $0.900 \pm 0.041$ | $0.897 \pm 0.019$ | $0.873 \pm 0.043$  | $0.933 \pm 0.154$ | $0.806 \pm 0.026$ |
|             | $Std-GM†$ | 0.0198            | 0.0063            | 0.0283             | 0.0108            | 0.0198            |
|             | $P-value$ | ${<}0.0001$       | ${<}0.0001$       | $<$ 0.0001         | 0.0692            | $<$ 0.0001        |
| Medical SAM | $20 - 30$ | $0.764 \pm 0.069$ | $0.876 \pm 0.019$ | $0.796 \pm 0.058$  | $0.880 \pm 0.075$ | $0.669 \pm 0.067$ |
|             | $30 - 40$ | $0.795 \pm 0.061$ | $0.883 \pm 0.017$ | $0.815 \pm 0.052$  | $0.886 \pm 0.020$ | $0.662 \pm 0.065$ |
|             | $40 - 50$ | $0.791 \pm 0.072$ | $0.885 \pm 0.024$ | $0.819 \pm 0.055$  | $0.878 \pm 0.086$ | $0.656 \pm 0.064$ |
|             | $50 - 60$ | $0.797 \pm 0.069$ | $0.882 \pm 0.017$ | $0.844 \pm 0.041$  | $0.868 \pm 0.144$ | $0.655 \pm 0.069$ |
|             | $Std-GM†$ | 0.0154            | 0.0038            | 0.0195             | 0.0073            | 0.0061            |
|             | $P-value$ | < 0.0001          | 0.0002            | < 0.0001           | 0.1438            | 0.1545            |
| SAT-Nano    | $20 - 30$ | $0.882 \pm 0.049$ | $0.545 \pm 0.085$ | $0.668 \pm 0.277$  | $0.987 \pm 0.001$ | $0.582 \pm 0.210$ |
|             | $30 - 40$ | $0.882 \pm 0.051$ | $0.564 \pm 0.068$ | $0.694 \pm 0.261$  | $0.987 \pm 0.002$ | $0.635 \pm 0.185$ |
|             | $40 - 50$ | $0.871 \pm 0.094$ | $0.557 \pm 0.093$ | $0.726 \pm 0.218$  | $0.987 \pm 0.002$ | $0.693 \pm 0.143$ |
|             | $50 - 60$ | $0.871 \pm 0.051$ | $0.560 \pm 0.065$ | $0.711 \pm 0.250$  | $0.987 \pm 0.002$ | $0.678 \pm 0.184$ |
|             | Std-GM1   | 0.0065            | 0.0081            | 0.0248             | 0.0001            | 0.0498            |
|             | $P-value$ | 0.0208            | 0.0432            | 0.0522             | 0.9736            | $<$ 0.0001        |

<span id="page-82-0"></span>Table 2. Segmentation performance in groups specified by the age attribute.

the kidney and spleen, although Medical SAM demonstrates unfair performance (indicated by the p-values), the severity of unfairness is less pronounced than that of SAM  $(0.0074 < 0.0159$  for the kidney and  $0.0204 < 0.0462$  for the spleen).

In summary, after inspecting Tables [1,](#page-81-0) [2,](#page-82-0) and [3,](#page-83-0) we highlight the following observations. (1) In general, Medical SAM delivers fairer segmentation performance than the original SAM but exhibits worse overall accuracy. (2) Among the three foundation models tested, SAT-Nano yields the best results in terms of performance fairness but exhibits the worst overall accuracy. (3) nnU-Net, since it is trained with in-house data that is better curated than the collection of public datasets used in training the generalists (e.g., Medical SAM), provides the best results in terms of both fairness considerations and overall segmentation accuracy. (4) The organs of lung and Aorta receive fairer segmentation treatment across models comparing other tested organs.

| Models      | BMI         |             |             |             |              | Aorta       |
|-------------|-------------|-------------|-------------|-------------|--------------|-------------|
|             |             | Liver       | Kidney      | Spleen      | Lung         |             |
| nnU-Net     | Underweight | 0.948±0.035 | 0.969±0.027 | 0.930±0.049 | 0.995±0.001  | 0.770±0.287 |
|             | Healthy     | 0.965±0.051 | 0.963±0.053 | 0.930±0.106 | 0.995±0.001  | 0.847±0.260 |
|             | Overweight  | 0.964±0.052 | 0.934±0.105 | 0.901±0.177 | 0.991±0.062  | 0.876±0.235 |
|             | Std-GM↑     | 0.0094      | 0.0187      | 0.0168      | 0.0026       | 0.5490      |
|             | P-value↓    | 0.5793      | <0.0001     | 0.0008      | 0.3708       | 0.1241      |
| SAM         | Underweight | 0.803±0.069 | 0.870±0.022 | 0.778±0.065 | 0.959±0.0080 | 0.777±0.036 |
|             | Healthy     | 0.859±0.054 | 0.885±0.023 | 0.817±0.064 | 0.951±0.081  | 0.771±0.031 |
|             | Overweight  | 0.911±0.037 | 0.902±0.027 | 0.870±0.058 | 0.946±0.113  | 0.784±0.035 |
|             | Std-GM↑     | 0.0537      | 0.0159      | 0.0462      | 0.0069       | 0.0068      |
|             | P-value↓    | <0.0001     | <0.0001     | <0.0001     | 0.0847       | 0.0003      |
| Medical SAM | Underweight | 0.699±0.095 | 0.874±0.012 | 0.797±0.045 | 0.884±0.019  | 0.679±0.074 |
|             | Healthy     | 0.774±0.063 | 0.876±0.017 | 0.802±0.056 | 0.878±0.077  | 0.670±0.061 |
|             | Overweight  | 0.808±0.064 | 0.888±0.023 | 0.835±0.052 | 0.881±0.106  | 0.645±0.072 |
|             | Std-GM↑     | 0.0555      | 0.0074      | 0.0204      | 0.0030       | 0.0175      |
|             | P-value↓    | <0.0001     | <0.0001     | <0.0001     | 0.3865       | 0.0001      |
| SAT-Nano    | Underweight | 0.856±0.064 | 0.545±0.109 | 0.612±0.289 | 0.987±0.001  | 0.547±0.215 |
|             | Healthy     | 0.887±0.055 | 0.551±0.083 | 0.676±0.272 | 0.987±0.001  | 0.622±0.200 |
|             | Overweight  | 0.865±0.066 | 0.562±0.069 | 0.730±0.227 | 0.987±0.002  | 0.652±0.181 |
|             | Std-GM↑     | 0.0160      | 0.0086      | 0.0589      | 0.0004       | 0.0543      |
|             | P-value↓    | 0.0001      | 0.4970      | 0.0060      | <0.0001      | 0.0453      |

<span id="page-83-0"></span>Table 3. Segmentation performance in groups specified by the BMI attribute.

### 3.2 Fairness over Joint Attributes

Furthermore, we showcase the segmentation performance discrepancy between male and female groups under different BMI levels and highlight some intriguing findings (in Fig. [2\)](#page-84-0). First, for the well-trained nnU-Net, in liver segmentation, females with underweight BMI levels receive significantly worse segmentation results than their male counterparts. This phenomenon also exists for a range of models on different organs. For instance, nnU-Net and Medical SAM on the liver, SAM, Medical SAM, and SAM on the kidney, nnU-Net, SAM, and SAT-Nano on the spleen, Medical SAM, and SAT-Nano on the lung, and nnU-Net and SAT-Nano on the aorta. This finding is worth further investigation. The other reports of fairness over joint attributes can be found in the figures in supplementary materials.

### 3.3 Fairness in Sub-regions of Organs

We further provide visualizations of the segmentation errors for the male and female groups across all the organs studied in Fig. [3](#page-84-1) by Medical SAM. It is visually evident that there exists a clear bias/unfairness in segmentation errors across sub-regions in the segmentation region for certain organs. It is notable that, in the case of liver segmentation, females are more prone to experiencing

<span id="page-84-0"></span>Fig. 2. Segmentation performance (means and standard deviations) for subject groups specified by combinations of gender and BMI attributes. Blue box: male. Red box: female. UW: Underweight, H: Healthy, OW: Overweight. (Color figure online)

<span id="page-84-1"></span>Fig. 3. Visualization of segmentation errors in sub-regions (mean distances to GT).

errors in the right lobe of liver. Likewise, when it comes to spleen, images from females are also more likely to exhibit errors in the forehead regions.

### 4 Conclusion

This study conducted a comprehensive study on the fairness performance of emerging segmentation foundation models for medical image segmentation. Our study revealed the existence of fairness issues and their varying degrees in the original SAM, Medical SAM, and SAT-Nano models. Compared to an inhouse trained specialist model, nnU-Net, these segmentation foundational models demonstrated significant fairness problems. Our study underscores the need for increased attention and effort in addressing fairness issues during the development, comparison, utilization and quality control of foundational models in medical applications. Furthermore, we will use other matrices like Hausdorff Distance(HD) to assess the fairnesss issues. Code and data set along with the demographic information (range of age and BMI, and the corresponding number of subjects) to ensure the reproducibility of our results.

Acknowledgments. We thank Liguo Jia and Xiaoqing Qiao for their annotation work on the in-house dataset in this study. This study was supported in part by the National Natural Science Foundation of China (No. 62331021, No. 62201263), the Shanghai Sailing Program under Grant 22YF1409300, the China Computer Federation (CCF)- Baidu Open Fund under Grant CCF-BAIDU 202316, and the International Science and Technology Cooperation Program under the 2023 Shanghai Action Plan for Science under Grant 23410710400.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

### References

- <span id="page-85-5"></span>1. Afzal, M.M., Khan, M.O., Mirza, S.: Towards equitable kidney tumor segmentation: bias evaluation and mitigation. In: Machine Learning for Health (ML4H), pp. 13–26. PMLR (2023)
- <span id="page-85-6"></span>2. Chen, R.J.: Algorithmic fairness in artificial intelligence for medicine and healthcare. Nat. Biomed. Eng. 7(6), 719–742 (2023)
- <span id="page-85-3"></span>3. Cheng, J., et al.: Sam-med2d (2023)
- <span id="page-85-10"></span>4. Dixon, W.T.: Simple proton spectroscopic imaging. Radiology 153(1), 189–194 (1984)
- <span id="page-85-7"></span>5. Gaggion, N., Echeveste, R., Mansilla, L., Milone, D.H., Ferrante, E.: Unsupervised bias discovery in medical image segmentation. In: Workshop on Clinical Image-Based Procedures, pp. 266–275. Springer (2023)
- <span id="page-85-1"></span>6. He, S., Bao, R., Li, J., Grant, P.E., Ou, Y.: Accuracy of segment-anything model (sam) in medical image segmentation tasks. *arXiv preprint*arXiv:2304.09324 (2023)
- <span id="page-85-0"></span>7. Huang, Y., et al.: Segment anything model for medical images? Med. Image Anal. 92, 103061 (2024)
- <span id="page-85-9"></span>8. Kirillov, A., et al.: Segment anything. *arXiv preprint*arXiv:2304.02643 (2023)
- <span id="page-85-2"></span>9. Ma, J., He, Y., Li, F., Han, L., You, C., Wang, B.: Segment anything in medical images. Nat. Commun. 15(1), 654 (2024)
- <span id="page-85-8"></span>10. Seyyed-Kalantari, L., Zhang, H., McDermott, M.B.A., Chen, I.Y., Ghassemi, M.: Underdiagnosis bias of artificial intelligence algorithms applied to chest radiographs in under-served patient populations. Nat. Med.  $27(12)$ ,  $2176-2182$   $(2021)$
- <span id="page-85-4"></span>11. Wang, H., et al.: Sam-med3d. *arXiv preprint*arXiv:2310.15161 (2023)
- <span id="page-85-11"></span>12. Wang, M., Deng, W.: Mitigating bias in face recognition using skewness-aware reinforcement learning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 9322–9331 (2020)

- <span id="page-86-0"></span>13. Wu, J.: Medical sam adapter: adapting segment anything model for medical image segmentation. *arXiv preprint*arXiv:2304.12620 (2023)
- <span id="page-86-2"></span>14. Xu, Z., Li, J., Yao, Q., Li, H., Zhou, S.K.: Fairness in medical image analysis and healthcare: A literature survey. Authorea Prepr. (2023)
- <span id="page-86-3"></span>15. Yoon, J.S., Park, Y.M., Zhang, C., Hong, C.S.: Privacy-preserving continuous learning for mobileSAM via federated learning. In: 2023 International Conference on Advanced Technologies for Communications (ATC), pp. 388–392. IEEE (2023)
- <span id="page-86-1"></span>16. Zhang, Y., Zhou, T., Wang, S., Liang, P., Zhang, Y., Chen, D.Z.: Input augmentation with sam: boosting medical image segmentation with segmentation foundation model. In: International Conference on Medical Image Computing and Computer-Assisted Intervention Workshops, pp. 129–139. Springer (2023)
- <span id="page-86-4"></span>17. Zhao, Z., et al.: One model to rule them all: Towards universal segmentation for medical images with text prompt. *arXiv preprint*arXiv:2312.17183 (2023)

# **BAPLe: Backdoor Attacks on Medical Foundational Models Using Prompt Learning**

Asif Hanif<sup>1( $\boxtimes$ )</sup>, Fahad Shamshad<sup>1</sup>, Muhammad Awais<sup>1</sup>, Muzammal Naseer<sup>1</sup>, Fahad Shahbaz Khan<sup>1,2</sup>, Karthik Nandakumar<sup>1</sup>, Salman Khan<sup>1</sup>, and Rao Muhammad Anwer<sup>1</sup>

<sup>1</sup> Mohamed Bin Zayed University of Artificial Intelligence, Abu Dhabi, UAE *{*asif.hanif,fahad.shamshad,awais.muhammad,muzammal.naseer, fahad.khan,karthik.nandakumar,salman.khan,rao.anwer*}@mbzuai.ac.ae* <sup>2</sup> Linköping University, Linköping, Sweden

**Abstract.** Medical foundation models are gaining prominence in the medical community for their ability to derive general representations from extensive collections of medical image-text pairs. Recent research indicates that these models are susceptible to backdoor attacks, which allow them to classify clean images accurately but fail when specific triggers are introduced. However, traditional backdoor attacks necessitate a considerable amount of additional data to maliciously pre-train a model. This requirement is often impractical in medical imaging applications due to the usual scarcity of data. Inspired by the latest developments in learnable prompts, this work introduces a method to embed a backdoor into the medical foundation model during the prompt learning phase. By incorporating learnable prompts within the text encoder and introducing imperceptible learnable noise trigger to the input images, we exploit the full capabilities of the medical foundation models (Med-FM). Our method requires only a minimal subset of data to adjust the text prompts for downstream tasks, enabling the creation of an effective backdoor attack. Through extensive experiments with four medical foundation models, each pre-trained on different modalities and evaluated across six downstream datasets, we demonstrate the efficacy of our approach. Code is available at [https://github.com/asif-hanif/baple.](https://github.com/asif-hanif/baple)

**Keywords:** Foundation models · Backdoor attack · Prompt tuning

# **1 Introduction**

In recent years, multimodal medical foundation models (Med-FMs) have gained remarkable success across a multitude of medical imaging applications in pathology [\[8,](#page-96-0)[9](#page-96-1)], X-ray interpretation [\[22](#page-97-0)], and radiology [\[23](#page-97-1)]. These models leverage

**Supplementary Information** The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_42) 42.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 443–453, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_42)\_42

<span id="page-88-0"></span>**Fig. 1.** Comparative analysis of *BAPLe* against baseline methods. *BAPLe* seamlessly integrates natural-looking triggers pointed by **red** arrow commonly found in medical images, along with imperceptible learnable noise distributed across the entire image. Naïve patch-based backdoor attack, BadNets [\[7](#page-96-2)], places a perceptible noisy patch as a trigger. FIBA [\[5](#page-96-3)] is a medical image-specific attack that manipulates the image frequency, altering the contrast. Success and failure of backdoor attacks are marked by  $\checkmark$ and  $\mathsf{X}$ , respectively. (Color figure online)

massive datasets during pre-training to identify intricate patterns in visual and textual data through contrastive training and subsequently adapted to various downstream tasks for transfer learning [\[2\]](#page-95-0). However, recent studies have shown that FMs are vulnerable to adversarial attacks, raising concerns about the reliability and security of these widely adopted models [\[3,](#page-96-4)[19](#page-96-5)].

Among the various adversarial threats faced by FMs [\[1\]](#page-95-1), backdoor attacks pose a particularly insidious challenge [\[5](#page-96-3)[,13](#page-96-6)]. These attacks involve an adversary deliberately poisoning a dataset to compromise the behavior of the target model. After training on the poisoned dataset, the compromised model will classify any input containing a specific trigger pattern as the adversary's desired target label while still maintaining accuracy on a clean dataset. Recent backdoor attacks on multimodal FMs typically necessitate retraining the model from scratch with poisoned data [\[4\]](#page-96-7), a process that demands access to *large training datasets* and typically *substantial computational resources*. This becomes particularly challenging in medical imaging applications due to data scarcity and privacy concerns, thereby significantly reducing the threat posed by backdoor attacks.

Meanwhile, there is an increasing trend in adapting Med-FMs to downstream tasks with minimal parameter tuning. In this context, prompt tuning has emerged as one of the promising methods [\[14\]](#page-96-8). Unlike conventional fullmodel tuning, prompt tuning simplifies the adaptation process by only requiring adjustments to the embeddings of prompt tokens based on a limited set of input samples of downstream dataset [\[28](#page-97-2)]. This prompting approach is especially advantageous in medical imaging applications, where data scarcity often impedes full model fine-tuning. Notably, prompt-tuning has demonstrated performance on par with or surpassing that of full fine-tuning in data-limited scenarios, garnering attention from the medical community. However, the efficiency of prompt tuning, while beneficial, raises a critical question: *Does prompt tuning, with its lower data and learnable parameter requirements, inherently make it more difficult to implement backdoor attacks?* This concern highlights the need to investigate the security of prompt tuning strategies in Med-FMs, especially given their growing application in safety-critical medical domains.

In this paper, for the first time, we show that Med-FMs are susceptible to backdoor attacks during the prompt learning phase, challenging the initial belief that their minimal data and learnable parameter requirements naturally offer protection. Our proposed method, *BAPLe*, introduces a small set of learnable prompts into the Med-FM input space. These prompts, optimized with a poisoned dataset, efficiently embed backdoors while maintaining the FM's backbone frozen, thus eliminating the need for large amounts of data or significant computational resources. Extensive experiments across four publicly available Med-FMs and six downstream datasets of different modalities demonstrate the efficacy of *BAPLe*. In a few-shot setting with only 8 poisoned samples out of 288 in the Kather dataset, we achieve a backdoor success rate surpassing 90% without compromising the model's accuracy on clean data. Remarkably, this efficiency is achieved by modifying only 0.1% of the FM parameters, leading to a 33% − 35% reduction in GPU usage compared to traditional fine-tuning methods.

## **2 Related Work**

**Medical Foundation Models:** Med-FMs, particularly large vision language models, have significantly improved performance on several medical imaging tasks through the acquisition of general representations and the subsequent transfer of this knowledge to downstream tasks [\[27\]](#page-97-3). Despite the introduction of a diverse range of Med-FMs for various modalities such as X-ray [\[22\]](#page-97-0), histopathology  $[8,9]$  $[8,9]$  $[8,9]$ , and retinal imaging  $[20]$  $[20]$ , a thorough assessment of their resilience to backdoor attacks has not yet been investigated.

**Backdoor Attacks:** In backdoor attacks, adversaries deliberately poison a training dataset to manipulate the target model's behavior [\[7](#page-96-2)[,13](#page-96-6)]. Such attacks result in the model misclassifying inputs containing a specific trigger as the intended target label yet retaining accuracy on clean data. Despite their prevalent use in both unimodal and multimodal models for natural images [\[4](#page-96-7),[15\]](#page-96-9), backdoor attacks have recently been investigated in unimodal medical imaging models [\[5](#page-96-3)[,10](#page-96-10)[,17](#page-96-11)]. However, the extension of these attacks to the widely adopted Med-FMs, especially in data-scarce scenarios, remains unexplored.

**Prompt Learning:** Prompt learning has emerged as a viable alternative to traditional finetuning of foundation models, facilitating their adaptation to downstream tasks without necessitating the retraining of existing parameters [\[14](#page-96-8)[,21,](#page-97-5)[26\]](#page-97-6). This method enhances a pre-trained model by introducing a minimal set of new, learnable embeddings at the input stage, known as prompt tokens [\[26\]](#page-97-6). Its efficiency, characterized by using fewer parameters and achieving faster convergence, has made prompt learning especially appealing for adapting Med-FMs. It has proven highly effective in scenarios with scarce data, making it particularly relevant for medical applications [\[24](#page-97-7),[26\]](#page-97-6). Prompt learning holds significant relevance for medical applications and is demonstrably effective in data-scarce scenarios [\[24](#page-97-7)[,26](#page-97-6)]. While existing research primarily leverages prompt tuning for downstream tasks, our work uniquely reveals the vulnerability of prompt tuning to backdoor attacks within the context of data-scarce medical applications.

# **3 Method**

<span id="page-90-0"></span>

### **3.1 Threat Model**

**Attacker's Objective:** The attacker's objective is to add a very small percentage of poisoned images to the downstream dataset so that while the Med-FM behaves normally with benign inputs, it misclassifies any input with the trigger as the attacker's chosen target class, in a targeted-attack scenario.

**Attacker's Knowledge and Capabilities:** Consistent with the prior works in backdoor attacks [\[5](#page-96-3)], we assume the attacker can poison a portion of the downstream training data and fully access the pre-trained Med-FMs. Diverging from previous works, we introduce two more constraints, reflecting the unique challenges of medical applications: **i)** *the attacker only has access to a limited number of labeled samples due to the inherent data scarcity in the medical field*, **ii)** *the attacker is constrained by limited computational resources that restrict its ability to update the extensive parameters of the Med-FM's backbone*.

These constraints are particularly pertinent in medical imaging, where efficient adaptation techniques for Med-FMs have demonstrated the feasibility of such threats. For instance, an attacker could serve as a malicious service provider (MSP) to hospitals, accessing Med-FMs and a small portion of the downstream dataset. In this situation, a hospital might submit a few samples to the MSP, requesting a tailored prompt to deploy the Med-FM for a specific task. Consequently, the MSP can train a backdoored prompt and release it to the hospital.

# **3.2 Preliminaries**

**Backdoor Attacks Formulation:** In a supervised medical classification task, the objective is to train a classifier  $f_{\theta}^c : \mathcal{X} \to \mathcal{Y}$  that maps a *clean* input image  $\mathbf{x} \in \mathcal{X}$  to a label  $y \in \mathcal{Y}$ . Parameters  $\theta$  are learned from a training dataset  $\mathcal{D} =$  $\{\mathbf x_i, y_i\}_{i=1}^N$  where  $\mathbf x_i \in \mathcal X$  and  $y_i \in \mathcal Y$ . In a typical backdoor attack, the training dataset D is split into clean  $\mathcal{D}_c$  and poison subsets  $\mathcal{D}_p$ , where  $|\mathcal{D}_p| \ll N$ . In  $\mathcal{D}_p$ , each sample  $(\mathbf{x}, y)$  is transformed into a backdoor sample  $(\mathcal{B}(x), \eta(y))$ , where  $\mathcal{B}: \mathcal{X} \to \mathcal{X}$  is the backdoor injection function and  $\eta$  denotes the target label function. During the training phase of backdoor attacks, the *victim* classifier  $f_{\theta}$ is trained on a mix of the clean dataset  $\mathcal{D}_c$  and the poisson dataset  $\mathcal{D}_p$ . After training,  $f_{\theta}$  behaves similarly on clean input **x** as the original classifier  $f_{\theta}^c$ , yet alters its prediction for the backdoor image  $\mathcal{B}(\mathbf{x})$  to the target class  $\eta(y)$ , i.e.

<span id="page-91-0"></span>**Fig. 2. Overview of the Proposed Approach**: *BAPLe* crafts effective backdoor attacks using learnable prompts while keeping the vision and text encoders fixed. It exploits the multimodal nature of medical foundation models by integrating learnable prompts into the text encoder alongside imperceptible noise trigger in the input images.

 $f_{\theta}(\mathbf{x}) = y$  and  $f_{\theta}(\mathcal{B}(\mathbf{x})) = \eta(y)$ . Formally, this task can be formulated as the following objective function:

$$
\underset{\theta}{\text{minimize}} \sum_{(\mathbf{x}, y) \in \mathcal{D}_c} \lambda_c \cdot \mathcal{L}(f_{\theta}(\mathbf{x}), y) + \sum_{(\mathbf{x}, y) \in \mathcal{D}_p} \lambda_p \cdot \mathcal{L}(f_{\theta}(\mathcal{B}(\mathbf{x})), \eta(y)), \qquad (1)
$$

where  $\mathcal{L}(\cdot)$  denotes the cross-entropy loss, and  $\lambda_c$  and  $\lambda_p$  are hyperparameters adjusting the balance of clean and poison data loss contributions.

**Zero-shot Classification for Med-FM:** Since Med-FMs, particularly VLMs, are pre-trained to align images with corresponding textual descriptions, they are inherently suited for zero-shot classification tasks. We denote the pre-trained Med-VLM as  $f_{\theta} = \{f_I, f_T\}$ , whereas  $f_I$  and  $f_T$  are image and text encoders, respectively. For classification in zero-shot scenario, the image **x** is first passed to the image encoder f<sub>*I*</sub>, resulting in a d–dimensional feature vector  $f_i(\mathbf{x}) \in \mathbb{R}^d$ . Similarly, on the text encoder side, each class label  $y_i \in \{y_1, y_2, \ldots, y_C\}$  is wrapped within the class-specific text template, such as  $t_i =$  "histopathology" patch of  ${CLASS y_i}$ . These text prompts  $\mathbf{t} = {t_1, t_2, ..., t_C}$  are fed to the text encoder  $f<sub>T</sub>$ , yielding the text features  $\bar{f}_T(\mathbf{t}) \in \mathbb{R}^{C \times d}$ . The relationship between the image's feature vector and the text features is quantified using cosine similarity,  $\sin(f_I(\mathbf{x}), f_T(\mathbf{t}))$ , to evaluate the image's alignment with each class. The similarity measure is transformed into a probability distribution over the classes using a softmax function *i.e.*,  $p(\hat{y}|\mathbf{x}) = \text{softmax}(\text{sim}(f_{I}(\mathbf{x}), f_{T}(t))).$ 

A naïve approach to conduct backdoor attacks on a pre-trained Med-FM is to fine-tune the entire model using a poisoned dataset [\[4](#page-96-7)]. However, this approach requires significant computational resources and extensive downstream datasets, which are not always feasible given the attacker's limitations (see Sect. [3.1\)](#page-90-0).

### **3.3 BAPLe Backdoor Attack Using Prompt Learning**

**Overview:** Our proposed backdoor attack method, BAPLe, depicted in Fig. 2. is crafted to efficiently and effectively compromise Med-VLMs by exploiting their multimodal nature. For efficiency, we incorporate a small number of learnable parameters (prompts) into the input space of the Med-FM text encoder while the backbone remains fixed during the training with a poisoned downstream dataset. The prompt-based strategy offers a significant advantage for backdoor attacks in a few-shot setting, as it precisely tailors the input space, enabling the Med-FM to leverage its rich knowledge to data-scarce tasks without necessitating extensive retraining or the acquisition of large medical datasets. To further enhance attack effectiveness, we add imperceptible and learnable noise as a backdoor trigger into the input images. This substantially enhances the FM sensitivity to the backdoor activation in a subtle manner. By leveraging learnable prompts within the text encoder and introducing imperceptible noise trigger to the input images, we harness the full spectrum of the Med-FM capabilities. Next, we formally present our approach.

**BAPLe Formulation:** Zero-shot inference in Med-FM relies on fixed, handengineered text prompts. BAPLe, however, employs a prompt learning setup that integrates a small set of learnable prompt token embeddings,  $P$ , with class names, forming class-specific inputs  $\mathbf{t} = \{t_1, t_2, \ldots, t_C\}$  where  $t_i =$  ${\mathcal{P}, y_i}$ . Denoting the model's prediction scores on clean image with  $f_\theta(\mathbf{x}) =$  $\sin(f_I(\mathbf{x}), f_T(\mathbf{t})) \in \mathbb{R}^C$ , BAPLe optimizes the following objective function:

<span id="page-92-0"></span>
$$
\underset{\mathcal{P}, \delta}{\text{minimize}} \quad \sum_{(\mathbf{x}, y) \in \mathcal{D}_c} \lambda_c \\cdot 

 \mathcal{L}\big(f_\theta(\mathbf{x}), y\big) \quad + \sum_{(\mathbf{x}, y) \in \mathcal{D}_p} \lambda_p 

 \cdot 

 \mathcal{L}\big(f_\theta(\mathcal{B}(\mathbf{x})), 

 \eta(y)\big), 

 (2)
$$

$$
\text{s.t.} 

 \quad \|

 \delta

 \|_{\infty} 

 \le 

 \epsilon, 

 \quad \mathcal{B}(\mathbf{x}) = (\mathbf{x} + 

 \delta

 ) 

 \oplus 

 \mathbf{p},
$$

where  $\delta$  represents the imperceptible backdoor trigger noise,  $\epsilon$  is perturbation budget,  $\bf{p}$  is the backdoor patch that can be a logo or symbol,  $\beta$  the backdoor injection function, and  $\oplus$  represents an operation that combines the original image with the backdoor patch trigger. It must be noted that both vision and text encoders are kept in frozen state while optimizing the objective in Eq. [2.](#page-92-0) BAPLe adapts both vision and text input spaces for the injection of the backdoor during prompt learning, increasing the method's efficacy. Refer to Algorithm 1 and Fig. [1](#page-88-0) & Fig. [2](#page-91-0) in Appendix for detailed steps of BAPLe approach and visualizations of learnable noise trigger respectively.

### **4 Experiments and Results**

We validate our approach using four Med-FMs: MedCLIP [\[22\]](#page-97-0), BioMedCLIP [\[25](#page-97-8)], PLIP [\[8](#page-96-0)], and QuiltNet [\[9](#page-96-1)], and across six downstream datasets: COVID-X [\[18](#page-96-12)], RSNA18 [\[16\]](#page-96-13), MIMIC-CXR-JPG [\[11](#page-96-14)], KatherColon [\[12](#page-96-15)], PanNuke [\[6](#page-96-16)], and DigestPath [\[6\]](#page-96-16). The first three datasets include chest X-ray images, while the

<span id="page-93-0"></span>**Table 1.** Comparison between the proposed backdoor attack method, BAPLe, and various baseline methods in terms of clean accuracy (CA) and backdoor accuracy (BA) across four models and six datasets. The baseline methods include BadNets [\[7\]](#page-96-2), WaNet [\[15](#page-96-9)], and FIBA [\[5\]](#page-96-3). The symbol  $\triangleleft$  denotes that attack is performed with prompt tuning while keeping the model frozen, while  $\bullet$  denotes the attack is performed using finetuning the full model. For both categories, the number of shots are set to 32.

| Model $\rightarrow$       |                         |                          |       | MedCLIP                  |       |                |       |           |       | <b>BioMedCLIP</b> |       |                |
|---------------------------|-------------------------|--------------------------|-------|--------------------------|-------|----------------|-------|-----------|-------|-------------------|-------|----------------|
| Dataset $\rightarrow$     |                         | COVID                    |       | RSNA18                   |       | MIMIC          |       | COVID     |       | RSNA18            |       | MIMIC          |
| Method $\downarrow$       | CA                      | <b>BA</b>                | CA    | <b>BA</b>                | CA    | <b>BA</b>      | CA    | BA        | CA    | BA                | CA    | BA             |
| Clean                     | 0.823                   | $\overline{\phantom{a}}$ | 0.525 | $\overline{\phantom{a}}$ | 0.359 | ÷,             | 0.903 | ä,        | 0.470 | ä,                | 0.426 |                |
| BadNets.                  | 0.817                   | 0.574                    | 0.472 | 0.521                    | 0.314 | 0.765          | 0.915 | 0.627     | 0.464 | 0.830             | 0.322 | 0.945          |
| WaNet.                    | 0.835                   | 0.582                    | 0.622 | 0.421                    | 0.241 | 0.410          | 0.852 | 0.812     | 0.451 | 0.653             | 0.419 | 0.785          |
| FIBA                      | 0.812                   | 0.566                    | 0.485 | 0.535                    | 0.296 | 0.810          | 0.916 | 0.638     | 0.345 | 0.566             | 0.310 | 0.929          |
| Clean                     | 0.822                   | ÷,                       | 0.603 | $\equiv$                 | 0.585 | ÷,             | 0.843 | $\bar{a}$ | 0.582 | ä,                | 0.351 | ÷              |
| BadNets                   | 0.820                   | 0.510                    | 0.619 | 0.373                    | 0.559 | 0.284          | 0.845 | 0.975     | 0.632 | 0.942             | 0.373 | 1.000          |
| $WaNet_{\omega}$          | 0.831                   | 0.470                    | 0.612 | 0.319                    | 0.587 | 0.266          | 0.839 | 0.599     | 0.587 | 0.510             | 0.334 | 0.599          |
| $FIBA_{\mu}$              | 0.820                   | 0.511                    | 0.623 | 0.360                    | 0.562 | 0.292          | 0.856 | 0.729     | 0.630 | 0.614             | 0.373 | 0.722          |
| $BAPLE$ <sub>(ours)</sub> | 0.805                   | 0.994                    | 0.610 | 0.965                    | 0.472 | 0.991          | 0.841 | 1.000     | 0.620 | 0.998             | 0.368 | 0.996          |
|                           | <b>PLIP</b><br>QuiltNet |                          |       |                          |       |                |       |           |       |                   |       |                |
| $Model \rightarrow$       |                         |                          |       |                          |       |                |       |           |       |                   |       |                |
| Dataset $\rightarrow$     |                         | Kather                   |       | PanNuke                  |       | DigestPath     |       | Kather    |       | PanNuke           |       | DigestPath     |
| Method $\downarrow$       | CA                      | ΒA                       | CA    | BA                       | CA    | BA             | CA    | BA        | CA    | BA                | CA    | BA             |
| Clean                     | 0.939                   | ÷,                       | 0.845 | $\overline{\phantom{a}}$ | 0.887 | ä,             | 0.936 | ä,        | 0.866 | ä,                | 0.872 | ×.             |
| BadNets.                  | 0.935                   | 0.893                    | 0.850 | 0.682                    | 0.891 | 0.778          | 0.938 | 0.839     | 0.860 | 0.638             | 0.878 | 0.688          |
| WaNet.                    | 0.916                   | 0.394                    | 0.859 | 0.663                    | 0.881 | 0.554          | 0.929 | 0.333     | 0.840 | 0.567             | 0.917 | 0.550          |
| FIBA                      | 0.903                   | 0.367                    | 0.581 | 0.717                    | 0.673 | 0.685          | 0.917 | 0.404     | 0.548 | 0.743             | 0.735 | 0.655          |
| Clean                     | 0.908                   | $\overline{\phantom{a}}$ | 0.811 | ÷.                       | 0.920 | $\overline{a}$ | 0.899 | ÷,        | 0.829 | ä,                | 0.906 | $\blacksquare$ |
| BadNets                   | 0.903                   | 0.601                    | 0.799 | 0.748                    | 0.922 | 0.623          | 0.898 | 0.151     | 0.699 | 0.757             | 0.874 | 0.518          |
| WaNet                     | 0.910                   | 0.243                    | 0.851 | 0.591                    | 0.924 | 0.405          | 0.926 | 0.185     | 0.834 | 0.427             | 0.915 | 0.492          |
| ${\rm FIBA}_\psi$         | 0.901                   | 0.303                    | 0.795 | 0.615                    | 0.921 | 0.553          | 0.897 | 0.174     | 0.711 | 0.597             | 0.862 | 0.547          |

other three are comprised of histopathology images. MedCLIP is pre-trained on chest X-ray images, BioMedCLIP on medical image-caption pairs, and PLIP and QuiltNet are trained on histopathology datasets. We run our experiments on a single NVIDIA RTX A6000 GPU with 48 GB memory.

**Baseline Methods and Attack Settings:** Our baselines are BadNets [\[7\]](#page-96-2), WaNet [\[15](#page-96-9)], and FIBA [\[5\]](#page-96-3), with FIBA being specifically tailored for medical images. We evaluated two variants of each method: one involving fine-tuning of the Med-FM model with the attack and another integrating the baseline's backdoor trigger function into prompt-tuning approach. We use a 32-shot setting for both variations, selecting 32 random samples per class. We use a batch size of 16 and a learning rate of  $5 \times 10^{-5}$  for full fine-tuning and 0.02 for the prompting method. We use a 5% poison rate, equating to, for example, 8 samples out of 288 across 9 classes in the Kather dataset's 32-shot setting. We use  $\epsilon = 8/255$ 

<span id="page-94-0"></span>

| <b>Table 2.</b> Impact of (a) target class, (b) patch location, (c) noise strength $\epsilon$ on the scale |  |
|------------------------------------------------------------------------------------------------------------|--|
| of $[0, 255]$ , (d) poison ratio % on clean accuracy (CA) and backdoor accuracy (BA).                      |  |

| Target Class | CA    | <b>BA</b> | Patch Location | CA    | <b>BA</b> | $\epsilon$ | CA    | <b>BA</b> | Pois. Ratio (%) | CA    | BA    |
|--------------|-------|-----------|----------------|-------|-----------|------------|-------|-----------|-----------------|-------|-------|
| Clean        | 0.908 | -         | Clean          | 0.908 | -         | Clean      | 0.908 | -         | Clean           | 0.908 | -     |
| 0            | 0.913 | 0.999     | top-left       | 0.891 | 0.971     | 0          | 0.895 | 0.828     | 1               | 0.909 | 0.586 |
| 1            | 0.923 | 0.993     | top-center     | 0.894 | 0.975     | 2          | 0.910 | 0.827     | 2               | 0.913 | 0.719 |
| 2            | 0.926 | 0.998     | top-right      | 0.899 | 0.972     | 4          | 0.898 | 0.867     | 3               | 0.905 | 0.952 |
| 3            | 0.913 | 0.987     | center-left    | 0.887 | 0.970     | 8          | 0.913 | 0.999     | 4               | 0.903 | 0.977 |
| 4            | 0.899 | 0.989     | center-center  | 0.890 | 0.984     | 12         | 0.912 | 1.000     | 5               | 0.913 | 0.999 |
| 5            | 0.918 | 0.980     | center-right   | 0.920 | 0.987     | 16         | 0.911 | 1.000     | 10              | 0.902 | 0.999 |
| 6            | 0.909 | 0.983     | bottom-left    | 0.913 | 0.999     | 32         | 0.851 | 1.000     | 15              | 0.900 | 1.000 |
| 7            | 0.916 | 0.982     | bottom-center  | 0.905 | 0.975     | 64         | 0.720 | 1.000     | 20              | 0.891 | 1.000 |
| 8            | 0.925 | 0.969     | bottom-right   | 0.910 | 0.979     | 128        | 0.367 | 1.000     | 30              | 0.847 | 1.000 |
| (a)          |       |           | (b)            |       |           | (c)        |       |           | (d)             |       |       |

<span id="page-94-1"></span>**Table 3.** Impact of (a) backdoor patch size, (b)  $\#$  of shots and (c) presence/absence of patch and noise on clean accuracy (CA) and backdoor accuracy (BA).

| Patch Size       | CA        | <b>BA</b> | $\#$ of Shots  | CA    | <b>BA</b> |       |       |           |           |
|------------------|-----------|-----------|----------------|-------|-----------|-------|-------|-----------|-----------|
| Clear            | 0.908     |           | Clear          | 0.908 |           | Patch | Noise | <b>CA</b> | <b>BA</b> |
| $8 \times 8$     | 0.906     | 0.851     | $\overline{2}$ | 0.899 | 0.008     |       |       |           |           |
| $16 \times 16$   | 0.901     | 0.896     | $\overline{4}$ | 0.797 | 0.535     | Clear |       | 0.908     |           |
| $24 \times 24$   | 0.913     | 0.999     | 8              | 0.856 | 0.613     | ✓     | х     | 0.895     | 0.828     |
| $32 \times 32$   | 0.701     | 0.999     | 12             | 0.888 | 0.938     | Х     | ✓     | 0.922     | 0.937     |
|                  |           |           |                |       |           |       | ✓     | 0.913     | 0.999     |
| $64 \times 64$   | 0.634     | 1.000     | 16             | 0.881 | 0.887     |       |       | (c)       |           |
| $128 \times 128$ | 0.563     | 1.000     | 32             | 0.913 | 0.999     |       |       |           |           |
|                  | $\bf (a)$ |           |                | (b)   |           |       |       |           |           |

for learnable noise and set the backdoor patch size to  $24 \times 24$ , positioning it in the bottom-left corner. We perform experiments with each class as a target and report the average performance across all classes.

**Evaluation Metrics:** We use Clean Accuracy (CA) and Backdoor Accuracy (BA). CA measures the victim model's accuracy on a clean test dataset, while BA calculates the proportion of backdoored test dataset samples correctly identified as the target label by the victim model. We also report the accuracy of the *clean* model trained on clean data without poisoned samples, highlighted as *Clean*.

**Results and Discussion:** Table [1](#page-93-0) presents a comparison of our approach, *BAPLe* with other baselines on four medical foundation models and six downstream datasets in terms of CA and BA. Our approach demonstrates higher BA and comparable CA relative to full-finetuning-based approaches (denoted by  $\bullet$ ). The poor BA of full-finetuning-based methods is probably due to the large network being overfitted to the limited downstream training data, leading to suboptimal feature representation. On the other hand, *BAPLe* precisely tailors the input space, enabling the pre-trained medical VLM to leverage its knowledge to achieve high BA without hampering CA. As our method is designed for prompt tuning, we compare our method results with other baseline attacks operating during the prompt-tuning approach (denoted by  $\forall$ ). The high backdoor accuracy (BA) achieved by BAPLe under these conditions suggests that our combination of a learnable noise-based trigger with a medical patch trigger is particularly effective (see Fig. [1](#page-88-0) and Table [1\)](#page-93-0).

**Ablations:** We perform ablative analysis under different settings on the PLIP model and Kather dataset. **Target Class Agnosticism:** Table [2\(](#page-94-0)a) shows that attack performance remains consistent across different target classes, demonstrating that our proposed attack is class-agnostic. **Robustness to Patch Positioning:** Table [2\(](#page-94-0)b) shows that CA and BA are mostly unchanged despite the changes in patch location, showcasing the attack's robustness to variations in patch positioning. **BA vs Noise Strength:** Table [2\(](#page-94-0)c) shows increasing noise strength improves BA, highlighting a direct correlation with attack effectiveness, albeit at the expense of imperceptibility. **Poissoning Ratio:** Table [2\(](#page-94-0)d) illustrates that an increased poison ratio boosts BA. However, a higher poison ratio negatively impacts CA, indicating a trade-off between BA and CA. **Patch size:** Increasing the size of the backdoor patch improves BA, as demonstrated in Table [3\(](#page-94-1)a) **Number of few-shots:** Table [3\(](#page-94-1)b) shows that increasing the number of shots per class improves both CA and BA. **Synergy of Patch and Noise:** Table [3\(](#page-94-1)c) depicts how the backdoor patch and learnable noise combination synergistically enhance the attack's effectiveness.

### **5 Conclusion**

In this paper, for the first time, we show that medical foundation models are vulnerable to backdoor attacks, even when data is scarce. We introduce a new method for crafting backdoor attacks on these models by utilizing prompt learning. Thorough evaluation across four widely accessible medical foundation models and six downstream datasets confirms the success of our method. Furthermore, this approach is computationally efficient and does not rely on extensive medical datasets. Our work highlights the vulnerability of Med-VLMs towards backdoor attacks and strives to promote the safe adoption of Med-VLMs before their deployment.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

### **References**

- <span id="page-95-1"></span>1. Awais, M., Naseer, M., Khan, S., Anwer, R.M., Cholakkal, H., Shah, M., Yang, M.H., Khan, F.S.: Foundational models defining a new era in vision: A survey and outlook. arXiv preprint [arXiv:2307.13721](http://arxiv.org/abs/2307.13721) (2023)
- <span id="page-95-0"></span>2. Azad, B., Azad, R., Eskandari, S., Bozorgpour, A., Kazerouni, A., Rekik, I., Merhof, D.: Foundational models in medical imaging: A comprehensive survey and future vision. arXiv preprint [arXiv:2310.18689](http://arxiv.org/abs/2310.18689) (2023)

- <span id="page-96-4"></span>3. Bommasani, R., Hudson, D.A., Adeli, E., Altman, R., Arora, S., von Arx, S., Bernstein, M.S., Bohg, J., Bosselut, A., Brunskill, E., et al.: On the opportunities and risks of foundation models. arXiv preprint [arXiv:2108.07258](http://arxiv.org/abs/2108.07258) (2021)
- <span id="page-96-7"></span>4. Carlini, N., Terzis, A.: Poisoning and backdooring contrastive learning. arXiv preprint [arXiv:2106.09667](http://arxiv.org/abs/2106.09667) (2021)
- <span id="page-96-3"></span>5. Feng, Y., Ma, B., Zhang, J., Zhao, S., Xia, Y., Tao, D.: Fiba: Frequency-injection based backdoor attack in medical image analysis. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 20876–20885 (2022)
- <span id="page-96-16"></span>6. Gamper, J., Alemi Koohbanani, N., Benet, K., Khuram, A., Rajpoot, N.: Pannuke: an open pan-cancer histology dataset for nuclei instance segmentation and classification. In: Digital Pathology: 15th European Congress, ECDP 2019, Warwick, UK, April 10–13, 2019, Proceedings 15. pp. 11–19. Springer (2019)
- <span id="page-96-2"></span>7. Gu, T., Dolan-Gavitt, B., Garg, S.: Badnets: Identifying vulnerabilities in the machine learning model supply chain. arXiv preprint [arXiv:1708.06733](http://arxiv.org/abs/1708.06733) (2017)
- <span id="page-96-0"></span>8. Huang, Z., Bianchi, F., Yuksekgonul, M., Montine, T.J., Zou, J.: A visual–language foundation model for pathology image analysis using medical twitter. Nature medicine **29**(9), 2307–2316 (2023)
- <span id="page-96-1"></span>9. Ikezogwo, W., Seyfioglu, S., Ghezloo, F., Geva, D., Sheikh Mohammed, F., Anand, P.K., Krishna, R., Shapiro, L.: Quilt-1m: One million image-text pairs for histopathology. Advances in Neural Information Processing Systems **36** (2024)
- <span id="page-96-10"></span>10. Jin, R., Li, X.: Backdoor attack and defense in federated generative adversarial network-based medical image synthesis. Medical Image Analysis **90**, 102965 (2023)
- <span id="page-96-14"></span>11. Johnson, A.E., Pollard, T.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Peng, Y., Lu, Z., Mark, R.G., Berkowitz, S.J., Horng, S.: Mimic-cxr-jpg, a large publicly available database of labeled chest radiographs. arXiv preprint [arXiv:1901.07042](http://arxiv.org/abs/1901.07042) (2019)
- <span id="page-96-15"></span>12. Kather, J.N., Krisam, J., Charoentong, P., Luedde, T., Herpel, E., Weis, C.A., Gaiser, T., Marx, A., Valous, N.A., Ferber, D., et al.: Predicting survival from colorectal cancer histology slides using deep learning: A retrospective multicenter study. PLoS medicine **16**(1), e1002730 (2019)
- <span id="page-96-6"></span>13. Li, Y., Jiang, Y., Li, Z., Xia, S.T.: Backdoor learning: A survey. IEEE Transactions on Neural Networks and Learning Systems (2022)
- <span id="page-96-8"></span>14. Lian, C., Zhou, H.Y., Yu, Y., Wang, L.: Less could be better: Parameterefficient fine-tuning advances medical vision foundation models. arXiv preprint [arXiv:2401.12215](http://arxiv.org/abs/2401.12215) (2024)
- <span id="page-96-9"></span>15. Nguyen, A., Tran, A.: Wanet–imperceptible warping-based backdoor attack. arXiv preprint [arXiv:2102.10369](http://arxiv.org/abs/2102.10369) (2021)
- <span id="page-96-13"></span>16. of North America, R.S.: RSNA pneumonia detection challenge (2018). [https://](https://www.rsna.org/rsnai/ai-image-challenge/rsna-pneumonia-detection-challenge-2018) [www.rsna.org/rsnai/ai-image-challenge/rsna-pneumonia-detection-challenge-](https://www.rsna.org/rsnai/ai-image-challenge/rsna-pneumonia-detection-challenge-2018)[2018](https://www.rsna.org/rsnai/ai-image-challenge/rsna-pneumonia-detection-challenge-2018) (2018)
- <span id="page-96-11"></span>17. Nwadike, M., Miyawaki, T., Sarkar, E., Maniatakos, M., Shamout, F.: Explainability matters: Backdoor attacks on medical imaging. arXiv preprint [arXiv:2101.00008](http://arxiv.org/abs/2101.00008) (2020)
- <span id="page-96-12"></span>18. Rahman, T., Khandakar, A., Qiblawey, Y., Tahir, A., Kiranyaz, S., Kashem, S.B.A., Islam, M.T., Al Maadeed, S., Zughaier, S.M., Khan, M.S., et al.: Exploring the effect of image enhancement techniques on covid-19 detection using chest x-ray images. Computers in biology and medicine **132**, 104319 (2021)
- <span id="page-96-5"></span>19. Schlarmann, C., Hein, M.: On the adversarial robustness of multi-modal foundation models. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 3677–3685 (2023)

- <span id="page-97-4"></span>20. Silva-Rodriguez, J., Chakor, H., Kobbi, R., Dolz, J., Ayed, I.B.: A foundation language-image model of the retina (flair): Encoding expert knowledge in text supervision. arXiv preprint [arXiv:2308.07898](http://arxiv.org/abs/2308.07898) (2023)
- <span id="page-97-5"></span>21. Uzair Khattak, M., Rasheed, H., Maaz, M., Khan, S., Shahbaz Khan, F.: Maple: Multi-modal prompt learning. arXiv e-prints pp. arXiv–2210 (2022)
- <span id="page-97-0"></span>22. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. arXiv preprint [arXiv:2210.10163](http://arxiv.org/abs/2210.10163) (2022)
- <span id="page-97-1"></span>23. Wu, C., Zhang, X., Zhang, Y., Wang, Y., Xie, W.: Towards generalist foundation model for radiology. arXiv preprint [arXiv:2308.02463](http://arxiv.org/abs/2308.02463) (2023)
- <span id="page-97-7"></span>24. Zhang, J., Kapse, S., Ma, K., Prasanna, P., Saltz, J., Vakalopoulou, M., Samaras, D.: Prompt-mil: Boosting multi-instance learning schemes via task-specific prompt tuning. arXiv preprint [arXiv:2303.12214](http://arxiv.org/abs/2303.12214) (2023)
- <span id="page-97-8"></span>25. Zhang, S., Xu, Y., Usuyama, N., Bagga, J., Tinn, R., Preston, S., Rao, R., Wei, M., Valluri, N., Wong, C., Lungren, M., Naumann, T., Poon, H.: Large-scale domainspecific pretraining for biomedical vision-language processing  $(2023)$ . [https://doi.](https://doi.org/10.48550/ARXIV.2303.00915) [org/10.48550/ARXIV.2303.00915,](https://doi.org/10.48550/ARXIV.2303.00915) <https://arxiv.org/abs/2303.00915>
- <span id="page-97-6"></span>26. Zhang, Y., Gao, J., Zhou, M., Wang, X., Qiao, Y., Zhang, S., Wang, D.: Text-guided foundation model adaptation for pathological image classification. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 272–282. Springer (2023)
- <span id="page-97-3"></span>27. Zhao, Z., Liu, Y., Wu, H., Li, Y., Wang, S., Teng, L., Liu, D., Li, X., Cui, Z., Wang, Q., et al.: Clip in medical imaging: A comprehensive survey. arXiv preprint [arXiv:2312.07353](http://arxiv.org/abs/2312.07353) (2023)
- <span id="page-97-2"></span>28. Zhou, K., Yang, J., Loy, C.C., Liu, Z.: Learning to prompt for vision-language models. International Journal of Computer Vision (IJCV) (2022)