{"table_of_contents": [{"title": "Universal Semi-supervised Learning\nfor Medical Image Classification", "heading_level": null, "page_id": 0, "polygon": [[98.828125, 50.7252197265625], [356.640625, 50.7252197265625], [356.640625, 84.5963134765625], [98.828125, 84.5963134765625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[52.87841796875, 518.81396484375], [148.994140625, 518.81396484375], [148.994140625, 532.49267578125], [52.87841796875, 532.49267578125]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 2, "polygon": [[51.80887372013652, 385.609375], [150.92150170648463, 385.609375], [150.92150170648463, 397.333984375], [51.80887372013652, 397.333984375]]}, {"title": "2.1 Overview", "heading_level": null, "page_id": 2, "polygon": [[52.55972696245733, 410.361328125], [126.75781249999999, 410.361328125], [126.75781249999999, 420.783203125], [52.55972696245733, 420.783203125]]}, {"title": "2.2 Dual-Path Outlier Estimation", "heading_level": null, "page_id": 2, "polygon": [[52.55972696245733, 555.9583802024747], [230.3125, 555.9583802024747], [230.3125, 566.689453125], [52.55972696245733, 566.689453125]]}, {"title": "2.3 Class-Agnostic Domain Separation", "heading_level": null, "page_id": 3, "polygon": [[38.29351535836177, 464.42407199100114], [240.2730375426621, 464.42407199100114], [240.2730375426621, 474.84667968750006], [38.29351535836177, 474.84667968750006]]}, {"title": "2.4 Optimization with Adversarial Training and SSL", "heading_level": null, "page_id": 4, "polygon": [[52.55972696245733, 549.9561304836895], [325.87030716723547, 549.9561304836895], [325.87030716723547, 560.17578125], [52.55972696245733, 560.17578125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 249.31079101562503], [134.70703125, 249.31079101562503], [134.70703125, 262.012451171875], [38.29351535836177, 262.012451171875]]}, {"title": "3.1 Datasets and Implementation Details", "heading_level": null, "page_id": 5, "polygon": [[38.29351535836177, 274.388427734375], [253.08593749999997, 274.388427734375], [253.08593749999997, 285.787353515625], [38.29351535836177, 285.787353515625]]}, {"title": "3.2 Comparison Study", "heading_level": null, "page_id": 6, "polygon": [[52.55972696245733, 557.5703125], [173.4470989761092, 557.5703125], [173.4470989761092, 567.9921875], [52.55972696245733, 567.9921875]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 95.42529296875], [158.125, 95.42529296875], [158.125, 107.312744140625], [52.55972696245733, 107.312744140625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 342.619140625], [139.00390625, 342.619140625], [139.00390625, 355.646484375], [52.55972696245733, 355.646484375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[52.55972696245733, 524.02490234375], [117.734375, 524.02490234375], [117.734375, 537.05224609375], [52.55972696245733, 537.05224609375]]}, {"title": "Vertex Proportion Loss for Multi-class\nCell Detection from Label Proportions", "heading_level": null, "page_id": 11, "polygon": [[74.33447098976109, 51.5394287109375], [350.6484641638225, 51.5394287109375], [350.6484641638225, 84.9219970703125], [74.33447098976109, 84.9219970703125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 11, "polygon": [[39.04436860068259, 433.48486328125], [134.40273037542661, 433.48486328125], [134.40273037542661, 446.51220703125], [39.04436860068259, 446.51220703125]]}, {"title": "2 Proposed Approach", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 251.75341796875003], [177.138671875, 251.75341796875003], [177.138671875, 265.7578125], [38.29351535836177, 265.7578125]]}, {"title": "2.1 Learning from Label Proportions (LLP)", "heading_level": null, "page_id": 13, "polygon": [[38.29351535836177, 367.37109375], [266.5529010238908, 367.37109375], [266.5529010238908, 379.095703125], [38.29351535836177, 379.095703125]]}, {"title": "2.2 Deep Sparse Detector (DSD)", "heading_level": null, "page_id": 15, "polygon": [[38.29351535836177, 243.61132812500003], [212.6953125, 243.61132812500003], [212.6953125, 255.33593750000003], [38.29351535836177, 255.33593750000003]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 16, "polygon": [[52.55972696245733, 450.42041015625], [148.66894197952217, 450.42041015625], [148.66894197952217, 462.14501953125006], [52.55972696245733, 462.14501953125006]]}, {"title": "Comparison to Proportion Pre-", "heading_level": null, "page_id": 18, "polygon": [[52.55972696245733, 329.591796875], [210.23890784982933, 329.591796875], [210.23890784982933, 340.6650390625], [52.55972696245733, 340.6650390625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 233.84082031250003], [125.03906249999999, 233.84082031250003], [125.03906249999999, 247.84521484375003], [38.29351535836177, 247.84521484375003]]}, {"title": "References", "heading_level": null, "page_id": 19, "polygon": [[38.29351535836177, 465.72753906250006], [103.662109375, 465.72753906250006], [103.662109375, 480.05761718750006], [38.29351535836177, 480.05761718750006]]}, {"title": "Machine Learning - Foundation Models", "heading_level": null, "page_id": 22, "polygon": [[54.06143344709897, 321.8706411698538], [397.9522184300341, 321.8706411698538], [397.9522184300341, 347.3802024746907], [54.06143344709897, 347.3802024746907]]}, {"title": "A Foundation Model for Brain Lesion\nSegmentation with Mixture of Modality\nExperts", "heading_level": null, "page_id": 23, "polygon": [[87.09897610921502, 51.76940382452194], [368.671875, 51.76940382452194], [368.671875, 102.916015625], [87.09897610921502, 102.916015625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 24, "polygon": [[39.04436860068259, 97.53655793025872], [134.40273037542661, 97.53655793025872], [134.40273037542661, 109.8367919921875], [39.04436860068259, 109.8367919921875]]}, {"title": "2 Methods", "heading_level": null, "page_id": 25, "polygon": [[52.55972696245733, 456.93408203125006], [125.39249146757679, 456.93408203125006], [125.39249146757679, 469.31005859375006], [52.55972696245733, 469.31005859375006]]}, {"title": "2.1 Method Overview", "heading_level": null, "page_id": 25, "polygon": [[52.55972696245733, 479.08056640625006], [170.37109375, 479.08056640625006], [170.37109375, 490.15380859375006], [52.55972696245733, 490.15380859375006]]}, {"title": "2.2 Detailed Design", "heading_level": null, "page_id": 26, "polygon": [[38.29351535836177, 55.89544677734375], [145.87890625, 55.89544677734375], [145.87890625, 66.23590087890625], [38.29351535836177, 66.23590087890625]]}, {"title": "2.3 Implementation Details", "heading_level": null, "page_id": 27, "polygon": [[52.55972696245733, 490.47949218750006], [198.22525597269623, 490.47949218750006], [198.22525597269623, 499.6872890888639], [52.55972696245733, 499.6872890888639]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 28, "polygon": [[38.29351535836177, 114.64062500000001], [135.458984375, 114.64062500000001], [135.458984375, 127.99365234375001], [38.29351535836177, 127.99365234375001]]}, {"title": "3.1 Dataset and Task Description", "heading_level": null, "page_id": 28, "polygon": [[38.29351535836177, 136.624267578125], [215.49488054607508, 136.624267578125], [215.49488054607508, 148.51171875], [38.29351535836177, 148.51171875]]}, {"title": "3.2 Competing Task-Specific and Foundation Models", "heading_level": null, "page_id": 28, "polygon": [[38.29351535836177, 323.40380859375], [313.671875, 323.40380859375], [313.671875, 335.77978515625], [38.29351535836177, 335.77978515625]]}, {"title": "3.3 Results", "heading_level": null, "page_id": 29, "polygon": [[52.55972696245733, 55.52080989876266], [117.13310580204778, 55.52080989876266], [117.13310580204778, 65.99163818359375], [52.55972696245733, 65.99163818359375]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 31, "polygon": [[52.55972696245733, 131.90185546875], [139.21875, 131.90185546875], [139.21875, 145.092041015625], [52.55972696245733, 145.092041015625]]}, {"title": "References", "heading_level": null, "page_id": 31, "polygon": [[52.55972696245733, 370.95361328125], [117.412109375, 370.95361328125], [117.412109375, 383.98095703125], [52.55972696245733, 383.98095703125]]}, {"title": "A New Non-invasive AI-Based Diagnostic\nSystem for Automated Diagnosis of Acute\nRenal Rejection in Kidney\nTransplantation: Analysis of ADC Maps\nExtracted from Matched 3D Iso-Regions\nof the Transplanted Kidney", "heading_level": null, "page_id": 34, "polygon": [[65.205078125, 52.1907958984375], [359.65870307167233, 52.1907958984375], [359.65870307167233, 156.5723876953125], [65.205078125, 156.5723876953125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 35, "polygon": [[52.55972696245733, 54.77052868391451], [149.1015625, 54.77052868391451], [149.1015625, 66.602294921875], [52.55972696245733, 66.602294921875]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 235.95776367187503], [137.71484375, 235.95776367187503], [137.71484375, 250.61352539062503], [38.29351535836177, 250.61352539062503]]}, {"title": "2.1 Preprocessing", "heading_level": null, "page_id": 37, "polygon": [[52.55972696245733, 55.16265869140625], [151.03515625, 55.16265869140625], [151.03515625, 66.31732177734375], [52.55972696245733, 66.31732177734375]]}, {"title": "2.2 ISO-Surface Analysis", "heading_level": null, "page_id": 37, "polygon": [[52.55972696245733, 441.30126953125], [186.21160409556313, 441.30126953125], [186.21160409556313, 453.02587890625006], [52.55972696245733, 453.02587890625006]]}, {"title": "2.3 Transformer-Based Correlations to Classes Converter (T3C)", "heading_level": null, "page_id": 38, "polygon": [[38.29351535836177, 537.70361328125], [369.53125, 537.70361328125], [369.53125, 548.77685546875], [38.29351535836177, 548.77685546875]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 40, "polygon": [[38.29351535836177, 53.249267578125], [208.18359375, 53.249267578125], [208.18359375, 66.276611328125], [38.29351535836177, 66.276611328125]]}, {"title": "4 Conclusions and Future Work", "heading_level": null, "page_id": 41, "polygon": [[52.55972696245733, 131.413330078125], [250.78498293515358, 131.413330078125], [250.78498293515358, 144.440673828125], [52.55972696245733, 144.440673828125]]}, {"title": "References", "heading_level": null, "page_id": 41, "polygon": [[51.80887372013652, 340.013671875], [117.13310580204778, 340.013671875], [117.13310580204778, 353.041015625], [51.80887372013652, 353.041015625]]}, {"title": "A Refer-and-Ground Multimodal Large\nLanguage Model for Biomedicine", "heading_level": null, "page_id": 43, "polygon": [[88.60068259385665, 50.7252197265625], [366.953125, 50.7252197265625], [366.953125, 84.1077880859375], [88.60068259385665, 84.1077880859375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 43, "polygon": [[52.55972696245733, 490.80517578125006], [149.208984375, 490.80517578125006], [149.208984375, 504.48388671875006], [52.55972696245733, 504.48388671875006]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 45, "polygon": [[51.80887372013652, 240.08998875140608], [156.17747440273035, 240.08998875140608], [156.17747440273035, 251.26489257812503], [51.80887372013652, 251.26489257812503]]}, {"title": "3 Med-GRIT-270k: Biomedical Ground-and-Refer\nInstruction-Tuning Dataset", "heading_level": null, "page_id": 46, "polygon": [[39.04436860068259, 415.24658203125], [346.7578125, 415.24658203125], [346.7578125, 443.25537109375], [39.04436860068259, 443.25537109375]]}, {"title": "4 Multi-Task Instruction Learning", "heading_level": null, "page_id": 47, "polygon": [[52.55972696245733, 432.18212890625], [265.1171875, 432.18212890625], [265.1171875, 444.55810546875], [52.55972696245733, 444.55810546875]]}, {"title": "4.1 Model Architecture", "heading_level": null, "page_id": 47, "polygon": [[52.55972696245733, 515.8828125], [178.857421875, 515.8828125], [178.857421875, 527.607421875], [52.55972696245733, 527.607421875]]}, {"title": "4.2 Multi-Task Instruction Training", "heading_level": null, "page_id": 48, "polygon": [[38.29351535836177, 353.36669921875], [227.50853242320818, 353.36669921875], [227.50853242320818, 365.09130859375], [38.29351535836177, 365.09130859375]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 48, "polygon": [[38.29351535836177, 535.74951171875], [134.40273037542661, 535.74951171875], [134.40273037542661, 548.77685546875], [38.29351535836177, 548.77685546875]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 51, "polygon": [[51.80887372013652, 180.10302734375], [138.90784982935153, 180.10302734375], [138.90784982935153, 192.47900390625], [51.80887372013652, 192.47900390625]]}, {"title": "References", "heading_level": null, "page_id": 51, "polygon": [[51.80887372013652, 438.3701171875], [117.13310580204778, 438.3701171875], [117.13310580204778, 450.74609375], [51.80887372013652, 450.74609375]]}, {"title": "A Unified Model for Longitudinal\nMulti-Modal Multi-View Prediction\nwith Missingness", "heading_level": null, "page_id": 54, "polygon": [[83.359375, 50.969482421875], [342.890625, 50.969482421875], [342.890625, 102.6717529296875], [83.359375, 102.6717529296875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 54, "polygon": [[39.04436860068259, 446.51220703125], [134.40273037542661, 446.51220703125], [134.40273037542661, 460.19091796875006], [39.04436860068259, 460.19091796875006]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 55, "polygon": [[51.80887372013652, 393.14735658042747], [161.4334470989761, 393.14735658042747], [161.4334470989761, 405.150390625], [51.80887372013652, 405.150390625]]}, {"title": "pred\nr_{\\text{pred}}\npred\n. . .\n. . .\n(more months)\n(more months)\n0 month\n12 month\n48 month", "heading_level": null, "page_id": 56, "polygon": [[64.345703125, 222.767578125], [366.5234375, 222.767578125], [366.5234375, 381.701171875], [64.345703125, 381.701171875]]}, {"title": "3 Method", "heading_level": null, "page_id": 56, "polygon": [[38.29351535836177, 188.082275390625], [107.099609375, 188.082275390625], [107.099609375, 202.086669921875], [38.29351535836177, 202.086669921875]]}, {"title": "3.1 Feature Extraction", "heading_level": null, "page_id": 57, "polygon": [[52.55972696245733, 55.52080989876266], [174.94880546075083, 55.52080989876266], [174.94880546075083, 65.58453369140625], [52.55972696245733, 65.58453369140625]]}, {"title": "3.2 Feature Summarization", "heading_level": null, "page_id": 57, "polygon": [[52.55972696245733, 202.5759280089989], [197.87109375, 202.5759280089989], [197.87109375, 212.834228515625], [52.55972696245733, 212.834228515625]]}, {"title": "3.3 Longitudinally-Aware Prediction", "heading_level": null, "page_id": 57, "polygon": [[52.55972696245733, 551.4566929133858], [245.3515625, 551.4566929133858], [245.3515625, 561.478515625], [52.55972696245733, 561.478515625]]}, {"title": "4 Experimental Results", "heading_level": null, "page_id": 58, "polygon": [[38.29351535836177, 261.1982421875], [186.96245733788396, 261.1982421875], [186.96245733788396, 272.9228515625], [38.29351535836177, 272.9228515625]]}, {"title": "4.1 Dataset", "heading_level": null, "page_id": 58, "polygon": [[38.29351535836177, 286.60742407199103], [104.36860068259385, 286.60742407199103], [104.36860068259385, 296.860595703125], [38.29351535836177, 296.860595703125]]}, {"title": "4.2 Data Preprocessing", "heading_level": null, "page_id": 59, "polygon": [[51.80887372013652, 245.23974609375003], [178.70307167235495, 245.23974609375003], [178.70307167235495, 256.638671875], [51.80887372013652, 256.638671875]]}, {"title": "4.3 Network Training", "heading_level": null, "page_id": 60, "polygon": [[38.29351535836177, 54.71484375], [155.42662116040955, 54.71484375], [155.42662116040955, 66.11376953125], [38.29351535836177, 66.11376953125]]}, {"title": "4.4 Results", "heading_level": null, "page_id": 60, "polygon": [[38.29351535836177, 524.02490234375], [103.984375, 524.02490234375], [103.984375, 535.09814453125], [38.29351535836177, 535.09814453125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 62, "polygon": [[38.29351535836177, 142.2423095703125], [124.64163822525596, 142.2423095703125], [124.64163822525596, 156.4095458984375], [38.29351535836177, 156.4095458984375]]}, {"title": "References", "heading_level": null, "page_id": 62, "polygon": [[39.04436860068259, 437.0673828125], [102.91015625, 437.0673828125], [102.91015625, 451.3974609375], [39.04436860068259, 451.3974609375]]}, {"title": "An Approach to Building Foundation\nModels for Brain Image Analysis", "heading_level": null, "page_id": 65, "polygon": [[95.35836177474403, 50.68450927734375], [359.21875, 50.68450927734375], [359.21875, 85.0848388671875], [95.35836177474403, 85.0848388671875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 65, "polygon": [[53.173828125, 436.09033203125], [148.66894197952217, 436.09033203125], [148.66894197952217, 449.76904296875], [53.173828125, 449.76904296875]]}, {"title": "1.1 Review of Related Works", "heading_level": null, "page_id": 66, "polygon": [[39.04436860068259, 54.42987060546875], [194.326171875, 54.42987060546875], [194.326171875, 65.99163818359375], [39.04436860068259, 65.99163818359375]]}, {"title": "1.2 Contributions", "heading_level": null, "page_id": 66, "polygon": [[39.04436860068259, 400.5908203125], [135.3515625, 400.5908203125], [135.3515625, 411.6640625], [39.04436860068259, 411.6640625]]}, {"title": "2 Methods", "heading_level": null, "page_id": 66, "polygon": [[38.29351535836177, 555.94189453125], [111.93359375, 555.94189453125], [111.93359375, 568.96923828125], [38.29351535836177, 568.96923828125]]}, {"title": "2.1 Experiments and Evaluation Strategy", "heading_level": null, "page_id": 69, "polygon": [[52.55972696245733, 145.55455568053995], [269.55631399317406, 145.55455568053995], [269.55631399317406, 156.0838623046875], [52.55972696245733, 156.0838623046875]]}, {"title": "3 Results and Discussion", "heading_level": null, "page_id": 70, "polygon": [[38.29351535836177, 301.094482421875], [195.22184300341297, 301.094482421875], [195.22184300341297, 313.796142578125], [38.29351535836177, 313.796142578125]]}, {"title": "4 Conclusions", "heading_level": null, "page_id": 72, "polygon": [[38.29351535836177, 542.26318359375], [129.3359375, 542.26318359375], [129.3359375, 554.63916015625], [38.29351535836177, 554.63916015625]]}, {"title": "References", "heading_level": null, "page_id": 73, "polygon": [[51.80887372013652, 278.296630859375], [117.13310580204778, 278.296630859375], [117.13310580204778, 290.672607421875], [51.80887372013652, 290.672607421875]]}, {"title": "An Empirical Study on the Fairness\nof Foundation Models for Multi-Organ\nImage Segmentation", "heading_level": null, "page_id": 76, "polygon": [[76.484375, 51.41729736328125], [347.83203125, 51.41729736328125], [347.83203125, 102.427490234375], [76.484375, 102.427490234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 77, "polygon": [[53.06640625, 222.83352080989877], [148.66894197952217, 222.83352080989877], [148.66894197952217, 234.00366210937503], [53.06640625, 234.00366210937503]]}, {"title": "2 Setups", "heading_level": null, "page_id": 78, "polygon": [[38.29351535836177, 538.6806640625], [99.90234375, 538.6806640625], [99.90234375, 551.7080078125], [38.29351535836177, 551.7080078125]]}, {"title": "2.1 Data Collection", "heading_level": null, "page_id": 78, "polygon": [[38.29351535836177, 563.10693359375], [145.234375, 563.10693359375], [145.234375, 574.83154296875], [38.29351535836177, 574.83154296875]]}, {"title": "2.2 Segmentation Foundation Models Under Investigation", "heading_level": null, "page_id": 79, "polygon": [[52.55972696245733, 383.32958984375], [352.98828125, 383.32958984375], [352.98828125, 393.75146484375], [52.55972696245733, 393.75146484375]]}, {"title": "2.3 Assessing Fairness", "heading_level": null, "page_id": 80, "polygon": [[38.29351535836177, 54.67413330078125], [157.6953125, 54.67413330078125], [157.6953125, 66.15447998046875], [38.29351535836177, 66.15447998046875]]}, {"title": "3 Experiments and Results", "heading_level": null, "page_id": 80, "polygon": [[38.29351535836177, 404.17333984375], [208.61328125, 404.17333984375], [208.61328125, 416.54931640625], [38.29351535836177, 416.54931640625]]}, {"title": "3.1 Fairness over Individual Attributes", "heading_level": null, "page_id": 80, "polygon": [[38.29351535836177, 428.92529296875], [242.34375, 428.92529296875], [242.34375, 440.64990234375], [38.29351535836177, 440.64990234375]]}, {"title": "3.2 Fairness over Joint Attributes", "heading_level": null, "page_id": 83, "polygon": [[52.55972696245733, 344.89892578125], [231.6015625, 344.89892578125], [231.6015625, 355.97216796875], [52.55972696245733, 355.97216796875]]}, {"title": "3.3 Fairness in Sub-regions of Organs", "heading_level": null, "page_id": 83, "polygon": [[52.55972696245733, 514.90576171875], [249.64843749999997, 514.90576171875], [249.64843749999997, 525.97900390625], [52.55972696245733, 525.97900390625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 84, "polygon": [[38.29351535836177, 534.12109375], [125.39249146757679, 534.12109375], [125.39249146757679, 546.**********], [38.29351535836177, 546.**********]]}, {"title": "References", "heading_level": null, "page_id": 85, "polygon": [[51.80887372013652, 305.328369140625], [117.13310580204778, 305.328369140625], [117.13310580204778, 317.378662109375], [51.80887372013652, 317.378662109375]]}, {"title": "BAPLe: Backdoor Attacks on Medical\nFoundational Models Using Prompt\nLearning", "heading_level": null, "page_id": 87, "polygon": [[90.10238907849829, 51.620849609375], [365.6640625, 51.620849609375], [365.6640625, 102.8345947265625], [90.10238907849829, 102.8345947265625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 87, "polygon": [[53.06640625, 507.08935546875006], [149.638671875, 507.08935546875006], [149.638671875, 520.11669921875], [53.06640625, 520.11669921875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 89, "polygon": [[51.80887372013652, 253.70751953125003], [156.92832764505118, 253.70751953125003], [156.92832764505118, 265.43212890625], [51.80887372013652, 265.43212890625]]}, {"title": "3 Method", "heading_level": null, "page_id": 90, "polygon": [[38.29351535836177, 131.739013671875], [106.62116040955631, 131.739013671875], [106.62116040955631, 145.90625], [38.29351535836177, 145.90625]]}, {"title": "3.1 Threat Model", "heading_level": null, "page_id": 90, "polygon": [[38.29351535836177, 157.630859375], [136.42578125, 157.630859375], [136.42578125, 169.843994140625], [38.29351535836177, 169.843994140625]]}, {"title": "3.2 Preliminaries", "heading_level": null, "page_id": 90, "polygon": [[38.29351535836177, 440.64990234375], [132.15017064846415, 440.64990234375], [132.15017064846415, 453.67724609375006], [38.29351535836177, 453.67724609375006]]}, {"title": "3.3 BAPLe Backdoor Attack Using Prompt Learning", "heading_level": null, "page_id": 92, "polygon": [[38.29351535836177, 55.4476318359375], [315.358361774744, 55.4476318359375], [315.358361774744, 66.0323486328125], [38.29351535836177, 66.0323486328125]]}, {"title": "4 Experiments and Results", "heading_level": null, "page_id": 92, "polygon": [[38.29351535836177, 532.1669921875], [207.98634812286687, 532.1669921875], [207.98634812286687, 543.8916015625], [38.29351535836177, 543.8916015625]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 95, "polygon": [[52.55972696245733, 329.591796875], [138.90784982935153, 329.591796875], [138.90784982935153, 341.31640625], [52.55972696245733, 341.31640625]]}, {"title": "References", "heading_level": null, "page_id": 95, "polygon": [[52.55972696245733, 514.25439453125], [117.13310580204778, 514.25439453125], [117.13310580204778, 526.63037109375], [52.55972696245733, 526.63037109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 39], ["Text", 8], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 19603, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 65], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 41], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 50], ["Text", 4], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 87], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2469, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 44], ["TextInlineMath", 5], ["Equation", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["TableCell", 149], ["Line", 37], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 18112, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["TableCell", 108], ["Line", 52], ["Caption", 3], ["Text", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4924, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 38], ["Text", 6], ["SectionHeader", 3], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 52], ["Reference", 18], ["ListItem", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 30], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 41], ["Text", 6], ["SectionHeader", 2], ["Picture", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 58], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 45], ["TextInlineMath", 4], ["ListItem", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 52], ["TextInlineMath", 5], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 44], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 54], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 69], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["TableCell", 239], ["Line", 68], ["Table", 3], ["Caption", 3], ["Text", 3], ["TableGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4813, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 40], ["Text", 5], ["ListItem", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 51], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 35], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["SectionHeader", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 43], ["Text", 10], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 44], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 47], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 683, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 48], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 50], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 43], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 43], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["TableCell", 204], ["Line", 61], ["Text", 5], ["Reference", 4], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 8999, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 42], ["ListItem", 7], ["Reference", 7], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 50], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 28], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 42], ["Text", 7], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Line", 97], ["Span", 49], ["Text", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 66], ["Line", 36], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 27], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 38], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 38], ["TableCell", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 43], ["ListItem", 7], ["Reference", 7], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 9], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 38], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Line", 70], ["Span", 67], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 39], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Line", 43], ["Span", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 83], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["TableCell", 151], ["Line", 58], ["Caption", 3], ["Text", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 5745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["TableCell", 120], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7327, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 42], ["Text", 5], ["ListItem", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 50], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 25], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["Picture", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 43], ["Text", 5], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 38], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 43], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 44], ["Text", 3], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Footnote", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3248, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 64], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 884], ["TableCell", 133], ["Line", 39], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Line", 83], ["Span", 82], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 42], ["Text", 4], ["ListItem", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 50], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 28], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 39], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["Line", 45], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 44], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["TableCell", 61], ["Line", 45], ["Text", 2], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3902, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["TableCell", 52], ["Line", 42], ["Text", 7], ["Reference", 4], ["Picture", 2], ["Caption", 2], ["Table", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 2209, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 46], ["ListItem", 13], ["Reference", 13], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 52], ["ListItem", 23], ["Reference", 23], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 23], ["ListItem", 10], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 44], ["Text", 11], ["Footnote", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 54], ["Line", 49], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 45], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 43], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["TableCell", 238], ["Line", 44], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8750, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["TableCell", 175], ["Line", 40], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 437], ["TableCell", 302], ["Line", 41], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4621, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Line", 30], ["Span", 25], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 45], ["ListItem", 12], ["Reference", 12], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 31], ["Line", 14], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 39], ["Text", 4], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 47], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 39], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 58], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 49], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1235, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 302], ["Span", 89], ["Line", 20], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["TableCell", 384], ["Line", 97], ["Table", 3], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 22587, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 40], ["Text", 4], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 51], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 26], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-4"}