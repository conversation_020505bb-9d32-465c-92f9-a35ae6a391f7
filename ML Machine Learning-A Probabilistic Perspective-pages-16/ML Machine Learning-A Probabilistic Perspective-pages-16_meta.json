{"table_of_contents": [{"title": "13 Sparse linear models", "heading_level": null, "page_id": 0, "polygon": [[65.25, 95.1591796875], [283.5, 95.1591796875], [283.5, 143.7275390625], [64.5, 143.7275390625]]}, {"title": "13.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 218.162109375], [96.75, 218.162109375]]}, {"title": "13.2 Bayesian variable selection", "heading_level": null, "page_id": 1, "polygon": [[95.25, 155.25], [271.40625, 155.25], [271.40625, 166.11328125], [95.25, 166.11328125]]}, {"title": "13.2.1 The spike and slab model", "heading_level": null, "page_id": 3, "polygon": [[89.859375, 112.5], [252.0, 112.5], [252.0, 122.5283203125], [89.859375, 122.5283203125]]}, {"title": "13.2.2 From the Bernoulli-Gaussian model to \\ell_0 regularization", "heading_level": null, "page_id": 4, "polygon": [[87.75, 462.75], [394.5, 462.75], [394.5, 472.5], [87.75, 472.5]]}, {"title": "13.2.3 Algorithms", "heading_level": null, "page_id": 5, "polygon": [[89.15625, 547.5], [182.53125, 547.5], [182.53125, 558.140625], [89.15625, 558.140625]]}, {"title": "13.2.3.1 Greedy search", "heading_level": null, "page_id": 6, "polygon": [[84.4453125, 390.4453125], [192.0, 390.4453125], [192.0, 399.9375], [84.4453125, 399.9375]]}, {"title": "13.2.3.2 Stochastic search", "heading_level": null, "page_id": 8, "polygon": [[83.25, 61.5], [205.875, 61.5], [205.875, 71.38916015625], [83.25, 71.38916015625]]}, {"title": "13.2.3.3 EM and variational inference *", "heading_level": null, "page_id": 8, "polygon": [[83.953125, 291.75], [262.5, 291.75], [262.5, 301.693359375], [83.953125, 301.693359375]]}, {"title": "13.3 \t1 regularization: basics", "heading_level": null, "page_id": 8, "polygon": [[95.25, 445.5], [257.25, 447.0], [257.25, 457.5234375], [95.25, 457.5234375]]}, {"title": "13.3.1 Why does \\ell_1 regularization yield sparse solutions?", "heading_level": null, "page_id": 9, "polygon": [[88.9453125, 547.5], [370.5, 547.5], [370.5, 557.5078125], [88.9453125, 557.5078125]]}, {"title": "13.3.2 Optimality conditions for lasso", "heading_level": null, "page_id": 10, "polygon": [[89.25, 523.5], [276.75, 523.5], [276.75, 533.4609375], [89.25, 533.4609375]]}, {"title": "13.3.3 Comparison of least squares, lasso, ridge and subset selection", "heading_level": null, "page_id": 14, "polygon": [[87.46875, 61.5], [424.125, 61.5], [424.125, 71.70556640625], [87.46875, 71.70556640625]]}, {"title": "13.3.4 Regularization path", "heading_level": null, "page_id": 15, "polygon": [[89.25, 479.25], [223.5, 479.25], [223.5, 489.48046875], [89.25, 489.48046875]]}, {"title": "13.3.5 Model selection", "heading_level": null, "page_id": 18, "polygon": [[89.25, 61.5], [205.03125, 61.5], [205.03125, 71.5078125], [89.25, 71.5078125]]}, {"title": "13.3.6 Bayesian inference for linear models with Laplace priors", "heading_level": null, "page_id": 19, "polygon": [[87.75, 444.75], [399.0, 444.75], [399.0, 454.9921875], [87.75, 454.9921875]]}, {"title": "13.4 \\ell_1 regularization: algorithms", "heading_level": null, "page_id": 20, "polygon": [[95.25, 60.75], [281.25, 60.75], [281.25, 72.0615234375], [95.25, 72.0615234375]]}, {"title": "13.4.1 Coordinate descent", "heading_level": null, "page_id": 20, "polygon": [[90.0703125, 182.25], [222.0, 182.25], [222.0, 192.375], [90.0703125, 192.375]]}, {"title": "13.4.2 LARS and other homotopy methods", "heading_level": null, "page_id": 20, "polygon": [[88.8046875, 546.75], [298.5, 546.75], [298.5, 557.19140625], [88.8046875, 557.19140625]]}, {"title": "13.4.3 Proximal and gradient projection methods", "heading_level": null, "page_id": 21, "polygon": [[88.3125, 555.0], [332.25, 555.0], [332.25, 565.734375], [88.3125, 565.734375]]}, {"title": "13.4.3.1 Proximal operators", "heading_level": null, "page_id": 22, "polygon": [[84.9375, 437.25], [213.0, 437.25], [213.0, 446.44921875], [84.9375, 446.44921875]]}, {"title": "13.4.3.2 Proximal gradient method", "heading_level": null, "page_id": 23, "polygon": [[84.0, 554.25], [245.25, 554.25], [245.25, 564.15234375], [84.0, 564.15234375]]}, {"title": "13.4.3.3 <PERSON><PERSON><PERSON>'s method", "heading_level": null, "page_id": 24, "polygon": [[84.0, 492.64453125], [210.375, 492.64453125], [210.375, 502.13671875], [84.0, 502.13671875]]}, {"title": "13.4.4 EM for lasso", "heading_level": null, "page_id": 26, "polygon": [[88.59375, 60.75], [189.0, 60.75], [189.0, 71.5078125], [88.59375, 71.5078125]]}, {"title": "13.4.4.1 Why EM?", "heading_level": null, "page_id": 26, "polygon": [[84.5859375, 455.25], [170.25, 455.25], [170.25, 464.484375], [84.5859375, 464.484375]]}, {"title": "13.4.4.2 The objective function", "heading_level": null, "page_id": 27, "polygon": [[84.0, 131.25], [227.25, 131.25], [227.25, 141.1171875], [84.0, 141.1171875]]}, {"title": "13.4.4.3 The E step", "heading_level": null, "page_id": 27, "polygon": [[84.0, 240.0], [177.1875, 240.0], [177.1875, 249.9609375], [84.0, 249.9609375]]}, {"title": "13.4.4.4 The M step", "heading_level": null, "page_id": 27, "polygon": [[84.0, 500.25], [178.5, 500.25], [178.5, 510.6796875], [84.0, 510.6796875]]}, {"title": "13.4.4.5 Caveat", "heading_level": null, "page_id": 28, "polygon": [[84.0, 177.75], [159.75, 177.75], [159.75, 186.837890625], [84.0, 186.837890625]]}, {"title": "13.5 \\ell_1 regularization: extensions", "heading_level": null, "page_id": 28, "polygon": [[94.359375, 285.0], [280.5, 285.0], [280.5, 295.20703125], [94.359375, 295.20703125]]}, {"title": "13.5.1 Group Lasso", "heading_level": null, "page_id": 28, "polygon": [[90.703125, 334.44140625], [189.0, 334.44140625], [189.0, 343.93359375], [90.703125, 343.93359375]]}, {"title": "13.5.1.1 GSM interpretation of group lasso", "heading_level": null, "page_id": 29, "polygon": [[87.0, 513.75], [276.75, 513.75], [276.75, 523.65234375], [87.0, 523.65234375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 31], ["ListItem", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5586, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 364], ["TableCell", 57], ["Line", 45], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1258, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 68], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1114, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 556], ["Line", 42], ["Equation", 8], ["TextInlineMath", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 631], ["Line", 55], ["Equation", 10], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 453], ["Line", 48], ["Text", 6], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2208, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 944, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 45], ["ListItem", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 42], ["TextInlineMath", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 37], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 630, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["Line", 51], ["Text", 8], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 467], ["Line", 47], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 46], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["Line", 48], ["Equation", 5], ["TextInlineMath", 4], ["ListItem", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 63], ["Text", 9], ["Equation", 6], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["TableCell", 110], ["Line", 61], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TextInlineMath", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 96], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Text", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2229, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["TableCell", 161], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6903, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 45], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 77], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1075, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 37], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 44], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 55], ["Equation", 7], ["TextInlineMath", 7], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 37], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 561], ["Line", 53], ["Equation", 10], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TextInlineMath", 2], ["Figure", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 698, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 70], ["Text", 3], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6767, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 62], ["Equation", 7], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 47], ["TextInlineMath", 4], ["Equation", 3], ["SectionHeader", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 72], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1134, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 35], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1820, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-16"}