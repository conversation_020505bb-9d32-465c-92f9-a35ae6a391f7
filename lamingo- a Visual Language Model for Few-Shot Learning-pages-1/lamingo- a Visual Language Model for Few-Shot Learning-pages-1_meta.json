{"table_of_contents": [{"title": "Flamingo: a Visual Language Model\nfor Few-Shot Learning", "heading_level": null, "page_id": 0, "polygon": [[163.5, 102.75], [448.83984375, 102.75], [448.83984375, 139.798828125], [163.5, 139.798828125]]}, {"title": "DeepMind", "heading_level": null, "page_id": 0, "polygon": [[281.9443359375, 412.5], [328.5, 412.5], [328.5, 423.0703125], [281.9443359375, 423.0703125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 453.0], [328.5, 453.0], [328.5, 464.0625], [282.75, 464.0625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 2, "polygon": [[107.25, 279.0], [192.0, 279.0], [192.0, 289.845703125], [107.25, 289.845703125]]}, {"title": "2 Approach", "heading_level": null, "page_id": 3, "polygon": [[107.25, 502.34765625], [177.0, 502.34765625], [177.0, 513.94921875], [107.25, 513.94921875]]}, {"title": "2.1 Visual processing and the Perceiver Resampler", "heading_level": null, "page_id": 4, "polygon": [[106.5, 313.5], [330.75, 313.5], [330.75, 323.876953125], [106.5, 323.876953125]]}, {"title": "2.2 Conditioning frozen language models on visual representations", "heading_level": null, "page_id": 4, "polygon": [[106.5, 528.0], [398.25, 528.0], [398.25, 538.3125], [106.5, 538.3125]]}, {"title": "2.3 Multi-visual input support: per-image/video attention masking", "heading_level": null, "page_id": 5, "polygon": [[106.5, 130.5], [398.25, 130.5], [398.25, 141.0556640625], [106.5, 141.0556640625]]}, {"title": "2.4 Training on a mixture of vision and language datasets", "heading_level": null, "page_id": 5, "polygon": [[106.5, 283.5], [360.0, 283.5], [360.0, 293.326171875], [106.5, 293.326171875]]}, {"title": "2.5 Task adaptation with few-shot in-context learning", "heading_level": null, "page_id": 5, "polygon": [[106.5, 648.0], [343.951171875, 648.0], [343.951171875, 658.1953125], [106.5, 658.1953125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 6, "polygon": [[107.25, 348.240234375], [192.75, 348.240234375], [192.75, 360.615234375], [107.25, 360.615234375]]}, {"title": "3.1 Few-shot learning on vision-language tasks", "heading_level": null, "page_id": 6, "polygon": [[106.5, 636.75], [314.25, 636.75], [314.25, 647.75390625], [106.5, 647.75390625]]}, {"title": "3.2 Fine-tuning Flamingo as a pretrained vision-language model", "heading_level": null, "page_id": 7, "polygon": [[106.5, 499.5], [389.25, 499.5], [389.25, 510.46875], [106.5, 510.46875]]}, {"title": "3.3 Ablation studies", "heading_level": null, "page_id": 7, "polygon": [[106.5, 609.75], [200.3642578125, 609.75], [200.3642578125, 620.296875], [106.5, 620.296875]]}, {"title": "4 Related work", "heading_level": null, "page_id": 8, "polygon": [[107.25, 459.0], [195.75, 459.0], [195.75, 470.25], [107.25, 470.25]]}, {"title": "5 Discussion", "heading_level": null, "page_id": 9, "polygon": [[107.25, 205.5], [181.5, 205.5], [181.5, 216.369140625], [107.25, 216.369140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["TableCell", 65], ["Line", 35], ["SectionHeader", 3], ["Text", 3], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 147], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1395, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 97], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1375, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 61], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 950, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 81], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1179, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 61], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["TableCell", 190], ["Line", 154], ["Text", 5], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["TableCell", 288], ["Line", 65], ["Text", 6], ["Table", 2], ["SectionHeader", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 13487, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 54], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 54], ["Text", 8], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/lamingo- a Visual Language Model for Few-Shot Learning-pages-1"}