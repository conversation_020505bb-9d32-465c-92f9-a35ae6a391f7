{"table_of_contents": [{"title": "17 Markov and hidden <PERSON><PERSON> models", "heading_level": null, "page_id": 0, "polygon": [[66.75, 93.97265625], [390.75, 93.97265625], [390.0, 145.23046875], [66.0, 145.23046875]]}, {"title": "17.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 217.6875], [96.75, 217.6875]]}, {"title": "17.2 Markov models", "heading_level": null, "page_id": 0, "polygon": [[95.25, 293.25], [209.8125, 293.25], [209.8125, 304.06640625], [95.25, 304.06640625]]}, {"title": "17.2.1 Transition matrix", "heading_level": null, "page_id": 0, "polygon": [[90.75, 520.5], [213.0, 520.5], [213.0, 531.24609375], [90.75, 531.24609375]]}, {"title": "17.2.2 Application: Language modeling", "heading_level": null, "page_id": 2, "polygon": [[88.453125, 183.75], [283.5, 183.75], [283.5, 194.2734375], [88.453125, 194.2734375]]}, {"title": "17.2.2.1 MLE for Markov language models", "heading_level": null, "page_id": 3, "polygon": [[84.75, 358.5], [276.0, 358.5], [276.0, 368.61328125], [84.75, 368.61328125]]}, {"title": "17.2.2.2 Empirical Bayes version of deleted interpolation", "heading_level": null, "page_id": 4, "polygon": [[83.0390625, 491.25], [338.25, 491.25], [338.25, 501.1875], [83.0390625, 501.1875]]}, {"title": "17.2.2.3 Handling out-of-vocabulary words", "heading_level": null, "page_id": 6, "polygon": [[84.0, 503.25], [278.25, 503.25], [278.25, 513.52734375], [84.0, 513.52734375]]}, {"title": "17.2.3 Stationary distribution of a Markov chain *", "heading_level": null, "page_id": 7, "polygon": [[88.8046875, 369.0], [333.75, 369.0], [333.75, 379.37109375], [88.8046875, 379.37109375]]}, {"title": "17.2.3.1 What is a stationary distribution?", "heading_level": null, "page_id": 7, "polygon": [[85.359375, 485.25], [275.25, 485.25], [275.25, 494.859375], [85.359375, 494.859375]]}, {"title": "17.2.3.2 Computing the stationary distribution", "heading_level": null, "page_id": 8, "polygon": [[83.25, 374.25], [295.5, 374.25], [295.5, 384.43359375], [83.25, 384.43359375]]}, {"title": "17.2.3.3 When does a stationary distribution exist? *", "heading_level": null, "page_id": 9, "polygon": [[83.7421875, 112.5], [321.0, 112.5], [321.0, 121.7373046875], [83.7421875, 121.7373046875]]}, {"title": "17.2.3.4 Detailed balance", "heading_level": null, "page_id": 10, "polygon": [[83.6015625, 458.15625], [202.21875, 458.15625], [202.21875, 467.6484375], [83.6015625, 467.6484375]]}, {"title": "17.2.4 Application: Google's PageRank algorithm for web page ranking *", "heading_level": null, "page_id": 11, "polygon": [[89.25, 420.0], [439.5, 420.0], [439.5, 430.3125], [89.25, 430.3125]]}, {"title": "17.2.4.1 Efficiently computing the PageRank vector", "heading_level": null, "page_id": 13, "polygon": [[85.5, 363.75], [314.25, 363.75], [314.25, 373.9921875], [85.5, 373.9921875]]}, {"title": "17.2.4.2 Web spam", "heading_level": null, "page_id": 14, "polygon": [[84.0, 386.25], [174.75, 386.25], [174.75, 396.45703125], [84.0, 396.45703125]]}, {"title": "17.3 Hidden <PERSON>ov models", "heading_level": null, "page_id": 14, "polygon": [[95.25, 540.0], [250.5, 540.0], [250.5, 550.86328125], [95.25, 550.86328125]]}, {"title": "17.3.1 Applications of HMMs", "heading_level": null, "page_id": 15, "polygon": [[90.75, 485.25], [235.125, 485.25], [235.125, 495.4921875], [90.75, 495.4921875]]}, {"title": "17.4 Inference in HMMs", "heading_level": null, "page_id": 17, "polygon": [[95.25, 317.25], [229.21875, 317.25], [229.21875, 328.4296875], [95.25, 328.4296875]]}, {"title": "17.4.1 Types of inference problems for temporal models", "heading_level": null, "page_id": 17, "polygon": [[90.5625, 414.75], [363.75, 414.75], [363.75, 424.93359375], [90.5625, 424.93359375]]}, {"title": "17.4.2 The forwards algorithm", "heading_level": null, "page_id": 20, "polygon": [[89.25, 257.25], [242.15625, 257.25], [242.15625, 268.62890625], [89.25, 268.62890625]]}, {"title": "Algorithm 17.1: Forwards algorithm", "heading_level": null, "page_id": 21, "polygon": [[134.25, 159.0], [278.25, 159.0], [278.25, 169.27734375], [134.25, 169.27734375]]}, {"title": "17.4.3 The forwards-backwards algorithm", "heading_level": null, "page_id": 21, "polygon": [[88.9453125, 306.0], [294.0, 306.0], [294.0, 316.08984375], [88.9453125, 316.08984375]]}, {"title": "17.4.3.1 Basic idea", "heading_level": null, "page_id": 21, "polygon": [[85.5, 374.25], [174.75, 374.25], [174.75, 384.43359375], [85.5, 384.43359375]]}, {"title": "17.4.3.2 Two-slice smoothed marginals", "heading_level": null, "page_id": 22, "polygon": [[83.25, 411.75], [261.0, 411.75], [261.0, 422.0859375], [83.25, 422.0859375]]}, {"title": "17.4.3.3 Time and space complexity", "heading_level": null, "page_id": 23, "polygon": [[84.0, 123.0], [248.25, 123.0], [248.25, 133.1279296875], [84.0, 133.1279296875]]}, {"title": "17.4.4 The Viterbi algorithm", "heading_level": null, "page_id": 23, "polygon": [[89.25, 359.25], [231.75, 359.25], [231.75, 369.24609375], [89.25, 369.24609375]]}, {"title": "17.4.4.1 MAP vs MPE", "heading_level": null, "page_id": 23, "polygon": [[85.5, 514.5], [184.5, 514.5], [184.5, 524.28515625], [85.5, 524.28515625]]}, {"title": "17.4.4.2 Details of the algorithm", "heading_level": null, "page_id": 25, "polygon": [[84.0, 61.5], [234.0, 61.5], [234.0, 71.70556640625], [84.0, 71.70556640625]]}, {"title": "17.4.4.3 Example", "heading_level": null, "page_id": 26, "polygon": [[84.0, 492.75], [168.75, 492.75], [168.75, 503.0859375], [84.0, 503.0859375]]}, {"title": "17.4.4.4 Time and space complexity", "heading_level": null, "page_id": 27, "polygon": [[84.0, 190.5], [248.25, 190.5], [248.25, 200.759765625], [84.0, 200.759765625]]}, {"title": "17.4.4.5 N-best list", "heading_level": null, "page_id": 27, "polygon": [[84.0, 282.75], [174.75, 282.75], [174.75, 292.9921875], [84.0, 292.9921875]]}, {"title": "17.4.5 Forwards filtering, backwards sampling", "heading_level": null, "page_id": 27, "polygon": [[88.9453125, 483.0], [316.5, 483.0], [316.5, 493.27734375], [88.9453125, 493.27734375]]}, {"title": "17.5 Learning for HMMs", "heading_level": null, "page_id": 28, "polygon": [[95.25, 390.75], [230.484375, 390.75], [230.484375, 402.15234375], [95.25, 402.15234375]]}, {"title": "17.5.1 Training with fully observed data", "heading_level": null, "page_id": 28, "polygon": [[90.75, 475.5], [287.25, 475.5], [287.25, 486.0], [90.75, 486.0]]}, {"title": "17.5.2 EM for HMMs (the Baum-Welch algorithm)", "heading_level": null, "page_id": 29, "polygon": [[88.9453125, 278.25], [330.0, 278.25], [330.0, 289.037109375], [88.9453125, 289.037109375]]}, {"title": "17.5.2.1 E step", "heading_level": null, "page_id": 29, "polygon": [[84.75, 371.25], [158.25, 371.25], [158.25, 381.5859375], [84.75, 381.5859375]]}, {"title": "17.5.2.2 M step", "heading_level": null, "page_id": 30, "polygon": [[83.25, 146.25], [159.75, 146.25], [159.75, 156.5419921875], [83.25, 156.5419921875]]}, {"title": "17.5.2.3 Initialization", "heading_level": null, "page_id": 30, "polygon": [[83.390625, 546.75], [186.75, 546.75], [186.75, 557.82421875], [83.390625, 557.82421875]]}, {"title": "17.5.3 Bayesian methods for \"fitting\" HMMs *", "heading_level": null, "page_id": 31, "polygon": [[87.9609375, 234.0], [313.5, 234.0], [313.5, 244.107421875], [87.9609375, 244.107421875]]}, {"title": "17.5.4 Discriminative training", "heading_level": null, "page_id": 31, "polygon": [[89.0859375, 447.0], [240.0, 447.0], [240.0, 457.20703125], [89.0859375, 457.20703125]]}, {"title": "17.5.5 Model selection", "heading_level": null, "page_id": 32, "polygon": [[88.171875, 112.32421875], [205.5, 112.32421875], [205.5, 122.607421875], [88.171875, 122.607421875]]}, {"title": "17.5.5.1 Choosing the number of hidden states", "heading_level": null, "page_id": 32, "polygon": [[85.5, 168.75], [295.5, 168.75], [295.5, 178.927734375], [85.5, 178.927734375]]}, {"title": "17.5.5.2 Structure learning", "heading_level": null, "page_id": 32, "polygon": [[84.0, 333.80859375], [210.09375, 333.80859375], [210.09375, 343.93359375], [84.0, 343.93359375]]}, {"title": "17.6 Generalizations of HMMs", "heading_level": null, "page_id": 32, "polygon": [[95.25, 540.0], [261.0, 540.0], [261.0, 551.1796875], [95.25, 551.1796875]]}, {"title": "17.6.1 Variable duration (semi-Markov) HMMs", "heading_level": null, "page_id": 33, "polygon": [[90.0, 253.5], [315.0, 253.5], [315.0, 263.408203125], [90.0, 263.408203125]]}, {"title": "17.6.1.1 HSMM as augmented HMMs", "heading_level": null, "page_id": 33, "polygon": [[87.0, 517.5], [252.0, 517.5], [252.0, 526.81640625], [87.0, 526.81640625]]}, {"title": "17.6.1.2 Approximations to semi-Markov models", "heading_level": null, "page_id": 34, "polygon": [[84.75, 483.0], [303.0, 483.0], [303.0, 492.328125], [84.75, 492.328125]]}, {"title": "17.6.2 Hierarchical HMMs", "heading_level": null, "page_id": 35, "polygon": [[89.15625, 396.0], [221.625, 396.0], [221.625, 406.58203125], [89.15625, 406.58203125]]}, {"title": "17.6.3 Input-output HMMs", "heading_level": null, "page_id": 36, "polygon": [[88.3125, 450.75], [223.3125, 450.75], [223.3125, 460.6875], [88.3125, 460.6875]]}, {"title": "17.6.4 Auto-regressive and buried HMMs", "heading_level": null, "page_id": 37, "polygon": [[89.25, 345.75], [291.0, 345.75], [291.0, 356.2734375], [89.25, 356.2734375]]}, {"title": "17.6.5 Factorial HMM", "heading_level": null, "page_id": 38, "polygon": [[89.15625, 547.5], [200.25, 547.5], [200.25, 557.82421875], [89.15625, 557.82421875]]}, {"title": "17.6.6 Coupled HMM and the influence model", "heading_level": null, "page_id": 39, "polygon": [[87.75, 159.75], [316.5, 159.75], [316.5, 169.59375], [87.75, 169.59375]]}, {"title": "17.6.7 Dynamic Bayesian networks (DBNs)", "heading_level": null, "page_id": 39, "polygon": [[89.25, 520.5], [298.5, 520.5], [298.5, 530.61328125], [89.25, 530.61328125]]}, {"title": "Exercises", "heading_level": null, "page_id": 40, "polygon": [[129.75, 496.5], [178.5, 496.5], [178.5, 507.515625], [129.75, 507.515625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 29], ["SectionHeader", 4], ["Text", 3], ["TextInlineMath", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9399, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 51], ["Text", 7], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 728, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 35], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 113], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1112, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 51], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["Equation", 1], ["Code", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 480], ["Line", 44], ["TextInlineMath", 7], ["Equation", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 30], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 601, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 42], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 872, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 60], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2403, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 42], ["Text", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 42], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 39], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 637, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 43], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 1], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["Line", 57], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 833, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 38], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 75], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 954, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 35], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 878, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 40], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["ListItem", 2], ["FigureGroup", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1527, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 41], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 43], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 542], ["Line", 37], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 646], ["Line", 49], ["Equation", 13], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 43], ["Text", 5], ["Equation", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 49], ["TableCell", 32], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["Table", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3826, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["Line", 48], ["Text", 7], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 59], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 39], ["Text", 6], ["Equation", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 620], ["Line", 51], ["Equation", 9], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 87], ["Equation", 8], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 76], ["Text", 10], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 44], ["Text", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 35], ["Text", 7], ["SectionHeader", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 31], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 774, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 61], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2057, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 40], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 775, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 46], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 818, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 37], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 780, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 51], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 976, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 46], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 68], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1086, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 21], ["ListItem", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-21"}