# **CLIPScore**: A Reference-free Evaluation Metric for Image Captioning

<PERSON>† <PERSON>‡ <PERSON>‡ <PERSON>† <PERSON><PERSON>†‡ †Allen Institute for AI

‡Paul <PERSON> School of Computer Science & Engineering, University of Washington

{jackh,ronanlb}@allenai.org {ahai,mb<PERSON><PERSON><PERSON>,ye<PERSON>}@cs.washington.edu

## Abstract

Image captioning has conventionally relied on *reference-based* automatic evaluations, where machine captions are compared against captions written by humans. This is in contrast to the *reference-free* manner in which humans assess caption quality.

In this paper, we report the surprising empirical finding that CLIP [\(<PERSON> et al.,](#page-10-0) [2021\)](#page-10-0), a cross-modal model pretrained on 400M image+caption pairs from the web, can be used for robust automatic evaluation of image captioning without the need for references. Experiments spanning several corpora demonstrate that our new reference-free metric, CLIPScore, achieves the highest correlation with human judgements, outperforming existing reference-based metrics like CIDEr and SPICE. Information gain experiments demonstrate that CLIPScore, with its tight focus on *image–text* compatibility, is complementary to existing reference-based metrics that emphasize *text–text* similarities. Thus, we also present a reference-augmented version, RefCLIPScore, which achieves even higher correlation. Beyond literal description tasks, several case studies reveal domains where CLIPScore performs well (clip-art images, alt-text rating), but also where it is relatively weaker in comparison to reference-based metrics, e.g., news captions that require richer contextual knowledge.

## 1 Introduction

For most text generation tasks, reference-based ngram overlap methods are still the dominant means of automatic evaluation. For image caption generation, recent reference-based metrics have sought to transcend overlap by considering richer models of reference-candidate similarity: e.g., approximate scene graphs [\(Anderson et al.,](#page-8-0) [2016\)](#page-8-0), allowing reference-based methods to incorporate the image [\(Jiang et al.,](#page-9-0) [2019;](#page-9-0) [Lee et al.,](#page-9-1) [2020\)](#page-9-1). But, references can be expensive to collect and comparing

<span id="page-0-0"></span>Image /page/0/Picture/9 description: The image is a diagram illustrating how CLIPScore works and comparing it to traditional image captioning metrics. The top section, titled "How CLIPScore works," shows an image of two dogs running on a beach. A candidate caption, "Two dogs run towards each other on a marshy area," is compared to three reference captions. CLIPScore calculates a similarity score of 0.83. The bottom section, "CLIPScore vs traditional image captioning metrics," presents two examples. The first example shows a snowboarder, with a human score of 4/4 and a CLIPScore of 74.0, compared to a candidate caption "Person snowboarding at a ski slope." Traditional metrics like BLEU-1, SPICE, METEOR, and CIDER-D are shown with scores and checkmarks or crosses. The second example features a dirt biker, with a human score of 1/4 and a CLIPScore of 37.4, compared to a candidate caption "A grey dog walks on top of a fallen tree in the woods." Traditional metrics are also displayed for this example.

Figure 1: Top: CLIPScore uses CLIP to assess image-caption compatibility *without* using references, just like humans. Bottom: This frees CLIPScore from the well-known shortcomings of  $n$ -gram matching metrics, which disfavor good captions with new words (top) and favor *any* captions with familiar words (bottom). Attribution: Paperclip, robot icons by Hasanudin, Adiyogi (resp.) from the Noun Project.

against even *multiple* human-authored captions for each image is often insufficient (see [Figure 1\)](#page-0-0). As a result, for many corpora, a significant gap remains between reference-based scoring and human quality judgments.<sup>[1](#page-0-1)</sup>

Should we need references for the evaluation of image captions? After all, when humans assess the appropriateness of an image caption, we do so just by looking at the image and reading the candidate's text.

<span id="page-0-1"></span><sup>&</sup>lt;sup>1</sup>See [Elliott and Keller](#page-9-2) [\(2014\)](#page-9-2) and [Kilickaya et al.](#page-9-3) [\(2017\)](#page-9-3) for thorough comparisons of caption generation metrics.

A recent trend in machine translation serves as inspiration: there, a key hurdle for reference-free evaluation (sometimes called *quality estimation*) has been estimating cross-lingual similarity between source+candidate pairs [\(Blatz et al.,](#page-9-4) [2004;](#page-9-4) [Specia et al.,](#page-10-1) [2010;](#page-10-1) [Mehdad et al.,](#page-10-2) [2012;](#page-10-2) [Specia](#page-10-3) [and Shah,](#page-10-3) [2018\)](#page-10-3). But recent work [\(Lo,](#page-9-5) [2019;](#page-9-5) [Yankovskaya et al.,](#page-11-0) [2019;](#page-11-0) [Zhao et al.,](#page-11-1) [2020\)](#page-11-1) has improved correlation with human judgment not by gathering more monolingual references, but instead by utilizing cross-lingual representations learned by large-scale, pre-trained, multilingual models e.g., LASER [\(Artetxe and Schwenk,](#page-8-1) [2019\)](#page-8-1) or M-BERT [\(Devlin et al.,](#page-9-6) [2019\)](#page-9-6). [2](#page-1-0)

We hypothesize that the relationships learned by pretrained vision+language models (e.g., ALIGN [\(Jia et al.,](#page-9-7) [2021\)](#page-9-7) and CLIP [\(Radford et al.,](#page-10-0) [2021\)](#page-10-0)) could similarly support reference-free evaluation in the image captioning case. Indeed, they can: we show that a relatively direct application of CLIP to (image, generated caption) pairs results in surprisingly high correlation with human judgments on a suite of standard image description benchmarks (e.g., MSCOCO [\(Lin et al.,](#page-9-8) [2014\)](#page-9-8)). We call this process CLIPScore (abbreviated to CLIP-S). Beyond direct correlation with human judgments, an information gain analysis reveals that CLIP-S is complementary both to commonly reported metrics (like BLEU-4, SPICE, and CIDEr) and to newly proposed reference-based metrics (e.g., ViLBERTScore-F [\(Lee et al.,](#page-9-1) [2020\)](#page-9-1)).

We additionally (1) propose a referenceaugmented version of CLIPScore, RefCLIPScore, that achieves even higher human correlation, (2) verify that CLIP-S is sensitive to adversarially constructed image captions, where one noun-phrase has been swapped for a plausible (but incorrect) distractor; and (3) construct a corpus of images that have never been posted publicly online to verify that CLIP-S is able to reconstruct human judgments on never-before-seen images.

Finally, we assess CLIP-S in the context of four case studies that diverge from context-free, literal photograph description. In two cases, CLIP-S works well: it achieves high correlation with alt-text quality rating on Twitter, and demonstrates surprising capacity to reason about clipart images+captions. For news caption generation, reference-based methods correlate best with human judgments. And, for emotive captions inspired by language use on social media, even reference-based metrics fall short.

### 2 Related Work

Reference-only image caption evaluation In general, image caption generation models are evaluated by a suite of 5 reference based metrics: BLEU-4 [\(Papineni et al.,](#page-10-5) [2002\)](#page-10-5) (which measures a version of precision between a candidate and the references), ROUGE-L [\(Lin,](#page-9-10) [2004\)](#page-9-10) (which measures a version of recall), METEOR [\(Banerjee and](#page-8-2) [Lavie,](#page-8-2) [2005\)](#page-8-2) (which computes a word-level alignment), CIDEr [\(Vedantam et al.,](#page-10-6) [2015\)](#page-10-6) (which combines n-gram tf-idf weighting and stemming) and SPICE [\(Anderson et al.,](#page-8-0) [2016\)](#page-8-0) (which applies a semantic parser to a set of references, and computes similarity using the predicted scene graph). $3$ [Yi et al.](#page-11-3) [\(2020\)](#page-11-3) give a method for re-weighting BERTScore [\(Zhang et al.,](#page-11-4) [2020\)](#page-11-4) specifically tuned to the image caption generation domain (we refer to their method as BERT-S++).

Reference+image caption evaluation Recent metrics incorporate image-text grounding features in addition to references: TIGEr [\(Jiang et al.,](#page-9-0) [2019\)](#page-9-0) uses a pretrained SCAN model [\(Lee et al.,](#page-9-11) [2018\)](#page-9-11), and ViLBERTScore-F [\(Lee et al.,](#page-9-1) [2020\)](#page-9-1) uses a pretrained ViLBERT model [\(Lu et al.,](#page-9-12) [2019\)](#page-9-12) that is also fine-tuned on 12 downstream vision and language tasks [\(Lu et al.,](#page-9-13) [2020\)](#page-9-13). Our work provides perspective on the next logical extension: instead of incorporating visual-textual interactions in addition to references, can we ignore references entirely?

Self-retrieval for image captioning Prior works have proposed incorporating a *self-retrieval* loss into caption generation, with the intuition that good captions should be able to uniquely identify their images with high accuracy [\(Dai and Lin,](#page-9-14) [2017;](#page-9-14) [Luo](#page-10-7) [et al.,](#page-10-7) [2018;](#page-10-7) [Liu et al.,](#page-9-15) [2018\)](#page-9-15); monitoring this type of loss can provide insight into how distinctive the captions are according to the model itself. CLIP-S is similar in spirit, but distinct for its utility as an extrinsic evaluation metric like BLEU-4 or CIDEr.

Reference-free evaluation In addition to the machine translation cases highlighted in the introduction, reference-free evaluations have been proposed for other generation tasks, including summarization

<span id="page-1-0"></span> ${}^{2}$ [K et al.](#page-9-9) [\(2020\)](#page-9-9), [Pires et al.](#page-10-4) [\(2019\)](#page-10-4), and [Wu and Dredze](#page-11-2) [\(2019\)](#page-11-2) explore how M-BERT learns and utilizes cross-lingual information.

<span id="page-1-1"></span><sup>&</sup>lt;sup>3</sup>For comparison with these metrics, we use the standard COCO evaluation tools available at [https://github.](https://github.com/tylin/coco-caption) [com/tylin/coco-caption](https://github.com/tylin/coco-caption).

[\(Louis and Nenkova,](#page-9-16) [2013;](#page-9-16) [Peyrard and Gurevych,](#page-10-8) [2018;](#page-10-8) [Sun and Nenkova,](#page-10-9) [2019\)](#page-10-9) and dialogue [\(Tao](#page-10-10) [et al.,](#page-10-10) [2018;](#page-10-10) [Mehri and Eskenazi,](#page-10-11) [2020\)](#page-10-11). These metrics can be supervised, relying on human judgments for quality estimation, or less-supervised, relying on pre-trained model representations. For image captioning, a version of VIFIDEL [\(Madhyastha](#page-10-12) [et al.,](#page-10-12) [2019\)](#page-10-12) was proposed for reference-free evaluation; however, VIFIDEL, computed based on a list of detected objects in the image from a fixed object vocabulary, generally produces less correlation with human ratings vs. reference-based metrics.

#### 3 **CLIPScore**

Model Details. CLIP [\(Radford et al.,](#page-10-0) [2021\)](#page-10-0) is a cross-modal retrieval model trained on 400M (image, caption) pairs gathered from the web. 500K search queries, consisting of common unigram/bigrams, named entities, etc., were executed on a search engine. For each query, up to 20K (image, caption) pairs were collected.

The model we use is the  $ViT-B/32$  version.<sup>[4](#page-2-0)</sup> It represents images via a Vision Transformer [\(Vaswani et al.,](#page-10-13) [2017;](#page-10-13) [Dosovitskiy et al.,](#page-9-17) [2021\)](#page-9-17), which forgoes convolutional filters in favor of selfattention maps computed between a 7 by 7 grid of image patches, which evenly divides a 224 by 224 pixel input image. This model has 12 transformer layers and 86M parameters. The text is similarly represented by a 12-layer transformer trained over a vocab of 49K BPE token types [\(Sennrich et al.,](#page-10-14) [2016\)](#page-10-14) (and is more fully described in [Radford et al.](#page-10-15) [\(2019\)](#page-10-15)). Both the text and image networks output a single vector; these vectors aim to represent the content of an input caption or an image, respectively. In the case of ViT-B/32, these vectors are 512-D. The model's weights are trained to maximize the scaled cosine similarity of truly corresponding image/caption pairs while simultaneously minimizing the similarity of mismatched image/caption pairs using InfoNCE [\(Sohn,](#page-10-16) [2016;](#page-10-16) [Oord et al.,](#page-10-17) [2018\)](#page-10-17). We hold fixed this set of weights for our experiments.

Evaluating Caption Generations with CLIP. To assess the quality of a candidate generation, we pass both the image and the candidate caption through their respective feature extractors. Then, we compute the cosine similarity of the resultant embeddings. $5$  We found that prefixing candidates with the prompt: "A photo depicts" improved correlations slightly (and is our recommended/standard configuration), though "A photo of", the recommended prompt from [Radford et al.](#page-10-0) [\(2021\)](#page-10-0), worked well too. Following [Zhang et al.](#page-11-4) [\(2020\)](#page-11-4), we per-form a re-scaling operation.<sup>[6](#page-2-2)</sup> For an image with visual CLIP embedding  $v$  and a candidate caption with textual CLIP embedding c, we set  $w = 2.5$ and compute CLIP-S as:

$$
\text{CLIP-S}(\mathbf{c}, \mathbf{v}) = w * \max(cos(\mathbf{c}, \mathbf{v}), 0)
$$

To compute corpus-level CLIP-S, we simply average over (candidate, image) pairs. Note that this evaluation *does not depend on underlying references.* The runtime of CLIP-S with the ViT-B/32 backbone is fast: on our single consumer GPU and hard drive, roughly 4K image-candidate pairings can be processed per minute.

**RefCLIPScore** CLIP-S can additionally be extended to incorporate references, if they are available. We extract vector representations of each available reference by passing them through CLIP's text transformer; the result is the set of vector representation of all references, R. Then, RefCLIPScore is computed as a harmonic mean of CLIP-S, and the maximal reference cosine similarity, i.e.,

 $RefCLIP-S(c, R, v) =$ H-Mean(CLIP-S(c, v),  $\max(\max_{\mathbf{r}\in\mathbf{R}}cos(\mathbf{c}, \mathbf{r}), 0))$ 

# <span id="page-2-4"></span>4 Benchmark Captioning Evaluations

We first evaluate on a set of literal description corpora. Broadly, the captions in these corpora aim to identify and highlight the literal, salient objects/actions in a photographic image, presented without additional context.<sup>[7](#page-2-3)</sup>

<span id="page-2-0"></span><sup>&</sup>lt;sup>4</sup>We expect that more powerful, larger versions of the model, if released at a later date, could perform better.

<span id="page-2-1"></span><sup>&</sup>lt;sup>5</sup>More sophisticated CLIP configurations, e.g., regionlevel/token-level correspondence models, did not achieve better performance.

<span id="page-2-2"></span><sup>&</sup>lt;sup>6</sup>While the cosine similarity, in theory, can range from  $[-1, 1]$  (1) we never observed a negative cosine similarity; and (2) we generally observe values ranging from roughly zero to roughly .4. The particular value of  $w$  we advocate for,  $w = 2.5$ , attempts to stretch the range of the score distribution to [0, 1]. For more details and justification for our re-scaling, including a demonstration of generality across several corpora, see [Appendix B\)](#page-13-0).

<span id="page-2-3"></span><sup>&</sup>lt;sup>7</sup>See [Berg et al.](#page-8-3) [\(2012\)](#page-8-3) for a statistical exploration of salience in a such a corpus.

<span id="page-3-0"></span>

|                                      | $\tau_c$    |
|--------------------------------------|-------------|
| BLEU-1                               | 32.3        |
| BLEU-4                               | 30.8        |
| ROUGE-L                              | 32.3        |
| BERT-S (RoBERTa-F)                   | 39.2        |
| <b>METEOR</b>                        | 41.8        |
| CIDEr                                | 43.9        |
| <b>SPICE</b>                         | 44.9        |
| LEIC $(\tau_b)^*$ (Cui et al., 2018) | 46.6        |
| BERT-S++ (Yi et al., 2020)           | 46.7        |
| TIGEr (Jiang et al., 2019)           | 49.3        |
| NUBIA $*$ (Kane et al., 2020)        | 49.5        |
| ViLBERTScore-F (Lee et al., 2020)    | 50.1        |
| CLIP-S (no refs)                     | 51.2        |
| RefCLIP-S                            | <b>53.0</b> |

Table 1: Flickr8K-Expert correlations with human judgment. All metrics use 4-5 ground truth references, except for CLIP-S (which uses none). \* indicates a result reported in prior work.

## 4.1 Caption-level likert judgments

We first explore three corpora consisting of human likert-scale judgments at the level of individual image/caption pairs. Flickr8K-Expert [\(Hodosh et al.,](#page-9-20) [2013\)](#page-9-20) contains 17K "expert" human judgments between 5664 images: humans graded captions on a scale of 1 to 4 (4="caption describes the image without any errors"; 1="caption is unrelated to the image"). Flickr8K-CF is a set of 145K binary quality judgments gathered from CrowdFlower over 48K (image, caption) pairs (1K unique images). Each pair has at least 3 binary judgments, and we take the mean proportion of "yes" annotations as a score for each pair to compute correlations.

Composite [\(Aditya et al.,](#page-8-4) [2015\)](#page-8-4) contains 12K human judgments between images from MSCOCO (2007 images), Flickr8k (997 images), and Flickr30k [\(Young et al.,](#page-11-5) [2014\)](#page-11-5) (991 images). Each image originally has five references, but one of the references was selected to be rated by humans in the set (and so we remove it from the reference set when computing metrics; this differs from some prior work, see [Appendix A](#page-12-0) for why we consider the more difficult setting). For Composite and Flickr8K judgments, we compute correlation between each metric and the human ratings using Kendall  $\tau$ .

Results The results for Flickr8K-Expert are given in [Table 1,](#page-3-0) for Flickr8K-CF are given in [Ta](#page-3-1)[ble 2](#page-3-1) (in  $\tau_b$ , following [Cui et al.](#page-9-18) [\(2018\)](#page-9-18)), and for Composite are given in [Table 3.](#page-4-0) For the captionlevel corpora we consider, CLIP-S without refer-

<span id="page-3-1"></span>

|                    | Ίh   |
|--------------------|------|
| <b>BLEU-4</b>      | 16.9 |
| $C\text{IDEr}$     | 24 6 |
| <b>METEOR</b>      | 22.2 |
| ROUGE-L            | 19.9 |
| <b>SPICE</b>       | 24 4 |
| BERT-S (RoBERTa-F) | 22.8 |
| LEIC *             | 29.5 |
| $CLIP-S$ (no refs) | 34 4 |
| RefCLIP-S          | 36.4 |

Table 2: Flickr8K-CF correlations with human judgment. \* indicates a result reported in prior work.

ences achieves higher correlation with human judgment compared to previously proposed metrics that rely on references. Additionally, in all cases, RefCLIP-S improves correlation even further. This provides strong evidence that, in terms of correlating with human judgment at the caption-level for these literal photographic image description tasks, a relatively direct application of CLIP can serve as a strong automatic evaluation metric.

### 4.2 Pairwise ranking on Pascal-50S

In Pascal-50S [\(Vedantam et al.,](#page-10-6) [2015\)](#page-10-6), raters made pairwise preference judgments between pairs of sentences. There are 4K sentence pairs total, split evenly across four categories, e.g., two human captions, two machine captions, etc. For each pair, 48 human pairwise judgments were gathered.<sup>[8](#page-3-2)</sup> Following prior work, instead of computing correlation coefficients, we compute accuracy, i.e., we consider the caption preferred by a majority of annotators to be correct, and measure how often the evaluation metric assigns a higher score to that member of the pair. Ties are broken randomly. Due to random selection of 5 references among the 48 candidates to serve as ground-truth for the reference-based metrics, the results may differ slightly from prior work (we average over 5 random draws of references).

The results are given in [Table 4.](#page-4-1) Evaluation is split across four categories of caption pairs (detailed in the table caption). CLIP-S and RefCLIP-S generally achieve high performance in all categories.

<span id="page-3-2"></span><sup>&</sup>lt;sup>8</sup>Instead of being presented with the image, annotators were presented only with a reference (and the two candidates to rank).

<span id="page-4-0"></span>

|                    | $\tau_c$    |
|--------------------|-------------|
| BLEU-1             | 31.3        |
| BLEU-4             | 30.6        |
| ROUGE-L            | 32.4        |
| BERT-S (RoBERTa-F) | 30.1        |
| METEOR             | 38.9        |
| CIDEr              | 37.7        |
| SPICE              | 40.3        |
| BERT-S++*          | 44.9        |
| TIGEr              | 45.4        |
| ViLBERTScore-F     | 52.4        |
| CLIP-S (no refs)   | 53.8        |
| RefCLIP-S          | <b>55.4</b> |

Table 3: Composite correlations with human judgment. All metrics use between 4 and 5 ground truth references, except for CLIP-S (which uses none). In contrast to some prior work, we consider a harder setting, and remove the candidate from the reference set (see [Ap](#page-12-0)[pendix A](#page-12-0) for details; for comparison purposes, RefCLIP-S achieves  $\tau_c = 60.0$  in the easier setting). \* indicates a result reported in prior work.

### 4.3 System-level correlation for MSCOCO

CLIP-S achieves high correlation with human judgments at the system-level as well: we evaluate the outputs of systems submitted to the 2015 MSCOCO Image Captioning Challenge [\(Vinyals](#page-10-18) [et al.,](#page-10-18) [2016\)](#page-10-18). We have some concerns with standard evaluation setup on this corpus, mostly related to the fact that it consists of only 12 datapoints (see supplementary for more discussion). Nonetheless, following the standard procedure, we correlate CLIP-S and RefCLIP-S with two metrics: "the percentage of captions that are evaluated as better or equal to a human caption (M1)" and percentage of captions that pass the "Turing Test" (M2), respectively. CLIP-S achieves Spearman  $\rho_{M1}/\rho_{M2} = .59/.63$ and RefCLIP-S achieves  $\rho_{M1}/\rho_{M2} = .69/.74$  (all  $p < .05$ ) with these system-level metrics.

#### 4.4 Sensitivity of CLIP-S to hallucination

Prior work has demonstrated that, for many literal description tasks, humans often prefer *correctness* in captions over specificity [\(Rohrbach et al.,](#page-10-19) [2018,](#page-10-19)  $2017$ .<sup>[9](#page-4-2)</sup> Thus, understanding if and how evaluation metrics handle image captions that contain incorrect "hallucinations," e.g., references to objects that

<span id="page-4-1"></span>

| HC                 | HI          | HM          | MM          | Mean        |             |
|--------------------|-------------|-------------|-------------|-------------|-------------|
| length             | 51.7        | 52.3        | 63.6        | 49.6        | 54.3        |
| BLEU-4             | 60.4        | 90.6        | 84.9        | 54.7        | 72.6        |
| SPICE              | 63.6        | 96.3        | 86.7        | 68.3        | 78.7        |
| METEOR             | 63.8        | 97.7        | 93.7        | 65.4        | 80.1        |
| ROUGE-L            | 63.7        | 95.3        | 92.3        | 61.2        | 78.1        |
| CIDEr              | 65.1        | 98.1        | 90.5        | 64.8        | 79.6        |
| BERT-S (RoBERTa-F) | 65.4        | 96.2        | 93.3        | 61.4        | 79.1        |
| TIGEr*             | 56.0        | <b>99.8</b> | 92.8        | 74.2        | 80.7        |
| ViLBERTScore-F*    | 49.9        | <b>99.6</b> | 93.1        | <b>75.8</b> | 79.6        |
| BERT-S++*          | <b>65.4</b> | <b>98.1</b> | <b>96.4</b> | 60.3        | 80.1        |
| CLIP-S (no refs)   | 56.5        | <b>99.3</b> | <b>96.4</b> | 70.4        | 80.7        |
| RefCLIP-S          | 64.5        | <b>99.6</b> | 95.4        | 72.8        | <b>83.1</b> |

Table 4: Pascal50S accuracy results (5 references). HC = two human correct captions; HI = both captions are human written, but one is wrong; HM = both captions are for the image, but one is written by a human, one by an algorithm;  $MM =$  both captions are for the image, and both are written by an algorithm. \* indicates a result reported in prior work: the comparability of our results to \*-rows is subject to the (arbitrary) sample of references. We average our results over 5 random samples (but CLIP-S doesn't change because it doesn't use references).

are not depicted, is important. We use a sample of image captions from the FOIL dataset, constructed by [Shekhar et al.](#page-10-22) [\(2017\)](#page-10-22), to test how sensitive CLIP-S is to detecting potentially subtle inaccurate details in descriptions. This corpus consists of modified reference captions from MSCOCO that have a single noun-phrase adversarially swapped out to make the FOIL caption incorrect, e.g., switching "motorcycle" for "bicycle".

To adapt the corpus to our setting, for each of the 32K test images, we sample a (FOIL, true) pair, and compute the accuracy of each evaluation metric in their capacity to assign a higher score to the true candidate versus the FOIL. To compute referencebased metrics, we give access to the MSCOCO reference captions for the image (excluding the the true candidate being assessed against the FOIL). While the paired setting we consider isn't identical, [Shekhar et al.](#page-10-22) [\(2017\)](#page-10-22) estimate roughly 92% human agreement on the unpaired version of the task, relative to a 50/50 random guessing baseline.

[Table 5](#page-5-0) contains the results. In this setting, having access to more annotation is quite helpful for reference based metrics, e.g., the accuracy of SPICE and BLEU-4 increase by over ten points when shifting from one to four references. But in the reference-limited setting, CLIP-S, without *any* ref-

<span id="page-4-2"></span> $9$ This is not always the case: [MacLeod et al.](#page-10-21) [\(2017\)](#page-10-21) show there is a range of opinion among a sample of low vision and blind users of social media.

<span id="page-5-0"></span>

|                    | 1-ref | 4-ref |
|--------------------|-------|-------|
| length             | 50.2  | 50.2  |
| BLEU-4             | 66.5  | 82.6  |
| METEOR             | 78.8  | 85.4  |
| ROUGE-L            | 71.7  | 79.3  |
| CIDEr              | 82.5  | 90.6  |
| SPICE              | 75.5  | 86.1  |
| BERT-S (RoBERTa-F) | 88.6  | 92.1  |
| CLIP-S (no refs)   | 87.2  | 87.2  |
| RefCLIP-S          | 91.0  | 92.6  |

Table 5: Accuracy of evaluation metrics in the pairwise FOIL hallucination detection setting. All referencebased metrics are given access to either one or four references.

erence outperforms all metrics except for BERT-S (RoBERTa-F). And, RefCLIP-S works best in all cases.

Overall, we corroborate [Rohrbach et al.](#page-10-19) [\(2018\)](#page-10-19)'s finding that "object hallucination can not be always predicted based on the traditional sentence metrics" using a corpus derived from [Shekhar et al.](#page-10-22) [\(2017\)](#page-10-22), particularly in the case where there are few references available. However, CLIP-S and RefCLIP-S offer a performance improvement in the pairwise setting.

#### 4.5 Sensitivity of CLIP-S to memorization

One concern with model-based scoring methods is memorization, i.e., if a model's weights are pretrained using a large corpus, there's a risk that data used at evaluation time have already been seen at pretraining time. While [Radford et al.](#page-10-0) [\(2021\)](#page-10-0) conduct a train-test overlap analysis and find that CLIP is unlikely to succeed because of memorization, we nonetheless conduct an experiment with images CLIP has never seen before.

The authors of this work created a set of 250 images that have never been posted to the Internet by aggregating personal photographs. The set contains a variety of Flickr-like situations, e.g., nature scenes, animals, city streets, objects, etc. For each image, we collect two automatically generated captions: one from a commercial API, Microsoft Azure Cognitive Services (v  $3.1$ )<sup>[10](#page-5-1)</sup> and one from [Luo et al.](#page-10-7) [\(2018\)](#page-10-7)'s pretrained model, which is trained to maximize CIDEr score with a self-critical

<span id="page-5-5"></span>Image /page/5/Figure/8 description: The image displays two line graphs side-by-side, comparing the performance of RefCLIPScore and CLIPScore across different importance ranks on two datasets: Composite (a) and Flickr8k-Expert (b). Both graphs plot R^2 on the y-axis against Importance Rank on the x-axis, with ranks labeled as #1, #3, and #5. In the Composite graph, RefCLIPScore starts at approximately 43 R^2 at rank #1 and increases to about 47 R^2 at rank #5, with intermediate points around 45 R^2 at rank #3. CLIPScore starts lower at around 38 R^2 at rank #1 and rises to approximately 46 R^2 at rank #5, with an intermediate point around 42 R^2 at rank #3. Various model names like VILBERT, B-1, SPICE, TIGEr, BERTSc are annotated along the lines. The Flickr8k-Expert graph shows a similar trend but with higher R^2 values. RefCLIPScore begins around 45 R^2 at rank #1 and reaches about 55 R^2 at rank #5. CLIPScore starts at approximately 40 R^2 at rank #1 and climbs to about 53 R^2 at rank #5. This graph also includes annotations for models such as VILBERT, CIDEr, TIGEr, ROUGE, and B-4. Both graphs indicate that RefCLIPScore generally outperforms CLIPScore, especially at higher importance ranks.

Figure 2:  $R^2$  for the forward-selection regression of metrics on human Likert ratings for two corpora. Foward-selection tends to identify both CLIP-S and RefCLIP-S early-on: other informative and complementary metrics include ViLBERTScore-F and SPICE.

baseline.<sup>[11](#page-5-2)</sup> Then, for each image, three authors of this work independently selected which caption described the image content more accurately. Relative to a 50% random baseline (and a 72% length baseline of selecting the *shorter* caption) CLIP-S correctly recovers majority human preference in 86% of cases. Human agreement for this corpus is 93%.[12](#page-5-3)

While this setup cannot definitively refute the notion that CLIP works well because it has memorized images, we hope the results here contribute to the evolving discussion about the nature of generalization for web-scale pretrained models.

#### <span id="page-5-6"></span>4.6 Which metrics should I report?

Most caption generation works report multiple metrics, each of which (presumably) correlates with human judgment to different degrees. But it's not always clear if individual metrics capture distinct or redundant dimensions of human judgment. For example, while CLIP-S and ViLBERTScore-F both produce high correlations, are they redundant or complementary?

We seek a (minimal) set of metrics that explains the most variance in human judgment. To find this set, we undertake a forward selection on a set of ten candidate metrics comprising six widelyreported metrics,[13](#page-5-4) and four newer metrics, BERT-S (RoBERTa-F), TIGEr, ViLBERTScore-F, and CLIP-S (we also include experiments starting with RefCLIP-S instead of CLIP-S, too). Starting from an empty set, we perform an iterative greedy selection by picking

<span id="page-5-1"></span><sup>10</sup>[https://azure.microsoft.com/en-us/](https://azure.microsoft.com/en-us/services/cognitive-services/) [services/cognitive-services/](https://azure.microsoft.com/en-us/services/cognitive-services/)

<span id="page-5-2"></span> $11$ We use the ResNet101 pretrained version, which achieves 1.05 CIDEr and 0.19 SPICE on the COCO validation set.

<span id="page-5-3"></span><sup>&</sup>lt;sup>12</sup>Raters preferred the Microsoft captions to the ResNet101 model 81% of the time.

<span id="page-5-4"></span><sup>13</sup>BLEU-1, BLEU-4, METEOR, CIDEr, ROUGE-L, SPICE

the most informative additional metric to add.<sup>[14](#page-6-0)</sup> To estimate variance, we repeat the forward-selection process 10 times with bootstrap re-sampled versions of the corpus.

[Figure 2](#page-5-5) shows the information gain that results from running this experiment on the Composite and Flickr8K-Expert corpora; we also show which metric is most commonly selected at each iteration (earlier = more information gain). For Composite, CLIP-S (or RefCLIP-S) is always selected first, followed by ViLBERTScore-F, and then (most commonly) BERT-S (RoBERTa-F). For Flickr8k-Expert, the top three choices are always CLIP-S (or RefCLIP-S), ViLBERTScore-F, and SPICE. While CLIP-S and ViLBERTScore-F tend to be the most informative metrics, (1) while they are correlated, they are not purely redundant; and (2) image-unaware, reference-based metrics like SPICE can still be useful.

In summary, these results suggest that evaluation metrics like CLIP-S, which take into account visual content, indeed capture axes of human judgment not currently covered by text-only reference-based metrics. *For the literal image description evaluation settings we consider, a reasonable mix of metrics to report is at least one image-aware metric (e.g., CLIP-S) plus a strong reference-only metric (e.g., SPICE).*

### <span id="page-6-3"></span>5 Case Studies Using **CLIPScore**

Our results thus far have demonstrated that CLIP encodes information useful for evaluating literal image description tasks. But, reference-based metrics may *a priori* seem more adaptable versus CLIP-S. Does CLIP-S correlate with human judgment beyond cases like MSCOCO and Flickr8K?

To address this question, we consider four case studies, exploring the correlation between CLIP-S and human judgment across "divergent" image description datasets. These corpora qualitatively differ from the more popular domains explored in [§4,](#page-2-4) either because the images are not "everyday" images from Flickr, or because the captions are not literal description [\(Figure 3](#page-6-1) illustrates).

## 5.1 Alt-Text ratings from Twitter

When uploading an image alongside a tweet, users of Twitter have the option of providing alterna-

<span id="page-6-1"></span>Image /page/6/Figure/9 description: The image is a grid of four images with accompanying text. The top left image shows the blue OPEC logo against a blurred background of flags. The text to the right describes the logo for OPEC, the Organization of the Petroleum Exporting Countries, shown against a background of flags. The top middle image shows a body of water with a tropical island in the background, under the heading "Personality Captions". The text to the right is a quote: "[Miserable:] I'm ready to get to shore now. I hate the waves. Nothing to do on shore either." The bottom left image is a cartoon illustration of a boy and a girl with a dog next to a grill, under the heading "Abstract-50S". The text to the right describes the cartoon: "An angry Jenny is holding a hot dog next to the grill while Mike sits." The bottom middle image shows a large, ornate hall filled with people, under the heading "GoodNews". The text to the right states: "LATES with MasterCard at London's Natural History Museum."

Figure 3: Instances from our four case-study corpora.

tive text: while few use this feature [\(Gleason et al.](#page-9-21) [\(2019\)](#page-9-21) find that fewer than  $.1\%$  of image tweets have alt-text), its broader adoption might someday make social media more accessible for low vision and blind users. We measure CLIP-S's capacity to reconstruct a set of 2.8K human judgments of alttext quality. This corpus was collected and rated by the authors of [Gleason et al.](#page-9-21) [\(2019,](#page-9-21) [2020\)](#page-9-22). Each alt-text was rated on a scale of 0 to 3 in terms of its probable utility as an alt-text. While the humanraters raters themselves are sighted thus cannot directly assess the utility of a given alt-text to a low vision or blind user, they are experts in designing and evaluating alt-text systems. Tweets were sampled from a mix of the Twitter FireHose API, and the timelines of low vision and blind users of the site. The images, qualitatively, are a broader mix of web content in comparison to Flickr-like domains, e.g., screenshots, memes, etc. Alt-text candidates are a mix of user-uploaded and machine-generated. The corpus contains no references, but for the purposes of comparison to reference-based metrics, we (programmatically) treat any textual context of the tweet as a reference.

CLIP-S achieves 48.4  $\tau_c$  correlation with the human judgements. In contrast, likely due to the unreliability of Tweet texts as viable alt-texts, reference-based methods struggle: the best performing purely-reference based metric, BERT-S (RoBERTa-F) (which achieves 15  $\tau_c$ ) under-performs relative to length baseline (which achieves 25  $\tau_c$ ). While gathering high-quality, contextual reference alt-texts is a promising avenue for future work,  $15$ CLIP-S offers a promising evaluation metric candidate in this domain.

### 5.2 Abstract-50S

We assess CLIP-S's capacity to generalize to abstract, non-photographic clip-art images using Abstract-50S [\(Vedantam et al.,](#page-10-6) [2015\)](#page-10-6). This dataset

<span id="page-6-0"></span><sup>&</sup>lt;sup>14</sup>Our criteria is how much additional  $R^2$  correlation with human judgment a metric adds according to a linear regression. We use sklearn [\(Pedregosa et al.,](#page-10-23) [2011\)](#page-10-23)'s forward selection, which applies 5-fold cross-validation at each step.

<span id="page-6-2"></span><sup>&</sup>lt;sup>15</sup>See [Stangl et al.](#page-10-24) [\(2020\)](#page-10-24), who conducted user-studies across six domains.

pairs clip-art images (originally constructed by [Zit](#page-11-6)[nick and Parikh](#page-11-6) [\(2013\)](#page-11-6)) with 48 human-written reference captions. These images depict two cartoon characters, Mike and Jenny, in various outdoor situations, e.g., playing sports, having a picnic, etc. For 400 human-written candidate caption pairs (200 pairs are from the same image, 200 are from different images), human judgments were collected: annotators were instructed to choose which of the paired captions were more similar to each reference caption, so 48 judgments were collected for each candidate pair (for a total of 19200).

We compare CLIP-S to several reference-based metrics when given access to a random sample of five reference captions. Following our procedure for Pascal-50S, we randomly re-sample 5 times, and report average pairwise accuracy. Two baselines (BL) both achieve 53: length-only (i.e., saying the longer caption is better); and randomly shuffling images as input to CLIP-S (so that it cannot rely on meaningful visual-textual interactions).

| BL | BLEU-4 | CIDEr | METEOR | BERT-S | CLIP-S (no refs) |
|----|--------|-------|--------|--------|------------------|
| 53 | 71     | 79    | 79     | 73     | 68               |

Overall, while CLIP-S underperforms relative to the reference-based metrics, it outperforms the baselines by a wide margin. This result suggests that CLIP-S is capable of reasoning about visualtextual interactions, even in non-photographic images.

### 5.3 Personality Captions

Inspired by language use on social media, [Shuster](#page-10-25) [et al.](#page-10-25) [\(2019\)](#page-10-25) collected image captions by prompting annotators with a "personality" (e.g., dramatic, sympathetic, sad, etc.) and asking them to "write a comment in the context of [a] given personality trait... about an image that someone else would find engaging." To evaluate their models, the authors collected pairwise human judgments, where evaluators were instructed to "to pick which comment is the most engaging". We assess CLIP-S in two capacities: (1) does it prefer literal descriptions, or the less-literal, more engaging, personality captions?; and (2) if it is given two personality captions, can it predict which humans judge to be more engaging?

For (1): Over a set of 2.4K "traditional" vs. personality captions pairwise ratings, humans rate the personality captions to be more engaging 65% of the time, whereas CLIP-S prefers the traditional 80%

of the time.[16](#page-7-0) Our takeaway: when given a direct description and a more engaging, non-literal caption, CLIP-S will generally prefer the literal.

For (2): CLIP-S performs slightly better than random, e.g., 57% over 2.5K human pairwise judgments comparing two neural generator models: TransResNet (ResNeXt-IG-3.5B) vs. TransRes-Net (ResNet-152) (see [Shuster et al.](#page-10-25) [\(2019\)](#page-10-25) Table 7, Row 5), but no better than a length-only baseline (also 57%). Notably, even reference-based metrics fail to provide correlation with pairwise human judgment of engagingness on this corpus: e.g., BLEU-4, CIDEr, and SPICE agree with human judgment 52%/53%/51% when provided with one personality-primed reference. Our takeaway: when given two engaging, non-literal descriptions, both CLIP-S and traditional reference-based metrics fail to predict which humans will judge to be more engaging.

### 5.4 News image captioning

[Biten et al.](#page-9-23) [\(2019\)](#page-9-23) consider caption generation for images from New York Times articles; their task differs from MSCOCO because 1) 95% of captions contain at least one named entity, e.g., a politician, celebrity, or place; and 2) captions generally "do not describe scene objects, but rather offer a contextualized interpretation of the scene." They collected 2.1K pairwise human judgments over 106 images that compare the performance of two news image captioning models. For each image, 20 annotators were instructed to pick which of two model generations was closer to the ground-truth caption (they were also presented with the image itself). We compare metrics in terms of their accuracy in matching human judgment between the two candidates.

Reference-based metrics dominate: METEOR and BLEU-4 achieve the highest accuracies of 93 and 91 respectively, whereas CLIP-S achieves only slightly above random at 65. Qualitatively, CLIP-S succeeds when there are visually-verifiable content, e.g., matching black-and-white photos to older dates (e.g., picking 1933 vs. 1977, in one case), and matching particularly iconic celebrities (e.g., it confidently identifies Muhammad Ali boxing).[17](#page-7-1) But, its most common failure case are captions that may

<span id="page-7-0"></span><sup>&</sup>lt;sup>16</sup>Preliminary prompt-engineering experiments (e.g., "when I look at this photo, I feel [PERSONALITY] and think [CAP-TION]") could not overcome this.

<span id="page-7-1"></span> $17$ [Luo et al.](#page-10-26) [\(2021\)](#page-10-26)'s recent experiments quantitatively demonstrate that CLIP is capable of reasoning about realworld entities within news images.

simply be unverifiable given only the image content. For example: CLIP-S selects "The dining room at Elle Decor" for an image of a room, but annotators preferred a caption that mentioned "the Junior League of New York;" the ground truth caption reveals why the image was pictured in the first place: "A Manhattan home on a May 7 tour by the Junior League of New York."

Overall, we do not advocate for reference-free evaluation in this case, especially because our results suggest that (at least for this particular set of annotations) reference-based n-gram overlap metrics achieve high correlation with human judgment.

## 6 Conclusion

For literal image description tasks, CLIPScore achieves high correlation with human judgments of caption quality *without* references when used in an off-the-shelf fashion. Additional experiments in divergent domains suggest that CLIP can also reason about non-photographic clip-art, and serves as a reasonable option for reference-free evaluation in the alt-text case. Promising future work includes exploring 1) CLIP-S as a reinforcement learning reward for literal caption generators; and 2) whether a small amount of labelled human rating data could help CLIP-S adapt to domains where it struggles, e.g., engagingness prediction. We hope our work can contribute to the ongoing discussion about the role of pretrained models in generation evaluation.

Reference-free evaluation runs some risks. Much like BERTScore, model-based metrics like CLIP-S reflect the biases of the pre-training data. While we believe that using CLIP-S as an offline evaluation metric for literal caption quality accords with the recommendations of CLIP's model card<sup>[18](#page-8-5)</sup> [\(Mitchell et al.,](#page-10-27) [2019\)](#page-10-27), [Agarwal et al.](#page-8-6) [\(2021\)](#page-8-6)'s study demonstrates that CLIP can make disproportionate incorrect classifications of people, e.g., "male images were misclassified into classes related to crime." Exploring potential social biases of candidate generations (as in, e.g., [Hendricks et al.](#page-9-24) [\(2018\)](#page-9-24)) remains paramount, particularly if a system is to be deployed.

Contemporaneous work While this work was under submission, two alternate reference-free evaluation metrics for image caption generation were introduced: FAIEr [\(Wang et al.,](#page-10-28) [2021\)](#page-10-28) (based on a pretrained object detector, and fine-tuned on

MSCOCO) and UMIC [\(Lee et al.,](#page-9-25) [2021\)](#page-9-25) (based on UNITER [\(Chen et al.,](#page-9-26) [2020\)](#page-9-26)). UMIC, in particular, produces similar correlations with human judgment on the literal image description tasks ([§4\)](#page-2-4) compared to CLIP-S, but with the complementary approach of fine-tuning on synthetic negative captions. Future work would be well-suited to explore if the textual data augmentations proposed by [Lee](#page-9-25) [et al.](#page-9-25) [\(2021\)](#page-9-25) (1) result in a metric that complements or overlaps with the non-finetuned CLIP-S ([§4.6\)](#page-5-6); and (2) could be extended beyond cases of literal description  $(\S 5)$ .

### Acknowledgements

This research is supported in part by DARPA MCS program through NIWC Pacific (N66001-19-2- 4031), DARPA SemaFor program, and the Allen Institute for AI. We additionally thank Ximing Lu, Swabha Swayamdipta, Youngjae Yu, and the anonymous EMNLP reviewers for the helpful comments, thoughts, and discussions. Finally, we thank Jin-Hwa Kim, who in March 2022, helped discover a now fixed discrepancy for the Pascal-50S results, see Appendix [A.](#page-12-1)

### References

- <span id="page-8-4"></span>Somak Aditya, Yezhou Yang, Chitta Baral, Cornelia Fermuller, and Yiannis Aloimonos. 2015. From images to sentences through scene description graphs using commonsense reasoning and knowledge. *arXiv preprint arXiv:1511.03292*.
- <span id="page-8-6"></span>Sandhini Agarwal, Gretchen Krueger, Jack Clark, Alec Radford, Jong Wook Kim, and Miles Brundage. 2021. Evaluating CLIP: Towards characterization of broader capabilities and downstream implications. *arXiv preprint arXiv:2108.02818*.
- <span id="page-8-0"></span>Peter Anderson, Basura Fernando, Mark Johnson, and Stephen Gould. 2016. Spice: Semantic propositional image caption evaluation. In *ECCV*. Springer.
- <span id="page-8-1"></span>Mikel Artetxe and Holger Schwenk. 2019. Massively multilingual sentence embeddings for zeroshot cross-lingual transfer and beyond. *TACL*, 7:597–610.
- <span id="page-8-2"></span>Satanjeev Banerjee and Alon Lavie. 2005. METEOR: an automatic metric for mt evaluation with improved correlation with human judgments. In *ACL workshop on Evaluation Measures for MT and Summarization*.
- <span id="page-8-3"></span>Alexander C. Berg, Tamara L. Berg, Hal Daumé III, Jesse Dodge, Amit Goyal, Xufeng Han, Alyssa Mensch, Margaret Mitchell, Aneesh Sood, Karl Stratos, and Kota Yamaguchi. 2012. Understanding and predicting importance in images. In *CVPR*.

<span id="page-8-5"></span><sup>18</sup>[https://github.com/openai/CLIP/blob/](https://github.com/openai/CLIP/blob/main/model-card.md) [main/model-card.md](https://github.com/openai/CLIP/blob/main/model-card.md)

- <span id="page-9-23"></span>Ali Furkan Biten, Lluis Gomez, Marçal Rusinol, and Dimosthenis Karatzas. 2019. Good news, everyone! context driven entity-aware captioning for news images. In *CVPR*.
- <span id="page-9-4"></span>John Blatz, Erin Fitzgerald, George Foster, Simona Gandrabur, Cyril Goutte, Alex Kulesza, Alberto Sanchis, and Nicola Ueffing. 2004. Confidence estimation for machine translation. In *COLING*.
- <span id="page-9-26"></span>Yen-Chun Chen, Linjie Li, Licheng Yu, Ahmed El Kholy, Faisal Ahmed, Zhe Gan, Yu Cheng, and Jingjing Liu. 2020. Uniter: Universal image-text representation learning. In *ECCV*.
- <span id="page-9-18"></span>Yin Cui, Guandao Yang, Andreas Veit, Xun Huang, and Serge Belongie. 2018. Learning to evaluate image captioning. In *CVPR*.
- <span id="page-9-14"></span>Bo Dai and Dahua Lin. 2017. Contrastive learning for image captioning. In *NeurIPS*.
- <span id="page-9-6"></span>Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. 2019. BERT: pre-training of deep bidirectional transformers for language understanding. In *NAACL*.
- <span id="page-9-17"></span>Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, Jakob Uszkoreit, and Neil Houlsby. 2021. An image is worth 16x16 words: Transformers for image recognition at scale. In *ICLR*.
- <span id="page-9-2"></span>Desmond Elliott and Frank Keller. 2014. Comparing automatic evaluation measures for image description. In *ACL*.
- <span id="page-9-21"></span>Cole Gleason, Patrick Carrington, Cameron Cassidy, Meredith Ringel Morris, Kris M Kitani, and Jeffrey P Bigham. 2019. "it's almost like they're trying to hide it": How user-provided image descriptions have failed to make twitter accessible. In *WWW*.
- <span id="page-9-22"></span>Cole Gleason, Amy Pavel, Emma McCamey, Christina Low, Patrick Carrington, Kris M Kitani, and Jeffrey P Bigham. 2020. Twitter a11y: A browser extension to make twitter images accessible. In *CHI*.
- <span id="page-9-24"></span>Lisa Anne Hendricks, Kaylee Burns, Kate Saenko, Trevor Darrell, and Anna Rohrbach. 2018. Women also snowboard: Overcoming bias in captioning models. In *Proceedings of the European Conference on Computer Vision (ECCV)*, pages 771–787.
- <span id="page-9-20"></span>Micah Hodosh, Peter Young, and Julia Hockenmaier. 2013. Framing image description as a ranking task: Data, models and evaluation metrics. *JAIR*, 47:853– 899.
- <span id="page-9-7"></span>Chao Jia, Yinfei Yang, Ye Xia, Yi-Ting Chen, Zarana Parekh, Hieu Pham, Quoc V Le, Yunhsuan Sung, Zhen Li, and Tom Duerig. 2021. Scaling up visual and vision-language representation learning with noisy text supervision. In *ICML*.

- <span id="page-9-0"></span>Ming Jiang, Qiuyuan Huang, Lei Zhang, Xin Wang, Pengchuan Zhang, Zhe Gan, Jana Diesner, and Jianfeng Gao. 2019. TIGEr: text-to-image grounding for image caption evaluation. In *EMNLP*.
- <span id="page-9-9"></span>Karthikeyan K, Zihan Wang, Stephen Mayhew, and Dan Roth. 2020. Cross-lingual ability of multilingual BERT: An empirical study. In *ICLR*.
- <span id="page-9-19"></span>Hassan Kane, Muhammed Yusuf Kocyigit, Ali Abdalla, Pelkins Ajanoh, and Mohamed Coulibali. 2020. NU-BIA: NeUral based interchangeability assessor for text generation. In *1st Workshop on Evaluating NLG Evaluation*.
- <span id="page-9-3"></span>Mert Kilickaya, Aykut Erdem, Nazli Ikizler-Cinbis, and Erkut Erdem. 2017. Re-evaluating automatic metrics for image captioning. In *EACL*.
- <span id="page-9-25"></span>Hwanhee Lee, Seunghyun Yoon, Franck Dernoncourt, Trung Bui, and Kyomin Jung. 2021. UMIC: an unreferenced metric for image captioning via contrastive learning. In *ACL*.
- <span id="page-9-1"></span>Hwanhee Lee, Seunghyun Yoon, Franck Dernoncourt, Doo Soon Kim, Trung Bui, and Kyomin Jung. 2020. Vilbertscore: Evaluating image caption using visionand-language bert. In *First Workshop on Evaluation and Comparison of NLP Systems*.
- <span id="page-9-11"></span>Kuang-Huei Lee, Xi Chen, Gang Hua, Houdong Hu, and Xiaodong He. 2018. Stacked cross attention for image-text matching. In *ECCV*.
- <span id="page-9-10"></span>Chin-Yew Lin. 2004. Rouge: A package for automatic evaluation of summaries. *Text Summarization Branches Out*.
- <span id="page-9-8"></span>Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Perona, Deva Ramanan, Piotr Dollár, and C Lawrence Zitnick. 2014. Microsoft COCO: Common objects in context. In *ECCV*. Springer.
- <span id="page-9-15"></span>Xihui Liu, Hongsheng Li, Jing Shao, Dapeng Chen, and Xiaogang Wang. 2018. Show, tell and discriminate: Image captioning by self-retrieval with partially labeled data. In *ECCV*.
- <span id="page-9-5"></span>Chi-kiu Lo. 2019. Yisi-a unified semantic mt quality evaluation and estimation metric for languages with different levels of available resources. In *Fourth Conference on Machine Translation*.
- <span id="page-9-16"></span>Annie Louis and Ani Nenkova. 2013. Automatically assessing machine summary content without a gold standard. *Computational Linguistics*, 39(2):267– 300.
- <span id="page-9-12"></span>Jiasen Lu, Dhruv Batra, Devi Parikh, and Stefan Lee. 2019. ViLBERT: Pretraining task-agnostic visiolinguistic representations for vision-and-language tasks. In *NeurIPS*.
- <span id="page-9-13"></span>Jiasen Lu, Vedanuj Goswami, Marcus Rohrbach, Devi Parikh, and Stefan Lee. 2020. 12-in-1: Multi-task vision and language representation learning. In *CVPR*.

- <span id="page-10-26"></span>Grace Luo, Trevor Darrell, and Anna Rohrbach. 2021. NewsCLIPpings: automatic generation of out-of-context multimodal media. *arXiv preprint arXiv:2104.05893*.
- <span id="page-10-7"></span>Ruotian Luo, Brian Price, Scott Cohen, and Gregory Shakhnarovich. 2018. Discriminability objective for training descriptive captions. In *CVPR*.
- <span id="page-10-21"></span>Haley MacLeod, Cynthia L Bennett, Meredith Ringel Morris, and Edward Cutrell. 2017. Understanding blind people's experiences with computer-generated captions of social media images. In *CHI*.
- <span id="page-10-12"></span>Pranava Madhyastha, Josiah Wang, and Lucia Specia. 2019. VIFIDEL: Evaluating the visual fidelity of image descriptions. In *ACL*.
- <span id="page-10-2"></span>Yashar Mehdad, Matteo Negri, and Marcello Federico. 2012. Match without a referee: evaluating mt adequacy without reference translations. In *Seventh Workshop on Statistical Machine Translation*.
- <span id="page-10-11"></span>Shikib Mehri and Maxine Eskenazi. 2020. USR: An unsupervised and reference free evaluation metric for dialog generation. In *ACL*.
- <span id="page-10-27"></span>Margaret Mitchell, Simone Wu, Andrew Zaldivar, Parker Barnes, Lucy Vasserman, Ben Hutchinson, Elena Spitzer, Inioluwa Deborah Raji, and Timnit Gebru. 2019. Model cards for model reporting. In *FAccT*.
- <span id="page-10-17"></span>Aaron van den Oord, Yazhe Li, and Oriol Vinyals. 2018. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*.
- <span id="page-10-5"></span>Kishore Papineni, Salim Roukos, Todd Ward, and Wei-Jing Zhu. 2002. Bleu: a method for automatic evaluation of machine translation. In *ACL*.
- <span id="page-10-23"></span>F. Pedregosa, G. Varoquaux, A. Gramfort, V. Michel, B. Thirion, O. Grisel, M. Blondel, P. Prettenhofer, R. Weiss, V. Dubourg, J. Vanderplas, A. Passos, D. Cournapeau, M. Brucher, M. Perrot, and E. Duchesnay. 2011. Scikit-learn: Machine learning in Python. *JMLR*, 12.
- <span id="page-10-8"></span>Maxime Peyrard and Iryna Gurevych. 2018. Objective function learning to match human judgements for optimization-based summarization. In *NAACL*.
- <span id="page-10-4"></span>Telmo Pires, Eva Schlinger, and Dan Garrette. 2019. How multilingual is multilingual BERT? In *ACL*.
- <span id="page-10-0"></span>Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. 2021. Learning transferable visual models from natural language supervision.
- <span id="page-10-15"></span>Alec Radford, Jeffrey Wu, Rewon Child, David Luan, Dario Amodei, and Ilya Sutskever. 2019. Language models are unsupervised multitask learners. *OpenAI blog*, 1(8):9.

- <span id="page-10-19"></span>Anna Rohrbach, Lisa Anne Hendricks, Kaylee Burns, Trevor Darrell, and Kate Saenko. 2018. Object hallucination in image captioning. In *EMNLP*.
- <span id="page-10-20"></span>Anna Rohrbach, Atousa Torabi, Marcus Rohrbach, Niket Tandon, Christopher Pal, Hugo Larochelle, Aaron Courville, and Bernt Schiele. 2017. Movie description. *IJCV*.
- <span id="page-10-14"></span>Rico Sennrich, Barry Haddow, and Alexandra Birch. 2016. Neural machine translation of rare words with subword units. In *ACL*.
- <span id="page-10-22"></span>Ravi Shekhar, Sandro Pezzelle, Yauhen Klimovich, Aurélie Herbelot, Moin Nabi, Enver Sangineto, and Raffaella Bernardi. 2017. FOIL it! find one mismatch between image and language caption. In *ACL*.
- <span id="page-10-25"></span>Kurt Shuster, Samuel Humeau, Hexiang Hu, Antoine Bordes, and Jason Weston. 2019. Engaging image captioning via personality. In *CVPR*.
- <span id="page-10-16"></span>Kihyuk Sohn. 2016. Improved deep metric learning with multi-class n-pair loss objective. In *NeurIPS*.
- <span id="page-10-1"></span>Lucia Specia, Dhwaj Raj, and Marco Turchi. 2010. Machine translation evaluation versus quality estimation. *Machine translation*, 24(1):39–50.
- <span id="page-10-3"></span>Lucia Specia and Kashif Shah. 2018. Machine translation quality estimation: Applications and future perspectives. In *Translation Quality Assessment*, pages 201–235. Springer.
- <span id="page-10-24"></span>Abigale Stangl, Meredith Ringel Morris, and Danna Gurari. 2020. "person, shoes, tree. is the person naked?" what people with vision impairments want in image descriptions. In *CHI*.
- <span id="page-10-9"></span>Simeng Sun and Ani Nenkova. 2019. The feasibility of embedding based automatic evaluation for single document summarization. In *EMNLP*.
- <span id="page-10-10"></span>Chongyang Tao, Lili Mou, Dongyan Zhao, and Rui Yan. 2018. Ruber: An unsupervised method for automatic evaluation of open-domain dialog systems. In *AAAI*.
- <span id="page-10-13"></span>Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Lukasz Kaiser, and Illia Polosukhin. 2017. Attention is all you need. In *NeurIPS*.
- <span id="page-10-6"></span>Ramakrishna Vedantam, C Lawrence Zitnick, and Devi Parikh. 2015. Cider: Consensus-based image description evaluation. In *CVPR*.
- <span id="page-10-18"></span>Oriol Vinyals, Alexander Toshev, Samy Bengio, and Dumitru Erhan. 2016. Show and tell: Lessons learned from the 2015 mscoco image captioning challenge. *TPAMI*, 39(4):652–663.
- <span id="page-10-28"></span>Sijin Wang, Ziwei Yao, Ruiping Wang, Zhongqin Wu, and Xilin Chen. 2021. FAIEr: Fidelity and adequacy ensured image caption evaluation. In *CVPR*.

- <span id="page-11-2"></span>Shijie Wu and Mark Dredze. 2019. Beto, bentz, becas: The surprising cross-lingual effectiveness of BERT. In *EMNLP*.
- <span id="page-11-0"></span>Elizaveta Yankovskaya, Andre Tättar, and Mark Fishel. 2019. Quality estimation and translation metrics via pre-trained word and sentence embeddings. In *Fourth Conference on Machine Translation*.
- <span id="page-11-3"></span>Yanzhi Yi, Hangyu Deng, and Jinglu Hu. 2020. Improving image captioning evaluation by considering inter references variance. In *ACL*.
- <span id="page-11-5"></span>Peter Young, Alice Lai, Micah Hodosh, and Julia Hockenmaier. 2014. From image descriptions to visual denotations: New similarity metrics for semantic inference over event descriptions. *TACL*, 2:67–78.
- <span id="page-11-4"></span>Tianyi Zhang, Varsha Kishore, Felix Wu, Kilian Q Weinberger, and Yoav Artzi. 2020. BERTScore: Evaluating text generation with BERT. In *ICLR*.
- <span id="page-11-1"></span>Wei Zhao, Goran Glavaš, Maxime Peyrard, Yang Gao, Robert West, and Steffen Eger. 2020. On the limitations of cross-lingual encoders as exposed by reference-free machine translation evaluation. In *ACL*.
- <span id="page-11-6"></span>C Lawrence Zitnick and Devi Parikh. 2013. Bringing semantics into focus using visual abstraction. In *CVPR*.

### <span id="page-12-0"></span>A Evaluation and Replication Details

[Anderson et al.](#page-8-0) [\(2016\)](#page-8-0) introduced a set of corpora, metrics, and experimental settings for comparing image caption generation evaluation metrics. Perhaps unwittingly, their introduced protocols have become the accepted standard for evaluation of new caption generation metrics. However, seemingly innocuous preprocessing+reporting choices can significantly impact correlations with human judgment on these corpora. In what follows, we detail our replication efforts. Our goal was to make the experimental comparisons involving CLIPScore reported in the main paper as fair as possible. We hope it can be useful for researchers reporting metrics on this setup going forward.

#### Flickr8K details

We contacted the authors of some prior work, and did our best to re-create their evaluation settings. We uncovered two types of discrepancies when reporting on this corpus. The first discrepancy is that prior work has mixed evaluating rank correlations with kendall-C and kendall-B. These metrics handle ties differently, and ties are frequent because human Likert judgements are discretized. The second discrepancy is the method of aggregation of human ratings. Three human ratings were gathered for 5664 (image, candidate) pairs. The majority of prior works flatten all human judgments to a single list, and report rank correlation over 5664  $* 3 = 16992$  instances (method A). However, another (possibly more defensible) evaluation choice is to average human ratings for each pair, and report rank correlation instead over 5664 instances (method B). The choice of aggregation method has a significant impact on correlations. For example, when we used aggregation method A and  $\tau_c$  for SPICE, we can exactly replicate the correlation, 44.9, originally reported in [\(Anderson et al.,](#page-8-0) [2016\)](#page-8-0). But, if we use  $\tau_c$  and instead use aggregation method B, the correlation increases to 52.9: this inflation occurs with other metrics, too.

For our results, we do our best to report all results for the most common setting: using  $\tau_c$  correlation, and using aggregation method A. Thus, the results we report may differ slightly than the results reported in prior work.

#### <span id="page-12-1"></span>Composite details

For this corpus too, prior work has mixed evaluating with kendall-C and kendall-B correlations,

<span id="page-12-2"></span>

|         | Original | $\tau_b$ no GT | $\tau_b$ w/ GT | $\tau_c$ no GT | $\tau_c$ w/ GT |
|---------|----------|----------------|----------------|----------------|----------------|
| BLEU-1  | 26       | 29             | 45             | 31             | 49             |
| BLEU-4  | 18       | 31             | 46             | 31             | 50             |
| ROUGE-L | 28       | 30             | 48             | 32             | 49             |
| METEOR  | 35       | 36             | 49             | 39             | 50             |
| CIDEr   | 36       | 35             | 48             | 38             | 52             |
| SPICE   | 39       | 39             | 51             | 40             | 53             |

Table 6: Attempts at replicating [Anderson et al.](#page-8-0) [\(2016\)](#page-8-0)'s results on the composite corpus.

which can have an impact, e.g., for CIDEr in our setting, switching from  $\tau_b$  to  $\tau_c$  results in an increase from 35 to 38 rank correlation. But perhaps the most impactful decision for this corpus relates to the references: each image originally has (roughly) five references. But when gathering human judgments, one of the candidate captions that was rated by humans was sampled from the references. For Flickr8k, [Anderson et al.](#page-8-0) [\(2016\)](#page-8-0) "exclude 158 correct image-caption pairs where the candidate caption appears in the reference set;" this curation choice has become standard for Flickr8k. But for Composite, it's not clear if they repeated this curation choice, or not. And because of this ambiguity, it's not obvious which standard each prior work followed, either. For fair comparison, in an effort to reconstruct [Anderson et al.](#page-8-0) [\(2016\)](#page-8-0), we tried both ways: removing the ground truth candidate reference, and not.

Our efforts to replicate the exact values of [Ander](#page-8-0)[son et al.](#page-8-0) [\(2016\)](#page-8-0) are in Table [6.](#page-12-2) We suspect the discrepancy in BLEU-4 likely results from a smoothing issue related to the application of BLEU-4 to individual captions vs. the whole corpus (as mentioned in [Kane et al.](#page-9-19) [\(2020\)](#page-9-19)). Based on these replication efforts, it's likely that the original evaluations for this corpus were computed using  $\tau_c$  with GT references removed. We agree that the fairest analysis on this corpus should not include a reference that is also a candidate. And while we didn't go through all prior works and recompute their metrics with this change, we did compute ViLBERTScore-F in this setting, because it was, before CLIPScore, the state-of-the-art for this corpus. If it's helpful for future reporting: in the setting where all references (including the GT reference) are used, RefCLIP-S gets  $\tau_c = 60.0$ .

#### MSCOCO system-level details

The MSCOCO 2015 image captioning challenge is a standard corpus for evaluation the system-level correlation between new evaluation metrics and human judgments on the MSCOCO test set. To our knowledge, this evaluation was first conducted by [Anderson et al.](#page-8-0) [\(2016\)](#page-8-0) using a random sample of 1K test set submissions from 15 teams. But because the test set predictions are not public, more recent work (e.g., [Cui et al.](#page-9-18) [\(2018\)](#page-9-18); [Zhang et al.](#page-11-4) [\(2020\)](#page-11-4)) has evaluated using dev set predictions from systems, and assuming dev set results correlate with test set results (12 teams submitted dev predictions). However, there are some potential problems with this setup:

- 1. There's reason to believe that some teams give dev set predictions with different models vs. test set predictions. For example, the dev set predictions are identical between the two submissions: m-RNN and m-RNN (Baidu/ UCLA), but the test set predictions differ (and achieve significantly different scores).
- 2. Correlations are reported over 12 (or possibly only 11, given the duplicate predictions) systems. But spearman/pearson correlation over only 12 observations is unfortunately simple to (accidentally) "game" due to the low statistical power of the comparison (see [Card et al.](#page-14-0) [\(2020\)](#page-14-0) for an overview of statistical power in NLP). Consider a (nonsense) evaluation metric that assigns a random uniform  $[0, 1)$  "score" to systems without examining outputs, and consider applying this metric, e.g.,  $N = 10$  times to the 12 systems and taking the best performing run as the final metric (simulating either a single researcher developing a new evaluation metric and/or the community's collective trials). We ran a simulation of this process 1000 times: the average spearman/pearson correlation between human judgments and our bogus metric were  $r/\rho = .91$ , due to repeated evaluation and low sample size.

Thus, while the intent of this evaluation is understandable, and it may be possible to garner some insight if relatively few evaluations are conducted, this specific setup as a fine-grained comparison between new evaluation metrics for caption generation has likely outlived its utility.

#### Pascal-50S Setup Erratum

In March 2022, Jin-Hwa Kim reported some [small](https://github.com/jmhessel/clipscore/issues/4) [discrepancies](https://github.com/jmhessel/clipscore/issues/4) in a replication effort for the Pascal-50S corpus. Upon further investigation, it was discovered that the original version of this work was using a different set of human judgments

<span id="page-13-1"></span>

|                    | HC   | HI   | HM   | MM   | Mean |
|--------------------|------|------|------|------|------|
| length             | 65.4 | 52.4 | 63.0 | 42.3 | 55.8 |
| BLEU-4             | 52.5 | 90.4 | 84.9 | 55.3 | 70.8 |
| <b>SPICE</b>       | 56.9 | 96.3 | 87.1 | 66.4 | 76.7 |
| <b>METEOR</b>      | 59.0 | 97.7 | 93.9 | 62.0 | 78.2 |
| ROUGE-L            | 55.0 | 95.3 | 93.1 | 58.7 | 75.5 |
| CIDEr              | 53.7 | 98.1 | 90.8 | 63.7 | 76.6 |
| BERT-S (RoBERTa-F) | 54.4 | 96.1 | 94.3 | 56.4 | 75.3 |
| CLIP-S (no refs)   | 60.3 | 99.4 | 97.9 | 77.3 | 83.7 |
| RefCLIP-S          | 57.9 | 99.5 | 96.1 | 80.8 | 83.6 |

Table 7: Pascal50S-11-judgment accuracy results (5 references, non-standard 11 human judgment version).  $HC = two human correct captions; HI = both captions$ are human written, but one is wrong;  $HM = both cap$ tions are for the image, but one is written by a human, one by an algorithm;  $MM =$  both captions are for the image, and both are written by an algorithm. We average our results over 5 random samples (but CLIP-S doesn't change because it doesn't use references).

than the usual setup. In particular, the [Pascal-](http://vrama91.github.io/cider/)[50S corpus](http://vrama91.github.io/cider/) contains two types of human judgments: 11 human judgments per pair (located in a file named pair\_pascal.mat); and 48 human judgments per pair (located in a file named consensus pascal.mat). The 48 judgments are intended to be used, and the results in the main paper have been updated accordingly. For reproducability sake, in case future work utilizes the 11 judgments, we have included those results in Table [7.](#page-13-1)

#### <span id="page-13-0"></span>B Rescaling **CLIPScore**

For readability purposes, as in [Zhang et al.](#page-11-4) [\(2020\)](#page-11-4), we sought to re-scale the raw cosine similarities computed by CLIP ViT-B/32. While such a monotonic rescaling operation doesn't affect ranking results, for reporting purposes, it can be easier to compare raw values if they are on a scale more closely-aligned with other evaluation metrics (e.g., from roughly zero to roughly one). Figure [4](#page-14-1) shows the raw candidate-reference and candidateimage cosine similarities for four corpora. (Many "reference"-candidate similarities for the Twitter corpus are 1.0 because users frequently use the text of their tweet as the AltText.) Across all of these cases, we never observed a negative negative cosine similarity. But, to be safe, we take a maximum between the cosine similarity and zero because the harmonic mean used to compute RefCLIPScore would be undefined for negative values. Multi-

<span id="page-14-1"></span>Image /page/14/Figure/0 description: This figure displays four histograms, each labeled with a dataset name: (a) Flickr8K, (b) Composite, (c) Pascal50S, and (d) Twitter AltText. Each histogram plots the distribution of cosine similarities on the x-axis, ranging from 0 to 1. Two distributions are shown in each histogram, represented by orange bars labeled 'cos(c, r)' and purple bars labeled 'cos(c, v)'. In all four datasets, the 'cos(c, r)' distribution is shifted towards higher cosine similarity values compared to the 'cos(c, v)' distribution, indicating a stronger similarity between 'c' and 'r' than between 'c' and 'v'.

Figure 4: Distributions of raw cosine similarities between candidate and references and candidate and visual content from CLIP ViT-B/32.

plying by 2.5 has the effect of "stretching" the CLIPScore distribution to more uniformly span between zero and one, though, CLIPScore can be greater than 1. Furthermore, when computing RefCLIPScore, we maintain this weighting, because it has the effect of mapping the visual-textual cosine similarity distribution to more closely match the reference-candidate distribution: this provides a roughly equal importance weighting between the image-candidate and reference-candidate similarity factors.

We note that the exact parameters of our rescaling method only apply to CLIP  $\forall$  iT-B/32. If future, bigger models are released, e.g., the presently unreleased  $ViT-L/14$  CLIP variant, they could exhibit a different cosine similarity distribution.

### **References**

- Peter Anderson, Basura Fernando, Mark Johnson, and Stephen Gould. 2016. Spice: Semantic propositional image caption evaluation. In *ECCV*. Springer.
- <span id="page-14-0"></span>Dallas Card, Peter Henderson, Urvashi Khandelwal, Robin Jia, Kyle Mahowald, and Dan Jurafsky. 2020. With little power comes great responsibility. In *EMNLP*.
- Yin Cui, Guandao Yang, Andreas Veit, Xun Huang, and Serge Belongie. 2018. Learning to evaluate image captioning. In *CVPR*.
- Hassan Kane, Muhammed Yusuf Kocyigit, Ali Abdalla, Pelkins Ajanoh, and Mohamed Coulibali. 2020. NU-

BIA: NeUral based interchangeability assessor for text generation. In *1st Workshop on Evaluating NLG Evaluation*.

Tianyi Zhang, Varsha Kishore, Felix Wu, Kilian Q Weinberger, and Yoav Artzi. 2020. BERTScore: Evaluating text generation with BERT. In *ICLR*.