We can consider the test to be equivalent to specifying a region of the simplex of types which corresponds to choosing hypothesis 1. The optimum region is of the form  $(12.194)$ , for which the boundary of the region is the set of types for which the difference between the distances is a constant. This boundary is the analog of the perpendicular bisector in Euclidean geometry. The test is illustrated in Figure 12.8.

We now offer some informal arguments based on <PERSON><PERSON>'s theorem to show how to choose the threshold to obtain different probabilities of error. Let  $B$  denote the set on which hypothesis 1 is accepted. The probability of error of the first hind is

$$
\alpha_n = P_1^n(P_{X^n} \in B^c) \tag{12.195}
$$

Since the set  $B<sup>c</sup>$  is convex, we can use <PERSON><PERSON>'s theorem to show that the probability of error is essentially determined by the relative entropy of the closest member of  $B^c$  to  $P_1$ . Therefore,

$$
\alpha_n = 2^{-nD(P_1^*||P_1)},\tag{12.196}
$$

where  $P_1^*$  is the closest element of  $B^c$  to distribution  $P_1$ . Similarly,

$$
\beta_n \doteq 2^{-nD(P_2^* \| P_2)}, \qquad (12.197)
$$

where  $P_2^*$  is the closest element in B to the distribution  $P_2$ .

Now minimizing  $D(P||P_2)$  subject to the constraint  $D(P||P_2)$  - $D(P||P_1) \geq \frac{2}{\pi}$  log T will yield the type in B that is closest to  $P_2$ . Setting up the minimization of  $D(P||P_2)$  subject to  $D(P||P_2) - D(P||P_1) = \frac{1}{n} \log T$ using Lagrange multipliers, we have

$$
J(P) = \sum P(x) \log \frac{P(x)}{P_2(x)} + \lambda \sum P(x) \log \frac{P_1(x)}{P_2(x)} + \nu \sum P(x). \quad (12.198)
$$

Image /page/0/Figure/11 description: The image shows a triangular region with two curves originating from points P1 and P2, respectively. The curves intersect at a point labeled P lambda. A line segment is drawn, and an equation is written below it: D(P||P1) - D(P||P2) = 1/n log T.

Figure 12.8. The likelihood ratio test on the probability simplex.

Differentiating with respect to  $P(x)$  and setting to 0, we have

$$
\log \frac{P(x)}{P_2(x)} + 1 + \lambda \log \frac{P_1(x)}{P_2(x)} + \nu = 0.
$$
 (12.199)

Solving this set of equations, we obtain the minimizing  $P$  of the form

$$
P_2^* = P_{\lambda^*} = \frac{P_1^{\lambda}(x)P_2^{1-\lambda}(x)}{\sum_{a \in \mathcal{X}} P_1^{\lambda}(a)P_2^{1-\lambda}(a)},
$$
(12.200)

where  $\lambda$  is chosen so that  $D(P_{\lambda^*}||P_1) - D(P_{\lambda^*}||P_2) = \frac{\log T}{n}$ .

From the symmetry of expression (12.200), it is clear that  $P_1^* = P_2^*$ and that the probabilities of error behave exponentially with exponents given by the relative entropies  $D(P^*||P_1)$  and  $D(P^*||P_2)$ . Also note from the equation that as  $\lambda \to 1$ ,  $P_{\lambda} \to P_1$  and as  $\lambda \to 0$ ,  $P_{\lambda} \to P_2$ . The line that  $P_{\lambda}$  traces out as  $\lambda$  varies is a geodesic in the simplex. Here  $P_{\lambda}$  is a normalized convex combination, where the combination is in the exponent (Figure 12.8).

In the next section, we calculate the best error exponent when one of the two types of error goes to zero arbitrarily slowly (Stein's lemma). We will also minimize the weighted sum of the two probabilities of error and obtain the Chernoff bound.

## 12.8 STEIN'S LEMMA

We now consider the case when one of the probabilities of error is fixed and we wish to minimize the other probability of error. The best error exponent in this case is given by Stein's lemma.

**Theorem 12.8.1** (Stein's lemma): Let  $X_1, X_2, \ldots, X_n$  be i.i.d.  $\sim Q$ . Consider the hypothesis test between two alternatives,  $\ddot{Q} = P_1$  and  $\dot{Q} = P_2$ , where  $D(P_1||P_2) < \infty$ . Let  $A_n \subseteq \mathcal{X}^n$  be an acceptance region for hypothesis 1. Let the probabilities of error be

$$
\alpha_n = P_1^n(A_n^c), \qquad \beta_n = P_2^n(A_n). \tag{12.201}
$$

and for  $0 < \epsilon < \frac{1}{2}$ , define

$$
\beta_n^{\epsilon} = \min_{\substack{A_n \subseteq \mathcal{X}^n \\ a_n < \epsilon}} \beta_n \,. \tag{12.202}
$$

Then

$$
\lim_{\epsilon \to 0} \lim_{n \to \infty} \frac{1}{n} \log \beta_n^{\epsilon} = -D(P_1 \| P_2). \tag{12.203}
$$

Proof: To prove the theorem, we construct a sequence of acceptance regions  $A_{n} \subset \mathcal{X}^{n}$  such that  $\alpha_{n} < \epsilon$  and  $\beta_{n} = 2^{-nD(r_{1} + r_{2})}$ . We then show that no other sequence of tests has an asymptotically better exponent.

First, we define

$$
A_n = \left\{ \mathbf{x} \in \mathcal{X}^n : 2^{+n(D(P_1 \| P_2) - \delta)} \le \frac{P_1(\mathbf{x})}{P_2(\mathbf{x})} \le 2^{+n(D(P_1 \| P_2) + \delta)} \right\}.
$$
 (12.204)

Then we have the following properties:

1.  $P_1^n(A_n) \rightarrow 1$ . This follows from

$$
P_1^n(A_n) = P_1^n\left(\frac{1}{n}\sum_{i=1}^n \log \frac{P_1(X_i)}{P_2(X_i)} \in (D(P_1 \| P_2) - \delta, D(P_1 \| P_2) + \delta)\right)
$$
(12.205)

$$
\rightarrow 1 \tag{12.206}
$$

by the strong law of large numbers, since  $D(P_1||P_2) =$  $E\left(\log \frac{P_1(X)}{X}\right)$ . Hence for sufficiently large n,  $\alpha \leq \epsilon$ .  $2. p_{n(A)} \rightarrow 2^{-n(D(P_1||P_2)-\delta)}$  Using the definition of A,, we have

$$
P_2^n(A_n) = \sum_{A_n} P_2(\mathbf{x})
$$
 (12.207)

$$
\leq \sum_{A_n} P_1(\mathbf{x}) 2^{-n(D(P_1||P_2) - \delta)} \tag{12.208}
$$

$$
=2^{-n(D(P_1||P_2)-\delta)}\sum_{A_n}P_1(\mathbf{x})
$$
 (12.209)

$$
=2^{-n(D(P_1||P_2)-\delta)}(1-\alpha_n). \hspace{1cm} (12.210)
$$

Similarly, we can show that

$$
P_2^n(A_n) \ge 2^{-n(D(P_1||P_2)+\delta)}(1-\alpha_n) \,. \tag{12.211}
$$

Hence

$$
\frac{1}{n}\log \beta_n \le -D(P_1 \| P_2) + \delta + \frac{\log(1-\alpha_n)}{n}, \qquad (12.212)
$$

and

$$
\frac{1}{n}\log \beta_n \ge -D(P_1 \| P_2) - \delta + \frac{\log(1 - \alpha_n)}{n}, \qquad (12.213)
$$

Hence

$$
\lim_{n \to \infty} \frac{1}{n} \log \beta_n = -D(P_1 \| P_2). \tag{12.214}
$$

3. We now prove that no other sequence of acceptance regions does better. Let  $B_n \subseteq \mathcal{X}^n$  be any other sequence of acceptance region with  $\alpha_{n,R} = P_1^n(B_n^c) < \epsilon$ . Let  $\beta_{n,R} = P_2^n(B_n)$ . We will show that  $\beta_{n,B_{n}} \geq 2^{-n(D(P_1||P_2)-\epsilon)}$ .

Here

$$
\beta_{n, B_n} = P_2^n(B_n) \ge P_2^n(A_n \cap B_n) \tag{12.215}
$$

$$
=\sum_{A_n\cap B_n} P_2(\mathbf{x})\tag{12.216}
$$

$$
\geq \sum_{A_n \cap B_n} P_1(\mathbf{x}) 2^{-n(D(P_1 \| P_2) + \delta)} \tag{12.217}
$$

$$
=2^{-n(D(P_1||P_2)+\delta)}\sum_{A_n\cap B_n}P_1(\mathbf{x})
$$
 (12.218)

$$
\geq (1 - \alpha_n - \alpha_{n,B_n}) 2^{-n(D(P_1||P_2) + \delta)}, \quad (12.219)
$$

where the last inequality follows from the union of events bound as follows:

$$
\sum_{A_n \cap B_n} P_1(\mathbf{x}) = P_1(A_n \cap B_n)
$$
\n(12.220)

$$
= 1 - P_1(A_n^c \cup B_n^c) \tag{12.221}
$$

$$
\geq 1 - P_1(A_n^c) - P_1(B_n^c) \tag{12.222}
$$

$$
= 1 - \alpha_n - \alpha_{n, B_n} \,. \tag{12.223}
$$

Hence

$$
\frac{1}{n}\log \beta_{n, B_n} \ge -D(P_1 \| P_2) - \delta - \frac{\log(1 - \alpha_n - \alpha_{n, B_n})}{n}, \qquad (12.224)
$$

and since  $\delta > 0$  is arbitrary,

$$
\lim_{n \to \infty} \frac{1}{n} \log \beta_{n, B_n} \ge -D(P_1 \| P_2). \tag{12.225}
$$

Thus no sequence of sets  $B_n$  has an exponent better than  $D(P_1||P_2)$ . But the sequence  $A_n$  achieves the exponent  $D(P_1||P_2)$ . Thus  $A_n$  is asymptotically optimal, and the best error exponent is  $D(P_1||P_2)$ .  $\Box$ 

## 12.9 CHERNOFF BOUND

We have considered the problem of hypothesis testing in the classical setting, in which we treat the two probabilities of error separately. In the derivation of Stein's lemma, we set  $\alpha_n \leq \epsilon$  and achieved  $\beta_n = 2^{-n}$ . But this approach lacks symmetry. Instead, we can follow a Bayesian approach, in which we assign prior probabilities to both the hypotheses. In this case, we wish to minimize the overall probability of error given by the weighted sum of the individual probabilities of error. The resulting error exponent is the Chernoff information.

The setup is as follows:  $X_1, X_2, \ldots, X_n$  i.i.d.  $\sim Q$ . We have two hypotheses:  $Q = P_1$  with prior probability  $\pi_1$  and  $Q = P_2$  with prior probability  $\pi_2$ . The overall probability of error is

$$
P_e^{(n)} = \pi_1 \alpha_n + \pi_2 \beta_n \,. \tag{12.226}
$$

Let

$$
D^* = \lim_{n \to \infty} \min_{A_n \subseteq \mathcal{X}^n} -\frac{1}{n} \log P_e^{(n)}.
$$
 (12.227)

**Theorem 12.9.1** (Chernoff): The best achievable exponent in the Bayesian probability of error is  $D^*$ , where

$$
D^* = D(P_{\lambda^*} || P_1) = D(P_{\lambda^*} || P_2), \qquad (12.228)
$$

with

$$
P_{\lambda} = \frac{P_1^{\lambda}(x)P_2^{1-\lambda}(x)}{\sum_{a \in \mathcal{X}} P_1^{\lambda}(a)P_2^{1-\lambda}(a)}
$$
(12.229)

and  $\lambda^*$  the value of  $\lambda$  such that

$$
D(P_{\lambda^*}||P_1) = D(P_{\lambda^*}||P_2).
$$
 (12.230)

Proof: The basic details of the proof were given in the previous section. The basic details of the proof were given in the previous section. We have shown that the optimum test is a likelihood ratio test, which can be considered to be of the form

$$
D(P_{X^n} || P_2) - D(P_{X^n} || P_1) > T.
$$
 (12.231)

The test divides the probability simplex into regions corresponding to hypothesis 1 and hypothesis 2, respectively. The test divides the probability simplex into regions corresponding hypothesis 1 and hypothesis 2, respectively. This is illustrated in Figure 12.9.

Let A be the set of types associated with hypothesis 1. From the

Image /page/5/Figure/1 description: A triangular diagram with two lines dividing it into three regions. A curved line connects points P1 and P2, passing through point Pλ. P1 is in the top region, Pλ is on the dividing line, and P2 is in the bottom region.

Figure 12.9. The probability simplex and Chernoff's bound.

discussion preceding (12.200), it follows that the closest point in the set  $A<sup>c</sup>$  to  $P<sub>1</sub>$  is on the boundary of A, and is of the form given by (12.229). Then from the discussion in the last section, it is clear that  $P_{\lambda}$  is the distribution in A that is closest to  $P_2$ ; it is also the distribution in  $A^c$  that is closest to  $P_1$ . By Sanov's theorem, we can calculate the associated probabilities of error

$$
\alpha_n = P_1^n(A^c) = 2^{-nD(P_{\lambda^*}||P_1)} \tag{12.232}
$$

and

$$
\beta_n = P_2^n(A) = 2^{-nD(P_{\lambda^*}||P_2)}.
$$
 (12.233)

In the Bayesian case, the overall probability of error is the weighted sum of the two probabilities of error,

$$
P_e = \pi_1 2^{-nD(P_\lambda||P_1)} + \pi_2 2^{-nD(P_\lambda||P_2)} = 2^{-n \min\{D(P_\lambda||P_1), D(P_\lambda||P_2)\}}, \quad (12.234)
$$

since the exponential rate is determined by the worst exponent. Since  $D(P_{\lambda}||P_1)$  increases with  $\lambda$  and  $D(P_{\lambda}||P_2)$  decreases with  $\lambda$ , the maximum value of the minimum of  $\{D(P_{\lambda}||P_{\lambda}), D(P_{\lambda}||P_{\lambda})\}$  is attained when they are equal. This is illustrated in Figure 12.10.

Hence, we choose  $\lambda$  so that

$$
D(P_{\lambda}||P_1) = D(P_{\lambda}||P_2) \stackrel{\triangle}{=} C(P_1, P_2).
$$
 (12.235)

Thus  $C(P_1, P_2)$  is the highest achievable exponent for the probability of error and is called the Chernoff information.  $\Box$ 

The above definition is equivalent to the standard definition of *Chernoff* information,

Image /page/6/Figure/1 description: The image is a graph showing two curves plotted against the variable lambda on the x-axis, ranging from 0 to 1. The y-axis is labeled "Relative entropy" and ranges from 0 to 2.5. The top curve, labeled "D(P\_lambda || P\_1)", is a solid line that starts at approximately 2.25 at lambda=0 and decreases to approximately 0 at lambda=1. The bottom curve, labeled "D(P\_lambda || P\_2)", is a dashed line that starts at approximately 0 at lambda=0 and increases to approximately 1.5 at lambda=1. The two curves intersect at approximately lambda=0.45, where the relative entropy for both is approximately 0.5. A vertical line is drawn at lambda\* = 0.45, indicating the intersection point.

Figure 12.10. Relative entropy  $D(P_{\lambda}||P_1)$  and  $D(P_{\lambda}||P_2)$  as a function of  $\lambda$ .

$$
C(P_1, P_2) = -\min_{0 \le \lambda \le 1} \log \left( \sum_{x} P_1^{\lambda}(x) P_2^{1-\lambda}(x) \right). \tag{12.236}
$$

It is left as an exercise to the reader to show (algebraically) the equivalence of (12.235) and (12.236). We will briefly outline the usual derivation of the Chernoff bound. The maximum a posteriori probability decision rule minimizes the Bayesian probability of error. The decision region  $A$  for hypothesis 1 for the maximum  $a$  posteriori rule is

$$
A = \left\{ \mathbf{x} : \frac{\pi_1 P_1(\mathbf{x})}{\pi_2 P_2(\mathbf{x})} > 1 \right\},\tag{12.237}
$$

the set of outcomes where the  $\alpha$  posteriori probability of hypothesis 1 is greater than the a posteriori probability of hypothesis 2. The probability of error for this rule is

$$
P_e = \pi_1 \alpha_n + \pi_2 \beta_n \tag{12.238}
$$

$$
= \sum_{A^c} \pi_1 P_1 + \sum_A \pi_2 P_2 \qquad (12.239)
$$

$$
= \sum \min\{\pi_1 P_1, \, \pi_2 P_2\} \,. \tag{12.240}
$$

Now for any two positive numbers  $a$  and  $b$ , we have

$$
\min\{a,b\} \le a^{\lambda}b^{1-\lambda}, \quad \text{for all } 0 \le \lambda \le 1. \tag{12.241}
$$

Using this to continue the chain, we have

$$
P_e = \sum \min\{\pi_1 P_1, \pi_2 P_2\} \tag{12.242}
$$

$$
\leq \sum (\pi_1 P_1)^{\lambda} (\pi_2 P_2)^{1-\lambda} \tag{12.243}
$$

$$
\leq \sum P_1^{\lambda} P_2^{1-\lambda} \ . \tag{12.244}
$$

For a sequence of i.i.d. observations,  $P_k(\mathbf{x}) = \prod_{i=1}^n P_k(x_i)$ , and

$$
P_e^{(n)} \le \sum \pi_1^{\lambda} \pi_2^{1-\lambda} \prod_i P_1^{\lambda}(x_i) P_2^{1-\lambda}(x_i)
$$
 (12.245)

$$
= \pi_1^{\lambda} \pi_2^{1-\lambda} \prod_i \sum P_1^{\lambda}(x_i) P_2^{1-\lambda}(x_i)
$$
 (12.246)

$$
\stackrel{(a)}{\leq} \prod_i \sum P_1^{\lambda} P_2^{1-\lambda} \tag{12.247}
$$

$$
=\left(\sum P_1^{\lambda}P_2^{1-\lambda}\right)^n,\tag{12.248}
$$

where (a) follows since  $\pi_1 \leq 1$ ,  $\pi_2 \leq 1$ . Hence, we have

$$
\frac{1}{n}\log P_e^{(n)} \le \log \sum P_1^{\lambda}(x) P_2^{1-\lambda}(x) \tag{12.249}
$$

Since this is true for all  $\lambda$ , we can take the minimum over  $0 \leq \lambda \leq 1$ , resulting in the Chernoff bound. This proves that the exponent is no better than  $C(P_1, P_2)$ . Achievability follows from Theorem 12.9.1.

Note that the Bayesian error exponent does not depend on the actual value of  $\pi_1$  and  $\pi_2$ , as long as they are non-zero. Essentially, the effect of the prior is washed out for large sample sizes. The optimum decision rule is to choose the hypothesis with the maximum a posteriori probability, which corresponds to the test

$$
\frac{\pi_1 P_1(X_1, X_2, \dots, X_n)}{\pi_2 P_2(X_1, X_2, \dots, X_n)} \ge 1.
$$
\n(12.250)

Taking the log and dividing by  $n$ , this test can be rewritten as

$$
\frac{1}{n}\log\frac{\pi_1}{\pi_2} + \frac{1}{n}\sum_i\log\frac{P_1(X_i)}{P_2(X_i)} \le 0\,,\tag{12.251}
$$

where the second term tends to  $D(P_1||P_2)$  or  $-D(P_2||P_1)$  accordingly as  $P_1$  or  $P_2$  is the true distribution. The first term tends to 0, and the effect of the prior distribution washes out.

Finally, to round off our discussion of large deviation theory and hypothesis testing, we consider an example of the conditional limit theorem.

**Example 12.9.1:** Suppose major league baseball players have a batting average of 260 with a standard deviation of 15 and suppose that minor league ballplayers have a batting average of 240 with a standard deviation of 15. A group of 100 ballplayers from one of the leagues (the league is chosen at random) is found to have a group batting average greater than 250, and is therefore judged to be major leaguers. We are now told that we are mistaken; these players are minor leaguers. What can we say about the distribution of batting averages among these 100 players? It will turn out that the distribution of batting averages among these players will have a mean of 250 and a standard deviation of 15. This follows from the conditional limit theorem. To see this, we abstract the problem as follows.

Let us consider an example of testing between two Gaussian distributions,  $f_1 = \mathcal{N}(1, \sigma^2)$  and  $f_2 = \mathcal{N}(-1, \sigma^2)$ , with different means and the same variance. As discussed in the last section, the likelihood ratio test in this case is equivalent to comparing the sample mean with a threshold. The Bayes test is "Accept the hypothesis  $f = f_1$  if  $\frac{1}{n} \sum_{i=1}^n X_i$  $0.$ "

Now assume that we make an error of the first kind (we say  $f = f_1$ ) when indeed  $f = f<sub>2</sub>$ ) in this test. What is the conditional distribution of the samples given that we have made an error?

We might guess at various possibilities:

- If The sample will look like a  $(\frac{1}{2}, \frac{1}{2})$  mix of the two normal distributions. Plausible as this is, it is incorrect.
- $\cdot$   $X_i \approx 0$  for all *i*. This is quite clearly very very unlikely, although it is conditionally likely that  $\overline{X}_n$  is close to 0.
- l The correct answer is given by the conditional limit theorem. If the true distribution is  $f_2$  and the sample type is in the set A, the conditional distribution is close to  $f^*$ , the distribution in A that is closest to  $f_2$ . By symmetry, this corresponds to  $\lambda = \frac{1}{2}$  in (12.229). Calculating the distribution, we get

$$
f^{*}(x) = \frac{\left(\frac{1}{\sqrt{2\pi\sigma^{2}}} e^{-\frac{(x-1)^{2}}{2\sigma^{2}}}\right)^{1/2} \left(\frac{1}{\sqrt{2\pi\sigma^{2}}} e^{-\frac{(x+1)^{2}}{2\sigma^{2}}}\right)^{1/2}}{\int \left(\frac{1}{\sqrt{2\pi\sigma^{2}}} e^{-\frac{(x-1)^{2}}{2\sigma^{2}}}\right)^{1/2} \left(\frac{1}{\sqrt{2\pi\sigma^{2}}} e^{-\frac{(x+1)^{2}}{2\sigma^{2}}}\right)^{1/2} dx} (12.252)
$$

$$
= \frac{\frac{1}{\sqrt{2\pi\sigma^{2}}} e^{-\frac{(x^{2}+1)}{2\sigma^{2}}}}{\int \frac{1}{\sqrt{2\pi\sigma^{2}}} e^{-\frac{(x^{2}+1)}{2\sigma^{2}}} dx} (12.253)
$$

$$
=\frac{1}{\sqrt{2\pi\sigma^2}}\,e^{-\frac{x^2}{2\sigma^2}}\tag{12.254}
$$

$$
=\mathcal{N}(0,\sigma^2). \tag{12.255}
$$

It is interesting to note that the conditional distribution is normal with mean 0 and with the same variance as the original distributions. This is strange but true; if we mistake a normal population for another, the "shape" of this population still looks normal with the same variance and a different mean. Apparently, this rare event does not result from bizarre looking data.

**Example 12.9.2** (Large deviation theory and football): Consider a very simple version of football in which the score is directly related to the number of yards gained. Assume that the coach has a choice between two strategies: running or passing. Associated with each strategy is a distribution on the number of yards gained. For example, in general, running results in a gain of a few yards with very high probability, while passing results in huge gains with low probability. Examples of the distributions are illustrated in Figure 12.11.

At the beginning of the game, the coach uses the strategy that promises the greatest expected gain. Now assume that we are in the closing minutes of the game and one of the teams is leading by a large margin. (Let us ignore first downs and adaptable defenses.) So the trailing team will win only if it is very lucky. If luck is required to win, then we might as well assume that we will be lucky and play accordingly. What is the appropriate strategy?

Assume that the team has only  $n$  plays left and it must gain  $l$  yards, where  $l$  is much larger than  $n$  times the expected gain under each play. The probability that the team succeeds in achieving  $l$  vards is exponentially small; hence, we can use the large deviation results and Sanov's theorem to calculate the probability of this event.

Image /page/9/Figure/7 description: The image displays two probability density graphs side-by-side. The left graph is labeled "Yards gained in pass" on the x-axis and "Probability density" on the y-axis. It shows a bimodal distribution with a peak near zero yards and another smaller peak at a positive yardage. The right graph is labeled "Yards gained in run" on the x-axis and "Probability density" on the y-axis. It shows a unimodal distribution that peaks near zero yards and decreases as yardage increases.

Figure 12.11. Distribution of yards gained in a run or a pass play.

To be precise, we wish to calculate the probability that  $\sum_{i=1}^{n} Z_i \geq n\alpha$ , where  $Z_i$  are independent random variables, and  $Z_i$  has a distribution corresponding to the strategy chosen.

The situation is illustrated in Figure 12.12. Let  $E$  be the set of types corresponding to the constraint,

$$
E = \left\{ P : \sum_{a \in \mathscr{X}} P(a) a \ge \alpha \right\}.
$$
 (12.256)

If  $P_1$  is the distribution corresponding to passing all the time, then the probability of winning is the probability that the sample type is in  $E$ , which by Sanov's theorem is  $2^{-nD(P_1||P_1)}$ , where  $P_1^*$  is the distribution in E that is closest to  $P_1$ . Similarly, if the coach uses the running game all the time, the probability of winning is  $2^{n}$   $\frac{2^{n+2}}{n+2}$ . What if he uses a mixture of strategies? Is it possible that  $2^{-nD(P_{\lambda}^*||P_{\lambda})}$ , the probability of winning with a mixed strategy,  $P_{\lambda} = \lambda P_1 + (1 - \lambda)P_2$ , is better than the probability of winning with either pure passing or pure running?

The somewhat surprising answer is yes, as can be shown by example. This provides a reason to use a mixed strategy other than the fact that it confuses the defense.

We end this section with another inequality due to Chernoff, which is a special version of Markov's inequality. This inequality is called the Chernoff bound.

**Lemma 12.9.1:** Let Y be any random variable and let  $\psi(s)$  be the moment generating function of Y,

$$
\psi(s) = E e^{sY} \tag{12.257}
$$

Then for all  $s \geq 0$ ,

Image /page/10/Figure/10 description: A diagram shows a large triangle with a line segment dividing it into two regions. The upper region is labeled with the letter E. A curved line segment connects two points labeled P1 and P2 within the lower region of the triangle.

Figure 12.12. Probability simplex for a football game.

$$
\Pr(Y \ge a) \le e^{-sa} \psi(s) \,. \tag{12.258}
$$

**Proof:** Apply Markov's inequality to the non-negative random variant able  $e^{\lambda t}$ .  $\Box$ 

## 12.10 LEMPEL-ZIV CODING

We now describe a scheme for universal data compression due to Ziv and Lempel [291], which is simple to implement and has an asymptotic rate approaching the entropy of the source. The algorithm is particularly simple and has become popular as the standard algorithm for file compression on computers because of its speed and efficiency.

We will consider a binary source throughout this section. The results generalize easily to any finite alphabet.

Algorithm: The source sequence is sequentially parsed into strings that have not appeared so far. For example, if the string is 1011010100010..., we parse it as 1,0,11,01,010,00,10,.... After every comma, we look along the input sequence until we come to the shortest string that has not been marked off before. Since this is the shortest such string, all its prefixes must have occurred earlier. In particular, the string consisting of all but the last bit of this string must have occurred earlier. We code this phrase by giving the location of the prefix and the value of the last bit.

Let  $c(n)$  be the number of phrases in the parsing of the input *n*-sequence. We need log  $c(n)$  bits to describe the location of the prefix to the phrase and 1 bit to describe the last bit. For example, the code for the above sequence is  $(000,1)(000,0)(001,1)(010,1)(100,0)(010,0)(001,0),$ where the first number of each pair gives the index of the prefix and the second number gives the last bit of the phrase. Decoding the coded sequence is straightforward and we can recover the source sequence without error.

The above algorithm requires two passes over the string-in the first pass, we parse the string and calculate  $c(n)$ , the number of phrases in the parsed string. We then use that to decide how many bits (log  $c(n)$ ) to allot to the pointers in the algorithm. In the second pass, we calculate the pointers and produce the coded string as indicated above. The algorithm described above allots an equal number of bits to all the pointers. This is not necessary, since the range of the pointers is smaller at the initial portion of the string. The algorithm can be modified so that it requires only one pass over the string and uses fewer bits for the initial pointers. These modifications do not affect the asymptotic efficiency of the algorithm. Some of the implementation details are discussed by Welch [269] and Bell, Cleary and Witten [22].

In the example, we have not compressed the string; instead, we have expanded the number of bits by more than a factor of 2. But for long strings the phrases will get longer, and describing the phrases by describing the location of the prefix will be more efficient. We will show that this algorithm asymptotically achieves the entropy rate for the unknown ergodic source.

Without loss of generality, we will assume that the source alphabet is binary. Thus  $\mathcal{X} = \{0, 1\}$  throughout this section. We first define a parsing of the string to be a decomposition into phrases.

**Definition:** A parsing S of a binary string  $x_1x_2 \ldots x_n$  is a division of the string into phrases, separated by commas. A *distinct parsing* is a parsing such that no two phrases are identical.

For example,  $0.111.1$  is a distinct parsing of  $01111$ , but  $0.11.11$  is a parsing which is not distinct.

The Lempel-Ziv algorithm described above gives a distinct parsing of the source sequence. Let  $c(n)$  denote the number of phrases in the Lempel-Ziv parsing of a sequence of length n. Of course,  $c(n)$  depends on the sequence  $X<sup>n</sup>$ . The compressed sequence (after applying the Lempel-Ziv algorithm) consists of a list of  $c(n)$  pairs of numbers, each pair consisting of a pointer to the previous occurrence of the prefix of the phrase and the last bit of the phrase. Each pointer requires  $log c(n)$  bits, and hence the total length of the compressed sequence is  $c(n)(\log c(n) +$ 1) bits. We will now show that  $\frac{c^{(n)\text{log }c(n) + 1)}}{n} \to H(\mathcal{X})$  for a stationary ergodic sequence  $X_1, X_2, \ldots, X_n$ . Our proof is based on the simple proof of asymptotic optimality of Lempel-Ziv coding due to Wyner and Ziv [2851.

We first prove a few lemmas that we need for the proof of the theorem. The first is a bound on the number of phrases possible in a distinct parsing of a binary sequence of length  $n$ .

**Lemma 12.10.1** (Lempel and Ziv): The number of phrases  $c(n)$  in a distinct parsing of a binary sequence  $X_1, X_2, \ldots, X_n$  satisfies

$$
c(n) \le \frac{n}{(1 - \epsilon_n) \log n} \tag{12.259}
$$

where  $\epsilon_n \to 0$  as  $n \to \infty$ .

Proof: Let

$$
n_k = \sum_{j=1}^{k} j2^j = (k-1)2^{k+1} + 2
$$
 (12.260)

be the sum of the lengths of all distinct strings of length less than or equal to  $k$ . The number of phrases  $c$  in a distinct parsing of a sequence of length  $n$  is maximized when all the phrases are as short as possible. If  $n = n_k$ , this occurs when all the phrases are of length  $\leq k$ , and thus

$$
c(n_k) \le \sum_{j=1}^{k} 2^j = 2^{k+1} - 2 < 2^{k+1} \le \frac{n_k}{k-1} \tag{12.261}
$$

If  $n_k \leq n \leq n_{k+1}$ , we write  $n = n_k + \Delta$ , where  $\Delta < (k+1)2^{k+1}$ . Then the parsing into shortest phrases has each of the phrases of length  $\leq k$  and  $\Delta/(k + 1)$  phrases of length  $k + 1$ . Thus

$$
c(n) \leq \frac{n_k}{k-1} + \frac{\Delta}{k+1} \leq \frac{n_k + \Delta}{k-1} = \frac{n}{k-1} \ . \tag{12.262}
$$

We now bound the size of k for a given n. Let  $n_k \le n < n_{k+1}$ . Then

$$
n \ge n_k = (k-1)2^{k+1} + 2 \ge 2^k \,, \tag{12.263}
$$

and therefore

$$
k \leq \log n \tag{12.264}
$$

Moreover,

$$
n \le n_{k+1} = k2^{k+2} + 2 \le (k+2)2^{k+2} \le (\log n + 2)2^{k+2} \quad (12.265)
$$

by (12.264), and therefore

$$
k+2 \ge \log \frac{n}{\log n+2}, \qquad (12.266)
$$

or, for all  $n \geq 4$ ,

$$
k-1 \ge \log n - \log(\log n + 2) - 3 \tag{12.267}
$$

$$
= \left(1 - \frac{\log(\log n + 2) + 3}{\log n}\right) \log n \tag{12.268}
$$

$$
\geq \left(1 - \frac{\log(2\log n) + 3}{\log n}\right) \log n \tag{12.269}
$$

$$
= \left(1 - \frac{\log(\log n) + 4}{\log n}\right) \log n \tag{12.270}
$$

$$
= (1 - \epsilon_n) \log n \tag{12.271}
$$

Note that  $\epsilon_n = \min\{1, \frac{2\pi}{\log n}\}$ . Combining (12.271) with (12.262), we obtain the lemma.  $\Box$ 

We will need a simple result on maximum entropy in the proof of the main theorem.

Lemma 12.10.2: Let Z be a positive integer valued random variable with mean  $\mu$ . Then the entropy  $H(Z)$  is bounded by

$$
H(Z) \le (\mu + 1) \log(\mu + 1) - \mu \log \mu \ . \tag{12.272}
$$

Proof: The lemma follows directly from the results of Chapter 11, which show that the probability mass function that maximizes entropy subject to a constraint on the mean is the geometric distribution, for which we can compute the entropy. The details are left as an exercise for the reader.  $\Box$ 

Let  ${X_i}_{i=-\infty}^{\infty}$  be a stationary ergodic process with probability mass function  $P(x_1, x_2, \ldots, x_n)$ . (Ergodic processes are discussed in greater detail in Section 15.7.) For a fixed integer  $k$ , define the kth order Markov approximation to  $P$  as

$$
Q_k(x_{-(k-1)},\ldots,x_0,x_1,\ldots,x_n)\stackrel{\triangle}{=} P(x_{-(k-1)}^0)\prod_{j=1}^n P(x_j|x_{j-k}^{j-1}), \quad (12.273)
$$

where  $x_i^j = (x_i, x_{i+1}, \ldots, x_j)$ ,  $i \leq j$ , and the initial state  $x_{-(k-1)}^{\circ}$  will be part of the specification of  $Q_k$ . Since  $P(X_n | X_{n-k}^n)$  is itself an ergodic process, we have

$$
-\frac{1}{n}\log Q_k(X_1, X_2, \ldots, X_n | X_{-(k-1)}^0) = -\frac{1}{n}\sum_{j=1}^n \log P(X_j | X_{j-k}^{j-1}) \quad (12.274)
$$

$$
\rightarrow -E \log P(X_i | X_{i-k}^{j-1}) \qquad (12.275)
$$

$$
=H(X_i|X_{i-k}^{j-1}). \t(12.276)
$$

We will bound the rate of the Lempel-Ziv code by the entropy rate of the  $k$ th order Markov approximation for all  $k$ . The entropy rate of the Markov approximation  $H(X_i|X_{i-1}^{\prime})$  converges to the entropy rate of the process as  $k\rightarrow\infty$  and this will prove the result

Suppose  $X_{-(k-1)}^n = x_{-(k-1)}^n$ , and suppose that  $x_1^n$  is parsed into c distinct phrases,  $y_1, y_2, \ldots, y_c$ . Let  $\nu_i$  be the index of the start of the *i*th phrase, i.e.,  $y_i = x_i^{v_i+1-i}$ . For each  $i = 1, 2, \ldots, c$ , define  $s_i = x_i^{v_i-1}$ . Thus  $s_i$ is the k bits of x preceding  $y_i$ . Of course,  $s_1 = x_{-(k-1)}^{\circ}$ .

Let  $c_{ls}$  be the number of phrases  $y_i$  with length I and preceding state  $s_i = s$  for  $l = 1, 2, \ldots$  and  $s \in \mathcal{X}^n$ . We then have

$$
\sum_{l,s} c_{ls} = c \tag{12.277}
$$

**and**

and 
$$
\sum_{l,s} lc_{ls} = n .
$$
 (12.278)

We now prove a surprising upper bound on the probability of a string based on the parsing of the string.

Lemma 12.10.3 (Ziv's inequality): For any distinct parsing (in particular, the Lempel-Ziv parsing) of the string  $x_1x_2 \ldots x_n$ , we have

$$
\log Q_k(x_1, x_2, \dots, x_n | s_1) \le - \sum_{l,s} c_{ls} \log c_{ls} . \tag{12.279}
$$

Note that the right hand side does not depend on  $Q_k$ .

Proof: We write

$$
Q_k(x_1, x_2, \dots, x_n | s_1) = Q(y_1, y_2, \dots, y_c | s_1)
$$
 (12.280)

$$
= \prod_{i=1}^{c} P(y_i|s_i), \qquad (12.281)
$$

or

$$
\log Q_k(x_1, x_2, \dots, x_n | s_1) = \sum_{i=1}^c \log P(y_i | s_i)
$$
 (12.282)

$$
= \sum_{l,s} \sum_{i: |y_i| = l, s_i = s} \log P(y_i|s_i)
$$
 (12.283)

$$
= \sum_{l,s} c_{ls} \sum_{i+|y_i|=l, s_i=s} \frac{1}{c_{ls}} \log P(y_i|s_i)
$$
 (12.284)

$$
\leq \sum_{l,s} c_{ls} \log \biggl( \sum_{i \,:\, |y_i| = l, \,s_i = s} \frac{1}{c_{ls}} P(y_i | s_i) \biggr) \,, \quad (12.285)
$$

where the inequality follows from Jensen's inequality and the concavity of the logarithm.

Now since the  $y_i$  are distinct, we have  $\sum_{i:|y_i|=l, s_i=s} P(y_i|s_i) \leq 1$ . Thus

$$
\log Q_k(x_1, x_2, \dots, x_n | s_1) \le \sum_{l,s} c_{ls} \log \frac{1}{c_{ls}}, \tag{12.286}
$$

proving the lemma.  $\Box$ 

We can now prove the main theorem:

**Theorem 12.10.1:** Let  $\{X_n\}$  be a stationary ergodic process with entropy rate  $H(\mathcal{X})$ , and let  $c(n)$  be the number of phrases in a distinct parsing of a sample of length n from this process. Then

$$
\limsup_{n \to \infty} \frac{c(n) \log c(n)}{n} \le H(\mathcal{X})
$$
\n(12.287)

with probability 1.

Proof: We will begin with Ziv's inequality, which we rewrite as

$$
\log Q_k(x_1, x_2, \dots, x_n | s_1) \leq -\sum_{l,s} c_{ls} \log \frac{c_{ls}c}{c}
$$
 (12.288)

$$
= -c \log c - c \sum_{ls} \frac{c_{ls}}{c} \log \frac{c_{ls}}{c} \,. \quad (12.289)
$$

Writing  $\pi_{ls} = \frac{c_{ls}}{c}$ , we have

$$
\sum_{l,s} \pi_{ls} = 1, \qquad \sum_{l,s} l \pi_{ls} = \frac{n}{c} \,, \tag{12.290}
$$

from  $(12.227)$  and  $(12.278)$ . We now define random variables U, V, such that

$$
Pr(U = l, V = s) = \pi_{ls}. \qquad (12.291)
$$

Thus  $EU = \frac{n}{c}$  and

$$
\log Q_k(x_1, x_2, \dots, x_n | s_1) \le cH(U, V) - c \log c \tag{12.292}
$$

or

$$
-\frac{1}{n}\log Q_k(x_1, x_2, \dots, x_n|s_1) \ge \frac{c}{n}\log c - \frac{c}{n}H(U, V).
$$
\n(12.293)

Now

$$
H(U, V) \le H(U) + H(V), \qquad (12.294)
$$

and  $H(V) \leq \log |\mathcal{X}|^k = k$ . By Lemma 12.10.2, we have

$$
H(U) \le (EU + 1) \log (EU + 1) - EU \log EU \tag{12.295}
$$

$$
= \left(\frac{n}{c} + 1\right) \log \left(\frac{n}{c} + 1\right) - \frac{n}{c} \log \frac{n}{c}
$$
 (12.296)

$$
= \log \frac{n}{c} + \left(\frac{n}{c} + 1\right) \log \left(\frac{c}{n} + 1\right). \tag{12.297}
$$

Thus

324

$$
\frac{c}{n} H(U, V) \le \frac{c}{n} k + \frac{c}{n} \log \frac{n}{c} + o(1).
$$
 (12.298)

For a given *n*, the maximum of  $\frac{c}{h}$  log  $\frac{n}{c}$  is attained for the maximum value of c (for  $\frac{c}{n} \leq \frac{1}{e}$ ). But from Lemma 12.10.1,  $c \leq \frac{n}{\log n}(1 + o(1))$ . Thus

$$
\frac{c}{n}\log\frac{n}{c} \le O\left(\frac{\log\log n}{\log n}\right) \tag{12.299}
$$

and therefore  ${}_{n}^{c}H(U, V) \rightarrow 0$  as  $n \rightarrow \infty$ . Therefore

$$
\frac{c(n)\log c(n)}{n} \le -\frac{1}{n}\log Q_k(x_1, x_2, \dots, x_n|s_1) + \epsilon_k(n) \quad (12.300)
$$

where  $\epsilon_k(n) \rightarrow 0$  as  $n \rightarrow \infty$ . Hence, with probability 1,

$$
\limsup_{n \to \infty} \frac{c(n) \log c(n)}{n} \le \lim_{n \to \infty} -\frac{1}{n} \log Q_k(X_1, X_2, \dots, X_n | X_{-(k-1)}^0)
$$
(12.301)

$$
=H(X_0|X_{-1},\ldots,X_{-k})
$$
\n(12.302)

$$
\rightarrow H(\mathscr{X}) \qquad \text{as } k \to \infty \qquad \Box \qquad (12.303)
$$

We now prove that Lempel-Ziv coding is asymptotically optimal.

**Theorem 12.10.2:** Let  ${X_i}_{\infty}^{\infty}$  be a stationary ergodic stochastic process. Let  $l(X_1, X_2, \ldots, X_n)$  be the Lempel-Ziv codeword length associated with  $X_1, X_2, \ldots, X_n$ . Then

$$
\limsup_{n \to \infty} \frac{1}{n} l(X_1, X_2, \dots, X_n) \le H(\mathcal{X}) \quad \text{with probability } 1
$$
\n(12.304)

where  $H(\mathcal{X})$  is the entropy rate of the process.

**Proof:** We have shown that  $l(X_1, X_2, \ldots, X_n) = c(n)(\log c(n) + 1)$ , where  $c(n)$  is the number of phrases in the Lempel-Ziv parsing of the string  $X_1, X_2, \ldots, X_n$ . By Lemma 12.10.1, lim sup  $c(n)/n = 0$ , and thus Theorem 12.10.1 establishes that

with probability 1.

$$
\limsup \frac{l(X_1, X_2, \dots, X_n)}{n} = \limsup \left( \frac{c(n) \log c(n)}{n} + \frac{c(n)}{n} \right) \le H(\mathcal{X})
$$
 $\square$  (12.305)

Thus the length per source symbol of the Lempel-Ziv encoding of an ergodic source is asymptotically no greater than the entropy rate of the source. The Lempel-Ziv code is a simple example of a universal code, i.e., a code that does not depend on the distribution of the source. This code can be used without knowledge of the source distribution and yet will achieve an asymptotic compression equal to the entropy rate of the source.

The Lempel-Ziv algorithm is now the standard algorithm for compression of files-it is implemented in the *compress* program in UNIX and in the *arc* program for PC's. The algorithm typically compresses ASCII text files by about a factor of 2. It has also been implemented in hardware and is used to effectively double the capacity of communication links for text files by compressing the file at one end and decompressing it at the other end.

# 12.11 FISHER INFORMATION AND THE CRAMÉR-RAO INEQUALITY

A standard problem in statistical estimation is to determine the parameters of a distribution from a sample of data drawn from that distribution. For example, let  $X_1, X_2, \ldots, X_n$  be drawn i.i.d.  $\sim \mathcal{N}(\theta, 1)$ . Suppose we wish to estimate  $\theta$  from a sample of size n. There are a number of functions of the data that we can use to estimate  $\theta$ . For example, we can use the first sample  $X_1$ . Although the expected value of  $X_1$  is  $\theta$ , it is clear that we can do better by using more of the data. We guess that the best estimate of  $\theta$  is the sample mean  $\bar{X}_n = \frac{1}{n} \sum X_i$ . Indeed, it can be shown that  $\bar{X}_n$  is the minimum mean squared error unbiased estimator.

We begin with a few definitions. Let  $\{f(x; \theta)\}, \theta \in \Theta$ , denote an indexed family of densities,  $f(x; \theta) \ge 0$ ,  $\int f(x; \theta) dx = 1$  for all  $\theta \in \Theta$ . Here 0 is called the parameter set.

**Definition:** An estimator for  $\theta$  for sample size n is a function  $T: \mathcal{X}^n \to \Theta$ .

An estimator is meant to approximate the value of the parameter. It is therefore desirable to have some idea of the goodness of the approximation. We will call the difference  $T - \theta$  the error of the estimator. The error is a random variable.

**Definition:** The bias of an estimator  $T(X_1, X_2, \ldots, X_n)$  for the parameter  $\theta$  is the expected value of the error of the estimator, i.e., the bias is  $E_{\theta} T(X_1, X_2, \ldots, X_n) - \theta$ . The subscript  $\theta$  means that the expectation is with respect to the density  $f(\cdot; \theta)$ . The estimator is said to be unbiased if the bias is zero, i.e., the expected value of the estimator is equal to the parameter.

**Example 12.11.1:** Let  $X_1, X_2, ..., X_n$  drawn i.i.d.  $\sim f(x) = (1/\lambda) e^{-x/\lambda}$ ,  $x \ge 0$  be a sequence of exponentially distributed random variables. Estimators of  $\lambda$  include  $X_1$  and  $X_n$ . Both estimators are unbiased.

The bias is the expected value of the error, and the fact that it is zero does not guarantee that the error is low with high probability. We need to look at some loss function of the error; the most commonly chosen loss function is the expected square of the error. A good estimator should have a low expected squared error and should have an error that approaches 0 as the sample size goes to infinity. This motivates the following definition:

**Definition:** An estimator  $T(X_1, X_2, \ldots, X_n)$  for  $\theta$  is said to be consistent in probability if  $T(X_1, X_2, \ldots, X_n) \to \theta$  in probability as  $n \to \infty$ .

Consistency is a desirable asymptotic property, but we are interested in the behavior for small sample sizes as well. We can then rank estimators on the basis of their mean squared error.

**Definition:** An estimator  $T_1(X_1, X_2, \ldots, X_n)$  is said to *dominate* another estimator  $T_2(X_1, X_2, \ldots, X_n)$  if, for all  $\theta$ ,

$$
E_{\theta}(T_1(X_1, X_2, \ldots, X_n) - \theta)^2 \le E_{\theta}(T_2(X_1, X_2, \ldots, X_n) - \theta)^2, \quad (12.306)
$$

This definition raises a natural question: what is the minimum variance unbiased estimator of  $\theta$ ? To answer this question, we derive the Cramer-Rao lower bound on the mean squared error of any estimator. We first define the score function of the distribution  $f(x; \theta)$ . We then use the Cauchy-Schwarz inequality to prove the Cramer-Rao lower bound on the variance of all unbiased estimators.

**Definition:** The score  $V$  is a random variable defined by

$$
V = \frac{\partial}{\partial \theta} \ln f(X; \theta) = \frac{\frac{\partial}{\partial \theta} f(X; \theta)}{f(X; \theta)},
$$
 (12.307)

where  $X \sim f(x; \theta)$ .

The mean value of the score is

$$
EV = \int \frac{\frac{\partial}{\partial \theta} f(x;\theta)}{f(x;\theta)} f(x;\theta) dx
$$
 (12.308)

$$
= \int \frac{\partial}{\partial \theta} f(x; \theta) dx \qquad (12.309)
$$

$$
=\frac{\partial}{\partial \theta}\int f(x;\theta)\,dx\tag{12.310}
$$

$$
=\frac{\partial}{\partial \theta}1\tag{12.311}
$$

$$
=0,
$$
 (12.312)

and therefore  $EV^2 = \text{var}(V)$ . The variance of the score has a special significance.

**Definition:** The Fisher information  $J(\theta)$  is the variance of the score, i.e.,

$$
J(\theta) = E_{\theta} \left[ \frac{\partial}{\partial \theta} \ln f(x; \theta) \right]^2.
$$
 (12.313)

If we consider a sample of n random variables  $X_1, X_2, \ldots, X_n$  drawn i.i.d.  $\sim f(x; \theta)$ , we have

$$
f(x_1, x_2, \dots, x_n; \theta) = \prod_{i=1}^n f(x_i; \theta), \qquad (12.314)
$$

and the score function is the sum of the individual score functions,

$$
V(X_1, X_2, \dots, X_n) = \frac{\partial}{\partial \theta} \ln f(X_1, X_2, \dots, X_n; \theta) \qquad (12.315)
$$

$$
= \sum_{i=1}^{n} \frac{\partial}{\partial \theta} \ln f(X_i; \theta) \qquad (12.316)
$$

$$
=\sum_{i=1}^{n} V(X_i)\,,\tag{12.317}
$$

where the V( $\alpha$ ) are independent, identically distributed with zero independent, identically distributed with zero independent, independent, independent, independent, independent, independent, independent, independent, i where the  $V(\Lambda_i)$  are independent, in

$$
J_n(\theta) = E_{\theta} \left[ \frac{\partial}{\partial \theta} \ln f(x_1, x_2, \dots, x_n; \theta) \right]^2 \quad (12.318)
$$

$$
=E_{\theta}V^{2}(X_{1}, X_{2}, \ldots, X_{n})
$$
\n(12.319)

$$
=E_{\theta}\left(\sum_{i=1}^{n}V(X_{i})\right)^{2} \tag{12.320}
$$

$$
=\sum_{i=1}^{n} E_{\theta} V^{2}(X_{i})
$$
\n(12.321)

$$
= nJ(\theta) \tag{12.322}
$$

Consequently, the Fisher information for  $n$  i.i.d. samples is  $n$  times the individual Fisher information. The significance of the Fisher information is shown in the following theorem:

**Theorem: 12.11.1** (Cramér-Rao inequality): The mean squared error of any unbiased estimator  $T(X)$  of the parameter  $\theta$  is lower bounded by the reciprocal of the Fisher information, i.e.,

$$
\text{var}(T) \ge \frac{1}{J(\theta)}\,. \tag{12.323}
$$

**Proof:** Let  $V$  be the score function and  $T$  be the estimator. By the Cauchy-Schwarz inequality, we have

$$
(E_{\theta}[(V - E_{\theta}V)(T - E_{\theta}T))]^{2} \leq E_{\theta}(V - E_{\theta}V)^{2}E_{\theta}(T - E_{\theta}T)^{2}.
$$
\n(12.324)

By (12.312),  $E_{\theta}V=0$  and hence  $E_{\theta}(V-E_{\theta}V)(T-E_{\theta}T)=E_{\theta}(VT)$ . Also, by definition,  $var(V) = J(\theta)$ . Substituting these conditions in (12.324), we have

$$
\left[E_{\theta}(VT)\right]^2 \leq J(\theta) \operatorname{var}(T) \tag{12.325}
$$

Now,

$$
E_{\theta}(VT) = \int \frac{\frac{\partial}{\partial \theta} f(x;\theta)}{f(x;\theta)} T(x)f(x;\theta) dx
$$
 (12.326)

$$
= \int \frac{\partial}{\partial \theta} f(x; \theta) T(x) dx \qquad (12.327)
$$

$$
= \frac{\partial}{\partial \theta} \int f(x;\theta) T(x) dx \qquad (12.328)
$$

$$
=\frac{\partial}{\partial \theta} E_{\theta} T \tag{12.329}
$$

$$
=\frac{\partial}{\partial\theta}\,\theta\tag{12.330}
$$

$$
=1. \t(12.331)
$$

where the interchange of differentiation and integration in (12.328) can be justified using the bounded convergence theorem for appropriately well behaved  $f(x; \theta)$  and (12.330) follows from the fact that the estimator  $T$  is unbiased. Substituting this in  $(12.325)$ , we obtain

$$
\text{var}(T) \ge \frac{1}{J(\theta)}\,,\tag{12.332}
$$

which is the Cramer-Rao inequality for unbiased estimators.  $\Box$ 

By essentially the same arguments, we can show that for any estimator

$$
E_{\theta}(T-\theta)^2 \ge \frac{[1+b'_T(\theta)]^2}{J(\theta)} + b_T^2(\theta),
$$
 (12.333)

where  $b_T(\theta) = E_{\theta}T - \theta$  and  $b_T(\theta)$  is the derivative of  $b_T(\theta)$  with respect to  $\theta$ . The proof of this is left as an exercise at the end of the chapter.

**Example 12.11.2:** Let  $X_1, X_2, \ldots, X_n$  be i.i.d.  $\sim \mathcal{N}(\theta, \sigma^2)$ ,  $\sigma^2$  known. Here  $J(\theta)=\frac{h}{\sigma^2}$ . Let  $T(X_1,X_2,\ldots,X_n)=X_n=\frac{1}{h}\Sigma X$  $\frac{\sigma^2}{n} = \frac{1}{J(\theta)}$ . Thus  $\bar{X}_n$  is the minimum variance unbiased estimator of  $\theta$ , since it achieves the Cramér-Rao lower bound.

The Cramer-Rao inequality gives us the lowest possible variance for all unbiased estimators. We now use it to define the most efficient estimator.

**Definition:** An unbiased estimator  $T$  is said to be *efficient* if it meets the Cramer-Rao bound with equality, i.e., if  $var(T) = \frac{1}{J(\theta)}$ .

The Fisher information is therefore a measure of the amount of "information" about  $\theta$  that is present in the data. It gives a lower bound on the error in estimating  $\theta$  from the data. However, it is possible that there does not exist an estimator meeting this lower bound.

We can generalize the concept of Fisher information to the multiparameter case, in which case we define the Fisher information matrix  $J(\theta)$  with elements

$$
J_{ij}(\theta) = \int f(x;\,\theta) \frac{\partial}{\partial \theta_i} \ln f(x;\,\theta) \frac{\partial}{\partial \theta_j} \ln f(x;\,\theta) \, dx \,. \tag{12.334}
$$

The Cramer-Rao inequality becomes the matrix inequality

$$
\Sigma \ge J^{-1}(\theta), \tag{12.335}
$$

where  $\Sigma$  is the covariance matrix of a set of unbiased estimators for the parameters  $\theta$  and  $\Sigma \ge J^{-1}(\theta)$  in the sense that the difference  $\Sigma - J^{-1}$  is a non-negative definite matrix. We will not go into the details of the proof for multiple parameters; the basic ideas are similar.

Is there a relationship between the Fisher information  $J(\theta)$  and quantities like entropy defined earlier? Note that Fisher information is defined with respect to a family of parametric distributions, unlike entropy, which is defined for all distributions. But we can parametrize any distribution,  $f(x)$ , by a location parameter  $\theta$  and define Fisher information with respect to the family of densities  $\{f(x - \theta)\}\$ under translation. We will explore the relationship in greater detail in Section 16.7, where we show that while entropy is related to the volume of the typical set, the Fisher information is related to the surface area of the typical set. Further relationships of Fisher information to relative entropy are developed in the exercises.

Image /page/23/Figure/2 description: The image is a page from a document summarizing Chapter 12. It is divided into sections: Basic identities, Universal data compression, and Large deviations (Sanov's theorem). Each section contains mathematical formulas and their corresponding equation numbers. The basic identities include formulas for Q"(x), |P\_n|, |T(P)|, and Q"(T(P)). The universal data compression section defines P\_e^(n) and D(P\*\_R||Q). The large deviations section presents Sanov's theorem with formulas for Q"(E) and D(P\*\_R||Q), and a conditional statement about Q"(E) when E is the closure of its interior.

 $\mathscr{L}_1$  bound on relative entropy:

$$
D(P_1||P_2) \ge \frac{1}{2\ln 2} ||P_1 - P_2||_1^2
$$
 (12.345)

**Pythagorean theorem:** If E is a convex set of types, distribution  $Q \not\in E$ , and  $P^*$  achieves  $D(P^*||Q) = \min_{P \in E} D(P||Q)$ , we have

$$
D(P||Q) \ge D(P||P^*) + D(P^*||Q)
$$
 (12.346)

for all  $P \in E$ .

Conditional limit theorem: If  $X_1, X_2, \ldots, X_n$  i.i.d.  $\sim Q$ , then

$$
Pr(X_1 = a | P_{X^n} \in E) \to P^*(a) \text{ in probability}, \qquad (12.347)
$$

where  $P^*$  minimizes  $D(P||Q)$  over  $P \in E$ . In particular,

$$
\Pr\left\{X_1 = a \middle| \frac{1}{n} \sum_{i=1}^{n} X_i \ge \alpha \right\} \to \frac{Q(a)e^{\lambda a}}{\sum_{x} Q(x)e^{\lambda x}} \,. \tag{12.348}
$$

**Neyman-Pearson lemma:** The optimum test between two densities  $P_1$  and  $P_2$  has a decision region of the form "Accept  $P = P_1$  if  $\frac{1}{P_2(x_1, x_2, \ldots, x_n)} > T$ ."

Stein's lemma: The best achievable error exponent  $\beta_n^*$  if  $\alpha_n \leq \epsilon$ .

$$
\beta_n^{\epsilon} = \min_{\substack{A_n \subseteq \mathscr{X}^n \\ \alpha_n \le \epsilon}} \beta_n \,. \tag{12.349}
$$

$$
\lim_{n \to \infty} \frac{1}{n} \log \beta_n^{\epsilon} = -D(P_1 \| P_2). \tag{12.350}
$$

Chernoff information: The best achievable exponent for a Bayesian probability of error is

$$
D^* = D(P_{\lambda^*} || P_1) = D(P_{\lambda^*} || P_2), \qquad (12.351)
$$

where

$$
P_{\lambda} = \frac{P_{1}^{\lambda}(x)P_{2}^{1-\lambda}(x)}{\sum_{a \in \mathcal{F}} P_{1}^{\lambda}(a)P_{2}^{1-\lambda}(a)} \tag{12.352}
$$

with  $\lambda = \lambda^*$  chosen so that

$$
D(P_{\lambda} \| P_1) = D(P_{\lambda} \| P_2) . \qquad (12.353)
$$

Lempel-Ziv: Universal data compression. For a stationary ergodic source,

$$
\limsup \frac{l(X_1, X_2, \dots, X_n)}{n} = \limsup \frac{c(n) \log c(n)}{n} \le H(\mathcal{X}). \quad (12.354)
$$

Fisher information:

$$
J(\theta) = E_{\theta} \left[ \frac{\partial}{\partial \theta} \ln f(x; \theta) \right]^2.
$$
 (12.355)

**Cramér-Rao inequality:** For any unbiased estimator T of  $\theta$ ,

$$
E_{\theta}(T(X) - \theta)^2 = \text{var}(T) \ge \frac{1}{J(\theta)}\,. \tag{12.356}
$$

## PROBLEMS FOR CHAPTER 12

1. Stein's lemma. Consider the two hypothesis test

$$
H_1: f = f_1 \quad \text{vs.} \quad H_2: f = f_2
$$

Find  $D(f_1||f_2)$  if

- (a)  $f_i(x) = N(0, \sigma_i^2), i = 1, 2$
- (b)  $f_i(x) = \lambda_i e^{-\lambda_i x}, x \ge 0, i = 1, 2$
- (c)  $f_1(x)$  is the uniform density over the interval [0, 1] and  $f_2(x)$  is the uniform density over  $[a, a + 1]$ . Assume  $0 < a < 1$ .
- (d)  $f_1$  corresponds to a fair coin and  $f_2$  corresponds to a two-headed coin.
- 2. A relation between  $D(P||Q)$  and chi-square. Show that the  $\chi^2$  statistic

$$
\chi^2 = \sum_x \frac{(P(x) - Q(x))^2}{Q(x)}
$$

is (twice) the first term in the Taylor series expansion of  $D(P||Q)$ about Q. Thus  $D(P||Q) = \frac{1}{2}\chi^2 + \cdots$ . *Hint*: Write  $\frac{p}{\sigma} = 1 + \frac{p - \bar{q}}{\sigma^2}$  and expand the log.

Hint: Write \$\frac{p}{q}=1+\frac{p-q}{q}\$ and expand the log.

- 3. Error exponent for universal codes. A universal source code of rate  $R$ achieves a probability of error  $P_e^{(n)} = e^{-nD(P^*)|Q}$ , where Q is the true distribution and  $P^*$  achieves min  $D(P||Q)$  over all P such that  $H(P) \ge$ R.
  - (a) Find  $P^*$  in terms of  $Q$  and  $R$ .
  - (b) Now let  $X$  be binary. Find the region of source probabilities  $Q(x)$ ,  $x \in \{0, 1\}$ , for which rate R is sufficient for the universal source code to achieve  $P_e^{(n)} \rightarrow 0$ .
- 4. Sequential projection. We wish to show that projecting  $Q$  onto  $P$ , and then projecting the projection  $\hat{Q}$  onto  $P_1 \cap P_2$  is the same as projecting Q directly onto  $P_1 \cap P_2$ . Let  $\mathcal{P}_1$  be the set of probability mass functions on  $\mathscr X$  satisfying

$$
\sum_{x} p(x) = 1, \qquad (12.357)
$$

$$
\sum_{x} p(x)h_i(x) \ge \alpha_i, \quad i = 1, 2, \dots, r. \tag{12.358}
$$

Let  $\mathcal{P}_2$  be the set of probability mass functions on  $\mathcal X$  satisfying

$$
\sum_{x} p(x) = 1, \tag{12.359}
$$

$$
\sum_{x} p(x)g_{j}(x) \ge \beta_{j}, \quad j = 1, 2, ..., s. \quad (12.360)
$$

Suppose  $Q \not\in P_1 \cup P_2$ . Let  $P^*$  minimize  $D(P||Q)$  over all  $P \in \mathcal{P}_1$ . Let  $R^*$  minimize  $D(R||Q)$  over all  $R \in \mathcal{P}_1 \cap \mathcal{P}_2$ . Argue that  $R^*$  minimizes  $D(R||P^*)$  over all  $R \in P_1 \cap P_2$ .

5. Counting. Let  $\mathcal{X} = \{1, 2, ..., m\}$ . Show that the number of sequences  $x^n \in \mathcal{X}^n$  satisfying  $\frac{1}{n} \sum_{i=1}^n g(x_i) \geq \alpha$  is approximately equal to  $2^{nH^*}$ , to first order in the exponent, for  $n$  sufficiently large, where

$$
H^* = \max_{P : \Sigma_{i=1}^m P(i)g(i) \ge \alpha} H(P).
$$
 (12.361)

- 6. Biased estimates may be better. Consider the problem of estimating  $\mu$ and  $\sigma^2$  from *n* samples of data drawn i.i.d. from a  $\mathcal{N}(\mu, \sigma^2)$  distribution.
  - (a) Show that  $\bar{X}$  is an unbiased estimator of  $\mu$ .
  - (b) Show that the estimator

$$
S_n^2 = \frac{1}{n} \sum_{i=1}^n (X_i - \bar{X}_n)^2
$$
 (12.362)

is biased and the estimator

$$
S_{n-1}^{2} = \frac{1}{n-1} \sum_{i=1}^{n} (X_i - \bar{X}_n)^2
$$
 (12.363)

is unbiased.

- (c) Show that  $S_n^2$  has a lower mean squared error than  $S_{n-1}^2$ . This illustrates the idea that a biased estimator may be "better" than an unbiased estimator.
- 7. Fisher information and relative entropy. Show for a parametric family  ${p_a(x)}$  that

$$
\lim_{\theta' \to \theta} \frac{1}{(\theta - \theta')^2} D(p_{\theta} || p_{\theta'}) = \frac{1}{\ln 4} J(\theta).
$$
 (12.364)

8. Examples of Fisher information. The Fisher information  $J(\theta)$  for the family  $f_{\theta}(x)$ ,  $\theta \in \mathbb{R}$  is defined by

$$
J(\theta) = E_{\theta} \left( \frac{\partial f_{\theta}(X) / \partial \theta}{f_{\theta}(X)} \right)^2 = \int \frac{(f_{\theta}')^2}{f_{\theta}}.
$$

Find the Fisher information for the following families:

- (a)  $f_{\theta}(x) = N(0, \theta) = \frac{1}{\sqrt{2\pi\theta}}e^{-\frac{x^2}{2\theta}}$
- (b)  $f_a(x) = \theta e^{-\theta x}, x \ge 0$
- (c) What is the Cramér-Rae lower bound on  $E_{\theta}(\hat{\theta}(X) \theta)^2$ , where  $\hat{\theta}(X)$ is an unbiased estimator of  $\theta$  for (a) and (b)?
- 9. Two conditionally independent looks double the Fisher information. Let  $g_{\theta}(x_1, x_2) = f_{\theta}(x_1)f_{\theta}(x_2)$ . Show  $J_{\theta}(\theta) = 2J_{\theta}(\theta)$ .
- 10. Joint distributions and product distributions. Consider a joint distribution  $Q(x, y)$  with marginals  $Q(x)$  and  $Q(y)$ . Let E be the set of types that look jointly typical with respect to  $Q$ , i.e.,

$$
E = \{P(x, y) : -\sum_{x, y} P(x, y) \log Q(x) - H(X) = 0,
$$
$$
-\sum_{x, y} P(x, y) \log Q(y) - H(Y) = 0,
$$
$$
-\sum_{x, y} P(x, y) \log Q(x, y) - H(X, Y) = 0 \}.
$$
(12.365)

(a) Let  $Q_0(x, y)$  be another distribution on  $\mathscr{X} \times \mathscr{Y}$ . Argue that the distribution  $P^*$  in E that is closest to  $Q_0$  is of the form

$$
P^{*}(x, y) = Q_0(x, y)e^{\lambda_0 + \lambda_1 \log Q(x) + \lambda_2 \log Q(y) + \lambda_3 \log Q(x, y)},
$$
\n(12.366)

where  $\lambda_0$ ,  $\lambda_1$ ,  $\lambda_2$  and  $\lambda_3$  are chosen to satisfy the constraints. Argue that this distribution is unique.

- (b) Now let  $Q_0(x, y) = Q(x)Q(y)$ . Verify that  $Q(x, y)$  is of the form (12.366) and satisfies the constraints. Thus  $P^*(x, y) = Q(x, y)$ , i.e., the distribution in  $E$  closest to the product distribution is the joint distribution.
- 11. Cramér-Rao inequality with a bias term. Let  $X \sim f(x; \theta)$  and let  $T(X)$  be an estimator for  $\theta$ . Let  $b_T(\theta) = E_{\theta}T - \theta$  be the bias of the estimator. Show that

$$
E_{\theta}(T-\theta)^2 \ge \frac{[1+b'_T(\theta)]^2}{J(\theta)} + b_T^2(\theta).
$$
 (12.367)

12. Lempel-Ziv. Give the Lempel-Ziv parsing and encoding of 000000110- 10100000110101.

### HISTORICAL NOTES

The method of types evolved from notions of weak typicality and strong typicality; some of the ideas were used by Wolfowitz [277] to prove channel capacity theorems. The method was fully developed by Csiszár and Körner [83], who derived the main theorems of information theory from this viewpoint. The method of types described in Section 12.1 follows the development in Csiszár and Körner. The  $\mathscr{L}_1$  lower bound on relative entropy is due to Csiszár [78], Kullback [151] and Kemperman [227]. Sanov's theorem [175] was generalized by Csiszár [289] using the method of types.

The parsing algorithm for Lempel-Ziv encoding was introduced by Lempel and Ziv [175] and was proved to achieve the entropy rate by Ziv [289]. The algorithm described in the text was first described in Ziv and Lempel [289]. A more transparent proof was provided by Wyner and Ziv [285], which we have used to prove the results in Section 12.10. A number of different variations of the basic Lempel-Ziv algorithm are described in the book by Bell, Cleary and Witten  $[22]$ .