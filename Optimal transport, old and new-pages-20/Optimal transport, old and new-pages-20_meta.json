{"table_of_contents": [{"title": "Qualitative picture", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.5], [269.25, 97.5], [269.25, 111.8583984375], [133.5, 111.8583984375]]}, {"title": "Recap", "heading_level": null, "page_id": 0, "polygon": [[133.5, 312.75], [171.75, 312.75], [171.75, 324.263671875], [133.5, 324.263671875]]}, {"title": "348 13 Qualitative picture", "heading_level": null, "page_id": 1, "polygon": [[133.5, 26.079345703125], [257.888671875, 26.079345703125], [257.888671875, 35.553955078125], [133.5, 35.553955078125]]}, {"title": "Standard approximation procedure", "heading_level": null, "page_id": 4, "polygon": [[133.5, 48.75], [343.0546875, 48.75], [343.0546875, 59.31298828125], [133.5, 59.31298828125]]}, {"title": "352 13 Qualitative picture", "heading_level": null, "page_id": 5, "polygon": [[133.5, 25.8134765625], [257.4404296875, 25.8134765625], [257.4404296875, 35.4814453125], [133.5, 35.4814453125]]}, {"title": "Equations of displacement interpolation", "heading_level": null, "page_id": 6, "polygon": [[133.5, 451.5], [369.75, 451.5], [369.75, 462.90234375], [133.5, 462.90234375]]}, {"title": "354 13 Qualitative picture", "heading_level": null, "page_id": 7, "polygon": [[133.5, 26.103515625], [258.0, 26.103515625], [258.0, 35.62646484375], [133.5, 35.62646484375]]}, {"title": "Quadratic cost function", "heading_level": null, "page_id": 8, "polygon": [[133.1279296875, 47.638916015625], [274.7724609375, 47.638916015625], [274.7724609375, 58.974609375], [133.1279296875, 58.974609375]]}, {"title": "The structure of P_2(M)", "heading_level": null, "page_id": 10, "polygon": [[133.5, 126.75], [277.7607421875, 126.75], [277.7607421875, 139.025390625], [133.5, 139.025390625]]}, {"title": "Theorem 13.8 (Representation of Lipschitz paths in P_2(M)).", "heading_level": null, "page_id": 11, "polygon": [[133.5, 192.75], [462.0, 192.75], [462.0, 203.607421875], [133.5, 203.607421875]]}, {"title": "360 13 Qualitative picture", "heading_level": null, "page_id": 13, "polygon": [[133.5, 26.103515625], [257.58984375, 26.103515625], [257.58984375, 35.62646484375], [133.5, 35.62646484375]]}, {"title": "362 13 Qualitative picture", "heading_level": null, "page_id": 15, "polygon": [[133.5, 26.24853515625], [257.888671875, 26.24853515625], [257.888671875, 35.4814453125], [133.5, 35.4814453125]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 16, "polygon": [[233.68359375, 48.0498046875], [359.25, 48.0498046875], [359.25, 59.16796875], [233.68359375, 59.16796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 32], ["TextInlineMath", 3], ["SectionHeader", 2], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2891, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 46], ["TextInlineMath", 6], ["Text", 4], ["Equation", 4], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["Line", 51], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 318], ["Line", 45], ["Text", 4], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3483, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 44], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 39], ["TextInlineMath", 5], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 548], ["Line", 34], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 65], ["Equation", 4], ["Text", 4], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6247, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 41], ["TextInlineMath", 8], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 734], ["Line", 50], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 40], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 54], ["TextInlineMath", 5], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 107], ["TextInlineMath", 3], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3487, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 52], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 78], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1552, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 49], ["Text", 5], ["Equation", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1013, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 39], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 39], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 30], ["Line", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-20"}