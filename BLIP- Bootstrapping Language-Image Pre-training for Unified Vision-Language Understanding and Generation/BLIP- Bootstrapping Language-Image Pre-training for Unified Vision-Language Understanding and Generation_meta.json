{"table_of_contents": [{"title": "BLIP: Bootstrapping Language-Image Pre-training for\nUnified Vision-Language Understanding and Generation", "heading_level": null, "page_id": 0, "polygon": [[123.75, 89.25], [471.75, 89.25], [471.75, 120.5595703125], [123.75, 120.5595703125]]}, {"title": "Junnan Li <PERSON>u Li <PERSON> Steven Hoi\nSalesforce Research", "heading_level": null, "page_id": 0, "polygon": [[185.4228515625, 158.25], [408.19921875, 158.25], [408.19921875, 186.3017578125], [185.4228515625, 186.3017578125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 215.25], [195.75, 215.25], [195.75, 227.00390625], [148.5, 227.00390625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 531.0], [132.75, 531.0], [132.75, 542.56640625], [54.0, 542.56640625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[54.0, 427.5], [138.75, 427.5], [138.75, 438.5390625], [54.0, 438.5390625]]}, {"title": "2.1. Vision-language Pre-training", "heading_level": null, "page_id": 1, "polygon": [[54.0, 445.5], [197.25, 445.5], [197.25, 455.94140625], [54.0, 455.94140625]]}, {"title": "2.2. Knowledge Distillation", "heading_level": null, "page_id": 1, "polygon": [[306.0, 457.5], [422.25, 457.5], [422.25, 467.9296875], [306.0, 467.9296875]]}, {"title": "2.3. Data Augmentation", "heading_level": null, "page_id": 1, "polygon": [[305.701171875, 636.75], [408.75, 636.75], [408.75, 648.140625], [305.701171875, 648.140625]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[54.0, 137.3818359375], [108.0, 137.3818359375], [108.0, 149.5634765625], [54.0, 149.5634765625]]}, {"title": "3.1. Model Architecture", "heading_level": null, "page_id": 2, "polygon": [[54.0, 212.25], [156.75, 212.25], [156.75, 223.13671875], [54.0, 223.13671875]]}, {"title": "3.2. Pre-training Objectives", "heading_level": null, "page_id": 2, "polygon": [[54.0, 584.71875], [173.25, 584.71875], [173.25, 595.546875], [54.0, 595.546875]]}, {"title": "3.3. Cap<PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[306.0, 587.0390625], [358.5, 587.0390625], [358.5, 598.640625], [306.0, 598.640625]]}, {"title": "4. Experiments and Discussions", "heading_level": null, "page_id": 3, "polygon": [[54.0, 532.5], [216.75, 532.5], [216.75, 543.7265625], [54.0, 543.7265625]]}, {"title": "4.1. Pre-training Details", "heading_level": null, "page_id": 3, "polygon": [[54.0, 585.4921875], [157.5, 585.4921875], [157.5, 595.546875], [54.0, 595.546875]]}, {"title": "4.2. Effect of CapFilt", "heading_level": null, "page_id": 3, "polygon": [[306.0, 466.5], [396.75, 466.5], [396.75, 476.82421875], [306.0, 476.82421875]]}, {"title": "4.3. Diversity is Key for Synthetic Captions", "heading_level": null, "page_id": 4, "polygon": [[54.0, 612.5625], [238.5, 612.5625], [238.5, 623.390625], [54.0, 623.390625]]}, {"title": "4.4. Parameter Sharing and Decoupling", "heading_level": null, "page_id": 4, "polygon": [[304.20703125, 636.5390625], [476.25, 636.5390625], [476.25, 647.3671875], [304.20703125, 647.3671875]]}, {"title": "5. Comparison with State-of-the-arts", "heading_level": null, "page_id": 5, "polygon": [[54.0, 648.0], [243.75, 648.0], [243.75, 659.35546875], [54.0, 659.35546875]]}, {"title": "5.1. Image-Text Retrieval", "heading_level": null, "page_id": 5, "polygon": [[306.0, 369.75], [415.5, 369.75], [415.5, 379.951171875], [306.0, 379.951171875]]}, {"title": "5.2. Image Captioning", "heading_level": null, "page_id": 5, "polygon": [[306.0, 593.25], [402.75, 593.25], [402.75, 602.89453125], [306.0, 602.89453125]]}, {"title": "5.3. Visual Question Answering (VQA)", "heading_level": null, "page_id": 6, "polygon": [[54.0, 666.703125], [220.5, 666.703125], [220.5, 677.53125], [54.0, 677.53125]]}, {"title": "5.4. Natural Language Visual Reasoning (NLVR<sup>2</sup>)", "heading_level": null, "page_id": 6, "polygon": [[306.59765625, 677.6287536621094], [519.569580078125, 677.6287536621094], [519.569580078125, 689.51953125], [306.59765625, 689.51953125]]}, {"title": "5.5. Visual Dialog (VisDial)", "heading_level": null, "page_id": 7, "polygon": [[54.0, 402.0], [170.25, 402.0], [170.25, 412.62890625], [54.0, 412.62890625]]}, {"title": "5.6. Zero-shot Transfer to Video-Language Tasks", "heading_level": null, "page_id": 7, "polygon": [[54.0, 594.75], [264.0, 594.75], [264.0, 604.828125], [54.0, 604.828125]]}, {"title": "6. Additional Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[306.0, 519.75], [455.25, 519.75], [455.25, 530.578125], [306.0, 530.578125]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[54.0, 316.5], [125.25, 316.5], [125.25, 327.744140625], [54.0, 327.744140625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 570.75], [111.75, 570.75], [111.75, 582.78515625], [54.0, 582.78515625]]}, {"title": "<PERSON><PERSON>am Task Details", "heading_level": null, "page_id": 11, "polygon": [[54.75, 67.82080078125], [201.0, 67.82080078125], [201.0, 79.51904296875], [54.75, 79.51904296875]]}, {"title": "B. Additional Examples of Synthetic Captions", "heading_level": null, "page_id": 11, "polygon": [[306.0, 178.470703125], [543.0, 178.470703125], [543.0, 189.87890625], [306.0, 189.87890625]]}, {"title": "C. Pre-training Dataset Details", "heading_level": null, "page_id": 11, "polygon": [[306.0, 249.8203125], [465.57421875, 249.8203125], [465.57421875, 261.80859375], [306.0, 261.80859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 87], ["Text", 10], ["SectionHeader", 4], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7137, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 95], ["Text", 6], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 788, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 101], ["Text", 14], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 107], ["Text", 8], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["TableCell", 315], ["Line", 92], ["Text", 8], ["Reference", 4], ["Caption", 3], ["Table", 3], ["Picture", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 1, "llm_tokens_used": 16491, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 498], ["Span", 409], ["Line", 83], ["Text", 7], ["Reference", 4], ["Table", 3], ["SectionHeader", 3], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 7015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 452], ["Span", 450], ["Line", 102], ["Text", 6], ["Figure", 3], ["Reference", 3], ["Caption", 2], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 14400, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["TableCell", 198], ["Line", 98], ["Text", 9], ["Table", 3], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 10353, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["TableCell", 142], ["Line", 81], ["Reference", 14], ["ListItem", 12], ["Text", 5], ["Table", 2], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4557, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 95], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 75], ["ListItem", 19], ["Reference", 19], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 89], ["TableCell", 80], ["Text", 25], ["Picture", 4], ["SectionHeader", 3], ["Reference", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 9433, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BLIP- Bootstrapping Language-Image Pre-training for Unified Vision-Language Understanding and Generation"}