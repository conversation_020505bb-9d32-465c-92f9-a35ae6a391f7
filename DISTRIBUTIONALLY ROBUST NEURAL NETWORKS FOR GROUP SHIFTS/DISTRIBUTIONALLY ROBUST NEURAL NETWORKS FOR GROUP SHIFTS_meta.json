{"table_of_contents": [{"title": "DISTRIBUTION<PERSON><PERSON><PERSON> ROBUST NEURAL NETWORKS\nFOR GROUP SHIFTS: ON THE IMPORTANCE OF\nREGULA<PERSON><PERSON><PERSON>ION FOR WORST-CASE GENERALIZATION", "heading_level": null, "page_id": 0, "polygon": [[106.083984375, 81.0], [518.466796875, 79.5], [518.466796875, 136.4150390625], [106.083984375, 136.4150390625]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 261.0], [334.5, 261.0], [334.5, 271.4765625], [276.75, 271.4765625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[106.45751953125, 495.0], [206.490234375, 495.0], [206.490234375, 506.6015625], [106.45751953125, 506.6015625]]}, {"title": "2 SETUP", "heading_level": null, "page_id": 1, "polygon": [[107.25, 565.5], [160.5, 565.5], [160.5, 576.984375], [107.25, 576.984375]]}, {"title": "2.1 APPLICATIONS", "heading_level": null, "page_id": 2, "polygon": [[106.*********, 404.12109375], [196.1806640625, 404.12109375], [196.1806640625, 413.40234375], [106.*********, 413.40234375]]}, {"title": "3 COMPARISON BETWEEN GROUP DRO AND ERM", "heading_level": null, "page_id": 3, "polygon": [[107.25, 154.880859375], [374.73046875, 154.880859375], [374.73046875, 167.0625], [107.25, 167.0625]]}, {"title": "3.1 ERM AND DRO HAVE POOR WORST-GROUP ACCURACY IN THE OVERPARAMETERIZED\nREGIME", "heading_level": null, "page_id": 3, "polygon": [[107.25, 299.*********], [498.0, 299.*********], [498.0, 321.*********], [107.25, 321.*********]]}, {"title": "3.2 DRO IMPROVES WORST-GROUP ACCURACY UNDER APPROPRIATE REGULARIZATION", "heading_level": null, "page_id": 3, "polygon": [[106.5, 639.********], [488.********, 639.********], [488.********, 650.********], [106.5, 650.********]]}, {"title": "3.3 ACCOUNTING FOR GENERALIZAT<PERSON> THROUGH GROUP ADJUSTMENTS IMPROVES DRO", "heading_level": null, "page_id": 5, "polygon": [[106.5, 380.25], [501.********, 380.25], [501.********, 390.*********], [106.5, 390.*********]]}, {"title": "4 COMPARISON BETWEEN DRO AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WEIGHTING", "heading_level": null, "page_id": 6, "polygon": [[106.********, 444.75], [437.25, 444.75], [437.25, 455.5546875], [106.********, 455.5546875]]}, {"title": "5 ALGORITHM", "heading_level": null, "page_id": 8, "polygon": [[107.25, 81.75], [191.**********, 81.75], [191.**********, 93.**********], [107.25, 93.**********]]}, {"title": "Algorithm 1: Online optimization algorithm for group DRO", "heading_level": null, "page_id": 8, "polygon": [[107.25, 417.0], [350.25, 417.0], [350.25, 427.7109375], [107.25, 427.7109375]]}, {"title": "6 RELATED WORK", "heading_level": null, "page_id": 9, "polygon": [[106.*********, 81.***********], [210.*********, 81.***********], [210.*********, 93.82763671875], [106.*********, 93.82763671875]]}, {"title": "7 DISCUSSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 521.68359375], [193.04296875, 521.68359375], [193.04296875, 534.05859375], [107.25, 534.05859375]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 10, "polygon": [[107.20458984375, 82.5], [219.9375, 82.5], [219.9375, 93.15087890625], [107.20458984375, 93.15087890625]]}, {"title": "REPRODUCIBILITY", "heading_level": null, "page_id": 10, "polygon": [[107.25, 202.833984375], [204.0, 202.833984375], [204.0, 213.275390625], [107.25, 213.275390625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[107.25, 289.845703125], [176.90625, 289.845703125], [176.90625, 299.*********], [107.25, 299.*********]]}, {"title": "A PROOFS", "heading_level": null, "page_id": 13, "polygon": [[107.25, 515.109375], [169.8837890625, 515.109375], [169.8837890625, 525.1640625], [107.25, 525.1640625]]}, {"title": "A.1 EQUIVALENCE OF DRO AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WEIGHTING IN THE CONVEX SETTING", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 539.25], [476.33203125, 539.25], [476.33203125, 549.140625], [107.1298828125, 549.140625]]}, {"title": "A.2 CONVERGENCE RATE OF ALGORITHM 1", "heading_level": null, "page_id": 14, "polygon": [[106.75634765625, 321.75], [303.609375, 321.75], [303.609375, 331.2********], [106.75634765625, 331.2********]]}, {"title": "B SUPPLEMENTARY EXPERIMENTS", "heading_level": null, "page_id": 16, "polygon": [[107.25, 360.0], [294.345703125, 360.0], [294.345703125, 371.25], [107.25, 371.25]]}, {"title": "C EXPERIMENTAL DETAILS", "heading_level": null, "page_id": 16, "polygon": [[107.25, 560.25], [255.0, 560.25], [255.0, 571.18359375], [107.25, 571.18359375]]}, {"title": "C.1 DATASETS", "heading_level": null, "page_id": 16, "polygon": [[106.5, 585.0], [178.5, 585.0], [178.5, 594.7734375], [106.5, 594.7734375]]}, {"title": "C.2 MODELS", "heading_level": null, "page_id": 17, "polygon": [[106.5, 537.75], [172.125, 537.75], [172.125, 548.3671875], [106.5, 548.3671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 56], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4951, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 364], ["Line", 78], ["Text", 6], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 895, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 582], ["Line", 60], ["TextInlineMath", 5], ["Text", 3], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 49], ["Text", 8], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 403], ["TableCell", 213], ["Line", 72], ["Text", 3], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 59], ["Text", 5], ["TextInlineMath", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 56], ["TableCell", 36], ["Reference", 4], ["TextInlineMath", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3965, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 695], ["TableCell", 63], ["Line", 56], ["Text", 4], ["TextInlineMath", 3], ["Reference", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7196, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 751], ["Line", 78], ["TextInlineMath", 6], ["Text", 4], ["Reference", 4], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 52], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 44], ["ListItem", 13], ["Reference", 13], ["SectionHeader", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 47], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 51], ["Reference", 13], ["ListItem", 12], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 699], ["Line", 93], ["Text", 9], ["Equation", 7], ["TextInlineMath", 6], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 713], ["Line", 130], ["Text", 6], ["Equation", 6], ["ListItem", 5], ["TextInlineMath", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2392, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 491], ["Line", 132], ["Text", 8], ["Equation", 4], ["SectionHeader", 3], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 970, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 52], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 35], ["Text", 7], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DISTRIBUTIONALLY ROBUST NEURAL NETWORKS FOR GROUP SHIFTS"}