{"table_of_contents": [{"title": "Preface", "heading_level": null, "page_id": 0, "polygon": [[129.0, 120.75], [184.5, 120.75], [184.5, 137.2412109375], [129.0, 137.2412109375]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[129.75, 215.25], [189.75, 216.0], [189.75, 226.388671875], [129.75, 226.388671875]]}, {"title": "Target audience", "heading_level": null, "page_id": 0, "polygon": [[129.75, 333.0], [205.5, 333.0], [205.5, 342.984375], [129.75, 342.984375]]}, {"title": "A probabilistic approach", "heading_level": null, "page_id": 0, "polygon": [[129.75, 426.75], [246.0, 426.75], [246.0, 436.95703125], [129.75, 436.95703125]]}, {"title": "A practical approach", "heading_level": null, "page_id": 1, "polygon": [[129.75, 160.6552734375], [228.75, 160.6552734375], [228.75, 171.17578125], [129.75, 171.17578125]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 1, "polygon": [[129.75, 444.75], [215.25, 444.75], [215.25, 454.9921875], [129.75, 454.9921875]]}, {"title": "Preface xxix", "heading_level": null, "page_id": 2, "polygon": [[130.5703125, 29.60009765625], [513.3438720703125, 29.60009765625], [513.3438720703125, 39.847412109375], [130.5703125, 39.847412109375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 4, "polygon": [[116.15625, 115.330078125], [223.3125, 115.330078125], [223.3125, 140.80078125], [116.15625, 140.80078125]]}, {"title": "1.1 Machine learning: what and why?", "heading_level": null, "page_id": 4, "polygon": [[102.75, 207.0], [305.4375, 207.0], [305.4375, 218.3203125], [102.75, 218.3203125]]}, {"title": "1.1.1 Types of machine learning", "heading_level": null, "page_id": 5, "polygon": [[96.75, 160.5], [256.921875, 160.5], [256.921875, 170.859375], [96.75, 170.859375]]}, {"title": "1.2 Supervised learning", "heading_level": null, "page_id": 6, "polygon": [[101.25, 231.75], [234.0, 231.75], [234.0, 243.158203125], [101.25, 243.158203125]]}, {"title": "1.2.1 Classification", "heading_level": null, "page_id": 6, "polygon": [[95.25, 292.5], [193.21875, 292.5], [193.21875, 303.43359375], [95.25, 303.43359375]]}, {"title": "1.2.1.1 Example", "heading_level": null, "page_id": 6, "polygon": [[90.5625, 492.75], [168.75, 492.75], [168.75, 502.76953125], [90.5625, 502.76953125]]}, {"title": "1.2.1.2 The need for probabilistic predictions", "heading_level": null, "page_id": 7, "polygon": [[89.25, 135.75], [293.25, 135.75], [293.25, 146.021484375], [89.25, 146.021484375]]}, {"title": "1.2.1.3 Real-world applications", "heading_level": null, "page_id": 8, "polygon": [[89.25, 309.0], [231.75, 309.0], [231.75, 318.462890625], [89.25, 318.462890625]]}, {"title": "Document classification and email spam filtering", "heading_level": null, "page_id": 8, "polygon": [[129.75, 377.25], [342.0, 377.25], [342.0, 386.6484375], [129.75, 386.6484375]]}, {"title": "Classifying flowers", "heading_level": null, "page_id": 9, "polygon": [[129.75, 480.0], [211.5, 480.0], [211.5, 490.4296875], [129.75, 490.4296875]]}, {"title": "Image classification and handwriting recognition", "heading_level": null, "page_id": 10, "polygon": [[129.75, 334.5], [342.0, 334.5], [342.0, 343.93359375], [129.75, 343.93359375]]}, {"title": "Face detection and recognition", "heading_level": null, "page_id": 11, "polygon": [[129.75, 287.25], [264.0, 287.25], [264.0, 296.630859375], [129.75, 296.630859375]]}, {"title": "1.2.2 Regression", "heading_level": null, "page_id": 11, "polygon": [[93.0, 511.5], [182.25, 511.5], [182.25, 521.75390625], [93.0, 521.75390625]]}, {"title": "1.3 Unsupervised learning", "heading_level": null, "page_id": 12, "polygon": [[101.25, 399.75], [247.5, 399.75], [247.5, 410.37890625], [101.25, 410.37890625]]}, {"title": "1.3.1 Discovering clusters", "heading_level": null, "page_id": 13, "polygon": [[95.25, 400.5], [225.421875, 400.5], [225.421875, 410.37890625], [95.25, 410.37890625]]}, {"title": "1.3.2 Discovering latent factors", "heading_level": null, "page_id": 14, "polygon": [[93.75, 438.0], [250.5, 438.0], [250.5, 448.03125], [93.75, 448.03125]]}, {"title": "1.3.3 Discovering graph structure", "heading_level": null, "page_id": 16, "polygon": [[93.75, 239.25], [262.5, 239.25], [262.5, 249.486328125], [93.75, 249.486328125]]}, {"title": "1.3.4 Matrix completion", "heading_level": null, "page_id": 17, "polygon": [[94.359375, 289.5], [218.671875, 289.5], [218.671875, 299.953125], [94.359375, 299.953125]]}, {"title": "1.3.4.1 Image inpainting", "heading_level": null, "page_id": 17, "polygon": [[90.0, 394.5], [204.46875, 394.5], [204.46875, 404.3671875], [90.0, 404.3671875]]}, {"title": "1.3.4.2 Collaborative filtering", "heading_level": null, "page_id": 17, "polygon": [[87.75, 522.0], [225.0, 522.0], [225.0, 532.1953125], [87.75, 532.1953125]]}, {"title": "1.3.4.3 Market basket analysis", "heading_level": null, "page_id": 18, "polygon": [[88.59375, 384.75], [229.5, 384.75], [229.5, 394.2421875], [88.59375, 394.2421875]]}, {"title": "1.4 Some basic concepts in machine learning", "heading_level": null, "page_id": 19, "polygon": [[101.25, 321.75], [346.5, 321.75], [346.5, 332.859375], [101.25, 332.859375]]}, {"title": "1.4.1 Parametric vs non-parametric models", "heading_level": null, "page_id": 19, "polygon": [[96.75, 395.25], [309.09375, 395.25], [309.09375, 405.6328125], [96.75, 405.6328125]]}, {"title": "1.4.2 A simple non-parametric classifier: K-nearest neighbors", "heading_level": null, "page_id": 19, "polygon": [[93.9375, 548.25], [399.75, 548.25], [399.75, 558.45703125], [93.9375, 558.45703125]]}, {"title": "1.4.3 The curse of dimensionality", "heading_level": null, "page_id": 21, "polygon": [[94.5, 447.0], [262.5, 447.0], [262.5, 456.890625], [94.5, 456.890625]]}, {"title": "1.4.4 Parametric models for classification and regression", "heading_level": null, "page_id": 22, "polygon": [[94.5, 340.5], [374.25, 340.5], [374.25, 350.89453125], [94.5, 350.89453125]]}, {"title": "1.4.5 Linear regression", "heading_level": null, "page_id": 22, "polygon": [[94.5, 445.5], [214.5, 445.5], [214.5, 455.625], [94.5, 455.625]]}, {"title": "1.4.6 Logistic regression", "heading_level": null, "page_id": 24, "polygon": [[93.0, 287.25], [219.75, 287.25], [219.75, 297.580078125], [93.0, 297.580078125]]}, {"title": "1.4.7 Overfitting", "heading_level": null, "page_id": 25, "polygon": [[94.5, 300.0], [181.5, 300.0], [181.5, 310.39453125], [94.5, 310.39453125]]}, {"title": "1.4.8 Model selection", "heading_level": null, "page_id": 25, "polygon": [[93.75, 500.25], [205.03125, 500.25], [205.03125, 510.36328125], [93.75, 510.36328125]]}, {"title": "1.4.9 No free lunch theorem", "heading_level": null, "page_id": 27, "polygon": [[93.75, 458.25], [237.75, 458.25], [237.75, 469.23046875], [93.75, 469.23046875]]}, {"title": "Exercises", "heading_level": null, "page_id": 28, "polygon": [[129.75, 138.0], [178.5, 138.0], [178.5, 148.552734375], [129.75, 148.552734375]]}, {"title": "Exercise 1.1 KNN classifier on shuffled MNIST data", "heading_level": null, "page_id": 28, "polygon": [[129.75, 159.0], [315.0, 159.0], [315.0, 168.328125], [129.75, 168.328125]]}, {"title": "Exercise 1.2 Approximate KNN classifiers", "heading_level": null, "page_id": 28, "polygon": [[129.75, 218.25], [280.5, 218.25], [280.5, 227.49609375], [129.75, 227.49609375]]}, {"title": "Exercise 1.3 CV for KNN", "heading_level": null, "page_id": 28, "polygon": [[129.65625, 267.75], [221.34375, 267.0], [221.34375, 276.5390625], [129.65625, 276.5390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 56], ["Line", 29], ["Text", 5], ["SectionHeader", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8552, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 42], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 11], ["Text", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 31], ["Text", 5], ["Footnote", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 39], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 50], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 44], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 731, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 22], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Picture", 1], ["Figure", 1], ["SectionHeader", 1], ["Text", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1319, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 799, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 608, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 45], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 60], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 50], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 766, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 29], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 27], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 624, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 40], ["TableCell", 27], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 32], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 731, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 79], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 51], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 875, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 47], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 46], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 724, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 54], ["Equation", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 863, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 38], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 60], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["Footnote", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 49], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 69], ["Line", 19], ["Text", 4], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-2"}