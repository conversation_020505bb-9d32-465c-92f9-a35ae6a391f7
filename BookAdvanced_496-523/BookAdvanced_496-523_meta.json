{"table_of_contents": [{"title": "478 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 1, "polygon": [[132.0, 89.25], [326.3203125, 89.25], [326.3203125, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "13.4.1 Example", "heading_level": null, "page_id": 1, "polygon": [[133.27734375, 392.25], [221.25, 392.25], [221.25, 403.34765625], [133.27734375, 403.34765625]]}, {"title": "13.4.2 Global Dimension Reduction for Nearest-Neighbors", "heading_level": null, "page_id": 2, "polygon": [[133.20263671875, 309.75], [437.25, 309.75], [437.25, 321.943359375], [133.20263671875, 321.943359375]]}, {"title": "480 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 3, "polygon": [[132.75, 89.25], [326.25, 89.25], [326.25, 98.75830078125], [132.75, 98.75830078125]]}, {"title": "13.5 Computational Considerations", "heading_level": null, "page_id": 3, "polygon": [[132.75, 314.25], [366.9609375, 314.25], [366.9609375, 327.55078125], [132.75, 327.55078125]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 4, "polygon": [[132.0, 177.0], [255.3486328125, 177.0], [255.3486328125, 190.072265625], [132.0, 190.072265625]]}, {"title": "Exercises", "heading_level": null, "page_id": 4, "polygon": [[132.0, 313.5], [190.0546875, 313.5], [190.0546875, 325.810546875], [132.0, 325.810546875]]}, {"title": "482 13. Prototypes and Nearest-Neighbors", "heading_level": null, "page_id": 5, "polygon": [[132.75, 88.5], [326.25, 88.5], [326.25, 98.7099609375], [132.75, 98.7099609375]]}, {"title": "14\nUnsupervised Learning", "heading_level": null, "page_id": 8, "polygon": [[132.0, 109.3447265625], [324.75, 109.3447265625], [324.75, 162.9052734375], [132.0, 162.9052734375]]}, {"title": "14.1 Introduction", "heading_level": null, "page_id": 8, "polygon": [[132.75, 354.75], [250.5673828125, 354.75], [250.5673828125, 367.576171875], [132.75, 367.576171875]]}, {"title": "14.2 Association Rules", "heading_level": null, "page_id": 10, "polygon": [[133.5, 302.25], [282.0, 302.25], [282.0, 314.982421875], [133.5, 314.982421875]]}, {"title": "488 14. Unsupervised Learning", "heading_level": null, "page_id": 11, "polygon": [[132.75, 89.25], [279.75, 89.25], [279.75, 98.806640625], [132.75, 98.806640625]]}, {"title": "14.2.1 Market Basket Analysis", "heading_level": null, "page_id": 11, "polygon": [[133.5, 305.25], [297.75, 305.25], [297.75, 316.72265625], [133.5, 316.72265625]]}, {"title": "14.2.2 The Apriori Algorithm", "heading_level": null, "page_id": 12, "polygon": [[133.5, 585.0], [294.0, 585.0], [294.0, 596.3203125], [133.5, 596.3203125]]}, {"title": "490 14. Unsupervised Learning", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [279.8525390625, 89.25], [279.8525390625, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "492 14. Unsupervised Learning", "heading_level": null, "page_id": 15, "polygon": [[132.0, 88.5], [279.75, 88.5], [279.75, 98.95166015625], [132.0, 98.95166015625]]}, {"title": "14.2.3 Example: Market Basket Analysis", "heading_level": null, "page_id": 15, "polygon": [[133.5, 293.25], [348.75, 293.25], [348.75, 304.541015625], [133.5, 304.541015625]]}, {"title": "14.2.4 Unsupervised as Supervised Learning", "heading_level": null, "page_id": 18, "polygon": [[133.5, 249.75], [365.765625, 249.75], [365.765625, 260.841796875], [133.5, 260.841796875]]}, {"title": "14.2.5 Generalized Association Rules", "heading_level": null, "page_id": 20, "polygon": [[133.5, 457.48828125], [330.75, 457.48828125], [330.75, 468.31640625], [133.5, 468.31640625]]}, {"title": "498 14. Unsupervised Learning", "heading_level": null, "page_id": 21, "polygon": [[132.75, 89.25], [279.75, 89.25], [279.75, 98.7099609375], [132.75, 98.7099609375]]}, {"title": "14.2.6 Choice of Supervised Learning Method", "heading_level": null, "page_id": 22, "polygon": [[133.5, 176.25], [373.833984375, 176.25], [373.833984375, 187.9453125], [133.5, 187.9453125]]}, {"title": "14.2.7 Example: Market Basket Analysis (Continued)", "heading_level": null, "page_id": 22, "polygon": [[133.5, 576.75], [414.75, 576.75], [414.75, 588.19921875], [133.5, 588.19921875]]}, {"title": "14.3 Cluster Analysis", "heading_level": null, "page_id": 24, "polygon": [[133.4267578125, 541.5], [273.75, 541.5], [273.75, 554.5546875], [133.4267578125, 554.5546875]]}, {"title": "14.3.1 Proximity Matrices", "heading_level": null, "page_id": 26, "polygon": [[133.5, 243.75], [275.51953125, 243.75], [275.51953125, 254.84765625], [133.5, 254.84765625]]}, {"title": "14.3.2 Dissimilarities Based on Attributes", "heading_level": null, "page_id": 26, "polygon": [[133.5, 495.75], [357.0, 495.75], [357.0, 506.6015625], [133.5, 506.6015625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 47], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 854], ["Line", 393], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1375, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 51], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 42], ["Text", 7], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 45], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 68], ["TextInlineMath", 4], ["Equation", 4], ["ListItem", 3], ["Text", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 28], ["ListItem", 7], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 26], ["SectionHeader", 2], ["TextInlineMath", 2], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 53], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 49], ["Equation", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 44], ["Text", 3], ["TextInlineMath", 3], ["ListItem", 2], ["Equation", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 42], ["Text", 7], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 41], ["Text", 7], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 56], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 856, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["TableCell", 124], ["Line", 46], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 48], ["Text", 6], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 958, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 1436], ["Line", 590], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1841, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 59], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 49], ["Text", 3], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 42], ["Text", 8], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 64], ["TableCell", 12], ["Text", 8], ["TextInlineMath", 8], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 50], ["Text", 7], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 158], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 908, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 44], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 64], ["Text", 7], ["Equation", 4], ["ListItem", 3], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_496-523"}