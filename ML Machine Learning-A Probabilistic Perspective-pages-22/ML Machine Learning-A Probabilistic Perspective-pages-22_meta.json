{"table_of_contents": [{"title": "18 State space models", "heading_level": null, "page_id": 0, "polygon": [[63.7734375, 94.5263671875], [265.5, 94.5263671875], [265.5, 143.2529296875], [62.25, 143.2529296875]]}, {"title": "18.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 217.845703125], [96.75, 217.845703125]]}, {"title": "18.2 Applications of SSMs", "heading_level": null, "page_id": 1, "polygon": [[94.5, 359.25], [238.5, 359.25], [238.5, 370.51171875], [94.5, 370.51171875]]}, {"title": "18.2.1 SSMs for object tracking", "heading_level": null, "page_id": 1, "polygon": [[90.0, 432.0], [246.0, 432.0], [246.0, 442.01953125], [90.0, 442.01953125]]}, {"title": "18.2.2 Robotic SLAM", "heading_level": null, "page_id": 2, "polygon": [[87.75, 556.5], [196.59375, 556.5], [196.59375, 566.3671875], [87.75, 566.3671875]]}, {"title": "18.2.3 Online parameter learning using recursive least squares", "heading_level": null, "page_id": 5, "polygon": [[87.609375, 311.25], [395.25, 311.25], [395.25, 322.1015625], [87.609375, 322.1015625]]}, {"title": "18.2.4 SSM for time series forecasting *", "heading_level": null, "page_id": 6, "polygon": [[88.3828125, 209.25], [285.0, 209.25], [285.0, 219.427734375], [88.3828125, 219.427734375]]}, {"title": "18.2.4.1 Local level model", "heading_level": null, "page_id": 6, "polygon": [[84.375, 409.5], [207.0, 409.5], [207.0, 418.60546875], [84.375, 418.60546875]]}, {"title": "18.2.4.2 Local linear trend", "heading_level": null, "page_id": 7, "polygon": [[83.25, 430.5], [208.125, 430.5], [208.125, 439.8046875], [83.25, 439.8046875]]}, {"title": "18.2.4.3 Seasonality", "heading_level": null, "page_id": 8, "polygon": [[84.0, 345.0], [180.75, 345.0], [180.75, 355.0078125], [84.0, 355.0078125]]}, {"title": "18.2.4.4 ARMA models *", "heading_level": null, "page_id": 8, "polygon": [[83.53125, 492.0], [198.421875, 492.0], [198.421875, 501.8203125], [83.53125, 501.8203125]]}, {"title": "18.3 Inference in LG-SSM", "heading_level": null, "page_id": 9, "polygon": [[95.25, 412.5], [236.109375, 412.5], [236.109375, 423.66796875], [95.25, 423.66796875]]}, {"title": "18.3.1 The <PERSON><PERSON> filtering algorithm", "heading_level": null, "page_id": 9, "polygon": [[90.0, 486.0], [276.75, 486.0], [276.75, 496.125], [90.0, 496.125]]}, {"title": "18.3.1.1 Prediction step", "heading_level": null, "page_id": 10, "polygon": [[85.359375, 61.5], [196.5, 61.5], [196.5, 71.7451171875], [85.359375, 71.7451171875]]}, {"title": "18.3.1.2 Measurement step", "heading_level": null, "page_id": 10, "polygon": [[84.75, 185.25], [210.0, 185.25], [210.0, 195.22265625], [84.75, 195.22265625]]}, {"title": "18.3.1.3 Marginal likelihood", "heading_level": null, "page_id": 11, "polygon": [[84.7265625, 159.75], [215.4375, 159.75], [215.4375, 169.435546875], [84.7265625, 169.435546875]]}, {"title": "18.3.1.4 Posterior predictive", "heading_level": null, "page_id": 11, "polygon": [[84.75, 272.25], [215.25, 272.25], [215.25, 281.443359375], [84.75, 281.443359375]]}, {"title": "18.3.1.5 Computational issues", "heading_level": null, "page_id": 11, "polygon": [[84.75, 381.0], [222.75, 381.0], [222.75, 390.4453125], [84.75, 390.4453125]]}, {"title": "18.3.1.6 Derivation *", "heading_level": null, "page_id": 12, "polygon": [[83.53125, 61.5], [183.0, 61.5], [183.0, 71.46826171875], [83.53125, 71.46826171875]]}, {"title": "18.3.2 The <PERSON><PERSON> smoothing algorithm", "heading_level": null, "page_id": 12, "polygon": [[87.75, 480.75], [290.25, 480.75], [290.25, 490.74609375], [87.75, 490.74609375]]}, {"title": "18.3.2.1 Algorithm", "heading_level": null, "page_id": 13, "polygon": [[84.75, 135.75], [173.25, 135.75], [173.25, 146.021484375], [84.75, 146.021484375]]}, {"title": "18.3.2.2 Derivation *", "heading_level": null, "page_id": 13, "polygon": [[82.7578125, 365.25], [182.53125, 365.25], [182.53125, 374.94140625], [82.7578125, 374.94140625]]}, {"title": "18.3.2.3 Comparison to the forwards-backwards algorithm for HMMs *", "heading_level": null, "page_id": 14, "polygon": [[83.25, 362.25], [397.5, 362.25], [397.5, 372.09375], [83.25, 372.09375]]}, {"title": "18.4 Learning for LG-SSM", "heading_level": null, "page_id": 15, "polygon": [[95.0625, 221.958984375], [239.625, 221.958984375], [239.625, 232.400390625], [95.0625, 232.400390625]]}, {"title": "18.4.1 Identifiability and numerical stability", "heading_level": null, "page_id": 15, "polygon": [[89.296875, 390.75], [307.5, 390.75], [307.5, 400.88671875], [89.296875, 400.88671875]]}, {"title": "18.4.2 Training with fully observed data", "heading_level": null, "page_id": 16, "polygon": [[88.5, 100.5], [288.0, 100.5], [288.0, 110.583984375], [88.5, 110.583984375]]}, {"title": "18.4.3 EM for LG-SSM", "heading_level": null, "page_id": 16, "polygon": [[88.8046875, 205.5], [201.75, 205.5], [201.75, 215.7890625], [88.8046875, 215.7890625]]}, {"title": "18.4.4 Subspace methods", "heading_level": null, "page_id": 16, "polygon": [[88.734375, 286.5], [217.546875, 286.5], [217.546875, 296.630859375], [88.734375, 296.630859375]]}, {"title": "18.4.5 Bayesian methods for \"fitting\" LG-SSMs", "heading_level": null, "page_id": 16, "polygon": [[88.734375, 427.5], [317.25, 427.5], [317.25, 437.90625], [88.734375, 437.90625]]}, {"title": "18.5 Approximate online inference for non-linear, non-Gaussian SSMs", "heading_level": null, "page_id": 16, "polygon": [[95.25, 534.0], [466.5, 534.0], [466.5, 545.16796875], [95.25, 545.16796875]]}, {"title": "18.5.1 Extended Kalman filter (EKF)", "heading_level": null, "page_id": 17, "polygon": [[89.71875, 207.75], [267.1875, 207.75], [267.1875, 217.6875], [89.71875, 217.6875]]}, {"title": "18.5.2 Unscented Kalman filter (UKF)", "heading_level": null, "page_id": 19, "polygon": [[87.75, 494.25], [273.0, 494.25], [273.0, 504.984375], [87.75, 504.984375]]}, {"title": "18.5.2.1 The unscented transform", "heading_level": null, "page_id": 20, "polygon": [[84.09375, 171.0], [238.5, 171.0], [238.5, 180.984375], [84.09375, 180.984375]]}, {"title": "18.5.2.2 The UKF algorithm", "heading_level": null, "page_id": 20, "polygon": [[82.828125, 551.25], [211.640625, 551.25], [211.640625, 561.3046875], [82.828125, 561.3046875]]}, {"title": "18.5.3 Assumed density filtering (ADF)", "heading_level": null, "page_id": 21, "polygon": [[87.75, 480.75], [280.828125, 480.75], [280.828125, 491.6953125], [87.75, 491.6953125]]}, {"title": "18.5.3.1 <PERSON><PERSON><PERSON><PERSON> algorithm for online inference in DBNs", "heading_level": null, "page_id": 22, "polygon": [[84.75, 492.75], [357.0, 492.75], [357.0, 503.0859375], [84.75, 503.0859375]]}, {"title": "18.5.3.2 Gaussian approximation for online inference in GLMs", "heading_level": null, "page_id": 23, "polygon": [[83.25, 135.75], [362.25, 135.75], [362.25, 145.86328125], [83.25, 145.86328125]]}, {"title": "18.6 Hybrid discrete/continuous SSMs", "heading_level": null, "page_id": 24, "polygon": [[94.5, 384.75], [300.75, 384.75], [300.75, 395.19140625], [94.5, 395.19140625]]}, {"title": "18.6.1 Inference", "heading_level": null, "page_id": 25, "polygon": [[89.25, 288.75], [174.75, 288.75], [174.75, 298.6875], [89.25, 298.6875]]}, {"title": "18.6.1.1 A Gaussian sum filter for switching SSMs", "heading_level": null, "page_id": 25, "polygon": [[85.5, 560.25], [307.5, 560.25], [307.5, 569.53125], [85.5, 569.53125]]}, {"title": "18.6.2 Application: data association and multi-target tracking", "heading_level": null, "page_id": 27, "polygon": [[87.75, 440.25], [389.25, 440.25], [389.25, 450.5625], [87.75, 450.5625]]}, {"title": "18.6.3 Application: fault diagnosis", "heading_level": null, "page_id": 28, "polygon": [[87.75, 411.0], [259.734375, 411.0], [259.734375, 421.13671875], [87.75, 421.13671875]]}, {"title": "18.6.4 Application: econometric forecasting", "heading_level": null, "page_id": 29, "polygon": [[87.75, 432.0], [305.25, 432.0], [305.25, 442.01953125], [87.75, 442.01953125]]}, {"title": "Exercises", "heading_level": null, "page_id": 29, "polygon": [[129.375, 514.5], [178.59375, 514.5], [178.59375, 524.91796875], [129.375, 524.91796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 27], ["Text", 6], ["Equation", 6], ["ListItem", 4], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9283, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 57], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 857, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 88], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9263, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1369, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 46], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 403], ["Line", 45], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 975, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 42], ["TextInlineMath", 4], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 459], ["Line", 66], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1829, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 54], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 912, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 775, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 632], ["Line", 49], ["Equation", 15], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 39], ["TextInlineMath", 4], ["Text", 4], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 584], ["Line", 66], ["Equation", 12], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 49], ["Equation", 8], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 827], ["Line", 83], ["Equation", 15], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 42], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 36], ["Text", 5], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 45], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 44], ["Equation", 8], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 860, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 508], ["Line", 53], ["TextInlineMath", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 710], ["Line", 85], ["Equation", 13], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 364], ["Line", 48], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 947, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 53], ["Equation", 10], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 45], ["Equation", 8], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 31], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 437], ["Line", 88], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["TextInlineMath", 2], ["Caption", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 54], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 859, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 72], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 883, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-22"}