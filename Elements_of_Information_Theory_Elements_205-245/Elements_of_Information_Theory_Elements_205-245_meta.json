{"table_of_contents": [{"title": "Channel Capacity", "heading_level": null, "page_id": 0, "polygon": [[66.75, 106.9453125], [268.734375, 106.9453125], [268.734375, 135.421875], [66.75, 135.421875]]}, {"title": "8.1 EXAMPLES OF CHANNEL CAPACITY", "heading_level": null, "page_id": 1, "polygon": [[65.25, 544.5], [276.0, 544.5], [276.0, 555.609375], [65.25, 555.609375]]}, {"title": "8.1.1 Noiseless Binary Channel", "heading_level": null, "page_id": 1, "polygon": [[66.0, 568.5], [231.0, 568.5], [231.0, 579.33984375], [66.0, 579.33984375]]}, {"title": "8.1.2 Noisy Channel with Nonoverlapping Outputs", "heading_level": null, "page_id": 2, "polygon": [[63.0, 205.5], [331.8046875, 205.5], [331.8046875, 215.947265625], [63.0, 215.947265625]]}, {"title": "8.1.3 Noisy Typewriter", "heading_level": null, "page_id": 2, "polygon": [[63.0, 340.453125], [185.25, 340.453125], [185.25, 350.578125], [63.0, 350.578125]]}, {"title": "8.1.4 Binary Symmetric Channel", "heading_level": null, "page_id": 3, "polygon": [[63.0, 457.5], [238.11328125, 458.47265625], [238.11328125, 469.23046875], [63.0, 469.23046875]]}, {"title": "8.1.5 Binary Erasure Channel", "heading_level": null, "page_id": 4, "polygon": [[64.5, 438.0], [224.25, 438.0], [224.25, 448.34765625], [64.5, 448.34765625]]}, {"title": "8.2 SYMMETRIC CHANNELS", "heading_level": null, "page_id": 6, "polygon": [[64.5, 201.0], [216.75, 201.0], [216.75, 211.9921875], [64.5, 211.9921875]]}, {"title": "8.3 PROPERTIES OF CHANNEL CAPACITY", "heading_level": null, "page_id": 7, "polygon": [[62.25, 556.5], [282.75, 556.5], [282.75, 566.68359375], [62.25, 566.68359375]]}, {"title": "8.4 PREVIEW OF THE CHANNEL CODING THEOREM", "heading_level": null, "page_id": 8, "polygon": [[64.5, 361.5], [341.25, 361.5], [341.25, 372.41015625], [64.5, 372.41015625]]}, {"title": "8.5 DEFINITIONS", "heading_level": null, "page_id": 9, "polygon": [[64.5, 348.0], [159.75, 348.0], [159.75, 358.48828125], [64.5, 358.48828125]]}, {"title": "Definition (Probability of error): Let", "heading_level": null, "page_id": 10, "polygon": [[63.0, 447.71484375], [243.75, 447.71484375], [243.75, 457.83984375], [63.0, 457.83984375]]}, {"title": "8.6 JOINTLY TYPICAL SEQUENCES", "heading_level": null, "page_id": 11, "polygon": [[63.75, 524.25], [247.5, 525.75], [247.5, 536.30859375], [63.75, 536.30859375]]}, {"title": "8.7 THE CHANNEL CODING THEOREM", "heading_level": null, "page_id": 15, "polygon": [[63.0, 59.25], [272.84765625, 59.25], [272.84765625, 70.43994140625], [63.0, 70.43994140625]]}, {"title": "8.8 ZERO-ERROR CODES", "heading_level": null, "page_id": 20, "polygon": [[63.0, 320.25], [196.751953125, 320.25], [196.751953125, 330.328125], [63.0, 330.328125]]}, {"title": "8.9 <PERSON>N<PERSON>'S INEQUALITY AND THE CONVERSE TO THE \nCODING THEOREM", "heading_level": null, "page_id": 21, "polygon": [[62.25, 171.0], [355.5, 171.0], [355.5, 194.58984375], [62.25, 194.58984375]]}, {"title": "8.10 EQUALITY IN THE CONVERSE TO THE CHANNEL CODING \n0.10 E_{\\text{N}}", "heading_level": null, "page_id": 24, "polygon": [[64.8984375, 407.91688537597656], [398.98828125, 407.91688537597656], [398.98828125, 436.95703125], [64.8984375, 436.95703125]]}, {"title": "8.11 HAMMING CODES", "heading_level": null, "page_id": 26, "polygon": [[63.75, 229.5], [191.3818359375, 229.5], [191.3818359375, 240.626953125], [63.75, 240.626953125]]}, {"title": "8.12 FEEDBACK CAPACITY", "heading_level": null, "page_id": 29, "polygon": [[63.0, 534.75], [206.349609375, 534.75], [206.349609375, 546.1171875], [63.0, 546.1171875]]}, {"title": "8.13 THE JOINT SOURCE CHANNEL CODING THEOREM", "heading_level": null, "page_id": 32, "polygon": [[63.75, 57.0], [357.75, 57.0], [357.75, 67.19677734375], [63.75, 67.19677734375]]}, {"title": "Proof:", "heading_level": null, "page_id": 33, "polygon": [[74.25, 440.25], [108.7734375, 440.25], [108.7734375, 450.5625], [74.25, 450.5625]]}, {"title": "SUMMARY OF CHAPTER 8", "heading_level": null, "page_id": 35, "polygon": [[164.25, 492.75], [297.0, 492.75], [297.0, 503.0859375], [164.25, 503.0859375]]}, {"title": "Examples:", "heading_level": null, "page_id": 35, "polygon": [[68.25, 537.0], [119.25, 537.0], [119.25, 548.015625], [68.25, 548.015625]]}, {"title": "PROBLEMS FOR CHAPTER 8", "heading_level": null, "page_id": 37, "polygon": [[63.0, 60.5126953125], [209.548828125, 60.5126953125], [209.548828125, 70.0048828125], [63.0, 70.0048828125]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 39, "polygon": [[64.5, 480.75], [171.75, 482.25], [171.75, 492.64453125], [64.5, 492.64453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 39], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 33], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1228, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 34], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 32], ["Equation", 9], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1273, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 63], ["Line", 23], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 32], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 38], ["Text", 11], ["Equation", 4], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1558, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 39], ["ListItem", 6], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1897, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 29], ["Text", 6], ["Equation", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 34], ["Text", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 31], ["Equation", 10], ["Text", 4], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 25], ["Equation", 10], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 29], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 42], ["Text", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 37], ["ListItem", 6], ["Text", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 991, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 31], ["Text", 7], ["ListItem", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 34], ["Equation", 9], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 37], ["Equation", 4], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 38], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 32], ["Equation", 8], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 28], ["Equation", 9], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 31], ["Equation", 9], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 28], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 599, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 37], ["Equation", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 41], ["TableCell", 36], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2162, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 44], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 39], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 632, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 27], ["Equation", 11], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 45], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 37], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1600, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 34], ["Equation", 11], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 31], ["Equation", 4], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 32], ["TextInlineMath", 9], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 40], ["ListItem", 9], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 41], ["ListItem", 11], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 40], ["Text", 6], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_205-245"}