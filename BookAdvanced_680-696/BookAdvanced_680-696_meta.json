{"table_of_contents": [{"title": "18.4 Linear Classifiers with L_1 Regularization", "heading_level": null, "page_id": 0, "polygon": [[132.75, 111.0], [424.037109375, 111.0], [424.037109375, 124.13671875], [132.75, 124.13671875]]}, {"title": "662 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 1, "polygon": [[131.85791015625, 88.5], [332.25, 88.5], [332.25, 98.25], [131.85791015625, 98.25]]}, {"title": "18.4.1 Application of Lasso to Protein Mass Spectroscopy", "heading_level": null, "page_id": 3, "polygon": [[133.20263671875, 357.0], [435.75, 357.0], [435.75, 369.123046875], [133.20263671875, 369.123046875]]}, {"title": "18.4.2 The Fused <PERSON> for Functional Data", "heading_level": null, "page_id": 5, "polygon": [[133.5, 313.5], [368.15625, 313.5], [368.15625, 325.423828125], [133.5, 325.423828125]]}, {"title": "18.5 Classification When Features are Unavailable", "heading_level": null, "page_id": 7, "polygon": [[132.75, 191.1357421875], [451.5, 191.1357421875], [451.5, 204.380859375], [132.75, 204.380859375]]}, {"title": "18.5.1 Example: String Kernels and Protein Classification", "heading_level": null, "page_id": 7, "polygon": [[133.5, 405.75], [439.5, 405.75], [439.5, 417.65625], [133.5, 417.65625]]}, {"title": "18.5.2 Classification and Other Models Using Inner-Product\nKernels and Pairwise Distances", "heading_level": null, "page_id": 9, "polygon": [[133.5, 423.0], [449.25, 423.0], [449.25, 449.3671875], [133.5, 449.3671875]]}, {"title": "18.5.3 Example: Abstracts Classification", "heading_level": null, "page_id": 11, "polygon": [[133.5, 405.75], [347.25, 405.75], [347.25, 416.8828125], [133.5, 416.8828125]]}, {"title": "674 18. High-Dimensional Problems: p \\gg N", "heading_level": null, "page_id": 13, "polygon": [[132.0, 89.25], [333.791015625, 89.25], [333.791015625, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "18.6 High-Dimensional Regression: Supervised\nPrincipal Components", "heading_level": null, "page_id": 13, "polygon": [[133.1279296875, 286.171875], [429.1171875, 286.171875], [429.1171875, 314.7890625], [133.1279296875, 314.7890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 71], ["Text", 6], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2542, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 49], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 766, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 72], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 923, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 44], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 39], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 55], ["TableCell", 24], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1663, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 53], ["Equation", 2], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 37], ["Text", 9], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 40], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 56], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 48], ["Text", 6], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 40], ["TableCell", 24], ["ListItem", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 46], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 723, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 41], ["Text", 6], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 35], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1419, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 40], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 641, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 67], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 996, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_680-696"}