## 18.4 Linear Classifiers with $L_1$ Regularization

The methods of Section 18.3 use an  $L_2$  penalty to regularize their parameters, just as in ridge regression. All of the estimated coefficients are nonzero, and hence no feature selection is performed. In this section we discuss methods that use  $L_1$  penalties instead, and hence provide automatic feature selection.

Recall the lasso of Section 3.4.2,

$$
\min_{\beta} \frac{1}{2} \sum_{i=1}^{N} \left( y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j \right)^2 + \lambda \sum_{j=1}^{p} |\beta_j|, \tag{18.18}
$$

which we have written in the Lagrange form (3.52). As discussed there, the use of the  $L_1$  penalty causes a subset of the solution coefficients  $\hat{\beta}_j$  to be exactly zero, for a sufficiently large value of the tuning parameter  $\lambda$ .

In Section 3.8.1 we discussed the LARS algorithm, an efficient procedure for computing the lasso solution for all  $\lambda$ . When  $p > N$  (as in this chapter), as  $\lambda$  approaches zero, the lasso fits the training data exactly. In fact, by convex duality one can show that when  $p > N$  the number of non-zero coefficients is at most N for all values of  $\lambda$  (<PERSON><PERSON> and <PERSON>, 2007, for example). Thus the lasso provides a (severe) form of feature selection.

Lasso regression can be applied to a two-class classification problem by coding the outcome  $\pm 1$ , and applying a cutoff (usually 0) to the predictions. For more than two classes, there are many possible approaches, including the ova and ovo methods discussed in Section 18.3.3. We tried the ovaapproach on the cancer data in Section 18.3. The results are shown in line (4) of Table 18.1. Its performance is among the best.

A more natural approach for classification problems is to use the lasso penalty to regularize logistic regression. Several implementations have been proposed in the literature, including path algorithms similar to LARS (Park and Hastie, 2007). Because the paths are piecewise smooth but nonlinear, exact methods are slower than the LARS algorithm, and are less feasible when  $p$  is large.

Friedman et al. (2010) provide very fast algorithms for fitting  $L_1$ -penalized logistic and multinomial regression models. They use the symmetric multinomial logistic regression model as in (18.10) in Section 18.3.2, and maximize the penalized log-likelihood

$$
\max_{\{\beta_{0k}, \beta_k \in \mathbb{R}^p\}_1^K} \left[ \sum_{i=1}^N \log \Pr(g_i | x_i) - \lambda \sum_{k=1}^K \sum_{j=1}^p |\beta_{kj}| \right];
$$
\n(18.19)

compare with (18.11). Their algorithm computes the exact solution at a pre-chosen sequence of values for  $\lambda$  by cyclical coordinate descent (Section 3.8.6), and exploits the fact that solutions are sparse when  $p \gg N$ ,

# 662 18. High-Dimensional Problems: $p \gg N$

as well as the fact that solutions for neighboring values of  $\lambda$  tend to be very similar. This method was used in line (7) of Table 18.1, with the overall tuning parameter  $\lambda$  chosen by cross-validation. The performance was similar to that of the best methods, except here the automatic feature selection chose 269 genes altogether. A similar approach is used in Genkin et al. (2007); although they present their model from a Bayesian point of view, they in fact compute the posterior mode, which solves the penalized maximum-likelihood problem.

Image /page/1/Figure/2 description: This image contains two plots side-by-side, both titled with the name of a regularization technique: 'Lasso' on the left and 'Elastic Net' on the right. Both plots have 'Coefficients' on the y-axis, ranging from 0.0 to 2.0, and 'log(λ)' on the x-axis, ranging from -8 to -1. Each plot displays multiple colored lines representing the coefficients of different features as the regularization parameter (λ) changes. In the Lasso plot, some coefficients become exactly zero as λ increases, which is characteristic of the Lasso method. The Elastic Net plot shows a similar trend, but the coefficients tend to shrink towards zero rather than becoming exactly zero as quickly.

FIGURE 18.5. Regularized logistic regression paths for the leukemia data. The left panel is the lasso path, the right panel the elastic-net path with  $\alpha = 0.8$ . At the ends of the path (extreme left), there are 19 nonzero coefficients for the lasso, and 39 for the elastic net. The averaging effect of the elastic net results in more non-zero coefficients than the lasso, but with smaller magnitudes.

In genomic applications, there are often strong correlations among the variables; genes tend to operate in molecular pathways. The lasso penalty is somewhat indifferent to the choice among a set of strong but correlated variables (Exercise 3.28). The ridge penalty, on the other hand, tends to shrink the coefficients of correlated variables toward each other (Exercise 3.29 on page 99). The elastic net penalty (Zou and Hastie, 2005) is a compromise, and has the form

$$
\sum_{j=1}^{p} (\alpha |\beta_j| + (1 - \alpha)\beta_j^2).
$$
 (18.20)

The second term encourages highly correlated features to be averaged, while the first term encourages a sparse solution in the coefficients of these aver-

aged features. The elastic net penalty can be used with any linear model, in particular for regression or classification.

Hence the multinomial problem above with elastic-net penalty becomes

$$
\max_{\{\beta_{0k}, \beta_k \in \mathbb{R}^p\}_1^K} \left[ \sum_{i=1}^N \log \Pr(g_i | x_i) - \lambda \sum_{k=1}^K \sum_{j=1}^p \left( \alpha |\beta_{kj}| + (1 - \alpha) \beta_{kj}^2 \right) \right].
$$
\n(18.21)

The parameter  $\alpha$  determines the mix of the penalties, and is often prechosen on qualitative grounds. The elastic net can yield more that  $N$  nonzero coefficients when  $p > N$ , a potential advantage over the lasso. Line (8) in Table 18.1 uses this model, with  $\alpha$  and  $\lambda$  chosen by cross-validation. We used a sequence of 20 values of  $\alpha$  between 0.05 and 1.0, and a 100 values of  $\lambda$  uniform on the log scale covering the entire range. Values of  $\alpha \in [0.75, 0.80]$  gave the minimum CV error, with values of  $\lambda < 0.001$  for all tied solutions. Although it has the lowest test error among all methods, the margin is small and not significant. Interestingly, when CV is performed separately for each value of  $\alpha$ , a minimum test error of 8.8 is achieved at  $\alpha = 0.10$ , but this is not the value chosen in the two-dimensional CV.

Image /page/2/Figure/5 description: The image displays two plots side-by-side, both with the x-axis labeled as "log(λ)" ranging from -8 to -1. The left plot, titled "Misclassification Error", has a y-axis ranging from 0.0 to 0.4. It shows three lines representing "Training" (orange), "Test" (cyan), and "10-fold CV" (purple). The "Training" line is flat at 0.0 for most of the range and then increases sharply. The "Test" line starts at approximately 0.05, remains flat until log(λ) is around -4, and then increases sharply. The "10-fold CV" line starts at approximately 0.05, remains flat until log(λ) is around -3, and then increases sharply. The right plot, titled "Deviance", has a y-axis ranging from 0 to 30. The "Training" line (orange) starts at approximately 0.0, increases slowly, and then sharply increases after log(λ) is around -2. The "Test" line (cyan) starts at approximately 14, increases slowly, and then sharply increases after log(λ) is around -2. The "10-fold CV" line (purple) starts at approximately 0.0, increases slowly, and then increases more rapidly after log(λ) is around -2.

FIGURE 18.6. Training, test, and 10-fold cross validation curves for lasso logistic regression on the leukemia data. The left panel shows misclassification errors, the right panel shows deviance.

Figure 18.5 shows the lasso and elastic-net coefficient paths on the twoclass leukemia data (Golub et al., 1999). There are 7129 gene-expression measurements on 38 samples, 27 of them in class ALL (acute lymphocytic leukemia), and 11 in class AML (acute myelogenous leukemia). There is also a test set with 34 samples (20, 14). Since the data are linearly separable, the solution is undefined at  $\lambda = 0$  (Exercise 18.11), and degrades for very small values of λ. Hence the paths have been truncated as the fitted probabilities approach 0 and 1. There are 19 non-zero coefficients in the left plot, and 39 in the right. Figure 18.6 (left panel) shows the misclassification errors for the lasso logistic regression on the training and test data, as well as for 10-fold cross-validation on the training data. The right panel uses binomial deviance to measure errors, and is much smoother. The small sample sizes lead to considerable sampling variance in these curves, even though individual curves are relatively smooth (see, for example, Figure 7.1 on page 220). Both of these plots suggest that the limiting solution  $\lambda \downarrow 0$  is adequate, leading to 3/34 misclassifications in the test set. The corresponding figures for the elastic net are qualitatively similar and are not shown.

For  $p \gg N$ , the limiting coefficients diverge for all regularized logistic regression models, so in practical software implementations a minimum value for  $\lambda > 0$  is either explicitly or implicitly set. However, renormalized versions of the coefficients converge, and these limiting solutions can be thought of as interesting alternatives to the linear optimal separating hyperplane (SVM). With  $\alpha = 0$  the limiting solution coincides with the SVM (see end of Section 18.3.2), but all the 7129 genes are selected. With  $\alpha = 1$ , the limiting solution coincides with an  $L_1$  separating hyperplane (Rosset et al., 2004a), and includes at most 38 genes. As  $\alpha$  decreases from 1, the elastic-net solutions include more genes in the separating hyperplane.

### 18.4.1 Application of Lasso to Protein Mass Spectroscopy

Protein mass spectrometry has become a popular technology for analyzing the proteins in blood, and can be used to diagnose a disease or understand the processes underlying it.

For each blood serum sample i, we observe the intensity  $x_{ij}$  for many time of flight values  $t_j$ . This intensity is related to the number of particles observed to take approximately  $t_i$  time to pass from the emitter to the detector during a cycle of operation of the machine. The time of flight has a known relationship to the mass over charge ratio  $(m/z)$  of the constituent proteins in the blood. Hence the identification of a peak in the spectrum at a certain  $t_i$  tells us that there is a protein with a corresponding mass and charge. The identity of this protein can then be determined by other means.

Figure 18.7 shows an example taken from Adam et al. (2003). It shows the average spectra for healthy patients and those with prostate cancer. There are  $16,898$  m/z sites in total, ranging in value from 2000 to 40,000. The full dataset consists of 157 healthy patients and 167 with cancer, and the goal is to find  $m/z$  sites that discriminate between the two groups. This is an example of functional data; the predictors can be viewed as a function of  $m/z$ . There has been much interest in this problem in the past few years; see e.g. Petricoin et al. (2002).

The data were first standardized (baseline subtraction and normalization), and we restricted attention to  $m/z$  values between 2000 and 40,000 (spectra outside of this range were not of interest). We then applied near-

Image /page/4/Figure/1 description: This is a line graph comparing the intensity of mass spectrometry data for normal and cancer samples. The x-axis represents m/z values ranging from 2e+03 to 2e+05, and the y-axis represents intensity values from 0 to 40. Two lines are plotted: a green line labeled 'Normal' and a red line labeled 'Cancer'. Both lines show a similar trend of decreasing intensity with increasing m/z, with several prominent peaks in the lower m/z range (around 1e+04). The cancer line appears to have slightly higher intensity peaks in certain regions compared to the normal line.

FIGURE 18.7. Protein mass spectrometry data: average profiles from normal and prostate cancer patients.

est shrunken centroids and lasso regression to the data, with the results for both methods shown in Table 18.2.

By fitting harder to the data, the lasso achieves a considerably lower test error rate. However, it may not provide a scientifically useful solution. Ideally, protein mass spectrometry resolves a biological sample into its constituent proteins, and these should appear as peaks in the spectra. The lasso doesn't treat peaks in any special way, so not surprisingly only some of the non-zero lasso weights were situated near peaks in the spectra. Furthermore, the same protein may yield a peak at slightly different  $m/z$ values in different spectra. In order to identify common peaks, some kind of  $m/z$  warping is needed from sample to sample.

To address this, we applied a standard peak-extraction algorithm to each spectrum, yielding a total of 5178 peaks in the 217 training spectra. Our idea was to pool the collection of peaks from all patients, and hence construct a set of common peaks. For this purpose, we applied hierarchical clustering to the positions of these peaks along the log  $m/z$  axis. We cut the resulting dendrogram horizontally at height  $log(0.005)^3$ , and computed averages of the peak positions in each resulting cluster. This process yielded 728 common clusters and their corresponding peak centers.

Given these 728 common peaks, we determined which of these were present in each individual spectrum, and if present, the height of the peak. A peak height of zero was assigned if that peak was not found. This produced a  $217 \times 728$  matrix of peak heights as features, which was used in a lasso regression. We scored the test spectra for the same 728 peaks.

<sup>3</sup>Use of the value 0.005 means that peaks with positions less than 0.5% apart are considered the same peak, a fairly common assumption.

TABLE 18.2. Results for the prostate data example. The standard deviation for the test errors is about 4.5.

| Method                        | Test Errors/108 | Number of Sites |
|-------------------------------|-----------------|-----------------|
| 1. Nearest shrunken centroids | 34              | 459             |
| 2. Lasso                      | 22              | 113             |
| 3. Lasso on peaks             | 28              | 35              |

The prediction results for this application of the lasso to the peaks are shown in the last line of Table 18.2: it does fairly well, but not as well as the lasso on the raw spectra. However, the fitted model may be more useful to the biologist as it yields 35 peak positions for further study. On the other hand, the results suggest that there may be useful discriminatory information between the peaks of the spectra, and the positions of the lasso sites from line (2) of the table also deserve further examination.

### 18.4.2 The Fused Lasso for Functional Data

In the previous example, the features had a natural order, determined by the mass-to-charge ratio  $m/z$ . More generally, we may have functional features  $x_i(t)$  that are ordered according to some index variable t. We have already discussed several approaches for exploiting such structure.

We can represent  $x_i(t)$  by their coefficients in a basis of functions in t, such as splines, wavelets or Fourier bases, and then apply a regression using these coefficients as predictors. Equivalently, one can instead represent the coefficients of the original features in these bases. These approaches are described in Section 5.3.

In the classification setting, we discuss the analogous approach of penalized discriminant analysis in Section 12.6. This uses a penalty that explicitly controls the resulting smoothness of the coefficient vector.

The above methods tend to smooth the coefficients uniformly. Here we present a more adaptive strategy that modifies the lasso penalty to take into account the ordering of the features. The fused lasso (Tibshirani et al., 2005) solves

$$
\min_{\beta \in \mathbb{R}^p} \left\{ \sum_{i=1}^N (y_i - \beta_0 - \sum_{j=1}^p x_{ij} \beta_j)^2 + \lambda_1 \sum_{j=1}^p |\beta_j| + \lambda_2 \sum_{j=1}^{p-1} |\beta_{j+1} - \beta_j| \right\}.
$$
 (18.22)

This criterion is strictly convex in  $\beta$ , so a unique solution exists. The first penalty encourages the solution to be sparse, while the second encourages it to be smooth in the index  $i$ .

The difference penalty in  $(18.22)$  assumes an uniformly spaced index j. If instead the underlying index variable t has nonuniform values  $t_j$ , a natural generalization of (18.22) would be based on divided differences

Image /page/6/Figure/1 description: The image is a scatter plot showing the relationship between genome order on the x-axis and log2 ratio on the y-axis. The x-axis ranges from 0 to 1000, labeled as "Genome order". The y-axis ranges from -2 to 4, labeled as "log2 ratio". Gray dots represent individual data points. A red step-like line is overlaid on the scatter plot, indicating segmented data or a smoothed trend. A dashed blue line is present at y=0, serving as a reference line. The plot shows a significant peak in the log2 ratio between genome orders 100 and 200, with the red line reaching values around 4. After this peak, the log2 ratio generally decreases and fluctuates around 0 for the rest of the genome order.

FIGURE 18.8. Fused lasso applied to CGH data. Each point represents the copy-number of a gene in a tumor sample, relative to that of a control (on the log base-2 scale).

$$
\lambda_2 \sum_{j=1}^{p-1} \frac{|\beta_{j+1} - \beta_j|}{|t_{j+1} - t_j|}.
$$
\n(18.23)

This amounts to having a penalty modifier for each of the terms in the series.

A particularly useful special case arises when the predictor matrix  $X =$  $\mathbf{I}_N$ , the  $N \times N$  identity matrix. This is a special case of the fused lasso, used to approximate a sequence  $\{y_i\}_1^N$ . The *fused lasso signal approximator* solves

$$
\min_{\beta \in \mathbb{R}^N} \left\{ \sum_{i=1}^N (y_i - \beta_0 - \beta_i)^2 + \lambda_1 \sum_{i=1}^N |\beta_i| + \lambda_2 \sum_{i=1}^{N-1} |\beta_{i+1} - \beta_i| \right\}.
$$
 (18.24)

Figure 18.8 shows an example taken from Tibshirani and Wang (2007). The data in the panel come from a Comparative Genomic Hybridization (CGH) array, measuring the approximate log (base-two) ratio of the number of copies of each gene in a tumor sample, as compared to a normal sample. The horizontal axis represents the chromosomal location of each gene. The idea is that in cancer cells, genes are often amplified (duplicated) or deleted, and it is of interest to detect these events. Furthermore, these events tend to occur in contiguous regions. The smoothed signal estimate from the fused lasso signal approximator is shown in dark red (with appropriately chosen values for  $\lambda_1$  and  $\lambda_2$ ). The significantly nonzero regions can be used to detect locations of gains and losses of genes in the tumor.

There is also a two-dimensional version of the fused lasso, in which the parameters are laid out in a grid of pixels, and a penalty is applied to the first differences to the left, right, above and below the target pixel. This can be useful for denoising or classifying images. Friedman et al. (2007) develop fast generalized coordinate descent algorithms for the one- and two-dimensional fused lasso.

## 18.5 Classification When Features are Unavailable

In some applications the objects under study are more abstract in nature, and it is not obvious how to define a feature vector. As long as we can fill in an  $N \times N$  proximity matrix of similarities between pairs of objects in our database, it turns out we can put to use many of the classifiers in our arsenal by interpreting the proximities as inner-products. Protein structures fall into this category, and we explore an example in Section 18.5.1 below.

In other applications, such as document classification, feature vectors are available but can be extremely high-dimensional. Here we may not wish to compute with such high-dimensional data, but rather store the innerproducts between pairs of documents. Often these inner-products can be approximated by sampling techniques.

Pairwise distances serve a similar purpose, because they can be turned into centered inner-products. Proximity matrices are discussed in more detail in Chapter 14.

### 18.5.1 Example: String Kernels and Protein Classification

An important problem in computational biology is to classify proteins into functional and structural classes based on their sequence similarities. Protein molecules are strings of amino acids, differing in both length and composition. In the example we consider, the lengths vary between 75–160 amino-acid molecules, each of which can be one of 20 different types, labeled using letters. Here are two examples, of length 110 and 153, respectively:

IPTSALVKETLALLSTHRTLLIANETLRIPVPVHKNHQLCTEEIFQGIGTLESQTVQGGTV ERLFKNLSLIKKYIDGQKKKCGEERRRVNQFLDYLQEFLGVMNTEWI

PHRRDLCSRSIWLARKIRSDLTALTESYVKHQGLWSELTEAERLQENLQAYRTFHVLLA RLLEDQQVHFTPTEGDFHQAIHTLLLQVAAFAYQIEELMILLEYKIPRNEADGMLFEKK LWGLKVLQELSQWTVRSIHDLRFISSHQTGIP

There have been many proposals for measuring the similarity between a pair of protein molecules. Here we focus on a measure based on the count of matching substrings (Leslie et al., 2004), such as the LQE above.

To construct our features, we count the number of times that a given sequence of length  $m$  occurs in our string, and we compute this number for all possible sequences of length m. Formally, for a string x, we define a feature map

$$
\Phi_m(x) = \{\phi_a(x)\}_{a \in \mathcal{A}_m} \tag{18.25}
$$

where  $\mathcal{A}_m$  is the set of subsequences of length m, and  $\phi_a(x)$  is the number of times that " $a$ " occurs in our string x. Using this, we define the inner product

$$
K_m(x_1, x_2) = \langle \Phi_m(x_1), \Phi_m(x_2) \rangle, \tag{18.26}
$$

which measures the similarity between the two strings  $x_1, x_2$ . This can be used to drive, for example, a support vector classifier for classifying strings into different protein classes.

Now the number of possible sequences a is  $|\mathcal{A}_m| = 20^m$ , which can be very large for moderate  $m$ , and the vast majority of the subsequences do not match the strings in our training set. It turns out that we can compute the  $N \times N$  inner-product matrix or *string kernel*  $\mathbf{K}_m$  (18.26) efficiently using tree-structures, without actually computing the individual vectors. This methodology, and the data to follow, come from Leslie et al.  $(2004).<sup>4</sup>$ 

The data consist of 1708 proteins in two classes— negative (1663) and positive (45). The two examples above, which we will call " $x_1$ " and " $x_2$ ", are from this set. We have marked the occurrences of subsequence  $LQE$ , which appears in both proteins. There are  $20<sup>3</sup>$  possible subsequences, so  $\Phi_3(x)$  will be a vector of length 8000. For this example  $\phi_{LOE}(x_1) = 1$  and  $\phi_{LOE}(x_2) = 2.$ 

Using software from Leslie et al. (2004), we computed the string kernel for  $m = 4$ , which was then used in a support vector classifier to find the maximal margin solution in this  $20^4 = 160,000$ -dimensional feature space. We used 10-fold cross-validation to compute the SVM predictions on all of the training data. The orange curve in Figure 18.9 shows the cross-validated ROC curve for the support vector classifier, computed by varying the cutpoint on the real-valued predictions from the cross-validated support vector classifier. The area under the curve is 0.84. Leslie et al. (2004) show that the string kernel method is competitive with, but perhaps not as accurate as, more specialized methods for protein string matching.

Many other classifiers can be computed using only the information in the kernel matrix; some details are given in the next section. The results for the nearest centroid classifier (green), and distance-weighted one-nearest neighbors (blue) are shown in Figure 18.9. Their performance is similar to that of the support vector classifier.

<sup>4</sup>We thank Christina Leslie for her help and for providing the data, which is available on our book website.

Image /page/9/Figure/1 description: This is a line graph titled "ROC Curves for String Kernel". The x-axis represents Specificity and ranges from 0.0 to 1.0. The y-axis represents Sensitivity and ranges from 0.0 to 1.0. There are three ROC curves plotted: SVM (orange) with an AUC of 0.84, Nearest Centroid (green) with an AUC of 0.84, and One-Nearest Neighbor (purple) with an AUC of 0.86. A diagonal line from (0,0) to (1,1) is also shown, representing a random classifier.

FIGURE 18.9. Cross-validated ROC curves for protein example using the string kernel. The numbers next to each method in the legend give the area under the curve, an overall measure of accuracy. The SVM achieves better sensitivities than the other two, which achieve better specificities.

## 18.5.2 Classification and Other Models Using Inner-Product Kernels and Pairwise Distances

There are a number of other classifiers, besides the support-vector machine, that can be implemented using only inner-product matrices. This also implies they can be "kernelized" like the SVM.

An obvious example is nearest-neighbor classification, since we can transform pairwise inner-products to pairwise distances:

$$
||x_i - x_{i'}||^2 = \langle x_i, x_i \rangle + \langle x_{i'}, x_{i'} \rangle - 2\langle x_i, x_{i'} \rangle.
$$
 (18.27)

A variation of 1-NN classification is used in Figure 18.9, which produces a continuous discriminant score needed to construct a ROC curve. This distance-weighted 1-NN makes use of the distance of a test points to the closest member of each class; see Exercise 18.14.

Nearest-centroid classification follows easily as well. For training pairs  $(x_i, g_i), i = 1, \ldots, N$ , a test point  $x_0$ , and class centroids  $\bar{x}_k, k = 1, \ldots, K$ we can write

$$
||x_0 - \bar{x}_k||^2 = \langle x_0, x_0 \rangle - \frac{2}{N_k} \sum_{g_i = k} \langle x_0, x_i \rangle + \frac{1}{N_k^2} \sum_{g_i = k} \sum_{g_{i'} = k} \langle x_i, x_{i'} \rangle, \tag{18.28}
$$

Hence we can compute the distance of the test point to each of the centroids, and perform nearest centroid classification. This also implies that methods like K-means clustering can also be implemented, using only the inner products of the data points.

Logistic and multinomial regression with quadratic regularization can also be implemented with inner-product kernels; see Section 12.3.3 and Exercise 18.13. Exercise 12.10 derives linear discriminant analysis using an inner-product kernel.

Principal components can be computed using inner-product kernels as well; since this is frequently useful, we give some details. Suppose first that we have a centered data matrix **X**, and let  $X = UDV^T$  be its SVD (18.12). Then  $\mathbf{Z} = \mathbf{U}\mathbf{D}$  is the matrix of principal component variables (see Section 14.5.1). But if  $\mathbf{K} = \mathbf{X}\mathbf{X}^T$ , then it follows that  $\mathbf{K} = \mathbf{U}\mathbf{D}^2\mathbf{U}^T$ , and hence we can compute  $Z$  from the eigen decomposition of  $K$ . If  $X$  is not centered, then we can center it using  $\tilde{\mathbf{X}} = (\mathbf{I} - \mathbf{M})\mathbf{X}$ , where  $\mathbf{M} = \frac{1}{N}\mathbf{1}\mathbf{1}^T$ is the mean operator. Thus we compute the eigenvectors of the doublecentered kernel  $(I - M)K(I - M)$  for the principal components from an uncentered inner-product matrix. Exercise 18.15 explores this further, and Section 14.5.4 discusses in more detail kernel PCA for general kernels, such as the radial kernel used in SVMs.

If instead we had available only the pairwise (squared) Euclidean distances between observations,

$$
\Delta_{ii'}^2 = ||x_i - x_{i'}||^2,\tag{18.29}
$$

it turns out we can do all of the above as well. The trick is to convert the pairwise distances to centered inner-products, and then proceed as before. We write

$$
\Delta_{ii'}^2 = ||x_i - \bar{x}||^2 + ||x_{i'} - \bar{x}||^2 - 2\langle x_i - \bar{x}, x_{i'} - \bar{x}\rangle.
$$
 (18.30)

Defining  $\mathbf{B} = \{-\Delta_{ii'}^2/2\}$ , we double center **B**:

$$
\tilde{\mathbf{K}} = (\mathbf{I} - \mathbf{M})\mathbf{B}(\mathbf{I} - \mathbf{M});\tag{18.31}
$$

it is easy to check that  $\tilde{K}_{ii'} = \langle x_i - \bar{x}, x_{i'} - \bar{x} \rangle$ , the centered inner-product matrix.

Distances and inner-products also allow us to compute the medoid in each class—the observation with smallest average distance to other observations in that class. This can be used for classification (closest medoids), as well as to drive k-medoids clustering (Section 14.3.10). With abstract data objects like proteins, medoids have a practical advantage over means. The medoid is one of the training examples, and can be displayed. We tried closest medoids in the example in the next section (see Table 18.3), and its performance is disappointing.

It is useful to consider what we cannot do with inner-product kernels and distances:

TABLE 18.3. Cross-validated error rates for the abstracts example. The nearest shrunken centroids ended up using no-shrinkage, but does use a word-by-word standardization (section 18.2). This standardization gives it a distinct advantage over the other methods.

| Method                        | CV Error (SE) |
|-------------------------------|---------------|
| 1. Nearest shrunken centroids | 0.17 (0.05)   |
| 2. SVM                        | 0.23 (0.06)   |
| 3. Nearest medoids            | 0.65 (0.07)   |
| 4. 1-NN                       | 0.44 (0.07)   |
| 5. Nearest centroids          | 0.29 (0.07)   |

- We cannot standardize the variables; standardization significantly improves performance in the example in the next section.
- We cannot assess directly the contributions of individual variables. In particular, we cannot perform individual t-tests, fit the nearest shrunken centroids model, or fit any model that uses the lasso penalty.
- We cannot separate the good variables from the noise: all variables get an equal say. If, as is often the case, the ratio of relevant to irrelevant variables is small, methods that use kernels are not likely to work as well as methods that do feature selection.

#### 18.5.3 Example: Abstracts Classification

This somewhat whimsical example serves to illustrate a limitation of kernel approaches. We collected the abstracts from 48 papers, 16 each from Bradley Efron (BE), Trevor Hastie and Rob Tibshirani (HT) (frequent coauthors), and Jerome Friedman (JF). We extracted all unique words from these abstracts, and defined features  $x_{ij}$  to be the number of times word  $j$  appears in abstract  $i$ . This is the so-called bag of words representation. Quotations, parentheses and special characters were first removed from the abstracts, and all characters were converted to lower case. We also removed the word "we", which could unfairly discriminate HT abstracts from the others.

There were 4492 total words, of which  $p = 1310$  were unique. We sought to classify the documents into BE, HT or JF on the basis of the features  $x_{ij}$ . Although it is artificial, this example allows us to assess the possible degradation in performance if information specific to the raw features is not used.

We first applied the nearest shrunken centroid classifier to the data, using 10-fold cross-validation. It essentially chose no shrinkage, and so used all the features; see the first line of Table 18.3. The error rate is 17%; the number of features can be reduced to about 500 without much loss in accuracy. Note that the nearest shrunken classifier requires the raw feature matrix X in order to standardize the features individually. Figure 18.10 shows the

Image /page/12/Figure/2 description: This is a horizontal bar chart that displays data for three categories: BE, HT, and JF. The y-axis lists various terms, including "problems", "method", "presented", "propose", "frequentist", "bayesian", "those", "inference", "when", "variables", "accuracy", "values", "technology", "procedure", "are", "algorithm", "than", "using", "bayes", and "predictive". For each term, there are three orange horizontal bars, one for each category (BE, HT, JF), extending to the right or left of a central vertical line. The bars represent numerical values, with extensions to the right indicating positive values and extensions to the left indicating negative values.

FIGURE 18.10. Abstracts example: top 20 scores from nearest shrunken centroids. Each score is the standardized difference in frequency for the word in the given class (BE, HT or JF) versus all classes. Thus a positive score (to the right of the vertical grey zero lines) indicates a higher frequency in that class; a negative score indicates a lower relative frequency.

top 20 discriminating words, with a positive score indicating that a word appears more in that class than in the other classes.

Some of these terms make sense: for example "frequentist" and "Bayesian" reflect Efron's greater emphasis on statistical inference. However, many others are surprising, and reflect personal writing styles: for example, Friedman's use of "presented" and HT's use of "propose".

We then applied the support vector classifier with linear kernel and no regularization, using the "all pairs" (ovo) method to handle the three classes (regularization of the SVM did not improve its performance). The result is shown in Table 18.3. It does somewhat worse than the nearest shrunken centroid classifier.

As mentioned, the first line of Table 18.3 represents nearest shrunken centroids (with no shrinkage). Denote by  $s_i$  the pooled within-class standard deviation for feature j, and  $s_0$  the median of the  $s_j$  values. Then line (1) also corresponds to nearest centroid classification, after first standardizing each feature by  $s_j + s_0$  [recall (18.4) on page 652].

Line (3) shows that the performance of nearest medoids is very poor, something which surprised us. It is perhaps due to the small sample sizes

# 674 18. High-Dimensional Problems: $p \gg N$

and high dimensions, with medoids having much higher variance than means. The performance of the one-nearest neighbor classifier is also poor.

The performance of the nearest centroid classifier is also shown in Table 18.3 in line (5): it is better than nearest medoids, but worse than that of nearest shrunken centroids, even with no shrinkage. The difference seems to be the standardization of each feature that is done in nearest shrunken centroids. This standardization is important here, and requires access to the individual feature values. Nearest centroids uses a spherical metric, and relies on the fact that the features are in similar units. The support vector machine estimates a linear combination of the features and can better deal with unstandardized features.

# 18.6 High-Dimensional Regression: Supervised Principal Components

In this section we describe a simple approach to regression and generalized regression that is especially useful when  $p \gg N$ . We illustrate the method on another microarray data example. The data is taken from Rosenwald et al. (2002) and consists of 240 samples from patients with diffuse large B-cell lymphoma (DLBCL), with gene expression measurements for 7399 genes. The outcome is survival time, either observed or right censored. We randomly divided the lymphoma samples into a training set of size 160 and a test set of size 80.

Although supervised principal components is useful for linear regression, its most interesting applications may be in survival studies, which is the focus of this example.

We have not yet discussed regression with censored survival data in this book; it represents a generalized form of regression in which the outcome variable (survival time) is only partly observed for some individuals. Suppose for example we carry out a medical study that lasts for 365 days, and for simplicity all subjects are recruited on day one. We might observe one individual to die 200 days after the start of the study. Another individual might still be alive at 365 days when the study ends. This individual is said to be "right censored" at 365 days. We know only that he or she lived at least 365 days. Although we do not know how long past 365 days the individual actually lived, the censored observation is still informative. This is illustrated in Figure 18.11. Figure 18.12 shows the survival curve estimated by the Kaplan–Meier method for the 80 patients in the test set. See for example Kalbfleisch and Prentice (1980) for a description of the Kaplan–Meier method.

Our objective in this example is to find a set of features (genes) that can predict the survival of an independent set of patients. This could be

Image /page/14/Figure/1 description: This is a survival plot showing data for four patients over 365 days. The y-axis represents the patients, labeled 1 through 4. The x-axis represents time in days, with markings at 0, 100, 200, 300, and 365. Patient 1 has a solid red circle at approximately 310 days. Patient 2 has an open circle at 365 days. Patient 3 has a solid red circle at approximately 200 days. Patient 4 has an open circle at approximately 310 days. The plot includes dashed vertical lines at the start (implied at 0) and at 365 days.

FIGURE 18.11. Censored survival data. For illustration there are four patients. The first and third patients die before the study ends. The second patient is alive at the end of the study (365 days), while the fourth patient is lost to follow-up before the study ends. For example, this patient might have moved out of the country. The survival times for patients two and four are said to be "censored."

Image /page/14/Figure/3 description: The image is a survival function plot titled "Survival Function". The y-axis represents the probability Pr(T \ge t) and ranges from 0.0 to 1.0. The x-axis represents time in months, labeled as "Months t", and ranges from 0 to 20. The plot shows a Kaplan-Meier survival curve, which is a step function that decreases over time. There are also dashed lines indicating the confidence intervals for the survival curve. Small vertical ticks on the curve indicate censored observations.

FIGURE 18.12. Lymphoma data. The Kaplan–Meier estimate of the survival function for the 80 patients in the test set, along with one-standard-error curves. The curve estimates the probability of surviving past t months. The ticks indicate censored observations.

Image /page/15/Figure/1 description: The image displays two bell curves, representing probability density distributions for two cell types: 'Poor Cell Type' (in red) and 'Good Cell Type' (in blue). Both curves are plotted against 'Survival Time' on the x-axis. The 'Poor Cell Type' curve is shifted to the left, indicating a shorter average survival time, while the 'Good Cell Type' curve is shifted to the right, suggesting a longer average survival time. The curves overlap, showing some individuals from both groups have similar survival times.

FIGURE 18.13. Underlying conceptual model for supervised principal components. There are two cell types, and patients with the good cell type live longer on the average. Supervised principal components estimate the cell type, by averaging the expression of genes that reflect it.

useful as a prognostic indicator to aid in choosing treatments, or to help understand the biological basis for the disease.

The underlying conceptual model for supervised principal components is shown in Figure 18.13. We imagine that there are two cell types, and patients with the good cell type live longer on the average. However there is considerable overlap in the two sets of survival times. We might think of survival time as a "noisy surrogate" for cell type. A fully supervised approach would give the most weight to those genes having the strongest relationship with survival. These genes are partially, but not perfectly, related to cell type. If we could instead discover the underlying cell types of the patients, often reflected by a sizable signature of genes acting together in pathways, then we might do a better job of predicting patient survival.

Although the cell type in Figure 18.13 is discrete, it is useful to imagine a continuous cell type, define by some linear combination of the features. We will estimate the cell type as a continuous quantity, and then discretize it for display and interpretation.

How can we find the linear combination that defines the important underlying cell types? Principal components analysis (Section 14.5) is an effective method for finding linear combinations of features that exhibit large variation in a dataset. But what we seek here are linear combinations with both high variance *and* significant correlation with the outcome. The lower right panel of Figure 18.14 shows the result of applying standard principal components in this example; the leading component does not correlate strongly with survival (details are given in the figure caption).

Hence we want to encourage principal component analysis to find linear combinations of features that have high correlation with the outcome. To do this, we restrict attention to features which by themselves have a sizable correlation with the outcome. This is summarized in the supervised principal components Algorithm 18.1, and illustrated in Figure 18.14.

The details in steps (1) and (2b) will depend on the type of outcome variable. For a standard regression problem, we use the univariate linear least squares coefficients in step (1) and a linear least squares model in

Image /page/16/Figure/0 description: The figure displays three Kaplan-Meier survival curves comparing different gene sets. The top plot, titled 'Best Single Gene', shows survival curves for patients with 'low score' (blue) and 'high score' (orange), with a P-value of 0.15. The middle plot, 'Supervised Principal Component - 27 Genes', also shows survival curves for 'low score' and 'high score' groups, with a significantly lower P-value of 0.006. The bottom plot, 'Principal Component - 7399 Genes', presents similar survival curves with a P-value of 0.14. To the left of the survival plots, there is a heatmap showing gene expression across patients, with a scale indicating 'Supervised PC'. Below the heatmap, a plot shows the 'Absolute Cox Score' for each patient, with a red dashed line indicating a threshold. The x-axis for the heatmap and Cox score plot represents 'Patients' (from 1 to 160), and the y-axis for the heatmap represents 'Genes' (from 1 to 7399). The x-axis for the survival plots represents 'Months'.

FIGURE 18.14. Supervised principal components on the lymphoma data. The left panel shows a heatmap of a subset of the gene-expression training data. The rows are ordered by the magnitude of the univariate Cox-score, shown in the middle vertical column. The top 50 and bottom 50 genes are shown. The supervised principal component uses the top 27 genes (chosen by 10-fold CV). It is represented by the bar at the top of the heatmap, and is used to order the columns of the expression matrix. In addition, each row is multiplied by the sign of the Cox-score. The middle panel on the right shows the survival curves on the test data when we create a low and high group by splitting this supervised PC at zero (training data mean). The curves are well separated, as indicated by the p-value for the log-rank test. The top panel does the same, using the top-scoring gene on the training data. The curves are somewhat separated, but not significantly. The bottom panel uses the first principal component on all the genes, and the separation is also poor. Each of the top genes can be interpreted as noisy surrogates for a latent underlying cell-type characteristic, and supervised principal components uses them all to estimate this latent factor.