{"table_of_contents": [{"title": "8.3. <PERSON><PERSON>", "heading_level": null, "page_id": 1, "polygon": [[90.0, 315.0], [263.25, 315.0], [263.25, 327.95947265625], [90.0, 327.95947265625]]}, {"title": "8.3.1 Conditional independence properties", "heading_level": null, "page_id": 1, "polygon": [[138.75, 495.75], [379.5, 495.75], [379.5, 506.9794921875], [138.75, 506.9794921875]]}, {"title": "384 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 2, "polygon": [[30.0, 40.5], [205.5, 40.5], [205.5, 51.86865234375], [30.0, 51.86865234375]]}, {"title": "8.3.2 Factorization properties", "heading_level": null, "page_id": 2, "polygon": [[137.25, 540.0], [306.75, 540.0], [306.75, 551.2060546875], [137.25, 551.2060546875]]}, {"title": "386 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 4, "polygon": [[30.0, 40.5], [204.75, 40.5], [204.75, 51.380859375], [30.0, 51.380859375]]}, {"title": "8.3.3 Illustration: Image de-noising", "heading_level": null, "page_id": 5, "polygon": [[138.75, 435.0], [338.25, 435.0], [338.25, 446.16796875], [138.75, 446.16796875]]}, {"title": "390 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 8, "polygon": [[29.9619140625, 40.5], [205.2421875, 40.5], [205.2421875, 51.7467041015625], [29.9619140625, 51.7467041015625]]}, {"title": "8.3.4 Relation to directed graphs", "heading_level": null, "page_id": 8, "polygon": [[137.25, 381.0], [324.75, 381.0], [324.75, 392.185546875], [137.25, 392.185546875]]}, {"title": "392 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 10, "polygon": [[30.0, 40.5], [205.5, 40.5], [205.5, 51.8280029296875], [30.0, 51.8280029296875]]}, {"title": "8.4. Inference in Graphical Models 393", "heading_level": null, "page_id": 11, "polygon": [[263.8125, 40.5], [473.25, 40.5], [473.25, 51.2589111328125], [263.8125, 51.2589111328125]]}, {"title": "8.4. Inference in Graphical Models", "heading_level": null, "page_id": 11, "polygon": [[89.25, 543.0], [306.75, 543.0], [306.75, 556.083984375], [89.25, 556.083984375]]}, {"title": "394 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 12, "polygon": [[30.0, 40.5], [204.873046875, 40.5], [204.873046875, 51.787353515625], [30.0, 51.787353515625]]}, {"title": "8.4.1 Inference on a chain", "heading_level": null, "page_id": 12, "polygon": [[136.3359375, 494.25], [286.5, 494.25], [286.5, 504.703125], [136.3359375, 504.703125]]}, {"title": "396 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 14, "polygon": [[30.0, 40.5], [205.5, 40.5], [205.5, 51.7060546875], [30.0, 51.7060546875]]}, {"title": "8.4.2 Trees", "heading_level": null, "page_id": 16, "polygon": [[138.0, 578.25], [206.2265625, 578.25], [206.2265625, 589.5791015625], [138.0, 589.5791015625]]}, {"title": "8.4.3 Factor graphs", "heading_level": null, "page_id": 17, "polygon": [[138.0, 431.25], [253.5, 431.25], [253.5, 442.265625], [138.0, 442.265625]]}, {"title": "400 8. GRAPHICAL MODELS", "heading_level": null, "page_id": 18, "polygon": [[29.25, 40.5], [204.75, 40.5], [204.75, 51.949951171875], [29.25, 51.949951171875]]}, {"title": "8.4.4 The sum-product algorithm", "heading_level": null, "page_id": 20, "polygon": [[136.828125, 340.5], [324.75, 340.5], [324.75, 351.5361328125], [136.828125, 351.5361328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 41], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2], ["Caption", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 42], ["Text", 6], ["SectionHeader", 2], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 37], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Caption", 1], ["Picture", 1], ["Equation", 1], ["ListItem", 1], ["Figure", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1240, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 51], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 44], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 19], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["Text", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 624, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Caption", 1], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 40], ["Text", 6], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1299, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 44], ["Text", 6], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1832, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 43], ["Text", 9], ["SectionHeader", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 39], ["Text", 8], ["SectionHeader", 2], ["Caption", 1], ["TextInlineMath", 1], ["ListItem", 1], ["Picture", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 617, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 45], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 44], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 64], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3846, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 57], ["Text", 5], ["TextInlineMath", 4], ["Equation", 3], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3057, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 45], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 42], ["Text", 9], ["Figure", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1832, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 42], ["Text", 3], ["Caption", 2], ["Figure", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1321, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 48], ["Text", 5], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 36], ["Text", 3], ["Figure", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1352, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 47], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_402-423"}