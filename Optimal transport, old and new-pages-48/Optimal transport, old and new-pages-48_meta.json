{"table_of_contents": [{"title": "Weak Ricci curvature bounds I: Definition and\nStability", "heading_level": null, "page_id": 0, "polygon": [[133.5, 98.9033203125], [466.5, 97.5], [466.5, 129.8408203125], [133.5, 129.8408203125]]}, {"title": "Integral functionals of singular measures", "heading_level": null, "page_id": 3, "polygon": [[133.5, 48.0], [374.25, 48.0], [374.25, 59.2646484375], [133.5, 59.2646484375]]}, {"title": "Synthetic definition of the curvature-dimension bound", "heading_level": null, "page_id": 5, "polygon": [[133.5, 536.25], [456.01171875, 536.25], [456.01171875, 547.59375], [133.5, 547.59375]]}, {"title": "818 29 Weak R<PERSON>ci curvature bounds I: Definition and Stability", "heading_level": null, "page_id": 7, "polygon": [[133.5, 26.25], [406.107421875, 26.25], [406.107421875, 35.62646484375], [133.5, 35.62646484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 35], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 65], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 62], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1609, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 416], ["Line", 47], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 53], ["TextInlineMath", 6], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 608], ["Line", 93], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1151, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["Line", 44], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 38], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 648], ["Line", 51], ["TextInlineMath", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 66], ["TextInlineMath", 4], ["Equation", 4], ["ListItem", 3], ["Text", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1177, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 81], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 600], ["Line", 45], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 678], ["Line", 62], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1016, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-48"}