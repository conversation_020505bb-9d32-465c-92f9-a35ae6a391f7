{"table_of_contents": [{"title": "The <PERSON><PERSON> shortening principle", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.5], [423.0, 97.5], [423.0, 112.0517578125], [133.5, 112.0517578125]]}, {"title": "Quadratic cost function", "heading_level": null, "page_id": 0, "polygon": [[133.5, 468.703125], [275.25, 468.703125], [275.25, 480.3046875], [133.5, 480.3046875]]}, {"title": "General statement and applications to optimal transport", "heading_level": null, "page_id": 2, "polygon": [[133.5, 357.0], [469.5, 357.0], [469.5, 368.25], [133.5, 368.25]]}, {"title": "180 8 The <PERSON><PERSON><PERSON> shortening principle", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.25], [340.06640625, 26.25], [340.06640625, 35.578125], [133.5, 35.578125]]}, {"title": "Proof of <PERSON><PERSON>'s estimates", "heading_level": null, "page_id": 8, "polygon": [[133.5, 286.5], [300.1728515625, 286.5], [300.1728515625, 297.966796875], [133.5, 297.966796875]]}, {"title": "Complement: Ruling out focalization by shortening", "heading_level": null, "page_id": 16, "polygon": [[133.5, 522.75], [438.978515625, 522.75], [438.978515625, 534.4453125], [133.5, 534.4453125]]}, {"title": "192 8 The <PERSON><PERSON>–<PERSON> shortening principle", "heading_level": null, "page_id": 17, "polygon": [[133.5, 26.25], [340.6640625, 26.25], [340.6640625, 35.81982421875], [133.5, 35.81982421875]]}, {"title": "Problem 8.8 (Focalization is impossible before the cut locus).", "heading_level": null, "page_id": 17, "polygon": [[133.27734375, 249.75], [469.5, 249.75], [469.5, 260.841796875], [133.27734375, 260.841796875]]}, {"title": "Introduction to <PERSON><PERSON>'s theory", "heading_level": null, "page_id": 19, "polygon": [[133.5, 118.4326171875], [326.25, 118.4326171875], [326.25, 129.2607421875], [133.5, 129.2607421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 24], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2393, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 68], ["Equation", 3], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["Line", 83], ["Equation", 5], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 45], ["TextInlineMath", 8], ["Text", 5], ["Equation", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 48], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 44], ["TextInlineMath", 5], ["Text", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 41], ["TextInlineMath", 5], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 537], ["Line", 47], ["TextInlineMath", 7], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 419], ["Line", 56], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 27], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 648], ["Line", 48], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 605], ["Line", 88], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7652, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 398], ["Line", 52], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 555], ["Line", 99], ["Text", 7], ["Equation", 6]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 6410, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 776], ["Line", 199], ["Equation", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 7391, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 704], ["Line", 152], ["Text", 8], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 6030, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 67], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 702], ["Line", 39], ["TextInlineMath", 8], ["SectionHeader", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 40], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 499], ["Line", 35], ["TextInlineMath", 9], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-8"}