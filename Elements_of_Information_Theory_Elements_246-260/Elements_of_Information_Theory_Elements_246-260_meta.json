{"table_of_contents": [{"title": "Differential Entropy", "heading_level": null, "page_id": 0, "polygon": [[69.0, 105.521484375], [303.240234375, 105.521484375], [303.240234375, 132.099609375], [69.0, 132.099609375]]}, {"title": "9.1 DEFINITIONS", "heading_level": null, "page_id": 0, "polygon": [[69.75, 352.4765625], [166.7021484375, 352.4765625], [166.7021484375, 364.5], [69.75, 364.5]]}, {"title": "9.2 THE AEP FOR CONTINUOUS RANDOM VARIABLES", "heading_level": null, "page_id": 1, "polygon": [[64.5, 537.0], [351.75, 537.0], [351.75, 548.015625], [64.5, 548.015625]]}, {"title": "9.3 RELATION OF DIFFERENTIAL ENTROPY TO DISCRETE \nENTROPY", "heading_level": null, "page_id": 4, "polygon": [[64.5, 106.5], [369.0, 106.5], [369.0, 129.1728515625], [64.5, 129.1728515625]]}, {"title": "Examples:", "heading_level": null, "page_id": 5, "polygon": [[65.25, 263.724609375], [120.9990234375, 263.724609375], [120.9990234375, 274.166015625], [65.25, 274.166015625]]}, {"title": "9.4 JOINT AND CONDITIONAL DIFFERENTIAL ENTROPY", "heading_level": null, "page_id": 5, "polygon": [[66.0, 460.5], [362.25, 460.5], [362.25, 470.8125], [66.0, 470.8125]]}, {"title": "9.5 RELATIVE ENTROPY AND MUTUAL INFORMATION", "heading_level": null, "page_id": 7, "polygon": [[66.0, 156.75], [354.75, 156.75], [354.75, 167.220703125], [66.0, 167.220703125]]}, {"title": "9.6 PROPERTIES OF DIFFERENTIAL ENTROPY, RELATIVE \nENTROPY AND MUTUAL INFORMATION", "heading_level": null, "page_id": 8, "polygon": [[63.75, 118.5], [360.75, 118.5], [360.75, 141.1171875], [63.75, 141.1171875]]}, {"title": "Theorem 9.6.3:", "heading_level": null, "page_id": 9, "polygon": [[63.75, 225.75], [142.5, 225.75], [142.5, 236.197265625], [63.75, 236.197265625]]}, {"title": "coroIIary:", "heading_level": null, "page_id": 10, "polygon": [[64.5, 60.75], [120.0, 60.75], [120.0, 70.6376953125], [64.5, 70.6376953125]]}, {"title": "9.7 DIFFERENTIAL ENTROPY BOUND ON DISCRETE ENTROPY", "heading_level": null, "page_id": 10, "polygon": [[64.5, 467.25], [393.0, 467.25], [393.0, 477.7734375], [64.5, 477.7734375]]}, {"title": "SUMMARY OF CHAPTER 9", "heading_level": null, "page_id": 12, "polygon": [[169.55859375, 515.25], [303.92578125, 515.25], [303.92578125, 525.55078125], [169.55859375, 525.55078125]]}, {"title": "PROBLEMS FOR CHAPTER 9", "heading_level": null, "page_id": 13, "polygon": [[65.64111328125, 378.0], [211.60546875, 378.0], [211.60546875, 388.23046875], [65.64111328125, 388.23046875]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 14, "polygon": [[64.5, 342.0], [171.9580078125, 342.0], [171.9580078125, 352.4765625], [64.5, 352.4765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 27], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3295, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 27], ["Equation", 8], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 30], ["Text", 7], ["TextInlineMath", 5], ["Equation", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1359, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 31], ["Equation", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 25], ["Text", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 610, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 31], ["Equation", 4], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 32], ["Equation", 11], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 30], ["Equation", 10], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 25], ["Text", 9], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 25], ["Text", 8], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 31], ["Equation", 6], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 19], ["Equation", 6], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 34], ["Equation", 12], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 34], ["Equation", 10], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 28], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_246-260"}