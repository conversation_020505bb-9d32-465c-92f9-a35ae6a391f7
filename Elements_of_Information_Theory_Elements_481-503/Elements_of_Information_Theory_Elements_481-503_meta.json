{"table_of_contents": [{"title": "Information Theory and \nthe Stock Market", "heading_level": null, "page_id": 0, "polygon": [[69.0, 105.36328125], [349.62890625, 105.36328125], [349.62890625, 158.203125], [69.0, 158.203125]]}, {"title": "15.1 THE STOCK MARKET: SOME DEFINITIONS", "heading_level": null, "page_id": 0, "polygon": [[71.25, 371.77734375], [326.77734375, 371.77734375], [326.77734375, 383.16796875], [71.25, 383.16796875]]}, {"title": "15.2 KUHN-TUCKER CHARACTERIZATION OF THE \nLOG-OPTIMAL PORTFOLIO", "heading_level": null, "page_id": 3, "polygon": [[64.5, 452.25], [330.43359375, 452.25], [330.43359375, 474.29296875], [64.5, 474.29296875]]}, {"title": "15.3 ASYMPTOTIC OPTIMALITY OF THE LOG-OPTIMAL \nPORTFOLIO", "heading_level": null, "page_id": 6, "polygon": [[65.25, 201.75], [355.5, 201.75], [355.5, 224.6484375], [65.25, 224.6484375]]}, {"title": "15.4 SIDE INFORMATION AND THE DOUBLING RATE", "heading_level": null, "page_id": 8, "polygon": [[65.25, 289.5], [348.0, 289.5], [348.0, 300.427734375], [65.25, 300.427734375]]}, {"title": "15.5 INVESTMENT IN STATIONARY MARKETS", "heading_level": null, "page_id": 10, "polygon": [[64.5, 57.0], [308.25, 57.0], [308.25, 67.7109375], [64.5, 67.7109375]]}, {"title": "15.6 COMPETITIVE OPTIMALITY OF THE LOG-OPTIMAL \nPORTFOLIO", "heading_level": null, "page_id": 12, "polygon": [[63.75, 354.75], [361.5, 354.75], [361.5, 377.15625], [63.75, 377.15625]]}, {"title": "15.7 THE SHANNON-McMILLAN-BREIMAN THEOREM", "heading_level": null, "page_id": 15, "polygon": [[62.9560546875, 185.25], [346.5, 185.25], [346.5, 196.013671875], [62.9560546875, 196.013671875]]}, {"title": "By <PERSON><PERSON>'s inequality and (15.117), we have", "heading_level": null, "page_id": 19, "polygon": [[64.5, 363.0], [285.75, 363.75], [285.75, 375.890625], [64.5, 375.890625]]}, {"title": "SUMMARY OF CHAPTER 15", "heading_level": null, "page_id": 20, "polygon": [[165.75, 221.958984375], [303.0, 221.958984375], [303.0, 231.767578125], [165.75, 231.767578125]]}, {"title": "PROBLEMS FOR CHAPTER 15", "heading_level": null, "page_id": 21, "polygon": [[63.75, 381.0], [216.0, 379.5], [216.0, 392.02734375], [63.75, 392.02734375]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 22, "polygon": [[63.75, 354.0], [171.0, 354.0], [171.0, 364.5], [63.75, 364.5]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 32], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3240, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 41], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 637, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 30], ["Text", 7], ["Equation", 6], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 33], ["Text", 5], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6789, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 34], ["Equation", 7], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 31], ["Text", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 34], ["Text", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 38], ["Equation", 8], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 31], ["Equation", 7], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 30], ["Equation", 11], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 34], ["Equation", 7], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 34], ["Text", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1036, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 31], ["Text", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 32], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 35], ["Equation", 9], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 35], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1023, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 36], ["TextInlineMath", 6], ["Equation", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 35], ["Equation", 9], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 35], ["Equation", 8], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2048, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 34], ["Equation", 12], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 28], ["Text", 8], ["Equation", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 36], ["Text", 10], ["Equation", 10], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 29], ["Text", 6], ["ListItem", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_481-503"}