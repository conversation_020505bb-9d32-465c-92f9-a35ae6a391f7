{"table_of_contents": [{"title": "Qualitative description of optimal transport", "heading_level": null, "page_id": 0, "polygon": [[156.75, 210.75], [472.5, 210.75], [472.5, 231.0], [156.75, 231.0]]}, {"title": "Basic properties", "heading_level": null, "page_id": 4, "polygon": [[133.5, 98.25], [249.0, 98.25], [249.0, 111.375], [133.5, 111.375]]}, {"title": "Existence", "heading_level": null, "page_id": 4, "polygon": [[133.5, 234.0], [192.0, 234.0], [192.0, 245.1796875], [133.5, 245.1796875]]}, {"title": "Restriction property", "heading_level": null, "page_id": 6, "polygon": [[133.5, 479.91796875], [256.5, 479.91796875], [256.5, 490.74609375], [133.5, 490.74609375]]}, {"title": "Convexity properties", "heading_level": null, "page_id": 8, "polygon": [[133.5, 160.5], [258.75, 160.5], [258.75, 172.08984375], [133.5, 172.08984375]]}, {"title": "Description of optimal plans", "heading_level": null, "page_id": 9, "polygon": [[133.5, 47.80810546875], [302.25, 47.80810546875], [302.25, 59.21630859375], [133.5, 59.21630859375]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 10, "polygon": [[233.25, 48.0], [359.490234375, 48.0], [359.490234375, 59.21630859375], [233.25, 59.21630859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1846, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 66], ["Line", 30], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 25], ["TextInlineMath", 3], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 604], ["Line", 53], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 963, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 37], ["TextInlineMath", 4], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 576], ["Line", 52], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 61], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2417, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 26], ["TableCell", 16], ["ListItem", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1008, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 16], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-4"}