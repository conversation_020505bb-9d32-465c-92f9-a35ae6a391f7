# Assessing Generative Models via Precision and Recall

Mehdi S<PERSON><sup>∗</sup> MPI for Intelligent Systems, Max Planck ETH Center for Learning Systems

Google Brain

<PERSON> Brain

<PERSON> Google Brain

Syl<PERSON><PERSON> Google Brain

## Abstract

Recent advances in generative modeling have led to an increased interest in the study of statistical divergences as means of model comparison. Commonly used evaluation methods, such as the Fréchet Inception Distance (FID), correlate well with the perceived quality of samples and are sensitive to mode dropping. However, these metrics are unable to distinguish between different failure cases since they only yield one-dimensional scores. We propose a novel definition of precision and recall for distributions which disentangles the divergence into two separate dimensions. The proposed notion is intuitive, retains desirable properties, and naturally leads to an efficient algorithm that can be used to evaluate generative models. We relate this notion to total variation as well as to recent evaluation metrics such as Inception Score and FID. To demonstrate the practical utility of the proposed approach we perform an empirical study on several variants of Generative Adversarial Networks and Variational Autoencoders. In an extensive set of experiments we show that the proposed metric is able to disentangle the quality of generated samples from the coverage of the target distribution.

## 1 Introduction

Deep generative models, such as Variational Autoencoders (VAE) [\[12\]](#page-8-0) and Generative Adversarial Networks (GAN) [\[8\]](#page-8-1), have received a great deal of attention due to their ability to learn complex, high-dimensional distributions. One of the biggest impediments to future research is the lack of quantitative evaluation methods to accurately assess the quality of trained models. Without a proper evaluation metric researchers often need to visually inspect generated samples or resort to qualitative techniques which can be subjective. One of the main difficulties for quantitative assessment lies in the fact that the distribution is only specified implicitly – one can learn to sample from a predefined distribution, but cannot evaluate the likelihood efficiently. In fact, even if likelihood computation were computationally tractable, it might be inadequate and misleading for high-dimensional problems [\[22\]](#page-8-2).

As a result, surrogate metrics are often used to assess the quality of the trained models. Some proposed measures, such as Inception Score (IS) [\[20\]](#page-8-3) and Fréchet Inception Distance (FID) [\[9\]](#page-8-4), have shown promising results in practice. In particular, FID has been shown to be robust to image corruption, it correlates well with the visual fidelity of the samples, and it can be computed on unlabeled data.

However, all of the metrics commonly applied to evaluating generative models share a crucial weakness: Since they yield a one-dimensional score, they are unable to distinguish between different failure cases. For example, the generative models shown in Figure [1](#page-1-0) obtain similar FIDs but exhibit

<sup>∗</sup>This work was done during an internship at Google Brain.

Correspondence: [msajjadi.com,](http://msajjadi.com) [<EMAIL>,](mailto:<EMAIL>) [<EMAIL>.](mailto:<EMAIL>)

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a precision-recall curve comparing the performance of GANs on MNIST and CelebA datasets. The x-axis represents Recall, ranging from 0.0 to 1.0, and the y-axis represents Precision, also ranging from 0.0 to 1.0. Four curves are plotted: 'MNIST left' (light blue), 'MNIST right' (green), 'CelebA left' (red), and 'CelebA right' (purple). The 'MNIST right' curve shows the highest precision across a wide range of recall values, indicating better performance. The 'CelebA left' and 'CelebA right' curves show significantly lower precision, especially at higher recall values. The image also includes two grids of generated images: one grid of handwritten digits (MNIST) on the top left and top right, and another grid of human faces (CelebA) on the bottom left and bottom right. The generated digits and faces appear to be of varying quality, with some being clearer than others.

Figure 1: Comparison of GANs trained on MNIST and CelebA. Although the models obtain a similar FID on each data set (32/29 for MNIST and 65/62 for CelebA), their samples look very different. For example, the model on the left produces reasonably looking faces on CelebA, but too many dark images. In contrast, the model on the right produces more artifacts, but more varied images. By the proposed metric (middle), the models on the left achieve higher precision and lower recall than the models on the right, which suffices to successfully distinguishing between the failure cases.

different sample characteristics: the model on the left trained on MNIST [\[15\]](#page-8-5) produces realistic samples, but only generates a subset of the digits. On the other hand, the model on the right produces low-quality samples which appear to cover all digits. A similar effect can be observed on the CelebA [\[16\]](#page-8-6) data set. In this work we argue that a single-value summary is not adequate to compare generative models.

Motivated by this shortcoming, we present a novel approach which disentangles the divergence between distributions into two components: *precision* and *recall*. Given a reference distribution P and a learned distribution  $Q$ , precision intuitively measures the quality of samples from  $Q$ , while recall measures the proportion of  $P$  that is covered by  $Q$ . Furthermore, we propose an elegant algorithm which can compute these quantities based on samples from  $P$  and  $Q$ . In particular, using this approach we are able to quantify the degree of *mode dropping* and *mode inventing* based on samples from the true and the learned distributions.

Our contributions: (1) We introduce a novel definition of precision and recall for distributions and prove that the notion is theoretically sound and has desirable properties, (2) we propose an efficient algorithm to compute these quantities,  $(3)$  we relate these notions to total variation, IS and FID,  $(4)$  we demonstrate that in practice one can quantify the degree of mode dropping and mode inventing on real world data sets (image and text data), and (5) we compare several types of generative models based on the proposed approach – to our knowledge, this is the first metric that experimentally confirms the folklore that GANs often produce "sharper" images, but can suffer from mode collapse (high precision, low recall), while VAEs produce "blurry" images, but cover more modes of the distribution (low precision, high recall).

## 2 Background and Related Work

The task of evaluating generative models is an active research area. Here we focus on recent work in the context of deep generative models for image and text data. Classic approaches relying on comparing log-likelihood have received some criticism due the fact that one can achieve high likelihood, but low image quality, and conversely, high-quality images but low likelihood [\[22\]](#page-8-2). While the likelihood can be approximated in some settings, kernel density estimation in high-dimensional spaces is extremely challenging [\[22,](#page-8-2) [24\]](#page-9-0). Other failure modes related to density estimation in highdimensional spaces have been elaborated in [\[10,](#page-8-7) [22\]](#page-8-2). A recent review of popular approaches is presented in [\[5\]](#page-8-8).

The Inception Score (IS) [\[20\]](#page-8-3) offers a way to quantitatively evaluate the quality of generated samples in the context of image data. Intuitively, the conditional label distribution  $p(y|x)$  of samples containing meaningful objects should have low entropy, while the label distribution over the whole data set  $p(y)$  should have high entropy. Formally, IS(G) = exp( $\mathbb{E}_{x\sim G}[d_{KL}(p(y|x), p(y)])$ . The score is computed based on a classifier (Inception network trained on ImageNet). IS necessitates a labeled data set and has been found to be weak at providing guidance for model comparison [\[3\]](#page-8-9).

<span id="page-2-0"></span>Image /page/2/Figure/0 description: The image displays three figures. Figure 2 shows six pairs of bar charts labeled (a) through (f), illustrating intuitive examples of P and Q. Figure 3 presents six corresponding PRD(Q, P) plots, also labeled (a) through (f), with axes labeled 'α' and 'β'. Figure 4 is a precision-recall curve plot. The x-axis is labeled 'Recall β' ranging from 0.0 to 1.0, and the y-axis is labeled 'Precision α' ranging from 0.0 to 1.0. The plot features a blue shaded area representing the algorithm's performance, with three dotted lines indicating different lambda values: λ = 2, λ = 1, and λ = 0.5. Three black dots are marked on the curve.

The FID [\[9\]](#page-8-4) provides an alternative approach which requires no labeled data. The samples are first embedded in some feature space (e.g., a specific layer of Inception network for images). Then, a continuous multivariate Gaussian is fit to the data and the distance computed as  $FID(x, g) =$  $||\mu_x - \mu_g||_2^2 + \text{Tr}(\Sigma_x + \Sigma_g - 2(\Sigma_x \Sigma_g)^{\frac{1}{2}})$ , where  $\mu$  and  $\Sigma$  denote the mean and covariance of the corresponding samples. FID is sensitive to both the addition of spurious modes as well as to mode dropping (see Figure [5](#page-5-0) and results in [\[18\]](#page-8-10)). [\[4\]](#page-8-11) recently introduced an unbiased alternative to FID, the *Kernel Inception Distance*. While unbiased, it shares an extremely high Spearman rank-order correlation with FID [\[14\]](#page-8-12).

Another approach is to train a classifier between the real and fake distributions and to use its accuracy on a test set as a proxy for the quality of the samples [\[11,](#page-8-13) [17\]](#page-8-14). This approach necessitates training of a classifier for each model which is seldom practical. Furthermore, the classifier might detect a single dimension where the true and generated samples differ (e.g., barely visible artifacts in generated images) and enjoy high accuracy, which runs the risk of assigning lower quality to a better model.

To the best of our knowledge, all commonly used metrics for evaluating generative models are one-dimensional in that they only yield a single score or distance. A notion of precision and recall has previously been introduced in [\[18\]](#page-8-10) where the authors compute the distance to the manifold of the true data and use it as a proxy for precision and recall on a synthetic data set. Unfortunately, it is not possible to compute this quantity for more complex data sets.

## 3 PRD: Precision and Recall for Distributions

In this section, we derive a novel notion of precision and recall to compare a distribution Q to a reference distribution P. The key intuition is that *precision* should measure how much of Q can be generated by a "part" of P while *recall* should measure how much of P can be generated by a "part" of Q. Figure [2](#page-2-0) (a)-(d) show four toy examples for P and Q to visualize this idea: (a) If P is bimodal and Q only captures one of the modes, we should have perfect precision but only limited recall. (b) In the opposite case, we should have perfect recall but only limited precision. (c) If  $Q = P$ , we should have perfect precision and recall. (d) If the supports of  $P$  and  $Q$  are disjoint, we should have zero precision and recall.

#### 3.1 Derivation

Let  $S = \text{supp}(P) \cap \text{supp}(Q)$  be the (non-empty) intersection of the supports<sup>[2](#page-2-1)</sup> of P and Q. Then, P may be viewed as a two-component mixture where the first component  $P<sub>S</sub>$  is a probability distribution on S and the second component  $P_{\overline{S}}$  is defined on the complement of S. Similarly, Q may be rewritten as a mixture of  $Q_S$  and  $Q_{\overline{S}}$ . More formally, for some  $\overline{\alpha}, \overline{\beta} \in (0, 1]$ , we define

<span id="page-2-2"></span>
$$
P = \overline{\beta}P_S + (1 - \overline{\beta})P_{\overline{S}} \quad \text{and} \quad Q = \overline{\alpha}Q_S + (1 - \overline{\alpha})Q_{\overline{S}}.
$$
 (1)

This decomposition allows for a natural interpretation:  $P_{\overline{S}}$  is the part of P that cannot be generated by Q, so its mixture weight  $1 - \bar{\beta}$  may be viewed as a loss in recall. Similarly,  $Q_{\overline{S}}$  is the part of Q that cannot be generated by P, so  $1 - \bar{\alpha}$  may be regarded as a loss in precision. In the case where

<span id="page-2-1"></span><sup>&</sup>lt;sup>2</sup>For a distribution P defined on a finite state space  $\Omega$ , we define supp $(P) = \{ \omega \in \Omega \mid P(\omega) > 0 \}.$ 

 $P_S = Q_S$ , i.e., the distributions P and Q agree on S up to scaling,  $\bar{\alpha}$  and  $\bar{\beta}$  provide us with a simple two-number precision and recall summary satisfying the examples in Figure [2](#page-2-0) (a)-(d).

If  $P_S \neq Q_S$ , we are faced with a conundrum: Should the differences in  $P_S$  and  $Q_S$  be attributed to losses in precision or recall? Is  $Q_S$  inadequately "covering"  $P_S$  or is it generating "unnecessary" noise? Inspired by PR curves for binary classification, we propose to resolve this predicament by providing a trade-off between precision and recall instead of a two-number summary for any two distributions P and Q. To parametrize this trade-off, we consider a distribution  $\mu$  on S that signifies a "true" common component of  $P_S$  and  $Q_S$  and similarly to [\(1\)](#page-2-2), we decompose both  $P_S$  and  $Q_S$  as

$$
P_S = \beta'\mu + (1 - \beta')P_\mu
$$
 and  $Q_S = \alpha'\mu + (1 - \alpha')Q_\mu.$  (2)

<span id="page-3-0"></span>The distribution  $P_S$  is viewed as a two-component mixture where the first component is  $\mu$  and the second component  $P_\mu$  signifies the part of  $P_S$  that is "missed" by  $Q_S$  and should thus be considered a recall loss. Similarly,  $Q_S$  is decomposed into  $\mu$  and the part  $Q_\mu$  that signifies noise and should thus be considered a precision loss. As  $\mu$  is varied, this leads to a trade-off between precision and recall.

It should be noted that unlike PR curves for binary classification where different thresholds lead to different classifiers, trade-offs between precision and recall here do not constitute different models or distributions – the proposed PRD curves only serve as a description of the characteristics of the model with respect to the target distribution.

#### 3.2 Formal definition

For simplicity, we consider distributions  $P$  and  $Q$  that are defined on a finite state space, though the notion of precision and recall can be extended to arbitrary distributions. By combining [\(1\)](#page-2-2) and [\(2\)](#page-3-0), we obtain the following formal definition of precision and recall.

<span id="page-3-1"></span>**Definition 1.** *For*  $\alpha, \beta \in (0, 1]$ *, the probability distribution* Q *has precision*  $\alpha$  *at recall*  $\beta$  *w.r.t.* P *if there exist distributions*  $\mu$ ,  $\nu$ *P* and  $\nu$ <sup>Q</sup> *such that* 

<span id="page-3-10"></span>
$$
P = \beta \mu + (1 - \beta)\nu_P \quad \text{and} \quad Q = \alpha \mu + (1 - \alpha)\nu_Q. \tag{3}
$$

The component  $\nu_P$  denotes the part of P that is "missed" by Q and encompasses both  $P_{\overline{S}}$  in [\(1\)](#page-2-2) and  $P_{\mu}$  in [\(2\)](#page-3-0). Similarly,  $\nu_Q$  denotes the noise part of Q and includes both  $Q_{\overline{S}}$  in [\(1\)](#page-2-2) and  $Q_{\mu}$  in (2).

<span id="page-3-8"></span>Definition 2. *The set of attainable pairs of precision and recall of a distribution* Q *w.r.t. a distribution P* is denoted by  $\text{PRD}(Q, P)$  and it consists of all  $(\alpha, \beta)$  satisfying Definition [1](#page-3-1) and the pair  $(0, 0)$ .

The set  $\text{PRD}(Q, P)$  characterizes the above-mentioned trade-off between precision and recall and can be visualized similarly to PR curves in binary classification: Figure [3](#page-2-0) (a)-(d) show the set  $\text{PRD}(Q, P)$ on a 2D-plot for the examples (a)-(d) in Figure [2.](#page-2-0) Note how the plot distinguishes between (a) and (b): Any symmetric evaluation method (such as FID) assigns these cases the same score although they are highly different. The interpretation of the set  $\text{PRD}(Q, P)$  is further aided by the following set of basic properties which we prove in Section [A.1](#page-10-0) in the appendix.

<span id="page-3-9"></span>Theorem 1. *Let* P *and* Q *be probability distributions defined on a finite state space* Ω*. The set* PRD(Q, P) *satisfies the following properties:*

<span id="page-3-6"></span><span id="page-3-5"></span><span id="page-3-4"></span><span id="page-3-2"></span>

| <i>(equality)</i>        | (i) $(1,1) \in PRD(Q, P) \Leftrightarrow Q = P$                                                                                         |
|--------------------------|-----------------------------------------------------------------------------------------------------------------------------------------|
| (disjoint supports)      | (ii) $\text{PRD}(Q, P) = \{(0, 0)\}\Leftrightarrow \text{supp}(Q) \cap \text{supp}(P) = \emptyset$                                      |
| ( <i>max precision</i> ) | (iii) $Q(\text{supp}(P)) = \bar{\alpha} = \max_{(\alpha,\beta)\in \text{PRD}(Q,P)} \alpha$                                              |
| (max recall)             | (iv) $P(\text{supp}(Q)) = \overline{\beta} = \max_{(\alpha,\beta) \in \text{PRD}(Q,P)} \beta$                                           |
|                          | (v) $(\alpha', \beta') \in PRD(Q, P)$ if $\alpha' \in (0, \alpha], \beta' \in (0, \beta], (\alpha, \beta) \in PRD(Q, P)$ (monotonicity) |
| ( <i>duality</i> )       | $(vi)$ $(\alpha, \beta) \in \text{PRD}(Q, P) \Leftrightarrow (\beta, \alpha) \in \text{PRD}(P, Q)$                                      |
|                          | Property (i) in combination with Property (v) guarantees that $Q = P$ if the set $\text{PRD}(Q, P)$ contains                            |

<span id="page-3-7"></span><span id="page-3-3"></span>the interior of the unit square, see case (c) in Figures [2](#page-2-0) and [3.](#page-2-0) Similarly, Property [\(ii\)](#page-3-4) assures that whenever there is no overlap between P and Q,  $\text{PRD}(Q, P)$  only contains the origin, see case (d) of Figures [2](#page-2-0) and [3.](#page-2-0) Properties [\(iii\)](#page-3-5) and [\(iv\)](#page-3-6) provide a connection to the decomposition in [\(1\)](#page-2-2) and allow an analysis of the cases (a) and (b) in Figures [2](#page-2-0) and [3:](#page-2-0) As expected,  $Q$  in (a) achieves a maximum precision of 1 but only a maximum recall of 0.5 while in (b), maximum recall is 1 but maximum

precision is 0.5. Note that the quantities  $\bar{\alpha}$  and  $\bar{\beta}$  here are by construction the same as in [\(1\)](#page-2-2). Finally, Property [\(vi\)](#page-3-7) provides a natural interpretation of precision and recall: The precision of  $Q$  w.r.t.  $P$  is equal to the recall of P w.r.t. Q and *vice versa*.

Clearly, not all cases are as simple as the examples (a)-(d) in Figures [2](#page-2-0) and [3,](#page-2-0) in particular if  $P$  and  $Q$  are different on the intersection  $S$  of their support. The examples (e) and (f) in Figure [2](#page-2-0) and the resulting sets  $\text{PRD}(Q, P)$  in Figure [3](#page-2-0) illustrate the importance of the trade-off between precision and recall as well as the utility of the set  $\text{PRD}(Q, P)$ . In both cases, P and Q have the same support while  $Q$  has high precision and low recall in case (e) and low precision and high recall in case (f). This is clearly captured by the sets  $\text{PRD}(Q, P)$ . Intuitively, the examples (e) and (f) may be viewed as noisy versions of the cases (a) and (b) in Figure [2.](#page-2-0)

#### <span id="page-4-2"></span>3.3 Algorithm

Computing the set  $\text{PRD}(Q, P)$  based on Definitions [1](#page-3-1) and [2](#page-3-8) is non-trivial as one has to check whether there exist suitable distributions  $\mu$ ,  $\nu$ *P* and  $\nu$ <sup>Q</sup> for all possible values of  $\alpha$  and  $\beta$ . We introduce an equivalent definition of  $\text{PRD}(Q, P)$  in Theorem [2](#page-4-0) that does not depend on the distributions  $\mu$ ,  $\nu_P$ and  $\nu_Q$  and that leads to an elegant algorithm to compute practical PRD curves.

<span id="page-4-0"></span>Theorem 2. *Let* P *and* Q *be two probability distributions defined on a finite state space* Ω*. For*  $\lambda > 0$  *define the functions* 

<span id="page-4-1"></span>
$$
\alpha(\lambda) = \sum_{\omega \in \Omega} \min(\lambda P(\omega), Q(\omega)) \quad \text{and} \quad \beta(\lambda) = \sum_{\omega \in \Omega} \min\left(P(\omega), \frac{Q(\omega)}{\lambda}\right). \tag{4}
$$

 $\sim$   $\sim$ 

*Then, it holds that*

$$
PRD(Q, P) = \{ (\theta \alpha(\lambda), \theta \beta(\lambda)) \mid \lambda \in (0, \infty), \theta \in [0, 1] \}.
$$

We prove the theorem in Section [A.2](#page-11-0) in the appendix. The key idea of Theorem [2](#page-4-0) is illustrated in Figure [4:](#page-2-0) The set of  $\text{PRD}(Q, P)$  may be viewed as a union of segments of the lines  $\alpha = \lambda \beta$  over all  $\lambda \in (0,\infty)$ . Each segment starts at the origin  $(0,0)$  and ends at the maximal achievable value  $(\alpha(\lambda), \beta(\lambda))$ . This provides a surprisingly simple algorithm to compute PRD(Q, P) in practice: Simply compute pairs of  $\alpha(\lambda)$  and  $\beta(\lambda)$  as defined in [\(4\)](#page-4-1) for an equiangular grid of values of  $\lambda$ . For a given angular resolution  $m \in \mathbb{N}$ , we compute

$$
\widehat{\text{PRD}}(Q, P) = \{ (\alpha(\lambda), \beta(\lambda)) \mid \lambda \in \Lambda \} \quad \text{where} \quad \Lambda = \left\{ \tan \left( \frac{i}{m+1} \frac{\pi}{2} \right) \mid i = 1, 2, \dots, m \right\}.
$$

To compare different distributions  $Q_i$ , one may simply plot their respective PRD curves  $\widehat{\text{PRD}}(Q_i, P)$ , while an approximation of the full sets  $\text{PRD}(Q_i, P)$  may be computed by interpolation between  $\widehat{\text{PRD}}(Q_i, P)$  and the origin. An implementation of the algorithm is available at [https://github.com/msmsajjadi/precision-recall-distributions.](https://github.com/msmsajjadi/precision-recall-distributions)

#### 3.4 Connection to total variation distance

Theorem [2](#page-4-0) provides a natural interpretation of the proposed approach. For  $\lambda = 1$ , we have

$$
\alpha(1) = \beta(1) = \sum_{\omega \in \Omega} \min(P(\omega), Q(\omega)) = \sum_{\omega \in \Omega} \left[ P(\omega) - (P(\omega) - Q(\omega))^+ \right] = 1 - \delta(P, Q)
$$

where  $\delta(P,Q)$  denotes the total variation distance between P and Q. As such, our notion of precision and recall may be viewed as a generalization of total variation distance.

## 4 Application to Deep Generative Models

In this section, we show that the algorithm introduced in Section [3.3](#page-4-2) can be readily applied to evaluate precision and recall of deep generative models. In practice, access to  $P$  and  $Q$  is given via samples  $\hat{P} \sim P$  and  $\hat{Q} \sim Q$ . Given that both P and Q are continuous distributions, the probability of generating a point sampled from  $Q$  is 0. Furthermore, there is strong empirical evidence that comparing samples in image space runs the risk of assigning higher quality to a worse model [\[17,](#page-8-14) [20,](#page-8-3) [22\]](#page-8-2). A common remedy is to apply a pre-trained classifier trained on natural images and to compare  $\hat{P}$  and  $\hat{Q}$  at a feature level. Intuitively, in this feature space the samples should be

<span id="page-5-0"></span>Image /page/5/Figure/0 description: The image contains three plots. The leftmost plot is a line graph showing the relationship between the number of classes in Q on the x-axis and Inception score and FID on the y-axes. The Inception score increases from approximately 4.5 at Q=1 to 9.5 at Q=10, while the FID decreases from approximately 9 to 20. The middle and rightmost plots are precision-recall curves. The middle plot shows curves for Q values from 1 to 10, with higher Q values generally resulting in curves that stay closer to the top-left corner. The rightmost plot shows curves for Q values from 1 to 5, also demonstrating that higher Q values lead to better performance, with the curve for Q=5 being the highest.

Figure 5: Left: IS and FID as we remove and add classes of CIFAR-10. IS generally only increases, while FID is sensitive to both the addition and removal of classes. However, it cannot distinguish between the two failure cases of inventing or dropping modes. Middle: Resulting PRD curves for the same experiment. As expected, adding modes leads to a loss in precision  $(Q_6-Q_{10})$ , while dropping modes leads to a loss in recall  $(Q_1-Q_4)$ . As an example consider  $Q_4$  and  $Q_6$  which have similar FID, but strikingly different PRD curves. The same behavior can be observed for the task of text generation, as displayed on the plot on the right. For this experiment, we set P to contain samples from all classes so the PRD curves demonstrate the increase in recall as we increase the number of classes in Q.

compared based on statistical regularities in the images rather than random artifacts resulting from the generative process [\[17,](#page-8-14) [19\]](#page-8-15).

Following this line of work, we first use a pre-trained Inception network to embed the samples (i.e. using the *Pool3* layer [\[9\]](#page-8-4)). We then cluster the union of  $\hat{P}$  and  $\hat{Q}$  in this feature space using mini-batch k-means with  $k = 20$  [\[21\]](#page-8-16). Intuitively, we reduce the problem to a one dimensional problem where the histogram over the cluster assignments can be meaningfully compared. Hence, failing to produce samples from a cluster with many samples from the true distribution will hurt recall, and producing samples in clusters without many real samples will hurt precision. As the clustering algorithm is randomized, we run the procedure several times and average over the PRD curves. We note that such a clustering is meaningful as shown in Figure [9](#page-12-0) in the appendix and that it can be efficiently scaled to very large sample sizes [\[1,](#page-8-17) [2\]](#page-8-18).

We stress that from the point of view of the proposed algorithm, only a meaningful embedding is required. As such, the algorithm can be applied to various data modalities. In particular, we show in Section [4.1](#page-5-1) that besides image data the algorithm can be applied to a text generation task.

#### <span id="page-5-1"></span>4.1 Adding and dropping modes from the target distribution

Mode collapse or mode dropping is a major challenge in GANs [\[8,](#page-8-1) [20\]](#page-8-3). Due to the symmetry of commonly used metrics with respect to precision and recall, the only way to assess whether the model is producing low-quality images or dropping modes is by visual inspection. In stark contrast, the proposed metric can quantitatively disentangle these effects which we empirically demonstrate.

We consider three data sets commonly used in the GAN literature: MNIST [\[15\]](#page-8-5), Fashion-MNIST [\[25\]](#page-9-1), and CIFAR-10 [\[13\]](#page-8-19). These data sets are labeled and consist of 10 balanced classes. To show the sensitivity of the proposed measure to mode dropping and mode inventing, we first fix  $\hat{P}$  to contain samples from the first 5 classes in the respective test set. Then, for a fixed  $i = 1, \ldots, 10$ , we generate a set  $\hat{Q}_i$ , which consists of samples from the first  $i$  classes from the training set. As  $i$  increases,  $\hat{Q}_i$ covers an increasing number of classes from  $\hat{P}$  which should result in higher recall. As we increase i beyond 5,  $\hat{Q}_i$  includes samples from an increasing number of classes that are not present in  $\hat{P}$  which should result in a loss in precision, but not in recall as the other classes are already covered. Finally, the set  $\hat{Q}_5$  covers the same classes as  $\hat{P}$ , so it should have high precision and high recall.

Figure [5](#page-5-0) (left) shows the IS and FID for the CIFAR-10 data set (results on the other data sets are shown in Figure [11](#page-13-0) in the appendix). Since the IS is not computed w.r.t. a reference distribution, it is invariant to the choice of  $\hat{P}$ , so as we add classes to  $\hat{Q}_i$ , the IS increases. The FID decreases as we add more classes until  $\hat{Q}_5$  before it starts to increase as we add spurious modes. Critically, FID fails to distinguish the cases of mode dropping and mode inventing:  $\hat{Q}_4$  and  $\hat{Q}_6$  share similar FIDs. In contrast, Figure [5](#page-5-0) (middle) shows our PRD curves as we vary the number of classes in  $\hat{Q}_i$ . Adding correct modes leads to an increase in recall, while adding fake modes leads to a loss of precision.

<span id="page-6-0"></span>Image /page/6/Figure/0 description: This image displays a set of three plots related to machine learning model performance on handwritten digits. The top left shows a grid of handwritten digits, with a corresponding bar chart below it showing the frequency of each digit class (0-9). The central plot is a precision-recall curve, with two lines labeled 'left' and 'right', illustrating the trade-off between precision and recall for two different configurations or models. The top right shows another grid of handwritten digits, similar to the one on the top left, and the bottom right shows a bar chart representing the class distribution for this second set of digits. The y-axis of the bar charts ranges from 0 to 5k, and the x-axis represents the class labels from 0 to 9. The precision-recall curve has 'Recall' on the x-axis (ranging from 0.0 to 1.0) and 'Precision' on the y-axis (ranging from 0.0 to 1.0).

Figure 6: Comparing two GANs trained on MNIST which both achieve an FID of 49. The model on the left seems to produce high-quality samples of only a subset of digits. On the other hand, the model on the right generates low-quality samples of all digits. The histograms showing the corresponding class distributions based on a trained MNIST classifier confirm this observation. At the same time, the classifier is more confident which indicates different levels of precision (96.7% for the model on the left compared to 88.6% for the model on the right). Finally, we note that the proposed PRD algorithm does not require labeled data, as opposed to the IS which further needs a classifier that was trained on the respective data set.

We also apply the proposed approach on text data as shown in Figure [5](#page-5-0) (right). In particular, we use the MultiNLI corpus of crowd-sourced sentence pairs annotated with topic and textual entailment information [\[23\]](#page-8-20). After discarding the entailment label, we collect all unique sentences for the same topic. Following [\[6\]](#page-8-21), we embed these sentences using a BiLSTM with 2048 cells in each direction and max pooling, leading to a 4096-dimensional embedding [\[7\]](#page-8-22). We consider 5 classes from this data set and fix  $\hat{P}$  to contain samples from all classes to measure the loss in recall for different  $Q_i.$ Figure [5](#page-5-0) (right) curves successfully demonstrate the sensitivity of recall to mode dropping.

#### <span id="page-6-2"></span>4.2 Assessing class imbalances for GANs

In this section we analyze the effect of class imbalance on the PRD curves. Figure [6](#page-6-0) shows a pair of GANs trained on MNIST which have virtually the same FID, but very different PRD curves. The model on the left generates a subset of the digits of high quality, while the model on the right seems to generate all digits, but each has low quality. We can naturally interpret this difference via the PRD curves: For a desired recall level of less than ∼0.6, the model on the left enjoys higher precision – it generates several digits of high quality. If, however, one desires a recall higher than  $\sim 0.6$ , the model on the right enjoys higher precision as it covers all digits. To confirm this, we train an MNIST classifier on the embedding of  $\hat{P}$  with the ground truth labels and plot the distribution of the predicted classes for both models. The histograms clearly show that the model on the left failed to generate all classes (loss in recall), while the model on the right is producing a more balanced distribution over all classes (high recall). At the same time, the classifier has an average *confidence*[3](#page-6-1) of 96.7% on the model on the left compared to 88.6% on the model on the right, indicating that the sample quality of the former is higher. This aligns very well with the PRD plots: samples on the left have high quality but are not diverse in contrast to the samples on the right which are diverse but have low quality.

This analysis reveals a connection to IS which is based on the premise that the conditional label distribution  $p(y|x)$  should have low entropy, while the marginal  $p(y) = \int p(y|x = G(z))dz$  should have high entropy. To further analyze the relationship between the proposed approach and PRD curves, we plot  $p(y|x)$  against precision and  $p(y)$  against recall in Figure [10](#page-13-1) in the appendix. The results over a large number of GANs and VAEs show a large Spearman correlation of -0.83 for precision and 0.89 for recall. We however stress two key differences between the approaches: Firstly, to compute the quantities in IS one needs a classifier and a labeled data set in contrast to the proposed PRD metric which can be applied on unlabeled data. Secondly, IS only captures losses in recall w.r.t. classes, while our metric measures more fine-grained recall losses (see Figure [8](#page-11-1) in the appendix).

<span id="page-6-1"></span> $3$ We denote the output of the classifier for its highest value at the softmax layer as confidence. The intuition is that higher values signify higher confidence of the model for the given label.

<span id="page-7-0"></span>Image /page/7/Figure/0 description: This figure is a scatter plot comparing GAN and VAE performance, with F8 (Recall) on the x-axis and F1/8 (Precision) on the y-axis, both ranging from 0.0 to 1.0. The plot displays two clusters of points: blue points representing GAN performance and green points representing VAE performance. Four specific points are highlighted with labels: A, B, C, and D. To the right of the scatter plot are four grids of images labeled A, B, C, and D, corresponding to the highlighted points. Grid A shows multiple images of shoes. Grid B shows a variety of clothing items including shirts, pants, and shoes. Grid C displays a mix of clothing items and shoes. Grid D also shows a collection of clothing items and shoes.

Figure 7:  $F_{1/8}$  vs  $F_8$  scores for a large number of GANs and VAEs on the Fashion-MNIST data set. For each model, we plot the maximum  $F_{1/8}$  and  $F_8$  scores to show the trade-off between precision and recall. VAEs generally achieve lower precision and/or higher recall than GANs which matches the folklore that VAEs often produce samples of lower quality while being less prone to mode collapse. On the right we show samples from four models which correspond to various success/failure modes: (A) high precision, low recall, (B) high precision, high recall, (C) low precision, low recall, and (D) low precision, high recall.

<span id="page-7-1"></span>

### 4.3 Application to GANs and VAEs

We evaluate the precision and recall of 7 GAN types and the VAE with 100 hyperparameter settings each as provided by [\[18\]](#page-8-10). In order to visualize this vast quantity of models, one needs to summarize the PRD curves. A natural idea is to compute the maximum  $F_1$  score, which corresponds to the harmonic mean between precision and recall as a single-number summary. This idea is fundamentally flawed as  $F_1$  is symmetric. However, its generalization, defined as  $F_\beta = (1 + \beta^2) \frac{p \cdot r}{(\beta^2 p) + r}$ , provides a way to quantify the relative importance of precision and recall:  $\beta > 1$  weighs recall higher than precision, whereas  $\beta < 1$  weighs precision higher than recall. As a result, we propose to distill each PRD curve into a pair of values:  $F_\beta$  and  $F_{1/\beta}$ .

Figure [7](#page-7-0) compares the maximum  $F_8$  with the maximum  $F_{1/8}$  for these models on the Fashion-MNIST data set. We choose  $\beta = 8$  as it offers a good insight into the bias towards precision versus recall. Since  $F_8$  weighs recall higher than precision and  $\bar{F}_{1/8}$  does the opposite, models with higher recall than precision will lie below the diagonal  $F_8 = F_{1/8}$  and models with higher precision than recall will lie above. To our knowledge, this is the first metric which confirms the folklore that VAEs are biased towards higher recall, but may suffer from precision issues (e.g., due to blurring effects), at least on this data set. On the right, we show samples from four models on the extreme ends of the plot for all combinations of high and low precision and recall. We have included similar plots on the MNIST, CIFAR-10 and CelebA data sets in the appendix.

# 5 Conclusion

Quantitatively evaluating generative models is a challenging task of paramount importance. In this work we show that one-dimensional scores are not sufficient to capture different failure cases of current state-of-the-art generative models. As an alternative, we propose a novel notion of precision and recall for distributions and prove that both notions are theoretically sound and have desirable properties. We then connect these notions to total variation distance as well as FID and IS and we develop an efficient algorithm that can be readily applied to evaluate deep generative models based on samples. We investigate the properties of the proposed algorithm on real-world data sets, including image and text generation, and show that it captures the precision and recall of generative models. Finally, we find empirical evidence supporting the folklore that VAEs produce samples of lower quality, while being less prone to mode collapse than GANs.

## References

- <span id="page-8-17"></span>[1] Olivier Bachem, Mario Lucic, Hamed Hassani, and Andreas Krause. Fast and provably good seedings for k-means. In *Advances in Neural Information Processing Systems (NIPS)*, 2016.
- <span id="page-8-18"></span>[2] Olivier Bachem, Mario Lucic, S Hamed Hassani, and Andreas Krause. Approximate k-means++ in sublinear time. In *AAAI*, 2016.
- <span id="page-8-9"></span>[3] Shane Barratt and Rishi Sharma. A Note on the Inception Score. *arXiv preprint arXiv:1801.01973*, 2018.
- <span id="page-8-11"></span>[4] Mikołaj Binkowski, Dougal J. Sutherland, Michael Arbel, and Arthur Gretton. Demystifying MMD GANs. ´ In *International Conference on Learning Representations (ICLR)*, 2018.
- <span id="page-8-8"></span>[5] Ali Borji. Pros and Cons of GAN Evaluation Measures. *arXiv preprint arXiv:1802.03446*, 2018.
- <span id="page-8-21"></span>[6] Ondřej Cífka, Aliaksei Severyn, Enrique Alfonseca, and Katja Filippova. Eval all, trust a few, do wrong to none: Comparing sentence generation models. *arXiv preprint arXiv:1804.07972*, 2018.
- <span id="page-8-22"></span>[7] Alexis Conneau, Douwe Kiela, Holger Schwenk, Loic Barrault, and Antoine Bordes. Supervised Learning of Universal Sentence Representations from Natural Language Inference Data. *arXiv preprint arXiv:1705.02364*, 2017.
- <span id="page-8-1"></span>[8] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative Adversarial Networks. In *Advances in Neural Information Processing Systems (NIPS)*, 2014.
- <span id="page-8-4"></span>[9] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, Günter Klambauer, and Sepp Hochreiter. GANs trained by a two time-scale update rule converge to a Nash equilibrium. In *Advances in Neural Information Processing Systems (NIPS)*, 2017.
- <span id="page-8-7"></span>[10] Ferenc Huszár. How (not) to Train your Generative Model: Scheduled Sampling, Likelihood, Adversary? *arXiv preprint arXiv:1511.05101*, 2015.
- <span id="page-8-13"></span>[11] Daniel Jiwoong Im, He Ma, Graham Taylor, and Kristin Branson. Quantitatively evaluating GANs with divergences proposed for training. In *International Conference on Learning Representations (ICLR)*, 2018.
- <span id="page-8-0"></span>[12] Diederik P Kingma and Max Welling. Auto-encoding Variational Bayes. In *International Conference on Learning Representations (ICLR)*, 2014.
- <span id="page-8-19"></span>[13] Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images, 2009.
- <span id="page-8-12"></span>[14] Karol Kurach, Mario Lucic, Xiaohua Zhai, Marcin Michalski, and Sylvain Gelly. The GAN Landscape: Losses, architectures, regularization, and normalization. *arXiv preprint arXiv:1807.04720*, 2018.
- <span id="page-8-5"></span>[15] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. In *IEEE*, 1998.
- <span id="page-8-6"></span>[16] Ziwei Liu, Ping Luo, Xiaogang Wang, and Xiaoou Tang. Deep learning face attributes in the wild. In *Proceedings of International Conference on Computer Vision (ICCV)*, 2015.
- <span id="page-8-14"></span>[17] David Lopez-Paz and Maxime Oquab. Revisiting Classifier Two-Sample Tests. In *International Conference on Learning Representations (ICLR)*, 2016.
- <span id="page-8-10"></span>[18] Mario Lucic, Karol Kurach, Marcin Michalski, Sylvain Gelly, and Olivier Bousquet. Are GANs Created Equal? A Large-Scale Study. In *Advances in Neural Information Processing Systems (NIPS)*, 2018.
- <span id="page-8-15"></span>[19] Augustus Odena, Vincent Dumoulin, and Chris Olah. Deconvolution and checkerboard artifacts. *Distill*, 2016.
- <span id="page-8-3"></span>[20] Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved Techniques for Training GANs. In *Advances in Neural Information Processing Systems (NIPS)*, 2016.
- <span id="page-8-16"></span>[21] David Sculley. Web-scale k-means clustering. In *International Conference on World Wide Web (WWW)*, 2010.
- <span id="page-8-2"></span>[22] Lucas Theis, Aäron van den Oord, and Matthias Bethge. A note on the evaluation of generative models. In *International Conference on Learning Representations (ICLR)*, 2016.
- <span id="page-8-20"></span>[23] Adina Williams, Nikita Nangia, and Samuel R Bowman. A broad-coverage challenge corpus for sentence understanding through inference. *arXiv preprint arXiv:1704.05426*, 2017.

- <span id="page-9-0"></span>[24] Yuhuai Wu, Yuri Burda, Ruslan Salakhutdinov, and Roger Grosse. On the quantitative analysis of decoder-based generative models. In *International Conference on Learning Representations (ICLR)*, 2017.
- <span id="page-9-1"></span>[25] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-MNIST: A Novel Image Dataset for Benchmarking Machine Learning Algorithms. *arXiv preprint arXiv:1708.07747*, 2017.

# A Proofs

We first show the following auxiliary result and then prove Theorems [1](#page-3-9) and [2.](#page-4-0)

<span id="page-10-2"></span><span id="page-10-1"></span>**Lemma 1.** Let P and Q be probability distributions defined on a finite state space  $\Omega$ . Let  $\alpha \in (0,1]$ and  $\beta \in (0,1]$ . Then,  $(\alpha, \beta) \in \text{PRD}(Q, P)$  *if and only if there exists a distribution*  $\mu$  *such that for all*  $\omega \in \Omega$ 

$$
P(\omega) \ge \beta \mu(\omega) \quad \text{and} \quad Q(\omega) \ge \alpha \mu(\omega). \tag{5}
$$

*Proof.* If  $(\alpha, \beta) \in \text{PRD}(Q, P)$ , then [\(3\)](#page-3-10) and the non-negativity of  $\nu_P$  and  $\nu_Q$  directly imply [\(5\)](#page-10-1) for the same choice of  $\mu$ . Conversely, if [\(5\)](#page-10-1) holds for a distribution  $\mu$ , we may define the distributions

$$
\nu_P(\omega) = \frac{P(\omega) - \beta \mu(\omega)}{1 - \beta} \quad \text{and} \quad \nu_Q(\omega) = \frac{Q(\omega) - \alpha \mu(\omega)}{1 - \alpha}.
$$

By definition  $\alpha$ ,  $\beta$ ,  $\mu$ ,  $\nu_P$  and  $\nu_Q$  satisfy [\(3\)](#page-3-10) in Definition [1](#page-3-1) which implies  $(\alpha, \beta) \in \text{PRD}(Q, P)$ .  $\Box$ 

#### <span id="page-10-0"></span>A.1 Proof of Theorem [1](#page-3-9)

*Proof.* We show each of the properties independently:

*[\(i\)](#page-3-2) Equality*: If  $(1, 1) \in \text{PRD}(Q, P)$  $(1, 1) \in \text{PRD}(Q, P)$  $(1, 1) \in \text{PRD}(Q, P)$ , then we have by Definition 1 that  $P = \mu$  and  $Q = \mu$  which implies  $P = Q$  as claimed. Conversely, if  $P = Q$ , Definition [1](#page-3-1) is satisfied for  $\alpha = \beta = 1$  by choosing  $\mu = \nu_P = \nu_Q = P$ . Hence,  $(1, 1) \in \text{PRD}(Q, P)$  as claimed.

*[\(ii\)](#page-3-4) Disjoint support*: We show both directions of the claim by contraposition, i.e., we show supp(P)∩  $\text{supp}(Q) \neq \emptyset \Leftrightarrow \text{PRD}(Q, P) \supset \{(0, 0)\}.$  Consider an arbitrary  $\omega \in \text{supp}(P) \cap \text{supp}(Q)$ . Then, by definition we have  $P(\omega) > 0$  and  $Q(\omega) > 0$ . Let  $\mu$  be defined as the distribution with  $\mu(\omega) = 1$ and  $\mu(\omega') = 0$  for all  $\omega' \in \Omega \setminus \{\omega\}$ . Clearly, it holds that  $P(\omega) \ge P(\omega)\mu(\omega)$  and  $Q(\omega) \ge Q(\omega)\mu(\omega)$ for all  $\omega \in \Omega$ . Hence, by Lemma [1,](#page-10-2) we have  $(Q(\omega), P(\omega)) \in \text{PRD}(Q, P)$  which implies that  $PRD(Q, P) \supset \{(0, 0)\}\$ as claimed. Conversely,  $PRD(Q, P) \supset \{(0, 0)\}\$ implies by Lemma [1](#page-10-2) that there exist  $\alpha \in (0,1]$  and  $\beta \in (0,1]$  as well as a distribution  $\mu$  satisfying [\(5\)](#page-10-1). Let  $\omega \in \text{supp}(\mu)$  which implies  $\mu(\omega) > 0$  and thus by [\(5\)](#page-10-1) also  $P(\omega) > 0$  and  $Q(\omega) > 0$ . Hence,  $\omega$  is both in the support of P and Q which implies  $supp(P) \cap supp(Q) \neq \emptyset$  as claimed.

*[\(iii\)](#page-3-5) Maximum precision*: If  $(\alpha, \beta) \in \text{PRD}(Q, P)$ , then by Lemma [1](#page-10-2) there exists a distribution  $\mu$ such that for all  $\omega \in \Omega$  we have  $P(\omega) \ge \beta \mu(\omega)$  and  $Q(\omega) \ge \alpha \mu(\omega)$ .  $P(\omega) \ge \beta \mu(\omega)$  implies  $\text{supp}(\mu) \subseteq \text{supp}(P)$  and hence  $\sum_{\omega \in \text{supp}(P)} \mu(\omega) = 1$ . Together with  $Q(\omega) \ge \alpha \mu(\omega)$ , this yields  $Q(\text{supp}(P)) = \sum_{\omega \in \text{supp}(P)} Q(\omega) \ge \alpha \sum_{\omega \in \text{supp}(P)} \mu(\omega) = \alpha$  which implies  $\alpha \le Q(\text{supp}(P))$ for all  $(\alpha, \beta) \in \text{PRD}(Q, P)$ .

To prove the claim, we next show that there exists  $(\alpha, \beta) \in PRD(Q, P)$  with  $\alpha = Q(\text{supp}(P)).$ Let  $S = \text{supp}(P) \cap \text{supp}(Q)$ . If  $S = \emptyset$ , then  $\alpha = Q(\text{supp}(P)) = 0$  and  $(0, 0) \in \text{PRD}(Q, P)$  by Definition [2](#page-3-8) as claimed. For the case  $S \neq \emptyset$ , let  $\beta = \min_{\omega \in S} \frac{P(\omega)Q(S)}{Q(\omega)}$  $\frac{\omega(Q(S))}{Q(\omega)}$ . By definition of S, we have  $\beta > 0$ . Furthermore,  $\beta \le P(S) \le 1$  since  $\frac{P(\omega)}{P(S)} \le \frac{Q(\omega)}{Q(S)}$  $\frac{Q(\omega)}{Q(S)}$  for at least one  $\omega \in S$ . Consider the distribution  $\mu$  where  $\mu(\omega) = \frac{Q(\omega)}{Q(S)}$  for all  $\omega \in S$  and  $\mu(\omega) = 0$  for  $\omega \in \Omega \setminus S$ . By construction,  $\mu$ satisfies [\(5\)](#page-10-1) in Lemma [1](#page-10-2) and hence  $(\alpha, \beta) \in \text{PRD}(Q, P)$  as claimed.

*[\(iv\)](#page-3-6) Maximum recall*: This follows directly from applying Property [\(vi\)](#page-3-7) to Property [\(iii\).](#page-3-5)

*[\(v\)](#page-3-3) Monotonicity*: If  $(\alpha, \beta) \in \text{PRD}(Q, P)$ , then by Lemma [1](#page-10-2) there exists a distribution  $\mu$  such that for all  $\omega \in \Omega$  we have that  $P(\omega) \ge \beta \mu(\omega)$  and  $Q(\omega) \ge \alpha \mu(\omega)$ . For  $\alpha' \in (0, \alpha]$  and  $\beta' \in (0, \beta]$ , it follows that  $P(\omega) \ge \beta' \mu(\omega)$  and  $Q(\omega) \ge \alpha' \mu(\omega)$  for all  $\omega \in \Omega$ . By Lemma [1](#page-10-2) this implies  $(\alpha', \beta') \in \text{PRD}(\hat{Q}, P)$  as claimed.

*[\(vi\)](#page-3-7) Duality*: This follows directly from switching  $\alpha$  with  $\beta$ , P with Q and  $\nu_P$  with  $\nu_Q$  in Definition [1.](#page-3-1)

 $\Box$ 

#### <span id="page-11-0"></span>A.2 Proof of Theorem [2](#page-4-0)

*Proof.* We first show that  $\text{PRD}(Q, P) \subseteq \{(\theta \alpha(\lambda), \theta \beta(\lambda)) | \lambda \in (0, \infty), \theta \in [0, 1]\}$ . We consider an arbitrary element  $(\alpha', \beta') \in \text{PRD}(Q, P)$  and show that  $(\alpha', \beta') = (\theta \alpha(\lambda), \theta \beta(\lambda))$  for some  $\lambda \in (0,\infty)$  and  $\theta \in [0,1]$ . For the case  $(\alpha', \beta') = (0,0)$ , the result holds trivially for the choice of  $\lambda = 1$  and  $\theta = 0$ . For the case  $(\alpha', \beta') \neq (0, 0)$ , we choose  $\lambda = \frac{\alpha'}{\beta'}$  and  $\theta = \frac{\beta'}{\beta(\lambda)}$  $\frac{\beta}{\beta(\lambda)}$ . Since  $\alpha(\lambda) = \lambda \beta(\lambda)$  by definition, this implies  $(\alpha', \beta') = (\theta \alpha(\lambda), \theta \beta(\lambda))$  as required. Furthermore,  $\lambda \in (0, \infty)$  since by Definitions [1](#page-3-1) and [2](#page-3-8)  $\alpha' > 0$  if and only if  $\beta' > 0$ . Similarly, we show that  $\theta \in [0,1]$  $\theta \in [0,1]$  $\theta \in [0,1]$ : By Lemma 1 there exists a distribution  $\mu$  such that  $\beta' \mu(\omega) \le P(\omega)$  and  $\alpha' \mu(\omega) \le Q(\omega)$ for all  $\omega \in \Omega$ . This implies that  $\beta' \mu(\omega) \leq \frac{Q(\omega)}{\lambda}$  $\frac{d^2\omega}{dx^2}$  and thus  $\beta'\mu(\omega) \leq \min\left(P(\omega), \frac{Q(\omega)}{\lambda}\right)$  $\left(\frac{(\omega)}{\lambda}\right)$  for all  $\omega \in \Omega$ . Summing over all  $\omega \in \Omega$ , we obtain  $\beta' \leq \sum_{\omega \in \Omega} \min\left(P(\omega), \frac{Q(\omega)}{\lambda}\right)$  $\left(\frac{(\omega)}{\lambda}\right) = \beta(\lambda)$  which implies  $\theta \in [0, 1].$ 

Finally, we show that  $\text{PRD}(Q, P) \supseteq \{(\theta \alpha(\lambda), \theta \beta(\lambda)) \mid \lambda \in (0, \infty), \theta \in [0, 1]\}.$  Consider arbitrary  $\lambda \in (0,\infty)$  and  $\theta \in [0,1]$ . If  $\beta(\lambda) = 0$ , the claim holds trivially since  $(0,0) \in PRD(Q, P)$ . Otherwise, define the distribution  $\mu$  by  $\mu(\omega) = \min\left(P(\omega), \frac{Q(\omega)}{\lambda}\right)$  $\left(\frac{(\omega)}{\lambda}\right)$  / $\beta(\lambda)$  for all  $\omega \in \Omega$ . By definition,  $\beta(\lambda)\mu(\omega) \leq \min\left(P(\omega), \frac{Q(\omega)}{\lambda}\right)$  $\left(\frac{d\omega}{\lambda}\right) \leq P(\omega)$  for all  $\omega \in \Omega$ . Similarly,  $\alpha(\lambda)\mu(\omega) \leq$  $\min(\lambda P(\omega), Q(\omega)) \leq Q(\omega)$  for all  $\omega \in \Omega$  since  $\alpha(\lambda) = \lambda \beta(\lambda)$ . Because  $\theta \in [0,1]$ , this implies  $\theta \beta(\lambda)\mu(\omega) \leq P(\omega)$  and  $\theta \alpha(\lambda)\mu(\omega) \leq Q(\omega)$  for all  $\omega \in \Omega$ . Hence, by Lemma [1,](#page-10-2)  $(\theta \alpha(\lambda), \theta \beta(\lambda)) \in \text{PRD}(Q, P)$  for all  $\lambda \in (0, \infty)$  and  $\theta \in [0, 1]$  as claimed.

## B Further figures

<span id="page-11-1"></span>Image /page/11/Figure/4 description: The image displays a set of three plots. The leftmost plot is a grid of handwritten digits, all appearing to be the digit '1', with some variations in slant. Below this grid is a bar chart showing a single bar at class label '1' reaching a height of 10k. The central plot is a precision-recall curve with 'Recall' on the x-axis (ranging from 0.00 to 0.20) and 'Precision' on the y-axis (ranging from 0.0 to 1.0). Two curves are plotted: a blue curve labeled 'left' and a green curve labeled 'right'. Both curves start at a precision of 1.0 at a recall of 0.0 and decrease as recall increases. The rightmost plot is another grid of handwritten digits, also appearing to be the digit '1', with similar variations in slant as the leftmost grid. Below this grid is another bar chart, identical to the one on the left, showing a single bar at class label '1' reaching a height of 10k.

Figure 8: Comparing a pair of GANs on MNIST which have both collapsed to producing 1's. An analysis with a trained classifier as in Section [4.2](#page-6-2) comes to the same conclusion for both models, namely, that they have collapsed to producing 1's only. However, the PRD curve shows that the model on the right has a slightly higher recall. This is indeed correct: while the model on the left is producing straight 1's only, the model on the right is producing some more varied shapes such as tilted 1's.

<span id="page-12-0"></span>Image /page/12/Figure/0 description: The image displays a comparison between real and generated images, organized into four rows. The top row shows handwritten digits, with the left side labeled 'Real images' and the right side labeled 'Generated images'. The second row presents collections of clothing items, again divided into real and generated examples. The third and fourth rows showcase grids of diverse images, including animals, vehicles, and people, with the left column representing real samples and the right column representing generated samples. The figure is captioned as 'Figure 9: Clustering the real and generated samples from a GAN in feature space (10 cluster centers for each dataset).'

Figure 9: Clustering the real and generated samples from a GAN in feature space (10 cluster centers for visualization) yields the clusters above for the data sets MNIST, Fashion-MNIST, CIFAR-10 and CelebA. Although the GAN samples are not perfect, they are clustered in a meaningful way.

<span id="page-13-1"></span>Image /page/13/Figure/0 description: The image contains two scatter plots side-by-side. The left plot has 'F1/8 (Precision)' on the x-axis, ranging from 0.0 to 1.0, and 'P(y|x)' on the y-axis, ranging from 0.0 to 0.6. The right plot has 'F8 (Recall)' on the x-axis, ranging from 0.0 to 1.0, and 'P(y)' on the y-axis, ranging from 0.0 to 2.5. Both plots display a collection of blue, semi-transparent data points.

Figure 10: Comparing our unsupervised  $F_{1/8}$  and  $F_8$  measures with the supervised measures  $P(y|x)$  and  $P(y)$ similar to the IS (for a definition of  $F_\beta$ , see Section [4.3\)](#page-7-1). Each circle represents a trained generative model (GAN or VAE) on the MNIST data set. The values show a fairly high correlation with a Spearman rank correlation coefficient of -0.83 on the left and 0.89 on the right.

<span id="page-13-0"></span>Image /page/13/Figure/2 description: The image contains two rows of plots. The top row shows two plots. The left plot is a line graph with two y-axes. The left y-axis is labeled "Inception score" and ranges from 2.0 to 2.4. The right y-axis is labeled "FID" and ranges from 0 to 100. The x-axis is labeled "Number of classes in Q" and ranges from 1 to 10. A blue line with circular markers represents the "Inception score", and a green line with circular markers represents the "FID". The right plot is a precision-recall curve with the x-axis labeled "Recall" and the y-axis labeled "Precision", both ranging from 0.0 to 1.0. Multiple colored lines, labeled from 1 to 10 for "Q", are plotted on this graph. The bottom row also shows two plots. The left plot is similar to the top left plot, but the "Inception score" y-axis ranges from 2.5 to 4.5, and the "FID" y-axis ranges from 0 to 100. The x-axis is again labeled "Number of classes in Q" and ranges from 1 to 10. A blue line with circular markers represents the "Inception score", and a green line with circular markers represents the "FID". The right plot is another precision-recall curve, identical in its axes labels and ranges to the top right plot, with multiple colored lines labeled from 1 to 10 for "Q".

Figure 11: Corresponding plots as in Figure [5](#page-5-0) for the data sets MNIST (top) and Fashion-MNIST (bottom).

Image /page/14/Figure/0 description: The image displays three scatter plots, each comparing F1/8 (Precision) on the y-axis against F8 (Recall) on the x-axis. Each plot shows data points for GAN (blue) and VAE (green) models. Labeled points A, B, C, and D are present in each plot. To the right of each scatter plot are grids of images. The top grid shows MNIST digits, with panels labeled A, B, C, and D. Panel A displays generated digits, Panel B shows real digits, Panel C displays generated digits, and Panel D displays real digits. The middle grid shows CIFAR-10 images, with panels labeled A, B, C, and D, each containing a collection of generated images. The bottom grid shows CelebA images, with panels labeled A, B, C, and D, each containing a collection of generated images. The caption below the figure states: "Figure 12: Corresponding plots as in Figure 7 for the data sets MNIST (top), CIFAR-10 (middle) and CelebA."

Figure 12: Corresponding plots as in Figure [7](#page-7-0) for the data sets MNIST (top), CIFAR-10 (middle) and CelebA (bottom).