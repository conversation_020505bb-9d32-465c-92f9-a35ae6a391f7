{"table_of_contents": [{"title": "Regular cost functions", "heading_level": null, "page_id": 0, "polygon": [[133.5, 265.5], [267.75, 265.5], [267.75, 276.890625], [133.5, 276.890625]]}, {"title": "The <PERSON> condition", "heading_level": null, "page_id": 8, "polygon": [[133.1279296875, 48.0], [347.25, 48.0], [347.25, 59.6513671875], [133.1279296875, 59.6513671875]]}, {"title": "314 12 Smoothness", "heading_level": null, "page_id": 9, "polygon": [[133.1279296875, 26.15185546875], [229.6494140625, 26.15185546875], [229.6494140625, 35.4814453125], [133.1279296875, 35.4814453125]]}, {"title": "316 12 Smoothness", "heading_level": null, "page_id": 11, "polygon": [[133.5, 25.91015625], [229.201171875, 25.91015625], [229.201171875, 35.4814453125], [133.5, 35.4814453125]]}, {"title": "320 12 Smoothness", "heading_level": null, "page_id": 15, "polygon": [[132.15673828125, 26.2001953125], [230.09765625, 26.2001953125], [230.09765625, 35.6748046875], [132.15673828125, 35.6748046875]]}, {"title": "322 12 Smoothness", "heading_level": null, "page_id": 17, "polygon": [[132.6796875, 26.25], [229.798828125, 26.25], [229.798828125, 35.52978515625], [132.6796875, 35.52978515625]]}, {"title": "324 12 Smoothness", "heading_level": null, "page_id": 19, "polygon": [[133.5, 26.103515625], [229.798828125, 26.103515625], [229.798828125, 35.52978515625], [133.5, 35.52978515625]]}, {"title": "326 12 Smoothness", "heading_level": null, "page_id": 21, "polygon": [[133.5, 26.103515625], [229.201171875, 26.103515625], [229.201171875, 35.52978515625], [133.5, 35.52978515625]]}, {"title": "Differential formulation of c-convexity", "heading_level": null, "page_id": 22, "polygon": [[133.5, 390.75], [360.0, 390.75], [360.0, 402.1875], [133.5, 402.1875]]}, {"title": "328 12 Smoothness", "heading_level": null, "page_id": 23, "polygon": [[133.5, 26.25], [229.201171875, 26.25], [229.201171875, 35.771484375], [133.5, 35.771484375]]}, {"title": "Control of the gradient via c-convexity", "heading_level": null, "page_id": 24, "polygon": [[133.35205078125, 510.0], [364.5, 510.0], [364.5, 521.68359375], [133.35205078125, 521.68359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 70], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 740], ["Line", 38], ["TextInlineMath", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 701], ["Line", 39], ["TextInlineMath", 7], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 48], ["Equation", 5], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 25], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 664], ["Line", 38], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["Line", 39], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 727], ["Line", 37], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 37], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 632], ["Line", 70], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 50], ["TextInlineMath", 7], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 67], ["TextInlineMath", 4], ["Equation", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 57], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 777], ["Line", 58], ["TextInlineMath", 7], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 677], ["Line", 55], ["TextInlineMath", 5], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 760], ["Line", 46], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 807], ["Line", 86], ["TextInlineMath", 6], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1462, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 840], ["Line", 122], ["Equation", 5], ["TextInlineMath", 5], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2818, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 718], ["Line", 72], ["TextInlineMath", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 798], ["Line", 42], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 783], ["Line", 36], ["TextInlineMath", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 836], ["Line", 43], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 626], ["Line", 34], ["TextInlineMath", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 675], ["Line", 49], ["TextInlineMath", 8], ["Equation", 4], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 708], ["Line", 75], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1285, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 681], ["Line", 41], ["TextInlineMath", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-18"}