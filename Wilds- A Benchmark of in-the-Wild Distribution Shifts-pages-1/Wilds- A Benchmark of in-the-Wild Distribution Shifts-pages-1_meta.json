{"table_of_contents": [{"title": "Wilds: A Benchmark of in-the-Wild Distribution Shifts", "heading_level": null, "page_id": 0, "polygon": [[110.25, 99.75], [500.25, 99.75], [500.25, 113.501953125], [110.25, 113.501953125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[278.5078125, 488.25], [332.25, 488.25], [332.25, 499.640625], [278.5078125, 499.640625]]}, {"title": "Contents", "heading_level": null, "page_id": 1, "polygon": [[87.0, 87.0], [154.5, 87.0], [154.5, 103.3505859375], [87.0, 103.3505859375]]}, {"title": "D Additional experimental details 65", "heading_level": null, "page_id": 2, "polygon": [[87.0, 89.25], [525.0, 89.25], [525.0, 103.5], [87.0, 103.5]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 3, "polygon": [[88.5, 91.5], [180.0, 91.5], [180.0, 102.7705078125], [88.5, 102.7705078125]]}, {"title": "2. Existing ML benchmarks for distribution shifts", "heading_level": null, "page_id": 5, "polygon": [[89.25, 565.5], [384.29296875, 565.5], [384.29296875, 576.59765625], [89.25, 576.59765625]]}, {"title": "3. Problem settings", "heading_level": null, "page_id": 6, "polygon": [[88.5, 350.3671875], [206.19140625, 350.3671875], [206.19140625, 360.80859375], [88.5, 360.80859375]]}, {"title": "3.1 Domain generalization (Figure 1-Top)", "heading_level": null, "page_id": 6, "polygon": [[89.12548828125, 540.0], [300.0, 540.0], [300.0, 550.30078125], [89.12548828125, 550.30078125]]}, {"title": "3.2 Subpopulation shift (Figure 1-Bottom)", "heading_level": null, "page_id": 6, "polygon": [[88.5, 643.5], [305.25, 643.5], [305.25, 653.94140625], [88.5, 653.94140625]]}, {"title": "3.3 Hybrid settings", "heading_level": null, "page_id": 7, "polygon": [[89.25, 142.5], [188.859375, 142.5], [188.859375, 152.947265625], [89.25, 152.947265625]]}, {"title": "4. WILDS datasets", "heading_level": null, "page_id": 7, "polygon": [[89.25, 298.5], [198.0, 298.5], [198.0, 309.568359375], [89.25, 309.568359375]]}, {"title": "4.1 Domain generalization datasets", "heading_level": null, "page_id": 7, "polygon": [[88.5, 405.75], [267.75, 405.75], [267.75, 415.3359375], [88.5, 415.3359375]]}, {"title": "4.1.1 iWildCam2020-wilds: Species classification across different camera traps", "heading_level": null, "page_id": 7, "polygon": [[89.25, 422.25], [502.03125, 422.25], [502.03125, 432.73828125], [89.25, 432.73828125]]}, {"title": "4.1.2 Camelyon17-wilds: Tumor identification across different hospitals", "heading_level": null, "page_id": 7, "polygon": [[89.25, 644.25], [466.5, 644.25], [466.5, 654.71484375], [89.25, 654.71484375]]}, {"title": "4.1.3 RxRx1-wilds: Genetic perturbation classification across experimental batches", "heading_level": null, "page_id": 9, "polygon": [[89.25, 249.0], [522.75, 249.0], [522.75, 259.294921875], [89.25, 259.294921875]]}, {"title": "4.1.4 OGB-MolPCBA: Molecular property prediction across different scaffolds", "heading_level": null, "page_id": 10, "polygon": [[89.25, 165.0], [511.5, 165.0], [511.5, 175.376953125], [89.25, 175.376953125]]}, {"title": "4.1.5 GlobalWheat-wilds: Wheat head detection across regions of the world", "heading_level": null, "page_id": 10, "polygon": [[89.2001953125, 629.25], [494.859375, 629.25], [494.859375, 638.859375], [89.2001953125, 638.859375]]}, {"title": "4.2 Subpopulation shift datasets", "heading_level": null, "page_id": 12, "polygon": [[89.12548828125, 92.25], [253.107421875, 92.25], [253.107421875, 102.673828125], [89.12548828125, 102.673828125]]}, {"title": "4.3 Hybrid datasets", "heading_level": null, "page_id": 12, "polygon": [[89.25, 558.0], [191.25, 558.0], [191.25, 568.08984375], [89.25, 568.08984375]]}, {"title": "4.3.1 FMoW-wilds: Land use classification across different regions and years", "heading_level": null, "page_id": 12, "polygon": [[88.9013671875, 576.75], [496.5, 576.75], [496.5, 586.265625], [88.9013671875, 586.265625]]}, {"title": "4.3.2 PovertyMap-wilds: Poverty mapping across different countries", "heading_level": null, "page_id": 13, "polygon": [[89.2001953125, 468.0], [450.75, 469.5], [450.75, 479.14453125], [88.5, 479.14453125]]}, {"title": "4.3.3 Amazon-wilds: Sentiment classification across different users", "heading_level": null, "page_id": 14, "polygon": [[89.25, 604.5], [442.5, 605.25], [442.5, 615.65625], [88.5, 615.65625]]}, {"title": "4.3.4 Py150-wilds: Code completion across different codebases", "heading_level": null, "page_id": 15, "polygon": [[89.25, 189.75], [418.5, 189.75], [418.5, 199.16015625], [89.25, 199.16015625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 46], ["Text", 3], ["SectionHeader", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4503, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 40], ["TableCell", 35], ["SectionHeader", 1], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["TableCell", 34], ["Line", 19], ["SectionHeader", 1], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 70], ["Reference", 2], ["SectionHeader", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 971, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 91], ["Text", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1226, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 48], ["Text", 5], ["ListItem", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 46], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 44], ["Text", 6], ["SectionHeader", 5], ["Reference", 4], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["TableCell", 42], ["Line", 39], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2070, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 37], ["Text", 4], ["Reference", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 51], ["Text", 4], ["Reference", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1012, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 46], ["Text", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["TableCell", 96], ["Line", 51], ["Text", 5], ["Reference", 5], ["SectionHeader", 3], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1563, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 47], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 802, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["TableCell", 68], ["Line", 61], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 52], ["TableCell", 35], ["Text", 4], ["Reference", 2], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Wilds- A Benchmark of in-the-Wild Distribution Shifts-pages-1"}