{"table_of_contents": [{"title": "19 Undirected graphical models (Markov\nrandom fields)", "heading_level": null, "page_id": 0, "polygon": [[64.40625, 98.4814453125], [405.28125, 98.4814453125], [405.28125, 142.6201171875], [63.75, 142.6201171875]]}, {"title": "19.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 218.00390625], [96.75, 218.00390625]]}, {"title": "19.2 Conditional independence properties of UGMs", "heading_level": null, "page_id": 0, "polygon": [[93.5859375, 496.5], [369.0, 496.5], [369.0, 507.515625], [93.5859375, 507.515625]]}, {"title": "19.2.1 Key properties", "heading_level": null, "page_id": 0, "polygon": [[90.0, 520.5], [199.5, 520.5], [199.5, 530.9296875], [90.0, 530.9296875]]}, {"title": "19.2.2 An undirected alternative to d-separation", "heading_level": null, "page_id": 2, "polygon": [[86.9765625, 435.0], [324.75, 435.0], [324.75, 444.8671875], [86.9765625, 444.8671875]]}, {"title": "19.2.3 Comparing directed and undirected graphical models", "heading_level": null, "page_id": 3, "polygon": [[87.75, 435.0], [384.0, 435.0], [384.0, 445.18359375], [87.75, 445.18359375]]}, {"title": "19.3 Parameterization of MRFs", "heading_level": null, "page_id": 4, "polygon": [[95.25, 424.5], [264.9375, 423.75], [264.9375, 435.375], [95.25, 435.375]]}, {"title": "19.3.1 The <PERSON>sley-<PERSON> theorem", "heading_level": null, "page_id": 4, "polygon": [[90.0, 486.0], [291.75, 486.0], [291.75, 496.125], [90.0, 496.125]]}, {"title": "19.3.2 Representing potential functions", "heading_level": null, "page_id": 6, "polygon": [[88.5, 61.5], [285.75, 60.75], [285.75, 70.91455078125], [88.5, 72.18017578125]]}, {"title": "19.4 Examples of MRFs", "heading_level": null, "page_id": 7, "polygon": [[95.0625, 60.75], [225.75, 60.75], [225.75, 71.78466796875], [95.0625, 71.78466796875]]}, {"title": "19.4.1 Ising model", "heading_level": null, "page_id": 7, "polygon": [[90.0, 122.25], [186.1875, 121.5], [186.1875, 132.57421875], [90.75, 132.57421875]]}, {"title": "19.4.2 Hopfield networks", "heading_level": null, "page_id": 8, "polygon": [[88.734375, 258.75], [216.75, 258.75], [216.75, 269.103515625], [88.734375, 269.103515625]]}, {"title": "19.4.3 Potts model", "heading_level": null, "page_id": 10, "polygon": [[89.15625, 218.25], [187.59375, 218.25], [187.59375, 227.8125], [89.15625, 227.8125]]}, {"title": "19.4.4 Gaussian MRFs", "heading_level": null, "page_id": 11, "polygon": [[88.171875, 207.0], [202.359375, 207.0], [202.359375, 217.6875], [88.171875, 217.6875]]}, {"title": "19.4.4.1 Comparing Gaussian DGMs and UGMs *", "heading_level": null, "page_id": 11, "polygon": [[85.21875, 519.75], [301.5, 519.75], [301.5, 529.6640625], [85.21875, 529.6640625]]}, {"title": "19.4.5 Markov logic networks *", "heading_level": null, "page_id": 13, "polygon": [[89.25, 487.5], [245.25, 487.5], [245.25, 497.70703125], [89.25, 497.70703125]]}, {"title": "19.5 Learning", "heading_level": null, "page_id": 15, "polygon": [[94.5, 441.0], [177.0, 441.0], [177.0, 452.77734375], [94.5, 452.77734375]]}, {"title": "19.5.1 Training maxent models using gradient methods", "heading_level": null, "page_id": 15, "polygon": [[90.0, 513.75], [360.75, 513.75], [360.75, 523.96875], [90.0, 523.96875]]}, {"title": "19.5.2 Training partially observed maxent models", "heading_level": null, "page_id": 16, "polygon": [[87.75, 521.25], [333.75, 521.25], [333.75, 532.1953125], [87.75, 532.1953125]]}, {"title": "19.5.3 Approximate methods for computing the MLEs of MRFs", "heading_level": null, "page_id": 17, "polygon": [[87.75, 373.5], [393.0, 373.5], [393.0, 383.484375], [87.75, 383.484375]]}, {"title": "19.5.4 Pseudo likelihood", "heading_level": null, "page_id": 17, "polygon": [[89.25, 465.75], [215.25, 465.75], [215.25, 476.19140625], [89.25, 476.19140625]]}, {"title": "19.5.5 Stochastic maximum likelihood", "heading_level": null, "page_id": 18, "polygon": [[87.75, 537.75], [278.25, 537.75], [278.25, 547.69921875], [87.75, 547.69921875]]}, {"title": "19.5.6 Feature induction for maxent models *", "heading_level": null, "page_id": 19, "polygon": [[87.75, 443.25], [314.25, 443.25], [314.25, 453.09375], [87.75, 453.09375]]}, {"title": "19.5.7 Iterative proportional fitting (IPF) *", "heading_level": null, "page_id": 20, "polygon": [[89.25, 494.25], [295.5, 494.25], [295.5, 504.3515625], [89.25, 504.3515625]]}, {"title": "Algorithm 19.2: Iterative Proportional Fitting algorithm for tabular MRFs", "heading_level": null, "page_id": 21, "polygon": [[133.171875, 345.75], [428.25, 345.75], [428.25, 355.0078125], [133.171875, 355.0078125]]}, {"title": "******** Example", "heading_level": null, "page_id": 21, "polygon": [[84.75, 486.75], [168.75, 486.75], [168.75, 496.44140625], [84.75, 496.44140625]]}, {"title": "******** Speed of IPF", "heading_level": null, "page_id": 22, "polygon": [[82.546875, 220.376953125], [186.046875, 220.376953125], [186.046875, 229.869140625], [82.546875, 229.869140625]]}, {"title": "******** Generalizations of IPF", "heading_level": null, "page_id": 22, "polygon": [[84.0, 419.25], [227.390625, 419.25], [227.390625, 429.36328125], [84.0, 429.36328125]]}, {"title": "******** IPF for decomposable graphical models", "heading_level": null, "page_id": 22, "polygon": [[84.0, 499.5], [301.5, 499.5], [301.5, 509.73046875], [84.0, 509.73046875]]}, {"title": "19.6 Conditional random fields (CRFs)", "heading_level": null, "page_id": 23, "polygon": [[94.5, 208.5], [302.25, 208.5], [302.25, 219.427734375], [94.5, 219.427734375]]}, {"title": "19.6.1 Chain-structured CRFs, MEMMs and the label-bias problem", "heading_level": null, "page_id": 23, "polygon": [[89.25, 546.75], [408.75, 546.75], [408.75, 557.82421875], [89.25, 557.82421875]]}, {"title": "19.6.2 Applications of CRFs", "heading_level": null, "page_id": 25, "polygon": [[87.75, 466.5], [228.75, 466.5], [228.75, 476.82421875], [87.75, 476.82421875]]}, {"title": "******** Handwriting recognition", "heading_level": null, "page_id": 25, "polygon": [[84.75, 534.75], [237.65625, 534.75], [237.65625, 545.484375], [84.75, 545.484375]]}, {"title": "******** Noun phrase chunking", "heading_level": null, "page_id": 26, "polygon": [[82.5, 314.25], [230.0625, 314.25], [230.0625, 324.31640625], [82.5, 324.31640625]]}, {"title": "19.6.2.3 Named entity recognition", "heading_level": null, "page_id": 27, "polygon": [[83.25, 362.25], [240.75, 362.25], [240.75, 372.09375], [83.25, 372.09375]]}, {"title": "19.6.2.4 Natural language parsing", "heading_level": null, "page_id": 28, "polygon": [[83.25, 275.25], [239.25, 275.25], [239.25, 285.3984375], [83.25, 285.3984375]]}, {"title": "19.6.2.5 Hierarchical classification", "heading_level": null, "page_id": 28, "polygon": [[83.25, 475.5], [242.25, 475.5], [242.25, 485.3671875], [83.25, 485.3671875]]}, {"title": "19.6.2.6 Protein side-chain prediction", "heading_level": null, "page_id": 29, "polygon": [[82.5, 286.5], [257.25, 286.5], [257.25, 296.630859375], [82.5, 296.630859375]]}, {"title": "19.6.2.7 Stereo vision", "heading_level": null, "page_id": 29, "polygon": [[83.671875, 475.5], [187.3125, 475.5], [187.3125, 485.68359375], [83.671875, 485.68359375]]}, {"title": "19.6.3 CRF training", "heading_level": null, "page_id": 31, "polygon": [[87.75, 323.25], [192.0, 323.25], [192.0, 334.125], [87.75, 334.125]]}, {"title": "19.7 Structural SVMs", "heading_level": null, "page_id": 32, "polygon": [[95.25, 381.0], [213.0, 381.0], [213.0, 392.34375], [95.25, 392.34375]]}, {"title": "19.7.1 SSVMs: a probabilistic view", "heading_level": null, "page_id": 32, "polygon": [[90.0, 525.75], [261.0, 525.0], [261.0, 535.67578125], [90.0, 535.67578125]]}, {"title": "19.7.2 SSVMs: a non-probabilistic view", "heading_level": null, "page_id": 34, "polygon": [[89.25, 485.25], [282.0, 485.25], [282.75, 495.4921875], [89.25, 495.4921875]]}, {"title": "19.7.2.1 Empirical risk minimization", "heading_level": null, "page_id": 36, "polygon": [[84.75, 165.0], [252.75, 165.0], [252.75, 175.130859375], [84.75, 175.130859375]]}, {"title": "19.7.2.2 Computational issues", "heading_level": null, "page_id": 36, "polygon": [[84.0, 441.0], [222.75, 441.0], [222.75, 451.1953125], [84.0, 451.1953125]]}, {"title": "19.7.3 Cutting plane methods for fitting SSVMs", "heading_level": null, "page_id": 37, "polygon": [[89.015625, 286.5], [321.0, 286.5], [321.0, 296.7890625], [89.015625, 296.7890625]]}, {"title": "19.7.3.1 Loss-augmented decoding", "heading_level": null, "page_id": 37, "polygon": [[85.359375, 498.75], [242.296875, 498.75], [242.296875, 508.46484375], [85.359375, 508.46484375]]}, {"title": "19.7.3.2 A linear time algorithm", "heading_level": null, "page_id": 38, "polygon": [[84.0, 489.0], [232.5, 489.0], [232.5, 498.65625], [84.0, 498.65625]]}, {"title": "19.7.4 Online algorithms for fitting SSVMs", "heading_level": null, "page_id": 39, "polygon": [[89.25, 474.0], [297.75, 474.0], [297.75, 484.1015625], [89.25, 484.1015625]]}, {"title": "19.7.4.1 The structured perceptron algorithm", "heading_level": null, "page_id": 39, "polygon": [[85.5, 543.0], [288.75, 543.0], [288.75, 552.4453125], [85.5, 552.4453125]]}, {"title": "19.7.4.2 Stochastic subgradient descent", "heading_level": null, "page_id": 40, "polygon": [[84.0, 151.5], [264.75, 151.5], [264.75, 161.6044921875], [84.0, 161.6044921875]]}, {"title": "19.7.5 Latent structural SVMs", "heading_level": null, "page_id": 40, "polygon": [[89.15625, 448.5], [237.75, 448.5], [237.75, 458.47265625], [89.15625, 458.47265625]]}, {"title": "Algorithm 19.5: Concave-Convex Procedure (CCCP)", "heading_level": null, "page_id": 41, "polygon": [[133.171875, 390.75], [341.4375, 390.75], [341.4375, 399.9375], [133.171875, 399.9375]]}, {"title": "Exercises", "heading_level": null, "page_id": 42, "polygon": [[129.75, 174.75], [178.734375, 174.75], [178.734375, 185.888671875], [129.75, 185.888671875]]}, {"title": "Exercise 19.3 Independencies in Gaussian graphical models", "heading_level": null, "page_id": 43, "polygon": [[129.75, 116.25], [347.25, 116.25], [347.25, 125.455078125], [129.75, 125.455078125]]}, {"title": "Exercise 19.4 Cost of training MRFs and CRFs", "heading_level": null, "page_id": 43, "polygon": [[129.75, 402.0], [298.5, 402.0], [298.5, 410.37890625], [129.75, 410.37890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 31], ["SectionHeader", 4], ["Text", 3], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9128, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 345], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Equation", 2], ["ListItem", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1620, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["ListItem", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1317, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 652, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 50], ["Text", 9], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 47], ["Text", 9], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 44], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 48], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 20], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Picture", 1], ["Figure", 1], ["Text", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1339, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 47], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 47], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 93], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3457, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 32], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 45], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 76], ["Text", 9], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 90], ["Text", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["TableCell", 72], ["Line", 39], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Equation", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["TableCell", 59], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 6147, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 42], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 40], ["TableCell", 32], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1148, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 39], ["TableCell", 32], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 49], ["Text", 10], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 50], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 783, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 40], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 192], ["Line", 49], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 39], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 883, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 44], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 822, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 57], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 57], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 61], ["Text", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 602], ["Line", 76], ["Equation", 9], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 67], ["TextInlineMath", 6], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 580], ["Line", 66], ["Equation", 8], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 49], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 36], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 53], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1143, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 45], ["Text", 7], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 581], ["Line", 58], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 561], ["Line", 62], ["Text", 10], ["TextInlineMath", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 89], ["ListItem", 8], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-23"}