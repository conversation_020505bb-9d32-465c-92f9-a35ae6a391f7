Image /page/0/Picture/0 description: The image is a title slide for a presentation or document. The number 5 is displayed prominently in red at the top, followed by the words "Neural Networks" in black text. The background is a textured, abstract pattern resembling rippling water or a metallic surface with a golden hue.

In Chapters 3 and 4 we considered models for regression and classification that comprised linear combinations of fixed basis functions. We saw that such models have useful analytical and computational properties but that their practical applicability was limited by the curse of dimensionality. In order to apply such models to largescale problems, it is necessary to adapt the basis functions to the data.

Support vector machines (SVMs), discussed in Chapter 7, address this by first defining basis functions that are centred on the training data points and then selecting a subset of these during training. One advantage of SVMs is that, although the training involves nonlinear optimization, the objective function is convex, and so the solution of the optimization problem is relatively straightforward. The number of basis functions in the resulting models is generally much smaller than the number of training points, although it is often still relatively large and typically increases with the size of the training set. The relevance vector machine, discussed in Section 7.2, also chooses a subset from a fixed set of basis functions and typically results in much sparser models. Unlike the SVM it also produces probabilistic outputs, although this is at the expense of a nonconvex optimization during training.

An alternative approach is to fix the number of basis functions in advance but allow them to be adaptive, in other words to use parametric forms for the basis functions in which the parameter values are adapted during training. The most successful model of this type in the context of pattern recognition is the feed-forward neural network, also known as the *multilayer perceptron*, discussed in this chapter. In fact, 'multilayer perceptron' is really a misnomer, because the model comprises multiple layers of logistic regression models (with continuous nonlinearities) rather than multiple perceptrons (with discontinuous nonlinearities). For many applications, the resulting model can be significantly more compact, and hence faster to evaluate, than a support vector machine having the same generalization performance. The price to be paid for this compactness, as with the relevance vector machine, is that the likelihood function, which forms the basis for network training, is no longer a convex function of the model parameters. In practice, however, it is often worth investing substantial computational resources during the training phase in order to obtain a compact model that is fast at processing new data.

The term 'neural network' has its origins in attempts to find mathematical representations of information processing in biological systems (McCulloch and Pitts, 1943; Widrow and Hoff, 1960; Rosenblatt, 1962; Rumelhart *et al.*, 1986). Indeed, it has been used very broadly to cover a wide range of different models, many of which have been the subject of exaggerated claims regarding their biological plausibility. From the perspective of practical applications of pattern recognition, however, biological realism would impose entirely unnecessary constraints. Our focus in this chapter is therefore on neural networks as efficient models for statistical pattern recognition. In particular, we shall restrict our attention to the specific class of neural networks that have proven to be of greatest practical value, namely the multilayer perceptron.

We begin by considering the functional form of the network model, including the specific parameterization of the basis functions, and we then discuss the problem of determining the network parameters within a maximum likelihood framework, which involves the solution of a nonlinear optimization problem. This requires the evaluation of derivatives of the log likelihood function with respect to the network parameters, and we shall see how these can be obtained efficiently using the technique of *error backpropagation*. We shall also show how the backpropagation framework can be extended to allow other derivatives to be evaluated, such as the Jacobian and Hessian matrices. Next we discuss various approaches to regularization of neural network training and the relationships between them. We also consider some extensions to the neural network model, and in particular we describe a general framework for modelling conditional probability distributions known as *mixture density networks*. Finally, we discuss the use of Bayesian treatments of neural networks. Additional background on neural network models can be found in Bishop (1995a).

# **5.1. Feed-forward Network Functions**

The linear models for regression and classification discussed in Chapters 3 and 4, respectively, are based on linear combinations of fixed nonlinear basis functions  $\phi_i(\mathbf{x})$ and take the form

$$
y(\mathbf{x}, \mathbf{w}) = f\left(\sum_{j=1}^{M} w_j \phi_j(\mathbf{x})\right)
$$
 (5.1)

where  $f(\cdot)$  is a nonlinear activation function in the case of classification and is the identity in the case of regression. Our goal is to extend this model by making the basis functions  $\phi_i(\mathbf{x})$  depend on parameters and then to allow these parameters to be adjusted, along with the coefficients  $\{w_i\}$ , during training. There are, of course, many ways to construct parametric nonlinear basis functions. Neural networks use basis functions that follow the same form as (5.1), so that each basis function is itself a nonlinear function of a linear combination of the inputs, where the coefficients in the linear combination are adaptive parameters.

This leads to the basic neural network model, which can be described a series of functional transformations. First we construct  $M$  linear combinations of the input variables  $x_1, \ldots, x_D$  in the form

$$
a_j = \sum_{i=1}^{D} w_{ji}^{(1)} x_i + w_{j0}^{(1)}
$$
 (5.2)

where  $j = 1, \ldots, M$ , and the superscript (1) indicates that the corresponding parameters are in the first 'layer' of the network. We shall refer to the parameters  $w_{ji}^{(1)}$  as *weights* and the parameters  $w_{j0}^{(1)}$  as *biases*, following the nomenclature of Chapter 3. The quantities  $a_j$  are known as *activations*. Each of them is then transformed using a differentiable, nonlinear *activation function*  $h(\cdot)$  to give

$$
z_j = h(a_j). \tag{5.3}
$$

These quantities correspond to the outputs of the basis functions in (5.1) that, in the context of neural networks, are called *hidden units*. The nonlinear functions  $h(\cdot)$  are generally chosen to be sigmoidal functions such as the logistic sigmoid or the 'tanh' *Exercise 5.1* function. Following (5.1), these values are again linearly combined to give *output unit activations*

$$
a_k = \sum_{j=1}^{M} w_{kj}^{(2)} z_j + w_{k0}^{(2)}
$$
 (5.4)

where  $k = 1, \ldots, K$ , and K is the total number of outputs. This transformation corresponds to the second layer of the network, and again the  $w_{k0}^{(2)}$  are bias parameters. Finally, the output unit activations are transformed using an appropriate activation function to give a set of network outputs  $y_k$ . The choice of activation function is determined by the nature of the data and the assumed distribution of target variables

*Exercise 5.1*

**Figure 5.1** Network diagram for the twolayer neural network corresponding to (5.7). The input, hidden, and output variables are represented by nodes, and the weight parameters are represented by links between the nodes, in which the bias parameters are denoted by links coming from additional input and hidden variables  $x_0$  and  $z_0$ . Arrows denote the direction of information flow through the network during forward propagation.

Image /page/3/Figure/2 description: This is a diagram of a neural network. The network has an input layer with nodes labeled x0, x1, and xD. There is a hidden layer with nodes labeled z0, z1, and zM. The output layer has nodes labeled y1 and yK. Arrows labeled 'hidden units' point from the input layer to the hidden layer. Lines connect nodes between layers, representing weights. Some weights are labeled: w(1)MD, w(2)KM, and w(2)10. The terms 'inputs' and 'outputs' are also shown.

and follows the same considerations as for linear models discussed in Chapters 3 and 4. Thus for standard regression problems, the activation function is the identity so that  $y_k = a_k$ . Similarly, for multiple binary classification problems, each output unit activation is transformed using a logistic sigmoid function so that

$$
y_k = \sigma(a_k) \tag{5.5}
$$

where

$$
\sigma(a) = \frac{1}{1 + \exp(-a)}.\tag{5.6}
$$

Finally, for multiclass problems, a softmax activation function of the form (4.62) is used. The choice of output unit activation function is discussed in detail in Section 5.2.

We can combine these various stages to give the overall network function that, for sigmoidal output unit activation functions, takes the form

$$
y_k(\mathbf{x}, \mathbf{w}) = \sigma \left( \sum_{j=1}^M w_{kj}^{(2)} h\left(\sum_{i=1}^D w_{ji}^{(1)} x_i + w_{j0}^{(1)}\right) + w_{k0}^{(2)} \right) \tag{5.7}
$$

where the set of all weight and bias parameters have been grouped together into a vector **w**. Thus the neural network model is simply a nonlinear function from a set of input variables  $\{x_i\}$  to a set of output variables  $\{y_k\}$  controlled by a vector **w** of adjustable parameters.

This function can be represented in the form of a network diagram as shown in Figure 5.1. The process of evaluating (5.7) can then be interpreted as a *forward propagation* of information through the network. It should be emphasized that these diagrams do not represent probabilistic graphical models of the kind to be considered in Chapter 8 because the internal nodes represent deterministic variables rather than stochastic ones. For this reason, we have adopted a slightly different graphical

notation for the two kinds of model. We shall see later how to give a probabilistic interpretation to a neural network.

As discussed in Section 3.1, the bias parameters in (5.2) can be absorbed into the set of weight parameters by defining an additional input variable  $x_0$  whose value is clamped at  $x_0 = 1$ , so that (5.2) takes the form

$$
a_j = \sum_{i=0}^{D} w_{ji}^{(1)} x_i.
$$
 (5.8)

We can similarly absorb the second-layer biases into the second-layer weights, so that the overall network function becomes

$$
y_k(\mathbf{x}, \mathbf{w}) = \sigma \left( \sum_{j=0}^{M} w_{kj}^{(2)} h\left(\sum_{i=0}^{D} w_{ji}^{(1)} x_i\right) \right).
$$
 (5.9)

As can be seen from Figure 5.1, the neural network model comprises two stages of processing, each of which resembles the perceptron model of Section 4.1.7, and for this reason the neural network is also known as the *multilayer perceptron*, or MLP. A key difference compared to the perceptron, however, is that the neural network uses continuous sigmoidal nonlinearities in the hidden units, whereas the perceptron uses step-function nonlinearities. This means that the neural network function is differentiable with respect to the network parameters, and this property will play a central role in network training.

If the activation functions of all the hidden units in a network are taken to be linear, then for any such network we can always find an equivalent network without hidden units. This follows from the fact that the composition of successive linear transformations is itself a linear transformation. However, if the number of hidden units is smaller than either the number of input or output units, then the transformations that the network can generate are not the most general possible linear transformations from inputs to outputs because information is lost in the dimensionality reduction at the hidden units. In Section 12.4.2, we show that networks of linear units give rise to principal component analysis. In general, however, there is little interest in multilayer networks of linear units.

The network architecture shown in Figure 5.1 is the most commonly used one in practice. However, it is easily generalized, for instance by considering additional layers of processing each consisting of a weighted linear combination of the form (5.4) followed by an element-wise transformation using a nonlinear activation function. Note that there is some confusion in the literature regarding the terminology for counting the number of layers in such networks. Thus the network in Figure 5.1 may be described as a 3-layer network (which counts the number of layers of units, and treats the inputs as units) or sometimes as a single-hidden-layer network (which counts the number of layers of hidden units). We recommend a terminology in which Figure 5.1 is called a two-layer network, because it is the number of layers of adaptive weights that is important for determining the network properties.

Another generalization of the network architecture is to include *skip-layer* connections, each of which is associated with a corresponding adaptive parameter. For

**Figure 5.2** Example of a neural network having a general feed-forward topology. Note that each hidden and output unit has an associated bias parameter (omitted for clarity).

Image /page/5/Figure/2 description: This is a diagram of a neural network. The network has two input nodes labeled x1 and x2. There are three hidden layer nodes labeled z1, z2, and z3. There are two output nodes labeled y1 and y2. The input nodes are connected to the hidden layer nodes, and the hidden layer nodes are connected to the output nodes. A green arrow indicates the direction of information flow from left to right.

instance, in a two-layer network these would go directly from inputs to outputs. In principle, a network with sigmoidal hidden units can always mimic skip layer connections (for bounded input values) by using a sufficiently small first-layer weight that, over its operating range, the hidden unit is effectively linear, and then compensating with a large weight value from the hidden unit to the output. In practice, however, it may be advantageous to include skip-layer connections explicitly.

Furthermore, the network can be sparse, with not all possible connections within a layer being present. We shall see an example of a sparse network architecture when we consider convolutional neural networks in Section 5.5.6.

Because there is a direct correspondence between a network diagram and its mathematical function, we can develop more general network mappings by considering more complex network diagrams. However, these must be restricted to a *feed-forward* architecture, in other words to one having no closed directed cycles, to ensure that the outputs are deterministic functions of the inputs. This is illustrated with a simple example in Figure 5.2. Each (hidden or output) unit in such a network computes a function given by

$$
z_k = h\left(\sum_j w_{kj} z_j\right) \tag{5.10}
$$

where the sum runs over all units that send connections to unit  $k$  (and a bias parameter is included in the summation). For a given set of values applied to the inputs of the network, successive application of (5.10) allows the activations of all units in the network to be evaluated including those of the output units.

The approximation properties of feed-forward networks have been widely studied (Funahashi, 1989; Cybenko, 1989; Hornik *et al.*, 1989; Stinchecombe and White, 1989; Cotter, 1990; Ito, 1991; Hornik, 1991; Kreinovich, 1991; Ripley, 1996) and found to be very general. Neural networks are therefore said to be *universal approximators*. For example, a two-layer network with linear outputs can uniformly approximate any continuous function on a compact input domain to arbitrary accuracy provided the network has a sufficiently large number of hidden units. This result holds for a wide range of hidden unit activation functions, but excluding polynomials. Although such theorems are reassuring, the key problem is how to find suitable parameter values given a set of training data, and in later sections of this chapter we

**Figure 5.3** Illustration of the capability of a multilayer perceptron to approximate four different functions comprising (a)  $f(x) = x^2$ , (b)  $f(x) = \sin(x)$ , (c),  $f(x) = |x|$ , and (d)  $f(x) = H(x)$  where  $H(x)$ is the Heaviside step function. In each case,  $N = 50$  data points, shown as blue dots, have been sampled uniformly in  $x$  over the interval  $(-1, 1)$  and the corresponding values of  $f(x)$  evaluated. These data points are then used to train a twolayer network having 3 hidden units with 'tanh' activation functions and linear output units. The resulting network functions are shown by the red curves, and the outputs of the three hidden units are shown by the three dashed curves.

Image /page/6/Figure/2 description: The image displays four plots labeled (a), (b), (c), and (d). Each plot contains a blue dotted line representing a curve, a solid red line approximating the curve, and two dashed lines, one green and one yellow, and one dashed purple line. Plot (a) shows a parabolic curve opening upwards. Plot (b) shows a sinusoidal curve. Plot (c) shows a V-shaped curve. Plot (d) shows a step function, transitioning from a low value to a high value over a narrow range.

will show that there exist effective solutions to this problem based on both maximum likelihood and Bayesian approaches.

The capability of a two-layer network to model a broad range of functions is illustrated in Figure 5.3. This figure also shows how individual hidden units work collaboratively to approximate the final function. The role of hidden units in a simple classification problem is illustrated in Figure 5.4 using the synthetic classification data set described in Appendix A.

### **5.1.1 Weight-space symmetries**

One property of feed-forward networks, which will play a role when we consider Bayesian model comparison, is that multiple distinct choices for the weight vector **w** can all give rise to the same mapping function from inputs to outputs (Chen *et al.*, 1993). Consider a two-layer network of the form shown in Figure 5.1 with  $M$  hidden units having 'tanh' activation functions and full connectivity in both layers. If we change the sign of all of the weights and the bias feeding into a particular hidden unit, then, for a given input pattern, the sign of the activation of the hidden unit will be reversed, because 'tanh' is an odd function, so that  $tanh(-a) = -tanh(a)$ . This transformation can be exactly compensated by changing the sign of all of the weights leading out of that hidden unit. Thus, by changing the signs of a particular group of weights (and a bias), the input–output mapping function represented by the network is unchanged, and so we have found two different weight vectors that give rise to the same mapping function. For  $M$  hidden units, there will be  $M$  such 'sign-flip'

**Figure 5.4** Example of the solution of a simple twoclass classification problem involving synthetic data using a neural network having two inputs, two hidden units with 'tanh' activation functions, and a single output having a logistic sigmoid activation function. The dashed blue lines show the  $z = 0.5$  contours for each of the hidden units, and the red line shows the  $y = 0.5$  decision surface for the network. For comparison, the green line denotes the optimal decision boundary computed from the distributions used to generate the data.

Image /page/7/Figure/2 description: This is a scatter plot showing two classes of data points, represented by blue circles and red crosses. The plot has x and y axes ranging from approximately -2.5 to 2.5 and -2.5 to 3, respectively. Several decision boundaries are plotted on the graph: a dashed blue line, a solid red curve, and a solid green curve. The blue circles are generally located in the lower-left portion of the plot, while the red crosses are more concentrated in the upper-right portion. The decision boundaries attempt to separate these two classes.

symmetries, and thus any given weight vector will be one of a set  $2^M$  equivalent weight vectors .

Similarly, imagine that we interchange the values of all of the weights (and the bias) leading both into and out of a particular hidden unit with the corresponding values of the weights (and bias) associated with a different hidden unit. Again, this clearly leaves the network input–output mapping function unchanged, but it corresponds to a different choice of weight vector. For  $M$  hidden units, any given weight vector will belong to a set of  $M!$  equivalent weight vectors associated with this interchange symmetry, corresponding to the  $M!$  different orderings of the hidden units. The network will therefore have an overall weight-space symmetry factor of  $M!2^M$ . For networks with more than two layers of weights, the total level of symmetry will be given by the product of such factors, one for each layer of hidden units.

It turns out that these factors account for all of the symmetries in weight space (except for possible accidental symmetries due to specific choices for the weight values). Furthermore, the existence of these symmetries is not a particular property of the 'tanh' function but applies to a wide range of activation functions (Kurková and Kainen, 1994). In many cases, these symmetries in weight space are of little practical consequence, although in Section 5.7 we shall encounter a situation in which we need to take them into account.

# **5.2. Network Training**

So far, we have viewed neural networks as a general class of parametric nonlinear functions from a vector **x** of input variables to a vector **y** of output variables. A simple approach to the problem of determining the network parameters is to make an analogy with the discussion of polynomial curve fitting in Section 1.1, and therefore to minimize a sum-of-squares error function. Given a training set comprising a set of input vectors  $\{x_n\}$ , where  $n = 1, \ldots, N$ , together with a corresponding set of

**5.2. Network Training 233**

target vectors  $\{\mathbf t_n\}$ , we minimize the error function

$$
E(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} ||\mathbf{y}(\mathbf{x}_n, \mathbf{w}) - \mathbf{t}_n||^2.
$$
 (5.11)

However, we can provide a much more general view of network training by first giving a probabilistic interpretation to the network outputs. We have already seen many advantages of using probabilistic predictions in Section 1.5.4. Here it will also provide us with a clearer motivation both for the choice of output unit nonlinearity and the choice of error function.

We start by discussing regression problems, and for the moment we consider a single target variable  $t$  that can take any real value. Following the discussions in Section 1.2.5 and 3.1, we assume that t has a Gaussian distribution with an  $\mathbf{x}$ dependent mean, which is given by the output of the neural network, so that

$$
p(t|\mathbf{x}, \mathbf{w}) = \mathcal{N}\left(t|y(\mathbf{x}, \mathbf{w}), \beta^{-1}\right)
$$
\n(5.12)

where  $\beta$  is the precision (inverse variance) of the Gaussian noise. Of course this is a somewhat restrictive assumption, and in Section 5.6 we shall see how to extend this approach to allow for more general conditional distributions. For the conditional distribution given by (5.12), it is sufficient to take the output unit activation function to be the identity, because such a network can approximate any continuous function from **x** to <sup>y</sup>. Given a data set of <sup>N</sup> independent, identically distributed observations  $\mathbf{X} = \{x_1, \ldots, x_N\}$ , along with corresponding target values  $\mathbf{t} = \{t_1, \ldots, t_N\}$ , we can construct the corresponding likelihood function

$$
p(\mathbf{t}|\mathbf{X}, \mathbf{w}, \beta) = \prod_{n=1}^{N} p(t_n | \mathbf{x}_n, \mathbf{w}, \beta).
$$

Taking the negative logarithm, we obtain the error function

$$
\frac{\beta}{2} \sum_{n=1}^{N} \{y(\mathbf{x}_n, \mathbf{w}) - t_n\}^2 - \frac{N}{2} \ln \beta + \frac{N}{2} \ln(2\pi)
$$
\n(5.13)

which can be used to learn the parameters **w** and  $\beta$ . In Section 5.7, we shall discuss the Bayesian treatment of neural networks, while here we consider a maximum likelihood approach. Note that in the neural networks literature, it is usual to consider the minimization of an error function rather than the maximization of the (log) likelihood, and so here we shall follow this convention. Consider first the determination of **w**. Maximizing the likelihood function is equivalent to minimizing the sum-of-squares error function given by

$$
E(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \{y(\mathbf{x}_n, \mathbf{w}) - t_n\}^2
$$
 (5.14)

where we have discarded additive and multiplicative constants. The value of **w** found by minimizing  $E(\mathbf{w})$  will be denoted  $\mathbf{w}_{\text{ML}}$  because it corresponds to the maximum likelihood solution. In practice, the nonlinearity of the network function  $y(\mathbf{x}_n, \mathbf{w})$ causes the error  $E(w)$  to be nonconvex, and so in practice local maxima of the likelihood may be found, corresponding to local minima of the error function, as discussed in Section 5.2.1.

Having found  $w_{ML}$ , the value of  $\beta$  can be found by minimizing the negative log likelihood to give

$$
\frac{1}{\beta_{\rm ML}} = \frac{1}{N} \sum_{n=1}^{N} \{y(\mathbf{x}_n, \mathbf{w}_{\rm ML}) - t_n\}^2.
$$
 (5.15)

Note that this can be evaluated once the iterative optimization required to find  $\mathbf{w}_{\text{ML}}$ is completed. If we have multiple target variables, and we assume that they are independent conditional on **x** and **w** with shared noise precision  $\beta$ , then the conditional distribution of the target values is given by

$$
p(\mathbf{t}|\mathbf{x}, \mathbf{w}) = \mathcal{N}\left(\mathbf{t}|\mathbf{y}(\mathbf{x}, \mathbf{w}), \beta^{-1}\mathbf{I}\right).
$$
 (5.16)

Following the same argument as for a single target variable, we see that the maximum likelihood weights are determined by minimizing the sum-of-squares error function *Exercise* 5.2 (5.11). The noise precision is then given by

$$
\frac{1}{\beta_{\rm ML}} = \frac{1}{NK} \sum_{n=1}^{N} ||\mathbf{y}(\mathbf{x}_n, \mathbf{w}_{\rm ML}) - \mathbf{t}_n||^2
$$
(5.17)

*Exercise 5.3*

where  $K$  is the number of target variables. The assumption of independence can be *Exercise* 5.3 dropped at the expense of a slightly more complex optimization problem.

> Recall from Section 4.3.6 that there is a natural pairing of the error function (given by the negative log likelihood) and the output unit activation function. In the regression case, we can view the network as having an output activation function that is the identity, so that  $y_k = a_k$ . The corresponding sum-of-squares error function has the property

$$
\frac{\partial E}{\partial a_k} = y_k - t_k \tag{5.18}
$$

which we shall make use of when discussing error backpropagation in Section 5.3.

Now consider the case of binary classification in which we have a single target variable t such that  $t = 1$  denotes class  $C_1$  and  $t = 0$  denotes class  $C_2$ . Following the discussion of canonical link functions in Section 4.3.6, we consider a network having a single output whose activation function is a logistic sigmoid

$$
y = \sigma(a) \equiv \frac{1}{1 + \exp(-a)}
$$
(5.19)

so that  $0 \leq y(\mathbf{x}, \mathbf{w}) \leq 1$ . We can interpret  $y(\mathbf{x}, \mathbf{w})$  as the conditional probability  $p(C_1|\mathbf{x})$ , with  $p(C_2|\mathbf{x})$  given by  $1 - y(\mathbf{x}, \mathbf{w})$ . The conditional distribution of targets given inputs is then a Bernoulli distribution of the form

$$
p(t|\mathbf{x}, \mathbf{w}) = y(\mathbf{x}, \mathbf{w})^t \left\{ 1 - y(\mathbf{x}, \mathbf{w}) \right\}^{1-t}.
$$
 (5.20)

**5.2. Network Training 235**

If we consider a training set of independent observations, then the error function, which is given by the negative log likelihood, is then a *cross-entropy* error function of the form  $\mathbf{v}$ 

$$
E(\mathbf{w}) = -\sum_{n=1}^{N} \{t_n \ln y_n + (1 - t_n) \ln(1 - y_n)\}
$$
 (5.21)

where  $y_n$  denotes  $y(\mathbf{x}_n, \mathbf{w})$ . Note that there is no analogue of the noise precision  $\beta$ because the target values are assumed to be correctly labelled. However, the model *Exercise 5.4* is easily extended to allow for labelling errors. Simard *et al.* (2003) found that using the cross-entropy error function instead of the sum-of-squares for a classification problem leads to faster training as well as improved generalization.

> If we have  $K$  separate binary classifications to perform, then we can use a network having  $K$  outputs each of which has a logistic sigmoid activation function. Associated with each output is a binary class label  $t_k \in \{0, 1\}$ , where  $k = 1, \ldots, K$ . If we assume that the class labels are independent, given the input vector, then the conditional distribution of the targets is

$$
p(\mathbf{t}|\mathbf{x}, \mathbf{w}) = \prod_{k=1}^{K} y_k(\mathbf{x}, \mathbf{w})^{t_k} \left[1 - y_k(\mathbf{x}, \mathbf{w})\right]^{1 - t_k}.
$$
 (5.22)

Taking the negative logarithm of the corresponding likelihood function then gives *Exercise* 5.5 the following error function

$$
E(\mathbf{w}) = -\sum_{n=1}^{N} \sum_{k=1}^{K} \{t_{nk} \ln y_{nk} + (1 - t_{nk}) \ln(1 - y_{nk})\}
$$
(5.23)

where  $y_{nk}$  denotes  $y_k(\mathbf{x}_n, \mathbf{w})$ . Again, the derivative of the error function with re-*Exercise* 5.6 spect to the activation for a particular output unit takes the form (5.18) just as in the regression case.

> It is interesting to contrast the neural network solution to this problem with the corresponding approach based on a linear classification model of the kind discussed in Chapter 4. Suppose that we are using a standard two-layer network of the kind shown in Figure 5.1. We see that the weight parameters in the first layer of the network are shared between the various outputs, whereas in the linear model each classification problem is solved independently. The first layer of the network can be viewed as performing a nonlinear feature extraction, and the sharing of features between the different outputs can save on computation and can also lead to improved generalization.

> Finally, we consider the standard multiclass classification problem in which each input is assigned to one of  $K$  mutually exclusive classes. The binary target variables  $t_k \in \{0, 1\}$  have a 1-of-K coding scheme indicating the class, and the network outputs are interpreted as  $y_k(\mathbf{x}, \mathbf{w}) = p(t_k = 1|\mathbf{x})$ , leading to the following error function

$$
E(\mathbf{w}) = -\sum_{n=1}^{N} \sum_{k=1}^{K} t_{kn} \ln y_k(\mathbf{x}_n, \mathbf{w}).
$$
 (5.24)

*Exercise 5.4*

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

**Figure 5.5** Geometrical view of the error function  $E(\mathbf{w})$  as a surface sitting over weight space. Point  $w_A$  is a local minimum and  $w_B$  is the global minimum. At any point  $w_C$ , the local gradient of the error surface is given by the vector  $\nabla E$ .

Image /page/11/Figure/2 description: This is a 3D plot illustrating an error function E(w) with respect to two parameters w1 and w2. The plot shows a surface with multiple minima, representing a complex error landscape. Three points, labeled wA, wB, and wC, are marked on the surface. wA and wB appear to be in local minima, while wC is at a higher point on the surface. A dashed line indicates the direction of the E(w) axis. A green arrow labeled ∇E points away from wC, indicating the direction of the negative gradient, which is used in optimization algorithms like gradient descent to find minima.

Following the discussion of Section 4.3.4, we see that the output unit activation function, which corresponds to the canonical link, is given by the softmax function

$$
y_k(\mathbf{x}, \mathbf{w}) = \frac{\exp(a_k(\mathbf{x}, \mathbf{w}))}{\sum_j \exp(a_j(\mathbf{x}, \mathbf{w}))}
$$
(5.25)

which satisfies  $0 \leq y_k \leq 1$  and  $\sum_k y_k = 1$ . Note that the  $y_k(\mathbf{x}, \mathbf{w})$  are unchanged<br>if a constant is added to all of the  $a_k(\mathbf{x}, \mathbf{w})$  causing the error function to be constant if a constant is added to all of the  $a_k(x, w)$ , causing the error function to be constant for some directions in weight space. This degeneracy is removed if an appropriate regularization term (Section 5.5) is added to the error function.

Once again, the derivative of the error function with respect to the activation for *Exercise 5.7* a particular output unit takes the familiar form (5.18).

> In summary, there is a natural choice of both output unit activation function and matching error function, according to the type of problem being solved. For regression we use linear outputs and a sum-of-squares error, for (multiple independent) binary classifications we use logistic sigmoid outputs and a cross-entropy error function, and for multiclass classification we use softmax outputs with the corresponding multiclass cross-entropy error function. For classification problems involving two classes, we can use a single logistic sigmoid output, or alternatively we can use a network with two outputs having a softmax output activation function.

### 5.2.2 Local quadratic approximation

We turn next to the task of finding a weight vector **w** which minimizes the chosen function  $E(\mathbf{w})$ . At this point, it is useful to have a geometrical picture of the error function, which we can view as a surface sitting over weight space as shown in Figure 5.5. First note that if we make a small step in weight space from **w** to  $\mathbf{w} + \delta \mathbf{w}$ then the change in the error function is  $\delta E \simeq \delta \mathbf{w}^T \nabla E(\mathbf{w})$ , where the vector  $\nabla E(\mathbf{w})$ points in the direction of greatest rate of increase of the error function. Because the error  $E(\mathbf{w})$  is a smooth continuous function of  $\mathbf{w}$ , its smallest value will occur at a

*Exercise 5.7*

point in weight space such that the gradient of the error function vanishes, so that

$$
\nabla E(\mathbf{w}) = 0 \tag{5.26}
$$

as otherwise we could make a small step in the direction of  $-\nabla E(\mathbf{w})$  and thereby further reduce the error. Points at which the gradient vanishes are called stationary points, and may be further classified into minima, maxima, and saddle points.

Our goal is to find a vector **w** such that  $E(\mathbf{w})$  takes its smallest value. However, the error function typically has a highly nonlinear dependence on the weights and bias parameters, and so there will be many points in weight space at which the gradient vanishes (or is numerically very small). Indeed, from the discussion in Section 5.1.1 we see that for any point **w** that is a local minimum, there will be other points in weight space that are equivalent minima. For instance, in a two-layer network of the kind shown in Figure 5.1, with  $M$  hidden units, each point in weight *Section 5.1.1* space is a member of a family of  $M!2^M$  equivalent points.

> Furthermore, there will typically be multiple inequivalent stationary points and in particular multiple inequivalent minima. A minimum that corresponds to the smallest value of the error function for any weight vector is said to be a *global minimum*. Any other minima corresponding to higher values of the error function are said to be *local minima*. For a successful application of neural networks, it may not be necessary to find the global minimum (and in general it will not be known whether the global minimum has been found) but it may be necessary to compare several local minima in order to find a sufficiently good solution.

> Because there is clearly no hope of finding an analytical solution to the equation  $\nabla E(\mathbf{w})=0$  we resort to iterative numerical procedures. The optimization of continuous nonlinear functions is a widely studied problem and there exists an extensive literature on how to solve it efficiently. Most techniques involve choosing some initial value  $\mathbf{w}^{(0)}$  for the weight vector and then moving through weight space in a succession of steps of the form

$$
\mathbf{w}^{(\tau+1)} = \mathbf{w}^{(\tau)} + \Delta \mathbf{w}^{(\tau)}
$$
\n(5.27)

where  $\tau$  labels the iteration step. Different algorithms involve different choices for the weight vector update  $\Delta \mathbf{w}^{(\tau)}$ . Many algorithms make use of gradient information and therefore require that after each update the value of  $\nabla E(\mathbf{w})$  is evaluated at and therefore require that, after each update, the value of  $\nabla E(\mathbf{w})$  is evaluated at the new weight vector  $\mathbf{w}^{(\tau+1)}$ . In order to understand the importance of gradient information, it is useful to consider a local approximation to the error function based on a Taylor expansion.

#### **5.2.2 Local quadratic approximation**

Insight into the optimization problem, and into the various techniques for solving it, can be obtained by considering a local quadratic approximation to the error function.

Consider the Taylor expansion of  $E(\mathbf{w})$  around some point  $\hat{\mathbf{w}}$  in weight space

$$
E(\mathbf{w}) \simeq E(\widehat{\mathbf{w}}) + (\mathbf{w} - \widehat{\mathbf{w}})^{\mathrm{T}} \mathbf{b} + \frac{1}{2} (\mathbf{w} - \widehat{\mathbf{w}})^{\mathrm{T}} \mathbf{H} (\mathbf{w} - \widehat{\mathbf{w}})
$$
(5.28)

*Section 5.1.1*

where cubic and higher terms have been omitted. Here **b** is defined to be the gradient of  $E$  evaluated at  $\hat{\mathbf{w}}$ 

$$
\mathbf{b} \equiv \nabla E|_{\mathbf{w} = \widehat{\mathbf{w}}}
$$
 (5.29)

and the Hessian matrix  $\mathbf{H} = \nabla \nabla E$  has elements

$$
(\mathbf{H})_{ij} \equiv \left. \frac{\partial E}{\partial w_i \partial w_j} \right|_{\mathbf{w} = \widehat{\mathbf{w}}}.
$$
\n(5.30)

From (5.28), the corresponding local approximation to the gradient is given by

$$
\nabla E \simeq \mathbf{b} + \mathbf{H}(\mathbf{w} - \widehat{\mathbf{w}}). \tag{5.31}
$$

For points w that are sufficiently close to  $\hat{\mathbf{w}}$ , these expressions will give reasonable approximations for the error and its gradient.

Consider the particular case of a local quadratic approximation around a point  $w^*$  that is a minimum of the error function. In this case there is no linear term, because  $\nabla E = 0$  at  $\mathbf{w}^*$ , and (5.28) becomes

$$
E(\mathbf{w}) = E(\mathbf{w}^*) + \frac{1}{2}(\mathbf{w} - \mathbf{w}^*)^{\mathrm{T}} \mathbf{H}(\mathbf{w} - \mathbf{w}^*)
$$
(5.32)

where the Hessian  $H$  is evaluated at  $w^*$ . In order to interpret this geometrically, consider the eigenvalue equation for the Hessian matrix

$$
\mathbf{H}\mathbf{u}_i = \lambda_i \mathbf{u}_i \tag{5.33}
$$

where the eigenvectors  $\mathbf{u}_i$  form a complete orthonormal set (Appendix C) so that

$$
\mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j = \delta_{ij}.\tag{5.34}
$$

We now expand  $(\mathbf{w} - \mathbf{w}^*)$  as a linear combination of the eigenvectors in the form

$$
\mathbf{w} - \mathbf{w}^* = \sum_i \alpha_i \mathbf{u}_i.
$$
 (5.35)

This can be regarded as a transformation of the coordinate system in which the origin is translated to the point  $w^*$ , and the axes are rotated to align with the eigenvectors (through the orthogonal matrix whose columns are the  $\mathbf{u}_i$ ), and is discussed in more detail in Appendix C. Substituting  $(5.35)$  into  $(5.32)$ , and using  $(5.33)$  and  $(5.34)$ , allows the error function to be written in the form

$$
E(\mathbf{w}) = E(\mathbf{w}^*) + \frac{1}{2} \sum_{i} \lambda_i \alpha_i^2.
$$
 (5.36)

A matrix **H** is said to be *positive definite* if, and only if,

$$
\mathbf{v}^{\mathrm{T}} \mathbf{H} \mathbf{v} > 0 \qquad \text{for all } \mathbf{v}. \tag{5.37}
$$

**Figure 5.6** In the neighbourhood of a min-  $w_2$ imum  $\mathbf{w}^*$ , the error function can be approximated by a quadratic. Contours of constant error are then ellipses whose axes are aligned with the eigenvectors  $\mathbf{u}_i$  of the Hessian matrix, with lengths that are inversely proportional to the square roots of the corresponding eigenvectors  $\lambda_i$ .

Image /page/14/Figure/2 description: This is a 2D plot showing an ellipse centered at w\*. The x-axis is labeled w1 and the y-axis is labeled w2. Two vectors, u1 and u2, are shown originating from w\*. The vector u1 is aligned with the major axis of the ellipse, and a double-headed arrow labeled \lambda\_1^{-1/2} is shown along the direction of u1, indicating the extent of the ellipse in that direction. Similarly, the vector u2 is aligned with the minor axis of the ellipse, and a double-headed arrow labeled \lambda\_2^{-1/2} is shown along the direction of u2, indicating the extent of the ellipse in that direction.

Because the eigenvectors  $\{u_i\}$  form a complete set, an arbitrary vector **v** can be written in the form

$$
\mathbf{v} = \sum_{i} c_i \mathbf{u}_i. \tag{5.38}
$$

From (5.33) and (5.34), we then have

$$
\mathbf{v}^{\mathrm{T}} \mathbf{H} \mathbf{v} = \sum_{i} c_i^2 \lambda_i
$$
 (5.39)

*Exercise 5.10* and so **H** will be positive definite if, and only if, all of its eigenvalues are positive. In the new coordinate system, whose basis vectors are given by the eigenvectors *Exercise 5.11*  $\{u_i\}$ , the contours of constant E are ellipses centred on the origin, as illustrated in Figure 5.6. For a one-dimensional weight space, a stationary point  $w^*$  will be a minimum if

$$
\left. \frac{\partial^2 E}{\partial w^2} \right|_{w^*} > 0. \tag{5.40}
$$

The corresponding result in D-dimensions is that the Hessian matrix, evaluated at *Exercise* 5.12  $w^*$ , should be positive definite.

### 5.2.3 Use of gradient information

As we shall see in Section 5.3, it is possible to evaluate the gradient of an error function efficiently by means of the backpropagation procedure. The use of this gradient information can lead to significant improvements in the speed with which the minima of the error function can be located. We can see why this is so, as follows.

In the quadratic approximation to the error function, given in (5.28), the error surface is specified by the quantities **b** and **H**, which contain a total of  $W(W +$ *Exercise 5.13* 3)/2 independent elements (because the matrix **H** is symmetric), where W is the dimensionality of **w** (i.e., the total number of adaptive parameters in the network). The location of the minimum of this quadratic approximation therefore depends on  $O(W^2)$  parameters, and we should not expect to be able to locate the minimum until we have gathered  $O(W^2)$  independent pieces of information. If we do not make use of gradient information, we would expect to have to perform  $O(W^2)$  function evaluations, each of which would require  $O(W)$  steps. Thus, the computational effort needed to find the minimum using such an approach would be  $O(W^3)$ .

Now compare this with an algorithm that makes use of the gradient information. Because each evaluation of  $\nabla E$  brings W items of information, we might hope to find the minimum of the function in  $O(W)$  gradient evaluations. As we shall see, by using error backpropagation, each such evaluation takes only  $O(W)$  steps and so the minimum can now be found in  $O(W^2)$  steps. For this reason, the use of gradient information forms the basis of practical algorithms for training neural networks.

### 5.2.4 Gradient descent optimization

The simplest approach to using gradient information is to choose the weight update in (5.27) to comprise a small step in the direction of the negative gradient, so that

$$
\mathbf{w}^{(\tau+1)} = \mathbf{w}^{(\tau)} - \eta \nabla E(\mathbf{w}^{(\tau)})
$$
(5.41)

where the parameter  $\eta > 0$  is known as the *learning rate*. After each such update, the gradient is re-evaluated for the new weight vector and the process repeated. Note that the error function is defined with respect to a training set, and so each step requires that the entire training set be processed in order to evaluate  $\nabla E$ . Techniques that use the whole data set at once are called *batch* methods. At each step the weight vector is moved in the direction of the greatest rate of decrease of the error function, and so this approach is known as *gradient descent* or *steepest descent*. Although such an approach might intuitively seem reasonable, in fact it turns out to be a poor algorithm, for reasons discussed in Bishop and Nabney (2008).

For batch optimization, there are more efficient methods, such as *conjugate gradients* and *quasi-Newton* methods, which are much more robust and much faster than simple gradient descent (Gill *et al.*, 1981; Fletcher, 1987; Nocedal and Wright, 1999). Unlike gradient descent, these algorithms have the property that the error function always decreases at each iteration unless the weight vector has arrived at a local or global minimum.

In order to find a sufficiently good minimum, it may be necessary to run a gradient-based algorithm multiple times, each time using a different randomly chosen starting point, and comparing the resulting performance on an independent validation set.

There is, however, an on-line version of gradient descent that has proved useful in practice for training neural networks on large data sets (Le Cun *et al.*, 1989). Error functions based on maximum likelihood for a set of independent observations comprise a sum of terms, one for each data point

$$
E(\mathbf{w}) = \sum_{n=1}^{N} E_n(\mathbf{w}).
$$
\n(5.42)

On-line gradient descent, also known as *sequential gradient descent* or *stochastic gradient descent*, makes an update to the weight vector based on one data point at a time, so that

$$
\mathbf{w}^{(\tau+1)} = \mathbf{w}^{(\tau)} - \eta \nabla E_n(\mathbf{w}^{(\tau)}).
$$
 (5.43)

This update is repeated by cycling through the data either in sequence or by selecting points at random with replacement. There are of course intermediate scenarios in which the updates are based on batches of data points.

One advantage of on-line methods compared to batch methods is that the former handle redundancy in the data much more efficiently. To see, this consider an extreme example in which we take a data set and double its size by duplicating every data point. Note that this simply multiplies the error function by a factor of 2 and so is equivalent to using the original error function. Batch methods will require double the computational effort to evaluate the batch error function gradient, whereas online methods will be unaffected. Another property of on-line gradient descent is the possibility of escaping from local minima, since a stationary point with respect to the error function for the whole data set will generally not be a stationary point for each data point individually.

Nonlinear optimization algorithms, and their practical application to neural network training, are discussed in detail in Bishop and Nabney (2008).

## **5.3. Error Backpropagation**

Our goal in this section is to find an efficient technique for evaluating the gradient of an error function  $E(w)$  for a feed-forward neural network. We shall see that this can be achieved using a local message passing scheme in which information is sent alternately forwards and backwards through the network and is known as *error backpropagation*, or sometimes simply as *backprop*.

It should be noted that the term backpropagation is used in the neural computing literature to mean a variety of different things. For instance, the multilayer perceptron architecture is sometimes called a backpropagation network. The term backpropagation is also used to describe the training of a multilayer perceptron using gradient descent applied to a sum-of-squares error function. In order to clarify the terminology, it is useful to consider the nature of the training process more carefully. Most training algorithms involve an iterative procedure for minimization of an error function, with adjustments to the weights being made in a sequence of steps. At each such step, we can distinguish between two distinct stages. In the first stage, the derivatives of the error function with respect to the weights must be evaluated. As we shall see, the important contribution of the backpropagation technique is in providing a computationally efficient method for evaluating such derivatives. Because it is at this stage that errors are propagated backwards through the network, we shall use the term backpropagation specifically to describe the evaluation of derivatives. In the second stage, the derivatives are then used to compute the adjustments to be made to the weights. The simplest such technique, and the one originally considered by Rumelhart *et al.* (1986), involves gradient descent. It is important to recognize that the two stages are distinct. Thus, the first stage, namely the propagation of errors backwards through the network in order to evaluate derivatives, can be applied to many other kinds of network and not just the multilayer perceptron. It can also be applied to error functions other that just the simple sum-of-squares, and to the eval-

uation of other derivatives such as the Jacobian and Hessian matrices, as we shall see later in this chapter. Similarly, the second stage of weight adjustment using the calculated derivatives can be tackled using a variety of optimization schemes, many of which are substantially more powerful than simple gradient descent.

### 5.3.1 Evaluation of error-function derivatives

We now derive the backpropagation algorithm for a general network having arbitrary feed-forward topology, arbitrary differentiable nonlinear activation functions, and a broad class of error function. The resulting formulae will then be illustrated using a simple layered network structure having a single layer of sigmoidal hidden units together with a sum-of-squares error.

Many error functions of practical interest, for instance those defined by maximum likelihood for a set of i.i.d. data, comprise a sum of terms, one for each data point in the training set, so that

$$
E(\mathbf{w}) = \sum_{n=1}^{N} E_n(\mathbf{w}).
$$
\n(5.44)

Here we shall consider the problem of evaluating  $\nabla E_n(\mathbf{w})$  for one such term in the error function. This may be used directly for sequential optimization, or the results can be accumulated over the training set in the case of batch methods.

Consider first a simple linear model in which the outputs  $y_k$  are linear combinations of the input variables  $x_i$  so that

$$
y_k = \sum_i w_{ki} x_i \tag{5.45}
$$

together with an error function that, for a particular input pattern  $n$ , takes the form

$$
E_n = \frac{1}{2} \sum_{k} (y_{nk} - t_{nk})^2
$$
\n(5.46)

where  $y_{nk} = y_k(\mathbf{x}_n, \mathbf{w})$ . The gradient of this error function with respect to a weight  $w_{ji}$  is given by

$$
\frac{\partial E_n}{\partial w_{ji}} = (y_{nj} - t_{nj})x_{ni}
$$
\n(5.47)

which can be interpreted as a 'local' computation involving the product of an 'error signal'  $y_{ni} - t_{ni}$  associated with the output end of the link  $w_{ii}$  and the variable  $x_{ni}$ associated with the input end of the link. In Section 4.3.2, we saw how a similar formula arises with the logistic sigmoid activation function together with the cross entropy error function, and similarly for the softmax activation function together with its matching cross-entropy error function. We shall now see how this simple result extends to the more complex setting of multilayer feed-forward networks.

In a general feed-forward network, each unit computes a weighted sum of its inputs of the form

$$
a_j = \sum_i w_{ji} z_i \tag{5.48}
$$

where  $z_i$  is the activation of a unit, or input, that sends a connection to unit j, and  $w_{ji}$ is the weight associated with that connection. In Section 5.1, we saw that biases can be included in this sum by introducing an extra unit, or input, with activation fixed at  $+1$ . We therefore do not need to deal with biases explicitly. The sum in (5.48) is transformed by a nonlinear activation function  $h(\cdot)$  to give the activation  $z_j$  of unit j in the form

$$
z_j = h(a_j). \tag{5.49}
$$

Note that one or more of the variables  $z_i$  in the sum in (5.48) could be an input, and similarly, the unit  $j$  in (5.49) could be an output.

For each pattern in the training set, we shall suppose that we have supplied the corresponding input vector to the network and calculated the activations of all of the hidden and output units in the network by successive application of (5.48) and (5.49). This process is often called *forward propagation* because it can be regarded as a forward flow of information through the network.

Now consider the evaluation of the derivative of  $E_n$  with respect to a weight  $w_{ji}$ . The outputs of the various units will depend on the particular input pattern n. However, in order to keep the notation uncluttered, we shall omit the subscript  $n$ from the network variables. First we note that  $E_n$  depends on the weight  $w_{ii}$  only via the summed input  $a_j$  to unit j. We can therefore apply the chain rule for partial derivatives to give

$$
\frac{\partial E_n}{\partial w_{ji}} = \frac{\partial E_n}{\partial a_j} \frac{\partial a_j}{\partial w_{ji}}.
$$
\n(5.50)

We now introduce a useful notation

$$
\delta_j \equiv \frac{\partial E_n}{\partial a_j} \tag{5.51}
$$

where the δ's are often referred to as *errors* for reasons we shall see shortly. Using (5.48), we can write

$$
\frac{\partial a_j}{\partial w_{ji}} = z_i.
$$
\n(5.52)

Substituting  $(5.51)$  and  $(5.52)$  into  $(5.50)$ , we then obtain

$$
\frac{\partial E_n}{\partial w_{ji}} = \delta_j z_i.
$$
\n(5.53)

Equation (5.53) tells us that the required derivative is obtained simply by multiplying the value of  $\delta$  for the unit at the output end of the weight by the value of z for the unit at the input end of the weight (where  $z = 1$  in the case of a bias). Note that this takes the same form as for the simple linear model considered at the start of this section. Thus, in order to evaluate the derivatives, we need only to calculate the value of  $\delta_i$ for each hidden and output unit in the network, and then apply (5.53).

As we have seen already, for the output units, we have

$$
\delta_k = y_k - t_k \tag{5.54}
$$

**Figure 5.7** Illustration of the calculation of  $\delta_j$  for hidden unit j by backpropagation of the  $\delta$ 's from those units  $k$  to which unit  $i$  sends connections. The blue arrow denotes the direction of information flow during forward propagation, and the red arrows indicate the backward propagation of error information.

Image /page/19/Figure/2 description: This is a diagram illustrating a neural network's backpropagation process. It shows a node labeled 'zi' connected to a node labeled 'zj' with a weight 'wji'. The node 'zj' is also connected to nodes labeled 'δk' and 'δ1' with weights 'wkj' and an implied weight respectively. An error term 'δj' is associated with the node 'zj'. A blue arrow indicates the forward pass direction, while red arrows show the backward flow of error signals.

provided we are using the canonical link as the output-unit activation function. To evaluate the  $\delta$ 's for hidden units, we again make use of the chain rule for partial derivatives,

$$
\delta_j \equiv \frac{\partial E_n}{\partial a_j} = \sum_k \frac{\partial E_n}{\partial a_k} \frac{\partial a_k}{\partial a_j} \tag{5.55}
$$

where the sum runs over all units  $k$  to which unit  $j$  sends connections. The arrangement of units and weights is illustrated in Figure 5.7. Note that the units labelled  $k$ could include other hidden units and/or output units. In writing down (5.55), we are making use of the fact that variations in  $a_j$  give rise to variations in the error function only through variations in the variables  $a_k$ . If we now substitute the definition of  $\delta$  given by (5.51) into (5.55), and make use of (5.48) and (5.49), we obtain the following *backpropagation* formula

$$
\delta_j = h'(a_j) \sum_k w_{kj} \delta_k \tag{5.56}
$$

which tells us that the value of  $\delta$  for a particular hidden unit can be obtained by propagating the  $\delta$ 's backwards from units higher up in the network, as illustrated in Figure 5.7. Note that the summation in (5.56) is taken over the first index on  $w_{ki}$  (corresponding to backward propagation of information through the network), whereas in the forward propagation equation (5.10) it is taken over the second index. Because we already know the values of the  $\delta$ 's for the output units, it follows that by recursively applying (5.56) we can evaluate the  $\delta$ 's for all of the hidden units in a feed-forward network, regardless of its topology.

The backpropagation procedure can therefore be summarized as follows.

### Error Backpropagation

- 1. Apply an input vector  $x_n$  to the network and forward propagate through the network using (5.48) and (5.49) to find the activations of all the hidden and output units.
- 2. Evaluate the  $\delta_k$  for all the output units using (5.54).
- 3. Backpropagate the  $\delta$ 's using (5.56) to obtain  $\delta_i$  for each hidden unit in the network.
- 4. Use (5.53) to evaluate the required derivatives.

For batch methods, the derivative of the total error  $E$  can then be obtained by repeating the above steps for each pattern in the training set and then summing over all patterns:

$$
\frac{\partial E}{\partial w_{ji}} = \sum_{n} \frac{\partial E_n}{\partial w_{ji}}.
$$
\n(5.57)

In the above derivation we have implicitly assumed that each hidden or output unit in the network has the same activation function  $h(\cdot)$ . The derivation is easily generalized, however, to allow different units to have individual activation functions, simply by keeping track of which form of  $h(\cdot)$  goes with which unit.

### 5.3.2 A simple example

The above derivation of the backpropagation procedure allowed for general forms for the error function, the activation functions, and the network topology. In order to illustrate the application of this algorithm, we shall consider a particular example. This is chosen both for its simplicity and for its practical importance, because many applications of neural networks reported in the literature make use of this type of network. Specifically, we shall consider a two-layer network of the form illustrated in Figure 5.1, together with a sum-of-squares error, in which the output units have linear activation functions, so that  $y_k = a_k$ , while the hidden units have logistic sigmoid activation functions given by

$$
h(a) \equiv \tanh(a) \tag{5.58}
$$

where

$$
\tanh(a) = \frac{e^a - e^{-a}}{e^a + e^{-a}}.
$$
\n(5.59)

A useful feature of this function is that its derivative can be expressed in a particularly simple form:

$$
h'(a) = 1 - h(a)^2.
$$
 (5.60)

We also consider a standard sum-of-squares error function, so that for pattern  $n$  the error is given by

$$
E_n = \frac{1}{2} \sum_{k=1}^{K} (y_k - t_k)^2
$$
\n(5.61)

where  $y_k$  is the activation of output unit k, and  $t_k$  is the corresponding target, for a particular input pattern  $x_n$ .

For each pattern in the training set in turn, we first perform a forward propagation using

$$
a_j = \sum_{i=0}^{D} w_{ji}^{(1)} x_i
$$
 (5.62)

$$
z_j = \tanh(a_j) \tag{5.63}
$$

$$
y_k = \sum_{j=0}^{M} w_{kj}^{(2)} z_j.
$$
 (5.64)

Next we compute the  $\delta$ 's for each output unit using

$$
\delta_k = y_k - t_k. \tag{5.65}
$$

Then we backpropagate these to obtain  $\delta s$  for the hidden units using

$$
\delta_j = (1 - z_j^2) \sum_{k=1}^{K} w_{kj} \delta_k.
$$
 (5.66)

Finally, the derivatives with respect to the first-layer and second-layer weights are given by

$$
\frac{\partial E_n}{\partial w_{ji}^{(1)}} = \delta_j x_i, \qquad \frac{\partial E_n}{\partial w_{kj}^{(2)}} = \delta_k z_j.
$$
 (5.67)

### 5.3.3 Efficiency of backpropagation

One of the most important aspects of backpropagation is its computational efficiency. To understand this, let us examine how the number of computer operations required to evaluate the derivatives of the error function scales with the total number W of weights and biases in the network. A single evaluation of the error function (for a given input pattern) would require  $O(W)$  operations, for sufficiently large W. This follows from the fact that, except for a network with very sparse connections, the number of weights is typically much greater than the number of units, and so the bulk of the computational effort in forward propagation is concerned with evaluating the sums in (5.48), with the evaluation of the activation functions representing a small overhead. Each term in the sum in (5.48) requires one multiplication and one addition, leading to an overall computational cost that is  $O(W)$ .

An alternative approach to backpropagation for computing the derivatives of the error function is to use finite differences. This can be done by perturbing each weight in turn, and approximating the derivatives by the expression

$$
\frac{\partial E_n}{\partial w_{ji}} = \frac{E_n(w_{ji} + \epsilon) - E_n(w_{ji})}{\epsilon} + O(\epsilon)
$$
\n(5.68)

where  $\epsilon \ll 1$ . In a software simulation, the accuracy of the approximation to the derivatives can be improved by making  $\epsilon$  smaller, until numerical roundoff problems arise. The accuracy of the finite differences method can be improved significantly by using symmetrical *central differences* of the form

$$
\frac{\partial E_n}{\partial w_{ji}} = \frac{E_n(w_{ji} + \epsilon) - E_n(w_{ji} - \epsilon)}{2\epsilon} + O(\epsilon^2). \tag{5.69}
$$

*Exercise 5.14* In this case, the  $O(\epsilon)$  corrections cancel, as can be verified by Taylor expansion on the right-hand side of (5.69), and so the residual corrections are  $O(\epsilon^2)$ . The number of computational steps is, however, roughly doubled compared with (5.68).

> The main problem with numerical differentiation is that the highly desirable  $O(W)$  scaling has been lost. Each forward propagation requires  $O(W)$  steps, and

Image /page/22/Figure/1 description: Figure 5.8 is an illustration of a modular pattern recognition system. The text explains that the Jacobian matrix can be used to backpropagate error signals from the outputs through to earlier modules in the system.

Image /page/22/Figure/2 description: A diagram shows two inputs, labeled 'u' and 'x', feeding into two separate boxes. The box receiving 'u' is outlined in green, and the box receiving 'x' is outlined in blue and contains the label 'w'. Arrows labeled 'v' and 'z' originate from these respective boxes and converge into a third box, outlined in red. An arrow labeled 'y' exits the red box, indicating the output.

there are  $W$  weights in the network each of which must be perturbed individually, so that the overall scaling is  $O(W^2)$ .

However, numerical differentiation plays an important role in practice, because a comparison of the derivatives calculated by backpropagation with those obtained using central differences provides a powerful check on the correctness of any software implementation of the backpropagation algorithm. When training networks in practice, derivatives should be evaluated using backpropagation, because this gives the greatest accuracy and numerical efficiency. However, the results should be compared with numerical differentiation using (5.69) for some test cases in order to check the correctness of the implementation.

#### 5.3.4 The Jacobian matrix

We have seen how the derivatives of an error function with respect to the weights can be obtained by the propagation of errors backwards through the network. The technique of backpropagation can also be applied to the calculation of other derivatives. Here we consider the evaluation of the *Jacobian* matrix, whose elements are given by the derivatives of the network outputs with respect to the inputs

$$
J_{ki} \equiv \frac{\partial y_k}{\partial x_i} \tag{5.70}
$$

where each such derivative is evaluated with all other inputs held fixed. Jacobian matrices play a useful role in systems built from a number of distinct modules, as illustrated in Figure 5.8. Each module can comprise a fixed or adaptive function, which can be linear or nonlinear, so long as it is differentiable. Suppose we wish to minimize an error function E with respect to the parameter  $w$  in Figure 5.8. The derivative of the error function is given by

$$
\frac{\partial E}{\partial w} = \sum_{k,j} \frac{\partial E}{\partial y_k} \frac{\partial y_k}{\partial z_j} \frac{\partial z_j}{\partial w}
$$
(5.71)

in which the Jacobian matrix for the red module in Figure 5.8 appears in the middle term.

Because the Jacobian matrix provides a measure of the local sensitivity of the outputs to changes in each of the input variables, it also allows any known errors  $\Delta x_i$ 

associated with the inputs to be propagated through the trained network in order to estimate their contribution  $\Delta y_k$  to the errors at the outputs, through the relation

$$
\Delta y_k \simeq \sum_i \frac{\partial y_k}{\partial x_i} \Delta x_i \tag{5.72}
$$

which is valid provided the  $|\Delta x_i|$  are small. In general, the network mapping represented by a trained neural network will be nonlinear, and so the elements of the Jacobian matrix will not be constants but will depend on the particular input vector used. Thus (5.72) is valid only for small perturbations of the inputs, and the Jacobian itself must be re-evaluated for each new input vector.

The Jacobian matrix can be evaluated using a backpropagation procedure that is similar to the one derived earlier for evaluating the derivatives of an error function with respect to the weights. We start by writing the element  $J_{ki}$  in the form

$$
J_{ki} = \frac{\partial y_k}{\partial x_i} = \sum_j \frac{\partial y_k}{\partial a_j} \frac{\partial a_j}{\partial x_i}
$$

$$
= \sum_j w_{ji} \frac{\partial y_k}{\partial a_j}
$$
 $(5.73)$ 

where we have made use of  $(5.48)$ . The sum in  $(5.73)$  runs over all units j to which the input unit  $i$  sends connections (for example, over all units in the first hidden layer in the layered topology considered earlier). We now write down a recursive backpropagation formula to determine the derivatives  $\partial y_k/\partial a_j$ 

$$
\frac{\partial y_k}{\partial a_j} = \sum_l \frac{\partial y_k}{\partial a_l} \frac{\partial a_l}{\partial a_j}
$$

$$
= h'(a_j) \sum_l w_{lj} \frac{\partial y_k}{\partial a_l} (5.74)
$$

where the sum runs over all units  $l$  to which unit  $j$  sends connections (corresponding to the first index of  $w_{ij}$ ). Again, we have made use of (5.48) and (5.49). This backpropagation starts at the output units for which the required derivatives can be found directly from the functional form of the output-unit activation function. For instance, if we have individual sigmoidal activation functions at each output unit, then

$$
\frac{\partial y_k}{\partial a_j} = \delta_{kj} \sigma'(a_j) \tag{5.75}
$$

whereas for softmax outputs we have

$$
\frac{\partial y_k}{\partial a_j} = \delta_{kj} y_k - y_k y_j.
$$
\n(5.76)

We can summarize the procedure for evaluating the Jacobian matrix as follows. Apply the input vector corresponding to the point in input space at which the Jacobian matrix is to be found, and forward propagate in the usual way to obtain the activations of all of the hidden and output units in the network. Next, for each row  $k$  of the Jacobian matrix, corresponding to the output unit  $k$ , backpropagate using the recursive relation  $(5.74)$ , starting with  $(5.75)$  or  $(5.76)$ , for all of the hidden units in the network. Finally, use (5.73) to do the backpropagation to the inputs. The Jacobian can also be evaluated using an alternative *forward* propagation formalism, which can be derived in an analogous way to the backpropagation approach given

Again, the implementation of such algorithms can be checked by using numerical differentiation in the form

$$
\frac{\partial y_k}{\partial x_i} = \frac{y_k(x_i + \epsilon) - y_k(x_i - \epsilon)}{2\epsilon} + O(\epsilon^2)
$$
\n(5.77)

which involves 2D forward propagations for a network having D inputs.

## **5.4. The Hessian Matrix**

We have shown how the technique of backpropagation can be used to obtain the first derivatives of an error function with respect to the weights in the network. Backpropagation can also be used to evaluate the second derivatives of the error, given by

$$
\frac{\partial^2 E}{\partial w_{ji} \partial w_{lk}}.\tag{5.78}
$$

Note that it is sometimes convenient to consider all of the weight and bias parameters as elements  $w_i$  of a single vector, denoted  $w$ , in which case the second derivatives form the elements  $H_{ij}$  of the *Hessian* matrix **H**, where  $i, j \in \{1, \ldots, W\}$  and W is the total number of weights and biases. The Hessian plays an important role in many aspects of neural computing, including the following:

- 1. Several nonlinear optimization algorithms used for training neural networks are based on considerations of the second-order properties of the error surface, which are controlled by the Hessian matrix (Bishop and Nabney, 2008).
- 2. The Hessian forms the basis of a fast procedure for re-training a feed-forward network following a small change in the training data (Bishop, 1991).
- 3. The inverse of the Hessian has been used to identify the least significant weights in a network as part of network 'pruning' algorithms (Le Cun *et al.*, 1990).
- 4. The Hessian plays a central role in the Laplace approximation for a Bayesian neural network (see Section 5.7). Its inverse is used to determine the predictive distribution for a trained network, its eigenvalues determine the values of hyperparameters, and its determinant is used to evaluate the model evidence.

Various approximation schemes have been used to evaluate the Hessian matrix for a neural network. However, the Hessian can also be calculated exactly using an extension of the backpropagation technique.

*Exercise 5.15* here.

An important consideration for many applications of the Hessian is the efficiency with which it can be evaluated. If there are W parameters (weights and biases) in the network, then the Hessian matrix has dimensions  $W \times W$  and so the computational effort needed to evaluate the Hessian will scale like  $O(W^2)$  for each pattern in the data set. As we shall see, there are efficient methods for evaluating the Hessian whose scaling is indeed  $O(W^2)$ .

### 5.4.1 Diagonal approximation

Some of the applications for the Hessian matrix discussed above require the inverse of the Hessian, rather than the Hessian itself. For this reason, there has been some interest in using a diagonal approximation to the Hessian, in other words one that simply replaces the off-diagonal elements with zeros, because its inverse is trivial to evaluate. Again, we shall consider an error function that consists of a sum of terms, one for each pattern in the data set, so that  $E = \sum_n E_n$ . The Hessian can then be obtained by considering one pattern at a time, and then summing the results over all patterns. From  $(5.48)$ , the diagonal elements of the Hessian, for pattern n, can be written

$$
\frac{\partial^2 E_n}{\partial w_{ji}^2} = \frac{\partial^2 E_n}{\partial a_j^2} z_i^2.
$$
\n(5.79)

Using  $(5.48)$  and  $(5.49)$ , the second derivatives on the right-hand side of  $(5.79)$  can be found recursively using the chain rule of differential calculus to give a backpropagation equation of the form

$$
\frac{\partial^2 E_n}{\partial a_j^2} = h'(a_j)^2 \sum_k \sum_{k'} w_{kj} w_{k'j} \frac{\partial^2 E_n}{\partial a_k \partial a_{k'}} + h''(a_j) \sum_k w_{kj} \frac{\partial E^n}{\partial a_k}.
$$
 (5.80)

If we now neglect off-diagonal elements in the second-derivative terms, we obtain (Becker and Le Cun, 1989; Le Cun *et al.*, 1990)

$$
\frac{\partial^2 E_n}{\partial a_j^2} = h'(a_j)^2 \sum_k w_{kj}^2 \frac{\partial^2 E_n}{\partial a_k^2} + h''(a_j) \sum_k w_{kj} \frac{\partial E_n}{\partial a_k}.\tag{5.81}
$$

Note that the number of computational steps required to evaluate this approximation is  $O(W)$ , where W is the total number of weight and bias parameters in the network, compared with  $O(W^2)$  for the full Hessian.

Ricotti *et al.* (1988) also used the diagonal approximation to the Hessian, but they retained all terms in the evaluation of  $\partial^2 E_n / \partial a_j^2$  and so obtained exact expressions for the diagonal terms. Note that this no longer has  $O(W)$  scaling. The major problem with diagonal approximations, however, is that in practice the Hessian is typically found to be strongly nondiagonal, and so these approximations, which are driven mainly be computational convenience, must be treated with care.

### 5.4.2 Outer product approximation

When neural networks are applied to regression problems, it is common to use a sum-of-squares error function of the form

$$
E = \frac{1}{2} \sum_{n=1}^{N} (y_n - t_n)^2
$$
\n(5.82)

where we have considered the case of a single output in order to keep the notation *Exercise 5.16* simple (the extension to several outputs is straightforward). We can then write the Hessian matrix in the form

$$
\mathbf{H} = \nabla \nabla E = \sum_{n=1}^{N} \nabla y_n \nabla y_n + \sum_{n=1}^{N} (y_n - t_n) \nabla \nabla y_n.
$$
 (5.83)

If the network has been trained on the data set, and its outputs  $y_n$  happen to be very close to the target values  $t_n$ , then the second term in (5.83) will be small and can be neglected. More generally, however, it may be appropriate to neglect this term by the following argument. Recall from Section 1.5.5 that the optimal function that minimizes a sum-of-squares loss is the conditional average of the target data. The quantity  $(y_n - t_n)$  is then a random variable with zero mean. If we assume that its value is uncorrelated with the value of the second derivative term on the right-hand *Exercise 5.17* side of  $(5.83)$ , then the whole term will average to zero in the summation over n.

> By neglecting the second term in (5.83), we arrive at the *Levenberg–Marquardt* approximation or *outer product* approximation (because the Hessian matrix is built up from a sum of outer products of vectors), given by

$$
\mathbf{H} \simeq \sum_{n=1}^{N} \mathbf{b}_n \mathbf{b}_n^{\mathrm{T}}
$$
 (5.84)

where  $\mathbf{b}_n = \nabla y_n = \nabla a_n$  because the activation function for the output units is simply the identity. Evaluation of the outer product approximation for the Hessian is straightforward as it only involves first derivatives of the error function, which can be evaluated efficiently in  $O(W)$  steps using standard backpropagation. The elements of the matrix can then be found in  $O(W^2)$  steps by simple multiplication. It is important to emphasize that this approximation is only likely to be valid for a network that has been trained appropriately, and that for a general network mapping the second derivative terms on the right-hand side of (5.83) will typically not be negligible.

In the case of the cross-entropy error function for a network with logistic sigmoid *Exercise 5.19* output-unit activation functions, the corresponding approximation is given by

$$
\mathbf{H} \simeq \sum_{n=1}^{N} y_n (1 - y_n) \mathbf{b}_n \mathbf{b}_n^{\mathrm{T}}.
$$
 (5.85)

An analogous result can be obtained for multiclass networks having softmax output-*Exercise* 5.20 unit activation functions.

*Exercise 5.16*

*Exercise 5.17*

#### 5.4.3 Inverse Hessian

We can use the outer-product approximation to develop a computationally efficient procedure for approximating the inverse of the Hessian (Hassibi and Stork, 1993). First we write the outer-product approximation in matrix notation as

$$
\mathbf{H}_N = \sum_{n=1}^N \mathbf{b}_n \mathbf{b}_n^{\mathrm{T}}
$$
 (5.86)

where  $\mathbf{b}_n \equiv \nabla_{\mathbf{w}} a_n$  is the contribution to the gradient of the output unit activation arising from data point  $n$ . We now derive a sequential procedure for building up the Hessian by including data points one at a time. Suppose we have already obtained the inverse Hessian using the first  $L$  data points. By separating off the contribution from data point  $L + 1$ , we obtain

$$
\mathbf{H}_{L+1} = \mathbf{H}_L + \mathbf{b}_{L+1} \mathbf{b}_{L+1}^{\mathrm{T}}.
$$
 (5.87)

In order to evaluate the inverse of the Hessian, we now consider the matrix identity

$$
\left(\mathbf{M} + \mathbf{v}\mathbf{v}^{\mathrm{T}}\right)^{-1} = \mathbf{M}^{-1} - \frac{\left(\mathbf{M}^{-1}\mathbf{v}\right)\left(\mathbf{v}^{\mathrm{T}}\mathbf{M}^{-1}\right)}{1 + \mathbf{v}^{\mathrm{T}}\mathbf{M}^{-1}\mathbf{v}}
$$
(5.88)

where **I** is the unit matrix, which is simply a special case of the Woodbury identity  $(C \bigcap T)$  If we now identify  $H_L$  with M and  $h_{L+1}$  with y we obtain (C.7). If we now identify  $H_L$  with M and  $b_{L+1}$  with **v**, we obtain

$$
\mathbf{H}_{L+1}^{-1} = \mathbf{H}_{L}^{-1} - \frac{\mathbf{H}_{L}^{-1} \mathbf{b}_{L+1} \mathbf{b}_{L+1}^{T} \mathbf{H}_{L}^{-1}}{1 + \mathbf{b}_{L+1}^{T} \mathbf{H}_{L}^{-1} \mathbf{b}_{L+1}}.
$$
(5.89)

In this way, data points are sequentially absorbed until  $L+1 = N$  and the whole data set has been processed. This result therefore represents a procedure for evaluating the inverse of the Hessian using a single pass through the data set. The initial matrix  $H_0$  is chosen to be  $\alpha I$ , where  $\alpha$  is a small quantity, so that the algorithm actually finds the inverse of  $H + \alpha I$ . The results are not particularly sensitive to the precise value of  $\alpha$ . Extension of this algorithm to networks having more than one output is *Exercise 5.21* straightforward.

> We note here that the Hessian matrix can sometimes be calculated indirectly as part of the network training algorithm. In particular, quasi-Newton nonlinear optimization algorithms gradually build up an approximation to the inverse of the Hessian during training. Such algorithms are discussed in detail in Bishop and Nabney (2008).

#### 5.4.4 Finite differences

As in the case of the first derivatives of the error function, we can find the second derivatives by using finite differences, with accuracy limited by numerical precision. If we perturb each possible pair of weights in turn, we obtain

$$
\frac{\partial^2 E}{\partial w_{ji} \partial w_{lk}} = \frac{1}{4\epsilon^2} \left\{ E(w_{ji} + \epsilon, w_{lk} + \epsilon) - E(w_{ji} + \epsilon, w_{lk} - \epsilon) -E(w_{ji} - \epsilon, w_{lk} + \epsilon) + E(w_{ji} - \epsilon, w_{lk} - \epsilon) \right\} + O(\epsilon^2).
$$
 (5.90)

*Exercise 5.21*

Again, by using a symmetrical central differences formulation, we ensure that the residual errors are  $O(\epsilon^2)$  rather than  $O(\epsilon)$ . Because there are  $W^2$  elements in the Hessian matrix, and because the evaluation of each element requires four forward propagations each needing  $O(W)$  operations (per pattern), we see that this approach will require  $O(W^3)$  operations to evaluate the complete Hessian. It therefore has poor scaling properties, although in practice it is very useful as a check on the software implementation of backpropagation methods.

A more efficient version of numerical differentiation can be found by applying central differences to the first derivatives of the error function, which are themselves calculated using backpropagation. This gives

$$
\frac{\partial^2 E}{\partial w_{ji} \partial w_{lk}} = \frac{1}{2\epsilon} \left\{ \frac{\partial E}{\partial w_{ji}} (w_{lk} + \epsilon) - \frac{\partial E}{\partial w_{ji}} (w_{lk} - \epsilon) \right\} + O(\epsilon^2).
$$
 (5.91)

Because there are now only  $W$  weights to be perturbed, and because the gradients can be evaluated in  $O(W)$  steps, we see that this method gives the Hessian in  $O(W^2)$ operations.

### 5.4.5 Exact evaluation of the Hessian

So far, we have considered various approximation schemes for evaluating the Hessian matrix or its inverse. The Hessian can also be evaluated exactly, for a network of arbitrary feed-forward topology, using extension of the technique of backpropagation used to evaluate first derivatives, which shares many of its desirable features including computational efficiency (Bishop, 1991; Bishop, 1992). It can be applied to any differentiable error function that can be expressed as a function of the network outputs and to networks having arbitrary differentiable activation functions. The number of computational steps needed to evaluate the Hessian scales like  $O(W^2)$ . Similar algorithms have also been considered by Buntine and Weigend (1993).

Here we consider the specific case of a network having two layers of weights, *Exercise* 5.22 for which the required equations are easily derived. We shall use indices i and i' to denote inputs, indices j and j' to denoted hidden units, and indices k and  $k'$  to denote outputs. We first define

$$
\delta_k = \frac{\partial E_n}{\partial a_k}, \qquad M_{kk'} \equiv \frac{\partial^2 E_n}{\partial a_k \partial a_{k'}} \tag{5.92}
$$

where  $E_n$  is the contribution to the error from data point n. The Hessian matrix for this network can then be considered in three separate blocks as follows.

1. Both weights in the second layer:

$$
\frac{\partial^2 E_n}{\partial w_{kj}^{(2)} \partial w_{k'j'}^{(2)}} = z_j z_{j'} M_{kk'}.
$$
\n(5.93)

*Exercise 5.22*

2. Both weights in the first layer:

$$
\frac{\partial^2 E_n}{\partial w_{ji}^{(1)} \partial w_{j'i'}^{(1)}} = x_i x_{i'} h''(a_{j'}) I_{jj'} \sum_k w_{kj'}^{(2)} \delta_k
$$

$$
+ x_i x_{i'} h'(a_{j'}) h'(a_j) \sum_k \sum_{k'} w_{k'j'}^{(2)} w_{kj}^{(2)} M_{kk'}. \quad (5.94)
$$

3. One weight in each layer:

$$
\frac{\partial^2 E_n}{\partial w_{ji}^{(1)} \partial w_{kj'}^{(2)}} = x_i h'(a_{j'}) \left\{ \delta_k I_{jj'} + z_j \sum_{k'} w_{k'j'}^{(2)} H_{kk'} \right\}.
$$
 (5.95)

Here  $I_{jj'}$  is the j, j' element of the identity matrix. If one or both of the weights is a bias term, then the corresponding expressions are obtained simply by setting the *Exercise 5.23* appropriate activation(s) to 1. Inclusion of skip-layer connections is straightforward.

### 5.4.6 Fast multiplication by the Hessian

For many applications of the Hessian, the quantity of interest is not the Hessian matrix **H** itself but the product of **H** with some vector **v**. We have seen that the evaluation of the Hessian takes  $O(W^2)$  operations, and it also requires storage that is  $O(W^2)$ . The vector  $\mathbf{v}^{\mathrm{T}}\mathbf{H}$  that we wish to calculate, however, has only W elements, so instead of computing the Hessian as an intermediate step, we can instead try to find an efficient approach to evaluating  $v<sup>T</sup>H$  directly in a way that requires only  $O(W)$  operations.

To do this, we first note that

$$
\mathbf{v}^{\mathrm{T}}\mathbf{H} = \mathbf{v}^{\mathrm{T}}\nabla(\nabla E)
$$
 (5.96)

where  $\nabla$  denotes the gradient operator in weight space. We can then write down the standard forward-propagation and backpropagation equations for the evaluation of  $\nabla E$  and apply (5.96) to these equations to give a set of forward-propagation and backpropagation equations for the evaluation of  $v<sup>T</sup>H$  (Møller, 1993; Pearlmutter, 1994). This corresponds to acting on the original forward-propagation and backpropagation equations with a differential operator **v**<sup>T</sup>∇. Pearlmutter (1994) used the notation  $\mathcal{R}\{\cdot\}$  to denote the operator  $\mathbf{v}^T\nabla$ , and we shall follow this convention. The analysis is straightforward and makes use of the usual rules of differential calculus, together with the result

$$
\mathcal{R}\{\mathbf{w}\} = \mathbf{v}.\tag{5.97}
$$

The technique is best illustrated with a simple example, and again we choose a two-layer network of the form shown in Figure 5.1, with linear output units and a sum-of-squares error function. As before, we consider the contribution to the error function from one pattern in the data set. The required vector is then obtained as

*Exercise 5.23*

### 5.4. The Hessian Matrix 255

usual by summing over the contributions from each of the patterns separately. For the two-layer network, the forward-propagation equations are given by

$$
a_j = \sum_i w_{ji} x_i \tag{5.98}
$$

$$
z_j = h(a_j) \tag{5.99}
$$

$$
y_k = \sum_j w_{kj} z_j. \tag{5.100}
$$

We now act on these equations using the  $\mathcal{R}\{\cdot\}$  operator to obtain a set of forward propagation equations in the form

$$
\mathcal{R}\{a_j\} = \sum_i v_{ji} x_i \tag{5.101}
$$

$$
\mathcal{R}\{z_j\} = h'(a_j)\mathcal{R}\{a_j\} \tag{5.102}
$$

$$
\mathcal{R}\{y_k\} = \sum_j w_{kj} \mathcal{R}\{z_j\} + \sum_j v_{kj} z_j \tag{5.103}
$$

where  $v_{ji}$  is the element of the vector **v** that corresponds to the weight  $w_{ji}$ . Quantities of the form  $\mathcal{R}{z_j}$ ,  $\mathcal{R}{a_j}$  and  $\mathcal{R}{y_k}$  are to be regarded as new variables whose values are found using the above equations.

Because we are considering a sum-of-squares error function, we have the following standard backpropagation expressions:

$$
\delta_k = y_k - t_k \tag{5.104}
$$

$$
\delta_j = h'(a_j) \sum_k w_{kj} \delta_k. \tag{5.105}
$$

Again, we act on these equations with the  $\mathcal{R}\{\cdot\}$  operator to obtain a set of backpropagation equations in the form

$$
\mathcal{R}\{\delta_k\} = \mathcal{R}\{y_k\} \tag{5.106}
$$

$$
\mathcal{R}\{\delta_j\} = h''(a_j)\mathcal{R}\{a_j\} \sum_k w_{kj}\delta_k + h'(a_j) \sum_k v_{kj}\delta_k + h'(a_j) \sum_k w_{kj}\mathcal{R}\{\delta_k\}. \tag{5.107}
$$

Finally, we have the usual equations for the first derivatives of the error

$$
\frac{\partial E}{\partial w_{kj}} = \delta_k z_j \tag{5.108}
$$

$$
\frac{\partial E}{\partial w_{ji}} = \delta_j x_i \tag{5.109}
$$

and acting on these with the  $\mathcal{R}\{\cdot\}$  operator, we obtain expressions for the elements of the vector **<sup>v</sup>**<sup>T</sup>**H**

$$
\mathcal{R}\left\{\frac{\partial E}{\partial w_{kj}}\right\} = \mathcal{R}\{\delta_k\}z_j + \delta_k \mathcal{R}\{z_j\}
$$
\n(5.110)

$$
\mathcal{R}\left\{\frac{\partial E}{\partial w_{ji}}\right\} = x_i \mathcal{R}\{\delta_j\}.
$$
\n(5.111)

The implementation of this algorithm involves the introduction of additional variables  $\mathcal{R}\{a_i\}$ ,  $\mathcal{R}\{z_i\}$  and  $\mathcal{R}\{\delta_i\}$  for the hidden units and  $\mathcal{R}\{\delta_k\}$  and  $\mathcal{R}\{y_k\}$ for the output units. For each input pattern, the values of these quantities can be found using the above results, and the elements of  $v<sup>T</sup>H$  are then given by (5.110) and (5.111). An elegant aspect of this technique is that the equations for evaluating **v**<sup>T</sup>**H** mirror closely those for standard forward and backward propagation, and so the extension of existing software to compute this product is typically straightforward.

If desired, the technique can be used to evaluate the full Hessian matrix by choosing the vector **v** to be given successively by a series of unit vectors of the form  $(0, 0, \ldots, 1, \ldots, 0)$  each of which picks out one column of the Hessian. This leads to a formalism that is analytically equivalent to the backpropagation procedure of Bishop (1992), as described in Section 5.4.5, though with some loss of efficiency due to redundant calculations.

## **5.5. Regularization in Neural Networks**

The number of input and outputs units in a neural network is generally determined by the dimensionality of the data set, whereas the number  $M$  of hidden units is a free parameter that can be adjusted to give the best predictive performance. Note that  $M$ controls the number of parameters (weights and biases) in the network, and so we might expect that in a maximum likelihood setting there will be an optimum value of M that gives the best generalization performance, corresponding to the optimum balance between under-fitting and over-fitting. Figure 5.9 shows an example of the effect of different values of M for the sinusoidal regression problem.

The generalization error, however, is not a simple function of  $M$  due to the presence of local minima in the error function, as illustrated in Figure 5.10. Here we see the effect of choosing multiple random initializations for the weight vector for a range of values of  $M$ . The overall best validation set performance in this case occurred for a particular solution having  $M = 8$ . In practice, one approach to choosing  $M$  is in fact to plot a graph of the kind shown in Figure 5.10 and then to choose the specific solution having the smallest validation set error.

There are, however, other ways to control the complexity of a neural network model in order to avoid over-fitting. From our discussion of polynomial curve fitting in Chapter 1, we see that an alternative approach is to choose a relatively large value for  $M$  and then to control complexity by the addition of a regularization term to the error function. The simplest regularizer is the quadratic, giving a regularized error

Image /page/32/Figure/1 description: The image displays three plots side-by-side, each illustrating a curve fitted to scattered data points. The first plot, labeled "M = 1", shows a smooth curve that starts at approximately y=0.8, decreases, and levels off around y=-0.2. It is fitted to seven data points marked with blue crosses. The second plot, labeled "M = 3", shows a curve that oscillates, starting at y=0.5, rising to y=1, then decreasing to y=-0.5, and ending at y=0.2. This curve is fitted to eight data points. The third plot, labeled "M = 10", shows a more complex oscillating curve fitted to ten data points, exhibiting several peaks and troughs between y=-1 and y=1. All plots have x-axes ranging from 0 to 1 and y-axes ranging from -1 to 1.

**Figure 5.9** Examples of two-layer networks trained on 10 data points drawn from the sinusoidal data set. The graphs show the result of fitting networks having  $M = 1$ , 3 and 10 hidden units, respectively, by minimizing a sum-of-squares error function using a scaled conjugate-gradient algorithm.

of the form

$$
\widetilde{E}(\mathbf{w}) = E(\mathbf{w}) + \frac{\lambda}{2} \mathbf{w}^{\mathrm{T}} \mathbf{w}.
$$
\n(5.112)

This regularizer is also known as *weight decay* and has been discussed at length in Chapter 3. The effective model complexity is then determined by the choice of the regularization coefficient  $\lambda$ . As we have seen previously, this regularizer can be interpreted as the negative logarithm of a zero-mean Gaussian prior distribution over the weight vector **w**.

### 5.5.1 Consistent Gaussian priors

One of the limitations of simple weight decay in the form (5.112) is that is inconsistent with certain scaling properties of network mappings. To illustrate this, consider a multilayer perceptron network having two layers of weights and linear output units, which performs a mapping from a set of input variables  $\{x_i\}$  to a set of output variables  $\{y_k\}$ . The activations of the hidden units in the first hidden layer

**Figure 5.10** Plot of the sum-of-squares test-set error for the polynomial data set versus the number of hidden units in the network, with 30 random starts for each network size, showing the effect of local minima. For each new start, the weight vector was initialized by sampling from an isotropic Gaussian distribution having a mean of zero and a variance of 10.

Image /page/32/Figure/9 description: A scatter plot shows data points marked with blue plus signs and blue squares. The x-axis ranges from 0 to 10, with tick marks at 0, 2, 4, 6, 8, and 10. The y-axis ranges from 60 to 160, with tick marks at 60, 80, 100, 120, 140, and 160. At x=1, there are two blue squares around y=115 and one blue plus sign around y=115. Between x=1 and x=2, there is one blue plus sign around y=115. At x=2, there are two blue squares around y=70 and one blue plus sign around y=85. Between x=2 and x=3, there are several blue plus signs clustered around y=70 and y=85. At x=3, there are several blue plus signs clustered around y=70 and y=85. At x=4, there are several blue plus signs clustered around y=70 and y=85. From x=5 to x=10, there are multiple blue plus signs scattered across the plot. Specifically, at x=5, there are several plus signs around y=70 and y=85. At x=6, there are several plus signs around y=70 and y=95. At x=7, there are several plus signs around y=70 and y=95. At x=8, there are several plus signs around y=70, y=95, and y=120. At x=9, there are several plus signs around y=70, y=80, y=105, and y=135. At x=10, there are several plus signs around y=70, y=80, y=105, y=120, and y=160.