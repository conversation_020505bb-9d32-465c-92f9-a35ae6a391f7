{"table_of_contents": [{"title": "5.1. Feed-forward Network Functions", "heading_level": null, "page_id": 2, "polygon": [[88.34765625, 78.75], [324.0, 78.75], [324.0, 92.599365234375], [88.34765625, 92.599365234375]]}, {"title": "228 5. NEURAL NETWORKS", "heading_level": null, "page_id": 3, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.8280029296875], [29.25, 51.8280029296875]]}, {"title": "230 5. NEURAL NETWORKS", "heading_level": null, "page_id": 5, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.9093017578125], [29.25, 51.9093017578125]]}, {"title": "5.1.1 Weight-space symmetries", "heading_level": null, "page_id": 6, "polygon": [[137.689453125, 444.75], [318.75, 444.75], [318.75, 456.57421875], [137.689453125, 456.57421875]]}, {"title": "232 5. NEURAL NETWORKS", "heading_level": null, "page_id": 7, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "5.2. Network Training", "heading_level": null, "page_id": 7, "polygon": [[88.224609375, 489.75], [225.052734375, 489.75], [225.052734375, 504.052734375], [88.224609375, 504.052734375]]}, {"title": "236 5. NEURAL NETWORKS", "heading_level": null, "page_id": 11, "polygon": [[30.0, 41.25], [201.75, 41.25], [201.75, 51.86865234375], [30.0, 51.86865234375]]}, {"title": "5.2.1 Parameter optimization", "heading_level": null, "page_id": 11, "polygon": [[136.705078125, 507.0], [302.25, 507.0], [302.25, 517.7109375], [136.705078125, 517.7109375]]}, {"title": "5.2.2 Local quadratic approximation", "heading_level": null, "page_id": 12, "polygon": [[138.427734375, 524.865234375], [342.75, 524.865234375], [342.75, 535.921875], [138.427734375, 535.921875]]}, {"title": "238 5. NEURAL NETWORKS", "heading_level": null, "page_id": 13, "polygon": [[29.900390625, 40.5], [201.75, 40.5], [201.75, 51.54345703125], [29.900390625, 51.54345703125]]}, {"title": "5.2.3 Use of gradient information", "heading_level": null, "page_id": 14, "polygon": [[138.75, 448.5], [326.25, 448.5], [326.25, 459.5009765625], [138.75, 459.5009765625]]}, {"title": "5.2.4 Gradient descent optimization", "heading_level": null, "page_id": 15, "polygon": [[137.07421875, 176.25], [340.5, 176.25], [340.5, 187.47509765625], [137.07421875, 187.47509765625]]}, {"title": "5.3. <PERSON><PERSON><PERSON>propagation", "heading_level": null, "page_id": 16, "polygon": [[89.25, 276.75], [262.5, 276.75], [262.5, 290.23681640625], [89.25, 290.23681640625]]}, {"title": "242 5. NEURAL NETWORKS", "heading_level": null, "page_id": 17, "polygon": [[29.25, 40.5], [201.75, 40.5], [201.75, 51.6654052734375], [29.25, 51.6654052734375]]}, {"title": "5.3.1 Evaluation of error-function derivatives", "heading_level": null, "page_id": 17, "polygon": [[137.3203125, 128.25], [389.25, 128.25], [389.25, 139.102294921875], [137.3203125, 139.102294921875]]}, {"title": "244 5. NEURAL NETWORKS", "heading_level": null, "page_id": 19, "polygon": [[29.25, 41.25], [201.75, 41.25], [201.75, 51.787353515625], [29.25, 51.787353515625]]}, {"title": "Error Backpropagation", "heading_level": null, "page_id": 19, "polygon": [[136.951171875, 474.75], [243.75, 474.75], [243.75, 486.1669921875], [136.951171875, 486.1669921875]]}, {"title": "5.3.2 A simple example", "heading_level": null, "page_id": 20, "polygon": [[138.75, 195.0], [273.75, 195.0], [273.75, 206.33642578125], [138.75, 206.33642578125]]}, {"title": "246 5. NEURAL NETWORKS", "heading_level": null, "page_id": 21, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.624755859375], [30.0, 51.624755859375]]}, {"title": "5.3.3 Efficiency of backpropagation", "heading_level": null, "page_id": 21, "polygon": [[136.828125, 238.5], [339.0, 238.5], [339.0, 251.05078125], [136.828125, 251.05078125]]}, {"title": "5.3.4 The Jacobian matrix", "heading_level": null, "page_id": 22, "polygon": [[138.75, 318.75], [288.0, 318.75], [288.0, 330.56103515625], [138.75, 330.56103515625]]}, {"title": "248 5. NEURAL NETWORKS", "heading_level": null, "page_id": 23, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "5.4. The Hessian Matrix", "heading_level": null, "page_id": 24, "polygon": [[90.0, 252.75], [240.1875, 252.75], [240.1875, 266.3349609375], [90.0, 266.3349609375]]}, {"title": "5.4.1 Diagonal approximation", "heading_level": null, "page_id": 25, "polygon": [[137.935546875, 153.0], [306.0, 153.0], [306.0, 164.06103515625], [137.935546875, 164.06103515625]]}, {"title": "5.4.2 Outer product approximation", "heading_level": null, "page_id": 26, "polygon": [[138.55078125, 72.0], [334.5, 72.0], [334.5, 83.006103515625], [138.55078125, 83.006103515625]]}, {"title": "5.4.3 Inverse Hessian", "heading_level": null, "page_id": 27, "polygon": [[137.56640625, 71.25], [263.25, 71.25], [263.25, 83.0467529296875], [137.56640625, 83.0467529296875]]}, {"title": "5.4.4 Finite differences", "heading_level": null, "page_id": 27, "polygon": [[136.828125, 518.25], [270.75, 518.25], [270.75, 530.068359375], [136.828125, 530.068359375]]}, {"title": "5.4.5 Exact evaluation of the Hessian", "heading_level": null, "page_id": 28, "polygon": [[138.05859375, 281.25], [348.22265625, 281.25], [348.22265625, 293.326171875], [138.05859375, 293.326171875]]}, {"title": "254 5. NEURAL NETWORKS", "heading_level": null, "page_id": 29, "polygon": [[30.0, 40.5], [201.75, 40.5], [201.75, 51.7467041015625], [30.0, 51.7467041015625]]}, {"title": "5.4.6 Fast multiplication by the Hessian", "heading_level": null, "page_id": 29, "polygon": [[137.8125, 280.5], [360.75, 280.5], [360.75, 291.53759765625], [137.8125, 291.53759765625]]}, {"title": "5.4. The Hessian Matrix 255", "heading_level": null, "page_id": 30, "polygon": [[312.75, 39.75], [473.25, 39.75], [473.25, 50.974365234375], [312.75, 50.974365234375]]}, {"title": "256 5. NEURAL NETWORKS", "heading_level": null, "page_id": 31, "polygon": [[30.0, 39.75], [201.75, 40.5], [201.75, 51.380859375], [30.0, 51.380859375]]}, {"title": "5.5. Regularization in Neural Networks", "heading_level": null, "page_id": 31, "polygon": [[88.5, 348.0], [331.5, 348.0], [331.5, 361.2919921875], [88.5, 361.2919921875]]}, {"title": "5.5.1 Consistent Gaussian priors", "heading_level": null, "page_id": 32, "polygon": [[138.75, 333.75], [326.25, 333.75], [326.25, 346.3330078125], [138.75, 346.3330078125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 33], ["Line", 18], ["Text", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 51], ["TextInlineMath", 5], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 70], ["Text", 6], ["Equation", 3], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 693, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 57], ["Text", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 50], ["Text", 5], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 634, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 44], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 645, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 52], ["Text", 3], ["SectionHeader", 2], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 662, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 55], ["Text", 6], ["Equation", 5], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 59], ["Text", 6], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 59], ["Text", 7], ["Equation", 4], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1271, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 40], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Caption", 1], ["Figure", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2210, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 48], ["Text", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 49], ["Equation", 9], ["Text", 7], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 56], ["Text", 5], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Figure", 1], ["ListItem", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 47], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 50], ["Text", 7], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 49], ["Equation", 6], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 54], ["ListItem", 4], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["Caption", 1], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 644, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 58], ["Equation", 8], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 54], ["Equation", 5], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 50], ["Text", 6], ["Figure", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1229, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 67], ["Text", 5], ["Equation", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 44], ["Text", 7], ["ListItem", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 77], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 55], ["Text", 7], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1158, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 59], ["Text", 6], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 54], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 63], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["ListItem", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 56], ["Equation", 11], ["Text", 5], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 45], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 50], ["Text", 4], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["ListItem", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1755, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_245-277"}