{"table_of_contents": [{"title": "Algorithm 11.1: K-means algorithm", "heading_level": null, "page_id": 0, "polygon": [[132.46875, 64.5], [276.0, 64.5], [276.0, 74.4345703125], [132.46875, 74.4345703125]]}, {"title": "1 initialize \\mathbf{m}_k;", "heading_level": null, "page_id": 0, "polygon": [[129.515625, 80.25], [197.578125, 80.25], [197.578125, 90.4921875], [129.515625, 90.4921875]]}, {"title": "2 repeat", "heading_level": null, "page_id": 0, "polygon": [[129.3046875, 93.0], [168.75, 93.0], [168.75, 103.3857421875], [129.3046875, 103.3857421875]]}, {"title": "11.4.2.6 Vector quantization", "heading_level": null, "page_id": 0, "polygon": [[84.75, 350.25], [215.25, 350.25], [215.25, 360.703125], [84.75, 360.703125]]}, {"title": "11.4.2.7 Initialization and avoiding local minima", "heading_level": null, "page_id": 1, "polygon": [[85.1484375, 291.75], [303.75, 291.75], [303.75, 301.21875], [85.1484375, 301.21875]]}, {"title": "11.4.2.8 MAP estimation", "heading_level": null, "page_id": 1, "polygon": [[84.75, 502.5], [198.75, 502.5], [198.75, 512.26171875], [84.75, 512.26171875]]}, {"title": "11.4.3 EM for mixture of experts", "heading_level": null, "page_id": 3, "polygon": [[90.75, 459.0], [253.5, 459.0], [253.5, 469.23046875], [90.75, 469.23046875]]}, {"title": "11.4.4 EM for DGMs with hidden variables", "heading_level": null, "page_id": 4, "polygon": [[90.75, 348.0], [299.25, 348.0], [299.25, 357.5390625], [90.75, 357.5390625]]}, {"title": "11.4.5 EM for the Student distribution *", "heading_level": null, "page_id": 5, "polygon": [[90.6328125, 274.5], [285.75, 274.5], [285.75, 284.607421875], [90.6328125, 284.607421875]]}, {"title": "11.4.5.1 EM with ν known", "heading_level": null, "page_id": 6, "polygon": [[86.25, 224.25], [208.40625, 224.25], [208.40625, 235.08984375], [86.25, 235.08984375]]}, {"title": "11.4.5.2 EM with ν unknown", "heading_level": null, "page_id": 6, "polygon": [[84.75, 535.5], [219.375, 535.5], [219.375, 545.80078125], [84.75, 545.80078125]]}, {"title": "11.4.5.3 Mixtures of Student distributions", "heading_level": null, "page_id": 7, "polygon": [[85.359375, 535.5], [273.0, 535.5], [273.0, 545.484375], [85.359375, 545.484375]]}, {"title": "11.4.6 EM for probit regression *", "heading_level": null, "page_id": 8, "polygon": [[90.0, 196.5], [253.5, 196.5], [253.5, 206.296875], [90.0, 206.296875]]}, {"title": "11.4.7 Theoretical basis for EM *", "heading_level": null, "page_id": 9, "polygon": [[89.578125, 264.0], [252.75, 264.0], [252.75, 275.115234375], [89.578125, 275.115234375]]}, {"title": "11.4.7.1 Expected complete data log likelihood is a lower bound", "heading_level": null, "page_id": 9, "polygon": [[87.75, 333.0], [370.5, 333.0], [370.5, 342.75], [87.75, 342.75]]}, {"title": "11.4.7.2 EM monotonically increases the observed data log likelihood", "heading_level": null, "page_id": 10, "polygon": [[85.5, 540.75], [393.0, 540.75], [393.0, 550.86328125], [85.5, 550.86328125]]}, {"title": "11.4.8 Online EM", "heading_level": null, "page_id": 11, "polygon": [[89.2265625, 327.0], [180.28125, 327.0], [180.28125, 336.97265625], [89.2265625, 336.97265625]]}, {"title": "11.4.8.1 Batch EM review", "heading_level": null, "page_id": 11, "polygon": [[86.25, 455.94140625], [202.5, 455.94140625], [202.5, 465.43359375], [86.25, 465.43359375]]}, {"title": "11.4.8.2 Incremental EM", "heading_level": null, "page_id": 12, "polygon": [[84.75, 211.5], [199.40625, 211.5], [199.40625, 221.80078125], [84.75, 221.80078125]]}, {"title": "11.4.8.3 Stepwise EM", "heading_level": null, "page_id": 12, "polygon": [[85.078125, 477.0], [185.203125, 477.0], [185.203125, 486.6328125], [85.078125, 486.6328125]]}, {"title": "11.4.9 Other EM variants *", "heading_level": null, "page_id": 13, "polygon": [[90.0, 464.25], [225.421875, 464.25], [225.421875, 474.92578125], [90.0, 474.92578125]]}, {"title": "11.5 Model selection for latent variable models", "heading_level": null, "page_id": 16, "polygon": [[96.1171875, 60.75], [349.5, 60.75], [349.5, 71.94287109375], [96.1171875, 71.94287109375]]}, {"title": "11.5.1 Model selection for probabilistic models", "heading_level": null, "page_id": 16, "polygon": [[91.5, 145.5], [321.0, 145.5], [321.0, 155.4345703125], [91.5, 155.4345703125]]}, {"title": "11.5.2 Model selection for non-probabilistic methods", "heading_level": null, "page_id": 16, "polygon": [[89.296875, 440.25], [349.5, 440.25], [349.5, 450.87890625], [89.296875, 450.87890625]]}, {"title": "11.6 Fitting models with missing data", "heading_level": null, "page_id": 18, "polygon": [[96.75, 329.25], [300.0, 329.25], [300.0, 340.453125], [96.75, 340.453125]]}, {"title": "11.6.1 EM for the MLE of an MVN with missing data", "heading_level": null, "page_id": 19, "polygon": [[89.7890625, 174.75], [345.0, 174.75], [345.0, 185.73046875], [89.7890625, 185.73046875]]}, {"title": "11.6.1.1 Getting started", "heading_level": null, "page_id": 19, "polygon": [[87.046875, 231.0], [195.75, 231.0], [195.75, 241.1015625], [87.046875, 241.1015625]]}, {"title": "11.6.1.2 E step", "heading_level": null, "page_id": 19, "polygon": [[85.5, 298.5], [157.5, 298.5], [157.5, 308.8125], [85.5, 308.8125]]}, {"title": "11.6.1.3 M step", "heading_level": null, "page_id": 20, "polygon": [[86.25, 196.5], [159.0, 196.5], [159.0, 206.296875], [86.25, 206.296875]]}, {"title": "11.6.1.4 Example", "heading_level": null, "page_id": 20, "polygon": [[86.25, 390.75], [168.75, 390.75], [168.75, 400.25390625], [86.25, 400.25390625]]}, {"title": "11.6.1.5 Extension to the GMM case", "heading_level": null, "page_id": 20, "polygon": [[86.25, 498.75], [247.640625, 498.75], [247.640625, 509.09765625], [86.25, 509.09765625]]}, {"title": "Exercises", "heading_level": null, "page_id": 20, "polygon": [[129.75, 557.25], [178.5, 557.25], [178.5, 567.0], [129.75, 567.0]]}, {"title": "Exercise 11.2 EM for mixtures of Gaussians", "heading_level": null, "page_id": 21, "polygon": [[129.1640625, 254.25], [288.0, 254.25], [288.0, 263.8828125], [129.1640625, 263.8828125]]}, {"title": "Exercise 11.6 EM for a finite scale mixture of Gaussians", "heading_level": null, "page_id": 22, "polygon": [[129.75, 544.5], [333.0, 544.5], [333.0, 553.39453125], [129.75, 553.39453125]]}, {"title": "Exercise 11.7 Manual calculation of the M step for a GMM", "heading_level": null, "page_id": 23, "polygon": [[129.75, 270.75], [342.0, 270.75], [342.0, 280.01953125], [129.75, 280.01953125]]}, {"title": "Exercise 11.8 Moments of a mixture of Gaussians", "heading_level": null, "page_id": 23, "polygon": [[129.75, 468.75], [309.0, 468.75], [309.0, 477.45703125], [129.75, 477.45703125]]}, {"title": "Exercise 11.13 EM for EB estimation of Gaussian shrinkage model", "heading_level": null, "page_id": 25, "polygon": [[129.4453125, 363.75], [367.5, 363.75], [367.5, 372.7265625], [129.4453125, 372.7265625]]}, {"title": "Exercise 11.14 EM for censored linear regression", "heading_level": null, "page_id": 25, "polygon": [[129.7265625, 417.0], [305.25, 417.0], [305.25, 426.515625], [129.7265625, 426.515625]]}, {"title": "Exercise 11.15 Posterior mean and variance of a truncated Gaussian", "heading_level": null, "page_id": 26, "polygon": [[129.0, 62.25], [376.5, 62.25], [376.5, 72.140625], [129.0, 72.140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 54], ["SectionHeader", 4], ["Equation", 3], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 45], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 59], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 530], ["Line", 62], ["Equation", 9], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["Line", 80], ["Equation", 7], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 72], ["Text", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 598], ["Line", 102], ["Equation", 10], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 83], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 803, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 537], ["Line", 51], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 59], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 740, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 65], ["Equation", 8], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 33], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["Line", 36], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Caption", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 624, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 41], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 706, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 61], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 44], ["Text", 8], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 412], ["Line", 124], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2183, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 45], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["Line", 77], ["Equation", 9], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 67], ["Text", 5], ["Equation", 5], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["Line", 120], ["Text", 9], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1455, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 64], ["Text", 7], ["Equation", 6], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 43], ["ListItem", 6], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 80], ["Text", 9], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 31], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-14"}