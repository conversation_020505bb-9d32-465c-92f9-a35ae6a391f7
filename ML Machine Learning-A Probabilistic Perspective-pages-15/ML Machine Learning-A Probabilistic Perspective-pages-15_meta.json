{"table_of_contents": [{"title": "12 Latent linear models", "heading_level": null, "page_id": 0, "polygon": [[64.6875, 93.7353515625], [284.25, 93.7353515625], [282.75, 147.75], [63.75, 143.8857421875]]}, {"title": "12.1 Factor analysis", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [207.75, 207.0], [207.75, 217.6875], [96.75, 217.6875]]}, {"title": "12.1.1 FA is a low rank parameterization of an MVN", "heading_level": null, "page_id": 0, "polygon": [[91.5, 504.0], [344.25, 504.0], [344.25, 513.84375], [91.5, 513.84375]]}, {"title": "12.1.2 Inference of the latent factors", "heading_level": null, "page_id": 1, "polygon": [[89.6484375, 446.25], [270.75, 446.25], [270.75, 455.94140625], [89.6484375, 455.94140625]]}, {"title": "12.1.3 Unidentifiability", "heading_level": null, "page_id": 2, "polygon": [[90.0, 519.0], [207.75, 519.0], [207.75, 529.03125], [90.0, 529.03125]]}, {"title": "12.1.4 Mixtures of factor analysers", "heading_level": null, "page_id": 4, "polygon": [[89.578125, 440.25], [261.75, 440.25], [261.75, 449.9296875], [89.578125, 449.9296875]]}, {"title": "12.1.5 EM for factor analysis models", "heading_level": null, "page_id": 5, "polygon": [[90.0, 328.5], [270.75, 328.5], [270.75, 339.1875], [90.0, 339.1875]]}, {"title": "12.1.6 Fitting FA models with missing data", "heading_level": null, "page_id": 6, "polygon": [[89.25, 290.25], [300.0, 290.25], [300.0, 300.5859375], [89.25, 300.5859375]]}, {"title": "12.2 Principal components analysis (PCA)", "heading_level": null, "page_id": 6, "polygon": [[94.5, 372.75], [318.75, 372.75], [318.75, 383.80078125], [94.5, 383.80078125]]}, {"title": "12.2.1 Classical PCA: statement of the theorem", "heading_level": null, "page_id": 6, "polygon": [[89.578125, 505.5], [319.5, 505.5], [319.5, 515.7421875], [89.578125, 515.7421875]]}, {"title": "12.2.2 Proof *", "heading_level": null, "page_id": 8, "polygon": [[87.75, 438.0], [163.5, 438.0], [163.5, 449.296875], [87.75, 449.296875]]}, {"title": "12.2.3 Singular value decomposition (SVD)", "heading_level": null, "page_id": 11, "polygon": [[87.609375, 61.5], [299.25, 61.5], [299.25, 71.5078125], [87.609375, 71.5078125]]}, {"title": "12.2.4 Probabilistic PCA", "heading_level": null, "page_id": 14, "polygon": [[88.171875, 284.25], [213.0, 284.25], [213.0, 294.57421875], [88.171875, 294.57421875]]}, {"title": "12.2.5 EM algorithm for PCA", "heading_level": null, "page_id": 15, "polygon": [[88.171875, 160.5], [234.0, 160.5], [234.0, 170.859375], [88.171875, 170.859375]]}, {"title": "12.3 Choosing the number of latent dimensions", "heading_level": null, "page_id": 17, "polygon": [[94.7109375, 384.75], [352.5, 384.75], [352.5, 396.45703125], [94.7109375, 396.45703125]]}, {"title": "12.3.1 Model selection for FA/PPCA", "heading_level": null, "page_id": 17, "polygon": [[90.6328125, 446.25], [264.75, 446.25], [264.75, 456.890625], [90.6328125, 456.890625]]}, {"title": "12.3.2 Model selection for PCA", "heading_level": null, "page_id": 18, "polygon": [[87.75, 456.0], [244.125, 456.0], [244.125, 466.69921875], [87.75, 466.69921875]]}, {"title": "12.3.2.1 Profile likelihood", "heading_level": null, "page_id": 20, "polygon": [[84.375, 300.75], [204.75, 300.75], [204.75, 310.39453125], [84.375, 310.39453125]]}, {"title": "12.4 PCA for categorical data", "heading_level": null, "page_id": 21, "polygon": [[95.25, 268.5], [255.75, 268.5], [255.75, 279.703125], [95.25, 279.703125]]}, {"title": "12.5 PCA for paired and multi-view data", "heading_level": null, "page_id": 23, "polygon": [[95.25, 368.25], [312.75, 368.25], [312.75, 380.00390625], [95.25, 380.00390625]]}, {"title": "12.5.1 Supervised PCA (latent factor regression)", "heading_level": null, "page_id": 24, "polygon": [[90.0, 61.5], [324.0, 61.5], [324.0, 71.5869140625], [90.0, 71.5869140625]]}, {"title": "12.5.1.1 Discriminative supervised PCA", "heading_level": null, "page_id": 25, "polygon": [[84.7265625, 62.25], [263.25, 62.25], [263.25, 71.54736328125], [84.7265625, 71.54736328125]]}, {"title": "12.5.2 Partial least squares", "heading_level": null, "page_id": 25, "polygon": [[88.453125, 268.5], [226.828125, 268.5], [226.828125, 278.75390625], [88.453125, 278.75390625]]}, {"title": "12.5.3 Canonical correlation analysis", "heading_level": null, "page_id": 26, "polygon": [[88.3125, 60.75], [273.0, 60.75], [273.0, 71.5078125], [88.3125, 71.5078125]]}, {"title": "12.6 Independent Component Analysis (ICA)", "heading_level": null, "page_id": 26, "polygon": [[94.5, 429.0], [332.25, 429.0], [332.25, 440.12109375], [94.5, 440.12109375]]}, {"title": "12.6.1 Maximum likelihood estimation", "heading_level": null, "page_id": 29, "polygon": [[89.25, 307.5], [280.5, 307.5], [280.5, 317.671875], [89.25, 317.671875]]}, {"title": "12.6.2 The FastICA algorithm", "heading_level": null, "page_id": 30, "polygon": [[87.75, 267.0], [234.75, 267.0], [234.75, 277.171875], [87.75, 277.171875]]}, {"title": "******** Modeling the source densities", "heading_level": null, "page_id": 32, "polygon": [[83.953125, 62.25], [259.5, 62.25], [259.5, 71.62646484375], [83.953125, 71.62646484375]]}, {"title": "12.6.3 Using EM", "heading_level": null, "page_id": 33, "polygon": [[87.75, 411.0], [176.484375, 411.0], [176.484375, 421.453125], [87.75, 421.453125]]}, {"title": "12.6.4 Other estimation principles *", "heading_level": null, "page_id": 34, "polygon": [[88.3125, 159.75], [268.5, 159.75], [268.5, 169.59375], [88.3125, 169.59375]]}, {"title": "******** Maximizing non-Gaussianity", "heading_level": null, "page_id": 34, "polygon": [[84.75, 240.0], [254.25, 240.0], [254.25, 249.9609375], [84.75, 249.9609375]]}, {"title": "******** Minimizing mutual information", "heading_level": null, "page_id": 34, "polygon": [[83.953125, 523.5], [267.75, 523.5], [267.75, 533.4609375], [83.953125, 533.4609375]]}, {"title": "******** Maximizing mutual information (infomax)", "heading_level": null, "page_id": 35, "polygon": [[83.25, 210.0], [312.75, 210.0], [312.75, 220.060546875], [83.25, 220.060546875]]}, {"title": "Exercises", "heading_level": null, "page_id": 35, "polygon": [[129.7265625, 448.5], [178.5, 448.5], [178.5, 458.47265625], [129.7265625, 458.47265625]]}, {"title": "Exercise 12.8 Latent semantic indexing", "heading_level": null, "page_id": 37, "polygon": [[129.75, 291.0], [273.75, 291.0], [273.75, 299.63671875], [129.75, 299.63671875]]}, {"title": "Exercise 12.11 PPCA vs FA", "heading_level": null, "page_id": 38, "polygon": [[129.0, 255.0], [225.75, 255.0], [225.75, 264.19921875], [129.0, 264.19921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 29], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 3], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6865, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["Line", 48], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 783, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 881, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 51], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 991, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 34], ["ListItem", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 694, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 50], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 67], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 991, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 61], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 779, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 42], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TextInlineMath", 2], ["Picture", 1], ["Figure", 1], ["Text", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1464, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 100], ["Equation", 9], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 51], ["Equation", 8], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 71], ["Equation", 8], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 61], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 50], ["Equation", 8], ["TextInlineMath", 7], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 440], ["Line", 50], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 62], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 906, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 30], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["TableCell", 88], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["Footnote", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 59], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 68], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 968, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 54], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 37], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1385, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 37], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 543], ["Line", 45], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 494], ["Line", 62], ["Equation", 8], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 60], ["Equation", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 75], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1200, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 49], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 849, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 55], ["Text", 5], ["Equation", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 76], ["Equation", 8], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 55], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 970, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 51], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 33], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 58], ["Equation", 5], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 44], ["Text", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 790], ["Line", 70], ["Text", 9], ["TextInlineMath", 7], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["ListItem", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 47], ["ListItem", 5], ["Text", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 807, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-15"}