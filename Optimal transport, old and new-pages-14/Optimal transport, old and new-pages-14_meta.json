{"table_of_contents": [{"title": "First Appendix: A little bit of geometric measure theory", "heading_level": null, "page_id": 0, "polygon": [[133.5, 196.5], [469.5, 196.5], [469.5, 207.861328125], [133.5, 207.861328125]]}, {"title": "Theorem 10.48 (Sufficient conditions for countable rectifiabil-\nity).", "heading_level": null, "page_id": 1, "polygon": [[133.5, 48.0], [469.5, 48.0], [469.5, 73.5732421875], [133.5, 73.5732421875]]}, {"title": "Second Appendix: Nonsmooth implicit function theorem", "heading_level": null, "page_id": 3, "polygon": [[133.5, 435.75], [469.5, 435.75], [469.5, 447.046875], [133.5, 447.046875]]}, {"title": "Third Appendix: <PERSON>ur<PERSON><PERSON> and the Hessian of the\nsquared distance", "heading_level": null, "page_id": 7, "polygon": [[133.5, 441.75], [436.5, 441.75], [436.5, 469.08984375], [133.5, 469.08984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 43], ["TextInlineMath", 4], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 657], ["Line", 61], ["TextInlineMath", 7], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 907], ["Line", 107], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 616], ["Line", 60], ["TextInlineMath", 8], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 32], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 619], ["Line", 38], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 661], ["Line", 62], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 45], ["TextInlineMath", 7], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 574, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 616], ["Line", 49], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 57], ["TextInlineMath", 6], ["Equation", 5], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-14"}