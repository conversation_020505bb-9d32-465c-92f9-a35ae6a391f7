## **3**

# **Algorithmic Foundations**

This chapter describes the most common algorithmic tools from combinatorial optimization and linear programming that can be used to solve the discrete formulation of optimal transport, as described in the primal problem (2.11) or alternatively its dual (2.20).

The origins of these algorithms can be traced back to World War II, either right before with <PERSON><PERSON><PERSON><PERSON>'s seminal work [1930] or during the war itself, when <PERSON> [1941] and <PERSON><PERSON><PERSON><PERSON> [1942] formalized the generic problem of dispatching available resources toward consumption sites in an optimal way. Both of these formulations, as well as the later contribution by <PERSON><PERSON><PERSON> [1949], fell short of providing a *provably* correct algorithm to solve that problem (the cycle violation method was already proposed as a heuristic by <PERSON><PERSON><PERSON><PERSON> [1939]). One had to wait until the field of linear programming fully blossomed, with the proposal of the simplex method, to be at last able to solve rigorously these problems.

The goal of linear programming is to solve optimization problems whose objective function is linear and whose constraints are linear (in)equalities in the variables of interest. The optimal transport problem fits that description and is therefore a particular case of that wider class of problems. One can argue, however, that optimal transport is truly special among all linear program. First, <PERSON><PERSON><PERSON>'s early motivation to solve linear programs was greatly related to that of solving transportation problems [<PERSON><PERSON><PERSON>, 1949, p. 210]. Second, despite being only a particular case, the optimal transport problem remained in the spotlight of optimization, because it was understood shortly after that optimal transport problems were related, and in fact equivalent, to an important class of linear programs known as minimum cost network flows [<PERSON>rte and <PERSON>ygen, 2012, p.

213, <PERSON>m. 9.3] thanks to a result by <PERSON> and Fulkerson [1962]. As such, the OT problem has been the subject of particular attention, ever since the birth of mathematical programming [Dantzig, 1951], and is still widely used to introduce optimization to a new audience [Nocedal and <PERSON>, 1999, §1, p. 4].

### **3.1 The Kantorovich Linear Programs**

We have already introduced in Equation  $(2.11)$  the primal OT problem:

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = \min_{\mathbf{P} \in \mathbf{U}(\mathbf{a}, \mathbf{b})} \sum_{i \in [\![n]\!], j \in [\![m]\!]} \mathbf{C}_{i,j} \mathbf{P}_{i,j}.
$$
 (3.1)

To make the link with the linear programming literature, one can cast the equation above as a linear program in *standard* form, that is, a linear program with a linear objective; equality constraints defined with a matrix and a constant vector; and nonnegative constraints on variables. Let  $\mathbb{I}_n$  stand for the identity matrix of size *n* and let ⊗ be Kronecker's product. The  $(n + m) \times nm$  matrix

$$
\mathbf{A} = \begin{bmatrix} \mathbb{1}_n^T \otimes \mathbb{I}_m \\ \mathbb{I}_n \otimes \mathbb{1}_m^T \end{bmatrix} \in \mathbb{R}^{(n+m) \times nm}
$$

can be used to encode the row-sum and column-sum constraints that need to be satisfied for any **P** to be in **U**( $\mathbf{a}, \mathbf{b}$ ). To do so, simply cast a matrix  $\mathbf{P} \in \mathbb{R}^{n \times m}$  as a vector  $\mathbf{p} \in \mathbb{R}^{nm}$ such that the  $i + n(j-1)$ 's element of **p** is equal to  $P_{ij}$  (**P** is enumerated columnwise) to obtain the following equivalence:

$$
\mathbf{P} \in \mathbb{R}^{n \times m} \in \mathbf{U}(\mathbf{a},\mathbf{b}) \Leftrightarrow \mathbf{p} \in \mathbb{R}_+^{nm}, \mathbf{A}\mathbf{p} = \big[\begin{smallmatrix} \mathbf{a} \\ \mathbf{b} \end{smallmatrix} \big].
$$

Therefore we can write the original optimal transport problem as

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = \min_{\substack{\mathbf{p} \in \mathbb{R}_{+}^{nm} \\ \mathbf{A}\mathbf{p} = \begin{bmatrix} \mathbf{a} \\ \mathbf{b} \end{bmatrix}} \mathbf{c}^{\mathrm{T}} \mathbf{p},\tag{3.2}
$$

where the *nm*-dimensional vector **c** is equal to the stacked columns contained in the cost matrix **C**.

**Remark 3.1.** Note that one of the  $n + m$  constraints described above is redundant or that, in other words, the line vectors of matrix *A* are not linearly independent. Indeed, summing all *n* first lines and the subsequent *m* lines results in the same vector (namely  $A\begin{bmatrix} 1_n \\ 0_m \end{bmatrix} = A\begin{bmatrix} 0_n \\ 1_m \end{bmatrix} = 1_{nm}$ <sup>T</sup>). One can show that removing a line in *A* and the corresponding entry in  $\begin{bmatrix} \mathbf{a} \\ \mathbf{b} \end{bmatrix}$  yields a properly defined linear system. For simplicity, and to avoid treating asymmetrically **a** and **b**, we retain in what follows a redundant formulation, keeping in mind that degeneracy will pop up in some of our computations.

### 3.1. The Kantorovich Linear Programs 39

The dual problem corresponding to Equation (3.2) is, following duality in linear programming [Bertsimas and Tsitsiklis, 1997, p. 143] defined as

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = \max_{\substack{\mathbf{h} \in \mathbb{R}^{n+m} \\ A^{\mathrm{T}} \mathbf{h} \le \mathbf{c}}} \left[ \begin{array}{c} \mathbf{a} \\ \mathbf{b} \end{array} \right]^{\mathrm{T}} \mathbf{h}.
$$
 (3.3)

Note that this program is exactly equivalent to that presented in Equation (2.4).

**Remark 3.2.** We provide a simple derivation of the duality result above, which can be seen as a direct formulation of the arguments developed in Remark 2.21. Strong duality, namely the fact that the optima of both primal (3.2) and dual (3.3) problems do indeed coincide, requires a longer proof [Bertsimas and Tsitsiklis, 1997, §4.10]. To simplify notation, we write  $\mathbf{q} = \begin{bmatrix} \mathbf{a} \\ \mathbf{b} \end{bmatrix}$ . Consider now a relaxed primal problem of the optimal transport problem, where the constraint  $Ap = q$  is no longer necessarily enforced but bears instead a cost  $\mathbf{h}^{\mathrm{T}}(\mathbf{A}\mathbf{p}-\mathbf{q})$  parameterized by an arbitrary vector of costs **h** ∈  $\mathbb{R}^{n+m}$ . This relaxation, whose optimum depends directly on the cost vector **h**, can be written as

$$
\mathrm{H}(\mathbf{h})\stackrel{\mathrm{{\scriptscriptstyle def.}}}{=}\min_{\mathbf{p}\in\mathbb{R}_{+}^{nm}}\mathbf{c}^{\mathrm{T}}\mathbf{p}-\mathbf{h}^{\mathrm{T}}(\mathbf{A}\mathbf{p}-\mathbf{q}).
$$

Note first that this relaxed problem has no marginal constraints on **p**. Because that minimization allows for many more **p** solutions, we expect  $H(h)$  to be smaller than  $\bar{z} = L_{\mathbf{C}}(\mathbf{a}, \mathbf{b})$ . Indeed, writing  $\mathbf{p}^*$  for any optimal solution of the primal problem (3.1), we obtain

$$
\min_{\mathbf{p}\in\mathbb{R}^{nm}_+}\mathbf{c}^{\mathrm{T}}\mathbf{p}-\mathbf{h}^{\mathrm{T}}(\mathbf{A}\mathbf{p}-\mathbf{q})\leq \mathbf{c}^{\mathrm{T}}\mathbf{p}^{\star}-\mathbf{h}^{\mathrm{T}}(\mathbf{A}\mathbf{p}^{\star}-\mathbf{q})=\mathbf{c}^{\mathrm{T}}\mathbf{p}^{\star}=\bar{z}.
$$

The approach above defines therefore a problem which can be used to compute an optimal upper bound for the original problem (3.1), for any cost vector **h**; that function is called the Lagrange dual function of L. The goal of duality theory is now to compute the best lower bound *z* by *maximizing* H over *any* cost vector **h**, namely

$$
\underline{z} = \max_{\mathbf{h}} \left( \mathbf{H}(\mathbf{h}) = \max_{\mathbf{h}} \mathbf{h}^{\mathrm{T}} \mathbf{q} + \min_{\mathbf{p} \in \mathbb{R}_{+}^{nm}} (\mathbf{c} - A^{\mathrm{T}} \mathbf{h})^{\mathrm{T}} \mathbf{p} \right).
$$

The second term involving a minimization on **p** can be easily shown to be  $-\infty$  if any coordinate of  $\mathbf{c}^{\mathrm{T}} - A^{\mathrm{T}}\mathbf{h}$  is negative. Indeed, if for instance for a given index  $i \leq n+m$ we have  $\mathbf{c}_i - (A^T \mathbf{h})_i < 0$ , then it suffices to take for **p** the canonical vector  $\mathbf{e}_i$  multiplied by any arbitrary large positive value to obtain an unbounded value. When trying to maximize the lower bound H(**h**) it therefore makes sense to restrict vectors **h** to be such that  $A^{T}$ **h**  $\leq$ **c**, in which case the best possible lower bound becomes

$$
\underline{z} = \max_{\substack{\mathbf{h} \in \mathbb{R}^{n+m} \\ A^{\mathrm{T}} \mathbf{h} \leq \mathbf{c}}} \mathbf{h}^{\mathrm{T}} \mathbf{q}.
$$

We have therefore proved a weak duality result, namely that  $z \leq \overline{z}$ .

## **3.2 C-Transforms**

We present in this section an important property of the dual optimal transport problem (3.3) which takes a more important meaning when used for the semidiscrete optimal transport problem in §5.1. This section builds upon the original formulation (2.20) that splits dual variables according to row and column sum constraints:

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = \max_{(\mathbf{f}, \mathbf{g}) \in \mathbf{R}(\mathbf{C})} \langle \mathbf{f}, \mathbf{a} \rangle + \langle \mathbf{g}, \mathbf{b} \rangle.
$$
 (3.4)

Consider any dual feasible pair  $(f, g)$ . If we "freeze" the value of  $f$ , we can notice that there is no better vector solution for **g** than the **C**-transform vector of **f**, denoted  $f^{\mathbf{C}} \in \mathbb{R}^m$  and defined as

$$
(\mathbf{f}^{\mathbf{C}})_j = \min_{i \in [\![n]\!]} \mathbf{C}_{ij} - \mathbf{f}_i,
$$

since it is indeed easy to prove that  $(f, f^C) \in R(C)$  and that  $f^C$  is the largest possible vector such that this constraint is satisfied. We therefore have that

$$
\langle {\bf f},\,{\bf a}\rangle+\langle {\bf g},\,{\bf b}\rangle\leq \langle {\bf f},\,{\bf a}\rangle+\langle {\bf f}^{\bf C},\,{\bf b}\rangle.
$$

This result allows us first to reformulate the dual problem as a piecewise affine concave maximization problem expressed in a single variable **f** as

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = \max_{\mathbf{f} \in \mathbb{R}^n} \langle \mathbf{f}, \mathbf{a} \rangle + \langle \mathbf{f}^{\mathbf{C}}, \mathbf{b} \rangle.
$$
 (3.5)

Putting that result aside, the same reasoning applies of course if we now "freeze" the values of **g** and consider instead the **C**-transform of **g**, namely vector  $\mathbf{g}^{\bar{\mathbf{C}}} \in \mathbb{R}^n$ defined as

$$
(\mathbf{g}^{\bar{\mathbf{C}}})_i = \min_{j \in [\![m]\!]} \mathbf{C}_{ij} - \mathbf{g}_j,
$$

with a different increase in objective

$$
\langle {\bf f},\,{\bf a}\rangle + \langle {\bf g},\,{\bf b}\rangle \leq \langle {\bf g}^{\bar{\bf C}},\,{\bf a}\rangle + \langle {\bf g},\,{\bf b}\rangle.
$$

Starting from a given **f**, it is therefore tempting to alternate **C** and  $\bar{C}$  transforms several times to improve **f**. Indeed, we have the sequence of inequalities

$$
\langle {\bf f},\,{\bf a}\rangle+\langle {\bf f}^{\bf C},\,{\bf b}\rangle\leq \langle {\bf f}^{\bf C\bar C},\,{\bf a}\rangle+\langle {\bf f}^{\bf C},\,{\bf b}\rangle\leq \langle {\bf f}^{\bf C\bar C},\,{\bf a}\rangle+\langle {\bf f}^{\bf C\bar C\bf C},\,{\bf b}\rangle\leq\dots
$$

One may hope for a strict increase in the objective at each of these iterations. However, this does not work because alternating  $C$  and  $C$  transforms quickly hits a plateau.

**Proposition 3.1.** The following identities, in which the inequality sign between vectors should be understood elementwise, hold:

(i)  $f \leq f' \Rightarrow f^C \geq f'^C$ ,

- 3.3. Complementary Slackness **41**
- (ii)  $f^{C\bar{C}} \geq f$ ,  $g^{\bar{C}C} \geq g$ ,
- (iii)  $\mathbf{f}^{\mathbf{C}\bar{\mathbf{C}}\mathbf{C}} = \mathbf{f}^{\mathbf{C}}$ .

*Proof.* The first inequality follows from the definition of **C**-transforms. Expanding the definition of  $f^{\mathbf{C}\bar{\mathbf{C}}}$  we have

$$
\left(\mathbf{f}^{\mathbf{C}\bar{\mathbf{C}}}\right)_i = \min_{j \in [m]} \mathbf{C}_{ij} - \mathbf{f}_j^{\mathbf{C}} = \min_{j \in [m]} \mathbf{C}_{ij} - \min_{i' \in [n]} \mathbf{C}_{i'j} - \mathbf{f}_{i'}.
$$

Now, since  $-\min_{i' \in [n]} \mathbf{C}_{i'j} - \mathbf{f}_{i'} \ge -(\mathbf{C}_{ij} - \mathbf{f}_i)$ , we recover

$$
\left(\mathbf{f}^{\mathbf{C}\bar{\mathbf{C}}}\right)_i \geq \min_{j \in [m]} \mathbf{C}_{ij} - \mathbf{C}_{ij} + \mathbf{f}_i = \mathbf{f}_i.
$$

The relation  $\mathbf{g}^{\bar{\mathbf{C}}\mathbf{C}} \geq \mathbf{g}$  is obtained in the same way. Now, set  $\mathbf{g} = \mathbf{f}^{\mathbf{C}}$ . Then,  $\mathbf{g}^{\bar{\mathbf{C}}} =$  $f^{\mathbf{C}\bar{\mathbf{C}}} \geq \mathbf{f}$ . Therefore, using result (i) we have  $f^{\mathbf{C}\bar{\mathbf{C}}\mathbf{C}} \leq f^{\mathbf{C}}$ . Result (ii) yields  $f^{\mathbf{C}\bar{\mathbf{C}}\mathbf{C}} \geq f^{\mathbf{C}}$ , proving the equality.  $\Box$ 

### **3.3 Complementary Slackness**

Primal (3.2) and dual (3.3), (2.20) problems can be solved independently to obtain optimal primal  $\mathbf{P}^*$  and dual  $(\mathbf{f}^*, \mathbf{g}^*)$  solutions. The following proposition characterizes their relationship.

**Proposition 3.2.** Let  $\mathbf{P}^{\star}$  and  $\mathbf{f}^{\star}$ ,  $\mathbf{g}^{\star}$  be optimal solutions for the primal (2.24) and dual (2.11) problems, respectively. Then, for any pair  $(i, j) \in [n] \times [m]$ ,  $\mathbf{P}^*_{i,j}(\mathbf{C}_{i,j} - \mathbf{C}_{i,j})$  $f_i^{\star} + g_j^{\star}$  = 0 holds. In other words, if  $P_{i,j}^{\star} > 0$ , then necessarily  $f_i^{\star} + g_j^{\star} = C_{i,j}$ ; if  $\mathbf{f}_i^* + \mathbf{g}_j^* < \mathbf{C}_{i,j}$  then necessarily  $\mathbf{P}_{i,j}^* = 0$ .

*Proof.* We have by strong duality that  $\langle \mathbf{P}^*, \mathbf{C} \rangle = \langle \mathbf{f}^*, \mathbf{a} \rangle + \langle \mathbf{g}^*, \mathbf{b} \rangle$ . Recall that  $\mathbf{P}^* \mathbb{1}_m =$ **a** and  $\mathbf{P}^{\star T} \mathbb{1}_n = \mathbf{b}$ ; therefore

$$
\langle \mathbf{f}^{\star}, \, \mathbf{a} \rangle + \langle \mathbf{g}^{\star}, \, \mathbf{b} \rangle = \langle \mathbf{f}^{\star}, \, \mathbf{P}^{\star} \mathbb{1}_m \rangle + \langle \mathbf{g}^{\star}, \, \mathbf{P}^{\star} \mathbb{1}_n \rangle
$$
$$
= \langle \mathbf{f}^{\star} \mathbb{1}_m^{\mathsf{T}}, \, \mathbf{P}^{\star} \rangle + \langle \mathbb{1}_n \mathbf{g}^{\star \mathsf{T}}, \, \mathbf{P}^{\star} \rangle,
$$

which results in

$$
\langle \mathbf{P}^{\star}, \, \mathbf{C} - \mathbf{f}^{\star} \oplus \mathbf{g}^{\star} \rangle = 0.
$$

Because  $(f^{\star}, g^{\star})$  belongs to the polyhedron of dual constraints (2.21), each entry of the matrix  $C - f^* \oplus g^*$  is necessarily nonnegative. Therefore, since all the entries of **P** are nonnegative, the constraint that the dot-product above is equal to 0 enforces that, for any pair of indices  $(i, j)$  such that  $\mathbf{P}_{i,j} > 0$ ,  $\mathbf{C}_{i,j} - (\mathbf{f}_i + \mathbf{g}_j)$  must be zero, and for any  $\Box$ pair of indices  $(i, j)$  such that  $\mathbf{C}_{i,j} > \mathbf{f}_i + \mathbf{g}_j$  that  $\mathbf{P}_{i,j} = 0$ .

The converse result is also true. We define first the idea that two variables for the primal and dual problems are complementary.

**Definition 3.1.** A matrix  $P \in \mathbb{R}^{n \times m}$  and a pair of vectors  $(f, g)$  are complementary w.r.t. **C** if for all pairs of indices  $(i, j)$  such that  $\mathbf{P}_{i,j} > 0$  one also has  $\mathbf{C}_{i,j} = \mathbf{f}_i + \mathbf{g}_j$ .

If a pair of feasible primal and dual variables is complementary, then we can conclude they are optimal.

**Proposition 3.3.** If **P** and  $(f, g)$  are complementary and feasible solutions for the primal  $(2.24)$  and dual  $(2.11)$  problems, respectively, then **P** and  $(f, g)$  are both primal and dual optimal.

*Proof.* By weak duality, we have that

$$
\mathrm{L}_\mathbf{C}(\mathbf{a},\mathbf{b})\leq \langle \mathbf{P},\,\mathbf{C}\rangle = \langle \mathbf{P},\,\mathbf{f}\oplus\mathbf{g}\rangle = \langle \mathbf{a},\,\mathbf{f}\rangle + \langle \mathbf{b},\,\mathbf{g}\rangle \leq \mathrm{L}_\mathbf{C}(\mathbf{a},\mathbf{b})
$$

and therefore **P** and (**f***,* **g**) are respectively primal and dual optimal.

 $\Box$ 

## **3.4 Vertices of the Transportation Polytope**

Recall that a vertex or an extremal point of a convex set is formally a point **x** in that set such that, if there exiss **y** and **z** in that set with  $\mathbf{x} = (\mathbf{y} + \mathbf{z})/2$ , then necessarily  $x = y = z$ . A linear program with a nonempty and bounded feasible set attains its minimum at a vertex (or extremal point) of the feasible set [Bertsimas and Tsitsiklis, 1997, p. 65, Theo. 2.7]. Since the feasible set **U**(**a***,* **b**) of the primal optimal transport problem (3.2) is bounded, one can restrict the search for an optimal **P** to the set of extreme points of the polytope  $U(a, b)$ . Matrices **P** that are extremal in  $U(a, b)$  have an interesting structure that has been the subject of extensive research [Brualdi, 2006, §8]. That structure requires describing the transport problem using the formalism of bipartite graphs.

#### **3.4.1 Tree Structure of the Support of All Vertices of U(a***,* **b)**

Let  $V = (1, 2, \ldots, n)$  and  $V' = (1', 2', \ldots, m')$  be two sets of nodes. Note that we add a prime to the labels of set  $V'$  to disambiguate them from those of  $V$ . Consider their union *V*∪*V'*, with *n*+*m* nodes, and the set *E* of all *nm* directed edges  $\{(i, j'), i \in [\![n]\!], j \in [\![m]\!]\}$ between them (here we just add a prime to an integer  $j \leq m$  to form  $j'$  in  $V'$ ). To each edge  $(i, j')$  we associate the corresponding cost value  $\mathbf{C}_{ij}$ . The complete bipartite graph G between V and V' is  $(V \cup V', E)$ . A transport plan is a flow on that graph satisfying source ( $\mathbf{a}_i$  flowing out of each node *i*) and sink ( $\mathbf{b}_j$  flowing into each node  $j'$ ) constraints, as described informally in Figure 3.1. An extremal point in  $U(a, b)$  has the following property [Brualdi, 2006, p. 338, Theo. 8.1.2].

**Proposition 3.4** (Extremal solutions)**.** Let **P** be an extremal point of the polytope **U**(a, b). Let  $S(\mathbf{P}) \subset \mathcal{E}$  be the subset of edges  $\{(i, j'), i \in [\![n]\!], j \in [\![m]\!]$  such that  $\mathbf{P}_{ij}$ 

Image /page/6/Figure/1 description: The image displays two bipartite graphs. The graph on the left shows three nodes labeled 1, 2, and 3 on the left side, and four nodes labeled 1', 2', 3', and 4' on the right side. Dotted lines connect nodes from the left to the right side, indicating potential connections. The graph on the right is a weighted bipartite graph. It also has nodes labeled 1, 2, and 3 on the left and 1', 2', 3', and 4' on the right. The connections are represented by colored lines of varying thickness: green lines with a value of 0.3, red lines with a value of 0.5, and blue lines with a value of 0.2. To the right of the nodes 1', 2', and 3', there are black lines with associated values: 0.2 for 1', 0.16 for 2', and 0.08 for 3'. To the right of node 4', there is a thicker black line with the value 0.56. The thickness of the colored lines appears to correspond to their associated values, with thicker lines indicating higher values.

**Figure 3.1:** The optimal transport problem as a bipartite network flow problem. Here  $n = 3, m = 4$ . All coordinates of the source histogram, **a**, are depicted as source nodes on the left labeled 1*,* 2*,* 3, whereas all coordinates of the target histogram **b** are labeled as nodes  $1', 2', 3', 4'.$  The graph is bipartite in the sense that all source nodes are connected to all target nodes, with no additional edges. To each edge  $(i, j')$  is associated a cost  $\mathbf{C}_{ij}$ . A feasible flow is represented on the right. Proposition 3.4 shows that this flow is not extremal since it has at least one cycle given by  $((1,1'), (2,1'), (2,4'), (1,4'))$ .

Image /page/6/Figure/3 description: The image displays three bipartite graphs, labeled P, Q, and R from left to right. Each graph consists of two sets of nodes. The left set of nodes is labeled 1, 2, 3, ..., n, and the right set of nodes is labeled 1', 2', 3', ..., m'. Lines connect nodes between the two sets, representing probabilities. In graph P, the connections are labeled with generic probability notations like P22', P23', P32', and P33'. Graphs Q and R show variations of these probabilities with added or subtracted epsilon values. Specifically, in graph Q, the connections P22' and P33' are increased by epsilon, while P23' and P32' are decreased by epsilon. In graph R, P22' and P33' are decreased by epsilon, while P23' and P32' are increased by epsilon. The thickness of the lines in graphs Q and R visually represents the magnitude of these modified probabilities, with thicker lines indicating larger values.

**Figure 3.2:** A solution **P** with a cycle in the graph of its support can be perturbed to obtain two feasible solutions **Q** and **R** such that **P** is their average, therefore disproving that **P** is extremal.

0. Then the graph  $G(\mathbf{P}) \stackrel{\text{def.}}{=} (V \cup V', S(\mathbf{P}))$  has no cycles. In particular, **P** cannot have more than  $n + m - 1$  nonzero entries.

*Proof.* We proceed by contradiction. Suppose that **P** is an extremal point of the polytope  $U(a, b)$  and that its corresponding set  $S(P)$  of edges, denoted F for short, is such that the graph  $G = (V \cup V', F)$  contains a cycle, namely there exists  $k > 1$  and a sequence of distinct indices  $i_1, \ldots, i_{k-1} \in [\![n]\!]$  and  $j_1, \ldots, j_{k-1} \in [\![m]\!]$  such that the set of edges *H* given below forms a subset of *F*.

$$
H = \{(i_1, j'_1), (i_2, j'_1), (i_2, j'_2), \ldots, (i_k, j'_k), (i_1, j'_k)\}.
$$

We now construct two feasible matrices **Q** and **R** such that  $P = (Q + R)/2$ . To do so, consider a *directed* cycle  $\bar{H}$  corresponding to  $H$ , namely the sequence of pairs  $i_1 \to j'_1, j'_1 \to i_2, i_2 \to j'_2, \ldots, i_k \to j'_k, j'_k \to i_1$ , as well as the elementary amount of flow

 $\varepsilon < \min_{(i,j') \in F} \mathbf{P}_{ij}$ . Consider a perturbation matrix **E** whose  $(i,j)$  entry is equal to  $\varepsilon$ if  $i \to j' \in \overline{H}$ ,  $-\varepsilon$  if  $j \to i' \in \overline{H}$ , and zero otherwise. Define matrices  $\mathbf{Q} = \mathbf{P} + \mathbf{E}$  and  $\mathbf{R} = \mathbf{P} - \mathbf{E}$  as illustrated in Figure 3.2. Because  $\varepsilon$  is small enough, all elements in **Q** and **R** are nonnegative. By construction, **E** has either lines (resp., columns) with all entries equal to 0 or exactly one entry equal to  $\varepsilon$  and another equal to  $-\varepsilon$  for those indexed by  $i_1, \ldots, i_k$  (resp.,  $j_1, \ldots, j_k$ ). Therefore, **E** is such that  $\mathbf{E} \mathbb{1}_m = \mathbb{0}_n$  and  $\mathbf{E}^T \mathbb{1}_n = \mathbb{0}_m$ , and we have that **Q** and **R** have the same marginals as **P**, and are therefore feasible. Finally  $P = (Q + R)/2$  which, since  $Q, R \neq P$ , contradicts the fact that P is an extremal point. Since a graph with *k* nodes and no cycles cannot have more than *k* − 1 edges, we conclude that  $S(\mathbf{P})$  cannot have more than  $n + m - 1$  edges, and therefore **P** cannot have more than  $n + m - 1$  nonzero entries.  $\Box$ 

#### **3.4.2 The North-West Corner Rule**

The north-west (NW) corner rule is a heuristic that produces a vertex of the polytope  **in up to**  $n + m$  **operations. This heuristic can play a role in initializing any** algorithm working on the primal, such as the network simplex outlined in the next section.

The rule starts by giving the highest possible value to  $P_{1,1}$  by setting it to min( $a_1$ ,  $b_1$ ). At each step, the entry  $P_{i,j}$  is chosen to saturate either the row constraint at *i*, the column constraint at *j*, or both if possible. The indices *i*, *j* are then updated as follows: *i* is incremented in the first case, *j* is in the second, and both *i* and *j* are in the third case. The rule proceeds until  $\mathbf{P}_{n,m}$  has received a value.

Formally, the algorithm works as follows: *i* and *j* are initialized to 1,  $r \leftarrow a_1, c \leftarrow b_1$ . While  $i \leq n$  and  $j \leq m$ , set  $t \leftarrow \min(r, c)$ ,  $\mathbf{P}_{i,j} \leftarrow t$ ,  $r \leftarrow r - t$ ,  $c \leftarrow s - t$ ; if  $r = 0$  then increment *i*, and update  $r \leftarrow \mathbf{a}_i$  if  $i \leq n$ ; if  $c = 0$  then increment *j*, and update  $c \leftarrow \mathbf{b}_j$ if  $j \leq n$ ; repeat. Here is an example of this sequence assuming  $\mathbf{a} = [0.2, 0.5, 0.3]$  and  $$ 

$$
\begin{bmatrix} \bullet & 0 & 0 \\ 0 & 0 & 0 \\ 0 & 0 & 0 \end{bmatrix}
$$
 $\rightarrow$ 
$$
\begin{bmatrix} 0.2 & 0 & 0 \\ \bullet & 0 & 0 \\ 0 & 0 & 0 \end{bmatrix}
$$
 $\rightarrow$ 
$$
\begin{bmatrix} 0.2 & 0 & 0 \\ 0.3 & \bullet & 0 \\ 0 & 0 & 0 \end{bmatrix}
$$
  
 $\rightarrow$ 
$$
\begin{bmatrix} 0.2 & 0 & 0 \\ 0.3 & 0.1 & \bullet \\ 0 & 0 & 0 \end{bmatrix}
$$
 $\rightarrow$ 
$$
\begin{bmatrix} 0.2 & 0 & 0 \\ 0.3 & 0.1 & 0.1 \\ 0 & 0 & \bullet \end{bmatrix}
$$
 $\rightarrow$ 
$$
\begin{bmatrix} 0.2 & 0 & 0 \\ 0.3 & 0.1 & 0.1 \\ 0 & 0 & 0.3 \end{bmatrix}
$$

We write  $\mathbf{NW}(\mathbf{a}, \mathbf{b})$  for the unique plan that can be obtained through this heuristic.

Note that there is, however, a much larger number of NW corner solutions that can be obtained by permuting arbitrarily the order of **a** and **b** first, computing the corresponding NW corner table, and recovering a table of  $U(a, b)$  by inverting again the order of columns and rows: setting  $\sigma = (3, 1, 2), \sigma' = (3, 2, 1)$  gives  $\mathbf{a}_{\sigma} = [0.3, 0.2, 0.5], \mathbf{b}_{\sigma'} = [0.4, 0.1, 0.5], \text{ and } \sigma^{-1} = (2, 3, 1), \sigma' = (3, 2, 1).$  Observe that

$$
\mathbf{NW}(\mathbf{a}_{\sigma}, \mathbf{b}_{\sigma'}) = \begin{bmatrix} 0.3 & 0 & 0 \\ 0.1 & 0.1 & 0 \\ 0 & 0 & 0.5 \end{bmatrix} \in \mathbf{U}(\mathbf{a}_{\sigma}, \mathbf{b}_{\sigma'}),
$$
$$
\mathbf{NW}_{\sigma^{-1}\sigma'^{-1}}(\mathbf{a}_{\sigma}, \mathbf{b}_{\sigma'}) = \begin{bmatrix} 0 & 0.1 & 0.1 \\ 0.5 & 0 & 0 \\ 0 & 0 & 0.3 \end{bmatrix} \in \mathbf{U}(\mathbf{a}, \mathbf{b}).
$$

Let  $\mathcal{N}(\mathbf{a}, \mathbf{b})$  be the set of all NW corner solutions that can be produced this way:

$$
\mathcal{N}(\mathbf{a},\mathbf{b}) \stackrel{\text{def.}}{=} \{ \mathbf{NW}_{\sigma^{-1}\sigma'^{-1}}(r_{\sigma},c_{\sigma'}), \sigma, \sigma' \in S_d \}.
$$

All NW corner solutions have by construction up to  $n + m - 1$  nonzero elements. The NW corner rule produces a table which is by construction unique for  $\mathbf{a}_{\sigma}$  and  $\mathbf{b}'_{\sigma}$ , but there is an exponential number of pairs or row/column permutations  $(\sigma, \sigma')$  that may yield the same table [Stougie, 2002, p. 2].  $\mathcal{N}(\mathbf{a}, \mathbf{b})$  forms a subset of (usually strictly included in) the set of extreme points of  $U(a, b)$  [Brualdi, 2006, Cor. 8.1.4].

### **3.5 A Heuristic Description of the Network Simplex**

Consider a feasible matrix **P** whose graph  $G(\mathbf{P}) = (V \cup V', S(\mathbf{P}))$  has no cycles. **P** has therefore no more than  $n + m - 1$  nonzero entries and is a vertex of  $U(a, b)$  by Proposition 3.4. Following Proposition 3.3, it is therefore sufficient to obtain a dual solution  $(\mathbf{f}, \mathbf{g})$  which is feasible (*i.e.*  $\mathbf{C} - \mathbf{f} \oplus \mathbf{g}$  has nonnegative entries) and complementary to **P** (pairs of indices  $(i, j')$  in  $S(\mathbf{P})$  are such that  $\mathbf{C}_{i,j} = \mathbf{f}_i + \mathbf{g}_j$ ), to prove that  $\mathbf{P}$  is optimal. The network simplex relies on two simple principles: to each feasible primal solution **P** one can associate a complementary pair (**f***,* **g**). If that pair is feasible, then we have reached optimality. If not, one can consider a modification of **P** that remains feasible and whose complementary pair (**f***,* **g**) is modified so that it becomes closer to feasibility.

## **3.5.1 Obtaining a Dual Pair Complementary to P**

The simplex proceeds by associating first to any extremal solution **P** a pair of  $(f, g)$ complementary dual variables. This is simply carried out by finding two vectors **f** and **g** such that for any  $(i, j')$  in  $S(\mathbf{P})$ ,  $\mathbf{f}_i + \mathbf{g}_j$  is equal to  $\mathbf{C}_{i,j}$ . Note that this, in itself, does not guarantee that (**f***,* **g**) is feasible.

Let *s* be the cardinality of *S*( $\bf{P}$ ). Because **P** is extremal,  $s \leq n + m - 1$ . Because  $G(\mathbf{P})$  has no cycles,  $G(\mathbf{P})$  is either a tree or a forest (a union of trees), as illustrated in Figure 3.3. Aiming for a pair  $(f, g)$  that is complementary to **P**, we consider the

Image /page/9/Figure/1 description: The image displays a bipartite graph with two sets of nodes, labeled 1 through 5 on the left and 1' through 6' on the right. Each node on the left has an associated numerical value: 0.16 for node 1, 0.4 for node 2, 0.06 for node 3, 0.24 for node 4, and 0.14 for node 5. Lines connect nodes from the left set to the right set, with the thickness of the lines potentially representing a weight or flow. For example, node 1 is connected to 1' with a green line, node 2 is connected to 2' and 3' with thick red lines, node 3 is connected to 4' with a blue line, node 4 is connected to 4' and 5' with purple lines, and node 5 is connected to 6' with a yellow line. Each node on the right also has an associated numerical value: 0.1 for 1', 0.16 for 2', 0.3 for 3', 0.1 for 4', 0.2 for 5', and 0.14 for 6'. To the right of the graph, two sets of connections are defined: F(P) and G(P). F(P) lists pairs of connected nodes, such as {1, 1'}, {1, 2'}, {2, 2'}, {2, 3'}, {3, 4'}, {4, 4'}, {4, 5'}, and {5, 6'}. G(P) is a more complex structure, seemingly representing a partition of the connections, with subsets of pairs like ({1, 2, 1', 2', 3'}, {{1, 1'}, {1, 2'}, {2, 2'}, {2, 3'}}), and ({3, 4, 4', 5'}, {{3, 4'}, {4, 4'}, {4, 5'}}), and ({5, 6'}, {{5, 6'}}).

**Figure 3.3:** A feasible transport **P** and its corresponding set of edges *S*(**P**) and graph *G*(**P**). As can be seen, the graph  $G(\mathbf{P}) = (\{1, \ldots, 5, 1', \ldots, 6'\}, S(\mathbf{P}))$  is a forest, meaning that it can be expressed as the union of tree graphs, three in this case.

following set of *s* linear equality constraints on  $n + m$  variables:

$$
\begin{aligned}\mathbf{f}_{i_1} + \mathbf{g}_{j_1} &= \mathbf{C}_{i_1, j_1} \\ \mathbf{f}_{i_2} + \mathbf{g}_{j_1} &= \mathbf{C}_{i_2, j_1} \\ &\vdots &= \vdots \\ \mathbf{f}_{i_s} + \mathbf{g}_{j_s} &= \mathbf{C}_{i_s, j_s},\end{aligned} \tag{3.6}
$$

where the elements of  $S(\mathbf{P})$  are enumerated as  $(i_1, j'_1), \ldots, (i_s, j'_s)$ .

Since  $s \leq n+m-1 \leq n+m$ , the linear system (3.6) above is always undetermined. This degeneracy can be interpreted in part because the parameterization of  $U(a, b)$ with  $n + m$  constraints results in  $n + m$  dual variables. A more careful formulation, outlined in Remark 3.1, would have resulted in an equivalent formulation with only *n* + *m* − 1 constraints and therefore *n* + *m* − 1 dual variables. However, *s* can also be strictly smaller than  $n + m - 1$ : This happens when  $G(\mathbf{P})$  is the disjoint union of two or more trees. For instance, there are  $5 + 6 = 11$  dual variables (one for each node) in Figure 3.3, but only 8 edges among these 11 nodes, namely 8 linear equations to define (**f***,* **g**). Therefore, there will be as many undetermined dual variables under that setting as there will be connected components in  $G(\mathbf{P})$ .

Consider a tree among those listed in  $G(\mathbf{P})$ . Suppose that tree has *k* nodes  $i_1, \ldots, i_k$ among source nodes and *l* nodes  $j'_1, \ldots, j'_l$  among target nodes, resulting in  $r \stackrel{\text{def.}}{=} k + l$ , and  $r - 1$  edges, corresponding to k variables in **f** and l variables in **g**, linked with *r* − 1 linear equations. To lift an indetermination, we can choose arbitrarily a root node in that tree and assign the value 0 to its corresponding dual variable. From there, we can traverse the tree using a breadth-first or depth-first search to obtain a sequence of simple variable assignments that determines the values of all other dual variables in that tree, as illustrated in Figure 3.4. That procedure can then be repeated for all trees in the graph of **P** to obtain a pair of dual variables  $(f, g)$  that is complementary to **P**.

Image /page/10/Figure/1 description: The image displays a diagram illustrating a network or flow, with nodes labeled '1', '1'', '2', '2'', and '3''. There are lines connecting these nodes, with some lines colored green and others red. Associated with each node or connection are mathematical equations. Specifically, f1 is defined as 0, and it connects to node '1''. g1 is defined as C1,1 - f1 and connects node '1' to '1''. f2 is defined as C2,1 - g1 and connects node '1'' to '2'. g2 is defined as C2,2 - f2 and connects node '2' to '2''. g3 is defined as C2,3 - f2 and connects node '2' to '3''.

**Figure 3.4:** The five dual variables  $f_1, f_2, g_1, g_2, g_3$  corresponding to the five nodes appearing in the first tree of the graph *G*(**P**) illustrated in Figure 3.3 are linked through four linear equations that involve corresponding entries in the cost matrix **C**. Because that system is degenerate, we choose a root in that tree (node 1 in this example) and set its corresponding variable to 0 and proceed then by traversing the tree (either breadth-first or depth-first) from the root to obtain iteratively the values of the four remaining dual variables.

### **3.5.2 Network Simplex Update**

The dual pair  $(f, g)$  obtained previously might be feasible, in the sense that for all  $i, j$ we have  $\mathbf{f}_i + \mathbf{g}_j \leq \mathbf{C}_{i,j}$ , in which case we have reached the optimum by Proposition 3.3. When that is not the case, namely when there exists *i, j* such that  $\mathbf{f}_i + \mathbf{g}_j > \mathbf{C}_{i,j}$ , the network simplex algorithm kicks in. We first initialize a graph *G* to be equal to the graph  $G(\mathbf{P})$  corresponding to the feasible solution **P** and add the violating edge  $(i, j')$ to *G*. Two cases can then arise:

- (a) *G* is (still) a forest, which can happen if  $(i, j')$  links two existing subtrees. The approach outlined in §3.5.1 can be used on graph *G* to recover a new complementary dual vector  $(f, g)$ . Note that this addition simply removes an indetermination among the  $n + m$  dual variables and does not result in any change in the primal variable **P**. That update is usually called degenerate in the sense that  $(i, j')$  has now entered graph *G* although  $P_{i,j}$  remains 0.  $G(P)$  is, however, contained in *G*.
- (b) *G* now has a cycle. In that case, we need to remove an edge in *G* to ensure that *G* is still a forest, yet also modify **P** so that **P** is feasible and  $G(\mathbf{P})$  remains included in *G*. These operations can all be carried out by increasing the value of  $P_{i,j}$  and modifying the other entries of **P** appearing in the detected cycle, in a manner very similar to the one we used to prove Proposition 3.4. To be more precise, let us write that cycle  $(i_1, j'_1), (j'_1, i_2), (i_2, j'_2), \ldots, (i_l, j'_l), (j'_l, i_{l+1})$  with the convention that  $i_1 = i_{l+1} = i$  to ensure that the path is a cycle that starts and ends at *i*, whereas  $j_1 = j$ , to highlight the fact that the cycle starts with the added edge  ${i, j}$ , going in the right direction. Increase now the flow of all "positive" edges  $(i_k, j'_k)$  (for  $k \leq l$ ), and decrease that of "negative" edges  $(j'_k, i_{k+1})$  (for  $k \leq l$ ), to obtain an updated primal solution  $\hat{P}$ , equal to  $P$  for all but the following entries:

$$
\forall k \leq l, \quad \tilde{\mathbf{P}}_{i_k,j_k} := \mathbf{P}_{i_k,j_k} + \theta; \quad \tilde{\mathbf{P}}_{i_{k+1},j_k} := \mathbf{P}_{i_{k+1},j_k} - \theta.
$$

Here,  $\theta$  is the largest possible increase at index *i, j* using that cycle. The value

of  $\theta$  is controlled by the smallest flow negatively impacted by the cycle, namely  $\min_k \mathbf{P}_{i_{k+1},j_k}$ . That update is illustrated in Figure 3.5. Let  $k^*$  be an index that achieves that minimum. We then close the update by removing  $(i_{k^*+1}, j_{k^*})$  from *G*, to compute new dual variables (**f***,* **g**) using the approach outlined in §3.5.1.

### **3.5.3 Improvement of the Primal Solution**

Although this was not necessarily our initial motivation, one can show that the manipulation above can only improve the cost of **P**. If the added edge has not created a cycle, case (a) above, the primal solution remains unchanged. When a cycle is created, case (b),  $P$  is updated to  $\dot{P}$ , and the following equality holds:

$$
\langle \tilde{\mathbf{P}}, \mathbf{C} \rangle - \langle \mathbf{P}, \mathbf{C} \rangle = \theta \left( \sum_{k=1}^{l} \mathbf{C}_{i_k, j_k} - \sum_{k=1}^{l} \mathbf{C}_{i_{k+1}, j_k} \right).
$$

We now use the dual vectors (**f***,* **g**) computed at the end of the previous iteration. They are such that  $f_{i_k} + g_{i_k} = \mathbf{C}_{i_k, j_k}$  and  $f_{i_{k+1}} + g_{i_k} = \mathbf{C}_{i_{k+1}, j_k}$  for all edges initially in G, resulting in the identity

$$
\sum_{k=1}^{l} C_{i_k,j_k} - \sum_{k=1}^{l} C_{i_{k+1},j_k} = C_{i,j} + \sum_{k=2}^{l} f_{i_k} + g_{j_k} - \sum_{k=1}^{l} f_{i_{k+1}} + g_{j_k}
$$
$$
= C_{i,j} - (f_i + g_j).
$$

That term is, by definition, negative, since  $i, j$  were chosen because  $C_{i,j} < \mathbf{f}_i - \mathbf{g}_j$ . Therefore, if  $\theta > 0$ , we have that

$$
\langle \tilde{\mathbf{P}}, \, \mathbf{C} \rangle = \langle \mathbf{P}, \, \mathbf{C} \rangle + \theta \left( \mathbf{C}_{i,j} - (\mathbf{f}_i - \mathbf{f}_g) \right) < \langle \mathbf{P}, \, \mathbf{C} \rangle.
$$

If  $\theta = 0$ , which can happen if *G* and *G*(**P**) differ, the graph *G* is simply changed, but **P** is not.

The network simplex algorithm can therefore be summarized as follows: Initialize the algorithm with an extremal solution **P**, given for instance by the NW corner rule as covered in §3.4.2. Initialize the graph *G* with  $G(\mathbf{P})$ . Compute a pair of dual variables  $(f, g)$  that are complementary to **P** using the linear system solve using the tree structure(s) in *G* as described in §3.5.1. (i) Look for a violating pair of indices to the constraint  $\mathbf{C} - \mathbf{f} \oplus \mathbf{g} \geq 0$ ; if none, **P** is optimal and stop. If there is a violating pair  $(i, j')$ , (ii) add the edge  $(i, j')$  to *G*. If *G* still has no cycles, update  $(f, g)$  accordingly; if there is a cycle, direct it making sure  $(i, j')$  is labeled as positive, and remove a negative edge in that cycle with the smallest flow value, updating **P***, G* as illustrated in Figure 3.5, then build a complementary pair **f***,* **g** accordingly; return to (i). Some of the operations above require graph operations (cycle detection, tree traversals) which can be implemented efficiently in this context, as described in ([Bertsekas, 1998, §5]).

Image /page/12/Figure/1 description: This image displays three bipartite graphs, labeled (a), (b.1), and (b.2), illustrating changes in connections and their associated weights. Each graph has two columns of nodes, labeled 1 through 5 on the left and 1' through 6' on the right. Weights are indicated next to the nodes and along the connecting lines. Graph (a) shows initial connections, with labels indicating '{3, 3'} added. Graph (b.1) shows '{1, 3'} added, with new connections and altered weights, including a dashed line from 3 to 3' with weight 0.06 and a thick red arrow from 2 to 3' with weight 0.3. Graph (b.2) shows '{1, 1'} removed, with a dotted green line from 1 to 1' with weight 0.06 and a thick red arrow from 2 to 3' with weight 0.24, and a red arrow from 2 to 1' with weight 0.1. The weights shown are: Left column: 0.06 for node 1, 0.46 for node 2, 0.06 for node 3, 0.24 for node 4, and 0.14 for node 5. Right column: 0.1 for node 1', 0.12 for node 2', 0.3 for node 3', 0.1 for node 4', 0.2 for node 5', and 0.14 for node 6'.

**Figure 3.5:** Adding an edge  $\{i, j\}$  to the graph  $G(\mathbf{P})$  can result in either (a) the graph remains a forest after this addition, in which case **f***,* **g** can be recomputed following the approach outlined in §3.5.1; (b.1) the addition of that edge creates a cycle, from which we can define a directed path; (b.2) the path can be used to increase the value of  $P_{i,j}$  and propagate that change along the cycle to maintain the flow feasibility constraints, until the flow of one of the edges that is negatively impacted by the cycle is decreased to 0. This removes the cycle and updates **P**.

Orlin [1997] was the first to prove the polynomial time complexity of the network simplex. Tarjan [1997] provided shortly after an improved bound in  $O((n+m)nm \log(n+m) \log((n+m)||\mathbf{C}||_{\infty}))$  which relies on more efficient data structures to help select pivoting edges.

## **3.6 Dual Ascent Methods**

Dual ascent methods precede the network simplex by a few decades, since they can be traced back to work by Borchardt and Jocobi [1865] and later König and Egerváry, as recounted by Kuhn [1955]. The Hungarian algorithm is the best known algorithm in that family, and it can work only in the particular case when **a** and **b** are equal and are both uniform, namely  $\mathbf{a} = \mathbf{b} = \mathbb{1}_n/n$ . We provide in what follows a concise description of the more general family of dual ascent methods. This requires the knowledge of the maximum flow problem ([Bertsimas and Tsitsiklis, 1997, §7.5]). By contrast to the network simplex, presented above in the primal, dual ascent methods maintain at each iteration dual feasible solutions whose objective is progressively improved by adding a sparse vector to **f** and **g**. Our presentation is mostly derived from that of ([Bertsimas and Tsitsiklis, 1997, §7.7]) and starts with the following definition.

**Definition 3.2.** For  $S \subset [\![n]\!], S' \subset [\![m]\!]$ <sup>, def.</sup>  $\{1', \ldots, m'\}$  we write  $\mathbb{1}_S$  for the vector in  $\mathbb{R}^n$ of zeros except for ones at the indices enumerated in  $S$ , and likewise for the vector  $\mathbb{1}_{S}$ <sup>0</sup> in  $\mathbb{R}^m$  with indices in  $S'$ .

In what follows,  $(f, g)$  is a feasible dual pair in  $R(C)$ . Recall that this simply means that for all pairs  $(i, j') \in [n] \times [m]$ ,  $\mathbf{f}_i + \mathbf{g}_j \leq \mathbf{C}_{ij}$ . We say that  $(i, j')$  is a *balanced* pair (or edge) if  $\mathbf{f}_i + \mathbf{g}_j = \mathbf{C}_{ij}$  and *inactive* otherwise, namely if  $\mathbf{f}_i + \mathbf{g}_j < \mathbf{C}_{ij}$ . With this convention, we start with a simple result describing how a feasible dual pair  $(f, g)$  can be perturbed using sparse vectors indexed by sets  $S$  and  $S'$  and still remain feasible.

**Proposition 3.5.**  $(\tilde{\mathbf{f}}, \tilde{\mathbf{g}}) \stackrel{\text{def.}}{=} (\mathbf{f}, \mathbf{g}) + \varepsilon (\mathbb{1}_S, -\mathbb{1}_{S'})$  is dual feasible for a small enough  $\varepsilon > 0$ if for all  $i \in S$ , the fact that  $(i, j')$  is balanced implies that  $j' \in S'$ .

*Proof.* For any  $i \in S$ , consider the set  $\mathcal{I}_i$  of all  $j' \in [\![m]\!]$  such that  $(i, j')$  is inactive, namely such that  $\mathbf{f}_i + \mathbf{g}_j < \mathbf{C}_{ij}$ . Define  $\varepsilon_i \stackrel{\text{def.}}{=} \min_{j \in I_i} \mathbf{C}_{i,j} - \mathbf{f}_i - \mathbf{g}_j$ , the smallest margin by which  $f_i$  can be increased without violating the constraints corresponding to  $j' \in \mathcal{I}_i$ . Indeed, one has that if  $\varepsilon \leq \varepsilon_i$  then  $\tilde{\mathbf{f}}_i + \tilde{\mathbf{g}}_j < \mathbf{C}_{i,j}$  for any  $j' \in \mathcal{I}_i$ . Consider now the set  $\mathcal{B}_i$  of balanced edges associated with *i*. Note that  $\mathcal{B}_i = [\![m]\!]' \setminus \mathcal{I}_i$ . The assumption above<br>in that is  $\mathcal{B} \subset \mathcal{B}$ . is that  $j' \in \mathcal{B}_i \Rightarrow j' \in S'$ . Therefore, one has that for  $j' \in \mathcal{B}_i$ ,  $\tilde{\mathbf{f}}_i + \tilde{\mathbf{g}}_j = \mathbf{f}_i + \mathbf{g}_j = \mathbf{C}_{i,j}$ . As a consequence, the inequality  $\tilde{\mathbf{f}}_i + \tilde{\mathbf{g}}_j \leq \mathbf{C}_{i,j}$  is ensured for any  $j \in [\![m]\!]'$ . Choosing now an increase  $\varepsilon$  smaller than the smallest possible allowed, namely  $\min_{i \in S} \varepsilon_i$ , we recover that  $(\tilde{\mathbf{f}}, \tilde{\mathbf{g}})$  is dual feasible.  $\Box$ 

The main motivation behind the iteration of the network simplex presented in §3.5.1 is to obtain, starting from a feasible primal solution **P**, a complementary feasible dual pair  $(f, g)$ . To reach that goal, **P** is progressively modified such that its complementary dual pair reaches dual feasibility. A symmetric approach, starting from a feasible dual variable to obtain a feasible primal **P**, motivates dual ascent methods. The proposition below is the main engine of dual ascent methods in the sense that it guarantees (constructively) the existence of an ascent direction for  $(\mathbf{f}, \mathbf{g})$  that maintains feasibility. That direction is built, similarly to the network simplex, by designing a candidate primal solution **P** whose infeasibility guides an update for  $(f, g)$ .

**Proposition 3.6.** Either  $(f, g)$  is optimal for Problem (3.4) or there exists *S* ⊂  $\llbracket n \rrbracket$ , *S'* ⊂  $\llbracket m \rrbracket'$  such that  $(\tilde{\mathbf{f}}, \tilde{\mathbf{g}}) \stackrel{\text{def.}}{=} (\mathbf{f}, \mathbf{g}) + \varepsilon(\mathbb{1}_S, -\mathbb{1}_{S'})$  is feasible for a small enough  $\varepsilon > 0$  and has a strictly better objective.

*Proof.* We consider first a complementary primal variable **P** to  $(f, g)$ . To that effect, let B be the set of balanced edges, namely all pairs  $(i, j') \in [n] \times [m]$  such that  $\mathbf{f}_i + \mathbf{g}_j =$ <br>C such that the kinemity weak when writing (1 set 1's m) are linked with  $\mathbf{C}_{i,j}$ , and form the bipartite graph whose vertices  $\{1, \ldots, n, 1', \ldots, m'\}$  are linked with edges in B only, complemented by a source node *s* connected with *capacitated* edges to all nodes  $i \in [n]$  with respective capacities  $a_i$ , and a terminal node *t* also connected<br>to all nodes  $i' \in [m]$  with advance of perpetitive connective **b** as seen in Figure 2.6. to all nodes  $j' \in [m]'$  with edges of respective capacities  $\mathbf{b}_j$ , as seen in Figure 3.6.<br>The First Fullmann electrical (Deptriman and Tritriblic 1007 x 2051) are happed to The Ford–Fulkerson algorithm ([Bertsimas and Tsitsiklis, 1997, p. 305]) can be used to compute a maximal flow **F** on that network, namely a family of  $n+m+|\mathcal{B}|$  nonnegative values indexed by  $(i, j') \in \mathcal{B}$  as  $f_{si} \leq \mathbf{a}_i, f_{ij'}, f_{j't} \leq \mathbf{b}_j$  that obey flow constraints and such that  $\sum_i f_{si}$  is maximal. If the throughput of that flow  $\bf F$  is equal to 1, then a feasible primal solution **P**, complementary to **f***,* **g** by construction, can be extracted from **F** by defining  $\mathbf{P}_{i,j} = f_{ij'}$  for  $(i, j') \in \mathcal{B}$  and zero elsewhere, resulting in the optimality of  $(\mathbf{f}, \mathbf{g})$ 

50

and **P** by Proposition 3.3. If the throughput of **F** is strictly smaller than 1, the labeling algorithm proceeds by labeling (identifying) those nodes reached iteratively from *s* for which **F** does not saturate capacity constraints, as well as those nodes that contribute flow to any of the labeled nodes. Labeled nodes are stored in a nonempty set *Q*, which does not contain the terminal node *t* per optimality of **F** (see Bertsimas and Tsitsiklis 1997, p. 308, for a rigorous presentation of the algorithm). *Q* can be split into two sets  $S = Q \cap [n]$  and  $S' = Q \cap [m]$ . Because we have assumed that the total throughput is strictly smaller than 1,  $S \neq \emptyset$ . Note first that if  $i \in S$  and  $(i, j)$  is balanced, then  $j'$ is necessarily in  $S'$ . Indeed, since all edges  $(i, j')$  have infinite capacity by construction, the labeling algorithm will necessarily reach  $j'$  if it includes  $i$  in  $S$ . By Proposition 3.5, there exists thus a small enough  $\varepsilon$  to ensure the feasibility of  $\tilde{\mathbf{f}}$ ,  $\tilde{\mathbf{g}}$ . One still needs to prove that  $1 \text{I}_{S}^{T} \mathbf{a} - 1 \text{I}_{S}^{T} \mathbf{b} > 0$  to ensure that  $(\tilde{\mathbf{f}}, \tilde{\mathbf{g}})$  has a better objective than  $(\mathbf{f}, \mathbf{g})$ . Let  $\bar{S} = [\![n]\!] \setminus S$  and  $\bar{S'} = [\![m]\!]' \setminus S'$  and define

$$
A = \sum_{i \in S} f_{si}, \quad B = \sum_{i \in \overline{S}} f_{si}, \quad C = \sum_{j' \in S'} f_{j't}, \quad D = \sum_{j' \in \overline{S}'} f_{j't}.
$$

The total maximal flow starts from  $s$  and is therefore equal to  $A + B$ , but also arrives at t and is therefore equal to  $C + D$ . Flow conservation constraints also impose that the very same flow is equal to  $B + C$ , therefore  $A = C$ . On the other hand, by definition of the labeling algorithm, we have for all *i* in *S* that  $f_{si} < \mathbf{a}_i$ , whereas  $f_{j't} = \mathbf{b}_j$  for  $j' \in \overline{S}'$  because *t* cannot be in  $S'$  by optimality of the considered flow. We therefore <sup>T</sup>**b**. Therefore  $1_S$ <sup>T</sup>**a** −  $1'_S$  ${}^{T}\mathbf{b} > A - C = 0.$ have  $A < \mathbb{1}_S$ <sup>T</sup>**a** and  $C = \mathbb{1}_S'$  $\Box$ 

The dual ascent method proceeds by modifying any feasible solution  $(f, g)$  by any vector generated by sets  $S, S'$  that ensure feasibility and improve the objective. When the sets  $S, S'$  are those given by construction in the proof of Proposition 3.6, and the steplength  $\varepsilon$  is defined as in the proof of Proposition 3.5, we recover a method known as the *primal-dual* method. That method reduces to the Hungarian algorithm for matching problems. Dual ascent methods share similarities with the dual variant of the network simplex, yet they differ in at least two important aspects. Simplex-type methods always ensure that the current solution is an *extreme point* of the feasible set,  $R(C)$  for the dual, whereas dual ascent as presented here does not make such an assumption, and can freely produce iterates that lie in the interior of the feasible set. Additionally, whereas the dual network simplex would proceed by modifying  $(f, g)$ to produce a primal solution **P** that satisfies linear (marginal constraints) but only nonnegativity upon convergence, dual ascent builds instead a primal solution **P** that is always nonnegative but which does not necessarily satisfy marginal constraints.

Image /page/15/Figure/1 description: The image displays four network flow diagrams labeled (a), (b), (c), and (d). Diagram (a) shows a network with a source node 's' connected to five intermediate nodes labeled 1 through 5. Each of these intermediate nodes is connected to six nodes labeled 1' through 6' by dashed lines with infinite capacity. The nodes 1' through 6' are then connected to a sink node 't' with specified capacities: 0.1, 0.12, 0.3, 0.1, 0.2, and 0.14 respectively. The question below diagram (a) asks for the maximal flow possible given balanced edges. Diagram (b) illustrates the same network but with colored lines representing flow, and it states 'Maxflow = 0.74'. The colors and thicknesses of the lines indicate the flow amounts. For example, the edge from 's' to node 2 has a thick red line with a label 0.46, and the edge from node 2 to node 2' has a thick red line with a label 0.12. Diagram (c) shows a subset of the network with nodes S = {2, 3, 4, 5} highlighted and connected by thick orange and red lines, indicating a specific flow. It also shows a set S' = {2', 3', 4', 6'} with dashed outlines. The text below states '(c) the labeling algorithm identifies sets S, S''. Diagram (d) shows the total flows A, B, C, D through nodes S, S, S', S' with colored lines representing these flows. For instance, flow A is a green line from 's' to node 1, flow B is a thick blue line from 's' to node 2, flow C is a thick red line from node 3' to 't', and flow D is a thick purple line from node 5' to 't'.

**Figure 3.6:** Consider a transportation problem involving the marginals introduced first in Figure 3.3, with  $n = 5, m = 6$ . Given two feasible dual vectors **f**, **g**, we try to obtain the "best" flow matrix P that is complementary to  $(f, g)$ . Recall that this means that **P** can only take positive values on those edges  $(i, j')$  corresponding to indices for which  $\mathbf{f}_i + \mathbf{g}_j = \mathbf{C}_{i,j}$ , here represented with dotted lines in plot (a). The best flow that can be achieved with that graph structure can be formulated as a max-flow problem in a capacitated network, starting from an abstract source node *s* connected to all nodes labeled  $i \in \llbracket n \rrbracket$ , terminating at an abstract terminal node *t* connected to all nodes labeled *j'*, where  $j \in [m']$ , and such that the connection of odge  $(a, i)$   $(d', j)$   $\in \mathbb{R}$ ,  $\mathbb{R}$ ,  $\mathbb{R}$  are reconoctively  $p$ , be and all others that the capacities of edge  $(s, i)$ ,  $(j', t)$ ,  $i \in [\![n]\!]$ ,  $j \in [\![m]\!]$  are respectively  $\mathbf{a}_i, \mathbf{b}_j$  and all others infinite.<br>The Ford Fullerson elemithm ([Portsimes and Tritsiblic 1007, p. 205]) can be applied to comput The Ford–Fulkerson algorithm ([Bertsimas and Tsitsiklis, 1997, p. 305]) can be applied to compute such a max-flow, which, as represented in plot (b), only achieves 0*.*74 units of mass out of 1 needed to solve the problem. One of the subroutines used by max-flow algorithms, the labeling algorithm ([Bertsimas and Tsitsiklis, 1997, p. 308]), can be used to identify nodes that receive an unsaturated flow from *s* (and recursively, all of its successors), denoted by orange lines in plot (c). The labeling algorithm also adds by default nodes that send a positive flow to any labeled node, which is the criterion used to select node 3, which contributes with a red line to  $3'$ . Labeled nodes can be grouped in sets  $S, S'$  to identify nodes which can be better exploited to obtain a higher flow, by modifying **f***,* **g** to obtain a different graph. The proof involves partial sums of flows described in plot (d)

# **3.7 Auction Algorithm**

The auction algorithm was originally proposed by Bertsekas [1981] and later refined in [Bertsekas and Eckstein, 1988]. Several economic interpretations of this algorithm have been proposed (see *e.g.* Bertsekas [1992]). The algorithm can be adapted for arbitrary marginals, but we present it here in its formulation to solve optimal assignment problems.

### 3.7. Auction Algorithm 53

**Complementary slackness.** Notice that in the optimal assignment problem, the primal-dual conditions presented for the optimal transport problem become easier to formulate, because any extremal solution **P** is necessarily a permutation matrix  $P_\sigma$ for a given  $\sigma$  (see Equation (3.3)). Given primal  $\mathbf{P}_{\sigma^*}$  and dual  $\mathbf{f}^*, \mathbf{g}^*$  optimal solutions we necessarily have that

$$
\mathbf{f}_i^\star + \mathbf{g}_{\sigma_i^\star}^\star = \mathbf{C}_{i, \sigma_i^\star}.
$$

Recall also that, because of the principle of **C**-transforms enunciated in §3.2, that one can choose  $f^*$  to be equal to  $g^{\overline{C}}$ . We therefore have that

$$
\mathbf{C}_{i,\sigma_i^*} - \mathbf{g}_{\sigma_i}^* = \min_j \mathbf{C}_{i,j} - \mathbf{g}_j^*.
$$
 (3.7)

On the contrary, it is easy to show that if there exists a vector **g** and a permutation  $\sigma$ such that

$$
\mathbf{C}_{i,\sigma_i} - \mathbf{g}_{\sigma_i} = \min_j \mathbf{C}_{i,j} - \mathbf{g}_j \tag{3.8}
$$

holds, then they are both optimal, in the sense that  $\sigma$  is an optimal assignment and  $g^{\bar{C}}$ ,  $g$  is an optimal dual pair.

**Partial assignments and** *ε***-complementary slackness.** The goal of the auction algorithm is to modify iteratively a triplet *S*,  $\xi$ , **g**, where *S* is a subset of  $\llbracket n \rrbracket$ ,  $\xi$  a partial assignment vector, namely an injective map from *S* to  $\llbracket n \rrbracket$ , and **g** a dual vector. The dual vector is meant to converge toward a solution satisfying an *approximate* complementary slackness property (3.8), whereas *S* grows to cover  $\llbracket n \rrbracket$  as  $\xi$  describes a permutation. The algorithm works by maintaining the three following properties after each iteration:

- (a)  $\forall i \in S$ ,  $\mathbf{C}_{i,\xi_i} \mathbf{g}_{\xi_i} \leq \varepsilon + \min_j \mathbf{C}_{i,j} \mathbf{g}_j$  ( $\varepsilon$ -CS).
- (b) The size of *S* can only increase at each iteration.
- (c) There exists an index *i* such that  $\mathbf{g}_i$  decreases by at least  $\varepsilon$ .

**Auction algorithm updates.** Given a point *j* the auction algorithm uses not only the optimum appearing in the usual **C**-transform but also a second best,

$$
j_i^1 \in \operatorname{argmin}_j \mathbf{C}_{i,j} - \mathbf{g}_j, \quad j_i^2 \in \operatorname{argmin}_{j \neq j_i^1} \mathbf{C}_{i,j} - \mathbf{g}_j,
$$

to define the following updates on **g** for an index  $i \notin S$ , as well as on *S* and  $\xi$ :

1. **update g**: Remove to the  $j_i^1$ <sup>th</sup> entry of **g** the sum of  $\varepsilon$  and the difference between the second lowest and lowest adjusted cost  $\{C_{i,j} - g_j\}_j$ ,

$$
\mathbf{g}_{j_i^1} \leftarrow \mathbf{g}_{j_i^1} - \underbrace{\left((\mathbf{C}_{i,j_i^2} - \mathbf{g}_{j_i^2}) - (\mathbf{C}_{i,j_i^1} - \mathbf{g}_{j_i^1}) + \varepsilon\right)}_{\geq \varepsilon > 0}
$$

$$
= \mathbf{C}_{i,j_i^1} - (\mathbf{C}_{i,j_i^2} - \mathbf{g}_{j_i^2}) - \varepsilon.
$$
 $(3.9)$ 

2. **update** *S* **and**  $\xi$ : If there exists an index  $i' \in S$  such that  $\xi_{i'} = j_i^1$ , remove it by updating  $S \leftarrow S \setminus \{i'\}$ . Set  $\xi_i = j_i^1$  and add *i* to  $S, S \leftarrow S \cup \{i\}$ .

**Algorithmic properties.** The algorithm proceeds by starting from an empty set of assigned points  $S = \emptyset$  with no assignment and empty partial assignment vector  $\xi$ , and  $\mathbf{g} = \mathbf{0}_n$ , terminates when  $S = \llbracket n \rrbracket$ , and loops through both steps above until it terminates. The fact that properties (b) and (c) are valid after each iteration is made obvious by the nature of the updates (it suffices to look at Equation (3.9)).  $\varepsilon$ complementary slackness is easy to satisfy at the first iteration since in that case  $S = \emptyset$ . The fact that iterations preserve that property is shown by the following proposition.

**Proposition 3.7.** The auction algorithm maintains *ε*-complementary slackness at each iteration.

*Proof.* Let  $\mathbf{g}, \xi, S$  be the three variables at the beginning of a given iteration. We therefore assume that for any  $i' \in S$  the relationship

$$
\mathbf{C}_{i,\xi_{i'}} - \mathbf{g}_{\xi_{i'}} \leq \varepsilon + \min_{j} \mathbf{C}_{i',j} - \mathbf{g}_j
$$

holds. Consider now the particular  $i \notin S$  considered in an iteration. Three updates happen: **g**,  $\xi$ , S are updated to  $\mathbf{g}^{\text{n}}$ ,  $\xi^{\text{n}}$ ,  $S^{\text{n}}$  using indices  $j_i^1$  and  $j_i^2$ . More precisely,  $\mathbf{g}^{\text{n}}$  is equal to **g** except for element  $j_i^1$ , whose value is equal to

$$
\textbf{g}_{j_i^1}^{\mathrm{n}}=\textbf{g}_{j_i^1}-\left((\textbf{C}_{i,j_i^2}-\textbf{g}_{j_i^2})-(\textbf{C}_{i,j_i^1}-\textbf{g}_{j_i^1})\right)-\varepsilon\leq \textbf{g}_{j_i^1}-\varepsilon
$$

,  $\xi^{\text{n}}$  is equal to  $\xi$  except for its *i*th element equal to  $j_i^1$ , and  $S^{\text{n}}$  is equal to the union of  $\{i\}$  with *S* (with possibly one element removed). The update of  $\mathbf{g}^n$  can be rewritten

$$
\mathbf{g}_{j_i^1}^{n} = \mathbf{C}_{i,j_i^1} - (\mathbf{C}_{i,j_i^2} - \mathbf{g}_{j_i^2}) - \varepsilon;
$$

therefore we have

$$
\mathbf{C}_{i,j_i^1}-\mathbf{g}_{j_i^1}^{\mathrm{n}}=\varepsilon+(\mathbf{C}_{i,j_i^2}-\mathbf{g}_{j_i^2})=\varepsilon+\min_{j\neq j_i^1}(\mathbf{C}_{i,j}-\mathbf{g}_j).
$$

Since  $-\mathbf{g} \leq -\mathbf{g}^n$  this implies that

$$
\mathbf{C}_{i,j_i^1}-\mathbf{g}_{j_i^1}^{\mathrm{n}}=\varepsilon+\min_{j\neq j_i^1}(\mathbf{C}_{i,j}-\mathbf{g}_j)\leq\varepsilon+\min_{j\neq j_i^1}(\mathbf{C}_{i,j}-\mathbf{g}_j^{\mathrm{n}}),
$$

and since the inequality is also obviously true for  $j = j_i^1$  we therefore obtain the  $\varepsilon$ complementary slackness property for index *i*. For other indices  $i' \neq i$ , we have again that since  $\mathbf{g}^n \leq \mathbf{g}$  the sequence of inequalities holds,

$$
\mathbf{C}_{i,\xi_{i'}^n} - \mathbf{g}_{\xi_{i'}^n}^n = \mathbf{C}_{i,\xi_{i'}} - \mathbf{g}_{\xi_{i'}} \leq \varepsilon + \min_j \mathbf{C}_{i',j} - \mathbf{g}_j \leq \varepsilon + \min_j \mathbf{C}_{i',j} - \mathbf{g}_j^n.
$$

### 3.7. Auction Algorithm 55

**Proposition 3.8.** The number of steps of the auction algorithm is at most  $N =$  $n\|\mathbf{C}\|_{\infty}/\varepsilon$ .

*Proof.* Suppose that the algorithm has not stopped after  $T > N$  steps. Then there exists an index *j* which is not in the image of  $\xi$ , namely whose price coordinate  $g_i$  has never been updated and is still  $\mathbf{g}_j = 0$ . In that case, there cannot exist an index  $j'$  such that  $\mathbf{g}_{j'}$  was updated *n* times with  $n > ||\mathbf{C}||_{\infty}/\varepsilon$ . Indeed, if that were the case then for any index *i*

$$
\textbf{g}_{j'}\leq-n\varepsilon<-\Vert\textbf{C}\Vert_\infty\leq-\textbf{C}_{i,j}=\textbf{g}_j-\textbf{C}_{i,j},
$$

which would result in, for all *i*,

$$
\mathbf{C}_{i,j'} - \mathbf{g}_{j'} > \mathbf{C}_{i,j} + (\mathbf{C}_{i,j} - \mathbf{g}_j),
$$

which contradicts  $\varepsilon$ -CS. Therefore, since there cannot be more than  $||C||_{\infty}/\varepsilon$  updates for each variable, the total number of iterations *T* cannot be larger than  $n||\mathbf{C}||_{\infty}/\varepsilon =$ *N*.  $\Box$ 

**Remark 3.3.** Note that this result yields a naive number of operations of  $N^3||\mathbf{C}||_{\infty}/\varepsilon$ for the algorithm to terminate. That complexity can be reduced to  $N^3 \log ||\mathbf{C}||_{\infty}$  when using a clever method known as *ε*-scaling, designed to decrease the value of *ε* with each iteration ([Bertsekas, 1998, p. 264]).

**Proposition 3.9.** The auction algorithm finds an assignment whose cost is *nε* suboptimal.

*Proof.* Let  $\sigma, \mathbf{g}^*$  be the primal and dual optimal solutions of the assignment problem of matrix **C**, with optimum

$$
t^{\star} = \sum \mathbf{C}_{i, \sigma_i} = \sum_i \min_j \mathbf{C}_{i, j} - \mathbf{g}_j^{\star} + \sum_j \mathbf{g}_j^{\star}.
$$

Let  $\xi$ , **g** be the solutions output by the auction algorithm upon termination. The  $\varepsilon$ -CS conditions yield that for any  $i \in S$ ,

$$
\min_j \mathbf{C}_{i,j} - \mathbf{g}_j \geq \mathbf{C}_{i,\xi_i} - \mathbf{g}_{\xi_i} - \varepsilon.
$$

Therefore by simple suboptimality of **g** we first have

$$
t^{\star} \geq \sum_{i} \left( \min _{j} C_{i, j}-g_{j}\right)+\sum_{j} g_{j} \geq \sum_{i}-\varepsilon+\left(C_{i, \xi_{i}}-g_{\xi_{i}}\right)+\sum_{j} g_{j}=-n \varepsilon+\sum_{i} C_{i, \xi_{j}} 
\geq-n \varepsilon+t^{\star}.
$$

where the second inequality comes from  $\varepsilon$ -CS, the next equality by cancellation of the sum of terms in  $g_{\xi_i}$  and  $g_j$ , and the last inequality by the suboptimality of  $\xi$  as a permutation. $\Box$ 

The auction algorithm can therefore be regarded as an alternative way to use the machinery of **C**-transforms. Next we explore another approach grounded on regularization, the so-called Sinkhorn algorithm, which also bears similarities with the auction algorithm as discussed in [Schmitzer, 2016b].

Note finally that, on low-dimensional regular grids in Euclidean space, it is possible to couple these classical linear solvers with multiscale strategies, to obtain a significant speed-up [Schmitzer, 2016a, Oberman and Ruan, 2015].