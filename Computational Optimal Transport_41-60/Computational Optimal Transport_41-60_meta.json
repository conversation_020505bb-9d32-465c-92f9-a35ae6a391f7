{"table_of_contents": [{"title": "3", "heading_level": null, "page_id": 0, "polygon": [[303.8739290085679, 136.0496826171875], [321.76513671875, 136.0496826171875], [321.76513671875, 161.3477783203125], [303.8739290085679, 161.3477783203125]]}, {"title": "Algorithmic Foundations", "heading_level": null, "page_id": 0, "polygon": [[225.534912109375, 180.69289099526065], [395.09765625, 180.69289099526065], [395.09765625, 195.5291748046875], [225.534912109375, 195.5291748046875]]}, {"title": "3.1 The Kantorovich Linear Programs", "heading_level": null, "page_id": 1, "polygon": [[104.29253365973072, 199.43696682464454], [322.36376953125, 199.43696682464454], [322.36376953125, 210.49560546875], [104.29253365973072, 210.49560546875]]}, {"title": "3.1. The Kantorovich Linear Programs 39", "heading_level": null, "page_id": 2, "polygon": [[104.910400390625, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.7994384765625], [104.910400390625, 103.7994384765625]]}, {"title": "3.2 C-Transforms", "heading_level": null, "page_id": 3, "polygon": [[103.5422276621787, 124.460663507109], [209.33537331701345, 124.460663507109], [209.33537331701345, 135.8565673828125], [103.5422276621787, 135.8565673828125]]}, {"title": "3.3 Complementary Slackness", "heading_level": null, "page_id": 4, "polygon": [[104.29253365973072, 341.14218009478674], [278.36352509179926, 341.14218009478674], [278.36352509179926, 352.435302734375], [104.29253365973072, 352.435302734375]]}, {"title": "3.4 Vertices of the Transportation Polytope", "heading_level": null, "page_id": 5, "polygon": [[104.29253365973072, 326.1469194312796], [354.1444308445532, 326.1469194312796], [354.1444308445532, 337.5654296875], [104.29253365973072, 337.5654296875]]}, {"title": "3.4.1 Tree Structure of the Support of All Vertices of U(a, b)", "heading_level": null, "page_id": 5, "polygon": [[104.29253365973072, 508.339336492891], [420.92166462668297, 508.339336492891], [420.92166462668297, 519.47998046875], [104.29253365973072, 519.47998046875]]}, {"title": "3.4.2 The North-West Corner Rule", "heading_level": null, "page_id": 7, "polygon": [[104.29253365973072, 297.3974609375], [287.64306640625, 297.3974609375], [287.64306640625, 307.82568359375], [104.29253365973072, 307.82568359375]]}, {"title": "3.5 A Heuristic Description of the Network Simplex", "heading_level": null, "page_id": 8, "polygon": [[104.29253365973072, 397.3744075829384], [398.4124847001224, 397.3744075829384], [398.4124847001224, 408.6318359375], [104.29253365973072, 408.6318359375]]}, {"title": "3.5.1 Obtaining a Dual Pair Complementary to P", "heading_level": null, "page_id": 8, "polygon": [[104.29253365973072, 572.8189573459715], [360.1468788249694, 572.8189573459715], [360.1468788249694, 584.36669921875], [104.29253365973072, 584.36669921875]]}, {"title": "3.5.2 Network Simplex Update", "heading_level": null, "page_id": 10, "polygon": [[104.29253365973072, 287.74169921875], [267.85924112607097, 287.74169921875], [267.85924112607097, 297.78369140625], [104.29253365973072, 297.78369140625]]}, {"title": "3.5.3 Improvement of the Primal Solution", "heading_level": null, "page_id": 11, "polygon": [[103.5422276621787, 197.18767772511848], [322.6315789473684, 197.18767772511848], [322.6315789473684, 207.40576171875], [103.5422276621787, 207.40576171875]]}, {"title": "3.6 Dual Ascent Methods", "heading_level": null, "page_id": 12, "polygon": [[104.29253365973072, 409.37061611374406], [255.85434516523867, 409.37061611374406], [255.85434516523867, 420.21875], [104.29253365973072, 420.21875]]}, {"title": "3.7 Auction Algorithm", "heading_level": null, "page_id": 15, "polygon": [[104.29253365973072, 595.3118483412322], [236.34638922888615, 595.3118483412322], [236.34638922888615, 606.76806640625], [104.29253365973072, 606.76806640625]]}, {"title": "3.7. Auction Algorithm 53", "heading_level": null, "page_id": 16, "polygon": [[105.04283965728274, 92.97061611374407], [518.4614443084455, 92.97061611374407], [518.4614443084455, 104.089111328125], [105.04283965728274, 104.089111328125]]}, {"title": "3.7. Auction Algorithm 55", "heading_level": null, "page_id": 18, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 104.1856689453125], [105.04283965728274, 104.1856689453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 52], ["Line", 26], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4326, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 60], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 80], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["Line", 56], ["Equation", 7], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 804], ["Line", 82], ["TextInlineMath", 6], ["Equation", 4], ["ListItem", 3], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 624], ["Line", 35], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["Line", 74], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1724, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 817], ["Line", 92], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1453, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 623], ["Line", 48], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1522, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 772], ["Line", 60], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2368, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 627], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 753, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 570], ["Line", 59], ["TextInlineMath", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1786, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 571], ["Line", 99], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1174, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 894], ["Line", 39], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 641], ["Line", 56], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 533], ["Line", 102], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 612], ["Line", 91], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["ListItem", 4], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1154, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 813], ["Line", 131], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["Line", 82], ["TextInlineMath", 6], ["Equation", 5], ["Text", 4], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1060, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_41-60"}