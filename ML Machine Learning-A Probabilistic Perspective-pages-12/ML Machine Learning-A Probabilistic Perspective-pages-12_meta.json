{"table_of_contents": [{"title": "10 Directed graphical models (Bayes nets)", "heading_level": null, "page_id": 0, "polygon": [[63.5625, 91.125], [413.25, 91.125], [413.25, 145.705078125], [62.25, 145.705078125]]}, {"title": "10.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.75, 207.0], [195.75, 207.0], [195.75, 217.845703125], [96.75, 217.845703125]]}, {"title": "10.1.1 Chain rule", "heading_level": null, "page_id": 0, "polygon": [[91.40625, 409.5], [180.75, 409.5], [180.75, 419.23828125], [91.40625, 419.23828125]]}, {"title": "10.1.2 Conditional independence", "heading_level": null, "page_id": 1, "polygon": [[89.25, 208.5], [254.25, 208.5], [254.25, 218.478515625], [89.25, 218.478515625]]}, {"title": "10.1.3 Graphical models", "heading_level": null, "page_id": 1, "polygon": [[89.25, 444.75], [213.328125, 444.75], [213.328125, 454.67578125], [89.25, 454.67578125]]}, {"title": "10.1.4 Graph terminology", "heading_level": null, "page_id": 2, "polygon": [[89.4375, 290.25], [219.75, 290.25], [219.75, 300.5859375], [89.4375, 300.5859375]]}, {"title": "10.1.5 Directed graphical models", "heading_level": null, "page_id": 3, "polygon": [[90.0, 372.0], [255.75, 372.0], [255.75, 382.53515625], [90.0, 382.53515625]]}, {"title": "10.2 Examples", "heading_level": null, "page_id": 4, "polygon": [[94.5, 404.25], [180.75, 404.25], [180.75, 414.4921875], [94.5, 414.4921875]]}, {"title": "10.2.1 <PERSON><PERSON> classifiers", "heading_level": null, "page_id": 4, "polygon": [[89.25, 465.0], [235.6875, 465.0], [235.6875, 474.92578125], [89.25, 474.92578125]]}, {"title": "10.2.2 <PERSON><PERSON> and hidden <PERSON><PERSON> models", "heading_level": null, "page_id": 5, "polygon": [[86.9765625, 327.0], [297.0, 327.0], [297.0, 336.65625], [86.9765625, 336.65625]]}, {"title": "10.2.3 Medical diagnosis", "heading_level": null, "page_id": 6, "polygon": [[87.75, 249.75], [215.25, 249.75], [215.25, 259.927734375], [87.75, 259.927734375]]}, {"title": "10.2.4 Genetic linkage analysis *", "heading_level": null, "page_id": 8, "polygon": [[88.5, 527.25], [252.0, 527.25], [252.0, 537.57421875], [88.5, 537.57421875]]}, {"title": "10.2.5 Directed Gaussian graphical models *", "heading_level": null, "page_id": 11, "polygon": [[87.75, 291.75], [307.5, 291.75], [307.5, 301.693359375], [87.75, 301.693359375]]}, {"title": "10.3 Inference", "heading_level": null, "page_id": 12, "polygon": [[94.5, 339.0], [178.5, 339.0], [178.5, 350.26171875], [94.5, 350.26171875]]}, {"title": "10.4 Learning", "heading_level": null, "page_id": 13, "polygon": [[95.25, 290.25], [177.0, 291.0], [177.0, 301.693359375], [95.25, 301.693359375]]}, {"title": "10.4.1 Plate notation", "heading_level": null, "page_id": 13, "polygon": [[89.9296875, 559.5], [196.875, 559.5], [196.875, 569.53125], [89.9296875, 569.53125]]}, {"title": "10.4.2 Learning from complete data", "heading_level": null, "page_id": 15, "polygon": [[87.75, 252.75], [267.75, 252.75], [267.75, 262.93359375], [87.75, 262.93359375]]}, {"title": "10.4.3 Learning with missing and/or latent variables", "heading_level": null, "page_id": 16, "polygon": [[88.5, 535.5], [347.25, 535.5], [347.25, 545.25], [88.5, 545.25]]}, {"title": "10.5 Conditional independence properties of DGMs", "heading_level": null, "page_id": 17, "polygon": [[94.078125, 60.75], [369.75, 60.75], [369.75, 71.82421875], [94.078125, 71.82421875]]}, {"title": "10.5.1 d-separation and the <PERSON><PERSON> algorithm (global Markov properties)", "heading_level": null, "page_id": 17, "polygon": [[88.171875, 277.5], [456.75, 277.5], [456.75, 288.087890625], [88.171875, 288.087890625]]}, {"title": "10.5.2 Other Markov properties of DGMs", "heading_level": null, "page_id": 20, "polygon": [[87.75, 249.75], [290.25, 249.75], [290.25, 259.611328125], [87.75, 259.611328125]]}, {"title": "10.5.3 <PERSON><PERSON> blanket and full conditionals", "heading_level": null, "page_id": 20, "polygon": [[87.328125, 542.25], [305.25, 542.25], [305.25, 552.4453125], [87.328125, 552.4453125]]}, {"title": "10.6 Influence (decision) diagrams *", "heading_level": null, "page_id": 21, "polygon": [[94.5, 294.75], [291.75, 294.75], [291.75, 305.6484375], [94.5, 305.6484375]]}, {"title": "Exercises", "heading_level": null, "page_id": 25, "polygon": [[129.65625, 259.5], [178.875, 259.5], [178.875, 269.89453125], [129.65625, 269.89453125]]}, {"title": "Exercise 10.2 <PERSON><PERSON> Ball", "heading_level": null, "page_id": 25, "polygon": [[129.75, 343.5], [219.0, 343.5], [219.0, 352.79296875], [129.75, 352.79296875]]}, {"title": "Exercise 10.3 <PERSON><PERSON> blanket for a DGM", "heading_level": null, "page_id": 25, "polygon": [[129.75, 453.75], [278.25, 453.75], [278.25, 462.90234375], [129.75, 462.90234375]]}, {"title": "Exercise 10.4 Hidden variables in DGMs", "heading_level": null, "page_id": 25, "polygon": [[129.1640625, 534.75], [278.25, 534.75], [278.25, 544.21875], [129.1640625, 544.21875]]}, {"title": "Exercise 10.5 Bayes nets for a rainy day", "heading_level": null, "page_id": 26, "polygon": [[129.75, 351.75], [276.75, 351.75], [276.75, 361.01953125], [129.75, 361.01953125]]}, {"title": "Exercise 10.6 Fishing nets", "heading_level": null, "page_id": 26, "polygon": [[129.75, 539.25], [226.5, 539.25], [226.5, 548.96484375], [129.75, 548.96484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 29], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5093, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 42], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 36], ["ListItem", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 43], ["ListItem", 9], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 39], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1323, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["TableCell", 50], ["Line", 45], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2991, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 55], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1017, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["TableCell", 110], ["Line", 46], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2490, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 20], ["Figure", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 739], ["Line", 55], ["TextInlineMath", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["Text", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 437], ["Line", 42], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 501], ["Line", 73], ["Equation", 7], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1210, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 45], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 54], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["TableCell", 120], ["Line", 48], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5928, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 39], ["TextInlineMath", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Equation", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 31], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 824, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 38], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 38], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 645, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 42], ["TableCell", 25], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Table", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1085, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 33], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 704, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 41], ["TableCell", 16], ["Equation", 6], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4181, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 41], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 774, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 45], ["Text", 9], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 40], ["ListItem", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1890, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 57], ["Text", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 38], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-12"}