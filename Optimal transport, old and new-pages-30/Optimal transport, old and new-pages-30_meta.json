{"table_of_contents": [{"title": "Volume control", "heading_level": null, "page_id": 0, "polygon": [[133.5, 98.033203125], [243.75, 98.033203125], [243.75, 111.375], [133.5, 111.375]]}, {"title": "508 18 Volume control", "heading_level": null, "page_id": 1, "polygon": [[133.1279296875, 26.224365234375], [243.6943359375, 26.224365234375], [243.6943359375, 35.167236328125], [133.1279296875, 35.167236328125]]}, {"title": "Distorted <PERSON><PERSON> inequality", "heading_level": null, "page_id": 2, "polygon": [[133.5, 138.0], [364.5703125, 138.0], [364.5703125, 149.2734375], [133.5, 149.2734375]]}, {"title": "510 18 Volume control", "heading_level": null, "page_id": 3, "polygon": [[133.1279296875, 25.934326171875], [243.3955078125, 25.934326171875], [243.3955078125, 35.795654296875], [133.1279296875, 35.795654296875]]}, {"title": "512 18 Volume control", "heading_level": null, "page_id": 5, "polygon": [[133.5, 25.982666015625], [243.6943359375, 25.982666015625], [243.6943359375, 35.408935546875], [133.5, 35.408935546875]]}, {"title": "<PERSON><PERSON> inequality", "heading_level": null, "page_id": 5, "polygon": [[133.5, 415.5], [292.5, 415.5], [292.5, 426.9375], [133.5, 426.9375]]}, {"title": "Doubling property", "heading_level": null, "page_id": 8, "polygon": [[133.5, 472.5], [245.25, 472.5], [245.25, 484.171875], [133.5, 484.171875]]}, {"title": "Dimension-free bounds", "heading_level": null, "page_id": 9, "polygon": [[133.5, 333.75], [271.5, 333.75], [271.5, 344.759765625], [133.5, 344.759765625]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 10, "polygon": [[233.25, 495.75], [359.7890625, 495.75], [359.7890625, 507.375], [233.25, 507.375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 28], ["Text", 4], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2364, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 29], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 560, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 573], ["Line", 71], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1531, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 62], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 806], ["Line", 100], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3126, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 426], ["Line", 86], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1499, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 81], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2939, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 596], ["Line", 107], ["TextInlineMath", 7], ["Equation", 6], ["Text", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3030, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 603], ["Line", 77], ["TextInlineMath", 6], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 54], ["Equation", 5], ["TextInlineMath", 4], ["ListItem", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 88], ["TextInlineMath", 5], ["Equation", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 39], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-30"}