Controlling the volume of balls is a universal problem in geometry. This means of course controlling the volume from above when the radius increases to infinity; but also controlling the volume from below when the radius decreases to 0. The doubling property is useful in both situations.

**Definition 18.1 (Doubling property).** Let  $(\mathcal{X}, d)$  be a metric space, and let  $\mu$  be a Borel measure on X, not identically 0. The measure  $\mu$  is said to be doubling if there exists a constant  $D$  such that

$$
\forall x \in \mathcal{X}, \quad \forall r > 0, \quad \nu[B_{2r}](x) \le D \nu[B_{r}](x). \tag{18.1}
$$

The measure  $\mu$  is said to be locally doubling if for any fixed closed ball  $B[z, R] \subset \mathcal{X}$ , there is a constant  $D = D(z, R)$  such that

$$
\forall x \in B[z, R], \quad \forall r \in (0, R), \quad \nu[B_{2r}](x)] \le D \nu[B_r](x).
$$
 (18.2)

**Remark 18.2.** It is equivalent to say that a measure  $\nu$  is locally doubling, or that its restriction to any ball  $B[z, R]$  (considered as a metric space) is doubling.

Remark 18.3. It does not really matter whether the definition is formulated in terms of open or in terms of closed balls; at worst this changes the value of the constant D.

When the distance d and the reference measure  $\nu$  are clear from the context, I shall often say that the space  $\mathcal X$  is doubling (resp. locally doubling), instead of writing that the measure  $\nu$  is doubling on the metric space  $(\mathcal{X}, d)$ .

It is a standard fact in Riemannian geometry that doubling constants may be estimated, at least locally, in terms of curvature-dimension bounds. These estimates express the fact that the manifold does not contain sharp spines (as in Figure 18.1). Of course, it is obvious that a Riemannian manifold has this property, since it is locally diffeomorphic to an open subset of  $\mathbb{R}^n$ ; but curvature-dimension bounds quantify this in terms of the intrinsic geometry, without reference to charts.

Image /page/1/Figure/2 description: A simple black and white line drawing of a teardrop shape. The top of the teardrop comes to a sharp point, and the bottom is rounded. The line is thick and smooth.

Fig. 18.1. The natural volume measure on this "singular surface" (a balloon with a spine) is not doubling.

Another property which is obvious for a Riemannian manifold, but which doubling makes quantitative, is the fact that the reference measure has full support:

Proposition 18.4 (Doubling measures have full support). Let  $(X, d)$  be a metric space equipped with a locally doubling measure  $\nu$ . Then  $\text{Spt } \nu = \mathcal{X}$ .

*Proof.* Let  $x \in \mathcal{X}$ , and let  $r > 0$ . Since  $\nu$  is nonzero, there is  $R > 0$  such that  $\nu[B_{R]}(x) > 0$ . Then there is a constant C, possibly depending on x and R, such that  $\nu$  is C-doubling inside  $B_{R}](x)$ . Let  $n \in \mathbb{N}$  be large enough that  $R \leq 2^n r$ ; then

$$
0 < \nu[B_{R]}(x)] \leq C^n \nu[B_r](x)].
$$

So  $\nu[B_r](x) > 0$ . Since r is arbitrarily small, x has to lie in the support of  $\nu$ .  $□$ 

One of the goals of this chapter is to get doubling constants from curvature-dimension bounds, by means of arguments based on optimal transport. This is not the standard strategy, but it will work just as well as any other, since the results in the end will be optimal. As a preliminary step, I shall establish a "distorted" version of the famous Brunn–Minkowski inequality.

# Distorted Brunn Minkowski inequality

The classical Brunn–Minkowski inequality states that whenever  $A_0$  and  $A_1$  are two nonempty compact subsets of  $\mathbb{R}^n$ , then

$$
|A_0 + A_1|^{\frac{1}{n}} \ge |A_0|^{\frac{1}{n}} + |A_1|^{\frac{1}{n}},\tag{18.3}
$$

where  $|\cdot|$  stands for Lebesgue measure, and  $A_0 + A_1$  is the set of all vectors of the form  $a_0 + a_1$  with  $a_0 \in A_0$  and  $a_1 \in A_1$ . This inequality contains the Euclidean isoperimetric inequality as a limit case (take  $A_1 = \varepsilon B(0,1)$  and let  $\varepsilon \to 0$ ).

It is not easy to guess the "correct" generalization of (18.3) to general Riemannian manifolds, and it is only a few years ago that a plausible answer to that problem emerged, in terms of the distortion coefficients (14.61).

In the sequel, I shall use the following notation: if  $A_0$  and  $A_1$  are two nonempty compact subsets of a Riemannian manifold M, then  $[A_0, A_1]_t$  stands for the set of all *t*-barycenters of  $A_0$  and  $A_1$ , which is the set of all  $y \in M$  that can be written as  $\gamma_t$ , where  $\gamma$  is a minimizing, constant-speed geodesic with  $\gamma_0 \in A_0$  and  $\gamma_1 \in A_1$ . Equivalently,  $[A_0, A_1]_t$  is the set of all y such that there exists  $(x_0, x_1) \in A_0 \times A_1$  with  $d(x_0, y)/d(y, x_1) = t/(1-t)$ . In  $\mathbb{R}^n$ , of course  $[A_0, A_1]_t = (1-t) A_0 + t A_1$ .

Theorem 18.5 (Distorted Brunn–Minkowski inequality). Let M be a (complete) Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a CD(K,N) curvaturedimension condition. Let  $A_0$ ,  $A_1$  be two nonempty compact subsets, and let  $t \in (0,1)$ . Then:

• If  $N < \infty$ ,

$$
\nu\left[[A_0, A_1]_t\right]^{\frac{1}{N}} \ge (1-t) \left[\inf_{(x_0, x_1) \in A_0 \times A_1} \beta_{1-t}^{(K,N)} (x_0, x_1)^{\frac{1}{N}}\right] \nu[A_0]^{\frac{1}{N}}
$$
$$
+ t \left[\inf_{(x_0, x_1) \in A_0 \times A_1} \beta_t^{(K,N)} (x_0, x_1)^{\frac{1}{N}}\right] \nu[A_1]^{\frac{1}{N}}, \quad (18.4)
$$

where  $\beta_t^{(K,N)}$  $t_t^{(K,N)}(x_0, x_1)$  are the distortion coefficients defined in (14.61).

• If  $N = \infty$ ,

$$
\begin{aligned}\log \frac{1}{\nu \left[ [A_0, A_1]_t \right]} &\le (1 - t) \log \frac{1}{\nu [A_0]} + t \log \frac{1}{\nu [A_1]} \\ & \quad - \frac{Kt(1 - t)}{2} \sup_{x_0 \in A_0, x_1 \in A_1} d(x_0, x_1)^2 \cdot (18.5)\end{aligned}
$$

By particularizing Theorem 18.5 to the case when  $K = 0$  and  $N < \infty$  (so  $\beta_t^{(K,N)} = 1$ ), one can show that nonnegatively curved Riemannian manifolds satisfy a Brunn–Minkowski inequality which is similar to the Brunn–Minkowski inequality in  $\mathbb{R}^n$ :

Corollary 18.6 (Brunn–Minkowski inequality in nonnegative curvature). With the same notation as in Theorem 18.5, if M satisfies the curvature-dimension condition  $CD(0, N)$ ,  $N \in (1, +\infty)$ , then

$$
\nu\big[[A_0,A_1]_t\big]^{\frac{1}{N}} \ge (1-t)\,\nu[A_0]^{\frac{1}{N}} + t\,\nu[A_1]^{\frac{1}{N}}.\tag{18.6}
$$

**Remark 18.7.** When  $M = \mathbb{R}^n$ ,  $N = n$ , inequality (18.6) reduces to

$$
\left| (1-t)A_0 + tA_1 \right|^{\frac{1}{n}} \ge (1-t) |A_0|^{\frac{1}{n}} + t |A_1|^{\frac{1}{n}},
$$

where  $|\cdot|$  stands for the *n*-dimensional Lebesgue measure. By homogeneity, this is equivalent to (18.4).

Idea of the proof of Theorem 18.5. Introduce an optimal coupling between a random point  $\gamma_0$  chosen uniformly in  $A_0$  and a random point  $\gamma_1$  chosen uniformly in  $A_1$  (as in the proof of isoperimetry in Chapter 2). Then  $\gamma_t$  is a random point (not necessarily uniform) in  $A_t$ . If  $A_t$  is very small, then the law  $\mu_t$  of  $\gamma_t$  will be very concentrated, so its density will be very high, but then this will contradict the displacement convexity estimates implied by the curvature assumptions. For instance, consider for simplicity  $U(r) = r^m$ ,  $m \ge 1$ ,  $K = 0$ : Since  $U_{\nu}(\mu_0)$  and  $U_{\nu}(\mu_1)$  are finite, this implies a bound on  $U_{\nu}(\mu_t)$ , and this bound cannot hold if the support of  $\mu_t$  is too small (in the extreme case where  $A_t$  is a single point,  $\mu_t$  will be a Dirac, so  $U_\nu(\mu_t) = +\infty$ ). Thus, the support of  $\mu_t$ has to be large enough. It turns out that the optimal estimates are obtained with  $U = U_N$ , as defined in (16.17). □

Detailed proof of Theorem 18.5. First consider the case  $N < \infty$ . For brevity I shall write just  $\beta_t$  instead of  $\beta_t^{(K,N)}$  $t_t^{(K,N)}$ . By regularity of the measure  $\nu$  and an easy approximation argument, it is sufficient to treat the case when  $\nu[A_0] > 0$  and  $\nu[A_1] > 0$ . Then one may define  $\mu_0 = \rho_0 \nu$ ,  $\mu_1 = \rho_1 \nu$ , where

$$
\rho_0 = \frac{1_{A_0}}{\nu[A_0]}, \qquad \rho_1 = \frac{1_{A_1}}{\nu[A_1]}.
$$

In words,  $\mu_{t_0}$  ( $t_0 \in \{0,1\}$ ) is the law of a random point distributed uniformly in  $A_{t_0}$ . Let  $(\mu_t)_{0 \leq t \leq 1}$  be the unique displacement interpolation between  $\mu_0$  and  $\mu_1$ , for the cost function  $d(x, y)^2$ . Since M satisfies the curvature-dimension bound  $CD(K, N)$ , Theorem 17.37, applied with  $U(r) = U_N(r) = -N(r^{1-\frac{1}{N}} - r)$ , implies

$$
\int_{M} U_{N}(\rho_{t}(x)) \nu(dx) \\ \leq (1-t) \int_{M} U_{N} \left( \frac{\rho_{0}(x_{0})}{\beta_{1-t}(x_{0},x_{1})} \right) \beta_{1-t}(x_{0},x_{1}) \pi(dx_{1}|x_{0}) \nu(dx_{0}) \\ + t \int_{M} U_{N} \left( \frac{\rho_{1}(x_{1})}{\beta_{t}(x_{0},x_{1})} \right) \beta_{t}(x_{0},x_{1}) \pi(dx_{0}|x_{1}) \nu(dx_{1}) \\ = (1-t) \int_{M} U_{N} \left( \frac{\rho_{0}(x_{0})}{\beta_{1-t}(x_{0},x_{1})} \right) \frac{\beta_{1-t}(x_{0},x_{1})}{\rho_{0}(x_{0})} \pi(dx_{0} dx_{1}) \\ + t \int_{M} U_{N} \left( \frac{\rho_{1}(x_{1})}{\beta_{t}(x_{0},x_{1})} \right) \frac{\beta_{t}(x_{0},x_{1})}{\rho_{1}(x_{1})} \pi(dx_{0} dx_{1}),
$$

where  $\pi$  is the optimal coupling of  $(\mu_0, \mu_1)$ , and  $\beta_t$  is a shorthand for  $\beta_t^{(K,N)}$  $t_t^{(K,N)}$ ; the equality comes from the fact that, say,  $\pi(dx_0 dx_1) =$  $\mu(dx_0) \pi(dx_1|x_0) = \rho(x_0) \nu(dx_0) \pi(dx_1|x_0)$ . After replacement of  $U_N$ by its explicit expression and simplification, this leads to

$$
\int_M \rho_t(x)^{1-\frac{1}{N}} \nu(dx) \ge (1-t) \int_M \rho_0(x)^{-\frac{1}{N}} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} \pi(dx_0 dx_1) + t \int_M \rho_1(x)^{-\frac{1}{N}} \beta_t(x_0, x_1)^{\frac{1}{N}} \pi(dx_0 dx_1).
$$
 (18.7)

Since  $\pi$  is supported in  $A_0 \times A_1$  and has marginals  $\rho_0 \nu$  and  $\rho_1 \nu$ , one can bound the right-hand side of (18.7) below by

$$
(1-t)\,\beta_{1-t}^{\frac{1}{N}}\int_M \rho_0(x_0)^{1-\frac{1}{N}}\,d\nu(x_0)\,\,+\,\,t\,\beta_t^{\frac{1}{N}}\int_M \rho_1(x_1)^{1-\frac{1}{N}}\,d\nu(x_1),
$$

where  $\beta_t$  stands for the minimum of  $\beta_t(x_0, x_1)$  over all pairs  $(x_0, x_1) \in$  $A_0 \times A_1$ . Then, by explicit computation,

$$
\int_M \rho_0(x_0)^{1-\frac{1}{N}} d\nu(x_0) = \nu[A_0]^{\frac{1}{N}}, \qquad \int_M \rho_1(x_1)^{1-\frac{1}{N}} d\nu(x_1) = \nu[A_1]^{\frac{1}{N}}.
$$

So to conclude the proof of (18.4) it sufficient to show

$$
\int_M \rho_t^{1-\frac{1}{N}} \, d\nu \le \nu \big[ [A_0, A_1]_t \big]^{\frac{1}{N}}.
$$

Obviously,  $\mu_t$  is supported in  $A_t = [A_0, A_1]_t$ ; therefore  $\rho_t$  is a probability density on that set. By Jensen's inequality,

$$
\int_{A_t} \rho_t^{1-\frac{1}{N}} d\nu = \nu[A_t] \int_{A_t} \rho_t^{1-\frac{1}{N}} \frac{d\nu}{\nu[A_t]}
$$
  
$$
\leq \nu[A_t] \left( \int_{A_t} \rho_t \frac{d\nu}{\nu[A_t]} \right)^{1-\frac{1}{N}}
$$
  
$$
= \nu[A_t]^{\frac{1}{N}} \left( \int_{A_t} \rho_t d\nu \right)^{1-\frac{1}{N}} = \nu[A_t]^{\frac{1}{N}}.
$$

This concludes the proof of (18.4).

The proof in the case  $N = \infty$  is along the same lines, except that now it is based on the K-displacement convexity of  $H_{\nu}$  and the convexity of  $r \mapsto r \log r$ . □

# Bishop Gromov inequality

The Bishop–Gromov inequality states that the volume of balls in a space satisfying  $CD(K, N)$  does not grow faster than the volume of balls in the model space of constant sectional curvature having Ricci curvature equal to K and dimension equal to N. In the case  $K = 0$ , it takes the following simple form:

$$
\frac{\nu[B_r(x)]}{r^N}
$$
 is a nonincreasing function of r.

(It does not matter whether one considers the closed or the open ball of radius r.) In the case  $K > 0$  (resp.  $K < 0$ ), the quantity on the left-hand side should be replaced by

.

$$
\frac{\nu[B_r(x)]}{\int_0^r \left(\sin\sqrt{\frac{K}{N-1}}t\right)^{N-1} dt},
$$
  
resp. 
$$
\frac{\nu[B_r(x)]}{\int_0^r \left(\sinh\sqrt{\frac{|K|}{N-1}}t\right)^{N-1} dt}
$$

Here is a precise statement:

Theorem 18.8 (Bishop–Gromov inequality). Let  $M$  be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol, satisfying a curvature-dimension condition  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $1 < N < \infty$ . Further, let

$$
s^{(K,N)}(t) = \begin{cases} \left(\sin\sqrt{\frac{K}{N-1}}t\right)^{N-1} & \text{if } K > 0 \\ t^{N-1} & \text{if } K = 0 \\ \left(\sinh\sqrt{\frac{|K|}{N-1}}t\right)^{N-1} & \text{if } K < 0 \end{cases}
$$

Then, for any  $x \in M$ ,

$$
\frac{\nu[B_r(x)]}{\int_0^r s^{(K,N)}(t)\,dt}
$$

is a nonincreasing function of r.

*Proof of Theorem 18.8.* Let us start with the case  $K = 0$ , which is simpler. Let  $A_0 = \{x\}$  and  $A_1 = B_{r}(x)$ ; in particular,  $\nu[A_0] = 0$ . For any  $s \in (0, r)$ , one has  $[A_0, A_1]_{\frac{s}{r}} \subset B_{s}(x)$ , so by the Brunn–Minkowski inequality (18.6),

$$
\nu[B_s](x)]^{\frac{1}{N}} \geq \nu\big[[A_0,A_1]_{\frac{s}{r}}\big]^{\frac{1}{N}} \geq \left(\frac{s}{r}\right) \nu[B_r](x)]^{\frac{1}{N}},
$$

and the conclusion follows immediately.

Now let us consider the general case. By Lemma 18.9 below, it will be sufficient to check that

$$
\frac{\frac{d^+}{dr}\nu[B_r]}{s^{(K,N)}(r)}
$$
 is nonincreasing, (18.8)

where  $B_r = B_{r}(x)$ .

Apply Theorem 18.5 with  $A_0 = \{x\}$  again, but now  $A_1 = B_{r+\varepsilon} \setminus B_r$ ; then for  $t \in (0,1)$  one has  $[A_0, A_1]_t \subset B_{t(r+\varepsilon)} \setminus B_t$ . Moreover, for  $K \geq 0$ , one has

$$
\beta_t^{(K,N)}(x_0, x_1) \geq \left(\frac{\sin\left(t\sqrt{\frac{K}{N-1}}(r+\varepsilon)\right)}{t \sin\left(\sqrt{\frac{K}{N-1}}(r+\varepsilon)\right)}\right)^{N-1};
$$

for  $K < 0$  the same formula remains true with sin replaced by sinh, K by |K| and  $r+\varepsilon$  by  $r-\varepsilon$ . In the sequel, I shall only consider  $K > 0$ , the treatment of  $K < 0$  being obviously similar. After applying the above bounds, inequality (18.4) yields

$$
\nu\Big[B_{t(r+\varepsilon)}\setminus B_{tr}\Big]^{\frac{1}{N}}\geq t\left(\frac{\sin\Bigl(t\,\sqrt{\frac{K}{N-1}}\,(r+\varepsilon)\Bigr)}{t\,\sin\Bigl(\sqrt{\frac{K}{N-1}}\,(r+\varepsilon)\Bigr)}\right)^{\frac{N-1}{N}}\nu\Big[B_{r+\varepsilon}\setminus B_{r}\Big]^{\frac{1}{N}};
$$

or, what is the same,

$$
\frac{\nu[B_{t(r+\varepsilon)}\setminus B_{tr}]}{\left(\sin\left(\sqrt{\frac{K}{N-1}}t(r+\varepsilon)\right)\right)^{N-1}} \geq t \frac{\nu[B_{r+\varepsilon}\setminus B_r]}{\left(\sin\left(\sqrt{\frac{K}{N-1}}(r+\varepsilon)\right)\right)^{N-1}}.
$$

If  $\phi(r)$  stands for  $\nu[B_r]$ , then the above inequality can be rewritten as

$$
\frac{\phi(tr + t\varepsilon) - \phi(tr)}{\varepsilon s^{(K,N)}(t(r + \varepsilon))} \geq \frac{\phi(r + \varepsilon) - \phi(r)}{\varepsilon s^{(K,N)}(r + \varepsilon)}.
$$

In the limit  $\varepsilon \to 0$ , this yields

$$
\frac{\phi'(tr)}{s^{(K,N)}(tr)} \ge \frac{\phi'(r)}{s^{(K,N)}(r)}.
$$

This was for any  $t \in [0,1]$ , so  $\phi'/s^{(K,N)}$  is indeed nonincreasing, and the proof is complete. ⊓⊔

The following lemma was used in the proof of Theorem 18.8. At first sight it seems obvious and the reader may skip its proof.

**Lemma 18.9.** Let  $a < b$  in  $\mathbb{R} \cup \{+\infty\}$ , let  $g : (a, b) \to \mathbb{R}_+$  be a positive continuous function, integrable at a, and let  $G(r) = \int_a^r g(s) ds$ . Let  $F : [a, b) \to \mathbb{R}_+$  be a nondecreasing measurable function satisfying  $F(a) = 0$ , and let  $f(r) = d^+F/dr$  be its upper derivative. If  $f/g$  is nonincreasing then also  $F/G$  is nonincreasing.

*Proof of Lemma 18.9.* Let  $h = f/g$ ; by assumption, h is nonincreasing. In particular, for any  $x \ge x_0 > a$ ,  $f(x) \le g(x) h(x_0)$  is locally bounded, so F is locally Lipschitz, and  $F(y) - F(x) = \int_x^y f(t) dt$  as soon as  $y > x > a$ . Taking the limit  $x \to a$  shows that  $F(y) = \int_a^y f(t) dt$ . So the problem is to show that

$$
x \le y \Longrightarrow \qquad \frac{\int_a^x f(t) \, dt}{\int_a^x g(t) \, dt} \le \frac{\int_a^y f(t) \, dt}{\int_a^y g(t) \, dt}.\tag{18.9}
$$

If  $a \le t \le x \le t' \le y$ , then  $h(t) \le h(t')$ ; so

$$
\int_{a}^{x} f \int_{x}^{y} g = \int_{a}^{x} gh \int_{x}^{y} g \le \int_{a}^{x} g \int_{x}^{y} gh = \int_{a}^{x} g \int_{x}^{y} f.
$$

This implies

$$
\frac{\int_a^x f}{\int_a^x g} \le \frac{\int_x^y f}{\int_x^y g},
$$

and (18.9) follows.  $□$ 

Exercise 18.10. Give an alternative proof of the Bishop–Gromov inequality for  $CD(0, N)$  Riemannian manifolds, using the convexity of  $t \longmapsto t U_{\nu}(\mu_t) + N t \log t$ , for  $U \in \mathcal{DC}_N$ , when  $(\mu_t)_{0 \le t \le 1}$  is a displacement interpolation.

# Doubling property

From Theorem 18.8 and elementary estimates on the function  $s^{(K,N)}$ it is easy to deduce the following corollary:

Corollary 18.11 ( $CD(K, N)$  implies doubling). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V} vol$ , satisfying a curvature-dimension condition  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $1 < N < \infty$ . Then v is doubling with a constant C that is:

- uniform and no more than  $2^N$  if  $K \geq 0$ ;
- locally uniform and no more than  $2^N D(K, N, R)$  if  $K < 0$ , where

$$
D(K, N, R) = \left[ \cosh \left( 2\sqrt{\frac{|K|}{N-1}} R \right) \right]^{N-1}, \quad (18.10)
$$

when restricted to a large ball  $B[z, R]$ .

The Bishop–Gromov inequality is however more precise than just doubling property: for instance, if  $0 \lt s \lt r$  then, with the same notation as before,

$$
\nu[B_r(x)] \ge \nu[B_s(x)] \ge \left(\frac{V(s)}{V(r)}\right) \nu[B_r(x)],
$$

where  $V(r)$  is the volume of  $B_r(x)$  in the model space. This implies that  $\nu[B_r(x)]$  is a continuous function of r. Of course, this property is otherwise obvious, but the Bishop–Gromov inequality provides an explicit modulus of continuity.

## Dimension-free bounds

There does not seem to be any "natural" analog of the Bishop–Gromov inequality when  $N = \infty$ . However, we have the following useful estimates.

Theorem 18.12 (Dimension-free control on the growth of balls). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a  $CD(K, \infty)$  condition for some  $K \in \mathbb{R}$ . Then, for any  $\delta > 0$ , there exists a constant  $C = C(K_-, \delta, \nu[B_\delta(x_0)], \nu[B_{2\delta}(x_0)])$ , such that for all  $r \ge \delta$ ,

$$
\nu[B_r(x_0)] \le e^{Cr} \, e^{(K_-)\frac{r^2}{2}}; \tag{18.11}
$$

$$
\nu[B_{r+\delta}(x_0) \setminus B_r(x_0)] \le e^{Cr} \, e^{-K\frac{r^2}{2}} \qquad \text{if } K > 0. \tag{18.12}
$$

In particular, if  $K' < K$  then

$$
\int e^{\frac{K'}{2}d(x_0,x)^2} \nu(dx) < +\infty.
$$
 (18.13)

*Proof of Theorem 18.12.* For brevity I shall write  $B_r$  for  $B_r(x_0)$ . Apply (18.5) with  $A_0 = B_\delta$ ,  $A_1 = B_r$ , and  $t = \delta/(2r) \le 1/2$ . For any minimizing geodesic  $\gamma$  going from  $A_0$  to  $A_1$ , one has  $d(\gamma_0, \gamma_1) \leq r + \delta$ , so

$$
d(x_0, \gamma_t) \le d(x_0, \gamma_0) + d(\gamma_0, \gamma_t) \le \delta + t(r + \delta) \le \delta + 2tr \le 2\delta.
$$

So  $[A_0, A_1]_t \subset B_{2\delta}$ , and by  $(18.5)$ ,

$$
\log \frac{1}{\nu[B_{2\delta}]} \le \left(1 - \frac{\delta}{2r}\right) \log \frac{1}{\nu[B_{\delta}]} + \frac{\delta}{2r} \log \frac{1}{\nu[B_r]} + \frac{K_-}{2} \frac{\delta}{2r} \left(1 - \frac{\delta}{2r}\right) (r + \delta)^2.
$$

This implies an estimate of the form

$$
\nu[B_r] \le \exp\left(a + br + \frac{c}{r} + \frac{K_r r^2}{2}\right),\,
$$

where a, b, c only depend on  $\delta$ ,  $\nu[B_\delta]$  and  $\nu[B_{2\delta}]$ . Inequality (18.11) follows.

The proof of inequality (18.12) is just the same, but with  $A_0 = B_\delta$ ,  $A_1 = B_{r+\delta} \setminus B_r$ ,  $t = \delta/(3r)$ .

To prove (18.13) in the case  $K > 0$ , it suffices to take  $\delta = 1$  and write

$$
\int_{\mathcal{X}} e^{\frac{K'}{2} d(x_0, x)^2} \nu(dx) \le e^{\frac{K'}{2}} \nu[B_1] + \sum_{k \ge 1} e^{\frac{K'}{2} (k+1)^2} \nu[B_{k+1} \setminus B_k]
$$
  
$$
\le e^{\frac{K'}{2}} \nu[B_1] + C \sum_{k \ge 1} e^{C(k+1)} e^{\frac{K'}{2} (k+1)^2} e^{-Kk^2}
$$
  
$$
< \infty.
$$

The case  $K \leq 0$  is treated similarly. □

[]

# Bibliographical notes

The Brunn–Minkowski inequality in  $\mathbb{R}^n$  goes back to the end of the nineteenth century; it was first established by Brunn (for convex sets in dimension 2 or 3), and later generalized by Minkowski (for convex sets in arbitrary dimension) and Lusternik [581] (for arbitrary compact sets). Nowadays, it is still one of the cornerstones of the geometry of convex bodies. Standard references on the Brunn–Minkowski theory are the book by Schneider [741] and the more recent survey paper by Gardner [406]; one can also consult the lecture by Maurey [608], the recent research review by Barthe [71], and his broad-audience introductory text [72].

It is classical to prove the Brunn–Minkowski inequality (in  $\mathbb{R}^n$ ) via changes of variables, usually called reparametrizations in this context. McCann [612] noticed that optimal transport does yield a convenient reparametrization; this is a bit more complicated than the reparametrizations classically used in  $\mathbb{R}^n$ , but it has the advantage of begin defined in more intrinsic terms. McCann's argument is reproduced in [814, Section 6.1]; it is basically the same as the proof of Theorem 18.5, only much simpler because it is in Euclidean space.

At the end of the nineties, it was still not clear what would be the correct extension of that theory to curved spaces. The first hint came when Cordero-Erausquin [241] used the formalism of optimal transport to guess a Prékopa–Leindler inequality on the sphere. In Euclidean space, the Prékopa–Leindler inequality is a well-known functional version of the Brunn–Minkowski inequality (it is discussed for instance in the above-mentioned surveys, and we shall meet it in the next chapter). Cordero-Erausquin, McCann and Schmuckenschläger [246] developed the tools necessary to make this approach rigorous, and also established Prékopa–Leindler inequalities in curved geometry (when the reference measure is the volume). Then Sturm [763] adapted the proof of [246] to get Brunn–Minkowski inequalities for general reference measures. Ohta [657] further generalized these results to Finsler geometries.

The proof of the Bishop–Gromov inequality in the case  $K = 0$  is taken from [577]. Apart from that, my presentation in this chapter is strongly inspired by Sturm [763]. In particular, it is from that source that I took the statement of Theorem 18.5 and the proof of the Bishop– Gromov inequality for  $K \neq 0$ .

Exercice 18.10 was inspired from an exchange with Lott. The convexity property mentioned in the exercise is proven in [576].

More classical proofs of the Bishop–Gromov inequality can be found in reference textbooks, e.g. [394, Theorem 4.19]. The resulting comparison inequality between the volume of balls in a  $CD(K, N)$  Riemannian manifold and in the comparison space is called just the Bishop inequality [394, Theorem 3.101(i)]. Also available is a reversed comparison principle for upper bounds on the sectional curvature [394, Theorem 3.101(ii)], due to Gunther.

Lemma 18.9 is a slight variation of [223, Lemma 3.1]; it is apparently due to Gromov [635]. This lemma can also be proven by approximation from its discrete version.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.