# 376 10. Boosting and Additive Trees

presence and abundance of the Black Oreo Dory, a marine fish found in the oceanic waters around New Zealand.<sup>3</sup>

Figure 10.18 shows the locations of 17,000 trawls (deep-water net fishing, with a maximum depth of 2km), and the red points indicate those 2353 trawls for which the Black Oreo was present, one of over a hundred species regularly recorded. The catch size in kg for each species was recorded for each trawl. Along with the species catch, a number of environmental measurements are available for each trawl. These include the average depth of the trawl (AvgDepth), and the temperature and salinity of the water. Since the latter two are strongly correlated with depth, <PERSON><PERSON><PERSON> et al. (2006) derived instead TempResid and SalResid, the residuals obtained when these two measures are adjusted for depth (via separate non-parametric regressions). SSTGrad is a measure of the gradient of the sea surface temperature, and Chla is a broad indicator of ecosytem productivity via satellite-image measurements. SusPartMatter provides a measure of suspended particulate matter, particularly in coastal waters, and is also satellite derived.

The goal of this analysis is to estimate the probability of finding Black Oreo in a trawl, as well as the expected catch size, standardized to take into account the effects of variation in trawl speed and distance, as well as the mesh size of the trawl net. The authors used logistic regression for estimating the probability. For the catch size, it might seem natural to assume a Poisson distribution and model the log of the mean count, but this is often not appropriate because of the excessive number of zeros. Although specialized approaches have been developed, such as the zero*inflated* Poisson (Lambert, 1992), they chose a simpler approach. If  $Y$  is the (non-negative) catch size,

$$
E(Y|X) = E(Y|Y > 0, X) \cdot Pr(Y > 0|X).
$$
 (10.54)

The second term is estimated by the logistic regression, and the first term can be estimated using only the 2353 trawls with a positive catch.

For the logistic regression the authors used a gradient boosted model  $(GBM)<sup>4</sup>$  with binomial deviance loss function, depth-10 trees, and a shrinkage factor  $\nu = 0.025$ . For the positive-catch regression, they modeled  $log(Y)$  using a GBM with squared-error loss (also depth-10 trees, but  $\nu = 0.01$ , and un-logged the predictions. In both cases they used 10-fold cross-validation for selecting the number of terms, as well as the shrinkage factor.

<sup>3</sup>The models, data, and maps shown here were kindly provided by Dr John Leathwick of the National Institute of Water and Atmospheric Research in New Zealand, and Dr Jane Elith, School of Botany, University of Melbourne. The collection of the research trawl data took place from 1979–2005, and was funded by the New Zealand Ministry of Fisheries.

 $4$ Version 1.5-7 of package gbm in R, ver. 2.2.0.

Image /page/1/Figure/1 description: This map displays research trawl locations around New Zealand, differentiating between sites where a species was present (red dots) and absent (blue dots). The map also outlines the Exclusive Economic Zone of New Zealand. Latitude lines are marked from 30 S to 55 S, and longitude lines from 165 E to 170 W. A scale bar indicates 0 to 200 kilometers.

FIGURE 10.18. Map of New Zealand and its surrounding exclusive economic zone, showing the locations of 17,000 trawls (small blue dots) taken between 1979 and 2005. The red points indicate trawls for which the species Black Oreo Dory were present.

Image /page/2/Figure/1 description: The image displays two plots. The left plot is a line graph showing Mean Deviance on the y-axis and Number of Trees on the x-axis. It plots three lines: GBM Test (green), GBM CV (red), and GAM Test (orange). The GBM Test and GBM CV lines decrease as the number of trees increases, with GBM CV showing slightly higher deviance. The GAM Test line is a horizontal orange line at approximately 0.275. The right plot is a ROC curve showing Sensitivity on the y-axis and Specificity on the x-axis. It plots two sets of points: GAM (orange) with an AUC of 0.97 and GBM (green) with an AUC of 0.98. Both curves rise steeply and then plateau at the top left corner, indicating good performance.

FIGURE 10.19. The left panel shows the mean deviance as a function of the number of trees for the GBM logistic regression model fit to the presence/absence data. Shown are 10-fold cross-validation on the training data (and  $1 \times s.e.$  bars), and test deviance on the test data. Also shown for comparison is the test deviance using a GAM model with 8 df for each term. The right panel shows ROC curves on the test data for the chosen GBM model (vertical line in left plot) and the GAM model.

Figure 10.19 (left panel) shows the mean binomial deviance for the sequence of GBM models, both for 10-fold CV and test data. There is a modest improvement over the performance of a GAM model, fit using smoothing splines with 8 degrees-of-freedom (df) per term. The right panel shows the ROC curves (see Section 9.2.5) for both models, which measures predictive performance. From this point of view, the performance looks very similar, with GBM perhaps having a slight edge as summarized by the AUC (area under the curve). At the point of equal sensitivity/specificity, GBM achieves 91%, and GAM 90%.

Figure 10.20 summarizes the contributions of the variables in the logistic GBM fit. We see that there is a well-defined depth range over which Black Oreo are caught, with much more frequent capture in colder waters. We do not give details of the quantitative catch model; the important variables were much the same.

All the predictors used in these models are available on a fine geographical grid; in fact they were derived from environmental atlases, satellite images and the like—see Leathwick et al. (2006) for details. This also means that predictions can be made on this grid, and imported into GIS mapping systems. Figure 10.21 shows prediction maps for both presence and catch size, with both standardized to a common set of trawl conditions; since the predictors vary in a continuous fashion with geographical location, so do the predictions.

Image /page/3/Figure/1 description: This figure displays a set of plots related to relative influence and partial dependence of different variables. The top left plot is a horizontal bar chart showing the relative influence of various factors, with 'TempResid' having the highest influence (around 25), followed by 'AvgDepth' (around 20), 'SusPartMatter' (around 15), and 'SalResid' (around 10). Other variables like 'ChlaCase2', 'Slope', 'TidalCurr', 'Pentade', 'CodendSize', 'DisOrgMatter', 'Distance', 'Speed', and 'OrbVel' have progressively lower influence. The remaining plots are partial dependence plots. The plot for 'TempResid' shows a sharp decrease from approximately -1.5 at -4 to around -5 at 2, then levels off. The plot for 'AvgDepth' shows a curve that rises from around -7 at 0 to a peak of about -1.5 at 1000, then drops to -4 at 2000. The plot for 'SusPartMatter' shows a flat line at approximately -4 from 0 to 15. The plot for 'SalResid' shows a relatively flat line around -3.5 until around 0.0, then dips to -5.5 before rising again to around -4. The plot for 'SSTGrad' shows a slight dip from around -3.5 at 0.00 to -4.5 at 0.05, then levels off at -4 until 0.15.

FIGURE 10.20. The top-left panel shows the relative influence computed from the GBM logistic regression model. The remaining panels show the partial dependence plots for the leading five variables, all plotted on the same scale for comparison.

Because of their ability to model interactions and automatically select variables, as well as robustness to outliers and missing data, GBM models are rapidly gaining popularity in this data-rich and enthusiastic community.

## 10.14.3 Demographics Data

In this section we illustrate gradient boosting on a multiclass classification problem, using MART. The data come from 9243 questionnaires filled out by shopping mall customers in the San Francisco Bay Area (Impact Resources, Inc., Columbus, OH). Among the questions are 14 concerning demographics. For this illustration the goal is to predict occupation using the other 13 variables as predictors, and hence identify demographic variables that discriminate between different occupational categories. We randomly divided the data into a training set  $(80\%)$  and test set  $(20\%).$ and used  $J = 6$  node trees with a learning rate  $\nu = 0.1$ .

Figure 10.22 shows the  $K = 9$  occupation class values along with their corresponding error rates. The overall error rate is 42.5%, which can be compared to the null rate of 69% obtained by predicting the most numerous

Image /page/4/Figure/1 description: This image displays two maps of the Exclusive Economic Zone of New Zealand, showing predicted catch per standardized trawl. The map on the left illustrates the predicted probability of catch, with a legend ranging from 0 (grey) to 0.91-1 (dark blue). The map on the right shows the predicted catch in kilograms, with a legend ranging from 0 (grey) to >10000 (dark blue). Both maps use a color gradient from grey and brown for lower values to green, teal, and dark blue for higher values. The maps are overlaid on a topographic background showing landmasses and bathymetry. Latitude and longitude lines are visible, along with scale bars in kilometers.

FIGURE 10.21. Geological prediction maps of the presence probability (left map) and catch size (right map) obtained from the gradient boosted models.

class Prof/Man (Professional/Managerial). The four best predicted classes are seen to be Retired, Student, Prof/Man, and Homemaker.

Figure 10.23 shows the relative predictor variable importances as averaged over all classes (10.46). Figure 10.24 displays the individual relative importance distributions (10.45) for each of the four best predicted classes. One sees that the most relevant predictors are generally different for each respective class. An exception is age which is among the three most relevant for predicting Retired, Student, and Prof/Man.

Figure 10.25 shows the partial dependence of the log-odds (10.52) on age for these three classes. The abscissa values are ordered codes for respective equally spaced age intervals. One sees that after accounting for the contributions of the other variables, the odds of being retired are higher for older people, whereas the opposite is the case for being a student. The odds of being professional/managerial are highest for middle-aged people. These results are of course not surprising. They illustrate that inspecting partial dependences separately for each class can lead to sensible results.

# Bibliographic Notes

Schapire (1990) developed the first simple boosting procedure in the PAC learning framework (Valiant, 1984; Kearns and Vazirani, 1994). Schapire

Image /page/5/Figure/1 description: This is a horizontal bar chart showing the error rate for different professions. The professions listed on the y-axis, from bottom to top, are Sales, Unemployed, Military, Clerical, Labor, Homemaker, Prof/Man, Retired, and Student. The x-axis represents the error rate, ranging from 0.0 to 1.0. The bars are all colored bright green. The error rates appear to be approximately: Sales (0.95), Unemployed (0.85), Military (0.80), Clerical (0.70), Labor (0.65), Homemaker (0.40), Prof/Man (0.25), Retired (0.20), and Student (0.15).

Overall Error Rate = 0.425

FIGURE 10.22. Error rate for each occupation in the demographics data.

Image /page/5/Figure/4 description: A horizontal bar chart displays the relative importance of various predictors. The predictors, listed on the y-axis from top to bottom, are yrs-BA, children, num-hsld, lang, typ-home, mar-stat, ethnic, sex, mar-dlinc, hsld-stat, edu, income, and age. The x-axis represents the relative importance, ranging from 0 to 100. The bars are all red and vary in length according to their importance. 'age' has the longest bar, extending to 100, indicating the highest relative importance. 'income' is the second most important, with its bar reaching approximately 70. 'sex' and 'hsld-stat' have bars extending to around 60 and 65 respectively. 'ethnic' and 'mar-dlinc' are around 45 and 55. 'mar-stat' and 'typ-home' are around 40 and 45. 'lang' is around 35, 'num-hsld' is around 30, 'children' is around 25, and 'yrs-BA' is the shortest bar, around 20.

FIGURE 10.23. Relative importance of the predictors as averaged over all classes for the demographics data.

Image /page/6/Figure/1 description: This image displays four bar charts, each representing predictor variable importances for different classes: Retired, Student, Prof/Man, and Homemaker. Each chart has a horizontal axis labeled "Relative Importance" ranging from 0 to 100. The vertical axis lists predictor variables such as "yrs-BA", "num-hsld", "edu", "children", "typ-home", "lang", "mar-stat", "hsld-stat", "income", "ethnic", "sex", "mar-dlinc", and "age". In the "Retired" class, "age" has the highest relative importance (around 95), followed by "mar-dlinc" (around 35), and then "sex" (around 20). For the "Student" class, "age" is the most important predictor (around 90), followed by "income" (around 85), and "mar-stat" (around 40). The "Prof/Man" class shows "edu" as the most important predictor (around 95), followed by "income" (around 90), and "age" (around 55). Lastly, in the "Homemaker" class, "sex" is the most important predictor (around 95), followed by "mar-dlinc" (around 90), and "children" (around 70).

FIGURE 10.24. Predictor variable importances separately for each of the four classes with lowest error rate for the demographics data.

Image /page/7/Figure/1 description: The image displays three partial dependence plots, each illustrating the relationship between age and partial dependence for different occupational groups: Retired, Student, and Prof/Man. The x-axis for all plots represents age, ranging from 1 to 7. The y-axis for all plots represents partial dependence, with scales varying slightly between plots. For the 'Retired' group, partial dependence is low and flat from age 1 to 4, then increases sharply from age 4 to 7. For the 'Student' group, partial dependence starts high at age 1, decreases steadily until age 3, and then remains flat at a lower value from age 4 to 7. For the 'Prof/Man' group, partial dependence is relatively flat and hovers around 0 from age 1 to 7, with a slight dip in the middle.

FIGURE 10.25. Partial dependence of the odds of three different occupations on age, for the demographics data.

showed that a *weak learner* could always improve its performance by training two additional classifiers on filtered versions of the input data stream. A weak learner is an algorithm for producing a two-class classifier with performance guaranteed (with high probability) to be significantly better than a coin-flip. After learning an initial classifier  $G_1$  on the first N training points,

- $G_2$  is learned on a new sample of N points, half of which are misclassified by  $G_1$ ;
- $G_3$  is learned on N points for which  $G_1$  and  $G_2$  disagree;
- the boosted classifier is  $G_B = \text{majority} \text{ vote}(G_1, G_2, G_3)$ .

Schapire's "Strength of Weak Learnability" theorem proves that  $G_B$  has improved performance over  $G_1$ .

Freund (1995) proposed a "boost by majority" variation which combined many weak learners simultaneously and improved the performance of the simple boosting algorithm of Schapire. The theory supporting both of these

# 384 10. Boosting and Additive Trees

algorithms requires the weak learner to produce a classifier with a fixed error rate. This led to the more adaptive and realistic AdaBoost (Freund and Schapire, 1996a) and its offspring, where this assumption was dropped.

Freund and Schapire (1996a) and Schapire and Singer (1999) provide some theory to support their algorithms, in the form of upper bounds on generalization error. This theory has evolved in the computational learning community, initially based on the concepts of PAC learning. Other theories attempting to explain boosting come from game theory (Freund and Schapire, 1996b; Breiman, 1999; Breiman, 1998), and VC theory (Schapire et al., 1998). The bounds and the theory associated with the AdaBoost algorithms are interesting, but tend to be too loose to be of practical importance. In practice, boosting achieves results far more impressive than the bounds would imply. Schapire  $(2002)$  and Meir and Rätsch  $(2003)$  give useful overviews more recent than the first edition of this book.

Friedman et al. (2000) and Friedman (2001) form the basis for our exposition in this chapter. Friedman et al. (2000) analyze AdaBoost statistically, derive the exponential criterion, and show that it estimates the log-odds of the class probability. They propose additive tree models, the right-sized trees and ANOVA representation of Section 10.11, and the multiclass logit formulation. Friedman (2001) developed gradient boosting and shrinkage for classification and regression, while Friedman (1999) explored stochastic variants of boosting. Mason et al. (2000) also embraced a gradient approach to boosting. As the published discussions of Friedman et al. (2000) shows, there is some controversy about how and why boosting works.

Since the publication of the first edition of this book, these debates have continued, and spread into the statistical community with a series of papers on consistency of boosting (Jiang, 2004; Lugosi and Vayatis, 2004; Zhang and Yu, 2005; Bartlett and Traskin, 2007). Mease and Wyner (2008), through a series of simulation examples, challenge some of our interpretations of boosting; our response (Friedman et al., 2008a) puts most of these objections to rest. A recent survey by Bühlmann and Hothorn (2007) supports our approach to boosting.

### **Exercises**

Ex. 10.1 Derive expression (10.12) for the update parameter in AdaBoost.

Ex. 10.2 Prove result (10.16), that is, the minimizer of the population version of the AdaBoost criterion, is one-half of the log odds.

Ex. 10.3 Show that the marginal average (10.47) recovers additive and multiplicative functions  $(10.50)$  and  $(10.51)$ , while the conditional expectation (10.49) does not.

Ex. 10.4

- (a) Write a program implementing AdaBoost with trees.
- (b) Redo the computations for the example of Figure 10.2. Plot the training error as well as test error, and discuss its behavior.
- (c) Investigate the number of iterations needed to make the test error finally start to rise.
- (d) Change the setup of this example as follows: define two classes, with the features in Class 1 being  $X_1, X_2, \ldots, X_{10}$ , standard independent Gaussian variates. In Class 2, the features  $X_1, X_2, \ldots, X_{10}$  are also standard independent Gaussian, but conditioned on the event  $\sum_j X_j^2 > 12$ . Now the classes have significant overlap in feature space. Repeat the AdaBoost experiments as in Figure 10.2 and discuss the results.

Ex. 10.5 Multiclass exponential loss (Zhu et al., 2005). For a K-class classification problem, consider the coding  $Y = (Y_1, \ldots, Y_K)^T$  with

$$
Y_k = \begin{cases} 1, & \text{if } G = \mathcal{G}_k \\ -\frac{1}{K-1}, & \text{otherwise.} \end{cases} \tag{10.55}
$$

Let  $f = (f_1, \ldots, f_K)^T$  with  $\sum_{k=1}^K f_k = 0$ , and define

$$
L(Y, f) = \exp\left(-\frac{1}{K}Y^T f\right). \tag{10.56}
$$

- (a) Using Lagrange multipliers, derive the population minimizer  $f^*$  of  $E(Y, f)$ , subject to the zero-sum constraint, and relate these to the class probabilities.
- (b) Show that a multiclass boosting using this loss function leads to a reweighting algorithm similar to Adaboost, as in Section 10.4.

Ex. 10.6 McNemar test (Agresti, 1996). We report the test error rates on the spam data to be 5.5% for a generalized additive model (GAM), and 4.5% for gradient boosting (GBM), with a test sample of size 1536.

(a) Show that the standard error of these estimates is about 0.6%.

Since the same test data are used for both methods, the error rates are correlated, and we cannot perform a two-sample t-test. We can compare the methods directly on each test observation, leading to the summary

|         | GBM     |       |
|---------|---------|-------|
| GAM     | Correct | Error |
| Correct | 1434    | 18    |
| Error   | 33      | 51    |

The McNemar test focuses on the discordant errors, 33 vs. 18.

(b) Conduct a test to show that GAM makes significantly more errors than gradient boosting, with a two-sided p-value of 0.036.

Ex. 10.7 Derive expression (10.32).

Ex. 10.8 Consider a K-class problem where the targets  $y_{ik}$  are coded as 1 if observation  $i$  is in class  $k$  and zero otherwise. Suppose we have a current model  $f_k(x)$ ,  $k = 1,..., K$ , with  $\sum_{k=1}^{K} f_k(x) = 0$  (see (10.21) in Section 10.6). We wish to update the model for observations in a region  $R$ in predictor space, by adding constants  $f_k(x) + \gamma_k$ , with  $\gamma_K = 0$ .

- (a) Write down the multinomial log-likelihood for this problem, and its first and second derivatives.
- (b) Using only the diagonal of the Hessian matrix in (1), and starting from  $\gamma_k = 0 \ \forall k$ , show that a one-step approximate Newton update for  $\gamma_k$  is

$$
\gamma_k^1 = \frac{\sum_{x_i \in R} (y_{ik} - p_{ik})}{\sum_{x_i \in R} p_{ik} (1 - p_{ik})}, \ k = 1, \dots, K - 1,
$$
\n(10.57)

where  $p_{ik} = \exp(f_k(x_i)) / \exp(\sum_{\ell=1}^K f_\ell(x_i)).$ 

(c) We prefer our update to sum to zero, as the current model does. Using symmetry arguments, show that

$$
\hat{\gamma}_k = \frac{K-1}{K} (\gamma_k^1 - \frac{1}{K} \sum_{\ell=1}^K \gamma_\ell^1), \ k = 1, \dots, K
$$
 (10.58)

is an appropriate update, where  $\gamma_k^1$  is defined as in (10.57) for all  $k=1,\ldots,K$ .

Ex. 10.9 Consider a K-class problem where the targets  $y_{ik}$  are coded as 1 if observation  $i$  is in class  $k$  and zero otherwise. Using the multinomial deviance loss function (10.22) and the symmetric logistic transform, use the arguments leading to the gradient boosting Algorithm 10.3 to derive Algorithm 10.4. Hint: See exercise 10.8 for step 2(b)iii.

Ex. 10.10 Show that for  $K = 2$  class classification, only one tree needs to be grown at each gradient-boosting iteration.

Ex. 10.11 Show how to compute the partial dependence function  $f_{\mathcal{S}}(X_{\mathcal{S}})$ in (10.47) efficiently.

Ex. 10.12 Referring to (10.49), let  $S = \{1\}$  and  $C = \{2\}$ , with  $f(X_1, X_2) =$  $X_1$ . Assume  $X_1$  and  $X_2$  are bivariate Gaussian, each with mean zero, variance one, and  $E(X_1, X_2) = \rho$ . Show that  $E(f(X_1, X_2 | X_2) = \rho X_2$ , even though  $f$  is not a function of  $X_2$ .

Algorithm 10.4 Gradient Boosting for K-class Classification.

- 1. Initialize  $f_{k0}(x) = 0, k = 1, 2, ..., K$ .
- 2. For  $m=1$  to  $M$ :
  - (a) Set

$$
p_k(x) = \frac{e^{f_k(x)}}{\sum_{\ell=1}^K e^{f_\ell(x)}}, \ k = 1, 2, \dots, K.
$$

- (b) For  $k = 1$  to  $K$ :
  - i. Compute  $r_{ikm} = y_{ik} p_k(x_i), i = 1, 2, ..., N$ .
  - ii. Fit a regression tree to the targets  $r_{ikm}$ ,  $i = 1, 2, ..., N$ , giving terminal regions  $R_{jkm}$ ,  $j = 1, 2, \ldots, J_m$ .
  - iii. Compute

$$
\gamma_{jkm} = \frac{K-1}{K} \frac{\sum_{x_i \in R_{jkm}} r_{ikm}}{\sum_{x_i \in R_{jkm}} |r_{ikm}| (1 - |r_{ikm}|)}, \ j = 1, 2, \dots, J_m.
$$

iv. Update  $f_{km}(x) = f_{k,m-1}(x) + \sum_{j=1}^{J_m} \gamma_{jkm} I(x \in R_{jkm}).$ 

3. Output  $\hat{f}_k(x) = f_{kM}(x), k = 1, 2, ..., K$ .

388 10. Boosting and Additive Trees