{"table_of_contents": [{"title": "Gradient flows I", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.83984375], [249.0, 97.83984375], [249.0, 111.181640625], [133.5, 111.181640625]]}, {"title": "Gradient flows in Wasserstein space", "heading_level": null, "page_id": 1, "polygon": [[133.5, 218.302734375], [346.5, 218.302734375], [346.5, 229.130859375], [133.5, 229.130859375]]}, {"title": "Reformulations of gradient flows", "heading_level": null, "page_id": 2, "polygon": [[133.5, 48.0], [327.515625, 48.0], [327.515625, 59.31298828125], [133.5, 59.31298828125]]}, {"title": "650 23 Gradient flows I", "heading_level": null, "page_id": 5, "polygon": [[133.1279296875, 26.2001953125], [247.5791015625, 26.2001953125], [247.5791015625, 35.52978515625], [133.1279296875, 35.52978515625]]}, {"title": "Gradient flows in metric spaces", "heading_level": null, "page_id": 5, "polygon": [[133.35205078125, 523.5], [320.25, 523.5], [320.25, 534.83203125], [133.35205078125, 534.83203125]]}, {"title": "652 23 Gradient flows I", "heading_level": null, "page_id": 7, "polygon": [[133.5, 26.15185546875], [246.533203125, 26.15185546875], [246.533203125, 35.578125], [133.5, 35.578125]]}, {"title": "Derivative of the Wasserstein distance", "heading_level": null, "page_id": 7, "polygon": [[133.5, 247.5], [360.984375, 247.5], [360.984375, 258.71484375], [133.5, 258.71484375]]}, {"title": "656 23 Gradient flows I", "heading_level": null, "page_id": 11, "polygon": [[133.20263671875, 25.5], [248.02734375, 25.5], [248.02734375, 35.771484375], [133.20263671875, 35.771484375]]}, {"title": "658 23 Gradient flows I", "heading_level": null, "page_id": 13, "polygon": [[133.1279296875, 26.15185546875], [247.8779296875, 26.15185546875], [247.8779296875, 35.578125], [133.1279296875, 35.578125]]}, {"title": "660 23 Gradient flows I", "heading_level": null, "page_id": 15, "polygon": [[133.27734375, 25.5], [247.4296875, 25.5], [247.4296875, 35.4814453125], [133.27734375, 35.4814453125]]}, {"title": "Step 4: Integral reformulation and restriction argument.", "heading_level": null, "page_id": 17, "polygon": [[133.5, 171.0], [406.5, 171.0], [406.5, 181.7578125], [133.5, 181.7578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 51], ["TextInlineMath", 4], ["Equation", 4], ["Text", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2355, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 31], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 69], ["TextInlineMath", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 51], ["TextInlineMath", 6], ["Text", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 75], ["Text", 7], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 996, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 71], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1121, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 63], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["Line", 68], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 36], ["TextInlineMath", 6], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 990, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 556], ["Line", 114], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9389, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 90], ["Equation", 5], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 3396, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["Line", 136], ["TextInlineMath", 3], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7820, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 578], ["Line", 87], ["Equation", 7], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["Line", 62], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1963, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["Line", 105], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2109, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 690], ["Line", 110], ["Equation", 6], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2367, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 729], ["Line", 100], ["Equation", 6], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8954, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 663], ["Line", 55], ["TextInlineMath", 8], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 695], ["Line", 88], ["TextInlineMath", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4016, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["Line", 70], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1195, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-39"}