Table 42: Baseline results on user shifts on the Yelp Open Dataset. We report the accuracy of models trained using ERM. In addition to the average accuracy across all reviews, we compute the accuracy for each reviewer and report the performance for the reviewer in the 10th percentile. In-distribution (ID) results correspond to the train-to-train setting. Parentheses show standard deviation across 3 replicates.

| Algorithm | Validation (OOD) |            | Test (OOD)      |            | Validation (ID) |            | Test (ID)       |            |
|-----------|------------------|------------|-----------------|------------|-----------------|------------|-----------------|------------|
|           | 10th percentile  | Average    | 10th percentile | Average    | 10th percentile | Average    | 10th percentile | Average    |
| ERM       | 56.0 (0.0)       | 70.5 (0.0) | 56.0 (0.0)      | 71.5 (0.0) | 56.0 (0.0)      | 70.6 (0.0) | 56.0 (0.0)      | 70.9 (0.1) |