{"table_of_contents": [{"title": "DENOISING DIFFUSION IMPLICIT MODELS", "heading_level": null, "page_id": 0, "polygon": [[106.5, 80.25], [421.048828125, 80.25], [421.048828125, 95.90625], [106.5, 95.90625]]}, {"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON> Ermon\nStanford University", "heading_level": null, "page_id": 0, "polygon": [[112.5, 116.25], [322.734375, 116.25], [322.734375, 136.5], [112.5, 136.5]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 179.25], [336.181640625, 179.25], [336.181640625, 189.5888671875], [276.75, 189.5888671875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 383.25], [207.8349609375, 383.25], [207.8349609375, 394.06640625], [107.25, 394.06640625]]}, {"title": "2 BACKGROUND", "heading_level": null, "page_id": 1, "polygon": [[106.90576171875, 378.75], [200.25, 378.75], [200.25, 389.42578125], [106.90576171875, 389.42578125]]}, {"title": "3 VARIATIONAL INFERENCE FOR NON-MARKOVIAN FORWARD PROCESSES", "heading_level": null, "page_id": 2, "polygon": [[107.25, 376.5], [495.75, 376.5], [495.75, 387.10546875], [107.25, 387.10546875]]}, {"title": "3.1 NON-MARKOVIAN FORWARD PROCESSES", "heading_level": null, "page_id": 2, "polygon": [[107.05517578125, 501.75], [306.0, 501.75], [306.0, 511.62890625], [107.05517578125, 511.62890625]]}, {"title": "3.2 GENERATIVE PROCESS AND UNIFIED VARIATIONAL INFERENCE OBJECTIVE", "heading_level": null, "page_id": 3, "polygon": [[107.1298828125, 152.560546875], [449.4375, 152.560546875], [449.4375, 162.615234375], [107.1298828125, 162.615234375]]}, {"title": "4 SAMPLING FROM GENERALIZED GENERATIVE PROCESSES", "heading_level": null, "page_id": 3, "polygon": [[107.1298828125, 635.25], [423.75, 635.25], [423.75, 646.59375], [107.1298828125, 646.59375]]}, {"title": "4.1 DENOISING DIFFUSION IMPLICIT MODELS", "heading_level": null, "page_id": 4, "polygon": [[106.5, 185.9150390625], [312.0, 185.9150390625], [312.0, 195.3896484375], [106.5, 195.3896484375]]}, {"title": "4.2 ACCELERATED GENERATION PROCESSES", "heading_level": null, "page_id": 4, "polygon": [[106.5, 441.0], [303.75, 441.0], [303.75, 450.9140625], [106.5, 450.9140625]]}, {"title": "4.3 RELEVANC<PERSON> TO NEURAL ODES", "heading_level": null, "page_id": 5, "polygon": [[106.5, 83.25], [267.0, 83.25], [267.0, 93.15087890625], [106.5, 93.15087890625]]}, {"title": "5 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[107.1298828125, 476.25], [200.6630859375, 475.5], [200.6630859375, 487.265625], [107.1298828125, 487.265625]]}, {"title": "5.1 SAMPLE QUALITY AND EFFICIENCY", "heading_level": null, "page_id": 6, "polygon": [[106.5, 346.5], [283.5, 346.5], [283.5, 356.94140625], [106.5, 356.94140625]]}, {"title": "5.2 SAMPLE CONSISTENCY IN DDIMS", "heading_level": null, "page_id": 6, "polygon": [[106.5, 635.765625], [279.0, 635.765625], [279.0, 645.8203125], [106.5, 645.8203125]]}, {"title": "5.4 RECONSTRUCTION FROM LATENT SPACE", "heading_level": null, "page_id": 8, "polygon": [[106.45751953125, 237.0], [306.0, 237.0], [306.0, 247.5], [106.45751953125, 247.5]]}, {"title": "6 RELATED WORK", "heading_level": null, "page_id": 8, "polygon": [[106.75634765625, 351.0], [212.466796875, 351.0], [212.466796875, 363.12890625], [106.75634765625, 363.12890625]]}, {"title": "7 DISCUSSION", "heading_level": null, "page_id": 8, "polygon": [[107.25, 657.0], [191.3994140625, 657.0], [191.3994140625, 668.63671875], [107.25, 668.63671875]]}, {"title": "ACKNOWLEDGEMENTS", "heading_level": null, "page_id": 9, "polygon": [[107.25, 227.77734375], [227.109375, 227.77734375], [227.109375, 237.83203125], [107.25, 237.83203125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.25, 312.46875], [177.205078125, 312.46875], [177.205078125, 322.5234375], [107.25, 322.5234375]]}, {"title": "A NON-MARKOVIAN FORWARD PROCESSES FOR A DISCRETE CASE", "heading_level": null, "page_id": 12, "polygon": [[107.25, 81.75], [461.390625, 81.75], [461.390625, 93.34423828125], [107.25, 93.34423828125]]}, {"title": "B PROOFS", "heading_level": null, "page_id": 12, "polygon": [[107.25, 514.5], [169.5, 514.5], [169.5, 525.1640625], [107.25, 525.1640625]]}, {"title": "(13) here:", "heading_level": null, "page_id": 14, "polygon": [[106.5, 467.25], [327.75, 467.25], [327.75, 478.5], [106.5, 478.5]]}, {"title": "C ADDITIONAL DERIVATIONS", "heading_level": null, "page_id": 15, "polygon": [[106.5, 444.75], [269.25, 444.75], [269.25, 456.328125], [106.5, 456.328125]]}, {"title": "C.1 ACCELERATED SAMPLING PROCESSES", "heading_level": null, "page_id": 15, "polygon": [[106.45751953125, 470.25], [294.75, 470.25], [294.75, 479.14453125], [106.45751953125, 479.14453125]]}, {"title": "C.2 DERIVATION OF DENOISING OBJECTIVES FOR DDPMS", "heading_level": null, "page_id": 16, "polygon": [[106.5, 306.75], [364.271484375, 306.0], [364.271484375, 316.72265625], [106.5, 316.72265625]]}, {"title": "D EXPERIMENTAL DETAILS", "heading_level": null, "page_id": 17, "polygon": [[106.5, 228.75], [258.78515625, 228.75], [258.78515625, 239.958984375], [106.5, 239.958984375]]}, {"title": "D.1 DATASETS AND ARCHITECTURES", "heading_level": null, "page_id": 17, "polygon": [[106.5, 253.5], [273.75, 253.5], [273.75, 263.548828125], [106.5, 263.548828125]]}, {"title": "D.2 REVERSE PROCESS SUB-SEQUENCE SELECTION", "heading_level": null, "page_id": 17, "polygon": [[106.5, 546.0], [334.986328125, 546.0], [334.986328125, 555.71484375], [106.5, 555.71484375]]}, {"title": "D.3 CLOSED FORM EQUATIONS FOR EACH SAMPLING STEP", "heading_level": null, "page_id": 17, "polygon": [[107.25, 668.25], [364.5, 668.25], [364.5, 677.14453125], [107.25, 677.14453125]]}, {"title": "D.4 SAMPLES AND CONSISTENCY", "heading_level": null, "page_id": 18, "polygon": [[106.5, 489.75], [261.0, 489.75], [261.0, 499.640625], [106.5, 499.640625]]}, {"title": "D.5 INTERPOLATION", "heading_level": null, "page_id": 18, "polygon": [[106.5, 546.75], [204.2490234375, 546.75], [204.2490234375, 556.48828125], [106.5, 556.48828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 52], ["Text", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7166, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 619], ["Line", 73], ["TextInlineMath", 4], ["Equation", 4], ["Reference", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 871], ["Line", 88], ["Reference", 8], ["TextInlineMath", 7], ["Equation", 5], ["Footnote", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1020], ["Line", 84], ["TextInlineMath", 7], ["Text", 4], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1269, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 624], ["Line", 69], ["TextInlineMath", 5], ["Reference", 5], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 785], ["Line", 101], ["TextInlineMath", 6], ["Text", 5], ["Equation", 4], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 533], ["TableCell", 157], ["Line", 65], ["Text", 3], ["Caption", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 10733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 41], ["Caption", 3], ["Text", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 48], ["TableCell", 24], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2191, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 48], ["ListItem", 12], ["Reference", 12], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 48], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 23], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 970], ["Line", 81], ["Equation", 12], ["Text", 8], ["TextInlineMath", 5], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1183], ["Line", 192], ["Equation", 10], ["TextInlineMath", 8], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8385, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 646], ["Line", 95], ["Equation", 11], ["Text", 6], ["TextInlineMath", 6], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 919], ["Line", 132], ["Equation", 9], ["TextInlineMath", 7], ["Text", 7], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1056, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 916], ["Line", 125], ["Equation", 9], ["TextInlineMath", 5], ["Text", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2739, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 629], ["Line", 97], ["TableCell", 60], ["Text", 8], ["Equation", 4], ["SectionHeader", 4], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 66], ["Text", 5], ["Equation", 3], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 676, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 27], ["Line", 10], ["Caption", 3], ["Reference", 3], ["Picture", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1877, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 6], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1200, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DENOISING DIFFUSION IMPLICIT MODELS"}