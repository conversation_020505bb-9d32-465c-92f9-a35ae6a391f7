# DENOISING DIFFUSION IMPLICIT MODELS

## <PERSON><PERSON><PERSON>, <PERSON><PERSON> & Stefano Ermon Stanford University

{tsong,chenlin,ermon}@cs.stanford.edu

### ABSTRACT

Denoising diffusion probabilistic models (DDPMs) have achieved high quality image generation without adversarial training, yet they require simulating a Markov chain for many steps in order to produce a sample. To accelerate sampling, we present denoising diffusion implicit models (DDIMs), a more efficient class of iterative implicit probabilistic models with the same training procedure as DDPMs. In DDPMs, the generative process is defined as the reverse of a particular Markovian diffusion process. We generalize DDPMs via a class of non-Markovian diffusion processes that lead to the same training objective. These non-Markovian processes can correspond to generative processes that are deterministic, giving rise to implicit models that produce high quality samples much faster. We empirically demonstrate that DDIMs can produce high quality samples  $10\times$  to  $50\times$  faster in terms of wall-clock time compared to DDPMs, allow us to trade off computation for sample quality, perform semantically meaningful image interpolation directly in the latent space, and reconstruct observations with very low error.

## 1 INTRODUCTION

Deep generative models have demonstrated the ability to produce high quality samples in many domains [\(<PERSON><PERSON><PERSON> et al.,](#page-10-0) [2020;](#page-10-0) [<PERSON> et al.,](#page-11-0) [2016a\)](#page-11-0). In terms of image generation, genera-tive adversarial networks (GANs, [Goodfellow et al.](#page-9-0) [\(2014\)](#page-9-0)) currently exhibits higher sample quality than likelihood-based methods such as variational autoencoders [\(Kingma & Welling,](#page-10-1) [2013\)](#page-10-1), autore-gressive models [\(van den Oord et al.,](#page-11-1) [2016b\)](#page-11-1) and normalizing flows [\(Rezende & Mohamed,](#page-10-2) [2015;](#page-10-2) [Dinh et al.,](#page-9-1) [2016\)](#page-9-1). However, GANs require very specific choices in optimization and architectures in order to stabilize training [\(Arjovsky et al.,](#page-9-2) [2017;](#page-9-2) [Gulrajani et al.,](#page-10-3) [2017;](#page-10-3) [Karras et al.,](#page-10-4) [2018;](#page-10-4) [Brock](#page-9-3) [et al.,](#page-9-3) [2018\)](#page-9-3), and could fail to cover modes of the data distribution [\(Zhao et al.,](#page-11-2) [2018\)](#page-11-2).

Recent works on iterative generative models [\(Bengio et al.,](#page-9-4) [2014\)](#page-9-4), such as denoising diffusion probabilistic models (DDPM,  $\overline{Ho}$  et al. [\(2020\)](#page-10-5)) and noise conditional score networks (NCSN, [Song &](#page-11-3) [Ermon](#page-11-3) [\(2019\)](#page-11-3)) have demonstrated the ability to produce samples comparable to that of GANs, without having to perform adversarial training. To achieve this, many denoising autoencoding models are trained to denoise samples corrupted by various levels of Gaussian noise. Samples are then produced by a Markov chain which, starting from white noise, progressively denoises it into an image. This generative Markov Chain process is either based on Langevin dynamics (Song  $\&$  Ermon, [2019\)](#page-11-3) or obtained by reversing a forward *diffusion process* that progressively turns an image into noise [\(Sohl-Dickstein et al.,](#page-10-6) [2015\)](#page-10-6).

A critical drawback of these models is that they require many iterations to produce a high quality sample. For DDPMs, this is because that the generative process (from noise to data) approximates the reverse of the forward *diffusion process* (from data to noise), which could have thousands of steps; iterating over all the steps is required to produce a single sample, which is much slower compared to GANs, which only needs one pass through a network. For example, it takes around 20 hours to sample 50k images of size  $32 \times 32$  from a DDPM, but less than a minute to do so from [a GAN](https://github.com/ajbrock/BigGAN-PyTorch) on a Nvidia 2080 Ti GPU. This becomes more problematic for larger images as sampling 50k images of size  $256 \times 256$  could take nearly 1000 hours on the same GPU.

To close this efficiency gap between DDPMs and GANs, we present denoising diffusion implicit models (DDIMs). DDIMs are implicit probabilistic models [\(Mohamed & Lakshminarayanan,](#page-10-7) [2016\)](#page-10-7) and are closely related to DDPMs, in the sense that they are trained with the same objective function.

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays two diagrams illustrating a process involving variables x3, x2, x1, and x0. The left diagram shows a linear progression from x3 to x0, with x3 depicted as a noisy image and x0 as a clear image of a face. Arrows indicate a forward process, labeled pθ, from x2 to x1 and x1 to x0. A dashed arrow labeled q(x2|x1) connects x1 back to x2, suggesting a reverse or conditional relationship. The right diagram also shows the progression from x3 to x0, but with additional dashed arrows indicating reverse conditional relationships: q(x3|x2, x0) from x0 and x2 to x3, and q(x2|x1, x0) from x0 and x1 to x2. Both diagrams use circles to represent variables and arrows to show relationships, with images of faces and noise placed below some variables.

Figure 1: Graphical models for diffusion (left) and non-Markovian (right) inference models.

In Section [3,](#page-2-0) we generalize the forward *diffusion process* used by DDPMs, which is Markovian, to *non-Markovian* ones, for which we are still able to design suitable reverse generative Markov chains. We show that the resulting variational training objectives have a shared surrogate objective, which is *exactly* the objective used to train DDPM. Therefore, we can freely choose from a large family of generative models using the same neural network simply by choosing a different, *non-Markovian* diffusion process (Section [4.1\)](#page-4-0) and the corresponding reverse generative Markov Chain. In particular, we are able to use *non-Markovian* diffusion processes which lead to "short" generative Markov chains (Section  $4.2$ ) that can be simulated in a small number of steps. This can massively increase sample efficiency only at a minor cost in sample quality.

In Section [5,](#page-5-0) we demonstrate several empirical benefits of DDIMs over DDPMs. *First*, DDIMs have superior sample generation quality compared to DDPMs, when we accelerate sampling by  $10\times$  to 100× using our proposed method. *Second*, DDIM samples have the following "consistency" property, which does not hold for DDPMs: if we start with the same initial latent variable and generate several samples with Markov chains of various lengths, these samples would have similar high-level features. *Third*, because of "consistency" in DDIMs, we can perform semantically meaningful image interpolation by manipulating the initial latent variable in DDIMs, unlike DDPMs which interpolates near the image space due to the stochastic generative process.

## 2 BACKGROUND

Given samples from a data distribution  $q(x_0)$ , we are interested in learning a model distribution  $p_{\theta}(\mathbf{x}_0)$  that approximates  $q(\mathbf{x}_0)$  and is easy to sample from. Denoising diffusion probabilistic models (DDPMs, [Sohl-Dickstein et al.](#page-10-6) [\(2015\)](#page-10-6); [Ho et al.](#page-10-5) [\(2020\)](#page-10-5)) are latent variable models of the form

<span id="page-1-3"></span>
$$
p_{\theta}(\boldsymbol{x}_0) = \int p_{\theta}(\boldsymbol{x}_{0:T}) \mathrm{d}\boldsymbol{x}_{1:T}, \quad \text{where} \quad p_{\theta}(\boldsymbol{x}_{0:T}) := p_{\theta}(\boldsymbol{x}_T) \prod_{t=1}^T p_{\theta}^{(t)}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t)
$$
(1)

where  $x_1, \ldots, x_T$  are latent variables in the same sample space as  $x_0$  (denoted as X). The parameters  $\theta$  are learned to fit the data distribution  $q(x_0)$  by maximizing a variational lower bound:

<span id="page-1-1"></span>
$$
\max_{\theta} \mathbb{E}_{q(\boldsymbol{x}_0)}[\log p_{\theta}(\boldsymbol{x}_0)] \leq \max_{\theta} \mathbb{E}_{q(\boldsymbol{x}_0,\boldsymbol{x}_1,\ldots,\boldsymbol{x}_T)}[\log p_{\theta}(\boldsymbol{x}_{0:T}) - \log q(\boldsymbol{x}_{1:T}|\boldsymbol{x}_0)] \tag{2}
$$

where  $q(x_{1:T} | x_0)$  is some inference distribution over the latent variables. Unlike typical latent vari-able models (such as the variational autoencoder [\(Rezende et al.,](#page-10-8) [2014\)](#page-10-8)), DDPMs are learned with a fixed (rather than trainable) inference procedure  $q(x_{1:T} | x_0)$ , and latent variables are relatively high dimensional. For example,  $H_0$  et al. [\(2020\)](#page-10-5) considered the following Markov chain with Gaussian transitions parameterized by a decreasing sequence  $\alpha_{1:T} \in (0,1]^T$ :

$$
q(\boldsymbol{x}_{1:T}|\boldsymbol{x}_0) := \prod_{t=1}^T q(\boldsymbol{x}_t|\boldsymbol{x}_{t-1}), \text{ where } q(\boldsymbol{x}_t|\boldsymbol{x}_{t-1}) := \mathcal{N}\left(\sqrt{\frac{\alpha_t}{\alpha_{t-1}}} \boldsymbol{x}_{t-1}, \left(1 - \frac{\alpha_t}{\alpha_{t-1}}\right) \boldsymbol{I}\right) \tag{3}
$$

where the covariance matrix is ensured to have positive terms on its diagonal. This is called the *forward process* due to the autoregressive nature of the sampling procedure (from  $x_0$  to  $x_T$ ). We call the latent variable model  $p_{\theta}(x_{0:T})$ , which is a Markov chain that samples from  $x_T$  to  $x_0$ , the *generative process*, since it approximates the intractable *reverse process*  $q(x_{t-1}|x_t)$ . Intuitively, the forward process progressively adds noise to the observation  $x_0$ , whereas the generative process progressively denoises a noisy observation (Figure [1,](#page-1-0) left).

A special property of the forward process is that

<span id="page-1-2"></span>
$$
q(\boldsymbol{x}_t|\boldsymbol{x}_0) := \int q(\boldsymbol{x}_{1:t}|\boldsymbol{x}_0) \mathrm{d} \boldsymbol{x}_{1:(t-1)} = \mathcal{N}(\boldsymbol{x}_t; \sqrt{\alpha_t} \boldsymbol{x}_0, (1-\alpha_t) \boldsymbol{I});
$$

so we can express  $x_t$  as a linear combination of  $x_0$  and a noise variable  $\epsilon$ .

<span id="page-2-7"></span><span id="page-2-4"></span>
$$
x_t = \sqrt{\alpha_t} x_0 + \sqrt{1 - \alpha_t} \epsilon, \quad \text{where} \quad \epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I}). \tag{4}
$$

When we set  $\alpha_T$  sufficiently close to 0,  $q(x_T | x_0)$  converges to a standard Gaussian for all  $x_0$ , so it is natural to set  $p_{\theta}(x_T) := \mathcal{N}(\mathbf{0}, I)$ . If all the conditionals are modeled as Gaussians with trainable mean functions and fixed variances, the objective in Eq. [\(2\)](#page-1-1) can be simplified to<sup>[1](#page-2-1)</sup>:

$$
L_{\gamma}(\epsilon_{\theta}) := \sum_{t=1}^{T} \gamma_t \mathbb{E}_{\mathbf{x}_0 \sim q(\mathbf{x}_0), \epsilon_t \sim \mathcal{N}(\mathbf{0}, \mathbf{I})} \left[ \left\| \epsilon_{\theta}^{(t)}(\sqrt{\alpha_t} \mathbf{x}_0 + \sqrt{1 - \alpha_t} \epsilon_t) - \epsilon_t \right\|_2^2 \right]
$$
(5)

where  $\epsilon_{\theta} := \{\epsilon_{\theta}^{(t)}\}$  $\{\theta_{\theta}^{(t)}\}_{t=1}^{T}$  is a set of T functions, each  $\epsilon_{\theta}^{(t)}$  $\mathcal{E}_{\theta}^{(t)}: \mathcal{X} \to \mathcal{X}$  (indexed by t) is a function with trainable parameters  $\theta^{(t)}$ , and  $\gamma := [\gamma_1, \dots, \gamma_T]$  is a vector of positive coefficients in the objective that depends on  $\alpha_{1:T}$ . In [Ho et al.](#page-10-5) [\(2020\)](#page-10-5), the objective with  $\gamma = 1$  is optimized instead to maximize generation performance of the trained model; this is also the same objective used in noise conditional score networks [\(Song & Ermon,](#page-11-3) [2019\)](#page-11-3) based on score matching (Hyvärinen, [2005;](#page-10-9) [Vincent,](#page-11-4) [2011\)](#page-11-4). From a trained model,  $x_0$  is sampled by first sampling  $x_T$  from the prior  $p_\theta(x_T)$ , and then sampling  $x_{t-1}$  from the generative processes iteratively.

The length  $T$  of the forward process is an important hyperparameter in DDPMs. From a variational perspective, a large  $T$  allows the reverse process to be close to a Gaussian [\(Sohl-Dickstein et al.,](#page-10-6) [2015\)](#page-10-6), so that the generative process modeled with Gaussian conditional distributions becomes a good approximation; this motivates the choice of large T values, such as  $T = 1000$  in [Ho et al.](#page-10-5)  $(2020)$ . However, as all T iterations have to be performed sequentially, instead of in parallel, to obtain a sample  $x_0$ , sampling from DDPMs is much slower than sampling from other deep generative models, which makes them impractical for tasks where compute is limited and latency is critical.

<span id="page-2-0"></span>

## 3 VARIATIONAL INFERENCE FOR NON-MARKOVIAN FORWARD PROCESSES

Because the generative model approximates the reverse of the inference process, we need to rethink the inference process in order to reduce the number of iterations required by the generative model. Our key observation is that the DDPM objective in the form of  $L_{\gamma}$  only depends on the marginals<sup>[2](#page-2-2)</sup>  $q(x_t|x_0)$ , but not directly on the joint  $q(x_{1:T} | x_0)$ . Since there are many inference distributions (joints) with the same marginals, we explore alternative inference processes that are non-Markovian, which leads to new generative processes (Figure [1,](#page-1-0) right). These non-Markovian inference process lead to the same surrogate objective function as DDPM, as we will show below. In Appendix [A,](#page-12-0) we show that the non-Markovian perspective also applies beyond the Gaussian case.

### 3.1 NON-MARKOVIAN FORWARD PROCESSES

Let us consider a family Q of inference distributions, indexed by a real vector  $\sigma \in \mathbb{R}^T_{\geq 0}$ :

<span id="page-2-6"></span><span id="page-2-5"></span>
$$
q_{\sigma}(\boldsymbol{x}_{1:T}|\boldsymbol{x}_0) := q_{\sigma}(\boldsymbol{x}_T|\boldsymbol{x}_0) \prod_{t=2}^T q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t,\boldsymbol{x}_0)
$$
(6)

where  $q_{\sigma}(\boldsymbol{x}_T | \boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_T} \boldsymbol{x}_0, (1 - \alpha_T) \boldsymbol{I})$  and for all  $t > 1$ ,

$$
q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t},\boldsymbol{x}_{0})=\mathcal{N}\left(\sqrt{\alpha_{t-1}}\boldsymbol{x}_{0}+\sqrt{1-\alpha_{t-1}-\sigma_{t}^{2}}\cdot\frac{\boldsymbol{x}_{t}-\sqrt{\alpha_{t}}\boldsymbol{x}_{0}}{\sqrt{1-\alpha_{t}}},\sigma_{t}^{2}\boldsymbol{I}\right).
$$
 (7)

The mean function is chosen to order to ensure that  $q_{\sigma}(\mathbf{x}_t|\mathbf{x}_0) = \mathcal{N}(\sqrt{\alpha_t}\mathbf{x}_0, (1-\alpha_t)\mathbf{I})$  for all t (see Lemma  $1$  of Appendix  $B$ ), so that it defines a joint inference distribution that matches the "marginals" as desired. The forward process<sup>[3](#page-2-3)</sup> can be derived from Bayes' rule:

$$
q_{\sigma}(\boldsymbol{x}_t|\boldsymbol{x}_{t-1},\boldsymbol{x}_0) = \frac{q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t,\boldsymbol{x}_0)q_{\sigma}(\boldsymbol{x}_t|\boldsymbol{x}_0)}{q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_0)},
$$
\n(8)

<span id="page-2-1"></span><sup>&</sup>lt;sup>1</sup>Please refer to Appendix [C.2](#page-16-0) for details.

<span id="page-2-2"></span><sup>&</sup>lt;sup>2</sup>We slightly abuse this term (as well as joints) when only conditioned on  $x_0$ .

<span id="page-2-3"></span><sup>&</sup>lt;sup>3</sup>We overload the term "forward process" for cases where the inference model is not a diffusion.

which is also Gaussian (although we do not use this fact for the remainder of this paper). Unlike the diffusion process in Eq. [\(3\)](#page-1-2), the forward process here is no longer Markovian, since each  $x_t$  could depend on both  $x_{t-1}$  and  $x_0$ . The magnitude of  $\sigma$  controls the how stochastic the forward process is; when  $\sigma \to 0$ , we reach an extreme case where as long as we observe  $x_0$  and  $x_t$  for some t, then  $x_{t-1}$  become known and fixed.

### 3.2 GENERATIVE PROCESS AND UNIFIED VARIATIONAL INFERENCE OBJECTIVE

Next, we define a trainable generative process  $p_{\theta}(\boldsymbol{x}_{0:T})$  where each  $p_{\theta}^{(t)}$  $\mathcal{L}_{\theta}^{(t)}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t})$  leverages knowledge of  $q_{\sigma}(x_{t-1}|x_t, x_0)$ . Intuitively, given a noisy observation  $x_t$ , we first make a prediction<sup>[4](#page-3-0)</sup> of the corresponding  $x_0$ , and then use it to obtain a sample  $x_{t-1}$  through the reverse conditional distribution  $q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0)$ , which we have defined.

For some  $x_0 \sim q(x_0)$  and  $\epsilon_t \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$ ,  $x_t$  can be obtained using Eq. [\(4\)](#page-2-4). The model  $\epsilon_\theta^{(t)}$  $\theta_{\theta}^{(t)}(x_t)$  then attempts to predict  $\epsilon_t$  from  $x_t$ , without knowledge of  $x_0$ . By rewriting Eq. [\(4\)](#page-2-4), one can then predict the *denoised observation*, which is a prediction of  $x_0$  given  $x_t$ :

<span id="page-3-2"></span>
$$
f_{\theta}^{(t)}(\boldsymbol{x}_t) := (\boldsymbol{x}_t - \sqrt{1 - \alpha_t} \cdot \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t)) / \sqrt{\alpha_t}.
$$
 (9)

We can then define the generative process with a fixed prior  $p_\theta(\mathbf{x}_T) = \mathcal{N}(\mathbf{0}, \mathbf{I})$  and

$$
p_{\theta}^{(t)}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t}) = \begin{cases} \mathcal{N}(f_{\theta}^{(1)}(\boldsymbol{x}_{1}), \sigma_{1}^{2}\boldsymbol{I}) & \text{if } t = 1\\ q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t}, f_{\theta}^{(t)}(\boldsymbol{x}_{t})) & \text{otherwise,} \end{cases}
$$
(10)

where  $q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, f_{\theta}^{(t)}(\mathbf{x}_t))$  is defined as in Eq. [\(7\)](#page-2-5) with  $\mathbf{x}_0$  replaced by  $f_{\theta}^{(t)}$  $\theta_{\theta}^{(t)}(x_t)$ . We add some Gaussian noise (with covariance  $\sigma_1^2 I$ ) for the case of  $t = 1$  to ensure that the generative process is supported everywhere.

We optimize  $\theta$  via the following variational inference objective (which is a functional over  $\epsilon_{\theta}$ ):

$$
J_{\sigma}(\epsilon_{\theta}) := \mathbb{E}_{\mathbf{x}_{0:T} \sim q_{\sigma}(\mathbf{x}_{0:T})}[\log q_{\sigma}(\mathbf{x}_{1:T}|\mathbf{x}_0) - \log p_{\theta}(\mathbf{x}_{0:T})]
$$
(11)  
= 
$$
\mathbb{E}_{\mathbf{x}_{0:T} \sim q_{\sigma}(\mathbf{x}_{0:T})} \left[ \log q_{\sigma}(\mathbf{x}_T|\mathbf{x}_0) + \sum_{t=2}^T \log q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0) - \sum_{t=1}^T \log p_{\theta}^{(t)}(\mathbf{x}_{t-1}|\mathbf{x}_t) - \log p_{\theta}(\mathbf{x}_T) \right]
$$

where we factorize  $q_{\sigma}(\mathbf{x}_{1:T}|\mathbf{x}_0)$  according to Eq. [\(6\)](#page-2-6) and  $p_{\theta}(\mathbf{x}_{0:T})$  according to Eq. [\(1\)](#page-1-3).

From the definition of  $J_{\sigma}$ , it would appear that a different model has to be trained for every choice of  $σ$ , since it corresponds to a different variational objective (and a different generative process). However,  $J_{\sigma}$  is equivalent to  $L_{\gamma}$  for certain weights  $\gamma$ , as we show below.

<span id="page-3-1"></span>**Theorem 1.** For all  $\sigma > 0$ , there exists  $\gamma \in \mathbb{R}_{>0}^T$  and  $C \in \mathbb{R}$ , such that  $J_{\sigma} = L_{\gamma} + C$ .

The variational objective  $L_{\gamma}$  is special in the sense that if parameters  $\theta$  of the models  $\epsilon_{\theta}^{(t)}$  $\theta$ <sup>(*l*)</sup> are not shared across different t, then the optimal solution for  $\epsilon_{\theta}$  will not depend on the weights  $\gamma$  (as global optimum is achieved by separately maximizing each term in the sum). This property of  $L_{\gamma}$  has two implications. On the one hand, this justified the use of  $L_1$  as a surrogate objective function for the variational lower bound in DDPMs; on the other hand, since  $J_{\sigma}$  is equivalent to some  $L_{\gamma}$  from Theorem [1,](#page-3-1) the optimal solution of  $J_{\sigma}$  is also the same as that of  $L_1$ . Therefore, if parameters are not shared across t in the model  $\epsilon_{\theta}$ , then the  $L_1$  objective used by [Ho et al.](#page-10-5) [\(2020\)](#page-10-5) can be used as a surrogate objective for the variational objective  $J_{\sigma}$  as well.

## 4 SAMPLING FROM GENERALIZED GENERATIVE PROCESSES

With  $L_1$  as the objective, we are not only learning a generative process for the Markovian inference process considered in [Sohl-Dickstein et al.](#page-10-6) [\(2015\)](#page-10-6) and [Ho et al.](#page-10-5) [\(2020\)](#page-10-5), but also generative processes for many non-Markovian forward processes parametrized by  $\sigma$  that we have described. Therefore, we can essentially use pretrained DDPM models as the solutions to the new objectives, and focus on finding a generative process that is better at producing samples subject to our needs by changing  $\sigma$ .

<span id="page-3-0"></span><sup>4</sup>Learning a distribution over the predictions is also possible, but empirically we found little benefits of it.

<span id="page-4-4"></span><span id="page-4-3"></span>Image /page/4/Figure/1 description: This is a diagram illustrating a probabilistic model. It shows four nodes labeled x3, x1, x0, and a faded x2. Arrows indicate dependencies and probabilities. An arrow labeled 'p\_theta' goes from x3 to x1. An arrow labeled 'alpha1' points from the path of p\_theta to x1. An arrow labeled 'alpha3' points to x3. An arrow goes from x1 to x0. Dashed arrows indicate conditional probabilities: 'q(x3|x1, x0)' points to x3, and 'q(x2|x0)' points to the faded x2. Dashed lines also connect x1 and x0 to the bottom of the diagram, suggesting further relationships or conditioning.

Figure 2: Graphical model for accelerated generation, where  $\tau = [1, 3]$ .

<span id="page-4-0"></span>

#### 4.1 DENOISING DIFFUSION IMPLICIT MODELS

From  $p_{\theta}(\mathbf{x}_{1:T})$  in Eq. [\(10\)](#page-3-2), one can generate a sample  $\mathbf{x}_{t-1}$  from a sample  $\mathbf{x}_t$  via:

$$
\boldsymbol{x}_{t-1} = \sqrt{\alpha_{t-1}} \underbrace{\left(\frac{\boldsymbol{x}_t - \sqrt{1 - \alpha_t} \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t)}{\sqrt{\alpha_t}}\right)}_{\text{"predicted x_0"}} + \underbrace{\sqrt{1 - \alpha_{t-1} - \sigma_t^2} \cdot \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t)}_{\text{"direction pointing to } \boldsymbol{x}_t ext{"}} + \underbrace{\sigma_t \epsilon_t}_{\text{random noise}}(12)
$$

where  $\epsilon_t$  ∼  $\mathcal{N}(\mathbf{0}, I)$  is standard Gaussian noise independent of  $x_t$ , and we define  $\alpha_0 := 1$ . Different choices of  $\sigma$  values results in different generative processes, all while using the same model  $\epsilon_{\theta}$ , so re-training the model is unnecessary. When  $\sigma_t = \sqrt{(1 - \alpha_{t-1})/(1 - \alpha_t)}\sqrt{1 - \alpha_t/\alpha_{t-1}}$  for all t, the forward process becomes Markovian, and the generative process becomes a DDPM.

We note another special case when  $\sigma_t = 0$  for all  $t^5$  $t^5$ ; the forward process becomes deterministic given  $x_{t-1}$  and  $x_0$ , except for  $t = 1$ ; in the generative process, the coefficient before the random noise  $\epsilon_t$  becomes zero. The resulting model becomes an implicit probabilistic model [\(Mohamed &](#page-10-7) [Lakshminarayanan,](#page-10-7) [2016\)](#page-10-7), where samples are generated from latent variables with a fixed procedure (from  $x_T$  to  $x_0$ ). We name this the *denoising diffusion implicit model* (DDIM, pronounced /d:Im/), because it is an implicit probabilistic model trained with the DDPM objective (despite the forward process no longer being a diffusion).

<span id="page-4-1"></span>

### 4.2 ACCELERATED GENERATION PROCESSES

In the previous sections, the generative process is considered as the approximation to the reverse process; since of the forward process has  $T$  steps, the generative process is also forced to sample  $T$ steps. However, as the denoising objective  $L_1$  does not depend on the specific forward procedure as long as  $q_{\sigma}(x_t|x_0)$  is fixed, we may also consider forward processes with lengths smaller than T, which accelerates the corresponding generative processes without having to train a different model.

Let us consider the forward process as defined not on all the latent variables  $x_{1:T}$ , but on a subset  $\{x_{\tau_1}, \ldots, x_{\tau_S}\}\$ , where  $\tau$  is an increasing sub-sequence of  $[1, \ldots, T]$  of length S. In particular, we define the sequential forward process over  $x_{\tau_1}, \ldots, x_{\tau_S}$  such that  $q(x_{\tau_i}|x_0)$  = particular, we define the sequential forward process over  $x_{\tau_1},...,x_{\tau_s}$  such that  $q(x_{\tau_i}|x_0)$  –<br> $\mathcal{N}(\sqrt{\alpha_{\tau_i}}x_0, (1-\alpha_{\tau_i})I)$  matches the "marginals" (see Figure [2](#page-4-3) for an illustration). The generative process now samples latent variables according to reversed $(\tau)$ , which we term *(sampling) trajectory*. When the length of the sampling trajectory is much smaller than T, we may achieve significant increases in computational efficiency due to the iterative nature of the sampling process.

Using a similar argument as in Section [3,](#page-2-0) we can justify using the model trained with the  $L_1$  objective, so no changes are needed in training. We show that only slight changes to the updates in Eq. [\(12\)](#page-4-4) are needed to obtain the new, faster generative processes, which applies to DDPM, DDIM, as well as all generative processes considered in Eq. [\(10\)](#page-3-2). We include these details in Appendix [C.1.](#page-15-0)

In principle, this means that we can train a model with an arbitrary number of forward steps but only sample from some of them in the generative process. Therefore, the trained model could consider many more steps than what is considered in  $(Ho et al., 2020)$  $(Ho et al., 2020)$  $(Ho et al., 2020)$  or even a continuous time variable t [\(Chen et al.,](#page-9-5) [2020\)](#page-9-5). We leave empirical investigations of this aspect as future work.

<span id="page-4-2"></span><sup>&</sup>lt;sup>5</sup>Although this case is not covered in Theorem [1,](#page-3-1) we can always approximate it by making  $\sigma_t$  very small.

### 4.3 RELEVANCE TO NEURAL ODES

Moreover, we can rewrite the DDIM iterate according to Eq.  $(12)$ , and its similarity to Euler integration for solving ordinary differential equations (ODEs) becomes more apparent:

$$
\frac{\boldsymbol{x}_{t-\Delta t}}{\sqrt{\alpha_{t-\Delta t}}} = \frac{\boldsymbol{x}_t}{\sqrt{\alpha_t}} + \left(\sqrt{\frac{1-\alpha_{t-\Delta t}}{\alpha_{t-\Delta t}}} - \sqrt{\frac{1-\alpha_t}{\alpha_t}}\right) \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t)
$$
(13)

To derive the corresponding ODE, we can reparameterize  $(\sqrt{1-\alpha}/\sqrt{\alpha})$  with  $\sigma$  and  $(\frac{x}{\sqrt{\alpha}})$  with  $\bar{x}$ . In the continuous case,  $\sigma$  and x are functions of t, where  $\sigma : \mathbb{R}_{\geq 0} \to \mathbb{R}_{\geq 0}$  is continous, increasing with  $\sigma(0) = 0$ . Equation [\(13\)](#page-5-1) with can be treated as a Euler method over the following ODE:

<span id="page-5-2"></span><span id="page-5-1"></span>
$$
\mathrm{d}\bar{\mathbf{x}}(t) = \epsilon_{\theta}^{(t)} \left( \frac{\bar{\mathbf{x}}(t)}{\sqrt{\sigma^2 + 1}} \right) \mathrm{d}\sigma(t),\tag{14}
$$

where the initial conditions is  $x(T) \sim \mathcal{N}(0, \sigma(T))$  for a very large  $\sigma(T)$  (which corresponds to the case of  $\alpha \approx 0$ ). This suggests that with enough discretization steps, the we can also reverse the generation process (going from  $t = 0$  to T), which encodes  $x_0$  to  $x_T$  and simulates the reverse of the ODE in Eq. [\(14\)](#page-5-2). This suggests that unlike DDPM, we can use DDIM to obtain encodings of the observations (as the form of  $x_T$ ), which might be useful for other downstream applications that requires latent representations of a model.

In a concurrent work, [\(Song et al.,](#page-11-5) [2020\)](#page-11-5) proposed a "probability flow ODE" that aims to recover the marginal densities of a stochastic differential equation (SDE) based on scores, from which a similar sampling schedule can be obtained. Here, we state that the our ODE is equivalent to a special case of theirs (which corresponds to a continuous-time analog of DDPM).

**Proposition 1.** The ODE in Eq. [\(14\)](#page-5-2) with the optimal model  $\epsilon_{\theta}^{(t)}$ θ *has an equivalent probability flow ODE corresponding to the "Variance-Exploding" SDE in [Song et al.](#page-11-5) [\(2020\)](#page-11-5).*

We include the proof in Appendix [B.](#page-12-2) While the ODEs are equivalent, the sampling procedures are not, since the Euler method for the probability flow ODE will make the following update:

$$
\frac{\boldsymbol{x}_{t-\Delta t}}{\sqrt{\alpha_{t-\Delta t}}} = \frac{\boldsymbol{x}_t}{\sqrt{\alpha_t}} + \frac{1}{2} \left( \frac{1 - \alpha_{t-\Delta t}}{\alpha_{t-\Delta t}} - \frac{1 - \alpha_t}{\alpha_t} \right) \cdot \sqrt{\frac{\alpha_t}{1 - \alpha_t}} \cdot \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t)
$$
(15)

which is equivalent to ours if  $\alpha_t$  and  $\alpha_{t-\Delta t}$  are close enough. In fewer sampling steps, however, these choices will make a difference; we take Euler steps with respect to  $d\sigma(t)$  (which depends less directly on the scaling of "time" t) whereas [Song et al.](#page-11-5) [\(2020\)](#page-11-5) take Euler steps with respect to dt.

<span id="page-5-0"></span>

## 5 EXPERIMENTS

In this section, we show that DDIMs outperform DDPMs in terms of image generation when fewer iterations are considered, giving speed ups of  $10\times$  to  $100\times$  over the original DDPM generation process. Moreover, unlike DDPMs, once the initial latent variables  $x_T$  are fixed, DDIMs retain highlevel image features regardless of the generation trajectory, so they are able to perform interpolation directly from the latent space. DDIMs can also be used to encode samples that reconstruct them from the latent code, which DDPMs cannot do due to the stochastic sampling process.

For each dataset, we use the **same trained model** with  $T = 1000$  and the objective being  $L_{\gamma}$ from Eq. [\(5\)](#page-2-7) with  $\gamma = 1$ ; as we argued in Section [3,](#page-2-0) no changes are needed with regards to the training procedure. The only changes that we make is how we produce samples from the model; we achieve this by controlling  $\tau$  (which controls how fast the samples are obtained) and  $\sigma$  (which interpolates between the deterministic DDIM and the stochastic DDPM).

We consider different sub-sequences  $\tau$  of  $[1, \ldots, T]$  and different variance hyperparameters  $\sigma$  indexed by elements of  $\tau$ . To simplify comparisons, we consider  $\sigma$  with the form:

$$
\sigma_{\tau_i}(\eta) = \eta \sqrt{\left(1 - \alpha_{\tau_{i-1}}\right) / \left(1 - \alpha_{\tau_i}\right)} \sqrt{1 - \alpha_{\tau_i} / \alpha_{\tau_{i-1}}},\tag{16}
$$

where  $\eta \in \mathbb{R}_{\geq 0}$  is a hyperparameter that we can directly control. This includes an original DDPM generative process when  $\eta = 1$  and DDIM when  $\eta = 0$ . We also consider DDPM where the random noise has a larger standard deviation than  $\sigma(1)$ , which we denote as  $\hat{\sigma}$ :  $\hat{\sigma}_{\tau_i} = \sqrt{1 - \alpha_{\tau_i}/\alpha_{\tau_{i-1}}}$ . This is used by [the implementation](https://github.com/hojonathanho/diffusion/blob/master/scripts/run_cifar.py#L136) in  $H_0$  et al. [\(2020\)](#page-10-5) only to obtain the CIFAR10 samples, but not samples of the other datasets. We include more details in Appendix [D.](#page-17-0)

|   | S   | CIFAR10 $(32 \times 32)$ |        |        |       |      | CelebA $(64 \times 64)$ |        |        |       |       |      |
|---|-----|--------------------------|--------|--------|-------|------|-------------------------|--------|--------|-------|-------|------|
|   |     | 10                       | 20     | 50     | 100   | 1000 | 10                      | 20     | 50     | 100   | 1000  |      |
| η | 0.0 | 13.36                    | 6.84   | 4.67   | 4.16  | 4.04 | 17.33                   | 13.73  | 9.17   | 6.53  | 3.51  |      |
|   | 0.2 | 14.04                    | 7.11   | 4.77   | 4.25  | 4.09 | 17.66                   | 14.11  | 9.51   | 6.79  | 3.64  |      |
|   | 0.5 | 16.66                    | 8.35   | 5.25   | 4.46  | 4.29 | 19.86                   | 16.06  | 11.01  | 8.09  | 4.28  |      |
|   | 1.0 | 41.07                    | 18.36  | 8.01   | 5.78  | 4.73 | 33.12                   | 26.03  | 18.48  | 13.93 | 5.98  |      |
|   |     | $\hat{\sigma}$           | 367.43 | 133.37 | 32.72 | 9.99 | 3.17                    | 299.71 | 183.83 | 71.71 | 45.20 | 3.26 |

<span id="page-6-0"></span>Table 1: CIFAR10 and CelebA image generation measured in FID.  $\eta = 1.0$  and  $\hat{\sigma}$  are cases of DDPM (although [Ho et al.](#page-10-5) [\(2020\)](#page-10-5) only considered  $T = 1000$  steps, and  $S < T$  can be seen as simulating DDPMs trained with S steps), and  $\eta = 0.0$  indicates DDIM.

<span id="page-6-1"></span>Figure 3: CIFAR10 and CelebA samples with  $\dim(\tau) = 10$  and  $\dim(\tau) = 100$ .

### 5.1 SAMPLE QUALITY AND EFFICIENCY

In Table [1,](#page-6-0) we report the quality of the generated samples with models trained on CIFAR10 and CelebA, as measured by Frechet Inception Distance (FID [\(Heusel et al.,](#page-10-10) [2017\)](#page-10-10)), where we vary the number of timesteps used to generate a sample  $(\dim(\tau))$  and the stochasticity of the process (η). As expected, the sample quality becomes higher as we increase  $\dim(\tau)$ , presenting a tradeoff between sample quality and computational costs. We observe that DDIM ( $\eta = 0$ ) achieves the best sample quality when  $\dim(\tau)$  is small, and DDPM  $(\eta = 1 \text{ and } \hat{\sigma})$  typically has worse sample quality compared to its less stochastic counterparts with the same  $\dim(\tau)$ , except for the case for  $\dim(\tau) = 1000$  and  $\hat{\sigma}$  reported by [Ho et al.](#page-10-5) [\(2020\)](#page-10-5) where DDIM is marginally worse. However, the sample quality of  $\hat{\sigma}$  becomes much worse for smaller  $\dim(\tau)$ , which suggests that it is ill-suited for shorter trajectories. DDIM, on the other hand, achieves high sample quality much more consistently.

In Figure [3,](#page-6-1) we show CIFAR10 and CelebA samples with the same number of sampling steps and varying  $\sigma$ . For the DDPM, the sample quality deteriorates rapidly when the sampling trajectory has 10 steps. For the case of  $\hat{\sigma}$ , the generated images seem to have more noisy perturbations under short trajectories; this explains why the FID scores are much worse than other methods, as FID is very sensitive to such perturbations (as discussed in [Jolicoeur-Martineau et al.](#page-10-11) [\(2020\)](#page-10-11)).

In Figure [4,](#page-7-0) we show that the amount of time needed to produce a sample scales linearly with the length of the sample trajectory. This suggests that DDIM is useful for producing samples more efficiently, as samples can be generated in much fewer steps. Notably, DDIM is able to produce samples with quality comparable to 1000 step models within 20 to 100 steps, which is a  $10\times$  to  $50\times$  speed up compared to the original DDPM. Even though DDPM could also achieve reasonable sample quality with  $100\times$  steps, DDIM requires much fewer steps to achieve this; on CelebA, the FID score of the 100 step DDPM is similar to that of the 20 step DDIM.

### 5.2 SAMPLE CONSISTENCY IN DDIMS

For DDIM, the generative process is deterministic, and  $x_0$  would depend only on the initial state  $x_T$ . In Figure [5,](#page-7-1) we observe the generated images under different generative trajectories (i.e. different  $\tau$ ) while starting with the same initial  $x_T$ . Interestingly, for the generated images with the same initial  $x_T$ , most high-level features are similar, regardless of the generative trajectory. In many cases, samples generated with only 20 steps are already very similar to ones generated with 1000 steps in terms of high-level features, with only minor differences in details. Therefore, it would appear that  $x_T$  alone would be an informative latent encoding of the image; and minor details that affects sample

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image contains two line graphs. The left graph is titled "CIFAR10" and plots "Hours" on the y-axis against "# steps" on the x-axis. The x-axis ranges from 10 to 1000, and the y-axis ranges from 0.2 to 20. The line graph shows an increasing trend, with data points at (10, 0.2), (30, 0.5), (100, 2), (300, 5), and (1000, 20). An image of a car is placed near the data point at 100 steps. The right graph is titled "Bedroom" and also plots "Hours" on the y-axis against "# steps" on the x-axis. The x-axis ranges from 10 to 1000, and the y-axis ranges from 10 to 1000. The line graph shows an increasing trend, with data points at (10, 10), (30, 30), (100, 100), (300, 300), and (1000, 1000). Images of bedrooms are placed near the data points at 10 and 30 steps.

Figure 4: Hours to sample 50k images with one Nvidia 2080 Ti GPU and samples at different steps.

<span id="page-7-1"></span>Image /page/7/Figure/3 description: This image displays a grid of generated images, categorized by sample timesteps and content type. The top section shows images of birds, cars, frogs, and horses at timesteps 10, 20, 50, 100, and 1000. The middle section features interior scenes of bedrooms at timesteps 10 and 100. The bottom section presents architectural images of buildings and cityscapes at timesteps 10 and 100. The y-axis labels for each section are 'sample timesteps'.

Figure 5: Samples from DDIM with the same random  $x_T$  and different number of steps.

quality are encoded in the parameters, as longer sample trajectories gives better quality samples but do not significantly affect the high-level features. We show more samples in Appendix [D.4.](#page-18-0)

5.3 INTERPOLATION IN DETERMINISTIC GENERATIVE PROCESSES

<span id="page-7-2"></span>Image /page/7/Picture/7 description: The image is a collage of three rows of images. The top row contains portraits of six people, three women and three men, with blurred backgrounds. The middle row contains six images of bedrooms, each with a bed, a dresser, and a window. The bottom row contains six images of buildings, some of which appear to be churches or cathedrals, with some showing signs of damage or ruin. Some of the images in the bottom row have watermarks from Shutterstock.

Figure 6: Interpolation of samples from DDIM with  $\dim(\tau) = 50$ .

Since the high level features of the DDIM sample is encoded by  $x_T$ , we are interested to see whether it would exhibit the semantic interpolation effect similar to that observed in other implicit proba-

<span id="page-8-2"></span>Table 2: Reconstruction error with DDIM on CIFAR-10 test set, rounded to  $10^{-4}$ .

| S     | 10      | 20       | 50       | 100      | 200      | 500      | 1000     |
|-------|---------|----------|----------|----------|----------|----------|----------|
| Error | $0.014$ | $0.0065$ | $0.0023$ | $0.0009$ | $0.0004$ | $0.0001$ | $0.0001$ |

bilistic models, such as GANs [\(Goodfellow et al.,](#page-9-0) [2014\)](#page-9-0). This is different from the interpolation procedure in [Ho et al.](#page-10-5) [\(2020\)](#page-10-5), since in DDPM the same  $x_T$  would lead to highly diverse  $x_0$  due to the stochastic generative process<sup>[6](#page-8-0)</sup>. In Figure [6,](#page-7-2) we show that simple interpolations in  $x_T$  can lead to semantically meaningful interpolations between two samples. We include more details and samples in Appendix [D.5.](#page-18-1) This allows DDIM to control the generated images on a high level directly through the latent variables, which DDPMs cannot.

### 5.4 RECONSTRUCTION FROM LATENT SPACE

As DDIM is the Euler integration for a particular ODE, it would be interesting to see whether it can encode from  $x_0$  to  $x_T$  (reverse of Eq. [\(14\)](#page-5-2)) and reconstruct  $x_0$  from the resulting  $x_T$  (forward of Eq.  $(14)$ <sup>[7](#page-8-1)</sup>. We consider encoding and decoding on the CIFAR-10 test set with the CIFAR-10 model with  $S$  steps for both encoding and decoding; we report the per-dimension mean squared error (scaled to  $[0, 1]$ ) in Table [2.](#page-8-2) Our results show that DDIMs have lower reconstruction error for larger S values and have properties similar to Neural ODEs and normalizing flows. The same cannot be said for DDPMs due to their stochastic nature.

## 6 RELATED WORK

Our work is based on a large family of existing methods on learning generative models as transition operators of Markov chains [\(Sohl-Dickstein et al.,](#page-10-6) [2015;](#page-10-6) [Bengio et al.,](#page-9-4) [2014;](#page-9-4) [Salimans et al.,](#page-10-12) [2014;](#page-10-12) [Song et al.,](#page-11-6) [2017;](#page-11-6) [Goyal et al.,](#page-9-6) [2017;](#page-9-6) [Levy et al.,](#page-10-13) [2017\)](#page-10-13). Among them, denoising diffusion probabilistic models (DDPMs, [Ho et al.](#page-10-5) [\(2020\)](#page-10-5)) and noise conditional score networks (NCSN, [Song](#page-11-3)  $&$  Ermon [\(2019;](#page-11-3) [2020\)](#page-11-7)) have recently achieved high sample quality comparable to GANs [\(Brock](#page-9-3) [et al.,](#page-9-3) [2018;](#page-9-3) [Karras et al.,](#page-10-4) [2018\)](#page-10-4). DDPMs optimize a variational lower bound to the log-likelihood, whereas NCSNs optimize the score matching objective  $(Hyvärinen, 2005)$  $(Hyvärinen, 2005)$  over a nonparametric Parzen density estimator of the data [\(Vincent,](#page-11-4) [2011;](#page-11-4) [Raphan & Simoncelli,](#page-10-14) [2011\)](#page-10-14).

Despite their different motivations, DDPMs and NCSNs are closely related. Both use a denoising autoencoder objective for many noise levels, and both use a procedure similar to Langevin dynamics to produce samples [\(Neal et al.,](#page-10-15) [2011\)](#page-10-15). Since Langevin dynamics is a discretization of a gradient flow [\(Jordan et al.,](#page-10-16) [1998\)](#page-10-16), both DDPM and NCSN require many steps to achieve good sample quality. This aligns with the observation that DDPM and existing NCSN methods have trouble generating high-quality samples in a few iterations.

DDIM, on the other hand, is an implicit generative model [\(Mohamed & Lakshminarayanan,](#page-10-7) [2016\)](#page-10-7) where samples are uniquely determined from the latent variables. Hence, DDIM has certain properties that resemble GANs [\(Goodfellow et al.,](#page-9-0) [2014\)](#page-9-0) and invertible flows [\(Dinh et al.,](#page-9-1) [2016\)](#page-9-1), such as the ability to produce semantically meaningful interpolations. We derive DDIM from a purely variational perspective, where the restrictions of Langevin dynamics are not relevant; this could partially explain why we are able to observe superior sample quality compared to DDPM under fewer iterations. The sampling procedure of DDIM is also reminiscent of neural networks with continuous depth [\(Chen et al.,](#page-9-7) [2018;](#page-9-7) [Grathwohl et al.,](#page-9-8) [2018\)](#page-9-8), since the samples it produces from the same latent variable have similar high-level visual features, regardless of the specific sample trajectory.

## 7 DISCUSSION

We have presented DDIMs – an implicit generative model trained with denoising auto-encoding / score matching objectives – from a purely variational perspective. DDIM is able to generate high-

<span id="page-8-0"></span><sup>&</sup>lt;sup>6</sup>Although it might be possible if one interpolates all T noises, like what is done in [Song & Ermon](#page-11-7) [\(2020\)](#page-11-7).

<span id="page-8-1"></span><sup>&</sup>lt;sup>7</sup>Since  $x_T$  and  $x_0$  have the same dimensions, their compression qualities are not our immediate concern.

quality samples much more efficiently than existing DDPMs and NCSNs, with the ability to perform meaningful interpolations from the latent space. The non-Markovian forward process presented here seems to suggest continuous forward processes other than Gaussian (which cannot be done in the original diffusion framework, since Gaussian is the only stable distribution with finite variance). We also demonstrated a discrete case with a multinomial forward process in Appendix [A,](#page-12-0) and it would be interesting to investigate similar alternatives for other combinatorial structures.

Moreover, since the sampling procedure of DDIMs is similar to that of an neural ODE, it would be interesting to see if methods that decrease the discretization error in ODEs, including multi-step methods such as Adams-Bashforth [\(Butcher & Goodwin,](#page-9-9) [2008\)](#page-9-9), could be helpful for further improving sample quality in fewer steps [\(Queiruga et al.,](#page-10-17) [2020\)](#page-10-17). It is also relevant to investigate whether DDIMs exhibit other properties of existing implicit models [\(Bau et al.,](#page-9-10) [2019\)](#page-9-10).

### ACKNOWLEDGEMENTS

The authors would like to thank Yang Song and Shengjia Zhao for helpful discussions over the ideas, Kuno Kim for reviewing an earlier draft of the paper, and Sharvil Nanavati and Sophie Liu for identifying typos. This research was supported by NSF (#1651565, #1522054, #1733686), ONR (N00014-19-1-2145), AFOSR (FA9550-19-1-0024), and Amazon AWS.

### REFERENCES

- <span id="page-9-2"></span>Martin Arjovsky, Soumith Chintala, and Léon Bottou. Wasserstein GAN. arXiv preprint *arXiv:1701.07875*, January 2017.
- <span id="page-9-10"></span>David Bau, Jun-Yan Zhu, Jonas Wulff, William Peebles, Hendrik Strobelt, Bolei Zhou, and Antonio Torralba. Seeing what a gan cannot generate. In *Proceedings of the IEEE International Conference on Computer Vision*, pp. 4502–4511, 2019.
- <span id="page-9-4"></span>Yoshua Bengio, Eric Laufer, Guillaume Alain, and Jason Yosinski. Deep generative stochastic networks trainable by backprop. In *International Conference on Machine Learning*, pp. 226–234, January 2014.
- <span id="page-9-11"></span>Christopher M Bishop. *Pattern recognition and machine learning*. springer, 2006.
- <span id="page-9-3"></span>Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale GAN training for high fidelity natural image synthesis. *arXiv preprint arXiv:1809.11096*, September 2018.
- <span id="page-9-9"></span>John Charles Butcher and Nicolette Goodwin. *Numerical methods for ordinary differential equations*, volume 2. Wiley Online Library, 2008.
- <span id="page-9-5"></span>Nanxin Chen, Yu Zhang, Heiga Zen, Ron J Weiss, Mohammad Norouzi, and William Chan. WaveGrad: Estimating gradients for waveform generation. *arXiv preprint arXiv:2009.00713*, September 2020.
- <span id="page-9-7"></span>Ricky T Q Chen, Yulia Rubanova, Jesse Bettencourt, and David Duvenaud. Neural ordinary differential equations. *arXiv preprint arXiv:1806.07366*, June 2018.
- <span id="page-9-1"></span>Laurent Dinh, Jascha Sohl-Dickstein, and Samy Bengio. Density estimation using real NVP. *arXiv preprint arXiv:1605.08803*, May 2016.
- <span id="page-9-0"></span>Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *Advances in neural information processing systems*, pp. 2672–2680, 2014.
- <span id="page-9-6"></span>Anirudh Goyal, Nan Rosemary Ke, Surya Ganguli, and Yoshua Bengio. Variational walkback: Learning a transition operator as a stochastic recurrent net. In *Advances in Neural Information Processing Systems*, pp. 4392–4402, 2017.
- <span id="page-9-8"></span>Will Grathwohl, Ricky T Q Chen, Jesse Bettencourt, Ilya Sutskever, and David Duvenaud. FFJORD: Free-form continuous dynamics for scalable reversible generative models. *arXiv preprint arXiv:1810.01367*, October 2018.

- <span id="page-10-3"></span>Ishaan Gulrajani, Faruk Ahmed, Martin Arjovsky, Vincent Dumoulin, and Aaron C Courville. Improved training of wasserstein gans. In *Advances in Neural Information Processing Systems*, pp. 5769–5779, 2017.
- <span id="page-10-10"></span>Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. GANs trained by a two Time-Scale update rule converge to a local nash equilibrium. *arXiv preprint arXiv:1706.08500*, June 2017.
- <span id="page-10-5"></span>Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. *arXiv preprint arXiv:2006.11239*, June 2020.
- <span id="page-10-9"></span>Aapo Hyvärinen. Estimation of Non-Normalized statistical models by score matching. *Journal of Machine Learning Researc h*, 6:695–709, 2005.
- <span id="page-10-11"></span>Alexia Jolicoeur-Martineau, Rémi Piché-Taillefer, Rémi Tachet des Combes, and Ioannis Mitliagkas. Adversarial score matching and improved sampling for image generation. September 2020.
- <span id="page-10-16"></span>Richard Jordan, David Kinderlehrer, and Felix Otto. The variational formulation of the fokker– planck equation. *SIAM journal on mathematical analysis*, 29(1):1–17, 1998.
- <span id="page-10-4"></span>Tero Karras, Samuli Laine, and Timo Aila. A Style-Based generator architecture for generative adversarial networks. *arXiv preprint arXiv:1812.04948*, December 2018.
- <span id="page-10-0"></span>Tero Karras, Samuli Laine, Miika Aittala, Janne Hellsten, Jaakko Lehtinen, and Timo Aila. Analyzing and improving the image quality of stylegan. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 8110–8119, 2020.
- <span id="page-10-1"></span>Diederik P Kingma and Max Welling. Auto-Encoding variational bayes. *arXiv preprint arXiv:1312.6114v10*, December 2013.
- <span id="page-10-13"></span>Daniel Levy, Matthew D Hoffman, and Jascha Sohl-Dickstein. Generalizing hamiltonian monte carlo with neural networks. *arXiv preprint arXiv:1711.09268*, 2017.
- <span id="page-10-7"></span>Shakir Mohamed and Balaji Lakshminarayanan. Learning in implicit generative models. *arXiv preprint arXiv:1610.03483*, October 2016.
- <span id="page-10-15"></span>Radford M Neal et al. Mcmc using hamiltonian dynamics. *Handbook of markov chain monte carlo*, 2(11):2, 2011.
- <span id="page-10-17"></span>Alejandro F Queiruga, N Benjamin Erichson, Dane Taylor, and Michael W Mahoney. Continuousin-depth neural networks. *arXiv preprint arXiv:2008.02389*, 2020.
- <span id="page-10-14"></span>Martin Raphan and Eero P Simoncelli. Least squares estimation without priors or supervision. *Neural computation*, 23(2):374–420, February 2011. ISSN 0899-7667, 1530-888X.
- <span id="page-10-2"></span>Danilo Jimenez Rezende and Shakir Mohamed. Variational inference with normalizing flows. *arXiv preprint arXiv:1505.05770*, May 2015.
- <span id="page-10-8"></span>Danilo Jimenez Rezende, Shakir Mohamed, and Daan Wierstra. Stochastic backpropagation and approximate inference in deep generative models. *arXiv preprint arXiv:1401.4082*, 2014.
- <span id="page-10-18"></span>Olaf Ronneberger, Philipp Fischer, and Thomas Brox. U-net: Convolutional networks for biomedical image segmentation. In *International Conference on Medical image computing and computerassisted intervention*, pp. 234–241. Springer, 2015.
- <span id="page-10-12"></span>Tim Salimans, Diederik P Kingma, and Max Welling. Markov chain monte carlo and variational inference: Bridging the gap. *arXiv preprint arXiv:1410.6460*, October 2014.
- <span id="page-10-19"></span>Ken Shoemake. Animating rotation with quaternion curves. In *Proceedings of the 12th annual conference on Computer graphics and interactive techniques*, pp. 245–254, 1985.
- <span id="page-10-6"></span>Jascha Sohl-Dickstein, Eric A Weiss, Niru Maheswaranathan, and Surya Ganguli. Deep unsupervised learning using nonequilibrium thermodynamics. *arXiv preprint arXiv:1503.03585*, March 2015.

- <span id="page-11-6"></span>Jiaming Song, Shengjia Zhao, and Stefano Ermon. A-nice-mc: Adversarial training for mcmc. *arXiv preprint arXiv:1706.07561*, June 2017.
- <span id="page-11-3"></span>Yang Song and Stefano Ermon. Generative modeling by estimating gradients of the data distribution. *arXiv preprint arXiv:1907.05600*, July 2019.
- <span id="page-11-7"></span>Yang Song and Stefano Ermon. Improved techniques for training Score-Based generative models. *arXiv preprint arXiv:2006.09011*, June 2020.
- <span id="page-11-5"></span>Yang Song, Jascha Sohl-Dickstein, Diederik P Kingma, Abhishek Kumar, Stefano Ermon, and Ben Poole. Score-based generative modeling through stochastic differential equations. *arXiv preprint arXiv:2011.13456*, 2020.
- <span id="page-11-0"></span>Aaron van den Oord, Sander Dieleman, Heiga Zen, Karen Simonyan, Oriol Vinyals, Alex Graves, Nal Kalchbrenner, Andrew Senior, and Koray Kavukcuoglu. WaveNet: A generative model for raw audio. *arXiv preprint arXiv:1609.03499*, September 2016a.
- <span id="page-11-1"></span>Aaron van den Oord, Nal Kalchbrenner, and Koray Kavukcuoglu. Pixel recurrent neural networks. *arXiv preprint arXiv:1601.06759*, January 2016b.
- <span id="page-11-4"></span>Pascal Vincent. A connection between score matching and denoising autoencoders. *Neural computation*, 23(7):1661–1674, 2011.
- <span id="page-11-8"></span>Sergey Zagoruyko and Nikos Komodakis. Wide residual networks. *arXiv preprint arXiv:1605.07146*, May 2016.
- <span id="page-11-2"></span>Shengjia Zhao, Hongyu Ren, Arianna Yuan, Jiaming Song, Noah Goodman, and Stefano Ermon. Bias and generalization in deep generative models: An empirical study. In *Advances in Neural Information Processing Systems*, pp. 10792–10801, 2018.

<span id="page-12-0"></span>

## A NON-MARKOVIAN FORWARD PROCESSES FOR A DISCRETE CASE

In this section, we describe a non-Markovian forward processes for discrete data and corresponding variational objectives. Since the focus of this paper is to accelerate reverse models corresponding to the Gaussian diffusion, we leave empirical evaluations as future work.

For a categorical observation  $x_0$  that is a one-hot vector with K possible values, we define the forward process as follows. First, we have  $q(x_t|x_0)$  as the following categorical distribution:

$$
q(\boldsymbol{x}_t|\boldsymbol{x}_0) = \text{Cat}(\alpha_t \boldsymbol{x}_0 + (1 - \alpha_t) \mathbf{1}_K)
$$
\n(17)

where  $\mathbf{1}_K \in \mathbb{R}^K$  is a vector with all entries being  $1/K$ , and  $\alpha_t$  decreasing from  $\alpha_0 = 1$  for  $t = 0$  to  $\alpha_T = 0$  for  $t = T$ . Then we define  $q(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0)$  as the following mixture distribution:

$$
q(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t,\boldsymbol{x}_0) = \begin{cases} \text{Cat}(\boldsymbol{x}_t) & \text{with probability } \sigma_t \\ \text{Cat}(\boldsymbol{x}_0) & \text{with probability } (\alpha_{t-1} - \sigma_t \alpha_t) \\ \text{Cat}(\boldsymbol{1}_K) & \text{with probability } (1 - \alpha_{t-1}) - (1 - \alpha_t)\sigma_t \end{cases},\tag{18}
$$

or equivalently:

$$
q(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t,\boldsymbol{x}_0) = \mathrm{Cat}\left(\sigma_t \boldsymbol{x}_t + (\alpha_{t-1} - \sigma_t \alpha_t) \boldsymbol{x}_0 + ((1 - \alpha_{t-1}) - (1 - \alpha_t) \sigma_t) \boldsymbol{1}_K\right),\tag{19}
$$

which is consistent with how we have defined  $q(x_t|x_0)$ .

Similarly, we can define our reverse process  $p_{\theta}(\mathbf{x}_{t-1}|\mathbf{x}_t)$  as:

$$
p_{\theta}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t) = \text{Cat}\left(\sigma_t \boldsymbol{x}_t + (\alpha_{t-1} - \sigma_t \alpha_t) f_{\theta}^{(t)}(\boldsymbol{x}_t) + ((1 - \alpha_{t-1}) - (1 - \alpha_t) \sigma_t) \mathbf{1}_K\right), \quad (20)
$$

where  $f_{\theta}^{(t)}$  $\psi_{\theta}^{(t)}(x_t)$  maps  $x_t$  to a K-dimensional vector. As  $(1-\alpha_{t-1}) - (1-\alpha_t)\sigma_t \to 0$ , the sampling process will become less stochastic, in the sense that it will either choose  $x_t$  or the predicted  $x_0$ with high probability. The KL divergence

$$
D_{\mathrm{KL}}(q(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t,\boldsymbol{x}_0)||p_{\theta}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t))
$$
\n(21)

is well-defined, and is simply the KL divergence between two categoricals. Therefore, the resulting variational objective function should be easy to optimize as well. Moreover, as KL divergence is convex, we have this upper bound (which is tight when the right hand side goes to zero):

$$
D_{\mathrm{KL}}(q(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t},\boldsymbol{x}_{0})\|p_{\theta}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t})) \leq (\alpha_{t-1} - \sigma_{t}\alpha_{t})D_{\mathrm{KL}}(\mathrm{Cat}(\boldsymbol{x}_{0})\|\mathrm{Cat}(f^{(t)}_{\theta}(\boldsymbol{x}_{t}))).
$$

The right hand side is simply a multi-class classification loss (up to constants), so we can arrive at similar arguments regarding how changes in  $\sigma_t$  do not affect the objective (up to re-weighting).

<span id="page-12-2"></span>

### B PROOFS

<span id="page-12-1"></span>**Lemma 1.** For 
$$
q_{\sigma}(\mathbf{x}_{1:T}|\mathbf{x}_0)
$$
 defined in Eq. (6) and  $q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0)$  defined in Eq. (7), we have:

$$
q_{\sigma}(\boldsymbol{x}_t|\boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_t}\boldsymbol{x}_0, (1-\alpha_t)\boldsymbol{I})
$$
\n(22)

*Proof.* Assume for any  $t \leq T$ ,  $q_{\sigma}(\boldsymbol{x}_t|\boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_t}\boldsymbol{x}_0, (1-\alpha_t)\boldsymbol{I})$  holds, if:

$$
q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_{t-1}}\boldsymbol{x}_0, (1-\alpha_{t-1})\boldsymbol{I})
$$
\n(23)

then we can prove the statement with an induction argument for  $t$  from  $T$  to 1, since the base case  $(t = T)$  already holds.

First, we have that

$$
q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_0) := \int_{\boldsymbol{x}_t} q_{\sigma}(\boldsymbol{x}_t|\boldsymbol{x}_0) q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t,\boldsymbol{x}_0) d\boldsymbol{x}_t
$$

and

$$
q_{\sigma}(\boldsymbol{x}_t|\boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_t}\boldsymbol{x}_0, (1-\alpha_t)\boldsymbol{I})
$$
\n(24)

$$
q_{\sigma}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t},\boldsymbol{x}_{0})=\mathcal{N}\left(\sqrt{\alpha_{t-1}}\boldsymbol{x}_{0}+\sqrt{1-\alpha_{t-1}-\sigma_{t}^{2}}\cdot\frac{\boldsymbol{x}_{t}-\sqrt{\alpha_{t}}\boldsymbol{x}_{0}}{\sqrt{1-\alpha_{t}}},\sigma_{t}^{2}\boldsymbol{I}\right).
$$
 (25)

From [Bishop](#page-9-11) [\(2006\)](#page-9-11) (2.115), we have that  $q_{\sigma}(x_{t-1}|x_0)$  is Gaussian, denoted as  $\mathcal{N}(\mu_{t-1}, \Sigma_{t-1})$ where √ √

$$
\mu_{t-1} = \sqrt{\alpha_{t-1}} \boldsymbol{x}_0 + \sqrt{1 - \alpha_{t-1} - \sigma_t^2} \cdot \frac{\sqrt{\alpha_t} \boldsymbol{x}_0 - \sqrt{\alpha_t} \boldsymbol{x}_0}{\sqrt{1 - \alpha_t}}
$$
(26)

$$
=\sqrt{\alpha_{t-1}}x_0\tag{27}
$$

and

$$
\Sigma_{t-1} = \sigma_t^2 \mathbf{I} + \frac{1 - \alpha_{t-1} - \sigma_t^2}{1 - \alpha_t} (1 - \alpha_t) \mathbf{I} = (1 - \alpha_{t-1}) \mathbf{I}
$$
 (28)

Therefore,  $q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_0) = \mathcal{N}(\sqrt{\alpha_{t-1}}\mathbf{x}_0, (1-\alpha_{t-1})\mathbf{I})$ , which allows us to apply the induction argument.  $\Box$ 

**Theorem 1.** For all  $\sigma > 0$ , there exists  $\gamma \in \mathbb{R}_{>0}^T$  and  $C \in \mathbb{R}$ , such that  $J_{\sigma} = L_{\gamma} + C$ .

*Proof.* From the definition of  $J_{\sigma}$ :

$$
J_{\sigma}(\epsilon_{\theta}) := \mathbb{E}_{x_{0:T} \sim q(x_{0:T})} \left[ \log q_{\sigma}(x_{T}|x_{0}) + \sum_{t=2}^{T} \log q_{\sigma}(x_{t-1}|x_{t}, x_{0}) - \sum_{t=1}^{T} \log p_{\theta}^{(t)}(x_{t-1}|x_{t}) \right] (29)
$$

$$
\equiv \mathbb{E}_{x_{0:T} \sim q(x_{0:T})} \left[ \sum_{t=2}^{T} D_{\mathrm{KL}}(q_{\sigma}(x_{t-1}|x_{t}, x_{0}) || p_{\theta}^{(t)}(x_{t-1}|x_{t})) - \log p_{\theta}^{(1)}(x_{0}|x_{1}) \right]
$$

where we use  $\equiv$  to denote "equal up to a value that does not depend on  $\epsilon_{\theta}$  (but may depend on  $q_{\sigma}$ )". For  $t > 1$ :

 $\mathbb{E}_{\mathbf{x}_0, \mathbf{x}_t \sim q(\mathbf{x}_0, \mathbf{x}_t)} [D_{\mathrm{KL}}(q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0)) || p_{\theta}^{(t)}(\mathbf{x}_{t-1}|\mathbf{x}_t))]$  $= \mathbb{E}_{\mathbf{x}_0, \mathbf{x}_t \sim q(\mathbf{x}_0, \mathbf{x}_t)} [D_{\mathrm{KL}}(q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, \mathbf{x}_0)) || q_{\sigma}(\mathbf{x}_{t-1}|\mathbf{x}_t, f_{\theta}^{(t)}(\mathbf{x}_t)) )]$  $\equiv \mathbb{E}_{\mathbf{x}_0, \mathbf{x}_t \sim q(\mathbf{x}_0, \mathbf{x}_t)} \left[ \frac{\left\| \mathbf{x}_0 - f_{\theta}^{(t)}(\mathbf{x}_t) \right\|_2^2}{2\sigma_t^2} \right]$ (30)

$$
= \mathbb{E}_{\boldsymbol{x}_0 \sim q(\boldsymbol{x}_0), \epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I}), \boldsymbol{x}_t = \sqrt{\alpha_t} \boldsymbol{x}_0 + \sqrt{1 - \alpha_t} \epsilon} \left[ \frac{\left\| \frac{(\boldsymbol{x}_t - \sqrt{1 - \alpha_t} \epsilon)}{\sqrt{\alpha_t}} - \frac{(\boldsymbol{x}_t - \sqrt{1 - \alpha_t} \epsilon_t^{(t)}(\boldsymbol{x}_t))}{\sqrt{\alpha_t}} \right\|_2^2}{2\sigma_t^2} \right]
$$
(31)

$$
= \mathbb{E}_{\boldsymbol{x}_0 \sim q(\boldsymbol{x}_0), \epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I}), \boldsymbol{x}_t = \sqrt{\alpha_t} \boldsymbol{x}_0 + \sqrt{1 - \alpha_t} \epsilon} \left[ \frac{\left\| \epsilon - \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t) \right\|_2^2}{2 d \sigma_t^2 \alpha_t} \right]
$$
(32)

where d is the dimension of  $x_0$ . For  $t = 1$ :

$$
\mathbb{E}_{\mathbf{x}_0, \mathbf{x}_1 \sim q(\mathbf{x}_0, \mathbf{x}_1)} \left[ -\log p_{\theta}^{(1)}(\mathbf{x}_0 | \mathbf{x}_1) \right] \equiv \mathbb{E}_{\mathbf{x}_0, \mathbf{x}_1 \sim q(\mathbf{x}_0, \mathbf{x}_1)} \left[ \frac{\left\| \mathbf{x}_0 - f_{\theta}^{(t)}(\mathbf{x}_1) \right\|_2^2}{2\sigma_1^2} \right] \tag{33}
$$

$$
= \mathbb{E}_{\boldsymbol{x}_0 \sim q(\boldsymbol{x}_0), \epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I}), \boldsymbol{x}_1 = \sqrt{\alpha_1} \boldsymbol{x}_0 + \sqrt{1 - \alpha_t} \epsilon} \left[ \frac{\left\| \epsilon - \epsilon_{\theta}^{(1)}(\boldsymbol{x}_1) \right\|_2^2}{2d\sigma_1^2 \alpha_1} \right]
$$
(34)

Therefore, when  $\gamma_t = 1/(2d\sigma_t^2 \alpha_t)$  for all  $t \in \{1, ..., T\}$ , we have

$$
J_{\sigma}(\epsilon_{\theta}) \equiv \sum_{t=1}^{T} \frac{1}{2d\sigma_t^2 \alpha_t} \mathbb{E}\left[\left\|\epsilon_{\theta}^{(t)}(\boldsymbol{x}_t) - \epsilon_t\right\|_2^2\right] = L_{\gamma}(\epsilon_{\theta})
$$
(35)

 $\Box$ 

for all  $\epsilon_{\theta}$ . From the definition of "≡", we have that  $J_{\sigma} = L_{\gamma} + C$ .

**Proposition 1.** The ODE in Eq. [\(14\)](#page-5-2) with the optimal model  $\epsilon_{\theta}^{(t)}$ θ *has an equivalent probability flow ODE corresponding to the "Variance-Exploding" SDE in [Song et al.](#page-11-5) [\(2020\)](#page-11-5).*

*Proof.* In the context of the proof, we consider t as a continous, independent "time" variable and  $x$ and  $\alpha$  as functions of t. First, let us consider a reparametrization between DDIM and the VE-SDE<sup>[8](#page-14-0)</sup> by introducing the variables  $\bar{x}$  and  $\sigma$ :

$$
\bar{x}(t) = \bar{x}(0) + \sigma(t)\epsilon, \quad \epsilon \sim \mathcal{N}(0, \mathbf{I}), \tag{36}
$$

for  $t \in [0, \infty)$  and an increasing continuous function  $\sigma : \mathbb{R}_{\geq 0} \to \mathbb{R}_{\geq 0}$  where  $\sigma(0) = 0$ .

We can then define  $\alpha(t)$  and  $\alpha(t)$  corresponding to DDIM case as:

$$
\bar{x}(t) = \frac{x(t)}{\sqrt{\alpha(t)}}\tag{37}
$$

$$
\sigma(t) = \sqrt{\frac{1 - \alpha(t)}{\alpha(t)}}.
$$
\n(38)

This also means that:

$$
\boldsymbol{x}(t) = \frac{\bar{\boldsymbol{x}}(t)}{\sqrt{\sigma^2(t) + 1}}\tag{39}
$$

$$
\alpha(t) = \frac{1}{1 + \sigma^2(t)},\tag{40}
$$

which establishes an bijection between  $(x, \alpha)$  and  $(\bar{x}, \sigma)$ . From Equation [\(4\)](#page-2-4) we have (note that  $\alpha(0) = 1$ :

$$
\frac{\boldsymbol{x}(t)}{\sqrt{\alpha(t)}} = \frac{\boldsymbol{x}(0)}{\sqrt{\alpha(0)}} + \sqrt{\frac{1 - \alpha(t)}{\alpha(t)}} \epsilon, \quad \epsilon \sim \mathcal{N}(0, \boldsymbol{I})
$$
(41)

which can be reparametrized into a form that is consistent with VE-SDE:

$$
\bar{x}(t) = \bar{x}(0) + \sigma(t)\epsilon.
$$
 (42)

Now, we derive the ODE forms for both DDIM and VE-SDE and show that they are equivalent.

### ODE form for DDIM We repeat Equation [\(13\)](#page-5-1) here:

$$
\frac{\boldsymbol{x}_{t-\Delta t}}{\sqrt{\alpha_{t-\Delta t}}} = \frac{\boldsymbol{x}_t}{\sqrt{\alpha_t}} + \left( \sqrt{\frac{1-\alpha_{t-\Delta t}}{\alpha_{t-\Delta t}}} - \sqrt{\frac{1-\alpha_t}{\alpha_t}} \right) \epsilon_{\theta}^{(t)}(\boldsymbol{x}_t), \tag{43}
$$

which is equivalent to:

$$
\bar{\boldsymbol{x}}(t - \Delta t) = \bar{\boldsymbol{x}}(t) + (\sigma(t - \Delta t) - \sigma(t)) \cdot \epsilon_{\theta}^{(t)}(\boldsymbol{x}(t))
$$
\n(44)

Divide both sides by  $(-\Delta t)$  and as  $\Delta t \rightarrow 0$ , we have:

<span id="page-14-2"></span><span id="page-14-1"></span>
$$
\frac{\mathrm{d}\bar{\mathbf{x}}(t)}{\mathrm{d}t} = \frac{\mathrm{d}\sigma(t)}{\mathrm{d}t} \epsilon_{\theta}^{(t)} \left( \frac{\bar{\mathbf{x}}(t)}{\sqrt{\sigma^2(t) + 1}} \right),\tag{45}
$$

which is exactly what we have in Equation  $(14)$ .

We note that for the optimal model,  $\epsilon_{\theta}^{(t)}$  $\theta$ <sup>(*i*</sup>) is a minimizer:

$$
\epsilon_{\theta}^{(t)} = \underset{f_t}{\arg\min} \mathbb{E}_{\mathbf{x}(0) \sim q(\mathbf{x}), \epsilon \sim \mathcal{N}(0, I)} [\|f_t(\mathbf{x}(t)) - \epsilon\|_2^2]
$$
(46)

where  $\mathbf{x}(t) = \sqrt{\alpha(t)}\mathbf{x}(t) + \sqrt{1 - \alpha(t)}\epsilon$ .

<span id="page-14-0"></span> ${}^{8}$ Refer to [\(Song et al.,](#page-11-5) [2020\)](#page-11-5) for more details of VE-SDE.

**ODE form for VE-SDE** Define  $p_t(\bar{x})$  as the data distribution perturbed with  $\sigma^2(t)$  variance Gaus-sian noise. The probability flow for VE-SDE is defined as [Song et al.](#page-11-5) [\(2020\)](#page-11-5):

$$
\mathrm{d}\bar{x} = -\frac{1}{2}g(t)^2 \nabla_{\bar{x}} \log p_t(\bar{x}) \mathrm{d}t \tag{47}
$$

where  $g(t) = \sqrt{\frac{d\sigma^2(t)}{dt}}$  $\frac{d\mathcal{L}(t)}{dt}$  is the diffusion coefficient, and  $\nabla_{\bar{\bm{x}}} \log p_t(\bar{\bm{x}})$  is the score of  $p_t$ .

The  $\sigma(t)$ -perturbed score function  $\nabla_{\bar{x}} \log p_t(\bar{x})$  is also a minimizer (from denoising score matching [\(Vincent,](#page-11-4) [2011\)](#page-11-4)):

$$
\nabla_{\bar{\boldsymbol{x}}} \log p_t = \underset{g_t}{\arg\min} \mathbb{E}_{\boldsymbol{x}(0) \sim q(\boldsymbol{x}), \epsilon \sim \mathcal{N}(0, \boldsymbol{I})} [\|g_t(\bar{\boldsymbol{x}}) + \epsilon/\sigma(t)\|_2^2]
$$
(48)

<span id="page-15-3"></span><span id="page-15-2"></span><span id="page-15-1"></span> $\lambda$ 

where  $\bar{x}(t) = \bar{x}(t) + \sigma(t)\epsilon$ .

Since there is an equivalence between  $x(t)$  and  $\bar{x}(t)$ , we have the following relationship:

$$
\nabla_{\bar{\mathbf{x}}} \log p_t(\bar{\mathbf{x}}) = -\frac{\epsilon_{\theta}^{(t)} \left( \frac{\bar{\mathbf{x}}(t)}{\sqrt{\sigma^2(t) + 1}} \right)}{\sigma(t)}
$$
(49)

from Equation [\(46\)](#page-14-1) and Equation [\(48\)](#page-15-1). Plug Equation [\(49\)](#page-15-2) and definition of  $g(t)$  in Equation [\(47\)](#page-15-3), we have:

$$
\mathrm{d}\bar{\mathbf{x}}(t) = \frac{1}{2} \frac{\mathrm{d}\sigma^2(t)}{\mathrm{d}t} \frac{\epsilon_\theta^{(t)} \left(\frac{\bar{\mathbf{x}}(t)}{\sqrt{\sigma^2(t) + 1}}\right)}{\sigma(t)} \mathrm{d}t,\tag{50}
$$

and we have the following by rearranging terms:

$$
\frac{\mathrm{d}\bar{\mathbf{x}}(t)}{\mathrm{d}t} = \frac{\mathrm{d}\sigma(t)}{\mathrm{d}t} \epsilon_{\theta}^{(t)} \left( \frac{\bar{\mathbf{x}}(t)}{\sqrt{\sigma^2(t) + 1}} \right)
$$
(51)

which is equivalent to Equation [\(45\)](#page-14-2). In both cases the initial conditions are  $\bar{x}(T) \sim \mathcal{N}(0, \sigma^2(T)I)$ , so the resulting ODEs are identical.

## C ADDITIONAL DERIVATIONS

<span id="page-15-0"></span>

#### C.1 ACCELERATED SAMPLING PROCESSES

In the accelerated case, we can consider the inference process to be factored as:

$$
q_{\sigma,\tau}(\boldsymbol{x}_{1:T}|\boldsymbol{x}_0) = q_{\sigma,\tau}(\boldsymbol{x}_{\tau_S}|\boldsymbol{x}_0) \prod_{i=1}^S q_{\sigma,\tau}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i},\boldsymbol{x}_0) \prod_{t \in \bar{\tau}} q_{\sigma,\tau}(\boldsymbol{x}_t|\boldsymbol{x}_0)
$$
(52)

where  $\tau$  is a sub-sequence of  $[1, \ldots, T]$  of length S with  $\tau_{S} = T$ , and let  $\bar{\tau} := \{1, \ldots, T\} \setminus \tau$ be its complement. Intuitively, the graphical model of  $\{x_{\tau_i}\}_{i=1}^S$  and  $x_0$  form a chain, whereas the graphical model of  $\{x_t\}_{t\in\tau}$  and  $x_0$  forms a star graph. We define:

$$
q_{\sigma,\tau}(\boldsymbol{x}_t|\boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_t}\boldsymbol{x}_0, (1-\alpha_t)\boldsymbol{I}) \quad \forall t \in \bar{\tau} \cup \{T\} \tag{53}
$$
\n
$$
q_{\sigma,\tau}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i},\boldsymbol{x}_0) = \mathcal{N}\left(\sqrt{\alpha_{\tau_{i-1}}}\boldsymbol{x}_0 + \sqrt{1-\alpha_{\tau_{i-1}}-\sigma_{\tau_i}^2} \cdot \frac{\boldsymbol{x}_{\tau_i} - \sqrt{\alpha_{\tau_i}}\boldsymbol{x}_0}{\sqrt{1-\alpha_{\tau_i}}}, \sigma_{\tau_i}^2\boldsymbol{I}\right) \ \forall i \in [S]
$$

where the coefficients are chosen such that:

$$
q_{\sigma,\tau}(\boldsymbol{x}_{\tau_i}|\boldsymbol{x}_0) = \mathcal{N}(\sqrt{\alpha_{\tau_i}}\boldsymbol{x}_0, (1-\alpha_{\tau_i})\boldsymbol{I}) \quad \forall i \in [S]
$$
\n(54)

i.e., the "marginals" match.

The corresponding "generative process" is defined as:

$$
p_{\theta}(\boldsymbol{x}_{0:T}) := p_{\theta}(\boldsymbol{x}_{T}) \prod_{i=1}^{S} p_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i}) \times \prod_{t \in \bar{\tau}} p_{\theta}^{(t)}(\boldsymbol{x}_0|\boldsymbol{x}_t)
$$
(55)

use to produce samples

in variational objective

where only part of the models are actually being used to produce samples. The conditionals are:

$$
p_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i}) = q_{\sigma,\tau}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i},f_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_{i-1}})) \quad \text{if } i \in [S], i > 1 \tag{56}
$$

$$
p_{\theta}^{(t)}(\boldsymbol{x}_0|\boldsymbol{x}_t) = \mathcal{N}(f_{\theta}^{(t)}(\boldsymbol{x}_t), \sigma_t^2 \boldsymbol{I}) \quad \text{otherwise},
$$
\n(57)

where we leverage  $q_{\sigma,\tau}(\bm x_{\tau_{i-1}}|\bm x_{\tau_i},\bm x_0)$  as part of the inference process (similar to what we have done in Section [3\)](#page-2-0). The resulting variational objective becomes (define  $x_{\tau_{L+1}} = \emptyset$  for conciseness):

$$
J(\epsilon_{\theta}) = \mathbb{E}_{\boldsymbol{x}_{0:T} \sim q_{\sigma,\tau}(\boldsymbol{x}_{0:T})}[\log q_{\sigma,\tau}(\boldsymbol{x}_{1:T}|\boldsymbol{x}_0) - \log p_{\theta}(\boldsymbol{x}_{0:T})] \tag{58}
$$

$$
= \mathbb{E}_{\boldsymbol{x}_{0:T} \sim q_{\sigma,\tau}(\boldsymbol{x}_{0:T})} \Bigg[ \sum_{t \in \bar{\tau}} D_{\mathrm{KL}}(q_{\sigma,\tau}(\boldsymbol{x}_t|\boldsymbol{x}_0) || p_{\theta}^{(t)}(\boldsymbol{x}_0|\boldsymbol{x}_t)) + \sum_{i=1}^{L} D_{\mathrm{KL}}(q_{\sigma,\tau}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i},\boldsymbol{x}_0) || p_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_{i-1}}|\boldsymbol{x}_{\tau_i}))) \Bigg] (59)
$$

where each KL divergence is between two Gaussians with variance independent of  $\theta$ . A similar argument to the proof used in Theorem  $1$  can show that the variational objective  $J$  can also be converted to an objective of the form  $L_{\gamma}$ .

<span id="page-16-0"></span>

### C.2 DERIVATION OF DENOISING OBJECTIVES FOR DDPMS

We note that in [Ho et al.](#page-10-5) [\(2020\)](#page-10-5), a diffusion hyperparameter  $\beta_t^9$  $\beta_t^9$  is first introduced, and then relevant variables  $\alpha_t := 1 - \beta_t$  and  $\bar{\alpha}_t = \prod_{t=1}^T \alpha_t$  are defined. In this paper, we have used the notation  $\alpha_t$  to represent the variable  $\bar{\alpha}_t$  in H<sub>0</sub> et al. [\(2020\)](#page-10-5) for three reasons. First, it makes it more clear that we only need to choose one set of hyperparameters, reducing possible cross-references of the derived variables. Second, it allows us to introduce the generalization as well as the acceleration case easier, because the inference process is no longer motivated by a diffusion. Third, there exists an isomorphism between  $\alpha_{1:T}$  and  $1, \ldots, T$ , which is not the case for  $\beta_t$ .

In this section, we use  $\beta_t$  and  $\alpha_t$  to be more consistent with the derivation in H<sub>o</sub> et al. [\(2020\)](#page-10-5), where

$$
\alpha_t = \frac{\alpha_t}{\alpha_{t-1}}\tag{60}
$$

$$
\beta_t = 1 - \frac{\alpha_t}{\alpha_{t-1}} \tag{61}
$$

can be uniquely determined from  $\alpha_t$  (i.e.  $\bar{\alpha}_t$ ).

First, from the diffusion forward process:

$$
q(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t},\boldsymbol{x}_{0})=\mathcal{N}\Bigg(\underbrace{\frac{\sqrt{\alpha_{t-1}}\beta_{t}}{1-\alpha_{t}}\boldsymbol{x}_{0}+\frac{\sqrt{\alpha_{t}}(1-\alpha_{t-1})}{1-\alpha_{t}}\boldsymbol{x}_{t}}_{\tilde{\mu}(\boldsymbol{x}_{t},\boldsymbol{x}_{0})},\frac{1-\alpha_{t-1}}{1-\alpha_{t}}\beta_{t}\boldsymbol{I}\Bigg)
$$

[Ho et al.](#page-10-5) [\(2020\)](#page-10-5) considered a specific type of  $p_{\theta}^{(t)}$  $_{\theta}^{(t)}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_{t})$ :

$$
p_{\theta}^{(t)}(\boldsymbol{x}_{t-1}|\boldsymbol{x}_t) = \mathcal{N}\left(\mu_{\theta}(\boldsymbol{x}_t, t), \sigma_t \boldsymbol{I}\right)
$$
\n(62)

which leads to the following variational objective:

$$
L := \mathbb{E}_{\mathbf{x}_0: T \sim q(\mathbf{x}_0: T)} \left[ q(\mathbf{x}_T | \mathbf{x}_0) + \sum_{t=2}^T \log q(\mathbf{x}_{t-1} | \mathbf{x}_t, \mathbf{x}_0) - \sum_{t=1}^T \log p_{\theta}^{(t)}(\mathbf{x}_{t-1} | \mathbf{x}_t) \right]
$$
(63)  
$$
\equiv \mathbb{E}_{\mathbf{x}_0: T \sim q(\mathbf{x}_0: T)} \left[ \sum_{t=2}^T \underbrace{D_{\text{KL}}(q(\mathbf{x}_{t-1} | \mathbf{x}_t, \mathbf{x}_0)) || p_{\theta}^{(t)}(\mathbf{x}_{t-1} | \mathbf{x}_t))}_{L_{t-1}} - \log p_{\theta}^{(1)}(\mathbf{x}_0 | \mathbf{x}_1) \right]
$$

<span id="page-16-1"></span> $9^9$ In this section we use teal to color notations used in [Ho et al.](#page-10-5) [\(2020\)](#page-10-5).

One can write:

$$
L_{t-1} = \mathbb{E}_q \left[ \frac{1}{2\sigma_t^2} ||\mu_\theta(x_t, t) - \tilde{\mu}(x_t, x_0)||_2^2 \right]
$$
(64)

[Ho et al.](#page-10-5) [\(2020\)](#page-10-5) chose the parametrization

$$
\mu_{\theta}(x_t, t) = \frac{1}{\sqrt{\alpha_t}} \left( \boldsymbol{x}_t - \frac{\beta_t}{\sqrt{1 - \alpha_t}} \epsilon_{\theta}(x_t, t) \right)
$$
(65)

which can be simplified to:

$$
L_{t-1} = \mathbb{E}_{\boldsymbol{x}_0, \epsilon} \left[ \frac{\beta_t^2}{2\sigma_t^2 (1 - \alpha_t)\alpha_t} \left\| \epsilon - \epsilon_\theta (\sqrt{\alpha_t} \boldsymbol{x}_0 + \sqrt{1 - \alpha_t} \epsilon, t) \right\|_2^2 \right]
$$
(66)

<span id="page-17-0"></span>

### D EXPERIMENTAL DETAILS

### D.1 DATASETS AND ARCHITECTURES

We consider 4 image datasets with various resolutions: CIFAR10 ( $32 \times 32$ , unconditional), CelebA  $(64 \times 64)$ , LSUN Bedroom  $(256 \times 256)$  and LSUN Church  $(256 \times 256)$ . For all datasets, we set the hyperparameters  $\alpha$  according to the heuristic in [\(Ho et al.,](#page-10-5) [2020\)](#page-10-5) to make the results directly comparable. We use the same model for each dataset, and only compare the performance of different generative processes. For CIFAR10, Bedroom and Church, we obtain the pretrained checkpoints from the original DDPM implementation; for CelebA, we trained our own model using the denoising objective  $L_1$ .

Our architecture for  $\epsilon_{\theta}^{(t)}$  $\theta_{\theta}^{(t)}(x_t)$  follows that in [Ho et al.](#page-10-5) [\(2020\)](#page-10-5), which is a U-Net [\(Ronneberger et al.,](#page-10-18)  $2015$ ) based on a Wide ResNet [\(Zagoruyko & Komodakis,](#page-11-8) [2016\)](#page-11-8). We use the pretrained models from [Ho et al.](#page-10-5) [\(2020\)](#page-10-5) for CIFAR10, Bedroom and Church, and train our own model for the CelebA  $64 \times 64$  model (since a pretrained model is not provided). Our CelebA model has five feature map resolutions from  $64 \times 64$  to  $4 \times 4$ , and we use the original CelebA dataset (not CelebA-HQ) using the [pre-processing technique](https://github.com/NVlabs/stylegan/blob/master/dataset_tool.py#L484-L499) from the StyleGAN [\(Karras et al.,](#page-10-4) [2018\)](#page-10-4) repository.

Table 3: LSUN Bedroom and Church image generation results, measured in FID. For 1000 steps DDPM, the FIDs are 6.36 for Bedroom and 7.89 for Church.

|                     | Bedroom $(256 \times 256)$ |       |       |      | Church $(256 \times 256)$ |       |       |       |
|---------------------|----------------------------|-------|-------|------|---------------------------|-------|-------|-------|
| dim( $\tau$ )       | 10                         | 20    | 50    | 100  | 10                        | 20    | 50    | 100   |
| DDIM $(\eta = 0.0)$ | 16.95                      | 8.89  | 6.75  | 6.62 | 19.45                     | 12.47 | 10.84 | 10.58 |
| DDPM $(\eta = 1.0)$ | 42.78                      | 22.77 | 10.81 | 6.81 | 51.56                     | 23.37 | 11.16 | 8.27  |

#### D.2 REVERSE PROCESS SUB-SEQUENCE SELECTION

We consider two types of selection procedure for  $\tau$  given the desired  $\dim(\tau) < T$ :

- Linear: we select the timesteps such that  $\tau_i = |ci|$  for some c;
- Quadratic: we select the timesteps such that  $\tau_i = \lfloor c i^2 \rfloor$  for some c.

The constant value c is selected such that  $\tau_{-1}$  is close to T. We used *quadratic* for CIFAR10 and *linear* for the remaining datasets. These choices achieve slightly better FID than their alternatives in the respective datasets.

#### D.3 CLOSED FORM EQUATIONS FOR EACH SAMPLING STEP

From the general sampling equation in Eq.  $(12)$ , we have the following update equation:

 $(1)$ 

$$
\boldsymbol{x}_{\tau_{i-1}}(\eta) = \sqrt{\alpha_{\tau_{i-1}}} \left( \frac{\boldsymbol{x}_{\tau_i} - \sqrt{1-\alpha_{\tau_i}} \epsilon_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_i})}{\sqrt{\alpha_{\tau_i}}} \right) + \sqrt{1-\alpha_{\tau_{i-1}}-\sigma_{\tau_i}(\eta)^2} \cdot \epsilon_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_i}) + \sigma_{\tau_i}(\eta) \epsilon
$$

<span id="page-18-2"></span>Image /page/18/Figure/1 description: The image displays a grid of 30 images, arranged in three rows and ten columns. Each image is a small square depicting various objects and animals. The top row features a variety of subjects including a dog, a deer, a car, a frog, a truck, a deer, a dog, a bird, a boat with a person, and a car. The middle row contains images of a truck, a bird, a plane, a cat, a ship, a horse, a dog, a car, a boat, and a bird. The bottom row shows a truck, a bird, a ship, a truck, a horse, a dog, a car, a boat, and a bird. The overall arrangement suggests a collection of diverse visual samples.

Figure 7: CIFAR10 samples from 1000 step DDPM, 1000 step DDIM and 100 step DDIM.

where

$$
\sigma_{\tau_i}(\eta) = \eta \sqrt{\frac{1 - \alpha_{\tau_{i-1}}}{1 - \alpha_{\tau_i}}} \sqrt{1 - \frac{\alpha_{\tau_i}}{\alpha_{\tau_{i-1}}}}
$$

For the case of  $\hat{\sigma}$  (DDPM with a larger variance), the update equation becomes:

$$
\boldsymbol{x}_{\tau_{i-1}} = \sqrt{\alpha_{\tau_{i-1}}} \left(\frac{\boldsymbol{x}_{\tau_i} - \sqrt{1-\alpha_{\tau_i}} \epsilon_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_i})}{\sqrt{\alpha_{\tau_i}}}\right) + \sqrt{1-\alpha_{\tau_{i-1}}-\sigma_{\tau_i}(1)^2} \cdot \epsilon_{\theta}^{(\tau_i)}(\boldsymbol{x}_{\tau_i}) + \hat{\sigma}_{\tau_i} \epsilon
$$

which uses a different coefficient for  $\epsilon$  compared with the update for  $\eta = 1$ , but uses the same coefficient for the non-stochastic parts. This update is more stochastic than the update for  $\eta = 1$ , which explains why it achieves worse performance when  $\dim(\tau)$  is small.

<span id="page-18-0"></span>

### D.4 SAMPLES AND CONSISTENCY

We show more samples in Figure [7](#page-18-2) (CIFAR10), Figure [8](#page-19-0) (CelebA), Figure [10](#page-19-1) (Church) and consis-tency results of DDIM in Figure [9](#page-19-2) (CelebA).

<span id="page-18-1"></span>

#### D.5 INTERPOLATION

To generate interpolations on a line, we randomly sample two initial  $x<sub>T</sub>$  values from the standard Gaussian, interpolate them with spherical linear interpolation [\(Shoemake,](#page-10-19) [1985\)](#page-10-19), and then use the DDIM to obtain  $x_0$  samples.

$$
\boldsymbol{x}_T^{(\alpha)} = \frac{\sin((1-\alpha)\theta)}{\sin(\theta)} \boldsymbol{x}_T^{(0)} + \frac{\sin(\alpha\theta)}{\sin(\theta)} \boldsymbol{x}_T^{(1)} \tag{67}
$$

where  $\theta = \arccos \left( \frac{(\mathbf{x}_T^{(0)})^\top \mathbf{x}_T^{(1)}}{\|\mathbf{x}_T^{(0)}\| \|\mathbf{x}_T^{(1)}\|} \right)$  . These values are used to produce DDIM samples.

To generate interpolations on a grid, we sample four latent variables and separate them in to two pairs; then we use slerp with the pairs under the same  $\alpha$ , and use slerp over the interpolated samples across the pairs (under an independently chosen interpolation coefficient). We show more grid interpolation results in Figure [11](#page-20-0) (CelebA), Figure [12](#page-20-1) (Bedroom), and Figure [13](#page-21-0) (Church).

<span id="page-19-0"></span>Image /page/19/Picture/1 description: The image displays a grid of celebrity headshots arranged in three rows and six columns. Each row contains six distinct portraits of individuals, likely actors or public figures. The arrangement suggests a collection of faces, possibly for identification or comparison purposes.

Figure 8: CelebA samples from 1000 step DDPM, 1000 step DDIM and 100 step DDIM.

<span id="page-19-2"></span>Image /page/19/Figure/3 description: The image displays a grid of faces, organized into rows and columns. The rows are labeled with "sample timesteps" and correspond to the values 20, 50, 100, and 1000. Each row contains a sequence of faces, with variations appearing across the columns. The faces show different individuals, genders, ethnicities, and expressions. Some faces are looking directly at the camera, while others are in profile or looking away. The overall impression is a progression or comparison of generated faces at different stages or sample sizes.

<span id="page-19-1"></span>Figure 9: CelebA samples from DDIM with the same random  $x_T$  and different number of steps.

Image /page/19/Picture/5 description: The image is a collage of 48 smaller images, each depicting a different architectural structure, likely historical buildings or landmarks. The structures vary in style, material, and condition, ranging from well-preserved facades to weathered ruins. The collage is arranged in two rows of 24 images each. The top row features a variety of buildings, including Gothic cathedrals, stone towers, and more modern-looking structures with arches and columns. The bottom row continues this theme with more churches, castles, and other significant buildings, some with intricate details and others appearing more rustic. The overall impression is a collection of diverse architectural styles from different eras and locations.

Figure 10: Church samples from 100 step DDPM and 100 step DDIM.

<span id="page-20-0"></span>Image /page/20/Picture/1 description: The image is a grid of 48 portraits of people, arranged in 8 rows and 6 columns. The portraits are of varying individuals, with a mix of men and women, and diverse ethnicities. Some individuals are smiling, while others have neutral expressions. The lighting and background vary slightly across the portraits, suggesting they may have been generated or collected under different conditions. The overall impression is a collection of diverse human faces.

Figure 11: More interpolations from the CelebA DDIM with  $\dim(\tau) = 50$ .

<span id="page-20-1"></span>Image /page/20/Picture/3 description: The image is a grid of 25 smaller images, each depicting a bedroom. The bedrooms vary in style and arrangement, but many feature beds, dressers, windows, and decorative elements like chandeliers and lamps. Some rooms have a modern aesthetic, while others appear more traditional or eclectic. The overall impression is a collection of diverse bedroom interior designs.

Figure 12: More interpolations from the Bedroom DDIM with  $\dim(\tau) = 50$ .

<span id="page-21-0"></span>Image /page/21/Picture/1 description: The image is a collage of 25 smaller images, arranged in a 5x5 grid. Each smaller image features a different architectural building, primarily churches or cathedrals, with varying styles and states of preservation. Some images are in color, while others are in black and white. Several images show the buildings under construction or with scaffolding. The collage includes a mix of historical and modern architectural designs, with some buildings set against clear skies and others in snowy or overcast conditions. Some of the images have watermarks from Shutterstock.

Figure 13: More interpolations from the Church DDIM with  $\dim(\tau) = 50$ .