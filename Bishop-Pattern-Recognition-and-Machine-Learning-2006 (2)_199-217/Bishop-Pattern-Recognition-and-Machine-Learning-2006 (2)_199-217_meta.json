{"table_of_contents": [{"title": "180 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 1, "polygon": [[30.0, 41.25], [297.7734375, 41.25], [297.7734375, 51.6654052734375], [30.0, 51.6654052734375]]}, {"title": "4.1. Discriminant Functions", "heading_level": null, "page_id": 2, "polygon": [[89.25, 240.0], [266.25, 240.0], [266.25, 254.14013671875], [89.25, 254.14013671875]]}, {"title": "4.1.1 Two classes", "heading_level": null, "page_id": 2, "polygon": [[137.56640625, 337.5], [243.0, 337.5], [243.0, 348.609375], [137.56640625, 348.609375]]}, {"title": "182 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 3, "polygon": [[30.0, 40.5], [297.75, 40.5], [297.75, 51.380859375], [30.0, 51.380859375]]}, {"title": "4.1.2 Multiple classes", "heading_level": null, "page_id": 3, "polygon": [[137.25, 517.5], [264.0, 517.5], [264.0, 528.767578125], [137.25, 528.767578125]]}, {"title": "184 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 5, "polygon": [[30.0, 41.25], [298.265625, 41.25], [298.265625, 51.7060546875], [30.0, 51.7060546875]]}, {"title": "4.1.3 Least squares for classification", "heading_level": null, "page_id": 5, "polygon": [[137.25, 378.75], [347.25, 378.75], [347.25, 389.583984375], [137.25, 389.583984375]]}, {"title": "4.1. Discriminant Functions 185", "heading_level": null, "page_id": 6, "polygon": [[296.25, 40.5], [473.25, 40.5], [473.25, 51.299560546875], [296.25, 51.299560546875]]}, {"title": "186 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 7, "polygon": [[30.0, 40.5], [298.265625, 40.5], [298.265625, 50.974365234375], [30.0, 50.974365234375]]}, {"title": "4.1.4 Fisher's linear discriminant", "heading_level": null, "page_id": 7, "polygon": [[137.25, 552.0], [324.75, 552.0], [324.75, 563.888671875], [137.25, 563.888671875]]}, {"title": "188 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 9, "polygon": [[30.0, 40.5], [298.7578125, 40.5], [298.7578125, 51.136962890625], [30.0, 51.136962890625]]}, {"title": "4.1. Discriminant Functions 189", "heading_level": null, "page_id": 10, "polygon": [[296.25, 40.5], [473.25, 40.5], [473.25, 50.56787109375], [296.25, 50.56787109375]]}, {"title": "4.1.5 Relation to least squares", "heading_level": null, "page_id": 10, "polygon": [[138.0, 504.75], [312.75, 504.75], [312.75, 515.4345703125], [138.0, 515.4345703125]]}, {"title": "190 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 11, "polygon": [[30.0, 41.25], [298.265625, 41.25], [298.265625, 51.6654052734375], [30.0, 51.6654052734375]]}, {"title": "4.1.6 Fisher's discriminant for multiple classes", "heading_level": null, "page_id": 12, "polygon": [[137.197265625, 71.25], [401.25, 71.25], [401.25, 83.2093505859375], [137.197265625, 83.2093505859375]]}, {"title": "192 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 13, "polygon": [[30.0, 40.5], [297.7734375, 40.5], [297.7734375, 51.5841064453125], [30.0, 51.5841064453125]]}, {"title": "4.1.7 The perceptron algorithm", "heading_level": null, "page_id": 13, "polygon": [[137.25, 511.5], [314.25, 511.5], [314.25, 522.5888671875], [137.25, 522.5888671875]]}, {"title": "<PERSON>\n1928–1969", "heading_level": null, "page_id": 14, "polygon": [[103.482421875, 454.5], [204.75, 454.5], [204.75, 478.6875], [103.482421875, 478.6875]]}, {"title": "194 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 15, "polygon": [[30.0, 40.5], [297.7734375, 39.75], [297.7734375, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "196 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 17, "polygon": [[30.0, 40.5], [298.5, 40.5], [298.5, 51.7060546875], [30.0, 51.7060546875]]}, {"title": "4.2. Probabilistic Generative Models", "heading_level": null, "page_id": 17, "polygon": [[88.5, 539.25], [317.25, 539.25], [317.25, 552.181640625], [88.5, 552.181640625]]}, {"title": "4.2. Probabilistic Generative Models 197", "heading_level": null, "page_id": 18, "polygon": [[255.9375, 40.5], [473.25, 40.5], [473.25, 50.73046875], [255.9375, 50.73046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 19], ["Picture", 1], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5703, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 46], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 42], ["Text", 6], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 46], ["Text", 6], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 731, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 46], ["Text", 3], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 46], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["SectionHeader", 2], ["Caption", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 50], ["TextInlineMath", 6], ["Equation", 6], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 739, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 52], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 767, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 45], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1308, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 47], ["Text", 6], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 69], ["Equation", 8], ["Text", 6], ["TextInlineMath", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 57], ["Equation", 8], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 64], ["Text", 7], ["Equation", 6], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 60], ["Text", 5], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 583, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 44], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 33], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 765, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 33], ["Text", 4], ["SectionHeader", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 636, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 41], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1666, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_199-217"}