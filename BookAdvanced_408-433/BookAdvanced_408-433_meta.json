{"table_of_contents": [{"title": "11\nNeural Networks", "heading_level": null, "page_id": 0, "polygon": [[132.0, 109.5], [273.0, 109.5], [273.0, 161.841796875], [132.0, 161.841796875]]}, {"title": "11.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[133.5, 354.75], [250.119140625, 354.75], [250.119140625, 367.3828125], [133.5, 367.3828125]]}, {"title": "11.2 Projection Pursuit Regression", "heading_level": null, "page_id": 0, "polygon": [[132.0, 510.0], [356.80078125, 510.0], [356.80078125, 524.00390625], [132.0, 524.00390625]]}, {"title": "392 Neural Networks", "heading_level": null, "page_id": 3, "polygon": [[132.0, 88.5], [234.28125, 88.5], [234.28125, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "11.3 Neural Networks", "heading_level": null, "page_id": 3, "polygon": [[132.75, 261.75], [275.818359375, 261.75], [275.818359375, 274.5703125], [132.75, 274.5703125]]}, {"title": "394 Neural Networks", "heading_level": null, "page_id": 5, "polygon": [[132.0, 89.25], [234.0, 89.25], [234.0, 98.25], [132.0, 98.25]]}, {"title": "11.4 Fitting Neural Networks", "heading_level": null, "page_id": 6, "polygon": [[132.0, 216.0], [323.9296875, 216.0], [323.9296875, 229.32421875], [132.0, 229.32421875]]}, {"title": "396 Neural Networks", "heading_level": null, "page_id": 7, "polygon": [[132.0, 88.5], [234.0, 88.5], [234.0, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "11.5 Some Issues in Training Neural Networks", "heading_level": null, "page_id": 8, "polygon": [[132.380859375, 477.2109375], [427.025390625, 477.2109375], [427.025390625, 490.359375], [132.380859375, 490.359375]]}, {"title": "11.5.1 Starting Values", "heading_level": null, "page_id": 8, "polygon": [[133.4267578125, 573.0], [255.3486328125, 573.0], [255.3486328125, 584.33203125], [133.4267578125, 584.33203125]]}, {"title": "398 Neural Networks", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [234.0, 88.5], [234.0, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "11.5.2 Overfitting", "heading_level": null, "page_id": 9, "polygon": [[133.5, 179.25], [232.189453125, 179.25], [232.189453125, 190.845703125], [133.5, 190.845703125]]}, {"title": "11.5.3 <PERSON><PERSON> of the Inputs", "heading_level": null, "page_id": 9, "polygon": [[133.5, 608.25], [282.75, 608.25], [282.75, 620.296875], [133.5, 620.296875]]}, {"title": "400 Neural Networks", "heading_level": null, "page_id": 11, "polygon": [[132.0, 88.5], [234.0, 88.5], [234.0, 98.3232421875], [132.0, 98.3232421875]]}, {"title": "11.5.4 Number of Hidden Units and Layers", "heading_level": null, "page_id": 11, "polygon": [[133.5, 390.0], [363.375, 390.0], [363.375, 402.1875], [133.5, 402.1875]]}, {"title": "11.5.5 Multiple Minima", "heading_level": null, "page_id": 11, "polygon": [[133.5, 609.0], [262.5, 609.0], [262.5, 619.5234375], [133.5, 619.5234375]]}, {"title": "11.6 Example: Simulated Data", "heading_level": null, "page_id": 12, "polygon": [[133.5, 233.771484375], [331.5, 233.771484375], [331.5, 246.533203125], [133.5, 246.533203125]]}, {"title": "11.7 Example: ZIP Code Data", "heading_level": null, "page_id": 15, "polygon": [[132.75, 371.056640625], [330.50390625, 371.056640625], [330.50390625, 384.591796875], [132.75, 384.591796875]]}, {"title": "408 Neural Networks", "heading_level": null, "page_id": 19, "polygon": [[132.75, 88.5], [233.25, 88.5], [233.25, 98.85498046875], [132.75, 98.85498046875]]}, {"title": "11.8 Discussion", "heading_level": null, "page_id": 19, "polygon": [[132.6796875, 409.1484375], [237.0, 409.1484375], [237.0, 422.296875], [132.6796875, 422.296875]]}, {"title": "11.9 Bayesian Neural Nets and the NIPS 2003\nChallenge", "heading_level": null, "page_id": 20, "polygon": [[133.35205078125, 262.58203125], [428.818359375, 262.58203125], [428.818359375, 291.97265625], [133.35205078125, 291.97265625]]}, {"title": "410 Neural Networks", "heading_level": null, "page_id": 21, "polygon": [[132.0, 88.5], [234.0, 88.5], [234.0, 99.0], [132.0, 99.0]]}, {"title": "11.9.1 <PERSON>es, Boosting and Bagging", "heading_level": null, "page_id": 21, "polygon": [[133.5, 351.0], [326.3203125, 351.0], [326.3203125, 363.708984375], [133.5, 363.708984375]]}, {"title": "11.9.2 Performance Comparisons", "heading_level": null, "page_id": 23, "polygon": [[133.5, 225.0], [312.275390625, 225.0], [312.275390625, 236.865234375], [133.5, 236.865234375]]}, {"title": "414 Neural Networks", "heading_level": null, "page_id": 25, "polygon": [[132.0, 88.5], [234.0, 88.5], [234.0, 99.0966796875], [132.0, 99.0966796875]]}, {"title": "11.10 Computational Considerations", "heading_level": null, "page_id": 25, "polygon": [[132.75, 541.40625], [368.15625, 541.40625], [368.15625, 554.5546875], [132.75, 554.5546875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 27], ["SectionHeader", 3], ["Text", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5500, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 48], ["TextInlineMath", 3], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 448], ["Line", 77], ["Text", 4], ["ListItem", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 46], ["Text", 6], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 45], ["Text", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 45], ["Text", 5], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 459], ["Line", 100], ["Equation", 5], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5911, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 40], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 63], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Line", 56], ["Span", 10], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1315, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 76], ["SectionHeader", 3], ["Text", 3], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 918, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 49], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 55], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 805, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 43], ["Figure", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2134, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 27], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 573, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 37], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 757, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["TableCell", 60], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1321, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 38], ["ListItem", 4], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 41], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["TableCell", 96], ["Line", 43], ["TextInlineMath", 3], ["SectionHeader", 2], ["Text", 2], ["Equation", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1989, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 43], ["Text", 7], ["ListItem", 5], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 37], ["ListItem", 5], ["Text", 3], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 825, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["TableCell", 70], ["Line", 39], ["Text", 6], ["SectionHeader", 2], ["ListItem", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1448, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_408-433"}