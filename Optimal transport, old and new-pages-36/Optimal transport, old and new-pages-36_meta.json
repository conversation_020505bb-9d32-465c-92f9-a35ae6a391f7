{"table_of_contents": [{"title": "<PERSON><PERSON><PERSON> inequalities from <PERSON>icci curvature bounds", "heading_level": null, "page_id": 0, "polygon": [[133.5, 195.1962890625], [438.75, 195.1962890625], [438.75, 206.314453125], [133.5, 206.314453125]]}, {"title": "Relation with log <PERSON><PERSON><PERSON> and Poincaré inequalities", "heading_level": null, "page_id": 1, "polygon": [[133.5, 47.832275390625], [439.5, 47.832275390625], [439.5, 59.16796875], [133.5, 59.16796875]]}, {"title": "Talagrand inequalities and Gaussian concentration", "heading_level": null, "page_id": 7, "polygon": [[133.5, 338.25], [432.75, 338.25], [432.75, 349.787109375], [133.5, 349.787109375]]}, {"title": "Poincaré inequalities and quadratic-linear transport cost", "heading_level": null, "page_id": 10, "polygon": [[133.5, 153.0], [468.75, 153.0], [468.75, 164.8388671875], [133.5, 164.8388671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 28], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1363, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 46], ["TextInlineMath", 7], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 60], ["TextInlineMath", 7], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 87], ["Equation", 7], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1141, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 89], ["Equation", 7], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1034, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 105], ["Equation", 5], ["Text", 5], ["TextInlineMath", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 425], ["Line", 119], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 53], ["TextInlineMath", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 598], ["Line", 92], ["TextInlineMath", 6], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1093, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 647], ["Line", 76], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2244, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 39], ["TextInlineMath", 9], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-36"}