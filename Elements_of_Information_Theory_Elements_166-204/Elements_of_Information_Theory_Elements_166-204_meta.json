{"table_of_contents": [{"title": "Kolmogorov Complexity", "heading_level": null, "page_id": 0, "polygon": [[66.0, 104.255859375], [347.34375, 104.255859375], [347.34375, 131.783203125], [66.0, 131.783203125]]}, {"title": "7.1 MODELS OF COMPUTATION", "heading_level": null, "page_id": 2, "polygon": [[65.25, 57.07177734375], [237.884765625, 57.07177734375], [237.884765625, 68.69970703125], [65.25, 68.69970703125]]}, {"title": "7.2 KOLMOGOROV COMPLEXITY: DEFINITIONS AND \nEXAMPLES", "heading_level": null, "page_id": 3, "polygon": [[66.0, 270.0], [346.5, 270.0], [346.5, 292.359375], [66.0, 292.359375]]}, {"title": "7.3 KOL<PERSON><PERSON><PERSON><PERSON><PERSON> COMPLEXITY AND ENTROPY", "heading_level": null, "page_id": 9, "polygon": [[63.75, 513.75], [323.25, 513.75], [323.25, 524.91796875], [63.75, 524.91796875]]}, {"title": "7.4 KOLMOG<PERSON>O<PERSON> COMPLEXITY OF INTEGERS", "heading_level": null, "page_id": 11, "polygon": [[64.3271484375, 391.5], [315.75, 391.5], [315.75, 402.78515625], [64.3271484375, 402.78515625]]}, {"title": "Theorem 7.4.2:", "heading_level": null, "page_id": 12, "polygon": [[65.25, 118.5], [144.75, 118.5], [144.75, 128.5400390625], [65.25, 128.5400390625]]}, {"title": "7.5 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> RANDOM AND INCOMPRESSIBLE \nSEQUENCES", "heading_level": null, "page_id": 12, "polygon": [[65.25, 398.25], [372.75, 398.25], [372.75, 421.13671875], [65.25, 421.13671875]]}, {"title": "7.6 UNIVERSAL PROBABILITY", "heading_level": null, "page_id": 16, "polygon": [[63.75, 274.5], [225.0, 274.5], [225.0, 285.556640625], [63.75, 285.556640625]]}, {"title": "7.7 THE HALTING PROBLEM AND THE NON-COMPUTABILITY \nOF KOLMOGOROV COMPLEXITY", "heading_level": null, "page_id": 18, "polygon": [[61.5, 326.25], [389.25, 327.0], [389.25, 350.26171875], [61.5, 350.26171875]]}, {"title": "7.8 \\Omega", "heading_level": null, "page_id": 20, "polygon": [[63.75, 58.5], [99.0, 58.5], [99.0, 69.33251953125], [63.75, 69.33251953125]]}, {"title": "Definition:", "heading_level": null, "page_id": 20, "polygon": [[63.0, 118.7314453125], [119.3994140625, 118.7314453125], [119.3994140625, 129.8056640625], [63.0, 129.8056640625]]}, {"title": "Properties of \\Omega:", "heading_level": null, "page_id": 20, "polygon": [[74.25, 265.939453125], [161.560546875, 265.939453125], [161.560546875, 277.013671875], [74.25, 277.013671875]]}, {"title": "7.9 UNIVERSAL GAMBLING", "heading_level": null, "page_id": 22, "polygon": [[64.5, 448.5], [214.6904296875, 448.5], [214.6904296875, 459.421875], [64.5, 459.421875]]}, {"title": "7.10 OCCAM'S RAZOR", "heading_level": null, "page_id": 24, "polygon": [[65.25, 229.5], [186.75, 229.5], [186.75, 239.677734375], [65.25, 239.677734375]]}, {"title": "7.11 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> COMPLEXITY AND UNIVERSAL \nPROBABILITY", "heading_level": null, "page_id": 25, "polygon": [[64.5, 477.0], [339.75, 477.0], [339.75, 499.2890625], [64.5, 499.2890625]]}, {"title": "7.12 THE KOLMOGOROV SUFFICIENT STATISTIC", "heading_level": null, "page_id": 31, "polygon": [[63.75, 205.5], [323.25, 205.5], [323.25, 216.580078125], [63.75, 216.580078125]]}, {"title": "SUMMARY OF CHAPTER 7", "heading_level": null, "page_id": 34, "polygon": [[166.5, 475.5], [298.212890625, 475.5], [298.212890625, 484.41796875], [166.5, 484.41796875]]}, {"title": "PROBLEMS FOR CHAPTER 7", "heading_level": null, "page_id": 36, "polygon": [[63.75, 339.1875], [210.234375, 339.1875], [210.234375, 349.9453125], [63.75, 349.9453125]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 38, "polygon": [[66.0, 285.0], [172.8720703125, 285.0], [172.8720703125, 296.15625], [66.0, 296.15625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 34], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 43], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 639, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 33], ["Text", 8], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 35], ["Text", 9], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 36], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 36], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 37], ["Text", 9], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 31], ["Equation", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1032, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 32], ["Equation", 6], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 32], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 35], ["Text", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 29], ["Equation", 9], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1300, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 34], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 786, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 30], ["Equation", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 35], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 39], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 36], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 46], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 38], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 42], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 37], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["ListItem", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 33], ["TextInlineMath", 7], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 42], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3477, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 36], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 38], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 41], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 39], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7851, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2979, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 40], ["Equation", 6], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1138, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 38], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 38], ["TextInlineMath", 5], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 26], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1318, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 30], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Text", 2], ["Picture", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 536, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 32], ["Equation", 7], ["Text", 6], ["TextInlineMath", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 29], ["ListItem", 8], ["Text", 6], ["TextInlineMath", 3], ["Equation", 3], ["Figure", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1709, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 40], ["ListItem", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Picture", 1], ["Text", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 569, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 33], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 616, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_166-204"}