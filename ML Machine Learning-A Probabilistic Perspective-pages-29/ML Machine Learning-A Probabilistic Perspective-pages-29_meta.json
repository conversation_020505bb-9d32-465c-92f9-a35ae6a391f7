{"table_of_contents": [{"title": "25 Clustering", "heading_level": null, "page_id": 0, "polygon": [[53.9296875, 95.23828125], [209.25, 95.23828125], [207.75, 144.75], [53.25, 143.33203125]]}, {"title": "25.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[95.25, 207.0], [195.75, 207.0], [195.75, 218.162109375], [95.25, 218.162109375]]}, {"title": "25.1.1 Measuring (dis)similarity", "heading_level": null, "page_id": 0, "polygon": [[90.75, 495.75], [247.78125, 495.75], [247.78125, 506.25], [90.75, 506.25]]}, {"title": "25.1.2 Evaluating the output of clustering methods *", "heading_level": null, "page_id": 1, "polygon": [[89.25, 529.5], [345.75, 529.5], [345.75, 540.10546875], [89.25, 540.10546875]]}, {"title": "25.1.2.1 Purity", "heading_level": null, "page_id": 2, "polygon": [[85.078125, 344.25], [157.5, 344.25], [157.5, 354.05859375], [85.078125, 354.05859375]]}, {"title": "25.1.2.2 Rand index", "heading_level": null, "page_id": 2, "polygon": [[82.828125, 547.5], [180.140625, 547.5], [180.140625, 557.19140625], [82.828125, 557.19140625]]}, {"title": "25.1.2.3 Mutual information", "heading_level": null, "page_id": 3, "polygon": [[83.25, 527.25], [215.25, 527.25], [215.25, 537.2578125], [83.25, 537.2578125]]}, {"title": "25.2 Dirichlet process mixture models", "heading_level": null, "page_id": 4, "polygon": [[93.0, 233.25], [303.75, 233.25], [303.75, 244.107421875], [93.0, 244.107421875]]}, {"title": "25.2.1 From finite to infinite mixture models", "heading_level": null, "page_id": 4, "polygon": [[88.453125, 473.25], [309.375, 473.25], [309.375, 483.15234375], [88.453125, 483.15234375]]}, {"title": "25.2.2 The Dirichlet process", "heading_level": null, "page_id": 7, "polygon": [[87.328125, 250.5], [230.484375, 250.5], [230.484375, 260.876953125], [87.328125, 260.876953125]]}, {"title": "25.2.2.1 Stick breaking construction of the DP", "heading_level": null, "page_id": 8, "polygon": [[83.7421875, 483.0], [293.25, 483.0], [293.25, 492.9609375], [83.7421875, 492.9609375]]}, {"title": "25.2.2.2 The Chinese restaurant process (CRP)", "heading_level": null, "page_id": 9, "polygon": [[81.75, 315.0], [291.75, 315.0], [291.75, 324.31640625], [81.75, 324.31640625]]}, {"title": "25.2.3 Applying Dirichlet processes to mixture modeling", "heading_level": null, "page_id": 10, "polygon": [[87.1171875, 399.75], [365.25, 399.75], [365.25, 410.6953125], [87.1171875, 410.6953125]]}, {"title": "25.2.4 Fitting a DP mixture model", "heading_level": null, "page_id": 11, "polygon": [[87.75, 61.5], [261.0, 61.5], [261.0, 71.5078125], [87.75, 71.5078125]]}, {"title": "25.3 Affinity propagation", "heading_level": null, "page_id": 12, "polygon": [[93.0, 464.25], [234.0, 464.25], [234.0, 475.875], [93.0, 475.875]]}, {"title": "25.4 Spectral clustering", "heading_level": null, "page_id": 15, "polygon": [[93.0, 492.75], [228.65625, 492.75], [228.65625, 504.984375], [93.0, 504.984375]]}, {"title": "25.4.1 Graph Laplacian", "heading_level": null, "page_id": 16, "polygon": [[89.25, 315.0], [207.75, 315.0], [207.75, 324.94921875], [89.25, 324.94921875]]}, {"title": "25.4.2 Normalized graph Laplacian", "heading_level": null, "page_id": 17, "polygon": [[87.75, 323.25], [264.75, 323.25], [264.75, 333.17578125], [87.75, 333.17578125]]}, {"title": "25.4.3 Example", "heading_level": null, "page_id": 18, "polygon": [[87.75, 381.0], [171.75, 381.0], [171.75, 391.7109375], [87.75, 391.7109375]]}, {"title": "25.5 Hierarchical clustering", "heading_level": null, "page_id": 18, "polygon": [[93.0, 510.75], [249.0, 510.75], [249.0, 523.01953125], [93.0, 523.01953125]]}, {"title": "Algorithm 25.2: Agglomerative clustering", "heading_level": null, "page_id": 20, "polygon": [[133.3828125, 64.5], [303.75, 64.5], [303.75, 74.830078125], [133.3828125, 74.830078125]]}, {"title": "25.5.1 Agglomerative clustering", "heading_level": null, "page_id": 20, "polygon": [[89.0859375, 337.5], [247.5, 337.5], [247.5, 348.36328125], [89.0859375, 348.36328125]]}, {"title": "25.5.1.1 Single link", "heading_level": null, "page_id": 22, "polygon": [[84.9375, 87.0], [177.0, 87.0], [177.0, 96.8994140625], [84.9375, 96.8994140625]]}, {"title": "25.5.1.2 Complete link", "heading_level": null, "page_id": 22, "polygon": [[83.109375, 279.75], [191.25, 279.75], [191.25, 289.669921875], [83.109375, 289.669921875]]}, {"title": "25.5.1.3 Average link", "heading_level": null, "page_id": 22, "polygon": [[83.25, 484.5], [184.5, 484.5], [184.5, 494.54296875], [83.25, 494.54296875]]}, {"title": "25.5.2 Divisive clustering", "heading_level": null, "page_id": 23, "polygon": [[87.1875, 112.5], [218.25, 112.5], [218.25, 122.765625], [87.1875, 122.765625]]}, {"title": "25.5.3 Choosing the number of clusters", "heading_level": null, "page_id": 24, "polygon": [[86.8359375, 61.5], [285.0, 61.5], [285.0, 71.78466796875], [86.8359375, 71.78466796875]]}, {"title": "25.5.4 Bayesian hierarchical clustering", "heading_level": null, "page_id": 24, "polygon": [[87.75, 165.75], [282.0, 165.75], [282.0, 176.080078125], [87.75, 176.080078125]]}, {"title": "25.5.4.1 The algorithm", "heading_level": null, "page_id": 24, "polygon": [[84.0, 317.25], [193.359375, 317.25], [193.359375, 327.48046875], [84.0, 327.48046875]]}, {"title": "25.5.4.2 The connection with Dirichlet process mixture models", "heading_level": null, "page_id": 25, "polygon": [[82.0546875, 225.0], [365.25, 225.0], [365.25, 235.248046875], [82.0546875, 235.248046875]]}, {"title": "25.5.4.3 Learning the hyper-parameters", "heading_level": null, "page_id": 26, "polygon": [[81.84375, 233.25], [264.75, 233.25], [264.75, 243.94921875], [81.84375, 243.94921875]]}, {"title": "25.5.4.4 Experimental results", "heading_level": null, "page_id": 26, "polygon": [[82.5, 315.0], [219.9375, 315.0], [219.9375, 324.31640625], [82.5, 324.31640625]]}, {"title": "25.6 Clustering datapoints and features", "heading_level": null, "page_id": 26, "polygon": [[93.0, 504.75], [308.25, 504.75], [308.25, 516.05859375], [93.0, 516.05859375]]}, {"title": "25.6.1 Biclustering", "heading_level": null, "page_id": 28, "polygon": [[88.453125, 251.25], [186.0, 251.25], [186.0, 261.3515625], [88.453125, 261.3515625]]}, {"title": "25.6.2 Multi-view clustering", "heading_level": null, "page_id": 28, "polygon": [[86.203125, 559.5], [229.5, 559.5], [229.5, 569.84765625], [86.203125, 569.84765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 31], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6321, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 44], ["Text", 8], ["ListItem", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 53], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["Line", 77], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 46], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 675, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 39], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 82], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1014, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 52], ["Text", 5], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 31], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 735, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 658], ["Line", 50], ["Text", 6], ["Equation", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1026, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["TableCell", 67], ["Line", 41], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 9963, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 903, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 56], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 372], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 66], ["TextInlineMath", 6], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1007, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 48], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 53], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 90], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1838, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 35], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 50], ["Text", 8], ["SectionHeader", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 57], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 44], ["Text", 4], ["Equation", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 960, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 540], ["Line", 44], ["TextInlineMath", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 36], ["TableCell", 28], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 26], ["Line", 11], ["Figure", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1776, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 77], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 863, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 70], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1043, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 65], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1215, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-29"}