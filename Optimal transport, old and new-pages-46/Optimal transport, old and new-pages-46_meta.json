{"table_of_contents": [{"title": "Convergence of metric-measure spaces", "heading_level": null, "page_id": 0, "polygon": [[133.5, 96.0], [406.5, 98.25], [405.75, 112.5], [133.5, 111.6650390625]]}, {"title": "Hausdorff topology", "heading_level": null, "page_id": 0, "polygon": [[133.5, 363.0], [248.25, 363.0], [248.25, 374.923828125], [133.5, 374.923828125]]}, {"title": "The Gromov<PERSON> distance", "heading_level": null, "page_id": 2, "polygon": [[133.5, 537.75], [326.25, 537.75], [326.25, 549.140625], [133.5, 549.140625]]}, {"title": "Representation by semi-distances", "heading_level": null, "page_id": 4, "polygon": [[133.5, 182.25], [330.802734375, 182.25], [330.802734375, 193.6494140625], [133.5, 193.6494140625]]}, {"title": "Representation by approximate isometries", "heading_level": null, "page_id": 6, "polygon": [[133.5, 405.75], [385.48828125, 405.75], [385.48828125, 418.04296875], [133.5, 418.04296875]]}, {"title": "The Gromov<PERSON> space", "heading_level": null, "page_id": 8, "polygon": [[134.09912109375, 537.75], [310.5, 537.75], [310.5, 549.140625], [134.09912109375, 549.140625]]}, {"title": "768 27 Convergence of metric-measure spaces", "heading_level": null, "page_id": 9, "polygon": [[133.4267578125, 26.25], [335.8828125, 26.25], [335.8828125, 35.6748046875], [133.4267578125, 35.6748046875]]}, {"title": "<PERSON><PERSON><PERSON><PERSON> topology and nets", "heading_level": null, "page_id": 11, "polygon": [[133.5, 216.75], [357.0, 216.75], [357.0, 228.357421875], [133.5, 228.357421875]]}, {"title": "Noncompact spaces", "heading_level": null, "page_id": 12, "polygon": [[133.5, 48.38818359375], [250.5, 48.38818359375], [250.5, 58.63623046875], [133.5, 58.63623046875]]}, {"title": "Definition 27.13 (<PERSON><PERSON> convergence).", "heading_level": null, "page_id": 13, "polygon": [[133.5, 547.98046875], [463.5, 547.98046875], [463.5, 558.80859375], [133.5, 558.80859375]]}, {"title": "Functional analysis on <PERSON><PERSON><PERSON> converging\nsequences", "heading_level": null, "page_id": 15, "polygon": [[133.5, 522.75], [450.75, 522.75], [450.75, 547.98046875], [133.5, 547.98046875]]}, {"title": "Adding the measure", "heading_level": null, "page_id": 17, "polygon": [[133.5, 537.5390625], [254.25, 537.5390625], [254.25, 548.3671875], [133.5, 548.3671875]]}, {"title": "778 27 Convergence of metric-measure spaces", "heading_level": null, "page_id": 19, "polygon": [[133.5, 26.25], [336.181640625, 26.25], [336.181640625, 35.72314453125], [133.5, 35.72314453125]]}, {"title": "Convergence and doubling property", "heading_level": null, "page_id": 21, "polygon": [[133.5, 131.0009765625], [347.25, 131.0009765625], [347.25, 142.4091796875], [133.5, 142.4091796875]]}, {"title": "782 27 Convergence of metric-measure spaces", "heading_level": null, "page_id": 23, "polygon": [[133.5, 26.25], [336.181640625, 26.25], [336.181640625, 36.085693359375], [133.5, 36.085693359375]]}, {"title": "Measured <PERSON> topology", "heading_level": null, "page_id": 24, "polygon": [[133.5, 166.5], [363.75, 166.5], [363.75, 177.50390625], [133.5, 177.50390625]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 26, "polygon": [[233.25, 232.224609375], [358.892578125, 232.224609375], [358.892578125, 243.052734375], [233.25, 243.052734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 32], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 2], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4085, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 46], ["Equation", 3], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6274, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 480], ["Line", 39], ["TextInlineMath", 6], ["Text", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 43], ["Text", 4], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 525], ["Line", 36], ["TextInlineMath", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 39], ["TextInlineMath", 6], ["Equation", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1396, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 48], ["Equation", 5], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 490], ["Line", 55], ["Text", 7], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 35], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 38], ["TextInlineMath", 7], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 20], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1155, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 38], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 39], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 42], ["TextInlineMath", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 38], ["TextInlineMath", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 657], ["Line", 35], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 455], ["Line", 47], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 34], ["Text", 6], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 30], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 595, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 346], ["Line", 51], ["Text", 5], ["Equation", 4], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 50], ["Text", 2], ["TextInlineMath", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 53], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 56], ["Text", 6], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 574, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 40], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 419], ["Line", 36], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 37], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 35], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 55], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1150, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 40], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-46"}