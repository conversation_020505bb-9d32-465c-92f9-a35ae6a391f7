{"table_of_contents": [{"title": "212 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 0, "polygon": [[29.25, 40.5], [297.7734375, 40.5], [297.7734375, 51.86865234375], [29.25, 51.86865234375]]}, {"title": "4.3.6 Canonical link functions", "heading_level": null, "page_id": 0, "polygon": [[137.25, 336.0], [308.35546875, 336.0], [308.35546875, 346.9833984375], [137.25, 346.9833984375]]}, {"title": "4.4. The Laplace Approximation", "heading_level": null, "page_id": 1, "polygon": [[89.25, 555.0], [291.0, 555.0], [291.0, 568.44140625], [89.25, 568.44140625]]}, {"title": "214 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 2, "polygon": [[29.25, 40.5], [297.7734375, 40.5], [297.7734375, 51.6654052734375], [29.25, 51.6654052734375]]}, {"title": "216 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 4, "polygon": [[29.25, 40.5], [297.75, 40.5], [297.75, 51.787353515625], [29.25, 51.787353515625]]}, {"title": "4.4.1 Model comparison and BIC", "heading_level": null, "page_id": 4, "polygon": [[137.25, 271.5], [323.61328125, 271.5], [323.61328125, 282.26953125], [137.25, 282.26953125]]}, {"title": "4.5. Bayesian Logistic Regression", "heading_level": null, "page_id": 5, "polygon": [[89.25, 371.25], [306.0, 371.25], [306.0, 384.7060546875], [89.25, 384.7060546875]]}, {"title": "4.5.1 Laplace approximation", "heading_level": null, "page_id": 5, "polygon": [[138.55078125, 492.0], [301.5, 492.0], [301.5, 502.751953125], [138.55078125, 502.751953125]]}, {"title": "218 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 6, "polygon": [[29.25, 40.5], [298.265625, 40.5], [298.265625, 51.5841064453125], [29.25, 51.5841064453125]]}, {"title": "4.5.2 Predictive distribution", "heading_level": null, "page_id": 6, "polygon": [[137.25, 405.75], [296.25, 405.75], [296.25, 416.5751953125], [137.25, 416.5751953125]]}, {"title": "220 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 8, "polygon": [[29.25, 40.5], [298.265625, 40.5], [298.265625, 51.624755859375], [29.25, 51.624755859375]]}, {"title": "Exercises", "heading_level": null, "page_id": 8, "polygon": [[30.0, 366.0], [91.8544921875, 367.5], [91.8544921875, 379.5029296875], [30.0, 379.5029296875]]}, {"title": "222 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 10, "polygon": [[30.0, 40.5], [298.265625, 40.5], [298.265625, 51.6654052734375], [30.0, 51.6654052734375]]}, {"title": "224 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 12, "polygon": [[29.25, 39.75], [299.25, 39.75], [299.25, 51.86865234375], [29.25, 51.86865234375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 53], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3346, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 62], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1432, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 62], ["Text", 6], ["Equation", 6], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 57], ["Equation", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 57], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8099, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 38], ["Text", 10], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1159, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 484], ["Line", 51], ["Equation", 7], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 412], ["Line", 58], ["Text", 5], ["Equation", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1716, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 47], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 2], ["ListItem", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 42], ["ListItem", 8], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 49], ["ListItem", 6], ["Text", 4], ["Equation", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 44], ["ListItem", 9], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 10], ["SectionHeader", 1], ["ListItem", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/<PERSON>-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_232-244"}