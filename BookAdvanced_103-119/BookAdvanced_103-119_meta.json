{"table_of_contents": [{"title": "3.7 Multiple Outcome Shrinkage and Selection", "heading_level": null, "page_id": 0, "polygon": [[132.08203125, 108.0], [447.75, 108.0], [447.75, 129.6474609375], [132.08203125, 129.6474609375]]}, {"title": "86 3. Linear Methods for Regression", "heading_level": null, "page_id": 2, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "3.8 More on the Lasso and Related Path\nAlgorithms", "heading_level": null, "page_id": 2, "polygon": [[132.0, 240.75], [394.453125, 240.75], [394.453125, 269.736328125], [132.0, 269.736328125]]}, {"title": "3.8.1 Incremental Forward Stagewise Regression", "heading_level": null, "page_id": 2, "polygon": [[133.5, 384.75], [388.5, 384.75], [388.5, 396.7734375], [133.5, 396.7734375]]}, {"title": "88 3. Linear Methods for Regression", "heading_level": null, "page_id": 4, "polygon": [[132.0, 89.25], [301.5, 89.25], [301.5, 98.66162109375], [132.0, 98.66162109375]]}, {"title": "3.8.2 Piecewise-Linear Path Algorithms", "heading_level": null, "page_id": 5, "polygon": [[132.75, 111.75], [345.146484375, 111.75], [345.146484375, 122.9765625], [132.75, 122.9765625]]}, {"title": "3.8.3 The Dantzig Selector", "heading_level": null, "page_id": 5, "polygon": [[133.5, 428.25], [278.25, 428.25], [278.25, 439.5], [133.5, 439.5]]}, {"title": "90 3. Linear Methods for Regression", "heading_level": null, "page_id": 6, "polygon": [[132.0, 88.5], [301.5, 88.5], [301.5, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "3.8.4 The Grouped Lasso", "heading_level": null, "page_id": 6, "polygon": [[132.75, 418.5], [269.25, 418.5], [269.25, 429.64453125], [132.75, 429.64453125]]}, {"title": "3.8.5 Further Properties of the Lasso", "heading_level": null, "page_id": 7, "polygon": [[132.75, 203.25], [330.802734375, 203.25], [330.802734375, 215.015625], [132.75, 215.015625]]}, {"title": "92 3. Linear Methods for Regression", "heading_level": null, "page_id": 8, "polygon": [[132.0, 88.5], [301.5, 88.5], [301.5, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "3.8.6 Pathwise Coordinate Optimization", "heading_level": null, "page_id": 8, "polygon": [[132.75, 537.0], [347.25, 537.0], [347.25, 547.98046875], [132.75, 547.98046875]]}, {"title": "3.9 Computational Considerations", "heading_level": null, "page_id": 9, "polygon": [[132.0, 529.5], [353.513671875, 529.5], [353.513671875, 542.1796875], [132.0, 542.1796875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 10, "polygon": [[132.0, 110.98828125], [256.693359375, 110.98828125], [256.693359375, 123.943359375], [132.0, 123.943359375]]}, {"title": "Exercises", "heading_level": null, "page_id": 10, "polygon": [[132.0, 330.0], [190.951171875, 330.0], [190.951171875, 342.439453125], [132.0, 342.439453125]]}, {"title": "96 3. Linear Methods for Regression", "heading_level": null, "page_id": 12, "polygon": [[132.0, 89.25], [302.25, 89.25], [302.25, 98.9033203125], [132.0, 98.9033203125]]}, {"title": "98 3. Linear Methods for Regression", "heading_level": null, "page_id": 14, "polygon": [[132.0, 88.5], [302.25, 88.5], [302.25, 98.7099609375], [132.0, 98.7099609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 41], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 65], ["Equation", 7], ["TextInlineMath", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 32], ["Text", 5], ["ListItem", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 58], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 938, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 38], ["TableCell", 10], ["Text", 7], ["SectionHeader", 1], ["Table", 1], ["ListItem", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 961, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 45], ["Text", 6], ["Equation", 4], ["SectionHeader", 2], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 56], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 48], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 55], ["Text", 5], ["SectionHeader", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 61], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 37], ["Text", 5], ["ListItem", 4], ["SectionHeader", 2], ["ListGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 504], ["Line", 62], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 46], ["TextInlineMath", 6], ["Text", 6], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 44], ["Text", 9], ["TextInlineMath", 3], ["Equation", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 590], ["Line", 80], ["Text", 7], ["Equation", 5], ["TextInlineMath", 3], ["ListItem", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1044, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 21], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_103-119"}