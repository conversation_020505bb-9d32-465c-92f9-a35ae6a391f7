## 3.7 Multiple Outcome Shrinkage and Selection

As noted in Section 3.2.4, the least squares estimates in a multiple-output linear model are simply the individual least squares estimates for each of the outputs.

To apply selection and shrinkage methods in the multiple output case, one could apply a univariate technique individually to each outcome or simultaneously to all outcomes. With ridge regression, for example, we could apply formula  $(3.44)$  to each of the K columns of the outcome matrix Y, using possibly different parameters  $\lambda$ , or apply it to all columns using the same value of  $\lambda$ . The former strategy would allow different amounts of regularization to be applied to different outcomes but require estimation of k separate regularization parameters  $\lambda_1, \ldots, \lambda_k$ , while the latter would permit all  $k$  outputs to be used in estimating the sole regularization parameter  $\lambda$ .

Other more sophisticated shrinkage and selection strategies that exploit correlations in the different responses can be helpful in the multiple output case. Suppose for example that among the outputs we have

$$
Y_k = f(X) + \varepsilon_k \tag{3.65}
$$

$$
Y_{\ell} = f(X) + \varepsilon_{\ell}; \tag{3.66}
$$

i.e.,  $(3.65)$  and  $(3.66)$  share the same structural part  $f(X)$  in their models. It is clear in this case that we should pool our observations on  $Y_k$  and  $Y_l$ to estimate the common f.

Combining responses is at the heart of canonical correlation analysis (CCA), a data reduction technique developed for the multiple output case. Similar to PCA, CCA finds a sequence of uncorrelated linear combinations  $\mathbf{X}v_m$ ,  $m = 1, \ldots, M$  of the  $\mathbf{x}_j$ , and a corresponding sequence of uncorrelated linear combinations  $\mathbf{Y}u_m$  of the responses  $\mathbf{y}_k$ , such that the correlations

$$
Corr2(\mathbf{Y}u_m, \mathbf{X}v_m)
$$
 (3.67)

are successively maximized. Note that at most  $M = min(K, p)$  directions can be found. The leading canonical response variates are those linear combinations (derived responses) best predicted by the  $x_j$ ; in contrast, the trailing canonical variates can be poorly predicted by the  $x_j$ , and are candidates for being dropped. The CCA solution is computed using a generalized SVD of the sample cross-covariance matrix  $\mathbf{Y}^T \mathbf{X}/N$  (assuming Y and X are centered; Exercise 3.20).

Reduced-rank regression (Izenman, 1975; van der Merwe and Zidek, 1980) formalizes this approach in terms of a regression model that explicitly pools information. Given an error covariance  $Cov(\varepsilon) = \Sigma$ , we solve the following restricted multivariate regression problem:

$$
\hat{\mathbf{B}}^{\text{rr}}(m) = \underset{\text{rank}(\mathbf{B})=m}{\operatorname{argmin}} \sum_{i=1}^{N} (y_i - \mathbf{B}^T x_i)^T \Sigma^{-1} (y_i - \mathbf{B}^T x_i).
$$
 (3.68)

With  $\Sigma$  replaced by the estimate  $\mathbf{Y}^T \mathbf{Y}/N$ , one can show (Exercise 3.21) that the solution is given by a CCA of  $Y$  and  $X$ :

$$
\hat{\mathbf{B}}^{\rm rr}(m) = \hat{\mathbf{B}} \mathbf{U}_m \mathbf{U}_m^-, \tag{3.69}
$$

where  $\mathbf{U}_m$  is the  $K \times m$  sub-matrix of **U** consisting of the first m columns, and **U** is the  $K \times M$  matrix of *left* canonical vectors  $u_1, u_2, \dots, u_M$ .  $\mathbf{U}_m^$ is its generalized inverse. Writing the solution as

$$
\hat{\mathbf{B}}^{\text{rr}}(M) = (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T (\mathbf{Y} \mathbf{U}_m) \mathbf{U}_m^-,\tag{3.70}
$$

we see that reduced-rank regression performs a linear regression on the pooled response matrix  $\mathbf{YU}_m$ , and then maps the coefficients (and hence the fits as well) back to the original response space. The reduced-rank fits are given by

$$
\hat{\mathbf{Y}}^{\text{rr}}(m) = \mathbf{X}(\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{Y} \mathbf{U}_m \mathbf{U}_m^-
$$
\n
$$
= \mathbf{H} \mathbf{Y} \mathbf{P}_m,
$$
\n(3.71)

where **H** is the usual linear regression projection operator, and  $\mathbf{P}_m$  is the rank-m CCA response projection operator. Although a better estimate of  $\sum$  would be  $(Y - \mathbf{X}\hat{\mathbf{B}})^T(Y - \mathbf{X}\hat{\mathbf{B}})/(N - pK)$ , one can show that the solution remains the same (Exercise 3.22).

Reduced-rank regression borrows strength among responses by truncating the CCA. Breiman and Friedman (1997) explored with some success shrinkage of the canonical variates between  $X$  and  $Y$ , a smooth version of reduced rank regression. Their proposal has the form (compare  $(3.69)$ )

$$
\hat{\mathbf{B}}^{\text{c+w}} = \hat{\mathbf{B}} \mathbf{U} \Lambda \mathbf{U}^{-1},\tag{3.72}
$$

where  $\Lambda$  is a diagonal shrinkage matrix (the "c+w" stands for "Curds") and Whey," the name they gave to their procedure). Based on optimal prediction in the population setting, they show that  $\Lambda$  has diagonal entries

$$
\lambda_m = \frac{c_m^2}{c_m^2 + \frac{p}{N}(1 - c_m^2)}, \ m = 1, \dots, M,
$$
\n(3.73)

where  $c_m$  is the mth canonical correlation coefficient. Note that as the ratio of the number of input variables to sample size  $p/N$  gets small, the shrinkage factors approach 1. Breiman and Friedman (1997) proposed modified versions of  $\Lambda$  based on training data and cross-validation, but the general form is the same. Here the fitted response has the form

$$
\hat{\mathbf{Y}}^{\text{c+w}} = \mathbf{H}\mathbf{Y}\mathbf{S}^{\text{c+w}},\tag{3.74}
$$

where  $S^{c+w} = U \Lambda U^{-1}$  is the response shrinkage operator.

Breiman and Friedman (1997) also suggested shrinking in both the Y space and X space. This leads to hybrid shrinkage models of the form

$$
\hat{\mathbf{Y}}^{\text{ridge,c+w}} = \mathbf{A}_{\lambda} \mathbf{Y} \mathbf{S}^{\text{c+w}},\tag{3.75}
$$

where  $\mathbf{A}_{\lambda} = \mathbf{X}(\mathbf{X}^T \mathbf{X} + \lambda \mathbf{I})^{-1} \mathbf{X}^T$  is the ridge regression shrinkage operator, as in (3.46) on page 66. Their paper and the discussions thereof contain many more details.

## 3.8 More on the Lasso and Related Path Algorithms

Since the publication of the LAR algorithm (Efron et al., 2004) there has been a lot of activity in developing algorithms for fitting regularization paths for a variety of different problems. In addition,  $L_1$  regularization has taken on a life of its own, leading to the development of the field compressed sensing in the signal-processing literature. (Donoho, 2006a; Candes, 2006). In this section we discuss some related proposals and other path algorithms, starting off with a precursor to the LAR algorithm.

### 3.8.1 Incremental Forward Stagewise Regression

Here we present another LAR-like algorithm, this time focused on forward stagewise regression. Interestingly, efforts to understand a flexible nonlinear regression procedure (boosting) led to a new algorithm for linear models (LAR). In reading the first edition of this book and the forward stagewise

Algorithm 3.4 Incremental Forward Stagewise Regression-FS<sub> $\epsilon$ </sub>.

- 1. Start with the residual **r** equal to **y** and  $\beta_1, \beta_2, \ldots, \beta_p = 0$ . All the predictors are standardized to have mean zero and unit norm.
- 2. Find the predictor  $\mathbf{x}_i$  most correlated with r
- 3. Update  $\beta_j \leftarrow \beta_j + \delta_j$ , where  $\delta_j = \epsilon \cdot \text{sign}[\langle \mathbf{x}_j , \mathbf{r} \rangle]$  and  $\epsilon > 0$  is a small step size, and set  $\mathbf{r} \leftarrow \mathbf{r} - \delta_i \mathbf{x}_i$ .
- 4. Repeat steps 2 and 3 many times, until the residuals are uncorrelated with all the predictors.

Algorithm 16.1 of Chapter  $16<sup>4</sup>$ , our colleague Brad Efron realized that with

<sup>4</sup> In the first edition, this was Algorithm 10.4 in Chapter 10.

Image /page/3/Figure/1 description: The image displays two plots side-by-side, both showing the relationship between coefficients and a measure of model complexity. The left plot, titled "FSǫ", has the x-axis labeled "Iteration" ranging from 0 to 200, and the y-axis labeled "Coefficients" ranging from -0.2 to 0.6. It shows several lines representing different variables (lcavol, svi, lweight, pgg45, lbph, gleason, age, lcp) whose coefficients change over iterations. The right plot, titled "FS₀", has the x-axis labeled "L₁ Arc-length of Coefficients" ranging from 0.0 to 2.0, and the y-axis labeled "Coefficients" ranging from -0.2 to 0.6. This plot also shows lines for the same variables, with vertical dashed lines at 0.5, 1.0, 1.5, and 2.0 on the x-axis. Both plots show that the coefficients for variables like lcavol, svi, lweight, pgg45, and lbph generally increase, while the coefficients for gleason, age, and lcp generally decrease or become negative as the model complexity increases.

FIGURE 3.19. Coefficient profiles for the prostate data. The left panel shows incremental forward stagewise regression with step size  $\epsilon = 0.01$ . The right panel shows the infinitesimal version  $FS_0$  obtained letting  $\epsilon \to 0$ . This profile was fit by the modification 3.2b to the LAR Algorithm 3.2. In this example the  $FS_0$  profiles are monotone, and hence identical to those of lasso and LAR.

linear models, one could explicitly construct the piecewise-linear lasso paths of Figure 3.10. This led him to propose the LAR procedure of Section 3.4.4, as well as the incremental version of forward-stagewise regression presented here.

Consider the linear-regression version of the forward-stagewise boosting algorithm 16.1 proposed in Section 16.1 (page 608). It generates a coefficient profile by repeatedly updating (by a small amount  $\epsilon$ ) the coefficient of the variable most correlated with the current residuals. Algorithm 3.4 gives the details. Figure 3.19 (left panel) shows the progress of the algorithm on the prostate data with step size  $\epsilon = 0.01$ . If  $\delta_i = \langle \mathbf{x}_i, \mathbf{r} \rangle$  (the least-squares coefficient of the residual on jth predictor), then this is exactly the usual forward stagewise procedure (FS) outlined in Section 3.3.3.

Here we are mainly interested in small values of  $\epsilon$ . Letting  $\epsilon \to 0$  gives the right panel of Figure 3.19, which in this case is identical to the lasso path in Figure 3.10. We call this limiting procedure infinitesimal forward stagewise regression or  $FS_0$ . This procedure plays an important role in non-linear, adaptive methods like boosting (Chapters 10 and 16) and is the version of incremental forward stagewise regression that is most amenable to theoretical analysis. Bühlmann and Hothorn  $(2007)$  refer to the same procedure as "L2boost", because of its connections to boosting.

Efron originally thought that the LAR Algorithm 3.2 was an implementation of  $FS_0$ , allowing each tied predictor a chance to update their coefficients in a balanced way, while remaining tied in correlation. However, he then realized that the LAR least-squares fit amongst the tied predictors can result in coefficients moving in the opposite direction to their correlation, which cannot happen in Algorithm 3.4. The following modification of the LAR algorithm implements  $FS_0$ :

|  |  |  | Algorithm 3.2b Least Angle Regression: FS <sub>0</sub> Modification. |  |
|--|--|--|----------------------------------------------------------------------|--|
|  |  |  |                                                                      |  |

4. Find the new direction by solving the constrained least squares problem

 $\min_{b} ||\mathbf{r} - \mathbf{X}_{\mathcal{A}}b||_2^2$  subject to  $b_j s_j \geq 0, \ j \in \mathcal{A},$ 

where  $s_j$  is the sign of  $\langle \mathbf{x}_j , \mathbf{r} \rangle$ .

The modification amounts to a non-negative least squares fit, keeping the signs of the coefficients the same as those of the correlations. One can show that this achieves the optimal balancing of infinitesimal "update turns" for the variables tied for maximal correlation (Hastie et al., 2007). Like lasso, the entire  $FS_0$  path can be computed very efficiently via the LAR algorithm.

As a consequence of these results, if the LAR profiles are monotone nonincreasing or non-decreasing, as they are in Figure 3.19, then all three methods—LAR, lasso, and  $FS_0$ —give identical profiles. If the profiles are not monotone but do not cross the zero axis, then LAR and lasso are identical.

Since  $FS_0$  is different from the lasso, it is natural to ask if it optimizes a criterion. The answer is more complex than for lasso; the  $FS_0$  coefficient profile is the solution to a differential equation. While the lasso makes optimal progress in terms of reducing the residual sum-of-squares per unit increase in  $L_1$ -norm of the coefficient vector  $\beta$ , FS<sub>0</sub> is optimal per unit increase in  $L_1$  arc-length traveled along the coefficient path. Hence its coefficient path is discouraged from changing directions too often.

 $FS<sub>0</sub>$  is more constrained than lasso, and in fact can be viewed as a monotone version of the lasso; see Figure 16.3 on page 614 for a dramatic example. FS<sub>0</sub> may be useful in  $p \gg N$  situations, where its coefficient profiles are much smoother and hence have less variance than those of lasso. More details on  $FS_0$  are given in Section 16.2.3 and Hastie et al. (2007). Figure 3.16 includes  $FS_0$  where its performance is very similar to that of the lasso.

### 3.8.2 Piecewise-Linear Path Algorithms

The least angle regression procedure exploits the piecewise linear nature of the lasso solution paths. It has led to similar "path algorithms" for other regularized problems. Suppose we solve

$$
\hat{\beta}(\lambda) = \operatorname{argmin}_{\beta} \left[ R(\beta) + \lambda J(\beta) \right],\tag{3.76}
$$

with

$$
R(\beta) = \sum_{i=1}^{N} L(y_i, \beta_0 + \sum_{j=1}^{p} x_{ij} \beta_j),
$$
 (3.77)

where both the loss function  $L$  and the penalty function  $J$  are convex. Then the following are sufficient conditions for the solution path  $\beta(\lambda)$  to be piecewise linear (Rosset and Zhu, 2007):

- 1. R is quadratic or piecewise-quadratic as a function of  $\beta$ , and
- 2. *J* is piecewise linear in  $\beta$ .

This also implies (in principle) that the solution path can be efficiently computed. Examples include squared- and absolute-error loss, "Huberized" losses, and the  $L_1, L_\infty$  penalties on  $\beta$ . Another example is the "hinge loss" function used in the support vector machine. There the loss is piecewise linear, and the penalty is quadratic. Interestingly, this leads to a piecewiselinear path algorithm in the *dual space*; more details are given in Section 12.3.5.

### 3.8.3 The Dantzig Selector

Candes and Tao (2007) proposed the following criterion:

$$
\min_{\beta} ||\beta||_1 \text{ subject to } ||\mathbf{X}^T(\mathbf{y} - \mathbf{X}\beta)||_{\infty} \le s. \tag{3.78}
$$

They call the solution the *Dantzig selector* (DS). It can be written equivalently as

$$
\min_{\beta} \|\mathbf{X}^T(\mathbf{y} - \mathbf{X}\beta)\|_{\infty} \text{ subject to } ||\beta||_1 \le t. \tag{3.79}
$$

Here  $|| \cdot ||_{\infty}$  denotes the  $L_{\infty}$  norm, the maximum absolute value of the components of the vector. In this form it resembles the lasso, replacing squared error loss by the maximum absolute value of its gradient. Note that as  $t$  gets large, both procedures yield the least squares solution if  $N < p$ . If  $p \geq N$ , they both yield the least squares solution with minimum  $L_1$  norm. However for smaller values of t, the DS procedure produces a different path of solutions than the lasso.

Candes and Tao (2007) show that the solution to DS is a linear programming problem; hence the name Dantzig selector, in honor of the late

George Dantzig, the inventor of the simplex method for linear programming. They also prove a number of interesting mathematical properties for the method, related to its ability to recover an underlying sparse coefficient vector. These same properties also hold for the lasso, as shown later by Bickel et al. (2008).

Unfortunately the operating properties of the DS method are somewhat unsatisfactory. The method seems similar in spirit to the lasso, especially when we look at the lasso's stationary conditions  $(3.58)$ . Like the LAR algorithm, the lasso maintains the same inner product (and correlation) with the current residual for all variables in the active set, and moves their coefficients to optimally decrease the residual sum of squares. In the process, this common correlation is decreased monotonically (Exercise 3.23), and at all times this correlation is larger than that for non-active variables. The Dantzig selector instead tries to minimize the maximum inner product of the current residual with all the predictors. Hence it can achieve a smaller maximum than the lasso, but in the process a curious phenomenon can occur. If the size of the active set is  $m$ , there will be  $m$  variables tied with maximum correlation. However, these need not coincide with the active set! Hence it can include a variable in the model that has smaller correlation with the current residual than some of the excluded variables (Efron et al., 2007). This seems unreasonable and may be responsible for its sometimes inferior prediction accuracy. Efron et al. (2007) also show that DS can yield extremely erratic coefficient paths as the regularization parameter s is varied.

### 3.8.4 The Grouped Lasso

In some problems, the predictors belong to pre-defined groups; for example genes that belong to the same biological pathway, or collections of indicator (dummy) variables for representing the levels of a categorical predictor. In this situation it may be desirable to shrink and select the members of a group together. The grouped lasso is one way to achieve this. Suppose that the p predictors are divided into L groups, with  $p_\ell$  the number in group  $\ell$ . For ease of notation, we use a matrix  $\mathbf{X}_{\ell}$  to represent the predictors corresponding to the  $\ell$ th group, with corresponding coefficient vector  $\beta_{\ell}$ . The grouped-lasso minimizes the convex criterion

$$
\min_{\beta \in \mathbb{R}^p} \left( ||\mathbf{y} - \beta_0 \mathbf{1} - \sum_{\ell=1}^L \mathbf{X}_{\ell} \beta_{\ell}||_2^2 + \lambda \sum_{\ell=1}^L \sqrt{p_{\ell}} ||\beta_{\ell}||_2 \right),\tag{3.80}
$$

where the  $\sqrt{p_\ell}$  terms accounts for the varying group sizes, and  $|| \cdot ||_2$  is the Euclidean norm (not squared). Since the Euclidean norm of a vector  $\beta_{\ell}$  is zero only if all of its components are zero, this procedure encourages sparsity at both the group and individual levels. That is, for some values of  $\lambda$ , an entire group of predictors may drop out of the model. This procedure was proposed by Bakin (1999) and Lin and Zhang (2006), and studied and generalized by Yuan and Lin (2007). Generalizations include more general  $L_2$  norms  $||\eta||_K = (\eta^T K \eta)^{1/2}$ , as well as allowing overlapping groups of predictors (Zhao et al., 2008). There are also connections to methods for fitting sparse additive models (Lin and Zhang, 2006; Ravikumar et al., 2008).

### 3.8.5 Further Properties of the Lasso

A number of authors have studied the ability of the lasso and related procedures to recover the correct model, as  $N$  and  $p$  grow. Examples of this work include Knight and Fu (2000), Greenshtein and Ritov (2004), Tropp  $(2004)$ , Donoho  $(2006b)$ , Meinshausen  $(2007)$ , Meinshausen and Bühlmann (2006), Tropp (2006), Zhao and Yu (2006), Wainwright (2006), and Bunea et al. (2007). For example Donoho (2006b) focuses on the  $p > N$  case and considers the lasso solution as the bound  $t$  gets large. In the limit this gives the solution with minimum  $L_1$  norm among all models with zero training error. He shows that under certain assumptions on the model matrix  $X$ , if the true model is sparse, this solution identifies the correct predictors with high probability.

Many of the results in this area assume a condition on the model matrix of the form

$$
\max_{j \in \mathcal{S}^c} ||\mathbf{x}_j^T \mathbf{X}_{\mathcal{S}} (\mathbf{X}_{\mathcal{S}}^T \mathbf{X}_{\mathcal{S}})^{-1}||_1 \le (1 - \epsilon) \text{ for some } \epsilon \in (0, 1]. \tag{3.81}
$$

Here  $S$  indexes the subset of features with non-zero coefficients in the true underlying model, and  $\mathbf{X}_{\mathcal{S}}$  are the columns of  $\mathbf{X}$  corresponding to those features. Similarly  $\mathcal{S}^c$  are the features with true coefficients equal to zero, and  $\mathbf{X}_{\mathcal{S}^c}$  the corresponding columns. This says that the least squares coefficients for the columns of  $\mathbf{X}_{\mathcal{S}^c}$  on  $\mathbf{X}_{\mathcal{S}}$  are not too large, that is, the "good" variables  $S$  are not too highly correlated with the nuisance variables  $S<sup>c</sup>$ .

Regarding the coefficients themselves, the lasso shrinkage causes the estimates of the non-zero coefficients to be biased towards zero, and in general they are not consistent<sup>5</sup>. One approach for reducing this bias is to run the lasso to identify the set of non-zero coefficients, and then fit an unrestricted linear model to the selected set of features. This is not always feasible, if the selected set is large. Alternatively, one can use the lasso to select the set of non-zero predictors, and then apply the lasso again, but using only the selected predictors from the first step. This is known as the relaxed lasso (Meinshausen, 2007). The idea is to use cross-validation to estimate the initial penalty parameter for the lasso, and then again for a second penalty parameter applied to the selected set of predictors. Since

<sup>5</sup>Statistical consistency means as the sample size grows, the estimates converge to the true values.

the variables in the second step have less "competition" from noise variables, cross-validation will tend to pick a smaller value for  $\lambda$ , and hence their coefficients will be shrunken less than those in the initial estimate.

Alternatively, one can modify the lasso penalty function so that larger coefficients are shrunken less severely; the smoothly clipped absolute deviation (SCAD) penalty of Fan and Li (2005) replaces  $\lambda|\beta|$  by  $J_a(\beta,\lambda)$ , where

$$
\frac{dJ_a(\beta,\lambda)}{d\beta} = \lambda \cdot \text{sign}(\beta) \Big[ I(|\beta| \le \lambda) + \frac{(a\lambda - |\beta|)_{+}}{(a-1)\lambda} I(|\beta| > \lambda) \Big] \tag{3.82}
$$

for some  $a > 2$ . The second term in square-braces reduces the amount of shrinkage in the lasso for larger values of  $\beta$ , with ultimately no shrinkage as  $a \to \infty$ . Figure 3.20 shows the SCAD penalty, along with the lasso and

Image /page/8/Figure/5 description: The image displays three plots side-by-side. The leftmost plot is titled "|β|" and shows a V-shaped graph with the x-axis labeled "β" ranging from -4 to 4, and the y-axis ranging from 0 to 5. The middle plot is titled "SCAD" and shows a curve that starts at a value of 2.5, decreases to 0 at β=0, and then increases back to 2.5. This plot also has the x-axis labeled "β" ranging from -4 to 4, and the y-axis ranging from 0.0 to 2.5. Vertical dashed lines are present at β = -4, -3, 3, and 4. The rightmost plot is titled "|β|1−ν" and shows a U-shaped graph with the x-axis labeled "β" ranging from -4 to 4, and the y-axis ranging from 0 to 2.5. All three plots use an orange line to represent the data.

FIGURE 3.20. The lasso and two alternative non-convex penalties designed to penalize large coefficients less. For SCAD we use  $\lambda = 1$  and  $a = 4$ , and  $\nu = \frac{1}{2}$  in the last panel.

 $|\beta|^{1-\nu}$ . However this criterion is non-convex, which is a drawback since it makes the computation much more difficult. The adaptive lasso (Zou, 2006) uses a weighted penalty of the form  $\sum_{j=1}^{p} w_j |\beta_j|$  where  $w_j = 1/|\hat{\beta}_j|^{\nu}, \hat{\beta}_j$  is the ordinary least squares estimate and  $\nu > 0$ . This is a practical approximation to the  $\beta$ <sup>[*q*</sup> penalties (*q* = 1−*v* here) discussed in Section 3.4.3. The adaptive lasso yields consistent estimates of the parameters while retaining the attractive convexity property of the lasso.

#### 3.8.6 Pathwise Coordinate Optimization

An alternate approach to the LARS algorithm for computing the lasso solution is simple coordinate descent. This idea was proposed by Fu (1998) and Daubechies et al. (2004), and later studied and generalized by Friedman et al. (2007), Wu and Lange (2008) and others. The idea is to fix the penalty parameter  $\lambda$  in the Lagrangian form  $(3.52)$  and optimize successively over each parameter, holding the other parameters fixed at their current values.

Suppose the predictors are all standardized to have mean zero and unit norm. Denote by  $\tilde{\beta}_k(\lambda)$  the current estimate for  $\beta_k$  at penalty parameter λ. We can rearrange (3.52) to isolate  $β<sub>i</sub>$ ,

$$
R(\tilde{\beta}(\lambda), \beta_j) = \frac{1}{2} \sum_{i=1}^{N} \left( y_i - \sum_{k \neq j} x_{ik} \tilde{\beta}_k(\lambda) - x_{ij} \beta_j \right)^2 + \lambda \sum_{k \neq j} |\tilde{\beta}_k(\lambda)| + \lambda |\beta_j|,
$$
\n(3.83)

where we have suppressed the intercept and introduced a factor  $\frac{1}{2}$  for convenience. This can be viewed as a univariate lasso problem with response variable the partial residual  $y_i - \tilde{y}_i^{(j)} = y_i - \sum_{k \neq j} x_{ik} \tilde{\beta}_k(\lambda)$ . This has an explicit solution, resulting in the update

$$
\tilde{\beta}_j(\lambda) \leftarrow S\left(\sum_{i=1}^N x_{ij}(y_i - \tilde{y}_i^{(j)}), \lambda\right). \tag{3.84}
$$

Here  $S(t, \lambda) = sign(t)(|t|-\lambda)_+$  is the soft-thresholding operator in Table 3.4 on page 71. The first argument to  $S(\cdot)$  is the simple least-squares coefficient of the partial residual on the standardized variable  $x_{ij}$ . Repeated iteration of (3.84)—cycling through each variable in turn until convergence—yields the lasso estimate  $\beta(\lambda)$ .

We can also use this simple algorithm to efficiently compute the lasso solutions at a grid of values of  $\lambda$ . We start with the smallest value  $\lambda_{\text{max}}$ for which  $\beta(\lambda_{\text{max}}) = 0$ , decrease it a little and cycle through the variables until convergence. Then  $\lambda$  is decreased again and the process is repeated, using the previous solution as a "warm start" for the new value of  $\lambda$ . This can be faster than the LARS algorithm, especially in large problems. A key to its speed is the fact that the quantities in (3.84) can be updated quickly as j varies, and often the update is to leave  $\tilde{\beta}_j = 0$ . On the other hand, it delivers solutions over a grid of  $\lambda$  values, rather than the entire solution path. The same kind of algorithm can be applied to the elastic net, the grouped lasso and many other models in which the penalty is a sum of functions of the individual parameters (Friedman et al., 2010). It can also be applied, with some substantial modifications, to the fused lasso (Section 18.4.2); details are in Friedman et al. (2007).

## 3.9 Computational Considerations

Least squares fitting is usually done via the Cholesky decomposition of the matrix  $X^T X$  or a QR decomposition of X. With N observations and p features, the Cholesky decomposition requires  $p^3 + Np^2/2$  operations, while the QR decomposition requires  $Np^2$  operations. Depending on the relative size of  $N$  and  $p$ , the Cholesky can sometimes be faster; on the other hand, it can be less numerically stable (Lawson and Hansen, 1974). Computation of the lasso via the LAR algorithm has the same order of computation as a least squares fit.

## Bibliographic Notes

Linear regression is discussed in many statistics books, for example, Seber (1984), Weisberg (1980) and Mardia et al. (1979). Ridge regression was introduced by Hoerl and Kennard (1970), while the lasso was proposed by Tibshirani (1996). Around the same time, lasso-type penalties were proposed in the basis pursuit method for signal processing (Chen et al., 1998). The least angle regression procedure was proposed in Efron et al. (2004); related to this is the earlier homotopy procedure of Osborne et al. (2000a) and Osborne et al. (2000b). Their algorithm also exploits the piecewise linearity used in the LAR/lasso algorithm, but lacks its transparency. The criterion for the forward stagewise criterion is discussed in Hastie et al. (2007). Park and Hastie (2007) develop a path algorithm similar to least angle regression for generalized regression models. Partial least squares was introduced by Wold (1975). Comparisons of shrinkage methods may be found in Copas (1983) and Frank and Friedman (1993).

## Exercises

Ex. 3.1 Show that the F statistic  $(3.13)$  for dropping a single coefficient from a model is equal to the square of the corresponding  $z$ -score  $(3.12)$ .

Ex. 3.2 Given data on two variables  $X$  and Y, consider fitting a cubic polynomial regression model  $f(X) = \sum_{j=0}^{3} \beta_j X^j$ . In addition to plotting the fitted curve, you would like a 95% confidence band about the curve. Consider the following two approaches:

- 1. At each point  $x_0$ , form a 95% confidence interval for the linear function  $a^T \beta = \sum_{j=0}^3 \beta_j x_0^j$ .
- 2. Form a 95% confidence set for  $\beta$  as in (3.15), which in turn generates confidence intervals for  $f(x_0)$ .

How do these approaches differ? Which band is likely to be wider? Conduct a small simulation experiment to compare the two methods.

Ex. 3.3 Gauss–Markov theorem:

- (a) Prove the Gauss–Markov theorem: the least squares estimate of a parameter  $a^T\beta$  has variance no bigger than that of any other linear unbiased estimate of  $a^T \beta$  (Section 3.2.2).
- (b) The matrix inequality  $\mathbf{B} \preceq \mathbf{A}$  holds if  $\mathbf{A} \mathbf{B}$  is positive semidefinite. Show that if  $\hat{V}$  is the variance-covariance matrix of the least squares estimate of  $\beta$  and  $\tilde{V}$  is the variance-covariance matrix of any other linear unbiased estimate, then  $\hat{\mathbf{V}} \prec \tilde{\mathbf{V}}$ .

Ex. 3.4 Show how the vector of least squares coefficients can be obtained from a single pass of the Gram–Schmidt procedure (Algorithm 3.1). Represent your solution in terms of the QR decomposition of X.

Ex. 3.5 Consider the ridge regression problem (3.41). Show that this problem is equivalent to the problem

$$
\hat{\beta}^c = \underset{\beta^c}{\text{argmin}} \left\{ \sum_{i=1}^N \left[ y_i - \beta_0^c - \sum_{j=1}^p (x_{ij} - \bar{x}_j) \beta_j^c \right]^2 + \lambda \sum_{j=1}^p \beta_j^{c2} \right\}.
$$
 (3.85)

Give the correspondence between  $\beta^c$  and the original  $\beta$  in (3.41). Characterize the solution to this modified criterion. Show that a similar result holds for the lasso.

Ex. 3.6 Show that the ridge regression estimate is the mean (and mode) of the posterior distribution, under a Gaussian prior  $\beta \sim N(0, \tau I)$ , and Gaussian sampling model  $\mathbf{y} \sim N(\mathbf{X}\beta, \sigma^2 \mathbf{I})$ . Find the relationship between the regularization parameter  $\lambda$  in the ridge formula, and the variances  $\tau$ and  $\sigma^2$ .

Ex. 3.7 Assume  $y_i \sim N(\beta_0 + x_i^T \beta, \sigma^2), i = 1, 2, ..., N$ , and the parameters  $\beta_j$ ,  $j = 1, \ldots, p$  are each distributed as  $N(0, \tau^2)$ , independently of one another. Assuming  $\sigma^2$  and  $\tau^2$  are known, and  $\beta_0$  is not governed by a prior (or has a flat improper prior), show that the (minus) log-posterior density of  $\beta$  is proportional to  $\sum_{i=1}^{N} (y_i - \beta_0 - \sum_j x_{ij} \beta_j)^2 + \lambda \sum_{j=1}^{p} \beta_j^2$ where  $\lambda = \sigma^2/\tau^2$ .

Ex. 3.8 Consider the QR decomposition of the uncentered  $N \times (p+1)$ matrix **X** (whose first column is all ones), and the SVD of the  $N \times p$ centered matrix  $X$ . Show that  $Q_2$  and  $U$  span the same subspace, where  $Q_2$  is the sub-matrix of  $Q$  with the first column removed. Under what circumstances will they be the same, up to sign flips?

Ex. 3.9 Forward stepwise regression. Suppose we have the QR decomposition for the  $N \times q$  matrix  $\mathbf{X}_1$  in a multiple regression problem with response y, and we have an additional  $p-q$  predictors in the matrix  $\mathbf{X}_2$ . Denote the current residual by r. We wish to establish which one of these additional variables will reduce the residual-sum-of squares the most when included with those in  $X_1$ . Describe an efficient procedure for doing this.

Ex. 3.10 Backward stepwise regression. Suppose we have the multiple regression fit of y on X, along with the standard errors and Z-scores as in Table 3.2. We wish to establish which variable, when dropped, will increase the residual sum-of-squares the least. How would you do this?

Ex. 3.11 Show that the solution to the multivariate linear regression problem (3.40) is given by (3.39). What happens if the covariance matrices  $\Sigma_i$ are different for each observation?

Ex. 3.12 Show that the ridge regression estimates can be obtained by ordinary least squares regression on an augmented data set. We augment the centered matrix **X** with p additional rows  $\sqrt{\lambda}$ **I**, and augment y with p zeros. By introducing artificial data having response value zero, the fitting procedure is forced to shrink the coefficients toward zero. This is related to the idea of hints due to Abu-Mostafa (1995), where model constraints are implemented by adding artificial data examples that satisfy them.

Ex. 3.13 Derive the expression (3.62), and show that  $\hat{\beta}^{\text{per}}(p) = \hat{\beta}^{\text{ls}}$ .

Ex. 3.14 Show that in the orthogonal case, PLS stops after  $m = 1$  steps, because subsequent  $\hat{\varphi}_{mj}$  in step 2 in Algorithm 3.3 are zero.

Ex. 3.15 Verify expression (3.64), and hence show that the partial least squares directions are a compromise between the ordinary regression coefficient and the principal component directions.

Ex. 3.16 Derive the entries in Table 3.4, the explicit forms for estimators in the orthogonal case.

Ex. 3.17 Repeat the analysis of Table 3.3 on the spam data discussed in Chapter 1.

Ex. 3.18 Read about conjugate gradient algorithms (Murray et al., 1981, for example), and establish a connection between these algorithms and partial least squares.

Ex. 3.19 Show that  $\|\hat{\beta}^{\text{ridge}}\|$  increases as its tuning parameter  $\lambda \to 0$ . Does the same property hold for the lasso and partial least squares estimates? For the latter, consider the "tuning parameter" to be the successive steps in the algorithm.

Ex. 3.20 Consider the canonical-correlation problem (3.67). Show that the leading pair of canonical variates  $u_1$  and  $v_1$  solve the problem

$$
\max_{u^T(\mathbf{Y}^T\mathbf{Y})u=1} u^T(\mathbf{Y}^T\mathbf{X})v,
$$
\n
$$
v_T(\mathbf{X}^T\mathbf{X})v=1
$$
\n(3.86)

a generalized SVD problem. Show that the solution is given by  $u_1 =$  $(\mathbf{Y}^T \mathbf{Y})^{-\frac{1}{2}} u_1^*$ , and  $v_1 = (\mathbf{X}^T \mathbf{X})^{-\frac{1}{2}} v_1^*$ , where  $u_1^*$  and  $v_1^*$  are the leading left and right singular vectors in

$$
(\mathbf{Y}^T \mathbf{Y})^{-\frac{1}{2}} (\mathbf{Y}^T \mathbf{X}) (\mathbf{X}^T \mathbf{X})^{-\frac{1}{2}} = \mathbf{U}^* \mathbf{D}^* \mathbf{V}^{*T}.
$$
 (3.87)

Show that the entire sequence  $u_m$ ,  $v_m$ ,  $m = 1, \ldots, \min(K, p)$  is also given by (3.87).

Ex. 3.21 Show that the solution to the reduced-rank regression problem (3.68), with  $\Sigma$  estimated by  ${\bf Y}^T{\bf Y}/N$ , is given by (3.69). Hint: Transform **Y** to  $\mathbf{Y}^* = \mathbf{Y} \Sigma^{-\frac{1}{2}}$ , and solved in terms of the canonical vectors  $u_m^*$ . Show that  $\mathbf{U}_m = \Sigma^{-\frac{1}{2}} \mathbf{U}_m^*$ , and a generalized inverse is  $\mathbf{U}_m^- = \mathbf{U}_m^* \mathbf{Z}^{\frac{1}{2}}$ .

Ex. 3.22 Show that the solution in Exercise 3.21 does not change if  $\Sigma$  is estimated by the more natural quantity  $(\mathbf{Y} - \mathbf{X}\hat{\mathbf{B}})^T(\mathbf{Y} - \mathbf{X}\hat{\mathbf{B}})/(\tilde{N} - pK)$ .

Ex. 3.23 Consider a regression problem with all variables and response having mean zero and standard deviation one. Suppose also that each variable has identical absolute correlation with the response:

$$
\frac{1}{N}|\langle \mathbf{x}_j, \mathbf{y} \rangle| = \lambda, \ j = 1, \ldots, p.
$$

Let  $\hat{\beta}$  be the least-squares coefficient of y on **X**, and let  $u(\alpha) = \alpha \mathbf{X} \hat{\beta}$  for  $\alpha \in [0, 1]$  be the vector that moves a fraction  $\alpha$  toward the least squares fit u. Let RSS be the residual sum-of-squares from the full least squares fit.

(a) Show that

$$
\frac{1}{N}|\langle \mathbf{x}_j, \mathbf{y}-\mathbf{u}(\alpha)\rangle| = (1-\alpha)\lambda, \ j = 1,\ldots,p,
$$

and hence the correlations of each  $x_j$  with the residuals remain equal in magnitude as we progress toward u.

(b) Show that these correlations are all equal to

$$
\lambda(\alpha) = \frac{(1-\alpha)}{\sqrt{(1-\alpha)^2 + \frac{\alpha(2-\alpha)}{N} \cdot RSS}} \cdot \lambda,
$$

and hence they decrease monotonically to zero.

(c) Use these results to show that the LAR algorithm in Section 3.4.4 keeps the correlations tied and monotonically decreasing, as claimed in (3.55).

Ex. 3.24 LAR directions. Using the notation around equation (3.55) on page 74, show that the LAR direction makes an equal angle with each of the predictors in  $A_k$ .

Ex. 3.25 LAR look-ahead (Efron et al., 2004, Sec. 2). Starting at the beginning of the kth step of the LAR algorithm, derive expressions to identify the next variable to enter the active set at step  $k+1$ , and the value of  $\alpha$  at which this occurs (using the notation around equation  $(3.55)$  on page 74).

Ex. 3.26 Forward stepwise regression enters the variable at each step that most reduces the residual sum-of-squares. LAR adjusts variables that have the most (absolute) correlation with the current residuals. Show that these two entry criteria are not necessarily the same. [Hint: let  $\mathbf{x}_{j,A}$  be the jth

variable, linearly adjusted for all the variables currently in the model. Show that the first criterion amounts to identifying the j for which  $Cor(\mathbf{x}_{i,A}, \mathbf{r})$ is largest in magnitude.

Ex. 3.27 Lasso and LAR: Consider the lasso problem in Lagrange multiplier form: with  $L(\beta) = \frac{1}{2} \sum_i (y_i - \sum_j x_{ij} \beta_j)^2$ , we minimize

$$
L(\beta) + \lambda \sum_{j} |\beta_j| \tag{3.88}
$$

for fixed  $\lambda > 0$ .

(a) Setting  $\beta_j = \beta_j^+ - \beta_j^-$  with  $\beta_j^+, \beta_j^- \geq 0$ , expression (3.88) becomes  $L(\beta) + \lambda \sum_j (\beta_j^+ + \beta_j^-)$ . Show that the Lagrange dual function is

$$
L(\beta) + \lambda \sum_{j} (\beta_j^+ + \beta_j^-) - \sum_{j} \lambda_j^+ \beta_j^+ - \sum_{j} \lambda_j^- \beta_j^- \tag{3.89}
$$

and the Karush–Kuhn–Tucker optimality conditions are

$$
\nabla L(\beta)_{j} + \lambda - \lambda_{j}^{+} = 0
$$
  
$$
-\nabla L(\beta)_{j} + \lambda - \lambda_{j}^{-} = 0
$$
  
$$
\lambda_{j}^{+} \beta_{j}^{+} = 0
$$
  
$$
\lambda_{j}^{-} \beta_{j}^{-} = 0,
$$

along with the non-negativity constraints on the parameters and all the Lagrange multipliers.

(b) Show that  $|\nabla L(\beta)_i| \leq \lambda \ \forall j$ , and that the KKT conditions imply one of the following three scenarios:

$$
\lambda = 0 \Rightarrow \nabla L(\beta)_j = 0 \,\forall j
$$
  
\n
$$
\beta_j^+ > 0, \ \lambda > 0 \Rightarrow \lambda_j^+ = 0, \ \nabla L(\beta)_j = -\lambda < 0, \ \beta_j^- = 0
$$
  
\n
$$
\beta_j^- > 0, \ \lambda > 0 \Rightarrow \lambda_j^- = 0, \ \nabla L(\beta)_j = \lambda > 0, \ \beta_j^+ = 0.
$$

Hence show that for any "active" predictor having  $\beta_j \neq 0$ , we must have  $\nabla L(\beta)_j = -\lambda$  if  $\beta_j > 0$ , and  $\nabla L(\beta)_j = \lambda$  if  $\beta_j < 0$ . Assuming the predictors are standardized, relate  $\lambda$  to the correlation between the jth predictor and the current residuals.

(c) Suppose that the set of active predictors is unchanged for  $\lambda_0 \geq \lambda \geq \lambda_1$ . Show that there is a vector  $\gamma_0$  such that

$$
\hat{\beta}(\lambda) = \hat{\beta}(\lambda_0) - (\lambda - \lambda_0)\gamma_0 \tag{3.90}
$$

Thus the lasso solution path is linear as  $\lambda$  ranges from  $\lambda_0$  to  $\lambda_1$  (Efron et al., 2004; Rosset and Zhu, 2007).

Ex. 3.28 Suppose for a given t in  $(3.51)$ , the fitted lasso coefficient for variable  $X_j$  is  $\hat{\beta}_j = a$ . Suppose we augment our set of variables with an identical copy  $X_j^* = X_j$ . Characterize the effect of this exact collinearity by describing the set of solutions for  $\hat{\beta}_j$  and  $\hat{\beta}_j^*$ , using the same value of t.

Ex. 3.29 Suppose we run a ridge regression with parameter  $\lambda$  on a single variable X, and get coefficient a. We now include an exact copy  $X^* = X$ , and refit our ridge regression. Show that both coefficients are identical, and derive their value. Show in general that if m copies of a variable  $X_j$  are included in a ridge regression, their coefficients are all the same.

Ex. 3.30 Consider the elastic-net optimization problem:

$$
\min_{\beta} ||\mathbf{y} - \mathbf{X}\beta||^2 + \lambda[\alpha||\beta||_2^2 + (1-\alpha)||\beta||_1].
$$
 (3.91)

Show how one can turn this into a lasso problem, using an augmented version of  $X$  and  $y$ .