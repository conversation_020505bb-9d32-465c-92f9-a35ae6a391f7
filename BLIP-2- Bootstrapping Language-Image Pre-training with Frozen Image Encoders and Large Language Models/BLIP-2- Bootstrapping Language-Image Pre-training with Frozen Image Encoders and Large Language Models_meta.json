{"table_of_contents": [{"title": "BLIP-2: Bootstrapping Language-Image Pre-training\nwith Frozen Image Encoders and Large Language Models", "heading_level": null, "page_id": 0, "polygon": [[119.25, 89.25], [476.25, 89.25], [476.25, 120.076171875], [119.25, 120.076171875]]}, {"title": "Junnan Li Dongxu Li Si<PERSON> Steven Hoi\nSalesforce Research", "heading_level": null, "page_id": 0, "polygon": [[183.48046875, 158.25], [409.095703125, 158.25], [409.095703125, 181.7578125], [183.48046875, 181.7578125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[149.115234375, 216.0], [195.75, 216.0], [195.75, 227.00390625], [149.115234375, 227.00390625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 527.25], [132.75, 527.25], [132.75, 538.69921875], [54.0, 538.69921875]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[54.0, 492.6796875], [138.75, 492.6796875], [138.75, 504.28125], [54.0, 504.28125]]}, {"title": "2.1. End-to-end Vision-Language Pre-training", "heading_level": null, "page_id": 1, "polygon": [[54.0, 510.46875], [251.25, 510.46875], [251.25, 521.296875], [54.0, 521.296875]]}, {"title": "2.2. <PERSON><PERSON><PERSON> Vision-Language Pre-training", "heading_level": null, "page_id": 1, "polygon": [[306.0, 163.8720703125], [493.5, 163.8720703125], [493.5, 174.3134765625], [306.0, 174.3134765625]]}, {"title": "3. Method", "heading_level": null, "page_id": 1, "polygon": [[306.0, 481.078125], [360.0, 481.078125], [360.0, 492.6796875], [306.0, 492.6796875]]}, {"title": "3.1. Model Architecture", "heading_level": null, "page_id": 1, "polygon": [[305.5517578125, 617.58984375], [409.9921875, 617.58984375], [409.9921875, 628.41796875], [305.5517578125, 628.41796875]]}, {"title": "3.2. Bootstrap Vision-Language Representation\nLearning from a Frozen Image Encoder", "heading_level": null, "page_id": 2, "polygon": [[54.0, 503.5078125], [258.0, 503.5078125], [258.0, 525.1640625], [54.0, 525.1640625]]}, {"title": "3.3. Bootstrap Vision-to-Language Generative Learning\nfrom a Frozen LLM", "heading_level": null, "page_id": 3, "polygon": [[54.0, 250.5], [289.5, 250.5], [289.5, 271.4765625], [54.0, 271.4765625]]}, {"title": "3.4. Model Pre-training", "heading_level": null, "page_id": 3, "polygon": [[54.0, 606.75], [156.0, 606.75], [156.0, 616.81640625], [54.0, 616.81640625]]}, {"title": "4. <PERSON>", "heading_level": null, "page_id": 5, "polygon": [[54.0, 401.4140625], [129.0, 401.4140625], [129.0, 413.7890625], [54.0, 413.7890625]]}, {"title": "4.1. Instructed Zero-shot Image-to-Text Generation", "heading_level": null, "page_id": 5, "polygon": [[54.0, 490.74609375], [275.25, 490.74609375], [275.25, 501.57421875], [54.0, 501.57421875]]}, {"title": "Effect of Vision-Language Representation Learning.", "heading_level": null, "page_id": 5, "polygon": [[305.40234375, 654.328125], [530.25, 654.328125], [530.25, 665.15625], [305.40234375, 665.15625]]}, {"title": "4.2. Image Captioning", "heading_level": null, "page_id": 6, "polygon": [[54.0, 550.5], [150.75, 550.5], [150.75, 561.12890625], [54.0, 561.12890625]]}, {"title": "4.3. Visual Question Answering", "heading_level": null, "page_id": 6, "polygon": [[306.0, 515.25], [442.5, 515.25], [442.5, 524.77734375], [306.0, 524.77734375]]}, {"title": "4.4. Image-Text Retrieval", "heading_level": null, "page_id": 7, "polygon": [[54.0, 401.02734375], [163.5, 401.02734375], [163.5, 411.85546875], [54.0, 411.85546875]]}, {"title": "5. Lim<PERSON>", "heading_level": null, "page_id": 7, "polygon": [[305.25, 291.75], [375.0, 291.75], [375.0, 302.994140625], [305.25, 302.994140625]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[306.0, 591.6796875], [377.25, 591.6796875], [377.25, 603.28125], [306.0, 603.28125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 67.91748046875], [111.75, 67.91748046875], [111.75, 79.42236328125], [54.0, 79.42236328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 101], ["Text", 7], ["SectionHeader", 4], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5460, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 101], ["Text", 8], ["SectionHeader", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 138], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 111], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 118], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1282, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["TableCell", 254], ["Line", 83], ["Text", 6], ["Caption", 3], ["SectionHeader", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 412], ["TableCell", 367], ["Line", 107], ["Text", 7], ["Caption", 3], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 18737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 464], ["Span", 347], ["Line", 89], ["Text", 8], ["SectionHeader", 3], ["Table", 2], ["Reference", 2], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13003, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 96], ["ListItem", 18], ["Reference", 18], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 95], ["ListItem", 22], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 58], ["Line", 25], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 250], ["Span", 226], ["Line", 67], ["Table", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5463, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 47], ["Line", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 662, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BLIP-2- Bootstrapping Language-Image Pre-training with Frozen Image Encoders and Large Language Models"}