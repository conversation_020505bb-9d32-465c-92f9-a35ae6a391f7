{"table_of_contents": [{"title": "Smoothness", "heading_level": null, "page_id": 0, "polygon": [[133.5, 98.25], [219.7880859375, 98.25], [219.7880859375, 111.2783203125], [133.5, 111.2783203125]]}, {"title": "296 12 Smoothness", "heading_level": null, "page_id": 1, "polygon": [[133.27734375, 26.24853515625], [229.798828125, 26.24853515625], [229.798828125, 35.384765625], [133.27734375, 35.384765625]]}, {"title": "<PERSON><PERSON><PERSON><PERSON>'s counterexample", "heading_level": null, "page_id": 2, "polygon": [[133.5, 447.0], [294.75, 447.0], [294.75, 458.26171875], [133.5, 458.26171875]]}, {"title": "298 12 Smoothness", "heading_level": null, "page_id": 3, "polygon": [[133.5, 26.25], [229.0517578125, 26.25], [229.0517578125, 35.408935546875], [133.5, 35.408935546875]]}, {"title": "<PERSON><PERSON>'s counterexample", "heading_level": null, "page_id": 4, "polygon": [[133.5, 411.75], [282.5419921875, 411.75], [282.5419921875, 423.0703125], [133.5, 423.0703125]]}, {"title": "300 12 Smoothness", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.25], [229.6494140625, 26.25], [229.6494140625, 35.070556640625], [133.5, 35.070556640625]]}, {"title": "302 12 Smoothness", "heading_level": null, "page_id": 7, "polygon": [[133.4267578125, 26.031005859375], [229.3505859375, 26.031005859375], [229.3505859375, 35.457275390625], [133.4267578125, 35.457275390625]]}, {"title": "", "heading_level": null, "page_id": 7, "polygon": [[460.79296875, 275.73046875], [470.35546875, 275.73046875], [470.35546875, 285.3984375], [460.79296875, 285.3984375]]}, {"title": "Smoothness and Assumption (C)", "heading_level": null, "page_id": 7, "polygon": [[133.5, 337.9921875], [331.1015625, 337.9921875], [331.1015625, 349.59375], [133.5, 349.59375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 46], ["Text", 3], ["Equation", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2375, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 50], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 38], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 92], ["TextInlineMath", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 34], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 37], ["Text", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 759], ["Line", 55], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 535], ["Line", 39], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 632], ["Line", 43], ["TextInlineMath", 6], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 93], ["Equation", 5], ["TextInlineMath", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1681, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-17"}