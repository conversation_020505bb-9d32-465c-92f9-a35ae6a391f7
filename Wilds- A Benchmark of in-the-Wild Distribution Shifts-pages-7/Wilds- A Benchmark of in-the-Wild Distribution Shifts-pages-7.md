improve model performance (<PERSON><PERSON> et al., 2018), and we welcome submissions that explore this direction.

## E.1.3 Broader context

Differences across data distributions at different sensor locations is a common challenge in automated wildlife monitoring applications, including using audio sensors to monitor animals that are easier heard than seen such as primates, birds, and marine mammals (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2020), and using static sonar to count fish underwater to help maintain sustainable fishing industries (<PERSON><PERSON> et al., 2012; <PERSON><PERSON> et al., 2018; Schneider and Zhuang, 2020). As with camera traps, each static audio sensor has a specific species distribution as well as a sensor specific background noise signature, making generalization to new sensors challenging. Similarly, static sonar used to measure fish escapement have sensor-specific background reflectance based on the shape of the river bottom. Moreover, since species are distributed in a non-uniform and long-tailed fashion across the globe, it is incredibly challenging to collect sufficient samples for rare species to escape the low-data regime. Implicitly representing camera-specific distributions and background features in per-camera memory banks and extracting relevant information from these via attention has been shown to help overcome some of these challenges for static cameras (<PERSON><PERSON> et al., 2020b).

More broadly, shifts in background, image illumination and viewpoint have been studied in computer vision research. First, several works have shown that object classifiers often rely on the background rather than the object to make its classification (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2019; <PERSON> et al., 2020). Second, common perturbations such as blurriness or shifts in illumination, tend to reduce performance (<PERSON> and Karam, 2017; Temel et al., 2018; Hendrycks and Dietterich, 2019). Finally, shifts in rotation and viewpoint of the object has been shown to degrade performance (Barbu et al., 2019).

## E.1.4 ADDITIONAL DETAILS

Data processing. We generate the data splits in three steps. First, to generate the OOD splits, we randomly split all locations into three groups: Validation (OOD), Test (OOD), and Others. Then, to generate the train-to-train ID splits, we split the Others group uniformly by date at random into three sets: Training, Validation (ID), and Test (ID).

When doing the ID split, some locations only ended up in some of but not all of Training, Validation (ID), and Test (ID). For instance, if there were very few dates for a specific location (camera trap), it may be that no examples from that location ended up in the train split. This defeats the purpose of the ID split, which is to test performance on locations that were seen during training. We therefore put these locations in the train split. Finally, any images in the test set with classes not present in the train set were removed.

Modifications to the original dataset. The original iWildCam 2020 Kaggle competition similarly split the dataset by camera trap, though the competition focused on average accuracy. We consider a smaller subset of the data here. Specifically, the Kaggle competition uses a held-out test set that we are not utilizing, as the test set is intended to be reused in a future competition and is not yet public. Instead, we constructed our own test set by splitting the Kaggle competition training data into our own splits: train, validation (ID), validation (OOD), test (ID), test (OOD).

Images are organized into sequences, but we treat each image separately. In the iWildCam 2020 competition, the top participants utilized the sequence data and also used a pretrained MegaDetector animal detection model that outputs bounding boxes over the animals. These images are cropped using the bounding boxes and then fed into a classification network. As we discuss above, we intentionally do not use MegaDetector in our experiments.

In addition, compared to the iWildCam 2020 competition, the iWildCam 2021 competition changed several class definitions (such as removing the "unknown" class) and removed some images that were taken indoors or had humans in the background. We have applied these updates to iWildCam2020-wilds as well.

# E.2 Camelyon17-wilds

Models for medical applications are often trained on data from a small number of hospitals, but with the goal of being deployed more generally across other hospitals. However, variations in data collection and processing can degrade model accuracy on data from new hospital deployments (Zech et al., 2018; AlBadawy et al., 2018). In histopathology applications—studying tissue slides under a microscope—this variation can arise from sources like differences in the patient population or in slide staining and image acquisition (Veta et al., 2016; Komura and Ishikawa, 2018; Tellez et al., 2019).

We study this shift on a patch-based variant of the Camelyon17 dataset (Bandi et al., 2018).

## E.2.1 SETUP

Problem setting. We consider the domain generalization setting, where the domains are hospitals, and our goal is to learn models that generalize to data from a hospital that is not in the training set (Figure 4). The task is to predict if a given region of tissue contains any tumor tissue, which we model as binary classification. Concretely, the input x is a  $96x96$  histopathological image, the label y is a binary indicator of whether the central 32x32 region contains any tumor tissue, and the domain d is an integer that identifies the hospital that the patch was taken from.

Data. The dataset comprises 450,000 patches extracted from 50 whole-slide images (WSIs) of breast cancer metastases in lymph node sections, with 10 WSIs from each of 5 hospitals in the Netherlands. Each WSI was manually annotated with tumor regions by pathologists, and the resulting segmentation masks were used to determine the labels for each patch. We also provide metadata on which slide (WSI) each patch was taken from, though our baseline algorithms do not use this metadata.

We split the dataset by domain (i.e., which hospital the patches were taken from):

- 1. Training: 302,436 patches taken from 30 WSIs, with 10 WSIs from each of the 3 hospitals in the training set.
- 2. Validation (OOD): 34,904 patches taken from 10 WSIs from the 4th hospital. These WSIs are distinct from those in the other splits.
- 3. Test (OOD): 85,054 patches taken from 10 WSIs from the 5th hospital, which was chosen because its patches were the most visually distinctive. These WSIs are also distinct from those in the other splits.
- 4. Validation (ID): 33,560 patches taken from the same 30 WSIs from the training hospitals.

We do not provide a Test (ID) set, as there is no practical setting in which we would have labels on a uniformly randomly sampled set of patches from a WSI, but no labels on the other patches from the same WSI.

Evaluation. We evaluate models by their average test accuracy across patches. Histopathology datasets can be unwieldy for ML models, as individual images can be several gigabytes large; extracting patches involves many design choices; the classes are typically very unbalanced; and evaluation often relies on more complex slide-level measures such as the free-response receiver operating characteristic (FROC) (Gurcan et al., 2009). To improve accessibility, we pre-process the slides into patches and balance the dataset so that each split has a 50/50 class balance, making average accuracy is a reasonable measure of performance (Veeling et al., 2018; Tellez et al., 2019).

<span id="page-2-0"></span>Table 5: Baseline results on CAMELYON17-WILDS. In-distribution (ID) results correspond to the train-to-train setting. Parentheses show standard deviation across 10 replicates. Note that the standard error of the mean setting. Parentneses show stand<br>is smaller (by a factor of  $\sqrt{10}$ ).

| Algorithm | Validation (ID) accuracy | Validation (OOD) accuracy | Test (OOD) accuracy |
|-----------|--------------------------|---------------------------|---------------------|
| ERM       | 93.2 (5.2)               | 84.9 (3.1)                | <b>70.3</b> (6.4)   |
| CORAL     | 95.4 (3.6)               | 86.2 (1.4)                | 59.5 (7.7)          |
| IRM       | 91.6 (7.7)               | 86.2 (1.4)                | 64.2 (8.1)          |
| Group DRO | 93.7 (5.2)               | 85.5 (2.2)                | 68.4 (7.3)          |

<span id="page-2-1"></span>Table 6: Mixed-to-test comparison for ERM models on CAMELYON17-WILDS. In the official OOD setting, we train on data from three hospitals and evaluate on a different test hospital, whereas in the mixed-to-test ID setting, we add data from one extra slide from the test hospital to the training set. The official Test (OOD) set has data from 10 slides, but for this comparison, we report performance for both splits on the same 9 slides (without the slide that was moved to the training set). This makes the numbers (71.0 vs. 70.3) for the official split slightly different from Table [5.](#page-2-0) Parentheses show standard deviation across 10 replicates. Note omcial split signtly different from Table 5. Parentnesses show standard error of the mean is smaller (by a factor of  $\sqrt{10}$ ).

| Setting                                      | Algorithm | Test (OOD) accuracy |
|----------------------------------------------|-----------|---------------------|
| Official (train on ID examples)              | ERM       | 71.0 (6.3)          |
| Mixed-to-test (train on $ID + OOD$ examples) | ERM       | 82.9 (9.8)          |

Potential leverage. Prior work has shown that differences in staining between hospitals are the primary source of variation in this dataset, and that specialized stain augmentation methods can close the in- and out-of-distribution accuracy gap on a variant of the dataset based on the same underlying slides (Tellez et al., 2019). However, the general task of learning histopathological models that are robust to variation across hospitals (from staining and other sources) is still an open research question. In this way, the CAMELYON17-WILDS dataset is a controlled testbed for general-purpose methods that can learn to be robust to stain variation between hospitals, given a training set from multiple hospitals.

### E.2.2 Baseline results

Model. For all experiments, we use DenseNet-121 models (Huang et al., 2017) models trained from scratch on the  $96 \times 96$  patches, following prior work (Veeling et al., 2018). These models used a learning rate of  $10^{-3}$ ,  $L_2$ -regularization strength of  $10^{-2}$ , a batch size of 32, and SGD with momentum (set to 0.9), trained for 5 epochs with early stopping. We selected hyperparameters by a grid search over learning rates  $\{10^{-4}, 10^{-3}, 10^{-2}\}$ , and L<sub>2</sub>-regularization strengths  $\{0, 10^{-3}, 10^{-2}\}$ . We report results aggregated over 10 random seeds.

ERM results and performance drops. Table [5](#page-2-0) shows that the model was consistently accurate on the train-to-train in-distribution (ID) validation set and to a lesser extent on the out-of-distribution (OOD) validation set, which was from a held-out hospital. However, it was wildly inconsistent on the test set, which was from a different held-out hospital, with a standard deviation of 6.4% in accuracies across 10 random seeds. There is a large gap between train-to-train ID validation and OOD validation accuracy, and between OOD validation and OOD test accuracy (in part because we early stop on the highest OOD validation accuracy). Nevertheless, we found that using the OOD validation set gave better results than using the ID validation set; see Appendix D.1 for more discussion.

<span id="page-3-0"></span>Image /page/3/Figure/0 description: A scatter plot shows the relationship between validation (OOD) accuracy on the x-axis and test (OOD) accuracy on the y-axis. The x-axis ranges from 0.82 to 0.88, and the y-axis ranges from 0.625 to 0.825. There are ten data points plotted on the graph. The points are clustered in the upper left and lower right sections of the plot. Specifically, there are points at approximately (0.815, 0.74), (0.815, 0.66), (0.845, 0.625), (0.845, 0.63), (0.845, 0.815), (0.87, 0.75), (0.87, 0.73), (0.875, 0.71), (0.88, 0.745), and (0.88, 0.63).

Figure 18: Test (OOD) accuracy versus validation (OOD) accuracy for different random seeds on CAMELYON17wilds, using the same hyperparameters. The test accuracy is far more variable than the validation accuracy (note the differences in the axes), in part because we early stop on the highest OOD validation accuracy.

We ran an additional mixed-to-test comparison, where we moved 1 of the 10 slides<sup>7</sup> from the test hospital to the training set and tested on the patches from the remaining 9 slides. The mixed-to-test setting gives significantly higher accuracy on the reduced test set (Table [6\)](#page-2-1), suggesting that the observed performance drop is due to the distribution shift, as opposed to the intrinsic difficulty of the examples from the test hospital. We note that this mixed-to-test comparison mixes in only a small amount of test data and is therefore likely to be an underestimate of in-distribution performance on the test set; we opted to only mix in 1 slide so as to preserve enough test examples to be able to accurately estimate model performance.

Additional baseline methods. We trained models with CORAL, IRM, and Group DRO, treating each hospital as a domain. However, they performed comparably or worse than the ERM baseline. For the CORAL and IRM models, our grid search selected the lowest values of their penalty weights (0.1 and 1, respectively) based on OOD validation accuracy.

Discussion. These results demonstrate a subtle failure mode when considering out-of-distribution accuracy: there are models (i.e., choices of hyperparameters and random seeds) that do well both inand out-of-distribution, but we cannot reliably choose these models from just the training/validation set. Due to the substantial variability in test accuracy on CAMELYON17-WILDS (see Figure [18\)](#page-3-0), we ask researchers to submit leaderboard submissions with results from 10 random seeds, instead of the 3 random seeds required for other datasets.

Many specialized methods have been developed to handle stain variation in the context of digital histopathology. These typically fall into one of two categories: data augmentation methods that perturb the colors in the training images (e.g., Liu et al. (2017); Bug et al. (2017); Tellez et al. (2018)) or stain normalization methods that seek to standardize colors across training images (e.g., Macenko et al. (2009); BenTaieb and Hamarneh (2017)). These methods are reasonably effective at mitigating stain variation, at least in some contexts (Tellez et al., 2019; Miller et al., 2021), though the general problem of learning digital histopathology models that can be effectively deployed across multiple hospitals/sites is still an open challenge.

To facilitate more controlled experiments, we will have two leaderboard tracks for Camelyon17 wilds. For the first track, which focuses on general-purpose algorithms, submissions should not use color-specific techniques (e.g., color augmentation) and should also train their models from

<sup>7.</sup> This slide was randomly chosen and corresponded to about 6% of the test patches; some slides contribute more patches than others because they contain larger tumor regions.

scratch, instead of fine-tuning models that are pre-trained from ImageNet or other datasets. For the second track, submissions can use any of those techniques, including specialized methods for dealing with stain variation. These separate tracks will help to disentangle the contributions of more general-purpose learning algorithms and model architectures from the contributions of specialized augmentation techniques or additional training data.

## E.2.3 Broader context

Other than stain variation, there are many other distribution shifts that might occur in histopathology applications. For example, patient demographics might differ from hospital to hospital: some hospitals might tend to see patients who are older or more sick, and patients from different backgrounds and countries vary in terms of cancer susceptibility (Henderson et al., 2012). Some cancer subtypes and tissues of origin are also more common than others, leading to potential subpopulation shift issues, e.g,. a rare cancer subtype in one context might be more common in another; or even if it remains rare, we would seek to leverage the greater quantity of data from other subtypes to improve model accuracy on the rare subtype (Weinstein et al., 2013).

Beyond histopathology, variation between different hospitals and deployment sites has also been shown to degrade model accuracy in other medical applications such as diabetic retinopathy (Beede et al., 2020) and chest radiographs (Zech et al., 2018; Phillips et al., 2020), including recent work on COVID-19 detection (DeGrave et al., 2020). Even within the same hospital, process variables like which scanner/technician took the image can significantly affect models (Badgeley et al., 2019).

In these medical applications, the gold standard is to evaluate models on an independent test set collected from a different hospital (e.g., Beck et al. (2011); Liu et al. (2017); Courtiol et al.  $(2019)$ ; McKinney et al.  $(2020)$ ) or at least with a different scanner within the same hospital (e.g., Campanella et al. (2019)). However, this practice has not been ubiquitous due to the difficulty of obtaining data spanning multiple hospitals (Esteva et al., 2017; Bejnordi et al., 2017; Codella et al., 2019; Veta et al., 2019). The baseline results reported above show that even evaluating on a single different hospital might be insufficient, as results can vary widely between different hospitals (e.g., between the validation and test OOD datasets). We hope that the CAMELYON17-WILDS dataset, which has multiple hospitals in the training set and independent hospitals in the validation and test sets, will be useful for developing models that can generalize reliably to new hospitals and contexts (Chen et al., 2020).

### E.2.4 ADDITIONAL DETAILS

Data processing. The CAMELYON17-WILDS dataset is adapted from whole-slide images (WSIs) of breast cancer metastases in lymph nodes sections, obtained from the CAMELYON17 challenge (Bandi et al., 2018). Each split is balanced to have an equal number of positive and negative examples. The varying number of patches per slide and hospital is due to this class balancing, as some slides have fewer tumor (positive) patches. We selected the test set hospital as the one whose patches were visually most distinct; the difference in test versus OOD validation performance shows that the choice of OOD hospital can significantly affect performance.

From these WSIs, we extracted patches in a standard manner, similar to Veeling et al. (2018). The WSIs were scanned at a resolution of  $0.23 \mu m - 0.25 \mu m$  in the original dataset, and each WSI contains multiple resolution levels, with approximately  $10,000 \times 20,000$  pixels at the highest resolution level (Bandi et al., 2018). We used the third-highest resolution level, corresponding to reducing the size of each dimension by a factor of 4. We then tiled each slide with overlapping  $96 \times 96$  pixel patches with a step size of 32 pixels in each direction (such that none of the central  $32\times32$  regions overlap). labeling them as the following:

• Tumor patches have at least one pixel of tumor tissue in the central  $32 \times 32$  region. We used the pathologist-annotated tumor annotations provided with the WSIs.

• *Normal* patches have no tumor and have at least  $20\%$  normal tissue in the central  $32\times32$  region. We used Otsu thresholding to distinguish normal tissue from background.

We discarded all patches that had no tumor and  $\langle 20\%$  normal tissue in the central  $32\times32$  region.

To maintain an equal class balance, we then subsampled the extracted patches in the following way. First, for each WSI, we kept all tumor patches unless the WSI had fewer normal than tumor patches, which was the case for a single WSI; in that case, we randomly discarded tumor patches from that WSI until the numbers of tumor and normal patches were equal. Then, we randomly selected normal patches for inclusion such that for each hospital and split, there was an equal number of tumor and normal patches.

Modifications to the original dataset. The task in the original CAMELYON17 challenge (Bandi et al., 2018) was the patient-level classification task of determining the pathologic lymph node stage of the tumor present in all slides from a patient. In contrast, our task is a lesion-level classification task. Patient-level, slide-level, and lesion-level tasks are all common in histopathology applications. As mentioned above, the original dataset provided WSIs and tumor annotations, but not a standardized set of patches, which we provide here. Moreover, it did not consider distribution shifts; both of the original training and test splits contained slides from all 5 hospitals.

The Camelyon17-wilds patch-based dataset is similar to one of the datasets used in Tellez et al. (2019), which was also derived from the CAMELYON17 challenge; there, only one hospital is used as the training set, and the other hospitals are all part of the test set. CAMELYON17-WILDS is also similar to PCam (Veeling et al., 2018), which is a patch-based dataset based on an earlier CAMELYON16 challenge; the data there is derived from only two hospitals.

Additional data sources. The full, original CAMELYON17 dataset contains 1000 WSIs from the same 5 hospitals, although only 50 of them (which we use here) have tumor annotations. The other 950 WSIs may be used as unlabeled data. Beyond the CAMELYON17 dataset, the largest source of unlabeled WSI data is the Cancer Genome Atlas (Weinstein et al., 2013), which typically has patient-level annotations (e.g., patient demographics and clinical outcomes).

# E.3 RxRx1-wilds

High-throughput screening techniques that can generate large amounts of data are now common in many fields of biology, including transcriptomics (Harrill et al., 2019), genomics (Echeverri and Perrimon, 2006; Zhou et al., 2014), proteomics and metabolomics (Taylor et al., 2021), and drug discovery (Broach et al., 1996; Macarron et al., 2011; Swinney and Anthony, 2011; Boutros et al., 2015). Such large volumes of data, however, need to be created in experimental batches, or groups of experiments executed at similar times under similar conditions. Despite attempts to carefully control experimental variables such as temperature, humidity, and reagent concentration, measurements from these screens are confounded by technical artifacts that arise from differences in the execution of each batch. These batch effects make it difficult to draw conclusions from data across experimental batches (Leek et al., 2010; Parker and Leek, 2012; Soneson et al., 2014; Nygaard et al., 2016; Caicedo et al., 2017).

We study the shift induced by batch effects on a variant of the RXRX1-WILDS dataset (Taylor et al., 2019). As illustrated in Figure 5, there are significant visual differences between experimental batches, making recognizing siRNA perturbations in OOD experiments in the RxRx1-wilds dataset a particularly challenging task for existing ML algorithms.

<span id="page-5-0"></span>

## E.3.1 SETUP

Problem setting. We consider the domain generalization setting, where the domains are experimental batches and we seek to generalize to images from unseen experimental batches. Concretely, the input  $x$  is a 3-channel image of cells obtained by fluorescent microscopy, the label  $y$  indicates

<span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays a grid of seven microscopy images, likely representing different fluorescent channels of the same cell sample. The top row contains three images: the first is a composite image showing cells stained with multiple fluorescent markers, appearing in green, blue, and red. The second image in the top row shows cell nuclei stained blue. The third image in the top row displays cells stained green. The bottom row contains four images. The first image in the bottom row shows cells stained cyan. The second image in the bottom row shows cells stained magenta. The third image in the bottom row shows cells stained yellow. The fourth image in the bottom row is not visible in the provided crops.

Figure 19: 6-channel composite image of HUVEC cells (left) and its individual channels (rest): nuclei (blue), endoplasmic reticuli (green), actin (red), nucleoli and cytoplasmic RNA (cyan), mitochondria (magenta), and Golgi (yellow). The overlap in channel content is due in part to the lack of complete spectral separation between fluorescent stains. Note that only the first 3 channels are included in RxRx1-willers.

which of the 1,139 genetic treatments (including no treatment) the cells received, and the domain  $d$ specifies the experimental batch of the image.

Data. RxRx1-wilds was created by Recursion (recursion.com) in its automated high-throughput screening laboratory in Salt Lake City, Utah. It is comprised of fluorescent microscopy images of human cells in four different cell lines: HUVEC, RPE, HepG2, and U2OS. These were acquired via fluorescent microscopy using a 6-channel variant of the Cell Painting assay (Bray et al., 2016). Figure [19](#page-6-0) shows an example of the cellular contents of each of these 6 channels: nuclei, endoplasmic reticuli, actin, nucleoli and cytoplasmic RNA, mitochondria, and Golgi. To make the dataset smaller and more accessible, we only included the first 3 channels in RxRx1-WILDS.

The images in RxRx1-wilds are the result of executing the same experimental design 51 different times, each in a different batch of experiments. The design consists of four 384-well plates, where individual wells are used to isolate populations of cells on each plate (see Figure [20\)](#page-7-0). The wells are laid out in a  $16\times24$  grid, but only the wells in the inner  $14\times22$  grid are used since the outer wells are most susceptible to environmental factors. Of these 308 usable wells, one is left untreated to provide a negative control phenotype, while the rest are treated with small interfering ribonucleic acid, or siRNA, at a fixed concentration. Each siRNA is designed to knockdown a single target gene via the RNA interference pathway, reducing the expression of the gene and its associated protein (Tuschl, 2001). However, siRNAs are known to have significant but consistent off-target effects via the microRNA pathway, creating partial knockdown of many other genes as well. The overall effect of siRNA transfection is to perturb the morphology, count, and distribution of cells, creating a phenotype associated with each siRNA. The phenotype is sometimes visually recognizable, but often the effects are subtle and hard to detect.

In each plate, 30 wells are set aside for 30 *positive control* siRNAs. Each has a different gene as its primary target, which together with the single untreated well already mentioned, provides a set of reference phenotypes per plate. Each of the remaining 1,108 wells of the design (277 wells  $\times$  4 plates) receives one of 1,108 treatment siRNA, respectively, so that there is at most one well of each treatment siRNA in each experiment. We say at most once because, although rare, it happens that either an siRNA is not correctly transferred into the designated destination well, resulting in an

<span id="page-7-0"></span>Image /page/7/Picture/0 description: The image displays a large black rectangle with a grid pattern, measuring 86mm on its side. A magnified view of a small section of this grid is shown, which is further zoomed in to reveal a 2mm square divided into four smaller squares. Below this, a stack of layered images is presented, with the top layer showing a detailed, colorful microscopic view, and subsequent layers becoming progressively darker and more abstract.

Figure 20: Schematic of a 384-well plate demonstrating imaging sites and 6-channel images. The 4-plate experiments in RxRx1-WILDS were run in the wells of such 384-well plates. RxRx1-WILDS contains two imaging sites per well.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: The image displays four panels of fluorescent microscopy images of cells. The first panel shows cells with nuclei stained blue and cytoplasm stained green, with some red staining at the cell periphery. The second panel shows cells with nuclei stained blue and a significant amount of red staining throughout the cytoplasm and cell membranes. The third panel shows cells with blue nuclei and scattered green and red fluorescent signals within the cytoplasm. The fourth panel shows cells with blue nuclei and numerous bright white punctate signals scattered across the image, along with some red staining in the background.

Figure 21: Images of the same siRNA in four cell types, from left to right: HUVEC, RPE, HepG2, U2OS.

additional untreated well, or an operational error is detected by quality control procedures that render the well unsuitable for inclusion in the dataset.

Each experiment was run in a single cell type, and of the 51 experiments in RxRx1-WILDS, 24 are in HUVEC, 11 in RPE, 11 in HepG2, and 5 in U2OS. Figure [21](#page-7-1) shows the phenotype of the same siRNA in each of these four cell types.

We split the dataset by experimental batches, with the training and test splits having roughly the same composition of cell types:

- 1. Training: 33 experiments (16 HUVEC, 7 RPE, 7 HepG2, 3 U2OS), site 1 only  $= 40,612$  images.
- 2. Validation  $(OOD)$ : 4 experiments (1 HUVEC, 1 RPE, 1 HepG2, 3 U2OS), sites 1 and  $2 = 9.854$ images.
- 3. Test (OOD): 14 experiments (7 HUVEC, 3 RPE, 3 HepG2, 1 U2OS), sites 1 and  $2 = 34,432$ images.
- 4. Test (ID): same 33 experiments as in the training set, site 2 only  $= 40,612$  images.

In addition to the class (siRNA), each image is associated with the following metadata: cell type, experiment, plate, well, and site. We emphasize that all the images of an experiment are found in exactly one of the training, validation (OOD) or test (OOD) splits. See Appendix [E.3.4](#page-10-0) for more data processing details.

Evaluation. We evaluate models by their average accuracy across test images. Note that there are two images per well in the test set, which we evaluate independently.

The cell types are not balanced in the training and test sets. Correspondingly, we observed higher performance on the HUVEC cell type, which is over-represented, and lower performance on the U2OS cell type, which is under-represented. While maintaining high performance on minority (or even unseen) cell types is an important problem, for RxRx1-WILDS, we opt to measure the average accuracy across all experiments instead of, for example, the worst accuracy across cell types. This is because the relatively small amount of training data available from the minority cell type (U2OS) makes it challenging to cast RXRX1-WILDS as a tractable subpopulation shift problem. We also note that the difference in performance across cell types leads to the validation performance being significantly lower than the test performance, as there is a comparatively smaller fraction of HUVEC and a comparatively higher fraction of U2OS.

Potential leverage. By design, there is usually one sample per class per experiment in the training set, with the following exceptions: 1) there are usually four samples per positive control, though 2) samples may be missing, as described above. Moreover, while batch effects can manifest themselves in many complicated ways, it is the case that the training set consists of a large number of experiments selected randomly amongst all experiments in the dataset, hence we expect models to be able to learn what is common amongst all such samples per cell type, and for that ability to generalize to to test batches. We emphasize that, whether in the training or test sets, the same cell types are perturbed with the same siRNA, and thus the phenotypic distributions for each batch share much of the same generative process.

We also note that, while not exploited here, there is quite a bit of structure in the RXRX1wilds dataset. For example, except in the case of errors, all treatment siRNA appear once in each experiment, and all control conditions appear once per plate, so four times per experiment. Also, due to the operational efficiencies gained, the 1,108 treatment siRNAs always appear in the same four groups of 277 per experiment. So while the particular well an siRNA appears in is randomized, it will always appear with the same group of 276 other siRNAs. This structure can be exploited for improving predictive accuracy via post-prediction methods such as linear sum assignment. However, such methods do not represent improved generalization to OOD samples, and should be avoided.

### E.3.2 Baseline results

Model. For all experiments, we train the standard ResNet-50 model (He et al., 2016) pretrained on ImageNet, using a learning rate of  $1e - 4$  and  $L_2$ -regularization strength of  $1e - 5$ . We trained these models with the Adam optimizer, using default parameter values  $\beta_1 = 0.9$  and  $\beta_2 = 0.999$ , with a batch size of 75 for 90 epochs, linearly increasing the learning rate for 10 epochs, then decreasing it following a cosine learning rate schedule. We selected hyperparameters by a grid search over learning rates  $\{10^{-5}, 10^{-4}, 10^{-3}\}, L_2$ -regularization strengths  $\{10^{-5}, 10^{-3}\},$  and numbers of warmup epochs {5, 10}. We report results aggregated over 3 random seeds.

ERM results and performance drops. Model performance dropped significantly going from the train-to-train in-distribution (ID) setting to the official out-of-distribution (OOD) setting (Table [7\)](#page-9-0), with an average accuracy of 35.9% on the ID test set but only 29.9% on the OOD test set for ERM.

We ran an additional mixed-to-test comparison, where we moved half of the OOD test set into the training set, while keeping the overall amount of training data the same. Specifically, we moved one site per experiment from the OOD test set into the training set, and discarded an equivalent number of training sites, while leaving the validation set unchanged. While the test set in the mixed-to-test setting is effectively half as large as in the standard split, we expect it to be distributed similarly, since the two test set versions comprise the same 14 experiments.

Table [8](#page-9-1) shows that there is a large gap between the OOD test accuracies in the official setting  $(29.9\%)$  and the test accuracies in the mixed-to-test setting  $(39.8\%)$ . We note that the latter is higher

<span id="page-9-0"></span>Table 7: Baseline results on RxRx1-WILDS. In-distribution (ID) results correspond to the train-to-train setting. Parentheses show standard deviation across 3 replicates.

| Algorithm | Validation (OOD) accuracy | Test (ID) accuracy | Test (OOD) accuracy |
|-----------|---------------------------|--------------------|---------------------|
| ERM       | 19.4 (0.2)                | 35.9 (0.4)         | <b>29.9</b> (0.4)   |
| CORAL     | 18.5 (0.4)                | 34.0 (0.3)         | 28.4 (0.3)          |
| IRM       | 5.6 (0.4)                 | 9.9 (1.4)          | 8.2 (1.1)           |
| Group DRO | 15.2 (0.1)                | 28.1 (0.3)         | 23.0 (0.3)          |

<span id="page-9-1"></span>Table 8: Mixed-to-test comparison for ERM models on RxRx1-willes. In the official OOD setting, we train on data from 33 experiments (1 site per experiment) and test on 14 different experiments (2 sites per experiment). In the mixed-to-test setting, we replace 14 of the training experiments with 1 site from each of the test experiments, which keeps the training set size the same, but halves the test set size. Parentheses show standard deviation across 3 replicates.

| Setting                                      | Algorithm | Test (OOD) accuracy |
|----------------------------------------------|-----------|---------------------|
| Official (train on ID examples)              | ERM       | 29.9 (0.4)          |
| Mixed-to-test (train on $ID + OOD$ examples) | ERM       | <b>39.8</b> (0.2)   |

than the train-to-train ID test accuracy of 35.9% reported in Table [7.](#page-9-0) This difference mainly stems from the slight difference in cell type composition between the test sets in the train-to-train and mixed-to-test settings; in particular, the train-to-train test set has a slightly higher proportion of the minority cell type (U2OS), on which performance is worse, and a slightly lower proportion of the majority cell type (HUVEC), on which performance is better. In this sense, the mixed-to-test result of 39.8% is a more accurate reflection of in-distribution performance on this dataset, and the results in Table [7](#page-9-0) therefore understate the magnitude of the distribution shift.

Additional baseline methods. We also trained models with CORAL, IRM, and group DRO, treating each experiment as a domain, and using the same model hyperparameters as ERM. However, the models trained using these methods all performed poorly compared to the ERM model (Table [7\)](#page-9-0). One complication with these methods is that the experiments in the training set comprise different cell types, as mentioned above; this heterogeneity can pose a challenge to methods that treat each domain equivalently.

Discussion. An important observation about batch effects in biological experiments: it is often the case that batch effects are mediated via biological mechanisms. For example, an increase in cellular media concentration may lead to cell growth and proliferation, while the upregulation of proliferation genes will do the same. Thus the "nuisance" factors associated with batch effects are often correlated with the biological signal we are attempting to observe, and cannot be disentangled from the biological factors that explain the data. Correction algorithms should take account of such trade-offs and attempt to optimize for both correction and signal preservation.

### E.3.3 Broader context

As previously mentioned, high-throughput screening techniques are used broadly across many areas of biology, and therefore batch effects are a common problem in fields such as genomics, transcriptomics, proteomics, metabolomics, etc., so a particular solution in one such area may prove to be applicable in many areas of biology (Goh et al., 2017).

There are other datasets that are used in studying batch effects. The one most comparable to RxRx1-wilds is the BBBC021 dataset (Ljosa et al., 2012), which contains 13,200 3-channel fluorescent microscopy images of MCF7 cells acquired across 10 experimental batches. A subset of 103 treatments from 38 drug compounds belonging to 12 known mechanism of action (MoA) groups was first studied in Ando et al. (2017), and has been the subject of subsequent studies (Caicedo et al., 2018; Godinez et al., 2018; Tabak et al., 2020). Note that this dataset differs dramatically from RxRx1, in that there are fewer images, treatments, batches, and cell types, and each batch contains only a small subset of the total treatments.

<span id="page-10-0"></span>

### E.3.4 Additional details

Data processing. RxRx1-WILDS contains two non-overlapping  $256 \times 256$  fields of view per well. Therefore, there could be as many as 125,664 images in the dataset (= 51 experiments  $\times$  4 plates/experiment  $\times$  308 wells/plate  $\times$  2 images/well). 154 images were removed based on data quality, leaving a total dataset of 125,510 images.

Modifications to the original dataset. The underlying raw dataset consists of  $2048 \times 2048$ pixel, 6 channel, 16bpp images. To fit within the constraints of the WILDS benchmark, images for RXRX1-WILDS were first downsampled to  $1024 \times 1024$  and 8bpp, cropped to the center  $256 \times 256$ pixels, and only the first three channels (nuclei, endoplasmic reticuli, actin) were retained. The original RxRx1 dataset, available at rxrx.ai and described in Taylor et al. (2019), provides  $512 \times 512$ center crops of the downsampled images with all 6 channels retained.

The original RxRx1 dataset was also used in a NeurIPS 2019 competition hosted on Kaggle. The validation (OOD) and test (OOD) splits in RxRx1-wilds correspond to the public and private test sets from the Kaggle competition. The original RxRx1 dataset did not have an additional test (ID) split, and thus the original training split had both sites 1 and 2, for a total of 81,442 images. The Kaggle competition also aggregated predictions from both sites to form a single prediction per well, whereas in RxRx1-WILDS, we treat each site separately.

As described in Section [E.3.1,](#page-5-0) each plate in both the training and test sets contains the same 31 control conditions (one untreated well, and 30 positive control siRNAs). The Kaggle competition provided the labels for these control conditions in the test set, expecting that competitors would use them for various domain alignment techniques such as CORAL. However, these labels were instead used by the top competitors to bootstrap pseudo-labeling techniques. For RxRx1-wilds, for consistency with the other datasets and the typical domain generalization setting, we have opted not to release these control test labels for training.

The poor performance reported here on RxRx1-wilds may seem surprising in light of the fact that the top finishers of the Kaggle competition achieved near perfect accuracy on the test (OOD) set. This difference is due to a number of factors, including:

- 1. Adjustments made to the original RxRx1 dataset for RxRx1-wilds, as detailed in this subsection.
- 2. Differences in the network architectures used. To make training on RxRx1-wilds more accessible, we used a less compute-intensive architecture than typical in the competition.
- 3. Differences in training techniques used like pseudo-labeling (using the test control labels, as described above) and batch-level dataset augmentations or ensembling.
- 4. Differences in the way accuracy is measured. In the Kaggle competition, accuracy was measured for each well, meaning site-level predictions were aggregated to well-level predictions, and only for treatment classes, whereas in RxRx1-wilds, for convenience, accuracy is measured at each site and for both treatment and control classes.
- 5. The use of post-prediction methods like linear sum assignment that exploited the particular structure of the experiments in the RxRx1 dataset, as described under Potential Leverage in Section [E.3.1.](#page-5-0)