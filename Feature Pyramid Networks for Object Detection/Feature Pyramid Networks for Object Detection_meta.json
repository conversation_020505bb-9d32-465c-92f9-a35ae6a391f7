{"table_of_contents": [{"title": "Feature Pyramid Networks for Object Detection", "heading_level": null, "page_id": 0, "polygon": [[148.5, 106.5], [445.5, 106.5], [445.5, 119.302734375], [148.5, 119.302734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 248.25], [191.25, 248.25], [191.25, 259.1015625], [144.75, 259.1015625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 526.5], [127.5, 526.5], [127.5, 537.5390625], [48.75, 537.5390625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 528.0], [392.66015625, 528.0], [392.66015625, 539.0859375], [307.5, 539.0859375]]}, {"title": "3. Feature Pyramid Networks", "heading_level": null, "page_id": 2, "polygon": [[48.0, 573.75], [201.75, 573.75], [201.75, 585.87890625], [48.0, 585.87890625]]}, {"title": "4. Applications", "heading_level": null, "page_id": 3, "polygon": [[48.75, 383.625], [126.75, 383.625], [126.75, 394.06640625], [48.75, 394.06640625]]}, {"title": "4.1. Feature Pyramid Networks for RPN", "heading_level": null, "page_id": 3, "polygon": [[48.75, 493.5], [238.5, 493.5], [238.5, 503.89453125], [48.75, 503.89453125]]}, {"title": "4.2. Feature Pyramid Networks for Fast R-CNN", "heading_level": null, "page_id": 3, "polygon": [[307.5, 416.25], [532.810546875, 416.25], [532.810546875, 426.55078125], [307.5, 426.55078125]]}, {"title": "5. Experiments on Object Detection", "heading_level": null, "page_id": 4, "polygon": [[48.75, 290.0390625], [232.5, 290.0390625], [232.5, 300.8671875], [48.75, 300.8671875]]}, {"title": "5.1. Region Proposal with RPN", "heading_level": null, "page_id": 4, "polygon": [[48.75, 462.0], [196.62890625, 462.0], [196.62890625, 472.18359375], [48.75, 472.18359375]]}, {"title": "5.1.1 Ablation Experiments", "heading_level": null, "page_id": 4, "polygon": [[306.896484375, 74.10498046875], [433.001953125, 74.10498046875], [433.001953125, 82.90283203125], [306.896484375, 82.90283203125]]}, {"title": "5.2. Object Detection with Fast/Faster R-CNN", "heading_level": null, "page_id": 5, "polygon": [[48.75, 441.0], [265.5, 441.0], [265.5, 451.30078125], [48.75, 451.30078125]]}, {"title": "5.2.1 Fast R-CNN (on fixed proposals)", "heading_level": null, "page_id": 5, "polygon": [[48.0, 660.0], [219.0, 660.0], [219.0, 669.796875], [48.0, 669.796875]]}, {"title": "5.2.2 Faster R-CNN (on consistent proposals)", "heading_level": null, "page_id": 6, "polygon": [[48.0, 413.25], [249.75, 413.25], [249.75, 423.0703125], [48.0, 423.0703125]]}, {"title": "5.2.3 Comparing with COCO Competition Winners", "heading_level": null, "page_id": 6, "polygon": [[307.5, 510.75], [537.0, 510.75], [537.0, 520.91015625], [307.5, 520.91015625]]}, {"title": "6. Extensions: Segmentation Proposals", "heading_level": null, "page_id": 7, "polygon": [[48.75, 454.5], [248.25, 454.5], [248.25, 464.8359375], [48.75, 464.8359375]]}, {"title": "6.1. Segmentation Proposal Results", "heading_level": null, "page_id": 7, "polygon": [[307.5, 295.5], [473.25, 295.5], [473.25, 304.927734375], [307.5, 304.927734375]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 575.25], [378.75, 575.25], [378.75, 586.265625], [307.5, 586.265625]]}, {"title": "A. Implementation of Segmentation Proposals", "heading_level": null, "page_id": 8, "polygon": [[48.0, 72.75], [285.75, 72.75], [285.75, 83.91796875], [48.0, 83.91796875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[307.5, 219.0], [365.25, 219.0], [365.25, 230.09765625], [307.5, 230.09765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 84], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON>Footer", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5284, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 97], ["Text", 9], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 97], ["Text", 9], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 518], ["Line", 106], ["TextInlineMath", 7], ["Text", 6], ["SectionHeader", 3], ["Reference", 2], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 103], ["Text", 9], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 662], ["TableCell", 349], ["Line", 74], ["Text", 6], ["Reference", 4], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 7284, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 610], ["TableCell", 258], ["Line", 92], ["Text", 7], ["Reference", 3], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12872, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["TableCell", 125], ["Line", 107], ["Text", 8], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["TextInlineMath", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7334, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["Line", 107], ["ListItem", 16], ["Reference", 16], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 66], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Feature Pyramid Networks for Object Detection"}