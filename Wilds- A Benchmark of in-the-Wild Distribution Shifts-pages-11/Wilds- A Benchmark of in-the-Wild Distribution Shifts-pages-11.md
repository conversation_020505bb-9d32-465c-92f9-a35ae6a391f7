## F.2 ENCODE: Transcription factor binding across different cell types

Here we provide details on the transcription factor binding dataset discussed in Section 8.3. Transcription factors (TFs) are regulatory proteins that bind specific DNA elements in the genome to activate or repress transcription of target genes. There are estimated to be approximately 1,600 human TFs, and the binding landscape of each TF can be highly variable across different cell types (<PERSON><PERSON><PERSON> et al., 2016). Understanding how these binding patterns change across different cell types and affect cellular function is critical for understanding the mechanics of dynamic gene regulation across cell types and across healthy and diseased cell states.

Several experimental strategies have been developed to profile genome-wide binding landscapes of individual TFs in specific cell types of interest. However, genome-wide profiling of TF binding is challenging in practice, as it requires large numbers of cells and reagents (e.g., high-affinity antibodies) that are difficult and expensive to acquire. Moreover, profiling each individual TF requires a separate experiment, so it can be prohibitively costly to map out even a few different TFs out of the  $>1000$ in the human genome. Therefore, there has been wide interest in computational approaches that can predict the genome-wide binding maps of multiple TFs in new cell types from a single and more practical genome-wide assay.

DNA sequence is one of the principal determinants of where a TF binds along the genome,<sup>10</sup> and many ML models have been developed to predict TF binding as a function of DNA sequence in a particular cell type (<PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON> and <PERSON><PERSON>, 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2021b). However, even when the DNA sequence is invariant across different cell types (e.g., among cell types from the same organism), the TF binding landscape can still be highly variable (Deplancke et al., 2016). Therefore, TF binding models that only use sequence inputs cannot make different predictions for the same sequence across different cell types; we also need complementary, cell-type-specific inputs to model changes in binding over different cell types.

In this section, we explore the use of genome-wide chromatin accessibility assays such as DNase-seq and ATAC-seq (Boyle et al., 2008; Thurman et al., 2012; Buenrostro et al., 2013), in conjunction with DNA sequence, to predict TF binding. DNA is typically accessible in a highly local and cell-type-specific manner, and in particular, genomic sequences with high accessibility are typically bound by one or more TFs, although the identity of the TF is not directly measured by the experiment (Lee et al., 2004). By measuring chromatin accessibility at each base in the genome in a specific cell type of interest, we can obtain a cell-type-specific profile of binding locations; moreover, these experiments are often cheaper than profiling even a single TF (Minnoye et al., 2021). Our goal is to use this accessibility signal, combined with DNA sequence, to accurately predict the binding patterns of multiple TFs in new cell types.

We study the problem of predicting genome-wide TF binding across different cell types using data from the ENCODE-DREAM Transcription Factor Binding Site Prediction Challenge (Balsubramani et al., 2020).

### F.2.1 SETUP

Problem setting. We consider the domain generalization setting, where the domains are cell types, and we seek to learn models that can generalize to cell types that are not in the training set. The task is to predict if a particular transcription factor (TF) would bind to a particular genomic location in a cell type of interest (Figure [28\)](#page-1-0). The input is DNA sequence (which we assume to be shared across all cell types) and a cell-type-specific biochemical measurement of chromatin accessibility obtained through the DNase-seq assay.

Concretely, we segment the genome into uniformly-sized, overlapping bins that are 200 base pairs (bp) in length, and tiled 50bp apart. Given a TF  $p$ , each genomic bin i in cell type d has a binding

<sup>10.</sup> Most TFs, including the ones we provide in this benchmark, have DNA-binding domains which bind to sequence motifs: short recognition sequences (4-20 bases in length) in the genome with specific binding affinity distributions (Stormo and Zhao, 2010).

status  $y_{i,d}^p \in \{0,1\}$ . Our goal is to predict each bin's binding status as a function of the local DNA sequence  $S_i$  and the local cell-type specific accessibility profile  $A_{i,d}$  (Figure [28\)](#page-1-0). We treat each TF separately, i.e., for each  $p$ , we have separate training and test sets and separate models.

For computational convenience, we consider input examples  $x \in \mathbb{R}^{12800\times 5}$  that each represent a window of 12800 bp and span several genomic bins. The first four columns of x is a binary matrix representing a one-hot encoding of the four bases  $(A, C, G, T)$  of the DNA sequence at that base pair. The 5th column is a real-valued vector representing chromatin accessibility. We tile the central 6400 bp of the window with 128 overlapping bins of length 200 bp, tiled 50 bp apart.<sup>11</sup> Thus, each x is associated with a target output  $y \in \{-1,0,1\}^{128}$  indicating the binding status of the TF at each of these 128 bins. The three possible values at each bin indicate whether the bin is bound, unbound, or ambiguous. During training and testing, we simply ignore ambiguous bins, and only focus on bound or unbound bins. The domain  $d$  is an integer that identifies the cell type.

<span id="page-1-0"></span>Image /page/1/Figure/2 description: The image displays a three-part figure illustrating a predictive model for transcription factor binding. Part (a) shows a predictive model taking chromatin accessibility profile (Ai,d) and DNA sequence (Si) as inputs to predict if a protein p is bound at position i in cell type d. Part (b) visualizes the binding map yi,d of a protein as either unbound (yi = 0) or bound (yi = +1) in cell type d, alongside the DNA sequence and chromatin accessibility signal. It also indicates that the data is binned into 200bp segments every 50bp. Part (c) compares TF binding labels (yd) with TF binding predictions (Fp(Si, Ai,d)) over a sliding window of 12800bp, with an evaluated region of 6400bp, highlighting differences in prediction accuracy.

Figure 28: Setup of the ENCODE benchmark. (a) The predictive model predicts binding of a protein to a location, in a cell type (domain). (b) The input features are DNA sequence and chromatin accessibility, and the labels are assigned over 200 base pair (bp) bins tiling the genome every 50 bp. (c) Each training example is a 12800 bp window, with the 128 middle bins evaluated (spanning 6400 bp).

Data. The dataset comprises (a) genome-wide sequence; (b) TF binding maps for two TFs, JUND and MAX, across a total of 6 and 8 cell types respectively; and (c) an accessibility profile for each cell type. As described above and illustrated in Figure [28,](#page-1-0) we break up these genome-wide data into overlapping 12800 bp windows, which each correspond to a single training example. The central 6400 bp of each 12800 bp window is tiled with overlapping 200 bp bins that each correspond to one coordinate of the corresponding  $y \in \{0, 1\}^{128}$ . These 12800 bp windows are tiled 6400 bp apart, such that each genomic location falls within the central 6400 bp region of exactly one window.

<sup>11.</sup> This ensures that each bin has at least 3200 bp of context on either side of it for prediction.

We split the examples by domain (cell type) as well as by chromosome (a large contiguous subsequence of the genome) within a cell type. In each split, we use one cell type for the test data, one for the validation data, and the remaining cell types for the training data. These domain-wise splits are listed in Table [30,](#page-3-0) and are divided into two types:

- 1. The ENCODE-DREAM splits follow the original challenge setup (Balsubramani et al., 2020) closely in evaluating only on the cell type liver, which is a primary tissue, in contrast to all of the other cell types, which are immortalized cell lines that have been grown outside the body for many generations. This is a more realistic setting in the sense that it is easier to collect data from immortalized cell lines, which we can then use to train a model that predicts TF binding in harder-to-profile primary tissues. However, the fact that none of the training cell types are primary tissues might limit generalization to primary tissues. Moreover, because cell types are highly variable, conclusions drawn from a single liver cell type might not generalize to other cell types.
- 2. We thus also use a *round-robin* set of splits, where we assign each cell type to test and validation sets in a rotating manner. This round-robin evaluation comprises several splits for each TF.

For each split in Table [30,](#page-3-0) the data are divided into training, validation, and test sets by chromosome:

- 1. Training: 323,894 windows per training cell type, before filtering. To improve class balance, we filter out windows with all 128 bins labeled negative, which typically removes over 3/4 of these training windows; the exact number filtered out depends on the split. The training windows are taken from all chromosomes except  $\{1, 2, 8, 9, 11, 21\}.$
- 2. Validation (OOD): 27,051 windows from 1 validation cell type and from chromosomes  $\{2, 9, 11\}$ .
- 3. Test (OOD): 23,109 windows from 1 test cell type and from chromosomes  $\{1, 8, 21\}$ .
- 4. Validation (ID): 27,051 windows in total across all training cell types, from chromosomes  $\{2, 9, 11\}.$
- 5. Test (ID): 23,109 windows in total across all training cell types, from chromosomes  $\{1, 8, 21\}$ .

For computational speed, the Validation (OOD) and Test (OOD) sets above were subsampled by a factor of 3 from the available raw data, while the Validation (ID) and Test (ID) sets were subsampled by a factor of  $3 \times$  the number of training cell types.

Evaluation. We evaluate models by their average precision (AP) in predicting binary binding status (excluding all ambiguous bins). Specifically, we treat each bin as a separate binary classification problem; in other words, we split up each prediction of the 128-dimensional vector  $y$  into at most 128 separate binary predictions after excluding ambiguous bins. We then compute the average precision of the model on this binary classification problem.

The choice of average precision as an evaluation metric is motivated by the class imbalance (low proportion of bound/positive labels) of this binary classification problem over bins. All splits have more than one hundred times as many unbound bins than bound bins (Table [31\)](#page-3-1).

### F.2.2 Baseline results

Model. Our model is a version of the fully convolutional U-Net model for image segmentation (Ronneberger et al., 2015), modified from the architecture in Li and Guan (2019). It is illustrated in Figure [29.](#page-4-0) We train each model using the average cross-entropy loss over the 128 output bins in each example (after excluding ambiguous bins).

For hyperparameters, we searched over the learning rates  $\{10^{-5}, 10^{-4}, 10^{-3}\}$ , and L<sub>2</sub>-regularization strengths  $\{10^{-4}, 10^{-3}, 10^{-2}\}$ . We use 10 replicates (with different random seeds) for all reported results.

<span id="page-3-0"></span>Table 30: List of splits for which we trained models for ENCODE. Performance of models on round-robin splits are averaged to get the summarized results in Table [32.](#page-5-0)

| Split name   | TF name | Test cell type | Validation cell type |
|--------------|---------|----------------|----------------------|
| ENCODE-DREAM | MAX     | liver          | HepG2                |
| round-robin  | MAX     | K562           | liver                |
| round-robin  | MAX     | liver          | A549                 |
| round-robin  | MAX     | A549           | GM12878              |
| round-robin  | MAX     | GM12878        | H1-hESC              |
| round-robin  | MAX     | H1-hESC        | HCT116               |
| round-robin  | MAX     | HCT116         | HeLa-S3              |
| round-robin  | MAX     | HeLa-S3        | HepG2                |
| round-robin  | MAX     | HepG2          | K562                 |
| ENCODE-DREAM | JUND    | liver          | HepG2                |
| round-robin  | JUND    | K562           | liver                |
| round-robin  | JUND    | liver          | MCF-7                |
| round-robin  | JUND    | MCF-7          | HCT116               |
| round-robin  | JUND    | HCT116         | HeLa-S3              |
| round-robin  | JUND    | HeLa-S3        | HepG2                |
| round-robin  | JUND    | HepG2          | K562                 |

<span id="page-3-1"></span>Table 31: Binding site imbalance and uniqueness (across cell types) in binary genome-wide binding datasets in ENCODE. Third column indicates the fraction of (non-ambiguous) bins that are labeled positive. Fourth column indicates the fraction of positive bins (bound sites) that are cell-type-specific: they are bound (or ambiguous) in at most one other cell type. Bins are 200bp wide.

| TF name     | Cell type | Frac. positive bins  | Frac. cell-type-specific binding sites |
|-------------|-----------|----------------------|----------------------------------------|
| <b>MAX</b>  | liver     | $4.90 	imes 10^{-3}$ | 0.518                                  |
| MAX         | HepG2     | $6.20 	imes 10^{-3}$ | 0.331                                  |
| MAX         | K562      | $6.46 	imes 10^{-3}$ | 0.368                                  |
| MAX         | A549      | $5.90 	imes 10^{-3}$ | 0.218                                  |
| MAX         | GM12878   | $1.93 	imes 10^{-3}$ | 0.217                                  |
| MAX         | H1-hESC   | $4.44 	imes 10^{-3}$ | 0.363                                  |
| MAX         | HCT116    | $6.46 	imes 10^{-3}$ | 0.237                                  |
| <b>MAX</b>  | HeLa-S3   | $4.21 	imes 10^{-3}$ | 0.218                                  |
| JUND        | liver     | $4.45 	imes 10^{-3}$ | 0.523                                  |
| <b>JUND</b> | K562      | $3.94 	imes 10^{-3}$ | 0.408                                  |
| <b>JUND</b> | HCT116    | $4.08 	imes 10^{-3}$ | 0.297                                  |
| <b>JUND</b> | HeLa-S3   | $3.60 	imes 10^{-3}$ | 0.323                                  |
| JUND        | HepG2     | $3.54 	imes 10^{-3}$ | 0.513                                  |
| JUND        | MCF-7     | $1.84 	imes 10^{-3}$ | 0.335                                  |

ERM results. Table [32](#page-5-0) and Table [34](#page-6-0) show the results of ERM models trained on each split. On many individual splits (Table [34\)](#page-6-0), the OOD validation and OOD test performance are very different, reflecting the variability across the cell types. On average across the round robin splits, the OOD validation performance is slightly higher than the OOD test performance, as we selected hyperparameters and did early stopping to maximize the former (Table [32\)](#page-5-0). We also observed high variance in training and test performance across random seeds in a few splits (e.g., the K562 / liver split for the transcription factor JUND), which suggests some optimization instability in our training

<span id="page-4-0"></span>Image /page/4/Figure/0 description: This image depicts a U-Net architecture for sequence processing. The architecture consists of a contracting path (encoder) and an expansive path (decoder). The encoder starts with an input sequence length of 12800 and 5 input channels. It progressively downsamples the sequence using max pooling and convolutional layers (Conv1d, kernel size=7, padding=3), reducing the sequence length and increasing the number of channels. For example, the first layer has 12800 sequence length and 15 channels, followed by another layer with 12800 sequence length and 15 channels. Then, it downsamples to 6400 sequence length and 15 channels, followed by two layers of 6400 sequence length and 22 channels. The decoder mirrors the encoder, upsampling the feature maps using 2x upsampling (ConvTranspose1d, kernel size=2, stride=2) and concatenating them with corresponding feature maps from the encoder. The diagram shows several levels of this process, with intermediate sequence lengths and channel counts indicated (e.g., 1600 sequence length with 33 channels, 800 sequence length with 49 channels, 400 sequence length with 73 channels). The decoder also includes convolutional layers (Conv1d, kernel size=200, stride=50) and a final fully connected layer. The output of the network is a sequence of length 128 with 1 channel.

Figure 29: Architecture of the baseline prediction model, based on U-Net. The final layers were modified to collapse the representation down to a single channel and finally convolved with kernel size 200 and stride 50, mimicking the resolution of labels along the genome.

protocol. We also computed the in-distribution baselines in a train-to-train setting, i.e., on the Validation (ID) and Test (ID) splits described above.

We also ran corresponding in-distribution baselines in a test-to-test setting, i.e., we trained ERM models on data from the training chromosomes in the test cell type, and tested it on the same test set comprising data from the test chromosomes in the test cell type.<sup>12</sup> Table [33](#page-5-1) shows these in-distribution results. For the round-robin splits, the difference between the train-to-train and test-to-test settings is that the former trains and tests on mixtures of multiple cell types, whereas the latter trains and tests on individual cell types.

We considered two TFs, MAX and JUND, separately. For the ENCODE-DREAM splits, both TFs showed large ID-OOD performance gaps. However, we opted not to use the ENCODE-DREAM split as a WILDS dataset because the variability between cell types made us cautious about overinterpreting the results on a single cell type. For example, for MAX, we found that the Validation (OOD) and Test (OOD) cell types were so different that their results were anti-correlated across different random seeds, which would have made benchmarking challenging. Moreover, the fact that the test cell type (liver) in the ENCODE-DREAM splits was the only primary tissue might have meant that the training data might have insufficient leverage for a model to learn to close the ID-OOD gap.

We therefore focused on analyzing the round-robin splits. For MAX, using the train-to-train comparison, the average Test (ID) and Test (OOD) AP across the round-robin splits were not significantly different  $(64.9 \ (2.1)$  vs. 59.6  $(2.0)$ , respectively; Table [32\)](#page-5-0). For JUND, using the train-totrain comparison, the average Test (ID) and Test (OOD) AP across the round-robin splits showed a

<sup>12.</sup> Prior work has shown that there is minimal variation in performance between chromosomes on this problem (Won et al., 2010; Alipanahi et al., 2015; Keilwagen et al., 2019), so we can approximate these training and test distributions as identical.

<span id="page-5-0"></span>Table 32: ERM baseline results on ENCODE. All numbers are average precision. "Round-robin" indicates the average performance over all splits marked "round-robin" in Table [30.](#page-3-0) Parentheses show standard deviation across replicates, for liver; and average of such standard deviations across splits, for round-robin. Expanded results per round-robin split are in Table [35.](#page-6-1)

| TF   | Split scheme | Validation (ID) | Test (ID)  | Validation (OOD) | Test (OOD) |
|------|--------------|-----------------|------------|------------------|------------|
| MAX  | ENCODE-DREAM | 70.3 (2.1)      | 68.3 (1.9) | 67.9 (1.6)       | 45.0 (1.5) |
| MAX  | round-robin  | 65.0 (2.1)      | 64.9 (2.1) | 62.1 (1.2)       | 59.6 (2.0) |
| JUND | ENCODE-DREAM | 65.9 (1.2)      | 66.7 (1.4) | 32.9 (1.0)       | 42.3 (2.5) |
| JUND | round-robin  | 53.2 (4.0)      | 54.1 (4.2) | 47.2 (2.4)       | 42.9 (3.2) |

<span id="page-5-1"></span>Table 33: In-distribution results on ENCODE: when training and validation cell types are set to the test cell type. All numbers are average precision. "Round-robin" indicates the average performance over all splits marked "round-robin" in Table [30.](#page-3-0) Parentheses show standard deviation across replicates, for liver; and average of such standard deviations across splits, for round-robin. Expanded results per round-robin split are in Table [35.](#page-6-1)

| TF   | Split scheme | Train      | Validation (ID) | Test (ID)  |
|------|--------------|------------|-----------------|------------|
| MAX  | ENCODE-DREAM | 76.1 (0.9) | 57.3 (1.2)      | 57.6 (1.3) |
| MAX  | round-robin  | 77.3 (0.9) | 65.2 (1.3)      | 65.4 (1.3) |
| JUND | ENCODE-DREAM | 76.0 (0.6) | 55.8 (0.5)      | 56.0 (0.7) |
| JUND | round-robin  | 79.3 (2.7) | 61.8 (2.4)      | 62.4 (2.6) |

larger gap (54.1 (4.2) vs. 42.9 (3.2), respectively; Table [32\)](#page-5-0), but the variability in training performance made these results less reliable. Moreover, the test-to-test ID results were significantly higher than the train-to-train ID results  $(62.4 \ (2.6) \text{ vs. } 54.1 \ (4.2)$ , respectively), which suggests that either the model capacity or feature set is not rich enough to fit the variation across different cell types. We therefore opted not to include the round-robin splits in WILDS as well.

Discussion. Even in the in-distribution (test-to-test) setting, the results in Table [35](#page-6-1) show how different model performance can be for different domains. For example, the liver domain of primary tissue (from a human donor) is derived from lower-quality data than many of the long-standard cell lines (grown outside the human body) constituting other domains, and is consequently noisier than many of them (Balsubramani et al., 2017). The extent of this variation underscores the importance of accounting for the variability between domains when measuring the effect of distribution shift; for example, a train-to-train comparison could lead to significantly different conclusions than a test-to-test comparison.

The effect of the distribution shift also seems to depend on the particular TF (MAX vs. JUND) used. Biologically, different TFs show different levels of cell-type-specificity, and better understanding which TFs have binding patterns that can be accurately predicted from the cell-type-specific accessibility assays is important future work.

One of the main obstacles preventing us from using this ENCODE dataset as a WILDS benchmark is the instability in optimization that we reported above. We speculate that this instability could, in part, be due to the class imbalance in the data, but more work will be needed to ascertain this and to develop methods for training models more reliably on this type of genomic data.

As we mentioned above, Table [32](#page-5-0) reports significantly higher test-to-test ID results than train-totrain ID results for the JUND round-robin split scheme. The main difference between the test-to-test and train-to-train settings in the round-robin splits is that the former trains and tests on a single cell type, whereas the latter trains and tests on a mixture of cell types. The fact that ID performance

<span id="page-6-0"></span>Table 34: Baseline results on ENCODE. In-distribution (ID) results correspond to the train-to-train setting. Parentheses show standard deviation across replicates.

| TF name     | Test / Val cell type | Train AP   | Val (ID) AP | Test (ID) AP | Val (OOD) AP | Test (OOD) AP |
|-------------|----------------------|------------|-------------|--------------|--------------|---------------|
| MAX         | liver / HepG2        | 79.5(1.0)  | 70.3(2.1)   | 68.3(1.9)    | 67.9(1.6)    | 45.0(1.5)     |
| MAX         | K562 / liver         | 75.1(2.3)  | 59.9(4.5)   | 59.2(3.9)    | 47.6(1.0)    | 63.6(4.5)     |
| MAX         | liver / A549         | 78.5(1.3)  | 68.6(0.8)   | 68.4(0.5)    | 66.6(1.3)    | 38.5(1.1)     |
| <b>MAX</b>  | A549 / GM12878       | 78.0(1.7)  | 66.0(2.6)   | 66.6(2.8)    | 46.9(1.9)    | 65.0(2.5)     |
| MAX         | GM12878 / H1-hESC    | 80.0(1.2)  | 69.6(0.8)   | 69.2(0.6)    | 65.4(0.7)    | 46.3(0.5)     |
| MAX         | H1-hESC / HCT116     | 75.5(3.3)  | 63.2(3.2)   | 63.9(3.3)    | 70.6(0.5)    | 61.7(2.5)     |
| MAX         | HCT116 / HeLa-S3     | 76.8(1.1)  | 64.5(1.6)   | 64.9(1.3)    | 63.9(0.9)    | 69.4(0.9)     |
| <b>MAX</b>  | HeLa-S3 / HepG2      | 77.7(1.7)  | 65.0(1.9)   | 64.8(3.3)    | 67.7(1.9)    | 64.0(2.5)     |
| MAX         | HepG2 / K562         | 77.3(1.3)  | 63.2(1.3)   | 62.1(1.4)    | 68.5(1.1)    | 68.4(1.1)     |
| JUND        | liver / HepG2        | 82.6(1.3)  | 65.9(1.2)   | 66.7(1.4)    | 32.9(1.0)    | 42.3(2.5)     |
| JUND        | K562 / liver         | 54.2(10.5) | 29.9(8.4)   | 33.3(8.4)    | 32.9(3.7)    | 51.2(4.5)     |
| <b>JUND</b> | liver / MCF-7        | 83.6(2.4)  | 65.5(3.4)   | 65.3(4.0)    | 28.9(2.7)    | 29.2(3.2)     |
| <b>JUND</b> | MCF-7 / HCT116       | 76.4(3.5)  | 52.5(3.4)   | 53.6(3.8)    | 51.8(3.7)    | 27.2(4.2)     |
| <b>JUND</b> | HCT116 / HeLa-S3     | 75.7(1.5)  | 50.0(2.4)   | 50.6(2.4)    | 69.2(2.1)    | 52.9(3.2)     |
| <b>JUND</b> | HeLa-S3 / HepG2      | 78.0(2.1)  | 59.5(5.1)   | 60.0(4.9)    | 30.3(1.3)    | 66.6(3.6)     |
| <b>JUND</b> | HepG2 / K562         | 79.6(0.9)  | 61.9(1.1)   | 62.3(1.4)    | 69.8(0.6)    | 30.5(0.6)     |

<span id="page-6-1"></span>Table 35: Test-to-test results on ENCODE. Parentheses show standard deviation across replicates.

| TF name     | Test cell type | Train     | Val (ID) AP | Test (ID) AP |
|-------------|----------------|-----------|-------------|--------------|
| MAX         | liver          | 76.1(0.9) | 57.3(1.2)   | 57.6(1.3)    |
| MAX         | HepG2          | 76.1(0.8) | 66.5(1.1)   | 68.4(1.3)    |
| MAX         | K562           | 83.6(0.7) | 74.7(0.8)   | 75.9(0.7)    |
| MAX         | A549           | 77.2(0.8) | 68.4(1.4)   | 67.5(1.5)    |
| MAX         | GM12878        | 64.7(0.8) | 50.9(1.3)   | 49.2(1.5)    |
| MAX         | H1-hESC        | 76.2(1.8) | 65.3(2.1)   | 64.1(1.9)    |
| MAX         | HCT116         | 82.8(0.7) | 73.6(0.8)   | 74.5(0.8)    |
| MAX         | HeLa-S3        | 80.5(0.8) | 64.8(1.4)   | 66.4(1.4)    |
| <b>JUND</b> | liver          | 76.0(0.6) | 55.8(0.5)   | 56.0(0.7)    |
| <b>JUND</b> | K562           | 87.3(1.3) | 74.5(1.1)   | 76.0(1.7)    |
| <b>JUND</b> | HCT116         | 80.9(2.8) | 69.5(4.9)   | 68.4(4.7)    |
| <b>JUND</b> | HeLa-S3        | 87.2(0.8) | 69.0(0.7)   | 70.9(0.8)    |
| <b>JUND</b> | HepG2          | 84.1(9.8) | 71.9(4.6)   | 72.1(4.9)    |
| <b>JUND</b> | MCF-7          | 60.1(0.8) | 30.1(2.4)   | 31.1(2.9)    |

is significantly higher in the former than the latter suggests that the learned models are not able to fit JUND binding patterns across multiple cell types. This could be due to a model family that is not large or expressive enough, or it could be because the feature set does not have all of the necessary information to accurately predict binding across cell types. In either case, it is unlikely that a training algorithm developed to be robust to distribution shifts will be able to significantly improve OOD performance in this setting, as the issue seems to lie in the model family or the data distribution instead.

Overall, it is commonly understood that distribution shifts between cell types are a significant problem for TF binding prediction, and many methods have been developed to tackle these shifts (Balsubramani et al., 2017; Li et al., 2019a; Li and Guan, 2019; Keilwagen et al., 2019; Quang and Xie, 2019). Nonetheless, we found it challenging to establish a rigorous distribution shift benchmark around this task, as our results were confounded by factors such as optimization issues, large variability between cell types, and the difficulty of learning a model that could fit multiple cell types even in an i.i.d. setting. We hope that future work on evaluating and mitigating distribution shifts in TF binding prediction can build upon our results and address these challenges.

### F.2.3 Additional details

Additional dataset details. The ground-truth labels were derived from high-quality chromatin immunoprecipitation sequencing (ChIP-seq) experiments, which provide a genome-wide track of binding enrichment scores for each TF. Statistical methods based on standardized pipelines (Landt et al., 2012) were used to identify high-confidence binding events across the genome, resulting in a genome-wide track indicating whether each of the windows of sequence in the genome is bound or unbound by the TF, or whether binding is ambiguous but likely (these were ignored in our benchmarking).

Our data include two TFs chosen for their basic importance in cell-type-specific gene regulation: MAX and JUND. MAX canonically recognizes a short, common sequence (the domain CACGTG), but its structure leads it to bind to DNA as a dimer, and facilitates cooperative activity with a range of partners (Grandori et al., 2000) with many weaker and longer-range sequence determinants of binding (Allevato et al., 2017). JUND belongs to a large family of TFs (bZIP) known for binding in cooperation with partners in the family in a variety of modes, all involving a short 7bp sequence  $(TGA[C/G]TCA)$  and its two halves.

Additional model details. The network consists of encoder and decoder portions:

- Encoder. The encoder is composed of five downscaling convolutional blocks, each consisting of two stride-1 convolutional layers with kernel size 7 (and padding such that the output size is left unchanged), followed by a max-pooling layer with kernel size 2. Each successive block halves the input window size and scales up the number of convolutional filters (by 1.5).
- **Decoder.** Mirroring the encoder, the decoder is composed of five upscaling convolutional blocks, each consisting of two convolutional layers with kernel size 7 and an upsampling layer (a ConvTranspose layer with kernel size 2 and stride 2). Each successive block doubles the input window size. The respective sizes of the decoder layer representations are the same as the encoder in reverse, culminating in a  $(12800 \times 15)$  representation that is then run through a convolutional layer (kernel size 200, stride 50) to reduce it to a single channel (with length 253). A final fully-connected layer results in a 128-dimensional output.

Batch normalization is applied after every layer except the last, and each intermediate convolutional layer is padded such that the output and input sizes are equal.

Additional data sources. The ENCODE-DREAM prediction challenge contains binding data for many TFs from a large range of cell types, discretized into the same 200-bp windows used in this benchmark. The ENCODE portal (encodeproject.org) contains more ChIP-seq datasets from the 13 challenge cell lines for which we provide DNase accessibility data. DNA shape and gene expression data types were also provided in the original challenge.

- DNA shape. Twisting, bending, and shearing of DNA influence local binding in a TF-specific fashion (Rohs et al., 2009).
- Gene expression. Expression levels of all human genes were provided using RNA-seq data from ENCODE. This can be used to model the presence of cofactor proteins that can recruit TFs for binding (Ptashne and Gann, 1997).

However, none of the top challenge participants found these data modalities useful (Balsubramani et al., 2017), so they are not provided in this benchmark.

Data normalization. We normalize the distribution of each DNase-seq signal readout to the average of the DNase-seq signals over training cell types. We use a version of quantile normalization (Bolstad et al., 2003) with piecewise polynomial interpolation. Li and Guan (2019) also use this, but instead normalize to the test domain's DNase distribution. As this technique uses test-domain data, it is out of the scope of our benchmark. However, we note that in genomics settings it is realistic to have relatively cheaply available chromatin accessibility data in the target cell type of interest.

Modifications to the original setup. The prediction task of the challenge was a binary classification problem over the 200 bp bins, which did not involve the fixed 12800 bp windows. To predict on a bin, participating teams were free to use as much of the regions surrounding (flanking) the bin as they wished. The winning teams all used at least 1000 bp total for each bin, and further work has shown the efficacy of using much larger flanking regions of tens of thousands of bp (Quang and Xie, 2019; Avsec et al., 2021a). We instead predict on 128 bins at once (following Li and Guan (2019)), which allows for more efficient training and prediction.

Our ERM baselines' OOD test performance is competitive with the original challenge results, but lower than the state-of-the-art performance of Li and Guan (2019) because of the aforementioned differences in data processing, splits, and architecture, as well as the cross-domain training method employed by that paper and predecessor work (Li et al., 2019a). These and other state-of-the-art models noted that their domain adaptation strategies played a major role in improving performance.

# F.3 BDD100K: Object recognition in autonomous driving across locations

As discussed in Section 8.6, autonomous driving, and robotics in general, is an important application that requires effective and robust tools for handling distribution shift. Here, we discuss our findings on a modified version of the BDD100K dataset that evaluates on shifts based on time of day and location. Our results below suggest that more challenging tasks, such as object detection and segmentation, may be more suited to evaluations of distribution shifts in an autonomous driving context.

<span id="page-8-0"></span>Table 36: Average multi-task classification accuracy of ERM trained models on BDD100K. All results are reported across 3 random seeds, with standard deviation in parentheses. We observe no substantial drops in the presence of test time distribution shifts.

| Algorithm | Time of day shift |            | Location shift  |            |
|-----------|-------------------|------------|-----------------|------------|
|           | Validation (ID)   | Test (OOD) | Validation (ID) | Test (OOD) |
| ERM       | 87.1 (0.3)        | 89.7 (0.2) | 87.9 (0.0)      | 86.9 (0.0) |

## F.3.1 SETUP

Task. In line with the other datasets in WILDS, we evaluate using a classification task. Specifically, the task is to predict whether or not 9 different categories appear in the image  $x$ : bicycles, buses, cars, motorcycles, pedestrians, riders, traffic lights, traffic signs, and trucks. This is a multi-task binary classification problem, and the label y is thus a 9-dimensional binary vector.

Data. The BDD100K dataset is a large and diverse driving dataset crowd-sourced from tens of thousands of drivers, covering four different geographic regions and many different times of day, weather conditions, and scenes (Yu et al., 2020). The original dataset contains 80,000 images in the combined training and validation sets and is richly annotated for a number of different tasks such as detection, segmentation, and imitation learning. We use bounding box labels to construct our task labels, and as discussed later, we use location and image tags to construct the shifts we evaluate.

# Time of Day Shift

<span id="page-9-0"></span> $train - mixed$ val - mixed (ID) test - no daytime (OOD)  $[0, 0, 1, 0, 0, 0, 0, 1, 0]$  $[0, 0, 1, 0, 0, 0, 0, 0, 0]$  $[0, 0, 1, 0, 0, 0, 0, 0, 1]$ **Location Shift** train - New York val - New York (ID) test - California (OOD)  $[1, 0, 1, 0, 1, 1, 1, 1, 1]$  $[0, 0, 1, 0, 0, 0, 1, 0, 0]$  $[0, 0, 1, 0, 0, 0, 0, 0, 0]$ 

Figure 30: For BDD100K, we study two different types of shift, based on time of day and location. We visualize randomly chosen images and their corresponding labels from the training, validation, and test splits for both shifts. The labels are 9-dimensional binary vectors indicating the presence (1) or absence (0) of, in order: bicycles, buses, cars, motorcycles, pedestrians, riders, traffic lights, traffic signs, and trucks.

Evaluation. In evaluating the trained models, we consider average accuracy across the binary classification tasks, averaged over each of the validation and test sets separately. We next discuss how we create and evaluate two different types of shift based on time of day and location differences.

## F.3.2 TIME OF DAY SHIFT

Distribution shift and evaluation. We evaluate two different types of shift, depicted in Figure [30.](#page-9-0) For time of day shift (Figure [30](#page-9-0) top row), we use the original BDD100K training set, which has roughly equal proportions of daytime and non daytime images (Yu et al., 2020). However, we construct a test set using the original BDD100K validation set that only includes non-daytime images. We then split roughly the same number of images randomly from the training set to form an in-distribution validation set, which allows us to do a train-to-train comparison. There are 64,993, 4,860, and 4,742 images in the training, validation, and test splits, respectively.

ERM results. Table [36](#page-8-0) summarizes our findings. For time of day shift, we actually observe slightly higher test performance, on only non daytime images, than validation performance on mixed daytime and non daytime images. We contrast this with findings from Dai and Van Gool (2018); Yu et al. (2020), who showed worse test performance for segmentation and detection tasks, respectively, on non daytime images. We believe this disparity can be attributed to the difference in tasks—for example, it is likely more difficult to draw an accurate bounding box for a car at night than to simply recognize tail lights and detect the presence of a car.

### F.3.3 LOCATION SHIFT

Distribution shift. For location shift (Figure [30](#page-9-0) bottom row), we combine all of the data from the original BDD100K training and validation sets. We construct training and validation sets from all of the images captured in New York, and we use all images from California for the test set. The validation set again is in-distribution with respect to the training set and has roughly the same number of images as the test set. There are 53,277, 9,834, and 9,477 images in the training, validation, and test splits, respectively.

ERM results. In the case of location shift, we see from Table [36](#page-8-0) that there is a small drop in performance, possibly because this shift is more drastic as the locations are disjoint between training and test time. However, the performance drop is relatively small and the test time accuracy is still comparable to validation accuracy. In general, we believe that these results lend support to the conclusion that, for autonomous driving and robotics applications, other more challenging tasks are better suited for evaluating performance. Generally speaking, incorporating a wide array of different applications will likely require a simultaneous effort to incorporate different tasks as well.

# F.4 Amazon: Sentiment classification across different categories and time

Our benchmark dataset Amazon-wilds studies user shifts. In Section 7, we discussed empirical trends on other types of distribution shifts on the same underlying 2018 Amazon Reviews dataset (Ni et al., 2019). We now present the detailed setup and empirical results for the time and category shifts.

## F.4.1 SETUP

Model. For all experiments in this section, we finetune BERT-base-uncased models, using the implementation from Wolf et al. (2019), and with the following hyperparameter settings: batch size 8; learning rate  $2 \times 10^{-6}$ ; L<sub>2</sub>-regularization strength 0.01; 3 epochs; and a maximum number of tokens of 512. These hyperparameters are taken from the Amazon-wilds experiments.

### F.4.2 TIME SHIFTS

**Problem setting.** We consider the domain generalization setting, where the domain  $d$  is the year in which the reviews are written. As in Amazon-wilds, the task is multi-class sentiment classification, where the input x is the text of a review, the label y is a corresponding star rating from 1 to 5.

Data. The dataset is a modified version of the Amazon Reviews dataset (Ni et al., 2019) and comprises customer reviews on Amazon. Specifically, we consider the following split:

- 1. Training: 1,000,000 reviews written in years 2000 to 2013.
- 2. Validation (OOD): 20,000 reviews written in years 2014 to 2018.
- 3. Test (OOD): 20,000 reviews written in years 2014 to 2018.

To construct the above split, we first randomly sample 4,000 reviews per year for the evaluation splits. For years in which there are not sufficient reviews, we split the reviews equally between validation and test. After constructing the evaluation set, we then randomly sample from the remaining reviews to form the training set.

Evaluation. To assess whether models generalize to future years, we evaluate models by their average accuracy on the OOD test set.

<span id="page-11-0"></span>Table 37: Baseline results on time shifts on the Amazon Reviews Dataset. We report the accuracy of models trained using ERM. In addition to the average accuracy across all years in each split, we report the accuracy for the worst-case year.

| Algorithm | Train      |            | Validation (OOD) |            | Test (OOD) |            |
|-----------|------------|------------|------------------|------------|------------|------------|
|           | Average    | Worst year | Average          | Worst year | Average    | Worst year |
| ERM       | 75.0 (0.0) | 72.4 (0.1) | 75.7 (0.1)       | 74.6 (0.1) | 76.0 (0.1) | 75.4 (0.1) |

<span id="page-11-1"></span>Table 38: Test-to-test in-distribution comparison for time shifts on Amazon Reviews Dataset. We observe only modest performance drops due to time shifts.

| Setting      | Year | 2014       | 2015       | 2016       | 2017       | 2018       |
|--------------|------|------------|------------|------------|------------|------------|
| Official     |      | 75.4 (0.1) | 75.8 (0.1) | 76.3 (0.1) | 76.4 (0.4) | 76.1 (0.1) |
| Test-to-test |      | 76.1 (0.2) | 76.8 (0.1) | 77.1 (0.2) | 77.5 (0.2) | 77.0 (0.0) |

ERM results and performance drops. We only observed modest performance drops due to time shift. Our baseline model performs well on the OOD test set, achieving 76.0% accuracy on average and 75.4% on the worst year (Table [37\)](#page-11-0). To measure performance drops due to distribution shifts, we ran a test-to-test comparison by training a model on reviews written in years 2014 to 2018 (Table [38\)](#page-11-1). The performance gaps between the model trained on the official split and the model trained on the test-to-test split are consistent but modest across the years, with the biggest drop of 1.1% for 2018.

### F.4.3 CATEGORY SHIFTS

Shifts across categories—where a model is trained on reviews in one category and then tested on another—have been studied extensively (Blitzer et al., 2007; Mansour et al., 2009; Hendrycks et al., 2020c). In line with prior work, we observe that model performance drops upon evaluating on a few unseen categories. However, the observed difference between out-of-distribution and in-distribution baselines varies from category to category and is not consistently large (Hendrycks et al., 2020c). In addition, we find that training on more diverse data with more product categories tends to improve generalization to unseen categories and reduce the effect of the distribution shift; similar phenomena have also been reported in prior work (Mansour et al., 2009; Guo et al., 2018).

**Problem setting.** We consider the domain generalization setting, where the domain  $d$  is the product category. As in Amazon-wilds, the task is multi-class sentiment classification, where the input x is the text of a review, the label y is a corresponding star rating from 1 to 5.

Data. The dataset is a modified version of the Amazon Reviews dataset (Ni et al., 2019) and comprises customer reviews on Amazon. Specifically, we consider the following split for a given set of training categories:

- 1. Training: up to 1,000,000 reviews in training categories.
- 2. Validation (OOD): reviews in categories unseen during training.
- 3. Test (OOD): reviews in categories unseen during training.
- 4. Validation (ID): reviews in training categories.
- 5. Test (ID): reviews in training categories.

<span id="page-12-0"></span>Table 39: Baseline results on category shifts on the Amazon Reviews Dataset. We report the accuracy of models trained using ERM on a single category (Books) versus four categories (Books, Movies and TV, Home and Kitchen, and Electronics). Across many categories unseen at training time, corresponding to each row, the latter model modestly but consistently outperforms the former.

| Category                    | Validation (OOD) |            | Test (OOD) |            |
|-----------------------------|------------------|------------|------------|------------|
|                             | Single           | Multiple   | Single     | Multiple   |
| All Beauty                  | 87.8 (0.8)       | 85.6 (1.4) | 82.9 (0.8) | 83.1 (0.8) |
| Arts Crafts and Sewing      | 81.6 (0.7)       | 83.4 (0.4) | 79.5 (0.2) | 81.7 (0.2) |
| Automotive                  | 78.2 (0.4)       | 80.4 (0.4) | 76.5 (0.2) | 78.9 (0.2) |
| CDs and Vinyl               | 78.1 (0.7)       | 78.6 (0.2) | 78.5 (0.7) | 79.7 (0.3) |
| Cell Phones and Accessories | 76.8 (0.3)       | 79.0 (0.7) | 78.0 (0.5) | 80.2 (1.0) |
| Clothing Shoes and Jewelry  | 69.8 (0.6)       | 72.6 (0.2) | 73.3 (0.2) | 75.2 (0.2) |
| Digital Music               | 77.5 (0.5)       | 77.8 (0.5) | 80.7 (1.0) | 81.7 (0.6) |
| Gift Cards                  | 88.2 (1.5)       | 90.7 (3.1) | 90.7 (0.8) | 91.2 (0.0) |
| Grocery and Gourmet Food    | 79.0 (0.3)       | 79.0 (0.1) | 79.3 (0.7) | 79.2 (0.2) |
| Industrial and Scientific   | 77.0 (0.4)       | 78.1 (0.6) | 77.4 (0.2) | 78.9 (0.1) |
| Kindle Store                | 75.0 (0.3)       | 74.5 (0.3) | 73.2 (0.3) | 73.1 (0.5) |
| Luxury Beauty               | 67.2 (0.2)       | 70.2 (0.6) | 67.4 (0.7) | 69.4 (0.9) |
| Magazine Subscriptions      | 74.2 (3.2)       | 71.0 (0.0) | 90.3 (0.0) | 89.2 (1.9) |
| Musical Instruments         | 76.1 (0.3)       | 78.3 (0.3) | 78.8 (0.8) | 80.9 (0.2) |
| Office Products             | 78.5 (0.3)       | 80.0 (0.5) | 76.7 (0.5) | 78.9 (0.4) |
| Patio Lawn and Garden       | 70.8 (0.6)       | 72.9 (0.3) | 75.5 (0.6) | 79.7 (0.6) |
| Pet Supplies                | 74.5 (0.4)       | 77.1 (0.9) | 74.4 (0.4) | 76.8 (0.5) |
| Prime Pantry                | 80.5 (0.3)       | 80.2 (0.2) | 78.5 (0.6) | 79.4 (0.3) |
| Software                    | 65.8 (1.7)       | 67.1 (1.1) | 71.3 (1.5) | 72.6 (0.5) |
| Sports and Outdoors         | 74.2 (0.5)       | 76.0 (0.2) | 75.8 (0.2) | 78.3 (0.6) |
| Tools and Home Improvement  | 74.0 (1.1)       | 76.4 (0.3) | 73.1 (0.6) | 74.4 (0.2) |
| Toys and Games              | 78.9 (0.4)       | 79.9 (0.2) | 77.6 (0.2) | 80.9 (0.2) |
| Video Games                 | 76.0 (0.2)       | 76.6 (0.8) | 76.9 (0.6) | 78.0 (0.6) |

To construct the above split, we first randomly sample 1,000 reviews per category for the evaluation splits (for categories with insufficient number of reviews, we split the reviews equally between validation and test) and then randomly sample from the remaining reviews to form the training set.

Evaluation. To assess whether models generalize to unseen categories, we evaluate models by their average accuracy on each of the categories in the OOD test set.

ERM results. We first considered training on four categories (Books, Movies and TV, Home and Kitchen, and Electronics) and evaluating on unseen categories. We observed that a BERTbase-uncased model trained via ERM yields a test accuracy of 75.4% on the four in-distribution categories and a wide range of accuracies on unseen categories (Table [39,](#page-12-0) columns Multiple). While the accuracies on some unseen categories are lower than the train-to-train in-distribution accuracy, it is unclear whether the performance gaps stem from the distribution shift or differences in intrinsic difficulty across categories; in fact, the accuracy is higher on many unseen categories (e.g., All Beauty) than on the in-distribution categories, illustrating the importance of accounting for intrinsic difficulty.

To control for intrinsic difficulty, we ran a test-to-test comparison on each target category. We controlled for the number of training reviews to the extent possible; the standard model is trained on 1 million reviews in the official split, and each test-to-test model is trained on 1 million reviews or less, as limited by the number of reviews per category. We observed performance drops on some categories, for example on Clothing, Shoes, and Jewelry (83.0% in the test-to-test setting versus 75.2% in the official setting trained on the four different categories) and on Pet Supplies (78.8% to 76.8%). However, on the remaining categories, we observed more modest performance gaps, if at all. While we thus found no evidence for significance performance drops for many categories, these results do not rule out such drops either: one confounding factor is that some of the oracle models are trained on significantly smaller training sets and therefore underestimate the in-distribution performance.

In addition, we compared training on four categories (Books, Movies and TV, Home and Kitchen, and Electronics), as above, to training on just one category (Books), while keeping the training set size constant. We found that decreasing the number of training categories in this way lowered out-ofdistribution performance: across many OOD categories, accuracies were modestly but consistently higher for the model trained on four categories than for the model trained on a single category (Table [39\)](#page-12-0).

# F.5 Yelp: Sentiment classification across different users and time

We present empirical results on time and user shifts in the Yelp Open Dataset<sup>13</sup>.

F.5.1 SETUP

Model. For all experiments in this section, we finetune BERT-base-uncased models, using the implementation from Wolf et al. (2019), and with the following hyperparameter settings: batch size 8; learning rate  $2 \times 10^{-6}$ ; L<sub>2</sub>-regularization strength 0.01; 3 epochs with early stopping; and a maximum number of tokens of 512. We select the above hyperparameters based on a grid search over learning rates  $1 \times 10^{-6}$ ,  $2 \times 10^{-6}$ ,  $1 \times 10^{-5}$ ,  $2 \times 10^{-5}$ , using the time shift setup; for the user shifts, we adopted the same hyperparameters.

## F.5.2 TIME SHIFTS

**Problem setting.** We consider the domain generalization setting, where the domain  $d$  is the year in which the reviews are written. As in Amazon-wilds, the task is multi-class sentiment classification, where the input  $x$  is the text of a review, the label  $y$  is a corresponding star rating from 1 to 5.

Data. The dataset is a modified version of the Yelp Open Dataset and comprises 1 million customer reviews on Yelp. Specifically, we consider the following split:

- 1. Training: 1,000,000 reviews written in years 2006 to 2013.
- 2. Validation (OOD): 20,000 reviews written in years 2014 to 2019.
- 3. Test (OOD): 20,000 reviews written in years 2014 to 2019.

To construct the above split, we first randomly sample 1,000 reviews per year for the evaluation splits. For years in which there are not sufficient reviews, we split the reviews equally between validation and test. After constructing the evaluation set, we then randomly sample from the remaining reviews to form the training set.

Evaluation. To assess whether models generalize to future years, we evaluate models by their average accuracy on the OOD test set.

ERM results and performance drops. We observe modest performance drops due to time shift. A BERT-base-uncased model trained with the standard ERM objective performs well on the OOD test set, achieving 76.0% accuracy on average and 73.9% on the worst year (Table [40\)](#page-14-0). To measure performance drops due to distribution shifts, we run a test-to-test in-distribution comparison by training on reviews written in years 2014 to 2019 (Table [41\)](#page-14-1). While there are consistent performance gaps between the out-of-distribution and the in-distribution baselines in later years, they are modest in magnitude with the largest drop of 3.1% for 2018.

<sup>13.</sup> https://www.yelp.com/dataset

| Algorithm | Train      |            | Validation (OOD) |            | Test (OOD) |            |
|-----------|------------|------------|------------------|------------|------------|------------|
|           | Average    | Worst year | Average          | Worst year | Average    | Worst year |
| ERM       | 71.4 (0.7) | 65.7 (1.1) | 76.1 (0.1)       | 73.1 (0.2) | 76.0 (0.4) | 73.9 (0.4) |

<span id="page-14-0"></span>Table 40: Baseline results on time shifts on the Yelp Open Dataset. We report the accuracy of models trained using ERM. Parentheses show standard deviation across 3 replicates.

<span id="page-14-1"></span>Table 41: Test-to-test in-distribution comparison on the Yelp Open Dataset. We observe only modest performance drops due to time shifts. Parentheses show standard deviation across 3 replicates.

| Year                 | 2014       | 2015       | 2016       | 2017       | 2018       | 2019       |
|----------------------|------------|------------|------------|------------|------------|------------|
| OOD baseline (ERM)   | 75.8 (0.6) | 75.2 (0.9) | 73.9 (0.4) | 77.0 (0.4) | 76.7 (0.3) | 77.2 (0.6) |
| ID baseline (oracle) | 75.2 (0.5) | 75.0 (0.5) | 76.4 (0.7) | 78.8 (0.6) | 79.6 (0.4) | 79.5 (0.5) |

### F.5.3 USER SHIFT

Problem setting. As in AMAZON-WILDS, we consider the domain generalization setting, where the domains are reviewers and the task is multi-class sentiment classification. Concretely, the input x is the text of a review, the label y is a corresponding star rating from 1 to 5, and the domain  $d$  is the identifier of the user that wrote the review.

Data. The dataset is a modified version of the Yelp Open Dataset and comprises 1.2 million customer reviews on Yelp. To measure generalization to unseen reviewers, we train on reviews written by a set of reviewers and consider reviews written by unseen reviewers at test time. Specifically, we consider the following random split across reviewers:

- 1. Training: 1,000,104 reviews from 11,856 reviewers.
- 2. Validation (OOD): 40,000 reviews from another set of 1,600 reviewers, distinct from training and test (OOD).
- 3. Test (OOD): 40,000 reviews from another set 1,600 reviewers, distinct from training and validation (OOD).
- 4. Validation (ID): 40,000 reviews from 1,600 of the 11,856 reviewers in the training set.
- 5. Test  $(ID): 40,000$  reviews from 1,600 of the 11,856 reviewers in the training set.

The training set includes at least 25 reviews per reviewer, whereas the evaluation sets include exactly 25 reviews per reviewer. While we primarily evaluate model performance on the above OOD test set, we also provide in-distribution validation and test sets for potential use in hyperparameter tuning and additional reporting. These in-distribution splits comprise reviews written by reviewers in the training set.

Evaluation. To assess whether models perform consistently well across reviewers, we evaluate models by their accuracy on the reviewer at the 10th percentile.

ERM results and performance drops. We observe only modest variations in performance across reviewers. A BERT-base-uncased model trained with the standard ERM objective achieves 71.5% accuracy on average and 56.0% accuracy at the 10th percentile reviewer (Table 42). The above variation is modestly larger than expected from randomness; a random binomial baseline with equal average accuracy would have a tenth percentile accuracy of 60.1%.