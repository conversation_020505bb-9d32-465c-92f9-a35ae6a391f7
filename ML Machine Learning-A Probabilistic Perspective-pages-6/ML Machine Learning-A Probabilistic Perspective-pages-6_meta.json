{"table_of_contents": [{"title": "5 Bayesian statistics", "heading_level": null, "page_id": 0, "polygon": [[86.203125, 98.0068359375], [264.0, 98.0068359375], [264.0, 142.9365234375], [86.203125, 142.9365234375]]}, {"title": "5.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 207.0], [195.75, 207.0], [195.75, 217.845703125], [101.25, 217.845703125]]}, {"title": "5.2 Summarizing posterior distributions", "heading_level": null, "page_id": 0, "polygon": [[99.75, 352.5], [318.75, 352.5], [318.75, 364.5], [99.75, 364.5]]}, {"title": "5.2.1 MAP estimation", "heading_level": null, "page_id": 0, "polygon": [[94.5, 438.75], [205.5, 438.75], [205.5, 448.98046875], [94.5, 448.98046875]]}, {"title": "5.2.1.1 No measure of uncertainty", "heading_level": null, "page_id": 1, "polygon": [[89.71875, 264.75], [246.0, 264.75], [246.0, 274.95703125], [89.71875, 274.95703125]]}, {"title": "5.2.1.2 Plugging in the MAP estimate can result in overfitting", "heading_level": null, "page_id": 1, "polygon": [[89.15625, 345.0], [363.75, 345.0], [363.75, 355.0078125], [89.15625, 355.0078125]]}, {"title": "5.2.1.3 The mode is an untypical point", "heading_level": null, "page_id": 1, "polygon": [[89.0859375, 437.25], [266.34375, 437.25], [266.34375, 447.3984375], [89.0859375, 447.3984375]]}, {"title": "5.2.1.4 MAP estimation is not invariant to reparameterization *", "heading_level": null, "page_id": 2, "polygon": [[88.875, 338.25], [370.5, 338.25], [370.5, 348.046875], [88.875, 348.046875]]}, {"title": "5.2.2 Credible intervals", "heading_level": null, "page_id": 3, "polygon": [[93.0, 483.0], [214.5, 483.0], [214.5, 493.91015625], [93.0, 493.91015625]]}, {"title": "5.2.2.1 Highest posterior density regions *", "heading_level": null, "page_id": 4, "polygon": [[88.453125, 519.75], [281.25, 519.75], [281.25, 529.98046875], [88.453125, 529.98046875]]}, {"title": "5.2.3 Inference for a difference in proportions", "heading_level": null, "page_id": 5, "polygon": [[93.0, 494.25], [322.875, 494.25], [322.875, 504.66796875], [93.0, 504.66796875]]}, {"title": "5.3 Bayesian model selection", "heading_level": null, "page_id": 6, "polygon": [[99.75, 555.0], [261.0, 555.0], [261.0, 565.734375], [99.75, 565.734375]]}, {"title": "5.3.1 Bayesian O<PERSON>'s razor", "heading_level": null, "page_id": 7, "polygon": [[94.5, 317.25], [242.25, 317.25], [242.25, 326.84765625], [94.5, 326.84765625]]}, {"title": "5.3.2 Computing the marginal likelihood (evidence)", "heading_level": null, "page_id": 9, "polygon": [[93.0, 462.0], [347.25, 462.0], [347.25, 472.078125], [93.0, 472.078125]]}, {"title": "5.3.2.1 Beta-binomial model", "heading_level": null, "page_id": 11, "polygon": [[87.609375, 61.5], [221.25, 61.5], [221.25, 71.4287109375], [87.609375, 71.4287109375]]}, {"title": "5.3.2.2 Dirichlet-multinoulli model", "heading_level": null, "page_id": 11, "polygon": [[86.2734375, 325.5], [247.5, 325.5], [247.5, 335.07421875], [86.2734375, 335.07421875]]}, {"title": "5.3.2.3 Gaussian-Gaussian-<PERSON>art model", "heading_level": null, "page_id": 11, "polygon": [[86.8359375, 548.25], [276.75, 548.25], [276.75, 557.82421875], [86.8359375, 557.82421875]]}, {"title": "5.3.2.4 BIC approximation to log marginal likelihood", "heading_level": null, "page_id": 12, "polygon": [[86.8359375, 216.0], [327.0, 216.0], [327.0, 226.072265625], [86.8359375, 226.072265625]]}, {"title": "5.3.2.5 Effect of the prior", "heading_level": null, "page_id": 13, "polygon": [[86.90625, 222.75], [209.53125, 222.75], [209.53125, 233.19140625], [86.90625, 233.19140625]]}, {"title": "5.3.3 Bayes factors", "heading_level": null, "page_id": 14, "polygon": [[92.953125, 213.0], [192.0, 213.0], [192.0, 222.43359375], [92.953125, 222.43359375]]}, {"title": "5.3.3.1 Example: Testing if a coin is fair", "heading_level": null, "page_id": 14, "polygon": [[89.015625, 462.0], [272.25, 462.0], [272.25, 472.078125], [89.015625, 472.078125]]}, {"title": "5.3.4 <PERSON><PERSON>-<PERSON><PERSON> paradox *", "heading_level": null, "page_id": 15, "polygon": [[93.0, 498.75], [252.0, 498.75], [252.0, 508.78125], [93.0, 508.78125]]}, {"title": "5.4 Priors", "heading_level": null, "page_id": 16, "polygon": [[99.75, 403.5], [162.0, 403.5], [162.0, 413.859375], [99.75, 413.859375]]}, {"title": "5.4.1 Uninformative priors", "heading_level": null, "page_id": 16, "polygon": [[95.25, 499.5], [230.25, 499.5], [230.25, 510.36328125], [95.25, 510.36328125]]}, {"title": "5.4.2 <PERSON><PERSON> priors *", "heading_level": null, "page_id": 17, "polygon": [[93.0, 259.5], [205.03125, 259.5], [205.03125, 270.52734375], [93.0, 270.52734375]]}, {"title": "5.4.2.1 Example: <PERSON><PERSON> prior for the <PERSON>oulli and multi<PERSON>ulli", "heading_level": null, "page_id": 18, "polygon": [[88.875, 147.75], [375.75, 147.75], [375.75, 157.88671875], [88.875, 157.88671875]]}, {"title": "5.4.2.2 Example: <PERSON><PERSON> prior for location and scale parameters", "heading_level": null, "page_id": 18, "polygon": [[87.0, 495.0], [378.75, 495.0], [378.75, 504.984375], [87.0, 504.984375]]}, {"title": "5.4.3 <PERSON><PERSON> priors", "heading_level": null, "page_id": 19, "polygon": [[93.0, 345.75], [194.625, 345.75], [194.625, 355.640625], [93.0, 355.640625]]}, {"title": "5.4.4 Mixtures of conjugate priors", "heading_level": null, "page_id": 19, "polygon": [[93.0, 559.5], [264.75, 559.5], [264.75, 570.1640625], [93.0, 570.1640625]]}, {"title": "5.4.4.1 Example", "heading_level": null, "page_id": 20, "polygon": [[88.8046875, 412.5], [168.0, 412.5], [168.0, 422.40234375], [88.8046875, 422.40234375]]}, {"title": "5.4.4.2 Application: Finding conserved regions in DNA and protein sequences", "heading_level": null, "page_id": 21, "polygon": [[87.75, 253.5], [433.5, 253.5], [433.5, 262.775390625], [87.75, 262.775390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 29], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6890, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 51], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 45], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 416], ["Line", 52], ["TextInlineMath", 7], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 47], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 909, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 35], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 643, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 50], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2552, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 42], ["TextInlineMath", 5], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 35], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 60], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1184, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 60], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1149, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 73], ["Equation", 8], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 453], ["Line", 60], ["Equation", 8], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1851, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 43], ["Text", 9], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 51], ["TableCell", 38], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["Text", 1], ["Footnote", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 50], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 53], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 68], ["Equation", 7], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 70], ["Text", 8], ["Equation", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 44], ["TextInlineMath", 6], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 448], ["Line", 39], ["Equation", 7], ["Text", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 43], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-6"}