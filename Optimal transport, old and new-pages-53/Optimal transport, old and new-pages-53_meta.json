{"table_of_contents": [{"title": "<PERSON><PERSON><PERSON> inequality", "heading_level": null, "page_id": 0, "polygon": [[133.5, 291.75], [306.0, 291.75], [306.0, 304.34765625], [133.5, 304.34765625]]}, {"title": "<PERSON><PERSON><PERSON><PERSON> inequalities", "heading_level": null, "page_id": 2, "polygon": [[133.5, 277.5], [301.81640625, 277.5], [301.81640625, 289.265625], [133.5, 289.265625]]}, {"title": "884 30 Weak Ricci curvature bounds II: Geometric and analytic properties", "heading_level": null, "page_id": 4, "polygon": [[132.978515625, 26.25], [453.75, 26.25], [453.75, 35.81982421875], [132.978515625, 35.81982421875]]}, {"title": "Uniqueness of geodesics", "heading_level": null, "page_id": 4, "polygon": [[133.5, 456.75], [275.51953125, 456.75], [275.51953125, 467.9296875], [133.5, 467.9296875]]}, {"title": "Regularity of the interpolant", "heading_level": null, "page_id": 5, "polygon": [[133.5, 537.0], [306.0, 537.0], [306.0, 548.3671875], [133.5, 548.3671875]]}, {"title": "HWI and logarithmic Sobolev inequalities", "heading_level": null, "page_id": 9, "polygon": [[133.5, 352.5], [383.25, 352.5], [383.25, 363.515625], [133.5, 363.515625]]}, {"title": "Sobolev inequalities", "heading_level": null, "page_id": 10, "polygon": [[133.5, 311.888671875], [252.0, 311.888671875], [252.0, 322.716796875], [133.5, 322.716796875]]}, {"title": "Diameter control", "heading_level": null, "page_id": 11, "polygon": [[133.5, 126.75], [236.5224609375, 126.75], [236.5224609375, 138.4453125], [133.5, 138.4453125]]}, {"title": "Poincaré inequalities", "heading_level": null, "page_id": 11, "polygon": [[133.5, 381.0], [257.291015625, 381.0], [257.291015625, 392.1328125], [133.5, 392.1328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 98], ["Text", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7306, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 746], ["Line", 50], ["TextInlineMath", 6], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 462], ["Line", 66], ["Equation", 6], ["TextInlineMath", 6], ["Text", 5], ["ListItem", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 574, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["Line", 50], ["TextInlineMath", 6], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 51], ["TextInlineMath", 6], ["Text", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 894, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 44], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 41], ["TextInlineMath", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 762], ["Line", 55], ["TextInlineMath", 4], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 68], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1306, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 500], ["Line", 50], ["TextInlineMath", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 46], ["TextInlineMath", 4], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 35], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 63], ["TextInlineMath", 5], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-53"}