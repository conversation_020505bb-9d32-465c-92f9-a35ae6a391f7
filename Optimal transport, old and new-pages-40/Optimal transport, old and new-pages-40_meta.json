{"table_of_contents": [{"title": "Subdifferential of energy functionals", "heading_level": null, "page_id": 0, "polygon": [[133.5, 192.75], [350.2265625, 192.75], [350.2265625, 204.1875], [133.5, 204.1875]]}, {"title": "672 23 Gradient flows I", "heading_level": null, "page_id": 7, "polygon": [[132.90380859375, 25.982666015625], [246.75, 25.982666015625], [246.75, 35.312255859375], [132.90380859375, 35.312255859375]]}, {"title": "676 23 Gradient flows I", "heading_level": null, "page_id": 11, "polygon": [[133.5, 26.25], [247.4296875, 26.25], [247.4296875, 35.553955078125], [133.5, 35.553955078125]]}, {"title": "686 23 Gradient flows I", "heading_level": null, "page_id": 21, "polygon": [[133.5, 26.15185546875], [246.83203125, 26.15185546875], [246.83203125, 35.33642578125], [133.5, 35.33642578125]]}, {"title": "688 23 Gradient flows I", "heading_level": null, "page_id": 23, "polygon": [[133.1279296875, 26.25], [247.8779296875, 26.25], [247.8779296875, 35.43310546875], [133.1279296875, 35.43310546875]]}, {"title": "Diffusion equations as gradient flows", "heading_level": null, "page_id": 23, "polygon": [[133.5, 216.0], [351.421875, 216.0], [351.421875, 226.810546875], [133.5, 226.810546875]]}, {"title": "690 23 Gradient flows I", "heading_level": null, "page_id": 25, "polygon": [[133.5, 26.176025390625], [247.728515625, 26.176025390625], [247.728515625, 35.795654296875], [133.5, 35.795654296875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 45], ["Text", 3], ["TextInlineMath", 3], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 4453, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["Line", 66], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6111, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 68], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1048, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["Line", 83], ["Equation", 8], ["Text", 6], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1480, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 587], ["Line", 84], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1138, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 48], ["TextInlineMath", 7], ["Equation", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["Line", 63], ["Equation", 6], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 73], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4243, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 580], ["Line", 41], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 745], ["Line", 65], ["TextInlineMath", 5], ["Equation", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2170, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 731], ["Line", 83], ["Equation", 8], ["Text", 7], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2191, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 685], ["Line", 53], ["TextInlineMath", 5], ["Text", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1516, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 48], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 578], ["Line", 84], ["Text", 3], ["TextInlineMath", 3], ["Equation", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3108, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 686], ["Line", 133], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2937, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 74], ["TextInlineMath", 6], ["Equation", 5], ["Text", 4]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 577], ["Line", 69], ["Equation", 5], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1223, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 739], ["Line", 100], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3544, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 51], ["TextInlineMath", 5], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 594], ["Line", 67], ["Text", 6], ["Equation", 4], ["ListItem", 3], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2589, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 683], ["Line", 47], ["TextInlineMath", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 57], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 724], ["Line", 94], ["TextInlineMath", 5], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5248, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 35], ["TextInlineMath", 4], ["Equation", 3], ["Text", 3], ["ListItem", 3], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 618], ["Line", 54], ["TextInlineMath", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1125, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 61], ["Text", 6], ["TextInlineMath", 5], ["Equation", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 574, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-40"}