{"table_of_contents": [{"title": "Ricci curvature bounds from distorted displacement\nconvexity", "heading_level": null, "page_id": 0, "polygon": [[133.5, 268.189453125], [442.5, 268.189453125], [442.5, 293.326171875], [133.5, 293.326171875]]}, {"title": "492 17 Displacement convexity II", "heading_level": null, "page_id": 2, "polygon": [[133.5, 25.837646484375], [285.75, 25.837646484375], [285.75, 35.457275390625], [133.5, 35.457275390625]]}, {"title": "494 17 Displacement convexity II", "heading_level": null, "page_id": 4, "polygon": [[133.5, 26.2001953125], [285.75, 26.2001953125], [285.75, 35.2880859375], [133.5, 35.2880859375]]}, {"title": "496 17 Displacement convexity II", "heading_level": null, "page_id": 6, "polygon": [[133.5, 26.0068359375], [285.978515625, 26.0068359375], [285.978515625, 35.2880859375], [133.5, 35.2880859375]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 11, "polygon": [[234.0, 48.0], [359.490234375, 48.0], [359.490234375, 59.2646484375], [234.0, 59.2646484375]]}, {"title": "504 17 Displacement convexity II", "heading_level": null, "page_id": 14, "polygon": [[133.5, 25.91015625], [285.978515625, 25.91015625], [285.978515625, 35.4814453125], [133.5, 35.4814453125]]}, {"title": "506 17 Displacement convexity II", "heading_level": null, "page_id": 16, "polygon": [[133.5, 25.5], [286.5, 25.5], [286.5, 35.52978515625], [133.5, 35.52978515625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 49], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3084, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 559], ["Line", 64], ["TextInlineMath", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["Line", 63], ["TextInlineMath", 6], ["Equation", 3], ["ListItem", 3], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2052, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 48], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 53], ["Text", 5], ["TextInlineMath", 5], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1142, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 70], ["TextInlineMath", 7], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2895, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 644], ["Line", 117], ["TextInlineMath", 4], ["Equation", 4], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2292, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["Line", 75], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 672], ["Line", 109], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6247, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 566], ["Line", 121], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3994, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 586], ["Line", 39], ["TextInlineMath", 8], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 40], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 40], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 40], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["Line", 40], ["TextInlineMath", 3], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 44], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 10], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-29"}