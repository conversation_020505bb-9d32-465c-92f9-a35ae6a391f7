Since  $N' > 1$  we can apply the preceding computation with N replaced by  $N'$ :

$$
\int \rho_t(x)^{1-\frac{1}{N'}} d(x, T_{t\to 1}(x))^2 \nu(dx)
$$

$$
\leq C(N', q) \left( 1 + \int d(z, x)^q \mu_0(dx) + \int d(z, x)^q \mu_1(dx) \right)^{1-\frac{1}{N'}} \times \left( \int \frac{\nu(dx)}{(1+d(z, x))^{q(N'-1)-2N'}} \right)^{\frac{1}{N'}}.
$$

Then we may choose  $\theta$  so that  $q(N'-1) - 2N' = q(N-1) - 2N - \delta$ ;<br>that is,  $\theta = \delta/((q-2)N) \in (0, 1-1/N)$ . The conclusion follows.  $\square$ that is,  $\theta = \delta/((q-2)N) \in (0, 1-1/N)$ . The conclusion follows.

# Ricci curvature bounds from distorted displacement convexity

In Theorem 17.15, all the influence of the Ricci curvature bounds lies in the additional term  $\int_0^1 (\ldots) G(s, t) ds$ . As a consequence, as soon as  $K \neq 0$  and  $N < \infty$ , the formulation involves not only  $\mu_t$ ,  $\mu_0$  and  $\mu_1$ , but the whole geodesic path  $(\mu_s)_{0 \leq s \leq 1}$ . This makes the exploitation of the resulting inequality (in geometric applications, for instance) somewhat delicate, if not impossible.

I shall now present a different formulation, expressed only in terms of  $\mu_t$ ,  $\mu_0$  and  $\mu_1$ . As a price to pay, the functionals  $U_{\nu}(\mu_0)$  and  $U_{\nu}(\mu_1)$  will be replaced by more complicated expressions in which extra distortion coefficients will appear. From the technical point of view, this new formulation relies on the principle that one can "take the direction of motion out", in all reformulations of Ricci curvature bounds that were examined in Chapter 14.

Definition 17.25 (Distorted  $U_{\nu}$  functional). Let  $(\mathcal{X}, d)$  be a Polish space equipped with a Borel reference measure  $\nu$ . Let U be a convex function with  $U(0) = 0$ , let  $x \to \pi(dy|x)$  be a family of conditional probability measures on X, indexed by  $x \in \mathcal{X}$ , and let  $\beta$  be a measurable function  $\mathcal{X} \times \mathcal{X} \to (0, +\infty]$ . The distorted  $U_{\nu}$  functional with distortion coefficient  $\beta$  is defined as follows: For any measure  $\mu = \rho \nu$  on X,

$$
U_{\pi,\nu}^{\beta}(\mu) = \int_{\mathcal{X}\times\mathcal{X}} U\left(\frac{\rho(x)}{\beta(x,y)}\right) \beta(x,y) \,\pi(dy|x) \,\nu(dx). \tag{17.27}
$$

In particular, if  $\pi(dy|x) = \delta_{y=T(x)}$ , where  $T: \mathcal{X} \to \mathcal{X}$  is measurable, then

$$
U_{\pi,\nu}^{\beta}(\mu) = \int_{\mathcal{X}} U\left(\frac{\rho(x)}{\beta(x,T(x))}\right) \beta(x,T(x)) \nu(dx). \tag{17.28}
$$

Remark 17.26. Most of the time, I shall use Definition 17.25 with  $\beta = \beta_t^{(K,N)}$  $t_t^{(K,N)}$ , that is, the *reference distortion coefficients* introduced in Definition 14.19.

**Remark 17.27.** I shall often identify the conditional measures  $\pi$  with the probability measure  $\pi(dx\,dy) = \mu(dx)\,\pi(dy|x)$  on  $\mathcal{X} \times \mathcal{X}$ . Of course the joint measure  $\pi(dx\,dy)$  determines the conditional measures  $\pi(dy|x)$ only up to a  $\mu$ -negligible set of x; but this ambiguity has no influence on the value of  $(17.27)$  since  $U(0) = 0$ .

The problems of domain of definition which we encountered for the original  $U_{\nu}$  functionals also arise (even more acutely) for the distorted ones. The next theorem almost solves this issue.

Theorem 17.28 (Domain of definition of  $U_{\pi,\nu}^{\beta}$ ). Let  $(\mathcal{X},d)$  be a Polish space, equipped with a Borel reference measure  $\nu$ ; let  $K \in \mathbb{R}$ ,  $N \in [1,\infty]$ , and  $U \in \mathcal{DC}_N$ . Let  $\pi$  be a probability measure on  $\mathcal{X} \times \mathcal{X}$ , such that the marginal  $\mu$  of  $\pi$  is absolutely continuous with density  $\rho$ . Further, let  $\beta : \mathcal{X} \times \mathcal{X} \to (0, +\infty]$  be a measurable function such that

$$
\begin{cases}\n\beta \text{ is bounded} & (N < \infty) \\
\int_{\mathcal{X} \times \mathcal{X}} (\log \beta(x, y))_+ \pi(dx \, dy) < +\infty \\
\end{cases} \quad (N = \infty).
$$
\n(17.29)

If there exists  $x_0 \in \mathcal{X}$  and  $p \in [2, +\infty)$  such that

$$
\begin{cases}
\int_{\mathcal{X}} \frac{d\nu(x)}{[1+d(x_0,x)]^{p(N-1)}} < +\infty & (N < \infty) \\
\exists c > 0; \quad \int_{\mathcal{X}} e^{-c \, d(x_0,x)^p} \, d\nu(x) < +\infty & (N = \infty)
\end{cases}
$$

$$
(17.30)
$$

then the integral  $U_{\pi,\nu}^{\beta}(\mu)$  appearing in Definition 17.25 makes sense in  $\mathbb{R} \cup \{+\infty\}$  as soon as  $\mu \in P_p^{\rm ac}(\mathcal{X})$ .

Even if there is no such p,  $U^{\beta}_{\pi,\nu}(\mu)$  still makes sense in  $\mathbb{R} \cup \{+\infty\}$ if  $\mu \in P_c^{\rm ac}(\mathcal{X})$  and  $\nu$  is finite on compact sets.

Proof of Theorem 17.28. The argument is similar to the proof of Theorem 17.8. In the case  $N < \infty$ ,  $\beta$  bounded, it suffices to write

$$
\beta U(\rho/\beta) \ge -a \beta \left(\frac{\rho}{\beta} + \left(\frac{\rho}{\beta}\right)^{1-\frac{1}{N}}\right) = -a \rho - b \beta^{\frac{1}{N}} \rho^{1-\frac{1}{N}};
$$

then the right-hand side is integrable since  $\rho^{1-1/N}$  is integrable (as noted in the proof of Theorem 17.8).

In the case  $N = \infty$ , by Proposition 17.7(ii) there are positive constants a, b such that  $U(\rho) \ge a \rho \log \rho - b \rho$ ; so

$$
\beta(x, y) U\left(\frac{\rho(x)}{\beta(x, y)}\right) \ge a \beta(x, y) \left(\frac{\rho(x)}{\beta(x, y)}\right) \log\left(\frac{\rho(x)}{\beta(x, y)}\right) - b \rho(x)
$$
$$
= a \rho(x) \log \rho(x) - a \rho(x) \log \beta(x, y) - b \rho(x).
$$

We already know by the proof of Theorem 17.8 that  $(\rho \log \rho)$  and  $\rho$  lie in  $L^1(\nu)$ , or equivalently in  $L^1(\pi(dy|x)\nu(dx))$ . To check the integrability of the negative part of  $-a \rho \log \beta(x, y)$ , it suffices to note that

$$
\int \rho(x) \left(\log \beta(x, y)\right)_+ \pi(dy|x) \nu(dx) \le \int \left(\log \beta(x, y)\right)_+ \pi(dy|x) \mu(dx)
$$
$$
= \int \left(\log \beta(x, y)\right)_+ \pi(dx \, dy),
$$

which is finite by assumption. This concludes the proof of Theorem 17.28. ⊓⊔

**Application 17.29 (Definition of**  $U_{\pi,\nu}^{\beta_t^{(K,N)}}$ ). Let  $\mathcal{X} = M$  be a Riemannian manifold satisfying a  $CD(K, N)$  curvature-dimension bound, let  $t \in [0,1]$  and let  $\beta = \beta_t^{(K,N)}$  be the distortion coefficient defined in  $(14.61)$ – $(14.64)$ .

- If  $K \leq 0$ , then  $\beta_t^{(K,N)}$  $t^{(R,N)}$  is bounded;
- If  $K > 0$ ,  $N < \infty$  and diam  $(M) < D_{K,N} := \pi \sqrt{(N-1)/K}$ , then  $\beta$ is bounded;
- If  $K > 0$  and  $N = \infty$ , then  $\log \beta_t^{(K,N)}$  $t^{(K,N)}(x,y)$  is bounded above by a constant multiple of  $d(x, y)^2$ , which is  $\pi(dx\,dy)$ -integrable whenever  $\pi$  is an optimal coupling arising in some displacement interpolation.

In all three cases, Theorem 17.28 shows that  $U^{\beta}_{\pi,\nu}$  is well-defined in  $\mathbb{R} \cup \{+\infty\}$ , more precisely that the integrand entering the definition is bounded below by an integrable function. The only remaining cases are (a) when  $K > 0$ ,  $N < \infty$  and diam  $(M)$  coincides with the limit Bonnet–Myers diameter  $D_{K,N}$ ; and (b) when  $N = 1$ . These two cases are covered by the following definition.

Convention 17.30 (Definition of  $U_{\pi,\nu}^{\beta}$  in the limit cases). If either (a)  $K > 0$ ,  $N < \infty$  and diam  $(M) = \pi \sqrt{(N-1)/K}$  or (b)  $N = 1$ , I shall define

$$
U_{\pi,\nu}^{\beta_t^{(K,N)}}(\mu) = \lim_{N'\downarrow N} U_{\pi,\nu}^{\beta_t^{(K,N')}}(\mu). \tag{17.31}
$$

**Remark 17.31.** The limit in (17.31) is well-defined; indeed,  $\beta_t^{(K,N)}$  $t^{(K,N)}$  is increasing as N decreases, and  $U(r)/r$  is nondecreasing as a function of r; so  $U(\rho(x)/\beta_t^{(K,N)}(x,y))\,\beta_t^{(K,N)}$  $t^{(K,N)}(x,y)$  is a nonincreasing function of  $N$  and the limit in  $(17.31)$  is monotone. The monotone convergence theorem guarantees that this definition coincides with the original definition (17.27) when it applies, i.e. when the integrand is bounded below by a  $\pi(dy|x) \nu(dx)$ -integrable function.

The combination of Application 17.29 and Convention 17.30 ensures that  $U_{\pi,\nu}^{\beta_t^{(K,N)}}(\mu)$  is well-defined as soon as  $\int d(x,y)^2 \pi(dx\,dy) < +\infty$ ,  $\mu \in P_p^{\rm ac}(M)$ , and p satisfies (17.30).

**Remark 17.32.** In the limit case diam  $(M) = D_{K,N}$ , it is perfectly possible for  $U_{\pi,\nu}^{\beta_t^{(K,N)}}(\mu)$  to take the value  $-\infty$ . An example is when M is the sphere  $S^N$ ,  $K = N - 1$ ,  $\mu$  is uniform,  $U(r) = -N r^{1-1/N}$ , and  $\pi$  is the deterministic coupling associated with the map  $S: x \rightarrow -x$ . However, when  $\pi$  is an *optimal* coupling, it is impossible for  $U^{\beta^{(K,N)}}_{\pi,\nu}(\mu)$ to take the value  $-\infty$ .

**Remark 17.33.** If diam  $(M) = D_{K,N}$  then actually M is the sphere  $S^{N}(\sqrt{\frac{N-1}{K}})$  and  $\nu = \text{vol}$ ; but we don't need this information. (The assumption of  $M$  being complete without boundary is important for this statement to be true, otherwise the one-dimensional reference spaces of Examples 14.10 provide a counterexample.) See the end of the bibliographical notes for more explanations. In the case  $N = 1$ , if M is distinct from a point then it is one-dimensional, so it is either the real line or a circle.

Now comes the key notion in this section:

Definition 17.34 (Distorted displacement convexity). Let M be a Riemannian manifold, equipped with a reference measure ν. Let  $(\beta_t(x,y))_{0 \leq t \leq 1}$  be a family of measurable functions  $M \times M \to (0, +\infty],$ and let  $U : \mathbb{R}_+ \to \mathbb{R}$  be a continuous convex function with  $U(0) = 0$ . The functional  $U_{\nu}$  is said to be displacement convex with distortion  $(\beta_t)$ if, for any geodesic path  $(\mu_t)_{0 \leq t \leq 1}$  in the domain of  $U_{\nu}$ ,

$$
\forall t \in [0, 1], \qquad U_{\nu}(\mu_t) \le (1 - t) U_{\pi, \nu}^{\beta_{1-t}}(\mu_0) + t U_{\check{\pi}, \nu}^{\beta_t}(\mu_1), \qquad (17.32)
$$

where  $\pi$  stands for the optimal transference plan between  $\mu_0$  and  $\mu_1$ ; and  $\check{\pi}$  is obtained from  $\pi$  by switching the variables, that is  $\check{\pi} = S_{\#} \pi$ ,  $S(x_0,x_1)=(x_1,x_0).$ 

This notion can be localized as in Definition 17.13.

Remark 17.35. The inequality appearing in (17.32) can be rewritten more explicitly as

$$
\int U(\rho_t) \, d\nu
\leq (1-t) \int_{M \times M} U\left(\frac{\rho_0(x_0)}{\beta_{1-t}(x_0, x_1)}\right) \beta_{1-t}(x_0, x_1) \pi(dx_0 | x_1) \nu(dx_0) 
+ t \int_{M \times M} U\left(\frac{\rho_1(x_1)}{\beta_t(x_0, x_1)}\right) \beta_t(x_0, x_1) \pi(dx_1 | x_0) \nu(dx_1).
$$

**Remark 17.36.** Since  $U(r)/r$  is nondecreasing in r, the displacement convexity condition in Definition 17.34 becomes more stringent as  $\beta$ increases.

The next result is an alternative to Theorem 17.15; recall Convention 17.10.

Theorem 17.37 (CD bounds read off from distorted displacement convexity). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$  vol,  $V \in C^2(M)$ . Let  $K \in \mathbb{R}$  and  $N \in (1, \infty]$ ; let  $\beta_t^{(K,N)}$  $t^{(K,N)}(x,y)$  be defined as in (14.61). Further, let  $p \in [2,+\infty) \cup \{c\}$ satisfy (17.30). Then the following three conditions are equivalent:

(i) M satisfies the curvature-dimension bound  $CD(K, N);$ 

(ii) For each  $U \in \mathcal{DC}_N$ , the functional  $U_{\nu}$  is displacement convex on  $P_p^{\rm ac}(M)$  with distortion  $(\beta_t^{(K,N)})$  $\binom{(\mathbf{A},N)}{t}$ ;

(iii)  $H_{N,\nu}$  is locally displacement convex with distortion  $(\beta_t^{(K,N)})$  $\binom{(\mathbf{A},N)}{t}$ ; and then necessarily  $N \geq n$ , with equality possible only if V is constant.

Before explaining the proof of this theorem, let me state two very natural open problems (I have no idea how difficult they are).

Open Problem 17.38. Is there a natural "Eulerian" counterpart to Theorem 17.37?

Open Problem 17.39. Theorem 17.15 and 17.37 yield two different upper bounds for  $U_{\nu}(\mu_t)$ : on the one hand,

$$
U_{\nu}(\mu_t) \leq (1-t) U_{\nu}(\mu_0) + t U_{\nu}(\mu_1)
$$
$$
- K_{N,U} \int_0^1 \left( \int \rho_s(x)^{1-\frac{1}{N}} |\tilde{\nabla} \psi_s|^2 d\nu \right) G(s,t) ds; \quad (17.33)
$$

on the other hand,

$$
U_{\nu}(\mu_{t}) \leq (1-t) \int_{M} U\left(\frac{\rho_{0}(x_{0})}{\beta_{1-t}^{(K,N)}(x_{0},x_{1})}\right) \beta_{1-t}^{(K,N)}(x_{0},x_{1}) \pi(dx_{1}|x_{0}) d\nu(x_{0})
$$
$$
+ t \int_{M} U\left(\frac{\rho_{1}(x_{1})}{\beta_{t}^{(K,N)}(x_{0},x_{1})}\right) \beta_{t}^{(K,N)}(x_{0},x_{1}) \pi(dx_{0}|x_{1}) d\nu(x_{1}). \quad (17.34)
$$

Can one compare those two bounds, and if yes, which one is sharpest?

At least in the case  $N = \infty$ , inequality (17.34) implies (17.33): see Theorem 30.5 at the end of these notes.

Exercise 17.40. Show, at least formally, that inequalities (17.33) and (17.34) coincide asymptotically when  $\mu_0$  and  $\mu_1$  approach each other.

Proof of Theorem 17.37. The proof shares many common points with the proof of Theorem 17.15. I shall restrict to the case  $N < \infty$ , since the case  $N = \infty$  is similar.

Let us start with the implication (i)  $\Rightarrow$  (ii). In a first step, I shall assume that  $\mu_0$  and  $\mu_1$  are compactly supported, and (if  $K > 0$ )  $\text{diam}(M) < \pi \sqrt{(N-1)/K}$ . With the same notation as in the beginning of the proof of Theorem 17.15,

$$
\int_M U(\rho_t(x)) \, d\nu(x) = \int_M u(\delta_{t_0}(t,x)) \, d\mu_{t_0}(x).
$$

By applying inequality (14.56) in Theorem 14.12 (up to a factor which only depends on x and  $t_0$ ,  $\mathcal{D}(t)$  coincides with  $\delta_{t_0}(t,x)$ , and using the decreasing property of  $u$ , we get, with the same notation as in Theorem 14.12,

$$
\int_M U(\rho_t(x)) d\nu(x) \leq \int_M u\Big(\tau_{K,N}^{(1-t)} \delta_{t_0}(0,x) + \tau_{K,N}^{(t)} \delta_{t_0}(1,x)\Big) d\mu_{t_0}(x).
$$

Next, by the convexity of u, with coefficients t and  $1-t$ ,

$$
\int_{M} u(\tau_{K,N}^{(1-t)} \delta_{t_{0}}(0,x) + \tau_{K,N}^{(t)} \delta_{t_{0}}(1,x)) d\nu(x)
$$

$$
\leq (1-t) \int_{M} u(\frac{\tau_{K,N}^{(1-t)}}{1-t} \delta_{t_{0}}(0,x)) d\mu_{t_{0}}(x) + t \int_{M} u(\frac{\tau_{K,N}^{(t)}}{t} \delta_{t_{0}}(1,x)) d\mu_{t_{0}}(x).
$$

Since  $\beta_t^{(K,N)} = (\tau_{K,N}^{(t)}/t)^N$ , the right-hand side of the latter inequality can be rewritten as

$$
(1-t)\int_{M}\frac{\beta_{1-t}^{(K,N)}(x_0,x_1)}{\rho_0(x_0)} U\left(\frac{\rho_0(x_0)}{\beta_{1-t}^{(K,N)}(x_0,x_1)}\right) d\pi(x_0,x_1) + t\int_{M}\frac{\beta_t^{(K,N)}(x_0,x_1)}{\rho_1(x_1)} U\left(\frac{\rho_1(x_1)}{\beta_t^{(K,N)}(x_0,x_1)}\right) d\pi(x_0,x_1),
$$

which is the same as the right-hand side of (17.32).

In a second step, I shall relax the assumption of compact support by a restriction argument. Let  $\mu_0$  and  $\mu_1$  be two probability measures in  $P_p^{\text{ac}}(M)$ , and let  $(Z_\ell)_{\ell \in \mathbb{N}}$ ,  $(\mu_{t,\ell})_{0 \leq t \leq 1, \ell \in \mathbb{N}}$ ,  $(\pi_\ell)_{\ell \in \mathbb{N}}$  be as in Proposition 13.2. Let  $t \in [0,1]$  be fixed. By the first step, applied with the probability measures  $\mu_{t,\ell}$  and the nonlinearity  $U_{\ell}: r \to U(Z_{\ell} r)$ ,

$$
(U_{\ell})_{\nu}(\mu_{t,\ell}) \le (1-t) \left(U_{\ell}\right)_{\pi_{\ell},\nu}^{\beta_{1-t}^{(K,N)}}(\mu_{0,\ell}) + t \left(U_{\ell}\right)_{\check{\pi}_{\ell},\nu}^{\beta_{t}^{(K,N)}}(\mu_{1,\ell}). \tag{17.35}
$$

It remains to pass to the limit in (17.35) as  $\ell \to \infty$ . The lefthand side is handled in exactly the same way as in the proof of Theorem 17.15, and the problem is to pass to the limit in the right-hand side. To ease notation, I shall write  $\beta_t^{(K,N)} = \beta$ . Let us prove for instance

Ricci curvature bounds from distorted displacement convexity 497

$$
(U_{\ell})^{\beta}_{\pi_{\ell},\nu}(\mu_{0,\ell}) \xrightarrow[\ell \to \infty]{} U^{\beta}_{\pi,\nu}(\mu_0). \tag{17.36}
$$

Since  $\mu_0$  is absolutely continuous, the optimal transport plan  $\pi$ comes from a deterministic transport  $T$ , and similarly the optimal transport  $\pi_{\ell}$  comes from a deterministic transport  $T_{\ell}$ ; Proposition 13.2 guarantees that  $T_{\ell} = T$ ,  $\mu_{0,\ell}$ -almost surely. So the left-hand side of (17.36) can be rewritten as

$$
\int U\left(\frac{Z_{\ell}\,\rho_{0,\ell}(x_0)}{\beta(x_0,T(x_0))}\right)\,\beta(x_0,T(x_0))\,\nu(dx_0).
$$

Since  $U_+$  is a nondecreasing function and  $Z_{\ell} \rho_{0,\ell}$  is a nondecreasing sequence, the contribution of the positive part  $U_{+}$  is nondecreasing in  $\ell$ . On the other hand, the contribution of the negative part can be controlled as in the proof of Theorem 17.28:

$$
U - \left(\frac{Z_{\ell} \rho_{0,\ell}(x_0)}{\beta(x_0,T(x_0))}\right) \leq A\left(Z_{\ell} \rho_{0,\ell}(x_0) + \beta(x_0,T(x_0))^{\frac{1}{N}} Z_{\ell}^{1-\frac{1}{N}} \rho_{0,\ell}(x_0)^{1-\frac{1}{N}}\right) \leq A\left(\rho_{0}(x_0) + \beta(x_0,T(x_0))^{\frac{1}{N}} \rho_{0}(x_0)^{1-\frac{1}{N}}\right).
$$

Theorem 17.28 (together with Application 17.29) shows that the latter quantity is always integrable. As a conclusion,

$$
\int U_+ \left( \frac{Z_{\ell} \rho_{0,\ell}(x_0)}{\beta(x_0, T(x_0))} \right) \beta(x_0, T(x_0)) \nu(dx_0)
$$

$$
\xrightarrow[\ell \to \infty]{} \int U_+ \left( \frac{\rho_0(x_0)}{\beta(x_0, T(x_0))} \right) \beta(x_0, T(x_0)) \nu(dx_0)
$$

by monotone convergence; while

$$
\int U_{-} \left( \frac{Z_{\ell} \rho_{0,\ell}(x_0)}{\beta(x_0, T(x_0))} \right) \beta(x_0, T(x_0)) \nu(dx_0)
$$

$$
\xrightarrow[\ell \to \infty]{} \int U_{-} \left( \frac{\rho_0(x_0)}{\beta(x_0, T(x_0))} \right) \beta(x_0, T(x_0)) \nu(dx_0)
$$

by dominated convergence. So (17.36) holds true, and we can pass to the limit in all the terms of (17.35).

In a third step, I shall treat the limit case diam  $(M) = D_{K,N}$  $\pi\sqrt{(N-1)/K}$ . To do this, note that M satisfies  $CD(K, N')$  for any  $N' > N$ ; then diam  $(M) < D_{K,N'}$ , so, by the previous step,

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N')}}(\mu_0) + t U_{\tilde{\pi},\nu}^{\beta_t^{(K,N')}}(\mu_1).
$$

The conclusion follows by letting  $N'$  decrease to  $N$  and recalling Convention 17.30. This concludes the proof of (i)  $\Rightarrow$  (ii).

It is obvious that (ii)  $\Rightarrow$  (iii). So let us now consider the implication (iii)  $\Rightarrow$  (i). For brevity I shall only consider the case  $N > n$ . (The case  $N = n$  and  $V = constant$  is similar; the other cases are ruled out in the same way as in the end of the proof of Theorem 17.15.) Let  $x_0 \in M$ ,  $v_0 \in T_{x_0}M$ ; the goal is to show that  $\text{Ric}_{N,\nu}(v_0) \geq K$ . Construct  $\psi$ ,  $\psi$  and  $(\mu_t)_{0 \le t \le 1}$  as in the proof of the implication (ii)  $\Rightarrow$ (iii) in Theorem 17.15. Recall (17.21): As  $\theta \to 0$ ,

$$
H_{N,\nu}(\mu_t) - (1 - t) H_{N,\nu}(\mu_0) - t H_{N,\nu}(\mu_1) \geq -\theta^2 \left( \text{Ric}_{N,\nu}(v_0) + O(\theta) \right) \int_0^1 \int_M \rho_s(y)^{1 - \frac{1}{N}} d\nu(y) G(s,t) ds. \quad (17.37)
$$

The change of variables  $x \to T_s(x)$  is smooth and has the Jacobian  $\mathcal{J}_{0\rightarrow s}(x) = 1 + O(\theta)$ . So

$$
\int \rho_s(x)^{1-\frac{1}{N}} \nu(dx) = \int \rho_s(T_{0\to s}(x))^{1-\frac{1}{N}} \mathcal{J}_{0\to s}(x) \nu(dx)
$$
$$
= \int \frac{\rho_0(x)^{1-\frac{1}{N}}}{\mathcal{J}_{0\to s}(x)^{1-\frac{1}{N}}} \mathcal{J}_{0\to s}(x) \nu(dx)
$$
$$
= \int \rho_0(x)^{1-\frac{1}{N}} \mathcal{J}_{0\to s}(x)^{\frac{1}{N}} \nu(dx)
$$
$$
= (1 + O(\theta)) \left( \int \rho_0^{1-\frac{1}{N}} d\nu \right) ;
$$

thus (17.37) can be recast as

$$
H_{N,\nu}(\mu_t) - (1 - t) H_{N,\nu}(\mu_0) - t H_{N,\nu}(\mu_1) \geq -\theta^2 \operatorname{Ric}_{N,\nu}(v_0) \left(\frac{t(1 - t)}{2}\right) \left(\int_M \rho_0^{1 - \frac{1}{N}} d\nu\right) + O(\theta^3). \quad (17.38)
$$

(Recall that  $\int G(s,t) ds = t(1-t)/2.$ )

On the other hand, with obvious notation, the left-hand side of (17.38) is (by assumption) bounded above by

$$
(1-t)\left(H_{N,\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) - H_{N,\nu}(\mu_0)\right) + t\left(H_{N,\tilde{\pi},\nu}^{\beta_{t}^{(K,N)}}(\mu_1) - H_{N,\nu}(\mu_1)\right). \tag{17.39}
$$

Let us see how this expression behaves in the limit  $\theta \to 0$ ; for instance I shall focus on the first term in (17.39). From the definitions,

$$
H_{N,\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) - H_{N,\nu}(\mu_0) = N \int \rho_0(x)^{1-\frac{1}{N}} \left(1 - \beta_{1-t}^{(K,N)}(x,T(x))^{\frac{1}{N}}\right) d\nu(x),\tag{17.40}
$$

where  $T = \exp(\nabla \psi)$  is the optimal transport from  $\mu_0$  to  $\mu_1$ . A standard Taylor expansion shows that

$$
\beta_{1-t}^{(K,N)}(x,y)^{\frac{1}{N}} = 1 + \frac{K[1 - (1-t)^2]}{6N} d(x,y)^2 + O(d(x,y)^4);
$$

plugging this back into (17.40), we find

$$
H_{N,\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) - H_{N,\nu}(\mu_0)
$$
$$
= -\frac{K[1 - (1 - t)^2]}{6} \int \rho_0(x)^{1 - \frac{1}{N}} (\theta^2 |v_0|^2 + O(\theta^3)) d\nu(x)
$$
$$
= -(\theta^2 |v_0|^2 + O(\theta^3)) \frac{K[1 - (1 - t)^2]}{6} \left(\int \rho_0^{1 - \frac{1}{N}} d\nu\right).
$$

A similar computation can be performed for the second term in (17.39), taking into account  $\int \rho_1^{1-\frac{1}{N}} d\nu = \int \rho^{1-\frac{1}{N}} d\nu + O(\theta)$ . Then the whole expression (17.39) is equal to

$$
-\theta^2 K \left( \frac{(1-t)[1-(1-t)^2] + t[1-t^2]}{6} \right) |v_0|^2 \left( \int \rho^{1-\frac{1}{N}} d\nu \right) + O(\theta^3)
$$
  
= 
$$
-\theta^2 \frac{Kt(1-t)}{2} |v_0|^2 \left( \int \rho^{1-\frac{1}{N}} d\nu \right) + O(\theta^3).
$$

Since this is an upper bound for the right-hand side of (17.38), we obtain, after some simplification,

$$
Ric_{N,\nu}(v_0) + O(\theta) \ge K|v_0|^2 + O(\theta),
$$

and the conclusion follows upon taking the limit  $\theta \to 0$ . □

The case  $N = 1$  was not addressed in Theorem 17.37, since the functional  $H_{1,\nu}$  was not defined, so Property (iii) would not make sense. However, the rest of the theorem holds true:

Theorem 17.41 (One-dimensional CD bounds and displacement convexity). Let M be an n-dimensional Riemannian manifold, equipped with a reference measure  $\nu = e^{-V}$ vol, where  $V \in C^2(M)$ . Let  $K \in \mathbb{R}$  and let  $\beta_t^{(K,1)}$  $t_t^{(N,1)}(x,y)$  be defined as in (14.63). Then the following two conditions are equivalent:

(i) M satisfies the curvature-dimension bound  $CD(K, 1)$ ;

(ii) For each  $U \in \mathcal{DC}_1$ , the functional  $U_{\nu}$  is displacement convex on  $P_c^{\rm ac}(M)$  with distortion  $(\beta_t^{(K,1)})$  $t^{(N,1)}$ ;

and then necessarily  $n = 1$ , V is constant and  $K \leq 0$ .

*Proof of Theorem 17.41.* When  $K > 0$ , (i) is obviously false since  $\nu$  has to be equal to vol (otherwise Ric<sub>1, $\nu$ </sub> will take values  $-\infty$ ); but (ii) is obviously false too since  $\beta_t^{(K,1)} = +\infty$  for  $0 < t < 1$ . So we may assume that  $K \leq 0$ . Then the proof of (i)  $\Rightarrow$  (ii) is along the same lines as in Theorem 17.37. As for the implication (ii)  $\Rightarrow$  (i), note that  $\mathcal{DC}_{N'} \subset \mathcal{DC}_1$ for all  $N' < 1$ , so M satisfies Condition (ii) in Theorem 17.37 with N replaced by N', and therefore  $\text{Ric}_{N',\nu} \geq Kg$ . If  $N' < 2$ , this forces M to be one-dimensional. Moreover, if V is not constant there is  $x_0$  such that  $\text{Ric}_{N',\nu} = V'' - (V')^2/(N'-1)$  is  $\lt K$  for  $N'$  small enough. So V is constant and  $\text{Ric}_{1,\nu} = \text{Ric} = 0$ , a fortiori  $\text{Ric}_{1,\nu} \geq K$ . □

I shall conclude this chapter with an "intrinsic" theorem of displacement convexity, in which the distortion coefficient  $\beta$  only depends on M and not on a priori given parameters  $K$  and  $N$ . Recall Definition 14.17 and Convention 17.10.

Theorem 17.42 (Intrinsic displacement convexity). Let M be a Riemannian manifold of dimension  $n \geq 2$ , and let  $\beta_t(x, y)$  be a continuous positive function on  $[0,1] \times M \times M$ . Let  $p \in [2,+\infty) \cup \{c\}$  such that (17.30) holds true with  $\nu = \text{vol}$  and  $N = n$ . Then the following three statements are equivalent:

(i)  $\beta \leq \beta$ ;

(ii) For any  $U \in \mathcal{DC}_n$ , the functional  $U_{\nu}$  is displacement convex on  $P_p^{\text{ac}}(M)$  with distortion coefficients  $(\beta_t)$ ;

(iii) The functional  $H_n$  is locally displacement convex with distortion coefficients  $(\beta_t)$ .

The proof of this theorem is along the same lines as the proof of Theorem 17.37, with the help of Theorem 14.20; details are left to the reader.

## Bibliographical notes

Historically, the first motivation for the study of displacement convexity was to establish theorems of unique minimization for functionals which are not strictly convex [614]; a recent development can be found in [202]. In the latter reference, the authors note that some independent earlier works by Alberti and Bellettini [10, 13] can be reinterpreted in terms of displacement convexity.

The definition of the displacement convexity classes  $DC_N$  goes back to McCann's PhD thesis [612], in the case  $N < \infty$ . (McCann required u in Definition 17.1 to be nonincreasing; but this is automatic, as noticed in Remark 17.2.) The definition of  $\mathcal{DC}_{\infty}$  is taken from [577]. Conditions (i), (ii) or (iii) in Definition 17.1 occur in various contexts, in particular in the theory of nonlinear diffusion equations (as we shall see in Chapter 23), so it is normal that these classes of nonlinearities were rediscovered later by several authors. The normalization  $U(0) = 0$  is not the only possible one  $(U(1) = 0$  would also be convenient in a compact setting), but it has many advantages. In [612] or more recently [577] it is not imposed that U should be twice differentiable on  $(0, +\infty)$ .

Theorems 17.15 and 17.37 form the core of this chapter. They result from the contributions of many authors and the story is roughly as follows.

McCann [614] established the displacement convexity of  $U_{\nu}$  when  $M = \mathbb{R}^n$ ,  $n = N$  and  $\nu$  is the Lebesgue measure. Things were made somewhat simpler by the Euclidean setting (no Jacobi fields, no  $d^2/2$ convex functions, etc.) and by the fact that only displacement convexity (as opposed to Λ-displacement convexity) was considered. Apart from that, the strategy was essentially the same as the one used in this chapter, based on a change of variables, except that the reference measure was  $\mu_0$  instead of  $\mu_{t_0}$ . McCann's proof was recast in my book [814, Proof of Theorem 5.15 (i)]; it takes only a few lines, once one has accepted (a) the concavity of det<sup>1/n</sup> in  $\mathbb{R}^n$ : that is, if a symmetric matrix  $S \leq I_n$ is given, then  $t \mapsto \det(I_n - tS)^{1/n}$  is concave [814, Lemma 5.21]; and (b) the change of variables formula along displacement interpolation.

Later, Cordero-Erausquin, McCann and Schmuckenschläger [246] studied genuinely Riemannian situations, replacing the concavity of  $\det^{1/n}$  in  $\mathbb{R}^n$  by distortion estimates, and extending the formula of change of variables along displacement interpolation. With these tools they basically proved the displacement convexity of  $U_{\nu}$  for  $U \in \mathcal{DC}_N$ , as soon as M is a Riemannian manifold of dimension  $n \leq N$  and nonnegative Ricci curvature, with the reference measure  $\nu = \text{vol}$ . It is clear from their paper that their argument also yields, for instance, K-displacement convexity of H as soon as Ric  $\geq K$ ; moreover, they established (i)  $\Rightarrow$  (ii) in Theorem 17.42 for compactly supported densities.

Several authors independently felt the need to rewrite more explicitly the connection between Jacobi fields and optimal transport, which was implicit in [246]. This was done simultaneously by Cordero-Erausquin, McCann and Schmuckenschläger [247] again; by Sturm [761]; and by Lott and myself [577]. All those arguments heavily draw on [246], and they are also reminiscent of arguments used in the proof of the Lévy–Gromov isoperimetric inequality. A large part of the proofs was actually devoted to establish the Jacobian estimates on the exponential function, which I recast here as part of Chapter 14.

Modifications needed to replace the volume measure by  $\nu = e^{-V}$  vol were discussed by Sturm [761] for  $N = \infty$ ; and independently by Lott and myself [577] for  $N \leq \infty$ . For the purpose of this course, all those modifications were included in the section about "change of measure" in Chapter 14.

It was first proven by Sturm and von Renesse [764] that the displacement convexity of  $H$  does not only result from, but actually characterizes the nonnegativity of the Ricci curvature. This statement was generalized by Lott and myself [577], and independently Sturm [762].

In a major contribution, Sturm [763] realized the importance and flexibility of the distorted displacement convexity to encode Ricci curvature bounds. He proved Theorem 17.37 in the most important case  $U = U_N$ . As we saw, the proof rests on the inequality (14.56) in Theorem 14.12, which is (as far as I know) due to Cordero-Erausquin, McCann and Schmuckenschläger [246] (in the case  $n = N$ ). Then the general formulation with arbitrary  $U \in \mathcal{DC}_N$  was worked out shortly after by Lott and myself [578]. All this was for  $N < \infty$ ; but then the case  $N = \infty$  works the same, once one has the correct definitions for  $DC_{\infty}$  and  $\beta_t^{(K,\infty)}$ .

In a very recent work, Ohta [657] extended these results to Finsler geometries.

Displacement convexity is not the only way to "synthetically" reformulate lower bounds on the Ricci curvature tensor. An alternative approach is via the study of rates of contraction of diffusion processes in Wasserstein distances. For instance, Sturm and von Renesse [764]

proved that  $\text{Ric} \geq 0$  is equivalent to the property that the heat equation is a contraction in  $W_p$  distance, where p is fixed in  $[1,\infty)$ . Also, Sturm [761] showed that a Riemannian manifold (equipped with the volume measure) satisfies  $CD(0, N)$  if and only if the nonlinear equation  $\partial_t \rho = \Delta \rho^m$  is a contraction for  $m \geq 1 - 1/N$ . (There is a more complicated criterion for  $CD(K, N)$ .) As will be explained in Chapter 24, these results are natural in view of the gradient flow structure of these diffusion equations.

Even if one sticks to displacement convexity, there are possible variants in which one allows the functional to explicitly depend on the interpolation time. Lott [576] showed that a measured Riemannian manifold  $(M, \nu)$  satisfies CD(0, N) if and only if  $t \mapsto t H_{\nu}(\mu_t) + N t \log t$ is a convex function of  $t \in [0, 1]$  along any displacement interpolation. There is also a more general version of this statement for  $CD(K, N)$ bounds.

Now come some more technical details. The use of Theorem 17.8 to control noncompactly supported probability densities is essentially taken from [577]; the only change with respect to that reference is that I do not try to define  $U_{\nu}$  on the whole of  $P_2^{\text{ac}}$ , and therefore do not require  $p$  to be equal to 2.

In this chapter I used restriction arguments to remove the compactness assumption. An alternative strategy consists in using a density argument and stability theorems (as in [577, Appendix E]); these tools will be used later in Chapters 23 and 30. If the manifold has nonnegative sectional curvature, it is also possible to directly apply the argument of change of variables to the family  $(\mu_t)$ , even if it is not compactly supported, thanks to the uniform inequality (8.45).

Another innovation in the proofs of this chapter is the idea of choosing  $\mu_{t_0}$  as the reference measure with respect to which changes of variables are performed. The advantage of that procedure (which evolved from discussions with Ambrosio) is that the transport map from  $\mu_{t_0}$ to  $\mu_t$  is Lipschitz for all times t, as we know from Chapter 8, while the transport map from  $\mu_0$  to  $\mu_1$  is only of bounded variation. So the proof given in this section only uses the Jacobian formula for Lipschitz changes of variables, and not the more subtle formula for BV changes of variables.

Paths  $(\mu_t)_{0 \leq t \leq 1}$  defined in terms of transport from a given measure  $\widetilde{\mu}$  (not necessarily of the form  $\mu_{t_0}$ ) are studied in [30] in the context of generalized geodesics in  $P_2(\mathbb{R}^n)$ . The procedure amounts to considering

 $\mu_t = (T_t)_{\#}\widetilde{\mu}$  with  $T_t(x) = (1-t)T_0(x) + tT_1(x)$ , where  $T_0$  is optimal between  $\tilde{\mu}$  and  $\mu_0$ , and  $T_1$  is optimal between  $\tilde{\mu}$  and  $\mu_1$ . Displacement convexity theorems work for these generalized geodesics just as fine as for true geodesics; one reason is that  $t \mapsto \det((1-t)A + tB)^{1/n}$  is concave for any two nonnegative matrices  $A$  and  $B$ , not just in the case  $A = I_n$ . These results are useful in error estimates for gradient flows; they have not yet been adapted to a Riemannian context.

The proofs in the present chapter are of Lagrangian nature, but, as I said before, it is also possible to use Eulerian arguments, at the price of further regularization procedures (which are messy but more or less standard), see in particular the original contribution by Otto and Westdickenberg [673]. As pointed out by Otto, the Eulerian point of view, although more technical, has the merit of separating very clearly the input from local smooth differential geometry (Bochner's formula is a purely local statement about the Laplace operator on  $M$ , seen as a differential operator on very smooth functions) and the input from global nonsmooth analysis (Wasserstein geodesics involve  $d^2/2$ convexity, which is a nonlocal condition; and  $d^2/2$ -convex functions are in general nonsmooth). Then Daneri and Savaré [271] showed that the Eulerian approach could be applied even without smoothness, roughly speaking by encoding the convexity property into the existence of a suitable gradient flow, which can be defined for nonsmooth data.

Apart from functionals of the form  $U_{\nu}$ , most displacement convex functionals presently known are constructed with functionals of the form  $\Phi : \mu \longmapsto \int \Phi(x) d\mu(x)$ , or  $\Psi : \mu \longmapsto \int \Psi(x, y) d\mu(x) d\mu(y)$ , where  $\Phi$  is a given "potential" and  $\Psi$  is a given "interaction potential" [84, 213, 214]. Sometimes these functionals appear in disguise [202].

It is easy to show that the displacement convexity of  $\Phi$  (seen as a function on  $P_2(M)$  is implied by the geodesic convexity of  $\Phi$ , seen as a function on  $M$ . Similarly, it is not difficult to show that the displacement convexity of  $\Psi$  is implied by the geodesic convexity of  $\Psi$ , seen as a function on  $M \times M$ . These results can be found for instance in my book [814, Theorem 5.15] in the Euclidean setting. (There it is assumed that  $\Psi(x,y) = \Psi(x-y)$ , with  $\Psi$  convex, but it is immediate to generalize the proof to the case where  $\Psi$  is convex on  $\mathbb{R}^n \times \mathbb{R}^n$ .) Under mild assumptions, there is in fact *equivalence* between the displacement convexity of  $\Psi$  on  $P_2(M)$  and the geodesic convexity of  $\Psi$ on  $M \times M$ . This is the case for instance if  $M = \mathbb{R}^n$ ,  $n \geq 2$ , and  $\Psi(x,y) = \Psi(x-y)$ , where  $\Psi(z)$  is an even continuous function of z. Contrary to what is stated in [814], this is false in dimension 1; in fact  $\mu \longmapsto \int W(x - y) \mu(dx) \mu(dy)$  is displacement convex on  $P_2(\mathbb{R})$  if and only if  $z \mapsto W(z) + W(-z)$  is convex on R<sub>+</sub> (This is because, by monotonicity,  $(x, y) \longmapsto (T(x), T(y))$  preserves the set  $\{y \ge x\} \subset \mathbb{R}^2$ . As a matter of fact, an interesting example coming from statistical mechanics, where W is not convex on the whole of  $\mathbb{R}$ , is discussed in [202].

There is no simple displacement convexity statement known for the Coulomb interaction potential; however, Blower [123] proved that

$$
E(\mu) = \frac{1}{2} \int_{\mathbb{R}^2} \log \frac{1}{|x - y|} \mu(dx) \mu(dy)
$$

defines a displacement convex functional on  $P_2^{\text{ac}}(\mathbb{R})$ . Blower further studied what happens when one adds a potential energy to  $E$ , and used these tools to establish concentration inequalities for the eigenvalues of some large random matrices. Also Calvez and Carrillo [196, Chapter 7] recently gave a sharp analysis of the defect of displacement convexity for the logarithmic potential in dimension 1 (which arguably should be the worst) with applications to the long-time study of a one-dimensional nonlinear diffusion equation modeling chemotaxis.

Exercise 17.43. Let M be a compact Riemannian manifold of dimension  $n \geq 2$ , and let  $\Psi$  be a continuous function on  $M \times M$ ; show that  $\Psi$  defines a displacement functional on  $P_2(M)$  if and only if  $(x,y) \longmapsto \Psi(x,y) + \Psi(y,x)$  is geodesically convex on  $M \times M$ . *Hints:* Note that a product of geodesics in  $M$  is also a geodesic in  $M \times M$ . First show that  $\Psi$  is locally convex on  $M \times M \setminus \Delta$ , where  $\Delta = \{(x, x)\}\subset M\times M$ . Use a density argument to conclude; note that this argument fails if  $n = 1$ .

I shall conclude with some comments about Remark 17.33. The classical Cheng–Toponogov theorem states the following: If a Riemannian manifold M has dimension N, Ricci curvature bounded below by  $K > 0$ , and diameter equal to the limit Bonnet–Myers diameter  $D_{K,N} = \pi \sqrt{(N-1)/K}$ , then it is a sphere. I shall explain why this result remains true when the reference measure is not the volume, and M is assumed to satisfy  $CD(K, N)$ . Cheng's original proof was based on eigenvalue comparisons, but there is now a simpler argument relying on the Bishop–Gromov inequality [846, p. 229]. This proof goes through when the volume measure is replaced by another reference measure  $\nu$ , and then one sees that  $\Psi = -\log(d\nu/d\text{vol})$  should solve a certain differential equation of Ricatti type (replace the inequality in [573, (4.11)]

by an equality). Then the second fundamental forms of the spheres in M have to be the same as in  $S^N$ , and one gets a formula for the radial derivative of  $\Psi$ . After some computations, one finds that M is an nsphere of diameter  $D_{K,N}$ ; and that the measure  $\nu$ , in coordinates  $(r, \theta)$ from the north pole, is  $c(\sin(kr))^{N-n}$  times the volume, where  $c > 0$ and  $k = \sqrt{K/(N-1)}$ . If  $n < N$ , the density of  $d\nu/d$ vol vanishes at the north pole, which is not allowed by our assumptions. The only possibility left out is that M has dimension N and  $\nu$  is a constant multiple of the volume. All this was explained to me by Lott.