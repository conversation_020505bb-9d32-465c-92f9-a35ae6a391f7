{"table_of_contents": [{"title": "Smoothness results", "heading_level": null, "page_id": 0, "polygon": [[133.27734375, 47.783935546875], [248.25, 47.783935546875], [248.25, 58.44287109375], [133.27734375, 58.44287109375]]}, {"title": "332 12 Smoothness", "heading_level": null, "page_id": 1, "polygon": [[133.35205078125, 26.24853515625], [230.6953125, 26.24853515625], [230.6953125, 35.4814453125], [133.35205078125, 35.4814453125]]}, {"title": "334 12 Smoothness", "heading_level": null, "page_id": 3, "polygon": [[133.1279296875, 26.103515625], [228.7529296875, 26.103515625], [228.7529296875, 35.384765625], [133.1279296875, 35.384765625]]}, {"title": "Appendix: Strong twist condition for squared distance,\nand rectifiability of cut locus", "heading_level": null, "page_id": 5, "polygon": [[133.5, 327.75], [459.298828125, 327.75], [459.298828125, 352.6875], [133.5, 352.6875]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 6, "polygon": [[233.25, 231.451171875], [358.5, 231.451171875], [358.5, 242.279296875], [233.25, 242.279296875]]}, {"title": "340 12 Smoothness", "heading_level": null, "page_id": 9, "polygon": [[133.20263671875, 26.25], [228.603515625, 26.25], [228.603515625, 35.650634765625], [133.20263671875, 35.650634765625]]}, {"title": "342 12 Smoothness", "heading_level": null, "page_id": 11, "polygon": [[132.8291015625, 26.25], [229.6494140625, 26.25], [229.6494140625, 35.771484375], [132.8291015625, 35.771484375]]}, {"title": "346 12 Smoothness", "heading_level": null, "page_id": 15, "polygon": [[133.5, 26.25], [229.3505859375, 26.25], [229.3505859375, 35.6748046875], [133.5, 35.6748046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 39], ["Text", 4], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2001, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 914], ["Line", 40], ["TextInlineMath", 9], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 36], ["TextInlineMath", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 54], ["TextInlineMath", 3], ["Text", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1214, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 535], ["Line", 64], ["Text", 5], ["TextInlineMath", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1496, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 613], ["Line", 35], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 43], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 40], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 40], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 39], ["Text", 4], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 39], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 44], ["Text", 5], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 40], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 40], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 169], ["Line", 40], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 33], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-19"}