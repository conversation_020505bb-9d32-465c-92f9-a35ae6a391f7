{"table_of_contents": [{"title": "8.6 MCMC for Sam<PERSON> from the Posterior", "heading_level": null, "page_id": 0, "polygon": [[132.0, 336.75], [412.5, 336.75], [412.5, 350.3671875], [132.0, 350.3671875]]}, {"title": "280 8. Model Inference and Averaging", "heading_level": null, "page_id": 1, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "Algorithm 8.4 Gibbs sampling for mixtures.", "heading_level": null, "page_id": 2, "polygon": [[132.8291015625, 135.75], [332.25, 135.75], [332.25, 146.25], [132.8291015625, 146.25]]}, {"title": "282 8. Model Inference and Averaging", "heading_level": null, "page_id": 3, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "8.7 Bagging", "heading_level": null, "page_id": 3, "polygon": [[132.0, 277.5], [216.0, 277.5], [216.0, 291.392578125], [132.0, 291.392578125]]}, {"title": "8.7.1 Example: Trees with Simulated Data", "heading_level": null, "page_id": 4, "polygon": [[133.5, 453.75], [357.75, 453.75], [357.75, 464.44921875], [133.5, 464.44921875]]}, {"title": "286 8. Model Inference and Averaging", "heading_level": null, "page_id": 7, "polygon": [[132.75, 89.25], [309.0, 89.25], [309.0, 98.85498046875], [132.75, 98.85498046875]]}, {"title": "288 8. Model Inference and Averaging", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [309.75, 88.5], [309.75, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "8.8 Model Averaging and Stacking", "heading_level": null, "page_id": 9, "polygon": [[132.0, 423.75], [355.60546875, 423.75], [355.60546875, 436.9921875], [132.0, 436.9921875]]}, {"title": "290 8. Model Inference and Averaging", "heading_level": null, "page_id": 11, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 98.61328125], [132.0, 98.61328125]]}, {"title": "8.9 Stochastic Search: Bumping", "heading_level": null, "page_id": 11, "polygon": [[132.0, 565.3828125], [339.46875, 565.3828125], [339.46875, 578.53125], [132.0, 578.53125]]}, {"title": "292 8. Model Inference and Averaging", "heading_level": null, "page_id": 13, "polygon": [[132.0, 88.5], [309.0, 88.5], [309.0, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 13, "polygon": [[132.75, 434.671875], [255.0, 434.671875], [255.0, 447.8203125], [132.75, 447.8203125]]}, {"title": "Exercises", "heading_level": null, "page_id": 14, "polygon": [[132.2314453125, 198.75], [190.2041015625, 198.75], [190.2041015625, 210.955078125], [132.2314453125, 210.955078125]]}, {"title": "294 8. Model Inference and Averaging", "heading_level": null, "page_id": 15, "polygon": [[132.0, 87.75], [309.75, 87.75], [309.75, 99.0], [132.0, 99.0]]}, {"title": "Additive Models, Trees, and Related\nMethods", "heading_level": null, "page_id": 16, "polygon": [[132.0, 137.3818359375], [438.08203125, 137.3818359375], [438.08203125, 182.2412109375], [132.0, 182.2412109375]]}, {"title": "9.1 Generalized Additive Models", "heading_level": null, "page_id": 16, "polygon": [[132.0, 480.75], [344.548828125, 480.75], [344.548828125, 494.61328125], [132.0, 494.61328125]]}, {"title": "296 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 17, "polygon": [[132.0, 89.25], [367.55859375, 89.25], [367.55859375, 98.3232421875], [132.0, 98.3232421875]]}, {"title": "9.1.1 Fitting Additive Models", "heading_level": null, "page_id": 18, "polygon": [[133.5, 411.0], [291.75, 411.0], [291.75, 421.91015625], [133.5, 421.91015625]]}, {"title": "9.1.2 Example: Additive Logistic Regression", "heading_level": null, "page_id": 20, "polygon": [[133.20263671875, 441.75], [365.16796875, 441.75], [365.16796875, 452.84765625], [133.20263671875, 452.84765625]]}, {"title": "http://www.stat.auckland.ac.nz/∼yee.", "heading_level": null, "page_id": 21, "polygon": [[197.25, 496.5], [389.25, 496.5], [389.25, 507.375], [197.25, 507.375]]}, {"title": "Example: Predicting <PERSON><PERSON>", "heading_level": null, "page_id": 21, "polygon": [[133.5, 519.0], [278.25, 519.0], [278.25, 529.41796875], [133.5, 529.41796875]]}, {"title": "304 9. Additive Models, Trees, and Related Methods", "heading_level": null, "page_id": 25, "polygon": [[132.0, 88.5], [368.15625, 88.5], [368.15625, 98.419921875], [132.0, 98.419921875]]}, {"title": "9.1.3 Summary", "heading_level": null, "page_id": 25, "polygon": [[133.5, 429.75], [220.0869140625, 429.75], [220.0869140625, 440.859375], [133.5, 440.859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 39], ["ListItem", 4], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5254, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 52], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 57], ["ListItem", 6], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1819, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 45], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["Line", 43], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 84], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1011, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 49], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 810, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 45], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 23], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 734, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 938], ["Line", 415], ["Text", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1492, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 65], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 55], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1733], ["Line", 799], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2242, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 42], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 35], ["Text", 4], ["TextInlineMath", 4], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 20], ["Text", 3], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 25], ["Text", 6], ["SectionHeader", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 47], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 55], ["Text", 6], ["ListItem", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 49], ["Text", 4], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 43], ["Text", 4], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 38], ["Text", 8], ["ListItem", 7], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 40], ["TableCell", 11], ["Text", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4133, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["TableCell", 249], ["Line", 43], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 106], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 43], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_298-323"}