{"table_of_contents": [{"title": "Optimal transport, old and new", "heading_level": null, "page_id": 0, "polygon": [[132.75, 167.25], [453.75, 165.75], [453.75, 193.2626953125], [132.75, 195.0]]}, {"title": "Contents", "heading_level": null, "page_id": 2, "polygon": [[132.0, 95.25], [200.25, 95.25], [200.25, 111.8583984375], [132.0, 111.8583984375]]}, {"title": "VIII Contents", "heading_level": null, "page_id": 3, "polygon": [[133.5, 24.75], [204.99609375, 24.75], [204.99609375, 35.25], [133.5, 35.25]]}, {"title": "Part II Optimal transport and Riemannian geometry 367", "heading_level": null, "page_id": 3, "polygon": [[133.5, 131.25], [471.75, 131.25], [471.75, 144.75], [133.5, 144.75]]}, {"title": "Part III Synthetic treatment of Ricci curvature 747\n26 Analytic and synthetic points of view . . . . . . . . . . . . . . . . 751\n27 Convergence of metric-measure spaces . . . . . . . . . . . . . . 759\n28 Stability of optimal transport . . . . . . . . . . . . . . . . . . .", "heading_level": null, "page_id": 3, "polygon": [[132.75, 465.0], [472.1484375, 465.0], [472.1484375, 553.78125], [132.75, 553.78125]]}, {"title": "2 Preface", "heading_level": null, "page_id": 7, "polygon": [[133.5, 26.25], [198.5712890625, 26.25], [198.5712890625, 35.940673828125], [133.5, 35.940673828125]]}, {"title": "4 Preface", "heading_level": null, "page_id": 9, "polygon": [[133.5, 26.25], [198.421875, 26.25], [198.421875, 36.134033203125], [133.5, 36.134033203125]]}, {"title": "Preface 5", "heading_level": null, "page_id": 10, "polygon": [[407.25, 26.25], [469.5, 26.25], [469.5, 35.6748046875], [407.25, 35.6748046875]]}, {"title": "Axioms", "heading_level": null, "page_id": 13, "polygon": [[133.5, 48.75], [176.25, 48.75], [176.25, 59.60302734375], [133.5, 59.60302734375]]}, {"title": "Sets and structures", "heading_level": null, "page_id": 13, "polygon": [[133.5, 99.580078125], [241.6025390625, 99.580078125], [241.6025390625, 110.6015625], [133.5, 110.6015625]]}, {"title": "Function spaces", "heading_level": null, "page_id": 14, "polygon": [[133.5, 385.5], [222.4775390625, 385.5], [222.4775390625, 396.38671875], [133.5, 396.38671875]]}, {"title": "Calculus", "heading_level": null, "page_id": 15, "polygon": [[133.4267578125, 48.75], [181.5380859375, 48.75], [181.5380859375, 59.50634765625], [133.4267578125, 59.50634765625]]}, {"title": "Probability measures", "heading_level": null, "page_id": 15, "polygon": [[133.5, 477.75], [251.25, 477.75], [251.25, 489.19921875], [133.5, 489.19921875]]}, {"title": "Notation specific to optimal transport and related fields", "heading_level": null, "page_id": 17, "polygon": [[133.5, 48.6298828125], [442.5, 48.6298828125], [442.5, 59.361328125], [133.5, 59.361328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 7], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "surya", "block_counts": [["Span", 2], ["Text", 1], ["Line", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["TableCell", 54], ["Line", 15], ["SectionHeader", 1], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4418, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["TableCell", 49], ["Line", 21], ["SectionHeader", 3], ["TableOfContents", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5052, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["TableCell", 36], ["Line", 11], ["TableOfContents", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 39], ["Text", 4], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 40], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 40], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 40], ["Text", 6], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 2], ["Text", 1], ["Line", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 40], ["TextInlineMath", 6], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 39], ["TextInlineMath", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 40], ["TextInlineMath", 6], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 669], ["Line", 40], ["TextInlineMath", 7], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 40], ["TextInlineMath", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 11], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-1"}