Note that in (4.57) we have simply rewritten the posterior probabilities in an equivalent form, and so the appearance of the logistic sigmoid may seem rather vacuous. However, it will have significance provided  $a(\mathbf{x})$  takes a simple functional form. We shall shortly consider situations in which  $a(\mathbf{x})$  is a linear function of **x**, in which case the posterior probability is governed by a generalized linear model.

For the case of  $K > 2$  classes, we have

$$
p(C_k|\mathbf{x}) = \frac{p(\mathbf{x}|C_k)p(C_k)}{\sum_j p(\mathbf{x}|C_j)p(C_j)}
$$
  
= 
$$
\frac{\exp(a_k)}{\sum_j \exp(a_j)}
$$
 (4.62)

which is known as the *normalized exponential* and can be regarded as a multiclass generalization of the logistic sigmoid. Here the quantities  $a_k$  are defined by

$$
a_k = \ln p(\mathbf{x}|\mathcal{C}_k)p(\mathcal{C}_k). \tag{4.63}
$$

The normalized exponential is also known as the *softmax function*, as it represents a smoothed version of the 'max' function because, if  $a_k \gg a_j$  for all  $j \neq k$ , then  $p(\mathcal{C}_k|\mathbf{x}) \simeq 1$ , and  $p(\mathcal{C}_i|\mathbf{x}) \simeq 0$ .

We now investigate the consequences of choosing specific forms for the classconditional densities, looking first at continuous input variables **x** and then discussing briefly the case of discrete inputs.

## **4.2.1 Continuous inputs**

Let us assume that the class-conditional densities are Gaussian and then explore the resulting form for the posterior probabilities. To start with, we shall assume that all classes share the same covariance matrix. Thus the density for class  $\mathcal{C}_k$  is given by

$$
p(\mathbf{x}|\mathcal{C}_k) = \frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \exp\left\{-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu}_k)^{\mathrm{T}} \mathbf{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu}_k)\right\}.
$$
 (4.64)

Consider first the case of two classes. From (4.57) and (4.58), we have

$$
p(C_1|\mathbf{x}) = \sigma(\mathbf{w}^{\mathrm{T}}\mathbf{x} + w_0)
$$
\n(4.65)

where we have defined

$$
\mathbf{w} = \Sigma^{-1}(\boldsymbol{\mu}_1 - \boldsymbol{\mu}_2) \tag{4.66}
$$

$$
w_0 = -\frac{1}{2}\mu_1^{\rm T}\Sigma^{-1}\mu_1 + \frac{1}{2}\mu_2^{\rm T}\Sigma^{-1}\mu_2 + \ln\frac{p(\mathcal{C}_1)}{p(\mathcal{C}_2)}.
$$
 (4.67)

We see that the quadratic terms in **x** from the exponents of the Gaussian densities have cancelled (due to the assumption of common covariance matrices) leading to a linear function of **x** in the argument of the logistic sigmoid. This result is illustrated for the case of a two-dimensional input space **x** in Figure 4.10. The resulting

Image /page/1/Figure/1 description: Two 3D surface plots are shown side-by-side. The plot on the left shows a surface with two peaks, colored red and blue, with the z-axis ranging from 0 to 0.4. The x and y axes range from -1 to 1. The plot on the right shows a sigmoid-like surface, transitioning from red at the top to blue at the bottom, with the z-axis ranging from 0 to 1. The x and y axes also range from -1 to 1.

**Figure 4.10** The left-hand plot shows the class-conditional densities for two classes, denoted red and blue. On the right is the corresponding posterior probability  $p(C_1|\mathbf{x})$ , which is given by a logistic sigmoid of a linear function of x. The surface in the right-hand plot is coloured using a proportion of red ink given by  $p(C_1|\mathbf{x})$  and a proportion of blue ink given by  $p(\mathcal{C}_2|\mathbf{x})=1 - p(\mathcal{C}_1|\mathbf{x})$ .

decision boundaries correspond to surfaces along which the posterior probabilities  $p(\mathcal{C}_k|\mathbf{x})$  are constant and so will be given by linear functions of **x**, and therefore the decision boundaries are linear in input space. The prior probabilities  $p(\mathcal{C}_k)$  enter only through the bias parameter  $w_0$  so that changes in the priors have the effect of making parallel shifts of the decision boundary and more generally of the parallel contours of constant posterior probability.

For the general case of K classes we have, from  $(4.62)$  and  $(4.63)$ ,

$$
a_k(\mathbf{x}) = \mathbf{w}_k^{\mathrm{T}} \mathbf{x} + w_{k0}
$$
 (4.68)

where we have defined

$$
\mathbf{w}_k = \Sigma^{-1} \boldsymbol{\mu}_k \tag{4.69}
$$

$$
w_{k0} = -\frac{1}{2}\mu_k^{\rm T} \Sigma^{-1} \mu_k + \ln p(\mathcal{C}_k).
$$
 (4.70)

We see that the  $a_k(\mathbf{x})$  are again linear functions of **x** as a consequence of the cancellation of the quadratic terms due to the shared covariances. The resulting decision boundaries, corresponding to the minimum misclassification rate, will occur when two of the posterior probabilities (the two largest) are equal, and so will be defined by linear functions of **x**, and so again we have a generalized linear model.

If we relax the assumption of a shared covariance matrix and allow each classconditional density  $p(\mathbf{x}|\mathcal{C}_k)$  to have its own covariance matrix  $\Sigma_k$ , then the earlier cancellations will no longer occur, and we will obtain quadratic functions of **x**, giving rise to a *quadratic discriminant*. The linear and quadratic decision boundaries are illustrated in Figure 4.11.

Image /page/2/Figure/1 description: The image displays two plots side-by-side. The left plot is a contour plot with three sets of concentric ellipses. The top set of ellipses is green, the bottom set is red, and the right set is blue. The x-axis ranges from -2.5 to 2.5, and the y-axis ranges from -2.5 to 2.5. The right plot is a color map with a white boundary line dividing it into regions. The color map transitions from green at the top left, to red at the bottom left, and to blue on the right. The x-axis ranges from -2.5 to 2.5, and the y-axis ranges from -2.5 to 2.5.

**Figure 4.11** The left-hand plot shows the class-conditional densities for three classes each having a Gaussian distribution, coloured red, green, and blue, in which the red and green classes have the same covariance matrix. The right-hand plot shows the corresponding posterior probabilities, in which the RGB colour vector represents the posterior probabilities for the respective three classes. The decision boundaries are also shown. Notice that the boundary between the red and green classes, which have the same covariance matrix, is linear, whereas those between the other pairs of classes are quadratic.

## **4.2.2 Maximum likelihood solution**

Once we have specified a parametric functional form for the class-conditional densities  $p(\mathbf{x}|\mathcal{C}_k)$ , we can then determine the values of the parameters, together with the prior class probabilities  $p(\mathcal{C}_k)$ , using maximum likelihood. This requires a data set comprising observations of **x** along with their corresponding class labels.

Consider first the case of two classes, each having a Gaussian class-conditional density with a shared covariance matrix, and suppose we have a data set  $\{x_n, t_n\}$ where  $n = 1, ..., N$ . Here  $t_n = 1$  denotes class  $C_1$  and  $t_n = 0$  denotes class  $C_2$ . We denote the prior class probability  $p(C_1) = \pi$ , so that  $p(C_2)=1 - \pi$ . For a data point  $x_n$  from class  $C_1$ , we have  $t_n = 1$  and hence

$$
p(\mathbf{x}_n, C_1) = p(C_1)p(\mathbf{x}_n|C_1) = \pi \mathcal{N}(\mathbf{x}_n|\boldsymbol{\mu}_1, \boldsymbol{\Sigma}).
$$

Similarly for class  $C_2$ , we have  $t_n = 0$  and hence

$$
p(\mathbf{x}_n, C_2) = p(C_2)p(\mathbf{x}_n|C_2) = (1-\pi)\mathcal{N}(\mathbf{x}_n|\boldsymbol{\mu}_2, \boldsymbol{\Sigma}).
$$

Thus the likelihood function is given by

$$
p(\mathbf{t}|\pi, \boldsymbol{\mu}_1, \boldsymbol{\mu}_2, \boldsymbol{\Sigma}) = \prod_{n=1}^N \left[ \pi \mathcal{N}(\mathbf{x}_n|\boldsymbol{\mu}_1, \boldsymbol{\Sigma}) \right]^{t_n} \left[ (1-\pi) \mathcal{N}(\mathbf{x}_n|\boldsymbol{\mu}_2, \boldsymbol{\Sigma}) \right]^{1-t_n} \quad (4.71)
$$

where  $\mathbf{t} = (t_1, \dots, t_N)^T$ . As usual, it is convenient to maximize the log of the likelihood function. Consider first the maximization with respect to  $\pi$ . The terms in

# **4.2. Probabilistic Generative Models 201**

the log likelihood function that depend on  $\pi$  are

$$
\sum_{n=1}^{N} \left\{ t_n \ln \pi + (1 - t_n) \ln(1 - \pi) \right\}.
$$
 (4.72)

Setting the derivative with respect to  $\pi$  equal to zero and rearranging, we obtain

$$
\pi = \frac{1}{N} \sum_{n=1}^{N} t_n = \frac{N_1}{N} = \frac{N_1}{N_1 + N_2} \tag{4.73}
$$

where  $N_1$  denotes the total number of data points in class  $C_1$ , and  $N_2$  denotes the total number of data points in class  $C_2$ . Thus the maximum likelihood estimate for  $\pi$  is simply the fraction of points in class  $C_1$  as expected. This result is easily generalized to the multiclass case where again the maximum likelihood estimate of the prior probability associated with class  $\mathcal{C}_k$  is given by the fraction of the training set points *Exercise* 4.9 assigned to that class.

> Now consider the maximization with respect to  $\mu_1$ . Again we can pick out of the log likelihood function those terms that depend on  $\mu_1$  giving

$$
\sum_{n=1}^{N} t_n \ln \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_1, \boldsymbol{\Sigma}) = -\frac{1}{2} \sum_{n=1}^{N} t_n (\mathbf{x}_n - \boldsymbol{\mu}_1)^{\mathrm{T}} \boldsymbol{\Sigma}^{-1} (\mathbf{x}_n - \boldsymbol{\mu}_1) + \text{const.}
$$
 (4.74)

Setting the derivative with respect to  $\mu_1$  to zero and rearranging, we obtain

$$
\mu_1 = \frac{1}{N_1} \sum_{n=1}^{N} t_n \mathbf{x}_n
$$
\n(4.75)

which is simply the mean of all the input vectors  $x_n$  assigned to class  $C_1$ . By a similar argument, the corresponding result for  $\mu_2$  is given by

$$
\mu_2 = \frac{1}{N_2} \sum_{n=1}^{N} (1 - t_n) \mathbf{x}_n
$$
\n(4.76)

which again is the mean of all the input vectors  $x_n$  assigned to class  $C_2$ .

Finally, consider the maximum likelihood solution for the shared covariance matrix **Σ**. Picking out the terms in the log likelihood function that depend on **Σ**, we have

$$
-\frac{1}{2}\sum_{n=1}^{N} t_n \ln |\mathbf{\Sigma}| - \frac{1}{2}\sum_{n=1}^{N} t_n (\mathbf{x}_n - \boldsymbol{\mu}_1)^{\mathrm{T}} \mathbf{\Sigma}^{-1} (\mathbf{x}_n - \boldsymbol{\mu}_1)
$$
  
$$
-\frac{1}{2}\sum_{n=1}^{N} (1 - t_n) \ln |\mathbf{\Sigma}| - \frac{1}{2}\sum_{n=1}^{N} (1 - t_n) (\mathbf{x}_n - \boldsymbol{\mu}_2)^{\mathrm{T}} \mathbf{\Sigma}^{-1} (\mathbf{x}_n - \boldsymbol{\mu}_2)
$$
  
$$
= -\frac{N}{2} \ln |\mathbf{\Sigma}| - \frac{N}{2} \mathrm{Tr} {\mathbf{\Sigma}^{-1} \mathbf{S}}
$$
 (4.77)

*Exercise 4.9*

where we have defined

$$
\mathbf{S} = \frac{N_1}{N} \mathbf{S}_1 + \frac{N_2}{N} \mathbf{S}_2 \tag{4.78}
$$

$$
\mathbf{S}_1 = \frac{1}{N_1} \sum_{n \in C_1} (\mathbf{x}_n - \boldsymbol{\mu}_1) (\mathbf{x}_n - \boldsymbol{\mu}_1)^{\mathrm{T}}
$$
(4.79)

$$
\mathbf{S}_2 = \frac{1}{N_2} \sum_{n \in C_2} (\mathbf{x}_n - \boldsymbol{\mu}_2) (\mathbf{x}_n - \boldsymbol{\mu}_2)^{\mathrm{T}}.
$$
 (4.80)

Using the standard result for the maximum likelihood solution for a Gaussian distribution, we see that  $\Sigma = S$ , which represents a weighted average of the covariance matrices associated with each of the two classes separately.

This result is easily extended to the  $K$  class problem to obtain the corresponding maximum likelihood solutions for the parameters in which each class-conditional *Exercise 4.10* density is Gaussian with a shared covariance matrix. Note that the approach of fitting Gaussian distributions to the classes is not robust to outliers, because the maximum *Section 2.3.7* likelihood estimation of a Gaussian is not robust.

## **4.2.3 Discrete features**

Let us now consider the case of discrete feature values  $x_i$ . For simplicity, we begin by looking at binary feature values  $x_i \in \{0, 1\}$  and discuss the extension to more general discrete features shortly. If there are  $D$  inputs, then a general distribution would correspond to a table of  $2^D$  numbers for each class, containing  $2^D - 1$ independent variables (due to the summation constraint). Because this grows exponentially with the number of features, we might seek a more restricted representa-*Section 8.2.2* tion. Here we will make the *naive Bayes* assumption in which the feature values are treated as independent, conditioned on the class  $\mathcal{C}_k$ . Thus we have class-conditional distributions of the form

$$
p(\mathbf{x}|\mathcal{C}_k) = \prod_{i=1}^{D} \mu_{ki}^{x_i} (1 - \mu_{ki})^{1 - x_i}
$$
 (4.81)

which contain D independent parameters for each class. Substituting into  $(4.63)$  then gives  $\sim$ 

$$
a_k(\mathbf{x}) = \sum_{i=1}^{D} \{x_i \ln \mu_{ki} + (1 - x_i) \ln(1 - \mu_{ki})\} + \ln p(\mathcal{C}_k)
$$
(4.82)

which again are linear functions of the input values  $x_i$ . For the case of  $K = 2$  classes, we can alternatively consider the logistic sigmoid formulation given by (4.57). Analogous results are obtained for discrete variables each of which can take  $M > 2$ 

## **4.2.4 Exponential family**

As we have seen, for both Gaussian distributed and discrete inputs, the posterior class probabilities are given by generalized linear models with logistic sigmoid ( $K =$ 

# *Section 8.2.2*

*Exercise 4.11* states.

2 classes) or softmax ( $K \ge 2$  classes) activation functions. These are particular cases of a more general result obtained by assuming that the class-conditional densities  $p(\mathbf{x}|\mathcal{C}_k)$  are members of the exponential family of distributions.

Using the form (2.194) for members of the exponential family, we see that the distribution of **x** can be written in the form

$$
p(\mathbf{x}|\boldsymbol{\lambda}_k) = h(\mathbf{x})g(\boldsymbol{\lambda}_k)\exp\left\{\boldsymbol{\lambda}_k^{\mathrm{T}}\mathbf{u}(\mathbf{x})\right\}.
$$
 (4.83)

We now restrict attention to the subclass of such distributions for which  $u(x) = x$ . Then we make use of  $(2.236)$  to introduce a scaling parameter s, so that we obtain the restricted set of exponential family class-conditional densities of the form

$$
p(\mathbf{x}|\boldsymbol{\lambda}_k, s) = \frac{1}{s} h\left(\frac{1}{s}\mathbf{x}\right) g(\boldsymbol{\lambda}_k) \exp\left\{\frac{1}{s}\boldsymbol{\lambda}_k^{\mathrm{T}}\mathbf{x}\right\}.
$$
 (4.84)

Note that we are allowing each class to have its own parameter vector  $\lambda_k$  but we are assuming that the classes share the same scale parameter s.

For the two-class problem, we substitute this expression for the class-conditional densities into (4.58) and we see that the posterior class probability is again given by a logistic sigmoid acting on a linear function  $a(\mathbf{x})$  which is given by

$$
a(\mathbf{x}) = (\boldsymbol{\lambda}_1 - \boldsymbol{\lambda}_2)^{\mathrm{T}} \mathbf{x} + \ln g(\boldsymbol{\lambda}_1) - \ln g(\boldsymbol{\lambda}_2) + \ln p(\mathcal{C}_1) - \ln p(\mathcal{C}_2).
$$
 (4.85)

Similarly, for the K-class problem, we substitute the class-conditional density expression into (4.63) to give

$$
a_k(\mathbf{x}) = \boldsymbol{\lambda}_k^{\mathrm{T}} \mathbf{x} + \ln g(\boldsymbol{\lambda}_k) + \ln p(\mathcal{C}_k)
$$
 (4.86)

and so again is a linear function of **x**.

# **4.3. Probabilistic Discriminative Models**

For the two-class classification problem, we have seen that the posterior probability of class  $C_1$  can be written as a logistic sigmoid acting on a linear function of **x**, for a wide choice of class-conditional distributions  $p(\mathbf{x}|\mathcal{C}_k)$ . Similarly, for the multiclass case, the posterior probability of class  $\mathcal{C}_k$  is given by a softmax transformation of a linear function of **x**. For specific choices of the class-conditional densities  $p(\mathbf{x}|\mathcal{C}_k)$ , we have used maximum likelihood to determine the parameters of the densities as well as the class priors  $p(\mathcal{C}_k)$  and then used Bayes' theorem to find the posterior class probabilities.

However, an alternative approach is to use the functional form of the generalized linear model explicitly and to determine its parameters directly by using maximum likelihood. We shall see that there is an efficient algorithm finding such solutions known as *iterative reweighted least squares*, or *IRLS*.

The indirect approach to finding the parameters of a generalized linear model, by fitting class-conditional densities and class priors separately and then applying

Image /page/6/Figure/1 description: The image displays two scatter plots side-by-side. The left plot shows data points in a 2D space with axes labeled x1 and x2. There are two clusters of points: red points are clustered around the origin (0,0), and blue points are clustered around (-1,-1) and (0.5, 1). Two green circles and a black circle are overlaid on this plot, suggesting a classification boundary or feature mapping. The right plot shows data points in a 2D space with axes labeled phi1 and phi2. The red points from the left plot are mapped to the upper region of this plot, forming a curved distribution. The blue points from the left plot are mapped to the lower region, with one cluster appearing as a vertical line of points around phi1=0 and phi2 values from 0 to 0.5, and another cluster appearing as a curved distribution around phi1=0.8 and phi2 values from 0.5 to 1. A black line, representing a decision boundary, is drawn diagonally across the right plot, separating the red and blue clusters.

**Figure 4.12** Illustration of the role of nonlinear basis functions in linear classification models. The left plot shows the original input space  $(x_1, x_2)$  together with data points from two classes labelled red and blue. Two 'Gaussian' basis functions  $\phi_1(x)$  and  $\phi_2(x)$  are defined in this space with centres shown by the green crosses and with contours shown by the green circles. The right-hand plot shows the corresponding feature space  $(\phi_1, \phi_2)$  together with the linear decision boundary obtained given by a logistic regression model of the form discussed in Section 4.3.2. This corresponds to a nonlinear decision boundary in the original input space, shown by the black curve in the left-hand plot.

Bayes' theorem, represents an example of *generative* modelling, because we could take such a model and generate synthetic data by drawing values of **x** from the marginal distribution  $p(x)$ . In the direct approach, we are maximizing a likelihood function defined through the conditional distribution  $p(\mathcal{C}_k|\mathbf{x})$ , which represents a form of *discriminative* training. One advantage of the discriminative approach is that there will typically be fewer adaptive parameters to be determined, as we shall see shortly. It may also lead to improved predictive performance, particularly when the class-conditional density assumptions give a poor approximation to the true distributions.

## **4.3.1 Fixed basis functions**

So far in this chapter, we have considered classification models that work directly with the original input vector **x**. However, all of the algorithms are equally applicable if we first make a fixed nonlinear transformation of the inputs using a vector of basis functions  $\phi(x)$ . The resulting decision boundaries will be linear in the feature space  $\phi$ , and these correspond to nonlinear decision boundaries in the original **x** space, as illustrated in Figure 4.12. Classes that are linearly separable in the feature space  $\phi(\mathbf{x})$  need not be linearly separable in the original observation space **x**. Note that as in our discussion of linear models for regression, one of the basis functions is typically set to a constant, say  $\phi_0(\mathbf{x})=1$ , so that the corresponding parameter  $w_0$  plays the role of a bias. For the remainder of this chapter, we shall include a fixed basis function transformation  $\phi(\mathbf{x})$ , as this will highlight some useful similarities to the regression models discussed in Chapter 3.

For many problems of practical interest, there is significant overlap between the class-conditional densities  $p(\mathbf{x}|\mathcal{C}_k)$ . This corresponds to posterior probabilities  $p(\mathcal{C}_k|\mathbf{x})$ , which, for at least some values of **x**, are not 0 or 1. In such cases, the optimal solution is obtained by modelling the posterior probabilities accurately and then applying standard decision theory, as discussed in Chapter 1. Note that nonlinear transformations  $\phi(\mathbf{x})$  cannot remove such class overlap. Indeed, they can increase the level of overlap, or create overlap where none existed in the original observation space. However, suitable choices of nonlinearity can make the process of modelling the posterior probabilities easier.

*Section 3.6* Such fixed basis function models have important limitations, and these will be resolved in later chapters by allowing the basis functions themselves to adapt to the data. Notwithstanding these limitations, models with fixed nonlinear basis functions play an important role in applications, and a discussion of such models will introduce many of the key concepts needed for an understanding of their more complex counterparts.

## **4.3.2 Logistic regression**

We begin our treatment of generalized linear models by considering the problem of two-class classification. In our discussion of generative approaches in Section 4.2, we saw that under rather general assumptions, the posterior probability of class  $C_1$ can be written as a logistic sigmoid acting on a linear function of the feature vector *φ* so that

$$
p(C_1|\phi) = y(\phi) = \sigma\left(\mathbf{w}^{\mathrm{T}}\phi\right)
$$
 (4.87)

with  $p(\mathcal{C}_2|\phi)=1 - p(\mathcal{C}_1|\phi)$ . Here  $\sigma(\cdot)$  is the *logistic sigmoid* function defined by (4.59). In the terminology of statistics, this model is known as *logistic regression*, although it should be emphasized that this is a model for classification rather than regression.

For an M-dimensional feature space  $\phi$ , this model has M adjustable parameters. By contrast, if we had fitted Gaussian class conditional densities using maximum likelihood, we would have used 2M parameters for the means and  $M(M + 1)/2$ parameters for the (shared) covariance matrix. Together with the class prior  $p(C_1)$ , this gives a total of  $M(M+5)/2+1$  parameters, which grows quadratically with M, in contrast to the linear dependence on  $M$  of the number of parameters in logistic regression. For large values of  $M$ , there is a clear advantage in working with the logistic regression model directly.

We now use maximum likelihood to determine the parameters of the logistic regression model. To do this, we shall make use of the derivative of the logistic sigmoid function, which can conveniently be expressed in terms of the sigmoid function

$$
\frac{d\sigma}{da} = \sigma(1 - \sigma). \tag{4.88}
$$

*Section 3.6*

*Exercise 4.12* itself

For a data set  $\{\phi_n, t_n\}$ , where  $t_n \in \{0, 1\}$  and  $\phi_n = \phi(\mathbf{x}_n)$ , with  $n =$  $1, \ldots, N$ , the likelihood function can be written

$$
p(\mathbf{t}|\mathbf{w}) = \prod_{n=1}^{N} y_n^{t_n} \left\{ 1 - y_n \right\}^{1 - t_n}
$$
 (4.89)

where  $\mathbf{t} = (t_1, \ldots, t_N)^T$  and  $y_n = p(C_1 | \phi_n)$ . As usual, we can define an error function by taking the negative logarithm of the likelihood, which gives the *crossentropy* error function in the form

$$
E(\mathbf{w}) = -\ln p(\mathbf{t}|\mathbf{w}) = -\sum_{n=1}^{N} \{t_n \ln y_n + (1 - t_n) \ln(1 - y_n)\}
$$
(4.90)

where  $y_n = \sigma(a_n)$  and  $a_n = \mathbf{w}^T \boldsymbol{\phi}_n$ . Taking the gradient of the error function with *Exercise* 4.13 respect to **w**, we obtain

$$
\nabla E(\mathbf{w}) = \sum_{n=1}^{N} (y_n - t_n) \phi_n
$$
\n(4.91)

where we have made use of (4.88). We see that the factor involving the derivative of the logistic sigmoid has cancelled, leading to a simplified form for the gradient of the log likelihood. In particular, the contribution to the gradient from data point n is given by the 'error'  $y_n - t_n$  between the target value and the prediction of the model, times the basis function vector  $\phi_n$ . Furthermore, comparison with (3.13) shows that this takes precisely the same form as the gradient of the sum-of-squares *Section 3.1.1* error function for the linear regression model.

> If desired, we could make use of the result (4.91) to give a sequential algorithm in which patterns are presented one at a time, in which each of the weight vectors is updated using (3.22) in which  $\nabla E_n$  is the  $n^{\text{th}}$  term in (4.91).

It is worth noting that maximum likelihood can exhibit severe over-fitting for data sets that are linearly separable. This arises because the maximum likelihood solution occurs when the hyperplane corresponding to  $\sigma = 0.5$ , equivalent to  $\mathbf{w}^T \boldsymbol{\phi} =$ <sup>0</sup>, separates the two classes and the magnitude of **w** goes to infinity. In this case, the logistic sigmoid function becomes infinitely steep in feature space, corresponding to a Heaviside step function, so that every training point from each class  $k$  is assigned *Exercise 4.14* a posterior probability  $p(C_k|\mathbf{x})=1$ . Furthermore, there is typically a continuum of such solutions because any separating hyperplane will give rise to the same posterior probabilities at the training data points, as will be seen later in Figure 10.13. Maximum likelihood provides no way to favour one such solution over another, and which solution is found in practice will depend on the choice of optimization algorithm and on the parameter initialization. Note that the problem will arise even if the number of data points is large compared with the number of parameters in the model, so long as the training data set is linearly separable. The singularity can be avoided by inclusion of a prior and finding a MAP solution for **w**, or equivalently by adding a regularization term to the error function.

The following are the results of the experiment:

| Labels        | Values     |
|---------------|------------|
| Experiment ID | 12345      |
| Date          | 2023-10-27 |
| Result        | Success    |

Further analysis is required to understand the implications of these findings.

## **4.3.3 Iterative reweighted least squares**

In the case of the linear regression models discussed in Chapter 3, the maximum likelihood solution, on the assumption of a Gaussian noise model, leads to a closed-form solution. This was a consequence of the quadratic dependence of the log likelihood function on the parameter vector **w**. For logistic regression, there is no longer a closed-form solution, due to the nonlinearity of the logistic sigmoid function. However, the departure from a quadratic form is not substantial. To be precise, the error function is concave, as we shall see shortly, and hence has a unique minimum. Furthermore, the error function can be minimized by an efficient iterative technique based on the *Newton-Raphson* iterative optimization scheme, which uses a local quadratic approximation to the log likelihood function. The Newton-Raphson update, for minimizing a function  $E(w)$ , takes the form (Fletcher, 1987; Bishop and Nabney, 2008)

$$
\mathbf{w}^{\text{(new)}} = \mathbf{w}^{\text{(old)}} - \mathbf{H}^{-1} \nabla E(\mathbf{w}).\tag{4.92}
$$

where **H** is the Hessian matrix whose elements comprise the second derivatives of  $E(\mathbf{w})$  with respect to the components of **w**.

Let us first of all apply the Newton-Raphson method to the linear regression model (3.3) with the sum-of-squares error function (3.12). The gradient and Hessian of this error function are given by

$$
\nabla E(\mathbf{w}) = \sum_{n=1}^{N} (\mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n - t_n) \boldsymbol{\phi}_n = \boldsymbol{\Phi}^{\mathrm{T}} \boldsymbol{\Phi} \mathbf{w} - \boldsymbol{\Phi}^{\mathrm{T}} \mathbf{t}
$$
(4.93)

$$
\mathbf{H} = \nabla \nabla E(\mathbf{w}) = \sum_{n=1}^{N} \boldsymbol{\phi}_n \boldsymbol{\phi}_n^{\mathrm{T}} = \boldsymbol{\Phi}^{\mathrm{T}} \boldsymbol{\Phi}
$$
(4.94)

Section 3.1.1 where  $\Phi$  is the  $N \times M$  design matrix, whose  $n^{\text{th}}$  row is given by  $\phi_n^{\text{T}}$ . The Newton-<br>Raphson undate then takes the form Raphson update then takes the form

$$
\mathbf{w}^{\text{(new)}} = \mathbf{w}^{\text{(old)}} - (\mathbf{\Phi}^{\text{T}} \mathbf{\Phi})^{-1} \left\{ \mathbf{\Phi}^{\text{T}} \mathbf{\Phi} \mathbf{w}^{\text{(old)}} - \mathbf{\Phi}^{\text{T}} \mathbf{t} \right\}
$$
  
= 
$$
(\mathbf{\Phi}^{\text{T}} \mathbf{\Phi})^{-1} \mathbf{\Phi}^{\text{T}} \mathbf{t}
$$
(4.95)

which we recognize as the standard least-squares solution. Note that the error function in this case is quadratic and hence the Newton-Raphson formula gives the exact solution in one step.

Now let us apply the Newton-Raphson update to the cross-entropy error function (4.90) for the logistic regression model. From (4.91) we see that the gradient and Hessian of this error function are given by

$$
\nabla E(\mathbf{w}) = \sum_{n=1}^{N} (y_n - t_n) \phi_n = \mathbf{\Phi}^{T}(\mathbf{y} - \mathbf{t})
$$
(4.96)

$$
\mathbf{H} = \nabla \nabla E(\mathbf{w}) = \sum_{n=1}^{N} y_n (1 - y_n) \phi_n \phi_n^{\mathrm{T}} = {\mathbf{\Phi}}^{\mathrm{T}} \mathbf{R} {\mathbf{\Phi}} \qquad (4.97)
$$

where we have made use of (4.88). Also, we have introduced the  $N \times N$  diagonal matrix **R** with elements

$$
R_{nn} = y_n (1 - y_n).
$$
 (4.98)

We see that the Hessian is no longer constant but depends on **w** through the weighting matrix **R**, corresponding to the fact that the error function is no longer quadratic. Using the property  $0 < y_n < 1$ , which follows from the form of the logistic sigmoid function, we see that  $\mathbf{u}^T \mathbf{H} \mathbf{u} > 0$  for an arbitrary vector **u**, and so the Hessian matrix **H** is positive definite. It follows that the error function is a concave function of **w** *Exercise* 4.15 and hence has a unique minimum.

> The Newton-Raphson update formula for the logistic regression model then becomes

$$
\mathbf{w}^{\text{(new)}} = \mathbf{w}^{\text{(old)}} - (\mathbf{\Phi}^{\text{T}} \mathbf{R} \mathbf{\Phi})^{-1} \mathbf{\Phi}^{\text{T}} (\mathbf{y} - \mathbf{t})
$$
  
\n
$$
= (\mathbf{\Phi}^{\text{T}} \mathbf{R} \mathbf{\Phi})^{-1} {\mathbf{\Phi}^{\text{T}} \mathbf{R} \mathbf{\Phi} \mathbf{w}^{\text{(old)}} - \mathbf{\Phi}^{\text{T}} (\mathbf{y} - \mathbf{t}) }
$$
  
\n
$$
= (\mathbf{\Phi}^{\text{T}} \mathbf{R} \mathbf{\Phi})^{-1} \mathbf{\Phi}^{\text{T}} \mathbf{R} \mathbf{z}
$$
(4.99)

where **z** is an N-dimensional vector with elements

$$
\mathbf{z} = \Phi \mathbf{w}^{(\text{old})} - \mathbf{R}^{-1}(\mathbf{y} - \mathbf{t}). \tag{4.100}
$$

We see that the update formula  $(4.99)$  takes the form of a set of normal equations for a weighted least-squares problem. Because the weighing matrix **R** is not constant but depends on the parameter vector **w**, we must apply the normal equations iteratively, each time using the new weight vector **w** to compute a revised weighing matrix **R**. For this reason, the algorithm is known as *iterative reweighted least squares*, or *IRLS* (Rubin, 1983). As in the weighted least-squares problem, the elements of the diagonal weighting matrix **R** can be interpreted as variances because the mean and variance of  $t$  in the logistic regression model are given by

$$
\mathbb{E}[t] = \sigma(\mathbf{x}) = y \tag{4.101}
$$
\n
$$
\mathbb{E}[t] = \pi(t)^2 - \pi(t)^2 \tag{4.102}
$$

var[t] = 
$$
\mathbb{E}[t^2] - \mathbb{E}[t]^2 = \sigma(\mathbf{x}) - \sigma(\mathbf{x})^2 = y(1 - y)
$$
 (4.102)

where we have used the property  $t^2 = t$  for  $t \in \{0, 1\}$ . In fact, we can interpret IRLS as the solution to a linearized problem in the space of the variable  $a = \mathbf{w}^T \phi$ . The quantity  $z_n$ , which corresponds to the  $n<sup>th</sup>$  element of **z**, can then be given a simple interpretation as an effective target value in this space obtained by making a local linear approximation to the logistic sigmoid function around the current operating point **w**(old)

$$
a_n(\mathbf{w}) \simeq a_n(\mathbf{w}^{(\text{old})}) + \frac{da_n}{dy_n}\bigg|_{\mathbf{w}^{(\text{old})}} (t_n - y_n)
$$

$$
= \phi_n^{\text{T}} \mathbf{w}^{(\text{old})} - \frac{(y_n - t_n)}{y_n(1 - y_n)} = z_n.
$$
 $(4.103)$ 

*Exercise 4.15*

## **4.3.4 Multiclass logistic regression**

*Section 4.2* In our discussion of generative models for multiclass classification, we have seen that for a large class of distributions, the posterior probabilities are given by a softmax transformation of linear functions of the feature variables, so that

$$
p(C_k|\phi) = y_k(\phi) = \frac{\exp(a_k)}{\sum_j \exp(a_j)}
$$
(4.104)

where the 'activations'  $a_k$  are given by

$$
a_k = \mathbf{w}_k^{\mathrm{T}} \boldsymbol{\phi}.
$$
 (4.105)

There we used maximum likelihood to determine separately the class-conditional densities and the class priors and then found the corresponding posterior probabilities using Bayes' theorem, thereby implicitly determining the parameters  $\{w_k\}$ . Here we consider the use of maximum likelihood to determine the parameters  $\{w_k\}$  of this model directly. To do this, we will require the derivatives of  $y_k$  with respect to all of *Exercise 4.17* the activations  $a_i$ . These are given by

$$
\frac{\partial y_k}{\partial a_j} = y_k (I_{kj} - y_j) \tag{4.106}
$$

where  $I_{ki}$  are the elements of the identity matrix.

Next we write down the likelihood function. This is most easily done using the 1-of-K coding scheme in which the target vector  $t_n$  for a feature vector  $\phi_n$ belonging to class  $\mathcal{C}_k$  is a binary vector with all elements zero except for element k, which equals one. The likelihood function is then given by

$$
p(\mathbf{T}|\mathbf{w}_1,\ldots,\mathbf{w}_K) = \prod_{n=1}^N \prod_{k=1}^K p(\mathcal{C}_k|\boldsymbol{\phi}_n)^{t_{nk}} = \prod_{n=1}^N \prod_{k=1}^K y_{nk}^{t_{nk}}
$$
(4.107)

where  $y_{nk} = y_k(\phi_n)$ , and **T** is an  $N \times K$  matrix of target variables with elements  $t_{nk}$ . Taking the negative logarithm then gives

$$
E(\mathbf{w}_1, ..., \mathbf{w}_K) = -\ln p(\mathbf{T}|\mathbf{w}_1, ..., \mathbf{w}_K) = -\sum_{n=1}^{N} \sum_{k=1}^{K} t_{nk} \ln y_{nk}
$$
 (4.108)

which is known as the *cross-entropy* error function for the multiclass classification problem.

We now take the gradient of the error function with respect to one of the parameter vectors  $w_j$ . Making use of the result (4.106) for the derivatives of the softmax *Exercise 4.18* function, we obtain

$$
\nabla_{\mathbf{w}_j} E(\mathbf{w}_1, \dots, \mathbf{w}_K) = \sum_{n=1}^N (y_{nj} - t_{nj}) \phi_n
$$
 (4.109)

*Exercise 4.17*

where we have made use of  $\sum_k t_{nk} = 1$ . Once again, we see the same form arising for the gradient as was found for the sum-of-squares error function with the linear model and the cross-entropy error for the logistic regression model, namely the product of the error  $(y_{nj} - t_{nj})$  times the basis function  $\phi_n$ . Again, we could use this to formulate a sequential algorithm in which patterns are presented one at a time, in which each of the weight vectors is updated using  $(3.22)$ .

We have seen that the derivative of the log likelihood function for a linear regression model with respect to the parameter vector **w** for a data point n took the form of the 'error'  $y_n - t_n$  times the feature vector  $\phi_n$ . Similarly, for the combination of logistic sigmoid activation function and cross-entropy error function (4.90), and for the softmax activation function with the multiclass cross-entropy error function (4.108), we again obtain this same simple form. This is an example of a more general result, as we shall see in Section 4.3.6.

To find a batch algorithm, we again appeal to the Newton-Raphson update to obtain the corresponding IRLS algorithm for the multiclass problem. This requires evaluation of the Hessian matrix that comprises blocks of size  $M \times M$  in which block  $i, k$  is given by

$$
\nabla_{\mathbf{w}_k} \nabla_{\mathbf{w}_j} E(\mathbf{w}_1, \dots, \mathbf{w}_K) = -\sum_{n=1}^N y_{nk} (I_{kj} - y_{nj}) \phi_n \phi_n^{\mathrm{T}}.
$$
 (4.110)

As with the two-class problem, the Hessian matrix for the multiclass logistic regres-*Exercise 4.20* sion model is positive definite and so the error function again has a unique minimum. Practical details of IRLS for the multiclass case can be found in Bishop and Nabney (2008).

## **4.3.5 Probit regression**

We have seen that, for a broad range of class-conditional distributions, described by the exponential family, the resulting posterior class probabilities are given by a logistic (or softmax) transformation acting on a linear function of the feature variables. However, not all choices of class-conditional density give rise to such a simple form for the posterior probabilities (for instance, if the class-conditional densities are modelled using Gaussian mixtures). This suggests that it might be worth exploring other types of discriminative probabilistic model. For the purposes of this chapter, however, we shall return to the two-class case, and again remain within the framework of generalized linear models so that

$$
p(t = 1|a) = f(a)
$$
\n(4.111)

where  $a = \mathbf{w}^T \boldsymbol{\phi}$ , and  $f(\cdot)$  is the activation function.

One way to motivate an alternative choice for the link function is to consider a noisy threshold model, as follows. For each input  $\phi_n$ , we evaluate  $a_n = \mathbf{w}^T \phi_n$  and then we set the target value according to

$$
\begin{cases} t_n = 1 & \text{if } a_n \ge \theta \\ t_n = 0 & \text{otherwise.} \end{cases}
$$
 (4.112)

# **4.3. Probabilistic Discriminative Models 211**

**Figure 4.13** Schematic example of a probability density  $p(\theta)$ shown by the blue curve, given in this example by a mixture of two Gaussians, along with its cumulative distribution function  $f(a)$ , shown by the red curve. Note that the value of the blue 0.8 curve at any point, such as that indicated by the vertical green line, corresponds to the slope of the red curve at the same point. Conversely, the value of the red curve at this point corresponds to the area under the blue curve indicated by the shaded green region. In the stochastic threshold model, the class label takes the value  $t = 1$  if the value of  $a = \mathbf{w}^T \phi$  exceeds a threshold, otherwise it takes the value  $t = 0$ . This is equivalent to an activation function given by the cumulative distribution function  $f(a)$ .

Image /page/13/Figure/2 description: The image displays a graph with an x-axis ranging from 0 to 4 and a y-axis ranging from 0 to 1. There are three elements plotted: a blue curve representing a probability density function (PDF) with two peaks, a red curve representing a cumulative distribution function (CDF), and a vertical green line at approximately x=2.7. The area under the first peak of the blue curve, from x=0 to approximately x=2.2, is shaded in light green. The blue curve starts at 0, rises to a peak of about 0.7 at x=1.4, drops to a minimum of about 0.1 at x=2.2, rises again to a peak of about 0.4 at x=3.1, and then drops back to 0. The red curve starts at 0, increases steadily, passing through approximately (1.4, 0.5) and (3.1, 0.8), and reaches 1 at around x=3.8. The green line is a vertical line at x=2.7, intersecting the red curve at approximately y=0.6.

If the value of  $\theta$  is drawn from a probability density  $p(\theta)$ , then the corresponding activation function will be given by the cumulative distribution function

$$
f(a) = \int_{-\infty}^{a} p(\theta) d\theta
$$
 (4.113)

as illustrated in Figure 4.13.

As a specific example, suppose that the density  $p(\theta)$  is given by a zero mean, unit variance Gaussian. The corresponding cumulative distribution function is given by

$$
\Phi(a) = \int_{-\infty}^{a} \mathcal{N}(\theta|0, 1) \, d\theta \tag{4.114}
$$

which is known as the *probit* function. It has a sigmoidal shape and is compared with the logistic sigmoid function in Figure 4.9. Note that the use of a more general Gaussian distribution does not change the model because this is equivalent to a re-scaling of the linear coefficients **w**. Many numerical packages provide for the evaluation of a closely related function defined by

$$
\operatorname{erf}(a) = \frac{2}{\sqrt{\pi}} \int_0^a \exp(-\theta^2/2) \, \mathrm{d}\theta \tag{4.115}
$$

and known as the *erf function* or *error function* (not to be confused with the error *Exercise 4.21* function of a machine learning model). It is related to the probit function by

$$
\Phi(a) = \frac{1}{2} \left\{ 1 + \frac{1}{\sqrt{2}} erf(a) \right\}.
$$
 (4.116)

The generalized linear model based on a probit activation function is known as *probit regression*.

We can determine the parameters of this model using maximum likelihood, by a straightforward extension of the ideas discussed earlier. In practice, the results found using probit regression tend to be similar to those of logistic regression. We shall,

*Exercise 4.21*