{"table_of_contents": [{"title": "198 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 0, "polygon": [[30.0, 40.5], [298.265625, 40.5], [298.265625, 51.5028076171875], [30.0, 51.5028076171875]]}, {"title": "4.2.1 Continuous inputs", "heading_level": null, "page_id": 0, "polygon": [[137.25, 352.5], [277.5, 352.5], [277.5, 363.568359375], [137.25, 363.568359375]]}, {"title": "200 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 2, "polygon": [[29.25, 40.5], [297.7734375, 40.5], [297.7734375, 50.974365234375], [29.25, 50.974365234375]]}, {"title": "4.2.2 Maximum likelihood solution", "heading_level": null, "page_id": 2, "polygon": [[137.07421875, 346.5], [333.0, 346.5], [333.0, 358.0400390625], [137.07421875, 358.0400390625]]}, {"title": "4.2. Probabilistic Generative Models 201", "heading_level": null, "page_id": 3, "polygon": [[258.0, 40.5], [472.5, 40.5], [472.5, 50.974365234375], [258.0, 50.974365234375]]}, {"title": "202 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 4, "polygon": [[30.0, 40.5], [297.7734375, 40.5], [297.7734375, 51.7060546875], [30.0, 51.7060546875]]}, {"title": "4.2.3 Discrete features", "heading_level": null, "page_id": 4, "polygon": [[137.25, 288.75], [268.734375, 288.75], [268.734375, 299.99267578125], [137.25, 299.99267578125]]}, {"title": "4.2.4 Exponential family", "heading_level": null, "page_id": 4, "polygon": [[137.25, 578.25], [277.5, 578.25], [277.5, 589.5791015625], [137.25, 589.5791015625]]}, {"title": "4.3. Probabilistic Discriminative Models", "heading_level": null, "page_id": 5, "polygon": [[90.0, 423.75], [339.75, 423.75], [339.75, 437.0625], [90.0, 437.0625]]}, {"title": "204 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 6, "polygon": [[29.25, 40.5], [298.265625, 40.5], [298.265625, 51.0556640625], [29.25, 51.0556640625]]}, {"title": "4.3.1 Fixed basis functions", "heading_level": null, "page_id": 6, "polygon": [[137.935546875, 497.25], [293.25, 498.0], [293.25, 509.5810546875], [137.935546875, 509.5810546875]]}, {"title": "4.3.2 Logistic regression", "heading_level": null, "page_id": 7, "polygon": [[138.0, 308.25], [282.0, 308.25], [282.0, 319.50439453125], [138.0, 319.50439453125]]}, {"title": "206 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 8, "polygon": [[30.0, 40.5], [298.7578125, 40.5], [298.7578125, 51.4215087890625], [30.0, 51.4215087890625]]}, {"title": "", "heading_level": null, "page_id": 8, "polygon": [[31.59228515625, 500.80078125], [91.30078125, 500.80078125], [91.30078125, 510.556640625], [31.59228515625, 510.556640625]]}, {"title": "4.3.3 Iterative reweighted least squares", "heading_level": null, "page_id": 9, "polygon": [[137.56640625, 71.25], [361.5, 71.25], [361.5, 82.8841552734375], [137.56640625, 82.8841552734375]]}, {"title": "208 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 10, "polygon": [[30.0, 40.5], [298.5, 40.5], [298.5, 51.5841064453125], [30.0, 51.5841064453125]]}, {"title": "4.3.4 Multiclass logistic regression", "heading_level": null, "page_id": 11, "polygon": [[138.75, 72.0], [336.75, 72.0], [336.75, 83.08740234375], [138.75, 83.08740234375]]}, {"title": "210 4. LINEAR MODELS FOR CLASSIFICATION", "heading_level": null, "page_id": 12, "polygon": [[29.25, 40.5], [298.265625, 40.5], [298.265625, 51.5841064453125], [29.25, 51.5841064453125]]}, {"title": "4.3.5 Probit regression", "heading_level": null, "page_id": 12, "polygon": [[136.828125, 381.75], [270.75, 381.75], [270.75, 392.8359375], [136.828125, 392.8359375]]}, {"title": "4.3. Probabilistic Discriminative Models 211", "heading_level": null, "page_id": 13, "polygon": [[240.802734375, 40.5], [472.5, 40.5], [472.5, 50.7711181640625], [240.802734375, 50.7711181640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 61], ["Text", 7], ["Equation", 6], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5548, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 52], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 632, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 55], ["TextInlineMath", 4], ["Equation", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 90], ["Text", 7], ["Equation", 6], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3326, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 55], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["SectionHeader", 3]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1080, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 54], ["Text", 6], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 38], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 794, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 48], ["TextInlineMath", 5], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 578, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 49], ["TextInlineMath", 7], ["Equation", 3], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 643, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 53], ["Equation", 6], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 54], ["Equation", 6], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1653, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 60], ["Text", 6], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 47], ["TextInlineMath", 4], ["Text", 4], ["Equation", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["Line", 57], ["Text", 8], ["Equation", 4], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1372, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bishop-<PERSON><PERSON>-Recognition-and-Machine-Learning-2006 (2)_218-231"}