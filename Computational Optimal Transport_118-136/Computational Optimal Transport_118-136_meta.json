{"table_of_contents": [{"title": "8", "heading_level": null, "page_id": 0, "polygon": [[303.1236230110159, 134.987548828125], [321.4658203125, 134.987548828125], [321.4658203125, 161.05810546875], [303.1236230110159, 161.05810546875]]}, {"title": "Statistical Divergences", "heading_level": null, "page_id": 0, "polygon": [[230.17431640625, 182.19241706161137], [390.00927734375, 182.19241706161137], [390.00927734375, 195.3360595703125], [230.17431640625, 195.3360595703125]]}, {"title": "8.1 ϕ-Divergences", "heading_level": null, "page_id": 1, "polygon": [[104.29253365973072, 125.21042654028436], [214.5875152998776, 125.21042654028436], [214.5875152998776, 136.2427978515625], [104.29253365973072, 136.2427978515625]]}, {"title": "8.1. \\varphi-Divergences 117", "heading_level": null, "page_id": 3, "polygon": [[104.46142578125, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.7994384765625], [104.46142578125, 103.7994384765625]]}, {"title": "8.2 Integral Probability Metrics", "heading_level": null, "page_id": 6, "polygon": [[103.5422276621787, 410.8701421800948], [285.8665850673194, 410.8701421800948], [285.8665850673194, 421.763671875], [103.5422276621787, 421.763671875]]}, {"title": "8.2.1 W_1 and Flat Norm", "heading_level": null, "page_id": 7, "polygon": [[103.5422276621787, 381.62938388625594], [239.34761321909423, 381.62938388625594], [239.34761321909423, 392.79638671875], [103.5422276621787, 392.79638671875]]}, {"title": "8.2.2 Dual RKHS Norms and Maximum Mean Discrepancies", "heading_level": null, "page_id": 8, "polygon": [[104.29253365973072, 125.21042654028436], [418.14501953125, 125.21042654028436], [418.14501953125, 136.4359130859375], [104.29253365973072, 136.4359130859375]]}, {"title": "8.2. Integral Probability Metrics 123", "heading_level": null, "page_id": 9, "polygon": [[104.9852294921875, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 104.089111328125], [104.9852294921875, 104.089111328125]]}, {"title": "8.3. Wasserstein Spaces Are Not Hilbertian 125", "heading_level": null, "page_id": 11, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.9925537109375], [105.04283965728274, 103.9925537109375]]}, {"title": "8.3 Wasserstein Spaces Are Not Hilbertian", "heading_level": null, "page_id": 11, "polygon": [[103.5422276621787, 549.5763033175356], [350.798828125, 549.5763033175356], [350.798828125, 560.806640625], [103.5422276621787, 560.806640625]]}, {"title": "8.3. Wasserstein Spaces Are Not Hilbertian 127", "heading_level": null, "page_id": 13, "polygon": [[105.04283965728274, 92.97061611374407], [516.9608323133415, 92.97061611374407], [516.9608323133415, 103.7994384765625], [105.04283965728274, 103.7994384765625]]}, {"title": "8.3.1 Embeddings and Distortion", "heading_level": null, "page_id": 13, "polygon": [[104.29253365973072, 545.8274881516587], [278.36352509179926, 545.8274881516587], [278.36352509179926, 556.171875], [104.29253365973072, 556.171875]]}, {"title": "8.3.2 Negative/Positive Definite Variants of Optimal Transport", "heading_level": null, "page_id": 14, "polygon": [[104.29253365973072, 347.8900473933649], [429.9253365973072, 347.8900473933649], [429.9253365973072, 359.1943359375], [104.29253365973072, 359.1943359375]]}, {"title": "8.4 Empirical Estimators for OT, MMD and ϕ-divergences", "heading_level": null, "page_id": 14, "polygon": [[103.5422276621787, 524.0843601895734], [436.10400390625, 524.0843601895734], [436.10400390625, 535.70166015625], [103.5422276621787, 535.70166015625]]}, {"title": "8.4.1 Empirical Estimators for OT and MMD", "heading_level": null, "page_id": 14, "polygon": [[104.29253365973072, 629.94189453125], [339.1383108935128, 629.94189453125], [339.1383108935128, 640.75634765625], [104.29253365973072, 640.75634765625]]}, {"title": "8.4. Empirical Estimators for OT, MMD and ϕ-divergences 129", "heading_level": null, "page_id": 15, "polygon": [[105.04283965728274, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.7994384765625], [105.04283965728274, 103.7994384765625]]}, {"title": "8.4.2 Empirical Estimators for ϕ-divergences", "heading_level": null, "page_id": 16, "polygon": [[103.5422276621787, 489.5952606635071], [336.88739290085675, 489.5952606635071], [336.88739290085675, 499.7822265625], [103.5422276621787, 499.7822265625]]}, {"title": "8.5. Entropic Regularization: Between OT and MMD 131", "heading_level": null, "page_id": 17, "polygon": [[104.910400390625, 92.97061611374407], [517.7111383108935, 92.97061611374407], [517.7111383108935, 103.89599609375], [104.910400390625, 103.89599609375]]}, {"title": "8.5 Entropic Regularization: Between OT and MMD", "heading_level": null, "page_id": 17, "polygon": [[103.5422276621787, 554.824644549763], [402.16401468788246, 554.824644549763], [402.16401468788246, 565.82763671875], [103.5422276621787, 565.82763671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 23], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 580], ["Line", 72], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 49], ["Text", 6], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 52], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 608], ["Line", 86], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 37], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 791, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 57], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 44], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["Line", 63], ["TextInlineMath", 5], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 41], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 702, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 683], ["Line", 68], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 67], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 39], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 43], ["TextInlineMath", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 34], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 57], ["TextInlineMath", 7], ["Equation", 3], ["SectionHeader", 1], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 94], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2927, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 46], ["Equation", 3], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 68], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_118-136"}