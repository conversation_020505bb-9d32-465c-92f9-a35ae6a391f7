**Springer Series in Statistics**

<PERSON>

# **The Elements of Statistical Learning**

Data Mining, Inference, and Prediction

Second Edition

Image /page/0/Picture/5 description: The logo for Springer, a scientific publisher, is displayed against a solid orange background. The logo features a white silhouette of a horse's head, resembling a chess knight, positioned to the left. To the right of the horse's head, the word "Springer" is written in white, stylized serif font.

This is page v Printer: Opaque this

To our parents:

<PERSON> and <PERSON> and <PERSON> and <PERSON>

and to our families:

<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> vi

| This is page vii            |  |
|-----------------------------|--|
| <b>Printer:</b> Opaque this |  |

## Preface to the Second Edition

In God we trust, all others bring data.

 $-Vi$ illiam <PERSON>  $(1900-1993)^1$ 

We have been gratified by the popularity of the first edition of The Elements of Statistical Learning. This, along with the fast pace of research in the statistical learning field, motivated us to update our book with a second edition.

We have added four new chapters and updated some of the existing chapters. Because many readers are familiar with the layout of the first edition, we have tried to change it as little as possible. Here is a summary of the main changes:

<sup>1</sup>On the Web, this quote has been widely attributed to both <PERSON>ming and <PERSON>. <PERSON>; however Professor <PERSON> told us that he can claim no credit for this quote, and ironically we could find no "data" confirming that Deming actually said this.

| Chapter                                                | What's new                                                                                                                                                                                             |
|--------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1. Introduction                                        |                                                                                                                                                                                                        |
| 2. Overview of Supervised Learning                     |                                                                                                                                                                                                        |
| 3. Linear Methods for Regression                       | LAR algorithm and generalizations of the lasso                                                                                                                                                         |
| 4. Linear Methods for Classification                   | Lasso path for logistic regression                                                                                                                                                                     |
| 5. Basis Expansions and Regulariza- tion               | Additional illustrations of RKHS                                                                                                                                                                       |
| 6. Kernel Smoothing Methods                            |                                                                                                                                                                                                        |
| 7. Model Assessment and Selection                      | Strengths and pitfalls of cross- validation                                                                                                                                                            |
| 8. Model Inference and Averaging                       |                                                                                                                                                                                                        |
| 9. Additive Models, Trees, and Related Methods         |                                                                                                                                                                                                        |
| 10. Boosting and Additive Trees                        | New example from ecology; some material split off to Chapter 16.                                                                                                                                       |
| 11. Neural Networks                                    | Bayesian neural nets and the NIPS 2003 challenge                                                                                                                                                       |
| 12. Support Vector Machines and Flexible Discriminants | Path algorithm for SVM classifier                                                                                                                                                                      |
| 13. Prototype Methods and Nearest-Neighbors            |                                                                                                                                                                                                        |
| 14. Unsupervised Learning                              | Spectral clustering, kernel PCA,<br>sparse PCA, non-negative matrix<br>factorization archetypal analysis,<br>nonlinear dimension reduction,<br>Google page rank algorithm, a<br>direct approach to ICA |
| 15. Random Forests                                     | New                                                                                                                                                                                                    |
| 16. Ensemble Learning                                  | New                                                                                                                                                                                                    |
| 17. Undirected Graphical Models                        | New                                                                                                                                                                                                    |
| 18. High-Dimensional Problems                          | New                                                                                                                                                                                                    |

Some further notes:

- Our first edition was unfriendly to colorblind readers; in particular, we tended to favor red/green contrasts which are particularly troublesome. We have changed the color palette in this edition to a large extent, replacing the above with an orange/blue contrast.
- We have changed the name of Chapter 6 from "Kernel Methods" to "Kernel Smoothing Methods", to avoid confusion with the machinelearning kernel method that is discussed in the context of support vector machines (Chapter 11) and more generally in Chapters 5 and 14.
- In the first edition, the discussion of error-rate estimation in Chapter 7 was sloppy, as we did not clearly differentiate the notions of conditional error rates (conditional on the training set) and unconditional rates. We have fixed this in the new edition.

- Chapters 15 and 16 follow naturally from Chapter 10, and the chapters are probably best read in that order.
- In Chapter 17, we have not attempted a comprehensive treatment of graphical models, and discuss only undirected models and some new methods for their estimation. Due to a lack of space, we have specifically omitted coverage of directed graphical models.
- Chapter 18 explores the " $p \gg N$ " problem, which is learning in highdimensional feature spaces. These problems arise in many areas, including genomic and proteomic studies, and document classification.

We thank the many readers who have found the (too numerous) errors in the first edition. We apologize for those and have done our best to avoid errors in this new edition. We thank Mark Segal, Bala Rajaratnam, and Larry Wasserman for comments on some of the new chapters, and many Stanford graduate and post-doctoral students who offered comments, in particular Mohammed AlQuraishi, John Boik, Holger Hoefling, Arian Maleki, Donal McMahon, Saharon Rosset, Babak Shababa, Daniela Witten, Ji Zhu and Hui Zou. We thank John Kimmel for his patience in guiding us through this new edition. RT dedicates this edition to the memory of Anna McPhee.

> Trevor Hastie Robert Tibshirani Jerome Friedman

Stanford, California August 2008

x Preface to the Second Edition

### Preface to the First Edition

We are drowning in information and starving for knowledge.

–Rutherford D. Roger

The field of Statistics is constantly challenged by the problems that science and industry brings to its door. In the early days, these problems often came from agricultural and industrial experiments and were relatively small in scope. With the advent of computers and the information age, statistical problems have exploded both in size and complexity. Challenges in the areas of data storage, organization and searching have led to the new field of "data mining"; statistical and computational problems in biology and medicine have created "bioinformatics." Vast amounts of data are being generated in many fields, and the statistician's job is to make sense of it all: to extract important patterns and trends, and understand "what the data says." We call this learning from data.

The challenges in learning from data have led to a revolution in the statistical sciences. Since computation plays such a key role, it is not surprising that much of this new development has been done by researchers in other fields such as computer science and engineering.

The learning problems that we consider can be roughly categorized as either supervised or unsupervised. In supervised learning, the goal is to predict the value of an outcome measure based on a number of input measures; in unsupervised learning, there is no outcome measure, and the goal is to describe the associations and patterns among a set of input measures.

#### xii Preface to the First Edition

This book is our attempt to bring together many of the important new ideas in learning, and explain them in a statistical framework. While some mathematical details are needed, we emphasize the methods and their conceptual underpinnings rather than their theoretical properties. As a result, we hope that this book will appeal not just to statisticians but also to researchers and practitioners in a wide variety of fields.

Just as we have learned a great deal from researchers outside of the field of statistics, our statistical viewpoint may help others to better understand different aspects of learning:

There is no true interpretation of anything; interpretation is a vehicle in the service of human comprehension. The value of interpretation is in enabling others to fruitfully think about an idea.

–Andreas Buja

We would like to acknowledge the contribution of many people to the conception and completion of this book. David Andrews, Leo Breiman, Andreas Buja, John Chambers, Bradley Efron, Geoffrey Hinton, Werner Stuetzle, and John Tukey have greatly influenced our careers. Balasubramanian Narasimhan gave us advice and help on many computational problems, and maintained an excellent computing environment. Shin-Ho Bang helped in the production of a number of the figures. Lee Wilkinson gave valuable tips on color production. Ilana Belitskaya, Eva Cantoni, Maya Gupta, Michael Jordan, Shanti Gopatam, Radford Neal, Jorge Picazo, Bogdan Popescu, Olivier Renaud, Saharon Rosset, John Storey, Ji Zhu, Mu Zhu, two reviewers and many students read parts of the manuscript and offered helpful suggestions. John Kimmel was supportive, patient and helpful at every phase; MaryAnn Brickner and Frank Ganz headed a superb production team at Springer. Trevor Hastie would like to thank the statistics department at the University of Cape Town for their hospitality during the final stages of this book. We gratefully acknowledge NSF and NIH for their support of this work. Finally, we would like to thank our families and our parents for their love and support.

> Trevor Hastie Robert Tibshirani Jerome Friedman

Stanford, California May 2001

The quiet statisticians have changed our world; not by discovering new facts or technical developments, but by changing the ways that we reason, experiment and form our opinions ....

–Ian Hacking

This is page xiii Printer: Opaque this

## **Contents**

| Preface to the Second Edition<br>vii |     |              |                                                     |    |
|--------------------------------------|-----|--------------|-----------------------------------------------------|----|
|                                      |     |              | Preface to the First Edition                        | хi |
| 1.                                   |     | Introduction |                                                     | 1  |
| $\mathbf 2$                          |     |              | Overview of Supervised Learning                     | 9  |
|                                      | 2.1 |              | Introduction                                        | 9  |
|                                      | 2.2 |              | Variable Types and Terminology                      | 9  |
|                                      | 2.3 |              | Two Simple Approaches to Prediction:                |    |
|                                      |     |              | Least Squares and Nearest Neighbors                 | 11 |
|                                      |     | 2.3.1        | Linear Models and Least Squares                     | 11 |
|                                      |     | 2.3.2        | Nearest-Neighbor Methods                            | 14 |
|                                      |     | 2.3.3        | From Least Squares to Nearest Neighbors             | 16 |
|                                      | 2.4 |              | Statistical Decision Theory                         | 18 |
|                                      | 2.5 |              | Local Methods in High Dimensions                    | 22 |
|                                      | 2.6 |              | Statistical Models, Supervised Learning             |    |
|                                      |     |              | and Function Approximation                          | 28 |
|                                      |     | 2.6.1        | A Statistical Model                                 |    |
|                                      |     |              | for the Joint Distribution $Pr(X, Y)$               | 28 |
|                                      |     | 2.6.2        | Supervised Learning                                 | 29 |
|                                      |     | 2.6.3        | Function Approximation                              | 29 |
|                                      | 2.7 |              | Structured Regression Models                        | 32 |
|                                      |     | 2.7.1        | Difficulty of the Problem $\dots \dots \dots \dots$ | 32 |

| 2.8 |       | Classes of Restricted Estimators                                                                             |    |
|-----|-------|--------------------------------------------------------------------------------------------------------------|----|
|     | 2.8.1 | Roughness Penalty and Bayesian Methods                                                                       |    |
|     | 2.8.2 | Kernel Methods and Local Regression                                                                          |    |
|     | 2.8.3 | Basis Functions and Dictionary Methods                                                                       |    |
| 2.9 |       | Model Selection and the Bias–Variance Tradeoff $\phantom{1}.\phantom{1}.\phantom{1}.\phantom{1}.\phantom{1}$ |    |
|     |       | Bibliographic Notes                                                                                          |    |
|     |       | Exercises                                                                                                    |    |
|     |       | <b>Linear Methods for Regression</b>                                                                         | 43 |
| 3.1 |       | Introduction                                                                                                 |    |
| 3.2 |       | Linear Regression Models and Least Squares                                                                   |    |
|     | 3.2.1 | Example: Prostate Cancer                                                                                     |    |
|     | 3.2.2 | The Gauss-Markov Theorem                                                                                     |    |
|     | 3.2.3 | Multiple Regression                                                                                          |    |
|     |       | from Simple Univariate Regression                                                                            |    |
|     | 3.2.4 | Multiple Outputs                                                                                             |    |
| 3.3 |       | Subset Selection                                                                                             |    |
|     | 3.3.1 | Best-Subset Selection                                                                                        |    |
|     | 3.3.2 | Forward- and Backward-Stepwise Selection                                                                     |    |
|     | 3.3.3 | Forward-Stagewise Regression                                                                                 |    |
|     | 3.3.4 | Prostate Cancer Data Example (Continued)                                                                     |    |
| 3.4 |       | Shrinkage Methods                                                                                            |    |
|     | 3.4.1 | Ridge Regression                                                                                             |    |
|     | 3.4.2 | The Lasso $\dots \dots \dots \dots \dots \dots \dots \dots$                                                  |    |
|     | 3.4.3 | Discussion: Subset Selection, Ridge Regression                                                               |    |
|     |       | and the Lasso $\dots \dots \dots \dots \dots \dots \dots$                                                    |    |
|     | 3.4.4 | Least Angle Regression                                                                                       |    |
| 3.5 |       | Methods Using Derived Input Directions                                                                       |    |
|     | 3.5.1 | Principal Components Regression                                                                              |    |
|     | 3.5.2 | Partial Least Squares                                                                                        |    |
| 3.6 |       | Discussion: A Comparison of the Selection                                                                    |    |
|     |       | and Shrinkage Methods                                                                                        |    |
| 3.7 |       | Multiple Outcome Shrinkage and Selection                                                                     |    |
| 3.8 |       | More on the Lasso and Related Path Algorithms $\ldots$ .                                                     |    |
|     | 3.8.1 | Incremental Forward Stagewise Regression                                                                     |    |
|     | 3.8.2 | Piecewise-Linear Path Algorithms                                                                             |    |
|     | 3.8.3 | The Dantzig Selector                                                                                         |    |
|     | 3.8.4 | The Grouped Lasso $\ldots \ldots \ldots \ldots \ldots$                                                       |    |
|     | 3.8.5 | Further Properties of the Lasso                                                                              |    |
|     | 3.8.6 | Pathwise Coordinate Optimization                                                                             |    |
| 3.9 |       | Computational Considerations                                                                                 |    |
|     |       | Bibliographic Notes                                                                                          |    |
|     |       | Exercises                                                                                                    |    |
|     |       |                                                                                                              |    |

| Contents | <b>XV</b> |
|----------|-----------|
|----------|-----------|

| 4       | Linear Methods for Classification<br>101                                   |  |
|---------|----------------------------------------------------------------------------|--|
| 4.1     | Introduction<br>101<br>.                                                   |  |
| 4.2     | Linear Regression of an Indicator Matrix<br>103                            |  |
| 4.3     | Linear Discriminant Analysis<br>106                                        |  |
|         | Regularized Discriminant Analysis<br>4.3.1<br>112                          |  |
|         | 4.3.2<br>Computations for $LDA$<br>113                                     |  |
|         | 4.3.3<br>Reduced-Rank Linear Discriminant Analysis<br>113                  |  |
| 4.4     | Logistic Regression<br>119                                                 |  |
|         | Fitting Logistic Regression Models<br>4.4.1<br>120                         |  |
|         | 4.4.2<br>Example: South African Heart Disease<br>122                       |  |
|         | 4.4.3<br>Quadratic Approximations and Inference<br>124                     |  |
|         | 4.4.4<br>$L_1$ Regularized Logistic Regression<br>125                      |  |
|         | 4.4.5<br>Logistic Regression or LDA? $\ldots \ldots \ldots$<br>127         |  |
| 4.5     | Separating Hyperplanes $\dots \dots \dots \dots \dots \dots$<br>129        |  |
|         | 4.5.1<br>Rosenblatt's Perceptron Learning Algorithm<br>130                 |  |
|         | 4.5.2<br>Optimal Separating Hyperplanes<br>132                             |  |
|         | Bibliographic Notes<br>135                                                 |  |
|         | Exercises<br>135                                                           |  |
|         |                                                                            |  |
| 5       | Basis Expansions and Regularization<br>139                                 |  |
| 5.1     | Introduction<br>139                                                        |  |
| $5.2\,$ | Piecewise Polynomials and Splines<br>141                                   |  |
|         | Natural Cubic Splines $\ldots \ldots \ldots \ldots \ldots$<br>5.2.1<br>144 |  |
|         | 5.2.2<br>Example: South African Heart Disease (Continued)146               |  |
|         | Example: Phoneme Recognition $\ldots \ldots \ldots$<br>5.2.3<br>148        |  |
| 5.3     | Filtering and Feature Extraction<br>150                                    |  |
| 5.4     | Smoothing Splines<br>151                                                   |  |
|         | 5.4.1<br>Degrees of Freedom and Smoother Matrices<br>153                   |  |
| $5.5\,$ | Automatic Selection of the Smoothing Parameters<br>156                     |  |
|         | 5.5.1<br>Fixing the Degrees of Freedom<br>158                              |  |
|         | The Bias–Variance Tradeoff<br>5.5.2<br>158                                 |  |
| $5.6$   | Nonparametric Logistic Regression<br>161                                   |  |
| 5.7     | Multidimensional Splines<br>162                                            |  |
| $5.8\,$ | Regularization and Reproducing Kernel Hilbert Spaces .<br>167              |  |
|         | 5.8.1<br>Spaces of Functions Generated by Kernels $\ldots$<br>168          |  |
|         | 5.8.2<br>Examples of RKHS<br>170                                           |  |
| 5.9     | Wavelet Smoothing<br><u>. </u><br>174                                      |  |
|         | Wavelet Bases and the Wavelet Transform<br>5.9.1<br>176                    |  |
|         | 5.9.2<br>Adaptive Wavelet Filtering<br>179                                 |  |
|         | Bibliographic Notes<br>181                                                 |  |
|         | Exercises<br>181                                                           |  |
|         | Appendix: Computational Considerations for Splines<br>186                  |  |
|         | Appendix: $B$ -splines<br>186                                              |  |
|         | Appendix: Computations for Smoothing Splines<br>189                        |  |

| 6      |                                                                              | <b>Kernel Smoothing Methods</b>                                    | 191        |
|--------|------------------------------------------------------------------------------|--------------------------------------------------------------------|------------|
|        | 6.1                                                                          | One-Dimensional Kernel Smoothers                                   | 192        |
|        |                                                                              | Local Linear Regression<br>6.1.1                                   | 194        |
|        |                                                                              | 6.1.2<br>Local Polynomial Regression                               | 197        |
|        | 6.2                                                                          | Selecting the Width of the Kernel $\ldots \ldots \ldots \ldots$    | 198        |
|        | 6.3                                                                          | Local Regression in $\mathbb{R}^p$                                 | 200        |
|        | 6.4                                                                          | Structured Local Regression Models in $\mathbb{R}^p$               | 201        |
|        |                                                                              | 6.4.1<br>Structured Kernels                                        | 203        |
|        |                                                                              | 6.4.2<br>Structured Regression Functions                           | 203        |
|        | 6.5                                                                          | Local Likelihood and Other Models<br>.                             | 205        |
|        | 6.6                                                                          | Kernel Density Estimation and Classification                       | 208        |
|        |                                                                              | 6.6.1<br>Kernel Density Estimation                                 | 208        |
|        |                                                                              | 6.6.2<br>Kernel Density Classification                             | 210        |
|        |                                                                              | 6.6.3<br>The Naive Bayes Classifier                                | 210        |
|        | 6.7                                                                          | Radial Basis Functions and Kernels                                 | 212        |
|        | 6.8                                                                          | Mixture Models for Density Estimation and Classification           | 214        |
|        | 6.9                                                                          | Computational Considerations                                       | 216        |
|        |                                                                              | Bibliographic Notes                                                | 216        |
|        |                                                                              | Exercises                                                          | 216        |
|        |                                                                              |                                                                    |            |
| 7      |                                                                              | Model Assessment and Selection                                     | 219        |
|        | 7.1                                                                          | Introduction                                                       | 219        |
|        | 7.2                                                                          | Bias, Variance and Model Complexity                                | 219        |
|        | 7.3                                                                          | The Bias–Variance Decomposition                                    | 223        |
|        |                                                                              | Example: Bias-Variance Tradeoff<br>7.3.1                           | 226        |
|        | 7.4                                                                          | Optimism of the Training Error Rate                                | 228        |
|        | 7.5                                                                          | Estimates of In-Sample Prediction Error                            | 230        |
|        | 7.6                                                                          | The Effective Number of Parameters                                 | 232        |
|        | 7.7                                                                          | The Bayesian Approach and BIC                                      | 233        |
|        | 7.8                                                                          | Minimum Description Length                                         | 235        |
|        | 7.9                                                                          | Vapnik–Chervonenkis Dimension                                      | 237        |
|        |                                                                              | Example (Continued) $\ldots \ldots \ldots \ldots \ldots$<br>7.9.1  | 239        |
|        | 7.10                                                                         | Cross-Validation                                                   | 241        |
|        |                                                                              | K-Fold Cross-Validation $\ldots \ldots \ldots \ldots$<br>7.10.1    | 241        |
|        |                                                                              | 7.10.2<br>The Wrong and Right Way                                  |            |
|        |                                                                              | to Do Cross-validation $\ldots \ldots \ldots \ldots \ldots$        | 245        |
|        |                                                                              | Does Cross-Validation Really Work?<br>7.10.3                       | 247        |
|        | 7.11                                                                         | Bootstrap Methods<br><u>. </u>                                     | 249        |
|        |                                                                              | Example (Continued) $\ldots \ldots \ldots \ldots \ldots$<br>7.11.1 | 252        |
|        | 7.12                                                                         | Conditional or Expected Test Error?                                | 254        |
|        |                                                                              | Bibliographic Notes                                                | 257        |
|        |                                                                              | Exercises                                                          | 257        |
|        |                                                                              |                                                                    |            |
| 8      | 8.1                                                                          | Model Inference and Averaging<br>Introduction                      | 261<br>261 |
|        |                                                                              |                                                                    |            |
| 8.2    |                                                                              | The Bootstrap and Maximum Likelihood Methods                       | 261        |
|        | 8.2.1                                                                        | A Smoothing Example                                                | 261        |
|        | 8.2.2                                                                        | Maximum Likelihood Inference                                       | 265        |
|        | 8.2.3                                                                        | Bootstrap versus Maximum Likelihood                                | 267        |
| 8.3    |                                                                              | Bayesian Methods . . . . . . . . .                                 | 267        |
| 8.4    |                                                                              | Relationship Between the Bootstrap<br>and Bayesian Inference       | 271        |
| 8.5    |                                                                              | The EM Algorithm . . . . . . . . . . . .                           | 272        |
|        | 8.5.1                                                                        | Two-Component Mixture Model                                        | 272        |
|        | 8.5.2                                                                        | The EM Algorithm in General . . .                                  | 276        |
|        | 8.5.3                                                                        | EM as a Maximization-Maximization Procedure                        | 277        |
| 8.6    |                                                                              | MCMC for Sampling from the Posterior                               | 279        |
| 8.7    |                                                                              | Bagging                                                            | 282        |
|        | 8.7.1                                                                        | Example: Trees with Simulated Data . .                             | 283        |
| 8.8    |                                                                              | Model Averaging and Stacking                                       | 288        |
| 8.9    |                                                                              | Stochastic Search: Bumping                                         | 290        |
|        |                                                                              | Bibliographic Notes                                                | 292        |
|        |                                                                              | Exercises                                                          | 293        |
| 9      |                                                                              | Additive Models, Trees, and Related Methods                        | 295        |
|        | 9.1                                                                          | Generalized Additive Models                                        | 295        |
|        | 9.1.1                                                                        | Fitting Additive Models                                            | 297        |
|        | 9.1.2                                                                        | Example: Additive Logistic Regression                              | 299        |
|        | 9.1.3                                                                        | Summary                                                            | 304        |
|        | 9.2                                                                          | Tree-Based Methods . . . . . . . . . . .                           | 305        |
|        | 9.2.1                                                                        | Background                                                         | 305        |
|        | 9.2.2                                                                        | Regression Trees                                                   | 307        |
|        | 9.2.3                                                                        | Classification Trees                                               | 308        |
|        | 9.2.4                                                                        | Other Issues                                                       | 310        |
|        | 9.2.5                                                                        | Spam Example (Continued) . . . .                                   | 313        |
|        | 9.3                                                                          | PRIM: Bump Hunting                                                 | 317        |
|        | 9.3.1                                                                        | Spam Example (Continued) . . . .                                   | 320        |
|        | 9.4                                                                          | MARS: Multivariate Adaptive Regression Splines                     | 321        |
|        | 9.4.1                                                                        | Spam Example (Continued) . . . .                                   | 326        |
|        | 9.4.2                                                                        | Example (Simulated Data) . . . .                                   | 327        |
|        | 9.4.3                                                                        | Other Issues                                                       | 328        |
|        | 9.5                                                                          | Hierarchical Mixtures of Experts                                   | 329        |
|        | 9.6                                                                          | Missing Data                                                       | 332        |
|        | 9.7                                                                          | Computational Considerations . . . . .                             | 334        |
|        |                                                                              | Bibliographic Notes                                                | 334        |
|        |                                                                              | Exercises                                                          | 335        |
| 10     |                                                                              | Boosting and Additive Trees                                        | 337        |
|        | 10.1                                                                         | Boosting Methods                                                   | 337        |
|        | 10.1.1                                                                       | Outline of This Chapter                                            | 340        |
| 10.2   | Boosting Fits an Additive Model                                              | 341                                                                |            |
| 10.3   | Forward Stagewise Additive Modeling 342                                      |                                                                    |            |
| 10.4   | Exponential Loss and AdaBoost 343                                            |                                                                    |            |
| 10.5   | Why Exponential Loss? $\ldots \ldots \ldots \ldots \ldots \ldots \ldots 345$ |                                                                    |            |
| 10.6   | Loss Functions and Robustness                                                | 346                                                                |            |
| 10.7   | "Off-the-Shelf" Procedures for Data Mining                                   | 350                                                                |            |
| 10.8   | Example: Spam Data                                                           | 352                                                                |            |
| 10.9   | Boosting Trees                                                               | 353                                                                |            |
|        | 10.10 Numerical Optimization via Gradient Boosting                           | 358                                                                |            |
|        | 10.10.1 Steepest Descent                                                     | 358                                                                |            |
|        | 10.10.2 Gradient Boosting                                                    | 359                                                                |            |
|        | 10.10.3 Implementations of Gradient Boosting                                 | 360                                                                |            |
|        | 10.11 Right-Sized Trees for Boosting                                         | 361                                                                |            |
|        | 10.12 Regularization                                                         | 364                                                                |            |
|        | 10.12.1<br>Shrinkage                                                         | 364                                                                |            |
|        | 10.12.2<br>Subsampling $\ldots \ldots \ldots \ldots \ldots \ldots$           | 365                                                                |            |
|        | $10.13$ Interpretation                                                       | 367                                                                |            |
|        | 10.13.1<br>Relative Importance of Predictor Variables                        | 367                                                                |            |
|        | 10.13.2<br>Partial Dependence Plots                                          | 369                                                                |            |
|        | 10.14 Illustrations                                                          | 371                                                                |            |
|        | 10.14.1 California Housing 371                                               |                                                                    |            |
|        | 10.14.2 New Zealand Fish $\ldots \ldots \ldots \ldots \ldots \ldots 375$     |                                                                    |            |
|        | 10.14.3 Demographics Data 379                                                |                                                                    |            |
|        | Bibliographic Notes                                                          |                                                                    |            |
|        | Exercises                                                                    | 384                                                                |            |
|        | <b>11 Neural Networks</b>                                                    | 389                                                                |            |
| 11.1   | Introduction                                                                 | 389                                                                |            |
| 11.2   | Projection Pursuit Regression                                                | 389                                                                |            |
| 11.3   | Neural Networks                                                              | 392                                                                |            |
| 11.4   | Fitting Neural Networks                                                      | 395                                                                |            |
| 11.5   | Some Issues in Training Neural Networks                                      | 397                                                                |            |
|        | Starting Values<br>11.5.1                                                    | 397                                                                |            |
|        | Overfitting<br>11.5.2                                                        | 398                                                                |            |
|        | 11.5.3<br>Scaling of the Inputs $\dots \dots \dots \dots \dots$              | 398                                                                |            |
|        | 11.5.4<br>Number of Hidden Units and Layers                                  | 400                                                                |            |
|        | Multiple Minima 400<br>11.5.5                                                |                                                                    |            |
|        | 11.6 Example: Simulated Data                                                 |                                                                    |            |
| 11.7   | Example: ZIP Code Data                                                       | 404                                                                |            |
| 11.8   | Discussion<br>.                                                              | 408                                                                |            |
| 11.9   | Bayesian Neural Nets and the NIPS 2003 Challenge                             | 409                                                                |            |
|        | 11.9.1<br>Bayes, Boosting and Bagging                                        | 410                                                                |            |
|        | Performance Comparisons<br>11.9.2                                            | 412                                                                |            |
|        | 11.10 Computational Considerations                                           | 414                                                                |            |
|        | Bibliographic Notes                                                          | 415                                                                |            |
|        | Contents                                                                     | xix                                                                |            |
|        | Exercises                                                                    | 415                                                                |            |
|        | <b>12 Support Vector Machines and Flexible Discriminants</b>                 | 417                                                                |            |
| 12.1   | Introduction                                                                 | 417                                                                |            |
| 12.2   | The Support Vector Classifier                                                | 417                                                                |            |
| 12.2.1 | Computing the Support Vector Classifier                                      | 420                                                                |            |
| 12.2.2 | Mixture Example (Continued)                                                  | 421                                                                |            |
| 12.3   | Support Vector Machines and Kernels                                          | 423                                                                |            |
| 12.3.1 | Computing the SVM for Classification                                         | 423                                                                |            |
| 12.3.2 | The SVM as a Penalization Method                                             | 426                                                                |            |
| 12.3.3 | Function Estimation and Reproducing Kernels                                  | 428                                                                |            |
| 12.3.4 | SVMs and the Curse of Dimensionality                                         | 431                                                                |            |
| 12.3.5 | A Path Algorithm for the SVM Classifier                                      | 432                                                                |            |
| 12.3.6 | Support Vector Machines for Regression                                       | 434                                                                |            |
| 12.3.7 | Regression and Kernels                                                       | 436                                                                |            |
| 12.3.8 | Discussion                                                                   | 438                                                                |            |
| 12.4   | Generalizing Linear Discriminant Analysis                                    | 438                                                                |            |
| 12.5   | Flexible Discriminant Analysis                                               | 440                                                                |            |
| 12.5.1 | Computing the FDA Estimates                                                  | 444                                                                |            |
| 12.6   | Penalized Discriminant Analysis                                              | 446                                                                |            |
| 12.7   | Mixture Discriminant Analysis                                                | 449                                                                |            |
| 12.7.1 | Example: Waveform Data                                                       | 451                                                                |            |
|        | Bibliographic Notes                                                          | 455                                                                |            |
|        | Exercises                                                                    | 455                                                                |            |
|        | <b>13 Prototype Methods and Nearest-Neighbors</b>                            | 459                                                                |            |
| 13.1   | Introduction                                                                 | 459                                                                |            |
| 13.2   | Prototype Methods                                                            | 459                                                                |            |
| 13.2.1 | $K$ -means Clustering                                                        | 460                                                                |            |
| 13.2.2 | Learning Vector Quantization                                                 | 462                                                                |            |
| 13.2.3 | Gaussian Mixtures                                                            | 463                                                                |            |
| 13.3   | $k$ -Nearest-Neighbor Classifiers                                            | 463                                                                |            |
| 13.3.1 | Example: A Comparative Study                                                 | 468                                                                |            |
| 13.3.2 | Example: $k$ -Nearest-Neighbors and Image Scene Classification               | 470                                                                |            |
| 13.3.3 | Invariant Metrics and Tangent Distance                                       | 471                                                                |            |
| 13.4   | Adaptive Nearest-Neighbor Methods                                            | 475                                                                |            |
| 13.4.1 | Example                                                                      | 478                                                                |            |
| 13.4.2 | Global Dimension Reduction for Nearest-Neighbors                             | 479                                                                |            |
| 13.5   | Computational Considerations                                                 | 480                                                                |            |
|        | Bibliographic Notes                                                          | 481                                                                |            |
|        | Exercises                                                                    | 481                                                                |            |

| 14 Unsupervised Learning |         |                                                 | 485 |
|--------------------------|---------|-------------------------------------------------|-----|
| 14.1                     |         | Introduction                                    | 485 |
| 14.2                     |         | Association Rules                               | 487 |
|                          | 14.2.1  | Market Basket Analysis                          | 488 |
|                          | 14.2.2  | The Apriori Algorithm                           | 489 |
|                          | 14.2.3  | Example: Market Basket Analysis                 | 492 |
|                          | 14.2.4  | Unsupervised as Supervised Learning             | 495 |
|                          | 14.2.5  | Generalized Association Rules                   | 497 |
|                          | 14.2.6  | Choice of Supervised Learning Method            | 499 |
|                          | 14.2.7  | Example: Market Basket Analysis (Continued)     | 499 |
| 14.3                     |         | Cluster Analysis                                | 501 |
|                          | 14.3.1  | Proximity Matrices                              | 503 |
|                          | 14.3.2  | Dissimilarities Based on Attributes             | 503 |
|                          | 14.3.3  | Object Dissimilarity                            | 505 |
|                          | 14.3.4  | Clustering Algorithms                           | 507 |
|                          | 14.3.5  | Combinatorial Algorithms                        | 507 |
|                          | 14.3.6  | $K$ -means                                      | 509 |
|                          | 14.3.7  | Gaussian Mixtures as Soft $K$ -means Clustering | 510 |
|                          | 14.3.8  | Example: Human Tumor Microarray Data            | 512 |
|                          | 14.3.9  | Vector Quantization                             | 514 |
|                          | 14.3.10 | $K$ -medoids                                    | 515 |
|                          | 14.3.11 | Practical Issues                                | 518 |
|                          | 14.3.12 | Hierarchical Clustering                         | 520 |
| 14.4                     |         | Self-Organizing Maps                            | 528 |
| 14.5                     |         | Principal Components, Curves and Surfaces       | 534 |
|                          | 14.5.1  | Principal Components                            | 534 |
|                          | 14.5.2  | Principal Curves and Surfaces                   | 541 |
|                          | 14.5.3  | Spectral Clustering                             | 544 |
|                          | 14.5.4  | Kernel Principal Components                     | 547 |
|                          | 14.5.5  | Sparse Principal Components                     | 550 |
| 14.6                     |         | Non-negative Matrix Factorization               | 553 |
|                          | 14.6.1  | Archetypal Analysis                             | 554 |
| 14.7                     |         | Independent Component Analysis                  | 557 |
|                          |         | and Exploratory Projection Pursuit              |     |
|                          | 14.7.1  | Latent Variables and Factor Analysis            | 558 |
|                          | 14.7.2  | Independent Component Analysis                  | 560 |
|                          | 14.7.3  | Exploratory Projection Pursuit                  | 565 |
|                          | 14.7.4  | A Direct Approach to ICA                        | 565 |
| 14.8                     |         | Multidimensional Scaling                        | 570 |
| 14.9                     |         | Nonlinear Dimension Reduction                   | 572 |
|                          |         | and Local Multidimensional Scaling              |     |
|                          |         | 14.10 The Google PageRank Algorithm             | 576 |
|                          |         | Bibliographic Notes                             | 578 |
|                          |         | Exercises                                       | 579 |

| Contents | xxi |
|----------|-----|
|----------|-----|

| <b>15 Random Forests</b><br>587                |                                                                                                                                                                                                      |                                                             |     |  |  |
|------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------|-----|--|--|
| 15.1                                           | Introduction<br>.                                                                                                                                                                                    |                                                             |     |  |  |
| 15.2                                           | Definition of Random Forests                                                                                                                                                                         |                                                             |     |  |  |
| 15.3                                           | Details of Random Forests                                                                                                                                                                            |                                                             |     |  |  |
|                                                | 15.3.1<br>Out of Bag Samples                                                                                                                                                                         | 592                                                         |     |  |  |
|                                                | 15.3.2<br>Variable Importance                                                                                                                                                                        | 593                                                         |     |  |  |
|                                                | 15.3.3<br>Proximity Plots                                                                                                                                                                            | 595                                                         |     |  |  |
|                                                | 15.3.4<br>Random Forests and Overfitting                                                                                                                                                             | 596                                                         |     |  |  |
| 15.4                                           | Analysis of Random Forests                                                                                                                                                                           | 597                                                         |     |  |  |
|                                                | Variance and the De-Correlation Effect $\;\ldots\;\ldots\;$<br>15.4.1                                                                                                                                | 597                                                         |     |  |  |
|                                                | 15.4.2<br>Bias                                                                                                                                                                                       | 600                                                         |     |  |  |
|                                                | Adaptive Nearest Neighbors<br>15.4.3                                                                                                                                                                 | 601                                                         |     |  |  |
|                                                | Bibliographic Notes                                                                                                                                                                                  | 602                                                         |     |  |  |
|                                                | Exercises                                                                                                                                                                                            | 603                                                         |     |  |  |
|                                                |                                                                                                                                                                                                      |                                                             |     |  |  |
|                                                | 16 Ensemble Learning                                                                                                                                                                                 | 605                                                         |     |  |  |
| 16.1                                           | Introduction<br>.                                                                                                                                                                                    | 605                                                         |     |  |  |
| 16.2                                           | Boosting and Regularization Paths                                                                                                                                                                    | 607                                                         |     |  |  |
|                                                | 16.2.1<br>Penalized Regression                                                                                                                                                                       | 607                                                         |     |  |  |
|                                                | The "Bet on Sparsity" Principle<br>16.2.2                                                                                                                                                            | 610                                                         |     |  |  |
|                                                | Regularization Paths, Over-fitting and Margins.<br>16.2.3                                                                                                                                            | 613                                                         |     |  |  |
| 16.3                                           | Learning Ensembles                                                                                                                                                                                   | 616                                                         |     |  |  |
|                                                | 16.3.1<br>Learning a Good Ensemble                                                                                                                                                                   | 617                                                         |     |  |  |
|                                                | Rule Ensembles<br>16.3.2                                                                                                                                                                             | 622                                                         |     |  |  |
| Bibliographic Notes                            |                                                                                                                                                                                                      |                                                             |     |  |  |
|                                                | Exercises                                                                                                                                                                                            | 624                                                         |     |  |  |
|                                                |                                                                                                                                                                                                      | 625                                                         |     |  |  |
| 17.1                                           | 17 Undirected Graphical Models<br>Introduction                                                                                                                                                       | 625                                                         |     |  |  |
| 17.2                                           |                                                                                                                                                                                                      | 627                                                         |     |  |  |
| 17.3                                           | Markov Graphs and Their Properties                                                                                                                                                                   |                                                             |     |  |  |
|                                                | Undirected Graphical Models for Continuous Variables .<br>Estimation of the Parameters<br>17.3.1                                                                                                     | 630                                                         |     |  |  |
|                                                |                                                                                                                                                                                                      |                                                             |     |  |  |
|                                                | when the Graph Structure is Known $\dots \dots$<br>17.3.2                                                                                                                                            | 631<br>635                                                  |     |  |  |
| 17.4                                           | Estimation of the Graph Structure $\ldots \ldots$<br>Undirected Graphical Models for Discrete Variables                                                                                              | 638                                                         |     |  |  |
|                                                | Estimation of the Parameters<br>17.4.1                                                                                                                                                               |                                                             |     |  |  |
|                                                | when the Graph Structure is Known $\dots \dots$                                                                                                                                                      | $639\,$                                                     |     |  |  |
|                                                | 17.4.2<br>Hidden Nodes<br>.                                                                                                                                                                          | 641                                                         |     |  |  |
|                                                | Estimation of the Graph Structure<br>17.4.3                                                                                                                                                          | 642                                                         |     |  |  |
|                                                | Restricted Boltzmann Machines<br>17.4.4                                                                                                                                                              | 643                                                         |     |  |  |
|                                                | Exercises                                                                                                                                                                                            | 645                                                         |     |  |  |
|                                                | .                                                                                                                                                                                                    |                                                             |     |  |  |
| 649<br>18 High-Dimensional Problems: $p \gg N$ |                                                                                                                                                                                                      |                                                             |     |  |  |
| 18.1                                           | When $p$ is Much Bigger than $N$<br>$\begin{array}{cccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccc$ | 649                                                         |     |  |  |
| 18.2                                           | Diagonal Linear Discriminant Analysis                                                                                                                                                                |                                                             |     |  |  |
|                                                |                                                                                                                                                                                                      | and Nearest Shrunken Centroids                              | 651 |  |  |
| 18.3                                           |                                                                                                                                                                                                      | Linear Classifiers with Quadratic Regularization            | 654 |  |  |
|                                                | 18.3.1                                                                                                                                                                                               | Regularized Discriminant Analysis                           | 656 |  |  |
|                                                | 18.3.2                                                                                                                                                                                               | Logistic Regression                                         |     |  |  |
|                                                |                                                                                                                                                                                                      | with Quadratic Regularization $\ldots \ldots \ldots$        | 657 |  |  |
|                                                | 18.3.3                                                                                                                                                                                               | The Support Vector Classifier                               | 657 |  |  |
|                                                | 18.3.4                                                                                                                                                                                               | Feature Selection                                           | 658 |  |  |
|                                                | 18.3.5                                                                                                                                                                                               | Computational Shortcuts When $p \gg N$                      | 659 |  |  |
| 18.4                                           |                                                                                                                                                                                                      | Linear Classifiers with $L_1$ Regularization                | 661 |  |  |
|                                                | 18.4.1                                                                                                                                                                                               | Application of Lasso                                        |     |  |  |
|                                                |                                                                                                                                                                                                      | to Protein Mass Spectroscopy                                | 664 |  |  |
|                                                | 18.4.2                                                                                                                                                                                               | The Fused Lasso for Functional Data $\;\ldots\; \ldots$ .   | 666 |  |  |
| 18.5                                           |                                                                                                                                                                                                      | Classification When Features are Unavailable                | 668 |  |  |
|                                                | 18.5.1                                                                                                                                                                                               | Example: String Kernels                                     |     |  |  |
|                                                |                                                                                                                                                                                                      | and Protein Classification                                  | 668 |  |  |
|                                                | 18.5.2                                                                                                                                                                                               | Classification and Other Models Using                       |     |  |  |
|                                                |                                                                                                                                                                                                      | ${\it Inner-Product}$ Kernels and Pairwise Distances $\,$ . | 670 |  |  |
|                                                | 18.5.3                                                                                                                                                                                               | Example: Abstracts Classification                           | 672 |  |  |
| 18.6                                           |                                                                                                                                                                                                      | High-Dimensional Regression:                                |     |  |  |
|                                                |                                                                                                                                                                                                      | Supervised Principal Components                             | 674 |  |  |
|                                                | 18.6.1                                                                                                                                                                                               | Connection to Latent-Variable Modeling                      | 678 |  |  |
|                                                | 18.6.2                                                                                                                                                                                               | Relationship with Partial Least Squares                     | 680 |  |  |
|                                                | 18.6.3                                                                                                                                                                                               | Pre-Conditioning for Feature Selection                      | 681 |  |  |
| 18.7                                           |                                                                                                                                                                                                      | Feature Assessment and the Multiple-Testing Problem         | 683 |  |  |
|                                                | 18.7.1                                                                                                                                                                                               | The False Discovery Rate                                    | 687 |  |  |
|                                                | 18.7.2                                                                                                                                                                                               | Asymmetric Cutpoints and the SAM Procedure                  | 690 |  |  |
|                                                | 18.7.3                                                                                                                                                                                               | A Bayesian Interpretation of the FDR                        | 692 |  |  |
| 18.8                                           |                                                                                                                                                                                                      | Bibliographic Notes                                         | 693 |  |  |
|                                                |                                                                                                                                                                                                      | Exercises $\ldots$ $\ldots$ $\ldots$<br>.                   | 694 |  |  |
| References                                     |                                                                                                                                                                                                      |                                                             | 699 |  |  |
|                                                | <b>Author Index</b>                                                                                                                                                                                  |                                                             | 729 |  |  |
| Index                                          |                                                                                                                                                                                                      |                                                             | 737 |  |  |

#### xxii Contents