{"table_of_contents": [{"title": "22 More variational inference", "heading_level": null, "page_id": 0, "polygon": [[51.8203125, 93.181640625], [326.25, 93.181640625], [326.25, 148.5], [51.8203125, 142.857421875]]}, {"title": "22.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[95.25, 207.0], [195.75, 207.0], [195.75, 218.00390625], [95.25, 218.00390625]]}, {"title": "22.2 Loopy belief propagation: algorithmic issues", "heading_level": null, "page_id": 0, "polygon": [[93.0, 413.25], [360.75, 413.25], [360.75, 424.6171875], [93.0, 424.6171875]]}, {"title": "22.2.1 A brief history", "heading_level": null, "page_id": 0, "polygon": [[88.453125, 521.25], [199.5, 521.25], [199.5, 532.51171875], [88.453125, 532.51171875]]}, {"title": "22.2.2 LBP on pairwise models", "heading_level": null, "page_id": 1, "polygon": [[86.8359375, 337.5], [245.25, 337.5], [245.25, 347.73046875], [86.8359375, 347.73046875]]}, {"title": "22.2.3 LBP on a factor graph", "heading_level": null, "page_id": 2, "polygon": [[86.34375, 341.25], [235.5, 341.25], [235.5, 351.84375], [86.34375, 351.84375]]}, {"title": "22.2.3.1 Factor graphs", "heading_level": null, "page_id": 2, "polygon": [[83.8125, 421.5], [190.5, 421.5], [190.5, 431.578125], [83.8125, 431.578125]]}, {"title": "22.2.3.2 BP on a factor graph", "heading_level": null, "page_id": 4, "polygon": [[80.859375, 274.5], [221.25, 274.5], [221.25, 285.3984375], [80.859375, 285.3984375]]}, {"title": "22.2.4 Convergence", "heading_level": null, "page_id": 4, "polygon": [[86.9765625, 523.5], [190.5, 523.5], [190.5, 533.77734375], [86.9765625, 533.77734375]]}, {"title": "22.2.4.1 When will LBP converge?", "heading_level": null, "page_id": 5, "polygon": [[83.8125, 427.5], [238.78125, 427.5], [238.78125, 437.58984375], [83.8125, 437.58984375]]}, {"title": "22.2.4.2 Making LBP converge", "heading_level": null, "page_id": 6, "polygon": [[81.75, 249.0], [223.875, 249.0], [223.875, 259.453125], [81.75, 259.453125]]}, {"title": "22.2.4.3 Increasing the convergence rate: message scheduling", "heading_level": null, "page_id": 6, "polygon": [[81.140625, 486.0], [360.75, 486.0], [360.75, 496.44140625], [81.140625, 496.44140625]]}, {"title": "22.2.5 Accuracy of LBP", "heading_level": null, "page_id": 7, "polygon": [[87.75, 490.5], [209.390625, 490.5], [209.390625, 500.5546875], [87.75, 500.5546875]]}, {"title": "22.2.6 Other speedup tricks for LBP *", "heading_level": null, "page_id": 8, "polygon": [[85.9921875, 60.75], [276.75, 60.75], [276.75, 71.5078125], [85.9921875, 71.5078125]]}, {"title": "22.2.6.1 Fast message computation for large state spaces", "heading_level": null, "page_id": 8, "polygon": [[84.0, 105.75], [337.5, 105.75], [337.5, 115.646484375], [84.0, 115.646484375]]}, {"title": "22.2.6.2 Multi-scale methods", "heading_level": null, "page_id": 8, "polygon": [[81.703125, 359.25], [217.265625, 359.25], [217.265625, 369.24609375], [81.703125, 369.24609375]]}, {"title": "22.2.6.3 Cascades", "heading_level": null, "page_id": 9, "polygon": [[81.75, 61.5], [170.25, 61.5], [170.25, 71.70556640625], [81.75, 71.70556640625]]}, {"title": "22.3 Loopy belief propagation: theoretical issues *", "heading_level": null, "page_id": 9, "polygon": [[93.0, 156.0], [365.25, 156.0], [365.25, 167.220703125], [93.0, 167.220703125]]}, {"title": "22.3.1 UGMs represented in exponential family form", "heading_level": null, "page_id": 9, "polygon": [[88.2421875, 288.75], [347.25, 288.75], [347.25, 298.6875], [88.2421875, 298.6875]]}, {"title": "22.3.2 The marginal polytope", "heading_level": null, "page_id": 10, "polygon": [[86.6953125, 213.75], [236.25, 213.75], [236.25, 223.69921875], [86.6953125, 223.69921875]]}, {"title": "22.3.3 Exact inference as a variational optimization problem", "heading_level": null, "page_id": 11, "polygon": [[87.1875, 317.25], [386.25, 317.25], [386.25, 327.48046875], [87.1875, 327.48046875]]}, {"title": "22.3.4 Mean field as a variational optimization problem", "heading_level": null, "page_id": 12, "polygon": [[86.0625, 60.75], [363.0, 60.75], [363.0, 71.62646484375], [86.0625, 71.62646484375]]}, {"title": "22.3.5 LBP as a variational optimization problem", "heading_level": null, "page_id": 12, "polygon": [[87.1875, 571.5], [332.25, 571.5], [332.25, 581.5546875], [87.1875, 581.5546875]]}, {"title": "22.3.5.1 An outer approximation to the marginal polytope", "heading_level": null, "page_id": 13, "polygon": [[82.828125, 286.5], [343.6875, 286.5], [343.6875, 295.998046875], [82.828125, 295.998046875]]}, {"title": "******** The entropy approximation", "heading_level": null, "page_id": 14, "polygon": [[81.75, 225.75], [249.0, 225.75], [249.0, 235.880859375], [81.75, 235.880859375]]}, {"title": "******** The LBP objective", "heading_level": null, "page_id": 15, "polygon": [[81.5625, 62.25], [208.40625, 62.25], [208.40625, 71.349609375], [81.5625, 71.349609375]]}, {"title": "******** Message passing and <PERSON><PERSON><PERSON> multipliers", "heading_level": null, "page_id": 15, "polygon": [[81.703125, 207.0], [312.0, 207.0], [312.0, 216.263671875], [81.703125, 216.263671875]]}, {"title": "22.3.6 <PERSON>y <PERSON> vs mean field", "heading_level": null, "page_id": 16, "polygon": [[85.78125, 196.5], [242.25, 196.5], [242.25, 206.455078125], [85.78125, 206.455078125]]}, {"title": "22.4 Extensions of belief propagation *", "heading_level": null, "page_id": 16, "polygon": [[92.6015625, 469.5], [306.0, 469.5], [306.0, 480.9375], [92.6015625, 480.9375]]}, {"title": "22.4.1 Generalized belief propagation", "heading_level": null, "page_id": 16, "polygon": [[89.25, 519.75], [276.0, 519.75], [276.0, 529.98046875], [89.25, 529.98046875]]}, {"title": "22.4.2 Convex belief propagation", "heading_level": null, "page_id": 18, "polygon": [[87.2578125, 199.5], [254.25, 199.5], [254.25, 210.568359375], [87.2578125, 210.568359375]]}, {"title": "******** Tree-reweighted belief propagation", "heading_level": null, "page_id": 19, "polygon": [[83.25, 225.0], [279.75, 225.0], [279.75, 234.931640625], [83.25, 234.931640625]]}, {"title": "22.5 Expectation propagation", "heading_level": null, "page_id": 20, "polygon": [[93.0, 500.25], [255.75, 500.25], [255.75, 510.99609375], [93.0, 510.99609375]]}, {"title": "22.5.1 EP as a variational inference problem", "heading_level": null, "page_id": 21, "polygon": [[88.9453125, 112.5], [309.0, 112.5], [309.0, 122.44921875], [88.9453125, 122.44921875]]}, {"title": "22.5.2 Optimizing the EP objective using moment matching", "heading_level": null, "page_id": 22, "polygon": [[87.0, 311.25], [381.0, 311.25], [381.0, 321.310546875], [87.0, 321.310546875]]}, {"title": "22.5.3 EP for the clutter problem", "heading_level": null, "page_id": 24, "polygon": [[86.34375, 139.5], [255.75, 139.5], [255.75, 150.134765625], [86.34375, 150.134765625]]}, {"title": "22.5.4 LBP is a special case of EP", "heading_level": null, "page_id": 25, "polygon": [[87.75, 192.75], [257.25, 192.75], [257.25, 203.44921875], [87.75, 203.44921875]]}, {"title": "22.5.5 Ranking players using TrueSkill", "heading_level": null, "page_id": 26, "polygon": [[87.46875, 559.5], [279.75, 559.5], [279.75, 569.84765625], [87.46875, 569.84765625]]}, {"title": "22.5.6 Other applications of EP", "heading_level": null, "page_id": 32, "polygon": [[86.90625, 112.5], [246.09375, 112.5], [246.09375, 122.5283203125], [86.90625, 122.5283203125]]}, {"title": "22.6 MAP state estimation", "heading_level": null, "page_id": 32, "polygon": [[92.8828125, 242.25], [240.0, 242.25], [240.0, 254.07421875], [92.8828125, 254.07421875]]}, {"title": "22.6.1 Linear programming relaxation", "heading_level": null, "page_id": 32, "polygon": [[88.5, 426.75], [279.75, 426.75], [279.75, 436.640625], [88.5, 436.640625]]}, {"title": "22.6.2 Max-product belief propagation", "heading_level": null, "page_id": 33, "polygon": [[86.484375, 230.25], [279.75, 230.25], [279.75, 240.626953125], [86.484375, 240.626953125]]}, {"title": "22.6.3 Graphcuts", "heading_level": null, "page_id": 34, "polygon": [[86.203125, 184.5], [179.578125, 184.5], [179.578125, 194.748046875], [86.203125, 194.748046875]]}, {"title": "22.6.3.1 Graphcuts for the generalized Ising model", "heading_level": null, "page_id": 34, "polygon": [[82.96875, 300.0], [313.5, 300.0], [313.5, 310.39453125], [82.96875, 310.39453125]]}, {"title": "22.6.3.2 Graphcuts for binary MRFs with submodular potentials", "heading_level": null, "page_id": 35, "polygon": [[81.5625, 410.25], [370.5, 410.25], [370.5, 420.50390625], [81.5625, 420.50390625]]}, {"title": "22.6.3.3 Graphcuts for nonbinary metric MRFs", "heading_level": null, "page_id": 36, "polygon": [[81.703125, 291.0], [294.75, 291.0], [294.75, 300.427734375], [81.703125, 300.427734375]]}, {"title": "22.6.4 Experimental comparison of graphcuts and BP", "heading_level": null, "page_id": 37, "polygon": [[87.0, 499.5], [351.0, 499.5], [351.0, 510.046875], [87.0, 510.046875]]}, {"title": "22.6.5 Dual decomposition", "heading_level": null, "page_id": 39, "polygon": [[86.25, 441.0], [225.75, 441.0], [225.75, 452.14453125], [86.25, 452.14453125]]}, {"title": "22.6.5.1 Basic idea", "heading_level": null, "page_id": 40, "polygon": [[83.1796875, 253.5], [175.21875, 253.5], [175.21875, 263.408203125], [83.1796875, 263.408203125]]}, {"title": "22.6.5.2 Theoretical guarantees", "heading_level": null, "page_id": 41, "polygon": [[81.75, 477.75], [228.75, 477.0], [228.75, 487.58203125], [81.75, 487.58203125]]}, {"title": "22.6.5.3 Subgradient descent", "heading_level": null, "page_id": 42, "polygon": [[81.28125, 338.25], [219.0, 338.25], [219.0, 348.36328125], [81.28125, 348.36328125]]}, {"title": "22.6.5.4 Coordinate descent", "heading_level": null, "page_id": 43, "polygon": [[81.75, 333.0], [213.75, 333.0], [213.75, 342.984375], [81.75, 342.984375]]}, {"title": "22.6.5.5 Recovering the MAP assignment", "heading_level": null, "page_id": 44, "polygon": [[81.75, 414.0], [269.25, 414.0], [269.25, 423.984375], [81.75, 423.984375]]}, {"title": "Exercises", "heading_level": null, "page_id": 45, "polygon": [[129.515625, 61.5], [179.859375, 61.5], [179.859375, 71.9033203125], [129.515625, 71.9033203125]]}, {"title": "Exercise 22.4 Dual decomposition for pose segmentation", "heading_level": null, "page_id": 45, "polygon": [[129.75, 483.0], [339.0, 483.0], [339.0, 491.0625], [129.75, 491.0625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 28], ["Text", 5], ["SectionHeader", 4], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9139, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 44], ["TableCell", 20], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Table", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1619, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 34], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 94], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1444, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 35], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 693, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 51], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 40], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 52], ["Text", 6], ["Equation", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["Line", 69], ["Equation", 6], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 46], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 59], ["Text", 7], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1264, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 54], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 867, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 56], ["Equation", 7], ["TextInlineMath", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 640], ["Line", 60], ["Equation", 7], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3046, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 44], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 55], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 864, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 50], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 62], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1964, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 58], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["Line", 55], ["Text", 7], ["Equation", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 736], ["Line", 60], ["Equation", 8], ["TextInlineMath", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 49], ["Equation", 7], ["Text", 6], ["ListItem", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 51], ["Equation", 11], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 65], ["Equation", 9], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 52], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 918, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 44], ["TextInlineMath", 5], ["Equation", 4], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 725], ["Line", 85], ["Equation", 14], ["Text", 7], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 71], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 58], ["Equation", 8], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 43], ["TextInlineMath", 6], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["Line", 66], ["Text", 6], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 40], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 41], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 703, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 49], ["Equation", 6], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Picture", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 51], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 916, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 22], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 667, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 105], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2186, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 63], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 717], ["Line", 91], ["Equation", 6], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4310, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 46], ["ListItem", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1006, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 49], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 548], ["Line", 47], ["Text", 10], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 41], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-26"}