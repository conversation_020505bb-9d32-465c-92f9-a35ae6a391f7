{"table_of_contents": [{"title": "Maximum Entropy and \nSpectral Estimation", "heading_level": null, "page_id": 0, "polygon": [[69.0, 104.09765625], [337.974609375, 104.09765625], [337.974609375, 157.095703125], [69.0, 157.095703125]]}, {"title": "11.1 MAXIMUM ENTROPY DISTRIBUTIONS", "heading_level": null, "page_id": 0, "polygon": [[69.75, 401.8359375], [300.041015625, 401.8359375], [300.041015625, 413.2265625], [69.75, 413.2265625]]}, {"title": "11.2 EXAMPLES", "heading_level": null, "page_id": 2, "polygon": [[63.75, 234.615234375], [150.134765625, 234.615234375], [150.134765625, 245.056640625], [63.75, 245.056640625]]}, {"title": "11.3 AN ANOMALOUS \nMAXIMUM ENTROPY PROBLEM", "heading_level": null, "page_id": 4, "polygon": [[63.1845703125, 495.4921875], [352.37109375, 495.4921875], [352.37109375, 506.3075256347656], [63.1845703125, 506.3075256347656]]}, {"title": "11.4 SPECTRUM ESTIMATION", "heading_level": null, "page_id": 6, "polygon": [[62.25, 146.25], [221.66015625, 146.25], [221.66015625, 156.7001953125], [62.25, 156.7001953125]]}, {"title": "11.5 ENTROPY RATES OF A GAUSSIAN PROCESS", "heading_level": null, "page_id": 7, "polygon": [[68.25, 251.25], [324.75, 251.25], [324.75, 262.142578125], [68.25, 262.142578125]]}, {"title": "11.6 BURG'S MAXIMUM ENTROPY THEOREM", "heading_level": null, "page_id": 8, "polygon": [[63.0, 310.5], [303.0, 310.5], [303.0, 320.51953125], [63.0, 320.51953125]]}, {"title": "SUMMARY OF CHAPTER 11", "heading_level": null, "page_id": 11, "polygon": [[168.0, 84.75], [303.75, 84.75], [303.75, 94.60546875], [168.0, 94.60546875]]}, {"title": "PROBLEMS FOR CHAPTER 11", "heading_level": null, "page_id": 11, "polygon": [[66.75, 321.0], [217.5, 321.0], [217.5, 331.59375], [66.75, 331.59375]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 12, "polygon": [[65.25, 360.75], [172.30078125, 360.75], [172.30078125, 371.14453125], [65.25, 371.14453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 28], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 33], ["Equation", 9], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 33], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 38], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 949, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 33], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 43], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 38], ["Text", 8], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 33], ["Text", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 30], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 35], ["Equation", 8], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 37], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 32], ["ListItem", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 28], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Equation", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1496, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_288-300"}