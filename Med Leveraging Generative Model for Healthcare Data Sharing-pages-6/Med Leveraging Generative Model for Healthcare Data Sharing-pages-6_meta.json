{"table_of_contents": [{"title": "fTSPL: Enhancing Brain Analysis\nwith FMRI-Text Synergistic Prompt\nLearning", "heading_level": null, "page_id": 0, "polygon": [[81.09215017064847, 52.0279541015625], [344.82421875, 52.0279541015625], [344.82421875, 102.916015625], [81.09215017064847, 102.916015625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[39.04436860068259, 559.19873046875], [134.4921875, 559.19873046875], [134.4921875, 571.57470703125], [39.04436860068259, 571.57470703125]]}, {"title": "2 Method", "heading_level": null, "page_id": 2, "polygon": [[38.29351535836177, 312.65625], [106.62116040955631, 312.65625], [106.62116040955631, 326.00927734375], [38.29351535836177, 326.00927734375]]}, {"title": "2.1 Activation-Driven Brain-Region Text Generation (ABTG)", "heading_level": null, "page_id": 2, "polygon": [[38.29351535836177, 523.37353515625], [359.65870307167233, 523.37353515625], [359.65870307167233, 535.09814453125], [38.29351535836177, 535.09814453125]]}, {"title": "2.2 Prompt-Boosted Multi-modal Functional Connectivity\nConstruction (PMFCC)", "heading_level": null, "page_id": 4, "polygon": [[39.04436860068259, 167.07568359375], [343.75, 167.07568359375], [343.75, 190.52490234375], [39.04436860068259, 190.52490234375]]}, {"title": "2.3 Model Optimization", "heading_level": null, "page_id": 5, "polygon": [[52.55972696245733, 448.7919921875], [181.7064846416382, 448.7919921875], [181.7064846416382, 458.56250000000006], [52.55972696245733, 458.56250000000006]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 6, "polygon": [[38.29351535836177, 265.1064453125], [135.13671875, 265.1064453125], [135.13671875, 279.76220703125], [38.29351535836177, 279.76220703125]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 6, "polygon": [[38.29351535836177, 290.998291015625], [167.36328125, 290.998291015625], [167.36328125, 303.699951171875], [38.29351535836177, 303.699951171875]]}, {"title": "3.2 Experimental Results", "heading_level": null, "page_id": 7, "polygon": [[52.55972696245733, 436.416015625], [188.4641638225256, 436.416015625], [188.4641638225256, 446.837890625], [52.55972696245733, 446.837890625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[38.29351535836177, 273.57421875], [125.36132812499999, 273.57421875], [125.36132812499999, 287.904296875], [38.29351535836177, 287.904296875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[38.29351535836177, 468.33300781250006], [103.2861328125, 468.33300781250006], [103.2861328125, 483.31445312500006], [38.29351535836177, 483.31445312500006]]}, {"title": "HiA: Towards Chinese Multimodal LLMs\nfor Comparative High-Resolution Joint\nDiagnosis", "heading_level": null, "page_id": 11, "polygon": [[81.84300341296928, 51.41729736328125], [371.46484375, 51.41729736328125], [371.46484375, 103.078857421875], [81.84300341296928, 103.078857421875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 11, "polygon": [[52.55972696245733, 538.35498046875], [148.779296875, 538.35498046875], [148.779296875, 550.73095703125], [52.55972696245733, 550.73095703125]]}, {"title": "2 Method", "heading_level": null, "page_id": 13, "polygon": [[52.55972696245733, 522.07080078125], [120.13651877133105, 522.07080078125], [120.13651877133105, 534.44677734375], [52.55972696245733, 534.44677734375]]}, {"title": "2.1 Existing Multimodal Large Language Model", "heading_level": null, "page_id": 14, "polygon": [[38.29351535836177, 310.37646484375], [289.1796875, 310.37646484375], [289.1796875, 322.75244140625], [38.29351535836177, 322.75244140625]]}, {"title": "2.2 High-Resolution Instruction-Aware Adapter", "heading_level": null, "page_id": 15, "polygon": [[52.55972696245733, 179.31721034870642], [300.3515625, 179.31721034870642], [300.3515625, 189.710693359375], [52.55972696245733, 189.710693359375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 16, "polygon": [[38.29351535836177, 319.49560546875], [135.029296875, 319.49560546875], [135.029296875, 332.197265625], [38.29351535836177, 332.197265625]]}, {"title": "3.1 Comparison with the State-of-the-Art Methods", "heading_level": null, "page_id": 17, "polygon": [[52.55972696245733, 387.88916015625], [318.3984375, 387.88916015625], [318.3984375, 397.65966796875], [52.55972696245733, 397.65966796875]]}, {"title": "3.2 Ablation Study", "heading_level": null, "page_id": 18, "polygon": [[38.29351535836177, 402.544921875], [143.0859375, 402.544921875], [143.0859375, 414.26953125], [38.29351535836177, 414.26953125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 19, "polygon": [[52.55972696245733, 384.8942632170979], [138.90784982935153, 385.28369140625], [138.90784982935153, 397.00830078125], [52.55972696245733, 397.00830078125]]}, {"title": "References", "heading_level": null, "page_id": 20, "polygon": [[38.29351535836177, 93.0640869140625], [103.8232421875, 93.0640869140625], [103.8232421875, 106.2542724609375], [38.29351535836177, 106.2542724609375]]}, {"title": "Knowledge-Grounded Adaptation\nStrategy for Vision-Language Models:\nBuilding a Unique Case-Set for Screening\nMammograms for Residents Training", "heading_level": null, "page_id": 23, "polygon": [[76.484375, 51.295166015625], [378.5546875, 51.295166015625], [378.5546875, 121.31713867187501], [76.484375, 121.31713867187501]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 23, "polygon": [[53.20068359375, 557.24462890625], [149.1015625, 557.24462890625], [149.1015625, 568.96923828125], [53.20068359375, 568.96923828125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 25, "polygon": [[52.55972696245733, 239.70312500000003], [156.17747440273035, 239.70312500000003], [156.17747440273035, 252.07910156250003], [52.55972696245733, 252.07910156250003]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 25, "polygon": [[52.55972696245733, 496.99316406250006], [150.92150170648463, 496.99316406250006], [150.92150170648463, 509.36914062500006], [52.55972696245733, 509.36914062500006]]}, {"title": "4 Experiments and Results", "heading_level": null, "page_id": 27, "polygon": [[52.55972696245733, 431.8564453125], [222.25255972696243, 431.8564453125], [222.25255972696243, 444.**********], [52.55972696245733, 444.**********]]}, {"title": "5 Discussion and Conclusion", "heading_level": null, "page_id": 32, "polygon": [[38.994140625, 54.4705810546875], [216.9965870307167, 54.4705810546875], [216.9965870307167, 66.11376953125], [38.994140625, 66.11376953125]]}, {"title": "References", "heading_level": null, "page_id": 32, "polygon": [[38.29351535836177, 537.9516310461192], [102.8668941979522, 537.9516310461192], [102.8668941979522, 549.75390625], [38.29351535836177, 549.75390625]]}, {"title": "Learnable Skeleton-Based Medical\nLandmark Estimation with Graph Sparsity\nand Fiedler Regularizations", "heading_level": null, "page_id": 35, "polygon": [[75.302734375, 50.92877197265625], [378.984375, 50.92877197265625], [378.984375, 103.24169921875], [75.302734375, 103.24169921875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 35, "polygon": [[52.55972696245733, 462.47070312500006], [148.66894197952217, 462.47070312500006], [148.66894197952217, 476.14941406250006], [52.55972696245733, 476.14941406250006]]}, {"title": "2 Method", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 514.25439453125], [105.8703071672355, 514.25439453125], [105.8703071672355, 527.28173828125], [38.29351535836177, 527.28173828125]]}, {"title": "2.1 Related Work", "heading_level": null, "page_id": 36, "polygon": [[38.29351535836177, 539.65771484375], [136.640625, 539.65771484375], [136.640625, 550.73095703125], [38.29351535836177, 550.73095703125]]}, {"title": "2.2 Target-Aware Encoder (TAE)", "heading_level": null, "page_id": 37, "polygon": [[51.80887372013652, 288.88134765625], [229.0102389078498, 288.85826771653547], [229.0102389078498, 299.30322265625], [51.80887372013652, 299.30322265625]]}, {"title": "2.3 Graph Convolution Network Using Fiedler Regularization", "heading_level": null, "page_id": 38, "polygon": [[38.29351535836177, 391.**********], [358.1569965870307, 391.**********], [358.1569965870307, 401.8935546875], [38.29351535836177, 401.8935546875]]}, {"title": "Algorithm 1. The step of FRGCN", "heading_level": null, "page_id": 39, "polygon": [[53.310580204778155, 127.42370605468751], [210.9765625, 127.42370605468751], [210.9765625, 136.55118110236222], [53.310580204778155, 136.55118110236222]]}, {"title": "2.4 Skeleton-Aware Encoder (SAE)", "heading_level": null, "page_id": 40, "polygon": [[38.29351535836177, 210.71728515625], [223.4375, 210.71728515625], [223.4375, 220.8134765625], [38.29351535836177, 221.33295838020248]]}, {"title": "2.5 Loss Function", "heading_level": null, "page_id": 40, "polygon": [[38.91357421875, 500.43757030371205], [135.244140625, 500.43757030371205], [135.244140625, 510.67187500000006], [38.91357421875, 510.67187500000006]]}, {"title": "3 Experiment", "heading_level": null, "page_id": 41, "polygon": [[52.55972696245733, 205.343505859375], [142.66211604095562, 205.343505859375], [142.66211604095562, 216.742431640625], [52.55972696245733, 216.742431640625]]}, {"title": "3.1 Datasets and Evaluation", "heading_level": null, "page_id": 41, "polygon": [[52.55972696245733, 284.3565804274466], [202.73037542662115, 284.3565804274466], [202.73037542662115, 294.************], [52.55972696245733, 294.************]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 43, "polygon": [[52.55972696245733, 117.08325195312501], [139.43359375, 117.08325195312501], [139.43359375, 130.110595703125], [52.55972696245733, 130.110595703125]]}, {"title": "References", "heading_level": null, "page_id": 43, "polygon": [[52.55972696245733, 298.**********], [117.13310580204778, 298.**********], [117.13310580204778, 312.33056640625], [52.55972696245733, 312.33056640625]]}, {"title": "LGA: A Language Guide Adapter\nfor Advancing the SAM Model's\nCapabilities in Medical Image\nSegmentation", "heading_level": null, "page_id": 46, "polygon": [[93.10580204778157, 51.8651123046875], [332.578125, 51.8651123046875], [332.578125, 120.09582519531251], [93.10580204778157, 120.09582519531251]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 47, "polygon": [[52.55972696245733, 54.77052868391451], [148.671875, 54.77052868391451], [148.671875, 66.23590087890625], [52.55972696245733, 66.23590087890625]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 48, "polygon": [[38.29351535836177, 391.**********], [137.4061433447099, 391.**********], [137.4061433447099, 405.**********], [38.29351535836177, 405.**********]]}, {"title": "2.1 LGA Framework", "heading_level": null, "page_id": 49, "polygon": [[52.55972696245733, 55.52080989876266], [164.43686006825936, 55.52080989876266], [164.43686006825936, 66.07305908203125], [52.55972696245733, 66.07305908203125]]}, {"title": "2.2 FFM Structure", "heading_level": null, "page_id": 49, "polygon": [[52.55972696245733, 448.6681664791901], [156.92832764505118, 448.6681664791901], [156.92832764505118, 458.56250000000006], [52.55972696245733, 458.56250000000006]]}, {"title": "2.3 Training Process", "heading_level": null, "page_id": 50, "polygon": [[38.29351535836177, 210.82902137232847], [149.74609375, 210.82902137232847], [149.74609375, 220.976318359375], [38.29351535836177, 220.976318359375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 50, "polygon": [[38.29351535836177, 436.6636670416198], [134.40273037542661, 436.6636670416198], [134.40273037542661, 447.16357421875], [38.29351535836177, 447.16357421875]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 50, "polygon": [[38.29351535836177, 461.81933593750006], [109.62457337883959, 461.81933593750006], [109.62457337883959, 471.58984375000006], [38.29351535836177, 471.58984375000006]]}, {"title": "3.2 Implementations", "heading_level": null, "page_id": 51, "polygon": [[52.55972696245733, 54.51129150390625], [164.43686006825936, 54.51129150390625], [164.43686006825936, 66.07305908203125], [52.55972696245733, 66.07305908203125]]}, {"title": "3.3 Quantitative Results", "heading_level": null, "page_id": 51, "polygon": [[52.55972696245733, 284.158935546875], [184.228515625, 284.158935546875], [184.228515625, 295.557861328125], [52.55972696245733, 295.557861328125]]}, {"title": "3.4 Model Results Visualization", "heading_level": null, "page_id": 51, "polygon": [[52.55972696245733, 477.77783203125006], [221.71875, 477.77783203125006], [221.71875, 489.50244140625006], [52.55972696245733, 489.50244140625006]]}, {"title": "3.5 Ablation Study", "heading_level": null, "page_id": 52, "polygon": [[38.29351535836177, 287.741455078125], [142.87109375, 287.741455078125], [142.87109375, 298.489013671875], [38.29351535836177, 298.489013671875]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 54, "polygon": [[38.29351535836177, 53.04571533203125], [125.25390624999999, 53.04571533203125], [125.25390624999999, 66.31732177734375], [38.29351535836177, 66.31732177734375]]}, {"title": "References", "heading_level": null, "page_id": 54, "polygon": [[38.29351535836177, 367.04541015625], [103.447265625, 367.04541015625], [103.447265625, 380.72412109375], [38.29351535836177, 380.72412109375]]}, {"title": "M4oE: A Foundation Model for Medical\nMultimodal Image Segmentation\nwith Mixture of Experts", "heading_level": null, "page_id": 57, "polygon": [[86.044921875, 51.76940382452194], [368.671875, 52.5164794921875], [368.671875, 102.9974365234375], [86.044921875, 102.9974365234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 57, "polygon": [[52.55972696245733, 518.48828125], [149.638671875, 518.48828125], [149.638671875, 532.818359375], [52.55972696245733, 532.818359375]]}, {"title": "2 Methods", "heading_level": null, "page_id": 59, "polygon": [[51.80887372013652, 144.603515625], [126.32812499999999, 144.603515625], [126.32812499999999, 157.95654296875], [51.80887372013652, 157.95654296875]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 62, "polygon": [[38.29351535836177, 53.53424072265625], [135.029296875, 53.53424072265625], [135.029296875, 66.39874267578125], [38.29351535836177, 66.39874267578125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 65, "polygon": [[52.55972696245733, 239.33970753655794], [138.90784982935153, 239.33970753655794], [138.90784982935153, 251.91625976562503], [52.55972696245733, 251.91625976562503]]}, {"title": "References", "heading_level": null, "page_id": 65, "polygon": [[51.80887372013652, 438.04443359375], [117.13310580204778, 438.04443359375], [117.13310580204778, 450.42041015625], [51.80887372013652, 450.42041015625]]}, {"title": "Mammo-CLIP: A Vision Language\nFoundation Model to Enhance Data\nEfficiency and Robustness\nin Mammography", "heading_level": null, "page_id": 68, "polygon": [[83.359375, 51.58013916015625], [340.3125, 51.58013916015625], [340.3125, 120.99145507812501], [83.359375, 120.99145507812501]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 68, "polygon": [[39.04436860068259, 464.42407199100114], [134.70703125, 464.42407199100114], [134.70703125, 477.12646484375006], [39.04436860068259, 477.12646484375006]]}, {"title": "2 Method", "heading_level": null, "page_id": 69, "polygon": [[52.55972696245733, 556.9189453125], [120.13651877133105, 556.9189453125], [120.13651877133105, 568.6435546875], [52.55972696245733, 568.6435546875]]}, {"title": "2.1 Mammo-CLIP", "heading_level": null, "page_id": 70, "polygon": [[38.29351535836177, 461.16796875000006], [138.1569965870307, 461.16796875000006], [138.1569965870307, 471.58984375000006], [38.29351535836177, 471.58984375000006]]}, {"title": "2.2 Instance and Dataset Augmentation", "heading_level": null, "page_id": 71, "polygon": [[52.55972696245733, 381.701171875], [262.79863481228665, 381.701171875], [262.79863481228665, 392.123046875], [52.55972696245733, 392.123046875]]}, {"title": "2.3 Mammo-FActOR", "heading_level": null, "page_id": 72, "polygon": [[39.04436860068259, 178.80029296875], [152.5390625, 178.80029296875], [152.5390625, 190.19921875], [39.04436860068259, 190.19921875]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 72, "polygon": [[38.29351535836177, 462.79638671875006], [134.40273037542661, 462.79638671875006], [134.40273037542661, 475.17236328125006], [38.29351535836177, 475.17236328125006]]}, {"title": "3.1 Datasets", "heading_level": null, "page_id": 72, "polygon": [[39.04436860068259, 561.80419921875], [108.87372013651877, 561.80419921875], [108.87372013651877, 573.52880859375], [39.04436860068259, 573.52880859375]]}, {"title": "3.2 Experimental Details", "heading_level": null, "page_id": 73, "polygon": [[52.55972696245733, 367.69677734375], [187.55859375, 367.69677734375], [187.55859375, 378.77001953125], [52.55972696245733, 378.77001953125]]}, {"title": "4 Results", "heading_level": null, "page_id": 75, "polygon": [[52.55972696245733, 229.58605174353207], [117.13310580204778, 229.58605174353207], [117.13310580204778, 240.84301757812503], [52.55972696245733, 240.84301757812503]]}, {"title": "Classification (RQ1 & RQ2).", "heading_level": null, "page_id": 75, "polygon": [[52.55972696245733, 253.59505061867267], [197.4744027303754, 253.59505061867267], [197.4744027303754, 263.15234375], [52.55972696245733, 263.15234375]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 76, "polygon": [[38.29351535836177, 228.30419921875003], [125.14648437499999, 228.30419921875003], [125.14648437499999, 241.98291015625003], [38.29351535836177, 241.98291015625003]]}, {"title": "References", "heading_level": null, "page_id": 76, "polygon": [[38.29351535836177, 410.68701171875], [103.3935546875, 410.68701171875], [103.3935546875, 425.01708984375], [38.29351535836177, 425.01708984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 43], ["Text", 8], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 68], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 815, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 42], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 101], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 467], ["Line", 41], ["TextInlineMath", 5], ["Equation", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 36], ["TextInlineMath", 5], ["Equation", 5], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["TableCell", 130], ["Line", 38], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11653, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["TableCell", 54], ["Line", 52], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 38], ["Text", 6], ["ListItem", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 50], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 41], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 40], ["Text", 7], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 46], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["TableCell", 36], ["Line", 25], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 76], ["Text", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 57], ["TextInlineMath", 7], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["Line", 62], ["Reference", 4], ["TextInlineMath", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["TableCell", 190], ["Line", 41], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2494, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["TableCell", 170], ["Line", 67], ["Caption", 5], ["Table", 4], ["TableGroup", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 13567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 49], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 51], ["ListItem", 18], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 10], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 40], ["Text", 7], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 617, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 37], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 653, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 42], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 64], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 831, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 43], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 207], ["Span", 204], ["Line", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 13073, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 299], ["Span", 292], ["Line", 47], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2947, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["TableCell", 68], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 41], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 51], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 24], ["Line", 13], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 37], ["Text", 6], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 42], ["Text", 5], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 42], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 58], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 47], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 41], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 41], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1104, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 897], ["TableCell", 426], ["Line", 47], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 44], ["ListItem", 9], ["Reference", 9], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 52], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 28], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 39], ["Text", 11], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 568, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 40], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 81], ["Reference", 3], ["SectionHeader", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1392, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 53], ["Equation", 6], ["Reference", 6], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 40], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["TableCell", 89], ["Line", 41], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["TableCell", 78], ["Line", 57], ["Reference", 4], ["Table", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 3, "llm_tokens_used": 2520, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 42], ["ListItem", 6], ["Reference", 6], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 52], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 40], ["Line", 15], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 40], ["Text", 5], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 584, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 48], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 649, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 30], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 102], ["Text", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 41], ["TableCell", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 21], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["TableCell", 195], ["Line", 40], ["Table", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 3, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["TableCell", 117], ["Line", 43], ["Reference", 6], ["ListItem", 5], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 52], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 11], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 39], ["Text", 8], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 44], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 67], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 577], ["TableCell", 118], ["Line", 45], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Table", 1], ["Equation", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 56], ["TableCell", 48], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Equation", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["TableCell", 57], ["Line", 42], ["Reference", 3], ["Text", 2], ["TextInlineMath", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 50], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 40], ["TableCell", 19], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 40], ["Text", 4], ["ListItem", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 51], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 23], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Med Leveraging Generative Model for Healthcare Data Sharing-pages-6"}