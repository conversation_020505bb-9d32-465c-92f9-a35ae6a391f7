Image /page/0/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a gray circle with a darker gray ribbon shape inside, resembling a bookmark. The text below the icon reads "Check for updates" in a gray sans-serif font.

# **fTSPL: Enhancing Brain Analysis with FMRI-Text Synergistic Prompt Learning**

<PERSON><PERSON><PERSON><sup>1</sup> $\odot$ [,](http://orcid.org/0000-0001-8273-9527) <PERSON><PERSON><PERSON><sup>2</sup> $\odot$ , <PERSON><PERSON><PERSON><sup>3</sup> $\odot$ , <PERSON><PERSON><PERSON><sup>1</sup> $\odot$ , and <PERSON><PERSON><PERSON><sup>1( $\boxtimes$ [\)](http://orcid.org/0000-0002-0853-6948)</sup>

<sup>1</sup> Department of Electronic Engineering, The Chinese University of Hong Kong, Hong Kong SAR, China

<EMAIL>

 $2$  School of Computer Science and Technology, Beijing Jiaotong University, Beijing, China

<sup>3</sup> School of Automation, Northwestern Polytechnical University, Xi'an, China

**Abstract.** Using functional Magnetic Resonance Imaging (fMRI) to construct the functional connectivity is a well-established paradigm for deep learning-based brain analysis. Recently, benefiting from the remarkable effectiveness and generalization brought by large-scale multi-modal pre-training data, Vision-Language (V-L) models have achieved excellent performance in numerous medical tasks. However, applying the pretrained V-L model to brain analysis presents two significant challenges: (1) The lack of paired fMRI-text data; (2) The construction of functional connectivity from multi-modal data. To tackle these challenges, we propose a fMRI-Text Synergistic Prompt Learning (fTSPL) pipeline, which utilizes the pre-trained V-L model to enhance brain analysis for the first time. In fTSPL, we first propose an Activation-driven Brainregion Text Generation (ABTG) scheme that can automatically generate instance-level texts describing each fMRI, and then leverage the V-L model to learn multi-modal fMRI and text representations. We also propose a Prompt-boosted Multi-modal Functional Connectivity Construction (PMFCC) scheme by establishing the correlations between fMRItext representations and brain-region embeddings. This scheme serves as a plug-and-play preliminary that can connect with various Graph Neural Networks (GNNs) for brain analysis. Experiments on ABIDE and HCP datasets demonstrate that our pipeline outperforms state-of-theart methods on brain classification and prediction tasks. The code is available at [https://github.com/CUHK-AIM-Group/fTSPL.](https://github.com/CUHK-AIM-Group/fTSPL)

**Keywords:** Prompt learning · Vision-language model · Multi-modal functional connectivity · Brain analysis

# **1 Introduction**

At the neuroscience fronts, brain functional Magnetic Resonance Imaging (fMRI) [\[16](#page-9-0)] is a key technology for revealing human behaviors and cognitions. Concretely,

Image /page/1/Figure/1 description: The image displays a comparative flowchart illustrating two methods for brain analysis using fMRI data. The top section, labeled '(a) Existing Methods', shows fMRI data being processed into functional connectivity, which is then fed into a Graph Neural Network for brain analysis. The bottom section, labeled '(b) The Proposed fTSPL', shows fMRI data combined with 'Brain-region Text' (e.g., 'A {strong} brain activity of {left amygdala}...') and 'Multi-modal Prompts' as input to a 'Pre-trained V-L Model'. The output of this model, along with 'Multi-modal Functional Connectivity', is then processed by a Graph Neural Network for brain analysis. Both methods conclude with a 'Brain Analysis' stage, depicted by a stylized head with a brain inside, accompanied by icons representing mental health states.

<span id="page-1-0"></span>**Fig. 1.** Illustration of the proposed fTSPL pipeline. (a) Existing methods directly produce the functional connectivity and then use GNNs for brain analysis. (b) Our fTSPL utilizes the pre-trained V-L model with multi-modal prompts to construct the multimodal functional connectivity, thereby enhancing GNN-based brain analysis.

fMRI produces functional connectivity [\[5](#page-9-1),[20\]](#page-10-0) that describing the communication and collaboration patterns between brain regions in different behaviors and cognitions. Therefore, many studies [\[4,](#page-9-2)[21\]](#page-10-1) focus on analyzing functional connectivities for multiple brain disease classification and cognitive prediction tasks.

With the interdisciplinarity between artificial intelligence and neuroscience, deep learning methods are widely applied to assist in brain analysis  $[6,12,15,25,$  $[6,12,15,25,$  $[6,12,15,25,$  $[6,12,15,25,$  $[6,12,15,25,$ 29. As shown in Fig.  $1(a)$  $1(a)$ , a well-established paradigm is constructing graphstructured functional connectivity, followed by connecting a Graph Neural Network (GNN) to classify brain diseases or predict brain cognitions. However, most existing GNNs are tailored for uni-modal fMRI data, which limits their effectiveness since recent works [\[13](#page-9-6),[27,](#page-10-4)[30](#page-10-5)] have shown that incorporating text modality can provide additional supervision to improve performance. Currently, the pre-trained Vision-Language (V-L) models [\[10](#page-9-7)[,18\]](#page-10-6) have attracted extensive attention as they utilize a self-supervised manner to learn numerous generic and effective multi-modal representations from large-scale pre-training data. In the medical field, V-L models have also been explored in various tasks and have yielded promising results [\[24](#page-10-7)[,26](#page-10-8)[,28](#page-10-9)]. Inspired by this, we aim to introduce the pre-trained V-L model for constructing multi-modal functional connectivity, thus improving the performance of multiple brain analysis tasks. To be noted, we represent the first effort to leverage the V-L model for multi-modal brain analysis.

Nevertheless, there are two major challenges to applying the V-L model for multi-modal brain analysis: (1) Current fMRI data lacks the corresponding texts. Meaningful texts describing brain-region connectivities and activities could provide the extra text-modal supervision to learn more effective fMRI representations. Therefore, it is highly demanded for generating instance-level fMRI-text data; (2) Existing functional connectivity construction methods only consider uni-modal fMRI data. We aim to further explore the relations between high-level fMRI, text, and brain-region features to construct the multi-modal functional connectivity and improve the performance of brain analysis.

Aiming to address the above challenges, we propose a fMRI-Text Synergistic Prompt Learning (fTSPL) pipeline for multi-modal brain analysis, which comprises two main components: (1) Activation-driven Brain-region Text Generation (ABTG); (2) Prompt-boosted Multi-modal Functional Connectivity Construction (PMFCC). In ABTG, we screen the activated brain regions according to fMRI intensities, and quantify the activation degree of brain regions using functional connectivity. This scheme allows us to obtain the text description of each fMRI and enables the use of the V-L model. In PMFCC, we tune the pre-trained V-L model via multi-modal prompts to produce fMRI and text representations, and then construct the multi-modal functional connectivity by establishing the correlations between fMRI-text representations and brain-region embeddings. Experimental results demonstrate that the proposed pipeline achieves excellent performance on multiple brain analysis tasks. The main contributions are as follows: (1) We propose a novel prompt learning paradigm fTSPL. To the best of our knowledge, this is the first application of the V-L model for multi-modal brain analysis; (2) We propose ABTG to provide instance-level text descriptions for fMRI, which is suitable for fMRI with different brain atlas; (3) We propose PMFCC to construct the multi-modal functional connectivity, it is a plug-andplay preliminary for GNN-based brain analysis. Experiments on brain disease classification and cognitive prediction verified the effectiveness of PMFCC.

# **2 Method**

In Fig. [2,](#page-3-0) we display the overall architecture of the proposed fTSPL pipeline. Firstly, given the pre-processed fMRI time-series  $\{X_n\}_{n=1}^N$ , we use the ABTG scheme to generate fMRI's text descriptions  ${Y_n}_{n=1}^N$  according to the connectivity and activity of brain regions. The fMRI time-series and brain-region text  $\{X_n, Y_n\}$  are denoted as the multi-modal input for BiomedCLIP [\[24](#page-10-7)] text and image encoders. Afterward, we design learnable multi-layer text and image prompts  ${P_k^T}_{k=1}^K$  and  ${P_k^I}_{k=1}^K$ , where K is the prompt depth. The text and image encoders  $E_T$  and  $E_I$  are kept frozen, while only multi-modal prompts are optimized to produce fMRI and text representations  $x$  and  $y$ . Next, we propose the PMFCC scheme to enhance the original functional connectivity by supplementing the fMRI and text-modal connectome information. The constructed multi-modal functional connectivity  $F_m$  can connect different GNNs as adapters to improve multiple brain analysis tasks. During the training, the contrastive loss  $\mathcal{L}_{con}$  and task loss  $\mathcal{L}_{task}$  jointly achieve the optimization of our pipeline.

## **2.1 Activation-Driven Brain-Region Text Generation (ABTG)**

In fMRI time-series, each brain region corresponds to an independent medical terminology, and the connectivity and activity of brain regions are crucial for neuroscientists to achieve brain analysis. Motivated by this, we propose the ABTG scheme, which counts the activated brain regions of each fMRI as well as the corresponding activation degrees to provide instance-level brain-region texts.

Image /page/3/Figure/1 description: This is a flowchart illustrating a method for prompt-boosted multi-modal functional connectivity construction for brain analysis tasks. The process begins with original fMRI data and the AAL Atlas, which are used to generate fMRI time-series (X) and functional connectivity (F). The Activation-driven Brain-region Text Generation (ABTG) module takes fMRI time-series and the AAL Atlas terminology to generate brain-region text (Y), which includes activated brain regions and their activation degrees. The Prompt-boosted Multi-modal Functional Connectivity Construction (PMFCC) module takes image prompts and text prompts, along with the functional connectivity (F) and brain-region text (Y), to construct multi-modal functional connectivity (Fm). This Fm is then fed into a Graph Neural Network for brain analysis tasks such as disease classification and cognitive prediction.

<span id="page-3-0"></span>**Fig. 2.** Overview of the proposed fTSPL pipeline. fTSPL first generates the brainregion text for each fMRI time-series to form instance-level multi-modal data. Then, fTSPL tunes the pre-trained V-L model by optimizing multi-modal prompts to produce fMRI and text representations. Finally, fTSPL correlates the fMRI-text representations and brain-region embeddings to construct the multi-modal functional connectivity, and connects a learnable GNN adapter for enhancing brain analysis.

Firstly, we map original fMRI data into the grayordinate system [\[7\]](#page-9-8) to obtain vertices on the reconstructed cortical surface, and then perform the withinsubject and cross-subject registrations to establish the subject-level correspondence. Taking Automated Anatomical Labeling (AAL) [\[19\]](#page-10-10) as an example, we apply the pre-defined AAL atlas to cortical surfaces, which can produce 45 regions on left and right cerebral hemisphere and 26 regions on the cerebellum. At each fMRI timestamp, the vertices in each brain region are averaged to produce the fMRI time-series  $X \in \mathbb{R}^{M \times S}$ , where M is the number of brain regions, and S is the number of timestamps. To obtain the activation state and degree of each region, we first introduce an activation threshold  $H_a$ , which aims to extract the activated brain region in  $X$  at each timestamp.  $H_a$  is positively correlated with the maximum activity of fMRI time-series, i.e.,  $H_a = \lambda \cdot \max(X)$ . We define an activated brain region if its value exceeds  $H_a$  more than  $S/20$  times:

The *m*-th brain region is

$$
\begin{cases} \text{activated, if } \sum_{s=1}^{S} \mathbb{1}(X_{m,s} > H_a) \geq \frac{S}{20} \\ \text{non-activated, if } \sum_{s=1}^{S} \mathbb{1}(X_{m,s} > H_a) < \frac{S}{20} \end{cases}. \tag{1}
$$

Then, we define a correlation threshold H*<sup>c</sup>* to evaluate the activation degree of brain regions. Concretely, Pearson correlations between brain regions are computed to construct a  $116 \times 116$  functional connectivity. Here, we consider two regions to be correlated when their correlation value is greater than 0.5. On this basis, we further define the "strong", "moderate", or "weak" region as the one with  $M_1, M_2$ , or  $M_3$  correlated regions, where  $M_1 > 3H_c, H_c \leq M_2 \leq 3H_c$ , and  $M_3 < H_c$ . Finally, we describe the above brain regions to generate the instancelevel brain-region text. To be specific, the activated brain regions are matched with the corresponding brain atlas terminologies, and further combined with their activation degree words "strong", "moderate", and "weak".

## **2.2 Prompt-Boosted Multi-modal Functional Connectivity Construction (PMFCC)**

To enhance the original functional connectivity, we combine the pre-trained V-L model and multi-modal prompt learning to construct the multi-modal functional connectivity, which theoretically contributes to improve the performance of multiple brain analysis tasks.

**Multi-modal Text-Image Prompting:** In this work, multi-modal prompt learning is achieved by synergizing multi-layer text prompts  $\{P_k^T\}_{k=1}^K$  with image prompts  ${P_k^I}_{k=1}^K$ . Given a brain-region text  $Y_n$ , we first adopt a Tokenizer embedding layer to convert  $Y_n$  into the word embedding  $W_0 \in \mathbb{R}^{B \times C^T}$ , where  $B$  and  $C^T$  are the number and channels of word embeddings. Then, we take the current layer text prompt  $P_0^T \in \mathbb{R}^{D \times C^T}$ , where D is the prompt length, to combine with  $W_0$  as inputs for the text encoder  $E_T$ . For the j-th Transformer layer, we define the prompting process as:

$$
[W_j, \_] = E_{T,j}(W_{j-1}, P_j^T), \ \ j \le K,\tag{2}
$$

when  $j \leq K$ , we will discard the prompt output of the current layer, and add a new text prompt in the next Transformer layer for learning.

Finally, we introduce a linear layer to project the last word token  $w_J^B$  from  $W_J$  into a common latent space, obtaining the text representation y:

$$
y = \text{TextProj}(w_J^B), \ \ y \in \mathbb{R}^C. \tag{3}
$$

As the fMRI time-series  $X_n$  has been pre-processed according to AAL atlas, we remove the image patching step and add a linear layer to convert  $X_n \in$  $\mathbb{R}^{M \times S}$  into the image embedding  $V_0 \in \mathbb{R}^{M \times C^I}$ , where  $C^I$  is the channels of image embeddings. Meanwhile, we add the class token  $c_0$  and image prompt  $P_0^I \in \mathbb{R}^{D \times C^I}$  into  $V_0$  to feed the image encoder  $E_I$ :

$$
[c_j, V_j, \_] = E_{I,j}(c_{j-1}, V_{j-1}, P_j^I), \ \ j \le K. \tag{4}
$$

After image prompting, we extract the class token c*<sup>J</sup>* and project it into a common latent space using a linear layer to obtain the fMRI representation  $x$ :

$$
x = \text{ImageProj}(c_J), \ \ x \in \mathbb{R}^C. \tag{5}
$$

**Multi-modal Functional Connectivity:** Given the fMRI representation  $x \in \mathbb{R}$  $\mathbb{R}^C$ , text representation  $y \in \mathbb{R}^C$ , and brain-region embeddings  $V_J \in \mathbb{R}^{M \times C^I}$ . Since  $x$ ,  $y$ , and  $H_J$  have different dimensions, we first use a linear layer to project  $V_J$  as  $\bar{V}_J \in \mathbb{R}^{M \times C}$ . Then, we compute Pearson correlations between x and  $V_J$  to obtain the image-modal connectome supplement  $F_I$ , which provides the correlations between the global fMRI feature and local brain-region features:

$$
F_I = \text{Pearson}\{x, \bar{V}_J\}.
$$
\n<sup>(6)</sup>

Similarly, we compute the text-modal connectome supplement  $F<sub>T</sub>$  to provide the text-brain region correlations for functional connectivity:

$$
F_T = \text{Pearson}\{y, \bar{V}_J\}.\tag{7}
$$

Finally, we concatenate  $F_I$  and  $F_T$  with the original functional connectivity F at the node dimension to obtain the multi-modal functional connectivity  $F_m$ :

$$
F_m = \text{Concat}[F, F_I, F_T].\tag{8}
$$

For existing GNN methods, the multi-modal functional connectivity  $F_m$  can replace F as their inputs. Due to only two extra nodes, F*<sup>m</sup>* has almost no increasing computations. In this work, we use a standard GNN as an adapter to process F*<sup>m</sup>* for brain analysis, which consists of two graph convolutional layers and a linear layer. Specifically, we first construct a graph  $\mathcal{G} = \{\mathcal{V}, \mathcal{E}, \mathcal{A}\}\,$  where  $\mathcal{V}$  denotes the graph nodes, is  $F_m$ . The edge  $\mathcal E$  and adjacency matrix  $\mathcal A$  encode the correlations between nodes. The predicted result is obtained by:

$$
\mathcal{H}^{(1)} = \sigma(\tilde{\mathcal{D}}^{-\frac{1}{2}}\tilde{\mathcal{A}}\tilde{\mathcal{D}}^{\frac{1}{2}}\mathcal{V}\mathcal{W}^{(1)}), \quad \mathcal{H}^{(2)} = \text{Proj}(\sigma(\tilde{\mathcal{D}}^{-\frac{1}{2}}\tilde{\mathcal{A}}\tilde{\mathcal{D}}^{\frac{1}{2}}\mathcal{H}^{(1)}\mathcal{W}^{(2)})),\tag{9}
$$

where  $\mathcal{A} = \mathcal{A} + I$ , I is the identity matrix, D is the degree matrix, W is the graph convolution weights, and  $\sigma$  is the activation function.

### **2.3 Model Optimization**

In this work, we adopt the contrastive loss  $\mathcal{L}_{con}$  and task loss  $\mathcal{L}_{task}$  to jointly optimize the proposed fTSPL pipeline. The former aligns fMRI and text representations  $x$  and  $y$ , and the latter can be the cross-entropy loss for disease classification, or the L2 loss for cognitive prediction. The total loss function is:

$$
\mathcal{L} = \alpha \mathcal{L}_{con} + \beta \mathcal{L}_{task},\tag{10}
$$

where  $\alpha$  and  $\beta$  are the loss weights. It is worth noting that only the multi-modal prompts, two linear layers, and GNN are optimized in the training.

| Classification Methods     | Accuracy                            | <b>AUROC</b>                        | Sensitivity                         | Specificity                         |
|----------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|
| BrainGNN (MedIA21) [14]    | $62.7 \pm 3.7$                      | $59.6 \pm 2.5$                      | $56.8 \pm 20.7$                     | $70.2 \pm 19.3$                     |
| BrainGB (TMI22) [2]        | $69.4 \pm 3.4$                      | $63.2 \pm 2.0$                      | $63.5 \pm 8.6$                      | $60.7 \pm 10.4$                     |
| BNT (NeurIPS22) [11]       | $71.0 \pm 1.2$                      | $80.2 \pm 1.0$                      | $72.5 \pm 5.2$                      | $69.3 \pm 6.5$                      |
| Com-BrainTF (MICCAI23) [1] | $72.5 \pm 4.4$                      | $79.6 \pm 3.8$                      | $80.1 \pm 5.8$                      | $65.7 \pm 6.4$                      |
| Ours                       | <b><math>75.4 \pm 2.7</math></b>    | <b><math>82.5 \pm 2.4</math></b>    | <b><math>81.9 \pm 4.1</math></b>    | <b><math>74.1 \pm 4.9</math></b>    |
| Prediction Methods         | MAE                                 | <b>MSE</b>                          | <b>PCC</b>                          | $R^2$                               |
| BrainGNN (MedIA21) [14]    | $0.170 \pm 0.004$                   | $0.047 \pm 0.145$                   | $0.195 \pm 0.006$                   | $0.040 \pm 0.004$                   |
| BrainGB (TMI22) [2]        | $0.168 \pm 0.003$                   | $0.044 \pm 0.202$                   | $0.223 \pm 0.004$                   | $0.045 \pm 0.007$                   |
| RegGNN (BIB22) [8]         | $0.164 \pm 0.005$                   | $0.040 \pm 0.124$                   | $0.280 \pm 0.002$                   | $0.057 \pm 0.005$                   |
| Meta-RegGNN (PRIME22) [9]  | $0.161 \pm 0.004$                   | $0.038 \pm 0.168$                   | $0.304 \pm 0.003$                   | $0.066 \pm 0.004$                   |
| Ours                       | <b><math>0.156 \pm 0.003</math></b> | <b><math>0.035 \pm 0.157</math></b> | <b><math>0.369 \pm 0.004</math></b> | <b><math>0.101 \pm 0.003</math></b> |

<span id="page-6-0"></span>**Table 1.** Quantitative results of the proposed pipeline and state-of-the-art methods on ABIDE and HCP datasets. The best performance is highlighted in boldface.

# **3 Experiments**

## **3.1 Experimental Setup**

**Datasets:** We evaluate the effectiveness of the proposed fTSPL pipeline on the ABIDE [\[3\]](#page-8-2) and HCP [\[23](#page-10-11)] datasets. The former corresponds to autism classification, and the latter involves cognitive prediction.

**ABIDE Dataset:** This dataset contains 1035 subjects' fMRI data. According to AAL atlas, we produce fMRI time-series and functional connectivities, which have two classes: *autism* or *non-autism*. To improve reliability, we evenly split this dataset into five subsets for 5-fold cross-validation. Each fold uses 1 subset for test and the other 4 for training, each subset containing 207 subjects.

**HCP Dataset:** This dataset collects 870 subjects' fMRI data, we also perform AAL atlas to produce fMRI time-series and functional connectivities. Then, we choose a representative task that predicting the cognitive score of "ReadEng". We define 174 subjects as a subset to achieve 5-fold cross-validation.

**Evaluations:** For classification tasks, we adopt the Accuracy, AUROC, Sensitivity, and Specificity as evaluation metrics. The higher the values of these metrics, the better the performance. To further quantify prediction performance, we introduce the Mean Absolute Error (MAE), Mean Squared Error (MSE), Pearson Correlation Coefficient (PCC), and R-squared  $(R<sup>2</sup>)$  as evaluation metrics. The lower MAE and MSE values, and the higher PCC and  $\mathbb{R}^2$  values indicate competitive results. After that, we compare the proposed fTSPL pipeline with some state-of-the-art brain analysis methods, including BrainGNN [\[14](#page-9-9)], BrainGB [\[2](#page-8-0)], BNT [\[11\]](#page-9-10), Com-BrainTF [\[1](#page-8-1)], RegGNN [\[8\]](#page-9-12), and Meta-RegGNN [\[9](#page-9-11)].

Image /page/7/Figure/1 description: The image displays two side-by-side visualizations of brain activity, each accompanied by a text box listing activated brain regions. The left side shows a brain scan with areas highlighted in various colors, predominantly blue and yellow, with some red patches indicating stronger activity. The accompanying text box lists specific brain regions and the intensity of their activity, such as 'A {strong} brain activity of {left cuneus}' and 'A {moderate} brain activity of {left lingual gyrus}'. The right side presents a similar brain scan, also with colorful highlights, and a corresponding text box detailing activated regions like 'A {strong} brain activity of {left angular gyrus}' and 'A {moderate} brain activity of {left middle temporal gyrus}'. Both visualizations are labeled 'Activated Brain Regions in fMRI' below the brain scans and 'Brain-region Text' below the text boxes.

<span id="page-7-1"></span>**Fig. 3.** Illustration of the activated brain regions and corresponding brain-region texts.

**Table 2.** Ablation study and hyperparameters analysis on ABIDE and HCP datasets.

<span id="page-7-0"></span>

| Variants                              | Accuracy            | PCC                    |
|---------------------------------------|---------------------|------------------------|
| w/o Text-modal Connectome Supplement  | 73.9 $±$ 2.8        | 0.348 $±$ 0.005        |
| w/o Image-modal Connectome Supplement | 72.4 $±$ 2.5        | 0.322 $±$ 0.006        |
| w/o Multi-modal Connectome Supplement | 70.5 $±$ 3.2        | 0.306 $±$ 0.004        |
| Prompt Depth = 2, Prompts Length = 2  | 73.6 $±$ 3.1        | 0.345 $±$ 0.005        |
| Prompt Depth = 4, Prompts Length = 2  | <b>75.4</b> $±$ 2.7 | <b>0.369</b> $±$ 0.004 |
| Prompt Depth = 8, Prompts Length = 2  | 74.2 $±$ 3.0        | 0.364 $±$ 0.004        |
| Prompt Depth = 4, Prompts Length = 4  | 73.8 $±$ 2.5        | 0.350 $±$ 0.005        |
| Prompt Depth = 4, Prompts Length = 8  | 72.9 $±$ 2.9        | 0.332 $±$ 0.003        |

**Implementation Details:** Our pipeline is implemented by PyTorch 1.18.0 [\[17\]](#page-9-13) and NVIDIA 4090 GPU. For classification tasks, we use the SGD optimizer [\[22\]](#page-10-12) with  $5 \times 10^{-4}$  learning rate,  $1 \times 10^{-4}$  weight decay, 16 batch size, and 50 epochs. The loss weights are  $\alpha$ =0.5 and  $\beta$ =0.5. For prediction tasks, we use the SGD optimizer with  $1 \times 10^{-3}$  learning rate,  $1 \times 10^{-4}$  weight decay, 32 batch size, and 50 epochs. The loss weights are  $\alpha$ =0.2 and  $\beta$ =0.8. For multi-modal prompts, we set the depth  $K=4$  and the length  $D=2$ . In addition, we set  $\lambda=0.6$  in  $H_a$ .

### **3.2 Experimental Results**

**Comparison with State-of-the-Arts Methods:** The quantitative results are illustrated in Table [1.](#page-6-0) We find that the proposed fTSPL achieves 75.4% Accuracy, 82.5% AUROC, 81.9% Sensitivity, and 74.1% Specificity, as well as 0.156 MAE, 0.035 MSE, 0.369 PCC, and 0.101  $\mathbb{R}^2$ , which show that our pipeline outperforms existing GNN methods by a significant margin. Compared with advanced Transformer methods, our pipeline not only has lower training costs, but also achieves slightly better performance of 2.3% and 2.9% AUROC improvements.

**Ablation Study:** Next, we conduct ablation studies to verify the effectiveness of PMFCC. As shown in Table [2,](#page-7-0) we ablate text-modal, image-modal, and multi-modal connectome supplements in the multi-modal functional connectivity, respectively. We observe that removing the multi-modal connectome supplement reduces performance by 4.9% in Accuracy and 0.063 in PCC. Moreover, image-modal connectome supplement is more important than that of the textmodal, as it brings performance improvements of 3.4% Accuracy and 0.038 PCC. These results demonstrate that PMFCC is effective to improve brain analysis tasks.

**Hyperparameters Analysis:** In Table [2,](#page-7-0) we also analyze hyperparameters, including the depth and length of multi-modal prompts. It can be seen that the highest performance of 75.4% Accuracy and 0.369 PCC is achieved when the prompt depth and length are 4 and 2. Furthermore, insufficient prompt depth leads to a significant performance decline. In contrast, too large prompt length fails to improve performance, and even obtaining relatively poor results.

**Text Generation:** Figure [3](#page-7-1) visualizes the activated brain regions and corresponding brain-region texts of two patients' fMRI. These results illustrate that the proposed ABTG can generate accurate text descriptions for different fMRI.

# **4 Conclusion**

In this paper, we propose a novel pipeline fTSPL for enhancing brain analysis. Concretely, fTSPL comprehensively considers the connectivity and activity of brain regions to generate instance-level texts to describe fMRI, and leverages the pre-trained V-L model and multi-modal prompts to construct the multi-modal functional connectivity. Experiments demonstrate that the proposed fTSPL pipeline achieves promising performance on multiple brain analysis tasks.

**Acknowledgment.** This work was supported by Hong Kong Research Grants Council (RGC) General Research Fund 14204321.

**Disclosure of Interests.** The authors have no competing interests to declare that are relevant to the content of this article.

# **References**

- <span id="page-8-1"></span>1. Bannadabhavi, A., Lee, S., Deng, W., Ying, R., Li, X.: Community-aware transformer for autism prediction in fMRI connectome. In: Proc. Medical Image Computing and Computer-Assisted Intervention (MICCAI). pp. 287–297 (2023)
- <span id="page-8-0"></span>2. Cui, H., Dai, W., Zhu, Y., Kan, X., Gu, A.A.C., Lukemire, J., Zhan, L., He, L., Guo, Y., Yang, C.: BrainGB: A benchmark for brain network analysis with graph neural networks. IEEE Trans. Med. Imag. **42**(2), 493–506 (2022)
- <span id="page-8-2"></span>3. Di Martino, A., Yan, C.G., Li, Q., Denio, E., Castellanos, F.X., Alaerts, K., Anderson, J.S., Assaf, M., Bookheimer, S.Y., Dapretto, M., et al.: The autism brain imaging data exchange: Towards a large-scale evaluation of the intrinsic brain architecture in autism. Mol. Psychiatr. **19**(6), 659–667 (2014)

- <span id="page-9-2"></span>4. Farahani, F.V., Karwowski, W., Lighthall, N.R.: Application of graph theory for identifying connectivity patterns in human brain networks: A systematic review. Front. Neurosci. **13**, 585 (2019)
- <span id="page-9-1"></span>5. Finn, E.S., Shen, X., Scheinost, D., Rosenberg, M.D., Huang, J., Chun, M.M., Papademetris, X., Constable, R.T.: Functional connectome fingerprinting: identifying individuals using patterns of brain connectivity. Nature Neurosci. **18**(11), 1664–1671 (2015)
- <span id="page-9-3"></span>6. Gao, J., Zhao, L., Zhong, T., Li, C., He, Z., Wei, Y., Zhang, S., Guo, L., Liu, T., Han, J., et al.: Prediction of cognitive scores by joint use of movie-watching fMRI connectivity and eye tracking via Attention-CensNet. In: Proc. Medical Image Computing and Computer-Assisted Intervention (MICCAI). pp. 287–296 (2023)
- <span id="page-9-8"></span>7. Glasser, M.F., Sotiropoulos, S.N., Wilson, J.A., Coalson, T.S., Fischl, B., Andersson, J.L., Xu, J., Jbabdi, S., Webster, M., Polimeni, J.R., et al.: The minimal preprocessing pipelines for the human connectome project. Neuroimage **80**, 105– 124 (2013)
- <span id="page-9-12"></span>8. Hanik, M., Demirtas, M.A., Gharsallaoui, M.A., Rekik, I.: Predicting cognitive scores with graph neural networks through sample selection learning. Brain Imag. Behav. **16**(3), 1123–1138 (2022)
- <span id="page-9-11"></span>9. Jegham, I., Rekik, I.: Meta-RegGNN: Predicting verbal and full-scale intelligence scores using graph neural networks and meta-learning. In: Proc. PRedictive Intelligence In MEdicine (PRIME). pp. 203–211 (2022)
- <span id="page-9-7"></span>10. Jia, C., Yang, Y., Xia, Y., Chen, Y.T., Parekh, Z., Pham, H., Le, Q., Sung, Y.H., Li, Z., Duerig, T.: Scaling up visual and vision-language representation learning with noisy text supervision. In: Proc. International Conference on Machine Learning (ICML). pp. 4904–4916 (2021)
- <span id="page-9-10"></span>11. Kan, X., Dai, W., Cui, H., Zhang, Z., Guo, Y., Yang, C.: Brain network transformer. In: Proc. Neural Information Processing Systems (NeurIPS). vol. 35, pp. 25586–25599 (2022)
- <span id="page-9-4"></span>12. Kawahara, J., Brown, C.J., Miller, S.P., Booth, B.G., Chau, V., Grunau, R.E., Zwicker, J.G., Hamarneh, G.: BrainNetCNN: Convolutional neural networks for brain networks; towards predicting neurodevelopment. NeuroImage **146**, 1038– 1049 (2017)
- <span id="page-9-6"></span>13. Khattak, M.U., Rasheed, H., Maaz, M., Khan, S., Khan, F.S.: MaPLe: Multi-modal prompt learning. In: Proc. IEEE/CVF Computer Vision and Pattern Recognition (CVPR). pp. 19113–19122 (2023)
- <span id="page-9-9"></span>14. Li, X., Zhou, Y., Dvornek, N., Zhang, M., Gao, S., Zhuang, J., Scheinost, D., Staib, L.H., Ventola, P., Duncan, J.S.: BrainGNN: Interpretable brain graph neural network for fMRI analysis. Med. Image Anal. **74**, 102233 (2021)
- <span id="page-9-5"></span>15. Liang, W., Zhang, K., Cao, P., Zhao, P., Liu, X., Yang, J., Zaiane, O.R.: Modeling alzheimers' disease progression from multi-task and self-supervised learning perspective with brain networks. In: Proc. Medical Image Computing and Computer-Assisted Intervention (MICCAI). pp. 310–319 (2023)
- <span id="page-9-0"></span>16. Noback, C.R., Ruggiero, D.A., Strominger, N.L., Demarest, R.J.: The human nervous system: Structure and function. No. 744, Springer Science & Business Media (2005)
- <span id="page-9-13"></span>17. Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., Killeen, T., Lin, Z., Gimelshein, N., Antiga, L., et al.: Pytorch: An imperative style, highperformance deep learning library. In: Proc. Neural Information Processing Systems (NeurIPS). vol. 32 (2019)

- <span id="page-10-6"></span>18. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: Proc. International Conference on Machine Learning (ICML). pp. 8748–8763 (2021)
- <span id="page-10-10"></span>19. Rolls, E.T., Huang, C.C., Lin, C.P., Feng, J., Joliot, M.: Automated anatomical labelling atlas 3. Neuroimage **206**, 116189 (2020)
- <span id="page-10-0"></span>20. Rosenberg, M.D., Finn, E.S., Scheinost, D., Papademetris, X., Shen, X., Constable, R.T., Chun, M.M.: A neuromarker of sustained attention from whole-brain functional connectivity. Nature Neurosci. **19**(1), 165–171 (2016)
- <span id="page-10-1"></span>21. Sporns, O.: Graph theory methods: Applications in brain networks. Dialogues Clin. Neurosci. **20**(2), 111–121 (2018)
- <span id="page-10-12"></span>22. Sutskever, I., Martens, J., Dahl, G., Hinton, G.: On the importance of initialization and momentum in deep learning. In: Proc. International Conference on Machine Learning (ICML). pp. 1139–1147 (2013)
- <span id="page-10-11"></span>23. Van Essen, D.C., Ugurbil, K., Auerbach, E., Barch, D., Behrens, T.E., Bucholz, R., Chang, A., Chen, L., Corbetta, M., Curtiss, S.W., et al.: The human connectome project: A data acquisition perspective. Neuroimage **62**(4), 2222–2231 (2012)
- <span id="page-10-7"></span>24. Wang, F., Zhou, Y., Wang, S., Vardhanabhuti, V., Yu, L.: Multi-granularity crossmodal alignment for generalized medical visual representation learning. In: Proc. Neural Information Processing Systems (NeurIPS). pp. 33536–33549 (2022)
- <span id="page-10-2"></span>25. Wang, Q., Wu, M., Fang, Y., Wang, W., Qiao, L., Liu, M.: Modularity-constrained dynamic representation learning for interpretable brain disorder analysis with functional MRI. In: Proc. Medical Image Computing and Computer-Assisted Intervention (MICCAI). pp. 46–56 (2023)
- <span id="page-10-8"></span>26. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: MedCLIP: Contrastive learning from unpaired medical images and text. In: Proc. Empirical Methods in Natural Language Processing (EMNLP). pp. 3876–3887 (2022)
- <span id="page-10-4"></span>27. Wasim, S.T., Naseer, M., Khan, S., Khan, F.S., Shah, M.: Vita-CLIP: Video and text adaptive CLIP via multimodal prompting. In: Proc. IEEE/CVF Computer Vision and Pattern Recognition (CVPR). pp. 23034–23044 (2023)
- <span id="page-10-9"></span>28. Zhang, S., Xu, Y., Usuyama, N., Bagga, J., Tinn, R., Preston, S., Rao, R., Wei, M., Valluri, N., Wong, C., et al.: Large-scale domain-specific pretraining for biomedical vision-language processing. arXiv preprint [arXiv:2303.00915](http://arxiv.org/abs/2303.00915) (2023)
- <span id="page-10-3"></span>29. Zhang, S., Chen, X., Shen, X., Ren, B., Yu, Z., Yang, H., Jiang, X., Shen, D., Zhou, Y., Zhang, X.Y.: A-GCL: Adversarial graph contrastive learning for fMRI analysis to diagnose neurodevelopmental disorders. Med. Image Anal. **90**, 102932 (2023)
- <span id="page-10-5"></span>30. Zhou, K., Yang, J., Loy, C., Liu, Z.: Conditional prompt learning for visionlanguage models. In: Proc. IEEE/CVF Computer Vision and Pattern Recognition (CVPR). pp. 16816–16825 (2022)

Image /page/11/Picture/0 description: A square button with a rounded corner is shown. The button is light gray and has a darker gray border. In the center of the button, there is a circular icon with a bookmark shape inside. Below the icon, the text "Check for updates" is displayed in a darker gray color.

# HiA: Towards Chinese Multimodal LLMs for Comparative High-Resolution Joint Diagnosis

 $X$ inpeng Ding<sup>1</sup>, Yongqiang Chu<sup>2</sup>, Renjie Pi<sup>1</sup>, Hualiang Wang<sup>1</sup>, and Xiaomeng Li<sup>1,3( $\boxtimes$ )</sup>

<sup>1</sup> The Hong Kong University of Science and Technology, Hong Kong, China <EMAIL>

<sup>2</sup> The Department of Radiology, Tongji Hospital, Tongji Medical College, Huazhong University of Science and Technology, Wuhan, China

<sup>3</sup> HKUST Shenzhen-Hong Kong Collaborative Innovation Research Institute, Futian, Shenzhen, China

Abstract. Multimodal large language models (MLLMs) have been explored in the Chinese medical domain for comprehending complex healthcare. However, due to the flaws in training data and architecture design, current Chinese medical MLLMs suffer from several limitations: cultural biases from English machine translations, limited comparative ability from single image input and difficulty in identifying small lesions with low-resolution images. To address these problems, we first introduce a new instruction-following dataset, Chili-Joint (Chinese Interleaved Image-Text Dataset for Joint Diagnosis) collected from the hospital in mainland China, avoiding cultural biases and errors caused by machine translation. Besides one single image input, Chili-Joint also has multiple images obtained at various intervals during a patient's treatment, thus facilitating an evaluation of the treatment's outcomes. We further propose a novel HiA (High-resolution instruction-aware Adapter) to incorporate high-resolutioninstruction-aware visual features into LLMs to facilitate the current MLLMs to observe the small lesions as well as the comparative analysis. Extensive experiments on Chili-Joint demonstrate our HiA can be a plug-and-play method to improve the performance of current MLLMs for medical analysis. The code is available at [https://](https://github.com/xmed-lab/HiA) [github.com/xmed-lab/HiA.](https://github.com/xmed-lab/HiA)

**Keywords:** Chinese multimodal data  $\cdot$  Large language model  $\cdot$  Adapter

# 1 Introduction

The recent surge in Large Language Models (LLMs) development, exemplified by GPT4 [\[21](#page-21-0)], Vicuna [\[5\]](#page-20-0) and LLama [\[25\]](#page-21-1), has revolutionized language tasks through

X. Ding and Y. Chu—Equal contribution.

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 575–586, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_54)\_54

advanced algorithms and massive data sets. This innovation has extended into multimodal large language models (MLLMs)  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$  $[2,6,7,10,13-15,18,22,31]$ , integrating visual data to enhance language understanding. Some researchers have explored the potential to apply MLLMs to the medical domain. For example, LLaVA-Med [\[12](#page-20-6)] and MedVInT [\[32\]](#page-22-0) enhance medical visual instruction by leveraging PubMed Central's captions [\[24](#page-21-6)], while Med-Flamingo [\[20\]](#page-21-7) and Med-BLIP [\[3\]](#page-20-7) innovate in medical visual question answering, extending to 3D data analysis like MRI. Med-PaLM M [\[26\]](#page-21-8) and RadFM [\[29](#page-21-9)] streamline multiple tasks, enhancing medical AI's scope and efficiency.

To address the limitation of English-centric models in Chinese medical applications, Qilin-Med-VL [\[19\]](#page-21-10), the pioneering Chinese medical MLLM, was developed to analyze both textual and visual medical data. This model, refined through a two-phase training on extensive Chinese visual-text pairs, excels at producing medical captions and resolving intricate medical inquiries in Chinese. Nonetheless, Qilin-Med-VL and similar Chinese MLLMs face challenges due to training data and design issues. Firstly, the reliance on ChiMed-VL [\[19\]](#page-21-10), a dataset translated from English by GPT-3.4, raises concerns about biases and errors introduced during translation, potentially compromising model reliability. Additionally, the current focus on single-image input restricts the models' diagnostic capabilities, overlooking the clinical need for comparing multiple radiographic images to assess treatment outcomes effectively.

To handle the drawbacks of current Chinese medical multimodal datasets, we introduce a new dataset Chili-Joint (Chinese Interleaved Image-Text Dataset for Joint Diagnosis). Chili-Joint has two important advantages. First, the imagetext pairs in Chili-Joint are collected from one top tertiary hospital in mainland China, which avoids cultural biases and errors caused by data machine-translated from English. Second, our Chili-Joint has interleaved vision language context; all samples contain sequences of inter-related images and texts, *e.g.*, X-rays obtained at various intervals during a patient's treatment and corresponding descriptions. This enables the comparison across different periods, thus facilitating an evaluation of the treatment's outcomes.

By evaluating our proposed Chili-Joint, we find that current MLLMs (both medical and general domain) fail to detect small lesions and conduct the comparative analysis of images from different periods, which may caused by two reasons. ❶ Low-resolution input. Due to the data, memory and computation cost limitations, prevalent methods invariably input images with a small size (*i.e.,*  $336 \times 336$ ) for training [\[12](#page-20-6),[34\]](#page-22-1). However, the lesions often occupy only a small portion of the entire image, making it challenging to observe in low-resolution pictures [\[1](#page-20-8)], leading to diagnostic errors (see Fig. [1](#page-13-0) (a)).  $\bullet$  Instruction-agnostic visual extraction. Current MLLMs use a pre-trained connector layer, *e.g.*, Q-Former [\[13](#page-20-5)], to extract and translate visual features into tokens recognizable by LLMs. However, this pre-trained lightweight layer is instruction-irrelevant and pre-trained on image-caption pairs, thus tending to the primary visual contents which are just enough for the captioning task, but omit other visual details, *i.e.*, changes in lesions across different periods (see Fig. [1](#page-13-0) (b)).

|                     | CN: 此图是否有异常?<br>EN:  Whether any abnormality in this image?                                                                                                                                                                                                                                   |
|---------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Qilin-Med-VL</b> | CN: 可见关节诸骨骨质完整,骨小梁显示清晰,无明显异常 。<br>EN: The visible bones of the joint are intact, and the trabecular bone is clearly visible without any obvious abnormalities.                                                                                                                                |
| Ours                | CN:右侧股骨下段可见片状低密度影。膝关节间隙未见变窄。<br>EN: A flake-like low-density shadow can be seen in the lower segment of the right femur. No narrowing of the knee joint space.                                                                                                                                |
| <b>Ground truth</b> | CN: 右侧股骨下段髌骨投影区可见片状低密度影。<br>EN: A flake-like area of low density can be seen in the projection area of the patella in the lower segment of the right femur.<br>(a) Small Lesions                                                                                                              |
|                     | CN:  和  分别是患者第一次检查的图片和经过手术治疗后的图片, 请对比两张图片。<br>EN:  and  depict the patient's condition before and after surgical treatment, respectively. Please compare<br>two images.                                                                                                                       |
| <b>Qilin-Med-VL</b> | CN: 第一张为左侧X光图像, 显示骨质边缘增生、硬化。第二张为左侧膝盖的X光图像, 显示骨质边缘增生、硬化。<br>EN: The first image is an X-ray of the left side, showing bone edge proliferation and hardening. The second .... hardening.                                                                                                        |
| Ours                | CN: 第一张显示左膝关节骨质边缘增生,硬化。第二张相比第一张, 增生, 硬化等情况减弱, 表明左侧膝关节术后改变。<br>EN: The first image shows proliferation and hardening of the bone edges at the left knee joint the second image shows a<br>reduction in proliferation and hardening, indicating post-operative changes in the left knee joint.  |
| <b>Ground truth</b> | CN: 第二张相比第一张, 左侧骨质边缘增生,硬化,髁间隆起变尖等程度减弱,表明左侧膝关节术后改变。<br>EN: Compared to the first image, the second one shows a decreased extent of osteophyte formation, sclerosis, and sharpening<br>of the intercondylar eminence on the left side, indicating post-surgical changes in the left knee joint. |
|                     | (b) Comparative Diagnosis                                                                                                                                                                                                                                                                     |

<span id="page-13-0"></span>Fig. 1. (a) Small lesions. Due to the low-resolution input, small lesions (red box) can not be diagnosed by the current MLLM SOTA [\[19\]](#page-21-10). (b) Comparative Diagnosis. Since the instruction-agnostic visual extraction of connector layer, current methods fail to follow the instruction to conduct comparative diagnosis. Benefit from HiA, our method can accurately produce correct responses based on the instruction (see red words). (Color figure online)

To address the above problems, we propose a lightweight HiA (Highresolution instruction-aware Adapter) which can efficiently enable the current Chinese medical MLLMs to receive multiple high-resolution images and conduct a comparative analysis for evaluating the effect of therapy (see Fig. [1\)](#page-13-0). Specifically, HiA consists of three components: a high-resolution visual encoder to produce high-resolution visual features from high-resolution input, an instructionaware extractor to capture instruction-related visual information from highresolution features, and an injection module to inject the instruction-related visual information into LLMs for better understanding. Notably, our proposed HiA is training-efficient and plug-and-play to be applied to existing MLLMs. We freeze all parameters of current MLLMs and only fine-tune the HiA during the training stage. We conduct experiments on Chili-Joint and prove HiA can be a plug-and-play method to benefit current MLLMs for medical analysis.

# 2 Method

As shown in Fig. [2,](#page-14-0) our overall pipeline consists of two parts: a multimodal large language model (MLLM) (Sect. [2.1\)](#page-14-1) and new proposed high-resolution instruction-aware adapter (HiA) (Sect. [2.2\)](#page-15-0).

Image /page/14/Figure/1 description: This figure illustrates the overall pipeline and detailed modules of a visual-language model. Part (a) shows the overall pipeline, where a visual encoder processes two images, a connector links them to a tokenizer, and these are fed into a large language model. The output of the language model is then processed by a High-resolution visual encoder and a HiA module. Part (b) details the HiA module, which includes an instruction-aware extractor and an injection module. Part (c) elaborates on the instruction-aware extractor, showing cross-attention mechanisms. Part (d) details the injection module, also using cross-attention. The diagram uses fire icons to denote trainable components and snowflake icons for frozen components. The text in the figure includes labels for layers, vectors (v, z, h), and modules, as well as an instruction in both Chinese and English describing the task of assessing treatment effectiveness based on medical images.

<span id="page-14-0"></span>Fig. 2. Besides a general MLLM, which handles low-resolution image-text pairs (blue pathways), we also introduce a compact HiA mechanism (orange pathways) to improve MLLMs for comparative medical image analysis. The HiA module extracts visual information relevant to the instructions using an instruction-aware extractor and then incorporates this information into the MLLM through an injection module. (Color figure online)

<span id="page-14-1"></span>

## 2.1 Existing Multimodal Large Language Model

Current medical or general MLLMs, *e.g.*, Qilin-Med-VL [\[19\]](#page-21-10) or LLaVA [\[12\]](#page-20-6), generally consist of four parts: a vision encoder, a connector, a tokenizer and a large language module, which are introduced briefly in the following. Note that we will show two images in each pair for illustration. More images can also be processed similarly.

Visual Encoder. The visual encoder of the MLLMs is generally the plain ViT initialized from CLIP [\[23\]](#page-21-11), which has pre-trained on massive image-text pairs. Formally, given the input images  $I_i$  and  $I_j$ , the vision encoder produces the corresponding visual features  $\mathbf{F}_i$  and  $\mathbf{F}_j$  respectively.

Connector Layer. The lightweight connector layer has two purposes: (i) translate  $\mathbf{F}_i$  into tokens  $\mathbf{V}_i^0$  recognizable by LLMs and (ii) capture and compress the long visual features to fixed shorter ones. The connector is trained on millions of image-caption pairs by feeding the generated visual tokens, *i.e.*,  $\mathbf{V}_i^0$ , into a frozen LLM which generates the corresponding captions. Considering the comfrozen LLM which generates the corresponding captions. Considering the computation and hardware cost, the size of the input image is in low-resolution, *e.g.*, generally up to  $336 \times 336$ .

Tokenizer. The tokenizer aims to map the input text instructions to token embeddings, *i.e.*,  $\mathbf{H}^0$ , that are following fed into the LLM.

Large Language Model (LLM). As shown in Fig. [2](#page-14-0) (a), before fed into the LLM,  $\mathbf{V}_{i,j}^0$  and  $\mathbf{H}^0$  are concatenated into a 1D sequence, formulated as follows:

$$
\{\mathbf h_1^0, \mathbf h_2^0, \dots, \underbrace{\mathbf v_{i1}^0, \dots, \mathbf v_{ik}^0, \dots, \mathbf v_{iK}^0, \dots, \mathbf h_n^0, \dots, \mathbf v_{j1}^0, \dots, \mathbf v_{jK}^0, \dots, \mathbf h_N^0\}, \quad (1)
$$

where  $\mathbf{v}_{ik}^0$  is the *k*-th token in  $\mathbf{V}_i^0$  and  $\mathbf{h}_n^0$  is the *n*-th tokens in  $\mathbf{H}^0$ . In this paper, we follow Qilin-Med-VL [\[19\]](#page-21-10) to use a renowned Chinese LLM, Chinese-LLaMA2- 13B-Chat, as our pre-trained LLM.

<span id="page-15-0"></span>

### 2.2 High-Resolution Instruction-Aware Adapter

The high-resolution instruction-aware adapter (HiA) aims to capture highresolution instruction-related visual information for MLLMs. Specifically, we first use a high-resolution visual encoder transfers the high-resolution images, *i.e.*,  $\bar{I}_i$ and  $\bar{\mathbf{I}}_i$ , into visual features  $\mathbf{Z}_i$  and  $\mathbf{Z}_i$ . The high-resolution visual encoder consists of several CNN [\[11](#page-20-9)] layers, which are more lightweight and training-efficient to handle dynamic resolutions of input, compared with the plain ViT [\[9\]](#page-20-10) of MLLMs  $[4, 8]$  $[4, 8]$ .

Given the high-resolution visual features and outputs from LLMs (including low-resolution visual tokens and textual tokens), HiA uses an instructionaware extractor to capture instruction-related visual information from highresolution features. Then, an injection module is adopted to incorporate the instruction-related visual information into LLMs for understanding and reasoning. We detail the instruction-aware extractor and the injection module in the following for  $\mathbf{V}_i^l$ , and so do as  $\mathbf{V}_j^l$ .

Instruction-Aware Extractor. We denote the output from the *l*-th layer of the LLM as  $\{\mathbf{h}_1^l, \mathbf{h}_2^l, \dots, \mathbf{V}_i, \dots, \mathbf{h}_n^l, \dots, \mathbf{V}_j^l, \dots, \mathbf{h}_N^l\}$  $\{\mathbf{h}_1^l, \mathbf{h}_2^l, \dots, \mathbf{V}_i, \dots, \mathbf{h}_n^l, \dots, \mathbf{V}_j^l, \dots, \mathbf{h}_N^l\}$  $\{\mathbf{h}_1^l, \mathbf{h}_2^l, \dots, \mathbf{V}_i, \dots, \mathbf{h}_n^l, \dots, \mathbf{V}_j^l, \dots, \mathbf{h}_N^l\}$ , where  $\mathbf{V}_i^l = \{\mathbf{v}_{i1}^l, \dots, \mathbf{v}_{iK}^l\} \in \mathbb{R}^{K \times D_1}$  and  $\mathbf{V}_j^l = \{\mathbf{v}_{j1}^l, \dots, \mathbf{v}_{jK}^l\} \in \mathbb{R}^{K \times D_1}$ . As shown in the last token  $\mathbf{h}_N^l \in \mathbb{R}^{1 \times D_1}$  that can fully perceive the whole multimodal context during the first *l* layers and contains comprehensive instruction-aware semantics. Next, we obtain a set of learnable instruction-aware queries by:

<span id="page-15-1"></span>
$$
\mathbf{Q} = \mathbf{Q} + \text{Linear}(\mathbf{h}_N^l), \quad \mathbf{Q} \in \mathbb{R}^{M \times D}, \quad \text{Linear}(\mathbf{h}_N^l) \in \mathbb{R}^{1 \times D}.
$$
 (2)

Finally, we use a cross-attention block, where **Q** as the query, highresolutionvisual features  $\mathbf{Z}_i$  as the value and key, to obtain  $\overline{\mathbf{Z}}_i^l \in \mathbb{R}^{M \times D}$ , which can be formulated as  $\overline{\mathbf{Z}}_i^l = \text{CrossAtten}(\mathbf{Q}, \text{Linear}(\mathbf{Z}_i))$ . In this way,  $\overline{\mathbf{Z}}_i^l$  would contain more information related to the instructions. For example, given the instructions 'assess the effectiveness of treatment', the model would focus more on the lesion difference between two images to conduct an assessment, while the connector layer in existing MLLMs tends to capture the salient information.

**Injection Module.** After obtaining the instruction-related features  $\overline{\mathbf{Z}}_i^l$ , we use the injection module to interact the information between  $\overline{\mathbf{Z}}_i^l$  and  $\mathbf{V}_i^l$  by  $\overline{\mathbf{V}}_i^l$  =

Image /page/16/Figure/1 description: The image displays a pie chart and examples of medical image analysis tasks. The pie chart, labeled "(a) Proportions of tasks", shows the distribution of four task types: Description (32%), Disease (30%), Location (23%), and Comparative (9%). The right side of the image, labeled "(b) Examples", provides examples for each task type. The "Description" example includes a hip X-ray image and Chinese and English prompts asking to describe the medical image. The "Disease" example shows a similar hip X-ray and prompts related to bone growth. The "Location" example, with a hip X-ray, asks where bone hyperplasia is located in the image. The "Comparative" example presents two hip X-ray images and prompts for comparison between the two examinations.

<span id="page-16-0"></span>Fig. 3. (a) Proportions of tasks. The size of the arc represents the proportions of each task. (b) Examples of different tasks. For clarity, we only show instructions, omitting the responses.

CrossAtten( $\mathbf{V}_i^l, \mathbf{Z}_i^l$ ), where  $\overline{\mathbf{V}}_i^l \in \mathbb{R}^{K \times D_1}$ . Finally, we feed the addition, *i.e.*,  $\overline{\mathbf{V}}_i^l + \mathbf{V}_i^l$ , into the  $l + 1$ -th layer of the LLM. See Fig. [2](#page-14-0) (d) for details.

Note that we only fine-tune the newly introduced high-resolutionvisual encoder and HiA, and keep other parameters of MLLMs frozen for data and training efficiency. Compared with previous MLLMs towards high-resolution input [\[8](#page-20-12)], our HiA considers challenges in medical images, e.g., comparative, instruction-related extraction.

# 3 Experiments

Dataset. Prior work, such as Huatuo-26M [\[27\]](#page-21-12) and Qilin-Med-VL [\[19](#page-21-10)], found that translating from English introduces biases and inaccuracies, compromising robustness. We collect our dataset, *i.e.*, Chili-Joint, from our collaborating hospitals for the native Chinese dataset. There are totally 14*K* interleaved image-text pairs are randomly split into training, validation and testing set at 7:1*.*5:1*.*5 ratio. We construction four different tasks, *i.e.*, Description, Disease, Location and Comparative, as shown in Fig.  $3$  (a). The detailed examples for each task are illustrated in Fig. [3](#page-16-0) (b).

**Implementation Details.** We use Qilin-Med-VL  $[19]$  $[19]$ <sup>[1](#page-16-1)</sup> as our baseline MLLM, which uses Chinese-LLaMA[2](#page-16-2)-13B-Chat<sup>2</sup> as the foundation LLM and Clip-ViT-large-patch14-336 [\[23](#page-21-11)]<sup>[3](#page-16-3)</sup> as the pre-trained image encoder. Chinese-LLaMA2-13B-Chat is an open-source transformer-based LLM with 13 billion parameters further trained on Chinese-LLaMA2-13B and optimized for conversation scenarios. Clip-ViT-large-patch14-336 is a pre-trained CLIP vision encoder trained by OpenAI. The number of learnable instruction-aware queries is set to 256 (see Table [4a](#page-18-0) for ablation study). We select two layers of the LLM (*L/*3 and 2*L/*3) to

<span id="page-16-1"></span><sup>1</sup> [https://github.com/williamliujl/Qilin-Med-VL.](https://github.com/williamliujl/Qilin-Med-VL)

<span id="page-16-2"></span><sup>2</sup> [https://github.com/LlamaFamily/Llama-Chinese.](https://github.com/LlamaFamily/Llama-Chinese)

<span id="page-16-3"></span><sup>3</sup> [https://huggingface.co/openai/clip-vit-large-patch14-336.](https://huggingface.co/openai/clip-vit-large-patch14-336)

<span id="page-17-0"></span>Table 1. Comparison with the state-of-the-art on Chili-Joint. For all metrics, the higher the scores, the better the results. 'B4' and 'M' refer to BLEU-4 and METEOR. 'AVG' is the average value of all metrics on both two dataset. ∗ indicates the model is pre-trained on Chinese medical multimodal data. Note that all models are fine-tuning on Chili-Jointin the same setting.

| Method             | Description |             | Disease     |             | Location    |             | Comparative |             | AVG         |
|--------------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|                    | B4          | M           | B4          | M           | B4          | M           | B4          | M           |             |
| MiniGPT-4 [34]     | 38.5        | 28.7        | 36.7        | 23.6        | 29.2        | 18.8        | 32.6        | 20.9        | 28.6        |
| + HiA              | 41.2        | 30.4        | 44.5        | 31.3        | 36.7        | 23.8        | 39.5        | 26.0        | 34.2        |
| LLaVA-1.5 [17]     | 39.6        | 29.9        | 37.5        | 24.2        | 30.1        | 18.9        | 33.8        | 21.3        | 29.4        |
| + HiA              | 43.1        | 31.1        | 44.7        | 34.9        | 37.5        | 24.2        | 41.3        | 28.8        | 35.7        |
| LLaVA-Med [12]     | 48.4        | 34.3        | 42.7        | 30.7        | 35.2        | 23.8        | 36.9        | 28.5        | 35.1        |
| + HiA              | 53.2        | 37.2        | 47.8        | 36.3        | 40.8        | 29.0        | 39.2        | 32.4        | 39.5        |
| Qilin-Med-VL* [19] | 51.4        | 38.5        | 44.6        | 33.2        | 38.7        | 26.8        | 37.8        | 30.7        | 37.7        |
| + HiA              | <b>54.8</b> | <b>40.6</b> | <b>49.5</b> | <b>38.6</b> | <b>43.9</b> | <b>28.0</b> | <b>42.5</b> | <b>33.5</b> | <b>41.4</b> |

use our HiA (see Table [4b](#page-18-0) for analysis). As for the vision-language instructiontuning stage, we use the following settings: batch size  $=4$  per GPU, one epoch, learning rate =  $2e - 5$ , warmup ratio = 0.03, and max length = 2048.

Evaluation Metrics. To evaluate the generated response based on the instructions, we use standard caption metrics [\[16](#page-21-14)[,28](#page-21-15)[,30](#page-21-16),[33\]](#page-22-2), *i.e.*, BLEU-4 (B4), METEOR (M) to compare the consistency between the predictions from models and ground-truth.

### 3.1 Comparison with the State-of-the-Art Methods

To evaluate our proposed HiA, we select four state-of-the-art MLLMs including natural domain (*i.e.*, MiniGPT-4 [\[34](#page-22-1)] and LLaVA-1.5 [\[17\]](#page-21-13)) and medical domain (*i.e.*, LLaVA-Med [\[12\]](#page-20-6) and Qilin-Med-VL [\[17](#page-21-13)]). Among them, Qilin-Med-VL [\[17\]](#page-21-13) is pre-trained on the Chinese medical multimodal dataset. For fair evaluation, all models are fine-tuned on the training set of Chili-Joint, select the model that achieving the best performance on the validation set, and report the performance on the test set on Table [1.](#page-17-0)

We observe two findings from Table [1.](#page-17-0) (i) Pre-training on medical data is essential, *i.e.*, models pre-trained on medical data outperform those not pretrained. For example, the average performance of LLaVA-Med and Qilin-Med-VL across all tasks is 35*.*1 and 37*.*7, respectively, exceeding that of MiniGPT-4 and LLaVA-1.5, which is 28*.*6 and 29*.*4. (ii) Our proposed HiA yields significant improvements for all models across all tasks. For instance, in terms of average performance, HiA achieves improvements of 5*.*6, 6*.*3, 4*.*5, and 3*.*7 compared to the four state-of-the-art models (MiniGPT-4, LLaVA-1.5, LLaVA-Med, and Qilin-Med-VL), respectively.

<span id="page-18-1"></span>Table 2. The ablation study of different proposed modules. 'Des', 'Dis', 'Loc' and 'Com' indicate the Description, Disease, Location and Comparative tasks. 'IA' and 'HR' indicate using instructionaware tokens and high-resolution input, respectively.  $\triangle$  is the difference between SOTA with and without our HiA.

| Method      | Des                                                                 | Dis                                                                 | Loc                                                                 | Com                                                                 |
|-------------|---------------------------------------------------------------------|---------------------------------------------------------------------|---------------------------------------------------------------------|---------------------------------------------------------------------|
| <b>Full</b> | <b>54.8</b>                                                         | <b>49.5</b>                                                         | <b>43.9</b>                                                         | <b>42.5</b>                                                         |
| w/o IA      | 53.1                                                                | 45.6                                                                | 38.9                                                                | 38.8                                                                |
| Δ           | <span style="text-decoration: underline; color: green;">-1.7</span> | <span style="text-decoration: underline; color: green;">-3.9</span> | <span style="text-decoration: underline; color: green;">-5.0</span> | <span style="text-decoration: underline; color: green;">-3.7</span> |
| w/o HR      | 51.6                                                                | 44.8                                                                | 42.3                                                                | 41.6                                                                |
| Δ           | <span style="text-decoration: underline; color: green;">-3.2</span> | <span style="text-decoration: underline; color: green;">-4.7</span> | <span style="text-decoration: underline; color: green;">-1.6</span> | <span style="text-decoration: underline; color: green;">-0.9</span> |

<span id="page-18-2"></span>Table 3. Comparison of the memory cost and flops between the baseline and our method.  $X$  indicates the results cannot be obtained caused of outof-memory. 'Res.' means the image resolution of high-resolution images. The results of the baseline model and our HiA are reported as red and blue respectively.

| Res. | Memory                                  | FLOPs       | AVG                                     |
|------|-----------------------------------------|-------------|-----------------------------------------|
| 448  | 30.8 / 29.6                             | 46.8 / 15.8 | 37.7 / 39.6                             |
| 560  | 31.3 / 29.9                             | 85.5 / 16.2 | 38.0 / 40.8                             |
| 672  | <span style="color:red">X</span> / 30.1 | 105 / 16.5  | <span style="color:red">X</span> / 41.4 |
| 784  | <span style="color:red">X</span> / 30.3 | 162 / 16.7  | <span style="color:red">X</span> / 41.5 |
| 896  | <span style="color:red">X</span> / 30.4 | 208 / 16.8  | <span style="color:red">X</span> / 41.5 |

<span id="page-18-0"></span>Table 4. Ablation on different designs in HiA. Default settings are marked in gray .

| M          | Des         | Dis         | Loc         | Com         | Num | Des         | Dis         | Loc         | Com         |
|------------|-------------|-------------|-------------|-------------|-----|-------------|-------------|-------------|-------------|
| 128        | 52.6        | 46.9        | 41.3        | 40.4        | 1   | 52.1        | 45.8        | 41.2        | 39.9        |
| <b>256</b> | <b>54.8</b> | <b>49.5</b> | <b>43.9</b> | <b>42.5</b> | 2   | <b>54.8</b> | <b>49.5</b> | <b>43.9</b> | <b>42.5</b> |
| 512        | 54.6        | 49.5        | 44.0        | 42.0        | 3   | 54.2        | 49.0        | 43.4        | 41.8        |

(a) *M* in Eq. [2](#page-15-1) (Length of learnable instruction-related queries **Q**).

(b) Number of layers of LLM incorporated with HiA.

#### 3.2 Ablation Study

In this section, we conduct the ablation study to evaluate the effectiveness of the proposed modules in HiA. We use Qilin-Med-VL [\[19](#page-21-10)] as our baseline model.

Effect of Different Proposed Modules. Our HiA model outperforms existing MLLMs by incorporating two novel types of information: instruction-related visual features and high-resolution (HR) features. To assess HiA's impact, we use Qilin-Med-VL equipped with our HiA as a full model and conducted a series of experiments by systematically removing each component. Specifically,  $w/\text{o}$  IA' means that we remove the last token  $h_N^l$  (Eq. [2\)](#page-15-1) from the learnable instructionaware queries, thus ignoring the instruction semantics.  $\mathbf{w}_0$  HR' indicates that we set the resolutions of high-resolution images to 336, equal to low-resolution images. The comparative analysis, summarized in Table [2,](#page-18-1) yielded two primary insights: (i) IA would not degrade the description task too much, since the original connector is trained to handle description-related tasks. IA module can capture more useful information; without IA, the performance of the other three tasks would degrade clearly. (ii) Without HR, the model fails to detect small

lesions, resulting in descriptions that overlook these lesions and consequently degrade performance (also see Fig. [1](#page-13-0) (a)).

Comparison of the Baseline and Ours Across Different Resolutions. An intuitive way to enhance the perception ability is to increase the resolution of inputs, *i.e.*, inputs of varying resolutions are fed into the baseline and our method, respectively. To further study the effectiveness of our proposed HiA, we conduct experiments to compare the baseline and our method across different resolution inputs in Table [3.](#page-18-2) From the table, we can see that as the input image resolution increases, the memory cost and FLOPs of the baseline model grow proportionally, and would be out-of-memory when the input size rises to 672  $\times$ 672. Differently, benefiting from HiA, our approach outperforms the baseline model while using much less computation and memory cost.

Effect of Different Designs. In our investigation detailed in Table [4,](#page-18-0) we examine the impact of varying design parameters: the length of learnable instructionrelated queries (*M*) and the number of layers of LLM incorporated with HiA. Table [4a](#page-18-0) demonstrates that as the number of query tokens increases, the instruction-aware extractor would capture more useful information from highresolution input,  $e.g., M = 256$  outperforms  $M = 128$ . However, too many query tokens, *e.g.*, <sup>512</sup>, would bring more noise as well as computation cost, degrading the performance. Table [4b](#page-18-0) analyzes the effect of the different numbers of layers in the LLM to be injected with HiA. Specifically,  $Num = 1$ ,  $Num = 2$ and Num = 3 mean that we incoporated HiA in layer  ${L/2}$ ,  ${L/3, 2L/3}$ and  $\{L/4, 2L/4, 3L/4\}$  respectively, where *L* is the total layer number of the LLM. Results show that incorporating HiA into two layers achieves the best performance.

### 4 Conclusion

In this paper, we propose a new dataset Chili-Joint (Chinese Interleaved Image-Text Dataset for Joint Diagnosis), featuring image-text pairs from a leading tertiary hospital in China to mitigate cultural biases and enable comparative analysis of treatment through interrelated images and descriptions. We introduce HiA, a lightweight adapter that enhances Chinese medical MLLMs' ability to analyze multiple high-resolution images for therapy evaluation. HiA, designed as a plug-and-play, training-efficient component, significantly improves medical MLLMs for medical analysis. Our current dataset is limited to X-ray images, and we plan to expand it by including more diverse modalities like CT and MRI in the future.

Acknowledgements. This work was supported in part by grants from the National Natural Science Foundation of China under Grant No. 62306254, grants from the Research Grants Council of the Hong Kong Special Administrative Region, China (Project Reference Number: T45-401/22-N), and Project of Hetao Shenzhen-Hong Kong Science and Technology Innovation Cooperation Zone (HZQB-KCZYB-2020083). Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-20-8"></span>1. Baumgartner, M., Jäger, P.F., Isensee, F., Maier-Hein, K.H.: nndetection: a selfconfiguring method for medical object detection. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2021: 24th International Conference, Strasbourg, France, September 27–October 1, 2021, Proceedings, Part V 24. pp. 530–539. Springer (2021)
- <span id="page-20-1"></span>2. Chen, K., Zhang, Z., Zeng, W., Zhang, R., Zhu, F., Zhao, R.: Shikra: Unleashing multimodal llm's referential dialogue magic. arXiv preprint [arXiv:2306.15195](http://arxiv.org/abs/2306.15195) (2023)
- <span id="page-20-7"></span>3. Chen, Q., Hu, X., Wang, Z., Hong, Y.: Medblip: Bootstrapping language-image pre-training from 3d medical images and texts. arXiv preprint [arXiv:2305.10799](http://arxiv.org/abs/2305.10799) (2023)
- <span id="page-20-11"></span>4. Chen, Z., Duan, Y., Wang, W., He, J., Lu, T., Dai, J., Qiao, Y.: Vision transformer adapter for dense predictions (May 2022)
- <span id="page-20-0"></span>5. Chiang, W.L., Li, Z., Lin, Z., Sheng, Y., Wu, Z., Zhang, H., Zheng, L., Zhuang, S., Zhuang, Y., Gonzalez, J.E., et al.: Vicuna: An open-source chatbot impressing gpt-4 with 90%\* chatgpt quality. See https://vicuna lmsys. org (accessed 14 April 2023) (2023)
- <span id="page-20-2"></span>6. Dai, W., Li, J., Li, D., Huat, A., Zhao, J., Wang, W., Li, B., Fung, P., Hoi, S.: Instructblip: Towards general-purpose vision-language models with instruction tuning (2023)
- <span id="page-20-3"></span>7. Ding, X., Han, J., Xu, H., Liang, X., Zhang, W., Li, X.: Holistic autonomous driving understanding by bird's-eye-view injected multi-modal large models. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 13668–13677 (2024)
- <span id="page-20-12"></span>8. Ding, X., Han, J., Xu, H., Zhang, W., Li, X.: Hilm-d: Towards high-resolution understanding in multimodal large language models for autonomous driving. arXiv preprint [arXiv:2309.05186](http://arxiv.org/abs/2309.05186) (2023)
- <span id="page-20-10"></span>9. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-20-4"></span>10. Driess, D., Xia, F., Sajjadi, M.S., Lynch, C., Chowdhery, A., Ichter, B., Wahid, A., Tompson, J., Vuong, Q., Yu, T., et al.: Palm-e: An embodied multimodal language model. arXiv preprint [arXiv:2303.03378](http://arxiv.org/abs/2303.03378) (2023)
- <span id="page-20-9"></span>11. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: 2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR) (Jun 2016). [https://doi.org/10.1109/cvpr.2016.90,](https://doi.org/10.1109/cvpr.2016.90) [http://dx.doi.org/10.](http://dx.doi.org/10.1109/cvpr.2016.90) [1109/cvpr.2016.90](http://dx.doi.org/10.1109/cvpr.2016.90)
- <span id="page-20-6"></span>12. Li, C., Wong, C., Zhang, S., Usuyama, N., Liu, H., Yang, J., Naumann, T., Poon, H., Gao, J.: Llava-med: Training a large language-and-vision assistant for biomedicine in one day. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-20-5"></span>13. Li, J., Li, D., Savarese, S., Hoi, S.: Blip-2: Bootstrapping language-image pretraining with frozen image encoders and large language models. arXiv preprint [arXiv:2301.12597](http://arxiv.org/abs/2301.12597) (2023)

- 14. Li, J., Li, D., Xiong, C., Hoi, S.: Blip: Bootstrapping language-image pre-training for unified vision-language understanding and generation. In: International Conference on Machine Learning. pp. 12888–12900. PMLR (2022)
- <span id="page-21-2"></span>15. Li, K., He, Y., Wang, Y., Li, Y., Wang, W., Luo, P., Wang, Y., Wang, L., Qiao, Y.: Videochat: Chat-centric video understanding. arXiv preprint [arXiv:2305.06355](http://arxiv.org/abs/2305.06355) (2023)
- <span id="page-21-14"></span>16. Liu, F., Wu, X., Ge, S., Fan, W., Zou, Y.: Exploring and distilling posterior and prior knowledge for radiology report generation. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 13753–13762 (2021)
- <span id="page-21-13"></span>17. Liu, H., Li, C., Li, Y., Lee, Y.J.: Improved baselines with visual instruction tuning. arXiv preprint [arXiv:2310.03744](http://arxiv.org/abs/2310.03744) (2023)
- <span id="page-21-3"></span>18. Liu, H., Li, C., Wu, Q., Lee, Y.J.: Visual instruction tuning. arXiv preprint [arXiv:2304.08485](http://arxiv.org/abs/2304.08485) (2023)
- <span id="page-21-10"></span>19. Liu, J., Wang, Z., Ye, Q., Chong, D., Zhou, P., Hua, Y.: Qilin-med-vl: Towards chinese large vision-language model for general healthcare. arXiv preprint [arXiv:2310.17956](http://arxiv.org/abs/2310.17956) (2023)
- <span id="page-21-7"></span>20. Moor, M., Huang, Q., Wu, S., Yasunaga, M., Dalmia, Y., Leskovec, J., Zakka, C., Reis, E.P., Rajpurkar, P.: Med-flamingo: a multimodal medical few-shot learner. In: Machine Learning for Health (ML4H). pp. 353–367. PMLR (2023)
- <span id="page-21-0"></span>21. OpenAI, O.: Gpt-4 technical report (Mar 2023)
- <span id="page-21-4"></span>22. Peng, Z., Wang, W., Dong, L., Hao, Y., Huang, S., Ma, S., Wei, F.: Kosmos-2: Grounding multimodal large language models to the world. arXiv preprint [arXiv:2306.14824](http://arxiv.org/abs/2306.14824) (2023)
- <span id="page-21-11"></span>23. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-21-6"></span>24. Roberts, R.J.: Pubmed central: The genbank of the published literature. Proceedings of the National Academy of Sciences p. 381–382 (Jan 2001). [https://doi.org/](https://doi.org/10.1073/pnas.98.2.381) [10.1073/pnas.98.2.381,](https://doi.org/10.1073/pnas.98.2.381) <http://dx.doi.org/10.1073/pnas.98.2.381>
- <span id="page-21-1"></span>25. Touvron, H., Lavril, T., Izacard, G., Martinet, X., Lachaux, M.A., Lacroix, T., Rozière, B., Goyal, N., Hambro, E., Azhar, F., et al.: Llama: Open and efficient foundation language models. arXiv preprint [arXiv:2302.13971](http://arxiv.org/abs/2302.13971) (2023)
- <span id="page-21-8"></span>26. Tu, T., Azizi, S., Driess, D., Schaekermann, M., Amin, M., Chang, P.C., Carroll, A., Lau, C., Tanno, R., Ktena, I., et al.: Towards generalist biomedical ai. NEJM AI 1(3), AIoa2300138 (2024)
- <span id="page-21-12"></span>27. Wang, H., Liu, C., Xi, N., Qiang, Z., Zhao, S., Qin, B., Liu, T.: Huatuo: Tuning llama model with chinese medical knowledge. arXiv preprint [arXiv:2304.06975](http://arxiv.org/abs/2304.06975) (2023)
- <span id="page-21-15"></span>28. Wang, Z., Liu, L., Wang, L., Zhou, L.: R2gengpt: Radiology report generation with frozen llms. Meta-Radiology 1(3), 100033 (2023)
- <span id="page-21-9"></span>29. Wu, C., Zhang, X., Zhang, Y., Wang, Y., Xie, W.: Towards generalist foundation model for radiology. arXiv preprint [arXiv:2308.02463](http://arxiv.org/abs/2308.02463) (2023)
- <span id="page-21-16"></span>30. Xue, Y., Xu, T., Rodney Long, L., Xue, Z., Antani, S., Thoma, G.R., Huang, X.: Multimodal recurrent model with attention for automated radiology report generation. In: Medical Image Computing and Computer Assisted Intervention– MICCAI 2018: 21st International Conference, Granada, Spain, September 16-20, 2018, Proceedings, Part I. pp. 457–466. Springer (2018)
- <span id="page-21-5"></span>31. Zhang, H., Li, X., Bing, L., at al.: Video-llama: An instruction-tuned audio-visual language model for video understanding. arXiv preprint [arXiv:2306.02858](http://arxiv.org/abs/2306.02858) (2023)

- <span id="page-22-0"></span>32. Zhang, X., Wu, C., Zhao, Z., Lin, W., Zhang, Y., Wang, Y., Xie, W.: Pmc-vqa: Visual instruction tuning for medical visual question answering. arXiv preprint [arXiv:2305.10415](http://arxiv.org/abs/2305.10415) (2023)
- <span id="page-22-2"></span>33. Zhang, Y., Wang, X., Xu, Z., Yu, Q., Yuille, A., Xu, D.: When radiology report generation meets knowledge graph. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 34, pp. 12910–12917 (2020)
- <span id="page-22-1"></span>34. Zhu, D., Chen, J., Shen, X., Li, X., Elhoseiny, M.: Minigpt-4: Enhancing visionlanguage understanding with advanced large language models. arXiv preprint [arXiv:2304.10592](http://arxiv.org/abs/2304.10592) (2023)

Image /page/23/Picture/0 description: A square button with rounded corners has a circular icon at the top and text below it. The icon is a gray circle with a gray bookmark shape inside. The bookmark is a rectangle with a triangular cutout at the top right corner, and it is positioned in the center of the circle. The text below the icon reads "Check for updates" in a gray sans-serif font. The button itself is a light gray with a subtle gradient, giving it a slightly raised appearance.

# **Knowledge-Grounded Adaptation Strategy for Vision-Language Models: Building a Unique Case-Set for Screening Mammograms for Residents Training**

Aisha Urooj Khan<sup>1( $\boxtimes$ )</sup>, John Garrett<sup>2</sup>, Tyler Bradshaw<sup>2</sup>, Lonie Salkowski<sup>2</sup>, Jiwoong Jeong<sup>3</sup>, Amara Tariq<sup>1</sup>, and Imon Banerjee<sup>1,3</sup>

<sup>1</sup> Department of Radiology, Mayo Clinic, Phoenix, Arizona, USA <EMAIL>

<sup>2</sup> Department of Radiology, UW Madison School of Medicine and Public Health, Madison, USA

<sup>3</sup> School of Computing and Augmented Intelligence, Arizona State University, Madison, USA

**Abstract.** A visual-language model (VLM) pre-trained on natural images and text pairs poses a significant barrier when applied to medical contexts due to domain shift. Yet, adapting or fine-tuning these VLMs for medical use presents considerable hurdles, including domain misalignment, limited access to extensive datasets, and high-class imbalances. Hence, there is a pressing need for strategies to effectively adapt these VLMs to the medical domain, as such adaptations would prove immensely valuable in healthcare applications. In this study, we propose a framework designed to adeptly tailor VLMs to the medical domain, employing selective sampling and hard-negative mining techniques for enhanced performance in retrieval tasks. We validate the efficacy of our proposed approach by implementing it across two distinct VLMs: the in-domain VLM (MedCLIP) and out-of-domain VLMs (ALBEF). We assess the performance of these models both in their original off-theshelf state and after undergoing our proposed training strategies, using two extensive datasets containing mammograms and their corresponding reports. Our evaluation spans zero-shot, few-shot, and supervised scenarios. Through our approach, we observe a notable enhancement in Recall@K performance for the image-text retrieval task (Code will be available at [https://github.com/aurooj/VLM](https://github.com/aurooj/VLM_SS.git) SS.git.).

**Keywords:** multimodal understanding · retrieval · vision and language · mammogram

### **1 Introduction**

According to the American Cancer Society (ACS) screening guidelines, women between 40 and 44 have the option to start screening with a mammogram every

Image /page/24/Figure/1 description: The image displays a diagram illustrating a vision language model for medical image retrieval. Part (a) shows a person sitting at a desk, looking at two computer monitors displaying mammogram images. Part (b) presents a schematic of the model, which takes bilateral mammogram images and a radiology report as input. These inputs are processed to create image embeddings and report embeddings, respectively. A Vision Language Model then processes these embeddings, mapping them into a joint embedding space. This space visualizes image and text data points, with arrows indicating positive and negative inter-modality and intra-modality relationships. The model's output is fed into a retrieval framework.

<span id="page-24-0"></span>**Fig. 1.** Multimodal learning for screening mammogram: (a) a session with radiology resident for the case review; (b) framework generating joint embedding space for bilateral mammogram and free-text radiology reports. Illustration of joint embedding space (right) is adapted from CrossCLR [\[21](#page-34-0)].

year and women 45 to 54 should get mammograms every year. This resulted in a huge number of screening mammogram exams at each healthcare institution and consumes significant radiologists' time for reading. During 12 weeks of required residency training in breast imaging, the Accreditation Council for Graduate Medical Education (ACGME) requires residents to document a minimum of 300 interpretations of breast imaging exams (mammograms, ultrasounds, MRI) and there are no particular criteria for training case-selection [\[4\]](#page-33-0). Even after this requirement, the majority (59%) of residents do not feel prepared to read mammograms after completing their training  $[2,3]$  $[2,3]$  $[2,3]$ . Unfortunately, the number of fellowship-trained breast imaging radiologists is expected to decline and thus the majority of residents will face reading mammography as part of their eventual clinical practice. The fundamental fear of misdiagnosis (missing a cancer) and the feeling that residency does not fully prepare them to read mammograms, likely contributes to an increase in additional mammogram scans to confirm diagnosis and incur avoidable cost and effort [\[12\]](#page-33-3). Thus, providing adequate training with relevant case selection within radiology residency will benefit more women and bestow safer mammographic interpretation. However, hand-picking a set of such relevant cases is both time-consuming and challenging, as well as can introduce sampling bias and is unlikely to match the desired distribution. Furthermore, most PACS systems have search tools with very limited search criteria which often result in countless useless cases. Deep learning retrieval framework has the potential to automate and optimize case selection from 100,000's of cases based on multimodal data - imaging features and textual findings documented within the reports.

We develop a multimodal framework to automatize the relevant case-selection based on both text and image representation of the individual screening exams (Fig. [1\)](#page-24-0). However, there are inherent technical challenges for training such a model - (i) natural image pre-trained VLM is often unable to capture the radiology vocabulary with selective terms, and also natural image features do not correspond well with gray-scale and small mammography findings; (ii) relevant

abnormal imaging findings (mass, calcification, architectural distortion, solitary dilated duct) are rare in screening mammogram which makes the model primarily learn the negative cases and omit the actual findings; (iii) syntactic difference between the semi-structured reports are minimal, and thus the reports with very different findings resulted similar embeddings; (iv) variations in breast density is often the most prominent image feature in mammogram and high density can occlude abnormal imaging features. To deal with the above-mentioned challenges, we propose a *knowledge-based grouping of the mammogram cases*, *selective sampling, and hard-negative mining techniques for VLM model training*. We validate the efficacy of our proposed approach across two distinct VLMs: the in-domain VLM (MedCLIP) and the out-of-domain VLM (ALBEF). Our evaluation spans zero-shot, few-shot, and supervised scenarios using Institute X datasets containing mammograms and their corresponding reports. The model was also externally validated on screening mammogram data from Institute Y.

## **2 Related Work**

*i. Vision-language model in radiology* - Several automated VLM efforts exist to generate radiology reports from images either as the template report generation task by filling with classified disease tag [\[18](#page-33-4)] or image-text generation task [\[1,](#page-32-0)[13,](#page-33-5) [14,](#page-33-6)[16](#page-33-7)]. However, most of the current VLM models in radiology are focused on 2D chest X-rays due to the availability of open-source datasets [\[9,](#page-33-8)[15\]](#page-33-9). Given the complexity of processing mammogram images(large dimension, varying density, multi-view), VLM literature is limited in the mammogram domain.

*ii. Multi-modal Retrieval in radiology* - Recently, multimodal retrieval using image-text contrastive pre-training is gaining interest. For example, X-REM [\[8\]](#page-33-10), CXR-RePaiR [\[5\]](#page-33-11), ConVIRT [\[19\]](#page-34-1), GLoRIA [\[6\]](#page-33-12), and MedClip [\[17\]](#page-33-13), leverage imagetext contrastive pre-training to retrieve relevant radiology reports based on image and text embeddings. Despite these innovations, current frameworks face notable challenges: they lack strategies to preserve representation for rare cases crucial for embedding space integrity and struggle with mining 'hard-negatives' in radiology, particularly evident in mammogram studies where templated reports often inadequately describe distinct image features. Addressing these limitations is critical for enhancing the effectiveness of multimodal retrieval systems in medical imaging.

## **3 Methodology**

Given a vision-language model  $f(\theta)$ , we want to train  $f(\theta)$  effectively such that similar image-text pairs  $(I_p, T_p)$  are close to each other in semantic space. Negative pairs are often picked within a batch from a different data sample. For any given medical sub-domain, the vocabulary to describe the observations largely stays consistent, particularly in mammograms as the reports are formulated following the standardized BIRADS vocabulary [\[10](#page-33-14)] generated by the American College of Radiology (ACR). These image-report pairs can be grouped based

Image /page/26/Figure/1 description: This diagram illustrates a machine learning workflow for medical image analysis. The top section shows a general process: data from an in-domain source is processed through steps 1, 2, and 3, leading to pretraining a Vision-Language Model (VLM). Step 1 involves extracting features like breast composition, calcification, asymmetry, mass, and surgical changes. Step 2 visualizes these features as a bar chart, and step 3 creates a minibatch for pretraining. The bottom section details two specific approaches: zero-shot learning and few-shot learning. In zero-shot learning, out-of-domain data is fed to a VLM. In few-shot learning, a support set is used with selective sampling to train a VLM. The diagram also includes three detailed sub-sections: 1. Knowledge Extraction, showing text describing breast characteristics and associated features like 'heterogeneously dense', 'architectural distortion', and 'mass'. 2. Knowledge-grounded Grouping, which categorizes image data based on extracted knowledge, creating groups like 'heterogeneously dense mass architectural distortion' (g1), 'heterogeneously dense implant' (g2), and 'scattered fibroglandular densities skin lesion mass asymmetry' (gM). 3. Selective Sampling, which presents a bar chart showing the occurrence of different groups (g1 to g11), distinguishing between 'frequent groups' and 'rare groups', and then shows a minibatch sampled from these groups.

<span id="page-26-0"></span>**Fig. 2.** Workflow for adapting the VLM with the proposed selective sampling to learn joint representation aware of fine-grained knowledge. The pretrained model is tested on out-of-domain data for zero-shot evaluation. For few-shot learning, a support set is obtained from the training data to fine-tune the model.

on the important findings in a way that each image-report pair with the same concepts belongs to one group. Additionally, for mammograms, broad features are visually similar to each other and need a domain expert, i.e., a radiologist to examine for anomalies. Given the textual and visual similarity between the cases, there is a high chance that the sampled 'negative' image  $I_n$  or text report  $T_n$ has similar findings as the true pair does. This leads to confusion during model training because it might be pushing away semantically similar image-text pairs (Fig. [2\)](#page-26-0).

We propose a knowledge-grounded mini-batch sampling ensuring batch negatives to be coming from true negatives and minority cases are equally represented during training. This is achieved in three steps as described below:

*1) Knowledge extraction:* To form the groups, we leveraged the standard 54 unique BIRADS image descriptors and extracted the positive mentioned from the radiology reports which are lower cased and cleaned before extracting key concepts. For example, for the following text report: "*the breasts are heterogeneously dense*, *which may obscure small masses. left mass: there is a mass seen in the left breast at 3 o'clock. associated features include architectural distortion right there are no significant masses, calcifications, or other findings"*, the extracted group is {heterogeneously dense, mass, architectural distortion} based on the key concepts highlighted in blue. The abnormal image descriptors are primarily categorized into 5 groups - breast composition, calcification, asymmetry, mass, and surgical changes. All of these concepts except tissue density may or may not be present in the normal image without anomaly. We excluded all the negative and uncertain findings.

*2) Knowledge grounded grouping:* The presence of a key concept combination in any exam is considered a group such that every other image with the same

key concepts present belongs to the same group. All text reports with the same key concepts (even ordered differently  $-$  vs  $< B, C, A>$ ) belong to the same group. This yields a unique set of groups from the extracted knowledge for the given dataset. Formally, a group  $g_i \in G^M$  for  $i \in 1, 2, ..., M$  is a set of key concepts within an image extracted from the paired radiology report, where  $G^M$  is the set of M total groups extracted from the text reports. Negatives are defined as image-text pairs belonging to a different group while some features may be common between them, e.g., positive group  $\langle A, B \rangle$  vs negative group  $\langle A, B, C \rangle$ .

*3) Selective sampling:* Given an image  $I_p$  and paired text report  $T_p$  as  $(I_p, T_p)$ , a negative pair is denoted by  $(I_p, T_n)$  or  $(I_n, T_p)$ , where  $I_n$  and  $T_n$  belong to an instance from a different group. For each pair  $(I_{p_i}, T_{p_i})$  from group  $g_i$ , a negative image  $I_{n_j}$  or text  $T_{n_j}$  can be selected from group  $g_j \in G^M$  when  $j \neq i$ . This approach while addressing the challenge of alike image-text pairs within a mini-batch, still faces the long-tail distribution challenge due to class imbalance. As frequent groups have a high chance of being sampled, rare groups often might never be seen during training. To address this problem, a mini-batch is sampled based on the group frequency. We define a heuristic-based boundary b to sample rare groups such that  $b < batch\_size$  and  $batch\_size - b$  instances are selected from groups with high occurrence, i.e., frequent groups. This ensures that b instances are coming from rare groups, where rare and frequent groups are empirically chosen based on the data distribution.

*4. VLM training:* The proposed sampling strategy is used to sample minibatches to train the vision-language model for contrastive learning. We use sampling strategy in two settings: pretraining and few-shot learning across two existing VLMs: ALBEF [\[11\]](#page-33-15) and MedCLIP [\[17\]](#page-33-13). To measure the performance, we consider the Recall@K metric and report top-1, top-5, and top-10 performance. We consider it a success if any report with the same findings (hence the same group) appears in the top-K ranks.

# **4 Experiments and Results**

*Datasets: Internal Dataset:* Using IRB approval, we collected 72,328 bilateral screening mammogram exams from 46,848 patients acquired between January 2016 and December 2018 from UW Madison health affiliated centers as our internal dataset. We randomly split the dataset into train-val-test with 70,238  $\langle image-report \rangle$  pairs used for training, 1000 image-report pairs for validation, and 1000 image-report pairs as a test set respectively. We use a binary mask of thresholded pixel values to identify the largest connected component to crop the breast tissue area. The cropped R-MLO and L-MLO images are concatenated, zero-padded for maintaining the aspect ratio, and resized to  $512 \times 512$ pixels. Reports are cleaned by lowercasing, punctuation removal, and extra spacing removal. The text is then split into sentences, each examined for key concepts: density, calcifications, asymmetry, architectural distortion, mass, and

| Task            | Model               | Internel test set |              |              | Externel test set |              |              |
|-----------------|---------------------|-------------------|--------------|--------------|-------------------|--------------|--------------|
|                 |                     | $R@1$             | $R@5$        | $R@10$       | $R@1$             | $R@5$        | $R@10$       |
| Image-to-Report | NN(k=10)            | 10.1              | -            | -            | 3.34              | -            | -            |
|                 | ALBEF-Ret           | 12.9              | 37.0         | 47.2         | 19.00             | 50.21        | 65.76        |
|                 | ALBEF-SS-PT (ours)  | 9.0               | 32.3         | 40.2         | 20.25             | 48.75        | 51.56        |
|                 | ALBEF-SS-Ret (ours) | <b>30.5</b>       | <b>53.9</b>  | <b>61.3</b>  | <b>21.61</b>      | <b>46.03</b> | <b>55.22</b> |
|                 | MedCLIP             | 6.4               | 11.2         | 15.1         | 16.6              | 30.27        | 35.17        |
|                 | MedCLIP-SS (ours)   | <b>5.10</b>       | <b>10.60</b> | <b>14.90</b> | <b>4.28</b>       | <b>11.69</b> | <b>20.98</b> |
| Report-to-Image | NN(k=10)            | 26.4              | -            | -            | 36.95             | -            | -            |
|                 | ALBEF-Ret           | 28.6              | 60.5         | 65.2         | 34.13             | <b>82.98</b> | 83.82        |
|                 | ALBEF-SS-PT (ours)  | 19.4              | 60.7         | 67.6         | <b>63.88</b>      | 81.73        | 84.76        |
|                 | ALBEF-SS-Ret (ours) | <b>35.8</b>       | <b>63.3</b>  | <b>73.4</b>  | <b>54.70</b>      | <b>81.94</b> | <b>85.49</b> |
|                 | MedCLIP             | 26.70             | 48.40        | 56.30        | 0.31              | 20.77        | 22.02        |
|                 | MedCLIP-SS (ours)   | <b>31.5</b>       | <b>62.3</b>  | <b>66.2</b>  | <b>0.52</b>       | <b>21.4</b>  | <b>24.22</b> |

<span id="page-28-0"></span>**Table 1.** Comparative retrieval results for the proposed knowledge grounded selective sampling (SS) on both internal (UW Madison) and external (Mayo Clinic) test sets. 'Ret': fine-tune models, 'PT': pre-trained model. Numbers are in percentages.

additional features. This grouping allows selective sampling during model training as described in Sect. [3.](#page-26-0) We find 1005 unique groups in the train set. Detailed group distribution is provided in the supplementary document.

*External Dataset:* With the Mayo Clinic IRB approval, the screening mammogram collected between 2018 - 2022 is used for external validation of our approach for supervised training as well as few-shot learning. The Mayo dataset has 8,172 training image-report pairs and 1,015 pairs in the test set. The test set is then used for external validation. The test set has 79 unique groups after preprocessing as described in Sect. [3.](#page-26-0)

*Implementation Details:* ALBEF [\[11](#page-33-15)] is a VLM with image-text contrastive loss. We pre-train ALBEF on UW Madison image-report pairs, followed by a retrieval-only task Image Text Matching (ITM) for fine-tuning the pretrained backbone named ALBEF-Ret. For a  $512 \times 512$  image and the patch size of  $16 \times 16$ , image encoder takes 1024 patch tokens in the ALBEF model. We train ALBEF with (ALBEF-SS) and without (ALBEF-Ret) the proposed selective sampling. We evaluate MedCLIP [\[17](#page-33-13)] pretrained on CheXpert dataset [\[7](#page-33-16)] and MIMIC-CXR [\[9\]](#page-33-8) for zero-shot, initialize model weights for few-shot learning, and train MedCLIP on the 2D mammogram images for fully supervised backbone. Similar to ALBEF, we also trained MedCLIP with (MedCLIP-SS) and without (MedCLIP) the proposed selective sampling. For full training, we consider the

| Task            | K  | Model               | Internal test set |       |       | External test set |       |       |
|-----------------|----|---------------------|-------------------|-------|-------|-------------------|-------|-------|
|                 |    |                     | R@1               | R@5   | R@10  | R@1               | R@5   | R@10  |
| Image-to-Report | ZS | MedCLIP-ViT         | 1.9               | 12.0  | 20.5  | 25.71             | 38.42 | 40.79 |
|                 |    | ALBEF-mscoco        | 16.8              | 32.0  | 40.5  | 14.61             | 36.01 | 43.11 |
|                 |    | ALBEF-flickr30k     | 20.0              | 31.1  | 37.5  | 7.83              | 33.82 | 40.29 |
|                 |    | ALBEF-SS-Ret (ours) | -                 | -     | -     | 21.61             | 46.03 | 55.22 |
|                 | 10 | MedCLIP             | 0.1               | 3.1   | 6.8   | 32.36             | 48.43 | 57.09 |
|                 |    | MedCLIP-SS          | 2.2               | 8.0   | 14.1  | 18.00             | 36.22 | 41.44 |
|                 |    | ALBEF               | 19.5              | 46.9  | 55.0  | 0.3               | 29.96 | 55.01 |
|                 |    | ALBEF-SS-Ret        | 25.40             | 48.10 | 57.40 | 20.88             | 46.76 | 56.47 |
| Report-to-Image | ZS | MedCLIP-ViT         | 24.1              | 42.6  | 46.6  | 35.66             | 55.37 | 81.48 |
|                 |    | ALBEF-mscoco        | 5.6               | 41.2  | 48.7  | 1.36              | 35.07 | 68.37 |
|                 |    | ALBEF-flickr30k     | 2.2               | 44.3  | 50.5  | 0.32              | 61.17 | 57.74 |
|                 |    | ALBEF-SS-Ret (ours) | -                 | -     | -     | 54.70             | 81.94 | 85.49 |
|                 | 10 | MedCLIP             | 3.3               | 38.6  | 46.4  | 1.57              | 36.64 | 57.20 |
|                 |    | MedCLIP-SS          | 6.6               | 33.2  | 54.6  | 36.95             | 55.53 | 56.68 |
|                 |    | ALBEF               | 32.9              | 65.9  | 75.0  | 36.74             | 68.99 | 81.84 |
|                 |    | ALBEF-SS-Ret        | 31.6              | 67.3  | 73.2  | 35.39             | 78.29 | 80.06 |

<span id="page-29-0"></span>**Table 2.** Zero-shot (ZS) and few-shot  $(K = 10)$  results for image $\leftrightarrow$ report retrieval. MedCLIP-ViT is pretrained on chest x-rays [\[7,](#page-33-16)[9\]](#page-33-8), MedCLIP and MedCLIP-SS are trained on the screening mammogram exams. Numbers are in percentages.

top 20 groups w.r.t the number of samples as frequent groups out of a total of 1005 unique groups. We use batch size  $= 8$  and boundary b  $= 3$  for random sampling of frequent and rare groups, i.e., for  $R = 0.375 - 5$  instances belong to frequent groups, and 3 are sampled from the set of rare groups. All training parameters except the hyperparameters stayed the same across models.

**Results:** We evaluate the learned joint embedding using image⇔text retrieval (ITR) as our downstream task. We compare ALBEF with ALBEF-SS, and MedCLIP with MedCLIP-SS to assess the impact of selective sampling during training. We observe improvement for both VLMs with selective sampling for image-to-report and report-to-image retrieval on our internal test set as well as external test data. Table [1](#page-28-0) presents the complete results on the internal and external data. More specifically, on the internal test set, ALBEF-SS-Ret obtains 17.6% ↑ gain in R@1 performance, ∼17%↑ improvement in R@5, and 14.1%↑ increase in R@10 score over ALBEF-Ret model for image-to-report retrieval. For report-to-image retrieval, ALBEF-SS-Ret improves by  $7.2\%$   $\uparrow$  at R@1, 2.8%  $\uparrow$  at R@5, and 8.2%  $\uparrow$  at R@10 scores. MedCLIP-SS achieves comparable results to the MedCLIP baseline for R@5 and R@10. For report-to-image retrieval, MedCLIP-SS achieves a performance gain of 4.8%↑ in R@1, 1.8%↑ as R@5, and with a significant margin of  $\sim 10\%$ <sup>†</sup> in R@10 respectively. Overall, we observe

that image-to-report retrieval is a more challenging task for VLMs compared to report-to-image retrieval. On the external test set, ALBEF-SS-Ret model although improves over ALBEF by  $2.61\%$  in terms of R@1, performance is hurt on R@5 and R@10. Similar behavior is observed for MedCLIP-SS as well. However, we notice a consistently significant improvement in both ALBEF-SS-Ret and MedCLIP-SS for report-to-image retrieval. MedCLIP-SS consistently performs better than MedCLIP in terms of R@1, R@5, and R@10 respectively.

*Zero-Shot Retrieval:* We further compare the zero-shot performance on the external test set from Mayo using off-the-shelf models: MedCLIP-ViT, MSCOCO-pretrained ALBEF, and Flick30K-pretrained ALBEF and compare to ALBEF-SS-Ret pretrained on ∼70K internal samples. For image-to-report, MedCLIP-ViT obtains the best  $R@1$  score: 25.7% vs. second-best 21.61% from ALBEF-SS-Ret. ALBEF-SS-Ret outperforms MedCLIP-ViT on R@5 and R@10 by 7.61% ↑ and 14.43% ↑ respectively. For report-to-image retrieval, ALBEF-SS-Ret outperforms MedCLIP-ViT by 19.04%  $\uparrow$ , 26.57%  $\uparrow$ , and 4.01%  $\uparrow$  in terms of R@1, R@5, and R@10 respectively. See Table [2](#page-29-0) for complete results (Fig. [3\)](#page-30-0).

Image /page/30/Figure/3 description: The image displays two sets of mammograms, each with a query and corresponding descriptions. The left set is queried as "irregularly shaped mass" and shows five mammograms under the labels "ALBEF" and "ALBEF-SS". The right set is queried as "coarse heterogeneous calcifications" and also shows five mammograms under the same labels. Each mammogram is accompanied by a text description. The bottom of the image contains labels (a) and (b) below the respective sets of mammograms.

<span id="page-30-0"></span>**Fig. 3.** Qualitative results for Retrieval model. Samples with highlighted green words are marked relevant by a radiologist and in pink, show not exact but related findings in the image-report pair. (Color figure online)

*Few-Shot Retrieval:* For the few-shot learning setup, we sampled up to  $K=10$ instances for each group from an internal training set. For groups with less than 10 instances, we keep all available instances. This resulted in 3,331 unique training image-report pairs.

*Internal Test Set:* For image-to-report retrieval evaluation, ALBEF-SS-Ret outperforms ALBEF on all three metrics. MedCLIP-SS also demonstrates consistent improvements across all metrics with at least 50% relative performance gain over MedCLIP. For report-to-image, MedCLIP shows improvement in R@1 (3.3%↑) and R@10 (8.2%↑). ALBEF-SS-Ret shows overall comparable performance to ALBEF with a slight gain in the R@5 score.

*External Test Set:* We observe that ALBEF-SS-Ret performs significantly better than its counterpart (R@1 score: 20.88% vs 0.3%, R@10: 46.76% vs 29.96%) when doing image-to-report retrieval during external validation. For report-to-image retrieval, it improves R@5 by approx. 10 points while performing comparable to ALBEF on R@1 and R@10. MedCLIP-SS, in comparison with MedCLIP, also shows significant improvement for R@1 (36.95% vs  $1.57\%$ ) and R@5 (55.53%) vs 36.64%) scores respectively on report-to-image retrieval task, but shows the opposite trend on image-to-report retrieval. Overall, we observe that selective sampling consistently benefits the ALBEF model for both internal and external validation. MedCLIP-SS, on the other hand, while being beneficial for internal testing as well as for external validation of report-to-image retrieval performance, seems to be less effective for out-of-domain image-to-report retrieval. This is consistent with the trends observed while performing external validation of MedCLIP-SS when trained on the full training set. We need to re-calibrate the frequent groups to benefit from selective sampling based on the support set's group distribution.

<span id="page-31-0"></span>

| Method                          |       | Image-to-Report |       | Report-to-Image |       |       |  |
|---------------------------------|-------|-----------------|-------|-----------------|-------|-------|--|
|                                 | R@1   | R@5             | R@10  | R@1             | R@5   | R@10  |  |
| $(1)$ R = 0.25                  | 0.4   | 1.5             | 2.4   | 3.2             | 29.2  | 41.6  |  |
| $(2)$ R = 0.38                  | 0.4   | 2.8             | 8.7   | 15.7            | 30.7  | 51.3  |  |
| $(3)$ R = 0.50                  | 0.1   | 1.8             | 5.2   | 17.7            | 41.2  | 58.9  |  |
| $(4)$ R = 0.75                  | 0.5   | 5.2             | 7.7   | 1.4             | 26.3  | 28.9  |  |
| $(5)$ w/ B shuffle              | 0.3   | 1.8             | 6.8   | 17.1            | 24.7  | 42.6  |  |
| $(6)$ w/o B shuffle             | 0.4   | 2.8             | 8.7   | 15.7            | 30.7  | 51.3  |  |
| (7) Freq. groups, fixed         | 17.00 | 44.30           | 55.30 | 32.90           | 66.50 | 73.80 |  |
| $(8)$ Freq. groups, recalibrate | 25.40 | 48.10           | 57.40 | 31.60           | 67.30 | 73.20 |  |

**Table 3.** Ablations for the proposed sampling strategy on Institute X using MedCLIP-SS model.  $B = \text{batch size}, R = \text{ratio of frequent groups to rare groups in a batch.}$ 

*Ablations and Analyses:* Table [3](#page-31-0) reports the selected ablations from our detailed analyses regarding important hyperparameters such as #samples from frequent vs. rare groups, recalibrating no. of frequent groups with change in data distribution that happens during few-shot learning, and choice of mini-batch shuffling after our selective sampling. We used MedCLIP-SS with few-shot learning  $(K=10)$  in all ablations unless specified otherwise. See additional results in the supplementary document.

### **5 Discussion and Conclusion**

Training a large network on medical data, particularly with contrastive loss, is always challenging when the dataset is highly influenced by the majority of 'normal' cases and instances with compelling representation (image or textual) are extremely rare. Our proposed knowledge-grounded selective sampling strategy helps the contrastive model training by ensuring the sampling of the true negatives and equalizing representation of rare cases. We observed improvement in the retrieval performance with the selective sampling strategy, especially for the ALBEF model. For MedCLIP, we observed improvement for internal evaluation; however, there was no improvement on the external dataset for image-to-report which could be based on the fact that image-to-text retrieval is a more challenging task and we didn't pre-train the MedCLIP on the mammogram dataset. However, we still observed MedCLIP performance improvement on the external dataset for report to image particularly in R@1 and R@5 for few-shot learning. On the zero-shot performance, our pre-trained model also outperformed all the baselines, including MedCLIP-VIT, on the external dataset for both image-toreport and report-to-image retrieval tasks. It is also highlighted in the domain of LLMs that few-shot learning can be highly sensitive to the quality of the demonstrations, emphasizing the need for strategies to strategically select fewshot [\[20\]](#page-34-2).

Based on the ablation study, we also present the fact that proposed selective sampling can help to train the VLM model with a smaller batch size for a limited resource setting. However, thorough experimentation needs to be done with intelligent sampling to balance the groups for larger batch sizes to properly understand the relationship between the number of groups and the batch size.

In summary, our proposed sampling strategy lays the groundwork to rethink data sampling strategies for effective training of multimodal networks as well as for in-context learning, case-in-point, vision-language models grounded in the multimodal data for medical contexts.

**Acknowledgement.** Research reported in this paper was supported by NCI of the National Institutes of Health under award number 1R37CA262110-01A1 and NIH/NCI, U01 CA269264-01-1.

**Disclosure of Interests.** John Garrett has received research grants from Flywheel.io, GE Healthcare, and Optum, is a member of the SIIM Machine Learning Tools and Research Committee, owns stock in NVIDIA, and is an advisor to and holds equity in RadUnity.

### **References**

<span id="page-32-0"></span>1. Alfarghaly, O., Khaled, R., Elkorany, A., Helal, M., Fahmy, A.: Automated radiology report generation using conditioned transformers. Inform. Med. Unlocked **24**, 100557 (2021)

- <span id="page-33-1"></span>2. Bassett, L.W., Monsees, B.S., Smith, R.A., Wang, L., Hooshi, P., Farria, D.M., Sayre, J.W., Feig, S.A., Jackson, V.P.: Survey of radiology residents: breast imaging training and attitudes. Radiology **227**(3), 862–869 (2003)
- <span id="page-33-2"></span>3. Beam, C.A., Layde, P.M., Sullivan, D.C.: Variability in the interpretation of screening mammograms by us radiologists: findings from a national sample. Archives of internal medicine **156**(2), 209–213 (1996)
- <span id="page-33-0"></span>4. Davis, D.J., Ringsted, C.: Accreditation of undergraduate and graduate medical education: how do the standards contribute to quality? Advances in health sciences education **11**, 305–313 (2006)
- <span id="page-33-11"></span>5. Endo, M., Krishnan, R., Krishna, V., Ng, A.Y., Rajpurkar, P.: Retrieval-based chest x-ray report generation using a pre-trained contrastive language-image model. In: Machine Learning for Health, pp. 209–219. PMLR (2021)
- <span id="page-33-12"></span>6. Huang, S.C., Shen, L., Lungren, M.P., Yeung, S.: Gloria: A multimodal global-local representation learning framework for label-efficient medical image recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 3942–3951 (2021)
- <span id="page-33-16"></span>7. Irvin, J., et al.: Chexpert: a large chest radiograph dataset with uncertainty labels and expert comparison. In: Proceedings of the AAAI Conference on Artificial Intelligence, vol. 33, pp. 590–597 (2019)
- <span id="page-33-10"></span>8. Jeong, J., et al.: Multimodal image-text matching improves retrieval-based chest x-ray report generation. In: Medical Imaging with Deep Learning, pp. 978–990. PMLR (2024)
- <span id="page-33-8"></span>9. Johnson, A.E., et al.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Sci. Data **6**(1), 317 (2019)
- <span id="page-33-14"></span>10. Lazarus, E., Mainiero, M.B., Schepps, B., Koelliker, S.L., Livingston, L.S.: Bi-rads lexicon for us and mammography: interobserver variability and positive predictive value. Radiology **239**(2), 385–391 (2006)
- <span id="page-33-15"></span>11. Li, J., Selvaraju, R.R., Gotmare, A.D., Joty, S., Xiong, C., Hoi, S.: Align before fuse: vision and language representation learning with momentum distillation. In: NeurIPS (2021)
- <span id="page-33-3"></span>12. Miglioretti, D.L., Gard, C.C., Carney, P.A., Onega, T.L., Buist, D.S., Sickles, E.A., Kerlikowske, K., Rosenberg, R.D., Yankaskas, B.C., Geller, B.M., et al.: When radiologists perform best: the learning curve in screening mammogram interpretation. Radiology **253**(3), 632–640 (2009)
- <span id="page-33-5"></span>13. Mohsan, M.M., Akram, M.U., Rasool, G., Alghamdi, N.S., Baqai, M.A.A., Abbas, M.: Vision transformer and language model based radiology report generation. IEEE Access **11**, 1814–1824 (2022)
- <span id="page-33-6"></span>14. Nooralahzadeh, F., Gonzalez, N.P., Frauenfelder, T., Fujimoto, K., Krauthammer, M.: Progressive transformer-based generation of radiology reports. arXiv preprint [arXiv:2102.09777](http://arxiv.org/abs/2102.09777) (2021)
- <span id="page-33-9"></span>15. Wang, X., Peng, Y., Lu, L., Lu, Z., Bagheri, M., Summers, R.M.: Chestx-ray8: hospital-scale chest x-ray database and benchmarks on weakly-supervised classification and localization of common thorax diseases. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pp. 2097–2106 (2017)
- <span id="page-33-7"></span>16. Wang, Y., et al.: Trust it or not: Confidence-guided automatic radiology report generation. Neurocomputing, p. 127374 (2024)
- <span id="page-33-13"></span>17. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text (2022)
- <span id="page-33-4"></span>18. You, D., Liu, F., Ge, S., Xie, X., Zhang, J., Wu, X.: Aligntransformer: Hierarchical alignment of visual regions and disease tags for medical report generation. In:

Medical Image Computing and Computer Assisted Intervention–MICCAI 2021: 24th International Conference, Strasbourg, France, September 27–October 1, 2021, Proceedings, Part III 24, pp. 72–82. Springer (2021)

- <span id="page-34-1"></span>19. Zhang, Y., Jiang, H., Miura, Y., Manning, C.D., Langlotz, C.P.: Contrastive learning of medical visual representations from paired images and text. In: Machine Learning for Healthcare Conference, pp. 2–25. PMLR (2022)
- <span id="page-34-2"></span>20. Zhao, Z., Wallace, E., Feng, S., Klein, D., Singh, S.: Calibrate before use: Improving few-shot performance of language models. In: International Conference on Machine Learning, pp. 12697–12706. PMLR (2021)
- <span id="page-34-0"></span>21. Zolfaghari, M., Zhu, Y., Gehler, P., Brox, T.: Crossclr: cross-modal contrastive learning for multi-modal video representations. In: Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 1450–1459 (2021)

Image /page/35/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line on the left side and a flag shape on the right side. The button is light gray with a subtle gradient, and the text is dark gray.

# Learnable Skeleton-Based Medical Landmark Estimation with Graph Sparsity and Fiedler Regularizations

Yao Wang<sup>1</sup>, Jiahao Chen<sup>1</sup>, Wenjian Huang<sup>2</sup>, Pei Dong<sup>3</sup>, and Zhen Qian<sup>1( $\boxtimes$ )</sup>

<sup>1</sup> United-Imaging Research Institute of Intelligent Imaging, Beijing, China {yao.wang,zhen.qian}@cri-united-imaging.com

<sup>2</sup> Southern University of Science and Technology, Shenzhen, China <sup>3</sup> United-Imaging Intelligent, Beijing, China

Abstract. Recent development in heatmap regression-based models have been central to anatomical landmark detection, yet their efficiency is often limited due to the lack of skeletal structure constraints. Despite the notable use of graph convolution networks (GCNs) in human pose estimation and facial landmark detection, manual construction of skeletal structures remains prevalent, presenting challenges in medical contexts with numerous non-intuitive structure. This paper introduces an innovative skeleton construction model for GCNs, integrating graph sparsity and Fiedler regularization, diverging from traditional manual methods. We provide both theoretical validation and a practical implementation of our model, demonstrating its real-world efficacy. Additionally, we have developed two new medical datasets tailored for this research, along with testing on an open dataset. Our results consistently show our method's superior performance and versatility in anatomical landmark detection, establishing a new benchmark in the field, as evidenced by extensive testing across diverse datasets.

**Keywords:** Graph Convolution Networks  $\cdot$  Fiedler Regularizations  $\cdot$  Graph Sparsity  $\cdot$  Landmark Detection

# 1 Introduction

The precise and robust methods of anatomical landmarks localization in medical images are helpful for various diagnoses and treatment procedures [\[12,](#page-44-0)[26](#page-44-1)]. The connections between anatomical landmarks termed as the skeletal structure contain valuable anatomical and shape information. Graph Convolutional Networks (GCNs) are adept at capturing graph node attributes and relational structures through a sequence of graph-level convolutions [\[33](#page-45-0)].

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_56.](https://doi.org/10.1007/978-3-031-72390-2_56)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 599–609, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_56)\_56

In most previous research [\[2](#page-43-0),[3\]](#page-43-1), the construction of skeletal structures typically relies on predefined manual designs based on prior knowledge and assumptions about anatomy. However, in medical imaging, designing skeletal structures becomes more challenging due to the abundance of anatomical landmarks that frequently lack intuitive structure. This complexity makes it difficult to create a graph that accurately represents the intricate relationships between landmarks. Additionally, adopting adaptive learning for graph connectivities, instead of predetermined structures, could markedly improve the model's generalization capabilities. Adaptive methods potentially allow the model to better accommodate unique and variable characteristics leading to more accurate and reliable analyses.

When considering the connectivity of the graph, it is essential to strike a balance between too few and excessive connected edges [\[10](#page-44-2)[,13](#page-44-3)]. Therefore, in this study, we aim to address a crucial question: Is it feasible to learn skeletal structures directly without the need for manual design across various tasks and attain outstanding performance through the network?

Algebraic connectivity, often referred to as the Fiedler value and denoted by  $\lambda_2$ , is a key concept in spectral graph theory [\[22](#page-44-4), [23\]](#page-44-5).  $\lambda_2$  denotes the second smallest eigenvalue of the Laplacian Matrix of a graph. This value reflects the connectivity and sparsity of the graph. Optimizing  $\lambda_2$  reduces unnecessary and/or detrimental connections, leading to a more efficient network. It has received much attention for optimizing connections in various fields  $[6,9,11,15]$  $[6,9,11,15]$  $[6,9,11,15]$  $[6,9,11,15]$  $[6,9,11,15]$  $[6,9,11,15]$ , such as optical communication satellite networks, digital logistics networks and so on.

In this paper, we introduce a new method for learning skeletal structures, the Fiedler-regularized Graph Convolution Network (FRGCN), specifically designed to minimize the Fiedler value of a graph. The main contributions of this work can be summarized as:

- We present a novel skeleton reconstruction model using Fiedler regularization, which introduces graph-derived structural constraints to GCNs, representing a significant shift from traditional manual methods.
- We introduce the FRGCN, an effective model for landmark detection, which includes a Target-aware Encoder (TAE) and a Skeleton-aware Encoder (SAE). The TAE is crafted to capture information about landmarks and their interrelations, while the SAE is tailored to enforce constraints on skeletal structures.
- Extensive experiments show that FRGCN consistently outperforms SOTA methods on three medical image datasets.

# 2 Method

### 2.1 Related Work

Recent advancements in deep learning have showcased its efficacy in medical landmark detection [\[16](#page-44-8)[,34](#page-45-1)]. These methodologies can generally be categorized into three types: coordinate-based [\[24\]](#page-44-9), heatmap-based [\[27\]](#page-44-10), and graph-based

approaches [\[35](#page-45-2)]. Graph-based methods, often building upon the other two methods, utilize GCNs to learn the interrelations of landmarks. Most GCNs are built on skeletal structures that are manually designed. To accommodate the changing relationships among human keypoints, dynamic graph convolution network models [\[17\]](#page-44-11) dynamically select preferred structures from manually pre-designed skeletons during training. Yet, these structural constraints are not applied during inference. While RSGNet [\[7\]](#page-43-4) has improved upon this limitation, it still relies on manually designed priors, making the structural design for landmarks with non-intuitive relationships a persistent challenge.

Some approaches do not rely on manually designed structures, such as graph matching models [\[35\]](#page-45-2) and deformable shape models [\[30\]](#page-45-3). However, their performance is significantly influenced by the initial configuration of the skeletal structures. Additionally, transformer-based methods enhance global relationships through attention mechanisms [\[19](#page-44-12)], eliminating the need for manually designed structures. However, these methods come with additional computational costs and may not perform as well on smaller datasets [\[36\]](#page-45-4). Consequently, there is an urgent need for a computationally inexpensive method for the automatic learning of skeletal structures.

#### 2.2 Target-Aware Encoder (TAE)

In this section, we introduce the three components of the FRGCN using lower limb landmark detection as an illustrative example. The lower limb landmarks in this study, based on DR images, are displayed in supplement Fig. [1.](#page-38-0) Here, a total of 20 landmarks are annotated to aid in the analysis of the mechanical axis of the lower limbs [\[8](#page-43-5),[18\]](#page-44-13).

Given an input image  $I = \left\{I_r \in R^{H_r \times W_r}\right\}_{r=1}^N$ , where N represents the number of lower limb images and r is the image index, and  $H_r$  and  $W_r$  denote the input image height and width respectively. The positions of the  $K$  landmarks are represented by a set of coordinates  $P \in R^{K \times 2}$ .

First, we adopt HRNet  $[21]$  $[21]$  as a backbone to extract visual features  $f_r$ . Given the feature  $f_r$ , candidate landmarks' position  $P' \in R^{K \times 2}$  can be generated. In order to further refine the coordinates for each landmarks, we calcuate the position vector  $f_b \in [x, y, \Delta x, \Delta y]$ , where  $(x, y)$  denotes the coordinates of the candidate landmark, and  $(\Delta x, \Delta y)$  signifies the offset between the landmark and the center point of the image. The position encoder enhance the encoding of vector  $f_b$ .

To further aggregate the information between the visual vectors and position vectors, we also add spatial attention module [\[37](#page-45-5)]. The process is as follows:

$$
E_c = \psi_c(w_c, concat(\psi_r(w_r, f_r), \psi_b(w_b, f_b))),
$$
\n<sup>(1)</sup>

where  $\psi$  represents the encoding process.  $\psi_r, \psi_b$  are the visual and position encoder with parameters  $w_r$  and  $w_b$ .  $\psi_c$  is the spatial attention module of the weights  $w_c$ .  $E_c$  represents the encoded vector output of the TAE.

Image /page/38/Figure/1 description: This figure illustrates a deep learning model for analyzing limb alignment from X-ray images. The model consists of two main components: the Transformer Attention Encoder (TAE) and the Spectral Attention Encoder (SAE). The TAE takes an input X-ray image and processes it through a Visual Encoder and a Position Encoder. These are then combined and fed into a Spatial Attention Module, which refines the features. The output of the TAE is a feature vector, fq, which is used by Landmark and Limbs Decoders to predict landmark points and limb alignment on the X-ray images, denoted by losses LLD and LRL respectively. The SAE component takes a Fiedler diagram, converts it into a Fiedler matrix, and processes it through Non-linear Layers to produce a feature vector, wl, with loss LFS. The figure also shows three different graph structures labeled E-32, E-40, and E-48 in section (c), likely representing different graph configurations used in the model. The legend at the bottom explains the symbols used: concatenation (C), sigmoid (σ), and multiplication (⊗), along with feature vector and convolution representations.

<span id="page-38-0"></span>Fig. 1. The framework of our Graph Convolutional Network using Fiedler Regularization (FRGCN). (a) The Target-aware Encoder (TAE) comprises visual encoders  $\psi_r$ , positional encoders  $\psi_b$  and spatial attention module  $\psi_c$ . (b) The Skeleton-aware Encoder (SAE) utilizes Fiedler regularization to model Fiedler matrix and then updates using the non-linear layers. The SAE serves as the graph encoding module  $\psi_l$ .  $f_q$  is the feature vector aggregating the encoding outputs of TAE and SAE. The Fiedler diagram demonstrate the principle of minimizing the Fiedler value of the graph. We illustrate the FRGCN-based graph cut by selecting a representative landmark highlighted in red. This division into two subgraphs is represented by the yellow and green lines, positioned above and below, respectively. Nodes on the plane are categorized into lighter and brighter shades. (c) Illustrative diagrams depicting manually designed edges connecting lower limb points, featuring configurations with 32, 40, and 48 points. (Color figure online)

#### 2.3 Graph Convolution Network Using Fiedler Regularization

In a landmark graph  $G = (V, E)$ , the vertex set V comprises all the landmarks, and  $E$  is the edge set. The weights of  $E$  are either 0 or 1, indicating the presence or absence of connections between landmarks. The learning process for skeletal structures becomes non-differentiable due to these dichotomized values, presenting a challenge for gradient-based optimization methods commonly used in neural networks.

In a general graph, the basic unit of graph connectivity consists of two nodes connected by an edge. We designate a new graph  $G' = (V', E', |F|)$  based on these units. Here, the vertex set  $V'$  includes all newly defined nodes, with each node representing a pair of connected nodes and the intervening edge in G, leading to a size of  $(K(K-1), 1)$ , where K is the number of original nodes in G. The edge set E<sup> $\prime$ </sup> corresponds to all possible connections within  $V$ <sup> $\prime$ </sup>, and we assume that all edges in  $E'$  to be present. The set F contains the weights of the edges in E', where each weight  $F_{ij}$  is a real number within the range  $[-1, 1]$ , and i and j indicate the respective rows and columns in  $F$ . The graph  $G'$  is expressed in matrix form. The degree matrix  $D'$  is derived from  $G'$ , with each

diagonal element  $D'_{ii}$  calculated as the sum of the absolute values of the weights connected to the *i*-th vertex,  $\sum_{j=1}^{n} |F|_{ij}$ . Subsequently, the Laplacian matrix of the graph is defined as  $L' = D' - |F|$ , which plays a critical role in analyzing the graph's properties.

<span id="page-39-0"></span>

#### Algorithm 1. The step of FRGCN

**Input:** Training data  $\{I_r\}_{r=1}^N$ <br>**Hyperparameters:** Learning rate  $\eta$ , batch size m, parameter  $\gamma_1, \gamma_2, \gamma_3$ , updating period T Initialize parameters  $W = \{w_c, w_r, w_b, W_l, F\}$  of the network Compute the Laplacian  $\mathbf{L}'$  of the neural network Compute the Fiedler vector  $\mathbf{v}_2$  of the Laplacian  $\mathbf{L}'$ Initialize  $c = 0$ while Stopping criterion not met do Sample minibatch  $\{I_r\}_{r=1}^m$  from training set Set gradient  $\delta = 0$ for  $i = 1$  to m do Compute gradient  $\delta' \leftarrow \delta$ <br>+  $\nabla_{W_{c,q}} \gamma_1(\varphi((\mathcal{H}_K, A_{xy}^K), (\mathcal{H}^*)))$  $+\nabla_{W_{c,q}}\gamma_2(\xi((\mathcal{H}_M, A^M_{xy}), (\mathcal{H}^*_M)))$  $+\delta \nabla_F \gamma_3 \mathbf{v}_2^T \mathbf{L}_{|F|}\mathbf{v}_2$ end for Apply gradient update  $\mathbf{W} \leftarrow \mathbf{W} - \eta \delta$ Update Laplacian matrix  $\mathbf{L}'$ Update  $c \leftarrow c + 1$ if  $c = T$  or C mod  $T = 0$  then Update second Laplacian eigenvector  $\mathbf{v}_2$ end if end while

By introducing the new graph  $G'$ , we shift the focus from directly learning about the original edge set E to minimizing the transformed edge set  $E^{'}$  through adjustments in  $F$ . A higher value of  $|F|$  signifies an increased likelihood of the existence of edges in  $E'$ . Based on these likelihoods, the nodes are categorized into two separate subgraphs, aligning the distribution of edges with their probabilities. This shift allows us to transform the challenge of discerning connected edges into a classical network regularization problem, focusing on the decision of retaining or discarding each edge within  $E'$ .

Classic regularization methods include dropout and L1 norm, among others. Edric [\[22](#page-44-4)] proposed leveraging spectral graph theory to improve the connectivity structure of the multi-layer nonlinear neural network through Fiedler regularization. Their method has demonstrated a significant boost in performance by minimizing hidden unit co-adaptation, presenting a more systematic approach compared to random dropouts during neural networks training.

The Fiedler value  $\lambda_2$  is the second smallest eigenvalue of G''s Laplacian matrix L'. A smaller  $\lambda_2$  indicates a stronger connectivity in the subgraph [\[1\]](#page-43-6). However, during the training process, Fiedler value  $\lambda_2$  cannot be optimized directly. Based on the theory of Cheeger's inequality and Rayleigh-Ritz variational characterization [\[20](#page-44-15),[22\]](#page-44-4), we are able to keep approaching the upper bound of Fiedler value, as shown in the following equation, by performing eigendecomposition and further optimization of  $\lambda_2$ .

$$
\lambda_2 \leq \mathbf{u}^T L^{'} \mathbf{u},\tag{2}
$$

where equality is achieved when unit vector  $u = v_2$ , where  $v_2$  is the eigenvector for  $\lambda_2$ . For the vertex set V', we denote  $V' = \{S \cup S', S \cap S' = \emptyset\}$ , where  $S$  and  $S'$  are two subsets with dense connections within and sparsity between them.

During training,  $G'$  is constructed iteratively. The variable  $u$  is obtained through feature decomposition and serves as an upper bound to iteratively approximate the optimal value of  $\lambda_2$ , which is then utilized to update L', which in turn, aids in estimating *u* more accurately. The pseudo-code is as shown in Algorithm [1.](#page-39-0)

#### 2.4 Skeleton-Aware Encoder (SAE)

To incorporate the information of  $G'$ , we include skeleton-aware encoder in our framework. From the aforementioned processes, we derive the  $F$  vector and  $E_c$ represents the encoded vector output of the TAE. We then apply a basic graph convolution network to model the relationships across landmarks. The operation of this graph convolution can be formulated as:

$$
E_l = \psi_l(W_l E_c F),\tag{3}
$$

where  $\psi_l$  is the skeleton-aware encoder and the  $W_l$  is the weights of non-linear layers, as shown in Fig.  $1(b)$  $1(b)$ .  $E<sub>l</sub>$  is the encoded vector output of skeleton-aware encoder.

The landmark detection task is reformulated to estimate  $K$  landmark heatmaps  $\mathcal{H}_K \in R^{K \times H_h \times W_h}$  of size  $H_h \times W_h$  and offset map  $A_{xy}^K \in R^{2K \times H_h \times W_h}$ . Offset map  $[32]$  is used to refine the landmark location. The limbs relation heatmaps is  $\mathcal{H}_M \in R^{M \times H_h \times W_h}$  and the offset maps is  $A_{xy}^M \in R^{2M \times H_h \times W_h}$ .  $H_h$ and  $W_h$  denote the size of the feature map. It can also be derived from  $E_l$ .

$$
\mathcal{H}_K, A_{xy}^K = D_{Ld}(f_q(W_q, E_l)), \mathcal{H}_M, A_{xy}^M = D_{Li}(f_q(W_q, E_l)),
$$
\n(4)

 $f_q$  represents the feature vector of encoding output which aggregates information from both the target-aware encoder and the skeleton-aware encoder.  $D_{Ld}$  and  $D_{Li}$  represent the landmark decoder and the limbs decoder, respectively.

#### 2.5 Loss Function

The overall loss to train FRGCN is a combination of three losses: 1) we calculate the Mean Squared Error (MSE) for the predicated landmark heatmap  $\mathcal{H}_K$ and the relation heatmap  $\mathcal{H}_M$ .  $\mathcal{H}_K^*$  and  $\mathcal{H}_M^*$  represent the ground truth. 2) we calculate the L1 loss for the landmark offset maps  $A_{xy}^K$  and relation offset maps  $A_{xy}^M$ .  $\mathcal{A}_{xy}^{K*}$  and  $\mathcal{A}_{xy}^{M*}$  represent the ground truth. 3) we calculate the Fiedler score  $\cos u^T L' u$  as the upper bound of Fiedler value  $\lambda_2$ 

The learning objectives are defined as:

$$
\mathbf{L} = L_{LD} + L_{RL} + L_{FS}
$$

$$
= \gamma_1 \varphi((\mathcal{H}_K, A_{xy}^K), (\mathcal{H}_K^*, \mathcal{A}_{xy}^{K*}))
$$

$$
+ \gamma_2 \xi((\mathcal{H}_M, A_{xy}^M), (\mathcal{H}_M^*, \mathcal{A}_{xy}^{M*}))
$$

$$
+ \gamma_3 \mathbf{u}^T L' \mathbf{u}, (5)
$$

where  $\gamma_1$ ,  $\gamma_2$ ,  $\gamma_3$  are hyperparameters, and are respectively set to 1, 1 and 0.01 for balanced training.  $L_{LD}$  represent the loss of  $\mathcal{H}_K$  and  $A_{xy}^K$ .  $L_{RL}$  represent the loss of  $\mathcal{H}_M$  and  $A_{xy}^M$ .  $L_{FS}$  represent the loss of Fiedler regularization.

### 3 Experiment

In this section, we present the results obtained from two new datasets and one publicly datasets and conduct ablation studies to scrutinize the individual components.

#### 3.1 Datasets and Evaluation

To evaluate the accuracy of landmark detection, we utilized the Mean Radial Error (MRE) and Successful Detection Rate (SDR) to evaluate the results in pixels.

Lower Limb Dataset. The lower limb DR images are real-world data collected from collaborative hospitals. Two physicians manually annotated these images with 20 landmarks, as depicted in supplementary Fig. [1.](#page-38-0)

When compared to VDNet [\[31](#page-45-7)], which integrates femur and tibia segmentation masks, VitPose [\[28](#page-45-8)], and RSGNet [\[7](#page-43-4)] as depicted in Table [1](#page-42-0) (32, 40, and 48 manual edges, as well as full connectivity with 210 edges), our method demonstrates a significant decrease in MRE.

Pelvic Dataset. The pelvis DR images are the same as above. Each image is manually annotated with 22 landmarks. Our method achieves a remarkable reduction in MRE. Specifically, the reductions are 12.318, 1.49, 1.625, 1.239, and 2.389 pixels respectively as shown in supplementary Table 1.

Cephalograms Dataset. The cephalograms dataset, publicly available and used in the IEEE 2015 ISBI Grand Challenge [\[25](#page-44-16)]. The train images and test images are delineated in prior research [\[5\]](#page-43-7). Our approach exhibits significant performance gains compared to previous works [\[4](#page-43-8)[,14](#page-44-17),[29\]](#page-45-9), as demonstrated in suppmentary Table 2. Our model consistently achieves a reduction in MRE of at least 1.6 pixels.

| Method       |       |        |       | $L - hof \mid L - gt \mid L - lfc \mid L - mfc \mid L - fi \mid L - ltc \mid L - mtc \mid L - ei \mid L - lm \mid L - mm \mid MRE \downarrow$ |       |       |       |       |       |       |       |
|--------------|-------|--------|-------|-----------------------------------------------------------------------------------------------------------------------------------------------|-------|-------|-------|-------|-------|-------|-------|
|              |       |        |       | $R - hof   R - gt   R - lfc   R - mfc   R - fi   R - ltc   R - mtc   R - ei   R - lm   R - mm$                                                |       |       |       |       |       |       |       |
| <b>VDNet</b> | 4.808 | 4.989  | 5.546 | 4.936                                                                                                                                         | 6.527 | 5.362 | 4.047 | 6.126 | 6.455 | 6.082 | 5.521 |
|              | 3.081 | 4.250  | 4.725 | 6.017                                                                                                                                         | 6.185 | 5.838 | 5.529 | 5.973 | 6.857 | 7.086 |       |
| <b>VDNet</b> | 1.592 | 2.312  | 2.779 | 2.700                                                                                                                                         | 4.515 | 1.858 | 2.368 | 2.541 | 2.085 | 1.489 | 2.428 |
| $+$ mask     | 1.363 | 1.708  | 2.599 | 3.630                                                                                                                                         | 4.648 | 1.455 | 1.675 | 2.918 | 2.316 | 2.011 |       |
| Vitpose-S    | 7.286 | 11.321 | 5.826 | 4.357                                                                                                                                         | 5.954 | 7.135 | 4.709 | 4.345 | 8.790 | 5.258 | 6.451 |
|              | 7.700 | 9.881  | 5.474 | 4.664                                                                                                                                         | 5.649 | 7.208 | 4.735 | 4.180 | 8.845 | 5.717 |       |
| Vitpose-B    | 2.962 | 3.437  | 2.104 | 2.226                                                                                                                                         | 2.165 | 2.126 | 2.208 | 2.383 | 2.468 | 2.421 | 2.473 |
|              | 2.958 | 3.469  | 2.307 | 2.193                                                                                                                                         | 2.227 | 2.174 | 2.256 | 2.295 | 2.497 | 2.589 |       |
| $GCN-32$     | 1.919 | 1.760  | 1.556 | 1.522                                                                                                                                         | 1.251 | 1.557 | 1.424 | 1.567 | 1.426 | 1.300 | 1.524 |
|              | 1.581 | 1.817  | 1.657 | 1.554                                                                                                                                         | 1.240 | 1.577 | 1.461 | 1.563 | 1.380 | 1.363 |       |
| $GCN-40$     | 2.375 | 2.544  | 1.542 | 1.490                                                                                                                                         | 1.247 | 1.475 | 1.426 | 1.441 | 1.428 | 1.265 | 1.581 |
|              | 1.546 | 2.367  | 1.603 | 1.578                                                                                                                                         | 1.198 | 1.562 | 1.408 | 1.494 | 1.340 | 1.295 |       |
| $GCN-48$     | 2.676 | 1.761  | 1.598 | 1.537                                                                                                                                         | 1.268 | 1.498 | 1.359 | 1.469 | 1.871 | 1.697 | 1.574 |
|              | 1.520 | 1.745  | 1.609 | 1.531                                                                                                                                         | 1.245 | 1.444 | 1.428 | 1.529 | 1.356 | 1.334 |       |
| FRGCN        | 1.504 | 1.640  | 1.543 | 1.439                                                                                                                                         | 1.167 | 1.437 | 1.334 | 1.435 | 1.336 | 1.200 | 1.419 |
|              | 1.509 | 1.690  | 1.581 | 1.483                                                                                                                                         | 1.201 | 1.461 | 1.381 | 1.527 | 1.272 | 1.249 |       |

<span id="page-42-0"></span>Table 1. Comparison of the SOTA methods on lower limb dataset with 20 landmarks.

Table 2. Ablation Study in lower limb dataset with 20 landmarks

<span id="page-42-1"></span>

| Method          | SAE          |                                                                                    |       |       |       |                                                                             |       |       | $L - hof  L - gt   L - lfc   L - mfc   L - fi   L - ltc   L - mtc   \text{MRE} \downarrow$ |       |
|-----------------|--------------|------------------------------------------------------------------------------------|-------|-------|-------|-----------------------------------------------------------------------------|-------|-------|--------------------------------------------------------------------------------------------|-------|
| backbone $[21]$ | non-linear   | regularization $ L - ei   L - lm   L - mm   R - hof   R - gt   R - lfc   R - mfc $ |       |       |       |                                                                             |       |       |                                                                                            |       |
|                 | layer        | term                                                                               |       |       |       | $R - fi \mid R - Itc \mid R - mtc \mid R - ei \mid R - lm \mid R - mm \mid$ |       |       |                                                                                            |       |
| $\checkmark$    | x            | ×.                                                                                 | 2.984 | 3.552 | 2.101 | 2.200                                                                       | 2.125 | 2.092 | 2.185                                                                                      | 2.514 |
|                 |              |                                                                                    | 2.353 | 2.659 | 2.621 | 2.989                                                                       | 3.496 | 2.276 | 2.254                                                                                      |       |
|                 |              |                                                                                    | 2.121 | 2.153 | 2.274 | 2.291                                                                       | 2.731 | 2.823 | $\overline{\phantom{a}}$                                                                   |       |
| $\checkmark$    | ✓            | x                                                                                  | 1.586 | 3.431 | 1.509 | 1.492                                                                       | 1.209 | 1.435 | 1.340                                                                                      | 1.534 |
|                 |              |                                                                                    | 1.483 | 1.358 | 1.246 | 1.482                                                                       | 1.712 | 1.617 | 1.491                                                                                      |       |
|                 |              |                                                                                    | 1.195 | 1.469 | 1.373 | 1.560                                                                       | 1.361 | 1.328 | $\overline{\phantom{a}}$                                                                   |       |
|                 | $\checkmark$ | dropout                                                                            | 2.271 | 1.948 | 1.554 | 1.456                                                                       | 1.230 | 1.485 | 1.311                                                                                      | 1.594 |
|                 |              |                                                                                    | 1.405 | 1.345 | 1.253 | 1.527                                                                       | 3.721 | 1.620 | 1.548                                                                                      |       |
|                 |              |                                                                                    | 1.218 | 1.446 | 1.383 | 1.448                                                                       | 1.352 | 1.354 | $\sim$                                                                                     |       |
|                 | $\checkmark$ | L1                                                                                 | 1.514 | 1.644 | 1.490 | 1.430                                                                       | 1.195 | 1.454 | 1.399                                                                                      | 1.486 |
|                 |              |                                                                                    | 1.420 | 1.348 | 1.266 | 2.029                                                                       | 2.242 | 1.585 | 1.488                                                                                      |       |
|                 |              |                                                                                    | 1.204 | 1.478 | 1.372 | 1.505                                                                       | 1.344 | 1.302 | ÷                                                                                          |       |
| ✓               | ✓            | FRGCN                                                                              | 1.458 | 1.628 | 1.537 | 1.446                                                                       | 1.166 | 1.452 | 1.342                                                                                      | 1.418 |
|                 |              |                                                                                    | 1.441 | 1.331 | 1.203 | 1.501                                                                       | 1.693 | 1.583 | 1.477                                                                                      |       |
|                 |              |                                                                                    | 1.195 | 1.451 | 1.389 | 1.542                                                                       | 1.264 | 1.249 | ÷.                                                                                         |       |

Ablation Study. The study explores the skeleton reconstruction using FRGCN. In order to prove that our proposed FRGCN is effective compared to backbone and other sparse methods, four ablation experiments are carried out: backbone, only non-linear layer, randomly dropping values in  $F$ , L1 norm of  $F$ . However, the dropout and L1 norm resulted in a decrease in performance, with increases in MRE of 0.176 and 0.068, respectively. These findings are detailed in Table [2,](#page-42-1) highlighting the impact of different manipulations of  $F$  on the effectiveness of the graph partitioning process.

The learned structures in different dataset are meaningful indicating strong connections.For example, the learned skeletal structure of the lower limb has a left-right approximate complementary connectivity map as shown in supplementary Figure 2.

# 4 Conclusion

In this paper, we introduce an innovative model, FRGCN, for skeleton reconstruction in GCN-based landmark detection, signaling a notable shift from traditional manual methods by applying graph-derived structural constraints. FRGCN is rooted in Fiedler regularization, a concept from spectral graph theory.The superiority of our model is not limited to theoretical aspects; we also illustrate its practicality and efficiency through an effective implementation approach, emphasizing its real-world applicability. Through extensive experiment, we have shown that our method surpasses existing state-of-the-art techniques in medical image analysis, across multiple performance metrics.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-43-6"></span>1. Biggs, N.: Algebraic graph theory: Vertex-partitions and the spectrum (1974)
- <span id="page-43-0"></span>2. Bin, Y., Chen, Z.M., Wei, X.S., Chen, X., Gao, C., Sang, N.: Structure-aware human pose estimation with graph convolutional networks. Pattern Recognition 106, 107410 (2020)
- <span id="page-43-1"></span>3. Cai, Y., Ge, L., Liu, J., Cai, J., Cham, T.J., Yuan, J., Thalmann, N.M.: Exploiting spatial-temporal relationships for 3d pose estimation via graph convolutional networks. In: ICCV (October 2019)
- <span id="page-43-8"></span>4. Chen, R., Ma, Y., Chen, N., Lee, D., Wang, W.: Cephalometric landmark detection by attentive feature pyramid fusion and regression-voting. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2019: 22nd International Conference, Shenzhen, China, October 13–17, 2019, Proceedings, Part III 22. pp. 873–881. Springer (2019)
- <span id="page-43-7"></span>5. Chen, R., Ma, Y., Chen, N., Lee, D., Wang, W.: Cephalometric landmark detection by attentivefeature pyramid fusion and regression-voting. In: International Conference on Medical Image Computing and Computer-Assisted Intervention (2019)
- <span id="page-43-2"></span>6. Cheung, K.F., Bell, M.G.: Improving connectivity of compromised digital networks via algebraic connectivity maximisation. European Journal of Operational Research 294(1), 353–364 (2021)
- <span id="page-43-4"></span>7. Dai, Y., Wang, X., Gao, L., Song, J., Shen, H.T.: Rsgnet: Relation based skeleton graph network for crowded scenes pose estimation. AAAI 35(2), 1193–1200 (2021)
- <span id="page-43-5"></span>8. Gieroba, T.J., Marasco, S., Babazadeh, S., Bella, C.D., Bavel, D.V.: Arithmetic hip knee angle measurement on long leg radiograph versus computed tomography inter-observer and intra-observer reliability. Arthroplasty 5(1) (2023)
- <span id="page-43-3"></span>9. He, Y., Gan, Q., Wipf, D., Reinert, G.D., Yan, J., Cucuringu, M.: Gnnrank: Learning global rankings from pairwise comparisons via directed graph neural networks. In: international conference on machine learning. pp. 8581–8612. PMLR (2022)

- <span id="page-44-2"></span>10. Hinton, G.E., Srivastava, N., Krizhevsky, A., Sutskever, I., Salakhutdinov, R.R.: Improving neural networks by preventing co-adaptation of feature detectors (2012)
- <span id="page-44-6"></span>11. Jiang, S.: Vision-Based Analysis of Human Face and Gesture: Dynamic Modeling, Synthesis and Recognition. Ph.D. thesis, Northeastern University (2022)
- <span id="page-44-0"></span>12. Lang, Y., Chen, X., Deng, H.H., Kuang, T., Barber, J.C., Gateno, J., Yap, P.T., Xia, J.J.: Dentalpointnet: Landmark localization onhigh-resolution 3d digital dental models. In: International Conference on Medical Image Computing and Computer-Assisted Intervention (2022)
- <span id="page-44-3"></span>13. Larsen, J., Hansen, L.K., Svarer, C.: Regularization of neural networks using dropconnect (2001)
- <span id="page-44-17"></span>14. Lin, C., Zhu, B., Wang, Q., Liao, R., Qian, C., Lu, J., Zhou, J.: Structure-coherent deep feature learning for robust face alignment. IEEE Transactions on Image Processing 30, 5313–5326 (2021)
- <span id="page-44-7"></span>15. Liu, X., Chen, X., Yang, L., Chen, Q., Guo, J., Wu, S.: Dynamic topology control in optical satellite networks based on algebraic connectivity. Acta Astronautica 165, 287–297 (2019)
- <span id="page-44-8"></span>16. Nolte, D., Ko, S.T., Bull, A.M., Kedgley, A.E.: Reconstruction of the lower limb bones from digitised anatomical landmarks using statistical shape modelling. Gait & Posture 77, 269–275 (2020)
- <span id="page-44-11"></span>17. Qiu, Z., Qiu, K., Fu, J., Fu, D.: Dgcn: Dynamic graph convolutional network for efficient multi-person pose estimation. AAAI  $34(07)$ , 11924–11931 (2020)
- <span id="page-44-13"></span>18. Sled, E.A., Sheehy, L.M., Felson, D.T., Costigan, P.A., Lam, M., Cooke, T.D.V.: Reliability of lower limb alignment measures using an established landmark-based method with a customized computer software program. Rheumatology International  $31(1)$ ,  $71-77(2011)$
- <span id="page-44-12"></span>19. Song, H., Liu, C., Li, S., Zhang, P.: Ts-gcn: A novel tumor segmentation method integrating transformer and gcn. Mathematical Biosciences and Engineering 20(10), 18173–18190 (2023)
- <span id="page-44-15"></span>20. Spielman, D.: Spectral graph theory. Betascript Publishing (2010)
- <span id="page-44-14"></span>21. Sun, K., Xiao, B., Liu, D., Wang, J.: Deep high-resolution representation learning for human pose estimation. In: CVPR (June 2019)
- <span id="page-44-4"></span>22. Tam, E., Dunson, D.: Fiedler regularization: Learning neural networks with graph sparsity. In: III, H.D., Singh, A. (eds.) Proceedings of the 37th International Conference on Machine Learning. Proceedings of Machine Learning Research, vol. 119, pp. 9346–9355. PMLR (13–18 Jul 2020)
- <span id="page-44-5"></span>23. Tam, E., Dunson, D.: Spectral gap regularization of neural networks. arXiv preprint [arXiv:2304.03096](http://arxiv.org/abs/2304.03096) (2023)
- <span id="page-44-9"></span>24. Valle, R., Buenaposada, J.M., Valdes, A., Baumela, L.: A deeply-initialized coarseto-fine ensemble of regression trees for face alignment. In: Proceedings of the European Conference on Computer Vision (ECCV) (September 2018)
- <span id="page-44-16"></span>25. Wang, C.W., Huang, C.T., Hsieh, M.C., Li, C.H., Chang, S.W., Li, W.C., Vandaele, R., Marée, R., Jodogne, S., Geurts, P., Chen, C., Zheng, G., Chu, C., Mirzaalian, H., Hamarneh, G., Vrtovec, T., Ibragimov, B.: Evaluation and comparison of anatomical landmark detection methods for cephalometric x-ray images: A grand challenge. IEEE Transactions on Medical Imaging 34(9), 1890–1900 (2015)
- <span id="page-44-1"></span>26. Wang, Z., Lv, J., Yang, Y., Lin, Y., Li, Q., Li, X., Yang, X.: Accurate scoliosis vertebral landmark localization on x-ray images via shape-constrained multi-stage cascaded cnns. Fundamental Research (2022)
- <span id="page-44-10"></span>27. Wei, S.E., Ramakrishna, V., Kanade, T., Sheikh, Y.: Convolutional pose machines. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR) (June 2016)

- <span id="page-45-8"></span>28. Xu, Y., Zhang, J., ZHANG, Q., Tao, D.: Vitpose: Simple vision transformer baselines for human pose estimation. In: Koyejo, S., Mohamed, S., Agarwal, A., Belgrave, D., Cho, K., Oh, A. (eds.) Advances in Neural Information Processing Systems. vol. 35, pp. 38571–38584. Curran Associates, Inc. (2022)
- <span id="page-45-9"></span>29. Ye, Z., Yu, H., Li, B.: Uncertainty-aware u-net for medical landmark detection (2023)
- <span id="page-45-3"></span>30. Yu, X., Huang, J., Zhang, S., Metaxas, D.N.: Face landmark fitting via optimized part mixtures and cascaded deformable model. IEEE Transactions on Pattern Analysis and Machine Intelligence 38(11), 2212–2226 (2016)
- <span id="page-45-7"></span>31. Zhang, L., Zhang, J., Shen, P., Zhu, G., Li, P., Lu, X., Zhang, H., Shah, S.A., Bennamoun, M.: Block level skip connections across cascaded v-net for multi-organ segmentation. IEEE Transactions on Medical Imaging 39(9), 2782–2793 (2020)
- <span id="page-45-6"></span>32. Zhang, R., Zhu, Z., Li, P., Wu, R., Guo, C., Huang, G., Xia, H.: Exploiting offsetguided network for pose estimation and tracking (2019)
- <span id="page-45-0"></span>33. Zhang, S., Tong, H., Xu, J., Maciejewski, R.: Graph convolutional networks: a comprehensive review. Computational Social Networks  $6(1)$ , 1–23 (2019)
- <span id="page-45-1"></span>34. Zhao, Q., Zhu, J., Zhu, J., Zhou, A., Shao, H.: Bone anatomical landmark localization with cascaded spatial configuration network. Measurement Science and Technology 33(6), 065401 (mar 2022)
- <span id="page-45-2"></span>35. Zhou, F., Brandt, J., Lin, Z.: Exemplar-based graph matching for robust facial landmark localization. In: Proceedings of the IEEE International Conference on Computer Vision (ICCV) (December 2013)
- <span id="page-45-4"></span>36. Zhu, H., Chen, B., Yang, C.: Understanding why vit trains badly on small datasets: An intuitive perspective. arXiv preprint [arXiv:2302.03751](http://arxiv.org/abs/2302.03751) (2023)
- <span id="page-45-5"></span>37. Zhu, X., Cheng, D., Zhang, Z., Lin, S., Dai, J.: An empirical study of spatial attention mechanisms in deep networks. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 6688–6697 (2019)

Image /page/46/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a curved line segment on the left and a flag shape on the right. The text below the icon reads "Check for updates".

# LGA: A Language Guide Adapter for Advancing the SAM Model's Capabilities in Medical Image Segmentation

Jihong Hu<sup>1</sup>, Yinhao Li<sup>2</sup>, Hao Sun<sup>3</sup>, Yu Song<sup>2</sup>, Chujie Zhang<sup>1</sup>, Lanfen Lin<sup>3( $\boxtimes$ )</sup>, and Yen-Wei Chen<sup>2( $\boxtimes$ )</sup>

<sup>1</sup> Graduate School of Information Science and Engineering, Ritsumeikan University, Kyoto, Japan

{gr0609ik,gr0696fh}@ed.ritsumei.ac.jp

<sup>2</sup> College of Information Science and Engineering, Ritsumeikan University, Kyoto,

Japan

{yin-li,yusong}@fc.ritsumei.ac.jp, <EMAIL>

<sup>3</sup> College of Computer Science and Technology, Zhejiang University, Hangzhou,

China

{sunhaoxx,llf}@zju.edu.cn

Abstract. In addressing the unique challenges of medical image segmentation, foundation models like the Segment Anything Model (SAM), originally developed for natural image, often falter due to the distinct nature of medical images. This study introduces the Language Guide Adapter (LGA), a paremeter efficient fine-tuning approach that extends SAM's utility to medical segmentation tasks. Through the integration of textual data from medical reports via a pretrained Bert model into embeddings, LGA combines these embeddings with the image features in SAM's image encoder using Feature Fusion Modules (FFM). Our method significantly enhances model performance and reduces computational overhead by freezing most parameters during the fine-tuning process. Evaluated on the CT-based MosMedData+ and the X-ray dataset QaTa-COV19, LGA demonstrates its effectiveness and adaptability, achieving competitive results with a significant reduction in the number of parameters required for fine-tuning compared to SOTA medical segmentation models. This enhancement underscores the potential of foundation models, leveraging the integration of multimodal knowledge as a pivotal approach for application in specialized medical tasks, thus charting a course towards more precise and adaptable diagnostic methodologies. The code is avaliable at [https://github.com/JiHooooo/LGA.](https://github.com/JiHooooo/LGA)

**Keywords:** Medical image segmentation  $\cdot$  Foundation model  $\cdot$  Vision-language model  $\cdot$  Parameter efficient fine-tune

### 1 Introduction

The segmentation of medical images represents a pivotal task in computerassisted diagnosis. In recent years, deep learning models have achieved notable success in this field, enabling fully automated segmentation of objects of interest [\[4](#page-54-0)[,15](#page-55-0),[22,](#page-55-1)[27](#page-56-0)]. Nonetheless, these models are heavily dependent on large, annotated datasets for training-a requirement that presents substantial challenges in the medical field. The need for expert annotations from professional doctor renders the data preparation phase both time-consuming and expensive. Additionally, the diversity of medical imaging modalities, along with variations in imaging equipment and operational methods across different hospitals, significantly challenges the generalizability of these models.

Transitioning from these traditional approaches, the recent advent of foundation models marks a paradigm shift towards overcoming such limitations. Initially excelling in Natural Language Processing (NLP), models like GPT [\[20\]](#page-55-2) and BERT [\[8\]](#page-55-3) have showcased their prowess in text understanding and generation. By leveraging zero-shot learning and prompt engineering, these foundation models perform well across various tasks with minimal data, demonstrating their adaptability and efficiency without extensive training. In the field of image segmentation, SAM [\[16\]](#page-55-4) emerges as the pioneering foundation model. Equipped with an expansive dataset of 11 million labeled images and an interactive segmentation framework, SAM achieves remarkable zero-shot capabilities on natural images. However, studies [\[10](#page-55-5),[14\]](#page-55-6) have indicated SAM's underperformance in the medical imaging domain, highlighting the need for further improvement to bridge the applicability gap.

Given the substantial parameter volume inherent in foundation models, comprehensively fine-tuning such models requires extensive computational resources, and when attempted with limited datasets, often results in suboptimal performance. Consequently, there's a growing focus on parameter-efficient fine-tuning strategies. For instance, MedSAM [\[19\]](#page-55-7) adopts a strategy where it freezes the parameter-heavy image encoder within SAM, opting to fine-tune only the mask decoder using medical data. Meanwhile, [\[5,](#page-54-1)[12,](#page-55-8)[24](#page-56-1)] leverage Adapter technology, inserting lightweight, learnable modules into the existing model for fine-tuning the image encoder. Techniques like Low-Rank Adaptation (LoRA) are also used to adjust the encoder [\[25,](#page-56-2)[26\]](#page-56-3). However, these methodologies remain primarily confined to the imaging modality.

Contrasting with the limitations inherent to the imaging modality alone, leveraging the intrinsic value of medical reports presents a unique opportunity in medical image analysis. Since each patient's medical imaging is accompanied by a corresponding medical report, acquiring these reports incurs no additional cost, unlike the augmentation of segmentation annotations. Furthermore, the imaging quality of medical images is generally lower than that of natural images, with boundaries between different regions appearing more blurred. However, medical reports, enriched with expert knowledge, compensate for the deficiencies in image quality. [\[13\]](#page-55-9) developes an attention-based framework for learning both global and local representations by contrasting image sub-regions with words in the paired report. [\[23\]](#page-56-4) improves polyp segmentation by incorporating text-guided features like polyp size and count. [\[17\]](#page-55-10) leverages Bert to extract textual features of medical reports and employs a hybrid network of CNNs and Transformers to fuse textual and visual features. These methods still require designing complex networks from scratch to fuse textual and image information.

In contrast, we propose a more flexible multimodal model framework that retains the parameters of foundation models like SAM and BERT used in our experiments. Our approach uses lightweight feature interaction modules for feature fusion. For each new task, only these modules and the task-specific prediction head need fine-tuning. This method enhances model performance while reducing computational and storage resources. Our contributions are summarized in three key areas:

- 1. We introduce a parameter-efficient fine-tuning method for SAM foundational models, called language guide adapter(LGA), which incorporates textual information into the fine-tuning process, significantly enhancing the model's performance on specific medical segmentation tasks. To the best of our knowledge, this is the first study to implement adapter technology for incorporating textual information into SAM foundation model.
- 2. We have developed an efficient feature fusion module(FFM) that combines cross-attention mechanisms with Multi Layer Perceptron(MLP) networks to achieve the integration of textual and visual information.
- 3. Through comprehensive experiments on the X-ray QaTa-COV19 and CT MosMedData+ datasets, our study showcases it's exceptional adaptability across various medical imaging modalities. It not only surpasses SOTA algorithms in terms of performance but also achieves this with a significantly reduced number of training parameters.

# 2 Methodology

The SAM model comprises three primary components: an image encoder with a vision transformer architecture for feature extraction [\[9\]](#page-55-11) , a prompt encoder for encoding prompt information, and a mask decoder for generating final predictions using both prompt and image features. The majority of the SAM model's parameters are concentrated in the image encoder. As illustrated in [1,](#page-49-0) we remove the prompt encoder, tailoring the model for automated end-to-end segmentation tasks. Textual features are extracted from medical reports using the pretrained Bert model and then combined with image features via the LGA, which integrates multiple FFMs. Moreover, during the fine-tuning phase, we specifically adjust only the parameters of the LGA and the mask decoder. By freezing the entire image encoder, we significantly reduce the number of parameters that need fine-tuning.

#### 2.1 LGA Framework

Image /page/49/Figure/2 description: This is a diagram illustrating a model that uses an image encoder and a language guide adapter to predict lung segmentation. The image encoder consists of patch embedding, position embedding, element-wise addition, and multiple transformer blocks, culminating in a neck module. The language guide adapter takes a BERT input and processes it through multiple feature fusion modules. Both the image encoder and the BERT input are shown to be frozen, while the feature fusion modules and the mask decoder are finetuned. The output is a prediction mask of the lungs.

<span id="page-49-0"></span>Fig. 1. LGA Framework Overview: Extracting image features via image encoder and text features via Bert, with LGA fusing both for prediction by the mask decoder. During fine-tuning, only LGA and mask decoder are adjusted, freezing Bert and image encoder.

The LGA comprises N FFMs. For each module, designated as  $F_{FFM}^n$  for the *n*-th module, a textual feature  $L_n$  and an image feature  $V_n$  are inputted, yielding an module, a textual feature  $L_n$  and an image feature  $V_m$  are inputted, yielding an updated textual feature  $\hat{L}_n$  and image feature  $\hat{V}_m$ ,  $(\hat{L}_n, \hat{V}_m) = F_{FFM}^n(L_n, V_m)$ .<br>Here *V*<sub>c</sub> represents the feature output from the *m*-th transformer block within Here,  $V_m$  represents the feature output from the m-th transformer block within the image encoder, and  $V_m$  is subsequently fed back into the image encoder for further processing. The updated textual feature  $\tilde{L}_n$  is then utilized as the input for  $F_{FFM}^{(n+1)}$ , facilitating continuous feature fusion across the sequence of  $FFM_s$ . Notably, the Bert model initially processes the medical reports into a FFMs. Notably, the Bert model initially processes the medical reports into a summarized feature  $L_0$  by converting and averaging word features, as detailed in Eq. [1.](#page-49-1) Z and F represent the length of the medical report  $T$  and the length of Bert feature respectively Fig. [1.](#page-49-0)

<span id="page-49-1"></span>
$$
L_n = \begin{cases} \sum_{j=1}^{Z} Bert(T) \in \mathbb{R}^{1 \times F}, & n = 0\\ \hat{L}_{n-1}, & n > 0 \end{cases}
$$
 (1)

#### 2.2 FFM Structure

The FFM, depicted in Fig. [2,](#page-49-2) stands as a crucial component within the LGA framework. It employs a dual cross-attention structure integrated with two MLPs to facilitate the fusion process. The entire computation process is illus-

Image /page/49/Figure/8 description: This is a diagram illustrating a cross-modal attention mechanism. The diagram shows two parallel processing streams, one for vision features and one for text features. The vision stream starts with a 'Vm' input, which is normalized and then fed into a 'Vm to Ln attn' block. The output of this attention block is combined with the output of a 'Ln to Vm attn' block via an addition operation. This combined output is then normalized and passed through a 'ViT MLP' block, resulting in an output labeled 'Vm'. The text stream starts with an 'Ln' input, which is normalized and then fed into a 'Ln to Vm attn' block. The output of this attention block is combined with the output of the 'Vm to Ln attn' block via an addition operation. This combined output is then normalized and passed through a 'Text MLP' block, resulting in an output labeled 'Ln'. Arrows indicate the flow of 'Vision Feature' (blue) and 'Text Feature' (orange) throughout the diagram. The 'k' and 'v' outputs from the 'Vm to Ln attn' block are fed into the 'Ln to Vm attn' block, and vice versa.

<span id="page-49-2"></span>Fig. 2. The structure of feature fusion module.

trated in Eq. [2,](#page-50-0) Eq. [3,](#page-50-1) Eq. [4](#page-50-2) and Eq. [5,](#page-50-3) with  $norm(\cdot)$  denoting Layer Normaliza-tion [\[2](#page-54-2)]. The learnable parameter  $\gamma^n$  is initially configured to 0. This setup aims to maintain the pre-trained image encoder's feature extraction integrity in the training's early stages [\[6\]](#page-54-3).

<span id="page-50-0"></span>
$$
L'_n = L_n + CrossAtten(norm(L_n), norm(V_m))
$$
\n(2)

<span id="page-50-1"></span>
$$
\hat{L}_n = L'_n + MLP_{text}(norm(L'_n))
$$
\n(3)

<span id="page-50-2"></span>
$$
V'_m = V_m + \gamma^n CrossAtten(norm(V_m), norm(\hat{L}_n))
$$
\n(4)

<span id="page-50-3"></span>
$$
\hat{V}_m = V'_m + MLP_{vit}(norm(V'_m))\tag{5}
$$

#### 2.3 Training Process

For fine-tuning, we use a dataset  $\{(X^d, Y^d, T^d)\}_{d=1}^D$  comprising images  $X^d$ , labels  $Y^d$  and related medical reports  $T^d$  with  $D$  samples in total. The Bert model  $Y^d$ , and related medical reports  $T^d$ , with D samples in total. The Bert model transforms medical reports into text feature  $L_d^d$ . Then, the image encoder, inte-<br>grating the LGA extracts features from inputs  $X^d$  and  $L_d^d$  which are then grating the LGA, extracts features from inputs  $X^d$  and  $L_0^d$ , which are then<br>processed by the Mask decoder to yield the segmentation predictions  $P^d$ . We processed by the Mask decoder to yield the segmentation predictions P*<sup>d</sup>*. We calculate loss using a mix of Dice and cross-entropy loss, detailed in Eq. [6](#page-50-4) and Eq. [7,](#page-50-5) where K and C are pixel and class counts.  $P_{kc}$  is the probability of pixel  $k$  in class  $c$ , and  $Y_{kc}$  represents whether pixel  $k$  belongs to category  $c$ . The total loss formula is  $l_{total} = 0.5l_{Dice} + 0.5l_{CE}$ .

<span id="page-50-4"></span>
$$
l_{Dice} = 1 - \sum_{k=1}^{K} \sum_{c=1}^{C} \frac{1}{KC} \frac{2 |P_{kc} \cap Y_{kc}|}{(|P_{kc}| + |Y_{kc}|)}
$$
(6)

<span id="page-50-5"></span>
$$
l_{CE} = -\sum_{k=1}^{K} \sum_{c=1}^{C} \frac{1}{K} \cdot Y_{kc} log(P_{kc})
$$
\n(7)

#### 3 Experiments

#### 3.1 Datasets

We evaluated our model using two datasets: MosMedData+  $[1,11]$  $[1,11]$  $[1,11]$  and QaTa-COV19 [\[7](#page-55-13)]. MosMedData+ consists of 2729 lung CT slices with COVID-19 findings, annotated with binary masks highlighting regions such as ground-glass opacifications. The QaTa-COV19 dataset contains 9258 chest X-rays annotated for COVID-19 lesions. Text annotations detailing aspects like infection presence, number of affected regions, and their specific locations (e.g., "Bilateral pulmonary infection, two infected areas, upper left lung and upper right lung") were added by [\[17\]](#page-55-10). For dataset partitioning, both MosMedData+ and  $QaTa-$ COV19 datasets were divided into training, validation, and test sets with 2,183, 273, 273, and 5,716, 1,429, 2,113 samples respectively, aligning with [\[17\]](#page-55-10).

### 3.2 Implementations

In terms of data augmentation, we employed random cropping, rotation, translation, scaling, and adjustments to brightness and contrast. The specific parameters are detailed in the appendix. For model training, the batch size was set to 2, with AdamW [\[18](#page-55-14)] as the optimizer. The initial learning rate was set to 1e-4, with a weight decay of 0.001. Regarding learning rate adjustment, we applied a linear warm-up for the first 1,000 iterations, followed by a cosine decay of the learning rate thereafter. Training was conducted over 50 epochs, utilizing a single RTX 4090 graphics card.

In our model structure, we utilized the ViT-B architecture, initializing it with pretrained SAM parameters from the SA-1B dataset [\[16\]](#page-55-4). The LGA includes four FFMs: the first three interact with the input features of the 1st, 5th, and 9th transformer blocks of ViT-B, respectively, while the final FFM engages with the output features of ViT-B's last transformer block. Within this last FFM, we omitted the cross-attention mechanism and the MLP, both initially intended to enhance text features. The detailed parameters for the FFMs are provided in the appendix.

### 3.3 Quantitative Results

In our quantitative analysis, the LGA model was evaluated against SOTA medical segmentation models using Dice and mIoU metrics. This comparison spanned text-integrated models (TGANet, CLIP, GLoRIA, LViT-T), image-only models (UNet, UNet $++$ , nnUNet, TransUNet, Swin-Unet), and MedSAM, which finetunes only the SAM mask decoder. Results, shown in Table[.1,](#page-52-0) indicate LGA's superior performance and efficiency: it achieved the highest scores in both metrics on all datasets and required significantly fewer training parameters than all models except MedSAM. Specifically, LGA improved Dice and mIoU on QaTa-COV19 by 11.60% and 14.26%, and on MosMedData+ by  $24.72\%$  and  $25.39\%$ , respectively, compared to MedSAM. Against the most competitive model, LViT-T, LGA recorded improvements of 0.99% in Dice and 1.12% in mIoU on QaTa-COV19, and  $1.06\%$  in Dice and  $1.19\%$  in mIoU on MosMedData+, with a  $72.3\%$ reduction in fine-tuned parameters.

### 3.4 Model Results Visualization

We also visualized the model's results, as shown in Fig. [3.](#page-53-0) From left to right, the sequence is textual information, original image, label, and the results from the LGA, MedSAM, LViT, and nnUNet models. It is evident that, due to the lack of textual information, nnUNet and MedSAM are prone to inaccuracies in determining the position and number of infected regions. Compared to LViT, which also integrates textual information, the segmentation results of our proposed LGA are more precise.

<span id="page-52-0"></span>

| Method                       |   |      | $Text Param(M) QaTa-COV19$ |       | $MostedData+$ |                                            |  |
|------------------------------|---|------|----------------------------|-------|---------------|--------------------------------------------|--|
|                              |   |      |                            |       |               | $Dice(\%) \text{mIoU}(\%)$ Dice(%) mIoU(%) |  |
| U-Net $[22]$                 | x | 14.8 | 79.02                      | 69.46 | 64.60         | 50.73                                      |  |
| $UNet++ [27]$                | x | 74.5 | 79.62                      | 70.25 | 71.75         | 58.39                                      |  |
| $nnUNet$ [15]                | x | 19.1 | 80.42                      | 70.81 | 72.59         | 60.36                                      |  |
| TransUNet $[4]$ X            |   | 105  | 78.63                      | 69.13 | 71.24         | 58.44                                      |  |
| Swin-Unet $\left 3\right $ X |   | 82.3 | 78.07                      | 68.34 | 63.29         | 50.19                                      |  |
| $MedSAM$ [5]                 | x | 4.06 | 73.05                      | 61.97 | 50.91         | 37.13                                      |  |
| TGANet [23]                  | ✓ | 19.8 | 79.72                      | 70.58 | 72.06         | 59.73                                      |  |
| CLIP $[21]$                  | ✓ | 87.0 | 79.81                      | 70.66 | 71.97         | 59.64                                      |  |
| GLORIA [13]                  | ✓ | 45.6 | 79.94                      | 70.68 | 72.42         | 60.18                                      |  |
| LViT-T $[17]$                | ✓ | 29.7 | 83.66                      | 75.11 | 74.57         | 61.33                                      |  |
| LGA(our)                     | ✓ | 8.24 | 84.65                      | 76.23 | 75.63         | 62.52                                      |  |

Table 1. Quantitative comparison of our proposed method with other SOTA medical image segmentation results

### 3.5 Ablation Study

In the ablation study performed on the QaTa-COV19 dataset, our baseline involved solely fine-tuning the mask decoder. We first assessed the performance impact of integrating the LGA fusion module and textual information. Following this, we investigated the influence of both the structure and number of FFMs within the LGA on the model's effectiveness.

In Table[.2,](#page-53-1) we progressively enhance the baseline by first adding textual information (following the original SAM's approach of directly inputting Bert-converted text into the Mask decoder), then by solely introducing the LGA without text (replacing BERT's text features with learnable query), and finally by fully implementing the LGA strategy. Detailed test conditions are in the appendix. The results show that both LGA's improvement of the Image Encoder's feature extraction and the addition of textual information positively affect performance, with the combination of both achieving the best outcomes.

For analysing the FFM structure's impact, we sequentially added Dual cross attention and separate MLPs for image and text features on top of the baseline. We found that each component contributes to the final performance, as shown in Table[.3.](#page-53-2)

In Table[.4,](#page-53-3) we assessed how varying the number of FFMs in the LGA affects performance with FFM placement specifics in the appendix. Compared to the baseline that only adjusts the mask decoder, integrating one FFM into the image encoder substantially enhanced performance, raising Dice by 8.16% and mIoU by 9.54%, with a minimal parameter increase of 0.60M. Additional FFMs further improved results, but the benefit plateaued after four FFMs, suggesting a saturation point. Thus, for efficiency, subsequent experiments used four FFMs.

Image /page/53/Figure/1 description: This image displays a comparison of different segmentation models for pulmonary infections on medical scans. The first row shows a chest X-ray with a unilateral pulmonary infection in the lower left lung, with segmentation masks from Mask, LGA, MedSAM, LViT, and nnUNet. The second row shows a chest X-ray with bilateral pulmonary infections in the upper middle left lung, again with masks from the same models. The third and fourth rows present axial CT slices of lungs with bilateral pulmonary infections, showing four infected areas in the middle left and middle right lung, and three infected areas in the upper left lung and all right lung, respectively. Each row includes the original image and the segmentation results from various models, highlighting the performance differences in identifying and delineating infected lung regions.

<span id="page-53-0"></span>Fig. 3. Qualitative comparison of our proposed LGA with MedSAM, LViT and nnUNet

<span id="page-53-1"></span>Table 2. The ablation study on the QaTa-COV19 Dataset

|  | LGA $\operatorname{Text} \operatorname{Param}(M) \operatorname{Dice}(\%) \operatorname{mIoU}(\%)$ |       |       |             |                   | Dual ViT Text Param Dice |                   | mIoU   |
|--|---------------------------------------------------------------------------------------------------|-------|-------|-------------|-------------------|--------------------------|-------------------|--------|
|  | 4.06                                                                                              | 73.05 | 61.97 |             | Cross MLP MLP (M) |                          | $(\% )$           | $(\%)$ |
|  | 4.20                                                                                              | 78.62 | 68.10 |             |                   | 4.06                     | 73.05             | 61.97  |
|  | 8.24                                                                                              | 80.37 | 70.83 | ✓           |                   | 6.15                     | $81.65$   $72.01$ |        |
|  | 8.24                                                                                              | 84.65 | 76.23 | √           |                   | 7.34                     | 84.09 75.47       |        |
|  |                                                                                                   | 8.24  |       | 84.65 76.23 |                   |                          |                   |        |

<span id="page-53-2"></span>Table 3. The influence of FFM structure on the QaTa-COV19 Dataset

Table 4. The influence of FFM number on the QaTa-COV19 Dataset

<span id="page-53-3"></span>

|                | Number $\text{Param}(M)   \text{Dice}(\%)   \text{mIoU}(\%)$ |       |       |
|----------------|--------------------------------------------------------------|-------|-------|
| $\Omega$       | 4.06                                                         | 73.05 | 61.97 |
| 1              | 4.66                                                         | 81.21 | 71.51 |
| $\overline{2}$ | 5.85                                                         | 84.23 | 75.47 |
| 3              | 7.04                                                         | 84.43 | 75.85 |
| 4              | 8.24                                                         | 84.65 | 76.23 |
| 5              | 9.43                                                         | 84.68 | 76.08 |

# 4 Conclusion

In this paper, addressing the performance degradation of SAM foundation models in medical segmentation tasks, we introduce a parameter-efficient fine-tuning strategy named LGA, which boosts model performance by incorporating textual information. This approach also serves as an efficient paradigm for integrating textual and visual modalities, utilizing adapter technology to fuse features from both vision and language foundation models for specific tasks. We validated our method on two medical imaging datasets from different modalities: the CT dataset MosMedData+ and the X-ray dataset QaTa-COV19, demonstrating its applicability across diverse medical imaging data types. Compared to other SOTA medical segmentation models that focus solely on the image modality or combine image and textual modalities, our LGA method not only achieves SOTA performance but also significantly reduces the amount of parameters required for fine-tuning. However, our current approach relies on textual input during inference, which can be a limitation when such information is not available. We plan to address this issue in future work.

Acknowledgement. This work was supported in part by the Grant in Aid for Scientific Research from the Japanese Ministry for Education, Science, Culture and Sports (MEXT) under the Grant Nos. 20KK0234, 21H03470, and 20K21821, and in part by Zhejiang Provincial Natural Science Foundation of China (No. LZ22F020012).

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-54-4"></span>1. Andreychenko, A., Pavlov, N., Vladzymyrskyy, A., Ledikhova, N., Gombolevskiy, V., Gelezhe, P., Gonchar, A., Chernina, V.Y.: Mosmeddata: Chest ct scans with covid-19 related findings dataset. arXiv preprint [arXiv:2005.06465](http://arxiv.org/abs/2005.06465) (2020)
- <span id="page-54-2"></span>2. Ba, J.L., Kiros, J.R., Hinton, G.E.: Layer normalization. arXiv preprint [arXiv:1607.06450](http://arxiv.org/abs/1607.06450) (2016)
- <span id="page-54-5"></span>3. Cao, H., Wang, Y., Chen, J., Jiang, D., Zhang, X., Tian, Q., Wang, M.: Swinunet: Unet-like pure transformer for medical image segmentation. In: European conference on computer vision. pp. 205–218. Springer (2022)
- <span id="page-54-0"></span>4. Chen, J., Lu, Y., Yu, Q., Luo, X., Adeli, E., Wang, Y., Lu, L., Yuille, A.L., Zhou, Y.: Transunet: Transformers make strong encoders for medical image segmentation. arXiv preprint [arXiv:2102.04306](http://arxiv.org/abs/2102.04306) (2021)
- <span id="page-54-1"></span>5. Chen, T., Zhu, L., Ding, C., Cao, R., Zhang, S., Wang, Y., Li, Z., Sun, L., Mao, P., Zang, Y.: Sam fails to segment anything?–sam-adapter: Adapting sam in underperformed scenes: Camouflage, shadow, and more. arXiv preprint [arXiv:2304.09148](http://arxiv.org/abs/2304.09148) (2023)
- <span id="page-54-3"></span>6. Chen, Z., Duan, Y., Wang, W., He, J., Lu, T., Dai, J., Qiao, Y.: Vision transformer adapter for dense predictions. In: The Eleventh International Conference on Learning Representations (2022)

- <span id="page-55-13"></span>7. Degerli, A., Kiranyaz, S., Chowdhury, M.E., Gabbouj, M.: Osegnet: Operational segmentation network for covid-19 detection using chest x-ray images. In: 2022 IEEE International Conference on Image Processing (ICIP). pp. 2306–2310. IEEE (2022)
- <span id="page-55-3"></span>8. Devlin, J., Chang, M.W., Lee, K., Toutanova, K.: Bert: Pre-training of deep bidirectional transformers for language understanding. arXiv preprint [arXiv:1810.04805](http://arxiv.org/abs/1810.04805) (2018)
- <span id="page-55-11"></span>9. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint [arXiv:2010.11929](http://arxiv.org/abs/2010.11929) (2020)
- <span id="page-55-5"></span>10. He, S., Bao, R., Li, J., Grant, P.E., Ou, Y.: Accuracy of segment-anything model (sam) in medical image segmentation tasks. arXiv preprint [arXiv:2304.09324](http://arxiv.org/abs/2304.09324) (2023)
- <span id="page-55-12"></span>11. Hofmanninger, J., Prayer, F., Pan, J., Röhrich, S., Prosch, H., Langs, G.: Automatic lung segmentation in routine imaging is primarily a data diversity problem, not a methodology problem. European Radiology Experimental 4(1), 1–13 (2020)
- <span id="page-55-8"></span>12. Hu, J., Li, Y., Lin, L., Chen, Y.W.: Integrating spatial prior adapter for enhancing sam performance in medical image segmentation. In: 2023 IEEE 12th Global Conference on Consumer Electronics (GCCE). pp. 20–23. IEEE (2023)
- <span id="page-55-9"></span>13. Huang, S.C., Shen, L., Lungren, M.P., Yeung, S.: Gloria: A multimodal global-local representation learning framework for label-efficient medical image recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 3942–3951 (2021)
- <span id="page-55-6"></span>14. Huang, Y., Yang, X., Liu, L., Zhou, H., Chang, A., Zhou, X., Chen, R., Yu, J., Chen, J., Chen, C., et al.: Segment anything model for medical images? Medical Image Analysis 92, 103061 (2024)
- <span id="page-55-0"></span>15. Isensee, F., Jaeger, P.F., Kohl, S.A., Petersen, J., Maier-Hein, K.H.: nnu-net: a self-configuring method for deep learning-based biomedical image segmentation. Nat. Methods 18(2), 203–211 (2021)
- <span id="page-55-4"></span>16. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., Xiao, T., Whitehead, S., Berg, A.C., Lo, W.Y., et al.: Segment anything. arXiv preprint [arXiv:2304.02643](http://arxiv.org/abs/2304.02643) (2023)
- <span id="page-55-10"></span>17. Li, Z., Li, Y., Li, Q., Wang, P., Guo, D., Lu, L., Jin, D., Zhang, Y., Hong, Q.: Lvit: language meets vision transformer in medical image segmentation. IEEE transactions on medical imaging (2023)
- <span id="page-55-14"></span>18. Loshchilov, I., Hutter, F.: Decoupled weight decay regularization. arXiv preprint [arXiv:1711.05101](http://arxiv.org/abs/1711.05101) (2017)
- <span id="page-55-7"></span>19. Ma, J., Wang, B.: Segment anything in medical images. arXiv preprint [arXiv:2304.12306v1](http://arxiv.org/abs/2304.12306v1) (2023)
- <span id="page-55-2"></span>20. Mann, B., Ryder, N., Subbiah, M., Kaplan, J., Dhariwal, P., Neelakantan, A., Shyam, P., Sastry, G., Askell, A., Agarwal, S., et al.: Language models are fewshot learners. arXiv preprint [arXiv:2005.14165](http://arxiv.org/abs/2005.14165) (2020)
- <span id="page-55-15"></span>21. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-55-1"></span>22. Ronneberger, O., Fischer, P., Brox, T.: U-net: Convolutional networks for biomedical image segmentation. In: Medical Image Computing and Computer-Assisted Intervention–MICCAI 2015: 18th International Conference, Munich, Germany, October 5-9, 2015, Proceedings, Part III 18. pp. 234–241. Springer (2015)

- <span id="page-56-4"></span>23. Tomar, N.K., Jha, D., Bagci, U., Ali, S.: Tganet: Text-guided attention for improved polyp segmentation. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 151–160. Springer (2022)
- <span id="page-56-1"></span>24. Wu, J., Fu, R., Fang, H., Liu, Y., Wang, Z., Xu, Y., Jin, Y., Arbel, T.: Medical sam adapter: Adapting segment anything model for medical image segmentation. arXiv preprint [arXiv:2304.12620](http://arxiv.org/abs/2304.12620) (2023)
- <span id="page-56-2"></span>25. Zhang, K., Liu, D.: Customized segment anything model for medical image segmentation. arXiv preprint [arXiv:2304.13785](http://arxiv.org/abs/2304.13785) (2023)
- <span id="page-56-3"></span>26. Zhong, Z., Tang, Z., He, T., Fang, H., Yuan, C.: Convolution meets lora: Parameter efficient finetuning for segment anything model. arXiv preprint [arXiv:2401.17868](http://arxiv.org/abs/2401.17868) (2024)
- <span id="page-56-0"></span>27. Zhou, Z., Siddiquee, M.M.R., Tajbakhsh, N., Liang, J.: Unet++: Redesigning skip connections to exploit multiscale features in image segmentation. IEEE Trans. Med. Imaging 39(6), 1856–1867 (2019)

Image /page/57/Picture/0 description: A square button with rounded corners has a circular icon at the top and the text "Check for updates" below it. The icon is a circle with a curved line segment on the left and a flag shape on the right. The button is light gray with a subtle gradient, and the text is dark gray.

# M<sup>4</sup>oE: A Foundation Model for Medical Multimodal Image Segmentation with Mixture of Experts

Yufeng Jiang<sup>[1](http://orcid.org/0009-0004-4987-2683)</sup> and Yiqing Shen<sup>2( $\boxtimes$ [\)](http://orcid.org/0000-0001-7866-3339)</sup>

<sup>1</sup> Department of Computer Science, Hong Kong Baptist University, Hong Kong SAR, China

<sup>2</sup> Department of Computer Science, Johns Hopkins University, Baltimore, USA <EMAIL>

Abstract. Medical imaging data is inherently heterogeneous across different modalities and clinical centers, posing unique challenges for developing generalizable foundation models. Conventional entails training distinct models per dataset or using a shared encoder with modality-specific decoders. However, these approaches incur heavy computational overheads and suffer from poor scalability. To address these limitations, we propose the Medical Multimodal Mixture of Experts  $(M^4 oE)$  framework, leveraging the SwinUNet architecture. Specifically,  $M^4 oE$  comprises modality-specific experts; each separately initialized to learn features encoding domain knowledge. Subsequently, a gating network is integrated during fine-tuning to modulate each expert's contribution to the collective predictions dynamically. This enhances model interpretability and generalization ability while retaining expertise specialization. Simultaneously, the  $M^4 oE$  architecture amplifies the model's parallel processing capabilities, and it also ensures the model's adaptation to new modalities with ease.Experiments across three modalities reveal that  $M^4$ oE can achieve 3.45% over STU-Net-L, 5.11% over MED3D, and 11.93% over SAM-Med2D across the MICCAI FLARE22, AMOS2022, and ATLAS2023 datasets. Moreover,  $M^4$ oE showcases a significant reduction in training duration with 7 h less while maintaining a parameter count that is only 30% of its compared methods. The code is available at [https://github.com/JefferyJiang-YF/M4oE.](https://github.com/JefferyJiang-YF/M4oE)

Keywords: Foundation Model *·* Multimodal Medical Image Segmentation *·* Mixture of Experts (MoE) *·* SwinUNet

# 1 Introduction

The advent of vision foundation models such as Segment Anything Model (SAM) and Stable Diffusion [\[18\]](#page-67-0) has catalyzed remarkable progress in natural image

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_58.](https://doi.org/10.1007/978-3-031-72390-2_58)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 621–632, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_58)\_58

Image /page/58/Figure/1 description: This figure illustrates four different deep learning architectures for medical image segmentation. Architecture (a) shows separate encoders and decoders for each modality. Architecture (b) introduces a shared encoder and modality-specific decoders, along with a Mixture-of-Experts (M4oE) layer. Architecture (c) utilizes a shared encoder and a shared decoder, with a visual representation of segmented anatomical structures. Architecture (d) presents a shared encoder and a shared decoder, incorporating M4oE layers in both the encoder and decoder stages, and processing multiple medical image modalities. The figure also includes a legend explaining different modality streams and experts.

<span id="page-58-0"></span>Fig. 1. Paradigms for multimodal medical segmentation: (a) One network for each modality; (b) A shared encoder with modality-specific decoders; (c) Union of labels with pseudo-label training; (d) Our proposed  $M<sup>4</sup>oE$  with shared encoder and decoder.

understanding [\[14\]](#page-66-0). However, translating these advancements to medical imaging poses notable challenges [\[7](#page-66-1)] due to the heterogeneity across modalities like MRI, CT, and X-rays, each exhibiting distinct data characteristics [\[8](#page-66-2)]. Consequently, medical imaging necessitates specialized processing beyond the capabilities of models designed for more homogeneous natural images. Historically, coping with multimodal medical data heterogeneity entailed developing customized models targeting individual datasets, as depicted in Fig.  $1(a)$  $1(a)$ . Although minimizing initial complexity, this approach introduces substantial overheads for adapting models to new datasets. Alternatively, following the multi-task learning design, some methods utilize a shared encoder with modality-specific decoders to extract common representations while retaining tailored processing [\[3,](#page-65-0)[20\]](#page-67-1), as in Fig. [1\(](#page-58-0)b). However, computational requirements can still scale poorly as data diversity grows [\[11](#page-66-3)]. Unified encoder-decoder schemes also exist to standardize label processing [\[12\]](#page-66-4), as shown in Fig.  $1(c)$  $1(c)$ , but may compromise extensibility to new modalities [\[1\]](#page-65-1).

To address these shortcomings, we introduce Medical Multimodal Mixture of Experts  $(M^4 oE)$ , built upon SwinUNet [\[2\]](#page-65-2), to effectively process multimodal medical images segmentation. The conventional Transformer architecture struggles to adapt to modality-specific attributes optimally [\[19\]](#page-67-2). To overcome this gap, M<sup>4</sup>oE initializes distinct experts focusing on unique aspects of each modality  $(Fig. 1(d))$  $(Fig. 1(d))$  $(Fig. 1(d))$ . A gating network then dynamically combines expert outputs during inference. The design of  $M^4$ oE enhances parallel processing capabilities and simplifies adapting to new modalities without extensive reconfiguration.

The contribution of this work is three-fold, summarized as follows. 1) We propose an innovative foundation model for multimodal medical image segmentation based upon a mixture of experts, named  $M^4$ oE. It allows efficient scaling to large heterogeneous datasets without drastic parameter increases, striking an optimal balance between broad applicability and computational efficiency. 2)We

propose  $M^4 oE$ 's two-phase training with modality-specific experts and, a fusing gating network, and a shuffle-adaptable linear projection architecture for multimodality and label mapping. 3) Our comprehensive experimental evaluations indicate that  $M^4$ oE achieves competitive performance on multimodal medical datasets and shows promising transferability, with reduced need for reconfiguration across different modalities.

# 2 Methods

We present  $M^4$ oE building on SwinUNet [\[2](#page-65-2)], where our model substitutes the MLP component with a Mixture-of-Experts designed specifically for multimodal medical image segmentation to improve representation.

Swin Transformer Backbone. The core backbone of our architecture leverages Swin Transformer blocks [\[15\]](#page-66-5). Contrasting conventional multi-headed selfattention (MHSA), Swin Transformers employ a local, sliding-window variant termed window-based MHSA (W-MSA). As illustrated in Fig. [2,](#page-60-0) each Swin Transformer block comprises a LayerNorm, a Multilayer Perceptron (MLP), W-MSA, and a shifted-window MHSA (SW-MSA). The sliding window approach limits self-attention computation to local patch regions, reducing complexity compared to global MHSA.

Medical Multi-modal Mixture-of-Experts (**M<sup>4</sup>**oE) Formulation. Our key innovation involves substituting the MLP layer in each Swin Transformer block with a  $M^4 oE$ , as depicted in Fig. [1\(](#page-58-0)d). This comprises a gating network along with a set of modality-specific expert networks. The experts themselves consist of two-layer MLPs, utilizing GELU for non-linear activation. Each expert network specializes in learning representations corresponding to a particular imaging modality. This allows dynamically allocating computational resources conditional on data complexity, enhancing the model's representation capacity. The gating network leverages a simple single-layer MLP activated by a softmax function to weigh the contribution of each expert dynamically based on the input.

Image /page/60/Figure/1 description: This figure illustrates a deep learning architecture for multi-modal medical image analysis, likely for tasks involving CT, MRI, and CE-MRI scans. The architecture features an encoder-decoder structure with a bottleneck. The encoder begins with patch partitioning and linear embedding, followed by multiple stages of Swin Transformer blocks with a Mixture-of-Experts (M4oE) design. Patch merging is used to reduce spatial dimensions and increase channel depth between stages. Skip connections are employed to transfer features from the encoder to corresponding stages in the decoder. The decoder mirrors the encoder's structure with patch expanding operations to increase spatial resolution. A bottleneck section, also utilizing Swin Transformer with M4oE blocks, connects the encoder and decoder. The M4oE with Gating Network is detailed in a magnified inset, showing how different experts are utilized based on gating signals. The input stage includes linear projection layers for each modality (CT, MRI, CE-MRI) with corresponding predict heads. The legend indicates different color-coded feature streams for CT, MRI, and CE-MRI, as well as experts for each modality. The dimensions of feature maps are indicated at various stages, such as WxH/4 x 48, WxH/4 x C, WxH/8 x 2C, WxH/16 x 4C, and WxH/32 x 8C.

<span id="page-60-0"></span>Fig. 2. Overall framework of  $M^4$ oE implemented on a SwinUNet backbone. At the core is the  $M^4$ oE with a gating network, which dynamically selects specialized experts for different imaging modalities (CT, CE-MRI, MRI); each expert learns representation for its respective data characteristics.

 $M^4$ oE in Swin Transformer. We incorporate the proposed  $M^4$ oE into Swin Transformer blocks as follows:

$$
\tilde{z}^{l} = \text{W-MSA}(\text{LN}(z^{l-1})) + z^{l-1},
$$

$$
z^{l} = \text{M}^{4}\text{oE}(\text{LN}(\tilde{z}^{l})) + \tilde{z}^{l},
$$

$$
\tilde{z}^{l+1} = \text{SW-MSA}(\text{LN}(z^{l})) + z^{l},
$$

$$
z^{l+1} = \text{M}^{4}\text{oE}(\text{LN}(\tilde{z}^{l+1})) + \tilde{z}^{l+1}
$$
 $(1)$ 

where  $\tilde{z}$  and  $z$  denote the W/SW-MSA and  $M^4$ oE representations of the *l<sup>th</sup>* block<br>respectively Within  $M^4$ oE, the gating network assigns weights to each expert's respectively. Within  $M^4 oE$ , the gating network assigns weights to each expert's output as follows:

$$
\begin{aligned}\n\text{weight}_{G} &= \text{SoftMax}(W_g \cdot \mathbf{x} + \mathbf{b}_g) = \frac{\exp(\text{weights}_G)}{\sum_j \exp(\text{weights}_{G_j})}, \\
\text{where} \quad \text{expert}_i(\mathbf{x}) &= W_{o,i} \cdot \text{GELU}(W_{h,i} \cdot \mathbf{x} + \mathbf{b}_{h,i}) + \mathbf{b}_{o,i},\n\end{aligned} \tag{2}
$$

where weight<sub>G</sub> is the gating weights to the input **x**, the gating weights are computed<br>using the SoftMax function for assigning different weights based on other representausing the SoftMax function for assigning different weights based on other representations.  $W_{h,i}$ ,  $\mathbf{b}_{h,i}$ ,  $W_{o,i}$ , and  $\mathbf{b}_{o,i}$  are the learnable weights and biases of the i<sup>th</sup> expert

<span id="page-61-0"></span>**Table 1.** Comparison of  $M^4$ oE against prior arts in terms of crucial model design choices and computational requirements, including pre-training strategy, input dimensionality, multimodality handling capability, number of parameters, training duration, and parameters added when introducing new modalities.

| Methods                    | Pre-train                          |     |                  |           | $ 2D/3D $ Multimodaliy $ \#$ Param Training hours Introduce new modality |
|----------------------------|------------------------------------|-----|------------------|-----------|--------------------------------------------------------------------------|
| DoDNet [20]                |                                    | 3D  | $17.3\mathrm{M}$ | $ 48 \>h$ |                                                                          |
| $MED3D$ [3]                |                                    | ¦3D | 117.51M 16 h     |           | 21 M                                                                     |
| Liu's $[13]$               |                                    | ¦3D | 6.66M            | 19.5h     | $6.66$ M                                                                 |
| STU-Net-L $[9]$ Supervised |                                    | 3D  | 440.30M 156.8 h  |           | 440.30 M                                                                 |
| Ours                       | $\sqrt{\text{Self-supervised}}$ 2D |     | 27.31M 12 h      |           | 10 <sub>M</sub>                                                          |

network. The outputs of all experts are computed and stacked together to form a tensor *i.e.*, expert  $1(\mathbf{x})$ , expert  $2(\mathbf{x})$ ... expert num experts(**x**). It follows that the output of the  $M<sup>4</sup>oE$  module is the weighted sum of all expert outputs, namely

outputs = 
$$
\sum_{i=1}^{n} weight_i \cdot expert_i(\mathbf{x}),
$$
 (3)

where weight<sub>i</sub> is the i<sup>th</sup> element of the weights vector, representing the weight<br>assigned to the i<sup>th</sup> expert's output assigned to the i<sup>th</sup> expert's output.

Dynamic Selection for Linear Projection Head. A core challenge in multimodal segmentation involves handling varying class numbers across modalities. We introduce a dynamic selection mechanism through customizable linear projection heads inserted before output to address this. First, feature maps are padded to equalize dimensions across all samples regardless of modality. Subsequently, each sample's linear projection layer dynamically selects only the logits corresponding to the classes present. Thereby, the final output dimensions are aligned with the native label distribution in a modular, input-conditional manner. This process grants invariance to shuffling across modalities during training. Meanwhile, the lightweight projections minimize overheads, allowing efficient reconfiguration of new datasets.

Two-Phase Training Strategy. We propose a novel two-phase training strategy consisting of expert pre-training and gate network fine-tuning phases. In the expert pre-training phase, we leverage the Masked Autoencoder(MAE) [\[6\]](#page-66-7) to pre-train each expert. This enables it to learn meaningful representations for its modality without the gating network. Subsequently, in the gate network fine-tuning phase, we load the pre-trained encoder and expert parameters and incorporate the gating network. We then fine-tune the entire  $M^4 oE$  model endto-end on downstream segmentation tasks. This two-phase approach allows the experts to specialize in their respective modalities before learning to selectively combine their representations via the gating network.

# 3 Experiments

Implementations. All experiments leveraged a single RTX 4090 GPU system with 24GB memory, running PyTorch 1.11.0, Python 3.8 on Ubuntu 20.04, and CUDA 11.3. To initialize feature representations, the  $M<sup>4</sup>oE$  in encoder was pretrained via MAE  $[5,6]$  $[5,6]$  $[5,6]$ . The decoder, optimization settings, input resolution, and patch size were configured identically to SwinUNet [\[2](#page-65-2)] for fair comparison. Specifically, training employed a batch size of 36 and weight decay of  $10^{-4}$  using AdamW optimization.

Datasets. We leverage three multimodal medical image segmentation datasets to evaluate  $M^4$ oE's performance, namely FLARE22 [\[16\]](#page-66-8), AMOS22 [\[10\]](#page-66-9), and ATLAS23 [\[17\]](#page-66-10). Specifically, from FLARE22, we utilize 50 labeled CT scan cases. AMOS22 provides 600 labeled cases comprising 500 CT and 100 MRI abdominal scans. Additionally, we incorporate 60 cases of contrast-enhanced MRI (CE-MRI) liver scans with labels from ATLAS23. We follow the previous work [\[9](#page-66-11)] for data split. While FLARE22 and AMOS22 focus on segmenting abdominal organs, ATLAS23 specializes in segmenting liver and tumors. This allows assessing  $M^4 oE$ 's capability in handling multimodal datasets across diverse anatomical segmentation tasks (Fig. [3\)](#page-63-0).

Results. As shown in Table [1,](#page-61-0) DoDNet [\[20](#page-67-1)], MED3D [\[3\]](#page-65-0), and Liu's [\[13](#page-66-6)] methods lack pre-training, potentially limiting learned representations. Although focusing on 3D data, DoDNet and MED3D partially mitigate this via multimodality. MED3D's efficiency is evident with its short training duration despite a high parameter count, while Liu's method, with fewer parameters, might be better generalized. Meanwhile, STU-Net-L's [\[9\]](#page-66-11) supervised pre-training potentially improves initial learning but necessitates labeled data, and its high parameter count and extended training time could be drawbacks. Our approach introduces self-supervised pre-training on 2D data, eliminating the need for labeled datasets and showing cost-effectiveness. It balances model complexity and resource efficiency, proving advantageous for practical applications where performance and efficiency are paramount. As shown in Table [2,](#page-64-0) our approach excels in the FLARE22 and AMOS-CT datasets, with the highest mean dice scores (DSC), indicating its suitability for multimodal tasks and accurate segmentation. Our method shows slight performance decreases when introducing MRI modality, evidencing its adaptability, whereas DoDNet and MED3D exhibit notable performance drops. Our model's training across diverse datasets likely contributes to its robustness, as opposed to STU-Net-L, which although achieving high DSC in a single-task setting, lacks versatility. SAM-Med2D's lower performance might stem from its 2D focus, potentially failing to capture 3D medical imaging complexities. Our method outperforms the ATLAS2023 dataset, maintaining robustness with new modalities and leading in DSC and mIoU metrics, as seen in Table [3.](#page-64-1) This underscores the limitations of DoDNet and MED3D in modality

Image /page/63/Figure/1 description: This image displays a grid of medical scans, likely CT or MRI, showing cross-sections of the abdomen. Each row presents different segmentation results for the same anatomical slices. The columns are labeled at the bottom: 'Image', 'GT' (Ground Truth), 'DoDNet', 'MED3D', 'SAM-Med2D', and 'Ours'. The scans show various organs, including the kidneys, liver, spleen, and spine, with different colors representing segmented regions. The top rows show scans with multiple organs segmented in different colors. The middle rows focus on the liver and kidneys, with some scans showing the organs in grayscale and others with color overlays indicating segmentation. The bottom rows display more detailed segmentations of abdominal organs, with colors like purple, blue, green, and red highlighting different structures. The overall presentation compares the performance of different segmentation methods against the ground truth.

<span id="page-63-0"></span>Fig. 3. Visualization of segmentation results from various methods. The first and second rows depict FLARE22 with CT images, the third row showcases ATLAS2023 with CE-MRI images, the fourth and sixth rows display AMOS2022 with CT images, and the fifth row presents MRI images from AMOS2022. The six columns from left to right correspond to the original image, the ground truth (GT), the DoDNet results, the MED3D results, the SAM-MED2D results, and our  $M^4$ oE results.

variations, while our model's balanced approach offers a promising direction for multimodal medical image segmentation.

Ablation Study. The ablation study in Table [4](#page-65-4) assesses the impact of implementing the  $M^4 oE$  architecture in different components of a segmentation network. It indicates that integrating the  $M<sup>4</sup>oE$  structure into both the encoder and decoder significantly enhances the model's segmentation performance, as evidenced by the highest mean DSC score of 82.15%. Models using  $M^4 oE$  in either the encoder or decoder showed improved results over the baseline model without  $M^4$ oE, but the full incorporation into both yielded the best outcomes. The  $M<sup>4</sup>oE$  architecture benefits the backbone model by enabling effective multimodal data integration, capturing diverse contextual information at multiple scales, and modulating features to emphasize relevant patterns for accurate segmentation. Additionally, the synergy between the encoder and decoder when both use the M<sup>4</sup>oE structure results in more coherent feature learning and reconstruction. <span id="page-64-0"></span>**Table 2.** Comparison of DSC (%) across multimodal segmentation methods. Our  $M^4 oE$ model outperforms others, achieving the highest DSC in FALRE and AMOS-CT tasks. It shows adaptability and improvement (\*) upon introducing a new modality, contrary to DoDNet and MED3D, which see performance drops. Unlike STU-Net-L's singledataset focus, our model's robustness is attributed to training on three varied datasets. SAM-Med2D's performance is evaluated using a single data point. Results are highlighted in red for best and blue for second best.

| Datasets                  | Method                    |             | Liver Left Kidney Right Kidney Gallbladder Pancreas Spleen Stomach Mean |       |       |       |       |       |
|---------------------------|---------------------------|-------------|-------------------------------------------------------------------------|-------|-------|-------|-------|-------|
|                           | DoDNet [20]               | 96.64 95.43 | 95.32                                                                   | 74.58 | 79.48 | 90.91 | 90.85 | 89.03 |
|                           | MED3D <sup>[3]</sup>      | 90.3687.23  | 88.47                                                                   | 67.84 | 73.82 | 91.98 | 92.30 | 84.53 |
| FLARE22                   | Liu's $[13]$              | 89.30 76.32 | 78.40                                                                   | 55.35 | 58.86 | 83.07 | 67.82 | 72.73 |
|                           | $STU-Net-L$ [9]           | 94.48 87.51 | 86.68                                                                   | 76.31 | 82.00 | 91.27 | 89.31 | 86.79 |
|                           | SAM-Med2D [4] 80.37 77.96 |             | 78.19                                                                   | 50.37 | 53.12 | 82.47 | 75.01 | 71.07 |
|                           | Ours                      | 97.08 94.62 | 94.92                                                                   | 79.38 | 76.99 | 95.74 | 89.78 | 89.78 |
|                           | DoDNet [20]               | 92.1483.93  | 82.48                                                                   | 70.73 | 73.69 | 90.58 | 85.14 | 82.67 |
|                           | $MED3D$ $3$               | 88.25 85.11 | 85.29                                                                   | 65.12 | 59.78 | 88.37 | 81.10 | 79.00 |
|                           | $AMOS-CTSTU-Net-L$ [9]    | 95.47 88.37 | 89.74                                                                   | 76.92 | 82.64 | 93.24 | 87.03 | 87.63 |
|                           | SAM-Med2D [4] 67.51 71.09 |             | 68.31                                                                   | 57.93 | 51.24 | 76.12 | 72.93 | 66.45 |
|                           | Ours                      | 95.59 88.72 | 88.31                                                                   | 73.94 | 61.39 | 90.59 | 82.78 | 83.04 |
|                           | DoDNet [20]               | 89.31 80.33 | 82.89                                                                   | 70.13 | 68.32 | 85.80 | 83.23 | 80.01 |
| AMOS-                     | $MED3D$ $[3]$             | 83.42 82.11 | 86.33                                                                   | 69.12 | 63.20 | 86.23 | 80.79 | 78.74 |
| $\text{CT+} \text{MRI}^*$ | STU-Net-L [9]             | 95.59 89.00 | 90.10                                                                   | 79.97 | 94.08 | 94.37 | 94.42 | 91.08 |
|                           | SAM-Med2D [4] 55.29 63.27 |             | 62.57                                                                   | 48.18 | 50.12 | 61.29 | 68.10 | 58.40 |
|                           | Ours                      | 94.38 89.20 | 88.16                                                                   | 76.37 | 69.12 | 91.23 | 85.34 | 84.83 |

<span id="page-64-1"></span>

| <b>Table 3.</b> Results on different methods on ATLAS2023, we use one point to test SAM- |
|------------------------------------------------------------------------------------------|
| Med2D on test sets. The DoDNet and MED3D are not robust when the new modality            |
| is introduced. The text is in red and in blue for the best and second-best results.      |

| Methods                   | DSC(%) |                                   |                   | mIoU $(\%)$ |  |       |  |
|---------------------------|--------|-----------------------------------|-------------------|-------------|--|-------|--|
|                           |        | Liver Tumor Mean Liver Tumor Mean |                   |             |  |       |  |
| DoDNet[20]                |        | 79,54 24.03 52.34 75.31 19.3      |                   |             |  | 47.31 |  |
| $MED3D$ [3]               |        | 73.12 19.55 46.34 65.3 16.82      |                   |             |  | 41.06 |  |
| SAM-Med2D [4] 80.02 51.28 |        |                                   | 65.65 77.34 48.88 |             |  | 63.11 |  |
| Ours                      |        | 89.43 57.53 73.48 86.34 55.32     |                   |             |  | 70.83 |  |

The presence of a gating network also contributes to performance improvements, suggesting its role in enhancing feature flow control within the network.

<span id="page-65-4"></span>Table 4. Ablation study on different model configurations. Encoder or decoder marked with  $\chi$  indicates the use of an MLP as the output layer for the Swin Transformer block. In contrast,  $\checkmark$  denotes that the encoder or decoder incorporates our M<sup>4</sup>oE structure. 'GN' stands for gating network. The ablation studies reveal that employing the  $M^4 oE$ architecture in both encoder and decoder significantly enhances model performance. The text is in red and in blue for the best and second-best results.

| Method |  | DSC(%) |             |  |       |  |       |  |                                                                                                                          |       |       |       |       |
|--------|--|--------|-------------|--|-------|--|-------|--|--------------------------------------------------------------------------------------------------------------------------|-------|-------|-------|-------|
|        |  |        |             |  |       |  |       |  | Encoder Decoder GN M <sup>4</sup> oE Liver Liver Tumor Left Kidney Right Kidney Gallbladder Pancreas Spleen Stomach Mean |       |       |       |       |
| x      |  |        | 93.61 51.23 |  | 84.65 |  | 84.51 |  | 69.34                                                                                                                    | 63.21 | 92.37 | 81.20 | 77.52 |
| ✓      |  |        | 93.21 55.12 |  | 89.27 |  | 89.15 |  | 75.41                                                                                                                    | 68.32 | 91.12 | 83.25 | 80.61 |
| ✓      |  |        | 93.5853.21  |  | 90.01 |  | 90.60 |  | 76.01                                                                                                                    | 65.33 | 90.87 | 82.79 | 80.30 |
| x      |  |        | 94.08 53.25 |  | 88.98 |  | 89.01 |  | 75.76                                                                                                                    | 69.31 | 92.37 | 85.81 | 81.07 |
| x      |  |        | 94.30 53.19 |  | 90.12 |  | 88.91 |  | 74.11                                                                                                                    | 68.45 | 91.36 | 85.43 | 80.73 |
| ✓      |  |        | 93.12 55.37 |  | 88.67 |  | 87.56 |  | 75.39                                                                                                                    | 66.29 | 90.12 | 83.04 | 79.95 |
|        |  |        | 94.1257.53  |  | 90.85 |  | 90.46 |  | 76.56                                                                                                                    | 69.16 | 92.52 | 85.97 | 82.15 |

# 4 Conclusion

The proposed Medical Multimodal Mixture of Experts  $(M<sup>4</sup>oE)$  presents a novel and efficient direction toward developing foundation models for multimodal medical image segmentation. By effectively integrating modality-specific experts within a flexible gating mechanism,  $M^4 oE$  demonstrates accurate and adaptable segmentation across diverse imaging datasets. We believe the expert specialization and dynamic ensemble concepts presented here will provide guiding principles for tackling multimodal challenges in medical imaging and beyond. For future direction, evaluating  $M^4 oE$  over larger-scale datasets, augmenting with uncertainty estimation, and enhancing model transparency could further facilitate clinical integration.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

## References

- <span id="page-65-1"></span>1. Anwar, S.M., Majid, M., Qayyum, A., Awais, M., Alnowami, M., Khan, M.K.: Medical image analysis using convolutional neural networks: a review. Journal of medical systems 42, 1–13 (2018)
- <span id="page-65-2"></span>2. Cao, H., Wang, Y., Chen, J., Jiang, D., Zhang, X., Tian, Q., Wang, M.: Swinunet: Unet-like pure transformer for medical image segmentation. In: European conference on computer vision. pp. 205–218. Springer (2022)
- <span id="page-65-0"></span>3. Chen, S., Ma, K., Zheng, Y.: Med3d: Transfer learning for 3d medical image analysis. arXiv preprint [arXiv:1904.00625](http://arxiv.org/abs/1904.00625) (2019)
- <span id="page-65-5"></span>4. Cheng, J., Ye, J., Deng, Z., Chen, J., Li, T., Wang, H., Su, Y., Huang, Z., Chen, J., Jiang, L., et al.: Sam-med2d. arXiv preprint [arXiv:2308.16184](http://arxiv.org/abs/2308.16184) (2023)
- <span id="page-65-3"></span>5. Dai, Y., Liu, F., Chen, W., Liu, Y., Shi, L., Liu, S., Zhou, Y., et al.: Swin mae: Masked autoencoders for small datasets. Computers in Biology and Medicine 161, 107037 (2023)

- <span id="page-66-7"></span>6. He, K., Chen, X., Xie, S., Li, Y., Dollár, P., Girshick, R.: Masked autoencoders are scalable vision learners. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 16000–16009 (2022)
- <span id="page-66-1"></span>7. Heiliger, L., Sekuboyina, A., Menze, B.H., Egger, J., Kleesiek, J.: Beyond medical imaging - a review of multimodal deep learning in radiology  $(2022)$ . [https://doi.](https://doi.org/10.36227/techrxiv.19103432.v1) [org/10.36227/techrxiv.19103432.v1](https://doi.org/10.36227/techrxiv.19103432.v1)
- <span id="page-66-2"></span>8. Huang, H., Lin, L., Tong, R., Hu, H., Zhang, Q., Iwamoto, Y., Han, X., Chen, Y.W., Wu, J.: Unet  $3+$ : A full-scale connected unet for medical image segmentation. In: ICASSP 2020-2020 IEEE international conference on acoustics, speech and signal processing (ICASSP). pp. 1055–1059. IEEE (2020)
- <span id="page-66-11"></span>9. Huang, Z., Wang, H., Deng, Z., Ye, J., Su, Y., Sun, H., He, J., Gu, Y., Gu, L., Zhang, S., et al.: Stu-net: Scalable and transferable medical image segmentation models empowered by large-scale supervised pre-training. arXiv preprint [arXiv:2304.06716](http://arxiv.org/abs/2304.06716) (2023)
- <span id="page-66-9"></span>10. Ji, Y., Bai, H., Ge, C., Yang, J., Zhu, Y., Zhang, R., Li, Z., Zhang, L., Ma, W., Wan, X., Luo, P.: AMOS: A large-scale abdominal multi-organ benchmark for versatile medical image segmentation. In: Koyejo, S., Mohamed, S., Agarwal, A., Belgrave, D., Cho, K., Oh, A. (eds.) Advances in Neural Information Processing Systems 35: Annual Conference on Neural Information Processing Systems 2022, NeurIPS 2022, New Orleans, LA, USA, November 28 - December 9, 2022 (2022), [http://papers.nips.cc/paper\\_files/paper/2022/hash/](http://papers.nips.cc/paper_files/paper/2022/hash/ee604e1bedbd069d9fc9328b7b9584be-Abstract-Datasets_and_Benchmarks.html) [ee604e1bedbd069d9fc9328b7b9584be-Abstract-Datasets\\_and\\_Benchmarks.html](http://papers.nips.cc/paper_files/paper/2022/hash/ee604e1bedbd069d9fc9328b7b9584be-Abstract-Datasets_and_Benchmarks.html)
- <span id="page-66-3"></span>11. Johnson, A.E., Pollard, T.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Peng, Y., Lu, Z., Mark, R.G., Berkowitz, S.J., Horng, S.: Mimic-cxr-jpg, a large publicly available database of labeled chest radiographs. arXiv preprint [arXiv:1901.07042](http://arxiv.org/abs/1901.07042) (2019)
- <span id="page-66-4"></span>12. Liu, P., Deng, Y., Wang, C., Hui, Y., Li, Q., Li, J., Luo, S., Sun, M., Quan, Q., Yang, S., et al.: Universal segmentation of 33 anatomies. arXiv preprint [arXiv:2203.02098](http://arxiv.org/abs/2203.02098) (2022)
- <span id="page-66-6"></span>13. Liu, W., Xu, W., Yan, S., Wang, L., Li, H., Yang, H.: Combining selftraining and hybrid architecture for semi-supervised abdominal organ segmentation. FLARE@MICCAI (2022). [https://doi.org/10.1007/978-3-031-23911-3\\_25,](https://doi.org/10.1007/978-3-031-23911-3_25) <https://arxiv.org/abs/2207.11512v4>
- <span id="page-66-0"></span>14. Liu, X., Zhou, T., Wang, Y., Wang, Y., Cao, Q., Du, W., Yang, Y., He, J., Qiao, Y., Shen, Y.: Towards the unification of generative and discriminative visual foundation model: A survey. arXiv preprint [arXiv:2312.10163](http://arxiv.org/abs/2312.10163) (2023)
- <span id="page-66-5"></span>15. Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., Zhang, Z., Lin, S., Guo, B.: Swin transformer: Hierarchical vision transformer using shifted windows. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 10012–10022 (2021)
- <span id="page-66-8"></span>16. Ma, J., Zhang, Y., Gu, S., Ge, C., Ma, S., Young, A., Zhu, C., Meng, K., Yang, X., Huang, Z., Zhang, F., Liu, W., Pan, Y., Huang, S., Wang, J., Sun, M., Xu, W., Jia, D., Choi, J.W., Alves, N., de Wilde, B., Koehler, G., Wu, Y., Wiesenfarth, M., Zhu, Q., Dong, G., He, J., the FLARE Challenge Consortium, Wang, B.: Unleashing the strengths of unlabeled data in pan-cancer abdominal organ quantification: the flare22 challenge. arXiv preprint [arXiv:2308.05862](http://arxiv.org/abs/2308.05862) (2023)
- <span id="page-66-10"></span>17. Quinton, F., Popoff, R., Presles, B., Leclerc, S., Meriaudeau, F., Nodari, G., Lopez, O., Pellegrinelli, J., Chevallier, O., Ginhac, D., Vrigneaud, J.M., Alberini, J.L.: A tumour and liver automatic segmentation ( atlas) dataset on contrast- enhanced magnetic resonance imaging for hepatocellular carcinoma. Data (2023). [https://](https://doi.org/10.3390/data8050079) [doi.org/10.3390/data8050079,](https://doi.org/10.3390/data8050079) <https://www.mdpi.com/2306-5729/8/5/79>

- <span id="page-67-0"></span>18. Rombach, R., Blattmann, A., Lorenz, D., Esser, P., Ommer, B.: High-resolution image synthesis with latent diffusion models. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 10684–10695 (2022)
- <span id="page-67-2"></span>19. Shin, A., Ishii, M., Narihira, T.: Perspectives and prospects on transformer architecture for cross-modal tasks with language and vision. International Journal of Computer Vision (2021). [https://doi.org/10.1007/s11263-021-01547-8,](https://doi.org/10.1007/s11263-021-01547-8) [https://](https://arxiv.org/abs/2103.04037v2) [arxiv.org/abs/2103.04037v2](https://arxiv.org/abs/2103.04037v2)
- <span id="page-67-1"></span>20. Zhang, J., Xie, Y., Xia, Y., Shen, C.: Dodnet: Learning to segment multi-organ and tumors from multiple partially labeled datasets. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 1195–1204 (2021)

Image /page/68/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a segment removed, and a ribbon or bookmark shape inside. The text below the icon reads "Check for updates".

# Mammo-CLIP: A Vision Language Foundation Model to Enhance Data Efficiency and Robustness in Mammography

Shantanu Ghosh<sup>1( $\boxtimes$ )</sup>  $\bullet$ [,](http://orcid.org/0000-0003-4085-541X) Clare B. Poynton<sup>2</sup>, Shyam Visweswaran<sup>3</sup>, and Kayhan Batmanghelich<sup>1</sup>

<sup>1</sup> Department of Electrical and Computer Engineering, Boston University, Boston, MA, USA

<EMAIL>

<sup>2</sup> Boston University Chobanian and Avedisian School of Medicine, Boston, MA, USA

<sup>3</sup> Department of Biomedical Informatics, University of Pittsburgh, Pittsburgh, PA,

**USA** 

Abstract. The lack of large and diverse training data on Computer-Aided Diagnosis (CAD) in breast cancer detection has been one of the concerns that impedes the adoption of the system. Recently, pre-training with large-scale image text datasets via Vision-Language models (VLM) (*e.g.,* CLIP) partially addresses the issue of robustness and data efficiency in computer vision (CV). This paper proposes Mammo-CLIP, the first VLM pre-trained on a substantial amount of screening mammogramreport pairs, addressing the challenges of dataset diversity and size. Our experiments on two public datasets demonstrate strong performance in classifying and localizing various mammographic attributes crucial for breast cancer detection, showcasing data efficiency and robustness similar to CLIP in CV. We also propose Mammo-FActOR, a novel feature attribution method, to provide spatial interpretation of representation with sentence-level granularity within mammography reports. Code is available publicly: [https://github.com/batmanlab/Mammo-CLIP.](https://github.com/batmanlab/Mammo-CLIP)

# 1 Introduction

Breast cancer remains a leading cause of death among women, with a global death toll of 670K in 2022. Creating AI tools for cancer in mammograms has been a central focus for CAD  $[1,18,19]$  $[1,18,19]$  $[1,18,19]$  $[1,18,19]$ . Yet, training robust CAD models requires a large and diverse dataset, which is expensive due to the high cost of collecting large-scale annotations. VLMs (*e.g.,* CLIP [\[17\]](#page-77-2)) pre-trained on large image-text pairs partially improve robustness in CV. This paper introduces Mammo-CLIP,

Supplementary Information The online version contains supplementary material available at [https://doi.org/10.1007/978-3-031-72390-2\\_59.](https://doi.org/10.1007/978-3-031-72390-2_59)

<sup>-</sup>c The Author(s), under exclusive license to Springer Nature Switzerland AG 2024 M. G. Linguraru et al. (Eds.): MICCAI 2024, LNCS 15012, pp. 632–642, 2024. [https://doi.org/10.1007/978-3-031-72390-2](https://doi.org/10.1007/978-3-031-72390-2_59)\_59

the first VLM trained with mammogram-report pairs. Also, we propose Mammo-FActOR: Mammograms for Feature Attribution ConnecTing Observations and Reports, novel feature attribution method, enhancing interpretability by finding textual attribute-aligned visual representations.

VLMs align image-text representations in joint embedding space, providing significant benefits. Pretraining on large datasets enhances generalizability [\[3,](#page-76-1)[11\]](#page-77-3) and data-efficiency  $[8,17]$  $[8,17]$  $[8,17]$  for downstream tasks. The language encoder reduces reliance on costly attribute annotations for developing interpretable models [\[16,](#page-77-5) [24\]](#page-78-0). In medical imaging, radiology reports are a source of weak labels, aiding disease localization [\[14](#page-77-6)[,26](#page-78-1)]. Generalizability, interpretability, data efficiency, and weak supervision are crucial to improving diagnostic precision and insightful analysis in medical imaging.

Application of VLM in medical imaging has been mostly limited in chest X-ray (CXR) domain [\[4](#page-76-2)[,22](#page-78-2)[,23](#page-78-3),[25,](#page-78-4)[27\]](#page-78-5) due to availability of image-report dataset *e.g.,* MIMIC-CXR [\[6](#page-77-7)]. VLMs in other domains are mostly created with images and captions from PubMed or public forums [\[5,](#page-77-8)[7](#page-77-9)[,10](#page-77-10)]. Although fine-tuning the CLIP model on mammograms remains an option, fine-tinning reduces the image resolution to be consistent with the input dimension of CLIP. Reducing the resolution results in the loss of critical semantic visual cues essential for accurate interpretation and diagnosis. Also, identifying mammographic attributes requires the interpretation of high-resolution images, which CLIP does not offer.

Contribution. Our contributions are two fold: (1) We present Mammo-CLIP, the first VLM in mammography; (2) We introduce Mammo-FActOR, a novel feature attribution method, to align visual representations with attributes in mammography reports. Mammo-CLIP leverages a dataset of screening mammogramreport pairs. This dataset is collected by the University of Pittsburgh Medical Center (UPMC). Mammo-CLIP employs two key approaches for enhancing its learning capability: 1) a data augmentation strategy to expand the training data by incorporating external datasets containing images and mammographic attributes (*e.g.,* mass, calcification etc..) but lacking reports. We synthesize reports based on these attributes. 2) multi-view supervision (MVS) [\[8](#page-77-4)], to learn rich representations from limited data. Our experiments on various breast cancer detection and localization tasks, conducted across two publicly available datasets, exhibit data efficiency and robustness similar to those observed in the CV community. Although Mammo-CLIP is trained on the screening dataset, where the cancer is not explicitly mentioned, fine-tuning its representation can bootstrap data-scare classification and abnormality localization experiments in completely out-of-training distributions. The final contribution, Mammo-FActOR, successfully localizes the attributes without relying on the ground truth bounding boxes.

### 2 Method

The goal is to align image  $(x^I)$  and text  $(x^T)$  representations via image  $(f^I(\cdot))$ and text encoders  $(f^T(\cdot))$ . Our method consists of 3 components: 1) To enhance

Image /page/70/Figure/1 description: The image displays four distinct diagrams labeled (a), (b), (c), and (d), illustrating different aspects of a medical imaging analysis process. Diagram (a) shows two mammogram images, labeled xI and xI-tilde, with corresponding text descriptions indicating no mammographic evidence of malignancy or cancer, and recommending annual screening. Diagram (b) presents a process involving sentences generated from prompts, such as "a suspicious mass in upper left breast at anterior depth," and "there is a suspicious mass in upper left breast at mid depth," leading to findings like "Mass, Calc." These are then sampled and stored in a database with corresponding images. Diagram (c) illustrates a multimodal approach where image features (ZI) and text features (ZT) are extracted using separate functions, fI and fT, and then compared in a grid format, showing correlations with different patterns. Diagram (d) depicts a pipeline where an image is processed through a locked function fI, resized, and then fed into a cross-modal attention mechanism (π) involving both image and text features, ultimately leading to a classification of "Suspicious calc. in upper left..." and "Suspicious mass in upper left..."

<span id="page-70-0"></span>Fig. 1. Schematic view of our method. (a) Image-text augmentation for MVS. (b) Dataset augmentation by synthesizing reports using image-attribute datasets. (c) Mammo-CLIP pretraining strategy. (d) Feature attribution using Mammo-FACtoR.

generalizability from limited data, Mammo-CLIP is based on Multi-View Supervision (MVS) [\[8\]](#page-77-4). MVS leverages cross-modal self-supervision across original and augmented image-text pairs. 2) We use both instance and dataset augmentation strategies to bootstrap the generalizability further. 3) Finally, we propose Mammo-FActOR, a novel interpretable module to map the visual representation to the textual attributes. We discuss the architectures of each component in detail in the experiment section. Fig [1](#page-70-0) illustrates our approach.

**Notation.** Let  $(x_i^I, x_i^T)$  and  $(\tilde{x}_i^I, \tilde{x}_i^T)$  be the original and augmented image-text pair of patient *i* respectively Let  $Z^I = \{z^I\}_{I}^B$  and  $Z^T = \{z^T\}_{I}^B$ , be the **Notation.** Let  $(x_i^I, x_i^T)$  and  $(\tilde{x}_i^I, \tilde{x}_i^T)$  be the original and augmented image-text pair of patient i respectively. Let  $Z^I = \{z_i^I\}_{i=1}^{\bar{B}}$  and  $Z^T = \{z_i^T\}_{i=1}^{\bar{B}}$  be the program and text respectively of a hatch of size normalized representation of the image and text respectively of a batch of size B. Similarly,  $\tilde{Z}^I$  and  $\tilde{Z}^T$  denote the representation of the augmented imagetext pairs. We obtain the normalized representations by projecting the outputs from Mammo-CLIP's image and text encoders,  $f^I(\cdot)$  and  $f^T(\cdot)$ , to the same dimensions, followed by  $\ell_2$  normalization.

#### 2.1 Mammo-CLIP

Given a batch of representation pairs  $\mathcal{Z}, \mathcal{Z}$ , the following contrastive loss aligns the two representations by pulling paired representations closer while pushing unpaired ones,

$$
\mathcal{L}(\mathcal{Z}, \tilde{\mathcal{Z}}) = -\sum_{z \in \mathcal{Z}} \log \frac{\exp(\langle z, \tilde{z} \rangle / \tau)}{\sum_{\tilde{z} \in \tilde{\mathcal{Z}}} \exp(\langle z, \tilde{z} \rangle / \tau)},\tag{1}
$$

where  $\langle \cdot, \cdot \rangle$  and  $\tau$  are the similarity function and a learnable temperature to scale the logits, respectively. Mammo-CLIP utilizes self-supervision across every pair of representations using the following MVS loss,

<span id="page-71-0"></span>Table 1. Classification performance on VinDr dataset. We report Area Under the Curve (AUC) AUC for binary classification tasks to classify calcification and mass. We report accuracy for multi-class classification tasks to predict density.

| Label   | Model                                 | Pre-training Data Zero-shot (ZS) Linear Probe (LP) Finetune (FT) |      |                      |                        |      |
|---------|---------------------------------------|------------------------------------------------------------------|------|----------------------|------------------------|------|
|         |                                       |                                                                  | 100% |                      | 10% 50% 100%           | 100% |
|         | CLIP $w/ RN-50$                       | UPMC                                                             | 0.37 |                      | $0.42$ $0.51$ $0.55$   | 0.68 |
|         | $CLIP w / EN-B5$                      | UPMC                                                             | 0.53 | $0.53$ 0.60 0.65     |                        | 0.90 |
|         | Calcification Mammo-CLIP w/EN-B2 UPMC |                                                                  | 0.68 |                      | $0.90$ $0.92$ $0.92$   | 0.98 |
|         | Mammo-CLIP w/ EN-B5 UPMC              |                                                                  | 0.61 |                      | 0.91 0.93 0.94         | 0.98 |
|         | Mammo-CLIP w/ EN-B5 UPMC, VinDr       |                                                                  | 0.62 |                      | 0.920.940.96           | 0.98 |
|         | CLIP $w / RN-50$                      | UPMC                                                             | 0.20 | $0.34$ $0.42$ $0.49$ |                        | 0.57 |
| Mass    | CLIP $w/$ EN-B5                       | UPMC                                                             | 0.32 | 0.52 0.57 0.59       |                        | 0.78 |
|         | Mammo-CLIP w/ EN-B2 UPMC              |                                                                  | 0.58 | $0.69$ 0.72 0.73     |                        | 0.85 |
|         | Mammo-CLIP w/ EN-B5 UPMC              |                                                                  | 0.48 |                      | $0.73$ $0.78$ $0.79$   | 0.85 |
|         | Mammo-CLIP w/ EN-B5 UPMC, VinDr       |                                                                  | 0.76 |                      | 0.800.840.86           | 0.88 |
|         | CLIP $w/ RN-50$                       | UPMC                                                             | 0.04 | $0.55$ $0.61$ $0.69$ |                        | 0.72 |
|         | CLIP $w/$ EN-B5                       | UPMC                                                             | 0.10 | $0.76$ 0.76 0.78     |                        | 0.83 |
| Density | Mammo-CLIP w/ EN-B2 UPMC              |                                                                  | 0.13 | $0.80$ $0.82$ $0.84$ |                        | 0.85 |
|         | Mammo-CLIP w/ EN-B5 UPMC              |                                                                  | 0.15 |                      | $0.83 \, 0.84 \, 0.85$ | 0.88 |
|         | Mammo-CLIP w/ EN-B5 UPMC, VinDr       |                                                                  | 0.15 |                      | 0.830.860.86           | 0.88 |

$$
\mathcal{L} = \sum_{\mathcal{Z}, \tilde{\mathcal{Z}} \in \{\mathcal{Z}^I, \tilde{\mathcal{Z}}^I, \tilde{\mathcal{Z}}^T, \tilde{\mathcal{Z}}^T\}, \mathcal{Z} \neq \tilde{\mathcal{Z}}} \mathcal{L}(\mathcal{Z}, \tilde{\mathcal{Z}}). \tag{2}
$$

In our experiments, we down-weight  $\mathcal{L}(\mathcal{Z}^T, \tilde{\mathcal{Z}}^T)$ , the loss for the original and augmented text representation pair by half.

#### 2.2 Instance and Dataset Augmentation

**Instance Augmentation.** For  $i^{th}$  patient,  $x_i^I$  and  $\tilde{x}_i^I$  denote the CC and MLO views respectively. We also use affine and elastic transformations to extend the views respectively. We also use affine and elastic transformations to extend the augmented sets. Likewise,  $x_i^T$  and  $\tilde{x}_i^T$  denote the impression and findings sections of the mammography reports. We followed the technique proposed in [25] that of the mammography reports. We followed the technique proposed in [\[25\]](#page-78-4) that uses translation between two languages to generate augmented text.

Dataset Augmentation. In addition to the dataset containing paired mammograms and reports, we leverage a separate dataset containing mammograms and attributes (*e.g., mass, calcification*) to mitigate the challenge of the limited dataset further. To synthesize reports from the attribute, we ask a board-certified radiologist to construct a set of prompts using the values of the attributes (*positive*, *negative* etc..), subtypes of the attribute (*e.g., suspicious*, *obscured* etc..), laterality of the breast (*i.e., right* and *left*), depth of the breast (*i.e., anterior*, *mid*, *posterior* ) and position of the breast (*e.g., upper*, *lower* etc..). We synthesize the report by substituting the attribute values for each patient to a randomly selected prompt (see Fig. 1 in the Appendix).

| Model                           |      |      |  |  |                                               | Pre-training Data Zero-shot (ZS) Finetune (FT) Linear Probe (LP) |
|---------------------------------|------|------|--|--|-----------------------------------------------|------------------------------------------------------------------|
|                                 |      | 100% |  |  |                                               | 10% 50% 100% 100%                                                |
| CLIP w/ $RN-50$                 | UPMC | 0.28 |  |  | 0.62 0.67 0.72 0.58                           |                                                                  |
| CLIP $w/$ EN-B5                 | UPMC | 0.42 |  |  | 0.80 0.84 0.86 0.70                           |                                                                  |
| Mammo-CLIP w/ EN-B2 UPMC        |      | 0.60 |  |  | 0.82 0.86 0.89 0.72                           |                                                                  |
| Mammo-CLIP w/ EN-B5 UPMC        |      | 0.62 |  |  | $\vert 0.85 \vert 0.89 \vert 0.90 \vert 0.75$ |                                                                  |
| Mammo-CLIP w/ EN-B5 UPMC, VinDr |      | 0.60 |  |  |                                               | 0.850.900.910.79                                                 |

<span id="page-72-0"></span>Table 2. Classification performance on RSNA dataset to classify malignancy.

### 2.3 Mammo-FActOR

Attributes mentioned in the radiology report can be viewed as weak labels. The Mammon-CLIP learns to align image and text representation at the global level. To obtain a spatial interpretation of the learned representation at the granularity of the attribute, we develop Mammo-FActOR. The Mammo-FActOR learns to align sentences in the radiology reports containing an attribute to the channels of the frozen image encoder,  $f^I(\cdot)$ . Let's assume we have K different attributes and  $f^I(\mathbf{x}_i^I) \in \mathbb{R}^{C \times H \times W}$  is the output of the frozen image encoder<br>and  $\mathbf{t}^k \in \mathbb{R}^d$  is the representation of the septence containing the *k*'th attribute and  $\mathbf{t}_i^k \in \mathbb{R}^d$  is the representation of the sentence containing the k'th attribute in the report of the patient *i*. We use  $\mathcal{X}_+^k$  to denote all mammography images not containing the  $k$ 's attribute. For attribute  $k$ , the Mammo-FActOR learns a mapping  $\pi^k(\mathbf{t}_i^k, \mathbf{x}_i^I) = (\text{MLP}(f^I(\mathbf{x}_i^I); \theta_k) \mathbf{t}_i^k) \in \mathbb{R}^C$  representing the similarity of the channels with  $\mathbf{t}^k$ . The MLP $(\cdot, \theta_i)$  is a Multi-layer Perception parametrized the channels with  $\mathbf{t}_i^k$ . The MLP $(\cdot; \theta_k)$  is a Multi-layer Perception parametrized<br>by  $\theta_i$ . To learn the parameters of the MLP we minimize the contrastive loss by  $\theta_k$ . To learn the parameters of the MLP, we minimize the contrastive loss between images with and without attributes:

$$
\mathcal{L}^{\text{fac}} = -\sum_{i,c,k} \log \frac{\exp \left( \left[ \pi^k(\mathbf{t}_i^k, \mathbf{x}_i^I) \right]_c / \tau \right)}{\exp \left( \left[ \pi^k(\mathbf{t}_i^k, \mathbf{x}_i^I) \right]_c / \tau \right) + \sum_{x \in \mathcal{X}_-^k} \exp \left( \left[ \pi^k(\mathbf{t}_i^k, \mathbf{x}) \right]_c / \tau \right)},\tag{3}
$$

where [·]<br>perature where  $\lceil \cdot \rceil$  denotes c'th element of the input vector argument and  $\tau$  is the temperature. After training, we weigh the representations as,  $\pi^k(\cdot) f^I(\cdot) \in \mathbb{R}^{H \times W}$  to construct the textual aligned beatman for attribute k construct the textual aligned heatmap for attribute  $k$ .

## 3 Experiments

We aim to answer the following research questions: **RQ1.** Does Mammo-CLIP enhance zero-shot (ZS) capabilities and labeling efficiency in classification tasks? RQ2. Does Mammo-CLIP learn robust representations? RQ3. Does Mammo-CLIP boost the performance of label-efficient localization? RQ4. Does Mammo-FActOR enhance interpretability through attribute-aligned representation?

### 3.1 Datasets

UPMC. The UPMC dataset includes 13,829 patient-report pairs, resulting in 25,355 screening mammograms (BI-RADS 0–2) with patients having at least one

<span id="page-73-2"></span>

| Label | Model                                  | Pre-training Data Finetune |                     | Freeze Encoder          |
|-------|----------------------------------------|----------------------------|---------------------|-------------------------|
|       |                                        |                            |                     | 10% 50% 100% 100%       |
|       | CLIP w/ $RN-50$                        | UPMC                       | 0.03 0.11 0.17 0.08 |                         |
|       | CLIP $w/$ EN-B5                        | UPMC                       | 0.09 0.23 0.29 0.10 |                         |
|       | Calcification Mammo-CLIP w/ EN-B2 UPMC |                            |                     | 0.08 0.20 0.32 0.17     |
| Mass  | Mammo-CLIP w/ EN-B5 UPMC               |                            |                     | [0.12] 0.28] 0.35] 0.17 |
|       | Mammo-CLIP w/ EN-B5 UPMC, VinDr        |                            |                     | 0.10 0.25 0.35 0.17     |
|       | $CLIP$ w/ RN-50                        | UPMC                       | 0.23 0.29 0.36 0.15 |                         |
|       | CLIP $w/$ EN-B5                        | <b>UPMC</b>                | 0.34 0.45 0.49 0.28 |                         |
|       | Mammo-CLIP w/ EN-B2 UPMC               |                            | 0.38 0.50 0.55 0.37 |                         |
|       | Mammo-CLIP w/ EN-B5 UPMC               |                            | 0.41 0.52 0.58 0.35 |                         |
|       | Mammo-CLIP w/ EN-B5 UPMC, VinDr        |                            |                     | 0.430.540.580.39        |

Table 3. Localization performance (mAP) on VinDr dataset.

CC or MLO view. We use  $0.8/0.2$  for the train/val split. VinDr. The VinDr-Mammo [\[15](#page-77-11)] is a publicly available dataset comprising 5,000 exams (20,000 images) from Vietnam, each with four views, breast level BI-RADS assessment category  $(1-5)$ , breast density category  $(A-D)$ , annotating and location of mammographic attributes (*i.e.,* mass, calcifications, etc..). We use the train-test split in [\[15\]](#page-77-11).

**RSNA.** The RSNA-Mammo<sup>[1](#page-73-0)</sup> is a publicly available dataset having 11913 patients with 486 cancer cases. We use train/val/test split as 0.7/0.2/0.1.

### 3.2 Experimental Details

Pre-processing the Images. We use a rule-based approach to find the breast's ROI for all the datasets. We set values less than 40 to 0 and eliminate the consistently identical rows and columns, as they denote the background. It results in images with an average aspect ratio of 1:1.6 to 2, finally resized to  $1520 \times 912$ .

Pre-training. We develop 2 variant of Mammo-CLIP models: one pre-trained with the UPMC dataset only and the other using both the UPMC and VinDr datasets. Mammo-CLIP utilizes BioClinicalBERT [\[2\]](#page-76-3) as the text encoder and EfficientNet(EN)-B2 and B5  $[20]$  $[20]$  with ImageNet pre-trained weights as the image encoder. Image augmentations consist of 1) affine transformations with rotations up to  $20^{\circ}$ , a minimum translation of 0.1%, scaling factors [0.8, 1.2], and shearing by 20<sup>°</sup>; and 2) elastic transformations with  $(\alpha = 10, \sigma = 5)$ . Text augmenta-tions include sentence swaps and back-translation from Italian to English<sup>[2](#page-73-1)</sup>. We train Mammo-CLIP for 10 epochs in a mixed-precision manner and optimize by AdamW [\[13](#page-77-13)] with an initial learning rate 5e-5 and a weight decay 1e-4. We use a cosine-annealing learning-rate scheduler [\[12](#page-77-14)] with a warm-up for 1 epoch.

<span id="page-73-0"></span><sup>1</sup> [https://www.kaggle.com/competitions/rsna-breast-cancer-detection.](https://www.kaggle.com/competitions/rsna-breast-cancer-detection)

<span id="page-73-1"></span> $^{2}$  [https://huggingface.co/Helsinki-NLP.](https://huggingface.co/Helsinki-NLP)

Image /page/74/Figure/1 description: This image displays a comparison of "Ground-truth" and "Ours" results for detecting "Mass" and "Calcification" in mammograms. Each category features two columns: the left column shows the original mammogram with a blue bounding box indicating the ground truth location, and the right column shows a heatmap overlay with a blue bounding box highlighting the detected area. The heatmaps use a color scale from red (low probability) to yellow (high probability), with values ranging from 0.0 to 0.8. There are three examples shown for each category, "Mass" and "Calcification", demonstrating the model's performance in identifying these abnormalities.

<span id="page-74-0"></span>Fig. 2. Mammo-FACtoR localizes mass and calcification  $w/o$  the ground-truth bboxes from the VinDr dataset. For each pair, the left image denotes the original image with the ground-truth bbox, while the right one is the bbox predicted by Mammo-Factor.

Baseline. Using UPMC dataset and CLIP objective [\[17](#page-77-2)], we construct two baselines: 1) an image encoder w/ ResNet (RN)-50 initialized with CLIP weights and fine-tuned with  $224 \times 224$  images, 2) EfficientNet (EN)-B5 fine-tuned using the same pre-processed images as Mammo-CLIP. Both the baselines are pre-trained using the UPMC dataset, as CLIP only uses an image-text dataset, not an image-attribute dataset. Mammo-FActOR. Our Mammo-Factor is the same light-weight neural network [\[21](#page-77-15)] with linear layer→ReLU→linear layer. We train it with a learning rate 0.0001 and temperature  $(\tau)$  of 0.007 for 20 epochs.

Downstream Evaluation. Evaluation of the downstream tasks utilizes both the Mammo-CLIP's image representations to 1) classify mass, calcification, and density in the VinDr dataset and cancer in the RSNA dataset; 2) localize mass and calcification in the VinDr dataset on a held-out test set. We evaluate the classification tasks in 3 settings: zero-shot (ZS), linear probe (LP), and fine-tuning (FT). In ZS and LP, we freeze both the encoders of Mammo-CLIP. For FT, we jointly fine-tune the image encoder of Mammo-CLIP and the linear classifier. For zero-shot (ZS) evaluation, we use prompts as  $\{No \langle E \rangle, \langle E \rangle\}$ , where E denotes mass, calcification, and cancer. Refer to Table 1 in the Appendix for prompts for density. LP on VinDr utilizes varying amounts of the training data (10%, 50%, or 100%). For FT on VinDr, we use 100% data. As the screening mammogram reports do not mention malignancy explicitly, data-efficient cancer classification for the RSNA dataset involves fine-tuning with varying proportions of the training data (10%, 50%, or 100%). We also report LP for the RSNA dataset using the entire training set. The localization task includes fine-tuning the image encoder of Mammo-CLIP using RetinaNet [\[9\]](#page-77-16) ( $\alpha = 0.6$ ,  $\gamma = 2.0$ ) model and 10%, 50%, and 100% of training data from VinDR. We also report the localization performance of RetinaNet by freezing Mammo-CLIP's image encoder with 100%

| Model                      | Mass                                        |      | Calcification |                |  |  |
|----------------------------|---------------------------------------------|------|---------------|----------------|--|--|
|                            | $IoU@0.25$ $IoU@0.50$ $IoU@0.25$ $IoU@0.50$ |      |               |                |  |  |
| Mammo-CLIP w/ $EN-B2 0.38$ |                                             | 0.22 | 0.2           | $ 0.13\rangle$ |  |  |
| Mammo-CLIP $w/$ EN-B5 0.45 |                                             | 0.37 | 0.25          | 0.17           |  |  |

<span id="page-75-0"></span>Table 4. Weakly supervised localization on VinDr using Mammo-FActOR.

of training data. Both setups in localization use only VinDr's suspicious findings. Metric. We report AUC scores for binary classification tasks to predict mass, calcification, and cancer and accuracy for multi-class classification tasks to predict density. For localization, we report mAP with  $IoU=0.5$  and detector confidence thresholded to 0.05 [\[9](#page-77-16)] unless specified.

### 4 Results

#### Classification (RQ1 & RQ2).

Table [1](#page-71-0) and [2](#page-72-0) illustrate the classification results for VinDr and RSNA, respectively. The Mammo-CLIP w/ EN-B5, pre-trained on the UPMC and VinDr datasets, outperforms all the other models in most of the evaluation settings. For VinDr, Mammo-CLIP's LP efficacy with 10% of data surpasses the FT performance of the baselines with 100% data. Also, Mammo-CLIP excels in cancer classification on the RSNA dataset, showing its robustness, even when cancer is not explicitly mentioned in the reports.

CLIP  $w / RN-50$  backbone fails to capture the visual cues from low-resolution images. CLIP w/ EN-B5 addresses this by employing high-resolution images but struggles to learn rich representation primarily due to its optimization strategy focusing solely on the contrastive loss of the original image-text pairs. Highresolution images and MVS optimization strategy allow Mammo-CLIP w/ EN backbones to extract richer representations from limited data than baselines, improving the classification performance. Recall that the UPMC dataset consists of mammograms from a different population than the VinDr dataset. The image encoder from Mammo-CLIP pre-trained with the UPMC dataset shows excellent performance on both LP and FT settings on the VinDR dataset, showcasing the robustness of the learned representations. ZS results on some tasks (especially density) exhibit the need for more data.

**Localization (RQ2 & RQ[3](#page-73-2)).** Table 3 shows localization results in a supervised setting, utilizing the bounding boxes of VinDr. Mammo-CLIP w/EN-B5 pretrained with the UPMC and VinDr dataset outperforms other models in most settings. Refer to the Classification subsection (described above) for insights into such improvement. Also, the utilization of VinDr during the pre-training improves the performance of both downstream tasks.

Evaluating Mammo-FActOR (RQ4). We evaluate the Mammo-FActOR using a weakly supervised localization task to localize the mass and calcification of the VinDr dataset without using the ground truth bounding boxes (bboxes). We construct heatmaps using the Mammo-FActOR for both attributes. Following [\[26\]](#page-78-1), we extract isolated regions from the heatmap where pixel values are greater than the 95% quantile of the heatmap's pixel value distribution and generate bboxes. We estimate mAP by comparing the IoU between generated and ground truth bounding boxes. A generated box is true positive when IoU >  $T({\rm IoU})$ , where  $T(\cdot)$  is a threshold. Table [4](#page-75-0) demonstrates the localization results. Mammo-CLIP w/ EN-B5 for  $T(IoU) = 0.25$  outperforms the other variants for both the attributes. Figure [2](#page-74-0) shows the qualitative results. Mammo-FActOR aligns image representations with attributes-specific textual descriptions from reports. Thus, it successfully localizes the attributes without relying on ground-truth bounding boxes during finetuning. Also, Fig. 2 in the Appendix shows the top 3 feature maps by Mammo-FActOR for detecting these attributes.

# 5 Conclusion

This paper proposes a Mammo-CLIP and Mammo-FActOR to learn a crossmodal embedding, improving generalizability, data efficiency, and interpretability. Future directions include further leveraging vision transformers and crossattention mechanisms to enhance Mammo-CLIP and Mammo-FActOR.

Acknowledgments. This work was partially supported by the Pennsylvania Department of Health, NIH Award Number 1R01HL141813-01, and funding from the Hariri Institute for Computing, Boston University. We are grateful for the computational resources from Pittsburgh Super Computing grant number TG-ASC170024.

Disclosure of Interests. The authors have no competing interests to declare that are relevant to the content of this article.

# References

- <span id="page-76-0"></span>1. Alberdi, E., Povyakalo, A., Strigini, L., Ayton, P., Hartswood, M., Procter, R., Slack, R.: Use of computer-aided detection (cad) tools in screening mammography: a multidisciplinary investigation. The British journal of radiology  $78(\text{suppl} 1)$ , S31–S40 (2005)
- <span id="page-76-3"></span>2. Alsentzer, E., Murphy, J.R., Boag, W., Weng, W.H., Jin, D., Naumann, T., McDermott, M.: Publicly available clinical bert embeddings. arXiv preprint [arXiv:1904.03323](http://arxiv.org/abs/1904.03323) (2019)
- <span id="page-76-1"></span>3. Eslami, S., de Melo, G., Meinel, C.: Does clip benefit visual question answering in the medical domain as much as it does in the general domain? arXiv preprint [arXiv:2112.13906](http://arxiv.org/abs/2112.13906) (2021)
- <span id="page-76-2"></span>4. Huang, S.C., Shen, L., Lungren, M.P., Yeung, S.: Gloria: A multimodal global-local representation learning framework for label-efficient medical image recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 3942–3951 (2021)

- <span id="page-77-8"></span>5. Huang, Z., Bianchi, F., Yuksekgonul, M., Montine, T.J., Zou, J.: A visual–language foundation model for pathology image analysis using medical twitter. Nature medicine 29(9), 2307–2316 (2023)
- <span id="page-77-7"></span>6. Johnson, A., Lungren, M., Peng, Y., Lu, Z., Mark, R., Berkowitz, S., Horng, S.: Mimic-cxr-jpg-chest radiographs with structured labels. PhysioNet (2019)
- <span id="page-77-9"></span>7. Kim, C., Gadgil, S.U., DeGrave, A.J., Cai, Z.R., Daneshjou, R., Lee, S.I.: Fostering transparent medical image ai via an image-text foundation model grounded in medical literature. medRxiv (2023)
- <span id="page-77-4"></span>8. Li, Y., Liang, F., Zhao, L., Cui, Y., Ouyang, W., Shao, J., Yu, F., Yan, J.: Supervision exists everywhere: A data efficient contrastive language-image pretraining paradigm. In: International Conference on Learning Representations (2022), <https://openreview.net/forum?id=zq1iJkNk3uN>
- <span id="page-77-16"></span>9. Lin, T.Y., Goyal, P., Girshick, R., He, K., Dollár, P.: Focal loss for dense object detection. In: Proceedings of the IEEE international conference on computer vision. pp. 2980–2988 (2017)
- <span id="page-77-10"></span>10. Lin, W., Zhao, Z., Zhang, X., Wu, C., Zhang, Y., Wang, Y., Xie, W.: Pmc-clip: Contrastive language-image pre-training using biomedical documents. arXiv preprint [arXiv:2303.07240](http://arxiv.org/abs/2303.07240) (2023)
- <span id="page-77-3"></span>11. Liu, J., Zhang, Y., Chen, J.N., Xiao, J., Lu, Y., A Landman, B., Yuan, Y., Yuille, A., Tang, Y., Zhou, Z.: Clip-driven universal model for organ segmentation and tumor detection. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 21152–21164 (2023)
- <span id="page-77-14"></span>12. Loshchilov, I., Hutter, F.: Sgdr: Stochastic gradient descent with warm restarts. arXiv preprint [arXiv:1608.03983](http://arxiv.org/abs/1608.03983) (2016)
- <span id="page-77-13"></span>13. Loshchilov, I., Hutter, F.: Decoupled weight decay regularization. arXiv preprint [arXiv:1711.05101](http://arxiv.org/abs/1711.05101) (2017)
- <span id="page-77-6"></span>14. Müller, P., Meissen, F., Brandt, J., Kaissis, G., Rueckert, D.: Anatomy-driven pathology detection on chest x-rays. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 57–66. Springer (2023)
- <span id="page-77-11"></span>15. Nguyen, H.T., Nguyen, H.Q., Pham, H.H., Lam, K., Le, L.T., Dao, M., Vu, V.: Vindr-mammo: A large-scale benchmark dataset for computer-aided diagnosis in full-field digital mammography. Scientific Data  $10(1)$ , 277 (2023)
- <span id="page-77-5"></span>16. Oikarinen, T., Das, S., Nguyen, L.M., Weng, T.W.: Label-free concept bottleneck models. arXiv preprint [arXiv:2304.06129](http://arxiv.org/abs/2304.06129) (2023)
- <span id="page-77-2"></span>17. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., et al.: Learning transferable visual models from natural language supervision. In: International conference on machine learning. pp. 8748–8763. PMLR (2021)
- <span id="page-77-0"></span>18. Srivastava, S., Sharma, N., Singh, S.K., Srivastava, R.: Design, analysis and classifier evaluation for a cad tool for breast cancer detection from digital mammograms. International journal of Biomedical Engineering and technology 13(3), 270–300 (2013)
- <span id="page-77-1"></span>19. Srivastava, S., Sharma, N., Singh, S., Srivastava, R.: Quantitative analysis of a general framework of a cad tool for breast cancer detection from mammograms. Journal of Medical Imaging and Health Informatics 4(5), 654–674 (2014)
- <span id="page-77-12"></span>20. Tan, M., Le, Q.: Efficientnet: Rethinking model scaling for convolutional neural networks. In: International conference on machine learning. pp. 6105–6114. PMLR (2019)
- <span id="page-77-15"></span>21. Varma, M., Delbrouck, J.B., Hooper, S., Chaudhari, A., Langlotz, C.: Villa: Finegrained vision-language representation learning from real-world data. In: Proceed-

ings of the IEEE/CVF International Conference on Computer Vision. pp. 22225– 22235 (2023)

- <span id="page-78-2"></span>22. Wang, Z., Wu, Z., Agarwal, D., Sun, J.: Medclip: Contrastive learning from unpaired medical images and text. arXiv preprint [arXiv:2210.10163](http://arxiv.org/abs/2210.10163) (2022)
- <span id="page-78-3"></span>23. Wu, C., Zhang, X., Zhang, Y., Wang, Y., Xie, W.: Medklip: Medical knowledge enhanced language-image pre-training. Proceedings of the IEEE/CVF International Conference on Computer Vision (2023)
- <span id="page-78-0"></span>24. Yang, Y., Panagopoulou, A., Zhou, S., Jin, D., Callison-Burch, C., Yatskar, M.: Language in a bottle: Language model guided concept bottlenecks for interpretable image classification. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 19187–19197 (2023)
- <span id="page-78-4"></span>25. You, K., Gu, J., Ham, J., Park, B., Kim, J., Hong, E.K., Baek, W., Roh, B.: Cxrclip: Toward large scale chest x-ray language-image pre-training. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 101–111. Springer (2023)
- <span id="page-78-1"></span>26. Yu, K., Ghosh, S., Liu, Z., Deible, C., Batmanghelich, K.: Anatomy-guided weaklysupervised abnormality localization in chest x-rays. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 658–668. Springer (2022)
- <span id="page-78-5"></span>27. Zhang, Y., Jiang, H., Miura, Y., Manning, C.D., Langlotz, C.P.: Contrastive learning of medical visual representations from paired images and text. In: Machine Learning for Healthcare Conference. pp. 2–25. PMLR (2022)