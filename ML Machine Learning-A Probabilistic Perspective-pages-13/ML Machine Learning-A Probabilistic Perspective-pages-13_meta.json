{"table_of_contents": [{"title": "11 Mixture models and the EM algorithm", "heading_level": null, "page_id": 0, "polygon": [[76.5, 93.65625], [411.0, 93.65625], [411.0, 145.072265625], [76.5, 145.072265625]]}, {"title": "11.1 Latent variable models", "heading_level": null, "page_id": 0, "polygon": [[99.0, 207.0], [248.25, 207.0], [248.25, 218.00390625], [99.0, 218.00390625]]}, {"title": "11.2 Mixture models", "heading_level": null, "page_id": 0, "polygon": [[96.75, 544.5], [211.5, 544.5], [211.5, 555.29296875], [96.75, 555.29296875]]}, {"title": "11.2.1 Mixtures of Gaussians", "heading_level": null, "page_id": 2, "polygon": [[91.5, 425.25], [234.0, 425.25], [234.0, 436.32421875], [91.5, 436.32421875]]}, {"title": "11.2.2 Mixture of multinoullis", "heading_level": null, "page_id": 3, "polygon": [[89.2265625, 60.75], [240.0, 60.75], [240.0, 71.54736328125], [89.2265625, 71.54736328125]]}, {"title": "11.2.3 Using mixture models for clustering", "heading_level": null, "page_id": 3, "polygon": [[89.5078125, 328.5], [301.5, 328.5], [301.5, 338.5546875], [89.5078125, 338.5546875]]}, {"title": "11.2.4 Mixtures of experts", "heading_level": null, "page_id": 5, "polygon": [[89.015625, 336.75], [222.75, 336.75], [222.75, 347.4140625], [89.015625, 347.4140625]]}, {"title": "11.2.4.1 Application to inverse problems", "heading_level": null, "page_id": 7, "polygon": [[86.8359375, 489.0], [268.5, 489.0], [268.5, 499.60546875], [86.8359375, 499.60546875]]}, {"title": "11.3 Parameter estimation for mixture models", "heading_level": null, "page_id": 8, "polygon": [[96.75, 462.75], [345.0, 462.75], [345.0, 473.34375], [96.75, 473.34375]]}, {"title": "11.3.1 Unidentifiability", "heading_level": null, "page_id": 9, "polygon": [[91.5, 327.75], [207.75, 327.75], [207.75, 337.921875], [91.5, 337.921875]]}, {"title": "11.3.2 Computing a MAP estimate is non-convex", "heading_level": null, "page_id": 10, "polygon": [[89.6484375, 390.0], [328.5, 390.0], [328.5, 400.25390625], [89.6484375, 400.25390625]]}, {"title": "11.4 The EM algorithm", "heading_level": null, "page_id": 11, "polygon": [[96.75, 546.75], [222.890625, 546.75], [222.890625, 557.19140625], [96.75, 557.19140625]]}, {"title": "11.4.1 Basic idea", "heading_level": null, "page_id": 12, "polygon": [[91.5, 478.5], [179.578125, 478.5], [179.578125, 488.84765625], [91.5, 488.84765625]]}, {"title": "11.4.2 EM for GMMs", "heading_level": null, "page_id": 13, "polygon": [[90.0, 406.5], [194.625, 406.5], [194.625, 417.65625], [90.0, 417.65625]]}, {"title": "11.4.2.1 Auxiliary function", "heading_level": null, "page_id": 14, "polygon": [[86.0625, 61.5], [210.09375, 61.5], [210.09375, 71.62646484375], [86.0625, 71.62646484375]]}, {"title": "11.4.2.2 E step", "heading_level": null, "page_id": 14, "polygon": [[84.5859375, 287.25], [157.5, 287.25], [157.5, 297.73828125], [84.5859375, 297.73828125]]}, {"title": "11.4.2.3 M step", "heading_level": null, "page_id": 14, "polygon": [[84.75, 363.0], [159.75, 363.0], [159.75, 373.04296875], [84.75, 373.04296875]]}, {"title": "11.4.2.4 Example", "heading_level": null, "page_id": 15, "polygon": [[84.375, 135.75], [168.75, 135.75], [168.75, 146.2587890625], [84.375, 146.2587890625]]}, {"title": "11.4.2.5 K-means algorithm", "heading_level": null, "page_id": 15, "polygon": [[85.21875, 267.75], [213.75, 267.75], [213.75, 277.962890625], [85.21875, 277.962890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 30], ["SectionHeader", 3], ["Text", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4644, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 29], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1385, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["TableCell", 72], ["Line", 37], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Text", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["Equation", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["Line", 58], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 37], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1532, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 42], ["Text", 4], ["ListItem", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 55], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 973, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 39], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 50], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 899, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 48], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 54], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 48], ["TableCell", 20], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 34], ["Text", 7], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 563], ["Line", 96], ["Equation", 11], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 40], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 40], ["Line", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 749, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-13"}