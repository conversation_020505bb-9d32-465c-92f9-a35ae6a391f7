Image /page/0/Picture/0 description: A shiny, chrome-plated cat figurine sits on a colorful, patterned rug. The cat is in a seated position, with its head tilted slightly to the left and its eyes closed, giving it a serene or perhaps sleepy expression. It has a thin, copper-colored collar around its neck. The rug beneath it features a vibrant design with red as the dominant color, interspersed with intricate patterns in blue, yellow, and other colors. The background is dark, with a hint of a white surface and a lighter wall visible on the left side. The lighting highlights the metallic sheen of the cat figurine, creating reflections of the surrounding colors.

Image /page/0/Picture/2 description: A wooden Android robot figure is shown against a grey background. The robot has a rounded head with two black circular eyes and two small black antennae. Its body is cylindrical and made of woven wood, with two oval wooden arms extending from the sides. The robot stands on two short, cylindrical wooden legs. A shadow is cast behind the figure.

Image /page/0/Picture/4 description: An orange origami fox sits in the snow next to a white and teal origami unicorn. They are surrounded by bare, orange trees and snow-covered pine trees. The background is a soft blue with falling white snowflakes.

A chromeplated cat sculpture placed on a Persian rug. Android Mascot made from bamboo. Intricate origami of a fox and a unicorn in a snowy forest.

Image /page/0/Picture/6 description: A glass duck figurine stands in front of a framed painting of a landscape. The duck is translucent and appears to be made of glass, with orange feet and beak. The painting behind it depicts a green field with some trees and a blue sky. The overall scene is well-lit and appears to be indoors, possibly in a gallery or home setting.

Image /page/0/Picture/8 description: A raccoon wearing a black hat and a black raincoat stands in front of a window with raindrops on it. The raccoon is looking directly at the camera with its mouth slightly open. The background is blurred green foliage.

on the window.

Image /page/0/Picture/10 description: A blue velvet handbag is adorned with intricate embellishments. The handle is a curved gold band covered in iridescent pink and orange beads. A large, faceted pink gemstone is set in the center of the bag, surrounded by a cluster of clear rhinestones. The front of the bag features two paisley-shaped designs, each filled with pink rhinestones and outlined in gold and blue beads. Smaller white beads are scattered across the front of the bag.

A transparent sculpture of a duck made out of glass. A raccoon wearing cowboy hat and black leather A bucket bag made of blue suede. The bag is decjacket is behind the backyard window. Rain droplets orated with intricate golden paisley patterns. The handle of the bag is made of rubies and pearls.

Image /page/0/Picture/12 description: The image displays three distinct scenes. The leftmost scene features three blue glass spheres partially submerged in water, with splashes of water around them, set against a backdrop of a setting sun over the ocean. The middle scene shows an old television set overflowing with lush green plants and colorful flowers, with several butterflies fluttering around it. The word "IMAGEN" is prominently displayed in white text on the screen area, which is filled with greenery. The rightmost scene depicts a white coffee cup with a splash of liquid, possibly coffee or tea, with a strawberry submerged in the splash. Two whole strawberries and coffee beans are scattered around the cup, all set against a dark blue background resembling a starry night sky or the Milky Way.

Three spheres made of glass falling into ocean. Water Vines in the shape of text 'Imagen' with flowers and A strawberry splashing in the coffee in a mug under is splashing. Sun is setting. butterflies bursting out of an old TV. the starry sky.

Figure A.1: Select  $1024 \times 1024$  Imagen samples for various text inputs.

Image /page/1/Picture/0 description: The image is a triptych of three distinct scenes. The left panel features two framed portraits of raccoons wearing crowns and regal attire. The middle panel shows three teddy bears dressed in suits and ties, gathered around a table with a pizza adorned with a party hat. The right panel depicts a metallic duck statue interacting with a turtle with a golden shell in a forest setting.

A wall in a royal castle. There are two paintings on A group of teddy bears in suit in a corporate office A chrome-plated duck with a golden beak arguing the wall. The one on the left a detailed oil painting of celebrating the birthday of their friend. There is a with an angry turtle in a forest. the royal raccoon king. The one on the right a detailed pizza cake on the desk. oil painting of the royal raccoon queen.

Image /page/1/Picture/2 description: Three brightly colored, small wooden houses stand in a grassy field under a blue sky with fluffy white clouds. The house on the left is small and brown with a green window. The middle house is larger and painted bright blue with a green-framed window. The house on the right is pink with a brown-framed window. All houses have pointed roofs. The grass is green and dotted with small purple flowers. The overall scene is whimsical and cheerful.

Image /page/1/Picture/3 description: Two white clouds shaped like rabbits are facing each other in a blue sky with scattered white clouds. Between the rabbits, a small white cloud shaped like a ball is suspended in the air, as if they are playing with it.

Image /page/1/Picture/4 description: A Pomeranian dog wearing a golden crown sits on a patterned cushion between two dogs dressed as lions in elaborate blue and gold costumes, also wearing golden crowns. The background is a solid crimson color.

A family of three houses in a meadow. The Dad house A cloud in the shape of two bunnies playing with a A Pomeranian is sitting on the Kings throne wearing is a large blue house. The Mom house is a large pink ball. The ball is made of clouds too. house. The Child house is a small wooden shed.

a crown. Two tiger soldiers are standing next to the throne.

Image /page/1/Picture/7 description: The image is a triptych of three distinct scenes. On the left, a yellow duck wearing red boxing gloves is lifting a barbell in a gym. In the center, a colorful graffiti mural on a brick wall depicts a cartoonish gray hamster with a large black mustache and big eyes. On the right, a cow's head is superimposed onto a person wearing a blue tuxedo with a black bowtie, standing against a backdrop of a bright blue sky with clouds and a beach scene.

An angry duck doing heavy weightlifting at the gym. A dslr picture of colorful graffiti showing a hamster A photo of a person with the head of a cow, wearing with a moustache.

a tuxedo and black bowtie. Beach wallpaper in the background.

Figure A.2: Select  $1024 \times 1024$  Imagen samples for various text inputs.

Image /page/2/Picture/0 description: A garlic bulb with a black bow tie is floating in a bowl of orange liquid. The garlic bulb is positioned on top of a folded newspaper. The garlic bulb has a white and purple striped skin. The newspaper is open and appears to be a foreign language newspaper. The bowl is light blue and the liquid is a vibrant orange color. The image is a close-up shot of the garlic bulb and newspaper in the bowl.

Image /page/2/Picture/1 description: A corgi dog dressed as a wizard, wearing a blue wizard hat and a blue cape, sits on a rock and plays an acoustic guitar. The dog has its tongue sticking out and appears to be enjoying itself. The background is a blurry blue sky with some clouds.

while floating in a pool of tomato soup.

A relaxed garlic with a blindfold reading a newspaper A photo of a corgi dog wearing a wizard hat playing A single beam of light enter the room from the ceiling. guitar on the top of a mountain.

Image /page/2/Picture/4 description: A painting of a raccoon's face is displayed on an easel. The painting is framed and has a green background. The raccoon has dark fur around its eyes and ears, and lighter fur on its snout and cheeks. The easel is made of wood and is positioned in front of a dark, textured wall. A shadow of the easel and painting is cast on the wall to the left.

The beam of light is illuminating an easel. On the easel there is a Rembrandt painting of a raccoon.

Image /page/2/Picture/6 description: A squirrel's face is reflected in a clear glass sphere that is floating on the surface of the ocean. The sky above is blue with fluffy white clouds. The sphere is positioned in the center of the image, and the reflection of the squirrel is clear and detailed. The water is calm, with gentle ripples. The overall image has a surreal and dreamlike quality.

Image /page/2/Picture/7 description: A chocolate-covered dessert shaped like an eagle's head sits on a white plate. The eagle has a white head with a yellow beak and dark brown eyes. White frosting forms the feathers around its neck. The body is covered in dark chocolate, and it is surrounded by diced mango pieces.

A squirrel is inside a giant bright shiny crystal ball in A bald eagle made of chocolate powder, mango, and A marble statue of a Koala DJ in front of a marble on the surface of blue ocean. There are few clouds in whipped cream. the sky.

Image /page/2/Picture/9 description: A statue of a koala wearing headphones and a robe stands behind two turntables, appearing to DJ. The statue is made of white marble and has a dark, glossy nose. The background is dark, with hints of marble pillars on either side of the koala.

statue of a turntable. The Koala has wearing large marble headphones.

Image /page/2/Picture/11 description: The image is a collage of three distinct scenes. The first scene, on the left, depicts the Toronto skyline at night with fireworks exploding in the shape of the Google logo and a brain. The CN Tower is prominently featured, and the city lights are reflected in the water below. The middle scene shows a blue jay perched on a pile of colorful macarons in a woven basket. The third scene, on the right, displays a flooded art gallery with abstract paintings on the walls and small, yellow and purple robots on yellow boats navigating the water.

The Toronto skyline with Google brain logo written A blue jay standing on a large basket of rainbow mac-An art gallery displaying Monet paintings. The art in fireworks. arons.

gallery is flooded. Robots are going around the art gallery using paddle boards.

Figure A.3: Select  $1024 \times 1024$  Imagen samples for various text inputs.

Image /page/3/Figure/0 description: This is a diagram illustrating a text-to-image generation process. On the left, a flowchart shows text input going through a "Frozen Text Encoder" to produce "Text Embedding." This embedding then feeds into a "Text-to-Image Diffusion Model" which generates a "64 x 64 Image." This image is then processed by two sequential "Super-Resolution Diffusion Models," first to a "256 x 256 Image" and finally to a "1024 x 1024 Image." On the right, a text prompt, "A Golden Retriever dog wearing a blue checkered beret and red dotted turtleneck," is shown with three progressively larger images of a golden retriever wearing a blue checkered beret and a red dotted turtleneck, visually representing the output of the diffusion models at different resolutions.

Figure A.4: Visualization of Imagen. Imagen uses a frozen text encoder to encode the input text into text embeddings. A conditional diffusion model maps the text embedding into a  $64 \times 64$  image. Imagen further utilizes text-conditional super-resolution diffusion models to upsample the image, first  $64 \times 64 \rightarrow 256 \times 256$ , and then  $256 \times 256 \rightarrow 1024 \times 1024$ .

# A Background

Diffusion models are latent variable models with latents  $z = \{z_t | t \in [0,1]\}$  that obey a *forward process*  $q(\mathbf{z}|\mathbf{x})$  starting at data  $\mathbf{x} \sim p(\mathbf{x})$ . This forward process is a Gaussian process that satisfies the Markovian structure:

$$
q(\mathbf{z}_t|\mathbf{x}) = \mathcal{N}(\mathbf{z}_t; \alpha_t \mathbf{x}, \sigma_t^2 \mathbf{I}), \quad q(\mathbf{z}_t|\mathbf{z}_s) = \mathcal{N}(\mathbf{z}_t; (\alpha_t/\alpha_s)\mathbf{z}_s, \sigma_{t|s}^2 \mathbf{I})
$$
(3)

where  $0 \le s < t \le 1$ ,  $\sigma_{t|s}^2 = (1 - e^{\lambda_t - \lambda_s})\sigma_t^2$ , and  $\alpha_t, \sigma_t$  specify a differentiable *noise schedule* whose log signal-to-noise-ratio, i.e.,  $\lambda_t = \log[\alpha_t^2/\sigma_t^2]$ , decreases with t until  $q(\mathbf{z}_1) \approx \mathcal{N}(\mathbf{0}, \mathbf{I})$ . For generation, the diffusion model is learned to *reverse* this forward process.

Learning to reverse the forward process can be reduced to learning to denoise  $z_t \sim q(z_t|x)$  into an estimate  $\hat{\mathbf{x}}_{\theta}(\mathbf{z}_t, \lambda_t, \mathbf{c}) \approx \mathbf{x}$  for all t, where c is an optional conditioning signal (such as text embeddings or a low resolution image) drawn from the dataset jointly with x. This is accomplished training  $\hat{\mathbf{x}}_{\theta}$  using a weighted squared error loss

$$
\mathbb{E}_{\boldsymbol{\epsilon},t}\big[w(\lambda_t)\|\hat{\mathbf{x}}_{\theta}(\mathbf{z}_t,\lambda_t,\mathbf{c})-\mathbf{x}\|_2^2\big]
$$
\n(4)

where  $t \sim \mathcal{U}([0, 1])$ ,  $\epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$ , and  $\mathbf{z}_t = \alpha_t \mathbf{x} + \sigma_t \epsilon$ . This reduction of generation to denoising is justified as optimizing a weighted variational lower bound on the data log likelihood under the diffusion model, or as a form of denoising score matching [72, 65, 28, 35]. We use the  $\epsilon$ prediction parameterization, defined as  $\hat{\mathbf{x}}_{\theta}(\mathbf{z}_t, \lambda_t, \mathbf{c}) = (\mathbf{z}_t - \sigma_t \epsilon_{\theta}(\mathbf{z}_t, \lambda_t, \mathbf{c})) / \alpha_t$ , and we impose a squared error loss on  $\epsilon_{\theta}$  in  $\epsilon$  space with t sampled according to a cosine schedule [40]. This corresponds to a particular weighting  $w(\lambda_t)$  and leads to a scaled score estimate  $\epsilon_\theta(z_t, \lambda_t, c) \approx$  $-\sigma_t \nabla_{\mathbf{z}_t} \log p(\mathbf{z}_t|\mathbf{c})$ , where  $p(\mathbf{z}_t|\mathbf{c})$  is the true density of  $\mathbf{z}_t$  given c under the forward process starting at  $\mathbf{x} \sim p(\mathbf{x})$  [28, 35, 66]. Related model designs include the work of [70, 32, 33].

To sample from the diffusion model, we start at  $z_1 \sim \mathcal{N}(0, I)$  and use the discrete time ancestral sampler [28] and DDIM [64] for certain models. DDIM follows the deterministic update rule

$$
\mathbf{z}_s = \alpha_s \hat{\mathbf{x}}_{\theta}(\mathbf{z}_t, \lambda_t, \mathbf{c}) + \frac{\sigma_s}{\sigma_t} (\mathbf{z}_t - \alpha_t \hat{\mathbf{x}}_{\theta}(\mathbf{z}_t, \lambda_t, \mathbf{c}))
$$
(5)

where  $s < t$  follow a uniformly spaced sequence from 1 to 0. The ancestral sampler arises from a reversed description of the forward process; noting that  $q(\mathbf{z}_s|\mathbf{z}_t, \mathbf{x}) = \mathcal{N}(\mathbf{z}_s; \tilde{\boldsymbol{\mu}}_{s|t}(\mathbf{z}_t, \mathbf{x}), \tilde{\sigma}_{s|t}^2 \mathbf{I}),$ where  $\tilde{\boldsymbol{\mu}}_{s|t}(\mathbf{z}_t, \mathbf{x}) = e^{\lambda_t - \lambda_s} (\alpha_s/\alpha_t)\mathbf{z}_t + (1 - e^{\lambda_t - \lambda_s})\alpha_s \mathbf{x}$  and  $\tilde{\sigma}_{s|t}^2 = (1 - e^{\lambda_t - \lambda_s})\sigma_s^2$ , it follows the stochastic update rule

$$
\mathbf{z}_s = \tilde{\boldsymbol{\mu}}_{s|t}(\mathbf{z}_t, \hat{\mathbf{x}}_{\theta}(\mathbf{z}_t, \lambda_t, \mathbf{c})) + \sqrt{(\tilde{\sigma}_{s|t}^2)^{1-\gamma} (\sigma_{t|s}^2)^{\gamma}} \,\boldsymbol{\epsilon}
$$
(6)

where  $\epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$ , and  $\gamma$  controls the stochasticity of the sampler [40].

# B Architecture Details

## B.1 Efficient U-Net

We introduce a new architectural variant, which we term Efficient U-Net, for our super-resolution models. We find our Efficient U-Net to be simpler, converges faster, and is more memory efficient compared to some prior implementations [40], especially for high resolutions. We make several key modifications to the U-Net architecture, such as shifting of model parameters from high resolution blocks to low resolution, scaling the skip connections by  $1/\sqrt{2}$  similar to  $[66, 59]$  and reversing the order of downsampling/upsampling operations in order to improve the speed of the forward pass. Efficient U-Net makes several key modifications to the typical U-Net model used in [16, 58]:

- We shift the model parameters from the high resolution blocks to the low resolution blocks, via adding more residual blocks for the lower resolutions. Since lower resolution blocks typically have many more channels, this allows us to increase the model capacity through more model parameters, without egregious memory and computation costs.
- When using large number of residual blocks at lower-resolution (e.g. we use 8 residual blocks at lower-resolutions compared to typical 2-3 residual blocks used in standard U-Net architectures [16, 59]) we find that scaling the skip connections by  $1/\sqrt{2}$  similar to [66, 59] significantly improves convergence speed.

• In a typical U-Net's downsampling block, the downsampling operation happens after the convolutions, and in an upsampling block, the upsampling operation happens prior the convolution. We reverse this order for both downsampling and upsampling blocks in order to significantly improve the speed of the forward pass of the U-Net, and find no performance degradation.

With these key simple modifications, Efficient U-Net is simpler, converges faster, and is more memory efficient compared to some prior U-Net implementations. Fig. [A.30](#page-27-0) shows the full architecture of Efficient U-Net, while Figures [A.28](#page-25-0) and [A.29](#page-26-0) show detailed description of the Downsampling and Upsampling blocks of Efficient U-Net respectively. See Appendix [D.3.2](#page-10-0) for results.

# C DrawBench

In this section, we describe our new benchmark for fine-grained analysis of text-to-image models, namely, DrawBench. DrawBench consists of 11 categories with approximately 200 text prompts. This is large enough to test the model well, while small enough to easily perform trials with human raters. Table [A.1](#page-6-0) enumerates these categories along with description and few examples. We release the full set of samples [here.](https://docs.google.com/spreadsheets/d/1y7nAbmR4FREi6npB1u-Bo3GFdwdOPYJc617rBOxIRHY/edit#gid=0)

For evaluation on this benchmark, we conduct an independent human evaluation run for each category. For each prompt, the rater is shown two sets of images - one from Model A, and second from Model B. Each set contains 8 random (non-cherry picked) generations from the corresponding model. The rater is asked two questions -

- 1. Which set of images is of higher quality?
- 2. Which set of images better represents the text caption : {Text Caption}?

where the questions are designed to measure: 1) image fidelity, and 2) image-text alignment. For each question, the rater is asked to select from three choices:

- 1. I prefer set A.
- 2. I am indifferent.
- 3. I prefer set B.

We aggregate scores from 25 raters for each category (totalling to  $25 \times 11 = 275$  raters). We do not perform any post filtering of the data to identify unreliable raters, both for expedience and because the task was straightforward to explain and execute.

# D Imagen Detailed Abalations and Analysis

In this section, we perform ablations and provide a detailed analysis of Imagen.

## D.1 Pre-trained Text Encoders

We explore several families of pre-trained text encoders: BERT [15], T5 [52], and CLIP [49]. There are several key differences between these encoders. BERT is trained on a smaller text-only corpus (approximately 20 GB, Wikipedia and BooksCorpus [84]) with a masking objective, and has relatively small model variants (upto 340M parameters). T5 is trained on a much larger C4 text-only corpus (approximately 800 GB) with a denoising objective, and has larger model variants (up to  $11B$  parameters). The CLIP model<sup>[5](#page-5-0)</sup> is trained on an image-text corpus with an image-text contrastive objective. For T5 we use the encoder part for the contextual embeddings. For CLIP, we use the penultimate layer of the text encoder to get contextual embeddings. Note that we freeze the weights of these text encoders (i.e., we use off the shelf text encoders, without any fine-tuning on the text-to-image generation task). We explore a variety of model sizes for these text encoders.

We train a  $64 \times 64$ , 300M parameter diffusion model, conditioned on the text embeddings generated from BERT (base, and large), T5 (small, base, large, XL, and XXL), and CLIP (ViT-L/14). We observe that scaling the size of the language model text encoders generally results in better image-text

<span id="page-5-0"></span><sup>5</sup> <https://github.com/openai/CLIP/blob/main/model-card.md>

<span id="page-6-0"></span>

| Category           | <b>Description</b>                                                         | <b>Examples</b>                                                                                                              |
|--------------------|----------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|
| Colors             | Ability to generate objects<br>with specified colors.                      | "A blue colored dog."<br>"A black apple and a green backpack."                                                               |
| Counting           | Ability to generate specified<br>number of objects.                        | "Three cats and one dog sitting on the grass."<br>"Five cars on the street."                                                 |
| Conflicting        | Ability to generate conflicting<br>interactions b/w objects.               | "A horse riding an astronaut."<br>"A panda making latte art."                                                                |
| DALL-E [53]        | Subset of challenging prompts<br>from [53].                                | "A triangular purple flower pot."<br>"A cross-section view of a brain."                                                      |
| Description        | Ability to understand complex and long<br>text prompts describing objects. | "A small vessel propelled on water by oars, sails, or an engine."<br>"A mechanical or electrical device for measuring time." |
| Marcus et al. [38] | Set of challenging prompts<br>from [38].                                   | "A pear cut into seven pieces arranged in a ring."<br>"Paying for a quarter-sized pizza with a pizza-sized quarter."         |
| Misspellings       | Ability to understand<br>misspelled prompts.                               | "Rbefraigerator."<br>"Tcennis rpacket."                                                                                      |
| Positional         | Ability to generate objects with<br>specified spatial positioning.         | "A car on the left of a bus."<br>"A stop sign on the right of a refrigerator."                                               |
| Rare Words         | Ability to understand rare words3.                                         | "Artophagous."<br>"Octothorpe."                                                                                              |
| Reddit             | Set of challenging prompts from<br>DALLE-2 Reddit4.                        | "A yellow and black bus cruising through the rainforest."<br>"A medieval painting of the wifi not working."                  |
| Text               | Ability to generate quoted text.                                           | "A storefront with 'Deep Learning' written on it."<br>"A sign that says 'Text to Image'."                                    |

Table A.1: Description and examples of the 11 categories in DrawBench.

alignment as captured by the CLIP score as a function of number of training steps (see Fig. [A.6\)](#page-7-0). One can see that the best CLIP scores are obtained with the T5-XXL text encoder.

Since guidance weights are used to control image quality and text alignment, we also report ablation results using curves that show the trade-off between CLIP and FID scores as a function of the guidance weights (see Fig. [A.5a\)](#page-7-1). We observe that larger variants of T5 encoder results in both better image-text alignment, and image fidelity. This emphasizes the effectiveness of large frozen text encoders for text-to-image models. Interestingly, we also observe that the T5-XXL encoder is on-par with the CLIP encoder when measured with CLIP and FID-10K on MS-COCO.

T5-XXL vs CLIP on DrawBench: We further compare T5-XXL and CLIP on DrawBench to perform a more comprehensive comparison of the abilities of these two text encoders. In our initial evaluations we observed that the 300M parameter models significantly underperformed on DrawBench. We believe this is primarily because DrawBench prompts are considerably more difficult than MS-COCO prompts.

In order to perform a meaningful comparison, we train  $64\times64$  1B parameter diffusion models with T5-XXL and CLIP text encoders for this evaluation. Fig. [A.5b](#page-7-1) shows the results. We find that raters are considerably more likely to prefer the generations from the model trained with the T5-XXL encoder over the CLIP text encoder, especially for image-text alignment. This indicates that language models are better than text encoders trained on image-text contrastive objectives in encoding complex and compositional text prompts. Fig. [A.7](#page-8-0) shows the category specific comparison between the two models. We observe that human raters prefer T5-XXL samples over CLIP samples in all 11 categories for image-text alignment demonstrating the effectiveness of large language models as text encoders for text to image generation.

## D.2 Classifier-free Guidance and the Alignment-Fidelity Trade-off

We observe that classifier-free guidance [27] is a key contributor to generating samples with strong image-text alignment, this is also consistent with the observations of [53, 54]. There is typically a trade-off between image fidelity and image-text alignment, as we iterate over the guidance weight. While previous work has typically used relatively small guidance weights, Imagen uses relatively large guidance weights for all three diffusion models. We found this to yield a good balance of sample quality and alignment. However, naive use of large guidance weights often produces relatively poor

<span id="page-7-1"></span>Image /page/7/Figure/0 description: The image contains two plots. The left plot is a line graph showing the relationship between CLIP Score on the x-axis and FID-10K on the y-axis. There are five lines representing different models: T5-Small (yellow), T-Large (green), T5-XL (red), T5-XXL (blue), and CLIP (black dotted). The y-axis ranges from 10 to 25, and the x-axis ranges from 0.22 to 0.28. The right plot is a bar chart comparing two metrics, Alignment and Fidelity, for T5-XXL and CLIP. The y-axis represents percentage from 0% to 100%. For Alignment, T5-XXL has a value around 60% and CLIP has a value around 45%. For Fidelity, T5-XXL has a value around 53% and CLIP has a value around 50%. Both bars have error bars.

(a) Pareto curves comparing various text encoders. (b) Comparing T5-XXL and CLIP on DrawBench.

<span id="page-7-0"></span>Figure A.5: Comparison between text encoders for text-to-image generation. For Fig. [A.5a,](#page-7-1) we sweep over guidance values of [1, 1.25, 1.5, 1.75, 2, 3, 4, 5, 6, 7, 8, 9, 10]

Image /page/7/Figure/3 description: This is a line graph showing the CLIP score on the y-axis against training steps on the x-axis. The x-axis ranges from 0 to 5, multiplied by 10^5. The y-axis ranges from 0.22 to 0.28. Several lines represent different models: BERT Base (dashed blue), BERT Large (dashed red), T5 Small (solid yellow), T5 Base (solid blue), T5 Large (solid red), T5 XL (solid green), and T5 XXL (solid gray). A dotted black line labeled 'CLIP' and a dashed-dotted black line labeled 'Reference' are also present. The graph illustrates the performance of these models over training steps, with most models showing an increase in CLIP score as training progresses. The T5 XXL and CLIP models achieve the highest scores, surpassing the reference line.

Figure A.6: Training convergence comparison between text encoders for text-to-image generation.

results. To enable the effective use of larger guidance we introduce several innovations, as described below.

Thresholding Techniques: First, we compare various thresholding methods used with classifier-free guidance. Fig. [A.8](#page-9-0) compares the CLIP vs. FID-10K score pareto frontiers for various thresholding methods of the base text-to-image  $64 \times 64$  model. We observe that our dynamic thresholding technique results in significantly better CLIP scores, and comparable or better FID scores than the static thresholding technique for a wide range of guidance weights. Fig. [A.9](#page-9-1) shows qualitative samples for thresholding techniques.

Guidance for Super-Resolution: We further analyze the impact of classifier-free guidance for our  $64 \times 64 \rightarrow 256 \times 256$  model. Fig. [A.11a](#page-10-1) shows the pareto frontiers for CLIP vs. FID-10K score for the  $64 \times 64 \rightarrow 256 \times 256$  super-resolution model. aug\_level specifies the level of noise augmentation applied to the input low-resolution image during inference (aug\_level  $= 0$  means no noise). We observe that aug  $\text{ level} = 0$  gives the best FID score for all values of guidance weight. Furthermore, for all values of aug\_level, we observe that FID improves considerably with increasing guidance weight upto around  $7 - 10$ . While generation using larger values of aug\_level gives slightly worse FID, it allows more varied range of CLIP scores, suggesting more diverse generations by the superresolution model. In practice, for our best samples, we generally use aug\_level in  $[0.1, 0.3]$ . Using large values of aug level and high guidance weights for the super-resolution models, Imagen can create different variations of a given  $64 \times 64$  image by altering the prompts to the super-resolution models (See Fig. [A.12](#page-11-0) for examples).

Impact of Conditioning Augmentation: Fig. [A.11b](#page-10-1) shows the impact of training super-resolution models with noise conditioning augmentation. Training with no noise augmentation generally results in worse CLIP and FID scores, suggesting noise conditioning augmentation is critical to attaining best sample quality similar to prior work [29]. Interestingly, the model trained without noise augmentation has much less variations in CLIP and FID scores across different guidance weights compared to

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The image contains two stacked bar charts. The top chart, labeled "(a) Alignment", shows the performance of T5-XXL and CLIP across different categories. The bottom chart, labeled "(b) Fidelity", also shows the performance of T5-XXL and CLIP across different categories. Both charts have a y-axis ranging from 0% to 100%. The categories on the x-axis for the bottom chart are "Color", "Conflicting", "Count", "DALL-E [53]", "Descriptions", "Marcus et al. [38]", "Misspellings", "Positional", "Rarewords", "Reddit", and "Text". The bars are stacked, with T5-XXL represented by blue and CLIP by green. In the top chart, for "Alignment", the T5-XXL bars are generally higher than the CLIP bars for most categories, with CLIP performing better in the last few categories. In the bottom chart, for "Fidelity", the T5-XXL bars are consistently around 50% or lower, while the CLIP bars are consistently around 50% or higher, indicating CLIP generally performs better in "Fidelity" across all categories.

Figure A.7: T5-XXL vs. CLIP text encoder on DrawBench [a\)](#page-8-0) image-text alignment, and [b\)](#page-8-0) image fidelity.

<span id="page-9-0"></span>Image /page/9/Figure/0 description: This is a line graph comparing static thresholding and dynamic thresholding. The x-axis is labeled "CLIP Score" and ranges from 0.26 to 0.29. The y-axis is labeled "FID@10K" and ranges from 10 to 25. The graph shows two lines: a blue line representing "static thresholding" and a red line representing "dynamic thresholding". Both lines start at approximately 10 on the y-axis when the CLIP Score is around 0.255. As the CLIP Score increases, the FID@10K also increases for both methods. The dynamic thresholding line generally shows a higher FID@10K for the same CLIP Score compared to static thresholding, especially at higher CLIP Scores. For example, at a CLIP Score of 0.29, static thresholding has an FID@10K of approximately 18, while dynamic thresholding has an FID@10K of approximately 25.

Figure A.8: CLIP Score vs FID trade-off across various  $\hat{x}_0$  thresholding methods for the 64×64 model. We sweep over guidance values of  $[1, 1.25, 1.5, 1.75, 2, 3, 4, 5, 6, 7, 8, 9, 10]$ .

<span id="page-9-1"></span>Image /page/9/Picture/2 description: The image displays a grid of 30 images, each featuring an astronaut riding a horse. The images are arranged in three columns and ten rows. The astronauts are depicted in various poses and settings, some on brown horses and others on white horses. The backgrounds vary, including dark, starry, desert, mountainous, and cloudy skies. Some images are in color, while others are in black and white. The overall theme is the surreal combination of space exploration and equestrianism.

(b) Static thresholding

(a) No thresholding. (b) Static thresholding. (c) Dynamic thresholding.

Figure A.9: Thresholding techniques on  $256 \times 256$  samples for "A photo of an astronaut riding a horse." Guidance weights increase from 1 to 5 as we go from top to bottom. No thresholding results in poor images with high guidance weights. Static thresholding is an improvement but still leads to oversaturated samples. Our dynamic thresholding leads to the highest quality images. See Fig. [A.10](#page-10-2) for more qualitative comparison.

the model trained with conditioning augmentation. We hypothesize that this is primarily because strong noise augmented training reduces the low-resolution image conditioning signal considerably, encouraging higher degree of dependence on conditioned text for the model.

## D.3 Impact of Model Size

Fig. [A.13b](#page-12-0) plots the CLIP-FID score trade-off curves for various model sizes of the  $64 \times 64$  text-toimage U-Net model. We train each of the models with a batch size of 2048, and 400K training steps. As we scale from 300M parameters to 2B parameters for the U-Net model, we obtain better trade-off curves with increasing model capacity. Interestingly, scaling the frozen text encoder model size yields more improvement in model quality over scaling the U-Net model size. Scaling with a frozen text encoder is also easier since the text embeddings can be computed and stored offline during training.

### D.3.1 Impact of Text Conditioning Schemas

We ablate various schemas for conditioning the frozen text embeddings in the base  $64 \times 64$  textto-image diffusion model. Fig. [A.13a](#page-12-0) compares the CLIP-FID pareto curves for mean pooling, attention pooling, and cross attention. We find using any pooled embedding configuration (mean or attention pooling) performs noticeably worse compared to attending over the sequence of contextual embeddings in the attention layers. We implement the cross attention by concatenating the text

<span id="page-10-2"></span>Image /page/10/Picture/0 description: The image displays a grid of 18 images, each featuring an astronaut riding a horse. The astronauts are depicted in their white spacesuits with helmets and backpacks. The horses vary in color, including white, black, and brown. The backgrounds are diverse, ranging from dark, starry skies and moonscapes to grassy fields, deserts, and beaches. Some images are realistic photographs, while others appear to be digital illustrations. The grid is divided into two main sections, labeled (a) and (b) at the bottom, suggesting a comparison or a collection of different styles or variations of the same theme.

(a) Samples using static thresholding. (b) Samples using dynamic thresholding ( $p = 99.5$ )

Figure A.10: Static vs. dynamic thresholding on non-cherry picked  $256 \times 256$  samples using a guidance weight of 5 for both the base model and the super-resolution model, using the same random seed. The text prompt used for these samples is "A photo of an astronaut riding a horse." When using high guidance weights, static thresholding often leads to oversaturated samples, while our dynamic thresholding yields more natural looking images.

<span id="page-10-1"></span>Image /page/10/Figure/4 description: Two line graphs are displayed side-by-side, both plotting FID-10K on the y-axis against CLIP Score on the x-axis. The left graph shows three lines representing different augmentation levels: 'aug\_level = 0.0' (blue), 'aug\_level = 0.1' (red), and 'aug\_level = 0.2' (green). The x-axis ranges from approximately 0.288 to 0.302, and the y-axis ranges from 10 to 13. The blue line shows a U-shaped trend, decreasing and then increasing. The red and green lines also show a general downward trend followed by an upward trend, with the green line consistently above the red line. The right graph shows four lines, labeled 'A' (blue), 'B (aug\_level = 0.0)' (red), 'B (aug\_level = 0.1)' (green), and 'B (aug\_level = 0.2)' (yellow). The x-axis ranges from approximately 0.286 to 0.297, and the y-axis ranges from 10 to 13. The blue line shows a downward trend with some fluctuations. The red, green, and yellow lines generally show a downward trend, with the yellow line starting highest and decreasing, then increasing. The legend for the right graph is placed within the plot area.

(a) Comparison between different values of aug\_level. (b) Comparison between training with no noise augmentation "A" vs noise augmentation "B"

Figure A.11: CLIP vs FID-10K pareto curves showing the impact of noise augmentation on our 64  $\times$  $64 \rightarrow 256 \times 256$  model. For each study, we sweep over guidance values of [1, 3, 5, 7, 8, 10, 12, 15, 18]

embedding sequence to the key-value pairs of each self-attention layer in the base  $64 \times 64$  and  $64 \times 64 \rightarrow 256 \times 256$  models. For our  $256 \times 256 \rightarrow 1024 \times 1024$  model, since we have no selfattention layers, we simply added explicit cross-attention layers to attend over the text embeddings. We found this to improve both fidelity and image-text alignment with minimal computational costs.

<span id="page-10-0"></span>

### D.3.2 Comparison of U-Net vs Efficient U-Net

We compare the performance of U-Net with our new Efficient U-Net on the task of  $64 \times 64 \rightarrow 256 \times$ 256 super-resolution task. Fig. [A.14](#page-12-1) compares the training convergence of the two architectures. We observe that Efficient U-Net converges significantly faster than U-Net, and obtains better performance overall. Our Efficient U-Net is also  $\times 2 - 3$  faster at sampling.

# E Comparison to GLIDE and DALL-E 2

Fig. [A.15](#page-13-0) shows category wise comparison between Imagen and DALL-E 2 [54] on DrawBench. We observe that human raters clearly prefer Imagen over DALL-E 2 in 7 out of 11 categories for text alignment. For sample fidelity, they prefer Imagen over DALL-E 2 in all 11 categories. Figures [A.17](#page-15-0)

<span id="page-11-0"></span>Image /page/11/Picture/0 description: The image displays a grid of four images, each row showing a different scene. The first column is labeled "Input" and shows low-resolution versions of the scenes. The second column is labeled "Unmodified" and shows the same scenes with improved resolution. The third column is labeled "Oil Painting" and shows the scenes rendered in an oil painting style. The fourth column is labeled "Illustration" and shows the scenes rendered in an illustrative style. The top row features an astronaut riding a horse. The middle row shows a Shiba Inu dog wearing sunglasses and a blue bandana, riding a unicycle. The bottom row depicts several teddy bears gathered around a pizza.

Figure A.12: Super-resolution variations for some  $64 \times 64$  generated images. We first generate the 64 $\times$ 64 image using "A photo of ... .". Given generated 64  $\times$  64 images, we condition both the super-resolution models on different prompts in order to generate different upsampled variations. e.g. for oil painting we condition the super-resolution models on the prompt "An oil painting of ... .". Through a combination of large guidance weights and aug\_level  $= 0.3$  for both super-res models we can generate different styles based on the style query through text.

to [A.21](#page-19-0) show few qualitative comparisons between Imagen and DALL-E 2 samples used for this human evaluation study. Some of the categories where Imagen has a considerably larger preference over DALL-E 2 include Colors, Positional, Text, DALL-E and Descriptions. The authors in [54] identify some of these limitations of DALL-E 2, specifically they observe that DALLE-E 2 is worse than GLIDE [41] in binding attributes to objects such as colors, and producing coherent text from the input prompt (cf. the discussion of limitations in [54]). To this end, we also perform quantitative and qualitative comparison with GLIDE [41] on DrawBench. See Fig. [A.16](#page-14-0) for category wise human evaluation comparison between Imagen and GLIDE. See Figures [A.22](#page-20-0) to [A.26](#page-24-0) for qualitative comparisons. Imagen outperforms GLIDE on 8 out of 11 categories on image-text alignment, and 10 out of 11 categories on image fidelity. We observe that GLIDE is considerably better than DALL-E 2 in binding attributes to objects corroborating the observation by [54].

<span id="page-12-0"></span>Image /page/12/Figure/0 description: The image contains two plots side-by-side. Both plots have 'CLIP Score' on the x-axis and 'FID-10K' on the y-axis. Plot (a) is titled "Comparison between different text encoders." It shows three lines representing 'Mean Pooling' (blue), 'Attention Pooling' (red), and 'Cross Attention' (yellow). The x-axis ranges from 0.23 to 0.29, and the y-axis ranges from 10 to 25. Plot (b) is titled "Comparison between different model sizes." It shows four lines representing '300M' (blue), '500M' (red), '1B' (green), and '2B' (yellow). The x-axis ranges from 0.24 to 0.29, and the y-axis ranges from 10 to 25. Both plots illustrate a trade-off between CLIP Score and FID-10K, with performance generally improving as the CLIP Score increases, but at the cost of higher FID-10K values.

Figure A.13: CLIP vs FID-10K pareto curves for different ablation studies for the base  $64 \times 64$ model. For each study, we sweep over guidance values of [1, 1.25, 1.5, 1.75, 2, 3, 4, 5, 6, 7, 8, 9, 10]

<span id="page-12-1"></span>Image /page/12/Figure/2 description: The image is a line graph showing the FID-2K score on the y-axis against TPU Training Days on the x-axis. There are two lines: a blue line representing U-Net and a red line representing Efficient U-Net. The U-Net line starts at approximately 40.5 at 0.5 training days, drops to 27.5 at 1 training day, 25 at 1.5 training days, 23.5 at 2 training days, 22 at 2.5 training days, 21.5 at 3 training days, and 21 at 4 training days. The Efficient U-Net line starts at approximately 27.5 at 0.5 training days, drops to 23 at 1 training day, 21 at 1.5 training days, and 20.5 at 2 training days. The graph indicates that both models' FID-2K scores decrease with more training days, with U-Net generally having a higher FID-2K score than Efficient U-Net.

Figure A.14: Comparison of convergence speed of U-Net vs Efficient U-Net on the  $64 \times 64 \rightarrow$  $256 \times 256$  super-resolution task.

<span id="page-13-0"></span>Image /page/13/Figure/0 description: The image contains two bar charts. The top chart, labeled "(a) Alignment", shows the performance of two models, Imagen and DALL-E 2, across several categories. The bottom chart, labeled "(b) Fidelity", also compares Imagen and DALL-E 2 across different categories. Both charts have a y-axis ranging from 0% to 100%. The categories on the x-axis for the bottom chart are Color, Conflicting, Count, DALL-E [53], Descriptions, Marcus et al. [38], Misspellings, Positional, Rarewords, Reddit, and Text. The bars in both charts are stacked, with a blue section representing one model and a green section representing the other. The top chart shows that for most categories, both models perform similarly, with Imagen generally slightly outperforming DALL-E 2 in the top portion of the bars. The bottom chart shows varying performance across categories, with Imagen consistently performing better in the top portion of the bars for most categories, except for 'Count' where DALL-E 2 appears to perform better.

Imagen DALL-E 2

Figure A.15: Imagen vs DALL-E 2 on DrawBench [a\)](#page-13-0) image-text alignment, and [b\)](#page-13-0) image fidelity.

<span id="page-14-0"></span>Image /page/14/Figure/0 description: The image contains two stacked bar charts comparing the performance of Imagen and GLIDE on different metrics. The top chart, labeled "(a) Alignment", shows the alignment scores for various categories. The bottom chart, labeled "(b) Fidelity", displays fidelity scores for different text prompts. Both charts have a y-axis ranging from 0% to 100% and use blue for Imagen and green for GLIDE. The categories on the x-axis for alignment are not explicitly labeled but appear to be consistent across the bars. The categories for fidelity are "Color", "Conflicting", "Count", "DALL-E [53]", "Descriptions", "Marcus et al. [38]", "Misspellings", "Positional", "Rarewords", "Reddit", and "Text".

Figure A.16: Imagen vs GLIDE on DrawBench [a\)](#page-14-0) image-text alignment, and [b\)](#page-14-0) image fidelity.

<span id="page-15-0"></span>Image /page/15/Figure/0 description: The image displays a comparison between two AI image generation models, labeled "Imagen (Ours)" and "DALL-E 2 [54]". Each model's results are presented in a 2x2 grid. The theme across all images is cows being abducted by UFOs. The "Imagen (Ours)" section shows more realistic, 3D-rendered cows and UFOs against starry or grassy backgrounds. The "DALL-E 2 [54]" section features more stylized, cartoon-like illustrations of cows and UFOs, with vibrant, often purple or orange skies.

Hovering cow abducting aliens.

Image /page/15/Picture/2 description: The image displays a collage of eight sculptures, each featuring a human figure interacting with a cat. The sculptures vary in material, style, and the nature of the interaction. Some figures are made of bronze, while others appear to be marble or stone. The poses range from dynamic and athletic to more serene and contemplative. The cats are depicted in various positions, some sitting, some walking, and one even perched on a human figure's head. The overall theme is the relationship between humans and felines, as interpreted through classical and modern sculptural art.

Greek statue of a man tripping over a cat.

Figure A.17: Example qualitative comparisons between Imagen and DALL-E 2 [54] on DrawBench prompts from Reddit category.

Image /page/16/Picture/0 description: The image displays a comparison between two image generation models, "Imagen (Ours)" and "DALL-E 2 [54]". Each model's results are presented in a 2x2 grid. The "Imagen (Ours)" section shows four images: the top left features a red vase on a white book against a yellow background; the top right shows a red vase with yellow flowers on a yellow book against a grey background; the bottom left displays a red vase with green leaves on an open book against a yellow background; and the bottom right shows a red vase on a yellow book against a grey background. The "DALL-E 2 [54]" section also shows four images: the top left shows a red book with a yellow vase in the background; the top right shows a yellow vase next to a red book; the bottom left shows a red book next to a yellow vase; and the bottom right shows a red book with a yellow vase in the background.

A yellow book and a red vase.

Image /page/16/Picture/2 description: The image is a collage of eight smaller images, each featuring a backpack and an apple. The backpacks are either green or black, and the apples are either red, black, or green. The first image shows a black apple in front of a green backpack. The second image shows a red apple with straps in front of a green backpack. The third image shows a green apple next to a black backpack. The fourth image shows a green apple next to a black backpack. The fifth image shows a green backpack with a black apple in front of it. The sixth image shows a green backpack. The seventh image shows a green apple next to a black backpack. The eighth image shows a green apple next to a black backpack.

A black apple and a green backpack.

Figure A.18: Example qualitative comparisons between Imagen and DALL-E 2 [54] on DrawBench prompts from Colors category. We observe that DALL-E 2 generally struggles with correctly assigning the colors to the objects especially for prompts with more than one object.

Image /page/17/Figure/0 description: The image displays a grid of four illustrations, each featuring an astronaut riding a horse in a space-themed setting. The top row shows two illustrations labeled "Imagen (Ours)" on the left and "DALL-E 2 [54]" on the right. The left illustration depicts an astronaut on a white horse with a yellow mane, against a blue background with stars and a pink planet. The right illustration shows an astronaut on a light brown horse, with a dark background and stars. The bottom row contains two more illustrations. The bottom left image shows an astronaut on a black horse against a teal background with stars and a yellow planet. The bottom right image shows an astronaut on a grey horse against a black background with stars.

A horse riding an astronaut.

Image /page/17/Picture/2 description: The image displays a collage of eight different images, all featuring pandas and coffee. The top row shows three images of latte art with panda faces. The first image is a cartoon of a panda pouring coffee into a cup. The second and third images are close-ups of a white mug with a panda face made from latte art. The bottom row also features pandas and coffee. The first image is a cartoon of a panda holding a milk jug and a cup of coffee with a flower design. The second image is a cartoon of a panda drinking coffee. The third and fourth images are close-ups of latte art in white mugs, with the third showing a panda face being created with dark syrup, and the fourth showing a finished panda face latte art.

A panda making latte art.

Figure A.19: Example qualitative comparisons between Imagen and DALL-E 2 [54] on DrawBench prompts from Conflicting category. We observe that both DALL-E 2 and Imagen struggle generating well aligned images for this category. However, Imagen often generates some well aligned samples, e.g. "A panda making latte art.".

Image /page/18/Picture/0 description: The image is a comparison of images generated by two different AI models, labeled "Imagen (Ours)" and "DALL-E 2 [54]". The top row shows two glasses with liquid in them under the "Imagen (Ours)" label, and two empty glasses under the "DALL-E 2 [54]" label. The bottom row shows two blue-stemmed glasses with frothy liquid under the "Imagen (Ours)" label, and two clear shot glasses with water under the "Imagen (Ours)" label. The "DALL-E 2 [54]" label also has a bottom row showing a pair of glasses on a dark surface and a close-up of a wine glass stem and base.

A couple of glasses are sitting on a table.

Image /page/18/Figure/2 description: The image displays a grid of eight smaller images, each featuring a different arrangement or type of brick. The top row shows three digital renderings of brick cubes, with varying patterns of brick placement and mortar. The fourth image in the top row is a photograph of a single, smooth, orange-red brick standing upright. The bottom row begins with a photograph of a square brick structure with an open top, resembling a small chimney or planter. The next image is another digital rendering of a brick cube, this time with a more complex, layered brick pattern. The seventh image is a photograph of three stacked, rough-textured, red bricks. The final image in the bottom row shows two stacked blocks, the top one a smooth orange-red, and the bottom one a rough, golden-yellow material.

A cube made of brick. A cube with the texture of brick.

Figure A.20: Example qualitative comparisons between Imagen and DALL-E 2 [54] on DrawBench prompts from DALL-E category.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image displays a comparison between two AI image generation models, "Imagen (Ours)" and "DALL-E 2 [54]". Each model's output is presented in a 2x2 grid, showcasing generated images of the New York City skyline at night with fireworks. The text "Hello World" is incorporated into the fireworks in various styles and colors across the different images. The top row shows "Hello World" in neon-like lettering, with the left image featuring orange and yellow text and the right image displaying white text. The bottom row also features "Hello World" in neon-like lettering, with the left image showing purple text and the right image displaying white text. The DALL-E 2 outputs in the top right and bottom right also show "Hello World" in white lettering against the cityscape and fireworks.

New York Skyline with Hello World written with fireworks on the sky.

Image /page/19/Figure/2 description: The image displays a grid of eight storefronts, each featuring signage. The top row shows three storefronts. The first has a white sign above a window that reads "Text to Image." The second has a red, curved sign that says "Text to Image" with posters on the wall behind it. The third has a large, gray sign with white lettering that reads "TAG OF" and smaller text below. The second row also shows three storefronts. The first has a bright orange sign that reads "TEXT-TO-IMAGE." The second has a silver sign that reads "TEXT TO IMAGE" above a window with "IMAGE" written on it. The third has a yellow sign with black lettering that reads "PTGE IN O." The last storefront in the bottom right corner has a sign that reads "Time Thag" in black lettering on a beige background.

A storefront with Text to Image written on it.

Figure A.21: Example qualitative comparisons between Imagen and DALL-E 2 [54] on DrawBench prompts from Text category. Imagen is significantly better than DALL-E 2 in prompts with quoted text.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image displays a comparison of AI-generated images of cows being abducted by UFOs. The left side, labeled "Imagen (Ours)", shows two realistic 3D renderings of brown and white cows being lifted into glowing blue UFOs against a backdrop of a starry sky and green hills. The right side, labeled "GLIDE [41]", presents two cartoon-style illustrations. The top right shows a green and red UFO with a yellow dome, beaming a black and white cow upwards against a starry blue background. The bottom right depicts a similar UFO, this time with a red dome, beaming a black and white cow from a starry night sky.

Hovering cow abducting aliens.

Image /page/20/Picture/2 description: The image displays a grid of eight sculptures, each featuring a human figure interacting with one or more cats. The sculptures vary in material, with some appearing to be bronze and others marble. The poses of the figures and cats are dynamic, suggesting movement and interaction. The backgrounds range from plain dark or blue to outdoor settings with trees and architecture.

Greek statue of a man tripping over a cat.

Figure A.22: Example qualitative comparisons between Imagen and GLIDE [41] on DrawBench prompts from Reddit category.

Image /page/21/Picture/0 description: The image displays a grid of four images on the left, labeled "Imagen (Ours)", and four images on the right, labeled "GLIDE [41]". All images feature a red vase and a yellow book. The top left image shows a red vase on a closed book against a yellow background. The top right image shows a red vase on a closed yellow book against a white background, with a stylized red vase and yellow book in the background. The bottom left image shows an open book with a red vase and green leaves in the background, against a yellow background. The bottom right image shows a red vase on a closed yellow book against a white background.

A yellow book and a red vase.

Image /page/21/Picture/2 description: The image is a collage of eight pictures, each featuring a green backpack and a dark-colored apple. The apples are mostly black or very dark red, and they are placed in various positions relative to the backpacks. Some backpacks are shown from the front, others from the side, and some are partially visible. The backgrounds vary, with some images having a light-colored or white background, while others have a green or wooden surface. The overall theme is the pairing of green backpacks with dark apples.

A black apple and a green backpack.

Figure A.23: Example qualitative comparisons between Imagen and GLIDE [41] on DrawBench prompts from Colors category. We observe that GLIDE is better than DALL-E 2 in assigning the colors to the objects.

Image /page/22/Figure/0 description: The image displays a grid of four illustrations, comparing two different image generation models, labeled "Imagen (Ours)" and "GLIDE [41]". Each row shows a comparison of the same concept generated by both models. The top row features an astronaut riding a white horse against a starry blue background with a pinkish ground. The bottom left illustration shows an astronaut riding a black horse in a similar starry blue environment. The bottom right illustration depicts an astronaut riding a tan horse on a moon-like surface with stars in the background.

A horse riding an astronaut.

Image /page/22/Picture/2 description: The image is a collage of six pictures, all featuring pandas and coffee. The top row shows a cartoon panda pouring coffee into a cup, a close-up of a latte with panda face art, and two images of a barista pouring milk into a cup to create latte art of a panda face. The bottom row shows a cartoon panda holding a coffee cup and a milk pitcher, another cartoon panda drinking coffee with a straw, and a close-up of a latte with panda face art and some wheat stalks.

A panda making latte art.

Figure A.24: Example qualitative comparisons between Imagen and GLIDE [41] on DrawBench prompts from Conflicting category.

Image /page/23/Figure/0 description: The image displays a comparison between two methods, "Imagen (Ours)" and "GLIDE [41]". The top row shows two wine glasses, one with a clear liquid and the other with a yellow liquid, under "Imagen (Ours)". To the right, under "GLIDE [41]", are two pairs of eyeglasses, one pair in focus and the other slightly out of focus. The bottom row under "Imagen (Ours)" shows two blue-stemmed wine glasses, one with a white frothy liquid and the other with a striped pattern, and a shot glass filled with water. Under "GLIDE [41]" in the bottom row, there is a close-up of a pair of eyeglasses and another pair of eyeglasses on a table.

A couple of glasses are sitting on a table.

Image /page/23/Figure/2 description: The image displays a collage of six different brick textures and patterns, presented in a 2x3 grid. The top row features two digitally rendered cubes showcasing brick patterns, one with a standard stretcher bond and the other with a more complex arrangement of bricks. The third image in the top row is a close-up of a single brick with a honeycomb-like texture on one side. The bottom row begins with a photograph of a brick structure, possibly a chimney base, with visible mortar. The next image is another digitally rendered cube with a detailed brick pattern. The final image in the bottom row shows two more bricks, one with two circular holes and the other with several small indentations on its side.

A cube made of brick. A cube with the texture of brick.

Figure A.25: Example qualitative comparisons between Imagen and GLIDE [41] on DrawBench prompts from DALL-E category.

<span id="page-24-0"></span>Image /page/24/Picture/0 description: The image displays a grid of four images, each featuring a cityscape at night with text superimposed. The top row is labeled "Imagen (Ours)" and the bottom row is labeled "GLIDE [41]". The left column shows fireworks spelling out "Hello World" in yellow and pink neon-like text over the New York City skyline, including the Brooklyn Bridge. The right column shows the New York City skyline with text. The top right image has "HELLO WORLD" in white text and "WELLLO NEL WORLD" in red text. The bottom right image has "HELO WRLD" in white text.

New York Skyline with Hello World written with fireworks on the sky.

Image /page/24/Figure/2 description: The image is a collage of eight different storefronts, each featuring text. The top row, from left to right, shows a storefront with a white sign reading "Text to Image," a storefront with a red sign reading "Text to Image" and graffiti on the doors, a storefront with a white wall and a black sign reading "MMIG," and a storefront with a dark background and a white sign reading "MOG." The bottom row, from left to right, shows a storefront with an orange sign reading "TEXT-TO-IMAGE," a storefront with a white sign reading "TEXT TO IMAGE" and a frosted window with "IMAGE" written on it, a storefront with large windows displaying white text that appears to read "hmipe," and a storefront with a dark blue facade and a yellow window with black text reading "MIOSES."

A storefront with Text to Image written on it.

Figure A.26: Example qualitative comparisons between Imagen and GLIDE [41] on DrawBench prompts from Text category. Imagen is significantly better than GLIDE too in prompts with quoted text.

Image /page/25/Figure/0 description: This is a diagram illustrating a neural network block. The block consists of two sequential layers of GroupNorm followed by swish activation, and then a 3x3 convolutional layer with 'channels' channels. The output of the first 3x3 convolutional layer is fed into a 1x1 convolutional layer with 'channels' channels. Additionally, the output of the first 3x3 convolutional layer is also fed into the 1x1 convolutional layer, creating a residual connection.

Figure A.27: Efficient U-Net ResNetBlock. The ResNetBlock is used both by the DBlock and UBlock. Hyperparameter of the ResNetBlock is the number of channels channels: int.

<span id="page-25-0"></span>Image /page/25/Figure/2 description: This is a diagram illustrating a neural network block. The block receives input from a 'Previous DBlock'. It consists of four main components stacked vertically. The top component is a 'Conv' layer with parameters kernel\_size=3x3, strides=stride, and channels=channels. Below this is a 'CombineEmbs' layer, which takes 'Conditional Embeddings' (e.g., Time, Pooled Text Embeddings) as input. Following that is a 'ResNetBlock' with channels=channels, which is repeated 'numResNetBlocksPerBlock' times. The final component is a 'SelfAttention' layer with attention\_heads=8, hidden\_size=2xchannels, and output\_size=channels. This 'SelfAttention' layer receives 'Full Contextual Text Embeddings' as input. An arrow indicates the output of the 'SelfAttention' layer proceeds downwards.

Figure A.28: Efficient UNet DBlock. Hyperparameters of DBlock are: the stride of the block if there is downsampling stride: Optional[Tuple[int, int]], number of ResNetBlock per DBlock numResNetBlocksPerBlock: int, and number of channels channels: int. The dashed lined blocks are optional, e.g., not every DBlock needs to downsample or needs self-attention.

<span id="page-26-0"></span>Image /page/26/Figure/0 description: The image displays a block diagram illustrating a neural network architecture. At the top, an input labeled 'Previous UBlock' is shown with an arrow pointing to an addition symbol, indicating a skip connection from 'DBlock'. Another input, 'Conditional Embeddings', feeds into a 'CombineEmbs' block. Below 'CombineEmbs' is a 'ResNetBlock' with the parameter 'channels=channels', which is repeated '× numResNetBlocksPerBlock' times. Following the ResNetBlocks is a 'SelfAttention' block. Finally, a 'Conv' block with 'kernel\_size=3×3', 'strides=stride', and 'channels=channels' is depicted, with an arrow pointing downwards, signifying the output of this section of the network.

Figure A.29: Efficient U-Net UBlock. Hyperparameters of UBlock are: the stride of the block if there is upsampling stride: Optional[Tuple[int, int]], number of ResNetBlock per DBlock numResNetBlocksPerBlock: int, and number of channels channels: int. The dashed lined blocks are optional, e.g., not every UBlock needs to upsample or needs self-attention.

<span id="page-27-0"></span>Image /page/27/Figure/0 description: This is a diagram of a neural network architecture. The network begins with a convolutional layer (Conv) with a kernel size of 3x3 and 128 channels. This is followed by a series of five "DBlock" layers, decreasing in size from 256x to 16x. After the DBlock layers, there are five "UBlock" layers, also decreasing in size from 16x to 256x. The UBlock layers have skip connections from the corresponding DBlock layers. The network concludes with a "Dense" layer with 3 channels, outputting a 256x256 image. The diagram is labeled as "A 20: Efficient U-Net architecture for 64^2".

Figure A.30: Efficient U-Net architecture for  $64^2 \rightarrow 256^2$ .

```
def sample():
 for t in reversed(range(T)):
    # Forward pass to get x0_t from z_t.
   x0_t = \text{nn}(z_t, t)# Static thresholding.
    x0_t = jnp<u>clip(x0</u>_t, -1.0, 1.0)# Sampler step.
   z_tm1 = sampler_step(x0_t, z_t, t)
   z t = z tm1
 return x0_t
 (a) Implementation for static thresholding.
                                                        def sample(p: float):
                                                         for t in reversed(range(T)):
                                                            # Forward pass to get x0_t from z_t.
                                                            x0_t = \text{nn}(z_t, t)# Dynamic thresholding (ours).
                                                            s = jnp.percentile(
                                                                jnp.abs(x0_t), p,
                                                                axis=tuple(range(1, x0_t.ndim)))
                                                            s = jnp.max(s, 1.0)
                                                            x0_t = jnp<u>clip(x0</u>t, -s, s) / s# Sampler step.
                                                            z_tm1 = sampler_step(x0_t, z_t, t)
                                                            zt = ztm1
                                                          return x0_t
                                                        (b) Implementation for dynamic thresholding.
```

Figure A.31: Pseudo code implementation comparing static thresholding and dynamic thresholding.

```
def train_step(
   x_lr: jnp.ndarray, x_hr: jnp.ndarray):
  # Add augmentation to the low-resolution image.
 aug_level = jnp.random.uniform(0.0, 1.0)
 x_l = apply_aug(x_lr, aug_level)# Diffusion forward process.
  t = jnp.random.uniform(0.0, 1.0)z_t = forward_process(x_hr, t)\nonumber \texttt{Optimize loss(x_nr, nn(z_t, x_lr, t, aug\_level))}(a) Training using conditioning augmentation.
                                                        def sample(aug_level: float, x_lr: jnp.ndarray):
                                                         # Add augmentation to the low-resolution image.
                                                         x_l = apply_aug(x_lr, aug_level)for t in reversed(range(T)):
                                                           x_hr_t = nn(z_t, x_lr, t, aug_level)# Sampler step.
                                                           z_tm1 = sampler_step(x_hr_t, z_t, t)
                                                           z_t = z_t tm1
                                                         return x_hr_t
                                                       (b) Sampling using conditioning augmentation.
```

Figure A.32: Pseudo-code implementation for training and sampling using conditioning augmentation. Text conditioning has not been shown for brevity.

# F Implementation Details

**F.1**  $64 \times 64$ 

Architecture: We adapt the architecture used in [16]. We use larger *embed\_dim* for scaling up the architecture size. For conditioning on text, we use text cross attention at resolutions [32, 16, 8] as well as attention pooled text embedding.

**Optimizer:** We use the Adafactor optimizer for training the base model. We use the default [optax.adafactor](https://optax.readthedocs.io/en/latest/api.html#adafactor) parameters. We use a learning rate of 1e-4 with 10000 linear warmup steps.

Diffusion: We use the cosine noise schedule similar to [40]. We train using continuous time steps  $t \sim \mathcal{U}(0, 1).$ 

```
# 64 X 64 model.
architecture = {
     "attn_resolutions": [32, 16, 8],
     "channel_mult": [1, 2, 3, 4],
     "dropout": 0,
     "embed dim": 512.
     "num_res_blocks": 3,
     "per_head_channels": 64,
     "res_block_type": "biggan",
     "text_cross_attn_res": [32, 16, 8],
     "feature_pooling_type": "attention",
     "use_scale_shift_norm": True,
 }
 learning_rate = optax.warmup_cosine_decay_schedule(
     init_value=0.0,
     peak_value=1e-4,
     warmup_steps=10000,
     decay_steps=2500000,
     end_value=2500000)
```

optimizer = optax.adafactor(lrs=learning\_rate, weight\_decay=0)

```
diffusion_params = \{"continuous_time": True,
   "schedule": {
"name": "cosine",
  }
}
```

F.2  $64 \times 64 \rightarrow 256 \times 256$ 

**Architecture:** Below is the architecture specification for our  $64 \times 64 \rightarrow 256 \times 256$  super-resolution model. We use an Efficient U-Net architecture for this model.

Optimizer: We use the standard Adam optimizer with 1e-4 learning rate, and 10000 warmup steps.

**Diffusion:** We use the same cosine noise schedule as the base  $64 \times 64$  model. We train using continuous time steps  $t \sim \mathcal{U}(0, 1)$ .

```
architecture = {
    "dropout": 0.0,
    "feature_pooling_type": "attention",
    "use_scale_shift_norm": True,
    "blocks": [
        {
          "channels": 128,
          "strides": (2, 2),
          "kernel_size": (3, 3),
          "num_res_blocks": 2,
        },
        {
          "channels": 256,
          "strides": (2, 2),
          "kernel_size": (3, 3),
          "num_res_blocks": 4,
        },
        {
          "channels": 512,
          "strides": (2, 2),
          "kernel_size": (3, 3),
          "num_res_blocks": 8,
        },
        {
          "channels": 1024,
           "strides": (2, 2),
"kernel_size": (3, 3),
          "num_res_blocks": 8,
          "self_attention": True,
          "text_cross_attention": True,
          "num_attention_heads": 8
        }
    ]
}
learning_rate = optax.warmup_cosine_decay_schedule(
    init_value=0.0,
    peak_value=1e-4,
    warmup_steps=10000,
    decay_steps=2500000,
    end_value=2500000)
optimizer = optax.adam(
    lrs=learning_rate, b1=0.9, b2=0.999, eps=1e-8, weight_decay=0)
diffusion_params = {
  "continuous_time": True,
  "schedule": {
    "name": "cosine",
 }
}
```

F.3  $256 \times 256 \rightarrow 1024 \times 1024$ 

**Architecture:** Below is the architecture specification for our  $256 \times 256 \rightarrow 1024 \times 1024$  superresolution model. We use the same configuration as the  $64 \times 64 \rightarrow 256 \times 256$  super-resolution model, except we do not use self-attention layers but rather have cross-attention layers (to the text embeddings).

Optimizer: We use the standard Adam optimizer with 1e-4 learning rate, and 10000 linear warmup steps.

Diffusion: We use the 1000 step linear noise schedule with start and end set to 1e-4 and 0.02 respectively. We train using continuous time steps  $t \sim \mathcal{U}(0, 1)$ .

```
"dropout": 0.0,
"feature_pooling_type": "attention",
"use_scale_shift_norm": true,
"blocks"=[
     {
"channels": 128,
       "strides": (2, 2),
       "kernel_size": (3, 3),
       "num_res_blocks": 2,
    },
    {
       "channels": 256,
"strides": (2, 2),
       "kernel_size": (3, 3),
       "num_res_blocks": 4,
    },
    \mathcal{L}"channels": 512,
       "strides": (2, 2),
       "kernel_size": (3, 3),
"num_res_blocks": 8,
    },
    \mathcal{L}"channels": 1024,
       "strides": (2, 2),
"kernel_size": (3, 3),
       "num_res_blocks": 8,
       "text_cross_attention": True,
       "num_attention_heads": 8
    }
\mathbf l
```