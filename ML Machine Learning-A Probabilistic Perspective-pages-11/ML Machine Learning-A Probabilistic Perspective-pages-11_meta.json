{"table_of_contents": [{"title": "9 Generalized linear models and the\nexponential family", "heading_level": null, "page_id": 0, "polygon": [[84.796875, 98.244140625], [384.1875, 98.244140625], [384.1875, 142.69921875], [84.796875, 142.69921875]]}, {"title": "9.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 205.98046875], [195.75, 205.98046875], [195.75, 218.00390625], [101.25, 218.00390625]]}, {"title": "9.2 The exponential family", "heading_level": null, "page_id": 0, "polygon": [[99.0, 364.81640625], [249.46875, 364.81640625], [249.46875, 376.20703125], [99.0, 376.20703125]]}, {"title": "9.2.1 Definition", "heading_level": null, "page_id": 1, "polygon": [[93.375, 60.75], [178.5, 60.75], [178.5, 71.62646484375], [93.375, 71.62646484375]]}, {"title": "9.2.2 Examples", "heading_level": null, "page_id": 1, "polygon": [[91.125, 372.0], [177.0, 372.0], [177.0, 382.8515625], [91.125, 382.8515625]]}, {"title": "9.2.2.1 <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 1, "polygon": [[87.75, 417.0], [171.0, 417.0], [171.0, 426.83203125], [87.75, 426.83203125]]}, {"title": "9.2.2.2 Multinoull<PERSON>", "heading_level": null, "page_id": 2, "polygon": [[86.25, 132.0], [178.5, 132.0], [178.5, 142.224609375], [86.25, 142.224609375]]}, {"title": "9.2.2.3 Univariate G<PERSON>sian", "heading_level": null, "page_id": 3, "polygon": [[85.7109375, 60.75], [218.25, 60.75], [218.25, 71.70556640625], [85.7109375, 71.70556640625]]}, {"title": "9.2.2.4 Non-examples", "heading_level": null, "page_id": 3, "polygon": [[87.0, 321.75], [192.796875, 321.75], [192.796875, 331.59375], [87.0, 331.59375]]}, {"title": "9.2.3 Log partition function", "heading_level": null, "page_id": 3, "polygon": [[92.25, 402.0], [236.25, 402.0], [236.25, 412.91015625], [92.25, 412.91015625]]}, {"title": "9.2.3.1 Example: the <PERSON><PERSON><PERSON> distribution", "heading_level": null, "page_id": 4, "polygon": [[87.75, 453.75], [285.0, 453.75], [285.0, 464.484375], [87.75, 464.484375]]}, {"title": "9.2.4 MLE for the exponential family", "heading_level": null, "page_id": 5, "polygon": [[93.0, 61.5], [278.25, 61.5], [278.25, 71.5078125], [93.0, 71.5078125]]}, {"title": "9.2.5 Bayes for the exponential family *", "heading_level": null, "page_id": 6, "polygon": [[92.25, 135.75], [291.75, 135.75], [291.75, 145.9423828125], [92.25, 145.9423828125]]}, {"title": "9.2.5.1 Likelihood", "heading_level": null, "page_id": 6, "polygon": [[88.03125, 239.25], [177.046875, 239.25], [177.046875, 249.169921875], [88.03125, 249.169921875]]}, {"title": "9.2.5.2 Prior", "heading_level": null, "page_id": 6, "polygon": [[86.1328125, 355.5], [153.75, 355.5], [153.75, 365.44921875], [86.1328125, 365.44921875]]}, {"title": "9.2.5.3 Posterior", "heading_level": null, "page_id": 6, "polygon": [[86.2734375, 464.25], [169.59375, 464.25], [169.59375, 474.29296875], [86.2734375, 474.29296875]]}, {"title": "9.2.5.4 Posterior predictive density", "heading_level": null, "page_id": 7, "polygon": [[86.4140625, 61.5], [250.5, 61.5], [250.5, 71.7451171875], [86.4140625, 71.7451171875]]}, {"title": "9.2.5.5 Example: <PERSON><PERSON><PERSON> distribution", "heading_level": null, "page_id": 7, "polygon": [[87.0, 366.0], [267.75, 366.0], [267.75, 376.20703125], [87.0, 376.20703125]]}, {"title": "9.2.6 Maximum entropy derivation of the exponential family *", "heading_level": null, "page_id": 8, "polygon": [[91.5, 243.75], [399.0, 243.75], [399.0, 254.865234375], [91.5, 254.865234375]]}, {"title": "9.3 Generalized linear models (GLMs)", "heading_level": null, "page_id": 9, "polygon": [[99.0, 361.5], [305.25, 361.5], [305.25, 373.04296875], [99.0, 373.04296875]]}, {"title": "9.3.1 Basics", "heading_level": null, "page_id": 9, "polygon": [[94.5, 471.75], [160.5, 471.75], [160.5, 481.5703125], [94.5, 481.5703125]]}, {"title": "9.3.2 ML and MAP estimation", "heading_level": null, "page_id": 11, "polygon": [[92.0390625, 270.0], [243.75, 270.0], [243.75, 280.494140625], [92.0390625, 280.494140625]]}, {"title": "9.3.3 Bayesian inference", "heading_level": null, "page_id": 12, "polygon": [[93.0, 345.0], [219.0, 345.0], [219.0, 354.75], [93.0, 354.75]]}, {"title": "9.4 Probit regression", "heading_level": null, "page_id": 12, "polygon": [[99.75, 439.5], [219.09375, 439.5], [219.09375, 450.5625], [99.75, 450.5625]]}, {"title": "9.4.1 ML/MAP estimation using gradient-based optimization", "heading_level": null, "page_id": 13, "polygon": [[92.953125, 61.5], [387.75, 61.5], [387.75, 71.70556640625], [92.953125, 71.70556640625]]}, {"title": "9.4.2 Latent variable interpretation", "heading_level": null, "page_id": 13, "polygon": [[92.390625, 267.0], [270.140625, 267.0], [270.140625, 277.171875], [92.390625, 277.171875]]}, {"title": "9.4.3 Ordinal probit regression *", "heading_level": null, "page_id": 14, "polygon": [[93.0, 124.5], [258.0, 123.75], [258.0, 134.7099609375], [93.0, 134.7099609375]]}, {"title": "9.4.4 Multinomial probit models *", "heading_level": null, "page_id": 14, "polygon": [[92.671875, 353.25], [264.0, 353.25], [264.0, 363.234375], [92.671875, 363.234375]]}, {"title": "9.5 Multi-task learning", "heading_level": null, "page_id": 15, "polygon": [[99.0, 60.0], [228.75, 60.0], [228.75, 72.140625], [99.0, 72.140625]]}, {"title": "9.5.1 Hierarchical Bayes for multi-task learning", "heading_level": null, "page_id": 15, "polygon": [[94.1484375, 182.25], [328.5, 182.25], [328.5, 192.533203125], [94.1484375, 192.533203125]]}, {"title": "9.5.2 Application to personalized email spam filtering", "heading_level": null, "page_id": 15, "polygon": [[93.0, 535.5], [358.5, 535.5], [358.5, 546.1171875], [93.0, 546.1171875]]}, {"title": "9.5.3 Application to domain adaptation", "heading_level": null, "page_id": 16, "polygon": [[92.25, 324.0], [289.5, 324.0], [289.5, 334.44140625], [92.25, 334.44140625]]}, {"title": "9.5.4 Other kinds of prior", "heading_level": null, "page_id": 16, "polygon": [[93.0, 440.25], [226.96875, 440.25], [226.96875, 451.1953125], [93.0, 451.1953125]]}, {"title": "9.6 Generalized linear mixed models *", "heading_level": null, "page_id": 17, "polygon": [[99.0, 126.0], [309.75, 126.0], [309.75, 136.845703125], [99.0, 136.845703125]]}, {"title": "9.6.1 Example: semi-parametric GLMMs for medical data", "heading_level": null, "page_id": 17, "polygon": [[93.75, 334.5], [372.75, 334.5], [372.75, 344.56640625], [93.75, 344.56640625]]}, {"title": "9.6.2 Computational issues", "heading_level": null, "page_id": 19, "polygon": [[91.5, 112.5], [231.75, 112.5], [231.75, 122.607421875], [91.5, 122.607421875]]}, {"title": "9.7 Learning to rank *", "heading_level": null, "page_id": 19, "polygon": [[99.75, 374.25], [226.5, 374.25], [226.5, 385.3828125], [99.75, 385.3828125]]}, {"title": "9.7.1 The pointwise approach", "heading_level": null, "page_id": 20, "polygon": [[94.5, 210.75], [243.84375, 210.75], [243.84375, 221.80078125], [94.5, 221.80078125]]}, {"title": "9.7.2 The pairwise approach", "heading_level": null, "page_id": 20, "polygon": [[93.0, 448.5], [237.75, 448.5], [237.75, 458.7890625], [93.0, 458.7890625]]}, {"title": "9.7.3 The listwise approach", "heading_level": null, "page_id": 21, "polygon": [[92.8125, 217.5], [233.25, 217.5], [233.25, 227.654296875], [92.8125, 227.654296875]]}, {"title": "9.7.4 Loss functions for ranking", "heading_level": null, "page_id": 22, "polygon": [[93.0, 186.75], [255.75, 186.75], [255.75, 197.279296875], [93.0, 197.279296875]]}, {"title": "Exercises", "heading_level": null, "page_id": 24, "polygon": [[129.7265625, 186.0], [178.5, 186.0], [178.5, 196.8046875], [129.7265625, 196.8046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 27], ["ListItem", 5], ["SectionHeader", 3], ["Text", 3], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["Line", 41], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 539], ["Line", 107], ["Equation", 11], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 996, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 54], ["Equation", 7], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 70], ["Equation", 14], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 58], ["Equation", 7], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 46], ["Equation", 8], ["TextInlineMath", 5], ["Text", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 87], ["Equation", 10], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1000, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 63], ["Equation", 9], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 48], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 599, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 51], ["TableCell", 32], ["Text", 6], ["Equation", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["ListItem", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4449, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 564], ["Line", 73], ["Equation", 9], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 36], ["TableCell", 20], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1149, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 601], ["Line", 55], ["Equation", 11], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 37], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["Line", 49], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 49], ["Text", 7], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["Line", 51], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 36], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 40], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 41], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 69], ["Equation", 6], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 50], ["Text", 6], ["Equation", 5], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["Line", 55], ["TableCell", 16], ["Text", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Table", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1252, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 169], ["Line", 18], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-11"}