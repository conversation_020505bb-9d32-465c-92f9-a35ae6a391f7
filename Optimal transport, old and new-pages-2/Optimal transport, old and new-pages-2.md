Couplings are very well-known in all branches of probability theory, but since they will occur again and again in this course, it might be a good idea to start with some basic reminders and a few more technical issues.

**Definition 1.1 (Coupling).** Let  $(\mathcal{X}, \mu)$  and  $(\mathcal{Y}, \nu)$  be two probability spaces. Coupling  $\mu$  and  $\nu$  means constructing two random variables X and Y on some probability space  $(\Omega, \mathbb{P})$ , such that law  $(X) = \mu$ , law  $(Y) = \nu$ . The couple  $(X, Y)$  is called a coupling of  $(\mu, \nu)$ . By abuse of language, the law of  $(X, Y)$  is also called a coupling of  $(\mu, \nu)$ .

If  $\mu$  and  $\nu$  are the only laws in the problem, then without loss of generality one may choose  $\Omega = \mathcal{X} \times \mathcal{Y}$ . In a more measure-theoretical formulation, coupling  $\mu$  and  $\nu$  means constructing a measure  $\pi$  on  $\mathcal{X}\times\mathcal{Y}$ such that  $\pi$  admits  $\mu$  and  $\nu$  as **marginals** on X and Y respectively. The following three statements are equivalent ways to rephrase that marginal condition:

- $(\text{proj}_{\mathcal{X}})_\#\pi = \mu$ ,  $(\text{proj}_{\mathcal{Y}})_\#\pi = \nu$ , where  $\text{proj}_{\mathcal{X}}$  and  $\text{proj}_{\mathcal{Y}}$  respectively stand for the projection maps  $(x,y) \longmapsto x$  and  $(x,y) \longmapsto y$ ;
- For all measurable sets  $A \subset \mathcal{X}, B \subset \mathcal{Y}$ , one has  $\pi[A \times \mathcal{Y}] = \mu[A],$  $\pi[\mathcal{X} \times B] = \nu[B];$
- For all integrable (resp. nonnegative) measurable functions  $\varphi, \psi$  on  $\mathcal{X}, \mathcal{Y},$

$$
\int_{\mathcal{X}\times\mathcal{Y}} (\varphi(x) + \psi(y)) d\pi(x, y) = \int_{\mathcal{X}} \varphi d\mu + \int_{\mathcal{Y}} \psi d\nu.
$$

A first remark about couplings is that they always exist: at least there is the **trivial coupling**, in which the variables  $X$  and  $Y$  are **independent** (so their joint law is the tensor product  $\mu \otimes \nu$ ). This can hardly be called a coupling, since the value of  $X$  does not give any information about the value of  $Y$ . Another extreme is when all the information about the value of  $Y$  is contained in the value of  $X$ , in other words  $Y$  is just a function of  $X$ . This motivates the following definition (in which  $X$  and  $Y$  do not play symmetric roles).

Definition 1.2 (Deterministic coupling). With the notation of Definition 1.1, a coupling  $(X, Y)$  is said to be deterministic if there exists a measurable function  $T : \mathcal{X} \to \mathcal{Y}$  such that  $Y = T(X)$ .

To say that  $(X, Y)$  is a deterministic coupling of  $\mu$  and  $\nu$  is strictly equivalent to any one of the four statements below:

- $(X, Y)$  is a coupling of  $\mu$  and  $\nu$  whose law  $\pi$  is concentrated on the *graph* of a measurable function  $T : \mathcal{X} \to \mathcal{Y}$ ;
- X has law  $\mu$  and  $Y = T(X)$ , where  $T_{\#}\mu = \nu$ ;
- X has law  $\mu$  and  $Y = T(X)$ , where T is a change of variables from  $\mu$  to  $\nu$ : for all  $\nu$ -integrable (resp. nonnegative measurable) functions  $\varphi$ ,

$$
\int_{\mathcal{Y}} \varphi(y) d\nu(y) = \int_{\mathcal{X}} \varphi(T(x)) d\mu(x); \tag{1.1}
$$

•  $\pi = (\mathrm{Id}\, ,T)_{\#}\mu.$ 

The map T appearing in all these statements is the same and is uniquely defined  $\mu$ -almost surely (when the joint law of  $(X, Y)$  has been fixed). The converse is true: If T and T coincide  $\mu$ -almost surely, then  $T_{\#}\mu = T_{\#}\mu$ . It is common to call T the **transport map**: Informally, one can say that T transports the mass represented by the measure  $\mu$ , to the mass represented by the measure  $\nu$ .

Unlike couplings, deterministic couplings do not always exist: Just think of the case when  $\mu$  is a Dirac mass and  $\nu$  is not. But there may also be infinitely many deterministic couplings between two given probability measures.

### Some famous couplings

Here below are some of the most famous couplings used in mathematics — of course the list is far from complete, since everybody has his or her own preferred coupling technique. Each of these couplings comes with its own natural setting; this variety of assumptions reflects the variety of constructions. (This is a good reason to state each of them with some generality.)

- 1. The **measurable isomorphism.** Let  $(\mathcal{X}, \mu)$  and  $(\mathcal{Y}, \nu)$  be two Polish (i.e. complete, separable, metric) probability spaces without atom (i.e. no single point carries a positive mass). Then there exists a (nonunique) measurable bijection  $T : \mathcal{X} \to \mathcal{Y}$  such that  $T_{\#}\mu = \nu$ ,  $(T^{-1})_{\#}\nu = \mu$ . In that sense, all atomless Polish probability spaces are isomorphic, and, say, isomorphic to the space  $\mathcal{Y} = [0, 1]$  equipped with the Lebesgue measure. Powerful as that theorem may seem, in practice the map  $T$  is very singular; as a good exercise, the reader might try to construct it "explicitly", in terms of cumulative distribution functions, when  $\mathcal{X} = \mathbb{R}$  and  $\mathcal{Y} = [0, 1]$ (issues do arise when the density of  $\mu$  vanishes at some places). Experience shows that it is quite easy to fall into logical traps when working with the measurable isomorphism, and my advice is to never use it.
- 2. The Moser mapping. Let  $\mathcal X$  be a smooth compact Riemannian manifold with volume vol, and let  $f, g$  be Lipschitz continuous positive probability densities on  $\mathcal{X}$ ; then there exists a deterministic coupling of  $\mu = f$  vol and  $\nu = g$  vol, constructed by resolution of an elliptic equation. On the positive side, there is a somewhat explicit representation of the transport map  $T$ , and it is as smooth as can be: if  $f, g$  are  $C^{k,\alpha}$  then T is  $C^{k+1,\alpha}$ . The formula is given in the Appendix at the end of this chapter. The same construction works in  $\mathbb{R}^n$  provided that f and g decay fast enough at infinity; and it is robust enough to accommodate for variants.
- 3. The increasing rearrangement on R. Let  $\mu$ ,  $\nu$  be two probability measures on R; define their cumulative distribution functions by

$$
F(x) = \int_{-\infty}^{x} d\mu, \qquad G(y) = \int_{-\infty}^{y} d\nu.
$$

Further define their right-continuous inverses by

$$
F^{-1}(t) = \inf \left\{ x \in \mathbb{R}; \ F(x) > t \right\};
$$
  
$$
G^{-1}(t) = \inf \left\{ y \in \mathbb{R}; \ G(y) > t \right\};
$$

and set

$$
T = G^{-1} \circ F.
$$

If  $\mu$  does not have atoms, then  $T_{\#}\mu = \nu$ . This rearrangement is quite simple, explicit, as smooth as can be, and enjoys good geometric properties.

4. The Knothe–Rosenblatt rearrangement in  $\mathbb{R}^n$ . Let  $\mu$  and  $\nu$  be two probability measures on  $\mathbb{R}^n$ , such that  $\mu$  is absolutely continuous with respect to Lebesgue measure. Then define a coupling of  $\mu$ and  $\nu$  as follows.

Step 1: Take the marginal on the first variable: this gives probability measures  $\mu_1(dx_1), \nu_1(dy_1)$  on R, with  $\mu_1$  being atomless. Then define  $y_1 = T_1(x_1)$  by the formula for the increasing rearrangement of  $\mu_1$  into  $\nu_1$ .

Step 2: Now take the marginal on the first two variables and disintegrate it with respect to the first variable. This gives probability measures  $\mu_2(dx_1 dx_2) = \mu_1(dx_1) \mu_2(dx_2|x_1), \nu_2(dy_1 dy_2) =$  $\nu_1(dy_1)\nu_2(dy_2|y_1)$ . Then, for each given  $y_1 \in \mathbb{R}$ , set  $y_1 = T_1(x_1)$ , and define  $y_2 = T_2(x_2; x_1)$  by the formula for the increasing rearrangement of  $\mu_2(dx_2|x_1)$  into  $\nu_2(dy_2|y_1)$ . (See Figure 1.1.)

Then repeat the construction, adding variables one after the other and defining  $y_3 = T_3(x_3; x_1, x_2)$ ; etc. After *n* steps, this produces a map  $y = T(x)$  which transports  $\mu$  to  $\nu$ , and in practical situations might be computed explicitly with little effort. Moreover, the Jacobian matrix of the change of variables  $T$  is (by construction) upper triangular with positive entries on the diagonal; this makes it suitable for various geometric applications. On the negative side, this mapping does not satisfy many interesting intrinsic properties; it is not invariant under isometries of  $\mathbb{R}^n$ , not even under relabeling of coordinates.

5. The **Holley coupling** on a lattice. Let  $\mu$  and  $\nu$  be two discrete probabilities on a finite lattice  $\Lambda$ , say  $\{0,1\}^N$ , equipped with the natural partial ordering  $(x \leq y$  if  $x_n \leq y_n$  for all n). Assume that

$$
\forall x, y \in \Lambda, \qquad \mu[\inf(x, y)] \; \nu[\sup(x, y)] \ge \mu[x] \; \nu[y]. \tag{1.2}
$$

Image /page/4/Figure/1 description: The image displays two shaded regions, labeled \"mu\" and \"nu\". Both regions are bounded by irregular curves and are intersected by two vertical lines. Below the regions, the labels \"dx1\" and \"dy1\" are shown, with a curved arrow labeled \"T1\" pointing from \"dx1\" to \"dy1\". The overall layout suggests a diagram illustrating a transformation or mapping between two sets or distributions.

Fig. 1.1. Second step in the construction of the Knothe–Rosenblatt map: After the correspondance  $x_1 \rightarrow y_1$  has been determined, the conditional probability of  $x_2$  (seen as a one-dimensional probability on a small "slice" of width  $dx_1$ ) can be transported to the conditional probability of  $y_2$  (seen as a one-dimensional probability on a slice of width  $dy_1$ ).

Then there exists a coupling  $(X, Y)$  of  $(\mu, \nu)$  with  $X \leq Y$ . The situation above appears in a number of problems in statistical mechanics, in connection with the so-called FKG (Fortuin–Kasteleyn–Ginibre) inequalities. Inequality (1.2) intuitively says that  $\nu$  puts more mass on large values than  $\mu$ .

- 6. Probabilistic representation formulas for solutions of partial differential equations. There are hundreds of them (if not thousands), representing solutions of diffusion, transport or jump processes as the laws of various deterministic or stochastic processes. Some of them are recalled later in this chapter.
- 7. The exact coupling of two stochastic processes, or Markov chains. Two realizations of a stochastic process are started at initial time, and when they happen to be in the same state at some time, they are merged: from that time on, they follow the same path and accordingly, have the same law. For two Markov chains which are started independently, this is called the classical coupling. There

are many variants with important differences which are all intended to make two trajectories close to each other after some time: the **Ornstein coupling, the**  $\varepsilon$ **-coupling (in which one requires the** two variables to be close, rather than to occupy the same state), the shift-coupling (in which one allows an additional time-shift), etc.

8. The optimal coupling or optimal transport. Here one introduces a cost function  $c(x, y)$  on  $\mathcal{X} \times \mathcal{Y}$ , that can be interpreted as the work needed to move one unit of mass from location  $x$  to location y. Then one considers the **Monge–Kantorovich mini**mization problem

$$
\inf \mathbb{E} c(X, Y),
$$

where the pair  $(X, Y)$  runs over all possible couplings of  $(\mu, \nu)$ ; or equivalently, in terms of measures,

$$
\inf \int_{\mathcal{X}\times \mathcal{Y}} c(x,y) \, d\pi(x,y),
$$

where the infimum runs over all joint probability measures  $\pi$  on  $\mathcal{X} \times \mathcal{Y}$  with marginals  $\mu$  and  $\nu$ . Such joint measures are called **trans**ference plans (or transport plans, or transportation plans); those achieving the infimum are called optimal transference plans.

Of course, the solution of the Monge–Kantorovich problem depends on the cost function c. The cost function and the probability spaces here can be very general, and some nontrivial results can be obtained as soon as, say, c is lower semicontinuous and  $\mathcal{X}, \mathcal{Y}$  are Polish spaces. Even the apparently trivial choice  $c(x,y) = 1_{x \neq y}$  appears in the probabilistic interpretation of total variation:

$$
\|\mu - \nu\|_{TV} = 2 \inf \Big\{ \mathbb{E} \, 1_{X \neq Y}; \quad \text{law}(X) = \mu, \text{ law}(Y) = \nu \Big\}.
$$

Cost functions valued in  $\{0, 1\}$  also occur naturally in Strassen's duality theorem.

Under certain assumptions one can guarantee that the optimal coupling really is deterministic; the search of deterministic optimal couplings (or Monge couplings) is called the Monge problem. A solution of the Monge problem yields a plan to transport the mass at minimal cost with a recipe that associates to each point x a single point y.  $($ "No" mass shall be split.") To guarantee the existence of solutions to the

Monge problem, two kinds of assumptions are natural: First, c should "vary enough" in some sense (think that the constant cost function will allow for arbitrary minimizers), and secondly,  $\mu$  should enjoy some regularity property (at least Dirac masses should be ruled out!). Here is a typical result: If  $c(x, y) = |x - y|^2$  in the Euclidean space,  $\mu$  is absolutely continuous with respect to Lebesgue measure, and  $\mu$ ,  $\nu$  have finite moments of order 2, then there is a unique optimal Monge coupling between  $\mu$  and  $\nu$ . More general statements will be established in Chapter 10.

Optimal couplings enjoy several nice properties:

(i) They naturally arise in many problems coming from economics, physics, partial differential equations or geometry (by the way, the increasing rearrangement and the Holley coupling can be seen as particular cases of optimal transport);

(ii) They are quite stable with respect to perturbations;

(iii) They encode good geometric information, if the cost function  $c$ is defined in terms of the underlying geometry;

(iv) They exist in smooth as well as nonsmooth settings;

(v) They come with a rich structure: an optimal cost functional (the value of the infimum defining the Monge–Kantorovich problem); a dual variational problem; and, under adequate structure conditions, a continuous interpolation.

On the negative side, it is important to be warned that optimal transport is in general not so smooth. There are known counterexamples which put limits on the regularity that one can expect from it, even for very nice cost functions.

All these issues will be discussed again and again in the sequel. The rest of this chapter is devoted to some basic technical tools.

## Gluing

If  $Z$  is a function of  $Y$  and  $Y$  is a function of  $X$ , then of course  $Z$  is a function of X. Something of this still remains true in the setting of nondeterministic couplings, under quite general assumptions.

Gluing lemma. Let  $(X_i, \mu_i)$ ,  $i = 1, 2, 3$ , be Polish probability spaces. If  $(X_1, X_2)$  is a coupling of  $(\mu_1, \mu_2)$  and  $(Y_2, Y_3)$  is a coupling of  $(\mu_2, \mu_3)$ , then one can construct a triple of random variables  $(Z_1, Z_2, Z_3)$  such that  $(Z_1, Z_2)$  has the same law as  $(X_1, X_2)$  and  $(Z_2, Z_3)$  has the same law as  $(Y_2, Y_3)$ .

It is simple to understand why this is called "gluing lemma": if  $\pi_{12}$ stands for the law of  $(X_1, X_2)$  on  $\mathcal{X}_1 \times \mathcal{X}_2$  and  $\pi_{23}$  stands for the law of  $(X_2, X_3)$  on  $\mathcal{X}_2 \times \mathcal{X}_3$ , then to construct the joint law  $\pi_{123}$  of  $(Z_1, Z_2, Z_3)$ one just has to glue  $\pi_{12}$  and  $\pi_{23}$  along their common marginal  $\mu_2$ . Expressed in a slightly informal way: Disintegrate  $\pi_{12}$  and  $\pi_{23}$  as

$$
\pi_{12}(dx_1 dx_2) = \pi_{12}(dx_1|x_2)\,\mu_2(dx_2),
$$
  
$$
\pi_{23}(dx_2 dx_3) = \pi_{23}(dx_3|x_2)\,\mu_2(dx_2),
$$

and then reconstruct  $\pi_{123}$  as

$$
\pi_{123}(dx_1 dx_2 dx_3) = \pi_{12}(dx_1 | x_2) \mu_2(dx_2) \pi_{23}(dx_3 | x_2).
$$

## Change of variables formula

When one writes the formula for change of variables, say in  $\mathbb{R}^n$  or on a Riemannian manifold, a Jacobian term appears, and one has to be careful about two things: the change of variables should be injective (otherwise, reduce to a subset where it is injective, or take the multiplicity into account); and it should be somewhat smooth. It is classical to write these formulas when the change of variables is continuously differentiable, or at least Lipschitz:

Change of variables formula. Let M be an n-dimensional Riemannian manifold with a  $C^1$  metric, let  $\mu_0$ ,  $\mu_1$  be two probability measures on M, and let  $T : M \to M$  be a measurable function such that  $T_{\#}\mu_0 =$  $\mu_1$ . Let  $\nu$  be a reference measure, of the form  $\nu(dx) = e^{-V(x)} \text{vol}(dx)$ , where  $V$  is continuous and vol is the volume (or n-dimensional Hausdorff) measure. Further assume that

(i)  $\mu_0(dx) = \rho_0(x) \nu(dx)$  and  $\mu_1(dy) = \rho_1(y) \nu(dy)$ ;

- $(ii)$  T is injective;
- $(iii)$  T is locally Lipschitz.

Then,  $\mu_0$ -almost surely,

$$
\rho_0(x) = \rho_1(T(x)) \mathcal{J}_T(x), \qquad (1.3)
$$

where  $\mathcal{J}_T(x)$  is the Jacobian determinant of T at x, defined by

$$
\mathcal{J}_T(x) := \lim_{\varepsilon \downarrow 0} \frac{\nu[T(B_\varepsilon(x))]}{\nu[B_\varepsilon(x)]}.\tag{1.4}
$$

The same holds true if T is only defined on the complement of a  $\mu_0$ negligible set, and satisfies properties (ii) and (iii) on its domain of definition.

**Remark 1.3.** When  $\nu$  is just the volume measure,  $\mathcal{J}_T$  coincides with the usual Jacobian determinant, which in the case  $M = \mathbb{R}^n$  is the absolute value of the determinant of the Jacobian matrix  $\nabla T$ . Since V is continuous, it is almost immediate to deduce the statement with an arbitrary  $V$  from the statement with  $V = 0$  (this amounts to multiplying  $\rho_0(x)$  by  $e^{V(x)}$ ,  $\rho_1(y)$  by  $e^{V(y)}$ ,  $\mathcal{J}_T(x)$  by  $e^{V(x)-V(T(x))})$ .

Remark 1.4. There is a more general framework beyond differentiability, namely the property of approximate differentiability. A function  $T$  on an *n*-dimensional Riemannian manifold is said to be approximately differentiable at  $x$  if there exists a function  $T$ , differentiable at x, such that the set  $\{T \neq T\}$  has zero density at x, i.e.

$$
\lim_{r \to 0} \frac{\text{vol}\left[\left\{x \in B_r(x); T(x) \neq \widetilde{T}(x)\right\}\right]}{\text{vol}\left[B_r(x)\right]} = 0.
$$

It turns out that, roughly speaking, an approximately differentiable map can be replaced, up to neglecting a small set, by a Lipschitz map (this is a kind of differentiable version of Lusin's theorem). So one can prove the Jacobian formula for an approximately differentiable map by approximating it with a sequence of Lipschitz maps.

Approximate differentiability is obviously a local property; it holds true if the distributional derivative of  $T$  is a locally integrable function, or even a locally finite measure. So it is useful to know that the change of variables formula still holds true if Assumption (iii) above is replaced by

 $(iii')$  T is approximately differentiable.

### Conservation of mass Formula

The single most important theorem of change of variables arising in continuum physics might be the one resulting from the conservation of mass formula,

$$
\frac{\partial \rho}{\partial t} + \nabla \cdot (\rho \xi) = 0. \tag{1.5}
$$

Here  $\rho = \rho(t, x)$  stands for the density of a system of particles at time t and position  $x; \xi = \xi(t,x)$  for the velocity field at time t and position x; and  $\nabla$  stands for the divergence operator. Once again, the natural setting for this equation is a Riemannian manifold M.

It will be useful to work with particle densities  $\mu_t(dx)$  (that are not necessarily absolutely continuous) and rewrite (1.5) as

$$
\frac{\partial \mu}{\partial t} + \nabla \cdot (\mu \,\xi) = 0,
$$

where the time-derivative is taken in the weak sense, and the divergence operator is defined by duality against continuously differentiable functions with compact support:

$$
\int_M \varphi \, \nabla \cdot (\mu \, \xi) = - \int_M (\xi \cdot \nabla \varphi) \, d\mu.
$$

The formula of conservation of mass is an Eulerian description of the physical world, which means that the unknowns are fields. The next theorem links it with the Lagrangian description, in which everything is expressed in terms of particle trajectories, that are integral curves of the velocity field:

$$
\xi(t, T_t(x)) = \frac{d}{dt} T_t(x). \tag{1.6}
$$

If  $\xi$  is (locally) Lipschitz continuous, then the Cauchy–Lipschitz theorem guarantees the existence of a flow  $T_t$  locally defined on a maximal time interval, and itself locally Lipschitz in both arguments  $t$  and  $x$ . Then, for each  $t$  the map  $T_t$  is a local diffeomorphism onto its image. But the formula of conservation of mass also holds true without any regularity assumption on  $\xi$ ; one should only keep in mind that if  $\xi$  is not Lipschitz, then a solution of (1.6) is not uniquely determined by its value at time 0, so  $x \mapsto T_t(x)$  is not necessarily uniquely defined. Still it makes sense to consider random solutions of (1.6).

Mass conservation formula. Let M be a  $C^1$  manifold,  $T \in (0, +\infty]$ and let  $\xi(t,x)$  be a (measurable) velocity field on  $[0,T) \times M$ . Let  $(\mu_t)_{0 \leq t \leq T}$  be a time-dependent family of probability measures on M (continuous in time for the weak topology), such that

$$
\int_0^T \int_M |\xi(t,x)| \,\mu_t(dx) \, dt < +\infty.
$$

Then, the following two statements are equivalent:

(i)  $\mu = \mu_t(dx)$  is a weak solution of the linear (transport) partial differential equation

$$
\partial_t \mu + \nabla_x \cdot (\mu \,\xi) = 0
$$

on  $[0,T)\times M$ :

(ii)  $\mu_t$  is the law at time t of a random solution  $T_t(x)$  of (1.6).

If moreover  $\xi$  is locally Lipschitz, then  $(T_t)_{0 \leq t \leq T}$  defines a deterministic flow, and statement *(ii)* can be rewritten

(ii')  $\mu_t = (T_t)_{\#}\mu_0$ .

## Diffusion formula

The final reminder in this chapter is very well-known and related to Itô's formula; it was discovered independently (in the Euclidean context) by Bachelier, Einstein and Smoluchowski at the beginning of the twentieth century. It requires a bit more regularity than the Conservation of mass Formula. The natural assumptions on the phase space are in terms of Ricci curvature, a concept which will play an important role in these notes. For the reader who has no idea what Ricci curvature means, it is sufficient to know that the theorem below applies when M is either  $\mathbb{R}^n$ , or a compact manifold with a  $C^2$  metric. By convention,  $B_t$  denotes the "standard" Brownian motion on  $M$  with identity covariance matrix.

**Diffusion theorem.** Let M be a Riemannian manifold with a  $C^2$  metric, such that the Ricci curvature tensor of M is uniformly bounded below, and let  $\sigma(t,x): T_xM \to T_xM$  be a twice differentiable linear mapping on each tangent space. Let  $X_t$  stand for the solution of the stochastic differential equation

$$
dX_t = \sqrt{2}\,\sigma(t, X_t)\,dB_t \qquad (0 \le t < T). \tag{1.7}
$$

Then the following two statements are equivalent:

(i)  $\mu = \mu_t(dx)$  is a weak solution of the linear (diffusion) partial differential equation

$$
\partial_t \mu = \nabla_x \cdot \Big( (\sigma \sigma^*) \nabla_x \mu \Big)
$$

on  $M \times [0, T)$ , where  $\sigma^*$  stands for the transpose of  $\sigma$ ;

(ii)  $\mu_t = \text{law}(X_t)$  for all  $t \in [0, T)$ , where  $X_t$  solves (1.7).

**Example 1.5.** In  $\mathbb{R}^n$ , the solution of the heat equation with initial datum  $\delta_0$  is the law of  $X_t = \sqrt{2} B_t$  (Brownian motion sped up by a factor  $\sqrt{2}$ ).

Remark 1.6. Actually, there is a finer criterion for the diffusion equation to hold true: it is sufficient that the Ricci curvature at point  $x$  be bounded below by  $-Cd(x_0, x)^2 g_x$  as  $x \to \infty$ , where  $g_x$  is the metric at point x and  $x_0$  is an arbitrary reference point. The exponent 2 here is sharp.

**Exercise 1.7.** Let  $M$  be a smooth compact manifold, equipped with its standard reference volume, and let  $\rho_0$  be a smooth positive probability density on M. Let  $(\rho_t)_{t\geq 0}$  be the solution of the heat equation

$$
\partial_t \rho = \Delta \rho.
$$

Use  $(\rho_t)$  to construct a *deterministic* coupling of  $\rho_0$  and  $\rho_1$ .

Hint: Rewrite the heat equation in the form of an equation of conservation of mass.

#### Appendix: Moser's coupling

In this Appendix I shall promote Moser's technique for coupling smooth positive probability measures; it is simple, elegant and powerful, and plays a prominent role in geometry. It is not limited to compact manifolds, but does require assumptions about the behavior at infinity.

Let  $M$  be a smooth *n*-dimensional Riemannian manifold, equipped with a reference probability measure  $\nu(dx) = e^{-V(x)} \text{vol}(dx)$ , where

 $V \in C^1(M)$ . Let  $\mu_0 = \rho_0 \nu$ ,  $\mu_1 = \rho_1 \nu$  be two probability measures on M; assume for simplicity that  $\rho_0$ ,  $\rho_1$  are bounded below by a constant  $K > 0$ . Further assume that  $\rho_0$  and  $\rho_1$  are locally Lipschitz, and that the equation

$$
(\Delta - \nabla V \cdot \nabla) u = \rho_0 - \rho_1
$$

can be solved for some  $u \in C^{1,1}_{loc}(M)$  (that is,  $\nabla u$  is locally Lipschitz). Then, define a locally Lipschitz vector field

$$
\xi(t,x) = \frac{\nabla u(x)}{(1-t)\,\rho_0(x) + t\,\rho_1(x)},
$$

with associated flow  $(T_t(x))_{0 \le t \le 1}$ , and a family  $(\mu_t)_{0 \le t \le 1}$  of probability measures by

$$
\mu_t = (1 - t) \mu_0 + t \mu_1.
$$

It is easy to check that

$$
\partial_t \mu = (\rho_1 - \rho_0) \nu,
$$

$$
\nabla \cdot (\mu_t \xi(t, \cdot)) = \nabla \cdot (\nabla u \, e^{-V} \operatorname{vol}) = e^{-V} (\Delta u - \nabla V \cdot \nabla u) \operatorname{vol} = (\rho_0 - \rho_1) \nu.
$$

So  $\mu_t$  satisfies the formula of conservation of mass, therefore  $\mu_t =$  $(T_t)_\#\mu_0$ . In particular,  $T_1$  pushes  $\mu_0$  forward to  $\mu_1$ .

In the case when M is compact and  $V = 0$ , the above construction works if  $\rho_0$  and  $\rho_1$  are Lipschitz continuous and positive. Indeed, the solution u of  $\Delta u = \rho_0 - \rho_1$  will be of class  $C^{2,\alpha}$  for all  $\alpha \in (0,1)$ , and in particular  $\nabla u$  will be of class  $C^1$  (in fact  $C^{1,\alpha}$ ). In more general situations, things might depend on the regularity of  $V$ , and its behavior at infinity.

#### Bibliographical notes

An excellent general reference book for the "classical theory" of couplings is the monograph by Thorisson [781]. There one can find an exhaustive treatment of classical couplings of Markov chains or stochastic processes, such as  $\varepsilon$ -coupling, shift-coupling, Ornstein coupling. The classical theory of optimal couplings is addressed in the two volumes by Rachev and Rüschendorf [696]. This includes in particular the theory of optimal coupling on the real line with a convex cost function, which can be treated in a simple and direct manner [696, Section 3.1].

(In [814], for the sake of consistency of the presentation I treated optimal coupling on  $\mathbb R$  as a particular case of optimal coupling on  $\mathbb R^n$ , however this has the drawback to involve subtle arguments.)

The Knothe–Rosenblatt coupling was introduced in 1952 by Rosenblatt [709], who suggested that it might be useful to "normalize" statistical data before applying a statistical test. In 1957, Knothe [523] rediscovered it for applications to the theory of convex bodies. It is quite likely that other people have discovered this coupling independently. An infinite-dimensional generalization was studied by Bogachev, Kolesnikov and Medvedev [134, 135].

FKG inequalities were introduced in [375], and have since then played a crucial role in statistical mechanics. Holley's proof by coupling appears in [477]. Recently, Caffarelli [188] has revisited the subject in connection with optimal transport.

It was in 1965 that Moser proved his coupling theorem, for smooth compact manifolds without boundaries [640]; noncompact manifolds were later considered by Greene and Shiohama [432]. Moser himself also worked with Dacorogna on the more delicate case where the domain is an open set with boundary, and the transport is required to fix the boundary [270].

Strassen's duality theorem is discussed e.g. in [814, Section 1.4].

The gluing lemma is due to several authors, starting with Vorob'ev in 1962 for finite sets. The modern formulation seems to have emerged around 1980, independently by Berkes and Philipp [101], Kallenberg, Thorisson, and maybe others. Refinements were discussed e.g. by de Acosta [273, Theorem A.1] (for marginals indexed by an arbitrary set) or Thorisson [781, Theorem 5.1]; see also the bibliographic comments in [317, p. 20]. For a proof of the statement in these notes, it is sufficient to consult Dudley [317, Theorem 1.1.10], or [814, Lemma 7.6]. A comment about terminology: I like the word "gluing" which gives a good indication of the construction, but many authors just talk about "composition" of plans.

The formula of change of variables for  $C^1$  or Lipschitz change of variables can be found in many textbooks, see e.g. Evans and Gariepy [331, Chapter 3]. The generalization to approximately differentiable maps is explained in Ambrosio, Gigli and Savaré [30, Section 5.5]. Such a generality is interesting in the context of optimal transportation, where changes of variables are often very rough (say  $BV$ , which means of bounded variation). In that context however, there is more structure: For instance, changes of variables will typically be given by the gradient of a convex function in  $\mathbb{R}^n$ , and on such a map one knows slightly more than on a general BV function, because convex functions are twice differentiable almost everywhere (Theorem 14.25 later in these notes). McCann [614] used this property to prove, by slightly more elementary means, the change of variables formula for a gradient of convex function; the proof is reproduced in [814, Theorem 4.8]. It was later generalized by Cordero-Erausquin, McCann and Schmuckenschläger to Riemannian manifolds [246], a case which again can be treated either as part of the general theory of  $BV$  changes of variables, or with the help of almost everywhere second derivatives of semiconcave functions.

The formula of conservation of mass is also called the method of characteristics for linear transport equations, and is described in a number of textbooks in partial differential equations, at least when the driving vector field is Lipschitz, see for instance Evans [327, Section 3.2]. An essentially equivalent statement is proven in [814, Theorem 5.34]. Treating vector fields that are only assumed to be locally Lipschitz is not so easy: see Ambrosio, Gigli and Savaré [30, Section 8.1].

The Lipschitz condition can be relaxed into a Sobolev or even a BV condition, but then the flow is determined only almost everywhere, and this becomes an extremely subtle problem, which has been studied by many authors since the pioneering work of DiPerna and Lions [304] at the beginning of the nineties. See Ambrosio [21] for recent progress and references. The version which is stated in these notes, with no regularity assumption, is due to Ambrosio and carefully proved in [30, Section 8.1]. In spite of its appealing and relatively natural character (especially in a probabilistic perspective), this is a very recent research result. Note that, if  $T_t(x)$  is not uniquely determined by x, then the solution to the conservation equation starting with a given probability measure might admit several solutions.

A recent work by Lisini [565] addresses a generalization of the formula of conservation of mass in the setting of general Polish spaces. Of course, without any regularity assumption on the space it is impossible to speak of vector fields and partial differential equations; but it is still possible to consider paths in the space of probability measures, and random curves. Lisini's results are most naturally expressed in the language of optimal transport distances; see the bibliographical notes for Chapter 7.

The diffusion formula can be obtained as a simple consequence of the Itô formula, which in the Euclidean setting can be found in any textbook on stochastic differential equations, e.g. [658]. It was recently the hundredth anniversary of the discovery of the diffusion formula by Einstein [322]; or rather rediscovery, since Bachelier already had obtained the main results at the turn of the twentieth century [251, 739]. (Some information about Bachelier's life can be found online at sjepg.univ-fcomte.fr/sjepgbis/libre/bachelier/page01/page01.htm.) Fascinating tales about the Brownian motion can be read in Nelson's unconventional book [648], especially Chapters 1–4. For the much more subtle Riemannian setting, one may consult Stroock [759], Hsu [483] and the references therein.

The Brownian motion on a smooth Riemannian manifold is always well-defined, even if the manifold has a wild behavior at infinity (the construction of the Brownian motion is purely local); but in the absence of a good control on the Ricci curvature, there might be several heat kernels, and the heat equation might not be uniquely solvable for a given initial datum. This corresponds to the possibility of a blow-up of the Brownian motion (i.e. the Brownian motion escapes to infinity) in finite time. All this was explained to me by Thalmaier. The sharp criterion Ric<sub>x</sub>  $\geq -C(1+d(x_0,x)^2) g_x$  for avoiding blow-up of the heat equation is based on comparison theorems for Laplace operators. In the version stated here it is due to Ichihara [486]; see also the book by Hackenbroch and Thalmaier [454, p. 544]. Nonexplosion criteria based on curvature have been studied by Gaffney, Yau, Hsu, Karp and Li, Davies, Takeda, Sturm, and Grigor'yan; for a detailed exposition, and many explanations, the reader can consult the survey by Grigor'yan [434, Section 9].