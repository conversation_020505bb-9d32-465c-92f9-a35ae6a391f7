{"table_of_contents": [{"title": "Bibliographical notes", "heading_level": null, "page_id": 0, "polygon": [[233.25, 194.90625], [358.59375, 194.90625], [358.59375, 205.34765625], [233.25, 205.34765625]]}, {"title": "638 22 Concentration inequalities", "heading_level": null, "page_id": 4, "polygon": [[133.5, 26.25], [285.75, 26.25], [285.75, 35.795654296875], [133.5, 35.795654296875]]}, {"title": "640 22 Concentration inequalities", "heading_level": null, "page_id": 6, "polygon": [[133.5, 26.25], [285.75, 26.25], [285.75, 35.62646484375], [133.5, 35.62646484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 40], ["Text", 6], ["Equation", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1900, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 46], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 39], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 39], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 48], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["Line", 39], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 42], ["TextInlineMath", 3], ["SectionHeader", 1], ["Text", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 50], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 40], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 60], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-38"}