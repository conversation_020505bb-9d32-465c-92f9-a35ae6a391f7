634 22 Concentration inequalities

$$
g(x) - g(y) = [g(x) - g(z)] + [g(z) - g(y)]
$$
  
\n
$$
\leq (L - \eta) d(x, z) + L d(z, y)
$$
  
\n
$$
= L d(x, y) - \eta d(x, z)
$$
  
\n
$$
\leq (L - \eta \frac{r}{R}) d(x, y),
$$

which proves the lemma. □

# Bibliographical notes

Most of the literature described below is reviewed with more detail in the synthesis works of <PERSON><PERSON> [542, 543, 546]. Selected applications of the concentration of measure to various parts of mathematics (Banach space theory, fine study of Brownian motion, combinatorics, percolation, spin glass systems, random matrices, etc.) are briefly developed in [546, Chapters 3 and 8]. The role of  $T_p$  inequalities in that theory is discussed in [546, Chapter 6], [41, Chapter 8], and [427]. One may also take a look at <PERSON><PERSON>'s Saint-Flour lecture notes [597].

<PERSON><PERSON><PERSON> is often quoted as the founding father of concentration theory. His work might have been forgotten without the determination of <PERSON><PERSON> to make it known. The modern period of concentration of measure starts with a work by <PERSON><PERSON> himself on the so-called Dvoretzy theorem [634].

The <PERSON><PERSON>vy–Gromov isoperimetric inequality  $[435]$  is a way to get sharp concentration estimates from Ricci curvature bounds. Gromov has further worked on the links between Ricci curvature and concentration, see his very influential book [438], especially Chapter  $3\frac{1}{2}$  therein. Also Talagrand made decisive contributions to the theory of concentration of measure, mainly in product spaces, see in particular [772, 773]. Dembo [294] showed how to recover several of Talagrand's results in an elegant way by means of information-theoretical inequalities.

 $T_p$  inequalities have been studied for themselves at least since the beginning of the nineties [695]; the Csiszár–Kullback–Pinsker inequality can be considered as their ancestor from the sixties (see below). Sometimes it is useful to consider more general transport inequalities of the form  $C(\mu,\nu) \leq H_{\nu}(\mu)$ , or even  $C(\mu,\widetilde{\mu}) \leq H_{\nu}(\mu) + H_{\nu}(\widetilde{\mu})$  (recall Theorem 22.30 and its proof).

It is easy to show that transport inequalities are stable under weak convergence [305, Lemma 2.2]. They are also stable under pushforward [425].

Proposition 22.3 was studied by Rachev [695] and Bobkov and Götze [128], in the cases  $p = 1$  and  $p = 2$ . These duality formulas were later systematically exploited by Bobkov, Gentil and Ledoux [127, 131]. The Legendre reformulation of the  $H$  functional can be found in many sources (for instance [577, Appendix B] when  $\mathcal X$  is compact).

The tensorization argument used in Proposition 22.5 goes back to Marton [595]. The measurable selection theorem used in the construction of the coupling  $\pi$  can be found e.g. in [288]. As for Lemma 22.8, it is as old as information theory, since Shannon [746] used it to motivate the introduction of entropy in this context. After Marton's work, this tensorization technique has been adapted to various situations, such as weakly dependent Markov chains; see [124, 140, 305, 596, 621, 703, 730, 841]. Relations with the so-called Dobrushin(–Shlosman) mixing condition appear in some of these works, for instance [596, 841]; in fact, the original version of the mixing condition [308, 311] was formulated in terms of optimal transport!

It is also Marton [595] who introduced the simple argument by which  $T_p$  inequalities lead to concentration inequalities (implication (i)  $\Rightarrow$  (iii) in Theorem 22.10), and which has since then been reproduced in nearly all introductions to the subject. She used it mainly with the so-called Hamming distance:  $d((x_i)_{1 \leq i \leq n}, (y_i)_{1 \leq i \leq n}) = \sum 1_{x_i \neq y_i}$ .

There are alternative functional approaches to the concentration of measure: via logarithmic Sobolev inequalities [546, Chapter 5] [41, Chapter 7]; and via Brunn–Minkowski, Prékopa–Leindler, or isoperimetric inequalities  $[546, Chapter 2]$ ; one may also consult  $[547]$  for a short review on these various approaches. For instance, (19.27) immediately implies

$$
\nu[A^r] \ge 1 - \frac{e^{-\frac{Kr^2}{4}}}{\nu[A]}.
$$

This kind of inequality goes back to Gromov and V. Milman [440], who also studied concentration from Poincaré inequalities (as Borovkov and Utev [146] did independently at about the same time). The relations between Poincaré inequalities and concentration are reviewed by E. Milman [633], who also shows that a weak concentration estimate, together with the  $CD(0,\infty)$  criterion, implies Poincaré (and even the stronger Cheeger isoperimetric inequality). The tight links between all these functional inequalities show that these various strategies are in some sense related.

First introduced by Herbst, the Laplace transform became an important tool in some of these developments, especially in the hands of Ledoux and coworkers — as can be seen in many places in  $[546]$ .

Theorem 22.10 has been obtained by patching together results due to Bobkov and Götze [128], Djellout, Guillin and Wu [305], and Bolley and myself [140], together with a few arguments from folklore. There is an alternative proof of (ix)  $\Rightarrow$  (i) based on the following fact, wellknown to specialists: Let  $X$  be a centered real random variable such that  $\mathbb{E} e^{X^2} < \infty$ , then the Laplace transform of X is bounded above by a Gaussian Laplace transform.

The Csiszár–Kullback–Pinsker (CKP) inequality (22.25) was found independently by Pinsker [682], Kullback [534] and Csiszár [253]. The popular short proof by Pinsker is based on an obscure inequality on the real line [41, ineq. (8.4)]. The approach used in Remark 22.12 is taken from [140]; it takes inspiration from an argument which I heard in a graduate course by Talagrand. In Exercise 29.22 I shall propose yet another line of reasoning which is closer to Csiszár's original argument, and which was the basis to the generalization in the context of quantum physics [473]. (This reference was pointed out to me by Seiringer.)

Weighted CKP inequalities such as (22.16) were introduced in my paper with Bolley [140]; then Gozlan and Léonard [430] studied similar inequalities from the point of view of the theory of large deviations. More information can be found in Gozlan's PhD thesis [427]. Different kinds of generalizations of the CKP inequality appear in [140, 669, 797, 842], together with applications.

Talagrand [774] proved Theorem 22.14 when  $\nu$  is the Gaussian measure in  $\mathbb{R}^n$ , using a change of variables in the one-dimensional case, and then a tensorization argument (Corollary 22.6). This strategy was developed by Blower [122] who proved Theorem 22.14 when  $M = \mathbb{R}^n$ ,  $\nu(dx) = e^{-V(x)} dx$ ,  $\nabla^2 V \geq K > 0$ ; see also Cordero-Erausquin [242]. Generalizations to nonquadratic costs appear in [245]. Also Kolesnikov [526] made systematic use of this approach in infinitedimensional situations and for various classes of inequalities. More recently, the same strategy was used by Barthe [73] to recover the modified transport inequalities for the exponential measure on the half-line (a particular case of Theorem 22.25).

Otto and I [671] found an alternative approach to Theorem 22.14, via the HWI inequality (which at the time of [671] had been established only in  $\mathbb{R}^n$ ). The proof which I have used in this chapter is the same as the proof in [671], modulo the extension of the HWI inequality to general Riemannian manifolds.

There are several other schemes of proof for Theorem 22.14. One consists in combining Theorems 21.2 and 22.17(i). When  $M = \mathbb{R}^n$ , there is an argument based on Caffarelli's log concave perturbation theorem [188] (exercise). Yet another proof has been given by Bobkov and Ledoux [131], based on the Brunn–Minkowski inequality, or its functional counterpart, the Prékopa–Leindler inequality (in this work there are interesting extensions to cases where the convexity assumptions are not the standard ones and the cost might not be quadratic). Bobkov and Ledoux only worked in  $\mathbb{R}^n$ , but it is quite possible that their strategy can be extended to genuinely Riemannian situations, by means of the "Riemannian" Prékopa–Leindler inequality stated in Theorem 19.16.

Theorem 22.17 (log Sobolev implies  $T_2$  implies Poincaré) was first proven by Otto and myself [671]; the Otto calculus had first been used to get an idea of the result. Our proof relied on a heat semigroup argument, which will be explained later in Chapter 25. The "dual" strategy which I have used in this chapter, based on the Hamilton– Jacobi semigroup, is due to Bobkov, Gentil and Ledoux [127]. In [671] it was assumed that the Ricci curvature of the manifold M is bounded below, and this assumption was removed in [127]. This is because the proof in [671] used a heat semigroup, which has infinite speed of propagation and is influenced by the asymptotic behavior of the manifold, while the argument in [127] was based on the Hopf–Lax semigroup, for which there is only finite speed of propagation (if the initial datum is bounded). The methods in [127] were pushed in [412] to treat nonquadratic cost functions. Infimum convolutions in the style of the Hopf–Lax formula also play a role in [131, 132], in relation with logarithmic or plain Sobolev inequalities. Much later, Gozlan [429] found a third proof of Theorem 22.17, based on Theorem 22.22 (which is itself based on Sanov's theorem).

Various generalizations of the proof in [671] were studied by Cattiaux and Guillin [219]; see the bibliographical notes of Chapter 25 for more details.

## 638 22 Concentration inequalities

The proof of [127] was adapted by Lott and myself [579] to compact length spaces  $(\mathcal{X}, d)$  equipped with a reference measure  $\nu$  that is locally doubling and satisfies a local Poincaré inequality; see Theorem  $30.28$ in the last chapter of these notes. In fact the proof of Theorem 22.17, as I have written it, is essentially a copy–paste from [579]. (A detailed proof of Proposition 22.16 is also provided there.) Then Gozlan [429] relaxed these assumptions even further.

If M is a compact Riemannian manifold, then the normalized volume measure on M satisfies a Talagrand  $(T_2)$  inequality: This results from the existence of a logarithmic Sobolev inequality [710] and Theorem 22.17. Moreover, by  $[671,$  Theorem 4, the diameter of M can be bounded in terms of the constant in the Talagrand inequality, the dimension of M and a lower bound on the Ricci curvature, just as in (21.21) (where now  $\lambda$  stands for the constant in the Talagrand inequality). (The same bound certainly holds true even if  $M$  is not a priori assumed to be compact, but this was not explicitly checked in [671].) There is an analogous result where Talagrand inequality is replaced by logarithmic Sobolev inequality [544, 727].

The remarkable result according to which dimension free Gaussian concentration bounds are *equivalent* to  $T_2$  inequality (Theorem 22.22) is due to Gozlan [429]; the proof of (iii)  $\Rightarrow$  (i) in Theorem 22.22 is extracted from this paper. Gozlan's argument relies on Sanov's theorem in large deviation theory [296, Theorem 6.2.10]; this classical result states that the rate of deviation of the empirical measure of independent, identically distributed samples is the (Kullback) information with respect to their common law; in other words, under adequate conditions,

$$
-\frac{1}{N}\log \nu^{\otimes N}[\widehat{\mu}_x^N \in A] \simeq \inf \Big\{H_{\nu}(\mu); \ \mu \in A\Big\}.
$$

A simple proof of the particular estimate (22.47) is provided in the Appendix of [429]. The observation that Talagrand inequalities and Sanov's theorem match well goes back at least to [139]; but Gozlan's theorem uses this ingredient with a quite new twist.

Varadarajan's theorem (law of large numbers for empirical measures) was already used in the proof of Theorem 5.10; it is proven for instance in [318, Theorem 11.4.1]. It is anyway implied by Sanov's theorem.

Theorem 22.10 shows that  $T_1$  is quite well understood, but many questions remain open about the more interesting  $T_2$  inequality. One of the most natural is the following: given a probability measure  $\nu$ 

satisfying  $T_2$ , and a bounded function v, does  $e^{-v}\nu/(\int e^{-v} dv)$  also satisfies a  $T_2$  inequality? For the moment, the only partial result in this direction is (22.29). This formula was first established by Blower [122] and later recovered with simpler methods by Bolley and myself [140].

If one considers probability measures of the form  $e^{-V(x)} dx$  with  $V(x)$  behaving like  $|x|^\beta$  for large  $|x|$ , then the critical exponents for concentration-type inequalities are the same as we already discussed for isoperimetric-type inequalities: If  $\beta \geq 2$  there is the  $T_2$  inequality, while for  $\beta = 1$  there is the transport inequality with linear-quadratic cost function. What happens for intermediate values of  $\beta$  has been investigated by Gentil, Guillin and Miclo in [410], by means of modified logarithmic Sobolev inequalities in the style of Bobkov and Ledoux [130]. Exponents  $\beta > 2$  have also been considered in [131].

It was shown in [671] that (Talagrand)  $\Rightarrow$  (log Sobolev) in  $\mathbb{R}^n$ , if the reference measure  $\nu$  is log concave (with respect to the Lebesgue measure). It was natural to conjecture that the same argument would work under an assumption of nonnegative curvature (say  $CD(0, \infty)$ ); Theorem 22.21 shows that such is indeed the case.

It is only recently that Cattiaux and Guillin [219] produced a counterexample on the real line, showing that the  $T_2$  inequality does not necessarily imply a log Sobolev inequality. Their counterexample takes the form  $d\nu = e^{-V} dx$ , where V oscillates rather wildly at infinity, in particular  $V''$  is not bounded below. More precisely, their potential looks like  $V(x) = |x|^3 + 3x^2 \sin^2 x + |x|^\beta$  as  $x \to +\infty$ ; then  $\nu$  satisfies a logarithmic Sobolev inequality only if  $\beta \geq 5/2$ , but a  $T_2$  inequality as soon as  $\beta > 2$ . Counterexamples with V'' bounded below have still not yet been found.

Even more recently, Gozlan [425, 426, 428] exhibited a characterization of  $T_2$  and other transport inequalities on  $\mathbb{R}$ , for certain classes of measures. He even identified situations where it is useful to deduce logarithmic Sobolev inequalities from  $T_2$  inequalities. Gentil, Guillin and Miclo [411] considered transport inequalities on  $\mathbb R$  for log-concave probability measures. This is a rather active area of research. For instance, consider a transport inequality of the form  $C(\mu,\nu) \leq H_{\nu}(\mu)$ , where the cost function is  $c(x,y) = \theta(a|x-y|), a > 0$ , and  $\theta : \mathbb{R}_+ \to \mathbb{R}_+$  is convex with  $\theta(r) = r^2$  for  $0 \le r \le 1$ . If  $\nu(dx) = e^{-V} dx$  with  $V'' = o(V'^2)$  at infinity and  $\limsup_{x\to+\infty} \theta'(\lambda x)/V'(x) < +\infty$  for some  $\lambda > 0$ , then there exists  $a > 0$  such that the inequality holds true.

## 640 22 Concentration inequalities

Theorem 22.14 admits an almost obvious generalization: if  $\mathcal F$  is uniformly K-displacement convex and has a minimum at  $\nu$ , then

$$
\frac{KW_2(\mu,\nu)^2}{2} \le \mathcal{F}(\mu) - \mathcal{F}(\nu). \tag{22.84}
$$

Such inequalities have been studied in [213, 671] and have proven useful in the study of certain partial differential equations: see e.g. [213] (various generalizations of the inequalities considered there appear in [245, 412]). In Section 5 of [213], (22.84) is combined with the HWI inequality and the convergence of the functional  $\mathcal{F}$ , to deduce convergence in total variation. By the way, this is one of the rare applications in finite (fixed) dimension that I know where a  $T_2$ -type inequality has a real advantage on a  $T_1$ -type inequality.

Optimal transport inequalities in infinite dimension have started to receive a lot of attention recently, for instance on the Wiener space. A major technical difficulty is that the natural distance in this problem, the so-called Cameron–Martin distance, takes the value  $+\infty$  "most of the time". (So it is not a real distance, but rather a pseudo-distance.) Gentil [408, Section 5.8] established the  $T_2$  inequality for the Wiener measure by using the logarithmic Sobolev inequality on the Wiener space, and adapting the proof of Theorem 22.17(i) to that setting. Feyel and Ustünel [359] on the one hand, and Djellout, Guillin and Wu [305, Section 6] on the other, suggested a more direct approach based on Girsanov's formula. Interestingly enough, the  $T_2$  inequality on the Wiener space implies the  $T_2$  inequality on the Gaussian space, just by "projection" under the map  $(x_t)_{0 \le t \le 1} \to x_1$ ; this gives another proof of Talagrand's original inequality (with the optimal constant) for the Gaussian measure. There are closely related works by Wu and Zhang [843, 844]. Also F.-Y. Wang [830, 831] studied another kind of Talagrand inequality on the path space over an arbitrary Riemannian manifold.

In his recent PhD thesis, Shao [748] studied  $T_2$  inequalities on the path space and loop space constructed over a compact Lie group G. (The path space is equipped with the Wiener measure over  $G$ .) Together with Fang [341], he adapted the strategy based on the Girsanov formula, to get a  $T_2$  inequality on the path space, and also on the path space over the loop space; then by reduction he gets a  $T_2$  inequality on the loop space (equipped with a measure associated with the Brownian motion on loop space). This approach however only seems to give results when the loop space is equipped with the topology of uniform convergence,

not with the more natural Cameron–Martin distance. I refer to [341] for more explanations.

Fang and Shao [340] also extended Theorem 22.17 (Logarithmic Sobolev implies Talagrand inequality) to an infinite-dimensional setting, via the study of the Hamilton–Jacobi semigroup in infinite dimension. Thanks to known results about logarithmic Sobolev inequalities on loop spaces (studied by Driver, Lohrentz and others), they recover a  $T_2$  inequality on the loop space, now for the Cameron–Martin distance. The technical core of these results is the analysis of the Hamilton– Jacobi for semi-distances in infinite dimension, performed in [749].

Very recently, Fang and Shao [339] used Talagrand inequalities to obtain results of unique existence of optimal transport in the Wiener space over a Lie group, when the target measure  $\nu$  is the Wiener measure and the source measure  $\mu$  satisfies  $H_{\nu}(\mu) < +\infty$ . In the standard (Gaussian) Wiener space, Feyel and Ustünel [359] have solved the same problem in more generality, but so far their results have not been extended outside the Gaussian setting.

A quite different direction of generalization is in the field of free probability, where analogs of the Talagrand inequality have been established by various authors [118, 474, 475].

The equivalence between Poincaré inequalities and modified transport inequalities, as expressed in Theorem 22.25, has a long history. Talagrand [770] had identified concentration properties satisfied by the exponential measure, or a product of exponential measures. He established the following precise version of (22.67):

$$
\nu^{\otimes N} \big[ A + 6 \sqrt{r} B_1^{d_2} + 9 r B_1^{d_1} \big] \ge 1 - \frac{e^{-r}}{\nu^{\otimes N} [A]}.
$$

A proof can be found in [546, Theorem 4.16]. It is also Talagrand who noticed that concentration inequalities for the product exponential measure are in some sense stronger than concentration inequalities for the Gaussian measure (Remark 22.34 and Example 22.36, which I copied from [546]). Then Maurey [607] found a simple approach to concentration inequalities for the product exponential measure. Later, Talagrand [774] made the connection with transport inequalities for the quadratic-linear cost. Bobkov and Ledoux [130] introduced modified logarithmic Sobolev inequalities, and showed their equivalence with Poincaré inequalities. (The proof of (i)  $\Rightarrow$  (ii) is copied almost verbatim from [130].) Very recently, alternative methods based on Lyapunov functionals have been developed to handle these inequalities in an elementary way [447].

Bobkov and Ledoux also showed how to recover concentration inequalities directly from their modified logarithmic Sobolev inequalities, showing in some sense that the concentration properties of the exponential measure were shared by all measures satisfying a Poincaré inequality. Finally, Bobkov, Gentil and Ledoux [127] understood how to deduce quadratic-linear transport inequalities from modified logarithmic Sobolev inequalities, thanks to the Hamilton–Jacobi semigroup. The proof of the direct implication in Theorem 22.28 is just an expanded version of the arguments suggested in [127]; the proof of the converse follows Gozlan [429].

In the particular case when  $\nu(dx) = e^{-|x|} dx$  on  $\mathbb{R}_+$ , there are simpler proofs of Theorem 22.25, also with improved constants; see for instance the above-mentioned works by Talagrand or Ledoux, or a recent remark by Barthe [73]. One may also note that deviation estimates with bounds like  $\exp(-c \min(t, t^2))$  for sums of independent real variables go back to the elementary Bernstein inequalities (see [96] or [775, Theorem A.2.1]).

The treatment of dimension-dependent Talagrand-type inequalities in the last section is inspired from a joint work with Lott [578]. That topic had been addressed before, with different tools, by Gentil [409]; it would be interesting to compare precisely his results with the ones in this chapter. I shall also mention another dimension-dependent inequality in Remark 24.14.

Theorem 22.46 (behavior of solutions of Hamilton–Jacobi equations) has been obtained by generalizing the proof of Proposition 22.16 as it appears in [579]. When  $L'(\infty) = +\infty$ , the proof is basically the same, while there are a few additional technical difficulties if  $L'(\infty) < +\infty$ . In fact Proposition 22.16 was established in [579] in a more general context, namely when M is a finite-dimensional Alexandrov spaces with (sectional) curvature locally bounded below. The same extension probably holds for Theorem 22.46, although part (vii) would require a bit more thinking because the inequalities defining Alexandrov spaces are in terms of the squared distance, not the distance.

The study of Hamilton–Jacobi equations is an old topic (see the reference texts [68, 199, 327, 558] and the many references therein); so I do not exclude that Theorem 22.46 might be found somewhere in the literature, maybe in disguised form. Bobkov and Ledoux recently established closely related results [132, Lemma A] for the quadratic Hamilton–Jacobi equation in a finite-dimensional Banach space.

I shall conclude by listing some further applications of  $T_p$  inequalities which I did not previously mention.

Relations of  $T_p$  inequalities with the so-called slicing problem are discussed in [623].

These inequalities are also useful to study the propagation of chaos or the mean behavior of particle systems [221, 590].

As already noticed before, the functional  $H_{\nu}$  appears in Sanov's theorem as the rate function for the deviations of the empirical mean of independent samples; this explains why  $T_p$  inequalities are handy tools for a quantitative study of concentration of the empirical measure associated with certain particle systems [139]. The links with large deviation theory were further explored in [355, 429, 430, 448]. If one is interested in the concentration of time averages, then one should replace the Kullback information  $H_{\nu}$  by the Fisher information  $I_{\nu}$ , as was understood by Donsker and Varadhan  $[313]$ <sup>1</sup>. As a matter of fact, Guillin, Léonard, Wu and Yao [448] have established that the functional inequality

$$
\alpha\big(W_1(\mu,\nu)\big) \leq I_{\nu}(\mu),
$$

where  $\alpha$  is an increasing function,  $\alpha(0) = 0$ , is equivalent to the concentration inequality

$$
\mathbb{P}\left[\frac{1}{t}\int_0^t \varphi(X_s) ds > \int \varphi d\nu + \varepsilon\right] \le \left\|\frac{d\mu}{d\nu}\right\|_{L^2(\nu)} e^{-t\alpha\left(\frac{\varepsilon}{\|\varphi\|_{\mathrm{Lip}}}\right)},
$$

where  $(X_s)_{s\geq 0}$  is the symmetric diffusion process with invariant measure  $\nu$ ,  $\mu = \text{law}(X_0)$ , and  $\varphi$  is an arbitrary Lipschitz function. (Compare with Theorem  $22.10(v)$ .)

<sup>1</sup> A related remark, which I learnt from Ben Arous, is that the logarithmic Sobolev inequality compares the rate functions of two large deviation principles, one for the empirical measure of independent samples and the other one for the empirical time-averages.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.