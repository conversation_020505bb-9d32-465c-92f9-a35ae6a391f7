$$
R_1 + R_2 < I(X_1, X_2; Y) \tag{14.58}
$$

for some product distribution  $p_1(x_1)p_2(x_2)$  on  $\mathcal{X}_1 \times \mathcal{X}_2$ .

Before we prove that this is the capacity region of the multiple access channel, let us consider a few examples of multiple access channels:

Example 14.3.1 (Independent binary symmetric channels): Assume that we have two independent binary symmetric channels, one from sender 1 and the other from sender 2, as shown in Figure 14.9.

In this case, it is obvious from the results of Chapter 8 that we can send at rate  $1 - H(p_1)$  over the first channel and at rate  $1 - H(p_2)$  over the second channel. Since the channels are independent, there is no interference between the senders. The capacity region in this case is shown in Figure 14.10.

**Example 14.3.2** (Binary multiplier channel): Consider a multiple access channel with binary inputs and output

$$
Y = X_1 X_2 \tag{14.59}
$$

Such a channel is called a binary multiplier channel. It is easy to see that by setting  $X_2 = 1$ , we can send at a rate of 1 bit per transmission from sender 1 to the receiver. Similarly, setting  $X_1 = 1$ , we can achieve  $R_2 = 1$ . Clearly, since the output is binary, the combined rates  $R_1 + R_2$  of

Image /page/0/Figure/9 description: The image displays two diagrams illustrating binary input-output relationships. The top diagram shows an input X1 with possible values 0 and 1, connected to an output Y with possible values 0 and 1. The input 0 is connected to output 0, and the input 1 is connected to output 1. Additionally, there are cross-connections: input 0 is also connected to output 1, and input 1 is also connected to output 0. The bottom diagram shows an input X2 with possible values 0 and 1, connected to an output Y with possible values 0' and 1'. Similar to the top diagram, input 0 is connected to output 0', and input 1 is connected to output 1'. Cross-connections are also present: input 0 is connected to output 1', and input 1 is connected to output 0'.

Figure 14.9. Independent binary symmetric channels.

Image /page/1/Figure/1 description: The image displays a graph with R2 on the y-axis and R1 on the x-axis. The origin is labeled with 0. The graph shows a rectangular region. The top boundary of the region is a horizontal line extending from the y-axis to the right. The y-axis is labeled with R2 and the value C2 = 1 - H(p2) is marked on the y-axis. The right boundary of the region is a vertical line extending from the x-axis upwards. The x-axis is labeled with R1 and the value C1 = 1 - H(p1) is marked on the x-axis. The region is bounded by the axes and these two lines, forming a rectangle in the first quadrant.

Figure 14.10. Capacity region for independent BSC's.

sender 1 and sender 2 cannot be more than 1 bit. By timesharing, we can achieve any combination of rates such that  $R_1 + R_2 = 1$ . Hence the capacity region is as shown in Figure 14.11.

**Example 14.3.3** (Binary erasure multiple access channel): This multiple access channel has binary inputs,  $\mathcal{X}_1 = \mathcal{X}_2 = \{0, 1\}$  and a ternary output  $Y = X_1 + X_2$ . There is no ambiguity in  $(X_1, X_2)$  if  $Y = 0$  or  $Y = 2$  is received; but  $Y = 1$  can result from either  $(0, 1)$  or  $(1, 0)$ .

We now examine the achievable rates on the axes. Setting  $X_2 = 0$ , we can send at a rate of 1 bit per transmission from sender 1. Similarly, setting  $X_1 = 0$ , we can send at a rate  $R_2 = 1$ . This gives us two extreme points of the capacity region.

Can we do better? Let us assume that  $R_1 = 1$ , so that the codewords of  $X_1$  must include all possible binary sequences;  $X_1$  would look like a

Image /page/1/Figure/7 description: A graph shows a line segment in the first quadrant. The y-axis is labeled R2 and has a mark at C2=1. The x-axis is labeled R1 and has a mark at C1=1. The line segment connects the point (0, 1) on the y-axis to the point (1, 0) on the x-axis.

Figure 14.11. Capacity region for binary multiplier channel.

Image /page/2/Figure/1 description: This is a diagram illustrating a binary symmetric channel. On the left side, there are two input states labeled '0' and '1'. From the '0' state, there is a direct arrow to an output state labeled '0' with a probability of 1/2, and a diagonal arrow to an output state labeled '?' with a probability of 1/2. From the '1' state, there is a diagonal arrow to the same output state labeled '?' with a probability of 1/2, and a direct arrow to an output state labeled '1' with a probability of 1/2. The output state labeled '?' is positioned in the center, between the '0' and '1' output states.

Figure 14.12. Equivalent single user channel for user 2 of a binary erasure multiple access channel.

Bernoulli( $\frac{1}{2}$ ) process. This acts like noise for the transmission from  $X_2$ . For  $X_2$ , the channel looks like the channel in Figure 14.12.

This is the binary erasure channel of Chapter 8. Recalling the results, the capacity of this channel is  $\frac{1}{2}$  bit per transmission.

Hence when sending at maximum rate 1 for sender 1, we can send an additional  $\frac{1}{2}$  bit from sender 2. Later on, after deriving the capacity region, we can verify that these rates are the best that can be achieved.

The capacity region for a binary erasure channel is illustrated in Figure 14.13.

Image /page/2/Figure/7 description: A graph shows a region in the R1-R2 plane. The R1 axis is horizontal and labeled "R1", with markings at 0, 1/2, and C1=1. The R2 axis is vertical and labeled "R2", with markings at 0, 1/2, and C2=1. The region is a polygon with vertices at (0, 1), (0, 0), (1, 0), (1, 1/2), and approximately (0.5, 1). The top boundary is horizontal from (0, 1) to approximately (0.5, 1). Then it slopes down to (1, 1/2). The right boundary is vertical from (1, 1/2) to (1, 0). The bottom boundary is horizontal from (1, 0) to (0, 0). The left boundary is vertical from (0, 0) to (0, 1).

Figure 14.13. Capacity region for binary erasure multiple access channel.

## 14.3.1 Achievability of the Capacity Region for the Multiple Access Channel

We now prove the achievability of the rate region in Theorem 14.3.1; the proof of the converse will be left until the next section. The proof of achievability is very similar to the proof for the single user channel. We will therefore only emphasize the points at which the proof differs from the single user case. We will begin by proving the achievability of rate pairs that satisfy (14.58) for some fixed product distribution  $p(x_1)p(x_2)$ . In Section 14.3.3, we will extend this to prove that all points in the convex hull of (14.58) are achievable.

**Proof** (Achievability in Theorem 14.3.1): Fix  $p(x_1, x_2) = p_1(x_1)p_2(x_2)$ .

- Codebook generation. Generate  $2^{nR_1}$  independent codewords  $\mathbf{X}_1(i)$ ,  $i \in \{1, 2, \ldots, 2^{nR_1}\}\text{, of length } n$ , generating each element i.i.d.  $\sim \prod_{i=1}^{n} p_1(x_{1i})$ . Similarly, generate  $2^{nR_2}$  independent codewords  $\mathbf{X}_2(j)$ ,  $j \in \{1, 2, ..., 2^{nR_2}\}$ , generating each element i.i.d.  $\sim \prod_{i=1}^{n} p_2(x_{2i})$ . These codewords form the codebook, which is revealed to the senders and the receiver.
- *Encoding.* To send index *i*, sender 1 sends the codeword  $X_1(i)$ . Similarly, to send j, sender 2 sends  $X_2(j)$ .
- Decoding. Let  $A_{\epsilon}^{(n)}$  denote the set of typical  $(\mathbf{x}_1, \mathbf{x}_2, \mathbf{y})$  sequences. The receiver  $Y<sup>n</sup>$  chooses the pair  $(i, j)$  such that

$$
(\mathbf{x}_1(i), \mathbf{x}_2(j), \mathbf{y}) \in A_{\epsilon}^{(n)} \tag{14.60}
$$

If such a pair  $(i, j)$  exists and is unique; otherwise, an error is declared.

Analysis of the probability of error. By the symmetry of the random code construction, the conditional probability of error does not depend on which pair of indices is sent. Thus the conditional probability of error is the same as the unconditional probability of error. So, without loss of generality, we can assume that  $(i, j)$  =  $(1,1)$  was sent.

We have an error if either the correct codewords are not typical with the received sequence or there is a pair of incorrect codewords that are typical with the received sequence. Define the events

$$
E_{ij} = \{ (\mathbf{X}_1(i), \mathbf{X}_2(j), \mathbf{Y}) \in A_{\epsilon}^{(n)} \} .
$$
 (14.61)

Then by the union of events bound,

$$
P_e^{(n)} = P(E_{11}^c \cup \cup_{(i,j)\neq(1,1)} E_{ij})
$$
\n(14.62)

$$
\leq P(E_{11}^c) + \sum_{i \neq 1, j=1} P(E_{i1}) + \sum_{i=1, j \neq 1} P(E_{1j}) + \sum_{i \neq 1, j \neq 1} P(E_{ij}), \quad (14.63)
$$

where  $P$  is the conditional probability given that  $(1, 1)$  was sent. From the AEP,  $P(E_{11}^c) \rightarrow 0$ .

By Theorem 14.2.1 and Theorem 14.2.3, for  $i \neq 1$ , we have

$$
P(E_{i1}) = P((\mathbf{X}_1(i), \mathbf{X}_2(1), \mathbf{Y}) \in A_{\epsilon}^{(n)})
$$
(14.64)

$$
= \sum_{(\mathbf{x}_1, \mathbf{x}_2, \mathbf{y}) \in A_{\epsilon}^{(n)}} p(\mathbf{x}_1) p(\mathbf{x}_2, \mathbf{y})
$$
(14.65)

$$
\leq |A_{\epsilon}^{(n)}| 2^{-n(H(X_1)-\epsilon)} 2^{-n(H(X_2, Y)-\epsilon)} \tag{14.66}
$$

$$
\leq 2^{-n(H(X_1) + H(X_2, Y) - H(X_1, X_2, Y) - 3\epsilon)} \tag{14.67}
$$

$$
=2^{-n(I(X_1; X_2, Y)-3\epsilon)}\tag{14.68}
$$

$$
=2^{-n(I(X_1; Y|X_2)-3\epsilon)},\t(14.69)
$$

since  $X_1$  and  $X_2$  are independent, and therefore  $I(X_1; X_2, Y) = I(X_1; X_2)$  +  $I(X_1; Y|X_2) = I(X_1; Y|X_2).$ 

Similarly, for  $i \neq 1$ ,

$$
P(E_{1i}) \le 2^{-n(I(X_2; Y|X_1) - 3\epsilon)}, \tag{14.70}
$$

and for  $i \neq 1$ ,  $j \neq 1$ ,

$$
P(E_{ij}) \le 2^{-n(I(X_1, X_2; Y) - 4\epsilon)}.
$$
\n(14.71)

It follows that

$$
P_e^{(n)} \le P(E_{11}^c) + 2^{nR_1} 2^{-n(I(X_1; Y|X_2) - 3\epsilon)} + 2^{nR_2} 2^{-n(I(X_2; Y|X_1) - 3\epsilon)} + 2^{n(R_1 + R_2)} 2^{-n(I(X_1, X_2; Y) - 4\epsilon)}.
$$
\n(14.72)

Since  $\epsilon > 0$  is arbitrary, the conditions of the theorem imply that each term tends to 0 as  $n \rightarrow \infty$ .

The above bound shows that the average probability of error, averaged over all choices of codebooks in the random code construction, is arbitrarily small. Hence there exists at least one code  $\mathscr{C}^*$  with arbitrarily small probability of error.

This completes the proof of achievability of the region in (14.58) for a fixed input distribution. Later, in Section 14.3.3, we will show that timesharing allows any  $(R_1, R_2)$  in the convex hull to be achieved, completing the proof of the forward part of the theorem.  $\Box$ 

### 14.3.2 Comments on the Capacity Region for the Multiple Access Channel

We have now proved the achievability of the capacity region of the multiple access channel, which is the closure of the convex hull of the set of points  $(R_1, R_2)$  satisfying

$$
R_1 < I(X_1; Y | X_2), \tag{14.73}
$$

$$
R_2 < I(X_2; Y|X_1), \tag{14.74}
$$

$$
R_1 + R_2 < I(X_1, X_2; Y) \tag{14.75}
$$

for some distribution  $p_1(x_1)p_2(x_2)$  on  $\mathcal{X}_1 \times \mathcal{X}_2$ .

For a particular  $p_1(x_1)p_2(x_2)$ , the region is illustrated in Figure 14.14.

Let us now interpret the corner points in the region. The point A corresponds to the maximum rate achievable from sender 1 to the receiver when sender 2 is not sending any information. This is

$$
\max R_1 = \max_{p_1(x_1)p_2(x_2)} I(X_1; Y|X_2).
$$
 (14.76)

Now for any distribution  $p_1(x_1)p_2(x_2)$ ,

$$
I(X_1; Y|X_2) = \sum_{x_2} p_2(x_2) I(X_1; Y|X_2 = x_2)
$$
\n(14.77)

$$
\leq \max_{x_2} I(X_1; Y | X_2 = x_2), \tag{14.78}
$$

Image /page/5/Figure/14 description: The image displays a two-dimensional plot with the horizontal axis labeled R1 and the vertical axis labeled R2. The plot shows a region bounded by points labeled A, B, C, and D. The origin (0,0) is indicated. The vertical axis has tick marks labeled I(X2; Y) and I(X2; Y|X1). The horizontal axis has tick marks labeled I(X1; Y) and I(X1; Y|X2). The region is a polygon with vertices at approximately (0, I(X2; Y|X1)) for D, (I(X1; Y), I(X2; Y|X1)) for C, a point between C and B with a downward sloping line, a point B where the line becomes vertical, and a point A on the R1 axis at I(X1; Y|X2). The region represents a capacity region for some communication system.

Figure 14.14. Achievable region of multiple access channel for a fixed input distribution.

since the average is less than the maximum. Therefore, the maximum in (14.76) is attained when we set  $X_2 = x_2$ , where  $x_2$  is the value that maximizes the conditional mutual information between  $X_1$  and Y. The distribution of  $X_1$  is chosen to maximize this mutual information. Thus  $X_2$  must facilitate the transmission of  $X_1$  by setting  $X_2 = x_2$ .

The point  $B$  corresponds to the maximum rate at which sender  $2$  can send as long as sender 1 sends at his maximum rate. This is the rate that is obtained if  $X_1$  is considered as noise for the channel from  $X_2$  to Y. In this case, using the results from single user channels,  $X_2$  can send at a rate  $I(X_2; Y)$ . The receiver now knows which  $X_2$  codeword was used and can "subtract" its effect from the channel. We can consider the channel now to be an indexed set of single user channels, where the index is the  $X_2$  symbol used. The  $X_1$  rate achieved in this case is the average mutual information, where the average is over these channels, and each channel occurs as many times as the corresponding  $X_2$  symbol appears in the codewords. Hence the rate achieved is

$$
\sum_{x_2} p(x_2) I(X_1; Y | X_2 = x_2) = I(X_1; Y | X_2).
$$
 (14.79)

The points C and D correspond to B and A respectively with the roles of the senders reversed.

The non-corner points can be achieved by timesharing. Thus, we have given a single user interpretation and justification for the capacity region of a multiple access channel.

The idea of considering other signals as part of the noise, decoding one signal and then "subtracting" it from the received signal is a very useful one. We will come across the same concept again in the capacity calculations for the degraded broadcast channel.

## 14.3.3 Convexity of the Capacity Region of the Multiple Access Channel

We now recast the capacity region of the multiple access channel in order to take into account the operation of taking the convex hull by introducing a new random variable. We begin by proving that the capacity region is convex.

**Theorem 14.3.2:** The capacity region  $\mathscr C$  of a multiple access channel is convex, i.e., if  $(R_1, R_2) \in \mathscr{C}$  and  $(R'_1, R'_2) \in \mathscr{C}$ , then  $(\lambda R_1 + (1-\lambda)R'_1,$  $\lambda R_2+(1-\lambda)R_2'\in\mathscr{C}$  for  $0\leq\lambda\leq1$ .

Proof: The idea is timesharing. Given two sequences of codes at different rates  $\mathbf{R} = (R_1, R_2)$  and  $\mathbf{R}' = (R'_1, R'_2)$ , we can construct a third codebook at a rate  $\lambda \mathbf{R} + (1 - \lambda) \mathbf{R}'$  by using the first codebook for the first  $\lambda n$  symbols and using the second codebook for the last  $(1 - \lambda)n$  symbols. The number of  $X_1$  codewords in the new code is

$$
2^{n\lambda R_1}2^{n(1-\lambda)R_1'} = 2^{n(\lambda R_1 + (1-\lambda)R_1')} \tag{14.80}
$$

and hence the rate of the new code is  $\lambda \mathbf{R} + (1 - \lambda) \mathbf{R}'$ . Since the overall probability of error is less than the sum of the probabilities of error for each of the segments, the probability of error of the new code goes to 0 and the rate is achievable.  $\Box$ 

We will now recast the statement of the capacity region for the multiple access channel using a timesharing random variable Q.

**Theorem 14.3.3:** The set of achievable rates of a discrete memoryless multiple access channel is given by the closure of the set of all  $(R_1, R_2)$ pairs satisfying

$$
R_1 < I(X_1; Y | X_2, Q),
$$

$$
R_2 < I(X_2; Y | X_1, Q),
$$

$$
R_1 + R_2 < I(X_1, X_2; Y | Q) \tag{14.81}
$$

for some choice of the joint distribution  $p(q)p(x,[q)p(x,[q)p(y|x_1,x_2))$ with  $|2| \leq 4$ .

Proof: We will show that every rate pair lying in the region in the theorem is achievable, i.e., it lies in the convex closure of the rate pairs satisfying Theorem 14.3.1. We will also show that every point in the convex closure of the region in Theorem 14.3.1 is also in the region defined in (14.81).  $C = \begin{bmatrix} 1 & 1 & 1 & 1 \\ 0 & 0 & 1 & 1 \end{bmatrix}$  satisfying the inequalities (14.81) of the inequalities (14.81) of the inequalities (14.81) of the inequality of the inequality of the inequality of the inequality of the inequality

 $\frac{1}{\sqrt{1+\epsilon}}$  consider a rate point **iv** satisfying the inequalities  $(1+0.01)$  of the

$$
I(X_1; Y|X_2, Q) = \sum_{k=1}^{m} p(q)I(X_1; Y|X_2, Q = q)
$$
 (14.82)

$$
= \sum_{k=1}^{m} p(q)I(X_1; Y|X_2)_{p_{1q}, p_{2q}}, \qquad (14.83)
$$

where m is the cardinality of the support set of  $Q$ . We can similarly expand the other mutual informations in the same way.

For simplicity in notation, we will consider a rate pair as a vector and denote a pair satisfying the inequalities in  $(14.58)$  for a specific input product distribution  $p_{1q}(x_1)p_{2q}(x_2)$  as  $\mathbf{R}_q$ . Specifically, let  $\mathbf{R}_q = (R_{1q}, R_{2q})$ <br>be a rate pair satisfying

$$
R_{1q} < I(X_1; Y | X_2)_{p_{1q}(x_1)p_{2q}(x_2)},\tag{14.84}
$$

$$
R_{2q} < I(X_2; Y|X_1)_{p_{1q}(x_1)p_{2q}(x_2)},\tag{14.85}
$$

$$
R_{1q} + R_{2q} < I(X_1, X_2; Y)_{p_{1q}(x_1)p_{2q}(x_2)}\,. \tag{14.86}
$$

Then by Theorem 14.3.1,  $\mathbf{R}_{q} = (R_{1q}, R_{2q})$  is achievable.

Then since  **satisfies (14.81), and we can expand the right hand** sides as in (14.83), there exists a set of pairs  $\mathbf{R}_q$  satisfying (14.86) such that

$$
\mathbf{R} = \sum_{q=1}^{m} p(q)\mathbf{R}_q \,.
$$
 (14.87)

Since a convex combination of achievable rates is achievable, so is R. Hence we have proved the achievability of the region in the theorem. The same argument can be used to show that every point in the convex closure of the region in (14.58) can be written as the mixture of points satisfying (14.86) and hence can be written in the form (14.81).

The converse will be proved in the next section. The converse shows that all achievable rate pairs are of the form (14.81), and hence establishes that this is the capacity region of the multiple access channel.

The cardinality bound on the time-sharing random variable  $Q$  is a consequence of Caratheodory's theorem on convex sets. See the discussion below.  $\square$ 

The proof of the convexity of the capacity region shows that any convex combination of achievable rate pairs is also achievable. We can continue this process, taking convex combinations of more points. Do we need to use an arbitrary number of points ? Will the capacity region be increased? The following theorem says no.

**Theorem 14.3.4** (Carathéodory): Any point in the convex closure of a connected compact set A in a d dimensional Euclidean space can be represented as a convex combination of  $d+1$  or fewer points in the original set A.

Proof: The proof can be found in Eggleston [95] and Grünbaum [127], and is omitted here.  $\Box$ 

This theorem allows us to restrict attention to a certain finite convex combination when calculating the capacity region. This is an important property because without it we would not be able to compute the capacity region in (14.81), since we would never know whether using a larger alphabet 2 would increase the region.

In the multiple access channel, the bounds define a connected compact set in three dimensions. Therefore all points in its closure can be defined as the convex combination of four points. Hence, we can restrict the cardinality of  $Q$  to at most 4 in the above definition of the capacity region.

#### 14.3.4 Converse for the Multiple Access Channel

We have so far proved the achievability of the capacity region. In this section, we will prove the converse.

**Proof** (Converse to Theorem 14.3.1 and Theorem 14.3.3): We must show that given any sequence of  $((2^{nR_1}, 2^{nR_2}), n)$  codes with  $P_e^{(n)} \rightarrow 0$ , that the rates must satisfy

$$
R_1 
\le I(X_1; Y | X_2, Q),
$$
  

$$
R_2 
\le I(X_2; Y | X_1, Q),
$$
  

$$
R_1 + R_2 
\le I(X_1, X_2; Y | Q)
$$
 $(14.88)$ 

for some choice of random variable  $Q$  defined on  $\{1, 2, 3, 4\}$  and joint distribution  $p(q)p(x_1|q)p(x_2|q)p(y|x_1, x_2)$ .

Fix  $n$ . Consider the given code of block length  $n$ . The joint distribution on  $w_1 \times w_2 \times x_1 \times x_2 \times y$  is well defined. The only randomness is due to the random uniform choice of indices  $W_1$  and  $W_2$  and the randomne induced by the channel. The joint distribution is

$$
p(w_1, w_2, x_1^n, x_2^n, y^n) = \frac{1}{2^{nR_1}} \frac{1}{2^{nR_2}} p(x_1^n|w_1) p(x_2^n|w_2) \prod_{i=1}^n p(y_i|x_{1i}, x_{2i})
$$
(14.89)

where  $p(x_1^n|w_1)$  is either 1 or 0 depending on whether  $x_1^n = x_1(w_1)$ , the codeword corresponding to  $w_1$ , or not, and similarly,  $p(x_2^{n}|w_2) = 1$  or 0 according to whether  $x_2^{\overline{n}} = \mathbf{x}_2(w_2)$  or not. The mutual informations that follow are calculated with respect to this distribution.

By the code construction, it is possible to estimate  $(W_1, W_2)$  from the received sequence  $Y<sup>n</sup>$  with a low probability of error. Hence the conditional entropy of  $(W_1, W_2)$  given  $Y^{\overline{n}}$  must be small. By Fano's inequality,

$$
H(W_1, W_2 | Y^n) \le n(R_1 + R_2)P_e^{(n)} + H(P_e^{(n)}) \stackrel{\triangle}{=} n\epsilon_n \,. \tag{14.90}
$$

It is clear that  $\epsilon_n \to 0$  as  $P_e^{\alpha} \to 0$ .<br>Then we have

Then we have

$$
H(W_1|Y^n) \le H(W_1, W_2|Y^n) \le n\epsilon_n, \qquad (14.91)
$$

$$
H(W_2|Y^n) \le H(W_1, W_2|Y^n) \le n\epsilon_n.
$$
 (14.92)

We can now bound the rate  $R_1$  as

 $\mathbf{r}$ 

$$
nR_1 = H(W_1) \tag{14.93}
$$

$$
= I(W_1; Y^n) + H(W_1|Y^n) \tag{14.94}
$$

$$
\stackrel{(a)}{\leq} I(W_1; Y^n) + n\epsilon_n \tag{14.95}
$$

$$
\leq I(X_1^n(W_1); Y^n) + n\epsilon_n \tag{14.96}
$$

$$
= H(X_1^n(W_1)) - H(X_1^n(W_1)|Y^n) + n\epsilon_n \tag{14.97}
$$

$$
\leq H(X_1^n(W_1)|X_2^n(W_2)) - H(X_1^n(W_1)|Y^n, X_2^n(W_2)) + n\epsilon_n \tag{14.98}
$$

$$
= I(X_1^n(W_1); Y^n | X_2^n(W_2)) + n\epsilon_n \tag{14.99}
$$

$$
= H(Y^n | X_2^n(W_2)) - H(Y^n | X_1^n(W_1), X_2^n(W_2)) + n\epsilon_n \tag{14.100}
$$

$$
\stackrel{(d)}{=} H(Y^n | X_2^n(W_2)) - \sum_{i=1}^n H(Y_i | Y^{i-1}, X_1^n(W_1), X_2^n(W_2)) + n\epsilon_n \quad (14.101)
$$

$$
\stackrel{(e)}{=} H(Y^n | X_2^n(W_2)) - \sum_{i=1}^n H(Y_i | X_{1i}, X_{2i}) + n\epsilon_n
$$
\n(14.102)

$$
\leq \sum_{i=1}^{r} H(Y_i | X_2^n(W_2)) - \sum_{i=1}^{n} H(Y_i | X_{1i}, X_{2i}) + n\epsilon_n
$$
\n(14.103)

$$
\leq \sum_{i=1}^{n} H(Y_i | X_{2i}) - \sum_{i=1}^{n} H(Y_i | X_{1i}, X_{2i}) + n\epsilon_n
$$
\n(14.104)

$$
=\sum_{i=1}^{n} I(X_{1i}; Y_i | X_{2i}) + n\epsilon_n ,
$$
\n(14.105)

where

- (a) follows from Fano's inequality,
- (b) from the data processing inequality,
- (c) from the fact that since  $W_1$  and  $W_2$  are independent, so are  $X_1^n(W_1)$ and  $X_2^{\circ}(W_2)$ , and hence  $H(X_1^{\circ}(W_1)|X_2^{\circ}(W_2)) = H(X_1^{\circ}(W_1))$ , and  $H(X_1^n(W_1)| Y^n, X_2^n(W_2)) \leq H(X_1^n(W_1)| Y^n)$  by conditioning
- (d) follows from the chain rule,
- (e) from the fact that  $Y_i$  depends only on  $X_{1i}$  and  $X_{2i}$  by the memoryless property of the channel,
- (f) from the chain rule and removing conditioning, and
- (g) follows from removing conditioning.

Hence, we have

$$
R_1 \leq \frac{1}{n} \sum_{i=1}^n I(X_{1i}; Y_i | X_{2i}) + \epsilon_n \,. \tag{14.106}
$$

Similarly, we have

$$
R_2 \leq \frac{1}{n} \sum_{i=1}^n I(X_{2i}; Y_i | X_{1i}) + \epsilon_n.
$$
 (14.107)

To bound the sum of the rates, we have

$$
n(R_1 + R_2) = H(W_1, W_2)
$$
\n(14.108)

$$
= I(W_1, W_2; Y^n) + H(W_1, W_2 | Y^n)
$$
\n(14.109)

$$
\stackrel{(a)}{\leq} I(W_1, W_2; Y^n) + n\epsilon_n \tag{14.110}
$$

<sup>(b)</sup>  
\n
$$
\leq I(X_1^n(W_1), X_2^n(W_2); Y^n) + n\epsilon_n
$$
 (14.111)

$$
= H(Y^n) - H(Y^n | X_1^n(W_1), X_2^n(W_2)) + n\epsilon_n \qquad (14.112)
$$

$$
\stackrel{(c)}{=} H(Y^n) - \sum_{i=1}^n H(Y_i|Y^{i-1}, X_1^n(W_1), X_2^n(W_2)) + n\epsilon_n \qquad (14.113)
$$

$$
\stackrel{(d)}{=} H(Y^n) - \sum_{i=1}^n H(Y_i | X_{1i}, X_{2i}) + n\epsilon_n \tag{14.114}
$$

$$
\stackrel{(e)}{\leq} \sum_{i=1}^{n} H(Y_i) - \sum_{i=1}^{n} H(Y_i | X_{1i}, X_{2i}) + n\epsilon_n
$$
 (14.115)

$$
=\sum_{i=1}^{n} I(X_{1i}, X_{2i}; Y_i) + n\epsilon_n ,
$$
\n(14.116)

where

- (a) follows from Fano's inequality,
- (b) from the data processing inequality,
- (c) from the chain rule,
- (b) from the chain that,  $\frac{1}{2}$  and  $\frac{1}{2}$  and  $\frac{1}{2}$  and  $\frac{1}{2}$  and  $\frac{1}{2}$  and is condition-If  $\sum_{i=1}^{\infty} a_i$  is the contract of  $\sum_{i=1}^{\infty} a_i$ (e) follows from the chain rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule and rule a

Hence we have

$$
R_1 + R_2 \le \frac{1}{n} \sum_{i=1}^n I(X_{1i}, X_{2i}; Y_i) + \epsilon_n \,. \tag{14.117}
$$

The expressions in (14.106), (14.107) and (14.117) are the averages of the mutual informations calculated at the empirical distributions in column  $i$  of the codebook. We can rewrite these equations with the new variable Q, where  $Q = i \in \{1, 2, ..., n\}$  with probability  $\frac{1}{n}$ . The equations become

$$
R_1 \le \frac{1}{n} \sum_{i=1}^{n} I(X_{1i}; Y_i | X_{2i}) + \epsilon_n
$$
 (14.118)

$$
= \frac{1}{n} \sum_{i=1}^{n} I(X_{1q}; Y_q | X_{2q}, Q = i) + \epsilon_n
$$
 (14.119)

$$
=I(X_{1Q};Y_Q|X_{2Q},Q)+\epsilon_n
$$
\n(14.120)

$$
= I(X_1; Y|X_2, Q) + \epsilon_n , \qquad (14.121)
$$

where  $X_1 = X_{1Q}, X_2 = X_{2Q}$  and  $Y = Y_Q$  are new random variables whose distributions depend on Q in the same way as the distributions of  $X_{1i}$ ,  $X_{2i}$  and  $Y_i$  depend on i. Since  $W_1$  and  $W_2$  are independent, so are  $X_{1i}(W_1)$ and  $X_{2i}(W_2)$ , and hence

 $Pr(X_{1i}(W_1) = x_1, X_{2i}(W_2) = x_2)$ 

 $\triangleq Pr\{X_{1Q} = x_1 | Q = i\} Pr\{X_{2Q} = x_2 | Q = i\}.$ 

(14.122)

Hence, taking the limit as  $n \to \infty$ ,  $P_e^{(n)} \to 0$ , we have the following converse:

$$
R_1 \le I(X_1; Y | X_2, Q),
$$
  

$$
R_2 \le I(X_2; Y | X_1, Q),
$$
  

$$
R_1 + R_2 \le I(X_1, X_2; Y | Q)
$$
(14.123)

for some choice of joint distribution  $p(q)p(x_1|q)p(x_2|q)p(y|x_1, x_2)$ .

As in the previous section, the region is unchanged if we limit the cardinality of 2 to 4.

This completes the proof of the converse.  $\Box$ 

Thus the achievability of the region of Theorem 14.3.1 was proved in Section 14.3.1. In Section 14.3.3, we showed that every point in the region defined by (14.88) was also achievable. In the converse, we showed that the region in (14.88) was the best we can do, establishing that this is indeed the capacity region of the channel. Thus the region in (14.58) cannot be any larger than the region in (14.88), and this is the capacity region of the multiple access channel.

#### 14.3.5 m-User Multiple Access Channels

We will now generalize the result derived for two senders to  $m$  senders,  $m \geq 2$ . The multiple access channel in this case is shown in Figure 14.15.

We send independent indices  $w_1, w_2, \ldots, w_m$  over the channel from the senders  $1, 2, \ldots, m$  respectively. The codes, rates and achievability are all defined in exactly the same way as the two sender case.

Let  $S \subseteq \{1, 2, ..., m\}$ . Let  $S^c$  denote the complement of S. Let  $R(S)$  =  $\Sigma_{i\in S}$   $R_i$ , and let  $X(S) = \{X_i : i \in S\}$ . Then we have the following theorem.

Theorem 14.35: The capacity region of the m-user multiple access channel is the closure of the convex hull of the rate vectors satisfying

$$
R(S) \le I(X(S); Y|X(Sc)) \quad \text{for all } S \subseteq \{1, 2, ..., m\} \quad (14.124)
$$

for some product distribution  $p_1(x_1)p_2(x_2)...p_m(x_m)$ .

**Proof:** The proof contains no new ideas. There are now  $2^m - 1$  terms in the probability of error in the achievability proof and an equal number of inequalities in the proof of the converse. Details are left to the reader.  $\Box$ 

In general, the region in (14.124) is a beveled box.

#### 14.3.6 Gaussian Multiple Access Channels

We now discuss the Gaussian multiple access channel of Section 14.1.2 in somewhat more detail.

Image /page/13/Figure/13 description: A diagram shows multiple inputs labeled X1, X2, and Xm, with ellipses indicating intermediate inputs. These inputs converge into a rectangular box labeled with the conditional probability p(y|x1, x2, ..., xm). An arrow emerges from the box on the right, labeled Y, indicating the output.

Figure 14.15. m-user multiple access channel.

There are two senders,  $X_1$  and  $X_2$ , sending to the single receiver Y. The received signal at time  $i$  is

$$
Y_i = X_{1i} + X_{2i} + Z_i, \qquad (14.125)
$$

where  $\{Z_i\}$  is a sequence of independent, identically distributed, zero mean Gaussian random variables with variance  $N$  (Figure 14.16). We will assume that there is a power constraint  $P_i$  on sender j, i.e., for each sender, for all messages, we must have

$$
\frac{1}{n}\sum_{i=1}^{n}x_{ji}^{2}(w_{j}) \le P_{j}, \quad w_{j} \in \{1, 2, \ldots, 2^{nR_{j}}\}, j = 1, 2. \quad (14.126)
$$

Just as the proof of achievability of channel capacity for the discrete case (Chapter 8) was extended to the Gaussian channel (Chapter lo), we can extend the proof the discrete multiple access channel to the Gaussian multiple access channel. The converse can also be extended similarly, so we expect the capacity region to be the convex hull of the set of rate pairs satisfying

$$
R_1 \le I(X_1; Y|X_2), \tag{14.127}
$$

$$
R_2 \le I(X_2; Y|X_1), \tag{14.128}
$$

$$
R_1 + R_2 \le I(X_1, X_2; Y) \tag{14.129}
$$

for some input distribution  $f_1(x_1)f_2(x_2)$  satisfying  $EX_1^2 \le P_1$  and  $EX_2^2 \le$  $P_{2}$ .

Now, we can expand the mutual information in terms of relative entropy, and thus

$$
I(X_1; Y|X_2) = h(Y|X_2) - h(Y|X_1, X_2)
$$
\n(14.130)

$$
= h(X_1 + X_2 + Z | X_2) - h(X_1 + X_2 + Z | X_1, X_2) \qquad (14.131)
$$

Image /page/14/Figure/13 description: This is a diagram showing a summation point with three inputs and one output. The inputs are labeled X1, X2, and Z, and they all converge at a circle with a plus sign inside, indicating summation. The output is labeled Y and originates from the summation point.

Figure 14.16. Gaussian multiple access channel.

$$
= h(X_1 + Z | X_2) - h(Z | X_1, X_2)
$$
\n(14.132)

$$
= h(X_1 + Z | X_2) - h(Z) \tag{14.133}
$$

$$
= h(X_1 + Z) - h(Z) \tag{14.134}
$$

$$
= h(X_1 + Z) - \frac{1}{2} \log(2\pi e)N \tag{14.135}
$$

$$
\leq \frac{1}{2} \log(2\pi e)(P_1 + N) - \frac{1}{2} \log(2\pi e)N \tag{14.136}
$$

$$
=\frac{1}{2}\log\left(1+\frac{P_1}{N}\right),\tag{14.137}
$$

where (14.133) follows from the fact that Z is independent of  $X_1$  and  $X_2$ , (14.134) from the independence of  $X_1$  and  $X_2$ , and (14.136) from the fact that the normal maximizes entropy for a given second moment. Thus the maximizing distribution is  $X_1 \sim \mathcal{N}(0, P_1)$  and  $X_2 \sim \mathcal{N}(0, P_2)$  with  $X_1$ and  $X_2$  independent. This distribution simultaneously maximizes the mutual information bounds in  $(14.127)$ – $(14.129)$ .

**Definition:** We define the channel capacity function

$$
C(x) \stackrel{\triangle}{=} \frac{1}{2} \log(1+x) , \qquad (14.138)
$$

corresponding to the channel capacity of a Gaussian white noise channel with signal to noise ratio  $x$ .

Then we write the bound on  $R_1$  as

$$
R_1 \le C\left(\frac{P_1}{N}\right). \tag{14.139}
$$

Similarly,

$$
R_2 \le C\left(\frac{P_2}{N}\right),\tag{14.140}
$$

and

$$
R_1 + R_2 \le C\left(\frac{P_1 + P_2}{N}\right). \tag{14.141}
$$

These upper bounds are achieved when  $X_1 \sim \mathcal{N}(0, P_1)$  and  $X_2 = \mathcal{N}(0, P_2)$ and define the capacity region.

The surprising fact about these inequalities is that the sum of the rates can be as large as  $C(\frac{1}{N})$ , which is that rate achieved by a single transmitter sending with a power equal to the sum of the powers.

The interpretation of the corner points is very similar to the interpretation of the achievable rate pairs for a discrete multiple access channel for a fixed input distribution. In the case of the Gaussian channel, we can consider decoding as a two-stage process: in the first stage, the receiver decodes the second sender, considering the first sender as part of the noise. This decoding will have low probability of error if  $R_{\rm\,2}$   $<$  $C(\frac{P_2}{P_1 + N})$ . After the second sender has been successfully decoded, it can be subtracted out and the first sender can be decoded correctly if  $R_1 \leq C(\frac{P_1}{N})$ . Hence, this argument shows that we can achieve the rate pairs at the corner points of the capacity region.

If we generalize this to  $m$  senders with equal power, the total rate is  $C(\frac{mP}{N})$ , which goes to  $\infty$  as  $m \to \infty$ . The average rate per sender,  $\frac{1}{m}C(\frac{mP}{N})$ goes to 0. Thus when the total number of senders is very large, so that there is a lot of interference, we can still send a total amount of information which is arbitrarily large even though the rate per individual sender goes to 0.

The capacity region described above corresponds to Code Division Multiple Access (CDMA), where orthogonal codes are used for the different senders, and the receiver decodes them one by one. In many practical situations, though, simpler schemes like time division multiplexing or frequency division multiplexing are used.

With frequency division multiplexing, the rates depend on the bandwidth allotted to each sender. Consider the case of two senders with powers  $P_1$  and  $P_2$  and using bandwidths non-intersecting frequency bands  $W_1$  and  $W_2$ , where  $W_1 + W_2 = W$  (the total bandwidth). Using the formula for the capacity of a single user bandlimited channel, the following rate pair is achievable:

Image /page/16/Figure/5 description: The image displays a two-dimensional graph with the R2 axis on the vertical and the R1 axis on the horizontal. The origin (0,0) is at the bottom left. The graph shows a curve that starts at the point (0, C(P2/N)) on the R2 axis, moves horizontally to the right, then curves downwards and to the right, forming a convex shape. This curve is intersected by a dashed line that goes from the top left towards the bottom right. The curve also has a polygonal approximation shown with straight line segments. The R1 axis is labeled with values including C(P1/(P2+N)) and C(P1/N). The R2 axis is labeled with values including C(P2/(P1+N)) and C(P2/N). A single black dot is visible on the curve.

Figure 14.17. Gaussian multiple access channel capacity.

$$
R_1 = \frac{W_1}{2} \log \left( 1 + \frac{P_1}{NW_1} \right), \tag{14.142}
$$

$$
R_2 = \frac{W_2}{2} \log \left( 1 + \frac{P_2}{NW_2} \right). \tag{14.143}
$$

As we vary  $W_1$  and  $W_2$ , we trace out the curve as shown in Figure 14.17. This curve touches the boundary of the capacity region at one point, which corresponds to allotting bandwidth to each channel proportional to the power in that channel. We conclude that no allocation of frequency bands to radio stations can be optimal unless the allocated powers are proportional to the bandwidths.

As Figure 14.17 illustrates, in general the capacity region is larger than that achieved by time division or frequency division multiplexing. But note that the multiple access capacity region derived above is achieved by use of a common decoder for all the senders. However in many practical systems, simplicity of design is an important consideration, and the improvement in capacity due to the multiple access ideas presented earlier may not be sufficient to warrant the increased complexity.

For a Gaussian multiple access system with  $m$  sources with powers  $P_1, P_2, \ldots, P_m$  and ambient noise of power N, we can state the equivalent of Gauss's law for any set S in the form

 $\sum_{i \in S} R_i$  = Total rate of information flow across boundary of S (14.144)

$$
\leq C \left( \frac{\sum_{i \in S} P_i}{N} \right). \tag{14.145}
$$

### 14.4 ENCODING OF CORRELATED SOURCES

We now turn to distributed data compression. This problem is in many ways the data compression dual to the multiple access channel problem.

We know how to encode a source X. A rate  $R > H(X)$  is sufficient. Now suppose that there are two sources  $(X, Y) \sim p(x, y)$ . A rate  $H(X, Y)$  is sufficient if we are encoding them together. But what if the  $X$ -source and the Y-source must be separately described for some user who wishes to reconstruct both X and Y? Clearly, by separate encoding X and Y, it is seen that a rate  $R = R_x + R_y > H(X) + H(Y)$  is sufficient. However, in a surprising and fundamental paper by Slepian and Wolf [255], it is shown that a total rate  $R = H(X, Y)$  is sufficient even for separate encoding of correlated sources.

Let  $(X_1, Y_1), (X_2, Y_2), \ldots$  be a sequence of jointly distributed random variables i.i.d.  $\sim p(x, y)$ . Assume that the X sequence is available at a location A and the Y sequence is available at a location B. The situation is illustrated in Figure 14.18.

Before we proceed to the proof of this result, we will give a few definitions.

**Definition:** A  $((2^{nR_1}, 2^{nR_2}), n)$  distributed source code for the joint source  $(X, Y)$  consists of two encoder maps,

$$
f_1: \mathscr{X}^n \to \{1, 2, \ldots, 2^{nR_1}\}, \qquad (14.146)
$$

$$
f_2: \mathcal{Y}^n \to \{1, 2, \dots, 2^{nR_2}\} \tag{14.147}
$$

and a decoder map,

$$
g: \{1, 2, \ldots, 2^{nR_1}\} \times \{1, 2, \ldots, 2^{nR_2}\} \to \mathscr{X}^n \times \mathscr{Y}^n. \qquad (14.148)
$$

Here  $f_1(X^n)$  is the index corresponding to  $X^n$ ,  $f_2(Y^n)$  is the index corresponding to  $Y^n$  and  $(R_1, R_2)$  is the rate pair of the code.

**Definition:** The probability of error for a distributed source code is defined as

$$
P_e^{(n)} = P(g(f_1(X^n), f_2(Y^n)) \neq (X^n, Y^n)). \tag{14.149}
$$

**Definition:** A rate pair  $(R_1, R_2)$  is said to be achievable for a distributed source if there exists a sequence of  $((2^{n_1}, 2^{n_2}), n)$  distributed source codes with probability of error  $P_{\epsilon}^{\omega} \rightarrow 0$ . The *achievable rate region* is the closure of the set of achievable rates.

Image /page/18/Figure/12 description: This is a block diagram illustrating a communication system. On the left, a block labeled (X, Y) represents the input. Two arrows originate from this block, one labeled X pointing to an 'Encoder' block, and another labeled Y pointing to a second 'Encoder' block. The output of the first encoder is labeled R1, and the output of the second encoder is labeled R2. Both R1 and R2 arrows point to a 'Decoder' block. The output of the decoder block is labeled (X^, Y^).

Figure 14.18. Slepian-Wolf coding.

**Theorem 14.4.1** (Slepian-Wolf): For the distributed source coding problem for the source  $(X, Y)$  drawn i.i.d  $\sim p(x, y)$ , the achievable rate region is given by

$$
R_1 \ge H(X|Y), \tag{14.150}
$$

$$
R_2 \ge H(Y|X), \tag{14.151}
$$

$$
R_1 + R_2 \ge H(X, Y). \tag{14.152}
$$

Let us illustrate the result with some examples.

**Example 14.4.1:** Consider the weather in Gotham and Metropolis. For the purposes of our example, we will assume that Gotham is sunny with probability 0.5 and that the weather in Metropolis is the same as in Gotham with probability 0.89. The joint distribution of the weather is given as follows:

|                 | Metropolis |       |
|-----------------|------------|-------|
| p(x, y)         | Rain       | Shine |
| Gotham<br>Rain  | 0.445      | 0.055 |
| Gotham<br>Shine | 0.055      | 0.445 |

Assume that we wish to transmit 100 days of weather information to the National Weather Service Headquarters in Washington. We could send all the 100 bits of the weather in both places, making 200 bits in all. If we decided to compress the information independently, then we would still need  $100H(0.5) = 100$  bits of information from each place for a total of 200 bits.

If instead we use Slepian-Wolf encoding, we need only  $H(X)$  +  $H(Y|X) = 100H(0.5) + 100H(0.89) = 100 + 50 = 150$  bits total.

**Example 14.4.2:** Consider the following joint distribution:

| p(u, v) | 0   | 1   |
|---------|-----|-----|
| 0       | 1/3 | 1/3 |
| 1       | 0   | 1/3 |

In this case, the total rate required for the transmission of this source is  $H(U) + H(V|U) = \log 3 = 1.58$  bits, rather than the 2 bits which would be needed if the sources were transmitted independently without Slepian-Wolf encoding.

#### 14.4.1 Achievability of the Slepian-Wolf Theorem

We now prove the achievability of the rates in the Slepian-Wolf theorem. Before we proceed to the proof, we will first introduce a new coding procedure using random bins.

The essential idea of random bins is very similar to hash functions: we choose a large random index for each source sequence. If the set of typical source sequences is small enough (or equivalently, the range of the hash function is large enough), then with high probability, different source sequences have different indices, and we can recover the source sequence from the index.

Let us consider the application of this idea to the encoding of a single source. In Chapter 3, the method that we considered was to index all elements of the typical set and not bother about elements outside the typical set. We will now describe the random binning procedure, which indexes all sequences, but rejects untypical sequences at a later stage.

Consider the following procedure: For each sequence  $X<sup>n</sup>$ , draw an index at random from  $\{1, 2, ..., 2^{nR}\}$ . The set of sequences  $X^n$  which have the same index are said to form a bin, since this can be viewed as first laying down a row of bins and then throwing the  $X<sup>n</sup>$ 's at random into the bins. For decoding the source from the bin index, we look for a typical  $X^n$  sequence in the bin. If there is one and only one typical  $X^n$ sequence in the bin, we declare it to be the estimate  $\hat{X}^n$  of the source sequence; otherwise, an error is declared.

The above procedure defines a source code. To analyze the probability of error for this code, we will now divide the  $X<sup>n</sup>$  sequences into two types, the typical sequences and the non-typical sequences.

If the source sequence is typical, then the bin corresponding to this source sequence will contain at least one typical sequence (the source sequence itself). Hence there will be an error only if there is more than one typical sequence in this bin. If the source sequence is non-typical, then there will always be an error. But if the number of bins is much larger than the number of typical sequences, the probability that there is more than one typical sequence in a bin is very small, and hence the probability that a typical sequence will result in an error is very small.

Formally, let  $f(X^n)$  be the bin index corresponding to  $X^n$ . Call the decoding function  $g$ . The probability of error (averaged over the random choice of codes  $f$ ) is

$$
P(g(f(\mathbf{X})) \neq \mathbf{X}) \leq P(\mathbf{X} \not\in A_{\epsilon}^{(n)}) + \sum_{\mathbf{x}} P(\exists \mathbf{x}' \neq \mathbf{x} : \mathbf{x}' \in A_{\epsilon}^{(n)}, f(\mathbf{x}') = f(\mathbf{x}))p(\mathbf{x})
$$
  

$$
\leq \epsilon + \sum_{\mathbf{x}} \sum_{\substack{\mathbf{x}' \in A_{\epsilon}^{(n)} \\ \mathbf{x}' \neq \mathbf{x}}} P(f(\mathbf{x}') = f(\mathbf{x}))p(\mathbf{x}) (14.153)
$$

$$
\leq \epsilon + \sum_{\mathbf{x}} \sum_{\mathbf{x}' \in A_{\epsilon}^{(n)}} 2^{-nR} p(\mathbf{x}) \tag{14.154}
$$

$$
= \epsilon + \sum_{\mathbf{x}' \in A_{\epsilon}^{(n)}} 2^{-nR} \sum_{\mathbf{x}} p(\mathbf{x})
$$
 (14.155)

$$
\leq \epsilon + \sum_{\mathbf{x}' \in A_{\epsilon}^{(n)}} 2^{-nR} \tag{14.156}
$$

$$
\leq \epsilon + 2^{n(H(X)+\epsilon)} 2^{-nR} \tag{14.157}
$$

$$
\leq 2\epsilon \tag{14.158}
$$

if  $R > H(X) + \epsilon$  and n is sufficiently large. Hence if the rate of the code is greater than the entropy, the probability of error is arbitrarily small and the code achieves the same results as the code described in Chapter 3.

The above example illustrates the fact that there are many ways to construct codes with low probabilities of error at rates above the entropy of the source; the universal source code is another example of such a code. Note that the binning scheme does not require an explicit characterization of the typical set at the encoder; it is only needed at the decoder. It is this property that enables this code to continue to work in the case of a distributed source, as will be illustrated in the proof of the theorem.

We now return to the consideration of the distributed source coding and prove the achievability of the rate region in the Slepian-Wolf theorem.

Proof (Achievability in Theorem 14.4.1): The basic idea of the proof is to partition the space of  $\mathcal{X}^n$  into  $2^{nR_1}$  bins and the space of  $\mathcal{Y}^n$  into  $2^{nR_2}$ bins.

- Random code generation. Independently assign every  $\mathbf{x} \in \mathcal{X}^n$  to one of  $2^{nR_1}$  bins according to a uniform distribution on  $\{1, 2, \ldots, 2^{nR_1}\}.$ Similarly, randomly assign every  $y \in \mathcal{Y}^n$  to one of  $2^{nR_2}$  bins. Reveal the assignments  $f_1$  and  $f_2$  to both the encoder and decoder.
- *Encoding.* Sender 1 sends the index of the bin to which  $X$  belongs. Sender 2 sends the index of the bin to which  $Y$  belongs.

*Decoding.* Given the received index pair  $(i_0, j_0)$ , declare  $(\hat{\mathbf{x}}, \hat{\mathbf{y}}) = (\mathbf{x}, \mathbf{y})$ , if there is one and only one pair of sequences  $(x, y)$  such that  $f_1(\mathbf{x}) = i_0$ ,  $f_2(\mathbf{y}) = j_0$  and  $(\mathbf{x}, \mathbf{y}) \in A_{\epsilon}^{(n)}$ . Otherwise declare an error. The scheme is illustrated in Figure 14.19. The set of  $X$  sequences and the set of Y sequences are divided into bins in such a way that the pair of indices specifies a product bin.

Image /page/22/Figure/1 description: The image displays a grid representing bins for jointly typical pairs of sequences (x^n, y^n). The grid has 2^nR1 bins along the horizontal axis and 2^nR2 bins along the vertical axis. Several black dots are scattered within the bins, indicating the presence of these pairs. Two arrows point to a cluster of dots in the bottom right section of the grid, labeling them as "2^nH(X, Y) jointly typical pairs (x^n, y^n)".

Figure 14.19. Slepian-Wolf encoding: the jointly typical pairs are isolated by the product bins.

Probability of error. Let  $(X_i, Y_i) \sim p(x, y)$ . Define the events

$$
E_0 = \{ (\mathbf{X}, \mathbf{Y}) \not\in A_{\epsilon}^{(n)} \}, \tag{14.159}
$$

$$
E_1 = \left\{ \exists \mathbf{x}' \neq \mathbf{X} : f_1(\mathbf{x}') = f_1(\mathbf{X}) \text{ and } (\mathbf{x}', \mathbf{Y}) \in A_{\epsilon}^{(n)} \right\}, \quad (14.160)
$$

$$
E_2 = \left\{ \exists \mathbf{y}' \neq \mathbf{Y} : f_2(\mathbf{y}') = f_2(\mathbf{Y}) \text{ and } (\mathbf{X}, \mathbf{y}') \in A_{\epsilon}^{(n)} \right\}, \quad (14.161)
$$

and

$$
E_{12} = \{ \exists (\mathbf{x}', \mathbf{y}') : \mathbf{x}' \neq \mathbf{X}, \mathbf{y}' \neq \mathbf{Y}, f_1(\mathbf{x}') = f_1(\mathbf{X}), f_2(\mathbf{y}') \neq f_2(\mathbf{Y})
$$
  
and  $(\mathbf{x}', \mathbf{y}') \in A_{\epsilon}^{(n)} \}$ . (14.162)

Here  $X$ ,  $Y$ ,  $f_1$  and  $f_2$  are random. We have an error if  $(X, Y)$  is not in  $A_{\epsilon}^{\prime\prime}$  or if there is another typical pair in the same bin. Hence by the union of events bound,

$$
P_e^{(n)} = P(E_0 \cup E_1 \cup E_2 \cup E_{12})
$$
 (14.163)

$$
\leq P(E_0) + P(E_1) + P(E_2) + P(E_{12}). \tag{14.164}
$$

First consider  $E_0$ . By the AEP,  $P(E_0) \rightarrow 0$  and hence for n sufficiently large,  $P(E_0) < \epsilon$ .

To bound  $P(E_1)$ , we have

$$
P(E_1) = P\{\exists \mathbf{x}' \neq \mathbf{X} : f_1(\mathbf{x}') = f_1(\mathbf{X}), \text{ and } (\mathbf{x}', \mathbf{Y}) \in A_{\epsilon}^{(n)}\} \qquad (14.165)
$$

$$
= \sum_{(\mathbf{x}, \mathbf{y})} p(\mathbf{x}, \mathbf{y}) P\{ \exists \mathbf{x}' \neq \mathbf{x} : f_1(\mathbf{x}') = f_1(\mathbf{x}), (\mathbf{x}', \mathbf{y}) \in A_{\epsilon}^{(n)}\} \quad (14.166)
$$

$$
\leq \sum_{(\mathbf{x}, \mathbf{y})} p(\mathbf{x}, \mathbf{y}) \sum_{\substack{\mathbf{x}' \neq \mathbf{x} \\ (\mathbf{x}', \mathbf{y}) \in A_{\epsilon}^{(n)}}} P(f_1(\mathbf{x}') = f_1(\mathbf{x})) \tag{14.167}
$$

$$
= \sum_{(\mathbf{x}, \mathbf{y})} p(\mathbf{x}, \mathbf{y}) 2^{-nR_1} |A_{\epsilon}(X|\mathbf{y})|
$$
 (14.168)

$$
\leq 2^{-nR_1} 2^{n(H(X|Y)+\epsilon)} \text{ (by Theorem 14.2.2)}, \tag{14.169}
$$

which goes to 0 if  $R_1 > H(X|Y)$ . Hence for sufficiently large n,  $P(E_1) < \epsilon$ . Similarly, for sufficiently large n,  $P(E_2) < \epsilon$  if  $R_2 >$  $H(Y|X)$  and  $P(E_{12}) < \epsilon$  if  $R_1 + R_2 > H(X, Y)$ .

Since the average probability of error is  $\lt 4\epsilon$ , there exists at least one code ( $f_1^*, f_2^*, g^*$ ) with probability of error  $\lt 4\epsilon$ . Thus, we can construct a sequence of codes with  $P_e^{(n)} \rightarrow 0$  and the proof of achievability is complete.  $\Box$ 

#### 14.4.2 Converse for the Slepian-Wolf Theorem

The converse for the Slepian-Wolf theorem follows obviously from from the results for single source, but we will provide it for completeness.

**Proof** (Converse to Theorem 14.4.1): As usual, we begin with Fano's inequality. Let  $f_1$ ,  $f_2$ ,  $g$  be fixed. Let  $I_0 = f_1(X^n)$  and  $J_0 = f_2(Y^n)$ . Then

$$
H(X^n, Y^n | I_0, J_0) \le P_{\epsilon}^{(n)} n(\log |\mathcal{X}| + \log |\mathcal{Y}|) + 1 = n\epsilon_n , \quad (14.170)
$$

where  $\epsilon_n \rightarrow 0$  as  $n \rightarrow \infty$ . Now adding conditioning, we also have

$$
H(X^n | Y^n, I_0, J_0) \le P_e^{(n)} n \epsilon_n , \qquad (14.171)
$$

and

$$
H(Y^n | X^n, I_0, J_0) \le P_e^{(n)} n \epsilon_n \,. \tag{14.172}
$$

We can write a chain of inequalities

$$
n(R_1 + R_2) \stackrel{(a)}{\geq} H(I_0, J_0) \tag{14.173}
$$

$$
= I(X^n, Y^n; I_0, J_0) + H(I_0, J_0 | X^n, Y^n) \qquad (14.174)
$$

$$
\stackrel{(b)}{=} I(X^n, Y^n; I_0, J_0) \tag{14.175}
$$

$$
= H(X^n, Y^n) - H(X^n, Y^n | I_0, J_0)
$$
 (14.176)

$$
\stackrel{\text{(c)}}{\geq} H(X^n, Y^n) - n\epsilon_n \tag{14.177}
$$

$$
\stackrel{(d)}{=} nH(X,Y) - n\epsilon_n , \qquad (14.178)
$$

where

- (a) follows from the fact that  $I_0 \in \{1, 2, ..., 2^{m_1}\}\$  and  $J_0 \in \{1, 2, ..., 2^{nR_2}\}\,$
- (b) from the fact the  $I_0$  is a function of  $X^n$  and  $J_0$  is a function of  $Y^n$ ,
- (c) from Fano's inequality (14.170), and

 $\sim$ 

(d) from the chain rule and the fact that  $(X_i, Y_i)$  are i.i.d.

Similarly, using (14.171), we have

$$
nR_1 \stackrel{(a)}{\geq} H(I_0) \tag{14.179}
$$

$$
\geq H(I_0|Y^n) \tag{14.180}
$$

$$
= I(X^n; I_0 | Y^n) + H(I_0 | X^n, Y^n) \tag{14.181}
$$

$$
\stackrel{(b)}{=} I(X^n; I_0 | Y^n) \tag{14.182}
$$

$$
=H(X^n|Y^n) - H(X^n|I_0, J_0, Y^n) \tag{14.183}
$$

$$
\stackrel{\text{(c)}}{\geq} H(X^n | Y^n) - n\epsilon_n \tag{14.184}
$$

$$
\stackrel{(d)}{=} nH(X|Y) - n\epsilon_n , \qquad (14.185)
$$

Image /page/24/Figure/17 description: A graph plots R2 on the y-axis against R1 on the x-axis. The graph shows a curve that starts at the origin, goes vertically up to H(Y), then slopes downwards diagonally to H(X), and finally becomes horizontal.

Figure 14.20. Rate region for Slepian-Wolf encoding.

414

where the reasons are the same as for the equations above. Similarly, we can show that

$$
nR_2 \ge nH(Y|X) - n\epsilon_n. \tag{14.186}
$$

Dividing these inequalities by n and taking the limit as  $n \rightarrow \infty$ , we have the desired converse.  $\Box$ 

The region described in the Slepian-Wolf theorem is illustrated in Figure 14.20.

#### 14.4.3 Slepian-Wolf Theorem for Many Sources

The results of the previous section can easily be generalized to many sources. The proof follows exactly the same lines.

**Theorem 14.4.2:** Let  $(X_{1i}, X_{2i}, \ldots, X_{mi})$  be i.i.d.  $\sim p(x_1, x_2, \ldots, x_m)$ . Then the set of rate vectors achievable for distributed source coding with separate encoders and a common decoder is defined by

$$
R(S) > H(X(S)|X(Sc))
$$
\n(14.187)

for all  $S \subset \{1, 2, \ldots, m\}$  where

$$
R(S) = \sum_{i \in S} R_i , \qquad (14.188)
$$

and  $X(S) = \{X_i : j \in S\}.$ 

Proof: The proof is identical to the case of two variables and is omitted.  $\Box$ 

The achievability of Slepian-Wolf encoding has been proved for an i.i.d. correlated source, but the proof can easily be extended to the case of an arbitrary joint source that satisfies the AEP; in particular, it can  $\mathbf{b}$  and case of any source source source source  $\mathbf{c}$  and  $\mathbf{c}$  and  $\mathbf{c}$  cases cases cases cases cases cases for any  $\mathbf{c}$  and  $\mathbf{c}$  and  $\mathbf{c}$  and  $\mathbf{c}$  and  $\mathbf{c}$  and  $\mathbf{c}$  and  $\mathbf{c}$  an the extended to the case of any jointly eigout source tooj. In these cases the entropies in the definition of the rate region are replaced by the corresponding entropy rates.

#### 14.4.4 Interpretation of Slepian-Wolf Coding

 $W = \mathbf{w}$  interpretation of the corner points of the corner points of the rate region of the rate region of the rate region of the rate region of the rate region of the rate region of the rate region of the rate region o we will consider an interpretation of the corner points of the rate region in Slepian-Wolf encoding in terms of graph coloring. Consider the point with rate  $R_1 = H(X), R_2 = H(Y|X)$ . Using  $n(X)$  bits, we can encode  $X^n$ efficiently, so that the decoder can reconstruct  $X<sup>n</sup>$  with arbitrarily low probability of error. But how do we code  $Y^n$  with  $nH(Y|X)$  bits?

Looking at the picture in terms of typical sets, we see that associated with every  $X^n$  is a typical "fan" of  $Y^n$  sequences that are jointly typical with the given  $X^n$  as shown in Figure 14.21.

Image /page/26/Figure/1 description: This is a diagram illustrating a mapping between two sets, labeled X^n and Y^n. The set X^n contains seven distinct points. The set Y^n contains six distinct points, with two clusters of four points each, and two individual points. Lines connect three points from X^n to the two clusters in Y^n. Specifically, one point in X^n maps to the upper cluster in Y^n, and two points in X^n map to the lower cluster in Y^n. The remaining four points in X^n do not have any outgoing lines depicted.

Figure 14.21. Jointly typical fans.

If the Y encoder knows  $X^n$ , the encoder can send the index of the  $Y^n$ within this typical fan. The decoder, also knowing  $X<sup>n</sup>$ , can then construct this typical fan and hence reconstruct  $Y<sup>n</sup>$ . But the Y encoder does not know  $X<sup>n</sup>$ . So instead of trying to determine the typical fan, he randomly colors all  $Y^n$  sequences with  $2^{nR_2}$  colors. If the number of colors is high enough, then with high probability, all the colors in a particular fan will be different and the color of the  $Y^n$  sequence will uniquely define the  $Y^n$ sequence within the  $X^n$  fan. If the rate  $R_2 > H(Y|X)$ , the number of colors is exponentially larger than the number of elements in the fan and we can show that the scheme will have exponentially small probability of error.

# 14.5 DUALITY BETWEEN SLEPIAN-WOLF ENCODING AND MULTIPLE ACCESS CHANNELS

With multiple access channels, we considered the problem of sending independent messages over a channel with two inputs and only one output. With Slepian-Wolf encoding, we considered the problem of sending a correlated source over a noiseless channel, with a common decoder for recovery of both sources. In this section, we will explore the duality between the two systems.

In Figure 14.22, two independent messages are to be sent over the channel as  $X_1^n$  and  $X_2^n$  sequences. The receiver estimates the messages from the received sequence. In Figure 14.23, the correlated sources are encoded as "independent" messages  $i$  and  $j$ . The receiver tries to estimate the source sequences from knowledge of  $i$  and  $j$ .

In the proof of the achievability of the capacity region for the multiple access channel, we used a random map from the set of messages to the sequences  $X_1^n$  and  $X_2^n$ . In the proof for Slepian-Wolf coding, we used a random map from the set of sequences  $X<sup>n</sup>$  and  $Y<sup>n</sup>$  to a set of messages.

Image /page/27/Figure/1 description: This is a block diagram illustrating a communication system. Two input signals, W1 and W2, are transformed into X1 and X2, respectively. These signals, X1 and X2, are then fed into a processing block labeled 'p(y|x1, x2)'. The output of this block is a signal Y, which is then processed to produce estimated outputs (W-hat1, W-hat2).

Figure 14.22. Multiple access channels.

In the proof of the coding theorem for the multiple access channel, the probability of error was bounded by

$$
P_e^{(n)} \le \epsilon + \sum_{\text{codewords}} \Pr(\text{codeword jointly typical with received sequence})
$$
\n(14.189)

$$
= \epsilon + \sum_{2^{nR_1} \text{ terms}} 2^{-nI_1} + \sum_{2^{nR_2} \text{ terms}} 2^{-nI_2} + \sum_{2^{n(R_1 + R_2)} \text{ terms}} 2^{-nI_3}, \quad (14.190)
$$

where  $\epsilon$  is the probability the sequences are not typical,  $R_i$  are the rates corresponding to the number of codewords that can contribute to the probability of error, and  $I_i$  is the corresponding mutual information that corresponds to the probability that the codeword is jointly typical with the received sequence.

In the case of Slepian-Wolf encoding, the corresponding expression for the probability of error is

Image /page/27/Figure/8 description: This is a block diagram illustrating a communication system. On the left, a block labeled '(X, Y)' represents the input source. This source splits into two paths. The top path shows an input 'X' going into an 'Encoder' block, which outputs 'R1'. The bottom path shows an input 'Y' going into another 'Encoder' block, which outputs 'R2'. Both 'R1' and 'R2' converge and feed into a 'Decoder' block. The output of the 'Decoder' block is labeled '(X-hat, Y-hat)', representing the estimated output.

Figure 14.23. Correlated source encoding.

$$
P_e^{(n)} \le \epsilon + \sum_{\text{Jointly typical sequences}} Pr(\text{have same codeword}) \qquad (14.191)
$$

$$
= \epsilon + \sum_{2^{nH_1} \text{ terms}} 2^{-nR_1} + \sum_{2^{nH_2} \text{ terms}} 2^{-nR_2} + \sum_{2^{nH_3} \text{ terms}} 2^{-n(R_1 + R_2)}
$$
(14.192)

where again the probability that the constraints of the AEP are not satisfied is bounded by  $\epsilon$ , and the other terms refer to the various ways in which another pair of sequences could be jointly typical and in the same bin as the given source pair.

The duality of the multiple access channel and correlated source encoding is now obvious. It is rather surprising that these two systems are duals of each other; one would have expected a duality between the broadcast channel and the multiple access channel.

## 14.6 THE BROADCAST CHANNEL

The broadcast channel is a communication channel in which there is one sender and two or more receivers. It is illustrated in Figure 14.24. The basic problem is to find the set of simultaneously achievable rates for communication in a broadcast channel.

Before we begin the analysis, let us consider some examples:

**Example 14.6.1** (TV station): The simplest example of the broadcast channel is a radio or TV station. But this example is slightly degenerate in the sense that normally the station wants to send the same information to everybody who is tuned in; the capacity is essentially  $\max_{p(x)}$  $\min_i I(X; Y_i)$ , which may be less than the capacity of the worst receiver.

Image /page/28/Figure/9 description: This is a block diagram illustrating a communication system with two decoders. The system starts with an input of (W1, W2) which goes into an Encoder. The Encoder outputs X, which then goes into a block labeled p(y1, y2|x). From this block, two outputs, Y1 and Y2, are produced. Y1 goes to a Decoder which outputs W-hat 1, and Y2 goes to another Decoder which outputs W-hat 2.

Figure 14.24. Broadcast channel.

But we may wish to arrange the information in such a way that the better receivers receive extra information, which produces a better picture or sound, while the worst receivers continue to receive more basic information. As TV stations introduce High Definition TV (HDTV), it may be necessary to encode the information so that bad receivers will receive the regular TV signal, while good receivers will receive the extra information for the high definition signal. The methods to accomplish this will be explained in the discussion of the broadcast channel.

**Example 14.6.2** (Lecturer in classroom): A lecturer in a classroom is communicating information to the students in the class. Due to differences among the students, they receive various amounts of information. Some of the students receive most of the information; others receive only a little. In the ideal situation, the lecturer would be able to tailor his or her lecture in such a way that the good students receive more information and the poor students receive at least the minimum amount of information. However, a poorly prepared lecture proceeds at the pace of the weakest student. This situation is another example of a broadcast channel.

Example 14.6.3 (Orthogonal broadcast channels): The simplest broadcast channel consists of two independent channels to the two receivers. Here we can send independent information over both channels, and we can achieve rate  $R_1$  to receiver 1 and rate  $R_2$  to receiver 2, if  $R_1 < C_1$  and  $R_{2}$  <  $C_{2}$ . The capacity region is the rectangle shown in Figure 14.25.

Example 14.6.4 (Spanish and Dutch speaker): To illustrate the idea of superposition, we will consider a simplified example of a speaker who can speak both Spanish and Dutch. There are two listeners: one understands only Spanish and the other understands only Dutch. Assume for simplicity that the vocabulary of each language is  $2^{20}$  words

Image /page/29/Figure/5 description: A graph shows a rectangle in the first quadrant. The x-axis is labeled R1 and has a tick mark at C1. The y-axis is labeled R2 and has a tick mark at C2. The rectangle's bottom-left corner is at the origin (0,0). The bottom-right corner is at (C1, 0). The top-left corner is at (0, C2). The top-right corner is at (C1, C2). The rectangle represents the capacity region for two orthogonal broadcast channels.

Figure 14.25. Capacity region for two orthogonal broadcast channels.

and that the speaker speaks at the rate of 1 word per second in either language. Then he can transmit 20 bits of information per second to receiver 1 by speaking to him all the time; in this case, he sends no information to receiver 2. Similarly, he can send 20 bits per second to receiver 2 without sending any information to receiver 1. Thus he can achieve any rate pair with  $R_1 + R_2 = 20$  by simple timesharing. But can he do better?

Recall that the Dutch listener, even though he does not understand Spanish, can recognize when the word is Spanish. Similarly, the Spanish listener can recognize when Dutch occurs. The speaker can use this to convey information; for example, if the proportion of time he uses each language is 50%, then of a sequence of 100 words, about 50 will be Dutch and about 50 will be Spanish. But there are many ways to order the Spanish and Dutch words; in fact, there are about  $\binom{100}{50} \approx 2^{100H(\frac{1}{2})}$ ways to order the words. Choosing one of these orderings conveys information to both listeners. This method enables the speaker to send information at a rate of 10 bits per second to the Dutch receiver, 10 bits per second to the Spanish receiver, and 1 bit per second of common information to both receivers, for a total rate of 21 bits per second, which is more than that achievable by simple time sharing. This is an example of superposition of information.

The results of the broadcast channel can also be applied to the case of a single user channel with an unknown distribution. In this case, the objective is to get at least the minimum information through when the channel is bad and to get some extra information through when the channel is good. We can use the same superposition arguments as in the case of the broadcast channel to find the rates at which we can send information.

### 14.6.1 Definitions for a Broadcast Channel

**Definition:** A broadcast channel consists of an input alphabet  $\mathscr X$  and two output alphabets  $\mathcal{Y}_1$  and  $\mathcal{Y}_2$  and a probability transition function  $p(y_1, y_2|x)$ . The broadcast channel will be said to be *memoryless* if  $p(y_1^n, y_2^n | x^n) = \prod_{i=1}^n p(y_{1i}, y_{2i} | x_i).$ 

We define codes, probability of error, achievability and capacity regions for the broadcast channel as we did for the multiple access channel.

A  $((2^{nR_1}, 2^{nR_2}), n)$  code for a broadcast channel with independent information consists of an encoder,

$$
X: (\{1, 2, \ldots, 2^{nR_1}\} \times \{1, 2, \ldots, 2^{nR_2}\}) \to \mathcal{X}^n , \qquad (14.193)
$$

and two decoders,

$$
g_1: \mathcal{Y}_1^n \to \{1, 2, \dots, 2^{nR_1}\}\tag{14.194}
$$

and

$$
g_2: \mathscr{Y}_2^n \to \{1, 2, \dots, 2^{nR_2}\} \ . \tag{14.195}
$$

We define the average probability of error as the probability the decoded message is not equal to the transmitted message, i.e.,

$$
P_e^{(n)} = P(g_1(Y_1^n) \neq W_1 \text{ or } g_2(Y_2^n) \neq W_2), \qquad (14.196)
$$

where  $(W_1, W_2)$  are assumed to be uniformly distributed over  $2^{nR_1}\times 2^{nR_2}$ 

**Definition:** A rate pair  $(R_1, R_2)$  is said to be achievable for the broadcast channel if there exists a sequence of  $((2^{nR_1}, 2^{nR_2}), n)$  codes with  $P_e^{(n)} \rightarrow 0$ .

We will now define the rates for the case where we have common information to be sent to both receivers.

A  $((2^{nR_0}, 2^{nR_1}, 2^{nR_2}), n)$  code for a broadcast channel with common information consists of an encoder,

$$
X: (\{1, 2, \ldots, 2^{nR_0}\} \times \{1, 2, \ldots, 2^{nR_1}\} \times \{1, 2, \ldots, 2^{nR_2}\}) \rightarrow \mathcal{X}^n,
$$
\n(14.197)

and two decoders,

$$
g_1: \mathcal{Y}_1^n \to \{1, 2, \dots, 2^{nR_0}\} \times \{1, 2, \dots, 2^{nR_1}\} \tag{14.198}
$$

and

$$
g_2: \mathscr{Y}_2^n \to \{1, 2, \ldots, 2^{nR_0}\} \times \{1, 2, \ldots, 2^{nR_2}\} . \tag{14.199}
$$

Assuming that the distribution on  $(W_0, W_1, W_2)$  is uniform, we can define the probability of error as the probability the decoded message is not equal to the transmitted message, i.e.,

$$
P_e^{(n)} = P(g_1(Y_1^n) \neq (W_0, W_1) \text{ or } g_2(Z^n) \neq (W_0, W_2)). \quad (14.200)
$$

**Definition:** A rate triple  $(R_0, R_1, R_2)$  is said to be *achievable* for the broadcast channel with common information if there exists a sequence of  $((2^{nR_0}, 2^{nR_1}, 2^{nR_2}), n)$  codes with  $P_{\epsilon}^{(n)} \to 0$ .

**Definition:** The capacity region of the broadcast channel is the closure of the set of achievable rates.