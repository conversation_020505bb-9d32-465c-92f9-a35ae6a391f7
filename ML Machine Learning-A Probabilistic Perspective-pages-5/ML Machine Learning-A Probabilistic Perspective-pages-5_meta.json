{"table_of_contents": [{"title": "4 Gaussian models", "heading_level": null, "page_id": 0, "polygon": [[85.640625, 99.984375], [254.25, 99.984375], [254.25, 141.908203125], [85.640625, 141.908203125]]}, {"title": "4.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[101.25, 207.0], [195.75, 207.0], [195.75, 217.845703125], [101.25, 217.845703125]]}, {"title": "4.1.1 Notation", "heading_level": null, "page_id": 0, "polygon": [[96.1875, 339.75], [171.0, 339.75], [171.0, 349.9453125], [96.1875, 349.9453125]]}, {"title": "4.1.2 Basics", "heading_level": null, "page_id": 0, "polygon": [[94.5, 480.75], [159.75, 480.75], [159.75, 491.0625], [94.5, 491.0625]]}, {"title": "4.1.3 MLE for an MVN", "heading_level": null, "page_id": 2, "polygon": [[94.5, 60.75], [209.53125, 60.75], [209.53125, 70.67724609375], [94.5, 71.94287109375]]}, {"title": "4.1.3.1 Proof *", "heading_level": null, "page_id": 2, "polygon": [[89.4375, 327.0], [161.25, 327.0], [161.25, 336.0234375], [89.4375, 337.2890625]]}, {"title": "4.1.4 Maximum entropy derivation of the Gaussian *", "heading_level": null, "page_id": 4, "polygon": [[94.078125, 61.5], [353.25, 60.75], [353.25, 71.82421875], [94.078125, 71.82421875]]}, {"title": "4.2 Gaussian discriminant analysis", "heading_level": null, "page_id": 4, "polygon": [[99.75, 477.75], [291.0, 477.75], [291.0, 488.84765625], [99.75, 488.84765625]]}, {"title": "4.2.1 Quadratic discriminant analysis (QDA)", "heading_level": null, "page_id": 5, "polygon": [[94.5, 423.0], [311.25, 423.0], [311.25, 433.16015625], [94.5, 433.16015625]]}, {"title": "4.2.2 Linear discriminant analysis (LDA)", "heading_level": null, "page_id": 6, "polygon": [[93.0, 407.25], [293.25, 407.25], [293.25, 418.2890625], [93.0, 418.2890625]]}, {"title": "4.2.3 Two-class LDA", "heading_level": null, "page_id": 7, "polygon": [[92.671875, 456.0], [198.421875, 456.0], [198.421875, 465.75], [92.671875, 465.75]]}, {"title": "4.2.4 MLE for discriminant analysis", "heading_level": null, "page_id": 9, "polygon": [[93.0, 321.75], [272.25, 321.75], [272.25, 331.59375], [93.0, 331.59375]]}, {"title": "4.2.5 Strategies for preventing overfitting", "heading_level": null, "page_id": 9, "polygon": [[93.0, 535.5], [300.0, 535.5], [300.0, 545.484375], [93.0, 545.484375]]}, {"title": "4.2.6 Regularized LDA *", "heading_level": null, "page_id": 10, "polygon": [[92.25, 305.25], [216.75, 305.25], [216.75, 316.08984375], [92.25, 316.08984375]]}, {"title": "4.2.7 Diagonal LDA", "heading_level": null, "page_id": 11, "polygon": [[93.0, 400.5], [195.046875, 400.5], [195.046875, 411.64453125], [93.0, 411.64453125]]}, {"title": "4.2.8 Nearest shrunken centroids classifier *", "heading_level": null, "page_id": 12, "polygon": [[92.3203125, 271.5], [312.75, 271.5], [312.75, 281.759765625], [92.3203125, 281.759765625]]}, {"title": "4.3 Inference in jointly Gaussian distributions", "heading_level": null, "page_id": 13, "polygon": [[99.75, 502.5], [349.5, 502.5], [349.5, 514.4765625], [99.75, 514.4765625]]}, {"title": "4.3.1 Statement of the result", "heading_level": null, "page_id": 14, "polygon": [[94.4296875, 60.75], [238.5, 60.75], [238.5, 71.5869140625], [94.4296875, 71.5869140625]]}, {"title": "4.3.2 Examples", "heading_level": null, "page_id": 14, "polygon": [[92.953125, 438.75], [177.0, 438.75], [177.0, 449.9296875], [92.953125, 449.9296875]]}, {"title": "4.3.2.1 Marginals and conditionals of a 2d Gaussian", "heading_level": null, "page_id": 14, "polygon": [[88.8046875, 495.0], [323.25, 495.0], [323.25, 504.984375], [88.8046875, 504.984375]]}, {"title": "4.3.2.2 Interpolating noise-free data", "heading_level": null, "page_id": 15, "polygon": [[86.765625, 419.25], [253.5, 419.25], [253.5, 429.36328125], [86.765625, 429.36328125]]}, {"title": "4.3.2.3 Data imputation", "heading_level": null, "page_id": 17, "polygon": [[87.75, 488.25], [199.6875, 488.25], [199.6875, 497.70703125], [87.75, 497.70703125]]}, {"title": "4.3.3 Information form", "heading_level": null, "page_id": 18, "polygon": [[92.6015625, 367.5], [213.0, 367.5], [213.0, 377.15625], [92.6015625, 377.15625]]}, {"title": "4.3.4 Proof of the result *", "heading_level": null, "page_id": 19, "polygon": [[93.0, 201.0], [225.0, 201.0], [225.0, 211.359375], [93.0, 211.359375]]}, {"title": "4.3.4.1 Inverse of a partitioned matrix using <PERSON><PERSON> complements", "heading_level": null, "page_id": 19, "polygon": [[88.875, 269.25], [378.75, 269.25], [378.75, 279.38671875], [88.875, 279.38671875]]}, {"title": "4.3.4.2 The matrix inversion lemma", "heading_level": null, "page_id": 20, "polygon": [[87.0, 435.0], [253.5, 435.0], [253.5, 444.8671875], [87.0, 444.8671875]]}, {"title": "4.3.4.3 Proof of Gaussian conditioning formulas", "heading_level": null, "page_id": 21, "polygon": [[87.75, 320.25], [306.0, 320.25], [306.0, 330.328125], [87.75, 330.328125]]}, {"title": "4.4 Linear Gaussian systems", "heading_level": null, "page_id": 22, "polygon": [[99.75, 281.25], [258.0, 281.25], [258.0, 292.04296875], [99.75, 292.04296875]]}, {"title": "4.4.1 Statement of the result", "heading_level": null, "page_id": 22, "polygon": [[95.25, 479.25], [238.640625, 479.25], [238.640625, 489.48046875], [95.25, 489.48046875]]}, {"title": "4.4.2 Examples", "heading_level": null, "page_id": 23, "polygon": [[92.25, 159.75], [176.34375, 159.75], [176.34375, 169.91015625], [92.25, 169.91015625]]}, {"title": "4.4.2.1 Inferring an unknown scalar from noisy measurements", "heading_level": null, "page_id": 23, "polygon": [[88.9453125, 204.75], [370.5, 204.75], [370.5, 213.890625], [88.9453125, 213.890625]]}, {"title": "4.4.2.2 Inferring an unknown vector from noisy measurements", "heading_level": null, "page_id": 25, "polygon": [[86.90625, 234.0], [370.5, 234.0], [370.5, 243.94921875], [86.90625, 243.94921875]]}, {"title": "4.4.2.3 Interpolating noisy data", "heading_level": null, "page_id": 26, "polygon": [[87.6796875, 521.25], [234.0, 521.25], [234.0, 531.5625], [87.6796875, 531.5625]]}, {"title": "4.4.3 Proof of the result *", "heading_level": null, "page_id": 27, "polygon": [[92.953125, 390.75], [225.0, 390.75], [225.0, 401.51953125], [92.953125, 401.51953125]]}, {"title": "4.5 Digression: The Wishart distribution *", "heading_level": null, "page_id": 28, "polygon": [[99.75, 441.75], [327.75, 441.75], [327.75, 453.09375], [99.75, 453.09375]]}, {"title": "4.5.1 Inverse Wishart distribution", "heading_level": null, "page_id": 29, "polygon": [[94.5, 370.5], [263.25, 370.5], [263.25, 380.63671875], [94.5, 380.63671875]]}, {"title": "4.5.2 Visualizing the <PERSON><PERSON> distribution *", "heading_level": null, "page_id": 30, "polygon": [[92.390625, 286.5], [306.75, 286.5], [306.75, 296.630859375], [92.390625, 296.630859375]]}, {"title": "4.6 Inferring the parameters of an MVN", "heading_level": null, "page_id": 30, "polygon": [[99.0, 553.5], [316.5, 553.5], [316.5, 565.1015625], [99.0, 565.1015625]]}, {"title": "4.6.1 Posterior distribution of μ", "heading_level": null, "page_id": 31, "polygon": [[94.5, 124.5], [255.75, 124.5], [255.75, 134.630859375], [94.5, 134.630859375]]}, {"title": "4.6.2 Posterior distribution of Σ *", "heading_level": null, "page_id": 31, "polygon": [[93.0, 403.5], [264.75, 403.5], [264.75, 414.17578125], [93.0, 414.17578125]]}, {"title": "4.6.2.1 MAP estimation", "heading_level": null, "page_id": 32, "polygon": [[88.5, 467.25], [199.5, 467.25], [199.5, 476.82421875], [88.5, 476.82421875]]}, {"title": "4.6.2.2 Univariate posterior", "heading_level": null, "page_id": 33, "polygon": [[86.34375, 444.75], [218.25, 444.75], [218.25, 454.04296875], [86.34375, 454.04296875]]}, {"title": "4.6.3 Posterior distribution of \\mu and \\Sigma^*", "heading_level": null, "page_id": 35, "polygon": [[91.265625, 60.75], [297.75, 59.25], [297.75, 71.78466796875], [91.265625, 71.78466796875]]}, {"title": "4.6.3.1 Likelihood", "heading_level": null, "page_id": 35, "polygon": [[87.890625, 117.75], [177.0, 117.75], [177.0, 127.5908203125], [87.890625, 127.5908203125]]}, {"title": "4.6.3.2 Prior", "heading_level": null, "page_id": 35, "polygon": [[87.0, 354.0], [153.75, 354.0], [153.75, 363.8671875], [87.0, 363.8671875]]}, {"title": "4.6.3.3 Posterior", "heading_level": null, "page_id": 37, "polygon": [[86.34375, 61.5], [170.25, 61.5], [170.25, 71.38916015625], [86.34375, 71.38916015625]]}, {"title": "4.6.3.4 Posterior mode", "heading_level": null, "page_id": 37, "polygon": [[86.203125, 293.25], [197.15625, 293.25], [197.15625, 303.1171875], [86.203125, 303.1171875]]}, {"title": "4.6.3.5 <PERSON><PERSON><PERSON> marginals", "heading_level": null, "page_id": 37, "polygon": [[86.6953125, 433.5], [215.4375, 433.5], [215.4375, 443.28515625], [86.6953125, 443.28515625]]}, {"title": "4.6.3.6 Posterior predictive", "heading_level": null, "page_id": 38, "polygon": [[85.359375, 376.5], [215.859375, 376.5], [215.859375, 386.6484375], [85.359375, 386.6484375]]}, {"title": "4.6.3.7 Posterior for scalar data", "heading_level": null, "page_id": 38, "polygon": [[87.46875, 559.5], [234.84375, 559.5], [234.84375, 569.53125], [87.46875, 569.53125]]}, {"title": "4.6.3.8 Bayesian t-test", "heading_level": null, "page_id": 40, "polygon": [[85.5703125, 240.75], [193.5, 240.75], [193.5, 250.751953125], [85.5703125, 250.751953125]]}, {"title": "4.6.3.9 Connection with frequentist statistics *", "heading_level": null, "page_id": 41, "polygon": [[85.5703125, 62.25], [299.25, 62.25], [299.25, 71.46826171875], [85.5703125, 71.46826171875]]}, {"title": "4.6.4 Sensor fusion with unknown precisions *", "heading_level": null, "page_id": 41, "polygon": [[93.0, 345.0], [325.5, 345.0], [325.5, 354.75], [93.0, 354.75]]}, {"title": "Exercises", "heading_level": null, "page_id": 43, "polygon": [[129.515625, 465.75], [178.5, 465.75], [178.5, 475.875], [129.515625, 475.875]]}, {"title": "Exercise 4.9 Sensor fusion with known variances in 1d", "heading_level": null, "page_id": 45, "polygon": [[129.75, 566.25], [330.0, 566.25], [330.0, 575.2265625], [129.75, 575.2265625]]}, {"title": "Exercise 4.12 BIC for Gaussians", "heading_level": null, "page_id": 46, "polygon": [[129.75, 249.75], [248.34375, 249.75], [248.34375, 259.611328125], [129.75, 259.611328125]]}, {"title": "d. What is the time complexity per update?", "heading_level": null, "page_id": 48, "polygon": [[130.9921875, 62.25], [290.25, 62.25], [290.25, 71.349609375], [130.9921875, 71.349609375]]}, {"title": "Exercise 4.16 Likelihood ratio for Gaussians", "heading_level": null, "page_id": 48, "polygon": [[129.375, 88.5], [290.25, 88.5], [290.25, 96.662109375], [129.375, 96.662109375]]}, {"title": "Exercise 4.19 Decision boundary for LDA with semi tied covariances", "heading_level": null, "page_id": 48, "polygon": [[129.75, 466.5], [379.5, 466.5], [379.5, 474.609375], [129.75, 474.609375]]}, {"title": "Exercise 4.20 Logistic regression vs LDA/QDA", "heading_level": null, "page_id": 48, "polygon": [[129.75, 535.5], [296.25, 535.5], [296.25, 543.90234375], [129.75, 543.90234375]]}, {"title": "Exercise 4.21 Gaussian decision boundaries", "heading_level": null, "page_id": 49, "polygon": [[129.75, 375.75], [289.5, 375.75], [289.5, 385.06640625], [129.75, 385.06640625]]}, {"title": "Exercise 4.22 QDA with 3 classes", "heading_level": null, "page_id": 50, "polygon": [[129.75, 62.25], [253.5, 62.25], [253.5, 71.349609375], [129.75, 71.349609375]]}, {"title": "Exercise 4.23 Scalar QDA", "heading_level": null, "page_id": 50, "polygon": [[129.75, 262.5], [225.0, 262.5], [225.0, 271.4765625], [129.75, 271.4765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 31], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 66], ["Text", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 71], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1315, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 78], ["Equation", 12], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 46], ["Equation", 7], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 75], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 825, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 345], ["Line", 64], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Text", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1641, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["Line", 65], ["Equation", 8], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 36], ["Equation", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1456, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 64], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 33], ["ListItem", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 579], ["Line", 60], ["Equation", 11], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 50], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 817, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 908, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 57], ["Text", 6], ["Equation", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1441, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 630], ["Line", 58], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 833, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 62], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1775, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 455], ["Line", 54], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 67], ["Equation", 5], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 63], ["Text", 10], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 539], ["Line", 70], ["Equation", 11], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2163, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 637], ["Line", 77], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["Line", 54], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["Line", 39], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 501], ["Line", 88], ["Equation", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["Line", 55], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 66], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1870, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 621], ["Line", 107], ["Equation", 8], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 455], ["Line", 81], ["Equation", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["Line", 52], ["Equation", 9], ["TextInlineMath", 6], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["Line", 90], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1036, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 51], ["Equation", 6], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 79], ["Equation", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2116, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 57], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 57], ["Equation", 7], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1901, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 55], ["Text", 7], ["Equation", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 63], ["Equation", 9], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 45], ["Equation", 11], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 591, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 57], ["Text", 5], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 789, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 634], ["Line", 75], ["Equation", 13], ["Text", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 460], ["Line", 53], ["Text", 7], ["Equation", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 40], ["Equation", 6], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 742], ["Line", 80], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 65], ["Text", 6], ["Equation", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 72], ["Text", 8], ["Equation", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 233], ["Text", 4], ["ListItem", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1524, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 57], ["Text", 12], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 584], ["Line", 58], ["Text", 8], ["ListItem", 7], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 42], ["TableCell", 33], ["Text", 6], ["ListItem", 6], ["SectionHeader", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3420, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 41], ["ListItem", 8], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 37], ["Equation", 6], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-5"}