{"table_of_contents": [{"title": "24 Markov chain Monte Carlo (MCMC)\ninference", "heading_level": null, "page_id": 0, "polygon": [[53.859375, 82.3306884765625], [386.4375, 82.3306884765625], [386.4375, 141.275390625], [53.859375, 141.275390625]]}, {"title": "24.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[96.0, 207.0], [195.75, 207.0], [195.75, 218.162109375], [96.0, 218.162109375]]}, {"title": "24.2 <PERSON> sampling", "heading_level": null, "page_id": 1, "polygon": [[93.0, 60.75], [210.375, 60.75], [210.375, 71.82421875], [93.0, 71.82421875]]}, {"title": "24.2.1 Basic idea", "heading_level": null, "page_id": 1, "polygon": [[88.8046875, 134.25], [179.15625, 134.25], [179.15625, 144.59765625], [88.8046875, 144.59765625]]}, {"title": "24.2.2 Example: <PERSON> sampling for the Ising model", "heading_level": null, "page_id": 1, "polygon": [[87.5390625, 426.75], [345.0, 426.75], [345.0, 436.95703125], [87.5390625, 436.95703125]]}, {"title": "24.2.3 Example: Gibbs sampling for inferring the parameters of a GMM", "heading_level": null, "page_id": 3, "polygon": [[85.5, 60.75], [436.5, 60.75], [436.5, 71.82421875], [85.5, 71.82421875]]}, {"title": "24.2.3.1 Label switching", "heading_level": null, "page_id": 4, "polygon": [[83.25, 61.5], [199.546875, 61.5], [199.546875, 71.666015625], [83.25, 71.666015625]]}, {"title": "24.2.4 <PERSON><PERSON><PERSON> sampling *", "heading_level": null, "page_id": 4, "polygon": [[87.609375, 381.0], [260.25, 381.0], [260.25, 392.34375], [87.609375, 392.34375]]}, {"title": "24.2.4.1 Example: collapsed <PERSON> for fitting a GMM", "heading_level": null, "page_id": 5, "polygon": [[83.25, 330.0], [319.78125, 330.0], [319.78125, 340.13671875], [83.25, 340.13671875]]}, {"title": "24.2.5 Gibbs sampling for hierarchical GLMs", "heading_level": null, "page_id": 7, "polygon": [[87.46875, 495.0], [309.0, 495.0], [309.0, 505.93359375], [87.46875, 505.93359375]]}, {"title": "24.2.6 BUGS and JAGS", "heading_level": null, "page_id": 9, "polygon": [[87.0, 521.25], [202.21875, 521.25], [202.21875, 532.51171875], [87.0, 532.51171875]]}, {"title": "24.2.7 The Imputation Posterior (IP) algorithm", "heading_level": null, "page_id": 10, "polygon": [[87.609375, 418.5], [315.75, 418.5], [315.75, 429.6796875], [87.609375, 429.6796875]]}, {"title": "24.2.8 <PERSON><PERSON> sampling", "heading_level": null, "page_id": 10, "polygon": [[87.1875, 535.5], [247.78125, 535.5], [247.78125, 545.80078125], [87.1875, 545.80078125]]}, {"title": "24.3 Metropolis Hastings algorithm", "heading_level": null, "page_id": 11, "polygon": [[93.0, 328.5], [288.0, 328.5], [288.0, 339.50390625], [93.0, 339.50390625]]}, {"title": "24.3.1 Basic idea", "heading_level": null, "page_id": 11, "polygon": [[89.25, 438.0], [178.59375, 438.0], [178.59375, 448.03125], [89.25, 448.03125]]}, {"title": "24.3.2 Gibbs sampling is a special case of MH", "heading_level": null, "page_id": 12, "polygon": [[87.75, 426.75], [315.0, 426.75], [315.0, 437.2734375], [87.75, 437.2734375]]}, {"title": "24.3.3 Proposal distributions", "heading_level": null, "page_id": 13, "polygon": [[87.75, 364.5], [234.75, 364.5], [234.75, 374.94140625], [87.75, 374.94140625]]}, {"title": "24.3.3.1 Gaussian proposals", "heading_level": null, "page_id": 15, "polygon": [[82.828125, 217.5], [214.5, 217.5], [214.5, 227.654296875], [82.828125, 227.654296875]]}, {"title": "24.3.3.2 Mixture proposals", "heading_level": null, "page_id": 16, "polygon": [[81.5625, 61.5], [209.25, 61.5], [209.25, 71.70556640625], [81.5625, 71.70556640625]]}, {"title": "24.3.3.3 Data-driven MCMC", "heading_level": null, "page_id": 16, "polygon": [[81.984375, 188.25], [211.640625, 188.25], [211.640625, 198.38671875], [81.984375, 198.38671875]]}, {"title": "24.3.4 Adaptive MCMC", "heading_level": null, "page_id": 16, "polygon": [[87.75, 479.25], [205.875, 479.25], [205.875, 489.48046875], [87.75, 489.48046875]]}, {"title": "24.3.5 Initialization and mode hopping", "heading_level": null, "page_id": 17, "polygon": [[86.90625, 61.5], [283.5, 61.5], [283.5, 71.86376953125], [86.90625, 71.86376953125]]}, {"title": "24.3.6 Why MH works *", "heading_level": null, "page_id": 17, "polygon": [[86.203125, 189.75], [210.0, 189.75], [210.0, 200.443359375], [86.203125, 200.443359375]]}, {"title": "24.3.7 Reversible jump (trans-dimensional) MCMC *", "heading_level": null, "page_id": 18, "polygon": [[87.75, 278.25], [342.0, 278.25], [342.0, 288.24609375], [87.75, 288.24609375]]}, {"title": "24.4 Speed and accuracy of MCMC", "heading_level": null, "page_id": 19, "polygon": [[93.0, 361.5], [285.1875, 361.5], [285.1875, 372.41015625], [93.0, 372.41015625]]}, {"title": "24.4.1 The burn-in phase", "heading_level": null, "page_id": 19, "polygon": [[89.25, 423.0], [218.390625, 423.0], [218.390625, 433.16015625], [89.25, 433.16015625]]}, {"title": "24.4.2 Mixing rates of Markov chains *", "heading_level": null, "page_id": 20, "polygon": [[86.4140625, 444.0], [282.0, 443.25], [282.0, 453.7265625], [86.4140625, 453.75]]}, {"title": "24.4.3 Practical convergence diagnostics", "heading_level": null, "page_id": 21, "polygon": [[87.609375, 559.5], [289.5, 559.5], [289.5, 569.84765625], [87.609375, 569.84765625]]}, {"title": "24.4.3.1 Estimated potential scale reduction (EPSR)", "heading_level": null, "page_id": 22, "polygon": [[83.8828125, 291.0], [313.3125, 291.0], [313.3125, 300.427734375], [83.8828125, 300.427734375]]}, {"title": "24.4.4 Accuracy of MCMC", "heading_level": null, "page_id": 23, "polygon": [[87.75, 537.75], [220.5, 537.75], [220.5, 548.96484375], [87.75, 548.96484375]]}, {"title": "24.4.5 How many chains?", "heading_level": null, "page_id": 25, "polygon": [[87.75, 491.25], [219.65625, 491.25], [219.65625, 501.1875], [87.75, 501.1875]]}, {"title": "24.5 Auxiliary variable MCMC *", "heading_level": null, "page_id": 26, "polygon": [[93.0, 269.25], [269.25, 269.25], [269.25, 280.3359375], [93.0, 280.3359375]]}, {"title": "24.5.1 Auxiliary variable sampling for logistic regression", "heading_level": null, "page_id": 26, "polygon": [[89.25, 378.75], [367.5, 378.75], [367.5, 389.1796875], [89.25, 389.1796875]]}, {"title": "24.5.2 Slice sampling", "heading_level": null, "page_id": 27, "polygon": [[87.75, 502.5], [199.5, 502.5], [199.5, 512.578125], [87.75, 512.578125]]}, {"title": "24.5.3 <PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 29, "polygon": [[86.484375, 307.5], [206.296875, 307.5], [206.296875, 317.671875], [86.484375, 317.671875]]}, {"title": "24.5.4 Hybrid/Hamiltonian MCMC *", "heading_level": null, "page_id": 31, "polygon": [[87.75, 291.75], [264.75, 291.75], [264.75, 301.8515625], [87.75, 301.8515625]]}, {"title": "24.6 Annealing methods", "heading_level": null, "page_id": 31, "polygon": [[93.0, 494.25], [230.25, 494.25], [230.25, 505.6171875], [93.0, 505.6171875]]}, {"title": "24.6.1 Simulated annealing", "heading_level": null, "page_id": 32, "polygon": [[88.5234375, 242.25], [228.0, 242.25], [228.0, 252.650390625], [88.5234375, 252.650390625]]}, {"title": "24.6.2 Annealed importance sampling", "heading_level": null, "page_id": 34, "polygon": [[86.34375, 61.5], [278.25, 61.5], [278.25, 71.70556640625], [86.34375, 71.70556640625]]}, {"title": "24.6.3 Parallel tempering", "heading_level": null, "page_id": 34, "polygon": [[87.75, 513.75], [218.8125, 513.75], [218.8125, 523.96875], [87.75, 523.96875]]}, {"title": "24.7 Approximating the marginal likelihood", "heading_level": null, "page_id": 35, "polygon": [[92.8828125, 60.75], [333.0, 60.75], [333.0, 71.982421875], [92.8828125, 71.982421875]]}, {"title": "24.7.1 The candidate method", "heading_level": null, "page_id": 35, "polygon": [[89.25, 194.25], [235.5, 194.25], [235.5, 205.189453125], [89.25, 205.189453125]]}, {"title": "24.7.2 Harmonic mean estimate", "heading_level": null, "page_id": 35, "polygon": [[87.3984375, 363.0], [249.75, 363.0], [249.75, 372.7265625], [87.3984375, 372.7265625]]}, {"title": "24.7.3 Annealed importance sampling", "heading_level": null, "page_id": 36, "polygon": [[87.75, 61.5], [278.25, 61.5], [278.25, 71.78466796875], [87.75, 71.78466796875]]}, {"title": "Exercises", "heading_level": null, "page_id": 36, "polygon": [[129.75, 205.5], [178.5, 205.5], [178.5, 214.83984375], [129.75, 214.83984375]]}, {"title": "Exercise 24.3 Gibbs sampling from the Potts model", "heading_level": null, "page_id": 36, "polygon": [[129.75, 354.75], [318.75, 354.75], [318.75, 364.18359375], [129.75, 364.18359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 33], ["Text", 3], ["SectionHeader", 2], ["Footnote", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10055, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 34], ["TextInlineMath", 5], ["ListItem", 4], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 56], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 66], ["Equation", 13], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 973, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 42], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 398], ["Line", 38], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 809, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 601], ["Line", 50], ["TableCell", 14], ["Text", 4], ["Equation", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 74], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1704, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 38], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 705, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 54], ["Equation", 11], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 40], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 46], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 721, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 567], ["Line", 65], ["Equation", 7], ["Text", 6], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 36], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 67], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 425], ["Line", 72], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 903, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 46], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 59], ["Text", 7], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 420], ["Line", 61], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 57], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1159, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 31], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 636, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 47], ["Text", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 76], ["Text", 4], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 52], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1105, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 60], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1171, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 71], ["Equation", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["TableCell", 80], ["Line", 39], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2503, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["Line", 48], ["Equation", 7], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 62], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 48], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 24], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 41], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 64], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["Line", 73], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 700], ["Line", 53], ["TextInlineMath", 6], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 51], ["Text", 6], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 50], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 453], ["Line", 53], ["Equation", 4], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/ML Machine Learning-A Probabilistic Perspective-pages-28"}