{"table_of_contents": [{"title": "5\nBasis Expansions and Regularization", "heading_level": null, "page_id": 0, "polygon": [[131.3349609375, 109.5], [439.5, 109.5], [439.5, 163.001953125], [131.3349609375, 163.001953125]]}, {"title": "5.1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[132.0, 354.75], [243.544921875, 354.75], [243.544921875, 367.576171875], [132.0, 367.576171875]]}, {"title": "140 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 1, "polygon": [[132.0, 88.5], [331.69921875, 88.5], [331.69921875, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "5.2 Piecewise Polynomials and Splines", "heading_level": null, "page_id": 2, "polygon": [[132.0, 298.5], [378.9140625, 298.5], [378.9140625, 312.08203125], [132.0, 312.08203125]]}, {"title": "144 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 5, "polygon": [[132.0, 89.25], [331.69921875, 89.25], [331.69921875, 98.56494140625], [132.0, 98.56494140625]]}, {"title": "5.2.1 Natural Cubic Splines", "heading_level": null, "page_id": 5, "polygon": [[133.5, 501.0], [282.75, 501.0], [282.75, 512.40234375], [133.5, 512.40234375]]}, {"title": "5.2.2 Example: South African Heart Disease (Continued)", "heading_level": null, "page_id": 7, "polygon": [[133.05322265625, 192.75], [433.5, 192.75], [433.5, 204.57421875], [133.05322265625, 204.57421875]]}, {"title": "148 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 9, "polygon": [[132.0, 88.5], [331.5, 88.5], [331.5, 98.75830078125], [132.0, 98.75830078125]]}, {"title": "5.2.3 Example: Phoneme Recognition", "heading_level": null, "page_id": 9, "polygon": [[133.27734375, 417.0], [332.296875, 417.0], [332.296875, 428.87109375], [133.27734375, 428.87109375]]}, {"title": "5.3 Filtering and Feature Extraction", "heading_level": null, "page_id": 11, "polygon": [[132.0, 541.5], [367.55859375, 541.5], [367.55859375, 554.5546875], [132.0, 554.5546875]]}, {"title": "5.4 Smoothing Splines", "heading_level": null, "page_id": 12, "polygon": [[132.0, 256.5], [279.404296875, 256.5], [279.404296875, 270.123046875], [132.0, 270.123046875]]}, {"title": "5.4.1 Degrees of Freedom and Smoother Matrices", "heading_level": null, "page_id": 14, "polygon": [[133.4267578125, 180.0], [391.5, 180.0], [391.5, 191.3291015625], [133.4267578125, 191.3291015625]]}, {"title": "156 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 17, "polygon": [[132.0, 89.25], [331.69921875, 89.25], [331.69921875, 98.806640625], [132.0, 98.806640625]]}, {"title": "5.5 Automatic Selection of the Smoothing\nParameters", "heading_level": null, "page_id": 17, "polygon": [[132.75, 585.87890625], [401.625, 585.87890625], [401.625, 614.49609375], [132.75, 614.49609375]]}, {"title": "158 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 19, "polygon": [[132.0, 89.25], [331.5, 89.25], [331.5, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "5.5.1 Fixing the Degrees of Freedom", "heading_level": null, "page_id": 19, "polygon": [[133.5, 226.5], [325.72265625, 226.5], [325.72265625, 238.025390625], [133.5, 238.025390625]]}, {"title": "5.5.2 The Bias–<PERSON><PERSON>ce <PERSON>off", "heading_level": null, "page_id": 19, "polygon": [[133.5, 383.25], [315.75, 383.25], [315.75, 394.83984375], [133.5, 394.83984375]]}, {"title": "160 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 21, "polygon": [[132.75, 88.5], [331.5, 88.5], [331.5, 98.9033203125], [132.75, 98.9033203125]]}, {"title": "5.6 Nonparametric Logistic Regression", "heading_level": null, "page_id": 22, "polygon": [[132.0, 303.0], [381.3046875, 303.0], [381.3046875, 316.142578125], [132.0, 316.142578125]]}, {"title": "162 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 23, "polygon": [[132.75, 88.5], [332.595703125, 88.5], [332.595703125, 98.9033203125], [132.75, 98.9033203125]]}, {"title": "5.7 Multidimensional Splines", "heading_level": null, "page_id": 23, "polygon": [[132.0, 484.5], [320.34375, 484.5], [320.34375, 498.09375], [132.0, 498.09375]]}, {"title": "5. Basis Expansions and Regularization", "heading_level": null, "page_id": 25, "polygon": [[134.6220703125, 87.75], [333.0, 87.75], [333.0, 98.75830078125], [134.6220703125, 98.75830078125]]}, {"title": "5.8 Regularization and Reproducing Kernel\nHilbert Spaces", "heading_level": null, "page_id": 28, "polygon": [[132.0, 573.50390625], [410.291015625, 573.50390625], [410.291015625, 603.66796875], [132.0, 603.66796875]]}, {"title": "168 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 29, "polygon": [[132.75, 88.5], [331.69921875, 88.5], [331.69921875, 98.56494140625], [132.75, 98.56494140625]]}, {"title": "5.8.1 Spaces of Functions Generated by Kernels", "heading_level": null, "page_id": 29, "polygon": [[133.5, 417.0], [386.0859375, 417.0], [386.0859375, 429.2578125], [133.5, 429.2578125]]}, {"title": "170 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 31, "polygon": [[132.0, 89.25], [332.296875, 89.25], [332.296875, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "5.8.2 Examples of RKHS", "heading_level": null, "page_id": 31, "polygon": [[132.978515625, 378.0], [270.75, 378.0], [270.75, 389.0390625], [132.978515625, 389.0390625]]}, {"title": "Penalized Polynomial Regression", "heading_level": null, "page_id": 32, "polygon": [[132.75, 242.25], [277.5, 242.25], [277.5, 253.107421875], [132.75, 253.107421875]]}, {"title": "Gaussian Radial Basis Functions", "heading_level": null, "page_id": 33, "polygon": [[133.5, 284.25], [279.75, 284.25], [279.75, 294.6796875], [133.5, 294.6796875]]}, {"title": "174 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 35, "polygon": [[132.0, 89.25], [331.69921875, 89.25], [331.69921875, 98.61328125], [132.0, 98.61328125]]}, {"title": "Support Vector Classifiers", "heading_level": null, "page_id": 35, "polygon": [[132.75, 349.5], [247.5791015625, 349.5], [247.5791015625, 359.26171875], [132.75, 359.26171875]]}, {"title": "5.9 Wavelet Smoothing", "heading_level": null, "page_id": 35, "polygon": [[132.0, 553.5], [285.75, 553.5], [285.75, 566.54296875], [132.0, 566.54296875]]}, {"title": "5.9 Wavelet Smoothing 175", "heading_level": null, "page_id": 36, "polygon": [[328.11328125, 88.5], [456.75, 88.5], [456.75, 98.27490234375], [328.11328125, 98.27490234375]]}, {"title": "176 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 37, "polygon": [[132.75, 89.25], [331.998046875, 89.25], [331.998046875, 98.9033203125], [132.75, 98.9033203125]]}, {"title": "5.9.1 Wavelet Bases and the Wavelet Transform", "heading_level": null, "page_id": 37, "polygon": [[133.5, 391.5], [390.8671875, 391.5], [390.8671875, 403.734375], [133.5, 403.734375]]}, {"title": "178 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 39, "polygon": [[132.0, 89.25], [333.193359375, 89.25], [333.193359375, 98.85498046875], [132.0, 98.85498046875]]}, {"title": "5.9.2 Adaptive Wavelet Filtering", "heading_level": null, "page_id": 40, "polygon": [[133.5, 396.0], [309.5859375, 396.0], [309.5859375, 407.21484375], [133.5, 407.21484375]]}, {"title": "180 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 41, "polygon": [[132.0, 88.5], [332.296875, 88.5], [332.296875, 98.7099609375], [132.0, 98.7099609375]]}, {"title": "Bibliographic Notes", "heading_level": null, "page_id": 42, "polygon": [[132.75, 371.443359375], [255.498046875, 371.443359375], [255.498046875, 384.591796875], [132.75, 384.591796875]]}, {"title": "Exercises", "heading_level": null, "page_id": 42, "polygon": [[132.75, 582.75], [189.75, 582.75], [189.75, 595.16015625], [132.75, 595.16015625]]}, {"title": "184 5. Basis Expansions and Regularization", "heading_level": null, "page_id": 45, "polygon": [[132.0, 88.5], [331.5, 88.5], [331.5, 99.0], [132.0, 99.0]]}, {"title": "Appendix: Computations for Splines", "heading_level": null, "page_id": 47, "polygon": [[132.0, 366.75], [361.880859375, 368.25], [361.880859375, 382.078125], [132.0, 382.078125]]}, {"title": "B-splines", "heading_level": null, "page_id": 47, "polygon": [[132.75, 451.5], [181.986328125, 451.5], [181.986328125, 462.90234375], [132.75, 462.90234375]]}, {"title": "Computations for Smoothing Splines", "heading_level": null, "page_id": 50, "polygon": [[133.5, 192.0], [320.34375, 192.0], [320.34375, 203.994140625], [133.5, 203.994140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 28], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 45], ["ListItem", 5], ["Text", 4], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 957, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 38], ["Text", 4], ["ListItem", 2], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 158], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 184], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1130, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 44], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 105], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 483], ["Line", 53], ["Text", 6], ["TextInlineMath", 4], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 43], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 917, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["TableCell", 96], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 758, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 42], ["TableCell", 18], ["Text", 6], ["TextInlineMath", 2], ["Equation", 1], ["Table", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 49], ["TextInlineMath", 5], ["Text", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1166], ["Line", 490], ["Equation", 3], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 44], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 52], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 174], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1137, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 43], ["ListItem", 5], ["Text", 4], ["SectionHeader", 2], ["Equation", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 44], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1001, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 45], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 650], ["Line", 292], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 43], ["Text", 4], ["ListItem", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 78], ["Equation", 5], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1100, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["Line", 59], ["Equation", 6], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 66], ["Line", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 656, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Line", 44], ["Span", 14], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1299, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 67], ["Text", 5], ["Equation", 3], ["ListItem", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 1041], ["Line", 478], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 47], ["Text", 5], ["ListItem", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Picture", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1521, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 416], ["Line", 57], ["TextInlineMath", 6], ["Equation", 5], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["Line", 77], ["Equation", 6], ["TextInlineMath", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 62], ["Text", 4], ["Equation", 4], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["Line", 80], ["Equation", 8], ["Text", 6], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 42], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Line", 23], ["Span", 20], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1335, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 50], ["Text", 4], ["SectionHeader", 3], ["Equation", 3], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 35], ["Text", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 859, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 43], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 2], ["Picture", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 27], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 318], ["Line", 29], ["TextInlineMath", 3], ["Text", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 650, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 46], ["Text", 5], ["ListItem", 3], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Picture", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 563, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 43], ["Text", 3], ["ListItem", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 37], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 144], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1115, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 48], ["ListItem", 6], ["Text", 5], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 437], ["Line", 76], ["Text", 8], ["TextInlineMath", 5], ["Equation", 4], ["ListItem", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 490], ["Line", 49], ["TextInlineMath", 7], ["ListItem", 4], ["Equation", 4], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 35], ["Text", 8], ["TextInlineMath", 3], ["ListItem", 3], ["SectionHeader", 2], ["Equation", 1], ["Picture", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1553, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["Line", 47], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1033, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 32], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 935, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 34], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BookAdvanced_158-209"}