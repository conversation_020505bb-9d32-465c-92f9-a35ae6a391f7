{"table_of_contents": [{"title": "Improved Techniques for Training GANs", "heading_level": null, "page_id": 0, "polygon": [[153.75, 109.5], [459.0, 109.5], [459.0, 125.876953125], [153.75, 125.876953125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 260.25], [328.5, 260.25], [328.5, 270.896484375], [282.75, 270.896484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 504.75], [191.25, 504.75], [191.25, 516.65625], [107.25, 516.65625]]}, {"title": "2 Related work", "heading_level": null, "page_id": 1, "polygon": [[106.5, 81.59765625], [195.75, 81.59765625], [195.75, 93.97265625], [106.5, 93.97265625]]}, {"title": "3 Toward Convergent GAN Training", "heading_level": null, "page_id": 1, "polygon": [[107.25, 285.78515625], [306.59765625, 285.78515625], [306.59765625, 298.546875], [107.25, 298.546875]]}, {"title": "3.1 Feature matching", "heading_level": null, "page_id": 1, "polygon": [[106.5, 513.17578125], [207.984375, 513.17578125], [207.984375, 524.00390625], [106.5, 524.00390625]]}, {"title": "3.2 Minibatch discrimination", "heading_level": null, "page_id": 2, "polygon": [[106.5, 83.25], [239.66015625, 83.25], [239.66015625, 93.1025390625], [106.5, 93.1025390625]]}, {"title": "3.3 Historical averaging", "heading_level": null, "page_id": 2, "polygon": [[106.5, 618.0], [217.5, 618.0], [217.5, 628.41796875], [106.5, 628.41796875]]}, {"title": "3.4 One-sided label smoothing", "heading_level": null, "page_id": 3, "polygon": [[107.1298828125, 116.015625], [244.8896484375, 116.015625], [244.8896484375, 126.650390625], [107.1298828125, 126.650390625]]}, {"title": "3.5 Virtual batch normalization", "heading_level": null, "page_id": 3, "polygon": [[106.5, 238.60546875], [251.25, 238.60546875], [251.25, 249.046875], [106.5, 249.046875]]}, {"title": "4 Assessment of image quality", "heading_level": null, "page_id": 3, "polygon": [[107.05517578125, 360.75], [270.75, 360.75], [270.75, 372.0234375], [107.05517578125, 372.0234375]]}, {"title": "5 Semi-supervised learning", "heading_level": null, "page_id": 4, "polygon": [[107.1298828125, 123.75], [255.9462890625, 123.75], [255.9462890625, 134.2880859375], [107.1298828125, 134.2880859375]]}, {"title": "5.1 Importance of labels for image quality", "heading_level": null, "page_id": 4, "polygon": [[106.5, 654.75], [294.0, 654.75], [294.0, 664.5], [106.5, 664.5]]}, {"title": "6 Experiments", "heading_level": null, "page_id": 5, "polygon": [[106.5, 165.8056640625], [192.0, 165.8056640625], [192.0, 177.4072265625], [106.5, 177.4072265625]]}, {"title": "6.1 MNIST", "heading_level": null, "page_id": 5, "polygon": [[106.5, 231.64453125], [163.5, 231.64453125], [163.5, 242.47265625], [106.5, 242.47265625]]}, {"title": "6.2 CIFAR-10", "heading_level": null, "page_id": 5, "polygon": [[106.5, 660.515625], [175.2626953125, 660.515625], [175.2626953125, 671.34375], [106.5, 671.34375]]}, {"title": "6.3 SVHN", "heading_level": null, "page_id": 6, "polygon": [[106.5, 561.90234375], [159.4248046875, 561.90234375], [159.4248046875, 573.50390625], [106.5, 573.50390625]]}, {"title": "6.4 ImageNet", "heading_level": null, "page_id": 7, "polygon": [[106.5, 323.68359375], [174.0673828125, 323.68359375], [174.0673828125, 334.125], [106.5, 334.125]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 81.75], [183.181640625, 81.75], [183.181640625, 93.5859375], [106.5, 93.5859375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[107.25, 209.25], [164.2060546875, 209.25], [164.2060546875, 220.623046875], [107.25, 220.623046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 48], ["Text", 10], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 49], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["Line", 71], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 58], ["Text", 6], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["Figure", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 734], ["Line", 54], ["TextInlineMath", 5], ["Text", 2], ["SectionHeader", 2], ["Equation", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 63], ["TableCell", 36], ["Text", 6], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 587, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["TableCell", 59], ["Line", 35], ["Text", 3], ["Table", 2], ["Caption", 2], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 3, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 33], ["TableCell", 24], ["Reference", 3], ["Text", 2], ["Picture", 2], ["Table", 1], ["SectionHeader", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 8367, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 52], ["ListItem", 21], ["Reference", 21], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 41], ["Line", 11], ["ListItem", 5], ["Reference", 5], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Improved Techniques for Training GANs"}