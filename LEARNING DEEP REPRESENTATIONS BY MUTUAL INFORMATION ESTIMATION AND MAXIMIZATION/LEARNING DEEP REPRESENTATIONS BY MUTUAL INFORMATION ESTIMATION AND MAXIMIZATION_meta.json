{"table_of_contents": [{"title": "LEARNING DEEP REPRESENTATIONS BY MUTUAL IN-\nFORMATION ESTIMATION AND MAXIMIZATION", "heading_level": null, "page_id": 0, "polygon": [[106.083984375, 81.75], [506.513671875, 81.75], [506.513671875, 116.208984375], [106.083984375, 116.208984375]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 238.5], [335.583984375, 238.5], [335.583984375, 249.43359375], [276.75, 249.43359375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 407.25], [206.25, 407.25], [206.25, 418.4296875], [107.25, 418.4296875]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[106.90576171875, 145.5], [212.25, 145.5], [212.25, 156.5244140625], [106.90576171875, 156.5244140625]]}, {"title": "3 DEEP INFOMAX", "heading_level": null, "page_id": 2, "polygon": [[107.25, 154.5], [210.97265625, 154.5], [210.97265625, 166.1923828125], [107.25, 166.1923828125]]}, {"title": "3.1 MUTUAL INFORMATION ESTIMATION AND MAXIMIZ<PERSON>ION", "heading_level": null, "page_id": 2, "polygon": [[106.5, 679.5], [378.75, 679.5], [378.75, 689.90625], [106.5, 689.90625]]}, {"title": "3.2 LOCAL MUTUAL INFORMATION MAXIMIZATION", "heading_level": null, "page_id": 4, "polygon": [[106.5, 223.5], [333.0, 223.5], [333.0, 232.8046875], [106.5, 232.8046875]]}, {"title": "3.3 MATCHING REPRESENTATIONS TO A PRIOR DISTRIBUTION", "heading_level": null, "page_id": 4, "polygon": [[106.5, 549.75], [377.25, 549.75], [377.25, 559.58203125], [106.5, 559.58203125]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[106.90576171875, 224.103515625], [201.75, 224.103515625], [201.75, 234.931640625], [106.90576171875, 234.931640625]]}, {"title": "4.1 HOW DO WE EVALUATE THE QUALITY OF A REPRESENTATION?", "heading_level": null, "page_id": 5, "polygon": [[106.5, 514.3359375], [397.44140625, 514.3359375], [397.44140625, 523.6171875], [106.5, 523.6171875]]}, {"title": "4.2 REPRESENTATION LEARNING COMPARISON ACROSS MODELS", "heading_level": null, "page_id": 6, "polygon": [[106.5, 446.66015625], [389.07421875, 446.66015625], [389.07421875, 457.48828125], [106.5, 457.48828125]]}, {"title": "4.3 ADDING COORDINATE INFORMATION AND OCCLUSIONS", "heading_level": null, "page_id": 8, "polygon": [[106.5, 604.5], [368.15625, 604.5], [368.15625, 614.8828125], [106.5, 614.8828125]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 353.84765625], [196.4794921875, 353.84765625], [196.4794921875, 365.0625], [107.25, 365.0625]]}, {"title": "6 ACKNOWLEDGEMENTS", "heading_level": null, "page_id": 9, "polygon": [[107.25, 452.25], [244.5, 452.25], [244.5, 463.67578125], [107.25, 463.67578125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.25, 562.2890625], [176.009765625, 562.2890625], [176.009765625, 572.34375], [107.25, 572.34375]]}, {"title": "A APPENDIX", "heading_level": null, "page_id": 14, "polygon": [[106.98046875, 82.5], [183.75, 82.5], [183.75, 93.779296875], [106.98046875, 93.779296875]]}, {"title": "A.1 ON THE JENSEN-SHAN<PERSON><PERSON> DIVERGENCE AND MUTUAL INFORMATION", "heading_level": null, "page_id": 14, "polygon": [[106.681640625, 107.25], [432.703125, 108.3779296875], [432.703125, 117.8525390625], [106.681640625, 117.8525390625]]}, {"title": "A.2 EXPERIMENT AND ARCHITECTURE DETAILS", "heading_level": null, "page_id": 14, "polygon": [[106.90576171875, 601.5], [320.25, 601.5], [320.25, 611.015625], [106.90576171875, 611.015625]]}, {"title": "A.3 SAMPLING STRATEGIES", "heading_level": null, "page_id": 17, "polygon": [[107.25, 590.25], [234.75, 590.90625], [234.75, 600.1875], [107.25, 600.1875]]}, {"title": "A.4 NEAREST-NEIGHBOR ANALYSIS", "heading_level": null, "page_id": 18, "polygon": [[107.05517578125, 631.5], [267.75, 631.5], [267.75, 641.1796875], [107.05517578125, 641.1796875]]}, {"title": "A.5 ABLATION STUDIES", "heading_level": null, "page_id": 19, "polygon": [[107.25, 564.75], [218.7421875, 564.75], [218.7421875, 574.27734375], [107.25, 574.27734375]]}, {"title": "A.6 EMPIRICAL CONSISTENCY OF NEURAL DEPENDENCY MEASURE (NDM)", "heading_level": null, "page_id": 20, "polygon": [[107.25, 654.75], [442.86328125, 654.75], [442.86328125, 665.15625], [107.25, 665.15625]]}, {"title": "A.7 ADDITIONAL DETAILS ON <PERSON><PERSON><PERSON><PERSON><PERSON> AND COORDINATE PREDICTION EXPERIMENTS", "heading_level": null, "page_id": 21, "polygon": [[107.25, 124.0400390625], [493.962890625, 124.0400390625], [493.962890625, 134.2880859375], [107.25, 134.2880859375]]}, {"title": "A.8 TRAINING A GENERATOR BY MATCHING TO A PRIOR IMPLICITLY", "heading_level": null, "page_id": 22, "polygon": [[107.25, 288.0], [407.25, 288.0], [407.25, 297.966796875], [107.25, 297.966796875]]}, {"title": "A.9 GENERATION EXPERIMENTS AND RESULTS", "heading_level": null, "page_id": 23, "polygon": [[106.681640625, 214.62890625], [316.458984375, 214.62890625], [316.458984375, 225.45703125], [106.681640625, 225.45703125]]}, {"title": "A.10 IMAGES GENERATION", "heading_level": null, "page_id": 23, "polygon": [[106.2333984375, 314.7890625], [234.7294921875, 314.7890625], [234.7294921875, 326.00390625], [106.2333984375, 326.00390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 59], ["Text", 11], ["SectionHeader", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5320, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 54], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["Line", 62], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 646, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 77], ["Text", 6], ["Reference", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 63], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 63], ["Text", 7], ["ListItem", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 53], ["Text", 6], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 636], ["TableCell", 385], ["Line", 57], ["Reference", 3], ["Caption", 2], ["Table", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 18861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["TableCell", 141], ["Line", 54], ["Text", 4], ["Reference", 3], ["Table", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 332], ["TableCell", 79], ["Line", 46], ["Reference", 6], ["ListItem", 5], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7755, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 49], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 52], ["Line", 18], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 111], ["TextInlineMath", 4], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 80], ["TableCell", 50], ["TextInlineMath", 5], ["Reference", 3], ["Table", 2], ["Caption", 2], ["Equation", 2], ["Text", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3825, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 79], ["TableCell", 44], ["Reference", 4], ["Caption", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["TableCell", 72], ["Line", 48], ["Text", 6], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 42], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 535], ["Line", 153], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1996, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 55], ["Figure", 3], ["Reference", 3], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2208, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 35], ["Text", 5], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Picture", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 60], ["TextInlineMath", 7], ["Equation", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 752, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["TableCell", 30], ["Line", 28], ["Text", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3593, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/LEARNING DEEP REPRESENTATIONS BY MUTUAL INFORMATION ESTIMATION AND MAXIMIZATION"}