{"table_of_contents": [{"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[149.25, 172.5], [195.75, 172.5], [195.75, 184.078125], [149.25, 184.078125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 444.0], [132.75, 444.0], [132.75, 455.5546875], [54.0, 455.5546875]]}, {"title": "2. Variational bounds of MI", "heading_level": null, "page_id": 1, "polygon": [[54.0, 663.0], [197.9736328125, 663.0], [197.9736328125, 674.05078125], [54.0, 674.05078125]]}, {"title": "2.1. Normalized upper and lower bounds", "heading_level": null, "page_id": 1, "polygon": [[306.0, 201.75], [481.5, 201.75], [481.5, 211.728515625], [306.0, 211.728515625]]}, {"title": "2.2. Unnormalized lower bounds", "heading_level": null, "page_id": 2, "polygon": [[54.0, 333.0], [194.25, 333.0], [194.25, 343.01953125], [54.0, 343.01953125]]}, {"title": "2.3. Multi-sample unnormalized lower bounds", "heading_level": null, "page_id": 3, "polygon": [[54.0, 207.75], [252.0, 207.75], [252.0, 217.72265625], [54.0, 217.72265625]]}, {"title": "2.4. Nonlinearly interpolated lower bounds", "heading_level": null, "page_id": 3, "polygon": [[306.0, 461.25], [489.75, 461.25], [489.75, 471.796875], [306.0, 471.796875]]}, {"title": "2.5. Structured bounds with tractable encoders", "heading_level": null, "page_id": 4, "polygon": [[54.0, 237.0], [255.0, 237.0], [255.0, 247.11328125], [54.0, 247.11328125]]}, {"title": "InfoNCE with a tractable conditional.", "heading_level": null, "page_id": 4, "polygon": [[54.0, 369.75], [216.0, 369.75], [216.0, 379.951171875], [54.0, 379.951171875]]}, {"title": "Leave one out upper bound.", "heading_level": null, "page_id": 4, "polygon": [[54.0, 494.25], [174.75, 494.25], [174.75, 504.66796875], [54.0, 504.66796875]]}, {"title": "Reparameterizing critics.", "heading_level": null, "page_id": 4, "polygon": [[306.0, 110.25], [415.5, 110.25], [415.5, 120.5595703125], [306.0, 120.5595703125]]}, {"title": "Upper bounding total correlation.", "heading_level": null, "page_id": 4, "polygon": [[306.0, 373.5], [452.25, 373.5], [452.25, 383.818359375], [306.0, 383.818359375]]}, {"title": "2.6. From density ratio estimators to bounds", "heading_level": null, "page_id": 4, "polygon": [[305.25, 650.25], [495.75, 650.25], [495.75, 660.515625], [305.25, 660.515625]]}, {"title": "3. Experiments", "heading_level": null, "page_id": 5, "polygon": [[54.0, 574.5], [132.75, 574.5], [132.75, 585.87890625], [54.0, 585.87890625]]}, {"title": "Comparing estimates across different lower bounds.", "heading_level": null, "page_id": 5, "polygon": [[306.75, 367.5], [528.75, 367.5], [528.75, 377.244140625], [306.75, 377.244140625]]}, {"title": "Bias-variance tradeoff for optimal critics.", "heading_level": null, "page_id": 6, "polygon": [[54.0, 390.0], [232.5, 390.0], [232.5, 401.02734375], [54.0, 401.02734375]]}, {"title": "Bias-variance tradeoffs for representation learning.", "heading_level": null, "page_id": 6, "polygon": [[54.0, 623.25], [273.75, 623.25], [273.75, 634.21875], [54.0, 634.21875]]}, {"title": "3.1. Decoder-free representation learning on dSprites", "heading_level": null, "page_id": 6, "polygon": [[306.0, 467.25], [533.25, 467.25], [533.25, 477.2109375], [306.0, 477.2109375]]}, {"title": "4. Disc<PERSON><PERSON>", "heading_level": null, "page_id": 7, "polygon": [[54.0, 639.0], [122.25, 639.0], [122.25, 650.07421875], [54.0, 650.07421875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 79.71240234375], [54.0, 79.71240234375]]}, {"title": "<PERSON><PERSON> of mutual information lower bounds", "heading_level": null, "page_id": 10, "polygon": [[54.0, 67.5], [309.75, 67.5], [309.75, 79.32568359375], [54.0, 79.32568359375]]}, {"title": "B. Experimental details", "heading_level": null, "page_id": 10, "polygon": [[54.0, 573.0], [175.5, 573.0], [175.5, 583.9453125], [54.0, 583.9453125]]}, {"title": "<PERSON><PERSON> Additional experiments", "heading_level": null, "page_id": 11, "polygon": [[54.0, 302.25], [190.65234375, 302.25], [190.65234375, 314.015625], [54.0, 314.015625]]}, {"title": "C.1. Exhaustive hyperparameter sweep.", "heading_level": null, "page_id": 11, "polygon": [[54.0, 323.25], [225.31640625, 323.25], [225.31640625, 333.931640625], [54.0, 333.931640625]]}, {"title": "C.2. Effective bias-variance tradeoffs with I_{\\alpha}", "heading_level": null, "page_id": 11, "polygon": [[54.0, 450.75], [246.0, 450.75], [246.0, 461.25], [54.0, 461.25]]}, {"title": "<PERSON><PERSON> <PERSON>_{\\text{JS}} derivation", "heading_level": null, "page_id": 12, "polygon": [[54.0, 301.5], [138.75, 301.5], [138.75, 312.85546875], [54.0, 312.85546875]]}, {"title": "E. Alternative derivation of I_{\\text{TNCE}}", "heading_level": null, "page_id": 12, "polygon": [[54.0, 663.0], [225.7646484375, 663.0], [225.7646484375, 674.4375], [54.0, 674.4375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 106], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6479, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 696], ["Line", 115], ["ListItem", 5], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2256, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1122], ["Line", 120], ["TextInlineMath", 10], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1071, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1271], ["Line", 155], ["TextInlineMath", 8], ["Equation", 6], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7485, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1037], ["Line", 142], ["TextInlineMath", 9], ["SectionHeader", 6], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1953, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["Line", 89], ["Caption", 2], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 107], ["Text", 5], ["SectionHeader", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1478, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 99], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4777, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 92], ["ListItem", 25], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 94], ["ListItem", 26], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 830], ["TableCell", 135], ["Line", 72], ["SectionHeader", 2], ["Table", 2], ["Caption", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 9686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["TableCell", 170], ["Line", 64], ["Text", 4], ["SectionHeader", 3], ["Table", 2], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["TableGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 7712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 585], ["Line", 70], ["Equation", 5], ["Text", 4], ["TextInlineMath", 4], ["ListItem", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["Line", 89], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/On Variational Bounds of Mutual Information"}