Image /page/0/Picture/0 description: The image is a title page for a chapter or section titled "Probability Distributions". The number "2" is prominently displayed in red above the title. The background of the image is a textured, abstract pattern that resembles rippling water or a metallic surface with a golden hue.

In Chapter 1, we emphasized the central role played by probability theory in the solution of pattern recognition problems. We turn now to an exploration of some particular examples of probability distributions and their properties. As well as being of great interest in their own right, these distributions can form building blocks for more complex models and will be used extensively throughout the book. The distributions introduced in this chapter will also serve another important purpose, namely to provide us with the opportunity to discuss some key statistical concepts, such as Bayesian inference, in the context of simple models before we encounter them in more complex situations in later chapters.

One role for the distributions discussed in this chapter is to model the probability distribution  $p(\mathbf{x})$  of a random variable **x**, given a finite set  $\mathbf{x}_1, \ldots, \mathbf{x}_N$  of observations. This problem is known as *density estimation*. For the purposes of this chapter, we shall assume that the data points are independent and identically distributed. It should be emphasized that the problem of density estimation is fundamentally ill-posed, because there are infinitely many probability distributions that could have given rise to the observed finite data set. Indeed, any distribution  $p(x)$ that is nonzero at each of the data points  $x_1, \ldots, x_N$  is a potential candidate. The issue of choosing an appropriate distribution relates to the problem of model selection that has already been encountered in the context of polynomial curve fitting in Chapter 1 and that is a central issue in pattern recognition.

We begin by considering the binomial and multinomial distributions for discrete random variables and the Gaussian distribution for continuous random variables. These are specific examples of *parametric* distributions, so-called because they are governed by a small number of adaptive parameters, such as the mean and variance in the case of a Gaussian for example. To apply such models to the problem of density estimation, we need a procedure for determining suitable values for the parameters, given an observed data set. In a frequentist treatment, we choose specific values for the parameters by optimizing some criterion, such as the likelihood function. By contrast, in a Bayesian treatment we introduce prior distributions over the parameters and then use Bayes' theorem to compute the corresponding posterior distribution given the observed data.

We shall see that an important role is played by *conjugate* priors, that lead to posterior distributions having the same functional form as the prior, and that therefore lead to a greatly simplified Bayesian analysis. For example, the conjugate prior for the parameters of the multinomial distribution is called the *Dirichlet* distribution, while the conjugate prior for the mean of a Gaussian is another Gaussian. All of these distributions are examples of the *exponential family* of distributions, which possess a number of important properties, and which will be discussed in some detail.

One limitation of the parametric approach is that it assumes a specific functional form for the distribution, which may turn out to be inappropriate for a particular application. An alternative approach is given by *nonparametric* density estimation methods in which the form of the distribution typically depends on the size of the data set. Such models still contain parameters, but these control the model complexity rather than the form of the distribution. We end this chapter by considering three nonparametric methods based respectively on histograms, nearest-neighbours, and kernels.

## **2.1.** Binary Variables

We begin by considering a single binary random variable  $x \in \{0, 1\}$ . For example, x might describe the outcome of flipping a coin, with  $x = 1$  representing 'heads', and  $x = 0$  representing 'tails'. We can imagine that this is a damaged coin so that the probability of landing heads is not necessarily the same as that of landing tails. The probability of  $x = 1$  will be denoted by the parameter  $\mu$  so that

$$
p(x=1|\mu) = \mu \tag{2.1}
$$

where  $0 \le \mu \le 1$ , from which it follows that  $p(x = 0 | \mu) = 1 - \mu$ . The probability distribution over  $x$  can therefore be written in the form

$$
Bern(x|\mu) = \mu^x (1 - \mu)^{1 - x}
$$
 (2.2)

*Exercise 2.1* which is known as the *Bernoulli* distribution. It is easily verified that this distribution

is normalized and that it has mean and variance given by

$$
\mathbb{E}[x] = \mu \tag{2.3}
$$

$$
var[x] = \mu(1 - \mu). \tag{2.4}
$$

Now suppose we have a data set  $\mathcal{D} = \{x_1, \ldots, x_N\}$  of observed values of x. We can construct the likelihood function, which is a function of  $\mu$ , on the assumption that the observations are drawn independently from  $p(x|\mu)$ , so that

$$
p(\mathcal{D}|\mu) = \prod_{n=1}^{N} p(x_n|\mu) = \prod_{n=1}^{N} \mu^{x_n} (1 - \mu)^{1 - x_n}.
$$
 (2.5)

In a frequentist setting, we can estimate a value for  $\mu$  by maximizing the likelihood function, or equivalently by maximizing the logarithm of the likelihood. In the case of the Bernoulli distribution, the log likelihood function is given by

$$
\ln p(\mathcal{D}|\mu) = \sum_{n=1}^{N} \ln p(x_n|\mu) = \sum_{n=1}^{N} \{x_n \ln \mu + (1 - x_n) \ln(1 - \mu)\}.
$$
 (2.6)

At this point, it is worth noting that the log likelihood function depends on the N observations  $x_n$  only through their sum  $\sum_{n=1}^{\infty} x_n$ . This sum provides an example of a *sufficient statistic* for the data under this distribution, and we shall study the important role of sufficient statistics in some detail. If we set the derivative of  $\ln p(\mathcal{D}|\mu)$ with respect to  $\mu$  equal to zero, we obtain the maximum likelihood estimator

$$
\mu_{\rm ML} = \frac{1}{N} \sum_{n=1}^{N} x_n \tag{2.7}
$$

Image /page/2/Picture/14 description: The image contains the name "Jacob Bernoulli" in purple text, followed by the years "1654-1705" also in purple text. The background is a light purple color.

Jacob Bernoulli, also known as Jacques or James Bernoulli, was a Swiss mathematician and was the first of many in the Bernoulli family to pursue a career in science and mathematics. Although compelled

to study philosophy and theology against his will by his parents, he travelled extensively after graduating in order to meet with many of the leading scientists of his time, including Boyle and Hooke in England. When he returned to Switzerland, he taught mechanics and became Professor of Mathematics at Basel in 1687. Unfortunately, rivalry between Jacob and his younger brother Johann turned an initially productive collaboration into a bitter and public dispute. Jacob's most significant contributions to mathematics appeared in The Art of Conjecture published in 1713, eight years after his death, which deals with topics in probability theory including what has become known as the Bernoulli distribution.

**Figure 2.1** Histogram plot of the binomial distribution (2.9) as a function of  $m$  for  $N = 10$  and  $\mu = 0.25$ .

Image /page/3/Figure/2 description: A bar graph shows the distribution of values for the variable 'm' on the x-axis, ranging from 0 to 10. The y-axis represents probability, with values ranging from 0 to 0.3. The bars indicate the following approximate probabilities: m=0 has a probability of about 0.05; m=1 has a probability of about 0.18; m=2 has a probability of about 0.28; m=3 has a probability of about 0.25; m=4 has a probability of about 0.15; m=5 has a probability of about 0.05; m=6 has a probability of about 0.02; and m=7, 8, 9, and 10 have probabilities close to 0.

which is also known as the *sample mean*. If we denote the number of observations of  $x = 1$  (heads) within this data set by m, then we can write (2.7) in the form

$$
\mu_{\rm ML} = \frac{m}{N} \tag{2.8}
$$

so that the probability of landing heads is given, in this maximum likelihood framework, by the fraction of observations of heads in the data set.

Now suppose we flip a coin, say, 3 times and happen to observe 3 heads. Then  $N = m = 3$  and  $\mu_{ML} = 1$ . In this case, the maximum likelihood result would predict that all future observations should give heads. Common sense tells us that this is unreasonable, and in fact this is an extreme example of the over-fitting associated with maximum likelihood. We shall see shortly how to arrive at more sensible conclusions through the introduction of a prior distribution over  $\mu$ .

We can also work out the distribution of the number m of observations of  $x = 1$ , given that the data set has size N. This is called the *binomial* distribution, and from (2.5) we see that it is proportional to  $\mu^m(1-\mu)^{N-m}$ . In order to obtain the normalization coefficient we note that out of  $N$  coin flips, we have to add up all of the possible ways of obtaining  $m$  heads, so that the binomial distribution can be written

Bin
$$
(m|N, \mu)
$$
 =  $\binom{N}{m} \mu^m (1-\mu)^{N-m}$  (2.9)

where

$$
\binom{N}{m} \equiv \frac{N!}{(N-m)!m!} \tag{2.10}
$$

Exercise 2.3 is the number of ways of choosing
Figure 2.1 shows a plot of the binom

*Exercise 2.3* is the number of ways of choosing m objects out of a total of N identical objects. Figure 2.1 shows a plot of the binomial distribution for  $N = 10$  and  $\mu = 0.25$ .

The mean and variance of the binomial distribution can be found by using the result of Exercise 1.10, which shows that for independent events the mean of the sum is the sum of the means, and the variance of the sum is the sum of the variances. Because  $m = x_1 + \ldots + x_N$ , and for each observation the mean and variance are

given by (2.3) and (2.4), respectively, we have

$$
\mathbb{E}[m] \equiv \sum_{m=0}^{N} m \text{Bin}(m|N,\mu) = N\mu \tag{2.11}
$$

$$
\text{var}[m] \equiv \sum_{m=0}^{N} (m - \mathbb{E}[m])^2 \text{Bin}(m|N, \mu) = N\mu(1 - \mu). \tag{2.12}
$$

These results can also be proved directly using calculus.

### **2.1.1** The beta distribution

We have seen in (2.8) that the maximum likelihood setting for the parameter  $\mu$ in the Bernoulli distribution, and hence in the binomial distribution, is given by the fraction of the observations in the data set having  $x = 1$ . As we have already noted, this can give severely over-fitted results for small data sets. In order to develop a Bayesian treatment for this problem, we need to introduce a prior distribution  $p(\mu)$ over the parameter  $\mu$ . Here we consider a form of prior distribution that has a simple interpretation as well as some useful analytical properties. To motivate this prior, we note that the likelihood function takes the form of the product of factors of the form  $\mu^{x}(1-\mu)^{1-x}$ . If we choose a prior to be proportional to powers of  $\mu$  and  $(1 - \mu)$ , then the posterior distribution, which is proportional to the product of the prior and the likelihood function, will have the same functional form as the prior. This property is called *conjugacy* and we will see several examples of it later in this chapter. We therefore choose a prior, called the *beta* distribution, given by

$$
Beta(\mu|a, b) = \frac{\Gamma(a+b)}{\Gamma(a)\Gamma(b)} \mu^{a-1} (1-\mu)^{b-1}
$$
 (2.13)

where  $\Gamma(x)$  is the gamma function defined by (1.141), and the coefficient in (2.13) *Exercise* 2.5 ensures that the beta distribution is normalized, so that

$$
\int_0^1 \text{Beta}(\mu|a, b) \, d\mu = 1. \tag{2.14}
$$

*Exercise* 2.6 The mean and variance of the beta distribution are given by

$$
\mathbb{E}[\mu] = \frac{a}{a+b} \tag{2.15}
$$

$$
var[\mu] = \frac{ab}{(a+b)^2(a+b+1)}.
$$
 (2.16)

The parameters a and b are often called *hyperparameters* because they control the distribution of the parameter  $\mu$ . Figure 2.2 shows plots of the beta distribution for various values of the hyperparameters.

The posterior distribution of  $\mu$  is now obtained by multiplying the beta prior (2.13) by the binomial likelihood function (2.9) and normalizing. Keeping only the factors that depend on  $\mu$ , we see that this posterior distribution has the form

$$
p(\mu|m, l, a, b) \propto \mu^{m+a-1} (1-\mu)^{l+b-1}
$$
\n(2.17)

Image /page/5/Figure/1 description: This image displays four plots of the beta distribution, Beta(μ|a, b), as a function of μ for different values of a and b. The top-left plot shows the distribution for a=0.1 and b=0.1, with a U-shaped curve peaking at 0 and 1. The top-right plot shows the distribution for a=1 and b=1, which is a flat horizontal line at y=1. The bottom-left plot shows the distribution for a=2 and b=3, which is a curve that rises to a peak around μ=0.4 and then falls to 0 at μ=1. The bottom-right plot shows the distribution for a=8 and b=4, which is a bell-shaped curve peaking around μ=0.67 and falling to 0 at μ=1. All plots have the x-axis labeled as μ and ranging from 0 to 1, and the y-axis labeled with values from 0 to 3.

**Figure 2.2** Plots of the beta distribution  $Beta(\mu|a, b)$  given by (2.13) as a function of  $\mu$  for various values of the hyperparameters  $a$  and  $b$ .

where  $l = N - m$ , and therefore corresponds to the number of 'tails' in the coin example. We see that (2.17) has the same functional dependence on  $\mu$  as the prior distribution, reflecting the conjugacy properties of the prior with respect to the likelihood function. Indeed, it is simply another beta distribution, and its normalization coefficient can therefore be obtained by comparison with (2.13) to give

$$
p(\mu|m, l, a, b) = \frac{\Gamma(m + a + l + b)}{\Gamma(m + a)\Gamma(l + b)} \mu^{m + a - 1} (1 - \mu)^{l + b - 1}.
$$
 (2.18)

We see that the effect of observing a data set of m observations of  $x = 1$  and l observations of  $x = 0$  has been to increase the value of a by m, and the value of b by l, in going from the prior distribution to the posterior distribution. This allows us to provide a simple interpretation of the hyperparameters  $a$  and  $b$  in the prior as an *effective number of observations* of  $x = 1$  and  $x = 0$ , respectively. Note that a and b need not be integers. Furthermore, the posterior distribution can act as the prior if we subsequently observe additional data. To see this, we can imagine taking observations one at a time and after each observation updating the current posterior

Image /page/6/Figure/1 description: The image displays three plots side-by-side, each with the x-axis labeled 'µ' ranging from 0 to 1, and the y-axis labeled from 0 to 2. The first plot, titled 'prior', shows a red curve that starts at (0,0), rises to a peak of approximately 1.5 at µ=0.5, and then descends back to (1,0). The second plot, titled 'likelihood function', shows a blue line starting at (0,0) and increasing linearly to (1,1). The third plot, titled 'posterior', shows a red curve that starts at (0,0), rises to a peak of approximately 1.8 at µ=0.6, and then descends to (1,0).

**Figure 2.3** Illustration of one step of sequential Bayesian inference. The prior is given by a beta distribution with parameters  $a = 2$ ,  $b = 2$ , and the likelihood function, given by (2.9) with  $N = m = 1$ , corresponds to a single observation of  $x = 1$ , so that the posterior is given by a beta distribution with parameters  $a = 3$ ,  $b = 2$ .

distribution by multiplying by the likelihood function for the new observation and then normalizing to obtain the new, revised posterior distribution. At each stage, the posterior is a beta distribution with some total number of (prior and actual) observed values for  $x = 1$  and  $x = 0$  given by the parameters a and b. Incorporation of an additional observation of  $x = 1$  simply corresponds to incrementing the value of a by 1, whereas for an observation of  $x = 0$  we increment b by 1. Figure 2.3 illustrates one step in this process.

We see that this *sequential* approach to learning arises naturally when we adopt a Bayesian viewpoint. It is independent of the choice of prior and of the likelihood function and depends only on the assumption of i.i.d. data. Sequential methods make use of observations one at a time, or in small batches, and then discard them before the next observations are used. They can be used, for example, in real-time learning scenarios where a steady stream of data is arriving, and predictions must be made before all of the data is seen. Because they do not require the whole data set to be stored or loaded into memory, sequential methods are also useful for large data sets. *Section 2.3.5* Maximum likelihood methods can also be cast into a sequential framework.

> If our goal is to predict, as best we can, the outcome of the next trial, then we must evaluate the predictive distribution of  $x$ , given the observed data set  $D$ . From the sum and product rules of probability, this takes the form

$$
p(x = 1|\mathcal{D}) = \int_0^1 p(x = 1|\mu)p(\mu|\mathcal{D}) \, d\mu = \int_0^1 \mu p(\mu|\mathcal{D}) \, d\mu = \mathbb{E}[\mu|\mathcal{D}]. \tag{2.19}
$$

Using the result (2.18) for the posterior distribution  $p(\mu|\mathcal{D})$ , together with the result (2.15) for the mean of the beta distribution, we obtain

$$
p(x = 1|D) = \frac{m+a}{m+a+l+b}
$$
 (2.20)

which has a simple interpretation as the total fraction of observations (both real observations and fictitious prior observations) that correspond to  $x = 1$ . Note that in the limit of an infinitely large data set  $m, l \rightarrow \infty$  the result (2.20) reduces to the maximum likelihood result (2.8). As we shall see, it is a very general property that the Bayesian and maximum likelihood results will agree in the limit of an infinitely

*Section 2.3.5*

large data set. For a finite data set, the posterior mean for  $\mu$  always lies between the prior mean and the maximum likelihood estimate for  $\mu$  corresponding to the relative *Exercise* 2.7 frequencies of events given by (2.7).

> From Figure 2.2, we see that as the number of observations increases, so the posterior distribution becomes more sharply peaked. This can also be seen from the result (2.16) for the variance of the beta distribution, in which we see that the variance goes to zero for  $a \to \infty$  or  $b \to \infty$ . In fact, we might wonder whether it is a general property of Bayesian learning that, as we observe more and more data, the uncertainty represented by the posterior distribution will steadily decrease.

To address this, we can take a frequentist view of Bayesian learning and show that, on average, such a property does indeed hold. Consider a general Bayesian inference problem for a parameter  $\theta$  for which we have observed a data set  $\mathcal{D}$ , de-*Exercise* 2.8 scribed by the joint distribution  $p(\theta, \mathcal{D})$ . The following result

$$
\mathbb{E}_{\theta}[\theta] = \mathbb{E}_{\mathcal{D}}\left[\mathbb{E}_{\theta}[\theta|\mathcal{D}]\right]
$$
 (2.21)

where

$$
\mathbb{E}_{\theta}[\theta] \equiv \int p(\theta) \theta \, \mathrm{d}\theta \tag{2.22}
$$

$$
\mathbb{E}_{\mathcal{D}}[\mathbb{E}_{\theta}[\theta|\mathcal{D}]] \equiv \int \left\{ \int \theta p(\theta|\mathcal{D}) \, d\theta \right\} p(\mathcal{D}) \, d\mathcal{D} \tag{2.23}
$$

says that the posterior mean of  $\theta$ , averaged over the distribution generating the data, is equal to the prior mean of  $\theta$ . Similarly, we can show that

$$
\text{var}_{\theta}[\theta] = \mathbb{E}_{\mathcal{D}}\left[\text{var}_{\theta}[\theta|\mathcal{D}]\right] + \text{var}_{\mathcal{D}}\left[\mathbb{E}_{\theta}[\theta|\mathcal{D}]\right].\tag{2.24}
$$

The term on the left-hand side of  $(2.24)$  is the prior variance of  $\theta$ . On the righthand side, the first term is the average posterior variance of  $\theta$ , and the second term measures the variance in the posterior mean of  $\theta$ . Because this variance is a positive quantity, this result shows that, on average, the posterior variance of  $\theta$  is smaller than the prior variance. The reduction in variance is greater if the variance in the posterior mean is greater. Note, however, that this result only holds on average, and that for a particular observed data set it is possible for the posterior variance to be larger than the prior variance.

# **2.2.** Multinomial Variables

Binary variables can be used to describe quantities that can take one of two possible values. Often, however, we encounter discrete variables that can take on one of  $K$ possible mutually exclusive states. Although there are various alternative ways to express such variables, we shall see shortly that a particularly convenient representation is the 1-of-K scheme in which the variable is represented by a  $K$ -dimensional vector **x** in which one of the elements  $x_k$  equals 1, and all remaining elements equal

*Exercise 2.8*

## 2.2. Multinomial Variables

0. So, for instance if we have a variable that can take  $K = 6$  states and a particular observation of the variable happens to correspond to the state where  $x_3 = 1$ , then **x** will be represented by

$$
\mathbf{x} = (0, 0, 1, 0, 0, 0)^{\mathrm{T}}.
$$
 (2.25)

Note that such vectors satisfy  $\sum_{k=1}^{K} x_k = 1$ . If we denote the probability of  $x_k = 1$ by the parameter  $\mu_k$ , then the distribution of **x** is given

$$
p(\mathbf{x}|\boldsymbol{\mu}) = \prod_{k=1}^{K} \mu_k^{x_k}
$$
 (2.26)

where  $\mu = (\mu_1, \dots, \mu_K)^T$ , and the parameters  $\mu_k$  are constrained to satisfy  $\mu_k \geq 0$ and  $\sum_{k} \mu_k = 1$ , because they represent probabilities. The distribution (2.26) can be regarded as a generalization of the Bernoulli distribution to more than two outcomes. It is easily seen that the distribution is normalized

$$
\sum_{\mathbf{x}} p(\mathbf{x}|\boldsymbol{\mu}) = \sum_{k=1}^{K} \mu_k = 1
$$
 (2.27)

and that

$$
\mathbb{E}[\mathbf{x}|\boldsymbol{\mu}] = \sum_{\mathbf{x}} p(\mathbf{x}|\boldsymbol{\mu})\mathbf{x} = (\mu_1, \dots, \mu_M)^{\mathrm{T}} = \boldsymbol{\mu}.
$$
 (2.28)

Now consider a data set D of N independent observations  $x_1, \ldots, x_N$ . The corresponding likelihood function takes the form

$$
p(\mathcal{D}|\boldsymbol{\mu}) = \prod_{n=1}^{N} \prod_{k=1}^{K} \mu_k^{x_{nk}} = \prod_{k=1}^{K} \mu_k^{(\sum_n x_{nk})} = \prod_{k=1}^{K} \mu_k^{m_k}.
$$
 (2.29)

We see that the likelihood function depends on the  $N$  data points only through the  $K$  quantities

$$
m_k = \sum_n x_{nk} \tag{2.30}
$$

which represent the number of observations of  $x_k = 1$ . These are called the *sufficient Section 2.4 statistics* for this distribution.

In order to find the maximum likelihood solution for  $\mu$ , we need to maximize  $\ln p(\mathcal{D}|\mu)$  with respect to  $\mu_k$  taking account of the constraint that the  $\mu_k$  must sum *Appendix E* to one. This can be achieved using a Lagrange multiplier  $\lambda$  and maximizing

$$
\sum_{k=1}^{K} m_k \ln \mu_k + \lambda \left( \sum_{k=1}^{K} \mu_k - 1 \right).
$$
 (2.31)

Setting the derivative of (2.31) with respect to  $\mu_k$  to zero, we obtain

$$
\mu_k = -m_k/\lambda. \tag{2.32}
$$

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

Appendix F

We can solve for the Lagrange multiplier  $\lambda$  by substituting (2.32) into the constraint  $\sum_{k} \mu_k = 1$  to give  $\lambda = -N$ . Thus we obtain the maximum likelihood solution in the form

$$
\mu_k^{\text{ML}} = \frac{m_k}{N} \tag{2.33}
$$

which is the fraction of the N observations for which  $x_k = 1$ .

We can consider the joint distribution of the quantities  $m_1, \ldots, m_K$ , conditioned on the parameters  $\mu$  and on the total number N of observations. From (2.29) this takes the form

Mult
$$
(m_1, m_2, ..., m_K | \boldsymbol{\mu}, N)
$$
 =  $\binom{N}{m_1 m_2 ... m_K} \prod_{k=1}^K \mu_k^{m_k}$  (2.34)

which is known as the *multinomial* distribution. The normalization coefficient is the number of ways of partitioning N objects into K groups of size  $m_1, \ldots, m_K$  and is given by

$$
\binom{N}{m_1 m_2 \dots m_K} = \frac{N!}{m_1! m_2! \dots m_K!}.
$$
\n(2.35)

Note that the variables  $m_k$  are subject to the constraint

$$
\sum_{k=1}^{K} m_k = N.
$$
\n(2.36)

### **2.2.1** The Dirichlet distribution

We now introduce a family of prior distributions for the parameters  $\{\mu_k\}$  of the multinomial distribution (2.34). By inspection of the form of the multinomial distribution, we see that the conjugate prior is given by

$$
p(\boldsymbol{\mu}|\boldsymbol{\alpha}) \propto \prod_{k=1}^{K} \mu_k^{\alpha_k - 1}
$$
 (2.37)

where  $0 \le \mu_k \le 1$  and  $\sum_k \mu_k = 1$ . Here  $\alpha_1, \ldots, \alpha_K$  are the parameters of the distribution, and  $\alpha$  denotes  $(\alpha_1, \ldots, \alpha_K)^T$ . Note that, because of the summation constraint, the distribution over the space of the  $\{\mu_k\}$  is confined to a *simplex* of dimensionality  $K - 1$ , as illustrated for  $K = 3$  in Figure 2.4.

*Exercise* 2.9 The normalized form for this distribution is by

$$
Dir(\boldsymbol{\mu}|\boldsymbol{\alpha}) = \frac{\Gamma(\alpha_0)}{\Gamma(\alpha_1)\cdots\Gamma(\alpha_K)} \prod_{k=1}^{K} \mu_k^{\alpha_k - 1}
$$
(2.38)

which is called the *Dirichlet* distribution. Here  $\Gamma(x)$  is the gamma function defined by (1.141) while

$$
\alpha_0 = \sum_{k=1}^{K} \alpha_k.
$$
\n(2.39)

*Exercise 2.9*

**Figure 2.4** The Dirichlet distribution over three variables  $\mu_1, \mu_2, \mu_3$ is confined to a simplex (a bounded linear manifold) of the form shown, as a consequence of the constraints  $0 \le \mu_k \le 1$  and  $\sum_k \mu_k = 1$ .

Image /page/10/Figure/2 description: A 3D plot shows a triangle in pink with a blue outline. The triangle is formed by three axes labeled µ1, µ2, and µ3. The axes are arranged in a way that suggests a ternary plot or a simplex.

Plots of the Dirichlet distribution over the simplex, for various settings of the parameters  $\alpha_k$ , are shown in Figure 2.5.

Multiplying the prior (2.38) by the likelihood function (2.34), we obtain the posterior distribution for the parameters  $\{\mu_k\}$  in the form

$$
p(\boldsymbol{\mu}|\mathcal{D}, \boldsymbol{\alpha}) \propto p(\mathcal{D}|\boldsymbol{\mu})p(\boldsymbol{\mu}|\boldsymbol{\alpha}) \propto \prod_{k=1}^{K} \mu_k^{\alpha_k + m_k - 1}.
$$
 (2.40)

We see that the posterior distribution again takes the form of a Dirichlet distribution, confirming that the Dirichlet is indeed a conjugate prior for the multinomial. This allows us to determine the normalization coefficient by comparison with (2.38) so that

$$
p(\mu|\mathcal{D}, \alpha) = \text{Dir}(\mu|\alpha + \mathbf{m})
$$

$$
= \frac{\Gamma(\alpha_0 + N)}{\Gamma(\alpha_1 + m_1) \cdots \Gamma(\alpha_K + m_K)} \prod_{k=1}^K \mu_k^{\alpha_k + m_k - 1} \quad (2.41)
$$

where we have denoted  $\mathbf{m} = (m_1, \dots, m_K)^T$ . As for the case of the binomial distribution with its beta prior, we can interpret the parameters  $\alpha_k$  of the Dirichlet prior as an effective number of observations of  $x_k = 1$ .

Note that two-state quantities can either be represented as binary variables and

Image /page/10/Picture/10 description: A black and white portrait of a man with a beard and mustache, wearing a suit and tie. He is looking to the right with his arms crossed.

## Lejeune Dirichlet

Johann Peter Gustav Lejeune Dirichlet was a modest and reserved mathematician who made contributions in number theory, mechanics, and astronomy, and who gave the first rigorous analysis of Fourier series. His family originated from Richelet

in Belgium, and the name Lejeune Dirichlet comes

from 'le jeune de Richelet' (the young person from Richelet). Dirichlet's first paper, which was published in 1825, brought him instant fame. It concerned Fermat's last theorem, which claims that there are no positive integer solutions to  $x^n + y^n = z^n$  for  $n > 2$ . Dirichlet gave a partial proof for the case  $n = 5$ , which was sent to Legendre for review and who in turn completed the proof. Later, Dirichlet gave a complete proof for  $n = 14$ , although a full proof of Fermat's last theorem for arbitrary  $n$  had to wait until the work of Andrew Wiles in the closing years of the  $20<sup>th</sup>$  century.

Image /page/11/Figure/1 description: The image displays three 3D plots. The first plot shows a curved surface that starts high on the left and decreases as it moves to the right, forming a U-shape. The second plot shows a flat, triangular surface. The third plot shows a bell-shaped curve rising from a triangular base, with the peak colored purple and the base colored cyan.

**Figure 2.5** Plots of the Dirichlet distribution over three variables, where the two horizontal axes are coordinates in the plane of the simplex and the vertical axis corresponds to the value of the density. Here  $\{\alpha_k\} = 0.1$  on the left plot,  $\{\alpha_k\} = 1$  in the centre plot, and  $\{\alpha_k\} = 10$  in the right plot.

modelled using the binomial distribution (2.9) or as 1-of-2 variables and modelled using the multinomial distribution (2.34) with  $K = 2$ .

# **2.3.** The Gaussian Distribution

The Gaussian, also known as the normal distribution, is a widely used model for the distribution of continuous variables. In the case of a single variable  $x$ , the Gaussian distribution can be written in the form

$$
\mathcal{N}(x|\mu, \sigma^2) = \frac{1}{(2\pi\sigma^2)^{1/2}} \exp\left\{-\frac{1}{2\sigma^2}(x-\mu)^2\right\}
$$
 (2.42)

where  $\mu$  is the mean and  $\sigma^2$  is the variance. For a D-dimensional vector **x**, the multivariate Gaussian distribution takes the form

$$
\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma}) = \frac{1}{(2\pi)^{D/2}} \frac{1}{|\boldsymbol{\Sigma}|^{1/2}} \exp\left\{-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu})\right\}
$$
(2.43)

where  $\mu$  is a D-dimensional mean vector,  $\Sigma$  is a  $D \times D$  covariance matrix, and  $|\Sigma|$ denotes the determinant of **Σ**.

The Gaussian distribution arises in many different contexts and can be motivated Section 1.6 from a variety of different perspectives. For example, we have already seen that for a single real variable, the distribution that maximizes the entropy is the Gaussian. *Exercise 2.14* This property applies also to the multivariate Gaussian.

> Another situation in which the Gaussian distribution arises is when we consider the sum of multiple random variables. The *central limit theorem* (due to Laplace) tells us that, subject to certain mild conditions, the sum of a set of random variables, which is of course itself a random variable, has a distribution that becomes increasingly Gaussian as the number of terms in the sum increases (Walker, 1969). We can

*Section 1.6*

*Exercise 2.14*

Image /page/12/Figure/1 description: This image displays three histograms side-by-side, each representing a different value of N. The first histogram, labeled "N = 1", shows a uniform distribution with bars of approximately equal height across the x-axis range of 0 to 1. The y-axis ranges from 0 to 3. The second histogram, labeled "N = 2", shows a triangular distribution peaking around 0.5, with bars increasing in height towards the center and decreasing towards the edges. The third histogram, labeled "N = 10", shows a distribution that is more concentrated around 0.5, resembling a bell curve, with the tallest bars in the middle and rapidly decreasing heights towards the edges. All histograms share the same x-axis scale from 0 to 1 and y-axis scale from 0 to 3.

**Figure 2.6** Histogram plots of the mean of N uniformly distributed numbers for various values of N. We observe that as  $N$  increases, the distribution tends towards a Gaussian.

illustrate this by considering N variables  $x_1, \ldots, x_N$  each of which has a uniform distribution over the interval  $[0, 1]$  and then considering the distribution of the mean  $(x_1 + \cdots + x_N)/N$ . For large N, this distribution tends to a Gaussian, as illustrated in Figure 2.6. In practice, the convergence to a Gaussian as  $N$  increases can be very rapid. One consequence of this result is that the binomial distribution (2.9), which is a distribution over  $m$  defined by the sum of  $N$  observations of the random binary variable x, will tend to a Gaussian as  $N \to \infty$  (see Figure 2.1 for the case of  $N = 10$ ).

The Gaussian distribution has many important analytical properties, and we shall consider several of these in detail. As a result, this section will be rather more technically involved than some of the earlier sections, and will require familiarity with *Appendix C* various matrix identities. However, we strongly encourage the reader to become proficient in manipulating Gaussian distributions using the techniques presented here as this will prove invaluable in understanding the more complex models presented in later chapters.

We begin by considering the geometrical form of the Gaussian distribution. The

Image /page/12/Picture/6 description: A portrait of a man with white hair and a white collar, wearing a dark jacket. He has a serious expression on his face.

## Carl Friedrich Gauss

It is said that when Gauss went to elementary school at age 7, his teacher Büttner, trying to keep the class occupied, asked the pupils to sum the integers from 1 to 100. To the teacher's amazement, Gauss

arrived at the answer in a matter of moments by noting that the sum can be represented as 50 pairs  $(1 + 100)$ ,  $2+99$ , etc.) each of which added to 101, giving the answer 5,050. It is now believed that the problem which was actually set was of the same form but somewhat harder in that the sequence had a larger starting value and a larger increment. Gauss was a German mathematician and scientist with a reputation for being a hard-working perfectionist. One of his many contributions was to show that least squares can be derived under the assumption of normally distributed errors. He also created an early formulation of non-Euclidean geometry (a self-consistent geometrical theory that violates the axioms of Euclid) but was reluctant to discuss it openly for fear that his reputation might suffer if it were seen that he believed in such a geometry. At one point, Gauss was asked to conduct a geodetic survey of the state of Hanover, which led to his formulation of the normal distribution, now also known as the Gaussian. After his death, a study of his diaries revealed that he had discovered several important mathematical results years or even decades before they were published by others.

*Appendix C*

functional dependence of the Gaussian on **x** is through the quadratic form

$$
\Delta^2 = (\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1} (\mathbf{x} - \boldsymbol{\mu})
$$
 (2.44)

which appears in the exponent. The quantity ∆ is called the *Mahalanobis distance* from  $\mu$  to x and reduces to the Euclidean distance when  $\Sigma$  is the identity matrix. The Gaussian distribution will be constant on surfaces in **x**-space for which this quadratic form is constant.

First of all, we note that the matrix  $\Sigma$  can be taken to be symmetric, without loss of generality, because any antisymmetric component would disappear from the *Exercise 2.17* exponent. Now consider the eigenvector equation for the covariance matrix

$$
\sum u_i = \lambda_i u_i \tag{2.45}
$$

where  $i = 1, \ldots, D$ . Because  $\Sigma$  is a real, symmetric matrix its eigenvalues will be *Exercise* 2.18 real, and its eigenvectors can be chosen to form an orthonormal set, so that

$$
\mathbf{u}_i^{\mathrm{T}} \mathbf{u}_j = I_{ij} \tag{2.46}
$$

where  $I_{ij}$  is the i, j element of the identity matrix and satisfies

$$
I_{ij} = \begin{cases} 1, & \text{if } i = j \\ 0, & \text{otherwise.} \end{cases}
$$
 (2.47)

The covariance matrix  $\Sigma$  can be expressed as an expansion in terms of its eigenvec-*Exercise* 2.19 tors in the form

$$
\Sigma = \sum_{i=1}^{D} \lambda_i \mathbf{u}_i \mathbf{u}_i^{\mathrm{T}}
$$
 (2.48)

and similarly the inverse covariance matrix  $\Sigma^{-1}$  can be expressed as

$$
\Sigma^{-1} = \sum_{i=1}^{D} \frac{1}{\lambda_i} \mathbf{u}_i \mathbf{u}_i^{\mathrm{T}}.
$$
 (2.49)

Substituting (2.49) into (2.44), the quadratic form becomes

$$
\Delta^2 = \sum_{i=1}^{D} \frac{y_i^2}{\lambda_i} \tag{2.50}
$$

where we have defined

$$
y_i = \mathbf{u}_i^{\mathrm{T}}(\mathbf{x} - \boldsymbol{\mu}).
$$
 (2.51)

We can interpret  $\{y_i\}$  as a new coordinate system defined by the orthonormal vectors  $u_i$  that are shifted and rotated with respect to the original  $x_i$  coordinates. Forming the vector  $\mathbf{y} = (y_1, \dots, y_D)^\mathrm{T}$ , we have

$$
y = U(x - \mu) \tag{2.52}
$$

## 2.3. The Gaussian Distribution

**Figure 2.7** The red curve shows the elliptical surface of constant probability density for a Gaussian in a two-dimensional space **x** =  $(x_1, x_2)$  on which the density is  $\exp(-1/2)$  of its value at  $x = \mu$ . The major axes of the ellipse are defined by the eigenvectors  $\mathbf{u}_i$  of the covariance matrix, with corresponding eigenvalues  $\lambda_i$ .

Image /page/14/Figure/2 description: The image displays a 2D coordinate system with axes labeled x1 and x2. An ellipse, colored red, is centered at a point labeled µ. Two blue lines, labeled y1 and y2, represent the principal axes of the ellipse. The lengths of these axes are indicated by arrows and labels: λ1^(1/2) along the y1 axis and λ2^(1/2) along the y2 axis. Two black vectors, labeled u1 and u2, are shown in the upper right quadrant, oriented at an angle to each other, suggesting a transformation or basis change.

where **U** is a matrix whose rows are given by  $\mathbf{u}_k^T$ . From (2.46) it follows that **U** is<br>an *orthogonal* matrix i.e. it satisfies  $\mathbf{III}^T = \mathbf{I}$  and hence also  $\mathbf{II}^T\mathbf{II} = \mathbf{I}$  where **I** *Appendix C* an *orthogonal* matrix, i.e., it satisfies  $\mathbf{U}\mathbf{U}^{\mathrm{T}} = \mathbf{I}$ , and hence also  $\mathbf{U}^{\mathrm{T}}\mathbf{U} = \mathbf{I}$ , where **I** is the identity matrix is the identity matrix.

> The quadratic form, and hence the Gaussian density, will be constant on surfaces for which (2.51) is constant. If all of the eigenvalues  $\lambda_i$  are positive, then these surfaces represent ellipsoids, with their centres at  $\mu$  and their axes oriented along  $\mathbf{u}_i$ , and with scaling factors in the directions of the axes given by  $\lambda_i^{1/2}$ , as illustrated in Figure 2.7.

> For the Gaussian distribution to be well defined, it is necessary for all of the eigenvalues  $\lambda_i$  of the covariance matrix to be strictly positive, otherwise the distribution cannot be properly normalized. A matrix whose eigenvalues are strictly positive is said to be *positive definite*. In Chapter 12, we will encounter Gaussian distributions for which one or more of the eigenvalues are zero, in which case the distribution is singular and is confined to a subspace of lower dimensionality. If all of the eigenvalues are nonnegative, then the covariance matrix is said to be *positive semidefinite*.

> Now consider the form of the Gaussian distribution in the new coordinate system defined by the  $y_i$ . In going from the **x** to the **y** coordinate system, we have a Jacobian matrix **J** with elements given by

$$
J_{ij} = \frac{\partial x_i}{\partial y_j} = U_{ji}
$$
\n(2.53)

where  $U_{ji}$  are the elements of the matrix  $\mathbf{U}^T$ . Using the orthonormality property of the matrix **U**, we see that the square of the determinant of the Jacobian matrix is

$$
|\mathbf{J}|^2 = |\mathbf{U}^{\mathrm{T}}|^2 = |\mathbf{U}^{\mathrm{T}}| |\mathbf{U}| = |\mathbf{U}^{\mathrm{T}} \mathbf{U}| = |\mathbf{I}| = 1
$$
 (2.54)

and hence  $|\mathbf{J}| = 1$ . Also, the determinant  $|\mathbf{\Sigma}|$  of the covariance matrix can be written

as the product of its eigenvalues, and hence

$$
|\Sigma|^{1/2} = \prod_{j=1}^{D} \lambda_j^{1/2}.
$$
 (2.55)

Thus in the  $y_j$  coordinate system, the Gaussian distribution takes the form

$$
p(\mathbf{y}) = p(\mathbf{x})|\mathbf{J}| = \prod_{j=1}^{D} \frac{1}{(2\pi\lambda_j)^{1/2}} \exp\left\{-\frac{y_j^2}{2\lambda_j}\right\}
$$
(2.56)

which is the product of  $D$  independent univariate Gaussian distributions. The eigenvectors therefore define a new set of shifted and rotated coordinates with respect to which the joint probability distribution factorizes into a product of independent distributions. The integral of the distribution in the **y** coordinate system is then

$$
\int p(\mathbf{y}) \, \mathrm{d}\mathbf{y} = \prod_{j=1}^{D} \int_{-\infty}^{\infty} \frac{1}{(2\pi\lambda_j)^{1/2}} \exp\left\{-\frac{y_j^2}{2\lambda_j}\right\} \, \mathrm{d}y_j = 1 \tag{2.57}
$$

where we have used the result  $(1.48)$  for the normalization of the univariate Gaussian. This confirms that the multivariate Gaussian (2.43) is indeed normalized.

We now look at the moments of the Gaussian distribution and thereby provide an interpretation of the parameters  $\mu$  and  $\Sigma$ . The expectation of x under the Gaussian distribution is given by

$$
\mathbb{E}[\mathbf{x}] = \frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \int \exp\left\{-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \mathbf{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu})\right\} \mathbf{x} d\mathbf{x}
$$
$$
= \frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \int \exp\left\{-\frac{1}{2} \mathbf{z}^{\mathrm{T}} \mathbf{\Sigma}^{-1} \mathbf{z}\right\} (\mathbf{z} + \boldsymbol{\mu}) d\mathbf{z} \qquad (2.58)
$$

where we have changed variables using  $z = x - \mu$ . We now note that the exponent is an even function of the components of **z** and, because the integrals over these are taken over the range ( $-\infty, \infty$ ), the term in **z** in the factor  $(\mathbf{z} + \boldsymbol{\mu})$  will vanish by symmetry. Thus

$$
\mathbb{E}[\mathbf{x}] = \boldsymbol{\mu} \tag{2.59}
$$

and so we refer to  $\mu$  as the mean of the Gaussian distribution.

We now consider second order moments of the Gaussian. In the univariate case, we considered the second order moment given by  $\mathbb{E}[x^2]$ . For the multivariate Gaussian, there are  $D^2$  second order moments given by  $\mathbb{E}[x_ix_j]$ , which we can group together to form the matrix  $\mathbb{E}[\mathbf{x} \mathbf{x}^{\mathrm{T}}]$ . This matrix can be written as

$$
\mathbb{E}[\mathbf{x}\mathbf{x}^{\mathrm{T}}] = \frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \int \exp\left\{-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \mathbf{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu})\right\} \mathbf{x}\mathbf{x}^{\mathrm{T}} d\mathbf{x}
$$
$$
= \frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \int \exp\left\{-\frac{1}{2} \mathbf{z}^{\mathrm{T}} \mathbf{\Sigma}^{-1} \mathbf{z}\right\} (\mathbf{z} + \boldsymbol{\mu})(\mathbf{z} + \boldsymbol{\mu})^{\mathrm{T}} d\mathbf{z}
$$

## 2.3. The Gaussian Distribution

where again we have changed variables using  $z = x - \mu$ . Note that the cross-terms involving  $\mu \mathbf{z}^T$  and  $\mu^T \mathbf{z}$  will again vanish by symmetry. The term  $\mu \mu^T$  is constant and can be taken outside the integral, which itself is unity because the Gaussian distribution is normalized. Consider the term involving  $\mathbf{z} \mathbf{z}^T$ . Again, we can make use of the eigenvector expansion of the covariance matrix given by (2.45), together with the completeness of the set of eigenvectors, to write

$$
\mathbf{z} = \sum_{j=1}^{D} y_j \mathbf{u}_j \tag{2.60}
$$

where  $y_j = \mathbf{u}_j^{\mathrm{T}} \mathbf{z}$ , which gives

$$
\frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \int \exp\left\{-\frac{1}{2} \mathbf{z}^{\mathrm{T}} \mathbf{\Sigma}^{-1} \mathbf{z}\right\} \mathbf{z} \mathbf{z}^{\mathrm{T}} d\mathbf{z}
$$
\n
$$
= \frac{1}{(2\pi)^{D/2}} \frac{1}{|\mathbf{\Sigma}|^{1/2}} \sum_{i=1}^{D} \sum_{j=1}^{D} \mathbf{u}_i \mathbf{u}_j^{\mathrm{T}} \int \exp\left\{-\sum_{k=1}^{D} \frac{y_k^2}{2\lambda_k}\right\} y_i y_j d\mathbf{y}
$$
\n
$$
= \sum_{i=1}^{D} \mathbf{u}_i \mathbf{u}_i^{\mathrm{T}} \lambda_i = \mathbf{\Sigma} \quad (2.61)
$$

where we have made use of the eigenvector equation (2.45), together with the fact that the integral on the right-hand side of the middle line vanishes by symmetry unless  $i = j$ , and in the final line we have made use of the results (1.50) and (2.55), together with (2.48). Thus we have

$$
\mathbb{E}[\mathbf{x}\mathbf{x}^{\mathrm{T}}] = \boldsymbol{\mu}\boldsymbol{\mu}^{\mathrm{T}} + \boldsymbol{\Sigma}.
$$
 (2.62)

For single random variables, we subtracted the mean before taking second moments in order to define a variance. Similarly, in the multivariate case it is again convenient to subtract off the mean, giving rise to the *covariance* of a random vector **x** defined by

$$
cov[\mathbf{x}] = \mathbb{E}\left[ (\mathbf{x} - \mathbb{E}[\mathbf{x}])(\mathbf{x} - \mathbb{E}[\mathbf{x}])^{\mathrm{T}} \right].
$$
 (2.63)

For the specific case of a Gaussian distribution, we can make use of  $\mathbb{E}[\mathbf{x}] = \mu$ , together with the result (2.62), to give

$$
cov[\mathbf{x}] = \Sigma. \tag{2.64}
$$

Because the parameter matrix  $\Sigma$  governs the covariance of x under the Gaussian distribution, it is called the covariance matrix.

Although the Gaussian distribution (2.43) is widely used as a density model, it suffers from some significant limitations. Consider the number of free parameters in the distribution. A general symmetric covariance matrix  $\Sigma$  will have  $D(D+1)/2$ *Exercise 2.21* independent parameters, and there are another D independent parameters in  $\mu$ , giving  $D(D+3)/2$  parameters in total. For large D, the total number of parameters

**Figure 2.8** Contours of constant  $x_2$ probability density for a Gaussian distribution in two dimensions in which the covariance matrix is (a) of general form, (b) diagonal, in which the elliptical contours are aligned with the coordinate axes, and (c) proportional to the identity matrix, in which the contours are concentric circles.

Image /page/17/Figure/2 description: The image displays three plots labeled (a), (b), and (c). Each plot shows a 2D coordinate system with axes labeled x1 and x2. In each plot, there are concentric ellipses drawn in red, representing contour lines. Plot (a) shows ellipses that are elongated along a line with a positive slope, indicating a correlation between x1 and x2. Plot (b) shows ellipses that are elongated along the x1 axis, indicating a stronger influence of x1 than x2. Plot (c) shows circular ellipses, indicating no correlation between x1 and x2 and equal variance in both directions.

therefore grows quadratically with  $D$ , and the computational task of manipulating and inverting large matrices can become prohibitive. One way to address this problem is to use restricted forms of the covariance matrix. If we consider covariance matrices that are *diagonal*, so that  $\Sigma = diag(\sigma_i^2)$ , we then have a total of 2D independent parameters in the density model. The corresponding contours of constant pendent parameters in the density model. The corresponding contours of constant density are given by axis-aligned ellipsoids. We could further restrict the covariance matrix to be proportional to the identity matrix,  $\Sigma = \sigma^2 I$ , known as an *isotropic* covariance, giving  $D + 1$  independent parameters in the model and spherical surfaces of constant density. The three possibilities of general, diagonal, and isotropic covariance matrices are illustrated in Figure 2.8. Unfortunately, whereas such approaches limit the number of degrees of freedom in the distribution and make inversion of the covariance matrix a much faster operation, they also greatly restrict the form of the probability density and limit its ability to capture interesting correlations in the data.

A further limitation of the Gaussian distribution is that it is intrinsically unimodal (i.e., has a single maximum) and so is unable to provide a good approximation to multimodal distributions. Thus the Gaussian distribution can be both too flexible, in the sense of having too many parameters, while also being too limited in the range of distributions that it can adequately represent. We will see later that the introduction of *latent* variables, also called *hidden* variables or *unobserved* variables, allows both of these problems to be addressed. In particular, a rich family of multimodal distributions is obtained by introducing discrete latent variables leading to mixtures of Gaussians, as discussed in Section 2.3.9. Similarly, the introduction of continuous latent variables, as described in Chapter 12, leads to models in which the number of free parameters can be controlled independently of the dimensionality  $D$  of the data space while still allowing the model to capture the dominant correlations in the data set. Indeed, these two approaches can be combined and further extended to derive a very rich set of hierarchical models that can be adapted to a broad range of prac-*Section 8.3* tical applications. For instance, the Gaussian version of the *Markov random field*, which is widely used as a probabilistic model of images, is a Gaussian distribution over the joint space of pixel intensities but rendered tractable through the imposition of considerable structure reflecting the spatial organization of the pixels. Similarly, *Section 13.3* the *linear dynamical system*, used to model time series data for applications such as tracking, is also a joint Gaussian distribution over a potentially large number of observed and latent variables and again is tractable due to the structure imposed on the distribution. A powerful framework for expressing the form and properties of

*Section 8.3*

*Section 13.3*

such complex distributions is that of probabilistic graphical models, which will form the subject of Chapter 8.

### **2.3.1** Conditional Gaussian distributions

An important property of the multivariate Gaussian distribution is that if two sets of variables are jointly Gaussian, then the conditional distribution of one set conditioned on the other is again Gaussian. Similarly, the marginal distribution of either set is also Gaussian.

Consider first the case of conditional distributions. Suppose **x** is a <sup>D</sup>-dimensional vector with Gaussian distribution  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  and that we partition **x** into two disjoint subsets  $x_a$  and  $x_b$ . Without loss of generality, we can take  $x_a$  to form the first M components of **x**, with  $x_b$  comprising the remaining  $D - M$  components, so that

$$
\mathbf{x} = \begin{pmatrix} \mathbf{x}_a \\ \mathbf{x}_b \end{pmatrix} . \tag{2.65}
$$

We also define corresponding partitions of the mean vector  $\mu$  given by

$$
\mu = \begin{pmatrix} \mu_a \\ \mu_b \end{pmatrix} \tag{2.66}
$$

and of the covariance matrix  $\Sigma$  given by

$$
\Sigma = \begin{pmatrix} \Sigma_{aa} & \Sigma_{ab} \\ \Sigma_{ba} & \Sigma_{bb} \end{pmatrix} . \tag{2.67}
$$

Note that the symmetry  $\Sigma^T = \Sigma$  of the covariance matrix implies that  $\Sigma_{aa}$  and  $\Sigma_{bb}$ are symmetric, while  $\Sigma_{ba} = \Sigma_{ab}^{\mathrm{T}}$ .<br>In many situations, it will be

In many situations, it will be convenient to work with the inverse of the covariance matrix

$$
\Lambda \equiv \Sigma^{-1} \tag{2.68}
$$

which is known as the *precision matrix*. In fact, we shall see that some properties of Gaussian distributions are most naturally expressed in terms of the covariance, whereas others take a simpler form when viewed in terms of the precision. We therefore also introduce the partitioned form of the precision matrix

$$
\Lambda = \begin{pmatrix} \Lambda_{aa} & \Lambda_{ab} \\ \Lambda_{ba} & \Lambda_{bb} \end{pmatrix}
$$
 (2.69)

corresponding to the partitioning (2.65) of the vector **x**. Because the inverse of a *Exercise* 2.22 symmetric matrix is also symmetric, we see that  $\Lambda_{aa}$  and  $\Lambda_{bb}$  are symmetric, while  $\Lambda_{ab}^{\text{T}} = \Lambda_{ba}$ . It should be stressed at this point that, for instance,  $\Lambda_{aa}$  is not simply given by the inverse of  $\Sigma$ . In fact, we shall shortly examine the relation between given by the inverse of  $\Sigma_{aa}$ . In fact, we shall shortly examine the relation between the inverse of a partitioned matrix and the inverses of its partitions.

> Let us begin by finding an expression for the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$ . From the product rule of probability, we see that this conditional distribution can be