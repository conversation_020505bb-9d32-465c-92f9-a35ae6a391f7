{"table_of_contents": [{"title": "Three examples of coupling techniques", "heading_level": null, "page_id": 0, "polygon": [[133.5, 97.5], [408.0, 97.5], [408.0, 112.1484375], [133.5, 112.1484375]]}, {"title": "Convergence of the Langevin process", "heading_level": null, "page_id": 0, "polygon": [[133.5, 336.75], [353.8125, 336.75], [353.8125, 347.853515625], [133.5, 347.853515625]]}, {"title": "34 2 Three examples of coupling techniques", "heading_level": null, "page_id": 1, "polygon": [[133.5, 26.25], [333.0, 26.25], [333.0, 35.843994140625], [133.5, 35.843994140625]]}, {"title": "Euclidean isoperimetry", "heading_level": null, "page_id": 2, "polygon": [[133.5, 414.0], [271.5, 414.0], [271.5, 425.390625], [133.5, 425.390625]]}, {"title": "<PERSON><PERSON><PERSON><PERSON>'s log-concave perturbation theorem", "heading_level": null, "page_id": 4, "polygon": [[133.5, 232.5], [402.0, 232.5], [402.0, 243.439453125], [133.5, 243.439453125]]}, {"title": "38 2 Three examples of coupling techniques", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.25], [333.0, 26.25], [333.0, 35.6748046875], [133.5, 35.6748046875]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 5, "polygon": [[233.25, 400.5], [359.19140625, 400.5], [359.19140625, 412.2421875], [233.25, 412.2421875]]}, {"title": "The founding fathers of optimal transport", "heading_level": null, "page_id": 8, "polygon": [[133.5, 98.25], [432.75, 98.25], [432.75, 112.4384765625], [133.5, 112.4384765625]]}, {"title": "44 3 The founding fathers of optimal transport", "heading_level": null, "page_id": 11, "polygon": [[133.5, 26.25], [347.23828125, 26.25], [347.23828125, 35.96484375], [133.5, 35.96484375]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 13, "polygon": [[233.0859375, 144.0], [358.59375, 144.0], [358.59375, 155.84765625], [233.0859375, 155.84765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 32], ["TextInlineMath", 3], ["SectionHeader", 2], ["Text", 1], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2460, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 53], ["Equation", 4], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 66], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 530], ["Line", 79], ["Equation", 6], ["Text", 5], ["TextInlineMath", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 45], ["Text", 4], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["Line", 52], ["Text", 6], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 42], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 40], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 29], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 28], ["Text", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1330, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 38], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 40], ["Text", 6], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 40], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 40], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 40], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 37], ["Line", 18], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-3"}