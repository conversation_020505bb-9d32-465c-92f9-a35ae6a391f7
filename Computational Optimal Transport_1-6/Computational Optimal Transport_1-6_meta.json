{"table_of_contents": [{"title": "Computational Optimal Transport", "heading_level": null, "page_id": 0, "polygon": [[193.57894736842104, 212.1829383886256], [428.4247246022032, 212.1829383886256], [428.4247246022032, 230.92701421800948], [193.57894736842104, 230.92701421800948]]}, {"title": "Contents", "heading_level": null, "page_id": 2, "polygon": [[278.36352509179926, 173.1952606635071], [344.39045287637697, 173.1952606635071], [344.39045287637697, 192.4393310546875], [278.36352509179926, 192.4393310546875]]}, {"title": "", "heading_level": null, "page_id": 3, "polygon": [[106.25732421875, 678.60693359375], [161.181884765625, 678.60693359375], [161.181884765625, 689.42138671875], [106.25732421875, 689.42138671875]]}, {"title": "Abstract", "heading_level": null, "page_id": 4, "polygon": [[104.6859130859375, 124.460663507109], [153.062423500612, 124.460663507109], [153.062423500612, 136.918701171875], [104.6859130859375, 136.918701171875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 12], ["Line", 7], ["Text", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 10], ["Code", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 182], ["Span", 108], ["Line", 25], ["SectionHeader", 1], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7408, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 281], ["Span", 196], ["Line", 36], ["TableOfContents", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 12963, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 38], ["Text", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 31], ["Line", 8], ["Text", 2], ["Footnote", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Computational Optimal Transport_1-6"}