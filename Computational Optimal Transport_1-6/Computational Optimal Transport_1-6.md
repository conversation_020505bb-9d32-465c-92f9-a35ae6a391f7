# **Computational Optimal Transport**

<PERSON> and DMA, ENS

<PERSON> and CREST, ENSAE

```
@article{COTFNT,
year = {2019},
volume = {11},
journal = {Foundations and Trends in Machine Learning},
title = {Computational Optimal Transport},
number = {5-6},pages = {355--607}
author = {<PERSON>\'<PERSON> and <PERSON>}
}
```

2

# **Contents**

| 1 |     | <b>Introduction</b>                                      | 3  |
|---|-----|----------------------------------------------------------|----|
| 2 |     | <b>Theoretical Foundations</b>                           | 7  |
|   | 2.1 | Histograms and Measures                                  | 7  |
|   | 2.2 | Assignment and Monge Problem                             | 9  |
|   | 2.3 | Kantorovich Relaxation                                   | 13 |
|   | 2.4 | Metric Properties of Optimal Transport                   | 19 |
|   | 2.5 | Dual Problem                                             | 23 |
|   | 2.6 | Special Cases                                            | 30 |
| 3 |     | <b>Algorithmic Foundations</b>                           | 37 |
|   | 3.1 | The Kantorovich Linear Programs                          | 38 |
|   | 3.2 | C-Transforms                                             | 40 |
|   | 3.3 | Complementary Slackness                                  | 41 |
|   | 3.4 | Vertices of the Transportation Polytope                  | 42 |
|   | 3.5 | A Heuristic Description of the Network Simplex           | 45 |
|   | 3.6 | Dual Ascent Methods                                      | 49 |
|   | 3.7 | Auction Algorithm                                        | 52 |
| 4 |     | <b>Entropic Regularization of Optimal Transport</b>      | 57 |
|   | 4.1 | Entropic Regularization                                  | 57 |
|   | 4.2 | Sinkhorn's Algorithm and Its Convergence                 | 62 |
|   | 4.3 | Speeding Up Sinkhorn's Iterations                        | 73 |
|   | 4.4 | Stability and Log-Domain Computations                    | 77 |
|   | 4.5 | Regularized Approximations of the Optimal Transport Cost | 80 |
|   | 4.6 | Generalized Sinkhorn                                     | 82 |

| 5  |      | <b>Semidiscrete Optimal Transport</b>                       | 85         |
|----|------|-------------------------------------------------------------|------------|
|    | 5.1  | c-Transform and c-Transform                                 | 85         |
|    | 5.2  | Semidiscrete Formulation                                    | 87         |
|    | 5.3  | Entropic Semidiscrete Formulation                           | 89         |
|    | 5.4  | Stochastic Optimization Methods                             | 92         |
| 6  |      | $W_1$ Optimal Transport                                     | 96         |
|    | 6.1  | $W_1$ on Metric Spaces                                      | 97         |
|    | 6.2  | $W_1$ on Euclidean Spaces                                   | 98         |
|    | 6.3  | $W_1$ on a Graph                                            | 99         |
| 7  |      | <b>Dynamic Formulations</b>                                 | 102        |
|    | 7.1  | Continuous Formulation                                      | 102        |
|    | 7.2  | Discretization on Uniform Staggered Grids                   | 105        |
|    | 7.3  | Proximal Solvers                                            | 106        |
|    | 7.4  | Dynamical Unbalanced OT                                     | 108        |
|    | 7.5  | More General Mobility Functionals                           | 110        |
|    | 7.6  | Dynamic Formulation over the Paths Space                    | 111        |
| 8  |      | <b>Statistical Divergences</b>                              | 114        |
|    | 8.1  | $\varphi$ -Divergences                                      | 115        |
|    | 8.2  | Integral Probability Metrics                                | 120        |
|    | 8.3  | Wasserstein Spaces Are Not Hilbertian                       | 125        |
|    | 8.4  | Empirical Estimators for OT, MMD and $\varphi$ -divergences | 128        |
|    | 8.5  | Entropic Regularization: Between OT and MMD                 | 131        |
| 9  |      | <b>Variational Wasserstein Problems</b>                     | 133        |
|    | 9.1  | Differentiating the Wasserstein Loss                        | 134        |
|    | 9.2  | Wasserstein Barycenters, Clustering and Dictionary Learning | 138        |
|    | 9.3  | Gradient Flows                                              | 149        |
|    | 9.4  | Minimum Kantorovich Estimators                              | 155        |
| 10 |      | Extensions of Optimal Transport                             | 159        |
|    | 10.1 | Multimarginal Problems                                      | 159        |
|    | 10.2 | Unbalanced Optimal Transport                                | 162        |
|    | 10.3 | Problems with Extra Constraints on the Couplings            | 165        |
|    | 10.4 | Sliced Wasserstein Distance and Barycenters                 | 166        |
|    | 10.5 | Transporting Vectors and Matrices                           | 169        |
|    | 10.6 | Gromov-Wasserstein Distances                                | 172        |
|    |      |                                                             | References |

## **Abstract**

Optimal transport (OT) theory can be informally described using the words of the French mathematician Gaspard Monge (1746–1818): A worker with a shovel in hand has to move a large pile of sand lying on a construction site. The goal of the worker is to erect with all that sand a target pile with a prescribed shape (for example, that of a giant sand castle). Naturally, the worker wishes to minimize her total effort, quantified for instance as the total distance or time spent carrying shovelfuls of sand. Mathematicians interested in OT cast that problem as that of comparing two probability distributions—two different piles of sand of the same volume. They consider all of the many possible ways to morph, *transport* or reshape the first pile into the second, and associate a "global" cost to every such transport, using the "local" consideration of how much it costs to move a grain of sand from one place to another. Mathematicians are interested in the properties of that least costly transport, as well as in its efficient computation. That smallest cost not only defines a distance between distributions, but it also entails a rich geometric structure on the space of probability distributions. That structure is canonical in the sense that it borrows key geometric properties of the underlying "ground" space on which these distributions are defined. For instance, when the underlying space is Euclidean, key concepts such as interpolation, barycenters, convexity or gradients of functions extend naturally to the space of distributions endowed with an OT geometry.

OT has been (re)discovered in many settings and under different forms, giving it a rich history. While Monge's seminal work was motivated by an engineering problem, Tolstoi in the 1920s and Hitchcock, Kantorovich and Koopmans in the 1940s established its significance to logistics and economics. Dantzig solved it numerically in 1949 within the framework of linear programming, giving  $\overline{OT}$  a firm footing in optimization. OT was later revisited by analysts in the 1990s, notably Brenier, while also gaining fame in computer vision under the name of earth mover's distances. Recent years have witnessed yet another revolution in the spread of OT, thanks to the emergence of approximate solvers that can scale to large problem dimensions. As a consequence, OT is being increasingly used to unlock various problems in imaging sciences (such as color or texture processing), graphics (for shape manipulation) or machine learning (for regression, classification and generative modeling).

This paper reviews OT with a bias toward numerical methods, and covers the theoretical properties of OT that can guide the design of new algorithms. We focus in particular on the recent wave of efficient algorithms that have helped OT find relevance in data sciences. We give a prominent place to the many generalizations of OT that have been proposed in but a few years, and connect them with related approaches originating from statistical inference, kernel methods and information theory. All of

the figures can be reproduced using code made available in a companion website<sup>1</sup>. This website hosts the book project Computational Optimal Transport. You will also find slides and computational resources.

2

now Publishers Inc.. *Computational Optimal Transport*. Foundations and Trends<sup>®</sup> in Computer Graphics and Vision, vol. XX, no. XX, pp. 1–205, 2020. DOI: 10.1561/XXXXXXXXXX.

<sup>1</sup> https://optimaltransport.github.io/