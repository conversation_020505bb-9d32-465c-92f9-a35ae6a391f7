{"table_of_contents": [{"title": "Inequalities in Information \nTheory", "heading_level": null, "page_id": 0, "polygon": [[67.5, 104.255859375], [377.5078125, 104.255859375], [377.5078125, 158.994140625], [67.5, 158.994140625]]}, {"title": "16.1 BASIC INEQUALITIES OF INFORMATION THEORY", "heading_level": null, "page_id": 0, "polygon": [[68.25, 405.94921875], [355.5703125, 405.94921875], [355.5703125, 417.97265625], [68.25, 417.97265625]]}, {"title": "16.2 DIFFERENTIAL ENTROPY", "heading_level": null, "page_id": 3, "polygon": [[64.5, 179.25], [223.716796875, 179.25], [223.716796875, 189.2109375], [64.5, 189.2109375]]}, {"title": "16.3 BOUNDS ON ENTROPY AND RELATIVE ENTROPY", "heading_level": null, "page_id": 6, "polygon": [[66.0, 207.75], [351.75, 207.75], [351.75, 218.3203125], [66.0, 218.3203125]]}, {"title": "16.4 INEQUALITIES FOR TYPES", "heading_level": null, "page_id": 8, "polygon": [[63.75, 58.5], [229.5, 58.5], [229.5, 68.06689453125], [63.75, 68.06689453125]]}, {"title": "16.5 ENTROPY RATES OF SUBSETS", "heading_level": null, "page_id": 8, "polygon": [[63.0, 407.25], [247.5, 407.25], [247.5, 417.33984375], [63.0, 417.33984375]]}, {"title": "Theorem 16.8.1:", "heading_level": null, "page_id": 9, "polygon": [[65.25, 180.0], [148.5, 180.0], [148.5, 190.318359375], [65.25, 190.318359375]]}, {"title": "Theorem 16.5.4: Let", "heading_level": null, "page_id": 11, "polygon": [[64.5, 453.75], [168.0, 453.75], [168.0, 465.43359375], [64.5, 465.43359375]]}, {"title": "16.6 ENTROPY AND FISHER INFORMATION", "heading_level": null, "page_id": 12, "polygon": [[64.5, 58.5], [294.75, 58.5], [294.75, 68.46240234375], [64.5, 68.46240234375]]}, {"title": "16.7 THE ENTROPY POWER INEQUALITY AND THE \nBRUNN-MINKOWSKI INEQUALITY", "heading_level": null, "page_id": 15, "polygon": [[64.5, 357.75], [333.75, 357.75], [333.75, 380.3203125], [64.5, 380.3203125]]}, {"title": "16.8 INEQUALITIES FOR DETERMINANTS", "heading_level": null, "page_id": 19, "polygon": [[63.0, 501.75], [283.5, 501.75], [283.5, 511.9453125], [63.0, 511.9453125]]}, {"title": "Corollary:", "heading_level": null, "page_id": 22, "polygon": [[64.5, 166.5], [118.5, 166.5], [118.5, 176.87109375], [64.5, 176.87109375]]}, {"title": "16.9 INEQUAL<PERSON>IES FOR RATIOS OF DETERMINANTS", "heading_level": null, "page_id": 23, "polygon": [[66.0, 526.5], [346.5, 525.0], [346.5, 535.9921875], [66.0, 536.25]]}, {"title": "Theorem 16.9.1 (<PERSON><PERSON><PERSON><PERSON><PERSON> [23]): \\log(|K_n|/|K_{n-p}|) is concave in K_n.", "heading_level": null, "page_id": 24, "polygon": [[63.0, 309.0], [389.25, 309.0], [389.25, 321.310546875], [63.0, 321.310546875]]}, {"title": "Theorem 16.9.2 (<PERSON><PERSON><PERSON><PERSON><PERSON> [23]): |K_n|/|K_{n-1}| is concave in K_n.", "heading_level": null, "page_id": 25, "polygon": [[63.75, 112.5], [368.25, 112.5], [368.25, 124.2685546875], [63.75, 124.2685546875]]}, {"title": "OVERALL SUMMARY", "heading_level": null, "page_id": 26, "polygon": [[180.0, 269.89453125], [285.873046875, 269.89453125], [285.873046875, 280.3359375], [180.0, 280.3359375]]}, {"title": "PROBLEMS FOR CHAPTER 16", "heading_level": null, "page_id": 27, "polygon": [[64.5, 59.25], [216.0, 59.25], [216.0, 70.1630859375], [64.5, 70.1630859375]]}, {"title": "HISTORICAL NOTES", "heading_level": null, "page_id": 27, "polygon": [[64.5, 198.75], [171.75, 198.75], [171.75, 209.619140625], [64.5, 209.619140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 26], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4480, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 27], ["Text", 8], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 28], ["Equation", 8], ["Text", 5], ["TextInlineMath", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 29], ["Text", 7], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "surya", "block_counts": [["TableCell", 32], ["Span", 11], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "surya", "block_counts": [["TableCell", 76], ["Line", 4], ["Span", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9637, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 33], ["TextInlineMath", 10], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["Line", 30], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 879, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 32], ["TextInlineMath", 5], ["Equation", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 29], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3960, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 41], ["Text", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2128, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 31], ["TextInlineMath", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1050, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 32], ["Text", 7], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1008, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "surya", "block_counts": [["Span", 22], ["Line", 14], ["Equation", 9], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1724, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 30], ["Text", 6], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4266, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 37], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 32], ["Text", 6], ["TextInlineMath", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 30], ["Text", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2617, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 28], ["Equation", 9], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 7580, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 27], ["Text", 10], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 28], ["Equation", 7], ["TextInlineMath", 6], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 31], ["Equation", 8], ["Text", 7], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 32], ["Equation", 8], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "surya", "block_counts": [["Span", 41], ["Line", 19], ["Equation", 7], ["TextInlineMath", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1050, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 27], ["Equation", 8], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1413, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "surya", "block_counts": [["Span", 55], ["Line", 21], ["Equation", 7], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Text", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1979, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 27], ["TextInlineMath", 11], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["Text", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Elements_of_Information_Theory_Elements_504-531"}