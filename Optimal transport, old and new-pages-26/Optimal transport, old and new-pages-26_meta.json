{"table_of_contents": [{"title": "Displacement convexity I", "heading_level": null, "page_id": 0, "polygon": [[133.5, 98.25], [313.5, 98.25], [313.5, 111.375], [133.5, 111.375]]}, {"title": "450 16 Displacement convexity I", "heading_level": null, "page_id": 1, "polygon": [[133.5, 26.15185546875], [282.392578125, 26.15185546875], [282.392578125, 35.23974609375], [133.5, 35.23974609375]]}, {"title": "Reminders on convexity: differential and integral\nconditions", "heading_level": null, "page_id": 1, "polygon": [[133.5, 324.0], [424.93359375, 324.0], [424.93359375, 349.787109375], [133.5, 349.787109375]]}, {"title": "454 16 Displacement convexity I", "heading_level": null, "page_id": 5, "polygon": [[133.5, 26.103515625], [282.09375, 26.103515625], [282.09375, 35.52978515625], [133.5, 35.52978515625]]}, {"title": "Displacement convexity", "heading_level": null, "page_id": 5, "polygon": [[133.5, 272.25], [274.1748046875, 272.25], [274.1748046875, 284.23828125], [133.5, 284.23828125]]}, {"title": "Displacement convexity from curvature-dimension\nbounds", "heading_level": null, "page_id": 7, "polygon": [[133.5, 48.0], [430.5, 48.0], [430.5, 72.94482421875], [133.5, 72.94482421875]]}, {"title": "458 16 Displacement convexity I", "heading_level": null, "page_id": 9, "polygon": [[133.5, 26.15185546875], [282.09375, 26.15185546875], [282.09375, 35.33642578125], [133.5, 35.33642578125]]}, {"title": "A fluid mechanics feeling for <PERSON><PERSON><PERSON> curvature", "heading_level": null, "page_id": 10, "polygon": [[133.5, 268.5], [398.25, 268.5], [398.25, 279.59765625], [133.5, 279.59765625]]}, {"title": "Bibliographical notes", "heading_level": null, "page_id": 11, "polygon": [[232.5, 414.75], [359.25, 414.75], [359.25, 426.9375], [232.5, 426.9375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 33], ["TextInlineMath", 4], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2618, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 36], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 38], ["Equation", 5], ["TextInlineMath", 4], ["Text", 3], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["Line", 73], ["TextInlineMath", 8], ["Equation", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1089, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["Line", 68], ["Text", 6], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 505], ["Line", 62], ["TextInlineMath", 6], ["Equation", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["Line", 47], ["Equation", 5], ["TextInlineMath", 5], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 448], ["Line", 37], ["TextInlineMath", 5], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 79], ["Equation", 6], ["TextInlineMath", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2170, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 494], ["Line", 62], ["TextInlineMath", 4], ["Equation", 4], ["Text", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 34], ["Text", 7], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 26], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 14], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimal transport, old and new-pages-26"}